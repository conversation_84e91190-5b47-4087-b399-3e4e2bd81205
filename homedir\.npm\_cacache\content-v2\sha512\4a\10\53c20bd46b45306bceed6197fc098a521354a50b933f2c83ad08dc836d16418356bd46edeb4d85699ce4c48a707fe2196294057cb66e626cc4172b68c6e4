{"name": "lru.min", "dist-tags": {"latest": "1.1.2"}, "versions": {"0.1.0": {"name": "lru.min", "version": "0.1.0", "devDependencies": {"tsx": "^4.17.0", "poku": "^2.5.0", "esbuild": "^0.23.1", "prettier": "^3.3.3", "happy-dom": "^15.0.0", "typescript": "^5.5.4", "@types/node": "^22.5.0", "@biomejs/biome": "^1.8.3", "packages-update": "^2.0.0"}, "dist": {"shasum": "9f5aec6544349a22bd24172d546eacf0d5ac19d5", "tarball": "https://registry.npmjs.org/lru.min/-/lru.min-0.1.0.tgz", "fileCount": 7, "integrity": "sha512-mT46uWDX25swxbtduLmiyycUCto8u2qQLgZDDq2NR+N9pXzlgJ/ca2eKTc8hWKnAnlxNclgcAVNSc8TnrLA+2w==", "signatures": [{"sig": "MEUCIFuOL+VDXxl37a1hXacnT+CIziOOboaJspuyrDzDNe1hAiEA76ovXxZUdtolxsxTaO/9hugIX0nqttBtSzaVluXUD0M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23634}, "engines": {"bun": ">=1.0.0", "deno": ">=1.30.0", "node": ">=8.0.0"}, "funding": {"url": "https://github.com/sponsors/wellwelwel", "type": "github"}}, "0.2.0": {"name": "lru.min", "version": "0.2.0", "devDependencies": {"tsx": "^4.17.0", "poku": "^2.5.0", "esbuild": "^0.23.1", "prettier": "^3.3.3", "happy-dom": "^15.0.0", "typescript": "^5.5.4", "@types/node": "^22.5.0", "@biomejs/biome": "^1.8.3", "packages-update": "^2.0.0"}, "dist": {"shasum": "cd8013bbe31511cbcf1460b77ebd47f839e9ac44", "tarball": "https://registry.npmjs.org/lru.min/-/lru.min-0.2.0.tgz", "fileCount": 7, "integrity": "sha512-ORjTfjYfi98IUX1M8kU7Qd41GXpgPbyXjpY2l0rzQy6t4go2XGPZJYEXiKTxz4vEtPV/gw2oUxb5MD5VjYlILw==", "signatures": [{"sig": "MEUCIGW6EQlBxCPz5vB4sMAIWGhyRnpvLIvD4Qp3yIHNBakxAiEA1CF0cpGjJnrfi/iniJlpnuOAHyhkTVjuO4eTgBipILY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23489}, "engines": {"bun": ">=1.0.0", "deno": ">=1.30.0", "node": ">=8.0.0"}, "funding": {"url": "https://github.com/sponsors/wellwelwel", "type": "github"}}, "0.2.1": {"name": "lru.min", "version": "0.2.1", "devDependencies": {"tsx": "^4.17.0", "poku": "^2.5.0", "esbuild": "^0.23.1", "prettier": "^3.3.3", "happy-dom": "^15.0.0", "typescript": "^5.5.4", "@types/node": "^22.5.0", "@biomejs/biome": "^1.8.3", "packages-update": "^2.0.0"}, "dist": {"shasum": "f90ce37b4a3f1056e6f2973970e846cfd9d8a69e", "tarball": "https://registry.npmjs.org/lru.min/-/lru.min-0.2.1.tgz", "fileCount": 7, "integrity": "sha512-ayEIl3/1xz42PEpLTDy98DEmdwvAETUCPUdTC4gasTP65z4Sy81OE7PlNxHinto1ebmLnpm1BxyZhcW3a9/5RA==", "signatures": [{"sig": "MEYCIQDkUIMJFCp3HusfcVtWUiFoZvYubHkOIJLqR0GcRXUTXAIhANjktJYF7+RVjw6u6NYQsdY1iju85lB5E+Egdp6i/znF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23588}, "engines": {"bun": ">=1.0.0", "deno": ">=1.30.0", "node": ">=8.0.0"}, "funding": {"url": "https://github.com/sponsors/wellwelwel", "type": "github"}}, "0.2.2": {"name": "lru.min", "version": "0.2.2", "devDependencies": {"tsx": "^4.17.0", "poku": "^2.5.0", "esbuild": "^0.23.1", "prettier": "^3.3.3", "happy-dom": "^15.0.0", "typescript": "^5.5.4", "@types/node": "^22.5.0", "@biomejs/biome": "^1.8.3", "packages-update": "^2.0.0"}, "dist": {"shasum": "44c552dc9431b3a19ca8861098ec977259344f59", "tarball": "https://registry.npmjs.org/lru.min/-/lru.min-0.2.2.tgz", "fileCount": 7, "integrity": "sha512-ici1GOKmgoquR+8Von86IW9wSZvIlypb1Ry5zNFo4zp4YSJqXLjU4e/sLlrVDXkh82bvAPod272PRGvDlw9a7Q==", "signatures": [{"sig": "MEUCIB0EgNtlK9D6s7O5ihA56vgaKHgYmv9d8Yk4EJDnMvRZAiEAthSzCuxLnxUsxYcmKbOjdms5gtEXVj9caWrTQwhVQG0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23588}, "engines": {"bun": ">=1.0.0", "deno": ">=1.30.0", "node": ">=8.0.0"}, "funding": {"url": "https://github.com/sponsors/wellwelwel", "type": "github"}}, "0.2.3": {"name": "lru.min", "version": "0.2.3", "devDependencies": {"tsx": "^4.17.0", "poku": "^2.5.0", "esbuild": "^0.23.1", "prettier": "^3.3.3", "happy-dom": "^15.0.0", "typescript": "^5.5.4", "@types/node": "^22.5.0", "@biomejs/biome": "^1.8.3", "packages-update": "^2.0.0"}, "dist": {"shasum": "af14809357908ae189d662c2556f2685aa6271d1", "tarball": "https://registry.npmjs.org/lru.min/-/lru.min-0.2.3.tgz", "fileCount": 7, "integrity": "sha512-DPmeNBgjmeymlEnxeFnG+AZFYSyP2B/Akf409HWjanYgiuIDVCgoXuuo4O2v0g9PtfWdDO+G5sxNqVUzNL0rwg==", "signatures": [{"sig": "MEQCIDN9yFD9sceNzjWaI44NxHAl1JxHpM5trmMyZY4qilpIAiAqvMBRPxQjOVk71YvUo+dngPlE1k6QXWez1qABi6QzTw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23588}, "engines": {"bun": ">=1.0.0", "deno": ">=1.30.0", "node": ">=8.0.0"}, "funding": {"url": "https://github.com/sponsors/wellwelwel", "type": "github"}}, "0.3.0": {"name": "lru.min", "version": "0.3.0", "devDependencies": {"tsx": "^4.17.0", "poku": "^2.5.0", "esbuild": "^0.23.1", "prettier": "^3.3.3", "happy-dom": "^15.0.0", "typescript": "^5.5.4", "@types/node": "^22.5.0", "@biomejs/biome": "^1.8.3", "packages-update": "^2.0.0", "monocart-coverage-reports": "^2.10.3"}, "dist": {"shasum": "9244faf67b8b963de2e1e7f71c7d8ab35c3112a5", "tarball": "https://registry.npmjs.org/lru.min/-/lru.min-0.3.0.tgz", "fileCount": 7, "integrity": "sha512-PCIo5aSfpLy/dkLK0NHMf9brfgJ1FLa9IJ4iTbORaF6fFGzhnztc1Y34WSfzrsw/kkDevqAViGOVsBqpIAdyBw==", "signatures": [{"sig": "MEUCIQDG5KYatqxM8j3bINKKKfDAtspsRnW5EqxfCK1e+yDnGQIgN/3bWi5vCCXpFHyJr09f8NGw5jpx5CQxhcMMg80MDjU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28575}, "engines": {"bun": ">=1.0.0", "deno": ">=1.30.0", "node": ">=8.0.0"}, "funding": {"url": "https://github.com/sponsors/wellwelwel", "type": "github"}}, "0.3.1": {"name": "lru.min", "version": "0.3.1", "devDependencies": {"tsx": "^4.17.0", "poku": "^2.5.0", "esbuild": "^0.23.1", "prettier": "^3.3.3", "happy-dom": "^15.0.0", "typescript": "^5.5.4", "@types/node": "^22.5.0", "@biomejs/biome": "^1.8.3", "packages-update": "^2.0.0", "monocart-coverage-reports": "^2.10.3"}, "dist": {"shasum": "57fc34e717d969a4f2240c3a91def8c50830c8b8", "tarball": "https://registry.npmjs.org/lru.min/-/lru.min-0.3.1.tgz", "fileCount": 7, "integrity": "sha512-Q0f9iCCN2qjRVDTxZ80joyr5Tv9WUAWY8nLKXFC1VnNcBxY4GJMiEutqVt0K1nuxBuVNeXhcm3Zi/ZuCP29zug==", "signatures": [{"sig": "MEQCIHRNMWTlXgDKbtSD4M4rgPCuoGDQRLAVwXyTmt7XwVC5AiBGW7zt2uMmrE6oCiwedQnKWuWaYh8wFFlOmNXc5KR0FQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27866}, "engines": {"bun": ">=1.0.0", "deno": ">=1.30.0", "node": ">=8.0.0"}, "funding": {"url": "https://github.com/sponsors/wellwelwel", "type": "github"}}, "0.3.2": {"name": "lru.min", "version": "0.3.2", "devDependencies": {"tsx": "^4.19.0", "poku": "^2.5.0", "esbuild": "^0.23.1", "prettier": "^3.3.3", "happy-dom": "^15.0.0", "typescript": "^5.5.4", "@types/node": "^22.5.0", "@biomejs/biome": "^1.8.3", "packages-update": "^2.0.0", "monocart-coverage-reports": "^2.10.3"}, "dist": {"shasum": "ffcf3ee961d4cacb4505191ed9745eb93106eded", "tarball": "https://registry.npmjs.org/lru.min/-/lru.min-0.3.2.tgz", "fileCount": 7, "integrity": "sha512-u2fhvG85ThKP3yxnjoWY79FjJbSwepsDuCL5D9CgGEeHCZhZ2UY32wjYDaaXOEplC6Qow9C96ZxW/Mk7TFN8QQ==", "signatures": [{"sig": "MEUCIQC/o5lFMtm6GGSyNBeBHvulU+CXVu6FqtwVxwx1v/0qQgIgOZToIhNKDS4bBSmHWbx0CvoGnURO96SLYvM94jpzD6w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30054}, "engines": {"bun": ">=1.0.0", "deno": ">=1.30.0", "node": ">=8.0.0"}, "funding": {"url": "https://github.com/sponsors/wellwelwel", "type": "github"}}, "1.0.0": {"name": "lru.min", "version": "1.0.0", "devDependencies": {"tsx": "^4.19.0", "poku": "^2.5.0", "esbuild": "^0.23.1", "prettier": "^3.3.3", "happy-dom": "^15.0.0", "typescript": "^5.5.4", "@types/node": "^22.5.0", "@biomejs/biome": "^1.8.3", "packages-update": "^2.0.0", "monocart-coverage-reports": "^2.10.3"}, "dist": {"shasum": "b54b7f2143620d4415822098aada950aad781968", "tarball": "https://registry.npmjs.org/lru.min/-/lru.min-1.0.0.tgz", "fileCount": 7, "integrity": "sha512-YthLl3hdewA3lIwPrpgDLNlq6bvgbZjJQR4qr6oQ2c6lC78sCOwJkn0AkuUArbydQNQ+PjwIVz9IwZNrmLhXeg==", "signatures": [{"sig": "MEQCIGO+tgOvU+Ny1jmicCSzYAWRj3rCSfihI2BHQhhtQYVfAiBXoHTsVo8pYPgVdCYr5RPLK6RkEmxSH8sYEEnZaKz0xA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/lru.min@1.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 30058}, "engines": {"bun": ">=1.0.0", "deno": ">=1.30.0", "node": ">=8.0.0"}, "funding": {"url": "https://github.com/sponsors/wellwelwel", "type": "github"}}, "1.1.0": {"name": "lru.min", "version": "1.1.0", "devDependencies": {"tsx": "^4.19.0", "poku": "^2.5.0", "esbuild": "^0.23.1", "prettier": "^3.3.3", "typescript": "^5.5.4", "@types/node": "^22.5.0", "@biomejs/biome": "^1.8.3", "packages-update": "^2.0.0", "monocart-coverage-reports": "^2.10.3"}, "dist": {"shasum": "fd222364c440a389d2c4e5f637cc0521a114bfca", "tarball": "https://registry.npmjs.org/lru.min/-/lru.min-1.1.0.tgz", "fileCount": 7, "integrity": "sha512-86xXMB6DiuKrTqkE/lRL0drlNh568awttBPJ7D66fzDHpy6NC5r3N+Ly/lKCS2zjmeGyvFDx670z0cD0PVBwGA==", "signatures": [{"sig": "MEYCIQDbV/xt+OAhAar3TqcoF4wf7czZ6eBVxwrAMb+mCxlgvAIhAKYzVdMOQQFPJc0c6jSos1H+ulkvzsz7sU/JzYUTyx5h", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/lru.min@1.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 32625}, "engines": {"bun": ">=1.0.0", "deno": ">=1.30.0", "node": ">=8.0.0"}, "funding": {"url": "https://github.com/sponsors/wellwelwel", "type": "github"}}, "1.1.1": {"name": "lru.min", "version": "1.1.1", "devDependencies": {"tsx": "^4.19.1", "poku": "^2.7.0", "terser": "^5.33.0", "esbuild": "^0.23.1", "prettier": "^3.3.3", "typescript": "^5.6.2", "@babel/core": "^7.25.2", "@types/node": "^22.5.5", "@biomejs/biome": "^1.9.2", "packages-update": "^2.0.0", "@babel/preset-env": "^7.25.4", "@types/babel__core": "^7.20.5", "monocart-coverage-reports": "^2.10.9"}, "dist": {"shasum": "146e01e3a183fa7ba51049175de04667d5701f0e", "tarball": "https://registry.npmjs.org/lru.min/-/lru.min-1.1.1.tgz", "fileCount": 7, "integrity": "sha512-FbAj6lXil6t8z4z3j0E5mfRlPzxkySotzUHwRXjlpRh10vc6AI6WN62ehZj82VG7M20rqogJ0GLwar2Xa05a8Q==", "signatures": [{"sig": "MEUCICSoqvOsIdCIWeZFaa0RYQb779ky8foFHNhMEV4ylhxrAiEAv87MCM/XvNj5yOezPnURaWYOTBtSIINNQFYoGf2DrDo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/lru.min@1.1.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 33542}, "engines": {"bun": ">=1.0.0", "deno": ">=1.30.0", "node": ">=8.0.0"}, "funding": {"url": "https://github.com/sponsors/wellwelwel", "type": "github"}}, "1.1.2": {"name": "lru.min", "version": "1.1.2", "devDependencies": {"@babel/core": "^7.26.9", "@babel/preset-env": "^7.26.9", "@biomejs/biome": "^1.9.4", "@types/babel__core": "^7.20.5", "@types/node": "^22.13.10", "esbuild": "^0.25.0", "monocart-coverage-reports": "2.12.1", "packages-update": "^2.0.0", "poku": "^3.0.1", "prettier": "^3.5.3", "terser": "^5.39.0", "tsx": "^4.19.3", "typescript": "^5.8.2"}, "dist": {"integrity": "sha512-Nv9KddBcQSlQopmBHXSsZVY5xsdlZkdH/Iey0BlcBYggMd4two7cZnKOK9vmy3nY0O5RGH99z1PCeTpPqszUYg==", "shasum": "01ce1d72cc50c7faf8bd1f809ebf05d4331021eb", "tarball": "https://registry.npmjs.org/lru.min/-/lru.min-1.1.2.tgz", "fileCount": 7, "unpackedSize": 34457, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/lru.min@1.1.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCePY931wlKRBW/pkfBTgndIE0ddaLH4WCn03eZPaELuwIhANl06TdTxfgGLGI0pjFWKFfZo0hWanoxbcVqYHaPIxTp"}]}, "engines": {"node": ">=8.0.0", "bun": ">=1.0.0", "deno": ">=1.30.0"}, "funding": {"type": "github", "url": "https://github.com/sponsors/wellwelwel"}}}, "modified": "2025-03-09T18:29:37.870Z"}