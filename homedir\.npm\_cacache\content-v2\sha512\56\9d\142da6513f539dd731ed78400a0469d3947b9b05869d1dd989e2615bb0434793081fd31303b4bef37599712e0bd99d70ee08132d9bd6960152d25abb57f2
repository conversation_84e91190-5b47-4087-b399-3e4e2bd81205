{"name": "uuid", "dist-tags": {"latest": "11.1.0"}, "versions": {"0.0.1": {"name": "uuid", "version": "0.0.1", "dist": {"shasum": "5b43a6840d25914b5a76a0664d71a51601ddec79", "tarball": "https://registry.npmjs.org/uuid/-/uuid-0.0.1.tgz", "integrity": "sha512-x3aIUBw/J5WMm+mfHLh5b7OelhczIY5/wr/b6JapW/SYdU4Yy7mW8AQ6vxecnRjy/qqe14mLV5vdA3c+4QCO/w==", "signatures": [{"sig": "MEUCIQCBH/OYMwdf/bhNLOnu7RN7FWR+xiRXSnjYCb9i79w44wIgPKNS2UlGKszZ04Kg4BfI1oLcscXAaz0j9MJNxflgWLw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}, "deprecated": "Please upgrade to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.", "hasInstallScript": true}, "0.0.2": {"name": "uuid", "version": "0.0.2", "dist": {"bin": {"0.4-sunos-5.11": {"shasum": "2ff8d977261ddadfd1446cee661ab87863659e45", "tarball": "http://registry.npmjs.org/uuid/-/uuid-0.0.2-0.4-sunos-5.11.tgz"}}, "shasum": "3171f2c4f58895b8b307692a335fb2349ddf6733", "tarball": "https://registry.npmjs.org/uuid/-/uuid-0.0.2.tgz", "integrity": "sha512-3h/4V/B5W+7FmanZTk1bQMDDoNstFk/2xy0W2W1s1WX8NPU2Sgrfi3GXZQvhqVZZiQAA7A7uUgOB4xzy0ngraA==", "signatures": [{"sig": "MEYCIQD1IQ4S7SggRuiiTxjJVzzfkHVzcPpPso/A+N++TgXqAgIhAP+LCzmHoT6q26ofm8BifbpLqynMNyNtpYHPSz7covx/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}, "deprecated": "Please upgrade to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.", "hasInstallScript": true}, "1.4.0": {"name": "uuid", "version": "1.4.0", "dist": {"shasum": "d0d3b84ab56902e99ff952f2a17aa3986d44d36f", "tarball": "https://registry.npmjs.org/uuid/-/uuid-1.4.0.tgz", "integrity": "sha512-IzR48RgxTHa2bbD4KtzkfO11HrwBBpN536a3D1NRBBNKMCMbHjGHQRzhCuS1cMMptTagWTLIMVYCG5SP0UyEfg==", "signatures": [{"sig": "MEYCIQDphxshQcuL4XhLpEmBDKZcZvcgMOG7E9UXZTmirKi7bQIhAIiXlZ2xCnZhpsMmbD4dc3SgKm4zB5Wt09SUSUkO7jcF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Please upgrade to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details."}, "1.4.1": {"name": "uuid", "version": "1.4.1", "devDependencies": {"mocha": "1.8.0"}, "dist": {"shasum": "a337828580d426e375b8ee11bd2bf901a596e0b8", "tarball": "https://registry.npmjs.org/uuid/-/uuid-1.4.1.tgz", "integrity": "sha512-VvxWRJy+jqowMX1wClasj2BIORh82/X3wkRNNpXDOh1tUxmVAbdEWRUM+yRVg30a+XBmf+duDVtMgvRiuGq0qw==", "signatures": [{"sig": "MEYCIQDGPgY/8s7EsZsplI7wUj9zkdvyAiFBNx8EMgYI37OuuQIhAPjpphlukLKOEBmORh7gaKymp9hSVj6dxl46neHEGac2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Please upgrade to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details."}, "1.4.2": {"name": "uuid", "version": "1.4.2", "devDependencies": {"mocha": "1.8.0"}, "dist": {"shasum": "453019f686966a6df83cdc5244e7c990ecc332fc", "tarball": "https://registry.npmjs.org/uuid/-/uuid-1.4.2.tgz", "integrity": "sha512-woV5Ei+GBJyrqMXt0mJ9p8/I+47LYKp/4urH76FNTMjl22EhLPz1tNrQufTsrFf/PYV/7ctSZYAK7fKPWQKg+Q==", "signatures": [{"sig": "MEQCIEzUTJA7B8PA2IbGadktJSU+HmCPu279aLDzPDr+5ZifAiArxPr9DAfvq+4Y+cA1zjnGMb2FD/j6UtrMiVyYubb5AA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Please upgrade to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details."}, "2.0.0": {"name": "uuid", "version": "2.0.0", "devDependencies": {"mocha": "1.8.0"}, "dist": {"shasum": "377ab4417736dba5ce379ff0a0c1a539921ebb74", "tarball": "https://registry.npmjs.org/uuid/-/uuid-2.0.0.tgz", "integrity": "sha512-MgCjmgHKiEVlRQ24qLbInOkKOrg1g8VhoXlzFHWY5dXfT/HLfcUomFyoQPIpp7YZ3ymtteUJBYhcYISFmmnsHw==", "signatures": [{"sig": "MEYCIQCd80jdLlS0JaoA9GQYpc/J2PNQ1x2BfrhCSmDTv8DkvwIhAPBr4w2SNVwKjeFrw3TYtCYSIlCji/5RlMwSgXDgROLy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details."}, "2.0.1": {"name": "uuid", "version": "2.0.1", "devDependencies": {"mocha": "1.8.0"}, "dist": {"shasum": "c2a30dedb3e535d72ccf82e343941a50ba8533ac", "tarball": "https://registry.npmjs.org/uuid/-/uuid-2.0.1.tgz", "integrity": "sha512-nWg9+Oa3qD2CQzHIP4qKUqwNfzKn8P0LtFhotaCTFchsV7ZfDhAybeip/HZVeMIpZi9JgY1E3nUlwaCmZT1sEg==", "signatures": [{"sig": "MEYCIQC+RbHqOIrw5jch5eIF2wZ6dJn7iwEtFB213kSmjR2nnQIhANNVG6MBAl7ce2sImkZvEahj1M4vS1K1iZr6lT69DfZI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details."}, "2.0.2": {"name": "uuid", "version": "2.0.2", "devDependencies": {"mocha": "1.8.0"}, "dist": {"shasum": "48bd5698f0677e3c7901a1c46ef15b1643794726", "tarball": "https://registry.npmjs.org/uuid/-/uuid-2.0.2.tgz", "integrity": "sha512-BooSif/UQWXwaQme+4z32duvmtUUz/nlHsyGrrSCgsGf6snMrp9q/n1nGHwQzU12kaCeceODmAiRZA8TCK06jA==", "signatures": [{"sig": "MEQCIHzzSD5cFI/bZ7LZWR2x6Y5ob8g4kek37JC/3t6MtnnYAiAHmHCbiiqkks6nXFZZBb0KnM2dpNeFhTgPwWgOuzIAjA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details."}, "2.0.3": {"name": "uuid", "version": "2.0.3", "devDependencies": {"mocha": "1.8.0"}, "dist": {"shasum": "67e2e863797215530dff318e5bf9dcebfd47b21a", "tarball": "https://registry.npmjs.org/uuid/-/uuid-2.0.3.tgz", "integrity": "sha512-FULf7fayPdpASncVy4DLh3xydlXEJJpvIELjYjNeQWYUZ9pclcpvCZSr2gkmN2FrrGcI7G/cJsIEwk5/8vfXpg==", "signatures": [{"sig": "MEUCIQCL2ujhY5VO83wMf65KONzxZ9Gv0SEYCLX49+BD3e6cjwIgKF3ja8+IHLoxkTF88HrygIa+MdIvfyDuQZJzUY86aZQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details."}, "3.0.0": {"name": "uuid", "version": "3.0.0", "devDependencies": {"mocha": "3.1.2"}, "bin": {"uuid": "./bin/uuid"}, "dist": {"shasum": "6728fc0459c450d796a99c31837569bdf672d728", "tarball": "https://registry.npmjs.org/uuid/-/uuid-3.0.0.tgz", "integrity": "sha512-rqE1LoOVLv3QrZMjb4NkF5UWlkurCfPyItVnFPNKDDGkHw4dQUdE4zMcLqx28+0Kcf3+bnUk4PisaiRJT4aiaQ==", "signatures": [{"sig": "MEQCIECZiYO/0a0dU1onAlLliDR6TCXZyhvPTBWIoiopzKzcAiBMa9RVCF7K3J7goNPNcElB2keP5F7UZAmvyUqATmaX2Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details."}, "3.0.1": {"name": "uuid", "version": "3.0.1", "devDependencies": {"mocha": "3.1.2"}, "bin": {"uuid": "./bin/uuid"}, "dist": {"shasum": "6544bba2dfda8c1cf17e629a3a305e2bb1fee6c1", "tarball": "https://registry.npmjs.org/uuid/-/uuid-3.0.1.tgz", "integrity": "sha512-tyhM7iisckwwmyHVFcjTzISz/R1ss/bRudNgHFYsgeu7j4JbhRvjE+Hbcpr9y5xh+b+HxeFjuToDT4i9kQNrtA==", "signatures": [{"sig": "MEYCIQCW7k8Jo30nhFQOdhmvhcANJL+gxbfSahTliBAKeBtkWgIhAKsDlHuPurAxguGBTu1Pg8y2bObl7VKJedHHWamXI//4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details."}, "3.1.0": {"name": "uuid", "version": "3.1.0", "devDependencies": {"mocha": "3.1.2"}, "bin": {"uuid": "./bin/uuid"}, "dist": {"shasum": "3dd3d3e790abc24d7b0d3a034ffababe28ebbc04", "tarball": "https://registry.npmjs.org/uuid/-/uuid-3.1.0.tgz", "integrity": "sha512-DIWtzUkw04M4k3bf1IcpS2tngXEL26YUD2M0tMDUpnUrz2hgzUBlD55a4FjdLGPvfHxS6uluGWvaVEqgBcVa+g==", "signatures": [{"sig": "MEUCIQDHvTmx2CLchJzwlxayiHWEIH3U6odSPQZoZR1/I3HIsQIgJwtjBLGzn0VdcD2NQyYT8SuDkxU+5XOTF/r95Vy6fEc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details."}, "3.2.0": {"name": "uuid", "version": "3.2.0", "devDependencies": {"mocha": "3.1.2", "runmd": "1.0.1", "eslint": "4.5.0", "standard-version": "4.2.0"}, "bin": {"uuid": "./bin/uuid"}, "dist": {"shasum": "19a63e22b3b32a0ba23984a4f384836465e24949", "tarball": "https://registry.npmjs.org/uuid/-/uuid-3.2.0.tgz", "integrity": "sha512-qC0vMFX6q6ee8JaoTF2Om1uL8KAV1ATUjVaHRxLiPJkIsp8JZl6ZjG0MIB+twZFLbi1vXj30rqj4zlqYiOS9xg==", "signatures": [{"sig": "MEUCIQCIjmQ5Rnw+x+5I5xRFDUrpAN7G9KZFwaC+pMY3QWgoZgIgBJpZgttKkDKQ9KIagxs7x8di73iKVzacBIHEcJiScLw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details."}, "3.2.1": {"name": "uuid", "version": "3.2.1", "devDependencies": {"mocha": "3.1.2", "runmd": "1.0.1", "eslint": "4.5.0", "standard-version": "4.2.0"}, "bin": {"uuid": "./bin/uuid"}, "dist": {"shasum": "12c528bb9d58d0b9265d9a2f6f0fe8be17ff1f14", "tarball": "https://registry.npmjs.org/uuid/-/uuid-3.2.1.tgz", "integrity": "sha512-jZnMwlb9Iku/O3smGWvZhauCf6cvvpKi4BKRiliS3cxnI+Gz9j5MEpTz2UFuXiKPJocb7gnsLHwiS05ige5BEA==", "signatures": [{"sig": "MEUCIQClBCsV3pD6yvLgviye5VlbJj5SXjRREEIhYsBxnwyRDAIgXalnZy9mRWZ++bns2tds6LMNA1UK9hicf8ML6os4q7k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details."}, "3.3.0": {"name": "uuid", "version": "3.3.0", "devDependencies": {"husky": "0.14.3", "mocha": "5.2.0", "runmd": "1.0.1", "eslint": "4.19.1", "@commitlint/cli": "7.0.0", "standard-version": "4.4.0", "@commitlint/config-conventional": "7.0.1"}, "bin": {"uuid": "./bin/uuid"}, "dist": {"shasum": "b237147804881d7b86f40a7ff8f590f15c37de32", "tarball": "https://registry.npmjs.org/uuid/-/uuid-3.3.0.tgz", "fileCount": 21, "integrity": "sha512-ijO9N2xY/YaOqQ5yz5c4sy2ZjWmA6AR6zASb/gdpeKZ8+948CxwfMW9RrKVk5may6ev8c0/Xguu32e2Llelpqw==", "signatures": [{"sig": "MEUCIDs6gd+UESKBONyc0AZr2W/e7smAg2PJSdGS77pEClqkAiEAynrxQAuB2ALbGrEHk2ErCK0tjTOvLCiDfnmqpW6VbTc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44057, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbMj5bCRA9TVsSAnZWagAAoowP/05xAG0lM80CctZLOyEd\nkUJj8ihe+AkwLCMa+7HgJpbc/NiNhJtjN0I9peEEXoGMrg+IZOehcUabm3pN\nUz29450CXc1mEeEWjTBHfE0fZOWGVSHrbVhKuqZByS/Gq4W9hHOMQcRbeDOM\nHUg2Cg1BD9YEJgCdWbMAZHkWmE36FJ7UaaRcZNn1qhzj5KlNqft8tRAxt0n5\nsQ099IitF+d8rMYxDHS23GCAEHDcwM0rolyQJio3mMmFi5GDdYoDybNCR/Jx\nyETA3f3PVdybkfPuCwufwfFH9YDBPHo0tCikpi9Sz4sKXxdDo7lxXToP5+ai\nujTl5AB+oEKtbjLIugrISDfaHRcRGV/z0lnoXJOl+i/GM9FCkoxwA6r+HnOX\ner8HwUFSDrd0YV37EJMKLPgWLqdJTpZHCZjuU5b9RhbAujp5gGpUO1iITgJC\nJ/WcMkTaaGcHteJuSV39UV4tG0B3fHaC9/eHoF/jrsoJGRfZhbKECMXcTTLM\n6+FNvIA3PGE+onKyPutX2ROCciL4bxS6Rih6cU4pbOUV5SmpbPFkW6XX3UY5\n53K64Qjuv1wNCNS2WzYBnU/HGqwgcg/Pefdi7A+n6Z87/2fxfEqQf24xjOk4\naa/VCC/3mCnVzTMeSa1ckCQSoQNh/f05wtZ7qf90PGx4NFwzT5CgNDnhovER\nyl2Z\r\n=lu2+\r\n-----END PGP SIGNATURE-----\r\n"}, "deprecated": "Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details."}, "3.3.2": {"name": "uuid", "version": "3.3.2", "devDependencies": {"husky": "0.14.3", "mocha": "5.2.0", "runmd": "1.0.1", "eslint": "4.19.1", "@commitlint/cli": "7.0.0", "standard-version": "4.4.0", "@commitlint/config-conventional": "7.0.1"}, "bin": {"uuid": "./bin/uuid"}, "dist": {"shasum": "1b4af4955eb3077c501c23872fc6513811587131", "tarball": "https://registry.npmjs.org/uuid/-/uuid-3.3.2.tgz", "fileCount": 21, "integrity": "sha512-yXJmeNaw3DnnKAOKJE51sL/ZaYfWJRl1pK9dr19YFCu0ObS231AB1/LbqTKRAQ5kw8A90rA6fr4riOUpTZvQZA==", "signatures": [{"sig": "MEUCIGzp+lfrn4ozcY3DNpdZOR57g9DG17uNy5jtH629WIiqAiEAoJMJz6bhcioSGQjR5D6LZypvYtTfpEIFlNZL6x8/goM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43599, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbNVMdCRA9TVsSAnZWagAAHMAP/3dazKIzx5o8pB3uqIOa\nUj3OOWemMXXKN6m/ncbq23zi69Vi0WRpYwC8vbRiD+NwXdnV5yP5fesAoGjw\ngttCujsby2Q96xqGDmnpxF//+OZC2/fPt/EBVtvlZM/KSpevRoDBF03ihcFL\nGyAFS7i0FvRJskP2xp+72Z+6C6A24OvW7BB+WGJcTh8z7QbStTPDQmjam1jM\n9lkez4SbOpFd4Hpca1yFM8HRb6UGUFo+UdIoylbaeKnUDBzCmtv1LwtkL77I\noMB8AyHna8iUjv43l08EODev+G08dBQcrL2K83WMp+ztAHwi8KOryxTLKgUV\nvZwaObtCH77QSnraFF5Vq1onGw+FTckQlP0zS37e5IKSRv0GV/t3E1aSkemt\ndDU2qURkFLyVuGSV8utAMWvwTDA6CEsXeEs49ZEYtZWKeEKc1dXxx0ovVMBV\nxGFgmTnP+pYXUCnqrXlJfkfEtBvGhU6N4KmfBz8tAWjfafYFZng9pQxF04vh\nMgHOy7upIMtDKZCyCGW+C81L24cdggmOVGfLHIpMLbMpR05cS1gXqHGe1OBy\nY5SP34YgMjhRnyfvb1LlcDJEjJvruzx46UP/D9JDBmjhLTJSDxwQuN+UrjPx\n9WhQEv/P3CvSlHVpfd+NF6b0vwM7IoiWbxMwF3z9jZpNU7o+3pySZrni3aMC\nHEgb\r\n=qIAF\r\n-----END PGP SIGNATURE-----\r\n"}, "deprecated": "Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details."}, "3.3.3": {"name": "uuid", "version": "3.3.3", "devDependencies": {"husky": "3.0.4", "mocha": "6.2.0", "runmd": "1.2.1", "eslint": "6.2.0", "@commitlint/cli": "8.1.0", "standard-version": "7.0.0", "@commitlint/config-conventional": "8.1.0"}, "bin": {"uuid": "./bin/uuid"}, "dist": {"shasum": "4568f0216e78760ee1dbf3a4d2cf53e224112866", "tarball": "https://registry.npmjs.org/uuid/-/uuid-3.3.3.tgz", "fileCount": 19, "integrity": "sha512-pW0No1RGHgzlpHJO1nsVrHKpOEIxkGg1xB+v0ZmdNH5OAeAwzAVrCnI2/6Mtx+Uys6iaylxa+D3g4j63IKKjSQ==", "signatures": [{"sig": "MEUCIQCdiKgMKeIQZbxEHTpmOAI6yGO4NhMPjPfJDOmeOKZw9QIgRmH0bjXJIwsj2pvSKGCaSvUVviVMgMKxvMDgZ9u/XNg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34683, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdWqOBCRA9TVsSAnZWagAA1b0P/30Jwx5qz0EAfdMRewwZ\nNaClKkF9nznrtjK84p4vrf+/7nlXfclQtZO65pcVSgXYQsqUTadATY/nHIdV\nC/TYyrgk0fjS5TkLzMs8268S5iO+ruCAyCXA27JqVyfFGxlPZRTneukR71R8\n7Htg3MnZMM5REIM7xckj1maZpi+SNEfvbNNY9Je4bKnAWNX1ezznicfpEaT7\nXQkoOakMKked+CGJhTEXdwKBn9rBKjNr74DMgXiIlEnL6pn7vhNu10zTY1cp\njbsdkOptkWsQBUMzDfkjpBJc+xnji80SSekfZ0iLt5Qak4fYzUcnbZM2XCaC\nvJEz2cLYlbhJox3K92/ikwsBZTGaXtwB96DQbJJ8zDvx7DtTq53tSF1qTgTf\ni+Nm8HDRGbGMkTseJm0sm83NYPQY4RQbiR4bcJNlQQ9ZLWCJSdfudaHkKOU9\nPyFW0aP1LT3qzitpAvHDV3B1fnQEvBJplmVOdm5F0qnTXEmgekjfQMGc5GMG\nDt42B9Vjxs2oVQszMKuG1K+ouXk4JcIQMXYYZA9JfpEmAocHfgam0Y03MZ2J\nb5+7YmeqIZu7n3PwVKTdsLQLPMmibtg6lCC+Akfq1ytuDWVPREIS7JM5Bts7\ngTiRG8rSPeNpLetCN25Ke02xbZDJof3dTJZxWu2vSTeczrh2BFqLjshy/5r4\ny3+j\r\n=wnZc\r\n-----END PGP SIGNATURE-----\r\n"}, "deprecated": "Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details."}, "3.4.0": {"name": "uuid", "version": "3.4.0", "devDependencies": {"husky": "~3.0.5", "mocha": "6.2.0", "runmd": "1.2.1", "eslint": "~6.4.0", "@commitlint/cli": "~8.2.0", "standard-version": "7.0.0", "@commitlint/config-conventional": "~8.2.0"}, "bin": {"uuid": "./bin/uuid"}, "dist": {"shasum": "b23e4358afa8a202fe7a100af1f5f883f02007ee", "tarball": "https://registry.npmjs.org/uuid/-/uuid-3.4.0.tgz", "fileCount": 19, "integrity": "sha512-HjSDRw6gZE5JMggctHBcjVak08+KEVhSIiDzFnT9S9aegmp85S/bReBVTb4QTFaRNptJ9kuYaNhnbNEOkbKb/A==", "signatures": [{"sig": "MEUCIB3lNgQ7KQZkVcdWdJzevvb8bQQfG/LcAeGEowNFZwntAiEA8VSKkOxvxVbnJSL57iyyAamxsH6Noy4+xlyyUnZvsRk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34269, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeINAiCRA9TVsSAnZWagAAOHsP/RVFM7OJamQHALP58acz\nQHrh9S8dhiix0+OGPfzJjPDnB/ver8y3weYkOtTXVU6qempJOgVZnigeR2yZ\nH00hUco5UxkK0j1eg1afyGoeso4jjNBtpVkyPnCb2Cg0A8l0g8I6zzb3LByf\nwv74IPlhjXlYyZsIlYwan5q4w5nRsf6+xdV4qdYXquxf05hCYgt3pWV4tSe6\nE2Y+WWZSVpYBpLyY1p7IJBk6xAYmwF1M8D+G0weLJCoOMa41fvhRsd2E84iW\nJYUDCKn+NfMoN4RaphGSBcpQD3POgE4UaI8D/QpFYUpF2bCQDFVakfqYsmtv\nsdCILUlRdhWmOJuoDc5GNk6iD83rtaqu+nwdNsBUal6iF+/8L1E8aCJ0VleI\nr5gLMm/kSl14lFue3iPkUIadoDu4rclpuPs2tiOIxH0Dnt9FHMw69uUr50O1\noCTLM2da2bZ9Vm9UxyIWEdx1TOFtiL4pWfcoQUQcRMDQ3wyoSmv/3ryfd+zl\n3HGwOM5w5zqOOs0JUizf90CRWvL48VUnaeKxL6xcS3OqbJmq7bDmfM2bAdJE\nI0g73oAf9/Ouch1l9Bt1mcZACtUeBYPSvlvN1u0jLzTqPP2VxBWboqZl291P\npR2Nqk3JDmcvY4HYl1oZ7wITiiiaARlTfNY4z2U5v8/f+QAtgwO/SPfHMXJL\nECdn\r\n=rDnh\r\n-----END PGP SIGNATURE-----\r\n"}, "deprecated": "Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details."}, "7.0.0-beta.0": {"name": "uuid", "version": "7.0.0-beta.0", "devDependencies": {"esm": "3.2.25", "jest": "24.9.0", "husky": "3.0.9", "runmd": "1.3.2", "eslint": "6.8.0", "rollup": "1.30.0", "prettier": "1.19.1", "@babel/cli": "7.8.3", "@babel/core": "7.8.3", "bundlewatch": "0.2.5", "http-server": "0.12.1", "lint-staged": "10.0.1", "npm-run-all": "4.1.5", "babel-eslint": "10.0.3", "@commitlint/cli": "8.3.5", "standard-version": "7.0.1", "@babel/preset-env": "7.8.3", "browserstack-local": "1.4.4", "selenium-webdriver": "3.6.0", "rollup-plugin-terser": "5.2.0", "eslint-config-prettier": "6.9.0", "eslint-plugin-prettier": "3.1.2", "@commitlint/config-conventional": "8.3.4", "babel-plugin-add-module-exports": "1.0.2"}, "bin": {"uuid": "dist/bin/uuid"}, "dist": {"shasum": "7f2bd91cf896f8f3d8d911d995edd7d66d4c2651", "tarball": "https://registry.npmjs.org/uuid/-/uuid-7.0.0-beta.0.tgz", "fileCount": 36, "integrity": "sha512-Am22LVM3UXB0DTzQAeDSsZwP5eyqjIhmff330hqkxGvIxX8RRrUYLtKJ0eYxiBgjeQdUaMONpBZbJachMShxBw==", "signatures": [{"sig": "MEYCIQDEK8JeminS5kvK+FXpwP9qLnE/Pij9kySPBNdZsTysIAIhAOlWZ9zLNvwc4qgCDpk5FRxUnkQgtz5Q1MuincsNk5Nv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68693, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeSngmCRA9TVsSAnZWagAAbkwP/RXJ5KOgqWONL6GrOdrx\nRRzSvg0VlcF8bj18EAg4ZEzWjgyCtLjAKfnjlQkfj1twaxlrrgbHBiYhEzME\nPr/8tUXn6/Hix13r7Zrkw+kJdISFz2acM6u0sgGJZ+ooxgXtwP/Xs2BVVk4k\n5u5cEN9GpyPxUvrDDgE8BPTDoQ1MxTU+4DWRcb57VIki26O44vvNYZGZJteR\nD/+P/oqHWNZ0hy01Ed5yXB3V/u92E3yc7FPKgGTLmmJjAdCRNifBjcEoQT51\n3/RlPkXB/yTza6fwaiVl+DA7FypppthNbo90drJqboS5iE4B/6ZwApSRrnKf\nWnKQDFWl/XWpNjtga+TC5VjN/KQbptheM3Xus0m9KNvmHUna/5svGJBkptqV\n3TSruCViqBaML1yxD1wTiArGacVakAbZRKSbz9XunOMIf8Qq6/unnBIRQDEi\njXbi+tHmGlJ9GzGn/Z4LXf7zwPPZV796LxIfq+TUsEQVrszJ1SRTsUkZ8oMg\nWOz4DwsI6DDU3KDBNiq+FqSfULRF/TwfrcybkfZDNyfXWbFPgk6bF583fN5e\n9l2TNs30eBsoCJiaLkCGnf4oHhUSMJhzTQoV8YyU/zwq+n04jNmP9S2PnkaW\nHZ+NAiAxMdi/y/3hEiaZAaEAQYhu3VVvjjeu6WU18OApWmF6E6idnLyb+A5V\nfXdA\r\n=UYmC\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.0": {"name": "uuid", "version": "7.0.0", "devDependencies": {"esm": "3.2.25", "jest": "25.1.0", "husky": "3.0.9", "runmd": "1.3.2", "eslint": "6.8.0", "rollup": "1.31.1", "prettier": "1.19.1", "@babel/cli": "7.8.4", "@babel/core": "7.8.4", "bundlewatch": "0.2.5", "http-server": "0.12.1", "lint-staged": "10.0.7", "npm-run-all": "4.1.5", "babel-eslint": "10.0.3", "@commitlint/cli": "8.3.5", "standard-version": "7.1.0", "@babel/preset-env": "7.8.4", "browserstack-local": "1.4.5", "selenium-webdriver": "3.6.0", "rollup-plugin-terser": "5.2.0", "eslint-config-prettier": "6.10.0", "eslint-plugin-prettier": "3.1.2", "@commitlint/config-conventional": "8.3.4", "babel-plugin-add-module-exports": "1.0.2"}, "bin": {"uuid": "dist/bin/uuid"}, "dist": {"shasum": "1833d4b9ce50b732bfaa271f9cb74e974d303c79", "tarball": "https://registry.npmjs.org/uuid/-/uuid-7.0.0.tgz", "fileCount": 36, "integrity": "sha512-LNUrNsXdI/fUsypJbWM8Jt4DgQdFAZh41p9C7WE9Cn+CULOEkoG2lgQyH68v3wnIy5K3fN4jdSt270K6IFA3MQ==", "signatures": [{"sig": "MEUCIQCpLKr2RXUFiUE2tGO+xCOQWt+BWDfRDk8e69A2rICJfAIgPBT8TE/hb/tbpmVPNUYCjZKdkXvC5rsRb5m9MsqLWsw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72408, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeU85jCRA9TVsSAnZWagAAQQkP/AlIM9UlpC8m86bmakLZ\nMxFzqPOYzXwQwpm2CkjwlYzdUWxqyvPiaobBeVhP3ieMW3aD2f19+3dAwpiw\ne9G9DZrQfGMFyzXFI/wRPBspzSWe6lP+n3J6IJoienzeXkV/3oeL0IBcAvr0\n4U7g7WW+C3deUyik8uO59fB3ITRAai/BX151MNeBmp0WB8S64FMTgUstesaq\ndlsKAcFNnN075DJEzTiIVm72h7bK2A5DTrkriQspVtYaobe0NA6zLovMLjFW\nJP84qZTPAcVfxRzd/5qVpUbHKyR5NLCraBWzDw/K64nd1HS05WAWKbSNebEM\nqPrs3VV6LBVtbXRqmT+7vedFr27g8f046NCMUQb0d0InxIt9/YXp0zaa79il\nBRnYvHaNmYXy4k0u4uxSsfxfLLZ7imtLIQ9nhkqVNlWYp4LjUlZLw1cBHsZx\npZ1HQ8G8TtnXEU/6GMtCZ6hnwCtq+4Bm4s4dgxzpGexOJIpYTK4+0rtmOEgR\ntY0aR5K27N7Lm2Zo88vZUqjbcQmuKEFhX5onVuPZBGg8qTXqOsasyi6T9jJf\n3YovQYwl/TdqRv27AUWS4ZhryG6byurIOKETENv/DC6ybYa1Lvy9t77Qor6a\nTwR3+IJ3S9RP0jtC59gMoUpH078XWBEN+4gkX8sSDEewZXv3Y6u2lR7hUqEy\nQKnT\r\n=SAzF\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.1": {"name": "uuid", "version": "7.0.1", "devDependencies": {"esm": "3.2.25", "jest": "25.1.0", "husky": "3.0.9", "runmd": "1.3.2", "eslint": "6.8.0", "rollup": "1.31.1", "prettier": "1.19.1", "@babel/cli": "7.8.4", "@babel/core": "7.8.4", "bundlewatch": "0.2.5", "http-server": "0.12.1", "lint-staged": "10.0.7", "npm-run-all": "4.1.5", "babel-eslint": "10.0.3", "@commitlint/cli": "8.3.5", "standard-version": "7.1.0", "@babel/preset-env": "7.8.4", "browserstack-local": "1.4.5", "selenium-webdriver": "3.6.0", "rollup-plugin-terser": "5.2.0", "eslint-config-prettier": "6.10.0", "eslint-plugin-prettier": "3.1.2", "@rollup/plugin-node-resolve": "7.1.1", "@commitlint/config-conventional": "8.3.4", "babel-plugin-add-module-exports": "1.0.2"}, "bin": {"uuid": "dist/bin/uuid"}, "dist": {"shasum": "95ed6ff3d8c881cbf85f0f05cc3915ef994818ef", "tarball": "https://registry.npmjs.org/uuid/-/uuid-7.0.1.tgz", "fileCount": 49, "integrity": "sha512-yqjRXZzSJm9Dbl84H2VDHpM3zMjzSJQ+hn6C4zqd5ilW+7P4ZmLEEqwho9LjP+tGuZlF4xrHQXT0h9QZUS/pWA==", "signatures": [{"sig": "MEUCIEt0azVUyp5SIuYfZDPQ69YE32hdEnpvoupa1n+dKMbgAiEA0vGngmyvYdlQqf23euVhMr0OGQEpjZ6y64fICveJNK4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 91125, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeVYL8CRA9TVsSAnZWagAAR8UP/iKsHTGRssBfDvB2I+MD\nvActHY6aC3wf+7nxLIyfA8OVfZCt8Q/bUB+FLKgqPPkp/MKFVkvEE3aidWX+\nD9HnZm3J2/b/+iH7l+3u2J/L+Bb369p7Q6Aje0EJksO6MEA/S/1egvLxMEd/\nYi3Q2/93eZOCt5ogroCUQ9vtKxLSJRwvoHICJN04DCw96OYe+GUTB6VOQFld\n5kx+ctksSaZmXYtmmWyHyHe8z2T2GoAdWZSg4LsB7nHGVcmio8rKqJmbdesU\n0esYjkGHhZszrB4/pgNSDl4PmMIpgTtfY5moE0tvaKlgVsUBQ4G1K1bo4LzW\nAtGBwgqtf2ystzXw9AU1SXS4l3vuhdRypHJCqvEZxnLliQeCaeMtNDoGBLed\n2aZe14zcTnyem5RgOgKax5cWJWj9o/zIl2aiAG8B2pPt+bml0Z/e+dok6MRp\nMqiqqa0SqeIrVicahyWzJ+6LSssaaqhLPc171g9lv7M3ddod4910E1XxfVLU\nxCDWYn/mgoBjs/0UqcE3RIkPdOJnLGbmvT2sr/grDRBVl9aY6lL84S8h8aZx\nLdmWlpRxPjt7GWCSIg8oZgVw4B5p+qp0Th2JYSK0xYFkvj4mOi39H7tN2UCA\nAukUAz4q83b4cyJIOjVP8nR4GXt1ae+MUdNSpuTJns4n/aH/fE20SU8svkxu\nrsp9\r\n=Hfuo\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.2-beta.0": {"name": "uuid", "version": "7.0.2-beta.0", "devDependencies": {"esm": "3.2.25", "jest": "25.1.0", "husky": "3.0.9", "runmd": "1.3.2", "eslint": "6.8.0", "rollup": "1.31.1", "prettier": "1.19.1", "@babel/cli": "7.8.4", "@babel/core": "7.8.4", "bundlewatch": "0.2.5", "http-server": "0.12.1", "lint-staged": "10.0.7", "npm-run-all": "4.1.5", "babel-eslint": "10.0.3", "@commitlint/cli": "8.3.5", "standard-version": "7.1.0", "@babel/preset-env": "7.8.4", "browserstack-local": "1.4.5", "selenium-webdriver": "3.6.0", "rollup-plugin-terser": "5.2.0", "eslint-config-prettier": "6.10.0", "eslint-plugin-prettier": "3.1.2", "@rollup/plugin-node-resolve": "7.1.1", "@commitlint/config-conventional": "8.3.4", "babel-plugin-add-module-exports": "1.0.2"}, "bin": {"uuid": "dist/bin/uuid"}, "dist": {"shasum": "8cc5d456c79a9dcb527108a799639cbf2e764bab", "tarball": "https://registry.npmjs.org/uuid/-/uuid-7.0.2-beta.0.tgz", "fileCount": 49, "integrity": "sha512-nWN1O0baX9+xv8DxMl6O5ERk0R/4xAuCkosXBNB68m1PReciNwzBoO9xzf8wX4HrXpfbcu9Mpo5mmgLcsDTZlA==", "signatures": [{"sig": "MEYCIQC4/RXXIVroLLrt7JIwsDWRVU/RV3w6ZhK4uPYzW4OGqAIhAPy/0+kzMucTPT0DgX9MOIqIpa5K9SXBowjXiyg9Gh88", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 91761, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeXW0MCRA9TVsSAnZWagAAvn8QAJHGY0rSQicG6809E5rG\nkOMu13oLNhO27M98xi5uOak2eYG3hsKqDKnngsUDqKj7wbWWyKAEN3kbhOfS\nF97vJaf+DbxQtnbDL5YZSMtYYmOAjpTsAkYP2vnRc2ovWUXRwR0f2O5Q5idX\nHwRLIpIUUsYEwFKq8Re9oTJnLGcQWLWmIdv3lEaujX9fsEInC8hb5aFZGrJJ\nx4HA5hcNcJK6gZt4G6wAqbgqKDvouR73IDOYyFST3G6VwlXD/ZA7Gx6KGqHu\nu+okHWuKybJwK7xJzoa8qs0gdXTmqSc5a6oQGKWFj6C4oa22EMaprDTn27xv\npYf4phKKEKr2UWyhZPVfdW4oeAtinetxM6gV6vLt2f6MQizXekUibzJRzLn+\n9B8bXeEiYOXWK8RZKwPlDl20fOXdYgLjHq/uo7DCh4FOq43nf7q+1eS+r6oo\n21dYXEFvwXYgHtF2JkqfXjN0xfhyt2+JSVEiYH6DJUDC/QRPYVEeitKa6zDK\nyWBNUHP8CidgLUG4Kj3wjQKYqjoMXZNdE0QJpVZZLMj+CjfvqQSPXcLjyc8g\nhGI6B7HTRZVyqt6GyhTVM4L+lwZh79N6flLcWtKYPAv5jSTrGOFAIwWvN2Sh\nVDZJYABAVS0APfqhLV/k1U2h2qZCLWvBcBpoStr09+A77JY67DcUOzxCS2tx\nX9f0\r\n=YJwA\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.2": {"name": "uuid", "version": "7.0.2", "devDependencies": {"esm": "3.2.25", "jest": "25.1.0", "husky": "3.0.9", "runmd": "1.3.2", "eslint": "6.8.0", "rollup": "1.31.1", "prettier": "1.19.1", "@babel/cli": "7.8.4", "@babel/core": "7.8.4", "bundlewatch": "0.2.5", "http-server": "0.12.1", "lint-staged": "10.0.7", "npm-run-all": "4.1.5", "babel-eslint": "10.0.3", "@commitlint/cli": "8.3.5", "standard-version": "7.1.0", "@babel/preset-env": "7.8.4", "browserstack-local": "1.4.5", "selenium-webdriver": "3.6.0", "rollup-plugin-terser": "5.2.0", "eslint-config-prettier": "6.10.0", "eslint-plugin-prettier": "3.1.2", "@rollup/plugin-node-resolve": "7.1.1", "@commitlint/config-conventional": "8.3.4", "babel-plugin-add-module-exports": "1.0.2"}, "bin": {"uuid": "dist/bin/uuid"}, "dist": {"shasum": "7ff5c203467e91f5e0d85cfcbaaf7d2ebbca9be6", "tarball": "https://registry.npmjs.org/uuid/-/uuid-7.0.2.tgz", "fileCount": 49, "integrity": "sha512-vy9V/+pKG+5ZTYKf+VcphF5Oc6EFiu3W8Nv3P3zIh0EqVI80ZxOzuPfe9EHjkFNvf8+xuTHVeei4Drydlx4zjw==", "signatures": [{"sig": "MEYCIQDQ0Uss9lZaPMJ6XBRR+DcCzUVXIJW+CgUFbjVOtS6+7gIhALhC5uKN5LLq+0/AQmSNN+zeM5KgtJ4PBTMYZpNoYm7t", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 92084, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeX6O9CRA9TVsSAnZWagAAjlYQAJ5hKsHQkBAKkf1NzVvv\nQ+/w/d3YJgcfrlrDFe2jqbGqooI74L6ueVPlJs4LIKUCpVvhZIvY4yGHI2hY\nYgzFhbffp2i6E8DGKOvrVvKR6+MFh97vAcOzI2ZUAzOtea++arEJLmxcFUxr\n9VXnY7j1GztqULh0p0XPfAXFkCzm/lm/hDTnBMr0A82qGs0u1PoA5haJl2si\nNA7bFMYQX5nXYyRi/sFfLC1aPiyGDoWs5fGFKRjMSsn7gA+oiI83mpWZ5WVn\nKq28bNNDc+aqe3yG9aT7veXXv3aeUnHmEbP8u6gpswh7QG2h+gNmxvDriovy\n08BxDuR89SPwKIzOb3UF/gk6wPSCpMHZ46xqTX8nVjnEfdvZDMQnzpGCIfGz\n8++uhxIFRbAKg+tvCgClS3fFqxZ27Ai1zNBbnNfH/+XjdH+idOZbK5yglWGr\nIAnYkstMmAsSF+dS1Betk+dnHfgUKJHd2zGU8USulhWblvSW3YIpeXNaIQzB\nzWKP9a47Yp+6B33HcqhRWOSnkhj7xsoL0LEhcrYhC8Zk1KSS5uzqucLpnNfG\nDyVzlxj/BUaNUVVsP3HuayAWtkmsVOZm3O6aKXog7IX4keIw1CCcfDM6bnaD\nzd++5iyGEFfgENa7Fl7LKxAQy2Y4Olcy/aUn3S4ht9CF7GGXvuOc9fNwf2QU\nrjjb\r\n=Xkrr\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.3": {"name": "uuid", "version": "7.0.3", "devDependencies": {"jest": "25.1.0", "husky": "3.0.9", "runmd": "1.3.2", "eslint": "6.8.0", "rollup": "1.32.0", "prettier": "1.19.1", "@wdio/cli": "5.18.7", "@babel/cli": "7.8.4", "@wdio/sync": "5.18.7", "@babel/core": "7.8.7", "bundlewatch": "0.2.6", "lint-staged": "10.0.8", "npm-run-all": "4.1.5", "babel-eslint": "10.1.0", "@commitlint/cli": "8.3.5", "standard-version": "7.1.0", "@babel/preset-env": "7.8.7", "@wdio/local-runner": "5.18.7", "@wdio/spec-reporter": "5.18.7", "rollup-plugin-terser": "5.2.0", "eslint-config-prettier": "6.10.0", "eslint-plugin-prettier": "3.1.2", "@wdio/jasmine-framework": "5.18.6", "@wdio/browserstack-service": "5.18.7", "@rollup/plugin-node-resolve": "7.1.1", "@wdio/static-server-service": "5.16.10", "@commitlint/config-conventional": "8.3.4", "babel-plugin-add-module-exports": "1.0.2"}, "bin": {"uuid": "dist/bin/uuid"}, "dist": {"shasum": "c5c9f2c8cf25dc0a372c4df1441c41f5bd0c680b", "tarball": "https://registry.npmjs.org/uuid/-/uuid-7.0.3.tgz", "fileCount": 50, "integrity": "sha512-DPSke0pXhTZgoF/d+WSt2QaKMCFSfx7QegxEWT+JOuHF5aWrKEn0G+ztjuJg/gG8/ItK+rbPCD/yNv8yyih6Cg==", "signatures": [{"sig": "MEQCIB8rpyV5wxjOGrmNtRkM/yt650w956eEas/2kBWtqdc0AiAMzo7dnEqhT0kFl40Rmitw4twBZLoWnCuUMhaagi2lyg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 92933, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeg5zlCRA9TVsSAnZWagAA67UP/0v7FNDUajl++p6xis/E\nyX/iTGa9FhCdz0usy/6trk20kx8RqF4Li0NrQ50r5GOCfFqFYgxbaweJIJM8\nZytCMDxfM4v/gVDwms92hV7BIVWcq+kPcReW8aDliUAWPIx33HnpCkPya7lG\nYbDv6vto4JJNggasmNF9dt5BTu4oof4q2t6VDXC1tMVHQ9SbsQo+0rpiTPDZ\nq+0py4XaNMKx0v1WH81pghxWr+z1AKn2pdx+24E4J4WUn/cA1Z0D7nN+HaTb\nckPlMx4yFJZDxT+EqdtQOHp7kxGht9Xk4xJD5bpUDyLcuurhQeNLLYiI/ynO\n4Txd8Tdby5INbAp9NCHMvzGmLQiZrfOLz54s2l6At3oQfDYShljIGouSgWtI\nktp7A1e5woBLk5KHTYssvDNZmFRBiL743rFGzFF+ZZ7eWT66aGqzDu52wpKF\nRcKidJp7jOgRHA3LruF9byaab6ApmO5tnNIF3qxG1DaAYIOCpHawjgcf+xAx\nDcF0cZy1jUnnOlziH3AwPfs99LlERHJViLXWReX/9jqzN9tp1R7BJQ6nIxjM\ngXp5IEjIe8r68jTbCyDMiwbfsW3//5qrSCvsOfAdQIcZBSeCWsbSblspABtS\nbM7ysSh6srvHNTj2q/zQm2zHxuGyl5beJrhP5n3jUM9IWOU1UYlhfa6nNV7T\nb7H7\r\n=76Y4\r\n-----END PGP SIGNATURE-----\r\n"}}, "8.0.0-beta.0": {"name": "uuid", "version": "8.0.0-beta.0", "devDependencies": {"jest": "25.3.0", "husky": "4.2.5", "runmd": "1.3.2", "eslint": "6.8.0", "rollup": "2.6.1", "prettier": "2.0.4", "@wdio/cli": "6.0.15", "@babel/cli": "7.8.4", "@wdio/sync": "6.0.15", "@babel/core": "7.9.0", "bundlewatch": "0.2.6", "lint-staged": "10.1.3", "npm-run-all": "4.1.5", "babel-eslint": "10.1.0", "@commitlint/cli": "8.3.5", "standard-version": "7.1.0", "@babel/preset-env": "7.9.5", "@wdio/local-runner": "6.0.15", "@wdio/spec-reporter": "6.0.14", "rollup-plugin-terser": "5.3.0", "eslint-config-prettier": "6.10.1", "eslint-plugin-prettier": "3.1.3", "@wdio/jasmine-framework": "6.0.15", "@wdio/browserstack-service": "6.0.12", "@rollup/plugin-node-resolve": "7.1.3", "@wdio/static-server-service": "6.0.13", "@commitlint/config-conventional": "8.3.4"}, "bin": {"uuid": "dist/bin/uuid"}, "dist": {"shasum": "93e7d8e269022a2fa2027d6a77d4129de877fadb", "tarball": "https://registry.npmjs.org/uuid/-/uuid-8.0.0-beta.0.tgz", "fileCount": 46, "integrity": "sha512-Ql2iMiWxJYtI3biUynCFc1J1XS6rOWhv8zN60bWh0hHwJsYZQ4jM0Z2614qS6cyPRRDOu6NVz+cLmCLt6G8eMw==", "signatures": [{"sig": "MEQCICuUBoR999ZZB0a2nYVCVrXTksDpYWGy5Gxd5wKbynR6AiBwMWaNfn9httMgXgP5drSCss27fWXzTtBnF3eyxHqq2Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 91304, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeqeHhCRA9TVsSAnZWagAAnpkP/0679ao/BXaHII9VD9bV\noDN88fiLKeGq75k2kv10eszV2vmnkkkQI9k3609kcDSdZhamDEg5080eJpwO\nEhTBNyhYefqRiYj6Z69iw3VI3mfdRXaOtl1sFzECZVDQLX5Gd8xLhbOVlzwc\nWZyKrn58plFTuN6o6rhlZb7uWUZ11nTqW7et8Dp2b8qLXjDc6SopAQZt2TpM\neLfpe+eJoFrGHaKSUtJobXuh9gsgGttR3mk7Jnx4Py/dbgeXM5FIQlou8DzI\n+8xuud/e3IFDc/E85eLNUtJPmvDtMOxKAsr1X+/s1CxDVT2GHaw+myNFH6Gk\nk6JUmiWPwwVXt0GOQI9kgDfYEOdCtIQSt7CbjJjcjF7Ta8tlrDnqJKQCCgFl\nhXu6paimY4qgFW61/zbE3WleYMb9ktCAlrgWJtQa6Yj4LaVh+lTaPxKcgQCH\nXJAS8J4ZvR5+ixoKenX3qXogw56Z3kM3ucrXiY3iECxqOkAl1/Bl1FyJ6MN/\n7ZkcrfhZ1QFXKu2he6GmdoHUQ8N6g+33gnTYwlj2f6JsY4E1DRAC0ja08HIl\nCY9x8s6LyH+IfrJvRn1UarefQMPaYRXHadhbzjA8csPf2THV5L5zOjOQ21My\nQzu806y7mDIV2X8u37q1v6rrS6G8xvPnUpuNAUrwLL3dI12XxM3PA71xQXkZ\nw8fo\r\n=qoi5\r\n-----END PGP SIGNATURE-----\r\n"}}, "8.0.0": {"name": "uuid", "version": "8.0.0", "devDependencies": {"jest": "25.3.0", "husky": "4.2.5", "runmd": "1.3.2", "eslint": "6.8.0", "rollup": "2.6.1", "prettier": "2.0.4", "@wdio/cli": "6.0.15", "@babel/cli": "7.8.4", "@wdio/sync": "6.0.15", "@babel/core": "7.9.0", "bundlewatch": "0.2.6", "lint-staged": "10.1.3", "npm-run-all": "4.1.5", "babel-eslint": "10.1.0", "@commitlint/cli": "8.3.5", "standard-version": "7.1.0", "@babel/preset-env": "7.9.5", "@wdio/local-runner": "6.0.15", "@wdio/spec-reporter": "6.0.14", "rollup-plugin-terser": "5.3.0", "eslint-config-prettier": "6.10.1", "eslint-plugin-prettier": "3.1.3", "@wdio/jasmine-framework": "6.0.15", "@wdio/browserstack-service": "6.0.12", "@rollup/plugin-node-resolve": "7.1.3", "@wdio/static-server-service": "6.0.13", "@commitlint/config-conventional": "8.3.4"}, "bin": {"uuid": "dist/bin/uuid"}, "dist": {"shasum": "bc6ccf91b5ff0ac07bbcdbf1c7c4e150db4dbb6c", "tarball": "https://registry.npmjs.org/uuid/-/uuid-8.0.0.tgz", "fileCount": 46, "integrity": "sha512-jOXGuXZAWdsTH7eZLtyXMqUb9EcWMGZNbL9YcGBJl4MH4nrxHmZJhEHvyLFrkxo+28uLb/NYRcStH48fnD0Vzw==", "signatures": [{"sig": "MEUCIC9KbaApQziNdTgtI4QGAv44v4cjpjvnqvPxBQqnSduTAiEAoOLPPlKD4rb1IvF2Niza2PAJl6wiiwZY75Dkll1mhLY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 92955, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeqeazCRA9TVsSAnZWagAAXeAP/Rtdc7+Dt5TYs8hZ4NHM\nM/RTUFQsLzjfu6Z5Y8oFVNtRYKaAA0VrWPO6YSYs1RTF1JLRujkGrVw/rn6g\nVK7XwfJna4YS3iOLcg8r8UtNF+dSNcWNcxDF3s9c74WdU6Y4uNzx36prO738\n3XOUluCw2G82AzlY+/D7FZcMhjnDa42ZIU78NQ7NSeUBUQyLfVa+acSAALSi\nmX79a77iF/m+AVfLasb5WjYHM87IFi+HEC48ZYJN1p8rE2fRPUReZtjh7ppZ\nwCLEyyIDTagS9/1bjrP/MCzvGKoBTHl8hELRKMWnZ/aL0QFuHYd7XjFNnILN\nnoTn/B/ZRheZdywH9dh2XlyyoGEOH6UnfsBT/QWCyYrnLTRCYkBLeVUioOZr\ncvDK5Dbb1flkkbUZTfzR9bXdLhsq25c76f1DDRbz1oW3HrhrPI9BXanF3ikR\n6nfKz4vJUgU71bQevXIXjG3QTWlxWODWsb7RPfDK2XpndpBwwn1ztdMBbWBE\nhxcpzCuwfz4PU6xB930GjEw/eN3QbUL/2Ocq22TFupl5hyebg77Gm+nhENNm\nA0rpZ+6XYicFUflDFIiOyPZzu4cKup5esxfC+tJNCYaAUF0jQGmg1BdnjNqQ\n1Ifm08U/3yvft8JG6RkEWFoXqQDk6Qw+AuLfOO3YU/6Vc4Zjyp9hATZ8WvWF\nKjwJ\r\n=Hsnx\r\n-----END PGP SIGNATURE-----\r\n"}}, "8.1.0": {"name": "uuid", "version": "8.1.0", "devDependencies": {"jest": "25.3.0", "husky": "4.2.5", "runmd": "1.3.2", "eslint": "6.8.0", "rollup": "2.6.1", "prettier": "2.0.4", "@wdio/cli": "6.0.15", "@babel/cli": "7.8.4", "@wdio/sync": "6.0.15", "@babel/core": "7.9.0", "bundlewatch": "0.2.6", "lint-staged": "10.1.3", "npm-run-all": "4.1.5", "babel-eslint": "10.1.0", "@commitlint/cli": "8.3.5", "standard-version": "7.1.0", "@babel/preset-env": "7.9.5", "@wdio/local-runner": "6.0.15", "eslint-plugin-node": "11.1.0", "@wdio/spec-reporter": "6.0.14", "eslint-plugin-import": "2.20.2", "rollup-plugin-terser": "5.3.0", "eslint-plugin-promise": "4.2.1", "eslint-config-prettier": "6.10.1", "eslint-config-standard": "14.1.1", "eslint-plugin-prettier": "3.1.3", "eslint-plugin-standard": "4.0.1", "@wdio/jasmine-framework": "6.0.15", "@wdio/browserstack-service": "6.0.12", "@rollup/plugin-node-resolve": "7.1.3", "@wdio/static-server-service": "6.0.13", "@commitlint/config-conventional": "8.3.4"}, "bin": {"uuid": "dist/bin/uuid"}, "dist": {"shasum": "6f1536eb43249f473abc6bd58ff983da1ca30d8d", "tarball": "https://registry.npmjs.org/uuid/-/uuid-8.1.0.tgz", "fileCount": 46, "integrity": "sha512-CI18flHDznR0lq54xBycOVmphdCYnQLKn8abKn7PXUiKUGdEd+/l9LWNJmugXel4hXq7S+RMNl34ecyC9TntWg==", "signatures": [{"sig": "MEYCIQDC/U2XCtN00jXHeDxaH/rwmhAVkEZlb7zWwopLdOX17QIhAKjCYvj14h55VEUc239fuJb3m8qbkAZMAwgP9hCieDiC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 95154, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJexXx2CRA9TVsSAnZWagAAabsP/RriwLU9HvZ0+fc/giqA\nSM/YFCU3fUv7SM7fSOnWBvkTIy3NgsqmCL0v5VsnffVZ5CZtCeF8gFQ9qWFx\ngwiZCautPzGGedJ8sP1yFNkR8QPHofjEk/e+j4CV5WY+i2FU0n3JvBehMcfT\nhTpCWSD42PG4x+HZ161Mtl9DxzXxXgGS+xw+X/kOa9m2cB8DjhPD2G+8C7ia\nmGNut2ul0py+lzsBQnWcA/fBlRZ7mR0aF7I7jGFcCt7721boIPVhyqQ/ihHq\neZI7UKwG3ofh8kHvO0wBZ9Mt/XplXFlzsBWY+gry4a2oJc+g/4AEmyVB6dQ3\neWvhCmueXSR0J4SPl2Husu9IAOofb0vwLj5VM9SUhbQAY95TD8947qHB6fFc\nBBh7OSrPjvfmYbE3Q03v8jvq1yahlBDqXD/ZVuvCVymMsFAg3mC0tai7kNbk\nbPlw+IiBt0S7xW5rmOtaLx4j4hw9YD+BtOJlgN0djbZT6qLNeBvqmYFTWE/v\nWuqUAfpEZFmnNcm5RuxxKh30WMq4Etspuag0PZ0HlIMIEZ+XOzwMD7yWM5dZ\n2pueDC5Bbq2yr0YNdTAff9s1YKdGt33ot8ugTzXdmlJu6n5e5WLr2EKb+3iD\nnLcwRHkbab2rrNPpHLfdQxsFW277oCbOxhfhMO2piwJ+wx30jD9anaYumqI5\nhbEG\r\n=3jU2\r\n-----END PGP SIGNATURE-----\r\n"}}, "8.2.0-beta.0": {"name": "uuid", "version": "8.2.0-beta.0", "devDependencies": {"jest": "25.5.4", "husky": "4.2.5", "runmd": "1.3.2", "eslint": "7.3.0", "rollup": "2.18.0", "prettier": "2.0.5", "@wdio/cli": "6.1.20", "@babel/cli": "7.10.3", "@wdio/sync": "6.1.14", "@babel/core": "7.10.3", "bundlewatch": "0.2.7", "lint-staged": "10.2.11", "npm-run-all": "4.1.5", "babel-eslint": "10.1.0", "@commitlint/cli": "9.0.1", "standard-version": "8.0.0", "@babel/preset-env": "7.10.3", "@wdio/local-runner": "6.1.20", "eslint-plugin-node": "11.1.0", "@wdio/spec-reporter": "6.1.14", "eslint-plugin-import": "2.21.2", "rollup-plugin-terser": "6.1.0", "eslint-plugin-promise": "4.2.1", "eslint-config-prettier": "6.11.0", "eslint-config-standard": "14.1.1", "eslint-plugin-prettier": "3.1.4", "eslint-plugin-standard": "4.0.1", "@wdio/jasmine-framework": "6.1.17", "@wdio/browserstack-service": "6.1.15", "@rollup/plugin-node-resolve": "8.0.1", "@wdio/static-server-service": "6.1.14", "@commitlint/config-conventional": "9.0.1"}, "bin": {"uuid": "dist/bin/uuid"}, "dist": {"shasum": "bbab71018e75e556381986bbaaf849648d0665c5", "tarball": "https://registry.npmjs.org/uuid/-/uuid-8.2.0-beta.0.tgz", "fileCount": 46, "integrity": "sha512-hwW39cDgHKtopHIRUBDMrGIpPTWOYEid17+nr9uQcuijDGMLZ9NgBZOFOfgn/UISN+3NVbXpvytwDR8WlU7RrQ==", "signatures": [{"sig": "MEUCIQC5owbHi9Ang+ySmVMChgsxBvp1/kMPUNgXmF/bvlBq2AIgPD/AI55R9r8vSc/K09Vr6FO9OAu0UBgV78dZ2GF9Z5U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 95945, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe8bGCCRA9TVsSAnZWagAAdrQP/22jrv7ITic0g4+xXc1W\n0DFiX/wf2FRbebPCrSwgXDX0DprjToyNMOOPIvOSSv8aveUYxOTLvBUEWIDA\nwWegdJT3d+cRHgWW6ipWO8lHFEMWmil0OJXfbL5QbEQDilcfConc3JIVybh+\ngActhVsquZhD1kC1W/m3hg48jT1ku3ferfhbNVLheYjyKdzHrw4Zp+cpU9AV\nlFqKWebIj6j7+BgWVP0HDoxV9BuRE4hPtk8Gy2I6Ns3jQHHMKydjCXbk9/UE\nKYh/R5wkAimrTJiOFP7KD6YkiRnrI1TLGpOoEwfgZdh4D53JFv6dzh3Aoa2M\nSsbd+a5vY7PxtfcPbm2ljDeTfKHld/srCWZVQHoeRnq5iGbXcrNz0TPjdKE8\ndUJhGM5QR4P9xB8o/n4Rsx58xnnLcWnVjOE+cPoGRiYTAsM6/i8QIeCSe3t3\nKr+qOyk+bMAGxrnriCo4O66C4fZiFcjQftYVPc2LgzvOGWvvCSGUgfuY5H4B\nqfOBMXyfQYEMSh4Io6sYDVGMdvJcuMsXHkzz08+hqyrWWgFoy0G8tFa4JqQG\nsDttZp20egk/VsszFnbUFFZjN5mK9YzoYq4xtv+VV9jPvawSQO6UZxw4WZ6h\nLW52Su/iDTaXV3EXvzKVtP2kLIDBS+fgVtIccKmGiIDtqC63PfCgZEqOt88T\nCjMB\r\n=Qdr4\r\n-----END PGP SIGNATURE-----\r\n"}}, "8.2.0": {"name": "uuid", "version": "8.2.0", "devDependencies": {"jest": "25.5.4", "husky": "4.2.5", "runmd": "1.3.2", "eslint": "7.3.0", "rollup": "2.18.0", "prettier": "2.0.5", "@wdio/cli": "6.1.20", "@babel/cli": "7.10.3", "@wdio/sync": "6.1.14", "@babel/core": "7.10.3", "bundlewatch": "0.2.7", "lint-staged": "10.2.11", "npm-run-all": "4.1.5", "babel-eslint": "10.1.0", "@commitlint/cli": "9.0.1", "standard-version": "8.0.0", "@babel/preset-env": "7.10.3", "@wdio/local-runner": "6.1.20", "eslint-plugin-node": "11.1.0", "@wdio/spec-reporter": "6.1.14", "eslint-plugin-import": "2.21.2", "rollup-plugin-terser": "6.1.0", "eslint-plugin-promise": "4.2.1", "eslint-config-prettier": "6.11.0", "eslint-config-standard": "14.1.1", "eslint-plugin-prettier": "3.1.4", "eslint-plugin-standard": "4.0.1", "@wdio/jasmine-framework": "6.1.17", "@wdio/browserstack-service": "6.1.15", "@rollup/plugin-node-resolve": "8.0.1", "@wdio/static-server-service": "6.1.14", "@commitlint/config-conventional": "9.0.1"}, "bin": {"uuid": "dist/bin/uuid"}, "dist": {"shasum": "cb10dd6b118e2dada7d0cd9730ba7417c93d920e", "tarball": "https://registry.npmjs.org/uuid/-/uuid-8.2.0.tgz", "fileCount": 46, "integrity": "sha512-CYpGiFTUrmI6OBMkAdjSDM0k5h8SkkiTP4WAjQgDgNB1S3Ou9VBEvr6q0Kv2H1mMk7IWfxYGpMH5sd5AvcIV2Q==", "signatures": [{"sig": "MEYCIQCPd2R0iAx0ALTVzGNyY6T8KZ2gc9CKSeFB2EEGug6xNwIhAJXelXmuyKvAas8x78nL/D08D5oEYDSAZnP1rYo4YzeM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 97285, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe8mxuCRA9TVsSAnZWagAARZsQAJz+kpmfR+I/GNkijZ0M\nmZWvdB5CuNKL/w0w4Lz7KPL43fpPSpWzmMs4uS49fXddbFPNUjO9tAYtWjD+\nuhvvWLjPcg8LNBnlWZJGLYIqjqexvERyrm+vPntComY60qwAxx2v+ZCIAeYj\nBKVXw2zUX0y1ICQWIvVpfUXXT5Zgl40CFEOSaBhewbWQuckED6g0rmlp4UTi\npNHIcrQxh/zzQjiPdQEMOf8jxMa3nbPwW62mAeIjgNiQCsqTi+9JwYDmJWaw\n2zKQu77TReMdv4IsULhgTiTXkH5LWkt+kethQhzZt5hMamRIrnAp1D6MyWoJ\nWHZYja+V54wN7lvAv2LXRfsLOEWK/7/sj2VenobnelbmhmOFKn3PrQPC9bVM\nFz9zQYuc6FpY+xHrM4kmWKuNsHcO38xgB7trsT22vWRJa930H8mjboXm2zNB\nq85115NCbZNrX9Q3vWtwPAkEXxtL3XP4h0AQcq37u2Ok7f/a7dt5+BHdOqd0\n/vazeDTgVifwKLVUCshvsZlCVD7Yc8hw16m43I4g9HlinPFXACTdLON5t0zA\nfus/e3SE2KQrw4AnzuFzF3hM2uiTWGp8agfJ6RqF2DFAFKrswA9oz7QJjT6k\n/pQhrcu9tCW0bnKYD3nT9258szQpiILve+VEUzwrlU+VR3g9nH5ef9SOOQkC\nn1aP\r\n=mMth\r\n-----END PGP SIGNATURE-----\r\n"}}, "8.3.0-beta.0": {"name": "uuid", "version": "8.3.0-beta.0", "devDependencies": {"jest": "25.5.4", "husky": "4.2.5", "runmd": "1.3.2", "eslint": "7.3.0", "rollup": "2.18.0", "prettier": "2.0.5", "@wdio/cli": "6.1.20", "@babel/cli": "7.10.3", "@wdio/sync": "6.1.14", "@babel/core": "7.10.3", "bundlewatch": "0.2.7", "lint-staged": "10.2.11", "npm-run-all": "4.1.5", "random-seed": "0.3.0", "babel-eslint": "10.1.0", "@commitlint/cli": "9.1.2", "standard-version": "8.0.2", "@babel/preset-env": "7.10.3", "@wdio/local-runner": "6.1.20", "eslint-plugin-node": "11.1.0", "@wdio/spec-reporter": "6.1.14", "eslint-plugin-import": "2.21.2", "rollup-plugin-terser": "6.1.0", "eslint-plugin-promise": "4.2.1", "eslint-config-prettier": "6.11.0", "eslint-config-standard": "14.1.1", "eslint-plugin-prettier": "3.1.4", "eslint-plugin-standard": "4.0.1", "@wdio/jasmine-framework": "6.1.17", "@wdio/browserstack-service": "6.1.15", "@rollup/plugin-node-resolve": "8.0.1", "@wdio/static-server-service": "6.1.14", "@commitlint/config-conventional": "9.0.1"}, "bin": {"uuid": "dist/bin/uuid"}, "dist": {"shasum": "fbe17ffc7b6214d36a52c89f9e19c82dcc0f294a", "tarball": "https://registry.npmjs.org/uuid/-/uuid-8.3.0-beta.0.tgz", "fileCount": 66, "integrity": "sha512-YX12mJFOtrnE7o7GIbtYIoTrRN+5DTKLJXiUMnucohXeBPY0UYIaK2HrteuarIrWrjNvZ7FROqXMRNPKQz8wMg==", "signatures": [{"sig": "MEUCIQCY4tELxAUmyUTtnM7eZnQ95HW5XWbnDdUNaAiS0UJ8TAIgBHP9wTozaFYBy8Z4CkEUYANwbFcQE0gyzyfqiDJO8iA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 112220, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfGL2qCRA9TVsSAnZWagAAqYsP/iD/kd3ajMto1FoCWAiT\n4B4tJRVvY6XYxtnYSukB3C2elXkcb8qWDE60nDRq/PWqBNuKDlXnXxpgGE4C\nJcfY1i+XDP4BxTUM1FxJGhrx6uNTun5zLJnBS02oUxe4LCIeuWus+160ETtQ\ndaWsEglcDQ6yiInKkFGMHb0FdRVPJofjylbxfj/dAVrgHvnCi9obUSw/m8ua\nmrVskeJ8U/X93H4rKen4QDDGyseB3tHqkX2GhrZqdOj3zmX4QAdonBmSyTny\n97YJ5xSQ/vFTEXVAPmbjrp6M5OAGnXS3YYNl3nTUhbsrs2POgSrWCLiucwX7\n1S576Rj2PmMP1PZ6lomFd3HafAlfz9ftGii98bZrG1aWZl06K3jZahYi53tn\nAknpoKggfk/4I0UpeX5/FiO4klZcR1/ggSC2PaJCcrIalYbCdBkzcWCfzXEh\nqlYRzR3S+nqOJqL5KJmSWgZsJVXMAAmx07wyXUHyBoPUj+oitxCN0ueANTUN\nzEibvxMUtj80iUv9FfN0eQUjnyIF4n0+8yz8L6yf4zK14LyaKr16bZq/iNSM\nOVgFp5jhqcFMGnFVaheX74N6h0VezVBOr8YB75pvZGbVGR7FMg3nBLaC76A/\nQtDObF08Z096NaZZhjiZGziMj8UWwnLVPg5EXedSqZPCqclU6Jsow1MT6QHX\n7jEI\r\n=YaPl\r\n-----END PGP SIGNATURE-----\r\n"}}, "8.3.0": {"name": "uuid", "version": "8.3.0", "devDependencies": {"jest": "25.5.4", "husky": "4.2.5", "runmd": "1.3.2", "eslint": "7.3.0", "rollup": "2.18.0", "prettier": "2.0.5", "@wdio/cli": "6.1.20", "@babel/cli": "7.10.3", "@wdio/sync": "6.1.14", "@babel/core": "7.10.3", "bundlewatch": "0.2.7", "lint-staged": "10.2.11", "npm-run-all": "4.1.5", "random-seed": "0.3.0", "babel-eslint": "10.1.0", "@commitlint/cli": "9.1.2", "standard-version": "8.0.2", "@babel/preset-env": "7.10.3", "@wdio/local-runner": "6.1.20", "eslint-plugin-node": "11.1.0", "@wdio/spec-reporter": "6.1.14", "eslint-plugin-import": "2.21.2", "rollup-plugin-terser": "6.1.0", "eslint-plugin-promise": "4.2.1", "eslint-config-prettier": "6.11.0", "eslint-config-standard": "14.1.1", "eslint-plugin-prettier": "3.1.4", "eslint-plugin-standard": "4.0.1", "@wdio/jasmine-framework": "6.1.17", "@wdio/browserstack-service": "6.1.15", "@rollup/plugin-node-resolve": "8.0.1", "@wdio/static-server-service": "6.1.14", "@commitlint/config-conventional": "9.0.1"}, "bin": {"uuid": "dist/bin/uuid"}, "dist": {"shasum": "ab738085ca22dc9a8c92725e459b1d507df5d6ea", "tarball": "https://registry.npmjs.org/uuid/-/uuid-8.3.0.tgz", "fileCount": 66, "integrity": "sha512-fX6Z5o4m6XsXBdli9g7DtWgAx+osMsRRZFKma1mIUsLCz6vRvv+pz5VNbyu9UEDzpMWulZfvpgb/cmDXVulYFQ==", "signatures": [{"sig": "MEUCIQCNFGDHbOcxZ53L6dQUhHtZHc/5FF6/fIadt4jyo6jFMgIgblvzqZQDW3ZNsIm8l2S6kXx5h9KlzHd/GDMi3EphMz0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 112959, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfHySQCRA9TVsSAnZWagAABs0P/jASs5mpvF0tYjja886Z\nK7hAuenEWArgOaP5Ec7kDuwTmvq7m0sDVM630EI+oobCitzyvuN2WsjSbF6K\nobylxJb0OE24W9zy1qCIzMZdsWMWj5K9I48ZowBHUcnIRS02UbWBndNJf7d/\nyCOvP75r9O+tYSoqKdj6hQjGr+5/wQk4WX1u8EI5RT0Z33hoBZ4geFsg8XBW\nlrjKjUaePNjBFwlJxO1osSXd/H2pWEFy9olflx9owJHdscwvz5FNawTSmxaF\n55pYI7kZc3KgQBb8x/d6sYzSC0HcQ87EnZCnlm8AU4cdZQNkak9Q1MDVG9fg\n+GvFVDvrzmx77HxylrsA00HrXK2iJnwKl7rABm7BJmtNGH3FuuKAyBUaMlLl\n//7UwUqh7w0Gu2TjJpNqTkVOzRxwbrvasQEDWPjfZqYKCjpUhM2AHIkGBom6\no3qTej2yW0XcTixgQR4hGhNcyEsFNka3SVUcpek8elwFrrOMpXbnCF5pe+f9\nVwjn3Dg3Ld5vS4KNATeCg90G54MRlkHcajFutLKmmsgYrakqIdS8rbGDA0wi\n/KNYzTVW34HyIUozC4lSEIbYJluob/SqvAxQW9PLH0c/O+OcWj4jF4gHcGzU\n1oMIZvEOSp3eMPc5p9bzzqT2EAx7o5KD2LUfdYCPXQ/pnRoYNRbxHjeU7ys4\n+7pE\r\n=Cc7C\r\n-----END PGP SIGNATURE-----\r\n"}}, "8.3.1": {"name": "uuid", "version": "8.3.1", "devDependencies": {"jest": "25.5.4", "husky": "4.3.0", "runmd": "1.3.2", "eslint": "7.10.0", "rollup": "2.28.2", "prettier": "2.1.2", "@babel/cli": "7.11.6", "@babel/core": "7.11.6", "bundlewatch": "0.3.1", "lint-staged": "10.4.0", "npm-run-all": "4.1.5", "random-seed": "0.3.0", "babel-eslint": "10.1.0", "@commitlint/cli": "11.0.0", "standard-version": "9.0.0", "@babel/preset-env": "7.11.5", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.22.1", "rollup-plugin-terser": "7.0.2", "eslint-plugin-promise": "4.2.1", "eslint-config-prettier": "6.12.0", "eslint-config-standard": "14.1.1", "eslint-plugin-prettier": "3.1.4", "eslint-plugin-standard": "4.0.1", "optional-dev-dependency": "2.0.1", "@rollup/plugin-node-resolve": "9.0.0", "@commitlint/config-conventional": "11.0.0"}, "bin": {"uuid": "dist/bin/uuid"}, "dist": {"shasum": "2ba2e6ca000da60fce5a196954ab241131e05a31", "tarball": "https://registry.npmjs.org/uuid/-/uuid-8.3.1.tgz", "fileCount": 66, "integrity": "sha512-FOmRr+FmWEIG8uhZv6C2bTgEVXsHk08kE7mPlrBbEe+c3r9pjceVPgupIfNIhc4yx55H69OXANrUaSuu9eInKg==", "signatures": [{"sig": "MEUCIQC+WYJRHHHyxeGtLWPj90JE3EKJo/r2VrnXEnU4wEsq9wIgDeyRdLAm3l/Vw/j53Z9hSqEscPD8C5x/7LJip+Ws4ks=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 114950, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfee5mCRA9TVsSAnZWagAAP8AP/RlQNqqmCN+O+fGNB+/W\n3JIWNWGPhLXMAiHDUrLxEJsCyuoG4F5fm3ouesRKUiChdptAGWKPElsuPiU2\nSONN3lZclwU8K/zc8jwde4B/4Si+UaHdtTZa8+66bseHOM0/QKNRcUVCDEk7\ndP2Y/E9yhsX4Q5f69YicVCZqBSOy+wPDjr1CHNxFJuGCZzYHu7GjBExSHE66\nrSVqsWIhIoUl1vaZP8YqlwnWQRWmbene8Muzz7EW6zM9NsXh3q1SKS2nCjZd\n/r3i/Ci4UzU7VZIiZNzUNNRwhA+474Anrq60QGS+N4ovgbfCVIxKhGQib4Rx\nFCq5LvjLR/9oH1nbM3GxpHLUz64jmVlmu0+Zj3paQNodzUK4WGN18VM1jvO5\nGMdfvGUnHiZduOpmpiXUFthapbp5js0/O8sNDaMr2rYlayz1IUUTDW1XeftE\nQ7c7SYdOvO/KGqjDTNebilJOQ4hDILbmcwZF9t1SXxmgSbpfw/2iJYzbb8LG\nVOZi/Ie1yCDdzX4qqS+/lGGI9TvkVmOYSat7gQ7NlCo3sy5fExdFDK+ewmwM\n+tNw/v1DDWaOtUqYBdgF7Vb8lj0piDy7+PH2/CvJBjdUbja4Ihv6mcuybJPw\nmKlI5EshBeaZTdtxSHcgecuAUHFCf2Z29VHpexUOrkZvi1GJzVCYFMxXX/7r\nUfDb\r\n=dV+3\r\n-----END PGP SIGNATURE-----\r\n"}}, "8.3.2-beta.0": {"name": "uuid", "version": "8.3.2-beta.0", "devDependencies": {"jest": "25.5.4", "husky": "4.3.0", "runmd": "1.3.2", "eslint": "7.10.0", "rollup": "2.28.2", "prettier": "2.1.2", "@babel/cli": "7.11.6", "@babel/core": "7.11.6", "bundlewatch": "0.3.1", "lint-staged": "10.4.0", "npm-run-all": "4.1.5", "random-seed": "0.3.0", "babel-eslint": "10.1.0", "@commitlint/cli": "11.0.0", "standard-version": "9.0.0", "@babel/preset-env": "7.11.5", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.22.1", "rollup-plugin-terser": "7.0.2", "eslint-plugin-promise": "4.2.1", "eslint-config-prettier": "6.12.0", "eslint-config-standard": "14.1.1", "eslint-plugin-prettier": "3.1.4", "eslint-plugin-standard": "4.0.1", "optional-dev-dependency": "2.0.1", "@rollup/plugin-node-resolve": "9.0.0", "@commitlint/config-conventional": "11.0.0"}, "bin": {"uuid": "dist/bin/uuid"}, "dist": {"shasum": "ecd8091258ce05be1dfb1fa7330481aaf90509a4", "tarball": "https://registry.npmjs.org/uuid/-/uuid-8.3.2-beta.0.tgz", "fileCount": 66, "integrity": "sha512-V2GewaGescJgTw3WDBz3xC6is44S1eCavBX6Kjou1+yLjHnMWA4rxfV1cmGTw2HMGO91AO0+8DvvQyRzWyu2iA==", "signatures": [{"sig": "MEYCIQCaMg4u1Ea79FgKD5O8f9LTotO6XbXzsYsYvNMOWiH35wIhAKznhNt6ndneH5qCcNSFaapUBi0upJCDzuBHGGr7FFDc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 115267, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfuRG4CRA9TVsSAnZWagAArWkP/A4ky90XZaB05E3M+XxL\nXm2wSelV84WssDZTlcmtcH6cfDJhhVvoRRGLjtLK4n3uvlO25zFpBnvcsVLz\nrJmXWkZnev/R5jBKig917EQKSY3xbelYFff+Jp+rAfQsTkHSx4Di3yIhlNkk\n9ow62Rnf0iKTw/LsjY47IXsiPaADt8I+9pbtGA2qY25fxBL6NU+ES4kxM4v0\n1kbCpNSSfak+7W36vgMJEeUwF/RIom8KlYLvqwHCMyNfcboQxgP3SRMEYUKQ\n6+hvY1iPPqBpElRoWNb5F+Z8CTsWpqHEGNh6M8ki1Nqd4ZmciUMea/EqgGno\nX/pPnXX+Eo/NZsb6PtnuymvBJpXsiTmyJdNyLldKi/6iaIzjrofANY8zasNs\nqxd32egI5voyxJ7k9TgkL2oy5XSvF1VCkuPWh7BAI8KihSwglt/KuQopDUZC\nlonzHQ72L/dwMkEoMzj8l8MW3A5BUtnf/NlWphcP/XdU6jrQ2B/KfggLoJBo\nLNuupLAIhLIRifO5y1QB6ngoSv4hoWbPT7ONhAljybJcrcPbKxU9NsOTBN/f\nYV8KQWgE/SKx7h+Xe7VopWBwr/S6VAXjdcJuQraqlv4zb2mUV+TUslTUb2fN\nTNCBf3x2yFdvT9L7Xr3YIbSb2Jxq86BzdrsM0kBOqx+qW9bNUFyPX8YU+Czf\nouYF\r\n=4xPE\r\n-----END PGP SIGNATURE-----\r\n"}}, "8.3.2": {"name": "uuid", "version": "8.3.2", "devDependencies": {"jest": "25.5.4", "husky": "4.3.0", "runmd": "1.3.2", "eslint": "7.10.0", "rollup": "2.28.2", "prettier": "2.1.2", "@babel/cli": "7.11.6", "@babel/core": "7.11.6", "bundlewatch": "0.3.1", "lint-staged": "10.4.0", "npm-run-all": "4.1.5", "random-seed": "0.3.0", "babel-eslint": "10.1.0", "@commitlint/cli": "11.0.0", "standard-version": "9.0.0", "@babel/preset-env": "7.11.5", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.22.1", "rollup-plugin-terser": "7.0.2", "eslint-plugin-promise": "4.2.1", "eslint-config-prettier": "6.12.0", "eslint-config-standard": "14.1.1", "eslint-plugin-prettier": "3.1.4", "eslint-plugin-standard": "4.0.1", "optional-dev-dependency": "2.0.1", "@rollup/plugin-node-resolve": "9.0.0", "@commitlint/config-conventional": "11.0.0"}, "bin": {"uuid": "dist/bin/uuid"}, "dist": {"shasum": "80d5b5ced271bb9af6c445f21a1a04c606cefbe2", "tarball": "https://registry.npmjs.org/uuid/-/uuid-8.3.2.tgz", "fileCount": 66, "integrity": "sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg==", "signatures": [{"sig": "MEYCIQDoIGLM2QHJtoVrBPsbbJcg2h2KwYvA1wDXmB9fI/0ZWgIhAI7TzATj9BbUwfvE5uIZqDRKCbmQ91wpyujY019QKegA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 116098, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfz+RMCRA9TVsSAnZWagAAa6AP+wR9Y2qbJF3GrA17E3aE\nw+Lt1lhp7AW2Nid8rcFEO9umfSvAuN4PX6SX5yJI+aznhmt33GQOGDhvfFzh\nwCJTbeMKjw31BcZVd0jQ4yZKDOl5hw95r7qLE3aJkhukfmaEx/RmtefQrRoi\nFw0gIzdnWehJ3lIOhqEYlXKzQS1BJv1UukwImMptGVhUyTIxPz3MRP2TRG89\nTDIn7Bss4cQblEyScNyiG+k2P4lFo3zhDJZb8UVjtmTD+iaWwKqJ+Dll/10E\nWllr/JUhU15GnegxPEqIdGwfVyUtyFGhQKTJqJErlhCTKrNmacYHuYl71cR6\n2H01asQTBGBDuUSj+SV/b9o5di5LzFmB0jXG9FId1tG6zlOPrKBksO9V7vJl\nQR0jUQJd5kXkNqcjmTJaJHfPsmpJkA5RSf3/o4INMgrqLemgtGXad0tsgmK2\nahxQ/ONGUpEBV6D0D08fmoqG75/nnvXOOLWin22bnwtJq/W+J+pqirxzzVte\nRmBAZIgJERjwhtNup/qhD0LuUwJXrDwuoZhu1GbDDRUyTSar/uB2ZTbfF/GV\nmCLzoTSZzAE64cz1R2mBl0E1BX6p60rcPRNH/AVRsl/18V5IMQS0lK6BnEZN\npRVbgLgwdCLQxAAtw0/XKa+FU3cyttyv/udG3Zpv+Gk4nYVNLl8TBcuxvFPG\n/zXt\r\n=IPIq\r\n-----END PGP SIGNATURE-----\r\n"}}, "9.0.0-beta.0": {"name": "uuid", "version": "9.0.0-beta.0", "devDependencies": {"jest": "28.1.3", "husky": "8.0.1", "runmd": "1.3.6", "eslint": "8.21.0", "prettier": "2.7.1", "@babel/cli": "7.18.10", "@babel/core": "7.18.10", "bundlewatch": "0.3.3", "lint-staged": "13.0.3", "npm-run-all": "4.1.5", "random-seed": "0.3.0", "@commitlint/cli": "17.0.3", "standard-version": "9.5.0", "@babel/preset-env": "7.18.10", "eslint-plugin-node": "11.1.0", "@babel/eslint-parser": "7.18.9", "eslint-plugin-import": "2.26.0", "eslint-plugin-promise": "6.0.0", "eslint-config-prettier": "8.5.0", "eslint-config-standard": "17.0.0", "eslint-plugin-prettier": "4.2.1", "optional-dev-dependency": "2.0.1", "@commitlint/config-conventional": "17.0.3"}, "bin": {"uuid": "dist/bin/uuid"}, "dist": {"shasum": "164a23bd9bc422462a1274e64d34ab7a8dc3f31b", "tarball": "https://registry.npmjs.org/uuid/-/uuid-9.0.0-beta.0.tgz", "fileCount": 76, "integrity": "sha512-FYW2Ry9thUvDKQKekvKvQhGifh6X4FYAkbN56sYD6l4Zh8EG3GyIiqRKEq9UuIEPO/I1u/grfQxlRjTgnLxvMw==", "signatures": [{"sig": "MEQCIDnMHDyBT0Gs8Tq9un0ZtOBm1BkO+JxBU3YULBBgj/B4AiAE9OrxsVoz7zg4nl1bPgsQmgFkbNL/7HG3MM8o0Gx2xA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 119295, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi7O0UACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmodMhAAo6mAI1wYveeyPvV4CUL8q5w1SZ66SW4xERBGaUTHj4DaxW/S\r\ntVSVJ/px+4yfCoIDb6mVN95Gvo37Q4QezE+BALzymMVdKSnlRr9e38qXfqHG\r\nb3v/MaLOYyG6zNEHVUVJN00X87PEIFMv6llJh624ghMFFn/3WwBG3ZMeQ0Qe\r\nLWdKBnENr6A7Dw51n9T9wGY7dKWs4fc2BkytMGIpH1jfYhV3S6M6LthLKOFM\r\nsGMGEagSm/3dxXwARnCRUUJWtTuQV+BlDEogq/VQCCrKcM7QwqMmf/FWZXBm\r\nCLqKqur0whw5sY3flmQ5Z66jtIQFWiYerrgl6NW+huQ0BhxXL5E95zcGfbTk\r\nrVz/iUeW3XBW99oq63+yaSS+pIuHV46XIU44w1i7BkxUOHGbnIU7LG3vwsaE\r\n+ZU3zDwIal32It/P3xL7wMht56Bo6iRZL4PMpWBoOVWJicEOOX/dmVjfuBpK\r\nNcfpG38aEBq2PBXs16UZ6NoLa07m2aEytzWMzvZhHQeqyciBUaRDI0FeCj/c\r\nq0pj/yYIfP/YAmcOv7R0EZWRkVgBsV89U564X9T7IuN7AbrSajlQB5MW5Lm/\r\nQfqEu561+nqyaDEmZfGT5YHwiJH/0ZuYD235Lu4kKRaNJY9IPTsaTxIH7l5G\r\nW0YmHAu+1hRvCTWC3dn4xLqDwtUVsDT3ba0=\r\n=+yYu\r\n-----END PGP SIGNATURE-----\r\n"}}, "9.0.0": {"name": "uuid", "version": "9.0.0", "devDependencies": {"jest": "28.1.3", "husky": "8.0.1", "runmd": "1.3.6", "eslint": "8.21.0", "prettier": "2.7.1", "@babel/cli": "7.18.10", "@babel/core": "7.18.10", "bundlewatch": "0.3.3", "lint-staged": "13.0.3", "npm-run-all": "4.1.5", "random-seed": "0.3.0", "@commitlint/cli": "17.0.3", "standard-version": "9.5.0", "@babel/preset-env": "7.18.10", "eslint-plugin-node": "11.1.0", "@babel/eslint-parser": "7.18.9", "eslint-plugin-import": "2.26.0", "eslint-plugin-promise": "6.0.0", "eslint-config-prettier": "8.5.0", "eslint-config-standard": "17.0.0", "eslint-plugin-prettier": "4.2.1", "optional-dev-dependency": "2.0.1", "@commitlint/config-conventional": "17.0.3"}, "bin": {"uuid": "dist/bin/uuid"}, "dist": {"shasum": "592f550650024a38ceb0c562f2f6aa435761efb5", "tarball": "https://registry.npmjs.org/uuid/-/uuid-9.0.0.tgz", "fileCount": 76, "integrity": "sha512-MXcSTerfPa4uqyzStbRoTgt5XIe3x5+42+q1sDuy3R5MDk66URdLMOZe5aPX/SQd+kuYAh0FdP/pO28IkQyTeg==", "signatures": [{"sig": "MEYCIQDmKbDr6WecVwMEbEwyKBiUaPniMBvoQeVW4/TgnB4XzwIhAPnKsWjySkMnuquSAkRxJl8MsKPm3S8so3Z19XguF1NJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 122687, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjFlYqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo9Ig//av6W4HOYtB3r5tW8Ie10yCvrJIAs8DqTN0hTvmuDvepR+iQM\r\ngEd/T/yL1UoWLE789CMNbsq4rz/LAhDzES52rjevFhE4VPC9P1ZqMvwLnQnS\r\n4BSsDUoJ58VoyCH+oRRR5YiBu/BJBHfGsiAgaIrEyq4hliC9FXs5CZCebJpd\r\nU/E2JX/G9yF5OR/UO5vuHRX7AORQh6WTnsT6qaCJK325vzA8POvMR3TDowAF\r\nltyDq8nlYDeDLEh84Qvv7QSJuQAQ3i1qBVPoXyuTTKS6w8vl+m1wUrxkEblJ\r\nVlxdjGvwlgq7MFL/pkiKgtaaK8ccQcqHn8wEJjqR/i9OpQmC2jFr9qeVrG1A\r\naJy6N8OTSd1iI4P6AprE8VnhXj/OZnZUR6p9oDfwGZAuiMjK3pSvEKXiaQqz\r\n31VRwq2mp+7Kq4AnIbRBkFWKm3L3c9lTa1ltNAW02585x9jxLPABMxd7vLip\r\n4VANifKwtfuTVcEwDGO8kdFbyeuiNRxf8igL041KWbINzQRsb/ElZg3jk//u\r\nFjZBMviIf9CK1IOCO1gX4YoEj4pFvwGkuuFPL145jfyKiGr/4UMw2/OjCJOo\r\no35Xf4yc4YUloK4D3auxXtbTn8jKq8cIxsLo3pITudQc7zWaYs8HkrRca3A/\r\nbbcDTamFyh736XTsJR3P9Cb6BT3uGWJ0uPc=\r\n=Ju3Q\r\n-----END PGP SIGNATURE-----\r\n"}}, "9.0.1": {"name": "uuid", "version": "9.0.1", "devDependencies": {"jest": "28.1.3", "husky": "8.0.1", "runmd": "1.3.9", "eslint": "8.21.0", "prettier": "2.7.1", "@babel/cli": "7.18.10", "@babel/core": "7.18.10", "bundlewatch": "0.3.3", "lint-staged": "13.0.3", "npm-run-all": "4.1.5", "random-seed": "0.3.0", "@commitlint/cli": "17.0.3", "standard-version": "9.5.0", "@babel/preset-env": "7.18.10", "eslint-plugin-node": "11.1.0", "@babel/eslint-parser": "7.18.9", "eslint-plugin-import": "2.26.0", "eslint-plugin-promise": "6.0.0", "eslint-config-prettier": "8.5.0", "eslint-config-standard": "17.0.0", "eslint-plugin-prettier": "4.2.1", "optional-dev-dependency": "2.0.1", "@commitlint/config-conventional": "17.0.3"}, "bin": {"uuid": "dist/bin/uuid"}, "dist": {"shasum": "e188d4c8853cc722220392c424cd637f32293f30", "tarball": "https://registry.npmjs.org/uuid/-/uuid-9.0.1.tgz", "fileCount": 76, "integrity": "sha512-b+1eJOlsR9K8HJpow9Ok3fiWOWSIcIzXodvv0rQjVoOVNpWMpxf1wZNpt4y9h10odCNrqnYp1OBzRktckBe3sA==", "signatures": [{"sig": "MEYCIQDvscJxv7mXEiTE+Ykp+FxcDL4/XiNCg4qDG+EPH07gaQIhAPzoirDjELJCA3SqLrbV0nJFhheyCg+AGW6BJ2QolPKT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 123288}, "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"]}, "10.0.0": {"name": "uuid", "version": "10.0.0", "devDependencies": {"jest": "29.7.0", "husky": "9.0.11", "runmd": "1.3.9", "eslint": "9.4.0", "globals": "15.3.0", "prettier": "3.3.0", "@wdio/cli": "7.16.10", "@babel/cli": "7.24.6", "@babel/core": "7.24.6", "bundlewatch": "0.3.3", "lint-staged": "15.2.5", "neostandard": "0.5.1", "npm-run-all": "4.1.5", "random-seed": "0.3.0", "@commitlint/cli": "19.3.0", "standard-version": "9.5.0", "@babel/preset-env": "7.24.6", "@wdio/local-runner": "7.16.10", "@wdio/spec-reporter": "7.16.9", "@babel/eslint-parser": "7.24.6", "eslint-plugin-prettier": "5.1.3", "@wdio/jasmine-framework": "7.16.6", "optional-dev-dependency": "2.0.1", "@wdio/browserstack-service": "7.16.10", "@wdio/static-server-service": "7.16.6", "@commitlint/config-conventional": "19.2.2", "@babel/plugin-syntax-import-attributes": "7.24.6"}, "bin": {"uuid": "dist/bin/uuid"}, "dist": {"shasum": "5a95aa454e6e002725c79055fd42aaba30ca6294", "tarball": "https://registry.npmjs.org/uuid/-/uuid-10.0.0.tgz", "fileCount": 96, "integrity": "sha512-8XkAphELsDnEGrDxUOHB3RGvXz6TeuYSGEZBOjtTtPm2lwhGBjLgOzLHB63IUWfBpNucQjND6d3AOudO+H3RWQ==", "signatures": [{"sig": "MEYCIQCL55oFsbUUreujw9RQl+8DxybBLO+ccjIG4q3U5SV8ngIhAPV/TPJWjej3tuO1uwAw8Y/DWwpB/uu/aDvKTJWH/QUA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 168173}, "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"]}, "11.0.0-0": {"name": "uuid", "version": "11.0.0-0", "devDependencies": {"jest": "29.7.0", "husky": "9.1.1", "runmd": "1.3.9", "eslint": "9.7.0", "globals": "15.8.0", "prettier": "3.3.3", "@wdio/cli": "9.0.9", "@eslint/js": "9.7.0", "typescript": "5.5.3", "bundlewatch": "0.3.3", "lint-staged": "15.2.7", "neostandard": "0.11.1", "npm-run-all": "4.1.5", "@commitlint/cli": "19.3.0", "standard-version": "9.5.0", "@types/eslint__js": "8.42.3", "typescript-eslint": "8.0.0-alpha.30", "@wdio/local-runner": "9.0.9", "@wdio/spec-reporter": "9.0.8", "@babel/eslint-parser": "7.24.8", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.2.1", "@wdio/jasmine-framework": "9.0.9", "optional-dev-dependency": "2.0.1", "@wdio/browserstack-service": "9.0.9", "@wdio/static-server-service": "9.0.8", "@commitlint/config-conventional": "19.2.2"}, "bin": {"uuid": "dist/esm/bin/uuid"}, "dist": {"shasum": "b9d151889aa116aceaa21c1a1a5e0d44a7fcbccc", "tarball": "https://registry.npmjs.org/uuid/-/uuid-11.0.0-0.tgz", "fileCount": 281, "integrity": "sha512-gPhXpKFuxFX0BvpbLtzvYQf+aqKWDGL0mpjrIg6k/DgG/VrOdZ4+RbmSeP89UVLsgGxecQ2n7aE6OESwYYnCpg==", "signatures": [{"sig": "MEQCIG9krhIVIlTZAM4Q/Pxl0+PAxhUUbWG1s6akuBOFRI9zAiAOVUs0qUkJyIaA1zuGIVOjkdjgCdz1ir71sLs7CkSSMA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 650868}, "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"]}, "11.0.0": {"name": "uuid", "version": "11.0.0", "devDependencies": {"jest": "29.7.0", "husky": "9.1.6", "runmd": "1.3.9", "eslint": "9.13.0", "globals": "15.11.0", "prettier": "3.3.3", "@eslint/js": "9.13.0", "typescript": "5.6.3", "bundlewatch": "0.4.0", "lint-staged": "15.2.10", "neostandard": "0.11.7", "npm-run-all": "4.1.5", "release-please": "16.14.3", "@commitlint/cli": "19.5.0", "standard-version": "9.5.0", "@types/eslint__js": "8.42.3", "typescript-eslint": "8.11.0", "@babel/eslint-parser": "7.25.9", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.2.1", "optional-dev-dependency": "2.0.1", "@commitlint/config-conventional": "19.5.0"}, "bin": {"uuid": "dist/esm/bin/uuid"}, "dist": {"shasum": "01c1f5492ed10ad2c0fba1ae1f6d542e6b568d0c", "tarball": "https://registry.npmjs.org/uuid/-/uuid-11.0.0.tgz", "fileCount": 281, "integrity": "sha512-iE8Fa5fgBY4rN5GvNUJ8TSwO1QG7TzdPfhrJczf6XJ6mZUxh/GX433N70fCiJL9h8EKP5ayEIo0Q6EBQGWHFqA==", "signatures": [{"sig": "MEYCIQCWrOJWNatNXcL1pMUOznnkHDsGU1CRtzT36hSfM0rCVwIhAMDFbImN0yh7LmQzMyhwTfRdAAgreWUl2qtrBvuEjlEL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 667676}, "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"]}, "11.0.1": {"name": "uuid", "version": "11.0.1", "devDependencies": {"jest": "29.7.0", "husky": "9.1.6", "runmd": "1.3.9", "eslint": "9.13.0", "globals": "15.11.0", "prettier": "3.3.3", "@eslint/js": "9.13.0", "typescript": "5.6.3", "bundlewatch": "0.4.0", "lint-staged": "15.2.10", "neostandard": "0.11.7", "npm-run-all": "4.1.5", "release-please": "16.14.3", "@commitlint/cli": "19.5.0", "standard-version": "9.5.0", "@types/eslint__js": "8.42.3", "typescript-eslint": "8.11.0", "@babel/eslint-parser": "7.25.9", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.2.1", "optional-dev-dependency": "2.0.1", "@commitlint/config-conventional": "19.5.0"}, "bin": {"uuid": "dist/esm/bin/uuid"}, "dist": {"shasum": "a527e188c4c11a7ff5d139e59f229a9f90440669", "tarball": "https://registry.npmjs.org/uuid/-/uuid-11.0.1.tgz", "fileCount": 281, "integrity": "sha512-wt9UB5EcLhnboy1UvA1mvGPXkIIrHSu+3FmUksARfdVw9tuPf3CH/CohxO0Su1ApoKAeT6BVzAJIvjTuQVSmuQ==", "signatures": [{"sig": "MEUCIBRJbGueTPd1gtkU1IoPh1dEhkFvJvWNEVfHXseVOiulAiEAl4LyNbDMXqgaJmZynfQpT5txlVbn5fYtKCGMCaGKHy0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 650767}, "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"]}, "11.0.2": {"name": "uuid", "version": "11.0.2", "devDependencies": {"jest": "29.7.0", "husky": "9.1.6", "runmd": "1.3.9", "eslint": "9.13.0", "globals": "15.11.0", "prettier": "3.3.3", "@eslint/js": "9.13.0", "typescript": "5.6.3", "bundlewatch": "0.4.0", "lint-staged": "15.2.10", "neostandard": "0.11.7", "npm-run-all": "4.1.5", "release-please": "16.14.3", "@commitlint/cli": "19.5.0", "standard-version": "9.5.0", "@types/eslint__js": "8.42.3", "typescript-eslint": "8.11.0", "@babel/eslint-parser": "7.25.9", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.2.1", "optional-dev-dependency": "2.0.1", "@commitlint/config-conventional": "19.5.0"}, "bin": {"uuid": "dist/esm/bin/uuid"}, "dist": {"shasum": "a8d68ba7347d051e7ea716cc8dcbbab634d66875", "tarball": "https://registry.npmjs.org/uuid/-/uuid-11.0.2.tgz", "fileCount": 280, "integrity": "sha512-14FfcOJmqdjbBPdDjFQyk/SdT4NySW4eM0zcG+HqbHP5jzuH56xO3J1DGhgs/cEMCfwYi3HQI1gnTO62iaG+tQ==", "signatures": [{"sig": "MEYCIQDF728NgEqz+WQIJbzy741TEBqKjasQ9TbpEEOQahTgYQIhAIJTpCpkp5opLTipKoL1yFxnb2EHYn/lJ/EokNGg1jGf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 650519}, "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"]}, "11.0.3": {"name": "uuid", "version": "11.0.3", "devDependencies": {"jest": "29.7.0", "husky": "9.1.6", "runmd": "1.3.9", "eslint": "9.13.0", "globals": "15.11.0", "prettier": "3.3.3", "commander": "12.1.0", "@eslint/js": "9.13.0", "typescript": "5.6.3", "bundlewatch": "0.4.0", "lint-staged": "15.2.10", "neostandard": "0.11.7", "npm-run-all": "4.1.5", "release-please": "16.14.3", "@commitlint/cli": "19.5.0", "standard-version": "9.5.0", "@types/eslint__js": "8.42.3", "typescript-eslint": "8.11.0", "@babel/eslint-parser": "7.25.9", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.2.1", "@commitlint/config-conventional": "19.5.0"}, "bin": {"uuid": "dist/esm/bin/uuid"}, "dist": {"shasum": "248451cac9d1a4a4128033e765d137e2b2c49a3d", "tarball": "https://registry.npmjs.org/uuid/-/uuid-11.0.3.tgz", "fileCount": 280, "integrity": "sha512-d0z310fCWv5dJwnX1Y/MncBAqGMKEzlBb1AOf7z9K8ALnd0utBX/msg/fA0+sbyN1ihbMsLhrBlnl1ak7Wa0rg==", "signatures": [{"sig": "MEUCIQDlhgS+bpevg+LIumEE6aJc+nlUQbPIQoXIIaxgS1W0FgIgaiuAjF9FIkiT1PKAYtCESbbz2IsTcMH2F98bsW4F/PE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 293890}, "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"]}, "11.0.4": {"name": "uuid", "version": "11.0.4", "devDependencies": {"jest": "29.7.0", "husky": "9.1.7", "runmd": "1.4.1", "eslint": "9.17.0", "globals": "15.14.0", "prettier": "3.4.2", "commander": "12.1.0", "@eslint/js": "9.17.0", "typescript": "5.7.2", "bundlewatch": "0.4.0", "lint-staged": "15.2.11", "neostandard": "0.12.0", "npm-run-all": "4.1.5", "release-please": "16.15.0", "@commitlint/cli": "19.6.1", "standard-version": "9.5.0", "@types/eslint__js": "8.42.3", "typescript-eslint": "8.18.2", "@babel/eslint-parser": "7.25.9", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.2.1", "@commitlint/config-conventional": "19.6.0"}, "bin": {"uuid": "dist/esm/bin/uuid"}, "dist": {"shasum": "37943977894ef806d2919a7ca3f89d6e23c60bac", "tarball": "https://registry.npmjs.org/uuid/-/uuid-11.0.4.tgz", "fileCount": 190, "integrity": "sha512-IzL6VtTTYcAhA/oghbFJ1Dkmqev+FpQWnCBaKq/gUluLxliWvO8DPFWfIviRmYbtaavtSQe4WBL++rFjdcGWEg==", "signatures": [{"sig": "MEYCIQD73ZGLsRD8FEQs09uxY7bdiLKinaBl8TpN1QaBqDH6ywIhANBgX8TG4FpbgQK3LTRin7rhkx/XWDeFbxeetZ5P2DXC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 131611}, "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"]}, "11.0.5": {"name": "uuid", "version": "11.0.5", "devDependencies": {"jest": "29.7.0", "husky": "9.1.7", "runmd": "1.4.1", "eslint": "9.17.0", "globals": "15.14.0", "prettier": "3.4.2", "commander": "12.1.0", "@eslint/js": "9.17.0", "typescript": "5.0.4", "bundlewatch": "0.4.0", "lint-staged": "15.2.11", "neostandard": "0.12.0", "npm-run-all": "4.1.5", "release-please": "16.15.0", "@commitlint/cli": "19.6.1", "standard-version": "9.5.0", "@types/eslint__js": "8.42.3", "typescript-eslint": "8.18.2", "@babel/eslint-parser": "7.25.9", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.2.1", "@commitlint/config-conventional": "19.6.0"}, "bin": {"uuid": "dist/esm/bin/uuid"}, "dist": {"shasum": "07b46bdfa6310c92c3fb3953a8720f170427fc62", "tarball": "https://registry.npmjs.org/uuid/-/uuid-11.0.5.tgz", "fileCount": 190, "integrity": "sha512-508e6IcKLrhxKdBbcA2b4KQZlLVp2+J5UwQ6F7Drckkc5N9ZJwFa4TgWtsww9UG8fGHbm6gbV19TdM5pQ4GaIA==", "signatures": [{"sig": "MEUCIQDlJZBK51EKTUbNwOmydZJHMg+rMDbwD5oTQxWZu4nXTAIgdhSo9LABv0rnkX14aqTWCnDrPGbUAFacYTf91fkNuVs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 131959}, "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"]}, "11.1.0": {"name": "uuid", "version": "11.1.0", "devDependencies": {"@babel/eslint-parser": "7.25.9", "@commitlint/cli": "19.6.1", "@commitlint/config-conventional": "19.6.0", "@eslint/js": "9.17.0", "@types/eslint__js": "8.42.3", "bundlewatch": "0.4.0", "commander": "12.1.0", "eslint": "9.17.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.2.1", "globals": "15.14.0", "husky": "9.1.7", "jest": "29.7.0", "lint-staged": "15.2.11", "neostandard": "0.12.0", "npm-run-all": "4.1.5", "prettier": "3.4.2", "release-please": "16.15.0", "runmd": "1.4.1", "standard-version": "9.5.0", "typescript": "5.0.4", "typescript-eslint": "8.18.2"}, "bin": {"uuid": "dist/esm/bin/uuid"}, "dist": {"integrity": "sha512-0/A9rDy9P7cJ+8w1c9WD9V//9Wj15Ce2MPz8Ri6032usz+NfePxx5AcN3bN+r6ZL6jEo066/yNYB3tn4pQEx+A==", "shasum": "9549028be1753bb934fc96e2bca09bb4105ae912", "tarball": "https://registry.npmjs.org/uuid/-/uuid-11.1.0.tgz", "fileCount": 190, "unpackedSize": 132894, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIAwmSSeNa1LZOnX1xHZTmKjAbNtJrVs4X6AGfFtdU6TgAiEA0QQOK2FF4KY5Qv7/xBIx19YeJ8hWbpvdw1M3Q8YSROw="}]}, "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"]}}, "modified": "2025-02-19T18:16:11.787Z"}