{"name": "call-bind-apply-helpers", "dist-tags": {"latest": "1.0.2"}, "versions": {"1.0.0": {"name": "call-bind-apply-helpers", "version": "1.0.0", "dependencies": {"es-errors": "^1.3.0", "function-bind": "^1.1.2"}, "devDependencies": {"nyc": "^10.3.2", "tape": "^5.9.0", "eslint": "=8.8.0", "evalmd": "^0.0.19", "encoding": "^0.1.13", "for-each": "^0.3.3", "npmignore": "^0.3.1", "in-publish": "^2.0.1", "typescript": "next", "@types/tape": "^5.6.5", "auto-changelog": "^2.5.0", "object-inspect": "^1.13.3", "@types/for-each": "^0.3.3", "has-strict-mode": "^1.0.1", "@ljharb/tsconfig": "^0.2.2", "es-value-fixtures": "^1.5.0", "safe-publish-latest": "^2.0.0", "@types/function-bind": "^1.1.10", "@arethetypeswrong/cli": "^0.17.0", "@ljharb/eslint-config": "^21.1.1", "@types/object-inspect": "^1.13.0"}, "dist": {"shasum": "33127b42608972f76812a501d69db5d8ce404979", "tarball": "https://registry.npmjs.org/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.0.tgz", "fileCount": 21, "integrity": "sha512-CCKAP2tkPau7D3GE8+V8R6sQubA9R5foIzGp+85EXCVSCivuxBNAWqcpn72PKYiIcqoViv/kcUDpaEIMBVi1lQ==", "signatures": [{"sig": "MEQCIHW7XfuHryTs2kNK+/GrAusDSttnxXzy+no1dG9KBiKAAiB8tBUgklhwfVMKrAPde2dlg/AhkIqh+K386M5Ic17Wvg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13840}, "engines": {"node": ">= 0.4"}}, "1.0.1": {"name": "call-bind-apply-helpers", "version": "1.0.1", "dependencies": {"es-errors": "^1.3.0", "function-bind": "^1.1.2"}, "devDependencies": {"nyc": "^10.3.2", "tape": "^5.9.0", "eslint": "=8.8.0", "evalmd": "^0.0.19", "encoding": "^0.1.13", "for-each": "^0.3.3", "npmignore": "^0.3.1", "in-publish": "^2.0.1", "typescript": "next", "@types/tape": "^5.6.5", "auto-changelog": "^2.5.0", "object-inspect": "^1.13.3", "@types/for-each": "^0.3.3", "has-strict-mode": "^1.0.1", "@ljharb/tsconfig": "^0.2.2", "es-value-fixtures": "^1.5.0", "safe-publish-latest": "^2.0.0", "@types/function-bind": "^1.1.10", "@arethetypeswrong/cli": "^0.17.1", "@ljharb/eslint-config": "^21.1.1", "@types/object-inspect": "^1.13.0"}, "dist": {"shasum": "32e5892e6361b29b0b545ba6f7763378daca2840", "tarball": "https://registry.npmjs.org/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.1.tgz", "fileCount": 21, "integrity": "sha512-BhYE+WDaywFg2TBWYNXAE+8B1ATnThNBqXHP5nQu0jWJdVvY2hvkpyB3qOmtmDePiS5/BDQ8wASEWGMWRG148g==", "signatures": [{"sig": "MEUCIHwPEWbTRvp20B4Sr47T95aJCDYbhRkpr46kXbWVYCVHAiEAsKrisdtgDsJoWqsHHKVWnVqpGu9V2Yv1yhQZSA920tM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14495}, "engines": {"node": ">= 0.4"}}, "1.0.2": {"name": "call-bind-apply-helpers", "version": "1.0.2", "dependencies": {"es-errors": "^1.3.0", "function-bind": "^1.1.2"}, "devDependencies": {"@arethetypeswrong/cli": "^0.17.3", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.3", "@types/for-each": "^0.3.3", "@types/function-bind": "^1.1.10", "@types/object-inspect": "^1.13.0", "@types/tape": "^5.8.1", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "es-value-fixtures": "^1.7.1", "eslint": "=8.8.0", "evalmd": "^0.0.19", "for-each": "^0.3.5", "has-strict-mode": "^1.1.0", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.4", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "dist": {"integrity": "sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==", "shasum": "4b5428c222be985d79c3d82657479dbe0b59b2d6", "tarball": "https://registry.npmjs.org/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz", "fileCount": 21, "unpackedSize": 15952, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIG6fAxtBxkVLqeMj4hMX04Vep0JNjOVl6JP2xrnsL50CAiEA9XubTWvgXSyVHKqfH6C/PHeZ06C6S+wNdxbVqOFCR6I="}]}, "engines": {"node": ">= 0.4"}}}, "modified": "2025-02-12T19:24:57.167Z"}