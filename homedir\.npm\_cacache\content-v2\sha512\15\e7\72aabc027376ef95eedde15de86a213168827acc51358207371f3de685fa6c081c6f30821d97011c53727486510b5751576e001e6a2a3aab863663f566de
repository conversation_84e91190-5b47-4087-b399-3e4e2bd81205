{"_id": "lightningcss-linux-arm64-gnu", "_rev": "37-a3f03542b8431c5ca749368baec79ddb", "name": "lightningcss-linux-arm64-gnu", "dist-tags": {"latest": "1.30.1"}, "versions": {"1.14.0": {"name": "lightningcss-linux-arm64-gnu", "version": "1.14.0", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-gnu@1.14.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "c9778570ffacc70c0b7f08910dbceb3ca80a477a", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-gnu/-/lightningcss-linux-arm64-gnu-1.14.0.tgz", "fileCount": 3, "integrity": "sha512-88NmNwTRa10MRDJa2DghZuGZq8rvFeZIm0G4k/oA2P5XaJJw7f6IhDEjCtBoruYOZhulBkEq2xQY+q0AWYMFcw==", "signatures": [{"sig": "MEQCIDNDeSjGC0QeTc+918gexRRWvbqasx8mImo+y4mehXFXAiAEtj694ys3RE4DdJ72gy2HEVQYrgJfWddPRZ2cFJVeHA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3014993, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjGXtNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmphJA/+OraRc9EWL8girlPk37HkNapaVELgwbvEMK3uw8hCd9pDYk5q\r\nYfr5sSjAnSV0i5X0VETjfV2XG+H++JHNlfrxwLCRhmQT42tc8A6fbHMWlEgU\r\nFtsrOr91aDQJrt2tzpFIJ1MIxEvToFvXVDaMAY530/BB9suqxDMUtlXIdH8I\r\nJuaUS1V6fSK1YYsD4AOo4yLjfftGz60yn+hb611ANDjS4AZ+qv2cCmrJe<PERSON>zZ\r\njgwzHgGRMccHqSmIZMSsZSWcr8u/Kkz5OZlDu3cNXFnOc+hvuM8i3EWv61Ir\r\nfZh0/OxljWut+vcvIrOWTeihyXHiSs/IwqFExt5nG7vGIRXX7MKjA/Z8eNBD\r\nYAGS95/oXgHOZ4r2lFwRkxY8XwUulNga/W8+ck5YG0N/dQS9I7sZIFRRVstp\r\nbH1dhdaPOMtQ9GQPWokWxw0T+za+GM8PAk8TJ6ZtOLSCGEtnE92qNAN3GFiR\r\n+EUTSJPcxLDnmkT7Aalz8c+j6vFM6Lj8R2mx1LeYARkouLLBrXbEfnNQ+s+V\r\nfapxsoNj8iw76Dk3yV6xbUyIV22T+fLUJpsYcwaPj3EEDWWc+KbgwQJo2Nmv\r\na/op1Hij3Akq0agF0MGN/bJU6JheOBendzXrfSou98LaaZVCbjT3VEfim1g8\r\n1z/eGQorTLVHQ5c/LDpk2TwwtYimp9pbSVg=\r\n=7Xgu\r\n-----END PGP SIGNATURE-----\r\n"}, "libc": ["glibc"], "main": "lightningcss.linux-arm64-gnu.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "2d17e0e1ed1482525985ef4d30809b4c06d76944", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "_nodeVersion": "16.17.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-gnu_1.14.0_1662614348781_0.8409406539524069", "host": "s3://npm-registry-packages"}}, "1.15.0": {"name": "lightningcss-linux-arm64-gnu", "version": "1.15.0", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-gnu@1.15.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "49919ba643e896a4a9acdc29dfd9efe3f54fe6c9", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-gnu/-/lightningcss-linux-arm64-gnu-1.15.0.tgz", "fileCount": 3, "integrity": "sha512-cVpXV5UUsl82uFFp5NUxXtu7eTAkxA1XIK6JLN4CbJKdIVinympA9/yWDzWmGs4Nu39im3clWD0n+1jwE0b4lQ==", "signatures": [{"sig": "MEQCIHiin7I/S5UaWYj33GcdNtoNOfWplZvzlCRztZ6kelBYAiAvDgmX1gPD7mhE9eBuQHWc7fGmwTNiVWFbACjtFcyxHw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3019136, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjIqcbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp1oxAAj81ijf9/yLL50E8XS+EpaEbcklrlylc2vXEzg5ubhNicL6Nb\r\nLY/K8ZDk/AwmXCXIblr+HWt+kNA8HkUVvb2eqkQMutzVAXZYTrJtuSDR12S3\r\no7IAgDDy6++QVGEhNPznMtHgPjW/cy82rEPjhvRpHwgXOWro+Kr8kIztkWW5\r\nIm4EIUBF6pX6A/0Z+wxLEGcsfOBYvr6vLWBIyX1KwPT8MDnZZAOZgUkT3AKr\r\nav5O3T11sw28A2Il1he62kwhsmLfET0kLbsOcI+dQA1CWPmoq61gw3p9NNVG\r\n8OotS3+hJfYto4umorFYWgrNokQm9MoTxFDEzp1jOrG9aujlFdvXmtwT2efY\r\nj2bVV3yBLJ+mnsRPGXw3hlKh3V50UWpbwC+blro/nNRoKajZ1PuECth0Q/zM\r\nsvXSS3zKGReQONIu4nwQKY0A6+FXWCm1xzmkPARTk1nflBBSyusGKv9Z2USn\r\nAjmObvT2yJM2p8N1BcSUE42Rz5eaJSUvXEL7XUFCf4EAQfTnIAEEPTa0EDlN\r\ne1Otszfm8bUMDjpGoAQ0V40FGa7Pg+UBHbpLczlomNba776TO7Hjx8bh9GXd\r\nANZDh5Q+cJmLN5PT+wlw92krXNuxSOKPL4cPt5r1tiEmFBwx/90LNrh8QS4q\r\nW183lgNYguaNKe+K7H50OWMiznOFv0LT4u8=\r\n=GIhv\r\n-----END PGP SIGNATURE-----\r\n"}, "libc": ["glibc"], "main": "lightningcss.linux-arm64-gnu.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "1a23835a0d72afc06ab5f98cbbee5fb03ae09074", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "_nodeVersion": "16.17.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-gnu_1.15.0_1663215387608_0.8190547221032507", "host": "s3://npm-registry-packages"}}, "1.15.1": {"name": "lightningcss-linux-arm64-gnu", "version": "1.15.1", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-gnu@1.15.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "2a674bf9b1aef16a39ae49a3dc7d132a0904206e", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-gnu/-/lightningcss-linux-arm64-gnu-1.15.1.tgz", "fileCount": 3, "integrity": "sha512-ZhCv3MlApRTIwszlNQ2t7FST7qK+M1iP6apTvOetPwDlzOMZ5dcH0a1453JPm4CwOSzHZ8079gnb5EqtU4+pjg==", "signatures": [{"sig": "MEUCIAu6uAwHBqDin37mfKfIP9MXZHlojsnozwQsX2BfgnrbAiEAx0Gq2n3quuc7vxsQQNtTfo0A8zrETbN4giewcctFXmI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3019136, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjI/I+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrQ5w/7BRZ0uHF9FfqwzP7FZPSJY0LgPtauid4W5ABBwKt3d2AoHZoL\r\nO9Cq3gfHXlTC5QjUjYJmr+OY01CGOdxoTVraLp57EgoGFp7tWsqMF05/1dwy\r\neJE5b0Ka4s/01lAbw2m4yXfb9AWbZgsOgLP7LlB3QqzK1aHHQwLnb36BhcYB\r\nkJFUcYSozQP4/EGEVYVvASbQ1MGRNoKPJGZJ/T6GV5VRRCZza1KA46z7Skg7\r\nEfUg4cSTz4DZ6JtSA7pepbbkvzBWeMZecifo3+b3wLs96Ya7yaLxEptllAIH\r\nIlw+gGfRJlIwzPD49pLQz7v5MQwbv7nypU4ZweSmp0c40T0+J8nQ/5U3um8q\r\nR5kGLxn9DPBzGtDj0uQwhFwtANvlxa3QD8cADpyPyrNdgGTyTIt+mapf8hwT\r\npRiJ9qAxUKfDY3nv9IwCGW5gletF+XBoko1+f7pl6F9fnfJY3K1bXzyVd027\r\nty9UVhNE3wyWxYPJe2lh0/Sw172dQ1nRPDI0a0mdArWzMkc/qJV33UdRgO9e\r\nRGhQy5Q7OATqcwsOoL9m1dyCNyx+P1aXOLb6OVE7ojLPbf2kmVQgVh/cS7XT\r\nigj7NzJ+uilwd9JoRAMcYtOItE1KT8u2uClhKsVEqvnaM0GiQWu/5zp6SqYn\r\neP0nsMAXcXnblLaJGyDN5c7Hwhs+mJcLZp4=\r\n=Y7Cb\r\n-----END PGP SIGNATURE-----\r\n"}, "libc": ["glibc"], "main": "lightningcss.linux-arm64-gnu.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "eb66ece2f9b47bf4ef60848736dde697ec80a319", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "_nodeVersion": "16.17.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-gnu_1.15.1_1663300157985_0.4548612508472991", "host": "s3://npm-registry-packages"}}, "1.16.0": {"name": "lightningcss-linux-arm64-gnu", "version": "1.16.0", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-gnu@1.16.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "21d139f5201c9b8bb3972c24116a5bcbb7e2c3b5", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-gnu/-/lightningcss-linux-arm64-gnu-1.16.0.tgz", "fileCount": 3, "integrity": "sha512-Drq9BSVIvmV9zsDJbCZWCulMvKMQWFIlYXPCKV/iwRj+ZAJ1BRngma0cNHB6uW7Wac8Jg04CJN5IA4ELE3J+cQ==", "signatures": [{"sig": "MEUCIQCNtlE6jT4T+ej9Q6ciOmCXIPwdwV3dDd6VYtHd0veGMwIgRVdhDPMLQp9qKTAR4F/MC7kTpsRsm0VjVjDTnyYxpR8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3772800, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjKTIdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrR6A//SSfVhEnVTAjS3b1prbtv+Hhe3U/9BeZ13BzPhZNEL3yu+Qw4\r\nUxj5Pw3Hzg3bX4eNgvWGKnA7EIxtbl614euRtV037Bf8sDfQ4rBD4owXMw5U\r\n15F2XxKW5AKnaJYmfscl+SHe90M2Ez0ddr3okRhpoQUNUL8FcIA2Iw3gwGuY\r\nvdMPLlIsTlZXhoodA31eg0bQZXQtIIC+ZA+LRUOLvchnSTWm5hDSpajowlfE\r\nHGNKfIyBM774yZ7+XZMPiidYWKhcEMyr01FyqkMJmkHGS50OJS48vJKdDNmY\r\ntWCHTzGacftmG1HmMpz/hjERK/vrmnpC5pijcaYdlNqjYRpyQqrFspeom2k2\r\nN3n5673MN+LyFli8JyfIhi7se/dSJ/DX3+o66vDxS7SM3CbLlNDe/Ucz+HwW\r\nL84bbKQgFNOxvvM/7nvIKVQmwf1qCYhf7uf4Rw2cpL5YWJiFDRMyCNKe1YIJ\r\nDReqdigS8pF38cCKi8+B8+u7cbgLUgSLZP6B3ycVGW4wovhbg3stjJwB2hkS\r\nzOIxpNKQztNWWjQeBpYIbR08M+VF3FXyxtDbyUYBwam0a3ir9k4H1piqEnm2\r\niAWPFGO2nhxCvnBOCtpVlYH5GkowV0Zahov/FWnskxwXGXh7UnNxY560/qbM\r\nD53JMauBTTJ4KMD8ydShOWSL1MXjDdjjB+M=\r\n=iSLy\r\n-----END PGP SIGNATURE-----\r\n"}, "libc": ["glibc"], "main": "lightningcss.linux-arm64-gnu.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "200120e24aa31449703707db14ffef1bf40a7fea", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "_nodeVersion": "16.17.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-gnu_1.16.0_1663644189456_0.5499002704752289", "host": "s3://npm-registry-packages"}}, "1.16.1": {"name": "lightningcss-linux-arm64-gnu", "version": "1.16.1", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-gnu@1.16.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "b6986324d21de3813b84432b51c3e7b019dd2224", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-gnu/-/lightningcss-linux-arm64-gnu-1.16.1.tgz", "fileCount": 3, "integrity": "sha512-NqxYXsRvI3/Fb9AQLXKrYsU0Q61LqKz5It+Es9gidsfcw1lamny4lmlUgO3quisivkaLCxEkogaizcU6QeZeWQ==", "signatures": [{"sig": "MEYCIQDvMeF8RcY3yGz0fqeWiPAH6invDlVE6IprathMSqBRtQIhAIl+yi7dbZeA++/msrnNlum8lRglMKzH2Bvo/sC69Qfh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3686744, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjZ/aaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpZ9w//VgThjPNH0REPMMxuhlFKGz+0HFpwJM2DagjJlksD0gbTgA3l\r\n8EDT2KWhoTSjI+puA5IHXXgPEhMcUCygxihGxPe2os36GO2Q67mQwOfqf/VT\r\n4o5DJmlveKeqzBQT3ntTUIlX1a4GWhH539SZyDXi1zPAJZpSx1ENwrBxnvWI\r\ni/HP2Uw4NiAWL5/SqubGglhZ2MfjoT5YmHXKcZVwpPNOcC9R189HtNy7ALGj\r\nYPJgIjBqr/yC5Wi8rs45laSvRnmzC+kV7LS0PrfEAT9C7dtrg8u0kjrsSwkP\r\nHukG6ZGhPuXylVRrymJ/ocvj2C6EXx7U7DyhtWg8hNg2j3Jn2MFfckJV0o+J\r\nfdlnhFxIbfFpfmSRpA6FjajjfRDDghrUgPWncHvsU5vb+Dfpky8jv0RPjQvu\r\n4CPbCXVHeQMVKdoPfAGHkTTyW/b6lFaOwUqJNDAbno+zqAhtnBOECxvs71Wr\r\nBGc/GvMsO00N4Zq2v8dN2AtYvo7PlKAA7mOwHasHAvKSolzonmXQciiQjm1H\r\nS7MHDGVreX1TtExRtkaFLBXegDWHb5TO4bYkNnAFz9g4zd7d7sF3z1ioEnI1\r\nVa8fMgA6+IaPADSGJbP2pRU+izBEs2CL5Vw2/6k1k4soi6Rhi7ljuTn/uuB+\r\nnqEsAwXPqruWgq0uYQh5Z0sPuYxW/g1Lkc4=\r\n=fPdi\r\n-----END PGP SIGNATURE-----\r\n"}, "libc": ["glibc"], "main": "lightningcss.linux-arm64-gnu.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "87e808fd771374ae705886351ee62c4fac3d3630", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "_nodeVersion": "16.18.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-gnu_1.16.1_1667757722390_0.20981681424479293", "host": "s3://npm-registry-packages"}}, "1.17.0": {"name": "lightningcss-linux-arm64-gnu", "version": "1.17.0", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-gnu@1.17.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "c36bca7226e1857ce21adac393051429401fc6cc", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-gnu/-/lightningcss-linux-arm64-gnu-1.17.0.tgz", "fileCount": 3, "integrity": "sha512-tLX4TtLg5h8RQjZuB8qXJ9bi2i16e70E+hKXCAV84KgjcAkjcBq1f9xuo5NNg7/+gyLvzdpUEOtmMd5FK5dMqQ==", "signatures": [{"sig": "MEUCIGszyT09jkXw+NwhXz+19Sh9XRkCOpv6h3C4uz5YxxgsAiEAtGAn/6ScCbyMnvaMxuA1A6DJLi6jnGcC6sG+aJ624JE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4661592, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjhjexACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrAcw/+P7XT4IPYuVUSamB/8oSFPRbRXyXwNyZ0uyZ0H6ARiJmoCiUq\r\nW1WqFCWf+/JVhm+A56hcQ62bfqnYgSEJxx+Sm+qrOnkEeQWmSsoKjxXW433E\r\nRfR3+DDX3H/yz0ObqhyemTxrndZzO6EE+JmTDPy/Je/v2gbiF5q7B1nWD2qa\r\n70B7aoMG59rHowH2Qumf8A/wt8qVbTl1XabWwpVuvmnh83UbtufIPoTes2w+\r\nSxnAfXuM9Zdu5IivEms/bVof+hrY/bGOEn8hjVe8hzVq6XK6b++MwV/pxjd3\r\nb7N9PzM14yt0yf6hfc/HpmFcLuChFpqcD0NlbzCwlPXjlr387rEagzLIlsSw\r\nhCemOtifCO2xaxRjz4/WQtYfbqx9kQR+HMkGD2lao3HmI5bT4UeSvl3xexXv\r\n4B6iiJmsOJJT+QUrn1WBDSXSxQRVd08q8tmWEmUGEgvA9R/N5jI2YEDCqujf\r\ngFhuZMwBnE/M2KcnnpB9H84hcEfXad8ILO4opY7O1tpHmgKvFAQx0/gYmRNy\r\n11jspWjIiM+sNse9VKDOkrsJGZIDoUFpHbP1M/rLo4Gv5sBwRaf9EvGVqGN8\r\n7gbmAO3NSBWvDJ4OV1fDTGl0GKpetf3T3YjBSsEka8+A1tPDRoLjkOJLRQdk\r\nD4PgnwDUt6vtmgsCN22XWxl+fjEFt5COtzs=\r\n=39v2\r\n-----END PGP SIGNATURE-----\r\n"}, "libc": ["glibc"], "main": "lightningcss.linux-arm64-gnu.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "d4853b204cc80b051d29be324e2d16f621e76336", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "_nodeVersion": "16.18.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-gnu_1.17.0_1669740465012_0.5741321788677516", "host": "s3://npm-registry-packages"}}, "1.17.1": {"name": "lightningcss-linux-arm64-gnu", "version": "1.17.1", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-gnu@1.17.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "aeee6b5ed613198aab978c878f26110d6e8e70d2", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-gnu/-/lightningcss-linux-arm64-gnu-1.17.1.tgz", "fileCount": 3, "integrity": "sha512-/1XaH2cOjDt+ivmgfmVFUYCA0MtfNWwtC4P8qVi53zEQ7P8euyyZ1ynykZOyKXW9Q0DzrwcLTh6+hxVLcbtGBg==", "signatures": [{"sig": "MEUCIQCCuiDl9QyC1lm0v3bgUnnWOsZErcN+FjbCrMRanNRo9AIgaSdNb6lGa+jw5qxNQFDUy4Qr50pMgt5wzM8ybjRoSJA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4661592, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjh5M5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqrAQ/+PMA+nWzwoqjUILCodoJm++yUiZzzQHWEhcj/DttO9DtslfSF\r\ngEOqfCEF0TSediTH4LCleX05w0YB1Aa2lI1aqMUr1xoxhb0DXmy1SQo6Zesc\r\nOQwgxrPUM7w1zRkiSgjKK5Ibv20V8JosZyrbVzoVhgPwTjOpoYm9x4pVG/Qp\r\nnK8pr8gdh642lpv5b2Tb8Uhx4M/Gl5EAPOFT8VnvQm+Qn2yzOTAeaJ0lth3q\r\nckvf4Lrcq1lNAHTrXAVI4WjJbjFKxBQZXOZtR7fawT2X68N3NS6f6l+dNae+\r\nPQwV6jd9PBHFGluQox0gCW97h1WNq8jma3ea9ORXv+Akv/6FOsjW0dMF0Jw/\r\nG2X0r14BIwgUmijE84j1drH5cVai2W5XqQ++e/gxaK147E4ZQJR2RrClJVJP\r\n0I8ghAFajJ/xZc+2EEItPlsOAH7gC4f3mopS0RW4kNufVvja3fIkwnlPdbei\r\nXhwySc/R2JcTjf4r8+bU5s7cdv23bl+xzqcls4GOdS93jq4JUo4RsBcCQm2F\r\nOvpUrcWI3cLVrJZy0d+x2Z/ibThAtxtJp9kPXoOTidLcUx6d+gvKObXREqor\r\nEgrBBI4fGtZbyb3nvBN3RC+mCsxyT1BHiKnhfzOsUGMCuInydYm3nAd+Qy9y\r\nMGDVJNfNlE67avTxJIUW7f3ymgZItS9lLZs=\r\n=deVG\r\n-----END PGP SIGNATURE-----\r\n"}, "libc": ["glibc"], "main": "lightningcss.linux-arm64-gnu.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "27cb9c2a382fd8d9702de8def9dd18db54280c43", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "_nodeVersion": "16.18.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-gnu_1.17.1_1669829432914_0.7470851633359832", "host": "s3://npm-registry-packages"}}, "1.18.0": {"name": "lightningcss-linux-arm64-gnu", "version": "1.18.0", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-gnu@1.18.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "6c8e0a6e2c8b44cf180f3a0f0740402e8f656155", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-gnu/-/lightningcss-linux-arm64-gnu-1.18.0.tgz", "fileCount": 3, "integrity": "sha512-JSqh4+21dCgBecIQUet35dtE4PhhSEMyqe3y0ZNQrAJQ5kyUPSQHiw81WXnPJcOSTTpG0TyMLiC8K//+BsFGQA==", "signatures": [{"sig": "MEYCIQD/D0glGilVLkSjrb4h092/PMVs6BAZONXwnqR3TtrjMQIhALULrKMcHA1VuN/yyw3t9nBNLfG7VB61LvLsMWmXiTF4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7781973, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjtbCgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo0qQ//WMT+lb5qUbVpafdLCHGhVD+Iipht+Wfs6h2O/3ig9QcPi4zI\r\nV6K6GUg9K983nk1hRY81xhkQVLgh8YPDdFhUMnCuuAeKWFpTatd5v6yXV1Ut\r\n3ni83QdYRsjEJOyZTdwFxBd0X4v/ENHTjLEzYh+bQ6cA9Iq+v3a+AJoo+m24\r\nTWtqRG+voKJdD9LkYE+hHLgwGi/zopqxeK3yZpNjnHJ1qaoaKuMqw181krH1\r\nJAuu79gYWIxLtjrmEBhktpFO2Wn77+G20/slIykqwpUMDlWU2KPxRRz4l4Ji\r\nb0ujrtVg2XobF7kRroZRXMO2lwf/wav/HN1DIijEW8G6oJ4SP7Z7LmJuaAoS\r\n48eYOrCQitxpkfaJDnBmh7LnywMLzk9HZTdtlGtgjJIHM9LQhdqnFLKagzox\r\nBUWW+jxHgDdM2/HBDPuglxYS9kvDt+ZL6znLUG559VgzHR+1qw8kqIPOY/7Z\r\nsbPRSQqrHDax7VWXi46uezdxy1jsfQncMljm11qdAi+uqP9NPSkSbuld2gP+\r\n36ZHo7p/+aMgCXE0awWjfQJ/pj0oMbwBVb3jHOw3nrCS6llN2WfXIIfUmREU\r\nBydx93IMnNvQVZmgWT92NKVBd8ROtsmzMCEZtCwfM/UZR1UeqzibMSwElr6v\r\n6I4BADSvljd7ZoS/caCyUIGpn5dgKvnFQGo=\r\n=pJLh\r\n-----END PGP SIGNATURE-----\r\n"}, "libc": ["glibc"], "main": "lightningcss.linux-arm64-gnu.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "6fb77292c9ef4320fa85a1e26fec3fbdbae3cf8a", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "16.18.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-gnu_1.18.0_1672851616530_0.7926529329496421", "host": "s3://npm-registry-packages"}}, "1.19.0": {"name": "lightningcss-linux-arm64-gnu", "version": "1.19.0", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-gnu@1.19.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "027f9df9c7f4ffa127c37a71726245a5794d7ba2", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-gnu/-/lightningcss-linux-arm64-gnu-1.19.0.tgz", "fileCount": 4, "integrity": "sha512-zwXRjWqpev8wqO0sv0M1aM1PpjHz6RVIsBcxKszIG83Befuh4yNysjgHVplF9RTU7eozGe3Ts7r6we1+Qkqsww==", "signatures": [{"sig": "MEUCICMYw+nzlwJqV5dYdM9m5xAMVGN6UMj/hj1hnl272NrWAiEA6O5iec1+5sogANPQGqQDqTP+/m/j9/1/QGhYVCBOSmI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7367581, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj6l5RACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqLChAAggH0+NW4es+DjFZwTdy59QagyzmzA4DsuliMbH0/tknHJ91c\r\nqoksdjosKiTOcoDansoPGeG3qQgbdiUvxUZM592KbY23bJYpARBcKxZf0SUL\r\nOskt4ANcIYxOmBsSQI1+jH5WaGGr5ISMOtF6i7bRzqwDtup37/13yIUSsahL\r\nzw1b5L8/gwbcuCwlN6g4kC0zxG4bYmfOEjL2zEUnYmvxn/FSjsOWfKq6w4UI\r\nA0aHjUDS0E9CINqcrle33y88CqJlxGChGKYDBLSlW4yJ6wGWt1QR0cfzAt+F\r\nnW2YChGqUNthZCYd/oO7sY9BzmPY9sdvkarZHYuSBQP/TTkJp9urFT0zHXRq\r\nPaTbe89MDc8lE9VQGinQGx8rdH7PJ6VSmFQ7LzdEUEdmEu5e7dP12LwUTQjd\r\nZEXWIl87fzBOSrDZs+dFF0SjNHt9PhDzkuWA7ZxDWwia79YW/5V2oD6wQ4Nq\r\nLUqGSwb7VDyOPDvpqKXS4OxwzlKAUC2kQRIs9P2avFN/uWGPPp+5t1bcTb2u\r\n5ypW+XyphpB0584ul+JPomKKSc1mzH8gNgVILZVXJ3mundMhoJJBXYPlcwdg\r\nAPl6tMWH8Bf+o5WQGg7Y68gCecBSMQKEdy2dPzshnm55jYnNodxwo9T/LcCM\r\ncRW9ZyZsCSgqCItC0+HH/jWLVb20WvJed6k=\r\n=QTze\r\n-----END PGP SIGNATURE-----\r\n"}, "libc": ["glibc"], "main": "lightningcss.linux-arm64-gnu.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "05c23f1269321d4b072f6264a5c9cb6edd8a02bb", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "16.19.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-gnu_1.19.0_1676303953199_0.20641434429094896", "host": "s3://npm-registry-packages"}}, "1.20.0": {"name": "lightningcss-linux-arm64-gnu", "version": "1.20.0", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-gnu@1.20.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "8b7786936ea462f744a85038fb033cc56049b677", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-gnu/-/lightningcss-linux-arm64-gnu-1.20.0.tgz", "fileCount": 4, "integrity": "sha512-gtXoa6v0HvMRLbev6Hsef0+Q5He7NslB+Rs7G49Y5LUSdJeGIATEN+j8JzHC0DnxCsOGbEgGRmvtJzzYDkkluw==", "signatures": [{"sig": "MEQCICGiqTuEevD33A1Atrkdbvfnw5g3umMSYqexJHwd3yg3AiASmS6achObi2bG53zQKZJQMBkZJekUjqfqua/71dCsxA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7596965, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkQGRBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpNiw/+NxnpfKm9TecePs2VSKIkzqswSUP6lCp+JPC+FYH3yfjM+38D\r\nmtJ1jx65LASYP3tBUK4pgSGjXELi5i2ta51aYkjMYiWaanCX5UqJeti32tt2\r\neRUZrJo9Dc1U8GX4OdsjPx4paDe4V5UJW6PyLaZG5MRjKZhZ9Ny6i1g/5roC\r\nWpKmtEwGSyBRemyB5P0LeGtrbOGBxYZRmx5SP2z06oQzzBZFTolQpy2c+Xnt\r\nHKq/uAiu8/tAvLz8dlQD4lJDYKLkz0+JMF1U2wYWLEg/rEBG+Unl1bQPE24a\r\nzQrBP4QOeg06ztwopSSBnMDi9NuOv3LKuxHIGF4OiV4ZqKZLfIHiV+a5Htwe\r\nEdt43Inv1/pH3wfZcNVo2AsnwyoWS84qkcjmp3yHUlL+7BTsL++HCngfXYX1\r\nfE9La37v1BQD8+0QOuW4GghrmCcpkFFal4ep9jEZPimNNIZMF4gp5j+PlkI9\r\nEIXwghsjn7CnKGB4CuiEYfWMvSAVOVkxBsucaU7xn0peJItB7MJPr4A7z+Jc\r\nfDhujKFZj8fJfbFLl6kGb8dB3TZb6PnTLbASasx39pL6/EhlBFwEeJ0HES4i\r\nrUWPccV5ZKmFZMS4zciFQfZUUuAreQI9EyZg/rYNuIVbDTn4OAkK7Z+8NFRc\r\nOeFn2OgsWDzxl/qJTE7sQzq4lDRxdnGNTlU=\r\n=iHBp\r\n-----END PGP SIGNATURE-----\r\n"}, "libc": ["glibc"], "main": "lightningcss.linux-arm64-gnu.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "4028d4d9026750b31b813ecb4ca5387dba8dd396", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.15.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-gnu_1.20.0_1681941569159_0.4002369984382339", "host": "s3://npm-registry-packages"}}, "1.21.0": {"name": "lightningcss-linux-arm64-gnu", "version": "1.21.0", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-gnu@1.21.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "49ce48a034686d864e358e16c8d10af8456ef7c8", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-gnu/-/lightningcss-linux-arm64-gnu-1.21.0.tgz", "fileCount": 4, "integrity": "sha512-JkOG8K2Y4m5MeP3DlaHOgGDDtHbhbJcN8JcizFN0snUIIru1qxYNWPhAQsEwysuTRY9aANP0nScZJkALpcYmgA==", "signatures": [{"sig": "MEUCIQDmGdnDlbmK84HDGW+1rmI2hOkFtKYRsmwwV8UHE+YaPAIgdlV//ia22RlsSbTf46zxMHHw7DKghY52v7IGiRqMIWM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7826341}, "libc": ["glibc"], "main": "lightningcss.linux-arm64-gnu.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "11f4179f81041e14a04536d3bb0618f06de4e454", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.16.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-gnu_1.21.0_1686117495568_0.6540467826707335", "host": "s3://npm-registry-packages"}}, "1.21.1": {"name": "lightningcss-linux-arm64-gnu", "version": "1.21.1", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-gnu@1.21.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "1744af42b0a610661aaa17cdd5eb2e3ec65eda3d", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-gnu/-/lightningcss-linux-arm64-gnu-1.21.1.tgz", "fileCount": 4, "integrity": "sha512-ggCX0iyG/h2C1MfDfmfhB0zpEUTTP+kG9XBbwHRFKrQsmb3b7WC5QiyVuGYkzoGiHy1JNuyi27qR9cNVLCR8FQ==", "signatures": [{"sig": "MEYCIQCr2qrat46N4p+2IDuU+m6cjl9KTeMG4A+dpUtAmjpwrwIhAM3yFc51DIs2b00hrtza3QuUwyOl1RnMr9hNAF4AbHKh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7826341}, "libc": ["glibc"], "main": "lightningcss.linux-arm64-gnu.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "a36c105af3bb83370f64e28a8cf98b7dea52a6e0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.16.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-gnu_1.21.1_1687660732598_0.021902807953114944", "host": "s3://npm-registry-packages"}}, "1.21.2": {"name": "lightningcss-linux-arm64-gnu", "version": "1.21.2", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-gnu@1.21.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "dad80b4037238152e0db6cb7944004cb17d24c12", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-gnu/-/lightningcss-linux-arm64-gnu-1.21.2.tgz", "fileCount": 4, "integrity": "sha512-YAFPWM8Wi2UXIf382aDGxx0/zxOROZ7ADTBi8coXmI5FjDqUjxypc4U9+o962oi3k/N3K6IaL2f/E172+Xj0vA==", "signatures": [{"sig": "MEQCIDlr0+l8ffCmqsrClsi04DE6d9g3C3RcPMnWIFEHLPJdAiAwmUF84WBzlSjpe8qEKmqwWJkSXibtDjH5Ry+fa0MEQA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7834533}, "libc": ["glibc"], "main": "lightningcss.linux-arm64-gnu.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "e4963a58a813e02b47a7dd0320defcf0c4182512", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.16.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-gnu_1.21.2_1688266095679_0.3099654494073052", "host": "s3://npm-registry-packages"}}, "1.21.3": {"name": "lightningcss-linux-arm64-gnu", "version": "1.21.3", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-gnu@1.21.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "85679922cfc0b12714963c35b146705e5760215e", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-gnu/-/lightningcss-linux-arm64-gnu-1.21.3.tgz", "fileCount": 4, "integrity": "sha512-iPGYDw8lSkOSqtsDMeom29jpk4Ou6cKnayQBRzYJeGHzEfDGMPtKjCUJc9n425iCuMXc92qF8YqHEwndD4W+rA==", "signatures": [{"sig": "MEYCIQCr8GsizqZvyHsiNv+gnGEvXgnvKp1VXqnAIM2FwzqMiwIhAOhVKeIdGT8LedUcM18lsdwfcwalhRuPZB2Gern3AqFQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7834533}, "libc": ["glibc"], "main": "lightningcss.linux-arm64-gnu.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "83bab71e80781b4d2b128f5938099b2f0c8239bd", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.16.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-gnu_1.21.3_1688397348642_0.9173247312695147", "host": "s3://npm-registry-packages"}}, "1.21.4": {"name": "lightningcss-linux-arm64-gnu", "version": "1.21.4", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-gnu@1.21.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "12ba164ad3054f8ad028c9eb7a032a0e34324124", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-gnu/-/lightningcss-linux-arm64-gnu-1.21.4.tgz", "fileCount": 4, "integrity": "sha512-rQmbEQzXoQK3/O8YuJyri3RreATpQ9mscODpYHLwG3behk021u4OCNyVL7m2GL4fL+6SZuwCccjiHp1e0WeM6A==", "signatures": [{"sig": "MEUCIQD6PUSEZ40Mup7tnWZPrPVBCBdN+z3fu9m6kEBLFIkLrQIgXkoaQoT2+6SyXALWXTDygTNaT4czp1A7w3VMhz+SYvo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7834533}, "libc": ["glibc"], "main": "lightningcss.linux-arm64-gnu.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "bd517fde245dcce75a4b2eb21a25b147d54eb64a", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.16.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-gnu_1.21.4_1688441869621_0.991565960209102", "host": "s3://npm-registry-packages"}}, "1.21.5": {"name": "lightningcss-linux-arm64-gnu", "version": "1.21.5", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-gnu@1.21.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "1aa64cf3c4013a82012cd15b1ef0f97d08bc9bfd", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-gnu/-/lightningcss-linux-arm64-gnu-1.21.5.tgz", "fileCount": 4, "integrity": "sha512-KfzFNhC4XTbmG3ma/xcTs/IhCwieW89XALIusKmnV0N618ZDXEB0XjWOYQRCXeK9mfqPdbTBpurEHV/XZtkniQ==", "signatures": [{"sig": "MEQCIBO3aJhFhUq4XTmgVuBg5STzaO/4UB79gFAg+jgpECZ7AiBpYQCcxmXJLA8ogUn14WRFgtj+9urBpegPP9y3Yj3RiA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7834533}, "libc": ["glibc"], "main": "lightningcss.linux-arm64-gnu.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "d44a73c3c000ccebbe8355b7cf9272236e573b4d", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.16.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-gnu_1.21.5_1688531414903_0.9406064596832695", "host": "s3://npm-registry-packages"}}, "1.21.6": {"name": "lightningcss-linux-arm64-gnu", "version": "1.21.6", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-gnu@1.21.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "ab89c2509a647aece77b358267cf01e5f8cd675a", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-gnu/-/lightningcss-linux-arm64-gnu-1.21.6.tgz", "fileCount": 4, "integrity": "sha512-qDlQR2yLSEPOvZ9NQRnnft9lG3Aan0YZZUsurECuCbOcVW0HPW17wOMeK80hQxaOwD3o0mdjTGYzVP9oFthDeA==", "signatures": [{"sig": "MEUCIQDyVSh1cZIpkdj7tEi46EjbpZhcEgNcMMo55n+WDHM46wIgTs0+xE33VYJicD/G/Cpqp71D4pje/zjBs0WRmQpwwjg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7842725}, "libc": ["glibc"], "main": "lightningcss.linux-arm64-gnu.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "f485eb51d13e677a674cb10e170c71138446b71d", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.17.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-gnu_1.21.6_1692511448414_0.6023216539659308", "host": "s3://npm-registry-packages"}}, "1.21.7": {"name": "lightningcss-linux-arm64-gnu", "version": "1.21.7", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-gnu@1.21.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "79357d765977dfd56a459f6487c8a66d30c8eacb", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-gnu/-/lightningcss-linux-arm64-gnu-1.21.7.tgz", "fileCount": 4, "integrity": "sha512-PENY8QekqL9TG3AY/A7rkUBb5ymefGxea7Oe7+x7Hbw4Bz4Hpj5cec5OoMypMqFbURPmpi0fTWx4vSWUPzpDcA==", "signatures": [{"sig": "MEQCIAjR9ShvB+5jUvbgjmmVbSro26mL//aAJPXNsN+Np60wAiB2ZF1OmxJ+eBpIUmQwOB1OfkTzS8UrxK2Na+ahxRXuUg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7842725}, "libc": ["glibc"], "main": "lightningcss.linux-arm64-gnu.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "393013928888d47ec7684d52ed79f758d371bd7b", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.17.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-gnu_1.21.7_1692555047567_0.7008834628817737", "host": "s3://npm-registry-packages"}}, "1.21.8": {"name": "lightningcss-linux-arm64-gnu", "version": "1.21.8", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-gnu@1.21.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "6a74eff0680dd0759591962a3b92353f9b2bf49a", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-gnu/-/lightningcss-linux-arm64-gnu-1.21.8.tgz", "fileCount": 4, "integrity": "sha512-J<PERSON>/TuMMllkzaXV7/eDjG4IJKLlCl+RfYZwtsVmC82gc0QX0O37csGAcY2OGleiuA4DnEo/Qea5WoFfZUNC6zg==", "signatures": [{"sig": "MEUCIEBrOHDPM72Ansg7pr8+5YZ9paPLqm7l7bysyJtnYUt5AiEA4sekoD39ZDpRbhYnUu2Ax2U8JnDqI1gA77xH5dhCMww=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7842725}, "libc": ["glibc"], "main": "lightningcss.linux-arm64-gnu.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "81cb0daee30dad924b14af7fbc739ddfe31d7f9e", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.17.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-gnu_1.21.8_1694407629812_0.7472481207567827", "host": "s3://npm-registry-packages"}}, "1.22.0": {"name": "lightningcss-linux-arm64-gnu", "version": "1.22.0", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-gnu@1.22.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "b8e6daee4a60020a4930fc3564669868e723a10d", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-gnu/-/lightningcss-linux-arm64-gnu-1.22.0.tgz", "fileCount": 4, "integrity": "sha512-AArGtKSY4DGTA8xP8SDyNyKtpsUl1Rzq6FW4JomeyUQ4nBrR71uPChksTpj3gmWuGhZeRKLeCUI1DBid/zhChg==", "signatures": [{"sig": "MEUCIBRIVUetKV3OMtg4JKxWkgO6wBm3Gimq/y6Vl754N0LCAiEAuylnpHUZL+UVZSn4hI//uA+Nmv03wdAeEs6+KbSwLuc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7846821}, "libc": ["glibc"], "main": "lightningcss.linux-arm64-gnu.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "7ff93ca5c69ba9df415e1e2319d275e2cec249d7", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.17.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-gnu_1.22.0_1694990940166_0.1901925974781684", "host": "s3://npm-registry-packages"}}, "1.22.1": {"name": "lightningcss-linux-arm64-gnu", "version": "1.22.1", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-gnu@1.22.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "33800723fb3d782c71cc131cf38ca678a0e9d1fa", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-gnu/-/lightningcss-linux-arm64-gnu-1.22.1.tgz", "fileCount": 4, "integrity": "sha512-nYO5qGtb/1kkTZu3FeTiM+2B2TAb7m2DkLCTgQIs2bk2o9aEs7I96fwySKcoHWQAiQDGR9sMux9vkV4KQXqPaQ==", "signatures": [{"sig": "MEUCIEG/YyUQZeNhVeC6YO4s8P4AUPr6vaWS1LTtQ+z5NFYRAiEA+eYvJ0GLYhziwBrEjsjE8IZJSTck5GFQeipERaJvy24=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7855013}, "libc": ["glibc"], "main": "lightningcss.linux-arm64-gnu.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "4994306f8f8d98a2b37433fced50efdacbbab901", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.18.2", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-gnu_1.22.1_1699395811745_0.8190674800378286", "host": "s3://npm-registry-packages"}}, "1.23.0": {"name": "lightningcss-linux-arm64-gnu", "version": "1.23.0", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-gnu@1.23.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "05cfcfa2cf47a042ca11cfce520ae9f91e4efcdb", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-gnu/-/lightningcss-linux-arm64-gnu-1.23.0.tgz", "fileCount": 4, "integrity": "sha512-RS7sY77yVLOmZD6xW2uEHByYHhQi5JYWmgVumYY85BfNoVI3DupXSlzbw+b45A9NnVKq45+oXkiN6ouMMtTwfg==", "signatures": [{"sig": "MEQCIAdLfTsGCWJf82RJtFtEctJUsnr8ydUrqPu/GNZThUvxAiAq19Oh48JruJNBbXP/e5t7vEZ6XuQECpdyl86zw5eZ8A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7895973}, "libc": ["glibc"], "main": "lightningcss.linux-arm64-gnu.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "b47f496a86075adcb6719aee3a8867e749a880b9", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.19.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-gnu_1.23.0_1705276074225_0.340909507680921", "host": "s3://npm-registry-packages"}}, "1.24.0": {"name": "lightningcss-linux-arm64-gnu", "version": "1.24.0", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-gnu@1.24.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "20af7648d894253c97afe3b58c91dc224243985c", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-gnu/-/lightningcss-linux-arm64-gnu-1.24.0.tgz", "fileCount": 4, "integrity": "sha512-MqqUB2TpYtFWeBvvf5KExDdClU3YGLW5bHKs50uKKootcvG9KoS7wYwd5UichS+W3mYLc5yXUPGD1DNWbLiYKw==", "signatures": [{"sig": "MEYCIQDrYr4sVuA66hCGr9vq1UN8/6V+9Eemlfaqn5+BrRCSYwIhAIyU+wzkZIYRj8590EjlCYWfGQhCbioJKxyJHNyttei6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7986125}, "libc": ["glibc"], "main": "lightningcss.linux-arm64-gnu.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "30b9d79b3d277dfb90d90d78cd9182ac755ad6cf", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.19.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-gnu_1.24.0_1708648890227_0.7913026476055836", "host": "s3://npm-registry-packages"}}, "1.24.1": {"name": "lightningcss-linux-arm64-gnu", "version": "1.24.1", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-gnu@1.24.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "6b569b6078634233bc470c4179dd67e535f22d73", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-gnu/-/lightningcss-linux-arm64-gnu-1.24.1.tgz", "fileCount": 4, "integrity": "sha512-AQxWU8c9E9JAjAi4Qw9CvX2tDIPjgzCTrZCSXKELfs4mCwzxRkHh2RCxX8sFK19RyJoJAjA/Kw8+LMNRHS5qEg==", "signatures": [{"sig": "MEYCIQDQL8OhFk1jNJZOKOFVf1yCJ9tNFa3mFk6S4IzKuLAoYQIhAOryh2cUj91ifIkEOzTXvuNA1Q8AWWufBBwj+8t07sES", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7817821}, "libc": ["glibc"], "main": "lightningcss.linux-arm64-gnu.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "baa1a2b7fa52eeb3827f8edcc2e14de33cd69ad0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.19.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-gnu_1.24.1_1710476580234_0.8885624179673988", "host": "s3://npm-registry-packages"}}, "1.25.0": {"name": "lightningcss-linux-arm64-gnu", "version": "1.25.0", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-gnu@1.25.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "6eec610a7362b2c6024910319914ff0f49945e38", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-gnu/-/lightningcss-linux-arm64-gnu-1.25.0.tgz", "fileCount": 4, "integrity": "sha512-1+6tuAsUyMVG5N2rzgwaOOf84yEU+Gjl71b+wLcz26lyM/ohgFgeqPWeB/Dor0wyUnq7vg184l8goGT26cRxoQ==", "signatures": [{"sig": "MEUCIQDSUMaysxBMkF62mIoXGpjG3UHzT9ufmm33X+6kOJGw9wIgU6Zqre8fy0s3L6mefHTcMexr+0FqrsndYXPBhg4Y/YU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7948893}, "libc": ["glibc"], "main": "lightningcss.linux-arm64-gnu.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "81d21b9f5201c6eb8365fbb4fa81cbd947c3474f", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.20.2", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-gnu_1.25.0_1715973756565_0.47352585041044293", "host": "s3://npm-registry-packages"}}, "1.25.1": {"name": "lightningcss-linux-arm64-gnu", "version": "1.25.1", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-gnu@1.25.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "4303c196d8d32b66b6a2f7c939c938bd0f138f75", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-gnu/-/lightningcss-linux-arm64-gnu-1.25.1.tgz", "fileCount": 4, "integrity": "sha512-Xjxsx286OT9/XSnVLIsFEDyDipqe4BcLeB4pXQ/FEA5+2uWCCuAEarUNQumRucnj7k6ftkAHUEph5r821KBccQ==", "signatures": [{"sig": "MEQCIEtfA8Ehm/Tw9By6BTyYPWMtJFHb1b6EJ0VNCcisAtlZAiB8Ja2v7iY1WdQTG3He7I57fcHJGgygaONnbnbJR97bKg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7957085}, "libc": ["glibc"], "main": "lightningcss.linux-arm64-gnu.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "fa6c015317e5cca29fa8a09b12d09ac53dcfbc77", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.20.2", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-gnu_1.25.1_1716617178931_0.5448830748048792", "host": "s3://npm-registry-packages"}}, "1.26.0": {"name": "lightningcss-linux-arm64-gnu", "version": "1.26.0", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-gnu@1.26.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "3b2d8cae1f112bf3ae26ea81eccc02efb81b5f09", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-gnu/-/lightningcss-linux-arm64-gnu-1.26.0.tgz", "fileCount": 4, "integrity": "sha512-iJmZM7fUyVjH+POtdiCtExG+67TtPUTer7K/5A8DIfmPfrmeGvzfRyBltGhQz13Wi15K1lf2cPYoRaRh6vcwNA==", "signatures": [{"sig": "MEUCIQDDE9t+8kUj1JwYk4HZAumisema8fppAGFrW674WOLgQQIgJ3rC7ozwdIKi6fG5vtcWOLPVuA0cOJURqnMPWePAiLM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7846493}, "libc": ["glibc"], "main": "lightningcss.linux-arm64-gnu.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "0bcd896e81a8a2c5d847f626a37e2cffea79e2a0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.20.4", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-gnu_1.26.0_1722958368119_0.5961097974648175", "host": "s3://npm-registry-packages"}}, "1.27.0": {"name": "lightningcss-linux-arm64-gnu", "version": "1.27.0", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-gnu@1.27.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "cfd9e18df1cd65131da286ddacfa3aee6862a752", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-gnu/-/lightningcss-linux-arm64-gnu-1.27.0.tgz", "fileCount": 4, "integrity": "sha512-cPsxo1QEWq2sfKkSq2Bq5feQDHdUEwgtA9KaB27J5AX22+l4l0ptgjMZZtYtUnteBofjee+0oW1wQ1guv04a7A==", "signatures": [{"sig": "MEQCIET0EvmTTj+7WWAkuL/aKMcyv0oCZtw+hbtLaBTmzCcHAiB9aU7YgL5+uP7sJJGPl4ad4Z/M9jsXeACjzV54gJHiUg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7850589}, "libc": ["glibc"], "main": "lightningcss.linux-arm64-gnu.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "eb49015cf887ae720b80a2856ccbdf61bf940ef1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.20.4", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-gnu_1.27.0_1726023641832_0.5925197405314158", "host": "s3://npm-registry-packages"}}, "1.28.0": {"name": "lightningcss-linux-arm64-gnu", "version": "1.28.0", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-gnu@1.28.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "51fed8677ea2da124f6052ed81e9257bb89e84aa", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-gnu/-/lightningcss-linux-arm64-gnu-1.28.0.tgz", "fileCount": 4, "integrity": "sha512-c7z/P4mTFnhVS4RaWq6TDhzwQAIZUuQD8kdk6TZX7spmFt4AbcMwANMGcLj4PP8DE9CS5E9vqkau5aAkXouHXw==", "signatures": [{"sig": "MEQCIEN6XS5+LYVdDqu6Uv+IXp6r/O1FQwxluxFLjXVr3ndsAiAhJjud6VJWuqHPfwiGWEAyvvIX8IuGBa8BJTAwujzohg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7862914}, "libc": ["glibc"], "main": "lightningcss.linux-arm64-gnu.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "8a67583105757e4a25378d65d243b87a345b2c2d", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"caniuse-lite": "^1.0.30001677", "lightningcss": "link:."}, "_nodeVersion": "18.20.4", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-gnu_1.28.0_1730668661083_0.445086770818486", "host": "s3://npm-registry-packages"}}, "1.28.1": {"name": "lightningcss-linux-arm64-gnu", "version": "1.28.1", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-gnu@1.28.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "9f4e4450617230ea557abb5ffd5d26b2047e9b62", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-gnu/-/lightningcss-linux-arm64-gnu-1.28.1.tgz", "fileCount": 4, "integrity": "sha512-iO+fN9hOMmzfwqcG2/BgUtMKD48H2JO/SXU44fyIwpY2veb65QF5xiRrQ9l1FwIxbGK3231KBYCtAqv+xf+NsQ==", "signatures": [{"sig": "MEQCIH78DpcN9s65sH9NfC1VRyf9ItCmgXBhhoy216VDIsltAiB5BhRgEuglShAiYZzKPhA2jOgV52Pn8iX03cwJJnvEgA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7858818}, "libc": ["glibc"], "main": "lightningcss.linux-arm64-gnu.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "a3390fd4140ca87f5035595d22bc9357cf72177e", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"caniuse-lite": "^1.0.30001677", "lightningcss": "link:."}, "_nodeVersion": "18.20.4", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-gnu_1.28.1_1730674698864_0.3434802784566615", "host": "s3://npm-registry-packages"}}, "1.28.2": {"name": "lightningcss-linux-arm64-gnu", "version": "1.28.2", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-gnu@1.28.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "85646f08c5efbfd7c94f8e5ed6392d5cf95fa42c", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-gnu/-/lightningcss-linux-arm64-gnu-1.28.2.tgz", "fileCount": 4, "integrity": "sha512-nhfjYkfymWZSxdtTNMWyhFk2ImUm0X7NAgJWFwnsYPOfmtWQEapzG/DXZTfEfMjSzERNUNJoQjPAbdqgB+sjiw==", "signatures": [{"sig": "MEYCIQDAzTjPSnm7kqWIGhU9S4AAF5KeCmKSrVpEMlRZOzK1ZQIhAK0MMb8JzoDUUqvDrWUAradI7J883w6wPZEJ4n9mxmeT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7862914}, "libc": ["glibc"], "main": "lightningcss.linux-arm64-gnu.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "9b2e8bbe732d7c101272ddab03ac21b88bf55c4a", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"caniuse-lite": "^1.0.30001677", "lightningcss": "link:."}, "_nodeVersion": "18.20.5", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-gnu_1.28.2_1732512289119_0.4260497991730474", "host": "s3://npm-registry-packages"}}, "1.29.0": {"name": "lightningcss-linux-arm64-gnu", "version": "1.29.0", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-gnu@1.29.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "a05ad259c1fa1c8addf44559e6628591722d9d7a", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-gnu/-/lightningcss-linux-arm64-gnu-1.29.0.tgz", "fileCount": 4, "integrity": "sha512-/Ca6ZeKybaQlhseizRXumJc6cufKoLllkaF5JJW0NS+VHfFB8AoTQlDSJNiVgqmpTxJ2Rlsgkcb+G0ovsN4tHQ==", "signatures": [{"sig": "MEUCIE73A9tZn1n+DzIkyMvQTys7BfowrCSEoVD5M202MstSAiEAqhryEshtAx3sSzt9UP8wTOINzd/hiGK/QBPfS7jI7Qg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7711338}, "libc": ["glibc"], "main": "lightningcss.linux-arm64-gnu.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "222fa9f7ad8b4004afd7a30c2f99cab3c8621191", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"caniuse-lite": "^1.0.30001677", "lightningcss": "link:."}, "_nodeVersion": "20.18.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-gnu_1.29.0_1736401660878_0.9049586633615838", "host": "s3://npm-registry-packages-npm-production"}}, "1.29.1": {"name": "lightningcss-linux-arm64-gnu", "version": "1.29.1", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-gnu@1.29.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "7825eb119ddf580a4a4f011c6f384a3f9c992060", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-gnu/-/lightningcss-linux-arm64-gnu-1.29.1.tgz", "fileCount": 4, "integrity": "sha512-0+vClRIZ6mmJl/dxGuRsE197o1HDEeeRk6nzycSy2GofC2JsY4ifCRnvUWf/CUBQmlrvMzt6SMQNMSEu22csWQ==", "signatures": [{"sig": "MEQCIDbzxR7vR/FiPcSpVNYD85kkJ4zsTCssH4jIJMxDaWEpAiBt94QabvqCjnU/ks+jCziFvmKF3XDADcWl0pfCzKSA2A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7711338}, "libc": ["glibc"], "main": "lightningcss.linux-arm64-gnu.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "b10f9baf8878411bf2b09dfe8d64ba09ef7a4eac", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"caniuse-lite": "^1.0.30001677", "lightningcss": "link:."}, "_nodeVersion": "20.18.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-gnu_1.29.1_1736445756370_0.9363145571964313", "host": "s3://npm-registry-packages-npm-production"}}, "1.29.2": {"name": "lightningcss-linux-arm64-gnu", "version": "1.29.2", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-gnu@1.29.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "e73d7608c4cce034c3654e5e8b53be74846224de", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-gnu/-/lightningcss-linux-arm64-gnu-1.29.2.tgz", "fileCount": 4, "integrity": "sha512-KKCpOlmhdjvUTX/mBuaKemp0oeDIBBLFiU5Fnqxh1/DZ4JPZi4evEH7TKoSBFOSOV3J7iEmmBaw/8dpiUvRKlQ==", "signatures": [{"sig": "MEYCIQDn6mRpRdCPlHx0vVgaQMRSiT22btJgGUFXPiVoj3tSnwIhAKM53CjMBL2EY5miLYZ5If4vTLE96oMThUheO0CwtenU", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 7715397}, "libc": ["glibc"], "main": "lightningcss.linux-arm64-gnu.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "f2303df29448a55c5f4f284f747eb53760769fe4", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "20.18.3", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-gnu_1.29.2_1741242116724_0.64379147671543", "host": "s3://npm-registry-packages-npm-production"}}, "1.29.3": {"name": "lightningcss-linux-arm64-gnu", "version": "1.29.3", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-gnu@1.29.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "b376082fbf9c21a67bdbb325f6acaba2ff07117d", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-gnu/-/lightningcss-linux-arm64-gnu-1.29.3.tgz", "fileCount": 4, "integrity": "sha512-Pqau7jtgJNmQ/esugfmAT1aCFy/Gxc92FOxI+3n+LbMHBheBnk41xHDhc0HeYlx9G0xP5tK4t0Koy3QGGNqypw==", "signatures": [{"sig": "MEUCIQDiOxTegRzq0k9B7o68JF4BoohuBGLMEM0H/yg7PN7JcgIgVFaOBjpkp5A1BNQNjVGo8Yhst9sMNMjdW1V5IpFtHgw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 7719493}, "libc": ["glibc"], "main": "lightningcss.linux-arm64-gnu.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "80eb8617c5f8f5519ed85bd3eb6d8953f4d32493", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "20.18.3", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-gnu_1.29.3_1741974592227_0.25762057026897645", "host": "s3://npm-registry-packages-npm-production"}}, "1.30.0": {"name": "lightningcss-linux-arm64-gnu", "version": "1.30.0", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-gnu@1.30.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "725c307493c4bb741f922b81a9319fd322c8f94e", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-gnu/-/lightningcss-linux-arm64-gnu-1.30.0.tgz", "fileCount": 4, "integrity": "sha512-Q45+fvm7eAAmorsEzc1ZBwajGnXDocB/nRaSldpHQa36QbP93GrzmBqfSdi2pEks2yXMxST4yznio24Q6en7Sg==", "signatures": [{"sig": "MEUCIBJcPogkWJI5QEpHJB/CjJfrHURjjGsRQMmUD69SRzgvAiEAiyCYcoVkGHL58vfvE+PqOPJPXPjsKCHS86xv9z2ZkUg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 7764549}, "libc": ["glibc"], "main": "lightningcss.linux-arm64-gnu.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "474c67c046b4a54217166057296fa4ca28764858", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "20.19.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-gnu_1.30.0_1746945550104_0.29660324684774997", "host": "s3://npm-registry-packages-npm-production"}}, "1.30.1": {"name": "lightningcss-linux-arm64-gnu", "version": "1.30.1", "license": "MPL-2.0", "description": "A CSS parser, transformer, and minifier written in Rust", "main": "lightningcss.linux-arm64-gnu.node", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "git+https://github.com/parcel-bundler/lightningcss.git"}, "engines": {"node": ">= 12.0.0"}, "resolutions": {"lightningcss": "link:."}, "os": ["linux"], "cpu": ["arm64"], "libc": ["glibc"], "_id": "lightningcss-linux-arm64-gnu@1.30.1", "gitHead": "f2dc67c4d3fe92f26693c02366db1e60cae0db27", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-gB72maP8rmrKsnKYy8XUuXi/4OctJiuQjcuqWNlJQ6jZiWqtPvqFziskH3hnajfvKB27ynbVCucKSm2rkQp4Bw==", "shasum": "eee7799726103bffff1e88993df726f6911ec009", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-gnu/-/lightningcss-linux-arm64-gnu-1.30.1.tgz", "fileCount": 4, "unpackedSize": 7764549, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIF6IkJC8O5nXy0XhIP3n55RWnRUmei9vVitgYEwif14JAiAscZuXHfhQVcsMcphPHZbLOgjDlqHXCBaMi5tUJqxxig=="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/lightningcss-linux-arm64-gnu_1.30.1_1747193921219_0.6736698384653077"}, "_hasShrinkwrap": false}}, "time": {"created": "2022-09-08T05:19:08.720Z", "modified": "2025-05-14T03:38:41.624Z", "1.14.0": "2022-09-08T05:19:09.035Z", "1.15.0": "2022-09-15T04:16:27.840Z", "1.15.1": "2022-09-16T03:49:18.288Z", "1.16.0": "2022-09-20T03:23:09.624Z", "1.16.1": "2022-11-06T18:02:02.692Z", "1.17.0": "2022-11-29T16:47:45.270Z", "1.17.1": "2022-11-30T17:30:33.212Z", "1.18.0": "2023-01-04T17:00:16.757Z", "1.19.0": "2023-02-13T15:59:13.475Z", "1.20.0": "2023-04-19T21:59:29.415Z", "1.21.0": "2023-06-07T05:58:15.822Z", "1.21.1": "2023-06-25T02:38:52.877Z", "1.21.2": "2023-07-02T02:48:16.026Z", "1.21.3": "2023-07-03T15:15:48.884Z", "1.21.4": "2023-07-04T03:37:49.840Z", "1.21.5": "2023-07-05T04:30:15.155Z", "1.21.6": "2023-08-20T06:04:08.690Z", "1.21.7": "2023-08-20T18:10:47.900Z", "1.21.8": "2023-09-11T04:47:10.148Z", "1.22.0": "2023-09-17T22:49:00.434Z", "1.22.1": "2023-11-07T22:23:32.029Z", "1.23.0": "2024-01-14T23:47:54.462Z", "1.24.0": "2024-02-23T00:41:30.415Z", "1.24.1": "2024-03-15T04:23:00.495Z", "1.25.0": "2024-05-17T19:22:36.767Z", "1.25.1": "2024-05-25T06:06:19.113Z", "1.26.0": "2024-08-06T15:32:48.490Z", "1.27.0": "2024-09-11T03:00:42.140Z", "1.28.0": "2024-11-03T21:17:41.362Z", "1.28.1": "2024-11-03T22:58:19.179Z", "1.28.2": "2024-11-25T05:24:49.440Z", "1.29.0": "2025-01-09T05:47:41.148Z", "1.29.1": "2025-01-09T18:02:36.649Z", "1.29.2": "2025-03-06T06:21:56.959Z", "1.29.3": "2025-03-14T17:49:52.491Z", "1.30.0": "2025-05-11T06:39:10.374Z", "1.30.1": "2025-05-14T03:38:41.462Z"}, "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "license": "MPL-2.0", "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "repository": {"type": "git", "url": "git+https://github.com/parcel-bundler/lightningcss.git"}, "description": "A CSS parser, transformer, and minifier written in Rust", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "This is the aarch64-unknown-linux-gnu build of lightningcss. See https://github.com/parcel-bundler/lightningcss for details.", "readmeFilename": "README.md"}