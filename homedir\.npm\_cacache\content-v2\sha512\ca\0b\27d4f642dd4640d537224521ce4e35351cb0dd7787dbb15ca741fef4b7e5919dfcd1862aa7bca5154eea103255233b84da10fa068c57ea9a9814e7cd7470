{"name": "append-field", "dist-tags": {"latest": "2.0.0"}, "versions": {"0.1.0": {"name": "append-field", "version": "0.1.0", "devDependencies": {"mocha": "^2.2.4", "standard": "^3.7.2", "testdata-w3c-json-form": "^0.2.0"}, "dist": {"shasum": "6ddc58fa083c7bc545d3c5995b2830cc2366d44a", "tarball": "https://registry.npmjs.org/append-field/-/append-field-0.1.0.tgz", "integrity": "sha512-8BgHoIwbQZaAQgDZLBu2vQoXHgUpSx4vQK1qv7e6R8YfbiSf4fCaBPJRtM1BaxVn1rIHc5ftv0cklsJ78BkouQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDtz+m8ld1y3v6AQusHnAjSV8PJIcSnS/0CFL5yAQHKSQIgLwnNX6Y51ogKZTGXSkp6P8kBI2sNUd4LkDNlrnJCobM="}]}}, "1.0.0": {"name": "append-field", "version": "1.0.0", "devDependencies": {"mocha": "^2.2.4", "standard": "^6.0.5", "testdata-w3c-json-form": "^0.2.0"}, "dist": {"shasum": "1e3440e915f0b1203d23748e78edd7b9b5b43e56", "tarball": "https://registry.npmjs.org/append-field/-/append-field-1.0.0.tgz", "integrity": "sha512-klpgFSWLW1ZEs8svjfb7g4qWY0YS5imI82dTg+QahUvJ8YqAY0P10Uk8tTyh9ZGuYEZEMaeJYCF5BFuX552hsw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDZeBYB3RMurzQNTqi3uh7fq/E+CGZn/jkeGTlujpuiIwIhAImbaab0j0wxRcE4J6wu6eLlt2uzAUJHt+338glH3DP+"}]}}, "2.0.0": {"name": "append-field", "version": "2.0.0", "devDependencies": {"mocha": "^8.4.0", "standard": "^16.0.3", "testdata-w3c-json-form": "^0.2.0", "ts-readme-generator": "^0.5.2"}, "dist": {"integrity": "sha512-yUPXgerKgcuwakzrRyklfhX+Ma2aYYMjb+BO2RPUwq+tk928V/i5DFWcCUS3hQhj468N+Ktmwb0tfbEtmfC6WA==", "shasum": "5997b4468ec8b87d3344cbe9af244a6853b9f416", "tarball": "https://registry.npmjs.org/append-field/-/append-field-2.0.0.tgz", "fileCount": 9, "unpackedSize": 6819, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg/BcuCRA9TVsSAnZWagAA5dcP/3ciGCHBLwd+e5WDEYEP\nb9VSBoWt9j3HmwgfFZ3cRKkyrbEYCshZw98LaH3U8R2czrtdzjlJVDHRW7fi\n18KO/pz7a1zQ9Pa8VW0AVMiU1kC7Gqy+9S+pVDOd13uxF4zNjnPuNAFce45Y\nfuDUUR4mdzpPCZMcvnrsjog4IyftlawI09yuJ4PPTgkykeU9FX2ZkPTC6rhG\nwyu3FnfWyqEeygKLAFSTNsI3edJ8lCd2d1+KbcqiHR2VuhazRbrMgOf39G5O\nNrH5zPh9B9xJs6SHPC0T5momBmQh9fjAIjsoXMVoSVox3KyPc3NsvVK8XY+T\nrByAeBzHekxTJ5F5zpXQBRTKh9sizrrOmPwu6fbwt9+6xGEYr+/+nJyzn6q/\n87sbMuovaAKWrb4gNDJ2Pwx6rMxZI5Z/j8RJk791n80K7C1SlNgsaK5EjQ7t\nw+KzEz0ah+hcr2Jan3GbcU0jPJiZkIOEJLiEi2gX6dL2sBefr24pj9q2D2Id\nmEi/e7qCg96HNXfyXGoh6+ADmAA3TKg2kLd/vEoMoOHOWplqTrfyL4FTvbWN\nm8UJnsN04bg7nmZZYGw5sU0aiiirO0ILCvtSjDzHb1qq7yei7xCRdkYxPHaQ\n9f9vu0MbVDmq0snZXxvgJs51l67yMgefolc+63S5kXXceEuwVbYUvCwyyNEm\nUBCX\r\n=dDsL\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCt5g4UO4RBBmaJQyEwY8IR1G/kOe9ixzKErNKIhxb2sAIhAMPqa/3ccy6BpjfNt/pYCUGj97fhI4dvi//H6ERf1Ki+"}]}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}}}, "modified": "2022-06-13T03:17:36.356Z"}