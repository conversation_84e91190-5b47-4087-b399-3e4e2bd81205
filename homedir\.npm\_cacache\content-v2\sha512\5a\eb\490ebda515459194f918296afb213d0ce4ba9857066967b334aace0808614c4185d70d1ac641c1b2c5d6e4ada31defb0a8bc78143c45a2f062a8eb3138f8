{"_id": "nopt", "_rev": "159-cfad8c96856244e8355cedaac53b7ca0", "name": "nopt", "dist-tags": {"latest": "8.1.0"}, "versions": {"1.0.0": {"name": "nopt", "version": "1.0.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "nopt@1.0.0", "dist": {"bin": {"0.4-darwin-10.7.0": {"shasum": "e0864df8d3e4d2b81ef268d8a50b2f1bccd39e54", "tarball": "http://registry.npmjs.org/nopt/-/nopt-1.0.0-0.4-darwin-10.7.0.tgz"}}, "shasum": "a786d439b09c142dca74b0b29ef1458da50e37d8", "tarball": "https://registry.npmjs.org/nopt/-/nopt-1.0.0.tgz", "integrity": "sha512-g4yg1XbYuPnbiq4+ylgT+E2T0EPQB4RM0u/XBBspw2p8w/9ABkwCG2itNNX2TtX53ZncZySvPizS15oc0HE4JA==", "signatures": [{"sig": "MEUCIQCgSGHL0t+Vt45KopZC988vh5t9blfQnz2w0Jc3HA1DCgIgcie7u3yijb7dcyvVHscJiFl9vqbliNyH3nPxbJDtiJY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/nopt.js", "engines": {"node": "*"}, "scripts": {"test": "node lib/optparse.js"}, "repository": {"url": "git://github.com/isaacs/nopt.git", "type": "git"}, "_npmVersion": "1.0.1rc0", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "directories": {}, "_nodeVersion": "v0.5.0-pre", "dependencies": {"abbrev": "1"}, "_defaultsLoaded": true, "_engineSupported": true}, "1.0.1": {"name": "nopt", "version": "1.0.1", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "nopt@1.0.1", "dist": {"shasum": "585e38c61508b02b1ea2cc0028eef8c303079285", "tarball": "https://registry.npmjs.org/nopt/-/nopt-1.0.1.tgz", "integrity": "sha512-5vYyxgk7nJ+GOifz3IKX6S1VqSzYCIey2Pf+8AApQvwr9OTi1KnenoYUB6f9cKDW9tavAnvvZAK4s10/Oh89gQ==", "signatures": [{"sig": "MEQCIExxO6u1ypJrjEDSSbD/lgpJvQoVtvK5+njIIf6yu3IYAiB/qadiCntpfSbtEvh3s9CWR/sFjUSUN0TRKpHbe6082g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/nopt.js", "engines": {"node": "*"}, "scripts": {"test": "node lib/optparse.js"}, "repository": {"url": "git://github.com/isaacs/nopt.git", "type": "git"}, "_npmVersion": "1.0.1rc2", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "directories": {}, "_nodeVersion": "v0.4.4", "dependencies": {"abbrev": "1"}, "_defaultsLoaded": true, "_engineSupported": true}, "1.0.2": {"name": "nopt", "version": "1.0.2", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "nopt@1.0.2", "dist": {"shasum": "bb26ab771fb09411f716b122c12cd98fdc98f4d1", "tarball": "https://registry.npmjs.org/nopt/-/nopt-1.0.2.tgz", "integrity": "sha512-df/jPsj3IzBEpTJ5m30R6hjsYhlHytYK2rZqTqFcMIf0V4hkMp8H2++8nQ1GeG45osen1UtM0dXjQPbGPGDMyg==", "signatures": [{"sig": "MEYCIQD93MXPEgg/TD4Jwu+ohnNTZ+E12JUeUlyjAc0gOlREIgIhAOBP8hGhs4NFvtvsCEtF74muc4bnNeVNJMShJvJrRInv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/nopt.js", "engines": {"node": "*"}, "scripts": {"test": "node lib/nopt.js"}, "repository": {"url": "git://github.com/isaacs/nopt.git", "type": "git"}, "_npmVersion": "1.0.1rc3", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "directories": {}, "_nodeVersion": "v0.5.0-pre", "dependencies": {"abbrev": "1"}, "_defaultsLoaded": true, "_engineSupported": true}, "1.0.3": {"name": "nopt", "version": "1.0.3", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "nopt@1.0.3", "dist": {"shasum": "a5557211e05f4baad09bbf8e9d798072bff69166", "tarball": "https://registry.npmjs.org/nopt/-/nopt-1.0.3.tgz", "integrity": "sha512-0RPQ4srMGu3J+z7xLd+L/Any+79zazeN1KQsm2kT9UePl2yKCfpyoTmRtLtH3+zI/wda+Ghiw9clgsQJZN7Nrg==", "signatures": [{"sig": "MEUCIGvQ8Wj3xYjO7mZZg+kyFY1cQOGJyFtyVf3qTAGW1w+LAiEAgEuA/QzrEY2CYLOQoIYD3KUTW86kPRp/pfxivcFwoxo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/nopt.js", "engines": {"node": "*"}, "scripts": {"test": "node lib/nopt.js"}, "repository": {"url": "git://github.com/isaacs/nopt.git", "type": "git"}, "_npmVersion": "1.0.1rc3", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "directories": {}, "_nodeVersion": "v0.5.0-pre", "dependencies": {"abbrev": "1"}, "_defaultsLoaded": true, "_engineSupported": true}, "1.0.4": {"name": "nopt", "version": "1.0.4", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "nopt@1.0.4", "dist": {"shasum": "023fc93f439094e662e2e4186345bfabda8eceda", "tarball": "https://registry.npmjs.org/nopt/-/nopt-1.0.4.tgz", "integrity": "sha512-LDtXvd1Iez7+aqoSpLS4s1eWpEqfheV5pBqRyqZd3akZsm0mkb9IYx8RLNpm5VO/6ZAO2q4DuFsKbKxZIQtq2Q==", "signatures": [{"sig": "MEQCICXhSk0Xxh0qMrqRX649a/9xxl470GNnB1p/jW0wpmM1AiAl6iXJgBvMgVSHle91DpkgCmqsL+anT+oxc8VcE/O0fg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/nopt.js", "engines": {"node": "*"}, "scripts": {"test": "node lib/nopt.js"}, "repository": {"url": "git://github.com/isaacs/nopt.git", "type": "git"}, "_npmVersion": "1.0.1rc4", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "directories": {}, "_nodeVersion": "v0.5.0-pre", "dependencies": {"abbrev": "1"}, "_defaultsLoaded": true, "_engineSupported": true}, "1.0.5": {"name": "nopt", "version": "1.0.5", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "nopt@1.0.5", "dist": {"shasum": "fc79e34a4e8862e9c413d2e1cac07ee645ac4cc8", "tarball": "https://registry.npmjs.org/nopt/-/nopt-1.0.5.tgz", "integrity": "sha512-pO8QRDGSBdY7SZSMLcL+P/VZddO/MT29MrDLHLVw86O/jWZRWPxnHeNjmKS7yjC+I7sNhuCpTdXcezhTXz4Ofg==", "signatures": [{"sig": "MEYCIQDmI3/+TF4vzHhuKVOxgotuDTPfuI9RZ632k2uUjmucDQIhAICaIxRcF6ZZJq1VMgeIH3DFBVMzWSx0Tm74babHfqVX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/nopt.js", "engines": {"node": "*"}, "scripts": {"test": "node lib/nopt.js"}, "repository": {"url": "git://github.com/isaacs/nopt.git", "type": "git"}, "_npmVersion": "1.0.1rcFINAL", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "directories": {}, "_nodeVersion": "v0.4.8-pre", "dependencies": {"abbrev": "1"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "1.0.6": {"name": "nopt", "version": "1.0.6", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "https://github.com/isaacs/nopt/raw/master/LICENSE", "type": "MIT"}, "_id": "nopt@1.0.6", "bin": {"nopt": "./bin/nopt.js"}, "dist": {"shasum": "37307cafcdccf78b954ec06dcef31b936b4d03df", "tarball": "https://registry.npmjs.org/nopt/-/nopt-1.0.6.tgz", "integrity": "sha512-dreXmuduYye7FMkIKxphhgi7V1bdAWCbaOWUfwz5Lpfyi+NQ0oazOOKa1YJGaXUlAr+28+8xw4D3VT5F+isAYg==", "signatures": [{"sig": "MEUCIDBLWvaLY2Q1Pf9FKYVC6NsyD0YEQ0PBEoP2alicy1wbAiEAhV7PY9luUgLc+MJVWtsJWZhQtBXJl2sQM2MEujcmcz0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/nopt.js", "engines": {"node": "*"}, "scripts": {"test": "node lib/nopt.js"}, "repository": {"url": "git://github.com/isaacs/nopt.git", "type": "git"}, "_npmVersion": "1.0.15", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "directories": {}, "_nodeVersion": "v0.4.10-pre", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/nopt/1.0.6/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"abbrev": "1"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "1.0.7": {"name": "nopt", "version": "1.0.7", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "https://github.com/isaacs/nopt/raw/master/LICENSE", "type": "MIT"}, "_id": "nopt@1.0.7", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "bin": {"nopt": "./bin/nopt.js"}, "dist": {"shasum": "cc72658b52a3f653a70883a1823dd8f3ddc57f75", "tarball": "https://registry.npmjs.org/nopt/-/nopt-1.0.7.tgz", "integrity": "sha512-SH2lJYQRxmZw7Yg7DgH8JqN8Kut2c/SwT6xh5yMqZoIh+ZltZ+e9s8hQLZc4zFp8cd6LSQZ1LywJ9mNqD+bwnw==", "signatures": [{"sig": "MEYCIQDoH66nikoruCi2BjBBdraLqZd6+VVg+Pwu2zKJCMMUegIhAMWFRlj+1oNQaZZ2qMFAbB4YxIyLL8/usUNFZBEU6IvD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/nopt.js", "engines": {"node": "*"}, "scripts": {"test": "node lib/nopt.js"}, "repository": {"url": "git://github.com/isaacs/nopt.git", "type": "git"}, "_npmVersion": "1.0.28-pre-DEV-UNSTABLE", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "directories": {}, "_nodeVersion": "v0.4.11", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/nopt/1.0.7/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"abbrev": "1"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "1.0.8": {"name": "nopt", "version": "1.0.8", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "https://github.com/isaacs/nopt/raw/master/LICENSE", "type": "MIT"}, "_id": "nopt@1.0.8", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "bin": {"nopt": "./bin/nopt.js"}, "dist": {"shasum": "d4ac752df307f1a02eb771c40ed23188e7ca44c6", "tarball": "https://registry.npmjs.org/nopt/-/nopt-1.0.8.tgz", "integrity": "sha512-JgQN7eri0sfXcEtFCXEIuOevrUZcEmJsg7gQapD4MDJJcdrcpZCduPSc38k5Fsbunw1LaP/wE03a7Zk0eTzhiQ==", "signatures": [{"sig": "MEYCIQCZt8mpNYwnBtfDKlM38sTfmKsY531j+24NMcEew2/eEwIhANBxzOOyR7riuBhpX5TDsTgtjd5CSjiM6ejZsB6YxYVO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/nopt.js", "engines": {"node": "*"}, "scripts": {"test": "node lib/nopt.js"}, "repository": {"url": "git://github.com/isaacs/nopt.git", "type": "git"}, "_npmVersion": "1.0.28-pre-DEV-UNSTABLE", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "directories": {}, "_nodeVersion": "v0.5.7-pre", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/nopt/1.0.8/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"abbrev": "1"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "1.0.9": {"name": "nopt", "version": "1.0.9", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "https://github.com/isaacs/nopt/raw/master/LICENSE", "type": "MIT"}, "_id": "nopt@1.0.9", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "bin": {"nopt": "./bin/nopt.js"}, "dist": {"shasum": "3bc0d7cba7bfb0d5a676dbed7c0ebe48a4fd454e", "tarball": "https://registry.npmjs.org/nopt/-/nopt-1.0.9.tgz", "integrity": "sha512-CmUZ3rzN0/4kRHum5pGRiGkhmBMzgtEDxrZVHqRJDSv8qK6s+wzaig/xeyB22Due5aZQeTiEZg/nrmMH2tapDQ==", "signatures": [{"sig": "MEYCIQCEyNPKcYZLkvhMJWXR56iZM6sxAlpGgqSIhLI+SjMExgIhAJfcEC/GT9iKxQAl8rm9kK1nDjFLgCDDMTZFLEItPmQw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/nopt.js", "engines": {"node": "*"}, "scripts": {"test": "node lib/nopt.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/nopt.git", "type": "git"}, "_npmVersion": "1.0.30", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "directories": {}, "_nodeVersion": "v0.5.8-pre", "dependencies": {"abbrev": "1"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "1.0.10": {"name": "nopt", "version": "1.0.10", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "https://github.com/isaacs/nopt/raw/master/LICENSE", "type": "MIT"}, "_id": "nopt@1.0.10", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "bin": {"nopt": "./bin/nopt.js"}, "dist": {"shasum": "6ddd21bd2a31417b92727dd585f8a6f37608ebee", "tarball": "https://registry.npmjs.org/nopt/-/nopt-1.0.10.tgz", "integrity": "sha512-NWmpvLSqUrgrAC9HCuxEvb+PSloHpqVu+FqcO4eeF2h5qYRhA7ev6KvelyQAKtegUbC6RypJnlEOhd8vloNKYg==", "signatures": [{"sig": "MEUCICv5nqEW1F+gMq6T9pFC7pd7a+EHDE2ok1pSLvsqj2JcAiEAy/fM0T63GZkbmmYHKaCC1yvkmKWYOtJyU4BBTjuA0cc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/nopt.js", "engines": {"node": "*"}, "scripts": {"test": "node lib/nopt.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/nopt.git", "type": "git"}, "_npmVersion": "1.0.93", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "directories": {}, "_nodeVersion": "v0.5.9-pre", "dependencies": {"abbrev": "1"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "2.0.0": {"name": "nopt", "version": "2.0.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "https://github.com/isaacs/nopt/raw/master/LICENSE", "type": "MIT"}, "_id": "nopt@2.0.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "bin": {"nopt": "./bin/nopt.js"}, "dist": {"shasum": "ca7416f20a5e3f9c3b86180f96295fa3d0b52e0d", "tarball": "https://registry.npmjs.org/nopt/-/nopt-2.0.0.tgz", "integrity": "sha512-uVTsuT8Hm3aN3VttY+BPKw4KU9lVpI0F22UAr/I1r6+kugMr3oyhMALkycikLcdfvGRsgzCYN48DYLBFcJEUVg==", "signatures": [{"sig": "MEUCIHQ+xTjkMB1FvEL5mmE+e0ZmEAvMKFbEeY7MEo0iCDjLAiEArOCH0zUdB2M/b2y3kpXQ0M7nael0QsR7NxUptls+cXU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/nopt.js", "scripts": {"test": "node lib/nopt.js"}, "repository": {"url": "http://github.com/isaacs/nopt", "type": "git"}, "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "directories": {}, "dependencies": {"abbrev": "1"}}, "2.1.0": {"name": "nopt", "version": "2.1.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "https://github.com/isaacs/nopt/raw/master/LICENSE", "type": "MIT"}, "_id": "nopt@2.1.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "bin": {"nopt": "./bin/nopt.js"}, "dist": {"shasum": "2334c03a00c1dcb22eb1c4a4c34ebde213ee49e2", "tarball": "https://registry.npmjs.org/nopt/-/nopt-2.1.0.tgz", "integrity": "sha512-OV/WvjV0J3Fae1Kg/fFO3NhDR7IgefaDiuAvPx/KfK8zuDATjbEKEsMTLjz+bNl+ZzwH3RxaqL/+eiAPKurvOA==", "signatures": [{"sig": "MEUCIQCK6BnlfYinqnUusf+YqFut/D7EfgRcUDNChKR0noEd5gIgcO83IoYNl75IDrr+Sw6dqPb9kiCbK6EAcKtvhKP0FCE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/nopt.js", "_from": ".", "scripts": {"test": "node lib/nopt.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "http://github.com/isaacs/nopt", "type": "git"}, "_npmVersion": "1.2.1", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "directories": {}, "dependencies": {"abbrev": "1"}}, "2.1.1": {"name": "nopt", "version": "2.1.1", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "https://github.com/isaacs/nopt/raw/master/LICENSE", "type": "MIT"}, "_id": "nopt@2.1.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "bin": {"nopt": "./bin/nopt.js"}, "dist": {"shasum": "91eb7c4b017e7c00adcad1fd6d63944d0fdb75c1", "tarball": "https://registry.npmjs.org/nopt/-/nopt-2.1.1.tgz", "integrity": "sha512-iKfahTKYJbKlv1JeIV0UFT5kzNdbeKe6AY69GQWm9feJEs3/fZQkjs2fDw3T7494PDXf5U1nu1hoqwkRcLycAw==", "signatures": [{"sig": "MEYCIQCsZojATGtFL3Y/o9VkdcSYcbp3I9+v74rMdCBL5UxPmQIhAMAdhPv18xgRDKOqkJHW9CYZO3r2VejVxG8UolpkDlDK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/nopt.js", "_from": ".", "scripts": {"test": "node lib/nopt.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "http://github.com/isaacs/nopt", "type": "git"}, "_npmVersion": "1.2.1", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "directories": {}, "dependencies": {"abbrev": "1"}}, "2.1.2": {"name": "nopt", "version": "2.1.2", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "https://github.com/isaacs/nopt/raw/master/LICENSE", "type": "MIT"}, "_id": "nopt@2.1.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/isaacs/nopt/issues"}, "bin": {"nopt": "./bin/nopt.js"}, "dist": {"shasum": "6cccd977b80132a07731d6e8ce58c2c8303cf9af", "tarball": "https://registry.npmjs.org/nopt/-/nopt-2.1.2.tgz", "integrity": "sha512-x8vXm7BZ2jE1Txrxh/hO74HTuYZQEbo8edoRcANgdZ4+PCV+pbjd/xdummkmjjC7LU5EjPzlu8zEq/oxWylnKA==", "signatures": [{"sig": "MEUCIHzLP3EX5XW3f8lyHB56v5kJ6ZV0BTbgdwIoBKJCnKZPAiEAiqwC3EY58PQNsWi6FwaYwRiJMyomWviJpdeNreYefOw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/nopt.js", "_from": ".", "scripts": {"test": "node lib/nopt.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "http://github.com/isaacs/nopt", "type": "git"}, "_npmVersion": "1.3.4", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "directories": {}, "dependencies": {"abbrev": "1"}}, "2.2.0": {"name": "nopt", "version": "2.2.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "https://github.com/isaacs/nopt/raw/master/LICENSE", "type": "MIT"}, "_id": "nopt@2.2.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/nopt", "bugs": {"url": "https://github.com/isaacs/nopt/issues"}, "bin": {"nopt": "./bin/nopt.js"}, "dist": {"shasum": "3d106676f3607ac466af9bf82bd707b1501d3bd5", "tarball": "https://registry.npmjs.org/nopt/-/nopt-2.2.0.tgz", "integrity": "sha512-r0XnozFD291fNFB5BtCY/buz/6Zl+eei8m7El86awJkIF93NphAk1gkCG3R+ZTbygtSzznwyloGGQS0KzSnkjw==", "signatures": [{"sig": "MEQCIFgVBkdiCnZpz+jsCPmEtUIkwrlG6cFDmT3FOdYLkeQNAiBY5b3SlybOJ3zTXLqbQwSdCkAX26Ubd2uOPD6foh+XuQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/nopt.js", "_from": ".", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "http://github.com/isaacs/nopt", "type": "git"}, "_npmVersion": "1.4.2", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "directories": {}, "dependencies": {"abbrev": "1"}, "devDependencies": {"tap": "~0.4.8"}}, "2.2.1": {"name": "nopt", "version": "2.2.1", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "https://github.com/isaacs/nopt/raw/master/LICENSE", "type": "MIT"}, "_id": "nopt@2.2.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/nopt", "bugs": {"url": "https://github.com/isaacs/nopt/issues"}, "bin": {"nopt": "./bin/nopt.js"}, "dist": {"shasum": "2aa09b7d1768487b3b89a9c5aa52335bff0baea7", "tarball": "https://registry.npmjs.org/nopt/-/nopt-2.2.1.tgz", "integrity": "sha512-gIOTA/uJuhPwFqp+spY7VQ1satbnGlD+iQVZxI18K6hs8Evq4sX81Ml7BB5byP/LsbR2yBVtmvdEmhi7evJ6Aw==", "signatures": [{"sig": "MEUCICWoB/ensXlUIvhqwrSgl1aPSzBOfcZT7q8S8fbVEK0PAiEAyRwH1VsCFgmy25H00Yf/2lGYxeuITI+nYgC4Lgvc5Io=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/nopt.js", "_from": ".", "_shasum": "2aa09b7d1768487b3b89a9c5aa52335bff0baea7", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "http://github.com/isaacs/nopt", "type": "git"}, "_npmVersion": "1.4.7", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "directories": {}, "dependencies": {"abbrev": "1"}, "devDependencies": {"tap": "~0.4.8"}}, "3.0.0": {"name": "nopt", "version": "3.0.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "https://github.com/isaacs/nopt/raw/master/LICENSE", "type": "MIT"}, "_id": "nopt@3.0.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/nopt", "bugs": {"url": "https://github.com/isaacs/nopt/issues"}, "bin": {"nopt": "./bin/nopt.js"}, "dist": {"shasum": "4fcf4bf09123d5ee6b2f70214a4d95789b875c79", "tarball": "https://registry.npmjs.org/nopt/-/nopt-3.0.0.tgz", "integrity": "sha512-5Ixzt7GN/YCH8TeTfJN9LE91RZ4r5MnY/FWr7Nl37ZsBkEh7W3CNZxQA4Bt1/WmmZS2vb0ZLFIl7WF1u/oj+tg==", "signatures": [{"sig": "MEYCIQDxUaFQdvlotlUfbGpnLHnTjQRLUB/PulA3PA4E9IQOyQIhAL0/9LcmeSBqXwKqYlVXGDRJPNHvbc0Jf28gMzpJ1qiO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/nopt.js", "_from": ".", "_shasum": "4fcf4bf09123d5ee6b2f70214a4d95789b875c79", "gitHead": "b08ea1db39ca91cb5db37bc1b2fcf07e16386094", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "http://github.com/isaacs/nopt", "type": "git"}, "_npmVersion": "1.4.14", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "directories": {}, "dependencies": {"abbrev": "1"}, "devDependencies": {"tap": "~0.4.8"}}, "3.0.1": {"name": "nopt", "version": "3.0.1", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "https://github.com/isaacs/nopt/raw/master/LICENSE", "type": "MIT"}, "_id": "nopt@3.0.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/nopt", "bugs": {"url": "https://github.com/isaacs/nopt/issues"}, "bin": {"nopt": "./bin/nopt.js"}, "dist": {"shasum": "bce5c42446a3291f47622a370abbf158fbbacbfd", "tarball": "https://registry.npmjs.org/nopt/-/nopt-3.0.1.tgz", "integrity": "sha512-buf094p2Zp3BOwcjyI9V3zfZJVKkb/BpPl+3NHBoOqIv1vW6Bw24/ucbuO1zmbP+jPfdqvnq0lKB2FulpILeaA==", "signatures": [{"sig": "MEQCICXGHYan4xDtRyDJKD1lR9hS0cXoNZrvVVHeRSxT7fjRAiBBiJcI3oyFg9zPEAB0WZMUfnZsJoUygoSmg/noZ3g3jg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/nopt.js", "_from": ".", "_shasum": "bce5c42446a3291f47622a370abbf158fbbacbfd", "gitHead": "4296f7aba7847c198fea2da594f9e1bec02817ec", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "http://github.com/isaacs/nopt", "type": "git"}, "_npmVersion": "1.4.18", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "directories": {}, "dependencies": {"abbrev": "1"}, "devDependencies": {"tap": "~0.4.8"}}, "3.0.2": {"name": "nopt", "version": "3.0.2", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "nopt@3.0.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/nopt#readme", "bugs": {"url": "https://github.com/isaacs/nopt/issues"}, "bin": {"nopt": "./bin/nopt.js"}, "dist": {"shasum": "a82a87f9d8c3df140fe78fb29657a7a774403b5e", "tarball": "https://registry.npmjs.org/nopt/-/nopt-3.0.2.tgz", "integrity": "sha512-WoB9oKq8r03hlIeU/PvXzsE2XaI97VQMSbw+8YoybgGI/Mow+qeFi7TDpVLCFEBaAhnp8uj5f7kQsuTgTCjRFA==", "signatures": [{"sig": "MEUCIQC68vClUcaWzDPGrR8VAd7sybjQrSW64nAXW8IxlIZe0gIgUwzNnElfeWFKaVKJfuGd040ma8g+1YrrNy20rgayJfM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/nopt.js", "_from": ".", "_shasum": "a82a87f9d8c3df140fe78fb29657a7a774403b5e", "gitHead": "a0ff8dcbb29ae9da68769c9f782bd4d70746b02d", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/isaacs/nopt.git", "type": "git"}, "_npmVersion": "2.10.0", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "directories": {}, "_nodeVersion": "2.0.1", "dependencies": {"abbrev": "1"}, "devDependencies": {"tap": "~0.4.8"}}, "3.0.3": {"name": "nopt", "version": "3.0.3", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "nopt@3.0.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/nopt#readme", "bugs": {"url": "https://github.com/isaacs/nopt/issues"}, "bin": {"nopt": "./bin/nopt.js"}, "dist": {"shasum": "0e9978f33016bae0b75e3748c03bbbb71da5c530", "tarball": "https://registry.npmjs.org/nopt/-/nopt-3.0.3.tgz", "integrity": "sha512-oCT64BXP373m3EApJPNCr6xHmIZtsVtlbj6gBq1YqRjNIrjGz7DRvDk8te7fwh4J1UHxsqYKpq4mwa+lyq9Vqw==", "signatures": [{"sig": "MEQCIC2AulFputZ33fgFmtXFpepGd+RaVqF1sk5j1MmE5HTBAiB+udTWZ2o4LXWxniGKoTBewhuRABAi0+w5h0ojb8WgyQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/nopt.js", "_from": ".", "_shasum": "0e9978f33016bae0b75e3748c03bbbb71da5c530", "gitHead": "f64a64cd48d9f2660dd4e59191ff46a26397d6b1", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/isaacs/nopt.git", "type": "git"}, "_npmVersion": "2.12.0", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "directories": {}, "_nodeVersion": "2.2.1", "dependencies": {"abbrev": "1"}, "devDependencies": {"tap": "^1.2.0"}}, "3.0.4": {"name": "nopt", "version": "3.0.4", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "nopt@3.0.4", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/nopt#readme", "bugs": {"url": "https://github.com/isaacs/nopt/issues"}, "bin": {"nopt": "./bin/nopt.js"}, "dist": {"shasum": "dd63bc9c72a6e4e85b85cdc0ca314598facede5e", "tarball": "https://registry.npmjs.org/nopt/-/nopt-3.0.4.tgz", "integrity": "sha512-VNAVhwDn16LQ7mLE6jGGuF+sXNI3Go22oCVrwZiOYvweZkH6iUpd+y+BoFOSgOHdMl3u+bDkmJcejNFw/471GQ==", "signatures": [{"sig": "MEQCIAE7pI5vmGfvsu3Jnm2TIo6G3zybUCcfU8nQVUYP2ESdAiBMvm5WUgPXEwxZC9SHCZOxUKBIux/h3vzAkDS2bRiPUw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/nopt.js", "_from": ".", "_shasum": "dd63bc9c72a6e4e85b85cdc0ca314598facede5e", "gitHead": "f52626631ea1afef5a6dd9acf23ddd1466831a08", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "zkat", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/isaacs/nopt.git", "type": "git"}, "_npmVersion": "2.14.3", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "directories": {}, "_nodeVersion": "2.2.2", "dependencies": {"abbrev": "1"}, "devDependencies": {"tap": "^1.2.0"}}, "3.0.5": {"name": "nopt", "version": "3.0.5", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "nopt@3.0.5", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/nopt#readme", "bugs": {"url": "https://github.com/npm/nopt/issues"}, "bin": {"nopt": "./bin/nopt.js"}, "dist": {"shasum": "34adf7482cf70b06d24693e094c2c1e2e7fab403", "tarball": "https://registry.npmjs.org/nopt/-/nopt-3.0.5.tgz", "integrity": "sha512-JiJ1B7WkhiGhkdHXUrz5IOnrEqkXOxqhofyuK8t4LHKAmLcWj0JY0s2izJWEpuEi5h25S+k70EG45CKOoLvxlg==", "signatures": [{"sig": "MEUCIQCsWyOU0jhTQgOwYUzbWdTG7/n6Lf3kvLLtBnA37pwZ0AIgEqiahq9XH9S0uGBY0+9aNGFSJcHg77YPmp/ktbTuKTo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/nopt.js", "_from": ".", "_shasum": "34adf7482cf70b06d24693e094c2c1e2e7fab403", "gitHead": "0af9e18228fff757bcc7dccf57623423719ca255", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "othiym23", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/nopt.git", "type": "git"}, "_npmVersion": "2.14.10", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "directories": {}, "_nodeVersion": "4.2.1", "dependencies": {"abbrev": "1"}, "devDependencies": {"tap": "^1.2.0"}}, "3.0.6": {"name": "nopt", "version": "3.0.6", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "nopt@3.0.6", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/nopt#readme", "bugs": {"url": "https://github.com/npm/nopt/issues"}, "bin": {"nopt": "./bin/nopt.js"}, "dist": {"shasum": "c6465dbf08abcd4db359317f79ac68a646b28ff9", "tarball": "https://registry.npmjs.org/nopt/-/nopt-3.0.6.tgz", "integrity": "sha512-4GUt3kSEYmk4ITxzB/b9vaIDfUVWN/Ml1Fwl11IlnIG2iaJ9O6WXZ9SrYM9NLI8OCBieN2Y8SWC2oJV0RQ7qYg==", "signatures": [{"sig": "MEUCIQDAQveo4hCC5nqRoGHUV7/wOe+95EQRcc/LEVjoPWIuAwIgSAqvdOoaPh0NI3h+77OwDRd7wDr0wM4ADP6ppzRxzPU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/nopt.js", "_from": ".", "_shasum": "c6465dbf08abcd4db359317f79ac68a646b28ff9", "gitHead": "10a750c9bb99c1950160353459e733ac2aa18cb6", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "othiym23", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/nopt.git", "type": "git"}, "_npmVersion": "2.14.10", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "directories": {}, "_nodeVersion": "4.2.1", "dependencies": {"abbrev": "1"}, "devDependencies": {"tap": "^1.2.0"}}, "4.0.0": {"name": "nopt", "version": "4.0.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "nopt@4.0.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/nopt#readme", "bugs": {"url": "https://github.com/npm/nopt/issues"}, "bin": {"nopt": "./bin/nopt.js"}, "dist": {"shasum": "a4f9c541d2f84e0e2288057125fefe7329836694", "tarball": "https://registry.npmjs.org/nopt/-/nopt-4.0.0.tgz", "integrity": "sha512-+iVDulYPQfPGupAup3iAmQJC+DKRqTCP3tBrnMkczFLp/VIMFQduspOmnWQzLIPsYpBCRfUvzlQU7YPTPqmQkg==", "signatures": [{"sig": "MEUCIQCuHIKM3csQB0VO5Us6hgGKgfzLLI1ai9cmDbNmT0a6SwIgFFMfV9gVL6KEHySzYEWyDvLpeGWwnd0t6QxzYsC5ulk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/nopt.js", "_from": ".", "_shasum": "a4f9c541d2f84e0e2288057125fefe7329836694", "gitHead": "ae1fb2967063a47ad1f0b31a7cd335cef0f6dd6c", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "othiym23", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/nopt.git", "type": "git"}, "_npmVersion": "4.0.5", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "directories": {}, "_nodeVersion": "7.2.0", "dependencies": {"osenv": "^0.1.4", "abbrev": "1"}, "devDependencies": {"tap": "^8.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/nopt-4.0.0.tgz_1481672322129_0.9774678370449692", "host": "packages-12-west.internal.npmjs.com"}}, "4.0.1": {"name": "nopt", "version": "4.0.1", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "nopt@4.0.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/nopt#readme", "bugs": {"url": "https://github.com/npm/nopt/issues"}, "bin": {"nopt": "./bin/nopt.js"}, "dist": {"shasum": "d0d4685afd5415193c8c7505602d0d17cd64474d", "tarball": "https://registry.npmjs.org/nopt/-/nopt-4.0.1.tgz", "integrity": "sha512-+5XZFpQZEY0cg5JaxLwGxDlKNKYxuXwGt8/Oi3UXm5/4ymrJve9d2CURituxv3rSrVCGZj4m1U1JlHTdcKt2Ng==", "signatures": [{"sig": "MEUCIDMiR7+IrmP/ceoKN0oWo+Fq+ObHMVSlSW9nzdgGw/GsAiEAn9MmeEyBgqvr7ebUvqnhggncLdKlOPgA5VClKmxiFUg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/nopt.js", "_from": ".", "_shasum": "d0d4685afd5415193c8c7505602d0d17cd64474d", "gitHead": "24921187dc52825d628042c9708bbd8e8734698c", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "othiym23", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/nopt.git", "type": "git"}, "_npmVersion": "4.0.5", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "directories": {}, "_nodeVersion": "7.2.0", "dependencies": {"osenv": "^0.1.4", "abbrev": "1"}, "devDependencies": {"tap": "^8.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/nopt-4.0.1.tgz_1481739985863_0.18861285015009344", "host": "packages-12-west.internal.npmjs.com"}}, "4.0.2": {"name": "nopt", "version": "4.0.2", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "nopt@4.0.2", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "mike<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/nopt#readme", "bugs": {"url": "https://github.com/npm/nopt/issues"}, "bin": {"nopt": "bin/nopt.js"}, "dist": {"shasum": "ccdb22d06b9990dab9704033bb31209dbd1f5b1e", "tarball": "https://registry.npmjs.org/nopt/-/nopt-4.0.2.tgz", "fileCount": 12, "integrity": "sha512-S+Q/CsNK7M1EFtMslCR/Xweq9WSuKy3O2fwITYEu9mtoBF/Du3yqKfiGfEdGBHhvN2BQUsPW+2fIg+7moyP6Rw==", "signatures": [{"sig": "MEYCIQDNUKgacRLTBSwxWwt9XPQzG0rrdsKZ1RvsR3++RvoCzAIhAIQB5AVZmNXhiI1dOe1eidCRoXRpdC9/x1WShcLU3xzt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 152220, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeZWksCRA9TVsSAnZWagAA2UAP/0YgqvG3HIICFuv+mSE2\nBxbfjS+G6pnco896MrH6sjnYjorq6xfkySGG5TMCT4av2VJTIJ+ntTUo8vey\noyUyt6gbmT7aTgm3/o1/LjLdIF0EfT4GBJ/UiBgjWEPz3AByfb8oCf0Ag2Sm\nPXQWeswa8mT4umuDhLRjgFkyAlJW6b8kjdpVSM6a3faLPkVSvj3942eqrEYP\nBZTb24IP63JxjAIj2sYLqOc2yQzIsouO6K153IStjp6hQXA2VagSaZCZtimA\nOftOV2Iomw9v38lJ6r16ss4a+PGf0rap4W7XEa7fpSmsDJ0NxO0jyDdfKNmV\nJi9g35n/UZ/me3ilQ8ZgyTPjVNypFk5aU0JoQYqz/+6UxdNtztoS5ZrVAax/\nsHqSlk6G2zruG67CqIbHzo29pby64/9R++RQTIK4lFkso588gEkYI89cgGIn\n63fEAw32qSCQQsAinVHixgv8o/VdBjUn2DvZLZDYzfnbeTqXtkVYsBQmQPuo\nS69d/HuTFDLQSuljqDYsFHnLNlzpr8TOr2kitStJzRwUXAr2anOvWcDIuIzf\nH28y+4YwCRqI/FS5GNxzcBgjzrZAoDHeIKLXd6dfE9Oc+e8B8yXvG3Pxa+ZA\nMuHbQgioDhNS8sqTpjoqLqmtZDHngW2bYuHE6wytEHN5b/yyytwn5oBcZw1+\n5XJq\r\n=FMqs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/nopt.js", "gitHead": "53f158e3edf55fc44b5275a6270abe1b1a422c5a", "scripts": {"test": "tap test/*.js", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/nopt.git", "type": "git"}, "_npmVersion": "6.14.2", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "directories": {}, "_nodeVersion": "13.9.0", "dependencies": {"osenv": "^0.1.4", "abbrev": "1"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.10.6"}, "_npmOperationalInternal": {"tmp": "tmp/nopt_4.0.2_1583704363758_0.020218475236866817", "host": "s3://npm-registry-packages"}}, "4.0.3": {"name": "nopt", "version": "4.0.3", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "nopt@4.0.3", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "mike<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/nopt#readme", "bugs": {"url": "https://github.com/npm/nopt/issues"}, "bin": {"nopt": "bin/nopt.js"}, "dist": {"shasum": "a375cad9d02fd921278d954c2254d5aa57e15e48", "tarball": "https://registry.npmjs.org/nopt/-/nopt-4.0.3.tgz", "fileCount": 6, "integrity": "sha512-CvaGwVMztSMJLOeXPrez7fyfObdZqNUK1cPAEzLHrTybIua9pMdmmPR5YwtfNftIOMv3DPUhFaxsZMNTQO20Kg==", "signatures": [{"sig": "MEQCICmFJU14Qvz1vhBhoY+KAnbg0U+NlLJPVkDjvHoZDIaiAiAcYsOsPBhtilxnycoVAFuZEUi3PCRLO5G7HcQXj0sQ7A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25815, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeZWl0CRA9TVsSAnZWagAAwAYP/jratQa5m7DIaS1qetL/\n0ECOaoClnnuJS4D4Tuc/+6FOaidSGyjdjUgsBR6F+RN9wcvRAwh9s7rC3mz6\nsPO2D40RI7yeX9kKhKCwNpH6q3SM/BL5DG0VA5Xf4iJ7fa1Cox7/qlgrRib3\nB01yvqX9XO1tpDEfQAi3B9JxCE5IWuEmOR2v6i69pPQbw1HaJf6SCuIjInjc\nuj1jTLOB/vtD21CuQOKEOj+gKg18eyuQcgXOKHWDAeYfps79ucP9VKHWvnMT\neB9N2PB+VeAfoO8jgDYxc+mPFP6tlOCV6sn/+c9Ak/mUanzhmQYJbkf7TYtC\n9e+zAc0AcvqM+SQEkzkcPwVxmC7M0rQOFoxQ/p7L1buU0gxBFAqUDIE+aDmu\n74aMO0Qocbq7YpwL92GNfU8rcfoRIPZwmAhgTZn2rdsrcpA5dSqUPzCi1c/K\nVeNNoTeBDb2J3Hu/ZACX1Z+7VjlxS49irDqJDRnE/93Sg1wZU7bVfG66+KVK\nwanHViGa77G8SgpO9OiJy84ElpiPnL91s25BCGhLhhawCJxHrak6sutdV9BW\nnF4dtzDO5TUIaPrPMEgBAGsvkVdrLPTO5FvBOQPRqf7h2NsOf4/U6P2yZOI6\n+Mkd6Rif2TdaqtoujHFncKm3LmE3jiIJSbb/R1r9+vvE0mxmuj7fSOF8uuIR\nrzwT\r\n=udJ+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/nopt.js", "gitHead": "2b1ef35dfaaee7a79a7e1b5ed83dcda2853214d4", "scripts": {"test": "tap test/*.js", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/nopt.git", "type": "git"}, "_npmVersion": "6.14.2", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "directories": {}, "_nodeVersion": "13.9.0", "dependencies": {"osenv": "^0.1.4", "abbrev": "1"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.10.6"}, "_npmOperationalInternal": {"tmp": "tmp/nopt_4.0.3_1583704435982_0.1350573274257274", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "nopt", "version": "5.0.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "nopt@5.0.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "my<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/nopt#readme", "bugs": {"url": "https://github.com/npm/nopt/issues"}, "bin": {"nopt": "bin/nopt.js"}, "dist": {"shasum": "530942bb58a512fccafe53fe210f13a25355dc88", "tarball": "https://registry.npmjs.org/nopt/-/nopt-5.0.0.tgz", "fileCount": 6, "integrity": "sha512-Tbj67rffqceeLpcRXrT7vKAN8CwfPeIBgM7E6iBkmKLV7bEMwpGgYLGv0jACUsECaa/vuxP0IjEont6umdMgtQ==", "signatures": [{"sig": "MEYCIQCPHXZMXpP4mpnnUMAdZglRWH8KUS6GvQ19QIdVghAPWQIhALtZwmltm4XJteZCbx3EI/7U9FRVzR/b8m0RZEUKCTwP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25840, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfOjqHCRA9TVsSAnZWagAAru4P/1cRNObNIaZrhtlfAP2C\nPbMyT2IzO5MPiIvZyBvf3jW2JMzlEwKq9g5vLnA3ZdpYhtxVsr9SmKMj91RW\npmzbDYAIZpivc6bYpyMwOEMk+99/KI3+tLXpvkGmoahgFHAmUBZQOCpzgtks\nktksdHPMhQvj0BkEreVOO/+mTJl1MI/QmpR2XeikCMcsLnha89TcceCFL7St\nH1zYAX6RfhuScPQ6rv/3f88SN4iui2mTfDOvkgADr/1Ru9lfZWdV+XMAJxr8\n+Sih4rBrTcB9BLg8rjl5YQYpQdhICW3A+7D/4hWNHVPjBC5wxgZGwlshoi9k\nNXcnfQ3/OBa0WknJwvQnM0G9za+nV1jIa9cCUt7Q8o8ua0MOYuVXlSAkgH2o\n67uwmhBneWRmw4/T1liCbZVVnX+sKbHewmTjz8tk4Y1QGOdYBVaMxf4uxrC1\n9PYkU6eGleyMdaohz4E1Os6+X78glhdOk5nAJGRpSVksnZFafYcA8y0kGyGh\nm7kD3nXhS2KgZ+mBBl9w/i7j9nYtFWIRawAjrD8nEOde0he/QLt9uq+m6q9u\nsJBLMbY3JebKMy00jtEQ+BVLrm9SIho/84EqTvyxTSX3O9a4nStZA3rkT1Ea\n8hjSyAXj2fpFslc0+YBqPYUCAhGcZB8FUY+2NEgYw0TztMsIQnCFl3eliipl\nUfFu\r\n=CtCh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/nopt.js", "engines": {"node": ">=6"}, "gitHead": "fee9d25fb8518cf202eb0bbdfa6e1c516ee8dff7", "scripts": {"test": "tap test/*.js", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/nopt.git", "type": "git"}, "_npmVersion": "7.0.0-beta.4", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "directories": {}, "_nodeVersion": "14.8.0", "dependencies": {"abbrev": "1"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.10.6"}, "_npmOperationalInternal": {"tmp": "tmp/nopt_5.0.0_1597651591432_0.3563498258220257", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "nopt", "version": "6.0.0", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "nopt@6.0.0", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/nopt#readme", "bugs": {"url": "https://github.com/npm/nopt/issues"}, "bin": {"nopt": "bin/nopt.js"}, "tap": {"lines": 87, "branches": 81, "functions": 91, "statements": 87}, "dist": {"shasum": "245801d8ebf409c6df22ab9d95b65e1309cdb16d", "tarball": "https://registry.npmjs.org/nopt/-/nopt-6.0.0.tgz", "fileCount": 5, "integrity": "sha512-ZwLpbTgdhuZUnZzjd7nb1ZV+4DoiC6/sfiVKok72ym/4Tlf+DFdlHYmT2JPmcNNWV6Pi3SDf1kT+A4r9RTuT9g==", "signatures": [{"sig": "MEQCIDU1NVk8kMNveW2BV2+frDkfZTlQ7k0p8/Y47/1/ORt2AiAa5Y74MaA6iSbKq/SxcQSZ/WXa9RxNOnydDzxiwvk58A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23946, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2HEWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp9hg/+IPBeHM5GKcA+Q3lqdhKzMIr0ISKi+qprqLO5WWG95+CP/rs2\r\nlfROhxbIm1oc79siFfGOAxocy86JasuCVVHK5pWVs25JM0TcUALa0TBkiQCt\r\nEthxDqRTAQQ+UVuACAcaPpMf7KlM2dvvMKbuDpCgkGjFyvUpdPhvzh/XxO1q\r\nXXJW6+G7FBxTVG3rmuvuESfgWNNNy/k7Xk4hRgBNpGahAl5AAIM6klHYHk55\r\nOmWHvzMAmv7CjdfkgvPDLyapY85gtUF5G3W22czfOgHPBuKyqhHGvQy1g6E6\r\nDIKWAjW5N95n6s19bM0MB8MwTSPZA+NXVbeqdIwdWY7OjjQS97elm4TGH8nP\r\nD6gX8Q6977DEu1VjP4U5RltHDwlJhMJ8zmdJ5jPlwWAcSzuffGAT/iH1zYlc\r\n3PFKwWyTJmt4/JtS5NLKj2+X6fQ6bKVY7v85sOon3wscvX8a1kFoN61GqtMr\r\n/pLXOc8RhXlkxvDoDC140eXJwN64Dji8xt8AunG93BJXxneB5Dcq1bDFCu4d\r\nq4nrwGCOHFmsZ6C8p/dIqWgAErZfLDFT0OZuk7poslZdJF8ITdXxb0M1BsYT\r\np6NPZqLjr52oPsOBJjLBLZ3siGE2bguonxRrh4bVPbhLGVN1TiDu6VA4fpyL\r\nAuhv107j9EzZapuIFGpWS2VNMg0hHzZV6GE=\r\n=vcSa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/nopt.js", "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "gitHead": "d4ad62dacb376e953bc61984e48998110cb37382", "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "gar", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/nopt.git", "type": "git"}, "_npmVersion": "8.14.0", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "directories": {}, "templateOSS": {"version": "3.5.0", "windowsCI": false, "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "18.6.0", "dependencies": {"abbrev": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.3.0", "@npmcli/template-oss": "3.5.0", "@npmcli/eslint-config": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/nopt_6.0.0_1658351894630_0.16149864199157515", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "nopt", "version": "7.0.0", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "nopt@7.0.0", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/nopt#readme", "bugs": {"url": "https://github.com/npm/nopt/issues"}, "bin": {"nopt": "bin/nopt.js"}, "tap": {"lines": 87, "nyc-arg": ["--exclude", "tap-snapshots/**"], "branches": 81, "functions": 91, "statements": 87}, "dist": {"shasum": "09220f930c072109c98ef8aaf39e1d5f0ff9b0d4", "tarball": "https://registry.npmjs.org/nopt/-/nopt-7.0.0.tgz", "fileCount": 5, "integrity": "sha512-e6Qw1rcrGoSxEH0hQ4GBSdUjkMOtXGhGFXdNT/3ZR0S37eR9DMj5za3dEDWE6o1T3/DP8ZOsPP4MIiky0c3QeA==", "signatures": [{"sig": "MEQCICkdBH9faMr/4xRVU7k0ArDYn4gA08GMeDbKQQVEjxnkAiBeIudXPJE7knZpBdYAEhi75GuyFmnpTCNzQjEhR0trfQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23895, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjYn/AACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrY2g//ZyLZwbNxoMToxnvF86xkiVijjmr1bXErQ1dmT5g/6RRWtq2u\r\nhVvxj8kbU/y1Hy8Gw3Jf9TYnCLWp34l87+xxjF95nwAoI7n4mmeKgmlrSEkg\r\nqgjhqbiVl5zLsHcsFlmLOZ1VgFjf2SU7Gus6jHGw2x61EcwV1NEbKFVQciNz\r\nqACb20nXSmAyqmfMbe3QwhFKY/wxV3BKpgj+jMNUD1iH0enSMTEM34Mh51Il\r\ncDfLKwVGskji2EXR6HhQ48x4GHAoxyt2e7ZKuPKUp5adnJJvgz6p/Lc5rYVr\r\nsONnP8lg3e9L8tNQMEZLgkIvAdkTjiUVj1ehYCXZJB5wwGaFqRcEFy5RAS6n\r\nA0OibDHJcxs+QMErf5DKrPvRF4NHBJQueCasyHQBbTqdQfzDH56juao1nTIf\r\nyg64/SJTk0sGkjrEoJygheiBoRAQlr0ztKuXUtANjEP/r7FMU+KbRK4GhyO3\r\nCzZ7/DJexdbyRXUMf35nPDSeWHyZakk9N7qvuVCcEDZMKjMoKqeOVq1UGYB8\r\nTuqGXvD230Vjc71crn88t8cXbWHsyO6zUudsECmw852ekd/bHgfHWHIxVIS9\r\nXK94lkPGvj1TJJdMhx1zgMK5XERmuFoiqPP1bIVAQbBfO9dolMJ3D4UvWX3l\r\nZzkSWNkztnGYUGEXUtKz1X9pWjFHDbY89+Y=\r\n=fxjA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/nopt.js", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "gitHead": "d7a16ec25d568347269980153a65a19c0dfeace9", "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "gar", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/nopt.git", "type": "git"}, "_npmVersion": "9.0.1", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "directories": {}, "templateOSS": {"version": "4.8.0", "windowsCI": false, "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "18.12.0", "dependencies": {"abbrev": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.3.0", "@npmcli/template-oss": "4.8.0", "@npmcli/eslint-config": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/nopt_7.0.0_1667399616072_0.9472621583883882", "host": "s3://npm-registry-packages"}}, "7.1.0": {"name": "nopt", "version": "7.1.0", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "nopt@7.1.0", "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/nopt#readme", "bugs": {"url": "https://github.com/npm/nopt/issues"}, "bin": {"nopt": "bin/nopt.js"}, "tap": {"lines": 91, "nyc-arg": ["--exclude", "tap-snapshots/**"], "branches": 87, "statements": 91}, "dist": {"shasum": "91f6a3366182176e72ecab93a09c19b63b485f28", "tarball": "https://registry.npmjs.org/nopt/-/nopt-7.1.0.tgz", "fileCount": 8, "integrity": "sha512-ZFPLe9Iu0tnx7oWhFxAo4s7QTn8+NNDDxYNaKLjE7Dp0tbakQ3M1QhQzsnzXHQBTUO3K9BmwaxnyO8Ayn2I95Q==", "signatures": [{"sig": "MEUCIG3tJnYBO4YfuraHWmbFvDkEmHknyGz0lh02V13YkNfUAiEAjXw1AViSTg4EOiHrVkTaSG6L1QBFV4C2uc9Hfa9HVCc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25136, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkGfwPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqT0A//fk6yKKt572DJ3PSV2fArB5TSdCnffdFZl0aOWtjYNISRjEuY\r\n+7AcJ0w1nostyv0KiInqFILNK6oXjYk3Oo+4wgup+7kcWWZXMBy6/Qm4vv/2\r\niNpMwwtWkVkRm5+fx1BxqEMCkeZ1Mo9SSJFdk9ilEQrRjsJAXX9ncSpXFKyy\r\n5s5lPpSI68sDLVr51t7fO22JVxo1VhAGpSb7G//PZgwJ6/rvQCuKCyY26RRT\r\ntmu3RNdTPLaGdWNh0JeuTK+/AvieqYY6sUXyh6u3nancgvPJtqBxf7/cMPqG\r\noe4jPYh9WOnOlt9acIu3XT2fx2H1GUQQQh2z4iQjbmXo0aDNE9itiuntmfR/\r\nhsRVb32TrKN+BE71foaMNq9FrKxiv/4E8fxbZg0EynBSsBrgbTRDiDclD8IF\r\n4YO4Dfp+CuyU5bIbrGONrRC6u4+qg39fdZOEt6tFGEammPztXqyHROlz1NNd\r\nJKatynGocHLwppPHxT52vTD+QjYqkdcrA3IixRXQ6fj4vyHvqH2zKIoN7WIg\r\n4PoyY2m+2icqlMWt2hVTcsXRyscLnvwuqx+uLKpuB2lzjRT0AHpbDj0p15y4\r\n5eypiGhQPetc6zT84YzYLbz6cfq7SgE26Ez+kTzOf5Uss9dBY9D0yO3o59oJ\r\nwEzqhyMQ7256DZ4JiGmQzdMz20u6Pos9ddY=\r\n=2+2R\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/nopt.js", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "gar", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/nopt.git", "type": "git"}, "_npmVersion": "9.6.1", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "directories": {}, "templateOSS": {"version": "4.12.0", "windowsCI": false, "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "18.13.0", "dependencies": {"abbrev": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.3.0", "@npmcli/template-oss": "4.12.0", "@npmcli/eslint-config": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/nopt_7.1.0_1679424527041_0.8887091932430908", "host": "s3://npm-registry-packages"}}, "7.2.0": {"name": "nopt", "version": "7.2.0", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "nopt@7.2.0", "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/nopt#readme", "bugs": {"url": "https://github.com/npm/nopt/issues"}, "bin": {"nopt": "bin/nopt.js"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}, "dist": {"shasum": "067378c68116f602f552876194fd11f1292503d7", "tarball": "https://registry.npmjs.org/nopt/-/nopt-7.2.0.tgz", "fileCount": 8, "integrity": "sha512-CVDtwCdhYIvnAzFoJ6NJ6dX3oga9/HyciQDnG1vQDjSLMeKLJ4A93ZqYKDrgYSr1FBY5/hMYC+2VCi24pgpkGA==", "signatures": [{"sig": "MEQCIBPMY2qIFCaF0E6r6R6cn/mmemO+fr8EvP+iKxM64XEOAiAYm4AnuEnNSlGxQRO9ej6Z38/Pks1qr1kaxUJwGBy8zQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/nopt@7.2.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 26137}, "main": "lib/nopt.js", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "gitHead": "1d7f39b0dc94bd6ccd154907f2c64456759ae215", "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/nopt.git", "type": "git"}, "_npmVersion": "9.7.1", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "directories": {}, "templateOSS": {"publish": true, "version": "4.15.1", "windowsCI": false, "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "18.16.0", "dependencies": {"abbrev": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.3.0", "@npmcli/template-oss": "4.15.1", "@npmcli/eslint-config": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/nopt_7.2.0_1686845791390_0.5960965762719304", "host": "s3://npm-registry-packages"}}, "7.2.1": {"name": "nopt", "version": "7.2.1", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "nopt@7.2.1", "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/nopt#readme", "bugs": {"url": "https://github.com/npm/nopt/issues"}, "bin": {"nopt": "bin/nopt.js"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}, "dist": {"shasum": "1cac0eab9b8e97c9093338446eddd40b2c8ca1e7", "tarball": "https://registry.npmjs.org/nopt/-/nopt-7.2.1.tgz", "fileCount": 8, "integrity": "sha512-taM24ViiimT/XntxbPyJQzCG+p4EKOpgD3mxFwW38mGjVUrfERQOeY4EDHjdnptttfHuHQXFx+lTP08Q+mLa/w==", "signatures": [{"sig": "MEUCIQDsGsywXebnr3VNxXxSfSuZKiPrOTyj3J5TmMy6Wh+dIQIgOqooSxmxbTSlbegMSG8W/S9pHAsXDSFZAdCxO9OQqaU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/nopt@7.2.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 26203}, "main": "lib/nopt.js", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "gitHead": "6d33cd7a772c9594d42417c69e7adeb497b16b03", "scripts": {"lint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/nopt.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "directories": {}, "templateOSS": {"publish": true, "version": "4.22.0", "windowsCI": false, "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "22.1.0", "dependencies": {"abbrev": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.3.0", "@npmcli/template-oss": "4.22.0", "@npmcli/eslint-config": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/nopt_7.2.1_1714785037461_0.2372552074127241", "host": "s3://npm-registry-packages"}}, "8.0.0": {"name": "nopt", "version": "8.0.0", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "nopt@8.0.0", "maintainers": [{"name": "hashtagchris", "email": "<EMAIL>"}, {"name": "reggi", "email": "<EMAIL>"}, {"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/nopt#readme", "bugs": {"url": "https://github.com/npm/nopt/issues"}, "bin": {"nopt": "bin/nopt.js"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}, "dist": {"shasum": "644f1e78da564b70e3606ab8db4836b0e32e198a", "tarball": "https://registry.npmjs.org/nopt/-/nopt-8.0.0.tgz", "fileCount": 8, "integrity": "sha512-1L/fTJ4UmV/lUxT2Uf006pfZKTvAgCF+chz+0OgBHO8u2Z67pE7AaAUUj7CJy0lXqHmymUvGFt6NE9R3HER0yw==", "signatures": [{"sig": "MEUCICzSXe/kmq9q6ZkryrJvfla0DQkyKTocWcrKxEKtZnsmAiEAzQ1CCsHOkBRNvX2CNfT4Q2HpPynk7R4v+L5Rnr+IOK0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/nopt@8.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 26225}, "main": "lib/nopt.js", "engines": {"node": "^18.17.0 || >=20.5.0"}, "gitHead": "a3b0bfc867502c4ca2faba36a217cb7af029227c", "scripts": {"lint": "npm run eslint", "snap": "tap", "test": "tap", "eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\"", "lintfix": "npm run eslint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/nopt.git", "type": "git"}, "_npmVersion": "10.8.3", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "directories": {}, "templateOSS": {"publish": true, "version": "4.23.3", "windowsCI": false, "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "22.8.0", "dependencies": {"abbrev": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.3.0", "@npmcli/template-oss": "4.23.3", "@npmcli/eslint-config": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/nopt_8.0.0_1725487951412_0.24294595974179733", "host": "s3://npm-registry-packages"}}, "8.1.0": {"name": "nopt", "version": "8.1.0", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "nopt@8.1.0", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "reggi", "email": "<EMAIL>"}, {"name": "hashtagchris", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/nopt#readme", "bugs": {"url": "https://github.com/npm/nopt/issues"}, "bin": {"nopt": "bin/nopt.js"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}, "dist": {"shasum": "b11d38caf0f8643ce885818518064127f602eae3", "tarball": "https://registry.npmjs.org/nopt/-/nopt-8.1.0.tgz", "fileCount": 8, "integrity": "sha512-ieGu42u/Qsa4TFktmaKEwM6MQH0pOWnaB3htzh0JRtx84+Mebc0cbZYN5bC+6WTZ4+77xrL9Pn5m7CV6VIkV7A==", "signatures": [{"sig": "MEUCIQCZL81e8uR+danfKFoFUHkO//R1eMRbfTZ0ROPeVoh7LAIgLoH5iRBtSGeZjMiid2sDN4WSnBEXqAnnOqUxDDnRRFg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/nopt@8.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 28128}, "main": "lib/nopt.js", "engines": {"node": "^18.17.0 || >=20.5.0"}, "gitHead": "e8cc07120b4acb938a182fa7a1cc7453d0c4cbfa", "scripts": {"lint": "npm run eslint", "snap": "tap", "test": "tap", "eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\"", "lintfix": "npm run eslint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/nopt.git", "type": "git"}, "_npmVersion": "11.0.0", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "directories": {}, "templateOSS": {"publish": true, "version": "4.23.6", "windowsCI": false, "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "22.12.0", "dependencies": {"abbrev": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.3.0", "@npmcli/template-oss": "4.23.6", "@npmcli/eslint-config": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/nopt_8.1.0_1737479745037_0.14450234149419994", "host": "s3://npm-registry-packages-npm-production"}}}, "time": {"created": "2011-03-30T03:23:55.464Z", "modified": "2025-05-14T20:03:58.730Z", "1.0.0": "2011-03-30T03:23:56.092Z", "1.0.1": "2011-03-30T06:58:18.917Z", "1.0.2": "2011-03-31T01:07:58.593Z", "1.0.3": "2011-03-31T01:12:32.481Z", "1.0.4": "2011-03-31T04:42:56.217Z", "1.0.5": "2011-04-29T19:50:02.032Z", "1.0.6": "2011-07-06T03:49:31.397Z", "1.0.7": "2011-09-08T17:49:45.337Z", "1.0.8": "2011-09-15T21:26:19.372Z", "1.0.9": "2011-09-22T21:20:18.314Z", "1.0.10": "2011-10-05T21:47:05.876Z", "2.0.0": "2012-07-23T22:36:57.179Z", "2.1.0": "2013-01-17T20:23:13.858Z", "2.1.1": "2013-01-18T16:26:25.780Z", "2.1.2": "2013-07-17T15:24:56.574Z", "2.2.0": "2014-02-16T20:54:31.122Z", "2.2.1": "2014-04-28T21:59:11.261Z", "3.0.0": "2014-06-06T20:36:37.144Z", "3.0.1": "2014-07-01T17:12:00.014Z", "3.0.2": "2015-05-19T01:38:16.099Z", "3.0.3": "2015-06-23T01:16:18.259Z", "3.0.4": "2015-09-10T01:29:59.419Z", "3.0.5": "2015-11-12T21:23:16.377Z", "3.0.6": "2015-11-12T21:58:26.454Z", "4.0.0": "2016-12-13T23:38:42.368Z", "4.0.1": "2016-12-14T18:26:26.094Z", "4.0.2": "2020-03-08T21:52:43.958Z", "4.0.3": "2020-03-08T21:53:56.097Z", "5.0.0": "2020-08-17T08:06:31.528Z", "6.0.0": "2022-07-20T21:18:14.824Z", "7.0.0": "2022-11-02T14:33:36.290Z", "7.1.0": "2023-03-21T18:48:47.189Z", "7.2.0": "2023-06-15T16:16:31.586Z", "7.2.1": "2024-05-04T01:10:37.691Z", "8.0.0": "2024-09-04T22:12:31.585Z", "8.1.0": "2025-01-21T17:15:45.225Z"}, "bugs": {"url": "https://github.com/npm/nopt/issues"}, "author": {"name": "GitHub Inc."}, "license": "ISC", "homepage": "https://github.com/npm/nopt#readme", "repository": {"url": "git+https://github.com/npm/nopt.git", "type": "git"}, "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "maintainers": [{"email": "<EMAIL>", "name": "gar"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "npm-cli-ops"}, {"email": "<EMAIL>", "name": "reggi"}, {"email": "<EMAIL>", "name": "hashtagchris"}, {"email": "<EMAIL>", "name": "owlstronaut"}], "readme": "If you want to write an option parser, and have it be good, there are\ntwo ways to do it.  The Right Way, and the Wrong Way.\n\nThe Wrong Way is to sit down and write an option parser.  We've all done\nthat.\n\nThe Right Way is to write some complex configurable program with so many\noptions that you hit the limit of your frustration just trying to\nmanage them all, and defer it with duct-tape solutions until you see\nexactly to the core of the problem, and finally snap and write an\nawesome option parser.\n\nIf you want to write an option parser, don't write an option parser.\nWrite a package manager, or a source control system, or a service\nrestarter, or an operating system.  You probably won't end up with a\ngood one of those, but if you don't give up, and you are relentless and\ndiligent enough in your procrastination, you may just end up with a very\nnice option parser.\n\n## USAGE\n\n```javascript\n// my-program.js\nvar nopt = require(\"nopt\")\n  , Stream = require(\"stream\").Stream\n  , path = require(\"path\")\n  , knownOpts = { \"foo\" : [String, null]\n                , \"bar\" : [Stream, Number]\n                , \"baz\" : path\n                , \"bloo\" : [ \"big\", \"medium\", \"small\" ]\n                , \"flag\" : <PERSON><PERSON>an\n                , \"pick\" : <PERSON><PERSON>an\n                , \"many1\" : [String, Array]\n                , \"many2\" : [path, Array]\n                }\n  , shortHands = { \"foofoo\" : [\"--foo\", \"Mr. Foo\"]\n                 , \"b7\" : [\"--bar\", \"7\"]\n                 , \"m\" : [\"--bloo\", \"medium\"]\n                 , \"p\" : [\"--pick\"]\n                 , \"f\" : [\"--flag\"]\n                 }\n             // everything is optional.\n             // knownOpts and shorthands default to {}\n             // arg list defaults to process.argv\n             // slice defaults to 2\n  , parsed = nopt(knownOpts, shortHands, process.argv, 2)\nconsole.log(parsed)\n```\n\nThis would give you support for any of the following:\n\n```console\n$ node my-program.js --foo \"blerp\" --no-flag\n{ \"foo\" : \"blerp\", \"flag\" : false }\n\n$ node my-program.js ---bar 7 --foo \"Mr. Hand\" --flag\n{ bar: 7, foo: \"Mr. Hand\", flag: true }\n\n$ node my-program.js --foo \"blerp\" -f -----p\n{ foo: \"blerp\", flag: true, pick: true }\n\n$ node my-program.js -fp --foofoo\n{ foo: \"Mr. Foo\", flag: true, pick: true }\n\n$ node my-program.js --foofoo -- -fp  # -- stops the flag parsing.\n{ foo: \"Mr. Foo\", argv: { remain: [\"-fp\"] } }\n\n$ node my-program.js --blatzk -fp # unknown opts are ok.\n{ blatzk: true, flag: true, pick: true }\n\n$ node my-program.js --blatzk=1000 -fp # but you need to use = if they have a value\n{ blatzk: 1000, flag: true, pick: true }\n\n$ node my-program.js --no-blatzk -fp # unless they start with \"no-\"\n{ blatzk: false, flag: true, pick: true }\n\n$ node my-program.js --baz b/a/z # known paths are resolved.\n{ baz: \"/Users/<USER>/b/a/z\" }\n\n# if Array is one of the types, then it can take many\n# values, and will always be an array.  The other types provided\n# specify what types are allowed in the list.\n\n$ node my-program.js --many1 5 --many1 null --many1 foo\n{ many1: [\"5\", \"null\", \"foo\"] }\n\n$ node my-program.js --many2 foo --many2 bar\n{ many2: [\"/path/to/foo\", \"path/to/bar\"] }\n```\n\nRead the tests at the bottom of `lib/nopt.js` for more examples of\nwhat this puppy can do.\n\n## Types\n\nThe following types are supported, and defined on `nopt.typeDefs`\n\n* String: A normal string.  No parsing is done.\n* path: A file system path.  Gets resolved against cwd if not absolute.\n* url: A url.  If it doesn't parse, it isn't accepted.\n* Number: Must be numeric.\n* Date: Must parse as a date. If it does, and `Date` is one of the options,\n  then it will return a Date object, not a string.\n* Boolean: Must be either `true` or `false`.  If an option is a boolean,\n  then it does not need a value, and its presence will imply `true` as\n  the value.  To negate boolean flags, do `--no-whatever` or `--whatever\n  false`\n* NaN: Means that the option is strictly not allowed.  Any value will\n  fail.\n* Stream: An object matching the \"Stream\" class in node.  Valuable\n  for use when validating programmatically.  (npm uses this to let you\n  supply any WriteStream on the `outfd` and `logfd` config options.)\n* Array: If `Array` is specified as one of the types, then the value\n  will be parsed as a list of options.  This means that multiple values\n  can be specified, and that the value will always be an array.\n\nIf a type is an array of values not on this list, then those are\nconsidered valid values.  For instance, in the example above, the\n`--bloo` option can only be one of `\"big\"`, `\"medium\"`, or `\"small\"`,\nand any other value will be rejected.\n\nWhen parsing unknown fields, `\"true\"`, `\"false\"`, and `\"null\"` will be\ninterpreted as their JavaScript equivalents.\n\nYou can also mix types and values, or multiple types, in a list.  For\ninstance `{ blah: [Number, null] }` would allow a value to be set to\neither a Number or null.  When types are ordered, this implies a\npreference, and the first type that can be used to properly interpret\nthe value will be used.\n\nTo define a new type, add it to `nopt.typeDefs`.  Each item in that\nhash is an object with a `type` member and a `validate` method.  The\n`type` member is an object that matches what goes in the type list.  The\n`validate` method is a function that gets called with `validate(data,\nkey, val)`.  Validate methods should assign `data[key]` to the valid\nvalue of `val` if it can be handled properly, or return boolean\n`false` if it cannot.\n\nYou can also call `nopt.clean(data, types, typeDefs)` to clean up a\nconfig object and remove its invalid properties.\n\n## Error Handling\n\nBy default nopt logs debug messages if `DEBUG_NOPT` or `NOPT_DEBUG` are set in the environment.\n\nYou can assign the following methods to `nopt` for a more granular notification of invalid, unknown, and expanding options:\n\n`nopt.invalidHandler(key, value, type, data)` - Called when a value is invalid for its option.\n`nopt.unknownHandler(key, next)` - Called when an option is found that has no configuration.  In certain situations the next option on the command line will be parsed on its own instead of as part of the unknown option. In this case `next` will contain that option.\n`nopt.abbrevHandler(short, long)` - Called when an option is automatically translated via abbreviations.\n\nYou can also set any of these to `false` to disable the debugging messages that they generate.\n\n## Abbreviations\n\nYes, they are supported.  If you define options like this:\n\n```javascript\n{ \"foolhardyelephants\" : Boolean\n, \"pileofmonkeys\" : Boolean }\n```\n\nThen this will work:\n\n```bash\nnode program.js --foolhar --pil\nnode program.js --no-f --pileofmon\n# etc.\n```\n\n## Shorthands\n\nShorthands are a hash of shorter option names to a snippet of args that\nthey expand to.\n\nIf multiple one-character shorthands are all combined, and the\ncombination does not unambiguously match any other option or shorthand,\nthen they will be broken up into their constituent parts.  For example:\n\n```json\n{ \"s\" : [\"--loglevel\", \"silent\"]\n, \"g\" : \"--global\"\n, \"f\" : \"--force\"\n, \"p\" : \"--parseable\"\n, \"l\" : \"--long\"\n}\n```\n\n```bash\nnpm ls -sgflp\n# just like doing this:\nnpm ls --loglevel silent --global --force --long --parseable\n```\n\n## The Rest of the args\n\nThe config object returned by nopt is given a special member called\n`argv`, which is an object with the following fields:\n\n* `remain`: The remaining args after all the parsing has occurred.\n* `original`: The args as they originally appeared.\n* `cooked`: The args after flags and shorthands are expanded.\n\n## Slicing\n\nNode programs are called with more or less the exact argv as it appears\nin C land, after the v8 and node-specific options have been plucked off.\nAs such, `argv[0]` is always `node` and `argv[1]` is always the\nJavaScript program being run.\n\nThat's usually not very useful to you.  So they're sliced off by\ndefault.  If you want them, then you can pass in `0` as the last\nargument, or any other number that you'd like to slice off the start of\nthe list.\n", "readmeFilename": "README.md", "users": {"pid": true, "amio": true, "detj": true, "jclo": true, "juno": true, "vwal": true, "agnat": true, "beatak": true, "bojand": true, "gdbtek": true, "kastor": true, "shriek": true, "womjoy": true, "itonyyo": true, "kahboom": true, "yokubee": true, "artjacob": true, "bcowgi11": true, "datoulei": true, "elidoran": true, "kehanshi": true, "leodutra": true, "manishrc": true, "szymex73": true, "yashprit": true, "abuelwafa": true, "evanlucas": true, "fgribreau": true, "mastayoda": true, "namgil.ko": true, "starfox64": true, "zombinary": true, "chrisenytc": true, "garrickajo": true, "nornalbion": true, "jonatasnona": true, "lupomontero": true, "ragingsmurf": true, "tunnckocore": true, "jamescostian": true, "lucasbrigida": true, "nickeltobias": true, "tobiasnickel": true, "jian263994241": true, "laggingreflex": true, "mattmcfarland": true, "chrisdickinson": true}}