{"_id": "sshpk", "_rev": "90-f15b5850e4ef554cc2d1a329ab73114b", "name": "sshpk", "dist-tags": {"latest": "1.18.0"}, "versions": {"1.0.0": {"name": "sshpk", "version": "1.0.0", "author": {"name": "Joyent, Inc"}, "license": "MIT", "_id": "sshpk@1.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/node-sshpk#readme", "bugs": {"url": "https://github.com/arekinath/node-sshpk/issues"}, "dist": {"shasum": "a8328ea1f1ab7f24925d05d08b41d307d6e1cc82", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.0.0.tgz", "integrity": "sha512-+a9TI8MCVSOVVvYGsJCe1zM77dR7wKAQvbOkVSw5R4kNX4XDgBdYVA0trI7NKPWVirZTwWA82XFZgimgjxfXkQ==", "signatures": [{"sig": "MEQCIHo3hC6i4r1Si3mbu/pcgo/DxfNU2wEwehfM009RnW2tAiAVom+oX9rVjwnOWjNrBweDCSU99G1ShC6RvN41DSl0Zg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "a8328ea1f1ab7f24925d05d08b41d307d6e1cc82", "gitHead": "aa01f44043fd2217f8682c22f61ecc63df4ac853", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arekinath/node-sshpk.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "A library for finding and using SSH public keys", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"asn1": "^0.2.2", "assert-plus": "^0.1.5"}, "devDependencies": {"tap": "0.4.2", "benchmark": "^1.0.0"}}, "1.0.1": {"name": "sshpk", "version": "1.0.1", "author": {"name": "Joyent, Inc"}, "license": "MIT", "_id": "sshpk@1.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/node-sshpk#readme", "bugs": {"url": "https://github.com/arekinath/node-sshpk/issues"}, "dist": {"shasum": "f082eaf45cf591f28025899076b711ae15492efe", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.0.1.tgz", "integrity": "sha512-VME/PREvDHZJubjNlQEJpINaX0D4M3LmgXhVu3RbHK+XljcLYpjmmceommhMIgfeh5QsOkUJzLn0UA82FvfD/w==", "signatures": [{"sig": "MEQCIDYgbXAdkmtADvGCcsVeSWpfyX/xVmwKCOSGzzWbubckAiAyGudJBakojTjqIpc9mxXSNWtPrZKtC8zGo37WS5a46A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "f082eaf45cf591f28025899076b711ae15492efe", "gitHead": "bdd775ba6df8022f0e3fe46184641fbdfd148dc8", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arekinath/node-sshpk.git", "type": "git"}, "_npmVersion": "2.12.1", "description": "A library for finding and using SSH public keys", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {"asn1": "^0.2.2", "assert-plus": "^0.1.5"}, "devDependencies": {"tap": "0.4.2", "benchmark": "^1.0.0"}}, "1.0.2": {"name": "sshpk", "version": "1.0.2", "author": {"name": "Joyent, Inc"}, "license": "MIT", "_id": "sshpk@1.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/node-sshpk#readme", "bugs": {"url": "https://github.com/arekinath/node-sshpk/issues"}, "dist": {"shasum": "6c4ede38cbc33de7b97701b4439a1f92ff8dca9d", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.0.2.tgz", "integrity": "sha512-FdtqHysFvZOqJtb5Skkfd9aHvNFTABefGaTaLmcryGE0Lb/vqaqLq8JIS2ucNKYBbdtcAcWkG50VNaCPoX/YcA==", "signatures": [{"sig": "MEYCIQCuAp3mUvurqFRvS/1nE3nXwECSLmwCjqvcfWEJRjv/XgIhAI1oVqHcNxeU/oQXDgjvD5QMGX/g/+r0JOB65eNH/+Nu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "6c4ede38cbc33de7b97701b4439a1f92ff8dca9d", "engines": {"node": ">=0.8.0"}, "gitHead": "275d7ae580d17c095f95a04ddaca204ffb77ed65", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arekinath/node-sshpk.git", "type": "git"}, "_npmVersion": "2.14.4", "description": "A library for finding and using SSH public keys", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {"asn1": "^0.2.2", "assert-plus": "^0.1.5"}, "devDependencies": {"tape": "^3.5.0", "benchmark": "^1.0.0"}}, "1.0.3": {"name": "sshpk", "version": "1.0.3", "author": {"name": "Joyent, Inc"}, "license": "MIT", "_id": "sshpk@1.0.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/node-sshpk#readme", "bugs": {"url": "https://github.com/arekinath/node-sshpk/issues"}, "dist": {"shasum": "6046f24117ed21399ba84aab69d4e2e308fc899f", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.0.3.tgz", "integrity": "sha512-eCgZLbJdY3f5BAdzAlFGLeCJBb4tw8YYt9wQcODSy88DhFyinG7uqpNO1+8gVYBvZvbp+4wv8QwvznnMtM5LHg==", "signatures": [{"sig": "MEUCIAKSuvaARdBpQMhOxWpcPvvKcuO3nyEwYXh+XGKVTyvBAiEAjWPxNjtw5SwyHxfOc7rWUz100C1IHXCdhINSEie95L4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "6046f24117ed21399ba84aab69d4e2e308fc899f", "engines": {"node": ">=0.8.0"}, "gitHead": "1f4cf3903d6d1846452eb20371ad929237a77b12", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arekinath/node-sshpk.git", "type": "git"}, "_npmVersion": "2.14.4", "description": "A library for finding and using SSH public keys", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {"asn1": "0.2.2", "assert-plus": "0.1.5"}, "devDependencies": {"tape": ">=3.5.0 <4.0.0", "benchmark": ">=1.0.0 <2.0.0"}}, "1.0.4": {"name": "sshpk", "version": "1.0.4", "author": {"name": "Joyent, Inc"}, "license": "MIT", "_id": "sshpk@1.0.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/node-sshpk#readme", "bugs": {"url": "https://github.com/arekinath/node-sshpk/issues"}, "dist": {"shasum": "584754ee1b57b9026caf2df0831b009011858f6b", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.0.4.tgz", "integrity": "sha512-bgdAfEw88FcX3cpCE8VaJoAtGBKxd135MYNjx94lckYuhVNuj/LOobix/Eh7XY85F/TCECnoae8t5GNs4d2glg==", "signatures": [{"sig": "MEYCIQDMDeLirkT0EiwfxeQ7fDvTbbxBbVE3SZugsGcSbo0BCAIhAO/gWRXxSZvqOf7x44RcqNVi7ZhP7yzNqo0umvVnrZYa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "584754ee1b57b9026caf2df0831b009011858f6b", "engines": {"node": ">=0.8.0"}, "gitHead": "6631e8a0b460432d5e30b543408602ecad1119f3", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arekinath/node-sshpk.git", "type": "git"}, "_npmVersion": "2.14.4", "description": "A library for finding and using SSH public keys", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {"asn1": "0.2.2", "assert-plus": "0.1.5"}, "devDependencies": {"tape": ">=3.5.0 <4.0.0", "benchmark": ">=1.0.0 <2.0.0"}}, "1.1.0": {"name": "sshpk", "version": "1.1.0", "author": {"name": "Joyent, Inc"}, "license": "MIT", "_id": "sshpk@1.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/node-sshpk#readme", "bugs": {"url": "https://github.com/arekinath/node-sshpk/issues"}, "dist": {"shasum": "f1046ee820294ca95d737932e0e2369663a59513", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.1.0.tgz", "integrity": "sha512-s0hab9Q9rIZz/UOAABr+FH1w2OW/FaAHMxXO1n2TRWbQmwecqeDlPqZy6Qm2CZNi0ld+nay0MZPzRPq9plgYNg==", "signatures": [{"sig": "MEYCIQC0ydf+xMO2m8R85BxEbIedREX5u06aBZ4EWkRZWj67BwIhAJE+0HL0YB7NjCXSUq8572iLejnBO84LMp+Vi9+B23vx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "f1046ee820294ca95d737932e0e2369663a59513", "engines": {"node": ">=0.8.0"}, "gitHead": "db5293cc5a0d9c4b2ff7111d8223064d82b88ec3", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arekinath/node-sshpk.git", "type": "git"}, "_npmVersion": "2.14.4", "description": "A library for finding and using SSH public keys", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {"asn1": "0.2.2", "assert-plus": "0.1.5"}, "devDependencies": {"tape": ">=3.5.0 <4.0.0", "benchmark": ">=1.0.0 <2.0.0"}}, "1.2.0": {"name": "sshpk", "version": "1.2.0", "author": {"name": "Joyent, Inc"}, "license": "MIT", "_id": "sshpk@1.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/node-sshpk#readme", "bugs": {"url": "https://github.com/arekinath/node-sshpk/issues"}, "dist": {"shasum": "e400ece7f6cbfe2da4db7335684198b1ced4057d", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.2.0.tgz", "integrity": "sha512-1wHmHoPyKyuLZ2S1e+y/GRPal/EFHhgl08JgA/9Jn8310woAxY6sGKXvbCG+yiSSGEm00m6dLe6WxKS3H1QOZg==", "signatures": [{"sig": "MEYCIQDdIm2k04cJcUiuD0KTs9oL8EUAF+X6UdjnnBUCDen23wIhAMhf+bbW/nhp/V/4+9pBuxTftizxhc81iCHj9NCS1OTt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "e400ece7f6cbfe2da4db7335684198b1ced4057d", "engines": {"node": ">=0.8.0"}, "gitHead": "1797dd1bcf59c2c67db6a670430ea0995a61dabf", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arekinath/node-sshpk.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "A library for finding and using SSH public keys", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"asn1": "0.2.2", "assert-plus": "0.1.5"}, "devDependencies": {"tape": ">=3.5.0 <4.0.0", "benchmark": ">=1.0.0 <2.0.0"}}, "1.2.1": {"name": "sshpk", "version": "1.2.1", "author": {"name": "Joyent, Inc"}, "license": "MIT", "_id": "sshpk@1.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/node-sshpk#readme", "bugs": {"url": "https://github.com/arekinath/node-sshpk/issues"}, "dist": {"shasum": "d7f8272dc165afbfa3ba682086bd3daf26be3e83", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.2.1.tgz", "integrity": "sha512-7GK+Kz/4sEKokNtyPqqHVkupLNghCION+O+9I452lp/+VZ9/Ccc4Glg6AbACynmaTyuMWF0cYJfXbfWm1v2mSg==", "signatures": [{"sig": "MEUCIHea3NWm2JHFEVS4UH8ey4yvheJtuRxiArHUmBV1w6kMAiEAwX1qn96ZlZK1PlLzkTllTq9e8T1CWCWRYqrN2HCUPKk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "d7f8272dc165afbfa3ba682086bd3daf26be3e83", "engines": {"node": ">=0.8.0"}, "gitHead": "531274a801f4ea9c142c699b55e4c118047568fb", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arekinath/node-sshpk.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "A library for finding and using SSH public keys", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"asn1": "0.2.2", "assert-plus": "0.1.5"}, "devDependencies": {"tape": ">=3.5.0 <4.0.0", "benchmark": ">=1.0.0 <2.0.0"}}, "1.3.0": {"name": "sshpk", "version": "1.3.0", "author": {"name": "Joyent, Inc"}, "license": "MIT", "_id": "sshpk@1.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/node-sshpk#readme", "bugs": {"url": "https://github.com/arekinath/node-sshpk/issues"}, "dist": {"shasum": "fd3229a6e0971b2003e61927fa74b2814c696cfb", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.3.0.tgz", "integrity": "sha512-APpqRP7VrlRT3ptS+9bAY/l4KniVUyNHBUkGeGt0VsUNlqT0nszhQkqST0yoNgzt6kiYVzztoOrnRzWQPiH44Q==", "signatures": [{"sig": "MEYCIQDxIr9MA4/q2wHnlYQdOurAW4M/jwQZbxzc3JmSm1X2xQIhAP1JVrkrnEmFlaVf7QL0EpmKaFMjXiLVi6irGlko1p86", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "fd3229a6e0971b2003e61927fa74b2814c696cfb", "engines": {"node": ">=0.8.0"}, "gitHead": "7e8e5c759195c6dfd61a1a501ad0593a49f4aa61", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arekinath/node-sshpk.git", "type": "git"}, "_npmVersion": "2.14.4", "description": "A library for finding and using SSH public keys", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {"asn1": ">=0.2.3 <0.3.0", "assert-plus": ">=0.1.5 <0.2.0", "big-integer": ">=1.6.0 <2.0.0"}, "devDependencies": {"tape": ">=3.5.0 <4.0.0", "benchmark": ">=1.0.0 <2.0.0"}, "optionalDependencies": {"big-integer": ">=1.6.0 <2.0.0"}}, "1.4.0": {"name": "sshpk", "version": "1.4.0", "author": {"name": "Joyent, Inc"}, "license": "MIT", "_id": "sshpk@1.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/node-sshpk#readme", "bugs": {"url": "https://github.com/arekinath/node-sshpk/issues"}, "bin": {"sshpk-conv": "bin/sshpk-conv"}, "dist": {"shasum": "4358666a18093d91cdbddb986e00545e119ee79b", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.4.0.tgz", "integrity": "sha512-K1+NhKQ0hpUFf4XnJzNtWBN+gX8fmZz9aWc4SbWU0IxF4FtvTJ5QcOm6iRSt98d/U0+TgEKVTzkD/fgiJL7uQw==", "signatures": [{"sig": "MEUCIQDb4pVZ3tR4wg6/K1+5W/eLKV1bDjshjCBsbNUdXHbcyQIgKgmqP7JSUeAuSMYLdxaX9Hmd/O6CNTAmfYNGUIQRQuA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "4358666a18093d91cdbddb986e00545e119ee79b", "engines": {"node": ">=0.8.0"}, "gitHead": "1e2a8adb67b4e63072dd40bf6a26779670423214", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arekinath/node-sshpk.git", "type": "git"}, "_npmVersion": "2.14.4", "description": "A library for finding and using SSH public keys", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "0.12.7", "dependencies": {"asn1": ">=0.2.3 <0.3.0", "dashdash": ">=1.10.1 <2.0.0", "assert-plus": ">=0.1.5 <0.2.0", "big-integer": ">=1.6.0 <2.0.0"}, "devDependencies": {"tape": ">=3.5.0 <4.0.0", "benchmark": ">=1.0.0 <2.0.0"}, "optionalDependencies": {"big-integer": ">=1.6.0 <2.0.0"}}, "1.4.1": {"name": "sshpk", "version": "1.4.1", "author": {"name": "Joyent, Inc"}, "license": "MIT", "_id": "sshpk@1.4.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/node-sshpk#readme", "bugs": {"url": "https://github.com/arekinath/node-sshpk/issues"}, "bin": {"sshpk-conv": "bin/sshpk-conv"}, "dist": {"shasum": "f271f1533deba161bae23d46d40ddb4c7f28b395", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.4.1.tgz", "integrity": "sha512-etyGas8kDAKYDCrmAOYEqs+6Hlsp9EzcpUOCi/jDgW53c+DA6TSqv1ZzeuQDptCL1FBbsf8vNWAmCACl0yMKvA==", "signatures": [{"sig": "MEUCIDzLpOC4y74VOYU7CbrALmGDuFSxXsNMuKabWn+nQWU4AiEAttipw7NQqi5XpKdmD5L59Fs3XVd7Cde2N5KKygTKwPA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "f271f1533deba161bae23d46d40ddb4c7f28b395", "engines": {"node": ">=0.8.0"}, "gitHead": "fabb2b74cab6b9820a78e58e9401e02c9fec3440", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arekinath/node-sshpk.git", "type": "git"}, "_npmVersion": "2.14.4", "description": "A library for finding and using SSH public keys", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "0.12.7", "dependencies": {"asn1": ">=0.2.3 <0.3.0", "dashdash": ">=1.10.1 <2.0.0", "assert-plus": ">=0.1.5 <0.2.0", "big-integer": ">=1.6.0 <2.0.0"}, "devDependencies": {"tape": ">=3.5.0 <4.0.0", "benchmark": ">=1.0.0 <2.0.0"}, "optionalDependencies": {"big-integer": ">=1.6.0 <2.0.0"}}, "1.4.2": {"name": "sshpk", "version": "1.4.2", "author": {"name": "Joyent, Inc"}, "license": "MIT", "_id": "sshpk@1.4.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/node-sshpk#readme", "bugs": {"url": "https://github.com/arekinath/node-sshpk/issues"}, "bin": {"sshpk-conv": "bin/sshpk-conv"}, "dist": {"shasum": "d06be5aa796f8a3bccdf7dcd6b9445177487012c", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.4.2.tgz", "integrity": "sha512-d19FZ7gfirLfoDn3aOMPusvxqRX/H09pwG6e9PvU7RRT4qxrEkhzEYy+2vy332n1oP8RRr025tSTY4z2EU0dfA==", "signatures": [{"sig": "MEUCIDA2q1kBmKNG/5vdmqc9uShvXAtg1ATG5wZl2Pz/TKKwAiEA7QOojarlwaUX4kcJwX2AGYQdVFpegj67f0VOd76FVk8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "d06be5aa796f8a3bccdf7dcd6b9445177487012c", "engines": {"node": ">=0.8.0"}, "gitHead": "610baceec44d0dba669586f41e36a9273c4159db", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arekinath/node-sshpk.git", "type": "git"}, "_npmVersion": "2.14.4", "description": "A library for finding and using SSH public keys", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "0.12.7", "dependencies": {"asn1": ">=0.2.3 <0.3.0", "dashdash": ">=1.10.1 <2.0.0", "assert-plus": ">=0.1.5 <0.2.0", "big-integer": ">=1.6.0 <2.0.0"}, "devDependencies": {"tape": ">=3.5.0 <4.0.0", "benchmark": ">=1.0.0 <2.0.0"}, "optionalDependencies": {"big-integer": ">=1.6.0 <2.0.0"}}, "1.4.3": {"name": "sshpk", "version": "1.4.3", "author": {"name": "Joyent, Inc"}, "license": "MIT", "_id": "sshpk@1.4.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/node-sshpk#readme", "bugs": {"url": "https://github.com/arekinath/node-sshpk/issues"}, "bin": {"sshpk-conv": "bin/sshpk-conv"}, "dist": {"shasum": "580177d5989653ad61b890af4ffb0e63dca6c5a1", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.4.3.tgz", "integrity": "sha512-enDhud100EEesQXT7BrJJKzlalpW4vcyEPGsGbPXqpleMfINCYY+SexIGKN/4YVSyyzBKW/VfMORP56OWmUYmw==", "signatures": [{"sig": "MEQCIHB27IQy7Yl/I/lgJfXtVX7DEhWbjoQH3Up4IgHhATCQAiBbMgwWf6qelKoNpsAauIztPCXaxvjt3uyyrREq4JH1JA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "580177d5989653ad61b890af4ffb0e63dca6c5a1", "engines": {"node": ">=0.8.0"}, "gitHead": "0a56fe7f508cd8dcec8bc8213ccc9fd42115f7bb", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arekinath/node-sshpk.git", "type": "git"}, "_npmVersion": "2.14.6", "description": "A library for finding and using SSH public keys", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "0.12.7", "dependencies": {"asn1": ">=0.2.3 <0.3.0", "dashdash": ">=1.10.1 <2.0.0", "assert-plus": ">=0.1.5 <0.2.0", "big-integer": ">=1.6.0 <2.0.0"}, "devDependencies": {"tape": ">=3.5.0 <4.0.0", "benchmark": ">=1.0.0 <2.0.0"}, "optionalDependencies": {"big-integer": ">=1.6.0 <2.0.0"}}, "1.4.4": {"name": "sshpk", "version": "1.4.4", "author": {"name": "Joyent, Inc"}, "license": "MIT", "_id": "sshpk@1.4.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/node-sshpk#readme", "bugs": {"url": "https://github.com/arekinath/node-sshpk/issues"}, "bin": {"sshpk-conv": "bin/sshpk-conv"}, "dist": {"shasum": "8da4da1bd64c48ce13fd9947144c917582462024", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.4.4.tgz", "integrity": "sha512-zNDiA87g7FZXS2e19l4bEiO2wbpJ+GiCEGjFnEvPjoJW2dy9uZ+fw2SI2zaJMFw98Zv9vbanp0igDL3c+vqo4w==", "signatures": [{"sig": "MEUCIQDiwj7U7sEDLkkktZl3IGNgY5zOoRtZqV36mfCuwEuA4QIgbnqSWAJLJKJOXHfld7eTTmftSc56KSiMM2qaaLAy8hw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "8da4da1bd64c48ce13fd9947144c917582462024", "engines": {"node": ">=0.8.0"}, "gitHead": "2ed1a1c0f086646caf2798f7204cdc749efd25e0", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arekinath/node-sshpk.git", "type": "git"}, "_npmVersion": "2.14.4", "description": "A library for finding and using SSH public keys", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "0.12.7", "dependencies": {"asn1": ">=0.2.3 <0.3.0", "dashdash": ">=1.10.1 <2.0.0", "assert-plus": ">=0.1.5 <0.2.0", "big-integer": ">=1.6.0 <2.0.0"}, "devDependencies": {"tape": ">=3.5.0 <4.0.0", "benchmark": ">=1.0.0 <2.0.0"}, "optionalDependencies": {"big-integer": ">=1.6.0 <2.0.0"}}, "1.4.5": {"name": "sshpk", "version": "1.4.5", "author": {"name": "Joyent, Inc"}, "license": "MIT", "_id": "sshpk@1.4.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/node-sshpk#readme", "bugs": {"url": "https://github.com/arekinath/node-sshpk/issues"}, "bin": {"sshpk-conv": "bin/sshpk-conv"}, "dist": {"shasum": "b08d682ea1819fd50bad39c0bc1f08cd4d787883", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.4.5.tgz", "integrity": "sha512-1UlQxgIWGPhAdhc80Ol3X2C/NXrJHzJmo76kKrd3kwBnfMk5NZFoIvC6a2Xwpj92Bmf4ed7zC1jaPBiu2q2ZAw==", "signatures": [{"sig": "MEQCIA0yEtOljpJOpatMoJtMc6Q2psXJhOC1mriTixhXNt99AiAlw9aCNcI56x2oL3KreiW0sLUEz7u/KBL/3ZMqvi5FAg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "b08d682ea1819fd50bad39c0bc1f08cd4d787883", "engines": {"node": ">=0.8.0"}, "gitHead": "74f71c47efa29371407ad14b53663975036a7237", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arekinath/node-sshpk.git", "type": "git"}, "_npmVersion": "2.14.4", "description": "A library for finding and using SSH public keys", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "0.12.7", "dependencies": {"asn1": ">=0.2.3 <0.3.0", "dashdash": ">=1.10.1 <2.0.0", "assert-plus": ">=0.1.5 <0.2.0", "big-integer": ">=1.6.0 <2.0.0"}, "devDependencies": {"tape": ">=3.5.0 <4.0.0", "benchmark": ">=1.0.0 <2.0.0"}, "optionalDependencies": {"big-integer": ">=1.6.0 <2.0.0"}}, "1.4.6": {"name": "sshpk", "version": "1.4.6", "author": {"name": "Joyent, Inc"}, "license": "MIT", "_id": "sshpk@1.4.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/node-sshpk#readme", "bugs": {"url": "https://github.com/arekinath/node-sshpk/issues"}, "bin": {"sshpk-conv": "bin/sshpk-conv"}, "dist": {"shasum": "aff7273e4797e60a83b031c94388bd00cf500c59", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.4.6.tgz", "integrity": "sha512-Jyrzoi1eNbkLJuwF7GNfLrIUONccL1Xhh6Mt2zw9nQpOUacKjR5zAXFcY+Wg6+M3Rx7+zwWmHpqLcwLNLQv4yQ==", "signatures": [{"sig": "MEUCIF5QCMytoA+fF8zFsUXn61AGaVVsljTH6lpIAm+HL8s2AiEApa642e5BLmgZvSUi1+Xvy1u1EdbzygEizhubM2N1gl4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "aff7273e4797e60a83b031c94388bd00cf500c59", "engines": {"node": ">=0.8.0"}, "gitHead": "f94621769b80857b02ab6e28534b4c538da9a988", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arekinath/node-sshpk.git", "type": "git"}, "_npmVersion": "2.14.6", "description": "A library for finding and using SSH public keys", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "0.12.7", "dependencies": {"asn1": ">=0.2.3 <0.3.0", "dashdash": ">=1.10.1 <2.0.0", "assert-plus": ">=0.1.5 <0.2.0", "big-integer": ">=1.6.0 <2.0.0"}, "devDependencies": {"tape": ">=3.5.0 <4.0.0", "benchmark": ">=1.0.0 <2.0.0"}, "optionalDependencies": {"big-integer": ">=1.6.0 <2.0.0"}}, "1.4.7": {"name": "sshpk", "version": "1.4.7", "author": {"name": "Joyent, Inc"}, "license": "MIT", "_id": "sshpk@1.4.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/node-sshpk#readme", "bugs": {"url": "https://github.com/arekinath/node-sshpk/issues"}, "bin": {"sshpk-conv": "bin/sshpk-conv"}, "dist": {"shasum": "62e3dbf98b065ca7950acd21cdb780b9d2080066", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.4.7.tgz", "integrity": "sha512-yFzBhX48lbX4WQMsajkHWTW3bIFbvVmatwHjVMg+0L3t4La/4VwhbqFk7JXcLIsIGV54xgR3GrQPji+jY7g0Aw==", "signatures": [{"sig": "MEUCIFvJ9muvCs5KjwqaSuwZZ9Oz6nO9FrDcUN4wDwgqjI7aAiEA90we3AcX8Wp1J7FbmBdo9k0ugS3NmEfVxBh9vIf15Yk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "62e3dbf98b065ca7950acd21cdb780b9d2080066", "engines": {"node": ">=0.8.0"}, "gitHead": "ac12e343830287500ee14716dd83171124931a27", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arekinath/node-sshpk.git", "type": "git"}, "_npmVersion": "2.14.4", "description": "A library for finding and using SSH public keys", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "0.12.7", "dependencies": {"asn1": ">=0.2.3 <0.3.0", "dashdash": ">=1.10.1 <2.0.0", "assert-plus": ">=0.1.5 <0.2.0", "big-integer": ">=1.6.0 <2.0.0"}, "devDependencies": {"tape": ">=3.5.0 <4.0.0", "benchmark": ">=1.0.0 <2.0.0"}, "optionalDependencies": {"big-integer": ">=1.6.0 <2.0.0"}}, "1.5.0": {"name": "sshpk", "version": "1.5.0", "author": {"name": "Joyent, Inc"}, "license": "MIT", "_id": "sshpk@1.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/node-sshpk#readme", "bugs": {"url": "https://github.com/arekinath/node-sshpk/issues"}, "bin": {"sshpk-conv": "bin/sshpk-conv"}, "dist": {"shasum": "00c40d3b80d4fe7e9ac562a343ce111fafca4fe2", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.5.0.tgz", "integrity": "sha512-sXBs0Zv/CVc0yvolRfeh9M30qsbqDr5Vo5RmpkaVABW1R3w4kdosQCRiCU4LQqaRZFLH82GJajBANfdOOuyQ/A==", "signatures": [{"sig": "MEUCIQC5hNxrSs+kXIwxRFU3svZ1p/RJXS1/yADRP3UYWz8c8AIgHAnnFd/7Spf6uNjLx31tsfsL8/LAL5ZsEHQAdyi9Lz0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "00c40d3b80d4fe7e9ac562a343ce111fafca4fe2", "engines": {"node": ">=0.10.0"}, "gitHead": "2258438cad3785851e57270b373c01b42589cc5a", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arekinath/node-sshpk.git", "type": "git"}, "_npmVersion": "2.14.4", "description": "A library for finding and using SSH public keys", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "0.8.28", "dependencies": {"asn1": ">=0.2.3 <0.3.0", "jsbn": ">=0.0.0 <1.0.0", "dashdash": ">=1.10.1 <2.0.0", "jodid25519": ">=1.0.0 <2.0.0", "assert-plus": ">=0.1.5 <0.2.0"}, "devDependencies": {"tape": ">=3.5.0 <4.0.0", "benchmark": ">=1.0.0 <2.0.0"}, "optionalDependencies": {"jsbn": ">=0.0.0 <1.0.0", "jodid25519": ">=1.0.0 <2.0.0"}}, "1.5.1": {"name": "sshpk", "version": "1.5.1", "author": {"name": "Joyent, Inc"}, "license": "MIT", "_id": "sshpk@1.5.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/node-sshpk#readme", "bugs": {"url": "https://github.com/arekinath/node-sshpk/issues"}, "bin": {"sshpk-conv": "bin/sshpk-conv"}, "dist": {"shasum": "85fe5c5e69b7318c3fa329d82abef0ba4228c4f9", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.5.1.tgz", "integrity": "sha512-CLZege1Vdn0zpTDU+IIAkg0gxeEBiW2oW6YE1h8Kl+dZOf3jhlU0iz7scpTMTsDyyj3pvFqWldLEjEoUlEsnYw==", "signatures": [{"sig": "MEUCIQCwZJWj1OFDmM5+YhYJby5Y0Pya4/E+FHctdjhmHROO+QIgFoJnAxBucUW5DzeeHQBbwpAcEeG6bCrwIZK51jb2Z24=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "85fe5c5e69b7318c3fa329d82abef0ba4228c4f9", "engines": {"node": ">=0.10.0"}, "gitHead": "da9a897fa020576665ca63295df4d9d34cb05eea", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arekinath/node-sshpk.git", "type": "git"}, "_npmVersion": "2.14.4", "description": "A library for finding and using SSH public keys", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "0.12.7", "dependencies": {"asn1": ">=0.2.3 <0.3.0", "jsbn": ">=0.0.0 <1.0.0", "dashdash": ">=1.10.1 <2.0.0", "jodid25519": ">=1.0.0 <2.0.0", "assert-plus": ">=0.1.5 <0.2.0"}, "devDependencies": {"tape": ">=3.5.0 <4.0.0", "benchmark": ">=1.0.0 <2.0.0"}, "optionalDependencies": {"jsbn": ">=0.0.0 <1.0.0", "jodid25519": ">=1.0.0 <2.0.0"}}, "1.6.0": {"name": "sshpk", "version": "1.6.0", "author": {"name": "Joyent, Inc"}, "license": "MIT", "_id": "sshpk@1.6.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/node-sshpk#readme", "bugs": {"url": "https://github.com/arekinath/node-sshpk/issues"}, "bin": {"sshpk-conv": "bin/sshpk-conv"}, "dist": {"shasum": "f9f9c76fd8e622d583b1ae83fe49aa7bec6e1d4a", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.6.0.tgz", "integrity": "sha512-bc6WjJfjvGWaAOsw8/VGr4K5RqtpsI0OnJ1XXUbRIQo8JapKuc+Wmcz5AaHiWG9A6ekk1fy7rFCzTHRxAUzNxw==", "signatures": [{"sig": "MEUCIBYDyrBIwHUVk1TumRjKSBQn9+fu8p8RxhgGyztK6PrUAiEArvxU5kxANN83CItI+Y9564Ffwe6s08Mz+gqD+pJHeQ4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "f9f9c76fd8e622d583b1ae83fe49aa7bec6e1d4a", "engines": {"node": ">=0.10.0"}, "gitHead": "f3013387324f5da539be282ac1b637056d956c18", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arekinath/node-sshpk.git", "type": "git"}, "_npmVersion": "2.14.4", "description": "A library for finding and using SSH public keys", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "4.1.1", "dependencies": {"asn1": ">=0.2.3 <0.3.0", "jsbn": ">=0.1.0 <0.2.0", "dashdash": ">=1.10.1 <2.0.0", "ecc-jsbn": ">=0.0.1 <1.0.0", "tweetnacl": ">=0.13.0 <1.0.0", "jodid25519": ">=1.0.0 <2.0.0", "assert-plus": ">=0.1.5 <0.2.0"}, "devDependencies": {"tape": ">=3.5.0 <4.0.0", "sinon": ">=1.17.2 <2.0.0", "benchmark": ">=1.0.0 <2.0.0"}, "optionalDependencies": {"jsbn": ">=0.1.0 <0.2.0", "ecc-jsbn": ">=0.0.1 <1.0.0", "tweetnacl": ">=0.13.0 <1.0.0", "jodid25519": ">=1.0.0 <2.0.0"}}, "1.6.1": {"name": "sshpk", "version": "1.6.1", "author": {"name": "Joyent, Inc"}, "license": "MIT", "_id": "sshpk@1.6.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/node-sshpk#readme", "bugs": {"url": "https://github.com/arekinath/node-sshpk/issues"}, "bin": {"sshpk-conv": "bin/sshpk-conv"}, "dist": {"shasum": "3f1e6825feb00dd178f897409d6661756d3fefac", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.6.1.tgz", "integrity": "sha512-0iZWUpmXwwi5GjUQKjE4QKoa/SiuF5jnNw+UIRngZnXuQwJhdFAwOmIn3t7v756iutdx+vgLvml+JO31YZ7iHQ==", "signatures": [{"sig": "MEYCIQCqsTYx7I414qyngEzpJnKuGnP0YaVpqF1OEGOnT7VJSgIhAIiqWu0w4edBSJw+aRFO+nQrX5Sdvo1aYLO5myCgjs9g", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "3f1e6825feb00dd178f897409d6661756d3fefac", "engines": {"node": ">=0.10.0"}, "gitHead": "b07d85a8d4f0e04b45f106fbc3768e30b63229e5", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arekinath/node-sshpk.git", "type": "git"}, "_npmVersion": "2.14.4", "description": "A library for finding and using SSH public keys", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "0.12.7", "dependencies": {"asn1": ">=0.2.3 <0.3.0", "jsbn": ">=0.1.0 <0.2.0", "dashdash": ">=1.10.1 <2.0.0", "ecc-jsbn": ">=0.0.1 <1.0.0", "tweetnacl": ">=0.13.0 <1.0.0", "jodid25519": ">=1.0.0 <2.0.0", "assert-plus": ">=0.1.5 <0.2.0"}, "devDependencies": {"tape": ">=3.5.0 <4.0.0", "sinon": ">=1.17.2 <2.0.0", "benchmark": ">=1.0.0 <2.0.0"}, "optionalDependencies": {"jsbn": ">=0.1.0 <0.2.0", "ecc-jsbn": ">=0.0.1 <1.0.0", "tweetnacl": ">=0.13.0 <1.0.0", "jodid25519": ">=1.0.0 <2.0.0"}}, "1.6.2": {"name": "sshpk", "version": "1.6.2", "author": {"name": "Joyent, Inc"}, "license": "MIT", "_id": "sshpk@1.6.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/node-sshpk#readme", "bugs": {"url": "https://github.com/arekinath/node-sshpk/issues"}, "bin": {"sshpk-conv": "bin/sshpk-conv"}, "dist": {"shasum": "a8bbe7f5087739b71e1cecb43669e1ade663214c", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.6.2.tgz", "integrity": "sha512-yrtjJgkM0CTjlI0Z9ermaCLRJZUnlSu3HaBwe/IRGvzBBtq4X0/vPFI7xKmlZqso0OM5vk2ptcKEYhEzrwef0Q==", "signatures": [{"sig": "MEQCIHBzmkZC9b7U7Xds4IicX7WKK0EKA6/gALK15vKhf1ojAiAYJWKgaq8vNXK1wavlN6f+PgFTjvpgwsGrp+EPLchQdg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "a8bbe7f5087739b71e1cecb43669e1ade663214c", "engines": {"node": ">=0.8.0"}, "gitHead": "f245b4f3dc0b9a89475b398fb1a7e895e73c6f95", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arekinath/node-sshpk.git", "type": "git"}, "_npmVersion": "2.14.4", "description": "A library for finding and using SSH public keys", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "0.8.28", "dependencies": {"asn1": ">=0.2.3 <0.3.0", "jsbn": ">=0.1.0 <0.2.0", "dashdash": ">=1.10.1 <2.0.0", "ecc-jsbn": ">=0.0.1 <1.0.0", "tweetnacl": ">=0.13.0 <1.0.0", "jodid25519": ">=1.0.0 <2.0.0", "assert-plus": ">=0.1.5 <0.2.0"}, "devDependencies": {"tape": ">=3.5.0 <4.0.0", "sinon": ">=1.17.2 <2.0.0", "benchmark": ">=1.0.0 <2.0.0"}, "optionalDependencies": {"jsbn": ">=0.1.0 <0.2.0", "ecc-jsbn": ">=0.0.1 <1.0.0", "tweetnacl": ">=0.13.0 <1.0.0", "jodid25519": ">=1.0.0 <2.0.0"}}, "1.7.0": {"name": "sshpk", "version": "1.7.0", "author": {"name": "Joyent, Inc"}, "license": "MIT", "_id": "sshpk@1.7.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/node-sshpk#readme", "bugs": {"url": "https://github.com/arekinath/node-sshpk/issues"}, "bin": {"sshpk-conv": "bin/sshpk-conv"}, "dist": {"shasum": "c64ac26de1bdd19cc9ccbe78ba747292c6c70a7c", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.7.0.tgz", "integrity": "sha512-60oGUGLvQ4Q46D82flsCNA/daEkvTnVr3HlyxP0/1nkwCRZcUFwHWNyb0uBEIZMg1CWA1J4BlyjafgARImVN6g==", "signatures": [{"sig": "MEUCIHE9Hh+mE6m7cTXdmv4Do6aDMobaT1NR1G6V/Gn722lrAiEA4Ry5locN6WlOHClSqCz4NpNx59ZYECku5x6e/lgjloo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "c64ac26de1bdd19cc9ccbe78ba747292c6c70a7c", "engines": {"node": ">=0.8.0"}, "gitHead": "d268fc9a5442727dfb975a0c609f7419bc4d7057", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arekinath/node-sshpk.git", "type": "git"}, "_npmVersion": "2.14.4", "description": "A library for finding and using SSH public keys", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "0.12.7", "dependencies": {"asn1": ">=0.2.3 <0.3.0", "jsbn": ">=0.1.0 <0.2.0", "dashdash": ">=1.10.1 <2.0.0", "ecc-jsbn": ">=0.0.1 <1.0.0", "tweetnacl": ">=0.13.0 <1.0.0", "jodid25519": ">=1.0.0 <2.0.0", "assert-plus": ">=0.2.0 <0.3.0"}, "devDependencies": {"tape": ">=3.5.0 <4.0.0", "sinon": ">=1.17.2 <2.0.0", "benchmark": ">=1.0.0 <2.0.0"}, "optionalDependencies": {"jsbn": ">=0.1.0 <0.2.0", "ecc-jsbn": ">=0.0.1 <1.0.0", "tweetnacl": ">=0.13.0 <1.0.0", "jodid25519": ">=1.0.0 <2.0.0"}}, "1.7.1": {"name": "sshpk", "version": "1.7.1", "author": {"name": "Joyent, Inc"}, "license": "MIT", "_id": "sshpk@1.7.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/node-sshpk#readme", "bugs": {"url": "https://github.com/arekinath/node-sshpk/issues"}, "bin": {"sshpk-conv": "bin/sshpk-conv"}, "dist": {"shasum": "565e386c42a77e6062fbd14c0472ff21cd53398c", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.7.1.tgz", "integrity": "sha512-5tF1bCTJM3L2EPeg25Njg91c231xKXrTthIOwFzRJo5DSdnaoC8SVTZvX4NcXsbu16LoszUuOyj+RzBa9E4law==", "signatures": [{"sig": "MEYCIQDY2UcmnjNTFyTzSYeV1TbKtx2RNjTpQieiJWnZgNOQmgIhAO/iPO0/ow0aeYLT8lL7YsjprQcaEPY+ysO+s2gjzQBw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "565e386c42a77e6062fbd14c0472ff21cd53398c", "engines": {"node": ">=0.8.0"}, "gitHead": "71dd768e94d6214a0b8e1196ca78297b582c6fb0", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arekinath/node-sshpk.git", "type": "git"}, "_npmVersion": "2.14.4", "description": "A library for finding and using SSH public keys", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "0.12.7", "dependencies": {"asn1": ">=0.2.3 <0.3.0", "jsbn": ">=0.1.0 <0.2.0", "dashdash": ">=1.10.1 <2.0.0", "ecc-jsbn": ">=0.0.1 <1.0.0", "tweetnacl": ">=0.13.0 <1.0.0", "jodid25519": ">=1.0.0 <2.0.0", "assert-plus": ">=0.2.0 <0.3.0"}, "devDependencies": {"tape": ">=3.5.0 <4.0.0", "sinon": ">=1.17.2 <2.0.0", "benchmark": ">=1.0.0 <2.0.0"}, "optionalDependencies": {"jsbn": ">=0.1.0 <0.2.0", "ecc-jsbn": ">=0.0.1 <1.0.0", "tweetnacl": ">=0.13.0 <1.0.0", "jodid25519": ">=1.0.0 <2.0.0"}}, "1.7.2": {"name": "sshpk", "version": "1.7.2", "author": {"name": "Joyent, Inc"}, "license": "MIT", "_id": "sshpk@1.7.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/node-sshpk#readme", "bugs": {"url": "https://github.com/arekinath/node-sshpk/issues"}, "bin": {"sshpk-conv": "bin/sshpk-conv"}, "dist": {"shasum": "e5eb43d0662bd201037327edb8b8f64656aca842", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.7.2.tgz", "integrity": "sha512-T2CUOimLP1rTzO588HpHZrLPpNHi2p7Dq2OC3QHBVIKLbjD6c+T+9E2VSbDAzxKnGnt9bx+Sllob78ch1FVNPA==", "signatures": [{"sig": "MEQCIE1Qcf4KEjGkpl4vT2JDx4xqapvTAb/eBq0bbeYonLNDAiB23UJsKGzcxabZoZ6YOpYHs3upO3PGlebL5kcn4J5o6w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "e5eb43d0662bd201037327edb8b8f64656aca842", "engines": {"node": ">=0.8.0"}, "gitHead": "f82457a5eca419cc3e6a2650e79b3414c47ea3bd", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arekinath/node-sshpk.git", "type": "git"}, "_npmVersion": "2.14.9", "description": "A library for finding and using SSH public keys", "directories": {"bin": "./bin", "lib": "./lib"}, "_nodeVersion": "0.12.9", "dependencies": {"asn1": ">=0.2.3 <0.3.0", "jsbn": ">=0.1.0 <0.2.0", "dashdash": ">=1.10.1 <2.0.0", "ecc-jsbn": ">=0.0.1 <1.0.0", "tweetnacl": ">=0.13.0 <1.0.0", "jodid25519": ">=1.0.0 <2.0.0", "assert-plus": ">=0.2.0 <0.3.0"}, "devDependencies": {"tape": ">=3.5.0 <4.0.0", "temp": "0.8.2", "sinon": ">=1.17.2 <2.0.0", "benchmark": ">=1.0.0 <2.0.0"}, "optionalDependencies": {"jsbn": ">=0.1.0 <0.2.0", "ecc-jsbn": ">=0.0.1 <1.0.0", "tweetnacl": ">=0.13.0 <1.0.0", "jodid25519": ">=1.0.0 <2.0.0"}}, "1.7.3": {"name": "sshpk", "version": "1.7.3", "author": {"name": "Joyent, Inc"}, "license": "MIT", "_id": "sshpk@1.7.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/node-sshpk#readme", "bugs": {"url": "https://github.com/arekinath/node-sshpk/issues"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "man": ["/Users/<USER>/dev/sshpk/man/man1/sshpk-conv.1", "/Users/<USER>/dev/sshpk/man/man1/sshpk-sign.1", "/Users/<USER>/dev/sshpk/man/man1/sshpk-verify.1"], "dist": {"shasum": "caa8ef95e30765d856698b7025f9f211ab65962f", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.7.3.tgz", "integrity": "sha512-vFFte2Qp/1AG2zIvpn3sc2aJnewD6dnzj5jAsmC6XT0Wj6YPBLaCVVZ8kPBM+RnAIkUixY2LL/hp/BvEJdp0tQ==", "signatures": [{"sig": "MEYCIQDKBmXGIquvEvdcwRhAcZRiMVH4+EnUrnNcoVzDZ7hDZQIhAMbmGqKrq+M6LmgnWDED67frlbhGkRl5vlmUC8eClORl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "caa8ef95e30765d856698b7025f9f211ab65962f", "engines": {"node": ">=0.8.0"}, "gitHead": "3d98bfc22bb1c09f0747244acbb408f3ca9448b5", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arekinath/node-sshpk.git", "type": "git"}, "_npmVersion": "2.14.9", "description": "A library for finding and using SSH public keys", "directories": {"bin": "./bin", "lib": "./lib", "man": "./man/man1"}, "_nodeVersion": "0.12.9", "dependencies": {"asn1": ">=0.2.3 <0.3.0", "jsbn": ">=0.1.0 <0.2.0", "dashdash": ">=1.10.1 <2.0.0", "ecc-jsbn": ">=0.0.1 <1.0.0", "tweetnacl": ">=0.13.0 <1.0.0", "jodid25519": ">=1.0.0 <2.0.0", "assert-plus": ">=0.2.0 <0.3.0"}, "devDependencies": {"tape": ">=3.5.0 <4.0.0", "temp": "0.8.2", "sinon": ">=1.17.2 <2.0.0", "benchmark": ">=1.0.0 <2.0.0"}, "optionalDependencies": {"jsbn": ">=0.1.0 <0.2.0", "ecc-jsbn": ">=0.0.1 <1.0.0", "tweetnacl": ">=0.13.0 <1.0.0", "jodid25519": ">=1.0.0 <2.0.0"}}, "1.7.4": {"name": "sshpk", "version": "1.7.4", "author": {"name": "Joyent, Inc"}, "license": "MIT", "_id": "sshpk@1.7.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/node-sshpk#readme", "bugs": {"url": "https://github.com/arekinath/node-sshpk/issues"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "man": ["/Users/<USER>/dev/sshpk/man/man1/sshpk-conv.1", "/Users/<USER>/dev/sshpk/man/man1/sshpk-sign.1", "/Users/<USER>/dev/sshpk/man/man1/sshpk-verify.1"], "dist": {"shasum": "ad7b47defca61c8415d964243b62b0ce60fbca38", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.7.4.tgz", "integrity": "sha512-p7+I1qlhKmgTIGmXu9DkQzjVig5uURH0xNWg/YU6O4YWsfCYoctnwbyrdgMcvzRmTBERMzOxVJifeaHlJLeLig==", "signatures": [{"sig": "MEQCICCMLv3576NQipDGpE+dy6WwiJVmpuJ+n1BWVvYEf9GaAiAeHQwr3UHpG4AAND8CbHayG2oYb06/EZl83SwbMpYTgQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "ad7b47defca61c8415d964243b62b0ce60fbca38", "engines": {"node": ">=0.8.0"}, "gitHead": "9b86d45d512c97f6ea10b98a2970828dfb290310", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arekinath/node-sshpk.git", "type": "git"}, "_npmVersion": "2.14.4", "description": "A library for finding and using SSH public keys", "directories": {"bin": "./bin", "lib": "./lib", "man": "./man/man1"}, "_nodeVersion": "0.12.9", "dependencies": {"asn1": ">=0.2.3 <0.3.0", "jsbn": ">=0.1.0 <0.2.0", "dashdash": ">=1.10.1 <2.0.0", "ecc-jsbn": ">=0.0.1 <1.0.0", "tweetnacl": ">=0.13.0 <1.0.0", "jodid25519": ">=1.0.0 <2.0.0", "assert-plus": ">=0.2.0 <0.3.0"}, "devDependencies": {"tape": ">=3.5.0 <4.0.0", "temp": "0.8.2", "sinon": ">=1.17.2 <2.0.0", "benchmark": ">=1.0.0 <2.0.0"}, "optionalDependencies": {"jsbn": ">=0.1.0 <0.2.0", "ecc-jsbn": ">=0.0.1 <1.0.0", "tweetnacl": ">=0.13.0 <1.0.0", "jodid25519": ">=1.0.0 <2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/sshpk-1.7.4.tgz_1455236064846_0.4782760036177933", "host": "packages-6-west.internal.npmjs.com"}}, "1.8.0": {"name": "sshpk", "version": "1.8.0", "author": {"name": "Joyent, Inc"}, "license": "MIT", "_id": "sshpk@1.8.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/node-sshpk#readme", "bugs": {"url": "https://github.com/arekinath/node-sshpk/issues"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "man": ["/Users/<USER>/dev/sshpk/man/man1/sshpk-conv.1", "/Users/<USER>/dev/sshpk/man/man1/sshpk-sign.1", "/Users/<USER>/dev/sshpk/man/man1/sshpk-verify.1"], "dist": {"shasum": "b8f8820f8a21b0ded4e6459da42313317ff15bf6", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.8.0.tgz", "integrity": "sha512-0U/udGtPTPIioWrFpf9EASjltk3zAZVGbNpH/oVEgBYROPEKvodfZB24mZxdhRCudarNSLJq7FryyrEiED3Xkg==", "signatures": [{"sig": "MEUCIQDVffb1cf1liKbwqR7G2QjYeDsjPnrdLODRLCQAp9NHFAIgHnIUTWNHC/c3vD8XwSM8p3GI3En0BuZnjFtcTvydJYU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "b8f8820f8a21b0ded4e6459da42313317ff15bf6", "engines": {"node": ">=0.10.0"}, "gitHead": "72e514427063ab67e32542df9da4ab55390072b6", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arekinath/node-sshpk.git", "type": "git"}, "_npmVersion": "2.15.1", "description": "A library for finding and using SSH public keys", "directories": {"bin": "./bin", "lib": "./lib", "man": "./man/man1"}, "_nodeVersion": "0.12.13", "dependencies": {"asn1": "~0.2.3", "jsbn": "~0.1.0", "getpass": "^0.1.1", "dashdash": "^1.12.0", "ecc-jsbn": "~0.0.1", "tweetnacl": "~0.13.0", "jodid25519": "^1.0.0", "assert-plus": "^1.0.0"}, "devDependencies": {"tape": "^3.5.0", "temp": "^0.8.2", "sinon": "^1.17.2", "benchmark": "^1.0.0"}, "optionalDependencies": {"jsbn": "~0.1.0", "ecc-jsbn": "~0.0.1", "tweetnacl": "~0.13.0", "jodid25519": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/sshpk-1.8.0.tgz_1461292391156_0.6731044049374759", "host": "packages-16-east.internal.npmjs.com"}}, "1.8.1": {"name": "sshpk", "version": "1.8.1", "author": {"name": "Joyent, Inc"}, "license": "MIT", "_id": "sshpk@1.8.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/node-sshpk#readme", "bugs": {"url": "https://github.com/arekinath/node-sshpk/issues"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "man": ["/Users/<USER>/dev/sshpk/man/man1/sshpk-conv.1", "/Users/<USER>/dev/sshpk/man/man1/sshpk-sign.1", "/Users/<USER>/dev/sshpk/man/man1/sshpk-verify.1"], "dist": {"shasum": "6cb6cd073d27fc323207dd414614708f235a4b07", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.8.1.tgz", "integrity": "sha512-YzSug31Zaj/rE16yZ2OQeh41+Q3BTy3BSE9tjclA+t/LPkODgUG4Lgb0AyPLln7MJaDuhfY5Kn1wSmRoxNtPeQ==", "signatures": [{"sig": "MEQCIBpFrCSt5QhpgjnKsecD7lqUzNoY+S0Y/ZdqHuP6dJMsAiBh5o1dlngEE7FFd16eY4wH98zfkQ4dqg08Zi4RMfwdDQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "6cb6cd073d27fc323207dd414614708f235a4b07", "engines": {"node": ">=0.10.0"}, "gitHead": "6374f3ed714efd80f33481fe99d40282ceb9be0e", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arekinath/node-sshpk.git", "type": "git"}, "_npmVersion": "2.15.1", "description": "A library for finding and using SSH public keys", "directories": {"bin": "./bin", "lib": "./lib", "man": "./man/man1"}, "_nodeVersion": "0.12.13", "dependencies": {"asn1": "~0.2.3", "jsbn": "~0.1.0", "getpass": "^0.1.1", "dashdash": "^1.12.0", "ecc-jsbn": "~0.0.1", "tweetnacl": "~0.13.0", "jodid25519": "^1.0.0", "assert-plus": "^1.0.0"}, "devDependencies": {"tape": "^3.5.0", "temp": "^0.8.2", "sinon": "^1.17.2", "benchmark": "^1.0.0"}, "optionalDependencies": {"jsbn": "~0.1.0", "ecc-jsbn": "~0.0.1", "tweetnacl": "~0.13.0", "jodid25519": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/sshpk-1.8.1.tgz_1461292754249_0.1356447017751634", "host": "packages-16-east.internal.npmjs.com"}}, "1.8.2": {"name": "sshpk", "version": "1.8.2", "author": {"name": "Joyent, Inc"}, "license": "MIT", "_id": "sshpk@1.8.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/node-sshpk#readme", "bugs": {"url": "https://github.com/arekinath/node-sshpk/issues"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "man": ["/home/<USER>/dev/node-sshpk/man/man1/sshpk-conv.1", "/home/<USER>/dev/node-sshpk/man/man1/sshpk-sign.1", "/home/<USER>/dev/node-sshpk/man/man1/sshpk-verify.1"], "dist": {"shasum": "d217f6661c59ce7e042f49562a582e6a6d06b164", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.8.2.tgz", "integrity": "sha512-19JUh+gP15bgiT5LiFD5Hr1Q+eW0Mgrc+eVjNYM5OS4LfF4fyuRyXuBfPU0vKO1ZXAYnKiCj/uFmmPSlRyBI8w==", "signatures": [{"sig": "MEQCIFZKigoQv1Ff6cEDHNibxjSQEYCsgyYDtSzsTyGO8YGDAiAI4aAlm81Xyhw716mBb+OZQIygegUeblcz7fc92bjH+Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "d217f6661c59ce7e042f49562a582e6a6d06b164", "engines": {"node": ">=0.10.0"}, "gitHead": "763f4a67aaed61abd5d425acd106502d465fc01b", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arekinath/node-sshpk.git", "type": "git"}, "_npmVersion": "2.14.9", "description": "A library for finding and using SSH public keys", "directories": {"bin": "./bin", "lib": "./lib", "man": "./man/man1"}, "_nodeVersion": "0.12.9", "dependencies": {"asn1": "~0.2.3", "jsbn": "~0.1.0", "getpass": "^0.1.1", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.13.0", "jodid25519": "^1.0.0", "assert-plus": "^1.0.0"}, "devDependencies": {"tape": "^3.5.0", "temp": "^0.8.2", "sinon": "^1.17.2", "benchmark": "^1.0.0"}, "optionalDependencies": {"jsbn": "~0.1.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.13.0", "jodid25519": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/sshpk-1.8.2.tgz_1461556618767_0.8821105812676251", "host": "packages-12-west.internal.npmjs.com"}}, "1.8.3": {"name": "sshpk", "version": "1.8.3", "author": {"name": "Joyent, Inc"}, "license": "MIT", "_id": "sshpk@1.8.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/node-sshpk#readme", "bugs": {"url": "https://github.com/arekinath/node-sshpk/issues"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "man": ["/Users/<USER>/dev/sshpk/man/man1/sshpk-conv.1", "/Users/<USER>/dev/sshpk/man/man1/sshpk-sign.1", "/Users/<USER>/dev/sshpk/man/man1/sshpk-verify.1"], "dist": {"shasum": "890cc9d614dc5292e5cb1a543b03c9abaa5c374e", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.8.3.tgz", "integrity": "sha512-J8KGisqeY0NkCLaXSnmUkk9tIkIZ4g+PeB1hFQUuoAlsLGooNslBNZt9HSN7xgzRqisJ9xD0lpKNPxrT8n6OQg==", "signatures": [{"sig": "MEUCIEupbt/ubcYLV9NH0TMwyzepuJBajwM24HigxoIPtALMAiEAwWzaldYPnXurnNW65joPR7tHPIADctYcUI4mD/8p0A4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "890cc9d614dc5292e5cb1a543b03c9abaa5c374e", "engines": {"node": ">=0.10.0"}, "gitHead": "82d39066b2df4e8284350ff5ebb08c5b95c74652", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arekinath/node-sshpk.git", "type": "git"}, "_npmVersion": "2.15.1", "description": "A library for finding and using SSH public keys", "directories": {"bin": "./bin", "lib": "./lib", "man": "./man/man1"}, "_nodeVersion": "0.12.13", "dependencies": {"asn1": "~0.2.3", "jsbn": "~0.1.0", "getpass": "^0.1.1", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.13.0", "jodid25519": "^1.0.0", "assert-plus": "^1.0.0"}, "devDependencies": {"tape": "^3.5.0", "temp": "^0.8.2", "sinon": "^1.17.2", "benchmark": "^1.0.0"}, "optionalDependencies": {"jsbn": "~0.1.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.13.0", "jodid25519": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/sshpk-1.8.3.tgz_1461968607532_0.32797130732797086", "host": "packages-16-east.internal.npmjs.com"}}, "1.9.0": {"name": "sshpk", "version": "1.9.0", "author": {"name": "Joyent, Inc"}, "license": "MIT", "_id": "sshpk@1.9.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/node-sshpk#readme", "bugs": {"url": "https://github.com/arekinath/node-sshpk/issues"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "man": ["/Users/<USER>/dev/node-sshpk/man/man1/sshpk-conv.1", "/Users/<USER>/dev/node-sshpk/man/man1/sshpk-sign.1", "/Users/<USER>/dev/node-sshpk/man/man1/sshpk-verify.1"], "dist": {"shasum": "b3ca15b9df95d36d8197ea2eb2123a70ef2349b7", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.9.0.tgz", "integrity": "sha512-u6/Gem+ESMUTEi2mYpeW7bUckz6S6xGkrzp6T4VloRP7+TS2M4kXlrA4kS1RGG0/Acxc8S4urGmQPD4JAtxFvA==", "signatures": [{"sig": "MEYCIQDOr2WZRfmyfuazywJDk7Fcn+LT5qiCbn96tf4jrMqSyAIhAPcTFGOw6l7Od9yicTML1zSjTqkoJQJ+n5Ctsb1DjdBn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "b3ca15b9df95d36d8197ea2eb2123a70ef2349b7", "engines": {"node": ">=0.10.0"}, "gitHead": "bf3f83cc4a2230d501f98602df8c060099eb7ca6", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arekinath/node-sshpk.git", "type": "git"}, "_npmVersion": "2.15.8", "description": "A library for finding and using SSH public keys", "directories": {"bin": "./bin", "lib": "./lib", "man": "./man/man1"}, "_nodeVersion": "0.12.15", "dependencies": {"asn1": "~0.2.3", "jsbn": "~0.1.0", "getpass": "^0.1.1", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.13.0", "jodid25519": "^1.0.0", "assert-plus": "^1.0.0"}, "devDependencies": {"tape": "^3.5.0", "temp": "^0.8.2", "sinon": "^1.17.2", "benchmark": "^1.0.0"}, "optionalDependencies": {"jsbn": "~0.1.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.13.0", "jodid25519": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/sshpk-1.9.0.tgz_1469644290699_0.8545950362458825", "host": "packages-16-east.internal.npmjs.com"}}, "1.9.1": {"name": "sshpk", "version": "1.9.1", "author": {"name": "Joyent, Inc"}, "license": "MIT", "_id": "sshpk@1.9.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/node-sshpk#readme", "bugs": {"url": "https://github.com/arekinath/node-sshpk/issues"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "man": ["/Users/<USER>/dev/node-sshpk/man/man1/sshpk-conv.1", "/Users/<USER>/dev/node-sshpk/man/man1/sshpk-sign.1", "/Users/<USER>/dev/node-sshpk/man/man1/sshpk-verify.1"], "dist": {"shasum": "13a43cb36e05a87b97eb5939b5621e32a699ebd4", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.9.1.tgz", "integrity": "sha512-1wLb7YX+MQe60seAfMiYbtpG6+azOJghnjoxo3wRp4N6FvOuxes/emwb97ismPCzmc2yIR1nPVXEkwfcMEO07A==", "signatures": [{"sig": "MEUCIQCewoqdla0A0w9ADAb3GgdBz+GK8RM8AbinZ9urnjUBugIgBddciWF33/TSxSYEhbzrTTblrV9hwdmvQR52zr1ojyk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "13a43cb36e05a87b97eb5939b5621e32a699ebd4", "engines": {"node": ">=0.10.0"}, "gitHead": "079f792ccd8a44acbd65d70ad94f42682715d3f0", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arekinath/node-sshpk.git", "type": "git"}, "_npmVersion": "2.15.8", "description": "A library for finding and using SSH public keys", "directories": {"bin": "./bin", "lib": "./lib", "man": "./man/man1"}, "_nodeVersion": "0.12.15", "dependencies": {"asn1": "~0.2.3", "jsbn": "~0.1.0", "getpass": "^0.1.1", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.13.0", "jodid25519": "^1.0.0", "assert-plus": "^1.0.0"}, "devDependencies": {"tape": "^3.5.0", "temp": "^0.8.2", "sinon": "^1.17.2", "benchmark": "^1.0.0"}, "optionalDependencies": {"jsbn": "~0.1.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.13.0", "jodid25519": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/sshpk-1.9.1.tgz_1469729314821_0.18617548048496246", "host": "packages-16-east.internal.npmjs.com"}}, "1.9.2": {"name": "sshpk", "version": "1.9.2", "author": {"name": "Joyent, Inc"}, "license": "MIT", "_id": "sshpk@1.9.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/node-sshpk#readme", "bugs": {"url": "https://github.com/arekinath/node-sshpk/issues"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "man": ["/Users/<USER>/dev/node-sshpk/man/man1/sshpk-conv.1", "/Users/<USER>/dev/node-sshpk/man/man1/sshpk-sign.1", "/Users/<USER>/dev/node-sshpk/man/man1/sshpk-verify.1"], "dist": {"shasum": "3b41351bbad5c34ddf4bd8119937efee31a46765", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.9.2.tgz", "integrity": "sha512-ybqmfX7I4lILkWPMg1La2SC5B99iIDdGKmeuV+QBkOmkIqijX8gTQvmbYaEFwvZ7lNxjThI7XVx15SZlRwzR5Q==", "signatures": [{"sig": "MEUCIQC1E+fCLHGGH4Mowv8x4j5t0hbPKkvdz3wA2iXBgn4onAIgb82T+XxbyocMprJxO/Eg1XReD+p/Gd9ObGbCaGxLcag=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "3b41351bbad5c34ddf4bd8119937efee31a46765", "engines": {"node": ">=0.10.0"}, "gitHead": "a8b794384822a52eea5ed3b2f192a780b7909609", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arekinath/node-sshpk.git", "type": "git"}, "_npmVersion": "2.15.8", "description": "A library for finding and using SSH public keys", "directories": {"bin": "./bin", "lib": "./lib", "man": "./man/man1"}, "_nodeVersion": "0.12.15", "dependencies": {"asn1": "~0.2.3", "jsbn": "~0.1.0", "getpass": "^0.1.1", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.13.0", "jodid25519": "^1.0.0", "assert-plus": "^1.0.0"}, "devDependencies": {"tape": "^3.5.0", "temp": "^0.8.2", "sinon": "^1.17.2", "benchmark": "^1.0.0"}, "optionalDependencies": {"jsbn": "~0.1.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.13.0", "jodid25519": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/sshpk-1.9.2.tgz_1469841656006_0.10793639998883009", "host": "packages-16-east.internal.npmjs.com"}}, "1.10.0": {"name": "sshpk", "version": "1.10.0", "author": {"name": "Joyent, Inc"}, "license": "MIT", "_id": "sshpk@1.10.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/node-sshpk#readme", "bugs": {"url": "https://github.com/arekinath/node-sshpk/issues"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "man": ["/Users/<USER>/dev/node-sshpk/man/man1/sshpk-conv.1", "/Users/<USER>/dev/node-sshpk/man/man1/sshpk-sign.1", "/Users/<USER>/dev/node-sshpk/man/man1/sshpk-verify.1"], "dist": {"shasum": "104d6ba2afb2ac099ab9567c0d193977f29c6dfa", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.10.0.tgz", "integrity": "sha512-G4fEgEQW0iRpmTsexYupcrttiidqcG0rdbfR2JzzI7s5znLbIRpo4WxV6QdSvviL0qqbBL+QIPeurfjsxMtqjg==", "signatures": [{"sig": "MEQCIDD7Fk7llaw0G0sonT+JvYH7zlq84Ff7SjTsr3SAkS6sAiAiIfTrU6Z83fJTgT40PV42mQQf8PmdSL0k/s/fxg5y9A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "104d6ba2afb2ac099ab9567c0d193977f29c6dfa", "engines": {"node": ">=0.10.0"}, "gitHead": "f3c297270aad821dda4f8b3381f3413537608e8e", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arekinath/node-sshpk.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "A library for finding and using SSH public keys", "directories": {"bin": "./bin", "lib": "./lib", "man": "./man/man1"}, "_nodeVersion": "0.12.15", "dependencies": {"asn1": "~0.2.3", "jsbn": "~0.1.0", "getpass": "^0.1.1", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.13.0", "jodid25519": "^1.0.0", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0"}, "devDependencies": {"tape": "^3.5.0", "temp": "^0.8.2", "sinon": "^1.17.2", "benchmark": "^1.0.0"}, "optionalDependencies": {"jsbn": "~0.1.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.13.0", "jodid25519": "^1.0.0", "bcrypt-pbkdf": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/sshpk-1.10.0.tgz_1472499565403_0.468067049048841", "host": "packages-16-east.internal.npmjs.com"}}, "1.10.1": {"name": "sshpk", "version": "1.10.1", "author": {"name": "Joyent, Inc"}, "license": "MIT", "_id": "sshpk@1.10.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/node-sshpk#readme", "bugs": {"url": "https://github.com/arekinath/node-sshpk/issues"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "man": ["/Users/<USER>/dev/node-sshpk/man/man1/sshpk-conv.1", "/Users/<USER>/dev/node-sshpk/man/man1/sshpk-sign.1", "/Users/<USER>/dev/node-sshpk/man/man1/sshpk-verify.1"], "dist": {"shasum": "30e1a5d329244974a1af61511339d595af6638b0", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.10.1.tgz", "integrity": "sha512-ywhcxCSLw4u+wfJM4XDCtuyXmGEiJ3kHZcnaiehYztqaRf3hj9RE8yamh77ToBD59GxfrQZawktGV+vm8cFdPw==", "signatures": [{"sig": "MEUCIQCofWmR5Lg0NPms2HhCw8Jkkz4PLktACfTp6AIjerv0oQIgdkOv3qNVwjBVx0DB0TF+ks5W+bKQof11RFt1HPNjriE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "30e1a5d329244974a1af61511339d595af6638b0", "engines": {"node": ">=0.10.0"}, "gitHead": "4212272b3889f2df155d2aa8a1a5305fe7a7d3a5", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arekinath/node-sshpk.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "A library for finding and using SSH public keys", "directories": {"bin": "./bin", "lib": "./lib", "man": "./man/man1"}, "_nodeVersion": "0.12.15", "dependencies": {"asn1": "~0.2.3", "jsbn": "~0.1.0", "getpass": "^0.1.1", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.14.0", "jodid25519": "^1.0.0", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0"}, "devDependencies": {"tape": "^3.5.0", "temp": "^0.8.2", "sinon": "^1.17.2", "benchmark": "^1.0.0"}, "optionalDependencies": {"jsbn": "~0.1.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.14.0", "jodid25519": "^1.0.0", "bcrypt-pbkdf": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/sshpk-1.10.1.tgz_1475095320582_0.4095200637821108", "host": "packages-16-east.internal.npmjs.com"}}, "1.10.2": {"name": "sshpk", "version": "1.10.2", "author": {"name": "Joyent, Inc"}, "license": "MIT", "_id": "sshpk@1.10.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/node-sshpk#readme", "bugs": {"url": "https://github.com/arekinath/node-sshpk/issues"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "man": ["/Users/<USER>/dev/node-sshpk/man/man1/sshpk-conv.1", "/Users/<USER>/dev/node-sshpk/man/man1/sshpk-sign.1", "/Users/<USER>/dev/node-sshpk/man/man1/sshpk-verify.1"], "dist": {"shasum": "d5a804ce22695515638e798dbe23273de070a5fa", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.10.2.tgz", "integrity": "sha512-KQh7n9HiKvK33VUUAew1GE7GC0kaPyvVQk+bulVMVJdLsvrMsjFpk44qQASup2gm4ZxAWQmqPZV1AzhCzyk1AA==", "signatures": [{"sig": "MEQCIDLnG5Z7+S0s9TFF5qiZ03C460XBPeas3MAruKcagefgAiAdTWlhQ2VGzjuC+N6hR3p08RKWwd2WmMtDJbd5deim1A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "d5a804ce22695515638e798dbe23273de070a5fa", "engines": {"node": ">=0.10.0"}, "gitHead": "3ae535658f83e610c0dd5bb97a1b2105db7220c7", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arekinath/node-sshpk.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "A library for finding and using SSH public keys", "directories": {"bin": "./bin", "lib": "./lib", "man": "./man/man1"}, "_nodeVersion": "0.12.15", "dependencies": {"asn1": "~0.2.3", "jsbn": "~0.1.0", "getpass": "^0.1.1", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.14.0", "jodid25519": "^1.0.0", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0"}, "devDependencies": {"tape": "^3.5.0", "temp": "^0.8.2", "sinon": "^1.17.2", "benchmark": "^1.0.0"}, "optionalDependencies": {"jsbn": "~0.1.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.14.0", "jodid25519": "^1.0.0", "bcrypt-pbkdf": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/sshpk-1.10.2.tgz_1484356342573_0.6690851037856191", "host": "packages-12-west.internal.npmjs.com"}}, "1.11.0": {"name": "sshpk", "version": "1.11.0", "author": {"name": "Joyent, Inc"}, "license": "MIT", "_id": "sshpk@1.11.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/node-sshpk#readme", "bugs": {"url": "https://github.com/arekinath/node-sshpk/issues"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "man": ["/Users/<USER>/dev/sshpk/man/man1/sshpk-conv.1", "/Users/<USER>/dev/sshpk/man/man1/sshpk-sign.1", "/Users/<USER>/dev/sshpk/man/man1/sshpk-verify.1"], "dist": {"shasum": "2d8d5ebb4a6fab28ffba37fa62a90f4a3ea59d77", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.11.0.tgz", "integrity": "sha512-xxv7jMGrKPHioDhw9zr2e4t2XwmuqfIP1mj4GT0/9A78LywlzYJD326y5IgiLssCMwF7hCSU4cqQlONkq0eZeg==", "signatures": [{"sig": "MEYCIQDfux1mF6JEfTDN2z8vlFwCdVVlQarJ74yFboB7n+j94gIhAN66RIljlZcoGXEyo6AWw30qF1X2bXgvtOXZZMQTHFPs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "2d8d5ebb4a6fab28ffba37fa62a90f4a3ea59d77", "engines": {"node": ">=0.10.0"}, "gitHead": "3bd4c3866a481845683a9e23d5062fff0b4b1bb0", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arekinath/node-sshpk.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "A library for finding and using SSH public keys", "directories": {"bin": "./bin", "lib": "./lib", "man": "./man/man1"}, "_nodeVersion": "0.12.18", "dependencies": {"asn1": "~0.2.3", "jsbn": "~0.1.0", "getpass": "^0.1.1", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.14.0", "jodid25519": "^1.0.0", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0"}, "devDependencies": {"tape": "^3.5.0", "temp": "^0.8.2", "sinon": "^1.17.2", "benchmark": "^1.0.0"}, "optionalDependencies": {"jsbn": "~0.1.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.14.0", "jodid25519": "^1.0.0", "bcrypt-pbkdf": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/sshpk-1.11.0.tgz_1488421334075_0.7735770349390805", "host": "packages-12-west.internal.npmjs.com"}}, "1.13.0": {"name": "sshpk", "version": "1.13.0", "author": {"name": "Joyent, Inc"}, "license": "MIT", "_id": "sshpk@1.13.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/node-sshpk#readme", "bugs": {"url": "https://github.com/arekinath/node-sshpk/issues"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "man": ["/Users/<USER>/dev/sshpk/man/man1/sshpk-conv.1", "/Users/<USER>/dev/sshpk/man/man1/sshpk-sign.1", "/Users/<USER>/dev/sshpk/man/man1/sshpk-verify.1"], "dist": {"shasum": "ff2a3e4fd04497555fed97b39a0fd82fafb3a33c", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.13.0.tgz", "integrity": "sha512-4pjoCmB+Vta6odJ3nrT2zf223+xW6mOJfYtHGX7n5CZUAVm6GhuHLbKeai3c0XoWCu6ZNyPo3Nf71CaUbgck2g==", "signatures": [{"sig": "MEUCIQC95OwIONegCq2GVsOqowX9ymrIeFz9yFtsB2noDEFhOwIgHVU5B15EeVSEJspQ5PlS/WoIW4iN1kAD4CrujSXUj1s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "ff2a3e4fd04497555fed97b39a0fd82fafb3a33c", "engines": {"node": ">=0.10.0"}, "gitHead": "61aa61683a0c8211641423843018aae1b3691bb7", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arekinath/node-sshpk.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "A library for finding and using SSH public keys", "directories": {"bin": "./bin", "lib": "./lib", "man": "./man/man1"}, "_nodeVersion": "0.12.15", "dependencies": {"asn1": "~0.2.3", "jsbn": "~0.1.0", "getpass": "^0.1.1", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.14.0", "jodid25519": "^1.0.0", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0"}, "devDependencies": {"tape": "^3.5.0", "temp": "^0.8.2", "sinon": "^1.17.2", "benchmark": "^1.0.0"}, "optionalDependencies": {"jsbn": "~0.1.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.14.0", "jodid25519": "^1.0.0", "bcrypt-pbkdf": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/sshpk-1.13.0.tgz_1492046338097_0.9430976777803153", "host": "packages-12-west.internal.npmjs.com"}}, "1.13.1": {"name": "sshpk", "version": "1.13.1", "author": {"name": "Joyent, Inc"}, "license": "MIT", "_id": "sshpk@1.13.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/node-sshpk#readme", "bugs": {"url": "https://github.com/arekinath/node-sshpk/issues"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "man": ["/Users/<USER>/dev/sshpk/man/man1/sshpk-conv.1", "/Users/<USER>/dev/sshpk/man/man1/sshpk-sign.1", "/Users/<USER>/dev/sshpk/man/man1/sshpk-verify.1"], "dist": {"shasum": "512df6da6287144316dc4c18fe1cf1d940739be3", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.13.1.tgz", "integrity": "sha512-oKPL4k+ld5d737ZPB4VM99bqNb3JH+ZkxwWXR1iSPtqYbZv81hkLD7v8gmclrgR7aA8uh8ar+q0IF7gHFHg7PQ==", "signatures": [{"sig": "MEUCIQDMsGQbhnkCQNvhXLKCAM8Iz8ulD4lVUGUh1cUR/aMW3AIgBhUmNFSt6PV6AylnAyF7pw7IEKMSVADyr+pqymxDM0M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "512df6da6287144316dc4c18fe1cf1d940739be3", "engines": {"node": ">=0.10.0"}, "gitHead": "a17ec8861242649038dcdba8f8d7df5edf2ddb8c", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arekinath/node-sshpk.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "A library for finding and using SSH public keys", "directories": {"bin": "./bin", "lib": "./lib", "man": "./man/man1"}, "_nodeVersion": "0.12.18", "dependencies": {"asn1": "~0.2.3", "jsbn": "~0.1.0", "getpass": "^0.1.1", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.14.0", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0"}, "devDependencies": {"tape": "^3.5.0", "temp": "^0.8.2", "sinon": "^1.17.2", "benchmark": "^1.0.0"}, "optionalDependencies": {"jsbn": "~0.1.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.14.0", "bcrypt-pbkdf": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/sshpk-1.13.1.tgz_1496888143718_0.9964376483112574", "host": "s3://npm-registry-packages"}}, "1.13.2": {"name": "sshpk", "version": "1.13.2", "author": {"name": "Joyent, Inc"}, "license": "MIT", "_id": "sshpk@1.13.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/node-sshpk#readme", "bugs": {"url": "https://github.com/arekinath/node-sshpk/issues"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "man": ["/Users/<USER>/dev/sshpk/man/man1/sshpk-conv.1", "/Users/<USER>/dev/sshpk/man/man1/sshpk-sign.1", "/Users/<USER>/dev/sshpk/man/man1/sshpk-verify.1"], "dist": {"shasum": "856569df3ffa8464658a393984aed2a48655e014", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.13.2.tgz", "fileCount": 35, "integrity": "sha512-DNemA6WwC2HcQjDR3qp1Nb6R86+6aAN1UxaWxlZQuVLraNb8TOZXiXfcEktKYSEsPB1JOHunmS080Kc9nZivIQ==", "signatures": [{"sig": "MEQCIHmruVlLAFi17tJ+JVLgorkzDhmY6Z8G1U5mhpFia7jmAiBG8ywEbz6gZqN7aOOI3in9Su8ZPCk7VZZBRO0G2xaWaA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 208057}, "main": "lib/index.js", "_from": ".", "_shasum": "856569df3ffa8464658a393984aed2a48655e014", "engines": {"node": ">=0.10.0"}, "gitHead": "46065d38a5e6d1bccf86d3efb2fb83c14e3f9957", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arekinath/node-sshpk.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "A library for finding and using SSH public keys", "directories": {"bin": "./bin", "lib": "./lib", "man": "./man/man1"}, "_nodeVersion": "0.12.18", "dependencies": {"asn1": "~0.2.3", "jsbn": "~0.1.0", "getpass": "^0.1.1", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.14.0", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^3.5.0", "temp": "^0.8.2", "sinon": "^1.17.2", "benchmark": "^1.0.0"}, "optionalDependencies": {"jsbn": "~0.1.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.14.0", "bcrypt-pbkdf": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/sshpk_1.13.2_1520899285256_0.7493494255908402", "host": "s3://npm-registry-packages"}}, "1.14.1": {"name": "sshpk", "version": "1.14.1", "author": {"name": "Joyent, Inc"}, "license": "MIT", "_id": "sshpk@1.14.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/node-sshpk#readme", "bugs": {"url": "https://github.com/arekinath/node-sshpk/issues"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "man": ["/Users/<USER>/dev/sshpk/man/man1/sshpk-conv.1", "/Users/<USER>/dev/sshpk/man/man1/sshpk-sign.1", "/Users/<USER>/dev/sshpk/man/man1/sshpk-verify.1"], "dist": {"shasum": "130f5975eddad963f1d56f92b9ac6c51fa9f83eb", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.14.1.tgz", "fileCount": 35, "integrity": "sha512-/+d3fDmcbBSaYbLHQ/BU9Kt9ZMuyU9kZyG6Icbu/sMA2AN5NgfGuKSAJBl62I5NFsopfVwkufmj5cCRIEXq36g==", "signatures": [{"sig": "MEUCIQDSxmm7XbUzAwi7bVCbUprLoAxdspo9/d0RyyILD8oYqgIgX+4ISE3Erbqrc0xRs7YGTp2M3+np2sJtT2UUvskJN4Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 208057}, "main": "lib/index.js", "_from": ".", "_shasum": "130f5975eddad963f1d56f92b9ac6c51fa9f83eb", "engines": {"node": ">=0.10.0"}, "gitHead": "6edb37cb986b7ddaf0d346440d37287cc059bfee", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arekinath/node-sshpk.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "A library for finding and using SSH public keys", "directories": {"bin": "./bin", "lib": "./lib", "man": "./man/man1"}, "_nodeVersion": "0.12.18", "dependencies": {"asn1": "~0.2.3", "jsbn": "~0.1.0", "getpass": "^0.1.1", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.14.0", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^3.5.0", "temp": "^0.8.2", "sinon": "^1.17.2", "benchmark": "^1.0.0"}, "optionalDependencies": {"jsbn": "~0.1.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.14.0", "bcrypt-pbkdf": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/sshpk_1.14.1_1520899928205_0.6230534223468485", "host": "s3://npm-registry-packages"}}, "1.14.2": {"name": "sshpk", "version": "1.14.2", "author": {"name": "Joyent, Inc"}, "license": "MIT", "_id": "sshpk@1.14.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/node-sshpk#readme", "bugs": {"url": "https://github.com/arekinath/node-sshpk/issues"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "man": ["/home/<USER>/dev/node-sshpk/man/man1/sshpk-conv.1", "/home/<USER>/dev/node-sshpk/man/man1/sshpk-sign.1", "/home/<USER>/dev/node-sshpk/man/man1/sshpk-verify.1"], "dist": {"shasum": "c6fc61648a3d9c4e764fd3fcdf4ea105e492ba98", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.14.2.tgz", "fileCount": 35, "integrity": "sha512-XIQCY8Ye6pY6rRNG+eCQiHyapz1vDY4OsMowlmy31arzqWPjC9phqZoVy+F/Oyz5xjsaDwgBpIMQmhj1kSJJOA==", "signatures": [{"sig": "MEYCIQCFCZFyMiSwPvjBaIvlp+rHojPbS+bxlq+ykH4qBclhxAIhAI9wQKRTVKxNQJC236DeBVUzMpdRTNVdZ2lAicft010N", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 209067, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbFtZyCRA9TVsSAnZWagAASjUQAJjjsdwBj1T7yJoSIYPj\n+0F1gZHNxlYUc713gd4TULwPLtMRpd8E/J/gSpNesHkWRk0wEK4ZXvU9BoTu\nj1guOxt2O5053rigJp7lGF2n4YHL41Wg6JImoB2aNSHwjN/ePo9VMZwq8/Gb\ndd06wHyYFyvbW6ntlV/UXKKcmyiGbjLq3ZEatmqgMwWL5h/Xvx2Vt0MAe4Pp\n7bsk7zy/1OUuz4IVXFnMHQ5+NS33wUCUAHz9JQZCzR2ZNPXx5j1ctH9We3XG\nybkKGMHoiDfaVRt//sJQEmtpgB5OBxU4UX912SsV3oTGsxkl+vT8CigVisE3\nsg0JIgx1JKxeTOJ9kF9cwuLOw5GoGp797n/btqSlSSQhF7ZbGMFk0FEvXIz/\n3ShYUrIRdmLgHMd2IbTolOkQXbsylBnuuGCTNGztvLpYCD8QCCRq2SywSWrg\nUY1Jtj2NdCtHyFR2rnsS2pyiymxHXxGEA7MebLNb7+VJ1Ti0PFC0gLMYHDyG\n5x1WPzXuVPv67Nhh2+1CGpEhj5Ln651Lj6iMduPrqpvFFnGROE0byot/FMvY\n479pqy2CN9v2XHxPyncKl/AhypLvzxvpzgThtl4E8bQoGyfIF1AvKnVB8D9Y\nAgRI3fYNNQXx73Gy1aAmASiwpoRQEFe0R3Yn3xEtobU/zMhGhDRfVBI9fZkX\nAMoW\r\n=/035\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_from": ".", "_shasum": "c6fc61648a3d9c4e764fd3fcdf4ea105e492ba98", "engines": {"node": ">=0.10.0"}, "gitHead": "175758a9473523409339e6c519c470c808ca03de", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arekinath/node-sshpk.git", "type": "git"}, "_npmVersion": "2.14.9", "description": "A library for finding and using SSH public keys", "directories": {"bin": "./bin", "lib": "./lib", "man": "./man/man1"}, "_nodeVersion": "0.12.9", "dependencies": {"asn1": "~0.2.3", "jsbn": "~0.1.0", "getpass": "^0.1.1", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.14.0", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0", "safer-buffer": "^2.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^3.5.0", "temp": "^0.8.2", "sinon": "^1.17.2", "benchmark": "^1.0.0"}, "optionalDependencies": {"jsbn": "~0.1.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.14.0", "bcrypt-pbkdf": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/sshpk_1.14.2_1528223344242_0.5071110838223176", "host": "s3://npm-registry-packages"}}, "1.15.0": {"name": "sshpk", "version": "1.15.0", "author": {"name": "Joyent, Inc"}, "license": "MIT", "_id": "sshpk@1.15.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dap", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "melloc", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/node-sshpk#readme", "bugs": {"url": "https://github.com/arekinath/node-sshpk/issues"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "man": ["/home/<USER>/dev/sshpk/man/man1/sshpk-conv.1", "/home/<USER>/dev/sshpk/man/man1/sshpk-sign.1", "/home/<USER>/dev/sshpk/man/man1/sshpk-verify.1"], "dist": {"shasum": "74e084946318c487a6c7c64796f864301ce35886", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.15.0.tgz", "fileCount": 35, "integrity": "sha512-fAUqwOzq7VlJ/pOgQWRJZmyMXGwXLgslaHYyJpNNg1uHoVFgW3Y8NuC8YBWaPV/9LltfgorM1Z9S2Q+DXwqxgQ==", "signatures": [{"sig": "MEYCIQC5mhyatJrE+ivquKcNJ2SaODUdF5CuJn/T/ujay+YLugIhAOn2Qnbs1RjkQKD08t1Fr69XcSJyNwYiMqmp1cUJdk2Q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 214130, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbv+P9CRA9TVsSAnZWagAAsQcP/1UXq7WNgDwO5el1p8bC\nFr5XZJ3uAtOkoJct5szuZK4hZ0UI/H4g6MkBOztesrKrM9JP5xASNoq/zDOX\nI6+dzRIRyna4YeVf5CtEXnMLdLLH+Wn2jT+7MPhR84CRJJjK6lKj7dKVIXAR\nku9EBHKQtBaddG94uiiFHbDTyhAMw5WhVG4JWujwBiWmAfjio3WIE8myAyrR\nVBaxjALEZTHJBGAK55OLq/JYr4MGeVCsmhsg6Uy4TUUBH5HDVRNzG5CHSNVX\nT/wHLs7Y7akab1e4HN6ts6UViq4z/kvaN3n7XRqqOY9klZ8Djmh5IVJD85lZ\n19jg7ze0FvC1AHvXlQSeirMHjQ/S8q1M4W6w1o4h6iuEkfyPvgOPtS/Ybnj8\nCD1U5svDku5HHkUml0EZtxiiWxRV+Z9yQ+buZISr8riPQf4ttc4IAGdhA0Y8\n/CE1yzI4qbiM2F1guBxSFB1n1UfgyXbcHd0MgivRjXgl2cCaejWKjEPI4U3T\neX8jRfWSVEqefDZt2JmJ0x8StpFTOQ4YFhjRP1DWlzttKaumC0gjkHlfc+r0\na1jDmhb3r4M46OGiflAfiUuRgalL1QIj8oe7Pjf0mZQL+YqxxJ3zHInbp6z5\nMXR9G14c6rlMBnZSH68bugySV0zpljtDK9MXTEibk7AhE7452T1NwP267WM/\ncgm5\r\n=FkRS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_from": ".", "_shasum": "74e084946318c487a6c7c64796f864301ce35886", "engines": {"node": ">=0.10.0"}, "gitHead": "026ef4764a55648dd15f45f7f14ff9da5d1fe2ad", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/joyent/node-sshpk.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "A library for finding and using SSH public keys", "directories": {"bin": "./bin", "lib": "./lib", "man": "./man/man1"}, "_nodeVersion": "0.12.18", "dependencies": {"asn1": "~0.2.3", "jsbn": "~0.1.0", "getpass": "^0.1.1", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.14.0", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0", "safer-buffer": "^2.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^3.5.0", "temp": "^0.8.2", "sinon": "^1.17.2", "benchmark": "^1.0.0"}, "optionalDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/sshpk_1.15.0_1539302396513_0.3649631460632181", "host": "s3://npm-registry-packages"}}, "1.15.1": {"name": "sshpk", "version": "1.15.1", "author": {"name": "Joyent, Inc"}, "license": "MIT", "_id": "sshpk@1.15.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dap", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "melloc", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/node-sshpk#readme", "bugs": {"url": "https://github.com/arekinath/node-sshpk/issues"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "man": ["/home/<USER>/dev/sshpk/man/man1/sshpk-conv.1", "/home/<USER>/dev/sshpk/man/man1/sshpk-sign.1", "/home/<USER>/dev/sshpk/man/man1/sshpk-verify.1"], "dist": {"shasum": "b79a089a732e346c6e0714830f36285cd38191a2", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.15.1.tgz", "fileCount": 35, "integrity": "sha512-mSdgNUaidk+dRU5MhYtN9zebdzF2iG0cNPWy8HG+W8y+fT1JnSkh0fzzpjOa0L7P8i1Rscz38t0h4gPcKz43xA==", "signatures": [{"sig": "MEQCIBcDpurZfzJRzO8i5DIh0k3vYqib/bznYHQsLbbcaig4AiB/OPi6Z+m96oxWB9C50pZnGVxvEi9yMJU0jlGiGJ//Ww==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 214292, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbv/A1CRA9TVsSAnZWagAA5gYP/2AnEPtbUm5vw1bvo0CG\nCiyRWL+Is/CrgQ8N2xWu1ZwQYmVBphqS0vnHcY9cM4SHWUIzlNIPm6I/cbUx\nH/003KgYIYDscKZz/HIHpUxlCMroDLO0/ZaRI4ICpApTsqqOZqtlixO6ONMW\nUB1wotJhgH6qUj6Oo9aMPlXk+UhF9oK8L8xlEX9NZEWiOH3q7glz9WEODV+3\n1/HylenpFRA8050NAevdg6GbfaEA5eNtUbwk7LLDKyp0O04PD75b9Rsgp+Z9\nWVdcmq3QTmnX2R7IDMCcLfRD7gpV7OmzOwcGZZow/c42CYm+MU9R3VKODARZ\nc/JXecahgd9hsKVKm06j4iNkk0susvtFG1LH6+LqakSzYm7NPXAYO8kZjhqM\nE1jXuGxt6h5hSMNTIyRVz3EzKTEKpLnaVl5bniwm5s1H5CIsxYjtUAJ2EB2k\nT4S474JyoPSQMV+H3FhH/Fr/Opv3iA3babWv1h49bs8DPw3hOM0UQzZMWH5t\nOIRvQ8/XtkdAscWOqj1nZ2mvnNT8JiuJP17V4oUybzGpNoyEM2b8Sp/D/Vpr\natwJXKTClyGE4OTVDW3Tp3+e/vM6TNlBNdxadtsVvBjuG1wVAP7vTVA7nNOL\nM8wkLHgw6ovu4Ylj5Z1mC9Sh81E4kp0r5oyhMayOij7L0rfq9Q9oAheKYzOs\ndrnu\r\n=eFBo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_from": ".", "_shasum": "b79a089a732e346c6e0714830f36285cd38191a2", "engines": {"node": ">=0.10.0"}, "gitHead": "2ab4f2a018766559252f2c3426a3735f0860ac0d", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/joyent/node-sshpk.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "A library for finding and using SSH public keys", "directories": {"bin": "./bin", "lib": "./lib", "man": "./man/man1"}, "_nodeVersion": "0.12.18", "dependencies": {"asn1": "~0.2.3", "jsbn": "~0.1.0", "getpass": "^0.1.1", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.14.0", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0", "safer-buffer": "^2.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^3.5.0", "temp": "^0.8.2", "sinon": "^1.17.2", "benchmark": "^1.0.0"}, "optionalDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/sshpk_1.15.1_1539305524898_0.7591491495784306", "host": "s3://npm-registry-packages"}}, "1.15.2": {"name": "sshpk", "version": "1.15.2", "author": {"name": "Joyent, Inc"}, "license": "MIT", "_id": "sshpk@1.15.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dap", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "melloc", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/node-sshpk#readme", "bugs": {"url": "https://github.com/arekinath/node-sshpk/issues"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "man": ["/home/<USER>/dev/node-sshpk/man/man1/sshpk-conv.1", "/home/<USER>/dev/node-sshpk/man/man1/sshpk-sign.1", "/home/<USER>/dev/node-sshpk/man/man1/sshpk-verify.1"], "dist": {"shasum": "c946d6bd9b1a39d0e8635763f5242d6ed6dcb629", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.15.2.tgz", "fileCount": 35, "integrity": "sha512-Ra/OXQtuh0/enyl4ETZAfTaeksa6BXks5ZcjpSUNrjBr0DvrJKX+1fsKDPpT9TBXgHAFsa4510aNVgI8g/+SzA==", "signatures": [{"sig": "MEYCIQC3rbbXwe5WsHCHAWfOWFsyxlRBKSfK0zbtko1gThJZbgIhAKhkFSuafquiz5q6S8E3IU7n8Dz84f0V9OeQu/b5oVkT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 214293, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb18lUCRA9TVsSAnZWagAAGE0P/iE1eESfe+WQTpA37kse\n57qOXlCg3Wj70NwCI/Q6XqjWj34K99f2zafhDoccUNWYje4B3nHWMazkO7WY\nesnETDsQWt3TPF7TiB3mABQHvmu1C76so5cLx6uDiHwZIBKPaCJfZmjiFvOY\nyDT+fmhwRfQJSrjgfOJ9L5KxYmubkqXk2IGNdciiYEP4+EkiI73P/Csmy/7m\naJliAVkRE52iX+9k4cKRoJdNPBJeezYhwF14lQG4ysKxrujh+0Dz0dO6tuH/\nEHxkjJSAfquJskcNFruUVP+Yw5vW8jz4JXnIRbT3TfUCel2W/1/dkqY3YzN5\nCQ7zTsRgd+POWucVG4GZ2hfQWH0xHrpjrazEqATk8ninFwaoQm+Yv4WSXklq\nESCdR1SYffP28cWVKF6r3SHV0Mag7k90tWP5maclCBSJYR8fkwocie4ChVV+\n3lJozYiwU3IdFjUin+4g6JHcCy+KZkRIcTF54N04JIpCBuusdnDwfsmPLDo9\nkGu9CLksr62YVr+h6dmkELI5vYSIVafppWpugwq0avBzy3WLu78wKqQ4X8EY\nvPGQQrXtwcwisyfe2EvjoeUuHC7eV+szW2eo/TStYKfP5OE/W5E7OessodOv\nOXzmUH8KFmHJyrr+GbBPYWAF8ikNI6eeq3zxM5CAgFo5qLVgj6e+OtjtJoAV\njVq5\r\n=ij+M\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_from": ".", "_shasum": "c946d6bd9b1a39d0e8635763f5242d6ed6dcb629", "engines": {"node": ">=0.10.0"}, "gitHead": "c7a6c6833370f69322c47e73e9f4cfdedaf4e8f4", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/joyent/node-sshpk.git", "type": "git"}, "_npmVersion": "2.14.9", "description": "A library for finding and using SSH public keys", "directories": {"bin": "./bin", "lib": "./lib", "man": "./man/man1"}, "_nodeVersion": "0.12.9", "dependencies": {"asn1": "~0.2.3", "jsbn": "~0.1.0", "getpass": "^0.1.1", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.14.0", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0", "safer-buffer": "^2.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^3.5.0", "temp": "^0.8.2", "sinon": "^1.17.2", "benchmark": "^1.0.0"}, "optionalDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/sshpk_1.15.2_1540868435177_0.6798014210154215", "host": "s3://npm-registry-packages"}}, "1.16.0": {"name": "sshpk", "version": "1.16.0", "author": {"name": "Joyent, Inc"}, "license": "MIT", "_id": "sshpk@1.16.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dap", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "melloc", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/node-sshpk#readme", "bugs": {"url": "https://github.com/arekinath/node-sshpk/issues"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "man": ["/home/<USER>/dev/sshpk/man/man1/sshpk-conv.1", "/home/<USER>/dev/sshpk/man/man1/sshpk-sign.1", "/home/<USER>/dev/sshpk/man/man1/sshpk-verify.1"], "dist": {"shasum": "1d4963a2fbffe58050aa9084ca20be81741c07de", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.16.0.tgz", "fileCount": 36, "integrity": "sha512-Zhev35/y7hRMcID/upReIvRse+I9SVhyVre/KTJSJQWMz3C3+G+HpO7m1wK/yckEtujKZ7dS4hkVxAnmHaIGVQ==", "signatures": [{"sig": "MEQCIDUJ0YnJdEfaLikyDrLz344d5Mxtn631PSsg6k7GftamAiBLoN3Yg/zBWoyROq/UWy5J489d7+r91MDqM5ZW+H0paA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 224787, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcHEepCRA9TVsSAnZWagAA2OkQAJSlbVdgQ/jlfALbl8NW\nEJA6sWX1GI6A8tPNbdAed4SvcACXotB8ECRXdRtLwHAHR0bOtF7VLJ2TpnIB\nAqME9pAjDyc/FThoRNSn0pnOgYSQhbSq8aHDHvEMnWuEQP8GestoD3WVGG4N\nMzxwYy03zLWoJtsLToooAD1wD0a9gqtUU5QNjLVOPbPozi2hdv2dIVkqFGON\nOULDFETXURHG7JxzvGiGrx9r6szoXd5ogEgzKfuIM4Ybdg62E5zdhgVSf/AP\n5vHosBQGwUgpYS8mlgAaQrKy33/bf+hf5RQDk/CMbMcWPe89w1uK15GbHzmE\naktB/0kLvn7Aa5McpwQet4wqssdPoPHGnxT7NBufFMRMEcwb6G7iab9bSlb9\nc6OnOhFz0AN6gFNMzI+vKyq9sh/YtHHCmt5pOgtU4fEDRpNDtT0kZpJKo0fr\ns5QVPXDkwlQfvCbg97FMfcc9jt0kW2p3mchzTWireb6Eu273+Ge49FRcoUVY\nUUAUI64MyRWcZzPf/BiYuLRmtLVrk0eKvmxq4KG+5YOgLRaL7kpC4TVz16eq\nLXVcHksejSZrnyEe7iSVvA4Rg45vn4amnPTUNocPduQ8Q2g0wC+3bAatKHcI\nL4eWqSMfrzPa6TTIMJLojyqt+P2GJC69Y+/I0Sv2QrUu0ASUVNUsjfoMy/SB\nIhiD\r\n=KY/A\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_from": ".", "_shasum": "1d4963a2fbffe58050aa9084ca20be81741c07de", "engines": {"node": ">=0.10.0"}, "gitHead": "574ff21e77117b478baf4664856bfc4b0aa41a12", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/joyent/node-sshpk.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "A library for finding and using SSH public keys", "directories": {"bin": "./bin", "lib": "./lib", "man": "./man/man1"}, "_nodeVersion": "0.12.18", "dependencies": {"asn1": "~0.2.3", "jsbn": "~0.1.0", "getpass": "^0.1.1", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.14.0", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0", "safer-buffer": "^2.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^3.5.0", "temp": "^0.8.2", "sinon": "^1.17.2", "benchmark": "^1.0.0"}, "optionalDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/sshpk_1.16.0_1545357224616_0.29220383678770756", "host": "s3://npm-registry-packages"}}, "1.16.1": {"name": "sshpk", "version": "1.16.1", "author": {"name": "Joyent, Inc"}, "license": "MIT", "_id": "sshpk@1.16.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dap", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "melloc", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/node-sshpk#readme", "bugs": {"url": "https://github.com/arekinath/node-sshpk/issues"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "man": ["/home/<USER>/dev/sshpk/man/man1/sshpk-conv.1", "/home/<USER>/dev/sshpk/man/man1/sshpk-sign.1", "/home/<USER>/dev/sshpk/man/man1/sshpk-verify.1"], "dist": {"shasum": "fb661c0bef29b39db40769ee39fa70093d6f6877", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.16.1.tgz", "fileCount": 36, "integrity": "sha512-HXXqVUq7+pcKeLqqZj6mHFUMvXtOJt1uoUx09pFW6011inTMxqI8BA8PM95myrIyyKwdnzjdFjLiE6KBPVtJIg==", "signatures": [{"sig": "MEUCIBqeAViYL8NvIdnMv3FSeYai1KnjK44joWKx7U/5tCqvAiEAhU36u79rmOQnYlzKwYMEBBauiYKEIDSa80P5FxDOpKg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 225493, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcSN3gCRA9TVsSAnZWagAAXGUP/0r6539fiSwCFuS8XYqB\nXnM4k2f4KvlCovAhf/3Xrn/3713RqKNF5P2TSHlBcJVFO5F5YIQnG1qwNXwk\nuIK89IxnsQaGQBATMielB8zmt40P5tOFDVZs+aDc2mCjdBgmQ/UFeZCm99+a\nFFUnTAtGV5tY3Wk+BgUYRiRhdHAcbSR0/bMtLjzcyZiVxyUg3YNyRJMa8zVK\no5qmhHDADsIYxhU5zxNwVu07lUtOwK/uL74ZuTXvaF5PhcCQhy9+4vNkvfIO\n3g/lFOU+qR7hNYs9MYOlTY3gbMV0umDtn802NSiic9u/FFB9hqOREDv0Xljn\nIrHy3SCzEzDj83pVR25Hn6p7SKN1KSevVP7go61ZoDAyzY+4MMNPu+4cdopH\n52HPdLRmdfDIkdPRf8nS9w3eUnPG/+SD24yhCW4NbCAkSX2is1KEQqrcZDQg\n5817ko30vdNpG5UtBVIeYGlGUJoViYjgvM7/duvlW+9IT/XRotACNUBSrOT5\nF3j9+Mlx9J7hMIAk8yNzh6Y6HRf2J7ACNwU5ehn7XiUsROzjjcTM6zEaosjX\nCR/Qjru4UePVAKT2gPKcDxSAPlA1G6lSEl9xTqfpg4nzfTdEjTiDAZHC39or\nw05p174+vuXy2RgU4ebLZ6EFdeKL+l0KU1htszw04UatsaPiGMsAJH7sVyMI\nkA8N\r\n=aIg1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_from": ".", "_shasum": "fb661c0bef29b39db40769ee39fa70093d6f6877", "engines": {"node": ">=0.10.0"}, "gitHead": "1aece0d1df155e60e4cb0ae42cb3ed7aa043e174", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/joyent/node-sshpk.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "A library for finding and using SSH public keys", "directories": {"bin": "./bin", "lib": "./lib", "man": "./man/man1"}, "_nodeVersion": "0.12.18", "dependencies": {"asn1": "~0.2.3", "jsbn": "~0.1.0", "getpass": "^0.1.1", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.14.0", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0", "safer-buffer": "^2.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^3.5.0", "temp": "^0.8.2", "sinon": "^1.17.2", "benchmark": "^1.0.0"}, "optionalDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/sshpk_1.16.1_1548279263799_0.6978058808540124", "host": "s3://npm-registry-packages"}}, "1.17.0": {"name": "sshpk", "version": "1.17.0", "author": {"name": "Joyent, Inc"}, "license": "MIT", "_id": "sshpk@1.17.0", "maintainers": [{"name": "todd.whiteman", "email": "<EMAIL>"}, {"name": "kusor", "email": "<EMAIL>"}, {"name": "wyatt", "email": "<EMAIL>"}, {"name": "micha<PERSON>.hicks", "email": "<EMAIL>"}, {"name": "bah<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chudley", "email": "<EMAIL>"}, {"name": "tcha<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "d<PERSON>ell", "email": "<EMAIL>"}, {"name": "dap", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "melloc", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/node-sshpk#readme", "bugs": {"url": "https://github.com/arekinath/node-sshpk/issues"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "man": ["/Users/<USER>/repos/joyent/node-sshpk/man/man1/sshpk-conv.1", "/Users/<USER>/repos/joyent/node-sshpk/man/man1/sshpk-sign.1", "/Users/<USER>/repos/joyent/node-sshpk/man/man1/sshpk-verify.1"], "dist": {"shasum": "578082d92d4fe612b13007496e543fa0fbcbe4c5", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.17.0.tgz", "fileCount": 36, "integrity": "sha512-/9HIEs1ZXGhSPE8X6Ccm7Nam1z8KcoCqPdI7ecm1N33EzAetWahvQWVqLZtaZQ+IDKX4IyA2o0gBzqIMkAagHQ==", "signatures": [{"sig": "MEYCIQCREA9xUWnPeycgVlo3WGA9hzjFEwUypGHIO86hYRx4IgIhAI4GOGbflCUXlKFJWAeUHzsKZTnartAl9iOQH/LoMDsW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 230511, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2KSRCRA9TVsSAnZWagAAoCEQAKULF8esvxDrotZij/Bd\nogIQ70AWe4brcXAbFB8Xs2MZA4PfsEaBocCI1iEVT0oZfhUrMBPnqR+1GaiZ\nHZtq19rWjd6GMWZjGgkAr24Mg6EVahHblATpBVe5B4wvUrt8z5NVXIFn9dDK\nX2JFlapQT5+ENbqV7eyCmh3in3ZCwuqP4EezAVR7XSbl+8UHKmPj2KxPJKI6\nKsW1U0+lBoVHi7oRKXWcGDfziO5/aQQ7vtdCpRBYcIzheXolYoTdpjRn3EEo\n5aqNV/fxHviICLtR6GmVmKgSTPD9am+MV4qJZUrfxxDpZo4uDUEoNgPaOIUm\n2NrCmolZyWHXayRFQ4R07tSuEn/oyniudmtDQHjzYAYtsM2F3bycvzMogtEI\nYJXyJCgggOGkUGYpcyrLA3uyAue5xp2aITbgHlLzn737L38OJ9+38+cTpMrT\nbBrJhyfOWu+7kTyLHIOAWy1A7iClzZZncee9fUyYLVYJFEsGH5RrXAcZoMMc\nW6NQ01gyvazDk1wN9LI2RJnA9bdWVEDlzIAISNlkJDtOzLx2oZ+IS7mIvgvi\nq78+EC06bggxmoM62ec3yKui7+1a1JzsB3+hHWVDo5eE+QESs61c61/wD01P\nKI9Fu6JYmZW3Tgx4p23xXZLnD+Vo/1QXIHGkYG+7rNVAm2J2r1aMERuFsWeh\nqege\r\n=KU2y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=0.10.0"}, "gitHead": "3e8366433e9c2ef688e7b14935bd4d4fa264229d", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "bah<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/joyent/node-sshpk.git", "type": "git"}, "_npmVersion": "6.14.13", "description": "A library for finding and using SSH public keys", "directories": {"bin": "./bin", "lib": "./lib", "man": "./man/man1"}, "_nodeVersion": "14.18.1", "dependencies": {"asn1": "~0.2.3", "jsbn": "~0.1.0", "getpass": "^0.1.1", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.14.0", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0", "safer-buffer": "^2.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^3.5.0", "temp": "^0.8.2", "sinon": "^1.17.2", "benchmark": "^1.0.0"}, "optionalDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/sshpk_1.17.0_1641587857836_0.4214761798300204", "host": "s3://npm-registry-packages"}}, "1.18.0": {"name": "sshpk", "version": "1.18.0", "author": {"name": "Joyent, Inc"}, "license": "MIT", "_id": "sshpk@1.18.0", "maintainers": [{"name": "bah<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "todd.whiteman", "email": "<EMAIL>"}, {"name": "kusor", "email": "<EMAIL>"}, {"name": "micha<PERSON>.hicks", "email": "<EMAIL>"}, {"name": "dap", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "melloc", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/node-sshpk#readme", "bugs": {"url": "https://github.com/arekinath/node-sshpk/issues"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "man": ["man/man1/sshpk-conv.1", "man/man1/sshpk-sign.1", "man/man1/sshpk-verify.1"], "dist": {"shasum": "1663e55cddf4d688b86a46b77f0d5fe363aba028", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.18.0.tgz", "fileCount": 36, "integrity": "sha512-2p2KJZTSqQ/I3+HX42EpYOa2l3f8Erv8MWKsy2I9uf4wA7yFIkXRffYdsx86y6z4vHtV8u7g+pPlr8/4ouAxsQ==", "signatures": [{"sig": "MEMCH3KmsTHyjBLhb29KbohmsZYfyUQi9BELtOmfvnrL24ICIBPFOH9yEV++smfnirjMLTZu2nL05zA6bIxKAQ8kxNRH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 230846}, "main": "lib/index.js", "engines": {"node": ">=0.10.0"}, "gitHead": "8df038598965802fe287715ab4389e56da2593c5", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "bah<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/joyent/node-sshpk.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A library for finding and using SSH public keys", "directories": {"bin": "./bin", "lib": "./lib", "man": "./man/man1"}, "_nodeVersion": "18.16.1", "dependencies": {"asn1": "~0.2.3", "jsbn": "~0.1.0", "getpass": "^0.1.1", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.14.0", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0", "safer-buffer": "^2.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^3.5.0", "temp": "^0.8.2", "sinon": "^1.17.2", "benchmark": "^1.0.0"}, "optionalDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/sshpk_1.18.0_1697736191124_0.5436805438683918", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2015-09-19T04:48:17.976Z", "modified": "2025-02-07T15:28:20.204Z", "1.0.0": "2015-09-19T04:48:17.976Z", "1.0.1": "2015-09-21T17:59:24.856Z", "1.0.2": "2015-09-22T18:06:39.992Z", "1.0.3": "2015-09-22T18:57:51.802Z", "1.0.4": "2015-09-22T23:25:17.084Z", "1.1.0": "2015-09-25T05:57:43.194Z", "1.2.0": "2015-09-28T06:47:28.445Z", "1.2.1": "2015-09-28T07:11:35.597Z", "1.3.0": "2015-09-30T23:00:53.424Z", "1.4.0": "2015-10-08T02:40:54.913Z", "1.4.1": "2015-10-09T00:30:45.207Z", "1.4.2": "2015-10-09T00:33:45.148Z", "1.4.3": "2015-10-13T17:51:13.407Z", "1.4.4": "2015-10-13T19:17:36.983Z", "1.4.5": "2015-10-14T22:13:23.547Z", "1.4.6": "2015-10-15T07:11:20.555Z", "1.4.7": "2015-10-16T18:06:41.402Z", "1.5.0": "2015-10-27T00:36:08.334Z", "1.5.1": "2015-10-27T18:24:07.848Z", "1.6.0": "2015-11-02T22:36:59.073Z", "1.6.1": "2015-11-07T00:36:03.917Z", "1.6.2": "2015-11-10T02:09:45.830Z", "1.7.0": "2015-11-18T23:31:15.289Z", "1.7.1": "2015-12-01T21:25:46.219Z", "1.7.2": "2016-01-06T00:11:05.547Z", "1.7.3": "2016-01-12T02:25:34.752Z", "1.7.4": "2016-02-12T00:14:26.422Z", "1.8.0": "2016-04-22T02:33:12.835Z", "1.8.1": "2016-04-22T02:39:15.897Z", "1.8.2": "2016-04-25T03:56:59.221Z", "1.8.3": "2016-04-29T22:23:28.468Z", "1.9.0": "2016-07-27T18:31:34.391Z", "1.9.1": "2016-07-28T18:08:38.524Z", "1.9.2": "2016-07-30T01:21:00.428Z", "1.10.0": "2016-08-29T19:39:27.982Z", "1.10.1": "2016-09-28T20:42:02.606Z", "1.10.2": "2017-01-14T01:12:25.046Z", "1.11.0": "2017-03-02T02:22:14.354Z", "1.13.0": "2017-04-13T01:18:58.333Z", "1.13.1": "2017-06-08T02:15:44.489Z", "1.13.2": "2018-03-13T00:01:25.328Z", "1.14.0": "2018-03-13T00:10:33.285Z", "1.14.1": "2018-03-13T00:12:08.258Z", "1.14.2": "2018-06-05T18:29:04.326Z", "1.15.0": "2018-10-11T23:59:56.621Z", "1.15.1": "2018-10-12T00:52:05.061Z", "1.15.2": "2018-10-30T03:00:35.414Z", "1.16.0": "2018-12-21T01:53:44.799Z", "1.16.1": "2019-01-23T21:34:23.894Z", "1.17.0": "2022-01-07T20:37:37.982Z", "1.18.0": "2023-10-19T17:23:11.379Z"}, "bugs": {"url": "https://github.com/arekinath/node-sshpk/issues"}, "author": {"name": "Joyent, Inc"}, "license": "MIT", "homepage": "https://github.com/arekinath/node-sshpk#readme", "repository": {"url": "git+https://github.com/joyent/node-sshpk.git", "type": "git"}, "description": "A library for finding and using SSH public keys", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "maintainers": [{"email": "<EMAIL>", "name": "todd.whiteman"}, {"email": "<EMAIL>", "name": "kusor"}, {"email": "<EMAIL>", "name": "micha<PERSON>.hicks"}, {"email": "<EMAIL>", "name": "bah<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "kebes<PERSON>"}, {"email": "<EMAIL>", "name": "dap"}, {"email": "<EMAIL>", "name": "j<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "melloc"}], "readme": "sshpk\n=========\n\nParse, convert, fingerprint and use SSH keys (both public and private) in pure\nnode -- no `ssh-keygen` or other external dependencies.\n\nSupports RSA, DSA, ECDSA (nistp-\\*) and ED25519 key types, in PEM (PKCS#1, \nPKCS#8) and OpenSSH formats.\n\nThis library has been extracted from\n[`node-http-signature`](https://github.com/joyent/node-http-signature)\n(work by [<PERSON>](https://github.com/mcavage) and\n[<PERSON>](https://github.com/bahamas10)) and\n[`node-ssh-fingerprint`](https://github.com/bahamas10/node-ssh-fingerprint)\n(work by <PERSON>), with additions (including ECDSA support) by\n[<PERSON>](https://github.com/arekinath).\n\nInstall\n-------\n\n```\nnpm install sshpk\n```\n\nExamples\n--------\n\n```js\nvar sshpk = require('sshpk');\n\nvar fs = require('fs');\n\n/* Read in an OpenSSH-format public key */\nvar keyPub = fs.readFileSync('id_rsa.pub');\nvar key = sshpk.parseKey(keyPub, 'ssh');\n\n/* Get metadata about the key */\nconsole.log('type => %s', key.type);\nconsole.log('size => %d bits', key.size);\nconsole.log('comment => %s', key.comment);\n\n/* Compute key fingerprints, in new OpenSSH (>6.7) format, and old MD5 */\nconsole.log('fingerprint => %s', key.fingerprint().toString());\nconsole.log('old-style fingerprint => %s', key.fingerprint('md5').toString());\n```\n\nExample output:\n\n```\ntype => rsa\nsize => 2048 bits\ncomment => <EMAIL>\nfingerprint => SHA256:PYC9kPVC6J873CSIbfp0LwYeczP/W4ffObNCuDJ1u5w\nold-style fingerprint => a0:c8:ad:6c:32:9a:32:fa:59:cc:a9:8c:0a:0d:6e:bd\n```\n\nMore examples: converting between formats:\n\n```js\n/* Read in a PEM public key */\nvar keyPem = fs.readFileSync('id_rsa.pem');\nvar key = sshpk.parseKey(keyPem, 'pem');\n\n/* Convert to PEM PKCS#8 public key format */\nvar pemBuf = key.toBuffer('pkcs8');\n\n/* Convert to SSH public key format (and return as a string) */\nvar sshKey = key.toString('ssh');\n```\n\nSigning and verifying:\n\n```js\n/* Read in an OpenSSH/PEM *private* key */\nvar keyPriv = fs.readFileSync('id_ecdsa');\nvar key = sshpk.parsePrivateKey(keyPriv, 'pem');\n\nvar data = 'some data';\n\n/* Sign some data with the key */\nvar s = key.createSign('sha1');\ns.update(data);\nvar signature = s.sign();\n\n/* Now load the public key (could also use just key.toPublic()) */\nvar keyPub = fs.readFileSync('id_ecdsa.pub');\nkey = sshpk.parseKey(keyPub, 'ssh');\n\n/* Make a crypto.Verifier with this key */\nvar v = key.createVerify('sha1');\nv.update(data);\nvar valid = v.verify(signature);\n/* => true! */\n```\n\nMatching fingerprints with keys:\n\n```js\nvar fp = sshpk.parseFingerprint('SHA256:PYC9kPVC6J873CSIbfp0LwYeczP/W4ffObNCuDJ1u5w');\n\nvar keys = [sshpk.parseKey(...), sshpk.parseKey(...), ...];\n\nkeys.forEach(function (key) {\n\tif (fp.matches(key))\n\t\tconsole.log('found it!');\n});\n```\n\nUsage\n-----\n\n## Public keys\n\n### `parseKey(data[, format = 'auto'[, options]])`\n\nParses a key from a given data format and returns a new `Key` object.\n\nParameters\n\n- `data` -- Either a Buffer or String, containing the key\n- `format` -- String name of format to use, valid options are:\n  - `auto`: choose automatically from all below\n  - `pem`: supports both PKCS#1 and PKCS#8\n  - `ssh`: standard OpenSSH format,\n  - `pkcs1`, `pkcs8`: variants of `pem`\n  - `rfc4253`: raw OpenSSH wire format\n  - `openssh`: new post-OpenSSH 6.5 internal format, produced by \n               `ssh-keygen -o`\n  - `dnssec`: `.key` file format output by `dnssec-keygen` etc\n  - `putty`: the PuTTY `.ppk` file format (supports truncated variant without\n             all the lines from `Private-Lines:` onwards)\n- `options` -- Optional Object, extra options, with keys:\n  - `filename` -- Optional String, name for the key being parsed \n                  (eg. the filename that was opened). Used to generate\n                  Error messages\n  - `passphrase` -- Optional String, encryption passphrase used to decrypt an\n                    encrypted PEM file\n\n### `Key.isKey(obj)`\n\nReturns `true` if the given object is a valid `Key` object created by a version\nof `sshpk` compatible with this one.\n\nParameters\n\n- `obj` -- Object to identify\n\n### `Key#type`\n\nString, the type of key. Valid options are `rsa`, `dsa`, `ecdsa`.\n\n### `Key#size`\n\nInteger, \"size\" of the key in bits. For RSA/DSA this is the size of the modulus;\nfor ECDSA this is the bit size of the curve in use.\n\n### `Key#comment`\n\nOptional string, a key comment used by some formats (eg the `ssh` format).\n\n### `Key#curve`\n\nOnly present if `this.type === 'ecdsa'`, string containing the name of the\nnamed curve used with this key. Possible values include `nistp256`, `nistp384`\nand `nistp521`.\n\n### `Key#toBuffer([format = 'ssh'])`\n\nConvert the key into a given data format and return the serialized key as\na Buffer.\n\nParameters\n\n- `format` -- String name of format to use, for valid options see `parseKey()`\n\n### `Key#toString([format = 'ssh])`\n\nSame as `this.toBuffer(format).toString()`.\n\n### `Key#fingerprint([algorithm = 'sha256'[, hashType = 'ssh']])`\n\nCreates a new `Fingerprint` object representing this Key's fingerprint.\n\nParameters\n\n- `algorithm` -- String name of hash algorithm to use, valid options are `md5`,\n                 `sha1`, `sha256`, `sha384`, `sha512`\n- `hashType` -- String name of fingerprint hash type to use, valid options are\n                `ssh` (the type of fingerprint used by OpenSSH, e.g. in\n                `ssh-keygen`), `spki` (used by HPKP, some OpenSSL applications)\n\n### `Key#createVerify([hashAlgorithm])`\n\nCreates a `crypto.Verifier` specialized to use this Key (and the correct public\nkey algorithm to match it). The returned Verifier has the same API as a regular\none, except that the `verify()` function takes only the target signature as an\nargument.\n\nParameters\n\n- `hashAlgorithm` -- optional String name of hash algorithm to use, any\n                     supported by OpenSSL are valid, usually including\n                     `sha1`, `sha256`.\n\n`v.verify(signature[, format])` Parameters\n\n- `signature` -- either a Signature object, or a Buffer or String\n- `format` -- optional String, name of format to interpret given String with.\n              Not valid if `signature` is a Signature or Buffer.\n\n### `Key#createDiffieHellman()`\n### `Key#createDH()`\n\nCreates a Diffie-Hellman key exchange object initialized with this key and all\nnecessary parameters. This has the same API as a `crypto.DiffieHellman`\ninstance, except that functions take `Key` and `PrivateKey` objects as\narguments, and return them where indicated for.\n\nThis is only valid for keys belonging to a cryptosystem that supports DHE\nor a close analogue (i.e. `dsa`, `ecdsa` and `curve25519` keys). An attempt\nto call this function on other keys will yield an `Error`.\n\n## Private keys\n\n### `parsePrivateKey(data[, format = 'auto'[, options]])`\n\nParses a private key from a given data format and returns a new\n`PrivateKey` object.\n\nParameters\n\n- `data` -- Either a Buffer or String, containing the key\n- `format` -- String name of format to use, valid options are:\n  - `auto`: choose automatically from all below\n  - `pem`: supports both PKCS#1 and PKCS#8\n  - `ssh`, `openssh`: new post-OpenSSH 6.5 internal format, produced by\n                      `ssh-keygen -o`\n  - `pkcs1`, `pkcs8`: variants of `pem`\n  - `rfc4253`: raw OpenSSH wire format\n  - `dnssec`: `.private` format output by `dnssec-keygen` etc.\n- `options` -- Optional Object, extra options, with keys:\n  - `filename` -- Optional String, name for the key being parsed\n                  (eg. the filename that was opened). Used to generate\n                  Error messages\n  - `passphrase` -- Optional String, encryption passphrase used to decrypt an\n                    encrypted PEM file\n\n### `generatePrivateKey(type[, options])`\n\nGenerates a new private key of a certain key type, from random data.\n\nParameters\n\n- `type` -- String, type of key to generate. Currently supported are `'ecdsa'`\n            and `'ed25519'`\n- `options` -- optional Object, with keys:\n  - `curve` -- optional String, for `'ecdsa'` keys, specifies the curve to use.\n               If ECDSA is specified and this option is not given, defaults to\n               using `'nistp256'`.\n\n### `PrivateKey.isPrivateKey(obj)`\n\nReturns `true` if the given object is a valid `PrivateKey` object created by a\nversion of `sshpk` compatible with this one.\n\nParameters\n\n- `obj` -- Object to identify\n\n### `PrivateKey#type`\n\nString, the type of key. Valid options are `rsa`, `dsa`, `ecdsa`.\n\n### `PrivateKey#size`\n\nInteger, \"size\" of the key in bits. For RSA/DSA this is the size of the modulus;\nfor ECDSA this is the bit size of the curve in use.\n\n### `PrivateKey#curve`\n\nOnly present if `this.type === 'ecdsa'`, string containing the name of the\nnamed curve used with this key. Possible values include `nistp256`, `nistp384`\nand `nistp521`.\n\n### `PrivateKey#toBuffer([format = 'pkcs1'])`\n\nConvert the key into a given data format and return the serialized key as\na Buffer.\n\nParameters\n\n- `format` -- String name of format to use, valid options are listed under \n              `parsePrivateKey`. Note that ED25519 keys default to `openssh`\n              format instead (as they have no `pkcs1` representation).\n\n### `PrivateKey#toString([format = 'pkcs1'])`\n\nSame as `this.toBuffer(format).toString()`.\n\n### `PrivateKey#toPublic()`\n\nExtract just the public part of this private key, and return it as a `Key`\nobject.\n\n### `PrivateKey#fingerprint([algorithm = 'sha256'])`\n\nSame as `this.toPublic().fingerprint()`.\n\n### `PrivateKey#createVerify([hashAlgorithm])`\n\nSame as `this.toPublic().createVerify()`.\n\n### `PrivateKey#createSign([hashAlgorithm])`\n\nCreates a `crypto.Sign` specialized to use this PrivateKey (and the correct\nkey algorithm to match it). The returned Signer has the same API as a regular\none, except that the `sign()` function takes no arguments, and returns a\n`Signature` object.\n\nParameters\n\n- `hashAlgorithm` -- optional String name of hash algorithm to use, any\n                     supported by OpenSSL are valid, usually including\n                     `sha1`, `sha256`.\n\n`v.sign()` Parameters\n\n- none\n\n### `PrivateKey#derive(newType)`\n\nDerives a related key of type `newType` from this key. Currently this is\nonly supported to change between `ed25519` and `curve25519` keys which are\nstored with the same private key (but usually distinct public keys in order\nto avoid degenerate keys that lead to a weak Diffie-Hellman exchange).\n\nParameters\n\n- `newType` -- String, type of key to derive, either `ed25519` or `curve25519`\n\n## Fingerprints\n\n### `parseFingerprint(fingerprint[, options])`\n\nPre-parses a fingerprint, creating a `Fingerprint` object that can be used to\nquickly locate a key by using the `Fingerprint#matches` function.\n\nParameters\n\n- `fingerprint` -- String, the fingerprint value, in any supported format\n- `options` -- Optional Object, with properties:\n  - `algorithms` -- Array of strings, names of hash algorithms to limit\n                support to. If `fingerprint` uses a hash algorithm not on\n                this list, throws `InvalidAlgorithmError`.\n  - `hashType` -- String, the type of hash the fingerprint uses, either `ssh`\n                  or `spki` (normally auto-detected based on the format, but\n                  can be overridden)\n  - `type` -- String, the entity this fingerprint identifies, either `key` or\n              `certificate`\n\n### `Fingerprint.isFingerprint(obj)`\n\nReturns `true` if the given object is a valid `Fingerprint` object created by a\nversion of `sshpk` compatible with this one.\n\nParameters\n\n- `obj` -- Object to identify\n\n### `Fingerprint#toString([format])`\n\nReturns a fingerprint as a string, in the given format.\n\nParameters\n\n- `format` -- Optional String, format to use, valid options are `hex` and\n              `base64`. If this `Fingerprint` uses the `md5` algorithm, the\n              default format is `hex`. Otherwise, the default is `base64`.\n\n### `Fingerprint#matches(keyOrCertificate)`\n\nVerifies whether or not this `Fingerprint` matches a given `Key` or\n`Certificate`. This function uses double-hashing to avoid leaking timing\ninformation. Returns a boolean.\n\nNote that a `Key`-type Fingerprint will always return `false` if asked to match\na `Certificate` and vice versa.\n\nParameters\n\n- `keyOrCertificate` -- a `Key` object or `Certificate` object, the entity to\n                        match this fingerprint against\n\n## Signatures\n\n### `parseSignature(signature, algorithm, format)`\n\nParses a signature in a given format, creating a `Signature` object. Useful\nfor converting between the SSH and ASN.1 (PKCS/OpenSSL) signature formats, and\nalso returned as output from `PrivateKey#createSign().sign()`.\n\nA Signature object can also be passed to a verifier produced by\n`Key#createVerify()` and it will automatically be converted internally into the\ncorrect format for verification.\n\nParameters\n\n- `signature` -- a Buffer (binary) or String (base64), data of the actual\n                 signature in the given format\n- `algorithm` -- a String, name of the algorithm to be used, possible values\n                 are `rsa`, `dsa`, `ecdsa`\n- `format` -- a String, either `asn1` or `ssh`\n\n### `Signature.isSignature(obj)`\n\nReturns `true` if the given object is a valid `Signature` object created by a\nversion of `sshpk` compatible with this one.\n\nParameters\n\n- `obj` -- Object to identify\n\n### `Signature#toBuffer([format = 'asn1'])`\n\nConverts a Signature to the given format and returns it as a Buffer.\n\nParameters\n\n- `format` -- a String, either `asn1` or `ssh`\n\n### `Signature#toString([format = 'asn1'])`\n\nSame as `this.toBuffer(format).toString('base64')`.\n\n## Certificates\n\n`sshpk` includes basic support for parsing certificates in X.509 (PEM) format\nand the OpenSSH certificate format. This feature is intended to be used mainly\nto access basic metadata about certificates, extract public keys from them, and\nalso to generate simple self-signed certificates from an existing key.\n\nNotably, there is no implementation of CA chain-of-trust verification, and only\nvery minimal support for key usage restrictions. Please do the security world\na favour, and DO NOT use this code for certificate verification in the\ntraditional X.509 CA chain style.\n\n### `parseCertificate(data, format)`\n\nParameters\n\n - `data` -- a Buffer or String\n - `format` -- a String, format to use, one of `'openssh'`, `'pem'` (X.509 in a\n               PEM wrapper), or `'x509'` (raw DER encoded)\n\n### `createSelfSignedCertificate(subject, privateKey[, options])`\n\nParameters\n\n - `subject` -- an Identity, the subject of the certificate\n - `privateKey` -- a PrivateKey, the key of the subject: will be used both to be\n                   placed in the certificate and also to sign it (since this is\n                   a self-signed certificate)\n - `options` -- optional Object, with keys:\n   - `lifetime` -- optional Number, lifetime of the certificate from now in\n                   seconds\n   - `validFrom`, `validUntil` -- optional Dates, beginning and end of\n                                  certificate validity period. If given\n                                  `lifetime` will be ignored\n   - `serial` -- optional Buffer, the serial number of the certificate\n   - `purposes` -- optional Array of String, X.509 key usage restrictions\n\n### `createCertificate(subject, key, issuer, issuerKey[, options])`\n\nParameters\n\n - `subject` -- an Identity, the subject of the certificate\n - `key` -- a Key, the public key of the subject\n - `issuer` -- an Identity, the issuer of the certificate who will sign it\n - `issuerKey` -- a PrivateKey, the issuer's private key for signing\n - `options` -- optional Object, with keys:\n   - `lifetime` -- optional Number, lifetime of the certificate from now in\n                   seconds\n   - `validFrom`, `validUntil` -- optional Dates, beginning and end of\n                                  certificate validity period. If given\n                                  `lifetime` will be ignored\n   - `serial` -- optional Buffer, the serial number of the certificate\n   - `purposes` -- optional Array of String, X.509 key usage restrictions\n\n### `Certificate#subjects`\n\nArray of `Identity` instances describing the subject of this certificate.\n\n### `Certificate#issuer`\n\nThe `Identity` of the Certificate's issuer (signer).\n\n### `Certificate#subjectKey`\n\nThe public key of the subject of the certificate, as a `Key` instance.\n\n### `Certificate#issuerKey`\n\nThe public key of the signing issuer of this certificate, as a `Key` instance.\nMay be `undefined` if the issuer's key is unknown (e.g. on an X509 certificate).\n\n### `Certificate#serial`\n\nThe serial number of the certificate. As this is normally a 64-bit or wider\ninteger, it is returned as a Buffer.\n\n### `Certificate#purposes`\n\nArray of Strings indicating the X.509 key usage purposes that this certificate\nis valid for. The possible strings at the moment are:\n\n * `'signature'` -- key can be used for digital signatures\n * `'identity'` -- key can be used to attest about the identity of the signer\n                   (X.509 calls this `nonRepudiation`)\n * `'codeSigning'` -- key can be used to sign executable code\n * `'keyEncryption'` -- key can be used to encrypt other keys\n * `'encryption'` -- key can be used to encrypt data (only applies for RSA)\n * `'keyAgreement'` -- key can be used for key exchange protocols such as\n                       Diffie-Hellman\n * `'ca'` -- key can be used to sign other certificates (is a Certificate\n             Authority)\n * `'crl'` -- key can be used to sign Certificate Revocation Lists (CRLs)\n\n### `Certificate#getExtension(nameOrOid)`\n\nRetrieves information about a certificate extension, if present, or returns\n`undefined` if not. The string argument `nameOrOid` should be either the OID\n(for X509 extensions) or the name (for OpenSSH extensions) of the extension\nto retrieve.\n\nThe object returned will have the following properties:\n\n * `format` -- String, set to either `'x509'` or `'openssh'`\n * `name` or `oid` -- String, only one set based on value of `format`\n * `data` -- Buffer, the raw data inside the extension\n\n### `Certificate#getExtensions()`\n\nReturns an Array of all present certificate extensions, in the same manner and\nformat as `getExtension()`.\n\n### `Certificate#isExpired([when])`\n\nTests whether the Certificate is currently expired (i.e. the `validFrom` and\n`validUntil` dates specify a range of time that does not include the current\ntime).\n\nParameters\n\n - `when` -- optional Date, if specified, tests whether the Certificate was or\n             will be expired at the specified time instead of now\n\nReturns a Boolean.\n\n### `Certificate#isSignedByKey(key)`\n\nTests whether the Certificate was validly signed by the given (public) Key.\n\nParameters\n\n - `key` -- a Key instance\n\nReturns a Boolean.\n\n### `Certificate#isSignedBy(certificate)`\n\nTests whether this Certificate was validly signed by the subject of the given\ncertificate. Also tests that the issuer Identity of this Certificate and the\nsubject Identity of the other Certificate are equivalent.\n\nParameters\n\n - `certificate` -- another Certificate instance\n\nReturns a Boolean.\n\n### `Certificate#fingerprint([hashAlgo])`\n\nReturns the X509-style fingerprint of the entire certificate (as a Fingerprint\ninstance). This matches what a web-browser or similar would display as the\ncertificate fingerprint and should not be confused with the fingerprint of the\nsubject's public key.\n\nParameters\n\n - `hashAlgo` -- an optional String, any hash function name\n\n### `Certificate#toBuffer([format])`\n\nSerializes the Certificate to a Buffer and returns it.\n\nParameters\n\n - `format` -- an optional String, output format, one of `'openssh'`, `'pem'` or\n               `'x509'`. Defaults to `'x509'`.\n\nReturns a Buffer.\n\n### `Certificate#toString([format])`\n\n - `format` -- an optional String, output format, one of `'openssh'`, `'pem'` or\n               `'x509'`. Defaults to `'pem'`.\n\nReturns a String.\n\n## Certificate identities\n\n### `identityForHost(hostname)`\n\nConstructs a host-type Identity for a given hostname.\n\nParameters\n\n - `hostname` -- the fully qualified DNS name of the host\n\nReturns an Identity instance.\n\n### `identityForUser(uid)`\n\nConstructs a user-type Identity for a given UID.\n\nParameters\n\n - `uid` -- a String, user identifier (login name)\n\nReturns an Identity instance.\n\n### `identityForEmail(email)`\n\nConstructs an email-type Identity for a given email address.\n\nParameters\n\n - `email` -- a String, email address\n\nReturns an Identity instance.\n\n### `identityFromDN(dn)`\n\nParses an LDAP-style DN string (e.g. `'CN=foo, C=US'`) and turns it into an\nIdentity instance.\n\nParameters\n\n - `dn` -- a String\n\nReturns an Identity instance.\n\n### `identityFromArray(arr)`\n\nConstructs an Identity from an array of DN components (see `Identity#toArray()`\nfor the format).\n\nParameters\n\n - `arr` -- an Array of Objects, DN components with `name` and `value`\n\nReturns an Identity instance.\n\n\nSupported attributes in DNs:\n\n| Attribute name | OID |\n| -------------- | --- |\n| `cn` | `*******` |\n| `o` | `********` |\n| `ou` | `********` |\n| `l` | `*******` |\n| `s` | `*******` |\n| `c` | `*******` |\n| `sn` | `*******` |\n| `postalCode` | `********` |\n| `serialNumber` | `*******` |\n| `street` | `*******` |\n| `x500UniqueIdentifier` | `********` |\n| `role` | `********` |\n| `telephoneNumber` | `********` |\n| `description` | `********` |\n| `dc` | `0.9.2342.19200300.100.1.25` |\n| `uid` | `0.9.2342.19200300.100.1.1` |\n| `mail` | `0.9.2342.19200300.100.1.3` |\n| `title` | `********` |\n| `gn` | `********` |\n| `initials` | `********` |\n| `pseudonym` | `********` |\n\n### `Identity#toString()`\n\nReturns the identity as an LDAP-style DN string.\ne.g. `'CN=foo, O=bar corp, C=us'`\n\n### `Identity#type`\n\nThe type of identity. One of `'host'`, `'user'`, `'email'` or `'unknown'`\n\n### `Identity#hostname`\n### `Identity#uid`\n### `Identity#email`\n\nSet when `type` is `'host'`, `'user'`, or `'email'`, respectively. Strings.\n\n### `Identity#cn`\n\nThe value of the first `CN=` in the DN, if any. It's probably better to use\nthe `#get()` method instead of this property.\n\n### `Identity#get(name[, asArray])`\n\nReturns the value of a named attribute in the Identity DN. If there is no\nattribute of the given name, returns `undefined`. If multiple components\nof the DN contain an attribute of this name, an exception is thrown unless\nthe `asArray` argument is given as `true` -- then they will be returned as\nan Array in the same order they appear in the DN.\n\nParameters\n\n - `name` -- a String\n - `asArray` -- an optional Boolean\n\n### `Identity#toArray()`\n\nReturns the Identity as an Array of DN component objects. This looks like:\n\n```js\n[ {\n  \"name\": \"cn\",\n  \"value\": \"Joe Bloggs\"\n},\n{\n  \"name\": \"o\",\n  \"value\": \"Organisation Ltd\"\n} ]\n```\n\nEach object has a `name` and a `value` property. The returned objects may be\nsafely modified.\n\nErrors\n------\n\n### `InvalidAlgorithmError`\n\nThe specified algorithm is not valid, either because it is not supported, or\nbecause it was not included on a list of allowed algorithms.\n\nThrown by `Fingerprint.parse`, `Key#fingerprint`.\n\nProperties\n\n- `algorithm` -- the algorithm that could not be validated\n\n### `FingerprintFormatError`\n\nThe fingerprint string given could not be parsed as a supported fingerprint\nformat, or the specified fingerprint format is invalid.\n\nThrown by `Fingerprint.parse`, `Fingerprint#toString`.\n\nProperties\n\n- `fingerprint` -- if caused by a fingerprint, the string value given\n- `format` -- if caused by an invalid format specification, the string value given\n\n### `KeyParseError`\n\nThe key data given could not be parsed as a valid key.\n\nProperties\n\n- `keyName` -- `filename` that was given to `parseKey`\n- `format` -- the `format` that was trying to parse the key (see `parseKey`)\n- `innerErr` -- the inner Error thrown by the format parser\n\n### `KeyEncryptedError`\n\nThe key is encrypted with a symmetric key (ie, it is password protected). The\nparsing operation would succeed if it was given the `passphrase` option.\n\nProperties\n\n- `keyName` -- `filename` that was given to `parseKey`\n- `format` -- the `format` that was trying to parse the key (currently can only\n              be `\"pem\"`)\n\n### `CertificateParseError`\n\nThe certificate data given could not be parsed as a valid certificate.\n\nProperties\n\n- `certName` -- `filename` that was given to `parseCertificate`\n- `format` -- the `format` that was trying to parse the key\n              (see `parseCertificate`)\n- `innerErr` -- the inner Error thrown by the format parser\n\nFriends of sshpk\n----------------\n\n * [`sshpk-agent`](https://github.com/arekinath/node-sshpk-agent) is a library\n   for speaking the `ssh-agent` protocol from node.js, which uses `sshpk`\n", "readmeFilename": "README.md", "users": {"meeh": true, "mojaray2k": true, "oliversalzburg": true, "shanewholloway": true}}