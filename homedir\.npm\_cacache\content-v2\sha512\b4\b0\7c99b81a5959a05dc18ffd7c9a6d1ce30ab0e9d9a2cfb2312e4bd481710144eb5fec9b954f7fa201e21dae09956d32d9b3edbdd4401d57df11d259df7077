{"_id": "precond", "_rev": "18-c7be5697fd5e4761e5f8b0634cd43db3", "name": "precond", "description": "Precondition checking utilities.", "dist-tags": {"latest": "0.2.3"}, "versions": {"0.1.0": {"name": "precond", "description": "Preconditions checking utilities.", "version": "0.1.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["preconditions", "asserts", "invariants"], "repository": {"type": "git", "url": "https://github.com/MathieuTurcotte/node-precond.git"}, "devDependencies": {"nodeunit": "0.7", "jshint": "0.9.0"}, "scripts": {"pretest": "node_modules/jshint/bin/hint lib/ tests/ examples/ index.js", "test": "node_modules/nodeunit/bin/nodeunit tests/"}, "engines": {"node": ">= 0.6"}, "_id": "precond@0.1.0", "dist": {"shasum": "6c5fd283b3c6f46d342261686378f1fdac04cd3c", "tarball": "https://registry.npmjs.org/precond/-/precond-0.1.0.tgz", "integrity": "sha512-V/774qucNU5+sKynimg5HHZJPJxbFxB18KT0lAbjHv8ho7aIRy0p6e0J7/vYQXC/YKPS7p5gaZY6XSV/Zy81/A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID3dVN2+/3yfbAnYUt23BA53QFF52PkfTjG0/timaF6VAiBF3PxBdYzCS7mfCtEdybYXkDhaxLG9ceUFBRHe5zXrLQ=="}]}, "_npmVersion": "1.1.62", "_npmUser": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}]}, "0.1.1": {"name": "precond", "description": "Preconditions checking utilities.", "version": "0.1.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["preconditions", "asserts", "invariants"], "repository": {"type": "git", "url": "https://github.com/MathieuTurcotte/node-precond.git"}, "devDependencies": {"nodeunit": "0.7", "jshint": "0.9.0"}, "scripts": {"pretest": "node_modules/jshint/bin/hint lib/ tests/ examples/ index.js", "test": "node_modules/nodeunit/bin/nodeunit tests/"}, "engines": {"node": ">= 0.6"}, "_id": "precond@0.1.1", "dist": {"shasum": "6d44c7f8d3dc76bb43824bef494177771718b8e7", "tarball": "https://registry.npmjs.org/precond/-/precond-0.1.1.tgz", "integrity": "sha512-za7f7xwA/GfolF3DIsY+y9hG8lQIrsUlWPzUG3SlqpoZ0uvQTnz+XIXEQOfItskQOCfUdCsSBApeaFSThe+lXw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDeKrhZx7hssiCXpmWk0hZlvZtuIXW6h0hzZ3Jq0ekqRwIgHSSCi7TuWKPDMs+loqrAPGV6WB/2NrsQa/wNPkVes64="}]}, "_npmVersion": "1.1.62", "_npmUser": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}]}, "0.2.0": {"name": "precond", "description": "Precondition checking utilities.", "version": "0.2.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["precondition", "assert", "invariant", "contract", "condition"], "repository": {"type": "git", "url": "https://github.com/MathieuTurcotte/node-precond.git"}, "devDependencies": {"nodeunit": "0.8", "jshint": "2.1"}, "scripts": {"pretest": "node_modules/.bin/jshint lib/ tests/ examples/ index.js", "test": "node_modules/.bin/nodeunit tests/"}, "engines": {"node": ">= 0.6"}, "_id": "precond@0.2.0", "dist": {"shasum": "9c72fb8655611c87bd122f4cad60f43829b3e2b7", "tarball": "https://registry.npmjs.org/precond/-/precond-0.2.0.tgz", "integrity": "sha512-tiCbpEbrCiHvQdOBXXPzuBA0F7M0Ale617itClwDAu8oSAmSTKVw+fQaWxN77m/phFKzyIEhY9SAXOTxj8Z33w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCA0PZtimYZFbrr656O0XsO+4gVyAbf8ktyoyHxj3jc3wIgI/gtvFGqRWHGuWB8OlS+IlilAJbJd+GLpfBvnb7RyyY="}]}, "_from": ".", "_npmVersion": "1.2.14", "_npmUser": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}]}, "0.2.1": {"name": "precond", "description": "Precondition checking utilities.", "version": "0.2.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["precondition", "assert", "invariant", "contract", "condition"], "repository": {"type": "git", "url": "https://github.com/MathieuTurcotte/node-precond.git"}, "devDependencies": {"nodeunit": "0.8", "jshint": "2.1"}, "scripts": {"pretest": "node_modules/.bin/jshint lib/ examples/ index.js", "test": "node_modules/.bin/nodeunit tests/"}, "engines": {"node": ">= 0.6"}, "_id": "precond@0.2.1", "dist": {"shasum": "214895838fb1a35d0a17f8523e758b43bb57ba55", "tarball": "https://registry.npmjs.org/precond/-/precond-0.2.1.tgz", "integrity": "sha512-YKOVTpoL6d6P4yiPrbtY6LfqxLgx0NOYLc5n6AwDYK8/diF6Xm0Z/QM8wVPaJSt5ngJ7CPwSd3k3pUGX8j05sg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHC+U2RGucciQhJLnj27tUN3QK1Nu7p00Fkn2tdqsWpiAiBUHiwEUNxfMtOn28UELHa+EP71S/jBZszplIzLxeMqWw=="}]}, "_from": ".", "_npmVersion": "1.2.14", "_npmUser": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}]}, "0.2.2": {"name": "precond", "description": "Precondition checking utilities.", "version": "0.2.2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["precondition", "assert", "invariant", "contract", "condition"], "repository": {"type": "git", "url": "https://github.com/MathieuTurcotte/node-precond.git"}, "devDependencies": {"nodeunit": "0.8", "jshint": "2.1"}, "scripts": {"pretest": "node_modules/.bin/jshint lib/ examples/ index.js", "test": "node_modules/.bin/nodeunit tests/"}, "engines": {"node": ">= 0.6"}, "_id": "precond@0.2.2", "dist": {"shasum": "c6ce717f909e7dd0a08d515f9fb428041d6f992e", "tarball": "https://registry.npmjs.org/precond/-/precond-0.2.2.tgz", "integrity": "sha512-iro7YBwNz8qTmBDL9ZEK9qjSCIrCzm2LLOxS6OSjOIMDEZggflXBcPw+N5Qbn/G9EjNhaS6ggny66ZL6WoZ/kg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCCB7BNE0tb0jWY+ZLqq1EdEcesZkcurv1mY8nX96Zs2AIgPFiQPZRkj6KG+hKIhDICj9ckpKWqu3BmMSx17cqn4vo="}]}, "_from": ".", "_npmVersion": "1.2.14", "_npmUser": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}]}, "0.2.3": {"name": "precond", "description": "Precondition checking utilities.", "version": "0.2.3", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["precondition", "assert", "invariant", "contract", "condition"], "repository": {"type": "git", "url": "https://github.com/MathieuTurcotte/node-precond.git"}, "devDependencies": {"nodeunit": "0.9", "jshint": "2.5"}, "scripts": {"pretest": "node_modules/.bin/jshint lib/ examples/ index.js", "test": "node_modules/.bin/nodeunit tests/"}, "engines": {"node": ">= 0.6"}, "files": ["index.js", "lib"], "bugs": {"url": "https://github.com/MathieuTurcotte/node-precond/issues"}, "_id": "precond@0.2.3", "dist": {"shasum": "aa9591bcaa24923f1e0f4849d240f47efc1075ac", "tarball": "https://registry.npmjs.org/precond/-/precond-0.2.3.tgz", "integrity": "sha512-QCYG84SgGyGzqJ/vlMsxeXd/pgL/I94ixdNFyh1PusWmTCyVfPJjZ1K1jvHtsbfnXQs2TSkEP2fR7QiMZAnKFQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCJFXShHfD8ZovOxpJggH50sXrXGg022CRxUw+5TFpBLgIgETTK4LM+vgKj6X40nLKAhxplzcbzeZVefZaqDcIV7RY="}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}]}}, "readme": "# Preconditions for Node.js\n[![Build Status](https://secure.travis-ci.org/MathieuTurcotte/node-precond.png?branch=master)](https://travis-ci.org/MathieuTurcotte/node-precond)\n[![NPM version](https://badge.fury.io/js/precond.png)](http://badge.fury.io/js/precond)\n\nPrecondition checks for Node.js inspired by [Guava's precondition checking\nutilities](https://code.google.com/p/guava-libraries/wiki/PreconditionsExplained).\n\n## Installation\n\n```\nnpm install precond\n```\n\n## Unit tests\n\n```\nnpm test\n```\n\n## Overview\n\nPrecond provides a set of functions to verify arguments and state correctness\n\nIt lets you rewrite constructs like the following\n\n```js\nif (!this.isConnected) {\n    throw new Error('Client should be connected before calling X.');\n}\n```\n\ninto a more compact and declarative check bellow.\n\n```js\nprecond.checkState(this.isConnected, 'Client should be ...');\n```\n\n**Note that even though the throw statement is wrapped in a function, the call\nstack will still start from the calling function. So the previous examples would\nboth produce the same stack trace.**\n\nAll arguments after the message will be used to format the actual error\nmessage that will be thrown.\n\nThe following precondition checks are provded:\n\n- checkArgument(value, [messageFormat, [formatArgs, ...]])\n- checkState(value, [messageFormat, [formatArgs, ...]])\n- checkIsDef(value, [messageFormat, [formatArgs, ...]]) -> value\n- checkIsDefAndNotNull(value, [messageFormat, [formatArgs, ...]]) -> value\n- checkIsString(value, [messageFormat, [formatArgs, ...]]) -> value\n- checkIsArray(value, [messageFormat, [formatArgs, ...]]) -> value\n- checkIsNumber(value, [messageFormat, [formatArgs, ...]]) -> value\n- checkIsBoolean(value, [messageFormat, [formatArgs, ...]]) -> value\n- checkIsFunction(value, [messageFormat, [formatArgs, ...]]) -> value\n- checkIsObject(value, [messageFormat, [formatArgs, ...]]) -> value\n\n## API\n\n### Static functions\n\n#### precond.checkArgument(value, [messageFormat, [formatArgs, ...]])\n\n- value: the value that is required to be truthy\n- messageFormat: error message format template\n- formatArgs: arguments to be substituted into the message template\n\nEnsures that value is true. Throws an `IllegalArgumentError` if value\nis false.\n\n#### precond.checkState(value, [messageFormat, [formatArgs, ...]])\n\n- value: the value that is required to be truthy\n- messageFormat: error message format template\n- formatArgs: arguments to be substituted into the message template\n\nEnsures that value is true. Throws an `IllegalStateError` if value\nis false.\n\n#### precond.checkIsDef(value, [messageFormat, [formatArgs, ...]])\n\n- value: the value that is required to be defined\n- messageFormat: error message format template\n- formatArgs: arguments to be substituted into the message template\n\nEnsures that value is defined (could be null). Throws an\n`IllegalArgumentError` if value is undefined. Returns the value of\nthe value that was validated.\n\n#### precond.checkIsDefAndNotNull(value, [messageFormat, [formatArgs, ...]])\n\n- value: the value that is required to be defined and not null\n- messageFormat: error message format template\n- formatArgs: arguments to be substituted into the message template\n\nEnsures that value is defined and not null. Throws an\n`IllegalArgumentError` if value is undefined or null. Returns the value of\nthe value that was validated.\n\n#### precond.checkIsString(value, [messageFormat, [formatArgs, ...]])\n\n- value: the value that is required to be a string\n- messageFormat: error message format template\n- formatArgs: arguments to be substituted into the message template\n\nEnsures that value is a string or a String object. Throws an\n`IllegalArgumentError` if value isn't a string. Returns the value of\nthe value that was validated.\n\n#### precond.checkIsArray(value, [messageFormat, [formatArgs, ...]])\n\n- value: the value that is required to be an array\n- messageFormat: error message format template\n- formatArgs: arguments to be substituted into the message template\n\nEnsures that value is an array. Throws an `IllegalArgumentError` if\nvalue isn't an array. Returns the value of the value that was\nvalidated.\n\n#### precond.checkIsNumber(value, [messageFormat, [formatArgs, ...]])\n\n- value: the value that is required to be a number\n- messageFormat: error message format template\n- formatArgs: arguments to be substituted into the message template\n\nEnsures that value is a number. Throws an `IllegalArgumentError` if\nvalue isn't a number. Returns the value of the value that was\nvalidated.\n\n#### precond.checkIsBoolean(value, [messageFormat, [formatArgs, ...]])\n\n- value: the value that is required to be a boolean\n- messageFormat: error message format template\n- formatArgs: arguments to be substituted into the message template\n\nEnsures that value is a boolean. Throws an `IllegalArgumentError` if\nvalue isn't a boolean. Returns the value of the value that was\nvalidated.\n\n#### precond.checkIsFunction(value, [messageFormat, [formatArgs, ...]])\n\n- value: the value that is required to be a function\n- messageFormat: error message format template\n- formatArgs: arguments to be substituted into the message template\n\nEnsures that value is a function. Throws an `IllegalArgumentError` if\nvalue isn't a function. Returns the value of the value that was\nvalidated.\n\n#### precond.checkIsObject(value, [messageFormat, [formatArgs, ...]])\n\n- value: the value that is required to be an object\n- messageFormat: error message format template\n- formatArgs: arguments to be substituted into the message template\n\nEnsures that value is an object. Throws an `IllegalArgumentError` if\nvalue isn't an object. Returns the value of the value that was\nvalidated.\n\n### Class precond.IllegalArgumentError\n\nExtends `Error` and is thrown to signal illegal arguments.\n\n### Class precond.IllegalStateError\n\nExtends `Error` and is thrown to signal that the program or object has reached\nan illegal state.\n\n## License\n\nThis code is free to use under the terms of the [MIT license](http://mturcotte.mit-license.org/).\n", "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2022-06-24T13:49:40.571Z", "created": "2012-11-15T01:33:14.214Z", "0.1.0": "2012-11-15T01:33:15.122Z", "0.1.1": "2012-11-16T03:46:33.767Z", "0.2.0": "2013-05-26T15:00:02.377Z", "0.2.1": "2013-05-26T16:45:32.480Z", "0.2.2": "2013-06-15T14:53:50.913Z", "0.2.3": "2014-06-21T13:37:39.556Z"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "https://github.com/MathieuTurcotte/node-precond.git"}, "keywords": ["precondition", "assert", "invariant", "contract", "condition"], "readmeFilename": "README.md", "bugs": {"url": "https://github.com/MathieuTurcotte/node-precond/issues"}, "users": {"muroc": true}}