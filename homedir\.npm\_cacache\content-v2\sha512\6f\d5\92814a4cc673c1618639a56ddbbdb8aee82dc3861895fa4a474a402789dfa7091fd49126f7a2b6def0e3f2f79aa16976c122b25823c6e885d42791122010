{"_id": "colorette", "_rev": "40-3dfabdd2229fde6fad998af5c28581c1", "name": "colorette", "dist-tags": {"latest": "2.0.20"}, "versions": {"0.1.0": {"name": "colorette", "version": "0.1.0", "description": "Node.js library for colorizing text using ANSI escape sequences.", "main": "index.js", "types": "colorette.d.ts", "scripts": {"test": "nyc -r lcov testmatrix test/*.js", "release": "npm test && git commit -am $npm_package_version && git tag $npm_package_version && git push origin master && git push --tags && npm publish"}, "repository": {"type": "git", "url": "git+https://github.com/jorgebucaran/colorette.git"}, "files": ["index.js", "colorette.d.ts"], "keywords": ["colorette", "terminal", "styles", "color", "ansi"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jorgebucaran/colorette/issues"}, "homepage": "https://github.com/jorgebucaran/colorette", "devDependencies": {"nyc": "12.0.2", "testmatrix": "0.1.2"}, "gitHead": "214efe5a698a937baa5e559250264cb0e12d3076", "_id": "colorette@0.1.0", "_npmVersion": "6.3.0", "_nodeVersion": "10.0.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-lmGFYjFqC0KYkTr1J4h6b3x5dZ2QUS2NKlBZInFsGUhRPFdVSzQ2JZdN5P0HdhjXGtO+Xbz5IrLpehllIthfAw==", "shasum": "e7cec51fe5ec60f2d287ccb7c827a967708ba02f", "tarball": "https://registry.npmjs.org/colorette/-/colorette-0.1.0.tgz", "fileCount": 5, "unpackedSize": 11408, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfgoPCRA9TVsSAnZWagAA5CsP/1R10An2ObwjSy/aStUI\na15mhdchxiJ385Cq8/ptlBKpywdj2XXGCntj7XlgDdAGLjzor44ur5WuspMA\nkUwhtcyBS7fyRjvLtbvqaHjWqVnE0b2hFHr1r8fw6eiAHBEbXzEwBgJDL0B4\nBGPEDB3kQPx5ISath3g2O6juw/r0lIoqOgdBzAK2CxOq4fCnT459oil4Pvy0\n687Mlm5RMwRAYgDbWWhWZOWhX80kif7eccCP9kFGcK3PxaFX01wemsiYla0c\n3Qh7ZSkCbR+j1c4yCBahCoIeNCyLEID5LYrz3RtWIUcHMsEi9kgm6EF1ZqV7\n1yQkAhCAJFfyHqLck9MJ2GSWJpC6DTLCzibeZLJBJ1ZEEctbratGgvjTUNNp\n7lzzXNbKDSj5CNfWKsTh5yvdMMg41AZEHVmYJfSHfb7OOevykf1fqnx1LNI+\n8gLCpsiDOw+zdX0tqNFnIvHm5WZIRus4LC6exWd6vgocx37I9hgZDa+CtlZm\nL4uhADH/OZGv0eH8FJrrSxJvDJkO37XbjwZcCrdAXxGvFbFWhIjKG1XW3AY4\nHY/ePV4JuM+/bdfzxic6aFu6dIrEy+aOG4Y9sxIBBWY3Tclc7ubnQtWrLsCp\nUvrECmWVFXQYNOFhHyCcWllV7XpBfOs63wXVvEJoSqu9Oepf8aR+iOMPp84b\nbOUf\r\n=J0gz\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDN5PyBe354oAbOQhMu1CXOARxii2bWFBga5UE5EY9YRgIgc4V7ggTnoJinpKfPcl6XC8JUp6KUCzs+htddOORVTsY="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/colorette_0.1.0_1534986766662_0.6533063836182449"}, "_hasShrinkwrap": false}, "1.0.0": {"name": "colorette", "version": "1.0.0", "description": "Node.js library for colorizing text using ANSI escape sequences.", "main": "index.js", "types": "colorette.d.ts", "scripts": {"test": "nyc -r lcov testmatrix test/*.js", "release": "npm test && git commit -am $npm_package_version && git tag $npm_package_version && git push origin master && git push --tags && npm publish"}, "repository": {"type": "git", "url": "git+https://github.com/jorgebucaran/colorette.git"}, "files": ["index.js", "colorette.d.ts"], "keywords": ["colorette", "terminal", "styles", "color", "ansi"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jorgebucaran/colorette/issues"}, "homepage": "https://github.com/jorgebucaran/colorette", "devDependencies": {"nyc": "12.0.2", "testmatrix": "0.1.2"}, "gitHead": "b7afe5853ab0a3744da2a2eba28432d8c414da43", "_id": "colorette@1.0.0", "_npmVersion": "6.3.0", "_nodeVersion": "10.0.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-92Fwo7yVVO1fb2VcuhLdF3dhNC4RJieDyyCYd796iimSCV+0We1r/KZf3IrmnHfHYZq2BY2sClLWJ6VJCOD/tA==", "shasum": "bcc99e898549bb2ef5fcfe0f397b9a891223b8c5", "tarball": "https://registry.npmjs.org/colorette/-/colorette-1.0.0.tgz", "fileCount": 5, "unpackedSize": 10784, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfrvKCRA9TVsSAnZWagAA+p0P/0MIPB+0weO8WnoUioJR\nggm7sD81/QrvXoXzSfXny818a7PRDJrY2+pcyLM140lnMFVYi1IydRqU8hEi\nBsBQhjBfRvFDsMbA2vEh8S2Cgep3DIdHmsL1GgrBEmyf0f79rTYzzslNkp+r\nu5HhJOakiGcW0Zvi4l8Qa/X4ePRjAuZq+SjmsYLqbjeTB1N+mn+gmXem9ezV\n+m5F1T97Yiduqi5E8p7XZBdmEjjZcLVKO2RLWEqYCWd9vXTmJY6+OcallN+X\nuSMsVg8zmfvkc+Kn4p2tOKFPZpTaMYQSlBKypX/MFmGtqpG1k1q7/dhDUqXY\n5Tm+tjz0aIoKsyyK6jUZ1lJcesbDKs2QtcPd0899u3esnIqveWTWgSXFAStO\nlOJBmM1d5JPe3tf+zp64GilojUR5xNWjkWUWhTcbk5dkVe0C0CgHFct0fdj4\nsrH9pQpoXFEp7Y+eaEQXuPb98UWxBGiqxvgvz+ZM22T9wDAuetJ3xahi8Ju3\n7PpVWRGJzVagTrSM/P7fpu2oFNN4sfNhO9XaXmPTIj/K5Ly+nIROPNkm1MZJ\nvqK6iXlfdjA3ZVPlaCaSh5GFJ0ELpXJe8B7rPx9pSuses8xBnZHrfAoetaMm\nCMdQ3oVeOzZ9oaN06N+OR1VRj/wl4Gf+4ewFei+tU4wT8mVfOZYc+MfGssQk\neCIq\r\n=LA/M\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAY/+vbkNDEmfLwFYOiAgDS0YsOAHNiNDvXYpni1EwRhAiATLjDetknbmNphlLhx1GrFSk0FS9Nj2GQ1FtlrCEYfoA=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/colorette_1.0.0_1535032265528_0.5424371106436079"}, "_hasShrinkwrap": false}, "1.0.1": {"name": "colorette", "version": "1.0.1", "description": "Node.js library for colorizing text using ANSI escape sequences.", "main": "index.js", "types": "colorette.d.ts", "scripts": {"test": "nyc -r lcov testmatrix test/*.js", "release": "npm test && git commit -am $npm_package_version && git tag $npm_package_version && git push origin master && git push --tags && npm publish"}, "repository": {"type": "git", "url": "git+https://github.com/jorgebucaran/colorette.git"}, "files": ["index.js", "colorette.d.ts"], "keywords": ["colorette", "terminal", "styles", "color", "ansi"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jorgebucaran/colorette/issues"}, "homepage": "https://github.com/jorgebucaran/colorette", "devDependencies": {"nyc": "12.0.2", "testmatrix": "0.1.2"}, "gitHead": "22d126cda98e6307d4fdc4f08d71692149e0547c", "_id": "colorette@1.0.1", "_npmVersion": "6.3.0", "_nodeVersion": "10.0.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-40MnlppkzHhFjRhtXunbpqKUT+eJn0gyVGi8aQlNSG8T2CCy31NdD7yktcS0aizH1VP2OhhQCyGMeTp0a/fvaw==", "shasum": "434bad4bd70969c075162fec86ca55da36bf837c", "tarball": "https://registry.npmjs.org/colorette/-/colorette-1.0.1.tgz", "fileCount": 5, "unpackedSize": 10795, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbf8VkCRA9TVsSAnZWagAAtEQQAJC156wsgUkHFa0Ky7wQ\nM/rKPTizJSccE/yhsG2WbS9wf/2smGRZQi414c8eg/cEK+418fiXRc4Kzjbq\n/M8NoetKB69obv6o094+JCjnja+bYzJ1jrT+Oov367lrIV1V/r5dehC+nj80\nlaiLqr6DcNNbLNiaOxtT+A5SGC/iOlJhG1rxvr7CxTopagNfBZo8WYtqcRMl\nbk7Tb5I3aVH6aFc+0y8CZnkczYi7PF4ULN3lSDZzEmX4S1IdqyqThniURLlS\n5mISiFpO8Nl3VBO8mgWOvJHxBXk4Z/J8Yv74IniJmv5gxZDeiyOKJYn+xl2x\n/lgoKAdLFZiYZ1+dLi91lgAwAXafE4UYkTZ73cLF7KFmvY1rqZqoqo9owZjT\nNdcSbSPI9Jc+Zc8R6rLcDEeX/pKYeqpEb/M46welBh3q7lLiyMdyJO9lWWs5\n7iz61p9V8KsXhEBogDXK9i+tj3zTSnzq+HWBzV3DLWFeWUpNSblw9HVZLW6u\navFTNDNnLYnxwz/eCSJ8NbO6T8rc39ktZ2I1dm6p1y26ky1L27WyGpt7eLW+\nEqo/fhhu35iaBvNDPrxTO6Zu7uE/JkHdkS5YPigGw4s54t9UwXxq25+7TrV5\nQq7x2rhpppRy89CmNZqso35GenxMEQfEMyt54ziMpcE5qnncrkua6ToiRXwk\nSWsP\r\n=b2qB\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCh2igwqapV2LN4Fw8xJSR0U1bCg2z2x/9GPwHAyz2LGQIhAOER1+anrx6APSWgmwn/EAXsQ8QWhkzAVtSJfyRIxikn"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/colorette_1.0.1_1535100259966_0.6219160097104803"}, "_hasShrinkwrap": false}, "1.0.2": {"name": "colorette", "version": "1.0.2", "description": "Node.js library for colorizing text using ANSI escape sequences.", "main": "index.js", "types": "colorette.d.ts", "scripts": {"test": "nyc -r lcov testmatrix test/*.js", "release": "npm test && git commit -am $npm_package_version && git tag $npm_package_version && git push origin master && git push --tags && npm publish"}, "repository": {"type": "git", "url": "git+https://github.com/jorgebucaran/colorette.git"}, "keywords": ["colorette", "terminal", "styles", "color", "ansi"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jorgebucaran/colorette/issues"}, "homepage": "https://github.com/jorgebucaran/colorette", "devDependencies": {"nyc": "13.0.1", "testmatrix": "0.1.2"}, "gitHead": "349387774804ca53c09c340a69ed65cfb2e2e56c", "_id": "colorette@1.0.2", "_npmVersion": "6.4.1", "_nodeVersion": "10.0.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-w+6oEGj2uxdTMCLxMln8hBqSnX0ax+atqWwLxkX39l/aTOybjhwkccRXvnGk/4XhCXHfDeh04dhFjks9uzHVcg==", "shasum": "45f2f92cdd3d77beef9080f6699b8e8a3f500300", "tarball": "https://registry.npmjs.org/colorette/-/colorette-1.0.2.tgz", "fileCount": 5, "unpackedSize": 10869, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbpwSUCRA9TVsSAnZWagAANpQP/29kuhzcnuYupgB3k/td\nHvQjXN4BUWHTqyWmBKnYJCx3woskNT6mYUcgwAlUo2+dtyUxPQ8E5Z03Dio2\nmk27172cchawbi7YIrBPGO6Ky+9jROSDW64oDmHpFssYlDpFTBcKY2mwEFqE\nnnX7hXKg47Lml9asB69hVlDz15f/hM/0CP4QySvFXCNuvPR3N9PN8gUKBkR5\n+jpLig3KIJnh8TkXg410icr04iIKX7fD+1qPz2WHWViykhslPPLIfS3wD/ao\nvugdJFfuePpfcE9rmm/5roH25ycvOT0Jn65X/9K2IO1aQtRx+6TSwgSrF1Gs\njDtF6/vESzr6gqBTHkh++wt6y/881uDlVDkWYsoW3lOgHMUMqDKEgCvD3QrH\nAnw6mxzOJ+cVEx9Kx3d9OhV0rmGnBzQihicxe4JIAoD3LLfpDpSmDxmeEppy\nGmfljLS65ikZrUz9ABd9xVdzIf364ZNnjKbO26PpMzjQfJ7eByRivzjdc1i8\nsNporIyGdqXX5A3OCIeYmDInLhguqdSW0ro1XvcEtOUle97twyHENq2Ro+wa\nFS5h245NMOxjRmtsDyIjAnFQ8nrZcDOJreFBca3OgYdUB6kwgVVxFdpSQ3jU\nxcw8LYDlOqqP+jjsRR5YJWLaSui1LDu0UlA2EFmA2AhuJHpsQ1U42kchoaxn\nWqDa\r\n=hjR1\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCKUvFJBFIKogA33sH7Gh1ARcfPYb+2w5d5fzz22ZqTTwIgEOeBtJnfRXeYZZbmordMyz7jmHHmSVW9vIJJuOOlJcI="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/colorette_1.0.2_1537672339337_0.47675680155596845"}, "_hasShrinkwrap": false}, "1.0.3": {"name": "colorette", "version": "1.0.3", "description": "Node.js library for colorizing text using ANSI escape sequences.", "main": "index.js", "types": "colorette.d.ts", "scripts": {"test": "nyc -r lcov testmatrix test/*.js", "release": "npm test && git commit -am $npm_package_version && git tag $npm_package_version && git push origin master && git push --tags && npm publish"}, "repository": {"type": "git", "url": "git+https://github.com/jorgebucaran/colorette.git"}, "keywords": ["colorette", "terminal", "styles", "color", "ansi"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jorgebucaran/colorette/issues"}, "homepage": "https://github.com/jorgebucaran/colorette", "devDependencies": {"nyc": "13.0.1", "testmatrix": "0.1.2"}, "gitHead": "685cbabf9eb36beb06525cb2594da75767579954", "_id": "colorette@1.0.3", "_npmVersion": "6.4.1", "_nodeVersion": "10.0.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-YrU2DsVirZjpxmho5noGdX0RvuA3LIlIb7vPLyI3R09q7SHGF2pdvsCwiMVDtgaWHDMIkxwQ2wxLE5t8kmpm/Q==", "shasum": "d7dd9f3bde91551dff50f79264fdd39547ebb5db", "tarball": "https://registry.npmjs.org/colorette/-/colorette-1.0.3.tgz", "fileCount": 5, "unpackedSize": 10869, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbp0A2CRA9TVsSAnZWagAASQQQAJt9gMzujIVAEZpeKQa/\nCVR62quJGILXBbhuJE9VG7N/2FWvOpy0q9mXl53dpIynv947EMe9O4uutK3a\ni/dZKB4Lxl2Z3Gu3zPH7wGBDzMvo7QKp6/DCC8Bg2oeRuF2luIo8c1blPrr0\n8/Vj2M80bGgd6L0owhXmLDO5FHxOrg38Duz/5y/MLXr8GV3+s5Kv4IbhEO+w\nrgbMcONi2kHO5jvGXbpIGU6z1saaxFhmZ0Am1CKHE372uBUKYFxujxQMRH3M\nMNyY5BuoF62WB1OW7JmbsVAyIzqKplu8aaS3ep68XLjEI3qEyiGXha9iVKJY\nedNTeDoo27HaUR4meJ6mlUYmFLUAxJcK/k4t14W1IMSBuFMLDbvLCKZ2jSk0\nMz/mup05rZZKTDaaPm8VncbsJ6dj1y2l4cY4yX+LFcWUmRur4COophvc6THR\nUm2jSmshmIlhnysbVReahSL9TML1flI4BcoX4s94W4WVYTnAe08XgwsRPU3E\n3k/vk/4c+tloKzx7ZVDk7C6Z3zxeXp+wvbRKPWEPq9gRjWfeiKRYBYghBiPW\nOWjE3ANgaxGZeo4KYUR6e/yluIaE+zJeHKK9W1kuEVAS4Fr0eoOpDmVsLT+2\nUiRTltGnabvsmGHD2nUC1nrZ9w3YPzf0fgzsOAyYJSwP0enP+/tCtWUSwlQE\nnt6I\r\n=hgVD\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCn5BpX6c0/OzXBNIjh14Cw1OF0orri72uRxHaV6Crm8wIhAMgc/f2kItORQzObO7OlLWBvhA0esd9FSQhZ0514l+tH"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/colorette_1.0.3_1537687605604_0.6841758704952161"}, "_hasShrinkwrap": false}, "1.0.4": {"name": "colorette", "version": "1.0.4", "description": "Node.js library for colorizing text using ANSI escape sequences.", "main": "index.js", "types": "colorette.d.ts", "scripts": {"test": "nyc -r lcov testmatrix test/*.js", "release": "npm test && git commit -am $npm_package_version && git tag $npm_package_version && git push origin master && git push --tags && npm publish"}, "repository": {"type": "git", "url": "git+https://github.com/jorgebucaran/colorette.git"}, "keywords": ["colorette", "terminal", "styles", "color", "ansi"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jorgebucaran/colorette/issues"}, "homepage": "https://github.com/jorgebucaran/colorette", "devDependencies": {"nyc": "13.0.1", "testmatrix": "0.1.2"}, "gitHead": "aca5f0897e445276a4fb506ca2d9cb8b88236258", "_id": "colorette@1.0.4", "_npmVersion": "6.4.1", "_nodeVersion": "10.0.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-PlchYmFXFnfHlOL4fhF0RTlOSw4m73mzJ+0x6MkiuOoLtx3ZK0sIL4fWqRdw2cbsJ8kV09V5ElJn7soaAxkhAQ==", "shasum": "20019ec1e0d7ec43fd8f39f114718989869e5ddf", "tarball": "https://registry.npmjs.org/colorette/-/colorette-1.0.4.tgz", "fileCount": 5, "unpackedSize": 10900, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbp0IQCRA9TVsSAnZWagAAvecQAKBnGOPWFz6I3LrQYs1c\nQ1UwDOUmq/+QyrPh3Fot5o/khPqz8qcw65Y/D2m+zR4T5Z/RwmG91ntXLQ7s\nqXCro/GRrcBPe/z1mOUXIwB7Pqk8qaIxwPMwnMJlWH/6WuXa8TjC9UBWRHvt\n0mRD5j5Fxz7SMgqdafkxOxqrqnHzcovLCOiJ88+lcMUfS9jQyd5ZvhvnMw/4\nZUmwkW6i9QHs8WUQgkCEKcLUJh2c2KLAurIkz5YTY1jLGfZkgbUhlXwqInj9\ndtOcZDpKnsx8PbAA8ngg3MTqo5i6ZGGvanyv/btVYrBKqBgQ3zJKYJBgxafA\nRy+K882X7p6uV9pEKrwOfRti/MmVE9FCnq6dAWvPu+xJ4uNlUxJ0fzxpHfgK\nq5CkLSVwny+n7VRGdPlWLHYtCIZtCcP33YFTG2Lgo7WOpbhkbWWPyt857BoJ\nN+rgd9ncqHlIUmrU4dlzv97jlsZfbaXi7Njg5K4Wniu89NBnsOONvG58W9ch\n/CMfsw/KvIpjrB6HtzqHSJlG4zf5hqYnw3tpz1ykYvueMKt1JsTYA07mDZMu\nYvMN6SJ+a4jxcpcFLwolX4ZrmKcw1GNNQgnkwBv2VDyZrxa+L/eiQ9xwmcS5\nUP5NC+AVblY3r+QMg6OF6nEPHsbSbYHuBSw9Mfar2ijVdqBEzdW9zCk26oQb\n9nIM\r\n=bmF2\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFd36PRWZh5AMpcusDOxV6B6Jin3+YgnuCSHsvLs5NGTAiBs2Nk8xxH7CsWZTG96itogi9hMhHHMkSsoSRxDDV8t3w=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/colorette_1.0.4_1537688080102_0.31564099053046335"}, "_hasShrinkwrap": false}, "1.0.5": {"name": "colorette", "version": "1.0.5", "description": "Node.js library for colorizing text using ANSI escape sequences.", "main": "index.js", "types": "colorette.d.ts", "scripts": {"test": "nyc -r lcov testmatrix test/*.js", "release": "npm test && git commit -am $npm_package_version && git tag $npm_package_version && git push origin master && git push --tags && npm publish"}, "repository": {"type": "git", "url": "git+https://github.com/jorgebucaran/colorette.git"}, "keywords": ["colorette", "terminal", "styles", "color", "ansi"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jorgebucaran/colorette/issues"}, "homepage": "https://github.com/jorgebucaran/colorette", "devDependencies": {"nyc": "13.0.1", "testmatrix": "0.1.2"}, "gitHead": "a754148a8f686abed89357c8894d413908b800ad", "_id": "colorette@1.0.5", "_npmVersion": "6.4.1", "_nodeVersion": "10.0.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-7mcpZLRdXqHLaOhGfKaDD/K7UjLSp6MkrDT6DziQ/HKBxqV3G1yLZAc14UCP8bCQAJTbcvd4k6MGRVYUa4NmHw==", "shasum": "966f565cea6bd9c4cbade11e30b5cb6ad2225c88", "tarball": "https://registry.npmjs.org/colorette/-/colorette-1.0.5.tgz", "fileCount": 5, "unpackedSize": 10402, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbsIA5CRA9TVsSAnZWagAAhY8P/jNCPv5m2NLQMyEYaczp\nZuamUBsg8NlQc5g7VFPSHa7WzUVdmJB+8Xe1jcOqkhKMcolvmGQqzaX5R6Wu\nOn6BXJiMQGsMmcXn99QelUNC1y7PpeECr4C/Wq+ogkwsIDRWJ9bnlN9iOJwK\nwN7IL/iJU5pThcmVnWROZsrb8iAdfudqX2AR9cHQg8c+d8qjG96iPBC8X9in\n/X6vrNHvJSlMyclMG6/nQhNGn+Dt7PNpQCvv2FvWwHCGPy7gCExi7y9GcT/s\n0QSlZKleJO3mZgzrt0dcbitXKsBsQZ4+zAY0Heys9fJ4BQjEj5Sd6iDl7tLq\nPUoNW50uSsk6pit5l/wDFvTBesTumFKnXlCVVsysWdI/q/Y9AZX2o2WTU4MT\nYAbCiNB2LO3Cp75ju0V/PFGYwOLM/TwzaVCQ8H80ccEXU1b6EF7dMaL5lTdD\nRRP+pyzHGRWA51+E92Jf78/jXyjlt2aq5SvPvNHXOopIlfPEsrLsTS4HAFnY\nWjDcwozvAdaSxk1Fbhgp1VO9mQsMz/0+5vWmO7cvHQstBLqPUEADSbOhdINH\n2J5JshHGHR7ph248vlw/kfXp5YyDtYo63g/b9CuaFVLa9E/yNGgCGwCasPx9\n2X99fHsCtci8CIA7kppDKbZnI1LUgCZLDTQ6sopMKsngaSYmPXyIxf47lJhm\nDl96\r\n=Z+8g\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA2vUXBX9I03ZCPx9OZ+8WeGV4tCyHveUL4wESo5zzmHAiEA328J9nEYPAG53oj73NBLgAVLK9SIUp0xD8L3jiXUAog="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/colorette_1.0.5_1538293816730_0.42289416476719466"}, "_hasShrinkwrap": false}, "1.0.6": {"name": "colorette", "version": "1.0.6", "description": "Node.js library for colorizing text using ANSI escape sequences.", "main": "index.js", "types": "colorette.d.ts", "scripts": {"test": "nyc -r lcov testmatrix test/index.js", "release": "npm test && git commit -am $npm_package_version && git tag $npm_package_version && git push origin master && git push --tags && npm publish"}, "repository": {"type": "git", "url": "git+https://github.com/jorgebucaran/colorette.git"}, "keywords": ["colorette", "terminal", "styles", "color", "ansi"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jorgebucaran/colorette/issues"}, "homepage": "https://github.com/jorgebucaran/colorette", "devDependencies": {"nyc": "13.0.1", "testmatrix": "0.1.2"}, "gitHead": "918ac6b4f05fdf75187f2d3592390caa342579c5", "_id": "colorette@1.0.6", "_npmVersion": "6.4.1", "_nodeVersion": "10.0.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Q9m/YliLdp5HoAjGdl0br6ZIU3nt/wOEMg0ENsmrkDhkHI+p5ez7kVT5ud/vH3CD0TF0c+BZswezh2Td8F/how==", "shasum": "b0265d21ce91902136b20977399cfceb464f3a82", "tarball": "https://registry.npmjs.org/colorette/-/colorette-1.0.6.tgz", "fileCount": 5, "unpackedSize": 10416, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbxbLdCRA9TVsSAnZWagAACk0P/3bL11QdklNj3AKUCY4J\niSzjS8/kuS3gXmhUQQ2yziUO8MmU+8NbyiOID0UkKjv+5Ynsg6f9OAMfnx2I\nn9YNHnw0tsvpPKWx7+nK2SNH1aotZJ87gDiXBDwi6TtIOUFiwoqU7QTfUKsL\nC9PieyC5nsm0VF5VSb6ZVNA+RJXQBImmWv2cYBpKHgYgDp375felnTsb9mkO\nh8MFmrKCuWBsE5jlMBmGqmfEk+1QNeAKiF6/Gafk9o4VBZ96RPq9DwGn2VRG\n0wQc30D6fSbGgPDGb6l0wvEGzWUwCPhIV/gvZm1WKx5tYfIqAcmmrpgjnnGy\nfvHBszhqqcDesg4tdw/VX2HrSqRpHMWCa0MrQ4Ip/qBSurdRhJQJIzJNuYat\nefXuNxqIYADk/ME9n5la1rR4hhtkQ4xldmrAuOLtmSE+Akw/IGGtWFEhTlrB\nWc/4gLOXy4ynyoqP2tWJwIBaVu7zbiPlYgd+Aag7GB3ymK/ddNnGNdrL39tu\nt16LjSzcgi9IT/AO9CjfB/NziO8KGO+yhrCnH0/u2xrYMfMapBDiiRPmY9MN\nSoDbML95I2heaggfAwPnuue1m4qhtvCW+M8xUQuUONMK/HGXz41Gd42K5Azg\n+mLV3QAl7SvbaTE1NbWZ1x8wuMyvfuonW55o6jC3zUZ5tRx43z/cbvDts6xt\nT7EF\r\n=AI3q\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCWo7/IIcCnBBmCjdA2xesM9a6icH3hYKSsRQPY2uzM3AIgY8ER/LbEhrJZTQLCzA905CDFJQLAsfG0RIei7wmkpcc="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/colorette_1.0.6_1539683035785_0.8328063099672018"}, "_hasShrinkwrap": false}, "1.0.7": {"name": "colorette", "version": "1.0.7", "description": "Node.js library for colorizing text using ANSI escape sequences.", "main": "index.js", "types": "colorette.d.ts", "scripts": {"test": "nyc -r lcov testmatrix test/index.js", "release": "npm test && git commit -am $npm_package_version && git tag $npm_package_version && git push origin master && git push --tags && npm publish"}, "repository": {"type": "git", "url": "git+https://github.com/jorgebucaran/colorette.git"}, "keywords": ["colorette", "terminal", "styles", "color", "ansi"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jorgebucaran/colorette/issues"}, "homepage": "https://github.com/jorgebucaran/colorette", "devDependencies": {"nyc": "13.1.0", "testmatrix": "0.1.2"}, "gitHead": "0b8582d949287c91ac4ec29205b5c551dae43992", "_id": "colorette@1.0.7", "_npmVersion": "6.4.1", "_nodeVersion": "10.0.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-KeK4klsvAgdODAjFPm6QLzvStizJqlxMBtVo4KQMCgk5tt/tf9rAzxmxLHNRynJg3tJjkKGKbHx3j4HLox27Lw==", "shasum": "7adf43c445ee63a541b4a4aef7d13f03df1e0cc0", "tarball": "https://registry.npmjs.org/colorette/-/colorette-1.0.7.tgz", "fileCount": 5, "unpackedSize": 9555, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbzvQCCRA9TVsSAnZWagAAaGgP/1XIhoJHcEJ54cRNFbbM\n0R07gc1AR9XIjlClUwvRCClwzQpXeOzepSHLKZnB0ieYDOSb7WKS9fl0ZHmT\nrAEfuhm0IxblsEXR15busBjN+0FEdaYYqM+uuY3u78t2K5QcNSIHU9CSePCs\nGJQ7u3knCO4+1ZNv8+5iRp9FaKykjJ48+J8og9yyRWm3fgYgoRwb8YhpFOR9\nggzYDwetpHHrtZsP8WWOheBvn3efDA8LiiRsphxONRGOwt6VmvSJy3MVjkVp\nksNy28x5duOhPi2p6v54XOGYjJNKS9h1YrWokr1rDniJ5NCfeTdt11AHdKMs\nndp4GcJAZpHxiDL0BTlEK+RgnFrQHYJ9dHbDAZqjuMZF8dcWXMdtYs6oq+Bs\nQSSG05cySWG/VUL03oukQZUf+wodie9TgdSh9zb1UyUXKzz5NxuX1IRiarwQ\ngUVY6L7KU3GWb92+lqnMtYdf74Mo7UgewyQ6yrEnacDIG2zkqz/F1dHY2UKB\nw+lixp6p8HMoFURfJ6saxhhx7L3UxaeNj3NwcGNqJllV0QDhJi7ASLaIBJQN\nVQEdaCrk/7I+jgJmBdcKfohNGTDK5I/KKcuUs8K2NFmNTPF8T/2m14pP5QVF\niOZYCPpkWsqWwN7Qy8mrMheBupbBo7Xaju2vuIMAtCgm7hZmVXb552e9/k6N\neRYh\r\n=GxL4\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAJPtoUxyrlKH2tN34+jakGyhLKTfXwjy+SwaYwLGZZ4AiEA19Tkf9dz+sjQz0H56pKtQKuZntPuaxe5kcWHoa3dU3Y="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/colorette_1.0.7_1540289537466_0.9806648957653736"}, "_hasShrinkwrap": false}, "1.0.8": {"name": "colorette", "version": "1.0.8", "description": "Node.js library for colorizing text using ANSI escape sequences.", "main": "index.js", "types": "colorette.d.ts", "scripts": {"test": "nyc -r lcov testmatrix test/index.js", "release": "npm test && git commit -am $npm_package_version && git tag $npm_package_version && git push origin master && git push --tags && npm publish"}, "repository": {"type": "git", "url": "git+https://github.com/jorgebucaran/colorette.git"}, "keywords": ["colorette", "terminal", "styles", "color", "ansi"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jorgebucaran/colorette/issues"}, "homepage": "https://github.com/jorgebucaran/colorette", "devDependencies": {"nyc": "13.1.0", "testmatrix": "0.1.2"}, "gitHead": "07ac11b2c4b1e59ac92e2f2910b6fb0d3a624f34", "_id": "colorette@1.0.8", "_nodeVersion": "11.14.0", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-X6Ck90ReaF+EfKdVGB7vdIQ3dr651BbIrBwY5YBKg13fjH+940sTtp7/Pkx33C6ntYfQcRumOs/aUQhaRPpbTQ==", "shasum": "421ff11c80b7414027ebed922396bc1833d1903c", "tarball": "https://registry.npmjs.org/colorette/-/colorette-1.0.8.tgz", "fileCount": 5, "unpackedSize": 9223, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc8TzWCRA9TVsSAnZWagAAgT8P/0oVK6x1w0J2jkh+RCQf\nUaQT0QtT0a3EpGfja7eWreqHTHftDi7TVs5OzqcL7h9/+/Jviglx+3SZmYt/\nKf9r4wjyWdmpYt8mYfwZmJYYbcL8HGXCReO30rlXnCRK06ZjaKZ9F4cfjj5j\nbCxjpjjeRtHKdu985kB1aagjPmjEslCWkb2YUCwxun+4Gdul2z+J7SndDTRE\nzGwrk1sgMIKuMgJv3Kj2KJv4Qeg6/yL/IkbIWC2t1PC3LKGJa6XjCL1XSGYf\nGeflsJ4L752/DaNDgTOOnkRnfDBY8BkOQXLVCG5eB1Ci36aLgvMh5F2aj2hy\n42toPygpaISWZQcOyoSI5cOlCdbz/ObYP91eu72PwD5oBZDfqdoRnCDWwgNE\nf5Y5UOMo3LYnv1woSfuzrgADF1bYBW/yt13tahNZqtSSXfAsa8ZjcHctIsq/\n3nxcwuhCjsUDnIZvzcPyncrjWhqKoO490VhcjNYH2yuNKsurfIlM6bQnGRxb\n1r4ypoyYYKtelx3t8MTouqBd2F15FzTeejWHKMuNYuFxlF8YSWJQmK1jPtuK\n0GNgflMPMMmK2Y0vPNipphBEgq475kjuOKbsWkgpdxe3ZgrlBhY9RSCRFs+e\naiN7IkcMWCdxAXMKFWwoZhOmYca4sZNmd3D1JuCqwwUvuiYI93bmz9qvph+e\nT23R\r\n=ZB0H\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIB8NMAdeNtiiu2GZpoevjEX0LdGyTCn6hBwlkjNB8jI+AiEA/bSUFpz3P4AkhR123cch/7VkG/Tfc60sbmkNO2K7MFQ="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/colorette_1.0.8_1559313621621_0.14417143603549487"}, "_hasShrinkwrap": false}, "1.1.0": {"name": "colorette", "version": "1.1.0", "description": "Color your terminal using pure idiomatic JavaScript.", "main": "index.js", "types": "colorette.d.ts", "scripts": {"test": "nyc -r lcov testmatrix test/index.js", "release": "v=$npm_package_version; git commit -am $v && git tag -s $v -m $v && git push && git push --tags && npm publish"}, "repository": {"type": "git", "url": "git+https://github.com/jorgebucaran/colorette.git"}, "keywords": ["colorette", "terminal", "styles", "color", "ansi"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jorgebucaran/colorette/issues"}, "homepage": "https://github.com/jorgebucaran/colorette", "devDependencies": {"nyc": "14.1.1", "testmatrix": "0.1.2"}, "gitHead": "51c9ae34397366733056e7a0e367ce02c1c62b5f", "_id": "colorette@1.1.0", "_nodeVersion": "12.5.0", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-6S062WDQUXi6hOfkO/sBPVwE5ASXY4G2+b4atvhJfSsuUUhIaUKlkjLe9692Ipyt5/a+IPF5aVTu3V5gvXq5cg==", "shasum": "1f943e5a357fac10b4e0f5aaef3b14cdc1af6ec7", "tarball": "https://registry.npmjs.org/colorette/-/colorette-1.1.0.tgz", "fileCount": 5, "unpackedSize": 9022, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdFqbICRA9TVsSAnZWagAAH4gP/3bcg2JVKGtPI8+Zkzez\nJeFAHKGz6sSXBkA6nNj6d/6xGorXmnxFuaZjrECf8c+0Lyu2AAY8lJP+3I6m\nEWkCgA2Mwyh3DT3UF4tbbZBmERtr0VDDxeiSMhkYhXNVc99+N1FSy9RydLaL\n1I3DBOjRJMOcxA7GdryYBbOwTWhYxfGOU+xtpqmeTtNiJ4s8Gbt5hjDlKO1r\nGgzIBJQq4lZ1MFtEgbDw6t/unfIlpwnkmfB7yySohGIlL3C16+oSjR/xOc+i\nnxV2oZeQQXyJt+tKK11YraTwLDzTIfVvueLcBjCjdUr3enjoPQudoFAO8ad0\n9kwMnBKIptr0hGBgJ4S/0FY1nQ37Xo+Af+zwxW2HUfd0DTzw/npdkKFwAFFq\nauuLaT/+lqdFSHx+oZnxDCzPiu37EpZ3faTALErgogzllnfhLRnaKCAfD8CN\nypwzMYlCimpX7LLiHlOUkdFs5oxMgi47mspYnhrXULMhI3di60he3h6+ykwa\naw7eaiVs1pZjkvQy43HAnFTc2leyGEqy+RJOZ159vZZiUoliu9g8MPmxpP6i\ne2doHb0IahYRBrKxIVCN33PDiXCYokAYk+uZF49gyNMFiG4FgyrNIFSYMVt6\nk7ekAQqzG0F+M6Hr31M8K4Q+M5T9gqSsSltgLwA+CGrNACv+13II1GQu8fJG\n/lQC\r\n=jxHv\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICFs8yG2OGCU0e9Bw3/d7mdMsPsN/bvYeyeO4VjmRawkAiA2hYYMnGH3pI8A8FfuLRFIYHbq7UN364HzQvvp0V4ifw=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/colorette_1.1.0_1561765575404_0.30068890487149136"}, "_hasShrinkwrap": false}, "1.2.0": {"name": "colorette", "version": "1.2.0", "description": "Color your terminal using pure idiomatic JavaScript.", "main": "index.js", "types": "colorette.d.ts", "scripts": {"test": "nyc -r lcov testmatrix test/index.js", "release": "v=$npm_package_version; git commit -am $v && git tag -s $v -m $v && git push && git push --tags && npm publish"}, "repository": {"type": "git", "url": "git+https://github.com/jorgebucaran/colorette.git"}, "keywords": ["colorette", "terminal", "styles", "color", "ansi"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jorgebucaran/colorette/issues"}, "homepage": "https://github.com/jorgebucaran/colorette", "devDependencies": {"nyc": "14.1.1", "testmatrix": "0.1.2"}, "gitHead": "99c8cfce1ea59d5cefcdf43affc44fcb193ec345", "_id": "colorette@1.2.0", "_nodeVersion": "14.2.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-soRSroY+OF/8OdA3PTQXwaDJeMc7TfknKKrxeSCencL2a4+Tx5zhxmmv7hdpCjhKBjehzp8+bwe/T68K0hpIjw==", "shasum": "45306add826d196e8c87236ac05d797f25982e63", "tarball": "https://registry.npmjs.org/colorette/-/colorette-1.2.0.tgz", "fileCount": 5, "unpackedSize": 8903, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJewC/2CRA9TVsSAnZWagAAotcP+wXzCtWNNiMKXcFUU6xh\nLLdnuA9PMHwdNja1wu2C8/aAMDw9QSSW7W2shydLbectOvnGoAhbnyTlQ5Wa\nP5b5qsFCiik04R7sXOB+WT7H0ig8nCBd7XGFdAxM3o/ZZS3f91kujASh8V+O\nx53yTRlJ9AZi6OKYL8d4XHp6iU0bPv3RF9uwIx2ZCxNeqelO43RpliTywQIO\ndlObU2SWRBx37vFXFfY+vmGHWRrBZflyX9zYeja5iHftFQcU/XF5u63bEeJu\ngE4b+HXODamCSyIGtEyiZE8mSCaGuasrWz3xLNCS6siq9Q2pqjkurzX/gboK\n5fENyjUaD40Hp/rLbUZEK3SNFYTO7XnDXnp2rjnsU1TEUa8mR1yJSQFz4Fii\ni3r3xysQ+rGywmza5iaFPJmF9wrrtjooLwC7wPQSv+aT6HPTVzys6qc71zU0\nMJDkGIyFE4SM3lC3ZhchiA/xkx9m6zU0SXqe0Lscf8BuQIc4A4oDUzMWOrXC\n5ZlxevXmCPOjg3lDMk5G+uaTVZjRWW6aNNBQDkN7rP+apoiyCgQMb/MdauNN\nAlXgpkWFfVbG4VHX8yS/1cZRiqM7b8s8Qg5ksBJ355w2SMvIloLaTZGvG2FL\nv/h02um25U8LXccNBKRC0mjs7FMQb8XzfAMF2ks96hW0WlWkpu4Om+8DY0FR\noBq4\r\n=jMfO\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBau1ryKlCUzj0Q1rPxmni+FArkScZNeyLHvxB1nKjaDAiBlG9FfwuYL3EpmVO+TTvfxQwuO2bb+icp0pcZur1sZuA=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/colorette_1.2.0_1589653494211_0.4988314825806963"}, "_hasShrinkwrap": false}, "1.2.1": {"name": "colorette", "version": "1.2.1", "description": "Color your terminal using pure idiomatic JavaScript.", "main": "index.cjs", "type": "module", "module": "index.js", "exports": {"./package.json": "./package.json", ".": {"require": "./index.cjs", "import": "./index.js"}}, "types": "colorette.d.ts", "scripts": {"test": "c8 testmatrix test/*.cjs", "build": "node -e \"fs.writeFileSync('index.cjs',fs.readFileSync('index.js','utf8').replace(/export const /g,'exports.'),'utf8')\"", "release": "v=$npm_package_version; git commit -am $v && git tag -s $v -m $v && git push && git push --tags && npm publish"}, "repository": {"type": "git", "url": "git+https://github.com/jorgebucaran/colorette.git"}, "keywords": ["colorette", "terminal", "styles", "color", "ansi"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jorgebucaran/colorette/issues"}, "homepage": "https://github.com/jorgebucaran/colorette", "devDependencies": {"c8": "7.2.0", "testmatrix": "0.1.2"}, "gitHead": "c5b8a001b7c0202752048bdf4afb9a9142dcf7e9", "_id": "colorette@1.2.1", "_nodeVersion": "14.5.0", "_npmVersion": "6.14.5", "dist": {"integrity": "sha512-puCDz0CzydiSYOrnXpz/PKd69zRrribezjtE9yd4zvytoRc8+RY/KJPvtPFKZS3E3wP6neGyMe0vOTlHO5L3Pw==", "shasum": "4d0b921325c14faf92633086a536db6e89564b1b", "tarball": "https://registry.npmjs.org/colorette/-/colorette-1.2.1.tgz", "fileCount": 6, "unpackedSize": 11788, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfAdaeCRA9TVsSAnZWagAAI1MP/Aij/C7R31opfbyZ8hn0\noUYxxdH+auCy13NmaaxC5Q5iuRwpsKm5wdg/QbUv87VUcwbWwYPMm7LpTS+y\n5aZ5R+nziKVHtrX9GfYoLLeOSaG2eJHglixBm64DF4lfvJAXjiH5ZP2AYZOr\nDpDZyTUDma7D3muK+LGhy8FMh2e+lOO6cdOVUGvT+n+kxpOS6rc9uyxTxe96\niNOfv9VpiaKld1tA6nWqswakNb9dTm2guamrgwn9nAmKMHTvwkPtEZSbhf6H\nDE5rjQZK5Ra3kWILmufft557psYvYSzj/4YFjqMdn2EdjiH1DApIRipNCpN9\nWOYvY4cXGe5I2hVW96pWYSnaFnK9BEqGFsV0KEOG0vVmlYg81wbt6+Yn8+rV\nfgIUSrnxnWjqVcvJxCuZ/xhbEGzs/1Shb3dmb5OLGWFba2IVuAwDC3A5HuLT\n6QXoUeeId8DGaz1Px6bB7CPyW+AKadATOyfv5PF9Zq8nxyRU13VAxg72U8+o\n4PCyuBJ65R8arSfTUaXI5Zl8aLFEQCIyxsSE4Kkz+YB+xKC3b5AQV43ft4Pl\nA5YHcPKpryNHDDMS6pbhIGJBMkaGSqDhGVlvWI8xP3m+hBhiod7J6QnHIKzP\n8vc1DJfy/AH5Oz92RYH7n3KB7bKWkGuGW29oVCHZbHzAsdKKLpId7eWAFj2w\nzeqn\r\n=1x39\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIH0fm56fNGrc8mei605pvFr6afq3KS66FzSQpw3VmjiMAiEAjAAIiaAzL75Y917dPKFXo9vX86L05AAPnOqjAUHlQcw="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/colorette_1.2.1_1593955998082_0.6836392249584926"}, "_hasShrinkwrap": false}, "1.2.2": {"name": "colorette", "version": "1.2.2", "type": "module", "main": "index.cjs", "module": "index.js", "types": "index.d.ts", "description": "Easily set the color and style of text in the terminal.", "repository": {"type": "git", "url": "git+https://github.com/jorgebucaran/colorette.git"}, "license": "MIT", "exports": {"./package.json": "./package.json", ".": {"require": "./index.cjs", "import": "./index.js"}}, "author": {"name": "<PERSON>"}, "keywords": ["terminal", "styles", "color", "ansi"], "scripts": {"test": "c8 twist tests/*.js", "build": "node -e \"fs.writeFileSync('index.cjs',fs.readFileSync('index.js','utf8').replace(/export const /g,'exports.'),'utf8')\"", "deploy": "npm test && git commit --all --message $tag && git tag --sign $tag --message $tag && git push && git push --tags", "release": "tag=$npm_package_version npm run deploy && npm publish --access public", "prepare": "npm run build"}, "devDependencies": {"c8": "*", "twist": "*"}, "gitHead": "****************************************", "bugs": {"url": "https://github.com/jorgebucaran/colorette/issues"}, "homepage": "https://github.com/jorgebucaran/colorette#readme", "_id": "colorette@1.2.2", "_nodeVersion": "15.6.0", "_npmVersion": "7.4.0", "dist": {"integrity": "sha512-MKGMzyfeuutC/ZJ1cba9NqcNpfeqMUcYmyF1ZFY6/Cn7CNSAKx6a+s48sqLqyAiZuaP2TcqMhoo+dlwFnVxT9w==", "shasum": "cbcc79d5e99caea2dbf10eb3a26fd8b3e6acfa94", "tarball": "https://registry.npmjs.org/colorette/-/colorette-1.2.2.tgz", "fileCount": 6, "unpackedSize": 10790, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgN6V2CRA9TVsSAnZWagAAqW8P/Ri/gmSZlFjd0LlXAukZ\nRk3K7Jzwsd/P0QFFIl3FTiVwOcOO0ku0BiCb4QdtU9Y9g+3ifpZU34MTL9dt\nAqLm4c7X8KTOfk/Xw+Uul/q4rOI38Uimv/14eu8e2019dQBhCvwia+MV0mGo\nl6MFpdYLkRiMqKzVjZlZ8H6KE2HRjl4PDSxnArBMR/4UhbMFlXQkvS45nKVp\nEHDg7RCS5EEjzW9b3MQNs7lKhQks8v5kQlgFacQr4x3GaArdj7dEmhHb1TxT\nOJaILIJAy9sO4mqhw67fxyiL4j+5F7I27uEWs+JXlH8OuxAHjWTODhUNCDDa\nR4Cv93RyLD3tDg9zwMkLxWjl7OT5YKslAbuVw3SZKeQ8n9SaG7skw+XIbNnt\nnG/+t1MKa1g2ecY3b6HLyzZPnXR6LcEqaci6b1l8ZVf4e5coOHt+utwn2i0T\nHJgjk/WachJri/Zj5pOynKnQfgD76nEMBE6khkRUz6FbfoccygGX1bCme6Zj\n6yYqSwR9n/P3EzqhJ/jDkrrrORzY+sdAOS1k5XNCNqMZIbRYo/wBGZAmnov3\nGdii6urAYFiO2gMkZAawVaXEgqWqNHslRhEkYR3D33QCIAuGhcqYmPm1x9/q\nYFMMLvC/adS/cjouO+vwBG5yq/CBV8Zn6UzwJxE/0an38KFS210DagzckHFT\ngBwg\r\n=GzjZ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF2R67fvKgGTimnEf9JxZrqPOaX3x+/lFpR8/8Sy2F6EAiEA/eXPhFqOWOhJaKc/xRNTGxIHQ+5JVnvzCncB9SDeJOk="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/colorette_1.2.2_1614259574431_0.10657063868240502"}, "_hasShrinkwrap": false}, "1.3.0": {"name": "colorette", "version": "1.3.0", "type": "module", "main": "index.cjs", "module": "index.js", "types": "index.d.ts", "description": "Easily set the text color and style in the terminal.", "repository": {"type": "git", "url": "git+https://github.com/jorgebucaran/colorette.git"}, "license": "MIT", "exports": {"./package.json": "./package.json", ".": {"require": "./index.cjs", "import": "./index.js"}}, "author": {"name": "<PERSON>"}, "keywords": ["terminal", "styles", "color", "ansi"], "scripts": {"test": "c8 twist tests/*.js", "build": "node -e \"fs.writeFileSync('index.cjs',fs.readFileSync('index.js','utf8').replace(/export const /g,'exports.'),'utf8')\"", "deploy": "npm test && git commit --all --message $tag && git tag --sign $tag --message $tag && git push && git push --tags", "release": "tag=$npm_package_version npm run deploy && npm publish --access public", "prepare": "npm run build"}, "devDependencies": {"c8": "*", "twist": "*"}, "gitHead": "****************************************", "bugs": {"url": "https://github.com/jorgebucaran/colorette/issues"}, "homepage": "https://github.com/jorgebucaran/colorette#readme", "_id": "colorette@1.3.0", "_nodeVersion": "16.5.0", "_npmVersion": "7.19.1", "dist": {"integrity": "sha512-ecORCqbSFP7Wm8Y6lyqMJjexBQqXSF7SSeaTyGGphogUjBlFP9m9o08wy86HL2uB7fMTxtOUzLMk7ogKcxMg1w==", "shasum": "ff45d2f0edb244069d3b772adeb04fed38d0a0af", "tarball": "https://registry.npmjs.org/colorette/-/colorette-1.3.0.tgz", "fileCount": 6, "unpackedSize": 11261, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhEndRCRA9TVsSAnZWagAAPVMP/2wXdyaonKv23tAwbYgP\nQj8VXG+HfeY/weyN8thQh8Ae7FHguAMv8hiPbhYWO5EHaD/yX+mDRZPBpsD+\nmbZa3ovaOn6qx3ETIg6pX2uq1++eTs42xtPNDDBIaZiy1GQ1KYJtw05SXDfr\n2uLdx2aVXhOed5n20biQIyKK1Y1atUmESrOMe1yKGQCNR2P+MWjSuPOAz6YX\niB98hcbGDR5VJXfA4pbhdUiXZIB//D1ftKnEK9F57HnwzIn+16yFJTi6marq\nLzpIjRLkUJl0PX8ypPf2eg/ysA4cbFEp7bNlZ6J+bFvRvMoAJMgjCEK7FLSH\nJRgURqdOUjsFN6EucVg6HffFxsvH5/fnIaLUkT+cHrbY/EcTM0tcgnNNPpQk\nO8r2JY1LTqvFjLNb3m1YYXj1areAd88vjnT/OpIRuAG6f+458gq3pprigxUP\nogsDBIn7KBnJuFf9HVlnRhZZ3IPfOVO1sRokPyQzV1OJlboeb0ZIQgjv2q2i\nRrAsvbofjgeUQSo+Oj4Lz+5RRNVjlJOta1HjQkoOMGFBe2iVYOvxkAGhgNak\naCA5TF+yEw8k6remxp/k+sGYhxyEsbQjO4vVvre+hhAZYJVVBccoRAq+V+tI\n9vErVgfAd76jZpkXu4qiTiCUz1Mdj7nOTh6R3l6paTaDpwFYWU1B0rulZrps\nIqKu\r\n=6aRq\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCY7lDwnFfcHyBIP/LTXRKn7pPAhSYZoN07Ra9nOZp09gIhAMwJLVyKQEvW7OePqQG0OnXfE+DjeG9/eLaNvcFgiWJV"}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/colorette_1.3.0_1628600145214_0.09386362274132676"}, "_hasShrinkwrap": false}, "1.4.0": {"name": "colorette", "version": "1.4.0", "type": "module", "main": "index.cjs", "module": "index.js", "types": "index.d.ts", "description": "Easily set the text color and style in the terminal.", "repository": {"type": "git", "url": "git+https://github.com/jorgebucaran/colorette.git"}, "license": "MIT", "exports": {"./package.json": "./package.json", ".": {"require": "./index.cjs", "import": "./index.js"}}, "author": {"name": "<PERSON>"}, "keywords": ["terminal", "styles", "color", "ansi"], "scripts": {"test": "c8 twist tests/*.js", "build": "node -e \"fs.writeFileSync('index.cjs', fs.readFileSync('index.js', 'utf8').replace(/export const /g, 'exports.').replace(/import \\* as ([^ ]+) from \\\"(.+)\\\"/, 'const \\$1 = require(\\\"\\$2\\\")'), 'utf8')\"", "deploy": "npm test && git commit --all --message $tag && git tag --sign $tag --message $tag && git push && git push --tags", "release": "tag=$npm_package_version npm run deploy && npm publish --access public", "prepare": "npm run build"}, "devDependencies": {"c8": "*", "twist": "*"}, "gitHead": "****************************************", "bugs": {"url": "https://github.com/jorgebucaran/colorette/issues"}, "homepage": "https://github.com/jorgebucaran/colorette#readme", "_id": "colorette@1.4.0", "_nodeVersion": "14.17.5", "_npmVersion": "6.14.14", "dist": {"integrity": "sha512-Y2oEozpomLn7Q3HFP7dpww7AtMJplbM9lGZP6RDfHqmbeRjiwRg4n6VM6j4KLmRke85uWEI7JqF17f3pqdRA0g==", "shasum": "5190fbb87276259a86ad700bff2c6d6faa3fca40", "tarball": "https://registry.npmjs.org/colorette/-/colorette-1.4.0.tgz", "fileCount": 6, "unpackedSize": 11316, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhN5wHCRA9TVsSAnZWagAAxggP/iZv6HwKpMMkP/Dt/YAd\n7IMjsH6O72wej6fsgfaF9/u5K0uSFw4HMGxKIRImDhGIGmY+lMjaV1Nmc3mD\nCO9rdvv7dabc6pSfyyjcKbOlkaTIcEYHBmWhbkIuuxWHF8gvm4CWKGKkMmN3\n3b5H7cesrPkVPyW9CQpwdx939+lGx7RfPjF0wZqy49go29K81b7SF1IWKN7V\nqz2YY7ZleAJj9GXMPyK5R+j5RmNVDfY5c7BIIU7kzNKot6d8CuDNmtT6g8Yp\no4jSOblaRnj4XZJnyaMfrndrmCa9EKWv1rvlnue/K9N2DAIQoua44tpGA/T8\nFu2I0hOx1QhkRMKizfTSCW1+ddEdf5pksZgxSkRb0r6vSFEdm/JKq28+aIRY\nGh3/+2eyAKX/S/UVG/+lYD34PP6+mfMW7HrwN+2meduE/CWCYyNZcDZUn0Jf\nIc0f5Y5cWIahaL1me3nwEZgVZiXQAovx1Keyn1E8XMoZNSbcxuv/tYVuwEba\njieDcHIue0hybG+EOgnGi+lWl+EMxPduI6uw+waBbmnuSOCx9WE77TYMBddX\nLB7uJIX9zO2CFbP91aPxwxhi5a9r8wf99bivRJpk8/GbMwRJ/2cqnrHd/Jjz\neG0CRuw7IL3/3VGhrZCrNHn8MFPOx1MajEQrr1dtO97wKxNjSpNBqboLCNVl\nS5dd\r\n=D8Dq\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCznbeiwbYtmqYzzT070krhrWgxM+Q7iTMEEi96wSY3ZwIhAN7ZTDflCwKkQyAbuY28XG/GZ2Cn/qGhQd5QNCE3OeGb"}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/colorette_1.4.0_1631034374859_0.4696235282536243"}, "_hasShrinkwrap": false}, "2.0.0": {"name": "colorette", "version": "2.0.0", "type": "module", "main": "index.cjs", "module": "index.js", "types": "index.d.ts", "description": "Easily set your terminal text color & styles.", "repository": {"type": "git", "url": "git+https://github.com/jorgebucaran/colorette.git"}, "license": "MIT", "exports": {"./package.json": "./package.json", ".": {"require": "./index.cjs", "import": "./index.js"}}, "author": {"name": "<PERSON>"}, "keywords": ["terminal", "styles", "color", "ansi"], "scripts": {"test": "c8 twist tests/*.js", "build": "npx rollup --interop=esModule --no-esModule --format cjs --input index.js --file index.cjs", "deploy": "npm test && git commit --all --message $tag && git tag --sign $tag --message $tag && git push && git push --tags", "release": "tag=$npm_package_version npm run deploy && npm publish --access public", "prepare": "npm run build"}, "devDependencies": {"c8": "*", "twist": "*"}, "gitHead": "****************************************", "bugs": {"url": "https://github.com/jorgebucaran/colorette/issues"}, "homepage": "https://github.com/jorgebucaran/colorette#readme", "_id": "colorette@2.0.0", "_nodeVersion": "16.9.1", "_npmVersion": "7.21.1", "dist": {"integrity": "sha512-d9bi+jjWiz/9YkdIJjnoubyYXgWZ7Z0kA/KoFPBNSiQFnKtiqFZSwg7L6c+U+ju8yuy/M1AMkIIkdHcQnqWe6A==", "shasum": "3d3f6be9891e66ae011338a34d95d9a7a02741a6", "tarball": "https://registry.npmjs.org/colorette/-/colorette-2.0.0.tgz", "fileCount": 6, "unpackedSize": 14838, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCoihL8kj1cpeS9afB2lLGlgnnIh8XEh0sl98XnI5VEIAIgG8zWMlFZFkGaFnnFf1iM52hqzdaWFN7vVdYlayw+eyw="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/colorette_2.0.0_1631885029097_0.17331451363717876"}, "_hasShrinkwrap": false}, "2.0.1": {"name": "colorette", "version": "2.0.1", "type": "module", "main": "index.cjs", "module": "index.js", "types": "index.d.ts", "description": "Easily set your terminal text color & styles.", "repository": {"type": "git", "url": "git+https://github.com/jorgebucaran/colorette.git"}, "license": "MIT", "exports": {"./package.json": "./package.json", ".": {"require": "./index.cjs", "import": "./index.js"}}, "author": {"name": "<PERSON>"}, "keywords": ["terminal", "styles", "color", "ansi"], "scripts": {"test": "c8 twist tests/*.js", "build": "npx rollup --interop=esModule --no-esModule --format cjs --input index.js --file index.cjs", "deploy": "npm test && git commit --all --message $tag && git tag --sign $tag --message $tag && git push && git push --tags", "release": "tag=$npm_package_version npm run deploy && npm publish --access public", "prepare": "npm run build"}, "devDependencies": {"c8": "*", "twist": "*"}, "gitHead": "****************************************", "bugs": {"url": "https://github.com/jorgebucaran/colorette/issues"}, "homepage": "https://github.com/jorgebucaran/colorette#readme", "_id": "colorette@2.0.1", "_nodeVersion": "16.9.1", "_npmVersion": "7.21.1", "dist": {"integrity": "sha512-mPExxrv20AelDEwVkSZNzjV4eQLd9iL47ZVHsZ93luss5IZlOY53noO+KurIciKylAkwTctrL82V44OucyzKRw==", "shasum": "99950fe9d5c734fb5229704dfc66ea6e48ae5516", "tarball": "https://registry.npmjs.org/colorette/-/colorette-2.0.1.tgz", "fileCount": 6, "unpackedSize": 15076, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDPlQsoJ1s6v1eibCoF81zIXrCgH/AArfObmB24n3f/TAIgOw3TXeittvBTVt62YA18b1QQ12hoSASKJzHla+b6aXQ="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/colorette_2.0.1_1631962036677_0.20193703429791854"}, "_hasShrinkwrap": false}, "2.0.2": {"name": "colorette", "version": "2.0.2", "type": "module", "main": "index.cjs", "module": "index.js", "types": "index.d.ts", "description": "Easily set your terminal text color & styles.", "repository": {"type": "git", "url": "git+https://github.com/jorgebucaran/colorette.git"}, "license": "MIT", "exports": {"./package.json": "./package.json", ".": {"require": "./index.cjs", "import": "./index.js"}}, "author": {"name": "<PERSON>"}, "keywords": ["terminal", "styles", "color", "ansi"], "scripts": {"test": "c8 twist tests/*.js", "build": "npx rollup --interop=esModule --no-esModule --format cjs --input index.js --file index.cjs", "deploy": "npm test && git commit --all --message $tag && git tag --sign $tag --message $tag && git push && git push --tags", "release": "tag=$npm_package_version npm run deploy && npm publish --access public", "prepare": "npm run build"}, "devDependencies": {"c8": "*", "twist": "*"}, "gitHead": "****************************************", "bugs": {"url": "https://github.com/jorgebucaran/colorette/issues"}, "homepage": "https://github.com/jorgebucaran/colorette#readme", "_id": "colorette@2.0.2", "_nodeVersion": "16.9.1", "_npmVersion": "7.21.1", "dist": {"integrity": "sha512-iO5Ycn8HKVhTGGKpwJtyzrrvOGI9YC4u4dppP5yKSGP47ApaZNwX7ne4PtgpTzq+JBwQh6FrdAfCSYTBQe1+FA==", "shasum": "f04cbc7f59ffc4f33ba4da0702dd4c8a68885d91", "tarball": "https://registry.npmjs.org/colorette/-/colorette-2.0.2.tgz", "fileCount": 6, "unpackedSize": 15077, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG2GNDHS2+DxIK7/JVyc2Ciu2mzid6C18uJ8G/a67yDYAiB9oshQsOnnUvsoXRB70AXaueeP7c15eC/yqt96GdPXYQ=="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/colorette_2.0.2_1631964830205_0.8044939689928128"}, "_hasShrinkwrap": false}, "2.0.3": {"name": "colorette", "version": "2.0.3", "type": "module", "main": "index.cjs", "module": "index.js", "types": "index.d.ts", "description": "Easily set your terminal text color & styles.", "repository": {"type": "git", "url": "git+https://github.com/jorgebucaran/colorette.git"}, "license": "MIT", "exports": {"./package.json": "./package.json", ".": {"require": "./index.cjs", "import": "./index.js"}}, "author": {"name": "<PERSON>"}, "keywords": ["terminal", "styles", "color", "ansi"], "scripts": {"test": "c8 twist tests/*.js", "build": "npx rollup --interop=esModule --no-esModule --format cjs --input index.js --file index.cjs", "deploy": "npm test && git commit --all --message $tag && git tag --sign $tag --message $tag && git push && git push --tags", "release": "tag=$npm_package_version npm run deploy && npm publish --access public", "prepare": "npm run build"}, "devDependencies": {"c8": "*", "twist": "*"}, "gitHead": "****************************************", "bugs": {"url": "https://github.com/jorgebucaran/colorette/issues"}, "homepage": "https://github.com/jorgebucaran/colorette#readme", "_id": "colorette@2.0.3", "_nodeVersion": "16.9.1", "_npmVersion": "7.21.1", "dist": {"integrity": "sha512-AjSecBBd6uNN1KgD8PtoKwveuIHpyxPLNLuoWOyPt7qvx18EzdJJYlHBiQWtQdIDZqNk8AXmv2p9RihritGhHw==", "shasum": "f904e646f92fce7ceab23e8af2bd40e293d836ce", "tarball": "https://registry.npmjs.org/colorette/-/colorette-2.0.3.tgz", "fileCount": 6, "unpackedSize": 14402, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDqSWbRtOBm1B2p9ZSdi6LWS6+GLanDeYi9qEvfnUET+AiAyT2nNowxq1Kab+ytPqo5Nb03B2mmpoEDRGc4AVMIAmw=="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/colorette_2.0.3_1632239062639_0.6526594270032338"}, "_hasShrinkwrap": false}, "2.0.4": {"name": "colorette", "version": "2.0.4", "type": "module", "main": "index.cjs", "module": "index.js", "types": "index.d.ts", "description": "Easily set your terminal text color & styles.", "repository": {"type": "git", "url": "git+https://github.com/jorgebucaran/colorette.git"}, "license": "MIT", "exports": {"./package.json": "./package.json", ".": {"require": "./index.cjs", "import": "./index.js"}}, "author": {"name": "<PERSON>"}, "keywords": ["terminal", "styles", "color", "ansi"], "scripts": {"test": "c8 twist tests/*.js", "build": "npx rollup --format cjs --input index.js --file index.cjs", "deploy": "npm test && git commit --all --message $tag && git tag --sign $tag --message $tag && git push && git push --tags", "release": "tag=$npm_package_version npm run deploy && npm publish --access public", "prepare": "npm run build"}, "devDependencies": {"c8": "*", "twist": "*"}, "gitHead": "****************************************", "bugs": {"url": "https://github.com/jorgebucaran/colorette/issues"}, "homepage": "https://github.com/jorgebucaran/colorette#readme", "_id": "colorette@2.0.4", "_nodeVersion": "16.9.1", "_npmVersion": "7.21.1", "dist": {"integrity": "sha512-mw9BGVqQa4f0jnWK5SGce3XA5qRklGBSkX9Ka6EBkPA3P8zPX/QC9BlLI803b8Jy27qA9HpUxhy1GxYTID1xYA==", "shasum": "d3f3e81ec242623cb103adfb75fcd192d1559b5e", "tarball": "https://registry.npmjs.org/colorette/-/colorette-2.0.4.tgz", "fileCount": 6, "unpackedSize": 15068, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEFCIK11eWB9gnMNEjox01DWbG43kDyDTtcxKd+V5qbRAiEAq7NXg89xk5j5pyHOuoYull6wet7wo0jrxSCB4vy5h88="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/colorette_2.0.4_1632242668263_0.8526571936875185"}, "_hasShrinkwrap": false}, "2.0.5": {"name": "colorette", "version": "2.0.5", "type": "module", "main": "index.cjs", "module": "index.js", "types": "index.d.ts", "description": "Easily set your terminal text color & styles.", "repository": {"type": "git", "url": "git+https://github.com/jorgebucaran/colorette.git"}, "license": "MIT", "exports": {"./package.json": "./package.json", ".": {"require": "./index.cjs", "import": "./index.js"}}, "author": {"name": "<PERSON>"}, "keywords": ["terminal", "styles", "color", "ansi"], "scripts": {"test": "c8 twist tests/*.js", "build": "npx rollup --format cjs --input index.js --file index.cjs", "deploy": "npm test && git commit --all --message $tag && git tag --sign $tag --message $tag && git push && git push --tags", "release": "tag=$npm_package_version npm run deploy && npm publish --access public", "prepare": "npm run build"}, "devDependencies": {"c8": "*", "twist": "*"}, "gitHead": "****************************************", "bugs": {"url": "https://github.com/jorgebucaran/colorette/issues"}, "homepage": "https://github.com/jorgebucaran/colorette#readme", "_id": "colorette@2.0.5", "_nodeVersion": "16.9.1", "_npmVersion": "7.21.1", "dist": {"integrity": "sha512-rPmg56Y2iDrBBoQN9A8TwkXpIKtk0dWo2PZvIK1AcY6SLdIbgg0mtLm+o0MQFHZhiMQ6cVgL+wkTEO9XPPM4Wg==", "shasum": "e9257d0a938e2340d3d4942bde308b1016721c84", "tarball": "https://registry.npmjs.org/colorette/-/colorette-2.0.5.tgz", "fileCount": 6, "unpackedSize": 15093, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDJrUw+zMpVr6hbNq7UDdY9R4WZHLhlvyDXOBKI2IG8/AIgX6nN546hVATFwAy7z/kJzHCofWpgJ4qkUVYiXDhQ6EU="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/colorette_2.0.5_1632246928525_0.9986684823789542"}, "_hasShrinkwrap": false}, "2.0.6": {"name": "colorette", "version": "2.0.6", "type": "module", "main": "index.cjs", "module": "index.js", "types": "index.d.ts", "description": "Easily set your terminal text color & styles.", "repository": {"type": "git", "url": "git+https://github.com/jorgebucaran/colorette.git"}, "license": "MIT", "exports": {"./package.json": "./package.json", ".": {"require": "./index.cjs", "import": "./index.js"}}, "author": {"name": "<PERSON>"}, "keywords": ["terminal", "styles", "color", "ansi"], "scripts": {"test": "c8 twist tests/*.js", "build": "npx rollup --interop=esModule --no-esModule --format cjs --input index.js --file index.cjs", "deploy": "npm test && git commit --all --message $tag && git tag --sign $tag --message $tag && git push && git push --tags", "release": "tag=$npm_package_version npm run deploy && npm publish --access public", "prepare": "npm run build"}, "devDependencies": {"c8": "*", "twist": "*"}, "gitHead": "****************************************", "bugs": {"url": "https://github.com/jorgebucaran/colorette/issues"}, "homepage": "https://github.com/jorgebucaran/colorette#readme", "_id": "colorette@2.0.6", "_nodeVersion": "16.9.1", "_npmVersion": "7.21.1", "dist": {"integrity": "sha512-FwC7kNWw/EMsU5yjTlq0iW0Mmv52+IYEDXv05Tz0XLwXQij9uqmX2mCmGoBSQTbbsL/1UO9CGn7bI+xWymReSg==", "shasum": "05934a002800fa4299a6db4a35d1c175ff71a4ab", "tarball": "https://registry.npmjs.org/colorette/-/colorette-2.0.6.tgz", "fileCount": 6, "unpackedSize": 14528, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCIVFiDcqCO58fOz/jVZz92sh7IDATHq4lqw5tbjXqDcAIhAL0REfm8vYp6qVtvUlyRBUQU1PZ4ZEJpDv33uqXbY3A6"}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/colorette_2.0.6_1632313665173_0.4236795736974608"}, "_hasShrinkwrap": false}, "2.0.7": {"name": "colorette", "version": "2.0.7", "type": "module", "main": "index.cjs", "module": "index.js", "types": "index.d.ts", "description": "Easily set your terminal text color & styles.", "repository": {"type": "git", "url": "git+https://github.com/jorgebucaran/colorette.git"}, "license": "MIT", "exports": {"./package.json": "./package.json", ".": {"require": "./index.cjs", "import": "./index.js"}}, "author": {"name": "<PERSON>"}, "keywords": ["terminal", "styles", "color", "ansi"], "scripts": {"test": "c8 twist tests/*.js", "build": "npx rollup --format cjs --input index.js --file index.cjs", "deploy": "npm test && git commit --all --message $tag && git tag --sign $tag --message $tag && git push && git push --tags", "release": "tag=$npm_package_version npm run deploy && npm publish --access public", "prepare": "npm run build"}, "devDependencies": {"c8": "*", "twist": "*"}, "gitHead": "****************************************", "bugs": {"url": "https://github.com/jorgebucaran/colorette/issues"}, "homepage": "https://github.com/jorgebucaran/colorette#readme", "_id": "colorette@2.0.7", "_nodeVersion": "16.9.1", "_npmVersion": "7.21.1", "dist": {"integrity": "sha512-wSXeeDPxoi5xKvjvOGxyYlyqB3J+tbowaSsFm1rEsDsDE942aTLftbOE3XMqf3XaYC7QUtcd/3qadNAIEIsAYw==", "shasum": "0173393a8f1045c3b66fc8201036ef38ed0d1e7e", "tarball": "https://registry.npmjs.org/colorette/-/colorette-2.0.7.tgz", "fileCount": 6, "unpackedSize": 15292, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEyyFPOKXTi/5cI6bQ+SVyUNQITSaAJ4G2oUZsqC9EzxAiAObj6HVGw7sqSZ+L6yGbvxKyjxVwQiGzKpNY686lpTbQ=="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/colorette_2.0.7_1632318029550_0.26494480683069477"}, "_hasShrinkwrap": false}, "2.0.8": {"name": "colorette", "version": "2.0.8", "type": "module", "main": "index.cjs", "module": "index.js", "types": "index.d.ts", "description": "Easily set your terminal text color & styles.", "repository": {"type": "git", "url": "git+https://github.com/jorgebucaran/colorette.git"}, "license": "MIT", "exports": {"./package.json": "./package.json", ".": {"require": "./index.cjs", "import": "./index.js"}}, "author": {"name": "<PERSON>"}, "keywords": ["terminal", "styles", "color", "ansi"], "scripts": {"test": "c8 twist tests/*.js", "build": "npx rollup --format cjs --input index.js --file index.cjs", "deploy": "npm test && git commit --all --message $tag && git tag --sign $tag --message $tag && git push && git push --tags", "release": "tag=$npm_package_version npm run deploy && npm publish --access public", "prepare": "npm run build"}, "devDependencies": {"c8": "*", "twist": "*"}, "gitHead": "****************************************", "bugs": {"url": "https://github.com/jorgebucaran/colorette/issues"}, "homepage": "https://github.com/jorgebucaran/colorette#readme", "_id": "colorette@2.0.8", "_nodeVersion": "16.9.1", "_npmVersion": "7.21.1", "dist": {"integrity": "sha512-7NCeZ48vqlSQ0W2d+v2RaVyyeBnIWuK/+MRSjfCqbTvMU6YgiUl0Yh06Z5e1+UBx6l2xP3A09CgqaWXtxZ4tvw==", "shasum": "7d9975b017fbec35b3de7abc3813ed7858c4213d", "tarball": "https://registry.npmjs.org/colorette/-/colorette-2.0.8.tgz", "fileCount": 6, "unpackedSize": 15254, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCm6EtwoZ7HfydpCsgXo1p8zgl1ApYzkLx+UcUd10VXGAIgNlzWvckcWAHSFifUaDQ6r10yWPBqyPwMoJWCsM4KoIo="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/colorette_2.0.8_1632408140954_0.7818431315474994"}, "_hasShrinkwrap": false}, "2.0.9": {"name": "colorette", "version": "2.0.9", "type": "module", "main": "index.cjs", "module": "index.js", "types": "index.d.ts", "description": "Easily set your terminal text color & styles.", "repository": {"type": "git", "url": "git+https://github.com/jorgebucaran/colorette.git"}, "license": "MIT", "exports": {"./package.json": "./package.json", ".": {"require": "./index.cjs", "import": "./index.js"}}, "author": {"name": "<PERSON>"}, "keywords": ["terminal", "styles", "color", "ansi"], "scripts": {"test": "c8 twist tests/*.js", "build": "npx rollup --format cjs --input index.js --file index.cjs", "deploy": "npm test && git commit --all --message $tag && git tag --sign $tag --message $tag && git push && git push --tags", "release": "tag=$npm_package_version npm run deploy && npm publish --access public", "prepare": "npm run build"}, "devDependencies": {"c8": "*", "twist": "*"}, "gitHead": "****************************************", "bugs": {"url": "https://github.com/jorgebucaran/colorette/issues"}, "homepage": "https://github.com/jorgebucaran/colorette#readme", "_id": "colorette@2.0.9", "_nodeVersion": "16.9.1", "_npmVersion": "7.21.1", "dist": {"integrity": "sha512-OR951RpKsI5xzf1F2IyxylkqP6Ox3IxlLPNu6PRx4GTrcpTbd59RgMOwLmqqa6j5yTF20n/FiEfhRayKHnPOnw==", "shasum": "d2b6597073ec5accc686caaf2b7e35cd56b35189", "tarball": "https://registry.npmjs.org/colorette/-/colorette-2.0.9.tgz", "fileCount": 6, "unpackedSize": 14966, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCOdmzxWH0WxSZMyZqGWqP7Q69qH0ehLaD4FVpH0sbRxgIhALq00/rROL4XRUy80nZtKjcQzJl4YdJN9nH8QRHnFM/7"}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/colorette_2.0.9_1632504808785_0.18877434548867833"}, "_hasShrinkwrap": false}, "2.0.10": {"name": "colorette", "version": "2.0.10", "type": "module", "main": "index.cjs", "module": "index.js", "types": "index.d.ts", "description": "Easily set your terminal text color & styles.", "repository": {"type": "git", "url": "git+https://github.com/jorgebucaran/colorette.git"}, "license": "MIT", "exports": {"./package.json": "./package.json", ".": {"require": "./index.cjs", "import": "./index.js"}}, "author": {"name": "<PERSON>"}, "keywords": ["terminal", "styles", "color", "ansi"], "scripts": {"test": "c8 twist tests/*.js", "build": "npx rollup --format cjs --input index.js --file index.cjs", "deploy": "npm test && git commit --all --message $tag && git tag --sign $tag --message $tag && git push && git push --tags", "release": "tag=$npm_package_version npm run deploy && npm publish --access public", "prepare": "npm run build"}, "devDependencies": {"c8": "*", "twist": "*"}, "gitHead": "****************************************", "bugs": {"url": "https://github.com/jorgebucaran/colorette/issues"}, "homepage": "https://github.com/jorgebucaran/colorette#readme", "_id": "colorette@2.0.10", "_nodeVersion": "16.9.1", "_npmVersion": "7.21.1", "dist": {"integrity": "sha512-tQ7Prvd+zU6EUsQVXJNqxJUZdZ4btI34jlp/W1N/bNyWsY55dxe2oSg+ss3COV4vKbWLVlwLJt5xMSCuZ3R4og==", "shasum": "0cc871faead8b07227c9ad784f27a0d71f3794e2", "tarball": "https://registry.npmjs.org/colorette/-/colorette-2.0.10.tgz", "fileCount": 6, "unpackedSize": 14961, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCNBWHOSJyDEQ8BfKx2M/fKNuGNOHK6E1ockHEPNM6jDQIgN/dxRLCmgQP165bK6/Zx/SytttDHtMnjw9J8t3EOQno="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/colorette_2.0.10_1632507074959_0.7896226925595644"}, "_hasShrinkwrap": false}, "2.0.11": {"name": "colorette", "version": "2.0.11", "type": "module", "main": "index.cjs", "module": "index.js", "types": "index.d.ts", "description": "Easily set your terminal text color & styles.", "repository": {"type": "git", "url": "git+https://github.com/jorgebucaran/colorette.git"}, "license": "MIT", "exports": {"./package.json": "./package.json", ".": {"require": "./index.cjs", "import": "./index.js"}}, "author": {"name": "<PERSON>"}, "keywords": ["terminal", "styles", "color", "ansi"], "scripts": {"test": "c8 twist tests/*.js", "build": "npx rollup --format cjs --input index.js --file index.cjs", "deploy": "npm test && git commit --all --message $tag && git tag --sign $tag --message $tag && git push && git push --tags", "release": "tag=$npm_package_version npm run deploy && npm publish --access public", "prepare": "npm run build"}, "devDependencies": {"c8": "*", "twist": "*"}, "gitHead": "****************************************", "bugs": {"url": "https://github.com/jorgebucaran/colorette/issues"}, "homepage": "https://github.com/jorgebucaran/colorette#readme", "_id": "colorette@2.0.11", "_nodeVersion": "16.9.1", "_npmVersion": "7.21.1", "dist": {"integrity": "sha512-ZqwF8QRKzkhpr6aOvqzsZgtdOh+ItZBrbHDJ3pEAOoLr79oVSRxviVKDHVvrBmUhK5NcGRT9RATQknOan52UXQ==", "shasum": "3b4cf8407edb24dd54fdbfd350f5ed252abbc64a", "tarball": "https://registry.npmjs.org/colorette/-/colorette-2.0.11.tgz", "fileCount": 6, "unpackedSize": 14977, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCjm3iHl564WMOVSiKOjaL7bAR+/ZaYBpN2uIiSlcji9AIhAO0mQv0OdhFagULb5rvO4aEv2KPbkKCxP4RU1jLzUOzS"}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/colorette_2.0.11_1632606592853_0.2574142711978873"}, "_hasShrinkwrap": false}, "2.0.12": {"name": "colorette", "version": "2.0.12", "type": "module", "main": "index.cjs", "module": "index.js", "types": "index.d.ts", "description": "Easily set your terminal text color & styles.", "repository": {"type": "git", "url": "git+https://github.com/jorgebucaran/colorette.git"}, "license": "MIT", "exports": {"./package.json": "./package.json", ".": {"require": "./index.cjs", "import": "./index.js"}}, "author": {"name": "<PERSON>"}, "keywords": ["terminal", "styles", "color", "ansi"], "scripts": {"test": "c8 twist tests/*.js", "build": "npx rollup --format cjs --input index.js --file index.cjs", "deploy": "npm test && git commit --all --message $tag && git tag --sign $tag --message $tag && git push && git push --tags", "release": "tag=$npm_package_version npm run deploy && npm publish --access public", "prepare": "npm run build"}, "devDependencies": {"c8": "*", "twist": "*"}, "gitHead": "****************************************", "bugs": {"url": "https://github.com/jorgebucaran/colorette/issues"}, "homepage": "https://github.com/jorgebucaran/colorette#readme", "_id": "colorette@2.0.12", "_nodeVersion": "16.9.1", "_npmVersion": "7.21.1", "dist": {"integrity": "sha512-lHID0PU+NtFzeNCwTL6JzUKdb6kDpyEjrwTD1H0cDZswTbsjLh2wTV2Eo2sNZLc0oSg0a5W1AI4Nj7bX4iIdjA==", "shasum": "7938ab254e7bb1bba29b0fd1b4cc168889ca4d74", "tarball": "https://registry.npmjs.org/colorette/-/colorette-2.0.12.tgz", "fileCount": 6, "unpackedSize": 14977, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCgA1cnpanlOI/HwO9HVQET2+EVeIJ8I5uvLolQ1nOorAIgGefFvm6qUh3tbLWVyhudrA5kOmWaVTX3tve1ASnDSO4="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/colorette_2.0.12_1632650654063_0.7977538098673309"}, "_hasShrinkwrap": false}, "2.0.13": {"name": "colorette", "version": "2.0.13", "type": "module", "main": "index.cjs", "module": "index.js", "types": "index.d.ts", "description": "Easily set your terminal text color & styles.", "repository": {"type": "git", "url": "git+https://github.com/jorgebucaran/colorette.git"}, "license": "MIT", "exports": {"./package.json": "./package.json", ".": {"require": "./index.cjs", "import": "./index.js"}}, "author": {"name": "<PERSON>"}, "keywords": ["terminal", "styles", "color", "ansi"], "scripts": {"test": "c8 twist tests/*.js", "build": "npx rollup --format cjs --input index.js --file index.cjs", "deploy": "npm test && git commit --all --message $tag && git tag --sign $tag --message $tag && git push && git push --tags", "release": "tag=$npm_package_version npm run deploy && npm publish --access public", "prepare": "npm run build"}, "devDependencies": {"c8": "*", "twist": "*"}, "gitHead": "****************************************", "bugs": {"url": "https://github.com/jorgebucaran/colorette/issues"}, "homepage": "https://github.com/jorgebucaran/colorette#readme", "_id": "colorette@2.0.13", "_nodeVersion": "16.10.0", "_npmVersion": "7.24.0", "dist": {"integrity": "sha512-lvA4NbohpqUypdfTtJpb5BwhdUrwi1ACLM6uW3lEj0CWKOXrCSJlexv9IgUUN6obat0YGTSy7wfLDLEfOvzFLA==", "shasum": "c8d3c7a1b57fbddb0b6b681ee51f8105c4e86eaf", "tarball": "https://registry.npmjs.org/colorette/-/colorette-2.0.13.tgz", "fileCount": 6, "unpackedSize": 15018, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC8aaIF896+1bDtJSF2RBNM4roLgytrzvqKuZ2gQ01rvgIhAJ5beaWS4jCu5deuI13q3B4rfxlCUan9ZhOcn+qn2jol"}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/colorette_2.0.13_1633094469293_0.7534242613835038"}, "_hasShrinkwrap": false}, "2.0.14": {"name": "colorette", "version": "2.0.14", "type": "module", "main": "index.cjs", "module": "index.js", "types": "index.d.ts", "description": "Easily set your terminal text color & styles.", "repository": {"type": "git", "url": "git+https://github.com/jorgebucaran/colorette.git"}, "license": "MIT", "exports": {"./package.json": "./package.json", ".": {"require": "./index.cjs", "import": "./index.js"}}, "author": {"name": "<PERSON>"}, "keywords": ["terminal", "styles", "color", "ansi"], "scripts": {"test": "c8 twist tests/*.js", "build": "npx rollup --format cjs --input index.js --file index.cjs", "deploy": "npm test && git commit --all --message $tag && git tag --sign $tag --message $tag && git push && git push --tags", "release": "tag=$npm_package_version npm run deploy && npm publish --access public", "prepare": "npm run build"}, "devDependencies": {"c8": "*", "twist": "*"}, "gitHead": "****************************************", "bugs": {"url": "https://github.com/jorgebucaran/colorette/issues"}, "homepage": "https://github.com/jorgebucaran/colorette#readme", "_id": "colorette@2.0.14", "_nodeVersion": "16.10.0", "_npmVersion": "7.24.0", "dist": {"integrity": "sha512-TL<PERSON>0rCLNjDIdKGLGqMtPEAOAZmavC1QCX4mEs3P0mrA/DDoU/tA+Y4UQK/862FkX2TTlbyVIkREZNbf7Y9YwA==", "shasum": "1629bb27a13cd719ff37d66bc341234af564122e", "tarball": "https://registry.npmjs.org/colorette/-/colorette-2.0.14.tgz", "fileCount": 6, "unpackedSize": 16400, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD0QangOISM+Rm4ZhQzRuQ2Lnprs0deVT1qOm/RT7ksZgIhAMdZiNAdK6SgkXaHs8JZ44qIH41tTeaqSJ0Web0IOcK9"}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/colorette_2.0.14_1633259359210_0.94652537113073"}, "_hasShrinkwrap": false}, "2.0.15": {"name": "colorette", "version": "2.0.15", "type": "module", "main": "index.cjs", "module": "index.js", "types": "index.d.ts", "description": "Easily set your terminal text color & styles.", "repository": {"type": "git", "url": "git+https://github.com/jorgebucaran/colorette.git"}, "license": "MIT", "exports": {"./package.json": "./package.json", ".": {"require": "./index.cjs", "import": "./index.js"}}, "author": {"name": "<PERSON>"}, "keywords": ["terminal", "styles", "color", "ansi"], "scripts": {"test": "c8 twist tests/*.js", "build": "npx rollup --format cjs --input index.js --file index.cjs", "deploy": "npm test && git commit --all --message $tag && git tag --sign $tag --message $tag && git push && git push --tags", "release": "tag=$npm_package_version npm run deploy && npm publish --access public", "prepare": "npm run build"}, "devDependencies": {"c8": "*", "twist": "*"}, "gitHead": "****************************************", "bugs": {"url": "https://github.com/jorgebucaran/colorette/issues"}, "homepage": "https://github.com/jorgebucaran/colorette#readme", "_id": "colorette@2.0.15", "_nodeVersion": "16.10.0", "_npmVersion": "7.24.0", "dist": {"integrity": "sha512-lIFQhufWaVvwi4wOlX9Gx5b0Nmw3XAZ8HzHNH9dfxhe+JaKNTmX6QLk4o7UHyI+tUY8ClvyfaHUm5bf61O3psA==", "shasum": "8e634aa0429b110d24be82eac4d42f5ea65ab2d5", "tarball": "https://registry.npmjs.org/colorette/-/colorette-2.0.15.tgz", "fileCount": 6, "unpackedSize": 16649, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDIYZtApZwaqFFqfsMgm1bXq2KWJgR9V591omJDoKWDWQIgZtsF4RYD7ysF66hPBxCzf9eG/DvVEGUbwvMGsYhLGps="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/colorette_2.0.15_1633600852180_0.6129050760419383"}, "_hasShrinkwrap": false}, "2.0.16": {"name": "colorette", "version": "2.0.16", "type": "module", "main": "index.cjs", "module": "index.js", "types": "index.d.ts", "description": "🌈Easily set your terminal text color & styles.", "repository": {"type": "git", "url": "git+https://github.com/jorgebucaran/colorette.git"}, "license": "MIT", "exports": {"./package.json": "./package.json", ".": {"require": "./index.cjs", "import": "./index.js"}}, "author": {"name": "<PERSON>"}, "keywords": ["terminal", "styles", "color", "ansi"], "scripts": {"test": "c8 twist tests/*.js", "build": "npx rollup --format cjs --input index.js --file index.cjs", "deploy": "npm test && git commit --all --message $tag && git tag --sign $tag --message $tag && git push && git push --tags", "release": "tag=$npm_package_version npm run deploy && npm publish --access public", "prepare": "npm run build"}, "devDependencies": {"c8": "*", "twist": "*"}, "gitHead": "****************************************", "bugs": {"url": "https://github.com/jorgebucaran/colorette/issues"}, "homepage": "https://github.com/jorgebucaran/colorette#readme", "_id": "colorette@2.0.16", "_nodeVersion": "16.10.0", "_npmVersion": "7.24.0", "dist": {"integrity": "sha512-hUewv7oMjCp+wkBv5Rm0v87eJhq4woh5rSR+42YSQJKecCqgIqNkZ6lAlQms/BwHPJA5NKMRlpxPRv0n8HQW6g==", "shasum": "713b9af84fdb000139f04546bd4a93f62a5085da", "tarball": "https://registry.npmjs.org/colorette/-/colorette-2.0.16.tgz", "fileCount": 6, "unpackedSize": 16846, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2mbgCRA9TVsSAnZWagAAkDYQAKQlFHIyvZCXvjTmPc03\nh1U+qI7oBeikvzJOtXpLfnfNgyvTLzUubxYaE/VV3jBdP5sTaCca6jKAYZO4\npo7xo9VRhHQeOyZbJibIxMlEJXKpQdC2oR897jXAxE2tgyrNDcXlSurZxTV2\njdMUVUxgGO0FN7KgE7Q/2EEUeeDysvXAMnLlc9x5wAO1jxY5FBeH/2t8DN06\npK1euMpXxrL0ZVenHv+1A8YPa8mnmUk5iGYElmNvkhJYdvObujaqcnY8NRaZ\nuPLq0m1IG9WmDjQ3Vsh1xrEQNlEw6Sw6xvr5Mu/Dt8S0shu5Ke1PodH/zYz7\ntw0jNmDq5I2v6F9LfqGZBKkF3rY1J45dSP/ME7t0tyx2QBLfBYBsAgTErPAI\nAEfrpaVI2gOrnY3kw2MDRDdB9JKRnpEwykwC6yXsvMFAsv8ZakF6bgRggAfh\nDXmovbPAVIvmc0iweuL3a3Gd+j5faoqL05iVZwCA44n0wWjewTrdpCFVJ7sr\nQfGfcRkSlHMSwu7bVL/hNq1lvIb4wiO3gBZko5i+ifvEszpEKKFtBZhEC7S2\nyArNt7Zh3hpFrW9eRT5sCfkCuv4OnhK6mhEZzuFucZoVD+r/jzdxPnVxertd\nnO4UAb8HVrLY5iqwd+1/AkyF0w0AQ0Esnt1zcyS26GZC8GqGb9+i6839RV4z\nTi4D\r\n=cqbf\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIH+Br3LEu6XtnUZAcWOuli5jR7F6xIjJ0/VTnFkjvzvaAiEAlUbebwE6xgqeylMCV+W/pIxtPdVU96LHepcWDlGkJQg="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/colorette_2.0.16_1633672886583_0.40802830671485246"}, "_hasShrinkwrap": false}, "2.0.17": {"name": "colorette", "version": "2.0.17", "type": "module", "main": "index.cjs", "module": "index.js", "types": "index.d.ts", "description": "🌈Easily set your terminal text color & styles.", "repository": {"type": "git", "url": "git+https://github.com/jorgebucaran/colorette.git"}, "license": "MIT", "exports": {"./package.json": "./package.json", ".": {"require": "./index.cjs", "import": "./index.js"}}, "author": {"name": "<PERSON>"}, "keywords": ["terminal", "styles", "color", "ansi"], "scripts": {"test": "c8 twist tests/*.js", "build": "npx rollup --format cjs --input index.js --file index.cjs", "deploy": "npm test && git commit --all --message $tag && git tag --sign $tag --message $tag && git push && git push --tags", "release": "tag=$npm_package_version npm run deploy && npm publish --access public", "prepare": "npm run build"}, "devDependencies": {"c8": "*", "twist": "*"}, "gitHead": "****************************************", "bugs": {"url": "https://github.com/jorgebucaran/colorette/issues"}, "homepage": "https://github.com/jorgebucaran/colorette#readme", "_id": "colorette@2.0.17", "_nodeVersion": "12.22.12", "_npmVersion": "6.14.16", "dist": {"integrity": "sha512-hJo+3Bkn0NCHybn9Tu35fIeoOKGOk5OCC32y4Hz2It+qlCO2Q3DeQ1hRn/tDDMQKRYUEzqsl7jbF6dYKjlE60g==", "shasum": "5dd4c0d15e2984b7433cb4a9f2ead45063b80c47", "tarball": "https://registry.npmjs.org/colorette/-/colorette-2.0.17.tgz", "fileCount": 7, "unpackedSize": 37583, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCuCbpbYHvn1TD93PizxtH9ECPS2TS647aiw3up89LjvQIgSgaxMwxmAJeOVatzIcc4Ws6k2jnSglz8aAyWfElQcn8="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJimho3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrYQg/5AGrGBeewtIVbI2Xy0KLYxgP8VCiDiJPQPqdJ70U18/RcdCMV\r\nbC5b9uyjW9d3eFIf8uo052a6LsTuk+XRRoRQvA48T0vzY+LsXAbBThOu/xPF\r\noZRx4vGDd6fKeKxJfMGd1LH+GzkO6woxWj2MgeAf8Au5LtwgisdsX+MsgrDk\r\nFrN1DUs2CABCMfduIjowHOX+w0rCLfjtgq3oWxoFR3xc+EdHRzKV4P6RwVpi\r\nXROwLnMR9lTIFXMjtzmWB793Ns7EiRj5rt7bZMGXzUS37e17wc/OX6yTL57l\r\niOaMezreIgeb9kg4XnfxNTGSoPmaIst2+whjYobVjLke2sjYa07hxZkige6/\r\nagFFJI70TgiYGPjlrwJRRehyAWI2oh4UolaqcF0BTp9NYay8EhruwiHXTSX8\r\nATXiopxB2vsSsE4RNUG9IrqNGn7ryOsY475/6vG5FGVYF3+/fjsx+pKaxOt5\r\nrwhTxXb3G0SpOBCgItCnw5Ds5wypgi7Knd9lmuWazS56GdCBEp7W2J5D/2Jz\r\n2MNXgpxfU+51kr4GPd7zDQUPWqStNultU0Ih0FoaHsBdhFD06UX992jOqHo4\r\nzJ01xESm0drwbnkelaMc67U4eHOT3KM2bAMiJjcWB9UT83raC8q5/JbgPWNW\r\nwdRg5wpDS1KVmHhP0EOEfzu1NOiAWCM2j1c=\r\n=dI7b\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/colorette_2.0.17_1654266423314_0.04010143041645131"}, "_hasShrinkwrap": false}, "2.0.18": {"name": "colorette", "version": "2.0.18", "type": "module", "main": "index.cjs", "module": "index.js", "types": "index.d.ts", "description": "🌈Easily set your terminal text color & styles.", "repository": {"type": "git", "url": "git+https://github.com/jorgebucaran/colorette.git"}, "license": "MIT", "exports": {"./package.json": "./package.json", ".": {"require": "./index.cjs", "import": "./index.js"}}, "author": {"name": "<PERSON>"}, "keywords": ["terminal", "styles", "color", "ansi"], "scripts": {"test": "c8 twist tests/*.js", "build": "npx rollup --format cjs --input index.js --file index.cjs", "deploy": "npm test && git commit --all --message $tag && git tag --sign $tag --message $tag && git push && git push --tags", "release": "tag=$npm_package_version npm run deploy && npm publish --access public", "prepare": "npm run build"}, "devDependencies": {"c8": "*", "twist": "*"}, "gitHead": "****************************************", "bugs": {"url": "https://github.com/jorgebucaran/colorette/issues"}, "homepage": "https://github.com/jorgebucaran/colorette#readme", "_id": "colorette@2.0.18", "_nodeVersion": "16.15.1", "_npmVersion": "8.11.0", "dist": {"integrity": "sha512-rHDY1i4V4JBCXHnHwaVyA202CKSj2kUrjI5cSJQbTdnFeI4ShV3e19Fe7EQfzL2tjSrvYyWugdGAtEc1lLvGDg==", "shasum": "4c260bcf86437ce94fa58e2e49a83b623f3c4d66", "tarball": "https://registry.npmjs.org/colorette/-/colorette-2.0.18.tgz", "fileCount": 6, "unpackedSize": 16934, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDAHfvY5peJTGKn0nn60Q37bF9nW9D8woS1ezVulS91iwIhAOer6tO8AvhnDDccO18Il1Mm4sEGw9LfuCI9K0ArRcKV"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJip+4PACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpl+A/9HWvqePlH/vxFE7Wsg5R0g8b4KHT2tN2Sb3BQmVj+kxGIBTR9\r\n6THAIvZ/Wd8P/4TGR+T9yCph/0IEEzNmuy0hk/nM4h76mMDMIucs/mEvF3MT\r\nq3PdQIFFHsU+OpHGSy7gJBL1yU7BGcsZvry9tZKnGlDyVj/oeI6+NkNHSvHs\r\nXaSGCxPdzwvnoJX7PktbjAkMr8+80/DhMDXJACfkKlLhIruThrzAl2d9yQ89\r\nukBv7QzTXx/yNgTcOjj+gD6I24moFxbUpTpFBmqgFpvKcIvFAbPY1WDr4f9e\r\nikw6pl9QD6nkeOHIblCF4vzXf7VJG5X5kWHXjnuGDNjYK9w+n7Oj+ngJaxlN\r\nk9lEBFBiMWedYDZs6xvxhpaeOxFtm6eikGi6kHA/QGQwNp/fMf4f/tFVH4xt\r\n6qIn4zrVOOdA+HvDefc9IZC6x2LQ56lICnkQGcT8iRjG4LY5qYl3CYTyOmem\r\nakccnXwJ3CBxWjJLzb/ZNANvyJhHim1+GVBNQBayGt8v+RXvOSbhKj2yRIod\r\ncqcBvR/noOFNUF1lIhBCHjE7SzWqWlanDcrACsnR2l0L5eJfczGaWUB1P2X1\r\nIar3f2Rh191juACSTrqAl+s0DZO9as+dtm76n7UO2ACnly5ZhJsVVYnzpctB\r\nWA+rL5LTJJBUCkI9UeKrPRufX2Fgb25oX7A=\r\n=6KJ6\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/colorette_2.0.18_1655172622813_0.5446029287888083"}, "_hasShrinkwrap": false}, "2.0.19": {"name": "colorette", "version": "2.0.19", "type": "module", "main": "index.cjs", "module": "index.js", "types": "index.d.ts", "description": "🌈Easily set your terminal text color & styles.", "repository": {"type": "git", "url": "git+https://github.com/jorgebucaran/colorette.git"}, "license": "MIT", "exports": {"./package.json": "./package.json", ".": {"require": "./index.cjs", "import": "./index.js"}}, "author": {"name": "<PERSON>"}, "keywords": ["terminal", "styles", "color", "ansi"], "scripts": {"test": "c8 twist tests/*.js", "build": "npx rollup --format cjs --input index.js --file index.cjs", "deploy": "npm test && git commit --all --message $tag && git tag --sign $tag --message $tag && git push && git push --tags", "release": "tag=$npm_package_version npm run deploy && npm publish --access public", "prepare": "npm run build"}, "devDependencies": {"c8": "*", "twist": "*"}, "gitHead": "****************************************", "bugs": {"url": "https://github.com/jorgebucaran/colorette/issues"}, "homepage": "https://github.com/jorgebucaran/colorette#readme", "_id": "colorette@2.0.19", "_nodeVersion": "16.15.1", "_npmVersion": "8.11.0", "dist": {"integrity": "sha512-3tlv/dIP7FWvj3BsbHrGLJ6l/oKh1O3TcgBqMn+yyCagOxc23fyzDS6HypQbgxWbkpDnf52p1LuR4eWDQ/K9WQ==", "shasum": "cdf044f47ad41a0f4b56b3a0d5b4e6e1a2d5a798", "tarball": "https://registry.npmjs.org/colorette/-/colorette-2.0.19.tgz", "fileCount": 6, "unpackedSize": 16978, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHZml67O7FW1xau0t235H/FKp+VKlpzkMpq2+pRG66onAiEAnU+D1S8HCmusyFRnFCmOJ3fG7rjOSCHSvyJqeXh2DXc="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiqKQXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpgQw//fjJWVqEzF9kluNAslsDm4sDwbrfAfVQwoav8jIh6zSwwEUIf\r\nT+gkzOACiXp6E0GVvenc4uO4EwGSo/jdcqeUysZ5fQ3XtxpzTwmlnkknrEM0\r\nGTB7zgefCdVOQAmgSlYZl5+53HK8CZLjVDTUFrb/hoqRcWrkqp0fHHOJ13q6\r\nAWlCqnQdBWL+C56HFarfk5yTSkflFTU1slwtv2aFvDLkqg7PuCiiMOh6f71w\r\nzUQhiyGGmzwMw5pyHmi2nwyTaLcH3Rh13ek8azpdvv8MXoqb000ML+Hz0RDi\r\nv/ld7XJbbPgmazkXiSge5VSsFAvfaobf5gtHz4lmU3eFf1MXTherahmTrM5K\r\nnaC2bxh4nC9kIM/EebVbHps69bGiSWnHr338pqBI1z45SaNLV9CpiDzGpogb\r\nIVmQfiZ6+LEIPCl5lW6DepeJMtsq9SjIcWlSgWi+QQdk+rwmDvjrMCOxyECR\r\nL24i62hYyF6OJxbYLe7AjQnymZKl7OKRMoTZGZ7Fv3yOJ61Z88I1CiYusEBV\r\nqG9780IgvgYa1klZrl1wj1sqgIV02ECCtCmhjs55zkO5s9OjzB6AElHS9jI3\r\nszsMyWkZTvyOaQQG4tidgMxHilPqCLSO3hIWyc6E5J4x9DiY9spjQIv+TKKz\r\nIEQL4UVk+hl/DHo0jdA2XQ1CNOuPoICpIPg=\r\n=u6e5\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/colorette_2.0.19_1655219222935_0.5265743969691066"}, "_hasShrinkwrap": false}, "2.0.20": {"name": "colorette", "version": "2.0.20", "type": "module", "main": "index.cjs", "module": "index.js", "types": "index.d.ts", "description": "🌈Easily set your terminal text color & styles.", "repository": {"type": "git", "url": "git+https://github.com/jorgebucaran/colorette.git"}, "license": "MIT", "exports": {"./package.json": "./package.json", ".": {"require": "./index.cjs", "import": "./index.js", "types": "./index.d.ts"}}, "author": {"name": "<PERSON>"}, "keywords": ["terminal", "styles", "color", "ansi"], "scripts": {"test": "c8 twist tests/*.js", "build": "npx rollup --format cjs --input index.js --file index.cjs", "deploy": "npm test && git commit --all --message $tag && git tag --sign $tag --message $tag && git push && git push --tags", "release": "tag=$npm_package_version npm run deploy && npm publish --access public", "prepare": "npm run build"}, "devDependencies": {"c8": "*", "twist": "*"}, "gitHead": "****************************************", "bugs": {"url": "https://github.com/jorgebucaran/colorette/issues"}, "homepage": "https://github.com/jorgebucaran/colorette#readme", "_id": "colorette@2.0.20", "_nodeVersion": "18.3.0", "_npmVersion": "8.11.0", "dist": {"integrity": "sha512-IfEDxwoWIjkeXL1eXcDiow4UbKjhLdq6/EuSVR9GMN7KVH3r9gQ83e73hsz1Nd1T3ijd5xv1wcWRYO+D6kCI2w==", "shasum": "9eb793e6833067f7235902fcd3b09917a000a95a", "tarball": "https://registry.npmjs.org/colorette/-/colorette-2.0.20.tgz", "fileCount": 6, "unpackedSize": 17009, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFUfwPC3mwnqYvSzfP68c8w9jezYBuJPlMNeOB7CiXpgAiBa4w4WRtRhBdwAVvDgrbHeU9ERO9yVuc6DSCgjccPDnQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkPBVxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpJ4A/9FnaQincr7/qx7jjb1crfUP7cJEwQcP2yvGCON638vi0A1F+1\r\ns/jYSVPw9vPnxJ2ddUeGqJip+BnsX84hzDe/zt3CI1QWvPm/OqPebSltRbCX\r\n+0EKQcdbJq8KBQouobRwrgnn3KaX3OP1YH/osXoKYoxrlBEnl9Imi3/9vZpF\r\nn7t1I/Ft9iChQ/U1NgnYvcl5mXESGqEn7SyS1WpFhT3uKxBNV50stMBll5aR\r\nYydy2631czeETMjOsxnmUT645ZyHNRJ2IqO/L9W1e5xqTt/UhigkQ06rguSs\r\n3jx8fJBBq4ynd5o3NWY7TiN9aEc7W8lqeZXL//Cd2wiUNDecTA0v9MaUuyGj\r\nZHB1yXJBxiUwukHXLjqpDVfhi8mSxkfFyYNacsD2kHb2Rgq4G6QFNWEIVnv8\r\nWEMbSOQyz2k9k1lZZ6c7qL1RB2lNSHYb5cGCVT9DWBMyYVsRUAkYo8L9VuGC\r\n2b8mC17AuCikKusFsjB35Uqa0rNECnHlrXmM7JRfmAYwRgsr2c3hiWiH4BFl\r\nzoaslclEXhTOMhDjcSLJtnoXq/imuhQcqerbK5xhDT3CZCidZaqOrodkq1m3\r\nhk/PhCy93nk4oKFc1fFySfSgu9JeEIkuSPFy5To5n18svOApMv/2NY+s7Daf\r\n/AFK56d0W1tBz2QgfWljjZdcerkpPioGPbM=\r\n=bGjK\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/colorette_2.0.20_1681659249742_0.190631151463021"}, "_hasShrinkwrap": false}}, "time": {"created": "2018-08-23T01:12:46.376Z", "0.1.0": "2018-08-23T01:12:46.756Z", "modified": "2023-04-16T15:34:10.025Z", "1.0.0": "2018-08-23T13:51:05.633Z", "1.0.1": "2018-08-24T08:44:20.089Z", "1.0.2": "2018-09-23T03:12:19.515Z", "1.0.3": "2018-09-23T07:26:45.777Z", "1.0.4": "2018-09-23T07:34:40.316Z", "1.0.5": "2018-09-30T07:50:16.825Z", "1.0.6": "2018-10-16T09:43:55.894Z", "1.0.7": "2018-10-23T10:12:17.552Z", "1.0.8": "2019-05-31T14:40:21.787Z", "1.1.0": "2019-06-28T23:46:15.543Z", "1.2.0": "2020-05-16T18:24:54.376Z", "1.2.1": "2020-07-05T13:33:18.271Z", "1.2.2": "2021-02-25T13:26:14.581Z", "1.3.0": "2021-08-10T12:55:45.383Z", "1.4.0": "2021-09-07T17:06:15.141Z", "2.0.0": "2021-09-17T13:23:49.289Z", "2.0.1": "2021-09-18T10:47:16.796Z", "2.0.2": "2021-09-18T11:33:50.414Z", "2.0.3": "2021-09-21T15:44:22.801Z", "2.0.4": "2021-09-21T16:44:28.396Z", "2.0.5": "2021-09-21T17:55:28.709Z", "2.0.6": "2021-09-22T12:27:45.345Z", "2.0.7": "2021-09-22T13:40:29.720Z", "2.0.8": "2021-09-23T14:42:21.096Z", "2.0.9": "2021-09-24T17:33:28.960Z", "2.0.10": "2021-09-24T18:11:15.375Z", "2.0.11": "2021-09-25T21:49:53.006Z", "2.0.12": "2021-09-26T10:04:14.202Z", "2.0.13": "2021-10-01T13:21:09.499Z", "2.0.14": "2021-10-03T11:09:19.358Z", "2.0.15": "2021-10-07T10:00:52.326Z", "2.0.16": "2021-10-08T06:01:26.703Z", "2.0.17": "2022-06-03T14:27:03.504Z", "2.0.18": "2022-06-14T02:10:23.005Z", "2.0.19": "2022-06-14T15:07:03.210Z", "2.0.20": "2023-04-16T15:34:09.905Z"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "🌈Easily set your terminal text color & styles.", "homepage": "https://github.com/jorgebucaran/colorette#readme", "keywords": ["terminal", "styles", "color", "ansi"], "repository": {"type": "git", "url": "git+https://github.com/jorgebucaran/colorette.git"}, "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/jorgebucaran/colorette/issues"}, "license": "MIT", "readme": "# 🌈Colorette\n\n> Easily set your terminal text color & styles.\n\n- No dependecies\n- Automatic color support detection\n- Up to [2x faster](#benchmarks) than alternatives\n- TypeScript support\n- [`NO_COLOR`](https://no-color.org) friendly\n- Node >= `10`\n\n> [**Upgrading from Colorette `1.x`?**](https://github.com/jorgebucaran/colorette/issues/70)\n\n## Quickstart\n\n```js\nimport { blue, bold, underline } from \"colorette\"\n\nconsole.log(\n  blue(\"I'm blue\"),\n  bold(blue(\"da ba dee\")),\n  underline(bold(blue(\"da ba daa\")))\n)\n```\n\nHere's an example using [template literals](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Template_literals).\n\n```js\nconsole.log(`\n  There's a ${underline(blue(\"house\"))},\n  With a ${bold(blue(\"window\"))},\n  And a ${blue(\"corvette\")}\n  And everything is blue\n`)\n```\n\nYou can also nest styles without breaking existing color sequences.\n\n```js\nconsole.log(bold(`I'm ${blue(`da ba ${underline(\"dee\")} da ba`)} daa`))\n```\n\nNeed to override terminal color detection? You can do that too.\n\n```js\nimport { createColors } from \"colorette\"\n\nconst { blue } = createColors({ useColor: false })\n\nconsole.log(blue(\"Blue? Nope, nah\"))\n```\n\n## Installation\n\n```console\nnpm install colorette\n```\n\n## API\n\n### \\<color\\>()\n\n> See all [supported colors](#supported-colors).\n\n```js\nimport { blue } from \"colorette\"\n\nblue(\"I'm blue\") //=> \\x1b[34mI'm blue\\x1b[39m\n```\n\n### createColors()\n\nOverride terminal color detection via `createColors({ useColor })`.\n\n```js\nimport { createColors } from \"colorette\"\n\nconst { blue } = createColors({ useColor: false })\n```\n\n### isColorSupported\n\n`true` if your terminal supports color, `false` otherwise. Used internally, but exposed for convenience.\n\n## Environment\n\nYou can override color detection from the CLI by setting the `--no-color` or `--color` flags.\n\n```console\n$ ./example.js --no-color | ./consumer.js\n```\n\nOr if you can't use CLI flags, by setting the `NO_COLOR=` or `FORCE_COLOR=` environment variables.\n\n```console\n$ NO_COLOR= ./example.js | ./consumer.js\n```\n\n## Supported colors\n\n| Colors  | Background Colors | Bright Colors | Bright Background Colors | Modifiers         |\n| ------- | ----------------- | ------------- | ------------------------ | ----------------- |\n| black   | bgBlack           | blackBright   | bgBlackBright            | dim               |\n| red     | bgRed             | redBright     | bgRedBright              | **bold**          |\n| green   | bgGreen           | greenBright   | bgGreenBright            | hidden            |\n| yellow  | bgYellow          | yellowBright  | bgYellowBright           | _italic_          |\n| blue    | bgBlue            | blueBright    | bgBlueBright             | <u>underline</u>  |\n| magenta | bgMagenta         | magentaBright | bgMagentaBright          | ~~strikethrough~~ |\n| cyan    | bgCyan            | cyanBright    | bgCyanBright             | reset             |\n| white   | bgWhite           | whiteBright   | bgWhiteBright            |                   |\n| gray    |                   |               |                          |                   |\n\n## [Benchmarks](https://github.com/jorgebucaran/colorette/actions/workflows/bench.yml)\n\n```console\nnpm --prefix bench start\n```\n\n```diff\n  chalk         1,786,703 ops/sec\n  kleur         1,618,960 ops/sec\n  colors          646,823 ops/sec\n  ansi-colors     786,149 ops/sec\n  picocolors    2,871,758 ops/sec\n+ colorette     3,002,751 ops/sec\n```\n\n## Acknowledgments\n\nColorette started out in 2015 by [@jorgebucaran](https://github.com/jorgebucaran) as a lightweight alternative to [Chalk](https://github.com/chalk/chalk) and was introduced originally as [Clor](https://github.com/jorgebucaran/colorette/commit/b01b5b9961ceb7df878583a3002e836fae9e37ce). Our terminal color detection logic borrows heavily from [@sindresorhus](https://github.com/sindresorhus) and [@Qix-](https://github.com/Qix-) work on Chalk. The idea of slicing strings to clear bleeding sequences was adapted from a similar technique used by [@alexeyraspopov](https://github.com/alexeyraspopov) in [picocolors](https://github.com/alexeyraspopov/picocolors). Thank you to all our contributors! <3\n\n## License\n\n[MIT](LICENSE.md)\n", "readmeFilename": "README.md", "users": {"icodeforcookies": true, "flumpus-dev": true}}