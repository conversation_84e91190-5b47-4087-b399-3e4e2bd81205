{"name": "@tailwindcss/vite", "dist-tags": {"internal": "0.0.0-internal.b2586d4e", "next": "4.0.0", "latest": "4.1.10", "insiders": "0.0.0-insiders.c5a997c"}, "versions": {"0.0.0-development.1": {"name": "@tailwindcss/vite", "version": "0.0.0-development.1", "dependencies": {"tailwindcss": "0.0.0-development.1", "@tailwindcss/oxide": "0.0.0-development.1"}, "dist": {"shasum": "ff4b8da03ee178910fcf1d7c00d59496f1012a52", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-development.1.tgz", "fileCount": 3, "integrity": "sha512-W5fMXDcQwQQqHC6ywKhVoSgStHaN2cqtwypd5DSLCaLgkM5ZPS5iprt8n7z7ozkHuNj8lGX5eTJ5J2dWPzcvrw==", "signatures": [{"sig": "MEQCIAijS76/P8wOqz8tXi+TFmy4y0G2U77YU/BL6+tYYpIKAiBvdIeu+dVPPtDYZ/Lx/SLSMHi2BikmK6c7APjIqL0CIw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3619}}, "0.0.0-development.2": {"name": "@tailwindcss/vite", "version": "0.0.0-development.2", "dependencies": {"tailwindcss": "0.0.0-development.2", "@tailwindcss/oxide": "0.0.0-development.2"}, "dist": {"shasum": "f7020999f9d63397cdc778910b80e0dfb2da5360", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-development.2.tgz", "fileCount": 3, "integrity": "sha512-Xjc3g5sNXbCaK84wTKnEYOxLT8H1/VHVlBRd0cBQfsfZXxW2gwq5T7+Gg+IgockIqPUvqRIhAhHyrfOg05YXiQ==", "signatures": [{"sig": "MEUCIQDpt+Wi149VlUs6ZnDW+2USj0TImwczjfi0ci0zmrUVdQIgVc47IxrVLN6/1JFpyPH4KT/7irgTmacs5anWn7OnlTg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3595}}, "0.0.0-development.3": {"name": "@tailwindcss/vite", "version": "0.0.0-development.3", "dependencies": {"tailwindcss": "0.0.0-development.3", "@tailwindcss/oxide": "0.0.0-development.3"}, "dist": {"shasum": "77164910e965146399b9f98a82a0c2d6c392ee72", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-development.3.tgz", "fileCount": 3, "integrity": "sha512-YLMllLpBfVFOHEcp2haXul1JVV8+4GFhbxrEYaCHGToggZjo9Llcjot5rwH6d1jAdmJoEwde6dffb8qZ2V92vQ==", "signatures": [{"sig": "MEUCIQDrRKQhRK7DHgU0WsFWen7OlgzF8N/cm8HwuDEMa0UTKgIgQiKhW1PW+06SHOnX3ezAiSvj2J6wo/VIlPrInKjZG0E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3595}}, "0.0.0-development.4": {"name": "@tailwindcss/vite", "version": "0.0.0-development.4", "dependencies": {"tailwindcss": "^0.0.0-development.4", "@tailwindcss/oxide": "^0.0.0-development.4"}, "devDependencies": {"vite": "^5.0.11", "@types/node": "^20.11.17"}, "dist": {"shasum": "240574adbe20730ad5d6aef8cec552d7326d1d4b", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-development.4.tgz", "fileCount": 3, "integrity": "sha512-8FaGvkYD3WkFT3M3th65vpQIMlduX6IbVtf+zSn41+y3FYWl0BKoUC4c953EOWum+PkXNPKAP2QcodNzcBXGyw==", "signatures": [{"sig": "MEYCIQD8rIpy4kwtIRwjVNwWagJeH6FjJCDmqtRu1W60pX6MEQIhANocibNw8yVjs7rIThojnXSZOJbqw9xfoYRrWvV/axWe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3922}}, "0.0.0-development.5": {"name": "@tailwindcss/vite", "version": "0.0.0-development.5", "dependencies": {"tailwindcss": "^0.0.0-development.5", "@tailwindcss/oxide": "^0.0.0-development.5"}, "devDependencies": {"vite": "^5.0.11", "@types/node": "^20.11.17"}, "dist": {"shasum": "a328774256d29221bdc711b4edb5518b3a9e0cc1", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-development.5.tgz", "fileCount": 3, "integrity": "sha512-ye+NZs/Ms6+2zc1BnxhZt70F+qvJl1TWC56QfYdn+4K7P+Ycf24PpPNvWQ53HkfEmAvpwWENtYXfN8AFAaMXHg==", "signatures": [{"sig": "MEQCIFyYk0No6shrmP6GoL+bJafaoTZXck1Bf94gy2KkBa10AiBFMOBMT8nBjzUPqdeZ13EV08Yt9CI66Mh/zuQzJR6FxA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3922}}, "0.0.0-oxide.1": {"name": "@tailwindcss/vite", "version": "0.0.0-oxide.1", "dependencies": {"tailwindcss": "^0.0.0-oxide.1", "@tailwindcss/oxide": "^0.0.0-oxide.1"}, "devDependencies": {"vite": "^5.0.11", "@types/node": "^20.11.17"}, "dist": {"shasum": "b5bba60cb8c5da354dffb00e893ee4de8c846423", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-oxide.1.tgz", "fileCount": 3, "integrity": "sha512-5SSpXbqyIC+IAaCB2q3h0YLWyjqFgCCty4CK6AHLcGD9Nzfhwz5rBNXNzlx/G/5ikV4ljuriJmXCzS05cCd0Zw==", "signatures": [{"sig": "MEUCIQCL1OdMK500LRihCAOU6+3/0pCnECcrveIvqfMDHvMnTwIgVy6ArC79mb39PSOkrELnpu4kH9lng7QDlmfi7Ws/WsA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4936}}, "0.0.0-oxide.2": {"name": "@tailwindcss/vite", "version": "0.0.0-oxide.2", "dependencies": {"tailwindcss": "0.0.0-oxide.2", "@tailwindcss/oxide": "0.0.0-oxide.2"}, "devDependencies": {"vite": "^5.0.11", "@types/node": "^20.11.17"}, "dist": {"shasum": "7b157ed803a52d57adbb625eadb87dfd4ae1a6ed", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-oxide.2.tgz", "fileCount": 3, "integrity": "sha512-nB2MqNir06D3GXumduP4DUUNn4hcmaT5CkjA0J55N3+MviQkZeGdUxs2Y116wQFaSLh/yqWyiDDFQKhxEx2fNg==", "signatures": [{"sig": "MEYCIQCJKg/whuofWRBcxmpfx8BJ9aJqvXrPK+/S1bLDz4VODAIhAO97W4XFHrBXpyXMcm27IwlsCLG7QE8rQG3M5/rqKqH+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4934}}, "0.0.0-oxide.3": {"name": "@tailwindcss/vite", "version": "0.0.0-oxide.3", "dependencies": {"tailwindcss": "0.0.0-oxide.3", "@tailwindcss/oxide": "0.0.0-oxide.3"}, "devDependencies": {"vite": "^5.0.11", "@types/node": "^20.11.17"}, "dist": {"shasum": "8b039501d6c2fcf78ebef08d03195527bc1585e0", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-oxide.3.tgz", "fileCount": 3, "integrity": "sha512-afW1N8p6mU5uvk6Ups6NgqE0N0S6sIcOVx5MuEOeJFs0xgqcFr461hnqPn00+R45FzFckOE2qhp6fd4lPZN1DQ==", "signatures": [{"sig": "MEQCIEVJ0e8FHoqSOKsj6i4Q6pt2kxt0ctX/2ZkKblGI7ZZ1AiBVU9eFSdCT6d4zfadqfl4KxIssSozf/N7bgAxBhhDeiQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4845}}, "0.0.0-oxide.4": {"name": "@tailwindcss/vite", "version": "0.0.0-oxide.4", "dependencies": {"tailwindcss": "0.0.0-oxide.4", "@tailwindcss/oxide": "0.0.0-oxide.4"}, "devDependencies": {"vite": "^5.0.11", "@types/node": "^20.11.17"}, "dist": {"shasum": "28cb5d7378e5331f0e5f7bef94d3fe262fe19991", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-oxide.4.tgz", "fileCount": 3, "integrity": "sha512-a1sINk4cd9npMEZ4qDjm+YMLVEn6t2brahNOOdFV61MFiPGmQ7r/zvr6sR7bDBt+2G9KDm+XeXZWLxweSC0+Kg==", "signatures": [{"sig": "MEQCICmWs4Wjx0OUHaNknz8etDf8oU4vCA9XGvM1U/7JeDzhAiAgbUYk/4xxhL4EPoHPmrBl1vOBzJj3Sf/Ogzswt+qqxw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4845}}, "4.0.0-alpha.1": {"name": "@tailwindcss/vite", "version": "4.0.0-alpha.1", "dependencies": {"tailwindcss": "4.0.0-alpha.1", "@tailwindcss/oxide": "4.0.0-alpha.1"}, "devDependencies": {"vite": "^5.0.11", "@types/node": "^20.11.17"}, "dist": {"shasum": "43a066bba912e6f1e0ac33f9ca684a4a954dd4fe", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.0-alpha.1.tgz", "fileCount": 4, "integrity": "sha512-7QUbIim9HPONM98oZ0TGFgEcjKh+wXn/U9b9KzbQ6EyEUstv3kiAcoOVcE0DU44z2Bn6ZFKmvjLzsFVDzIS3dg==", "signatures": [{"sig": "MEUCIAuaI61Fr54ZQfX5iseDzwv1hkQozg2CIUnlCrozDtStAiEA81d15uNU9GqoE6c9gFNnrcTflGGvd9GMvVl/p3hvbTM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5916}}, "4.0.0-alpha.2": {"name": "@tailwindcss/vite", "version": "4.0.0-alpha.2", "dependencies": {"tailwindcss": "4.0.0-alpha.2", "@tailwindcss/oxide": "4.0.0-alpha.2"}, "devDependencies": {"vite": "^5.0.11", "@types/node": "^20.11.17"}, "dist": {"shasum": "b560f5896290b47bbc5e3a063aa9c0dc907c65f4", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-ecDAg9AsfwNhgP/1Kmf5UFRnYU6JNfUUGJpJOP0Tw16IKVBnirglnwQPBr3n+CK2u9yDqUuYpn1ymc+VJ0nhAg==", "signatures": [{"sig": "MEUCIEoYMqU5rbw3+TY1x2nKoJsLDl0Zb6G3rnfZma5rtsEuAiEAkWcR1YMDtsbIkFi2Onn+aLMyY0vALRJuRD9Z5lmx0q8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5685}}, "4.0.0-alpha.3": {"name": "@tailwindcss/vite", "version": "4.0.0-alpha.3", "dependencies": {"tailwindcss": "4.0.0-alpha.3", "@tailwindcss/oxide": "4.0.0-alpha.3"}, "devDependencies": {"vite": "^5.0.11", "@types/node": "^20.11.17"}, "dist": {"shasum": "36114c6e79973311963ce531b67ea4a1584b3f04", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-D1fvMB2ZQAoLDKsIzmGBQ/g1MW5Ew+Nc+QMtuGUIas6wW1Xbr+PF8LvvyMwzRcJ9GVObh5SR1gtUYUvbk5e1Ew==", "signatures": [{"sig": "MEUCICGOI0I+/PXj8cJuNA+oFnapIzc0/QjZL1XdwP7gvwdIAiEA7sRQH/NY6gX/tTJJ8A5YKYG2LOP0HrMrBreh/gHHyDA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5685}}, "4.0.0-alpha.4": {"name": "@tailwindcss/vite", "version": "4.0.0-alpha.4", "dependencies": {"tailwindcss": "4.0.0-alpha.4", "@tailwindcss/oxide": "4.0.0-alpha.4"}, "devDependencies": {"vite": "^5.0.11", "@types/node": "^20.11.17"}, "dist": {"shasum": "f6b000739090f4f160c41d8275d350f1d22e793b", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-+WgsHAtV9pft3RPwJQk7SyvauKl/h6pWp9U3l1uPvL3fSkCJFAH+LAJzGIwTahjxx79X7bgQZayHmgqfMzvjFA==", "signatures": [{"sig": "MEQCIBsDB0NPKPFYHIJ2VVlJSLgMycG566U7C3QCr1lY73i7AiBD4Gm30mu0wHESWwa3TGyBWLiuSgBzPfxw4FbLvkVYnQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5685}}, "4.0.0-alpha.5": {"name": "@tailwindcss/vite", "version": "4.0.0-alpha.5", "dependencies": {"tailwindcss": "4.0.0-alpha.5", "@tailwindcss/oxide": "4.0.0-alpha.5"}, "devDependencies": {"vite": "^5.0.11", "@types/node": "^20.11.17"}, "dist": {"shasum": "2283158415d8821e0560ca65139940f626a9dad0", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-+Wv8Cn0dcJV7VzBl4m0kOHUPLo0yPRVTK52qtc22UWBzfxkIA9UE4wO4WTzEbuauMbSYQb2Zz3eqBY7fp0/z0A==", "signatures": [{"sig": "MEQCIE8MeB6tg50B8xj3hM6uwRzeHgzE7LjqIxEEVxk04mLBAiAlkAz0uwR0JfsTuGIGfljzFvL0e4wcc3/4k/u79epKiQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5685}}, "4.0.0-alpha.6": {"name": "@tailwindcss/vite", "version": "4.0.0-alpha.6", "dependencies": {"tailwindcss": "4.0.0-alpha.6", "@tailwindcss/oxide": "4.0.0-alpha.6"}, "devDependencies": {"vite": "^5.0.11", "@types/node": "^20.11.17"}, "dist": {"shasum": "b5bc4f9e0875761e7ba8b7dfffe3adc0a045e725", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-L8hh6fE8C+HbTfYDIjoI8uCyjyRHO6OCi2ncBYp7AbmIvzjLfNYoOa1EgDU9t4HsoaYc8VB5+5wKbAoJbDR57w==", "signatures": [{"sig": "MEQCIBXbUKBq8KKSR9BkBbQTCKvALVa0qKBw5tkoNeb1ll6bAiAOrTyoouoEaYSQxWM0t+vl1wQ81yilxcb9pv7kpTegKA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5685}}, "4.0.0-alpha.7": {"name": "@tailwindcss/vite", "version": "4.0.0-alpha.7", "dependencies": {"tailwindcss": "4.0.0-alpha.7", "@tailwindcss/oxide": "4.0.0-alpha.7"}, "devDependencies": {"vite": "^5.0.11", "@types/node": "^20.11.17"}, "dist": {"shasum": "f02cef74697d5a38c2866d0c0b8421a085b368a7", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-C28Cy74JzIGRj7GbraFDZmm/oWuJZL9KZSZaYTNCuR+vuRX2SJLJyqKXjXqpQLVw9SCW6OwesBiYbftQnHP/Zw==", "signatures": [{"sig": "MEYCIQC76xrGuzTTYduy4oy1jnli7LxWsG3QEhI2tY8oeuogOwIhALfeF/znKsKU6u++JPxPL4CtXfz5nyD8a4UUofgYiR9y", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.0-alpha.7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 5871}}, "0.0.0-internal.6835ef85": {"name": "@tailwindcss/vite", "version": "0.0.0-internal.6835ef85", "dependencies": {"tailwindcss": "0.0.0-internal.6835ef85", "@tailwindcss/oxide": "0.0.0-internal.6835ef85"}, "devDependencies": {"vite": "^5.0.11", "@types/node": "^20.11.17"}, "dist": {"shasum": "52204937f8fe8d9adac7e3cb90c1003103cb65a3", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-internal.6835ef85.tgz", "fileCount": 5, "integrity": "sha512-pFHx/mlKn0cB+xJ/02yL6XSfec5teU/gWSOtjJFtNcQavjvNzBZKfkAkvqvQ2droFrkeLOJu996G2A6khqdHPA==", "signatures": [{"sig": "MEUCIQCtuoiE53fxUVNHLafJ0NG1vlZFgzsrPXKpwP4CYw1+7wIgENmolybpEtWiovn4d1iW3yJpHg0QvvEXz7I6OJ3Dg7g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-internal.6835ef85", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 5901}}, "4.0.0-alpha.8": {"name": "@tailwindcss/vite", "version": "4.0.0-alpha.8", "dependencies": {"tailwindcss": "4.0.0-alpha.8", "@tailwindcss/oxide": "4.0.0-alpha.8"}, "devDependencies": {"vite": "^5.0.11", "@types/node": "^20.11.17"}, "dist": {"shasum": "26d4adee19accddb2e3943313854325c16010231", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-r5e5I8zK+YkXUta2tgeWAIfkuqPahWCsmfx43tJ+YpBYRlMXPcSllRhxuB4qzpeB5cBqdcz3824BSyMpjHmg5w==", "signatures": [{"sig": "MEYCIQCv2lzUIyyVSmrG8sW4a2U0p3nkRXhsotsqe8GELHZMQAIhALZv3ntDvX9h+/JmIVXicLrc6lvS4cKxYKRHirE+eH72", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.0-alpha.8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 5856}}, "0.0.0-internal.b2586d4e": {"name": "@tailwindcss/vite", "version": "0.0.0-internal.b2586d4e", "dependencies": {"tailwindcss": "0.0.0-internal.b2586d4e", "@tailwindcss/oxide": "0.0.0-internal.b2586d4e"}, "devDependencies": {"vite": "^5.0.11", "@types/node": "^20.11.17"}, "dist": {"shasum": "2c359ad61464c13068827955a753e23bd8611d44", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-internal.b2586d4e.tgz", "fileCount": 5, "integrity": "sha512-cE5RCX4xAkJ7vJMIm3FPDa+RCFcTIl6UVga89aEYj6kGXUiMyCl73kFb+wRUbZI8UonrMPQk+jcHYpW7PXN7Ww==", "signatures": [{"sig": "MEUCIAZ6sQkLH5VQM4OPADDV1PKBQuaJyA+4ujqVnNgV+JuFAiEA/2S6owFUhd2QuwfmHoJAScz0BR+CmwiUDmr4WdEY7XY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-internal.b2586d4e", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 5886}}, "4.0.0-alpha.9": {"name": "@tailwindcss/vite", "version": "4.0.0-alpha.9", "dependencies": {"tailwindcss": "4.0.0-alpha.9", "lightningcss": "^1.24.0", "@tailwindcss/oxide": "4.0.0-alpha.9"}, "devDependencies": {"vite": "^5.0.11", "@types/node": "^20.11.17"}, "dist": {"shasum": "15d99631411ac32059bdd221a25a6d453de2c096", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.0-alpha.9.tgz", "fileCount": 5, "integrity": "sha512-o6VpCyClUOQVJr/kXG4DsZaG/rfck3zwcuL0L6QXgKpGP7XOrlwgZIGPYh7lEBgBqrokqzXAX8d/PLiVB+uv8Q==", "signatures": [{"sig": "MEUCIAfseCyeXcGivFkTmERZaYnC+C21v1dxgB03+t1QAQkbAiEAw+1ao221E/hXsy1JKOi+33oUFei3qIhuD1oHMy9SkPs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.0-alpha.9", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 6212}}, "4.0.0-alpha.10": {"name": "@tailwindcss/vite", "version": "4.0.0-alpha.10", "dependencies": {"tailwindcss": "4.0.0-alpha.10", "lightningcss": "^1.24.0", "@tailwindcss/oxide": "4.0.0-alpha.10"}, "devDependencies": {"vite": "^5.2.0", "@types/node": "^20.11.17"}, "dist": {"shasum": "ee064473b1dc859e6c0c617ce3373eb2e9a78c1f", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.0-alpha.10.tgz", "fileCount": 5, "integrity": "sha512-NUbAvkuq7DuONF6TqYlmJ6FYPpwAvEaKPAUFvS+k/OzcIm3HnBlTeDtEle6nzWmmdz+EJOgBEWIURkJ9tHHyPA==", "signatures": [{"sig": "MEUCIQDglsSfgtTThE5p9VY9ObRVZ3hsMyI8GLbTHVDjGqivWwIgVZgvYIf7FpxvWnLFStoNnB+GuveTwPr+LaVix8+yyo0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.0-alpha.10", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 6075}}, "4.0.0-alpha.11": {"name": "@tailwindcss/vite", "version": "4.0.0-alpha.11", "dependencies": {"tailwindcss": "4.0.0-alpha.11", "lightningcss": "^1.24.0", "@tailwindcss/oxide": "4.0.0-alpha.11"}, "devDependencies": {"vite": "^5.2.0", "@types/node": "^20.11.17"}, "peerDependencies": {"vite": "^5.2.0"}, "dist": {"shasum": "ef7f614a12c91721e54fe6547b20173d6525f02d", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.0-alpha.11.tgz", "fileCount": 5, "integrity": "sha512-VIeE6r5dMh+CLpIEiFFEw474QyQznXsBNJLz4GZ2kGtRqKxnHBjvPnA0EHSBpHZxVTeP/nyj1zopjK6kqInfKw==", "signatures": [{"sig": "MEYCIQDwcv48lpiJQ0S+DGKxMySHQxKTfTB3J7AQNOoAO+ug8AIhAJhjraIZTCMhB4T94azGjMUp1+kc2jd+NvT0FK1PsfBJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.0-alpha.11", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 6517}}, "4.0.0-alpha.12": {"name": "@tailwindcss/vite", "version": "4.0.0-alpha.12", "dependencies": {"tailwindcss": "4.0.0-alpha.12", "lightningcss": "^1.24.0", "@tailwindcss/oxide": "4.0.0-alpha.12"}, "devDependencies": {"vite": "^5.2.0", "@types/node": "^20.11.17"}, "peerDependencies": {"vite": "^5.2.0"}, "dist": {"shasum": "1254199d0bc9a749165314db13d3185ded563351", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.0-alpha.12.tgz", "fileCount": 5, "integrity": "sha512-sPCcusYQfWxoVpJrwOM/+Cs2FRIdQ+7sbb6NN5VG2GppTFeGpnfjgpLOE6c57xXH0GnEuv8xvLLcTdh20j9zkA==", "signatures": [{"sig": "MEYCIQDTWmnNf4UPhWlnC7vQ44EElGueGEB2CuJxixdkW69VCAIhAIH9DIwWGbbUaJWm0KMEEk1aBkbZIPTkzuUM+7SzKaPI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.0-alpha.12", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 6557}}, "4.0.0-alpha.13": {"name": "@tailwindcss/vite", "version": "4.0.0-alpha.13", "dependencies": {"tailwindcss": "4.0.0-alpha.13", "lightningcss": "^1.24.0", "@tailwindcss/oxide": "4.0.0-alpha.13"}, "devDependencies": {"vite": "^5.2.0", "@types/node": "^20.11.17"}, "peerDependencies": {"vite": "^5.2.0"}, "dist": {"shasum": "bf769a6974fe015f22d4cf5318abc7c7f1e979ff", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.0-alpha.13.tgz", "fileCount": 5, "integrity": "sha512-4X6wuKM0DSwQTbcC9O7P69Uy5xUoypjtDcAW5QYuhQxpmHTlmrfiK+5JJcOwBCfwsa69hzJUg2WunPnKyVHMWw==", "signatures": [{"sig": "MEQCIGNAmfvp2G/Sz0iyUJNpYdy+9WHtJ1JcUcH3uRMPnDRTAiBTF74HNWgo1XoDLPu0cPyN1v/A5iqWx58kx9NgE6N5Rw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.0-alpha.13", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 6557}}, "4.0.0-alpha.14": {"name": "@tailwindcss/vite", "version": "4.0.0-alpha.14", "dependencies": {"tailwindcss": "4.0.0-alpha.14", "lightningcss": "^1.24.0", "@tailwindcss/oxide": "4.0.0-alpha.14"}, "devDependencies": {"vite": "^5.2.0", "@types/node": "^20.11.17"}, "peerDependencies": {"vite": "^5.2.0"}, "dist": {"shasum": "e339bd2aff669709a845a19e1c9746ccf3cc6d32", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.0-alpha.14.tgz", "fileCount": 5, "integrity": "sha512-tvKPP948JrgSR1Ig5ogLYEn1h4FW36W3p0eCGFGJV++NF7QsBh0j98HydrhvQDxsC9jZ037yGWUOc4VPMbt22g==", "signatures": [{"sig": "MEYCIQD0h1Qxj7sxIRX9hYS9YPr7qyOma0nWhp7lWHiO8BkfagIhAM5WfcKrJmjoW77Xs/T25qp0d40PJ6XYS3F/zcY5rafV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.0-alpha.14", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 6723}}, "4.0.0-alpha.15": {"name": "@tailwindcss/vite", "version": "4.0.0-alpha.15", "dependencies": {"tailwindcss": "4.0.0-alpha.15", "lightningcss": "^1.24.0", "@tailwindcss/oxide": "4.0.0-alpha.15"}, "devDependencies": {"vite": "^5.2.0", "@types/node": "^20.11.17"}, "peerDependencies": {"vite": "^5.2.0"}, "dist": {"shasum": "c2e7f4767f9671280e99ce57a23d9a64d7c0c2f4", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.0-alpha.15.tgz", "fileCount": 5, "integrity": "sha512-7mZwtqc4s+LX5h9EKR7WkLCkMuf1/xr9hxtcHPiAN3hh/fvhO5djnldCgEKJaNBX+5UnxOtfajnMCFVGrfah3Q==", "signatures": [{"sig": "MEUCIQC8h4OwRauVIcl25WxQKOWaaqufCWvKln0m+h+iClfMhAIgefzAcfztLMPxO4uverUALrUpObnmyFX+wuX0mPxa58c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.0-alpha.15", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 6723}}, "4.0.0-alpha.16": {"name": "@tailwindcss/vite", "version": "4.0.0-alpha.16", "dependencies": {"tailwindcss": "4.0.0-alpha.16", "lightningcss": "^1.25.1", "@tailwindcss/oxide": "4.0.0-alpha.16"}, "devDependencies": {"vite": "^5.2.11", "@types/node": "^20.12.12"}, "peerDependencies": {"vite": "^5.2.0"}, "dist": {"shasum": "9683df325578d98285e994eb23aee3558f377c20", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.0-alpha.16.tgz", "fileCount": 5, "integrity": "sha512-Zeft2VUwKvg7f7fHdftHt+VNWNPKhZH1gkRRKnU0qCa2SVEX5ShgokrsUE0sWIWSbETu5sUJeLPoZI3k/WaVMg==", "signatures": [{"sig": "MEUCIBJLM7L/bt6+/FyT3RVeT3KGmveoWLHznffZl61erZubAiEA+wsdBxxYok62I7YplzNQvdWIWU4EFRnp/ij12v6JQdw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.0-alpha.16", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 6724}}, "4.0.0-alpha.17": {"name": "@tailwindcss/vite", "version": "4.0.0-alpha.17", "dependencies": {"tailwindcss": "4.0.0-alpha.17", "lightningcss": "^1.25.1", "@tailwindcss/oxide": "4.0.0-alpha.17"}, "devDependencies": {"vite": "^5.2.11", "@types/node": "^20.12.12"}, "peerDependencies": {"vite": "^5.2.0"}, "dist": {"shasum": "e2e4bfcbb98509e4ea4b4484309b772c4f0fbf73", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.0-alpha.17.tgz", "fileCount": 5, "integrity": "sha512-Lixgt4GDFF652OwPQFG1vTSlp9kWDquKzezqXTmA1q+6Ojys4UxJVGsxPUMwGaT5Znd/gZCJrsJW24UFX6uQJg==", "signatures": [{"sig": "MEYCIQCWq6a+KzmkcH3Mz41750E1khkI/ZvPnReuzNLp8wgKBwIhAJSmPvhk804mgBrxtr6+zJYhZQwSE0SSfqZqv7jIoV/N", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.0-alpha.17", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 6724}}, "4.0.0-alpha.18": {"name": "@tailwindcss/vite", "version": "4.0.0-alpha.18", "dependencies": {"tailwindcss": "4.0.0-alpha.18", "lightningcss": "^1.25.1", "@tailwindcss/oxide": "4.0.0-alpha.18"}, "devDependencies": {"vite": "^5.2.11", "@types/node": "^20.12.12"}, "peerDependencies": {"vite": "^5.2.0"}, "dist": {"shasum": "d439ad1c5f725d328d9eb6dbe0da6bb64a6e056e", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.0-alpha.18.tgz", "fileCount": 5, "integrity": "sha512-4fgiYJTcQGtsYKbQ5lxetf2kwCzU2JClPCFH+VNF8cpOu4omk/t1FvPFeW8FfIYe22bV7PqoQVyzKRjBMF27NA==", "signatures": [{"sig": "MEUCIEvOMKXB2i64/q+JxtMlj1IrBqd95nTfL5gwNDIOFFHTAiEAgm1WbdvPuH0CklD11jGzweFbphJCrQJg/7iVA3KmD1g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.0-alpha.18", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 7063}}, "4.0.0-alpha.19": {"name": "@tailwindcss/vite", "version": "4.0.0-alpha.19", "dependencies": {"tailwindcss": "4.0.0-alpha.19", "lightningcss": "^1.26.0", "@tailwindcss/oxide": "4.0.0-alpha.19", "postcss-load-config": "^6.0.1"}, "devDependencies": {"vite": "^5.4.0", "@types/node": "^20.14.8", "internal-postcss-fix-relative-paths": "0.0.0"}, "peerDependencies": {"vite": "^5.2.0"}, "dist": {"shasum": "86da50fd8c2ade55fecd8361346186dbc108f7ce", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.0-alpha.19.tgz", "fileCount": 5, "integrity": "sha512-+JG43ehbw1YyPAHjl7zzUpaR8egqJwC5/R//QMn8DsQ1fBaeTs2U+/0W9dkh5SVcFijRhldayvMs5zixGQHiSA==", "signatures": [{"sig": "MEYCIQDqKy0Xv4LyGlEcTnU36HLS51WmHoXQnUu9N8BA6WfgfAIhAIW/aJkWEYMcU+uRMnrirAAGBCR6uconsils5x0knrcX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.0-alpha.19", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9126}}, "4.0.0-alpha.20": {"name": "@tailwindcss/vite", "version": "4.0.0-alpha.20", "dependencies": {"tailwindcss": "4.0.0-alpha.20", "lightningcss": "^1.26.0", "@tailwindcss/oxide": "4.0.0-alpha.20", "postcss-load-config": "^6.0.1"}, "devDependencies": {"vite": "^5.4.0", "@types/node": "^20.14.8", "internal-postcss-fix-relative-paths": "0.0.0"}, "peerDependencies": {"vite": "^5.2.0"}, "dist": {"shasum": "f1022739e37c5ae45c65aca86b71e51a6625b192", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.0-alpha.20.tgz", "fileCount": 5, "integrity": "sha512-NsqlqTfFEVFp9lOhYTNbiglli9d0kag9mJ1lHHC5WBvkmgAXMwbah1ZPL02qrYUTMjSVrkN0astqQw/CS1zptA==", "signatures": [{"sig": "MEQCIAv7lpoWTiFs8+IQkfBTMIO92yt/8hDr+z0BBoLlCoS4AiAxabUyQFLbMjjU4DWKBR55QGMEPVSEMvfA7owqg1QXJw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.0-alpha.20", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9101}}, "4.0.0-alpha.21": {"name": "@tailwindcss/vite", "version": "4.0.0-alpha.21", "dependencies": {"tailwindcss": "4.0.0-alpha.21", "lightningcss": "^1.26.0", "@tailwindcss/node": "4.0.0-alpha.21", "@tailwindcss/oxide": "4.0.0-alpha.21", "postcss-load-config": "^6.0.1"}, "devDependencies": {"vite": "^5.4.0", "@types/node": "^20.14.8", "internal-postcss-fix-relative-paths": "0.0.0"}, "peerDependencies": {"vite": "^5.2.0"}, "dist": {"shasum": "5816c4a9cfa70aea1ce04d2c98821bca86fe54e4", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.0-alpha.21.tgz", "fileCount": 5, "integrity": "sha512-54uBFd1UavjKoYng2IApvB+B8X/6O7k1gPGk4T0KrtzV0ZsoT7K/MyAnm6zyi1mIHez8btzNiOSV38YPb9i90A==", "signatures": [{"sig": "MEYCIQDqoBFt9kn8cEUwK6B5Y5lZyLGD4DNOUYAMb6YavKa/egIhANyzSmX2kd+ZUn5pW5Rtr+JX14tdpT/vX+2H2Y+Eh0Yf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.0-alpha.21", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9176}}, "4.0.0-alpha.22": {"name": "@tailwindcss/vite", "version": "4.0.0-alpha.22", "dependencies": {"postcss": "^8.4.41", "tailwindcss": "4.0.0-alpha.22", "lightningcss": "^1.26.0", "postcss-import": "^16.1.0", "@tailwindcss/node": "4.0.0-alpha.22", "@tailwindcss/oxide": "4.0.0-alpha.22"}, "devDependencies": {"vite": "^5.4.0", "@types/node": "^20.14.8", "@types/postcss-import": "^14.0.3", "internal-postcss-fix-relative-paths": "0.0.0"}, "peerDependencies": {"vite": "^5.2.0"}, "dist": {"shasum": "ba754b781463d9b095ce22a496019d62189e8d87", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.0-alpha.22.tgz", "fileCount": 5, "integrity": "sha512-kzNz4bVLOejsx7ZyykEK3+N2iXi90SJtxIolUJ4lxFwopeymmH1wXMFvgDyRHMfEJBHw/J+gH+Zo1J7cJb1cow==", "signatures": [{"sig": "MEYCIQDDX+NtByoxtHbY+aTjV5bX0s4JrvIzO4JIDI93cxZm1QIhAP/LleHMcKPpAqjkEOVgWrkXLpUkJ2X0Yfwp5jB0c0Z5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.0-alpha.22", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9738}}, "4.0.0-alpha.23": {"name": "@tailwindcss/vite", "version": "4.0.0-alpha.23", "dependencies": {"postcss": "^8.4.41", "tailwindcss": "4.0.0-alpha.23", "lightningcss": "^1.26.0", "postcss-import": "^16.1.0", "@tailwindcss/node": "4.0.0-alpha.23", "@tailwindcss/oxide": "4.0.0-alpha.23"}, "devDependencies": {"vite": "^5.4.0", "@types/node": "^20.14.8", "@types/postcss-import": "^14.0.3", "internal-postcss-fix-relative-paths": "0.0.0"}, "peerDependencies": {"vite": "^5.2.0"}, "dist": {"shasum": "c605915fddd6250a270692be1b9f89e3b62b2588", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.0-alpha.23.tgz", "fileCount": 5, "integrity": "sha512-J<PERSON>llYp6YKOl3mMpKZ4UGf9E32HBCxf/tZ1jrFORB+Sj7ng69rDHOkYyQREezPIHguHj2yeREeShIivAcLnu5tQ==", "signatures": [{"sig": "MEUCIQCDazwHtTriVUKgfW/qvFCQ2rSftqUQGSjSb/1zqgtFQwIgKdiQP+WN7LMII4TkoEjB4SjXEY+h31Lq67LaV9ohwOU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.0-alpha.23", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9738}}, "4.0.0-alpha.24": {"name": "@tailwindcss/vite", "version": "4.0.0-alpha.24", "dependencies": {"postcss": "^8.4.41", "tailwindcss": "4.0.0-alpha.24", "lightningcss": "^1.26.0", "postcss-import": "^16.1.0", "@tailwindcss/node": "4.0.0-alpha.24", "@tailwindcss/oxide": "4.0.0-alpha.24"}, "devDependencies": {"vite": "^5.4.0", "@types/node": "^20.14.8", "@types/postcss-import": "^14.0.3", "internal-postcss-fix-relative-paths": "0.0.0"}, "peerDependencies": {"vite": "^5.2.0"}, "dist": {"shasum": "86571c0a0355210cdfbfdd0e1547effd0611fa5c", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.0-alpha.24.tgz", "fileCount": 5, "integrity": "sha512-XhOIyV93if3DeRBa2IeTXjaFCrY/JRFDJ6SIcwgELpfuZBunTICd8TWe/22I0abKd208yfX9sWEismGrY47iwA==", "signatures": [{"sig": "MEMCIFXpVoHYjqM0nKf8awvsyIv1ugvNUuU1v4UshS12R7cNAh9OtWCxTfoUbPQlrnn6ZPWwNG9t0qxrgnfT8mWLGxOm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.0-alpha.24", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9738}}, "4.0.0-alpha.25": {"name": "@tailwindcss/vite", "version": "4.0.0-alpha.25", "dependencies": {"tailwindcss": "4.0.0-alpha.25", "lightningcss": "^1.26.0", "@tailwindcss/node": "4.0.0-alpha.25", "@tailwindcss/oxide": "4.0.0-alpha.25"}, "devDependencies": {"vite": "^5.4.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0"}, "dist": {"shasum": "5d553a0391e04bed94de6fef9db8ebabd895e040", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.0-alpha.25.tgz", "fileCount": 5, "integrity": "sha512-lTWX8uPUd4kFd5/mKSCmodLBApMEFZRc9Vbj/4mKcETGYGkxzdXPgTRJPNg0P5dSzvHnObNjyoXhPV9UOGZXiQ==", "signatures": [{"sig": "MEUCIHeE3HpY/YPp6GmABNHbxVNbfDEON629TjZCMAhz+z3aAiEAr1tFxEURz4N1LvU0LQDziSTeWuMXmk3YjiBMW0D+Ktk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.0-alpha.25", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 8036}}, "4.0.0-alpha.26": {"name": "@tailwindcss/vite", "version": "4.0.0-alpha.26", "dependencies": {"tailwindcss": "4.0.0-alpha.26", "lightningcss": "^1.26.0", "@tailwindcss/node": "4.0.0-alpha.26", "@tailwindcss/oxide": "4.0.0-alpha.26"}, "devDependencies": {"vite": "^5.4.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0"}, "dist": {"shasum": "aec8b5dd9d9246fbc8f4294c7026742a0fdf1738", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.0-alpha.26.tgz", "fileCount": 5, "integrity": "sha512-BOqJqOlrJwwkiAfiBTA0a1wUmT/hak4+jpxe5p1kdoQSI3a+LCLpd7MwdPatxnZ0bj+/cjiLHqPScRyftli2vA==", "signatures": [{"sig": "MEQCIGk6vgW5yt8kEKln2Fz6EkrLi2GWoE4o0K3YIYkEFD63AiBMOWWDvY+ak3S47p8NL/gdGDL8PS2fWRU2mjKCSLtblw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.0-alpha.26", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 8556}}, "4.0.0-alpha.27": {"name": "@tailwindcss/vite", "version": "4.0.0-alpha.27", "dependencies": {"tailwindcss": "4.0.0-alpha.27", "lightningcss": "^1.26.0", "@tailwindcss/node": "4.0.0-alpha.27", "@tailwindcss/oxide": "4.0.0-alpha.27"}, "devDependencies": {"vite": "^5.4.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0"}, "dist": {"shasum": "501a85d3c9f1a01a549311ece2f1c840d70acd38", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.0-alpha.27.tgz", "fileCount": 5, "integrity": "sha512-kn51cawq0FCiu2ioPHLuWeUFV5J0jhav9oboiLdFm1bcOnYesso89LBYzFfJryQwnnmzbFgcPqkBWewwKL99rQ==", "signatures": [{"sig": "MEQCIApf+N1fhk7oE760avruI9nbPBoifq1j4vaulORlxOBXAiADAsTW7j57NcYwhXRsxHMd7nmXGgJeoOV9d/c5ELuA+A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.0-alpha.27", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 8556}}, "4.0.0-alpha.28": {"name": "@tailwindcss/vite", "version": "4.0.0-alpha.28", "dependencies": {"tailwindcss": "4.0.0-alpha.28", "lightningcss": "^1.26.0", "@tailwindcss/node": "4.0.0-alpha.28", "@tailwindcss/oxide": "4.0.0-alpha.28"}, "devDependencies": {"vite": "^5.4.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0"}, "dist": {"shasum": "fd1372cca454fa3a2cb47df164994005cac44e1b", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.0-alpha.28.tgz", "fileCount": 5, "integrity": "sha512-FCouNgyaz4fN1ts17jgATKSgD3s8crres8qRbSMwKenZoWECCtdoSN+pHkmrR1yQUjOmYjJu88ahhe/CBcQdOQ==", "signatures": [{"sig": "MEYCIQDbz7s5NxEdViuoxTj5+9hnCCRjlby0zo5lAzuJro4LYQIhAPYGgTR6M4V2k2DvTngIwLsVCB/2jasDRuoTg47KJd1s", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.0-alpha.28", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 8556}}, "4.0.0-alpha.29": {"name": "@tailwindcss/vite", "version": "4.0.0-alpha.29", "dependencies": {"tailwindcss": "4.0.0-alpha.29", "lightningcss": "^1.26.0", "@tailwindcss/node": "4.0.0-alpha.29", "@tailwindcss/oxide": "4.0.0-alpha.29"}, "devDependencies": {"vite": "^5.4.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0"}, "dist": {"shasum": "caf5db2169deeacc22c9e69495848957c7f96610", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.0-alpha.29.tgz", "fileCount": 5, "integrity": "sha512-WuQM1QgrSYKmlVOx8yccdeKI2P6vXE0xYOo3vbUDvSy/mPmoAZqxR5gDIILY6aHNHBOzMshmpwMtW18MvpFATw==", "signatures": [{"sig": "MEQCIDO8wZmKI0VgmehHOGd6+NXsyn/yNHTE9IMPYc2yGhFnAiBG40SDiQnTPGqVMQhnJWVj1LpMW5uGOGmCCVVe/enWNg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.0-alpha.29", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 8593}}, "4.0.0-alpha.30": {"name": "@tailwindcss/vite", "version": "4.0.0-alpha.30", "dependencies": {"tailwindcss": "4.0.0-alpha.30", "lightningcss": "^1.26.0", "@tailwindcss/node": "4.0.0-alpha.30", "@tailwindcss/oxide": "4.0.0-alpha.30"}, "devDependencies": {"vite": "^5.4.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0"}, "dist": {"shasum": "b3e239b92343ec906bb28d6b2d8821aaf4533dec", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.0-alpha.30.tgz", "fileCount": 5, "integrity": "sha512-Ql9m2udo/r08XUg1wf/pokKCVTBI4rElV85cPnXp8Bp9eVI/4WbwnjaKbW36R4wYEtuiPZ8F+VN7q/vyVkapSQ==", "signatures": [{"sig": "MEYCIQDSNJV9chUvARcXAYAJ8e/l2JtcOnmlZWSeRA9LSYjCegIhAM+m6lrNZ+qf5/nMfY/Jj7Xy+VcRWbiQRFgkpPz1Gd7j", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.0-alpha.30", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 8593}}, "4.0.0-alpha.31": {"name": "@tailwindcss/vite", "version": "4.0.0-alpha.31", "dependencies": {"tailwindcss": "4.0.0-alpha.31", "lightningcss": "^1.26.0", "@tailwindcss/node": "4.0.0-alpha.31", "@tailwindcss/oxide": "4.0.0-alpha.31"}, "devDependencies": {"vite": "^5.4.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0"}, "dist": {"shasum": "5d31f5f4d285fea75d1d49ea3f462bc0d965a7d0", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.0-alpha.31.tgz", "fileCount": 5, "integrity": "sha512-N+BvUWEBkhyP2YFmTdDqqIEWdm65nEHQwusa3cNbbh/A9DuiqdSAvNkLqUAimb4sWtm2GIMMgnUTaY1V6TVb3g==", "signatures": [{"sig": "MEQCIGBZ5KRDnz0C+vCtEEjFzYtnadobL8lTnTNoaAO5onA0AiBH6wI8BY5wKt0tiyth6biZfwajKLGwrUW2NmLepCJNlA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.0-alpha.31", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9350}}, "4.0.0-alpha.32": {"name": "@tailwindcss/vite", "version": "4.0.0-alpha.32", "dependencies": {"tailwindcss": "4.0.0-alpha.32", "lightningcss": "^1.26.0", "@tailwindcss/node": "4.0.0-alpha.32", "svelte-preprocess": "^6.0.2", "@tailwindcss/oxide": "4.0.0-alpha.32"}, "devDependencies": {"vite": "^5.4.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0"}, "dist": {"shasum": "ffed5fe6f7115c99aed72c9508bf86fd64ec1ece", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.0-alpha.32.tgz", "fileCount": 5, "integrity": "sha512-w/bu4S+4D1lu7J/izPGCmcotAbSLxqEuTQwATSMOru++VQACkkKHi2dGsiXL2wJE+hdgSkXjT22+JDqvE5USOA==", "signatures": [{"sig": "MEUCIQCd1n3Fsx6nJnbUm8SSYjI9hKU5mliLqZNeI1b+GPNUigIgJcFnJI5SKG+qbqUBarrnlk1jejqzS/X32BvMcMOw07g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.0-alpha.32", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 10372}}, "4.0.0-alpha.33": {"name": "@tailwindcss/vite", "version": "4.0.0-alpha.33", "dependencies": {"tailwindcss": "4.0.0-alpha.33", "lightningcss": "^1.26.0", "@tailwindcss/node": "4.0.0-alpha.33", "svelte-preprocess": "^6.0.2", "@tailwindcss/oxide": "4.0.0-alpha.33"}, "devDependencies": {"vite": "^5.4.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0"}, "dist": {"shasum": "c95c248acb25317fb6a3345b105e23034378f0d2", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.0-alpha.33.tgz", "fileCount": 5, "integrity": "sha512-HK6bUwVYI9dBKNLZSv+baR1kUo5dbxYjkT2r2FQvK2Yr+BPt6tJ4kXbG3KhfndQ6xdjKN9PVqTueXzi2v/GNdg==", "signatures": [{"sig": "MEQCIAl6UKxo46OA6snoar7FxTNy0Z/Mpp4cSOKcUDp5WgabAiAzD2ER5liW+Ydya1F8robDs/j589FcCJFnRleEfybKeQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.0-alpha.33", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 10372}}, "4.0.0-alpha.34": {"name": "@tailwindcss/vite", "version": "4.0.0-alpha.34", "dependencies": {"tailwindcss": "4.0.0-alpha.34", "lightningcss": "^1.26.0", "@tailwindcss/node": "4.0.0-alpha.34", "svelte-preprocess": "^6.0.2", "@tailwindcss/oxide": "4.0.0-alpha.34"}, "devDependencies": {"vite": "^5.4.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0"}, "dist": {"shasum": "7b811040b716c36af673ce0e2391ff2f952c5796", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.0-alpha.34.tgz", "fileCount": 5, "integrity": "sha512-9n/SRZtrTq3KxjASeMBPyv5tV2f9kU8zzVgzObA+cUGqU7wPGKPNnadptBzZWeqjYABgxKjjuljd7Qn6/vz6YA==", "signatures": [{"sig": "MEYCIQC6VkNk/glbjJqH6u/JhCZPpUzck8CrNdWkHnWavBucYwIhALz52/zDk+wvRbPlc95PFYNxx22Nz5OaT817kQp6zUJ+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.0-alpha.34", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 10718}}, "4.0.0-alpha.35": {"name": "@tailwindcss/vite", "version": "4.0.0-alpha.35", "dependencies": {"tailwindcss": "4.0.0-alpha.35", "lightningcss": "^1.26.0", "@tailwindcss/node": "4.0.0-alpha.35", "svelte-preprocess": "^6.0.2", "@tailwindcss/oxide": "4.0.0-alpha.35"}, "devDependencies": {"vite": "^5.4.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0"}, "dist": {"shasum": "a75439f6d638db39d1471f502051e460be16f0aa", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.0-alpha.35.tgz", "fileCount": 5, "integrity": "sha512-qhaiCJ/nBc88r3FIXr5xGy5zB4qbvXaHty/0r9msnu42l4+Y3A4eoFNN4iRwiFny8YNsXHKwa4eU2A7rVFkhbg==", "signatures": [{"sig": "MEUCIQCMBbJ/OCMDNro0awAeHYDQh4Vnrq3R5f+Ea8ESe298gwIgI2MQ0XIpQxLPu1nN+n5MUhFDBPhdquzPUbOKsUW3nec=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.0-alpha.35", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 10718}}, "4.0.0-alpha.36": {"name": "@tailwindcss/vite", "version": "4.0.0-alpha.36", "dependencies": {"tailwindcss": "4.0.0-alpha.36", "lightningcss": "^1.26.0", "@tailwindcss/node": "4.0.0-alpha.36", "svelte-preprocess": "^6.0.2", "@tailwindcss/oxide": "4.0.0-alpha.36"}, "devDependencies": {"vite": "^5.4.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0"}, "dist": {"shasum": "e36c071941a5d162c9f79317c892a28554833f39", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.0-alpha.36.tgz", "fileCount": 5, "integrity": "sha512-P43YtkMdE1nvRqPqQ3HmHVhwdIImfNdLlXFO10Oz1qniVutFoZTNp2G3Dp4hhN1UKNhle9N8XysJqobfFNTtpA==", "signatures": [{"sig": "MEUCIDisQVxS0pXRNxP/89pTHivAiaLWzEfCA4WF5vb/heETAiEAlG11NI1kvckOEh3LnUq85Pi/JGhAWCMwBTZyT7DwfZE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.0-alpha.36", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 10718}}, "4.0.0-beta.1": {"name": "@tailwindcss/vite", "version": "4.0.0-beta.1", "dependencies": {"tailwindcss": "4.0.0-beta.1", "lightningcss": "^1.26.0", "@tailwindcss/node": "4.0.0-beta.1", "svelte-preprocess": "^6.0.2", "@tailwindcss/oxide": "4.0.0-beta.1"}, "devDependencies": {"vite": "^5.4.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0"}, "dist": {"shasum": "1b2447b9b41696f9d4e72ffb626034aea1dea68c", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.0-beta.1.tgz", "fileCount": 5, "integrity": "sha512-rzll9oDIXFv2zij1DYbeeCR8C3oFxfA4IxXRc7Wuu2kMKhGLxjjW/xBmNqRiOpswYDvYp51XhsM05nt1qaBJfw==", "signatures": [{"sig": "MEUCIQDJP9JCMVAE8bYHYshyU5e0AS+P5O0jWX5zK1BRqE+B0AIgY5y/UeYoTyrH3YwuuODdyobnANGQNI5W7iy1DzwQrhk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.0-beta.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 10710}}, "4.0.0-beta.2": {"name": "@tailwindcss/vite", "version": "4.0.0-beta.2", "dependencies": {"tailwindcss": "4.0.0-beta.2", "lightningcss": "^1.26.0", "@tailwindcss/node": "4.0.0-beta.2", "svelte-preprocess": "^6.0.2", "@tailwindcss/oxide": "4.0.0-beta.2"}, "devDependencies": {"vite": "^5.4.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0"}, "dist": {"shasum": "2dfef01286525df8b003937d92fdcc2bb21447e7", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.0-beta.2.tgz", "fileCount": 5, "integrity": "sha512-9RJwoCRjjS0ltmGiWtZGPyVg967SSwbI/+DXovU1iCWwiWJJhDJTaZ5GO1Cd6FMj4wFEjna3RkI7CG44I+FprA==", "signatures": [{"sig": "MEQCIH6qHRYYZ5sCB85svZPGom+RzFL/cH8UljbVpkkf3qLNAiAvAASa3o0Cu69KI2CMci/NZBv9cGDB/w4woojLATYUeQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.0-beta.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 10710}}, "4.0.0-beta.3": {"name": "@tailwindcss/vite", "version": "4.0.0-beta.3", "dependencies": {"tailwindcss": "4.0.0-beta.3", "lightningcss": "^1.26.0", "@tailwindcss/node": "4.0.0-beta.3", "svelte-preprocess": "^6.0.2", "@tailwindcss/oxide": "4.0.0-beta.3"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0"}, "dist": {"shasum": "525865e383bcfe9a0b4942ff39a76b326d2d5192", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.0-beta.3.tgz", "fileCount": 5, "integrity": "sha512-OEnRluaa3hvjnCQhY11YN8GiPoUbXQL5Qb/727q+3FBiGA1vIqrVbIJ01Z256bkaZLCl+AUyqCGasRd2jsSVLA==", "signatures": [{"sig": "MEUCIQD2oqWpA9oICk29F2igdNfytL2COip4kcSDlyE4AfyfWwIgRfRHkkg191KB+l8uFGU+8Js/igWMO7mCtWzp4Uh79n8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.0-beta.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11167}}, "4.0.0-beta.4": {"name": "@tailwindcss/vite", "version": "4.0.0-beta.4", "dependencies": {"tailwindcss": "4.0.0-beta.4", "lightningcss": "^1.26.0", "@tailwindcss/node": "4.0.0-beta.4", "svelte-preprocess": "^6.0.2", "@tailwindcss/oxide": "4.0.0-beta.4"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0"}, "dist": {"shasum": "14434afaf1aa2aa4cb80ca751205e3e18cc98ec0", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.0-beta.4.tgz", "fileCount": 5, "integrity": "sha512-VpexhM1fzmhT3KfviF3BAOeJ5p0Pc0wW6QkTUI+aiRlIHtaPI9QxNrOvLgkLSqGxucb78ERA0eI2hrcAN6gjWA==", "signatures": [{"sig": "MEQCIAq+N+AWlVg+hEWmBlksk+hteCfwrOJ10qo6AvgVVD3bAiApmcH/chTvu3AnJKN0v6x4PHZq73y+6/SOWHlmVFWybA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.0-beta.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11352}}, "4.0.0-beta.5": {"name": "@tailwindcss/vite", "version": "4.0.0-beta.5", "dependencies": {"tailwindcss": "4.0.0-beta.5", "lightningcss": "^1.26.0", "@tailwindcss/node": "4.0.0-beta.5", "@tailwindcss/oxide": "4.0.0-beta.5"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "f5dc4018f525f6008747d49cbebf796040c1d930", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.0-beta.5.tgz", "fileCount": 5, "integrity": "sha512-zyPth1Zl1ChmDmNWXoaznj7HuQlEZ+V6xJfMScryCYKZHy+hzrXKwambjIb/ddE6ajR7DneSFAdALsn0WWQcwg==", "signatures": [{"sig": "MEYCIQDxw7qG2Whtj5RxedAu+TkGJVp8dIgnBL+TMgNZ8veOTQIhAJ6Q9MjZ2gqloRFAukl9uepf2LqqnJrYTigtbG0s6iWo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.0-beta.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11096}}, "4.0.0-beta.6": {"name": "@tailwindcss/vite", "version": "4.0.0-beta.6", "dependencies": {"tailwindcss": "4.0.0-beta.6", "lightningcss": "^1.26.0", "@tailwindcss/node": "4.0.0-beta.6", "@tailwindcss/oxide": "4.0.0-beta.6"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "b578fb34eed12fe757c099d80ae7ea47106bfadd", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.0-beta.6.tgz", "fileCount": 5, "integrity": "sha512-SDlW8wrfhoGvEmNdbxdjPD0RJV5ErPegeJQFXNlS6CF815tzMMTekToIeMJphvwUO6w2TLSjvO9w3F9Z0ElMgQ==", "signatures": [{"sig": "MEUCIHkqXyn/0Dzv1PPfmyqCzroncLh4bBItBxjJ+xQss8PwAiEA6ZPO8BwQxAJhP2NhLoyMJBkVhpGZjS2JUHkA+7X8m1U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.0-beta.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11096}}, "4.0.0-beta.7": {"name": "@tailwindcss/vite", "version": "4.0.0-beta.7", "dependencies": {"tailwindcss": "4.0.0-beta.7", "lightningcss": "^1.26.0", "@tailwindcss/node": "4.0.0-beta.7", "@tailwindcss/oxide": "4.0.0-beta.7"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "08723dc730361a9ce94c54d9443be307856fa85e", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.0-beta.7.tgz", "fileCount": 5, "integrity": "sha512-F0il5CoWBlwLt6m1zefQzt2ZzTQizOA3SpMcblPp26HHUxDoGxOafKyzuzgldDBNU37rFpRq6UvdKseA3Re62Q==", "signatures": [{"sig": "MEQCIHcbvLwHMCknnfNZVbWvJ7/vroVJSNAMpZGvsuCgVf+aAiB2eOt+TPZyzhVatk/tUzcwbwGlTel602H4+fFCyWTn+A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.0-beta.7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 12181}}, "4.0.0-beta.8": {"name": "@tailwindcss/vite", "version": "4.0.0-beta.8", "dependencies": {"tailwindcss": "4.0.0-beta.8", "lightningcss": "^1.26.0", "@tailwindcss/node": "4.0.0-beta.8", "@tailwindcss/oxide": "4.0.0-beta.8"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "85f53579ffce9c4b7a13664020bf5e1764c91738", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.0-beta.8.tgz", "fileCount": 5, "integrity": "sha512-ZNlj0fdeH4/uWXafrXklZY+TgmN7wOHWHHBL4i3xzD4BflcCDZJkgJER/8baJCpagMzwWDnA6CyXDX+2q7lMRQ==", "signatures": [{"sig": "MEYCIQDMN82FY2Rlf7jNoiJ442AnAxLMP1XQOfAbE7AtZhcbzwIhAN9AScDuNK9995ye6709sNkW8spwWDnWi96H6UD9O3dS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.0-beta.8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 12181}}, "4.0.0-beta.9": {"name": "@tailwindcss/vite", "version": "4.0.0-beta.9", "dependencies": {"tailwindcss": "4.0.0-beta.9", "lightningcss": "^1.29.0", "@tailwindcss/node": "4.0.0-beta.9", "@tailwindcss/oxide": "4.0.0-beta.9"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "f37260b8bada874f726e2e071c33b615a0db5b70", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.0-beta.9.tgz", "fileCount": 5, "integrity": "sha512-Hf28QkwSLM6bbOkcTQk1iEEOB37v+9vfqdpHUaLSluZpEGCVAFc0i+p2Gvp6MlK840tyixQu5L39VjL2lAZFFQ==", "signatures": [{"sig": "MEUCIQDK7D4RHoD0rmird53dH3BidOJQwAfaX7e/vbYk6wP1nQIgJf+fckb87vKTbTgfOFkDaKB+ksIh9WwbT789Tnb6d+k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.0-beta.9", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11180}}, "4.0.0-beta.10": {"name": "@tailwindcss/vite", "version": "4.0.0-beta.10", "dependencies": {"tailwindcss": "4.0.0-beta.10", "lightningcss": "^1.29.1", "@tailwindcss/node": "4.0.0-beta.10", "@tailwindcss/oxide": "4.0.0-beta.10"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "648799ce7067f9f8c51186175a50d72afcbe813d", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.0-beta.10.tgz", "fileCount": 5, "integrity": "sha512-Hyrc4W8TVvUKU3HXO4orkVSbZz7Ot2ACwTxWRWTEsG9XBuobKoTwHswLmXV2jRGH0PXUN+I2L/vW9ESO4VBFPQ==", "signatures": [{"sig": "MEQCICA5qk9ofapCS2SlvelM4IQx6AB3CNMf7lTTxzufUi5jAiAJHpRgpoywcl3OJbdEBcn4cCWQuQs9w53XxMCnyFIPeQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.0-beta.10", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11184}}, "4.0.0": {"name": "@tailwindcss/vite", "version": "4.0.0", "dependencies": {"tailwindcss": "4.0.0", "lightningcss": "^1.29.1", "@tailwindcss/node": "^4.0.0", "@tailwindcss/oxide": "^4.0.0"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "40360987491d148a0dbd2d9e257dd7150b70e08c", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.0.tgz", "fileCount": 5, "integrity": "sha512-4uukMiU9gHui8KMPMdWic5SP1O/tmQ1NFSRNrQWmcop5evAVl/LZ6/LuWL3quEiecp2RBcRWwqJrG+mFXlRlew==", "signatures": [{"sig": "MEUCIQD/NuxvEhvcycJJB9BNVZLp29qi8BqvL9lx9qWmSw6yKAIgHSpHvLYUqE70jQ1BsXUB19qEGsxwJpaRM92WMqr5EWQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11154}}, "0.0.0-insiders.515a9bd": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.515a9bd", "dependencies": {"tailwindcss": "0.0.0-insiders.515a9bd", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.515a9bd", "@tailwindcss/oxide": "0.0.0-insiders.515a9bd"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "6c929b147f62ea1223b81edd8943396f1d04f55e", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.515a9bd.tgz", "fileCount": 5, "integrity": "sha512-cBH3wPDTBOS2dvntYYzsMtnKUIAXLUDUm2ttdJDxcQFqbH/s0mdyg+697wY6Hkya0FB0RXbgb0dx2k/UFvpesQ==", "signatures": [{"sig": "MEUCID1u4hHiW9vWDv18Ee9c4Ap4ENZMG54gGRuuVIMYR8+hAiEA66WQeiFNrgG6IbagpPlaUTspM8eXVD3om/VKUz3KnnI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.515a9bd", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11220}}, "0.0.0-insiders.28008f1": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.28008f1", "dependencies": {"tailwindcss": "0.0.0-insiders.28008f1", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.28008f1", "@tailwindcss/oxide": "0.0.0-insiders.28008f1"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "836999e38673b5fb5a4b46b4fa782a9f15f0e67a", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.28008f1.tgz", "fileCount": 5, "integrity": "sha512-UoXREf6/QSFjf4BwRUlpTow91TOBwOkbBTwOpo9r63hKAb9a3cfeJvMczrA6fKpIhchBj8q+VVyWcDAqeF1LhQ==", "signatures": [{"sig": "MEYCIQCrKJSkKZ/ECYNbrMQS/gGIECXqhKeI3YzWKnfmndQujQIhAMf2OzTb8Z+6RZawOnhmsLDWF+9mbQXCm3dF8Eqor/NJ", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.28008f1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11220}}, "0.0.0-insiders.e1c084c": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.e1c084c", "dependencies": {"tailwindcss": "0.0.0-insiders.e1c084c", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.e1c084c", "@tailwindcss/oxide": "0.0.0-insiders.e1c084c"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "de5efbbac9aee271cb7c1e0b3033ef8a4f9c4ba6", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.e1c084c.tgz", "fileCount": 5, "integrity": "sha512-xHhQ2Dy2Y22O51Z42LY9AJF2N4YYZt8079lQQkSArqiQb3UBgFkCcX+kXcXVRjLSabMaREd/fX1/N9vCpcZLHw==", "signatures": [{"sig": "MEUCIQDPGs5uXOawZNONKGSJtH5QJ9ZkgBp9YU14hcbVSNUetwIgWDuIMnhNA5k/x32aPcnfvuBAwBDb8VYQams477duKRo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.e1c084c", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11220}}, "0.0.0-insiders.d66d7ab": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.d66d7ab", "dependencies": {"tailwindcss": "0.0.0-insiders.d66d7ab", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.d66d7ab", "@tailwindcss/oxide": "0.0.0-insiders.d66d7ab"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "48b580a527dab5e7def1b87dd405bfa95282500d", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.d66d7ab.tgz", "fileCount": 5, "integrity": "sha512-3cA1Db5/zFfyJ+4jGmnpFf/WBUZzh9tp6zKur0gLgClUeXohudidDeuiqPCQIUSS5ecI0sahK4nqg7jqrhRh3Q==", "signatures": [{"sig": "MEQCIH4ceqaV3DQecKShz4B10toRq0Cy1cW/oUlFKvbZNsGhAiAYZAAi1E6a23coV3FT+w1GxEaV60jhM+9SOSN5Xh6YOw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.d66d7ab", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11220}}, "0.0.0-insiders.82ddc24": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.82ddc24", "dependencies": {"tailwindcss": "0.0.0-insiders.82ddc24", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.82ddc24", "@tailwindcss/oxide": "0.0.0-insiders.82ddc24"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "8e28ca3e64c4523d261b0f7f327779b73aea46b4", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.82ddc24.tgz", "fileCount": 5, "integrity": "sha512-bk/oAv6awF3vMO5rlvG7WGLsPgSH+HQmEWzp24MlZE1g1OtnhprocD3wSbKfsHyjv+bAOMsbMifQ6wCG6j/WAw==", "signatures": [{"sig": "MEYCIQCacVC+6w88AJLxJrV1srL2dasOoM8+AY5ISPqUSl5DMQIhANcIa3aSrO97t29hxJF17tNpODP/20fHxlzm1Xr16IPH", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.82ddc24", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11220}}, "0.0.0-insiders.b009afa": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.b009afa", "dependencies": {"tailwindcss": "0.0.0-insiders.b009afa", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.b009afa", "@tailwindcss/oxide": "0.0.0-insiders.b009afa"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "b51e9fc910a3785d8d716e6555258fce30841e97", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.b009afa.tgz", "fileCount": 5, "integrity": "sha512-vObkP32GqAB7GEF9PzjRl6ee/MJN1e1m2Ob6QnYCDhmHjQEcYAK8oTGAFVEnnWlmNOVCXW+JFwygJd3neHyeFg==", "signatures": [{"sig": "MEUCIQDKkfV0xIk2G4JrUN+UTktGuxh2A5Gt8W9p49AhLdNQBQIgXWyWSUkl7/KXDfWmwSeH/V3GM9oDvu5LnqK/BlRSGLk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.b009afa", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11220}}, "0.0.0-insiders.86264a9": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.86264a9", "dependencies": {"tailwindcss": "0.0.0-insiders.86264a9", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.86264a9", "@tailwindcss/oxide": "0.0.0-insiders.86264a9"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "28629929524654060b5581960d89f69efdd2f864", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.86264a9.tgz", "fileCount": 5, "integrity": "sha512-qdRyvQH9nDKH3+K/LCcYKkJ0UwOBXPE2+zl3sqzFXHng/AnIRwkZsS6ZCUPORdhqE7ceUCX7bCTL4PNX+eCRTQ==", "signatures": [{"sig": "MEQCIF6Y/mn9G1QctSTV0O8F20D0f+lQBW0MFvsJsiTlX0KUAiBQfzFciJoKXr93LXtj8HBvsvEWPrQAoS1sAP9JzjqkIA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.86264a9", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11220}}, "0.0.0-insiders.e02a29f": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.e02a29f", "dependencies": {"tailwindcss": "0.0.0-insiders.e02a29f", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.e02a29f", "@tailwindcss/oxide": "0.0.0-insiders.e02a29f"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "6d121c7bb1959d4d57626306f9d9ce3713082e2e", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.e02a29f.tgz", "fileCount": 5, "integrity": "sha512-Wlvl0d7wWH3jLoIkaWofSNOYknOUIKbEq8SBVF3iEyLal3zH6PsjjnLcMjfNS4mJyrg+6S/wS4gIA0SWDEzNig==", "signatures": [{"sig": "MEYCIQCsCGkbt2To43H9OQz4dz37MIRLuFGMGpEWs0yQgibvKQIhAPg49obcUPvqGhC7ltUb6u5OIvToVV1uOYW9vKd3WR1C", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.e02a29f", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11220}}, "0.0.0-insiders.b492187": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.b492187", "dependencies": {"tailwindcss": "0.0.0-insiders.b492187", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.b492187", "@tailwindcss/oxide": "0.0.0-insiders.b492187"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "cb11b5864a1a670ea97941f5e4e7a13e2b5e4f99", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.b492187.tgz", "fileCount": 5, "integrity": "sha512-Kaqh4ke7M8ffmojJnHykc8lcWakwZJF+aYPGeQ7uuuzWs9elSovdz+oO904A3IzNWXUyFlZhbLpAu4+QYz54Wg==", "signatures": [{"sig": "MEUCIECD056W5oqNXEshhkgUXnp8JBIAPJCe0f5pIyoG0yreAiEAk5IY4zloPY3EME2OBjhVuMbUPRiXp2bN4TgTBWChWAA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.b492187", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11220}}, "0.0.0-insiders.2d3c196": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.2d3c196", "dependencies": {"tailwindcss": "0.0.0-insiders.2d3c196", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.2d3c196", "@tailwindcss/oxide": "0.0.0-insiders.2d3c196"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "23509a04df3f2d422a975675089bf9f2b9bc18af", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.2d3c196.tgz", "fileCount": 5, "integrity": "sha512-Q/g4JdmIpgZ1AwVRbjn5YL3fk6pFIRytGw114wai8cJUpsueHuAYBgb3sdfbfiWraCqmCMsoyYCMzGDxlNPD4A==", "signatures": [{"sig": "MEYCIQCnkKBExaMN1VaAdsvDVdjAzCGsPYD61JQrPNmvy1ya4wIhAMlPEf46fYy5JPGtKvOXFmZhU2xxKpEl2JlWR2DHwxfw", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.2d3c196", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11220}}, "0.0.0-insiders.f237f59": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.f237f59", "dependencies": {"tailwindcss": "0.0.0-insiders.f237f59", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.f237f59", "@tailwindcss/oxide": "0.0.0-insiders.f237f59"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "c37f6412f0e1351117627a30e3df4bca9142a570", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.f237f59.tgz", "fileCount": 5, "integrity": "sha512-cRIbWUntMWxRT1NDi7xdGlhNvvEO9XAdagbJCJFj3JiXl7ZcVwHqSrDRLu83hJMSqLaM0ywDVEYmN9xOMZ0xJg==", "signatures": [{"sig": "MEQCIFLMoTx22CkDKP4bUvZwep7h0n1n6oQ98W7C44wQQfBBAiBWZHLyUQfDsKqH1APm/hxDP6iviWH2YFj46WVGijgHfg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.f237f59", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11220}}, "0.0.0-insiders.9bfeb33": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.9bfeb33", "dependencies": {"tailwindcss": "0.0.0-insiders.9bfeb33", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.9bfeb33", "@tailwindcss/oxide": "0.0.0-insiders.9bfeb33"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "06a0e60297246622fed81f7ec4a401eaeca664ba", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.9bfeb33.tgz", "fileCount": 5, "integrity": "sha512-eX3sbDHL4l0DjmGvU6woGoKPW3VadqHCKOLOwNvkyMzSnPgr3/QYDDHD554q/xhieZFBoP3g0GQqAoLQsxlxag==", "signatures": [{"sig": "MEYCIQDYjvJ8q74TfIPPgcYaNKBBkK0fw+qm6GzNdY5MuY1MNAIhAJvxzv4iM5Aq5xKENLrzUSuZwS+OmxIcat0cBw9fyEr/", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.9bfeb33", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11220}}, "0.0.0-insiders.125c089": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.125c089", "dependencies": {"tailwindcss": "0.0.0-insiders.125c089", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.125c089", "@tailwindcss/oxide": "0.0.0-insiders.125c089"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "1de84c3ee0c1d705840511a9df84085d232ad667", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.125c089.tgz", "fileCount": 5, "integrity": "sha512-j6NvEY0lNJwncji9VVCK7Y0HeZAZxvtofVXHvKPVOEGjbQi/7DfbRfg/7iDYOz3NbN4eLqzMa4Li7p++GHy6vA==", "signatures": [{"sig": "MEYCIQD1wnwdRUWzeP9E9R8UMkVdxy5nGy+ZCfz6mrl0LVfs4gIhAL5veZFj+unLt+VzYMbsPcnG7kZzUUrvqwKXCUzVRI2E", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.125c089", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11220}}, "0.0.0-insiders.3da9d61": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.3da9d61", "dependencies": {"tailwindcss": "0.0.0-insiders.3da9d61", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.3da9d61", "@tailwindcss/oxide": "0.0.0-insiders.3da9d61"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "07b88cf6a05db627caa46ee8ab681480bc1b1145", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.3da9d61.tgz", "fileCount": 5, "integrity": "sha512-vcq/PBiad7N1P1V7DWy7aE0YJMrGdzqQ0Kib1MdsBc9v1l+FcKusy0BU70zMq0k0gKryU8mrweBJepRVbS2r2g==", "signatures": [{"sig": "MEUCIQDV4CYpE6KjgEpmNzce23sCw3Irk+iaopzY1N3eiPojWQIgeT+M+PFmqP6kjhu6nmVKtuzsg6HFRB4bvO6EMy+gfag=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.3da9d61", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11220}}, "0.0.0-insiders.965048c": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.965048c", "dependencies": {"tailwindcss": "0.0.0-insiders.965048c", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.965048c", "@tailwindcss/oxide": "0.0.0-insiders.965048c"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "c3d5a0a798134804dca0a48f69d36a16852fa9d3", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.965048c.tgz", "fileCount": 5, "integrity": "sha512-xWHwOmbQkQ/dWDix2OapkKaYtJgg0PtBtuPE42pRxyP2XSS9uXsU8IRJvKFOoc1uZcxgFAE8PscsWUjXm7KoPw==", "signatures": [{"sig": "MEUCIQClIrIwVsWPqmFcsvw50cGnzsNKvByJbBJvZDIlLFLxqwIgI759MNY6DL/jImk4F+IvKejUmXZvI2nJStdCC2Zvg+A=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.965048c", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11220}}, "0.0.0-insiders.924dd69": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.924dd69", "dependencies": {"tailwindcss": "0.0.0-insiders.924dd69", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.924dd69", "@tailwindcss/oxide": "0.0.0-insiders.924dd69"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "1b6d19481fed1315037170ace1b119d0ff494ba1", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.924dd69.tgz", "fileCount": 5, "integrity": "sha512-jg1pCmt9U/s1jFDEnd/KOyfHO4eoViK/vu/Pi6PDjSvSidGClrOceZd2nSghECcqoBFMa6/JymTvxcTICZj1TQ==", "signatures": [{"sig": "MEQCIF6ppf0OMvWDOoU6arphp9TZeULeFNy++FJGNWWCQ/56AiA2+9yGCD6R09DIsRWerNWzY8wawbYTRLVD66g8xZSxbw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.924dd69", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11220}}, "0.0.0-insiders.9fef2bd": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.9fef2bd", "dependencies": {"tailwindcss": "0.0.0-insiders.9fef2bd", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.9fef2bd", "@tailwindcss/oxide": "0.0.0-insiders.9fef2bd"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "27971b4b02949df0527dd86ea184d6a99f754db9", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.9fef2bd.tgz", "fileCount": 5, "integrity": "sha512-zBv3M4UqgL+p97NJnDnaQ0aylE/UZRx1H3iLERhEoqInFF9MGBrGKRmvrMH+ZqHbUEE8LFccxFx9nAJp01azGw==", "signatures": [{"sig": "MEYCIQCoOwrqoASKq8KdYDwp9KCVLZyePJCSiL829Q21NgLRXgIhAMv02oV1LLpQlijpjCZmjb6IJPatCsHBpZvwPfQrDeRb", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.9fef2bd", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11220}}, "0.0.0-insiders.a4761ea": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.a4761ea", "dependencies": {"tailwindcss": "0.0.0-insiders.a4761ea", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.a4761ea", "@tailwindcss/oxide": "0.0.0-insiders.a4761ea"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "e63481c6e7c187e01db10f2efa897c50a49eb6d7", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.a4761ea.tgz", "fileCount": 5, "integrity": "sha512-TtE6rb1TxIEdmDMZm307E/7M1jHpMIVfY3OwMnaDy/6i+xP0lO7eQprE5z6T71NIrHR1RCQ6rn+apFUJH0FJCw==", "signatures": [{"sig": "MEYCIQD5C1Ue6crtIOKxryUS2SM70g+0hIlCzK9d/l3x0TiqOQIhAO8yiyFhYsCy0qOdotiQ49NbcqC9Mwj55Ivii108rIN6", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.a4761ea", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11334}}, "0.0.0-insiders.655209": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.655209", "dependencies": {"tailwindcss": "0.0.0-insiders.0655209", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.0655209", "@tailwindcss/oxide": "0.0.0-insiders.0655209"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "cdf9bcbfe677edd0e7a36b3c439b382e010bb939", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.655209.tgz", "fileCount": 5, "integrity": "sha512-Gf0VgRhc07Jh0gEpoPvTJ6T71oTB8eXz/ukJwnG0lanVnj8Oy767kWOT3L2X3DlV/35nL06/6rz+59onyWvEKg==", "signatures": [{"sig": "MEUCIQCP4n9h7Ur4ZGdVewjCmie+3AwXVwbG6ssaFBGFfMGvgwIgRoRzktMiHrQbKLek0ZGT073DPXPv+dK48rkOSwLfpak=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.655209", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11334}}, "4.0.1": {"name": "@tailwindcss/vite", "version": "4.0.1", "dependencies": {"tailwindcss": "4.0.1", "lightningcss": "^1.29.1", "@tailwindcss/node": "^4.0.1", "@tailwindcss/oxide": "^4.0.1"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "3b2f4ba884620710ea07326e230c87b727c14cdd", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.1.tgz", "fileCount": 5, "integrity": "sha512-ZkwMBA7uR+nyrafIZI8ce3PduE0dDVFVmxmInCUPTN17Jgy6RfEPXzqtL5fz658eDDxKa5xZ+gmiTt+5AMD0pw==", "signatures": [{"sig": "MEQCICeSagKTnyeW8A62YB7priz9z53Q4EIKYsvknLJbI2NgAiB2VGHmSQB57+3VNdHOvSWCm2GBMlJkdTpmtTF0pCAHZw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11268}}, "0.0.0-insiders.ea24995": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.ea24995", "dependencies": {"tailwindcss": "0.0.0-insiders.ea24995", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.ea24995", "@tailwindcss/oxide": "0.0.0-insiders.ea24995"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "93be11b0a5d6a481c1299d6e8d03c741d5ad4113", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.ea24995.tgz", "fileCount": 5, "integrity": "sha512-h4L0FHPdk21qVleEMel/OHz2+nZnEvy0H+W6HHQNgAuMTUKQKueXNzaV+F1pA15IodlNz8PFCj1sH00x0ddetQ==", "signatures": [{"sig": "MEUCIQC9BkH2BJoW6j9hoUXNt2BJ4dHitNrayRcQYEIVWrHuCwIgVi0uKazfecTd57JvbvCVbef5r5gT9RqA4JNw2gtGpOY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.ea24995", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11334}}, "0.0.0-insiders.0d5e2be": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.0d5e2be", "dependencies": {"tailwindcss": "0.0.0-insiders.0d5e2be", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.0d5e2be", "@tailwindcss/oxide": "0.0.0-insiders.0d5e2be"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "b862425fedafa5d40b34beb5880c950c7374868c", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.0d5e2be.tgz", "fileCount": 5, "integrity": "sha512-1dZGJJsMKXS7Ry35c1ttCZoOeJgRU94TrVyYlPft3oABv8Cg2xoNQ9zDNm6PTleh3uWk+QDdOdjw9K+D8MKgkQ==", "signatures": [{"sig": "MEYCIQCBe1yc8dSRFHTuKU2ALqOnbT8A40wh5KDrG4TFrsvkMgIhAIwX+0moemyigCD3lqrq+dWvH4/YXbyI5IyD1KMWJJe0", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.0d5e2be", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11334}}, "0.0.0-insiders.2242941": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.2242941", "dependencies": {"tailwindcss": "0.0.0-insiders.2242941", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.2242941", "@tailwindcss/oxide": "0.0.0-insiders.2242941"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "06a03705e0627651dcc8dca58fca23a399416251", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.2242941.tgz", "fileCount": 5, "integrity": "sha512-LPGc8rTQN8ko4H/mobhSoMtMAeZPWBEHXrUylg6zL4DVi61iq26Uvb5PX2UFGuB09hSkqXcJmJ2doMgIIcGROQ==", "signatures": [{"sig": "MEUCIGYZBIP8dHqlTGd1FQvmCoajmM79kBqrtl+SX4LUWQ6wAiEAxTzGLWPSbjEqHMsMpcZaGnWfKbE4FQWAaDTdsKvc8FA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.2242941", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11334}}, "0.0.0-insiders.c09bb5e": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.c09bb5e", "dependencies": {"tailwindcss": "0.0.0-insiders.c09bb5e", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.c09bb5e", "@tailwindcss/oxide": "0.0.0-insiders.c09bb5e"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "6be4d140198935ef01dd1220d5d3f9b3a24c18dc", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.c09bb5e.tgz", "fileCount": 5, "integrity": "sha512-GcoU7iYsWHZBW+yOjYQkAFcuERzZA2c/M5J1vCIXRzg3ZUGf+GsEYgaY9Avl0sSDOBSEeQmvRPAxUCY1ZQ9TUA==", "signatures": [{"sig": "MEUCIEvDGSjcS9g0em8OwBJ0EsaIHQi825Bty/8IcVveAV0BAiEAn5Tc8XaY8skQczjpAgs2fuSa8CLRj3MJ1vFzEGG42YQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.c09bb5e", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11292}}, "0.0.0-insiders.0589d7d": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.0589d7d", "dependencies": {"tailwindcss": "0.0.0-insiders.0589d7d", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.0589d7d", "@tailwindcss/oxide": "0.0.0-insiders.0589d7d"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "e8af92c901282301a67216c79ba930a20f04cf33", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.0589d7d.tgz", "fileCount": 5, "integrity": "sha512-HMbsukMD3CXFk3rbdeGmF2QgLLhxXHQMsOtE6f82noZok/TE02JLSFQI4fq7icsMCZfJVSGeAjG3Pu5Hwf4O3A==", "signatures": [{"sig": "MEUCIQDw/xjS8B9YBuc4nxof0vQ2r6bKrs/iZHtkShT1ltN6KQIgauOzA26ZOPjE94OD6i74Jrxkj6rBB81WtbpbaEwiP9s=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.0589d7d", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11292}}, "0.0.0-insiders.d85e9cf": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.d85e9cf", "dependencies": {"tailwindcss": "0.0.0-insiders.d85e9cf", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.d85e9cf", "@tailwindcss/oxide": "0.0.0-insiders.d85e9cf"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "1266aa23eaddb268e9dd7dc4bfef7d18731dcfe4", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.d85e9cf.tgz", "fileCount": 5, "integrity": "sha512-a/yYZgfZQho5wusjrK7Sgk0rHlR27V5LRV3LxGxcancbSHjAelvtRFiocDsIQDZNlO137PMlmVlD3/2fS3MElw==", "signatures": [{"sig": "MEUCIHcFuLJN54G+bNDjoxqLD0gERX3X+JFW9E+gfm63AOPOAiEAxm5VO1Bpn7N95vxZdqQbOac2Wg04DQ3Z4DIE7Q68gvg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.d85e9cf", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11292}}, "0.0.0-insiders.88c8906": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.88c8906", "dependencies": {"tailwindcss": "0.0.0-insiders.88c8906", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.88c8906", "@tailwindcss/oxide": "0.0.0-insiders.88c8906"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "c620fae2023f4982072e33ad349e77c798ba602f", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.88c8906.tgz", "fileCount": 5, "integrity": "sha512-eOOjB4OV+FX0QkwN2CIVM3AxWIVSphYfp63sccHBD6LudBT/Agd97AaxENKDvtCRUmhlHMihBNcFpswn8XPxqA==", "signatures": [{"sig": "MEUCIGtRI2vF1PbXCgdlsPp9T/KQKRP4Ai9DFF/lOZjB0SuXAiEAtm+kXW7FUbYfC4Crpc0sQF8x5A7t7uWq/7oOs4Ir7B8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.88c8906", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11292}}, "0.0.0-insiders.3aa0e49": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.3aa0e49", "dependencies": {"tailwindcss": "0.0.0-insiders.3aa0e49", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.3aa0e49", "@tailwindcss/oxide": "0.0.0-insiders.3aa0e49"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "b2a07bd2a32d6a639c6e43a15f52f3563b7198cb", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.3aa0e49.tgz", "fileCount": 5, "integrity": "sha512-zQ4fFsCvC11h38swFifR+EN12ZIdFOL1nBkQoA+oH3XNiLjhWe+fBavulcmNTjcSv55jRikwtP+dtktDfqTskQ==", "signatures": [{"sig": "MEUCIBM+FChcBPWTIfNbx4EM3oMqig3BKHjdB1wOgGzqFnFbAiEAz5FBwHAYFkyHTiUEyf7oMyNZYW3x7779xJvgTqacpyo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.3aa0e49", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11292}}, "0.0.0-insiders.60e6195": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.60e6195", "dependencies": {"tailwindcss": "0.0.0-insiders.60e6195", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.60e6195", "@tailwindcss/oxide": "0.0.0-insiders.60e6195"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "e25c9872818fb2bc3a2e6f0ed7444f64182353f1", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.60e6195.tgz", "fileCount": 5, "integrity": "sha512-C5EKR5kNrbNMrIKPxPpgSuC/OkxpePNuxV3llNah7ZwhEPQf/tMCyZVFYY8CbUPTDp3RNVaFy9t2nTd0gu9iEw==", "signatures": [{"sig": "MEUCIFWxVqFVfOIPlfClS63OTpKUbV1Zw75F49iCx5gEkiG+AiEAjrXybTnr8RWD9R8aM2hxqIyIlZieFyUJyszwV60vfj0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.60e6195", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11292}}, "0.0.0-insiders.deb33a9": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.deb33a9", "dependencies": {"tailwindcss": "0.0.0-insiders.deb33a9", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.deb33a9", "@tailwindcss/oxide": "0.0.0-insiders.deb33a9"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "fd64170b5a0dc7a48995c66d44df4b770ff2634b", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.deb33a9.tgz", "fileCount": 5, "integrity": "sha512-/9QzAuVaYPm9FZ4pp//ySbZDLcW833nqMNCQfhgjICnOfdPMoJqNGtUgDAGkEyER400Zyv7d2nyyeD8G5JrsvA==", "signatures": [{"sig": "MEYCIQDa3GrZugvBZp48H/4pyoC44zBpCgHhlxWhTAi9qenY1AIhAK2UFhwx6SL1oygKfRBGdQrLKe07NOd8P03xlO37lVoS", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.deb33a9", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11292}}, "0.0.0-insiders.9572202": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.9572202", "dependencies": {"tailwindcss": "0.0.0-insiders.9572202", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.9572202", "@tailwindcss/oxide": "0.0.0-insiders.9572202"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "f8c3104b3b5417d49e9c1767d63a906ab107a746", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.9572202.tgz", "fileCount": 5, "integrity": "sha512-1CvLNaCd4cyYjozhIXnQNb0WQVDthgzV75orA3QdDSCkGH2oRB3kiqXYrT3eRVTaI5UOVr1VrU7gVXOXVxqo4Q==", "signatures": [{"sig": "MEUCIEsNuFf7g+B89iWOW8VIS6X4F91BNQcag+zvcCdH6SlQAiEA6tkDT0pykI6nI0zaJCVquxDN2qkvIllovQSiwbUx8f4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.9572202", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11329}}, "0.0.0-insiders.35a5e8c": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.35a5e8c", "dependencies": {"tailwindcss": "0.0.0-insiders.35a5e8c", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.35a5e8c", "@tailwindcss/oxide": "0.0.0-insiders.35a5e8c"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "8f471d3b687cb754c5dc14ee2b4d4543da36e2eb", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.35a5e8c.tgz", "fileCount": 5, "integrity": "sha512-sOl2dKqd3hpY9XmW8Wo9laIZ7hN7hK61M+QM7+uOYYyIRaaQE90x8Q9mKFaJNXeTOCd0Pq8TrE9a4wEcpp7byw==", "signatures": [{"sig": "MEYCIQCOPYCCSnmBx2wJeQbGYU17WIvB1si8IAvfn/LguVqr7wIhAITq60e3kAkEiRSrJG18E4J08MQcf5Cp+IbImq8TnxVN", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.35a5e8c", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11329}}, "0.0.0-insiders.7f1d097": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.7f1d097", "dependencies": {"tailwindcss": "0.0.0-insiders.7f1d097", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.7f1d097", "@tailwindcss/oxide": "0.0.0-insiders.7f1d097"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "65554c5719daf98661be2b18b027cbdbbec00af3", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.7f1d097.tgz", "fileCount": 5, "integrity": "sha512-U+wJ2QJI4w1dXZV4XsL96uPzd7u5BjtKoPYqBF+BzqO1ROz4yaFo+cOyo3jX3LQnzxuETIRbPS7vcHW6nakXnw==", "signatures": [{"sig": "MEUCIQCkz5DiXVWDSUm+UognaSd6GGyCuIuDf//AaW9JB7yZsAIgUYWhBdqMWjFne0/QfIqhKN0P0teoOqkn3pfp1YtpCpw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.7f1d097", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11329}}, "0.0.0-insiders.4052eb2": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.4052eb2", "dependencies": {"tailwindcss": "0.0.0-insiders.4052eb2", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.4052eb2", "@tailwindcss/oxide": "0.0.0-insiders.4052eb2"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "6eb885fc5881091b8ae7dbcd82b51e6bb84a8abc", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.4052eb2.tgz", "fileCount": 5, "integrity": "sha512-09wvf6JtqwuA10L/PDnqMC6sGzwqYV4pubPVxUUfK8i2qkLQ/huTWnlipq+PdmKH7vdbTweIFhhp72tkMjXubw==", "signatures": [{"sig": "MEYCIQDcFwesLLDifdQBtq6lWJ4B8mYQ3vSt35xvKOvR8OnPPQIhANj4spx7fulanHheQAWIvAUAJBIvlj0+xdgAruUU3FwF", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.4052eb2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11329}}, "0.0.0-insiders.50bafce": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.50bafce", "dependencies": {"tailwindcss": "0.0.0-insiders.50bafce", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.50bafce", "@tailwindcss/oxide": "0.0.0-insiders.50bafce"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "4eaf1a8de7c97ff19bdbc15bd5ee09c649e61059", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.50bafce.tgz", "fileCount": 5, "integrity": "sha512-txiS9JJrG3vFPn3SGxN3LAh3PPAH3ptbc3iv8bc+qS2DCIxZtt7ypL+04cC5A2Ps//sLhMY3n/NOypDRX2PfDA==", "signatures": [{"sig": "MEYCIQD3OpNjs2vyavZhcVPtsfkWZdQmwktC/468qzL6Waf+OQIhAIBz2b07HdRq6Qx3uAiCgg76v9lOYkyKzugiJtPOePcN", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.50bafce", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11329}}, "4.0.2": {"name": "@tailwindcss/vite", "version": "4.0.2", "dependencies": {"tailwindcss": "4.0.2", "lightningcss": "^1.29.1", "@tailwindcss/node": "^4.0.2", "@tailwindcss/oxide": "^4.0.2"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "bfdd4e09dbb76b1aea6c4ead0b56fb66f8d872f7", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.2.tgz", "fileCount": 5, "integrity": "sha512-Q9MHw5MpXAPF+z3ZU7nUOX2ysUFkvdCo0FNoge9TNLD4JxCkoe9pru6C7wOzWE8NKkD3aOjngFpaPTVVP07hug==", "signatures": [{"sig": "MEUCIQDCirb2qMdtPZ5e/ntMunQ+jyHMJ+ZoT+nZ7RvCY3iNWQIgU4VsuzDck0Jhs/b2siVDbASk9+2s9gUW06eo9p5A6Yw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11263}}, "0.0.0-insiders.b7436f8": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.b7436f8", "dependencies": {"tailwindcss": "0.0.0-insiders.b7436f8", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.b7436f8", "@tailwindcss/oxide": "0.0.0-insiders.b7436f8"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "dacb2b21f2418eb4e1af27ac839d08664738b6fa", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.b7436f8.tgz", "fileCount": 5, "integrity": "sha512-xpT3UWmNBrzsGIivnWFKMy6apbGo51JBjMzW2ULiYsciOsYDJHN+Ae4kxRX85iwentvA8+hyiwWR9UkL7UPUFw==", "signatures": [{"sig": "MEYCIQDs4L1fOHIqVyYBdiNGd/HEohRYGY7nIEXsQwgOJp+i1QIhAMUdeQ2co35Q/OK7a0Fh2NojyIW2w2PWmsD4ujTqPdjM", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.b7436f8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11329}}, "0.0.0-insiders.b7c3f50": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.b7c3f50", "dependencies": {"tailwindcss": "0.0.0-insiders.b7c3f50", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.b7c3f50", "@tailwindcss/oxide": "0.0.0-insiders.b7c3f50"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "c9dd3fb0e715e55443b0ea4dde8b921f517c2ee1", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.b7c3f50.tgz", "fileCount": 5, "integrity": "sha512-Vz5HnYaPTttPqpe1meM92RtIGqQJ+1e3PCPtfVITxj9u+XqB5q5SDuD88r5cxAYrumUW+2TKv34p66GlugZLOg==", "signatures": [{"sig": "MEUCIEOBn9X9RyI5Txiw8FTBx9OBp321TrMKv+YxIvZeFPqSAiEA9ltIvQohG6kyDxktlvKkReOMSFFtulTwgqYdaXkWo84=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.b7c3f50", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11329}}, "4.0.3": {"name": "@tailwindcss/vite", "version": "4.0.3", "dependencies": {"tailwindcss": "4.0.3", "lightningcss": "^1.29.1", "@tailwindcss/node": "^4.0.3", "@tailwindcss/oxide": "^4.0.3"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "5c9a9ce59d97fe6330bb2e1520e6b793a4b38158", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.3.tgz", "fileCount": 5, "integrity": "sha512-Qj6rSO+EvXnNDymloKZ11D54JJTnDrkRWJBzNHENDxjt0HtrCZJbSLIrcJ/WdaoU4othrel/oFqHpO/doxIS/Q==", "signatures": [{"sig": "MEQCIHhZgWd1P8o7sNucFhoXxe6s/w5W2tqef2//trgNJkoUAiANc62kwzSrn9bAfxY/uEztxi4r7MU2KffAuERg0wPK7A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11263}}, "0.0.0-insiders.b8d8548": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.b8d8548", "dependencies": {"tailwindcss": "0.0.0-insiders.b8d8548", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.b8d8548", "@tailwindcss/oxide": "0.0.0-insiders.b8d8548"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "7a23c667005e02e979f4240340f766666f9b3e52", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.b8d8548.tgz", "fileCount": 5, "integrity": "sha512-/Tkchbhi+HedXfHAGBIdOxYFBCXME3P70yAwgSQHpG9QIoKacZq48+qxpGkC7u1NYmFMuMiOZ6g/Cwc/n+kaUA==", "signatures": [{"sig": "MEUCIFyfp9ViGLawryvFzwlzHOSrgWtqQ1KPD4kDlzbjArJdAiEAjhYgFdAlbhCJE0+P/O4GwuCN+CYvw/p0U30p42tFxn4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.b8d8548", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11329}}, "0.0.0-insiders.ac202ff": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.ac202ff", "dependencies": {"tailwindcss": "0.0.0-insiders.ac202ff", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.ac202ff", "@tailwindcss/oxide": "0.0.0-insiders.ac202ff"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "d9f28a9cf3b851cb56f684667203bc29b90dcfee", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.ac202ff.tgz", "fileCount": 5, "integrity": "sha512-5HSkjDpyXdTczXbLhKQanhPpBX4F5oRQ0bZfj0aj7O2IgmBpiZE2xihJ03kG1MH4lpyHCNiP+gtHC6CKXtwKvg==", "signatures": [{"sig": "MEUCIQC7QDPkg7sl1TAoCIbiLbjMg5wcW9afSxylxrWWYDc8+wIgZHFWegH2xloQapNfh5TZ2iQEtre5PqnBc2rbvzReNag=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.ac202ff", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11329}}, "0.0.0-insiders.06dfa39": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.06dfa39", "dependencies": {"tailwindcss": "0.0.0-insiders.06dfa39", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.06dfa39", "@tailwindcss/oxide": "0.0.0-insiders.06dfa39"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "c6f530ccbaa101aa4f5eb374c6f1c37ec6971251", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.06dfa39.tgz", "fileCount": 5, "integrity": "sha512-u0qNpbeJwZLmVmKvFbH4Bgwplr+1YDPDQOci5DBA1qtL3dcSGm/6Qk1lqrsYlp2Y3M7M7dz079wtH/SXvg3kxg==", "signatures": [{"sig": "MEUCIEvDdOd4JuqBPTTDGmEbCgQfM3bBIip/GTPg7bT2lqvsAiEAlttkNczSi4La5nUeYxMMXOT1bXe6vigA8Bidh0r+/40=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.06dfa39", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11329}}, "0.0.0-insiders.e1a85ac": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.e1a85ac", "dependencies": {"tailwindcss": "0.0.0-insiders.e1a85ac", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.e1a85ac", "@tailwindcss/oxide": "0.0.0-insiders.e1a85ac"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "484c36a84c82c655f6c2194fba73afcc75aab73e", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.e1a85ac.tgz", "fileCount": 5, "integrity": "sha512-uVpInKvwfbjvCPDBRhLH4Y5GE9Aa7q8383Ih8fNOwkq7trtyIN23iEEqx553OW7uU8eMF2A7+VLFHuFI/6IF5A==", "signatures": [{"sig": "MEYCIQCuUo/KonhE1/B+wRoNmMxphbshNmoIEQOdvORyhVWrGQIhAPHInvV5+T6rYc9N19OjpLmGQvyrnOzoXHm32JB38bgH", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.e1a85ac", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11413}}, "0.0.0-insiders.9fd6766": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.9fd6766", "dependencies": {"tailwindcss": "0.0.0-insiders.9fd6766", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.9fd6766", "@tailwindcss/oxide": "0.0.0-insiders.9fd6766"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "0bf9cda0ef59f04c6a5e13be51d2ff2cb85a0fd8", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.9fd6766.tgz", "fileCount": 5, "integrity": "sha512-v913+Ik<PERSON>7MLuaqShiedWX3XEdEjGZtcNeHomPNJEHsl8329X/HDwXqspvTNJOMfEgLQW3ab+9x3XmKVCaS5wGg==", "signatures": [{"sig": "MEUCICPjlg4o9yzdI4t0pnKJAszezqiIZG5qaS3WrQhyl/JoAiEA0erz4goYIP3U8fkJMtXosQhQTr/A+qvQcAi1SWJowhE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.9fd6766", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11413}}, "0.0.0-insiders.3b61277": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.3b61277", "dependencies": {"tailwindcss": "0.0.0-insiders.3b61277", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.3b61277", "@tailwindcss/oxide": "0.0.0-insiders.3b61277"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "27d8029b07042524c4814de18c102036e274ee35", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.3b61277.tgz", "fileCount": 5, "integrity": "sha512-AIhOQ5HD+UcQwdLduKPJVe42cwBpoamzvasFgM9asV5XdiwoxND+mHKScUzmwjLw53Wxa4qEj6d0fa9zlyOkhg==", "signatures": [{"sig": "MEQCIDJX0u8se/yjNzK8pTnov1Lq0X0gbVSOjrGdhiHQPGD5AiBZeMs5ENNnLXeY/yQOXCpynRsSs9PBeeVw8CQT95vCkg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.3b61277", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11413}}, "0.0.0-insiders.82d486a": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.82d486a", "dependencies": {"tailwindcss": "0.0.0-insiders.82d486a", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.82d486a", "@tailwindcss/oxide": "0.0.0-insiders.82d486a"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "76a0c031c5ca627e9169b2aff10bbdb45c6992ff", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.82d486a.tgz", "fileCount": 5, "integrity": "sha512-r/Ky4Zo+Ji3kXssTccpPU2puq2xkPP160Q8K6e/MWqOv4pdF2DhisN0J5IuJ7iUJ3DAVDcBqYygI7ULcEGQDYQ==", "signatures": [{"sig": "MEUCIFIRrbwLCYsAqtp7BQdOCfd/Q3b4i6ay6Wkiv4kIODtJAiEAzuq/EGNWL4e+W6fAEa9j3wTqUPFh3Stvr/muXhJitw0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.82d486a", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11413}}, "0.0.0-insiders.3f8e764": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.3f8e764", "dependencies": {"tailwindcss": "0.0.0-insiders.3f8e764", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.3f8e764", "@tailwindcss/oxide": "0.0.0-insiders.3f8e764"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "abccd9d1baa0528358084c271e497fbac2d8e4ae", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.3f8e764.tgz", "fileCount": 5, "integrity": "sha512-hyA+TS+gP+HCHa1CGVUsXofLW5Zkn82lr3uj4Y5xM3BhrYUHnrnT8zV2f0mWTIze0Zn2AM602CpYUHfBLllkMQ==", "signatures": [{"sig": "MEUCIQDZple5wORhY/gTDq+MBdhQLFCD4qT4Iu6MlYVZfNWcEgIgbawaQw+WdkgLlMNIvVNqQaFCmTH//l/SBj7PSaFtvfQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.3f8e764", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11413}}, "0.0.0-insiders.0ecc22b": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.0ecc22b", "dependencies": {"tailwindcss": "0.0.0-insiders.0ecc22b", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.0ecc22b", "@tailwindcss/oxide": "0.0.0-insiders.0ecc22b"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "0f3537138f2ae3773a2b3d2f545fa7f41eadf5af", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.0ecc22b.tgz", "fileCount": 5, "integrity": "sha512-5mP9pAiTLRgb+G0dnv1GU3f4qoeqtDynCKdEhU3GXNU6wTsO1rmB07ElCybdIBy5D51258CZap8aK6QyWJDIlA==", "signatures": [{"sig": "MEUCIQDdN8O5Av78ZSiG0GlHVlVN6w8cgyRKTPURT8IWjM4BVQIgUJrucS+7OXBQeYIX2CZ6KN40dS+uSZydBIxgr7pNQeM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.0ecc22b", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11413}}, "0.0.0-insiders.837e240": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.837e240", "dependencies": {"tailwindcss": "0.0.0-insiders.837e240", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.837e240", "@tailwindcss/oxide": "0.0.0-insiders.837e240"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "5d72c8bf0172c6502d7e567948a44932cfe0799f", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.837e240.tgz", "fileCount": 5, "integrity": "sha512-VqCvwCib4+tF1/4xxUVQg2WMVSZwKCH4ZIiBN8VABWFrUvxdfXf/KcRCQvhkN2rW/o6iJMKfYNeCl0W/SuSA7A==", "signatures": [{"sig": "MEQCID1pYrpoJ5bHWU81L8bfNXuBvrWgr1jRXDwvMjBJsrTNAiBjr4h9K9N6rVQdKN/EPNQKOhTyEe6u4sk3TUhCotjKYg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.837e240", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11413}}, "0.0.0-insiders.d566dbd": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.d566dbd", "dependencies": {"tailwindcss": "0.0.0-insiders.d566dbd", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.d566dbd", "@tailwindcss/oxide": "0.0.0-insiders.d566dbd"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "e69f117ed39d85f88f8e9b0145878ec7da0ea770", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.d566dbd.tgz", "fileCount": 5, "integrity": "sha512-6SitpVCSvB182nLOOhQqBPT8dUFwmVuJg/CrldYMMw3di8whNY+8Glf56lWAbV+WaS0AFoKMMc6i1iFffIncPw==", "signatures": [{"sig": "MEYCIQDYcTOHIgDw9qrK9zhZiQ2TpFE3ckeoSpAb5E7/ioMc4wIhANl1W7ePCHB8Lyj5Kf5k6/7cQz8q55sMFpmKrLDKM+jv", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.d566dbd", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11413}}, "0.0.0-insiders.144581d": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.144581d", "dependencies": {"tailwindcss": "0.0.0-insiders.144581d", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.144581d", "@tailwindcss/oxide": "0.0.0-insiders.144581d"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "2198f87a0b52bf32b6b8a5ebee33b890b53e450e", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.144581d.tgz", "fileCount": 5, "integrity": "sha512-G9hqpp5ZFPa3rzl2LY3QAja9X679Q5aQ7n1wsXw5LZVFV6d1X1sma5jRwZcrPSO7xV8Vi+q+2AOUzcq2yfFXXQ==", "signatures": [{"sig": "MEYCIQCQbXof6OCfp0B0zFvrDJ9wtP7KHHXNTs12WHQSeqs+GwIhALEs3GmwYHlzKumAgw/ZwbSVFuqrLbyzYn/SKOYrTG+M", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.144581d", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11413}}, "0.0.0-insiders.1e949af": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.1e949af", "dependencies": {"tailwindcss": "0.0.0-insiders.1e949af", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.1e949af", "@tailwindcss/oxide": "0.0.0-insiders.1e949af"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "61b1f10d56ea6b3f806b5f4fa1157d7e9324fbf7", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.1e949af.tgz", "fileCount": 5, "integrity": "sha512-IJFQbCsqyc+8DXZzxJycS53cRn5SbHwse3bUEuQyULmvsniZpLN+qpsHXCYg7+N6fJpfnzRCI2cV13X8CES9vQ==", "signatures": [{"sig": "MEYCIQDaVhqgUfVTy+nZ73lIlo5ezsjCXqzFm9ov0qM4Y/DIGwIhAIFT9CzVrPuPFIdXaVwI5Xwx5XIfpsyKJnXxaAZEQTMB", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.1e949af", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11413}}, "0.0.0-insiders.83fdf37": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.83fdf37", "dependencies": {"tailwindcss": "0.0.0-insiders.83fdf37", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.83fdf37", "@tailwindcss/oxide": "0.0.0-insiders.83fdf37"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "724229c9d3f00541794c81ee56c5bff6bbb32307", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.83fdf37.tgz", "fileCount": 5, "integrity": "sha512-okMNbSNUnTl2WAMCgsdMY5FVPkdXCjtPfVfkvv9By+qFVE0YnBi0Lb7t6HM4oJqiWjL5cKvLksPJ2A+FiEwvJQ==", "signatures": [{"sig": "MEUCIFbBFC1uzScgyBfLNFhVwodi3J52Zok2/19FsCYV5ObrAiEA08B8HSwcudY28ZOPKV4DxcQEQ9K33TKWepUyaVFllr4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.83fdf37", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11413}}, "4.0.4": {"name": "@tailwindcss/vite", "version": "4.0.4", "dependencies": {"tailwindcss": "4.0.4", "lightningcss": "^1.29.1", "@tailwindcss/node": "^4.0.4", "@tailwindcss/oxide": "^4.0.4"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "40db7652d22f66edf897340872a967ba7eff4f7e", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.4.tgz", "fileCount": 5, "integrity": "sha512-zrWGbluPeXeoetUQoDFmt1dQIeiOBThfznla7zPIqST69rMmiDD4SZwJrHVoL5CvXz06AYQXz/M/jELSakL7Rg==", "signatures": [{"sig": "MEYCIQC2yYv3QqM3c2OQzgEyosiSheJ66jimNSAp7mZ6ecpq5gIhAKhh/nGXRvwomoA2urebSxDISpgEEFPO7dVwy0gYnTBc", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11347}}, "0.0.0-insiders.25b4278": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.25b4278", "dependencies": {"tailwindcss": "0.0.0-insiders.25b4278", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.25b4278", "@tailwindcss/oxide": "0.0.0-insiders.25b4278"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "9a1e55246b88527c7015f4955119024079df21b1", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.25b4278.tgz", "fileCount": 5, "integrity": "sha512-tmEkSNMavdSYpbZxCuawSbB/3OABOdebZo77ye/xaDSlAfLw+A9FIReRn/ZolpbKEVnyYzuqxuMDJQCNbU82ww==", "signatures": [{"sig": "MEUCIDcHPaWJkTqk4uJrKMK9zYOSfFIQb6Q2YJ768KHGwEG3AiEAgIyCXnHdiruMtKoALYc+LUSa+ckk3sWvnyl81aLcAvY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.25b4278", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11384}}, "0.0.0-insiders.81de67c": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.81de67c", "dependencies": {"tailwindcss": "0.0.0-insiders.81de67c", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.81de67c", "@tailwindcss/oxide": "0.0.0-insiders.81de67c"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "bc0bfbdec2afaa12bf00468203ac820bd1ea22ed", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.81de67c.tgz", "fileCount": 5, "integrity": "sha512-sfJlkhuqvR402UFU9bYoERBvUsj5ZEhbe9t3l0FxGTXK1QNO2f06UAXP1YIzsL/SjqQsH8fJqzrSGj0HjwvNlw==", "signatures": [{"sig": "MEUCIGm2CraL+E5GVP65Wl+lKUVJzbj5tyfbhUkLNdSYQZtaAiEAwV2ExSeY9a1qa2LKbpDtgOX8eZJGVhSmdmUpvXtSLO4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.81de67c", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11384}}, "0.0.0-insiders.d684733": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.d684733", "dependencies": {"tailwindcss": "0.0.0-insiders.d684733", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.d684733", "@tailwindcss/oxide": "0.0.0-insiders.d684733"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "88a3d30a3dfdb964472be63f6b9d01ae12622eb6", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.d684733.tgz", "fileCount": 5, "integrity": "sha512-m0g/gHvb6qNWGdNNYtlki1revpsMgIZeYGL2SMJ24zEwMCcRWGvLMU6C+U37dHoIl4cbYijzAWqATYbpEmCwGg==", "signatures": [{"sig": "MEYCIQD/8qrbscUAbfkGnBuMt+bG8RbGCQx/leXB7cGJlIUlEgIhAKQgP7GObHj7FY/+CoUtle5UJVrbukvrRTpjLMFxsEt5", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.d684733", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11384}}, "0.0.0-insiders.ad00119": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.ad00119", "dependencies": {"tailwindcss": "0.0.0-insiders.ad00119", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.ad00119", "@tailwindcss/oxide": "0.0.0-insiders.ad00119"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "4453ed4c6c5a713005ec52da1249b59913c68bdf", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.ad00119.tgz", "fileCount": 5, "integrity": "sha512-4oRrAjo3F6hTO0WIt/Gn9J4FWiBw7bYNM4CJcEs+EncWkiaUCNnsdwa7qEUvqoVCLR50v2YqIQ7UJ84LQldHpw==", "signatures": [{"sig": "MEYCIQDCcSz5W6+n+7S/wZE7UlRltyQg06Tg1PuqVAhh53gfKAIhAMn0pwkktix//+Ft6DFCfzaHBm+ASoS5sQLJY2TZ1X/E", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.ad00119", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11384}}, "4.0.5": {"name": "@tailwindcss/vite", "version": "4.0.5", "dependencies": {"tailwindcss": "4.0.5", "lightningcss": "^1.29.1", "@tailwindcss/node": "^4.0.5", "@tailwindcss/oxide": "^4.0.5"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "b7747d7c29bd7cef8a2a0c3e0b527cae17ed5a2f", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.5.tgz", "fileCount": 5, "integrity": "sha512-/i4hjLTUYVjUG0MTUviQP3HR/hzwyzv8Sq4sz2pnsNuf+FIjjhJB0vcnIMH1KIX0k8ozD6CBv2Dl76tlm/JFFA==", "signatures": [{"sig": "MEQCIB+bmbzi0DYoWqCouqLflPOWkdFQ5SCKmjkaaDsKZnVUAiA+4i4nIOEk91NvOlZilx9MFNMHoWdacqGuagw9fJjy3A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11318}}, "0.0.0-insiders.aaf66b0": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.aaf66b0", "dependencies": {"tailwindcss": "0.0.0-insiders.aaf66b0", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.aaf66b0", "@tailwindcss/oxide": "0.0.0-insiders.aaf66b0"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "7ceb731c8bfae5aa7fa2a4043873499ce4480239", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.aaf66b0.tgz", "fileCount": 5, "integrity": "sha512-EPuVBVp3iKKijc8k3gk8K6FLWFntc+L2eqaeZU9pxe2V4aYH+OJ7bST9Y6zgsgFIHQwMZXeJW81mFc7V1BY5Rg==", "signatures": [{"sig": "MEUCIQCQksx55yiMDBrNdZV9oQzFaG2SY+xHSTJlaHEpOuK7PwIgLKXCLUSJ80jCJ8l4lFwGBh6bXtgx9e0Fk8crYI6GYaI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.aaf66b0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11384}}, "0.0.0-insiders.a659159": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.a659159", "dependencies": {"tailwindcss": "0.0.0-insiders.a659159", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.a659159", "@tailwindcss/oxide": "0.0.0-insiders.a659159"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "8a59b89f06f830b84b39d25e7c4908ac99c71906", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.a659159.tgz", "fileCount": 5, "integrity": "sha512-fYPPlZr1DZx5XITAJxgIKe79uqO51FkVw6eClIledqFGs39pYUu3G5QsT/ClhuLvoncKUlXhQ5EieX+otXNNkg==", "signatures": [{"sig": "MEYCIQDPBMgLohU0vdwqt/ttKxv19KKf7S/KEL4S/LGp7O7nvwIhAPT/2XhU2znszoLXZ9w6S/pPy0vBfufXhgtxrsR7/9Rq", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.a659159", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11384}}, "0.0.0-insiders.1f84241": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.1f84241", "dependencies": {"tailwindcss": "0.0.0-insiders.1f84241", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.1f84241", "@tailwindcss/oxide": "0.0.0-insiders.1f84241"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "e1a5165b655da39e1d040791d401c5e856106432", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.1f84241.tgz", "fileCount": 5, "integrity": "sha512-MzeUISyh3YWhua1SeRYWpRo/szc9EuGNrQ07Z5QAUWhOEST2gFVFszm24qXNy1pCFbndm0gyYWpExmEt4kJ9Jw==", "signatures": [{"sig": "MEUCIQCRmzEuazdUQJTi46EytaOzFDX9KXpTcfnja5jEbRmY0QIgIkzZf7ZLBjaORUVfcGD8/k6DjZPgVkJiW22D6whh8tk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.1f84241", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11384}}, "0.0.0-insiders.9bbe2e3": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.9bbe2e3", "dependencies": {"tailwindcss": "0.0.0-insiders.9bbe2e3", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.9bbe2e3", "@tailwindcss/oxide": "0.0.0-insiders.9bbe2e3"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "fa15c121b35490ef02325bdadaba4158402049d6", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.9bbe2e3.tgz", "fileCount": 5, "integrity": "sha512-Ug33HtVjkKTZt/xZcwkj2sTZUp+yc3T5lBI1tKHKLe3I/b14kG45yoGHfDIgdtOJ55Q3NR9HZ56+6BpTyWTFaw==", "signatures": [{"sig": "MEYCIQC8w3nSx+rIkiyh41sutE5GyD4HdD02ETC02DFiuUMSmQIhAMMMhJ/t1D8SksAmyXzxvSX+HDeECO28XBYiXXwdC0Oi", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.9bbe2e3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11384}}, "0.0.0-insiders.d045aaa": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.d045aaa", "dependencies": {"tailwindcss": "0.0.0-insiders.d045aaa", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.d045aaa", "@tailwindcss/oxide": "0.0.0-insiders.d045aaa"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "b6d13a56273bd496b2e20bea70b311ed0fd957f8", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.d045aaa.tgz", "fileCount": 5, "integrity": "sha512-yyjsHJT1qm2RZi1Vih2SNgnID8coedikbsvvS25BIe2pgK4Vstkz165TdMNNiiLFpf2oy/6qzn7Hzz5Ty+7mOg==", "signatures": [{"sig": "MEUCIQCRyWfROdSovOPKtnd3m6sptp8v90Ei6MmYtszMiEDmuAIgQ5QgYgb8dkeax9EvnELHsGaoqvSdDiV1LQtNSJc/vmg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.d045aaa", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11384}}, "4.0.6": {"name": "@tailwindcss/vite", "version": "4.0.6", "dependencies": {"tailwindcss": "4.0.6", "lightningcss": "^1.29.1", "@tailwindcss/node": "^4.0.6", "@tailwindcss/oxide": "^4.0.6"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "e43be7bfaabaf3f157d08273e21edb74faf24f47", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.6.tgz", "fileCount": 5, "integrity": "sha512-O25vZ/URWbZ2JHdk2o8wH7jOKqEGCsYmX3GwGmYS5DjE4X3mpf93a72Rn7VRnefldNauBzr5z2hfZptmBNtTUQ==", "signatures": [{"sig": "MEQCIGdfG1vR9qIbz+w+Yy/w6oN0w+e8/kes1EIz08Pk41WmAiBpKGPlWcIjzKDMFiVerVKzQEn8u+4r490pLJlkmyiV0Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11318}}, "0.0.0-insiders.272c6df": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.272c6df", "dependencies": {"tailwindcss": "0.0.0-insiders.272c6df", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.272c6df", "@tailwindcss/oxide": "0.0.0-insiders.272c6df"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "ebd72b13b64293c19d017f62f62672a739c2830b", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.272c6df.tgz", "fileCount": 5, "integrity": "sha512-aLDXqP0XouD4GDVIdX4pRupI+TiV2IZ5f0XoC9RzU6IOxuvlpv/p2m1uEaB3JDo0P7NDEJnOuZn0lcUriJ3sow==", "signatures": [{"sig": "MEUCIQD61TDGrxKib1SuKwb+8xgqP5GGsqSzY6+3V9Y9MccBRAIgf2nbGHHhx40qQcrjCRROrT86dedMy3C4NevPdszGSm0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.272c6df", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11384}}, "0.0.0-insiders.121cf6b": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.121cf6b", "dependencies": {"tailwindcss": "0.0.0-insiders.121cf6b", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.121cf6b", "@tailwindcss/oxide": "0.0.0-insiders.121cf6b"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "3fe91a96d2f65594a98a3c2516cbfd39d916376a", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.121cf6b.tgz", "fileCount": 5, "integrity": "sha512-juPezbquNyaIJeoG8IC8uYaJvC97YFq54UfjaNMMCbV177GXziIlCIWMV9L4GLUd8yy5boIy+DUUwTB6Dlx0Ow==", "signatures": [{"sig": "MEUCIEKUbQ0/1m029AK+y6cwuSy7KYQvXX750eD7SHZKVxueAiEA0XilaLPY30rhQDyW5NnBnv7gkSUMl0oGroWPD1U+kug=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.121cf6b", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11384}}, "0.0.0-insiders.f678a70": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.f678a70", "dependencies": {"tailwindcss": "0.0.0-insiders.f678a70", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.f678a70", "@tailwindcss/oxide": "0.0.0-insiders.f678a70"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "14c8701a0ea17cf000b44acafc233d93e1706723", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.f678a70.tgz", "fileCount": 5, "integrity": "sha512-aB4gi0n1f1lR+b8M9tzgM4CkAlbHX/RRG3B72q6/x9XaMTzXLEkUTu/3F+1gZpTVigWGKhvAE+PBEsIlMxuuKQ==", "signatures": [{"sig": "MEQCIGB9+JnHHvrlxzEhJzyg8OLZ0uq1vzRyDX7oe0Lx0M+NAiBiVX9ETlBPMAKGh7OyA2x8NkMrhrCiHJbDX5L07n8cPA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.f678a70", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11384}}, "0.0.0-insiders.17c7c7e": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.17c7c7e", "dependencies": {"tailwindcss": "0.0.0-insiders.17c7c7e", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.17c7c7e", "@tailwindcss/oxide": "0.0.0-insiders.17c7c7e"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "8f6da6c002db8e0277e171ba89a7af5f4501b23c", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.17c7c7e.tgz", "fileCount": 5, "integrity": "sha512-k/XfnL3zCyIGsDH7kNW/EfmvbrAvegmhcuxikdAQoghPzZySfgEUPsnunRHsh1Lct3b1a0vBQoV2/ALfnMKg+w==", "signatures": [{"sig": "MEUCIF4ZTDAJKDZdQ7yRCNZs8YRfGgIbqYuAtngz/Llgae+7AiEAhn8xgEmgI29AKphGTwTkcwX/SztMFEI2DgnEPEhcXtk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.17c7c7e", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11384}}, "0.0.0-insiders.f995dae": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.f995dae", "dependencies": {"tailwindcss": "0.0.0-insiders.f995dae", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.f995dae", "@tailwindcss/oxide": "0.0.0-insiders.f995dae"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "c32fdfd390f7f4ed9d50398581d2bc055573de4e", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.f995dae.tgz", "fileCount": 5, "integrity": "sha512-FwG7LKNSYxhWrfoNLnVhJAO6vXbi1S1U6mEpGXGOcHlh4N9/28WGx/wJ7ifoUp6Lm4chTbeKwhcEZk2E5vsWEg==", "signatures": [{"sig": "MEUCIQDRGLYr2EikBsZAJ2ia7Q6xAL2PPbgOQi368eZo6AFGCAIgZG+7e4QhRenLr7b7KItK2c/4fjQSTkHqE/HxcvzTDdY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.f995dae", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11384}}, "0.0.0-insiders.aad440e": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.aad440e", "dependencies": {"tailwindcss": "0.0.0-insiders.aad440e", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.aad440e", "@tailwindcss/oxide": "0.0.0-insiders.aad440e"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "901e05995e02ec59c84010fa4427c808b80ed026", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.aad440e.tgz", "fileCount": 5, "integrity": "sha512-wU6lQIfM6LkwWZURViLm4ojeKKwVB6VNwLPcMfh/eyOudli3Pq8tZkW1mDm7+hLjPQz8RDeWocFbg8oZlBntvw==", "signatures": [{"sig": "MEUCIQCmTNKAQuwPKKkHUVrfQNyxgso9V6l4N2KIRiUpnjFgmQIgFp1C+/3qBlDlQWOUB7yVaWGKFfL7rEABEmLG0yriHV0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.aad440e", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11384}}, "0.0.0-insiders.ff8fd8c": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.ff8fd8c", "dependencies": {"tailwindcss": "0.0.0-insiders.ff8fd8c", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.ff8fd8c", "@tailwindcss/oxide": "0.0.0-insiders.ff8fd8c"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "fec2254e3a2af188d2660851947a4bb2e57960fc", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.ff8fd8c.tgz", "fileCount": 5, "integrity": "sha512-DVFrES5krP0/DKoUG0Sf+YJxKCta0MoZe40s6g48/Pr3Zd+yWeHlvpinasMPaem/TFeIf7aELVv+ZBPm5B11Og==", "signatures": [{"sig": "MEQCIBxlea9EV8aPEf6PAmtEHgY1OHXSiGyTx0p797FHmUzmAiAgZbKo0M8xI3e59MRNdNOH4r6QQGAbNFivFUpQxiKizg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.ff8fd8c", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11384}}, "0.0.0-insiders.c80c0b5": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.c80c0b5", "dependencies": {"tailwindcss": "0.0.0-insiders.c80c0b5", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.c80c0b5", "@tailwindcss/oxide": "0.0.0-insiders.c80c0b5"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "c0c075597ac10bc73dc7a7d03aaa2b2147043861", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.c80c0b5.tgz", "fileCount": 5, "integrity": "sha512-hFMkq8BVDrTyrtjkkoEFMvmpAJYoGr/x6FFrHjjM/u/rFs3EbvqQNoXQkl+7ADmTR3tHmAn9fQWMBSyKhEAXBA==", "signatures": [{"sig": "MEQCIGE6L8DM/ScT5ABkgfP9mgWudGpikhC+sFSfjliifNPLAiAFwW0Uso/VO6Y5gIBdUMd8fgxPXMgUHKK21N8zXdRG1Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.c80c0b5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11384}}, "0.0.0-insiders.7326f64": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.7326f64", "dependencies": {"tailwindcss": "0.0.0-insiders.7326f64", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.7326f64", "@tailwindcss/oxide": "0.0.0-insiders.7326f64"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "d1ef0440a5ff1f367f56f5b6486c23ffa9cfa97c", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.7326f64.tgz", "fileCount": 5, "integrity": "sha512-Ct/CJ4PntKskL0LqvaOvmN00aNO1SpbKcVgUSBN/Axi0aJMi3x7hGd9+2XjYWnFOQx/4rby2apWda3qgdLNnIA==", "signatures": [{"sig": "MEYCIQDlPp/CTUUDQtyXzTgOgn/DeXUUDMEB6IsVf8I/YYb/BAIhAJWXHw3ekuPIby2lzA3upoaTtKzZ4TsP+BJ/BVmKGpB7", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.7326f64", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11384}}, "0.0.0-insiders.53749c3": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.53749c3", "dependencies": {"tailwindcss": "0.0.0-insiders.53749c3", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.53749c3", "@tailwindcss/oxide": "0.0.0-insiders.53749c3"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "f618ff86eadccd4701a41bbddb400e5fca7fddcb", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.53749c3.tgz", "fileCount": 5, "integrity": "sha512-Kf+sDwZECwj7HcfPivHB17pCkdXBk6JZ8He4M9Kn6BfO4xjehwHvHIdkIAASlHNeNz1eiSMYHty7UyK245FM1w==", "signatures": [{"sig": "MEUCIHJ1WCdY/qHBx0TFkdMsd4ZLf/7DkOMj+vALydVZwTlLAiEAn0ugem7FqFQI3khfIeE9XgTjn2rCtMwfNZJNZTkaxEo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.53749c3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11384}}, "0.0.0-insiders.6164783": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.6164783", "dependencies": {"tailwindcss": "0.0.0-insiders.6164783", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.6164783", "@tailwindcss/oxide": "0.0.0-insiders.6164783"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "86d89f0be58d585c2155d92683f9fe16f608722f", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.6164783.tgz", "fileCount": 5, "integrity": "sha512-tMXe0IzmSYUCsGzDrNuXt2QakVFPeIFE/riv2HYd/4+I6MXtzo+s5AJEn9rH5UYA33migAlLbrtthPNUXiXVPg==", "signatures": [{"sig": "MEQCIEptjrLaERaxsi0fpzd6+m4RYvjzMQui7+an/2+QOwoEAiBW85lUpNR2O4IMWb1wAN9t1lK9Ca8uWlJEBa26LRWShg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.6164783", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11384}}, "0.0.0-insiders.4f18f90": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.4f18f90", "dependencies": {"tailwindcss": "0.0.0-insiders.4f18f90", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.4f18f90", "@tailwindcss/oxide": "0.0.0-insiders.4f18f90"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "3927a3145b276534bd3c8b9649105bc8667c8f20", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.4f18f90.tgz", "fileCount": 5, "integrity": "sha512-dQhheZwUgshYj74o5lF6vtrAS2QnLOSgr2x97t+Lf2UU810QjfWlc+sz8KkH2LBvOLon9enwsuq2iRHzIMueRg==", "signatures": [{"sig": "MEYCIQC4YDoVK2T5gqV09JoSv9Gt8+jRi9OeYYgwZ9AMp5tAVwIhANtp6yVxnX9vOvaiDF6feBcrsO3PJhQEJ3Sq2wum+AVK", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.4f18f90", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11384}}, "0.0.0-insiders.dec6c8c": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.dec6c8c", "dependencies": {"tailwindcss": "0.0.0-insiders.dec6c8c", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.dec6c8c", "@tailwindcss/oxide": "0.0.0-insiders.dec6c8c"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "f3f28e18b72ef5491dbff9fdc84c651a1aa59f71", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.dec6c8c.tgz", "fileCount": 5, "integrity": "sha512-1xYyDcrmTwvm0Elm661OEaN6nRpRaV2PChHPBEQZ1hG5pMJBzeWKpIHVHJT6FtqkR4szbyXWxn1uYRca4kyTNA==", "signatures": [{"sig": "MEUCIQDGFr8VygRnoGEpURNFLRqppFdKUfTa78MrOt5z3qGKuAIgIAcGW4xMNVeQWvn/nFUpxdW/ucqWlkDAU5pdq1ryYFo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.dec6c8c", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11384}}, "0.0.0-insiders.63b9be9": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.63b9be9", "dependencies": {"tailwindcss": "0.0.0-insiders.63b9be9", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.63b9be9", "@tailwindcss/oxide": "0.0.0-insiders.63b9be9"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "0a1c93f6c9897b14a3c2192b288010364ee650c1", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.63b9be9.tgz", "fileCount": 5, "integrity": "sha512-XBJIhJufDJYksI52eBsuLT1TB7cWYtuTaGOeUNrq7SM6NyzcrTuj/VnrXTqbAYd8LBZuJUYut2e4uCc50iM0Hg==", "signatures": [{"sig": "MEQCICc8KUWOQ9aMjRxFd2bfkzcr09Lp6LMjHaAR7qJrR95XAiANff5yYH1TiHWTVUgDb/ED7ZwOBYJZpt5vsgdSv+6+qg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.63b9be9", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11384}}, "0.0.0-insiders.a1e083a": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.a1e083a", "dependencies": {"tailwindcss": "0.0.0-insiders.a1e083a", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.a1e083a", "@tailwindcss/oxide": "0.0.0-insiders.a1e083a"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "4976b895645538f9c2b4e8c2debcbffe78cb727c", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.a1e083a.tgz", "fileCount": 5, "integrity": "sha512-E0QPEqiUNcVZvOAR+hdkLhdXEgWZar2G5kxdmV+lIG5H1EZBGDiV9XAx3cOz4SFijSCNYyh3MQDbhJslSaxe4A==", "signatures": [{"sig": "MEYCIQD8XDY+y9f6aukPHnO18tUyKV3W/IWWTSm8OvEZqffOigIhANQt2YBQLbRO3Z6PZb4fHU/st2Q/KI4UY6h/6/szFD9S", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.a1e083a", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11384}}, "0.0.0-insiders.7d51e38": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.7d51e38", "dependencies": {"tailwindcss": "0.0.0-insiders.7d51e38", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.7d51e38", "@tailwindcss/oxide": "0.0.0-insiders.7d51e38"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "2be0b5bccb4528b133d0c083714d6cd0c8c96d68", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.7d51e38.tgz", "fileCount": 5, "integrity": "sha512-Tmj88gLtSnJEL5zOfVc7Uv8335gw5HZaiRESWjYAIzMfefRJyTq7L6PsIPEIG7vVb1UztwVoBvnlslKhop4WEw==", "signatures": [{"sig": "MEUCIFUUVDl+36AIn4v8+PDKQZOml8f0F3VKBQZKbVxsTYSSAiEA2EyAMcJ40Hr5a8qKz8hsS0TLuEC4luzheAY4LZD7His=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.7d51e38", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11384}}, "0.0.0-insiders.fadf442": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.fadf442", "dependencies": {"tailwindcss": "0.0.0-insiders.fadf442", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.fadf442", "@tailwindcss/oxide": "0.0.0-insiders.fadf442"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "6643d67668cea1785d6b1407e34a2783bc4b0a9f", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.fadf442.tgz", "fileCount": 5, "integrity": "sha512-wmFuoOB4xqa9W6HWua9eFc7d5p9FHJxsLJYLwsQAy1X496hoQoBo7y81fqUxSWsvKO22YJeB9aNXgnORhlEnkQ==", "signatures": [{"sig": "MEUCIQDPQmf7t1dZWH5nl/9Ig/jnQQeJ+ow9ECTWa4zTltgoXgIgEdUnDeXK7W1ijv+Cf9e1nrWEja2OFx8viRWdvC0abXQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.fadf442", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11384}}, "0.0.0-insiders.14b1337": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.14b1337", "dependencies": {"tailwindcss": "0.0.0-insiders.14b1337", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.14b1337", "@tailwindcss/oxide": "0.0.0-insiders.14b1337"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "fa7f006fd80f4ddbf6c0aa5bee1e6badb3988808", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.14b1337.tgz", "fileCount": 5, "integrity": "sha512-T/UUoS/gu3nE2snR1NYKHQBfwASXpXOlOltoAAwPju4v44hyKG1ukl6dzVOqdc8qzQPrj3wo3B5m9ofajJE2eQ==", "signatures": [{"sig": "MEUCIQDfcb/oWtpY0pUaVGFb8ib72tbwx0M3ikPM1O4BPDweAgIgTShVHKFNrIJsqm5P1Q+lzjKV2aaGprRhBq+x04gLxxY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.14b1337", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11384}}, "0.0.0-insiders.c504f78": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.c504f78", "dependencies": {"tailwindcss": "0.0.0-insiders.c504f78", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.c504f78", "@tailwindcss/oxide": "0.0.0-insiders.c504f78"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "3a180b280091d6eb37b483877cdd4643ed97d651", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.c504f78.tgz", "fileCount": 5, "integrity": "sha512-P/Xo8KUiQA4HMIu2NtsDBMRGV4unu/STB/U5JqIOB9/ZBogs5NOnsgiEcUpkOsU4g0jOS5TQP2+S4LSe6BHD8A==", "signatures": [{"sig": "MEQCIFS6+tL5rLDa+LJ/ux16mVglLSzkjozaDghQuw9ICI/XAiA3FqVEjYm+2+IOrZoZNmjY6bTulVzxC4U6DmNeKcvY6A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.c504f78", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11384}}, "0.0.0-insiders.34505b9": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.34505b9", "dependencies": {"tailwindcss": "0.0.0-insiders.34505b9", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.34505b9", "@tailwindcss/oxide": "0.0.0-insiders.34505b9"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "c630a33f23bae709ac19c74016ef4079e2ed33a5", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.34505b9.tgz", "fileCount": 5, "integrity": "sha512-/twuNZwjFpDzTgNpjp7R/f6Vt8aUjC11bMyB+BwRtMKgCQgbESzHmbdQx6iNY0pgg01Q5Djf1SYy19wg5qjXaw==", "signatures": [{"sig": "MEQCIHgmBWF5/sM518ZmcvmIlD7LX33+wN8DZCNsF+dGN4InAiBH04QKNKql2Lfm7TtrXY1ZK6pcB0ytEFHuG35ZlvpOoQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.34505b9", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11384}}, "0.0.0-insiders.bbc2f3f": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.bbc2f3f", "dependencies": {"tailwindcss": "0.0.0-insiders.bbc2f3f", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.bbc2f3f", "@tailwindcss/oxide": "0.0.0-insiders.bbc2f3f"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "9b133e89f3e108cb52cf979bbb6c702aea539d1d", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.bbc2f3f.tgz", "fileCount": 5, "integrity": "sha512-2j3ymX1RXs9j7Yli6TN5SML17G7b/79xIKA3+AC/lXwRYTJ67mMMhlqJdPT2GgpCCew5IJsc75SJrAnDzV5qag==", "signatures": [{"sig": "MEQCICHpK7DMlPHwZ0/fsA7rFqU96ppMqTPgMD/08x+cUxNcAiBYm9RzDb7pcfzqI/ZsBKFcFvH83mi42tRxzq9O3bVxhA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.bbc2f3f", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11384}}, "0.0.0-insiders.8b13076": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.8b13076", "dependencies": {"tailwindcss": "0.0.0-insiders.8b13076", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.8b13076", "@tailwindcss/oxide": "0.0.0-insiders.8b13076"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "fa5c5bd0e4d20d92adf3caf08ed813313efdd433", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.8b13076.tgz", "fileCount": 5, "integrity": "sha512-4BUeVRlM2zx8fGduumgra2M6FUy1Ffy1TDSyqA7iH6spnDO7PztnWrHpe58QWLq7mDnCd3ANjuw0bkODVZQlUA==", "signatures": [{"sig": "MEUCIQCB4N9/4rQwH0PFENnTJl0F6rnruQs0ijMvEhh/9wk4IgIgSqTfKo+L9VKRrRZq8R5cOxEG0Oz5fnBnrJ9gNpQMYEE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.8b13076", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11384}}, "0.0.0-insiders.1c905f2": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.1c905f2", "dependencies": {"tailwindcss": "0.0.0-insiders.1c905f2", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.1c905f2", "@tailwindcss/oxide": "0.0.0-insiders.1c905f2"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "a9ae2d4f9f70a68484d2242bc554cbfed615371b", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.1c905f2.tgz", "fileCount": 5, "integrity": "sha512-GZXMMPBE7aNdPsglnGd4S4fflVA+u6QXsFTptXZ37SvgK/6G15JMDbPcX/AHenJAXBhgiUhZI3l6QPYJNqC57w==", "signatures": [{"sig": "MEUCIHwtHIGTFLacDa6oXpoYu76DDRPHJgt8WkyFrlfOa53MAiEA7rLMAJDva1/SqJJ8Q168PvxzmcvXZHOF0v6Ycc38PdI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.1c905f2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11384}}, "0.0.0-insiders.f014108": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.f014108", "dependencies": {"tailwindcss": "0.0.0-insiders.f014108", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.f014108", "@tailwindcss/oxide": "0.0.0-insiders.f014108"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "b20a2a2225911d51e6d5ba532a94e4f4c24dd3e1", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.f014108.tgz", "fileCount": 5, "integrity": "sha512-ci<PERSON>bsoxaAm8PYX1+G4Z0hvf8SQ5gVGKqWwQ59MbcIkcfBaPCxbAsyOLNsiBqtjwCTXkJhr1f/4og0S6BCh6TA==", "signatures": [{"sig": "MEQCIEeHAxh+hpWU1Ys9ZKXikMO0eqn6VbmKoLgCG1O0dzHRAiAeSXNvTiLanTIIpHEziDabiMWJolj5am42CYXk8ASKYg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.f014108", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11384}}, "0.0.0-insiders.08972f2": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.08972f2", "dependencies": {"tailwindcss": "0.0.0-insiders.08972f2", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.08972f2", "@tailwindcss/oxide": "0.0.0-insiders.08972f2"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "288ac6ec7a9f7adb358dc32bc7f4684ed450bfd5", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.08972f2.tgz", "fileCount": 5, "integrity": "sha512-aZFEi86DTcMhzkNvyvTkhVVKB7LdfZ8u95B023yiL1Mr4h3+NLzOKh7hQ+FSROnHkbOM7hOyL+0WKuCxlmwiEg==", "signatures": [{"sig": "MEUCIAmk5Z5XlFolv+Ur2PeGQ35DPs771mHsa/QCUZPQL+ipAiEAxZY6RjZcteDQuqiRBJWJjAGEfLjIdwpe+01Mb07P16Y=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.08972f2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11384}}, "0.0.0-insiders.7ba4659": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.7ba4659", "dependencies": {"tailwindcss": "0.0.0-insiders.7ba4659", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.7ba4659", "@tailwindcss/oxide": "0.0.0-insiders.7ba4659"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "9346abc673bce572cb831c537bf8db4f0dd925c5", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.7ba4659.tgz", "fileCount": 5, "integrity": "sha512-kO/O5WRzsWSgLptj2N3YbCg6VWtVpO4VVkMmBhHlHBNfyxLOqEFWJdQICa0BDOP/34vjlPa1hay5/Jhd03H3JQ==", "signatures": [{"sig": "MEYCIQDf44e6L216oBCcOhHL98+aqAJm3wFrDv+PTjCLrissbwIhAJ12NDTX0qWX8NIPVPs9r01bNdhJeZcLlEtUVvsvOZOu", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.7ba4659", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11384}}, "0.0.0-insiders.ec1d7d4": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.ec1d7d4", "dependencies": {"tailwindcss": "0.0.0-insiders.ec1d7d4", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.ec1d7d4", "@tailwindcss/oxide": "0.0.0-insiders.ec1d7d4"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "513cd21716e10518dd9f97600736a7d124d1161e", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.ec1d7d4.tgz", "fileCount": 5, "integrity": "sha512-1EjSQJhP5uUPkg0Awq9Ms+f1yA43/jG4tD0pROv6QPcGXDUjTwYjN7FpHicNfKcOG6pGWfqfmr8kSB6ALZxFkA==", "signatures": [{"sig": "MEUCIHxp8Gkn9KoCoTSRIeqjN2Me2bgKmnyF5njbOkEDioVZAiEA772L+eYXOl84kXLjo5Cxn7VHP0OGS2GHHtj5gKOesgQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.ec1d7d4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11384}}, "0.0.0-insiders.23723e1": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.23723e1", "dependencies": {"tailwindcss": "0.0.0-insiders.23723e1", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.23723e1", "@tailwindcss/oxide": "0.0.0-insiders.23723e1"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "80ba3160a7ac919640db3cc7e362e37336257528", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.23723e1.tgz", "fileCount": 5, "integrity": "sha512-+o1RatZVh0EdXboVWVxJw+PgKSIIiJfrDvxBjmMgrln5UOpRrL+zJfxQWDp+nTA4ysvpjnlXx5PAd9CP0YcGiQ==", "signatures": [{"sig": "MEUCIQCGAUzzm5WPTRMfoZiWJIX5FeHrT88kCejmqVuhtS6EBAIgN+2dK98SmINpGPG6JiMNlOAMmTeiZlbYAkIDgdpMnfA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.23723e1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11384}}, "0.0.0-insiders.541c3d2": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.541c3d2", "dependencies": {"tailwindcss": "0.0.0-insiders.541c3d2", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.541c3d2", "@tailwindcss/oxide": "0.0.0-insiders.541c3d2"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "ee56cebcd59edb0ada98d18cbe865a039c82cb14", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.541c3d2.tgz", "fileCount": 5, "integrity": "sha512-ajPD1tneEdYmSnnedh3rXJ5L20wKARYZn0bB+YpNtl+KEIpXXlDe9Z/95I8pM7kmt1DyRWIWuGGJAh3LOxZSXw==", "signatures": [{"sig": "MEYCIQCpDp0nfz0sZ9SOsi/R/WkO1l+JsNsMdmyYPtWtErXDpgIhAO+aeFNKjcqbgsRP+6iT21/qOmLzSfOn5cpnq+ge7M87", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.541c3d2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11384}}, "4.0.7": {"name": "@tailwindcss/vite", "version": "4.0.7", "dependencies": {"tailwindcss": "4.0.7", "lightningcss": "^1.29.1", "@tailwindcss/node": "4.0.7", "@tailwindcss/oxide": "4.0.7"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "ae71255a4067f71c36051767ba511905c608ae95", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.7.tgz", "fileCount": 5, "integrity": "sha512-GYx5sxArfIMtdZCsxfya3S/efMmf4RvfqdiLUozkhmSFBNUFnYVodatpoO/en4/BsOIGvq/RB6HwcTLn9prFnQ==", "signatures": [{"sig": "MEQCIAvwF5kcycKBBK9jE3Im6ZO+8IZTCjtPx+EU5kG/iyQTAiB/lgHd0L/QJM4SkQ6GoO4sN+WfjGE4z6c1Ix9zg83sKw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11316}}, "0.0.0-insiders.61af484": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.61af484", "dependencies": {"tailwindcss": "0.0.0-insiders.61af484", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.61af484", "@tailwindcss/oxide": "0.0.0-insiders.61af484"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "cc17a2430293b833a90b02235621961d12a17109", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.61af484.tgz", "fileCount": 5, "integrity": "sha512-71cou9CbS2r8eH2AxVuB4CDAI5vq7R/dX4sxkMe1MrY1kyM+5EfkkT1GozCTV/VV7g2BffuqAYOqLiY11X0JIQ==", "signatures": [{"sig": "MEYCIQD7zoyW5hUpe6DTC6mMlhsgRaszmppXsMPsgzhdK8QWNAIhALpgUofcbybAUxpKzMXL+/CpjBBI96m9FKOz0Ri87D5l", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.61af484", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11384}}, "0.0.0-insiders.b9af722": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.b9af722", "dependencies": {"tailwindcss": "0.0.0-insiders.b9af722", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.b9af722", "@tailwindcss/oxide": "0.0.0-insiders.b9af722"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "c3985a8ee917367f5140021a87362cc20431b3b1", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.b9af722.tgz", "fileCount": 5, "integrity": "sha512-B6UgCEjUpwElKy42MGodekhgQfr3JUup7waaA5EdfWVAvp3+PW2P2HGwPU1t+NTlTHnKZOUThFuWevX4XtYicw==", "signatures": [{"sig": "MEUCIQDGX9VgOeTKWodWXKWz16k4F010/FxLwxdO6nI2Ds6Y2gIgWlKToC71taV4Fmi/d6A2rpNMZrjCxcA9zVZr4jRuU+o=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.b9af722", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11384}}, "0.0.0-insiders.88b762b": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.88b762b", "dependencies": {"tailwindcss": "0.0.0-insiders.88b762b", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.88b762b", "@tailwindcss/oxide": "0.0.0-insiders.88b762b"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "e8ede71f5504c1efb20303827a400a087a57aefc", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.88b762b.tgz", "fileCount": 5, "integrity": "sha512-yHih2TZx7CHGyp8PXiKa/X+gsq+PqptgHJWRCBqQ9ccBg3igBwey8cgWJLUgYjZs9UcF5E1gy5l7/avs7LPhRA==", "signatures": [{"sig": "MEQCIEM35MdPBVVXqm+frShFwcZLgPF4X/BhSiH3JMaMm2OwAiBLtmLnib7oDBG4h9FV/dQ7Sxx2vqvwWwyCn6CXI8J4Hw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.88b762b", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9674}}, "0.0.0-insiders.3f270d2": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.3f270d2", "dependencies": {"tailwindcss": "0.0.0-insiders.3f270d2", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.3f270d2", "@tailwindcss/oxide": "0.0.0-insiders.3f270d2"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "c36bbfba4268763bd3d81f6adef6dba9902f59c2", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.3f270d2.tgz", "fileCount": 5, "integrity": "sha512-jEthxo4uErGJSMhle/q5fqvV7vV3h2MPGRKsn0RmkL2KnETYxGKpFgO/XVvj6cfAtCXBLRD1pnGRl81q4TfRkA==", "signatures": [{"sig": "MEQCIAqLTz9PcCN5KOFcGsJpxl06CLLzC5pAVUBDdFu7RCM2AiBuoTANpQuS8+t3Af/9gHWe0mkUhFaLzEG9EwR120iGFQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.3f270d2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9674}}, "0.0.0-insiders.1d56525": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.1d56525", "dependencies": {"tailwindcss": "0.0.0-insiders.1d56525", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.1d56525", "@tailwindcss/oxide": "0.0.0-insiders.1d56525"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "9714705ed9c3384374f1c9f2adb78170ac9e05f3", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.1d56525.tgz", "fileCount": 5, "integrity": "sha512-lf/PTM8FG8O7iwy7Dv8EEGQHqU/3ZN0PowNmsYWfcqgm6tmfVrPVe1qRZdqSWDTGcjUxVN1jMQa+SZRcZRAelA==", "signatures": [{"sig": "MEUCIQDuihbKkwsPH2Qi8NbEXIYqdweSpXxsfTGTRrYAEG08xQIgUt1KmtzvOexY9Xqa31mYrPPe8PzD3GHAcZ/hlslTnG0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.1d56525", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9674}}, "0.0.0-insiders.ec0049a": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.ec0049a", "dependencies": {"tailwindcss": "0.0.0-insiders.ec0049a", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.ec0049a", "@tailwindcss/oxide": "0.0.0-insiders.ec0049a"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "31568b04e1c8ba0cc1f0ab0e430aa8e241c5b09f", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.ec0049a.tgz", "fileCount": 5, "integrity": "sha512-xSnR5ga9so5Mfjr8vm7gmHRio3mk7gmaSJIrB0uUJgO6jugr/Hn/5z0OZ6inL1pUFJo9bdZmBGGQoG3iV1xyGw==", "signatures": [{"sig": "MEYCIQDzfC3qMgq1/NmLKLg7BG68CPwARnblX4sz23dYTJnJKAIhAJsU0CQDy8zfEiKyfCMS9JYvMFXg99qdLGtcJwzfgSar", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.ec0049a", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9674}}, "0.0.0-insiders.dd7d8fd": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.dd7d8fd", "dependencies": {"tailwindcss": "0.0.0-insiders.dd7d8fd", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.dd7d8fd", "@tailwindcss/oxide": "0.0.0-insiders.dd7d8fd"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "924f3e535eafcfb0de1547fcf9b83dbac3b23c6a", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.dd7d8fd.tgz", "fileCount": 5, "integrity": "sha512-aDUbANSWvBBqbqNd8sxDJoPk3FkVkogT34jQHEbIVOpMr2A+L5u0N3zzJCH0AWPTaCm0nfjvaesd+h8H3ErYoQ==", "signatures": [{"sig": "MEUCIG37icXE+S2ZXBmN0JVqqAIu2IjMEsIbD8fc49BaxW19AiEAiXyo5EjHBqvyXSNyJ6bVW9pLw0wiBBgi9J2cQIhtqWs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.dd7d8fd", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9674}}, "0.0.0-insiders.7bece4d": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.7bece4d", "dependencies": {"tailwindcss": "0.0.0-insiders.7bece4d", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.7bece4d", "@tailwindcss/oxide": "0.0.0-insiders.7bece4d"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "f7a4c56c3369ca8819b2d51712d1c19d8fe8ed66", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.7bece4d.tgz", "fileCount": 5, "integrity": "sha512-pc/4rH2DzgostFguco1KQDWGpfMQQvL6pDGvMf+yfHyG25D4KKJocJ1zoFyT5bo7iuGmQGIY5q8wspHQwv1FBQ==", "signatures": [{"sig": "MEYCIQCZm5J83xOA1Vual7bFCKNHdhBUztWnMk6CsReWyn41bQIhANYtSfJ3a7XTvzCzcVrtcG8n/KTLJX1eDEx3Pn/nkHyN", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.7bece4d", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9674}}, "0.0.0-insiders.f8d7623": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.f8d7623", "dependencies": {"tailwindcss": "0.0.0-insiders.f8d7623", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.f8d7623", "@tailwindcss/oxide": "0.0.0-insiders.f8d7623"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "37ffb3dd74d34141a24e7026d88fe05b9b5cf1e1", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.f8d7623.tgz", "fileCount": 5, "integrity": "sha512-oqjr//0t9Ka7vVLY39/LwgE2g3oTvHjE4R2Z/ngULsRjI1N5sq2kHfjQ2jTDYU6f+3VGF8e1dhKXH3UtCLM5cg==", "signatures": [{"sig": "MEUCIFGcpGuo7k/ye031AU1X9JLnsmMl03+68afqgSxHuvdRAiEAqP7k9Euy8km3wv26KBABtijMauZR4QpTWsaipDxCjc0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.f8d7623", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9674}}, "0.0.0-insiders.113142a": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.113142a", "dependencies": {"tailwindcss": "0.0.0-insiders.113142a", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.113142a", "@tailwindcss/oxide": "0.0.0-insiders.113142a"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "02a5aa811009b43afdb1c8773be8cda15c7edcdc", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.113142a.tgz", "fileCount": 5, "integrity": "sha512-nxexIzAiBtEo/r00vQjHLvXMRJEDuxBLUBEuSxpxsXmnVlQ2SV3sw+i0sRW+6ZAnrMu3ZhaqqNc+0esdGZ1etA==", "signatures": [{"sig": "MEYCIQCYQs01RNLc2rasTRFMhR+TWkAebO/UfNvobkljU/gexwIhAJ7rbRNJ4nunVzPfAGZlzu7wFC2KEzx3+cR0wjFTkf29", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.113142a", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9674}}, "0.0.0-insiders.b47b6d2": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.b47b6d2", "dependencies": {"tailwindcss": "0.0.0-insiders.b47b6d2", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.b47b6d2", "@tailwindcss/oxide": "0.0.0-insiders.b47b6d2"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "0c1a9d654655dace9169dc94ff3bb4aef1af18ad", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.b47b6d2.tgz", "fileCount": 5, "integrity": "sha512-Ao8htN0Z263F942OVAEKA8naA0G2N6RetPqhHJwq/wzWFkTNB4mP8Kum03S1FkAJClm4gCCFw7mTAEdpOZ8X9A==", "signatures": [{"sig": "MEUCIF+8KZx+swHddscmsGZSjYTUjVHH60cVDxeBposAxuAZAiEA6CBr05wbR4USYTsa0+fY/FiNiae5+XwkumTLkKLNfhs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.b47b6d2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9674}}, "0.0.0-insiders.419b3dc": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.419b3dc", "dependencies": {"tailwindcss": "0.0.0-insiders.419b3dc", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.419b3dc", "@tailwindcss/oxide": "0.0.0-insiders.419b3dc"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "e582a7205807ada9f5563a43ed6f25d1ef105b63", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.419b3dc.tgz", "fileCount": 5, "integrity": "sha512-iMgoLP03hjAdhqjUBnGKEm36q1dAwjoAYWP45g9eqH+j4OFkZaB5yUgvg50nU/Rffl9/dOb3LlmZL6Kzv6FnXw==", "signatures": [{"sig": "MEUCIGcx3CexX4dgoXQF2D2sZBYvTU/N7sR6fZFSAicko7ePAiEAmLJX4fOXMNa6aQAH3zMTkhOcwglfnCKmZmBjq2hzobw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.419b3dc", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9674}}, "4.0.8": {"name": "@tailwindcss/vite", "version": "4.0.8", "dependencies": {"tailwindcss": "4.0.8", "lightningcss": "^1.29.1", "@tailwindcss/node": "4.0.8", "@tailwindcss/oxide": "4.0.8"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "6adb0461e24c521e3121f1cda654e89e18f11a0c", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.8.tgz", "fileCount": 5, "integrity": "sha512-+SAq44yLzYlzyrb7QTcFCdU8Xa7FOA0jp+Xby7fPMUie+MY9HhJysM7Vp+vL8qIp8ceQJfLD+FjgJuJ4lL6nyg==", "signatures": [{"sig": "MEUCIQC7NbDSVqhgP9B/CVH3N3zbA6FxobsnS/ecnvgXhyr/aQIgA7wmKPqcrmpKwH46i27Q2cn++3EE2gw+alrwvtgfD8Y=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9606}}, "0.0.0-insiders.62d3e74": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.62d3e74", "dependencies": {"tailwindcss": "0.0.0-insiders.62d3e74", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.62d3e74", "@tailwindcss/oxide": "0.0.0-insiders.62d3e74"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "299b230ab799fb4f2afd743e0f92239544cbff89", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.62d3e74.tgz", "fileCount": 5, "integrity": "sha512-oI6lOLpBIGTBcPMl747vxx+RllkbJGRVS/B7edIVBva39hejRNraLLHDXjg+4ULFeY2/WyqYd0VZ2IlHF0f0lw==", "signatures": [{"sig": "MEYCIQCGpQ8Zl598U7CEwsjddupqzp7Fxv9gGsafuYyM0zKZkAIhAPihx9j0sTDH1W3g0d+hrpd489Q05Xz3gF4dTJCFEJyB", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.62d3e74", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9674}}, "0.0.0-insiders.751eb74": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.751eb74", "dependencies": {"tailwindcss": "0.0.0-insiders.751eb74", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.751eb74", "@tailwindcss/oxide": "0.0.0-insiders.751eb74"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "0da59f77525b29866e54d932f1a147eae6b4b353", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.751eb74.tgz", "fileCount": 5, "integrity": "sha512-k9guUIDreF45aYLnTfY4fF1bPphV9skFOB4aIyNBEiW3smX6K/gSnpU0GwlPqJkB+YCKkYvmIO4HWXragZdYkA==", "signatures": [{"sig": "MEYCIQCiw8fc3yUqsbgFvD2JuwshQ9BpdT1rEB5GxATZpnv0zwIhAIAM86hKuv5BFAkOoXLiSRhvFUNG53/FvS1TbkiHMIBG", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.751eb74", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9674}}, "0.0.0-insiders.604be56": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.604be56", "dependencies": {"tailwindcss": "0.0.0-insiders.604be56", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.604be56", "@tailwindcss/oxide": "0.0.0-insiders.604be56"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "ee609357988e1171f7c3f8045d7d86b0ba8a5846", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.604be56.tgz", "fileCount": 5, "integrity": "sha512-NNzDBq0DAkvewyoleM0BfVoRlw91kkNnAHFDkKZpGUE4vFc8DdOrA1cGMfp2gnbBp+bfAoypYBiAADg/kBa8HQ==", "signatures": [{"sig": "MEYCIQD6sLVubJNJHtyaE1fJMfVopqg08YQ8CaLe5+rLFaLswAIhAMquHKsg7ufEqsiVb2gJAx1PesdvLvCwkXS1DbCuK/Ly", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.604be56", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9674}}, "0.0.0-insiders.a893de2": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.a893de2", "dependencies": {"tailwindcss": "0.0.0-insiders.a893de2", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.a893de2", "@tailwindcss/oxide": "0.0.0-insiders.a893de2"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "5a148d70270d632359d15414b5e8dbf18aa3b6a7", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.a893de2.tgz", "fileCount": 5, "integrity": "sha512-W9ECvP3tPPsJN6HgG1+9AXgzg11PdoWZmwntBA0ubGpSsidRZWdqBqEWRNegqbIZLB7L5SPupXtERycgGCjfrw==", "signatures": [{"sig": "MEQCIBYUlYJqX9lGWdyfQ+NwGBgNxnDCgfL6NlWGygqWWDKvAiALthUFzZfxALlst8lJdi8dZ8XrXpbYAHTnkFglSNsN/Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.a893de2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9674}}, "0.0.0-insiders.37ea0c3": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.37ea0c3", "dependencies": {"tailwindcss": "0.0.0-insiders.37ea0c3", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.37ea0c3", "@tailwindcss/oxide": "0.0.0-insiders.37ea0c3"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "7de11dee6acb6c552472fab75ae75790611e95c8", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.37ea0c3.tgz", "fileCount": 5, "integrity": "sha512-GVLfnY7dSURx1GiWxLn2u2Rx6lkozfWzxuV9QhsbX5uwVVjmBFXQUrW/AEZ1SigjDDE/zN+mv5mW1V/47Njfsw==", "signatures": [{"sig": "MEYCIQDBZFbs526GBaoLQzQ42YEudo1nXdPuJ/Qgmu2krd7EpAIhANsybJKCzvrlCgbgPnZrsjqRGODmAQb/Mm2vE3IsJOh1", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.37ea0c3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9674}}, "0.0.0-insiders.59e003e": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.59e003e", "dependencies": {"tailwindcss": "0.0.0-insiders.59e003e", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.59e003e", "@tailwindcss/oxide": "0.0.0-insiders.59e003e"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "ca7b722245c7f3411e01b27191daadd9f19cc0a5", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.59e003e.tgz", "fileCount": 5, "integrity": "sha512-bCrUoQ7TS1ViIJ7dw82OZzvwTw5UgO6IkTQYwYkECZ7r9AN6+HPqo9xWD7OFt7mZHUDSz5W1HY7BtPRTn+POMQ==", "signatures": [{"sig": "MEYCIQD8j942C4aXVEOFInYZOJzTJOQTjcCDMclqSOtOh8ZmmQIhAOKh0BkBj7Vnpgi+VLtrHDiPKG9xDkkN2uVWlXA6DVMd", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.59e003e", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9736}}, "0.0.0-insiders.b389483": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.b389483", "dependencies": {"tailwindcss": "0.0.0-insiders.b389483", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.b389483", "@tailwindcss/oxide": "0.0.0-insiders.b389483"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "2c4080bf81414e417675789764d4ef48d167144b", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.b389483.tgz", "fileCount": 5, "integrity": "sha512-ZBczwrnv/uhqSpWlW8Gam398Gs7bi+MMSJMFunF7tBKYBa1/Rxl3hWB0A5el2dXj7Drv/77kQa9aLrZ2YXF9pw==", "signatures": [{"sig": "MEYCIQCvKS0WTXcpvb7twQhtBGIL5pJcw6dzwdtsh4GZ9YxDkgIhAIChi0UQQtdWGDPFO/dAISjr0RzLsHdvOkObGxvGndmz", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.b389483", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9736}}, "0.0.0-insiders.ef57e6e": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.ef57e6e", "dependencies": {"tailwindcss": "0.0.0-insiders.ef57e6e", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.ef57e6e", "@tailwindcss/oxide": "0.0.0-insiders.ef57e6e"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "a2f4e8c178ef8de00bdeb619f5e3fe87b8026a95", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.ef57e6e.tgz", "fileCount": 5, "integrity": "sha512-T67C2dXFSkhyiSdsG3gcIsV6m65HiA7Ul6jCNSwlOch4X7IKomeS5RWxoZdmE/SKwOCetLK4bxphxQBDYSiEQg==", "signatures": [{"sig": "MEQCIBfKU9u7KxL3Etmpb9S5WLoYelClf/kl2qz9EY/RiN9TAiAJMsb/qQRidSeOy2S/FMzANJ+zfF7SVS8v9RnQKNYnmg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.ef57e6e", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9736}}, "0.0.0-insiders.662c686": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.662c686", "dependencies": {"tailwindcss": "0.0.0-insiders.662c686", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.662c686", "@tailwindcss/oxide": "0.0.0-insiders.662c686"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "81ee40b84e48fee8de0134eaa9fbc5c117fb2bd1", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.662c686.tgz", "fileCount": 5, "integrity": "sha512-1nyCzeTCcbtpJzEQNSouJpXv2hYRlb8/RJXqwcV8Rn1FnWgwpPnylRbWI0rG+ZGVYd8XwIpj+IjhQbwECPgEZA==", "signatures": [{"sig": "MEUCIQCz89JiWeR53Qxl7Y7WvUvRyZTvMuZtTTsQVyrN2josogIgAd5wG1YNb2i1uwGLoK5OC5MS4Xn0Zls4XmftjS4aZcs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.662c686", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9736}}, "0.0.0-insiders.a8a2a43": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.a8a2a43", "dependencies": {"tailwindcss": "0.0.0-insiders.a8a2a43", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.a8a2a43", "@tailwindcss/oxide": "0.0.0-insiders.a8a2a43"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "bbcf5fab069ece37f446dccb342c29c8d640cd55", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.a8a2a43.tgz", "fileCount": 5, "integrity": "sha512-om4VAYFNd7y7DM6ieNfSokuiCWsWvyWzb9nZWltBFkR1a2hhDoxeMy7ijBVcewZIIfwDslX+ikoVb0KNr51s4w==", "signatures": [{"sig": "MEQCIDQ8bL4jrKoSBlnBeICiiBgZeGMwmUfM1yCl2MmVX+XmAiA5IG4p2ux3/gjPasftMgz70RQY/oHKJdOycias2lYBTw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.a8a2a43", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9736}}, "4.0.9": {"name": "@tailwindcss/vite", "version": "4.0.9", "dependencies": {"tailwindcss": "4.0.9", "lightningcss": "^1.29.1", "@tailwindcss/node": "4.0.9", "@tailwindcss/oxide": "4.0.9"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "31e707ee7dabecc5a288c77c7504c8454a7add2d", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.9.tgz", "fileCount": 5, "integrity": "sha512-BIKJ<PERSON>+hwdIsN7V6I7SziMZIVHWWMsV/uCQKYEbeiGRDRld+TkqyRRl9+dQ0MCXbhcVr+D9T/qX2E84kT7V281g==", "signatures": [{"sig": "MEUCIQC4DRAWu6SdrEF+EAB2Zgx7p+pQNLqury8oePZvmJrV7wIgNR8VyGWuJ2q9PzqcFWCXIyh1gG6zTslkF81LIouWFL8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.9", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9668}}, "0.0.0-insiders.ab9df29": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.ab9df29", "dependencies": {"tailwindcss": "0.0.0-insiders.ab9df29", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.ab9df29", "@tailwindcss/oxide": "0.0.0-insiders.ab9df29"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "436d4a11de98262ece6e8638d016218d937f8c7f", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.ab9df29.tgz", "fileCount": 5, "integrity": "sha512-od4HdYKU7tIYlNzPBJmecdZ3k+UY1vtmunb/UzTWYdGP5dccVUrg2buMuIXsyaPCtScfTkdq3B720izCaCUE8Q==", "signatures": [{"sig": "MEQCICgX+eTi+xYXgcnZIA7eJg4wK9FcCto/Ab4vXLmAfZiNAiAb8j7TZDJhcjAejHUVeJ93lCtrUr2rEiQwRFkSXIDjhw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.ab9df29", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9736}}, "0.0.0-insiders.124b82b": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.124b82b", "dependencies": {"tailwindcss": "0.0.0-insiders.124b82b", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.124b82b", "@tailwindcss/oxide": "0.0.0-insiders.124b82b"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "409b3231d159ab22f75ea221eedebe193d89bd08", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.124b82b.tgz", "fileCount": 5, "integrity": "sha512-Hpj3hsjVL+VP4RgjyptphJFGrpbT7Ks8anS8Vmdo0Hd6SkErcY+eE0MEmRRm3XyiP2G/q9hCYMDNY2vi1P4hLQ==", "signatures": [{"sig": "MEQCIC6unTyG/sZ6JGAYRTp0f0a2oxvOzmH06JhzIuGf2hY3AiAVRE+61HE87KTXYb/5DjYJTUbMcQPoea858FwngvyemA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.124b82b", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9736}}, "0.0.0-insiders.e938c58": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.e938c58", "dependencies": {"tailwindcss": "0.0.0-insiders.e938c58", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.e938c58", "@tailwindcss/oxide": "0.0.0-insiders.e938c58"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "461be6e28e09145989579c72d400a17d9084ed0c", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.e938c58.tgz", "fileCount": 5, "integrity": "sha512-MEsnyB0EqQoy10uA4HaL5LfUMe/VoYXrEOtS6vuoJu+SEDgFe2COnh3gA64SAvvHTK9fy93YWYSgvZ12VkASJg==", "signatures": [{"sig": "MEUCIGV1L3j51KyJn0yJ0fBzR0eUH8pzzGD1C943YLX4Pb6BAiEA7AXj0sG5/cb6oPwYyLPCpfmqb6yl50xgFUdO72qq2Vk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.e938c58", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9736}}, "0.0.0-insiders.5532d48": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.5532d48", "dependencies": {"tailwindcss": "0.0.0-insiders.5532d48", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.5532d48", "@tailwindcss/oxide": "0.0.0-insiders.5532d48"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "5dfdb4602182d090f52c8679f0e0c321acd94f00", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.5532d48.tgz", "fileCount": 5, "integrity": "sha512-M3u0GFErQ2Ii4BPxm/QLf2YWMdyXeLzcBbIiE76VaBq+G3YFW5phbbXTAAfguSUDSH6Vyaq315mYJF5/dsWX1g==", "signatures": [{"sig": "MEUCIQDh2rGZFzx0WBGlW9GPkGCI2zr03oMyYoXUtPZuCYk0DQIgeYMSVBZADNUAiFH3FP12+Hzv05pJTMmBXoUDFewW6fY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.5532d48", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9736}}, "0.0.0-insiders.66ef77c": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.66ef77c", "dependencies": {"tailwindcss": "0.0.0-insiders.66ef77c", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.66ef77c", "@tailwindcss/oxide": "0.0.0-insiders.66ef77c"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "844576aac70e97907d154bc41de6802023a4bb16", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.66ef77c.tgz", "fileCount": 5, "integrity": "sha512-3a7O9r1q8tyIthrLnx/dqjPE/RXhbHIB3Y1amtdtPXUzjQF5rUHJTm2IG0b80stvhubWycesdEWActKVCowSRw==", "signatures": [{"sig": "MEYCIQC2q2pjCXymmGRRHLB6U3PtY3zHdZmZmyyFHhlC7XbTmAIhAOWGOzQf2DIXM8EcQWZFbvkF7/gZIjBKCmwTrsmAKj9V", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.66ef77c", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9736}}, "0.0.0-insiders.595b88f": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.595b88f", "dependencies": {"tailwindcss": "0.0.0-insiders.595b88f", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.595b88f", "@tailwindcss/oxide": "0.0.0-insiders.595b88f"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "afefc327e8db725eb0b7076721e3395a45737492", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.595b88f.tgz", "fileCount": 5, "integrity": "sha512-4qjGIGUK/uvla4eAet15DRgHkr1Dw4xOZeZn9/CMK7KSyYLB8Xt820/Jh5S/rZSAMAiy2vB7ateqBn1GgfQMCA==", "signatures": [{"sig": "MEYCIQD7QXLbvp+YyNqVbzlacEeXY/U+D6gD90OroaEq5oMXpgIhAPU2PE/5DVXQrDLie8gOsjxbUCIHk5yS92YF4kcQYtxA", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.595b88f", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9736}}, "0.0.0-insiders.a98ebac": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.a98ebac", "dependencies": {"tailwindcss": "0.0.0-insiders.a98ebac", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.a98ebac", "@tailwindcss/oxide": "0.0.0-insiders.a98ebac"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "7d0290cec017ef773711989e9d180810de49420c", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.a98ebac.tgz", "fileCount": 5, "integrity": "sha512-qKT0B6gRwfYoH1HLpUlT5GgxUOuPcE4LN15Npp9FmmiE1WoOjyB5XIRbeTfHb1Lhi0hsDgKsh8tzPP7T7Ulxrg==", "signatures": [{"sig": "MEUCIGdbtbaSUdNV8SYGpsMjxAeEdC5Uk4KqkU3aOhPWnNTVAiEArtPGULRbkf5JzdZ7qn+WEpjZL85oRsdpHJS04c9Bn2U=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.a98ebac", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9736}}, "0.0.0-insiders.0b36dd5": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.0b36dd5", "dependencies": {"tailwindcss": "0.0.0-insiders.0b36dd5", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.0b36dd5", "@tailwindcss/oxide": "0.0.0-insiders.0b36dd5"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "42f7c27609d251b23238ed60aec59421128ff253", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.0b36dd5.tgz", "fileCount": 5, "integrity": "sha512-9sEtgdgZ7KXhGkE2m4cRfGaVVjd+i1/RI9mVBvSpp5N1iL42oIthruWLCVbTxmK3+BeYEAZNd0LIVYIbMmGxkQ==", "signatures": [{"sig": "MEUCIGfS3uAU94S6VoghUNRSML/YfZUquTgWjJzfC+YHxQdQAiEAnB9FJ/lKUN+cXjw2hHeJtQ+yvYS6TacUjM7W6r2NfZw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.0b36dd5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9736}}, "0.0.0-insiders.4c11001": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.4c11001", "dependencies": {"tailwindcss": "0.0.0-insiders.4c11001", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.4c11001", "@tailwindcss/oxide": "0.0.0-insiders.4c11001"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "39df5ad2567e7e63d6b53c9729f037255ce328f3", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.4c11001.tgz", "fileCount": 5, "integrity": "sha512-eZj3II/W9b5X/hr/gWHIJ6iVLb/QTSQ2f7EchmOgAyq/Sdf3ohCmNXnUzvVw2h02upencOoSSifJIbGspSbWAg==", "signatures": [{"sig": "MEYCIQDRShW5WN7M/2BQUBCselsbzoZjhTeNLl5A9sHBOtHdZwIhAJT5BGxpV08gTWfiyNkmsGByfWtlchAf3yyuu/YuOusd", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.4c11001", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9736}}, "0.0.0-insiders.a8b64f3": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.a8b64f3", "dependencies": {"tailwindcss": "0.0.0-insiders.a8b64f3", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.a8b64f3", "@tailwindcss/oxide": "0.0.0-insiders.a8b64f3"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "b1b2d4147b9b7bb9be6276b1293885da9870a62b", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.a8b64f3.tgz", "fileCount": 5, "integrity": "sha512-Wy7sFzPU6gNXAlYLJzhjpPb3+jKTgDRDMunXr9n3/3O9LC1EcTVbKH8GcxIEA+sy9u6rCiIJItqrey7JjY0ypg==", "signatures": [{"sig": "MEYCIQCj8AlYPOL5B0ucRbYPsLtJHwHW3miBu27XcwC4Ok9JrwIhAKH4q8y8ZNUFgc5AsCEiE0MTd3HNJzBBvOakNfo34r7M", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.a8b64f3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9781}}, "0.0.0-insiders.1638b16": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.1638b16", "dependencies": {"tailwindcss": "0.0.0-insiders.1638b16", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.1638b16", "@tailwindcss/oxide": "0.0.0-insiders.1638b16"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "2a219234a73097b6327d3deed240b6b504c941ac", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.1638b16.tgz", "fileCount": 5, "integrity": "sha512-u2BKQkpgH+1zlSH1/sHwvggWnkFT1pEODcHqA9L8S5JTpinqnIfj0wZCmnMaU7dsNRJ6tGFmKNQdKFKfVAx8bQ==", "signatures": [{"sig": "MEYCIQCq8V+G2E17KAbuCLoye4BTeZCvWu8vah/F5xkkcXo+jgIhAMKIkNtsf2kFCyV0uXsbhreITfYJuaFTAHepphM0lROa", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.1638b16", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9781}}, "4.0.10": {"name": "@tailwindcss/vite", "version": "4.0.10", "dependencies": {"tailwindcss": "4.0.10", "lightningcss": "^1.29.1", "@tailwindcss/node": "4.0.10", "@tailwindcss/oxide": "4.0.10"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "0a52d02e6f29f98cc4636e8cfa089d898540c62c", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.10.tgz", "fileCount": 5, "integrity": "sha512-SFY/FgEj68k/6o3Q0PxoZK6KzQZV9T4yMy+kwOGq17NOWXAyDJ+Fagz3tkzqhzKpWTzMMPFfIo+g5r3seyp6uQ==", "signatures": [{"sig": "MEUCIQChZX0A+/g5qMyvWJfDSJWjZsghGaRG9cBepaZqI0vMcAIgcXTrrWe4ViijhQ5aMHc+qsLDTp7GQ/D+5bOHCB7O5m8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.10", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9717}}, "0.0.0-insiders.9c59b07": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.9c59b07", "dependencies": {"tailwindcss": "0.0.0-insiders.9c59b07", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.9c59b07", "@tailwindcss/oxide": "0.0.0-insiders.9c59b07"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "33538687b11b9d20e8632abbded0243b7003c83f", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.9c59b07.tgz", "fileCount": 5, "integrity": "sha512-Rtme6nXlBozdMBvHdE9TX+pbaLIEtuUIVVwJERsRBlHy+Wo2A1O/gGtad6ZDuK96FEo93Qd2T5QxUwXKc1J5BA==", "signatures": [{"sig": "MEUCIQCi7txTsWdHgegBHNMZk+XTnhsiigr9cRooKq3oAIMAOAIgTEEXJSF+Pm1oav7lavLX2YQIeKt6XGe2SF/HbtirNg0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.9c59b07", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9781}}, "0.0.0-insiders.4a02364": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.4a02364", "dependencies": {"tailwindcss": "0.0.0-insiders.4a02364", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.4a02364", "@tailwindcss/oxide": "0.0.0-insiders.4a02364"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "d7b8adc04b63545cdec6528a0ae1d5e970fef81e", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.4a02364.tgz", "fileCount": 5, "integrity": "sha512-u9qLSgCMo4cKHbpbB3RS/1OsgjdZ4BDyNlyA/X3DRuOZPTJw+m5hjACxTRx6nzVAJf9OkvgHWnossdyJNsZvAA==", "signatures": [{"sig": "MEYCIQDTqZKJmJ5zjiEd5mMq3opzWszAyyyGVzqfiQj7N1BklgIhAICjFbPmMf1FK7H7qgdr93eY6M1+mXTgnt5AIGRNDcSJ", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.4a02364", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9781}}, "0.0.0-insiders.617b7ab": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.617b7ab", "dependencies": {"tailwindcss": "0.0.0-insiders.617b7ab", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.617b7ab", "@tailwindcss/oxide": "0.0.0-insiders.617b7ab"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "c48d07151b2f8e42aca35df43f93a31044e5f10a", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.617b7ab.tgz", "fileCount": 5, "integrity": "sha512-xTRE/WWv5M4c7SPNuv2SqyDNSt8jHmuGD3LgdOM683U1SPmXLjcNnibEpzsXloP7hsKU++DNnuCan30Q3UN6jg==", "signatures": [{"sig": "MEQCIBEsorRqqgQP9XCE7nYQdtFWSperu1yHz9gK9h3DLsieAiBdH+VkKxZXPaRlPRSMb+TIFJm5n3TRC5NxBmne+5RMYQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.617b7ab", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9781}}, "0.0.0-insiders.af132fb": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.af132fb", "dependencies": {"tailwindcss": "0.0.0-insiders.af132fb", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.af132fb", "@tailwindcss/oxide": "0.0.0-insiders.af132fb"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "9c8d499f649e03ce63f765b09fe7614d27544e98", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.af132fb.tgz", "fileCount": 5, "integrity": "sha512-IG72IUYzEper+CQAdP1420iPAIb/O1hEYtvUgs1R7+3NKHu9G3AiJMpYvM9lm17K+yLUtLDGvf3NOqBJl/Dfaw==", "signatures": [{"sig": "MEUCIFOwN6FwvOIhyz7i5ugFSNdtaOCbpJP+wndihwjALX3VAiEAzQJIrI66V3XBwOBTb9sNR0wL7VPoX3HN0aeOuHM1Y1g=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.af132fb", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9781}}, "0.0.0-insiders.bff387b": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.bff387b", "dependencies": {"tailwindcss": "0.0.0-insiders.bff387b", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.bff387b", "@tailwindcss/oxide": "0.0.0-insiders.bff387b"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "89573f7c925a33e76c490a02ce2f9b5b3d92568f", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.bff387b.tgz", "fileCount": 5, "integrity": "sha512-kOVZqrkyr55S0qzELZN20G2GLrFGVCZxs+9xQo3M/D6EwL5IALfJHjt9bAAS+lTxO7hutPATiCOkYzb5SBvSWA==", "signatures": [{"sig": "MEYCIQC7Ble4F/Xn0rUilEqAdaeZiDYpzSn4Fj+RRUdKl4IgvgIhANJrOCTpTsayRNQboWDViI6viRHMSQscXvfVXHVsOHrs", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.bff387b", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9781}}, "0.0.0-insiders.b676da8": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.b676da8", "dependencies": {"tailwindcss": "0.0.0-insiders.b676da8", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.b676da8", "@tailwindcss/oxide": "0.0.0-insiders.b676da8"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "6b4853578db15c8f417c271a62e3be5adafd0191", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.b676da8.tgz", "fileCount": 5, "integrity": "sha512-dOrCmTQiB96Yj9sZ8zpXwhuOZTsvg1T6MqPmVM9PW40eicqrnQen25H611FUN73yowp3wpw9Vlvl3X7eUJOe6A==", "signatures": [{"sig": "MEQCIGWvS4W4jjgNUx5/ts6HrQeKjnhzAN2lVq0oxvgLUtETAiB+yP8du5WxNMsBctDd0uvlGjaMGaY5raTS5sE5sViKPQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.b676da8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9781}}, "4.0.11": {"name": "@tailwindcss/vite", "version": "4.0.11", "dependencies": {"tailwindcss": "4.0.11", "lightningcss": "^1.29.1", "@tailwindcss/node": "4.0.11", "@tailwindcss/oxide": "4.0.11"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "880cb4f9a2fa81e147ddf11f7fbd51e29540dd81", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.11.tgz", "fileCount": 5, "integrity": "sha512-rjzupwJLR/2d06SQ7JWPyEdj95qW4tGcP/i6vHkbtHnDR0XkohaG7ab8lKHnxJsr+/4RDHwKDGRPKB3VWgx2vg==", "signatures": [{"sig": "MEUCIHMjXC2kKU6IyhSM4u5Z+0rYyOH1EGKtI0GeKp1CZHlOAiEAqYcHSvhNBv8l0tXLJtVKNQH/f5Ch/IYV7u/K74e8ffY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.11", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9717}}, "0.0.0-insiders.3d0606b": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.3d0606b", "dependencies": {"tailwindcss": "0.0.0-insiders.3d0606b", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.3d0606b", "@tailwindcss/oxide": "0.0.0-insiders.3d0606b"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "fedaa063dc238ca8310c09e09cadc7adfa7527d1", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.3d0606b.tgz", "fileCount": 5, "integrity": "sha512-<PERSON><PERSON>lI5twuXGxkfzVMP86QQR81s54xUq/ThFZfcH4yZiBYdRCaLcfEH1u3KCaXiPOdXOFk8+ydpXv6zZXzTidY4Q==", "signatures": [{"sig": "MEUCIEY/ZkXpNuku2VK0rZTi3vBXvvc9Jzq0gsL9Xp82/qLrAiEAoneZyaNhcIKXkI/Fxi5fGAnQW/BWbYFmzBSc9rzQV/0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.3d0606b", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9781}}, "0.0.0-insiders.85c6e04": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.85c6e04", "dependencies": {"tailwindcss": "0.0.0-insiders.85c6e04", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.85c6e04", "@tailwindcss/oxide": "0.0.0-insiders.85c6e04"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "7120e5d72bc4afe13107ac482b6d98a2f8f2b0cc", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.85c6e04.tgz", "fileCount": 5, "integrity": "sha512-vOTEyrkLoLqiI/rosK+atXUvX7MRsn1NCy1MTeGSMvJNUMvcMorQN1ojHTh5m0GhYI6xkXvfWiG9GSbifIh7yQ==", "signatures": [{"sig": "MEQCIBzNHeT3KyE/duX8yxoMj5sTxpQDE9a0Ms2CpObQpvC8AiALOi1atS5iSC6vcvYlCxIPFhSnBQtFTEoEFV6k8AwV7w==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.85c6e04", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9781}}, "0.0.0-insiders.57e91a6": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.57e91a6", "dependencies": {"tailwindcss": "0.0.0-insiders.57e91a6", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.57e91a6", "@tailwindcss/oxide": "0.0.0-insiders.57e91a6"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "e373c54698d625b0f242aa9459185db1dc51dfd5", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.57e91a6.tgz", "fileCount": 5, "integrity": "sha512-mMc5zOhw0eQHMNO43Pu9y8UayxlTfhhDWfP3d6ftbt/cSKcWen61I9E13G7uwPHIjAfeD5vo1FT4uwlqtgh4pQ==", "signatures": [{"sig": "MEQCIFK537U6jzqfoNW5Z01CB0eEUy0cZTj73yWuTqtlJvl0AiAeypi+eQckJDEMisT7DaKIBXQ192Id/UHyXhyJn4r4Dw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.57e91a6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9781}}, "0.0.0-insiders.d0a9746": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.d0a9746", "dependencies": {"tailwindcss": "0.0.0-insiders.d0a9746", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.d0a9746", "@tailwindcss/oxide": "0.0.0-insiders.d0a9746"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "b7d6b923c03a5552ed82778b7c8ec55f8da75e23", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.d0a9746.tgz", "fileCount": 5, "integrity": "sha512-a9P1LxyG6ZEzzfQzvBBtEXNq/5dnEt/kq/SQn2/m0BEi1/d2KY1fmnjd5387Yn+gyhWGb4qJ+gXd9aJBDg2/MA==", "signatures": [{"sig": "MEQCIGtmjAosIIPlFJjyHJp8hVtPRH6faqvJwUVlCRAG1PQmAiARHbxiy0T3qFoKaO/kbWbuNBtGpuI2z6lB5/M0nbmhYw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.d0a9746", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9781}}, "0.0.0-insiders.bd1e540": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.bd1e540", "dependencies": {"tailwindcss": "0.0.0-insiders.bd1e540", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.bd1e540", "@tailwindcss/oxide": "0.0.0-insiders.bd1e540"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "19b128be38315713ff715a2128d8b6e312e12bd0", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.bd1e540.tgz", "fileCount": 5, "integrity": "sha512-kpDaSeJUsHW3ydVjTi/e6WkrVowXpWXOOyUrU2yeOp5X38NwQMsBmGZtveWcFpQAk8vsuleF7cZytEOu2kSTwg==", "signatures": [{"sig": "MEQCICKX/KW312A5vIzkFCZyQEcvw5C+Jck3UJrDE+jwy0zdAiApUkjgjTsQ43ZIOHdqH+olOVtuYWlN3x51MFYvoZ9ICg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.bd1e540", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9781}}, "0.0.0-insiders.d18fed1": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.d18fed1", "dependencies": {"tailwindcss": "0.0.0-insiders.d18fed1", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.d18fed1", "@tailwindcss/oxide": "0.0.0-insiders.d18fed1"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "ddfd18a67eb33b8159183d04a81f4e0b76dd794e", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.d18fed1.tgz", "fileCount": 5, "integrity": "sha512-fWAmckSnx7oDccZMFaeXP8Ui3GcITTzh39YyTWdS3Osh4JJ8+sp1rQFKpKxH+9zdApRJKdSI9uirMUxYcZBQ9A==", "signatures": [{"sig": "MEQCIB51nWDOn7vKZsQtozn/lpAxeJ3oyExvUcyGgt9YMQR+AiAzGZFeHyWP8HhXO1nDzvWPyAKRzdZNyiN+taxBZFwHaQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.d18fed1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9781}}, "0.0.0-insiders.225f323": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.225f323", "dependencies": {"tailwindcss": "0.0.0-insiders.225f323", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.225f323", "@tailwindcss/oxide": "0.0.0-insiders.225f323"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "56c1785f9381fdf39219bc0151c78ff2b2f35d93", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.225f323.tgz", "fileCount": 5, "integrity": "sha512-Rc/RVTNigPiEEWEWTapOhM+FXI2ni6N+xTVGFjRormm1nqVv6tYj3cIYYIuqEWEVWQYUZo6T1qGJpt0CJ6Wblg==", "signatures": [{"sig": "MEQCIGSbGN1eeZ8asrbF4xNIOo5k7mHfTqgSjxj6dCdbWMZhAiAf2gDpkrJ6fJj79sCl40Zk061t/kXeIWkRYr5mYdlQ5w==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.225f323", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9781}}, "0.0.0-insiders.f498e4a": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.f498e4a", "dependencies": {"tailwindcss": "0.0.0-insiders.f498e4a", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.f498e4a", "@tailwindcss/oxide": "0.0.0-insiders.f498e4a"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "2f0d83af3985e558f4ef21cc6e3b2eaa826393a0", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.f498e4a.tgz", "fileCount": 5, "integrity": "sha512-SPfbRrTBkXmJguWGPKJvVJIx6h/SZfrZm7yHCrsLl0dr9a7YEFq2Or2bPf/16xpRiQZ+zxv/UyBGHFaozo3V8A==", "signatures": [{"sig": "MEYCIQCTDZCmfW8zBlOtKz8F/tWZabz6SGvA52STD49M9fRK2wIhAKib9izenqv36Ja0D5iqeCjG1nIy4P6kaJrxjxiMmVrd", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.f498e4a", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9781}}, "0.0.0-insiders.2f28e5f": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.2f28e5f", "dependencies": {"tailwindcss": "0.0.0-insiders.2f28e5f", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.2f28e5f", "@tailwindcss/oxide": "0.0.0-insiders.2f28e5f"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "e7f3b2935c3c0259ddd29a0871299ea7631ec6ca", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.2f28e5f.tgz", "fileCount": 5, "integrity": "sha512-xtmNkOmsGBf7g+KfNgJjrNABx0+N9oF24SvV1NIikmUK+I3w6W3SFrLLgTqva1Q6jIJ3Uwa95/g41LXgr32vEA==", "signatures": [{"sig": "MEUCICPijfEAgazSwZQ+M5ebeNBcLnN4M9quAsszKvj9Qah8AiEAuOaMvUdUNXVsEWSzUrVRdj9iYGiT8kkaGjl6bv4KU9A=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.2f28e5f", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9781}}, "4.0.12": {"name": "@tailwindcss/vite", "version": "4.0.12", "dependencies": {"tailwindcss": "4.0.12", "lightningcss": "^1.29.1", "@tailwindcss/node": "4.0.12", "@tailwindcss/oxide": "4.0.12"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "326ba32aaafe0108911611aa881ff78e95de8174", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.12.tgz", "fileCount": 5, "integrity": "sha512-JM3gp601UJiryIZ9R2bSqalzcOy15RCybQ1Q+BJqDEwVyo4LkWKeqQAcrpHapWXY31OJFTuOUVBFDWMhzHm2Bg==", "signatures": [{"sig": "MEQCIEH9sncGPct6iZ/ZTEAJQAz0gyZeQwzGXndCQHpS4vbgAiBGsFnmXxxLaCNpaEupGq/aklJRRebPAEjRzuQVJ3eFig==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.12", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9717}}, "0.0.0-insiders.7005ad7": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.7005ad7", "dependencies": {"tailwindcss": "0.0.0-insiders.7005ad7", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.7005ad7", "@tailwindcss/oxide": "0.0.0-insiders.7005ad7"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "f8a518fec8d1241b8c5829dfb55535d1a104299f", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.7005ad7.tgz", "fileCount": 5, "integrity": "sha512-Wjuj4LptcxJ6B18MLfLHLBZVeXwHTOZmSUd5WE023sdLmONK1nJSuAInkT1jlyuui0PALqdcqM3zTm+Cv0WYSQ==", "signatures": [{"sig": "MEUCIB0yKrLzG6cucFKvq7eZbUy+Trc3Nwv2G+zFq8pNGo2CAiEA/NochFTK6BwgY3jbzpHRXbwTFEb3NiMBKhJri6AfmIs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.7005ad7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9781}}, "0.0.0-insiders.bc5a8c3": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.bc5a8c3", "dependencies": {"tailwindcss": "0.0.0-insiders.bc5a8c3", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.bc5a8c3", "@tailwindcss/oxide": "0.0.0-insiders.bc5a8c3"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "9254edbd7fc4ca620f45c9c0845cf546890d3c9a", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.bc5a8c3.tgz", "fileCount": 5, "integrity": "sha512-mGWSQ59yl8zT0zhWD3p4DAW7Ow75jwZLCDdXeu2zHsJzw3XJ+1EQHo3t+l5gVg5I1QOfkFm4zQ3VgletZI93kw==", "signatures": [{"sig": "MEYCIQCwryW8e0mH9CWQ21HiXtnmzysvmQ4ei7wnT2fSCjABmgIhAMICJ0r9tDKys6GhzwD7ZAuTqBInD9Fqc4NwZ8IdKSyC", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.bc5a8c3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9781}}, "0.0.0-insiders.cc3e852": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.cc3e852", "dependencies": {"tailwindcss": "0.0.0-insiders.cc3e852", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.cc3e852", "@tailwindcss/oxide": "0.0.0-insiders.cc3e852"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "e6ec02744d810147e58e591d8b2c5c337e538fd2", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.cc3e852.tgz", "fileCount": 5, "integrity": "sha512-gtUHCWxcP3Jkby6BerJWy3b7I9v0Sz/IE8AtqxycRq7xGuPkV9qO/UB0r19MI7dpVX1OYu7AEtBwk57zJgiDJw==", "signatures": [{"sig": "MEQCIH0g6e+bSjrnDRxpXb7Et5DFjoWtBFxVfYlaLEdvrc1DAiBtJwwzN7GtzToKBEtTDeUxHarRqHaz3OluI4C+hdhwow==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.cc3e852", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9781}}, "0.0.0-insiders.de145c5": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.de145c5", "dependencies": {"tailwindcss": "0.0.0-insiders.de145c5", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.de145c5", "@tailwindcss/oxide": "0.0.0-insiders.de145c5"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "a1ce82c318d88307acbb6eaca27f141fc354238b", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.de145c5.tgz", "fileCount": 5, "integrity": "sha512-45chMH7T+Q5lOWFnbiPF+RF3IpFu/N0MV2PVmrD8UqV5IzY7tMnWKo1gQUukgQuvmiG4BqjZbXSZZyDjaxYU9Q==", "signatures": [{"sig": "MEYCIQD6A1KEH6n3ufFS9jWPjIEpcV4pClxOYg3i3vhDcDgmkAIhAIaHq9Tae0a7q9JEQNfneALDXg+c0x7aUhm+Kltb3S0A", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.de145c5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9781}}, "0.0.0-insiders.a57cd2d": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.a57cd2d", "dependencies": {"tailwindcss": "0.0.0-insiders.a57cd2d", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.a57cd2d", "@tailwindcss/oxide": "0.0.0-insiders.a57cd2d"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "03fd76fdbeebb01ee432373d2c4a5102a9beb45c", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.a57cd2d.tgz", "fileCount": 5, "integrity": "sha512-IspnDQpGYcVbBWb/6I54qLR/brzBwPw0oFBNiWE6fOavFLQ91ulDhAmtERLh/i8VzdOnNS07YwqEoyQnB5cWIg==", "signatures": [{"sig": "MEUCIFgFHceBtuGe93aR+bGlpjUJqbKdw3A80PmENWZ9mEkPAiEAmDofuQw13cI9mMQtEcEN5efIITWQhFJgDLz7XOfPijw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.a57cd2d", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9781}}, "0.0.0-insiders.5e2633b": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.5e2633b", "dependencies": {"tailwindcss": "0.0.0-insiders.5e2633b", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.5e2633b", "@tailwindcss/oxide": "0.0.0-insiders.5e2633b"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "3394bdd52f3be3dc0f8abc51a537c6d077b91107", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.5e2633b.tgz", "fileCount": 5, "integrity": "sha512-9cN0Eo0UHj3Hr1ogjvkcWvxvTcqe8jEK6o8Jsc3i+9/B/WGILaXpUmeb4HuUErugSA5wy3U6kVJk6yoaAT19WQ==", "signatures": [{"sig": "MEYCIQCEQxJwx71mM8MMwyFIlwvD82++2XGoa9G/+hEPvRk1qQIhAMiBjv9DlXmQWTejOc6BYUz0ZM1WERG/cZQGBv4U/3tP", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.5e2633b", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9781}}, "0.0.0-insiders.8bc633b": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.8bc633b", "dependencies": {"tailwindcss": "0.0.0-insiders.8bc633b", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.8bc633b", "@tailwindcss/oxide": "0.0.0-insiders.8bc633b"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "9e3cc2219f5284b3795fda4c05a279bdb682be55", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.8bc633b.tgz", "fileCount": 5, "integrity": "sha512-f5rnj+SJabKnLD7GQly9hEulKlCiEgYBZnnQ7Yqa6kIUUTffprDymZ8FnKdkuOeDRRjeowPzRWR/LOcKnWw7qQ==", "signatures": [{"sig": "MEYCIQD1Ur5z+kC4ZgteY2kx7/+YswUGibhGsoSMtuI49Rd4pgIhANyravrHprd1ubIqq4MJ5WGEs3ddWL1Btgh9VFDuVsUw", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.8bc633b", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9781}}, "0.0.0-insiders.9ddeb09": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.9ddeb09", "dependencies": {"tailwindcss": "0.0.0-insiders.9ddeb09", "lightningcss": "^1.29.1", "@tailwindcss/node": "0.0.0-insiders.9ddeb09", "@tailwindcss/oxide": "0.0.0-insiders.9ddeb09"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "efe31fefe5479d96d4895cc5d5a2874f482dcdaa", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.9ddeb09.tgz", "fileCount": 5, "integrity": "sha512-e7E5L0YvAuGy+1/u2vyvCAYE5I4GHiJ0wUKq728QBwjq477zp2aWA7c9tv8zeLUwcNZQ1+hBY/gLLm/zU5+nJQ==", "signatures": [{"sig": "MEUCIDtimgK3+nuhXkRx80RnOwP4tGi2OjtwC3YTLHi9WGTnAiEAkKaPIKgOr+W5XfJUXA3W1pJnaUP9Uy28ROemv4AKv+0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.9ddeb09", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9781}}, "0.0.0-insiders.785cade": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.785cade", "dependencies": {"tailwindcss": "0.0.0-insiders.785cade", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.785cade", "@tailwindcss/oxide": "0.0.0-insiders.785cade"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "6b623f861754e67c089a5498e2ad455b59524f7f", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.785cade.tgz", "fileCount": 5, "integrity": "sha512-C/TnEDHcXTcZl8znFRYo1DSdyltpBiOtKDaHnwXLdmrMWKRawOM2kzf/NQwtcsRfJN4Rw+85G9Y7h83NIdVhuw==", "signatures": [{"sig": "MEQCIB8ol42QalDvKDtENrLW+E23EsZ7fJPtPF3iBdQ1gcYWAiA1SRxg0ve5PGJf5tft8SM2/3BYsWv0tqCnKkUB/8UPZQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.785cade", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9780}}, "0.0.0-insiders.9d7f253": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.9d7f253", "dependencies": {"tailwindcss": "0.0.0-insiders.9d7f253", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.9d7f253", "@tailwindcss/oxide": "0.0.0-insiders.9d7f253"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "990e412a3fc998538924b0f059ccab2990f4ed38", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.9d7f253.tgz", "fileCount": 5, "integrity": "sha512-CwgIyDsLgraiPCXsB1M2xFoMfIhaL7ilgKZWSCsmLLsBwLQtpPNHUV+Y1xxvxYfRZDRXx3iHIm5y9xoG66tAdQ==", "signatures": [{"sig": "MEUCIByzrYWQgcLdreayrADpXv3CYl6X5ZTpJ17OWb7hAgmuAiEArUTmMVti7I8U9DjTZboIhkZkmrpyij0tftO3bge6U/g=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.9d7f253", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9780}}, "0.0.0-insiders.22746e6": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.22746e6", "dependencies": {"tailwindcss": "0.0.0-insiders.22746e6", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.22746e6", "@tailwindcss/oxide": "0.0.0-insiders.22746e6"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "9a69d569487b3500a0ca30e6fd7d48f29a302409", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.22746e6.tgz", "fileCount": 5, "integrity": "sha512-x9XRQ8d94V7ExW95aYf+YCWFtRZUPDBytTMMPbtcoPbitD1AqUUD2nGcXnOhDszhC9ysvKOKwLUsACzkUJlxpA==", "signatures": [{"sig": "MEYCIQCEEZlbVtUEaANaXCcQyOCoTDX9mtmpfE24RQ+wusT3YAIhAMzi4fufVEuvRjIaP/rNmc28t51j5VxJPG+DZ33wJ5EO", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.22746e6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9780}}, "0.0.0-insiders.3706292": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.3706292", "dependencies": {"tailwindcss": "0.0.0-insiders.3706292", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.3706292", "@tailwindcss/oxide": "0.0.0-insiders.3706292"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "89452bbed0f4e7c003067ab1c938b1ae9689a4e8", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.3706292.tgz", "fileCount": 5, "integrity": "sha512-gbfcg0WwndUtyDXnRHLu5deEWoj+w2HuOxPDH7vFHHI6ciXUgxkBjT+uodSvbtkQh3sqMRwJb8FcnHI8oyu9yw==", "signatures": [{"sig": "MEQCIGXjBVdHSsxEb9AB9icHEMNm/xJNdTVqae7ofYBge1DLAiAY9L7uqCeUGX9ImLaDkPHxOj9AoLI3oYLrdujc+SrxJA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.3706292", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9780}}, "0.0.0-insiders.4455048": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.4455048", "dependencies": {"tailwindcss": "0.0.0-insiders.4455048", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.4455048", "@tailwindcss/oxide": "0.0.0-insiders.4455048"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "22536e0e8cb0d5fa4e17ec4ea87d8af18c0a317e", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.4455048.tgz", "fileCount": 5, "integrity": "sha512-<PERSON><PERSON>+OhRP8tE1RMm9/u5a51iAJO2izaHD0d92+ZpRLLVSmdo940l8mVkzd1QhhgEwROl/eOCUUS5SsS/ngRKdsw==", "signatures": [{"sig": "MEQCIGX9KGYRw9kgBQSKiedjQFdjBZcRxeczaF8r0eQPu0kuAiA7kFeEoRD0hwNcuRQISHrmUwGELTYr+0c1DwyTmCUyIA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.4455048", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9780}}, "4.0.13": {"name": "@tailwindcss/vite", "version": "4.0.13", "dependencies": {"tailwindcss": "4.0.13", "lightningcss": "1.29.2", "@tailwindcss/node": "4.0.13", "@tailwindcss/oxide": "4.0.13"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "e260f4c50e8f43bde638490c3888962b31875fd2", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.13.tgz", "fileCount": 5, "integrity": "sha512-0XTd/NoVUAktIDaA4MdXhve0QWYh7WlZg20EHCuBFR80F8FhbVkRX+AY5cjbUP/IO2itHzt0iHc0iSE5kBUMhQ==", "signatures": [{"sig": "MEYCIQCf1/N6Wg+fYXk6P5KXSdZ29kpMv/9oyOcHz6oJLFIyOAIhAO13u1pUBlFbW7BvCYvx7DWA6EtQrnT8sL8CNpWrGnm3", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.13", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9716}}, "0.0.0-insiders.ca408d0": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.ca408d0", "dependencies": {"tailwindcss": "0.0.0-insiders.ca408d0", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.ca408d0", "@tailwindcss/oxide": "0.0.0-insiders.ca408d0"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "c0d6ce3851e9b62a21fe1484851175284aa90ed9", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.ca408d0.tgz", "fileCount": 5, "integrity": "sha512-kmK0TMCzbD3GzyqYQdYz+amALG6V09/ozxgiVIU2OrMR5P2NyL6kZKUI8ofJaZ58CVdZNuNFve/pN9DpkkD0aA==", "signatures": [{"sig": "MEUCID515LtgIWYPmwHpMnS1J+hrWmPOYEhe4NTzp6782nnpAiEA7OiXKSJ2NxZvrjOFqv7euTkqXFB1GaWwdSGnm8R71Q4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.ca408d0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9780}}, "0.0.0-insiders.215f4f3": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.215f4f3", "dependencies": {"tailwindcss": "0.0.0-insiders.215f4f3", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.215f4f3", "@tailwindcss/oxide": "0.0.0-insiders.215f4f3"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "6debe6ea324a94c2b41ce4186930114ab00afe13", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.215f4f3.tgz", "fileCount": 5, "integrity": "sha512-weKAuFOplJcscumjcLXyodHLgsc/z0luxQ9BvRaAT1JX0a42thItyKQZqYlTOM70cd0lg3fMd5X+WtRu5jrroA==", "signatures": [{"sig": "MEUCIQDMai13PNvvmYV/F5KkXCNjPAhBtTXSquhki3UieqvMuAIgSfKoj16r+00WW80nkUNgNP6Ci95wO/2+BwBscOVJSOI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.215f4f3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9780}}, "0.0.0-insiders.cedd54f": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.cedd54f", "dependencies": {"tailwindcss": "0.0.0-insiders.cedd54f", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.cedd54f", "@tailwindcss/oxide": "0.0.0-insiders.cedd54f"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "2d17731d6eeae61b6fcf24bc90965998dffcb132", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.cedd54f.tgz", "fileCount": 5, "integrity": "sha512-xDNSXzgf15J9fMOTQNmCr/f1gjDVtMG23l0mj/9mOSaF36d99K2jiqR2bC3AbW2HxL+1WGU+n3Ll5x/2EDBfNQ==", "signatures": [{"sig": "MEQCICo2xVOeP3R1y3vgWpupNr0/ACpvAgzxBkG0Gl8cjDhQAiBgaM13RvmFFLJ6YPnbAfykgXJWSAudrvnBINPU93GwCQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.cedd54f", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9780}}, "0.0.0-insiders.26f91d2": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.26f91d2", "dependencies": {"tailwindcss": "0.0.0-insiders.26f91d2", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.26f91d2", "@tailwindcss/oxide": "0.0.0-insiders.26f91d2"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "7a3f75a4ca167376203025538d14a807a430e084", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.26f91d2.tgz", "fileCount": 5, "integrity": "sha512-Cy7+VfF48rprV9HhhgHn0rt6cViwEQTgeqdfb2CCkvO4HvLUn4e0j5qP4mpW9V8Elbo8tExJhIwNPobJmv44qg==", "signatures": [{"sig": "MEUCIQC7yhwr6mmcqpF8dnZyyqSK312TRxh9if5dIXiHOxKeMwIgNZeujRWan3t9vksDHxKpmXPYo6eEOQxxPSY56dAbPtI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.26f91d2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9780}}, "0.0.0-insiders.74ccde4": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.74ccde4", "dependencies": {"tailwindcss": "0.0.0-insiders.74ccde4", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.74ccde4", "@tailwindcss/oxide": "0.0.0-insiders.74ccde4"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "c1d33ec4e8a4f412d309c3621ff23cd9a6625635", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.74ccde4.tgz", "fileCount": 5, "integrity": "sha512-RR5Nm8EBFxxiTv1hY4OKBl66zyid/gY5vk/01mfhMeKsd7w5V4XWMziV1RXJ/zVgjwVWE5yPnvwKgkHUOj0Rkg==", "signatures": [{"sig": "MEUCIQDamIlvGgVuPToHOTkSWyoogyv3+3Ut8DvFBHl3SxjIjgIgHXcNK1sF/DgVhMLLvM8Q62grxUdK5JOhoMhfp/Td5QA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.74ccde4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9780}}, "0.0.0-insiders.221855b": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.221855b", "dependencies": {"tailwindcss": "0.0.0-insiders.221855b", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.221855b", "@tailwindcss/oxide": "0.0.0-insiders.221855b"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "cdc076a5cf6cc766e40f8ddad6b6ed955147f3e3", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.221855b.tgz", "fileCount": 5, "integrity": "sha512-krIm0bm9TYGc3z2VfLLaUjt4hIN8pdZiTXccxlxzcAKlADW5+C0qVedrfI8kUbzj5Jz3ATIZnLzSqHX+50A2RQ==", "signatures": [{"sig": "MEYCIQDVrhaDwjo4/uaMWTi7osLJfQ6PNP/RO8d71lRH6GoMXwIhAOcf53O7GL0WuUE4pLIgCPBPeYbkOtsCSxd01bVtdulu", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.221855b", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9780}}, "0.0.0-insiders.3c5903c": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.3c5903c", "dependencies": {"tailwindcss": "0.0.0-insiders.3c5903c", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.3c5903c", "@tailwindcss/oxide": "0.0.0-insiders.3c5903c"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "423489212feba3d7e4761b1ea2abb78ccf58fca4", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.3c5903c.tgz", "fileCount": 5, "integrity": "sha512-m+0gQ6TaTsFH4O+WNdxhNLKINXm7os0XGlngO2wMwEubQc0kUpojy03kMhALh5Po6ZHmgBzEOHCBSgebIO//ZQ==", "signatures": [{"sig": "MEQCIGRylArp5PxuP0O2/DKAEEjQFflkc7aU0UpH7gs0tLh2AiAldnJd8khefVMFdxeObTKSoAhmaH7OwU6nCZXLZ1KGjQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.3c5903c", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9780}}, "4.0.14": {"name": "@tailwindcss/vite", "version": "4.0.14", "dependencies": {"tailwindcss": "4.0.14", "lightningcss": "1.29.2", "@tailwindcss/node": "4.0.14", "@tailwindcss/oxide": "4.0.14"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "9d02b8925ce6eab307494d05e024566be0c205dd", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.14.tgz", "fileCount": 5, "integrity": "sha512-y69ztPTRFy+13EPS/7dEFVl7q2Goh1pQueVO8IfGeyqSpcx/joNJXFk0lLhMgUbF0VFJotwRSb9ZY7Xoq3r26Q==", "signatures": [{"sig": "MEUCIEhnYxnZ/Ss2pdncqx2oepL58mo57pe5cA2BZa8Mi4NyAiEAswETo3sUudz44BS5b42n/gcy43vfSRhgk8MA0g0ACjw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.14", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9716}}, "0.0.0-insiders.50562a9": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.50562a9", "dependencies": {"tailwindcss": "0.0.0-insiders.50562a9", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.50562a9", "@tailwindcss/oxide": "0.0.0-insiders.50562a9"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "792ffc906f034ed965eecd860d9639b921ae61ee", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.50562a9.tgz", "fileCount": 5, "integrity": "sha512-1ASNMAGQcCR4645QnlRLrN1hKvx3V3QMiOfa+FxlRa6XcyI0T0nWJ+cl3RaKCKG7+rF2q2/SRkUIzKmhk12/zA==", "signatures": [{"sig": "MEQCIEQd82LGqiyeqWToKkKW0UrklDZToweXydYdc+iCfXIbAiAN9iRdN7ZaI9nbOWgyf+K++bGcyt+uyQpoI0uOiRjaWQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.50562a9", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9780}}, "0.0.0-insiders.48957c5": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.48957c5", "dependencies": {"tailwindcss": "0.0.0-insiders.48957c5", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.48957c5", "@tailwindcss/oxide": "0.0.0-insiders.48957c5"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "171655f3fa63d8c1cf40066d0c6935c7e357c73d", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.48957c5.tgz", "fileCount": 5, "integrity": "sha512-ug/aLnZnuq0+0qau6rdW+XlvgBCX6uwxkHYmEXpHUmoIFNFSmEVy71Z9khFDBJRYX1oEAPjTCRi5AZvm7mdjSw==", "signatures": [{"sig": "MEQCID/0pDmqzegPzwNVUDu6RSqooc2LM4DaV2U0bbH4eAH7AiAqUPWYmNcIXBzYc3vmF/7CwlrRzJ/P1Db9GND/UesXxQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.48957c5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9780}}, "0.0.0-insiders.6b2b262": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.6b2b262", "dependencies": {"tailwindcss": "0.0.0-insiders.6b2b262", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.6b2b262", "@tailwindcss/oxide": "0.0.0-insiders.6b2b262"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "7ddf99d3ccf708a5372acc2922b4fdba85d275f4", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.6b2b262.tgz", "fileCount": 5, "integrity": "sha512-8Udtn9GO+v0wgR2vvW7n9VuhUhrzSz2EIL4NGXhoD6AhrGXdSMUunG2I8OhzKSg062yL2ArSXTEWXrQRq2nqtg==", "signatures": [{"sig": "MEUCIQCeoNTZ6GEMTa5GqSDazvReFNB7fKp5gY+l1O14/rjipQIgS60Umr7KgDaBhWwz+sDRpFD40v0mzr8yGZP/3P3wZms=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.6b2b262", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9780}}, "0.0.0-insiders.1a88518": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.1a88518", "dependencies": {"tailwindcss": "0.0.0-insiders.1a88518", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.1a88518", "@tailwindcss/oxide": "0.0.0-insiders.1a88518"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "444f16b31a25036ef5d0e404ef2c39ea2cf67161", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.1a88518.tgz", "fileCount": 5, "integrity": "sha512-LVxSl6ai8TMRqnHzJWNXgueXnhJsO0KQm1UiD6cxI3HIDadMX1ZEgwy1IoYhpERwBq/81Vcai+L+E8iqAGrKSA==", "signatures": [{"sig": "MEQCIEU4sgtbNnP4qHn1CmXZ6mYgqe7qAsgp2WHtU1SplF5tAiAzewwougQJ3Svf81+NtfRF87bHBAusxN0fAelhxEgHUQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.1a88518", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9780}}, "0.0.0-insiders.4489493": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.4489493", "dependencies": {"tailwindcss": "0.0.0-insiders.4489493", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.4489493", "@tailwindcss/oxide": "0.0.0-insiders.4489493"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "3ecbd98d41081c9380a5d3c6b29b6360cfe019af", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.4489493.tgz", "fileCount": 5, "integrity": "sha512-R2<PERSON>tKN9ezYLkKrnI/u53Sl9hI4aCNAH57+dv2XAMJGwWJtwmYosny4TK1yejgQVGbuYTiVdoCBr3NVJfmfPhkA==", "signatures": [{"sig": "MEYCIQCXCkE6IHmy2AwWdLQW/RaqpNG0iwBYuxcD0lgd0ljqKAIhAO00yvnO6U+csa0b38i6lXoD/1baW2prtCmZ1QI2FvmH", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.4489493", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9780}}, "0.0.0-insiders.d6d913e": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.d6d913e", "dependencies": {"tailwindcss": "0.0.0-insiders.d6d913e", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.d6d913e", "@tailwindcss/oxide": "0.0.0-insiders.d6d913e"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "7f2d387e85f12ae39b3a24c2f7a8ef634fd421d3", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.d6d913e.tgz", "fileCount": 5, "integrity": "sha512-FbQacsKvo9D2DknsSJbDLgkGyuuI3ZlFxGrM5N9OT2pKn81H7x4uradWg2QGAqv1vpdeAM60YHDX4GZZthwGtQ==", "signatures": [{"sig": "MEYCIQC0KnUM1bz9Gko9wKRq+UX+znuEDFlTPfN1SYO7fFBzugIhALoKBh3hOMmwwHteZCUwtvoHZ+ZRo44KTXKZ0aqksf9L", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.d6d913e", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9780}}, "0.0.0-insiders.1d2d50e": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.1d2d50e", "dependencies": {"tailwindcss": "0.0.0-insiders.1d2d50e", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.1d2d50e", "@tailwindcss/oxide": "0.0.0-insiders.1d2d50e"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "10e4d6d00cbbc42844f155dc648e12a09c2a3535", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.1d2d50e.tgz", "fileCount": 5, "integrity": "sha512-i0eLn0TqKp3X6Vp5Laf/p8xdwgDhghAwHK+EUIlEbnLVq5QHw2ovQUZkhIeVDhJIKVgWMFhOsuAHWJHqarI9vw==", "signatures": [{"sig": "MEUCICFXZKkgA1oIL42Vzo0QeFUk/OwpeVGWUZ8AE8bJpVgtAiEA1kiEudshZbXCoWan9asa9MqKlPYnYV9NQP6wyAlVKwM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.1d2d50e", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9780}}, "0.0.0-insiders.ebfde31": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.ebfde31", "dependencies": {"tailwindcss": "0.0.0-insiders.ebfde31", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.ebfde31", "@tailwindcss/oxide": "0.0.0-insiders.ebfde31"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "bfee0c057f5ba775133698bdd58f606b748a5183", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.ebfde31.tgz", "fileCount": 5, "integrity": "sha512-dBntCWHyXVktjag9cuTK4PN+NiuIWYxfGwuTad7vwTlSjtOdqTFHgtAHUFOcdy16c6hL5gLMRLCNXMgdrFnXHw==", "signatures": [{"sig": "MEQCIAPA1y09gklH9Asx6ui2dbtF223kPl/9KALguORX7Fx+AiAh3Ma+QMB0VDqTmSvpy3I0xVB8mTVVX/nW/Xt9Rk8Q4A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.ebfde31", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9780}}, "0.0.0-insiders.d7c8116": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.d7c8116", "dependencies": {"tailwindcss": "0.0.0-insiders.d7c8116", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.d7c8116", "@tailwindcss/oxide": "0.0.0-insiders.d7c8116"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "53a137eeaf94dcfb2cd2e9e20cc2d7fc9d07ef93", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.d7c8116.tgz", "fileCount": 5, "integrity": "sha512-<PERSON><PERSON><PERSON>8YBliRdaHERO6VnSQD6sU09IsSh49srtnlM0MRs75qJeiOf7JBS3kkvjTG+ysOq7XcvBy9FtYdQ7mSu2Liw==", "signatures": [{"sig": "MEYCIQDokmfyRb+FSIB65RP825PJihecnU2KpQdVezCQC/GCDwIhAMuim2NR9xUkjCXvbxGR2qNSvrZ1s2lzOpVDc06IIHvp", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.d7c8116", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9780}}, "0.0.0-insiders.1564bf0": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.1564bf0", "dependencies": {"tailwindcss": "0.0.0-insiders.1564bf0", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.1564bf0", "@tailwindcss/oxide": "0.0.0-insiders.1564bf0"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "895f6adcf9682c16cc286ae662c48b20a18fc8d2", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.1564bf0.tgz", "fileCount": 5, "integrity": "sha512-5clcbf15EPdvmA/DTE5yiyZQAh/g9hkrdY4h4D7Q6Awj4g/pePDocsw1T/NO2vuakH4aHlv+lhVCN5/49cffBw==", "signatures": [{"sig": "MEUCIQDuXAJdgJOBpJGCbPX1byzQuCZRtBsLmAU1NRrbHkFsnwIgD5lve8tqEMittlD+/sbAj6ZuYETVjN46MnzHv6rF/rQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.1564bf0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9780}}, "0.0.0-insiders.cec7f05": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.cec7f05", "dependencies": {"tailwindcss": "0.0.0-insiders.cec7f05", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.cec7f05", "@tailwindcss/oxide": "0.0.0-insiders.cec7f05"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "73b51d6d07a072736013dc464d62aa845822396a", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.cec7f05.tgz", "fileCount": 5, "integrity": "sha512-nA1bKLScc9bxO+5bl+tOCe3lprR65yOWjKQoqVKde4TLG8hDr4WthyUQ0hyaxfotxOcxJ9FXljL5gPaWMUPBuA==", "signatures": [{"sig": "MEUCIQDs+q6pc76Fc4DmL83iqei4CwDg4fexrbAQhEvjyIwdnQIgbym+xVp4Neqa0YastUdglyyo/ezZ6jStAyjyccolVbk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.cec7f05", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9780}}, "0.0.0-insiders.d698c10": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.d698c10", "dependencies": {"tailwindcss": "0.0.0-insiders.d698c10", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.d698c10", "@tailwindcss/oxide": "0.0.0-insiders.d698c10"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "b9ec55c8bda7b42ba4122c81db5a081f66ff5403", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.d698c10.tgz", "fileCount": 5, "integrity": "sha512-/o9qlf1B5Saw+wqUiurtC8cAhJNd5Y6FU3xz194nclGurW6pGSNL1OdwrcOqUjQQiMcxTilWBui0u0ZS1iOEYw==", "signatures": [{"sig": "MEQCIHPuyVoIU2VQKpKJSgIhlKawi4xJLUVDgshF/cCd1UQLAiBC5FZzZI9hvT9IN2WrDpqRMm6LGMBoumrocxKw7ZB5Pw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.d698c10", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9780}}, "0.0.0-insiders.f369e22": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.f369e22", "dependencies": {"tailwindcss": "0.0.0-insiders.f369e22", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.f369e22", "@tailwindcss/oxide": "0.0.0-insiders.f369e22"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "73e165fc11522d2239c7beee5c134810b5d8c453", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.f369e22.tgz", "fileCount": 5, "integrity": "sha512-dlCSHTtTFLlE+gp17h16J2efdUThQEHklRTiN5K8nVKw3Ng3KqeksY+W+Ja93GryLQpcBw69i2xWy+yycARzhQ==", "signatures": [{"sig": "MEUCIQDyVriNug9n9u3yx/Ft84PghP4s8qCsbO46ASPLssuWfAIgGsZIktxqFZ68aBny+0PSam0b82cNy7gAZQ68ZCBWHk0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.f369e22", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9780}}, "0.0.0-insiders.503bad4": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.503bad4", "dependencies": {"tailwindcss": "0.0.0-insiders.503bad4", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.503bad4", "@tailwindcss/oxide": "0.0.0-insiders.503bad4"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "dfb4a91cfb4ddb88cd99223df443e720b9afb3cc", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.503bad4.tgz", "fileCount": 5, "integrity": "sha512-OieTYUJaWFjZfz5fO5Uf3t9kCWG226l8fN9aycSMSkO5H27dLK5dv/Rz2D09w7cwZnqMCvtXMrfFEwtHAYBVuw==", "signatures": [{"sig": "MEQCIAnbiZDIPJHeRHXzHSi/9thc952DDbGTHYqq0vYKxQM8AiB8V8tM2Yls6E3E7EpZ6tlV66HAPh7ox/RCXq5iS04hKw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.503bad4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9780}}, "0.0.0-insiders.a1acaee": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.a1acaee", "dependencies": {"tailwindcss": "0.0.0-insiders.a1acaee", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.a1acaee", "@tailwindcss/oxide": "0.0.0-insiders.a1acaee"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "09eaf491c6cbe6401de6a9e535f3d3634919fde0", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.a1acaee.tgz", "fileCount": 5, "integrity": "sha512-jiT7ZzuVSGhIqdxgyp+w81HhrKuyK6qFuYqmomE5gj3i2q9Jxydwktu1ZRwXhKG9EiNJiGPe6eRPG/uUEGIfuw==", "signatures": [{"sig": "MEUCIQC76gbfZKu+Hn3oVTh4BLpPekSDNyqS13lKoY5lInmZAQIgMQVvox2ThmzsQqvPXc4lY+JOUbTeysuKFVOYssU+7pA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.a1acaee", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9780}}, "0.0.0-insiders.3f313b4": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.3f313b4", "dependencies": {"tailwindcss": "0.0.0-insiders.3f313b4", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.3f313b4", "@tailwindcss/oxide": "0.0.0-insiders.3f313b4"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "62eba21c27fcd39f354c2361aae61aa319b3c8ec", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.3f313b4.tgz", "fileCount": 5, "integrity": "sha512-OYgAR06rfuRFJDVwW73OzH66y+aRdLFiYleR3Yl1a/MnRhTA5GgN7/DzK3TTOy6RqaDzd6/Goy6fXJbHXxm13g==", "signatures": [{"sig": "MEUCIQCMqX0DBZnWR4ylM+9XYP1manGob31trnMiJ5f7eRmtPQIgSr+UdnXUF+VaEcLxAbhbfJCAGIGLF3KgQitdg8BEQbc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.3f313b4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9780}}, "0.0.0-insiders.ca7b10e": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.ca7b10e", "dependencies": {"tailwindcss": "0.0.0-insiders.ca7b10e", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.ca7b10e", "@tailwindcss/oxide": "0.0.0-insiders.ca7b10e"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "2dd34261320ae753bbcfb5f3161a31e66d8c5ca3", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.ca7b10e.tgz", "fileCount": 5, "integrity": "sha512-VM21Qi/WsLYPCP60jwtzgxKquNjPnPHdMyQdIKtp06h4iNajgR4vGq1QqHjNijO5t/aQz0PznFO43S6tcI0gNQ==", "signatures": [{"sig": "MEUCIHC5CY5RWdcv/C0tJFSk+SIs1sZ9GydFV71otGdkTl+NAiEA0qhvt0RhNoj1zu8xZyFGm+sH9OhOZibZc1LxuEbUi5Y=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.ca7b10e", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9780}}, "0.0.0-insiders.40a76e3": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.40a76e3", "dependencies": {"tailwindcss": "0.0.0-insiders.40a76e3", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.40a76e3", "@tailwindcss/oxide": "0.0.0-insiders.40a76e3"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "9c7823e8566653ba69ab8a87f02a356a124d5af8", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.40a76e3.tgz", "fileCount": 5, "integrity": "sha512-bhkNkUGdiimhVlNSSyZ2DdcgIDdRmXVf8vhechSRnyzpYgGzAwk520yNW0uFj+r/h03GFhLv6IoC6JyaH5oquQ==", "signatures": [{"sig": "MEYCIQDpvDYl1ZnrOP7sbyVYtG3qIInampIFFMay0cehkF3jxgIhAIVe0hEhcKX5FeZgcxiSlpI63y4KZf9kVygprrfUsZ+x", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.40a76e3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9780}}, "0.0.0-insiders.4c57d9f": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.4c57d9f", "dependencies": {"tailwindcss": "0.0.0-insiders.4c57d9f", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.4c57d9f", "@tailwindcss/oxide": "0.0.0-insiders.4c57d9f"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "7c7156bd7bca9c8c449ce7d3edae28e8a37319fb", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.4c57d9f.tgz", "fileCount": 5, "integrity": "sha512-GbmpeM7E1CgO1m1ugOJ23utFfVxstvp05WisE6CYBEE0rXvmdBDE+UA+t5JFbuXH1Eveec/LWFVv5VdBqTRlMA==", "signatures": [{"sig": "MEQCIH/7UoISz0hTUYEV+QtEFraf+KCuyN5NFqG32nyzDqT3AiAR8JgnAjo9w71OPKQJerR1XQic95/EYe9YEU5IHfz/qA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.4c57d9f", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9780}}, "0.0.0-insiders.a3316f2": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.a3316f2", "dependencies": {"tailwindcss": "0.0.0-insiders.a3316f2", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.a3316f2", "@tailwindcss/oxide": "0.0.0-insiders.a3316f2"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "87a4b9a226b27bc6ce0aac76a80ea92b5d6f83e6", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.a3316f2.tgz", "fileCount": 5, "integrity": "sha512-ynM+E2lk2/3g5gTaHKNHb7rQucHJt7odXO7MsRV/7Oo+dOHt64PmId2tCiNWGbG7hIhxkcHYqWPXrVaG8lRIOA==", "signatures": [{"sig": "MEUCIQDLDqRJoKerJi48bkjoR5l9qXvBio4fD1A59+JqBcxKFwIgPY/FGCxY8Fd9dk6HOD5h7kx2BUxDwwLB7iVQlVsOQ/E=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.a3316f2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9780}}, "0.0.0-insiders.250c843": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.250c843", "dependencies": {"tailwindcss": "0.0.0-insiders.250c843", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.250c843", "@tailwindcss/oxide": "0.0.0-insiders.250c843"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "05e46fe9c37f6d5dbca2f66b82baf26d86b2ebd1", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.250c843.tgz", "fileCount": 5, "integrity": "sha512-URItjlpJO008rBTBKOekcv5IJdpWvqIS/QqDnTs6B6yYwaf8jSjMR85vQaRG0Iv+/bzoStwv9SM5v4BE9kxoFg==", "signatures": [{"sig": "MEUCIQCZ++qTzY4qVxvf6pilmQ+DIjEVVBuLlRxEwdzCcxHS4AIgNl38GJ9+p1nayPJoyc/5bQEMQNPPI1B1V1FH6FRekPA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.250c843", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9780}}, "0.0.0-insiders.508746b": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.508746b", "dependencies": {"tailwindcss": "0.0.0-insiders.508746b", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.508746b", "@tailwindcss/oxide": "0.0.0-insiders.508746b"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "31e9c8c339ebb0de5be90d999d0f8b805171e32b", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.508746b.tgz", "fileCount": 5, "integrity": "sha512-+Qi1MBelq4W1Me7MUMaDO9Ma1aPM9ji9pqlCSBvF7H//A7I0ZZHhrp20qaXL5kvZK9xJRTYZTSl+CvJ7dfnv5Q==", "signatures": [{"sig": "MEUCIQCFhrLX0Q9qELvlm402bu9/74i+JIWrAkMqABgQgfL3zQIgUoRIy361rq8H2copkobb9gzOURs3vJlO54H41zG4i1E=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.508746b", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9780}}, "0.0.0-insiders.1aab04c": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.1aab04c", "dependencies": {"tailwindcss": "0.0.0-insiders.1aab04c", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.1aab04c", "@tailwindcss/oxide": "0.0.0-insiders.1aab04c"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "86dd66c54b171e9a2042e195665c09cde235fd89", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.1aab04c.tgz", "fileCount": 5, "integrity": "sha512-XsYXlH97sYWbb3E7sJ/eyVEGaN0R3YFMDLdfQXecpi4C9SlY/ixC9inaogrx9Vf0RAVd27gfBoHQUJv3HJpKjQ==", "signatures": [{"sig": "MEUCIG3VMGDJRDNvNc4sklBcs42z0SWgS5/h0dsraDWuHkUPAiEAzFFNjTXULtQ1xs6oXf7I8Iu679OuOlHypQ6KSY+ihec=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.1aab04c", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9780}}, "0.0.0-insiders.498b06f": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.498b06f", "dependencies": {"tailwindcss": "0.0.0-insiders.498b06f", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.498b06f", "@tailwindcss/oxide": "0.0.0-insiders.498b06f"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "8f5b74c78047f2ca20c79f2e9f0114d69d57c2f6", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.498b06f.tgz", "fileCount": 5, "integrity": "sha512-GvFXwWvXhkvAxhmA0wVSqnFAF721o9VrY/Emp7QujMQ9DfCZx+AKblI9TeJbAYGfdXoLNmAukGaG6SSrN1XIFw==", "signatures": [{"sig": "MEUCICSHPwgMMfjFTA9lWYsiiIvJO6aPfe9RjbIYcA5+KsSWAiEA5L1UHt5KS9POpz9EmKN3LesPLalX/iAH1+kb43Xbg5I=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.498b06f", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9780}}, "4.0.15": {"name": "@tailwindcss/vite", "version": "4.0.15", "dependencies": {"tailwindcss": "4.0.15", "lightningcss": "1.29.2", "@tailwindcss/node": "4.0.15", "@tailwindcss/oxide": "4.0.15"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "1edb04bf803a2518cc4113b26b020c035ae817c1", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.15.tgz", "fileCount": 5, "integrity": "sha512-JRexava80NijI8cTcLXNM3nQL5A0ptTHI8oJLLe8z1MpNB6p5J4WCdJJP8RoyHu8/eB1JzEdbpH86eGfbuaezQ==", "signatures": [{"sig": "MEQCIDr7rKqAcQTERhuriMPeAgMlyd7KmWkMcRurJDFOUGJMAiBlV3uDIee1U3r51v34bsq1BB8hfEMQ8Te3ffOYVcVQkQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.15", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9716}}, "0.0.0-insiders.91c0d56": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.91c0d56", "dependencies": {"tailwindcss": "0.0.0-insiders.91c0d56", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.91c0d56", "@tailwindcss/oxide": "0.0.0-insiders.91c0d56"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "722d88ba1ccf7632adbe7d3b34fa2d3eb3c7ec2b", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.91c0d56.tgz", "fileCount": 5, "integrity": "sha512-OGHfHAQk2d0/pP0AYoo6WmssZ4m6geVnvzbrIvPLIJvU9f6k0Is4xyinzCwKNJlvGWT9QwmuEP7R+tULXGhPUw==", "signatures": [{"sig": "MEUCIE6e1yPQ6un9F2p7mo2jru5JkVZCGnWfex+MK+fdqqUyAiEAhsKlqaZX7w8gm5nO85mgUYWoa5jQQ6+xBPkr7NHJr4k=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.91c0d56", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9780}}, "0.0.0-insiders.5426baf": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.5426baf", "dependencies": {"tailwindcss": "0.0.0-insiders.5426baf", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.5426baf", "@tailwindcss/oxide": "0.0.0-insiders.5426baf"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "a12896a631f1f698f84cfc5b9609ea1552caa052", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.5426baf.tgz", "fileCount": 5, "integrity": "sha512-Xe6nuG3StV9h7Qlbxyj3VVnOoz+f4EifAdxGHglxmq5oxaYTxvJ0IZ9Mv99PeHd/eGyeC521IkNiIzAsUwznCQ==", "signatures": [{"sig": "MEQCIBNzNb+P6xhnAbp5jO3GGcMxne9Dgz1+ZfYF4riHbWlNAiBngUXNgOPRQUBHvpa+4p45xsifCAwBzjOD8tcfJWOCSg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.5426baf", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9780}}, "0.0.0-insiders.1c481b8": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.1c481b8", "dependencies": {"tailwindcss": "0.0.0-insiders.1c481b8", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.1c481b8", "@tailwindcss/oxide": "0.0.0-insiders.1c481b8"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "68d8856374ec1849370e9d1a6043dac227b72e42", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.1c481b8.tgz", "fileCount": 5, "integrity": "sha512-2N5dzotowZhfFk978PTl+5umuq29oEcDNOOwKfTSA901rFX6p/I+eeQFJBfHV5N0x7FfV5Wry7vli7TFCKk5Gg==", "signatures": [{"sig": "MEQCIClP6/d0Pae3GBXDRymP0Mdt/65eP6XTjRzo0JIRz513AiAgr+/gfdek42iC+GkuYV7UMDoqYlEdw/kVr0OA3EW5rg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.1c481b8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9780}}, "0.0.0-insiders.42f68bb": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.42f68bb", "dependencies": {"tailwindcss": "0.0.0-insiders.42f68bb", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.42f68bb", "@tailwindcss/oxide": "0.0.0-insiders.42f68bb"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "d518e8894a3688a14554a8bdad823c98c1bc5176", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.42f68bb.tgz", "fileCount": 5, "integrity": "sha512-gaISDo/LA6iTXTTfl1PVzRITCXxq5efcJ5N6jcXKOHVN82Zxt9CrNO57gE8kxj1TSdKgF3IVXrCYQyHCCozp1Q==", "signatures": [{"sig": "MEQCIAUdLKmgldpbNhBPVLxYg2WpcSV5HkcGK4VbOA4aVit4AiBoEqJ4hi3eFoEc36sn9axAok2EEAyOQl6W0phBnZi3Ug==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.42f68bb", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9780}}, "0.0.0-insiders.fac8f7d": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.fac8f7d", "dependencies": {"tailwindcss": "0.0.0-insiders.fac8f7d", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.fac8f7d", "@tailwindcss/oxide": "0.0.0-insiders.fac8f7d"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "5b3417477c86c8bade90c2c5a69ec09a981a3af2", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.fac8f7d.tgz", "fileCount": 5, "integrity": "sha512-5YoCMpC0CdAhLXPv0eBqNu6ebwhacHfgmJH353SgS8UFHgMJPcUTKMDJdw5UFhdMnha3x47jX9ZL06YLiMlAgg==", "signatures": [{"sig": "MEYCIQCw68ClVn3yUba7Av+aAb1gHNMJSodFHjgMxW9ADz2lsgIhAJPOaOzY4Ejpo7ZkZjvKsYwpokrEJQxTXu9HlmNC8mf2", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.fac8f7d", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9833}}, "0.0.0-insiders.711b9cd": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.711b9cd", "dependencies": {"tailwindcss": "0.0.0-insiders.711b9cd", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.711b9cd", "@tailwindcss/oxide": "0.0.0-insiders.711b9cd"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "9413a771375f04b1f78a09b755b18599027adaee", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.711b9cd.tgz", "fileCount": 5, "integrity": "sha512-C0+jm/sWYmKBWtM8WFGFOVmKK18iEdNuu1+U4u/1hNcUAUyENM0ANss/riQF8/FTVcaec5CZQ8+SZ6H5YOnCHg==", "signatures": [{"sig": "MEUCIQCfhhpabAYBfYt/Xx8qkOhpyP263VQYAKz0WiaZZtzfGQIgMaFqRkzFw7Mlgz8WUzhBnLs6EMe5fO2jOYSd6Xtp998=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.711b9cd", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9833}}, "0.0.0-insiders.baa016a": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.baa016a", "dependencies": {"tailwindcss": "0.0.0-insiders.baa016a", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.baa016a", "@tailwindcss/oxide": "0.0.0-insiders.baa016a"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "80bb9751b88d0806c63ac7d1c56eeb8f58490239", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.baa016a.tgz", "fileCount": 5, "integrity": "sha512-tkef3YGo3w7PUAQ8XTBCCBkwBf3oSXoFBqUywDc9VRYX11mVZmmF00/4gMxe5ctIJiWzf7SHyRPzLfy57GY5pQ==", "signatures": [{"sig": "MEYCIQD/8tkGOsmKik3qI09F80rUigrQcdVBPEu94TIMf6od+QIhAI1CjDvDaMbD/PcOavRm0mLpDTAeF9YCt390Rrv0pFOP", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.baa016a", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9833}}, "0.0.0-insiders.e8715d0": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.e8715d0", "dependencies": {"tailwindcss": "0.0.0-insiders.e8715d0", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.e8715d0", "@tailwindcss/oxide": "0.0.0-insiders.e8715d0"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "9debd128574815fd465e4144a184e2b9062d85b0", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.e8715d0.tgz", "fileCount": 5, "integrity": "sha512-/XGhfOR208XxFpA5VRb9TaPMDt6/sGL9Seu3fThw3JL/1+Y06VX3Bjlc7bMEIJqcaQzuoK96CGAnh+A4Y+hU9g==", "signatures": [{"sig": "MEUCIH02bsgstpjEUUQOBs2fz46gCWIPnS16T+mIH/3aeLAeAiEAteOD1FQ3CrfIC6TVJYoBiZbHswzUOh3/DFLlkQYDzaE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.e8715d0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9833}}, "0.0.0-insiders.bd501e8": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.bd501e8", "dependencies": {"tailwindcss": "0.0.0-insiders.bd501e8", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.bd501e8", "@tailwindcss/oxide": "0.0.0-insiders.bd501e8"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "66878213a04cf6488ed5a7214544178b4c3298a5", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.bd501e8.tgz", "fileCount": 5, "integrity": "sha512-jyI9S7zNv0gOzOKocUBYbRHYe9+lrWNfDkzgyUzVOppGa7sSM4X+iXnl0Vy7kLos8vVnpCBULDqQzr4/gUYiTQ==", "signatures": [{"sig": "MEUCIQCAgLEgQY9hFISWaKKGZMGsylvL4VMraAmLQ+AY5OZUfwIgDlqxy8FTMSHy9C6xowDO2L50gSdsOZF25M4ZZIksHJI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.bd501e8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9833}}, "0.0.0-insiders.1c50b5c": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.1c50b5c", "dependencies": {"tailwindcss": "0.0.0-insiders.1c50b5c", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.1c50b5c", "@tailwindcss/oxide": "0.0.0-insiders.1c50b5c"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "311c0e56b0c2f94e8b9eabc656122e2d507c4acd", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.1c50b5c.tgz", "fileCount": 5, "integrity": "sha512-7uMgkV0642NbqJ0e5DmvxKRtXsMOqeIlbKeC08JdcyGqaNUUV5RRDzSNOHt6KhTLa6rV5OLTkh1TsTrSJm9pUA==", "signatures": [{"sig": "MEUCIA87gVqioGbfU8LjV5svb0wMJ2kuXUzT8vLuXpxOV5hIAiEAzd2oCs1WL2L4mKimp1QOFpe8xYbe6SvRmnSxs+476k4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.1c50b5c", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9833}}, "4.0.16": {"name": "@tailwindcss/vite", "version": "4.0.16", "dependencies": {"tailwindcss": "4.0.16", "lightningcss": "1.29.2", "@tailwindcss/node": "4.0.16", "@tailwindcss/oxide": "4.0.16"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "f17f1b9eaca7ad150775975a9c2959663b754b56", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.16.tgz", "fileCount": 5, "integrity": "sha512-6mZVWhAyjVNMMRw0Pvv2RZfTttjsAClU8HouLNZbeLbX0yURMa0UYEY/qS4dB1tZlRpiDBnCLsGsWbxEyIjW6A==", "signatures": [{"sig": "MEQCIG0U2H3cvs8k8pmaAotij/1ZGndCLThT86BAA178zjE6AiBy1wvkziSXo1o7ksaoUDq8H4PpGGkSwvgnsu38sCqtLw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.16", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9769}}, "0.0.0-insiders.c45616f": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.c45616f", "dependencies": {"tailwindcss": "0.0.0-insiders.c45616f", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.c45616f", "@tailwindcss/oxide": "0.0.0-insiders.c45616f"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "b18466eca749ee097ce2302211cd8176bbc62825", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.c45616f.tgz", "fileCount": 5, "integrity": "sha512-1XniWPxWh2uHSn8+bXn2NpzLaIFwFNSNWe3CB1JCOBvEtft+kqMa27Qiy3GNeM51c9RwB219Hrdka+7p56Ji+A==", "signatures": [{"sig": "MEQCIA0COiN35Ds80wZHCC821dOaV6MdB1qAxkMndqVwuaJ8AiAeCRZx11gjJ4RW7qJN/bFIDnM984jDPq+AiRlorsJinQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.c45616f", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9862}}, "0.0.0-insiders.1ef9775": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.1ef9775", "dependencies": {"tailwindcss": "0.0.0-insiders.1ef9775", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.1ef9775", "@tailwindcss/oxide": "0.0.0-insiders.1ef9775"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "deb11d9e8c694c1c962ccc2c3775ff81391399e1", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.1ef9775.tgz", "fileCount": 5, "integrity": "sha512-wTVCGy4PObjbpKmeLLDL9Yta2tEryz5l+76eeq+MOWPmuF1cgQ5p0iHZCzSdxY3z4kc7DEjshA/V8JdBo7gbjQ==", "signatures": [{"sig": "MEYCIQC2rSeplwoxBguXOa9ek8E956jylpB8SIBnH+7umISXdwIhAI/uBtfvrE41EtU3tRfSKUHlhtNhb+oPNydWuc6iQIIo", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.1ef9775", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9862}}, "0.0.0-insiders.3dcd615": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.3dcd615", "dependencies": {"tailwindcss": "0.0.0-insiders.3dcd615", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.3dcd615", "@tailwindcss/oxide": "0.0.0-insiders.3dcd615"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "844eacd6d61841c990c31ed3183a926ff994e1c1", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.3dcd615.tgz", "fileCount": 5, "integrity": "sha512-L9BB9buapwBGNKGXmmpeoOKbVFQZld2R73+zDxisTLvsw5IVVY8eyJC3l3XpEqzBdBRQZnZOTO6FSUDGzR39mg==", "signatures": [{"sig": "MEUCIFZGbjFtepEy+3R4V3SBGd/mF+XiuGfe2Ml/BIvj8a+8AiEAlVDm3QDwDg9T4HzP7BitJqiUzSm3K4g4qzdPoVXjAb4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.3dcd615", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9862}}, "4.0.17": {"name": "@tailwindcss/vite", "version": "4.0.17", "dependencies": {"tailwindcss": "4.0.17", "lightningcss": "1.29.2", "@tailwindcss/node": "4.0.17", "@tailwindcss/oxide": "4.0.17"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "57d710e96a3d73aa0e54093d480eb0f9466a82fb", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.17.tgz", "fileCount": 5, "integrity": "sha512-HJbBYDlDVg5cvYZzECb6xwc1IDCEM3uJi3hEZp3BjZGCNGJcTsnCpan+z+VMW0zo6gR0U6O6ElqU1OoZ74Dhww==", "signatures": [{"sig": "MEYCIQCwCoqQq8LTpSeDfKmzp38hfXBrBcNwB4apyZW0l3/fBQIhAMn6w59bV3g6ia+IIUTgJ/867qM0pF+5YMsmBFFvPH65", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.0.17", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9769}}, "0.0.0-insiders.18365ff": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.18365ff", "dependencies": {"tailwindcss": "0.0.0-insiders.18365ff", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.18365ff", "@tailwindcss/oxide": "0.0.0-insiders.18365ff"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "f96fb72b88fdc5e37c8cf45de3690651d18e8212", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.18365ff.tgz", "fileCount": 5, "integrity": "sha512-hY0DS82UVLfu1bOJGmqSjCDQxbNJJjr1ESrcFwt3m7Grm2bBchxaDj3Iic+VYprTTfswN2z8+aqPV7QV8M+GkQ==", "signatures": [{"sig": "MEQCIBif8aRAQ81kJrI7VARl3DRle3wHKVIAr9e49K9RESf/AiA68lKIFgygUIatz2uMmsZaNtZ7gZdwOtrBo1zzAyEc4g==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.18365ff", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9862}}, "0.0.0-insiders.b0c48c3": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.b0c48c3", "dependencies": {"tailwindcss": "0.0.0-insiders.b0c48c3", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.b0c48c3", "@tailwindcss/oxide": "0.0.0-insiders.b0c48c3"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "c7c0934ba6d8bc1ba6a8b0708619a2fd5908de97", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.b0c48c3.tgz", "fileCount": 5, "integrity": "sha512-ZxNlONnbb+vZAk2934qXUq8QRfXwsbFddr5Pw09Np7eVPI6aMGqIasltMQt6Qg3pqMxn4uaXip9YnVI/R1O5yg==", "signatures": [{"sig": "MEYCIQCO6WheTBjRzrPosL10rDnlmhCpDcCgsgA2eaRa+muEJQIhANV3LVpYtjHM4KkanVc4BsaBhGUOQbv7XzKZluXx8tDn", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.b0c48c3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9862}}, "0.0.0-insiders.224ce0b": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.224ce0b", "dependencies": {"tailwindcss": "0.0.0-insiders.224ce0b", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.224ce0b", "@tailwindcss/oxide": "0.0.0-insiders.224ce0b"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "96a655d9061c342d3b1ea09281b77b8edf7c6049", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.224ce0b.tgz", "fileCount": 5, "integrity": "sha512-ooGSgkjLSqEZLMR2vAn/svMwkla/5IeYvosUWnGcZuYt8fUoJHsO1Ty+EC2PvRis/a9EAtyDpVy8cbMQYH/+bw==", "signatures": [{"sig": "MEUCIQCDoPjsvvo19MJoMJWJizMQxvcor6CPAY5M6JkH6Zw8JgIgU8ctxt2YznP2sRJgh2oGTbAvTe8KxuCdsw+28GltKM8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.224ce0b", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9862}}, "0.0.0-insiders.eecb6f7": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.eecb6f7", "dependencies": {"tailwindcss": "0.0.0-insiders.eecb6f7", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.eecb6f7", "@tailwindcss/oxide": "0.0.0-insiders.eecb6f7"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "8c56c7bbc4aef510c256d22ff8df5bedec94662b", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.eecb6f7.tgz", "fileCount": 5, "integrity": "sha512-slqQxNO2sIhd4yWKUCOM1KPC24vL8cO+iEch3EbXw7+M2ecxKiBzOfMCjZF7a3pR9aUckehbhH7bcrvbgUucRA==", "signatures": [{"sig": "MEYCIQDeQ7nQ1eaXmL9IGKAGJmADHr7xPwl7ZzfBFVTlaJxvVAIhAK1ejmqpyPCaEUH9rGMRFsWGO5A1euUwlzimDBnFP5rd", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.eecb6f7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9862}}, "0.0.0-insiders.3e53e25": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.3e53e25", "dependencies": {"tailwindcss": "0.0.0-insiders.3e53e25", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.3e53e25", "@tailwindcss/oxide": "0.0.0-insiders.3e53e25"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "7237ceecea5b495d7606b4bd438c5b267e9fa21c", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.3e53e25.tgz", "fileCount": 5, "integrity": "sha512-VVSllPCy9EyxDw4gvsWaLb1MAHD+ks6Fo0fMTIRJ9hEB+nUw9IlLSxWWVWM05OnANkV8iYrs5lVz4dtS1fIB9g==", "signatures": [{"sig": "MEUCIGWhkGZZ2eU3IN77AaVPGPPY+U4dNPHZnXdwZBCoSI3IAiEAlEOq6kiBnt2mnc/3PEpMp6TT+15RnCEnP5qXBblt5C4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.3e53e25", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9862}}, "0.0.0-insiders.ec2f3bb": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.ec2f3bb", "dependencies": {"tailwindcss": "0.0.0-insiders.ec2f3bb", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.ec2f3bb", "@tailwindcss/oxide": "0.0.0-insiders.ec2f3bb"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "1ab2105876c15524e08075ea84a82e6aaf0187d0", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.ec2f3bb.tgz", "fileCount": 5, "integrity": "sha512-lAI65p1cndV8+xdm4Chi3Jb1oSZP2HwZVP6kltYJ8ouPCAmq27aDJIORBC7KROCsFAVSXBdaziWn1IvX+/1tgA==", "signatures": [{"sig": "MEYCIQDUZvusnYajCg1OBHPefRPxal4Lkkq4sTzkeZyoogMY4AIhAPai0Q8K0MmSPckXihdlgc4uJFnCBwS9bCR3TUw/jr3J", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.ec2f3bb", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9862}}, "0.0.0-insiders.c3ae9d1": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.c3ae9d1", "dependencies": {"tailwindcss": "0.0.0-insiders.c3ae9d1", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.c3ae9d1", "@tailwindcss/oxide": "0.0.0-insiders.c3ae9d1"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "a10c142bfe7a1b33ca30602b8c9cf78c4f649c05", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.c3ae9d1.tgz", "fileCount": 5, "integrity": "sha512-rSU7aMs7nlAtlV9J0T9hDzG0CYnQEzyQpjSVi4FGoWlOF51s6zF5BS3s0cwmS2X8YzhnzitYXEmqF6X3t1oELg==", "signatures": [{"sig": "MEQCIHLUuEH633JnLLVkB/gOWJueGPpExMMElTX6KRWS74QMAiBV32l8uZRs+T/yBZzGsiAtjG3NTVOQ1qRXJhdeFssb8g==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.c3ae9d1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9862}}, "0.0.0-insiders.601f369": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.601f369", "dependencies": {"tailwindcss": "0.0.0-insiders.601f369", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.601f369", "@tailwindcss/oxide": "0.0.0-insiders.601f369"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "5587576d5f5507300f34d1cf15164bfe45adf948", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.601f369.tgz", "fileCount": 5, "integrity": "sha512-pVUs2Xgz0wSQwGFVEaLOztd7ByYjXFsRRDlMYZpkvk9J+WTmd3HoTyF/jVEbdNdBz+cQHs05kLm1XpO118I8SQ==", "signatures": [{"sig": "MEUCIC+Pk4WD+5Sq1pNrGu4SJdtEWp9NfXZmzcEZ/DLfxK6UAiEA6flG/SuEtTX5I9DxPV7AW/FE8YvgKqzO8/2g+A4MeeM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.601f369", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9862}}, "0.0.0-insiders.9cceeaa": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.9cceeaa", "dependencies": {"tailwindcss": "0.0.0-insiders.9cceeaa", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.9cceeaa", "@tailwindcss/oxide": "0.0.0-insiders.9cceeaa"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "91e03d30220d57ecadb5cdb98c59c402e43d2702", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.9cceeaa.tgz", "fileCount": 5, "integrity": "sha512-4szR4Qkg5WZtCsDtdbG7ug8fhDaJqBjV9BXMjAtbmIlgFIHf3Lxhumo9V4Vc5SAvlHun+sBuiyC+kiReHPeyGA==", "signatures": [{"sig": "MEYCIQDUjwlmvC35bxm8x0q34oFsYRWUbTNmx6oYLTK49SpU+QIhALzWI6wVmQ5R14MbTWiSoKqCR4TKV1mHISBh08Yh6kqa", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.9cceeaa", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9862}}, "0.0.0-insiders.1b6230f": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.1b6230f", "dependencies": {"tailwindcss": "0.0.0-insiders.1b6230f", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.1b6230f", "@tailwindcss/oxide": "0.0.0-insiders.1b6230f"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "81b12db62a4b8d1ddd05389c5e0cd6e0fe8c45a3", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.1b6230f.tgz", "fileCount": 5, "integrity": "sha512-QxUHusB6+Iseq39H2+Nib38WbACK4cCoIrB+FTsAaO5IGbie0ml3Z7aVnWxBa4ZQuy1nJ/sRHEmM5g3gesKjCQ==", "signatures": [{"sig": "MEUCIBjs0/ZGRBJ3XZjwt7Gv9YiX+aA2qK8zLPfgzsD0HYO/AiEA+dCyr9s8FItY3HB83AU1jvO9A6qqqACksTGdyB+yaRQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.1b6230f", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9862}}, "0.0.0-insiders.ab868c6": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.ab868c6", "dependencies": {"tailwindcss": "0.0.0-insiders.ab868c6", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.ab868c6", "@tailwindcss/oxide": "0.0.0-insiders.ab868c6"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "b3644602b8bde333fcb53936167ab87316499c4e", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.ab868c6.tgz", "fileCount": 5, "integrity": "sha512-dTSDetAFA7LPuFE/yNPeRNxBwp6kcX1YkP813G1LIPrPnAh0DQEp9mvJrQk559Htz0mwBnCDiB5QvWtqJV6u9w==", "signatures": [{"sig": "MEUCIH3RctRJIfWppVd9dY5tG9VwghJWeP8d7eb+7pyYb/SFAiEA13sOAUpmy+QPyv1YzniUb1ebukqgKLwjztJtu4Qdwqk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.ab868c6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9862}}, "0.0.0-insiders.3412a96": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.3412a96", "dependencies": {"tailwindcss": "0.0.0-insiders.3412a96", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.3412a96", "@tailwindcss/oxide": "0.0.0-insiders.3412a96"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "55b632a0d4d773ad3f54cd38fb9852f7d1e3bc32", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.3412a96.tgz", "fileCount": 5, "integrity": "sha512-A/iQp8Nqsc2XsdVJLQaN1tD7jIpou0MT3VfQpGTpuATbIjzupk3uWYW5hSUdi7uXYisV/LC8m8YmJqtWByGYvA==", "signatures": [{"sig": "MEUCIQCKu/Q9xSfkt0Sjk7QLusxFH4OGFK9Mz6fJB/YPp3z9sQIgbblYetBxp47Aa8q/SHFTCRB8edVVJrhHDWUtdNJANWY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.3412a96", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9862}}, "0.0.0-insiders.2af7c57": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.2af7c57", "dependencies": {"tailwindcss": "0.0.0-insiders.2af7c57", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.2af7c57", "@tailwindcss/oxide": "0.0.0-insiders.2af7c57"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "2a5895b72f6ac966f4997290f97b504697b72f62", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.2af7c57.tgz", "fileCount": 5, "integrity": "sha512-NbsMvpoz6LOpbrZe1CYNf8HEwJ3Ue1BWk4yzVm4N0GAzoSRjMeZuuAjL6nryCF7zda3XlNUEOuVmjNIKTuDvOA==", "signatures": [{"sig": "MEUCIGos3XKKBP2jkCAULXQ991drss2LUhxdfg1B7eknub/eAiEA32Yh/7izYa4j3Tqw/yZmb/WPoLsjCYg5r36jxai2pb0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.2af7c57", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9862}}, "0.0.0-insiders.5e255de": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.5e255de", "dependencies": {"tailwindcss": "0.0.0-insiders.5e255de", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.5e255de", "@tailwindcss/oxide": "0.0.0-insiders.5e255de"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "7553f8cb5585632c6d28a072df8679c95444d5e8", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.5e255de.tgz", "fileCount": 5, "integrity": "sha512-OjgvQ8L7q5Gon/DnX+KIvFMliFdmlHhX4t2AcONuJj7/4gdDj9I6dCJPueyt4I2ZYUjMculpYTL5/4J2zGX7Qg==", "signatures": [{"sig": "MEUCIQCTnBLYTKWUC8tThBcpCz78hLDaOPhl8TzOfcKeEP6omwIgbC4mLs6Jzyh+aWETt9lOjSdSqOd945kkB7lCjV10RYA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.5e255de", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9862}}, "0.0.0-insiders.1a68b99": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.1a68b99", "dependencies": {"tailwindcss": "0.0.0-insiders.1a68b99", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.1a68b99", "@tailwindcss/oxide": "0.0.0-insiders.1a68b99"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "cb61b00bda4756cc340886e921117f7421e136d6", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.1a68b99.tgz", "fileCount": 5, "integrity": "sha512-U2NsJ0LLvfDG0l7Ud6BNMUD3QMSpChfzi/yH8DOopedBQI7Eba6N6hHh946qbIhFXg3lFb4BnzDeKuQAdy0hYA==", "signatures": [{"sig": "MEYCIQDmAWftP8TgtLMDibYowJ+qt2Xzm0vbJqWjVnNdVre4ZwIhALmDLt1HDRheBO+C11dLCkndsPPmefih9h3DezCjQwPP", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.1a68b99", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9862}}, "0.0.0-insiders.f772266": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.f772266", "dependencies": {"tailwindcss": "0.0.0-insiders.f772266", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.f772266", "@tailwindcss/oxide": "0.0.0-insiders.f772266"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "93c1b8418229cf165c0721951131100cbdbb0e8d", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.f772266.tgz", "fileCount": 5, "integrity": "sha512-qyGHS0tl8wLBcw6Arn94C0q3sJc6JrDXeTaKwUgHwnWTOOiDSQsATvdBvO7vIacCNUS4QuerOIPXs4YsCbFmDQ==", "signatures": [{"sig": "MEUCIQCta0DS0J3Owjt4kytjhhgjNoWByb7gxe8USq4xduGEYQIgJebTKjv7vL/kPrftUDsjy+jTlejprskh2I4wfMWBbXs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.f772266", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9862}}, "0.0.0-insiders.c32b608": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.c32b608", "dependencies": {"tailwindcss": "0.0.0-insiders.c32b608", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.c32b608", "@tailwindcss/oxide": "0.0.0-insiders.c32b608"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "0e15a2a6e09a47293e55fcc183e36cb58d4ada4f", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.c32b608.tgz", "fileCount": 5, "integrity": "sha512-qjs59zNBekrIvExOFii1PDmjTOan5/OvUeKC50YDPq45dSi82IY948hgCMogSGZ503jZKMeJnnHKlHqerr87rw==", "signatures": [{"sig": "MEQCICKOB0osn2batnmxziBdW0op9uhQUstXBzFiZV9ALJYiAiAtk6jP01Fp8h/+MdcYUnjx4ugMtKR37VVYLfql3aIm/w==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.c32b608", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9862}}, "0.0.0-insiders.eec1bf2": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.eec1bf2", "dependencies": {"tailwindcss": "0.0.0-insiders.eec1bf2", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.eec1bf2", "@tailwindcss/oxide": "0.0.0-insiders.eec1bf2"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "4c0e579fdf6d8dfc71ba1f6977eadd1e4c7e9eb2", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.eec1bf2.tgz", "fileCount": 5, "integrity": "sha512-ExTqZfgex3Lmymxh939yriVvbJI2gIBbnOm2uc02PIqLuzyTbl+9Is485BOitfxY24O7neoA1b77pImYgHgZgg==", "signatures": [{"sig": "MEUCIBSLvdNMo1p9dwa+PxjRqhwEkTxpmejsVlC/A6s2we+/AiEAng9qUTKjFThZX6nw073sLjLNoyBqcKC2JZy3/iexCCE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.eec1bf2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9862}}, "0.0.0-insiders.c7ba564": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.c7ba564", "dependencies": {"tailwindcss": "0.0.0-insiders.c7ba564", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.c7ba564", "@tailwindcss/oxide": "0.0.0-insiders.c7ba564"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "fb5d59dc57278e9393b754c96511a25b2e1be5f4", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.c7ba564.tgz", "fileCount": 5, "integrity": "sha512-Jbrh474A83BnXZVQ/YDG+497WBjvQ+LS9pRxlAqRMuVy/mjA0IdPaEUQaTkEK5Yj9lMiR+qlrkTgFd7qddOPAQ==", "signatures": [{"sig": "MEQCHyBiCKYrB5zEJNn6fkG9I/LkPZNgh9+QpF5L6IG4IEYCIQDu9bWSsLr8l4d6KhjpeN+ECtExskYJBQGeAonnkNFUlw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.c7ba564", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9862}}, "0.0.0-insiders.5380109": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.5380109", "dependencies": {"tailwindcss": "0.0.0-insiders.5380109", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.5380109", "@tailwindcss/oxide": "0.0.0-insiders.5380109"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "943955a9dd83f530772e1da51b06a433473f7847", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.5380109.tgz", "fileCount": 5, "integrity": "sha512-ZGilzQwB1NIsnYO/7oTD4VlU84HhyEMjRZPJkx6FABiwieiD+1yA4VBfwJku3PMG39SqE4LAtf/NUILcMOFagg==", "signatures": [{"sig": "MEYCIQDzs+kdn8ZIXW0YAa6JuH38iT0V4H+wifh3Au/oKFHZpAIhALEL+giUVEKXKh3Xp2tsIkg3oiC0f8JOpe9j5u0kjI86", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.5380109", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9862}}, "0.0.0-insiders.d54e23d": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.d54e23d", "dependencies": {"tailwindcss": "0.0.0-insiders.d54e23d", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.d54e23d", "@tailwindcss/oxide": "0.0.0-insiders.d54e23d"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "3fcdb31efe2c8019e7c60dd0d8f5ee672915b16e", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.d54e23d.tgz", "fileCount": 5, "integrity": "sha512-LBa4cDBk97QZLMOxHBFmOjiHlns373SCBp8kI5NHJ+OINtlPkMv+5uX+vypQjisD/g8p0qEKxNVi4Rck1wWc/g==", "signatures": [{"sig": "MEYCIQCsRYY2tTZyRMpTagO34gDeU5K7Lly/Xt1M+pPosR/OAQIhAMHkvJcSKxFeHjxGTD/OBgtGfoON1g2WjgytPNn5w4gk", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.d54e23d", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9862}}, "0.0.0-insiders.80017eb": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.80017eb", "dependencies": {"tailwindcss": "0.0.0-insiders.80017eb", "lightningcss": "1.29.2", "@tailwindcss/node": "0.0.0-insiders.80017eb", "@tailwindcss/oxide": "0.0.0-insiders.80017eb"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "f4c07f223a317fabe51e30403e7c0951912397e9", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.80017eb.tgz", "fileCount": 5, "integrity": "sha512-sChD245Q8lWsw2WplvbGCz2yuID3xrVOEJ3yXqMP9sDU8A+guy/NsxQYfsikQsI28fshvIj/2RTlw9MnjDRZQQ==", "signatures": [{"sig": "MEUCIGxoM+vjKpQHKc+Xa7b4vCEy877PV6wRRFmtL/2nR1HAAiEAwGkPZFtf3FfO67U12BPEnVg195tCXEKyAJ+w4Keo61A=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.80017eb", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9862}}, "0.0.0-insiders.156afc6": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.156afc6", "dependencies": {"tailwindcss": "0.0.0-insiders.156afc6", "@tailwindcss/node": "0.0.0-insiders.156afc6", "@tailwindcss/oxide": "0.0.0-insiders.156afc6"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "01417f4cd708ccf9dea414feb086625ed90f180f", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.156afc6.tgz", "fileCount": 5, "integrity": "sha512-WP+ohPQHlWaLvRnmisY/on1hDO9TV0e8K4FKqysV3jtKHFZhxvmCEG5mkBw/Uzi3ZzUhmzDNLiSJygf760+oiQ==", "signatures": [{"sig": "MEUCIQDarBujjmsnS9aY+BR4LLdo27wgIgV8kKWQeISOF3AfyQIgc4Y2if0mzzhJC6wTKQdjYMXlwuZoU3lBGlQsSLG/ifA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.156afc6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9398}}, "0.0.0-insiders.f590be4": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.f590be4", "dependencies": {"tailwindcss": "0.0.0-insiders.f590be4", "@tailwindcss/node": "0.0.0-insiders.f590be4", "@tailwindcss/oxide": "0.0.0-insiders.f590be4"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "08f4d245e8a5173b85921ed8764940f3d0044f57", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.f590be4.tgz", "fileCount": 5, "integrity": "sha512-9esG93zGXPwPp6050mqIfYBj/anjyPCIqLtprytsU4JHae0Af7gCyyUlumtCEcedYztl+XvpHnXxFLwENN1M1Q==", "signatures": [{"sig": "MEQCIEJ8Pt/Te1UJFwU8m91TLLLdWqKiCAjAlMXvF5pz5dllAiBmX8kUqj3bF1yae7shcXKcpOH925NvraBevhX73fDjAg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.f590be4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9398}}, "0.0.0-insiders.55d7a65": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.55d7a65", "dependencies": {"tailwindcss": "0.0.0-insiders.55d7a65", "@tailwindcss/node": "0.0.0-insiders.55d7a65", "@tailwindcss/oxide": "0.0.0-insiders.55d7a65"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "7931d8583a99150ceec000569c092711e35b820d", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.55d7a65.tgz", "fileCount": 5, "integrity": "sha512-2e1y6YjPU2C/ZKHxkNhTZZ7bGiQF7blBlMcGjvLhhwSbbE84vp8oIHcc2CuSXVXHT1dWJt+11YLV/SeSx/i0Mw==", "signatures": [{"sig": "MEUCIQDixEkpT2c5PLHYgRPDnNwehWZ6EdLa3BEeGynjDL+ShAIgQ19AgSQVF6Zd2yFU1YyZPsZAv4HMsSZ/j31m6G85k9I=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.55d7a65", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9398}}, "0.0.0-insiders.9374647": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.9374647", "dependencies": {"tailwindcss": "0.0.0-insiders.9374647", "@tailwindcss/node": "0.0.0-insiders.9374647", "@tailwindcss/oxide": "0.0.0-insiders.9374647"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "831e13a0f803c44f8837b54e0f85a65a84a1e0b7", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.9374647.tgz", "fileCount": 5, "integrity": "sha512-+UrO90oHgBdw1gc0I3OB/eOv26BeGCc5Ydhg1+Cm4Aw05hKlSFrfdVRQwMGG0ZfhTzQLI1lzJckd9FevkRUa3A==", "signatures": [{"sig": "MEUCIA8xstYGiG8nXcNh2w/PJPmfSCkFx4dHoq/IMyj7quG1AiEAzkeVXlwjWAW8iJHSRPrw1JTm6oFvlYuwiOTfyLwm/vU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.9374647", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9398}}, "0.0.0-insiders.b94720a": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.b94720a", "dependencies": {"tailwindcss": "0.0.0-insiders.b94720a", "@tailwindcss/node": "0.0.0-insiders.b94720a", "@tailwindcss/oxide": "0.0.0-insiders.b94720a"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "e47a827a9eab4da064c15019c7853ce3d46c0d4b", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.b94720a.tgz", "fileCount": 5, "integrity": "sha512-UnuLKdPC48Plxy2+2rT/HFPmIOLWxZ/nQeTLSGM8vNPevS4+92sky7orrI2TQMXd+qJrU7n9XvJBdmDUhxf8Rg==", "signatures": [{"sig": "MEYCIQD7ICeHEZxExwsxeqdTM7XSM8o+TzS5MgYBQfWrQy5G1wIhALAO0Lwg3i18eiHzzP51nLBEU3iZ+U7tcdAF/SCHZJjR", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.b94720a", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9398}}, "0.0.0-insiders.8f631d0": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.8f631d0", "dependencies": {"tailwindcss": "0.0.0-insiders.8f631d0", "@tailwindcss/node": "0.0.0-insiders.8f631d0", "@tailwindcss/oxide": "0.0.0-insiders.8f631d0"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "f085054d6dc61201ba345962dcbdbbd35506f5c3", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.8f631d0.tgz", "fileCount": 5, "integrity": "sha512-7PrpoE4LBbLEhVcj/h0yJsb+dHrpr8Sr0oJGcjGqQfWaVELa0IrZn4qpyMpEFIAoOrszeUYeUzMzc/brHF6lkA==", "signatures": [{"sig": "MEUCIFytGnf2P9yW4wED7DOi1IbBmtX6Guq2EqKO4LqSgM7lAiEAyKic14aoh6iZYfr4QkWXEN1OkbdyWpnK5XM3awfQ02M=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.8f631d0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9398}}, "4.1.0": {"name": "@tailwindcss/vite", "version": "4.1.0", "dependencies": {"tailwindcss": "4.1.0", "@tailwindcss/node": "4.1.0", "@tailwindcss/oxide": "4.1.0"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "576b3252cc12a6e5031d90e879464e7d2e890054", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.1.0.tgz", "fileCount": 5, "integrity": "sha512-IszG0h/o8jOGheY0f7v41a9qyDymZ5eU8qm4koTypMKagBhaQA06Keip13wch6sz7rG3cvIG7A3/ytdfRh2BUw==", "signatures": [{"sig": "MEYCIQCVdx76nXbBNT3cItgCtOaLukwer/9JaaFpULRCTEjUWgIhAMgXiSiDg36IyF/lknvwDe17MXlIsDc2HX/CaCJosNL5", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9330}}, "0.0.0-insiders.3c937ec": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.3c937ec", "dependencies": {"tailwindcss": "0.0.0-insiders.3c937ec", "@tailwindcss/node": "0.0.0-insiders.3c937ec", "@tailwindcss/oxide": "0.0.0-insiders.3c937ec"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "cc8d6a0fea8c8df9341559a4046c2a8a088082ae", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.3c937ec.tgz", "fileCount": 5, "integrity": "sha512-0hrXbR+fd4a12v6DOmmDYLK4BYXEkDDQ9LMdbio8AwJxG8hCT7uyfDK7nkV7xzLZ2qGNu1FIiHOmL2DKFjyiIQ==", "signatures": [{"sig": "MEUCIQDp+OY46Bk96KqoMtU5lExI/R35nqVrxcJhL0O0nE+7JgIgEzhn16T+yvtgDp4eihQvZhihGssJMVTbCtIOt33+aig=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.3c937ec", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9398}}, "0.0.0-insiders.a429462": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.a429462", "dependencies": {"tailwindcss": "0.0.0-insiders.a429462", "@tailwindcss/node": "0.0.0-insiders.a429462", "@tailwindcss/oxide": "0.0.0-insiders.a429462"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "eb7d5a947d4f9982e93bad1224a51833657a1e47", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.a429462.tgz", "fileCount": 5, "integrity": "sha512-DTJpwxd0r+S+8Xha47WY2KkW6/tW8dgxoIx7Ez3zFdHC0I0FX5mg3vTSrdc5V4sgfH3TbmaGDMhcuiJy/eQ3hw==", "signatures": [{"sig": "MEQCIDS0Fmkfh3AxGixL1maJc1zq0ga9iMvJAzFjsWwQV/LVAiAfS9qE3h/GpsRIw9BSvJUWJOmcAP/tzz+ttCTHo7jp8g==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.a429462", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9398}}, "0.0.0-insiders.6a0a3ec": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.6a0a3ec", "dependencies": {"tailwindcss": "0.0.0-insiders.6a0a3ec", "@tailwindcss/node": "0.0.0-insiders.6a0a3ec", "@tailwindcss/oxide": "0.0.0-insiders.6a0a3ec"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "0fb872ce0b6938e6c9332a6e1751f820871d4fe0", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.6a0a3ec.tgz", "fileCount": 5, "integrity": "sha512-VcKFP5M0dIx+wB85HckNy0oIFm8X10FNA/f/pLy0ju8wrvz4I1LmkbR9jqNm7kiPwOKq9S6Tirhgii6FwR1UjQ==", "signatures": [{"sig": "MEYCIQDU88yHfJFs1Tb5dPV5+DBah/4SJJhBwQAv1jwa2dHoQQIhAOhZYrU9Sh5irp98drBkFRPMBLRONRFdB0WL8Ho2HH12", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.6a0a3ec", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9398}}, "4.1.1": {"name": "@tailwindcss/vite", "version": "4.1.1", "dependencies": {"tailwindcss": "4.1.1", "@tailwindcss/node": "4.1.1", "@tailwindcss/oxide": "4.1.1"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "508dcb9dd91e0fb036f63b5164257ed8467a704c", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.1.1.tgz", "fileCount": 5, "integrity": "sha512-tFTkRZwXq4XKr3S2dUZBxy80wbWYHdDSsu4QOB1yE1HJFKjfxKVpXtup4dyTVdQcLInoHC9lZXFPHnjoBP774g==", "signatures": [{"sig": "MEQCIDJkoOMzHnMmFu/od7mCIkf0AKqq2iw1XnweJVNYYLtcAiBBSiJUIHp9x32FntT74x31p75JhzsNeIsu5nxC44RIKw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.1.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9330}}, "0.0.0-insiders.4484192": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.4484192", "dependencies": {"tailwindcss": "0.0.0-insiders.4484192", "@tailwindcss/node": "0.0.0-insiders.4484192", "@tailwindcss/oxide": "0.0.0-insiders.4484192"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "b857e70ca55bc57883b348cd00810133fc8c9062", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.4484192.tgz", "fileCount": 5, "integrity": "sha512-W2Ynn06zm75xH3ou/YlxzYBYVZYIMks4am4CUV3Mz5Q1DEnt8ONEqUJ/G8NG6Bu0ZYE1mFiSDWBdlHh5b+4FpQ==", "signatures": [{"sig": "MEQCICCK3EUrA1k0F0dMlzy0oUpkuvM0OgepbKye46OsY04xAiBj/W/9FlYHEuhjii1HiH7mXp+LtrAELhBo6+CLEdrWyg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.4484192", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9398}}, "0.0.0-insiders.80f9578": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.80f9578", "dependencies": {"tailwindcss": "0.0.0-insiders.80f9578", "@tailwindcss/node": "0.0.0-insiders.80f9578", "@tailwindcss/oxide": "0.0.0-insiders.80f9578"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "be23f7c34530b097719e7b939004de6caf50741a", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.80f9578.tgz", "fileCount": 5, "integrity": "sha512-ZhLfHJsqT+Bikv6YoGW40mGh/xgWQYEgtnpCCwdRpWJ4+7nr8c5D0fiQ47zQy2822OzhubVJ9r/8azGS/TV6/g==", "signatures": [{"sig": "MEUCIQCcnIQc5Xi+ZLYJu+d/rBo6F3YwBNKabYCJFO4jdaYOZgIga/j2W5FoTKLpbce+FB5RkufDd0tLJhNb+C9D0K1qmNI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.80f9578", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9398}}, "0.0.0-insiders.e5b2b0f": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.e5b2b0f", "dependencies": {"tailwindcss": "0.0.0-insiders.e5b2b0f", "@tailwindcss/node": "0.0.0-insiders.e5b2b0f", "@tailwindcss/oxide": "0.0.0-insiders.e5b2b0f"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "66bd42e42b94deb16597eaf9866edf18ad93430b", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.e5b2b0f.tgz", "fileCount": 5, "integrity": "sha512-EjSNy461/DnrG78/PWhthWXmNB6IOVl+sYig6ei9YDzrJ70LbkqL4/cm6mKna07zUbg8ep1zsuzIRKXwPxr4ag==", "signatures": [{"sig": "MEUCIETSPSgzxRHHBdVf5IJZhuLt5n1R+bhsNtzySPJBcs17AiEA5JZS2Et0nzlSu/8vCrPJ5BIM0s30KFSOQRKUyy8zqgk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.e5b2b0f", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9398}}, "0.0.0-insiders.f8b9aa9": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.f8b9aa9", "dependencies": {"tailwindcss": "0.0.0-insiders.f8b9aa9", "@tailwindcss/node": "0.0.0-insiders.f8b9aa9", "@tailwindcss/oxide": "0.0.0-insiders.f8b9aa9"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "2c3b071968a1be7485ac589d83f467bc6ba5a6c2", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.f8b9aa9.tgz", "fileCount": 5, "integrity": "sha512-Zvktf9t+fe181Ky+sBGbTW6AP2VLGfG4dZYEz1n1PAnC8Yvmv0oPQh7bW8w+yq3jRlWqOy6N6kTHgxPCWS0ppA==", "signatures": [{"sig": "MEYCIQDG7yDOMGCgGsnaVHUQCi063zqwQB87SMX0jQS5E9UpOwIhAPy52opkm6ovgSAoy0ToQ/lvHn1PU9/DYhGxWm79WZZi", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.f8b9aa9", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9398}}, "0.0.0-insiders.3e41e9f": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.3e41e9f", "dependencies": {"tailwindcss": "0.0.0-insiders.3e41e9f", "@tailwindcss/node": "0.0.0-insiders.3e41e9f", "@tailwindcss/oxide": "0.0.0-insiders.3e41e9f"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "0d6ecedec0267d9c083572032dc4cc8c33f8d331", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.3e41e9f.tgz", "fileCount": 5, "integrity": "sha512-b+ehB0ASJ+Jkg8atVpVopQPfNpL5eMALofm+F4LWis68BPn35+O3SIktSrgrSnBWUlr2aYAPLkBPETZDgYBbHQ==", "signatures": [{"sig": "MEUCIAtKfNzELM3T0CBc7p6Vi8+fSPMOU7PbmDNloklXYxg5AiEA0vN94htmM0vBqo2zZ8EDiaKY1SwrPo3yRHbBzvy/hI0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.3e41e9f", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9398}}, "0.0.0-insiders.e45302b": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.e45302b", "dependencies": {"tailwindcss": "0.0.0-insiders.e45302b", "@tailwindcss/node": "0.0.0-insiders.e45302b", "@tailwindcss/oxide": "0.0.0-insiders.e45302b"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "f4d83b7025e9db09c9932e14e275209792b37b26", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.e45302b.tgz", "fileCount": 5, "integrity": "sha512-z2mdMogYH5KOAh6ETv0tVNUcyM0Ea2ojQlnpqd35ZXQpwkPXf+jRIKTEBENj0CE8p+aKYeFo5SCOy69RNeiogg==", "signatures": [{"sig": "MEUCIQDcg9EwWpky6+LCtEohQH1lkXBjCEt/3pc6XkGzLCmx3QIgG03I4GVwGjBSOiATGvUxm4LBbUE1XUev2kanJo64pC4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.e45302b", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9398}}, "0.0.0-insiders.81a676f": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.81a676f", "dependencies": {"tailwindcss": "0.0.0-insiders.81a676f", "@tailwindcss/node": "0.0.0-insiders.81a676f", "@tailwindcss/oxide": "0.0.0-insiders.81a676f"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "f444a8d7dedac0035bb93e6396b42a299007472a", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.81a676f.tgz", "fileCount": 5, "integrity": "sha512-oJjrf30n3+UZW3RSAFxcs70Cm4FwtjSkGljpxbr3Su6SwVmMVLuUiTKt0En7z1V3ra2Mo4D+D0t9Xzo5gLiFGQ==", "signatures": [{"sig": "MEUCIQDv6u5KWx4I8IUBaAVwacKLbCuPdmYjqUOJ77XZ3zdl6QIgbi/JfDFjrtiJXUq34xNmRSApZ+uutt+ZLh0QeygdlUc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.81a676f", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9398}}, "0.0.0-insiders.60b0da9": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.60b0da9", "dependencies": {"tailwindcss": "0.0.0-insiders.60b0da9", "@tailwindcss/node": "0.0.0-insiders.60b0da9", "@tailwindcss/oxide": "0.0.0-insiders.60b0da9"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "7c127511ab3be6f03297a2163a3942598e233197", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.60b0da9.tgz", "fileCount": 5, "integrity": "sha512-36wYXR/amVVO48J3jJ/NLMCYGu0wlxD3IbkGgK0CLthd+lF0GtsdFlJ2SLWj0u8kaodVhnTuqEBNFyucd9DQxQ==", "signatures": [{"sig": "MEYCIQDuD05HrRWKgJXnq1cDislXwwQ68yHd9qmsSuzlZEmexAIhAI7wnSnlkFBfgYboSvDe219NbE3W0Vyj/2zXesiapB7C", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.60b0da9", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9398}}, "0.0.0-insiders.4200a1e": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.4200a1e", "dependencies": {"tailwindcss": "0.0.0-insiders.4200a1e", "@tailwindcss/node": "0.0.0-insiders.4200a1e", "@tailwindcss/oxide": "0.0.0-insiders.4200a1e"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "eb8da56f63c5f457ac2b0f722b4c2c9772dae251", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.4200a1e.tgz", "fileCount": 5, "integrity": "sha512-EDEXdeDoumCBMpOmwkVykqTBsHYhZjDWAfhcfkfvf17Lkopn6FbDB9FDBmnOZd/+1nBgllYnv4ymgfnIb1MUlw==", "signatures": [{"sig": "MEUCIFlyUird/D+jJL2/JUEsZXee0HQc9fGWYzgP4Ah4JT9rAiEAy9i2j0VXsFJH+ihC8VjmD3gSOoHIfLVWyigHOhA/ILk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.4200a1e", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9398}}, "0.0.0-insiders.5a9d1f4": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.5a9d1f4", "dependencies": {"tailwindcss": "0.0.0-insiders.5a9d1f4", "@tailwindcss/node": "0.0.0-insiders.5a9d1f4", "@tailwindcss/oxide": "0.0.0-insiders.5a9d1f4"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "914566c1337639ecafc5eecfd6a56dac978f13a8", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.5a9d1f4.tgz", "fileCount": 5, "integrity": "sha512-5ssZDhew7uMWRAA1tSG4n7ueb/SveaRxrzqsHI6O34yebm/jaz/O9/t9h1Z6M24egZSdR3BGXJNF0kfH4MgZPw==", "signatures": [{"sig": "MEQCIGtmpdOdDTxDx9KQhrBGiRaTWbiRnsDxhz+ffMBP3dRaAiA7BEsqp2oD5X3m10MU6K9Hc4Q2Q4iY7vZqbqSwBDZsCg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.5a9d1f4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9398}}, "0.0.0-insiders.4c99367": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.4c99367", "dependencies": {"tailwindcss": "0.0.0-insiders.4c99367", "@tailwindcss/node": "0.0.0-insiders.4c99367", "@tailwindcss/oxide": "0.0.0-insiders.4c99367"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "06d32b0b43176eeb46f6d14a39ec01a4231752b0", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.4c99367.tgz", "fileCount": 5, "integrity": "sha512-Mpd/70Y4SO3ecL0IUei82i2mclSSuFhfaQ76ble2WOZvIf2qlBs6/ZzctcqQQk6NtoZf+aafP/k8Fvk5lK6mog==", "signatures": [{"sig": "MEQCIHlvk+KnkNNXQ12ZiyrzXyOE1q1pEj73/MpYFnrvjBoxAiBOpReHczjro9WlERfCQSeBRH5XxDG/em2sWDLThkSBbA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.4c99367", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9398}}, "0.0.0-insiders.3f434a6": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.3f434a6", "dependencies": {"tailwindcss": "0.0.0-insiders.3f434a6", "@tailwindcss/node": "0.0.0-insiders.3f434a6", "@tailwindcss/oxide": "0.0.0-insiders.3f434a6"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "e06d68b32cf9cb6d4e55d80b6c4293a8d8874b83", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.3f434a6.tgz", "fileCount": 5, "integrity": "sha512-oShSV+O1tCQJ6bSoNDALRSVntjKzjzVYetTMANvf/OyUdrpSX5Hu68UM/fdgW3NApPeFMfchKk8kuY7r6ESXLA==", "signatures": [{"sig": "MEUCIQCp245ghuu6lO2xpXx5kCjWCYB9rOaekllcdUu9+luE8gIgWENcxhWXTZD5l1EF/s7FFvDZGwo7ZdYKzK6bevkBPXw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.3f434a6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9411}}, "4.1.2": {"name": "@tailwindcss/vite", "version": "4.1.2", "dependencies": {"tailwindcss": "4.1.2", "@tailwindcss/node": "4.1.2", "@tailwindcss/oxide": "4.1.2"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "85c4bb2dd9857b734e55f551af166c312f499299", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.1.2.tgz", "fileCount": 5, "integrity": "sha512-3r/ZdMW0gxY8uOx1To0lpYa4coq4CzINcCX4laM1rS340Kcn0ac4A/MMFfHN8qba51aorZMYwMcOxYk4wJ9FYg==", "signatures": [{"sig": "MEUCIQCfXfswLtIw3xBU1nIrtAd5P4So/AGQ4AzN9av8DmgavQIgfn0Zs5RgP18Y9M+ArMPuFlKyCQVWXQJmTo9XeGsvRQc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.1.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9343}}, "0.0.0-insiders.fc94ab4": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.fc94ab4", "dependencies": {"tailwindcss": "0.0.0-insiders.fc94ab4", "@tailwindcss/node": "0.0.0-insiders.fc94ab4", "@tailwindcss/oxide": "0.0.0-insiders.fc94ab4"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "15c304031abf91d88ad5d4248bd094eaa828624b", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.fc94ab4.tgz", "fileCount": 5, "integrity": "sha512-koS5OSt8KkwF8FVeRw+DK+dgywtZSE0TNxlm1vgrYeqkuApVvBWmVVfhz7fboD1z4M55fLixm4XEieloAoBdhQ==", "signatures": [{"sig": "MEQCIGKF7TxnxC7mXdhvTC6cV3sLKl7+DiPaoMQ9p4X8j30AAiAlEvmOPyxlvj2WMRowQyLitS6nOhIJF+F1EC9T7uOtMA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.fc94ab4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9411}}, "0.0.0-insiders.57e55a6": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.57e55a6", "dependencies": {"tailwindcss": "0.0.0-insiders.57e55a6", "@tailwindcss/node": "0.0.0-insiders.57e55a6", "@tailwindcss/oxide": "0.0.0-insiders.57e55a6"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "183df4c0578e73d6121b7714e1a1a4612fada800", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.57e55a6.tgz", "fileCount": 5, "integrity": "sha512-Nev2OWIBKGDoNxQsks3M06iQv0jk9Jjb1x0G4MlBLTmDlmmFZ2EkqYAZLI5T98zB9fkOY2wjYqOw0wQ5q6Z/Ig==", "signatures": [{"sig": "MEYCIQCvH2uGJ3hShjhTdiMlh3Ubhl3MUAx85eb0ZMNY9EKU7AIhAMt/r/xyuIhj1qTFNx1HXp3CAIJayG6cK7E1DlEsa7R7", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.57e55a6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9411}}, "0.0.0-insiders.2fd7c8d": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.2fd7c8d", "dependencies": {"tailwindcss": "0.0.0-insiders.2fd7c8d", "@tailwindcss/node": "0.0.0-insiders.2fd7c8d", "@tailwindcss/oxide": "0.0.0-insiders.2fd7c8d"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "278692ed16e9889b93a30a350e54fda1ba377d4c", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.2fd7c8d.tgz", "fileCount": 5, "integrity": "sha512-bWGKbLokzOviX0sLNBpOYgpdgxK2YINv0uIP4cQWqzYYNzcI9302dWH1Zf60wlfiSxwZwCh6TZM0yBLEu6j44A==", "signatures": [{"sig": "MEQCIDqd7Vc/Bp9O2pPc6s9RxWc2lkTGy5iUO6cflVnK1UUbAiB1/o5F1x3AMo69/wb5Ct5bQPdo8RKo8tXvqsw4zFdmeQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.2fd7c8d", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9411}}, "0.0.0-insiders.e085977": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.e085977", "dependencies": {"tailwindcss": "0.0.0-insiders.e085977", "@tailwindcss/node": "0.0.0-insiders.e085977", "@tailwindcss/oxide": "0.0.0-insiders.e085977"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "069722f7f0d8c9e71d21ee9c245b820eb7db7c80", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.e085977.tgz", "fileCount": 5, "integrity": "sha512-HShKBqdujl1yavbHekx8JQCo3WtfrTwe9HKucMEB+EGfsOe43M9im/2kFJXcmob5nxSK3hhq6xCXldeVjNXx6Q==", "signatures": [{"sig": "MEUCIQCsZLOloOljyZXq2yn24JjncfErv2pu/SkUfDyaJ2gN0wIgRtJ76toXptSY4AokKWt2tVUDbH+YqX+OqkZ59ZpvW3U=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.e085977", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9411}}, "0.0.0-insiders.7d31725": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.7d31725", "dependencies": {"tailwindcss": "0.0.0-insiders.7d31725", "@tailwindcss/node": "0.0.0-insiders.7d31725", "@tailwindcss/oxide": "0.0.0-insiders.7d31725"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "69b91167cb7925f4e3ce853cc490f5f3dcd530b5", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.7d31725.tgz", "fileCount": 5, "integrity": "sha512-b<PERSON>ripZ32FA1aVKFLQlOyOf6+Rhdhgndc1ro+sQVoxxEL/VXJFC+PCGUyP7sM3BTedZis7OVM/QPq1YmrUQKtNQ==", "signatures": [{"sig": "MEQCIE+2feBKV9EGP/Try2//Lu+46+c/zdkZ9tldcFSCaIhZAiAjlBh7DsTyYCcW3fk0MwBhWl49oyae/rDuWzPeVFzprg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.7d31725", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9411}}, "0.0.0-insiders.5a77c9d": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.5a77c9d", "dependencies": {"tailwindcss": "0.0.0-insiders.5a77c9d", "@tailwindcss/node": "0.0.0-insiders.5a77c9d", "@tailwindcss/oxide": "0.0.0-insiders.5a77c9d"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "03a5841a3e025ba575fff6d1ce672ba5997952ae", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.5a77c9d.tgz", "fileCount": 5, "integrity": "sha512-SL/oq33cmQgYR56azF5L+QwYdB168goxYbzUOKFi58NjP0oL+T/YsBeWK0msk+ek7MO5HbDI4xJjqfLKPsAd8Q==", "signatures": [{"sig": "MEYCIQC+E4Drdgkg/FI2AM4YHw70eNgPW2dlmrR1mOtGvKVLLQIhAM1Y2jY6iZRYxVQNLnJq7VsapbIUZrspUDz9l4HU+Jn1", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.5a77c9d", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9411}}, "4.1.3": {"name": "@tailwindcss/vite", "version": "4.1.3", "dependencies": {"tailwindcss": "4.1.3", "@tailwindcss/node": "4.1.3", "@tailwindcss/oxide": "4.1.3"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "db28184d7a1d8b9f7352c61a0a7e73fa76e13129", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.1.3.tgz", "fileCount": 5, "integrity": "sha512-lUI/QaDxLtlV52Lho6pu07CG9pSnRYLOPmKGIQjyHdTBagemc6HmgZxyjGAQ/5HMPrNeWBfTVIpQl0/jLXvWHQ==", "signatures": [{"sig": "MEUCIQDV9JmmvNvMoPW1XCdHCu6PQb6aqZhqRLQx2oqAlwBwngIgdD2KERnBrs9P8fbIp7yaOqJwsbj9S0x3MBShnRPcogI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.1.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9343}}, "0.0.0-insiders.811e97d": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.811e97d", "dependencies": {"tailwindcss": "0.0.0-insiders.811e97d", "@tailwindcss/node": "0.0.0-insiders.811e97d", "@tailwindcss/oxide": "0.0.0-insiders.811e97d"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "b065876ae382b718c7bdbd5a2d80b763dfb8e70b", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.811e97d.tgz", "fileCount": 5, "integrity": "sha512-3jwNS4YRU5S15T4LyeXIMatmxVcZbjcpF6uhMmbN9DEsozMP7DThQSqJGH/iEUZS4vadAfpWdMvTc0v9w2F8tQ==", "signatures": [{"sig": "MEUCIDojbH16Myy5qCASmfuOFbaSLY1g1hGDSEEIAaYv2zlmAiEAo43oav21IPIaBb/XbqiSlGUIw3LRi2e7cWwqyc5uGvk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.811e97d", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9411}}, "0.0.0-insiders.3e9cf87": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.3e9cf87", "dependencies": {"tailwindcss": "0.0.0-insiders.3e9cf87", "@tailwindcss/node": "0.0.0-insiders.3e9cf87", "@tailwindcss/oxide": "0.0.0-insiders.3e9cf87"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "13eb5a5d23713d44be50b71daa4bf373948721d6", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.3e9cf87.tgz", "fileCount": 5, "integrity": "sha512-8e9ofreOBS6CAw9oIssvZLLZiN7W/Cc+U20pkz4ZZkW0f2mWWbEF/ZiaGIVbqA9wVdQN2xLr9X0Ixu+HdX483A==", "signatures": [{"sig": "MEQCIG9QTLNYIPKY9Mp+FSeLZ57Rk9RJ9zK4cz0DrCWOZu4OAiAf/eDGbVI8R8y+AZt3F+w0b+PB24npaCHK/kL4Adv0Lg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.3e9cf87", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9411}}, "0.0.0-insiders.76e18e6": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.76e18e6", "dependencies": {"tailwindcss": "0.0.0-insiders.76e18e6", "@tailwindcss/node": "0.0.0-insiders.76e18e6", "@tailwindcss/oxide": "0.0.0-insiders.76e18e6"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "c0cf9b17ab1a26bcdea313d13f0e0f2ca8abc307", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.76e18e6.tgz", "fileCount": 5, "integrity": "sha512-yPiq6zo1sYVPVaTmZVfWtdnB4JeCZr+Wox200Mg7H6xS35nX2NLCzoa71Owx3cnqa97W9Sa4klplLbVg1K/A6Q==", "signatures": [{"sig": "MEUCIENUm30gmAm3LTgAGk6MEYOgVMmxnA2fjeJHHE93Qti/AiEAiA6EI9aJb4cyaxDNQa+WatRLBcFMt0Gu1ZdPG/pAuF8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.76e18e6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9411}}, "0.0.0-insiders.f66d287": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.f66d287", "dependencies": {"tailwindcss": "0.0.0-insiders.f66d287", "@tailwindcss/node": "0.0.0-insiders.f66d287", "@tailwindcss/oxide": "0.0.0-insiders.f66d287"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "81d921d0fcddcbdaeca73334583a03f700248e6b", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.f66d287.tgz", "fileCount": 5, "integrity": "sha512-UJxOzRdh2anbyNqKbea3rGSShkaFMDGCqOgTsMEUeoGpSoN2BBbuQADKvAj3Alfl8y89AU0eveCJIy4GWHR00Q==", "signatures": [{"sig": "MEUCIQD5+RR7aqzMIUO5M3RtFtv9xkuiS5Z7iCHLQ0Riz5L/kgIgLokkKhzJlHspD/MGdeloSj2PeE5Zyd/XpkgbDYEp5X0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.f66d287", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9411}}, "0.0.0-insiders.cdecb55": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.cdecb55", "dependencies": {"tailwindcss": "0.0.0-insiders.cdecb55", "@tailwindcss/node": "0.0.0-insiders.cdecb55", "@tailwindcss/oxide": "0.0.0-insiders.cdecb55"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "8b2d8adf25c467d051296be3c1b29c81e2b87540", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.cdecb55.tgz", "fileCount": 5, "integrity": "sha512-5ujhuE<PERSON>+tRqHRXSS57DjxuPZlMjDgdgI7cr1gycI6X/bq3BNz4/HAdK8dkDIdq+L/88jj+XGYynKEgIESCZx6w==", "signatures": [{"sig": "MEUCIQD5sMouolyPMWyOlGXwerptQIjxM1nCFSmrT9bYdomoDAIgH8d0Q/hamZTmjWFJZxBfTcJCSyL8VEL2XkrR6uOzzvc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.cdecb55", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9411}}, "0.0.0-insiders.6e1f533": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.6e1f533", "dependencies": {"tailwindcss": "0.0.0-insiders.6e1f533", "@tailwindcss/node": "0.0.0-insiders.6e1f533", "@tailwindcss/oxide": "0.0.0-insiders.6e1f533"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "5264c7aa01a8106905acf5e3357fc59d57d667b8", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.6e1f533.tgz", "fileCount": 5, "integrity": "sha512-6X+pa2MGCu6HjHkv15aZgVJGamJVZ4AoWNzFgDH1w/jmhb+5aH/o8bh0vjuDf2IEA/7vJs8h1PBeYHpXHHFj2g==", "signatures": [{"sig": "MEQCIHOMeZmdfiS+gkE6dBxpITsN1k/y5AtQhyuZud0VHHnpAiBZayZaBFquL/SNArmHAMhJxrXuhPl1VYN5/St4v5suRA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.6e1f533", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9411}}, "0.0.0-insiders.25539e3": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.25539e3", "dependencies": {"tailwindcss": "0.0.0-insiders.25539e3", "@tailwindcss/node": "0.0.0-insiders.25539e3", "@tailwindcss/oxide": "0.0.0-insiders.25539e3"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "ceeeabf2186310f641fdc97e1feb075cac6d8886", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.25539e3.tgz", "fileCount": 5, "integrity": "sha512-PfBlg70kIYzBfKHx0Hff2HpKr44Rr2I5FNJivcWx8QCOXZZFYc/dzUiRsCRftfOokAUqllEY9NXbKm0PfXWOpQ==", "signatures": [{"sig": "MEYCIQDTP1bSdbXfr+9Y6fIeJTUYgM+cYBkcwuQCg60UqRq2QAIhALkthevf+25kdDoRpyc7pGWL9c0DH/NZsU+pQufbGs0N", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.25539e3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9411}}, "4.1.4": {"name": "@tailwindcss/vite", "version": "4.1.4", "dependencies": {"tailwindcss": "4.1.4", "@tailwindcss/node": "4.1.4", "@tailwindcss/oxide": "4.1.4"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "4ae66008e3f69499b7a951ba42aa4bc3cb2f7cd0", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.1.4.tgz", "fileCount": 5, "integrity": "sha512-4UQeMrONbvrsXKXXp/uxmdEN5JIJ9RkH7YVzs6AMxC/KC1+Np7WZBaNIco7TEjlkthqxZbt8pU/ipD+hKjm80A==", "signatures": [{"sig": "MEYCIQDvbgYnuDAxY0ksrovWrejf7tnYkMW5Hapuk9clzXusgAIhALhjZ1stQyaEo4YBQ+ktz6cpT6vWL4bkgBlB6e7LUk0q", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.1.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9343}}, "0.0.0-insiders.adcf1de": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.adcf1de", "dependencies": {"tailwindcss": "0.0.0-insiders.adcf1de", "@tailwindcss/node": "0.0.0-insiders.adcf1de", "@tailwindcss/oxide": "0.0.0-insiders.adcf1de"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "4a81248dc0e134e114b301692c87a21e13248112", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.adcf1de.tgz", "fileCount": 5, "integrity": "sha512-j2lBDxCR2lyEE/dhzdUbHGpNZjp3EFUmjgAaWQaNjeJZN8mEnRFAymkp1POxEogsHmn9JZu4rK7MWQ1zUogRuQ==", "signatures": [{"sig": "MEUCIDSU6V5tZ+SKu4sOVasVT8y3+E/k/93qp3TTb3lTXgAdAiEAid4kuxELq7c903fCEKfcw41n0xdnGpGwnjKBYAjIr9g=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.adcf1de", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9411}}, "0.0.0-insiders.8feb6a7": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.8feb6a7", "dependencies": {"tailwindcss": "0.0.0-insiders.8feb6a7", "@tailwindcss/node": "0.0.0-insiders.8feb6a7", "@tailwindcss/oxide": "0.0.0-insiders.8feb6a7"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "32e3ec8ea82ca00dc9c67fa6f0ba2d89d8eeeda9", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.8feb6a7.tgz", "fileCount": 5, "integrity": "sha512-Ln2e3dnLaXYraGjQ2rFXY31T+73IONh7/x0xSx7blHYZr5VgriFAQyu8SWiap11aPqscR//1Y/p5RunKNBksdg==", "signatures": [{"sig": "MEUCIQCBnPl4IZHmd/0biUSwonxUxRQmFOnxKWnL1NCqGVui3gIgG1I3lkqOsKAeTG5nmAlQCr1QK+Vb3JRUP8MqdfmnKNc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.8feb6a7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9411}}, "0.0.0-insiders.fc4afc2": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.fc4afc2", "dependencies": {"tailwindcss": "0.0.0-insiders.fc4afc2", "@tailwindcss/node": "0.0.0-insiders.fc4afc2", "@tailwindcss/oxide": "0.0.0-insiders.fc4afc2"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "fca2eda50de0271e92f5f29415d4c3b231ee9844", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.fc4afc2.tgz", "fileCount": 5, "integrity": "sha512-hNPN5xYfARMfOk2LxbrMYrg1LhApVywp7yYS3TjGOnWmdy5h3UtFhZVkNbl7n1yTQzL652ocK9gzgcf5sRBgrg==", "signatures": [{"sig": "MEYCIQCL3kH8PeAGkVwdxmxDkuuN/w7gVHwYnVCU1029WUXhgAIhAJuZD9L/a1Qw+c4FNIw+0vvvOEARFy9BeDCLT5t+vP3+", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.fc4afc2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9411}}, "0.0.0-insiders.650558d": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.650558d", "dependencies": {"tailwindcss": "0.0.0-insiders.650558d", "@tailwindcss/node": "0.0.0-insiders.650558d", "@tailwindcss/oxide": "0.0.0-insiders.650558d"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "f34b2648171c9cca69bec91e77183bfa2d97a355", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.650558d.tgz", "fileCount": 5, "integrity": "sha512-sw1+ASf4We4RyUKjJreArnA+nWOSYAnn94B3ZUUBhURRFnP4lJHLxMgNeWkSDf+nxQje13nHOYoaXZrdI1km7A==", "signatures": [{"sig": "MEQCIHnBcD+lrd3KmQSdkiecaP3oMGTB1Y+C4qMT6XENTOe5AiAPBE2BVokfxnjicuE2zV+v0qOrMtbLZInsu7KFEKbX3Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.650558d", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9411}}, "0.0.0-insiders.ee0d752": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.ee0d752", "dependencies": {"tailwindcss": "0.0.0-insiders.ee0d752", "@tailwindcss/node": "0.0.0-insiders.ee0d752", "@tailwindcss/oxide": "0.0.0-insiders.ee0d752"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "f7de748eae21cab2d8d4ffe88e09977785ea1591", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.ee0d752.tgz", "fileCount": 5, "integrity": "sha512-T3OSF8tXmBxYEw0zb/Ge0cL6aoj1dWSowLY8pIwI37FO2RCmalMwfUxVpjNwKGwtHra2GLADsfp590kvKjO0vg==", "signatures": [{"sig": "MEQCIGCG9n/iNV9WEf+uqafb7a84Y+Q2pLhomtN8HMgWE/WIAiAekDdYY3JDKuu3o51tz/Kk2p0E0kBtk6nHjdsm8l6SCQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.ee0d752", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9411}}, "0.0.0-insiders.8bf06ab": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.8bf06ab", "dependencies": {"tailwindcss": "0.0.0-insiders.8bf06ab", "@tailwindcss/node": "0.0.0-insiders.8bf06ab", "@tailwindcss/oxide": "0.0.0-insiders.8bf06ab"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "4a9ad8ac5c98811a36f091041449a9cd772a860e", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.8bf06ab.tgz", "fileCount": 5, "integrity": "sha512-X9uEw4VPd39GRlBBHpuUAxF5RCFKHYA4PjWS/+9Auz3nYkKbNoOku+53cncqrXN3CIItL5iAvWagPCguXssieg==", "signatures": [{"sig": "MEQCIGgXkN9CdP5jZ3mnqZLh7DxklxF9y2aao5Es95eFeNDKAiAAy6f2jG21T0UNxg0ATannKAloFpr2x7RhX/FlVofQ5g==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.8bf06ab", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9411}}, "0.0.0-insiders.25ec6a3": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.25ec6a3", "dependencies": {"tailwindcss": "0.0.0-insiders.25ec6a3", "@tailwindcss/node": "0.0.0-insiders.25ec6a3", "@tailwindcss/oxide": "0.0.0-insiders.25ec6a3"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "e5080768ae3844cd9863a7ce2d6ea17071350db0", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.25ec6a3.tgz", "fileCount": 5, "integrity": "sha512-cBxpdc5NvGgn4BdEYXxy0aldyESlCiTos8vQhy2aMqBwvxd8noKUJniQA2OX88Pm9N7JJR8T1eCG9GW21FzLmQ==", "signatures": [{"sig": "MEYCIQCtf6Mk+X6KwykiPzvaya3aX+t7JJZS8QMLAsDpDF6dEgIhAN4KG4RACdNvis496BRoBkSgFiART6aEU4s9HieSRoVL", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.25ec6a3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9411}}, "0.0.0-insiders.8e826b1": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.8e826b1", "dependencies": {"tailwindcss": "0.0.0-insiders.8e826b1", "@tailwindcss/node": "0.0.0-insiders.8e826b1", "@tailwindcss/oxide": "0.0.0-insiders.8e826b1"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "61b9463290efed8b0ca854fd73c1f22bb8a1ce81", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.8e826b1.tgz", "fileCount": 5, "integrity": "sha512-dAxtkqNDsaBMdwnm/v0LimlNgZNr/uffS2sXKOvozgKiOrUBflOWnCufkgJbhIE4wNkGaY7ko/FnPcMtMtQjAw==", "signatures": [{"sig": "MEYCIQD2y006Tn7fWEIiQ/AVz8G88BVzWyE0+5VNMk4u+d978AIhAObwSbjmqLdd/0e8dyhhHPg/D5j0JTkiWLlhdbC4dg60", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.8e826b1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9411}}, "0.0.0-insiders.a7f4a4d": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.a7f4a4d", "dependencies": {"tailwindcss": "0.0.0-insiders.a7f4a4d", "@tailwindcss/node": "0.0.0-insiders.a7f4a4d", "@tailwindcss/oxide": "0.0.0-insiders.a7f4a4d"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "dfebd085330d070fc495cc6cec56967bea3e5321", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.a7f4a4d.tgz", "fileCount": 5, "integrity": "sha512-Y3gZOV5HIEP/aXNRHmSSxyYSg/Ix4yJtmX2tmA0Kx9IbZpJTQ8u3zBgDgLVMHyGuL15jsY7UlwmOve1OgJqyag==", "signatures": [{"sig": "MEYCIQCfJiqOB5scmrGW1X0l/tXijJ7Nra1FLuJknVjw1bknJgIhAN57273cwXpvLSUgCN/cOYEwr5f1KBgD1r1fcl9mRD88", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.a7f4a4d", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9411}}, "0.0.0-insiders.46758f7": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.46758f7", "dependencies": {"tailwindcss": "0.0.0-insiders.46758f7", "@tailwindcss/node": "0.0.0-insiders.46758f7", "@tailwindcss/oxide": "0.0.0-insiders.46758f7"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "2586f7eebbceb7b7546247a92bd77f53a1fe8e7a", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.46758f7.tgz", "fileCount": 5, "integrity": "sha512-ApBWGdh9ogUGLvankIHlrQjOFrflWoIvsQ3BidF27mY814EMiNmqsVqA+c2NJqqd72MIl6Y9D+XWj0gaHTWVyg==", "signatures": [{"sig": "MEUCIQDJ/CwG/8234g5jgq1TGrtkp+PvQA5XSEuf2yQ2WpKkYgIgTd17l314+l6OmCbmZaHizHY+Z+aC7k117Qsa+LR5iHM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.46758f7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9411}}, "0.0.0-insiders.2bf2b4d": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.2bf2b4d", "dependencies": {"tailwindcss": "0.0.0-insiders.2bf2b4d", "@tailwindcss/node": "0.0.0-insiders.2bf2b4d", "@tailwindcss/oxide": "0.0.0-insiders.2bf2b4d"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "ec629a9f3dee31e67dd9d4e774649d8a578d1503", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.2bf2b4d.tgz", "fileCount": 5, "integrity": "sha512-XBF1hxIvTaQ+yC4/4F8dUdtCfkzsMdiXb6U0ZxfIrFe1IdYVOQEfRSdFRpQYJX8ijaYcYonrOz71jOSZr6lEcw==", "signatures": [{"sig": "MEUCIQDT5oTOUh+ZLJeQnaV3NXfDfQYx+F9Tx/934OzsQ8PJTgIgOXlH/P3HjqFKED0M4liYtRJtafRKfT1SZA4GnCIEdKA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.2bf2b4d", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9411}}, "0.0.0-insiders.d780a55": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.d780a55", "dependencies": {"tailwindcss": "0.0.0-insiders.d780a55", "@tailwindcss/node": "0.0.0-insiders.d780a55", "@tailwindcss/oxide": "0.0.0-insiders.d780a55"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "5054dccc1c7f5cc9911269736dafbe030961b497", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.d780a55.tgz", "fileCount": 5, "integrity": "sha512-Lmxhf5LXKFsSQJ8UB4Bnrq8MkcRJbuktKMw8M6fWjQzkqoCa6BERTP4HY1YtyOvoUHyOpTLx712b/O37nk/GXQ==", "signatures": [{"sig": "MEUCIAp3iykoneBxzX0t2uQZdppgGe4WR86sTDYf65gTvkzXAiEAr6N9lMSI3v8039nAVT1ffaTcYu8ClolY7C3JUfIS+po=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.d780a55", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9411}}, "0.0.0-insiders.231cddd": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.231cddd", "dependencies": {"tailwindcss": "0.0.0-insiders.231cddd", "@tailwindcss/node": "0.0.0-insiders.231cddd", "@tailwindcss/oxide": "0.0.0-insiders.231cddd"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "4e0cfb25fa12216daaa42e253519891818489e54", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.231cddd.tgz", "fileCount": 5, "integrity": "sha512-J2oJdSUueQoTXeMRj+AmXDDXZmJYMhumRWD9BwBv3MElW/H5Gs4Ga0oOMTP4NH79GARK9qVLrtFfsQTFp5LNKg==", "signatures": [{"sig": "MEQCIGvAySatNqPwCOQIHRnBaLqttK6dBqEKdGzUlqpOXoHoAiAxx6Gs18xx2hVbOF2QvIMjB3voWbu9dMUut39JTYlGFQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.231cddd", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9411}}, "0.0.0-insiders.52000a3": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.52000a3", "dependencies": {"tailwindcss": "0.0.0-insiders.52000a3", "@tailwindcss/node": "0.0.0-insiders.52000a3", "@tailwindcss/oxide": "0.0.0-insiders.52000a3"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "2f86d97b7f3fc0609a8f9d6966d45c031a4e0073", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.52000a3.tgz", "fileCount": 5, "integrity": "sha512-cWMDTQ9safpZ/bAVfhBP955Bh6vSDwUT+soJJudewnE4eL46U3s+CF4qwg8z4370P+tIs5AKGGAN8BYXY39LmQ==", "signatures": [{"sig": "MEUCIQCsA1rdZnhzDFVsq5KgdGV3rG7Re0bfxJOKMhm3DdkmwwIgIhtubeujkvz4pzAfSIQ4eVSMV0p6EnyMijLxDqSl0cU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.52000a3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9411}}, "0.0.0-insiders.62ca1ec": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.62ca1ec", "dependencies": {"tailwindcss": "0.0.0-insiders.62ca1ec", "@tailwindcss/node": "0.0.0-insiders.62ca1ec", "@tailwindcss/oxide": "0.0.0-insiders.62ca1ec"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "ea1922be210c47c62e613a390091e06decf2c898", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.62ca1ec.tgz", "fileCount": 5, "integrity": "sha512-5fkmcgTtemZir6Ljw1RrRkyncbNUwjjCz0DLkb6SKnzE7EiBcxj/JtPaA2uqBg7h3zBL3WOiYOxpZQoU6MhcpA==", "signatures": [{"sig": "MEQCIFtTxoOVrzotBvQL4oSJgNtY2OvhxAv32KAi1ylHll+PAiB91TKr57p9/tw45Jmi5aeKJoVQgj19or1tLpNiyN41cQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.62ca1ec", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9411}}, "0.0.0-insiders.ba10379": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.ba10379", "dependencies": {"tailwindcss": "0.0.0-insiders.ba10379", "@tailwindcss/node": "0.0.0-insiders.ba10379", "@tailwindcss/oxide": "0.0.0-insiders.ba10379"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "ce9abff05704970808ad7fab63e7df9499566dab", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.ba10379.tgz", "fileCount": 5, "integrity": "sha512-nb0GvPVyBl/i9cTYGdmXSDrvuck7BMaf9vhpDZdz6lJ5yGDoJ4uKfPjYuVXHEBiiHmOUFAv8wYQ3YNJ5OvUl5g==", "signatures": [{"sig": "MEUCIHcD/o8wCMNf4yi75flYzzoCZ9268JpfuHFRc9hB2mnoAiEA+vEI4pgQNsOzA75u3DWk8IrLbX1bMtDc+144+pU6iqA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.ba10379", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9411}}, "0.0.0-insiders.af1d4aa": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.af1d4aa", "dependencies": {"tailwindcss": "0.0.0-insiders.af1d4aa", "@tailwindcss/node": "0.0.0-insiders.af1d4aa", "@tailwindcss/oxide": "0.0.0-insiders.af1d4aa"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "bacc454604ac5067f22705108cf06bf85f47d961", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.af1d4aa.tgz", "fileCount": 5, "integrity": "sha512-oLc9mwKA8GxlrykDjYPrjwhxFGKn62fF51VcwKIxOcoUnaHhTEYPA+W7Sb9PgJ232g3zw6QTuOTw/E7C4C/tuA==", "signatures": [{"sig": "MEQCICxYApy/HHpg8Otg010ZCZ026jQsAbbzVqpNT587o5IoAiArT3ladgPmm6nr9JiS0Dnvs410ISYfEMhJgBHn0wHbkg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.af1d4aa", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9411}}, "0.0.0-insiders.3a1b27e": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.3a1b27e", "dependencies": {"tailwindcss": "0.0.0-insiders.3a1b27e", "@tailwindcss/node": "0.0.0-insiders.3a1b27e", "@tailwindcss/oxide": "0.0.0-insiders.3a1b27e"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "b1633122e46619872c82d2d97a80a6b0bd297d6b", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.3a1b27e.tgz", "fileCount": 5, "integrity": "sha512-BqFP9Ya2aRUkqcrtnf+6aSRTRZGmunTMY4YLLO1/Hlssk1QRbzTbZbT4GkGZXnALsANC5mw4G2I3tkkkPLFfOA==", "signatures": [{"sig": "MEYCIQD42UHhCoo9YGHDmpL6MTrN/Zle+EC7NWIiDaEyNSW0aAIhAJ4eeJgOBz8HFZ5QWIgceaDFYEVUYib1ZeRfsbkH2qUs", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.3a1b27e", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9411}}, "0.0.0-insiders.d2daf59": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.d2daf59", "dependencies": {"tailwindcss": "0.0.0-insiders.d2daf59", "@tailwindcss/node": "0.0.0-insiders.d2daf59", "@tailwindcss/oxide": "0.0.0-insiders.d2daf59"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "cdecbdc8f333e89031c4cc0fbd4d0d2f1fa0cd76", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.d2daf59.tgz", "fileCount": 5, "integrity": "sha512-jpH5udKMahpmPG3YFBI76DDYNNf2593MArBnwUuareSNgcJNlfPnt3v8QcnhNJ72Sn4RCkI8HtHmbJMh8VaKMQ==", "signatures": [{"sig": "MEUCIQCSnGD8yZarYioFNrwIwm2GJX0ZVAxJ3JtENqD7TYHtNAIgOZj3qUjX9LUPDDPwZ/289ea9vyfJ1WRsQxbg8duwGeo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.d2daf59", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9411}}, "0.0.0-insiders.9fec4ef": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.9fec4ef", "dependencies": {"tailwindcss": "0.0.0-insiders.9fec4ef", "@tailwindcss/node": "0.0.0-insiders.9fec4ef", "@tailwindcss/oxide": "0.0.0-insiders.9fec4ef"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "9cef37512c6f99b7607bd9fedf4ca1f03597b54c", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.9fec4ef.tgz", "fileCount": 5, "integrity": "sha512-CuqT11we3O4d3VkUteC88ho0HZdw9XnMb4WDKxfyX4JE6iFThIWmL53a9ccU9iQE9ZbP+R6T0Px5kgOcjHK1vg==", "signatures": [{"sig": "MEYCIQCuk6uXihTaCZMNoMoG56ZrJk1Wh9OHHUfXhAImvzW9HgIhAP/pPp4458EJ3IkTnibG79R9a/DajWFzv1xpP9Ye+78w", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.9fec4ef", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9411}}, "0.0.0-insiders.d3846a4": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.d3846a4", "dependencies": {"tailwindcss": "0.0.0-insiders.d3846a4", "@tailwindcss/node": "0.0.0-insiders.d3846a4", "@tailwindcss/oxide": "0.0.0-insiders.d3846a4"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "a4cd257b93ea1a4e7bf291b976a21a1cbd484e08", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.d3846a4.tgz", "fileCount": 5, "integrity": "sha512-9ei0uRK0uwQr53RnKIUxPVmnSOIIof4YthwSpXZeLw0McX+/KQkdE41BIcRKBfzWEd7XDsw+TM4n+J6cPgDfvg==", "signatures": [{"sig": "MEQCIE/ypBvnGyyqgLopfB5tftfB2hhQMIskfRilv+phBb0lAiAG4OwuZagjZ2gcD31SJzL9fEAAJDK2ojCIWnPGWTF3SQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.d3846a4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9411}}, "0.0.0-insiders.dbc8023": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.dbc8023", "dependencies": {"tailwindcss": "0.0.0-insiders.dbc8023", "@tailwindcss/node": "0.0.0-insiders.dbc8023", "@tailwindcss/oxide": "0.0.0-insiders.dbc8023"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "c043e15a4b85979425233f36e15c7ad81fedb0c1", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.dbc8023.tgz", "fileCount": 5, "integrity": "sha512-r3T041CNgsZgoPYc9PVE6ll2KdhvlRPDwRNWvRoLJZFW+MbGl13Jp5FS4mvkTC2LKFoXhyPPiZlwrwUEkASYSw==", "signatures": [{"sig": "MEUCIA6bYyn1vezLLRHxnWHbX+4e5iBJ5WVVC/Jvx1U0ZQHYAiEAgfCChChNoW54gHcMbOIOqj6cT7OVfmyOHVD44Q90AT0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.dbc8023", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9411}}, "0.0.0-insiders.ab4eb18": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.ab4eb18", "dependencies": {"tailwindcss": "0.0.0-insiders.ab4eb18", "@tailwindcss/node": "0.0.0-insiders.ab4eb18", "@tailwindcss/oxide": "0.0.0-insiders.ab4eb18"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "77fafba0fe0b7255f381fbbe98f58d9880345972", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.ab4eb18.tgz", "fileCount": 5, "integrity": "sha512-syvKzPwd1LjJyND5pDVOQf6as8qwdCP+c/y+nfqPaQvVYdC0FwtCARBUgoJvkc1iNGM+mgxzoK+Zyh1zgB1i1A==", "signatures": [{"sig": "MEYCIQC+OJpKrUImJd70PrfDtFnT3VwSbIO4zTmDAhJx9IxvWgIhANoqCZwS6R7JZ8SmHU5qbtPddIhN/mcwcSKOGLy99EIX", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.ab4eb18", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9411}}, "0.0.0-insiders.a636933": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.a636933", "dependencies": {"tailwindcss": "0.0.0-insiders.a636933", "@tailwindcss/node": "0.0.0-insiders.a636933", "@tailwindcss/oxide": "0.0.0-insiders.a636933"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "fd253b9a92437a35528b400319b724eaa92fd458", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.a636933.tgz", "fileCount": 5, "integrity": "sha512-px9XRXoBifgFJvERW7XSH7FdmxO9EKiZM+sFTIx2KOe6hHxv77gIYqt6LBcs4skPrYC/XR5MWxma5ucGng8mmg==", "signatures": [{"sig": "MEYCIQCCs4loIKcxfnNML3RbGlhE7aZgfq/KRjUoOqE2C4r3VgIhAJdkjM43BYCIGiqiUDK+wvOGSIxeYIP4IuP5B0A77okF", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.a636933", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9411}}, "0.0.0-insiders.45cd32e": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.45cd32e", "dependencies": {"tailwindcss": "0.0.0-insiders.45cd32e", "@tailwindcss/node": "0.0.0-insiders.45cd32e", "@tailwindcss/oxide": "0.0.0-insiders.45cd32e"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "8dd8d913c3f27438e638613b92f2d3db7eb7adae", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.45cd32e.tgz", "fileCount": 5, "integrity": "sha512-3UKNeM/leaY0UWkMc5A0z0fGJw2CO7AYFMfS4qKWygFWjwfJV8ma2Aia2CaLfE9JJSRfvRUL+S1zmAp2631DQw==", "signatures": [{"sig": "MEYCIQCGfzRma5w26ic5u42iol24vd67FNpkL09HuUvQ6jUJ2AIhAOAOdufGnCcuaMXA13WYsPCdLiRQ9hKiaNdXBvUGg6yI", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.45cd32e", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9411}}, "4.1.5": {"name": "@tailwindcss/vite", "version": "4.1.5", "dependencies": {"tailwindcss": "4.1.5", "@tailwindcss/node": "4.1.5", "@tailwindcss/oxide": "4.1.5"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "981e54fb8e664216d090524b40138595e8d236ca", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.1.5.tgz", "fileCount": 5, "integrity": "sha512-FE1stRoqdHSb7RxesMfCXE8icwI1W6zGE/512ae3ZDrpkQYTTYeSyUJPRCjZd8CwVAhpDUbi1YR8pcZioFJQ/w==", "signatures": [{"sig": "MEUCIQCeTh/V6SfTzKjv5+JPgnB/eT/r2ro05e2Z6Xxjz+T7LgIgN1XDkGbB/4gsPwSuDcZk07zsIGoSBJscR90s+E5VBq8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.1.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9343}}, "0.0.0-insiders.4e42756": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.4e42756", "dependencies": {"tailwindcss": "0.0.0-insiders.4e42756", "@tailwindcss/node": "0.0.0-insiders.4e42756", "@tailwindcss/oxide": "0.0.0-insiders.4e42756"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "b873ac39257aba202f01a209077c6ede1bc52055", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.4e42756.tgz", "fileCount": 5, "integrity": "sha512-iezMAURt/1m0uk5+e1hNXhO/ora8heDvjCm2hcWKA7Kr8l8CPPyfqVYwD9Zi3jIjYcjzENNZydozCeFxE96Arw==", "signatures": [{"sig": "MEUCIQDhGf2DIvHW/7ynb+lF+LjidfMTEXzqBm39muvy1LsdwQIgXbfDDzXNgpNzJYIxzkjoIk/2smlU6TWJVneYBEEfk6c=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.4e42756", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9411}}, "0.0.0-insiders.c095071": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.c095071", "dependencies": {"tailwindcss": "0.0.0-insiders.c095071", "@tailwindcss/node": "0.0.0-insiders.c095071", "@tailwindcss/oxide": "0.0.0-insiders.c095071"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "ffa66fb271e0d95d06ee7c5eebd3945e46290447", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.c095071.tgz", "fileCount": 5, "integrity": "sha512-0Vt0dOJxW1mWJurhC6k70TO4Y6qZZQcqvLYY3bbqT+RsmP2yYymQmSNiJaX3bPM4aiqmuTDZgV4DTLPnRkJHWQ==", "signatures": [{"sig": "MEYCIQCbl28n8chmjJSqV54fJdfHkC5Y/EdYhrd8c69xKmTUIAIhAKAkdgaexSFhgnksP1tLjW8Q0lUtPNqIiuK8e3yJB44h", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.c095071", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9411}}, "0.0.0-insiders.dd5ec49": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.dd5ec49", "dependencies": {"tailwindcss": "0.0.0-insiders.dd5ec49", "@tailwindcss/node": "0.0.0-insiders.dd5ec49", "@tailwindcss/oxide": "0.0.0-insiders.dd5ec49"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "a7897319292537dff3283d76de92f5254bcc760e", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.dd5ec49.tgz", "fileCount": 5, "integrity": "sha512-1KcIqqw7s1jQdZMd+5WyTYWz/4ht04fgsjA1Yez3ZeCmIf0ODPn4iy2GkE+L8W/Pa4EAkUq1jE6H4ko/a4gVfg==", "signatures": [{"sig": "MEUCIBPYeXVUvKEkNU0EVEp9iiI7/gy6K24quOnYgugdfHvnAiEAvnRdiYPkEFGEz2BqkmIaKtk1ennXYgK6pVP6BmZhn/8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.dd5ec49", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9411}}, "0.0.0-insiders.e00d092": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.e00d092", "dependencies": {"tailwindcss": "0.0.0-insiders.e00d092", "@tailwindcss/node": "0.0.0-insiders.e00d092", "@tailwindcss/oxide": "0.0.0-insiders.e00d092"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "821754663d3424064968455ef8bb79ebe3fa38fe", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.e00d092.tgz", "fileCount": 5, "integrity": "sha512-CU3nxRVTfSkT9In6vQldSQtFpCf9xOwpORaYKg3ns8vfCQpWRjqiUFD1GEs66pW/8cyYsDove0DJ5LosHsJEpA==", "signatures": [{"sig": "MEUCIQCVlLhfwDi7fbvD/cQ/j5HfBXFZlGUyy4viCsbRPJERbwIgAeVx8wX9MaVon6kxNvDRWbHWT09NjtM8GF1p9J62kXA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.e00d092", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9411}}, "0.0.0-insiders.473f024": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.473f024", "dependencies": {"tailwindcss": "0.0.0-insiders.473f024", "@tailwindcss/node": "0.0.0-insiders.473f024", "@tailwindcss/oxide": "0.0.0-insiders.473f024"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "40919b90f05a358e65b5591286b9e8d6d7df0871", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.473f024.tgz", "fileCount": 5, "integrity": "sha512-T+ZkDPc1ccEocarK26pw4JjKAwFBXKVHXe2hoq0ImuTs6w+HHqWnR7AInIFnz2VwneGUi5EQm0Zwdu3Qlg2WDw==", "signatures": [{"sig": "MEUCIQCuzWtXovPDeJsvTxTh8OykcVcFCeXIktNY9uAuiZUeRwIgU49GcLOlTyERVHshQ3wSDP0WA4Em8hPeqPo2Zj8Dg/A=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.473f024", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9411}}, "0.0.0-insiders.d38554d": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.d38554d", "dependencies": {"tailwindcss": "0.0.0-insiders.d38554d", "@tailwindcss/node": "0.0.0-insiders.d38554d", "@tailwindcss/oxide": "0.0.0-insiders.d38554d"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "ebd5155dc0950b58532af3a48256edda2575fdf3", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.d38554d.tgz", "fileCount": 5, "integrity": "sha512-u8GDoV4xAqPcfZ7QiQPGGD2fifV0KZqEKRB61iNZKZc6KKTT2jopwypzcsLLp7WzN0c+VKzITebmtlv6h5cYgQ==", "signatures": [{"sig": "MEYCIQCMTmKxLRw8oxowDouxly6KlrfXoK5cXS81Pb0O/dJFwwIhAKiCKgMSI/qWYHWrlY5ietGp/4EdQxVXEO1fnbVqN+s/", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.d38554d", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9411}}, "0.0.0-insiders.6a1df6a": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.6a1df6a", "dependencies": {"tailwindcss": "0.0.0-insiders.6a1df6a", "@tailwindcss/node": "0.0.0-insiders.6a1df6a", "@tailwindcss/oxide": "0.0.0-insiders.6a1df6a"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "1d13df3f8b3ca6debb3bc5f6c1dd78ab3149c295", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.6a1df6a.tgz", "fileCount": 5, "integrity": "sha512-c/tcvNRbwREFmTvQqBB6CBLJROwU+i3RO/aAx8Rw1JGzcYDGXAduZlLPiK4JJrnaBLX2lJram0WIVe+cwxV1hA==", "signatures": [{"sig": "MEYCIQCineJA+uxWCDDR1194ZEIIIgLxkUbhEBnp5tsabC33XQIhAILfIIKYkFjaEVpxzxK8fiu9XshK9d4tqdPrns2XRYme", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.6a1df6a", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9411}}, "0.0.0-insiders.5c5ae04": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.5c5ae04", "dependencies": {"tailwindcss": "0.0.0-insiders.5c5ae04", "@tailwindcss/node": "0.0.0-insiders.5c5ae04", "@tailwindcss/oxide": "0.0.0-insiders.5c5ae04"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "d6bf18f0da658d558a0fe6454ed8c41d59cbbf27", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.5c5ae04.tgz", "fileCount": 5, "integrity": "sha512-r69lkdgZPPeFSVWRtjsGc2YIyMmwq+N+rfe3jWQYQ0DFAHNmV6S4PTvpgImSNKVhoa9QLr/44nraYKhSNcq5vw==", "signatures": [{"sig": "MEUCIGEob15+BlEH9f0+L0O41UKWYtRz5ZsD/L7kIM57ApOcAiEA4ZiCgU6pH2cgLM1e3O1Q381ZD4epwEz5EkimFqsvWEE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.5c5ae04", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9411}}, "0.0.0-insiders.ed45952": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.ed45952", "dependencies": {"tailwindcss": "0.0.0-insiders.ed45952", "@tailwindcss/node": "0.0.0-insiders.ed45952", "@tailwindcss/oxide": "0.0.0-insiders.ed45952"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "c83c81824a97aea45e5aa877ba94c2c19ed4eb1d", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.ed45952.tgz", "fileCount": 5, "integrity": "sha512-suAlxzF17bpWR9oTGplTK/HlylBametEmHwcCCnculclfOpY/AuaHup5ufkdLIC0hH13lxwqG335f+a1sgE+BA==", "signatures": [{"sig": "MEUCIQCFDvjWXeQxZKZSwE049IdSBD+OZ/a5NPjg4F5eRQ+7nAIgNSaI9VNybKJBCDcw2UwvvzjaUDFY8d+qa1Kaz0lK2to=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.ed45952", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9411}}, "0.0.0-insiders.4f8539c": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.4f8539c", "dependencies": {"tailwindcss": "0.0.0-insiders.4f8539c", "@tailwindcss/node": "0.0.0-insiders.4f8539c", "@tailwindcss/oxide": "0.0.0-insiders.4f8539c"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "40b852ce012355f004eead1f1a00a35eacb86bb4", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.4f8539c.tgz", "fileCount": 5, "integrity": "sha512-LVHeyPFo3CVxRVod7FLY2NE1nT36XvEgY8/c7mrxHgPw3QvGZe+UoftEESoRBxKmwr9bRc+fp0pKu9MI5u9JAQ==", "signatures": [{"sig": "MEUCIQCe2zeUwozba7zixP+OnQ28NxmObCsvttMKGX+i5jhH4gIgXoNZRYaa0iHEGaq0G4rLHX8EdRuFYM6kt3XBEvwneZ4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.4f8539c", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9411}}, "0.0.0-insiders.449dfcf": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.449dfcf", "dependencies": {"tailwindcss": "0.0.0-insiders.449dfcf", "@tailwindcss/node": "0.0.0-insiders.449dfcf", "@tailwindcss/oxide": "0.0.0-insiders.449dfcf"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "1af45d37cbd5da17d42e35d106e01c5505d86e5c", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.449dfcf.tgz", "fileCount": 5, "integrity": "sha512-KXbf+vU2YOI6PeExTTfLXu14iXXlyrqR2yHGuwyfZXWU9lpg44VyzNZr7no1t41tYB0+NTYAln/e2YZdjwiEPA==", "signatures": [{"sig": "MEQCIB6Xpdv4EanL4su95dfwQe5EInqNOQSzMjgOrqhLOuRdAiA3+CbQlgfUDB73l0Z+rsNxUrMrh7r6xNSP9g5Fm4tNeg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.449dfcf", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9411}}, "0.0.0-insiders.d8c4df8": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.d8c4df8", "dependencies": {"tailwindcss": "0.0.0-insiders.d8c4df8", "@tailwindcss/node": "0.0.0-insiders.d8c4df8", "@tailwindcss/oxide": "0.0.0-insiders.d8c4df8"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "9fcacca7b1f7fb73316f909ab7e65502d08ceedc", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.d8c4df8.tgz", "fileCount": 5, "integrity": "sha512-DuJ8d669JQExVmyHlYsPGBxpd25wxEKcJLoj+8PWHtKcFsK6K4hBMfkcR4W2EKYJ2xyYQVBCga2q3C6wo3oBhA==", "signatures": [{"sig": "MEQCIHcPRatu+8CngcsLcwiQ/AZTCksUpY1Idxj+UBFNeMLBAiAO422jYeoAXPNiuPlib+Fa/aa6F+Ogamx7scZYHAzYiw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.d8c4df8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9411}}, "0.0.0-insiders.179e5dd": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.179e5dd", "dependencies": {"tailwindcss": "0.0.0-insiders.179e5dd", "@tailwindcss/node": "0.0.0-insiders.179e5dd", "@tailwindcss/oxide": "0.0.0-insiders.179e5dd"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "ef55bd290a7a5cd0bc0bf3ce15627f549afdef83", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.179e5dd.tgz", "fileCount": 5, "integrity": "sha512-XeJM/PufrK84t4Dd60HTqHqUSzfprvx9YYFOToK43YmZxrOCnlbpm0glDU6DY/Vv3yrEWRnvuPKUCyW2tchNrA==", "signatures": [{"sig": "MEQCIHxMkyw14THDE3b+rY9FUVuxHUc5upGLyjUmX4zlkq8NAiBNZnN3w6pupP0yLc6If3z8E+r1kV+TuqOvd/iz+ZXz0g==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.179e5dd", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9411}}, "0.0.0-insiders.17ca56d": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.17ca56d", "dependencies": {"tailwindcss": "0.0.0-insiders.17ca56d", "@tailwindcss/node": "0.0.0-insiders.17ca56d", "@tailwindcss/oxide": "0.0.0-insiders.17ca56d"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "d6a3bd7686432f502c1f82c44fbe0a344048667d", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.17ca56d.tgz", "fileCount": 5, "integrity": "sha512-k089XYvTAT+5mxVrs/GVd+PnJGWTarcWXOqipchxuQXIGQgipbPPdScLPG3N/mUdq8u8mVmZOyv6LnD4Q26e5Q==", "signatures": [{"sig": "MEYCIQCGb8P9QiTTjWWSTfNsck+F7zpuYZwmvaUM29rtc+UJ0QIhAI0HEAN3iU/+UKn63pNN2QFVkR7zd4F+paUw6bhAfp/C", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.17ca56d", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9411}}, "0.0.0-insiders.62706dc": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.62706dc", "dependencies": {"tailwindcss": "0.0.0-insiders.62706dc", "@tailwindcss/node": "0.0.0-insiders.62706dc", "@tailwindcss/oxide": "0.0.0-insiders.62706dc"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "c7688f58c76ef4c8b0874faef33938105f8c3d81", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.62706dc.tgz", "fileCount": 5, "integrity": "sha512-P/Ax46SMbRtKXYKppBaecJLg5v+9s4z+pXTtLOk3xJbXXpw9T3GO3QlbVQb7XmJ9u0dbzE/V1RD8yPa6Zr3Znw==", "signatures": [{"sig": "MEQCIESRsJyGuHax3DvNHWFFLtdts1aIRSWRjGTB6610F2A5AiAZRNS7jCZcyrwahSEnf3KehBdYD7rrHIXFBS82SMPcfA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.62706dc", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9411}}, "0.0.0-insiders.56b22bb": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.56b22bb", "dependencies": {"tailwindcss": "0.0.0-insiders.56b22bb", "@tailwindcss/node": "0.0.0-insiders.56b22bb", "@tailwindcss/oxide": "0.0.0-insiders.56b22bb"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "89d47bf62052d33fee2491a3648545c198130cc4", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.56b22bb.tgz", "fileCount": 5, "integrity": "sha512-loSWjDocseMSdPFXhCXX16MYgKbRPGKRdnz6NhxvUWNC+Kje1Zf3HgJrCXnkqZ+S62Ud+mXEgeRQK5/PSZY3iw==", "signatures": [{"sig": "MEUCID/875O9UKnKV21nuhO6iyc1MIjxSByGOBdHVWaOk3rsAiEAmu4jS7+1gAL4fyb7lZASC14xp8TVcU3bWtFS7vJqUrg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.56b22bb", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9669}}, "0.0.0-insiders.ff9f183": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.ff9f183", "dependencies": {"tailwindcss": "0.0.0-insiders.ff9f183", "@tailwindcss/node": "0.0.0-insiders.ff9f183", "@tailwindcss/oxide": "0.0.0-insiders.ff9f183"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "cff4707f06fef902e4343f690e156001aa06d9d5", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.ff9f183.tgz", "fileCount": 5, "integrity": "sha512-qedtwW6tXGmu3bgA+TYT6aUpAmX/yYm8VlVEdQN/U56QrzwwAKCgC/QO5YeurWGjYGiLhYZgCYXAV7BA7YGlKA==", "signatures": [{"sig": "MEUCIHylQNDI9Afp2tWCDywT9ImutI++n5GcCddOZlnyzwUYAiEA8EoVdC75vKYCDGvgbugzG3QxC49tj3LlCwEnyCkKKgc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.ff9f183", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9669}}, "0.0.0-insiders.ae57d26": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.ae57d26", "dependencies": {"tailwindcss": "0.0.0-insiders.ae57d26", "@tailwindcss/node": "0.0.0-insiders.ae57d26", "@tailwindcss/oxide": "0.0.0-insiders.ae57d26"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "9f37e04713685c5b6a70d17a66bf999beb228be8", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.ae57d26.tgz", "fileCount": 5, "integrity": "sha512-2I705rbAmPVgNbZo7nlX9qLz7VMnPENJA8x0hE/uxhvHdaiJb61YBYCm7T4IzVbmYlKRcYA07iVTumXXdas42g==", "signatures": [{"sig": "MEQCIAajRz03P+pk9fUHTwH0mR0jXBIm79/dxE/RrFDqljAuAiAK+hXkU1ha/A13FQ+F4vS4NIUx0byr6r521ePAY/z2uw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.ae57d26", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9669}}, "0.0.0-insiders.2f6679a": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.2f6679a", "dependencies": {"tailwindcss": "0.0.0-insiders.2f6679a", "@tailwindcss/node": "0.0.0-insiders.2f6679a", "@tailwindcss/oxide": "0.0.0-insiders.2f6679a"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "02dfdd713623bb01fa0c45560ba817f9456914d9", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.2f6679a.tgz", "fileCount": 5, "integrity": "sha512-VpqO+mIh53QHA0pAJA3x3Jg46C9EQ23jbLLAp3B95SQkbLz99NZ2tw4I/WBmPiW3DhSfU56+PAzCR/YKdLncqw==", "signatures": [{"sig": "MEYCIQDY2VrH9YORK5UZFxSUBnO+GVdgdINIk3A/ehFBs+vHPQIhAM3fOjcV5Rn9VwA9fW7tCp/9PBF/Ckf6y9e5ouORLa18", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.2f6679a", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9669}}, "0.0.0-insiders.47bb007": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.47bb007", "dependencies": {"tailwindcss": "0.0.0-insiders.47bb007", "@tailwindcss/node": "0.0.0-insiders.47bb007", "@tailwindcss/oxide": "0.0.0-insiders.47bb007"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "3ace9d5087297ba353fe3d2d9cfd6aa0a20a5ee7", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.47bb007.tgz", "fileCount": 5, "integrity": "sha512-/NZH6llk4Tt83QkQLgY1QOuhFkg2lbdPvO6k/hixG8AJogQnHjTDcb1mQjf1kQHL9ucl3kJj35NOQyIk8JepaA==", "signatures": [{"sig": "MEUCIQCK/nrIFVHVyaxuxTQBhm0H1PhLupKzm1MGBBtT4gMR4AIgQa2elB+BpOjHKFQ6SBiQPX/O8AKDpR+KJtQL37pyor0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.47bb007", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9669}}, "0.0.0-insiders.2d13998": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.2d13998", "dependencies": {"tailwindcss": "0.0.0-insiders.2d13998", "@tailwindcss/node": "0.0.0-insiders.2d13998", "@tailwindcss/oxide": "0.0.0-insiders.2d13998"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "bfb1ff1bc86397f015eae7c47ef59303a3f73082", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.2d13998.tgz", "fileCount": 5, "integrity": "sha512-lupAaZk/BtiBnTgY8K1uA+BtPHjcM0TPIJ+F1i49ngLVkSaW2Kl6ds2kGhEh2sif7eCJkHmpeID3aceOhxvh9Q==", "signatures": [{"sig": "MEUCIQDOostTVLYd82PBbQ31WPPMO6Il/Zddt036p4wgpZHlFwIgGBpD353ccEdparX3NKSxMZDe0fzsNaceZkbS36QYTdY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.2d13998", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9669}}, "4.1.6": {"name": "@tailwindcss/vite", "version": "4.1.6", "dependencies": {"tailwindcss": "4.1.6", "@tailwindcss/node": "4.1.6", "@tailwindcss/oxide": "4.1.6"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "ef332f5cfc75c4628717d91a712dae0ef3b21b24", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.1.6.tgz", "fileCount": 5, "integrity": "sha512-zjtqjDeY1w3g2beYQtrMAf51n5G7o+UwmyOjtsDMP7t6XyoRMOidcoKP32ps7AkNOHIXEOK0bhIC05dj8oJp4w==", "signatures": [{"sig": "MEUCIQC22BTTtcHmEbDM6FqthkDiUzXw1PIEbgpCNSq63JtJIQIgY5jLcrP7PHcQ5yKWtIxeHhS7BEjXYxvF4McIFuPilrg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.1.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9601}}, "0.0.0-insiders.737994b": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.737994b", "dependencies": {"tailwindcss": "0.0.0-insiders.737994b", "@tailwindcss/node": "0.0.0-insiders.737994b", "@tailwindcss/oxide": "0.0.0-insiders.737994b"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "bc76da4ee87f0905e6ccf382cbcdab1d24196782", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.737994b.tgz", "fileCount": 5, "integrity": "sha512-04EgPTD5IO+5yKavJgVywmzlvekTyef8oRzWUpqVXr1b6OuHv+ViuDtC+9YIzorRUoHCE12iUJh70qWgaUlv2g==", "signatures": [{"sig": "MEUCIC4YWXKlqtnyourimDVQEHdCR31lzCrSXg5c72zya7YNAiEAiyHQH54S8+2mMfJXOAyl/yfeBV60/8j+pEivNbS8Yks=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.737994b", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9669}}, "0.0.0-insiders.3386049": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.3386049", "dependencies": {"tailwindcss": "0.0.0-insiders.3386049", "@tailwindcss/node": "0.0.0-insiders.3386049", "@tailwindcss/oxide": "0.0.0-insiders.3386049"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "12503de08d325deeccc819622a86d8c078445b7d", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.3386049.tgz", "fileCount": 5, "integrity": "sha512-p93BHk/azfrA7GBWpqvfwbazJq5FFQSAuRC6/gzN3J421zdcrlwnWQ7njWRD9wnxMRJwtkH7BoYxtftxbFEkRw==", "signatures": [{"sig": "MEUCIQD1iRqjbAzs+OwNKRYvRkJYlptrnxegZs86iZWDUKkZJQIgPfBLsvemQ2XoMA9yjXRg62OvJ8BhdBsrTrtxQhe4c4s=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.3386049", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9669}}, "0.0.0-insiders.f0986ce": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.f0986ce", "dependencies": {"tailwindcss": "0.0.0-insiders.f0986ce", "@tailwindcss/node": "0.0.0-insiders.f0986ce", "@tailwindcss/oxide": "0.0.0-insiders.f0986ce"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "0256ef6cec276efbef0fe37a6daf177db29b4f9c", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.f0986ce.tgz", "fileCount": 5, "integrity": "sha512-wMW8KNCvbo1gSn2XUPaPVhAwL1sLGLmVMLQkSuXjpuLo+RjUgiipPKUkcy45SrNXY+kcFGFxV3DJ6vnMTKv+zw==", "signatures": [{"sig": "MEYCIQCfqeMj8bwctosBgc+HT40Fzco05JSnYjHUnhVuvZRobgIhAIgfP7Mn+VVCxAY7FiFgQAXnWIDYOn9t7Cj4I+ghHd3B", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.f0986ce", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9669}}, "0.0.0-insiders.0d975f5": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.0d975f5", "dependencies": {"tailwindcss": "0.0.0-insiders.0d975f5", "@tailwindcss/node": "0.0.0-insiders.0d975f5", "@tailwindcss/oxide": "0.0.0-insiders.0d975f5"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "e55eb436211890b541e7e1ca19ed7cdea4e10375", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.0d975f5.tgz", "fileCount": 5, "integrity": "sha512-4wm291bsXE6PCsYcYgNNU13xKfiT0I1alzO0NscyyEqqdYOXmMNnCHxxE53o9nNItubNR+EiehaOy7EdkoVpMA==", "signatures": [{"sig": "MEQCIFiUPPUSa6jyxWczOh14hjn6oeNc23zU4NZw83AUGKGFAiB18Pbv817YSuJj31eILgvQAF9oWbJwTkNwWBfurcoBtQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.0d975f5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9669}}, "0.0.0-insiders.19e2b29": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.19e2b29", "dependencies": {"tailwindcss": "0.0.0-insiders.19e2b29", "@tailwindcss/node": "0.0.0-insiders.19e2b29", "@tailwindcss/oxide": "0.0.0-insiders.19e2b29"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "fad9906fd2cd44f39d2de233ed2e000776f1898f", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.19e2b29.tgz", "fileCount": 5, "integrity": "sha512-Vy7b/lJ+jZyfbVGQSHt8BUBpwZQ+meFtFpEzZTZ/offT79f7PFZyykdc8XOEbSMuunyRLmUAKaK5YM+eMoGqBw==", "signatures": [{"sig": "MEQCIHWhKucHk/CrMv4snfBdN4MQu76WO3Lnpdov+IqXZsJYAiAPf083HrOrUZyE2AuaouocvhRKaNwUdDZTpdW5LPZ5LA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.19e2b29", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9669}}, "0.0.0-insiders.5688f0a": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.5688f0a", "dependencies": {"tailwindcss": "0.0.0-insiders.5688f0a", "@tailwindcss/node": "0.0.0-insiders.5688f0a", "@tailwindcss/oxide": "0.0.0-insiders.5688f0a"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "537f1ed859c7a4ee35152d31842ff95b061bff73", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.5688f0a.tgz", "fileCount": 5, "integrity": "sha512-rxSoDCLdU/VDPk8GU1Vn0SJGvfFdMIRg/jLF7vePEvjjd0nVicIF68q9hdRcrZImjQSWW1DkNOqbmcXQSeSvXw==", "signatures": [{"sig": "MEUCIQD+Ek/4CSpbjzZM5Z7/f2G44KxlJIIc/1tGY3OJv8HpuAIgfTKVVZ4ftYebMLLT7EUBY/oSq6ZF6E1Dy8TcpW08ssk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.5688f0a", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9669}}, "0.0.0-insiders.ba944ca": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.ba944ca", "dependencies": {"tailwindcss": "0.0.0-insiders.ba944ca", "@tailwindcss/node": "0.0.0-insiders.ba944ca", "@tailwindcss/oxide": "0.0.0-insiders.ba944ca"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "613e7bd8fb11f75ebbeef97e6b6a2244534637ca", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.ba944ca.tgz", "fileCount": 5, "integrity": "sha512-fykXnDO4RC2l2YHlMOvIJFIjp+lErlYIMEyb8dbDOHkG8qxPzjAEtCAnY5uzGEKXD6DgwfuBjERyxPRU3hZs4w==", "signatures": [{"sig": "MEUCIQDY1OOhoPP/FrsslKnrSJPcxCiyRi5b9+rfTmZqVptm2QIgIVt2CzgqzT2JlfvcaAgF3ApwOmjPHHsaVmuQrHD0Rpw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.ba944ca", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9669}}, "0.0.0-insiders.4fba87b": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.4fba87b", "dependencies": {"tailwindcss": "0.0.0-insiders.4fba87b", "@tailwindcss/node": "0.0.0-insiders.4fba87b", "@tailwindcss/oxide": "0.0.0-insiders.4fba87b"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "efcf86043eab60d0b1f268405d342cb13b846794", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.4fba87b.tgz", "fileCount": 5, "integrity": "sha512-VZO5vUB/FPwBKG7k6cl2MpHeRl8UF1JXIZyxGTgUUbRtbK3TWZ8A7tq5p4YCj2Fruxt8evW5gnnOAAQ+tjtxdg==", "signatures": [{"sig": "MEQCIGNn7fObB2FXZueBGpYILZjS2Jof4unE/WvNmlkJHMqKAiBZTOP0hU9Z/q7NFBkgQkpnEz7zTcvSi6vucC1J20dOFQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.4fba87b", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9669}}, "0.0.0-insiders.ef2e6c7": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.ef2e6c7", "dependencies": {"tailwindcss": "0.0.0-insiders.ef2e6c7", "@tailwindcss/node": "0.0.0-insiders.ef2e6c7", "@tailwindcss/oxide": "0.0.0-insiders.ef2e6c7"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "870a2fda977653591036c10dd0ea786309c4b4f0", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.ef2e6c7.tgz", "fileCount": 5, "integrity": "sha512-ZjVELlSwAtedij87VNGnvMUF7KxGWYf0yPriZBPJ7BiCU20ZoAjfAEJdOObUBSPCLIGP0wCdgirDEg6J7XN6uQ==", "signatures": [{"sig": "MEUCIAFmRNNx49ToxbdtBlcoi9MijOrGNXlvHV7jFka+xXfgAiEAzXyrVK03tAWYSigwVCGWL+nO/50Fy+vO5seC/FYTPPs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.ef2e6c7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9669}}, "0.0.0-insiders.498f9ff": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.498f9ff", "dependencies": {"tailwindcss": "0.0.0-insiders.498f9ff", "@tailwindcss/node": "0.0.0-insiders.498f9ff", "@tailwindcss/oxide": "0.0.0-insiders.498f9ff"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "8f340c5b8e245a395400c9068f98ae8b129c3ce9", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.498f9ff.tgz", "fileCount": 5, "integrity": "sha512-Lxy9yVAwRv1RrlZaGQMf1PqRzH1YKvsUzG2rHXIB0fm9Zd7tjsEcQ5sylUrPyf1m/W6//YJEdgehvofijPWntQ==", "signatures": [{"sig": "MEYCIQCDjNcIM1wSRZ6caRvZ5CTE9VEWsXb4yNQv1GHEAqElZQIhAJ6jfk9ILH5YZRN22h7v8RyLBSKqlo+YVB3laJKlA20g", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.498f9ff", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9669}}, "0.0.0-insiders.4db711d": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.4db711d", "dependencies": {"tailwindcss": "0.0.0-insiders.4db711d", "@tailwindcss/node": "0.0.0-insiders.4db711d", "@tailwindcss/oxide": "0.0.0-insiders.4db711d"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "f162fcbb403eccaec3a9fe341ce4c94491564a49", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.4db711d.tgz", "fileCount": 5, "integrity": "sha512-gwNXZikZ2wOoepiNzoTKk3jIMGEBo4AYExJjjm9GH90jArw69lZ/x+/0wGXPL1Dth4AIY2ueEX3rA5xWn+clEA==", "signatures": [{"sig": "MEQCIDq8RNm9m/kU0zJFgCz2Kj6X3uCuLYCYZ4cSdEAvG4C1AiAex9ipTxCi74lqg34BZrEyZSOckrESKqt9nOWo9oapcg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.4db711d", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9669}}, "0.0.0-insiders.e57a2f5": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.e57a2f5", "dependencies": {"tailwindcss": "0.0.0-insiders.e57a2f5", "@tailwindcss/node": "0.0.0-insiders.e57a2f5", "@tailwindcss/oxide": "0.0.0-insiders.e57a2f5"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "e0f43d27ee9d3a512968a5d3cc5332d461fbf4c9", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.e57a2f5.tgz", "fileCount": 5, "integrity": "sha512-cdQiIETCUFVNN79+yDNhOWL5pDbcb2utxTmkPKSBQ4hSdn/la5ViUf1e1oESwwEQTtLoQhi+0w4O1K3cdmNO+A==", "signatures": [{"sig": "MEYCIQDob2sL19ZPSh/eEB33prnRje9UoWNdWuG/yxEUsICiqgIhAOUA0p4Fo8C1+u2Dv9/n3qPJFM4AQeMTnwiolzPiKv3I", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.e57a2f5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9669}}, "0.0.0-insiders.f3157cd": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.f3157cd", "dependencies": {"tailwindcss": "0.0.0-insiders.f3157cd", "@tailwindcss/node": "0.0.0-insiders.f3157cd", "@tailwindcss/oxide": "0.0.0-insiders.f3157cd"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "9042f39508108280035adf5a34569cd5b958fc65", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.f3157cd.tgz", "fileCount": 5, "integrity": "sha512-guKE4QzIJibHBZKoOaqUiyd7NC7JlQ5M/STgmnxxjVkVSN6J5v1vVylG15McgpH+3u61Eav6cMwZ2DQg48nUJQ==", "signatures": [{"sig": "MEUCIFbDZR6oj7w99mmOGvrNkqRIN08Icr3B09Ikn39RBRMpAiEAkhh00FEPon8sEljCSlR1eCUOBShhsAEdP9uXXhBsap4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.f3157cd", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9669}}, "0.0.0-insiders.6fb98d2": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.6fb98d2", "dependencies": {"tailwindcss": "0.0.0-insiders.6fb98d2", "@tailwindcss/node": "0.0.0-insiders.6fb98d2", "@tailwindcss/oxide": "0.0.0-insiders.6fb98d2"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "3bbb87fbee849eeb8f776da0ccf3924754e50d51", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.6fb98d2.tgz", "fileCount": 5, "integrity": "sha512-i7X5HTFNa+jYuub8Y5cuknTOAXJQcid1t67SglDI4EESHjoAHKb7b91rolXV9jOejI9xvD8TtCPiN26i96l1qg==", "signatures": [{"sig": "MEUCIDNVx5Modb5ozE8ClFuwepxLYn4XvP47tM5c87WIMd9+AiEA1oVpz7TRGeFriQxUXSfpT8bDRI0co9RHUCtP7uMuUJo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.6fb98d2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9669}}, "0.0.0-insiders.1ada8e0": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.1ada8e0", "dependencies": {"tailwindcss": "0.0.0-insiders.1ada8e0", "@tailwindcss/node": "0.0.0-insiders.1ada8e0", "@tailwindcss/oxide": "0.0.0-insiders.1ada8e0"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "c53b2b222061eb9340dbdc0cd32d64cddd3b7ae4", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.1ada8e0.tgz", "fileCount": 5, "integrity": "sha512-RsM8zoxjE7jSycuVlD/aFsEmQKlmuKYj02Fci6HHQbzoiso0N8+kq7NbtoVKi0r5pWuhAEsYjvNAjLBvJlPQgQ==", "signatures": [{"sig": "MEUCIAcb7T5qUB6qaWcRZZKI1ivF6+ZHySdqio1X1gDTkSm3AiEA3yaArlWAci0gLENNeSDHTAcZj8edyaJImmNUo6BBdYw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.1ada8e0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9669}}, "0.0.0-insiders.bf591fe": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.bf591fe", "dependencies": {"tailwindcss": "0.0.0-insiders.bf591fe", "@tailwindcss/node": "0.0.0-insiders.bf591fe", "@tailwindcss/oxide": "0.0.0-insiders.bf591fe"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "83b61254d691c4f58acda94b233d2cb5d78081ba", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.bf591fe.tgz", "fileCount": 5, "integrity": "sha512-WS7sGJimYnF+kwMA7I7X1S6Hrqb3/WISiN+2eYxpA5P8DW3FbxnNL/FKYG4aGdvXGwKgQx+ovP9IdNKnCqG9Lw==", "signatures": [{"sig": "MEUCIQD/WosIZB3pMP5FFFuXTqn1rb0Co0fvXlI7O08ko7DEGAIgBUo8FJ8UKB7UPksYuGhpkb7TgFWTmdxjBV5jQzgnyeI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.bf591fe", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9669}}, "0.0.0-insiders.74e084a": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.74e084a", "dependencies": {"tailwindcss": "0.0.0-insiders.74e084a", "@tailwindcss/node": "0.0.0-insiders.74e084a", "@tailwindcss/oxide": "0.0.0-insiders.74e084a"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "1acb4c1e2cd39369683445138314c4921b4e76a9", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.74e084a.tgz", "fileCount": 5, "integrity": "sha512-LDGC82EbX/la6YcgpmdrMX5jjrtjhbNk9tB7XItedt8rxyH9kXh2jmwCPfR5Of1HuapwuXcBZFt4wzs0Q8WS7g==", "signatures": [{"sig": "MEYCIQDBeumysHIdDtnwVCjT4lSGtaa9qFyRn7hX1sEHj3EpggIhAOlQaJZhd8G9A/5VBaQ5XTesABjsOB9rI5gSHvM98/Vv", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.74e084a", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9669}}, "4.1.7": {"name": "@tailwindcss/vite", "version": "4.1.7", "dependencies": {"tailwindcss": "4.1.7", "@tailwindcss/node": "4.1.7", "@tailwindcss/oxide": "4.1.7"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "73fb55d2341982fb920f916e1ef6781099cf2df6", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.1.7.tgz", "fileCount": 5, "integrity": "sha512-tYa2fO3zDe41I7WqijyVbRd8oWT0aEID1Eokz5hMT6wShLIHj3yvwj9XbfuloHP9glZ6H+aG2AN/+ZrxJ1Y5RQ==", "signatures": [{"sig": "MEUCIHrkxbrFTpvxXt7v03U2WoXHbMA76h6KhG0yEbiGaJ5mAiEA/A0+WyCiDkijw176iMC5dDEJ/iuA6ZJt+Mh+9esxJBo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.1.7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9601}}, "0.0.0-insiders.d69604e": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.d69604e", "dependencies": {"tailwindcss": "0.0.0-insiders.d69604e", "@tailwindcss/node": "0.0.0-insiders.d69604e", "@tailwindcss/oxide": "0.0.0-insiders.d69604e"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "ce4e8884ff6e155900f92c4d59d3c636c901bf7d", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.d69604e.tgz", "fileCount": 5, "integrity": "sha512-SCfGuAjoP1eM1deRwDWRdOP6l8fxTBqgemSoTW36NShKqHvSA8ToyZuUvs8Q2BmaNo6ifzK3wsi7kbN20U9yHw==", "signatures": [{"sig": "MEUCIDchLKjb27PVJQ1vrngdrX41FTu7nAQicCdxym9R4yAgAiEAjKqr1OEXYvi9hzvGCiWd/KkGQgakK5V0YX0gtwLyV8g=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.d69604e", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9669}}, "0.0.0-insiders.c7d368b": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.c7d368b", "dependencies": {"tailwindcss": "0.0.0-insiders.c7d368b", "@tailwindcss/node": "0.0.0-insiders.c7d368b", "@tailwindcss/oxide": "0.0.0-insiders.c7d368b"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "778265f312f91ceba3024db75ba5172e97a4ad41", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.c7d368b.tgz", "fileCount": 5, "integrity": "sha512-TtxFhvZFwypQ+cvHy8xNvWXUKEZkDVnDssMiZcNb59vcCb/usOb6I9EJT4YU6Ke9yaydFWtJlzfiOmDQjWaS8w==", "signatures": [{"sig": "MEUCIQCTThA2Acomm8pnV1+WuicTeMtJ16x2zJ8i3GnbdTD4BQIgdQQvC+Jqsx2xsN4JQlD5FYfJ1AlQ4NBNIR2f0Is/3gc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.c7d368b", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9669}}, "0.0.0-insiders.71fb9cd": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.71fb9cd", "dependencies": {"tailwindcss": "0.0.0-insiders.71fb9cd", "@tailwindcss/node": "0.0.0-insiders.71fb9cd", "@tailwindcss/oxide": "0.0.0-insiders.71fb9cd"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "cac60c092e9230835e75e4fb11d0ef730d3867b2", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.71fb9cd.tgz", "fileCount": 5, "integrity": "sha512-WNdvEzhuUEJNEDasvuBWaESCNAUpHXGX6Kkl2GEViKINngJDcMqlYWZk2AWFsM0Zz9UQbB5psYDx96nIjEITyQ==", "signatures": [{"sig": "MEQCIFYT6znjGbCWf2tVAmXEo6+WRhsuusI23ziz6Osb5XsCAiB+Kzk/JXqpQQbz3H41nLgAx2s7XtAnzFJHSe6I8pmjPQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.71fb9cd", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9669}}, "0.0.0-insiders.a42251c": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.a42251c", "dependencies": {"tailwindcss": "0.0.0-insiders.a42251c", "@tailwindcss/node": "0.0.0-insiders.a42251c", "@tailwindcss/oxide": "0.0.0-insiders.a42251c"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "047717a54f49f6a4588503545d46841ef066cef9", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.a42251c.tgz", "fileCount": 5, "integrity": "sha512-p1jdm2IIRp2L0nukgLk4w1a7QSpIteOeIfsXNv+Dco+7SazFR0UDALCLam80grgiqEggHRXtoiYv6bvRp6iDHA==", "signatures": [{"sig": "MEUCIQD0+nlnNvL7ZA1J14HxsuqbGWzcDw19zhwjZYkVVyGU5AIgA3YLy59mj2Bo0xxgPybgQ3w7EZb4kBx5qh9QZrP1Rd4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.a42251c", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9669}}, "0.0.0-insiders.9df5ba7": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.9df5ba7", "dependencies": {"tailwindcss": "0.0.0-insiders.9df5ba7", "@tailwindcss/node": "0.0.0-insiders.9df5ba7", "@tailwindcss/oxide": "0.0.0-insiders.9df5ba7"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "8393144170d135a3255248e2ea05d7e267819537", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.9df5ba7.tgz", "fileCount": 5, "integrity": "sha512-aAhG8/LAqjzl3hMio1n0XWVlvPshcolnpA0+Pe00sq+MfH5/0/Y0I4Uut1m1XHQ4bl23KgIvyubSjMcjtNZ48A==", "signatures": [{"sig": "MEUCIQC16IvD6qWMQltIYzKitA5Xspz4y33sOJ8/tzcSO0IiMAIgHa58E3wOzHSq4OFp+ekG6xEllk5NpGsI5Een2qSi+1o=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.9df5ba7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9669}}, "0.0.0-insiders.7013ca3": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.7013ca3", "dependencies": {"tailwindcss": "0.0.0-insiders.7013ca3", "@tailwindcss/node": "0.0.0-insiders.7013ca3", "@tailwindcss/oxide": "0.0.0-insiders.7013ca3"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "98a7dde6dbd87aaf2beb12a6291902db2e982be5", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.7013ca3.tgz", "fileCount": 5, "integrity": "sha512-xReXOeggeZP5xqdjq84zPdTSsrQ8QnbyMZjNOT05eWmmlbG/ZRSuGkIyoLAf/J4huGTdSnMxmD1TxewOiL4X1Q==", "signatures": [{"sig": "MEUCIBcy+7y32D2BqEMQbP2ZwfYxkky5H3x7WFdqHVhupRnyAiEAuldiySaiXi/c9HmS8Rvz3YQTUC8Go689uzscX8ETq18=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.7013ca3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9669}}, "0.0.0-insiders.24ed64e": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.24ed64e", "dependencies": {"tailwindcss": "0.0.0-insiders.24ed64e", "@tailwindcss/node": "0.0.0-insiders.24ed64e", "@tailwindcss/oxide": "0.0.0-insiders.24ed64e"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "6349d043381d9f4134753b8878b62608f2bfa8d1", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.24ed64e.tgz", "fileCount": 5, "integrity": "sha512-OnffiOjs50NvVAu4QJ3uekudBsKl9XOq7uvncZCuVDEFu6X11RElpX+FAXOVTmswyFtKvCIQNogm+srftPDYEQ==", "signatures": [{"sig": "MEUCIDiTpNjiG89plrVQetwRbLgejuWulqggsyI3r6ABhsMcAiEAyIIQEOIRCm2FTonP+sc3lvi9C4xiYdpS1kYWDlwTws4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.24ed64e", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9669}}, "0.0.0-insiders.ed3cecd": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.ed3cecd", "dependencies": {"tailwindcss": "0.0.0-insiders.ed3cecd", "@tailwindcss/node": "0.0.0-insiders.ed3cecd", "@tailwindcss/oxide": "0.0.0-insiders.ed3cecd"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "2cbd23e2eda830af75334019ced8b6102d05cae8", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.ed3cecd.tgz", "fileCount": 5, "integrity": "sha512-HiMx6N5M3KgR6NcvIT0A6IDNCSMfJK5njW5TQoISxhY/8JMdJRzclihsgMzRr/sWqwVb0d4BtJuDU6oR6drR8Q==", "signatures": [{"sig": "MEQCIBQapg5B0d3xvAat27C5C9WbXJqnpDztodYmYVdBcBxrAiAHmlncEmI/OBubjImT2YgTraW5eu7u3lZ2GFr6TlYVXA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.ed3cecd", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9669}}, "0.0.0-insiders.9cb3899": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.9cb3899", "dependencies": {"tailwindcss": "0.0.0-insiders.9cb3899", "@tailwindcss/node": "0.0.0-insiders.9cb3899", "@tailwindcss/oxide": "0.0.0-insiders.9cb3899"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "51d7e8a546366a4ce8d3007b83f27f7b90d419c0", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.9cb3899.tgz", "fileCount": 5, "integrity": "sha512-Xkit5okiQ2Az/MyJDvlduBn40ICQiPMDqASsnNBs1mMXm82k7C4Zu8AUcGkRt+O2m8lsIrHoKoiA5THVEO+AZg==", "signatures": [{"sig": "MEUCID+3il9NdgQBDGy2QXTsvYiqPmlHMAiTdFkMmk5egjF8AiEA7bFCAn3kl2K+S+amgpl9rASu7wTjEymagd4ferESsgY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.9cb3899", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9669}}, "0.0.0-insiders.884f02c": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.884f02c", "dependencies": {"tailwindcss": "0.0.0-insiders.884f02c", "@tailwindcss/node": "0.0.0-insiders.884f02c", "@tailwindcss/oxide": "0.0.0-insiders.884f02c"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "1a96b643da945ba5487b38776d50dec7f4730258", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.884f02c.tgz", "fileCount": 5, "integrity": "sha512-sm6r6FIQzrPdAS2Bzoxi2aJdPayN5HtFUeJqyfnpo6UL7zFwFW1Loprm0B5mMi++hVyLejnROYaK+FLG5ilfpA==", "signatures": [{"sig": "MEUCIFa8oKypSTISE0vkJgJruRic/jPtr6DK+mN6k1sf027AAiEAjNT/shD4ApugkPlN2cD+gEJedmdU0NTqO3Infk4kLxY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.884f02c", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9669}}, "0.0.0-insiders.8fcc633": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.8fcc633", "dependencies": {"tailwindcss": "0.0.0-insiders.8fcc633", "@tailwindcss/node": "0.0.0-insiders.8fcc633", "@tailwindcss/oxide": "0.0.0-insiders.8fcc633"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "7a78939498cbef8e576ac6cb08a760b2f9f19dfb", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.8fcc633.tgz", "fileCount": 5, "integrity": "sha512-rVsqF6fb6O03MAkN2RAjLwNIAtFMf4rfC6656mvwbIlWhBxHQZpWNr8PbvVhyvNomeYrbigIiTvOJf357c0sYQ==", "signatures": [{"sig": "MEQCIBQ2w2YwB74ZvuLCmsPeyVMvvASZlbwTiiRDQgAiOkdwAiB69/HC+Frbq5gOdpyt7GQ/oS0+IUoLP921Zm5Vz5N00A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.8fcc633", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9669}}, "0.0.0-insiders.5d4e8f0": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.5d4e8f0", "dependencies": {"tailwindcss": "0.0.0-insiders.5d4e8f0", "@tailwindcss/node": "0.0.0-insiders.5d4e8f0", "@tailwindcss/oxide": "0.0.0-insiders.5d4e8f0"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "726f6284edc901c3b979fe17b44dd0f973e258b3", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.5d4e8f0.tgz", "fileCount": 5, "integrity": "sha512-Is0xVadee+M8NetxIFhxLMKXwAG70cN8U1neJ7ZE4ej/NSbZ2n0A+koOu+zr7k0ApmLsoKA0Mjz7uTpMNqr+Jw==", "signatures": [{"sig": "MEUCIQDk5PqbWP3a46oHQRd5exQZV2j3Krew+TAx45MfPdrojQIgGeZwqXRBr7Mcgj17GQbuhyrUboMon2Mp6M1H9U/gc5M=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.5d4e8f0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9669}}, "0.0.0-insiders.5131237": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.5131237", "dependencies": {"tailwindcss": "0.0.0-insiders.5131237", "@tailwindcss/node": "0.0.0-insiders.5131237", "@tailwindcss/oxide": "0.0.0-insiders.5131237"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "632deaed44531e8ed33d93bfc0ee80842b3ec37d", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.5131237.tgz", "fileCount": 5, "integrity": "sha512-yP7IaQm/X80casLkB+0P22+3uNl5R2j5crR0vWtBL4E4G1mftSK75lchyEw9f0huS6S6Hjpou+6Ua/DZNXHYAw==", "signatures": [{"sig": "MEYCIQCJ7vyebBwLixJkyAj2VZFMYdV1J4hanVvfzcTMPyn34gIhAIR4T25RlPHr1ETjutBG7unBaDfigzHNAt6iHRyZ68Lr", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.5131237", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9669}}, "0.0.0-insiders.1d4c263": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.1d4c263", "dependencies": {"tailwindcss": "0.0.0-insiders.1d4c263", "@tailwindcss/node": "0.0.0-insiders.1d4c263", "@tailwindcss/oxide": "0.0.0-insiders.1d4c263"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "07b85924165a709a3394f82e03dcfe37f7524561", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.1d4c263.tgz", "fileCount": 5, "integrity": "sha512-2xrus9hkku9T6RFWGbDoGCuXPogoAOoodINDcNZGLEPwIURRUQ0Dmqb8XQLcjziUHaZ4WHBIfbIy9mRlACIKoA==", "signatures": [{"sig": "MEQCICm/tkS6S2KO03ZnAMBpJN0OAulWUnmDOYrR5brtNOfMAiBxFiN1PdeKOl/SIwQPPQEZ10Sdq/pehP2y4ALE//chhw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.1d4c263", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9669}}, "0.0.0-insiders.a37f6ba": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.a37f6ba", "dependencies": {"tailwindcss": "0.0.0-insiders.a37f6ba", "@tailwindcss/node": "0.0.0-insiders.a37f6ba", "@tailwindcss/oxide": "0.0.0-insiders.a37f6ba"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "3fe1bdbe0bdf69ead106bbb3a445c84771dd3da6", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.a37f6ba.tgz", "fileCount": 5, "integrity": "sha512-CRiz44367XHu4ufBbVseGfRtpxUa2UMeQ7YmgflZYd8BdIXSKHc5cT5mPQtd/U9sICUCp5q6JyMr7lSRfmXHtw==", "signatures": [{"sig": "MEUCICayyBZxXuQh5jC8p3JO3BT2JZUjXkI2EEPMZShPllbZAiEAvD0iWCi1jLPVX8xER3LrVGfnrzr24EYHFobI23XzYhU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.a37f6ba", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9669}}, "0.0.0-insiders.58a6ad0": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.58a6ad0", "dependencies": {"tailwindcss": "0.0.0-insiders.58a6ad0", "@tailwindcss/node": "0.0.0-insiders.58a6ad0", "@tailwindcss/oxide": "0.0.0-insiders.58a6ad0"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "240c91518a6793ee3a4cf2984ac895e858c3182d", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.58a6ad0.tgz", "fileCount": 5, "integrity": "sha512-b6TsNXtR85Fkdd3lbU9OJyQKXwqYTD/e0xYBQb3GHe4r+Yd3LzER4flX00LUUW7/uDOBNmYibDJA2UVTslMrYA==", "signatures": [{"sig": "MEQCIElO/AGD8DhX1E9IAt+PGLQd9bD3/Wx5SVIXbhAntHXxAiAMerCQ09gKDaIxR8hz3QAQGF/MJGLHYp3DvACXqd2laQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.58a6ad0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9669}}, "0.0.0-insiders.4bfacb3": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.4bfacb3", "dependencies": {"tailwindcss": "0.0.0-insiders.4bfacb3", "@tailwindcss/node": "0.0.0-insiders.4bfacb3", "@tailwindcss/oxide": "0.0.0-insiders.4bfacb3"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "d1881ca0fc6a4defae04450c04c2e6328e64ab7c", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.4bfacb3.tgz", "fileCount": 5, "integrity": "sha512-BVm/CbvCmljhi39yk5nWsW8WC44mZCIkpbm55Iz3Q3vDEY3YAXOGR/6NwAM8bmRjpZl6AiB/fMtjqFMky8niaA==", "signatures": [{"sig": "MEYCIQC7NY1D240jplPx2yiJGBUZ7xHPh2z6EP4EPXnHeg7ldwIhAJGSTV62qe+a5NnZYiR+omoKu9vVpdRB4ygKccF3VAHI", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.4bfacb3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9669}}, "0.0.0-insiders.193eb84": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.193eb84", "dependencies": {"tailwindcss": "0.0.0-insiders.193eb84", "@tailwindcss/node": "0.0.0-insiders.193eb84", "@tailwindcss/oxide": "0.0.0-insiders.193eb84"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "34ab6324553481f8aed0511d366e3e817a08ccc5", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.193eb84.tgz", "fileCount": 5, "integrity": "sha512-rDa+TcXzeZIUeYx0/FayvEB1zzS8HKI0UgCYTzT+EpKoz1qvutkMtLEh1hbbMWMcqgkHASz7j+00TqECxvG5ng==", "signatures": [{"sig": "MEQCIGAWlGBSRhd/S4FDV45iNf3E9sac8PmjL5EXfeQsWfaRAiBygwF1P6moR01RJjlhmDiS84x5F+NI0cvWkmUCP+v1Gw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.193eb84", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9669}}, "4.1.8": {"name": "@tailwindcss/vite", "version": "4.1.8", "dependencies": {"tailwindcss": "4.1.8", "@tailwindcss/node": "4.1.8", "@tailwindcss/oxide": "4.1.8"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "4da74494e2e0578767e02b96450b7f5127862698", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.1.8.tgz", "fileCount": 5, "integrity": "sha512-CQ+I8yxNV5/6uGaJjiuymgw0kEQiNKRinYbZXPdx1fk5WgiyReG0VaUx/Xq6aVNSUNJFzxm6o8FNKS5aMaim5A==", "signatures": [{"sig": "MEQCIGh/jYdqnjft8AQ9h+h622Aan9o81CK6JNZWGJZRzlHAAiBGxjbsgjJnA4ABQ8Ci62fzZbPGjZyktqNyL1bR/2SMTQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.1.8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9601}}, "0.0.0-insiders.3c629de": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.3c629de", "dependencies": {"tailwindcss": "0.0.0-insiders.3c629de", "@tailwindcss/node": "0.0.0-insiders.3c629de", "@tailwindcss/oxide": "0.0.0-insiders.3c629de"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "469456769a0fc5eeeb40f718caf431ba23228d42", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.3c629de.tgz", "fileCount": 5, "integrity": "sha512-Gkz7cniBDH2IXPE9g6YL6NLxP0j8XHX4FRMSZG7dQdhqX/fBKE5W0Zrhrd5ZF9z/+q7Vyp+C9N/lbYC0KKDLzg==", "signatures": [{"sig": "MEYCIQCve8ShgfEzqSBwIzqBFtKlfyGYcSs3SM4sDptcrNRFVwIhAMmiwuczp4pyDdGjim/egmqi35trLyrsj55CwuxZ9Y3Q", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.3c629de", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9659}}, "0.0.0-insiders.31c0a21": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.31c0a21", "dependencies": {"tailwindcss": "0.0.0-insiders.31c0a21", "@tailwindcss/node": "0.0.0-insiders.31c0a21", "@tailwindcss/oxide": "0.0.0-insiders.31c0a21"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "3690aaeff85cbf906ce026b97c88da609b7f9049", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.31c0a21.tgz", "fileCount": 5, "integrity": "sha512-Kko13HnTty+1WhNHRkYagCm6Fm3aSIRAxu2LNXIEotIHgXDm34/BDzEzpEiok9tdCuTix3XFJuo+tpVtAKR+5A==", "signatures": [{"sig": "MEQCIDDPP0glR7VvTninoMBLBF7bK4AyIIXFEBc3bLqY5cs8AiBj8j5Yacs6ofHhpMCY7EMZCEVU0qo36Cnjdx4YCGRNyA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.31c0a21", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9659}}, "0.0.0-insiders.54c86d4": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.54c86d4", "dependencies": {"tailwindcss": "0.0.0-insiders.54c86d4", "@tailwindcss/node": "0.0.0-insiders.54c86d4", "@tailwindcss/oxide": "0.0.0-insiders.54c86d4"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "ae41561742d3fe1d79f7a6b0d0ccf8f7917b3f6e", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.54c86d4.tgz", "fileCount": 5, "integrity": "sha512-6vA1ZYRuvhWUDlj4IzVjKUWEnGCpYG0SSmPwnhZ2zndV8nye0DpHw4Hrf5SZNMikGMhjEqd3rlEjp317M/uMUA==", "signatures": [{"sig": "MEYCIQDMmw0y1NJFW9ARxlG1/GyyVyWkHLPmlOrmDR9RRYjXkQIhAKKd06NgENTmr+XxqqZRLFhcURvzuk8tDGnuiWGyUykJ", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.54c86d4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9659}}, "0.0.0-insiders.191195a": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.191195a", "dependencies": {"tailwindcss": "0.0.0-insiders.191195a", "@tailwindcss/node": "0.0.0-insiders.191195a", "@tailwindcss/oxide": "0.0.0-insiders.191195a"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "31c6d3b8feb57c6656e29bc596f3e40d19a6f331", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.191195a.tgz", "fileCount": 5, "integrity": "sha512-ix2SMic8BAM4cmv1MomCP6KPCZsek1YTXvy3qyrgQG4Wz2cXmyZIMGpbEfgCXn4US4spGrB8VzGbHD50zKZ9BA==", "signatures": [{"sig": "MEYCIQCHPSdHXAb8swLIBaHGave8tja+iulSZaXouQBkqrY5WQIhAJ3ZCj7RIMBn7H4IGvoQT8MmbwEq4o6oxaA/p51FPtiK", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.191195a", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9659}}, "0.0.0-insiders.b3fde17": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.b3fde17", "dependencies": {"tailwindcss": "0.0.0-insiders.b3fde17", "@tailwindcss/node": "0.0.0-insiders.b3fde17", "@tailwindcss/oxide": "0.0.0-insiders.b3fde17"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "e531053b0a3ff74e4e32be9aa7702b4472dec2d0", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.b3fde17.tgz", "fileCount": 5, "integrity": "sha512-0CMTzb6fZhtZh2pwWT2ru6821iCZN+blQBkJF0fa7gik4YiG465PVfgO/h7ckDHpFZVOl/I5wUeZtBY66s6wBQ==", "signatures": [{"sig": "MEUCIQCC0R0M1zf0Cv4xICOjyVw2RcPrEhhwlVoFI1nBGG8IdwIgHTs5g6tcUC90jiFVSw2UVtFjVJQe6m9hgnD4L6mHAOI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.b3fde17", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9659}}, "0.0.0-insiders.f425720": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.f425720", "dependencies": {"tailwindcss": "0.0.0-insiders.f425720", "@tailwindcss/node": "0.0.0-insiders.f425720", "@tailwindcss/oxide": "0.0.0-insiders.f425720"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "a55dfa73fbfe09a1809022e802c62a3e00ca38ec", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.f425720.tgz", "fileCount": 5, "integrity": "sha512-x1gBd0ZEOhFNyHeoXD5yjFvklE117LzsFLumnjqrPHPVhgxcfmRK8FmpJLyvPv4c/wWsN0E5+H797pEtkFal1Q==", "signatures": [{"sig": "MEYCIQCQm2X5Y0FaCnEsPvpTLjdpJl479KIjmgDeDTST5xyFxwIhAOEDRwHiYkpq6dClwbOQ5YXfK6Nmlz3gLhlQmsnyCp7f", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.f425720", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9659}}, "0.0.0-insiders.0c1c0c4": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.0c1c0c4", "dependencies": {"tailwindcss": "0.0.0-insiders.0c1c0c4", "@tailwindcss/node": "0.0.0-insiders.0c1c0c4", "@tailwindcss/oxide": "0.0.0-insiders.0c1c0c4"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "3024426367d3c28f5c8c79be35b6f446d7e1ddb2", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.0c1c0c4.tgz", "fileCount": 5, "integrity": "sha512-OeUZ4EpVaw0e+p42rygXVm+8609tM0aGE7x1961xAYxYJLAg/0j8cgMaDMq1dRGPeD/Tzhq+WYQJ8FxGp+4hiQ==", "signatures": [{"sig": "MEQCIETfu4g27/gYC01TUptnUyCz5occwVAWL+Z9X4Y/eBYeAiB4vO44nrkYH4gvuO68xPazUZyUTLtAXMNKjoZmgduUGA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.0c1c0c4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9659}}, "0.0.0-insiders.8bfbac5": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.8bfbac5", "dependencies": {"tailwindcss": "0.0.0-insiders.8bfbac5", "@tailwindcss/node": "0.0.0-insiders.8bfbac5", "@tailwindcss/oxide": "0.0.0-insiders.8bfbac5"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "e478a2f0a67754939835b678a7e02ffc0b19c8c0", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.8bfbac5.tgz", "fileCount": 5, "integrity": "sha512-rAPrXjc08VBwknjYamFUtKxvQwfHlDhX2nxZb++2FSVqPeKcKpucX1u2Y8cqsMjEWgEkz+r06JPfSfumAEbzSg==", "signatures": [{"sig": "MEYCIQCGrTf1zy5ujILpeCnZuYPY5jNYTtbpwHodD0zs7dpmvQIhALLwr5wp17sgmxuJHDXvOD2O7R9xUa/2InKIgdNmNRtl", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.8bfbac5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9659}}, "0.0.0-insiders.288ab3e": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.288ab3e", "dependencies": {"tailwindcss": "0.0.0-insiders.288ab3e", "@tailwindcss/node": "0.0.0-insiders.288ab3e", "@tailwindcss/oxide": "0.0.0-insiders.288ab3e"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "3aa0578d404ac8fc74a13a7f24ccfe0bf2de6080", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.288ab3e.tgz", "fileCount": 5, "integrity": "sha512-n5tDlLRS+IW3+iiBHJxwmjkU+8ec3xZiaFq/whwJZ0E6++ssliXYc9zGbYwShVph/ZsXxQEnHvfuHrrVe6RW1g==", "signatures": [{"sig": "MEUCIA8odkSmwL32YF7Pg8zDurOrwvwu6VabyoLrXe9uxTQQAiEA0YMBbhvjIwiq+ALPLp4bsVbQg6xtImxiXZ97nRFtCBY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.288ab3e", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9659}}, "0.0.0-insiders.63f6a6c": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.63f6a6c", "dependencies": {"tailwindcss": "0.0.0-insiders.63f6a6c", "@tailwindcss/node": "0.0.0-insiders.63f6a6c", "@tailwindcss/oxide": "0.0.0-insiders.63f6a6c"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "584f379b52e425b4bdb38d5610dadfff65ae9f44", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.63f6a6c.tgz", "fileCount": 5, "integrity": "sha512-3Q34P0w73Ow36oKWsV4OYBCDiYMfYvgxlKqDrN87gEzdoc9+dZEXvNs1TGDX3JKRH0pqjzhqtcyLKQMXC9l1MQ==", "signatures": [{"sig": "MEUCIEihriN/S2qH89y4tOj4jigN+L0rz2FPDv5IGndip004AiEA31/WRXWCYtrc/6YTJ9n0w28zqkC5rHkcWbKWZPLRq6c=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.63f6a6c", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9659}}, "0.0.0-insiders.21ece6c": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.21ece6c", "dependencies": {"tailwindcss": "0.0.0-insiders.21ece6c", "@tailwindcss/node": "0.0.0-insiders.21ece6c", "@tailwindcss/oxide": "0.0.0-insiders.21ece6c"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "41ca0b24a3bc9ea4613769af33975b51aa169669", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.21ece6c.tgz", "fileCount": 5, "integrity": "sha512-4HIdYWiv++jghlpWg8/JNm3I0V3jBl5gZPFBv2jtLr3++ep+5YPAibKKfbta1PQIeQ7gWaxbRm4wwbhR2iDChQ==", "signatures": [{"sig": "MEQCIAtq4H9SUBO0hR7EdkfW1lfYLALH9hLXGereCEtL+hcCAiA5HOH/R/8rfA20sGNJa/IPwH+BkshDmDsR+soFcxEEyA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.21ece6c", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9659}}, "0.0.0-insiders.ada85b1": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.ada85b1", "dependencies": {"tailwindcss": "0.0.0-insiders.ada85b1", "@tailwindcss/node": "0.0.0-insiders.ada85b1", "@tailwindcss/oxide": "0.0.0-insiders.ada85b1"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "988883440481dcade2c2572e1ee9c84e53747870", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.ada85b1.tgz", "fileCount": 5, "integrity": "sha512-9jM2YLtnyO4bn8UU5CUJTzJhJSw67mZj70DDuKjRpHaF7xROOrix4CHzH/N7QWUuKIPYIEzm13zGPP1CVLzesg==", "signatures": [{"sig": "MEUCIQDpN2y80Bqnb3XzLkJEmvDBPbcsFtQ3D72eOCjvg0EW/AIgTuc/3Wzj4T6rpwq4Nmm2vUF+pgpSIokwXprBTqwhHd8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.ada85b1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9659}}, "0.0.0-insiders.f0f42f6": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.f0f42f6", "dependencies": {"tailwindcss": "0.0.0-insiders.f0f42f6", "@tailwindcss/node": "0.0.0-insiders.f0f42f6", "@tailwindcss/oxide": "0.0.0-insiders.f0f42f6"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "7f0c2d1c5f0e18bac789eb7dd083a40c9e0a144f", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.f0f42f6.tgz", "fileCount": 5, "integrity": "sha512-+t7iizGSZBaoMXXcXft9/OEbC2vljzPi7CHb7zT4sGoncRfWNgXCJ7f2rvGqYBQ0+gbHMEYP/GUTbtG7YJiy2A==", "signatures": [{"sig": "MEQCIH48m7XuX9UmSie2x12h21m+sgC0+DOSM37CJl0vI2aqAiBEq/cyuVWFet00GgXCqkoE+tZZYw7Uzsnk6IwLNkHZ0A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.f0f42f6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9659}}, "0.0.0-insiders.fd95af4": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.fd95af4", "dependencies": {"tailwindcss": "0.0.0-insiders.fd95af4", "@tailwindcss/node": "0.0.0-insiders.fd95af4", "@tailwindcss/oxide": "0.0.0-insiders.fd95af4"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "bf4181bb34f62167b95db8666e177eaac62a733c", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.fd95af4.tgz", "fileCount": 5, "integrity": "sha512-bd2HpaQP4DvVoH+VY4bkqSHR5kuj1hkBz/VYuhJLn7WIDCPhz21dJ8s9RmvH0by8aM//E3B3I9P6dEWctggyVQ==", "signatures": [{"sig": "MEUCIQCl/mI5KQgMCo35VQUIHdBzsGRdQPgizlLgvDOS7KZ9AwIga2pXXzyAZ0AM7FOCq7cI5sElQK0U4dBDrtzfYULTTVY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.fd95af4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9659}}, "0.0.0-insiders.bea843c": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.bea843c", "dependencies": {"tailwindcss": "0.0.0-insiders.bea843c", "@tailwindcss/node": "0.0.0-insiders.bea843c", "@tailwindcss/oxide": "0.0.0-insiders.bea843c"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "0a3021d5f0d19ca2cad0523b35bf4b8de2ef3463", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.bea843c.tgz", "fileCount": 5, "integrity": "sha512-tSBdZ1k5g0bBdC1a1wP+YUyVfXHZs7pEQhOT9I9Ce5yGbNP7ibRpYp8VJ680/rfGDaXLQVZcDviiuOnN2tJLSg==", "signatures": [{"sig": "MEYCIQCTWEq8UgD3GJRk0nqQBlm2TzQLbGiXtEKtBRHbXPPq0QIhAIrf83Ve0uqY6iG99yejot5swjkmvAWbHnB8uH34uLFh", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.bea843c", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9659}}, "0.0.0-insiders.da08956": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.da08956", "dependencies": {"tailwindcss": "0.0.0-insiders.da08956", "@tailwindcss/node": "0.0.0-insiders.da08956", "@tailwindcss/oxide": "0.0.0-insiders.da08956"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "48c60fb7fa72b1e6c80b321a8c9ac59b59ee2715", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.da08956.tgz", "fileCount": 5, "integrity": "sha512-Gfc8s77l8A/Ro30gvl9kw1RKjiXi5HgQ57o7o5A0pPvCl+I7T/4/JWzKPJ9lIKFTU1QOM+Jvt8D6WfWuAo1q/w==", "signatures": [{"sig": "MEUCIQDNauI6RWferJfC/5aT1MZkCncNsGgV/vZQ81NPP5hoDwIgK5pmM1+Kcf53BPakFDAaTkfBLPHYFhbArWHO1tR/BoM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.da08956", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9659}}, "0.0.0-insiders.aa817fb": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.aa817fb", "dependencies": {"tailwindcss": "0.0.0-insiders.aa817fb", "@tailwindcss/node": "0.0.0-insiders.aa817fb", "@tailwindcss/oxide": "0.0.0-insiders.aa817fb"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "abfae187ae04611691ce21d6738e5f79c8bc16b8", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.aa817fb.tgz", "fileCount": 5, "integrity": "sha512-GR+I/AcH3ZftggJCyJ9DBpDzfC2eNrIHzcCD4KpkTMPNfuJd4uXYC3SsJ67fuWcyxm0ll4nuHAfqUpeqy5rxHQ==", "signatures": [{"sig": "MEQCIBvk0KrQyko4JOtFBLZhGzWaYxB0brj2AygHy4NIIBF7AiAFdMRUDJA5DyUJGye1kIheWD4rekNb6JsZLuZ8ckYx4Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.aa817fb", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9659}}, "0.0.0-insiders.b88371a": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.b88371a", "dependencies": {"tailwindcss": "0.0.0-insiders.b88371a", "@tailwindcss/node": "0.0.0-insiders.b88371a", "@tailwindcss/oxide": "0.0.0-insiders.b88371a"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "0c05beb1f32b519f776616782d482a5477be547f", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.b88371a.tgz", "fileCount": 5, "integrity": "sha512-LhLFO/5Uws0Xas92OVLCchRBVKewY97W2xbFlwLtcfR8Ne+Sq1Thg+H7XtN1StPQikgOQusJ6GNWlMAf0Laxew==", "signatures": [{"sig": "MEQCIBKVk7FM8O+ta1hCQYKFoj7XcRw28dlt+ikTiHpUBxa/AiBaZw/Nb7sCU8pQOyD0SIfbd2Tk6rdiztMSnT2Ar/RJkA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.b88371a", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9659}}, "4.1.9": {"name": "@tailwindcss/vite", "version": "4.1.9", "dependencies": {"tailwindcss": "4.1.9", "@tailwindcss/node": "4.1.9", "@tailwindcss/oxide": "4.1.9"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "70c3e5cc59edd7aa7e2c7dd61a6cf006f8457b00", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.1.9.tgz", "fileCount": 5, "integrity": "sha512-JdcROJysSGRpDq0JT5XPxRjF3rq4QnZD/PsNUVIQrMyYHUIBxRFPTUmGlWjy24igeC3rAgcRIDGLSd9AsljW5A==", "signatures": [{"sig": "MEQCIB1XtJ/encMNzvObSbcOGmpGK150l1swasp/v5EILS0RAiBVQCYiEn0jQYY7CNC6Yx+0fB3V55SXlwifgdmMmR3t4g==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.1.9", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9591}}, "0.0.0-insiders.427649e": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.427649e", "dependencies": {"tailwindcss": "0.0.0-insiders.427649e", "@tailwindcss/node": "0.0.0-insiders.427649e", "@tailwindcss/oxide": "0.0.0-insiders.427649e"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "bab66fbf8da40d230a3299308f9bed3e4c8a6e57", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.427649e.tgz", "fileCount": 5, "integrity": "sha512-E8RtrRKTxgjIdpO1ND89uAnqrOFffY4RLY5gCLh3i5p0buIWndAojbLfgfZfdDwq0kRJLSSqNHzAtTPwet6flw==", "signatures": [{"sig": "MEUCIQChYRdXQzXqFpvxF3VgOjpX4F4+4rcs8Mhqexv1N/Q8XQIgBrHvV5gcyL1IEDYrO70cxHXF9rzEF/V+RCokDgcO/Ek=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.427649e", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9659}}, "0.0.0-insiders.ddb0bef": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.ddb0bef", "dependencies": {"tailwindcss": "0.0.0-insiders.ddb0bef", "@tailwindcss/node": "0.0.0-insiders.ddb0bef", "@tailwindcss/oxide": "0.0.0-insiders.ddb0bef"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "aad46d028351b98e30ec3bef5e125367906fff59", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.ddb0bef.tgz", "fileCount": 5, "integrity": "sha512-nfjpbfKj/Od4NHuacoQsYpRwNmVj4DxG8mtIu9PMeMGfvxM2punKy2krf2snkkx5RhvogKdrNFHSAbcf6QFiXg==", "signatures": [{"sig": "MEQCIFuI+1KURthAfwuIlg/gP/U0sIYve4+8MU56GVX9TrhCAiAH4wLb0BaEDS9Y0Srt8xkAcuIYIlGN6gco2O5NAMvZVA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.ddb0bef", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9659}}, "0.0.0-insiders.d06bbb8": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.d06bbb8", "dependencies": {"tailwindcss": "0.0.0-insiders.d06bbb8", "@tailwindcss/node": "0.0.0-insiders.d06bbb8", "@tailwindcss/oxide": "0.0.0-insiders.d06bbb8"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "7a335eb156be8da65efa256e5933469357e45ece", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.d06bbb8.tgz", "fileCount": 5, "integrity": "sha512-QXfVUY7vw2+ug4tT0yBKeCM6mIwznJlKAoHU+fP7N92SR7Q8yS0D5eGtgjFR/CFimmE66VzESJYqqd8G61TZUg==", "signatures": [{"sig": "MEQCIFXefQBbpN+an7nC0oBPfEMmFrHgqFCM7HZoXHON7DvfAiBPxcIOXbE6rHwfKEJ2Ku30/bT4z/5RS720iq77tD1WgQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.d06bbb8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9659}}, "4.1.10": {"name": "@tailwindcss/vite", "version": "4.1.10", "dependencies": {"tailwindcss": "4.1.10", "@tailwindcss/node": "4.1.10", "@tailwindcss/oxide": "4.1.10"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "9ffa396a3f85d31f53eeaa4bac33eb0286bc955d", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.1.10.tgz", "fileCount": 5, "integrity": "sha512-QWnD5HDY2IADv+vYR82lOhqOlS1jSCUUAmfem52cXAhRTKxpDh3ARX8TTXJTCCO7Rv7cD2Nlekabv02bwP3a2A==", "signatures": [{"sig": "MEQCIBtNAvttEdk7d/ErlFuKnvoa8GTWw6NMarl2lpskdXgKAiARrHAMu4RbSb1jOv/8SUxHMgHR+cQ/E9tyBxroV1ZUdw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@4.1.10", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9595}}, "0.0.0-insiders.2ebaff2": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.2ebaff2", "dependencies": {"tailwindcss": "0.0.0-insiders.2ebaff2", "@tailwindcss/node": "0.0.0-insiders.2ebaff2", "@tailwindcss/oxide": "0.0.0-insiders.2ebaff2"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "1dc0ce68c3c3bb4aee30b07b846e61163d2b61ca", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.2ebaff2.tgz", "fileCount": 5, "integrity": "sha512-Wweyd91H4ixMaPW0Go/uoBQSW0WI9segU6PcIhHymR5IiHQgwENQsdIUT+jZQU104TCt5bVEPGfRuRiXrCOnBw==", "signatures": [{"sig": "MEUCIFZIdg13eY+s2DDC56B+vqawH9LseDE5j+uwZfEegcc5AiEA4r24QyVPxqZ1JptS1fb/dSgbYeoknoAnEpXn/k8NE3s=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.2ebaff2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9659}}, "0.0.0-insiders.7f97179": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.7f97179", "dependencies": {"tailwindcss": "0.0.0-insiders.7f97179", "@tailwindcss/node": "0.0.0-insiders.7f97179", "@tailwindcss/oxide": "0.0.0-insiders.7f97179"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "5178a8a86203b3e4fee664d4d96178db0715abd8", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.7f97179.tgz", "fileCount": 5, "integrity": "sha512-sHnCcvJnPUnm7lPdmhY08Go/CTq7MR2d0nSjfnnQDX+7mWt2ye0AhlWz072qpXXtFT0T7WscLQS+1lf+bEmH2A==", "signatures": [{"sig": "MEYCIQCx83XdqCGBp9olhAD8kgmRO0afsdkFSyPUnfAyCtsVVQIhAMFoBwwDbrTiY80yZIRvudLAXUBeaMdbX6CabpwwC0T2", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.7f97179", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9659}}, "0.0.0-insiders.bab16ae": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.bab16ae", "dependencies": {"tailwindcss": "0.0.0-insiders.bab16ae", "@tailwindcss/node": "0.0.0-insiders.bab16ae", "@tailwindcss/oxide": "0.0.0-insiders.bab16ae"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "3bbd534c171ac761788785bc4d1fa72a37016ee9", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.bab16ae.tgz", "fileCount": 5, "integrity": "sha512-/2xc/hEWzfS8D7jeUdPMgZ+7G7P6SipZfp7KyH/g68WnJA7U2g1zSkDJG7PdyAcHAidtIeQ+Nfbtr465Ik87Lw==", "signatures": [{"sig": "MEUCIFGaX0ux60htW7F5q8G3VXuQQQiZnCGQlJsUlr4Y/dr3AiEAjrV1enVXXWvKhKY1kEhn0IN6CiKYKDyJz7Jsey2uJao=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.bab16ae", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9659}}, "0.0.0-insiders.4453496": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.4453496", "dependencies": {"tailwindcss": "0.0.0-insiders.4453496", "@tailwindcss/node": "0.0.0-insiders.4453496", "@tailwindcss/oxide": "0.0.0-insiders.4453496"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "f3caeaf5263ecbb0a8edc4a3d472969de6a5e210", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.4453496.tgz", "fileCount": 5, "integrity": "sha512-YzFNqAVCMM0intWsGFXqfZqI9oK2YKaWl/rkAFNOQp6IfSX9g7xI8syQLEe6aIX+38cvs7rRlsULgulO+akB9w==", "signatures": [{"sig": "MEYCIQDDBDlPH09eWpQNSssUBtnPyl5wQRNXjVNZ1iOow2RSWQIhAL4Ud62HvTXESK/ziwh2RK2cFxKbT3ozGQPP/U1NJUl4", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.4453496", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9659}}, "0.0.0-insiders.63b5d7b": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.63b5d7b", "dependencies": {"tailwindcss": "0.0.0-insiders.63b5d7b", "@tailwindcss/node": "0.0.0-insiders.63b5d7b", "@tailwindcss/oxide": "0.0.0-insiders.63b5d7b"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "890a1f048b57c5974daeb947fa378548792394be", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.63b5d7b.tgz", "fileCount": 5, "integrity": "sha512-EIGe0k/DK3lLmXCcQdVcyme1GbAwrG+LmyJtBBWyoI4eFhQUrC7dhr8UFdba+wrOo6Ow3KZWGs6xl20TcxVU4w==", "signatures": [{"sig": "MEYCIQD+IZkhJvjUhosDK+1OG4mJIc/MDfPPgFKXU4XI+Od9kQIhAMJh30//wIIrKnAsWlqKB7JJOQulv4j0jCUrai8JyHG0", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.63b5d7b", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9659}}, "0.0.0-insiders.75cbfc2": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.75cbfc2", "dependencies": {"tailwindcss": "0.0.0-insiders.75cbfc2", "@tailwindcss/node": "0.0.0-insiders.75cbfc2", "@tailwindcss/oxide": "0.0.0-insiders.75cbfc2"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "fe83a34380d99394bd389c5eceec6c440c18ca30", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.75cbfc2.tgz", "fileCount": 5, "integrity": "sha512-tDaoUERxyF7ZtUzFvRAZeRAtS1vgiAktb3tSsFimmH+u5SG1FrQ0w8R9sUZbRFJlF4S1Cjz2v+CQ+Q9DEzdO5g==", "signatures": [{"sig": "MEQCIDf6d9MkHmxNoj84WJP2EarxNdRvlkBCGDwaQ534k4DgAiBzY4thXA+0VYycfqjGFbIrFQEbmyk2EtOyypT5AkWcxA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.75cbfc2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9659}}, "0.0.0-insiders.f4a7eea": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.f4a7eea", "dependencies": {"tailwindcss": "0.0.0-insiders.f4a7eea", "@tailwindcss/node": "0.0.0-insiders.f4a7eea", "@tailwindcss/oxide": "0.0.0-insiders.f4a7eea"}, "devDependencies": {"vite": "^6.0.0", "@types/node": "^20.14.8"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"shasum": "5c43f2cca0cdd06f3eacd8cacbc5a378ee4d6e10", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.f4a7eea.tgz", "fileCount": 5, "integrity": "sha512-6cwBer4h+acRkeByTwIQqZKLJli5pb552yO4SXYghxmzqgHnRkKDfk1XuQFkxhdzSOr+Xl7Q0FSHwArXxHVD2w==", "signatures": [{"sig": "MEUCIQCAUQzrHaX0YbkH2rGorUAFihZqBcz1iu7vjhtj1elb4wIgZBOFDFnVWBzVm+gyWEqOg3adUsIpePWxaIC+3tYcQBM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.f4a7eea", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9659}}, "0.0.0-insiders.c5a997c": {"name": "@tailwindcss/vite", "version": "0.0.0-insiders.c5a997c", "dependencies": {"@tailwindcss/node": "0.0.0-insiders.c5a997c", "tailwindcss": "0.0.0-insiders.c5a997c", "@tailwindcss/oxide": "0.0.0-insiders.c5a997c"}, "devDependencies": {"@types/node": "^20.14.8", "vite": "^6.0.0"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}, "dist": {"integrity": "sha512-6Z9NukxGnJpiSWUc3FSHYJ6MhtuYXQBnNeaj1iw8xma8lfZ+y5MI9QBZaiLvvycEfXBU8gbYyM0pPysKZsiLyA==", "shasum": "f9ea0c698641e2be450fb6a51d9dc0e35a436f08", "tarball": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-0.0.0-insiders.c5a997c.tgz", "fileCount": 5, "unpackedSize": 9659, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2fvite@0.0.0-insiders.c5a997c", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIBmBSvrQ3foiUxiKKg+cpYXpCBEpejzl1fj2oa6ClD3+AiEApCXMKzRof0z0hCEwYIQDYPqdALNmPCJnboAT/v4LmcM="}]}}}, "modified": "2025-06-19T19:01:12.604Z"}