{"name": "@webpack-cli/configtest", "dist-tags": {"latest": "3.0.1"}, "versions": {"1.0.0": {"name": "@webpack-cli/configtest", "version": "1.0.0", "peerDependencies": {"webpack": "4.x.x || 5.x.x", "webpack-cli": "4.x.x"}, "dist": {"shasum": "2aff5f1ebc6f793c13ba9b2a701d180eab17f5ee", "tarball": "https://registry.npmjs.org/@webpack-cli/configtest/-/configtest-1.0.0.tgz", "fileCount": 5, "integrity": "sha512-Un0SdBoN1h4ACnIO7EiCjWuyhNI0Jl96JC+63q6xi4HDUYRZn8Auluea9D+v9NWKc5J4sICVEltdBaVjLX39xw==", "signatures": [{"sig": "MEQCIESxJN7xNcVa4WeP5BGF8hH3x5XGUJoKYz3J2PqvPjL7AiBobznkEbr9VmoljVWl9O5id/Chb9xEtPnruBZHmWwkhg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5163, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgBsJACRA9TVsSAnZWagAAlrEP/iMu55LH24bdKji0Zz5J\n1Rb+Kz1CYeFNhLLwfUsa6iL8cZnVc7RUXql3EA8czK3J3tE6IVoYEUkh+vxy\nyHHXxeoRRH+Sq+B6fGbMn9oEcZJhmM2bj37yFD2842qOy7xI2rY6V16p04KA\nRg9CLkkPV/I0CJ0YoFOn5Y7GnrJGBS4rqdnKXz5vylt8yu2dpigzOywpIyX9\nohnZJkKZIFd/esH6KznIRLHPmsn+WtZJXGg0RDB0NliDhUu86vIgaQvO04Fc\ncRYcJxZicHBC7JSvmuSt2iP3kslbPZ+1dESA93lZdEVbEKtEojyyipVKWDCe\nO89RY9BeRZBkNJF0eY0DrYxJCJF73ZFOreB9u4pvW8JS81wVghU5uLMQakxk\n7Pc17I5WS5nWq32Yjsk4KO0o003XiNSt7wS4Xga6CKyizhBxJl63CN6YrCN9\nqug+p2YxWjyWHa547Kq2nc+x0UqzZmLK79GNxkXMNPitr/tKtqipw7E40OMT\nVjvw/Xm3N+y2/Ni3Ao5vclXHjO/7cwU8knm9sH5q67iinuQgJmomcSFannlO\nQvzbCtYnC9cLm6I9cua4VAQ4YPXZyQe3E26tBfB0wEAKyna5Z/4wECxT91Kz\n8VVh6r8A6vYMfsJjIsAzzgQivcS3zMUmVtCP6BJ/04Ia9OjYqWgnu13Yfq9S\njUHs\r\n=Sor5\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1": {"name": "@webpack-cli/configtest", "version": "1.0.1", "peerDependencies": {"webpack": "4.x.x || 5.x.x", "webpack-cli": "4.x.x"}, "dist": {"shasum": "241aecfbdc715eee96bed447ed402e12ec171935", "tarball": "https://registry.npmjs.org/@webpack-cli/configtest/-/configtest-1.0.1.tgz", "fileCount": 5, "integrity": "sha512-B+4uBUYhpzDXmwuo3V9yBH6cISwxEI4J+NO5ggDaGEEHb0osY/R7MzeKc0bHURXQuZjMM4qD+bSJCKIuI3eNBQ==", "signatures": [{"sig": "MEUCIQDJfEWWpKsQJgbyTNnzTmPilyWlOcsXSysnSH6cwLf2LwIgR4MfoTFpVm1YPh3K+n+zDvya3D+jxg3GrMtg6DREPXs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5483, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGYXtCRA9TVsSAnZWagAA+bQP/3tr156J3/7+LjWQPpWQ\ngfSS9LcewQsywCKJubistG6mqAI8dMm/jMdq2jN3ZaaBx9c/PCP+Z4DF5hlJ\nsDGouTccwxSOPXijGFJyvkq8fQeMBBVcZcCHAjTzRudcYDM65zPsnqknIgfq\nQkPlJtUVXdyiax67Rk63xkNHgXliwlZf2JKXh+dPYPBfK4jzkJa2uDQ+mdn8\nAfue+jb+JsKbztnNzLaejrW/H2yFybP9DOp9wqb2rFBASwwSpmDJjNHG65Lk\niR9lDm+907E3yF8lPnTqaXGi48iELzdjxTIOS2+Ssclue3FfEXjmk/Ocxmys\nmjP2DaWqRNL//V2M18Mb8rGorlbX/VdSrE7fcJijnXzdfEwl4NoKwSRxbnLb\nCSdc6eUKFOHA5IaMxul85h3Nv7CJT1Pcb7jzfS+eLSIY/2QOgw39J7u/xokc\n+csXpVlE+mtrDUiclEbT8cbTMTudUTdXLgfeLS7zDi4txTB/kI1mwpfUhiIl\nmevIWi6xN1TImeIMrbLPVsFAPmsrho43/cVDxXyB5VEeMRfEoGSy8UE23yR3\navtleTcTAWvMO6EuA1i2xyR0xWUSbc+12qW1L6f2cukDBgCKz8ybw39C73iB\nUmUgYEIQBrOdRhoa2IOSuRmUehpSlJiHd4jINYx7U60F0XNqI9vGtZGxUxJF\ntccH\r\n=X9dQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2": {"name": "@webpack-cli/configtest", "version": "1.0.2", "peerDependencies": {"webpack": "4.x.x || 5.x.x", "webpack-cli": "4.x.x"}, "dist": {"shasum": "2a20812bfb3a2ebb0b27ee26a52eeb3e3f000836", "tarball": "https://registry.npmjs.org/@webpack-cli/configtest/-/configtest-1.0.2.tgz", "fileCount": 7, "integrity": "sha512-3OBzV2fBGZ5TBfdW50cha1lHDVf9vlvRXnjpVbJBa20pSZQaSkMJZiwA8V2vD9ogyeXn8nU5s5A6mHyf5jhMzA==", "signatures": [{"sig": "MEUCIQC2bbE1MYkc5rJMGiM1OctyBiA8+ZP8UQE/BjKd8VjsjQIgQtqGG/3PMYCb0hZq14BS8AWAlcUHfGeMhavT/fU1be4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4427, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgX1JHCRA9TVsSAnZWagAAqAEQAIqw8I1LlC7cicMyda0J\nGvFZrdAKvf/KZdPRUlI82r7fxh3+VbjS8+XFGGctO78ZaGUv9j/p23dYibhX\nrcHvVMl2/fZKvEa56e0XS1kRnghKkV6DJvmW8HTSEnlOQRoR5UkcBXQIJbk3\n4uxDlSJAbMGUrH3/qK+Jk3GRcNo/2CfaSCmm/ihdxsLnUObJolHJ+OdYQO7F\nlhppglWYQp/eWFfeLtPdVwudpx0YV+a/zDzgrYci3RfrPo1WZX3GKAWBfGHT\nM8tj/NQ5Fik43sUNHbzCvrj7uD4fBVq1cSQmVAqi4+EW6CZ8u3Bgy4d1cEkb\nU7BfNGcZMf5VrEUbJIE1zJSOF3mnq9rwcTuvn74hPFfoe8hWKvxkvvPzbP1k\nfIaTdQiV1RFwyyjUXmMHB32eFSa8fFvdI3Vui/uYtVmux07sqHT8vQOXm9QP\nhutwhY0V5E5YrFhpFhqH+TrNXTKnPIrSdtaV5PLgK92TAtKpupQ4fXS/45Ar\nQSRQBW6Xn8BWP+a3aRWQXWt1+ZOFbM/MQ97Qm1p9LISmfqBCEwW5+bT7g62j\n8ipRsonQBVoQ38AI5Aus7d7dT86ducom0pfJEsbFXD5CUGDy8OW8ePll9Nc2\n7AXzd617x+5fdvXtE++ap/qr+SB9Rtk9sQNvfnza1VEds/R6jbzvXAJ+YhEa\nNJBu\r\n=n2eM\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.3": {"name": "@webpack-cli/configtest", "version": "1.0.3", "peerDependencies": {"webpack": "4.x.x || 5.x.x", "webpack-cli": "4.x.x"}, "dist": {"shasum": "204bcff87cda3ea4810881f7ea96e5f5321b87b9", "tarball": "https://registry.npmjs.org/@webpack-cli/configtest/-/configtest-1.0.3.tgz", "fileCount": 6, "integrity": "sha512-WQs0ep98FXX2XBAfQpRbY0Ma6ADw8JR6xoIkaIiJIzClGOMqVRvPCWqndTxf28DgFopWan0EKtHtg/5W1h0Zkw==", "signatures": [{"sig": "MEYCIQCws4NgcbgyWJAWs8hYocbA05qDyhRBR5TQdC6Dj8HbgQIhAMCHiGkgBAvaR47WHnryjv2YIDH1nnTVWxx52g1fvgMp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5940, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgk+rsCRA9TVsSAnZWagAAnUUP/jvf1CgoEcz5HepG+5TH\nypBD9yOemAABY6E/y6nODzJT+RKzdzJSX5MsvO4TJHMdzG6F2roCBIhICChd\nJFx4b2Fa2hINo5FyZRA1qLcXysD1yE9DhxilpIodLE3CwFOc623GJhky6gCB\nE2LevuWvHX1a+iAoja4ijEsnBSTXHnVtOh6ja/XNBFj6ZvyiXGsaE3rZdaOJ\nfv0FhH/a3zaIrkXNwB1+MDoM5pp1TdGcXpqSgedSBn+moZxXRbWWmnMiz27G\nkulZh1Ovav54RDNSn7+EdoWOk6ZNq0Yfaa239YdxsEITF3rwyhgExcpZuOLF\nwUdsBxhD+KqqoRVDowhXvhx8+ewwV1kXLWkTvuGfG+nTkWRpwzZI88dAPjpe\niJvEYWUZdMDsjz/tpP35vg35nBLGPFSTl8Y3v9d7QB/vuumPmhc1aK85HzAh\n4nHS8BRgqXCqglKmrHPVx9tnkibDyZjRbgEbzaQGextqvbr3hjkCdrSo5bOw\nsMRmmXKWFKt864nNrJzTfwlJRPK3RFkjR23o1NHP4YENMg30Xz7GDBZHsnWG\nJEWZVlzjsR0URmdCN36Db+jrm/M9beMTsec9+Y3Ik6Urb5TYiv8gZ8ErLLos\nXPNY55TXXy0vBG1KQLEEPX5IOSivzAHlS2uZ2KeD6TiAdOqylPgw0isFqEx6\ngJtK\r\n=nB+S\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.4": {"name": "@webpack-cli/configtest", "version": "1.0.4", "peerDependencies": {"webpack": "4.x.x || 5.x.x", "webpack-cli": "4.x.x"}, "dist": {"shasum": "f03ce6311c0883a83d04569e2c03c6238316d2aa", "tarball": "https://registry.npmjs.org/@webpack-cli/configtest/-/configtest-1.0.4.tgz", "fileCount": 5, "integrity": "sha512-cs3XLy+UcxiP6bj0A6u7MLLuwdXJ1c3Dtc0RkKg+wiI1g/Ti1om8+/2hc2A2B60NbBNAbMgyBMHvyymWm/j4wQ==", "signatures": [{"sig": "MEQCIEc1bfQrDcfqgZWjH4/HLrIhx+Q2GzkdUlV7zWsbKWlpAiBnhPsZh5fLWKm5/n2VC1JTtbTM3/oDKamQC3Jwmplg7Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4382, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgvhoBCRA9TVsSAnZWagAAn/IP/RbUtKxMv46MD9eQkzN1\ni9jHFdBldhm6z9NfNpYtIl4MzixLfxPkfIOopVHNenFmG6MvUTZ1Vbqjak1p\n6K1O/JlnH69RMcSFfrAGpalWETyBxGu+9HP40A7+Vkbf2wSBprrYxk52iswD\n62BLGxDnxRAZsUVbk2dbdxfHJDIwCuuAbYq2n88+NmuWsG5ocTeS3WWkM6+9\nMB3ZwGn4sb0gpOYFXcqIrget9f+a2B7i1kdKp3Ecxgx8O0vLvUGdXNHCBcm4\nKaGnIZAvOcWgciScU5CE2PmmCHnAURbSXzvuiG1CGOVYFAUX+yeb4q5C0uVs\nPNwolTzHYftK4n4k/CfzX6GZRCiSJr56aPewkJckJpcnnwyd5/aFG0gab9c0\nqgVkck5ITnxL1nMSnFf5z0NvdM/nlxdkCpoYu9puhCxOQIZZ2uA82piui8/d\ngl7huhJiHTPnzvxQAsVpeLjZx6i9rVDfddk9f8kd/y8wPRyFu0SsX5UGfi7G\nkD3cZg/5RIP04bx069hTTbNlAZQkgDjs7um/aFVXZhVe3cCBIk8Ry4+wXnjA\n1wFIrol9lpSvJYr4H/Kb78EMlLXoQ22R1swbpKPzQ12aoSzcZLZycjF839fs\nsUqheyffExlmAM6LFgI8dD3EPgYaSm9iV/cpI1o21n+9BJ4cnjVpWPak8BtG\nrx/P\r\n=Bjhu\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.0": {"name": "@webpack-cli/configtest", "version": "1.1.0", "peerDependencies": {"webpack": "4.x.x || 5.x.x", "webpack-cli": "4.x.x"}, "dist": {"shasum": "8342bef0badfb7dfd3b576f2574ab80c725be043", "tarball": "https://registry.npmjs.org/@webpack-cli/configtest/-/configtest-1.1.0.tgz", "fileCount": 4, "integrity": "sha512-ttOkEkoalEHa7RaFYpM0ErK1xc4twg3Am9hfHhL7MVqlHebnkYd2wuI/ZqTDj0cVzZho6PdinY0phFZV3O0Mzg==", "signatures": [{"sig": "MEUCIACbF5VTF8WQT3hfbfER2sEof8suZX6FA+B9COL2y7CyAiEA3x+73ZJaFMV+TbcvxRRajM18WmR+Fe0JXORZaziN5+s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3401, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2hJBCRA9TVsSAnZWagAA5yMP/1QNqedQD25DkNnfYR0j\nuDl+Ua6ZCN+7WbMiv2fiHMd+Z69fnjIlGWcouvGDi7+MLOkFpDW8UW1YndcU\nEw1jjVLHuSZGwANhWrwQ1XOXOFOB9qN9HJgyZfPrPUk187mhJSWmCcGd0fap\nIgmUqKqWoQ6vECLo75B8549m9OvVvdHy+p3XCS/V8dnKwTgm2AOqPBarbqcS\nugIZZC/XvSl6w27XFnXN5klCAhK2gwStRwT+kDISWx8ptTCk/duPtHUpi+Uk\n2b8oacDD1h4AHk3Cp2TlywuI7EhDLH015jRGTBeJphOMziHX1F7WQ3SYeQyJ\ncvhCWLjCoEKcdDhlwDnCsnO/PeoK7KqX7Io84tercINVDmjzd0MOVF9+Oa0F\n+5V5Zjztv4fzn3XipVwfMcKbGi1wDBOz35ahHq30RLrv9EE3YKhhWO3pmevB\nZbqwdiyXqZXewU+eisUbf0r6gh0Q+HqzG3jPK4XYB1B0IuVXtIsNSwju1UkX\nsxpFMVfFXIESGNxwCErKLbsJEKcoFvs+SiCCXLFLj1ILS+boEqr+FrZ4nxFD\nHzTRMwfvzCZn60GFSriqNg/z9GCFKS+Z9BGLutizHZCj2aE6Lf/bNQzbAtFI\nOnvsIYzcY4GW9Rk44ayZhC16LzyZqQIw9Hqi+mtRazn7wmAxx2W9CXIKB/VC\ntqmg\r\n=UnHM\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.1": {"name": "@webpack-cli/configtest", "version": "1.1.1", "peerDependencies": {"webpack": "4.x.x || 5.x.x", "webpack-cli": "4.x.x"}, "dist": {"shasum": "9f53b1b7946a6efc2a749095a4f450e2932e8356", "tarball": "https://registry.npmjs.org/@webpack-cli/configtest/-/configtest-1.1.1.tgz", "fileCount": 4, "integrity": "sha512-1FBc1f9G4P/AxMqIgfZgeOTuRnwZMten8E7zap5zgpPInnCrP8D4Q81+4CWIch8i/Nf7nXjP0v6CjjbHOrXhKg==", "signatures": [{"sig": "MEYCIQDQariPSHz+n+oCn5v8neNR/Lt6UsQ0WISo24m06uWrRwIhAJPeyLkGD7IKw8dXmhzbMUb97nITlxcMCscPObIKXgv6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3401, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh7qi+CRA9TVsSAnZWagAAP4EP/RwpnZ7DrkPJi5tnmdS7\nVpKUiWHfUTwz2qo6Y57ZJhiWw1IsJFvjnxZgIennKSxqphWMjryuifGQ+ytH\na3YBN2jjyyQBHoPwv2GSIb4laD7H7GKCoSaJBlnnncY3s+c4/EIFkYXK4BVt\nqJXZBUv4csuDNuO3gOw9gEQ+JQQQYGQZZHcFZllNuAgpkrpF6KaBA4hPlMiz\nndAKuIRlqYAZX6CodrY/qHzuXs5KqUYHu0MmC0OBpdRgDTjdKLnkRaS/HW7j\n2asG+UyrH628e/U7WJkVJP+5Ump14+FP3ChLO6nW3WC5cFhmU212xwihkobQ\n3F1BXMbiirbNoLEHTtA04GY79i3Lr/iM0q5aYHvqFzdZZ4HBspAQw19vig80\nudkIJSEcgCbZZ2YXnzPWlkvlNwWKC2lOhLVYLw/EztoHHEd1GB0PO902xARp\n5AeOY5IiRBrISIxulxdX/gbMcA2hKSUtY10DLcJRQWtVK9pq/aTS6N5ZKR9k\nZ4iPZBg9Ih2jPTZV0aMRN0Mwa1lxONT8YR+032QueuiRF05tadOkDYIqiBwW\nZHeHAWozX86a1BDEZOFisVaE8iR8egzaCSVy1WN/ftociNWIjQQOks161S2+\ncWtbrylNLz1mbW7Dmt2//l9GPAlnKB9oyJuYWv4wYqIbb2D4c988JwQvcz/3\n1bVv\r\n=Eb5Z\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.2.0": {"name": "@webpack-cli/configtest", "version": "1.2.0", "peerDependencies": {"webpack": "4.x.x || 5.x.x", "webpack-cli": "4.x.x"}, "dist": {"shasum": "7b20ce1c12533912c3b217ea68262365fa29a6f5", "tarball": "https://registry.npmjs.org/@webpack-cli/configtest/-/configtest-1.2.0.tgz", "fileCount": 5, "integrity": "sha512-4FB8Tj6xyVkyqjj1OaTqCjXYULB9FMkqQ8yGrZjRDrYh0nOE+7Lhs45WioWQQMV+ceFlE368Ukhe6xdvJM9Egg==", "signatures": [{"sig": "MEQCIAjtXl3MT45ZpvlPeiHwskxcCaHMPZ10VS0IMefqhUF6AiB68r6CqPUA3ZGo+92hMWW0gA/pRyzVFi/U4q6HkP4UAA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4519, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJipodgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqCPBAAn4F8edb5/cwtl8G59A5XUGhfSu16h8kxP6hnpLIfYW+/jFpo\r\nmSbw1fRCFyOzQeoGKGPiezQXEJzz7twRn53AOX6qsFfl4acyAj/emdGnUcqp\r\niNR06yxrhDI+0b3BF2SRGr9ETkDXUtrU1x+YEw8sgz4S7Pkf+aqawmyv6EQy\r\nYmjdl2Cdn0LAP/9YgtBG31o9rDJ0lUiZDYr7KQ66PUrXs1WftDAkYP84vwuj\r\nFqKUxaf1bjTclBLIPnp2UBkpR8b3Op8T0lM8HM+fRlpdHg3GwDLrHdBH9kR+\r\nyfgauZTUem+AcP8wCe2ROde/UODIy8JPH/4UjyWjdc853K/jxgIvBwt7oDdd\r\npBliIqgxd24wndRO2VujcIn/FKvC7zDiUxlJu9Vd0W0ixtm9U7P9CM4RiX9l\r\ngl4X1cgBV/TwoVHW/AaVsBW5lcFdbFNGoE0HnGBhet+B/ozGyrBoN4MkBCms\r\nvVDQrzCZ6BJI6Rp1tmaX0eqVRIBwv5s/VWoj5OVAf3ViEQpC/FVbmr7vNhC9\r\nq0N6RnDIoUz8376CZ1cU//AtSDT5MM5Wx3Q3BT035qOar2HE2vntvujyb0+E\r\nMF95jBaPSfZpDD6nvK8OzvkoWHYKQc4lJCtC5Ab2pUgx72cw0VoiXlHxftdn\r\nCx4EP5y64hbPsv+o4yrU2iDUmh05uKpJliE=\r\n=z4o2\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0": {"name": "@webpack-cli/configtest", "version": "2.0.0", "peerDependencies": {"webpack": "5.x.x", "webpack-cli": "5.x.x"}, "dist": {"shasum": "5e1bc37064c7d00e1330641fa523f8ff85a39513", "tarball": "https://registry.npmjs.org/@webpack-cli/configtest/-/configtest-2.0.0.tgz", "fileCount": 5, "integrity": "sha512-war4OU8NGjBqU3DP3bx6ciODXIh7dSXcpQq+P4K2Tqyd8L5OjZ7COx9QXx/QdCIwL2qoX09Wr4Cwf7uS4qdEng==", "signatures": [{"sig": "MEUCIQDi+QEWH3g/0MMdnS35b4Spfgb8Wf6OGmPRH1YZNOO5lQIgNDinKXcZ7Ew9bXU/tsmFx9yD7OUeel3kw1crDvPBIMI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4303, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjdbmwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoTkA/+NQ0Q9arSOuidvxFFfQQ9hAOb1/8TCuWX6D5gCO/+esEmWayw\r\ntAxorGtBMXPhPlBGCNOgSGSAvZz2dcKymNq8zI/epljITSrceHwrmkxgqPrq\r\ndrn625GsCMIbBqpaY7qfGzuXFxBIXcVdw9Brt5Ikurka/xi4V/QHfeCCfda/\r\nKH3ZKc4EMbia2dpeNsrYS/k/QATSAwBc+AQUdhyQaFWjcUl6xxG8WtYGMx/e\r\n4ocxe5e0iPsVVGTWzsGBfKqeBR03Ylr3oPt9sbTV4WFdrxbvzdIEked+3Dgx\r\nwBEhjDY0yzHQm4zWqMt+88qza1ZIl32+zbDAxRyhKMjdwnsMKclgaxzIqcrY\r\nQgMFC2o1ClGItZZOtKW5gq9Bdi3fd4G2yhoJ8Qdr4Bt7x1fiQOms/6QGoeQs\r\nqv7p24lTgrSGcLs08vt0aikg/NqPKc0hOOGhKo6KdiYl38yehrsLtccrs8wM\r\nwU0Y5OjW7YZWOgYFYJx8y/a4pDBfsFEg9oV2QFqw0OASRb5geXkLyeKiupte\r\nyOHFhCeLHb77Y2hTBqSOu++Gi3tY8MIF2ZJE/Pu0ykET9kFhcC3LNWixrSV0\r\ntx9MyRtIFbJ29agH3rKY8BOL5pR1jGNg2FeLCgXzHDMlAE6n3XuK9qU4rPG0\r\nwEaOEHsmAzoH2zma/9G9SbfmesZrBe/EbZo=\r\n=Xyij\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=14.15.0"}}, "2.0.1": {"name": "@webpack-cli/configtest", "version": "2.0.1", "peerDependencies": {"webpack": "5.x.x", "webpack-cli": "5.x.x"}, "dist": {"shasum": "a69720f6c9bad6aef54a8fa6ba9c3533e7ef4c7f", "tarball": "https://registry.npmjs.org/@webpack-cli/configtest/-/configtest-2.0.1.tgz", "fileCount": 5, "integrity": "sha512-njsdJXJSiS2iNbQVS0eT8A/KPnmyH4pv1APj2K0d1wrZcBLw+yppxOy4CGqa0OxDJkzfL/XELDhD8rocnIwB5A==", "signatures": [{"sig": "MEYCIQCbGipeFrdE9M4kzJqCzYs70jJ2O2rglp33REtYRSi/HgIhAIK89ie6iS2T6Gv0DQCAY3Lf6Ij59r7euLb8grz0QgI9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4303, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjjitvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoYIA/7BeGkU+IMDSxnFLUt3hnxV377WjZRTmEIstkkcoOkfEDa4wmF\r\n0babKcpNwv3yQ25yDEUV095CohdV1FWoKLcKSVeT3gw9V8fzhTi52OdNq3ss\r\nnO0Fqi+EM5coHY6ha0wCQAZ5kOEhdDGMXIO2kmdwvseRDeOWLJxI7MvfN8z6\r\n4hjuNH+zney7AJpnENgeutZ2JK8vU0oq6no0Mh6sqVIglOXqNXjDcM0E8bMN\r\ndxrPsF81/UB8hp99SD0QWm6haQZJuwk7n2G8hbn/4mlyyvdrMS/W94RH2OLs\r\n4OQL55JcCOkrw+Tr4P64mDgjdQac75ppQROwbry6gMn0Na125H+J5BdgzfmP\r\nEeubyKkeki85lcVYp9TmFQj9SSISfDVHFH9ygROp9uq+Zi1ZwUihsfjmYB2z\r\nGT5IOP5uZ9EW1iulbn1HOywor8I2hFs/AUmrlSZFeMXlrR0EF9SZr7MIlmI6\r\nHkczhi5ZocXW1xqAg8/B5J16MnFtQ3IC2uyJ0iUFahlr0mck1RheqJ10C39A\r\nTKRCHtsjw3WBe/hD+MNIbZ6u2ctzGIkynbFUnys4pDgLmdPFFieOwxMi0lLa\r\nrNvZn8YHQ+FZmQjPDo61jGP11uiP14SiXop0pLBXVk7hkd+yNkzfUotZv+bR\r\nXG/Qa8Y8aIS4QrFxCpsqeGzqlGLst5RdANE=\r\n=yyo6\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=14.15.0"}}, "2.1.0": {"name": "@webpack-cli/configtest", "version": "2.1.0", "peerDependencies": {"webpack": "5.x.x", "webpack-cli": "5.x.x"}, "dist": {"shasum": "b59b33377b1b896a9a7357cfc643b39c1524b1e6", "tarball": "https://registry.npmjs.org/@webpack-cli/configtest/-/configtest-2.1.0.tgz", "fileCount": 5, "integrity": "sha512-K/vuv72vpfSEZoo5KIU0a2FsEoYdW0DUMtMpB5X3LlUwshetMZRZRxB7sCsVji/lFaSxtQQ3aM9O4eMolXkU9w==", "signatures": [{"sig": "MEUCIAW6YmUVoEOIZeXT+z65gONQObN5Z+Fkjh1MVSkHf2vwAiEA8HsIk4P5OS/4W8G2gFOjkRK8vOSVt2XrxaA1udhmvwA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4545}, "engines": {"node": ">=14.15.0"}}, "2.1.1": {"name": "@webpack-cli/configtest", "version": "2.1.1", "peerDependencies": {"webpack": "5.x.x", "webpack-cli": "5.x.x"}, "dist": {"shasum": "3b2f852e91dac6e3b85fb2a314fb8bef46d94646", "tarball": "https://registry.npmjs.org/@webpack-cli/configtest/-/configtest-2.1.1.tgz", "fileCount": 5, "integrity": "sha512-wy0mglZpDSiSS0XHrVR+BAdId2+yxPSoJW8fsna3ZpYSlufjvxnP4YbKTCBZnNIcGN4r6ZPXV55X4mYExOfLmw==", "signatures": [{"sig": "MEYCIQChsBAhS5Nbs+eR4zKwwpJUAOo5hpic9hNKTGKCGMhDvwIhAPrTsE6NrsEoX1Sx7hsDJ/8ThmK7MJAmkADtMDKbWa6b", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4550}, "engines": {"node": ">=14.15.0"}}, "3.0.0": {"name": "@webpack-cli/configtest", "version": "3.0.0", "peerDependencies": {"webpack": "5.x.x", "webpack-cli": "5.x.x"}, "dist": {"shasum": "40a18b301e255ca8b90338cb82bbf8b506ee54b1", "tarball": "https://registry.npmjs.org/@webpack-cli/configtest/-/configtest-3.0.0.tgz", "fileCount": 5, "integrity": "sha512-3byRXqOvwm/zGM0OhSbq15aJeX5ZUSe0RS7gfzH9wtX9UX6foShghZKxNOq+oJ59s5dsZrvBk4WHLfSnaBJJWw==", "signatures": [{"sig": "MEYCIQC1jAZ7um+76ACtTCJjJntLyt+XqkKkIl7ifMfNB7RK1AIhAIP2uHwP0oJvDj25nLVyDspvgzI0YNL2soycD9na8ZrV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4550}, "engines": {"node": ">=18.12.0"}}, "3.0.1": {"name": "@webpack-cli/configtest", "version": "3.0.1", "peerDependencies": {"webpack": "^5.82.0", "webpack-cli": "6.x.x"}, "dist": {"integrity": "sha512-u8d0pJ5YFgneF/GuvEiDA61Tf1VDomHHYMjv/wc9XzYj7nopltpG96nXN5dJRstxZhcNpV1g+nT6CydO7pHbjA==", "shasum": "76ac285b9658fa642ce238c276264589aa2b6b57", "tarball": "https://registry.npmjs.org/@webpack-cli/configtest/-/configtest-3.0.1.tgz", "fileCount": 5, "unpackedSize": 4552, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCmpTayuGUPdKMlZ7L+/sbXp/YplxeRiZIr9s08sgk8IgIhAKiG/MQorDi2aeXETYhLG/LTKjaiV8jgjyOXjgDd32ge"}]}, "engines": {"node": ">=18.12.0"}}}, "modified": "2024-12-20T14:12:07.461Z"}