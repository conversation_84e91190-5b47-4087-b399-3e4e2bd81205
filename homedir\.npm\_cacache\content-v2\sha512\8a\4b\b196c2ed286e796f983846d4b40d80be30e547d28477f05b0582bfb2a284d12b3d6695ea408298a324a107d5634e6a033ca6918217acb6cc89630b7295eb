{"_id": "process-nextick-args", "_rev": "16-ec16db120748320a07e76e5ede14ae0d", "name": "process-nextick-args", "description": "process.nextTick but always with args", "dist-tags": {"latest": "2.0.1"}, "versions": {"1.0.0": {"name": "process-nextick-args", "version": "1.0.0", "description": "process.nextTick but always with args", "main": "index.js", "scripts": {"test": "node test.js | tspec"}, "repository": {"type": "git", "url": "https://github.com/calvinmetcalf/process-nextick-args.git"}, "author": "", "license": "MIT", "bugs": {"url": "https://github.com/calvinmetcalf/process-nextick-args/issues"}, "homepage": "https://github.com/calvinmetcalf/process-nextick-args", "devDependencies": {"tap-spec": "^3.0.0", "tape": "^4.0.0"}, "gitHead": "3305d2f1aef9bc8a061a3958a9e76a72db307e4d", "_id": "process-nextick-args@1.0.0", "_shasum": "287028923c8d1626558765564696ae8a3c124d53", "_from": ".", "_npmVersion": "2.0.0", "_npmUser": {"name": "cwmma", "email": "<EMAIL>"}, "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}], "dist": {"shasum": "287028923c8d1626558765564696ae8a3c124d53", "tarball": "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-1.0.0.tgz", "integrity": "sha512-N8O1F26Iz3xSwQACld6YI98x00xZN2MTqzAQZiDQ4eGA9xUxEtJ0btlUKIcRkAGEMviKFJZmZSUIjqiI6p/XHg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD1A5OLPIy3gunKWy2RSvcuCpLXN1mq8HMMTE3GV4Df6gIgFckI2KG0ISckPl6INtn4DXTcVEYcZjza5SYi/LVg3Oc="}]}, "directories": {}}, "1.0.1": {"name": "process-nextick-args", "version": "1.0.1", "description": "process.nextTick but always with args", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "https://github.com/calvinmetcalf/process-nextick-args.git"}, "author": "", "license": "MIT", "bugs": {"url": "https://github.com/calvinmetcalf/process-nextick-args/issues"}, "homepage": "https://github.com/calvinmetcalf/process-nextick-args", "devDependencies": {"tap": "~0.2.6"}, "gitHead": "b7c95b21096503e76a1b7f4f60920d32d8378eeb", "_id": "process-nextick-args@1.0.1", "_shasum": "918a5ab4a7744340b83ff416101ba53c5c531879", "_from": ".", "_npmVersion": "2.0.0", "_npmUser": {"name": "cwmma", "email": "<EMAIL>"}, "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}], "dist": {"shasum": "918a5ab4a7744340b83ff416101ba53c5c531879", "tarball": "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-1.0.1.tgz", "integrity": "sha512-Ab720IphAxz10NEZwd2+eL463i8av0CySnTrUON17lrOD6G5W3C0mKesEZB2AiNHk0y9Mi2+lk5ma8zk/1t1wg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDUnCGV+dc5D2bOhQAyUT+j8l8xgH1quReFL9vcNoEkuAiEAvK5/UT7Gb0LKrrSBY+aBj7iQLc8ihKwIsMCXEaT0wFc="}]}, "directories": {}}, "1.0.2": {"name": "process-nextick-args", "version": "1.0.2", "description": "process.nextTick but always with args", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "git+https://github.com/calvinmetcalf/process-nextick-args.git"}, "author": "", "license": "MIT", "bugs": {"url": "https://github.com/calvinmetcalf/process-nextick-args/issues"}, "homepage": "https://github.com/calvinmetcalf/process-nextick-args", "devDependencies": {"tap": "~0.2.6"}, "gitHead": "295707643b4ed6667c1afb71ffb6101669b5dac2", "_id": "process-nextick-args@1.0.2", "_shasum": "8b4d3fc586668bd5b6573e732edf2b71c1c1d8aa", "_from": ".", "_npmVersion": "2.11.1", "_nodeVersion": "2.3.0", "_npmUser": {"name": "cwmma", "email": "<EMAIL>"}, "dist": {"shasum": "8b4d3fc586668bd5b6573e732edf2b71c1c1d8aa", "tarball": "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-1.0.2.tgz", "integrity": "sha512-CcRyYh3eFOInWZ0HwkaFqBCtoU/qviOKzC9btTs1myoqJsypJqHMco/JjagnGfwQ9pbvSnLDISUiQpZe3GlhxA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC3fgFyEvhR2XkqfHnNmS8dyRFYjzcJNXwoPZxEe9ORkgIhAKWmIg5f1W4DGPBz44fljeRgOSwRbcT51BAwD/uZUCnK"}]}, "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}], "directories": {}}, "1.0.3": {"name": "process-nextick-args", "version": "1.0.3", "description": "process.nextTick but always with args", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "git+https://github.com/calvinmetcalf/process-nextick-args.git"}, "author": "", "license": "MIT", "bugs": {"url": "https://github.com/calvinmetcalf/process-nextick-args/issues"}, "homepage": "https://github.com/calvinmetcalf/process-nextick-args", "devDependencies": {"tap": "~0.2.6"}, "gitHead": "e855846a69662b9489f1ad3dde1ebf2ccc4370b8", "_id": "process-nextick-args@1.0.3", "_shasum": "e272eed825d5e9f4ea74d8d73b1fe311c3beb630", "_from": ".", "_npmVersion": "2.9.0", "_nodeVersion": "2.5.0", "_npmUser": {"name": "cwmma", "email": "<EMAIL>"}, "dist": {"shasum": "e272eed825d5e9f4ea74d8d73b1fe311c3beb630", "tarball": "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-1.0.3.tgz", "integrity": "sha512-HisaXRBQios6xPfBmCe9hkxbinGzyZ/C3JuOzL7gf/gjhg/AEF33agxGs8irDF3QI+bos2WfPT671uzjJo9SoA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDhDXSU6l7ho9yZx3AVOlLp6agpb4fNsnmRpcJ/YoL5LAiBQLAmJcsaD4gL9DC0TN1SPIPLFs/1YVtjOhTRWFMIYYg=="}]}, "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}], "directories": {}}, "1.0.4": {"name": "process-nextick-args", "version": "1.0.4", "description": "process.nextTick but always with args", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "git+https://github.com/calvinmetcalf/process-nextick-args.git"}, "author": "", "license": "MIT", "bugs": {"url": "https://github.com/calvinmetcalf/process-nextick-args/issues"}, "homepage": "https://github.com/calvinmetcalf/process-nextick-args", "devDependencies": {"tap": "~0.2.6"}, "gitHead": "a3128dc05e1a16c905257e34ee26c696412de545", "_id": "process-nextick-args@1.0.4", "_shasum": "7890ef9b7f70e1200ff716c22fc65d09803dfdd4", "_from": ".", "_npmVersion": "2.14.4", "_nodeVersion": "4.1.1", "_npmUser": {"name": "cwmma", "email": "<EMAIL>"}, "dist": {"shasum": "7890ef9b7f70e1200ff716c22fc65d09803dfdd4", "tarball": "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-1.0.4.tgz", "integrity": "sha512-Nnxn19QCg1SDzj7R1s5QVoojkBmigi/5Eqf4Sa+gyg3nes00RZ9lGnvejS55u5qVPITQII8GwLWr3bSW8m8pJQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB2WIwnAXW8xg7OlCOLr8o+aqtky9wZnx2HDlkYzHORSAiBsR0wYJQ/Rre8ZsulaXQLZICND4qCuZyyIiEnYj8QeQg=="}]}, "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}], "directories": {}}, "1.0.5": {"name": "process-nextick-args", "version": "1.0.5", "description": "process.nextTick but always with args", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "git+https://github.com/calvinmetcalf/process-nextick-args.git"}, "author": "", "license": "MIT", "bugs": {"url": "https://github.com/calvinmetcalf/process-nextick-args/issues"}, "homepage": "https://github.com/calvinmetcalf/process-nextick-args", "devDependencies": {"tap": "~0.2.6"}, "gitHead": "c5c594df85299636b95a49fa24b4ad34120a9978", "_id": "process-nextick-args@1.0.5", "_shasum": "aec23defcb8e82f50c81aefa206661b67f3f6ee5", "_from": ".", "_npmVersion": "2.14.4", "_nodeVersion": "4.1.1", "_npmUser": {"name": "cwmma", "email": "<EMAIL>"}, "dist": {"shasum": "aec23defcb8e82f50c81aefa206661b67f3f6ee5", "tarball": "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-1.0.5.tgz", "integrity": "sha512-IGFdM0B3vs41w9QP38NatGERxq01SCzUkFGgxNskM+vEeM/DnnQ7SJ1n8B59O/CFlrq8qqAluuobMnDecqHxug==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDKKOi0wxugkIqDsgoWNL114ySS2xlArNKMKUivWPd8cwIhAJnJo43+z9bpp8FCDOEAtpQ5nNNPs16e7JQ6dwfeY0pD"}]}, "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}], "directories": {}}, "1.0.6": {"name": "process-nextick-args", "version": "1.0.6", "description": "process.nextTick but always with args", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "git+https://github.com/calvinmetcalf/process-nextick-args.git"}, "author": "", "license": "MIT", "bugs": {"url": "https://github.com/calvinmetcalf/process-nextick-args/issues"}, "homepage": "https://github.com/calvinmetcalf/process-nextick-args", "devDependencies": {"tap": "~0.2.6"}, "gitHead": "e85787b05a8c3c1adb714f332d822e9162699c78", "_id": "process-nextick-args@1.0.6", "_shasum": "0f96b001cea90b12592ce566edb97ec11e69bd05", "_from": ".", "_npmVersion": "2.14.4", "_nodeVersion": "4.1.1", "_npmUser": {"name": "cwmma", "email": "<EMAIL>"}, "dist": {"shasum": "0f96b001cea90b12592ce566edb97ec11e69bd05", "tarball": "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-1.0.6.tgz", "integrity": "sha512-94OochEFu6/hLhOK4cpk2+CU5xVLmdimAnwrjPbb5VLct879euAq+I3mHUfP8u3abmdl2BLRX3oqEUvg5PBsag==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGLIO7YlxQv37Seab6RTqXxpHodlYAyBuNNLCssIZZmrAiAyTeiZajBGaeHciORTLMPzNa7w2DjJSasPKADWuN74mw=="}]}, "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}], "directories": {}}, "1.0.7": {"name": "process-nextick-args", "version": "1.0.7", "description": "process.nextTick but always with args", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "https://github.com/calvinmetcalf/process-nextick-args.git"}, "author": "", "license": "MIT", "bugs": {"url": "https://github.com/calvinmetcalf/process-nextick-args/issues"}, "homepage": "https://github.com/calvinmetcalf/process-nextick-args", "devDependencies": {"tap": "~0.2.6"}, "gitHead": "5c00899ab01dd32f93ad4b5743da33da91404f39", "_id": "process-nextick-args@1.0.7", "_shasum": "150e20b756590ad3f91093f25a4f2ad8bff30ba3", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "5.11.0", "_npmUser": {"name": "cwmma", "email": "<EMAIL>"}, "dist": {"shasum": "150e20b756590ad3f91093f25a4f2ad8bff30ba3", "tarball": "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-1.0.7.tgz", "integrity": "sha512-yN0WQmuCX63LP/TMvAg31nvT6m4vDqJEiiv2CAZqWOGNWutc9DfDk1NPYYmKUFmaVM2UwDowH4u5AHWYP/jxKw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGq5zdkp8tRRtkR6ZKtlfNY4dLk1j+Q2gs1cF7ivSBcfAiEAtRzOYNgL60ZpxRTPM1nBW2EwEVOoCyJY4yKvEwNpn5E="}]}, "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/process-nextick-args-1.0.7.tgz_1462394251778_0.36989671061746776"}, "directories": {}}, "2.0.0": {"name": "process-nextick-args", "version": "2.0.0", "description": "process.nextTick but always with args", "main": "index.js", "files": ["index.js"], "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "git+https://github.com/calvinmetcalf/process-nextick-args.git"}, "author": "", "license": "MIT", "bugs": {"url": "https://github.com/calvinmetcalf/process-nextick-args/issues"}, "homepage": "https://github.com/calvinmetcalf/process-nextick-args", "devDependencies": {"tap": "~0.2.6"}, "gitHead": "8407900951af3e7651fca50f127f0e4ddc01ad55", "_id": "process-nextick-args@2.0.0", "_npmVersion": "5.5.1", "_nodeVersion": "8.6.0", "_npmUser": {"name": "cwmma", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-MtEC1TqN0EU5nephaJ4rAtThHtC86dNN9qCuEhtshvpVBkAW5ZO7BASN9REnF9eoXGcRub+pFuKEpOHE+HbEMw==", "shasum": "a37d732f4271b4ab1ad070d35508e8290788ffaa", "tarball": "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-2.0.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDYCakzU8nCihXHOgnJX1qKfL/Eaai6RTXXM5yUBWfVVgIgEOWkDelhPx/IN9pRrcFz5kEVUcZtSjXml5LH29bzJwY="}]}, "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/process-nextick-args-2.0.0.tgz_1513027423688_0.4807069287635386"}, "directories": {}}, "2.0.1": {"name": "process-nextick-args", "version": "2.0.1", "description": "process.nextTick but always with args", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "git+https://github.com/calvinmetcalf/process-nextick-args.git"}, "author": "", "license": "MIT", "bugs": {"url": "https://github.com/calvinmetcalf/process-nextick-args/issues"}, "homepage": "https://github.com/calvinmetcalf/process-nextick-args", "devDependencies": {"tap": "~0.2.6"}, "gitHead": "b96d59913025441b00c4fd40e6894ddfa8e1c398", "_id": "process-nextick-args@2.0.1", "_nodeVersion": "10.15.0", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag==", "shasum": "7820d9b16120cc55ca9ae7792680ae7dba6d7fe2", "tarball": "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-2.0.1.tgz", "fileCount": 4, "unpackedSize": 3175, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdCpxfCRA9TVsSAnZWagAA2wUQAJE/zQojeYBtJTNHDGq7\ntlqxR0qbiugyY02+BV/s6cTIRG7uCAwpyWelDotgFeE5IfxoLNOLuRtc5y5w\nZr/xlgT5KxchI87af3FpNTB2Y/0+lKDjbSdj0k+Mz8IjxwUtBdt/8yqH89EF\nxOE+H9WCjL94tqDrnaPAXfIo84m0FLUA5hneNrJqmhXGfC/a7ncO5x1AmBDj\nPGV0pyS4XxuUEsV2KSx2Fy2pXxsI01mxq7nAeOwNPb20XbPbGhzTU9eLOzAJ\nMggd+bKbuDdxCFdf/CQ76d9Gz2T6fxjHNsKrw7AYcfrTi6bLQQKBpeN5KE7i\nagIYpzq2HWsjhrClsm1TQNbV8SWQR2OhbgDdMKn8OkIsvQGKJpJbW6I+m1va\nTd4Gc9i1dIweSjvS2g/iaVdm2E0EWhU9tSH7SnKKmEOBCsWsGW5XJQP7W+sO\nKuxicRik6vCGyaMrZz83blayXuXGWPSP9uDt0/2MINRefJTTusWaSikZxyQn\nZ0uqV9Li6WEEWALHQf3NtlAq49U0w/NN9jRPI2iViPdf4Q/cyf/Fwp5PmVKt\nFYx9V7me9XNCxu1kBlx7ZkbZW0M0eEuLYW8J2hNMiJjuksUr0mOlXjSKwMGf\nru2wMy7dCU+zVeA65QtGPSSfMOrATVzjbRWuXkR3xAmv3bJmbMIn1IG/rtSr\nnc/W\r\n=K8U9\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCOwa1vxlMRzglu9VqYiur+kmrXHC8yoYGD2ANfi2H3dwIgHG/r+i0L9odxlWqun4/CbmFJLvV6DNrrFUP0vjUBy20="}]}, "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}], "_npmUser": {"name": "cwmma", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/process-nextick-args_2.0.1_1560976479045_0.30434079670500513"}, "_hasShrinkwrap": false}}, "readme": "process-nextick-args\n=====\n\n[![Build Status](https://travis-ci.org/calvinmetcalf/process-nextick-args.svg?branch=master)](https://travis-ci.org/calvinmetcalf/process-nextick-args)\n\n```bash\nnpm install --save process-nextick-args\n```\n\nAlways be able to pass arguments to process.nextTick, no matter the platform\n\n```js\nvar pna = require('process-nextick-args');\n\npna.nextTick(function (a, b, c) {\n  console.log(a, b, c);\n}, 'step', 3,  'profit');\n```\n", "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}], "time": {"modified": "2022-06-24T17:00:59.196Z", "created": "2015-05-21T22:06:18.527Z", "1.0.0": "2015-05-21T22:06:18.527Z", "1.0.1": "2015-05-21T22:21:41.256Z", "1.0.2": "2015-07-16T12:03:58.178Z", "1.0.3": "2015-09-09T18:43:39.402Z", "1.0.4": "2015-12-02T00:20:19.202Z", "1.0.5": "2015-12-02T10:37:54.526Z", "1.0.6": "2015-12-03T01:12:25.011Z", "1.0.7": "2016-05-04T20:37:34.467Z", "2.0.0": "2017-12-11T21:23:44.634Z", "2.0.1": "2019-06-19T20:34:39.252Z"}, "homepage": "https://github.com/calvinmetcalf/process-nextick-args", "repository": {"type": "git", "url": "git+https://github.com/calvinmetcalf/process-nextick-args.git"}, "bugs": {"url": "https://github.com/calvinmetcalf/process-nextick-args/issues"}, "license": "MIT", "readmeFilename": "readme.md", "users": {"bret": true, "mojaray2k": true, "vivek.vikhere": true}}