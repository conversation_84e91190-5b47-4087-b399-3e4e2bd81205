{"_id": "<PERSON><PERSON><PERSON>", "_rev": "60-ef0e9bf78742a68c5336400186b787df", "name": "<PERSON><PERSON><PERSON>", "description": "A light, featureful and explicit option parsing library.", "dist-tags": {"latest": "2.0.0"}, "versions": {"1.0.0": {"name": "<PERSON><PERSON><PERSON>", "description": "Yet another node.js option parsing library", "version": "1.0.0", "author": {"name": "<PERSON>", "url": "trentm.com"}, "repository": {"type": "git", "url": "git://github.com/trentm/node-dashdash.git"}, "main": "./lib/dashdash.js", "dependencies": {"assert-plus": "0.1.2"}, "devDependencies": {"nodeunit": "0.7.4"}, "engines": {"node": ">=0.8"}, "_id": "dashdash@1.0.0", "dist": {"shasum": "34f5e36c7dc2e32351fbeef2f2829ad121f2a601", "tarball": "https://registry.npmjs.org/dashdash/-/dashdash-1.0.0.tgz", "integrity": "sha512-xXgr2xERFOw5jGnLrgRxbWc06CuMd79CmZNXFLTnJfsCHtDX6y1qizLRqaEy95waYTL1pri9XV90J3oGeKAi6Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDfBiFPgCg4ixNn/a4DUDZu4Cy0diUaCA1n187EXJdCDwIhAPIRe7oq9NRt877T/Z0ajV+pqnKnspGetURHQb64wQrU"}]}, "_from": ".", "_npmVersion": "1.2.11", "_npmUser": {"name": "trentm", "email": "<EMAIL>"}, "maintainers": [{"name": "trentm", "email": "<EMAIL>"}], "directories": {}}, "1.0.1": {"name": "<PERSON><PERSON><PERSON>", "description": "Yet another node.js option parsing library", "version": "1.0.1", "author": {"name": "<PERSON>", "url": "trentm.com"}, "repository": {"type": "git", "url": "git://github.com/trentm/node-dashdash.git"}, "main": "./lib/dashdash.js", "dependencies": {"assert-plus": "0.1.2"}, "devDependencies": {"nodeunit": "0.7.4"}, "engines": {"node": ">=0.8"}, "_id": "dashdash@1.0.1", "dist": {"shasum": "6ea53c159b099173aae73faecc643b7d2a343013", "tarball": "https://registry.npmjs.org/dashdash/-/dashdash-1.0.1.tgz", "integrity": "sha512-KJWwsHAQL+JvtHeseaPCQlRQu3hN+2c2QkKRfQuXZXbxFKJApnxHZeJqD+Rl1n+2ogqLsUmQ8Wc6FgCc9M5CEw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICIwkYiq8y9rC0KUaq+m7vV4d5or+VuG3FBA3+oDCRsMAiEAi6j2f1V7K7gFDyjtQRSfssOhZI148PQudiLlDRkXrCw="}]}, "_from": ".", "_npmVersion": "1.2.11", "_npmUser": {"name": "trentm", "email": "<EMAIL>"}, "maintainers": [{"name": "trentm", "email": "<EMAIL>"}], "directories": {}}, "1.0.2": {"name": "<PERSON><PERSON><PERSON>", "description": "Yet another node.js option parsing library", "version": "1.0.2", "author": {"name": "<PERSON>", "url": "trentm.com"}, "repository": {"type": "git", "url": "git://github.com/trentm/node-dashdash.git"}, "main": "./lib/dashdash.js", "dependencies": {"assert-plus": "0.1.2"}, "devDependencies": {"nodeunit": "0.7.4"}, "engines": {"node": ">=0.8"}, "_id": "dashdash@1.0.2", "dist": {"shasum": "cb1f44d6d0f046435ab784e15217cf48b78b8381", "tarball": "https://registry.npmjs.org/dashdash/-/dashdash-1.0.2.tgz", "integrity": "sha512-pZQwoqCGhMtju1BAl5jgXTlMjRYNXzfk/2vFKfvQNv2BHD+L2mOLZmdRIlw9ugaqcKYkluyXZcgAZf7jA1Ta7g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC92fWesBo9z/ZUlJUJn18wORbt2SoOiAEb3qZMt+D+wAiA+Qipk6SSKgqwKG1zleyEdS79oKGZCo8bTDCO7j9wZnw=="}]}, "_from": ".", "_npmVersion": "1.2.11", "_npmUser": {"name": "trentm", "email": "<EMAIL>"}, "maintainers": [{"name": "trentm", "email": "<EMAIL>"}], "directories": {}}, "1.1.0": {"name": "<PERSON><PERSON><PERSON>", "description": "A light, featureful and explicit option parsing library.", "version": "1.1.0", "author": {"name": "<PERSON>", "url": "trentm.com"}, "repository": {"type": "git", "url": "git://github.com/trentm/node-dashdash.git"}, "main": "./lib/dashdash.js", "dependencies": {"assert-plus": "0.1.2"}, "devDependencies": {"nodeunit": "0.7.4"}, "engines": {"node": ">=0.8"}, "_id": "dashdash@1.1.0", "dist": {"shasum": "dad70e645851de3216d4cc43756610c79973b100", "tarball": "https://registry.npmjs.org/dashdash/-/dashdash-1.1.0.tgz", "integrity": "sha512-92M3L+xzyn2CqTfVziYP/ZH0eo3c4yI6c/+AoipNRHkPe2FL1zgS6sM5rkzvIcMtdQg7OuLNd0Ztxl0ElD+PLA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDsJ8ychBmQsF93yBPU8MDGosvqRVXyML6ldF0YZTIMcgIgMEX/8VtGhGsjDGbWLXjjLY0ZT3YABHRO719MQyTmLfs="}]}, "_from": ".", "_npmVersion": "1.2.11", "_npmUser": {"name": "trentm", "email": "<EMAIL>"}, "maintainers": [{"name": "trentm", "email": "<EMAIL>"}], "directories": {}}, "1.2.0": {"name": "<PERSON><PERSON><PERSON>", "description": "A light, featureful and explicit option parsing library.", "version": "1.2.0", "author": {"name": "<PERSON>", "url": "trentm.com"}, "repository": {"type": "git", "url": "git://github.com/trentm/node-dashdash.git"}, "main": "./lib/dashdash.js", "dependencies": {"assert-plus": "0.1.2"}, "devDependencies": {"nodeunit": "0.7.4"}, "engines": {"node": ">=0.8"}, "_id": "dashdash@1.2.0", "dist": {"shasum": "d617c4725e41d825400b637032bb0c42309e9a26", "tarball": "https://registry.npmjs.org/dashdash/-/dashdash-1.2.0.tgz", "integrity": "sha512-BTzZChko541ZQ9P8Zfq1DnAKUtezO36V/nrCji7vh5gcEmN6/trQMxSZ6fF96Eg8D5ooipZ+125t+QmyRCkrUg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDGBKqYTWBmHS72ig2pUBSIdLkA6hIyiEc6mip9PjTTOQIhAIS21XG7oDC2IVPAeSCkmuQXoM5XxwAKU7RtimS9UpOx"}]}, "_from": ".", "_npmVersion": "1.2.11", "_npmUser": {"name": "trentm", "email": "<EMAIL>"}, "maintainers": [{"name": "trentm", "email": "<EMAIL>"}], "directories": {}}, "1.2.1": {"name": "<PERSON><PERSON><PERSON>", "description": "A light, featureful and explicit option parsing library.", "version": "1.2.1", "author": {"name": "<PERSON>", "url": "trentm.com"}, "repository": {"type": "git", "url": "git://github.com/trentm/node-dashdash.git"}, "main": "./lib/dashdash.js", "dependencies": {"assert-plus": "0.1.2"}, "devDependencies": {"nodeunit": "0.7.4"}, "engines": {"node": ">=0.8"}, "_id": "dashdash@1.2.1", "dist": {"shasum": "6085257782585250e23ef8173a0b42ffbc8ca67a", "tarball": "https://registry.npmjs.org/dashdash/-/dashdash-1.2.1.tgz", "integrity": "sha512-psdR0zsNXvgBkAllMIb29MqUz8lUAy7VFngaWwxTI79Pqk7Uz3kFrZOtSKOzzvShX6Ws2gSJUza85b9KMpW0aA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDhl+BAmJ2eARQAvknIjKUSY4+uwjHJ2XoJuEWYQ0/7YgIgEbQZCOLz1QI7UzWskoFGpUlTWe+ejv1P9smJBvX6LkA="}]}, "_from": ".", "_npmVersion": "1.2.14", "_npmUser": {"name": "trentm", "email": "<EMAIL>"}, "maintainers": [{"name": "trentm", "email": "<EMAIL>"}], "directories": {}}, "1.3.0": {"name": "<PERSON><PERSON><PERSON>", "description": "A light, featureful and explicit option parsing library.", "version": "1.3.0", "author": {"name": "<PERSON>", "url": "trentm.com"}, "repository": {"type": "git", "url": "git://github.com/trentm/node-dashdash.git"}, "main": "./lib/dashdash.js", "dependencies": {"assert-plus": "0.1.2"}, "devDependencies": {"nodeunit": "0.7.4"}, "engines": {"node": ">=0.8"}, "_id": "dashdash@1.3.0", "dist": {"shasum": "99ae1d5b398f4b89b578d9dc0d56472414460b32", "tarball": "https://registry.npmjs.org/dashdash/-/dashdash-1.3.0.tgz", "integrity": "sha512-sSb/D2irrdtUyLWmbZYkifUO2STZWLnhtr7X9G51TvvemtS6oIrCYU+n8uDBq0NDRvQ1wzZ+Ia61O09bRgakpQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC4eHnwrSInGxENSMaH/BiXgj6WhEpbk8vJKxl1MAoaEwIhALpj0sFzz35UXDDSLP6Pmnh/1uEHIXxo8njfNSrd7Chi"}]}, "_from": ".", "_npmVersion": "1.2.14", "_npmUser": {"name": "trentm", "email": "<EMAIL>"}, "maintainers": [{"name": "trentm", "email": "<EMAIL>"}], "directories": {}}, "1.3.1": {"name": "<PERSON><PERSON><PERSON>", "description": "A light, featureful and explicit option parsing library.", "version": "1.3.1", "author": {"name": "<PERSON>", "url": "trentm.com"}, "repository": {"type": "git", "url": "git://github.com/trentm/node-dashdash.git"}, "main": "./lib/dashdash.js", "dependencies": {"assert-plus": "0.1.2"}, "devDependencies": {"nodeunit": "0.7.4"}, "engines": {"node": ">=0.8"}, "_id": "dashdash@1.3.1", "dist": {"shasum": "9119758398e5be65190daa401b905204bc4854ff", "tarball": "https://registry.npmjs.org/dashdash/-/dashdash-1.3.1.tgz", "integrity": "sha512-raLNhV8NAXw/N66ZEAWYrrbdbzfQ59Vtadu+jUSo4llEquPKTl70R8cmJhOjTLNZvYub9Btoh64luMyN46CY5A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHHWAzBDDBGqiQf4115TCTCaL7vn8VfJ6Q9540maUqWqAiATba8bG2MCH7FN40Ok/6lSQQVRnLNIxL3BIvwSG4C5fg=="}]}, "_from": ".", "_npmVersion": "1.2.14", "_npmUser": {"name": "trentm", "email": "<EMAIL>"}, "maintainers": [{"name": "trentm", "email": "<EMAIL>"}], "directories": {}}, "1.3.2": {"name": "<PERSON><PERSON><PERSON>", "description": "A light, featureful and explicit option parsing library.", "version": "1.3.2", "author": {"name": "<PERSON>", "url": "trentm.com"}, "repository": {"type": "git", "url": "git://github.com/trentm/node-dashdash.git"}, "main": "./lib/dashdash.js", "dependencies": {"assert-plus": "0.1.2"}, "devDependencies": {"nodeunit": "0.7.4"}, "engines": {"node": ">=0.8"}, "_id": "dashdash@1.3.2", "dist": {"shasum": "1e76d13fadf25f8f50e70212c98a25beb1b3b8eb", "tarball": "https://registry.npmjs.org/dashdash/-/dashdash-1.3.2.tgz", "integrity": "sha512-nZR++Yn4ECxmxRi9zK80m6YZoxWaj+HT+BLp6bfCx5/gWwaBP1lhDq+vhxpjAaYu4yyEwvn/s5v4E6j6WDYcuA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFgd3sBu3GbiOJ6U/avpeOxBWhE+e410l5Qfua31NqwrAiBKpVkNWThKYQRhbaaMl6I2Wx1/u6llC0cLP8aZPPVjFQ=="}]}, "_from": ".", "_npmVersion": "1.2.14", "_npmUser": {"name": "trentm", "email": "<EMAIL>"}, "maintainers": [{"name": "trentm", "email": "<EMAIL>"}], "directories": {}}, "1.4.0": {"name": "<PERSON><PERSON><PERSON>", "description": "A light, featureful and explicit option parsing library.", "version": "1.4.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://trentm.com"}, "repository": {"type": "git", "url": "git://github.com/trentm/node-dashdash.git"}, "main": "./lib/dashdash.js", "dependencies": {"assert-plus": "0.1.2"}, "devDependencies": {"nodeunit": "0.7.4"}, "engines": {"node": ">=0.8"}, "scripts": {"test": "nodeunit test/*.test.js"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://trentm.com"}, {"name": "<PERSON>", "url": "https://github.com/isaacs"}], "bugs": {"url": "https://github.com/trentm/node-dashdash/issues"}, "_id": "dashdash@1.4.0", "dist": {"shasum": "2d0ecc65efe7c4344cb016fbf4a8bfebbb056187", "tarball": "https://registry.npmjs.org/dashdash/-/dashdash-1.4.0.tgz", "integrity": "sha512-ynmgu1n68eUh9gsE4cvf2lUgzhToVE0bNHmFyHPTu9FynEZhnd1LdS1Yi2sRL5RuwfYj6REdg2VsCROdjZxfBw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHy6bPtKTtJ2byQwS5P0O5OEBFZ6w7i5less6SzvQDafAiEA1jTiDPCZiHoBJrSLASMO/frZtTfqax12acOkGQKsssc="}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "trentm", "email": "<EMAIL>"}, "maintainers": [{"name": "trentm", "email": "<EMAIL>"}], "directories": {}}, "1.5.0": {"name": "<PERSON><PERSON><PERSON>", "description": "A light, featureful and explicit option parsing library.", "version": "1.5.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://trentm.com"}, "keywords": ["option", "parser", "parsing", "cli", "command", "args"], "repository": {"type": "git", "url": "git://github.com/trentm/node-dashdash.git"}, "main": "./lib/dashdash.js", "dependencies": {"assert-plus": "0.1.2"}, "devDependencies": {"nodeunit": "0.7.4"}, "engines": {"node": ">=0.8"}, "scripts": {"test": "nodeunit test/*.test.js"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://trentm.com"}, {"name": "<PERSON>", "url": "https://github.com/isaacs"}], "bugs": {"url": "https://github.com/trentm/node-dashdash/issues"}, "_id": "dashdash@1.5.0", "dist": {"shasum": "fa5aa8a9415a7c5c3928be18bd4975458e666452", "tarball": "https://registry.npmjs.org/dashdash/-/dashdash-1.5.0.tgz", "integrity": "sha512-u6PYWUX4Pl7F0OVhMUEW0zIQhjjfS6av4ZvZaEKYAVtgQh7ObHQGr9zdncrH3efmITE7y77GO1SV8aPQt79ebQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDLual1Gn+JCjdjdXG1m8gu3OoofJ1CxV9fHcHKA8/puQIgfpLTOGE0Dmb8JATT+xLn+wc5FagGa4jbKHP5/06Jtik="}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "trentm", "email": "<EMAIL>"}, "maintainers": [{"name": "trentm", "email": "<EMAIL>"}], "directories": {}}, "1.6.0": {"name": "<PERSON><PERSON><PERSON>", "description": "A light, featureful and explicit option parsing library.", "version": "1.6.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://trentm.com"}, "keywords": ["option", "parser", "parsing", "cli", "command", "args"], "repository": {"type": "git", "url": "git://github.com/trentm/node-dashdash.git"}, "main": "./lib/dashdash.js", "dependencies": {"assert-plus": "0.1.x"}, "devDependencies": {"nodeunit": "0.9.x"}, "engines": {"node": ">=0.8"}, "scripts": {"test": "nodeunit test/*.test.js"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://trentm.com"}, {"name": "<PERSON>", "url": "https://github.com/isaacs"}, {"name": "<PERSON>", "url": "https://github.com/jclulow"}], "bugs": {"url": "https://github.com/trentm/node-dashdash/issues"}, "homepage": "https://github.com/trentm/node-dashdash", "_id": "dashdash@1.6.0", "_shasum": "c4b14318f88edead4713a1695e283276a7543963", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "trentm", "email": "<EMAIL>"}, "maintainers": [{"name": "trentm", "email": "<EMAIL>"}], "dist": {"shasum": "c4b14318f88edead4713a1695e283276a7543963", "tarball": "https://registry.npmjs.org/dashdash/-/dashdash-1.6.0.tgz", "integrity": "sha512-bSncDNZ9Ilgn6/eMZqPB8n8W6hjit0/UM3d+Wq2vwnzbIVmFXLoonpLbaW0wlwuWc3pcfH+ZuTcf3bqkPKLWzg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDXW431A88mLvttLqj4NkmbZYX6WN68r4tB0GabpqQqQQIgJH9Rk6TTPAatY8mLG6Az0Gj9JYvDccXhNN+T0j8Y/N0="}]}, "directories": {}}, "1.7.0": {"name": "<PERSON><PERSON><PERSON>", "description": "A light, featureful and explicit option parsing library.", "version": "1.7.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://trentm.com"}, "keywords": ["option", "parser", "parsing", "cli", "command", "args"], "repository": {"type": "git", "url": "git://github.com/trentm/node-dashdash.git"}, "main": "./lib/dashdash.js", "dependencies": {"assert-plus": "0.1.x"}, "devDependencies": {"nodeunit": "0.9.x"}, "engines": {"node": ">=0.8"}, "scripts": {"test": "nodeunit test/*.test.js"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://trentm.com"}, {"name": "<PERSON>", "url": "https://github.com/isaacs"}, {"name": "<PERSON>", "url": "https://github.com/jclulow"}, {"name": "<PERSON>", "url": "https://github.com/pfmooney"}], "bugs": {"url": "https://github.com/trentm/node-dashdash/issues"}, "homepage": "https://github.com/trentm/node-dashdash", "_id": "dashdash@1.7.0", "_shasum": "81234881f5773c28ac324dc0070c274b8b3c2bc6", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "trentm", "email": "<EMAIL>"}, "maintainers": [{"name": "trentm", "email": "<EMAIL>"}], "dist": {"shasum": "81234881f5773c28ac324dc0070c274b8b3c2bc6", "tarball": "https://registry.npmjs.org/dashdash/-/dashdash-1.7.0.tgz", "integrity": "sha512-lw0pf/NFSFKnGxPwCvwdb4gdk/7LcmbCvN7/+Mo6rMH1+yHslE9ggKPbffUzGujt1ObOw3ZZGc9HuHwUJkhP6w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHxM3KEHszhOGiHQ7NEsBD6tiPcJKk8meL3pSpy+ron/AiBojZj0u7FpX5lbWD9Bu/g5Mmo0Ia0bK66CZ642mrus5Q=="}]}, "directories": {}}, "1.7.1": {"name": "<PERSON><PERSON><PERSON>", "description": "A light, featureful and explicit option parsing library.", "version": "1.7.1", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://trentm.com"}, "keywords": ["option", "parser", "parsing", "cli", "command", "args"], "repository": {"type": "git", "url": "git://github.com/trentm/node-dashdash.git"}, "main": "./lib/dashdash.js", "dependencies": {"assert-plus": "0.1.x"}, "devDependencies": {"nodeunit": "0.9.x"}, "engines": {"node": ">=0.8"}, "scripts": {"test": "nodeunit test/*.test.js"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://trentm.com"}, {"name": "<PERSON>", "url": "https://github.com/isaacs"}, {"name": "<PERSON>", "url": "https://github.com/jclulow"}, {"name": "<PERSON>", "url": "https://github.com/pfmooney"}], "bugs": {"url": "https://github.com/trentm/node-dashdash/issues"}, "homepage": "https://github.com/trentm/node-dashdash", "_id": "dashdash@1.7.1", "_shasum": "aa85b3e8c9a8932d1263162deb7b6acb9a8b37f4", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "trentm", "email": "<EMAIL>"}, "maintainers": [{"name": "trentm", "email": "<EMAIL>"}], "dist": {"shasum": "aa85b3e8c9a8932d1263162deb7b6acb9a8b37f4", "tarball": "https://registry.npmjs.org/dashdash/-/dashdash-1.7.1.tgz", "integrity": "sha512-O49caXNIfS9XS9AsdQOa+ABw2GaTkKF8Gn5EZdZVudkqd9Qge1+YvXz8Mqu0otFmbdd4hLrldne5REEkp1PeAw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHGb/XigWKAxtAKoPuup+DXKFn7NF3c4jiArfkeNuuS6AiEAwroblc3oK3NzK2YRwVTv+sJzZ5PF19RuwmrGdjw5A5g="}]}, "directories": {}}, "1.7.2": {"name": "<PERSON><PERSON><PERSON>", "description": "A light, featureful and explicit option parsing library.", "version": "1.7.2", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://trentm.com"}, "keywords": ["option", "parser", "parsing", "cli", "command", "args"], "repository": {"type": "git", "url": "git://github.com/trentm/node-dashdash.git"}, "main": "./lib/dashdash.js", "dependencies": {"assert-plus": "0.1.x"}, "devDependencies": {"nodeunit": "0.9.x"}, "engines": {"node": ">=0.8"}, "scripts": {"test": "nodeunit test/*.test.js"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://trentm.com"}, {"name": "<PERSON>", "url": "https://github.com/isaacs"}, {"name": "<PERSON>", "url": "https://github.com/jclulow"}, {"name": "<PERSON>", "url": "https://github.com/pfmooney"}], "bugs": {"url": "https://github.com/trentm/node-dashdash/issues"}, "homepage": "https://github.com/trentm/node-dashdash", "_id": "dashdash@1.7.2", "_shasum": "2c0041ae78400c716c09a3329ea5e4dbf47d2c04", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "trentm", "email": "<EMAIL>"}, "maintainers": [{"name": "trentm", "email": "<EMAIL>"}], "dist": {"shasum": "2c0041ae78400c716c09a3329ea5e4dbf47d2c04", "tarball": "https://registry.npmjs.org/dashdash/-/dashdash-1.7.2.tgz", "integrity": "sha512-qw1UO3D66fQQgUVb0YEotpcCgKvvEGP31xZ8lBT4ZWzNV3vk2r4hZmy/+UBo4dSSk2BB/LiEu71fjDF+m3uSjA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDzfSLMjc/2YLcduU9rVkaR2vfV3s44SKnC9B20kePD4wIgPsl5Qmlnzd43/QjCTpOgkV1GhePpuXvxtfhMYFstfUw="}]}, "directories": {}}, "1.7.3": {"name": "<PERSON><PERSON><PERSON>", "description": "A light, featureful and explicit option parsing library.", "version": "1.7.3", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://trentm.com"}, "keywords": ["option", "parser", "parsing", "cli", "command", "args"], "repository": {"type": "git", "url": "git://github.com/trentm/node-dashdash.git"}, "main": "./lib/dashdash.js", "dependencies": {"assert-plus": "0.1.x"}, "devDependencies": {"nodeunit": "0.9.x"}, "engines": {"node": ">=0.8"}, "scripts": {"test": "nodeunit test/*.test.js"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://trentm.com"}, {"name": "<PERSON>", "url": "https://github.com/isaacs"}, {"name": "<PERSON>", "url": "https://github.com/jclulow"}, {"name": "<PERSON>", "url": "https://github.com/pfmooney"}], "bugs": {"url": "https://github.com/trentm/node-dashdash/issues"}, "homepage": "https://github.com/trentm/node-dashdash", "_id": "dashdash@1.7.3", "_shasum": "bf533fedaa455ed8fee11519ebfb9ad66170dcdf", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "trentm", "email": "<EMAIL>"}, "maintainers": [{"name": "trentm", "email": "<EMAIL>"}], "dist": {"shasum": "bf533fedaa455ed8fee11519ebfb9ad66170dcdf", "tarball": "https://registry.npmjs.org/dashdash/-/dashdash-1.7.3.tgz", "integrity": "sha512-NxuWFXR3+HJULO6F6VprWnUQbx0MXgfEuOfz3m+pw8LYZV06SHRjcaBVvVlwH132xJq12mljySVDLcbMcFM7EA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGffXTpxTpaF6e1fJObZ4IdeIj/FAN8y0y/yhEy1bpYxAiBiPk1qxUMl6QehEhpbRmpqaYdQX2+s3Asexu5h6aG2/w=="}]}, "directories": {}}, "1.8.0": {"name": "<PERSON><PERSON><PERSON>", "description": "A light, featureful and explicit option parsing library.", "version": "1.8.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://trentm.com"}, "keywords": ["option", "parser", "parsing", "cli", "command", "args"], "repository": {"type": "git", "url": "git://github.com/trentm/node-dashdash.git"}, "main": "./lib/dashdash.js", "dependencies": {"assert-plus": "0.1.x"}, "devDependencies": {"nodeunit": "0.9.x"}, "engines": {"node": ">=0.8"}, "scripts": {"test": "nodeunit test/*.test.js"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://trentm.com"}, {"name": "<PERSON>", "url": "https://github.com/isaacs"}, {"name": "<PERSON>", "url": "https://github.com/jclulow"}, {"name": "<PERSON>", "url": "https://github.com/pfmooney"}], "bugs": {"url": "https://github.com/trentm/node-dashdash/issues"}, "homepage": "https://github.com/trentm/node-dashdash", "_id": "dashdash@1.8.0", "_shasum": "40c19740ee4cb6921992d118ad4dd7689f8b9f43", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "trentm", "email": "<EMAIL>"}, "maintainers": [{"name": "trentm", "email": "<EMAIL>"}], "dist": {"shasum": "40c19740ee4cb6921992d118ad4dd7689f8b9f43", "tarball": "https://registry.npmjs.org/dashdash/-/dashdash-1.8.0.tgz", "integrity": "sha512-oC4hHz9Fas+i2nXhx5UFflNcVEsN429GIDa/aLFwlz51sx9r1Y5ghegvqpbmpL5f1sf+DaI4yc2iC6BlUJIvuQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCu7XEDGpAKzAF37g0GS6zJj+OSoioeSZHDHIigXLer8gIhALbyJQbnIhB0s7bdLLSxViaQ5M0F6/ArGRw5iSjzDHhH"}]}, "directories": {}}, "1.9.0": {"name": "<PERSON><PERSON><PERSON>", "description": "A light, featureful and explicit option parsing library.", "version": "1.9.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://trentm.com"}, "keywords": ["option", "parser", "parsing", "cli", "command", "args"], "repository": {"type": "git", "url": "git://github.com/trentm/node-dashdash.git"}, "main": "./lib/dashdash.js", "dependencies": {"assert-plus": "0.1.x"}, "devDependencies": {"nodeunit": "0.9.x"}, "engines": {"node": ">=0.8"}, "scripts": {"test": "nodeunit test/*.test.js"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://trentm.com"}, {"name": "<PERSON>", "url": "https://github.com/isaacs"}, {"name": "<PERSON>", "url": "https://github.com/jclulow"}, {"name": "<PERSON>", "url": "https://github.com/pfmooney"}], "bugs": {"url": "https://github.com/trentm/node-dashdash/issues"}, "homepage": "https://github.com/trentm/node-dashdash", "_id": "dashdash@1.9.0", "_shasum": "05d97206366f5411563c3d0fbc87d19cd46cbd51", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "trentm", "email": "<EMAIL>"}, "maintainers": [{"name": "trentm", "email": "<EMAIL>"}], "dist": {"shasum": "05d97206366f5411563c3d0fbc87d19cd46cbd51", "tarball": "https://registry.npmjs.org/dashdash/-/dashdash-1.9.0.tgz", "integrity": "sha512-NLYyhdm8dYPRR+6n2IT4vTplHTGiQYEy5GEFX36iK3T0jmjf6OZ0gmvnJQ95yH7o5doA4jLe76FLDvoZATPztg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFKSnigE7EQ8RNaTH+qyeS1jesA0Q6CQbTkRPr1xP6txAiBNOJM9ptJ9ABuFpEpGg0qEvQYO3FW8KdYG0lg+Po7ysg=="}]}, "directories": {}}, "1.10.0": {"name": "<PERSON><PERSON><PERSON>", "description": "A light, featureful and explicit option parsing library.", "version": "1.10.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://trentm.com"}, "keywords": ["option", "parser", "parsing", "cli", "command", "args"], "repository": {"type": "git", "url": "git://github.com/trentm/node-dashdash.git"}, "main": "./lib/dashdash.js", "dependencies": {"assert-plus": "0.1.x"}, "devDependencies": {"nodeunit": "0.9.x"}, "engines": {"node": ">=0.8"}, "scripts": {"test": "nodeunit test/*.test.js"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://trentm.com"}, {"name": "<PERSON>", "url": "https://github.com/isaacs"}, {"name": "<PERSON>", "url": "https://github.com/jclulow"}, {"name": "<PERSON>", "url": "https://github.com/pfmooney"}], "bugs": {"url": "https://github.com/trentm/node-dashdash/issues"}, "homepage": "https://github.com/trentm/node-dashdash", "_id": "dashdash@1.10.0", "_shasum": "119b756c1aa67e55dcfb3152e2e659d1d26f5229", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "trentm", "email": "<EMAIL>"}, "maintainers": [{"name": "trentm", "email": "<EMAIL>"}], "dist": {"shasum": "119b756c1aa67e55dcfb3152e2e659d1d26f5229", "tarball": "https://registry.npmjs.org/dashdash/-/dashdash-1.10.0.tgz", "integrity": "sha512-<PERSON>0SPaPAJZr56IAwdASmc7r3LRO85BHeGjsGRXPDVlowC4e8Ni1ny/78XaeSI9DtO6nyeLguhxbniOCiHSVPoyQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICqnHpQ02XX5aXvMO9JjG93IUEcY8Q6aROqlBPiW5UhtAiEA8jy9O8fqfd0KVzw8rjXSvWJNy6BLMzruyPui6vFO8xw="}]}, "directories": {}}, "1.10.1": {"name": "<PERSON><PERSON><PERSON>", "description": "A light, featureful and explicit option parsing library.", "version": "1.10.1", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://trentm.com"}, "keywords": ["option", "parser", "parsing", "cli", "command", "args"], "repository": {"type": "git", "url": "git://github.com/trentm/node-dashdash.git"}, "main": "./lib/dashdash.js", "dependencies": {"assert-plus": "0.1.x"}, "devDependencies": {"nodeunit": "0.9.x"}, "engines": {"node": ">=0.8"}, "scripts": {"test": "nodeunit test/*.test.js"}, "license": "MIT", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://trentm.com"}, {"name": "<PERSON>", "url": "https://github.com/isaacs"}, {"name": "<PERSON>", "url": "https://github.com/jclulow"}, {"name": "<PERSON>", "url": "https://github.com/pfmooney"}], "gitHead": "e95a66a095553ef045283b34d7615bf02f0dfaac", "bugs": {"url": "https://github.com/trentm/node-dashdash/issues"}, "homepage": "https://github.com/trentm/node-dashdash", "_id": "dashdash@1.10.1", "_shasum": "0abf1af89a8f5129a81f18c2b35b21df22622f60", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "trentm", "email": "<EMAIL>"}, "maintainers": [{"name": "trentm", "email": "<EMAIL>"}], "dist": {"shasum": "0abf1af89a8f5129a81f18c2b35b21df22622f60", "tarball": "https://registry.npmjs.org/dashdash/-/dashdash-1.10.1.tgz", "integrity": "sha512-hu/OyjwJnarCHKBL1eM4ZaRn00dwRwfSOR316vE5IO7PO4iM+xMx6xOY2g76yRwq+OHBrmb5oh74tVr27piJTQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICwYQHCASr1inGaHb9w5X8EaetC3eJLewSIsMr7zanCCAiEAvoOhYYw7zYyKsNNEFBbkRoePF48NYkI4YXHg3ArRlPA="}]}, "directories": {}}, "1.11.0": {"name": "<PERSON><PERSON><PERSON>", "description": "A light, featureful and explicit option parsing library.", "version": "1.11.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://trentm.com"}, "keywords": ["option", "parser", "parsing", "cli", "command", "args"], "repository": {"type": "git", "url": "git://github.com/trentm/node-dashdash.git"}, "main": "./lib/dashdash.js", "dependencies": {"assert-plus": "0.1.x"}, "devDependencies": {"nodeunit": "0.9.x"}, "engines": {"node": ">=0.8"}, "scripts": {"test": "nodeunit test/*.test.js"}, "license": "MIT", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://trentm.com"}, {"name": "<PERSON>", "url": "https://github.com/isaacs"}, {"name": "<PERSON>", "url": "https://github.com/jclulow"}, {"name": "<PERSON>", "url": "https://github.com/pfmooney"}], "gitHead": "c0f126a9ec1c8e75ef904a68b1f3b4b7fc1a86ad", "bugs": {"url": "https://github.com/trentm/node-dashdash/issues"}, "homepage": "https://github.com/trentm/node-dashdash", "_id": "dashdash@1.11.0", "_shasum": "babd3892d71ab403948c6a2b9d65f7de1ace8e92", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "trentm", "email": "<EMAIL>"}, "maintainers": [{"name": "trentm", "email": "<EMAIL>"}], "dist": {"shasum": "babd3892d71ab403948c6a2b9d65f7de1ace8e92", "tarball": "https://registry.npmjs.org/dashdash/-/dashdash-1.11.0.tgz", "integrity": "sha512-NmYMiqgQuUp+Beq1cjSi33QDJGwrOC6FFD0J3KWhHvLLuO+iT59GZI+Wvr7Kra4ycC+OXtFfwrOUS6BmAs7nIA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBdUi5nZzRdJGoe114dzsDerHPzaTuPot5Mx8OM5dNbAAiEAjmNZt8srQWE/OUEO1PBcDJ3vk9XPquji+T7vQ90PfPE="}]}, "directories": {}}, "1.12.0": {"name": "<PERSON><PERSON><PERSON>", "description": "A light, featureful and explicit option parsing library.", "version": "1.12.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://trentm.com"}, "keywords": ["option", "parser", "parsing", "cli", "command", "args", "bash", "completion"], "repository": {"type": "git", "url": "git://github.com/trentm/node-dashdash.git"}, "main": "./lib/dashdash.js", "dependencies": {"assert-plus": "0.1.x"}, "devDependencies": {"nodeunit": "0.9.x"}, "engines": {"node": ">=0.8"}, "scripts": {"test": "nodeunit test/*.test.js"}, "license": "MIT", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://trentm.com"}, {"name": "<PERSON>", "url": "https://github.com/isaacs"}, {"name": "<PERSON>", "url": "https://github.com/jclulow"}, {"name": "<PERSON>", "url": "https://github.com/pfmooney"}], "gitHead": "30f46825de72f479b014c32c7a8ab97a0ea68b34", "bugs": {"url": "https://github.com/trentm/node-dashdash/issues"}, "homepage": "https://github.com/trentm/node-dashdash", "_id": "dashdash@1.12.0", "_shasum": "478075969f75afad5c9af944233b6e7dd3edf1fb", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "trentm", "email": "<EMAIL>"}, "maintainers": [{"name": "trentm", "email": "<EMAIL>"}], "dist": {"shasum": "478075969f75afad5c9af944233b6e7dd3edf1fb", "tarball": "https://registry.npmjs.org/dashdash/-/dashdash-1.12.0.tgz", "integrity": "sha512-6mOjnEy6VdyO8TJLSbALtqI42XXCFMidpErkg3JxhvjUrc9rPIRm5StFlLXo+Sb+IRjJgKlXE1Wdv1e5j6P/+A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDAR9R8qGe+jdp1HkIuLlOkqITZbbp5ZYpLsSNe9DUnzAiA+7OP/MCUlPjqVMc1pVqxqbwlovFgm2Rb3Rvbu1fZ1AA=="}]}, "directories": {}}, "1.12.1": {"name": "<PERSON><PERSON><PERSON>", "description": "A light, featureful and explicit option parsing library.", "version": "1.12.1", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://trentm.com"}, "keywords": ["option", "parser", "parsing", "cli", "command", "args", "bash", "completion"], "repository": {"type": "git", "url": "git://github.com/trentm/node-dashdash.git"}, "main": "./lib/dashdash.js", "dependencies": {"assert-plus": "0.1.x"}, "devDependencies": {"nodeunit": "0.9.x"}, "engines": {"node": ">=0.8"}, "scripts": {"test": "nodeunit test/*.test.js"}, "license": "MIT", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://trentm.com"}, {"name": "<PERSON>", "url": "https://github.com/isaacs"}, {"name": "<PERSON>", "url": "https://github.com/jclulow"}, {"name": "<PERSON>", "url": "https://github.com/pfmooney"}], "gitHead": "927f21d4bfbd69e95c900509d415e4f5bef6dd8a", "bugs": {"url": "https://github.com/trentm/node-dashdash/issues"}, "homepage": "https://github.com/trentm/node-dashdash", "_id": "dashdash@1.12.1", "_shasum": "ed5fd0f9d2dc189e1fbf11e40f6a412167203b6a", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "trentm", "email": "<EMAIL>"}, "maintainers": [{"name": "trentm", "email": "<EMAIL>"}], "dist": {"shasum": "ed5fd0f9d2dc189e1fbf11e40f6a412167203b6a", "tarball": "https://registry.npmjs.org/dashdash/-/dashdash-1.12.1.tgz", "integrity": "sha512-JM+2pz5zuDqro3frwDoY3orNo2/5RXvibLjf2hcdKXOiWTRHmqhXMCEHIdP7q1TiNNrV6/uKlJKeGJvD3nezeQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDM4WjbARTSLKkBi22c4VyGTDG4umQEG6nr5i7cQK4tYAIgT7UenV/yrvZ7AXAnkWpWYmykWxnHgeXaYij1eV8kF58="}]}, "directories": {}}, "1.12.2": {"name": "<PERSON><PERSON><PERSON>", "description": "A light, featureful and explicit option parsing library.", "version": "1.12.2", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://trentm.com"}, "keywords": ["option", "parser", "parsing", "cli", "command", "args", "bash", "completion"], "repository": {"type": "git", "url": "git://github.com/trentm/node-dashdash.git"}, "main": "./lib/dashdash.js", "dependencies": {"assert-plus": "^0.2.0"}, "devDependencies": {"nodeunit": "0.9.x"}, "engines": {"node": ">=0.8"}, "scripts": {"test": "nodeunit test/*.test.js"}, "license": "MIT", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://trentm.com"}, {"name": "<PERSON>", "url": "https://github.com/isaacs"}, {"name": "<PERSON>", "url": "https://github.com/jclulow"}, {"name": "<PERSON>", "url": "https://github.com/pfmooney"}], "gitHead": "d4248f21da0b30bd89c88ccee9bf6eac0e59f9f6", "bugs": {"url": "https://github.com/trentm/node-dashdash/issues"}, "homepage": "https://github.com/trentm/node-dashdash", "_id": "dashdash@1.12.2", "_shasum": "1c6f70588498d047b8cd5777b32ba85a5e25be36", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "trentm", "email": "<EMAIL>"}, "maintainers": [{"name": "trentm", "email": "<EMAIL>"}], "dist": {"shasum": "1c6f70588498d047b8cd5777b32ba85a5e25be36", "tarball": "https://registry.npmjs.org/dashdash/-/dashdash-1.12.2.tgz", "integrity": "sha512-kzh1ragIr9AG3hNCNefKNkHroHsaRMtvh59rOYlxKofol44jAv2xUIou2DygFkYT40auev6K/BoT6scCD9r/+A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHzQI5zOaW8EziJXHSY1o6j9To/D4HQvUz4SWeI55gVpAiEAp0kqNO+i8krEgWvHBUPu8kMvY4aVpUG/hNweW2u8JV4="}]}, "directories": {}}, "1.13.0": {"name": "<PERSON><PERSON><PERSON>", "description": "A light, featureful and explicit option parsing library.", "version": "1.13.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://trentm.com"}, "keywords": ["option", "parser", "parsing", "cli", "command", "args", "bash", "completion"], "repository": {"type": "git", "url": "git://github.com/trentm/node-dashdash.git"}, "main": "./lib/dashdash.js", "dependencies": {"assert-plus": "^1.0.0"}, "devDependencies": {"nodeunit": "0.9.x"}, "engines": {"node": ">=0.10"}, "scripts": {"test": "nodeunit test/*.test.js"}, "license": "MIT", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://trentm.com"}, {"name": "<PERSON>", "url": "https://github.com/isaacs"}, {"name": "<PERSON>", "url": "https://github.com/jclulow"}, {"name": "<PERSON>", "url": "https://github.com/pfmooney"}, {"name": "<PERSON>", "url": "https://github.com/davepacheco"}], "gitHead": "69425c9d9000d72c95e78ca43e3f84675f8f73ff", "bugs": {"url": "https://github.com/trentm/node-dashdash/issues"}, "homepage": "https://github.com/trentm/node-dashdash", "_id": "dashdash@1.13.0", "_shasum": "a5aae6fd9d8e156624eb0dd9259eb12ba245385a", "_from": ".", "_npmVersion": "1.4.29", "_npmUser": {"name": "trentm", "email": "<EMAIL>"}, "maintainers": [{"name": "trentm", "email": "<EMAIL>"}], "dist": {"shasum": "a5aae6fd9d8e156624eb0dd9259eb12ba245385a", "tarball": "https://registry.npmjs.org/dashdash/-/dashdash-1.13.0.tgz", "integrity": "sha512-XwMbafXv5kR4O1ZWKC1ez2kELlRqFZ5c5CbiksY+1zPQ5EUkpWTXdSo9Zm2arwTNk2CQUTbYNQOLMp1JdrKlSQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDHpFUyaj1P4wMeinohcI3ySTr20bF1S+sG1Zk0wgFYswIgYss9ncZ4WTGnNS4na69mb1SDNhhT/jWsPCreF3/Xffk="}]}, "_npmOperationalInternal": {"host": "packages-5-east.internal.npmjs.com", "tmp": "tmp/dashdash-1.13.0.tgz_1455248887432_0.5643249636050314"}, "directories": {}}, "1.13.1": {"name": "<PERSON><PERSON><PERSON>", "description": "A light, featureful and explicit option parsing library.", "version": "1.13.1", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://trentm.com"}, "keywords": ["option", "parser", "parsing", "cli", "command", "args", "bash", "completion"], "repository": {"type": "git", "url": "git://github.com/trentm/node-dashdash.git"}, "main": "./lib/dashdash.js", "dependencies": {"assert-plus": "^1.0.0"}, "devDependencies": {"nodeunit": "0.9.x"}, "engines": {"node": ">=0.10"}, "scripts": {"test": "nodeunit test/*.test.js"}, "license": "MIT", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://trentm.com"}, {"name": "<PERSON>", "url": "https://github.com/isaacs"}, {"name": "<PERSON>", "url": "https://github.com/jclulow"}, {"name": "<PERSON>", "url": "https://github.com/pfmooney"}, {"name": "<PERSON>", "url": "https://github.com/davepacheco"}], "gitHead": "aabf8a7e71ce7ed3d24c3c57d64a57b78c1a8546", "bugs": {"url": "https://github.com/trentm/node-dashdash/issues"}, "homepage": "https://github.com/trentm/node-dashdash", "_id": "dashdash@1.13.1", "_shasum": "3530ed38b9026be9af05c83423c9154122e3d47c", "_from": ".", "_npmVersion": "1.4.29", "_npmUser": {"name": "trentm", "email": "<EMAIL>"}, "maintainers": [{"name": "trentm", "email": "<EMAIL>"}], "dist": {"shasum": "3530ed38b9026be9af05c83423c9154122e3d47c", "tarball": "https://registry.npmjs.org/dashdash/-/dashdash-1.13.1.tgz", "integrity": "sha512-jk/kFpzoSSGtyQd2dc1NI3CCvp94PTP2zOvRO7P9NNfHT0ziFEI7E65fOyyQfULEFHzC1u9bGa2KkqXBnqYhJg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQClVKy/w/EDnX6EVaCaefKXYyqCU6pgZjhhOzBx1G5/GgIhALNfVnPa7S0YO7ckyqLmYdhgH5oGRb+lczPfLevTtru9"}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/dashdash-1.13.1.tgz_1461355198185_0.31851457548327744"}, "directories": {}}, "1.14.0": {"name": "<PERSON><PERSON><PERSON>", "description": "A light, featureful and explicit option parsing library.", "version": "1.14.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://trentm.com"}, "keywords": ["option", "parser", "parsing", "cli", "command", "args", "bash", "completion"], "repository": {"type": "git", "url": "git://github.com/trentm/node-dashdash.git"}, "main": "./lib/dashdash.js", "dependencies": {"assert-plus": "^1.0.0"}, "devDependencies": {"nodeunit": "0.9.x"}, "engines": {"node": ">=0.10"}, "scripts": {"test": "nodeunit test/*.test.js"}, "license": "MIT", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://trentm.com"}, {"name": "<PERSON>", "url": "https://github.com/isaacs"}, {"name": "<PERSON>", "url": "https://github.com/jclulow"}, {"name": "<PERSON>", "url": "https://github.com/pfmooney"}, {"name": "<PERSON>", "url": "https://github.com/davepacheco"}], "gitHead": "2f70d248a509418e4473922ee034faf9cd91b3f8", "bugs": {"url": "https://github.com/trentm/node-dashdash/issues"}, "homepage": "https://github.com/trentm/node-dashdash", "_id": "dashdash@1.14.0", "_shasum": "29e486c5418bf0f356034a993d51686a33e84141", "_from": ".", "_npmVersion": "1.4.29", "_npmUser": {"name": "trentm", "email": "<EMAIL>"}, "maintainers": [{"name": "trentm", "email": "<EMAIL>"}], "dist": {"shasum": "29e486c5418bf0f356034a993d51686a33e84141", "tarball": "https://registry.npmjs.org/dashdash/-/dashdash-1.14.0.tgz", "integrity": "sha512-yQoNs2JYflS72nKEufCisG/IytQVVtsIRGVUKPsxI0QZs/Q8gZVBR3YUxHYbW1mLT2Q41HQU4seotPHlEHw36Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCugETeQX1th/nUQ1r3sKeBqe8K0cBJS9aNI0UYD/IarQIgCcfw9JkJkn+cChaHmrtGxEf5W0j6NUnDJCE+etlU9UI="}]}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/dashdash-1.14.0.tgz_1464847169030_0.07120498712174594"}, "directories": {}}, "1.14.1": {"name": "<PERSON><PERSON><PERSON>", "description": "A light, featureful and explicit option parsing library.", "version": "1.14.1", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://trentm.com"}, "keywords": ["option", "parser", "parsing", "cli", "command", "args", "bash", "completion"], "repository": {"type": "git", "url": "git://github.com/trentm/node-dashdash.git"}, "main": "./lib/dashdash.js", "dependencies": {"assert-plus": "^1.0.0"}, "devDependencies": {"nodeunit": "0.9.x"}, "engines": {"node": ">=0.10"}, "scripts": {"test": "nodeunit test/*.test.js"}, "license": "MIT", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://trentm.com"}, {"name": "<PERSON>", "url": "https://github.com/isaacs"}, {"name": "<PERSON>", "url": "https://github.com/jclulow"}, {"name": "<PERSON>", "url": "https://github.com/pfmooney"}, {"name": "<PERSON>", "url": "https://github.com/davepacheco"}], "gitHead": "1dd7379640462a21ca6d92502803de830b4acfa2", "bugs": {"url": "https://github.com/trentm/node-dashdash/issues"}, "homepage": "https://github.com/trentm/node-dashdash#readme", "_id": "dashdash@1.14.1", "_shasum": "853cfa0f7cbe2fed5de20326b8dd581035f6e2f0", "_from": ".", "_npmVersion": "2.15.9", "_nodeVersion": "4.6.1", "_npmUser": {"name": "trentm", "email": "<EMAIL>"}, "maintainers": [{"name": "trentm", "email": "<EMAIL>"}], "dist": {"shasum": "853cfa0f7cbe2fed5de20326b8dd581035f6e2f0", "tarball": "https://registry.npmjs.org/dashdash/-/dashdash-1.14.1.tgz", "integrity": "sha512-jRFi8UDGo6j+odZiEpjazZaWqEal3w/basFjQHQEwVtZJGDpxbH1MeYluwCS8Xq5wmLJooDlMgvVarmWfGM44g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD3mkX7tzvThaNArC2+pif2pN4nFydl8zsTjts8lcQ/jgIhAL0p1App1p1XBLbNxXGEAk2hv1q0y7G0K7NhO4hLaoaP"}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/dashdash-1.14.1.tgz_1479854020349_0.731718891998753"}, "directories": {}}, "2.0.0": {"name": "<PERSON><PERSON><PERSON>", "description": "A light, featureful and explicit option parsing library.", "version": "2.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://trentm.com"}, "keywords": ["option", "parser", "parsing", "cli", "command", "args", "bash", "completion"], "repository": {"type": "git", "url": "git://github.com/trentm/node-dashdash.git"}, "main": "./lib/dashdash.js", "dependencies": {"assert-plus": "^1.0.0"}, "devDependencies": {"eslint": "^7.3.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "prettier": "^2.0.5", "tap": "^14.x"}, "engines": {"node": ">=10.x"}, "scripts": {"test": "tap test/*.test.js", "check": "eslint lib test", "lint": "eslint --rule 'prettier/prettier: off' lib test", "fmt": "eslint --fix lib test"}, "license": "MIT", "gitHead": "3cc3b7edd4ea11c8eddc6f1a9d2983b2184f7fb6", "bugs": {"url": "https://github.com/trentm/node-dashdash/issues"}, "homepage": "https://github.com/trentm/node-dashdash#readme", "_id": "dashdash@2.0.0", "_nodeVersion": "10.20.1", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-ElMoAPlrzmF4l0OscF5pPBZv8LhUJBnwh7rHKllUOrwabAr47R1aQIIwC53rc59ycCb7k5Sj1/es+A3Bep/x5w==", "shasum": "4bd6f2bc0e89001523a68d804bf2a7da9f73088b", "tarball": "https://registry.npmjs.org/dashdash/-/dashdash-2.0.0.tgz", "fileCount": 6, "unpackedSize": 83496, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfbnegCRA9TVsSAnZWagAAMvsQAIopMgBWqXmRivUwMi8H\nSYW2fpyBK59ttr6ga6TVcsTRmrh9uo+f5bySqv7L4TCK8Y7n7aYWRtW6LT1B\nhEIFYlUOCqQU7pFzUuGRlEfWAcTA9cVsZQN7kQhUjz4Vs9LSShOYeNVl84cA\n5CirHvEpw3fuLsEoss4pRGMlnqjHBtl4VGGBeVAbO+MjQ3v9L/LNYBlQurpt\nGx/fvY6OA79uDJRy8KtUUcKuXwXAjxnFiihIlRek3oGVmMwLBjcX8EHnhL//\noYoOmsC/KB62zsGz2llllMkbxTJRUvucTABu1ueZBT3IIv4QegaRulAa9/qJ\ngffqcXmlQqbGxNXVDqT27GLqjtBhuTDcLfxZeffBuuM77OWarAdw8s7aIc8p\nd+ewpomVWNlHKJhKhpJTdrUE+5LbSh+nsBaCoJYjRcRqRvPU9GMvfhg2MzBB\ncuoL9C01pw7Bk0S9rB5kHDkezFHErETQDTNNidI5pVHqMf9qC4DP0kWJNLGW\nhno38YyvZXtgzIzjHzukvGCVRlUN+9cIGKBuvfV/Sj22hKuw088FcOrG6Afm\niiNn3iv7Tmf4DragqbwXruZhrQp7Nmkrbwrw2AIVu8c/MDY8IN2zTvbcZsUm\nlMAkkP9VM8HrPBbHAgtGr/8emLqfF1jYDunqF/unKOrjru3kfLaquhPvsIaK\nMmwj\r\n=y1Xt\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFMx+maqwJRr7goHvhXSe8UDGCD/509KDQhto1eedK1mAiEAuDNXHLcaSDypFxbi8mW+94JdOhrFzmjivpaGsvbZCAo="}]}, "maintainers": [{"name": "trentm", "email": "<EMAIL>"}], "_npmUser": {"name": "trentm", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dashdash_2.0.0_1601075104120_0.5661131481585062"}, "_hasShrinkwrap": false}}, "readme": "A light, featureful and explicit option parsing library for node.js.\n\n[Why another one? See below](#why). tl;dr: The others I've tried are one of\ntoo loosey goosey (not explicit), too big/too many deps, or ill specified.\nYMMV.\n\n\n# Install\n\n    npm install dashdash\n\n\n# Active Versions\n\n- [2.x](https://github.com/trentm/node-dashdash/tree/master) - Version 2.x\n  dropped support for node versions earlier than node 10.x. While I don't\n  expect to actively break functionality, new changes are no longer tested\n  on earlier versions.\n- [1.x](https://github.com/trentm/node-dashdash/tree/1.x) - This version\n  supports back to node version 0.10.x.\n\n\n# Usage\n\n```javascript\nvar dashdash = require('dashdash');\n\n// Specify the options. Minimally `name` (or `names`) and `type`\n// must be given for each.\nvar options = [\n    {\n        // `names` or a single `name`. First element is the `opts.KEY`.\n        names: ['help', 'h'],\n        // See \"Option specs\" below for types.\n        type: 'bool',\n        help: 'Print this help and exit.'\n    }\n];\n\n// Shortcut form. As called it infers `process.argv`. See below for\n// the longer form to use methods like `.help()` on the Parser object.\nvar opts = dashdash.parse({options: options});\n\nconsole.log(\"opts:\", opts);\nconsole.log(\"args:\", opts._args);\n```\n\n\n# Longer Example\n\nA more realistic [starter script \"foo.js\"](./examples/foo.js) is as follows.\nThis also shows using `parser.help()` for formatted option help.\n\n```javascript\nvar dashdash = require('dashdash');\n\nvar options = [\n    {\n        name: 'version',\n        type: 'bool',\n        help: 'Print tool version and exit.'\n    },\n    {\n        names: ['help', 'h'],\n        type: 'bool',\n        help: 'Print this help and exit.'\n    },\n    {\n        names: ['verbose', 'v'],\n        type: 'arrayOfBool',\n        help: 'Verbose output. Use multiple times for more verbose.'\n    },\n    {\n        names: ['file', 'f'],\n        type: 'string',\n        help: 'File to process',\n        helpArg: 'FILE'\n    }\n];\n\nvar parser = dashdash.createParser({options: options});\ntry {\n    var opts = parser.parse(process.argv);\n} catch (e) {\n    console.error('foo: error: %s', e.message);\n    process.exit(1);\n}\n\nconsole.log(\"# opts:\", opts);\nconsole.log(\"# args:\", opts._args);\n\n// Use `parser.help()` for formatted options help.\nif (opts.help) {\n    var help = parser.help({includeEnv: true}).trimRight();\n    console.log('usage: node foo.js [OPTIONS]\\n'\n                + 'options:\\n'\n                + help);\n    process.exit(0);\n}\n\n// ...\n```\n\n\nSome example output from this script (foo.js):\n\n```\n$ node foo.js -h\n# opts: { help: true,\n  _order: [ { name: 'help', value: true, from: 'argv' } ],\n  _args: [] }\n# args: []\nusage: node foo.js [OPTIONS]\noptions:\n    --version             Print tool version and exit.\n    -h, --help            Print this help and exit.\n    -v, --verbose         Verbose output. Use multiple times for more verbose.\n    -f FILE, --file=FILE  File to process\n\n$ node foo.js -v\n# opts: { verbose: [ true ],\n  _order: [ { name: 'verbose', value: true, from: 'argv' } ],\n  _args: [] }\n# args: []\n\n$ node foo.js --version arg1\n# opts: { version: true,\n  _order: [ { name: 'version', value: true, from: 'argv' } ],\n  _args: [ 'arg1' ] }\n# args: [ 'arg1' ]\n\n$ node foo.js -f bar.txt\n# opts: { file: 'bar.txt',\n  _order: [ { name: 'file', value: 'bar.txt', from: 'argv' } ],\n  _args: [] }\n# args: []\n\n$ node foo.js -vvv --file=blah\n# opts: { verbose: [ true, true, true ],\n  file: 'blah',\n  _order:\n   [ { name: 'verbose', value: true, from: 'argv' },\n     { name: 'verbose', value: true, from: 'argv' },\n     { name: 'verbose', value: true, from: 'argv' },\n     { name: 'file', value: 'blah', from: 'argv' } ],\n  _args: [] }\n# args: []\n```\n\n\nSee the [\"examples\"](examples/) dir for a number of starter examples using\nsome of dashdash's features.\n\n\n# Environment variable integration\n\nIf you want to allow environment variables to specify options to your tool,\ndashdash makes this easy. We can change the 'verbose' option in the example\nabove to include an 'env' field:\n\n```javascript\n    {\n        names: ['verbose', 'v'],\n        type: 'arrayOfBool',\n        env: 'FOO_VERBOSE',         // <--- add this line\n        help: 'Verbose output. Use multiple times for more verbose.'\n    },\n```\n\nthen the **\"FOO_VERBOSE\" environment variable** can be used to set this\noption:\n\n```shell\n$ FOO_VERBOSE=1 node foo.js\n# opts: { verbose: [ true ],\n  _order: [ { name: 'verbose', value: true, from: 'env' } ],\n  _args: [] }\n# args: []\n```\n\nBoolean options will interpret the empty string as unset, '0' as false\nand anything else as true.\n\n```shell\n$ FOO_VERBOSE= node examples/foo.js                 # not set\n# opts: { _order: [], _args: [] }\n# args: []\n\n$ FOO_VERBOSE=0 node examples/foo.js                # '0' is false\n# opts: { verbose: [ false ],\n  _order: [ { key: 'verbose', value: false, from: 'env' } ],\n  _args: [] }\n# args: []\n\n$ FOO_VERBOSE=1 node examples/foo.js                # true\n# opts: { verbose: [ true ],\n  _order: [ { key: 'verbose', value: true, from: 'env' } ],\n  _args: [] }\n# args: []\n\n$ FOO_VERBOSE=boogabooga node examples/foo.js       # true\n# opts: { verbose: [ true ],\n  _order: [ { key: 'verbose', value: true, from: 'env' } ],\n  _args: [] }\n# args: []\n```\n\nNon-booleans can be used as well. Strings:\n\n```shell\n$ FOO_FILE=data.txt node examples/foo.js\n# opts: { file: 'data.txt',\n  _order: [ { key: 'file', value: 'data.txt', from: 'env' } ],\n  _args: [] }\n# args: []\n```\n\nNumbers:\n\n```shell\n$ FOO_TIMEOUT=5000 node examples/foo.js\n# opts: { timeout: 5000,\n  _order: [ { key: 'timeout', value: 5000, from: 'env' } ],\n  _args: [] }\n# args: []\n\n$ FOO_TIMEOUT=blarg node examples/foo.js\nfoo: error: arg for \"FOO_TIMEOUT\" is not a positive integer: \"blarg\"\n```\n\nWith the `includeEnv: true` config to `parser.help()` the environment\nvariable can also be included in **help output**:\n\n    usage: node foo.js [OPTIONS]\n    options:\n        --version             Print tool version and exit.\n        -h, --help            Print this help and exit.\n        -v, --verbose         Verbose output. Use multiple times for more verbose.\n                              Environment: FOO_VERBOSE=1\n        -f FILE, --file=FILE  File to process\n\n\n# Bash completion\n\nDashdash provides a simple way to create a Bash completion file that you\ncan place in your \"bash_completion.d\" directory -- sometimes that is\n\"/usr/local/etc/bash_completion.d/\"). Features:\n\n- Support for short and long opts\n- Support for knowing which options take arguments\n- Support for subcommands (e.g. 'git log <TAB>' to show just options for the\n  log subcommand). See\n  [node-cmdln](https://github.com/trentm/node-cmdln#bash-completion) for\n  how to integrate that.\n- Does the right thing with \"--\" to stop options.\n- Custom optarg and arg types for custom completions.\n\nDashdash will return bash completion file content given a parser instance:\n\n    var parser = dashdash.createParser({options: options});\n    console.log( parser.bashCompletion({name: 'mycli'}) );\n\nor directly from a `options` array of options specs:\n\n    var code = dashdash.bashCompletionFromOptions({\n        name: 'mycli',\n        options: OPTIONS\n    });\n\nWrite that content to \"/usr/local/etc/bash_completion.d/mycli\" and you will\nhave Bash completions for `mycli`. Alternatively you can write it to\nany file (e.g. \"~/.bashrc\") and source it.\n\nYou could add a `--completion` hidden option to your tool that emits the\ncompletion content and document for your users to call that to install\nBash completions.\n\nSee [examples/ddcompletion.js](examples/ddcompletion.js) for a complete\nexample, including how one can define bash functions for completion of custom\noption types. Also see [node-cmdln](https://github.com/trentm/node-cmdln) for\nhow it uses this for Bash completion for full multi-subcommand tools.\n\n- TODO: document specExtra\n- TODO: document includeHidden\n- TODO: document custom types, `function complete\\_FOO` guide, completionType\n- TODO: document argtypes\n\n\n# Parser config\n\nParser construction (i.e. `dashdash.createParser(CONFIG)`) takes the\nfollowing fields:\n\n- `options` (Array of option specs). Required. See the\n  [Option specs](#option-specs) section below.\n\n- `interspersed` (Boolean). Optional. Default is true. If true this allows\n  interspersed arguments and options. I.e.:\n\n        node ./tool.js -v arg1 arg2 -h   # '-h' is after interspersed args\n\n  Set it to false to have '-h' **not** get parsed as an option in the above\n  example.\n\n- `allowUnknown` (Boolean).  Optional.  Default is false.  If false, this causes\n  unknown arguments to throw an error.  I.e.:\n\n        node ./tool.js -v arg1 --afe8asefksjefhas\n\n  Set it to true to treat the unknown option as a positional\n  argument.\n\n  **Caveat**: When a shortopt group, such as `-xaz` contains a mix of\n  known and unknown options, the *entire* group is passed through\n  unmolested as a positional argument.\n\n  Consider if you have a known short option `-a`, and parse the\n  following command line:\n\n        node ./tool.js -xaz\n\n  where `-x` and `-z` are unknown.  There are multiple ways to\n  interpret this:\n\n    1. `-x` takes a value: `{x: 'az'}`\n    2. `-x` and `-z` are both booleans: `{x:true,a:true,z:true}`\n\n  Since dashdash does not know what `-x` and `-z` are, it can't know\n  if you'd prefer to receive `{a:true,_args:['-x','-z']}` or\n  `{x:'az'}`, or `{_args:['-xaz']}`. Leaving the positional arg unprocessed\n  is the easiest mistake for the user to recover from.\n\n\n# Option specs\n\nExample using all fields (required fields are noted):\n\n```javascript\n{\n    names: ['file', 'f'],       // Required (one of `names` or `name`).\n    type: 'string',             // Required.\n    completionType: 'filename',\n    env: 'MYTOOL_FILE',\n    help: 'Config file to load before running \"mytool\"',\n    helpArg: 'PATH',\n    helpWrap: false,\n    default: path.resolve(process.env.HOME, '.mytoolrc')\n}\n```\n\nEach option spec in the `options` array must/can have the following fields:\n\n- `name` (String) or `names` (Array). Required. These give the option name\n  and aliases. The first name (if more than one given) is the key for the\n  parsed `opts` object.\n\n- `type` (String). Required. One of:\n\n    - bool\n    - string\n    - number\n    - integer\n    - positiveInteger\n    - date (epoch seconds, e.g. 1396031701, or ISO 8601 format\n      `YYYY-MM-DD[THH:MM:SS[.sss][Z]]`, e.g. \"2014-03-28T18:35:01.489Z\")\n    - arrayOfBool\n    - arrayOfString\n    - arrayOfNumber\n    - arrayOfInteger\n    - arrayOfPositiveInteger\n    - arrayOfDate\n\n  FWIW, these names attempt to match with asserts on\n  [assert-plus](https://github.com/mcavage/node-assert-plus).\n  You can add your own custom option types with `dashdash.addOptionType`.\n  See below.\n\n- `completionType` (String). Optional. This is used for [Bash\n  completion](#bash-completion) for an option argument. If not specified,\n  then the value of `type` is used. Any string may be specified, but only the\n  following values have meaning:\n\n    - `none`: Provide no completions.\n    - `file`: Bash's default completion (i.e. `complete -o default`), which\n      includes filenames.\n    - *Any string FOO for which a `function complete_FOO` Bash function is\n      defined.* This is for custom completions for a given tool. Typically\n      these custom functions are provided in the `specExtra` argument to\n      `dashdash.bashCompletionFromOptions()`. See\n      [\"examples/ddcompletion.js\"](examples/ddcompletion.js) for an example.\n\n- `env` (String or Array of String). Optional. An environment variable name\n  (or names) that can be used as a fallback for this option. For example,\n  given a \"foo.js\" like this:\n\n        var options = [{names: ['dry-run', 'n'], env: 'FOO_DRY_RUN'}];\n        var opts = dashdash.parse({options: options});\n\n  Both `node foo.js --dry-run` and `FOO_DRY_RUN=1 node foo.js` would result\n  in `opts.dry_run = true`.\n\n  An environment variable is only used as a fallback, i.e. it is ignored if\n  the associated option is given in `argv`.\n\n- `help` (String). Optional. Used for `parser.help()` output.\n\n- `helpArg` (String). Optional. Used in help output as the placeholder for\n  the option argument, e.g. the \"PATH\" in:\n\n        ...\n        -f PATH, --file=PATH    File to process\n        ...\n\n- `helpWrap` (Boolean). Optional, default true. Set this to `false` to have\n  that option's `help` *not* be text wrapped in `<parser>.help()` output.\n\n- `default`. Optional. A default value used for this option, if the\n  option isn't specified in argv.\n\n- `hidden` (Boolean). Optional, default false. If true, help output will not\n  include this option. See also the `includeHidden` option to\n  `bashCompletionFromOptions()` for [Bash completion](#bash-completion).\n\n\n# Option group headings\n\nYou can add headings between option specs in the `options` array.  To do so,\nsimply add an object with only a `group` property -- the string to print as\nthe heading for the subsequent options in the array.  For example:\n\n```javascript\nvar options = [\n    {\n        group: 'Armament Options'\n    },\n    {\n        names: [ 'weapon', 'w' ],\n        type: 'string'\n    },\n    {\n        group: 'General Options'\n    },\n    {\n        names: [ 'help', 'h' ],\n        type: 'bool'\n    }\n];\n...\n```\n\nNote: You can use an empty string, `{group: ''}`, to get a blank line in help\noutput between groups of options.\n\n\n# Help config\n\nThe `parser.help(...)` function is configurable as follows:\n\n        Options:\n          Armament Options:\n        ^^  -w WEAPON, --weapon=WEAPON  Weapon with which to crush. One of: |\n       /                                sword, spear, maul                  |\n      /   General Options:                                                  |\n     /      -h, --help                  Print this help and exit.           |\n    /   ^^^^                            ^                                   |\n    \\       `-- indent                   `-- helpCol              maxCol ---'\n     `-- headingIndent\n\n- `indent` (Number or String). Default 4. Set to a number (for that many\n  spaces) or a string for the literal indent.\n- `headingIndent` (Number or String). Default half length of `indent`. Set to\n  a number (for that many spaces) or a string for the literal indent. This\n  indent applies to group heading lines, between normal option lines.\n- `nameSort` (String). Default is 'length'. By default the names are\n  sorted to put the short opts first (i.e. '-h, --help' preferred\n  to '--help, -h'). Set to 'none' to not do this sorting.\n- `maxCol` (Number). Default 80. Note that reflow is just done on whitespace\n  so a long token in the option help can overflow maxCol.\n- `helpCol` (Number). If not set a reasonable value will be determined\n  between `minHelpCol` and `maxHelpCol`.\n- `minHelpCol` (Number). Default 20.\n- `maxHelpCol` (Number). Default 40.\n- `helpWrap` (Boolean). Default true. Set to `false` to have option `help`\n  strings *not* be textwrapped to the helpCol..maxCol range.\n- `includeEnv` (Boolean). Default false. If the option has associated\n  environment variables (via the `env` option spec attribute), then\n  append mentioned of those envvars to the help string.\n- `includeDefault` (Boolean). Default false. If the option has a default value\n  (via the `default` option spec attribute, or a default on the option's type),\n  then a \"Default: VALUE\" string will be appended to the help string.\n\n\n# Custom option types\n\nDashdash includes a good starter set of option types that it will parse for\nyou. However, you can add your own via:\n\n    var dashdash = require('dashdash');\n    dashdash.addOptionType({\n        name: '...',\n        takesArg: true,\n        helpArg: '...',\n        parseArg: function (option, optstr, arg) {\n            ...\n        },\n        array: false,  // optional\n        arrayFlatten: false,  // optional\n        default: ...,   // optional\n        completionType: ...  // optional\n    });\n\nFor example, a simple option type that accepts 'yes', 'y', 'no' or 'n' as\na boolean argument would look like:\n\n    var dashdash = require('dashdash');\n\n    function parseYesNo(option, optstr, arg) {\n        var argLower = arg.toLowerCase()\n        if (~['yes', 'y'].indexOf(argLower)) {\n            return true;\n        } else if (~['no', 'n'].indexOf(argLower)) {\n            return false;\n        } else {\n            throw new Error(format(\n                'arg for \"%s\" is not \"yes\" or \"no\": \"%s\"',\n                optstr, arg));\n        }\n    }\n\n    dashdash.addOptionType({\n        name: 'yesno'\n        takesArg: true,\n        helpArg: '<yes|no>',\n        parseArg: parseYesNo\n    });\n\n    var options = {\n        {names: ['answer', 'a'], type: 'yesno'}\n    };\n    var opts = dashdash.parse({options: options});\n\nSee \"examples/custom-option-\\*.js\" for other examples.\nSee the `addOptionType` block comment in \"lib/dashdash.js\" for more details.\nPlease let me know [with an\nissue](https://github.com/trentm/node-dashdash/issues/new) if you write a\ngenerally useful one.\n\n\n\n# Why\n\nWhy another node.js option parsing lib?\n\n- `nopt` really is just for \"tools like npm\". Implicit opts (e.g. '--no-foo'\n  works for every '--foo'). Can't disable abbreviated opts. Can't do multiple\n  usages of same opt, e.g. '-vvv' (I think). Can't do grouped short opts.\n\n- `optimist` has surprise interpretation of options (at least to me).\n  Implicit opts mean ambiguities and poor error handling for fat-fingering.\n  `process.exit` calls makes it hard to use as a library.\n\n- `optparse` Incomplete docs. Is this an attempted clone of Python's `optparse`.\n  Not clear. Some divergence. `parser.on(\"name\", ...)` API is weird.\n\n- `argparse` Dep on underscore. No thanks just for option processing.\n  `find lib | wc -l` -> `26`. Overkill.\n  Argparse is a bit different anyway. Not sure I want that.\n\n- `posix-getopt` No type validation. Though that isn't a killer. AFAIK can't\n  have a long opt without a short alias. I.e. no `getopt_long` semantics.\n  Also, no whizbang features like generated help output.\n\n- [\"commander.js\"](https://github.com/visionmedia/commander.js): I wrote\n  [a critique](http://trentm.com/2014/01/a-critique-of-commander-for-nodejs.html)\n  a while back. It seems fine, but last I checked had\n  [an outstanding bug](https://github.com/visionmedia/commander.js/pull/121)\n  that would prevent me from using it.\n\n\n# License\n\nMIT. See LICENSE.txt.\n", "maintainers": [{"name": "trentm", "email": "<EMAIL>"}], "time": {"modified": "2022-06-14T18:39:35.503Z", "created": "2013-02-28T08:00:19.903Z", "1.0.0": "2013-02-28T08:00:21.323Z", "1.0.1": "2013-02-28T09:02:35.313Z", "1.0.2": "2013-03-01T04:27:17.922Z", "1.1.0": "2013-03-08T06:50:05.004Z", "1.2.0": "2013-03-08T17:23:28.716Z", "1.2.1": "2013-04-24T04:05:07.026Z", "1.3.0": "2013-06-01T04:12:06.242Z", "1.3.1": "2013-06-03T20:13:00.497Z", "1.3.2": "2013-06-03T20:32:11.954Z", "1.4.0": "2013-11-25T23:14:34.971Z", "1.5.0": "2014-03-29T05:28:13.099Z", "1.6.0": "2014-06-04T22:13:05.771Z", "1.7.0": "2014-09-25T06:58:14.126Z", "1.7.1": "2014-11-19T23:44:01.631Z", "1.7.2": "2015-01-05T21:54:34.068Z", "1.7.3": "2015-02-05T08:49:20.454Z", "1.8.0": "2015-03-26T04:08:11.335Z", "1.9.0": "2015-04-07T16:09:52.766Z", "1.10.0": "2015-04-13T05:51:22.989Z", "1.10.1": "2015-09-11T22:39:55.045Z", "1.11.0": "2015-12-31T17:03:38.561Z", "1.12.0": "2016-01-08T18:42:31.068Z", "1.12.1": "2016-01-09T00:26:34.390Z", "1.12.2": "2016-01-18T19:18:23.688Z", "1.13.0": "2016-02-12T03:48:10.373Z", "1.13.1": "2016-04-22T19:59:58.788Z", "1.14.0": "2016-06-02T05:59:31.595Z", "1.14.1": "2016-11-22T22:33:40.575Z", "2.0.0": "2020-09-25T23:05:04.302Z"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://trentm.com"}, "repository": {"type": "git", "url": "git://github.com/trentm/node-dashdash.git"}, "users": {"jacques": true, "groundwater": true, "guumaster": true, "j.su": true, "scottfreecode": true, "mojaray2k": true, "shuoshubao": true, "rijnhard": true, "ganeshkbhat": true}, "bugs": {"url": "https://github.com/trentm/node-dashdash/issues"}, "readmeFilename": "README.md", "keywords": ["option", "parser", "parsing", "cli", "command", "args", "bash", "completion"], "homepage": "https://github.com/trentm/node-dashdash#readme", "license": "MIT"}