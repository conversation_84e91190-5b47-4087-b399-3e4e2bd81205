{"_id": "require-from-string", "_rev": "24-c2cdc326408bbec716917c1afa732f95", "name": "require-from-string", "description": "Require module from string", "dist-tags": {"latest": "2.0.2"}, "versions": {"1.0.0": {"name": "require-from-string", "version": "1.0.0", "description": "Require module from string", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/floatdrop/require-from-string"}, "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "github.com/floatdrop"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js"], "keywords": [], "dependencies": {}, "devDependencies": {"mocha": "*"}, "gitHead": "247ebd2cccc5aabde8876312dfed1dc1e8e6bedd", "bugs": {"url": "https://github.com/floatdrop/require-from-string/issues"}, "homepage": "https://github.com/floatdrop/require-from-string", "_id": "require-from-string@1.0.0", "_shasum": "05f10696e6ff228172c4e313a8bdebd95534548a", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "floatdrop", "email": "<EMAIL>"}, "maintainers": [{"name": "floatdrop", "email": "<EMAIL>"}], "dist": {"shasum": "05f10696e6ff228172c4e313a8bdebd95534548a", "tarball": "https://registry.npmjs.org/require-from-string/-/require-from-string-1.0.0.tgz", "integrity": "sha512-NPk19C5PqAFl1JOHwyOvtiB7QHiMpbzdiq/n8VeKOE75L0wMCKAoQRLfVE/HqnFrwmMyl5bVFfFLlr0M81QOjQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDzYLnqduLTbVYBqrrY/xErtb+YTp8Etmnxqz1NqN8N+QIgNLWP1aBHxO14iiqbmC4XYeiYvsFw/8fpbzXPoWT23sw="}]}, "directories": {}}, "1.0.1": {"name": "require-from-string", "version": "1.0.1", "description": "Require module from string", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/floatdrop/require-from-string"}, "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "github.com/floatdrop"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js"], "keywords": [], "dependencies": {}, "devDependencies": {"mocha": "*"}, "gitHead": "b869afb66be27941b362c44381f93bb3baf2422f", "bugs": {"url": "https://github.com/floatdrop/require-from-string/issues"}, "homepage": "https://github.com/floatdrop/require-from-string", "_id": "require-from-string@1.0.1", "_shasum": "b5d140f894b70feacb8f9a7209588b51d0c7f629", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.0", "_npmUser": {"name": "floatdrop", "email": "<EMAIL>"}, "dist": {"shasum": "b5d140f894b70feacb8f9a7209588b51d0c7f629", "tarball": "https://registry.npmjs.org/require-from-string/-/require-from-string-1.0.1.tgz", "integrity": "sha512-wKgFU9RE/yK9AHv9JTlpKtqF0prdiIPa4PIBL95Z7CjW8INyTsBGn3cFt3G0Dw4hMbxvwVe9+1ZnYYoGPJqX6g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGRvTq1OGMH0bpQTWKSSXoIykEBshVsVY2T3siW811wkAiEAhEfiDcJTKgX6jpM56ZaumPxSk+6hPBG/sWOT1lGMdGg="}]}, "maintainers": [{"name": "floatdrop", "email": "<EMAIL>"}], "directories": {}}, "1.0.2": {"name": "require-from-string", "version": "1.0.2", "description": "Require module from string", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/floatdrop/require-from-string"}, "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "github.com/floatdrop"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js"], "keywords": [], "dependencies": {}, "devDependencies": {"mocha": "*"}, "gitHead": "0ce4cc5c5eb0487eddd6463ceafff7fcf0d3d459", "bugs": {"url": "https://github.com/floatdrop/require-from-string/issues"}, "homepage": "https://github.com/floatdrop/require-from-string", "_id": "require-from-string@1.0.2", "_shasum": "83ce7d5b4671d89afb2ee0287a51a2a5b997acfd", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "floatdrop", "email": "<EMAIL>"}, "maintainers": [{"name": "floatdrop", "email": "<EMAIL>"}], "dist": {"shasum": "83ce7d5b4671d89afb2ee0287a51a2a5b997acfd", "tarball": "https://registry.npmjs.org/require-from-string/-/require-from-string-1.0.2.tgz", "integrity": "sha512-zceVE5W40yFQ7cNFgSkOKMgE00tXoF58RFe8NqhTVWEI9azOieuvaO8IPDvUG9Ziq3UOh5Vku3+RHfD6M/AaGQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCS5LeiZ8cVP01bX2gjHT5pB1Mfrm3H/AOjESrhgLGhwwIgMNNL32NWrC9DnXlrk0Ei0o0T64JPUWV0ENpCb+oJXjs="}]}, "directories": {}}, "1.1.0": {"name": "require-from-string", "version": "1.1.0", "description": "Require module from string", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/floatdrop/require-from-string"}, "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "github.com/floatdrop"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js"], "keywords": [], "dependencies": {}, "devDependencies": {"mocha": "*"}, "gitHead": "42b400d921efa55874cc301e59282a2d2a442715", "bugs": {"url": "https://github.com/floatdrop/require-from-string/issues"}, "homepage": "https://github.com/floatdrop/require-from-string", "_id": "require-from-string@1.1.0", "_shasum": "91610638dfe986818c591f585d1085b386503289", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.0", "_npmUser": {"name": "floatdrop", "email": "<EMAIL>"}, "dist": {"shasum": "91610638dfe986818c591f585d1085b386503289", "tarball": "https://registry.npmjs.org/require-from-string/-/require-from-string-1.1.0.tgz", "integrity": "sha512-KIOCdVHbUyDA3aDuoIHcuLsCsO00m/rwnOm1kFN2NNTnMpI7lkVnkzo4utM4jVm7BCdb4D+7++xCdgx7hpA6ag==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDxgrH/lsoVWpHAc1NI21atxAB1tdp00/4qaH/MhaRB4AiEA4Ej5LtMD1KsKlabY9oAl9MaTqxs9JhmtTFU31sr61SA="}]}, "maintainers": [{"name": "floatdrop", "email": "<EMAIL>"}], "directories": {}}, "1.2.0": {"name": "require-from-string", "version": "1.2.0", "description": "Require module from string", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/floatdrop/require-from-string.git"}, "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "github.com/floatdrop"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js"], "keywords": [], "dependencies": {}, "devDependencies": {"mocha": "*"}, "gitHead": "8610539617336b3f60376c040a090a9797645bfe", "bugs": {"url": "https://github.com/floatdrop/require-from-string/issues"}, "homepage": "https://github.com/floatdrop/require-from-string#readme", "_id": "require-from-string@1.2.0", "_shasum": "12a23f7e69c9d6d16a4517650f8b59629777e3a0", "_from": ".", "_npmVersion": "2.15.1", "_nodeVersion": "4.4.3", "_npmUser": {"name": "floatdrop", "email": "<EMAIL>"}, "dist": {"shasum": "12a23f7e69c9d6d16a4517650f8b59629777e3a0", "tarball": "https://registry.npmjs.org/require-from-string/-/require-from-string-1.2.0.tgz", "integrity": "sha512-7swt+9ZwilLV4efl9ZuYsG1CtT98eusv8j9hfzSoD7ZvIGBzl8xZQn8RWsvxavst41WQqVbWSfM9Mrgf5lp5tA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAXM5yQT268yhzaecdkBK4lHhzD4dv1tGeRuAbvfF5jrAiA42kgmMNKwVL07eLionZofGeHokIaBIF9X3U1Xdds0FQ=="}]}, "maintainers": [{"name": "floatdrop", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/require-from-string-1.2.0.tgz_1462260731186_0.739558148663491"}, "directories": {}}, "1.2.1": {"name": "require-from-string", "version": "1.2.1", "description": "Require module from string", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/floatdrop/require-from-string.git"}, "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "github.com/floatdrop"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js"], "keywords": [], "dependencies": {}, "devDependencies": {"mocha": "*"}, "gitHead": "b81e995c6ff82fbf71d9ee7a9990b10794fecb98", "bugs": {"url": "https://github.com/floatdrop/require-from-string/issues"}, "homepage": "https://github.com/floatdrop/require-from-string#readme", "_id": "require-from-string@1.2.1", "_shasum": "529c9ccef27380adfec9a2f965b649bbee636418", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.6.0", "_npmUser": {"name": "floatdrop", "email": "<EMAIL>"}, "dist": {"shasum": "529c9ccef27380adfec9a2f965b649bbee636418", "tarball": "https://registry.npmjs.org/require-from-string/-/require-from-string-1.2.1.tgz", "integrity": "sha512-H7AkJWMobeskkttHyhTVtS0fxpFLjxhbfMa6Bk3wimP7sdPRGL3EyCg3sAQenFfAe+xQ+oAc85Nmtvq0ROM83Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDpd7vHXOYuo8RA/lL8DvBGqc2nFvUpmf0Rj4WHXevtkgIgGSbXabOE7T7ktq/vf+hG6CgNWqjw7fMJI/uo4fBSNs0="}]}, "maintainers": [{"name": "floatdrop", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/require-from-string-1.2.1.tgz_1475350323439_0.3740670408587903"}, "directories": {}}, "2.0.0": {"name": "require-from-string", "version": "2.0.0", "description": "Require module from string", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/floatdrop/require-from-string.git"}, "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "github.com/floatdrop"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js"], "keywords": [], "dependencies": {}, "devDependencies": {"mocha": "*"}, "gitHead": "ce8ae318921a649ede77fd13c1b1851889923786", "bugs": {"url": "https://github.com/floatdrop/require-from-string/issues"}, "homepage": "https://github.com/floatdrop/require-from-string#readme", "_id": "require-from-string@2.0.0", "_shasum": "620588727e5941acbf467db17728b9cab5176211", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.7.1", "_npmUser": {"name": "floatdrop", "email": "<EMAIL>"}, "dist": {"shasum": "620588727e5941acbf467db17728b9cab5176211", "tarball": "https://registry.npmjs.org/require-from-string/-/require-from-string-2.0.0.tgz", "integrity": "sha512-VX8ZAJPwCYe0KVhHyRfY8xLjqfwbvoqssql5vq6JPBF9dOrRkdalCdDQxQ/pwvff7l0Ft3GpeFIqzvrI4qQxmw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGQ2q4Lqrtxs9fB+lYElKC5x49SdZyGUiy07A2gUfxoCAiEA/BFPfHEOPts8mQdoEaQgi5HE8kJq/92PuuqBtr/Anwk="}]}, "maintainers": [{"name": "floatdrop", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/require-from-string-2.0.0.tgz_1505545473212_0.1633690781891346"}, "directories": {}}, "2.0.1": {"name": "require-from-string", "version": "2.0.1", "description": "Require module from string", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/floatdrop/require-from-string.git"}, "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "github.com/floatdrop"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js"], "keywords": [], "dependencies": {}, "devDependencies": {"mocha": "*"}, "gitHead": "cbe9197bfb08e7354b2e84cd060ba36fc476e1d3", "bugs": {"url": "https://github.com/floatdrop/require-from-string/issues"}, "homepage": "https://github.com/floatdrop/require-from-string#readme", "_id": "require-from-string@2.0.1", "_shasum": "c545233e9d7da6616e9d59adfb39fc9f588676ff", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.7.1", "_npmUser": {"name": "floatdrop", "email": "<EMAIL>"}, "dist": {"shasum": "c545233e9d7da6616e9d59adfb39fc9f588676ff", "tarball": "https://registry.npmjs.org/require-from-string/-/require-from-string-2.0.1.tgz", "integrity": "sha512-McvI+pnbFkdwzAcXx2b8WKmSo9ucq6mlVcLd2ZgxuwOknFtj5dz9PTleVfMVTgcp5ucJ8CMDqYkJ3jDZImvcxg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDYDVuCZAdTg4i4CkF9ALbEwb1epiOGL7/8rCkwRGwfiAIgEJTKgxOB1rOsfMf46tMJGvwSkcYrFbEL7n5Ute5iVMA="}]}, "maintainers": [{"name": "floatdrop", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/require-from-string-2.0.1.tgz_1505631156427_0.9107523618731648"}, "directories": {}}, "2.0.2": {"name": "require-from-string", "version": "2.0.2", "description": "Require module from string", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/floatdrop/require-from-string.git"}, "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "github.com/floatdrop"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js"], "keywords": [], "dependencies": {}, "devDependencies": {"mocha": "*"}, "gitHead": "d1575a49065eb7a49b86b4de963f04f1a14dfd60", "bugs": {"url": "https://github.com/floatdrop/require-from-string/issues"}, "homepage": "https://github.com/floatdrop/require-from-string#readme", "_id": "require-from-string@2.0.2", "_npmVersion": "5.6.0", "_nodeVersion": "9.5.0", "_npmUser": {"name": "floatdrop", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw==", "shasum": "89a7fdd938261267318eafe14f9c32e598c36909", "tarball": "https://registry.npmjs.org/require-from-string/-/require-from-string-2.0.2.tgz", "fileCount": 4, "unpackedSize": 3422, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE2jrMs9LEA5YwPbB+k86fiEWbTiZrILb/xQp2cSIEePAiEApTYU69t0eG1qFeeVayYDlVRtYxsT4vFi9gJB5pqvtXE="}]}, "maintainers": [{"name": "floatdrop", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/require-from-string_2.0.2_1523267387201_0.3738099631330949"}, "_hasShrinkwrap": false}}, "readme": "# require-from-string [![Build Status](https://travis-ci.org/floatdrop/require-from-string.svg?branch=master)](https://travis-ci.org/floatdrop/require-from-string)\n\nLoad module from string in Node.\n\n## Install\n\n```\n$ npm install --save require-from-string\n```\n\n\n## Usage\n\n```js\nvar requireFromString = require('require-from-string');\n\nrequireFromString('module.exports = 1');\n//=> 1\n```\n\n\n## API\n\n### requireFromString(code, [filename], [options])\n\n#### code\n\n*Required*  \nType: `string`\n\nModule code.\n\n#### filename\nType: `string`  \nDefault: `''`\n\nOptional filename.\n\n\n#### options\nType: `object`\n\n##### appendPaths\nType: `Array`\n\nList of `paths`, that will be appended to module `paths`. Useful, when you want\nto be able require modules from these paths.\n\n##### prependPaths\nType: `Array`\n\nSame as `appendPaths`, but paths will be prepended.\n\n## License\n\nMIT © [Vsevolo<PERSON> Struk<PERSON>sky](http://github.com/floatdrop)\n", "maintainers": [{"name": "floatdrop", "email": "<EMAIL>"}], "time": {"modified": "2022-06-26T11:37:26.323Z", "created": "2015-07-18T15:08:53.362Z", "1.0.0": "2015-07-18T15:08:53.362Z", "1.0.1": "2015-11-03T08:09:49.990Z", "1.0.2": "2015-11-06T19:08:43.545Z", "1.1.0": "2015-11-07T11:43:15.008Z", "1.2.0": "2016-05-03T07:32:12.394Z", "1.2.1": "2016-10-01T19:32:05.278Z", "2.0.0": "2017-09-16T07:04:34.217Z", "2.0.1": "2017-09-17T06:52:37.555Z", "2.0.2": "2018-04-09T09:49:47.301Z"}, "homepage": "https://github.com/floatdrop/require-from-string#readme", "keywords": [], "repository": {"type": "git", "url": "git+https://github.com/floatdrop/require-from-string.git"}, "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "github.com/floatdrop"}, "bugs": {"url": "https://github.com/floatdrop/require-from-string/issues"}, "license": "MIT", "readmeFilename": "readme.md", "users": {"nichoth": true, "morewry": true, "eshinn": true}}