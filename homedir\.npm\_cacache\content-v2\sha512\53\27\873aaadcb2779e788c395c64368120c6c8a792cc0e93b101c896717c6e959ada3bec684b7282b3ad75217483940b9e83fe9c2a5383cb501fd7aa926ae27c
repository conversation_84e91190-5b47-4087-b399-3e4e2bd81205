{"_id": "@esbuild/linux-s390x", "_rev": "95-45f147f2d759017c57296905472831ea", "name": "@esbuild/linux-s390x", "dist-tags": {"latest": "0.25.5"}, "versions": {"0.15.18": {"name": "@esbuild/linux-s390x", "version": "0.15.18", "license": "MIT", "_id": "@esbuild/linux-s390x@0.15.18", "maintainers": [{"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "fe01343a525b4515f7a873d1d1d8bf485b0c8454", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.15.18.tgz", "fileCount": 3, "integrity": "sha512-+cqKgNHlMc1IUA7PabMDPuDQsEgmw/X9toCY5eLzg5quj1GbnxX4G4YIareYEJy6GTVjimGEiAdcTE354J20Vw==", "signatures": [{"sig": "MEYCIQCP0wY5MRLlhvTytMmySFbe0JvaaKnPRVa/uJJxuIkV1AIhAOTZuJ2ZgY7tvZOwRvNwreC4VqbiH/grYnTkg6glzf3S", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8913404, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjjoSdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoe/w//eRmYc/2ZL2QDmb2JPSbnxc/23LN2LoXuXM7fMmhN0exLpgxF\r\ntLP+sOZKO5cBVJ9Fo3Gd6O4ZlPLZQWrRcPHlxMDtrq+pE/KhuGD4KanZEFXK\r\nyDCa4sfXKn1NLJvUcuxEKCQH/je5kIaF6MHVeUthX2O6YO7CeCMITUC71dyX\r\n3VMAWXjLXT0jayeDkAjsUW2lYcYB8pMmV1c0IrRGrqkHzHxPsuxAtT7I/J7a\r\n7Vr6LMHzD1BX3m/sXY3KGATcoKsUv9f1STXDjDwq52T0wfTQFoNCgYslEUBG\r\ncmocBjjJpW7f0I5H/X5eUcrxxaTLZ3FJQ4yMmwHobSotIkG8puFl/sNTqQXg\r\nFy9lleWIeJ8SFuk16xkn+lzE8OtwBAI/m6hvQ3NgIHRrAXQUGb+kb+FJJmbw\r\nqrsf1Y2MYmxJ32DI2DtUbuLWBFXAujj4Sto6Luu0zlxKDG7clKz9EXtnNz3c\r\n7XwUzVWpwQ8Jx/3a81rtsVrtn/tqKv3rgtpWy/rye8n8/HFdIeRGkBA60akm\r\nkZ1SPbjO8VaV07IakgqFkxisVGVAuzX63wagJQys/Weec7ebbgsjIPidN89d\r\nRIsFYy16MMGXc6JyNwQkFmLvd6meNzT9GRqdfCs8Pqnf67D4L10qriIgsWqV\r\n1q2VMPaDLL411r0POAUeWgc9bM8XiCgLsEI=\r\n=owqJ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "07e607164c880e03e13f86aa50a58cd6d44ec084", "_npmUser": {"name": "esbuild", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.15.18_1670284445647_0.31468527241834576", "host": "s3://npm-registry-packages"}}, "0.16.0": {"name": "@esbuild/linux-s390x", "version": "0.16.0", "license": "MIT", "_id": "@esbuild/linux-s390x@0.16.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "d5bc0221eec59649d09f3ce211b2aea5cd5b71ca", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.16.0.tgz", "fileCount": 3, "integrity": "sha512-d/4JQcGXzYrI126ipVunENYyjHs69MpOMvnEkbsW7zyDUuEinWkKwBhbqUwo3cDeg2IvUfFAUoI6PMH3XVkkAA==", "signatures": [{"sig": "MEUCIQDl+lQmYQY1B6g+KiOUUtuR8za8gBuAmR2uyl7rPo9y1AIgO1NwieUAIYQtlnGC7eSHskoBy5Fj8WVa3h5OmMXDlt0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8913403, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkA68ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo38xAAoZO/Qh+X+kz/pMEarJkEfAeSJ+NcYIdMARfP7UVm3tb76+f9\r\nP1iLrmFHDz4KmjzrIOPYzJiyQ0b2QheZ5QTv739KUQq5thOdVB32F6uKWR1B\r\nYlvKvaylq2UxlGX4nCfAX41OOIi33yoFKn8ee6k99+vrRFSVoyhOiKmpnEXW\r\n1YoaLSQ39wZUwc0HryCiwKNgSicppQqRPLzAF3xLTz4qMwaodg0e+MWScwwh\r\nZu8Rz5aTBv3TP9fLU/zQYKfMFIF0upTnT60+I0u69r1x6WTCHN7kR8YbYdCG\r\nz6sK8Ko7hwN0lS6FxCZ+9s9TS/ocNNgDM9W7jqcBbBBWcQy7eNWydg57U76n\r\nhIroRwnKJV1cNVSrgFdsazw/22IjCgbRensgBR6BTuZjxaHBeDWhqG8E8iTW\r\nIN1DM4bDqM94beQ5sEjGmhoIJETbiq8DAtLZjJYDhiSB5xGWXA77im3sChSO\r\nirhBMkW5Zh+1rKos/tcZpCqOkRdoSGSUKomo5Z4wlUvFw8ZUGEGeeBgzwP6V\r\nzR9JoaPBd/ozD7o26HSTv+GYfsrg0rKzCc8Zf6eibdQ4ov41O4t0fOn3mFzS\r\nz+GV3WAsnPtyEXOve1ZQZ6ozhlBxKD8IwqzdeEojZB7rjbq2odPPKssJy9mH\r\n648idkkqHrXNTEJw+StAOvL8yGSiomgG/K0=\r\n=WO60\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "6c8d15d404874fd939d7f4062cc6a660dffdabcb", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.16.0_1670385340626_0.737721162899379", "host": "s3://npm-registry-packages"}}, "0.16.1": {"name": "@esbuild/linux-s390x", "version": "0.16.1", "license": "MIT", "_id": "@esbuild/linux-s390x@0.16.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "9700a2595de1363c60723810a88c83f6aec24006", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.16.1.tgz", "fileCount": 3, "integrity": "sha512-e4txkDfouCcByJacawPh9M6qmF9TyzJ+Y6Sj4L+Iu7pRBaAldSqI/pQym26XBcawVlmyYhLA51JXVlQdyj3Rlg==", "signatures": [{"sig": "MEUCIQC0A/+XOcVPqCO3Z0PTUYsxVG9Mrbm24/NZNJ2RFaSnjwIgNPYLjeN+DjVLJPBwHpiC9dI1lP9F+DilhYvZJH/bb/8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8913403, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkBtGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrYBg//WiPcPA+oB69y70WMMaOAl3l1NX9t/64KVjh4x3WTZ3biMcJ1\r\nqRb6kV0gASWMFAnENC++mzi0AYN+GZQqcH6n/J2iowl6A+K0CEbn9fDdu4LL\r\nJs5RXxWW0Q++a7Tcp5A/uNFnpG/TfFatmv1UQ68NE624NzwSeymQw7r07Oww\r\nyDTdd7UWW91pnj64CsWsUuCz16ApG/G2vNUciOZ977AmGCbFubE4tLtdiXPl\r\noWwihcXFeIlb+SsPlXwOzM0T6a0vOVgb8VsneIoIw0PhskWaAC2rT0jaZnpW\r\nHyq0AlnhsHH5NBo24AnMm5RjWERBPAfqes76t4OhuvFGHWGL2T87NJ6B/l5R\r\nglNwkMW6LgbfaEEOj5E/nOb9k3+YuueaVmaYRXPsw5l6NPA933MXDGe38BHz\r\njZAEb3mlKfq6M9yJ3lte2hZySYO6CEZRdHBBwjUoQHep80PaHjDSzg3g48d5\r\nRuh0zZnYJrQp+SJW+uJbhCabIbhPQTryHmIx1Xf5dNnvAPa7Z3lDXTU18tdt\r\nnOHPAoJVXNpiXsP1L6MncG9Q8sR+Urda8hHO1hI1Sl3cAfxqiaSSS/OHve0Z\r\nV96hHfTXlYTuLYpEBS8pC8Yht3NJvIQwWUWNIcZGbKJuN6oLPwKYzhkDzG5r\r\nOzVnAUgscOQr+LTe66aEDxHoErSrNSLV6Q8=\r\n=WwvY\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "3b62a3680cdd1c9d76bed3e2e60841e371670c35", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.16.1_1670388549747_0.8677867594299558", "host": "s3://npm-registry-packages"}}, "0.16.2": {"name": "@esbuild/linux-s390x", "version": "0.16.2", "license": "MIT", "_id": "@esbuild/linux-s390x@0.16.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "b0fd46338d0b4906cfc7e9c4b1850960432f2f7a", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.16.2.tgz", "fileCount": 3, "integrity": "sha512-5ifr0lshZbLI457Qe6y3MsDYv1cSOJ8awgi0HT14cS59WliT7bDkrr3kmDw/LqGOAPyDvDD+U8s2cFBSENetuA==", "signatures": [{"sig": "MEYCIQD3EBllJCxH0jezCioBmjF/IIlJZIQpS+VZjBjAGLnnSgIhAP89x/P0G5zj1CeG+6c34tf9XamZ5GGDKSs4NfTn3SFn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8913403, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkYuPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrc1A/9E3hRyWqyGGz7l9eljmHthqwOvmxf3iHvrrqDTl0EbS200D+y\r\nYOIOGfvnDHAU7e3bWQuWHyOxM43WRyTIr6qGLXxVg1yOLstXSKO6UVX1REG7\r\naBaAI/sMotevzJLe8wVIEI94jKFa/rPEcYoSkZDUteQgOZAo3GGOnPJNgMYC\r\ns88OVxiPtFvIXK3yJOSWDztya1B3Wk7ViQx5NDQmora0xS3xy9q5t7HPnCZB\r\nIvo0nxQT8LlTZ3bk/RGG36YJivAXTSkKHuOgeciSSwBtqYhg0jtdKdE3qPxh\r\nTpsmj1qwcbcfvdZwoImSuhD+HL9VErRAjZGqbRrCWnvzCUS40gTS1JHY84Kp\r\nu8llf8eeEOpK/941d7Nu4PvJ8aDpYkV385NrOrwOhp7JxALDyT6q96DTIZ24\r\nWVztXzuHeHbLzK7VBx+kZvTAapy9vffgdxUBoxp+8XOnA6/+iUWkai6f1/ej\r\ninVqQcUwB56no/m+3Ge+fNx65dKh2ky+agGku0w9OgMi7C+K49w8aRX7Gbak\r\nyW3sE+ylQYG/p8VdtpI4Vh8SIXlmdFf7MMHE24S9Ms/xvzw78BnbScxFV1kz\r\n3vFXpBdTz9u9yAw98UsMQ/m6e8pPEW420mN+ajfpRjiMIIIN82Nipf54Rzzl\r\nt92NMzFqyKYKZzazEZ/Z4O9bypvFPUpuNYQ=\r\n=fGDR\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "0829d74c0b3a913c0cf6d3f59902871bf63e0d16", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.16.2_1670482831385_0.0923673690973228", "host": "s3://npm-registry-packages"}}, "0.16.3": {"name": "@esbuild/linux-s390x", "version": "0.16.3", "license": "MIT", "_id": "@esbuild/linux-s390x@0.16.3", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "22e10edd6e91f53c2e1f60e46abd453d7794409b", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.16.3.tgz", "fileCount": 3, "integrity": "sha512-NbeREhzSxYwFhnCAQOQZmajsPYtX71Ufej3IQ8W2Gxskfz9DK58ENEju4SbpIj48VenktRASC52N5Fhyf/aliQ==", "signatures": [{"sig": "MEUCIQCWCSVSRoW8hsAOnf4pMGoc+EauGjm1pAmi7dzJMUd5XwIgZA0Yc+dmy0SuCDZyR/NofURCRSPnE4cRL0az8b3qUTo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8913403, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkkWBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoAlg//SSsuv6jRV/zYzFvOGecK/2ar0gJlcI+hPbbGHj46GYrVY9iS\r\nFyRPOXpO9f3LFhUfKZZ6zUkbDIBiyvaqMukGAsIV2oVHKS1cE6dN5YaYN6UN\r\n0K6dMG2HBdjzHPql7lqC/Rfqnkja/aRP2lFpQARGgluL+JtwT2ZrFUn2CRHX\r\nmkaVsKaOFWq9ZVi201KVbb4AYS/zZQMfENZCQnB9cJS06gdHUKE51GR3TiZZ\r\nTjP3/cTHsmdEEreWpoG/olOWbTJ1t4bjbQ2pp2xU6eYVJjPmtLhGuKqxm2Pi\r\np9O+h5tJQK3hgxfCgftVPW7vHBjTQFrI1ZJhP7jT3nP7NBUQxJ8r0TfVT6nl\r\nX34A36XNYcylMY3tk/EDkT8H1WME/YMnn6o4d6rghOzVX6mAqnHPwN9jGtlY\r\no2PJqhz4Ru0aRfKwmv1wUbSV/V/RUNaYH53Uznup5ftBKDvRAYnKaMf1Pl5+\r\niEKAgiby0488paCVwY2ywCAWV60zmb3rSebJRspd5eDMoLFMd9ajS5Ni4Sq3\r\nPUmzxAOTqVK+N4hfZPwh96uGwv+canWl1//TpyjixqTPqvuGdGI9dRdGCTJ4\r\n8XosLU482NwUB1efVp6mkBqUeiT3u4l2vTE5dWR6V7mIWTkoU5hJw8pgCX4b\r\nHnYQozNNRu7gKIZI2BQyWDO41k0Br5VjgxY=\r\n=hMr1\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "8ef19fefc9bfdd28fab95dec3783d3f100f25e3e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.16.3_1670530433630_0.604650053222084", "host": "s3://npm-registry-packages"}}, "0.16.4": {"name": "@esbuild/linux-s390x", "version": "0.16.4", "license": "MIT", "_id": "@esbuild/linux-s390x@0.16.4", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "4cae1f70ac2943f076dd130c3c80d28f57bf75d1", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.16.4.tgz", "fileCount": 3, "integrity": "sha512-pUvPQLPmbEeJRPjP0DYTC1vjHyhrnCklQmCGYbipkep+oyfTn7GTBJXoPodR7ZS5upmEyc8lzAkn2o29wD786A==", "signatures": [{"sig": "MEQCIFgsX3k8dJNS6990f9v/rnlvExkfgTQ6p7aSqSwPKF7BAiA22LtggW6bhs6Upt8RlbQEkQPff5MC8ONCf7p1W7rixQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8913403, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjlAI0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrHeQ//Sqvq8BqmJbxNfvFM7WqX0Q1KACFqGVU4wIrPft/PaLVvqWlV\r\n/Z1ZHTHeGKAVjJsFrWCaZr9YK6n/GEO21eDa/S66GaoGsiOMMlIxRc5l633e\r\nMbeRfhL8R92Ubz+wtq1FxyaO91zfxfbSs+rDjQS6sI3/zPjRYivD6uKxqOCx\r\nDi+UYWsO7hjHa599R/HjhwSDxquftXiM88y6nDrdkdqkIUgIyTkEYbGOg95J\r\nNZb5jz4NjGkjv945AkfsuVpkxk1xFZ3GEU0B2NlMK44iKF+zlSON493DABV2\r\n2Ocv+0Tvw4fhSDNomw8NeF9KI9Zy1veHhOdNdAz2XObY022+NlmMb92fOc7t\r\njClIQ5FPyHZJ9DCwhCD6vh2N3VrweeW+54W7jlaXTfe0oB6ojzCSzUTdC7Yd\r\nuGlUI1nXCnWnrWQ2wtnc8jzWc7zES1mgnYYVWVYIPfRKdoX9c01TGb5LTA2Y\r\nIKtagfxRSPq6bPIIcSm111o/E3tmjDNL3C3OHIoJ3027wmyWRl6xyi/uGUQQ\r\nZUK2el6LoifQFu/jJO1MhoGUg9w46wE/kaRrgN20AoAw+euAXJllLWX574J7\r\nnCl9T+u3YImUYqm5uixyCiwsIgWxsOdyKf0iN+V3gP9wkEsXAOogn9MRjVg0\r\npSHE2+ozSgp2NnvVIOM2+xnd7VuD4YtkvLc=\r\n=b88U\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "643af8ed12345b3a249f1d4c7643c261d95c098c", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.16.4_1670644276118_0.8838590112509603", "host": "s3://npm-registry-packages"}}, "0.16.5": {"name": "@esbuild/linux-s390x", "version": "0.16.5", "license": "MIT", "_id": "@esbuild/linux-s390x@0.16.5", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "18d418673579081e7ec3c376b110cea832efadbc", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.16.5.tgz", "fileCount": 3, "integrity": "sha512-pYY86RiLD1s5RN8q0aMhWD44NiHmAZxv2bSzaNlL63/ibWETld+m6F+MPh9+ZNOqGJw53E/0qHukYI5Lm+1k7A==", "signatures": [{"sig": "MEUCIQCGphekVkaFKw1gUUBvcLurD+xH2ZJqBt+Efmdqy5IkSgIgaxCj9XwPneMy6BbjY9oMySVPWNFNDrPJQk1l2PMaL/M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8978939, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjmLrkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq0xQ/9Fxqq4C289iiQ29ZdMcYZY8QWU2t8JvNOU6r+8u/U58tIW45a\r\n0yIRTSGckiFKjZ0ZQa83rbxp8zozL+et5oQ8NXxll+tMBcxpxpXD0ofkr8ui\r\nLo8gKnzm7uCphg69hwpZQLKnT3005Wwx3iXE1HfZAFetFZT78yyPzhCVNjzd\r\nDX/a/ZF1+PVw8TyLn8Ceh7GqvM7vymJit52cEcH8d29h39svkYUsBPYO/ZgB\r\nbrnMiU1FUIX4P8CpQDp36OLOm7Te9Rv1SR7agm5UfaU03PBXig7kfMW6WA/N\r\nheMELHcE7kD1Imo91MEgNl5t3d/bj6HfSFT4JjeiyuNf/IZpiyOZfSOuox3D\r\niCgj2C+kIpdF98Sev6IqASoY5L1qmvY+d92T86XxD6NCqNWn275r91IGQGOB\r\n9UdSvbuGFrExbMaA78tSE5CthggCGATwDIFlv3NaE/Ap5i0yY1CYWe3zf5V+\r\n/HFGvJgUp/XoiAN9FNYwzaEv+XBMvn2VIL6vOlXXIzhgwUDXDOMNsl6yPPFt\r\nUW+NjdZiVWiMb/mWyN80uOZO7mcxgp+hGUKYbg6V+oKmLw0zwDsXpDeEX+q4\r\nl1z9IO5h5RoKFOf7wJXMbMEezW3c3XVqVTsf9p5mNGv5zXaQ0Fm0ZRaeyTAE\r\nt51uI1/xs7ezMDhSIj+gKAI8bmUZo38bEwY=\r\n=9B8o\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "bb9639c3e1f57a3fdfaadf073a35d87020253f70", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.16.5_1670953700029_0.6331298753667602", "host": "s3://npm-registry-packages"}}, "0.16.6": {"name": "@esbuild/linux-s390x", "version": "0.16.6", "license": "MIT", "_id": "@esbuild/linux-s390x@0.16.6", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "b3c051e15f6fd3f9af801aa2cfe2cad0e069e4ea", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.16.6.tgz", "fileCount": 3, "integrity": "sha512-zt1vo5Zzu1Y+0K64wYIQR1pMVNYDbwDetrWy/4XyD4c+tnZfxGZwzZOmb65LSto8hxAYq5UG6DpHSNJ4zy5F1w==", "signatures": [{"sig": "MEYCIQDH2YQIlSa9uSF/HO+bwO6vL9S6oRAy21c+3QjOzD2VjQIhAIsKyGvntMysa4dGcvRVGn2mzjOiIo7vWdKKjnYSFOHh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8978939, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjmV3pACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoknQ//f8K8T00eMvjItwH+HEGQ1FPrQUKFjnzsOHUqzlLbCeRl8/gC\r\nSMfFE6ZikcQNm6iepCClXz5XxJEQWuoJVXHX0z+YqgHqq70di3LHBvqHPUhL\r\noCyHinPvtC5l5tum0GlIG6CO2Ffn3h5Sjmnae/hzeD5kw4f75bMNAExuyK6b\r\no0VuYi/0pc+O9J8GFBknVGFJyBtX5Si4kGm3JGh4PDq2mWfpeZKhRWdRHOzA\r\n9n7mDMCEwmmxJAAdf1lABgxnTpggAHco7olk9nedDZXXVQuRcpmHm+ezJtnR\r\n4Xjdb4lwMRSpe44MQCWDjE7g54K38PGPLeXDu+1XILDzjO1H/84wdVjQjoQN\r\nsmyPpYkj1gNu4et74UQXW+XNCAxjZp4WUVq9TBM1XniZrbLaeoPYvlzl6wmn\r\njf8ujWlplSG45pt7of0V282AHVkrqqbRLQkPLt5LwpvbxUZgxZRwnH9fwVh7\r\nDyI07lV71ijt9Bf5nFJ0h0xk477D5a+nOtwp5CGZWqWWF381xOcuEMMrD29y\r\n6hAqjM7UUHhQoVdtqMv/Ok6JC6ZxPkkPFb1UMFAq2NMl2t0uWKTlNYvbVKpe\r\nSL6t9T7LJyxTUAsMKMsbzDH7VUGPZ3PPbfSdUV8MAjZMCT67iBG3sMI8bEz5\r\nFyO8kBL0b5K6d9Aolq9cZxm3EwfbUTkDSf8=\r\n=Jp7J\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "ee8e0ddc78114b73836ee1c520d255fd28c1ab1a", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.16.6_1670995433327_0.3619599762279926", "host": "s3://npm-registry-packages"}}, "0.16.7": {"name": "@esbuild/linux-s390x", "version": "0.16.7", "license": "MIT", "_id": "@esbuild/linux-s390x@0.16.7", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "a36fd4605904c49310616dd648c0c25a267a19c0", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.16.7.tgz", "fileCount": 3, "integrity": "sha512-3SA/2VJuv0o1uD7zuqxEP+RrAyRxnkGddq0bwHQ98v1KNlzXD/JvxwTO3T6GM5RH6JUd29RTVQTOJfyzMkkppA==", "signatures": [{"sig": "MEQCIFVq89BCL0OqpnwNfrEnpzgV+WM2QzMzDgDOeII91YIAAiAX6eCiPBtQGlCA2bmoIu3UqrAVs1MBGcEmTvaqlC5JGw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8978939, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjmlKEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqSTg//SKLU85d/LUjP486xzKRRXdQjGMV38wVZzN8C8FOvi4A5rf3R\r\nPyvnAda1jwo5ykrozLWluJtpZCZ0vfB4Li3m/ln8UxeWbJ72WsrAkjvruPWK\r\npDBvLTqtyXKfsE5wk63uZtsIsHDj48qB8DL7c7lZYPEQqCWylDKnsiaJuWSU\r\nREvaV9T/wAWSEkwkSdk0/mMFugJJcqqE5MfwMJjdSfWO8LSzTtRvPqwZoA5E\r\n9XcoLlhebosIAlg/jMzaG+9LgH2/9UPFpDMYyABK1TegMRFrx7qNyPUNf57+\r\n6qfFayl9o/BkSPZEyIpPcsBxEGXKU7PXf5SgBqQbIfAAWqsY77vAe72aTslH\r\n206+xdXQ73JhQ+6T3zvLaWho2GOImFmzVYyfITTestKVcW1VtlnX+Gds2twZ\r\nZJbJjVnWPiD+mP1LrmbXJuk7x8MCOM4B+0tedM83C/I2QXKhKSFTyKJ0J+r3\r\ndTkl+rLigCiKhPq2GnN1G+DuOgl3bWNzWLRMzJ4j5p8pjzEpzkvOY7GX2qLn\r\n8PG2ealwnnYOUkuEQPZDQw0AASaUs7RQwLemfDxtsjSvKBzyiAiFSkb0ia0v\r\nc5Yl/VWhDDtma2xBA6JnHZchOqUOiZSj7pVlnDPqZfUDFkZyBkhPRLSd++jR\r\nLGRX2nYuK+Ay7muMmPE7WGfx6e8eq24oKaA=\r\n=PW9Q\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "be16d813dfaca257af7ba99e458f54d1abdc31a4", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.16.7_1671058051985_0.10468859553621845", "host": "s3://npm-registry-packages"}}, "0.16.8": {"name": "@esbuild/linux-s390x", "version": "0.16.8", "license": "MIT", "_id": "@esbuild/linux-s390x@0.16.8", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "0641fbfd9fe1b7e14172ddd9c9dd396d402d54f9", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.16.8.tgz", "fileCount": 3, "integrity": "sha512-k8RIN4M+GWQAfJ/oGqwxZlpzOyGF8mxp5mH1A1WUJrpSUo4pe0zkq2EoP1KMQbYkjeJi45YsjwK3IOnSoueXbA==", "signatures": [{"sig": "MEYCIQC3VOYdJV43geFd01wv3PtvlzOndWwZqwOuF99BSCc7YwIhAIHFj8l7b/9R/LlMnBlwNnqEnMdp2TSxJW/P58k3aoKy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8978939, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjnQHAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqnfQ//QfGCQeEsbCeFpE2z2TjhoN29hjWEWondMMMC0EiEh/SbtgOv\r\nf6rX4pESHpOaHQWb0VGx53pNuxkTs4nWG63IuV/6AVqwIUNYbPKRcwyOX75v\r\nF5DkcJYNavdwdLGbd6FurMXiKuSxQm10g7SoNA4MGu8JqlFsA3YNR1/5cs+W\r\nbVistfh0L2EZw2O6X59ircInxVC5T7+coAnld/uPYY6rKPbzWHuGxBReGaIq\r\nYLe2Z43EP8W/UmyhFuAelpb5FYSBcdKFQewe+JEsy0C+Wsf7wnm56H6SA/nq\r\nQXYGUmIGdV5NBtY8lHNV0Bs9rGZKJa9moh+2h0HBqn+xrD1uoAYlEMAy8BHC\r\nbFM9oeh5MNnRBY7DNmNh6iGfaT9huO+3VF3XcUbh5OL98eUqTiJ7qFFS6x2E\r\nWGrWJ6I66YWmNDWM1IjEhLPLq7Le+v/W/okP0xkKPsCtQNBEb1JtiF6toLQb\r\nT+pDF/M3+IQRlcyvmobpbqqbPtABO6J7FVTTTLo/2G/dVqh5YYo1UH2JdnB6\r\naG5sucfOIcih+EAOYv2nMfuqpYMA8CoB5SJVi5UQ05Oyd60agQ+tr5KTG1C6\r\nCsJ7vyv2mqXoxozPqrWQborQlTPO35BLcqqUhDmxE9+gDlnlyM5v6wEjvODM\r\nnpU5FHmj7aDki0uST0Z7ztmwuh4PTXtYjOM=\r\n=Tu11\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "0ddb995d7d9bbcefa8e74c5a29c700111427bf18", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.16.8_1671233983970_0.474635740972825", "host": "s3://npm-registry-packages"}}, "0.16.9": {"name": "@esbuild/linux-s390x", "version": "0.16.9", "license": "MIT", "_id": "@esbuild/linux-s390x@0.16.9", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "4398f9d9d64dba4cfa6eed267476eaa9c9b7f214", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.16.9.tgz", "fileCount": 3, "integrity": "sha512-49wQ0aYkvwXonGsxc7LuuLNICMX8XtO92Iqmug5Qau0kpnV6SP34jk+jIeu4suHwAbSbRhVFtDv75yRmyfQcHw==", "signatures": [{"sig": "MEYCIQClKr2CpMpoz/ntLHrdXH9XjM95qxqR3ikXLugKMQAlawIhAJlMtQOii3/KbPHvHGVRRg+gK8Tv5wm4SrEm+3j5UH6M", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8978939, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjnpfHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp7IQ/+N3wwI0Dy5H10rwuq1ygPRHvLkfLzJiBXiwjUssdO7DWYOaYg\r\nGeHttHIcBDNSmhYSWBQg674wUg6qbau+pEJvVbzCnOuQhk6kHJs1JCjLbK3W\r\ntFlduYj78oH7DpaSGulbc6zhjD6FsBVn2wR77Gh8f1QgVR434lqUIkQ5N33r\r\nX1134ZgZMp0NcQFkTOWwMr7d6DpcyuEjtfsFPdNGsdnagOzFluzYpkBdTKKS\r\nglhr+oDE97vKF/erjHmJMkDYx4LhzDdlnMYKkzdp4da4RTNPWA3Q2Nfs4LD8\r\nZyXxchDJ9u6cd2j1n2IviUIE6epIRnBWOA9wA9jH1ettGgMUVTgTgoq/rRQ1\r\n270Oy/PA56rPi7Zp2ZZCkSkFlbJV5S841yISt6IQTeeacB6VQoylMZo2p3YP\r\nS6DTq29unidES2+rO4T45tMtBi5nOL/zmDA5bJA0xJFJLEIlC8da5DS1zl6/\r\nPStb+3xkki/30f8/Adtq3jTGyIRlbi9rIS1UJqfSEnwDDl0fXrwmLPZQc5FN\r\ncH1SYoObAeuVxAGScVju+6js9UEofV/foBGepMY8AxYCAWxN33cKfVtAygtF\r\ngH4xSLe5bIZzrqTj5NepM7rc+DWu/UcXH8aWdu34MIqtgdCJbrkVY4TDHsDi\r\nJfDESJ+/Kp1wDRVD2d/N3YVoGNZv/XJTYko=\r\n=tdte\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "29ae56a2ca081ed980ac9c73fcced1fdbc479f90", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.16.9_1671337927617_0.5711584960404614", "host": "s3://npm-registry-packages"}}, "0.16.10": {"name": "@esbuild/linux-s390x", "version": "0.16.10", "license": "MIT", "_id": "@esbuild/linux-s390x@0.16.10", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "9e3377aaf0191a9d6628e806a279085ec4391f3e", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.16.10.tgz", "fileCount": 3, "integrity": "sha512-fu7XtnoeRNFMx8DjK3gPWpFBDM2u5ba+FYwg27SjMJwKvJr4bDyKz5c+FLXLUSSAkMAt/UL+cUbEbra+rYtUgw==", "signatures": [{"sig": "MEYCIQC/G/C+LmQgEdbGsKtWtXJInbkTE8N6OFfRlwheByqaKgIhAMc22l3yOLSjewqYG6H/fEaVvqFosW//eUVvIWhkjmMD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8978940, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjoPNWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoDjQ//bbfL38sWT+0EnUa3FOFFxQCHXvcQ8e181dwb6cJ4IYtrLljZ\r\nCCONvwgXtEcadoswu0irkbH9fokXxqBzzDArbM0Nh6kkx3kN5fztfNhYyP3k\r\nStLs0YN6y4S5PctfIM0AYW/J+gG5MkWtkEIwXWF2jQ/2GcT06T9NPwwZRSyR\r\nMKSaw9YTeRynEQ4qjLFx2VY2JH0s06HjUU83UUrW150z04ixvDr93gS3A0eK\r\nXPkXQ7x3izawiHi+0HDxZmYiU6YTPnns1TZWP7aNo9MuqSHGUSFhxy9zr4B3\r\n4Njst3gLLnZfQlAcbRR4MLkTa2YdIKJxdDO50U4hjjcp0eFwaAvfr+8Vmqw2\r\nCi8idTh0f1yziLqsNq+XVcWSw4apdhqEHit4GXWmFqcW+WV/y6yVxmIKz8Dp\r\nXPr0rMzB9ruYN+7zcyVKNhclOoA/ITUrauPmqznYlGnONx/FVA+oIyvWQiZY\r\nqOC3bqx+jxKwMGiF3buHEWABhJK8tL8U+yYZk2Z2Kw5CAtzrut8GK/UKn9OM\r\nthAEMmQNSfDHV0KaFipiwDnInL/X+m02d5ZnZhbdV+vzDlmrAaTFAbDnCu1T\r\niM+yd5G8FkwK9kX/jt7qpN0U+ESHqS5MIafudFR/xkV1ZaAGkwY0ncWGjD6q\r\nr0Eb1xECqfNKKMVpMU20rCxiroVfKUEtdgQ=\r\n=dBy9\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "0fea6aea59845d1c0bef9dc16dfff636c3f721d4", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.16.10_1671492437759_0.0952419185484299", "host": "s3://npm-registry-packages"}}, "0.16.11": {"name": "@esbuild/linux-s390x", "version": "0.16.11", "license": "MIT", "_id": "@esbuild/linux-s390x@0.16.11", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "379f1f25e3fb4fc9595c03dc8d1de3427b4097fe", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.16.11.tgz", "fileCount": 3, "integrity": "sha512-Xs2tRB0fgly4XfC4FMv1Fd699AMEH8BClp36mzqRuVzm/285XIJaK5cPEZ9cLLn9ukNHdvvSX/83u5uS1BCd8g==", "signatures": [{"sig": "MEUCIQCXCj0SuZmsOXRKTY3oZi/Wuk2ouNHeKkWGQt2Z5PmPAQIgXn+/M6jyx8BQh35g3ZFe7VtZRlFS9TtjdLNkO+FcRTs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8978940, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjqkzcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoFqA//X5+xwheBVpd0B8BtT3/+3JS3UyL3V30/tSaK1HrdUN3593RC\r\ncOvd6vdrfkf6/3+Ft1egEgY7lUfips7OkCcC/fwtJ8C3S+fu8XpIPt7VWi0z\r\n0C8Qy5ccty5sSwt4nuqkHOQ13AHZ/OhX4dRNcoKdDSqjgvP9f1jW+oGZbtZC\r\nsNYoMpVj/payUZa8T6rVR7lsOeW+Ag6MdqeoeO5NwQwmFx+0mnQ2H8IuqRWH\r\nYoB3OX0laSIvDK7ed6v3/V/lKU8CXMgUOCHedd7yCTPX5AqBePH6PSB3eC6g\r\n5Tyfr93/hAPX/Hnwd8BHrTzl+UhXiHK7hYkyPsoxxvbdia5j8e4cNDOsZaGz\r\nsj+fKvwDcu8NJytvehetG1q1oqEfNSsfnEEzADmAFcNP4Bx374jFiNR+VDxf\r\ngHaAUC46SoShTA+m3iWrZKNxdwy+XGqY5+UmvgUfrICiaTIgJcC4/p//LslP\r\nTgaqF9T5moyjUncVgEefCobQgyzXa4ZVl+btxbVF+4Ul1dF9rbGMajM1vMlT\r\neLGWmJZZLOBgFrhqbu6rajvEeizeygl8q1bzuhwEJoH6Mu9hm3wCCiLvXaFa\r\n1v7FnDg3rornCx8ACVCHw2DvVLgxuge79q6JOPsrrtCt3ZAg+c5A2qkxBQ5E\r\nCDSa7JXbI//GzTmPFIJzwjCAcY88nrJ75V8=\r\n=HYoK\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "17555921cbe672f6327f49a2436df1a69124623b", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.16.11_1672105180493_0.3793494172316545", "host": "s3://npm-registry-packages"}}, "0.16.12": {"name": "@esbuild/linux-s390x", "version": "0.16.12", "license": "MIT", "_id": "@esbuild/linux-s390x@0.16.12", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "981a639f8c2a2e0646f47eba0fae7c2c270b208b", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.16.12.tgz", "fileCount": 3, "integrity": "sha512-s9AyI/5vz1U4NNqnacEGFElqwnHusWa81pskAf8JNDM2eb6b2E6PpBmT8RzeZv6/TxE6/TADn2g9bb0jOUmXwQ==", "signatures": [{"sig": "MEUCIQDWSd/DQhFUhSnwIsIvRMpknb+5ZuqhUFpygyhysTI8pAIgT7PGnPJIGvavCR2y9ONtLz2x+ptnoLmFF2ee6XG/d+k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8978940, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjq6VJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmok2Q//X2VMluK1qz613ElQ0WLcEizoZgSLDgdlxcpRaAyBz5Essgn2\r\na7c5n9DvUMWzsmMUKsGI0EItJjkuqOhTmGI3/PZgO1NsVaPHrXPRTNDgAQ0w\r\nJ9rWTPNcDTXIilLNiJfqSdCtDepk91iY/KdFerVNKIJLwyqrL76lmbvYELIh\r\npTQuyJgy/iYb/HGvhiWsrM6F5VMmbV6Ed7ica61kJvOqaGuSaji5lKsElI1T\r\nUgraYd5as7OguGsBCiR2yo5g87tPsWHnf6JlDikAA+Vln5tGlWhbNJdyzz1X\r\niLFHvg7kcba8Lqo9IpVLhWZ3T31hFoCpU3c13SR6MjTJVVsfubJ6cPdRHY8k\r\ncpaplnptomvGOn6U4ANsMUgDGK5bBG5nf9hlt+RVALPro36tlb+EkAgqUJ+U\r\n5XqgX+wgUZqVXdx/5ZThyyMZenoMOCiQF4cRBHs2m2WjFrPjrNk20e02K4e0\r\n5hUaAE+1ts4Qica50GDcJyQu5T4MaYmk1FVE5++yCVSXsVZiHxOa+zJnAzdU\r\njJ380IwfFZ1L9Zww7KpfXLowHvcAC+7hxz4TVUpu7/iafYKCPWSG2ZRxrjT9\r\nFdMIl7hyLI5nmPOIQEnLEDNIm0A6xzOGhvXlYUu1Oy98AM2X/NxsJLSUl39r\r\nmbivPbfJ+hLBC0UBunjt93279o/8HdMRmgs=\r\n=JP65\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "ddda86edaae10abdc759601da6198b33e61c1220", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.16.12_1672193353160_0.7657766938424269", "host": "s3://npm-registry-packages"}}, "0.16.13": {"name": "@esbuild/linux-s390x", "version": "0.16.13", "license": "MIT", "_id": "@esbuild/linux-s390x@0.16.13", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "df3550a51e4155cde31486e01d8121f078e959ae", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.16.13.tgz", "fileCount": 3, "integrity": "sha512-AqRBIrc/+kl08ahliNG+EyU+j41wIzQfwBTKpi80cCDiYvYFPuXjvzZsD9muiu58Isj0RVni9VgC4xK/AnSW4g==", "signatures": [{"sig": "MEQCIDxpd14K5g3uB/L5Vn9s8/QVsDKizD/NM/ZsPlqflfmeAiB7sXlOwfLbAWk+yHJlapyt/R4K7v1YUOjJWLFp0Juo4g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8978940, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjs2F5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrXVQ//eC9ufuXo9C3cIupP5rWH9HYyGMioTscVghW57234XsBy//Jn\r\nJVRhgIT30gZexUofSLpzbpEx1qAHCVHyQye9wh3bUbpsJDZM2gNb+Ve7wZZM\r\nbqa20Du/x7Pa4dFqPtal9F3MrDdwsggFJoVvXVapjEcHnznOVnrRPjYmP/Vd\r\n0cBIEfqRA08CMYMQhsKXcDkdeBv+wVbq+xc2if/AtwvMu84Y40PSWsa2ap3H\r\nULefupd8s3pnjrQ81YeRAd0hi3lxkEj2Rzk47oVBBn31FiOpXD3bCWfA7ioe\r\nf2sgpjLOVwaioRlTlHoXw1bEWgnBxoJpWpYEdibfR/3z9XkK7i04mGAKKHoS\r\nLokkG647nSPIEvb52497PI0xu84E22VTVXH0T9jGJR8sBNM9aYEbRA56fz34\r\nBcgapCxLbFMLT33alWHBgl2FOfuYgnI0dGQogVA3XSxt1QieqopfdoprrrRF\r\nHuaJH9rnARGM9GcZvS5P/dvZUZ/gZp5Qa9VXBakbpeO/t5mwms7vf4Ht7mR8\r\ngeyaw1FFtNVufVmoAZhc9es2fjBOboqtFj6czz6G+yetPYg2B8uUDyBQlIeG\r\nBnOpG5ww8lLg0/X8e9/EaTbo3LtMqouctpLNYv1ebDwcRN1v9Yy6twFfk+M0\r\nI6q5pgQGkzUrc95cFPp9zVQ8pTxnFPJ7xgs=\r\n=rAC9\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "0db0b46399de81fb29f6fcb65dfb5fad3638b6d8", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.16.13_1672700280881_0.45110743992347", "host": "s3://npm-registry-packages"}}, "0.16.14": {"name": "@esbuild/linux-s390x", "version": "0.16.14", "license": "MIT", "_id": "@esbuild/linux-s390x@0.16.14", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "c87440b6522b9a36a9cafd05b0f1ca3c5bad4cca", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.16.14.tgz", "fileCount": 3, "integrity": "sha512-++fw3P4fQk9nqvdzbANRqimKspL8pDCnSpXomyhV7V/ISha/BZIYvZwLBWVKp9CVWKwWPJ4ktsezuLIvlJRHqA==", "signatures": [{"sig": "MEUCIEVzrj22Zu78LT/DO3nI6MJQEF4hD+krM2khHir+BLFPAiEA1T4YJYISJZjee2+SPK4m8MG21ZACo+Dc7Tub8U9u0K4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8978940, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjtd3/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrTyA/+KA/7b/YYSKFI/T+vn0nlNmtFtI2PX+fYm5OWdHmDkzC5Q87A\r\nfjwwzJ0mAr9/l4tWQXYx5oJAySTeu+iJvI6Rds7bg0exAmglRvYaEhCWj8+B\r\n/c+EcUk1OEuZ8hXQeWeQRDPxZ6XD+5LrFGvPIViBeT1AERzqeSv3MQuQ1BlK\r\nmHbJaLlfVwTB+Lds2mmqxHWEq16viUBUSfWpf6PWt/F6v8z7wJRnUMBhco2g\r\nbyCmg+/T8D+Qb2HzTNu/XGRIQFnW/8LZBxfmXTFziaItP6GSBzbRsCqUL9nS\r\nqUipNRng3IvFAUvi9CBK3U8WohwjdMQU1sEiRxyBrl57UmAYcuGQXKsIxxou\r\nY3GnJZwsGRGXPKeCd3Qh/VLiDRxQU/q4/kf0UKnyIkua78+KhKemEHdwKvF1\r\nDKj6HVT43bVW19/YzJJkDP0DlQg6tWbG4eBLZzW0gmUmccQO4IrDupK7U0ok\r\nO0E49RBhMYHPLSjh9cwFgvh+b3MUxx9LizXP1vcDNuVbWnWIn3k0VIE8tKjV\r\nBvebu69fAJ1crn1sEXX47OF+Y9cA/ctALW/37qJaq8H/ZaYWKJ4dzJp/NeuT\r\n3rC1eQ+PkW3vSVvE73s50GK03wB6qU9y2p6+2PJC2IAhf5ZrZ98jRNIlXe3O\r\n8EkMr1U2hHBtQcbFwkfd/gFCml96qSQC6Ek=\r\n=0vV8\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "93328af7dcf842f750c3e782bd83997e4f817e8d", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.16.14_1672863231094_0.41637564683563544", "host": "s3://npm-registry-packages"}}, "0.16.15": {"name": "@esbuild/linux-s390x", "version": "0.16.15", "license": "MIT", "_id": "@esbuild/linux-s390x@0.16.15", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "69572a26c2120ddd446d8207d30ae8f94a801a72", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.16.15.tgz", "fileCount": 3, "integrity": "sha512-M0nKLFMdyFGBoitxG42kq6Xap0CPeDC6gfF9lg7ZejzGF6kqYUGT+pQGl2QCQoxJBeat/LzTma1hG8C3dq2ocg==", "signatures": [{"sig": "MEUCIHpZKZM60P6ntE2wUXJnEftRx/eZKPOtO9paIAdl8LfLAiEA+jioMHAvUEX1x675cI+W+96t/nWN+k1cLEIYCVduksI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8978940, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjuPLoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpCBA/8DQhyzCaN33sq3HTtntc60x61L+RbvHC8ttiiGLlnqHzxlEk3\r\n5HqNVy0qplh/wI3i+k3HXOgXxGaulSmlLJcUen3RlXspyLZjKU7a2C0Q/iry\r\nqmwN5d3+ToemVZDaoR8pcuQroWjbkLboDSaEwMy7xliN9lQxUILyKLlBvK4B\r\nMA/J6HekixKlss8kMw9k8VNarOeWdtXYgrFq2Ay3vnD7UFzGCW0ZMFLza4Qj\r\nnjzk8LHYhRLa++m6/rqvX6z48OhX97FU8KnNGuHymWG/FpxD36DisJq3m8Re\r\nmpybzTOXPUuNxagcmH5dBD8Q8M/ugYent0ftjb6ZGi1SCHFK3EN3J13OxU31\r\nMjIkzdJ4EChcE8cW5ayPDcf7y8IG1yqYI6YQy9dnipCJhfvaoXL/mZ8bW+U+\r\nZ4emF/1ivJhMzEvKtlKmwoq8gHJkFXbvJ2D4jX+l6DViOgLTDzQv224Ts7yz\r\nYDKUc2hgGsnP/p2hiVS/+knTX6LxGHnC+QyDJQIXYHllfQ1WW4uppc5yVv9w\r\n1M4AF1rv+SkvR9G4n8gTCB7S75DZu4ofpGIZdzcN2bWnOpfKBTknM5woM/Vd\r\ndcLXPKa2+pLuASfautyqTypLOJCcYuKpoEVNtAzo85zLmPiGsgTYwfvBBS3O\r\nhTKnGC1VSSbVxOcOKVyEcdqyJAXfEcQDCeE=\r\n=YqBF\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "33a515951c626e56addc1dd4c6561a1514559fd0", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.16.15_1673065191817_0.6328008234249178", "host": "s3://npm-registry-packages"}}, "0.16.16": {"name": "@esbuild/linux-s390x", "version": "0.16.16", "license": "MIT", "_id": "@esbuild/linux-s390x@0.16.16", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "5042de05b9b653dfd134f05e1b37b61704c14c42", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.16.16.tgz", "fileCount": 3, "integrity": "sha512-jRqBCre9gZGoCdCN/UWCCMwCMsOg65IpY9Pyj56mKCF5zXy9d60kkNRdDN6YXGjr3rzcC4DXnS/kQVCGcC4yPQ==", "signatures": [{"sig": "MEUCIC8Yqz/8Zmg4U55rm4DgJtdcKsoHc6i+DugmJnlsieWDAiEA0PF4svpicxOoZrCv3tTnXhw0vty2vMiO07dRLXnRzcc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8978940, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJju0dMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmojvg//btIBLIkxmQT8A8AE6lefw+fMG3OM9Z+cSzC0SH85h7Ss4enb\r\nUmEYgMmzjv1/rEa30hUyHFxqo4G5/Yagu5VuBgL6oWHfErwmVsgqNm2BiSVk\r\nnerHEx0K2ugAhzsmTg3hfDeOUAADCxZR3jGF0+IyYWW24XdJkqGtwUYgwp+g\r\ngMayUGdmPHspNZFxouh9x9IwP5gWjvlT1YOAr8CnvcCCppIvUD74JsHEaWZX\r\n4uI4G7t3F1qJey557lVryR6iy+HE0cdie2DSC1iCDW8v5sa+fB4aHEOUEyYr\r\nbzXT9jW6HNngo/2/E9OmvTUFDaE8AtWS3y39CJK7RM9SYrP9SobWpE6HvNBn\r\nSQTCmiNsuHhcn15sm3n4SLZTC8Ng0+J1OERwMzTvJkVPxCGje9MultLwKff0\r\nHeXAklwHqyVvcXLziOG7iMJ2YrlaYVjrzMs/GpgMLLWSAfdk42QWU3mrU1yM\r\nhZxg5eHUXkim8NNIRLkpPKeaPlgvN5Mqvcqx40rBUgusu3xCp47z2h77ZKzU\r\nppUXsWrcnX4RdhJaUnb9qi3NeYbNo88QZz3sC3rQK9SdHmgpKfqguVoCpt4H\r\nS9pnrJr2NXkLfJZyxvxvlCS3puJq5QewkXNj2k4HSb9hPR27P+Yq3vUloq7Q\r\niw4akvuVwWQFz1qmB7bSEkhrrABujWx1amE=\r\n=XhoR\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "edede3c49ad6adddc6ea5b3c78c6ea7507e03020", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.16.16_1673217867986_0.2129771296809344", "host": "s3://npm-registry-packages"}}, "0.16.17": {"name": "@esbuild/linux-s390x", "version": "0.16.17", "license": "MIT", "_id": "@esbuild/linux-s390x@0.16.17", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "661f271e5d59615b84b6801d1c2123ad13d9bd87", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.16.17.tgz", "fileCount": 3, "integrity": "sha512-gzy7nUTO4UA4oZ2wAMXPNBGTzZFP7mss3aKR2hH+/4UUkCOyqmjXiKpzGrY2TlEUhbbejzXVKKGazYcQTZWA/w==", "signatures": [{"sig": "MEYCIQCMBnE0upXDw2CE7Pk8s87BHNNxLebGCza+4gncSRts/wIhANFXclROHq//J0Fd3UiqFq2/Doz0uZC9aNd7I1G88LT/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9044476, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjvzEQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqQIxAAlVNqwBu5eaZmq2DYnuO3qD/K515QRbUp455d4DRDnkKshrmL\r\nPqEV7CiGOZ5cd9amG2ybc1XlDusc3L5EXIp/vOnxItZoWCtEN0ydkM8IKBUl\r\nf4pnzEDj6lS5G/qeMaJ0/PoUj81tkvPkyP26jBPj1a7t7+ytoyLEQzhrXDXz\r\nSBUDUbCC3PhPnGsAd82mIBHx6dPiXf686DFup6Lq7a9s+FovalWUV2p0HtvF\r\n24oA1MAO0pItrhlm1e7tMJ3HmGYzZP2coJFWfDATzR77SbIMdtcWi1K+5viS\r\nO5bkavgcLJiDKPcb6tQFcvvF3SeDizZaKggyaLnaAKY3OvqJH5FuqbYAmWtO\r\nDQ9jEc7JxS61hRfc++OsQRDVClAs7pFHzAZv1/spXTIRcc541bfk9bgtWqqb\r\nB8swx4xHYp1C6hUU6t+uJ16HfV9WrPMKcxycJtsRZk6FO21HkEg6dt1D1qvt\r\ngucCvjViQDs04U6II6oP+VbQHZSfqv2JbZfddiLTe58hgvMMYM6crGuo/HLC\r\nHbxZwH+bo0Ld6tj4JSnvOX9AsR5ZFi2vV7WhD7br7k0CjIiwUOnh4Yk7A4dp\r\nvYZ1Uh5/yN4yc96mXDrclUsDjHVAkeR9zcZKXAG4K365ebA9h8L+XilbAiZS\r\n+q7LFEIGCkYXJr6flaMg00kdQ3FGyxPbqmg=\r\n=cXh0\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "d751dfb82002d332aa4dbfa89c74d25203d28123", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.16.17_1673474319821_0.0633223754527168", "host": "s3://npm-registry-packages"}}, "0.17.0": {"name": "@esbuild/linux-s390x", "version": "0.17.0", "license": "MIT", "_id": "@esbuild/linux-s390x@0.17.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "ad6569476d6751cc9255fe059a5e074a08dd3e27", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.17.0.tgz", "fileCount": 3, "integrity": "sha512-7xq9/kY0vunCL2vjHKdHGI+660pCdeEC6K6TWBVvbTGXvT8s/qacfxMgr8PCeQRbNUZLOA13G6/G1+c0lYXO1A==", "signatures": [{"sig": "MEUCIQCExwg49v8VsLS84eJPUjz6+CitTtWfgWjjUKExN98XLwIgBOq7t5feELntPGMnRDsf5O344fcLkCzMk4of7lv0SVo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9306619, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjwjGXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr5CA/5ALWBZLF4zoel5AOQI9WFcnZGi1hgx6NrJRqqccXpVxFvHa3W\r\nVlvWWZR5rbgSoS/onATYFO3au2BNTIXL38dvtfqRlQ08dEm+dQvqwRrZB9sG\r\n9iT0ZWuL7ieqqhKKgY8K61neLkNELX8sDjPT1tEESXJ81ZRcseCI0mevnvOf\r\nqdfXnG5Wo7X1o0hiTsHo+5m23Ol03XriB6r50XalBshH2wh8IdSULqtbfORG\r\nAL7GRl+nvmeDaYHZoXf3d/TKd2rWanm78+jUpnuDzI1VALQaFhyN5mboibin\r\nBmq2IDbq+OYI4e7e3mMLj91YubbzyFtkNjcD8GyuhV4oG39Nhv6+DFv1eUB6\r\nPJ+/9UwcQd/Cm+S25cju2NucSZbTD9bjkOCLQxyYDKwpiDsWA5IXO/yu9bMT\r\nxa3TXB4qXqyzlL0YQDOu5cbvn7pGH5ETUFfeJUeQquRlK/OmeuQoHiRHse43\r\nuCBXT3pz6HLmMyWe+xGNEMZecrd3Ud9FGYxqBxGVaqgTkSG7GY6Z0DEsEcda\r\n9t6+OenF5EAdKJHJi4e/yCIzD4XdU2xDXMSYYsw+mixjWWtOMDvG4bKAumZ6\r\n+OBaru+O3MpZ6banwUhG1EPej9CMHEpNcOrsnlhV6wmrXGERtwWRia1jf+c3\r\nkc53dTkvMrIi7umngf+hL7aZau5DSx7KUi0=\r\n=Xktn\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "2a18b3d6ab3d1003afbcfcb4a6ffca61e04fcb57", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.17.0_1673671063015_0.15369717954339435", "host": "s3://npm-registry-packages"}}, "0.17.1": {"name": "@esbuild/linux-s390x", "version": "0.17.1", "license": "MIT", "_id": "@esbuild/linux-s390x@0.17.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "2afd08351e59835fa92f7c58f588060ccdd3d260", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.17.1.tgz", "fileCount": 3, "integrity": "sha512-oWqggXTbGp5PaBFIkEzA371aoTWHA7rjzWIysiRubYzjXxHy60qzkdZD1OaS44EMRiMItxUj8UeC3fpU+Z7qgg==", "signatures": [{"sig": "MEUCIQC98WEVlo0lWzRDchpQjSFLlnI51ktj/6XrUtAideWCAgIgdHGHPaxLKbMiTNYrsbKF9Bsw+cuNPsUcT2XofV9FkDg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9306619, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxZIpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmokzxAAkrBRGwJTg1LYUS56Q/x1QPMLhbzc1vrkfGWl7nbX6Dn5MAeU\r\nk+fhn5OMakWeSWksd0zTP95uv1ERP2G8iIzKog0q3KvZwUO/JR8geD1gONvq\r\nfWn8nMYd6nNKJMO0A76mYKcjt1Q58t28mb9Og7wIciDb3fH50HULgb+T+YUt\r\nxV1+WzWM7vrdnbKO8SP8+4KbcsbmXJDhjCIBDgb87H/jZpc7DOYQSSBMwvuE\r\nTrvnYlq6e0RP2riq3qwHxdnHv/+P/eJxcTeO1qV4QRRQIKGW6V5xD7pHk5It\r\nLF4YiHb1iW6IYOhRlPlPsYm4j+LeNf/1q6Ydp2+XS/MsWif9fJ8ynCtYYrrZ\r\ntk4kkSw4KejUEn06wm5mP1dQBoAQqTJMhnTDjxVfkkXBLCvzz5QAkElaNzYq\r\niz0Ulf4pxER5CueSaZWUhW90gcHAmSIT9Df/7QjFkUpPoAVIQKP6Px0n2w1/\r\nrua1vDQSgVNmMCG5O7A9RSpanxYmh2O3l9YE+pMZZbbZtTb+NMpGPCqgTF/P\r\nCv2X19+04Hzfk5S47b1ggBv1evLanr7i/eDFZ+zs9m5vZ7J2xeBBhgfcNW9G\r\nIu8hspRaJwfLISO0PzxHkCKrmbeNJrT8XBbBjlYoJh23gayro0If2m4xdOcr\r\n1hSnL4v6CTNTbKdSEEgVFEqr30h8UkYV4vo=\r\n=Ei1u\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "002ef9522a4103132cefb075aee3e09b0e4fa3f1", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.17.1_1673892392760_0.07597764624113768", "host": "s3://npm-registry-packages"}}, "0.17.2": {"name": "@esbuild/linux-s390x", "version": "0.17.2", "license": "MIT", "_id": "@esbuild/linux-s390x@0.17.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "5c03feb73b0c3fa80834eb150cd9c14206681b4e", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.17.2.tgz", "fileCount": 3, "integrity": "sha512-jUFCO+/VA1Y/oeauSNBubp2UtGu4xjBUEFVgMPm0qLuw6xw18yOagKwBOPVmyE3ZSFqGd9BAPZM/JrtadgBryA==", "signatures": [{"sig": "MEUCIQDvpCHAMWR23AyO3U5UJRn63LvvBj4H8eGqjIpzelgQIwIgSUF7+KB7wrWTvLhoaBcHp8jLlIeFbT1EFro6E9tWP0M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9306619, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxkLZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqp4g/8DqNT5UBnNEO1y3T5wJ6jz1Cs6ydQJeeYqZEviM1/c124NAtM\r\n0ZmJK9tDd4xzvyOxPdxxetmBdKDQ56b7ynpxiVMdP+EPDQxo8oiBfAyA0N9q\r\n5F18v8h8AWDifrRxGtg74akBGvwARlD2UPEy2HP4kOi4WhVFJ4IcK8kQcH08\r\nOgyJchDQQze5d99MrX/DRvi/TQ0j95GHd2ykd2Qq0GnOipiR1Nu1XQGXTckR\r\nLkxrHPRKerUSTZ+1QGuedbhFWukzBLmBjrdORx1f5tXXmwjGuD3cTs29UzQu\r\nqOu5h8U7yPcruCxiZJqJzWfjkFISub2v1S/CPwyELZCt5OZn5jXDTn6C7Hmc\r\ndrl+KrEl1PhfiqayPF9ZJ8e/4Qmi5pWkpOhf/ybuZBmIbs4TiEkkrPCvnZM5\r\nGyaxjXhGSzGcTcA09nKaYAske+b7uEacfzs8cyDv7kiM7NU+9l/mjYp7ny3a\r\nL/7NhwGr7y6zrauqbpVhtRO9PMCxqDycfeQC+d8u7wesa1IsN6xJ2LLOTqhF\r\nE1kAw1eRIOEgfSxMbveist38pxfgrou4S6kOavhBhAX9W7i3oUG5/a038B0S\r\ncweLgDiPYzsse5eaPZfHr8QwymMLF4NvkPeN1vz6gmu7rtsD63huJ4/7+MC9\r\nnGi2fiVNtX4/IRqJroC3AidEH4gt446x5D8=\r\n=r1DU\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "a98870a2fa9f7af7024be24cb6833e638aa71da3", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.17.2_1673937625723_0.037896961738512314", "host": "s3://npm-registry-packages"}}, "0.17.3": {"name": "@esbuild/linux-s390x", "version": "0.17.3", "license": "MIT", "_id": "@esbuild/linux-s390x@0.17.3", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "6bb50c5a2613d31ce1137fe5c249ecadbecccdea", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.17.3.tgz", "fileCount": 3, "integrity": "sha512-Iw8lkNHUC4oGP1O/KhumcVy77u2s6+KUjieUqzEU3XuWJqZ+AY7uVMrrCbAiwWTkpQHkr00BuXH5RpC6Sb/7Ug==", "signatures": [{"sig": "MEYCIQDp/fo4uXfpctQshecHKbRuaGXWgM4oG4wugwegEhJRYwIhAJnQ9/jxoJeXUkvUgzvYVfeXLVmivMkw2TzuNUoRX0Os", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9306619, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjyEVBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqZtg//f8ZDMjgv8ibyb9UQayTaNypq0UzZwaReVR0FqkzBK2lk2skj\r\nXxtcDDUJMUAWTdmYJKz8QpoFpYbm7wKxj1c1mQKtgmkqOVCRB65YvVI5dUYL\r\n1fHnVzH/Bju1B613EbumXfvKES3aKv5mjnsthetxCJC7rPEVHNJXq/3AI38w\r\n+KTYm9cZ3Qpg86NK0vESOxVxJLWqLQM1ITI8H7qmCLqevMG8fkYOqsOYFEl6\r\nRezCMRnKuFzxTwdmo7wSSLYhFr3DbKOSJ4V5ui7oSigUP3RP7Q+tlSuNCrOa\r\n/z2DadAnzrZpyu7dy8uhsHvrILobSkC76qu+PmXA9Z6D3LxgO1xuTPTiB4Sc\r\nLzzYVN6KPYqsejjvIO3e1v4TJlBpqYsxbo9LN+rub+TWGBuaQMkEyN/Ft3mm\r\nA988Fr/hDJyH4FrdafO90hYaT/osS8PTy8DVrYYpdRlPaqMZhdlgx8JQsEzy\r\nqWys4a3EqjzCUpUwOjhKczT6Ot+gUpY5MguH4HYFjgL5NiNcMIa8MMzRaGNe\r\ntMtgh50wUTCX1FN/P0Lf18PRRQEhIqoSlEuYOaBUqfDEzL9LzuGNSTZ9sUKH\r\n9mng30lu0PL5NllH6rhKUMi2gw2FC9I9ygNwSrrAGjwz1ngLnhoBcT0K95hp\r\nvcNKRTw21K6ptPAqOxaqfIclVuAFv97sRjM=\r\n=deJH\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "acc5becf4d4e81473761091fc340efe16325da4a", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.17.3_1674069313077_0.04691858855321129", "host": "s3://npm-registry-packages"}}, "0.17.4": {"name": "@esbuild/linux-s390x", "version": "0.17.4", "license": "MIT", "_id": "@esbuild/linux-s390x@0.17.4", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "197695bece68f514dcdcc286562b5d48c5dad5f9", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.17.4.tgz", "fileCount": 3, "integrity": "sha512-AQYuUGp50XM29/N/dehADxvc2bUqDcoqrVuijop1Wv72SyxT6dDB9wjUxuPZm2HwIM876UoNNBMVd+iX/UTKVQ==", "signatures": [{"sig": "MEYCIQDZMDBpygc7z3xrWgCRPM3EwGU3drvyQyIgBCw02lNvVQIhAJ2Wui+cA8H4WAEp81ohpn2Kl4BJ52enWyVH3whcKc1t", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9306619, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjzNQxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpY5Q//VtNRg+Jn/cE8qW/GM2zvbnD5QFTgsnfYDx+Z9lXH6+Id6TQJ\r\nA6KjMOLFdEi66aDJ5ZvOOTe03GUntq9Vu6iRwIDNWXxjNoielER5KUcsJS5A\r\nkHAbPfBynNHfUG0fT3aU+Td8ok+HfPgGdfwA3eh/g/Y0NOUtJApLww4jfa+f\r\nxjAgUfFsw3DapQw14ULy3iMZQguFs5G2p1xJnPUdhoN4CyWKRYv8ODd+TFnj\r\na3jZ2yQOUtfzZahn+dPYcM17t1y6a3+WhKTxhih8lz4xADwIJrj6oTwJPmTC\r\njk9nUmCdWhGuEJhvXjBy3tpQ7rwKwRubk/zAj+z9NJzYwrEPOOxHlbHpbYMZ\r\nXY1Ba2/AgoYD0pUlnnWd2ZLQ2bBsMBDInVPtf1ODt+So0LsNwYOoaQhKPiII\r\n6XnWKJXK1R6hkU6G1M7s2zuEcD7DSbD4nIj0Lb8mNIZ1ZKaQIfRb0fL6I1eR\r\njjG3ocWyY8P0DOpfGnDF6PMg0KJK+wiGpzCdUtB7gM5EJ//dviLDLAfpGoH4\r\nC/yfTOXTOPMzHKauqveXvvrVQbZ/EgI4BWQJi36G/9Kpzk0kycieEUItJ0dx\r\nLfjXjVyO7fpqzjeQgdnS1EbcRzW3yi/DFf2kXGJS6mjj2JqqMo2w5f5bbY0/\r\nJA/qec6AhM01X3IYU1/UMBSu9h+J2C6/Xwc=\r\n=VMSA\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "3c83a84d01e22664923b543998b5c03c0c5d8654", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.17.4_1674368049438_0.3203006302993774", "host": "s3://npm-registry-packages"}}, "0.17.5": {"name": "@esbuild/linux-s390x", "version": "0.17.5", "license": "MIT", "_id": "@esbuild/linux-s390x@0.17.5", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "962fa540d7498967270eb1d4b9ac6c4a4f339735", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.17.5.tgz", "fileCount": 3, "integrity": "sha512-kRV3yw19YDqHTp8SfHXfObUFXlaiiw4o2lvT1XjsPZ++22GqZwSsYWJLjMi1Sl7j9qDlDUduWDze/nQx0d6Lzw==", "signatures": [{"sig": "MEYCIQD3SkD3lh2R6rCs7jddXmS5t5Dz3zrM+E5t8Ae9kk3EUgIhAOFS5ocT4lBKql2ObWZ6D05ifYvtOmQHXaMcrN3YcXCt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9306619, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj0/4HACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqttQ/+JJkBCp8VtYMHuhKXm75y/eZFM3S7atfkJRlsUb2Q7HALQnH4\r\n6C7GSNKWnS1to8tnOq8oUbHDxQUSdY3iq4B7QDV0qSqpBDDvQAl+kK/0DxJD\r\nguU1JHwH4sXReNsZ1nCnGjcQe8MP+yYcEELAweH7zq71KvSUzeNMtjD4Avjv\r\nghFyBUElkPpKaDwAezDvPkm8OX0SjvV0Ah5Azauusq5p2ldtgZb1AtpC5DAK\r\nFHDSBCti0lz0/DnvfzyqW1UgdUBnpf5Y0nmKDYwr1303y+UurIDxzVIhIkYw\r\n4buTmx3CRpsf7JwEtF18X/A/EUkcNpy73lpoTZYstwAo8slAy7YajpOiHbPj\r\n7K2V/0V5y9w6xIr65bqWUWBXLUDu3FVxJf9BjRQU6Ne53RZfbifPqshSi5X7\r\nvtCp2qkE6nx0FeyGrFFX3Ji/YiioYg85s+AwbKAmNlBwCrqV/ki7VbSQ36s9\r\nik/Q+kpQ9Y7/EpkXUFVsHY1f9KmB8q+6tuubIBWN9xmu2SAoSIQApN/QPKA5\r\nb4l17AyDx4O5gZgLvUBkIZggpTZtZCDhUCcVjVxL9U2sPhztLD9S2+kLwN3j\r\nQsH4wnerXqNSE+fwdx0fscFjdJbCM+YvcaTyTK7oB3/MWhETo5z/gbRlNv2C\r\nPJu2hcyT99FZH5RcQ6p0LJsHYK2frw4AYhI=\r\n=Ukt4\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "a8b660d85a0a57087a0f188857519f194f52b84c", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.17.5_1674837511080_0.31785888766102244", "host": "s3://npm-registry-packages"}}, "0.17.6": {"name": "@esbuild/linux-s390x", "version": "0.17.6", "license": "MIT", "_id": "@esbuild/linux-s390x@0.17.6", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "d7ba7af59285f63cfce6e5b7f82a946f3e6d67fc", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.17.6.tgz", "fileCount": 3, "integrity": "sha512-SPUiz4fDbnNEm3JSdUW8pBJ/vkop3M1YwZAVwvdwlFLoJwKEZ9L98l3tzeyMzq27CyepDQ3Qgoba44StgbiN5Q==", "signatures": [{"sig": "MEUCIQC59vIVBJH+Gfn49mf8O4HN+CmaYecwwnnlTmD//v1C2gIgIFcZp0ecs+NNVVkqlKn0HfLIeJ1FDIdgGRkXhwsBerg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9437691, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4TJfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoUog/9Fux7MDIBRbEQWtJ77UXEe6omPU0Bt5FjTtkOpRwxc5rjM4y3\r\nI24Id5krKGabWri5h9YPrNf5lpuW2hGC2mwa7NkqoL4dFobx8o/mZYH0Mn/E\r\nYHXE3efg8MgvDYFFVkCqvn0ruUh7IZER3YZkhC3gNSshZKnLmYHbnMkQusON\r\n/m/laxtfB/3VgkTzMmvGI9OC6wWBynLCYITkaR/5uoFkcirntiVnPVwOG7Yw\r\nEmIzCXhM8+49z5NlqySuE9SYDBmLjMrqybcA3G2QDy0QLM6LPD82zq2hZdlJ\r\nkITVGqeAAiKhC3GIiDWCLspF/703WtdjdvZQb8HJbLhLd4mabjvCmbUQB4YJ\r\n+FtMl5ugadH7ltUPNL1Z8wPcn8S0jv9hPk5M+3eb9MWc6zF1T9aeyjWRk7ZL\r\nsoRNq2KSVT7wFqZhMbN8uDNcp5+bKi5lI3uPa3LO/Y9OtvLWBoe1g0nVmc/y\r\nX6cp4SIvArHbFDNEcBi/2i5WX5Pg/JRJmiPkYQGUfd8mM+9iSPYx6r+qeuwe\r\nltt5OK+vT6Z2dPuwqSpkz7gt8obFz4gZwRgSU3eTBYl+7x2CyFdI2ZVBmPRf\r\nArgJUZ4nSKXpHNPo35wrfSQxGqHQy+9yzaD/FLyb5e0W4bx+hRyoYbF1Dviq\r\nKvTwr3Fw7m/aOUoeIGlsYHxJ7WVPevBPTus=\r\n=dFAT\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "e1143a75dd5e7d9fb8591096edfa123f6eedbe44", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.17.6_1675702879574_0.6273280098910314", "host": "s3://npm-registry-packages"}}, "0.17.7": {"name": "@esbuild/linux-s390x", "version": "0.17.7", "license": "MIT", "_id": "@esbuild/linux-s390x@0.17.7", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "0bf23c78c52ea60ae4ea95239b728683a86a7ab8", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.17.7.tgz", "fileCount": 3, "integrity": "sha512-x7cuRSCm998KFZqGEtSo8rI5hXLxWji4znZkBhg2FPF8A8lxLLCsSXe2P5utf0RBQflb3K97dkEH/BJwTqrbDw==", "signatures": [{"sig": "MEYCIQDlqxLoEOnFAXhwNCE14fSoSzXv4hQXcWvwbRXpUr50UAIhAPaz6g6j3GRqo3EvhzA7zCZcHva/0+nNEyc87m5ZlMsC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9437691, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj5XNMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrNhBAAo0QfK6r/kigXpVTU/VfuMPwW2D3XhnMUXt67/JhC/BDb8aQv\r\nC3rFAVCbJEV5ToWp7RGPVBfjIOzP6l7CctcHUgEenCicWy1pFint9zMqsDQy\r\noUc8vhf2NVldCO+WYCYtqz2hqnPuRPMmktEv1rfLSSW/DQJNlbW2AKIO8JSW\r\nfSS3nzotCHIq9IGVlHTEHC7BtawLlXc/fdEamS+iwlZvOHy3GTAtkYLgl+PI\r\nqgB9/E2s7qyK4hsyGoPXUQ2CO+NOJKBK06itgCl8kq59TcG89z9w4NWqGVow\r\nzEXwuKyK/qiehAvh8BLJU7dulX45TGksw1hQrK6J0pzoAU8t2+zuZoGIE+ZR\r\nX2WUaLpnUeGQLn98reiMSts0nVU8qOOYQWqtBFcnBLJtjn9+0ggLMKsuUjz+\r\nALT/XeF1GHMB81SA9xV4JsTA7AXcQeCFxwpTZ13XzeeCNO77lyROfQYiGYDK\r\ndTXT05c77Hy1NO5T2AeK+BUMEYc8sNC/7FMWaJT/auh+2Rfva/MYj3xV4DOj\r\nOM9J3V5H/VnRA6XrNsraO3qDuXtMiEHbsZPQS/iKT58NhHnqLqoWPRlHvmWv\r\nVJ38CNx8FH3YgcIPiJcdXL4lDod2dgBuyubrGUCbJm6qCLxcBAzYQ/7sYF1x\r\nS5nwP2YlqCkHBO9k1TueB0ckfT7y6rvsmVc=\r\n=WoC/\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "e345b13687bc3ac86f18f4a266a162653544ad31", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.17.7_1675981644437_0.49889209699687287", "host": "s3://npm-registry-packages"}}, "0.17.8": {"name": "@esbuild/linux-s390x", "version": "0.17.8", "license": "MIT", "_id": "@esbuild/linux-s390x@0.17.8", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "291c49ae5c3d11d226352755c0835911fe1a9e5c", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.17.8.tgz", "fileCount": 3, "integrity": "sha512-QgbNY/V3IFXvNf11SS6exkpVcX0LJcob+0RWCgV9OiDAmVElnxciHIisoSix9uzYzScPmS6dJFbZULdSAEkQVw==", "signatures": [{"sig": "MEUCIQDuDyxnpL0+HCQDrsRjmR9qgD6g1Jhsq5yGn2m4JtFFlQIgCEYaoufevAMOAoc40LzuA5eZ966NM19+Bd6tJcxpQRk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9437691, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj6dphACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoaTw/+OgXXKOj1fJol7gf+4ni96XwkkD+KSlXDzEuZxpnm3T/o6WQH\r\nhRmQ7Tl/Nnt6qx394moi/yLaFqoHq0mLiRCNWWI4hNvTJaEYJWHP+yHbI/le\r\n+1n0gClyClVLJDbNidG45DF3LVNOa9LeOWsavX3bruc+C0Axbaw7oBP+95BL\r\nJMneyaBM+7fMxaZBqG2DQVDuwr/8f4lkPjs0fXsG6J1MS/S8lZT6K6bxjKbl\r\nFhfNIyLJbqRF83WUdzdGoyvLjUGlm+IzeWTIw70uTQKwrBI9lemQv1QP1ChR\r\nfcDbdeiJNkZAAEkWJOboeOYQCzZ7O+ld7A06erfItIAFK6XJs3uYP+itrNP6\r\nhab3NaO9kP5KHgEj+ZF+D7x7nsnOsydvz8vR4yTPCyykSgjX7MEhk3Hx7csr\r\nf+wOaimFfLNpOFV3fTD/1hu2s22cBQAb1D0JXhCKaUt/o01eiWwGZWBius9e\r\nTBmF/ZyiTgttj4Ksi1l33wEJ2XxQKQdrnaDwZu1ofXMjH8WoPi9x82/UxyJI\r\nXBgROm9ivqkobl8GjavVsW3hiw0ziqasgaL4H5oOKirG1O7XCDMwoj9imUf3\r\naPSJ9IBJiqv3GX7h/sk04t6mTfRj7d6Atq8t54CzHwZhYztGrL8jk8HkJgSU\r\n/0Ep4eKt/+97F7O4SNofpQxrtmqzRcajsEA=\r\n=OjsP\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "5e0b1cd2ce6297cf31b132e413134ffe2576c668", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.17.8_1676270176866_0.24116976251171884", "host": "s3://npm-registry-packages"}}, "0.17.9": {"name": "@esbuild/linux-s390x", "version": "0.17.9", "license": "MIT", "_id": "@esbuild/linux-s390x@0.17.9", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "4b64f84e7fad4b42dd4cb95ec8c8525c7f2c8447", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.17.9.tgz", "fileCount": 3, "integrity": "sha512-zuL5TDhxstsvxYVZ1McsnfNrO6vlpZmxiNShJmYuYPt8COBJ/4iRkwHZ5Rbf1OkEVazB3/WASNtopv1/Gq19IQ==", "signatures": [{"sig": "MEYCIQCjUU9t9X7KxgpnsY3rJAE2xV20aeoKRnPRiaFhqlJxTAIhAJp1C/HEQQGnY5Rq34LVlcK/QyzYXANAi2MUH9WRt+ew", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9437691, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj8mBUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqzxg//ecmjIfklq6ptUneWV6o2sfadWNBTcn5SV+lsBxLTKBn7ci6y\r\nDg49gRZEdL6REU6oYItL2tXRKkUgxOqwZ8JAmcLfwtpfsmetdub9Qh4Iq9bv\r\nfZP4GPFrmBzx0qGZ6DzOChMsR9ZRLLrm287V2tuRImhAyl4odwzFNT+ZsDF1\r\ndjF9GdjB5dgYDN8Ums5To9uQTtUmHIqYvTNxL6zZzl1wStuJ8pseKHuq16HX\r\nNm1cHTQSnOs0cJimtFVwlwS/nC0QNI+ZVEAhyN85tvCmhShJCktoj+1ErrFS\r\npbE0Hbb4AL2OWlvpf0nK0CDyRxurxVE9ngdrB0naqiF6BiEzkWIBEfsi1Jlq\r\nAmlMOkKro+zclVCeSINOHx2pYqIMf3i2a1jf9nz1oya3dU10Y5G7y6VjSOCQ\r\n0JN78zJAXn2XwVX98/x7P9dB8dRAWD4PEWJyJVKFOFPeuPEpSlraJKMUTp4q\r\nIZbT6sWdMid+CheMAZiOs0LVQu29mSvc39eKS+N0zKJYUWTBa/YTSJkN+e7t\r\n6lNAYnPfv+UHU5fJ/LnD1ipsTue86LgIFs3BCpM6+iq50vVYmtHAQVK3hdw5\r\n9deqSichES9yzM5ENnYS6Ezo8o/JMJsfHviNnYEFbAG7Znu2NtoFjIiz89up\r\nKw/K6MmrOVJil+yQeLLD51wk3Ma+PzLhoU0=\r\n=eaWC\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "3765e880ed0a55b8e62bfa17b7e004e656eaf914", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.17.9_1676828756111_0.9365176191924387", "host": "s3://npm-registry-packages"}}, "0.17.10": {"name": "@esbuild/linux-s390x", "version": "0.17.10", "license": "MIT", "_id": "@esbuild/linux-s390x@0.17.10", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "126254d8335bb3586918b1ca60beb4abb46e6d54", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.17.10.tgz", "fileCount": 3, "integrity": "sha512-LsY7QvOLPw9WRJ+fU5pNB3qrSfA00u32ND5JVDrn/xG5hIQo3kvTxSlWFRP0NJ0+n6HmhPGG0Q4jtQsb6PFoyg==", "signatures": [{"sig": "MEUCIQCLI/19ur0Z4aj7G4wD3cgfJOOOw0OXf/CdLMkvpsaXHQIgMIMJOI1ChiBfwBIty713f14PVb3jpJ+rx33vwQbHR9Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9437692, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj87QTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq+Og//RCeWBXw4cnMAYJWZy95agfipx/W9lsRMHNO8jKQBCKIR1Cbp\r\nGWln5xNrqfcsATrNrSzMfW02q2tlaesmO6FfeMij2Bq+N91dCU8YjDhQ3KkJ\r\nFAk0CE5P4v8/3H+DoW8xS72Dlun+/WfgA4gadVKuWr+tAcd90iS0qsRsfIRh\r\niLrtlU82eGeuFXHY2INtSMUa8IWw18Z0ATgyhuuuA2OV6zHtxP7/a7YROQRr\r\nW9e9gIAd27/6FXx6r10fw+25wLF2XAVDEO+fMNOQ97RRvhUB+g3hu4+8lDOo\r\nj2YEx+fZ5Orwtcts2H8PYT5tRt4NZPpnFarS7g+RXILvW0XTUxZ5wBVgrKiW\r\nU0vSWrLqT1zeZm1BXt1wSCYju/y4+wq+c/kH1nRvbf2LwcRLhVSpB4bxApWm\r\n3BJpoCqMGYuZ4Z4dH0k8etGTtOOYvhLoR4d5/+tk0Oy225Vs0pU7G9MA3naW\r\nnx4ULUbcJNNox0O07qxD1rMCS6S/knl0iY7afGqT26u06FNrgp3P5GvSaUuH\r\n5Cvw9Fub+MtHfCYsGRPr2AXPs8fEFzJdjBYPv2iBwhY4lGFNLmjfGnBXHVZf\r\nRDqFtD0SZzc845ibG1hpzdejh2z6tYF5IPz+kx9wHPqFQ27TbROgMLw3FBpJ\r\nsPo1ZB+fOyDZI3znXcn3TLl+pzy51Hdb7tk=\r\n=3uVa\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "996d400a7ab25b67b80122e2d4a8515575918e79", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.17.10_1676915731025_0.8495072211914823", "host": "s3://npm-registry-packages"}}, "0.17.11": {"name": "@esbuild/linux-s390x", "version": "0.17.11", "license": "MIT", "_id": "@esbuild/linux-s390x@0.17.11", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "4bad33894bc7415cea4be8fa90fe456226a424ad", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.17.11.tgz", "fileCount": 3, "integrity": "sha512-4N5EMESvws0Ozr2J94VoUD8HIRi7X0uvUv4c0wpTHZyZY9qpaaN7THjosdiW56irQ4qnJ6Lsc+i+5zGWnyqWqQ==", "signatures": [{"sig": "MEUCIQCSCkypUhnyoKYVdRHUirXer+VupOIxM6CVqGUeNmpKVwIgAWSa8MLnN+WDYdn5y3fsTeaQdyizDnZTucpRTLqs5xs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9437692, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkAnd6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpD5A//Q5H2evTaQOOV05j+yDd3hnjae4sVMWWU26nvIKP8Pskd94JJ\r\nL1Xh0JuD7VpQOi+8wCjg2U14jz9LC9tncF78ZY5pQMc1V28B8njJQ5pfMEn6\r\nD3EJ29WfyxC66+VhtzWtAJXVvI+Rp0pqqJiMn1RmUfsFgxjIODkZMKhqtoBh\r\nhQAEvvHyfwUPDufwUC263Ha4BXo7ztnmQygLV/FjQtMCJ08tR3QfW+FoixyZ\r\nlsRRVY4Cym5LESmeZoP7JXNO4RtNKgTIrxZHZWuBPqeN3cDVKSlOqMwWFpCI\r\ntUKq88dqPD3Lcl+BBO9UDo0jz6+t8FCC5ewqTCkhGSn7f34cNDOwX1NB3g5/\r\n7p9fz0Yl6p6i9lCYksNpawPeBFaRgHOthKOATxOgTTRCmvkHZs29NoOwSacC\r\nfu+M9jMAfAjbrKHyn0tZOctm4lxna8HgW0R0LGjTDXMtg02+Gnatjafn2IwD\r\npWIfXo1jYhDHk2xA/iEHMHoBkNeIr4W338pnsenpWWYj1xyKLyF5VNIx4+2w\r\nDfUAHgH2jjILTc437WITt04Ejwab/NkVcqwC/0vg2mEemk5xSLQByGWNbv8f\r\npwqfOsQ2r8RQeFTU5Maqnrcsk+hyC4TkXhlQ58I8obIptgyFBBHeEdDUlXnB\r\nIyNqcwMJbWk0XPqEuvZkJAO5uT1EdIuAGBk=\r\n=JR4R\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "38cfd53020d9e0454bae0956e549546c55a66aed", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.17.11_1677883258215_0.8951860488633765", "host": "s3://npm-registry-packages"}}, "0.17.12": {"name": "@esbuild/linux-s390x", "version": "0.17.12", "license": "MIT", "_id": "@esbuild/linux-s390x@0.17.12", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "21e40830770c5d08368e300842bde382ce97d615", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.17.12.tgz", "fileCount": 3, "integrity": "sha512-j3ucLdeY9HBcvODhCY4b+Ds3hWGO8t+SAidtmWu/ukfLLG/oYDMaA+dnugTVAg5fnUOGNbIYL9TOjhWgQB8W5g==", "signatures": [{"sig": "MEUCIQD6rRt8XshqcpD85OdLEXHDT0DcpwzhpEeAFPWUxGmXngIgYoZyILMulpgoaJbHHV/51Clqg8nTOgtuLMbFswqcsCc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9437692, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkFAYNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpHYA/+Is2gAD/dQ7ytoBYfh+f3tXUR5uq4g0sCSdJtFpbriWd5t6wz\r\nPh4L/RHAktGCCKEh/KLG4ISU8qkGOBue0nme91bWtKl/Hq390++UoMYy1sM3\r\nA0y2zyD/MM7DWtHFgalExGI4eMogLQC/ECt4xn5m2ph7ydKoPMh4OQAlWUdq\r\n1OULskqUzHK9f/o43/AoSgaOQszm3Lave6EN0BdOQfMWnQiHh6b+NVELyMfc\r\nzyQsBi2JmQSMFB++18GxriE7X4Lqp+KjFeqPjkr54DRfNmweKh9fNHBa2DBg\r\nsy+typc+tpDmiXKTcidM9Gfz1Yyquo08y9YSDVdAiEpWSIgLijV+CcjSWRPy\r\nsoC0Uos8Xm5UHkkWNS+CMh0tmuAREEtco4gBNuU8HP8yA4qyg1uD9JWuvVVa\r\neFLQNeuSaS4r5/2RmFOuUAcIof5Euzhmbx9Y6X+z0nRgHIyu25vHyuLmH+gF\r\nfrNCv0BHiiJ5/gPekKsvLbaMQ07d52RcDeLh6In29MLXDY+qNDJ3GEog85zY\r\nUYQo5qZXOz5qX/DK3lAHC+BYLJRNmYP1Bb8CCOCz7CmFpRZYbmWoukc/V6s3\r\nTIduxEpx6pHaS8AA/c+cI9GVjGAXIshpvRo1uQoiLDBAtaccGDMhFbrgKnAr\r\n5h0LrsV/H+2E7fIJWdBqehwEIbSKL5SB5Vs=\r\n=UX7y\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "495216dbea685cd0e10172a866eeb8ca4764a0bf", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.17.12_1679033868834_0.8305840324819014", "host": "s3://npm-registry-packages"}}, "0.17.13": {"name": "@esbuild/linux-s390x", "version": "0.17.13", "license": "MIT", "_id": "@esbuild/linux-s390x@0.17.13", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "38dd0677b876c29280785b18cafc1673961e7f8e", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.17.13.tgz", "fileCount": 3, "integrity": "sha512-4jAJI5O6E/hATL4lsrG2A+noDjZ377KlATVFKwV3SWaNHj+OvoXe/T84ScQIXEtPI7ndJyLkMYruXj8RR5Ilyw==", "signatures": [{"sig": "MEQCIGay/0jBNySHqC380wKpf2ekcMFGov/gHODAutQrkFInAiAZuUtD75utLIjJ7PohKM7EF/s+0A7bsbL0h0A8If5AlQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9437692, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkHfKpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpH3Q//aRFSGCsMOQ76KO2G9tPV92XbuN6AzD6oIJkHaDt8AZViP4te\r\nOlxmRh/TfW5cTRjwgG875wZptmew+Svk+PPnClVXVll6jxfsrcYKJe+Rpc83\r\nFVcvIFfIeVa1WLL0KKpntU6kjTr0k6qjpoU+yPA+fCd83Sf5d6iKqqZ4OHpj\r\nyy3g3EPX2t1xCI/h1QnOLyx72OAZjrQs5tjummKe1L2t/F1rW0HWMoshLGjt\r\nWJ46bG+xENiRaS09uD+sNLRt0qxHUtKAzWG20CNsqs9qZ/1I7FdjCKUkx9Gi\r\nMz3v+0dnoqJuTTfaOTqZx2ChDfDGvNcjhxX1EkMoRoTFcgTZcz0caUVsVX2K\r\n5MrBrQnK/bj/roEgbXWDy2qmoZGu09ZYQgtpBjg4fmcXf5CQlWfZQP/QqzUj\r\nj/tZ3JIWg2ReOcDXYCQ2/scZ3CdZK3yctCCZ/ptZ0zl3+dwelLhqFn9kXw5I\r\nSjvNK2tOe2UyEpawWJG4xhnwt0dROTiabPqyXWs66GA7P1gbITLBG5OozwXW\r\n7by+H+HtdHgUrD5cJkBsiqwNEkOl2U/eaxWVcHtmSFMg03rev9QuGGGiKMMv\r\njbye7gtm3yOH+00mpes0/3iOc2Hu1cFlCUpyTTCe4hjhKUefpNl/6xWHiZ0A\r\nBPqO7JPSxKnCxOByIVb/xYaTQZASXZDlTAI=\r\n=Ne5b\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "8dbb85531a9971058f4d8739cb52e98fa43b40a1", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.17.13_1679684264843_0.47924313242974437", "host": "s3://npm-registry-packages"}}, "0.17.14": {"name": "@esbuild/linux-s390x", "version": "0.17.14", "license": "MIT", "_id": "@esbuild/linux-s390x@0.17.14", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "4397dff354f899e72fd035d72af59a700c465ccb", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.17.14.tgz", "fileCount": 3, "integrity": "sha512-Hdm2Jo1yaaOro4v3+6/zJk6ygCqIZuSDJHdHaf8nVH/tfOuoEX5Riv03Ka15LmQBYJObUTNS1UdyoMk0WUn9Ww==", "signatures": [{"sig": "MEYCIQDQfuh1CrEwLduU+7KxUBvmbsQs0qyjXjA973LZoGeOIwIhAIvTq0B9CJZlftjxXrk9ZP0pxA59hpNyr5bGdecPj0QI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9503228, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkH7J1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqfrA/+Kh+9cgflYbAftNoqzDfNXXhm1YNc0UcbkCVeR+IrvvbEirdM\r\n5SCjLKevriQndgwAzxlukIZfnillNkEi8DAiI15NGiKKNj6ND9AOXZvdkLv3\r\nvV4HMokpkq7BTGWYxoTsk35/1GTtQTkakPBYMZJlJkskP9QK86Ze0xZZeJKb\r\nAHpj8oYlpFTbXw/AKiKjzl+NCW5SxR+9vPjFlcCK/e+fVNo+nBLWDyUnQwxI\r\nLsz9HMWai/4ty5IsPw3rfU02FIOrmcW3RKxtIW9oj0l5RLn9VtO9MFJAIaKY\r\ndliLSbmgggIvQhModzt+3Afdiql/+BN0W6bAwy5Ed701V+eJmaXo04RkrTAC\r\nL5b9rpP4lxnqT5nnLf7GrsNsgYOxtwZQ8YyfJEaR9dxxxSy8fBK/FAmoTG73\r\n3WFPw206S1aBiRxE+7fRko+XVvXXU4mE7ZELDDvdUIQUMNGiTiuaZ+k7EyvA\r\nlqcYTsTbu/HYVhHFzH1VHvjFp9o+b029sxYt+LCHlLVxM7k7dbPZfXhcWBtG\r\nUO9BWP/bPYfDUf+0nzWzUpWSDLPfJq0mQXu9UeGniKVGznNYRPGryFvVGdwN\r\n8x83g97gCAdZsTcjBdMv2RUWf8OV2XllfFa/pjW/WB6e27DKvKODv98xCNdi\r\nzlLM0BIk8U2A7S+TWqBwcNcLy46ZOKW9QLE=\r\n=jM56\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "b2b897870564a6b8e8bc802a140c55bf602de31b", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.17.14_1679798901437_0.09866383740997287", "host": "s3://npm-registry-packages"}}, "0.17.15": {"name": "@esbuild/linux-s390x", "version": "0.17.15", "license": "MIT", "_id": "@esbuild/linux-s390x@0.17.15", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "20bf7947197f199ddac2ec412029a414ceae3aa3", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.17.15.tgz", "fileCount": 3, "integrity": "sha512-BuS6Jx/ezxFuHxgsfvz7T4g4YlVrmCmg7UAwboeyNNg0OzNzKsIZXpr3Sb/ZREDXWgt48RO4UQRDBxJN3B9Rbg==", "signatures": [{"sig": "MEUCIQCUYs0kqSsK3k1Zl9H//uNEUGeAH/FL2zDWgiU5E2xUWAIgLuKzdMrj1Le9Kc7L9gT9/HplIreyhpybfsT4c1FgR/M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9503228, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkKK/eACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpe1g/+K4yY0hCsNe5lScn9mcE1oeMeewP3j9esjlwRRCwBcM3/d7qW\r\ntT+up9VriBrqFrlpL4TdPnEhhES8URC45YR3r1ZJF794/FfFs7pihPkrIhkO\r\nnBPRqraR1/LSSCbLn5eyzZ+J87vVa4id9z495cIT3Qs2eGHIvEDh5qmSrqEw\r\nzKpsFRapUYfSeWYwOwt8OdJ5LWKDjcohmJVnc9SIcnfIgWkCV7bqubXpQk8S\r\n163ayHAuLBLxcWzcnqynGNFiZFuDym1LTPpPEdTbT4uGb6wo9JItqCf1gG+z\r\n9H+RqQUvWoIgKg6VUEgV0b0dwDRP6ztL+gf+Fb3C08lmbsf4Is4sdyyZ3GN0\r\nYoh5VGyeV/BwqZnKLp7qBCre9S4CCpoK2WKlZ+1YwrBJraS4sAZvJxcmozg9\r\ny+K6o6c1McXN8S6wNzMzQrH4ZO4QA8yfo4twmvEVwlN4Pj2RVOMYXawy/L7s\r\nhPEnO7fylWoE+yngBEnSPKDXEQjjRstheByIVNx2+tl3H7Mms11P90/k0xGY\r\ntEMSNEThxiqgrgSTzQm85cdIzsae45HsUVH4VNqB7m25cKYNxlnTymc4cHJN\r\nzelsumRK5tcW8SUQfZDHmTS9jTKxsS2Y4SKqOb1P8eCguq0s0esz79c05EFI\r\nJxA9dTWeJUb5u3mxvEgyGXf3OPNcbXcnDFk=\r\n=U8lx\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "ef912892181e27386205d110c622c55c4c1df856", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.17.15_1680388061993_0.4329058454071144", "host": "s3://npm-registry-packages"}}, "0.17.16": {"name": "@esbuild/linux-s390x", "version": "0.17.16", "license": "MIT", "_id": "@esbuild/linux-s390x@0.17.16", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "d2b8c0779ccd2b7917cdf0fab8831a468e0f9c01", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.17.16.tgz", "fileCount": 3, "integrity": "sha512-gHRReYsJtViir63bXKoFaQ4pgTyah4ruiMRQ6im9YZuv+gp3UFJkNTY4sFA73YDynmXZA6hi45en4BGhNOJUsw==", "signatures": [{"sig": "MEUCIQCXnLlUDiFhgpoOxhDA6mN77xXuvmXs4cikyAMn6IWARQIgUKM6KxkqwGJ2AxRAMNM8XKZRKyv0NU/e3q3jpYkL7qg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9503228, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkM5ImACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmreShAAjkzRJskeP1DefASB16sF/aRLf5h51oqzeQev8cFvEPW+7VGE\r\nLKf3P7NxEEyKogqFDLcM0/OhysWuxCaWOCpQHljc4FJg2rDa5ca3woQVMuty\r\nu1Em25L5npt2Ll7b3bCkGcX9eI1TO5qfxIBqy7JwAvYtZ96Hx9HYrpdJ/RKn\r\nEJeY1hubzI/EJfUC7b6/BzV9yVgF9bpywfTK84iF1QSoomsz6CNA1G3u5kmN\r\nkiY+UMGyt9lLwGU3BIkEV4TW17hUlMOdcLWJrDl/jodpSYmjg0gEv+98iDfT\r\nuWOFDQGHWO+V7eRc/5qXMFDv6fFqa1lMu0v+ic6l6eteWDPG9Y9pOfyraNYn\r\nlE26tJIVtnhOYrpwcSee1dEKZ6xBGkScl7u2uG+kP+mPVjn4u1p3wrCBhSHK\r\nrsbyslECzRq1/8yOazb8DL+t28ZfPaQ0brGT75y9FbHxDJWU9Qfzkluq69sc\r\nqCJadF420w/FFvJhVUaDN9abFyLmpnDTtY/0jqB6IKoW4P3zWTOzrxa03FPa\r\nCQw/8xJiMyCMyImXeyKJZPnRulYN2ASI+oTL5qdkRwcv0kJH0gzmqVN3UYvk\r\nUjd6Zrl66n7xyTKCNea6/Fgjr37plYXAr8NeXE8c5zdZyvcvWAE6AhCvLKL7\r\nl+QhNfeo0uxeIYwWGSEvtKyuq+Ps5uKJLDI=\r\n=FMRQ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "f0704baa38ef9a808b7e34cac75aa4ac285bb087", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.17.16_1681101350186_0.3817800103321318", "host": "s3://npm-registry-packages"}}, "0.17.17": {"name": "@esbuild/linux-s390x", "version": "0.17.17", "license": "MIT", "_id": "@esbuild/linux-s390x@0.17.17", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "83cb16d1d3ac0dca803b3f031ba3dc13f1ec7ade", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.17.17.tgz", "fileCount": 3, "integrity": "sha512-lK+SffWIr0XsFf7E0srBjhpkdFVJf3HEgXCwzkm69kNbRar8MhezFpkIwpk0qo2IOQL4JE4mJPJI8AbRPLbuOQ==", "signatures": [{"sig": "MEUCIHsEuuRy3QPxAR+4jsmURj+g4fB88vQSR91TA0o0dfkCAiEA1kw0p5hP1c9YJ4NWH6FYD+5HCT8DknNRNbEBWXVi+YE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9503228, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkPGd+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpZHRAAoaN4aoztLSF5gq6Fb09xUrOCB5XR5Vwr8RxRrxH8uIZJLadE\r\nrD0AW3Uw9/HVEOSJ/xBpzOAoH1UjcUXRAdlsbZlUpjA9Q1yywLq1HJRM324/\r\nTT+OwDBfRSFdJPaEO/t+4Pxef4G/3O1GftQCUKqrliJRZ92+Y3+Xs+2fG9P6\r\n3S/ZpOXptsnkrBbwp1KN4QKH/ntmhHpPgd1U4qZtOhkcO1iBKup6bz0MV1Ic\r\nur24hVZysg6uHokuOUJ2mb13zEeLwG5K687N2bcEwnqK44yKVH2EkO9tYTNG\r\nZGPHJpuPSOZeGw6294PhTNEbTC4vn6iF56lTWwztlC5Qnh03hFxn5lWeWUEm\r\n/2vjsEHXOjFlxGJ5knToK7H8QF+mzGHSSi457QOV8lXOGqswYl56JEIybvrx\r\nOEfOEq/Cs2bo0cFoWlv69GVUSGiTLgtTcmjHHkPMA8YsDyk9rwt+m0MFXDLJ\r\naRi530bfwGfJnNBMeiiYck/0BueaMbAwYLYA6bgmLtO7S9bJ380q1jJMwxNq\r\nwvEAB5hj+gfHxCL+TgjYlJnDGqcekx+GFdiO7jv/AbO154YsY02F/Bk8yYG7\r\noDoHIsNsLgNuUH+/LAniPfn34JvZeFASJdHnQe80yMkAoG2J34E6rgKGnGwx\r\nrMWHJ682izBo3sOgPZsPi5c0T5zY9reUWGY=\r\n=8Iws\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "0776a4be2bb80980482b123a9a05dbf55cc35683", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.17.17_1681680253866_0.08016679274435279", "host": "s3://npm-registry-packages"}}, "0.17.18": {"name": "@esbuild/linux-s390x", "version": "0.17.18", "license": "MIT", "_id": "@esbuild/linux-s390x@0.17.18", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "66cb01f4a06423e5496facabdce4f7cae7cb80e5", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.17.18.tgz", "fileCount": 3, "integrity": "sha512-cX0I8Q9xQkL/6F5zWdYmVf5JSQt+ZfZD2bJudZrWD+4mnUvoZ3TDDXtDX2mUaq6upMFv9FlfIh4Gfun0tbGzuw==", "signatures": [{"sig": "MEQCIFBavkIz5ke91VUvGld1EGmAmTx265zj8hjuzuMTFI+FAiAF9dLAegziOR1w/PJTLwbx8AcDZEr0fNaOrUZysP3sYQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9503228, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkREaiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmppWQ/5APpaHdQxfTjRRdvgmSx15ZZuJys2fLyrddeTaLoqoTIf/ftp\r\nWGwOdWhzd4AqbaDYK6PHoW8wuy19fi8u1v55QGzCgeX9tsv+H66FDPL1AZVl\r\nsSr/m4ytGrfOtS1KxX0+DjkRT5HPhRe3VwdJBaizAnXDesQ5SXPQ2U6+oQWm\r\nKZ7Y9rd0aDE0mvkaKwoUjzncxtDhWqFftQLmI0yj+q6oATIvs02dXccMGj+y\r\nHvKCb88HK3nkOdnXsfrfXlQjMF8NBCYisFweFnyJxNPHf4awC8CghcTI1MSs\r\n3HV9JJpOC6RPBa26EWg3iF+W/+W5UxbGc/rERwuFXt8VKELEsdUlsxH4gcv6\r\n64TW+ZYoy+h/fPGFjed2ULZaHiOKB7XO96Jb5n4l7JlvtpiPhNRN3lz9DiRr\r\n9VVPsESx2EFO83QJGYmo/sJdXI5v2Kmpt3ft1xq+AvwRNGSa5Jv1PqOaws5V\r\nNvYMk0uuagxryhLgsIxAN6J9o+pjvng22dUJT7xDdFUWVGFW7nmgOaic5Mp2\r\nFOfYYTIeDadUYPkCnUAvoAQ4LJO+sZ1dt9yuUe4EVwR826Vq5QcjR07hnepj\r\n4AwSRP4Uf+rrsj/S6LatZA/UAGpywPh/C9bLF+nwWdqCjR+YNQMc7mx5UGQ2\r\nKFI5JYUEUwLmhg/eTUmnO+QTi/lh5A4Mmks=\r\n=nYOH\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "ee646b4ed8d3b9567e1a0ce2e478241b68a2a2e4", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.17.18_1682196130208_0.7191542233483752", "host": "s3://npm-registry-packages"}}, "0.17.19": {"name": "@esbuild/linux-s390x", "version": "0.17.19", "license": "MIT", "_id": "@esbuild/linux-s390x@0.17.19", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "e2afd1afcaf63afe2c7d9ceacd28ec57c77f8829", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.17.19.tgz", "fileCount": 3, "integrity": "sha512-IbFsFbxMWLuKEbH+7sTkKzL6NJmG2vRyy6K7JJo55w+8xDk7RElYn6xvXtDW8HCfoKBFK69f3pgBJSUSQPr+4Q==", "signatures": [{"sig": "MEYCIQCNdgNXNaRh+PQhwWd7vw9/RH+rwOv1mxNGY15iqoystwIhAO7efIX5MpCWT7wS20QgLnGSncUD5uMl9z0c22JxQlQ/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9503228}, "engines": {"node": ">=12"}, "gitHead": "d47ab43980c457db27d2671ab618cd5c40a618a7", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.17.19_1683936431166_0.5423053942169518", "host": "s3://npm-registry-packages"}}, "0.18.0": {"name": "@esbuild/linux-s390x", "version": "0.18.0", "license": "MIT", "_id": "@esbuild/linux-s390x@0.18.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "aba0c4e869d709ed564298811434851b9ec8bc33", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.18.0.tgz", "fileCount": 3, "integrity": "sha512-MgyuC30oYB465hyAqsb3EH6Y4zTeqqgixRAOpsDNMCelyDiW9ZDPXvMPfBgCZGJlDZFGKDm2I9ou8E3VI+v7pg==", "signatures": [{"sig": "MEQCIFrPabKAuswGBK0fbsWvAh8BmgPdgU/uETNINfBfj/3wAiBhkxQEBOyWAQcueQUEPM1PfBlqiSPWwWeBGOP3BXXlpQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9503227}, "engines": {"node": ">=12"}, "gitHead": "4dda49d4ea86afcbe715bdca5e8f4b13659e0c2f", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.18.0_1686345896171_0.27599228896380135", "host": "s3://npm-registry-packages"}}, "0.18.1": {"name": "@esbuild/linux-s390x", "version": "0.18.1", "license": "MIT", "_id": "@esbuild/linux-s390x@0.18.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "e9b3052cd2ea1fa929da60e54112678cc8f9df0c", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.18.1.tgz", "fileCount": 3, "integrity": "sha512-u9iRg0eUUIyBbg5hANvRBYRaAnhVemAA2+pi3IgrzQTMeR/uPHQtJI3XInNZkNR6ACA4Fdl8N941p81XygeqWQ==", "signatures": [{"sig": "MEUCIQDdylC/65oSarzz4KD2CZRHxkRnDHa+Uk08cRtrZIeZNAIgHggAElMMYI4i6XafSM+DvdVjtXDZNVQOm+MV1+PVA54=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9503227}, "engines": {"node": ">=12"}, "gitHead": "3aa3ec2da489dad64d90aa965c9782984defc904", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.18.1_1686545538555_0.3364465756931059", "host": "s3://npm-registry-packages"}}, "0.18.2": {"name": "@esbuild/linux-s390x", "version": "0.18.2", "license": "MIT", "_id": "@esbuild/linux-s390x@0.18.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "e88269b0b4924d2b239f5e6da3493d1cbb71b2a0", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.18.2.tgz", "fileCount": 3, "integrity": "sha512-izzEFMRO8LaQIlX22+fTgP5I7Os3T51mtAWsRNpZ5pMfQIa9PqtgFAoRcb10DV+/YkH/TMMxQIlevUvDS6E4vw==", "signatures": [{"sig": "MEYCIQDGRMrgcfjLjjDLYNvGfTGEZqZf/lKFD7Q47HKbgNsF3gIhAM2zAVCtXmIQhHD5Pa6i2hRdDJD2M6FMcagLUiFR5EuG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9503227}, "engines": {"node": ">=12"}, "gitHead": "a7a909605b1387b5c74d1bd0217af3fb4a843461", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.18.2_1686624093621_0.4637551542689915", "host": "s3://npm-registry-packages"}}, "0.18.3": {"name": "@esbuild/linux-s390x", "version": "0.18.3", "license": "MIT", "_id": "@esbuild/linux-s390x@0.18.3", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "4eeef68a641308abf023b9bd2d7c7e60133394c5", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.18.3.tgz", "fileCount": 3, "integrity": "sha512-oIcK2LqHWqfMERqjvaKJ3QJmycHn723HsXIv5gH4iGfmePfSj+gi0ZQv2h4bHUg2bs2gJtV0DlIjGhEuvdgxLw==", "signatures": [{"sig": "MEQCIEuXQ3Uuh6Pxga44SbUTeVV0wCOTl/SKdvsEhddsA604AiBrQQgDrA1vtNv/ieiS+wjfOm8gwkGYrRkyuOeT5nQ1mQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9503227}, "engines": {"node": ">=12"}, "gitHead": "9224cce93632b0fc6db8767676211fb44ac642a7", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.18.3_1686831733877_0.7985075631900369", "host": "s3://npm-registry-packages"}}, "0.18.4": {"name": "@esbuild/linux-s390x", "version": "0.18.4", "license": "MIT", "_id": "@esbuild/linux-s390x@0.18.4", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "d7d47a6cde5536e97b698d5e913f3e04662ff3da", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.18.4.tgz", "fileCount": 3, "integrity": "sha512-D19ed0xreKQvC5t+ArE2njSnm18WPpE+1fhwaiJHf+Xwqsq+/SUaV8Mx0M27nszdU+Atq1HahrgCOZCNNEASUg==", "signatures": [{"sig": "MEUCIQDZxAIgbx6YGP1rmGlyvZXZfK/YLwLZ2D7Aecbsl3tnsAIgNXm8v6VOeNOWu1vYup2Q6LOBjC7yhoYmuCng7HULx6g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9503227}, "engines": {"node": ">=12"}, "gitHead": "bfc5a0fe07b6f6855ff3ff11a91894066378c5dd", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.18.4_1686929953880_0.7103096960337865", "host": "s3://npm-registry-packages"}}, "0.18.5": {"name": "@esbuild/linux-s390x", "version": "0.18.5", "license": "MIT", "_id": "@esbuild/linux-s390x@0.18.5", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "2c51d84d1224cd52e96e46abe33fe9f2670395c2", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.18.5.tgz", "fileCount": 3, "integrity": "sha512-m4uIYyrl5znGnNHgiM/Zsw6I9Se513NqdTxeUxZ66/VDWbuUp8ACe1KOSpwF4NNxfYy6Q3W8beZsIdF4F85q8Q==", "signatures": [{"sig": "MEYCIQCtSvlJKMNmSwtcMSO36jLMdqkFlaG2LlYMT87NSqBKFQIhALjjXyL32CKymqLmns35OaCjDkZW6J6KNNYgCvfI1aIk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9634299}, "engines": {"node": ">=12"}, "gitHead": "931be1b9b2312609c5214812671b0d2a21cfe92b", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.18.5_1687222407264_0.7211644215114306", "host": "s3://npm-registry-packages"}}, "0.18.6": {"name": "@esbuild/linux-s390x", "version": "0.18.6", "license": "MIT", "_id": "@esbuild/linux-s390x@0.18.6", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "82ca83bb86566413e1c5a80a164d41bda4c947cf", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.18.6.tgz", "fileCount": 3, "integrity": "sha512-FlYpyr2Xc2AUePoAbc84NRV+mj7xpsISeQ36HGf9etrY5rTBEA+IU9HzWVmw5mDFtC62EQxzkLRj8h5Hq85yOQ==", "signatures": [{"sig": "MEUCIH7vfikoai6+B++MXnst4OJwynJ7ziMvtdEZgAx94STeAiEArqS+bYO4UyhxoqNIUHMWKzwmsg1YVv09/oSPbbrqNDk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9634299}, "engines": {"node": ">=12"}, "gitHead": "f0b5803694c3d74c1d84851a518e3e25916ec005", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.18.6_1687303531462_0.3435707776592489", "host": "s3://npm-registry-packages"}}, "0.18.7": {"name": "@esbuild/linux-s390x", "version": "0.18.7", "license": "MIT", "_id": "@esbuild/linux-s390x@0.18.7", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "77a3c952b19006af2e288658d3c29fb6c02c9bcb", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.18.7.tgz", "fileCount": 3, "integrity": "sha512-fjBl45O8ivc3Nl14hdGpbHoVtdHnYGsLpwnlv2rNyb5NOsgY3Y8EhVe/fqR9ndHgO4eL68knKxkrRveEJq+v1g==", "signatures": [{"sig": "MEYCIQDoh3qG1UJoP+SAln2jm9VyMPIvCicGQk1DGQPxZXhqlgIhAPeGYXjpVNSV260I7GQDL5226nrBLnXpUrp1WwWNRDyr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9634299}, "engines": {"node": ">=12"}, "gitHead": "adb8d19b56d2ae2d65128305c875b577476fac93", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.18.7_1687574823578_0.49689377071616514", "host": "s3://npm-registry-packages"}}, "0.18.8": {"name": "@esbuild/linux-s390x", "version": "0.18.8", "license": "MIT", "_id": "@esbuild/linux-s390x@0.18.8", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "2df4670cf4d0a85c7b428218256c0bcb921cd639", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.18.8.tgz", "fileCount": 3, "integrity": "sha512-XU3UTgyFx80B+kCD82kun9usGT1+3YILtGeGx+StNWGT8wjHYCc5ZTsh4g+58kDoGPezquGO+Kso5VSlX2GU2g==", "signatures": [{"sig": "MEQCIC+A3vsrSjNVFK1fwMwiRoC25Ejt6aKLC8aM99WnN3R7AiAygIUzziT/WqTfZYnVzlkRLsY/m7ahVfCU5zAoJKscWg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9634299}, "engines": {"node": ">=12"}, "gitHead": "9b233a4f670a73173dcc7e83ebf7648d0007b082", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.18.8_1687663190454_0.7405524758290019", "host": "s3://npm-registry-packages"}}, "0.18.9": {"name": "@esbuild/linux-s390x", "version": "0.18.9", "license": "MIT", "_id": "@esbuild/linux-s390x@0.18.9", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "0f57a44ce133048218963b04075bb2956e4b2c3f", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.18.9.tgz", "fileCount": 3, "integrity": "sha512-XsnaI89KstE0jG4cMdzuJ8SKcKAod26had7U/4SzvuMrci0/XyEQXB1jikn6MB7LPGrd5rcLeYp3F7psUxhkWw==", "signatures": [{"sig": "MEYCIQCQWovEG+CjYz+2QuaxFb3UaRmWR01W3JBQsZNk4V96gAIhAPUDB2VARNPJH6UeJCOK1TJSUb4fMxiT0lOF82MaClFC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9634299}, "engines": {"node": ">=12"}, "gitHead": "d568ff038d012a7894a9d4334b75f8559bf2532e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.18.9_1687757342791_0.3675482620039998", "host": "s3://npm-registry-packages"}}, "0.18.10": {"name": "@esbuild/linux-s390x", "version": "0.18.10", "license": "MIT", "_id": "@esbuild/linux-s390x@0.18.10", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "84318e86ee1e377c603c7b5359f5f67771eddd99", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.18.10.tgz", "fileCount": 3, "integrity": "sha512-NLuSKcp8WckjD2a7z5kzLiCywFwBTMlIxDNuud1AUGVuwBBJSkuubp6cNjJ0p5c6CZaA3QqUGwjHJBiG1SoOFw==", "signatures": [{"sig": "MEUCIAC6xq5a9o4FnN2MBkwiB7G+TUjWnacLR6UMbzXaBmJqAiEA9PhoGKqAewTP+By51Ohtp2J06JFfCjAovP8J9yJ1P4k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9634300}, "engines": {"node": ">=12"}, "gitHead": "cdb6c7ce3f9419e51bd855fd61d07d2c615fb30e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.18.10_1687814460069_0.9752194838058339", "host": "s3://npm-registry-packages"}}, "0.18.11": {"name": "@esbuild/linux-s390x", "version": "0.18.11", "license": "MIT", "_id": "@esbuild/linux-s390x@0.18.11", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "98fbc794363d02ded07d300df2e535650b297b96", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.18.11.tgz", "fileCount": 3, "integrity": "sha512-/SlRJ15XR6i93gRWquRxYCfhTeC5PdqEapKoLbX63PLCmAkXZHY2uQm2l9bN0oPHBsOw2IswRZctMYS0MijFcg==", "signatures": [{"sig": "MEYCIQD288GYPPDcVUI2vpwuXq62AW0IwsqijAwJxrIngcOUlAIhAP1Xgent0y4E3t1lpGvhjLzXVNRQ+pMXQ4+EVuA/oTsv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9634300}, "engines": {"node": ">=12"}, "gitHead": "2703f90d47fd96f425fedcfd2a5c318a43b04d45", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.18.11_1688191472363_0.6962970397321357", "host": "s3://npm-registry-packages"}}, "0.18.12": {"name": "@esbuild/linux-s390x", "version": "0.18.12", "license": "MIT", "_id": "@esbuild/linux-s390x@0.18.12", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "48db270c54e8d32110e0aa63f8a87d2485fb6cdf", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.18.12.tgz", "fileCount": 3, "integrity": "sha512-dJ3Rb3Ei2u/ysSXd6pzleGtfDdc2MuzKt8qc6ls8vreP1G3B7HInX3i7gXS4BGeVd24pp0yqyS7bJ5NHaI9ing==", "signatures": [{"sig": "MEUCIExU7MIEtskJ6jmL/nr2tADYfkeHz+9AeySCLaFwWoLcAiEAqKHqB9DhT+wTSB3YK4fyQOufiNMoj63DiMRrwcBJ+wU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9634300}, "engines": {"node": ">=12"}, "gitHead": "d196e4c4898fc46dc553124e28b1b29829ef7f7d", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.18.12_1689212087516_0.12089059259822865", "host": "s3://npm-registry-packages"}}, "0.18.13": {"name": "@esbuild/linux-s390x", "version": "0.18.13", "license": "MIT", "_id": "@esbuild/linux-s390x@0.18.13", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "c1367a0a02b37f6b0382e71d9c9d97352ca23013", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.18.13.tgz", "fileCount": 3, "integrity": "sha512-Pct1QwF2sp+5LVi4Iu5Y+6JsGaV2Z2vm4O9Dd7XZ5tKYxEHjFtb140fiMcl5HM1iuv6xXO8O1Vrb1iJxHlv8UA==", "signatures": [{"sig": "MEYCIQDPeCPq+uxK467n6rZPUHHIfk/GObghaP21BI7u7+yI2QIhAPfpd3jzwEqtVpilAZGmaa7qKovmVp7bLIVfCboJMnEW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9634300}, "engines": {"node": ">=12"}, "gitHead": "12a8a25b4ca8b650d7c96046b8a3e76491a119f5", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.18.13_1689388673074_0.7924697416025694", "host": "s3://npm-registry-packages"}}, "0.18.14": {"name": "@esbuild/linux-s390x", "version": "0.18.14", "license": "MIT", "_id": "@esbuild/linux-s390x@0.18.14", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "3987e30f807b8faf20815b2b2f0a4919084d4e7c", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.18.14.tgz", "fileCount": 3, "integrity": "sha512-G/Lf9iu8sRMM60OVGOh94ZW2nIStksEcITkXdkD09/T6QFD/o+g0+9WVyR/jajIb3A0LvBJ670tBnGe1GgXMgw==", "signatures": [{"sig": "MEYCIQCjsQ5jaFlin+DjR/7Yu42x5miY0ITlAJHB3/mwPjHOxgIhAIdPXakUbDH17tt9Qp+GFHj+nYSDzye7vvYJPBViGoN1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9699836}, "engines": {"node": ">=12"}, "gitHead": "af0fe32eaea8112de45e17cb8d0ad487b2123132", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.18.14_1689656457381_0.05950274947125034", "host": "s3://npm-registry-packages"}}, "0.18.15": {"name": "@esbuild/linux-s390x", "version": "0.18.15", "license": "MIT", "_id": "@esbuild/linux-s390x@0.18.15", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "ce608d95989a502878d7cb1167df791e45268011", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.18.15.tgz", "fileCount": 3, "integrity": "sha512-632T5Ts6gQ2WiMLWRRyeflPAm44u2E/s/TJvn+BP6M5mnHSk93cieaypj3VSMYO2ePTCRqAFXtuYi1yv8uZJNA==", "signatures": [{"sig": "MEYCIQDHQPJhjVAhtEUT4RR049pgYEVdei0JTW+UjeTMTxoUlgIhAOfr8zA6MuTpLBIBZfLtbgylqs8dzYri8mZdE6/LVaXq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9699836}, "engines": {"node": ">=12"}, "gitHead": "daf64732be2e05d1258023b5b7d5389e08e291a9", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.18.15_1689857651471_0.34979070434946924", "host": "s3://npm-registry-packages"}}, "0.18.16": {"name": "@esbuild/linux-s390x", "version": "0.18.16", "license": "MIT", "_id": "@esbuild/linux-s390x@0.18.16", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "4d44c030f78962cf410f604f92fcc1505e4afdde", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.18.16.tgz", "fileCount": 3, "integrity": "sha512-3ZC0BgyYHYKfZo3AV2/66TD/I9tlSBaW7eWTEIkrQQKfJIifKMMttXl9FrAg+UT0SGYsCRLI35Gwdmm96vlOjg==", "signatures": [{"sig": "MEQCIH4St3T95fZZpXi9TgQyMC8saZAvE9Bs52nxH0noiY+dAiA9zkL4vQZFozhwOw0wxynxamY8AIEgI3EVSEd3EMwZuQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9699836}, "engines": {"node": ">=12"}, "gitHead": "22920366954b4d18aed77dfc2b5961f339d4e318", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.18.16_1690087724994_0.3759211891581751", "host": "s3://npm-registry-packages"}}, "0.18.17": {"name": "@esbuild/linux-s390x", "version": "0.18.17", "license": "MIT", "_id": "@esbuild/linux-s390x@0.18.17", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "7459c2fecdee2d582f0697fb76a4041f4ad1dd1e", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.18.17.tgz", "fileCount": 3, "integrity": "sha512-j/34jAl3ul3PNcK3pfI0NSlBANduT2UO5kZ7FCaK33XFv3chDhICLY8wJJWIhiQ+YNdQ9dxqQctRg2bvrMlYgg==", "signatures": [{"sig": "MEQCIG1brOGSCkokS2Besxlx4aB8rGw7p9c0tt0l1DlVu0r8AiAJU2Zhb206Opuwwkmr8BkhR6wWraBwncqN+pekeTuAzA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9699836}, "engines": {"node": ">=12"}, "gitHead": "1771c7109f7f5d17d96543d6b17a6ab12d9d38d0", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.18.17_1690335692749_0.22556652386435383", "host": "s3://npm-registry-packages"}}, "0.18.18": {"name": "@esbuild/linux-s390x", "version": "0.18.18", "license": "MIT", "_id": "@esbuild/linux-s390x@0.18.18", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "bec4e9c982e778c51deaa754e1ed3f0546705647", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.18.18.tgz", "fileCount": 3, "integrity": "sha512-crT7jtOXd9iirY65B+mJQ6W0HWdNy8dtkZqKGWNcBnunpLcTCfne5y5bKic9bhyYzKpQEsO+C/VBPD8iF0RhRw==", "signatures": [{"sig": "MEUCIQDX7UHG0QJKnbx1be29or9ulmN+piUKOWngnlgrcQpPygIgNGNuA6+601Cy8UsCYgs8JJvaNGu3huhac8cmLpvmWlk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9765372}, "engines": {"node": ">=12"}, "gitHead": "e8e43ad19359f0b29d84607c89c6aa95a4d1637d", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.18.18_1691255224555_0.8972412786091679", "host": "s3://npm-registry-packages"}}, "0.18.19": {"name": "@esbuild/linux-s390x", "version": "0.18.19", "license": "MIT", "_id": "@esbuild/linux-s390x@0.18.19", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "ed5cca8dac130d2f736914f9efad5fa15c238c20", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.18.19.tgz", "fileCount": 3, "integrity": "sha512-q1V1rtHRojAzjSigZEqrcLkpfh5K09ShCoIsdTakozVBnM5rgV58PLFticqDp5UJ9uE0HScov9QNbbl8HBo6QQ==", "signatures": [{"sig": "MEYCIQDpf/Ns1dxuQHlJ9SoRggyF0XDTlNaHZU25+VP9i/69BwIhAM+FeZTb0aFSqZDyR4+wKtGBnbd2VoISShxXCx9CY3GA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9765372}, "engines": {"node": ">=12"}, "gitHead": "e08ee8990905f24b987a7ddffde89e20cbf3cf6a", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.18.19_1691376717954_0.4387756060856929", "host": "s3://npm-registry-packages"}}, "0.18.20": {"name": "@esbuild/linux-s390x", "version": "0.18.20", "license": "MIT", "_id": "@esbuild/linux-s390x@0.18.20", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "255e81fb289b101026131858ab99fba63dcf0071", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.18.20.tgz", "fileCount": 3, "integrity": "sha512-+8231GMs3mAEth6Ja1iK0a1sQ3ohfcpzpRLH8uuc5/KVDFneH6jtAJLFGafpzpMRO6DzJ6AvXKze9LfFMrIHVQ==", "signatures": [{"sig": "MEYCIQDu80Xvnrl9JXT44McF6oZoPVU9uiqUy7UbvDY1pF3RbQIhAIf8N2/pNpXjvFPjDHK59si/xxwdMMhC1ti+OB6dta8d", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9765372}, "engines": {"node": ">=12"}, "gitHead": "22f0818cf81024b63752d815c51fe737612b43ec", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.18.20_1691468148955_0.8571664452380341", "host": "s3://npm-registry-packages"}}, "0.19.0": {"name": "@esbuild/linux-s390x", "version": "0.19.0", "license": "MIT", "_id": "@esbuild/linux-s390x@0.19.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "edc26cb41d8745716bda9c26bac1f0001eaad029", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.19.0.tgz", "fileCount": 3, "integrity": "sha512-ofcucfNLkoXmcnJaw9ugdEOf40AWKGt09WBFCkpor+vFJVvmk/8OPjl/qRtks2Z7BuZbG3ztJuK1zS9z5Cgx9A==", "signatures": [{"sig": "MEQCIEqYWzYrK25HtB4RwMg2aHv0uCF/zcix5ISk+Gl3D/lZAiAeQ9ySvr7yWYF/E6u4mQntGM1nj/LGPjIcTiBnharHMQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9830907}, "engines": {"node": ">=12"}, "gitHead": "c337498cdad8cac87517ec49c923441b2dc67bf2", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.19.0_1691509990950_0.5650101199667279", "host": "s3://npm-registry-packages"}}, "0.19.1": {"name": "@esbuild/linux-s390x", "version": "0.19.1", "license": "MIT", "_id": "@esbuild/linux-s390x@0.19.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "ac6876f7b0221237792001fd644ca6523a82d5a7", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.19.1.tgz", "fileCount": 3, "integrity": "sha512-BBIE32cyqAYhMOQ42/jnecoF5P/S5lMob2vXSUiFpD3xCFbXOFkjP1OjfFKnalSO9+B5emvPTQFfNQXuLeVGEw==", "signatures": [{"sig": "MEQCIDQfKBn2jIYMO2xDel3y9u95Dx9s7vRIx100R/hbplcRAiBXKKsqZ0iU+yHsA0xMYP5VoU/NwEO7V7WXJ8NsRbmavg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9830907}, "engines": {"node": ">=12"}, "gitHead": "49801f761347d53bd1f6a88767bb79e257f9fbb9", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.19.1_1691769487888_0.5806016554360482", "host": "s3://npm-registry-packages"}}, "0.19.2": {"name": "@esbuild/linux-s390x", "version": "0.19.2", "license": "MIT", "_id": "@esbuild/linux-s390x@0.19.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "ece3ed75c5a150de8a5c110f02e97d315761626b", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.19.2.tgz", "fileCount": 3, "integrity": "sha512-U+RinR6aXXABFCcAY4gSlv4CL1oOVvSSCdseQmGO66H+XyuQGZIUdhG56SZaDJQcLmrSfRmx5XZOWyCJPRqS7g==", "signatures": [{"sig": "MEUCIFDlG0GyGEykxE72boHJvV2SwumR6TMqQu9iKAv9oLW4AiEAtPi/zu5adrupihJKWHB3kBunLiAB3gZZzjwkqpIWWs4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9830907}, "engines": {"node": ">=12"}, "gitHead": "09a100124e9daef9e0be57d21cc7729c6f3516e7", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.19.2_1691978340705_0.9876818174366053", "host": "s3://npm-registry-packages"}}, "0.19.3": {"name": "@esbuild/linux-s390x", "version": "0.19.3", "license": "MIT", "_id": "@esbuild/linux-s390x@0.19.3", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "c5fb47474b9f816d81876c119dbccadf671cc5f6", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.19.3.tgz", "fileCount": 3, "integrity": "sha512-LlmsbuBdm1/D66TJ3HW6URY8wO6IlYHf+ChOUz8SUAjVTuaisfuwCOAgcxo3Zsu3BZGxmI7yt//yGOxV+lHcEA==", "signatures": [{"sig": "MEYCIQCJ9GUFfK1+Tg+5ISKWrqVF882oEcIWCC0tnvYmJws8LAIhAO+DBx7u5gyn7NAfQZj1uBBjgwR45w5yRaLupDVYPRiw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9830907}, "engines": {"node": ">=12"}, "gitHead": "673ad10ff752486aa90749b63ebeb952c29106a1", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.19.3_1694653978568_0.6651372809308704", "host": "s3://npm-registry-packages"}}, "0.19.4": {"name": "@esbuild/linux-s390x", "version": "0.19.4", "license": "MIT", "_id": "@esbuild/linux-s390x@0.19.4", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "8d2cca20cd4e7c311fde8701d9f1042664f8b92b", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.19.4.tgz", "fileCount": 3, "integrity": "sha512-152aLpQqKZYhThiJ+uAM4PcuLCAOxDsCekIbnGzPKVBRUDlgaaAfaUl5NYkB1hgY6WN4sPkejxKlANgVcGl9Qg==", "signatures": [{"sig": "MEUCIGtNk+lyikD4Tg0UMkELLE9qOkAXv0tfB3J/5cTZP4bkAiEAo/pJ3pXHgWukUmSVafHCf44iVzoCwpFA4eJkSHSsXBg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9830907}, "engines": {"node": ">=12"}, "gitHead": "a75b16ec09e76a050cea8ad43588172dc297784d", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.19.4_1695865732144_0.6097024100132482", "host": "s3://npm-registry-packages"}}, "0.19.5": {"name": "@esbuild/linux-s390x", "version": "0.19.5", "license": "MIT", "_id": "@esbuild/linux-s390x@0.19.5", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "b38d5681db89a3723862dfa792812397b1510a7d", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.19.5.tgz", "fileCount": 3, "integrity": "sha512-Z6JrMyEw/EmZBD/OFEFpb+gao9xJ59ATsoTNlj39jVBbXqoZm4Xntu6wVmGPB/OATi1uk/DB+yeDPv2E8PqZGw==", "signatures": [{"sig": "MEUCIQC7jp2eDflbeAoosBeYMrt9BlNlUHxh65WiO0VpMujTbAIgEd7jjXGpfQlzU2NNHYxwbOzRQx+o764a+Tk8FPz6pkA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9830907}, "engines": {"node": ">=12"}, "gitHead": "a7fcc43fdb6b6edc58f781fe96328f4867f4b33e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.19.5_1697519478725_0.16792588305011735", "host": "s3://npm-registry-packages"}}, "0.19.6": {"name": "@esbuild/linux-s390x", "version": "0.19.6", "license": "MIT", "_id": "@esbuild/linux-s390x@0.19.6", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "d7a843a2620e73c5c9d65c482e2fbddc7e0f7753", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.19.6.tgz", "fileCount": 3, "integrity": "sha512-MopyYV39vnfuykHanRWHGRcRC3AwU7b0QY4TI8ISLfAGfK+tMkXyFuyT1epw/lM0pflQlS53JoD22yN83DHZgA==", "signatures": [{"sig": "MEUCICkL4KJvEmkNwnRQ0LwUB2iR7oLrYWtTYPa8jIX2/QxVAiEAzEY0rTRiSzACZ2l5AJjG9zFMgSoro7cg5AV134qi3xg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9830907}, "engines": {"node": ">=12"}, "gitHead": "6073a3a9a02909d54cedbaf9c06f5fa501f9f337", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.19.6_1700377931108_0.15148613376219355", "host": "s3://npm-registry-packages"}}, "0.19.7": {"name": "@esbuild/linux-s390x", "version": "0.19.7", "license": "MIT", "_id": "@esbuild/linux-s390x@0.19.7", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "be94974e0caa0783ae05f9477fd7170b9ac29cb0", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.19.7.tgz", "fileCount": 3, "integrity": "sha512-U8Rhki5PVU0L0nvk+E8FjkV8r4Lh4hVEb9duR6Zl21eIEYEwXz8RScj4LZWA2i3V70V4UHVgiqMpszXvG0Yqhg==", "signatures": [{"sig": "MEQCIQDPpCub6uW7+8AZL6CsVkZXU9WifnOFF3K/6YOiJpq7lAIfEzIlxjXp+QUVgBiFvzUB7WFINviL8QBGOuPJMgw7uQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9961979}, "engines": {"node": ">=12"}, "gitHead": "a7773b340bb216d053df91b7479b5aa2a152b0de", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.19.7_1700528533514_0.655340235800943", "host": "s3://npm-registry-packages"}}, "0.19.8": {"name": "@esbuild/linux-s390x", "version": "0.19.8", "license": "MIT", "_id": "@esbuild/linux-s390x@0.19.8", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "989e8a05f7792d139d5564ffa7ff898ac6f20a4a", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.19.8.tgz", "fileCount": 3, "integrity": "sha512-NPxbdmmo3Bk7mbNeHmcCd7R7fptJaczPYBaELk6NcXxy7HLNyWwCyDJ/Xx+/YcNH7Im5dHdx9gZ5xIwyliQCbg==", "signatures": [{"sig": "MEUCIArHwITVYdVG8Id+ePecOA0wnhmLmh3w1ms9NSGrL2jfAiEAgrCXTNOmNGaDUvLHu87kK7uWWsUD/cb0bL8wfjWWlnw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9961979}, "engines": {"node": ">=12"}, "gitHead": "e97bd6706c7aaddb3770ae31b164d7ccaec8056c", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.19.8_1701040128259_0.2162612660040346", "host": "s3://npm-registry-packages"}}, "0.19.9": {"name": "@esbuild/linux-s390x", "version": "0.19.9", "license": "MIT", "_id": "@esbuild/linux-s390x@0.19.9", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "2dffe497726b897c9f0109e774006e25b33b4fd0", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.19.9.tgz", "fileCount": 3, "integrity": "sha512-zHbglfEdC88KMgCWpOl/zc6dDYJvWGLiUtmPRsr1OgCViu3z5GncvNVdf+6/56O2Ca8jUU+t1BW261V6kp8qdw==", "signatures": [{"sig": "MEUCIQDFSRghG0wjokX6wkuN/cChCscRf/AExYuhwaSH1foonwIgI8oWd22v4JKQPeqALv537szCvSec+GAPsAmzK4im9ZY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10027515}, "engines": {"node": ">=12"}, "gitHead": "9edc9d44c3d0480c27f68a71365f18e688b6184a", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.19.9_1702185001285_0.9155139022046472", "host": "s3://npm-registry-packages"}}, "0.19.10": {"name": "@esbuild/linux-s390x", "version": "0.19.10", "license": "MIT", "_id": "@esbuild/linux-s390x@0.19.10", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "d30af63530f8d4fa96930374c9dd0d62bf59e069", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.19.10.tgz", "fileCount": 3, "integrity": "sha512-pZFe0OeskMHzHa9U38g+z8Yx5FNCLFtUnJtQMpwhS+r4S566aK2ci3t4NCP4tjt6d5j5uo4h7tExZMjeKoehAA==", "signatures": [{"sig": "MEQCIGMFcG9ogUIBOZqHk4VmFgwLw0YvZvOgJKQZXFK3h5DlAiAiJH3jGgQ/m2HOFmWu/reP5X7MI6QfhCEUnPcM9PTgPA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10027516}, "engines": {"node": ">=12"}, "gitHead": "55e1127a49db0c26f1abd97f1b180bbc728aa95a", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.19.10_1702945338113_0.9282868739904913", "host": "s3://npm-registry-packages"}}, "0.19.11": {"name": "@esbuild/linux-s390x", "version": "0.19.11", "license": "MIT", "_id": "@esbuild/linux-s390x@0.19.11", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "4ad8567df48f7dd4c71ec5b1753b6f37561a65a8", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.19.11.tgz", "fileCount": 3, "integrity": "sha512-A5xdUoyWJHMMlcSMcPGVLzYzpcY8QP1RtYzX5/bS4dvjBGVxdhuiYyFwp7z74ocV7WDc0n1harxmpq2ePOjI0Q==", "signatures": [{"sig": "MEUCIHLbfkxg+QHNwuLhTJX0eoB3Lvimz/2/WTplgDpdHDSrAiEA+/gJoLD8Lmg3dHFLo2zYm0a5zci/E0zCll1k31A6fbs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10027516}, "engines": {"node": ">=12"}, "gitHead": "6ee82255bdfdffef2de60827e9d35a425a7cbff6", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.19.11_1703881992813_0.3678335297810664", "host": "s3://npm-registry-packages"}}, "0.19.12": {"name": "@esbuild/linux-s390x", "version": "0.19.12", "license": "MIT", "_id": "@esbuild/linux-s390x@0.19.12", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "e86fb8ffba7c5c92ba91fc3b27ed5a70196c3cc8", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.19.12.tgz", "fileCount": 3, "integrity": "sha512-+Pil1Nv3Umes4m3AZKqA2anfhJiVmNCYkPchwFJNEJN5QxmTs1uzyy4TvmDrCRNT2ApwSari7ZIgrPeUx4UZDg==", "signatures": [{"sig": "MEQCIGkPfbcxTC1K35mbjW5Yt+fLvMnTr69Oxx8sRpC/G1dXAiB8VaedD7d7eA3+AiYIbeIzhOtLGR4x1dRo6vIEMXY/iA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10027516}, "engines": {"node": ">=12"}, "gitHead": "d7fd1ad35715cda76eb33343b7c07b275e402a2e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.19.12_1706031687499_0.19610257195112135", "host": "s3://npm-registry-packages"}}, "0.20.0": {"name": "@esbuild/linux-s390x", "version": "0.20.0", "license": "MIT", "_id": "@esbuild/linux-s390x@0.20.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "cb3d069f47dc202f785c997175f2307531371ef8", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.20.0.tgz", "fileCount": 3, "integrity": "sha512-fgf9ubb53xSnOBqyvWEY6ukBNRl1mVX1srPNu06B6mNsNK20JfH6xV6jECzrQ69/VMiTLvHMicQR/PgTOgqJUQ==", "signatures": [{"sig": "MEUCIQCvWyHQhbB9yV69cYiHP67ma2C/H88qgD8QK1InQOCEgQIgQMSbdYaKS2XLWhZ2VAgY8TRP3PlsrGgEWILrJsFmNQY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10027559}, "engines": {"node": ">=12"}, "gitHead": "2af5ccf478812d2d7226ad4435d46fbbb3419a8c", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.20.0_1706374209964_0.02939098708055199", "host": "s3://npm-registry-packages"}}, "0.20.1": {"name": "@esbuild/linux-s390x", "version": "0.20.1", "license": "MIT", "_id": "@esbuild/linux-s390x@0.20.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "b9ab8af6e4b73b26d63c1c426d7669a5d53eb5a7", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.20.1.tgz", "fileCount": 3, "integrity": "sha512-5BepC2Au80EohQ2dBpyTquqGCES7++p7G+7lXe1bAIvMdXm4YYcEfZtQrP4gaoZ96Wv1Ute61CEHFU7h4FMueQ==", "signatures": [{"sig": "MEUCIQCLPk8KmxRgUwfYW6WoF9HqIOCKt2sHF39LFDi4bT9TFQIgeWH8tSDmNwPSEWPLDnSsTh/tW1NiSAXfYV5lUueuQqk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10093095}, "engines": {"node": ">=12"}, "gitHead": "9f9e4f85e6e28a58727531458663afd157b8b415", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.20.1_1708324779816_0.3991868354410355", "host": "s3://npm-registry-packages"}}, "0.20.2": {"name": "@esbuild/linux-s390x", "version": "0.20.2", "license": "MIT", "_id": "@esbuild/linux-s390x@0.20.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "3c830c90f1a5d7dd1473d5595ea4ebb920988685", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.20.2.tgz", "fileCount": 3, "integrity": "sha512-wcWISOobRWNm3cezm5HOZcYz1sKoHLd8VL1dl309DiixxVFoFe/o8HnwuIwn6sXre88Nwj+VwZUvJf4AFxkyrQ==", "signatures": [{"sig": "MEUCIAfOOL+l2rcYNBiiotg8x/Y/UEepgtUYJ4erfXkmb682AiEAsPgMvICEg5NdGrS43l8lAjeXKn/bYeParUYkedgVUOU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10093095}, "engines": {"node": ">=12"}, "gitHead": "617eddaa32b7649ad23ddd15257816df3f0f544c", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.20.2_1710445820396_0.7671731851499681", "host": "s3://npm-registry-packages"}}, "0.21.0": {"name": "@esbuild/linux-s390x", "version": "0.21.0", "license": "MIT", "_id": "@esbuild/linux-s390x@0.21.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "fa73b2645adf971e9880c534f10616e8fc675585", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.21.0.tgz", "fileCount": 3, "integrity": "sha512-Gz/gafubuM3L1D29LnqaxcGg16aa2XES/uFTFdcvrwsRpMxkLiowaUvIiWJfatf/oCyyZu5CT8SrlMy37dGc7A==", "signatures": [{"sig": "MEUCIALUmHcNH6nekjAAyZSqs2DJ0OgrakNAD+43U/+H24QaAiEArU6BcDjjCnuP3/I4cJc/HvlRlNsoft9s/GsKq6RxWV8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10158631}, "engines": {"node": ">=12"}, "gitHead": "c6da2c3aa2b1321be3fdacd1e53566c5f24ee702", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.21.0_1715050380471_0.8029434857689024", "host": "s3://npm-registry-packages"}}, "0.21.1": {"name": "@esbuild/linux-s390x", "version": "0.21.1", "license": "MIT", "_id": "@esbuild/linux-s390x@0.21.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "e25b97005e4c82540d1bc7af88e333fb55142570", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.21.1.tgz", "fileCount": 3, "integrity": "sha512-o73TcUNMuoTZlhwFdsgr8SfQtmMV58sbgq6gQq9G1xUiYnHMTmJbwq65RzMx89l0iya69lR4bxBgtWiiOyDQZA==", "signatures": [{"sig": "MEYCIQCogKYZedtyWS+vfSi7ocPiXtvvEEkxCVFi/ejipotLMAIhAJzsHPMBwGAAFppdy38mzRveAD8jhBVrUtIQVr/i4Ja0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10158631}, "engines": {"node": ">=12"}, "gitHead": "e87639417e47ba5db160f105785dc10bde0999cf", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.21.1_1715100971877_0.1507862317463058", "host": "s3://npm-registry-packages"}}, "0.21.2": {"name": "@esbuild/linux-s390x", "version": "0.21.2", "license": "MIT", "_id": "@esbuild/linux-s390x@0.21.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "2835f5b9b4c961baf6d6f03a870ab2d5bc3fbfcc", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.21.2.tgz", "fileCount": 3, "integrity": "sha512-NP7fTpGSFWdXyvp8iAFU04uFh9ARoplFVM/m+8lTRpaYG+2ytHPZWyscSsMM6cvObSIK2KoPHXiZD4l99WaxbQ==", "signatures": [{"sig": "MEUCIQDEW4vryKWcidTIw2fFRmLdomgjIOMxib8OWm7g2NEnDAIgUvzfFYiBm4FzOU9yzvYIfglnCS0VkGrtz+WC6caFW9o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10158631}, "engines": {"node": ">=12"}, "gitHead": "b24180e4fbd07504e91cb922948870d5467072e0", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.21.2_1715546005113_0.9910947751525019", "host": "s3://npm-registry-packages"}}, "0.21.3": {"name": "@esbuild/linux-s390x", "version": "0.21.3", "license": "MIT", "_id": "@esbuild/linux-s390x@0.21.3", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "05e6f3a12a0dcd60672f25e8789a83cd3affa487", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.21.3.tgz", "fileCount": 3, "integrity": "sha512-vnD1YUkovEdnZWEuMmy2X2JmzsHQqPpZElXx6dxENcIwTu+Cu5ERax6+Ke1QsE814Zf3c6rxCfwQdCTQ7tPuXA==", "signatures": [{"sig": "MEUCIQC3QteNSQbgnMxHHVaYEvt6lRyZfMGMZpgR302xeH2YogIgRr30iahLeZr9TMB/mo6tctnylvWvvT6115KtUOnCs5Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10158631}, "engines": {"node": ">=12"}, "gitHead": "efa3dd2d8e895f7f9a9bef0d588560bbae7d776e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.21.3_1715806387803_0.1497529328302687", "host": "s3://npm-registry-packages"}}, "0.21.4": {"name": "@esbuild/linux-s390x", "version": "0.21.4", "license": "MIT", "_id": "@esbuild/linux-s390x@0.21.4", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "9cbd26854b5b12cf22fb54c96cd1adffaf6ace6f", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.21.4.tgz", "fileCount": 3, "integrity": "sha512-Iqc/l/FFwtt8FoTK9riYv9zQNms7B8u+vAI/rxKuN10HgQIXaPzKZc479lZ0x6+vKVQbu55GdpYpeNWzjOhgbA==", "signatures": [{"sig": "MEUCIFMdC0rOlEJ7qLJODzPkVUvkMK080VF/wUa/4fTdJkBFAiEA3wGf8IqKrusY6UWcLI+/Otxph1AOYmsqoQVT3nZoxhc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10158631}, "engines": {"node": ">=12"}, "gitHead": "67cbf87a4909d87a902ca8c3b69ab5330defab0a", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.21.4_1716603080688_0.0030435952565712743", "host": "s3://npm-registry-packages"}}, "0.21.5": {"name": "@esbuild/linux-s390x", "version": "0.21.5", "license": "MIT", "_id": "@esbuild/linux-s390x@0.21.5", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "b7ccf686751d6a3e44b8627ababc8be3ef62d8de", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.21.5.tgz", "fileCount": 3, "integrity": "sha512-zus5sxzqBJD3eXxwvjN1yQkRepANgxE9lgOW2qLnmr8ikMTphkjgXu1HR01K4FJg8h1kEEDAqDcZQtbrRnB41A==", "signatures": [{"sig": "MEUCIQDRViB5tVxpaaQsB24/ZmXS3v48oWWiQGVspYAz+VP/sAIgTJQqRSakH12CCCSxJ9OaS3JCBZe3o/9/3DvpwPFyER0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10158631}, "engines": {"node": ">=12"}, "gitHead": "fc37c2fa9de2ad77476a6d4a8f1516196b90187e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.21.5_1717967851632_0.1414382720026377", "host": "s3://npm-registry-packages"}}, "0.22.0": {"name": "@esbuild/linux-s390x", "version": "0.22.0", "license": "MIT", "_id": "@esbuild/linux-s390x@0.22.0", "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "a7ab13ae163307ac615dac5ce7f60a6b0a067d59", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.22.0.tgz", "fileCount": 3, "integrity": "sha512-8j4a2ChT9+V34NNNY9c/gMldutaJFmfMacTPq4KfNKwv2fitBCLYjee7c+Vxaha2nUhPK7cXcZpJtJ3+Y7ZdVQ==", "signatures": [{"sig": "MEUCIQDpZi+0DiheTg03VuD+XS318IJcfVRUXrCN5OoLMDolgQIgX9bLKbOdb65CE7Ls0ztW8AD8afBC+VedsJ3pTOA8KTI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10420927}, "engines": {"node": ">=18"}, "gitHead": "80c6e6ea094a71691ab1644ab61494cc67729365", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.22.0_1719779907698_0.7398833948257686", "host": "s3://npm-registry-packages"}}, "0.23.0": {"name": "@esbuild/linux-s390x", "version": "0.23.0", "license": "MIT", "_id": "@esbuild/linux-s390x@0.23.0", "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "45390f12e802201f38a0229e216a6aed4351dfe8", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.23.0.tgz", "fileCount": 3, "integrity": "sha512-WDi3+NVAuyjg/Wxi+o5KPqRbZY0QhI9TjrEEm+8dmpY9Xir8+HE/HNx2JoLckhKbFopW0RdO2D72w8trZOV+Wg==", "signatures": [{"sig": "MEUCIQD/yAdwHckPpSZTqREM46kd2AjC1UxBCLhNungS28V2awIgGKol/nerBr8TkA1CNw2khImKhIPihQYQtOl0MO3j1CA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10420927}, "engines": {"node": ">=18"}, "gitHead": "9d506806bdd963b02b3d6edf45e717e03dcba785", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.23.0_1719891259945_0.7884790089957332", "host": "s3://npm-registry-packages"}}, "0.23.1": {"name": "@esbuild/linux-s390x", "version": "0.23.1", "license": "MIT", "_id": "@esbuild/linux-s390x@0.23.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "e71ea18c70c3f604e241d16e4e5ab193a9785d6f", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.23.1.tgz", "fileCount": 3, "integrity": "sha512-9ygs73tuFCe6f6m/Tb+9LtYxWR4c9yg7zjt2cYkjDbDpV/xVn+68cQxMXCjUpYwEkze2RcU/rMnfIXNRFmSoDw==", "signatures": [{"sig": "MEUCIQDmHIpvVO+od7pOpy/1EDZR000rxfJUs1gH5HSAroUJ4AIgJjRF39eZc0avNw60wfOvlVBnS6c385aH+c7u9A/1qzI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10420927}, "engines": {"node": ">=18"}, "gitHead": "332727499e62315cff4ecaff9fa8b86336555e46", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.23.1_1723846435254_0.1399147649681165", "host": "s3://npm-registry-packages"}}, "0.24.0": {"name": "@esbuild/linux-s390x", "version": "0.24.0", "license": "MIT", "_id": "@esbuild/linux-s390x@0.24.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "29ebf87e4132ea659c1489fce63cd8509d1c7319", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.24.0.tgz", "fileCount": 3, "integrity": "sha512-ZcQ6+qRkw1UcZGPyrCiHHkmBaj9SiCD8Oqd556HldP+QlpUIe2Wgn3ehQGVoPOvZvtHm8HPx+bH20c9pvbkX3g==", "signatures": [{"sig": "MEQCIBu8dsYgIuKFreXVgz4YPHibr+R9xpcaGhSNbxMiJniTAiA7pN7QGA0Sw3tS6u5MqQZcDQ75Wt/ewMaxOOwAKn500Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10683071}, "engines": {"node": ">=18"}, "gitHead": "d34e79e2a998c21bb71d57b92b0017ca11756912", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.24.0_1726970820389_0.6763874903526335", "host": "s3://npm-registry-packages"}}, "0.24.1": {"name": "@esbuild/linux-s390x", "version": "0.24.1", "license": "MIT", "_id": "@esbuild/linux-s390x@0.24.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "b210628db31d5712e2ee4536d4f133d0f650253a", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.24.1.tgz", "fileCount": 3, "integrity": "sha512-47oImRwZavr5qEvEHNPcdly8LuFp3i4xrqT9mNbUn4ZKbwyegVp10k1E1YARiOim8ggfPAPABhPqXdS1NJOAnw==", "signatures": [{"sig": "MEQCIHSXPcWSuUR4acKhVG6ay/ipAeE1nXfogHqrviDzpZ+lAiAc9DB8DgP3v/WZgkWpUoKKFNBZ9GIMf8s5mfVSws18KQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10748607}, "engines": {"node": ">=18"}, "gitHead": "de9598f42dc3ffc395e3fd3672a4804f6b4e5c09", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.24.1_1734673333694_0.9966206432376423", "host": "s3://npm-registry-packages-npm-production"}}, "0.24.2": {"name": "@esbuild/linux-s390x", "version": "0.24.2", "license": "MIT", "_id": "@esbuild/linux-s390x@0.24.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "ab18e56e66f7a3c49cb97d337cd0a6fea28a8577", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.24.2.tgz", "fileCount": 3, "integrity": "sha512-S0Bh0A53b0YHL2XEXC20bHLuGMOhFDO6GN4b3YjRLK//Ep3ql3erpNcPlEFed93hsQAjAQDNsvcK+hV90FubSw==", "signatures": [{"sig": "MEYCIQCMHK7zpymJbSMsT2H9cQKiOPBQXqzbyZRreyDddr+udQIhAPKpveuCq88P93XywX7JkmIzuGt+Tmc/RSS00gZv8Kh+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10748607}, "engines": {"node": ">=18"}, "gitHead": "745abd9f0c06f73ca40fbe198546a9bc36c23b81", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.24.2_1734717452405_0.6229764723378819", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.0": {"name": "@esbuild/linux-s390x", "version": "0.25.0", "license": "MIT", "_id": "@esbuild/linux-s390x@0.25.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "8323afc0d6cb1b6dc6e9fd21efd9e1542c3640a4", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.25.0.tgz", "fileCount": 3, "integrity": "sha512-XM2BFsEBz0Fw37V0zU4CXfcfuACMrppsMFKdYY2WuTS3yi8O1nFOhil/xhKTmE1nPmVyvQJjJivgDT+xh8pXJA==", "signatures": [{"sig": "MEUCIQDmQ1lWsFWmk5SkHimTnJXsL3LC6zcHFIvadsqEbCi74QIgZ3ycHNAGf+dh2L2Q2RoW4H3cVRdmZ3x8yqjnko4CZ8Q=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10748607}, "engines": {"node": ">=18"}, "gitHead": "e9174d671b1882758cd32ac5e146200f5bee3e45", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.25.0_1738983780811_0.6393802956323678", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.1": {"name": "@esbuild/linux-s390x", "version": "0.25.1", "license": "MIT", "_id": "@esbuild/linux-s390x@0.25.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "e677b4b9d1b384098752266ccaa0d52a420dc1aa", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.25.1.tgz", "fileCount": 3, "integrity": "sha512-cEECeLlJNfT8kZHqLarDBQso9a27o2Zd2AQ8USAEoGtejOrCYHNtKP8XQhMDJMtthdF4GBmjR2au3x1udADQQQ==", "signatures": [{"sig": "MEQCIBfF5s25fgho84qdc/LkUuHYrq1ucxP+La72I90PW+8WAiBVu2QO1iOcNG7k+H2nvsxD5NJkN99rlD8ZWpizst6jBw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10748607}, "engines": {"node": ">=18"}, "gitHead": "6bfc1c13b4d986b86e8bc2035f00c337b0c1d007", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.14.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.25.1_1741578374336_0.16811784943479124", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.2": {"name": "@esbuild/linux-s390x", "version": "0.25.2", "license": "MIT", "_id": "@esbuild/linux-s390x@0.25.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "419e25737ec815c6dce2cd20d026e347cbb7a602", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.25.2.tgz", "fileCount": 3, "integrity": "sha512-GRa4IshOdvKY7M/rDpRR3gkiTNp34M0eLTaC1a08gNrh4u488aPhuZOCpkF6+2wl3zAN7L7XIpOFBhnaE3/Q8Q==", "signatures": [{"sig": "MEUCICrVJN/KwfEpWTjaWEjQAtPmZPPhmkD6BmT4ItsaMuzyAiEA2Ps8vlGk2GZ8UJGULe937X0Z7+sbCvhekv5UdHNIweY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10748607}, "engines": {"node": ">=18"}, "gitHead": "4475787eef4c4923b92b9fa37ebba1c88b9e1d9b", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.14.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.25.2_1743356022069_0.7008318890148588", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.3": {"name": "@esbuild/linux-s390x", "version": "0.25.3", "license": "MIT", "_id": "@esbuild/linux-s390x@0.25.3", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "f6a7cb67969222b200974de58f105dfe8e99448d", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.25.3.tgz", "fileCount": 3, "integrity": "sha512-s+w/NOY2k0yC2p9SLen+ymflgcpRkvwwa02fqmAwhBRI3SC12uiS10edHHXlVWwfAagYSY5UpmT/zISXPMW3tQ==", "signatures": [{"sig": "MEQCIEOHVrNN8Tur2KbZ2m5bet4jLBu3kQKfcn5WzbrtxvJ0AiBjCdcK4NUJNNM/KKcKyu8mDE0sCUfdK4U6kllC+NknzA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10748607}, "engines": {"node": ">=18"}, "gitHead": "677910b073194b64d5ae01aefd7a7465bbf5b27b", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.14.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.25.3_1745380624821_0.8618525665147787", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.4": {"name": "@esbuild/linux-s390x", "version": "0.25.4", "license": "MIT", "_id": "@esbuild/linux-s390x@0.25.4", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "1466876e0aa3560c7673e63fdebc8278707bc750", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.25.4.tgz", "fileCount": 3, "integrity": "sha512-jFnu+6UbLlzIjPQpWCNh5QtrcNfMLjgIavnwPQAfoGx4q17ocOU9MsQ2QVvFxwQoWpZT8DvTLooTvmOQXkO51g==", "signatures": [{"sig": "MEYCIQDYVUh86tKdG/wlfVDdqLPFEVF6n6sZbMHQv4o/6ss/vgIhAKvPBycd31RadtR8QdbUFuRD00fiPN2UfDpwINHiIt+5", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10748607}, "engines": {"node": ">=18"}, "gitHead": "218d29e9da018d60cf87b8fb496bb8167936ff54", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.14.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-s390x_0.25.4_1746491489140_0.41896051611155927", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.5": {"name": "@esbuild/linux-s390x", "version": "0.25.5", "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["linux"], "cpu": ["s390x"], "_id": "@esbuild/linux-s390x@0.25.5", "gitHead": "ea453bf687c8e5cf3c5f11aae372c5ca33be0c98", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-K2dSKTKfmdh78uJ3NcWFiqyRrimfdinS5ErLSn3vluHNeHVnBAFWC8a4X5N+7FgVE1EjXS1QDZbpqZBjfrqMTQ==", "shasum": "5669af81327a398a336d7e40e320b5bbd6e6e72d", "tarball": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.25.5.tgz", "fileCount": 3, "unpackedSize": 10748607, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCICMsNDjdsknshQ0eb2ViUwc8m6hGbeYnVqNhITLTRDOuAiEAwXKODeLoTlYgF64FMjtuz5TjUT0cF9FcuMQJ3y9ZDP0="}]}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/linux-s390x_0.25.5_1748315620900_0.24042108084358982"}, "_hasShrinkwrap": false}}, "time": {"created": "2022-12-05T23:54:05.569Z", "modified": "2025-05-27T03:13:41.331Z", "0.15.18": "2022-12-05T23:54:05.952Z", "0.16.0": "2022-12-07T03:55:40.943Z", "0.16.1": "2022-12-07T04:49:10.070Z", "0.16.2": "2022-12-08T07:00:31.720Z", "0.16.3": "2022-12-08T20:13:53.860Z", "0.16.4": "2022-12-10T03:51:16.373Z", "0.16.5": "2022-12-13T17:48:20.305Z", "0.16.6": "2022-12-14T05:23:53.594Z", "0.16.7": "2022-12-14T22:47:32.237Z", "0.16.8": "2022-12-16T23:39:44.236Z", "0.16.9": "2022-12-18T04:32:07.932Z", "0.16.10": "2022-12-19T23:27:18.146Z", "0.16.11": "2022-12-27T01:39:40.736Z", "0.16.12": "2022-12-28T02:09:13.378Z", "0.16.13": "2023-01-02T22:58:01.149Z", "0.16.14": "2023-01-04T20:13:51.294Z", "0.16.15": "2023-01-07T04:19:52.098Z", "0.16.16": "2023-01-08T22:44:28.202Z", "0.16.17": "2023-01-11T21:58:40.122Z", "0.17.0": "2023-01-14T04:37:43.330Z", "0.17.1": "2023-01-16T18:06:33.081Z", "0.17.2": "2023-01-17T06:40:25.961Z", "0.17.3": "2023-01-18T19:15:13.342Z", "0.17.4": "2023-01-22T06:14:09.694Z", "0.17.5": "2023-01-27T16:38:31.364Z", "0.17.6": "2023-02-06T17:01:19.896Z", "0.17.7": "2023-02-09T22:27:24.773Z", "0.17.8": "2023-02-13T06:36:17.159Z", "0.17.9": "2023-02-19T17:45:56.393Z", "0.17.10": "2023-02-20T17:55:31.253Z", "0.17.11": "2023-03-03T22:40:58.578Z", "0.17.12": "2023-03-17T06:17:49.084Z", "0.17.13": "2023-03-24T18:57:45.134Z", "0.17.14": "2023-03-26T02:48:21.749Z", "0.17.15": "2023-04-01T22:27:42.289Z", "0.17.16": "2023-04-10T04:35:50.483Z", "0.17.17": "2023-04-16T21:24:14.191Z", "0.17.18": "2023-04-22T20:42:10.444Z", "0.17.19": "2023-05-13T00:07:11.412Z", "0.18.0": "2023-06-09T21:24:56.404Z", "0.18.1": "2023-06-12T04:52:18.753Z", "0.18.2": "2023-06-13T02:41:33.839Z", "0.18.3": "2023-06-15T12:22:14.173Z", "0.18.4": "2023-06-16T15:39:14.160Z", "0.18.5": "2023-06-20T00:53:27.534Z", "0.18.6": "2023-06-20T23:25:31.774Z", "0.18.7": "2023-06-24T02:47:03.866Z", "0.18.8": "2023-06-25T03:19:50.757Z", "0.18.9": "2023-06-26T05:29:03.023Z", "0.18.10": "2023-06-26T21:21:00.375Z", "0.18.11": "2023-07-01T06:04:32.686Z", "0.18.12": "2023-07-13T01:34:47.872Z", "0.18.13": "2023-07-15T02:37:53.379Z", "0.18.14": "2023-07-18T05:00:57.679Z", "0.18.15": "2023-07-20T12:54:11.715Z", "0.18.16": "2023-07-23T04:48:45.236Z", "0.18.17": "2023-07-26T01:41:33.003Z", "0.18.18": "2023-08-05T17:07:04.879Z", "0.18.19": "2023-08-07T02:51:58.214Z", "0.18.20": "2023-08-08T04:15:49.224Z", "0.19.0": "2023-08-08T15:53:11.243Z", "0.19.1": "2023-08-11T15:58:08.098Z", "0.19.2": "2023-08-14T01:59:00.950Z", "0.19.3": "2023-09-14T01:12:58.878Z", "0.19.4": "2023-09-28T01:48:52.440Z", "0.19.5": "2023-10-17T05:11:19.031Z", "0.19.6": "2023-11-19T07:12:11.370Z", "0.19.7": "2023-11-21T01:02:13.756Z", "0.19.8": "2023-11-26T23:08:48.558Z", "0.19.9": "2023-12-10T05:10:01.525Z", "0.19.10": "2023-12-19T00:22:18.516Z", "0.19.11": "2023-12-29T20:33:13.077Z", "0.19.12": "2024-01-23T17:41:27.769Z", "0.20.0": "2024-01-27T16:50:10.210Z", "0.20.1": "2024-02-19T06:39:40.034Z", "0.20.2": "2024-03-14T19:50:20.653Z", "0.21.0": "2024-05-07T02:53:00.696Z", "0.21.1": "2024-05-07T16:56:12.108Z", "0.21.2": "2024-05-12T20:33:25.395Z", "0.21.3": "2024-05-15T20:53:08.046Z", "0.21.4": "2024-05-25T02:11:20.961Z", "0.21.5": "2024-06-09T21:17:31.872Z", "0.22.0": "2024-06-30T20:38:27.977Z", "0.23.0": "2024-07-02T03:34:20.343Z", "0.23.1": "2024-08-16T22:13:55.505Z", "0.24.0": "2024-09-22T02:07:00.696Z", "0.24.1": "2024-12-20T05:42:14.045Z", "0.24.2": "2024-12-20T17:57:32.774Z", "0.25.0": "2025-02-08T03:03:01.115Z", "0.25.1": "2025-03-10T03:46:14.539Z", "0.25.2": "2025-03-30T17:33:42.316Z", "0.25.3": "2025-04-23T03:57:05.081Z", "0.25.4": "2025-05-06T00:31:29.526Z", "0.25.5": "2025-05-27T03:13:41.143Z"}, "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "license": "MIT", "homepage": "https://github.com/evanw/esbuild#readme", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "description": "The Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler.", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "readme": "# esbuild\n\nThis is the Linux IBM Z 64-bit Big Endian binary for esbuild, a JavaScript bundler and minifier. See https://github.com/evanw/esbuild for details.\n", "readmeFilename": "README.md"}