{"_id": "bcrypt-pbkdf", "_rev": "37-c5a0dd30e64ec3abfb00f3f299799a4d", "name": "bcrypt-pbkdf", "dist-tags": {"latest": "1.0.2"}, "versions": {"1.0.0": {"name": "bcrypt-pbkdf", "version": "1.0.0", "license": "BSD-4-<PERSON><PERSON>", "_id": "bcrypt-pbkdf@1.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "3ca76b85241c7170bf7d9703e7b9aa74630040d4", "tarball": "https://registry.npmjs.org/bcrypt-pbkdf/-/bcrypt-pbkdf-1.0.0.tgz", "integrity": "sha512-1d7nPZyCV4TvkiRd3dZEqDrcj88tAe3ZxwkKeAJJ9eEoDIHyVPxGbmV1HMFVGa2XH4JHRrwUja36rLzR5BA60g==", "signatures": [{"sig": "MEUCIBsBuTlX3JsQac5rE1mTTrcXA9SjHj3veRkF5h3gQJQDAiEAxtyynYs2Ig6eMQybwfYM8xiaT/T6Qrua7OJBdYaAv/o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "3ca76b85241c7170bf7d9703e7b9aa74630040d4", "gitHead": "e88be37d3cd25395b4aa496ac468b33671368be6", "scripts": {}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "3.10.3", "description": "Port of the OpenBSD bcrypt_pbkdf function to pure JS", "directories": {}, "_nodeVersion": "0.12.15", "dependencies": {"tweetnacl": "^0.14.3"}, "devDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/bcrypt-pbkdf-1.0.0.tgz_1471381825814_0.06877309852279723", "host": "packages-16-east.internal.npmjs.com"}}, "1.0.1": {"name": "bcrypt-pbkdf", "version": "1.0.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "bcrypt-pbkdf@1.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dap", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "trentm", "email": "<EMAIL>"}], "dist": {"shasum": "63bc5dcb61331b92bc05fd528953c33462a06f8d", "tarball": "https://registry.npmjs.org/bcrypt-pbkdf/-/bcrypt-pbkdf-1.0.1.tgz", "integrity": "sha512-vY4sOrSlpwNZXsinfJ0HpbSkFft4nhSVLeUrQ4j2ydGmBOiVY83aMJStJATBy0C3+XdaYa990kIA1qkC2mUq6g==", "signatures": [{"sig": "MEQCIBZuaBsom/6JLjGKhclNtePzqZsHzR0C80PlV9mSWGjcAiA3p8h8FYgvOslN30CTH1iAqVrEmDMcDtPORpnPxiAbNA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "63bc5dcb61331b92bc05fd528953c33462a06f8d", "gitHead": "fa2ab3ae9efa15367264151398635a915c7b411d", "scripts": {}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "2.14.9", "description": "Port of the OpenBSD bcrypt_pbkdf function to pure JS", "directories": {}, "_nodeVersion": "0.12.9", "dependencies": {"tweetnacl": "^0.14.3"}, "devDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/bcrypt-pbkdf-1.0.1.tgz_1486007687899_0.974529881728813", "host": "packages-18-east.internal.npmjs.com"}}, "1.0.2": {"name": "bcrypt-pbkdf", "version": "1.0.2", "license": "BSD-3-<PERSON><PERSON>", "_id": "bcrypt-pbkdf@1.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dap", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "trentm", "email": "<EMAIL>"}], "homepage": "https://github.com/joyent/node-bcrypt-pbkdf#readme", "bugs": {"url": "https://github.com/joyent/node-bcrypt-pbkdf/issues"}, "dist": {"shasum": "a4301d389b6a43f9b67ff3ca11a3f6637e360e9e", "tarball": "https://registry.npmjs.org/bcrypt-pbkdf/-/bcrypt-pbkdf-1.0.2.tgz", "fileCount": 5, "integrity": "sha512-qeFIXtP4MSoi6NLqO12WfqARWWuCKi2Rn/9hJLEmtB5yTNr9DqFWkJRCf2qShWzPeAMRnOgCrq0sg/KLv5ES9w==", "signatures": [{"sig": "MEYCIQChYywQjt5fZLC9Rra6lu+zL8cvuTL5dMd2eOEhxappzwIhALDhzEgD/8ch3dt4FJFJIkUGloGoubSOB8uag2TfDxIC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28994}, "main": "index.js", "_from": ".", "_shasum": "a4301d389b6a43f9b67ff3ca11a3f6637e360e9e", "gitHead": "15fa7399a1090ba70d855764f7ace23003bf45f3", "scripts": {}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/joyent/node-bcrypt-pbkdf.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "Port of the OpenBSD bcrypt_pbkdf function to pure JS", "directories": {}, "_nodeVersion": "0.12.18", "dependencies": {"tweetnacl": "^0.14.3"}, "_hasShrinkwrap": false, "devDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/bcrypt-pbkdf_1.0.2_1530232438519_0.6464853720318084", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2016-08-16T21:10:29.470Z", "modified": "2025-02-07T15:27:49.396Z", "1.0.0": "2016-08-16T21:10:29.470Z", "1.0.1": "2017-02-02T03:54:49.593Z", "1.0.2": "2018-06-29T00:33:59.042Z"}, "bugs": {"url": "https://github.com/joyent/node-bcrypt-pbkdf/issues"}, "license": "BSD-3-<PERSON><PERSON>", "homepage": "https://github.com/joyent/node-bcrypt-pbkdf#readme", "repository": {"url": "git://github.com/joyent/node-bcrypt-pbkdf.git", "type": "git"}, "description": "Port of the OpenBSD bcrypt_pbkdf function to pure JS", "maintainers": [{"email": "<EMAIL>", "name": "todd.whiteman"}, {"email": "<EMAIL>", "name": "kusor"}, {"email": "<EMAIL>", "name": "micha<PERSON>.hicks"}, {"email": "<EMAIL>", "name": "bah<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "kebes<PERSON>"}, {"email": "<EMAIL>", "name": "trentm"}, {"email": "<EMAIL>", "name": "dap"}, {"email": "<EMAIL>", "name": "j<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON>"}], "readme": "Port of the OpenBSD `bcrypt_pbkdf` function to pure Javascript. `npm`-ified\nversion of [<PERSON>'s port](https://github.com/devi/tmp/blob/master/js/bcrypt_pbkdf.js),\nwith some minor performance improvements. The code is copied verbatim (and\nun-styled) from <PERSON>'s work.\n\nThis product includes software developed by Niels Provos.\n\n## API\n\n### `bcrypt_pbkdf.pbkdf(pass, passlen, salt, saltlen, key, keylen, rounds)`\n\nDerive a cryptographic key of arbitrary length from a given password and salt,\nusing the OpenBSD `bcrypt_pbkdf` function. This is a combination of Blowfish and\nSHA-512.\n\nSee [this article](http://www.tedunangst.com/flak/post/bcrypt-pbkdf) for\nfurther information.\n\nParameters:\n\n * `pass`, a Uint8Array of length `passlen`\n * `passlen`, an integer Number\n * `salt`, a Uint8Array of length `saltlen`\n * `saltlen`, an integer Number\n * `key`, a Uint8Array of length `keylen`, will be filled with output\n * `keylen`, an integer Number\n * `rounds`, an integer Number, number of rounds of the PBKDF to run\n\n### `bcrypt_pbkdf.hash(sha2pass, sha2salt, out)`\n\nCalculate a Blowfish hash, given SHA2-512 output of a password and salt. Used as\npart of the inner round function in the PBKDF.\n\nParameters:\n\n * `sha2pass`, a Uint8Array of length 64\n * `sha2salt`, a Uint8Array of length 64\n * `out`, a Uint8Array of length 32, will be filled with output\n\n## License\n\nThis source form is a 1:1 port from the OpenBSD `blowfish.c` and `bcrypt_pbkdf.c`.\nAs a result, it retains the original copyright and license. The two files are\nunder slightly different (but compatible) licenses, and are here combined in\none file. For each of the full license texts see `LICENSE`.\n", "readmeFilename": "README.md"}