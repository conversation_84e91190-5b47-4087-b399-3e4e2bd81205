{"name": "tough-cookie", "dist-tags": {"next": "6.0.0-rc.0", "latest": "5.1.2"}, "versions": {"0.9.0": {"name": "tough-cookie", "version": "0.9.0", "dependencies": {"punycode": "https://github.com/goinstant/Node-PunyCode/tarball/master"}, "devDependencies": {"vows": ">=0.5.11", "async": ">=0.1.12"}, "dist": {"shasum": "b09b191982dcd7b0cccd78e176c2d6842810c9c5", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-0.9.0.tgz", "integrity": "sha512-7GOflgzmE6usQhUSZwstSlYRSlCkIa+HSWkKeshLjervEXl01deSAaZ5MIHjrWciUf5j41BVZXcJkygHXBaTow==", "signatures": [{"sig": "MEYCIQD8LhUoX7gC1v8SSLXxZoxArr6LAYPDxmL1n8p86ieLfgIhAPYkpxull+QuYrspaWNA63/mn/X943KWcbVYa1QIuaUb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.4.12"}, "deprecated": "ReDoS vulnerability parsing Set-<PERSON><PERSON> https://nodesecurity.io/advisories/130"}, "0.9.1": {"name": "tough-cookie", "version": "0.9.1", "dependencies": {"punycode": "https://github.com/goinstant/Node-PunyCode/tarball/master"}, "devDependencies": {"vows": ">=0.5.11", "async": ">=0.1.12"}, "dist": {"shasum": "0f882fcc572567283eef639b0ba1c1a4785c72fe", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-0.9.1.tgz", "integrity": "sha512-Mu0u1TIY42OtXbQfN7QTPgLdvZNqh97nJsoc7Jsg7SG/3gYNwaAxHt9WxeHhSr0ITMZQJ/DNLBEwqmt5wtwo/A==", "signatures": [{"sig": "MEUCIQC5/HtKEHjVD7qLM8LE5UwACvjjO+KUm9gnqvE0A16IswIgF/Zw/AmHS5IFgZenNM3VYANeazX5JmboJ0nLdPuvodY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.4.12"}, "deprecated": "ReDoS vulnerability parsing Set-<PERSON><PERSON> https://nodesecurity.io/advisories/130"}, "0.9.3": {"name": "tough-cookie", "version": "0.9.3", "dependencies": {"punycode": "https://github.com/goinstant/Node-PunyCode/tarball/master"}, "devDependencies": {"vows": ">=0.5.11", "async": ">=0.1.12"}, "dist": {"shasum": "57017d596dbf03e4c7c79cf2f8c01fc69e9b70b1", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-0.9.3.tgz", "integrity": "sha512-o20HYK9A6pBCEZZNc35MIMAebLtrEOuTEHQso7L2H8vdtHCtqcQ7Kk1X7tWp+GxqLCINQhzi4aJaWoQKWsdfmA==", "signatures": [{"sig": "MEQCID8vHH5ua/vDcn0i6C2gJ0vlxqIHq5SSV5fAw+cj5+HgAiAkxYV3mhc01jeKkMspm6voEeopRNhbX6eSlKMQXfcd6Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.4.12"}, "deprecated": "ReDoS vulnerability parsing Set-<PERSON><PERSON> https://nodesecurity.io/advisories/130"}, "0.9.4": {"name": "tough-cookie", "version": "0.9.4", "dependencies": {"punycode": "https://github.com/goinstant/Node-PunyCode/tarball/master"}, "devDependencies": {"vows": ">=0.5.11", "async": ">=0.1.12"}, "dist": {"shasum": "e8a768ccb452dd3a9b8b844fe3a2ab2d319f0315", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-0.9.4.tgz", "integrity": "sha512-bqNqgDiSD7+9wjRWZLSNg9UzGY/vjA53KbxM9Q09ENmBUhsRQ6Fz8KHiaPrQnMGAZdkeAHaEq5/z4U0oc1uNJA==", "signatures": [{"sig": "MEYCIQCWlZcvzDCRbJHjLMhpsa5cK+jhcNYFpTN4roeVMJzE2wIhAMbfoQFTwdAmoGc65nqF2iTm9ex9YZSesW+fV4mwxPaQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.4.12"}, "deprecated": "ReDoS vulnerability parsing Set-<PERSON><PERSON> https://nodesecurity.io/advisories/130"}, "0.9.5": {"name": "tough-cookie", "version": "0.9.5", "dependencies": {"punycode": ">=0.2.0"}, "devDependencies": {"vows": ">=0.5.11", "async": ">=0.1.12"}, "dist": {"shasum": "a189208232ec5ee8b94f2578769b21155ab9b131", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-0.9.5.tgz", "integrity": "sha512-5LZ/FHsKPY7+3A0HPe+pqTR9Wpaue5SpQzpN7WLuhk0AeYMvtv/rqD0YA08B9X2ZfKrMT6brRTpMcwdOIMICEw==", "signatures": [{"sig": "MEUCICRL3ZsDhsgSXm5PI+JOfAcNvSpRualLOthLZUa0KXEdAiEAxmY4bRWJR+XP2rQRsFjTh+jdLlfQ2nh3KFY5V3ZHSLk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.4.12"}, "deprecated": "ReDoS vulnerability parsing Set-<PERSON><PERSON> https://nodesecurity.io/advisories/130"}, "0.9.6": {"name": "tough-cookie", "version": "0.9.6", "dependencies": {"punycode": ">=0.2.0"}, "devDependencies": {"vows": ">=0.5.11", "async": ">=0.1.12"}, "dist": {"shasum": "c63b55e3862676e5d394a9f102f7dd599539af15", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-0.9.6.tgz", "integrity": "sha512-2GLlGQmMUtMGR3PMiDu9wxPfvGav4Cdtfvj/8vvVJjq2pwqObobRJ8cXF6mUDu0XM4fX4+BhyVnRecWI582Vqg==", "signatures": [{"sig": "MEQCIB2EBM8lcoHXX5iFMOf7BtXcrGmkKPIzQAFMzk9+9so5AiA8lblH7uXY85fxxV8CKv/pc9rKDB+P29HWr2wuAtBZRg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.4.12"}, "deprecated": "ReDoS vulnerability parsing Set-<PERSON><PERSON> https://nodesecurity.io/advisories/130"}, "0.9.7": {"name": "tough-cookie", "version": "0.9.7", "dependencies": {"punycode": ">=0.2.0"}, "devDependencies": {"vows": ">=0.6.0", "async": ">=0.1.12"}, "dist": {"shasum": "ea037e175d326574b0afb196d658672c7912bd45", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-0.9.7.tgz", "integrity": "sha512-bSc8TKXfQSZbEwWvtrBP6BFt6xDkIsOuAIC5r9539+IG3QBwT3FlVl75BvbARvcexeChDKWxY8gr/rq9NgnsoA==", "signatures": [{"sig": "MEQCIHDA9gGNYY54rVmIc46NT13zYaNHdqNJjhYVHDwV9VgzAiA8jSrORCopYfKsW5g3R4u4Wrbft8UiwhP7OVIVmM8VsA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.4.12"}, "deprecated": "ReDoS vulnerability parsing Set-<PERSON><PERSON> https://nodesecurity.io/advisories/130"}, "0.9.8": {"name": "tough-cookie", "version": "0.9.8", "dependencies": {"punycode": ">=0.2.0"}, "devDependencies": {"vows": ">=0.6.0", "async": ">=0.1.12"}, "dist": {"shasum": "435991f917d7aa2d1e9e1ed93484f19a430bfd15", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-0.9.8.tgz", "integrity": "sha512-ldoi2HSCy5qy7nwDWIX3Bzj04RNcckPELdsneuq9w5aKUiGzs2+ymWMPDydt79+UuBhslu/DV9JQVpUGJP9SxQ==", "signatures": [{"sig": "MEUCIQCHK/E+DG6AL0FdKWG8he/NeROePI3GkpXf4+HrKvQcKAIgIjq2+DIPEixz0Vevte/wTAGuRHBTDksjlb/E8Pld9h8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.4.12"}, "deprecated": "ReDoS vulnerability parsing Set-<PERSON><PERSON> https://nodesecurity.io/advisories/130"}, "0.9.9": {"name": "tough-cookie", "version": "0.9.9", "dependencies": {"punycode": ">=0.2.0"}, "devDependencies": {"vows": ">=0.6.0", "async": ">=0.1.12"}, "dist": {"shasum": "e62bc00ee4bef4dfba957dd7177218d0327ef595", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-0.9.9.tgz", "integrity": "sha512-+Po0qsWdrntbmk8z0SeVtMG2hooLThSKd1jLMMnFMgO8Fh998odHrv6zUI4XTalFAMV4ow5p8Xho3IOcUrtNQw==", "signatures": [{"sig": "MEUCIQD6fbY6E41WOAqoEkgxLs3VjGuP7URIreVCOfik9GoOsAIgebr1ngA519B6pozS/YThNnpljoQR4l1MmYAv5ZcqWrg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.4.12"}, "deprecated": "ReDoS vulnerability parsing Set-<PERSON><PERSON> https://nodesecurity.io/advisories/130"}, "0.9.11": {"name": "tough-cookie", "version": "0.9.11", "dependencies": {"punycode": ">=0.2.0"}, "devDependencies": {"vows": ">=0.6.0", "async": ">=0.1.12"}, "dist": {"shasum": "6f45ee9f494967cbfcdeb97efa5174e1be9b87bb", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-0.9.11.tgz", "integrity": "sha512-2yQ9Jx7m6E5s2iW9w86EIZzwhQrPJvxhZMId5v6qdd08sKvFS9/7agGRbrLAgEUQiTU4YOsKoZRkHcr3tLUKgg==", "signatures": [{"sig": "MEUCIQCbZ3qnNeW8iQLnt4WAg0c/KGVWN+DBhtiGks67UM4y0QIgPdt2/2pvR9vZcV2PfWx1pAyegDKowzgG3mq/DQOonOI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.4.12"}, "deprecated": "ReDoS vulnerability parsing Set-<PERSON><PERSON> https://nodesecurity.io/advisories/130"}, "0.9.12": {"name": "tough-cookie", "version": "0.9.12", "dependencies": {"punycode": ">=0.2.0"}, "devDependencies": {"vows": ">=0.6.0", "async": ">=0.1.12"}, "dist": {"shasum": "9dec1931bb8bb6c46252ed2c9ed8ca00abe11546", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-0.9.12.tgz", "integrity": "sha512-NRJ2CyeF4l9v2ZgEnREkOFMck0MYpppALfT3iEqIUVXHb6x02ROzHFaBK+zVenp9VmqDgfYtcbhL2oP5mWMvkw==", "signatures": [{"sig": "MEQCIHAQttJAwgm/RlloRmYFZxPYjOx1wNy58iJiSXfBy9CXAiBzwi6JNpgEBcnr4w8ZBR1Di9qs7nDikKhJUcHUc6vPGg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.4.12"}, "deprecated": "ReDoS vulnerability parsing Set-<PERSON><PERSON> https://nodesecurity.io/advisories/130"}, "0.9.13": {"name": "tough-cookie", "version": "0.9.13", "dependencies": {"punycode": ">=0.2.0"}, "devDependencies": {"vows": ">=0.6.0", "async": ">=0.1.12"}, "dist": {"shasum": "8b3b0e536d8b1ee350aa53fca99273d03af50a56", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-0.9.13.tgz", "integrity": "sha512-RBev1ZGhVU6xdDcdD7BL1X1VgoAvOZ7zkzypI5L2qKWVe/Tjclt4htkKiJ7sUTb0qeHK0c3vcE90799Q7abtPg==", "signatures": [{"sig": "MEQCIHSe1QdObOsuo9hrBkhtfjD1sZN4ThGJfrBroWkwO+XnAiB71laeJih16eFrfhCudxU5kGxCBz3flPafmMA8SYPcqg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.4.12"}, "deprecated": "ReDoS vulnerability parsing Set-<PERSON><PERSON> https://nodesecurity.io/advisories/130"}, "0.9.14": {"name": "tough-cookie", "version": "0.9.14", "dependencies": {"punycode": ">=0.2.0"}, "devDependencies": {"vows": ">=0.6.0", "async": ">=0.1.12"}, "dist": {"shasum": "fb2276385802fda1b3d25c083dcac93f2057d35f", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-0.9.14.tgz", "integrity": "sha512-hCEJR0pAerpL7bGrvtKSIjkbIn4zIga85vRpxWXqx+vv0cqMl9bvY0djAR7K8h+5IWLDh+FVHwExiPmSunxeBw==", "signatures": [{"sig": "MEQCIAtye5mEfANddXx4vsB+ErXu8uTmQng8ftfZgaKmXSiLAiAv+Lu9BXxvt1P03xpe1J2x7FpAvB45hreH0MYEqQpWlA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.4.12"}, "deprecated": "ReDoS vulnerability parsing Set-<PERSON><PERSON> https://nodesecurity.io/advisories/130"}, "0.9.15": {"name": "tough-cookie", "version": "0.9.15", "dependencies": {"punycode": ">=0.2.0"}, "devDependencies": {"vows": ">=0.6.0", "async": ">=0.1.12"}, "dist": {"shasum": "75617ac347e3659052b0350131885829677399f6", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-0.9.15.tgz", "integrity": "sha512-j4C2GYjRuBjEGRgF5eIvxSvUkTXk7KJDAX6SZtlDWNioUlCcW4lLSlETSWWSiVbXS6SOB+LUK7PYn0RDxgJ3pQ==", "signatures": [{"sig": "MEYCIQDgPhUt5WRDw97q7x+qQex7/P6pqbXQmkXkNJ660fmb+wIhALKgSUsn49PdKzOwD6g3/tMEVgFk59EjRwQEtgZ5RZQJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.4.12"}, "deprecated": "ReDoS vulnerability parsing Set-<PERSON><PERSON> https://nodesecurity.io/advisories/130"}, "0.10.0": {"name": "tough-cookie", "version": "0.10.0", "dependencies": {"punycode": ">=0.2.0"}, "devDependencies": {"vows": "0.7.0", "async": ">=0.1.12"}, "dist": {"shasum": "0cda4e24b2e6c417ad44270c48c9787f560aa1bc", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-0.10.0.tgz", "integrity": "sha512-jHFp3bNlSM+ugQ2ztGEf4pkCiZlYko9ShMboFS69OrTgvra4HHJ+hC4VsHaS821HKaxPaoMPCdRPLFgHOYz0eg==", "signatures": [{"sig": "MEQCIA1c4K+/cpWCbv2X4bIyqK6MFn22ESRD1lDbmdvrN+vFAiAHZfCgP+7bvc9vMfaV6diODhqkfSc7gK25tGhESElOYg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.4.12"}, "deprecated": "ReDoS vulnerability parsing Set-<PERSON><PERSON> https://nodesecurity.io/advisories/130"}, "0.11.0": {"name": "tough-cookie", "version": "0.11.0", "dependencies": {"punycode": ">=0.2.0"}, "devDependencies": {"vows": "0.7.0", "async": ">=0.1.12"}, "dist": {"shasum": "7d8ef58b287253c98f50fa21e9b14408e07db323", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-0.11.0.tgz", "integrity": "sha512-PBpcp5SnMfjIIbofOyDDxBK5nvaVFs259fVIfr2F4iqbqr6LlfvS3nDfrcyS8p5TG5TmmQSgStvoKAdYi7OmgA==", "signatures": [{"sig": "MEQCIEKjeOaoDMFayQKVun6AXXKK6sU7q1Y/yNg9CiF3L2sdAiA80SwaL/tbzXVx/OUyCpEgySif85FrwyDen31U1gLa1g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.4.12"}, "deprecated": "ReDoS vulnerability parsing Set-<PERSON><PERSON> https://nodesecurity.io/advisories/130"}, "0.12.0": {"name": "tough-cookie", "version": "0.12.0", "dependencies": {"punycode": ">=0.2.0"}, "devDependencies": {"vows": "0.7.0", "async": ">=0.1.12"}, "dist": {"shasum": "713ccd8a77cd987ccd10b6f6938bbd96ecba36bf", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-0.12.0.tgz", "integrity": "sha512-DX/q+v1Cn/QDElfxph0HrK2w6XEHI8Sv9+mDlQdb+SEVD6SmU78EviK67HbIOqbZkeA0E5LqW0FqvpR4PdbGAg==", "signatures": [{"sig": "MEYCIQD+E9/yIHBIfgH01SwFAr1z3y4/QCMh3z0Tms66EwJdJAIhAPzaa8wrgCyw9mNJozrUHJd/N2zLsOo7rgiWbmexDFpW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.4.12"}, "deprecated": "ReDoS vulnerability parsing Set-<PERSON><PERSON> https://nodesecurity.io/advisories/130"}, "0.12.1": {"name": "tough-cookie", "version": "0.12.1", "dependencies": {"punycode": ">=0.2.0"}, "devDependencies": {"vows": "0.7.0", "async": ">=0.1.12"}, "dist": {"shasum": "8220c7e21abd5b13d96804254bd5a81ebf2c7d62", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-0.12.1.tgz", "integrity": "sha512-+gd4PklNJsxzu1NoNjhGRfOZZ5llND6VtQZGuaDXdmI0Ii79V5+YCa2sLx8Q6lYhYN2+9frCzUwOLQpuwHvO4Q==", "signatures": [{"sig": "MEYCIQDR17EfeJNy0MhX5uRZ8i3KER8iAS+RytmSZIFblpnGbgIhANJkRxuWG7Uhq8O0/UYCM2hz1cXS3FemqsdQ6zgf9ghf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.4.12"}, "deprecated": "ReDoS vulnerability parsing Set-<PERSON><PERSON> https://nodesecurity.io/advisories/130"}, "0.13.0": {"name": "tough-cookie", "version": "0.13.0", "dependencies": {"punycode": ">=0.2.0"}, "devDependencies": {"vows": "0.7.0", "async": ">=0.1.12"}, "dist": {"shasum": "34531cfefeba2dc050fb8e9a3310f876cdcc24f4", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-0.13.0.tgz", "integrity": "sha512-VS439wmO0qE49JLHhhG6tC5dMFfmVdyryd2PTjkwsDvOMElMebPX4Lo3OLBBqLrOHnvcNdzQa7MS/SkNvO0Idw==", "signatures": [{"sig": "MEUCIFyUOUwuRIbxdW/DZ4SKQyGwvlJ0g89p+g/Yvkc6Gv08AiEAtj8CrQ8TcECA2ezmK/WJyNy19V0HYP7MeIchop5xQSs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.4.12"}, "deprecated": "ReDoS vulnerability parsing Set-<PERSON><PERSON> https://nodesecurity.io/advisories/130"}, "1.0.0": {"name": "tough-cookie", "version": "1.0.0", "dependencies": {"punycode": ">=0.2.0"}, "devDependencies": {"vows": "0.7.0", "async": ">=0.1.12"}, "dist": {"shasum": "56ae180f2a88bc5db462c479229ee36e071e0296", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-1.0.0.tgz", "integrity": "sha512-hFWvzFc9JSGIVtlEocqLwSc0FGLrFb7itTfO/5a4kYSipu8U68eq6RcyTaL+H7fhoxg0OUXbXXIWM2huUUECEw==", "signatures": [{"sig": "MEYCIQCwYYLglTEem6Q/kf8K4YoVtmr/Ba1LhUjOJPxaDfjCiAIhAM3AcWG+TZGtE6fxojREVX7c7nsMRD+MUXpn3/Z4id4J", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.4.12"}, "deprecated": "ReDoS vulnerability parsing Set-<PERSON><PERSON> https://nodesecurity.io/advisories/130"}, "1.1.0": {"name": "tough-cookie", "version": "1.1.0", "devDependencies": {"vows": "0.7.0", "async": ">=0.1.12"}, "dist": {"shasum": "126d2490e66ae5286b6863debd4a341076915954", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-1.1.0.tgz", "integrity": "sha512-7RU5PzHLrzl7RWnuBgiduEcGHIVVZG4pRyRhZwhvH7rVBcxOtQKUEM9Woxa9lf2OOTCV77/WbFuUO3FkpJ0ckw==", "signatures": [{"sig": "MEYCIQDZ1Psi+/DOla9fp56zidWMq7pwihirEiBHICQG9pUV4AIhAKbUrd3B+0/r1lExnwNCyb54wQPlifi3/acifUIZadgj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}, "deprecated": "ReDoS vulnerability parsing Set-<PERSON><PERSON> https://nodesecurity.io/advisories/130"}, "1.2.0": {"name": "tough-cookie", "version": "1.2.0", "devDependencies": {"vows": "0.7.0", "async": ">=0.1.12"}, "dist": {"shasum": "9b7e9d98e769e80b5aa899d944fe44e02ebf82ad", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-1.2.0.tgz", "integrity": "sha512-u7gvM6OgRHEQorTvPL+S0e6g4hMrsBUAQbh1mKz/79VyetrtavIHwZBFuvzkiJIucMMi3yfQiIr+Tl/UQXa5PQ==", "signatures": [{"sig": "MEUCIQD/5BrfKCHazs/iqKX/WmgTSJ9j0CcnIGlSluKPIXChJwIgWnbZqaEk18uZDR5kNlEx+PY6GfzXujK/Vx5/JHaOBEE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}, "deprecated": "ReDoS vulnerability parsing Set-<PERSON><PERSON> https://nodesecurity.io/advisories/130"}, "2.0.0": {"name": "tough-cookie", "version": "2.0.0", "devDependencies": {"vows": "0.7.0", "async": ">=0.1.12"}, "dist": {"shasum": "41ce08720b35cf90beb044dd2609fb19e928718f", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-2.0.0.tgz", "integrity": "sha512-qYeH1zA+4+36nVi2waxBoFcbL54iInWYs6NuMQztwijcfhPZqeCm/fjRkDrnEtkYzOIh19SkKrjs5A+VDx+5sA==", "signatures": [{"sig": "MEQCID5g8TOaUF5mGeVq+JRtIeQJJs+Zh/oTjr3L2JlG7URiAiBiYzu29HKbT48u7aUvVlcHkrYm7U2cFE2YEMNXAyw8TA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}, "deprecated": "ReDoS vulnerability parsing Set-<PERSON><PERSON> https://nodesecurity.io/advisories/130"}, "2.1.0": {"name": "tough-cookie", "version": "2.1.0", "devDependencies": {"vows": "^0.8.1", "async": "^1.4.2"}, "dist": {"shasum": "4e66609e3e4360a93aff0d4d64b3632e966e8613", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-2.1.0.tgz", "integrity": "sha512-jd1ifZarNXBTPmOH8+PDhw26AN3KjFzr01nrrQ2g3fy5xeZdT/OvcqasKW0riUcQFq+XFagffBf01584FBvD3Q==", "signatures": [{"sig": "MEUCIGXjNCcQDBaTNVVCh0NGH/R4jiOlbA7/JwUX8oxOF80oAiEAwy4m6tqTqb6e81SFTY1mBrdXuEPjOFf28AlhL0ETeZM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}, "deprecated": "ReDoS vulnerability parsing Set-<PERSON><PERSON> https://nodesecurity.io/advisories/130"}, "2.2.0": {"name": "tough-cookie", "version": "2.2.0", "devDependencies": {"vows": "^0.8.1", "async": "^1.4.2"}, "dist": {"shasum": "d4ce661075e5fddb7f20341d3f9931a6fbbadde0", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-2.2.0.tgz", "integrity": "sha512-frs4Ftha6JFRPQyqjgjx9UbInbzE6MZmJBDe65/3kbyuTR/WNLsYjbD4mH/KboKbGEQe0ICZ1xmbU8D4FYeVCQ==", "signatures": [{"sig": "MEUCIE5YOjEFQQjtGum7cbvxfmPYlRaJvqjak2pzf31UCi7ZAiEAp0x3ckksUiy3MxyDQolWGYslIWVBuRNlW8TJM7A03EA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}, "deprecated": "ReDoS vulnerability parsing Set-<PERSON><PERSON> https://nodesecurity.io/advisories/130"}, "2.2.1": {"name": "tough-cookie", "version": "2.2.1", "devDependencies": {"vows": "^0.8.1", "async": "^1.4.2"}, "dist": {"shasum": "3b0516b799e70e8164436a1446e7e5877fda118e", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-2.2.1.tgz", "integrity": "sha512-eEYYegvebFzOPfrJK0rcwfhp1oadk6zvwOiySQn88/xiL1WSK7Jdgrmq1MPqWAWvTgaTWiQN658Hsu/2ccjERQ==", "signatures": [{"sig": "MEYCIQChyFXYglyI1G4rJy6BWfn8oOCo6IHBLPn+FYyr3Ae3oAIhAIhyruyXNUoFiyeL0JFMZO20czzuNqychw8R6+16GI/x", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}, "deprecated": "ReDoS vulnerability parsing Set-<PERSON><PERSON> https://nodesecurity.io/advisories/130"}, "2.2.2": {"name": "tough-cookie", "version": "2.2.2", "devDependencies": {"vows": "^0.8.1", "async": "^1.4.2"}, "dist": {"shasum": "c83a1830f4e5ef0b93ef2a3488e724f8de016ac7", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-2.2.2.tgz", "integrity": "sha512-Knz9Yr0hlBoWQgUKzOIvRg5adinizAf49i2gHRhj6cLjlM304zRw7uyiY22ADniDxnPHXfIeyQD0EAkgpIz0ow==", "signatures": [{"sig": "MEQCIAZwy0RJP+GTJNtL1ZDYcuVKWHTtpJvMqkS0iJ51r776AiAWwc6RYfLa3vpEV1xM4PjlsCv+nTlvnDdAgIb3h7aIiw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}, "deprecated": "ReDoS vulnerability parsing Set-<PERSON><PERSON> https://nodesecurity.io/advisories/130"}, "2.3.0": {"name": "tough-cookie", "version": "2.3.0", "devDependencies": {"vows": "^0.8.1", "async": "^1.4.2"}, "dist": {"shasum": "38f67cc4fc1b3acd9f8643b92aa3cf138e4acbfc", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-2.3.0.tgz", "integrity": "sha512-L9ufTLFvuUouKJn1vq14PaynFl09dv5o54Ie/t1GD5JXg1kXbOdlIc5FOyHXEI2Akkdrw5CcASpQjstoZOjqMA==", "signatures": [{"sig": "MEQCIHEWVcNpBIv/0KQk7Z3ka8cE+HFiQ0lYOYp39UEHq9PQAiBzD34mgagzMplIPG6AJ5seQkspxanvp0tOOoj6OW/0zw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0"}}, "2.3.1": {"name": "tough-cookie", "version": "2.3.1", "devDependencies": {"vows": "^0.8.1", "async": "^1.4.2", "string.prototype.repeat": "^0.2.0"}, "dist": {"shasum": "99c77dfbb7d804249e8a299d4cb0fd81fef083fd", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-2.3.1.tgz", "integrity": "sha512-3C8DIrKkBO8q3BIqcUB6zPuqua6wCso6ddmRmHjbJ5/OfJ6rrw6zTf1DFDsBSZbKNKC1XzfVJRxZrdcxyjEOjA==", "signatures": [{"sig": "MEYCIQCwN4qJMHnRcHugeQYl2rTK3MJHeyY0NpwVvKGm0ppVfAIhAI3XSvM36tUV8QTdX3BEid5Nq69anNEpQE6OJhOAvMnJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "2.3.2": {"name": "tough-cookie", "version": "2.3.2", "dependencies": {"punycode": "^1.4.1"}, "devDependencies": {"vows": "^0.8.1", "async": "^1.4.2", "string.prototype.repeat": "^0.2.0"}, "dist": {"shasum": "f081f76e4c85720e6c37a5faced737150d84072a", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-2.3.2.tgz", "integrity": "sha512-42UXjmzk88F7URyg9wDV/dlQ7hXtl/SDV6xIMVdDq82cnDGQDyg8mI8xGBPOwpEfbhvrja6cJ8H1wr0xxykBKA==", "signatures": [{"sig": "MEYCIQCy1INmcPZBCecr993z2pi4qNlcaWvpSZ5ZBCyR14O79QIhAMnex5zsD8jrcmNyAFBd7/ApdcISk1YplNvZ/o8hE28t", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "2.3.3": {"name": "tough-cookie", "version": "2.3.3", "dependencies": {"punycode": "^1.4.1"}, "devDependencies": {"vows": "^0.8.1", "async": "^1.4.2", "string.prototype.repeat": "^0.2.0"}, "dist": {"shasum": "0b618a5565b6dea90bf3425d04d55edc475a7561", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-2.3.3.tgz", "integrity": "sha512-WR9pjSY3qO0z3yC6g33CRcVt2Wbevh0gP1XiSFql0/xRioi9qbDs3C+g4Nv2N8jmv/BloIi/SYoy/mfw5vus2A==", "signatures": [{"sig": "MEUCIG2Oweq4BNJRNtgwmEN8QbtTkSXKfd4YoJJdFw0AolgiAiEAtdfwVkELEIEoGzBQub2p+fevNEXRLSvtfdEQ1ZI8T9w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "2.3.4": {"name": "tough-cookie", "version": "2.3.4", "dependencies": {"punycode": "^1.4.1"}, "devDependencies": {"vows": "^0.8.1", "async": "^1.4.2", "string.prototype.repeat": "^0.2.0"}, "dist": {"shasum": "ec60cee38ac675063ffc97a5c18970578ee83655", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-2.3.4.tgz", "fileCount": 9, "integrity": "sha512-TZ6TTfI5NtZnuyy/Kecv+CnoROnyXn2DN97LontgQpCwsX2XyLYCC0ENhYkehSOwAp8rTQKc/NUIF7BkQ5rKLA==", "signatures": [{"sig": "MEQCIG2iW01+3jjm5npCBMwBUjHZbzfUo/kZijmWZL+Cs8OwAiAhU7UjqOnKnp7aFnAKA4uaZKlM2UQtKl+TbUXu+mrDzw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 245484}, "engines": {"node": ">=0.8"}}, "2.4.2": {"name": "tough-cookie", "version": "2.4.2", "dependencies": {"psl": "^1.1.24", "punycode": "^1.4.1"}, "devDependencies": {"nyc": "^11.6.0", "vows": "^0.8.1", "async": "^1.4.2", "string.prototype.repeat": "^0.2.0"}, "dist": {"shasum": "aa9133154518b494efab98a58247bfc38818c00c", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-2.4.2.tgz", "fileCount": 9, "integrity": "sha512-vahm+X8lSV/KjXziec8x5Vp0OTC9mq8EVCOApIsRAooeuMPSO8aT7PFACYkaL0yZ/3hVqw+8DzhCJwl8H2Ad6w==", "signatures": [{"sig": "MEUCIQCkMJCrxKyKK5Y3aBRD0GZsJcVRbPc1BDBgiabHk7CltwIgJr6XgXavNjYPi+pqNZUi9pW29MVsJ2mJhtn7Ni5eW58=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83527, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbFcXVCRA9TVsSAnZWagAAebkP/3vI33XnffCWzttnMUuy\nlkEvl2WA4AEeGUWa9M2DQbMEWOMejI07yGblzrL8gngjTz1unuRNosrKVVwW\n8Bf0fR4ZzZamD3UzjhJpslqrXaXEcibK6MjYbdZD5EaN4GyYuzyVp77hQE3M\n5nZqNmgvI5/hgkLmtFJhuxg7IuKL1FTpWmSJYMu11Mv2ZlV9Ki6rGKcjuIAQ\n2UIomwxTfyKguXX5zeqH4F/J0Dq4f30ick8DfpHYwDwdCclLqVtIojVex961\nLNt5dsyVTVgMV9UuWWsYnNG9Z3vOT31z5ib5BxP0nG2Xzw17if4pS7R98leV\n9WQjJYxc7wpRv2E/27xSrLJxYu+5R6YiBhYyqv4jhxXfSchJTIRuUzRXfXEZ\nrn/gg8Xz1uKPOBGFu+wu6a+oWinOWQ7k2OTgdmIhu3YJqHN7kHvDfnLnQsXQ\nbQQ+fW+SDd0mHIKBHhYgnLd6mpLEHAQmaTON5a2ivQrRdGhm7htOoN7A3fhP\nTw5tq7AJLkpm7gIsswFwjFBrMNqnEWhVI71iP+UqN7AdtBVgbpjj1YPmJcQE\nHZ0ntcp4roksLzvZwQMtCKS8AGIXEFTWt7W/0LVsGTz72+EnB9Sw6l9IxMwp\nJaYswwVvCBfe010Nx6+LGp4ZBgGt19iirfHCesMFRm8Hs/lDNFm3uzCfiJoQ\nZ0Bs\r\n=Dc7H\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=0.8"}}, "2.4.3": {"name": "tough-cookie", "version": "2.4.3", "dependencies": {"psl": "^1.1.24", "punycode": "^1.4.1"}, "devDependencies": {"nyc": "^11.6.0", "vows": "^0.8.1", "async": "^1.4.2", "string.prototype.repeat": "^0.2.0"}, "dist": {"shasum": "53f36da3f47783b0925afa06ff9f3b165280f781", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-2.4.3.tgz", "fileCount": 9, "integrity": "sha512-Q5srk/4vDM54WJsJio3XNn6K2sCG+CQ8G5Wz6bZhRZoAe/+TxjWB/GlFAnYEbkYVlON9FMk/fE3h2RLpPXo4lQ==", "signatures": [{"sig": "MEQCICetFbo9YKADTmuQ0ii1aDWfGQbdb3yGQDhVX+XGEWqvAiBoGt2+lr51EM0MG4heI5fLinoJivS5ikY7p7lBpbiiFA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83959, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbMVcQCRA9TVsSAnZWagAAzX0P/2COkbzb3dOX/FTOYIVL\n7oHge7BvGODNkEHTjBtNsR0jO/AA/gVcUOq6CSmFH+UC14LrmCyNDemQvD15\njnYuJXUIiZsSZO7BGGbOFYrQF+DKLiwgf0LokrIM8KasZ5nxc5xS1jYHG5o+\nwCqutfhlRTqlPbZsexB+eRlIuYNvpsbRAdSDYXrhLJNMZgx49g/SZ7IwunGN\nJ/5Fe041MZqqdi7MAavM3T7vX9IvVRJJ3f4xAEVxY1vh4s0dN+h4fG2eMQzL\nOiaulRUIahs4xmJUYHZgZESc9eTIrI8K+YT7i3r35TGc0Du7ueB2KYxBqy7q\n6AUFbdX9Ep308kTVsTf/h4wE0RvdxKLfZ0ww7T1ivADX9xMi2oIvVtyhCQHo\nSIpyBhaav/fTufF0/Fu4Lcr7PMJxyvF0UcNRqrPmEQTw4UZkFKfT8HN2P5cH\nBvJsFwpeEjt84tt5pJ/bmD0j0EUCLUXLGH6fbA84fHpZGifO239zSf262Qdv\nSXeygU7YxnK8wOCnvlauZx/gp+/WTMfMdODxiX0QnqwS5BctiPCgGG1cKJcC\nqvTECaAEmxMVf/gEW73oxVXJgUO7f6jJaWoawV7ka+wR91W9Bp0pKGOOAxAG\nU72rmnwQKid9U7ly5833sb3D+lbzKJXGNIqohegP4daKZfNNTaLWZXmKE+Iy\n7Vml\r\n=8KpS\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=0.8"}}, "2.5.0": {"name": "tough-cookie", "version": "2.5.0", "dependencies": {"psl": "^1.1.28", "punycode": "^2.1.1"}, "devDependencies": {"nyc": "^11.6.0", "vows": "^0.8.2", "async": "^1.4.2", "genversion": "^2.1.0", "string.prototype.repeat": "^0.2.0"}, "dist": {"shasum": "cd9fb2a0aa1d5a12b473bd9fb96fa3dcff65ade2", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-2.5.0.tgz", "fileCount": 10, "integrity": "sha512-nlLsUzgm1kfLXSXfRZMc1KLAugd4hqJHDTvc2hDIwS3mZAfMEuMbc03SujMF+GEcpaX/qboeycw6iO8JwVv2+g==", "signatures": [{"sig": "MEQCIEYFbv3CWBGVbI7AMMc+Dlq7X1lqNc10as1vDexIldpEAiBkSdt+ctje2SmxUV7ShsCOifWPkP0EAcQrkBQGya0fUA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 86644, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb/HqnCRA9TVsSAnZWagAABFsQAJO1aOX7ja9l926/wykB\np/V8EJ1GC61WAB7vpYqz8spGAX5Sc/RsMgj2af9BFIXzvj+jx/suXlcnnspP\nuti6WLq1J0xA+IC4HsDJrsw0N3c4ycPyi+Pg5R24nDGUaTbg5f8jcwy8pbmD\nBl/kwdxp5Z5qDmbyFtyk4wMi/i+z0NPBHi2Le9kwK+y5kArxgqpktgtRZSQ0\n//LkVmDHy28IGHSyDOjgGS/fcUfQUaHncbt642qvNa+T5AkSWBtb9fExByEW\n2EF1QOwN9UEfCT6VHf2SvtqCOskBr4ub45u2OChHdZICgkEXiSguQ+2HtU3E\nUzUy1gPCS6j39L1+t8nAlWaI6OAizY0iqg2uiRVS8N38SZrLYZk0hkA2Hlgj\nx2s4DivIs8Rg/0Ma1CRY3cMDbiZiN5/5G7ekGLhiI+eMvQkD4xV2MPIEE4cC\na8mncey6W59O8CnTrFePVx4o07TQarkGN/FU8RRhylOrSmLLnZqmwy/NgLIF\nL9d5kVlea+gDnCYx0EzJ5T1zeCMQMHvdavbf+3n/kLMjrlhDahlWkXII391I\nVjafX4/JzDMJcaF69CG3sVsdGTAhNYQPFDevJjRW5TOh3yF9dEPj3BJU37PE\nXhTEqYH4cZZrNwvz5QC5cBMIILXckxcvIitdlFRx8nVH1hJCSQPCn2izHFMa\nIGH7\r\n=NGDH\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=0.8"}}, "3.0.0": {"name": "tough-cookie", "version": "3.0.0", "dependencies": {"psl": "^1.1.28", "ip-regex": "^3.0.0", "punycode": "^2.1.1"}, "devDependencies": {"nyc": "^11.6.0", "vows": "^0.8.2", "async": "^1.4.2", "genversion": "^2.1.0", "string.prototype.repeat": "^0.2.0"}, "dist": {"shasum": "d2bceddebde633153ff20a52fa844a0dc71dacef", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-3.0.0.tgz", "fileCount": 10, "integrity": "sha512-LHMvg+RBP/mAVNqVbOX8t+iJ+tqhBA/t49DuI7+IDAWHrASnesqSu1vWbKB7UrE2yk+HMFUBMadRGMkB4VCfog==", "signatures": [{"sig": "MEUCIQCaZ/iMALNDbmvaIPuTqka7rhC1ldXGYSZ+KlvV9sKeKAIgRctUJD7Xz6zxY7cnNvKjs7s9jjCBZVHAQBOFl+Zva5o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87070, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcNP+KCRA9TVsSAnZWagAADf4QAJi7MF7vm4rLLpmyny6X\nD+T4VwBe8XwV0PbhUeiGzKxYfGn1UEX5rS5tlU9ZhL4WxQ6u1Ay3ToXW/2co\nos1oDufOmsOs4Ws3OmBDJqucOFQ4N7S3gUgmm0bwsgXGtJiJ4Wi3FOjyT8Bg\nr5k13amk7SHWwirTrhPRe1uIa5sytnDrixsqOgvOp3oyBHAkgW48EWVXFjhQ\nptKKDCeHbpcT8tu3GsleoIfcp+Qy3BwqkFQyalsXxNAdOZz0nkLCEfbedIVF\nrA4q5kvTItaMNz1h4f9US01ppzjiq9AN100FGG8SRRvNmnbVfrwPjb7fSgIc\ncnaRX4r3liMpZnVjhbm8ODGAHnyubFvpSIoy9fWGhBbtR+OfDOM36DE3fylc\nhZcAI/ZjFthODwuSIbbGjcXXlRj666PMdpbpb+vK8PR7pN2UcDJueLdK+AZo\nLER9tpfiLHmoEq/dj5i0VFQysjZmEiY54Ub8NhClrYRctUz7A7DotINjmvk+\nIDCUqBy1b3o1OoBka8A/43FfEXfZWbWcqr5bF7JGT95qLQNqzAlFjiHXpn8x\nYuTpI5TO94GWTrd4srB3S8e9xskHR/+9eESxZJsRNeCdvE/IHKEtnE8yg30z\nku2fXb95Y0Wd2vW9UTYDCTFhm4deaXRXO2TBGcD4XbHFq4y520OQce9a2TqI\nZQOl\r\n=T9IA\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}}, "3.0.1": {"name": "tough-cookie", "version": "3.0.1", "dependencies": {"psl": "^1.1.28", "ip-regex": "^2.1.0", "punycode": "^2.1.1"}, "devDependencies": {"nyc": "^11.6.0", "vows": "^0.8.2", "async": "^1.4.2", "genversion": "^2.1.0", "string.prototype.repeat": "^0.2.0"}, "dist": {"shasum": "9df4f57e739c26930a018184887f4adb7dca73b2", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-3.0.1.tgz", "fileCount": 10, "integrity": "sha512-yQyJ0u4pZsv9D4clxO69OEjLWYw+jbgspjTue4lTQZLfV0c5l1VmK2y1JK8E9ahdpltPOaAThPcp5nKPUgSnsg==", "signatures": [{"sig": "MEUCIQCSO6PKoMdqxvFYATUmRqzRMRf4E+o3K9DxlojbNTGQOgIgcDi6jxBtowe3g3CcuhwbqwMmWEkC55d8lh+5JUuJubU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87070, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcWP56CRA9TVsSAnZWagAAsIAP/RlcRoDPI26puHk0sTVv\nlXuH+3rlk8xqX8w4ERWdhISu6ZznD3qBdSnGMPEI37QWUmC6WhQLYAJUkCf7\nJ7PNdqN7fQFLtpq4jyEzES5ccuASUzFN4qffJKVLRODQFvr/SAuP/hR7u0OC\n+x9Q5Z+TsYCKuDR31yaH+eTsgxDIfd6O3Z6PqunA5lJN169PDDNHhCcofZyU\n21D80ky/ETA6Z9PqTuO0zNdKog7xdEPuhfa3CZ7EqFBHpN61nXg3uaDkQjiy\nWFy94EbLkt/43ffku7xFyr043wE4n/bvtKHvZIYV8ysZwS2x96O6/cimrTy7\n9z74OFYGXDI75Phtb7lOqA8nQG6fkeEgIAKmw4PpCpEEW3oG+4jSswxbLVWs\n45G4mNpLx1+2UGTLQKoZzp63uReQfeOlkzkAy+n5yf8EEeLCKjHjFADw3po2\nTNAXyCtamUIPGm/z7P9IPxCCqTmdbO0LhCzv6RgjMSSi7nkpWC17y+FAo5st\n1/f5+ZSdR56nVDzhwjvK0fLJ9SHL9gQs1xIQKeI+vUbWefymRr0ql28DUheC\nckKdO+SwYSmVAC6qZOVvDCb1YRLzJtBOXNYOON4jKaYGRncFsQNWPPBlXI/B\noHpXZaVXybbF3fKIpZkoJAjzOIKbqhKL475XHy7Xpc8YZMLEsyWSN1CwABA8\nFR63\r\n=dLGc\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}}, "4.0.0": {"name": "tough-cookie", "version": "4.0.0", "dependencies": {"psl": "^1.1.33", "punycode": "^2.1.1", "universalify": "^0.1.2"}, "devDependencies": {"nyc": "^14.0.0", "vows": "^0.8.2", "async": "^2.6.2", "eslint": "^5.16.0", "prettier": "^1.17.0", "genversion": "^2.1.0", "eslint-config-prettier": "^4.2.0", "eslint-plugin-prettier": "^3.0.1"}, "dist": {"shasum": "d822234eeca882f991f0f908824ad2622ddbece4", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-4.0.0.tgz", "fileCount": 10, "integrity": "sha512-tHdtEpQCMrc1YLrMaqXXcj6AxhYi/xgit6mZu1+EDWUn+qhUf8wMQoFIy9NXuq23zAwtcB0t/MjACGR18pcRbg==", "signatures": [{"sig": "MEUCIAQCtnCbyPSjpZPPJBXuCaL5Q5mFYYmzTDWfoSMSMahpAiEA3+uUvmcDNRrJyZybJSRlT0qzllgFZX6YCzJvfwBGFqQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 98591, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJec8X6CRA9TVsSAnZWagAA50wQAI0neQjP1hgIBfW4V38C\nAz8VbNBRaMP/RVn4a2mWBS6Y8Spl4/vrgipcm4gHnY97GPsOahxJbvgPXyFV\nPjDKe0IcjReFMr7qTZJcg27Z99I1QqjNm0lSQffm2Kx+2CTzmM78qPFHCQLl\nQFjb09vmchVSIA29vcE1wsv64fP8XkaYQnjDBP6bb01mv8PBgNcnPQ+R1K6L\nFCzTtje3qayhlzMhLdcUp0nyCAge4XVrS/ihslnZyq/L5xgFPG+KiLTRSJH5\nQDXX5ZskkaHiRM4zSRb8lGY+NrefYQiE/I48d4Xt2G/6YNdmZ17J27iK7CKJ\ns1Wo/BZ87bDBHxHYS4YsSJKsyn3R+A5Wz1skjqXAXc2uuyGVaGaGcp4TiGGM\n2EEau0+/EkOxmW35uCxZEaJzgFnRZiHWNkrBXOSIfaws8dwZeyZ4YUvrz33H\nrkoeMqvABX7FqymFxHjOTgOvvXwTRaISj/RXQ0xLqtJZATS1m4Q+gOImYQ6N\nin6FdaBg912cnQsedw2NXxQYt0BfdboJ1pRN+9k4zAKROWzaiberMag1Wgzu\nvDvcXw96zKC3WVu5zmdrb8KVofNfaBFlyYW5txJEO4+D+BB/JQF3/KybFIk4\nR38qEmxeTJis696aIObBnD6OI/GVQbghX3Gk0Yw9/efPviCSvIq3O970QcRe\nKerl\r\n=LEUk\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}}, "4.1.0": {"name": "tough-cookie", "version": "4.1.0", "dependencies": {"psl": "^1.1.33", "punycode": "^2.1.1", "url-parse": "^1.5.3", "universalify": "^0.2.0"}, "devDependencies": {"nyc": "^14.0.0", "vows": "^0.8.2", "async": "^2.6.2", "eslint": "^5.16.0", "prettier": "^1.17.0", "genversion": "^2.1.0", "eslint-config-prettier": "^4.2.0", "eslint-plugin-prettier": "^3.0.1"}, "dist": {"shasum": "039b203b2ad95cd9d2a2aae07b238cb83adc46c7", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-4.1.0.tgz", "fileCount": 12, "integrity": "sha512-IVX6AagLelGwl6F0E+hoRpXzuD192cZhAcmT7/eoLr0PnsB1wv2E5c+A2O+V8xth9FlL2p0OstFsWn0bZpVn4w==", "signatures": [{"sig": "MEUCIH7xTNiCTc4n+A1zqf0zoCu7Li3R5NxzZqWdrQRBSb00AiEA5zLPz4Q/xy4dVJ8DSqfsc8MGMNRHCepmTIkGq/s104E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 110945, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjA753ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoU2BAAhlzXflBVF7ApbHwodg36nw9LoNAYU67PY6u6aJm2DJNjEHWy\r\nntLXUkvf4da7v1hgQ9ruDnRHOgcmBfgQF1hhDUw6Gcz2Q9UmZz8VOd0boTWQ\r\nsrKE9uGZzm6BO4QVBMd0gnQlfGI5ygwy8Z9CjIUuRhjTI94drPOngvyItu4m\r\nVpRj+9wMqhIwZ4rNmsW7dZSSWhLMtKQ3zjIPGj9IaiEufLeq/IkNnNn2zDdl\r\nNiQsXkx/vyssAiN969ctsc+hVCccMEljoFjAosOuQqHhAXeNNnEP3m4vVEB0\r\nz6muYFirvsNknByoW6Mr42XSd714PjPYzwc/7UNgBc+iX3QslwKyPEfi1JFe\r\nFSiJXZeo1a9huINCDJ2A6SmIcWPIhnlMVb6oJ74gj1JDTOY+awJCbwTCrIX8\r\n26VK8w0xaZDvjKJAvjleOp0yFAwd5qY0N7TcOhLpBJOEfcYTQRt1ySPCY8e0\r\n126gEGLEG2jjj86qoc1+UM6zqZD7f3Mu7Cl6eI3lmFhoyclDlibgzgTBFmBu\r\nKAejSYZw3AhwSy0C11NY5S7mW+CGOY0CkzHQZCKrt3K9jnP1/UxggiDKFJ7j\r\nPelZEkb3TbMQyV7QhBPNKJCBMEKKZdaQhgyt46DSl8mMSdsNCFI7kLXP0UNn\r\nRtg4JI6PlZr/pb9nOtsWmsbRRSHM3chQVZ4=\r\n=NI9r\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}}, "4.1.1": {"name": "tough-cookie", "version": "4.1.1", "dependencies": {"psl": "^1.1.33", "punycode": "^2.1.1", "url-parse": "^1.5.3", "universalify": "^0.2.0"}, "devDependencies": {"nyc": "^14.0.0", "vows": "^0.8.2", "async": "^2.6.2", "eslint": "^5.16.0", "prettier": "^1.17.0", "genversion": "^2.1.0", "eslint-config-prettier": "^4.2.0", "eslint-plugin-prettier": "^3.0.1"}, "dist": {"shasum": "3600960f1e1c83545f130ac80043b9de8e5d488b", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-4.1.1.tgz", "fileCount": 12, "integrity": "sha512-Ns3k8QxkEzIfLZbRwLOrMPDqRa1BEAl4BzNNAOYY4BhBmEkf+HvP467F4NrD9loK3NcYflWOpUH3LJg0ehq/rQ==", "signatures": [{"sig": "MEQCIAfoAhE+ryFWXRiRHEbJYExk27Xva65vMgt4iqqYO7mTAiBFTg0D+wDTNxeBxBSeqn7+8+nzFl2ucCmH3oNlxxfM8Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 111023, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjBn4xACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpMcA/8DK3/t7wu2242cGOcdyBJDgiqXqD4OFrELjaqPo/V1l+KNdno\r\nTsJO/o+orPqd7JzQYVN7QpttgrlOS/5XXyc4LyFeq+VjbtSspSBLeKTxNDJN\r\nFTlcdNIR7n5HwLNCIwsDElzh2j4+ZpSN5fW+ZRrOOSZBPI/7CknyaGPogWR/\r\nsICzxDkl8LJMTsOUsdFKkHNBUVTIz59obFA5dGcqwCRS+3iBNmNEkFj9Jv3m\r\nnM4KrNAkDmPLF5VR82f/4YsSlo9h7MVb6oiXSSoFQh1NfJbOGljQNusZXq16\r\nZ4+sLuaOIVKR0NCOfaPB9rt9XhfHUfO6Sx8cYRt7ZkqCIoQMVlpSDGPcEmeU\r\nShLSZMChLKfZ3qcO6tYYR8LDujN7K9TVLsiimUe9jaB2Q24Ccf4gRMe0UCrp\r\nGDtaGLVsGpzjCyKGX6KaJGcSdrqwt/i+MZ7UBIKRB9Nicg3K+vAikjTCpF4O\r\n/Keic74Sh2YbSVpiiZZwKcOPRyrhK89cQuYMOkVdttJIzE3UAOBYNhKTJIRd\r\nJJDDIMLX4s+NYd89oYz2ufXz+SPzK1Zeu4vQA3cObhqAgbxwIYimQfD04MbU\r\nFX18PqqoE/31xoP5gRqOAXLJjTQ1seDmBrQw5H9wY+DMav/3pARjPt06PS3A\r\nwElCO0WyNhrewr9vLYTZueHHXdgTugTst6Y=\r\n=spPn\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}}, "4.1.2": {"name": "tough-cookie", "version": "4.1.2", "dependencies": {"psl": "^1.1.33", "punycode": "^2.1.1", "url-parse": "^1.5.3", "universalify": "^0.2.0"}, "devDependencies": {"nyc": "^14.0.0", "vows": "^0.8.2", "async": "^2.6.2", "eslint": "^5.16.0", "prettier": "^1.17.0", "genversion": "^2.1.0", "eslint-config-prettier": "^4.2.0", "eslint-plugin-prettier": "^3.0.1"}, "dist": {"shasum": "e53e84b85f24e0b65dd526f46628db6c85f6b874", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-4.1.2.tgz", "fileCount": 12, "integrity": "sha512-G9fqXWoYFZgTc2z8Q5zaHy/vJMjm+WV0AkAeHxVCQiEB1b+dGvWzFW6QV07cY5jQ5gRkeid2qIkzkxUnmoQZUQ==", "signatures": [{"sig": "MEUCIEUy+pxJNMBHOD4GkhPqjOJcehTbIJ0yaor75oZhm3AFAiEAhZ2tTh5u6TgISSHV96ABMlH50z/CPMLqdwJGRyaK4ro=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 111265, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjB8SbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqz6w//TBi0Ctct6cZws8JksB34lSki0wEC1cnhTbJ/ualMjNICM1Jf\r\nAb+OWdIsCe3PBGWCfgTgIs6qY4gJ02OeD9XKAXrPpw1ZnPyqjfsLTdSZqx1p\r\nPaoLaOI4CH56eeV7nsQb1WgDfuJ0hHRVTPEdfm4VI4H8uYL1FEbBjfqnJLAv\r\nYj2sbsSL6QCBTe1O0XHrdi+Uq4kXR2UdYniKo9oAyZv9m0g4nuToqn1bv9a2\r\n3dNK0gAYj5nGQ5ZuuFjhpeYXe5V3DGI5slfB3oObckjmVm0JnRGL2W/+BwyF\r\n0wUfKEepixlHEBgRC07YOTcj/Mg7+z/9iQ8elhuLU8sLmirCllFk5TU9hgkk\r\njt+r2U8XAvbghgu07tZAocf8pjuemNcO7HL/gFxYAQZV5GS+c/8Lif7p94rX\r\nZzFIVhEgBMZz5oHTxBnYXI1dxcAvLGO4R/lslLyGSR/N38NJorBi8c/PrYPy\r\nrfP9Q51/fEiQjcK7su70SgZcGcAWr5zIU48rV68PFJZiW2lXAvATJYUTp0Gp\r\nsn5V346saQHBMLRViSR+XZkrc11uKXzKPpHPyanK6oAcQo+L609bOFufJql+\r\nDHekjeick6DniFQ5PraGKqHGWkWLpY2jtXs8OAkA6nb1G6J3vz0OXN8YI1C4\r\nxBR3Gq9O3L/u+zdW5EL3xboZ9sHtRJUWE3c=\r\n=nD4R\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}}, "4.1.3": {"name": "tough-cookie", "version": "4.1.3", "dependencies": {"psl": "^1.1.33", "punycode": "^2.1.1", "url-parse": "^1.5.3", "universalify": "^0.2.0"}, "devDependencies": {"nyc": "^14.0.0", "vows": "^0.8.2", "async": "^2.6.2", "eslint": "^5.16.0", "prettier": "^1.17.0", "genversion": "^2.1.0", "eslint-config-prettier": "^4.2.0", "eslint-plugin-prettier": "^3.0.1"}, "dist": {"shasum": "97b9adb0728b42280aa3d814b6b999b2ff0318bf", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-4.1.3.tgz", "fileCount": 12, "integrity": "sha512-aX/y5pVRkfRnfmuX+OdbSdXvPe6ieKX/G2s7e98f4poJHnqH3281gDPm/metm6E/WRamfx7WC4HUqkWHfQHprw==", "signatures": [{"sig": "MEUCIAZVXfCk1+POemg+4/Bq/dKZv3MmPUc6CHSGq8Uoy2RkAiEA25Q3TsaeQ8yzBNfscUbA7hD4QJ6pE43n4Hu3YpayOY4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 111456}, "engines": {"node": ">=6"}}, "5.0.0-rc.0": {"name": "tough-cookie", "version": "5.0.0-rc.0", "dependencies": {"psl": "^1.1.33", "punycode": "^2.1.1", "url-parse": "^1.5.3"}, "devDependencies": {"jest": "^29.5.0", "vows": "^0.8.2", "async": "2.6.4", "eslint": "^8.36.0", "ts-jest": "^29.0.5", "ts-node": "^10.9.1", "prettier": "^2.8.7", "@types/psl": "^1", "genversion": "^3.1.1", "typescript": "^4.9.5", "@types/jest": "^29", "@types/node": "^16.18.23", "@types/punycode": "^2", "@types/url-parse": "^1.4.8", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "@typescript-eslint/parser": "^5.57.0", "@typescript-eslint/eslint-plugin": "^5.57.0"}, "dist": {"shasum": "c847afb84d51923d3a114fb72109de77061f3a56", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-5.0.0-rc.0.tgz", "fileCount": 12, "integrity": "sha512-wxZlZr3cZNegRzh+4R2VYJvtpX81BwGsoDUREM2ndENZJzvXBnAKO+IWumePhYGpB8n76LfAFp0Rl9an+QKd4Q==", "signatures": [{"sig": "MEUCIQDjhaNibfGK695H9+sfdkjHSlmMBmQIvh1F8nkrOzVaOwIgF0bIuyZncu3GSygzL1P8fZisKErTj4MTMepHAO21mQ0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71779}, "engines": {"node": ">=16"}}, "5.0.0-rc.1": {"name": "tough-cookie", "version": "5.0.0-rc.1", "dependencies": {"tldts": "^6.1.0", "punycode": "^2.3.1", "url-parse": "^1.5.10"}, "devDependencies": {"jest": "^29.7.0", "vows": "^0.8.3", "async": "3.2.5", "eslint": "^8.54.0", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "prettier": "^3.1.0", "@types/psl": "^1.1.3", "genversion": "^3.1.1", "typescript": "4.9.5", "@types/jest": "^29.5.10", "@types/node": "^14.18.63", "@types/punycode": "^2.1.4", "@types/url-parse": "^1.4.11", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "@typescript-eslint/parser": "^6.13.1", "@typescript-eslint/eslint-plugin": "^6.13.1"}, "dist": {"shasum": "a27d86bf7ae96be7acfe527cdf5db247ce0cfbdb", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-5.0.0-rc.1.tgz", "fileCount": 41, "integrity": "sha512-2XI8gvpALkNg6hFxQfwllhjmDx54u6KQojske77JGNmjRGvF1UAfF5lPaOp1Vm95VKZp2k9J6YBR2RiXcgPHvQ==", "signatures": [{"sig": "MEQCIHiewU9EcjmIgorU0bc11x2QmGWpfRcFrHGGcOvH73L2AiBb9CORNQIkSIofYgpXKKnpqGujKoKpZcDxFgiGwFOK6A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 166577}, "engines": {"node": ">=16"}}, "4.1.4": {"name": "tough-cookie", "version": "4.1.4", "dependencies": {"psl": "^1.1.33", "punycode": "^2.1.1", "url-parse": "^1.5.3", "universalify": "^0.2.0"}, "devDependencies": {"nyc": "^14.0.0", "vows": "^0.8.2", "async": "^2.6.2", "eslint": "^5.16.0", "prettier": "^1.17.0", "genversion": "^2.1.0", "eslint-config-prettier": "^4.2.0", "eslint-plugin-prettier": "^3.0.1"}, "dist": {"shasum": "945f1461b45b5a8c76821c33ea49c3ac192c1b36", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-4.1.4.tgz", "fileCount": 12, "integrity": "sha512-Loo5UUvLD9ScZ6jh8beX1T6sO1w2/MpCRpEP7V280GKMVUQ0Jzar2U3UJPsrdbziLEMMhu3Ujnq//rhiFuIeag==", "signatures": [{"sig": "MEYCIQCudAtt/UQBKzfveaDCOKQjGa5Kqo8hHdjOltvnsIvceQIhAIGpaDQ63bpISPTPWUGQYJ0howz1egmDQ8Qx2jBY/vXL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 111787}, "engines": {"node": ">=6"}}, "5.0.0-rc.2": {"name": "tough-cookie", "version": "5.0.0-rc.2", "dependencies": {"tldts": "^6.1.14", "punycode": "^2.3.1", "url-parse": "^1.5.10"}, "devDependencies": {"jest": "^29.7.0", "vows": "^0.8.3", "async": "3.2.5", "eslint": "^8.57.0", "globals": "^14.0.0", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "prettier": "^3.2.5", "@eslint/js": "^8.57.0", "genversion": "^3.2.0", "typescript": "4.9.5", "@types/jest": "^29.5.12", "@types/node": "^14.18.63", "@types/punycode": "^2.1.4", "@types/url-parse": "^1.4.11", "typescript-eslint": "^7.3.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "@microsoft/api-extractor": "^7.42.3", "@microsoft/api-documenter": "^7.23.37"}, "dist": {"shasum": "ff21e01f13c1223880e26886989355a18bac5dab", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-5.0.0-rc.2.tgz", "fileCount": 22, "integrity": "sha512-dwe5OkVHGe1NinilLDJWapqyQyHBtxlai0qWhIjysY7nSnATpsqhnfIEkZTTJ5HUdECz8V14zf015GF7V+xACA==", "signatures": [{"sig": "MEQCIARQ42vAn4AWlBXge+vc1A2d+EaXvhuceSdR7bk2IL88AiADmPpybRyWeOAF78ExeXHlBZ3FeLwAO7Gqb2g70xuAOA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 142745}, "engines": {"node": ">=16"}}, "5.0.0-rc.3": {"name": "tough-cookie", "version": "5.0.0-rc.3", "dependencies": {"tldts": "^6.1.28", "punycode": "^2.3.1", "url-parse": "^1.5.10"}, "devDependencies": {"jest": "^29.7.0", "vows": "^0.8.3", "async": "3.2.5", "eslint": "^8.57.0", "globals": "^15.6.0", "ts-jest": "^29.1.5", "ts-node": "^10.9.2", "prettier": "^3.3.2", "@eslint/js": "^9.5.0", "genversion": "^3.2.0", "typescript": "5.5.2", "@types/jest": "^29.5.12", "@types/node": "^14.18.63", "@types/punycode": "^2.1.4", "@types/url-parse": "^1.4.11", "typescript-eslint": "^7.13.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "@microsoft/api-extractor": "^7.47.0", "@microsoft/api-documenter": "^7.25.3"}, "dist": {"shasum": "786a62bda1de3c4eeaf2dcd3cd596eeeb4c8aa27", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-5.0.0-rc.3.tgz", "fileCount": 22, "integrity": "sha512-vPx5Wz/Vz4NwE0uFUgwZfZfmn3RVXSnofUAmFPQNMuA2cNxFcPyX6f2d3GgpQWyGswzzZeZSv3G64DaRkjmQdA==", "signatures": [{"sig": "MEYCIQC1QgYepWY3T2EHyNi3iVNbS+Je1y3+imSGjJvp4SUQogIhAJaFvIJn8tVOB2ZufiD0Le0wWAPoggAV1CDt71iMholk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 138592}, "engines": {"node": ">=16"}}, "5.0.0-rc.4": {"name": "tough-cookie", "version": "5.0.0-rc.4", "dependencies": {"tldts": "^6.1.32"}, "devDependencies": {"jest": "^29.7.0", "vows": "^0.8.3", "async": "3.2.5", "eslint": "^8.57.0", "globals": "^15.8.0", "ts-jest": "^29.2.2", "ts-node": "^10.9.2", "prettier": "^3.3.3", "@eslint/js": "^9.7.0", "genversion": "^3.2.0", "typescript": "5.5.3", "@types/jest": "^29.5.12", "@types/node": "^16.18.101", "typescript-eslint": "^7.16.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "@microsoft/api-extractor": "^7.47.2", "@microsoft/api-documenter": "^7.25.7"}, "dist": {"shasum": "3a894d4ec329dab4bc4a39cc04f58d3f0c6a8459", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-5.0.0-rc.4.tgz", "fileCount": 22, "integrity": "sha512-EN59UG6X/O6Nz2p21O6UK8R97zvLETOZ9+FGNdo56VuJZ8cftVCZ6tyxvedkQBfcX22avA1HY+4n04OVT2q6cw==", "signatures": [{"sig": "MEUCIQD6/oQpPR7M8HBcfo/M64/NEHmGKUfMI73g5+T5MzmYtgIgdXbI91f0rPvdajwUDP0qP/2sS+VLqcN7Ljabf1+aaPA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 138979}, "engines": {"node": ">=16"}}, "5.0.0": {"name": "tough-cookie", "version": "5.0.0", "dependencies": {"tldts": "^6.1.32"}, "devDependencies": {"jest": "^29.7.0", "vows": "^0.8.3", "async": "3.2.6", "eslint": "^9.9.1", "globals": "^15.8.0", "ts-jest": "^29.2.2", "ts-node": "^10.9.2", "prettier": "^3.3.3", "@eslint/js": "^9.7.0", "genversion": "^3.2.0", "typescript": "5.5.3", "@types/jest": "^29.5.12", "@types/node": "^16.18.101", "typescript-eslint": "^8.0.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "@microsoft/api-extractor": "^7.47.2", "@microsoft/api-documenter": "^7.25.7"}, "dist": {"shasum": "6b6518e2b5c070cf742d872ee0f4f92d69eac1af", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-5.0.0.tgz", "fileCount": 41, "integrity": "sha512-FRKsF7cz96xIIeMZ82ehjC3xW2E+O2+v11udrDYewUbszngYhsGa8z6YUMMzO9QJZzzyd0nGGXnML/TReX6W8Q==", "signatures": [{"sig": "MEYCIQDCO6TMg6HJQ1gWs358/m6vJnSoxNOazm8CXqS6JFgJygIhAP54FWqqiZNA7Yy+C77RK5d+Fk8S2Qas+MY9f5g+NAM1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 224687}, "engines": {"node": ">=16"}}, "5.1.0-rc.0": {"name": "tough-cookie", "version": "5.1.0-rc.0", "dependencies": {"tldts": "^6.1.32"}, "devDependencies": {"jest": "^29.7.0", "vows": "^0.8.3", "async": "3.2.6", "eslint": "^9.9.1", "globals": "^15.8.0", "ts-jest": "^29.2.2", "ts-node": "^10.9.2", "prettier": "^3.3.3", "@eslint/js": "^9.7.0", "genversion": "^3.2.0", "typescript": "5.5.3", "@types/jest": "^29.5.12", "@types/node": "^16.18.101", "typescript-eslint": "^8.0.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "@microsoft/api-extractor": "^7.47.2", "@microsoft/api-documenter": "^7.25.7"}, "dist": {"shasum": "9d36d4c3e230316e7bbdef84e4bfa4c622c9cbb7", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-5.1.0-rc.0.tgz", "fileCount": 41, "integrity": "sha512-DGT34m1GwNGWor4+fn7RwYYZ2O0DkAkkjteHj355Vmnto7JXUvKxK1o3DJolNm6NuzQ+bX2rqpQOu+Z3A4e7aA==", "signatures": [{"sig": "MEYCIQCFIcmoM1dIUPUFn0laeBfjihByNiq9+3SAX4FE8nD5tgIhAM/YXHPMyJNhiHtHd0FnW4liH5iguRjlZPOrWlqxsTh0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tough-cookie@5.1.0-rc.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 227329}, "engines": {"node": ">=16"}}, "5.1.0": {"name": "tough-cookie", "version": "5.1.0", "dependencies": {"tldts": "^6.1.32"}, "devDependencies": {"jest": "^29.7.0", "vows": "^0.8.3", "async": "3.2.6", "eslint": "^9.9.1", "globals": "^15.8.0", "ts-jest": "^29.2.2", "ts-node": "^10.9.2", "prettier": "^3.3.3", "@eslint/js": "^9.7.0", "genversion": "^3.2.0", "typescript": "5.5.3", "@types/jest": "^29.5.12", "@types/node": "^16.18.101", "typescript-eslint": "^8.0.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "@microsoft/api-extractor": "^7.47.2", "@microsoft/api-documenter": "^7.25.7"}, "dist": {"shasum": "0667b0f2fbb5901fe6f226c3e0b710a9a4292f87", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-5.1.0.tgz", "fileCount": 41, "integrity": "sha512-rvZUv+7MoBYTiDmFPBrhL7Ujx9Sk+q9wwm22x8c8T5IJaR+Wsyc7TNxbVxo84kZoRJZZMazowFLqpankBEQrGg==", "signatures": [{"sig": "MEUCIQCTfRH/dlcWhoj0Cz3JI9x6Ky7gnwPAXEerrXVyf73OKQIgEs9QM2rgnMnSJEHBtml50rP7br2fo6CK8+OikfjwjGY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tough-cookie@5.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 227531}, "engines": {"node": ">=16"}}, "5.1.1": {"name": "tough-cookie", "version": "5.1.1", "dependencies": {"tldts": "^6.1.32"}, "devDependencies": {"jest": "^29.7.0", "vows": "^0.8.3", "async": "3.2.6", "eslint": "^9.9.1", "globals": "^15.8.0", "ts-jest": "^29.2.2", "ts-node": "^10.9.2", "prettier": "^3.3.3", "@eslint/js": "^9.7.0", "genversion": "^3.2.0", "typescript": "5.5.3", "@types/jest": "^29.5.12", "@types/node": "^16.18.101", "typescript-eslint": "^8.0.1", "eslint-plugin-import": "^2.31.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.1", "@microsoft/api-extractor": "^7.47.2", "@microsoft/api-documenter": "^7.25.7", "eslint-import-resolver-typescript": "^3.7.0"}, "dist": {"shasum": "4641c1fdbf024927e29c5532edb7b6e5377ea1f2", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-5.1.1.tgz", "fileCount": 41, "integrity": "sha512-Ek7HndSVkp10hmHP9V4qZO1u+pn1RU5sI0Fw+jCU3lyvuMZcgqsNgc6CmJJZyByK4Vm/qotGRJlfgAX8q+4JiA==", "signatures": [{"sig": "MEUCIQCp2EuNsKUEbxXoDICrrll2cT3eIN5cyC2SWVOizIRhMQIgUWtsOLIIvkBD/jiT/jKHevkcUsCagHsqO4Vp2BX270Q=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tough-cookie@5.1.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 227729}, "engines": {"node": ">=16"}}, "5.1.2": {"name": "tough-cookie", "version": "5.1.2", "dependencies": {"tldts": "^6.1.32"}, "devDependencies": {"jest": "^29.7.0", "vows": "^0.8.3", "async": "3.2.6", "eslint": "^9.9.1", "globals": "^15.8.0", "ts-jest": "^29.2.2", "ts-node": "^10.9.2", "prettier": "^3.3.3", "@eslint/js": "^9.7.0", "genversion": "^3.2.0", "typescript": "5.5.3", "@types/jest": "^29.5.12", "@types/node": "^16.18.101", "typescript-eslint": "^8.0.1", "eslint-plugin-import": "^2.31.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.1", "@microsoft/api-extractor": "^7.47.2", "@microsoft/api-documenter": "^7.25.7", "eslint-import-resolver-typescript": "^3.7.0"}, "dist": {"shasum": "66d774b4a1d9e12dc75089725af3ac75ec31bed7", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-5.1.2.tgz", "fileCount": 41, "integrity": "sha512-FVDYdxtnj0G6Qm/DhNPSb8Ju59ULcup3tuJxkFb5K8Bv2pUXILbf0xZWU8PX8Ov19OXljbUyveOFwRMwkXzO+A==", "signatures": [{"sig": "MEUCIGFarTXljj47LpUT1Q4IBND0NiHP9QPQKBsaSQAsVSS0AiEAh5LJWLi6BcsNEA2g81q1VGkFI+TIYiXg7nrznBfDlo0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tough-cookie@5.1.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 227724}, "engines": {"node": ">=16"}}, "6.0.0-rc.0": {"name": "tough-cookie", "version": "6.0.0-rc.0", "dependencies": {"tldts": "^7.0.5"}, "devDependencies": {"@eslint/js": "^9.24.0", "@microsoft/api-documenter": "^7.26.20", "@microsoft/api-extractor": "^7.52.3", "@types/node": "^18.19.79", "@vitest/eslint-plugin": "^1.1.40", "eslint": "^9.24.0", "eslint-config-prettier": "^10.1.2", "eslint-import-resolver-typescript": "^4.3.2", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.6", "genversion": "^3.2.0", "globals": "^16.0.0", "prettier": "^3.5.3", "typescript": "5.5.3", "typescript-eslint": "^8.29.1", "vitest": "^3.1.1"}, "dist": {"integrity": "sha512-HT9g/ttJnTxeqHtiEQvcJlftobAsjhNyNhY/S0SIYmE/U6VBoL4gvULFDzQ6Z4zvirG1nYvqLy62Jd80YqBaEg==", "shasum": "8e5a22714d553d95de6b5d7a6247f2dd826c5548", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-6.0.0-rc.0.tgz", "fileCount": 43, "unpackedSize": 225735, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCWXdbeByltrEIaqtzz5KzCaeOZLP1pzXOlHTNqfCOE3gIhAN+9TjDVmQCC8A50234tY8CYaeHOBjAa71vYtUQF+Nfh"}]}, "engines": {"node": ">=16"}}}, "modified": "2025-05-02T13:50:43.367Z"}