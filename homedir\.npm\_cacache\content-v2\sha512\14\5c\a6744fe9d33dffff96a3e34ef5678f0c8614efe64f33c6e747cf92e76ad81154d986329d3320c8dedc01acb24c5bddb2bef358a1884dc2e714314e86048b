{"name": "is-plain-object", "dist-tags": {"latest": "5.0.0"}, "versions": {"0.1.0": {"name": "is-plain-object", "version": "0.1.0", "devDependencies": {"verb": "~0.2.6", "chai": "~1.9.1", "mocha": "*"}, "dist": {"shasum": "3ca7db022de72fd12007f1957beb59ea596b979c", "tarball": "https://registry.npmjs.org/is-plain-object/-/is-plain-object-0.1.0.tgz", "integrity": "sha512-pb50PSj2wtnl3sxe0uy8851298LRiZygwy48zRPwSIINOvDIZvO35ndeNTxFdNz5ilaWL37utyr3i+p9EeO4Sg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFBY+v+OxDBhePJsE3dhUng0cYcv++gsAwCrICj131zUAiBUACmOgjjP2rdqqHHzX1jYpwfsRNnxyN0/BrmQQqX8hQ=="}]}, "engines": {"node": ">=0.10.0"}}, "1.0.0": {"name": "is-plain-object", "version": "1.0.0", "dependencies": {"isobject": "^0.2.0"}, "devDependencies": {"mocha": "*"}, "dist": {"shasum": "ff5f752db71c3328afd5e685eb6adddd3eaffab7", "tarball": "https://registry.npmjs.org/is-plain-object/-/is-plain-object-1.0.0.tgz", "integrity": "sha512-+tXodG6I903VrzqxqKAQjA7ti9niaR18pza7uWPLmUvQfsUk+WzNwnu4JnW7UNT3idAcNTGMjZtvdrYTJnWkUA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDzV2dnUoXi6ImXNKwg8orNM1gQTj07FcytTd1KgLOOBgIhAI33xZyqllAHWXsuR+cUlo2WvgXIDFWLareCSEzUi5EG"}]}, "engines": {"node": ">=0.10.0"}}, "2.0.0": {"name": "is-plain-object", "version": "2.0.0", "dependencies": {"isobject": "^0.2.0"}, "devDependencies": {"browserify": "*", "chai": "*", "mocha": "*", "mocha-phantomjs": "*", "phantomjs": "*", "uglify-js": "*"}, "dist": {"shasum": "8612587fa90279dc1b6e1cec2056f6c1df7abb2a", "tarball": "https://registry.npmjs.org/is-plain-object/-/is-plain-object-2.0.0.tgz", "integrity": "sha512-HwcxEMZP726Fk8+02WDquQHSd95nifNqIvT23+FfQh4LxW+spxD/E+ZLe5GvyCsDEjc/sH/8kWNeeHfajlR+7Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE+9gh7ASqgOtSc3k8T4iG3fKiRnWs/j/UEO0wAR4o8mAiEA2j03cH7/rG2/L/RxYv5RfAd9Gtafe0F7Lbkk9E/SwrI="}]}, "engines": {"node": ">=0.10.0"}}, "2.0.1": {"name": "is-plain-object", "version": "2.0.1", "dependencies": {"isobject": "^1.0.0"}, "devDependencies": {"browserify": "*", "chai": "*", "mocha": "*", "mocha-phantomjs": "*", "phantomjs": "*", "uglify-js": "*"}, "dist": {"shasum": "4d7ca539bc9db9b737b8acb612f2318ef92f294f", "tarball": "https://registry.npmjs.org/is-plain-object/-/is-plain-object-2.0.1.tgz", "integrity": "sha512-F/FLn/qy/gwj4RWlHhdO+lc1ACqkIPqwxP9TlnsNJerkqvFNLlTQoos+1MxIklqdwGutrmzV6SUPiDrkiBpB6g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD1Pal0Ari8VY/hYL6+UKzQHSmV5S1FXxR93uujnv6SpAIhAPRlK8UKxhA+/1sH9owDJ90OWMw/RssRQP2QdL+WtnCI"}]}, "engines": {"node": ">=0.10.0"}}, "2.0.2": {"name": "is-plain-object", "version": "2.0.2", "dependencies": {"isobject": "^3.0.0"}, "devDependencies": {"browserify": "^14.3.0", "gulp-format-md": "^0.1.12", "mocha": "^3.4.2", "mocha-phantomjs": "^4.1.0", "phantomjs": "^2.1.7", "uglify-js": "^3.0.12"}, "dist": {"shasum": "1d9ab795669937de31998071ca1f701770b375a4", "tarball": "https://registry.npmjs.org/is-plain-object/-/is-plain-object-2.0.2.tgz", "integrity": "sha512-0LshiloqaVPchDPbWL/5j2pNXuveiywMgjSXzB8IPYx22XnWBuOoQr9yoAZvw1XUEwRDzejOnX0nELlObuKaxA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCuYXWLb/8Y1pPbP+a4N2LsbQD2l15n/YVhNrqVV6zwZwIhAIDXQEYVmMEEEonjH0dVp1XGzhl4YciEIMLSoQzMCD6N"}]}, "engines": {"node": ">=0.10.0"}}, "2.0.3": {"name": "is-plain-object", "version": "2.0.3", "dependencies": {"isobject": "^3.0.0"}, "devDependencies": {"browserify": "^14.3.0", "chai": "^4.0.0", "gulp-format-md": "^0.1.12", "mocha": "^3.4.2", "mocha-phantomjs": "^4.1.0", "phantomjs": "^2.1.7", "uglify-js": "^3.0.12"}, "dist": {"shasum": "c15bf3e4b66b62d72efaf2925848663ecbc619b6", "tarball": "https://registry.npmjs.org/is-plain-object/-/is-plain-object-2.0.3.tgz", "integrity": "sha512-4QumWYGOyh/PBXAjqTavDrW61AXQMo9MddhHneiqHZkeql0uKV6ytfEPQuZFNf/hoaRi8ooNYFvQBC5tI3i8xA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFlolFswB26pz+eiUPbf2z2yELBC5VTgXVyEJwpYQ3J6AiEAr3IYV2wgKEO5wv0Z89FP0KAilxSpBZaYAysW97AvgUc="}]}, "engines": {"node": ">=0.10.0"}}, "2.0.4": {"name": "is-plain-object", "version": "2.0.4", "dependencies": {"isobject": "^3.0.1"}, "devDependencies": {"browserify": "^14.4.0", "chai": "^4.0.2", "gulp-format-md": "^1.0.0", "mocha": "^3.4.2", "mocha-phantomjs": "^4.1.0", "phantomjs": "^2.1.7", "uglify-js": "^3.0.24"}, "dist": {"integrity": "sha512-h5PpgXkWitc38BBMYawTYMWJHFZJVnBquFE57xFpjB8pJFiF6gZ+bU+WyI/yqXiFR5mdLsgYNaPe8uao6Uv9Og==", "shasum": "2c163b3fafb1b606d9d17928f05c2a1c38e07677", "tarball": "https://registry.npmjs.org/is-plain-object/-/is-plain-object-2.0.4.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE5xlLxkVCwt4n8oWPscuhWZsIE3aR2y0PyyzU5T+0ztAiEAgqSebo53ZeAAXDTOVEWqpSasxMpfZWaBgDvVfBr4WJA="}]}, "engines": {"node": ">=0.10.0"}}, "3.0.0": {"name": "is-plain-object", "version": "3.0.0", "dependencies": {"isobject": "^4.0.0"}, "devDependencies": {"chai": "^4.2.0", "esm": "^3.2.22", "gulp-format-md": "^1.0.0", "mocha": "^6.1.4", "mocha-headless-chrome": "^2.0.2", "rollup": "^1.10.1", "rollup-plugin-node-resolve": "^4.2.3"}, "dist": {"integrity": "sha512-tZIpofR+P05k8Aocp7UI/2UTa9lTJSebCXpFFoR9aibpokDj/uXBsJ8luUu0tTVYKkMU6URDUuOfJZ7koewXvg==", "shasum": "47bfc5da1b5d50d64110806c199359482e75a928", "tarball": "https://registry.npmjs.org/is-plain-object/-/is-plain-object-3.0.0.tgz", "fileCount": 6, "unpackedSize": 9185, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcyLXkCRA9TVsSAnZWagAAd2IP+wfkoxXYjoEkwNWIUrfy\nzjhbQ5xnTwlZuNoPWZuVdQSYDwufCyh/zWeuiJjsCEkNT/z0QegPdK4PZFIi\n6BNti78yVazpcsonD3njnmoyrV5/8iXyI+C1PRo6nOO6kNHXx0JjAoLRAoVC\nDguYquTDhdo2WlQJ01ln8dSx9ueWQx5QMLE9lBXPzYmrtXbQcLd5Z8uXE4P/\n0vrkIuteL/p7I5RF2iFYjz4sEShk9W+ZNqlAf8Xsaa7yT2B5RRiy/ron5/+g\nLrnPAPn4954fWupxRuivAqgj3CfoOxx6Pl1D+ksK6sNywn6zB2fqTQm8vSLe\nsh6+BrX4ML34PA+2GSMmsv2+pU9Ksc2MwVJYzvIRQYlxMIza6rSMpfoJFiqW\nbiGyVf5+NSozNOnNpqxrv2OYIZnsKk+4ZL8kM5VrMnMAtB0IO8dgPO7uG/pf\n/CpqXJ9b9YKit5JnvuVksepqBIj+j2e40Od4E/wjsNKrIN0J06NksaVTmexo\njebkKSgAqGqqTPeeShtj44dF+Mdu8ChtBE5NYPLzJMDK5gslVb3/IdICzAwg\ncUXCgDR7KfoSio/4bCBB3sx+B4rCwcP3Eoi139qrQeXnl/OVCgINbd7U0u4A\nNIBq4uhTTb7ylFxJAcDBGPfqohPzoUc93pj+tClmPfF/+Bl7KBjZPKdhFI9u\nudtk\r\n=3ByD\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFC4SrcnGkCU7NJNGUV9QLoPRhia1KiX3QwXhJIJWgfLAiEAokEdJd/9ak0gHre5uHzqbfOXJAFQiuCsBgzeoQDRBPM="}]}, "engines": {"node": ">=0.10.0"}}, "3.0.1": {"name": "is-plain-object", "version": "3.0.1", "devDependencies": {"@rollup/plugin-node-resolve": "^8.1.0", "chai": "^4.2.0", "esm": "^3.2.22", "gulp-format-md": "^1.0.0", "isobject": "^4.0.0", "mocha": "^6.1.4", "mocha-headless-chrome": "^3.1.0", "rollup": "^1.10.1"}, "dist": {"shasum": "662d92d24c0aa4302407b0d45d21f2251c85f85b", "integrity": "sha512-Xnpx182SBMrr/aBik8y+GuR4U1L9FqMSojwDQwPMmxyC6bvEqly9UBCxhauBF5vNh2gwWJNX6oDV7O+OM4z34g==", "tarball": "https://registry.npmjs.org/is-plain-object/-/is-plain-object-3.0.1.tgz", "fileCount": 7, "unpackedSize": 9457, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe9v38CRA9TVsSAnZWagAAkbEQAJ2Xlwabzov0sVASU7tL\nzQZeGLp+nSkO6NsxaLstwPvOlcwwVyvguew4itIVLHJJ1Hc6c25OejfbJ8j2\nXvOqFWdmtBZovDMD0LzhLe9mEdE7dyLQeuNqcNS94GTaXi0LLRVcK1GxQEM4\ntD30mx2iEdgW7chgYmm/UD1jiRkiyMA8jOt2Cf76+dTxtkqXGAz4hoI6wsId\noH1274RbdaCjEqBmcfWWaWkhAdrb2B3XoWRkdyFAlG/t7kmAUPJ8hS/ztHal\niLCI9uoHs0RLYE+dOe4URvsJi/GJD8pmXVBwr1vWsHJoklWeTly/segTpzu0\njUNRjmNkYPtQq2gh/RVVkzAKWn1A9dQQqaEEp6HsYSteHgS8UVmcffiHMDtB\nak1sgV5wCGYmcB94r6v8wyZXKIztt+ouGJAmCqiSGE/tRxjwSJtvjmnbhNzV\n19+UmP3m/Yy/dQxW2pvRfSMttTWmJo6aGB4n4Y1WwiCDQ0bn3Ij0kQBgy1sf\npz/0yIEfi8Sk1NTRxYhaQ7Q4nztKbui7168X7CWgKetmF2eO4lDAfwArTqrZ\noV5TDctShs6mMWChaIYEB0f39ubmZjjwWArINB1k8sRoicULfiYRtZ6q3sSP\nC5P9ajP2fh8HYyn7C/dalPzirSFx+rT8AY6F34PoGqgg8uY7gS05ZmJTDSlW\nEV/9\r\n=X+uo\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCJQEDF9zTAZyiFeVOQ8eYMHxjx6eRVmXZhV8afoy/XwwIgPyj8lSg+Tj/sk5WR6Dz2xrObpQ3VafDmaPmBxrUVO3M="}]}, "engines": {"node": ">=0.10.0"}}, "4.0.0": {"name": "is-plain-object", "version": "4.0.0", "devDependencies": {"@rollup/plugin-node-resolve": "^8.1.0", "chai": "^4.2.0", "esm": "^3.2.22", "gulp-format-md": "^1.0.0", "isobject": "^4.0.0", "mocha": "^6.1.4", "mocha-headless-chrome": "^3.1.0", "rollup": "^2.18.1"}, "dist": {"shasum": "2cd131aa2dc1340ceaee89248f61823706457feb", "integrity": "sha512-WipTQmPd1scuJUgwV/EZ1QbbOpEyHTQEXz4cJvrn+uwwgUtPcd3FD+yvAcKLYA8so2UsjNAq1JBu9rGgmGguVg==", "tarball": "https://registry.npmjs.org/is-plain-object/-/is-plain-object-4.0.0.tgz", "fileCount": 7, "unpackedSize": 9586, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfFjJRCRA9TVsSAnZWagAArSEP/0zl2FpPkI11JRXiXaq+\nmc5ciKkpKcuyOFga+lOoKPGM09m0xTFPIhNC968UtoGeyW+vw983F+hHsD3J\nQ+CibN7jmPUYASKdziI5iZj1/I2v2WNXQNPat3XTZEn2ODqzXaV+TVWMneO8\nNDCIXlCm/la8bWMK5BjEWxzybgPSnRNEw+oDOe8+XMwNJU8TO5XmWwOrtYnb\ndGE532K842tgVMRclfhWJVmK0PG9R+DNleiwZyFpEJYF2loZk1SHzMeFrdaW\nMN2rvGXrlNOcFaEnoDhtyJxzB/tOM5he+L1jo2QiRpTMF5BzA8U2Ch5d8+7/\nyO317xt0rmP5SHMEx2WJQ5t6Tjx5AuB3bkZBgJiQekvSTnUXPF2/dicgMbse\nCt8ulAIXWPvAwjNyLvM/FLFbr4w/NxuHwslKQwrYKXvkxQ11aVU1ENfnNzJg\n+XeduOHN+vSeFF3b1RKeV0AKqVaK5GfjHumZwYDqhMTLFy2bX60P7cpw5cVL\ntGk2cMPclT5hp9PT+/A8AqYbX1J4/6QENjqmfklJlmhdbm+vQIUXtr4Ratbx\nSElqBUNxiuj2H6UAiOfJZFOp+XZfiLmSvqMsJLoswe9pWJMBA6xxmFmL5qRW\nyjIeyxz/uY4gXlZSWVXgdR57hwd8HUN8Igyaepz8ulQ4zawyoPxOjPwNOHmJ\nITUZ\r\n=kqQc\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEUgfdCT4SIGnNA30Hu1QTtct1otgrP98p3wd3ZD0hl2AiB1CZGFUV28djSRpC5U3ahrZP8LkrU2hJtFSqHBAoG2vA=="}]}, "engines": {"node": ">=0.10.0"}}, "4.1.0": {"name": "is-plain-object", "version": "4.1.0", "devDependencies": {"chai": "^4.2.0", "esm": "^3.2.22", "gulp-format-md": "^1.0.0", "mocha": "^6.1.4", "mocha-headless-chrome": "^3.1.0", "rollup": "^2.22.1"}, "dist": {"shasum": "00196ad308ebb7de9d1fb57ae92ef1c38d5a740e", "integrity": "sha512-1N1OpoS8S4Ua+FsH6Mhvgaj0di3uRXgulcv2dnFu2J/WcEsDNbBoiUX6mYmhQ2cAzZ+B/lTJtX1qUSL5RwsGug==", "tarball": "https://registry.npmjs.org/is-plain-object/-/is-plain-object-4.1.0.tgz", "fileCount": 7, "unpackedSize": 8848, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfFsqcCRA9TVsSAnZWagAAyIcP/2EOxdbMmCMKyWJOsPse\n+s6cj5zx8u0p6ZPg1MrXp8am9hNfZ12NIyuCUuJ/3vVfHJeAiP+sSNWrlUuJ\n3fyKi7AKddLkUarOU9h6hnNxp0K5CGMrfTPqsnG1HP6Z2gRinOr9CgHOjbPF\nRYbECybxV3uAv4JivKxfgNN+8UaZwn95LnA5DY+oq7/leR2dxnlSS3rh3UVS\nr/kGOqrb2PloXYPPPcO3nJsZWfboApgArqUkVENUPGIzIYiibCsMty/F7rIo\nnZPibVN5Rsm5ZbAtzxMRm8v1S9nHbfC5HEMtTNtkc0f/4LwFHO+G7KxyAMqK\ncTRHdOw4UQM5qUDggxWalf3DoBWA8jwxtJ2gHca+nTPExG7OOOh3x0qJUGBU\nnoRAe87DilT9uzgGVghgOPAoDTVXHAIwcwV6xab0hKN1kMGE3j9yNazwX16s\nFdCDHu8OX0k18bDz23xrKWw4TRrYU75RseQHkazRRr+mq9JzTzu03Ra3eEcS\n5RlOSz0/IV5ZlqooPLqMUxeTKF/fW1pwdpzQUXKoPze8nLAMqqh/fyNHEI0w\nc1uS3qTST24pNv6cCU0j3H7LaWjM5V1zvXolJeDmufl928DLweGnO6vaotQ5\nNQGi6bJzKXUxqzbpOWlaHSbaZrP+8hT/vI8jeM09ifsCKMJxSgw9peTLNQ3x\nF6p3\r\n=5d0P\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCs6nxgLJtbQAdT41zUF4T8z4F8LsdkvkSAlf33k7yyCAIhAKVb+gbr8kW4lmUnrZ6StinyM8TRSoRXbfoR/bbp7Xrh"}]}, "engines": {"node": ">=0.10.0"}}, "4.1.1": {"name": "is-plain-object", "version": "4.1.1", "devDependencies": {"chai": "^4.2.0", "esm": "^3.2.22", "gulp-format-md": "^1.0.0", "mocha": "^6.1.4", "mocha-headless-chrome": "^3.1.0", "rollup": "^2.22.1"}, "dist": {"shasum": "1a14d6452cbd50790edc7fdaa0aed5a40a35ebb5", "integrity": "sha512-5Aw8LLVsDlZsETVMhoMXzqsXwQqr/0vlnBYzIXJbYo2F4yYlhLHs+Ez7Bod7IIQKWkJbJfxrWD7pA1Dw1TKrwA==", "tarball": "https://registry.npmjs.org/is-plain-object/-/is-plain-object-4.1.1.tgz", "fileCount": 7, "unpackedSize": 8836, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfGoXUCRA9TVsSAnZWagAAYKcP/3EnkUPC8tUbqR/CUOpH\nUYQhWAnCjNfSacMHUehZNN9QiauM8Rxcwl8n3/J19kzqt0nyT9wgWiu8+R+F\nHvTJlJyQ0ZbFdiLhE1gm1nx0/OU96OKJi7SkQtZFCRz0eT4tkNA0vVS8uTmT\nS1/LtCzCnpMNB7Iq3V/9RVmkUOV0sox9RiNXDle+aa3T2B87fJwfUzptKATZ\n5OcjbA6qqmECGPmDLNPFdpH9isfyWMUsvHRYGn55fwP38g/yMjZupFtiqWPJ\n22R2jMSTg/ni5yyTpEQlkjH/o2kaqmlrJsgaLX4jbK+hYXKHQzg0loE/0thi\nDnktO4ywGcdQpZnFn3L/WuAMwauAca81Otd4FfwvOCW8EiNhcXTk4EVpCs/E\nhgE+U5ClvIAW5c7SLBQhVV0P2FuaBUZ0cC9rWaA0i1ySPROKFsrcDaCAuS80\nFZ5NClxpLEO0+mosTZ/mAtGLeMHrLlmikizvmHXso2VJIoidmybc78/ZpOcO\nBzroS6/laUWhnRsvV1M0C/hOfluFg34P+egb/VcVnbskjRMaRI6Ra+0h/L5p\npv8tzLFLYmq1J/ggPd6yNrOwS3QGzE0ZuICo6hAXFETVKY7iLB0N471yScM5\nYaB8+luUuAVglF32P6SkyKp2mrqrfIvVoggzmamhodICoU3aLY/UiG6CdLny\nSt+i\r\n=rLPi\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC/YtEbBvZKERT2sxKDHXaQBz5wgcJhLf7FXZ7/9eynPQIhAOzLP2XIWOqGMkz56Osf19VbuqQ0c18vlFeLPCx3LVsS"}]}, "engines": {"node": ">=0.10.0"}}, "5.0.0": {"name": "is-plain-object", "version": "5.0.0", "devDependencies": {"chai": "^4.2.0", "esm": "^3.2.22", "gulp-format-md": "^1.0.0", "mocha": "^6.1.4", "mocha-headless-chrome": "^3.1.0", "rollup": "^2.22.1"}, "dist": {"shasum": "4427f50ab3429e9025ea7d52e9043a9ef4159344", "integrity": "sha512-VRSzKkbMm5jMDoKLbltAkFQ5Qr7VDiTFGXxYFXXowVj387GeGNOCsOH6Msy00SGZ3Fp84b1Naa1psqgcCIEP5Q==", "tarball": "https://registry.npmjs.org/is-plain-object/-/is-plain-object-5.0.0.tgz", "fileCount": 8, "unpackedSize": 9158, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfWQMLCRA9TVsSAnZWagAAbRYQAKQJLFQphXNDxc6H/B1u\n9dBvAqV0kSdebj3S054eyEsQXjJ+WZ2GTQQEiwLenikkNNDUmOkbZxNm2jtw\nzmaZM+RxrYgG8CkDvPO2Iq0KHR5RwbPi6JIiSG/mGb3NkFwCXjdJzFQCujrp\no9fWdALUFfEc3umG4OH4pz2wUkP8zU6RHRfo9lOtEE3zqkyLqHbPSPwfLGcA\nmEmsSPSTFg4+12l7iN1B7UFsyDuLlA2m/t/CLQaPBuum1jjjJpul3Eb4mV2q\ntsi9s8L63EafxM17xR8OQoGowAxw4q3gAADEtx/oR4jld8n2vu3TKDCeG0CP\nlZAEX8psMnqrx+CAiHY2NrC5sXKXx3z6e/h6g0MlJ74wlYZRZYLro+vQDJLM\nOHwfMcGzRKKz/ztHwMl5OaCIx1jURFK8YX0+VZi/VEmNom/LX9tUcpJm8heo\n+4uPTCo78K02AGlcvxNhpPwOI0KfgeZxVQff/D9ZU3AlBqTOqpzVsMAvNswI\nSCus5AV8kwBPyI2IIRZkmlJ5I5S0OS7CUlpJ7QFtaqQmHlq1uxZ2OF3Onrq+\nQa4HxsDPq1T1q1wGMnUJPGZKP1wWTSmQ94Uehz0IN4p7qo6+ifBr5nq6xpxr\ny4eALo3qYuHnQ0amaiOESVMw4zVbAh1f3d6Scp8uyjT22utICnNO0dioz5Sx\nM9nC\r\n=QWWU\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCk7Geh9U3CeUw2l/8l+DV8BI0RwR8Y86gVNGYF3v/mCwIgAJVlWH+CzM1p/dJIDN3IlN0Yg39kZwuJk3qNXX794vQ="}]}, "engines": {"node": ">=0.10.0"}}}, "modified": "2023-07-12T19:12:32.135Z"}