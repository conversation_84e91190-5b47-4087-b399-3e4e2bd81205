{"name": "express", "dist-tags": {"latest-4": "4.21.2", "latest": "5.1.0"}, "versions": {"1.0.1": {"name": "express", "version": "1.0.1", "dependencies": {"connect": ">= 0.3.0"}, "bin": {"express": "./bin/express"}, "directories": {"lib": "./lib/express"}, "dist": {"shasum": "53ad8442c3feb46588f08698f1872c4dbf24137f", "tarball": "https://registry.npmjs.org/express/-/express-1.0.1.tgz", "integrity": "sha512-JHRGK6BRZSNy4i/9kzk7R8l0IlyVdpAvA6421fgkEqpLpZqO1prIbgXe5i9l6o8XI8krYI1euecawL1jzv5yeQ==", "signatures": [{"sig": "MEUCICcPcOan8O27LA8LyDzu6Oq5sMQB/MldUgIzGU3Ui/psAiEAlDf7/FIzTmbGyJ7I6JFKXAqYS2pYv1UVIl5KVwWi850=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.2.0"}, "deprecated": "express 1.x series is deprecated"}, "1.0.0": {"name": "express", "version": "1.0.0", "dependencies": {"connect": ">= 0.3.0"}, "bin": {"express": "./bin/express"}, "directories": {"lib": "./lib/express"}, "dist": {"shasum": "48a43d78a96eb9232f631d23cc8de8f854d8e0e9", "tarball": "https://registry.npmjs.org/express/-/express-1.0.0.tgz", "integrity": "sha512-AN8xILDGhFQ9NbbEs2ebaqTFbjaF3ycdd1mOvlfZJoSZ8f/KMqPPKNuN6JX3KQYT2oIPPQvyGQZU4cwHkavgMA==", "signatures": [{"sig": "MEYCIQDCV9SskQHC3nGigkCApLMvGIwg4z8eyOXJQhmH0Nq2SgIhAJddBia9AOVOVXY1UjqK4L0WtaklA4IusbBy9Df81U8e", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.2.0"}, "deprecated": "express 1.x series is deprecated"}, "0.14.0": {"name": "express", "version": "0.14.0", "directories": {"lib": "./lib"}, "dist": {"shasum": "7b33a9fb54c605a3be46c1d3dbbc821acf1d2efb", "tarball": "https://registry.npmjs.org/express/-/express-0.14.0.tgz", "integrity": "sha512-ULazYLF3/YqOU5rzkviWJEd4TNZ0j77Nymuqa1+sQe0dhxcsDzKOQK8GemM9S3i8x2Q55GWXhnhRHwYaJIrM1g==", "signatures": [{"sig": "MEYCIQCGihQqfDiARxIfslKVGe5vzfxCGOh+vhulZER4lO9oBQIhAO2VvApOYRVumT9XVDffvpYimysO/Hm1Qd59+KBublcM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.1.98"}, "deprecated": "express 0.x series is deprecated"}, "0.14.1": {"name": "express", "version": "0.14.1", "directories": {"lib": "./lib"}, "dist": {"shasum": "40b0119ea0549892b03b5bb56c79cdff468d04b4", "tarball": "https://registry.npmjs.org/express/-/express-0.14.1.tgz", "integrity": "sha512-Hjpy5WUGnlZEx05p2f8h4jiIXoijUmwAcGeRyNRvG/Z0M54GG5KIopCaMQBCBGCwSYzUdxYt2pnTHRk+RzoSYg==", "signatures": [{"sig": "MEUCIQDDMYWVRarLQDdVxEFb8vC9TPyO0jkrbzCcZCFhpESpRQIgJMXEWyuJ/r2KKM3T9C9gRB5EReaQx8A5+Fklw9uVops=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.1.98"}, "deprecated": "express 0.x series is deprecated"}, "1.0.2": {"name": "express", "version": "1.0.2", "dependencies": {"connect": ">= 0.3.0"}, "bin": {"express": "./bin/express"}, "directories": {"lib": "./lib/express"}, "dist": {"shasum": "5985fd1986b2275d8e96976a8b8de011dc823e0d", "tarball": "https://registry.npmjs.org/express/-/express-1.0.2.tgz", "integrity": "sha512-yhoqrwlcmKFsCnP2cEwDrTuZG/g8DivcEd4J9SiI+8eE0ke8mN/TxqFcnuFVptVlZSakGLphGJjsSsP6wbJNLA==", "signatures": [{"sig": "MEUCIQDktTHoqwKvwgeNOjCpP9TS+6rOns0e112lHDGc8KmH7wIgSu+03Awjzbf943ZHrRuxwKkeZ7fbO4qn1oOmDDAONcw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.2.0"}, "deprecated": "express 1.x series is deprecated"}, "1.0.3": {"name": "express", "version": "1.0.3", "dependencies": {"connect": ">= 0.3.0"}, "bin": {"express": "./bin/express"}, "directories": {"lib": "./lib/express"}, "dist": {"shasum": "e07fd860c4af7ffddc77653fd1fd930fce26cb61", "tarball": "https://registry.npmjs.org/express/-/express-1.0.3.tgz", "integrity": "sha512-cgUCj5FDE0tMDT1WBDu1rZpy1D584ctng4Q6jogPGMnLuKPpz18UIPqaTKOBJayxL752Mb/R0qWQ38KgmjOXAw==", "signatures": [{"sig": "MEUCIQDuY8dYLK7VF7scm9NPnelQVxaCWyR8zBDsGMKXIJUDvAIgAN3oVC6KZPX7a85R6p82f+w01oJD0KrEIQQvTZ7RoME=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.2.0"}, "deprecated": "express 1.x series is deprecated"}, "1.0.4": {"name": "express", "version": "1.0.4", "dependencies": {"qs": ">= 0.0.1", "connect": ">= 0.5.x"}, "bin": {"express": "./bin/express"}, "directories": {"bin": "./bin", "lib": "./lib/express"}, "dist": {"shasum": "fab80c530d40b04f4f558f7f03b2cbf0f9040b14", "tarball": "https://registry.npmjs.org/express/-/express-1.0.4.tgz", "integrity": "sha512-qzQZ0MFFYH2998kqOaM4/cGZsMQE+9Cj/TKOdjWE2Mu3j2LvkHQqVaMv20UVTqgH/i6vDXb/xG/nh3TKzLlJxw==", "signatures": [{"sig": "MEUCIQDncd4Gwj2mkZiRTyDPpwjsRu71aw6SKeSQBvUoPRFewQIgIjy7/kdrQu1iHYk3cMi1KTTfE+QzST+xHJebEyuKd8I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.2.0"}, "deprecated": "express 1.x series is deprecated"}, "1.0.5": {"name": "express", "version": "1.0.5", "dependencies": {"qs": ">= 0.0.2", "connect": ">= 0.5.0"}, "bin": {"express": "./bin/express"}, "directories": {"bin": "./bin", "lib": "./lib/express"}, "dist": {"shasum": "2d32dff93a8c454e9a717c43b856c5369efc2856", "tarball": "https://registry.npmjs.org/express/-/express-1.0.5.tgz", "integrity": "sha512-5izCGDGti+/+1GFP7I9dHu1nXBllfBms+QXXugcFnTsBbPjpKOjl/t0+eux9i/Kb13ySeaE0pAuUfU8kgCb2XQ==", "signatures": [{"sig": "MEYCIQCi0wJfBC0su3b8grBipRfH+zmM/HumIspGsP7wfqg9tgIhAMSaZ/00GgExyiigfb9PiVlv4MB+X1jZFjJTP1YlOlrm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.2.0"}, "deprecated": "express 1.x series is deprecated"}, "1.0.6": {"name": "express", "version": "1.0.6", "dependencies": {"qs": ">= 0.0.2", "connect": ">= 0.5.0"}, "bin": {"express": "./bin/express"}, "directories": {"bin": "./bin", "lib": "./lib/express"}, "dist": {"shasum": "9aee1508f0e9ce4cc2eabdda94ec8793898306f9", "tarball": "https://registry.npmjs.org/express/-/express-1.0.6.tgz", "integrity": "sha512-PZbYXTqKPxAlmHzrtugx56Fa0pbG6pH7Fi5aD3NhwXnQUMNuLCnPlJ500eOIoQEfixCMAdmfOu1/sgc6Qjf9hA==", "signatures": [{"sig": "MEUCIBzulvgP0wRJivGd/FZIH7lRSXpKNFEO2uW2X/lmCZg7AiEAtCxHeAIrnQ+tXVn/GbG+qmhpy3UOez6W+xHyznP97/c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.2.0"}, "deprecated": "express 1.x series is deprecated"}, "1.0.7": {"name": "express", "version": "1.0.7", "dependencies": {"qs": ">= 0.0.2", "connect": ">= 0.5.0"}, "bin": {"express": "./bin/express"}, "directories": {"bin": "./bin", "lib": "./lib/express"}, "dist": {"shasum": "ccb14eee039e4177ce410fe5f074e96f68629e6c", "tarball": "https://registry.npmjs.org/express/-/express-1.0.7.tgz", "integrity": "sha512-HNhxDJxv02EFo0Asa3Ms71WuFQJxXRxfFmOBBlzdXEKvkn1F9gYicE/w/L6mKXqu5WjPOK947g7L8/RoGdv7qA==", "signatures": [{"sig": "MEUCIGXeU3CU2pMB08bauV0GGSWetwqTx6tHdDwIgiqw5ZCWAiEA7zYhdaDFhgmnZNKWcooMqbf1qUzIF/Mb9EBA1C2mk9A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.2.0"}, "deprecated": "express 1.x series is deprecated"}, "1.0.8": {"name": "express", "version": "1.0.8", "dependencies": {"qs": ">= 0.0.5", "connect": ">= 0.5.0 < 1.0.0"}, "bin": {"express": "./bin/express"}, "directories": {"bin": "./bin", "lib": "./lib/express"}, "dist": {"shasum": "fe254667ad612c23dd87d61180dc194cda1f7d38", "tarball": "https://registry.npmjs.org/express/-/express-1.0.8.tgz", "integrity": "sha512-5Zd5CzpkrNE6TY9+4/AqUGcsH1iGtzvlCd70Kau7f5OOeg7I2QZnkNi4vftOVMC+Z37WxsWQdJXX8mFNVr+s4g==", "signatures": [{"sig": "MEUCIQDdtgRxHyLfyiDKEkIouZ7u1863riZZegl25mqlE33Y5gIgA2mEkzo7AmxYmC8spP6ieYjrQTYtX7gRsFUtdcKaGds=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.2.0 < 0.4.0"}, "deprecated": "express 1.x series is deprecated"}, "2.0.0": {"name": "express", "version": "2.0.0", "dependencies": {"qs": ">= 0.0.6", "mime": ">= 0.0.1", "connect": ">= 1.1.0 < 2.0.0"}, "bin": {"express": "./bin/express"}, "directories": {"bin": "./bin", "lib": "./lib"}, "dist": {"shasum": "f9f715cf54e9b6f3f00115fe7e1188964d0a74b2", "tarball": "https://registry.npmjs.org/express/-/express-2.0.0.tgz", "integrity": "sha512-1DemoQhUgHTFQJwq+Ny5z/ItFIekVkNS2D4ViBSxD/5pTGOA983gxyEKJkemG0TDAeCOR4yfsdi7P7jG4VXTdQ==", "signatures": [{"sig": "MEUCIQCIiSFaOWrxmvAgGMP5bXq6P4N2lXpufDGoNTE2ghTueQIgFOAX4a3Upj+ECQ0iinTW7y3mI71dfdA9/5I25BneZfA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.1 < 0.5.0"}, "deprecated": "express 2.x series is deprecated"}, "2.1.0": {"name": "express", "version": "2.1.0", "dependencies": {"qs": ">= 0.0.6", "mime": ">= 0.0.1", "connect": ">= 1.1.1 < 2.0.0"}, "bin": {"express": "./bin/express"}, "directories": {"bin": "./bin", "lib": "./lib"}, "dist": {"shasum": "34542d68cf298d5a89d74dc1c8f96b5c4e1b00a7", "tarball": "https://registry.npmjs.org/express/-/express-2.1.0.tgz", "integrity": "sha512-DCXIC0Ip3FhmHSa6zI91xD8Rat54TDteDdyY5oVTUKQ0vfQiu3IS3+vq8oR0evKI2Kk/H+MTSMyxlAMXo3jGCA==", "signatures": [{"sig": "MEYCIQCnlyI1cbkIHe1jbsRqZkUkPIeg462wOjbysqRbV9lDYgIhALXyvNJtnVDHpcNHtqYMuiE1+7v7kkLeTKb/MoQRcP+G", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.1 < 0.5.0"}, "deprecated": "express 2.x series is deprecated"}, "2.1.1": {"name": "express", "version": "2.1.1", "dependencies": {"qs": ">= 0.0.6", "mime": ">= 0.0.1", "connect": ">= 1.1.1 < 2.0.0"}, "bin": {"express": "./bin/express"}, "directories": {"bin": "./bin", "lib": "./lib"}, "dist": {"shasum": "4ab83c3509050ef917532cdb174bc23d8a007af4", "tarball": "https://registry.npmjs.org/express/-/express-2.1.1.tgz", "integrity": "sha512-kLf0FsCA8IOdNy7DXZEmFsE5jTd1v2P5bg1Xb+V3OjNpqvEfWpuiWzia0cr1+5qcr7cBwvjDESZNguHQDDYl5A==", "signatures": [{"sig": "MEUCIEYcRX86PfpDv3sDv0U9VTnT+Qir7udjNeN+4cy6dCg0AiEAvzOf8nPTGcLsoB2DztZxLLP+Ky/eIqKik/4BVndJQCo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.1 < 0.5.0"}, "deprecated": "express 2.x series is deprecated"}, "2.2.0": {"name": "express", "version": "2.2.0", "dependencies": {"qs": ">= 0.0.6", "mime": ">= 0.0.1", "connect": ">= 1.2.0 < 2.0.0"}, "bin": {"express": "./bin/express"}, "directories": {"bin": "./bin", "lib": "./lib"}, "dist": {"shasum": "ab38a7eaad67a1c28495021a798d234086d73dea", "tarball": "https://registry.npmjs.org/express/-/express-2.2.0.tgz", "integrity": "sha512-77HicPpMXBsJ3yaZhmdr+N7uAsiHOdI5UHZIZSYVnCOCGbjZhW01DRTajxG+rDhtVx50N3Bju0Dex5fcT0+iVw==", "signatures": [{"sig": "MEUCIQC9d2hMMbRSq1zAeOgFDcuKUFRmU35eMyoPN3KKOckLNQIgM1yIDw5OmWTrRuqxOFb7qAcnZiNp90a5Hy/Fs6s0VB4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.1 < 0.5.0"}, "deprecated": "express 2.x series is deprecated"}, "2.2.1": {"name": "express", "version": "2.2.1", "dependencies": {"qs": ">= 0.0.6", "mime": ">= 0.0.1", "connect": ">= 1.2.0 < 2.0.0"}, "bin": {"express": "./bin/express"}, "directories": {"bin": "./bin", "lib": "./lib"}, "dist": {"shasum": "a4937f9d5e661282cd62d88e227132f79ccbe25f", "tarball": "https://registry.npmjs.org/express/-/express-2.2.1.tgz", "integrity": "sha512-bbNEfV2XAw6uWXCVsknKwRhFtXt+PqJ27Wfuzh3oeY25CkEgmdaREzpCtc8ORRxpG5QWdYfUR5HjqvZGPEAMtw==", "signatures": [{"sig": "MEUCIHfpsOO38sUQAEiQBur9JmIvHJwHeUaxNG0Q8oOZ4tTrAiEA5NyM6jERqMol0ogXpGU7c5TqrHbDxA2HWShWfp1s5rc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.1 < 0.5.0"}, "deprecated": "express 2.x series is deprecated"}, "2.2.2": {"name": "express", "version": "2.2.2", "dependencies": {"qs": ">= 0.0.6", "mime": ">= 0.0.1", "connect": ">= 1.3.0 < 2.0.0"}, "bin": {"express": "./bin/express"}, "directories": {"bin": "./bin", "lib": "./lib"}, "dist": {"shasum": "19c26d4cd36018896fc90a9eef3300052b3e01d2", "tarball": "https://registry.npmjs.org/express/-/express-2.2.2.tgz", "integrity": "sha512-C70Bq6K3N1XamJHOfTzxvNePA2U6wS2aKYlb6SmLQCOLDaP2+sSkSgRTXvH3qaE620/pQ0csYVSlZPppvLm/Tw==", "signatures": [{"sig": "MEUCIQD9fykNj5XlJfQpFfhznAHdaCF7yx0ChHpzAiSjt+FaQgIgSvcrO+3hfP3mgM9PJ2s3mdSZYmboXpltcqN0bvo5hd8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.1 < 0.5.0"}, "deprecated": "express 2.x series is deprecated"}, "2.3.0": {"name": "express", "version": "2.3.0", "dependencies": {"qs": ">= 0.0.6", "mime": ">= 0.0.1", "connect": ">= 1.4.0 < 2.0.0"}, "bin": {"express": "./bin/express"}, "directories": {"bin": "./bin", "lib": "./lib"}, "dist": {"shasum": "c32ae9a32a364077976352349eac54820cf21e3e", "tarball": "https://registry.npmjs.org/express/-/express-2.3.0.tgz", "integrity": "sha512-XgpuRwbgiGrMQ/iC2EGJInEfm+CAcQp3Tk+Syv/ZI4MaHBYVlF+PMP62Nga09GMDNtaJQmqFJffASuDIFyzq/g==", "signatures": [{"sig": "MEUCIQCEDzCHj8xcbClz6M8K58xoNB1TCP5D2eAgcH7lrGHg+AIgWwqsoSIpL3R7Vg9DYSVFxN58VR4Qpvrwc2DFcAjsM8o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.1 < 0.5.0"}, "deprecated": "express 2.x series is deprecated"}, "2.3.1": {"name": "express", "version": "2.3.1", "dependencies": {"qs": ">= 0.0.6", "mime": ">= 0.0.1", "connect": ">= 1.4.0 < 2.0.0"}, "bin": {"express": "./bin/express"}, "directories": {"bin": "./bin", "lib": "./lib"}, "dist": {"shasum": "15a9459c9b9e785d52d14a62595a29d7cbab4882", "tarball": "https://registry.npmjs.org/express/-/express-2.3.1.tgz", "integrity": "sha512-mwynulEpsvAy60xTfThl+fHGIcE8v8+GHaaVEFuDmhj/2U64t/kVkcL0byh1SSPqp/uRuQEq+nIx63pjztwH/w==", "signatures": [{"sig": "MEQCIDjn+toB6jNwTiTUSaTgae0hMgNoFte08DSzbNVHz+7lAiBXJXZouze9fuUvApuB1LNZISKnLQMtWYDwGrzVrfmVuQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.1 < 0.5.0"}, "deprecated": "express 2.x series is deprecated"}, "2.3.2": {"name": "express", "version": "2.3.2", "dependencies": {"qs": ">= 0.0.6", "mime": ">= 0.0.1", "connect": ">= 1.4.0 < 2.0.0"}, "bin": {"express": "./bin/express"}, "directories": {"bin": "./bin", "lib": "./lib"}, "dist": {"shasum": "ad6a3071d59a3bf1a4ed0b1b2942d9f0e510a028", "tarball": "https://registry.npmjs.org/express/-/express-2.3.2.tgz", "integrity": "sha512-s1QuAcLuFK6bJRcy0DdPnyQe2+0TCIAV4imLIgHIn+++HDH+/cCrWzztXsYlMBNHWyvLcDl3iDKyXGXRAu0n8A==", "signatures": [{"sig": "MEUCIAkx/Peg6JNmWUVGPKw+iwEwHoCXkwa4ExY4vJspqDFMAiEAtju2DNlVDtJXBN7NvqMA/q9Nfn9jzXinD7SNGZYlrDI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.1 < 0.5.0"}, "deprecated": "express 2.x series is deprecated"}, "2.3.3": {"name": "express", "version": "2.3.3", "dependencies": {"qs": ">= 0.0.6", "mime": ">= 0.0.1", "connect": ">= 1.4.0 < 2.0.0"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "936507d26e0433598679a645a87e403b3292547c", "tarball": "https://registry.npmjs.org/express/-/express-2.3.3.tgz", "integrity": "sha512-UxP5i2wosa3UqrgUjIVo2TNTl1BTZQYa2AH1qlVV6dtFQG6Xf6n2FFzxYa002FxwDnUtUPV5a3CBVBXJR1MdQA==", "signatures": [{"sig": "MEUCIQDvwz3hI+nylrREz1g7Sa9kseP1ARc4/brcckVV/LhffQIgc9qAu00MzS23UbLvAmSnrwe6BnvSMsJckvs8kRHxVTU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.1 < 0.5.0"}, "deprecated": "express 2.x series is deprecated"}, "2.3.4": {"name": "express", "version": "2.3.4", "dependencies": {"qs": ">= 0.0.6", "mime": ">= 0.0.1", "connect": ">= 1.4.0 < 2.0.0"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "8db976504b3f7f1da32abc845c45c20610a1ffd0", "tarball": "https://registry.npmjs.org/express/-/express-2.3.4.tgz", "integrity": "sha512-K+6mV39CDGGlXNC8RHnxusQ9Ra+rPJSSkEL9QzRQOgReGiQ7tcyIR/aQTT3LvS3KbAA77YCphXBa4qChVGvTiQ==", "signatures": [{"sig": "MEYCIQCjzs44O1qK9A3HNKl4CXEAj+p+ulkFHaxuYqZuCjX9EQIhAMAR3cpPe6mL9N8a15WsA/nbyuIbsvcbIKJhPE4TBUcb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.1 < 0.5.0"}, "deprecated": "express 2.x series is deprecated"}, "2.3.5": {"name": "express", "version": "2.3.5", "dependencies": {"qs": ">= 0.0.6", "mime": ">= 0.0.1", "connect": ">= 1.4.0 < 2.0.0"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "a3113d0d9db4ea118e2c12b044a04c16741e799b", "tarball": "https://registry.npmjs.org/express/-/express-2.3.5.tgz", "integrity": "sha512-QiLz4FCBLhZ9IQVob3FWnLP4gGvRBz2L8LMmLO6q/N0UG7biHrtdElY8tLAoIklNtZqMfs6jFGhFKK5QGsy4sw==", "signatures": [{"sig": "MEQCIFC8GkWcJ6TQQFtqS/iNEy86G05zm31VSAdlePBoAVb3AiBqW0kdPVUQfp8BCL9DWImWXrpfxcMOWSOhwON/zLr0TQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.1 < 0.5.0"}, "deprecated": "express 2.x series is deprecated"}, "2.3.6": {"name": "express", "version": "2.3.6", "dependencies": {"qs": ">= 0.0.6", "mime": ">= 0.0.1", "connect": ">= 1.4.1 < 2.0.0"}, "devDependencies": {"ejs": "0.4.2", "jade": "0.11.0", "hamljs": "0.5.1", "should": "0.2.1", "stylus": "0.13.0", "expresso": "0.7.2", "connect-form": "0.2.1", "connect-redis": ">= 0.0.1", "node-markdown": ">= 0.0.1", "express-messages": "0.0.2"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "8598e2995fc7c7427b7c3aed53837be652e873c7", "tarball": "https://registry.npmjs.org/express/-/express-2.3.6.tgz", "integrity": "sha512-0FI6fNIsmMDQqhN/8ND364gYEy45ml5+WnSvCvTp9W4MeSGFdm1NnszHcx2PNUP7oTJtp3UYH8vlPBDXt4EkmQ==", "signatures": [{"sig": "MEYCIQDQJ/XOwUGNIsnMJ0UC/0G2BSSUSXbBEyab6mZr1lqOlQIhAKyeVNBZOJKrC3Dk0AtumlI1vCRgYoAItT0zIRANHjo6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.1 < 0.5.0"}, "deprecated": "express 2.x series is deprecated"}, "2.3.7": {"name": "express", "version": "2.3.7", "dependencies": {"qs": ">= 0.0.6", "mime": ">= 0.0.1", "connect": ">= 1.4.1 < 2.0.0"}, "devDependencies": {"ejs": "0.4.2", "jade": "0.11.0", "hamljs": "0.5.1", "should": "0.2.1", "stylus": "0.13.0", "expresso": "0.7.2", "connect-form": "0.2.1", "connect-redis": ">= 0.0.1", "node-markdown": ">= 0.0.1", "express-messages": "0.0.2"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "6d008ca32c4a23110032e67f4c40843c068e13b7", "tarball": "https://registry.npmjs.org/express/-/express-2.3.7.tgz", "integrity": "sha512-DWX+PZQiI9Hk3ZgLmXDjv05kxTxEkkxUXU9REDR447n++tP3gufv1VaGSzGecOth+933kVuX+5RYL7blQrpm9A==", "signatures": [{"sig": "MEYCIQCZjrDgF0TuhwewGI8dJ/bQPxU9RKAuV7SzZrngWAuFqgIhAIfej2Ma/Xhf4BZ/+WeZDG8z1R9Tqk3I1+AexA6UHfV2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.1 < 0.5.0"}, "deprecated": "express 2.x series is deprecated"}, "2.3.8": {"name": "express", "version": "2.3.8", "dependencies": {"qs": ">= 0.0.6", "mime": ">= 0.0.1", "connect": ">= 1.4.1 < 2.0.0"}, "devDependencies": {"ejs": "0.4.2", "jade": "0.11.0", "hamljs": "0.5.1", "should": "0.2.1", "stylus": "0.13.0", "expresso": "0.7.2", "connect-form": "0.2.1", "connect-redis": ">= 0.0.1", "node-markdown": ">= 0.0.1", "express-messages": "0.0.2"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "fac5808b93b5abf84906c886fe314a0d4f44fa89", "tarball": "https://registry.npmjs.org/express/-/express-2.3.8.tgz", "integrity": "sha512-vyOxwEFDTQycYrRIsgjODTdt11VSmW/OxqPHqo8kTLekpRbxOxLz60MHJUwixF/igm9MCRhJ0FUaTSg/pKjIcw==", "signatures": [{"sig": "MEUCIDYj+udTYuA5uHIvtIJ9PVQNyOlrM+6ssA5Mz3PgtgwFAiEAmzQM+swLh6LvX0kGGBSVeLvkHHsilIiREuOArCjPQCI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.1 < 0.5.0"}, "deprecated": "express 2.x series is deprecated"}, "2.3.9": {"name": "express", "version": "2.3.9", "dependencies": {"qs": ">= 0.0.6", "mime": ">= 0.0.1", "connect": ">= 1.4.1 < 2.0.0"}, "devDependencies": {"ejs": "0.4.2", "jade": "0.11.0", "hamljs": "0.5.1", "should": "0.2.1", "stylus": "0.13.0", "expresso": "0.7.2", "connect-form": "0.2.1", "connect-redis": ">= 0.0.1", "node-markdown": ">= 0.0.1", "express-messages": "0.0.2"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "e5b6a5dc5452e9bcaf8936297f9f0e111b71a2a7", "tarball": "https://registry.npmjs.org/express/-/express-2.3.9.tgz", "integrity": "sha512-OXr9LTgIvhBgxjchYsbmHSteZcahnPBk6FM+tFBCerBgU6V2NfD5uuPOhobEyyf9TAfo5KqRs2BWbfFFEpKkBg==", "signatures": [{"sig": "MEUCIQCxb6G+7DDKXcns90MgyP9HejHoRhhUssKM3DXc1vpIJQIgbhDOGRBeEu8YMqsYeSqSmG6fCAED7A6anS+8QcEDlkE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.1 < 0.5.0"}, "deprecated": "express 2.x series is deprecated"}, "2.3.10": {"name": "express", "version": "2.3.10", "dependencies": {"qs": ">= 0.0.6", "mime": ">= 0.0.1", "connect": ">= 1.4.1 < 2.0.0"}, "devDependencies": {"ejs": "0.4.2", "jade": "0.11.0", "hamljs": "0.5.1", "should": "0.2.1", "stylus": "0.13.0", "expresso": "0.7.2", "connect-form": "0.2.1", "connect-redis": ">= 0.0.1", "node-markdown": ">= 0.0.1", "express-messages": "0.0.2"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "09b5e939b28af0705d1ac46265c703db1016310c", "tarball": "https://registry.npmjs.org/express/-/express-2.3.10.tgz", "integrity": "sha512-+DgIPkB512cTUVZhYQWgG9XMs68QB7Mgi9gzyycRQeAOdMqKqEhuIDyBFCNqCDIziOZBB3oquD7Vi77TEiGxPA==", "signatures": [{"sig": "MEUCIEmjKWRPRyMMNPEA7wkOYG2uS30Txho5SYMNRFODpau0AiEAl0dDfPB6LKq2Mj2gKDc9OBc4T/bFIEEnJ/bAhzaF9UU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.1 < 0.5.0"}, "deprecated": "express 2.x series is deprecated"}, "2.3.11": {"name": "express", "version": "2.3.11", "dependencies": {"qs": ">= 0.0.6", "mime": ">= 0.0.1", "connect": ">= 1.4.1 < 2.0.0"}, "devDependencies": {"ejs": "0.4.2", "jade": "0.11.0", "hamljs": "0.5.1", "should": "0.2.1", "stylus": "0.13.0", "expresso": "0.7.2", "connect-form": "0.2.1", "connect-redis": ">= 0.0.1", "node-markdown": ">= 0.0.1", "express-messages": "0.0.2"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "1dcd3a404332565a64c8290797e183707612f25a", "tarball": "https://registry.npmjs.org/express/-/express-2.3.11.tgz", "integrity": "sha512-Ch/eY/a1ZxITWKuT1Xo88bZBL8BDh1iYF6uh4ORmKr9PnHbZXx6O4sH0XWLH696VFH3drPGuNZ8jxDv4M3oStA==", "signatures": [{"sig": "MEQCIGi8iIgt7Ts4uJPx+7h6xM51BT5fUy9aEkZdEs9DkwZ9AiAZe/jKqfreMRSyqgl5XUm2cWDB+/vOQDZwFsthwAiBhw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.1 < 0.5.0"}, "deprecated": "express 2.x series is deprecated"}, "2.3.12": {"name": "express", "version": "2.3.12", "dependencies": {"qs": ">= 0.0.6", "mime": ">= 0.0.1", "connect": ">= 1.5.1 < 2.0.0"}, "devDependencies": {"ejs": "0.4.2", "jade": "0.11.0", "hamljs": "0.5.1", "should": "0.2.1", "stylus": "0.13.0", "expresso": "0.7.2", "connect-form": "0.2.1", "connect-redis": ">= 0.0.1", "node-markdown": ">= 0.0.1", "express-messages": "0.0.2"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "9e750c8e50ff976f89b4ed9e1ca6d534bad23014", "tarball": "https://registry.npmjs.org/express/-/express-2.3.12.tgz", "integrity": "sha512-opvOGY7N/HWlCzL0IPw5EXTmAcBgyl+u6/+UtoUlMbXrL4ev+0LfPkMsHzaOsXC2zHcKIuY/H4TZxgH5ZqkVfQ==", "signatures": [{"sig": "MEUCICRwBTEn8ztlL0TFVJbNr7e4MHUAnJbrhSeHvC3j7mJMAiEAkyYD3/R0DMT/XGW4V4DTgYk5O34NrALgXQ0bzJ4x+VY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.1 < 0.5.0"}, "deprecated": "express 2.x series is deprecated"}, "2.4.0": {"name": "express", "version": "2.4.0", "dependencies": {"qs": ">= 0.0.6", "mime": ">= 0.0.1", "connect": ">= 1.5.1 < 2.0.0"}, "devDependencies": {"ejs": "0.4.2", "jade": "0.11.0", "hamljs": "0.5.1", "should": "0.2.1", "stylus": "0.13.0", "expresso": "0.7.2", "connect-form": "0.2.1", "connect-redis": ">= 0.0.1", "node-markdown": ">= 0.0.1", "express-messages": "0.0.2"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "c6cad05e9ec481a91e3817ca25cfd55ea37c00ce", "tarball": "https://registry.npmjs.org/express/-/express-2.4.0.tgz", "integrity": "sha512-i0tYYuPYvCSP0yNF7LM5Y9Ppple6D+68flpl1B76We7a7cRTKV1KKflNUcgw3davXhFRFJlzZp7y506Ml1AWPA==", "signatures": [{"sig": "MEQCIBeUH4LLVVmXHEakwlnn7s2KIYh4hPoFYyMiX8fOfZSeAiAhVYM3Jm6MBoKGRX/ZuF5pu8uzJp9l3BNV87lyghU8BQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.1 < 0.5.0"}, "deprecated": "express 2.x series is deprecated"}, "2.4.1": {"name": "express", "version": "2.4.1", "dependencies": {"qs": ">= 0.0.6", "mime": ">= 0.0.1", "connect": ">= 1.5.2 < 2.0.0"}, "devDependencies": {"ejs": "0.4.2", "jade": "0.11.0", "hamljs": "0.5.1", "should": "0.2.1", "stylus": "0.13.0", "expresso": "0.7.2", "connect-form": "0.2.1", "connect-redis": ">= 0.0.1", "node-markdown": ">= 0.0.1", "express-messages": "0.0.2"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "006d435d5ca4332e51cc56ec3a69c707e40d62b4", "tarball": "https://registry.npmjs.org/express/-/express-2.4.1.tgz", "integrity": "sha512-f9/+15NlaaS48j1e/gcIOWtTANVzgEcRdtq4BDZwKYR6zZq64ARD25xcWibzMA+hEbQzeo7QctJrnFsZk5UVVw==", "signatures": [{"sig": "MEUCIG62kYanS9/e1m6BHxrJiBk3OLmY48yWGEYX1GI1wK1FAiEAsMyvC1/rV6ERfOeiT30ev7wtKCRii5USQWrWqctL7sg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.1 < 0.5.0"}, "deprecated": "express 2.x series is deprecated"}, "2.4.2": {"name": "express", "version": "2.4.2", "dependencies": {"qs": ">= 0.0.6", "mime": ">= 0.0.1", "connect": ">= 1.5.2 < 2.0.0"}, "devDependencies": {"ejs": "0.4.2", "jade": "0.11.0", "hamljs": "0.5.1", "should": "0.2.1", "stylus": "0.13.0", "expresso": "0.7.2", "connect-form": "0.2.1", "connect-redis": ">= 0.0.1", "node-markdown": ">= 0.0.1", "express-messages": "0.0.2"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "bfdd3dfd9c387e3196ac9dc8c7ff8d3a930d4d1a", "tarball": "https://registry.npmjs.org/express/-/express-2.4.2.tgz", "integrity": "sha512-wmPXjbOGlDxub/I0SQZdyZCn/H+iu1008nGE+hjw6RNcIQULp4xnBn/SBWrFNPLnsixcMGbPE0Ioyrl2uoVBRg==", "signatures": [{"sig": "MEYCIQCIvrMf7A/9di06dU6vE9TygMXVw/dUl7hDCofpXs9xdQIhAKcZg9a7EFQ8gVMImKIJaDoxB0Y/zrk8RwiAWo4MUytR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.1 < 0.5.0"}, "deprecated": "express 2.x series is deprecated"}, "2.4.3": {"name": "express", "version": "2.4.3", "dependencies": {"qs": ">= 0.0.6", "mime": ">= 0.0.1", "connect": ">= 1.5.2 < 2.0.0"}, "devDependencies": {"ejs": "0.4.2", "jade": "0.11.0", "hamljs": "0.5.1", "should": "0.2.1", "stylus": "0.13.0", "expresso": "0.7.2", "connect-form": "0.2.1", "connect-redis": ">= 0.0.1", "node-markdown": ">= 0.0.1", "express-messages": "0.0.2"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "5f52dd1e2cddbb83b3483cfb4c8c5c24d3975450", "tarball": "https://registry.npmjs.org/express/-/express-2.4.3.tgz", "integrity": "sha512-sujgOgutC2lCuPcqBI7iGYseCvuUNU1G8cHOS13hh5Cs6n/xLMYZNcWiFSVYz1AvhWTj33pXd2CAlM5H+L0qfQ==", "signatures": [{"sig": "MEYCIQC6t3ZJzze8gPShRiHlQFBlOkecUDjTC7ZoSfkTzzpCzQIhAP5waMxiEn5ft345Esiwao3Ulb1/KhJyXbT9wHEu2PrY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.1 < 0.5.0"}, "deprecated": "express 2.x series is deprecated"}, "2.4.4": {"name": "express", "version": "2.4.4", "dependencies": {"qs": ">= 0.3.0", "mime": ">= 0.0.1", "connect": ">= 1.5.2 < 2.0.0"}, "devDependencies": {"ejs": "0.4.2", "jade": "0.11.0", "hamljs": "0.5.1", "should": "0.2.1", "stylus": "0.13.0", "expresso": "0.7.2", "connect-form": "0.2.1", "connect-redis": ">= 0.0.1", "node-markdown": ">= 0.0.1", "express-messages": "0.0.2"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "ae677e39c6f489e328cb7994b88ebee7db19b6d9", "tarball": "https://registry.npmjs.org/express/-/express-2.4.4.tgz", "integrity": "sha512-4ctvACOlTu3sRKdSU3Q5Cw6jakxuBTNndPAdOJncKsUTs2Ifd//uzgd8nrfU4X24X2tbinhLNIFlN5BmNzNZjg==", "signatures": [{"sig": "MEYCIQDA4l0xwoBIbF/R3/S6aZ2O1JXRz+z5gV7YsznMjW6WFgIhAK9nLQOmmQthDkcWIte4Sa64Bp0hBbu3/jit2vAXt5M0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.1 < 0.5.0"}, "deprecated": "express 2.x series is deprecated"}, "2.4.5": {"name": "express", "version": "2.4.5", "dependencies": {"qs": ">= 0.3.1", "mime": ">= 0.0.1", "connect": ">= 1.5.2 < 2.0.0"}, "devDependencies": {"ejs": "0.4.2", "jade": "0.11.0", "hamljs": "0.5.1", "should": "0.2.1", "stylus": "0.13.0", "expresso": "0.7.2", "connect-form": "0.2.1", "connect-redis": ">= 0.0.1", "node-markdown": ">= 0.0.1", "express-messages": "0.0.2"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "b042984190df1ea06cc6e89c3eb4dfa848376322", "tarball": "https://registry.npmjs.org/express/-/express-2.4.5.tgz", "integrity": "sha512-sRNw3lIJrfYVhXwFUZHwVTsUdStXVsgnZlyJhv0uQhL0mjgBAkm/qITFwbeDyzsmE2J8QY2TrQn3q6U3OjDbIw==", "signatures": [{"sig": "MEUCIGFnHjtz4UlQ7hq18Pe6oHr2P8bLKqAgi8XU1P0zQre2AiEAsdbS2qPJhlx+5aziRZ+bm6N6l1zfp9NeAMfOteDWMR8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.1 < 0.5.0"}, "deprecated": "express 2.x series is deprecated"}, "2.4.6": {"name": "express", "version": "2.4.6", "dependencies": {"qs": ">= 0.3.1", "mime": ">= 0.0.1", "connect": ">= 1.5.2 < 2.0.0"}, "devDependencies": {"ejs": "0.4.2", "jade": "0.11.0", "hamljs": "0.5.1", "should": "0.2.1", "stylus": "0.13.0", "expresso": "0.7.2", "connect-form": "0.2.1", "connect-redis": ">= 0.0.1", "node-markdown": ">= 0.0.1", "express-messages": "0.0.2"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "df8152c5a40bd89ad74ab07e5ef999fac5a00916", "tarball": "https://registry.npmjs.org/express/-/express-2.4.6.tgz", "integrity": "sha512-9Dct3CMeqVzISyUvqG+ROboBxKsPjjWGgQwU1LHCe4pYZLjCYtF/Y32BukWnvElVHtX9t7bY7Oi7Vov5jUr7rA==", "signatures": [{"sig": "MEUCIELuY3EP7sUKkqFan6DrrUaRpSkR3+7uKF1oAfuDAT7GAiEA/3+cGHj+xajeLuBm2/xk34m8JdQhDmwh34NLLzm1hhc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.1 < 0.5.0"}, "deprecated": "express 2.x series is deprecated"}, "2.4.7": {"name": "express", "version": "2.4.7", "dependencies": {"qs": ">= 0.3.1", "mime": ">= 0.0.1", "mkdirp": "0.0.7", "connect": "1.7.x"}, "devDependencies": {"ejs": "0.4.2", "jade": "0.11.0", "hamljs": "0.5.1", "should": "0.2.1", "stylus": "0.13.0", "expresso": "0.7.2", "connect-form": "0.2.1", "connect-redis": ">= 0.0.1", "node-markdown": ">= 0.0.1", "express-messages": "0.0.2"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "872bbf5427e062100901ade6e80ff577ac24de3f", "tarball": "https://registry.npmjs.org/express/-/express-2.4.7.tgz", "integrity": "sha512-eHbV/y2mMx1lfwPuXokwXO4VpPhi4P2A13YYEkDQSzCoNL2MxF9jub3ubiXC7aQg4hdpNIYGKPnqp7OoMWO9Rg==", "signatures": [{"sig": "MEUCIQDxIIE8c21w4rz+wsoCeugnn3MiCmZAzJGysSriK/+giwIgPCv7r6zZfITWaWplWTE8W3uTU8rJxW0Esk7sBjl4GnY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.1 < 0.5.0"}, "deprecated": "express 2.x series is deprecated"}, "2.5.0": {"name": "express", "version": "2.5.0", "dependencies": {"qs": ">= 0.3.1", "mime": ">= 0.0.1", "mkdirp": "0.0.7", "connect": "1.7.x"}, "devDependencies": {"ejs": "0.4.2", "jade": "0.16.2", "hamljs": "0.5.1", "should": "0.3.2", "stylus": "0.13.0", "expresso": "0.9.2", "connect-form": "0.2.1", "connect-redis": ">= 0.0.1", "node-markdown": ">= 0.0.1", "express-messages": "0.0.2"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "3f9716eaa0e7380025fbb2c6c9942e3d9c9ed3b9", "tarball": "https://registry.npmjs.org/express/-/express-2.5.0.tgz", "integrity": "sha512-6IcOeBRtdJ9LhCSyXUKUJ9SeB0fK+9Qkvgm7qRznoeKIxGLlRRjHL0g0rHh9nS+IxThAY99hQt1nJ2AqYOmhbw==", "signatures": [{"sig": "MEUCIBC5j5E1IAGPxxTXwcJlI6IUWRS5TyEWsjD3I58zeCJjAiEAvST3xA6/KZqDjlJBQ4qz3xm94QOWMLJl0ujvQ+bhMKM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.1 < 0.7.0"}, "deprecated": "express 2.x series is deprecated"}, "2.5.1": {"name": "express", "version": "2.5.1", "dependencies": {"qs": ">= 0.3.1", "mime": ">= 0.0.1", "mkdirp": "0.0.7", "connect": "1.8.x"}, "devDependencies": {"ejs": "0.4.2", "jade": "0.16.2", "hamljs": "0.5.1", "should": "0.3.2", "stylus": "0.13.0", "expresso": "0.9.2", "connect-form": "0.2.1", "connect-redis": ">= 0.0.1", "node-markdown": ">= 0.0.1", "express-messages": "0.0.2"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "0644284c2c219264e2955fe94717ce7b462cd5d6", "tarball": "https://registry.npmjs.org/express/-/express-2.5.1.tgz", "integrity": "sha512-j+f7sBpTm5tu6FgwWuJdTIkqxzz850p/pMVv4/aHVajxuBjNJ3gIdR0YR2FWQX8UEFDYyMMaks1l1BNN+L5JMQ==", "signatures": [{"sig": "MEUCIQC62qixdwryp2ffpFA/OwfD/g+rXHwhMiGDvFwRBd5PZwIgQ+Kl1GBYqOvuAsGoeCrQ0RxeVDG/Gwa6J39L+3FwmYI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.1 < 0.7.0"}, "deprecated": "express 2.x series is deprecated"}, "2.5.2": {"name": "express", "version": "2.5.2", "dependencies": {"qs": ">= 0.3.1", "mime": ">= 0.0.1", "mkdirp": "0.0.7", "connect": "1.8.x"}, "devDependencies": {"ejs": "0.4.2", "jade": "0.16.2", "hamljs": "0.5.1", "should": "0.3.2", "stylus": "0.13.0", "expresso": "0.9.2", "connect-form": "0.2.1", "connect-redis": ">= 0.0.1", "node-markdown": ">= 0.0.1", "express-messages": "0.0.2"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "d58c41f7dff9a69696cffcc8e9bde4e81cbbcbef", "tarball": "https://registry.npmjs.org/express/-/express-2.5.2.tgz", "integrity": "sha512-+jUaT7NSDYkRLYPMnv76AXl9EEZi/w2cIQkFjmYArzTE5NaKp0NYFgebtTu9kPEKmfM9gGGcclpVaShSaPbupQ==", "signatures": [{"sig": "MEYCIQCsm8oF0eHuzRGOqd182vbu41eaDYJCfoBYOwmzeALXZQIhAKRpXQmoRV38S7z4xg5g2FRzJ2srcC+y4bSxhqbuM/4w", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.1 < 0.7.0"}, "deprecated": "express 2.x series is deprecated"}, "2.5.3": {"name": "express", "version": "2.5.3", "dependencies": {"qs": ">= 0.3.1", "mime": ">= 0.0.1", "mkdirp": "0.0.7", "connect": "1.x"}, "devDependencies": {"ejs": "0.4.2", "jade": "0.16.2", "hamljs": "0.5.1", "should": "0.3.2", "stylus": "0.13.0", "expresso": "0.9.2", "connect-form": "0.2.1", "connect-redis": ">= 0.0.1", "node-markdown": ">= 0.0.1", "express-messages": "0.0.2"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "65c909b778715753797129b9ea39bca6a248d6f1", "tarball": "https://registry.npmjs.org/express/-/express-2.5.3.tgz", "integrity": "sha512-UzGtmAa6iiEcVphsAAfEjWJS76lFP+T4l2BPMr0CZjrrjdnzBzycFaJKJlZmLvw2fK0CMvWZo0dJUx2+u8tFcg==", "signatures": [{"sig": "MEUCID7CX5abYNR+L4EFExPZPF5PQf+quKpa0S5G4QUlBS9xAiEA34YuFGnhGxJcdB/S3EQ7Ft8UGH6nauBqYDTR9X1ox/Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.1 < 0.7.0"}, "deprecated": "express 2.x series is deprecated"}, "2.5.4": {"name": "express", "version": "2.5.4", "dependencies": {"qs": ">= 0.3.1", "mime": ">= 0.0.1", "mkdirp": "0.0.7", "connect": "1.x"}, "devDependencies": {"ejs": "0.4.2", "jade": "0.16.2", "hamljs": "0.5.1", "should": "0.3.2", "stylus": "0.13.0", "expresso": "0.9.2", "connect-form": "0.2.1", "connect-redis": ">= 0.0.1", "node-markdown": ">= 0.0.1", "express-messages": "0.0.2"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "3090710723a13acfe000817b0fbeea13d8faee4b", "tarball": "https://registry.npmjs.org/express/-/express-2.5.4.tgz", "integrity": "sha512-i2Ky2WacMAYXWDHe7pUnQ98/F9RhXkw3vgo/C48Pxrppd0wN43ndUBsUxzAaRr4InoW7BT2peom6DzYVXHwRjg==", "signatures": [{"sig": "MEUCIARpE+mumW80v/md8XgIZErmvq0rqueljNkqcexD0pNAAiEA6OLSqDMKiOlTC+YdX8drfdhpUGRxqTJOUWfObDtYjUs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.1 < 0.7.0"}, "deprecated": "express 2.x series is deprecated"}, "2.5.5": {"name": "express", "version": "2.5.5", "dependencies": {"qs": ">= 0.3.1", "mime": ">= 0.0.1", "mkdirp": "0.0.7", "connect": "1.x"}, "devDependencies": {"ejs": "0.4.2", "jade": "0.16.2", "hamljs": "0.5.1", "should": "0.3.2", "stylus": "0.13.0", "expresso": "0.9.2", "connect-form": "0.2.1", "connect-redis": ">= 0.0.1", "node-markdown": ">= 0.0.1", "express-messages": "0.0.2"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "d15d4ffe5c420adda0645680361bb21c836b6e7c", "tarball": "https://registry.npmjs.org/express/-/express-2.5.5.tgz", "integrity": "sha512-dxkVgTEY6SwEliEjIKGCeXVOhonfj1ZhipQdN++e248j0ZfRPlgMrJkViCein7jDAUf8SOxTS5bEHcqyy4FUIw==", "signatures": [{"sig": "MEYCIQC3w3WmO8VylnJ/I0iMNQSbkmp+9LXRK5xglnKtitxVSgIhAIRHA21FXw8RZDJ70VYtnTHaZrX+stRInuZ5qZVY8HZu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.1 < 0.7.0"}, "deprecated": "express 2.x series is deprecated"}, "2.5.6": {"name": "express", "version": "2.5.6", "dependencies": {"qs": ">= 0.3.1", "mime": ">= 0.0.1", "mkdirp": "0.0.7", "connect": "1.x"}, "devDependencies": {"ejs": "0.4.2", "jade": "0.16.2", "hamljs": "0.6.x", "should": "0.3.2", "stylus": "0.13.0", "expresso": "0.9.2", "connect-form": "0.2.1", "connect-redis": ">= 0.0.1", "node-markdown": ">= 0.0.1", "express-messages": "0.0.2"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "1f2a96d01e1285797dae715d9ac93d9c60dd772a", "tarball": "https://registry.npmjs.org/express/-/express-2.5.6.tgz", "integrity": "sha512-jmE8xPqEr8OV1xzcbJldjMF9L5Pcq8ePKF3UMVChvW7nh/Wm4NY080RkY9PmtdZPjy1npJD8GuMAMn08saY9tg==", "signatures": [{"sig": "MEQCIA6vIcilZtVdIMjGmSNbF/IQF+8/Z1QR8OYOzrWPbpxsAiBE/p9dE6qOiOIkMUiyiubWHGN/mtbts5XUl+DiG7OirA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.1 < 0.7.0"}, "deprecated": "express 2.x series is deprecated"}, "2.5.7": {"name": "express", "version": "2.5.7", "dependencies": {"qs": ">= 0.3.1", "mime": ">= 0.0.1", "mkdirp": "0.0.7", "connect": "1.x"}, "devDependencies": {"ejs": "0.4.2", "jade": "0.16.2", "hamljs": "0.6.x", "should": "0.3.2", "stylus": "0.13.0", "expresso": "0.9.2", "connect-form": "0.2.1", "connect-redis": ">= 0.0.1", "node-markdown": ">= 0.0.1", "express-messages": "0.0.2"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "9f8fa92be38cb3c11959e99e18806cda19fd359f", "tarball": "https://registry.npmjs.org/express/-/express-2.5.7.tgz", "integrity": "sha512-Rz8OCTQ8xYncXU05UqRmOSq1VdLm+wf/d5J7sWC479uBfH1TAAVjwAVo5f9zj90B7XzsG3JZa9im0utz67w25g==", "signatures": [{"sig": "MEQCIAbQSU/2+B7JnJ5HexkJPuHrv+rWME1bmEoe7pfCWKo7AiAyqUrbNZLkWjzB7+ZKrFNjTLZANUizHPOlAtjGFESDEw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.1 < 0.7.0"}, "deprecated": "express 2.x series is deprecated"}, "2.5.8": {"name": "express", "version": "2.5.8", "dependencies": {"qs": "0.4.x", "mime": "1.2.4", "mkdirp": "0.3.0", "connect": "1.x"}, "devDependencies": {"ejs": "0.4.2", "jade": "0.16.2", "hamljs": "0.6.x", "should": "0.3.2", "stylus": "0.13.0", "expresso": "0.9.2", "connect-form": "0.2.1", "connect-redis": ">= 0.0.1", "node-markdown": ">= 0.0.1", "express-messages": "0.0.2"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "f166b55d4e8c6d2307ef88ad1768209613f7452a", "tarball": "https://registry.npmjs.org/express/-/express-2.5.8.tgz", "integrity": "sha512-BRmcCveTPqm7+AAHN8Ak3dzEoIvbB6xOutoZVZ6rRGgccYsVXqODIzJASdvkswI4B+Qg90miT5/ZBB8FJ7Q9+Q==", "signatures": [{"sig": "MEYCIQCXHpNgRA0TL9894H6nqhKMI6samhggd1ZIUTJh3ADCtQIhAKcoqxUEzl04w1WkcfDHDQApKvPJXADvaCvqZm0EvxZb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.1 < 0.7.0"}, "deprecated": "express 2.x series is deprecated"}, "2.5.9": {"name": "express", "version": "2.5.9", "dependencies": {"qs": "0.4.x", "mime": "1.2.4", "mkdirp": "0.3.0", "connect": "1.x"}, "devDependencies": {"ejs": "0.4.2", "jade": "0.16.2", "hamljs": "0.6.x", "should": "0.3.2", "stylus": "0.13.0", "expresso": "0.9.2", "connect-form": "0.2.1", "connect-redis": ">= 0.0.1", "node-markdown": ">= 0.0.1", "express-messages": "0.0.2"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "62d111ccaccf425182e1f30e541f84b551a72f2c", "tarball": "https://registry.npmjs.org/express/-/express-2.5.9.tgz", "integrity": "sha512-h3ckhknNZyLKbo4Nt3tp+fOPVfQ2J8LGKZ0LAschUZKWnSoNYmWbdvwKmTnXjmvS5dcD46Q+3Rd0azEcon8zoA==", "signatures": [{"sig": "MEYCIQDVhWC8qwoSe04XRRx1uOVTEPyjHFgO8nFyXJnHw0YSDgIhAOaP8xWKWBeVKMilKJAyn5eWgvjjK7O52Pbb/5dZzsn6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.1 < 0.7.0"}, "deprecated": "express 2.x series is deprecated"}, "2.5.10": {"name": "express", "version": "2.5.10", "dependencies": {"qs": "0.4.x", "mime": "1.2.4", "mkdirp": "0.3.0", "connect": "1.x"}, "devDependencies": {"ejs": "0.4.2", "jade": "0.16.2", "hamljs": "0.6.x", "should": "0.3.2", "stylus": "0.13.0", "expresso": "0.9.2", "connect-form": "0.2.1", "connect-redis": ">= 0.0.1", "node-markdown": ">= 0.0.1", "express-messages": "0.0.2"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "b1cdaf0c7e98e33125e6f8476800bdeb7f7efc8a", "tarball": "https://registry.npmjs.org/express/-/express-2.5.10.tgz", "integrity": "sha512-SPAW8n5AO6mPHPzveff396Lp+MJFWASYIOu0lEIz6UN7s++jE8MUROccp+hccHG65HzRisbrVCreST1MD7Q1eA==", "signatures": [{"sig": "MEUCIDeu03ryUtwOcQItemIFFOy/PWmDcsjpESMA5JwVL8+aAiEAkKmphhjr/RXxSKOM8ucgQqNOEinLUFt0V6+Rk/0Js4Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}, "deprecated": "express 2.x series is deprecated"}, "2.5.11": {"name": "express", "version": "2.5.11", "dependencies": {"qs": "0.4.x", "mime": "1.2.4", "mkdirp": "0.3.0", "connect": "1.x"}, "devDependencies": {"ejs": "0.4.2", "jade": "0.16.2", "hamljs": "0.6.x", "should": "0.3.2", "stylus": "0.13.0", "expresso": "0.9.2", "connect-form": "0.2.1", "connect-redis": ">= 0.0.1", "node-markdown": ">= 0.0.1", "express-messages": "0.0.2"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "4ce8ea1f3635e69e49f0ebb497b6a4b0a51ce6f0", "tarball": "https://registry.npmjs.org/express/-/express-2.5.11.tgz", "integrity": "sha512-gc3jJ0P3Bh1Zjkxe0ICSNmjhDvYWKiXfQIdDWuRPr8S4IZAZexzJHjrzNz56LsRKHTL0OiXQq602GfwZjZ8xPQ==", "signatures": [{"sig": "MEYCIQDexi2vUqEZBWRxRQAQb9FVALibXHC8Hm/uxbf8yi0XuwIhAK7iCSwwTHwsNFGOdA/P4X87SAFwocTPBvbyUUIxQ4KC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "express 2.x series is deprecated"}, "3.0.0": {"name": "express", "version": "3.0.0", "dependencies": {"crc": "0.2.0", "send": "0.1.0", "debug": "*", "fresh": "0.1.0", "cookie": "0.0.4", "mkdirp": "0.3.3", "connect": "2.6.0", "methods": "0.0.1", "commander": "0.6.1", "range-parser": "0.0.4"}, "devDependencies": {"ejs": "*", "hjs": "*", "jade": "*", "mocha": "*", "should": "*", "stylus": "*", "supertest": "0.0.1", "connect-redis": "*", "github-flavored-markdown": "*"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "41e202f3627ea442be9e86d5ec51246ad72339ed", "tarball": "https://registry.npmjs.org/express/-/express-3.0.0.tgz", "integrity": "sha512-77v5ENowsy0mmT/bY0Z5iXID3JZUtWrgVnVjewaznQLFYQdvel74XiM/hhttrKYEcLoxKsG/HjYFk1rk5Wecqg==", "signatures": [{"sig": "MEYCIQDetj4wqqiE5ELQTlfDGrWxJ2tXMU14COu+934DOGB8hgIhAK15NvTg5emsRgIWhlimm6ZX6tWitq0IZnsT9n/oambs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "3.0.1": {"name": "express", "version": "3.0.1", "dependencies": {"crc": "0.2.0", "send": "0.1.0", "debug": "*", "fresh": "0.1.0", "cookie": "0.0.4", "mkdirp": "0.3.3", "connect": "2.6.2", "methods": "0.0.1", "commander": "0.6.1", "range-parser": "0.0.4", "cookie-signature": "0.0.1"}, "devDependencies": {"ejs": "*", "hjs": "*", "jade": "*", "mocha": "*", "should": "*", "stylus": "*", "supertest": "0.0.1", "connect-redis": "*", "github-flavored-markdown": "*"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "36a5008d158a97e82817f45b89561633b61a1be8", "tarball": "https://registry.npmjs.org/express/-/express-3.0.1.tgz", "integrity": "sha512-lYyaCRJIZx/37FSkoHnVj/DImaTcPXLXsO3XvKkJwsQ/NfmAHP79xGDJV8EboUw0B2AB8n2jXWxmCRguINCuMw==", "signatures": [{"sig": "MEUCIQCfwbjTAlZN5gptFli1Xwwb1CSXdLVRYcxkJXnl26lNYQIgPNwVUYkabcjptQci1Avo5RZdJT1EYTbGzZP9nRWBizU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "3.0.2": {"name": "express", "version": "3.0.2", "dependencies": {"crc": "0.2.0", "send": "0.1.0", "debug": "*", "fresh": "0.1.0", "cookie": "0.0.4", "mkdirp": "0.3.3", "connect": "2.6.2", "methods": "0.0.1", "commander": "0.6.1", "range-parser": "0.0.4", "cookie-signature": "0.0.1"}, "devDependencies": {"ejs": "*", "hjs": "*", "jade": "*", "mocha": "*", "should": "*", "stylus": "*", "supertest": "0.0.1", "connect-redis": "*", "github-flavored-markdown": "*"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "fd93ed32f9a938cf79b7c4df95a2458d412f09b9", "tarball": "https://registry.npmjs.org/express/-/express-3.0.2.tgz", "integrity": "sha512-APaictzMzDSRYRRK4Gg5jrnyNuqsc0lC3RUcVaMBUMZ+svFnjYNj+CNAQ8qDvJtVv7o4k88iuRcy1Xp7sFd8bw==", "signatures": [{"sig": "MEYCIQCvBPY3Ke5j7KcTIjwJz6Ez2EmG27gHsLEQeiUoM8OwhQIhAOtU3ZMEZXPdovo7Q4gbJro+9bKppEshqKXS1dkqbLdI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "3.0.3": {"name": "express", "version": "3.0.3", "dependencies": {"crc": "0.2.0", "send": "0.1.0", "debug": "*", "fresh": "0.1.0", "cookie": "0.0.5", "mkdirp": "0.3.3", "connect": "2.7.0", "methods": "0.0.1", "commander": "0.6.1", "range-parser": "0.0.4", "cookie-signature": "0.0.1"}, "devDependencies": {"ejs": "*", "hjs": "*", "jade": "*", "mocha": "*", "should": "*", "stylus": "*", "supertest": "0.0.1", "connect-redis": "*", "github-flavored-markdown": "*"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "007c7590b1ab31219e6d8d71f86ad5086204868c", "tarball": "https://registry.npmjs.org/express/-/express-3.0.3.tgz", "integrity": "sha512-FvLeKv/gvbKLTQZX0yaJ8xNOImQf8G307G9q7saUk0FDz8ftPpw6FiV5Y5p7dBB5un2pZjrXkgmpERZHr1tuCg==", "signatures": [{"sig": "MEYCIQDcw2yZIzcM51AgB+EjvNCJV3PtTfnVh70rL7R9AWZR5gIhANVl1enYZK5FVXkTz6Aycwg3fFmIAooDDtTACq4oT/5a", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "3.0.4": {"name": "express", "version": "3.0.4", "dependencies": {"send": "0.1.0", "debug": "*", "fresh": "0.1.0", "cookie": "0.0.5", "mkdirp": "0.3.3", "connect": "2.7.1", "methods": "0.0.1", "commander": "0.6.1", "buffer-crc32": "0.1.1", "range-parser": "0.0.4", "cookie-signature": "0.0.1"}, "devDependencies": {"ejs": "*", "hjs": "*", "jade": "*", "mocha": "*", "should": "*", "stylus": "*", "supertest": "0.0.1", "connect-redis": "*", "github-flavored-markdown": "*"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "04a8e939145940a6bb3b215d736ec2c1584ee0a8", "tarball": "https://registry.npmjs.org/express/-/express-3.0.4.tgz", "integrity": "sha512-X5Mf8PholbK9LhRNNP5QZOBvHthhxpEMeRuIKq+wRRgMlSFJvO8MgwYc4VmKCVTe09nqxHnNm1bn293CEHGO4Q==", "signatures": [{"sig": "MEQCIG0tMRWDCDqL1KYwY18pZVdunVdvg8qJqcIMo6/fQ0lKAiAp2wJqV7MB2IHacKWdz8nh4K74J5hSS7SL1gR64MjJQQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "3.0.5": {"name": "express", "version": "3.0.5", "dependencies": {"send": "0.1.0", "debug": "*", "fresh": "0.1.0", "cookie": "0.0.5", "mkdirp": "0.3.3", "connect": "2.7.1", "methods": "0.0.1", "commander": "0.6.1", "buffer-crc32": "0.1.1", "range-parser": "0.0.4", "cookie-signature": "0.0.1"}, "devDependencies": {"ejs": "*", "hjs": "*", "jade": "*", "mocha": "*", "should": "*", "stylus": "*", "supertest": "0.0.1", "connect-redis": "*", "github-flavored-markdown": "*"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "4c6e5850e6b5e8ca2af57f21ed7097de50948b73", "tarball": "https://registry.npmjs.org/express/-/express-3.0.5.tgz", "integrity": "sha512-MMNtkTpEFfjAVvq9TeXqvIYqc9slO/x5CgBRPs9tGO1HHF4buvXgGaUNdQVWc/cLFw5gt/Z4mL3/IpPfv1aKmg==", "signatures": [{"sig": "MEUCID5vtBy5N8FPPyz/awzFRPPiLQMlUuiXg8VLxIMlwagdAiEAzao+aPDTitd5aqbk8YTYVxo54VCkfcs3H/zOl9ZyBNc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "3.0.6": {"name": "express", "version": "3.0.6", "dependencies": {"send": "0.1.0", "debug": "*", "fresh": "0.1.0", "cookie": "0.0.5", "mkdirp": "0.3.3", "connect": "2.7.2", "methods": "0.0.1", "commander": "0.6.1", "buffer-crc32": "0.1.1", "range-parser": "0.0.4", "cookie-signature": "0.0.1"}, "devDependencies": {"ejs": "*", "hjs": "*", "jade": "*", "mocha": "*", "should": "*", "stylus": "*", "supertest": "0.0.1", "connect-redis": "*", "github-flavored-markdown": "*"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "d274fcb868b95788bf4af62168d75d13fd77d8b4", "tarball": "https://registry.npmjs.org/express/-/express-3.0.6.tgz", "integrity": "sha512-2OJlOO/Ky8aepySj6OsW1VxuhEYaTohc41qXil63JaX9dOMvR/heof354lVQrjtPTqS+A8CyUnUmgKS336jMxg==", "signatures": [{"sig": "MEQCIChvU3n88q1pro9bSwKzg6yKuj5eAAUYVVzJjyA6QLkuAiAxwMAKUEjN5X1LLXUDdCRL3C/ZRhAxadf2RMsODt8bzg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "3.1.0": {"name": "express", "version": "3.1.0", "dependencies": {"send": "0.1.0", "debug": "*", "fresh": "0.1.0", "cookie": "0.0.5", "mkdirp": "0.3.3", "connect": "2.7.2", "methods": "0.0.1", "commander": "0.6.1", "buffer-crc32": "0.1.1", "range-parser": "0.0.4", "cookie-signature": "0.0.1"}, "devDependencies": {"ejs": "*", "hjs": "*", "jade": "*", "mocha": "*", "should": "*", "stylus": "*", "supertest": "0.0.1", "connect-redis": "*", "github-flavored-markdown": "*"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "f869b2d92320f5c3dd496c172e06f02b6ad43310", "tarball": "https://registry.npmjs.org/express/-/express-3.1.0.tgz", "integrity": "sha512-fZ/utHDyS9FSH5cYCifxpZjF4bboMyeq1YIndUgknybbn39fKB+rovjslEfNL7Pi9Cfavbj3H7srcdZ/+hKN3A==", "signatures": [{"sig": "MEYCIQCIYjaxDf5hboozKl4TATlewijKifEdCKID8loFg4CWUQIhAJC12JTXKdBfR6+AZpuxIGUvO4A7usTmgbPPD+udNntF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "3.1.1": {"name": "express", "version": "3.1.1", "dependencies": {"send": "0.1.0", "debug": "*", "fresh": "0.1.0", "cookie": "0.0.5", "mkdirp": "~0.3.4", "connect": "2.7.4", "methods": "0.0.1", "commander": "0.6.1", "buffer-crc32": "~0.2.1", "range-parser": "0.0.4", "cookie-signature": "0.0.1"}, "devDependencies": {"ejs": "*", "hjs": "*", "jade": "*", "mocha": "*", "should": "*", "stylus": "*", "supertest": "0.0.1", "connect-redis": "*", "github-flavored-markdown": "*"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "2cc065f642856be506686399aadeff375a701468", "tarball": "https://registry.npmjs.org/express/-/express-3.1.1.tgz", "integrity": "sha512-fbKtqXcyQ+44AGXCK8lO/nd+RdCsSwtBgqvni334jpJARst9Xba9k90Ipe74a8C4/VM4kcE7fcZgSLNq88Pcig==", "signatures": [{"sig": "MEUCIQCabVnaIxifhUENtrVZVyXdf0PCJj2Y+tLOQQMtAORrdgIgF0Mob4gY80NjwCm0/lMxvmeFCCoZQD5InL/tvL06SY4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "3.1.2": {"name": "express", "version": "3.1.2", "dependencies": {"send": "0.1.0", "debug": "*", "fresh": "0.1.0", "cookie": "0.0.5", "mkdirp": "~0.3.4", "connect": "2.7.5", "methods": "0.0.1", "commander": "0.6.1", "buffer-crc32": "~0.2.1", "range-parser": "0.0.4", "cookie-signature": "1.0.0"}, "devDependencies": {"ejs": "*", "hjs": "*", "jade": "*", "mocha": "*", "should": "*", "stylus": "*", "supertest": "0.0.1", "connect-redis": "*", "github-flavored-markdown": "*"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "52a02c8db8f22bbfa0d7478d847cd45161f985f7", "tarball": "https://registry.npmjs.org/express/-/express-3.1.2.tgz", "integrity": "sha512-7c0C0Fp6ttRoRxUfaswPpZnAs73ToJbvFuDgci8DHbgcgMAa7E85ckSZp6R53T4StpDGVdhKXKgBpN4V6o2pQQ==", "signatures": [{"sig": "MEYCIQDcG5zdy0J1gz05jvI5wXV3CWclxqZZ8K6HTmsbaee8HQIhAL/+vU6DDH16wQ2pnag+/yWVIp4dAek0+c22grEC9z5F", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "3.2.0": {"name": "express", "version": "3.2.0", "dependencies": {"send": "0.1.0", "debug": "*", "fresh": "0.1.0", "cookie": "0.0.5", "mkdirp": "~0.3.4", "connect": "2.7.6", "methods": "0.0.1", "commander": "0.6.1", "buffer-crc32": "~0.2.1", "range-parser": "0.0.4", "cookie-signature": "1.0.1"}, "devDependencies": {"ejs": "*", "hjs": "*", "jade": "*", "mocha": "*", "should": "*", "stylus": "*", "supertest": "0.0.1", "connect-redis": "*", "github-flavored-markdown": "*"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "7b66d6c66b038038eedf452804222b3077374ae0", "tarball": "https://registry.npmjs.org/express/-/express-3.2.0.tgz", "integrity": "sha512-caGNBarr84MakVQC4C60TMfoGSRDRDBexvz8r4Ggr52x2wS4IGstHINb7JBuBpmLGJi3824pqjD/PGe8Y+w26Q==", "signatures": [{"sig": "MEQCIHRqYo7fSn1mMG5rLrJTzG0mBojNWRj5Y3gVYt4+Z0h0AiANJ5O/Ep1oSK0nSz8MRQvJDjDx+WKuIqZfdVmpk3dXwQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "3.2.1": {"name": "express", "version": "3.2.1", "dependencies": {"qs": "0.6.1", "send": "0.1.0", "debug": "*", "fresh": "0.1.0", "cookie": "0.0.5", "mkdirp": "0.3.4", "connect": "2.7.7", "methods": "0.0.1", "commander": "0.6.1", "buffer-crc32": "0.2.1", "range-parser": "0.0.4", "cookie-signature": "1.0.1"}, "devDependencies": {"ejs": "*", "hjs": "*", "jade": "*", "mocha": "*", "should": "*", "stylus": "*", "supertest": "0.6.0", "connect-redis": "*", "github-flavored-markdown": "*"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "fd9ce6c0b8e4fda80772cef9af6e756434628d84", "tarball": "https://registry.npmjs.org/express/-/express-3.2.1.tgz", "integrity": "sha512-uXWDszllJM7RKmMrVFBXj355d9iqo4ZXEdiVHdXOyeDes6b+s6IYpojTOurfi2KYo4jHqDjVDGoL9r3orZuL4w==", "signatures": [{"sig": "MEQCIBsUXqZ0sDJqbdw6zI0BtFy6adOqBfj/uhesfR4u0GYPAiA9TamegJ1OWwC0z9esJtMNtQvIAnKKx90PLdNr0DCm+A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "3.2.2": {"name": "express", "version": "3.2.2", "dependencies": {"qs": "0.6.3", "send": "0.1.0", "debug": "*", "fresh": "0.1.0", "cookie": "0.0.5", "mkdirp": "0.3.4", "connect": "2.7.8", "methods": "0.0.1", "commander": "0.6.1", "buffer-crc32": "0.2.1", "range-parser": "0.0.4", "cookie-signature": "1.0.1"}, "devDependencies": {"ejs": "*", "hjs": "*", "jade": "*", "mocha": "*", "should": "*", "stylus": "*", "supertest": "0.6.0", "connect-redis": "*", "github-flavored-markdown": "*"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "22c6cb2e0efc20833670425cd820c5f4bb119f8b", "tarball": "https://registry.npmjs.org/express/-/express-3.2.2.tgz", "integrity": "sha512-qChBgdjfopcqX8CASjhvRRmOqQTMAZhZtfJFJqc0/TI2yUI5s2Z2NQC6x4HcM5dPe2L+avpeWoJOAhoI+s76Lw==", "signatures": [{"sig": "MEYCIQCW19N6gr8Y+gMrZdrgzFeGRJKBopwTGV3umC5asvPFPgIhAPGAXp8YRe9RDtVOu7nJOSUbCrMC2s3NHIgnzaW5dQoy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "3.2.3": {"name": "express", "version": "3.2.3", "dependencies": {"qs": "0.6.4", "send": "0.1.0", "debug": "*", "fresh": "0.1.0", "cookie": "0.0.5", "mkdirp": "0.3.4", "connect": "2.7.9", "methods": "0.0.1", "commander": "0.6.1", "buffer-crc32": "0.2.1", "range-parser": "0.0.4", "cookie-signature": "1.0.1"}, "devDependencies": {"ejs": "*", "hjs": "*", "jade": "*", "mocha": "*", "should": "*", "stylus": "*", "supertest": "0.6.0", "connect-redis": "*", "github-flavored-markdown": "*"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "9952eb764953ad40e4caa1f0b8715f7ba667f477", "tarball": "https://registry.npmjs.org/express/-/express-3.2.3.tgz", "integrity": "sha512-YUCfz9KqtXxWbT8uSnpHPIRdTUockyeU8QcOA+VZWpF+nHORoKs6JSMzs4ez25MDKUP0PdNI4lvPPT039bEMJg==", "signatures": [{"sig": "MEUCIQDPPy0gZG/c7eRCbk1G/H9GlxufJ2EeG+eHrpaXQK1WCgIgadAhHMyFPXGEj549H1Qy6X/Y2yXZ+jKgA/hUgReU2RY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "3.2.4": {"name": "express", "version": "3.2.4", "dependencies": {"send": "0.1.0", "debug": "*", "fresh": "0.1.0", "cookie": "0.0.5", "mkdirp": "0.3.4", "connect": "2.7.9", "methods": "0.0.1", "commander": "0.6.1", "buffer-crc32": "0.2.1", "range-parser": "0.0.4", "cookie-signature": "1.0.1"}, "devDependencies": {"ejs": "*", "hjs": "*", "jade": "*", "mocha": "*", "should": "*", "stylus": "*", "supertest": "0.6.0", "connect-redis": "*", "github-flavored-markdown": "*"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "f39fcba9a224011058fb581647688b12df94f585", "tarball": "https://registry.npmjs.org/express/-/express-3.2.4.tgz", "integrity": "sha512-LjaZToAP88lwB40fhJfM9SdulSI0ZquXTSPH2K1tS5mXDz5pyNaIQ7CwkCCbt17FPLE21YoTl0Ne9HEh2pr6OQ==", "signatures": [{"sig": "MEUCIH/woWogewKVWCH1zM0mgYRi5YmKaFrxtd7uEuL6HYlzAiEA7xVWgdAyDYYy7InQ4tVn9znWybXeHVIsFA49e4h0nJ0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "3.2.5": {"name": "express", "version": "3.2.5", "dependencies": {"send": "0.1.0", "debug": "*", "fresh": "0.1.0", "cookie": "0.1.0", "mkdirp": "0.3.4", "connect": "2.7.10", "methods": "0.0.1", "commander": "0.6.1", "buffer-crc32": "0.2.1", "range-parser": "0.0.4", "cookie-signature": "1.0.1"}, "devDependencies": {"ejs": "*", "hjs": "*", "jade": "*", "mocha": "*", "marked": "*", "should": "*", "stylus": "*", "supertest": "0.6.0", "connect-redis": "*"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "d2c86134d9fa1573b8004d23c6dc0d50bc8efe20", "tarball": "https://registry.npmjs.org/express/-/express-3.2.5.tgz", "integrity": "sha512-thmlKM3mgbUkIuV3GTNfloUGviWHrfwgFnl5y+6Fp5eZSD/FLEcgPaIMAxKa2x/DZ1jAC8mAcTy2ewh5Yt7Ddw==", "signatures": [{"sig": "MEYCIQC22+sEKKUYYCT5BWHJ3QjvpkV01R4Sfv1ZPveE0nIIEwIhAORYD7xUMdwrzEo8CFjtsK0sMeVjHcz/shbVx2zR4p3B", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "3.2.6": {"name": "express", "version": "3.2.6", "dependencies": {"send": "0.1.0", "debug": "*", "fresh": "0.1.0", "cookie": "0.1.0", "mkdirp": "0.3.4", "connect": "2.7.11", "methods": "0.0.1", "commander": "0.6.1", "buffer-crc32": "0.2.1", "range-parser": "0.0.4", "cookie-signature": "1.0.1"}, "devDependencies": {"ejs": "*", "hjs": "*", "jade": "*", "mocha": "*", "marked": "*", "should": "*", "stylus": "*", "supertest": "0.6.0", "connect-redis": "*"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "d8a9fe065adc23c5b41ec2c689c672b261430ffc", "tarball": "https://registry.npmjs.org/express/-/express-3.2.6.tgz", "integrity": "sha512-iuD6d9/gP0Ioc8dpHuf8mzX6kEI2ze2Z7Mg7EzTGKddEPkKGpjp91xJYsquP5ZYxsYIiWvIVRbkLbfAh0tUDpQ==", "signatures": [{"sig": "MEQCIEnkkPmS6RKWkXwCYAuDODas4D35J1dKFIhcZStEYCiiAiBg8GBHx/imUbd+OTVwNBWwTQoMBE3Ylyy4l7ho6r8MiQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "3.3.0": {"name": "express", "version": "3.3.0", "dependencies": {"send": "0.1.1", "debug": "*", "fresh": "0.1.0", "cookie": "0.1.0", "mkdirp": "0.3.4", "connect": "2.8.0", "methods": "0.0.1", "commander": "0.6.1", "buffer-crc32": "0.2.1", "range-parser": "0.0.4", "cookie-signature": "1.0.1"}, "devDependencies": {"ejs": "*", "hjs": "*", "jade": "0.30.0", "mocha": "*", "marked": "*", "should": "*", "stylus": "*", "supertest": "0.6.0", "connect-redis": "*"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "f89f8fc1ddfb7ffdfc9db3103a75881cd64dce7f", "tarball": "https://registry.npmjs.org/express/-/express-3.3.0.tgz", "integrity": "sha512-QuhYH3jMBdzOB7tZ2jIt2se4n/zjTkINzNcOYzVRzlZ7TTBirepl+LLHBi3Zbr59igXvch134boN68lKAo1aHg==", "signatures": [{"sig": "MEUCIE/g8EvKKrsGfkOKevJ0crlsjMo46o9Ampq6Z8kDBTk5AiEA3uJg5dUdB45hxf+ZPxQ/7BEYPU13VBjAIDXu9N7KDwQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "3.3.1": {"name": "express", "version": "3.3.1", "dependencies": {"send": "0.1.1", "debug": "*", "fresh": "0.1.0", "cookie": "0.1.0", "mkdirp": "0.3.4", "connect": "2.8.1", "methods": "0.0.1", "commander": "0.6.1", "buffer-crc32": "0.2.1", "range-parser": "0.0.4", "cookie-signature": "1.0.1"}, "devDependencies": {"ejs": "*", "hjs": "*", "jade": "0.30.0", "mocha": "*", "marked": "*", "should": "*", "stylus": "*", "supertest": "0.6.0", "connect-redis": "*"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "4bb79fb3548313d9e1a49ffdc5aa369a936127d7", "tarball": "https://registry.npmjs.org/express/-/express-3.3.1.tgz", "integrity": "sha512-arOByboFpBvbk4uTGXzYfWaZVxJRKA2GVJqTQA7cfw6EA17ZK5i8f8hWR+Sp8ULO/ufTSyjBn4JAKPpbnJ0KqA==", "signatures": [{"sig": "MEUCIQCEwTp2UAHtnlhqiMIaK8IPW0cK6FPS/+hMwN1y/PNpcAIgHdmrvEY0J/pyc319ShWFnf19g897/2E9Rn7FMSvPq+U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "3.3.2": {"name": "express", "version": "3.3.2", "dependencies": {"send": "0.1.2", "debug": "*", "fresh": "0.1.0", "cookie": "0.1.0", "mkdirp": "0.3.4", "connect": "2.8.2", "methods": "0.0.1", "commander": "0.6.1", "buffer-crc32": "0.2.1", "range-parser": "0.0.4", "cookie-signature": "1.0.1"}, "devDependencies": {"ejs": "*", "hjs": "*", "jade": "0.30.0", "mocha": "*", "marked": "*", "should": "*", "stylus": "*", "supertest": "0.6.0", "connect-redis": "*"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "d70c4888da2f35c9fa80e6747323ec6afeb6f947", "tarball": "https://registry.npmjs.org/express/-/express-3.3.2.tgz", "integrity": "sha512-MZZiAWL1CQCiMHkDv4BPbTzBzH2i3hjUZudTsBJWzDbA2alXBqajYUSr0rBS0fCaGwyI+VMM4PkeCRfbl+Qk3A==", "signatures": [{"sig": "MEQCIFjWQEVnGarkZEQ38ohhqZpwhOUrqJdAaTjfCx+D98ZwAiBXHMjDqjXDx2KE9pgS9N0nGOD0GavcSZGOBP3ZiltgUA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "3.3.3": {"name": "express", "version": "3.3.3", "dependencies": {"send": "0.1.2", "debug": "*", "fresh": "0.1.0", "cookie": "0.1.0", "mkdirp": "0.3.5", "connect": "2.8.3", "methods": "0.0.1", "commander": "1.2.0", "buffer-crc32": "0.2.1", "range-parser": "0.0.4", "cookie-signature": "1.0.1"}, "devDependencies": {"ejs": "*", "hjs": "*", "jade": "0.30.0", "mocha": "*", "marked": "*", "should": "*", "stylus": "*", "supertest": "0.6.0", "connect-redis": "*"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "c9b5244edad7c6b85dae94e5cf1b29162470c933", "tarball": "https://registry.npmjs.org/express/-/express-3.3.3.tgz", "integrity": "sha512-WTECSOuzRnZiYewFcPFLB08UQCJiVtimIo9cqwscMOs9RRyznpCmSbV5f4ogo5rD8lBB+3Zsz4lbRzs0J7BSTQ==", "signatures": [{"sig": "MEYCIQCk9qM1cLSGz52WUGvwcWzcjmrAaVfmVbErqKIrWOXDsQIhAOzxm4Qw3Rt1MpiBKluH0YMxpyHKZSrju8bsO3X61Mh2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "3.3.4": {"name": "express", "version": "3.3.4", "dependencies": {"send": "0.1.3", "debug": "*", "fresh": "0.1.0", "cookie": "0.1.0", "mkdirp": "0.3.5", "connect": "2.8.4", "methods": "0.0.1", "commander": "1.2.0", "buffer-crc32": "0.2.1", "range-parser": "0.0.4", "cookie-signature": "1.0.1"}, "devDependencies": {"ejs": "*", "hjs": "*", "jade": "0.30.0", "mocha": "*", "marked": "*", "should": "*", "stylus": "*", "supertest": "0.6.0", "connect-redis": "*"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "9abf22017213a8f6f54a421ce22b8ec27b7def62", "tarball": "https://registry.npmjs.org/express/-/express-3.3.4.tgz", "integrity": "sha512-kRxUCxqeMMhoPsZt4RX8DWGDca7APwJyCRHgCiCVJyO6VF5uTL2ChXVBE08rRgvDnScy6kuFO3AWfAeNg6UklQ==", "signatures": [{"sig": "MEUCIFlC3EKhIB9lj/71gcrRI3zvik0R6lpquPjJ//DTLgs6AiEAi0MfqdGMYDMtrAgAjeTfrykaPwF0cYWGMWvx4mqmEnc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "3.3.5": {"name": "express", "version": "3.3.5", "dependencies": {"send": "0.1.4", "debug": "*", "fresh": "0.2.0", "cookie": "0.1.0", "mkdirp": "0.3.5", "connect": "2.8.5", "methods": "0.0.1", "commander": "1.2.0", "buffer-crc32": "0.2.1", "range-parser": "0.0.4", "cookie-signature": "1.0.1"}, "devDependencies": {"ejs": "*", "hjs": "*", "jade": "0.30.0", "mocha": "*", "marked": "*", "should": "*", "stylus": "*", "supertest": "0.6.0", "connect-redis": "*"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "3fd077660c9ccae4710fcfb326290a01d1e72566", "tarball": "https://registry.npmjs.org/express/-/express-3.3.5.tgz", "integrity": "sha512-2Rt0Hi7mKC2PJ/xsGvcXbRtRJW9wzeg2R4BgkjB3lG27A2QeBq8mTcslUOeyStr7/Wu1+zxY06dLas2na6HgUQ==", "signatures": [{"sig": "MEYCIQDHMQl7B3ICMDWaHfh+/Xy5O8w69oYOJUY6OipWoUwTwAIhALdTtl30b/+TwgtK90XJBtTcKSFrAT7kL+RLb6vedm/U", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "3.3.6": {"name": "express", "version": "3.3.6", "dependencies": {"send": "0.1.4", "debug": "*", "fresh": "0.2.0", "cookie": "0.1.0", "mkdirp": "0.3.5", "connect": "2.8.5", "methods": "0.0.1", "commander": "1.2.0", "buffer-crc32": "0.2.1", "range-parser": "0.0.4", "cookie-signature": "1.0.1"}, "devDependencies": {"ejs": "*", "hjs": "*", "jade": "0.30.0", "mocha": "*", "marked": "*", "should": "*", "stylus": "*", "supertest": "0.6.0", "connect-redis": "*"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "c1082fdb55b9de2ce399252eb4e048da2ed9918d", "tarball": "https://registry.npmjs.org/express/-/express-3.3.6.tgz", "integrity": "sha512-u6qKN7jNlaclUJq4rXWSJmr/jlio7y+5S7jjKVg4FirpcuYN59rx0/3/iy0E+QuBca8m8X6I8GEmbhCf129NuA==", "signatures": [{"sig": "MEUCIQDINjedT6MaUSQuv4fDpoRcn26AgC13JLuv6Lu9metxCgIgOQMzh4+qdnm5fSmWRLTLj7sFZWDWcHYKgQeO1mX86Vo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "3.0.0-alpha4": {"name": "express", "version": "3.0.0-alpha4", "dependencies": {"crc": "0.2.0", "mime": "1.2.5", "debug": "*", "mkdirp": "0.3.1", "connect": "2.2.2", "commander": "0.5.2"}, "devDependencies": {"ejs": "*", "jade": "*", "mocha": "*", "should": "*", "stylus": "*", "connect-redis": "*", "github-flavored-markdown": "*"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "9bc6be2bcfbbd74dba66063808d3a75ad4bd7edb", "tarball": "https://registry.npmjs.org/express/-/express-3.0.0alpha4.tgz", "integrity": "sha512-Mvjh3iIHgvt5GL0da80IObef3blVPDrjtB327JMvMYbXsl570gcPSKpi8IaXBM+Rbb4pCOCD4ePZf1tTn9e3KA==", "signatures": [{"sig": "MEUCIHV1R+bvm6hKBEFDF4T8bZ3Ra5kKXThjFi+vjBRqTOzzAiEAz2hyLFW6Z+emvnvkKf11YCOWWQRl+aXIFeSQ6+G65vo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "3.0.0-alpha1": {"name": "express", "version": "3.0.0-alpha1", "dependencies": {"mime": "1.2.5", "debug": "*", "mkdirp": "0.3.1", "connect": "2.1.2", "commander": "0.5.2"}, "devDependencies": {"ejs": "*", "jade": "*", "mocha": "*", "should": "*", "stylus": "*", "connect-redis": "*", "github-flavored-markdown": "*"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "252902b7ed3a4b18a9163c51bdab519282cf2401", "tarball": "https://registry.npmjs.org/express/-/express-3.0.0alpha1.tgz", "integrity": "sha512-DkACIR8z/1Eu3FInetGALl+8PDypNxUtttTy8ujYhF4h2ZeYOQLnIA+RTUWxPu+GtkGPAYZ8KgDzBlC3bZMjDw==", "signatures": [{"sig": "MEYCIQDsx9EykzbWM/xEUJtznpCjCbSP1UZV0pq2xBQS0qlbWAIhALbtJvnLUaPuenfbYnnD5hz0HOnrooP2eBhGLncYySNc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.5.0 < 0.7.0"}}, "3.0.0-rc2": {"name": "express", "version": "3.0.0-rc2", "dependencies": {"crc": "0.2.0", "send": "0.0.3", "debug": "*", "fresh": "0.1.0", "cookie": "0.0.4", "mkdirp": "0.3.3", "connect": "2.4.2", "methods": "0.0.1", "commander": "0.6.1", "range-parser": "0.0.4"}, "devDependencies": {"ejs": "*", "hjs": "*", "jade": "*", "mocha": "*", "should": "*", "stylus": "*", "supertest": "0.0.1", "connect-redis": "*", "github-flavored-markdown": "*"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "ffa79ccee41abc97f2c57576cc433339200fcd33", "tarball": "https://registry.npmjs.org/express/-/express-3.0.0rc2.tgz", "integrity": "sha512-lp8LQWLzVBQHBp20GgpHp+ScBNwl70W1oYwbLZ8hcmq1OeqO3HnW2yTmFdrJIiXcE3i7487H10M929bhWBorHQ==", "signatures": [{"sig": "MEQCIFE9AVWQUyHF6k5XsRlhT/6l55rhOG0MsNG5Y34VBG88AiAHw0mfWN8RpL2i3cUt7xB7F+Z22SfXptyhmEdXJCkODw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "3.0.0-alpha5": {"name": "express", "version": "3.0.0-alpha5", "dependencies": {"crc": "0.2.0", "mime": "1.2.5", "debug": "*", "mkdirp": "0.3.1", "connect": "2.2.2", "commander": "0.6.0"}, "devDependencies": {"ejs": "*", "jade": "*", "mocha": "*", "should": "*", "stylus": "*", "connect-redis": "*", "github-flavored-markdown": "*"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "d01ff9c2ebd769744ee90cc89561a1c8ca5340ac", "tarball": "https://registry.npmjs.org/express/-/express-3.0.0alpha5.tgz", "integrity": "sha512-8aNsu8Wlki9cO/0cEcYwRMoE1WTOUMlUCa08Ee/Oi3+IHzKVaOYjofwDh4yfSJ4jRg0IjR3SoeZ4AoZ5pgMoDw==", "signatures": [{"sig": "MEQCIAsy21YqjJKF0nD2Y2IjeGk2OSSGpyQiwfpxSx2MO3rhAiALElqFriQ2p7zs4OgRpC9w3i73eeD2J9dIfqJMzlIP6w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "3.0.0-beta4": {"name": "express", "version": "3.0.0-beta4", "dependencies": {"crc": "0.2.0", "debug": "*", "fresh": "0.1.0", "cookie": "0.0.3", "mkdirp": "0.3.3", "connect": "2.3.4", "methods": "0.0.1", "commander": "0.6.1", "range-parser": "0.0.4"}, "devDependencies": {"ejs": "*", "hjs": "*", "jade": "*", "mocha": "*", "should": "*", "stylus": "*", "supertest": "0.0.1", "connect-redis": "*", "github-flavored-markdown": "*"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "0f7e5bb2db67e81b4d1c752300954133df276063", "tarball": "https://registry.npmjs.org/express/-/express-3.0.0beta4.tgz", "integrity": "sha512-1KbnqOEUCBnw1OM94GgyzDqEopX2gPfB8dV9+HK9LNqE816/J1r36m0oMR1F4r2MRdpR6P3ArtGoYQBa02OzDQ==", "signatures": [{"sig": "MEQCICllfeY0wh1oCX8l9MB2zf9ARYeAJYrmZdOm+99dbBjlAiB9mHrb5aRGXC2bblpWk5eo4GTR6XakwhMjYKrt8bvc5g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "3.0.0-rc1": {"name": "express", "version": "3.0.0-rc1", "dependencies": {"crc": "0.2.0", "send": "0.0.3", "debug": "*", "fresh": "0.1.0", "cookie": "0.0.4", "mkdirp": "0.3.3", "connect": "2.4.1", "methods": "0.0.1", "commander": "0.6.1", "range-parser": "0.0.4"}, "devDependencies": {"ejs": "*", "hjs": "*", "jade": "*", "mocha": "*", "should": "*", "stylus": "*", "supertest": "0.0.1", "connect-redis": "*", "github-flavored-markdown": "*"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "b96bc45e19a0fece6b4c26c297db2f958a50643a", "tarball": "https://registry.npmjs.org/express/-/express-3.0.0rc1.tgz", "integrity": "sha512-IB1XN2wmQNqHkseNaqsWy7Q0bTRCVBwme6LJkq4vJ1oVLI4lNmVM6XGzYGpamzCF0SqZhUdPGBSAP0faQsPGmA==", "signatures": [{"sig": "MEQCIASXqTAWi4W16MR+HxRkf4SL89JfoP1SJI1Ko1MxK8R1AiBSKFZj2xCjKsKVf0qxLM0iQ0RZMdV7zQhJqkmpRSKf6g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "3.0.0-beta1": {"name": "express", "version": "3.0.0-beta1", "dependencies": {"crc": "0.2.0", "mime": "1.2.5", "debug": "*", "mkdirp": "0.3.2", "connect": "2.3.0", "commander": "0.6.1"}, "devDependencies": {"ejs": "*", "jade": "*", "mocha": "*", "should": "*", "stylus": "*", "connect-redis": "*", "github-flavored-markdown": "*"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "557dda7815bffb84dea4cd3c09e1fe6538b2262f", "tarball": "https://registry.npmjs.org/express/-/express-3.0.0beta1.tgz", "integrity": "sha512-louxpob+X36z2q6B5nSXV3Z2P6nu/+lUaiOan6aBAcBJdGHSXIJ7boJMUgulEMPB23MisDbqaFl1n0IMqLWMkg==", "signatures": [{"sig": "MEYCIQDxR7oZuds8+XXv8gOvGkfuUhZBES+4gYPvH7PPtEcz2QIhAMNxnQ2bmqOhNS2QrseevN5YaO7w0AEtIuL8vnsYc9+j", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "1.0.0-rc": {"name": "express", "version": "1.0.0-rc", "dependencies": {"connect": ">= 0.2.2"}, "bin": {"express": "./bin/express"}, "directories": {"lib": "./lib/express"}, "dist": {"shasum": "cc9545ae107dac12821f997e3dd43c5df223ba13", "tarball": "https://registry.npmjs.org/express/-/express-1.0.0rc.tgz", "integrity": "sha512-qagPpCEW0cdM/L+LKLUr491Gk5Kayc8ryZlE0mRgHUSHNam3vN5tYfZmjFuqPvwsto7B4VE4wxJK+F78GyCJoQ==", "signatures": [{"sig": "MEUCIEX5zEim83LuECuGchljgrJEPzum4xSj19h+v8Fl3BO1AiEA+6pd1bOZjgMAeJPakBmFhCPmpHbCTDthwe3PCAnIOBI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.1.98"}}, "3.0.0-beta7": {"name": "express", "version": "3.0.0-beta7", "dependencies": {"send": "0.0.3", "debug": "*", "fresh": "0.1.0", "cookie": "0.0.3", "mkdirp": "0.3.3", "connect": "2.3.9", "methods": "0.0.1", "commander": "0.6.1", "range-parser": "0.0.4", "response-send": "0.0.1"}, "devDependencies": {"ejs": "*", "hjs": "*", "jade": "*", "mocha": "*", "should": "*", "stylus": "*", "supertest": "0.0.1", "connect-redis": "*", "github-flavored-markdown": "*"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "92e854f2814e05a333d2acfde43585cfda21d9aa", "tarball": "https://registry.npmjs.org/express/-/express-3.0.0beta7.tgz", "integrity": "sha512-cF94rFbnS64RITqgFi3oPBAVJosqAwnWcN52nTM1hisxBsCjvGH7UrV3TKtPNxvN7eF6+FIQNa6tBDjLligIbQ==", "signatures": [{"sig": "MEUCICbkXz6cBomKoSj6Y5muuUbbF0gOEhYG/6FIGSWIvf65AiEAiQ1CB/gjHOLqSjzAMvfYFfDothAEULQysXiwBfz0Eaw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "2.0.0-beta3": {"name": "express", "version": "2.0.0-beta3", "dependencies": {"qs": ">= 0.0.6", "mime": ">= 0.0.1", "connect": ">= 1.0.1 < 2.0.0"}, "bin": {"express": "./bin/express"}, "directories": {"bin": "./bin", "lib": "./lib"}, "dist": {"shasum": "f9c1324023729c4eb96688023e989fe2f8565c61", "tarball": "https://registry.npmjs.org/express/-/express-2.0.0beta3.tgz", "integrity": "sha512-dZoDukf5t2VJTsTBicHDLHRTLre3A3w6PV/epzw8Kkx2JJVc9dbbIRd1vfDd5zYXhP66tdPk1UmfBiipb5MdjA==", "signatures": [{"sig": "MEUCIHct6bgiPwGMyp4KXWepm5kkMfbGUZ8VYLSMWOKNthEmAiEAo3krZXqn4DkQ3R5xY+HFEZHWxF4cGYIxkbUUpXDf2y8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.1 < 0.5.0"}}, "1.0.0-rc3": {"name": "express", "version": "1.0.0-rc3", "dependencies": {"connect": ">= 0.2.5"}, "bin": {"express": "./bin/express"}, "directories": {"lib": "./lib/express"}, "dist": {"shasum": "ae5ee7dfbe436192adad65c7817c5ae78a8b4f93", "tarball": "https://registry.npmjs.org/express/-/express-1.0.0rc3.tgz", "integrity": "sha512-ydlKi0qmj0MJGdfUWKGtcw7RTHtDhXHoFWfFCpSIiMueLv6ZEtf8uIuwTxAVSp/zkKbJDTP1xPMYKd405/mK4A==", "signatures": [{"sig": "MEYCIQCD+4qXeCsGy2YzvcMcB12CJ1DZOVH+hT8gDrMu7xMWMAIhALhRsyoZ5z/CpVUnEdYoo6Sa9APjRhYz5gDXrOlSMprM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.2.0"}}, "3.0.0-rc5": {"name": "express", "version": "3.0.0-rc5", "dependencies": {"crc": "0.2.0", "send": "0.1.0", "debug": "*", "fresh": "0.1.0", "cookie": "0.0.4", "mkdirp": "0.3.3", "connect": "2.5.0", "methods": "0.0.1", "commander": "0.6.1", "range-parser": "0.0.4"}, "devDependencies": {"ejs": "*", "hjs": "*", "jade": "*", "mocha": "*", "should": "*", "stylus": "*", "supertest": "0.0.1", "connect-redis": "*", "github-flavored-markdown": "*"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "c63b56257f33a74498dbc0ba8986a3d5b627fc9d", "tarball": "https://registry.npmjs.org/express/-/express-3.0.0rc5.tgz", "integrity": "sha512-dHJhtDxbZyO4i7kdjBH/ZIYI3ygo68w1PoK7HL3YK2FgdbgMgqy+qvZBbnAAe9HdpMUmuH3Vg+m2P0y7klBKAA==", "signatures": [{"sig": "MEQCIEr2k6n0E5vim29/oLu8HNC1cDps/+TBqnBq3QdbyImsAiBzzL8JW8GATjacmljYAQ+yWVC7eWmYQvl6x5/aXsCOPw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "3.0.0-beta3": {"name": "express", "version": "3.0.0-beta3", "dependencies": {"crc": "0.2.0", "debug": "*", "fresh": "0.0.1", "cookie": "0.0.3", "mkdirp": "0.3.2", "connect": "2.3.3", "commander": "0.6.1"}, "devDependencies": {"ejs": "*", "hjs": "*", "jade": "*", "mocha": "*", "should": "*", "stylus": "*", "connect-redis": "*", "github-flavored-markdown": "*"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "e8425ee5f1d1c649c2e0627f437a331e9b9da867", "tarball": "https://registry.npmjs.org/express/-/express-3.0.0beta3.tgz", "integrity": "sha512-eTV9u5nDsH+fzJZPQVCOOF/Oewlr8SS2pA74TQvP9fudVWfZ+pPxvNoUewK4L8kGrYXDRIbHIvJp9qnvkK3tCA==", "signatures": [{"sig": "MEYCIQCkqVxRMsDaxYkPwT2Z5E9beIGsGhTCPM/azoNbvx+RAgIhAPiajYTqDEA5bDiX87ytKt/n5MMfmS0FD9iFHdlXcHO2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "2.0.0-rc3": {"name": "express", "version": "2.0.0-rc3", "dependencies": {"qs": ">= 0.0.6", "mime": ">= 0.0.1", "connect": ">= 1.1.0 < 2.0.0"}, "bin": {"express": "./bin/express"}, "directories": {"bin": "./bin", "lib": "./lib"}, "dist": {"shasum": "538a35c8b0e2b08c455a20528b8d6a5568e901c1", "tarball": "https://registry.npmjs.org/express/-/express-2.0.0rc3.tgz", "integrity": "sha512-LfsL0Y+8Y/bRNtlGpTDt+FOcD1c/R3dwZxdE9xRy8sOz9yqWYBiEggILWoq7jk8U1snVo47Ah495RwZkVWNVqg==", "signatures": [{"sig": "MEQCIFeYa/PV/aaAaIeYbYDOI2erKvao0sehyFA6GNrb3tybAiA8LdO4q99lrgpr10UOxNF7N84irVP0ZzV08+McBj3byw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.1 < 0.5.0"}}, "2.0.0-rc": {"name": "express", "version": "2.0.0-rc", "dependencies": {"qs": ">= 0.0.6", "mime": ">= 0.0.1", "connect": ">= 1.0.1 < 2.0.0"}, "bin": {"express": "./bin/express"}, "directories": {"bin": "./bin", "lib": "./lib"}, "dist": {"shasum": "6d3da0301b6cdce94ee437ae40ae6c8c7f5d7ccf", "tarball": "https://registry.npmjs.org/express/-/express-2.0.0rc.tgz", "integrity": "sha512-fQCbCn5TveLwoBOa54pyhOh8W7siaLL6VqE2XJMRuXbsnb5K2M9rrrpTwnlfBA0OZ8eRV9K4Wbbc5ZEk6mVQCg==", "signatures": [{"sig": "MEUCIE6vhm+XWwDlkDkAZcvVrM9Y3kICKzSpitN04todUE5QAiEAxpxcQzLXafEwLHODx5RIGutqzGtBrUht3V3MSau0tzA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.1 < 0.5.0"}}, "3.0.0-beta6": {"name": "express", "version": "3.0.0-beta6", "dependencies": {"send": "0.0.2", "debug": "*", "fresh": "0.1.0", "cookie": "0.0.3", "mkdirp": "0.3.3", "connect": "2.3.8", "methods": "0.0.1", "commander": "0.6.1", "range-parser": "0.0.4", "response-send": "0.0.1"}, "devDependencies": {"ejs": "*", "hjs": "*", "jade": "*", "mocha": "*", "should": "*", "stylus": "*", "supertest": "0.0.1", "connect-redis": "*", "github-flavored-markdown": "*"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "3eef2ed7ce7511170df4d15f4d2dade10dbc6614", "tarball": "https://registry.npmjs.org/express/-/express-3.0.0beta6.tgz", "integrity": "sha512-A7e//BYZKcK4SfCy0H4e9e7geapDlNx/mkrwRzT13f3f6AhrysaucxVZ5pQWVAW3p10QGmOa0YAo/wEqJcKtBg==", "signatures": [{"sig": "MEUCICNCKCwd/c9ocqJrc/awpwlXi3e0W+ZSpiVYYeisRcOwAiEAvDTfQY+4rSFAlPkwqyk3JKkzLcG8pvRY+4fjByc1CxE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "1.0.0-beta2": {"name": "express", "version": "1.0.0-beta2", "bin": {"express": "./bin/express"}, "directories": {"lib": "./lib/express"}, "dist": {"shasum": "4e9f6f94405c969173e09a20ba3f0d27020ec9e9", "tarball": "https://registry.npmjs.org/express/-/express-1.0.0beta2.tgz", "integrity": "sha512-gKUkVZbi2Y5e8yYXIe1DQus13PtgOtr0dSEpbrTxxfPixVtsc1luol7Ke4/rvYAnEtzcVqv2l5f/Q4nRwPQV9Q==", "signatures": [{"sig": "MEQCIHPPaQqNuMY0p9/zZ8g4b4SE2y5KS7Bd3w34z3oL8mCIAiApXeasOM2Io3jcuNwU3BQ6re4DmXZBJm51Do1WL2RefQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.1.98"}}, "2.0.0-rc2": {"name": "express", "version": "2.0.0-rc2", "dependencies": {"qs": ">= 0.0.6", "mime": ">= 0.0.1", "connect": ">= 1.1.0 < 2.0.0"}, "bin": {"express": "./bin/express"}, "directories": {"bin": "./bin", "lib": "./lib"}, "dist": {"shasum": "381e1388bcd56d0449dbbf2272975f907488f710", "tarball": "https://registry.npmjs.org/express/-/express-2.0.0rc2.tgz", "integrity": "sha512-4oBhCb0WH+/ftwIWGrtJxbN8NDcchioolcYrKckKUdu7Zy4vOQaZAks+TSSiMtOS8IYkJXIDrBMAfuerJwnLGw==", "signatures": [{"sig": "MEUCIEP4/GfEJ1WkEDmlv3mrh3wmjmHaVEFkewLP6TixshMlAiEAvI0UmPM7PeMl2OmAhENUmDgS+PZnbEocsfAnJnSLhko=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.1 < 0.5.0"}}, "2.0.0-beta2": {"name": "express", "version": "2.0.0-beta2", "dependencies": {"qs": ">= 0.0.6", "mime": ">= 0.0.1", "connect": ">= 1.0.1 < 2.0.0"}, "bin": {"express": "./bin/express"}, "directories": {"bin": "./bin", "lib": "./lib"}, "dist": {"shasum": "274e49af300145688e87ed2f5c5e59f6e26af135", "tarball": "https://registry.npmjs.org/express/-/express-2.0.0beta2.tgz", "integrity": "sha512-5X6MJse/UhJ8XJ/htQmTLQjuWMyjnthhB+dApMpUXpmk7zO7jg3DSDVxg+on/CYOcYdoZnX7tjdd90P2zu468g==", "signatures": [{"sig": "MEUCIQCVUNMHZ5LVu7+XTkwMtsjAZyXJ72GDm9A96VDBrW+NcwIgTAjY2LV3INk958BFUV31EIZzyMXJfhRVSa2Vx3nPfqM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.1 < 0.5.0"}}, "1.0.0-rc2": {"name": "express", "version": "1.0.0-rc2", "dependencies": {"connect": ">= 0.2.4"}, "bin": {"express": "./bin/express"}, "directories": {"lib": "./lib/express"}, "dist": {"shasum": "040b7790e1ab041e8218835376c5d21bba634bac", "tarball": "https://registry.npmjs.org/express/-/express-1.0.0rc2.tgz", "integrity": "sha512-af090XKSGWlAe2u3NyZpYzj6yYImoYEDwSPAnqqJ4w5Wzgy4THJ9bmO9G29XDy5/s4FkMa2ftvmn2oHxovrlKg==", "signatures": [{"sig": "MEQCIG7SacQhKorgU6rON2Yc5y/JK3iUMH8aDs74aCiwhbNVAiBolVX1AvvtE0Bbq9KLAQ8gAY4zTVgY5G1n3bbZ3qGtLg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.1.98"}}, "3.0.0-beta2": {"name": "express", "version": "3.0.0-beta2", "dependencies": {"crc": "0.2.0", "mime": "1.2.5", "debug": "*", "cookie": "0.0.3", "mkdirp": "0.3.2", "connect": "2.3.1", "commander": "0.6.1"}, "devDependencies": {"ejs": "*", "jade": "*", "mocha": "*", "should": "*", "stylus": "*", "connect-redis": "*", "github-flavored-markdown": "*"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "2755a16a2f7054c06d93f3a17dd6cbd0d5aa8698", "tarball": "https://registry.npmjs.org/express/-/express-3.0.0beta2.tgz", "integrity": "sha512-+ydx45TtZs2FuXimLEUAFNX3rLd+V1WY1PObVtNSzEi+knO7ALSvTL86RrIJRoegP1bcRQRPCUKgTkmkN1lw6g==", "signatures": [{"sig": "MEQCIDfWoS4LFJefXnbiqkACOpbwkgQ0jWsIF6ZWJKVpZEBkAiA241HG74mQmzhyK7+QBmvzwU6Da2iCmt7dKvH7x9FoxQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "1.0.0-beta": {"name": "express", "version": "1.0.0-beta", "bin": {"express": "./bin/express"}, "directories": {"lib": "./lib/express"}, "dist": {"shasum": "f8c485ec1aa2d8612c667a0fca08603abdb27246", "tarball": "https://registry.npmjs.org/express/-/express-1.0.0beta.tgz", "integrity": "sha512-rEDXYkD9v9wpt8O5/q9wWsiZa34xleLhL17feQ9i19W2987uymLU1WSwvDsqWeJHUkzKMC6GKITRsOKlnmC5XA==", "signatures": [{"sig": "MEQCIHAEt5SAyLAQO9dNV7wW6EIvkEnwCqob9DVCTQmfVrSXAiA6NmoLASoMFbtnneCvmELF4odRPii+uwV0c9B9E2i3lQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.1.98"}}, "3.0.0-alpha2": {"name": "express", "version": "3.0.0-alpha2", "dependencies": {"crc": "0.1.0", "mime": "1.2.5", "debug": "*", "mkdirp": "0.3.1", "connect": "2.2.1", "commander": "0.5.2"}, "devDependencies": {"ejs": "*", "jade": "*", "mocha": "*", "should": "*", "stylus": "*", "connect-redis": "*", "github-flavored-markdown": "*"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "e82f7ba6b2c3e678c44343d0ba4fe339ca928e6c", "tarball": "https://registry.npmjs.org/express/-/express-3.0.0alpha2.tgz", "integrity": "sha512-9wh3e8zsrMTcDZUh8BXQEg01JPJ0d+cvGc7Bn4RGDsFSuIiRR1mL30LX2BtkXlizsDBbuwfKbtuGPWq531eE5A==", "signatures": [{"sig": "MEYCIQDrDqSyHloaVVxTOUtflHJ1AVnbJZns81N/y4UHlPJtNQIhAPeVRO1AMEOqqJote+Jd3SqkF0yENkP7zjnSelMQsV79", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.5.0 < 0.7.0"}}, "2.0.0-beta": {"name": "express", "version": "2.0.0-beta", "dependencies": {"qs": ">= 0.0.6", "mime": ">= 0.0.1", "connect": ">= 1.0.1"}, "bin": {"express": "./bin/express"}, "directories": {"bin": "./bin", "lib": "./lib"}, "dist": {"shasum": "c2095479887128f161ee13211e7b886edb4d9f98", "tarball": "https://registry.npmjs.org/express/-/express-2.0.0beta.tgz", "integrity": "sha512-TphlIsE43fwc+ERX8ICA+IFfuwQv8hUIO84QJt3vwWdKyjLHhGfZYIhoZPIoOCvok0EbLngb5bBKDs7Oih1yhw==", "signatures": [{"sig": "MEUCICfVU+/M7uU5YwlHpasuGNpHdPIO7e66NwJjOwunpAOKAiEA61UnvCToXMkicLLkHwRuxPdGvfShoGjQOg2P+ZA7u/g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.1 < 0.5.0"}}, "1.0.0-rc4": {"name": "express", "version": "1.0.0-rc4", "dependencies": {"connect": ">= 0.2.6"}, "bin": {"express": "./bin/express"}, "directories": {"lib": "./lib/express"}, "dist": {"shasum": "c5363c021717c02728c692fedc632cac9a869160", "tarball": "https://registry.npmjs.org/express/-/express-1.0.0rc4.tgz", "integrity": "sha512-OgxMiewmSH12ZIQyCU9183l6B/sn1Z/aF78Mi60T8j7nu+ReXNGLhxZ5aRjQBmVSEJ7thWzFJYF4oOJx9KpJKA==", "signatures": [{"sig": "MEUCIQDV+ckhXWsXKBbrSOaGn6eZgC+Mxg9BKtnflruRGaTO9AIgILrMPN583XoniXzjdADf36yH31WEfUYucgvxesAPraA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.2.0"}}, "3.0.0-rc3": {"name": "express", "version": "3.0.0-rc3", "dependencies": {"crc": "0.2.0", "send": "0.0.3", "debug": "*", "fresh": "0.1.0", "cookie": "0.0.4", "mkdirp": "0.3.3", "connect": "2.4.3", "methods": "0.0.1", "commander": "0.6.1", "range-parser": "0.0.4"}, "devDependencies": {"ejs": "*", "hjs": "*", "jade": "*", "mocha": "*", "should": "*", "stylus": "*", "supertest": "0.0.1", "connect-redis": "*", "github-flavored-markdown": "*"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "740d4e14335a1e92a19493930def0c747a0367b4", "tarball": "https://registry.npmjs.org/express/-/express-3.0.0rc3.tgz", "integrity": "sha512-IGH67Csjt0NXAYbVQAEBVeJL92rFr3kXF/5UCCcEmcBSX+mFibKOJks7CuB1cyD7W0DBF2GQylAqda7rocMzWA==", "signatures": [{"sig": "MEUCIQC7mDjvGzKWN0j2XHy07a7WCGO30RIfAAySJ/R4W/TLCgIgDKT+BWUw4+pypKPdFobLX4qn+si9vCnraOZSLK1R5Ls=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "3.0.0-alpha3": {"name": "express", "version": "3.0.0-alpha3", "dependencies": {"crc": "0.2.0", "mime": "1.2.5", "debug": "*", "mkdirp": "0.3.1", "connect": "2.2.2", "commander": "0.5.2"}, "devDependencies": {"ejs": "*", "jade": "*", "mocha": "*", "should": "*", "stylus": "*", "connect-redis": "*", "github-flavored-markdown": "*"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "a65af40b696d39310c434d810adc9c4942fc2f9c", "tarball": "https://registry.npmjs.org/express/-/express-3.0.0alpha3.tgz", "integrity": "sha512-A+ti/ChoNqX+IR1J9D9xOFTQI72Xzuy+/IPXlVMLgkXTPFx299ec3lCw2qsP+owY2WxIhUAvPR8yYPV46WW8Xw==", "signatures": [{"sig": "MEYCIQCIroIJ7VILvMDdUEDE5cAB3klCWsl4nrYv6fuDiGZxYAIhALxSC0yFX9IR15bBEFU2hvsf/JyKXglCjoiwyKkNys19", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "3.0.0-rc4": {"name": "express", "version": "3.0.0-rc4", "dependencies": {"crc": "0.2.0", "send": "0.0.4", "debug": "*", "fresh": "0.1.0", "cookie": "0.0.4", "mkdirp": "0.3.3", "connect": "2.4.4", "methods": "0.0.1", "commander": "0.6.1", "range-parser": "0.0.4"}, "devDependencies": {"ejs": "*", "hjs": "*", "jade": "*", "mocha": "*", "should": "*", "stylus": "*", "supertest": "0.0.1", "connect-redis": "*", "github-flavored-markdown": "*"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "f07490f3578a87e06d4244d58c18d6f6e2c5fc33", "tarball": "https://registry.npmjs.org/express/-/express-3.0.0rc4.tgz", "integrity": "sha512-Vx40pGbuHjmtjIlZfqGxksj3lsAH2uCVT1n1L+w9exylRv506klbjA3+U/qr0lY7s/4P0h780+gUcC8Uom7BYg==", "signatures": [{"sig": "MEUCIQDnZfboOkKSlCR+PXpI+Rw8gSWRT1Or3S8OFlTzSQleIQIgFFONd+L0TTqWEqjIyoibQUm2VpxMBQUMNVHX0qZyMfI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "3.3.7": {"name": "express", "version": "3.3.7", "dependencies": {"send": "0.1.4", "debug": "*", "fresh": "0.2.0", "cookie": "0.1.0", "mkdirp": "0.3.5", "connect": "2.8.7", "methods": "0.0.1", "commander": "1.2.0", "buffer-crc32": "0.2.1", "range-parser": "0.0.4", "cookie-signature": "1.0.1"}, "devDependencies": {"ejs": "*", "hjs": "*", "jade": "0.30.0", "mocha": "*", "marked": "*", "should": "*", "stylus": "*", "supertest": "0.6.0", "connect-redis": "*"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "de0b67ae1b04999fe7141940c2749f5b435a8fcd", "tarball": "https://registry.npmjs.org/express/-/express-3.3.7.tgz", "integrity": "sha512-esS7Oe17bfuikOkI/Ouw9To8c6wM978pDfcgR40pk46Z4wYuV2+39WXxcc4oaaP8sbKJQ1T/8RZGrP+ANWKPzg==", "signatures": [{"sig": "MEYCIQCB0NHQ0J34UKMMJSqV0UzENkAlucJzLieQ7UmAWOizzAIhAIS2/BbXubmnRofw99AaRGpmAzN6SIgoP9T+RzVxf38m", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "3.3.8": {"name": "express", "version": "3.3.8", "dependencies": {"send": "0.1.4", "debug": "*", "fresh": "0.2.0", "cookie": "0.1.0", "mkdirp": "0.3.5", "connect": "2.8.8", "methods": "0.0.1", "commander": "1.2.0", "buffer-crc32": "0.2.1", "range-parser": "0.0.4", "cookie-signature": "1.0.1"}, "devDependencies": {"ejs": "*", "hjs": "*", "jade": "0.30.0", "mocha": "*", "marked": "*", "should": "*", "stylus": "*", "supertest": "0.6.0", "connect-redis": "*"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "8e98ac30d81f4c95b85d71d2af6cf84f62ef19bd", "tarball": "https://registry.npmjs.org/express/-/express-3.3.8.tgz", "integrity": "sha512-zWTuSlv+cvqsPsQmHJYYWEKyzwThpin6QxNqU9f0QgWvtksqcEu5FHnzcn/QnL/45gteWLBBZiSzhG2Q5yOqpg==", "signatures": [{"sig": "MEUCIHiUJptb6Yz0cKSkv1/99XW4x4dMl+yk096tNXne8F9iAiEAkUOYlAfJLrz+4A1K4Rf6pqnqbipmsX05e/KYI9r40eU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "3.4.0": {"name": "express", "version": "3.4.0", "dependencies": {"send": "0.1.4", "debug": "*", "fresh": "0.2.0", "cookie": "0.1.0", "mkdirp": "0.3.5", "connect": "2.9.0", "methods": "0.0.1", "commander": "1.2.0", "buffer-crc32": "0.2.1", "range-parser": "0.0.4", "cookie-signature": "1.0.1"}, "devDependencies": {"ejs": "*", "hjs": "*", "jade": "0.30.0", "mocha": "*", "marked": "*", "should": "*", "stylus": "*", "supertest": "0.6.0", "connect-redis": "*"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "6ed289da0d5f55ac30997cf832e5fc36f784071e", "tarball": "https://registry.npmjs.org/express/-/express-3.4.0.tgz", "integrity": "sha512-R<PERSON>joaiTCsjvIMwTadbeSxQytaYP0vYDUwd8WWMpp+Qw55+wVeDyUAMIt8vLqMlhALfjwtlBzkmHBacuG4hx9Jg==", "signatures": [{"sig": "MEUCIB3tCJuUi9vS9ElerFX5Ml7A8sRSZgYurOU/mHm4HJc5AiEAi7F/jqtOanFyC2UX+20LWhdQl17JxuRWPPPM6EJYroU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "3.4.1": {"name": "express", "version": "3.4.1", "dependencies": {"send": "0.1.4", "debug": "*", "fresh": "0.2.0", "cookie": "0.1.0", "mkdirp": "0.3.5", "connect": "2.9.1", "methods": "0.0.1", "commander": "2.0.0", "buffer-crc32": "0.2.1", "range-parser": "0.0.4", "cookie-signature": "1.0.1"}, "devDependencies": {"ejs": "*", "hjs": "*", "jade": "0.30.0", "mocha": "*", "marked": "*", "should": "2", "stylus": "*", "supertest": "0.6.0", "connect-redis": "*"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "3b4fb8862b6a1dfce3dc760629833d0cfef9314c", "tarball": "https://registry.npmjs.org/express/-/express-3.4.1.tgz", "integrity": "sha512-SkKTH3OFcgQi6cBc/gR4AGwYX93+iHbrqOEeLqm8qlyrlP3MbOz6W8bXS9fCJ8DAnva2m2TGi8pQ2ZQINNisFA==", "signatures": [{"sig": "MEUCIQDlKLGInfhlw8diyvfn4JsjB5YuA1LLM631ORwQ8WGoNgIgN7FSTxdFQLBAhyI/jUV3Rxyx6xUNQuvpGfsuUXpvPjU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "3.4.2": {"name": "express", "version": "3.4.2", "dependencies": {"send": "0.1.4", "debug": "*", "fresh": "0.2.0", "cookie": "0.1.0", "mkdirp": "0.3.5", "connect": "2.9.2", "methods": "0.0.1", "commander": "1.3.2", "buffer-crc32": "0.2.1", "range-parser": "0.0.4", "cookie-signature": "1.0.1"}, "devDependencies": {"ejs": "*", "hjs": "*", "jade": "0.30.0", "mocha": "*", "marked": "*", "should": "2", "stylus": "*", "supertest": "0.6.0", "connect-redis": "*"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "3cfaa66fb1e1fac5012129b473f0e2143544aa07", "tarball": "https://registry.npmjs.org/express/-/express-3.4.2.tgz", "integrity": "sha512-4RHDftomQ/BVJYPzNHoxfQY9daj+3p5p/BJD8SCjVQZ0MaT4nnkFdTgmdfLdMLJ8PdEJ7WdLs5Yj3tjg+zqqUQ==", "signatures": [{"sig": "MEUCICb9Lb6Ft5R6g14GW9w+FTiIjrD4u9L4nLXdXOlZ89HtAiEA2G1JfKlhqD34RuxFy6meKrTvRfMfPnbM1JLX5eYgkXI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "3.4.3": {"name": "express", "version": "3.4.3", "dependencies": {"send": "0.1.4", "debug": "*", "fresh": "0.2.0", "cookie": "0.1.0", "mkdirp": "0.3.5", "connect": "2.10.1", "methods": "0.0.1", "commander": "1.3.2", "buffer-crc32": "0.2.1", "range-parser": "0.0.4", "cookie-signature": "1.0.1"}, "devDependencies": {"ejs": "*", "hjs": "*", "jade": "0.30.0", "mocha": "*", "marked": "*", "should": "2", "stylus": "*", "supertest": "0.6.0", "connect-redis": "*"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "d0d237d60cd9c741b50da88379527e2a1d804627", "tarball": "https://registry.npmjs.org/express/-/express-3.4.3.tgz", "integrity": "sha512-X8SwkyUeKi2awhef/H0HgEsW1xxl/o+nO2Rryd9TKUKkn60D3X+71nL6LdHRAYTDNmcFdJVqjkzip9UHbAv61A==", "signatures": [{"sig": "MEYCIQD6xGJUnsp2zRGq7Z5X1qSW7sP07De4otoS9J8thry5lQIhAPWGZBDiZcU6n/IMllhWoMp6kfUGzJ2aZ920c+2b8/Ot", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "3.4.4": {"name": "express", "version": "3.4.4", "dependencies": {"send": "0.1.4", "debug": "*", "fresh": "0.2.0", "cookie": "0.1.0", "mkdirp": "0.3.5", "connect": "2.11.0", "methods": "0.1.0", "commander": "1.3.2", "buffer-crc32": "0.2.1", "range-parser": "0.0.4", "cookie-signature": "1.0.1"}, "devDependencies": {"ejs": "*", "hjs": "*", "jade": "0.30.0", "mocha": "*", "marked": "*", "should": "2", "stylus": "*", "supertest": "0.8.1 - 1", "connect-redis": "*"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "0b63ae626c96b71b78d13dfce079c10351635a86", "tarball": "https://registry.npmjs.org/express/-/express-3.4.4.tgz", "integrity": "sha512-uM3G4rpA6QPWsr35/6xIYXlL1Ti6kW0T+BTLF4hEJgeO9TYKbW2a9wDB2mENvu8QEIYV2mSGKcbRyFC6XFrstA==", "signatures": [{"sig": "MEUCIBK7AvWbzrRkdmJSRN8xxdeDSDdKYlzlLVKXPen5DeoWAiEAj2gzcGY/yTyqvSCKCZrO5JKUwngHgouwwkHx8sOQ5Ac=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "3.4.5": {"name": "express", "version": "3.4.5", "dependencies": {"send": "0.1.4", "debug": ">= 0.7.3 < 1", "fresh": "0.2.0", "cookie": "0.1.0", "mkdirp": "0.3.5", "connect": "2.11.1", "methods": "0.1.0", "commander": "1.3.2", "buffer-crc32": "0.2.1", "range-parser": "0.0.4", "cookie-signature": "1.0.1"}, "devDependencies": {"ejs": "~0.8.4", "hjs": "~0.0.6", "jade": "~0.30.0", "mocha": "~1.14.0", "marked": "0.2.10", "should": "~2.0.2", "stylus": "~0.40.0", "supertest": "~0.8.1", "connect-redis": "~1.4.5"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "dc82aa4d932f0d0ee93e8e7ee9824d73bb00d47a", "tarball": "https://registry.npmjs.org/express/-/express-3.4.5.tgz", "integrity": "sha512-7+XrhbuIF0<PERSON>lov+q5BsYgFBXGwwLByBBLEEf6Gc/FdQ3rX6jR6d/jTdkuxD35AgIJCW2irhOAZ0BQRkMrY3/pQ==", "signatures": [{"sig": "MEQCIBZMd2LlSMTBzMCQ3/JjkXdF3K7dtOFUa5olw4Pp6L1fAiBN8uz6jnnyjvafhF6QuJi4/EGhD4NxWjgyVoeIqENa/w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "3.4.6": {"name": "express", "version": "3.4.6", "dependencies": {"send": "0.1.4", "debug": ">= 0.7.3 < 1", "fresh": "0.2.0", "cookie": "0.1.0", "mkdirp": "0.3.5", "connect": "2.11.2", "methods": "0.1.0", "commander": "1.3.2", "buffer-crc32": "0.2.1", "range-parser": "0.0.4", "cookie-signature": "1.0.1"}, "devDependencies": {"ejs": "~0.8.4", "hjs": "~0.0.6", "jade": "~0.30.0", "mocha": "~1.14.0", "marked": "0.2.10", "should": "~2.0.2", "stylus": "~0.40.0", "supertest": "~0.8.1", "connect-redis": "~1.4.5"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "85b6004076f9004f806e9f49c90487d1f6f89c43", "tarball": "https://registry.npmjs.org/express/-/express-3.4.6.tgz", "integrity": "sha512-QC38nmyD0vAFJzZAPxqcw64hTrYRWolwRMIbzz6J3QaBV6xA2iq8Ik0fvpEC73RzKaFubGGNOdYCrNbE0Juhjw==", "signatures": [{"sig": "MEQCIDsQ/jIVBiyQ8o/cmMzG+JC8oXNevVl4cKNtRFKlWctGAiA9YoRBhwlfJWfsgbMrMJijdDF473dVKVe+Rc+wZlP9nQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "3.4.7": {"name": "express", "version": "3.4.7", "dependencies": {"send": "0.1.4", "debug": ">= 0.7.3 < 1", "fresh": "0.2.0", "cookie": "0.1.0", "mkdirp": "0.3.5", "connect": "2.12.0", "methods": "0.1.0", "commander": "1.3.2", "buffer-crc32": "0.2.1", "range-parser": "0.0.4", "cookie-signature": "1.0.1", "merge-descriptors": "0.0.1"}, "devDependencies": {"ejs": "~0.8.4", "hjs": "~0.0.6", "jade": "~0.30.0", "mocha": "~1.15.1", "marked": "0.2.10", "should": "~2.1.1", "stylus": "~0.40.0", "supertest": "~0.8.1", "connect-redis": "~1.4.5"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "3b939c47d2aa44dfecf77d50da2123c5bd313366", "tarball": "https://registry.npmjs.org/express/-/express-3.4.7.tgz", "integrity": "sha512-XZoRDfeu9IFZsn1piZvejXmuVKTsVIMo//djeEPE/A0CDUjEhXl1TphTkDvH9KZeAW3IVxabFd6ZcXRoZiqhxg==", "signatures": [{"sig": "MEYCIQDOKC3SRzgwDAHQs5kftBMYXSztaZbx50mNPKLUSidlEwIhANr/q8NCwwjXLLAObT0weUrW2fk4t1aYMRATzdixWGKO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "3.4.8": {"name": "express", "version": "3.4.8", "dependencies": {"send": "0.1.4", "debug": ">= 0.7.3 < 1", "fresh": "0.2.0", "cookie": "0.1.0", "mkdirp": "0.3.5", "connect": "2.12.0", "methods": "0.1.0", "commander": "1.3.2", "buffer-crc32": "0.2.1", "range-parser": "0.0.4", "cookie-signature": "1.0.1", "merge-descriptors": "0.0.1"}, "devDependencies": {"ejs": "~0.8.4", "hjs": "~0.0.6", "jade": "~0.30.0", "mocha": "~1.15.1", "marked": "0.2.10", "should": "~2.1.1", "stylus": "~0.40.0", "supertest": "~0.8.1", "connect-redis": "~1.4.5"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "aa7a8986de07053337f4bc5ed9a6453d9cc8e2e1", "tarball": "https://registry.npmjs.org/express/-/express-3.4.8.tgz", "integrity": "sha512-NC6Ff/tlg4JNjGTrw0is0aOe9k7iAnb3Ra6mF3Be15UscxZKpbP7XCMmXx9EiNpHe9IClbHo6EDslH9eJNo1HQ==", "signatures": [{"sig": "MEUCIQCHBJot6a6ygctAGph8LdPwbrH382ZRYyGeCjJDHSMO7gIgeCC0g0URp/GaSnz+tGbwxUPKMU9U+8ZXAUxXYFZKC1I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "4.0.0-rc1": {"name": "express", "version": "4.0.0-rc1", "dependencies": {"qs": "0.6.6", "send": "0.2.0", "debug": ">= 0.7.3 < 1", "fresh": "0.2.2", "cookie": "0.1.0", "accepts": "1.0.0", "methods": "0.1.0", "type-is": "1.0.0", "escape-html": "1.0.1", "utils-merge": "1.0.0", "buffer-crc32": "0.2.1", "range-parser": "1.0.0", "cookie-signature": "1.0.3", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~0.8.4", "hjs": "~0.0.6", "jade": "~0.30.0", "mocha": "~1.15.1", "marked": "0.2.10", "morgan": "1.0.0", "should": "~2.1.1", "stylus": "~0.40.0", "supertest": "~0.8.1", "body-parser": "1.0.0", "connect-redis": "~1.4.5", "cookie-parser": "1.0.1", "static-favicon": "1.0.0", "express-session": "1.0.1"}, "dist": {"shasum": "a9f3f89e4726e2ff60f62ab625c960eaa2cba3a6", "tarball": "https://registry.npmjs.org/express/-/express-4.0.0-rc1.tgz", "integrity": "sha512-scu1yQVGyTszbHw5t0HHeW4qr5n1aNCSQQTT72p9VyKa+Wy/VGT8dFpSEOUuoXgrkS5J6zaRjzPo1p8YQ5lBNw==", "signatures": [{"sig": "MEUCIBqvOgh2X9M8yn2B4Y6f1F4g1jK00dlx0QzWeZGnb2JnAiEA69V2grTHv9PEFJxQNaoMQE+L7nhRP2QMrcFhTBUUiwo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "4.0.0-rc2": {"name": "express", "version": "4.0.0-rc2", "dependencies": {"qs": "0.6.6", "send": "0.2.0", "debug": ">= 0.7.3 < 1", "fresh": "0.2.2", "cookie": "0.1.0", "accepts": "1.0.0", "methods": "0.1.0", "type-is": "1.0.0", "escape-html": "1.0.1", "utils-merge": "1.0.0", "buffer-crc32": "0.2.1", "range-parser": "1.0.0", "cookie-signature": "1.0.3", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~0.8.4", "hjs": "~0.0.6", "jade": "~0.30.0", "mocha": "~1.15.1", "marked": "0.2.10", "morgan": "1.0.0", "should": "~2.1.1", "stylus": "~0.40.0", "supertest": "~0.8.1", "body-parser": "1.0.0", "connect-redis": "~1.4.5", "cookie-parser": "1.0.1", "static-favicon": "1.0.0", "express-session": "1.0.1"}, "dist": {"shasum": "0b3fc3b853b393cdb5042dc9960498015ed06b96", "tarball": "https://registry.npmjs.org/express/-/express-4.0.0-rc2.tgz", "integrity": "sha512-OF+eisQk7uRLO2sV/cjU0miUpTSASy/kQtVmPDuXU7ZSUuN8x4Lyp6siNNK5qNwuj7rVHdIvsoAyXzPhr7iEOg==", "signatures": [{"sig": "MEUCIQDRZJr/hNX5LcmBzMVVYkj4wg1M2vsbt/qH9mmUd3wO2gIgW9vXRvwkD6203JtKk/CffbMBelAX1hSr3RwlNk/X4OI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "3.5.0": {"name": "express", "version": "3.5.0", "dependencies": {"send": "0.2.0", "debug": ">= 0.7.3 < 1", "fresh": "0.2.2", "cookie": "0.1.1", "mkdirp": "0.3.5", "connect": "2.14.1", "methods": "0.1.0", "commander": "1.3.2", "buffer-crc32": "0.2.1", "range-parser": "1.0.0", "cookie-signature": "1.0.3", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~0.8.4", "hjs": "~0.0.6", "jade": "~0.30.0", "mocha": "~1.17.1", "marked": "0.2.10", "should": "~2.1.1", "stylus": "~0.40.0", "supertest": "~0.9.0", "connect-redis": "~1.4.5"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "703f299aa2a7fce122025b61a2e170d536b35019", "tarball": "https://registry.npmjs.org/express/-/express-3.5.0.tgz", "integrity": "sha512-656cQZf60/1+uMB8wbqPriK7oLVh3nlY3gMI5YVzQSGfW2ZU3UBP2wnrz9bpdwYUsjvL3PziJF6DMvpIm6z+Ew==", "signatures": [{"sig": "MEQCID08tKYerKAljYxUuJiNQvwf8Hw+jG+nFRs845LDSRt6AiBWdqaV2VktiEgsGkYUZjQpZdqvX9rrCexPSpDg19NPTg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "4.0.0-rc3": {"name": "express", "version": "4.0.0-rc3", "dependencies": {"qs": "0.6.6", "send": "0.2.0", "debug": ">= 0.7.3 < 1", "fresh": "0.2.2", "cookie": "0.1.0", "accepts": "1.0.0", "methods": "0.1.0", "type-is": "1.0.0", "parseurl": "1.0.1", "escape-html": "1.0.1", "utils-merge": "1.0.0", "buffer-crc32": "0.2.1", "range-parser": "1.0.0", "serve-static": "1.0.1", "path-to-regexp": "0.1.0", "cookie-signature": "1.0.3", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~0.8.4", "hjs": "~0.0.6", "jade": "~0.30.0", "mocha": "~1.15.1", "vhost": "1.0.0", "marked": "0.2.10", "morgan": "1.0.0", "should": "~2.1.1", "stylus": "~0.40.0", "supertest": "~0.8.1", "body-parser": "1.0.0", "connect-redis": "~1.4.5", "cookie-parser": "1.0.1", "static-favicon": "1.0.0", "express-session": "1.0.1"}, "dist": {"shasum": "da0113235684e89d36bd7796440809e889ee8692", "tarball": "https://registry.npmjs.org/express/-/express-4.0.0-rc3.tgz", "integrity": "sha512-9WTQNRz1q1Dc8qzil31iSj2JaLcYs45jSf1JaddYZesUbyeeKSOrjcqO7H7tCk9Ler0BGvQpnjau8yi8G33d1Q==", "signatures": [{"sig": "MEUCIQD7FoOEinN6INUiSJpRb2wnSiWTs9KhMDlhJRXEhJUBKgIgAj79+pcL3cVtn5PCzN7YUtXpgrlLBAh/faRtrKGD0mg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "4.0.0-rc4": {"name": "express", "version": "4.0.0-rc4", "dependencies": {"qs": "0.6.6", "send": "0.2.0", "debug": ">= 0.7.3 < 1", "fresh": "0.2.2", "cookie": "0.1.0", "accepts": "1.0.0", "methods": "0.1.0", "type-is": "1.0.0", "parseurl": "1.0.1", "escape-html": "1.0.1", "utils-merge": "1.0.0", "buffer-crc32": "0.2.1", "range-parser": "1.0.0", "serve-static": "1.0.1", "path-to-regexp": "0.1.2", "cookie-signature": "1.0.3", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~0.8.4", "hjs": "~0.0.6", "jade": "~0.30.0", "mocha": "~1.15.1", "vhost": "1.0.0", "marked": "0.2.10", "morgan": "1.0.0", "should": "~2.1.1", "stylus": "~0.40.0", "supertest": "~0.8.1", "body-parser": "1.0.0", "connect-redis": "~1.4.5", "cookie-parser": "1.0.1", "static-favicon": "1.0.0", "express-session": "1.0.1"}, "dist": {"shasum": "1cedc8790f47b776b9d100f5388e5fb652ea4388", "tarball": "https://registry.npmjs.org/express/-/express-4.0.0-rc4.tgz", "integrity": "sha512-9p+A5sMkfZBHKh32WwirHbK7HM+aQYwLBtK/kzcHsD6lfOjfpzXJHJses3l+Gdl9qpvuC96Ss1j/AYwUdS2FvA==", "signatures": [{"sig": "MEYCIQCvXhSs5+vLQXa3woRgHnMWVwWMfdBFVNhzpvL+TZr4vgIhAMBYgXrEZoG8gS3rZB7Bb8zZfn9KJJDYOLBWYQ+6tU4H", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "3.5.1": {"name": "express", "version": "3.5.1", "dependencies": {"send": "0.2.0", "debug": ">= 0.7.3 < 1", "fresh": "0.2.2", "cookie": "0.1.1", "mkdirp": "0.3.5", "connect": "2.14.1", "methods": "0.1.0", "commander": "1.3.2", "buffer-crc32": "0.2.1", "range-parser": "1.0.0", "cookie-signature": "1.0.3", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~0.8.4", "hjs": "~0.0.6", "jade": "~0.30.0", "mocha": "~1.17.1", "marked": "0.2.10", "should": "~2.1.1", "stylus": "~0.40.0", "supertest": "~0.9.0", "connect-redis": "~1.4.5"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "4b333e1117faca336a538f4c724140b9ce1a87e7", "tarball": "https://registry.npmjs.org/express/-/express-3.5.1.tgz", "integrity": "sha512-e/StwaOKfAPR9RpHagNZ4HudkqcfD2PB8yjwNe3pTFHxIaF2+NHOjmVHbfgdmWtX/8jXSjjVTLPmO09S/6MaWg==", "signatures": [{"sig": "MEYCIQDGp24F4BhQBbjkqwQY4QioPRtaXoXg1RkoQ88PHgw+cAIhAOtpp3Vxr3cECQsdC1AlKtxVp1J6cT7jVnNiigICRmVt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "4.0.0": {"name": "express", "version": "4.0.0", "dependencies": {"qs": "0.6.6", "send": "0.2.0", "debug": ">= 0.7.3 < 1", "fresh": "0.2.2", "cookie": "0.1.0", "accepts": "1.0.0", "methods": "0.1.0", "type-is": "1.0.0", "parseurl": "1.0.1", "escape-html": "1.0.1", "utils-merge": "1.0.0", "buffer-crc32": "0.2.1", "range-parser": "1.0.0", "serve-static": "1.0.1", "path-to-regexp": "0.1.2", "cookie-signature": "1.0.3", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~0.8.4", "hjs": "~0.0.6", "jade": "~0.30.0", "mocha": "~1.15.1", "vhost": "1.0.0", "marked": "0.2.10", "morgan": "1.0.0", "should": "~2.1.1", "stylus": "~0.40.0", "supertest": "~0.8.1", "body-parser": "1.0.0", "connect-redis": "~1.4.5", "cookie-parser": "1.0.1", "static-favicon": "1.0.0", "express-session": "1.0.1"}, "dist": {"shasum": "274dc82933c9f574cc38a0ce5ea8172be9c6b094", "tarball": "https://registry.npmjs.org/express/-/express-4.0.0.tgz", "integrity": "sha512-HP2D9TkAYTAfau6FklzmchQQH/7Dh/JmbrbiJanV80rO12Kc00z5tDrqahBG3fR6x/RuUZvpEwiQ91b7UhJ8qQ==", "signatures": [{"sig": "MEYCIQCxdGGcaJPv00k7b2IW/JOvFwwtihe5qGAQSPAGFwPb3gIhANW59vG/BhRSp+tQgjZJIWTLrQZ6A7hGRJO5lqtBDMiP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "3.5.2": {"name": "express", "version": "3.5.2", "dependencies": {"send": "0.3.0", "debug": ">= 0.7.3 < 1", "fresh": "0.2.2", "cookie": "0.1.2", "mkdirp": "0.4.0", "connect": "2.14.5", "methods": "0.1.0", "commander": "1.3.2", "buffer-crc32": "0.2.1", "range-parser": "1.0.0", "cookie-signature": "1.0.3", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~0.8.4", "hjs": "~0.0.6", "jade": "~0.30.0", "mocha": "~1.18.2", "marked": "0.2.10", "should": "~2.1.1", "stylus": "~0.40.0", "supertest": "~0.11.0", "connect-redis": "~1.4.5"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "aab0d2b31ef21259eac24dc45c43378fcf144b6d", "tarball": "https://registry.npmjs.org/express/-/express-3.5.2.tgz", "integrity": "sha512-dMrC/Ub9OEeHp+3nq5Y0LJp9i1bKC5HFGD5NDx6S+gXWliWmJe6TC7KPvIW4JVdzIV2COSAc3cv6krU0/giMKA==", "signatures": [{"sig": "MEYCIQDxz2otwY2lo/SHAgFik7p0vU94REDC01jjaJZbBjollQIhANAZlv5Y7a9yCyly+qJTN6naUIylefgszOv+s/qiplfd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "4.1.0": {"name": "express", "version": "4.1.0", "dependencies": {"qs": "0.6.6", "send": "0.3.0", "debug": ">= 0.7.3 < 1", "fresh": "0.2.2", "cookie": "0.1.2", "accepts": "1.0.1", "methods": "0.1.0", "type-is": "1.1.0", "parseurl": "1.0.1", "escape-html": "1.0.1", "utils-merge": "1.0.0", "buffer-crc32": "0.2.1", "range-parser": "1.0.0", "serve-static": "1.1.0", "path-to-regexp": "0.1.2", "cookie-signature": "1.0.3", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~0.35.0", "mocha": "~1.18.2", "vhost": "1.0.0", "marked": "0.3.2", "morgan": "1.0.0", "should": "~3.3.1", "supertest": "~0.11.0", "multiparty": "~3.2.4", "body-parser": "1.0.2", "connect-redis": "~2.0.0", "cookie-parser": "1.0.1", "static-favicon": "1.0.2", "express-session": "1.0.3", "method-override": "1.0.0"}, "dist": {"shasum": "a822be824cf88e8ad67ec5df75d02887de6058b4", "tarball": "https://registry.npmjs.org/express/-/express-4.1.0.tgz", "integrity": "sha512-3hAH24+ledW5rsB5xRbSLX2NpXicKMZ4CP5Ak0CRdVDY5VI0MURMgU8DeO+GF1/bkLNn1GzpiYxIyJtecDiycA==", "signatures": [{"sig": "MEUCIQDg6VRMV7cO2kSrrHTLBT5x929XE8ZjGwJ7X98Ojmi5XAIgc2ilNbfQx2nK0e6yluEE154d8cbH5Qw7lsqDhFJQzxM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "4.1.1": {"name": "express", "version": "4.1.1", "dependencies": {"qs": "0.6.6", "send": "0.3.0", "debug": ">= 0.7.3 < 1", "fresh": "0.2.2", "cookie": "0.1.2", "accepts": "1.0.1", "methods": "0.1.0", "type-is": "1.1.0", "parseurl": "1.0.1", "escape-html": "1.0.1", "utils-merge": "1.0.0", "buffer-crc32": "0.2.1", "range-parser": "1.0.0", "serve-static": "1.1.0", "path-to-regexp": "0.1.2", "cookie-signature": "1.0.3", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~0.35.0", "mocha": "~1.18.2", "vhost": "1.0.0", "marked": "0.3.2", "morgan": "1.0.0", "should": "~3.3.1", "supertest": "~0.11.0", "multiparty": "~3.2.4", "body-parser": "1.0.2", "connect-redis": "~2.0.0", "cookie-parser": "1.0.1", "static-favicon": "1.0.2", "express-session": "1.0.3", "method-override": "1.0.0"}, "dist": {"shasum": "266f08c3cbc21fc1831e954073dda8cf3cae002f", "tarball": "https://registry.npmjs.org/express/-/express-4.1.1.tgz", "integrity": "sha512-Zj9yrLFBbLnb7mGGXokxFmGvu9070lyn2JB40wNLiuw0eDQo5pCIYUM4gc43M0jwGpPtN2L/emBX6DovNApvQA==", "signatures": [{"sig": "MEUCIFARKShxvfMpUFaKdqMR1GdZL4IBcUfVsFZyKGNgNmUiAiEApPhZqJgleh88vsytgm0wTeK+FLpOmlaFVASaN4NaG4k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "3.5.3": {"name": "express", "version": "3.5.3", "dependencies": {"send": "0.3.0", "debug": ">= 0.7.3 < 1", "fresh": "0.2.2", "cookie": "0.1.2", "mkdirp": "0.4.0", "connect": "2.14.5", "methods": "0.1.0", "commander": "1.3.2", "buffer-crc32": "0.2.1", "range-parser": "1.0.0", "cookie-signature": "1.0.3", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~0.8.4", "hjs": "~0.0.6", "jade": "~0.30.0", "mocha": "~1.18.2", "marked": "0.2.10", "should": "~2.1.1", "stylus": "~0.40.0", "supertest": "~0.11.0", "connect-redis": "~1.4.5"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "af440e1ddad078934ec78241420b40bbc56dc2ad", "tarball": "https://registry.npmjs.org/express/-/express-3.5.3.tgz", "integrity": "sha512-48nmJguLFuSN/bzWQiXMVPbVpPHj/kc7lL02+w2Y8OYfS2ebx0r48zkLzU9bGNlO6NHR/BPHZrIGnurGiFeD3g==", "signatures": [{"sig": "MEUCIHW4D9svMCvy4FYoqBZx52oRncfkyGgWZc+X7H1dI82wAiEAzl5e1JUB6UxMqDVhZwvPHuTDlDMFJh7s+OwYC686hx0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "4.1.2": {"name": "express", "version": "4.1.2", "dependencies": {"qs": "0.6.6", "send": "0.3.0", "debug": ">= 0.7.3 < 1", "fresh": "0.2.2", "cookie": "0.1.2", "accepts": "1.0.1", "methods": "0.1.0", "type-is": "1.1.0", "parseurl": "1.0.1", "escape-html": "1.0.1", "utils-merge": "1.0.0", "buffer-crc32": "0.2.1", "range-parser": "1.0.0", "serve-static": "1.1.0", "path-to-regexp": "0.1.2", "cookie-signature": "1.0.3", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~0.35.0", "mocha": "~1.18.2", "vhost": "1.0.0", "marked": "0.3.2", "morgan": "1.0.0", "should": "~3.3.1", "supertest": "~0.11.0", "multiparty": "~3.2.4", "body-parser": "1.0.2", "connect-redis": "~2.0.0", "cookie-parser": "1.0.1", "static-favicon": "1.0.2", "express-session": "1.0.3", "method-override": "1.0.0"}, "dist": {"shasum": "cb1d114255718a65a1bcd6958036ef720c529487", "tarball": "https://registry.npmjs.org/express/-/express-4.1.2.tgz", "integrity": "sha512-U7G/TwoXu9uSHtGnvW6WQyLz3rP+qp3sHUiNpKw5Mz2o6DGenpxngH5tGnxgg09Hjqzje2CzZKukPoZfemepYQ==", "signatures": [{"sig": "MEQCICjM49y198GcqVtODS6Vy9tlzARg/ugNb2YFDho83cxWAiBQwm7CSOjyVvSvAoy0xFHnIKtHGxK2fP0LQJENYYY0lA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "3.6.0": {"name": "express", "version": "3.6.0", "dependencies": {"send": "0.3.0", "debug": ">= 0.8.0 < 1", "fresh": "0.2.2", "cookie": "0.1.2", "mkdirp": "0.5.0", "connect": "2.15.0", "methods": "1.0.0", "commander": "1.3.2", "buffer-crc32": "0.2.1", "range-parser": "1.0.0", "cookie-signature": "1.0.3", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~0.8.4", "hjs": "~0.0.6", "jade": "~0.30.0", "mocha": "~1.18.2", "marked": "0.2.10", "should": "~2.1.1", "stylus": "~0.40.0", "supertest": "~0.12.1", "connect-redis": "~1.4.5"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "94c7b0f8f506b046d4d9770b40992f224026e5d5", "tarball": "https://registry.npmjs.org/express/-/express-3.6.0.tgz", "integrity": "sha512-7f4V19/t988d9moAC/7Xm9FrlxM1A3/NH+19R5ywamBHFiMxEVQjCaD3HS87dkV4jNXMsYsnIlrMZvv+kfLLaQ==", "signatures": [{"sig": "MEYCIQDk7WkGsHDyhBuvXzJQ3UP8FWSkehXLNY3meK4Jcz4YVwIhAPR7zFy8ugNtbuNsa4yiz2FfVkYEVMj7nETiiYfGJ6bd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "4.2.0": {"name": "express", "version": "4.2.0", "dependencies": {"qs": "0.6.6", "send": "0.3.0", "debug": "0.8.1", "fresh": "0.2.2", "cookie": "0.1.2", "accepts": "1.0.1", "methods": "1.0.0", "type-is": "1.1.0", "parseurl": "1.0.1", "escape-html": "1.0.1", "utils-merge": "1.0.0", "buffer-crc32": "0.2.1", "range-parser": "1.0.0", "serve-static": "1.1.0", "path-to-regexp": "0.1.2", "cookie-signature": "1.0.3", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~0.35.0", "mocha": "~1.18.2", "vhost": "1.0.0", "marked": "0.3.2", "morgan": "1.0.1", "should": "~3.3.1", "supertest": "~0.12.0", "multiparty": "~3.2.4", "body-parser": "~1.1.2", "connect-redis": "~2.0.0", "cookie-parser": "1.0.1", "express-session": "1.0.4", "method-override": "1.0.0"}, "dist": {"shasum": "3121993a45126693e8bf897aefb4dd783762dc60", "tarball": "https://registry.npmjs.org/express/-/express-4.2.0.tgz", "integrity": "sha512-RdrDA/TM+yy+z3qMcFPeKQw0grhBcSmZ1hz2FFN59VPPu2e8G0NB+tu1xsg6uhuuoWvawfvILBO4YA14UKE85Q==", "signatures": [{"sig": "MEUCIQCv03zlbxTykRHh7TLN1NJwLxwYcQ7CPrynI1HPpzMZ4wIgaPyn8MSjQC+vZ0BTStM3s2vqDoe1EL98EdUkFydQUqU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "3.7.0": {"name": "express", "version": "3.7.0", "dependencies": {"send": "0.3.0", "debug": ">= 0.8.0 < 1", "fresh": "0.2.2", "cookie": "0.1.2", "mkdirp": "0.5.0", "connect": "2.16.2", "methods": "1.0.0", "parseurl": "1.0.1", "commander": "1.3.2", "proxy-addr": "1.0.0", "buffer-crc32": "0.2.1", "range-parser": "1.0.0", "cookie-signature": "1.0.3", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~0.8.4", "hjs": "~0.0.6", "jade": "~0.30.0", "mocha": "~1.18.2", "marked": "0.2.10", "should": "~3.3.1", "stylus": "~0.40.0", "istanbul": "0.2.10", "coveralls": "2.10.0", "supertest": "~0.12.1", "connect-redis": "~1.4.5"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "74f62f00ab2d7d49f19a9b6c81fb80b00e495868", "tarball": "https://registry.npmjs.org/express/-/express-3.7.0.tgz", "integrity": "sha512-7rgImcZYp0mZHdM4F2XGWNMP1141xoNvtVicD1KUb6Z9357h0omP54/bWkL7AX/RC566y2pjV5MFcpvmI5EvOw==", "signatures": [{"sig": "MEQCIH1c2KWS+auW3j7KbDXDmQRsff+rbHix2WE/MJtQylFAAiAgxUk5/GZoBzHqbSkY34u/vv6w75Ft6+d7aosOh4PhbQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "3.8.0": {"name": "express", "version": "3.8.0", "dependencies": {"send": "0.3.0", "debug": ">= 0.8.0 < 1", "fresh": "0.2.2", "cookie": "0.1.2", "mkdirp": "0.5.0", "connect": "2.17.1", "methods": "1.0.0", "parseurl": "1.0.1", "commander": "1.3.2", "proxy-addr": "1.0.0", "buffer-crc32": "0.2.1", "range-parser": "1.0.0", "cookie-signature": "1.0.3", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~0.8.4", "hjs": "~0.0.6", "jade": "~0.30.0", "mocha": "~1.19.0", "marked": "0.2.10", "should": "~3.3.1", "stylus": "~0.40.0", "istanbul": "0.2.10", "supertest": "~0.12.1", "connect-redis": "~1.4.5"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "f243c1752630b21b5e898cc586d1d39690422876", "tarball": "https://registry.npmjs.org/express/-/express-3.8.0.tgz", "integrity": "sha512-KWp/ABf7ZtP32z5RE2aIisC/XgFG/uP5B6BPr7Jga7/l2MHhMjXLNM/1sKZk1P8eCuxqt9No9QJ3J8zh+Crt8A==", "signatures": [{"sig": "MEQCIGbVD8dFawDawFYBszCpRf5PXqJor4Svwvwep62ONydCAiBl9FOwBue4VUzq4IP12FdP9WSSdmpJvQ2PMgOturSH4Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "4.3.0": {"name": "express", "version": "4.3.0", "dependencies": {"qs": "0.6.6", "send": "0.3.0", "debug": "0.8.1", "fresh": "0.2.2", "cookie": "0.1.2", "accepts": "1.0.1", "methods": "1.0.0", "type-is": "1.2.0", "parseurl": "1.0.1", "proxy-addr": "1.0.0", "escape-html": "1.0.1", "utils-merge": "1.0.0", "buffer-crc32": "0.2.1", "range-parser": "1.0.0", "serve-static": "1.1.0", "path-to-regexp": "0.1.2", "cookie-signature": "1.0.3", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~0.35.0", "after": "0.8.1", "mocha": "~1.19.0", "vhost": "1.0.0", "marked": "0.3.2", "morgan": "1.1.1", "should": "~3.3.1", "istanbul": "0.2.10", "supertest": "~0.12.0", "multiparty": "~3.2.4", "body-parser": "1.2.0", "connect-redis": "~2.0.0", "cookie-parser": "1.1.0", "express-session": "1.2.0", "method-override": "1.0.1"}, "dist": {"shasum": "3a65f18e40be9ea124f11c435b88b07430ef6fea", "tarball": "https://registry.npmjs.org/express/-/express-4.3.0.tgz", "integrity": "sha512-tJTn7tHTaFXLeeatQJhGpmK4MZYhxj9dbT9WOxSHRBp0o7bdBRBZfaJZ40av0kV8uKcQxo+e1S9+vkJyCbBCNw==", "signatures": [{"sig": "MEQCIBhzigTnR3ymkThxTxIRUDPzmf2dWjmBLqpMhaPji1VsAiBy1Zu681FlMvxD8i4xPVjKS2MRlLyfiPZsDrMG+JcOvQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "4.3.1": {"name": "express", "version": "4.3.1", "dependencies": {"qs": "0.6.6", "send": "0.3.0", "debug": "0.8.1", "fresh": "0.2.2", "cookie": "0.1.2", "accepts": "1.0.1", "methods": "1.0.0", "type-is": "1.2.0", "parseurl": "1.0.1", "proxy-addr": "1.0.0", "escape-html": "1.0.1", "utils-merge": "1.0.0", "buffer-crc32": "0.2.1", "range-parser": "1.0.0", "serve-static": "1.1.0", "path-to-regexp": "0.1.2", "cookie-signature": "1.0.3", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~0.35.0", "after": "0.8.1", "mocha": "~1.19.0", "vhost": "1.0.0", "marked": "0.3.2", "morgan": "1.1.1", "should": "~3.3.1", "istanbul": "0.2.10", "supertest": "~0.12.0", "multiparty": "~3.2.4", "body-parser": "1.2.0", "connect-redis": "~2.0.0", "cookie-parser": "1.1.0", "express-session": "1.2.0", "method-override": "1.0.1"}, "dist": {"shasum": "656b2c148d1db3e2ac53727b799f0e34ecc7d713", "tarball": "https://registry.npmjs.org/express/-/express-4.3.1.tgz", "integrity": "sha512-bVVAZmEHt9+Vd4OzE8CA7vUeiLzFaMPXSs0/R6bAtV1cgi7+7vFFYGfXOA8cwfN6LWk3bU68g/ca1wJCx/nIQg==", "signatures": [{"sig": "MEUCIQCvDMUd9B37Uw2zGGm3KBWHLR3EWSYGwZGQcBaUWwmuhQIgWB5ooVLn4CJCy4eXYg4t0m2lLzCUSCLPmSyMWZS2Hd0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "3.8.1": {"name": "express", "version": "3.8.1", "dependencies": {"send": "0.3.0", "debug": ">= 0.8.0 < 1", "fresh": "0.2.2", "cookie": "0.1.2", "mkdirp": "0.5.0", "connect": "2.17.3", "methods": "1.0.0", "parseurl": "1.0.1", "commander": "1.3.2", "proxy-addr": "1.0.0", "buffer-crc32": "0.2.1", "range-parser": "1.0.0", "cookie-signature": "1.0.3", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~0.8.4", "hjs": "~0.0.6", "jade": "~0.30.0", "mocha": "~1.19.0", "marked": "0.2.10", "should": "~3.3.1", "stylus": "~0.40.0", "istanbul": "0.2.10", "supertest": "~0.12.1", "connect-redis": "~1.4.5"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "884148c879c5ae88243c635dee4d91956b750143", "tarball": "https://registry.npmjs.org/express/-/express-3.8.1.tgz", "integrity": "sha512-Mcl3hc9KgMpm97n0SRKr+/m4VQtqEy4nrKkc93scr/F6m0rdynh7UGRdaL6ggHPiVprhSUrqCkwaZAOAwott6g==", "signatures": [{"sig": "MEUCIQCtidDnSwFFwxgHgLAhFmxqibdVmvj3DJybmEi91eSVWwIgbQArO29GSvyH0b/E7g4VzQXL4+V3wJmvwYF3IMxmr24=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "4.3.2": {"name": "express", "version": "4.3.2", "dependencies": {"qs": "0.6.6", "send": "0.3.0", "debug": "0.8.1", "fresh": "0.2.2", "cookie": "0.1.2", "accepts": "1.0.1", "methods": "1.0.0", "type-is": "1.2.0", "parseurl": "1.0.1", "proxy-addr": "1.0.0", "escape-html": "1.0.1", "utils-merge": "1.0.0", "buffer-crc32": "0.2.1", "range-parser": "1.0.0", "serve-static": "1.1.0", "path-to-regexp": "0.1.2", "cookie-signature": "1.0.3", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~0.35.0", "after": "0.8.1", "mocha": "~1.19.0", "vhost": "1.0.0", "marked": "0.3.2", "morgan": "1.1.1", "should": "~3.3.1", "istanbul": "0.2.10", "supertest": "~0.12.0", "multiparty": "~3.2.4", "body-parser": "1.2.2", "connect-redis": "~2.0.0", "cookie-parser": "1.1.0", "express-session": "1.2.1", "method-override": "1.0.2"}, "dist": {"shasum": "b8332c55d7b2f69f2d90e14c0958431e3a1a25dc", "tarball": "https://registry.npmjs.org/express/-/express-4.3.2.tgz", "integrity": "sha512-xTEyCCjKrZe4UJvUKVk0+61NsS+AAlIsRhSpnm4ExsONN1qmO3wnTOnCk/iAPxUCyZczoacJ2Ign3Vc+CqXnkg==", "signatures": [{"sig": "MEUCIDHnt+LIOeE4g75uC6PU59BxQQnsdtZLIxCkwfnl/qJYAiEAvRg2TBo6ag1CmKTSdzfSekgtXsFs1gH+QRJIwh3YBXE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "3.9.0": {"name": "express", "version": "3.9.0", "dependencies": {"send": "0.4.0", "debug": ">= 0.8.0 < 1", "fresh": "0.2.2", "cookie": "0.1.2", "mkdirp": "0.5.0", "connect": "2.18.0", "methods": "1.0.0", "parseurl": "1.0.1", "commander": "1.3.2", "proxy-addr": "1.0.0", "buffer-crc32": "0.2.1", "range-parser": "1.0.0", "cookie-signature": "1.0.3", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.3.1", "mocha": "~1.20.0", "marked": "0.3.2", "should": "~4.0.0", "istanbul": "0.2.10", "supertest": "~0.13.0", "connect-redis": "~1.4.5"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "da991c3ff90bb5b9f26842e3e3f70c8caa4797c8", "tarball": "https://registry.npmjs.org/express/-/express-3.9.0.tgz", "integrity": "sha512-MxUmHZ6AKpue475Ku8Zw1Yx72/tcyAMblIE3qrEZzWV3J8F0aaVNlqL8UpKzHywBRlp8SowMQGHxqfqjcLUTtQ==", "signatures": [{"sig": "MEUCIB79pYyDlqMDRpNXvO0jbFwdV4+DzyDCp8ZhsP2IcmyIAiEApSMQhJ9FwrklG03Ur1O/CUEryS6bcVnO64KSOww0cYA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "4.4.0": {"name": "express", "version": "4.4.0", "dependencies": {"qs": "0.6.6", "send": "0.4.0", "debug": "0.8.1", "fresh": "0.2.2", "cookie": "0.1.2", "accepts": "1.0.2", "methods": "1.0.0", "type-is": "1.2.0", "parseurl": "1.0.1", "proxy-addr": "1.0.0", "escape-html": "1.0.1", "utils-merge": "1.0.0", "buffer-crc32": "0.2.1", "range-parser": "1.0.0", "serve-static": "1.2.0", "path-to-regexp": "0.1.2", "cookie-signature": "1.0.3", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.3.1", "after": "0.8.1", "mocha": "~1.20.0", "vhost": "1.0.0", "marked": "0.3.2", "morgan": "1.1.1", "should": "~4.0.0", "istanbul": "0.2.10", "supertest": "~0.13.0", "multiparty": "~3.2.4", "body-parser": "1.2.2", "connect-redis": "~2.0.0", "cookie-parser": "1.1.0", "express-session": "1.2.1", "method-override": "1.0.2"}, "dist": {"shasum": "1ffd7dbe7a24fb2940ad0570611a3312b76d8f37", "tarball": "https://registry.npmjs.org/express/-/express-4.4.0.tgz", "integrity": "sha512-A<PERSON><PERSON>ugCEffboOgG2PgEea906dK6J82fG+m1HjmMwUd0158KIIehImDhGSM3uBlGh6FgAV04Hu32LlVfeGCjSsug==", "signatures": [{"sig": "MEQCIDF+JU0IOEMbvxC3ufSh4lWEYS/IMV0rTaFDTJ2njy6UAiArRh+YiYiv+UFW4ytMD7zIVHXoM1IwbHlWodAx7MweaQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "4.4.1": {"name": "express", "version": "4.4.1", "dependencies": {"qs": "0.6.6", "send": "0.4.1", "debug": "0.8.1", "fresh": "0.2.2", "cookie": "0.1.2", "accepts": "1.0.2", "methods": "1.0.1", "type-is": "1.2.0", "parseurl": "1.0.1", "proxy-addr": "1.0.0", "escape-html": "1.0.1", "utils-merge": "1.0.0", "buffer-crc32": "0.2.1", "range-parser": "1.0.0", "serve-static": "1.2.1", "path-to-regexp": "0.1.2", "cookie-signature": "1.0.3", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.3.1", "after": "0.8.1", "mocha": "~1.20.0", "vhost": "1.0.0", "marked": "0.3.2", "morgan": "1.1.1", "should": "~4.0.0", "istanbul": "0.2.10", "supertest": "~0.13.0", "multiparty": "~3.2.4", "body-parser": "1.3.0", "connect-redis": "~2.0.0", "cookie-parser": "1.1.0", "express-session": "1.2.1", "method-override": "2.0.1"}, "dist": {"shasum": "9e0364d1c74e076d7409d302429a384b10dfbd42", "tarball": "https://registry.npmjs.org/express/-/express-4.4.1.tgz", "integrity": "sha512-qIrOJ2/9+0i50PwvWZvrCaram8HPxLsUuc+k/SsWnEV6B3ZbjPdPQ+KNpifLRPD1Kym0z8X4GPPGfCGcyC0O0w==", "signatures": [{"sig": "MEYCIQDSLSlprZmz+ohIbUtuoCZPuJ3UsfcV7vO1V46ypPZkTQIhALIjRW1XR5XD1F7gGr9CLS3v3Ff1MxYwCJnGB7I0dqpc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "3.10.0": {"name": "express", "version": "3.10.0", "dependencies": {"send": "0.4.1", "debug": ">= 0.8.0 < 1", "fresh": "0.2.2", "cookie": "0.1.2", "mkdirp": "0.5.0", "connect": "2.19.1", "methods": "1.0.1", "parseurl": "1.0.1", "commander": "1.3.2", "proxy-addr": "1.0.0", "escape-html": "1.0.1", "buffer-crc32": "0.2.1", "range-parser": "1.0.0", "cookie-signature": "1.0.3", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.3.1", "mocha": "~1.20.0", "marked": "0.3.2", "should": "~4.0.0", "istanbul": "0.2.10", "supertest": "~0.13.0", "connect-redis": "~1.4.5"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "508aebb75685a84fe5873b080a2f759c5e0f4a97", "tarball": "https://registry.npmjs.org/express/-/express-3.10.0.tgz", "integrity": "sha512-FU8u1bDO1BExFJMVukA6j8UH1UsHAcmwzSratCensbRLzC1xUvMfDHrKYPXIb94FIrDcj7mc82WgBbfKBhgTzQ==", "signatures": [{"sig": "MEUCIQCawL4QyDAw9s79MKG/3qP05nO0ibwTizx1EIQLG4x34QIgX+8VzUtBvXiGy6dAvt8mVLjD3TqNBmYg+XoTRA2HyCQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "3.10.1": {"name": "express", "version": "3.10.1", "dependencies": {"send": "0.4.1", "debug": ">= 0.8.0 < 1", "fresh": "0.2.2", "cookie": "0.1.2", "mkdirp": "0.5.0", "connect": "2.19.2", "methods": "1.0.1", "parseurl": "1.0.1", "commander": "1.3.2", "proxy-addr": "1.0.1", "escape-html": "1.0.1", "buffer-crc32": "0.2.1", "range-parser": "1.0.0", "cookie-signature": "1.0.3", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.3.1", "mocha": "~1.20.0", "marked": "0.3.2", "should": "~4.0.0", "istanbul": "0.2.10", "supertest": "~0.13.0", "connect-redis": "~1.4.5"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "259578cd1238731560460e833bc8b2a10b031b4d", "tarball": "https://registry.npmjs.org/express/-/express-3.10.1.tgz", "integrity": "sha512-7Wwl1cnQ+t6uU+iqvugHJI91XB6S3B7pop6cbNc1Iddk1fYfFGpfuYGyJr7JFxvjzLWQc5VZL/UBQImn7WkGtQ==", "signatures": [{"sig": "MEUCIGApMGDRf8N3+XM429VgnSBv7dtKSMjK4oDUWj6eTUOmAiEAkTycT0jUdiK5/XEYyj11nu26GCTADO6/BDX4VKQv0v8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "3.10.2": {"name": "express", "version": "3.10.2", "dependencies": {"send": "0.4.1", "debug": ">= 0.8.0 < 1", "fresh": "0.2.2", "cookie": "0.1.2", "mkdirp": "0.5.0", "connect": "2.19.3", "methods": "1.0.1", "parseurl": "1.0.1", "commander": "1.3.2", "proxy-addr": "1.0.1", "escape-html": "1.0.1", "buffer-crc32": "0.2.1", "range-parser": "1.0.0", "cookie-signature": "1.0.3", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.3.1", "mocha": "~1.20.0", "marked": "0.3.2", "should": "~4.0.0", "istanbul": "0.2.10", "supertest": "~0.13.0", "connect-redis": "~1.4.5"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "4fa0df0a6dd3956255cc23ade6c6576911d8e467", "tarball": "https://registry.npmjs.org/express/-/express-3.10.2.tgz", "integrity": "sha512-fBVMz0OyYImDk/wjlonVWCikl8/MG5na1zG07FyzTDM5OH+q5WDHjMt2yupZp8p8IaKEQH1Od86XhH10yhKZ2w==", "signatures": [{"sig": "MEQCIFDu4FWcHqo/hT9DjOFqDt146txLMAghfkxVkNS/mFgeAiBY49q2FbBPKxGW9uE5JrWARNkTQ5aEVgaBR7WJR3jSFw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "3.10.3": {"name": "express", "version": "3.10.3", "dependencies": {"send": "0.4.1", "vary": "0.1.0", "debug": "1.0.0", "fresh": "0.2.2", "cookie": "0.1.2", "mkdirp": "0.5.0", "connect": "2.19.4", "methods": "1.0.1", "parseurl": "1.0.1", "commander": "1.3.2", "proxy-addr": "1.0.1", "escape-html": "1.0.1", "buffer-crc32": "0.2.1", "range-parser": "1.0.0", "cookie-signature": "1.0.3", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.3.1", "mocha": "~1.20.0", "marked": "0.3.2", "should": "~4.0.0", "istanbul": "0.2.10", "supertest": "~0.13.0", "connect-redis": "~1.4.5"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "d669d5fa2d79fa6349af5fa6338d646bc346ada5", "tarball": "https://registry.npmjs.org/express/-/express-3.10.3.tgz", "integrity": "sha512-esbPqqoUItd0mQNWZtqmefZmMjcOMjV6CuUwmATqSeEigaNilcr3ll8v8Raz9ZDYDcMMjP+m+UC52afxRg79IA==", "signatures": [{"sig": "MEQCIBxNDqWf2TRjwylEs2+4aDakHzPWDz6SEzzOMaRCBNkdAiAOjgjaBKrIHbdTad5nN3S1yzLvykF9gUWgergZyPjBYg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "3.10.4": {"name": "express", "version": "3.10.4", "dependencies": {"send": "0.4.2", "vary": "0.1.0", "debug": "1.0.1", "fresh": "0.2.2", "cookie": "0.1.2", "mkdirp": "0.5.0", "connect": "2.19.5", "methods": "1.0.1", "parseurl": "1.0.1", "commander": "1.3.2", "proxy-addr": "1.0.1", "escape-html": "1.0.1", "buffer-crc32": "0.2.1", "range-parser": "1.0.0", "cookie-signature": "1.0.3", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.3.1", "mocha": "~1.20.0", "marked": "0.3.2", "should": "~4.0.0", "istanbul": "0.2.10", "supertest": "~0.13.0", "connect-redis": "~1.4.5"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "527bd28b0e17cd41722617ab88cb4a41b15f497d", "tarball": "https://registry.npmjs.org/express/-/express-3.10.4.tgz", "integrity": "sha512-ChaHf836jCytfaeUEx7unmb8jbQah+V6/Opv6G1QOKVOoPTbuGeZ07kOwaVhvy/m+AjhzPsasWvEF9bEUCQGbA==", "signatures": [{"sig": "MEMCHyhon6reeoVSaPRS6oPrx1INXuC8DIVeG1Bd25ScNawCIFhRZHOXr8cktSVilcS82kKILUco25sOsdoSujZwemPd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "4.4.2": {"name": "express", "version": "4.4.2", "dependencies": {"qs": "0.6.6", "send": "0.4.2", "vary": "0.1.0", "debug": "1.0.1", "fresh": "0.2.2", "cookie": "0.1.2", "accepts": "1.0.2", "methods": "1.0.1", "type-is": "1.2.1", "parseurl": "1.0.1", "proxy-addr": "1.0.1", "escape-html": "1.0.1", "utils-merge": "1.0.0", "buffer-crc32": "0.2.1", "range-parser": "1.0.0", "serve-static": "1.2.2", "path-to-regexp": "0.1.2", "cookie-signature": "1.0.3", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.3.1", "after": "0.8.1", "mocha": "~1.20.1", "vhost": "2.0.0", "marked": "0.3.2", "morgan": "1.1.1", "should": "~4.0.4", "istanbul": "0.2.10", "supertest": "~0.13.0", "multiparty": "~3.2.4", "body-parser": "1.3.0", "connect-redis": "~2.0.0", "cookie-parser": "1.1.0", "express-session": "1.2.1", "method-override": "2.0.2"}, "dist": {"shasum": "ff6c8a513d31cc60cabe0f71848dea3cb4f56df6", "tarball": "https://registry.npmjs.org/express/-/express-4.4.2.tgz", "integrity": "sha512-ibouH4JfxvhzMogUMNHmTr2dSmUBDjS0H6Tf+A7c1CEiE4tMEQ37YX2paWtkojqMDqyBMEiuoXlBrV829V6aUw==", "signatures": [{"sig": "MEYCIQDQUSjHcfsZw0aRalwzvVvfC2RsXUfBIt6FK874lfXXNQIhANAf2CNUludNSTwladulPsFCaLgDVdIve3QTHoT44B1/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "3.10.5": {"name": "express", "version": "3.10.5", "dependencies": {"send": "0.4.3", "vary": "0.1.0", "debug": "1.0.2", "fresh": "0.2.2", "cookie": "0.1.2", "mkdirp": "0.5.0", "connect": "2.19.6", "methods": "1.0.1", "parseurl": "1.0.1", "commander": "1.3.2", "proxy-addr": "1.0.1", "escape-html": "1.0.1", "buffer-crc32": "0.2.1", "range-parser": "1.0.0", "cookie-signature": "1.0.3", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.3.1", "mocha": "~1.20.0", "marked": "0.3.2", "should": "~4.0.0", "istanbul": "0.2.10", "supertest": "~0.13.0", "connect-redis": "~1.4.5"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "842c0bcb4f6b7fc6323fa3030f24d0e9f82c5501", "tarball": "https://registry.npmjs.org/express/-/express-3.10.5.tgz", "integrity": "sha512-3NDN8IvxeqxWxeLJP9DxsFcn+jp302uF9XuV6LSwqIbiCOwKpgxUrHYTZbM6GsoijUHBx3hx6SEj3QwHZp1FdA==", "signatures": [{"sig": "MEQCIFDzeHGNdyXnqAL/8g5r8yoiG0ypWVZ8F0O+qoywbw+8AiAYUrnX/GcY77AZfBjWEz/yLa/A7j+YKW4p0/pLr6EcRA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "4.4.3": {"name": "express", "version": "4.4.3", "dependencies": {"qs": "0.6.6", "send": "0.4.3", "vary": "0.1.0", "debug": "1.0.2", "fresh": "0.2.2", "cookie": "0.1.2", "accepts": "1.0.3", "methods": "1.0.1", "type-is": "1.2.1", "parseurl": "1.0.1", "proxy-addr": "1.0.1", "escape-html": "1.0.1", "utils-merge": "1.0.0", "buffer-crc32": "0.2.1", "range-parser": "1.0.0", "serve-static": "1.2.3", "path-to-regexp": "0.1.2", "cookie-signature": "1.0.3", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.3.1", "after": "0.8.1", "mocha": "~1.20.1", "vhost": "2.0.0", "marked": "0.3.2", "morgan": "1.1.1", "should": "~4.0.4", "istanbul": "0.2.10", "supertest": "~0.13.0", "multiparty": "~3.2.4", "body-parser": "1.3.0", "connect-redis": "~2.0.0", "cookie-parser": "1.1.0", "express-session": "1.2.1", "method-override": "2.0.2"}, "dist": {"shasum": "c52525743153f00452fe8b13fee1e94330a208a0", "tarball": "https://registry.npmjs.org/express/-/express-4.4.3.tgz", "integrity": "sha512-2mxxFd86wp9iKf6gjRD/eWqI2j0nGsHAnWJTjI6tu1WfpGnwOQG58h9KTyh+zH9FYEMULdy+xUY4apaZkTnGVg==", "signatures": [{"sig": "MEUCIHkwQQjZI7m04pajL7i9x7i1bPyhDIadZzU4m0suC/DvAiEAjH4Q3dIbV9elxSViJc3u8p6AzDRB/SKWRNPb84qgCm8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "3.11.0": {"name": "express", "version": "3.11.0", "dependencies": {"depd": "0.3.0", "send": "0.4.3", "vary": "0.1.0", "debug": "1.0.2", "fresh": "0.2.2", "cookie": "0.1.2", "mkdirp": "0.5.0", "connect": "2.20.2", "methods": "1.0.1", "parseurl": "1.0.1", "commander": "1.3.2", "proxy-addr": "1.0.1", "escape-html": "1.0.1", "buffer-crc32": "0.2.3", "range-parser": "1.0.0", "cookie-signature": "1.0.3", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.3.1", "mocha": "~1.20.0", "marked": "0.3.2", "should": "~4.0.0", "istanbul": "0.2.10", "supertest": "~0.13.0", "connect-redis": "~1.4.5"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "f1c8e1c991a444dd7ae331bfb7f1a4557fcfd2ee", "tarball": "https://registry.npmjs.org/express/-/express-3.11.0.tgz", "integrity": "sha512-/647bsD/48HoC+myehc3S93C6KUBpncWSjxEImmRajSlnI7McA9F9QFb6gc6Vxp9KfO/S7OiETvoT2xU0nDfVw==", "signatures": [{"sig": "MEYCIQDG0VVBRilk36OzcV0tBAovXk3+CVpgQOviZabPrnzl7AIhAPc8c3hRLWKs0pM043f5R/mauwvhnwEmcoQ8yhIHvGvo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "4.4.4": {"name": "express", "version": "4.4.4", "dependencies": {"qs": "0.6.6", "send": "0.4.3", "vary": "0.1.0", "debug": "1.0.2", "fresh": "0.2.2", "cookie": "0.1.2", "accepts": "~1.0.5", "methods": "1.0.1", "type-is": "1.2.1", "parseurl": "1.0.1", "proxy-addr": "1.0.1", "escape-html": "1.0.1", "utils-merge": "1.0.0", "buffer-crc32": "0.2.3", "range-parser": "1.0.0", "serve-static": "1.2.3", "path-to-regexp": "0.1.2", "cookie-signature": "1.0.3", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.3.1", "after": "0.8.1", "mocha": "~1.20.1", "vhost": "2.0.0", "marked": "0.3.2", "morgan": "1.1.1", "should": "~4.0.4", "istanbul": "0.2.10", "supertest": "~0.13.0", "multiparty": "~3.2.4", "body-parser": "~1.4.3", "connect-redis": "~2.0.0", "cookie-parser": "~1.3.1", "express-session": "~1.5.0", "method-override": "2.0.2"}, "dist": {"shasum": "198bfd931c16ce869e54af5fb0515064fb8ea431", "tarball": "https://registry.npmjs.org/express/-/express-4.4.4.tgz", "integrity": "sha512-TlldMjbP2nT8ZnK0Zq1Vz8bsiVRD1K9bV0qUb5AfE+PsipeDFMteRCVBfEYsr5Dxxlmgh80mtcoS9r7I/9V5mw==", "signatures": [{"sig": "MEUCIQCHwbZqk0b1EiZvVassAmhPDJkG6TpauKanVp+LPQWl5gIgPq/Fu3KAaDIGAGU73+4fjKkhtLtoBpiTjzp2y/+IFn0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "3.12.0": {"name": "express", "version": "3.12.0", "dependencies": {"depd": "0.3.0", "send": "0.4.3", "vary": "0.1.0", "debug": "1.0.2", "fresh": "0.2.2", "cookie": "0.1.2", "mkdirp": "0.5.0", "connect": "2.21.0", "methods": "1.0.1", "parseurl": "1.0.1", "commander": "1.3.2", "proxy-addr": "1.0.1", "escape-html": "1.0.1", "media-typer": "0.2.0", "buffer-crc32": "0.2.3", "range-parser": "1.0.0", "cookie-signature": "1.0.3", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.3.1", "mocha": "~1.20.0", "marked": "0.3.2", "should": "~4.0.0", "istanbul": "0.2.10", "supertest": "~0.13.0", "connect-redis": "~1.4.5"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "8f00c9bef6f4d186f4a481ad831844dd7d73336e", "tarball": "https://registry.npmjs.org/express/-/express-3.12.0.tgz", "integrity": "sha512-EsuN2D68jFZn8V9kwmmtMvwA88OOwY4q/OybUA7PK5RtYSZ4eNHN2NwWGSRkI0nE+K9ItX6oupbMikCeNtKLQA==", "signatures": [{"sig": "MEUCIENEfdL+n1ranQcKeOngQl0mPjstfBRc9ZDBmmLdB6FCAiEA0xjnsqHD98Z8OstDXllo6IuO1Cb3I4GUX8Yf3e+ZIPU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "3.12.1": {"name": "express", "version": "3.12.1", "dependencies": {"depd": "0.3.0", "send": "0.4.3", "vary": "0.1.0", "debug": "1.0.2", "fresh": "0.2.2", "cookie": "0.1.2", "mkdirp": "0.5.0", "connect": "2.21.1", "methods": "1.0.1", "parseurl": "1.0.1", "commander": "1.3.2", "proxy-addr": "1.0.1", "escape-html": "1.0.1", "media-typer": "0.2.0", "buffer-crc32": "0.2.3", "range-parser": "1.0.0", "cookie-signature": "1.0.4", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.3.1", "mocha": "~1.20.0", "marked": "0.3.2", "should": "~4.0.0", "istanbul": "0.2.12", "supertest": "~0.13.0", "connect-redis": "~1.4.5"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "f13d260d1ac6ebc4913a42dfee913cdc65dd96d4", "tarball": "https://registry.npmjs.org/express/-/express-3.12.1.tgz", "integrity": "sha512-Zov4bkDbh22oxHBHygXUcUW4IuPmvmUd5JlRDrqO4qDSSVIrzHr3xosGNchQ/FpELKO6b46YvAQ3s0v0RuFKiw==", "signatures": [{"sig": "MEUCIQDFjkcaKaAJyfHOKrMIYXmuw2QMg9D14bd2X/yp7N7bHAIgayDRxN9doIajw2KYdC3oljCx+nRQ5THsitQiDO++Z5g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "4.4.5": {"name": "express", "version": "4.4.5", "dependencies": {"qs": "0.6.6", "send": "0.4.3", "vary": "0.1.0", "debug": "1.0.2", "fresh": "0.2.2", "cookie": "0.1.2", "accepts": "~1.0.5", "methods": "1.0.1", "type-is": "1.2.1", "parseurl": "1.0.1", "proxy-addr": "1.0.1", "escape-html": "1.0.1", "utils-merge": "1.0.0", "buffer-crc32": "0.2.3", "range-parser": "1.0.0", "serve-static": "1.2.3", "path-to-regexp": "0.1.2", "cookie-signature": "1.0.4", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.3.1", "after": "0.8.1", "mocha": "~1.20.1", "vhost": "2.0.0", "marked": "0.3.2", "morgan": "1.1.1", "should": "~4.0.4", "istanbul": "0.2.10", "supertest": "~0.13.0", "multiparty": "~3.2.4", "body-parser": "~1.4.3", "connect-redis": "~2.0.0", "cookie-parser": "~1.3.1", "express-session": "~1.5.0", "method-override": "2.0.2"}, "dist": {"shasum": "5f2f302f277187abd721c3a36e44d86c5e3f03eb", "tarball": "https://registry.npmjs.org/express/-/express-4.4.5.tgz", "integrity": "sha512-oUXJpgoP1Exol+xM8OznQKUMx09HXr0QL5IhGvAO9+7/Bv7SZdT2Wdb9zQnOpLF1Op9dEMHmG3MntakgSF4XFA==", "signatures": [{"sig": "MEUCICYgmWN9ZEOhEzRSKW3SQDWGdiOxwYLXzB4t8Y4mKbLfAiEA4OhhTl1GIgTBNQJLM6hPTKcOZRV0HmAR8p1hjLtzSe0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "3.13.0": {"name": "express", "version": "3.13.0", "dependencies": {"depd": "0.3.0", "send": "0.5.0", "vary": "0.1.0", "debug": "1.0.2", "fresh": "0.2.2", "cookie": "0.1.2", "mkdirp": "0.5.0", "connect": "2.22.0", "methods": "1.0.1", "parseurl": "1.0.1", "commander": "1.3.2", "basic-auth": "0.0.1", "proxy-addr": "1.0.1", "escape-html": "1.0.1", "media-typer": "0.2.0", "buffer-crc32": "0.2.3", "range-parser": "1.0.0", "cookie-signature": "1.0.4", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.3.1", "mocha": "~1.20.0", "marked": "0.3.2", "should": "~4.0.0", "istanbul": "0.2.12", "supertest": "~0.13.0", "connect-redis": "~1.4.5"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "69ac1d62732992e9529dc3b21eb40f23cc64438b", "tarball": "https://registry.npmjs.org/express/-/express-3.13.0.tgz", "integrity": "sha512-tD97188RX3NjMTunKagu+Dy37XMg08zzQSutMn5v2/yhyAGwIio+jYzzDKklexYyQHdNVJrzrby96dGNu5rT5A==", "signatures": [{"sig": "MEYCIQC+OR7E0GeqZukxBR4EaCt8P5VgMPWlRS/pkUNhQR8ahwIhAPTJrZHcdQ8BVKxEJk4aSijfq27RDZJ9a8h1Cg/Wf9qd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "4.5.0": {"name": "express", "version": "4.5.0", "dependencies": {"qs": "0.6.6", "depd": "0.3.0", "send": "0.5.0", "vary": "0.1.0", "debug": "1.0.2", "fresh": "0.2.2", "cookie": "0.1.2", "accepts": "~1.0.7", "methods": "1.0.1", "type-is": "~1.3.2", "parseurl": "1.0.1", "proxy-addr": "1.0.1", "escape-html": "1.0.1", "media-typer": "0.2.0", "utils-merge": "1.0.0", "buffer-crc32": "0.2.3", "finalhandler": "0.0.2", "range-parser": "1.0.0", "serve-static": "~1.3.0", "path-to-regexp": "0.1.2", "cookie-signature": "1.0.4", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.3.1", "after": "0.8.1", "mocha": "~1.20.1", "vhost": "2.0.0", "marked": "0.3.2", "morgan": "1.1.1", "should": "~4.0.4", "istanbul": "0.2.14", "supertest": "~0.13.0", "multiparty": "~3.3.0", "body-parser": "~1.4.3", "connect-redis": "~2.0.0", "cookie-parser": "~1.3.1", "express-session": "~1.6.1", "method-override": "2.0.2"}, "dist": {"shasum": "64c68b9e41f66339c95a462f37f94ff436724bd7", "tarball": "https://registry.npmjs.org/express/-/express-4.5.0.tgz", "integrity": "sha512-d2pbVE334EpnjehYH8MZxcrvX7q7KLyl1EdDIOp0TjuLm8P68QXFP3KI90jvQqSFC4rBHy/QWPYONwp5K8JHkQ==", "signatures": [{"sig": "MEUCIQCcsN7RKyY8ztg9H0ApQ7KbGroVWylGFrRX5MWFCuNF4AIgcYfLAy5DLnFNrOofbt1d61bxBaxM2l4zAjSL+QDyo10=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "4.5.1": {"name": "express", "version": "4.5.1", "dependencies": {"qs": "0.6.6", "depd": "0.3.0", "send": "0.5.0", "vary": "0.1.0", "debug": "1.0.2", "fresh": "0.2.2", "cookie": "0.1.2", "accepts": "~1.0.7", "methods": "1.0.1", "type-is": "~1.3.2", "parseurl": "1.0.1", "proxy-addr": "1.0.1", "escape-html": "1.0.1", "media-typer": "0.2.0", "utils-merge": "1.0.0", "buffer-crc32": "0.2.3", "finalhandler": "0.0.2", "range-parser": "1.0.0", "serve-static": "~1.3.0", "path-to-regexp": "0.1.2", "cookie-signature": "1.0.4", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.3.1", "after": "0.8.1", "mocha": "~1.20.1", "vhost": "2.0.0", "marked": "0.3.2", "morgan": "1.1.1", "should": "~4.0.4", "istanbul": "0.2.14", "supertest": "~0.13.0", "multiparty": "~3.3.0", "body-parser": "~1.4.3", "connect-redis": "~2.0.0", "cookie-parser": "~1.3.1", "express-session": "~1.6.1", "method-override": "2.0.2"}, "dist": {"shasum": "4bc3e6ec9db28e575fe591c36fbb781ffef6fe7c", "tarball": "https://registry.npmjs.org/express/-/express-4.5.1.tgz", "integrity": "sha512-36quUqw4ZqgDnQzQpYLnNphPx612kfmQjbZlFt9Mz6Up/27IhkACtYBG9L/dAZkrXD0il3cq58DU+lpSL/Bg1g==", "signatures": [{"sig": "MEQCICDbGfqoXbWbEqJcKJe6PhZlJOy+enQWpvAq+795nQflAiAP1gNGvv4Yg5xTkHPUmf5bK6bkwjykOaTH1Ds+mWh6Xw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "3.14.0": {"name": "express", "version": "3.14.0", "dependencies": {"depd": "0.3.0", "send": "0.5.0", "vary": "0.1.0", "debug": "1.0.3", "fresh": "0.2.2", "cookie": "0.1.2", "mkdirp": "0.5.0", "connect": "2.23.0", "methods": "1.1.0", "parseurl": "~1.1.3", "commander": "1.3.2", "basic-auth": "1.0.0", "proxy-addr": "1.0.1", "escape-html": "1.0.1", "media-typer": "0.2.0", "buffer-crc32": "0.2.3", "range-parser": "1.0.0", "cookie-signature": "1.0.4", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.3.1", "mocha": "~1.20.0", "marked": "0.3.2", "should": "~4.0.0", "istanbul": "0.3.0", "supertest": "~0.13.0", "connect-redis": "~1.4.5"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "91f28701eedbce71ddca15b0fb92cfeff1401afb", "tarball": "https://registry.npmjs.org/express/-/express-3.14.0.tgz", "integrity": "sha512-Cwcpg5enjXXhKrO0/b694hYGA4zTGM6413ODQ/UTYt5NTQf7zg4AMfbK7Va2u7+RIMNO0kZHymXyK+AMxkKkDg==", "signatures": [{"sig": "MEUCIQCp/Dd8vuZrFupZLb92yuCzBFg/Pck2A1uaFvNgfAN7sAIgX9s4O+Eh4TOX449wf7k6ONPzPRJKAj++xl95nYwiM/M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "4.6.0": {"name": "express", "version": "4.6.0", "dependencies": {"qs": "0.6.6", "depd": "0.3.0", "send": "0.6.0", "vary": "0.1.0", "debug": "1.0.3", "fresh": "0.2.2", "cookie": "0.1.2", "accepts": "~1.0.7", "methods": "1.1.0", "type-is": "~1.3.2", "parseurl": "~1.1.3", "proxy-addr": "1.0.1", "escape-html": "1.0.1", "media-typer": "0.2.0", "utils-merge": "1.0.0", "buffer-crc32": "0.2.3", "finalhandler": "0.0.3", "range-parser": "1.0.0", "serve-static": "~1.3.2", "path-to-regexp": "0.1.3", "cookie-signature": "1.0.4", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.3.1", "after": "0.8.1", "mocha": "~1.20.1", "vhost": "2.0.0", "marked": "0.3.2", "morgan": "1.1.1", "should": "~4.0.4", "istanbul": "0.3.0", "supertest": "~0.13.0", "multiparty": "~3.3.0", "body-parser": "~1.4.3", "connect-redis": "~2.0.0", "cookie-parser": "~1.3.1", "express-session": "~1.6.5", "method-override": "~2.1.1"}, "dist": {"shasum": "abaf229003006ada5a4dc5d99abbc7095570af7d", "tarball": "https://registry.npmjs.org/express/-/express-4.6.0.tgz", "integrity": "sha512-fsjcOFHuFyTy0ke7lLKotrqYaNp/YmKbewhSs4o8vEco0gge5MdrrekPmKMJ8K9bjVV3PyslNxcM0VjBrsIJqQ==", "signatures": [{"sig": "MEQCIERz61qdA+PGWD8d7qOjWTOD8H5sCh42WCwiuFff2I2tAiB+LAJiisdl4nXRU/ItqRnj/ZvWd875d/K04xX4RFJcfQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "4.6.1": {"name": "express", "version": "4.6.1", "dependencies": {"qs": "0.6.6", "depd": "0.3.0", "send": "0.6.0", "vary": "0.1.0", "debug": "1.0.3", "fresh": "0.2.2", "cookie": "0.1.2", "accepts": "~1.0.7", "methods": "1.1.0", "type-is": "~1.3.2", "parseurl": "~1.1.3", "proxy-addr": "1.0.1", "escape-html": "1.0.1", "media-typer": "0.2.0", "utils-merge": "1.0.0", "buffer-crc32": "0.2.3", "finalhandler": "0.0.3", "range-parser": "1.0.0", "serve-static": "~1.3.2", "path-to-regexp": "0.1.3", "cookie-signature": "1.0.4", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.3.1", "after": "0.8.1", "mocha": "~1.20.1", "vhost": "2.0.0", "marked": "0.3.2", "morgan": "1.1.1", "should": "~4.0.4", "istanbul": "0.3.0", "supertest": "~0.13.0", "multiparty": "~3.3.0", "body-parser": "~1.4.3", "connect-redis": "~2.0.0", "cookie-parser": "~1.3.1", "express-session": "~1.6.5", "method-override": "~2.1.1"}, "dist": {"shasum": "c806e51755cb453ba17fac2f343caff6af885df4", "tarball": "https://registry.npmjs.org/express/-/express-4.6.1.tgz", "integrity": "sha512-nG9Y8xfzgrW/9XCr5sv+KDbtY8mZPN9HO3GziltaubpvleI+1RyHxAKvYjmFih3HkQIaPXW9ozxMHBDNf3UXng==", "signatures": [{"sig": "MEUCIEy11wAlf8eXFF6sygnRi0bvJVKtksrik8BlDGJwL/uIAiEA+fxt15IGuPgHyENn0L/FeAfCzjXUNeX01qxxRadgiDE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "3.15.0": {"name": "express", "version": "3.15.0", "dependencies": {"depd": "0.4.2", "send": "0.7.0", "vary": "0.1.0", "debug": "1.0.4", "fresh": "0.2.2", "cookie": "0.1.2", "mkdirp": "0.5.0", "connect": "2.24.0", "methods": "1.1.0", "parseurl": "~1.2.0", "commander": "1.3.2", "basic-auth": "1.0.0", "proxy-addr": "1.0.1", "escape-html": "1.0.1", "media-typer": "0.2.0", "buffer-crc32": "0.2.3", "range-parser": "1.0.0", "cookie-signature": "1.0.4", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.3.1", "mocha": "~1.20.0", "marked": "0.3.2", "should": "~4.0.0", "istanbul": "0.3.0", "supertest": "~0.13.0", "connect-redis": "~1.4.5"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "c9ac9eb2c38c34a650597300a06848d2e7001aa4", "tarball": "https://registry.npmjs.org/express/-/express-3.15.0.tgz", "integrity": "sha512-ZstNjJx4x7viFQ5OHLRXy0P5qKolrVPldBd7O9bV0xDYehzrHSa7j9KGvzAV27AV3ZrmEwm3SrIpYmfsfhcQaw==", "signatures": [{"sig": "MEUCIH1dzsnNH9U5LnYKzFTGc/5kMphiL9x/r5rNQNl/VagTAiEAtoGjFrhAwer02f4Tg8lqUmW2mG9Qm3nmHSVqu0dtU+s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "4.7.0": {"name": "express", "version": "4.7.0", "dependencies": {"qs": "0.6.6", "depd": "0.4.2", "send": "0.7.0", "vary": "0.1.0", "debug": "1.0.4", "fresh": "0.2.2", "cookie": "0.1.2", "accepts": "~1.0.7", "methods": "1.1.0", "type-is": "~1.3.2", "parseurl": "~1.2.0", "proxy-addr": "1.0.1", "escape-html": "1.0.1", "media-typer": "0.2.0", "utils-merge": "1.0.0", "buffer-crc32": "0.2.3", "finalhandler": "0.1.0", "range-parser": "1.0.0", "serve-static": "~1.4.0", "path-to-regexp": "0.1.3", "cookie-signature": "1.0.4", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.5.0", "after": "0.8.1", "mocha": "~1.20.1", "vhost": "2.0.0", "marked": "0.3.2", "morgan": "~1.2.0", "should": "~4.0.4", "istanbul": "0.3.0", "supertest": "~0.13.0", "multiparty": "~3.3.1", "body-parser": "~1.5.0", "connect-redis": "~2.0.0", "cookie-parser": "~1.3.1", "express-session": "~1.7.0", "method-override": "~2.1.1"}, "dist": {"shasum": "9b38ca8eb3bf75fdcd9fad39ad85d02f5ef80b4b", "tarball": "https://registry.npmjs.org/express/-/express-4.7.0.tgz", "integrity": "sha512-9Aq4Kzm8jsFBRL4YscuVLr62xruEi2dZWfjlwGTJsN5i/IQDGXKnhN+Exx7/GGEfsVNgujyEsWL+zCUzoj6cYw==", "signatures": [{"sig": "MEUCIQDZZFPq/BwoaGNA2helKDSuMoNiPcVIfxdp7OsB6qo/gAIgaj5OX93RGZoi6XWROQ3HnzscbC6uO+id4cGG5uxYuO0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "3.15.1": {"name": "express", "version": "3.15.1", "dependencies": {"depd": "0.4.3", "send": "0.7.1", "vary": "0.1.0", "debug": "1.0.4", "fresh": "0.2.2", "cookie": "0.1.2", "mkdirp": "0.5.0", "connect": "2.24.1", "methods": "1.1.0", "parseurl": "~1.2.0", "commander": "1.3.2", "basic-auth": "1.0.0", "proxy-addr": "1.0.1", "escape-html": "1.0.1", "media-typer": "0.2.0", "buffer-crc32": "0.2.3", "range-parser": "1.0.0", "cookie-signature": "1.0.4", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.3.1", "mocha": "~1.21.0", "marked": "0.3.2", "should": "~4.0.0", "istanbul": "0.3.0", "supertest": "~0.13.0", "connect-redis": "~1.4.5"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "ce6800e0fa51c1c9700f246fc90eb8bcde8172e1", "tarball": "https://registry.npmjs.org/express/-/express-3.15.1.tgz", "integrity": "sha512-tSYb+JG8fjWzBepGdR9TJWK4hjsGcJHJhu30OIFWOKDvsP3Lbjm47Wo6u1iHlb/JzwzvyUOJ/D//YomBilrqlg==", "signatures": [{"sig": "MEUCIGTcHv8zJKRB/+Inq8YJXEK4De8WGHuYofYAiqZcTJiuAiEA/SruxeQBFdjhKH5dCYdp5afSEIVe8RtkVAhfO9Eauao=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "4.7.1": {"name": "express", "version": "4.7.1", "dependencies": {"qs": "0.6.6", "depd": "0.4.3", "send": "0.7.1", "vary": "0.1.0", "debug": "1.0.4", "fresh": "0.2.2", "cookie": "0.1.2", "accepts": "~1.0.7", "methods": "1.1.0", "type-is": "~1.3.2", "parseurl": "~1.2.0", "proxy-addr": "1.0.1", "escape-html": "1.0.1", "media-typer": "0.2.0", "utils-merge": "1.0.0", "buffer-crc32": "0.2.3", "finalhandler": "0.1.0", "range-parser": "1.0.0", "serve-static": "~1.4.1", "path-to-regexp": "0.1.3", "cookie-signature": "1.0.4", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.5.0", "after": "0.8.1", "mocha": "~1.21.0", "vhost": "2.0.0", "marked": "0.3.2", "morgan": "~1.2.0", "should": "~4.0.4", "istanbul": "0.3.0", "supertest": "~0.13.0", "multiparty": "~3.3.1", "body-parser": "~1.5.0", "connect-redis": "~2.0.0", "cookie-parser": "~1.3.1", "express-session": "~1.7.0", "method-override": "~2.1.1"}, "dist": {"shasum": "06c0aa7d03d5ea5565bb0249b2da3671a24062d3", "tarball": "https://registry.npmjs.org/express/-/express-4.7.1.tgz", "integrity": "sha512-0+K2NuJuSDgN2/o0H3mJII60igGtLQRloOZ5HiElJoefHRFu1IzwnCL+ANozgUY9ar/Kyzf78Rtl1nzIT9/ylA==", "signatures": [{"sig": "MEQCIA/RuUkIqT4ETXbHwxte+eLvw5ZqovFvNQreSlbgWx+rAiAtCbQKCVgFTgwzsI5mlV9Zuj6wRSzEswm2ivPCPj3nLA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "3.15.2": {"name": "express", "version": "3.15.2", "dependencies": {"depd": "0.4.4", "send": "0.7.2", "vary": "0.1.0", "debug": "1.0.4", "fresh": "0.2.2", "cookie": "0.1.2", "mkdirp": "0.5.0", "connect": "2.24.2", "methods": "1.1.0", "parseurl": "~1.2.0", "commander": "1.3.2", "basic-auth": "1.0.0", "proxy-addr": "1.0.1", "escape-html": "1.0.1", "media-typer": "0.2.0", "buffer-crc32": "0.2.3", "range-parser": "1.0.0", "cookie-signature": "1.0.4", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.3.1", "mocha": "~1.21.0", "marked": "0.3.2", "should": "~4.0.0", "istanbul": "0.3.0", "supertest": "~0.13.0", "connect-redis": "~1.4.5"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "a45f213bcfc5022914223d5d67747661cc7515a1", "tarball": "https://registry.npmjs.org/express/-/express-3.15.2.tgz", "integrity": "sha512-ofqNWte0jj9UnFPUmUjexViDfev2ZpRA3hHb0zoULEb003dcfVxhA2syfyXYwWN5lrMYpIEIHuWU7t7m85GYsw==", "signatures": [{"sig": "MEUCIB+X6dOysCdziMV/tB+tQTTBoZkg+0Fds1OBUv10HiaFAiEA8v07jFtRUzMZ/GUo8C9s0tNBEhnUT9anMVWea7n70AU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "4.7.2": {"name": "express", "version": "4.7.2", "dependencies": {"qs": "0.6.6", "depd": "0.4.4", "send": "0.7.2", "vary": "0.1.0", "debug": "1.0.4", "fresh": "0.2.2", "cookie": "0.1.2", "accepts": "~1.0.7", "methods": "1.1.0", "type-is": "~1.3.2", "parseurl": "~1.2.0", "proxy-addr": "1.0.1", "escape-html": "1.0.1", "media-typer": "0.2.0", "utils-merge": "1.0.0", "buffer-crc32": "0.2.3", "finalhandler": "0.1.0", "range-parser": "1.0.0", "serve-static": "~1.4.2", "path-to-regexp": "0.1.3", "cookie-signature": "1.0.4", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.5.0", "after": "0.8.1", "mocha": "~1.21.0", "vhost": "2.0.0", "marked": "0.3.2", "morgan": "~1.2.2", "should": "~4.0.4", "istanbul": "0.3.0", "supertest": "~0.13.0", "multiparty": "~3.3.1", "body-parser": "~1.5.2", "connect-redis": "~2.0.0", "cookie-parser": "~1.3.1", "express-session": "~1.7.2", "method-override": "~2.1.1"}, "dist": {"shasum": "2cbae61efab6c2db72a547ff3bf380e637c08590", "tarball": "https://registry.npmjs.org/express/-/express-4.7.2.tgz", "integrity": "sha512-nbKaLU7QicBt5ys/+S7+V3j53QBsudrOGsXk00dp3yhpWB8VmzvttVyaTLzJ2w+Se3n187utaqccQR9Y1CbZZQ==", "signatures": [{"sig": "MEUCIC6gYMZy/KNNFzcJrd0t9ccYknbAuBAP3+5Wwai8LcS3AiEA136NGNE9GXEhzAsQEHtCbb4Kfu/5VGNXtJrYc9TH9hs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "4.7.3": {"name": "express", "version": "4.7.3", "dependencies": {"qs": "0.6.6", "depd": "0.4.4", "send": "0.7.3", "vary": "0.1.0", "debug": "1.0.4", "fresh": "0.2.2", "cookie": "0.1.2", "accepts": "~1.0.7", "methods": "1.1.0", "type-is": "~1.3.2", "parseurl": "~1.2.0", "proxy-addr": "1.0.1", "escape-html": "1.0.1", "media-typer": "0.2.0", "utils-merge": "1.0.0", "buffer-crc32": "0.2.3", "finalhandler": "0.1.0", "range-parser": "1.0.0", "serve-static": "~1.4.3", "path-to-regexp": "0.1.3", "cookie-signature": "1.0.4", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.5.0", "after": "0.8.1", "mocha": "~1.21.0", "vhost": "2.0.0", "marked": "0.3.2", "morgan": "~1.2.2", "should": "~4.0.4", "istanbul": "0.3.0", "supertest": "~0.13.0", "multiparty": "~3.3.1", "body-parser": "~1.5.2", "connect-redis": "~2.0.0", "cookie-parser": "~1.3.1", "express-session": "~1.7.2", "method-override": "~2.1.1"}, "dist": {"shasum": "9fde138763113224c8204a48209511d0c2d27284", "tarball": "https://registry.npmjs.org/express/-/express-4.7.3.tgz", "integrity": "sha512-ZVXW1bH8DxEedqLOtQU8v0biTzXaJGKHE/XcfGP5acjQmzEsTKmRiNS/jD3zkISxnuBnoi7tt3tiu0Zg+FCqzg==", "signatures": [{"sig": "MEUCIELk/hQj9kePLPrynLQCWrL2hNJ9olUMnvlWAp0dorUTAiEAkmpg3uEPN0jS/G4IvehkBoz0vWMD74Bvp8x+NUy+jyA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "3.15.3": {"name": "express", "version": "3.15.3", "dependencies": {"depd": "0.4.4", "send": "0.7.4", "vary": "0.1.0", "debug": "1.0.4", "fresh": "0.2.2", "cookie": "0.1.2", "mkdirp": "0.5.0", "connect": "2.24.3", "methods": "1.1.0", "parseurl": "~1.2.0", "commander": "1.3.2", "basic-auth": "1.0.0", "proxy-addr": "1.0.1", "escape-html": "1.0.1", "media-typer": "0.2.0", "buffer-crc32": "0.2.3", "range-parser": "1.0.0", "cookie-signature": "1.0.4", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.3.1", "mocha": "~1.21.0", "marked": "0.3.2", "should": "~4.0.0", "istanbul": "0.3.0", "supertest": "~0.13.0", "connect-redis": "~1.4.5"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "993a9ef1c2d67f2525d086a67dc187edeab6f025", "tarball": "https://registry.npmjs.org/express/-/express-3.15.3.tgz", "integrity": "sha512-Vefe8HuWIjQqqyn/dyeJnMt2SKmoXWpnGpyrTg/quI/c0z2yhF0IQuhCK+jv/JwP2F81m+5JWHH/47oppYp4Ug==", "signatures": [{"sig": "MEQCIE9m6zsa97drZi5y/B5llSLKYQXcd4ghwCJ2Qqhtd55XAiA7o2+R88+pEqwZa98SktuGwpqe0QcFyf+wAnLzE0OIKw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "4.7.4": {"name": "express", "version": "4.7.4", "dependencies": {"qs": "0.6.6", "depd": "0.4.4", "send": "0.7.4", "vary": "0.1.0", "debug": "1.0.4", "fresh": "0.2.2", "cookie": "0.1.2", "accepts": "~1.0.7", "methods": "1.1.0", "type-is": "~1.3.2", "parseurl": "~1.2.0", "proxy-addr": "1.0.1", "escape-html": "1.0.1", "media-typer": "0.2.0", "utils-merge": "1.0.0", "buffer-crc32": "0.2.3", "finalhandler": "0.1.0", "range-parser": "1.0.0", "serve-static": "~1.4.4", "path-to-regexp": "0.1.3", "cookie-signature": "1.0.4", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.5.0", "after": "0.8.1", "mocha": "~1.21.0", "vhost": "2.0.0", "marked": "0.3.2", "morgan": "~1.2.2", "should": "~4.0.4", "istanbul": "0.3.0", "supertest": "~0.13.0", "multiparty": "~3.3.1", "body-parser": "~1.5.2", "connect-redis": "~2.0.0", "cookie-parser": "~1.3.1", "express-session": "~1.7.2", "method-override": "~2.1.1"}, "dist": {"shasum": "caf59389cf0b31b1314bf44d3355c2a80cfa217c", "tarball": "https://registry.npmjs.org/express/-/express-4.7.4.tgz", "integrity": "sha512-**************************/j9sGyXAk6RWHvZzB1bXwlmoPZ9QHCZmvOKeY6TImh0f05rXoK6NTOTw+OCA==", "signatures": [{"sig": "MEUCIH84LxG74LDXAVL5Z88ZgySFziA1rwRcklxbgx2P+sWzAiEA91mZi3T5Vs2AGoZDw3YTieMTPwUtz1rrGmjbJ0x+K88=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "3.16.0": {"name": "express", "version": "3.16.0", "dependencies": {"depd": "0.4.4", "send": "0.8.1", "vary": "0.1.0", "debug": "1.0.4", "fresh": "0.2.2", "cookie": "0.1.2", "mkdirp": "0.5.0", "connect": "2.25.0", "methods": "1.1.0", "parseurl": "~1.2.0", "commander": "1.3.2", "basic-auth": "1.0.0", "proxy-addr": "1.0.1", "escape-html": "1.0.1", "media-typer": "0.2.0", "buffer-crc32": "0.2.3", "range-parser": "1.0.0", "cookie-signature": "1.0.4", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.5.0", "mocha": "~1.21.0", "marked": "0.3.2", "should": "~4.0.0", "istanbul": "0.3.0", "supertest": "~0.13.0", "connect-redis": "~1.5.0"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "289dc292da617d06ac21bc1f4b2ee0e9a09a9c38", "tarball": "https://registry.npmjs.org/express/-/express-3.16.0.tgz", "integrity": "sha512-++W7IKbfi2JlsKgT/8feHWkfPLKbIEB6Xb2VDpoRxvc0Efzp1r+1lE/6drTs/Pbf+rdgRkhXqXBv2qRJRlfFVw==", "signatures": [{"sig": "MEUCIQDK2BP4bphquEws666D5XthZgkZkigPBa6TDFdzwfWm1AIgf4sfC5VSjAlbkZE2HitZvohcpoerzEGvlqj4j+Lduqg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "4.8.0": {"name": "express", "version": "4.8.0", "dependencies": {"qs": "1.0.2", "depd": "0.4.4", "send": "0.8.1", "vary": "0.1.0", "debug": "1.0.4", "fresh": "0.2.2", "cookie": "0.1.2", "accepts": "~1.0.7", "methods": "1.1.0", "type-is": "~1.3.2", "parseurl": "~1.2.0", "proxy-addr": "1.0.1", "escape-html": "1.0.1", "media-typer": "0.2.0", "utils-merge": "1.0.0", "buffer-crc32": "0.2.3", "finalhandler": "0.1.0", "range-parser": "1.0.0", "serve-static": "~1.5.0", "path-to-regexp": "0.1.3", "cookie-signature": "1.0.4", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.5.0", "after": "0.8.1", "mocha": "~1.21.0", "vhost": "2.0.0", "marked": "0.3.2", "morgan": "~1.2.2", "should": "~4.0.4", "istanbul": "0.3.0", "supertest": "~0.13.0", "multiparty": "~3.3.1", "body-parser": "~1.6.0", "connect-redis": "~2.0.0", "cookie-parser": "~1.3.1", "express-session": "~1.7.2", "method-override": "~2.1.1"}, "dist": {"shasum": "a6079da464ec502ecaef4e11faa7e127f5593d85", "tarball": "https://registry.npmjs.org/express/-/express-4.8.0.tgz", "integrity": "sha512-kzygZF1NEj4ZGeKhtC/NpxYN+xfmfVjJwBEdQmVEldBB7RHrLWxPFgIg3AVUmT50Uu+yPihZhMbO6OVGqB54VA==", "signatures": [{"sig": "MEYCIQCPakgVE3txSQgI9VUFNN3l1LR69QrF8OI5tdDoKKAsYwIhAJbw1xiC5ULDoAh4hAj5Dxq3UDKSH/SU7NxUE94O4v0X", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "3.16.1": {"name": "express", "version": "3.16.1", "dependencies": {"depd": "0.4.4", "send": "0.8.1", "vary": "0.1.0", "debug": "1.0.4", "fresh": "0.2.2", "cookie": "0.1.2", "mkdirp": "0.5.0", "connect": "2.25.1", "methods": "1.1.0", "parseurl": "~1.2.0", "commander": "1.3.2", "basic-auth": "1.0.0", "proxy-addr": "1.0.1", "escape-html": "1.0.1", "media-typer": "0.2.0", "buffer-crc32": "0.2.3", "range-parser": "1.0.0", "cookie-signature": "1.0.4", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.5.0", "mocha": "~1.21.4", "marked": "0.3.2", "should": "~4.0.0", "istanbul": "0.3.0", "supertest": "~0.13.0", "connect-redis": "~1.5.0"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "fc5cc9627c8c2837da21119b8d909247b0b40ba0", "tarball": "https://registry.npmjs.org/express/-/express-3.16.1.tgz", "integrity": "sha512-U0p31IJPS72HpbODLFAhQjSdyJz4CfWn3sBtERI2Q21ox8zd2gakHAQFOTcgfjDwwMBoram5nrwO40LDIvzc7A==", "signatures": [{"sig": "MEYCIQCKhcA74w2AuWmJXu6liuO/uHq/d86u5pY50FrwNQinFAIhAIRhR3sLGZhqHhYJkgZVq8M56wD2/i34G0m1eh9FOXsU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "4.8.1": {"name": "express", "version": "4.8.1", "dependencies": {"qs": "1.1.0", "depd": "0.4.4", "send": "0.8.1", "vary": "0.1.0", "debug": "1.0.4", "fresh": "0.2.2", "cookie": "0.1.2", "accepts": "~1.0.7", "methods": "1.1.0", "type-is": "~1.3.2", "parseurl": "~1.2.0", "proxy-addr": "1.0.1", "escape-html": "1.0.1", "media-typer": "0.2.0", "utils-merge": "1.0.0", "buffer-crc32": "0.2.3", "finalhandler": "0.1.0", "range-parser": "1.0.0", "serve-static": "~1.5.0", "path-to-regexp": "0.1.3", "cookie-signature": "1.0.4", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.5.0", "after": "0.8.1", "mocha": "~1.21.4", "vhost": "2.0.0", "marked": "0.3.2", "morgan": "~1.2.2", "should": "~4.0.4", "istanbul": "0.3.0", "supertest": "~0.13.0", "multiparty": "~3.3.1", "body-parser": "~1.6.1", "connect-redis": "~2.0.0", "cookie-parser": "~1.3.1", "express-session": "~1.7.2", "method-override": "~2.1.1"}, "dist": {"shasum": "24cf5a613156d5d95bc8c2fa843cf12e2a1be6c9", "tarball": "https://registry.npmjs.org/express/-/express-4.8.1.tgz", "integrity": "sha512-FwAziNljiYox1pf+8sAVX67v981nQhzPn90yL5sI5eI0rQ3v5/j9CyXh/pjCJ/JT181352sbqkDVdmIGinDqQg==", "signatures": [{"sig": "MEUCIQCAncSwBkzYelpEolRIo1kLwME9HiAUsZsQSIFvrulRkAIgeji04gYdohPq8aVz82nsOJJbJOVZOT1Xi2YSPsKiZ7Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "3.16.2": {"name": "express", "version": "3.16.2", "dependencies": {"depd": "0.4.4", "send": "0.8.1", "vary": "0.1.0", "debug": "1.0.4", "fresh": "0.2.2", "cookie": "0.1.2", "mkdirp": "0.5.0", "connect": "2.25.2", "methods": "1.1.0", "parseurl": "~1.2.0", "commander": "1.3.2", "basic-auth": "1.0.0", "proxy-addr": "1.0.1", "escape-html": "1.0.1", "media-typer": "0.2.0", "buffer-crc32": "0.2.3", "range-parser": "1.0.0", "cookie-signature": "1.0.4", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.5.0", "mocha": "~1.21.4", "marked": "0.3.2", "should": "~4.0.0", "istanbul": "0.3.0", "supertest": "~0.13.0", "connect-redis": "~1.5.0"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "5ed1411187b64e05fef8b70671d3bf9fdf9bc7eb", "tarball": "https://registry.npmjs.org/express/-/express-3.16.2.tgz", "integrity": "sha512-OYwpQM8g6wZd9jI1OXZ0oZCJQ4wVjSCx9FLydwhTTiD2zBQTZ1CxZKEn0Myq++ifobOmrC4nq4/2yNEKcDCXIA==", "signatures": [{"sig": "MEUCIQCLXWkXXTFc5SVnQY5s8x4+rfsq16JblzUEPUEUhasuuwIgMa2GuhrK8bOj3Dc1TLnMlsOVbmq9zHUh/dcT6m9noUM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "4.8.2": {"name": "express", "version": "4.8.2", "dependencies": {"qs": "1.2.0", "depd": "0.4.4", "send": "0.8.1", "vary": "0.1.0", "debug": "1.0.4", "fresh": "0.2.2", "cookie": "0.1.2", "accepts": "~1.0.7", "methods": "1.1.0", "type-is": "~1.3.2", "parseurl": "~1.2.0", "proxy-addr": "1.0.1", "escape-html": "1.0.1", "media-typer": "0.2.0", "utils-merge": "1.0.0", "buffer-crc32": "0.2.3", "finalhandler": "0.1.0", "range-parser": "1.0.0", "serve-static": "~1.5.0", "path-to-regexp": "0.1.3", "cookie-signature": "1.0.4", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.5.0", "after": "0.8.1", "mocha": "~1.21.4", "vhost": "2.0.0", "marked": "0.3.2", "morgan": "~1.2.2", "should": "~4.0.4", "istanbul": "0.3.0", "supertest": "~0.13.0", "multiparty": "~3.3.1", "body-parser": "~1.6.1", "connect-redis": "~2.0.0", "cookie-parser": "~1.3.1", "express-session": "~1.7.2", "method-override": "~2.1.1"}, "dist": {"shasum": "99fd5c03a8d885ba83981599619d71d088e46d3c", "tarball": "https://registry.npmjs.org/express/-/express-4.8.2.tgz", "integrity": "sha512-jUWgvhgBsmoYAdyjNUjZAOgxTt7CpKL338rRqXzQJ/MsiPqqkvvWXr7PWsgAD5qXTO21pMsgtQW4yff6v4s+YA==", "signatures": [{"sig": "MEUCIQDDd8g9XCgLq0DbkaCvnwquI2ZyWvW8oQGdk8fcaBd//AIgE80ILbGjNLc1liG9hS33Mtk3C5VDC42E261IdGJyzZ4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "3.16.3": {"name": "express", "version": "3.16.3", "dependencies": {"depd": "0.4.4", "send": "0.8.1", "vary": "0.1.0", "debug": "1.0.4", "fresh": "0.2.2", "cookie": "0.1.2", "mkdirp": "0.5.0", "connect": "2.25.3", "methods": "1.1.0", "parseurl": "~1.2.0", "commander": "1.3.2", "basic-auth": "1.0.0", "proxy-addr": "1.0.1", "escape-html": "1.0.1", "media-typer": "0.2.0", "buffer-crc32": "0.2.3", "range-parser": "1.0.0", "cookie-signature": "1.0.4", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.5.0", "mocha": "~1.21.4", "marked": "0.3.2", "should": "~4.0.0", "istanbul": "0.3.0", "supertest": "~0.13.0", "connect-redis": "~1.5.0"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "89157f5e6a84365036ed93ae1e413ab1bd6ce1a5", "tarball": "https://registry.npmjs.org/express/-/express-3.16.3.tgz", "integrity": "sha512-wzGa+7IRt8N5/fIYtg+uf1kq2NJVzUd3PUbXcPyEa/xyy6/qUld2bYOOp1SG4fl40lRlC0oAM8lopwlUaTL3ow==", "signatures": [{"sig": "MEUCIQCEHTM64p4NvmHLVVUrhQfPr19J8Vv8oR/uQCbiQC2bcgIgMniJY6xnnXj7r7CFx2PekToS3DUkRZh5LSr0dk9O3o4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "3.16.4": {"name": "express", "version": "3.16.4", "dependencies": {"depd": "0.4.4", "send": "0.8.1", "vary": "0.1.0", "debug": "1.0.4", "fresh": "0.2.2", "cookie": "0.1.2", "mkdirp": "0.5.0", "connect": "2.25.4", "methods": "1.1.0", "parseurl": "~1.3.0", "commander": "1.3.2", "basic-auth": "1.0.0", "proxy-addr": "1.0.1", "escape-html": "1.0.1", "media-typer": "0.2.0", "buffer-crc32": "0.2.3", "range-parser": "1.0.0", "cookie-signature": "1.0.4", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.5.0", "mocha": "~1.21.4", "marked": "0.3.2", "should": "~4.0.0", "istanbul": "0.3.0", "supertest": "~0.13.0", "connect-redis": "~1.5.0"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "d0dae63fc0d5a24ef48901d6b31d5e5791226033", "tarball": "https://registry.npmjs.org/express/-/express-3.16.4.tgz", "integrity": "sha512-I4tEyqiC1FgmhkmkzD3+kVwhoK/pE6U9r3B1daPTdZH4i67DrK5E6z/N3Bmoyk+zYsGPj706ktl76doLvV2Ozg==", "signatures": [{"sig": "MEUCIH68UxXCS8VNsacgW4cqHZ564xWHQyY+WWsZVwAKSGWGAiEAjHnRzVG6tgomNLpXAN+gKEJCJNLMbs4CEwnESLLnmKM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "4.8.3": {"name": "express", "version": "4.8.3", "dependencies": {"qs": "1.2.1", "depd": "0.4.4", "send": "0.8.1", "vary": "0.1.0", "debug": "1.0.4", "fresh": "0.2.2", "cookie": "0.1.2", "accepts": "~1.0.7", "methods": "1.1.0", "type-is": "~1.3.2", "parseurl": "~1.3.0", "proxy-addr": "1.0.1", "escape-html": "1.0.1", "media-typer": "0.2.0", "utils-merge": "1.0.0", "buffer-crc32": "0.2.3", "finalhandler": "0.1.0", "range-parser": "1.0.0", "serve-static": "~1.5.1", "path-to-regexp": "0.1.3", "cookie-signature": "1.0.4", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.5.0", "after": "0.8.1", "mocha": "~1.21.4", "vhost": "2.0.0", "marked": "0.3.2", "morgan": "~1.2.2", "should": "~4.0.4", "istanbul": "0.3.0", "supertest": "~0.13.0", "multiparty": "~3.3.1", "body-parser": "~1.6.1", "connect-redis": "~2.0.0", "cookie-parser": "~1.3.1", "express-session": "~1.7.2", "method-override": "~2.1.1"}, "dist": {"shasum": "a2c95b9079cda0473a04448f6b6c1e7fc20bf200", "tarball": "https://registry.npmjs.org/express/-/express-4.8.3.tgz", "integrity": "sha512-S6HFc1b96OKPnD9ixLLYGerppjw029gfFq7WGaQkCEGWvnn0BAvOQQfsc3oZXgx9IbHPM1J22pobQzSc0TBg8Q==", "signatures": [{"sig": "MEUCIQC7M3iBvCzhAKun4i4kxn+7QQQmvMXfzUlzNXONI18W0AIgYQDiqkw8OsGibYTtuIA8osPdMQjuGqZsrwxyseVYijw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "3.16.5": {"name": "express", "version": "3.16.5", "dependencies": {"depd": "0.4.4", "send": "0.8.1", "vary": "0.1.0", "debug": "1.0.4", "fresh": "0.2.2", "cookie": "0.1.2", "mkdirp": "0.5.0", "connect": "2.25.5", "methods": "1.1.0", "parseurl": "~1.3.0", "commander": "1.3.2", "basic-auth": "1.0.0", "proxy-addr": "1.0.1", "escape-html": "1.0.1", "media-typer": "0.2.0", "buffer-crc32": "0.2.3", "range-parser": "1.0.0", "cookie-signature": "1.0.4", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.5.0", "mocha": "~1.21.4", "marked": "0.3.2", "should": "~4.0.0", "istanbul": "0.3.0", "supertest": "~0.13.0", "connect-redis": "~1.5.0"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "70dc7fd31be9d7bea32312ce0e461dd4ca5bb58b", "tarball": "https://registry.npmjs.org/express/-/express-3.16.5.tgz", "integrity": "sha512-l8HL/Gsq6HUKT53rYnm2bpc57LuOmnk2JP2+OuR5C83OfStPQBgT46GVHJe8lkJdU2aiy+ySCJc/k0s95Nj8eA==", "signatures": [{"sig": "MEQCIANUY2A4m2XkCOiLxDKhH2qbpao6qVqvZ5mQ5JbPZ7LUAiBclxGAw716EJXr1CXy8n0qXTGYh3t/HFvMMz+ELAGOkA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "3.16.6": {"name": "express", "version": "3.16.6", "dependencies": {"depd": "0.4.4", "send": "0.8.2", "vary": "0.1.0", "debug": "1.0.4", "fresh": "0.2.2", "cookie": "0.1.2", "mkdirp": "0.5.0", "connect": "2.25.6", "methods": "1.1.0", "parseurl": "~1.3.0", "commander": "1.3.2", "basic-auth": "1.0.0", "proxy-addr": "1.0.1", "escape-html": "1.0.1", "media-typer": "0.2.0", "buffer-crc32": "0.2.3", "range-parser": "1.0.0", "cookie-signature": "1.0.4", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.5.0", "mocha": "~1.21.4", "marked": "0.3.2", "should": "~4.0.0", "istanbul": "0.3.0", "supertest": "~0.13.0", "connect-redis": "~1.5.0"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "585104615f0b857750856424bcfaa4c16b3cce1c", "tarball": "https://registry.npmjs.org/express/-/express-3.16.6.tgz", "integrity": "sha512-4kRApWsN6s9++TUOQlSfknTcF6VVeLhzetWF7VcMH/eRNdKXuG44CZo9pliG2SDuIeEV3VIIwdpTQxT9dYbbqg==", "signatures": [{"sig": "MEUCIEx866/FSiZN75W/gffJUxTGeMfyGgtBLRP9+qpswiinAiEA+8+w0x/E9aaBtz0TEv0J2zVyojb70fkwBYhVJ1FNUO0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "4.8.4": {"name": "express", "version": "4.8.4", "dependencies": {"qs": "1.2.2", "depd": "0.4.4", "send": "0.8.2", "vary": "0.1.0", "debug": "1.0.4", "fresh": "0.2.2", "cookie": "0.1.2", "accepts": "~1.0.7", "methods": "1.1.0", "type-is": "~1.3.2", "parseurl": "~1.3.0", "proxy-addr": "1.0.1", "escape-html": "1.0.1", "media-typer": "0.2.0", "utils-merge": "1.0.0", "buffer-crc32": "0.2.3", "finalhandler": "0.1.0", "range-parser": "1.0.0", "serve-static": "~1.5.2", "path-to-regexp": "0.1.3", "cookie-signature": "1.0.4", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.5.0", "after": "0.8.1", "mocha": "~1.21.4", "vhost": "2.0.0", "marked": "0.3.2", "morgan": "~1.2.2", "should": "~4.0.4", "istanbul": "0.3.0", "supertest": "~0.13.0", "multiparty": "~3.3.2", "body-parser": "~1.6.4", "connect-redis": "~2.0.0", "cookie-parser": "~1.3.1", "express-session": "~1.7.5", "method-override": "~2.1.1"}, "dist": {"shasum": "b14d432cc1897e10b1915cf9b648f8930deadb0e", "tarball": "https://registry.npmjs.org/express/-/express-4.8.4.tgz", "integrity": "sha512-3dL5jdOJo0TtkUBAaqzf4XYrieQbKSDW8a8WIfFjxPE5mXBiMcWgnKtcQQ3ZD9AtX6hnGtiF03Jprsuj2zm/TA==", "signatures": [{"sig": "MEUCIQDG76/1FUXEhhtjpA7JWZAsd0y95nFp0CQBolMAViMttgIgEyWTBJucyYNKqr7Z8DV2OkujG8RnSr2YK0bGgPjOa4A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "3.16.7": {"name": "express", "version": "3.16.7", "dependencies": {"depd": "0.4.4", "send": "0.8.3", "vary": "0.1.0", "debug": "1.0.4", "fresh": "0.2.2", "cookie": "0.1.2", "mkdirp": "0.5.0", "connect": "2.25.7", "methods": "1.1.0", "parseurl": "~1.3.0", "commander": "1.3.2", "basic-auth": "1.0.0", "proxy-addr": "1.0.1", "escape-html": "1.0.1", "media-typer": "0.2.0", "buffer-crc32": "0.2.3", "range-parser": "1.0.0", "cookie-signature": "1.0.4", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.5.0", "mocha": "~1.21.4", "marked": "0.3.2", "should": "~4.0.0", "istanbul": "0.3.0", "supertest": "~0.13.0", "connect-redis": "~1.5.0"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "788aab5d66e85060211d6fea08eb2986f2f2631c", "tarball": "https://registry.npmjs.org/express/-/express-3.16.7.tgz", "integrity": "sha512-kWuVFr+ai0F3S/P1Idh4yyMaCbkRytlu31j6LT2yBjtcPZOtCMUdBeOflLTrj2Uqpal9ilz0ViJFhoxL6oYldg==", "signatures": [{"sig": "MEQCIB/FB1xBZtxDtc4YbA6fDFJEVi4t0wfsQYkIOG/vFlZ8AiA2CxTenHT47Pcap4tofYsE+OKxefG5VHC0ZgEd3kRatg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "4.8.5": {"name": "express", "version": "4.8.5", "dependencies": {"qs": "1.2.2", "depd": "0.4.4", "send": "0.8.3", "vary": "0.1.0", "debug": "1.0.4", "fresh": "0.2.2", "cookie": "0.1.2", "accepts": "~1.0.7", "methods": "1.1.0", "type-is": "~1.3.2", "parseurl": "~1.3.0", "proxy-addr": "1.0.1", "escape-html": "1.0.1", "media-typer": "0.2.0", "utils-merge": "1.0.0", "buffer-crc32": "0.2.3", "finalhandler": "0.1.0", "range-parser": "1.0.0", "serve-static": "~1.5.3", "path-to-regexp": "0.1.3", "cookie-signature": "1.0.4", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.5.0", "after": "0.8.1", "mocha": "~1.21.4", "vhost": "2.0.0", "marked": "0.3.2", "morgan": "~1.2.3", "should": "~4.0.4", "istanbul": "0.3.0", "supertest": "~0.13.0", "multiparty": "~3.3.2", "body-parser": "~1.6.5", "connect-redis": "~2.0.0", "cookie-parser": "~1.3.1", "express-session": "~1.7.6", "method-override": "~2.1.3"}, "dist": {"shasum": "59cf7666c29bf7cb8545a1acd43dd81a52cb26d9", "tarball": "https://registry.npmjs.org/express/-/express-4.8.5.tgz", "integrity": "sha512-6ZnKd/f2MNC1DKqkJRXg/zABRWwh1XwRfnP20zgkLYALy4ojDnEZ6KPohfbaoLLlyEZhRUfaaLKvXWGWZTYtTA==", "signatures": [{"sig": "MEYCIQDIBCLMBOq+jl3yfOJaQDloAxJkFMA+XV1fwrBXL0qXLwIhAI1tculbSO1jJ2nrMWmAfVRyILhN+BLp6Nt1X7sP09L6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "3.16.8": {"name": "express", "version": "3.16.8", "dependencies": {"depd": "0.4.4", "send": "0.8.3", "vary": "0.1.0", "debug": "1.0.4", "fresh": "0.2.2", "cookie": "0.1.2", "mkdirp": "0.5.0", "connect": "2.25.8", "methods": "1.1.0", "parseurl": "~1.3.0", "commander": "1.3.2", "basic-auth": "1.0.0", "proxy-addr": "1.0.1", "escape-html": "1.0.1", "media-typer": "0.2.0", "buffer-crc32": "0.2.3", "range-parser": "1.0.0", "cookie-signature": "1.0.4", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.5.0", "mocha": "~1.21.4", "marked": "0.3.2", "should": "~4.0.0", "istanbul": "0.3.0", "supertest": "~0.13.0", "connect-redis": "~1.5.0"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "46307b9e35a52e523b9d58a16e4c128cd21f43f4", "tarball": "https://registry.npmjs.org/express/-/express-3.16.8.tgz", "integrity": "sha512-k0bmsIYLLAnfdcmCJWd0qa1zn6FE4I3G4T0sdwiZy3DPz+mlAFObI/qaFcsqxeiSFjKVpvNnIVDW7he9RQcmpQ==", "signatures": [{"sig": "MEYCIQDaR9zIf3/ZVKaRcpoNp95yek8b5TaGPerUxPiCJJBgMQIhAKUh/widHMgncs1rtShFdT7ULPP+g/g38RMDjQNivb5Y", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "4.8.6": {"name": "express", "version": "4.8.6", "dependencies": {"qs": "2.2.0", "depd": "0.4.4", "send": "0.8.3", "vary": "0.1.0", "debug": "1.0.4", "fresh": "0.2.2", "cookie": "0.1.2", "accepts": "~1.0.7", "methods": "1.1.0", "type-is": "~1.3.2", "parseurl": "~1.3.0", "proxy-addr": "1.0.1", "escape-html": "1.0.1", "media-typer": "0.2.0", "utils-merge": "1.0.0", "buffer-crc32": "0.2.3", "finalhandler": "0.1.0", "range-parser": "1.0.0", "serve-static": "~1.5.3", "path-to-regexp": "0.1.3", "cookie-signature": "1.0.4", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.5.0", "after": "0.8.1", "mocha": "~1.21.4", "vhost": "2.0.0", "marked": "0.3.2", "morgan": "~1.2.3", "should": "~4.0.4", "istanbul": "0.3.0", "supertest": "~0.13.0", "multiparty": "~3.3.2", "body-parser": "~1.6.6", "connect-redis": "~2.0.0", "cookie-parser": "~1.3.2", "express-session": "~1.7.6", "method-override": "~2.1.3"}, "dist": {"shasum": "703b2aa835dafab9840bb890bc55557d96516acd", "tarball": "https://registry.npmjs.org/express/-/express-4.8.6.tgz", "integrity": "sha512-v5yy9ihNeVIV99l9ozuJfnOIah44uwVHfiQu6b7UP0l2Yzp5YrubxxTL0G+l+yviwAHqkZEuo7ykXGvrIap9nQ==", "signatures": [{"sig": "MEQCIAUl0KrWQ5RQwy8n12IW7S3guscdnBVpzbilm2+rRX9iAiB/Y3BAExSaOZdcKzV42VT39Qywj5xLTZFwrUuWsB09HA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "3.16.9": {"name": "express", "version": "3.16.9", "dependencies": {"depd": "0.4.4", "send": "0.8.3", "vary": "0.1.0", "debug": "1.0.4", "fresh": "0.2.2", "cookie": "0.1.2", "mkdirp": "0.5.0", "connect": "2.25.9", "methods": "1.1.0", "parseurl": "~1.3.0", "commander": "1.3.2", "basic-auth": "1.0.0", "proxy-addr": "1.0.1", "escape-html": "1.0.1", "media-typer": "0.2.0", "buffer-crc32": "0.2.3", "range-parser": "1.0.0", "cookie-signature": "1.0.4", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.5.0", "mocha": "~1.21.4", "marked": "0.3.2", "should": "~4.0.0", "istanbul": "0.3.0", "supertest": "~0.13.0", "connect-redis": "~1.5.0"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "993747be5669700280d9682cb61ad138939847fc", "tarball": "https://registry.npmjs.org/express/-/express-3.16.9.tgz", "integrity": "sha512-6Sr9DaXrfOx+z4bY5u0vVQvcv2FQOEw0eKS/3r9x/PDIfQJ8YRVUvKBjrT897NJX3g3XjsqgeZdm2pQNXJL9lw==", "signatures": [{"sig": "MEYCIQCdZG5+C7ejFD0oWOO8Qjxz8ZIu8sRz7KjJPc/8FVf4lQIhAPp/6mNei6G+3LdEwZINc4O+wFAZ3pWICZHLRShEPvYX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "4.8.7": {"name": "express", "version": "4.8.7", "dependencies": {"qs": "2.2.2", "depd": "0.4.4", "send": "0.8.3", "vary": "0.1.0", "debug": "1.0.4", "fresh": "0.2.2", "cookie": "0.1.2", "accepts": "~1.0.7", "methods": "1.1.0", "type-is": "~1.3.2", "parseurl": "~1.3.0", "proxy-addr": "1.0.1", "escape-html": "1.0.1", "media-typer": "0.2.0", "utils-merge": "1.0.0", "buffer-crc32": "0.2.3", "finalhandler": "0.1.0", "range-parser": "1.0.0", "serve-static": "~1.5.3", "path-to-regexp": "0.1.3", "cookie-signature": "1.0.4", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.5.0", "after": "0.8.1", "mocha": "~1.21.4", "vhost": "2.0.0", "marked": "0.3.2", "morgan": "~1.2.3", "should": "~4.0.4", "istanbul": "0.3.0", "supertest": "~0.13.0", "multiparty": "~3.3.2", "body-parser": "~1.6.7", "connect-redis": "~2.0.0", "cookie-parser": "~1.3.2", "express-session": "~1.7.6", "method-override": "~2.1.3"}, "dist": {"shasum": "e4290dd5ff9c5a1a1af6f7a1c0c53021adf8564d", "tarball": "https://registry.npmjs.org/express/-/express-4.8.7.tgz", "integrity": "sha512-j9tpYNhv9/1n2Tq4b4Dht73X8Hy4tPPIRzvVkAGwCHXqPLfTjxv4sTswNLC3D8HqfW6eteQ+R/CpiHy9ALmVug==", "signatures": [{"sig": "MEQCICZiojMOArsomfGM6y3y2YwjSMVmzEGBFxk3yeinqKSiAiBMk9Ai0VK3zI9XqmtszMYwitFEYeAaSHtHQC3NudbfGw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "3.16.10": {"name": "express", "version": "3.16.10", "dependencies": {"depd": "0.4.4", "send": "0.8.5", "vary": "0.1.0", "debug": "1.0.4", "fresh": "0.2.2", "cookie": "0.1.2", "mkdirp": "0.5.0", "connect": "2.25.10", "methods": "1.1.0", "parseurl": "~1.3.0", "commander": "1.3.2", "basic-auth": "1.0.0", "proxy-addr": "1.0.1", "escape-html": "1.0.1", "media-typer": "0.2.0", "buffer-crc32": "0.2.3", "range-parser": "1.0.0", "cookie-signature": "1.0.4", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.5.0", "mocha": "~1.21.4", "marked": "0.3.2", "should": "~4.0.0", "istanbul": "0.3.2", "supertest": "~0.13.0", "connect-redis": "~1.5.0"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "c68c5ac30e9e890b812c11408dcde183c411bb56", "tarball": "https://registry.npmjs.org/express/-/express-3.16.10.tgz", "integrity": "sha512-KmLs1KW1n/GLZHLaR8kSBbnK9z2S56VIMR6yDBHpL9if7Fy9ydimmr26Rfs9QsTvdPpZCJxommuveB2NdNZbbw==", "signatures": [{"sig": "MEUCIDMX74ggw34AuAlORLD+sS5Rv/53S0MSuluqW7JSdL5YAiEA2aAuWte2Kvc7JW3t7rPbNtG3kzKVR6UvIVbHg2g/STI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "4.8.8": {"name": "express", "version": "4.8.8", "dependencies": {"qs": "2.2.2", "depd": "0.4.4", "send": "0.8.5", "vary": "0.1.0", "debug": "1.0.4", "fresh": "0.2.2", "cookie": "0.1.2", "accepts": "~1.0.7", "methods": "1.1.0", "type-is": "~1.3.2", "parseurl": "~1.3.0", "proxy-addr": "1.0.1", "escape-html": "1.0.1", "media-typer": "0.2.0", "utils-merge": "1.0.0", "buffer-crc32": "0.2.3", "finalhandler": "0.1.0", "range-parser": "1.0.0", "serve-static": "~1.5.4", "path-to-regexp": "0.1.3", "cookie-signature": "1.0.4", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.5.0", "after": "0.8.1", "mocha": "~1.21.4", "vhost": "~3.0.0", "marked": "0.3.2", "morgan": "~1.2.3", "should": "~4.0.4", "istanbul": "0.3.2", "supertest": "~0.13.0", "multiparty": "~3.3.2", "body-parser": "~1.7.0", "connect-redis": "~2.0.0", "cookie-parser": "~1.3.2", "express-session": "~1.7.6", "method-override": "~2.1.3"}, "dist": {"shasum": "6aba348ccdfa87608040b12ca0010107a0aac28e", "tarball": "https://registry.npmjs.org/express/-/express-4.8.8.tgz", "integrity": "sha512-kE2rT0gfYxKC+i2SQObFO9SvN2rTptYnYYPWznCXahXBcFjriwhU0OYCJr7ZcRsJuWl8ZevkCcn8ry8TRkDOoQ==", "signatures": [{"sig": "MEYCIQChVo+DNJ4mcgUZNPDAfARWTrevGuimQsB6TbuB0m3xKQIhAKP7APe9hyIgKc99gnOutTznpQrfASSS5iAeuKE4n8iz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "3.17.0": {"name": "express", "version": "3.17.0", "dependencies": {"depd": "0.4.4", "send": "0.9.1", "vary": "~1.0.0", "debug": "~2.0.0", "fresh": "0.2.4", "cookie": "0.1.2", "mkdirp": "0.5.0", "connect": "2.26.0", "methods": "1.1.0", "parseurl": "~1.3.0", "commander": "1.3.2", "basic-auth": "1.0.0", "proxy-addr": "1.0.1", "escape-html": "1.0.1", "media-typer": "0.3.0", "buffer-crc32": "0.2.3", "range-parser": "~1.0.2", "cookie-signature": "1.0.5", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.6.0", "mocha": "~1.21.4", "marked": "0.3.2", "should": "~4.0.0", "istanbul": "0.3.2", "supertest": "~0.13.0", "connect-redis": "~1.5.0"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "e882e8921dbd193042559b52f7d0250f749ec7ac", "tarball": "https://registry.npmjs.org/express/-/express-3.17.0.tgz", "integrity": "sha512-ze2pBIxrapiIMcn0JFBnFzko+ZZ7gJYzgIBrGDbq06PZwVxbOm486yMzxRZPQcBRvkz86CtucUO65XtVhyR0iQ==", "signatures": [{"sig": "MEUCIQCD8izY7r0DdHJxVnASozvSNNZEAP+UNAERGbNcJGfVsgIgarqI1+cP/piPwGRkkSmByz06sqeuKdrje0DqCLfxPTc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "3.17.1": {"name": "express", "version": "3.17.1", "dependencies": {"depd": "0.4.4", "send": "0.9.1", "vary": "~1.0.0", "debug": "~2.0.0", "fresh": "0.2.4", "cookie": "0.1.2", "mkdirp": "0.5.0", "connect": "2.26.0", "methods": "1.1.0", "parseurl": "~1.3.0", "commander": "1.3.2", "basic-auth": "1.0.0", "proxy-addr": "1.0.1", "escape-html": "1.0.1", "media-typer": "0.3.0", "buffer-crc32": "0.2.3", "range-parser": "~1.0.2", "cookie-signature": "1.0.5", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.6.0", "mocha": "~1.21.4", "marked": "0.3.2", "should": "~4.0.0", "istanbul": "0.3.2", "supertest": "~0.13.0", "connect-redis": "~1.5.0"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "82b357f0bc78733b1ac1070224f89a37dea76a74", "tarball": "https://registry.npmjs.org/express/-/express-3.17.1.tgz", "integrity": "sha512-kuBQpRO++ViFv/zdDi8CbO+VpiPNNp9gqYeloXIUz7JhteW4sVWknmY/D7RK/L8sP6mgXYu4SEnVUrvQFbm8pA==", "signatures": [{"sig": "MEQCIHi6hgeAPrPQLYgflgCycxxMedxckjO0okbu8aAa49pUAiAY7jhGA5j9c+eM4RD+DGTHWWvXfq551nHGPxwG6YqJvg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "4.9.0": {"name": "express", "version": "4.9.0", "dependencies": {"qs": "2.2.3", "depd": "0.4.4", "etag": "~1.3.0", "send": "0.9.1", "vary": "~1.0.0", "debug": "~2.0.0", "fresh": "0.2.4", "cookie": "0.1.2", "accepts": "~1.1.0", "methods": "1.1.0", "type-is": "~1.5.1", "parseurl": "~1.3.0", "proxy-addr": "1.0.1", "escape-html": "1.0.1", "media-typer": "0.3.0", "on-finished": "~2.1.0", "utils-merge": "1.0.0", "buffer-crc32": "0.2.3", "finalhandler": "0.2.0", "range-parser": "~1.0.2", "serve-static": "~1.6.1", "path-to-regexp": "0.1.3", "cookie-signature": "1.0.5", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.6.0", "after": "0.8.1", "mocha": "~1.21.4", "vhost": "~3.0.0", "marked": "0.3.2", "morgan": "~1.3.0", "should": "~4.0.4", "istanbul": "0.3.2", "supertest": "~0.13.0", "multiparty": "~3.3.2", "body-parser": "~1.8.1", "connect-redis": "~2.1.0", "cookie-parser": "~1.3.2", "express-session": "~1.8.1", "method-override": "~2.2.0"}, "dist": {"shasum": "9b2ea4ebce57c7ac710604c74f6c303ab344a7f3", "tarball": "https://registry.npmjs.org/express/-/express-4.9.0.tgz", "integrity": "sha512-BtW8QJkgNSOzPH5bYiaqOFpGKHUkX4jVSguCD3mmuRjAbE7j7cJJX4dMFqV7GPSLDg4EIU9JfAhZuw4kIlnPww==", "signatures": [{"sig": "MEUCIChjE9fhJe8TriQI+HMYlB5awZWIO+A7PeF1LhPCazawAiEA/ztRwZ65PMMXVJMZSiGx+HS6tRdHavFkWtHhd1vmcDg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "3.17.2": {"name": "express", "version": "3.17.2", "dependencies": {"crc": "3.0.0", "depd": "0.4.5", "send": "0.9.2", "vary": "~1.0.0", "debug": "~2.0.0", "fresh": "0.2.4", "cookie": "0.1.2", "mkdirp": "0.5.0", "connect": "2.26.1", "methods": "1.1.0", "parseurl": "~1.3.0", "commander": "1.3.2", "basic-auth": "1.0.0", "proxy-addr": "1.0.1", "escape-html": "1.0.1", "media-typer": "0.3.0", "range-parser": "~1.0.2", "cookie-signature": "1.0.5", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.6.0", "mocha": "~1.21.4", "marked": "0.3.2", "should": "~4.0.0", "istanbul": "0.3.2", "supertest": "~0.13.0", "connect-redis": "~1.5.0"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "9593dd94af5d4776ea2b6dbff8c4d850a3381353", "tarball": "https://registry.npmjs.org/express/-/express-3.17.2.tgz", "integrity": "sha512-RPnb1XgnVJjV8SdlTlXrjiT0As73XBmsUzzN7XUWtF6dBPjKqxdE6DkHJHr1SgnR2tGDEzpSzd4FsD2X/7zkOg==", "signatures": [{"sig": "MEQCIDvHIOvNb4kc7FhxUm9UqKtsWIBmv5904YW2BS2UlXbeAiAWcfQMX8Z9l6dJp9XEXa2t4R+WUAwpE9o6aq1w33DDUQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "4.9.1": {"name": "express", "version": "4.9.1", "dependencies": {"qs": "2.2.3", "depd": "0.4.5", "etag": "~1.3.1", "send": "0.9.2", "vary": "~1.0.0", "debug": "~2.0.0", "fresh": "0.2.4", "cookie": "0.1.2", "accepts": "~1.1.0", "methods": "1.1.0", "type-is": "~1.5.1", "parseurl": "~1.3.0", "proxy-addr": "1.0.1", "escape-html": "1.0.1", "media-typer": "0.3.0", "on-finished": "~2.1.0", "utils-merge": "1.0.0", "finalhandler": "0.2.0", "range-parser": "~1.0.2", "serve-static": "~1.6.2", "path-to-regexp": "0.1.3", "cookie-signature": "1.0.5", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.6.0", "after": "0.8.1", "mocha": "~1.21.4", "vhost": "~3.0.0", "marked": "0.3.2", "morgan": "~1.3.1", "should": "~4.0.4", "istanbul": "0.3.2", "supertest": "~0.13.0", "multiparty": "~3.3.2", "body-parser": "~1.8.2", "connect-redis": "~2.1.0", "cookie-parser": "~1.3.3", "express-session": "~1.8.2", "method-override": "~2.2.0"}, "dist": {"shasum": "70536ee2a8f2c302c4df45e23f4fcc7e4c2c9603", "tarball": "https://registry.npmjs.org/express/-/express-4.9.1.tgz", "integrity": "sha512-kQiQpJsbszFf7Gdzt3UHYmFZS5b41022YkLO/H85jZ0pBK/LNl8dfBhxikfqj0u4WqykbaY7WhMYBJ8OVyzk7g==", "signatures": [{"sig": "MEUCIECYeBYjSFE9fKRcvlQuPH5uW2k3MqmY5K4zfS7OGLyvAiEAzrr6m+xCfo0WQwPK5OVuzVwzXXk+kuvnT5j86EGCl8U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "4.9.2": {"name": "express", "version": "4.9.2", "dependencies": {"qs": "2.2.3", "depd": "0.4.5", "etag": "~1.3.1", "send": "0.9.2", "vary": "~1.0.0", "debug": "~2.0.0", "fresh": "0.2.4", "cookie": "0.1.2", "accepts": "~1.1.0", "methods": "1.1.0", "type-is": "~1.5.1", "parseurl": "~1.3.0", "proxy-addr": "1.0.1", "escape-html": "1.0.1", "media-typer": "0.3.0", "on-finished": "~2.1.0", "utils-merge": "1.0.0", "finalhandler": "0.2.0", "range-parser": "~1.0.2", "serve-static": "~1.6.2", "path-to-regexp": "0.1.3", "cookie-signature": "1.0.5", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.6.0", "after": "0.8.1", "mocha": "~1.21.4", "vhost": "~3.0.0", "marked": "0.3.2", "morgan": "~1.3.1", "should": "~4.0.4", "istanbul": "0.3.2", "supertest": "~0.13.0", "multiparty": "~3.3.2", "body-parser": "~1.8.2", "connect-redis": "~2.1.0", "cookie-parser": "~1.3.3", "express-session": "~1.8.2", "method-override": "~2.2.0"}, "dist": {"shasum": "988fbe666dfb1ba7f13edf7f27fea2a8bd101439", "tarball": "https://registry.npmjs.org/express/-/express-4.9.2.tgz", "integrity": "sha512-IvJTBpYZ+Tt714DZ8cBFKTGYA6pZPdaQykLN5Z8AJtMddg5HtTAVLLHRLFfkERArwfbPQPyEh9Uyl6EBwv3pPA==", "signatures": [{"sig": "MEUCIC4AXbjin3cC7nqcclOKh++xJt1imfuys9YseG1wZ6kQAiEA7XeJKdxz2ME4s4n4/G/5U4NxeSVkpFF99EGGns6UxgU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "3.17.3": {"name": "express", "version": "3.17.3", "dependencies": {"crc": "3.0.0", "depd": "0.4.5", "send": "0.9.2", "vary": "~1.0.0", "debug": "~2.0.0", "fresh": "0.2.4", "cookie": "0.1.2", "mkdirp": "0.5.0", "connect": "2.26.1", "methods": "1.1.0", "parseurl": "~1.3.0", "commander": "1.3.2", "basic-auth": "1.0.0", "proxy-addr": "~1.0.2", "escape-html": "1.0.1", "media-typer": "0.3.0", "range-parser": "~1.0.2", "cookie-signature": "1.0.5", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.6.0", "mocha": "~1.21.4", "marked": "0.3.2", "should": "~4.0.0", "istanbul": "0.3.2", "supertest": "~0.13.0", "connect-redis": "~1.5.0"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "cc25ea448a0f23225385948511f0bedb2dfa92c2", "tarball": "https://registry.npmjs.org/express/-/express-3.17.3.tgz", "integrity": "sha512-RnCiQVPKF4QFskBRHfIwsNixhMu36qO0jj/pTuhfBO5iJP7rhvOwxFUZBcg93rPapFLq9P8w4F8jogq8oJ/++g==", "signatures": [{"sig": "MEYCIQDicsSTTyFmqzp2K8FAWuvdWO14ZB/Wgw+LcMD/eoyB4QIhAMl+tuzOt0AzCKVYIBSk3WdUW3SW60skryG7AcA4QGWd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "4.9.3": {"name": "express", "version": "4.9.3", "dependencies": {"qs": "2.2.3", "depd": "0.4.5", "etag": "~1.3.1", "send": "0.9.2", "vary": "~1.0.0", "debug": "~2.0.0", "fresh": "0.2.4", "cookie": "0.1.2", "accepts": "~1.1.0", "methods": "1.1.0", "type-is": "~1.5.1", "parseurl": "~1.3.0", "proxy-addr": "~1.0.2", "escape-html": "1.0.1", "media-typer": "0.3.0", "on-finished": "~2.1.0", "utils-merge": "1.0.0", "finalhandler": "0.2.0", "range-parser": "~1.0.2", "serve-static": "~1.6.2", "path-to-regexp": "0.1.3", "cookie-signature": "1.0.5", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.6.0", "after": "0.8.1", "mocha": "~1.21.4", "vhost": "~3.0.0", "marked": "0.3.2", "morgan": "~1.3.1", "should": "~4.0.4", "istanbul": "0.3.2", "supertest": "~0.13.0", "multiparty": "~3.3.2", "body-parser": "~1.8.2", "connect-redis": "~2.1.0", "cookie-parser": "~1.3.3", "express-session": "~1.8.2", "method-override": "~2.2.0"}, "dist": {"shasum": "6aadd470fbb0fdd2550536ab33b63c3fcb7f1028", "tarball": "https://registry.npmjs.org/express/-/express-4.9.3.tgz", "integrity": "sha512-wuQn3JQ2d/Bo7SFOXZQc6JeOFFKs094UQhVYlmhcIMkmpitUKa6oHByIuVDQxrDEmgFi5ysR4wuRNFK7JREP6Q==", "signatures": [{"sig": "MEUCIQCHcwegbcZ5fLvnc//W/loSjnq5aF/V6ga4qp/9Go1eAAIgC8p9oFZf2TYOgjCNeQZQiM6NoT2BgSHStdvcLYjt8KI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "3.17.4": {"name": "express", "version": "3.17.4", "dependencies": {"crc": "3.0.0", "depd": "0.4.5", "send": "0.9.2", "vary": "~1.0.0", "debug": "~2.0.0", "fresh": "0.2.4", "cookie": "0.1.2", "mkdirp": "0.5.0", "connect": "2.26.2", "methods": "1.1.0", "parseurl": "~1.3.0", "commander": "1.3.2", "basic-auth": "1.0.0", "proxy-addr": "~1.0.2", "escape-html": "1.0.1", "media-typer": "0.3.0", "range-parser": "~1.0.2", "cookie-signature": "1.0.5", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.6.0", "mocha": "~1.21.4", "marked": "0.3.2", "should": "~4.0.0", "istanbul": "0.3.2", "supertest": "~0.13.0", "connect-redis": "~1.5.0"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "38d2749198f4d2d6b19433bd1105d065eb975a14", "tarball": "https://registry.npmjs.org/express/-/express-3.17.4.tgz", "integrity": "sha512-/h1s3HdHXFHE+ZUfmm75Vb3o30tYjOmkR/J4LS4lwJppmc+0Ngczn5qDPPSdnRxSRU5qpCddmjRgO2tjT4z4Mg==", "signatures": [{"sig": "MEYCIQDkMssqQRT1JroO+B9GKzfNucPefrgpw2HSnsu+xhv+EgIhALLoixsmmwhr0BxqwWwxxRwSyABq2lDvXCiwmhyFgoXZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "4.9.4": {"name": "express", "version": "4.9.4", "dependencies": {"qs": "2.2.4", "depd": "0.4.5", "etag": "~1.3.1", "send": "0.9.2", "vary": "~1.0.0", "debug": "~2.0.0", "fresh": "0.2.4", "cookie": "0.1.2", "accepts": "~1.1.0", "methods": "1.1.0", "type-is": "~1.5.1", "parseurl": "~1.3.0", "proxy-addr": "~1.0.2", "escape-html": "1.0.1", "media-typer": "0.3.0", "on-finished": "~2.1.0", "utils-merge": "1.0.0", "finalhandler": "0.2.0", "range-parser": "~1.0.2", "serve-static": "~1.6.2", "path-to-regexp": "0.1.3", "cookie-signature": "1.0.5", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.6.0", "after": "0.8.1", "mocha": "~1.21.4", "vhost": "~3.0.0", "marked": "0.3.2", "morgan": "~1.3.1", "should": "~4.0.4", "istanbul": "0.3.2", "supertest": "~0.13.0", "multiparty": "~3.3.2", "body-parser": "~1.8.2", "connect-redis": "~2.1.0", "cookie-parser": "~1.3.3", "express-session": "~1.8.2", "method-override": "~2.2.0"}, "dist": {"shasum": "008e18c92add61fcb534968e04c7e0102a66690b", "tarball": "https://registry.npmjs.org/express/-/express-4.9.4.tgz", "integrity": "sha512-3TVse44+mmguMtmixYFpXGSwTrP6QzrHaXZPywMYiCuYQRnJxDScYCiTt4A2rSto5vugNxi2QrFSqqBIizoEjQ==", "signatures": [{"sig": "MEQCIE55wiKCSW7qLpAN2Q09NhfGuuPlJwWdI6JR5h1FOLQRAiAu/zyOEKLMItjfiwKY/a441rgE6Bouywjzg0vXQFTQ9Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "3.17.5": {"name": "express", "version": "3.17.5", "dependencies": {"crc": "3.0.0", "depd": "0.4.5", "send": "0.9.3", "vary": "~1.0.0", "debug": "~2.0.0", "fresh": "0.2.4", "cookie": "0.1.2", "mkdirp": "0.5.0", "connect": "2.26.3", "methods": "1.1.0", "parseurl": "~1.3.0", "commander": "1.3.2", "basic-auth": "1.0.0", "proxy-addr": "~1.0.3", "escape-html": "1.0.1", "media-typer": "0.3.0", "range-parser": "~1.0.2", "cookie-signature": "1.0.5", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.6.0", "mocha": "~1.21.4", "marked": "0.3.2", "should": "~4.0.0", "istanbul": "0.3.2", "supertest": "~0.13.0", "connect-redis": "~1.5.0"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "859f4f7bd8d4b8656982592d432f6a0ee06afd30", "tarball": "https://registry.npmjs.org/express/-/express-3.17.5.tgz", "integrity": "sha512-eCd0IfQAJHVdMIDHh/s1fnoblv1OXR8zHcbpmLPqOry7KgXzPTlfLspmm0VdUw5NIHTg/NFKTSq/ZeRR55CuHQ==", "signatures": [{"sig": "MEYCIQCq7krgCcCQGwjVs4DA5MUoDuf42XUYCJfcgeKFLCS0LwIhAIVld4uo+C1En312SRNiywq+fanfNRLrRtglB4LPZfIC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "4.9.5": {"name": "express", "version": "4.9.5", "dependencies": {"qs": "2.2.4", "depd": "0.4.5", "etag": "~1.4.0", "send": "0.9.3", "vary": "~1.0.0", "debug": "~2.0.0", "fresh": "0.2.4", "cookie": "0.1.2", "accepts": "~1.1.0", "methods": "1.1.0", "type-is": "~1.5.1", "parseurl": "~1.3.0", "proxy-addr": "~1.0.3", "escape-html": "1.0.1", "media-typer": "0.3.0", "on-finished": "~2.1.0", "utils-merge": "1.0.0", "finalhandler": "0.2.0", "range-parser": "~1.0.2", "serve-static": "~1.6.3", "path-to-regexp": "0.1.3", "cookie-signature": "1.0.5", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.6.0", "after": "0.8.1", "mocha": "~1.21.4", "vhost": "~3.0.0", "marked": "0.3.2", "morgan": "~1.3.1", "should": "~4.0.4", "istanbul": "0.3.2", "supertest": "~0.13.0", "multiparty": "~3.3.2", "body-parser": "~1.8.2", "connect-redis": "~2.1.0", "cookie-parser": "~1.3.3", "express-session": "~1.8.2", "method-override": "~2.2.0"}, "dist": {"shasum": "7f62aa84ac8f5e96acfb98e2944dde0bf1cf8688", "tarball": "https://registry.npmjs.org/express/-/express-4.9.5.tgz", "integrity": "sha512-IlyZzYirNGdVfD4uqphSGfSMJIrs19IeWO6Jalom+pIstQPd32DPzpw6hQ95kfwomjeCm/FH9nEaRo/INOMLMA==", "signatures": [{"sig": "MEQCIBiQzLSrBdkC2TK5g/XohCn66FQCP0f9mXvCXmyaLESxAiAywlLmAgOiufrgFe4YtQRzUHhE4f+pvKmbVNjXRCGtTg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "3.17.6": {"name": "express", "version": "3.17.6", "dependencies": {"crc": "3.0.0", "depd": "0.4.5", "send": "0.9.3", "vary": "~1.0.0", "debug": "~2.0.0", "fresh": "0.2.4", "cookie": "0.1.2", "mkdirp": "0.5.0", "connect": "2.26.4", "methods": "1.1.0", "parseurl": "~1.3.0", "commander": "1.3.2", "basic-auth": "1.0.0", "proxy-addr": "~1.0.3", "escape-html": "1.0.1", "media-typer": "0.3.0", "range-parser": "~1.0.2", "cookie-signature": "1.0.5", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.6.0", "mocha": "~1.21.4", "marked": "0.3.2", "should": "~4.0.0", "istanbul": "0.3.2", "supertest": "~0.13.0", "connect-redis": "~1.5.0"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "e2f9a6a48b85233afc4f7b6c5cd6799c53f5f46f", "tarball": "https://registry.npmjs.org/express/-/express-3.17.6.tgz", "integrity": "sha512-AeTZ3lHZVe/RKFUnZq1KK/HxswL8vTNFG0AiU+9XNAQcOwtnJGHh4DZFMvA8IH7FFuWyAHCnWRkgjpG89w+5Yg==", "signatures": [{"sig": "MEYCIQC1kLVUUUIciw10aTmV+87POUTasg/R3OTHUZMV3M7q1gIhANAVQbm0IitV07JtXbJNpcZjQjr7F3eXCiy2AjIPKKnr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "3.17.7": {"name": "express", "version": "3.17.7", "dependencies": {"crc": "3.0.0", "depd": "0.4.5", "send": "0.9.3", "vary": "~1.0.0", "debug": "~2.0.0", "fresh": "0.2.4", "cookie": "0.1.2", "mkdirp": "0.5.0", "connect": "2.26.5", "methods": "1.1.0", "parseurl": "~1.3.0", "commander": "1.3.2", "basic-auth": "1.0.0", "proxy-addr": "~1.0.3", "escape-html": "1.0.1", "media-typer": "0.3.0", "range-parser": "~1.0.2", "cookie-signature": "1.0.5", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.6.0", "mocha": "~1.21.4", "marked": "0.3.2", "should": "~4.0.0", "istanbul": "0.3.2", "supertest": "~0.14.0", "connect-redis": "~1.5.0"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "4261113907252e0b4b8346a342d321fe7fd11d75", "tarball": "https://registry.npmjs.org/express/-/express-3.17.7.tgz", "integrity": "sha512-vObzQ3Sh9RzvkvV0/11zyHZpELGHnCOg0Mg7OX9DKQ2oWJ+0zI26ewXFJYwYZwYsE+hLXie5sIlbsWZ5n6kHGw==", "signatures": [{"sig": "MEUCIQC8lUPYdgogS58DntS/FKyoxQ8tVXTKLSw6C3gDRICZFAIgBmByX8whg2Hom8N6RDXXvBvRLT8nVTm9tvigY9UW7zY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "4.9.6": {"name": "express", "version": "4.9.6", "dependencies": {"qs": "2.2.4", "depd": "0.4.5", "etag": "~1.4.0", "send": "0.9.3", "vary": "~1.0.0", "debug": "~2.0.0", "fresh": "0.2.4", "cookie": "0.1.2", "accepts": "~1.1.1", "methods": "1.1.0", "type-is": "~1.5.2", "parseurl": "~1.3.0", "proxy-addr": "~1.0.3", "escape-html": "1.0.1", "media-typer": "0.3.0", "on-finished": "~2.1.0", "utils-merge": "1.0.0", "finalhandler": "0.2.0", "range-parser": "~1.0.2", "serve-static": "~1.6.4", "path-to-regexp": "0.1.3", "cookie-signature": "1.0.5", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.6.0", "after": "0.8.1", "mocha": "~1.21.4", "vhost": "~3.0.0", "marked": "0.3.2", "morgan": "~1.3.1", "should": "~4.0.4", "istanbul": "0.3.2", "supertest": "~0.14.0", "multiparty": "~3.3.2", "body-parser": "~1.8.2", "connect-redis": "~2.1.0", "cookie-parser": "~1.3.3", "express-session": "~1.8.2", "method-override": "~2.2.0"}, "dist": {"shasum": "0b3e3970784d9133c4335c299539e6d895dbb208", "tarball": "https://registry.npmjs.org/express/-/express-4.9.6.tgz", "integrity": "sha512-tBojShQ7yTtXqpIUkjSkDZx/Sdh0xaeJWkIwQt4UoPynF9si9KeIfdRlsaT4jAIEhvnE7T1hkmm8gdn5y0cqFQ==", "signatures": [{"sig": "MEYCIQCcFdJ9xqCBffmF23WdRSp/4XQ4JoVtnrTJunTx7i+6CwIhALwiZnj70S5odlwgpY0v6uDB/rihTJkcXqizD1N6HCNz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "4.9.7": {"name": "express", "version": "4.9.7", "dependencies": {"qs": "2.2.4", "depd": "0.4.5", "etag": "~1.4.0", "send": "0.9.3", "vary": "~1.0.0", "debug": "~2.0.0", "fresh": "0.2.4", "cookie": "0.1.2", "accepts": "~1.1.1", "methods": "1.1.0", "type-is": "~1.5.2", "parseurl": "~1.3.0", "proxy-addr": "~1.0.3", "escape-html": "1.0.1", "media-typer": "0.3.0", "on-finished": "~2.1.0", "utils-merge": "1.0.0", "finalhandler": "0.2.0", "range-parser": "~1.0.2", "serve-static": "~1.6.4", "path-to-regexp": "0.1.3", "cookie-signature": "1.0.5", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.6.0", "after": "0.8.1", "mocha": "~1.21.4", "vhost": "~3.0.0", "marked": "0.3.2", "morgan": "~1.3.1", "should": "~4.0.4", "istanbul": "0.3.2", "supertest": "~0.14.0", "multiparty": "~3.3.2", "body-parser": "~1.8.2", "connect-redis": "~2.1.0", "cookie-parser": "~1.3.3", "express-session": "~1.8.2", "method-override": "~2.2.0"}, "dist": {"shasum": "ae3e0bdf0095749467fde125afd77e7988ff0fbb", "tarball": "https://registry.npmjs.org/express/-/express-4.9.7.tgz", "integrity": "sha512-rtzuJlqDeyaJ2K2tJ/Vz5NupUVqI1IIHOujeszP0pQiD07YwEyNSlbrLcsX4EgSqGTObqAXNCeoEpZLi0L9/ew==", "signatures": [{"sig": "MEUCIQD7ZHgrnHGDyJTvxjOZQd+mjGVfs2l8WOcF64KWa+e2OwIgeETcfTnk/w02cna0/1LHE4QK9/a89QOZ5y5sPWnjGQI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "3.17.8": {"name": "express", "version": "3.17.8", "dependencies": {"crc": "3.0.0", "depd": "0.4.5", "send": "0.9.3", "vary": "~1.0.0", "debug": "~2.0.0", "fresh": "0.2.4", "cookie": "0.1.2", "mkdirp": "0.5.0", "connect": "2.26.6", "methods": "1.1.0", "parseurl": "~1.3.0", "commander": "1.3.2", "basic-auth": "1.0.0", "proxy-addr": "~1.0.3", "escape-html": "1.0.1", "media-typer": "0.3.0", "range-parser": "~1.0.2", "cookie-signature": "1.0.5", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.6.0", "mocha": "~1.21.5", "marked": "0.3.2", "should": "~4.0.0", "istanbul": "0.3.2", "supertest": "~0.14.0", "connect-redis": "~1.5.0"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "f0a451865f31938ea518a924c6f521df2d474d4b", "tarball": "https://registry.npmjs.org/express/-/express-3.17.8.tgz", "integrity": "sha512-O99UeYYsd/RrFYIfVRGBQnGyCH4FVfqJOKv8cqt3RcL8JqUqjgadiQYyXSYNJS/rYRF3bzOU2YXEe8G9xdrpFg==", "signatures": [{"sig": "MEYCIQCBOetde+rn+n5tC+UznezW8Ic5DCos/2Qy5TpDmJgQGQIhAJQSHZkB+5sDWl7gulpVD8Yly4QeZY+lGW2tt7nx4EI8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "4.9.8": {"name": "express", "version": "4.9.8", "dependencies": {"qs": "2.2.4", "depd": "0.4.5", "etag": "~1.4.0", "send": "0.9.3", "vary": "~1.0.0", "debug": "~2.0.0", "fresh": "0.2.4", "cookie": "0.1.2", "accepts": "~1.1.2", "methods": "1.1.0", "type-is": "~1.5.2", "parseurl": "~1.3.0", "proxy-addr": "~1.0.3", "escape-html": "1.0.1", "media-typer": "0.3.0", "on-finished": "~2.1.0", "utils-merge": "1.0.0", "finalhandler": "0.2.0", "range-parser": "~1.0.2", "serve-static": "~1.6.4", "path-to-regexp": "0.1.3", "cookie-signature": "1.0.5", "merge-descriptors": "0.0.2"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.6.0", "after": "0.8.1", "mocha": "~1.21.5", "vhost": "~3.0.0", "marked": "0.3.2", "morgan": "~1.3.1", "should": "~4.0.4", "istanbul": "0.3.2", "supertest": "~0.14.0", "multiparty": "~3.3.2", "body-parser": "~1.8.2", "connect-redis": "~2.1.0", "cookie-parser": "~1.3.3", "express-session": "~1.8.2", "method-override": "~2.2.0"}, "dist": {"shasum": "f360f596baeabbd0e5223b603d6eb578d9d2d10d", "tarball": "https://registry.npmjs.org/express/-/express-4.9.8.tgz", "integrity": "sha512-PPN6AUsOM1V2SsX7sUCljN4pxF2dhFIeSBUwNe4AsK9MtYPYOf28W6EQUS5h8WnRdvjnSWe/Ld5tEi/gP9t5Ag==", "signatures": [{"sig": "MEQCIEIO1vLLobpiM1ajTKsXQODNBIETt/Og2GpI2MWY8L+eAiAN6bp0qbLOXxuVGIBwoyioL2qfEsmhk8LCV/w9entyEQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "3.18.0": {"name": "express", "version": "3.18.0", "dependencies": {"depd": "~1.0.0", "etag": "~1.5.0", "send": "0.10.0", "vary": "~1.0.0", "debug": "~2.1.0", "fresh": "0.2.4", "cookie": "0.1.2", "mkdirp": "0.5.0", "connect": "2.27.0", "methods": "1.1.0", "parseurl": "~1.3.0", "commander": "1.3.2", "basic-auth": "1.0.0", "proxy-addr": "~1.0.3", "escape-html": "1.0.1", "media-typer": "0.3.0", "range-parser": "~1.0.2", "cookie-signature": "1.0.5", "merge-descriptors": "0.0.2", "content-disposition": "0.5.0"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.7.0", "mocha": "~1.21.5", "marked": "0.3.2", "should": "~4.0.0", "istanbul": "0.3.2", "supertest": "~0.14.0", "connect-redis": "~1.5.0"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "ff1f4ee689ba6e622a087e397994f7c2115c5c57", "tarball": "https://registry.npmjs.org/express/-/express-3.18.0.tgz", "integrity": "sha512-mdN1G/8PIiXEXBc/OSr89ftAekBElETC9KXzNDRQCAJR5LVQ2zKBo+3xNEqNIoTE0LHpcenFT0CpRfy1dxE/FA==", "signatures": [{"sig": "MEYCIQCwWigRkVNWJXfom577t8j+L514Nx/ZcOdrmI8enYoopgIhAOQDacQA06lAZUmE0nExhseoarl56+93abUJa/waGK/0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "3.18.1": {"name": "express", "version": "3.18.1", "dependencies": {"depd": "~1.0.0", "etag": "~1.5.0", "send": "0.10.1", "vary": "~1.0.0", "debug": "~2.1.0", "fresh": "0.2.4", "cookie": "0.1.2", "mkdirp": "0.5.0", "connect": "2.27.1", "methods": "1.1.0", "parseurl": "~1.3.0", "commander": "1.3.2", "basic-auth": "1.0.0", "proxy-addr": "~1.0.3", "escape-html": "1.0.1", "media-typer": "0.3.0", "utils-merge": "1.0.0", "range-parser": "~1.0.2", "cookie-signature": "1.0.5", "merge-descriptors": "0.0.2", "content-disposition": "0.5.0"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.7.0", "mocha": "~2.0.0", "marked": "0.3.2", "should": "~4.1.0", "istanbul": "0.3.2", "supertest": "~0.14.0", "connect-redis": "~1.5.0"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "0bbd6269abbdb53482166b0b5a9a04e311be9977", "tarball": "https://registry.npmjs.org/express/-/express-3.18.1.tgz", "integrity": "sha512-mZnd3s8n/hT3c3gn7Lmx5yElm6UaY802CdFPoTv8IwVfRKV8kmv9zxj41npo4j9hNSRid20Dnu4j313uMJ9Yqw==", "signatures": [{"sig": "MEYCIQCcoygLxOEvilFx6LWIcVCSBwW6+33P4IzCVKHXERIwuwIhALZ1cmXYu7/qKtlAC1xW6I56ANy1tWeMh91Z9LYqI2S4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "4.10.0": {"name": "express", "version": "4.10.0", "dependencies": {"qs": "2.3.0", "depd": "~1.0.0", "etag": "~1.5.0", "send": "0.10.1", "vary": "~1.0.0", "debug": "~2.1.0", "fresh": "0.2.4", "cookie": "0.1.2", "accepts": "~1.1.2", "methods": "1.1.0", "type-is": "~1.5.2", "parseurl": "~1.3.0", "proxy-addr": "~1.0.3", "escape-html": "1.0.1", "media-typer": "0.3.0", "on-finished": "~2.1.1", "utils-merge": "1.0.0", "finalhandler": "0.3.2", "range-parser": "~1.0.2", "serve-static": "~1.7.1", "path-to-regexp": "0.1.3", "cookie-signature": "1.0.5", "merge-descriptors": "0.0.2", "content-disposition": "0.5.0"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.7.0", "after": "0.8.1", "mocha": "~2.0.0", "vhost": "~3.0.0", "marked": "0.3.2", "morgan": "~1.4.1", "should": "~4.1.0", "istanbul": "0.3.2", "supertest": "~0.14.0", "multiparty": "~4.0.0", "body-parser": "~1.9.1", "connect-redis": "~2.1.0", "cookie-parser": "~1.3.3", "express-session": "~1.9.1", "method-override": "~2.3.0"}, "dist": {"shasum": "52719d5a1cde4edd47b87da43b1a7c337d761a12", "tarball": "https://registry.npmjs.org/express/-/express-4.10.0.tgz", "integrity": "sha512-n/+TXZzQQo6ZpHfuskjmkLt6PTHWNY5FHfL7zHLAcLPS7kFfYnEQWG6TqZGCd8aQoIr4Vf51RMZKqH3Xf5m+cw==", "signatures": [{"sig": "MEYCIQDYyR4Qs+DmnpDkML1nCM9MAOronrtRyAA2C/VKjzgSJQIhAMqaSs8bdfZUeoBxNSHPo22hzwYMEyZOdyNR7n3XIdBR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "3.18.2": {"name": "express", "version": "3.18.2", "dependencies": {"depd": "~1.0.0", "etag": "~1.5.0", "send": "0.10.1", "vary": "~1.0.0", "debug": "~2.1.0", "fresh": "0.2.4", "cookie": "0.1.2", "mkdirp": "0.5.0", "connect": "2.27.2", "methods": "1.1.0", "parseurl": "~1.3.0", "commander": "1.3.2", "basic-auth": "1.0.0", "proxy-addr": "~1.0.3", "escape-html": "1.0.1", "media-typer": "0.3.0", "utils-merge": "1.0.0", "range-parser": "~1.0.2", "cookie-signature": "1.0.5", "merge-descriptors": "0.0.2", "content-disposition": "0.5.0"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.7.0", "mocha": "~2.0.0", "marked": "0.3.2", "should": "~4.1.0", "istanbul": "0.3.2", "supertest": "~0.14.0", "connect-redis": "~1.5.0"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "7f92bce77e4f606a8defcf6aed54f8cfa0e044ca", "tarball": "https://registry.npmjs.org/express/-/express-3.18.2.tgz", "integrity": "sha512-rwbk7n4Iv5h7b1vZg3V62DNqVADeYcPl9TeIrWinXQ3NH6+iDdsjteJdKhGzuZdMUX2DNCzpK7hgejn99CRlaQ==", "signatures": [{"sig": "MEQCIEy2eBz0kWwfh+27JPXkpeE380clXea6YvYttTJm/eRwAiBwvwZ9kOhrAAs4XYEiNBggbXfyGeaSU8KCF3AHc/sNjg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "4.10.1": {"name": "express", "version": "4.10.1", "dependencies": {"qs": "2.3.2", "depd": "~1.0.0", "etag": "~1.5.0", "send": "0.10.1", "vary": "~1.0.0", "debug": "~2.1.0", "fresh": "0.2.4", "cookie": "0.1.2", "accepts": "~1.1.2", "methods": "1.1.0", "type-is": "~1.5.2", "parseurl": "~1.3.0", "proxy-addr": "~1.0.3", "escape-html": "1.0.1", "media-typer": "0.3.0", "on-finished": "~2.1.1", "utils-merge": "1.0.0", "finalhandler": "0.3.2", "range-parser": "~1.0.2", "serve-static": "~1.7.1", "path-to-regexp": "0.1.3", "cookie-signature": "1.0.5", "merge-descriptors": "0.0.2", "content-disposition": "0.5.0"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.7.0", "after": "0.8.1", "mocha": "~2.0.0", "vhost": "~3.0.0", "marked": "0.3.2", "morgan": "~1.4.1", "should": "~4.1.0", "istanbul": "0.3.2", "supertest": "~0.14.0", "multiparty": "~4.0.0", "body-parser": "~1.9.1", "connect-redis": "~2.1.0", "cookie-parser": "~1.3.3", "express-session": "~1.9.1", "method-override": "~2.3.0"}, "dist": {"shasum": "a291c812bc8b0ed6ab877366fe0e68a2368fde7e", "tarball": "https://registry.npmjs.org/express/-/express-4.10.1.tgz", "integrity": "sha512-8TUDI3wcFBu1QvoFOIWBAqWzYGa/wwcqsgzTyd530DZdGZmHc4HidFajerAusCeABpnSd9cNoM9XRyHCtD6qOw==", "signatures": [{"sig": "MEQCIB6fpia2kb8KFqtkUFMQTQkOlPdmdsqyKz1ctwtL4PlAAiA6asdN+DnvB23awhTX2jSpABCv4i04JwlBNmcqvvUWLw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "5.0.0-alpha.1": {"name": "express", "version": "5.0.0-alpha.1", "dependencies": {"qs": "2.3.2", "depd": "~1.0.0", "etag": "~1.5.0", "send": "0.10.1", "vary": "~1.0.0", "debug": "~2.1.0", "fresh": "0.2.4", "cookie": "0.1.2", "accepts": "~1.1.2", "methods": "1.1.0", "type-is": "~1.5.2", "parseurl": "~1.3.0", "proxy-addr": "~1.0.3", "escape-html": "1.0.1", "media-typer": "0.3.0", "on-finished": "~2.1.1", "utils-merge": "1.0.0", "finalhandler": "0.3.2", "range-parser": "~1.0.2", "serve-static": "~1.7.1", "path-to-regexp": "0.1.3", "cookie-signature": "1.0.5", "merge-descriptors": "0.0.2", "content-disposition": "0.5.0"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.7.0", "after": "0.8.1", "mocha": "~2.0.0", "vhost": "~3.0.0", "marked": "0.3.2", "morgan": "~1.4.1", "should": "~4.1.0", "istanbul": "0.3.2", "supertest": "~0.14.0", "multiparty": "~4.0.0", "body-parser": "~1.9.1", "connect-redis": "~2.1.0", "cookie-parser": "~1.3.3", "express-session": "~1.9.1", "method-override": "~2.3.0"}, "dist": {"shasum": "415df02c51ae01c221362fca59b03591d956b2d7", "tarball": "https://registry.npmjs.org/express/-/express-5.0.0-alpha.1.tgz", "integrity": "sha512-a2POJuyTqwuIfCbgEnThsLYZk9wbicdMo5NCyr5r+s8SQZRzNqpQB3XypCbg4eHeiIfYRy33x5t63kMXtsIoZg==", "signatures": [{"sig": "MEUCIQC1PUdoMW6h9tvuXZSecazTQOWREy2jjbD3g4YuG8hPLwIgftBiNPcvZxkgFKMqjJHcnIs+W/gpJ0ahvgXBb3LhTLA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "3.18.3": {"name": "express", "version": "3.18.3", "dependencies": {"depd": "~1.0.0", "etag": "~1.5.0", "send": "0.10.1", "vary": "~1.0.0", "debug": "~2.1.0", "fresh": "0.2.4", "cookie": "0.1.2", "mkdirp": "0.5.0", "connect": "2.27.3", "methods": "1.1.0", "parseurl": "~1.3.0", "commander": "1.3.2", "basic-auth": "1.0.0", "proxy-addr": "~1.0.3", "escape-html": "1.0.1", "media-typer": "0.3.0", "utils-merge": "1.0.0", "range-parser": "~1.0.2", "cookie-signature": "1.0.5", "merge-descriptors": "0.0.2", "content-disposition": "0.5.0"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.7.0", "mocha": "~2.0.0", "marked": "0.3.2", "should": "~4.2.1", "istanbul": "0.3.2", "supertest": "~0.14.0", "connect-redis": "~1.5.0"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "4020829da766557f308161b3d0ea01c838b2aff6", "tarball": "https://registry.npmjs.org/express/-/express-3.18.3.tgz", "integrity": "sha512-2voKm/hfM1rG6ORXQfWXJxafommRQIHr2NrG0GyVHUeWqqZXe+3Rg4I43iiqrMpMchegnxilRP30I5vpBd5xYg==", "signatures": [{"sig": "MEQCIGtA4s5+4ycODA844KCbrFn4WU539cm4w/vH1mHdjBV3AiAbVN8fqjS3DlQ3I5WwSUfVoUsre7P4ZdIVbjcS0h/U0w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "4.10.2": {"name": "express", "version": "4.10.2", "dependencies": {"qs": "2.3.2", "depd": "~1.0.0", "etag": "~1.5.0", "send": "0.10.1", "vary": "~1.0.0", "debug": "~2.1.0", "fresh": "0.2.4", "cookie": "0.1.2", "accepts": "~1.1.3", "methods": "1.1.0", "type-is": "~1.5.3", "parseurl": "~1.3.0", "proxy-addr": "~1.0.3", "escape-html": "1.0.1", "media-typer": "0.3.0", "on-finished": "~2.1.1", "utils-merge": "1.0.0", "finalhandler": "0.3.2", "range-parser": "~1.0.2", "serve-static": "~1.7.1", "path-to-regexp": "0.1.3", "cookie-signature": "1.0.5", "merge-descriptors": "0.0.2", "content-disposition": "0.5.0"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.7.0", "after": "0.8.1", "mocha": "~2.0.0", "vhost": "~3.0.0", "marked": "0.3.2", "morgan": "~1.4.1", "should": "~4.2.1", "istanbul": "0.3.2", "supertest": "~0.14.0", "multiparty": "~4.0.0", "body-parser": "~1.9.1", "connect-redis": "~2.1.0", "cookie-parser": "~1.3.3", "express-session": "~1.9.1", "method-override": "~2.3.0"}, "dist": {"shasum": "df06dde94d968932829d440a2004c5efe64495b0", "tarball": "https://registry.npmjs.org/express/-/express-4.10.2.tgz", "integrity": "sha512-RzDZFi0JhDz0/vX3nThylfdnXWaFsxvFNkiwPuCCvRe8SwUby9KzmzZpEJ4ToRYKLdtIFwjlBm+x7nxQsAAhzA==", "signatures": [{"sig": "MEUCIEvX0cHvJkXsYDe+HkhMP0U0Bmz27Uo4n+Nzvvi3+9PWAiEA/LQaYCRomFzyrB7odHEN1qe9lNqgzVOHDBtfWljJE2Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "3.18.4": {"name": "express", "version": "3.18.4", "dependencies": {"depd": "~1.0.0", "etag": "~1.5.1", "send": "0.10.1", "vary": "~1.0.0", "debug": "~2.1.0", "fresh": "0.2.4", "cookie": "0.1.2", "mkdirp": "0.5.0", "connect": "2.27.4", "methods": "1.1.0", "parseurl": "~1.3.0", "commander": "1.3.2", "basic-auth": "1.0.0", "proxy-addr": "~1.0.4", "escape-html": "1.0.1", "media-typer": "0.3.0", "utils-merge": "1.0.0", "range-parser": "~1.0.2", "cookie-signature": "1.0.5", "merge-descriptors": "0.0.2", "content-disposition": "0.5.0"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.7.0", "mocha": "~2.0.0", "marked": "0.3.2", "should": "~4.3.0", "istanbul": "0.3.2", "supertest": "~0.15.0", "connect-redis": "~1.5.0"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "7b40ad2c10a987692ee97a387c21593011f03712", "tarball": "https://registry.npmjs.org/express/-/express-3.18.4.tgz", "integrity": "sha512-44ZbMufA7N/pbsxNoTz5oZ2W8SGmP7UrXO/mQ2vPpj7A+HwsJG3RG8nWnzCT6wjbV/3apRkAEWAhCkNExaeMRw==", "signatures": [{"sig": "MEQCIENO5qKbV6KACGKojaxjvtbpT1hnyfiZf9ROKVO493onAiAziswZuuXs7ZJF2KhG1JYH5Ri53MaoJ/Dmy9dk1bxHJA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "4.10.3": {"name": "express", "version": "4.10.3", "dependencies": {"qs": "2.3.3", "depd": "~1.0.0", "etag": "~1.5.1", "send": "0.10.1", "vary": "~1.0.0", "debug": "~2.1.0", "fresh": "0.2.4", "cookie": "0.1.2", "accepts": "~1.1.3", "methods": "1.1.0", "type-is": "~1.5.3", "parseurl": "~1.3.0", "proxy-addr": "~1.0.4", "escape-html": "1.0.1", "media-typer": "0.3.0", "on-finished": "~2.1.1", "utils-merge": "1.0.0", "finalhandler": "0.3.2", "range-parser": "~1.0.2", "serve-static": "~1.7.1", "path-to-regexp": "0.1.3", "cookie-signature": "1.0.5", "merge-descriptors": "0.0.2", "content-disposition": "0.5.0"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.7.0", "after": "0.8.1", "mocha": "~2.0.0", "vhost": "~3.0.0", "marked": "0.3.2", "morgan": "~1.5.0", "should": "~4.3.0", "istanbul": "0.3.2", "supertest": "~0.15.0", "multiparty": "~4.0.0", "body-parser": "~1.9.3", "connect-redis": "~2.1.0", "cookie-parser": "~1.3.3", "express-session": "~1.9.2", "method-override": "~2.3.0"}, "dist": {"shasum": "08006c11d0c519339963bf643c3d76c2765f9349", "tarball": "https://registry.npmjs.org/express/-/express-4.10.3.tgz", "integrity": "sha512-TCNYrD5CKrwoa2PEtBwHTOHiS+vfqXJqubz8q/s29O83DukcAOwwgMrnt8ZhDxQBLtkJtbyn2ZoG+BG+FbqjZg==", "signatures": [{"sig": "MEQCIA1jWycnX7zTZnmol+VVnQCq61+BV5UcJFuKLzoZwFQRAiBPpkyA0JGGI9lb2qTMHk3RW2CWCzb9vS82RnOpsJpboA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "4.10.4": {"name": "express", "version": "4.10.4", "dependencies": {"qs": "2.3.3", "depd": "~1.0.0", "etag": "~1.5.1", "send": "0.10.1", "vary": "~1.0.0", "debug": "~2.1.0", "fresh": "0.2.4", "cookie": "0.1.2", "accepts": "~1.1.3", "methods": "1.1.0", "type-is": "~1.5.3", "parseurl": "~1.3.0", "proxy-addr": "~1.0.4", "escape-html": "1.0.1", "media-typer": "0.3.0", "on-finished": "~2.1.1", "utils-merge": "1.0.0", "finalhandler": "0.3.2", "range-parser": "~1.0.2", "serve-static": "~1.7.1", "path-to-regexp": "0.1.3", "cookie-signature": "1.0.5", "merge-descriptors": "0.0.2", "content-disposition": "0.5.0"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.7.0", "after": "0.8.1", "mocha": "~2.0.0", "vhost": "~3.0.0", "marked": "0.3.2", "morgan": "~1.5.0", "should": "~4.3.0", "istanbul": "0.3.2", "supertest": "~0.15.0", "multiparty": "~4.0.0", "body-parser": "~1.9.3", "connect-redis": "~2.1.0", "cookie-parser": "~1.3.3", "express-session": "~1.9.2", "method-override": "~2.3.0"}, "dist": {"shasum": "31aa70acdad6b6093945c30523df8537336deb58", "tarball": "https://registry.npmjs.org/express/-/express-4.10.4.tgz", "integrity": "sha512-PtQBuV54+fDGeAR5jc8wrb96QY93DCheUjtn09NQj5cIPxCom+mV5zsltItNMUqIh8Fd83JS9+iB08nI46SIvg==", "signatures": [{"sig": "MEUCIHTal2wtYi/nfCYLKfMW463xcYovGnfUE9OwG8V24AUpAiEAjW4BaF+J5wX2ryNMCFoR/PUE7mtJO3HTl4TGbanALP8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "4.10.5": {"name": "express", "version": "4.10.5", "dependencies": {"qs": "2.3.3", "depd": "~1.0.0", "etag": "~1.5.1", "send": "0.10.1", "vary": "~1.0.0", "debug": "~2.1.0", "fresh": "0.2.4", "cookie": "0.1.2", "accepts": "~1.1.4", "methods": "1.1.0", "type-is": "~1.5.4", "parseurl": "~1.3.0", "proxy-addr": "~1.0.4", "escape-html": "1.0.1", "media-typer": "0.3.0", "on-finished": "~2.1.1", "utils-merge": "1.0.0", "finalhandler": "0.3.2", "range-parser": "~1.0.2", "serve-static": "~1.7.1", "path-to-regexp": "0.1.3", "cookie-signature": "1.0.5", "merge-descriptors": "0.0.2", "content-disposition": "0.5.0"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.7.0", "after": "0.8.1", "mocha": "~2.0.0", "vhost": "~3.0.0", "marked": "0.3.2", "morgan": "~1.5.0", "should": "~4.3.0", "istanbul": "0.3.2", "supertest": "~0.15.0", "multiparty": "~4.0.0", "body-parser": "~1.9.3", "connect-redis": "~2.1.0", "cookie-parser": "~1.3.3", "express-session": "~1.9.2", "method-override": "~2.3.0"}, "dist": {"shasum": "cdcff3ea56f9cd8017043356553661cbae161f4f", "tarball": "https://registry.npmjs.org/express/-/express-4.10.5.tgz", "integrity": "sha512-ez14J1uW0DMDXihVX/+nfXysMHRkPNsbqMeT09wsB70zhFgsm9F2sVEhzhFFc5LQEa+jtZj+WI5Kj3V0YWCYgQ==", "signatures": [{"sig": "MEUCIGxXM5MoBX/E3g6xOtwB2VL580kMW5lVeZ7jK0HN1coPAiEA9Ydd3FSAFdlSWnQMBW0VrF8EwhrYKgEK4WHuIEuXiYM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "3.18.5": {"name": "express", "version": "3.18.5", "dependencies": {"depd": "~1.0.0", "etag": "~1.5.1", "send": "0.10.1", "vary": "~1.0.0", "debug": "~2.1.0", "fresh": "0.2.4", "cookie": "0.1.2", "mkdirp": "0.5.0", "connect": "2.27.6", "methods": "1.1.0", "parseurl": "~1.3.0", "commander": "1.3.2", "basic-auth": "1.0.0", "proxy-addr": "~1.0.4", "escape-html": "1.0.1", "media-typer": "0.3.0", "utils-merge": "1.0.0", "range-parser": "~1.0.2", "cookie-signature": "1.0.5", "merge-descriptors": "0.0.2", "content-disposition": "0.5.0"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "mocha": "~2.0.0", "marked": "0.3.2", "should": "~4.3.1", "istanbul": "0.3.5", "supertest": "~0.15.0", "connect-redis": "~1.5.0"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "bf0feb8562f82419ffdacf7c2315755758bfd7ec", "tarball": "https://registry.npmjs.org/express/-/express-3.18.5.tgz", "integrity": "sha512-as2HKBlZmVJtQ+uiWUT0pgS+E41DXFvAAs1QLeMQ8dK06qCdr3JGpatSYsDTWizuefw22q/eodu/zGYidWy58g==", "signatures": [{"sig": "MEQCIFVAR2AraaKnebH4Ir5UF6RXgUqmiGQEWwslgKNFyWd+AiACsGpt/ft2F/DfhNRTzs0RL2fMaC7vpMF4TY3xKb0HSg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "3.18.6": {"name": "express", "version": "3.18.6", "dependencies": {"depd": "~1.0.0", "etag": "~1.5.1", "send": "0.10.1", "vary": "~1.0.0", "debug": "~2.1.0", "fresh": "0.2.4", "cookie": "0.1.2", "mkdirp": "0.5.0", "connect": "2.27.6", "methods": "1.1.0", "parseurl": "~1.3.0", "commander": "1.3.2", "basic-auth": "1.0.0", "proxy-addr": "~1.0.4", "escape-html": "1.0.1", "media-typer": "0.3.0", "utils-merge": "1.0.0", "range-parser": "~1.0.2", "cookie-signature": "1.0.5", "merge-descriptors": "0.0.2", "content-disposition": "0.5.0"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "mocha": "~2.0.0", "marked": "0.3.2", "should": "~4.3.1", "istanbul": "0.3.5", "supertest": "~0.15.0", "connect-redis": "~1.5.0"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "cbcc7cb610d061ac619e5d090a5539353a3e870b", "tarball": "https://registry.npmjs.org/express/-/express-3.18.6.tgz", "integrity": "sha512-H55nAX+L05QoYG7r7aNeoUSn/DCh5/RyloG25oefKtyhSf2Kp+83yhtH3OweWPVeowcY8fYsvYwblXPlmp/ylA==", "signatures": [{"sig": "MEUCIGhg+R4su3TJp9Nlvft09WczJPVJKkmrzg8r9IOg3+6UAiEAlikvQDDVb7rcWQO1Zi0V9N+JsAZbCj5puI9o7WHCYMU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "4.10.6": {"name": "express", "version": "4.10.6", "dependencies": {"qs": "2.3.3", "depd": "~1.0.0", "etag": "~1.5.1", "send": "0.10.1", "vary": "~1.0.0", "debug": "~2.1.0", "fresh": "0.2.4", "cookie": "0.1.2", "accepts": "~1.1.4", "methods": "1.1.0", "type-is": "~1.5.4", "parseurl": "~1.3.0", "proxy-addr": "~1.0.4", "escape-html": "1.0.1", "media-typer": "0.3.0", "on-finished": "~2.1.1", "utils-merge": "1.0.0", "finalhandler": "0.3.2", "range-parser": "~1.0.2", "serve-static": "~1.7.1", "path-to-regexp": "0.1.3", "cookie-signature": "1.0.5", "merge-descriptors": "0.0.2", "content-disposition": "0.5.0"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.7.0", "after": "0.8.1", "mocha": "~2.0.0", "vhost": "~3.0.0", "marked": "0.3.2", "morgan": "~1.5.0", "should": "~4.3.1", "istanbul": "0.3.5", "supertest": "~0.15.0", "multiparty": "~4.0.0", "body-parser": "~1.9.3", "connect-redis": "~2.1.0", "cookie-parser": "~1.3.3", "express-session": "~1.9.2", "method-override": "~2.3.0"}, "dist": {"shasum": "a9015979ccf38b11a39c0f726dcf6c4b85a4e758", "tarball": "https://registry.npmjs.org/express/-/express-4.10.6.tgz", "integrity": "sha512-4YjjwFJ3W0SnOqT0o6bZ7YZk/joOrrvkWKUEk7ydFJo6U2nvBfBmPvi2Iah5L9Xl5dG8ozlV+sCpFymkOZ6p+g==", "signatures": [{"sig": "MEUCIQDlcxJyMQmzlQk+p+GrH3wIZG7YaUECYYJ4lZx0lTWGRgIgMqQmpGO7RKoZOUV6fmBUmCUgpb8oaggxoooCTEsDmqQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "4.10.7": {"name": "express", "version": "4.10.7", "dependencies": {"qs": "2.3.3", "depd": "~1.0.0", "etag": "~1.5.1", "send": "0.10.1", "vary": "~1.0.0", "debug": "~2.1.1", "fresh": "0.2.4", "cookie": "0.1.2", "accepts": "~1.1.4", "methods": "1.1.1", "type-is": "~1.5.5", "parseurl": "~1.3.0", "proxy-addr": "~1.0.4", "escape-html": "1.0.1", "media-typer": "0.3.0", "on-finished": "~2.2.0", "utils-merge": "1.0.0", "finalhandler": "0.3.3", "range-parser": "~1.0.2", "serve-static": "~1.7.2", "path-to-regexp": "0.1.3", "cookie-signature": "1.0.5", "merge-descriptors": "0.0.2", "content-disposition": "0.5.0"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.7.0", "after": "0.8.1", "mocha": "~2.0.0", "vhost": "~3.0.0", "marked": "0.3.2", "morgan": "~1.5.0", "should": "~4.3.1", "istanbul": "0.3.5", "supertest": "~0.15.0", "multiparty": "~4.0.0", "body-parser": "~1.9.3", "connect-redis": "~2.1.0", "cookie-parser": "~1.3.3", "express-session": "~1.9.2", "method-override": "~2.3.0"}, "dist": {"shasum": "0652f8cd5d0e2949d77b7dea7c5208161ec81ac6", "tarball": "https://registry.npmjs.org/express/-/express-4.10.7.tgz", "integrity": "sha512-mXUR7bx8lFUi6mHg3yRxZhWl2iW3JZmpnzpVIZR64UupKaCOQ6bBcfHmQyMd8u7xu0jUvqjMlLBtperSSRho8A==", "signatures": [{"sig": "MEYCIQCh+pcYE5w2Fltn+K7TfLo5kf1iIMBYmPCeBysbVjbvqAIhAK09KRBohk1lyR+WM404QTZgi7V3iIYrN/s+9pkCGy7K", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "3.19.0": {"name": "express", "version": "3.19.0", "dependencies": {"depd": "~1.0.0", "etag": "~1.5.1", "send": "0.11.0", "vary": "~1.0.0", "debug": "~2.1.1", "fresh": "0.2.4", "cookie": "0.1.2", "mkdirp": "0.5.0", "connect": "2.28.1", "methods": "~1.1.1", "parseurl": "~1.3.0", "commander": "2.6.0", "basic-auth": "1.0.0", "proxy-addr": "~1.0.5", "escape-html": "1.0.1", "media-typer": "0.3.0", "utils-merge": "1.0.0", "range-parser": "~1.0.2", "cookie-signature": "1.0.5", "merge-descriptors": "0.0.2", "content-disposition": "0.5.0"}, "devDependencies": {"ejs": "2.0.8", "hjs": "~0.0.6", "mocha": "~2.1.0", "marked": "0.3.2", "should": "~4.4.4", "istanbul": "0.3.5", "supertest": "~0.15.0", "connect-redis": "~1.5.0"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "cdac51029ccd012840d74c8c9a05834ac3a23a25", "tarball": "https://registry.npmjs.org/express/-/express-3.19.0.tgz", "integrity": "sha512-/6eJ0kTasdl0U/wg4bItdrKNnS+pKbmT+cNgJsffD22Lqb9WtTiNARlqqHEaCf1y+rxSRedrXwOR7dRyj9GlbQ==", "signatures": [{"sig": "MEUCIQCl0bLE8HsO6Ju2j8t/QNBXX7OdjlzRLdmZI5HkgfgqyQIgBTLQ+SGrYF/lNveTLhao/7gudGMrPmB0Vu74/sDQKpw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "4.10.8": {"name": "express", "version": "4.10.8", "dependencies": {"qs": "2.3.3", "depd": "~1.0.0", "etag": "~1.5.1", "send": "0.10.1", "vary": "~1.0.0", "debug": "~2.1.1", "fresh": "0.2.4", "cookie": "0.1.2", "accepts": "~1.1.4", "methods": "1.1.1", "type-is": "~1.5.5", "parseurl": "~1.3.0", "proxy-addr": "~1.0.5", "escape-html": "1.0.1", "media-typer": "0.3.0", "on-finished": "~2.2.0", "utils-merge": "1.0.0", "finalhandler": "0.3.3", "range-parser": "~1.0.2", "serve-static": "~1.7.2", "path-to-regexp": "0.1.3", "cookie-signature": "1.0.5", "merge-descriptors": "0.0.2", "content-disposition": "0.5.0"}, "devDependencies": {"ejs": "~1.0.0", "hjs": "~0.0.6", "jade": "~1.8.2", "after": "0.8.1", "mocha": "~2.0.0", "vhost": "~3.0.0", "marked": "0.3.2", "morgan": "~1.5.1", "should": "~4.3.1", "istanbul": "0.3.5", "supertest": "~0.15.0", "multiparty": "~4.1.0", "body-parser": "~1.10.1", "connect-redis": "~2.1.0", "cookie-parser": "~1.3.3", "express-session": "~1.9.2", "method-override": "~2.3.1"}, "dist": {"shasum": "2d83571e065c0efb2679c0a5f9ae66aeaa47024a", "tarball": "https://registry.npmjs.org/express/-/express-4.10.8.tgz", "integrity": "sha512-w3euLVN0/Sfj9vHPF1gu5sHoE0fyPRv7OlqrQxdz8WbMzHnTThNmKDWUj6iivW/G0WmaF4GYmLadA4xrSQ6Euw==", "signatures": [{"sig": "MEQCICZY561ciJVW41Sm3TijnyyH+/rjjGgeiLeRxa8NDGhUAiAI8ROlFwd8GlhtMWJXQWuPmEud/Jik9q/xwYr2Od4bXA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "4.11.0": {"name": "express", "version": "4.11.0", "dependencies": {"qs": "2.3.3", "depd": "~1.0.0", "etag": "~1.5.1", "send": "0.11.0", "vary": "~1.0.0", "debug": "~2.1.1", "fresh": "0.2.4", "cookie": "0.1.2", "accepts": "~1.2.2", "methods": "~1.1.1", "type-is": "~1.5.5", "parseurl": "~1.3.0", "proxy-addr": "~1.0.5", "escape-html": "1.0.1", "media-typer": "0.3.0", "on-finished": "~2.2.0", "utils-merge": "1.0.0", "finalhandler": "0.3.3", "range-parser": "~1.0.2", "serve-static": "~1.8.0", "path-to-regexp": "0.1.3", "cookie-signature": "1.0.5", "merge-descriptors": "0.0.2", "content-disposition": "0.5.0"}, "devDependencies": {"ejs": "2.0.8", "hjs": "~0.0.6", "jade": "~1.9.0", "after": "0.8.1", "mocha": "~2.1.0", "vhost": "~3.0.0", "marked": "0.3.2", "morgan": "~1.5.1", "should": "~4.4.4", "istanbul": "0.3.5", "supertest": "~0.15.0", "multiparty": "~4.1.0", "body-parser": "~1.10.1", "connect-redis": "~2.1.0", "cookie-parser": "~1.3.3", "express-session": "~1.10.1", "method-override": "~2.3.1"}, "dist": {"shasum": "ad5b5157b74a95fc5c59442efad0306e7b1aeb99", "tarball": "https://registry.npmjs.org/express/-/express-4.11.0.tgz", "integrity": "sha512-87ZlPW8cHFfTfhJ6BZ3mQ2voRUSNw7nkQKfDOzASF8IWV1uMlynW7iSftFNbNuCHYVKO6A3RGFH3MRkCiJcaoA==", "signatures": [{"sig": "MEUCIQDJ1H0D1SaTYmmcmVq7V7fjFusDTCpKIDGnroS5Sn+iVAIgeuIH2xoBJtiKvXa4XkBD2bXFuw8YBY5wEIGf9SqVDEI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "3.19.1": {"name": "express", "version": "3.19.1", "dependencies": {"depd": "~1.0.0", "etag": "~1.5.1", "send": "0.11.1", "vary": "~1.0.0", "debug": "~2.1.1", "fresh": "0.2.4", "cookie": "0.1.2", "mkdirp": "0.5.0", "connect": "2.28.2", "methods": "~1.1.1", "parseurl": "~1.3.0", "commander": "2.6.0", "basic-auth": "1.0.0", "proxy-addr": "~1.0.5", "escape-html": "1.0.1", "media-typer": "0.3.0", "utils-merge": "1.0.0", "range-parser": "~1.0.2", "cookie-signature": "1.0.5", "merge-descriptors": "0.0.2", "content-disposition": "0.5.0"}, "devDependencies": {"ejs": "2.1.4", "hjs": "~0.0.6", "mocha": "~2.1.0", "marked": "0.3.2", "should": "~4.6.1", "istanbul": "0.3.5", "supertest": "~0.15.0", "connect-redis": "~1.5.0"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "2b65f584a4c9856ff656595680f522a106b81693", "tarball": "https://registry.npmjs.org/express/-/express-3.19.1.tgz", "integrity": "sha512-rruz4J8PYvzQom6Dnuw/xytx4jQQpzn/bCGhm4a90hgH9A7qqEIFKO9/tU6psGPmlszi4JKD8bE5I1CyqBoFXg==", "signatures": [{"sig": "MEYCIQCTFs43kGKW70VSAVJ6eG+P4qnXDB/Jt0afR5o7yIvsrQIhAK7pBcQyW2wnvILa+iO2Q7hFMNxammniFiuqKg3jBROW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "4.11.1": {"name": "express", "version": "4.11.1", "dependencies": {"qs": "2.3.3", "depd": "~1.0.0", "etag": "~1.5.1", "send": "0.11.1", "vary": "~1.0.0", "debug": "~2.1.1", "fresh": "0.2.4", "cookie": "0.1.2", "accepts": "~1.2.2", "methods": "~1.1.1", "type-is": "~1.5.5", "parseurl": "~1.3.0", "proxy-addr": "~1.0.5", "escape-html": "1.0.1", "media-typer": "0.3.0", "on-finished": "~2.2.0", "utils-merge": "1.0.0", "finalhandler": "0.3.3", "range-parser": "~1.0.2", "serve-static": "~1.8.1", "path-to-regexp": "0.1.3", "cookie-signature": "1.0.5", "merge-descriptors": "0.0.2", "content-disposition": "0.5.0"}, "devDependencies": {"ejs": "2.1.4", "hjs": "~0.0.6", "jade": "~1.9.1", "after": "0.8.1", "mocha": "~2.1.0", "vhost": "~3.0.0", "marked": "0.3.2", "morgan": "~1.5.1", "should": "~4.6.1", "istanbul": "0.3.5", "supertest": "~0.15.0", "multiparty": "~4.1.1", "body-parser": "~1.10.2", "connect-redis": "~2.1.0", "cookie-parser": "~1.3.3", "express-session": "~1.10.1", "method-override": "~2.3.1"}, "dist": {"shasum": "36d04dd27aa1667634e987529767f9c99de7903f", "tarball": "https://registry.npmjs.org/express/-/express-4.11.1.tgz", "integrity": "sha512-8QjOxh+sIMSsittO0ehligYjRyDCBcAFojue9PTRBj3srR6dBHr81hUz7C0U2gRv8shvE18Khu8gUb5rWJ+FRA==", "signatures": [{"sig": "MEUCIQDz9alq0bpT90S0PDJAZT1Bw7sUxrX7qKUHNYQnZgOYPAIgQNSO4wjosz0oyrZ6rmPjKqeBj7YBoMpzDpnUo/FbkYc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "3.19.2": {"name": "express", "version": "3.19.2", "dependencies": {"depd": "~1.0.0", "etag": "~1.5.1", "send": "0.11.1", "vary": "~1.0.0", "debug": "~2.1.1", "fresh": "0.2.4", "cookie": "0.1.2", "mkdirp": "0.5.0", "connect": "2.28.3", "methods": "~1.1.1", "parseurl": "~1.3.0", "commander": "2.6.0", "basic-auth": "1.0.0", "proxy-addr": "~1.0.6", "escape-html": "1.0.1", "media-typer": "0.3.0", "utils-merge": "1.0.0", "range-parser": "~1.0.2", "cookie-signature": "1.0.5", "merge-descriptors": "0.0.2", "content-disposition": "0.5.0"}, "devDependencies": {"ejs": "2.1.4", "hjs": "~0.0.6", "mocha": "~2.1.0", "marked": "0.3.3", "should": "~4.6.2", "istanbul": "0.3.5", "supertest": "~0.15.0", "connect-redis": "~1.5.0"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "7f9b3ad8ae0f29d2df98cb3d8649dec8bcc47bf6", "tarball": "https://registry.npmjs.org/express/-/express-3.19.2.tgz", "integrity": "sha512-CjJzqfEKWKvQo/eZY64CkiXsbKiAo76ve9xCmtx7p7o3dqWrARVQ3zv00CsF4bGcS//7yIQHZJNRkbiEv9GM5g==", "signatures": [{"sig": "MEYCIQCVwkKvId39ocUzCmCv84m886cSIADjvXtdi0UUlMsiTwIhAORpWaClF1PdCO+55zDk1FLYAngvEojMECSyCoYr5wXN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "4.11.2": {"name": "express", "version": "4.11.2", "dependencies": {"qs": "2.3.3", "depd": "~1.0.0", "etag": "~1.5.1", "send": "0.11.1", "vary": "~1.0.0", "debug": "~2.1.1", "fresh": "0.2.4", "cookie": "0.1.2", "accepts": "~1.2.3", "methods": "~1.1.1", "type-is": "~1.5.6", "parseurl": "~1.3.0", "proxy-addr": "~1.0.6", "escape-html": "1.0.1", "media-typer": "0.3.0", "on-finished": "~2.2.0", "utils-merge": "1.0.0", "finalhandler": "0.3.3", "range-parser": "~1.0.2", "serve-static": "~1.8.1", "path-to-regexp": "0.1.3", "cookie-signature": "1.0.5", "merge-descriptors": "0.0.2", "content-disposition": "0.5.0"}, "devDependencies": {"ejs": "2.1.4", "hjs": "~0.0.6", "jade": "~1.9.1", "after": "0.8.1", "mocha": "~2.1.0", "vhost": "~3.0.0", "marked": "0.3.3", "morgan": "~1.5.1", "should": "~4.6.2", "istanbul": "0.3.5", "supertest": "~0.15.0", "multiparty": "~4.1.1", "body-parser": "~1.11.0", "connect-redis": "~2.2.0", "cookie-parser": "~1.3.3", "express-session": "~1.10.2", "method-override": "~2.3.1"}, "dist": {"shasum": "8df3d5a9ac848585f00a0777601823faecd3b148", "tarball": "https://registry.npmjs.org/express/-/express-4.11.2.tgz", "integrity": "sha512-aNDSFzq2p9qKvk/O7LBTiQJbWEvQbTVYNc3/X0DWk026uKGUgTW5Is3vS5lL7Q4OL9GfxYfzkpFMdbi64z3P6Q==", "signatures": [{"sig": "MEYCIQCc1wFXAsePxjMMUbej69F/M+RgLtUjNK6x9/Ihr54VJAIhAJm3EqPFBu3/5Mbd9Ys7vZ+pbelmuWXuD5c1SlRN4VbF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "3.20.0": {"name": "express", "version": "3.20.0", "dependencies": {"depd": "~1.0.0", "etag": "~1.5.1", "send": "0.12.1", "vary": "~1.0.0", "debug": "~2.1.1", "fresh": "0.2.4", "cookie": "0.1.2", "mkdirp": "0.5.0", "connect": "2.29.0", "methods": "~1.1.1", "parseurl": "~1.3.0", "commander": "2.6.0", "basic-auth": "1.0.0", "proxy-addr": "~1.0.6", "escape-html": "1.0.1", "utils-merge": "1.0.0", "content-type": "~1.0.1", "range-parser": "~1.0.2", "cookie-signature": "1.0.6", "merge-descriptors": "0.0.2", "content-disposition": "0.5.0"}, "devDependencies": {"ejs": "2.1.4", "hjs": "~0.0.6", "mocha": "~2.1.0", "marked": "0.3.3", "should": "~5.0.0", "istanbul": "0.3.5", "supertest": "~0.15.0", "connect-redis": "~1.5.0"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "9dac561e31a08e7d2852790d86d17c7b70bdd9ac", "tarball": "https://registry.npmjs.org/express/-/express-3.20.0.tgz", "integrity": "sha512-A4L8vQkl7GDMrQrrFQyyoTXjQQbGQCLR/Z/BU8IPX6ydGUyQ+5EexG5MjJl2rUmGqdTuBibnqYnn6tloZC/Uqw==", "signatures": [{"sig": "MEQCIGQPh769ViHfDoXOv5V4wpSxCXLJurWuaCqhOCxQF4W2AiAKwQWI3ezLNqEbP/XD9NgExeDZ2pw++9rbhaxnNsKtiw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "4.12.0": {"name": "express", "version": "4.12.0", "dependencies": {"qs": "2.3.3", "depd": "~1.0.0", "etag": "~1.5.1", "send": "0.12.1", "vary": "~1.0.0", "debug": "~2.1.1", "fresh": "0.2.4", "cookie": "0.1.2", "accepts": "~1.2.4", "methods": "~1.1.1", "type-is": "~1.6.0", "parseurl": "~1.3.0", "proxy-addr": "~1.0.6", "escape-html": "1.0.1", "on-finished": "~2.2.0", "utils-merge": "1.0.0", "content-type": "~1.0.1", "finalhandler": "0.3.3", "range-parser": "~1.0.2", "serve-static": "~1.9.1", "path-to-regexp": "0.1.3", "cookie-signature": "1.0.6", "merge-descriptors": "0.0.2", "content-disposition": "0.5.0"}, "devDependencies": {"ejs": "2.3.1", "hjs": "~0.0.6", "jade": "~1.9.2", "after": "0.8.1", "mocha": "~2.1.0", "vhost": "~3.0.0", "marked": "0.3.3", "morgan": "~1.5.1", "should": "~5.0.1", "istanbul": "0.3.6", "supertest": "~0.15.0", "multiparty": "~4.1.1", "body-parser": "~1.12.0", "connect-redis": "~2.2.0", "cookie-parser": "~1.3.4", "express-session": "~1.10.3", "method-override": "~2.3.1"}, "dist": {"shasum": "739660fce86acbc11ba9c37dc96ff009dc9975e8", "tarball": "https://registry.npmjs.org/express/-/express-4.12.0.tgz", "integrity": "sha512-o1DGBW/H1e6BUWZsLX1H7u2GDW79Icwx+Fz0OTDpuis5vbzGik1MNEbO8ncTwStHEPWzadNXRk+zst2tACj5RQ==", "signatures": [{"sig": "MEUCIAPe61hKyCzfjSlbCkQzDy2aREIXYOwFQRVfXx5ozxFeAiEAgx28qWpATyuHvIMx+kyCG0Yc5IU3yL0+cYu3TrH55u8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "3.20.1": {"name": "express", "version": "3.20.1", "dependencies": {"depd": "~1.0.0", "etag": "~1.5.1", "send": "0.12.1", "vary": "~1.0.0", "debug": "~2.1.1", "fresh": "0.2.4", "cookie": "0.1.2", "mkdirp": "0.5.0", "connect": "2.29.0", "methods": "~1.1.1", "parseurl": "~1.3.0", "commander": "2.6.0", "basic-auth": "1.0.0", "proxy-addr": "~1.0.6", "escape-html": "1.0.1", "utils-merge": "1.0.0", "content-type": "~1.0.1", "range-parser": "~1.0.2", "cookie-signature": "1.0.6", "merge-descriptors": "0.0.2", "content-disposition": "0.5.0"}, "devDependencies": {"ejs": "2.3.1", "mocha": "~2.1.0", "marked": "0.3.3", "should": "~5.0.0", "istanbul": "0.3.6", "supertest": "~0.15.0", "connect-redis": "~1.5.0"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "982701ba766a67a8bcc6f6d92366a1d0794e2c55", "tarball": "https://registry.npmjs.org/express/-/express-3.20.1.tgz", "integrity": "sha512-NmXr4OqROm1N2IdnbQctXkwaaGr/mLexd9BfP4lNlcgVUG1bysEab9cETM0uOCaDD5WSWQyKVSigCvgOPoB+Hw==", "signatures": [{"sig": "MEUCIBFaIuJaPdwal+MHa/L/A1EePSlf1xuR2ikxiBhJCEXzAiEAoRFIvrLaYCjhhfDi97EgZwZG8U9L+rMxIz5WxuuXNkA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "4.12.1": {"name": "express", "version": "4.12.1", "dependencies": {"qs": "2.3.3", "depd": "~1.0.0", "etag": "~1.5.1", "send": "0.12.1", "vary": "~1.0.0", "debug": "~2.1.1", "fresh": "0.2.4", "cookie": "0.1.2", "accepts": "~1.2.4", "methods": "~1.1.1", "type-is": "~1.6.0", "parseurl": "~1.3.0", "proxy-addr": "~1.0.6", "escape-html": "1.0.1", "on-finished": "~2.2.0", "utils-merge": "1.0.0", "content-type": "~1.0.1", "finalhandler": "0.3.3", "range-parser": "~1.0.2", "serve-static": "~1.9.1", "path-to-regexp": "0.1.3", "cookie-signature": "1.0.6", "merge-descriptors": "1.0.0", "content-disposition": "0.5.0"}, "devDependencies": {"ejs": "2.3.1", "jade": "~1.9.2", "after": "0.8.1", "mocha": "~2.1.0", "vhost": "~3.0.0", "marked": "0.3.3", "morgan": "~1.5.1", "should": "~5.0.1", "istanbul": "0.3.6", "supertest": "~0.15.0", "multiparty": "~4.1.1", "body-parser": "~1.12.0", "connect-redis": "~2.2.0", "cookie-parser": "~1.3.4", "express-session": "~1.10.3", "method-override": "~2.3.1"}, "dist": {"shasum": "bb784ce513d39f2b283fa2736303f89ba7951aeb", "tarball": "https://registry.npmjs.org/express/-/express-4.12.1.tgz", "integrity": "sha512-fmKTKIHL/n0DbYAkjhRDQmmMox4Qx9ZdO9AUtxRuHmTIlIMkR5H99KoMQb4Q/SyRpb9uxA+PSd6cTs4qHTuQ2A==", "signatures": [{"sig": "MEUCIHLlvVBCZMrbRiYSPMkBiLhcZyr3NM7HwdzPn4n4erE+AiEAp41w17RMj1FhZc/QclpnEcFYYrk3xceXeJPxke6u7lE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "4.12.2": {"name": "express", "version": "4.12.2", "dependencies": {"qs": "2.3.3", "depd": "~1.0.0", "etag": "~1.5.1", "send": "0.12.1", "vary": "~1.0.0", "debug": "~2.1.1", "fresh": "0.2.4", "cookie": "0.1.2", "accepts": "~1.2.4", "methods": "~1.1.1", "type-is": "~1.6.0", "parseurl": "~1.3.0", "proxy-addr": "~1.0.6", "escape-html": "1.0.1", "on-finished": "~2.2.0", "utils-merge": "1.0.0", "content-type": "~1.0.1", "finalhandler": "0.3.3", "range-parser": "~1.0.2", "serve-static": "~1.9.1", "path-to-regexp": "0.1.3", "cookie-signature": "1.0.6", "merge-descriptors": "1.0.0", "content-disposition": "0.5.0"}, "devDependencies": {"ejs": "2.3.1", "jade": "~1.9.2", "after": "0.8.1", "mocha": "~2.1.0", "vhost": "~3.0.0", "marked": "0.3.3", "morgan": "~1.5.1", "should": "~5.0.1", "istanbul": "0.3.6", "supertest": "~0.15.0", "multiparty": "~4.1.1", "body-parser": "~1.12.0", "connect-redis": "~2.2.0", "cookie-parser": "~1.3.4", "express-session": "~1.10.3", "method-override": "~2.3.1"}, "dist": {"shasum": "7e72ad4c1b4edf07536a6d1e2acec0161d8564bd", "tarball": "https://registry.npmjs.org/express/-/express-4.12.2.tgz", "integrity": "sha512-5wliOBEBG2ImErf0faQSXjLeXtkca+lWCjdoJofCwEgb/rr37njGx/u7oBn/9c2uwAesMwX0cY7mjkq+VpD41A==", "signatures": [{"sig": "MEQCID5dag+s51RvoWD8kQgvpLFG0FIJeWvYcJ2tZFJmxwurAiAcy9Ygeh9PdsvIioWOM3lt+f5NT+l5yJIlDQ1heBCaNQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "3.20.2": {"name": "express", "version": "3.20.2", "dependencies": {"depd": "~1.0.0", "etag": "~1.5.1", "send": "0.12.2", "vary": "~1.0.0", "debug": "~2.1.3", "fresh": "0.2.4", "cookie": "0.1.2", "mkdirp": "0.5.0", "connect": "2.29.1", "methods": "~1.1.1", "parseurl": "~1.3.0", "commander": "2.6.0", "basic-auth": "1.0.0", "proxy-addr": "~1.0.7", "escape-html": "1.0.1", "utils-merge": "1.0.0", "content-type": "~1.0.1", "range-parser": "~1.0.2", "cookie-signature": "1.0.6", "merge-descriptors": "1.0.0", "content-disposition": "0.5.0"}, "devDependencies": {"ejs": "2.3.1", "mocha": "~2.2.1", "marked": "0.3.3", "should": "~5.2.0", "istanbul": "0.3.8", "supertest": "~0.15.0", "connect-redis": "~1.5.0"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "c604027746e60f3da0a4b43063375d21c3235858", "tarball": "https://registry.npmjs.org/express/-/express-3.20.2.tgz", "integrity": "sha512-nIsSf1Igx409gWImJ3ymrhcFDiN3O3YTtz6NvnpOauwqxlmkVogKvs5VOH549np+Z3PdyIG13JblgYJmHyJ6BA==", "signatures": [{"sig": "MEUCIQDBoLjYmLgz1CI5N+eFqN+aa5ViadJ6hGxhdmDs8SdlPQIgVhLDHOGBU1MJ/zByRk1anKnzcOwXnLcPyKcdOeEm0gY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "4.12.3": {"name": "express", "version": "4.12.3", "dependencies": {"qs": "2.4.1", "depd": "~1.0.0", "etag": "~1.5.1", "send": "0.12.2", "vary": "~1.0.0", "debug": "~2.1.3", "fresh": "0.2.4", "cookie": "0.1.2", "accepts": "~1.2.5", "methods": "~1.1.1", "type-is": "~1.6.1", "parseurl": "~1.3.0", "proxy-addr": "~1.0.7", "escape-html": "1.0.1", "on-finished": "~2.2.0", "utils-merge": "1.0.0", "content-type": "~1.0.1", "finalhandler": "0.3.4", "range-parser": "~1.0.2", "serve-static": "~1.9.2", "path-to-regexp": "0.1.3", "cookie-signature": "1.0.6", "merge-descriptors": "1.0.0", "content-disposition": "0.5.0"}, "devDependencies": {"ejs": "2.3.1", "jade": "~1.9.2", "after": "0.8.1", "mocha": "~2.2.1", "vhost": "~3.0.0", "marked": "0.3.3", "morgan": "~1.5.2", "should": "~5.2.0", "istanbul": "0.3.8", "supertest": "~0.15.0", "multiparty": "~4.1.1", "body-parser": "~1.12.2", "connect-redis": "~2.2.0", "cookie-parser": "~1.3.4", "cookie-session": "~1.1.0", "express-session": "~1.10.4", "method-override": "~2.3.2"}, "dist": {"shasum": "6b9d94aec5ae03270d86d390c277a8c5a5ad0ee2", "tarball": "https://registry.npmjs.org/express/-/express-4.12.3.tgz", "integrity": "sha512-GySu5pAQ23SCika55I28O1PaklPIrWMEOvqBUl/Ck0x0H2ekWdYMuy5jW7QZzoomXvtK0yTYzWsPbEhnW0Wsgw==", "signatures": [{"sig": "MEUCIAOq1IVb8VeOAtgvw1rH8L3O1NW5XaXZGBBuS7XY5hvMAiEAsDPTGizOuYhMW2YaPhFyUvDG+Wv0G2CmyBYlvvN95nQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "3.20.3": {"name": "express", "version": "3.20.3", "dependencies": {"depd": "~1.0.1", "etag": "~1.5.1", "send": "0.12.3", "vary": "~1.0.0", "debug": "~2.2.0", "fresh": "0.2.4", "cookie": "0.1.2", "mkdirp": "0.5.0", "connect": "2.29.2", "methods": "~1.1.1", "parseurl": "~1.3.0", "commander": "2.6.0", "basic-auth": "1.0.0", "proxy-addr": "~1.0.8", "escape-html": "1.0.1", "utils-merge": "1.0.0", "content-type": "~1.0.1", "range-parser": "~1.0.2", "cookie-signature": "1.0.6", "merge-descriptors": "1.0.0", "content-disposition": "0.5.0"}, "devDependencies": {"ejs": "2.3.1", "mocha": "2.2.5", "marked": "0.3.3", "should": "6.0.1", "istanbul": "0.3.9", "supertest": "1.0.1", "connect-redis": "~1.5.0"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "5085ab3f5ff761cf7e1597e9b9df156f1094aded", "tarball": "https://registry.npmjs.org/express/-/express-3.20.3.tgz", "integrity": "sha512-PD3NiR/0T0Wjsjx72lFYVjR3bdQ+8Jh+68CFPRa8QnlGM84IWB45UZMLwpHVzktu4YIN1qdjI0Ur/Fm8V3h8Yg==", "signatures": [{"sig": "MEYCIQCw7uL4CksbCYaq48QieMdu5PdfQ01qIKvNVa9Z8AshzwIhAKzKJnsqStI2JdYmZ+tRYY4j9bSYE+U5mg3DRHAGkIJq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "4.12.4": {"name": "express", "version": "4.12.4", "dependencies": {"qs": "2.4.2", "depd": "~1.0.1", "etag": "~1.6.0", "send": "0.12.3", "vary": "~1.0.0", "debug": "~2.2.0", "fresh": "0.2.4", "cookie": "0.1.2", "accepts": "~1.2.7", "methods": "~1.1.1", "type-is": "~1.6.2", "parseurl": "~1.3.0", "proxy-addr": "~1.0.8", "escape-html": "1.0.1", "on-finished": "~2.2.1", "utils-merge": "1.0.0", "content-type": "~1.0.1", "finalhandler": "0.3.6", "range-parser": "~1.0.2", "serve-static": "~1.9.3", "path-to-regexp": "0.1.3", "cookie-signature": "1.0.6", "merge-descriptors": "1.0.0", "content-disposition": "0.5.0"}, "devDependencies": {"ejs": "2.3.1", "jade": "~1.9.2", "after": "0.8.1", "mocha": "2.2.5", "vhost": "~3.0.0", "marked": "0.3.3", "morgan": "~1.5.3", "should": "6.0.1", "istanbul": "0.3.9", "supertest": "1.0.1", "multiparty": "~4.1.2", "body-parser": "~1.12.4", "connect-redis": "~2.3.0", "cookie-parser": "~1.3.4", "cookie-session": "~1.1.0", "express-session": "~1.11.2", "method-override": "~2.3.3"}, "dist": {"shasum": "8fec2510255bc6b2e58107c48239c0fa307c1aa2", "tarball": "https://registry.npmjs.org/express/-/express-4.12.4.tgz", "integrity": "sha512-pbZznlqu9soBZPkF5SoG/zll+IfRZqAXvFzQO/fIIHD36VUpkRafbQsiKtMm3uMQ9v5cGg3+n7gZyaPOdzIVYg==", "signatures": [{"sig": "MEUCIA9i5YXSxGh+nDDY2dtBmQaeLIwg54AcAiap3mrJDvJxAiEA9MIv59aAr9Nc7LjEmghPSAl/4RD8TfF3jtrhiCO5jkU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "3.21.0": {"name": "express", "version": "3.21.0", "dependencies": {"depd": "~1.0.1", "etag": "~1.7.0", "send": "0.13.0", "vary": "~1.0.0", "debug": "~2.2.0", "fresh": "0.3.0", "cookie": "0.1.3", "mkdirp": "0.5.1", "connect": "2.30.0", "methods": "~1.1.1", "parseurl": "~1.3.0", "commander": "2.6.0", "basic-auth": "1.0.2", "proxy-addr": "~1.0.8", "escape-html": "1.0.2", "utils-merge": "1.0.0", "content-type": "~1.0.1", "range-parser": "~1.0.2", "cookie-signature": "1.0.6", "merge-descriptors": "1.0.0", "content-disposition": "0.5.0"}, "devDependencies": {"ejs": "2.3.1", "mocha": "2.2.5", "marked": "0.3.3", "should": "7.0.1", "istanbul": "0.3.9", "supertest": "1.0.1", "connect-redis": "~1.5.0"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "8ff7c424a92d15ee1a27c4bc8425ddba2c14aa38", "tarball": "https://registry.npmjs.org/express/-/express-3.21.0.tgz", "integrity": "sha512-ZNh5hwryzyT3AnvKntWqeHEIphFWBZAFMQsaNGxU7/+nJBwBdmk9q3JtuOHRW8zAKyXe7HGj+umabpHzukPfWA==", "signatures": [{"sig": "MEYCIQDRLIEykTdq4TxpC/+Dtal9AslTfYOILcQ9lWcF6pGrlAIhALfjp2iEcTufhmxSRncLHlRpKV/LnYOAcHF/7zBS/fvh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "4.13.0": {"name": "express", "version": "4.13.0", "dependencies": {"qs": "2.4.2", "depd": "~1.0.1", "etag": "~1.7.0", "send": "0.13.0", "vary": "~1.0.0", "debug": "~2.2.0", "fresh": "0.3.0", "cookie": "0.1.3", "accepts": "~1.2.9", "methods": "~1.1.1", "type-is": "~1.6.3", "parseurl": "~1.3.0", "proxy-addr": "~1.0.8", "escape-html": "1.0.2", "on-finished": "~2.3.0", "utils-merge": "1.0.0", "content-type": "~1.0.1", "finalhandler": "0.4.0", "range-parser": "~1.0.2", "serve-static": "~1.10.0", "array-flatten": "1.1.0", "path-to-regexp": "0.1.6", "cookie-signature": "1.0.6", "merge-descriptors": "1.0.0", "content-disposition": "0.5.0"}, "devDependencies": {"ejs": "2.3.1", "jade": "~1.11.0", "after": "0.8.1", "mocha": "2.2.5", "vhost": "~3.0.0", "marked": "0.3.3", "morgan": "~1.6.0", "should": "7.0.1", "istanbul": "0.3.9", "supertest": "1.0.1", "multiparty": "~4.1.2", "body-parser": "~1.13.1", "connect-redis": "~2.3.0", "cookie-parser": "~1.3.5", "cookie-session": "~1.1.0", "express-session": "~1.11.3", "method-override": "~2.3.3"}, "dist": {"shasum": "0678bdbc72715170b3fcc917052f046cb9689add", "tarball": "https://registry.npmjs.org/express/-/express-4.13.0.tgz", "integrity": "sha512-BKwIL+hoQxmKd/rspOscPBb8Gt2YsTdwPlJ/3wA6KpCBQiaTrUbDbqGw26eCetsLRFl8IeRDNmaYKsBQa1OXWQ==", "signatures": [{"sig": "MEUCIDid1RpGV8pLIt3mi/ud74p3M8yb8ltecVnszQOJbPY/AiEAoJPqoi0Q1m83izpDqnoYyt7/N8CoIGJom9LZCVpvJT0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "3.21.1": {"name": "express", "version": "3.21.1", "dependencies": {"depd": "~1.0.1", "etag": "~1.7.0", "send": "0.13.0", "vary": "~1.0.0", "debug": "~2.2.0", "fresh": "0.3.0", "cookie": "0.1.3", "mkdirp": "0.5.1", "connect": "2.30.1", "methods": "~1.1.1", "parseurl": "~1.3.0", "commander": "2.6.0", "basic-auth": "~1.0.3", "proxy-addr": "~1.0.8", "escape-html": "1.0.2", "utils-merge": "1.0.0", "content-type": "~1.0.1", "range-parser": "~1.0.2", "cookie-signature": "1.0.6", "merge-descriptors": "1.0.0", "content-disposition": "0.5.0"}, "devDependencies": {"ejs": "2.3.2", "mocha": "2.2.5", "marked": "0.3.3", "should": "7.0.1", "istanbul": "0.3.9", "supertest": "1.0.1", "connect-redis": "~1.5.0"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "427b1f4e68dcfd5da6809892fe19219d52ce6b55", "tarball": "https://registry.npmjs.org/express/-/express-3.21.1.tgz", "integrity": "sha512-hFg0GiY70dVrBmOjGnbUizWnSMnfdCZ1dit0ARCdIGq67aCz1eJAplFdXiwGUHKa3ThFzOYDxBvmTvtJ460bgg==", "signatures": [{"sig": "MEUCIQDXf6R+ODZPMjZJbUMFShpxDT+AYO+e4v0Kh7y6RnoyhQIgbusjfGUDLa63lon3gQ+HLLlBPznAzVOfL/Qcj+mOc7A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "4.13.1": {"name": "express", "version": "4.13.1", "dependencies": {"qs": "4.0.0", "depd": "~1.0.1", "etag": "~1.7.0", "send": "0.13.0", "vary": "~1.0.0", "debug": "~2.2.0", "fresh": "0.3.0", "cookie": "0.1.3", "accepts": "~1.2.10", "methods": "~1.1.1", "type-is": "~1.6.4", "parseurl": "~1.3.0", "proxy-addr": "~1.0.8", "escape-html": "1.0.2", "on-finished": "~2.3.0", "utils-merge": "1.0.0", "content-type": "~1.0.1", "finalhandler": "0.4.0", "range-parser": "~1.0.2", "serve-static": "~1.10.0", "array-flatten": "1.1.0", "path-to-regexp": "0.1.6", "cookie-signature": "1.0.6", "merge-descriptors": "1.0.0", "content-disposition": "0.5.0"}, "devDependencies": {"ejs": "2.3.2", "jade": "~1.11.0", "after": "0.8.1", "mocha": "2.2.5", "vhost": "~3.0.0", "marked": "0.3.3", "morgan": "~1.6.1", "should": "7.0.1", "istanbul": "0.3.9", "supertest": "1.0.1", "multiparty": "~4.1.2", "body-parser": "~1.13.2", "connect-redis": "~2.3.0", "cookie-parser": "~1.3.5", "cookie-session": "~1.2.0", "express-session": "~1.11.3", "method-override": "~2.3.3"}, "dist": {"shasum": "f117aa1d1f6bedbc8de5b6d71fc31a5acd0f63df", "tarball": "https://registry.npmjs.org/express/-/express-4.13.1.tgz", "integrity": "sha512-n+IFE1PjNsA6fFt110NLUDVv4S0uLK2mXf9YYBQAj5XFTJ0eLdE+oDXOdu8YASWR88Cy/EawdKH6Sa33hGXsZw==", "signatures": [{"sig": "MEQCIBPK5ccQuQLVQeatTjN9OTdjIRH2GYyrM8KW0xPKMgzrAiBaa90h30axL7nOw5iC7e36yB4lnDBWM6wRiVAicyWdkg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "5.0.0-alpha.2": {"name": "express", "version": "5.0.0-alpha.2", "dependencies": {"qs": "4.0.0", "depd": "~1.0.1", "etag": "~1.7.0", "send": "0.13.0", "vary": "~1.0.0", "debug": "~2.2.0", "fresh": "0.3.0", "cookie": "0.1.3", "router": "~1.1.2", "accepts": "~1.2.10", "methods": "~1.1.1", "type-is": "~1.6.4", "parseurl": "~1.3.0", "proxy-addr": "~1.0.8", "escape-html": "1.0.2", "on-finished": "~2.3.0", "utils-merge": "1.0.0", "content-type": "~1.0.1", "finalhandler": "0.4.0", "range-parser": "~1.0.2", "serve-static": "~1.10.0", "array-flatten": "1.1.0", "path-to-regexp": "0.1.6", "cookie-signature": "1.0.6", "path-is-absolute": "1.0.0", "merge-descriptors": "1.0.0", "content-disposition": "0.5.0"}, "devDependencies": {"ejs": "2.3.2", "jade": "~1.11.0", "after": "0.8.1", "mocha": "2.2.5", "vhost": "~3.0.0", "marked": "0.3.3", "morgan": "~1.6.1", "should": "7.0.1", "istanbul": "0.3.9", "supertest": "1.0.1", "multiparty": "~4.1.2", "body-parser": "~1.13.2", "connect-redis": "~2.3.0", "cookie-parser": "~1.3.5", "cookie-session": "~1.2.0", "express-session": "~1.11.3", "method-override": "~2.3.3"}, "dist": {"shasum": "fd54177f657b6a4c4540727702edd1cbaa3a6ac5", "tarball": "https://registry.npmjs.org/express/-/express-5.0.0-alpha.2.tgz", "integrity": "sha512-dCv747cpHLEUBzHm5MR8y6eq43izDxmfWtpq1oaTY3vlW3nlLugPob1rzg7/mul/a7o/Svg6SNx/oc/3fz61mA==", "signatures": [{"sig": "MEUCIDLqdUoDlY1Am331SC0MCPYDHMFPwVhyLwaYG33HWVlIAiEA77d79cV00P+DVKLmKUK2bSABjXCKmzvh4NB9DjCtCh4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "3.21.2": {"name": "express", "version": "3.21.2", "dependencies": {"depd": "~1.0.1", "etag": "~1.7.0", "send": "0.13.0", "vary": "~1.0.1", "debug": "~2.2.0", "fresh": "0.3.0", "cookie": "0.1.3", "mkdirp": "0.5.1", "connect": "2.30.2", "methods": "~1.1.1", "parseurl": "~1.3.0", "commander": "2.6.0", "basic-auth": "~1.0.3", "proxy-addr": "~1.0.8", "escape-html": "1.0.2", "utils-merge": "1.0.0", "content-type": "~1.0.1", "range-parser": "~1.0.2", "cookie-signature": "1.0.6", "merge-descriptors": "1.0.0", "content-disposition": "0.5.0"}, "devDependencies": {"ejs": "2.3.3", "mocha": "2.2.5", "marked": "0.3.5", "should": "7.0.2", "istanbul": "0.3.9", "supertest": "1.0.1", "connect-redis": "~1.5.0"}, "bin": {"express": "./bin/express"}, "dist": {"shasum": "0c2903ee5c54e63d65a96170764703550665a3de", "tarball": "https://registry.npmjs.org/express/-/express-3.21.2.tgz", "integrity": "sha512-r3mq2RNCDxAdmZrzEAdjlk5/W7x8+vjU1aAcoAoZFq62KtkWQX+MbaSN4g59CwdUFf9MFf1VSqkZJ+LeR9jmww==", "signatures": [{"sig": "MEUCIQCunWiseqob+u+OP/mOWM52gu79j+EAHAjZVTpzAbVDOgIgF/xBfcFGu33tQ8m83WOCJPGv4RnxT1tL+byghI7wZIM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "4.13.2": {"name": "express", "version": "4.13.2", "dependencies": {"qs": "4.0.0", "depd": "~1.0.1", "etag": "~1.7.0", "send": "0.13.0", "vary": "~1.0.1", "debug": "~2.2.0", "fresh": "0.3.0", "cookie": "0.1.3", "accepts": "~1.2.12", "methods": "~1.1.1", "type-is": "~1.6.6", "parseurl": "~1.3.0", "proxy-addr": "~1.0.8", "escape-html": "1.0.2", "on-finished": "~2.3.0", "utils-merge": "1.0.0", "content-type": "~1.0.1", "finalhandler": "0.4.0", "range-parser": "~1.0.2", "serve-static": "~1.10.0", "array-flatten": "1.1.1", "path-to-regexp": "0.1.7", "cookie-signature": "1.0.6", "merge-descriptors": "1.0.0", "content-disposition": "0.5.0"}, "devDependencies": {"ejs": "2.3.3", "jade": "~1.11.0", "after": "0.8.1", "mocha": "2.2.5", "vhost": "~3.0.1", "marked": "0.3.5", "morgan": "~1.6.1", "should": "7.0.2", "istanbul": "0.3.17", "supertest": "1.0.1", "multiparty": "~4.1.2", "body-parser": "~1.13.3", "connect-redis": "~2.4.1", "cookie-parser": "~1.3.5", "cookie-session": "~1.2.0", "express-session": "~1.11.3", "method-override": "~2.3.5"}, "dist": {"shasum": "e4259f58d8ca85f54b820d7057b02ef90b471f1d", "tarball": "https://registry.npmjs.org/express/-/express-4.13.2.tgz", "integrity": "sha512-p00t6rHJbOSwmTkwGnXRJsaFW+0dAN1U5bCbJBKaM3DP2ECbrPnc9Tg09DazCPDywvSs2X/4f3yhzybqdOB+Mw==", "signatures": [{"sig": "MEUCIQDSu74z0DCUrhdLa2gtQwF+t14kF9LnQ3VGCC9/TsBkuQIgGBWK71b1c72/+3k4zFcBrhqM0l8OMa9W/Y6leXQjLcA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "4.13.3": {"name": "express", "version": "4.13.3", "dependencies": {"qs": "4.0.0", "depd": "~1.0.1", "etag": "~1.7.0", "send": "0.13.0", "vary": "~1.0.1", "debug": "~2.2.0", "fresh": "0.3.0", "cookie": "0.1.3", "accepts": "~1.2.12", "methods": "~1.1.1", "type-is": "~1.6.6", "parseurl": "~1.3.0", "proxy-addr": "~1.0.8", "escape-html": "1.0.2", "on-finished": "~2.3.0", "utils-merge": "1.0.0", "content-type": "~1.0.1", "finalhandler": "0.4.0", "range-parser": "~1.0.2", "serve-static": "~1.10.0", "array-flatten": "1.1.1", "path-to-regexp": "0.1.7", "cookie-signature": "1.0.6", "merge-descriptors": "1.0.0", "content-disposition": "0.5.0"}, "devDependencies": {"ejs": "2.3.3", "jade": "~1.11.0", "after": "0.8.1", "mocha": "2.2.5", "vhost": "~3.0.1", "marked": "0.3.5", "morgan": "~1.6.1", "should": "7.0.2", "istanbul": "0.3.17", "supertest": "1.0.1", "multiparty": "~4.1.2", "body-parser": "~1.13.3", "connect-redis": "~2.4.1", "cookie-parser": "~1.3.5", "cookie-session": "~1.2.0", "express-session": "~1.11.3", "method-override": "~2.3.5"}, "dist": {"shasum": "ddb2f1fb4502bf33598d2b032b037960ca6c80a3", "tarball": "https://registry.npmjs.org/express/-/express-4.13.3.tgz", "integrity": "sha512-6+EQT2fOxWOhT0c5rxbeRfe6Q0bPlHh10ko0BbvgoCBwzDfLgNZ5admFoB8Wqwc9zJY4eF1zo6+pZpfWgQPkEg==", "signatures": [{"sig": "MEYCIQCgDj5f54YE0jythgU22pyH0Oturb86amqJzKrwVFrk9AIhAJ5D8uOZsl+EBhYXls05ZLt4zFVda5yRN2shcJhyaSPW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "4.13.4": {"name": "express", "version": "4.13.4", "dependencies": {"qs": "4.0.0", "depd": "~1.1.0", "etag": "~1.7.0", "send": "0.13.1", "vary": "~1.0.1", "debug": "~2.2.0", "fresh": "0.3.0", "cookie": "0.1.5", "accepts": "~1.2.12", "methods": "~1.1.2", "type-is": "~1.6.6", "parseurl": "~1.3.1", "proxy-addr": "~1.0.10", "escape-html": "~1.0.3", "on-finished": "~2.3.0", "utils-merge": "1.0.0", "content-type": "~1.0.1", "finalhandler": "0.4.1", "range-parser": "~1.0.3", "serve-static": "~1.10.2", "array-flatten": "1.1.1", "path-to-regexp": "0.1.7", "cookie-signature": "1.0.6", "merge-descriptors": "1.0.1", "content-disposition": "0.5.1"}, "devDependencies": {"ejs": "2.3.4", "jade": "~1.11.0", "after": "0.8.1", "mocha": "2.3.4", "vhost": "~3.0.1", "marked": "0.3.5", "morgan": "~1.6.1", "should": "7.1.1", "istanbul": "0.4.2", "supertest": "1.1.0", "multiparty": "~4.1.2", "body-parser": "~1.14.2", "connect-redis": "~2.4.1", "cookie-parser": "~1.4.1", "cookie-session": "~1.2.0", "express-session": "~1.13.0", "method-override": "~2.3.5"}, "dist": {"shasum": "3c0b76f3c77590c8345739061ec0bd3ba067ec24", "tarball": "https://registry.npmjs.org/express/-/express-4.13.4.tgz", "integrity": "sha512-pNykF2h4VgBMH8gJfwwBh4kqaIyV/DcFIX6UpC751GF7du2kA1pkxJ2/SmggVGbYCa4mBRcWh0yiTfK8Dp/Rdg==", "signatures": [{"sig": "MEYCIQCcEfDZvv5V8CRtSb3qFZAiILxDgtIYdh7efAi4g5R94wIhANyOrzaYWjouQ/LKbQVlq6I2NpLnRokkUK6RIOZNuRlH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "4.14.0": {"name": "express", "version": "4.14.0", "dependencies": {"qs": "6.2.0", "depd": "~1.1.0", "etag": "~1.7.0", "send": "0.14.1", "vary": "~1.1.0", "debug": "~2.2.0", "fresh": "0.3.0", "cookie": "0.3.1", "accepts": "~1.3.3", "methods": "~1.1.2", "type-is": "~1.6.13", "parseurl": "~1.3.1", "encodeurl": "~1.0.1", "proxy-addr": "~1.1.2", "escape-html": "~1.0.3", "on-finished": "~2.3.0", "utils-merge": "1.0.0", "content-type": "~1.0.2", "finalhandler": "0.5.0", "range-parser": "~1.2.0", "serve-static": "~1.11.1", "array-flatten": "1.1.1", "path-to-regexp": "0.1.7", "cookie-signature": "1.0.6", "merge-descriptors": "1.0.1", "content-disposition": "0.5.1"}, "devDependencies": {"ejs": "2.4.2", "jade": "~1.11.0", "after": "0.8.1", "mocha": "2.5.3", "vhost": "~3.0.2", "marked": "0.3.5", "morgan": "~1.7.0", "should": "9.0.2", "istanbul": "0.4.3", "supertest": "1.2.0", "multiparty": "~4.1.2", "body-parser": "~1.15.1", "connect-redis": "~2.4.1", "cookie-parser": "~1.4.3", "cookie-session": "~1.2.0", "express-session": "~1.13.0", "method-override": "~2.3.6"}, "dist": {"shasum": "c1ee3f42cdc891fb3dc650a8922d51ec847d0d66", "tarball": "https://registry.npmjs.org/express/-/express-4.14.0.tgz", "integrity": "sha512-DMNT04ECPAVNiEZqRtl2VMxg4TMxL0+Qv2VrQcsO+vaOSkeHJSCSmEfxrcJ30ikZx5l8VdPAdhhrVPw0wZFZ2Q==", "signatures": [{"sig": "MEYCIQCGWzIB0sWd1EoAXpV+e/mPcC5ypa+lttZpeaLGwWTdFgIhAKKYtaqKASY9E2MQN4ORhSY11xwKu9Ztv7nN7gxiwMt7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "4.14.1": {"name": "express", "version": "4.14.1", "dependencies": {"qs": "6.2.0", "depd": "~1.1.0", "etag": "~1.7.0", "send": "0.14.2", "vary": "~1.1.0", "debug": "~2.2.0", "fresh": "0.3.0", "cookie": "0.3.1", "accepts": "~1.3.3", "methods": "~1.1.2", "type-is": "~1.6.14", "parseurl": "~1.3.1", "encodeurl": "~1.0.1", "proxy-addr": "~1.1.3", "escape-html": "~1.0.3", "on-finished": "~2.3.0", "utils-merge": "1.0.0", "content-type": "~1.0.2", "finalhandler": "0.5.1", "range-parser": "~1.2.0", "serve-static": "~1.11.2", "array-flatten": "1.1.1", "path-to-regexp": "0.1.7", "cookie-signature": "1.0.6", "merge-descriptors": "1.0.1", "content-disposition": "0.5.2"}, "devDependencies": {"ejs": "2.5.5", "jade": "~1.11.0", "after": "0.8.2", "mocha": "3.2.0", "vhost": "~3.0.2", "marked": "0.3.6", "morgan": "~1.7.0", "should": "11.2.0", "istanbul": "0.4.5", "supertest": "1.2.0", "multiparty": "4.1.3", "body-parser": "1.16.0", "connect-redis": "~2.4.1", "cookie-parser": "~1.4.3", "cookie-session": "~1.2.0", "express-session": "1.15.0", "method-override": "~2.3.6"}, "dist": {"shasum": "646c237f766f148c2120aff073817b9e4d7e0d33", "tarball": "https://registry.npmjs.org/express/-/express-4.14.1.tgz", "integrity": "sha512-aL+R5JmMN85tiMiWNx3wge9k3hjqezh530Nbc36kJezDZO8hkK+LBaaFvv9wziPiigK6vCubJ+omjQyqt8keYw==", "signatures": [{"sig": "MEYCIQDW62HJU/W4Q030rtNlPjwka6WJQJXQP9JqiJKlZMz6pgIhAPPCm8EJb8otbryDqZ5C6x+nPTuE8b5BpggD4JiDKVBi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "5.0.0-alpha.3": {"name": "express", "version": "5.0.0-alpha.3", "dependencies": {"qs": "6.2.0", "depd": "~1.1.0", "etag": "~1.7.0", "send": "0.14.2", "vary": "~1.1.0", "debug": "~2.2.0", "fresh": "0.3.0", "cookie": "0.3.1", "router": "~1.1.5", "accepts": "~1.3.3", "methods": "~1.1.2", "type-is": "~1.6.14", "parseurl": "~1.3.1", "encodeurl": "~1.0.1", "proxy-addr": "~1.1.3", "escape-html": "~1.0.3", "on-finished": "~2.3.0", "utils-merge": "1.0.0", "content-type": "~1.0.2", "finalhandler": "0.5.1", "range-parser": "~1.2.0", "serve-static": "~1.11.2", "array-flatten": "2.1.1", "path-to-regexp": "0.1.7", "cookie-signature": "1.0.6", "path-is-absolute": "1.0.1", "merge-descriptors": "1.0.1", "content-disposition": "0.5.2"}, "devDependencies": {"ejs": "2.5.5", "jade": "~1.11.0", "after": "0.8.2", "mocha": "3.2.0", "vhost": "~3.0.2", "marked": "0.3.6", "morgan": "~1.7.0", "should": "11.2.0", "istanbul": "0.4.5", "supertest": "1.2.0", "multiparty": "4.1.3", "body-parser": "1.16.0", "connect-redis": "~2.4.1", "cookie-parser": "~1.4.3", "cookie-session": "~1.2.0", "express-session": "1.15.0", "method-override": "~2.3.6"}, "dist": {"shasum": "19d63b931bf0f64c42725952ef0602c381fe64db", "tarball": "https://registry.npmjs.org/express/-/express-5.0.0-alpha.3.tgz", "integrity": "sha512-b1tN07sH7zUkqZQJ7hyvyyePGcpmiy5KGgetvznCiw5MRzXXZMD6y6fbf+K9ylu57EfFgpdrEQvDeN1Jw9SFMw==", "signatures": [{"sig": "MEYCIQCM7kcMESi4dmS4jZysxSvoj+3R1gCaTpCJF+7Si5qf0wIhANSFPYXVMmmJVxKpn3UXJSch8KwXIVaIcf+rfQxpSm/H", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "4.15.0": {"name": "express", "version": "4.15.0", "dependencies": {"qs": "6.3.1", "depd": "~1.1.0", "etag": "~1.8.0", "send": "0.15.0", "vary": "~1.1.0", "debug": "2.6.1", "fresh": "0.5.0", "cookie": "0.3.1", "accepts": "~1.3.3", "methods": "~1.1.2", "type-is": "~1.6.14", "parseurl": "~1.3.1", "statuses": "~1.3.1", "encodeurl": "~1.0.1", "proxy-addr": "~1.1.3", "escape-html": "~1.0.3", "on-finished": "~2.3.0", "utils-merge": "1.0.0", "content-type": "~1.0.2", "finalhandler": "~1.0.0", "range-parser": "~1.2.0", "serve-static": "1.12.0", "array-flatten": "1.1.1", "path-to-regexp": "0.1.7", "setprototypeof": "1.0.3", "cookie-signature": "1.0.6", "merge-descriptors": "1.0.1", "content-disposition": "0.5.2"}, "devDependencies": {"ejs": "2.5.6", "jade": "~1.11.0", "after": "0.8.2", "mocha": "3.2.0", "vhost": "~3.0.2", "marked": "0.3.6", "morgan": "1.8.1", "should": "11.2.0", "istanbul": "0.4.5", "supertest": "1.2.0", "multiparty": "4.1.3", "body-parser": "1.17.0", "connect-redis": "~2.4.1", "cookie-parser": "~1.4.3", "cookie-session": "~1.2.0", "express-session": "1.15.1", "method-override": "2.3.7", "pbkdf2-password": "1.2.1"}, "dist": {"shasum": "8fb125829f70a04a59e1c40ceb8dea19cf5c879c", "tarball": "https://registry.npmjs.org/express/-/express-4.15.0.tgz", "integrity": "sha512-ZfKQUIOr+nPpy0nbfho9jK6dmp3S0Z7VRGuxJFYgPkJSU+n41ujsFl2UFV79y6JSFhkDJXibgqpWscNmu9hZeA==", "signatures": [{"sig": "MEQCIBzkfViQfgWU9+1Fkn+kMXWLpg7HwPLanPqXs5cOOiFeAiAkWFSBYGv4yNWIPRms1L6T5abSZHvx2m48y6hBw6JnTA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "5.0.0-alpha.4": {"name": "express", "version": "5.0.0-alpha.4", "dependencies": {"qs": "6.3.1", "depd": "~1.1.0", "etag": "~1.8.0", "send": "0.15.0", "vary": "~1.1.0", "debug": "2.6.1", "fresh": "0.5.0", "cookie": "0.3.1", "router": "~1.3.0", "accepts": "~1.3.3", "methods": "~1.1.2", "type-is": "~1.6.14", "parseurl": "~1.3.1", "statuses": "~1.3.1", "encodeurl": "~1.0.1", "proxy-addr": "~1.1.3", "escape-html": "~1.0.3", "on-finished": "~2.3.0", "utils-merge": "1.0.0", "content-type": "~1.0.2", "finalhandler": "~1.0.0", "range-parser": "~1.2.0", "serve-static": "1.12.0", "array-flatten": "2.1.1", "path-to-regexp": "0.1.7", "setprototypeof": "1.0.3", "cookie-signature": "1.0.6", "path-is-absolute": "1.0.1", "merge-descriptors": "1.0.1", "content-disposition": "0.5.2"}, "devDependencies": {"ejs": "2.5.6", "jade": "~1.11.0", "after": "0.8.2", "mocha": "3.2.0", "vhost": "~3.0.2", "marked": "0.3.6", "morgan": "1.8.1", "should": "11.2.0", "istanbul": "0.4.5", "supertest": "1.2.0", "multiparty": "4.1.3", "body-parser": "1.17.0", "connect-redis": "~2.4.1", "cookie-parser": "~1.4.3", "cookie-session": "~1.2.0", "express-session": "1.15.1", "method-override": "2.3.7", "pbkdf2-password": "1.2.1"}, "dist": {"shasum": "cd96a23fa9e3fce471f9637376b1c7b9d70b865e", "tarball": "https://registry.npmjs.org/express/-/express-5.0.0-alpha.4.tgz", "integrity": "sha512-tGHJ3aRnr7bC5vW7Qg+dxasNis3Mo90PGT5s0rsbqe9gju9dqKuyJ4zMeFRZZXiEY4it57FBM9sBYX2P5LnrqQ==", "signatures": [{"sig": "MEQCIB+wSFo1hP/vv0YQn/kMsL7tE9ARiD7wT4e9cJSK5TQVAiAhBCfgQfGNbBmL4tCwD3DX7WyM0i3J2BaH/9m8dE/KYg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "4.15.1": {"name": "express", "version": "4.15.1", "dependencies": {"qs": "6.3.1", "depd": "~1.1.0", "etag": "~1.8.0", "send": "0.15.1", "vary": "~1.1.0", "debug": "2.6.1", "fresh": "0.5.0", "cookie": "0.3.1", "accepts": "~1.3.3", "methods": "~1.1.2", "type-is": "~1.6.14", "parseurl": "~1.3.1", "statuses": "~1.3.1", "encodeurl": "~1.0.1", "proxy-addr": "~1.1.3", "escape-html": "~1.0.3", "on-finished": "~2.3.0", "utils-merge": "1.0.0", "content-type": "~1.0.2", "finalhandler": "~1.0.0", "range-parser": "~1.2.0", "serve-static": "1.12.1", "array-flatten": "1.1.1", "path-to-regexp": "0.1.7", "setprototypeof": "1.0.3", "cookie-signature": "1.0.6", "merge-descriptors": "1.0.1", "content-disposition": "0.5.2"}, "devDependencies": {"ejs": "2.5.6", "jade": "~1.11.0", "after": "0.8.2", "mocha": "3.2.0", "vhost": "~3.0.2", "marked": "0.3.6", "morgan": "1.8.1", "should": "11.2.0", "istanbul": "0.4.5", "supertest": "1.2.0", "multiparty": "4.1.3", "body-parser": "1.17.0", "connect-redis": "~2.4.1", "cookie-parser": "~1.4.3", "cookie-session": "~1.2.0", "express-session": "1.15.1", "method-override": "2.3.7", "pbkdf2-password": "1.2.1"}, "dist": {"shasum": "e32897816d94cc477e45f0149a8966bc938a329b", "tarball": "https://registry.npmjs.org/express/-/express-4.15.1.tgz", "integrity": "sha512-FjQ0nPP8Jh9FWZAPoj6cJcDE5bGv2WDIULOiRG/5dsp+i863CxjWkiAcAolhQtv3ybrdrxoZu2CmrObX7gJ3Xg==", "signatures": [{"sig": "MEYCIQCoYkg1lUBeT3AWYpVKkSsL/c6r791WP2VLOXPkKEq7/gIhAKVRaNhHFxGBYG/D/vaqGOzXcmce6Ss48UPCeHYxrKGq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "4.15.2": {"name": "express", "version": "4.15.2", "dependencies": {"qs": "6.4.0", "depd": "~1.1.0", "etag": "~1.8.0", "send": "0.15.1", "vary": "~1.1.0", "debug": "2.6.1", "fresh": "0.5.0", "cookie": "0.3.1", "accepts": "~1.3.3", "methods": "~1.1.2", "type-is": "~1.6.14", "parseurl": "~1.3.1", "statuses": "~1.3.1", "encodeurl": "~1.0.1", "proxy-addr": "~1.1.3", "escape-html": "~1.0.3", "on-finished": "~2.3.0", "utils-merge": "1.0.0", "content-type": "~1.0.2", "finalhandler": "~1.0.0", "range-parser": "~1.2.0", "serve-static": "1.12.1", "array-flatten": "1.1.1", "path-to-regexp": "0.1.7", "setprototypeof": "1.0.3", "cookie-signature": "1.0.6", "merge-descriptors": "1.0.1", "content-disposition": "0.5.2"}, "devDependencies": {"ejs": "2.5.6", "jade": "~1.11.0", "after": "0.8.2", "mocha": "3.2.0", "vhost": "~3.0.2", "marked": "0.3.6", "morgan": "1.8.1", "should": "11.2.0", "istanbul": "0.4.5", "supertest": "1.2.0", "multiparty": "4.1.3", "body-parser": "1.17.1", "connect-redis": "~2.4.1", "cookie-parser": "~1.4.3", "cookie-session": "~1.2.0", "express-session": "1.15.1", "method-override": "2.3.7", "pbkdf2-password": "1.2.1"}, "dist": {"shasum": "af107fc148504457f2dca9a6f2571d7129b97b35", "tarball": "https://registry.npmjs.org/express/-/express-4.15.2.tgz", "integrity": "sha512-zcWjYahMpHMWzQub5h04Tx7EY1Kmef0KXA2024pIpJAObNYU2rzY/J66otPCc0Y1Xn6Y/nosFq2S2AzIXAHGRw==", "signatures": [{"sig": "MEUCIB0LuhieKXqoopeloH1aGwruEFUTbTcYi2QTcOe9sE7eAiEAyOEswjppQkUlE8Hu4oAahoWVfF9CMjnr08TpxIsbaLA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "5.0.0-alpha.5": {"name": "express", "version": "5.0.0-alpha.5", "dependencies": {"qs": "6.4.0", "depd": "~1.1.0", "etag": "~1.8.0", "send": "0.15.1", "vary": "~1.1.0", "debug": "2.6.1", "fresh": "0.5.0", "cookie": "0.3.1", "router": "~1.3.0", "accepts": "~1.3.3", "methods": "~1.1.2", "type-is": "~1.6.14", "parseurl": "~1.3.1", "statuses": "~1.3.1", "encodeurl": "~1.0.1", "proxy-addr": "~1.1.3", "escape-html": "~1.0.3", "on-finished": "~2.3.0", "utils-merge": "1.0.0", "content-type": "~1.0.2", "finalhandler": "~1.0.0", "range-parser": "~1.2.0", "serve-static": "1.12.1", "array-flatten": "2.1.1", "path-to-regexp": "0.1.7", "setprototypeof": "1.0.3", "cookie-signature": "1.0.6", "path-is-absolute": "1.0.1", "merge-descriptors": "1.0.1", "content-disposition": "0.5.2"}, "devDependencies": {"ejs": "2.5.6", "jade": "~1.11.0", "after": "0.8.2", "mocha": "3.2.0", "vhost": "~3.0.2", "marked": "0.3.6", "morgan": "1.8.1", "should": "11.2.0", "istanbul": "0.4.5", "supertest": "1.2.0", "multiparty": "4.1.3", "body-parser": "1.17.1", "connect-redis": "~2.4.1", "cookie-parser": "~1.4.3", "cookie-session": "~1.2.0", "express-session": "1.15.1", "method-override": "2.3.7", "pbkdf2-password": "1.2.1"}, "dist": {"shasum": "e37423a8d82826fb915c7dd166e2900bfa3552e6", "tarball": "https://registry.npmjs.org/express/-/express-5.0.0-alpha.5.tgz", "integrity": "sha512-1bvJhzSMte2ToQ/gofxM272O1T7e6NkLEcG15HIojZGd2exF6vjnDHfNXtyizL7sW9aSRNzzROFrZGX356fGvA==", "signatures": [{"sig": "MEYCIQDRoTdAvxRgybdggef6KzoxYG22eRLKQwadwVulROfttQIhAI3yh3SnCvy7mCeS2d/zp4tyoMPLY3HkpHwVEV8S6RrJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "4.15.3": {"name": "express", "version": "4.15.3", "dependencies": {"qs": "6.4.0", "depd": "~1.1.0", "etag": "~1.8.0", "send": "0.15.3", "vary": "~1.1.1", "debug": "2.6.7", "fresh": "0.5.0", "cookie": "0.3.1", "accepts": "~1.3.3", "methods": "~1.1.2", "type-is": "~1.6.15", "parseurl": "~1.3.1", "statuses": "~1.3.1", "encodeurl": "~1.0.1", "proxy-addr": "~1.1.4", "escape-html": "~1.0.3", "on-finished": "~2.3.0", "utils-merge": "1.0.0", "content-type": "~1.0.2", "finalhandler": "~1.0.3", "range-parser": "~1.2.0", "serve-static": "1.12.3", "array-flatten": "1.1.1", "path-to-regexp": "0.1.7", "setprototypeof": "1.0.3", "cookie-signature": "1.0.6", "merge-descriptors": "1.0.1", "content-disposition": "0.5.2"}, "devDependencies": {"ejs": "2.5.6", "hbs": "4.0.1", "after": "0.8.2", "mocha": "3.4.1", "vhost": "~3.0.2", "marked": "0.3.6", "morgan": "1.8.1", "should": "11.2.1", "istanbul": "0.4.5", "supertest": "1.2.0", "multiparty": "4.1.3", "body-parser": "1.17.1", "connect-redis": "~2.4.1", "cookie-parser": "~1.4.3", "cookie-session": "~1.2.0", "express-session": "1.15.2", "method-override": "2.3.8", "pbkdf2-password": "1.2.1"}, "dist": {"shasum": "bab65d0f03aa80c358408972fc700f916944b662", "tarball": "https://registry.npmjs.org/express/-/express-4.15.3.tgz", "integrity": "sha512-eyQPXY6coAN6NpdZhXZsAvoGSvWZtYq1S6YCBA86+RTwzzecvLvgPR/Jm+MR0otFUzLnmI/lfv79aoIMDpWW8w==", "signatures": [{"sig": "MEUCIQCwdExdMScOJMrj/NCTtHTQVqlxqIhtIwMKDlSs9wOhFwIgZJnpGcdDg203WDE003OOIJOAu0itxT6TcNUEkUVbmlQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "4.15.4": {"name": "express", "version": "4.15.4", "dependencies": {"qs": "6.5.0", "depd": "~1.1.1", "etag": "~1.8.0", "send": "0.15.4", "vary": "~1.1.1", "debug": "2.6.8", "fresh": "0.5.0", "cookie": "0.3.1", "accepts": "~1.3.3", "methods": "~1.1.2", "type-is": "~1.6.15", "parseurl": "~1.3.1", "statuses": "~1.3.1", "encodeurl": "~1.0.1", "proxy-addr": "~1.1.5", "escape-html": "~1.0.3", "on-finished": "~2.3.0", "utils-merge": "1.0.0", "content-type": "~1.0.2", "finalhandler": "~1.0.4", "range-parser": "~1.2.0", "serve-static": "1.12.4", "array-flatten": "1.1.1", "path-to-regexp": "0.1.7", "setprototypeof": "1.0.3", "cookie-signature": "1.0.6", "merge-descriptors": "1.0.1", "content-disposition": "0.5.2"}, "devDependencies": {"ejs": "2.5.7", "hbs": "4.0.1", "after": "0.8.2", "mocha": "3.5.0", "vhost": "~3.0.2", "eslint": "2.13.1", "marked": "0.3.6", "morgan": "1.8.2", "should": "11.2.1", "istanbul": "0.4.5", "supertest": "1.2.0", "multiparty": "4.1.3", "body-parser": "1.17.2", "connect-redis": "~2.4.1", "cookie-parser": "~1.4.3", "cookie-session": "1.3.0", "express-session": "1.15.5", "method-override": "2.3.9", "pbkdf2-password": "1.2.1"}, "dist": {"shasum": "032e2253489cf8fce02666beca3d11ed7a2daed1", "tarball": "https://registry.npmjs.org/express/-/express-4.15.4.tgz", "integrity": "sha512-g48YBFQpZMDOGe/l9rOUinTcSBAtHVH17+0iFp/nwIP9Ik5thohP0feDe4M748aCOrlA7mC99mlUlbpxdZcq9w==", "signatures": [{"sig": "MEUCIH4jrGAhDRIvaOM8v/0B1wfmuGZQpvqczuTkCJ0YfojSAiEA6gW5V+OsFXn/MGqFTOuQJlcLHKppxVu5m7lXmKyM4SQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "4.15.5": {"name": "express", "version": "4.15.5", "dependencies": {"qs": "6.5.0", "depd": "~1.1.1", "etag": "~1.8.0", "send": "0.15.6", "vary": "~1.1.1", "debug": "2.6.9", "fresh": "0.5.2", "cookie": "0.3.1", "accepts": "~1.3.3", "methods": "~1.1.2", "type-is": "~1.6.15", "parseurl": "~1.3.1", "statuses": "~1.3.1", "encodeurl": "~1.0.1", "proxy-addr": "~1.1.5", "escape-html": "~1.0.3", "on-finished": "~2.3.0", "utils-merge": "1.0.0", "content-type": "~1.0.2", "finalhandler": "~1.0.6", "range-parser": "~1.2.0", "serve-static": "1.12.6", "array-flatten": "1.1.1", "path-to-regexp": "0.1.7", "setprototypeof": "1.0.3", "cookie-signature": "1.0.6", "merge-descriptors": "1.0.1", "content-disposition": "0.5.2"}, "devDependencies": {"ejs": "2.5.7", "hbs": "4.0.1", "after": "0.8.2", "mocha": "3.5.3", "vhost": "~3.0.2", "eslint": "2.13.1", "marked": "0.3.6", "morgan": "1.8.2", "should": "13.1.0", "istanbul": "0.4.5", "supertest": "1.2.0", "multiparty": "4.1.3", "body-parser": "1.18.1", "connect-redis": "~2.4.1", "cookie-parser": "~1.4.3", "cookie-session": "1.3.1", "express-session": "1.15.5", "method-override": "2.3.9", "pbkdf2-password": "1.2.1"}, "dist": {"shasum": "670235ca9598890a5ae8170b83db722b842ed927", "tarball": "https://registry.npmjs.org/express/-/express-4.15.5.tgz", "integrity": "sha512-E5kemI7MqPbkLstU1xLG3VB7TgSRyJu4N765mQinL9agWiD5HzpK5IZTUYuqd8m0M8A8ReM23xBqcwiSzrDRGQ==", "signatures": [{"sig": "MEUCIQDirwvnhfjYP091uQn8IM+YU3xRTJNEA69WEVQHY2GeGAIgV9JaJGv6W6yNUlQcG4c5824LnXWlR6WWz119rSx0i8o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "5.0.0-alpha.6": {"name": "express", "version": "5.0.0-alpha.6", "dependencies": {"qs": "6.5.0", "depd": "~1.1.1", "etag": "~1.8.0", "send": "0.15.6", "vary": "~1.1.1", "debug": "2.6.9", "fresh": "0.5.2", "cookie": "0.3.1", "router": "~1.3.1", "accepts": "~1.3.3", "methods": "~1.1.2", "type-is": "~1.6.15", "parseurl": "~1.3.1", "statuses": "~1.3.1", "encodeurl": "~1.0.1", "proxy-addr": "~1.1.5", "escape-html": "~1.0.3", "on-finished": "~2.3.0", "utils-merge": "1.0.0", "content-type": "~1.0.2", "finalhandler": "~1.0.6", "range-parser": "~1.2.0", "serve-static": "1.12.6", "array-flatten": "2.1.1", "path-to-regexp": "0.1.7", "setprototypeof": "1.0.3", "cookie-signature": "1.0.6", "path-is-absolute": "1.0.1", "merge-descriptors": "1.0.1", "content-disposition": "0.5.2"}, "devDependencies": {"ejs": "2.5.7", "hbs": "4.0.1", "after": "0.8.2", "mocha": "3.5.3", "vhost": "~3.0.2", "eslint": "2.13.1", "marked": "0.3.6", "morgan": "1.8.2", "should": "13.1.0", "istanbul": "0.4.5", "supertest": "1.2.0", "multiparty": "4.1.3", "body-parser": "1.18.1", "connect-redis": "~2.4.1", "cookie-parser": "~1.4.3", "cookie-session": "1.3.1", "express-session": "1.15.5", "method-override": "2.3.9", "pbkdf2-password": "1.2.1"}, "dist": {"shasum": "85dc44d7e90d4809041407f388f239b5bd2f681e", "tarball": "https://registry.npmjs.org/express/-/express-5.0.0-alpha.6.tgz", "integrity": "sha512-+1R7lL9TIxBcUzBbg7E9Lbc0CZ2Yjb1ANJPc1y1ri953TJfeXY3BAru/MPvOntfvHsSVaaYw43jqcqv6fVGu0w==", "signatures": [{"sig": "MEUCIF1+2uMkyUfXC2eDZkv9yRAve04W/zfkomy6Dtf4w8/4AiEA5YjR0sCP/wK+ss7DDVXoy7TGFgi/bpI16pr2r9RQb+s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "4.16.0": {"name": "express", "version": "4.16.0", "dependencies": {"qs": "6.5.1", "depd": "~1.1.1", "etag": "~1.8.1", "send": "0.16.0", "vary": "~1.1.2", "debug": "2.6.9", "fresh": "0.5.2", "cookie": "0.3.1", "accepts": "~1.3.4", "methods": "~1.1.2", "type-is": "~1.6.15", "parseurl": "~1.3.2", "statuses": "~1.3.1", "encodeurl": "~1.0.1", "proxy-addr": "~2.0.2", "body-parser": "1.18.2", "escape-html": "~1.0.3", "on-finished": "~2.3.0", "safe-buffer": "5.1.1", "utils-merge": "1.0.1", "content-type": "~1.0.4", "finalhandler": "1.1.0", "range-parser": "~1.2.0", "serve-static": "1.13.0", "array-flatten": "1.1.1", "path-to-regexp": "0.1.7", "setprototypeof": "1.1.0", "cookie-signature": "1.0.6", "merge-descriptors": "1.0.1", "content-disposition": "0.5.2"}, "devDependencies": {"ejs": "2.5.7", "hbs": "4.0.1", "after": "0.8.2", "mocha": "3.5.3", "vhost": "~3.0.2", "eslint": "2.13.1", "marked": "0.3.6", "morgan": "1.9.0", "should": "13.1.0", "istanbul": "0.4.5", "supertest": "1.2.0", "multiparty": "4.1.3", "connect-redis": "~2.4.1", "cookie-parser": "~1.4.3", "cookie-session": "1.3.2", "express-session": "1.15.6", "method-override": "2.3.10", "pbkdf2-password": "1.2.1"}, "dist": {"shasum": "b519638e4eb58e7178c81b498ef22f798cb2e255", "tarball": "https://registry.npmjs.org/express/-/express-4.16.0.tgz", "integrity": "sha512-mBhv3wUcA4u0UyVlxjwJYmvdrDu1eBj9w9/8go1WCRqMDH2Rs0i+GFoWutMHtYlEkfjz9C4aQeEleWxT//ZYKg==", "signatures": [{"sig": "MEYCIQDXzXYKOO+Q6ZA6LBuHqFSa6PTpqvXcUlBsGQgRToirzgIhAOS6BqayPiftlPuw7RIkd59gwUZlWBWxPz1NBUhW/gyB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "4.16.1": {"name": "express", "version": "4.16.1", "dependencies": {"qs": "6.5.1", "depd": "~1.1.1", "etag": "~1.8.1", "send": "0.16.1", "vary": "~1.1.2", "debug": "2.6.9", "fresh": "0.5.2", "cookie": "0.3.1", "accepts": "~1.3.4", "methods": "~1.1.2", "type-is": "~1.6.15", "parseurl": "~1.3.2", "statuses": "~1.3.1", "encodeurl": "~1.0.1", "proxy-addr": "~2.0.2", "body-parser": "1.18.2", "escape-html": "~1.0.3", "on-finished": "~2.3.0", "safe-buffer": "5.1.1", "utils-merge": "1.0.1", "content-type": "~1.0.4", "finalhandler": "1.1.0", "range-parser": "~1.2.0", "serve-static": "1.13.1", "array-flatten": "1.1.1", "path-to-regexp": "0.1.7", "setprototypeof": "1.1.0", "cookie-signature": "1.0.6", "merge-descriptors": "1.0.1", "content-disposition": "0.5.2"}, "devDependencies": {"ejs": "2.5.7", "hbs": "4.0.1", "after": "0.8.2", "mocha": "3.5.3", "vhost": "~3.0.2", "eslint": "2.13.1", "marked": "0.3.6", "morgan": "1.9.0", "should": "13.1.0", "istanbul": "0.4.5", "supertest": "1.2.0", "multiparty": "4.1.3", "connect-redis": "~2.4.1", "cookie-parser": "~1.4.3", "cookie-session": "1.3.2", "express-session": "1.15.6", "method-override": "2.3.10", "pbkdf2-password": "1.2.1"}, "dist": {"shasum": "6b33b560183c9b253b7b62144df33a4654ac9ed0", "tarball": "https://registry.npmjs.org/express/-/express-4.16.1.tgz", "integrity": "sha512-STB7LZ4N0L+81FJHGla2oboUHTk4PaN1RsOkoRh9OSeEKylvF5hwKYVX1xCLFaCT7MD0BNG/gX2WFMLqY6EMBw==", "signatures": [{"sig": "MEQCIEu0eghWh5bnIcF0GIFfqL3+bum66PPMIrWJDHNxhDUpAiAZ7L3GVXHOd+RViF7ekl5EBwr0MILqiHPlsIL/EnPapA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "4.16.2": {"name": "express", "version": "4.16.2", "dependencies": {"qs": "6.5.1", "depd": "~1.1.1", "etag": "~1.8.1", "send": "0.16.1", "vary": "~1.1.2", "debug": "2.6.9", "fresh": "0.5.2", "cookie": "0.3.1", "accepts": "~1.3.4", "methods": "~1.1.2", "type-is": "~1.6.15", "parseurl": "~1.3.2", "statuses": "~1.3.1", "encodeurl": "~1.0.1", "proxy-addr": "~2.0.2", "body-parser": "1.18.2", "escape-html": "~1.0.3", "on-finished": "~2.3.0", "safe-buffer": "5.1.1", "utils-merge": "1.0.1", "content-type": "~1.0.4", "finalhandler": "1.1.0", "range-parser": "~1.2.0", "serve-static": "1.13.1", "array-flatten": "1.1.1", "path-to-regexp": "0.1.7", "setprototypeof": "1.1.0", "cookie-signature": "1.0.6", "merge-descriptors": "1.0.1", "content-disposition": "0.5.2"}, "devDependencies": {"ejs": "2.5.7", "hbs": "4.0.1", "after": "0.8.2", "mocha": "3.5.3", "vhost": "~3.0.2", "eslint": "2.13.1", "marked": "0.3.6", "morgan": "1.9.0", "should": "13.1.0", "istanbul": "0.4.5", "supertest": "1.2.0", "multiparty": "4.1.3", "connect-redis": "~2.4.1", "cookie-parser": "~1.4.3", "cookie-session": "1.3.2", "express-session": "1.15.6", "method-override": "2.3.10", "pbkdf2-password": "1.2.1"}, "dist": {"shasum": "e35c6dfe2d64b7dca0a5cd4f21781be3299e076c", "tarball": "https://registry.npmjs.org/express/-/express-4.16.2.tgz", "integrity": "sha512-4mc9RUEAUpPMFR6gpXcnPt0/q2Zil35FTUr07ixWYX90RmUKL3jUbvTvJzkc/uL3r+A7kuWSiIqOyVUSWoZXWQ==", "signatures": [{"sig": "MEUCIQDVhRhbrcPsh3lmoPmtGwzrCcp+lWhbdDfAcV1aTyxTmQIgUPX6Mg2pSyQ8NapfzSEVJzpk7eMSUGFkFqETw+MiHnk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}}, "4.16.3": {"name": "express", "version": "4.16.3", "dependencies": {"qs": "6.5.1", "depd": "~1.1.2", "etag": "~1.8.1", "send": "0.16.2", "vary": "~1.1.2", "debug": "2.6.9", "fresh": "0.5.2", "cookie": "0.3.1", "accepts": "~1.3.5", "methods": "~1.1.2", "type-is": "~1.6.16", "parseurl": "~1.3.2", "statuses": "~1.4.0", "encodeurl": "~1.0.2", "proxy-addr": "~2.0.3", "body-parser": "1.18.2", "escape-html": "~1.0.3", "on-finished": "~2.3.0", "safe-buffer": "5.1.1", "utils-merge": "1.0.1", "content-type": "~1.0.4", "finalhandler": "1.1.1", "range-parser": "~1.2.0", "serve-static": "1.13.2", "array-flatten": "1.1.1", "path-to-regexp": "0.1.7", "setprototypeof": "1.1.0", "cookie-signature": "1.0.6", "merge-descriptors": "1.0.1", "content-disposition": "0.5.2"}, "devDependencies": {"ejs": "2.5.7", "hbs": "4.0.1", "after": "0.8.2", "mocha": "3.5.3", "vhost": "~3.0.2", "eslint": "2.13.1", "marked": "0.3.17", "morgan": "1.9.0", "should": "13.2.1", "istanbul": "0.4.5", "supertest": "1.2.0", "multiparty": "4.1.3", "connect-redis": "~2.4.1", "cookie-parser": "~1.4.3", "cookie-session": "1.3.2", "express-session": "1.15.6", "method-override": "2.3.10", "pbkdf2-password": "1.2.1"}, "dist": {"shasum": "6af8a502350db3246ecc4becf6b5a34d22f7ed53", "tarball": "https://registry.npmjs.org/express/-/express-4.16.3.tgz", "fileCount": 16, "integrity": "sha512-CDaOBMB9knI6vx9SpIxEMOJ6VBbC2U/tYNILs0qv1YOZc15K9U2EcF06v10F0JX6IYcWnKYZJwIDJspEHLvUaQ==", "signatures": [{"sig": "MEUCIQCMYl+Tezd+bY8N8hrbVMo/BRX9s8v6hxQJAes/StRadAIgNaTstG+LFGSuGYeaBCvp9lSCiRPtNVCT8iq2NQORepw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 205577}, "engines": {"node": ">= 0.10.0"}}, "4.16.4": {"name": "express", "version": "4.16.4", "dependencies": {"qs": "6.5.2", "depd": "~1.1.2", "etag": "~1.8.1", "send": "0.16.2", "vary": "~1.1.2", "debug": "2.6.9", "fresh": "0.5.2", "cookie": "0.3.1", "accepts": "~1.3.5", "methods": "~1.1.2", "type-is": "~1.6.16", "parseurl": "~1.3.2", "statuses": "~1.4.0", "encodeurl": "~1.0.2", "proxy-addr": "~2.0.4", "body-parser": "1.18.3", "escape-html": "~1.0.3", "on-finished": "~2.3.0", "safe-buffer": "5.1.2", "utils-merge": "1.0.1", "content-type": "~1.0.4", "finalhandler": "1.1.1", "range-parser": "~1.2.0", "serve-static": "1.13.2", "array-flatten": "1.1.1", "path-to-regexp": "0.1.7", "setprototypeof": "1.1.0", "cookie-signature": "1.0.6", "merge-descriptors": "1.0.1", "content-disposition": "0.5.2"}, "devDependencies": {"ejs": "2.6.1", "hbs": "4.0.1", "after": "0.8.2", "mocha": "5.2.0", "vhost": "~3.0.2", "eslint": "2.13.1", "marked": "0.5.1", "morgan": "1.9.1", "should": "13.2.3", "istanbul": "0.4.5", "supertest": "3.3.0", "multiparty": "4.2.1", "connect-redis": "3.4.0", "cookie-parser": "~1.4.3", "cookie-session": "1.3.2", "express-session": "1.15.6", "method-override": "3.0.0", "pbkdf2-password": "1.2.1"}, "dist": {"shasum": "fddef61926109e24c515ea97fd2f1bdbf62df12e", "tarball": "https://registry.npmjs.org/express/-/express-4.16.4.tgz", "fileCount": 16, "integrity": "sha512-j12Uuyb4FMrd/qQAm6uCHAkPtO8FDTRJZBDd5D2KOL2eLaz1yUNdUB/NOIyq0iU4q4cFarsUCrnFDPBcnksuOg==", "signatures": [{"sig": "MEUCIGZuIM4RppbIw/CpTNE/sts59NSNu9wBJxCaCKfwkHw2AiEA0P2cuvOOJsRrAMvX8ylRvtRf4SLn1SgjA6bC/CIdK9k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 206123, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbvsqSCRA9TVsSAnZWagAAPFwP/iCvznxNrmvgY9ox7w2k\ncS/ej9HZJ5NGjBEWtae1F2bjJ2V7rOxVTGTlqiPMSNIzTgw3fpFkIXp9kCA4\nY03NOsYUjYscGjXR6f2fvOVJ/Si5FKlqr7Ow6WMBClrdo/CMCc8kH9fxtPja\nHla58xiU7ftlzUHIjGmmnHFzAjAeGj+3e3v1omuoeP6mPuxlwYoQ0MuD0sFa\n9qJAFZ0MBrfvoQBI8G++GZZhxalhefuibWi1ErRw3F5cLfvhjKi4HGPh+sDu\nc63D99wQIJIq4HumwX0JNW7OywuL28wgxgtvKyg0iCVR/BnAYiEA0UZUVI4h\nsX1Kuht1oHEp1iGOvGALYotPiovnDCAra+2zPM1p8oZKdXHEpkAygG3mCiD5\nyWlWrFo5jJudULWzMtHp6F0RwQJjpSavnkbusKWZvO717/1Ku5FIM4cnTWVK\nELGmb011jRPMvwFqv1C04SvhBT+UrXe4kd0qwJWQEDT1aWzbjbaroPmVQ+l1\nxzUkHRHm7vYCBE0RxQ4FImNWlYYQVVyBSroYwxvJnP6H8m/DR7oxDPDoJcBn\nXXETuH8Ca+q8KjwdrstVXCwKfB+zs0Z41/oOWKrhsDY2B9HwmyOOA8EtG4QO\np2waBrigD8L4T/Y3II4T144z2MclVid7DulrzKCMiE6yoTqvrH64FfWkLQqG\n11u8\r\n=ZruB\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.10.0"}}, "5.0.0-alpha.7": {"name": "express", "version": "5.0.0-alpha.7", "dependencies": {"qs": "6.5.2", "depd": "~1.1.2", "etag": "~1.8.1", "send": "0.16.2", "vary": "~1.1.2", "debug": "3.1.0", "fresh": "0.5.2", "cookie": "0.3.1", "router": "2.0.0-alpha.1", "accepts": "~1.3.5", "methods": "~1.1.2", "type-is": "~1.6.16", "parseurl": "~1.3.2", "statuses": "~1.4.0", "encodeurl": "~1.0.2", "proxy-addr": "~2.0.4", "body-parser": "1.18.3", "escape-html": "~1.0.3", "on-finished": "~2.3.0", "safe-buffer": "5.1.2", "utils-merge": "1.0.1", "content-type": "~1.0.4", "finalhandler": "1.1.1", "range-parser": "~1.2.0", "serve-static": "1.13.2", "array-flatten": "2.1.1", "setprototypeof": "1.1.0", "cookie-signature": "1.0.6", "path-is-absolute": "1.0.1", "merge-descriptors": "1.0.1", "content-disposition": "0.5.2"}, "devDependencies": {"ejs": "2.6.1", "hbs": "4.0.1", "after": "0.8.2", "mocha": "5.2.0", "vhost": "~3.0.2", "eslint": "2.13.1", "marked": "0.5.1", "morgan": "1.9.1", "should": "13.2.3", "istanbul": "0.4.5", "supertest": "3.3.0", "multiparty": "4.2.1", "connect-redis": "3.4.0", "cookie-parser": "~1.4.3", "cookie-session": "1.3.2", "express-session": "1.15.6", "method-override": "3.0.0", "pbkdf2-password": "1.2.1"}, "dist": {"shasum": "879bfb1bd52834646a9d8c3a773863c36e4d494c", "tarball": "https://registry.npmjs.org/express/-/express-5.0.0-alpha.7.tgz", "fileCount": 11, "integrity": "sha512-3FW+yXzYCViXf6Ty9TN9IKLW+rC8qok3ktS4hS1FILAEnMnfnDpQ+23rZVvWC0Ul1alYpJXx7xSBSBp073970g==", "signatures": [{"sig": "MEUCIQCLv8FYXx29DQeMIl+CaZG9+V4/zL8rjl2s4X1B6sTCyAIgVFObBlzDqJBpjj/Jn+jWAbHOR+E9kla9l1e4ViKkkRE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 179029, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb09eLCRA9TVsSAnZWagAA/1AP/269JF2vhXEO9n3MaQLu\nSs95oz9PfsYyucun0Qgjjd5OyERY7IwtkbYoMn60M18w8ni1JR9kjqQ8m07t\nUIpgUBnfnytj9L7qlnmPMF2Uzrh6YwX5gg1jzx0Tri8EwehllZg3f5o2nxPX\nduG87uxNzxUszo52FXRR98Vz6vVup0/0smLa8jtq+VxXRhW3zGcU+zTAIoyy\nP7bvI4Zg5RKWzABTIfBsqW9sxJ6yT0Xa/otiO/IJ3YjJb2f76FdAN1RwrEnA\nvherLVx1V6EooqhkrS0W45Ong2KEytpHWTKj5APDpggffflfJyiON2BqvrPI\nmSDESQzyArpgwckBaSofLcydD7aaGtYP/NpATT3khrWw3UkFeeG0LGGulz7e\nbPN8PFuSXiZ5dfcBXNQsViSF6jkghg0y8bffC3h4VewsKKfgLKehwOjn+Mp4\n7dyZ0KcCJn/xcCCJJFAkJJB9j4Pfqxj1D2hlUMXfSj6L7unmbOnwsFtL0m32\nb0w+WkUxy8DR+UFGUUHGK5bNE9OsX5tYSWm9RH8Z8cco5rgLBkk3Lxq1rSjY\nzLrM5FsakMWgSlI1BTN5gXX9TYumCzke4vI8emkxe8lR3l+XPz2wP/+HB1wg\nfxSecAWnJKwkDAtBifHB8eiOWNLoSnJiOkWk0VnkSXa9Aw95yED1B/sAv/i5\nk5y/\r\n=n8Sg\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.10.0"}}, "4.17.0": {"name": "express", "version": "4.17.0", "dependencies": {"qs": "6.7.0", "depd": "~1.1.2", "etag": "~1.8.1", "send": "0.17.1", "vary": "~1.1.2", "debug": "2.6.9", "fresh": "0.5.2", "cookie": "0.4.0", "accepts": "~1.3.7", "methods": "~1.1.2", "type-is": "~1.6.18", "parseurl": "~1.3.3", "statuses": "~1.5.0", "encodeurl": "~1.0.2", "proxy-addr": "~2.0.5", "body-parser": "1.19.0", "escape-html": "~1.0.3", "on-finished": "~2.3.0", "safe-buffer": "5.1.2", "utils-merge": "1.0.1", "content-type": "~1.0.4", "finalhandler": "~1.1.2", "range-parser": "~1.2.1", "serve-static": "1.14.1", "array-flatten": "1.1.1", "path-to-regexp": "0.1.7", "setprototypeof": "1.1.1", "cookie-signature": "1.0.6", "merge-descriptors": "1.0.1", "content-disposition": "0.5.3"}, "devDependencies": {"ejs": "2.6.1", "hbs": "4.0.4", "after": "0.8.2", "mocha": "5.2.0", "vhost": "~3.0.2", "eslint": "2.13.1", "marked": "0.6.2", "morgan": "1.9.1", "should": "13.2.3", "istanbul": "0.4.5", "supertest": "3.3.0", "multiparty": "4.2.1", "connect-redis": "3.4.1", "cookie-parser": "~1.4.4", "cookie-session": "1.3.3", "express-session": "1.16.1", "method-override": "3.0.0", "pbkdf2-password": "1.2.1"}, "dist": {"shasum": "288af62228a73f4c8ea2990ba3b791bb87cd4438", "tarball": "https://registry.npmjs.org/express/-/express-4.17.0.tgz", "fileCount": 16, "integrity": "sha512-1Z7/t3Z5ZnBG252gKUPyItc4xdeaA0X934ca2ewckAsVsw9EG71i++ZHZPYnus8g/s5Bty8IMpSVEuRkmwwPRQ==", "signatures": [{"sig": "MEUCIQCAGNpx7PevrfR2EkKVHEUA6C6dXtuIWUpmOoM8ng0TfAIgZF84IsByjeMzvdMh4WsI/jc5G4vzcOwc2nqGXGWfdA0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 208134, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc3hUVCRA9TVsSAnZWagAA3oUP/3V7aiaEhUSyQ9lyDnPF\nxiRwGy0XNIoNRyZbw8gMN2/7V/jjV+H/vWdo9gSr3NJAGsGlS8AtT0uNvKv/\nrssr3WA65/J9QNdCixePj/LHilRzOSMKxnIhk20bVh186vEx7fwehqXbifcS\nNIoSieQRnllJCVH0JudVim4AMWdy3Y2vOLV1kE6UpDs41c3eXzUfFEVxI+WD\nXjUrfHsRCK/IZ5No2Hw8uwF2Y2pnuRHFC0ehIWn+Foijy87doiFidxdn2ybg\nFjdo+AFH3LX2RBR4o7UugtDV1wB0ymRVRNSIk6xoKmMGi5RNE5dhPxNkEvk7\nX5nK18AhRzRFIIZDhHtOZE9wWvlf/25p0y8CmzKrXkpmiuzcby4EneyV0Muk\n8WmbnEO1ah7SATsVf0d/AnR1tCXE+0wLXvVrq9Z1BAkeW1rsR9OHqzpLGCc3\njmYqyrN+2iyPeqy/cemnU52fmUC/Kfj8q8Uv2RCxJo9cAKKp+ljaMXCiJMcP\nrYPu4X0n0ijSLVF1dAQkDs05MVbZeCl5RM0GhPndFwcdCBc1JdJSYK/6ylHK\nFDoB5YBzxglMPL8iMwbDhS2+N25vRDAWr52GKTwJFmcfW04/EXrFEgm2gk1/\nmRLojES3L7P5L96c9P2SVzp7YjWFw71OkQctzlDrNC28VOU3ie95pYQyHV5/\n64mY\r\n=ZTgO\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.10.0"}}, "4.17.1": {"name": "express", "version": "4.17.1", "dependencies": {"qs": "6.7.0", "depd": "~1.1.2", "etag": "~1.8.1", "send": "0.17.1", "vary": "~1.1.2", "debug": "2.6.9", "fresh": "0.5.2", "cookie": "0.4.0", "accepts": "~1.3.7", "methods": "~1.1.2", "type-is": "~1.6.18", "parseurl": "~1.3.3", "statuses": "~1.5.0", "encodeurl": "~1.0.2", "proxy-addr": "~2.0.5", "body-parser": "1.19.0", "escape-html": "~1.0.3", "on-finished": "~2.3.0", "safe-buffer": "5.1.2", "utils-merge": "1.0.1", "content-type": "~1.0.4", "finalhandler": "~1.1.2", "range-parser": "~1.2.1", "serve-static": "1.14.1", "array-flatten": "1.1.1", "path-to-regexp": "0.1.7", "setprototypeof": "1.1.1", "cookie-signature": "1.0.6", "merge-descriptors": "1.0.1", "content-disposition": "0.5.3"}, "devDependencies": {"ejs": "2.6.1", "hbs": "4.0.4", "after": "0.8.2", "mocha": "5.2.0", "vhost": "~3.0.2", "eslint": "2.13.1", "marked": "0.6.2", "morgan": "1.9.1", "should": "13.2.3", "istanbul": "0.4.5", "supertest": "3.3.0", "multiparty": "4.2.1", "connect-redis": "3.4.1", "cookie-parser": "~1.4.4", "cookie-session": "1.3.3", "express-session": "1.16.1", "method-override": "3.0.0", "pbkdf2-password": "1.2.1"}, "dist": {"shasum": "4491fc38605cf51f8629d39c2b5d026f98a4c134", "tarball": "https://registry.npmjs.org/express/-/express-4.17.1.tgz", "fileCount": 16, "integrity": "sha512-mHJ9O79RqluphRrcw2X/GTh3k9tVv8YcoyY4Kkh4WDMUYKRZUq0h1o0w2rrrxBqM7VoeUVqgb27xlEMXTnYt4g==", "signatures": [{"sig": "MEUCICQgcHZI8QrmBKcFkCxEopG6oFx7tOFQ/5MEphrR6yzDAiEAk/lgG7aANiylDTtQ31NrvELKJ4MmRQ+3u89poClP2Io=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 208133, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc6hU/CRA9TVsSAnZWagAAFc4QAJzqxI1sgdfreUHk+NIa\n38jYea65Xg8N9JgZVF67j7aXqPT6VXhGu2j54oveGIkr+RL2Xm58RrRWn+Sg\nVWOOSZzotLKtx1qCYS4ozPRYvujKMLYDeiLxePDCSrrLYt48+IJwkHF04Un1\nJ0ZUmtlEqgLL85gvaCrKa9qF8TfwbQhhIzQ914vum11tJ506ePpffN2xFY0M\nsHf0CiuV1OFOD7Wne/RR7DVsxQwZ/FXomkxLJm8+T+T9ZYm3WQxWVD7BRQpA\nN08+zkPd1XMEZiVZkR9Ie4+7ydZomJE8PNCOt5SzvEW6ekDW10QuuF0521Wj\n5lHp4AflVFq1LTJB0WDR6VIPJRp0H5aYTh1tBRxWHUx/EP2LfFS/XEz1bUvm\nBDVj2e1iA4ZWz8aeu9p/2N8Zp05WGINF3/E4YG9smxxs5EDJZGA9k1DAj6US\nzKWTOemaqypRshFWThvfA70a1Rcwdj+0XGboscg/S20XTT0FvG2GLkEY0OO/\niHBy5fKYplUQsths48V8I9P9Gx6U534iaFJlxlzzVEsDleBkH+NBSP8OB7dx\n8N/0ZQDBY6JWL5ZSW9yVY2FzrTEmUOPC1Rts5Uj4m7SBmu8yK154ylnPQ4T6\nMr0jG8XQPYhTLc5pYNTFZNV1Ydu4d01xIrLhGy/3dc7kRlwy3FN5ceNVsB88\njyN+\r\n=QYw2\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.10.0"}}, "5.0.0-alpha.8": {"name": "express", "version": "5.0.0-alpha.8", "dependencies": {"qs": "6.7.0", "depd": "~1.1.2", "etag": "~1.8.1", "send": "0.17.1", "vary": "~1.1.2", "debug": "3.1.0", "fresh": "0.5.2", "cookie": "0.4.0", "router": "2.0.0-alpha.1", "accepts": "~1.3.7", "methods": "~1.1.2", "type-is": "~1.6.18", "parseurl": "~1.3.3", "statuses": "~1.5.0", "encodeurl": "~1.0.2", "proxy-addr": "~2.0.5", "body-parser": "1.19.0", "escape-html": "~1.0.3", "on-finished": "~2.3.0", "safe-buffer": "5.1.2", "utils-merge": "1.0.1", "content-type": "~1.0.4", "finalhandler": "~1.1.2", "range-parser": "~1.2.1", "serve-static": "1.14.1", "array-flatten": "2.1.1", "setprototypeof": "1.1.1", "cookie-signature": "1.0.6", "path-is-absolute": "1.0.1", "merge-descriptors": "1.0.1", "content-disposition": "0.5.3"}, "devDependencies": {"ejs": "2.6.1", "hbs": "4.0.4", "after": "0.8.2", "mocha": "5.2.0", "vhost": "~3.0.2", "eslint": "2.13.1", "marked": "0.6.2", "morgan": "1.9.1", "should": "13.2.3", "istanbul": "0.4.5", "supertest": "3.3.0", "multiparty": "4.2.1", "connect-redis": "3.4.1", "cookie-parser": "~1.4.4", "cookie-session": "1.3.3", "express-session": "1.16.1", "method-override": "3.0.0", "pbkdf2-password": "1.2.1"}, "dist": {"shasum": "b9dd3a568eab791e3391db47f9e6ab91e61b13fe", "tarball": "https://registry.npmjs.org/express/-/express-5.0.0-alpha.8.tgz", "fileCount": 11, "integrity": "sha512-PL8wTLgaNOiq7GpXt187/yWHkrNSfbr4H0yy+V0fpqJt5wpUzBi9DprAkwGKBFOqWHylJ8EyPy34V5u9YArfng==", "signatures": [{"sig": "MEQCIHXunfWcElNx4SpK/OCDhs8cyRaWnpiiL8BqvB9FsVIGAiABaQni/YsCGW842xV/RbwiIvFDCu1YHUTZYZK1bFSD5g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 181194, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJee/3fCRA9TVsSAnZWagAAzf8P/2dMh5PO1SR+CZLuGvPE\nOcR9dd4+epUcIgK6antdYjzMm+HHHMTnObyS523wd9Xm2nWLNDI70nSNHUbn\nxIjlGp9o+NMtvv0RnKKkG+xnlidfrkt7SVvlVzr5D65m6UNxp8bP01KElCNh\nqkAO7ipVYFhzEWFbFJWljN9kR1mCSp4qpL+vTn1wn8xSryYH/+ZRc8rBBlCA\nzBUfx3cQAaH8fy6Cij/bzTdcGWqucBrTP6wgRZca3EDKaOhC8JSf072ISqZM\nAwIUANiYZKPGDv5AUh2T1C8jG4tKdoROr9iqIrsHn9iW8Ppk5R4odblZtDNW\nhROzSfS7i5lFZFxhMZCnrV5aN/zbBiRtMIpFGns0EYWd07l5fMRA817ItntM\nbBZB4MJBH91SoTonBg8Elo5oE9428kdHDKiNi+eK6C3ndqAE0KzgeOIBmol4\n4V3Q4/v6MxSAjGWO9Kw3wKjpCJ4B3LV3F4NwKGHDQlWidkCKQOFg5ylCmDcw\n+7z8/GeahapeWRtkifAhavX0rNYiRUjrgY0yeR98YFOg1K4yvYSXxbY4Xv2B\nWqxikjXqPXk6PavumvJizunzxGVKfOpiQ6XFcIkpfIEJ3JRfU8LUgx+EhAIq\nB35/nbzY8E3f3RCINhqV3y+rBsHhPmnElCASaL5iO5A9CaltyfyZPA+ciMS4\neKlV\r\n=P2Q8\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.10.0"}}, "4.17.2": {"name": "express", "version": "4.17.2", "dependencies": {"qs": "6.9.6", "depd": "~1.1.2", "etag": "~1.8.1", "send": "0.17.2", "vary": "~1.1.2", "debug": "2.6.9", "fresh": "0.5.2", "cookie": "0.4.1", "accepts": "~1.3.7", "methods": "~1.1.2", "type-is": "~1.6.18", "parseurl": "~1.3.3", "statuses": "~1.5.0", "encodeurl": "~1.0.2", "proxy-addr": "~2.0.7", "body-parser": "1.19.1", "escape-html": "~1.0.3", "on-finished": "~2.3.0", "safe-buffer": "5.2.1", "utils-merge": "1.0.1", "content-type": "~1.0.4", "finalhandler": "~1.1.2", "range-parser": "~1.2.1", "serve-static": "1.14.2", "array-flatten": "1.1.1", "path-to-regexp": "0.1.7", "setprototypeof": "1.2.0", "cookie-signature": "1.0.6", "merge-descriptors": "1.0.1", "content-disposition": "0.5.4"}, "devDependencies": {"ejs": "3.1.6", "hbs": "4.2.0", "after": "0.8.2", "mocha": "9.1.3", "vhost": "~3.0.2", "eslint": "7.32.0", "marked": "0.7.0", "morgan": "1.10.0", "should": "13.2.3", "istanbul": "0.4.5", "supertest": "6.1.6", "multiparty": "4.2.2", "connect-redis": "3.4.2", "cookie-parser": "1.4.6", "cookie-session": "2.0.0", "express-session": "1.17.2", "method-override": "3.0.0", "pbkdf2-password": "1.2.1"}, "dist": {"shasum": "c18369f265297319beed4e5558753cc8c1364cb3", "tarball": "https://registry.npmjs.org/express/-/express-4.17.2.tgz", "fileCount": 16, "integrity": "sha512-oxlxJxcQlYwqPWKVJJtvQiwHgosH/LrLSPA+H4UxpyvSS6jC5aH+5MoHFM+KABgTOt0APue4w66Ha8jCUo9QGg==", "signatures": [{"sig": "MEUCIQCj/e8PryrAQItMiEqwZG8G/6c3VCvCcACzDEsod50p9AIgdReUmh6Vmi6IJZSb+xGRED9VTyQL5gRfTGy345QGXgI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 209594, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhvAioCRA9TVsSAnZWagAAUG4P+wYwkJtSmhBGxKM7keyt\ntB8DChwU/BRD+v0MmQVLOXdR9i4W9yuPuxftYEgDkrPGEhLMDFW2J6CB+fIA\nYg2bPX2DWN4VlyBHGFen08rtXz36Nf9ffNmC0wU5P4soY/thaMHP4q0AYLou\n2x4plFMP01sOJGXlsx2B8/CmpH7jrpLYVqeZTZL4QpHvGPyYXIS9D1uSYNs4\n4Bly7M5GXqUXIxZqKGyRAui1eSDiKFVmh9s/1bj1RojQJ2PbAfOYlBm5v/St\nnGmZrI6Att9QotXlg0U/8asGb9HaPcIFPLQPowqhjCyOpRWnILy8DKVYMMHk\nPuarv+CjFLI6R5w0lGCY7VXLO5pOscZwlE66reDPqJtQwT2DVCLP8xZ4tyBk\nk01RZwc+HvVMin/8KLQoiKzs8nlmMFbq8bG7H7Sv4I+NlLIGEYFxBvb62dNK\n5SOHTGrUrdyVt680HqNo0mrpX9duGHFzwlxLgU/Fd3SpcBuzGNy8G11uoJnj\n8srRI/USPOEjSNETLFzJAcWxYHbSO/PpAuXbPt/K2FxLv5+rW9eYWedBESLX\n0SY+oBhGtAQCdfeFdbw7RgByCWeVe9QVJ/kAz6muVR43HJDOjooHnABCnFCB\nNrzs08HpHoRgQe1Ohis5DeDYVkKmTrOFNhOpgOxz/N0krXH7wIOdlnVOpSbL\nXeZ6\r\n=nJ56\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.10.0"}}, "5.0.0-beta.1": {"name": "express", "version": "5.0.0-beta.1", "dependencies": {"qs": "6.9.6", "depd": "~1.1.2", "etag": "~1.8.1", "send": "1.0.0-beta.1", "vary": "~1.1.2", "debug": "3.1.0", "fresh": "0.5.2", "cookie": "0.4.1", "router": "2.0.0-beta.1", "accepts": "~1.3.7", "methods": "~1.1.2", "type-is": "~1.6.18", "parseurl": "~1.3.3", "statuses": "~1.5.0", "encodeurl": "~1.0.2", "mime-types": "~2.1.34", "proxy-addr": "~2.0.7", "body-parser": "2.0.0-beta.1", "escape-html": "~1.0.3", "on-finished": "~2.3.0", "safe-buffer": "5.2.1", "utils-merge": "1.0.1", "content-type": "~1.0.4", "finalhandler": "~1.1.2", "range-parser": "~1.2.1", "serve-static": "2.0.0-beta.1", "array-flatten": "3.0.0", "setprototypeof": "1.2.0", "cookie-signature": "1.0.6", "path-is-absolute": "1.0.1", "merge-descriptors": "1.0.1", "content-disposition": "0.5.4"}, "devDependencies": {"ejs": "3.1.6", "hbs": "4.2.0", "after": "0.8.2", "mocha": "9.1.3", "vhost": "~3.0.2", "eslint": "7.32.0", "marked": "0.7.0", "morgan": "1.10.0", "should": "13.2.3", "istanbul": "0.4.5", "supertest": "6.1.6", "multiparty": "4.2.2", "connect-redis": "3.4.2", "cookie-parser": "1.4.6", "cookie-session": "2.0.0", "express-session": "1.17.2", "method-override": "3.0.0", "pbkdf2-password": "1.2.1"}, "dist": {"shasum": "efbfd372e4650a48e417b1adbaf43599092ddc8f", "tarball": "https://registry.npmjs.org/express/-/express-5.0.0-beta.1.tgz", "fileCount": 11, "integrity": "sha512-KPtBrlZoQu2Ps0Ce/Imqtq73AB0KBJ8Gx59yZQ3pmDJU2/LhcoZETo03oSgtTQufbcLXt/WBITk/jMjl/WMyrQ==", "signatures": [{"sig": "MEUCIEIcsYOCCNkdDSPdFiCuDwcc+srd3Lqn4PUwKwb9tnMoAiEA1Xd2ZeJILIRew32nhWHCHsosmHMjrA9Bq41gtvnSD7Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 184181, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiCv29CRA9TVsSAnZWagAA1tkP/3/PHN/4CvkMhXuk8Rvs\n4uP8VlmvjHd0PgbgQ4bKn/YVAGdJmVn6M73bgF0qE6g3iGVF16BGo7I+5aHO\ny+eX34isPYeaO48vPkUKhR0zRtRqw0e53RldcYp77bwF6VJyWQsX/FLeFd+h\nwRpoZpebHAfcxV2VtDkRl0KG2HdbBr4GdDyn1hy8djCTI2o72BskJGtJej1h\nhFV+IUeN8ZJU8kRHbGJ+mcuMYrjxyJQe+y+mnLiRzwrsSXn15JA3tMR1+bEY\nIyV0ezkCzW9oeINm7DV9DwvgUqlJDK83YmWXeFFLYKf/hvXFj4vXSx4VKEKa\nb0Ysa3/Zt+isoUn863hCxW6EN+c2H3/7cDXe5L4cQc9TB0ogsTQv/dvtrAs2\nNL6jwHdrAkvuxHlv9bJ/xe6bT1XX5WOkCkBMMy9e/tWjWwU2IhEJ0A7TGLnK\nTX39VxunQG+xVB9vFwgDEr4+QD7VbRYTOhDxUfrfq3cktaNIxrgfgcMQx06+\nXU1xRLqV3q1QeHM6T8Vrhj4Dp0GLOc9bWCY05G8oSBpuE/lv6BcnmlsjKvil\nFHgn3ojA8nH4Sd5beH5emGOgt7dEB8U0ZiWGEsDt+c80Y90fXHmPIYvc70ET\nuDph5hMVNi+BZRHX7qkbxpKI80C4pDT8PAVlQzmVdSB39jLaIASLC6YsFCSn\n2jGq\r\n=ZZux\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 4"}}, "4.17.3": {"name": "express", "version": "4.17.3", "dependencies": {"qs": "6.9.7", "depd": "~1.1.2", "etag": "~1.8.1", "send": "0.17.2", "vary": "~1.1.2", "debug": "2.6.9", "fresh": "0.5.2", "cookie": "0.4.2", "accepts": "~1.3.8", "methods": "~1.1.2", "type-is": "~1.6.18", "parseurl": "~1.3.3", "statuses": "~1.5.0", "encodeurl": "~1.0.2", "proxy-addr": "~2.0.7", "body-parser": "1.19.2", "escape-html": "~1.0.3", "on-finished": "~2.3.0", "safe-buffer": "5.2.1", "utils-merge": "1.0.1", "content-type": "~1.0.4", "finalhandler": "~1.1.2", "range-parser": "~1.2.1", "serve-static": "1.14.2", "array-flatten": "1.1.1", "path-to-regexp": "0.1.7", "setprototypeof": "1.2.0", "cookie-signature": "1.0.6", "merge-descriptors": "1.0.1", "content-disposition": "0.5.4"}, "devDependencies": {"ejs": "3.1.6", "hbs": "4.2.0", "nyc": "15.1.0", "after": "0.8.2", "mocha": "9.2.0", "vhost": "~3.0.2", "eslint": "7.32.0", "marked": "0.7.0", "morgan": "1.10.0", "should": "13.2.3", "supertest": "6.2.2", "multiparty": "4.2.3", "resolve-path": "1.4.0", "connect-redis": "3.4.2", "cookie-parser": "1.4.6", "cookie-session": "2.0.0", "express-session": "1.17.2", "method-override": "3.0.0", "pbkdf2-password": "1.2.1"}, "dist": {"shasum": "f6c7302194a4fb54271b73a1fe7a06478c8f85a1", "tarball": "https://registry.npmjs.org/express/-/express-4.17.3.tgz", "fileCount": 16, "integrity": "sha512-yuSQpz5I+Ch7gFrPCk4/c+dIBKlQUxtgwqzph132bsT6qhuzss6I8cLJQz7B3rFblzd6wtcI0ZbGltH/C4LjUg==", "signatures": [{"sig": "MEQCIFc4F1j7TWTkiuz8R5kiHz8y6TNu8xlfJzxnr/L+1U32AiBQwTkz/uZhA9/GNCK9YP6kPq0P/kNS7FunwaFVN/D1yQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 209842, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDa2VCRA9TVsSAnZWagAAu18P/1s1SVYTG1o2bANlQlHR\nb5msgTeD5tHVlyfZZj6pOYKZ6nRH9qmR6fa9nTxPf5buDL5QQRzkMDK3KpGW\nS70A7NIE89EJsAglLcE9H6zLj194joBcWnFcvCsg7nnENbBhxuMPanxgPSuj\ndC2sURtBFEqDrNMYFuxuAaMaAuB8+YGlgzFvhIiOxPBS/dcKX2XpyUnUF2hZ\nrOPUTUmJCR1mZcK5a8wadgDiD+70crH+3qVTQC3w774kPkP2iZKKcwL10mCX\nSWu7Nh+kLYtwHknUOcN3ezwUSP7ofgTmMQIII9Lp11R6AqYPuLbcn6YACNPr\n+mkzEOIRbr63LNecu9wxBbGSM93xzjhW5Lvm5y16QhIlRiGS+EyvcaSzfGwY\n7ES3HSpPTXTdDG2tJNa6OzAE6B8o8jxyHgzXUYzF5wHOf3F5geAL1Fe1rDX8\na7+eKg000CvjH/xvevbBQXT+4BowEIQPMN68H7XvHt6xI5AwUh78fQy3Z10U\nY0pJoVEvzPULpUKYt3VCciLKn0FWWJ+sa2TG4jnxD08yqhWkz9JDyZ6O884J\nsCdzA+2NyE12J0+fNrwK+WuPWlUpoCJbZ/cBG0aWR9znxwlC59DCH1A7zZgF\nB6AjYd97T9ZHQugzHqi531Fso/xcbHrskeZ9hXD9OhrhmOhzRlkpPxea9xQK\nAYKJ\r\n=Yf7x\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.10.0"}}, "4.18.0": {"name": "express", "version": "4.18.0", "dependencies": {"qs": "6.10.3", "depd": "2.0.0", "etag": "~1.8.1", "send": "0.18.0", "vary": "~1.1.2", "debug": "2.6.9", "fresh": "0.5.2", "cookie": "0.5.0", "accepts": "~1.3.8", "methods": "~1.1.2", "type-is": "~1.6.18", "parseurl": "~1.3.3", "statuses": "2.0.1", "encodeurl": "~1.0.2", "proxy-addr": "~2.0.7", "body-parser": "1.20.0", "escape-html": "~1.0.3", "http-errors": "2.0.0", "on-finished": "2.4.1", "safe-buffer": "5.2.1", "utils-merge": "1.0.1", "content-type": "~1.0.4", "finalhandler": "1.2.0", "range-parser": "~1.2.1", "serve-static": "1.15.0", "array-flatten": "1.1.1", "path-to-regexp": "0.1.7", "setprototypeof": "1.2.0", "cookie-signature": "1.0.6", "merge-descriptors": "1.0.1", "content-disposition": "0.5.4"}, "devDependencies": {"ejs": "3.1.6", "hbs": "4.2.0", "nyc": "15.1.0", "after": "0.8.2", "mocha": "9.2.1", "vhost": "~3.0.2", "eslint": "7.32.0", "marked": "0.7.0", "morgan": "1.10.0", "supertest": "6.2.2", "multiparty": "4.2.3", "connect-redis": "3.4.2", "cookie-parser": "1.4.6", "cookie-session": "2.0.0", "express-session": "1.17.2", "method-override": "3.0.0", "pbkdf2-password": "1.2.1"}, "dist": {"shasum": "7a426773325d0dd5406395220614c0db10b6e8e2", "tarball": "https://registry.npmjs.org/express/-/express-4.18.0.tgz", "fileCount": 16, "integrity": "sha512-EJEXxiTQJS3lIPrU1AE2vRuT7X7E+0KBbpm5GSoK524yl0K8X+er8zS2P14E64eqsVNoWbMCT7MpmQ+ErAhgRg==", "signatures": [{"sig": "MEYCIQCG4z+BvdpxyYYBRK5s0B+b0EjLDHXsfK2t2eppSSj4QAIhAO4NWKhv3X+GA2iX5Cwuj8Y0Vg5oFQPL6xKqB2ndWKNc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 213532, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZvLUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq8Yg/+ID81QIsO2JQNJgvDRhNjoyY75xRyKzvFggw6HQIIKUwbLBUJ\r\nIvfF8n0S8HAxDS3c9M1i5Zy87Wfk7/thsT3H1YYRRcDSd0kni8/VFNtHaOmt\r\nlPO17D11ib8170zgBnYcvmgDZUK0BsxxpAjvL4BihFT2gYvb+WTE1lNf9cWg\r\nD3j3G3tAK1f/0EklGEVsBhZ/qw9ZEw/zR5xYdxBMi2ci9AE+HTpQVjTJ0WsJ\r\nHeJHmoXg2FQv7wvfvJIXfsghtwxtMB49OHZzGw+6qK28N9xX2+m1fwqfYcqA\r\n0sWmru9yAnUjMT+gqvTdXH28RnYJn1Kygvaz5hm8KwX2lTRp5e5l1KJjzHqI\r\nHIzsMtHIdntad1D+p/0l/pK1kPuzyJ/wRuNCIFjBVYEJPQs3+srsn1BF8dFX\r\ny5oWYJftLjyzIoVcVx6CvZpGstVGBEjSUIsAMNACoESc02XfIGu49UNuHP/c\r\n/Qtgg1rxyYtAWq+8B1/4y+KG1bHqxs7/QfCb8AZUouKVoL80aflpb6nc4DCv\r\nNOcuQvJpqgrLJ0dtT6zNv6RghF5xvoPXLvf80lGcPwe22wASjWsExuOTF092\r\nbkE+AHLbWf8/BGeBK7aGwLzibZsRjtIYN0DkHMn6IbUlZN8SsojGPCvy+cij\r\nWs6MH4k8R2o57SXXzV3Sk/34v8LJoAVQv/U=\r\n=J/fz\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.10.0"}}, "4.18.1": {"name": "express", "version": "4.18.1", "dependencies": {"qs": "6.10.3", "depd": "2.0.0", "etag": "~1.8.1", "send": "0.18.0", "vary": "~1.1.2", "debug": "2.6.9", "fresh": "0.5.2", "cookie": "0.5.0", "accepts": "~1.3.8", "methods": "~1.1.2", "type-is": "~1.6.18", "parseurl": "~1.3.3", "statuses": "2.0.1", "encodeurl": "~1.0.2", "proxy-addr": "~2.0.7", "body-parser": "1.20.0", "escape-html": "~1.0.3", "http-errors": "2.0.0", "on-finished": "2.4.1", "safe-buffer": "5.2.1", "utils-merge": "1.0.1", "content-type": "~1.0.4", "finalhandler": "1.2.0", "range-parser": "~1.2.1", "serve-static": "1.15.0", "array-flatten": "1.1.1", "path-to-regexp": "0.1.7", "setprototypeof": "1.2.0", "cookie-signature": "1.0.6", "merge-descriptors": "1.0.1", "content-disposition": "0.5.4"}, "devDependencies": {"ejs": "3.1.7", "hbs": "4.2.0", "nyc": "15.1.0", "after": "0.8.2", "mocha": "9.2.2", "vhost": "~3.0.2", "eslint": "7.32.0", "marked": "0.7.0", "morgan": "1.10.0", "supertest": "6.2.3", "multiparty": "4.2.3", "connect-redis": "3.4.2", "cookie-parser": "1.4.6", "cookie-session": "2.0.0", "express-session": "1.17.2", "method-override": "3.0.0", "pbkdf2-password": "1.2.1"}, "dist": {"shasum": "7797de8b9c72c857b9cd0e14a5eea80666267caf", "tarball": "https://registry.npmjs.org/express/-/express-4.18.1.tgz", "fileCount": 16, "integrity": "sha512-zZBcOX9TfehHQhtupq57OF8lFZ3UZi08Y97dwFCkD8p9d/d2Y3M+ykKcwaMDEL+4qyUolgBDX6AblpR3fL212Q==", "signatures": [{"sig": "MEUCIQCkQvOH47SIBWy00awo1JTFX0klR2Z/gFD1XQLJSieioQIgecss7yNkwudVtiYtSwaXu4f1pcl7ldy70OgkXpAPcnU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 213619, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJibD2UACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo5Pw//VEM3BtbWJw45FYCrAu3cETYrwBsndrbd1mKHas6kvU4QEzVN\r\naIjNfLjXWFXf+lrfsTnCopzA1Se2RiSrXEHdKOzBUjr7fB+zIJJrz4uVkJT7\r\nQ4L8yR/9wkpj9y7vAPZuWBLAZB8nxc35mO8JlYpnlr4DFDDcwWnJQeovKGtB\r\nq6AZaXnA/t4TDl1nU8CSpTuYrsTGkQns/Ddv9YNBUtKOtoAcULdVzH8M3Oa8\r\nsHNZDuwvWOatUPmeiY74y1rDxU2yJu3ubHosarJFRQhb6/TrM5Ai0TL/cA4I\r\nSd/WVmJY+80yy0nAznQHfB/a3UqWBYmGlRSD+tkgJ9oA40JfM9rsqVZe9vTa\r\ngQOWm6wXq4oK1jryaTCgaNzNMijQQQnGNXQZ2T2HrxvVa20dXiaoVe17Das/\r\nCfW3v6ZlSYPeh77LbEHHkCLvKesOsphm7PJGMewM0ARxipiChiLQLe4HKG8e\r\nQwPAx2uyglNFebyEW/Bd/N2IF486Y0NO130vw2URGuD3HIpah0yIaMYTJVq6\r\nsIPfvNjs6/H/y3WNpjupn+f0dYuSuVD+hH0Q5KqWkt5p+VIyNxnOCVUR4Sf3\r\nqhlQxK3LR3BO/HzgtRAUoJ8Wn1kSadnq/vrHnIOYBvr4+kTg2ETrSMP8oL6V\r\n94Bsqu5CKFnNfz+ZJFhrhWi5paqktyKSVV4=\r\n=/LCr\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.10.0"}}, "4.18.2": {"name": "express", "version": "4.18.2", "dependencies": {"qs": "6.11.0", "depd": "2.0.0", "etag": "~1.8.1", "send": "0.18.0", "vary": "~1.1.2", "debug": "2.6.9", "fresh": "0.5.2", "cookie": "0.5.0", "accepts": "~1.3.8", "methods": "~1.1.2", "type-is": "~1.6.18", "parseurl": "~1.3.3", "statuses": "2.0.1", "encodeurl": "~1.0.2", "proxy-addr": "~2.0.7", "body-parser": "1.20.1", "escape-html": "~1.0.3", "http-errors": "2.0.0", "on-finished": "2.4.1", "safe-buffer": "5.2.1", "utils-merge": "1.0.1", "content-type": "~1.0.4", "finalhandler": "1.2.0", "range-parser": "~1.2.1", "serve-static": "1.15.0", "array-flatten": "1.1.1", "path-to-regexp": "0.1.7", "setprototypeof": "1.2.0", "cookie-signature": "1.0.6", "merge-descriptors": "1.0.1", "content-disposition": "0.5.4"}, "devDependencies": {"ejs": "3.1.8", "hbs": "4.2.0", "nyc": "15.1.0", "after": "0.8.2", "mocha": "10.0.0", "vhost": "~3.0.2", "eslint": "8.24.0", "marked": "0.7.0", "morgan": "1.10.0", "supertest": "6.3.0", "multiparty": "4.2.3", "connect-redis": "3.4.2", "cookie-parser": "1.4.6", "cookie-session": "2.0.0", "express-session": "1.17.2", "method-override": "3.0.0", "pbkdf2-password": "1.2.1"}, "dist": {"shasum": "3fabe08296e930c796c19e3c516979386ba9fd59", "tarball": "https://registry.npmjs.org/express/-/express-4.18.2.tgz", "fileCount": 16, "integrity": "sha512-5/PsL6iGPdfQ/lKM1UuielYgv3BUoJfz1aUwU9vHZ+J7gyvwdQXFEBIEIaxeGf0GIcreATNyBExtalisDbuMqQ==", "signatures": [{"sig": "MEUCIFOwNzq9IZDBTh/rqUj9ymFeX4N4vva8HcKJmWtA1rJGAiEAunyw7UDFI3azDrLOebNqnmREemjsgMZuXlqyu6Zpsa0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 213863, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQdooACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmroIA/9HlIJRLB+eEMFhlmlbotRbu6wKAsWi8AIdh3f6f8UTtsyQCPb\r\n7ae4QtFHv4eCIla9mOyoPlzJkLR7TsMhdIVxy4uTnokd9M5xm4crJpqt84ru\r\n24SjwnAvajzdgDaCw5CAZwUN2naPQuV4nn5TjUI7a2mIjVlLXmwbbdNqP4lH\r\nHCFfk7jUCeA4DtlSZHexN1QSoJrmQH1+xnO4UViBSVQMF/ZAtNQktjUV0T7D\r\nDWHyQbW4EtQvyxvfi7be+0EyEoXgfzH8SrU35OZhDgQCf8nPeR4k8KetwhUJ\r\nqIX1utH28XoN05+a18I8bPi+r/oD0lTRuSGS3SejVGUof6UJZ5DwDNr2bhq8\r\nW8bpeL2nQPeIqAuxVl6ZDoyxfKjDEo2DVUuiCcbxajxUsSuVD35MrJ/HJvDI\r\nfFLqurVK23Vjs7m6RjjUbUzs2FbwNWia8M6x1V2Co1r0+wB48LCYSaEhpHGb\r\nZQ20H9YCBz67Q2+lWs2j++XjATKKgZBq0/IfGvCIK98w9BXsL+5rZChIsFqE\r\nh9eqac3F0pGfwormci0fTOdvqkweCsZ8etJabuYGhewTNt2gj8cJVAJEkyU1\r\nndPtD6qCSqsPmta3zo9QVDJkjlvInZKiis3plJSgla0RaU6tn5ZS8qhqPqU1\r\nM0WRIWbochWXnuHxzlmXF5pJqHv/C8BT8SI=\r\n=do9F\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.10.0"}}, "4.18.3": {"name": "express", "version": "4.18.3", "dependencies": {"qs": "6.11.0", "depd": "2.0.0", "etag": "~1.8.1", "send": "0.18.0", "vary": "~1.1.2", "debug": "2.6.9", "fresh": "0.5.2", "cookie": "0.5.0", "accepts": "~1.3.8", "methods": "~1.1.2", "type-is": "~1.6.18", "parseurl": "~1.3.3", "statuses": "2.0.1", "encodeurl": "~1.0.2", "proxy-addr": "~2.0.7", "body-parser": "1.20.2", "escape-html": "~1.0.3", "http-errors": "2.0.0", "on-finished": "2.4.1", "safe-buffer": "5.2.1", "utils-merge": "1.0.1", "content-type": "~1.0.4", "finalhandler": "1.2.0", "range-parser": "~1.2.1", "serve-static": "1.15.0", "array-flatten": "1.1.1", "path-to-regexp": "0.1.7", "setprototypeof": "1.2.0", "cookie-signature": "1.0.6", "merge-descriptors": "1.0.1", "content-disposition": "0.5.4"}, "devDependencies": {"ejs": "3.1.9", "hbs": "4.2.0", "nyc": "15.1.0", "after": "0.8.2", "mocha": "10.2.0", "vhost": "~3.0.2", "eslint": "8.47.0", "marked": "0.7.0", "morgan": "1.10.0", "supertest": "6.3.0", "connect-redis": "3.4.2", "cookie-parser": "1.4.6", "cookie-session": "2.0.0", "express-session": "1.17.2", "method-override": "3.0.0", "pbkdf2-password": "1.2.1"}, "dist": {"shasum": "6870746f3ff904dee1819b82e4b51509afffb0d4", "tarball": "https://registry.npmjs.org/express/-/express-4.18.3.tgz", "fileCount": 16, "integrity": "sha512-6VyCijWQ+9O7WuVMTRBTl+cjNNIzD5cY5mQ1WM8r/LEkI2u8EYpOotESNwzNlyCn3g+dmjKYI6BmNneSr/FSRw==", "signatures": [{"sig": "MEQCIGiI9F3bXYjNfAgEVF5uPBljfiqsGAWjOUuDjyXh7nw9AiBC92pgAVTQ7i5Adb2FWEyvlhfRq+HWJxeoVvAQhWIUPQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 214068}, "engines": {"node": ">= 0.10.0"}}, "4.19.0": {"name": "express", "version": "4.19.0", "dependencies": {"qs": "6.11.0", "depd": "2.0.0", "etag": "~1.8.1", "send": "0.18.0", "vary": "~1.1.2", "debug": "2.6.9", "fresh": "0.5.2", "cookie": "0.6.0", "accepts": "~1.3.8", "methods": "~1.1.2", "type-is": "~1.6.18", "parseurl": "~1.3.3", "statuses": "2.0.1", "encodeurl": "~1.0.2", "proxy-addr": "~2.0.7", "body-parser": "1.20.2", "escape-html": "~1.0.3", "http-errors": "2.0.0", "on-finished": "2.4.1", "safe-buffer": "5.2.1", "utils-merge": "1.0.1", "content-type": "~1.0.4", "finalhandler": "1.2.0", "range-parser": "~1.2.1", "serve-static": "1.15.0", "array-flatten": "1.1.1", "path-to-regexp": "0.1.7", "setprototypeof": "1.2.0", "cookie-signature": "1.0.6", "merge-descriptors": "1.0.1", "content-disposition": "0.5.4"}, "devDependencies": {"ejs": "3.1.9", "hbs": "4.2.0", "nyc": "15.1.0", "after": "0.8.2", "mocha": "10.2.0", "vhost": "~3.0.2", "eslint": "8.47.0", "marked": "0.7.0", "morgan": "1.10.0", "supertest": "6.3.0", "connect-redis": "3.4.2", "cookie-parser": "1.4.6", "cookie-session": "2.0.0", "express-session": "1.17.2", "method-override": "3.0.0", "pbkdf2-password": "1.2.1"}, "dist": {"shasum": "c9f689a62522f3399132d49eacd9af177d8ccb9e", "tarball": "https://registry.npmjs.org/express/-/express-4.19.0.tgz", "fileCount": 16, "integrity": "sha512-/ERliX0l7UuHEgAy7HU2FRsiz3ScIKNl/iwnoYzHTJC0Sqj3ctWDD3MQ9CbUEfjshvxXImWaeukD0Xo7a2lWLA==", "signatures": [{"sig": "MEUCIQDaPYTFTYdwtMpPsow/w0l6knlit8MpXaYuUTLCYZz78AIgCc61nyRBxhgU3wlQ8VYqEreQ4fLKN8xSdJPlNIjDsNQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 214897}, "engines": {"node": ">= 0.10.0"}}, "4.19.1": {"name": "express", "version": "4.19.1", "dependencies": {"qs": "6.11.0", "depd": "2.0.0", "etag": "~1.8.1", "send": "0.18.0", "vary": "~1.1.2", "debug": "2.6.9", "fresh": "0.5.2", "cookie": "0.6.0", "accepts": "~1.3.8", "methods": "~1.1.2", "type-is": "~1.6.18", "parseurl": "~1.3.3", "statuses": "2.0.1", "encodeurl": "~1.0.2", "proxy-addr": "~2.0.7", "body-parser": "1.20.2", "escape-html": "~1.0.3", "http-errors": "2.0.0", "on-finished": "2.4.1", "safe-buffer": "5.2.1", "utils-merge": "1.0.1", "content-type": "~1.0.4", "finalhandler": "1.2.0", "range-parser": "~1.2.1", "serve-static": "1.15.0", "array-flatten": "1.1.1", "path-to-regexp": "0.1.7", "setprototypeof": "1.2.0", "cookie-signature": "1.0.6", "merge-descriptors": "1.0.1", "content-disposition": "0.5.4"}, "devDependencies": {"ejs": "3.1.9", "hbs": "4.2.0", "nyc": "15.1.0", "after": "0.8.2", "mocha": "10.2.0", "vhost": "~3.0.2", "eslint": "8.47.0", "marked": "0.7.0", "morgan": "1.10.0", "supertest": "6.3.0", "connect-redis": "3.4.2", "cookie-parser": "1.4.6", "cookie-session": "2.0.0", "express-session": "1.17.2", "method-override": "3.0.0", "pbkdf2-password": "1.2.1"}, "dist": {"shasum": "4700635795e911600a45596138cf5b0320e78256", "tarball": "https://registry.npmjs.org/express/-/express-4.19.1.tgz", "fileCount": 16, "integrity": "sha512-K4w1/Bp7y8iSiVObmCrtq8Cs79XjJc/RU2YYkZQ7wpUu5ZyZ7MtPHkqoMz4pf+mgXfNvo2qft8D9OnrH2ABk9w==", "signatures": [{"sig": "MEUCIQChG6ebSVHguOn5VxRV7JTKMAKco6TCKsK7N6+JxidFbQIgMz0HPASjtd4JCQ/H1cfsQwye2+5hsgl76yxHcrR/Nfc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 215018}, "engines": {"node": ">= 0.10.0"}}, "5.0.0-beta.2": {"name": "express", "version": "5.0.0-beta.2", "dependencies": {"qs": "6.11.0", "depd": "2.0.0", "etag": "~1.8.1", "send": "1.0.0-beta.2", "vary": "~1.1.2", "debug": "3.1.0", "fresh": "0.5.2", "cookie": "0.6.0", "router": "2.0.0-beta.2", "accepts": "~1.3.8", "methods": "~1.1.2", "type-is": "~1.6.18", "parseurl": "~1.3.3", "statuses": "2.0.1", "encodeurl": "~1.0.2", "mime-types": "~2.1.34", "proxy-addr": "~2.0.7", "body-parser": "2.0.0-beta.2", "escape-html": "~1.0.3", "http-errors": "2.0.0", "on-finished": "2.4.1", "safe-buffer": "5.2.1", "utils-merge": "1.0.1", "content-type": "~1.0.4", "finalhandler": "1.2.0", "range-parser": "~1.2.1", "serve-static": "2.0.0-beta.2", "array-flatten": "3.0.0", "setprototypeof": "1.2.0", "cookie-signature": "1.0.6", "path-is-absolute": "1.0.1", "merge-descriptors": "1.0.1", "content-disposition": "0.5.4"}, "devDependencies": {"ejs": "3.1.9", "hbs": "4.2.0", "nyc": "15.1.0", "after": "0.8.2", "mocha": "10.2.0", "vhost": "~3.0.2", "eslint": "8.47.0", "marked": "0.7.0", "morgan": "1.10.0", "supertest": "6.3.0", "connect-redis": "3.4.2", "cookie-parser": "1.4.6", "cookie-session": "2.0.0", "express-session": "1.17.2", "method-override": "3.0.0", "pbkdf2-password": "1.2.1"}, "dist": {"shasum": "cf1714a16d08b8d5f170a314e19c3669375dcc13", "tarball": "https://registry.npmjs.org/express/-/express-5.0.0-beta.2.tgz", "fileCount": 11, "integrity": "sha512-qb6eRKeXBBzz0j1eiXnjkNvTo9sEbzEVhX712Nj0WfYlGUAkyYydhhw44aQto6ribVT4drRhHxKneQCzxYy+ow==", "signatures": [{"sig": "MEQCIDCdm5ZS7A158RNSEpqEQscvgqNo49W+ViTNeCA6rytpAiB5nqKQPoxTolbmu99lAmhUFfKfO6mO5nbHBMANdwGNrQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 189412}, "engines": {"node": ">= 4"}}, "4.19.2": {"name": "express", "version": "4.19.2", "dependencies": {"qs": "6.11.0", "depd": "2.0.0", "etag": "~1.8.1", "send": "0.18.0", "vary": "~1.1.2", "debug": "2.6.9", "fresh": "0.5.2", "cookie": "0.6.0", "accepts": "~1.3.8", "methods": "~1.1.2", "type-is": "~1.6.18", "parseurl": "~1.3.3", "statuses": "2.0.1", "encodeurl": "~1.0.2", "proxy-addr": "~2.0.7", "body-parser": "1.20.2", "escape-html": "~1.0.3", "http-errors": "2.0.0", "on-finished": "2.4.1", "safe-buffer": "5.2.1", "utils-merge": "1.0.1", "content-type": "~1.0.4", "finalhandler": "1.2.0", "range-parser": "~1.2.1", "serve-static": "1.15.0", "array-flatten": "1.1.1", "path-to-regexp": "0.1.7", "setprototypeof": "1.2.0", "cookie-signature": "1.0.6", "merge-descriptors": "1.0.1", "content-disposition": "0.5.4"}, "devDependencies": {"ejs": "3.1.9", "hbs": "4.2.0", "nyc": "15.1.0", "after": "0.8.2", "mocha": "10.2.0", "vhost": "~3.0.2", "eslint": "8.47.0", "marked": "0.7.0", "morgan": "1.10.0", "supertest": "6.3.0", "connect-redis": "3.4.2", "cookie-parser": "1.4.6", "cookie-session": "2.0.0", "express-session": "1.17.2", "method-override": "3.0.0", "pbkdf2-password": "1.2.1"}, "dist": {"shasum": "e25437827a3aa7f2a827bc8171bbbb664a356465", "tarball": "https://registry.npmjs.org/express/-/express-4.19.2.tgz", "fileCount": 16, "integrity": "sha512-5T6nhjsT+EOMzuck8JjBHARTHfMht0POzlA60WV2pMD3gyXw2LZnZ+ueGdNxG+0calOJcWKbpFcuzLZ91YWq9Q==", "signatures": [{"sig": "MEQCIAiBinxjUjCsVue+c1HIbRF/B9ESiPYkTNWw33BKTcwaAiBPzoW68VZcyXGrFli4cw9qFjpL7WgAz0jMjTq0n0GpXQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 214763}, "engines": {"node": ">= 0.10.0"}}, "5.0.0-beta.3": {"name": "express", "version": "5.0.0-beta.3", "dependencies": {"qs": "6.11.0", "depd": "2.0.0", "etag": "~1.8.1", "send": "1.0.0-beta.2", "vary": "~1.1.2", "debug": "3.1.0", "fresh": "0.5.2", "cookie": "0.6.0", "router": "2.0.0-beta.2", "accepts": "~1.3.8", "methods": "~1.1.2", "type-is": "~1.6.18", "parseurl": "~1.3.3", "statuses": "2.0.1", "encodeurl": "~1.0.2", "mime-types": "~2.1.34", "proxy-addr": "~2.0.7", "body-parser": "2.0.0-beta.2", "escape-html": "~1.0.3", "http-errors": "2.0.0", "on-finished": "2.4.1", "safe-buffer": "5.2.1", "utils-merge": "1.0.1", "content-type": "~1.0.4", "finalhandler": "1.2.0", "range-parser": "~1.2.1", "serve-static": "2.0.0-beta.2", "array-flatten": "3.0.0", "setprototypeof": "1.2.0", "cookie-signature": "1.0.6", "path-is-absolute": "1.0.1", "merge-descriptors": "1.0.1", "content-disposition": "0.5.4"}, "devDependencies": {"ejs": "3.1.9", "hbs": "4.2.0", "nyc": "15.1.0", "after": "0.8.2", "mocha": "10.2.0", "vhost": "~3.0.2", "eslint": "8.47.0", "marked": "0.7.0", "morgan": "1.10.0", "supertest": "6.3.0", "connect-redis": "3.4.2", "cookie-parser": "1.4.6", "cookie-session": "2.0.0", "express-session": "1.17.2", "method-override": "3.0.0", "pbkdf2-password": "1.2.1"}, "dist": {"shasum": "be38300eaafadee3de6ce756aaa34f52c0472da6", "tarball": "https://registry.npmjs.org/express/-/express-5.0.0-beta.3.tgz", "fileCount": 11, "integrity": "sha512-e7Qizw4gMBVe1Ky2oNi5C1h6oS8aWDcY2yYxvRMy5aMc6t2aqobuHpQRfR3LRC9NAW/c6081SeGWMGBorLXePg==", "signatures": [{"sig": "MEYCIQD4f/Y4yQdrkMfBnpsDr11VKpmbgvCF/EHTnUg1gNM5nwIhAJoFWdoGlVZibdWDUXunTODONw6fDLHpY3LXw7l4Iydb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 189311}, "engines": {"node": ">= 4"}}, "4.20.0": {"name": "express", "version": "4.20.0", "dependencies": {"qs": "6.11.0", "depd": "2.0.0", "etag": "~1.8.1", "send": "0.19.0", "vary": "~1.1.2", "debug": "2.6.9", "fresh": "0.5.2", "cookie": "0.6.0", "accepts": "~1.3.8", "methods": "~1.1.2", "type-is": "~1.6.18", "parseurl": "~1.3.3", "statuses": "2.0.1", "encodeurl": "~2.0.0", "proxy-addr": "~2.0.7", "body-parser": "1.20.3", "escape-html": "~1.0.3", "http-errors": "2.0.0", "on-finished": "2.4.1", "safe-buffer": "5.2.1", "utils-merge": "1.0.1", "content-type": "~1.0.4", "finalhandler": "1.2.0", "range-parser": "~1.2.1", "serve-static": "1.16.0", "array-flatten": "1.1.1", "path-to-regexp": "0.1.10", "setprototypeof": "1.2.0", "cookie-signature": "1.0.6", "merge-descriptors": "1.0.3", "content-disposition": "0.5.4"}, "devDependencies": {"ejs": "3.1.9", "hbs": "4.2.0", "nyc": "15.1.0", "after": "0.8.2", "mocha": "10.2.0", "vhost": "~3.0.2", "eslint": "8.47.0", "marked": "0.7.0", "morgan": "1.10.0", "supertest": "6.3.0", "connect-redis": "3.4.2", "cookie-parser": "1.4.6", "cookie-session": "2.0.0", "express-session": "1.17.2", "method-override": "3.0.0", "pbkdf2-password": "1.2.1"}, "dist": {"shasum": "f1d08e591fcec770c07be4767af8eb9bcfd67c48", "tarball": "https://registry.npmjs.org/express/-/express-4.20.0.tgz", "fileCount": 16, "integrity": "sha512-pLdae7I6QqShF5PnNTCVn4hI91Dx0Grkn2+IAsMTgMIKuQVte2dN9PeGSSAME2FR8anOhVA62QDIUaWVfEXVLw==", "signatures": [{"sig": "MEMCHzoFziE9EIL7mQTWYNRSC8OsFYK7KgxetnT7Gb1ycosCIBvXLtOV/D5A5TUhfagBQw1ggBmJDY8ldwTvUmafBI28", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 220461}, "engines": {"node": ">= 0.10.0"}}, "5.0.0": {"name": "express", "version": "5.0.0", "dependencies": {"qs": "6.13.0", "depd": "2.0.0", "etag": "~1.8.1", "once": "1.4.0", "send": "^1.1.0", "vary": "~1.1.2", "debug": "4.3.6", "fresh": "2.0.0", "cookie": "0.6.0", "router": "^2.0.0", "accepts": "^2.0.0", "methods": "~1.1.2", "type-is": "^2.0.0", "parseurl": "~1.3.3", "statuses": "2.0.1", "encodeurl": "~2.0.0", "mime-types": "^3.0.0", "proxy-addr": "~2.0.7", "body-parser": "^2.0.1", "escape-html": "~1.0.3", "http-errors": "2.0.0", "on-finished": "2.4.1", "safe-buffer": "5.2.1", "utils-merge": "1.0.1", "content-type": "~1.0.4", "finalhandler": "^2.0.0", "range-parser": "~1.2.1", "serve-static": "^2.1.0", "setprototypeof": "1.2.0", "cookie-signature": "^1.2.1", "merge-descriptors": "^2.0.0", "content-disposition": "^1.0.0"}, "devDependencies": {"ejs": "3.1.9", "hbs": "4.2.0", "nyc": "15.1.0", "after": "0.8.2", "mocha": "10.2.0", "vhost": "~3.0.2", "eslint": "8.47.0", "marked": "0.7.0", "morgan": "1.10.0", "supertest": "6.3.0", "connect-redis": "3.4.2", "cookie-parser": "1.4.6", "cookie-session": "2.0.0", "express-session": "1.17.2", "method-override": "3.0.0", "pbkdf2-password": "1.2.1"}, "dist": {"shasum": "744f9ec86025a01aeca99e4300aa4fc050d493c7", "tarball": "https://registry.npmjs.org/express/-/express-5.0.0.tgz", "fileCount": 11, "integrity": "sha512-V4UkHQc+B7ldh1YC84HCXHwf60M4BOMvp9rkvTUWCK5apqDC1Esnbid4wm6nFyVuDy8XMfETsJw5lsIGBWyo0A==", "signatures": [{"sig": "MEYCIQDih7freOEykO/qdfDA8mENZ2xhkdyGUe6k7UAVB+bY+QIhAIY5B1EOcfEs5uZmpTCyCiTzoqj8BA4sLqX+sy12TcWa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 195793}, "engines": {"node": ">= 18"}}, "4.21.0": {"name": "express", "version": "4.21.0", "dependencies": {"qs": "6.13.0", "depd": "2.0.0", "etag": "~1.8.1", "send": "0.19.0", "vary": "~1.1.2", "debug": "2.6.9", "fresh": "0.5.2", "cookie": "0.6.0", "accepts": "~1.3.8", "methods": "~1.1.2", "type-is": "~1.6.18", "parseurl": "~1.3.3", "statuses": "2.0.1", "encodeurl": "~2.0.0", "proxy-addr": "~2.0.7", "body-parser": "1.20.3", "escape-html": "~1.0.3", "http-errors": "2.0.0", "on-finished": "2.4.1", "safe-buffer": "5.2.1", "utils-merge": "1.0.1", "content-type": "~1.0.4", "finalhandler": "1.3.1", "range-parser": "~1.2.1", "serve-static": "1.16.2", "array-flatten": "1.1.1", "path-to-regexp": "0.1.10", "setprototypeof": "1.2.0", "cookie-signature": "1.0.6", "merge-descriptors": "1.0.3", "content-disposition": "0.5.4"}, "devDependencies": {"ejs": "3.1.9", "hbs": "4.2.0", "nyc": "15.1.0", "after": "0.8.2", "mocha": "10.2.0", "vhost": "~3.0.2", "eslint": "8.47.0", "marked": "0.7.0", "morgan": "1.10.0", "supertest": "6.3.0", "connect-redis": "3.4.2", "cookie-parser": "1.4.6", "cookie-session": "2.0.0", "express-session": "1.17.2", "method-override": "3.0.0", "pbkdf2-password": "1.2.1"}, "dist": {"shasum": "d57cb706d49623d4ac27833f1cbc466b668eb915", "tarball": "https://registry.npmjs.org/express/-/express-4.21.0.tgz", "fileCount": 16, "integrity": "sha512-VqcNGcj/Id5ZT1LZ/cfihi3ttTn+NJmkli2eZADigjq29qTlWi/hAQ43t/VLPq8+UX06FCEx3ByOYet6ZFblng==", "signatures": [{"sig": "MEUCIQCgJ0W7MRP8kttoxkyEDAp5Ot9QPnB9qvR+AnX0Xbkb2gIgOeIWYqoU34Qu7xnq1dUpvvYbBSXHARbfkhyuoKtc6oA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 220827}, "engines": {"node": ">= 0.10.0"}}, "4.21.1": {"name": "express", "version": "4.21.1", "dependencies": {"qs": "6.13.0", "depd": "2.0.0", "etag": "~1.8.1", "send": "0.19.0", "vary": "~1.1.2", "debug": "2.6.9", "fresh": "0.5.2", "cookie": "0.7.1", "accepts": "~1.3.8", "methods": "~1.1.2", "type-is": "~1.6.18", "parseurl": "~1.3.3", "statuses": "2.0.1", "encodeurl": "~2.0.0", "proxy-addr": "~2.0.7", "body-parser": "1.20.3", "escape-html": "~1.0.3", "http-errors": "2.0.0", "on-finished": "2.4.1", "safe-buffer": "5.2.1", "utils-merge": "1.0.1", "content-type": "~1.0.4", "finalhandler": "1.3.1", "range-parser": "~1.2.1", "serve-static": "1.16.2", "array-flatten": "1.1.1", "path-to-regexp": "0.1.10", "setprototypeof": "1.2.0", "cookie-signature": "1.0.6", "merge-descriptors": "1.0.3", "content-disposition": "0.5.4"}, "devDependencies": {"ejs": "3.1.9", "hbs": "4.2.0", "nyc": "15.1.0", "after": "0.8.2", "mocha": "10.2.0", "vhost": "~3.0.2", "eslint": "8.47.0", "marked": "0.7.0", "morgan": "1.10.0", "supertest": "6.3.0", "connect-redis": "3.4.2", "cookie-parser": "1.4.6", "cookie-session": "2.0.0", "express-session": "1.17.2", "method-override": "3.0.0", "pbkdf2-password": "1.2.1"}, "dist": {"shasum": "9dae5dda832f16b4eec941a4e44aa89ec481b281", "tarball": "https://registry.npmjs.org/express/-/express-4.21.1.tgz", "fileCount": 16, "integrity": "sha512-YSFlK1Ee0/GC8QaO91tHcDxJiE/X4FbpAyQWkxAvG6AXCuR65YzK8ua6D9hvi/TzUfZMpc+BwuM1IPw8fmQBiQ==", "signatures": [{"sig": "MEQCIHNnN5rxPKTPXLR4YJQ8fxwwZZC/leP40NOKCSYlK5wPAiAP9wQBrXie0ClGWFBN95G8L8kp0qwEwzbJw1I2tSM26g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 220949}, "engines": {"node": ">= 0.10.0"}}, "5.0.1": {"name": "express", "version": "5.0.1", "dependencies": {"qs": "6.13.0", "depd": "2.0.0", "etag": "~1.8.1", "once": "1.4.0", "send": "^1.1.0", "vary": "~1.1.2", "debug": "4.3.6", "fresh": "2.0.0", "cookie": "0.7.1", "router": "^2.0.0", "accepts": "^2.0.0", "methods": "~1.1.2", "type-is": "^2.0.0", "parseurl": "~1.3.3", "statuses": "2.0.1", "encodeurl": "~2.0.0", "mime-types": "^3.0.0", "proxy-addr": "~2.0.7", "body-parser": "^2.0.1", "escape-html": "~1.0.3", "http-errors": "2.0.0", "on-finished": "2.4.1", "safe-buffer": "5.2.1", "utils-merge": "1.0.1", "content-type": "~1.0.4", "finalhandler": "^2.0.0", "range-parser": "~1.2.1", "serve-static": "^2.1.0", "setprototypeof": "1.2.0", "cookie-signature": "^1.2.1", "merge-descriptors": "^2.0.0", "content-disposition": "^1.0.0"}, "devDependencies": {"ejs": "3.1.9", "hbs": "4.2.0", "nyc": "15.1.0", "after": "0.8.2", "mocha": "10.2.0", "vhost": "~3.0.2", "eslint": "8.47.0", "marked": "0.7.0", "morgan": "1.10.0", "supertest": "6.3.0", "connect-redis": "3.4.2", "cookie-parser": "1.4.6", "cookie-session": "2.0.0", "express-session": "1.17.2", "method-override": "3.0.0", "pbkdf2-password": "1.2.1"}, "dist": {"shasum": "5d359a2550655be33124ecbc7400cd38436457e9", "tarball": "https://registry.npmjs.org/express/-/express-5.0.1.tgz", "fileCount": 11, "integrity": "sha512-ORF7g6qGnD+YtUG9yx4DFoqCShNMmUKiXuT5oWMHiOvt/4WFbHC6yCwQMTSBMno7AqntNCAzzcnnjowRkTL9eQ==", "signatures": [{"sig": "MEUCIFXFNz3TffVJstjlibgtYQchq4aV5ajNDpGqAY+SJSmOAiEA9V5OUQfFnDAP+HlOsWQbQY923DnplzOpTXABzpeDj14=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 195997}, "engines": {"node": ">= 18"}}, "4.21.2": {"name": "express", "version": "4.21.2", "dependencies": {"qs": "6.13.0", "depd": "2.0.0", "etag": "~1.8.1", "send": "0.19.0", "vary": "~1.1.2", "debug": "2.6.9", "fresh": "0.5.2", "cookie": "0.7.1", "accepts": "~1.3.8", "methods": "~1.1.2", "type-is": "~1.6.18", "parseurl": "~1.3.3", "statuses": "2.0.1", "encodeurl": "~2.0.0", "proxy-addr": "~2.0.7", "body-parser": "1.20.3", "escape-html": "~1.0.3", "http-errors": "2.0.0", "on-finished": "2.4.1", "safe-buffer": "5.2.1", "utils-merge": "1.0.1", "content-type": "~1.0.4", "finalhandler": "1.3.1", "range-parser": "~1.2.1", "serve-static": "1.16.2", "array-flatten": "1.1.1", "path-to-regexp": "0.1.12", "setprototypeof": "1.2.0", "cookie-signature": "1.0.6", "merge-descriptors": "1.0.3", "content-disposition": "0.5.4"}, "devDependencies": {"ejs": "3.1.9", "hbs": "4.2.0", "nyc": "15.1.0", "after": "0.8.2", "mocha": "10.2.0", "vhost": "~3.0.2", "eslint": "8.47.0", "marked": "0.7.0", "morgan": "1.10.0", "supertest": "6.3.0", "connect-redis": "3.4.2", "cookie-parser": "1.4.6", "cookie-session": "2.0.0", "express-session": "1.17.2", "method-override": "3.0.0", "pbkdf2-password": "1.2.1"}, "dist": {"shasum": "cf250e48362174ead6cea4a566abef0162c1ec32", "tarball": "https://registry.npmjs.org/express/-/express-4.21.2.tgz", "fileCount": 16, "integrity": "sha512-28HqgMZAmih1Czt9ny7qr6ek2qddF4FclbMzwhCREB6OFfH+rXAnuNCwo1/wFvrtbgsQDb4kSbX9de9lFbrXnA==", "signatures": [{"sig": "MEUCIQDgW+EeVA3ED3nIfT7ipG0ojC1lhtT1OknpYrIK0VbOyAIgeASCxbafoIFAu0dare62N0BImlTfYMUQ4Z7MDXa0x80=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 221226}, "engines": {"node": ">= 0.10.0"}, "funding": {"url": "https://opencollective.com/express", "type": "opencollective"}}, "5.1.0": {"name": "express", "version": "5.1.0", "dependencies": {"qs": "^6.14.0", "etag": "^1.8.1", "once": "^1.4.0", "send": "^1.1.0", "vary": "^1.1.2", "debug": "^4.4.0", "fresh": "^2.0.0", "cookie": "^0.7.1", "router": "^2.2.0", "accepts": "^2.0.0", "type-is": "^2.0.1", "parseurl": "^1.3.3", "statuses": "^2.0.1", "encodeurl": "^2.0.0", "mime-types": "^3.0.0", "proxy-addr": "^2.0.7", "body-parser": "^2.2.0", "escape-html": "^1.0.3", "http-errors": "^2.0.0", "on-finished": "^2.4.1", "content-type": "^1.0.5", "finalhandler": "^2.1.0", "range-parser": "^1.2.1", "serve-static": "^2.2.0", "cookie-signature": "^1.2.1", "merge-descriptors": "^2.0.0", "content-disposition": "^1.0.0"}, "devDependencies": {"ejs": "^3.1.10", "hbs": "4.2.0", "nyc": "^17.1.0", "after": "0.8.2", "mocha": "^10.7.3", "vhost": "~3.0.2", "eslint": "8.47.0", "marked": "^15.0.3", "morgan": "1.10.0", "supertest": "^6.3.0", "connect-redis": "^8.0.1", "cookie-parser": "1.4.7", "cookie-session": "2.1.0", "express-session": "^1.18.1", "method-override": "3.0.0", "pbkdf2-password": "1.2.1"}, "dist": {"shasum": "d31beaf715a0016f0d53f47d3b4d7acf28c75cc9", "tarball": "https://registry.npmjs.org/express/-/express-5.1.0.tgz", "fileCount": 11, "integrity": "sha512-DT9ck5YIRU+8GYzzU5kT3eHGA5iL+1Zd0EutOmTE9Dtk+Tvuzd23VBU+ec7HPNSTxXYO55gPV/hq4pSBJDjFpA==", "signatures": [{"sig": "MEUCIAYGANhLhreKEaT/rA9vMs2iwa89ddVCaWXMWR3FO9kjAiEAg8L/49srcYLWhUyimf7TKgEYIhru9nYpaScPEe0uddI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 197014}, "engines": {"node": ">= 18"}, "funding": {"url": "https://opencollective.com/express", "type": "opencollective"}}}, "modified": "2025-06-14T02:53:32.178Z"}