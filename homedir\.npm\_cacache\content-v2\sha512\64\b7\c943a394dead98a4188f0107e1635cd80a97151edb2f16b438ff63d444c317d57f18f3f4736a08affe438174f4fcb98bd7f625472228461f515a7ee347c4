{"_id": "@rollup/rollup-linux-riscv64-gnu", "_rev": "107-1beb31d2518864d39390787c4d235c31", "name": "@rollup/rollup-linux-riscv64-gnu", "dist-tags": {"beta": "4.33.0-0", "latest": "4.44.0"}, "versions": {"4.7.0": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.7.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.7.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "b7ed4894b44a47f4145cce77175fe578d1379863", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.7.0.tgz", "fileCount": 3, "integrity": "sha512-rlfy5RnQG1aop1BL/gjdH42M2geMUyVQqd52GJVirqYc787A/XVvl3kQ5NG/43KXgOgE9HXgCaEH05kzQ+hLoA==", "signatures": [{"sig": "MEUCIEKIFXRo6l83x9/408Msce2xC/a54ExPMyt/CgFsGWwLAiEA4UhdaiC0riRKa1X2iFVC+ns2oVx59qtSfDbnwSUTIV0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2455126}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "098e29ca3e0643006870f9ed94710fd3004a9043", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.7.0_1702022294934_0.6906427952706169", "host": "s3://npm-registry-packages"}}, "4.8.0": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.8.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.8.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "67984f1d1f663610f4e1f6e638a2b07169562448", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.8.0.tgz", "fileCount": 3, "integrity": "sha512-BH5xIh7tOzS9yBi8dFrCTG8Z6iNIGWGltd3IpTSKp6+pNWWO6qy8eKoRxOtwFbMrid5NZaidLYN6rHh9aB8bEw==", "signatures": [{"sig": "MEQCICsKOc6J/T1ctmA7fBBKjSdtXVv61uD2FSbUQ5Ns3kpXAiBi5k2B9cQ/rXPEqMIvHwh8XpaYs63uDQW7fQJ1jlEAJA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2455126}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "62b648e1cc6a1f00260bb85aa2050097bb4afd2b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.8.0_1702275909805_0.48459486460219914", "host": "s3://npm-registry-packages"}}, "4.9.0": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.9.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.9.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "e9add70ddca7bd6f685ec447ae83eb3be552f211", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.9.0.tgz", "fileCount": 3, "integrity": "sha512-uwvOYNtLw8gVtrExKhdFsYHA/kotURUmZYlinH2VcQxNCQJeJXnkmWgw2hI9Xgzhgu7J9QvWiq9TtTVwWMDa+w==", "signatures": [{"sig": "MEUCIFu5XQ/lXLU+J8WRrct+0R1S+gNlokFKDH2vRFSzWzF2AiEA7scWAPhdDDhU1FWyCiWOZJ8nYzN5S+DpS2CF/0mJ3bw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2455126}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "c5337ef28a71c796e768a9f0edb3d7259a93f1aa", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.9.0_1702459470460_0.5953788266373206", "host": "s3://npm-registry-packages"}}, "4.9.1": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.9.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.9.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "415c0533bb752164effd05f5613858e8f6779bc9", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.9.1.tgz", "fileCount": 3, "integrity": "sha512-LmYIO65oZVfFt9t6cpYkbC4d5lKHLYv5B4CSHRpnANq0VZUQXGcCPXHzbCXCz4RQnx7jvlYB1ISVNCE/omz5cw==", "signatures": [{"sig": "MEQCIFHWEZJaYJiVmBKwc+kx8aKI+rZx5Tp6I6xG71kM5Rm6AiBaBS/hGv2DVaDPitz5mgf737yPZH/MNeAjW+SzBNiuzw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2455142}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "d56ac63dc0452820272a0d7536340277f7db68bf", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.9.1_1702794383108_0.46355093592712815", "host": "s3://npm-registry-packages"}}, "4.9.2": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.9.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.9.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "ca96f2d43a553d73aec736e991c07010561bc7a9", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.9.2.tgz", "fileCount": 3, "integrity": "sha512-Wnx/IVMSZ31D/cO9HSsU46FjrPWHqtdF8+0eyZ1zIB5a6hXaZXghUKpRrC4D5DcRTZOjml2oBhXoqfGYyXKipw==", "signatures": [{"sig": "MEYCIQDg4ujSxtE8C3zwjFX7gFVdFVYZMW7SMUjMQ2AR4BvcUAIhAMKOgGf6ZaB8SAe56LfWvk6f2yq8FnCs/ywbRPHABY9W", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2442838}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "347a34745b2679c1192535db3c0f60889861d3ad", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.9.2_1703917418923_0.11589368802521616", "host": "s3://npm-registry-packages"}}, "4.9.3": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.9.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.9.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "19e9485d4e7540eb5b5e24415b4460f49c05ed80", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.9.3.tgz", "fileCount": 3, "integrity": "sha512-4/QVaRyaB5tkEAGfjVvWrmWdPF6F2NoaoO5uEP7N0AyeBw7l8SeCWWKAGrbx/00PUdHrJVURJiYikazslSKttQ==", "signatures": [{"sig": "MEQCIGcm7UfWjR3DlqhBkX/9KheulOYOjywhNGtBkdzG+AzHAiAu4DVl+is9xfy95FG2skWrUOMgdzmwg5jnl1U+bOH1Nw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2455126}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "4ab3ad360457cd79f4ea852447d3ddca22da95d6", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.9.3_1704435658136_0.8920731431989419", "host": "s3://npm-registry-packages"}}, "4.9.4": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.9.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.9.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "09589e4e1a073cf56f6249b77eb6c9a8e9b613a8", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.9.4.tgz", "fileCount": 3, "integrity": "sha512-LFHS/8Q+I9YA0yVETyjonMJ3UA+DczeBd/MqNEzsGSTdNvSJa1OJZcSH8GiXLvcizgp9AlHs2walqRcqzjOi3A==", "signatures": [{"sig": "MEQCIQCyHD8szBwWKCx2Xjvz2dyZEjJ/ou+SPooGPS2en+BjDgIfO75mqJVdZkPhvi/KK2uLro2/XsxzgGBj041KCqFxIA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2455126}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "18372035f167ec104280e1e91ef795e4f7033f1e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.9.4_1704523152141_0.022298237926702047", "host": "s3://npm-registry-packages"}}, "4.9.5": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.9.5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.9.5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "987d30b5d2b992fff07d055015991a57ff55fbad", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.9.5.tgz", "fileCount": 3, "integrity": "sha512-HeGqmRJuyVg6/X6MpE2ur7GbymBPS8Np0S/vQFHDmocfORT+Zt76qu+69NUoxXzGqVP1pzaY6QIi0FJWLC3OPA==", "signatures": [{"sig": "MEQCIEYx+Ee9h7sfaEFZZq71oESCHxQ2ypBLtpg0jmuwtdGQAiAHb3Ob2HgFB1y1Z9p0ItIJHdzy6oarl91qBqgrCKCVpw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2455126}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "7fa474cc5ed91c96a4ff80e286aa8534bc15834f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.9.5_1705040185087_0.5169433265762513", "host": "s3://npm-registry-packages"}}, "4.9.6": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.9.6", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.9.6", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "160510e63f4b12618af4013bddf1761cf9fc9880", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.9.6.tgz", "fileCount": 3, "integrity": "sha512-+uCOcvVmFUYvVDr27aiyun9WgZk0tXe7ThuzoUTAukZJOwS5MrGbmSlNOhx1j80GdpqbOty05XqSl5w4dQvcOA==", "signatures": [{"sig": "MEUCIQDesTzuxg3jb/o5C48lCHO3ZWEZgmfx5x7Zo6JfRbueUgIgZeLxao9U5IYE0WGe+uyqSyXOll8Ut5qXNMR2HWEHJJ8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2446934}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "ecb6b0a430098052781aa6ee04ec92ee70960321", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.9.6_1705816350529_0.2749187201711498", "host": "s3://npm-registry-packages"}}, "4.10.0": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.10.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.10.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "fbe3d80f7a7ac54a8847f5bddd1bc6f7b9ccb65f", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.10.0.tgz", "fileCount": 3, "integrity": "sha512-X1ES+V4bMq2ws5fF4zHornxebNxMXye0ZZjUrzOrf7UMx1d6wMQtfcchZ8SqUnQPPHdOyOLW6fTcUiFgHFadRA==", "signatures": [{"sig": "MEUCIQDN8OW82cn2Imgo/pkBmdsYFXVWQJR8axE4012jX4LVsgIgQEq5tF6+Gym/zMAzN0kUyGx85VGdchJrf6xkoZymQP0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2528895}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "762420860765e8e46e24d48b38f5b98ca31735fa", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.10.0_1707544733324_0.5438900254708261", "host": "s3://npm-registry-packages"}}, "4.11.0": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.11.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.11.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "25bc0e9dbd5feab0b6fa68b2b87ce5f28a8a7c19", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.11.0.tgz", "fileCount": 3, "integrity": "sha512-kKT9XIuhbvYgiA3cPAGntvrBgzhWkGpBMzuk1V12Xuoqg7CI41chye4HU0vLJnGf9MiZzfNh4I7StPeOzOWJfA==", "signatures": [{"sig": "MEUCIQDz10XGewJ7RPwD3R1xDZl9BVKUBIWHzvvhiOQH3ZhRpQIgOSNW9A1lZ520UfZ/WnWmFombnx1ip2xyQ6VQuCI6Z6Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2528895}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "90ad652b745c5fe7167d92b4ad671cc387577a99", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.11.0_1707977396941_0.7108249671898321", "host": "s3://npm-registry-packages"}}, "4.12.0": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.12.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.12.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "0c6ad792e1195c12bfae634425a3d2aa0fe93ab7", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.12.0.tgz", "fileCount": 3, "integrity": "sha512-ix+qAB9qmrCRiaO71VFfY8rkiAZJL8zQRXveS27HS+pKdjwUfEhqo2+YF2oI+H/22Xsiski+qqwIBxVewLK7sw==", "signatures": [{"sig": "MEUCIQDipUhkSy4f6VTm68b579gBw7TV8ZMs4eepXWbax+dtFwIgQvgqAz5FjNwj69xrkzWGF2OSVr/KiWVurYaA1b6LyeM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2541183}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "0146b84be33a8416b4df4b9382549a7ca19dd64a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.12.0_1708090357106_0.5093700880628875", "host": "s3://npm-registry-packages"}}, "4.12.1": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.12.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.12.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "215101b2bb768cce2f2227145b8dd5c3c716c259", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.12.1.tgz", "fileCount": 3, "integrity": "sha512-sHig3LaGlpNgDj5o8uPEoGs98RII8HpNIqFtAI8/pYABO8i0nb1QzT0JDoXF/pxzqO+FkxvwkHZo9k0NJYDedg==", "signatures": [{"sig": "MEUCIQCQ581juIRyb/Jd/+UXZmUNqKeZ8DtVHH3g4J+6K18A6wIgMNbCkNqz5iiWzeEMCMWNWIQcvNV2r7SKzRFdgc92v+0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2520703}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "f44dac3170a671b0978afa3af43818617904f544", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.12.1_1709705032058_0.9329350743395888", "host": "s3://npm-registry-packages"}}, "4.13.0": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.13.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.13.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "303d57a328ee9a50c85385936f31cf62306d30b6", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.13.0.tgz", "fileCount": 3, "integrity": "sha512-Oq90dtMHvthFOPMl7pt7KmxzX7E71AfyIhh+cPhLY9oko97Zf2C9tt/XJD4RgxhaGeAraAXDtqxvKE1y/j35lA==", "signatures": [{"sig": "MEUCICZM56L7VAuP+bvLsVse+h/PdDTgLWIh2yDVtT/dgrIfAiEA3/BhCtlchdT6QlgA4/xb7pvB0KmkgbGCsjD46eDYE/o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2512511}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "1c8afed74bd81cd38ad0b373ea6b6ec382975013", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.13.0_1710221336783_0.30922712842191413", "host": "s3://npm-registry-packages"}}, "4.13.1-1": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.13.1-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.13.1-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "90eecf040630e351179818e2466666e4818f9e0c", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.13.1-1.tgz", "fileCount": 3, "integrity": "sha512-hlJQDGm5NMBMnbduDvQ8q9pOH56w67Qtqw5S5PeQbVyArO3kW0pjhAOO4w/WJ4BeE+0jk9uFdwTPf7R66njaGg==", "signatures": [{"sig": "MEUCIHD27yebw1pmIEgFkINH1tbnjztzpE7M/9ru4tygbi+yAiEA3T8p2arxOtpR+4nN2OaqlGm/W685JN3yav3G2V02FOs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2532993}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "84797d177bee161df233644292bc8f128b989cea", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.13.1-1_1711265977518_0.9008659550277964", "host": "s3://npm-registry-packages"}}, "4.13.1": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.13.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.13.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "ec05966a4ed1b3338c8842108353ac6d3443dc6a", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.13.1.tgz", "fileCount": 3, "integrity": "sha512-Jv1DkIvwEPAb+v25/Unrnnq9BO3F5cbFPT821n3S5litkz+O5NuXuNhqtPx5KtcwOTtaqkTsO+IVzJOsxd11aQ==", "signatures": [{"sig": "MEYCIQCoPsKyVwthtxZ+cyR/QbkTRH+zMy68fO5kryqe9qGGJQIhANvz+lrV5gLWqQTVAhP5rzmj+MYhzBmCBuVAKbT5O8hS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2532991}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "fffaedeaa1cf9c8f6efc93d53bb8a81738e0ce87", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.13.1_1711535276491_0.2942048242610522", "host": "s3://npm-registry-packages"}}, "4.13.2": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.13.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.13.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "47b932ee59a5395a3a341b0493e361d9e6032cf2", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.13.2.tgz", "fileCount": 3, "integrity": "sha512-C3GSKvMtdudHCN5HdmAMSRYR2kkhgdOfye4w0xzyii7lebVr4riCgmM6lRiSCnJn2w1Xz7ZZzHKuLrjx5620kw==", "signatures": [{"sig": "MEUCIGttoftAacv4pNaZe+CRCOTgwZARSuz0rGDhwGMghcEGAiEA3XPPRIk4GoXmo8biOfZB5oZ6wV7+hd+600k8gtnhwFg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2532991}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "b379a592234416a2084918b0eea4c81865a1579f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.13.2_1711635231978_0.5065597963696651", "host": "s3://npm-registry-packages"}}, "4.14.0": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.14.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.14.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "53d9448962c3f9ed7a1672269655476ea2d67567", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.14.0.tgz", "fileCount": 3, "integrity": "sha512-SDDhBQwZX6LPRoPYjAZWyL27LbcBo7WdBFWJi5PI9RPCzU8ijzkQn7tt8NXiXRiFMJCVpkuMkBf4OxSxVMizAw==", "signatures": [{"sig": "MEYCIQCJ/ZomB7AEovOqUUVfqzHEKyMsJNYEfZvMKUWOeJgyTQIhAPJ6zgaIEjBIsWrqvhT9X2IIDkTEMpg4JJMxpq379wGr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2553455}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "5abe71bd5bae3423b4e2ee80207c871efde20253", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.14.0_1712121787739_0.060696263255830996", "host": "s3://npm-registry-packages"}}, "4.14.1": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.14.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.14.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "180694d1cd069ddbe22022bb5b1bead3b7de581c", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.14.1.tgz", "fileCount": 3, "integrity": "sha512-RWdiHuAxWmzPJgaHJdpvUUlDz8sdQz4P2uv367T2JocdDa98iRw2UjIJ4QxSyt077mXZT2X6pKfT2iYtVEvOFw==", "signatures": [{"sig": "MEUCIC6D1PtzU4cLz34UqQ3O75hiY58KBDztvUw1jwy69c7jAiEAvwg+vXRi6BPEimFp+x6CnOWhRH+mUtWcYuvLOo4YduY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2553455}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "0b665c31833525c923c0fc20f43ebfca748c6670", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.14.1_1712475352026_0.5906421486110172", "host": "s3://npm-registry-packages"}}, "4.14.2": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.14.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.14.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "48ee7fe5fee7b6d0028b6dda4fab95238208a0cd", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.14.2.tgz", "fileCount": 3, "integrity": "sha512-KNUH6jC/vRGAKSorySTyc/yRYlCwN/5pnMjXylfBniwtJx5O7X17KG/0efj8XM3TZU7raYRXJFFReOzNmL1n1w==", "signatures": [{"sig": "MEYCIQDQ0b/BFpvDIvDVJN5upo+E6D5RQixS/dEN9MFibUAgiAIhAItcWpqnNNpRnC9SnEmDpaI9vOLGKrly10oF3Hv6rpnX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2524743}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "7275328b41b29605142bfdf55d68cb54e895a20c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.14.2_1712903032912_0.17427332868455325", "host": "s3://npm-registry-packages"}}, "4.14.3": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.14.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.14.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "8b88ed0a40724cce04aa15374ebe5ba4092d679f", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.14.3.tgz", "fileCount": 3, "integrity": "sha512-sk/Qh1j2/RJSX7FhEpJn8n0ndxy/uf0kI/9Zc4b1ELhqULVdTfN6HL31CDaTChiBAOgLcsJ1sgVZjWv8XNEsAQ==", "signatures": [{"sig": "MEQCIHZubeWbD0QD2RGkJdDbYgM0sypw2fZs8F5gVc22HUbVAiBbvhjo+YEKOla0XdQBMjVhVW1S/k9ta+BtGwUQz3ky1g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2524743}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "e64f3d8d0cdc561f00d3efe503e3081f81889679", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.14.3_1713165526281_0.10450281450666421", "host": "s3://npm-registry-packages"}}, "4.15.0": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.15.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.15.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "961c290372d170f588ebf65c145c485c4aad7005", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.15.0.tgz", "fileCount": 3, "integrity": "sha512-3A1FbHDbBUvpJXFAZwVsiROIcstVHP9AX/cwnyIhAp+xyQ1cBCxywKtuzmw0Av1MDNNg/y/9dDHtNypfRa8bdw==", "signatures": [{"sig": "MEUCIAWF2XVekEkxG3X2hi5aOAmWkhqy/7PlAjR6VM6ucEmdAiEAjE3NiVihz/fwSQZoTnqT1EtWBGqMbbkrkg/9YG4ADFE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2623047}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "e6e05cde31fc144228bb825c9d4ebba2f377075c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.15.0_1713591450410_0.5872138317360631", "host": "s3://npm-registry-packages"}}, "4.16.0": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.16.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.16.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "a419fb6e09baef0119dc7a59df8fda7208a2d672", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.16.0.tgz", "fileCount": 3, "integrity": "sha512-qxAB8MiHuDI8jU0D+WI9Gym3fvUJHA/AjKRXxbEH921SB3AeKQStq1FKFA59dAoqqCArjJ1voXM/gMvgEc1q4Q==", "signatures": [{"sig": "MEUCIFbocVcSO7BnayGKYLgCvgjImtKHBFMPAeG1adhd/QwhAiEAyiFSmRxXpQNzd2bkowQuEu4COe4YL/9bJOiWR0sWg3I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2623047}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "38fe70780cb7e374b47da99e3a3dca6b2a2170d2", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.16.0_1713674553715_0.3031919134703249", "host": "s3://npm-registry-packages"}}, "4.16.1": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.16.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.16.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "5319629dcdcb85ba201c6f0f894c9472e7d1013d", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.16.1.tgz", "fileCount": 3, "integrity": "sha512-uU8zuGkQfGqfD9w6VRJZI4IuG4JIfNxxJgEmLMAmPVHREKGsxFVfgHy5c6CexQF2vOfgjB33OsET3Vdn2lln9A==", "signatures": [{"sig": "MEQCIGUUG8JlD7swxfPTEFAfT39Tny61Hx+LICe84G265OAzAiBA3UG6BHPaLSQc1ND1omdXBRTmdF4e00jTw99QREzGzg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2623047}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "5d8019b901e98cc8895751a23e5edfc9135b1a35", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.16.1_1713724218051_0.129778456303133", "host": "s3://npm-registry-packages"}}, "4.16.2": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.16.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.16.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "7a7d091a94fa7c50ebf72d5578475093e01c739e", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.16.2.tgz", "fileCount": 3, "integrity": "sha512-ohkPt0lKoCU0s4B6twro2aft+QROPdUiWwOjPNTzwTsBK5w+2+iT9kySdtOdq0gzWJAdiqsV4NFtXOwGZmIsHA==", "signatures": [{"sig": "MEUCIQD2ndxGsstMcAjSEiaU3EuAlwSR+rJruwLbmxRlc5oE6wIgdHkkQ/3yUqICN+ZeDHb2bczVsX5OUBDDn+LZQVE1tp8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2623047}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "18839eb234f79adc44a591e355fd7b3243a4cd21", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.16.2_1713799175809_0.9131049941053297", "host": "s3://npm-registry-packages"}}, "4.16.3": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.16.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.16.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "6b546cd229123187f52b80ee679aaf7a8d1d4ff6", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.16.3.tgz", "fileCount": 3, "integrity": "sha512-/pArXjqnEdhbQ1qe4CTTlJ6/GjWGdWNRucKAp4fqKnKf7QC0BES3QEV34ACumHHQ4uEGt4GctF2ISCMRhkli0A==", "signatures": [{"sig": "MEYCIQCKJQoPV8MJt5Iyqc9AWdXOCJXOEu4ExzhFrva5Jkd9MgIhALI6qSvAaHwBdI0c6eNeCyv6yae4gvmeBR9XFSoWtb9g", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2623047}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "b9a62fd4cf28538d7c3b268eb25e709b45d44cce", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.16.3_1713849168700_0.394309368378563", "host": "s3://npm-registry-packages"}}, "4.16.4": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.16.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.16.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "d32727dab8f538d9a4a7c03bcf58c436aecd0139", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.16.4.tgz", "fileCount": 3, "integrity": "sha512-Lh/8ckoar4s4Id2foY7jNgitTOUQczwMWNYi+Mjt0eQ9LKhr6sK477REqQkmy8YHY3Ca3A2JJVdXnfb3Rrwkng==", "signatures": [{"sig": "MEQCIHXObWbIEVyaPFyRyLXtJDlvnEJMG3dB6Y//zGMODVE3AiA0TYYiT70Wo4zO5+g6Kwe12eIPOdWlKi9sDn3M9Yp4dA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2623047}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "1c404fa352b70007066e94ff4c1981f8046f8cef", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.16.4_1713878121530_0.8207206844248898", "host": "s3://npm-registry-packages"}}, "4.17.0": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.17.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.17.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "0f9719bec216cf2769ac9f420211190b2f55c8fa", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.17.0.tgz", "fileCount": 3, "integrity": "sha512-leFtyiXisfa3Sg9pgZJwRKITWnrQfhtqDjCamnZhkZuIsk1FXmYwKoTkp6lsCgimIcneFFkHKp/yGLxDesga4g==", "signatures": [{"sig": "MEUCIQDEmiQdeKPfpQSTpjNhzKuJSxUkiIh0/SBUteMsN3DBcQIgEtU4ifTtL1GgsY543nC0rioLyI6z27qORYMTxSug6Wc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2606663}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "91352494fc722bcd5e8e922cd1497b34aec57a67", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.17.0_1714217406873_0.47108449386173756", "host": "s3://npm-registry-packages"}}, "4.17.1": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.17.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.17.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "59fef3d0a5feee3b072d92898c9d62c0c7e6e95c", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.17.1.tgz", "fileCount": 3, "integrity": "sha512-gEYmYYHaehdvX46mwXrU49vD6Euf1Bxhq9pPb82cbUU9UT2NV+RSckQ5tKWOnNXZixKsy8/cPGtiUWqzPuAcXQ==", "signatures": [{"sig": "MEYCIQCjZWMVECg13Rf2xY8xiPudqE142hGyXXgbTsz4bXDQogIhAJTGF6yQnU6tQ74/YU/ncapbJUHxZU9qfi8WWpOIBAZT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2606663}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "dbf0a2e5d3c3eae09ac4d502646d0ecab63f40fd", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.17.1_1714366688841_0.04783454258253372", "host": "s3://npm-registry-packages"}}, "4.17.2": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.17.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.17.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "7ef1c781c7e59e85a6ce261cc95d7f1e0b56db0f", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.17.2.tgz", "fileCount": 3, "integrity": "sha512-BOaNfthf3X3fOWAB+IJ9kxTgPmMqPPH5f5k2DcCsRrBIbWnaJCgX2ll77dV1TdSy9SaXTR5iDXRL8n7AnoP5cg==", "signatures": [{"sig": "MEYCIQCIH2EMzgQWFGAw3cJJudBSV9RZBvwS4b5g4AUrgf92egIhAOVSYRfm/fOYQqA2jA9e/GsrO/hGrqS2t0/bNwHZwjJQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2606663}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "5e955a1c2c5e080f80f20f650da9b44909d65d56", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.17.2_1714453264778_0.2734957677990819", "host": "s3://npm-registry-packages"}}, "4.18.0": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.18.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.18.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "8ed09c1d1262ada4c38d791a28ae0fea28b80cc9", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.18.0.tgz", "fileCount": 3, "integrity": "sha512-ROCM7i+m1NfdrsmvwSzoxp9HFtmKGHEqu5NNDiZWQtXLA8S5HBCkVvKAxJ8U+CVctHwV2Gb5VUaK7UAkzhDjlg==", "signatures": [{"sig": "MEQCIF1dpEH35nunsMaP35gsH7D+W1hzFMGdHP/9MxdWEHOnAiBaOGiUbhIJpeykHCOfZb6z1tUutHB+PJbz3UkAzoOVtQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2635343}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "bb6f069ea3623b0297ef3895f2dcb98a2ca5ef58", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.18.0_1716354240437_0.3086230680178452", "host": "s3://npm-registry-packages"}}, "4.18.1": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.18.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.18.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "f5a635c017b9bff8b856b0221fbd5c0e3373b7ec", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.18.1.tgz", "fileCount": 3, "integrity": "sha512-f+pJih7sxoKmbjghrM2RkWo2WHUW8UbfxIQiWo5yeCaCM0TveMEuAzKJte4QskBp1TIinpnRcxkquY+4WuY/tg==", "signatures": [{"sig": "MEUCIBVBo6XPCLONtjFxB50kECoAWoHMSouiMerK37/ruDnuAiEAhhJPhEMuUXGxN63XORON0VUU2wS2UXmqdFJAxNxpbg8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2508455}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "21f9a4949358b60801c948cd4777d7a39d9e6de0", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.18.1_1720452328476_0.8947863940493828", "host": "s3://npm-registry-packages"}}, "4.19.0": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.19.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.19.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "70ae58103b5bc7ba2e2235738b51d97022c8ef92", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.19.0.tgz", "fileCount": 3, "integrity": "sha512-2DnD3mkS2uuam/alF+I7M84koGwvn3ZVD7uG+LEWpyzo/bq8+kKnus2EVCkcvh6PlNB8QPNFOz6fWd5N8o1CYg==", "signatures": [{"sig": "MEUCIQDcYAcM+BwkM/kjwZfoSXqtyquuTP/At6lEpBGPQ085WAIgOs7de2GPzs51rKNBtyBIRYK4uYGqV72j4BuRkZDBxUU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2467559}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "28546b5821efcb72c2eb05f422d986524647a0e3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.19.0_1721454390441_0.130172806390914", "host": "s3://npm-registry-packages"}}, "4.19.1": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.19.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.19.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "c08fb3e629d50d2eac31329347cfc559a1cf81d1", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.19.1.tgz", "fileCount": 3, "integrity": "sha512-/1BmHYh+iz0cNCP0oHCuF8CSiNj0JOGf0jRlSo3L/FAyZyG2rGBuKpkZVH9YF+x58r1jgWxvm1aRg3DHrLDt6A==", "signatures": [{"sig": "MEUCIQCB88XECykLCeAVJCWmWMutbbT7zDLPzoDtxfQuOIbdoQIgbYV4wn/ku7/eUcoIFKK25UL093g9w8C10aaQKpoohO4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2377431}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "8b967917c2923dc6a02ca1238261387aefa2cb2f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.19.1_1722056056140_0.15306171581725958", "host": "s3://npm-registry-packages"}}, "4.19.2": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.19.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.19.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "80d63c5562915a2f8616a04251fcaee0218112b0", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.19.2.tgz", "fileCount": 3, "integrity": "sha512-EAz6vjPwHHs2qOCnpQkw4xs14XJq84I81sDRGPEjKPFVPBw7fwvtwhVjcZR6SLydCv8zNK8YGFblKWd/vRmP8g==", "signatures": [{"sig": "MEYCIQCr/0f9Iue+9GPoaapc6zFyKkbNVi5B9FqY8rAK0UoDfAIhAMGDl6Qf0rRS39qJEJKW0Ugk7qe3Hqg8FwwsbSgt5WOA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2377431}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "39955e55dbc12ec379a21efcf8fc21e55ec6ce3a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.19.2_1722501189429_0.04777974604222002", "host": "s3://npm-registry-packages"}}, "4.20.0": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.20.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.20.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "c894ade2300caa447757ddf45787cca246e816a4", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.20.0.tgz", "fileCount": 3, "integrity": "sha512-qmuxFpfmi/2SUkAw95TtNq/w/I7Gpjurx609OOOV7U4vhvUhBcftcmXwl3rqAek+ADBwSjIC4IVNLiszoj3dPA==", "signatures": [{"sig": "MEUCIQDqPisCFh8sHoniWbGhb3f+g7i5ieOCma2DXj9Lcg0WCAIgC684UaPbU56Cp9Pu2qpFEYgyb2lYaOJfGw2h6m0rSY0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2377431}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "df12edfea6e9c1a71bda1a01bed1ab787b7514d5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.20.0_1722660546902_0.3676659655625498", "host": "s3://npm-registry-packages"}}, "4.21.0": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.21.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.21.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "a2eab4346fbe5909165ce99adb935ba30c9fb444", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.21.0.tgz", "fileCount": 3, "integrity": "sha512-H1eRaCwd5E8eS8leiS+o/NqMdljkcb1d6r2h4fKSsCXQilLKArq6WS7XBLDu80Yz+nMqHVFDquwcVrQmGr28rg==", "signatures": [{"sig": "MEUCIBk+3AyWBo89V9V/ZtSO54CPQ+0TlkT+zfO+v5NMe2xjAiEAw54xmsUdV5KR8twJpRBrLn94Jis18BN9dqQFKky9Uf8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2303703}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "c4bb050938778bcbe7b3b3ea3419f7fa70d60f5b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.21.0_1723960548839_0.9788166130362417", "host": "s3://npm-registry-packages"}}, "4.21.1": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.21.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.21.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "385d76a088c27db8054d9f3f28d64d89294f838e", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.21.1.tgz", "fileCount": 3, "integrity": "sha512-BggMndzI7Tlv4/abrgLwa/dxNEMn2gC61DCLrTzw8LkpSKel4o+O+gtjbnkevZ18SKkeN3ihRGPuBxjaetWzWg==", "signatures": [{"sig": "MEYCIQDv9GnrM7ruvIN+/XqmwvvPaID2PrOp6q96wb1EPHmE2AIhANCX19fPjv+cXgBtF0wlD+BSslwt2K3qn2S9TRO3XMRL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2307799}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "c33c6ceb7da712c3d14b67b81febf9303fbbd96c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.21.1_1724687670056_0.7442896566997781", "host": "s3://npm-registry-packages"}}, "4.21.2": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.21.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.21.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "38edfba9620fe2ca8116c97e02bd9f2d606bde09", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.21.2.tgz", "fileCount": 3, "integrity": "sha512-RL56JMT6NwQ0lXIQmMIWr1SW28z4E4pOhRRNqwWZeXpRlykRIlEpSWdsgNWJbYBEWD84eocjSGDu/XxbYeCmwg==", "signatures": [{"sig": "MEUCIDESc1xiLWNtPSmIDAeByNxo5SCq07ZauESPEmo7OHAtAiEA3j8Zw2+ZOQInd9wf9mN3WZh96fyc06xFCRxdr7vTyvI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2311895}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "f83b3151e93253a45f5b8ccb9ccb2e04214bc490", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.21.2_1725001481763_0.17361354818389674", "host": "s3://npm-registry-packages"}}, "4.21.3": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.21.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.21.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "eef1536a53f6e6658a2a778130e6b1a4a41cb439", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.21.3.tgz", "fileCount": 3, "integrity": "sha512-yXH6K6KfqGXaxHrtr+Uoy+JpNlUlI46BKVyonGiaD74ravdnF9BUNC+vV+SIuB96hUMGShhKV693rF9QDfO6nQ==", "signatures": [{"sig": "MEUCIQDoailf/ERkAvHvauWIAIRLVWLPNRGsDaIKgUAlic4I4gIgSImmIy15QT3hHhTDnrqaQ9lPv/s2QH++PF2xf9GyQhQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2242183}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "9f5a735524a5c56ba61a8dc6989374917f5aceb1", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.21.3_1726124766290_0.533009715651761", "host": "s3://npm-registry-packages"}}, "4.22.0": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.22.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.22.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "90b11314fbf45d04083f658e08dc3b32fd713061", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.22.0.tgz", "fileCount": 3, "integrity": "sha512-VWQiCcN7zBgZYLjndIEh5tamtnKg5TGxyZPWcN9zBtXBwfcGSZ5cHSdQZfQH/GB4uRxk0D3VYbOEe/chJhPGLQ==", "signatures": [{"sig": "MEUCIHA0zdSaoDMLG/7J5wVMah/2xEisKg6xC6Uk/RK62108AiEAlj7FlpzUO7W1HDL5izmL8lzZYt7nscbW9gpvfCZ4nxw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2242191}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "5e7a3631a28a863ddb97a64189c3b76eec9983ca", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.22.0_1726721747267_0.9047628519504411", "host": "s3://npm-registry-packages"}}, "4.22.1": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.22.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.22.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "d3bea69308889c3f1af417dcc6f3c06f8d4ad5aa", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.22.1.tgz", "fileCount": 3, "integrity": "sha512-xzbqImk1h5abj0bPU5XQVrqBhLHl2zTygG6+vES2TrgmNSiaPzn39aqI8QtdqmGYz507ZVI2qocTTfVwW23SmQ==", "signatures": [{"sig": "MEQCIAZfMFoCsF922HxZdtuaZY6147EJNhGMSZdQzTKRs7YyAiAeZYWKGW5VTQY4zZxSeJk11HAuEhz6Gu1eezlkz8T6vQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2246287}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "76e962daca5b7352bf199c28fa0a10ad4745c5e7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.22.1_1726820529179_0.047942243289808006", "host": "s3://npm-registry-packages"}}, "4.22.2": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.22.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.22.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "d86e9b7b5b242652cd691c46d1939130c35cb68d", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.22.2.tgz", "fileCount": 3, "integrity": "sha512-/egzQzbOSRef2vYCINKITGrlwkzP7uXRnL+xU2j75kDVp3iPdcF0TIlfwTRF8woBZllhk3QaxNOEj2Ogh3t9hg==", "signatures": [{"sig": "MEQCIDPkK3EyoTVuRZkqBY1mXHqzNauiBAxUcD2J95+CYePXAiAysF4z2AeY8Y14ubls6rR0RRdbFHwNBbZEU/dyJq5QDg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2246287}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "b86ffd776cfa906573d36c3f019316d02445d9ef", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.22.2_1726824840903_0.360683391381611", "host": "s3://npm-registry-packages"}}, "4.22.3-0": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.22.3-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.22.3-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "34d75dd44390e5e9582496ed4d067c86879c2f8e", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.22.3-0.tgz", "fileCount": 3, "integrity": "sha512-3eUhCDQYZ86RiSzFIS/d7yhKteiQJwXThpb4D2LD4RiQnhLQMAMfAu3w/dZ7ObDq/xGCxhl0pELL6STmE9iWXA==", "signatures": [{"sig": "MEUCIQCq25Sk8if4+ITN6XhWOxL8is+YwnjngI1Z4W6mD2VpTQIgS7KxAcNhh0Y5S9N8Dl5C9UKd7jItN1dC7q7OzmrUV80=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2246289}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "9e04b4849db9134473b84e4b94aa353ae4fd8754", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.22.3-0_1726843695328_0.2065762621180034", "host": "s3://npm-registry-packages"}}, "4.22.3": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.22.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.22.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "6fac5db5ee66b7ee2bee8db5ffe22908d723fcc9", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.22.3.tgz", "fileCount": 3, "integrity": "sha512-aXFDDzVq2HTT7GsV5s8rpsH1Mk6WlZOxxsd2H/4LP4gJ8Fu9+C+O5k2ccmXjSh7IPc8R0u1YdeGETARcJvgoPg==", "signatures": [{"sig": "MEQCIGXOjJJ49G+z6tyTyp6PvcxUG5dm6Ljp0ccdPi2h3OBbAiBCuHC3+UEcJk10qb3UyDarZ7dWy8OqHMnfms0RBY2aqg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2246287}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "e1cba8e84a0c01dd16580ba7a2536a988dfb4e18", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.22.3_1726895006309_0.2671290521855656", "host": "s3://npm-registry-packages"}}, "4.22.4": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.22.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.22.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "67ac70eca4ace8e2942fabca95164e8874ab8128", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.22.4.tgz", "fileCount": 3, "integrity": "sha512-ePYIir6VYnhgv2C5Xe9u+ico4t8sZWXschR6fMgoPUK31yQu7hTEJb7bCqivHECwIClJfKgE7zYsh1qTP3WHUA==", "signatures": [{"sig": "MEYCIQCIqbGkD4EMTysDnmqWIvCt9hNaB8uyR862BEEruzkx7QIhANb2hjA3vY7iPooauLuk+y2RglnpLXVg/M6TEM1xJEN9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2246287}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "79c0aba353ca84c0e22c3cfe9eee433ba83f3670", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.22.4_1726899097019_0.7631313329544125", "host": "s3://npm-registry-packages"}}, "4.22.5": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.22.5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.22.5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "cb00342b7432bdef723aa606281de2f522d6dcf7", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.22.5.tgz", "fileCount": 3, "integrity": "sha512-PTQq1Kz22ZRvuhr3uURH+U/Q/a0pbxJoICGSprNLAoBEkyD3Sh9qP5I0Asn0y0wejXQBbsVMRZRxlbGFD9OK4A==", "signatures": [{"sig": "MEUCIHKjBEQVViF8CxxiGRZ4hcewqAsmCvo8Ue7IR1zQAWNnAiEAvzJ+WM8zXe27cunRt328DWbuxwzuMhJe3EjVsZuEAAQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2246287}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "bc7780c322e134492f40a76bf64afe561670425c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.22.5_1727437713038_0.262720606345588", "host": "s3://npm-registry-packages"}}, "4.23.0": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.23.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.23.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "008bbfc76beae9651b989a36a0308fbb90ce9fcd", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.23.0.tgz", "fileCount": 3, "integrity": "sha512-o4QI2KU/QbP7ZExMse6ULotdV3oJUYMrdx3rBZCgUF3ur3gJPfe8Fuasn6tia16c5kZBBw0aTmaUygad6VB/hQ==", "signatures": [{"sig": "MEQCIDELE+UMTIPHnbyaM9vT0oopNeHfXmjye2JPpXiIn7cQAiBuVkrpdkwsIocY/YZQH7R0agxw+4P307qNWUx2HrdFxw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2246287}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "ed98e0821e6ad064839f0af46ceca061adbe3f14", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.23.0_1727766623867_0.6141639690904996", "host": "s3://npm-registry-packages"}}, "4.24.0": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.24.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.24.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "48e42e41f4cabf3573cfefcb448599c512e22983", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.24.0.tgz", "fileCount": 3, "integrity": "sha512-M3Dg4hlwuntUCdzU7KjYqbbd+BLq3JMAOhCKdBE3TcMGMZbKkDdJ5ivNdehOssMCIokNHFOsv7DO4rlEOfyKpg==", "signatures": [{"sig": "MEYCIQDT68s1X+COHpcJJDOJ3r451LxPFG87MyWdT8Io6JI8jAIhAN4waHc4NS0gJLpNgtE3dDoSJa/3UETedq5YYOWfMKoB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2250383}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "d3c000f4fd453e39a354299f0cfaa6831f56d7d8", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.24.0_1727861857779_0.9965368572820972", "host": "s3://npm-registry-packages"}}, "4.24.1": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.24.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.24.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "c13c25efcb6dd17a99d0573d43b4176fff334ecf", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.24.1.tgz", "fileCount": 3, "integrity": "sha512-PNKCMA1xRBARR7/j6KXMSB1z0/eGenC/t2wdQl5et3jnrHA+igIaLVNUEPfnVjmZIZJign7u/dobvV2VkPxMiw==", "signatures": [{"sig": "MEUCIEEvu/mEJZaGz1lZjHY9O72wjc5/mk8cfGQqEtM6c64rAiEAhqRm1ChiSWXr/ocp+he5tgPHmpG8RvzKaKLl/fmj0vE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2272543}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "88a54d892dacbb0efdbcade263a32d9df1a77b37", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.24.1_1730011401809_0.2025945460424159", "host": "s3://npm-registry-packages"}}, "4.24.2": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.24.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.24.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "b08461ace599c3f0b5f27051f1756b6cf1c78259", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.24.2.tgz", "fileCount": 3, "integrity": "sha512-tbtXwnofRoTt223WUZYiUnbxhGAOVul/3StZ947U4A5NNjnQJV5irKMm76G0LGItWs6y+SCjUn/Q0WaMLkEskg==", "signatures": [{"sig": "MEYCIQC7NvaEaX03WKSLMzbl3bGvXswQ/Dj0+LKzVx05YWRkvQIhAMPDbb3clMz9GPvkdfsuOS/9Er5Kr0oCIG7M4xEXMSNP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2272543}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "32d0e7dae85121ac0850ec28576a10a6302f84a9", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.24.2_1730043629140_0.44231791148707833", "host": "s3://npm-registry-packages"}}, "4.25.0-0": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.25.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.25.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "14e6dbd1301f8dd27988916bf6de1e7b6d6ec770", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.25.0-0.tgz", "fileCount": 3, "integrity": "sha512-Uc5lkdZcDsShLJAD/7ux1FMy8p2U7E42bCUJX6ENtMjVi2VGLYgupsUeQvHBGhoF/voWyNhLlXihXdh7YLhPmA==", "signatures": [{"sig": "MEQCIAX9THHHpxRzAEuQBgvChDAh9QV9O2H0Y9xGRErw14svAiB85YLL+wuUFOIxPFdUei7FOw0fSh21ZREFgQ3jUP0j9A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2272545}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "b7fcaba12e863db516f39de74c1eacfe5329a5c3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.25.0-0_1730182532099_0.2622986338914848", "host": "s3://npm-registry-packages"}}, "4.24.3": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.24.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.24.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "f43d4e0572397e3d3acd82d77d79ce021dea3310", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.24.3.tgz", "fileCount": 3, "integrity": "sha512-lWKNQfsbpv14ZCtM/HkjCTm4oWTKTfxPmr7iPfp3AHSqyoTz5AgLemYkWLwOBWc+XxBbrU9SCokZP0WlBZM9lA==", "signatures": [{"sig": "MEMCHw/IoGY7/ifGAG5wDcd1lDBM7XRfztCsNXAPVmjKY7ACIAcXqTNOXc9XAXg92jg43jcV/2g10n3Uk8wmziZD2tIn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2272543}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "69353a84d70294ecfcd5e1ab8e372e21e94c9f8e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.24.3_1730211269876_0.5047121095985219", "host": "s3://npm-registry-packages"}}, "4.24.4": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.24.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.24.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "bc9c195db036a27e5e3339b02f51526b4ce1e988", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.24.4.tgz", "fileCount": 3, "integrity": "sha512-Aie/TbmQi6UXokJqDZdmTJuZBCU3QBDA8oTKRGtd4ABi/nHgXICulfg1KI6n9/koDsiDbvHAiQO3YAUNa/7BCw==", "signatures": [{"sig": "MEYCIQCpuH7Ux3TAoZNeGrmifqVunYoR9EPfTVVYmJYMhg3dZwIhANkC+Q0w4YqQft/vEQHcewwe5gPNCO3odhpPU9BQQ7CA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2276639}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "cdf34ab5411aac6ac3f6cd21b10d2e58427e88ec", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.24.4_1730710048963_0.23127900653394629", "host": "s3://npm-registry-packages"}}, "4.25.0": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.25.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.25.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "49195be7e6a7d68d482b12461e2ea914e31ff977", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.25.0.tgz", "fileCount": 3, "integrity": "sha512-G4hTREQrIdeV0PE2JruzI+vXdRnaK1pg64hemHq2v5fhv8C7WjVaeXc9P5i4Q5UC06d/L+zA0mszYIKl+wY8oA==", "signatures": [{"sig": "MEQCIArPO0QtC/xlEZO/YiMvvzx39AFdGUwbVEPkIl2upbGkAiA3jD4UjZPc2xTlKgZ132PTqU2x1p5/mv84ucVAAgFKTg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2268447}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "42e587e0e37bc0661aa39fe7ad6f1d7fd33f825c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.25.0_1731141462919_0.9872360513134042", "host": "s3://npm-registry-packages"}}, "4.26.0": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.26.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.26.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "fe27eb8cbd3a6e0706459781c2463b624f785696", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.26.0.tgz", "fileCount": 3, "integrity": "sha512-MBR2ZhCTzUgVD0OJdTzNeF4+zsVogIR1U/FsyuFerwcqjZGvg2nYe24SAHp8O5sN8ZkRVbHwlYeHqcSQ8tcYew==", "signatures": [{"sig": "MEYCIQDuTOMZ5lbdcHJK8Y3R5i9grVHb45+mYXHLVBgQZoLohgIhAKomcJVkEXolOiHTov48fXcidRvWkNPXxb7AIOcBcOkh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2268447}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "ae1d14b7855ff6568a6697d37271a5eb4d8e2d3e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.26.0_1731480319476_0.534920080049688", "host": "s3://npm-registry-packages"}}, "4.27.0-0": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.27.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.27.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "0d71cbd9b9fece3da2db9e609f5335f2fb64c64d", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.27.0-0.tgz", "fileCount": 3, "integrity": "sha512-kwJbC1I/EDS9Dzrp+YlLbkFMDHTZ4g8TanLOvmq/4xkka6n9rmufhtzsSOgQ/IxkJ7vZl+fQNtPAuCw/ul+d/Q==", "signatures": [{"sig": "MEUCIHR5TIzJsa5PL3IewVIrNVqiK9LLl9oziotURefwwYEGAiEAh+2M2VVYxO69NZp2yQvTJzhoBO0jd+bWVytxu20m2kg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2268449}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "5e6074f07843bcbcf26b916c557fdfd81d2adece", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.27.0-0_1731481413362_0.04620095608230024", "host": "s3://npm-registry-packages"}}, "4.27.0-1": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.27.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.27.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "6d7ed1a78f8032673e69f83cfdc4ac2733ac69c8", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.27.0-1.tgz", "fileCount": 3, "integrity": "sha512-Twpz+Q/NzPSCMjRc8BppGb1mgK5fb07dvrusfulTfbzjaRfGVWqtgPSR5VGNpSNV4DeLyvoViOajyE/Aa9e7mw==", "signatures": [{"sig": "MEUCICxPavBZmZNlBw8HX9VM1oUTm9cR5UTMHJLYbSuENkyVAiEA656Bef+z1t4Y+cxT4vZZvv/O87q9OrRry8AQF3dFOpY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2268449}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "81f5021d7d7e2a488639dc036f2334995b3761fc", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.27.0-1_1731566011473_0.8010669660853647", "host": "s3://npm-registry-packages"}}, "4.27.0": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.27.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.27.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "d3886c2386e9f2ae4e538c0dc06eff4b311a982d", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.27.0.tgz", "fileCount": 3, "integrity": "sha512-Pu7xLHRy+5UjFCKR/vWsbFmiBYUC9993v99YoKWhAgK4VsdNuWHPs17NuCJEtVsZpYCNVPbRyBpQw58Ma8BmeA==", "signatures": [{"sig": "MEYCIQD9US+75iKNoL9PdtU6x6q71OHA+1y57GuZH8Kc6kDT7QIhALg/pmF+lXp/U6A/h6+I7XLkkO33mWSrtNubAqWAs8gg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2280735}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "c035068dfebeb959a35a8acf3ff008a249e2af73", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.27.0_1731667255938_0.7183367042810789", "host": "s3://npm-registry-packages"}}, "4.27.1-0": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.27.1-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.27.1-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "eaf5a29c2483ce81d58f68ae3269f2762ac19df6", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.27.1-0.tgz", "fileCount": 3, "integrity": "sha512-w+GopeqawzUMlBy6AaK81h7tRHtsWpIOF7+CP35YLpimA1b6msyV2TN0W+42RwvejT0n2yIcyN9VoTCUAv/lhQ==", "signatures": [{"sig": "MEUCIQD2DLVzwjcWTkcvpK9Yd4dH0B3qTzBPicbaAco2SNI0vQIgUqHZnH0H4YlHNEwYBMOyd0q9cZhgy6oVctd0wx8YQr4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2280737}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "a80f6a94d720224a44331d5a50745e9887619703", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.27.1-0_1731677309370_0.2068089665603441", "host": "s3://npm-registry-packages"}}, "4.27.1-1": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.27.1-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.27.1-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "b78f8e82a6edd15ea031d1fbdfd1387eb1dce9e0", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.27.1-1.tgz", "fileCount": 3, "integrity": "sha512-brzBOufT1nnGO8/KR55cQ8SELPHxPJGyXQLM/JEYr+k6dLT51kgOIub6rEsnebR20xSJLcw/Z10BRAkPqDunwQ==", "signatures": [{"sig": "MEYCIQCSZ+J0QWwJIo3rdVvYb5Yq3asYWpJHIDkOSQ89yljbmwIhAO0RQ1ubuaM2Lb0CXBr3+bT+SSCzqe+xYvWQn5kSwIkD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2280737}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "892ce0206dbf4fbf656b2f0563ef803c5e5a0016", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.27.1-1_1731685104360_0.4787194267316228", "host": "s3://npm-registry-packages"}}, "4.27.1": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.27.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.27.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "36adb6579d2ffb6b981ece91491aefc61432bdd3", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.27.1.tgz", "fileCount": 3, "integrity": "sha512-udOKLtPNOVwSwre2v+Bmp3qYBvqkjP+wYIGic1r3XqzpiuvIxfQOt8hSxnp45eFeiHV50+ol4IpZJ2WEt4Hkog==", "signatures": [{"sig": "MEUCIF+0dYOQDqTc/Y2yobpVnKugyfmwojkTs720qLI0BT4YAiEA5g+qtQ2W8WKAFvRUHwoJSFzT9Ur85KF5CiyDI4SB5ks=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2280735}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "aaf38b725dd142b1da4190a91de8b04c006fead5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.27.1_1731686882500_0.4350711933403737", "host": "s3://npm-registry-packages"}}, "4.27.2": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.27.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.27.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "9ebdec626efffe3a8676958ae7e5992cfdd2ec55", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.27.2.tgz", "fileCount": 3, "integrity": "sha512-DEP3Njr9/ADDln3kNi76PXonLMSSMiCir0VHXxmGSHxCxDfQ70oWjHcJGfiBugzaqmYdTC7Y+8Int6qbnxPBIQ==", "signatures": [{"sig": "MEQCIGROFoUOEEK4Tc1U5QUIa6YGONzux/TDuM/TyWJza+6YAiAROmoy8tR4xU0rBA/vw4X8ry3ZKZ7PHLGdBQlG6ggv1g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2280735}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "a503a4dd9982bf20fd38aeb171882a27828906ae", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.27.2_1731691225316_0.6714106510281885", "host": "s3://npm-registry-packages"}}, "4.27.3": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.27.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.27.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "fa84b3f81826cee0de9e90f9954f3e55c3cc6c97", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.27.3.tgz", "fileCount": 3, "integrity": "sha512-LdrI4Yocb1a/tFVkzmOE5WyYRgEBOyEhWYJe4gsDWDiwnjYKjNs7PS6SGlTDB7maOHF4kxevsuNBl2iOcj3b4A==", "signatures": [{"sig": "MEUCIQC0AT+XWtxcUJ5lQhDrOheilbD0Wt+VFnqy0DJVXvAlnQIgWy/iRlQdZY787O1bvEXLguwlNB0HxsSXKfGfNRJybcA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2280735}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "7c0b1f8810013b5a351a976df30a6a5da4fa164b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.27.3_1731947997056_0.19924131551783764", "host": "s3://npm-registry-packages"}}, "4.27.4": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.27.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.27.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "9a467f7ad5b61c9d66b24e79a3c57cb755d02c35", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.27.4.tgz", "fileCount": 3, "integrity": "sha512-qyyprhyGb7+RBfMPeww9FlHwKkCXdKHeGgSqmIXw9VSUtvyFZ6WZRtnxgbuz76FK7LyoN8t/eINRbPUcvXB5fw==", "signatures": [{"sig": "MEQCIEVI6UHPLmYBs7PS+OqnTcCDIqdiIRxJveaMlhIIf9ASAiB5WR6I8isX3m1cdMJmtCVm82bhNvczo/MyZUo+dx24Xg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2276639}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "e805b546405a4e6cfccd3fe73e9f4df770023824", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.27.4_1732345240145_0.37350964220393035", "host": "s3://npm-registry-packages"}}, "4.28.0": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.28.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.28.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "d488290bf9338bad4ae9409c4aa8a1728835a20b", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.28.0.tgz", "fileCount": 3, "integrity": "sha512-VEdVYacLniRxbRJLNtzwGt5vwS0ycYshofI7cWAfj7Vg5asqj+pt+Q6x4n+AONSZW/kVm+5nklde0qs2EUwU2g==", "signatures": [{"sig": "MEUCIGQOfeDJpAza8HLPwcor0YP8v/uC2hI/YAaeE/YFRPUUAiEAsylFN4K8SakhzhRwrcrRLdr6uKgj7vHgAHbKa61y448=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2272559}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "0595e433edec3608bfc0331d8f02912374e7f7f7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.28.0_1732972568144_0.6555784996368468", "host": "s3://npm-registry-packages"}}, "4.28.1": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.28.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.28.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "1eb6651839ee6ebca64d6cc64febbd299e95e6bd", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.28.1.tgz", "fileCount": 3, "integrity": "sha512-vWXy1Nfg7TPBSuAncfInmAI/WZDd5vOklyLJDdIRKABcZWojNDY0NJwruY2AcnCLnRJKSaBgf/GiJfauu8cQZA==", "signatures": [{"sig": "MEQCIHXUcwIgV8HnXqAmPRT28IMkMC0A2YG28gkv+trCiPzuAiBq1fqTsQmKanUcy0OVsk6JrTQ5luROHNXx/TR11531Bg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2260271}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "e60fb1c5d4e54ed5257495215eeda1bb43cf54ba", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.28.1_1733485519547_0.9198441896099427", "host": "s3://npm-registry-packages"}}, "4.29.0-0": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.29.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.29.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "504e754c7e5b3e3e200d92aa3cc9020bad7707d1", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.29.0-0.tgz", "fileCount": 3, "integrity": "sha512-+LT21b4iBeUv5aHAAGy5+L0XEiIDjTeJyz7VkeVUpFoVWe3x06T8mzVr0w3z5PGVjHT/Kg5DbRz0Uiz4SRWecA==", "signatures": [{"sig": "MEUCIExKMlAvXKq1tRDa8cEpi2zsuSn0toupLP0ZPr4zL2YeAiEA9+obOua1gtBjnqzC1ZbxlgdkXz0sUdswJA/QqT9wVwE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2264369}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "879d03d68890f365f880e30c69b58377b8743407", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.29.0-0_1734331217700_0.8703128796904793", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.0-1": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.29.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.29.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "e5fcaaf8886b91e69acd15065a352fff0147ccce", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.29.0-1.tgz", "fileCount": 3, "integrity": "sha512-Tnpk1N1QQE1JUxCmUcbQXXcKti+hZzAoyw91EbUrFkV7wKIRnbqZWw2aqvtfzWcbiE6yiFTCdxy1njCNhblfIQ==", "signatures": [{"sig": "MEUCIHmKHlnwhoJ24EvxABQsDO6vMGujdiHLcXCI+o7IEPLsAiEAn9pInwPPRxPWPmFWjXSMhHTA6Q7zdNe2iiiJk4Vnjco=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2264369}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "fa5064084196636acd98263f95ffea59f8362e32", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.29.0-1_1734590272670_0.0769110719913575", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.0-2": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.29.0-2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.29.0-2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "bcf32227dba7b82a7baafd52374178f2af02d9bd", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.29.0-2.tgz", "fileCount": 3, "integrity": "sha512-DD1DAX1adhCYcY/1rZRDBaEXcq6f0erCqhQj8EVwEfsH3rzWG5SOZ3o+eN8+B7Chl6lyasBP1wqxUo3IwWN1sg==", "signatures": [{"sig": "MEUCIQCYbrkAIRSJ+PjYRZVVQELmmDlmGsX0txnky2lX7PtVIwIgWaXae7d7ocxwPxKe0UNGOCIns1/JJEJvF9gSdr4Owz8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2264369}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "bbb7e7b1d4e208a923b0f18ceb8dd886838e1a01", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.29.0-2_1734677786361_0.028980780380934323", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.0": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.29.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.29.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "47ee954365e37dc9fda91b101aa4a29f20ee3b13", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.29.0.tgz", "fileCount": 3, "integrity": "sha512-KGuQ8WGhnq09LR7eOru7P9jfBSYXTMhq6TyavWfmEo+TxvkvuRwOCee5lPIa6HYjblOuFr4GeOxSE0c8iyw2Fg==", "signatures": [{"sig": "MEUCIQDblT70mWlg1Znh9Qqz0vkvgIfEKIeY/oRYwEzvUuXavgIgV3I3VomDyGNDILxypqv0MfrLYtB3EvvDKKgWjX20oTc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2264367}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "dadd4882c4984d7875af799ad56e506784d50e1c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.29.0_1734719868596_0.4969515509043996", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.1": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.29.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.29.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "64be13d51852ec1e2dfbd25d997ed5f42f35ea6d", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.29.1.tgz", "fileCount": 3, "integrity": "sha512-G5pn0NChlbRM8OJWpJFMX4/i8OEU538uiSv0P6roZcbpe/WfhEO+AT8SHVKfp8qhDQzaz7Q+1/ixMy7hBRidnQ==", "signatures": [{"sig": "MEQCICvYpbnL7fCHX4uO5YWcCBRuv6nPKeGey+COHK5vdLn7AiAErADOmbfm+oifv2TcX6b6nI0GJAAN86nDAjSOVXcnlQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2264367}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "5d3777803404c67ce14c62b8b05d6e26e46856f5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.29.1_1734765386292_0.8226332994453702", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.0-0": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.30.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.30.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "1d8698ad7a717be5c832e997cb12c25e2a83bde5", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.30.0-0.tgz", "fileCount": 3, "integrity": "sha512-RJAbO1YICdcIKF640pURqJk5gwPYWNUD/avwS6g8l0CpIrZzVVg8Rz/j3KyNCz8lf8wBedbf3XVjwgkhX+mxOA==", "signatures": [{"sig": "MEUCIQDg+8cQV6JKLsav1VFnqszQ/vAUdig4FrmoiOKrwdwy0AIgAtUmyprbuRMExju6bbmWtTsfoG7vrtMwd6jpH8mILWk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2264369}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "2339f1d8384a8999645823f83f9042a9fc7b3bf7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.30.0-0_1734765457904_0.5824841400195031", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.0-1": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.30.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.30.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "8a09fb351dffc83d9ac8d23f38358f2affed1c1c", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.30.0-1.tgz", "fileCount": 3, "integrity": "sha512-5vGqgAwkp2pZ65so2Im5wpPOTEhTlpuTYOtg3/pdL6C/qTJtk6F3uK5K/2r0Kk4TohoM6tV8OzTLd8MXcivOww==", "signatures": [{"sig": "MEUCIQDF1rwSmqyWWWnH9owqfzAluBnaxwqIZ/fKVrv/cuc8vgIgUxuPSzZbDEf88juwrOHVNpkX57R2sGsNo/nVHw7XtAo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2268465}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "41ab39a6e4a5181e9be21e816dd6f11c57e1c52a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.30.0-1_1735541561209_0.1948802894079189", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.2": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.29.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.29.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "1edee7a06538597720c4bf8178367d4b5651717d", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.29.2.tgz", "fileCount": 3, "integrity": "sha512-YgikssQ5UNq1GoFKZydMEkhKbjlUq7G3h8j6yWXLBF24KyoA5BcMtaOUAXq5sydPmOPEqB6kCyJpyifSpCfQ0w==", "signatures": [{"sig": "MEUCIF6upzFiom1QwwkzB8W87Td+qlF2rjDv1fGjKy7PbYgCAiEA5sdOLanYReSYb/SovteMCC+Rmd/faBl+yMQ+sCKl8aY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2268463}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "f5c349e5bb4cb40b0cc1a1b2a3fb5de415946406", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.29.2_1736078887613_0.9769024863697857", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.0": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.30.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.30.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "25ab4a6dbcbd27f4a68382f7963363f886a237aa", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.30.0.tgz", "fileCount": 3, "integrity": "sha512-U0xcC80SMpEbvvLw92emHrNjlS3OXjAM0aVzlWfar6PR0ODWCTQtKeeB+tlAPGfZQXicv1SpWwRz9Hyzq3Jx3g==", "signatures": [{"sig": "MEQCIHBhzNb2ouItR01YM7z8C009iHXJgrSlKJcmm5EVnDhLAiBpydIaeACiDZgqmnd2o3vU2MEu93OiIyfLkTCd0ib3Aw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2268463}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "958d5ebabd49297e9a4b78ad34ac0a0132305dea", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.30.0_1736145421649_0.9035224570401574", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.1": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.30.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.30.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "7d0d40cee7946ccaa5a4e19a35c6925444696a9e", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.30.1.tgz", "fileCount": 3, "integrity": "sha512-0WKLaAUUHKBtll0wvOmh6yh3S0wSU9+yas923JIChfxOaaBarmb/lBKPF0w/+jTVozFnOXJeRGZ8NvOxvk/jcw==", "signatures": [{"sig": "MEQCIADseo7h9t0FxRCRaISItBityrZ1kNsg2OUwAJnP5JI9AiBrwaJ3XGBMAWiWC6RF4yazNkOoLiJnfAWUXGBDTxGKmg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2269903}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "94917087deb9103fbf605c68670ceb3e71a67bf7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.30.1_1736246175401_0.5473205263068421", "host": "s3://npm-registry-packages-npm-production"}}, "4.31.0-0": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.31.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.31.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "9fe52457c1f108da978cc17c5e19e1694110cb42", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.31.0-0.tgz", "fileCount": 3, "integrity": "sha512-RmHOwQOvhXTv+FSEwRJuirKnCalc7mTHlV3leCnXqLbn72zDeGVwQcrMR0PjwOhUHFuAKQU0H8pB2nG9aQAMXQ==", "signatures": [{"sig": "MEYCIQCzhc1hdIWS8NCMxSG8XPYqpwGav1Mj6JRIvGFJMdO3xAIhANdqN1Y3BH9gmoyLr/C+z9mC+2u+8GRMgdnutmk140gG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2306729}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "8c80d5f657f0777d14bd75d446fee3fa4b7639fc", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.31.0-0_1736834285520_0.3340705966194071", "host": "s3://npm-registry-packages-npm-production"}}, "4.31.0": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.31.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.31.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "7bc75c4f22db04d3c972f83431739cfa41c6a36e", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.31.0.tgz", "fileCount": 3, "integrity": "sha512-wal2Tc8O5lMBtoePLBYRKj2CImUCJ4UNGJlLwspx7QApYny7K1cUYlzQ/4IGQBLmm+y0RS7dwc3TDO/pmcneTw==", "signatures": [{"sig": "MEUCIB+ch0iJSF5ZyAUn6Jnmv76cCfVgA1l036cv+7TigDRFAiEA9tSFbN0H7lFOtonX8XqrzUIxPYSdsWuht/krolYZVwE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2319015}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "15c264d59e0768b7d283a7bb8ded0519d1b5199e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.31.0_1737291430233_0.5853848452772297", "host": "s3://npm-registry-packages-npm-production"}}, "4.32.0": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.32.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.32.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "19fce2594f9ce73d1cb0748baf8cd90a7bedc237", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.32.0.tgz", "fileCount": 3, "integrity": "sha512-qhlXeV9AqxIyY9/R1h1hBD6eMvQCO34ZmdYvry/K+/MBs6d1nRFLm6BOiITLVI+nFAAB9kUB6sdJRKyVHXnqZw==", "signatures": [{"sig": "MEUCIQChgFcNRsxfFx5bcOX3vnT9r4Uq18BiGO3WrYBXWDIEuwIgCdn6yoviaN/dNGtzTOTuh27TTUewkYhl0YJu7bbBw2s=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2323111}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "2538304efdc05ecb7c52e6376d5777565139f075", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.32.0_1737707277420_0.3059619227965904", "host": "s3://npm-registry-packages-npm-production"}}, "4.33.0-0": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.33.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.33.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "84a585f407b15323ade754333d7a020e03cd51f0", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.33.0-0.tgz", "fileCount": 3, "integrity": "sha512-kzin5hFavYLJEx0H6MzVb7/NfMee3phh/LDR7X00JEY8GZibFmuIZOPNWx8l+11cmU18ESyoiG0QDJALVPjqPA==", "signatures": [{"sig": "MEQCIAZcD/GAt9SHRNFSunSBvdhLpja/C5i3g6jFu6t1zItbAiAKJPGfWq7XOAeWBB0v/4xC81vKFIhTYe8BZhr9CdtE5w==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2323113}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "f854e1988542d09f9691923eddd80888e92240d3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.33.0-0_1738053031514_0.7992738252944416", "host": "s3://npm-registry-packages-npm-production"}}, "4.32.1": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.32.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.32.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "bea4fd8ad190e9bc1d11efafa2efc9d121f50b96", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.32.1.tgz", "fileCount": 3, "integrity": "sha512-iWswS9cIXfJO1MFYtI/4jjlrGb/V58oMu4dYJIKnR5UIwbkzR0PJ09O0PDZT0oJ3LYWXBSWahNf/Mjo6i1E5/g==", "signatures": [{"sig": "MEUCIAjfp6EArYY0DokgRTrWA18iXjlQG3+J7Rk2YxiGuAAdAiEAlzETkY6CLBGgR0szBLr0HUrFWbTQ5LKFs4w4BvDQxt0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2323111}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "abcf4febe11f3d313fae41ddca35fc60670b9ff8", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.32.1_1738053219179_0.7076693474076665", "host": "s3://npm-registry-packages-npm-production"}}, "4.33.0": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.33.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.33.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "2d74a989f48546048302714c631479cbf09e0224", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.33.0.tgz", "fileCount": 3, "integrity": "sha512-WQltfx27zYsp2miNbI65bgKFuOvBzHzJvUxWeO8+dGmudmvdGq5mPfGhVBjJBdX6BWOZrPtkLftOSlIllKNARQ==", "signatures": [{"sig": "MEYCIQDJiJOFstOHEQQHDgf4ZtPq9VyoRpr04PJ0DWl0/xxg7AIhANm5wTiaqgX5wDGiK/mBoi0QerCAOOXvoMCiwD0XEirJ", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2308767}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "494483e8df7b5d04796b30e37f54d7e96fa91a97", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.33.0_1738393943747_0.80527498468314", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.0": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.34.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.34.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "8441dc29c13e2d03a510e59f3e5b1bfab2542071", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.34.0.tgz", "fileCount": 3, "integrity": "sha512-B1Oqt3GLh7qmhvfnc2WQla4NuHlcxAD5LyueUi5WtMc76ZWY+6qDtQYqnxARx9r+7mDGfamD+8kTJO0pKUJeJA==", "signatures": [{"sig": "MEQCICstMVRU8/gSDYVW7faJ+syl2EUXeuD8SIMVN3w1JZ3sAiA+4Sgwn1ieJZpuFx4JtKTYC+CFJht2El8ACrV2e23nLA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2308767}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "979d62888dbe75f92e50fdd64246c737c52f5f1f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.34.0_1738399245252_0.24625390198150288", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.1": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.34.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.34.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "1d917841458ccc34dcc34be32ea0a4b33437370f", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.34.1.tgz", "fileCount": 3, "integrity": "sha512-4uJb9qz7+Z/yUp5RPxDGGGUcoh0PnKF33QyWgEZ3X/GocpWb6Mb+skDh59FEt5d8+Skxqs9mng6Swa6B2AmQZg==", "signatures": [{"sig": "MEQCIBDY5s55eeTamhUZSP+CATeXVUjs3w9m70uEEdZhVMsvAiBCRBg9PQpYCCQL+UHrlgC81cZ5e6z6SH7X3+1AgoxghQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2308767}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "0f20524ad9ecd166a900d43af93f05a3405d2a45", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.34.1_1738565916010_0.10354245358109804", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.2": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.34.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.34.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "130cfaaceffd581dad94e93102a6d34eded10eb4", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.34.2.tgz", "fileCount": 3, "integrity": "sha512-bSQijDC96M6PuooOuXHpvXUYiIwsnDmqGU8+br2U7iPoykNi9JtMUpN7K6xml29e0evK0/g0D1qbAUzWZFHY5Q==", "signatures": [{"sig": "MEQCICcYxvP1b+bBzbm1ASP3P8T9wL+OcTHEg+sAGJGx0l/aAiBanjgSBcWN9g75Qup/VC5Vt5uRTDvJKBDZohKOfZGj0A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2308767}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "615efa045779fae70c4fd5fe64fdb08a039c0442", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.34.2_1738656626190_0.27764387491657505", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.3": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.34.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.34.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "c57b3e2c12969586f3513295cb36da96746edbf6", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.34.3.tgz", "fileCount": 3, "integrity": "sha512-YgD0DnZ3CHtvXRH8rzjVSxwI0kMTr0RQt3o1N92RwxGdx7YejzbBO0ELlSU48DP96u1gYYVWfUhDRyaGNqJqJg==", "signatures": [{"sig": "MEUCIQCNFX6jFHnurusvda620Dz3LK/1wFf/2LskKD7OM6cg9gIgHBta96qc2Mwx4xP9C49ujkxsRy79Q946sSwFcuKzK2I=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2308767}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "ac8b06a2b5406f694c38c416912cc2b18ba13355", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.34.3_1738747348725_0.7742268620961259", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.4": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.34.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.34.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "ec9fce2a5d221517a4aad8e657ee2951d4709e8d", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.34.4.tgz", "fileCount": 3, "integrity": "sha512-wcpCLHGM9yv+3Dql/CI4zrY2mpQ4WFergD3c9cpRowltEh5I84pRT/EuHZsG0In4eBPPYthXnuR++HrFkeqwkA==", "signatures": [{"sig": "MEQCIG4xXdLIDxar9whiJAcbfgUSyNGDHvZMAPZ42gW4JyjKAiAcj6ljgMQuu2wK0fNKZfwa1RnoWssT2oca56alqAva2w==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2308767}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "19312a762c3cda56a0f6dc80a0887a4499db2257", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.34.4_1738791095373_0.3016039382954334", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.5": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.34.5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.34.5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "642fd9af7fc9bd7bb4b9afde39460c48839348a4", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.34.5.tgz", "fileCount": 3, "integrity": "sha512-XtD/oMhCdixi3x8rCNyDRMUsLo1Z+1UQcK+oR7AsjglGov9ETiT3TNFhUPzaGC1jH+uaMtPhxrVRUub+pnAKTg==", "signatures": [{"sig": "MEYCIQCgoqTYMPF5Yb5LEdbKOTviQLvDGyZVjf7vWGe01c2xMAIhAKiUNc8WWCjZKn9ME0d2pBUvJXdu508c6Ry7BYeSBzzD", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2308767}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "3426b026e95319048dd5b703f2a0330c1c924e52", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.34.5_1738918406273_0.7421888551767348", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.6": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.34.6", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.34.6", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "9c158360abf6e6f7794285642ba0898c580291f6", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.34.6.tgz", "fileCount": 3, "integrity": "sha512-aK+Zp+CRM55iPrlyKiU3/zyhgzWBxLVrw2mwiQSYJRobCURb781+XstzvA8Gkjg/hbdQFuDw44aUOxVQFycrAg==", "signatures": [{"sig": "MEUCIQC3aEAgZ+pm77+8wLKvgGJYLf8YEZfnOqeh46QtsHaQrQIgQIn4YwdT/DHj/VjM0POBpIOKvpb84TOhIJGohfZIV48=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2296503}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "4b8745922d37d8325197d5a6613ffbf231163c7d", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.34.6_1738945950970_0.3549268995712218", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.7": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.34.7", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.34.7", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "0df84ce2bea48ee686fb55060d76ab47aff45c4c", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.34.7.tgz", "fileCount": 3, "integrity": "sha512-rB+ejFyjtmSo+g/a4eovDD1lHWHVqizN8P0Hm0RElkINpS0XOdpaXloqM4FBkF9ZWEzg6bezymbpLmeMldfLTw==", "signatures": [{"sig": "MEUCIQCaERKe6udDdlIhEzZmXYmMiDkTXqS16auHHeEh6lNGsgIgV+kOBBKrl2vYRg6wx+Jf9pPWD4ujOyDSt9SUn9l5HD4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2288311}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "f9c52f80074e33f5b0799e8ca215e3bfac7d2755", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.34.7_1739526862047_0.6643302086224445", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.8": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.34.8", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.34.8", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "ef5dc37f4388f5253f0def43e1440ec012af204d", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.34.8.tgz", "fileCount": 3, "integrity": "sha512-xAQCAHPj8nJq1PI3z8CIZzXuXCstquz7cIOL73HHdXiRcKk8Ywwqtx2wrIy23EcTn4aZ2fLJNBB8d0tQENPCmw==", "signatures": [{"sig": "MEYCIQCcTEMxJremdsZ7e8mmp9UTvURacr2cxskp82YdcN5KugIhAMiHYTt/8vrItVo0szsazSjpxqcCL1Kke/Z8Pe7A5Mlm", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2288311}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "8f667b7c15b176728449a4917cb29fe5ee3a1c0c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.34.8_1739773607634_0.5162880431693522", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.9": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.34.9", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.34.9", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "10f5d7349fbd2fe78f9e36ecc90aab3154435c8d", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.34.9.tgz", "fileCount": 3, "integrity": "sha512-Z2i0Uy5G96KBYKjeQFKbbsB54xFOL5/y1P5wNBsbXB8yE+At3oh0DVMjQVzCJRJSfReiB2tX8T6HUFZ2k8iaKg==", "signatures": [{"sig": "MEYCIQCOnAif4ZCorF1/Y/uC2ZamSF5BDDbRNV2OKiiKFvmEOQIhAOr2CZ9R+WxenOITve4IFTJWG37c0JF6bV7GWcDfsvWO", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2423719}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "0ab9b9772e24dfe9ef08bfce3132e99a15b793f6", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.34.9_1740814380716_0.3488345422250696", "host": "s3://npm-registry-packages-npm-production"}}, "4.35.0": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.35.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.35.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "78dde3e6fcf5b5733a97d0a67482d768aa1e83a5", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.35.0.tgz", "fileCount": 3, "integrity": "sha512-s91fuAHdOwH/Tad2tzTtPX7UZyytHIRR6V4+2IGlV0Cej5rkG0R61SX4l4y9sh0JBibMiploZx3oHKPnQBKe4g==", "signatures": [{"sig": "MEQCIEnT41kV/aOf4niYxJLRs4FcQ+JBkYgkUq5cGKcMTlv/AiAp45FXJ3xZ3Az6fATpBu2QFB3q+74LZi3jI3VSH5kREw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2436007}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "70ef1cce7c740030cc2935b563d13950cc1511f5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.35.0_1741415110868_0.9315040112674782", "host": "s3://npm-registry-packages-npm-production"}}, "4.36.0": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.36.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.36.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "05eb5e71db5b5b1d1a3428265a63c5f6f8a1e4b8", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.36.0.tgz", "fileCount": 3, "integrity": "sha512-ttE6ayb/kHwNRJGYLpuAvB7SMtOeQnVXEIpMtAvx3kepFQeowVED0n1K9nAdraHUPJ5hydEMxBpIR7o4nrm8uA==", "signatures": [{"sig": "MEQCID0KVbV1nWe/PflpxWv3BUOCIvRohmSNQQKjXsVhig4tAiBgALU2TLnUFc82FYzucVRcA7WIfFW7j+VEiznjZiwoXQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2431911}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "ab7bfa8fe9c25e41cc62058fa2dcde6b321fd51d", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.36.0_1742200568840_0.8468157884977692", "host": "s3://npm-registry-packages-npm-production"}}, "4.37.0": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.37.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.37.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "9677e39288ccc91ebcd707cdd794732d701cd174", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.37.0.tgz", "fileCount": 3, "integrity": "sha512-PpWwHMPCVpFZLTfLq7EWJWvrmEuLdGn1GMYcm5MV7PaRgwCEYJAwiN94uBuZev0/J/hFIIJCsYw4nLmXA9J7Pw==", "signatures": [{"sig": "MEUCIQC9OJPA6bdKVm1j1uiOXAVXFm2BWqWKqyVI7frH1h2F4gIgTt7pPi7h1LYgY9cBTG6uI7rX0AWLV51dG44MT/lOp5M=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2436007}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "8b1c634d945dda9294cf579de68c4b223c618e7f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.37.0_1742741848236_0.7862154237415964", "host": "s3://npm-registry-packages-npm-production"}}, "4.38.0": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.38.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.38.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "8cb565417b29851a0c549614898bdab689f23187", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.38.0.tgz", "fileCount": 3, "integrity": "sha512-GEAIabR1uFyvf/jW/5jfu8gjM06/4kZ1W+j1nWTSSB3w6moZEBm7iBtzwQ3a1Pxos2F7Gz+58aVEnZHU295QTg==", "signatures": [{"sig": "MEUCIGlS4dZ6mjDHRtKw75RxOAx5X8y4p/CNy+juPfCtpk25AiEA+bp7x6YC7L0/5rdvnNGz7OqWiXHBoCpch7a7veMz2PM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2444199}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "22b64bcc511dfc40ce463e3f662a928915908713", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.38.0_1743229771855_0.6970447182522701", "host": "s3://npm-registry-packages-npm-production"}}, "4.39.0": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.39.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.39.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "4bab37353b11bcda5a74ca11b99dea929657fd5f", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.39.0.tgz", "fileCount": 3, "integrity": "sha512-t7j5Zhr7S4bBtksT73bO6c3Qa2AV/HqiGlj9+KB3gNF5upcVkx+HLgxTm8DK4OkzsOYqbdqbLKwvGMhylJCPhQ==", "signatures": [{"sig": "MEQCIDZA8ufguo+ocKQI/YPyUwWMi9a4j2iK/Y4TBg6L++oZAiA0fAatusI4fB0jEqWLhgUiGAyAhXIKw0kzsTd7C9vfOA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2444199}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "5c001245779063abac3899aa9d25294ab003581b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.39.0_1743569396808_0.758512911750906", "host": "s3://npm-registry-packages-npm-production"}}, "4.40.0": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.40.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.40.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "5812a1a7a2f9581cbe12597307cc7ba3321cf2f3", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.40.0.tgz", "fileCount": 3, "integrity": "sha512-uJkYTugqtPZBS3Z136arevt/FsKTF/J9dEMTX/cwR7lsAW4bShzI2R0pJVw+hcBTWF4dxVckYh72Hk3/hWNKvA==", "signatures": [{"sig": "MEYCIQDJO3H4v4q6eJzLfHrn+S5vgk/8hWhqbS9VPNF7NOOwSAIhAL3ncBmUQgLMxtuKVBmC1r2RAD07WZpIAAx32F5n1RtA", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2464215}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "1f2d579ccd4b39f223fed14ac7d031a6c848cd80", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.40.0_1744447199161_0.9412641142642977", "host": "s3://npm-registry-packages-npm-production"}}, "4.40.1": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.40.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.40.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "9336fd5e47d7f4760d02aa85f76976176eef53ca", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.40.1.tgz", "fileCount": 3, "integrity": "sha512-EQSP+8+1VuSulm9RKSMKitTav89fKbHymTf25n5+Yr6gAPZxYWpj3DzAsQqoaHAk9YX2lwEyAf9S4W8F4l3VBQ==", "signatures": [{"sig": "MEUCIQCcBhgztz347NcsyYnXNd8Yv6rlKAjUJGctio1f2NikFgIgfWqGbZrFChmimrJplaynRpeH/L2+2AvZi1f4uQSc3Ic=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2447855}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "1e6c40f49c428b7657fe3b9a2026f705acd39da1", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.40.1_1745814947291_0.5888830920453632", "host": "s3://npm-registry-packages-npm-production"}}, "4.40.2": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.40.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.40.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "4b3b8e541b7b13e447ae07774217d98c06f6926d", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.40.2.tgz", "fileCount": 3, "integrity": "sha512-QNU7BFHEvHMp2ESSY3SozIkBPaPBDTsfVNGx3Xhv+TdvWXFGOSH2NJvhD1zKAT6AyuuErJgbdvaJhYVhVqrWTg==", "signatures": [{"sig": "MEQCIFxXUG4YzwLM2U4wYHqY1OJ2Qkbrm9IX7ciTgbEn0cT7AiAWUDJA4Azqa4eF+1439Ati75jyJCJwBtkGPhufavxo0Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2460143}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "02da7efedcf373f0f819b78e3acbe50de05d9a5b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.40.2_1746516438200_0.9586291158595117", "host": "s3://npm-registry-packages-npm-production"}}, "4.41.0": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.41.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.41.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "8ef6f680d011b95a2f6546c6c31a37a33138035f", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.41.0.tgz", "fileCount": 3, "integrity": "sha512-TWrZb6GF5jsEKG7T1IHwlLMDRy2f3DPqYldmIhnA2DVqvvhY2Ai184vZGgahRrg8k9UBWoSlHv+suRfTN7Ua4A==", "signatures": [{"sig": "MEUCIC3HC1DDpjxEw1fB2nwr5SL2ASN2TTbg7JdKGXGNZMCnAiEAn8hK5frGlDngYVQ+mDlotp5bl0k6gFpCA816LZ3BkjY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2647119}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "0928185cd544907dab472754634ddf988452aae6", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.41.0_1747546433147_0.5436757751687813", "host": "s3://npm-registry-packages-npm-production"}}, "4.41.1": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.41.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.41.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "4610dbd1dcfbbae32fbc10c20ae7387acb31110c", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.41.1.tgz", "fileCount": 3, "integrity": "sha512-3rwCIh6MQ1LGrvKJitQjZFuQnT2wxfU+ivhNBzmxXTXPllewOF7JR1s2vMX/tWtUYFgphygxjqMl76q4aMotGw==", "signatures": [{"sig": "MEUCIQDo2KVQgfexS3c6uJjX+XOIDFtUdjWHb7dIZGvZ803nCwIgaggQ1jDKzmJsyiQHAH9jZBWchd8ztj2xT8a+wWrr+S0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2737447}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "7c469dc4eb8e1cb6def9fdc04581fdfce9975da3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.41.1_1748067300320_0.03666429814569949", "host": "s3://npm-registry-packages-npm-production"}}, "4.41.2": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.41.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.41.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "5a20f8025e962a711887fbb02e409889b3dc3694", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.41.2.tgz", "fileCount": 3, "integrity": "sha512-/9TBJIyoervs137gteTbzOSsIc/Io6xnXvjXYTEEqDRi3a9OokdBwfllN8AmPFGcU+WzoAiTc6n9SJiPohJ3CQ==", "signatures": [{"sig": "MEUCIQDudJJtsoL631EKkKsQkqVI2oWc2kxek5/q283xb3WCMgIgSHezw/8/9OUIboHb35obLWjjVIJRLRdUzTMvUsS3MJQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2745639}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "13b4669dbc21cb738551cd725d2a18c77b3cea11", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.41.2_1749210057403_0.2630362872114491", "host": "s3://npm-registry-packages-npm-production"}}, "4.42.0": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.42.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.42.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "e46e2d1125957694bfb5222ecd63dd6c9bd69682", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.42.0.tgz", "fileCount": 3, "integrity": "sha512-KQETDSEBamQFvg/d8jajtRwLNBlGc3aKpaGiP/LvEbnmVUKlFta1vqJqTrvPtsYsfbE/DLg5CC9zyXRX3fnBiA==", "signatures": [{"sig": "MEQCIFmFPK9ZK6EJXNqer7vBTrvZmqTWQmTJG1oy4OYlFtSbAiAjYZYUfXJGbnm+JApbpSRy1NIW2pcsGpWkAetlz9mYRA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2745639}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "f76339428586620ff3e4c32fce48f923e7be7b05", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.42.0_1749221317104_0.9918260013885105", "host": "s3://npm-registry-packages-npm-production"}}, "4.43.0": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.43.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-gnu@4.43.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "e7df825d71daefa7037605015455aa58be43cd7a", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.43.0.tgz", "fileCount": 3, "integrity": "sha512-XXKvo2e+wFtXZF/9xoWohHg+MuRnvO29TI5Hqe9xwN5uN8NKUYy7tXUG3EZAlfchufNCTHNGjEx7uN78KsBo0g==", "signatures": [{"sig": "MEYCIQCY23eIa6Qg3mzDs6UU3RkNPKyHhrVqTDr7yoPCVsgL2gIhANcxQjgvugzQS06OQq3jPcICwLo9kxpWTDsDNQ220Ojt", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2745639}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "gitHead": "72858cb1474b81c91902794ab7d28c79f34b8ca8", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-gnu_4.43.0_1749619385808_0.6490120556743464", "host": "s3://npm-registry-packages-npm-production"}}, "4.44.0": {"name": "@rollup/rollup-linux-riscv64-gnu", "version": "4.44.0", "os": ["linux"], "cpu": ["riscv64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-riscv64-gnu.node", "_id": "@rollup/rollup-linux-riscv64-gnu@4.44.0", "gitHead": "fa4b2842c823f6a61f6b994a28b7fcb54419b6c6", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-vV3cL48U5kDaKZtXrti12YRa7TyxgKAIDoYdqSIOMOFBXqFj2XbChHAtXquEn2+n78ciFgr4KIqEbydEGPxXgA==", "shasum": "028708f73c8130ae924e5c3755de50fe93687249", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.44.0.tgz", "fileCount": 3, "unpackedSize": 2737447, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQD17KqzJcAMlttGX1AR2LRCwZr8obY1rKGYOAR4mljLcAIgFGHynoGPeiX7KxXM1xpxnztbFeP3uoQCBmZrs2SI2cQ="}]}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>", "actor": {"name": "lukastaegert", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-riscv64-gnu_4.44.0_1750314203732_0.5114221586001964"}, "_hasShrinkwrap": false}}, "time": {"created": "2023-12-08T07:58:14.843Z", "modified": "2025-06-19T06:23:24.276Z", "4.7.0": "2023-12-08T07:58:15.177Z", "4.8.0": "2023-12-11T06:25:10.070Z", "4.9.0": "2023-12-13T09:24:30.729Z", "4.9.1": "2023-12-17T06:26:23.412Z", "4.9.2": "2023-12-30T06:23:39.160Z", "4.9.3": "2024-01-05T06:20:58.365Z", "4.9.4": "2024-01-06T06:39:12.362Z", "4.9.5": "2024-01-12T06:16:25.294Z", "4.9.6": "2024-01-21T05:52:30.718Z", "4.10.0": "2024-02-10T05:58:53.554Z", "4.11.0": "2024-02-15T06:09:57.208Z", "4.12.0": "2024-02-16T13:32:37.425Z", "4.12.1": "2024-03-06T06:03:52.257Z", "4.13.0": "2024-03-12T05:28:57.018Z", "4.13.1-1": "2024-03-24T07:39:37.717Z", "4.13.1": "2024-03-27T10:27:56.731Z", "4.13.2": "2024-03-28T14:13:52.158Z", "4.14.0": "2024-04-03T05:23:08.005Z", "4.14.1": "2024-04-07T07:35:52.267Z", "4.14.2": "2024-04-12T06:23:53.139Z", "4.14.3": "2024-04-15T07:18:46.454Z", "4.15.0": "2024-04-20T05:37:30.611Z", "4.16.0": "2024-04-21T04:42:33.939Z", "4.16.1": "2024-04-21T18:30:18.260Z", "4.16.2": "2024-04-22T15:19:36.031Z", "4.16.3": "2024-04-23T05:12:49.034Z", "4.16.4": "2024-04-23T13:15:21.764Z", "4.17.0": "2024-04-27T11:30:07.070Z", "4.17.1": "2024-04-29T04:58:09.114Z", "4.17.2": "2024-04-30T05:01:05.002Z", "4.18.0": "2024-05-22T05:04:00.618Z", "4.18.1": "2024-07-08T15:25:28.689Z", "4.19.0": "2024-07-20T05:46:30.656Z", "4.19.1": "2024-07-27T04:54:16.403Z", "4.19.2": "2024-08-01T08:33:09.644Z", "4.20.0": "2024-08-03T04:49:07.163Z", "4.21.0": "2024-08-18T05:55:49.064Z", "4.21.1": "2024-08-26T15:54:30.315Z", "4.21.2": "2024-08-30T07:04:42.010Z", "4.21.3": "2024-09-12T07:06:06.609Z", "4.22.0": "2024-09-19T04:55:47.482Z", "4.22.1": "2024-09-20T08:22:09.486Z", "4.22.2": "2024-09-20T09:34:01.178Z", "4.22.3-0": "2024-09-20T14:48:15.603Z", "4.22.3": "2024-09-21T05:03:26.592Z", "4.22.4": "2024-09-21T06:11:37.319Z", "4.22.5": "2024-09-27T11:48:33.226Z", "4.23.0": "2024-10-01T07:10:24.093Z", "4.24.0": "2024-10-02T09:37:37.984Z", "4.24.1": "2024-10-27T06:43:22.088Z", "4.24.2": "2024-10-27T15:40:29.371Z", "4.25.0-0": "2024-10-29T06:15:32.282Z", "4.24.3": "2024-10-29T14:14:30.075Z", "4.24.4": "2024-11-04T08:47:29.183Z", "4.25.0": "2024-11-09T08:37:43.257Z", "4.26.0": "2024-11-13T06:45:19.746Z", "4.27.0-0": "2024-11-13T07:03:33.529Z", "4.27.0-1": "2024-11-14T06:33:31.749Z", "4.27.0": "2024-11-15T10:40:56.179Z", "4.27.1-0": "2024-11-15T13:28:29.640Z", "4.27.1-1": "2024-11-15T15:38:24.569Z", "4.27.1": "2024-11-15T16:08:02.794Z", "4.27.2": "2024-11-15T17:20:25.611Z", "4.27.3": "2024-11-18T16:39:57.328Z", "4.27.4": "2024-11-23T07:00:40.405Z", "4.28.0": "2024-11-30T13:16:08.388Z", "4.28.1": "2024-12-06T11:45:19.752Z", "4.29.0-0": "2024-12-16T06:40:17.928Z", "4.29.0-1": "2024-12-19T06:37:52.927Z", "4.29.0-2": "2024-12-20T06:56:26.602Z", "4.29.0": "2024-12-20T18:37:48.845Z", "4.29.1": "2024-12-21T07:16:26.527Z", "4.30.0-0": "2024-12-21T07:17:38.151Z", "4.30.0-1": "2024-12-30T06:52:41.421Z", "4.29.2": "2025-01-05T12:08:07.880Z", "4.30.0": "2025-01-06T06:37:01.870Z", "4.30.1": "2025-01-07T10:36:15.630Z", "4.31.0-0": "2025-01-14T05:58:05.743Z", "4.31.0": "2025-01-19T12:57:10.477Z", "4.32.0": "2025-01-24T08:27:57.634Z", "4.33.0-0": "2025-01-28T08:30:31.729Z", "4.32.1": "2025-01-28T08:33:39.389Z", "4.33.0": "2025-02-01T07:12:23.923Z", "4.34.0": "2025-02-01T08:40:45.503Z", "4.34.1": "2025-02-03T06:58:36.209Z", "4.34.2": "2025-02-04T08:10:26.463Z", "4.34.3": "2025-02-05T09:22:28.952Z", "4.34.4": "2025-02-05T21:31:35.601Z", "4.34.5": "2025-02-07T08:53:26.475Z", "4.34.6": "2025-02-07T16:32:31.297Z", "4.34.7": "2025-02-14T09:54:22.297Z", "4.34.8": "2025-02-17T06:26:47.928Z", "4.34.9": "2025-03-01T07:33:00.997Z", "4.35.0": "2025-03-08T06:25:11.155Z", "4.36.0": "2025-03-17T08:36:09.087Z", "4.37.0": "2025-03-23T14:57:28.473Z", "4.38.0": "2025-03-29T06:29:32.125Z", "4.39.0": "2025-04-02T04:49:57.085Z", "4.40.0": "2025-04-12T08:39:59.358Z", "4.40.1": "2025-04-28T04:35:47.508Z", "4.40.2": "2025-05-06T07:27:18.450Z", "4.41.0": "2025-05-18T05:33:53.381Z", "4.41.1": "2025-05-24T06:15:00.553Z", "4.41.2": "2025-06-06T11:40:57.699Z", "4.42.0": "2025-06-06T14:48:37.325Z", "4.43.0": "2025-06-11T05:23:06.076Z", "4.44.0": "2025-06-19T06:23:23.984Z"}, "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "homepage": "https://rollupjs.org/", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "description": "Native bindings for Rollup", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "readme": "# `@rollup/rollup-linux-riscv64-gnu`\n\nThis is the **riscv64gc-unknown-linux-gnu** binary for `rollup`\n", "readmeFilename": "README.md"}