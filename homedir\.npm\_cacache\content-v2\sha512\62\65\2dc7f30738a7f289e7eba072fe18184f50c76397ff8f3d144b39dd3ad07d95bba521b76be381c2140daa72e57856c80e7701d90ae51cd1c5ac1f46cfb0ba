{"name": "call-bound", "dist-tags": {"latest": "1.0.4"}, "versions": {"1.0.1": {"name": "call-bound", "version": "1.0.1", "dependencies": {"call-bind": "^1.0.7", "get-intrinsic": "^1.2.4"}, "devDependencies": {"nyc": "^10.3.2", "gopd": "^1.1.0", "tape": "^5.9.0", "eslint": "=8.8.0", "evalmd": "^0.0.19", "encoding": "^0.1.13", "for-each": "^0.3.3", "npmignore": "^0.3.1", "in-publish": "^2.0.1", "typescript": "next", "@types/tape": "^5.6.5", "auto-changelog": "^2.5.0", "object-inspect": "^1.13.3", "has-strict-mode": "^1.0.1", "@ljharb/tsconfig": "^0.2.0", "@types/call-bind": "^1.0.5", "es-value-fixtures": "^1.5.0", "safe-publish-latest": "^2.0.0", "@types/get-intrinsic": "^1.2.3", "@arethetypeswrong/cli": "^0.17.0", "@ljharb/eslint-config": "^21.1.1"}, "dist": {"shasum": "4b48be3bf3c0869dd8e5a8a7e071921d6d398495", "tarball": "https://registry.npmjs.org/call-bound/-/call-bound-1.0.1.tgz", "fileCount": 11, "integrity": "sha512-BsIq9Q4b/cM9JPRCYCeYrMwUC/UUgLTidqtVfaJTPH+L6bS+JYwI7mPwxw4Nl+4/SAkwZ2/6EYR6Cf3XIZ8+RQ==", "signatures": [{"sig": "MEYCIQDv4QWOSQGRR+LpdirXu7toGHMDNhf8WNQ6+o0EA0b2DgIhAOp94n00ERKbHkytiYOjT018sPe67U1Er5dsMO2zn6fH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10624}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "1.0.2": {"name": "call-bound", "version": "1.0.2", "dependencies": {"call-bind": "^1.0.8", "get-intrinsic": "^1.2.5"}, "devDependencies": {"nyc": "^10.3.2", "gopd": "^1.2.0", "tape": "^5.9.0", "eslint": "=8.8.0", "evalmd": "^0.0.19", "encoding": "^0.1.13", "for-each": "^0.3.3", "npmignore": "^0.3.1", "in-publish": "^2.0.1", "typescript": "next", "@types/tape": "^5.6.5", "auto-changelog": "^2.5.0", "object-inspect": "^1.13.3", "has-strict-mode": "^1.0.1", "@ljharb/tsconfig": "^0.2.2", "@types/call-bind": "^1.0.5", "es-value-fixtures": "^1.5.0", "safe-publish-latest": "^2.0.0", "@types/get-intrinsic": "^1.2.3", "@arethetypeswrong/cli": "^0.17.1", "@ljharb/eslint-config": "^21.1.1"}, "dist": {"shasum": "9dbd4daf9f5f753bec3e4c8fbb8a2ecc4de6c39b", "tarball": "https://registry.npmjs.org/call-bound/-/call-bound-1.0.2.tgz", "fileCount": 11, "integrity": "sha512-0lk0PHFe/uz0vl527fG9CgdE9WdafjDbCXvBbs+LUv000TVt2Jjhqbs4Jwm8gz070w8xXyEAxrPOMullsxXeGg==", "signatures": [{"sig": "MEYCIQC0XMlUsfMhHo2LGVXEUY26hPbc+O/MEekKNO7UlsNJbgIhAPgYO8gdlAP4DSthUbrZrMb6tMsVI06LwbDvAlhrCles", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11181}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "1.0.3": {"name": "call-bound", "version": "1.0.3", "dependencies": {"get-intrinsic": "^1.2.6", "call-bind-apply-helpers": "^1.0.1"}, "devDependencies": {"nyc": "^10.3.2", "gopd": "^1.2.0", "tape": "^5.9.0", "eslint": "=8.8.0", "evalmd": "^0.0.19", "encoding": "^0.1.13", "for-each": "^0.3.3", "npmignore": "^0.3.1", "in-publish": "^2.0.1", "typescript": "next", "@types/tape": "^5.6.5", "auto-changelog": "^2.5.0", "object-inspect": "^1.13.3", "has-strict-mode": "^1.0.1", "@ljharb/tsconfig": "^0.2.2", "@types/call-bind": "^1.0.5", "es-value-fixtures": "^1.5.0", "safe-publish-latest": "^2.0.0", "@types/get-intrinsic": "^1.2.3", "@arethetypeswrong/cli": "^0.17.1", "@ljharb/eslint-config": "^21.1.1"}, "dist": {"shasum": "41cfd032b593e39176a71533ab4f384aa04fd681", "tarball": "https://registry.npmjs.org/call-bound/-/call-bound-1.0.3.tgz", "fileCount": 11, "integrity": "sha512-YTd+6wGlNlPxSuri7Y6X8tY2dmm12UMH66RpKMhiX6rsk5wXXnYgbUcOt8kiS31/AjfoTOvCsE+w8nZQLQnzHA==", "signatures": [{"sig": "MEYCIQCkMn+LCstD0IdrnD/wyjFUcB86vjhezSMsjOTX7xrX6QIhAOJQnvBENPNMMsPT6aOr1VRII/Js/XSqTpGyogIcTL6E", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12021}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "1.0.4": {"name": "call-bound", "version": "1.0.4", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "get-intrinsic": "^1.3.0"}, "devDependencies": {"@arethetypeswrong/cli": "^0.17.4", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.3.0", "@types/call-bind": "^1.0.5", "@types/get-intrinsic": "^1.2.3", "@types/tape": "^5.8.1", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "es-value-fixtures": "^1.7.1", "eslint": "=8.8.0", "evalmd": "^0.0.19", "for-each": "^0.3.5", "gopd": "^1.2.0", "has-strict-mode": "^1.1.0", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.4", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "dist": {"integrity": "sha512-+ys997U96po4Kx/ABpBCqhA9EuxJaQWDQg7295H4hBphv3IZg0boBKuwYpt4YXp6MZ5AmZQnU/tyMTlRpaSejg==", "shasum": "238de935d2a2a692928c538c7ccfa91067fd062a", "tarball": "https://registry.npmjs.org/call-bound/-/call-bound-1.0.4.tgz", "fileCount": 11, "unpackedSize": 17106, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIDBV4HqqB+gGBZypN0mLd7alhxFA5D8ZGm+mBeCB1nyJAiEAyvuZQ/flopoGNRE9Azbsjp0bf/nB+HkBL8oVv7Od5Qg="}]}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}}, "modified": "2025-03-03T17:50:03.694Z"}