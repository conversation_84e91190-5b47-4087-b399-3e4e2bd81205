{"_id": "ignore-by-default", "_rev": "8-600c3d3de8b731cd799b97eca7803f40", "name": "ignore-by-default", "description": "A list of directories you should ignore by default", "dist-tags": {"latest": "2.1.0"}, "versions": {"1.0.0": {"name": "ignore-by-default", "version": "1.0.0", "description": "A list of directories you should ignore by default", "main": "index.js", "files": ["index.js"], "scripts": {"test": "standard && node test.js"}, "repository": {"type": "git", "url": "git+https://github.com/novemberborn/ignore-by-default.git"}, "keywords": ["ignore", "chokidar", "watcher", "exclude", "glob", "pattern"], "author": {"name": "<PERSON>", "url": "https://novemberborn.net/"}, "license": "ISC", "bugs": {"url": "https://github.com/novemberborn/ignore-by-default/issues"}, "homepage": "https://github.com/novemberborn/ignore-by-default#readme", "devDependencies": {"figures": "^1.4.0", "standard": "^5.4.1"}, "gitHead": "3216727f7e417ce435aa0b7ed62175e46d6c85bc", "_id": "ignore-by-default@1.0.0", "_shasum": "e2632c24a3d333687279bc3c86c4424309c0323c", "_from": ".", "_npmVersion": "3.7.1", "_nodeVersion": "5.5.0", "_npmUser": {"name": "novemberborn", "email": "<EMAIL>"}, "dist": {"shasum": "e2632c24a3d333687279bc3c86c4424309c0323c", "tarball": "https://registry.npmjs.org/ignore-by-default/-/ignore-by-default-1.0.0.tgz", "integrity": "sha512-bo1UP8HQN97mafg04Esa5Zi8uUFf0JZsqV6GW3X86RAUNM5kDNyVw7Xb6ADPYn0vqtsP0pVWu9X4d9OBoG1BcQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC0BXqCiiGV8KHgkCh0M4+eZuBW+t5rarXzAuG8l0WnfgIhAOfMlcX9GdSTwhkeTP/nPBH5wg6pphkD3z/WIJGJwbRb"}]}, "maintainers": [{"name": "novemberborn", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-9-west.internal.npmjs.com", "tmp": "tmp/ignore-by-default-1.0.0.tgz_1454695577738_0.20600721682421863"}, "directories": {}}, "1.0.1": {"name": "ignore-by-default", "version": "1.0.1", "description": "A list of directories you should ignore by default", "main": "index.js", "files": ["index.js"], "scripts": {"test": "standard && node test.js"}, "repository": {"type": "git", "url": "git+https://github.com/novemberborn/ignore-by-default.git"}, "keywords": ["ignore", "chokidar", "watcher", "exclude", "glob", "pattern"], "author": {"name": "<PERSON>", "url": "https://novemberborn.net/"}, "license": "ISC", "bugs": {"url": "https://github.com/novemberborn/ignore-by-default/issues"}, "homepage": "https://github.com/novemberborn/ignore-by-default#readme", "devDependencies": {"figures": "^1.4.0", "standard": "^6.0.4"}, "gitHead": "fa7ec97d8249ab6aab63cc1e46d14dd30fcb11ee", "_id": "ignore-by-default@1.0.1", "_shasum": "48ca6d72f6c6a3af00a9ad4ae6876be3889e2b09", "_from": ".", "_npmVersion": "3.7.5", "_nodeVersion": "5.7.0", "_npmUser": {"name": "novemberborn", "email": "<EMAIL>"}, "dist": {"shasum": "48ca6d72f6c6a3af00a9ad4ae6876be3889e2b09", "tarball": "https://registry.npmjs.org/ignore-by-default/-/ignore-by-default-1.0.1.tgz", "integrity": "sha512-Ius2VYcGNk7T90CppJqcIkS5ooHUZyIQK+ClZfMfMNFEF9VSE73Fq+906u/CWu92x4gzZMWOwfFYckPObzdEbA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA5ewAE1O6h8cmrjjmIeeHHwYJd4QhqJF2yz5hDQyBFgAiEAmFPHyJK8wwtif2pPRG5QvQu7v21FS8+YlNRAnPveSF0="}]}, "maintainers": [{"name": "novemberborn", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-9-west.internal.npmjs.com", "tmp": "tmp/ignore-by-default-1.0.1.tgz_1456498124456_0.1001870334148407"}, "directories": {}}, "2.0.0": {"name": "ignore-by-default", "version": "2.0.0", "description": "A list of directories you should ignore by default", "engines": {"node": ">=10 <11 || >=12 <13 || >=14"}, "main": "index.js", "scripts": {"test": "standard && node test.js"}, "repository": {"type": "git", "url": "git+https://github.com/novemberborn/ignore-by-default.git"}, "keywords": ["ignore", "chokidar", "watcher", "exclude", "glob", "pattern"], "author": {"name": "<PERSON>", "url": "https://novemberborn.net/"}, "license": "ISC", "bugs": {"url": "https://github.com/novemberborn/ignore-by-default/issues"}, "homepage": "https://github.com/novemberborn/ignore-by-default#readme", "devDependencies": {"figures": "^3.2.0", "standard": "^14.3.4"}, "gitHead": "520cf377b5589d1da67b354a944192e7783a05d7", "_id": "ignore-by-default@2.0.0", "_nodeVersion": "14.2.0", "_npmVersion": "6.14.5", "dist": {"integrity": "sha512-+mQSgMRiFD3L3AOxLYOCxjIq4OnAmo5CIuC+lj5ehCJcPtV++QacEV7FdpzvYxH6DaOySWzQU6RR0lPLy37ckA==", "shasum": "537092018540640459569fe7c8c7a408af581146", "tarball": "https://registry.npmjs.org/ignore-by-default/-/ignore-by-default-2.0.0.tgz", "fileCount": 4, "unpackedSize": 2987, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe5N3qCRA9TVsSAnZWagAAHj4QAJh5HdMiTJGXx5+ecv+I\nfbjh0VdR/yk181+vm20KfNQSqs2ApkmM/MESw4N6jRZ6kzsdLxG1Gk0GJXvC\nzq0pOHgANGSrM1HOwDH2cAyMJo4fHMyVzBf8jYgpuyTujvCab8k+WSXkLLEA\nzNqxiVxhCtp9BsrY9Kn4ts35rKFv/XOBE+Raw0laLoBneBZDtPtFvn0sLhPF\nBP9GA2P7HoNYtycJExsGLA/v6aOKj0wh6jq658+7KMB6YHV3iVNVSzwS4T+J\n5y4DtOHLxaDC8gp3ekMFaThTAqKR2+Kdk0GnUDhSwwae6a/x0tyQEKM3z6Y0\nzljgpgAlf6XgMgrHiNm/x3VpqWJJmmgMqGS2Xyegbo5qaqLgKkJnEA7LD9LF\nDlaIoqVZ24OmuLuzkKn96YiSX/fxpSIgqhH5+bF1MGtEDaasCXP1pbfhbtp8\npkcg1cQV5YU2tCzBmDwMarMOFGg594zWEUs5rsKWRbGwKUPXbXU8trHIxmv4\nJEgQOQo/pBnen2wqpmYcZdU8ZUrEuQRd0a0I83Ps1VTJwjddPi8Jzb2vQqb3\nQuDvUY3jbW5TmxNdslT4zEV10ySoK+OAbAbxk3vmAa/Bf23n6vwyUcKTqCI1\nL+TPiHE+maWjmgL9BbXX40sQ5QZYo0Uo+VAraiCMpSWjccjSn3cJENXXOsIM\ntjln\r\n=hIam\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCwW7cAPKkWXfEATXd/JF9Y0AWTMSTLbJUGm3825zlnvgIgNdxDcyQnUwMYrz3TF3KTk11GFawVD+Wm9iyMs8pm0YU="}]}, "maintainers": [{"name": "novemberborn", "email": "<EMAIL>"}], "_npmUser": {"name": "novemberborn", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ignore-by-default_2.0.0_1592057322215_0.19461620144736624"}, "_hasShrinkwrap": false}, "2.1.0": {"name": "ignore-by-default", "version": "2.1.0", "description": "A list of directories you should ignore by default", "engines": {"node": ">=10 <11 || >=12 <13 || >=14"}, "main": "index.js", "scripts": {"test": "standard && node test.js"}, "repository": {"type": "git", "url": "git+https://github.com/novemberborn/ignore-by-default.git"}, "keywords": ["ignore", "chokidar", "watcher", "exclude", "glob", "pattern"], "author": {"name": "<PERSON>", "url": "https://novemberborn.net/"}, "license": "ISC", "bugs": {"url": "https://github.com/novemberborn/ignore-by-default/issues"}, "homepage": "https://github.com/novemberborn/ignore-by-default#readme", "devDependencies": {"figures": "^3.2.0", "standard": "^14.3.4"}, "gitHead": "c2fefc478188edca24a2e3fb2a7bda9f558fbe2f", "_id": "ignore-by-default@2.1.0", "_nodeVersion": "16.14.2", "_npmVersion": "8.5.1", "dist": {"integrity": "sha512-yiWd4GVmJp0Q6ghmM2B/V3oZGRmjrKLXvHR3TE1nfoXsmoggllfZUQe74EN0fJdPFZu2NIvNdrMMLm3OsV7Ohw==", "shasum": "c0e0de1a99b6065bdc93315a6f728867981464db", "tarball": "https://registry.npmjs.org/ignore-by-default/-/ignore-by-default-2.1.0.tgz", "fileCount": 4, "unpackedSize": 3078, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCtF3SlmyEWuF+H7Xb5m9yHz4r4amFVpDLRXrUaNwN0fgIhAMoUuRVGlLO9CHvL5lr3Gv5Df8akbUEr2KHDTe5SNu6w"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJib6m7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpi2g/9GgXJN0aZsAL2txVPFnqF97eZVoj6UvJtoRnyfbqIwV8gMfDn\r\n2PpQYGHYtMm9q1YBEKB4iB4XG4lT4oBBz8yaT/SYv8JChP2Iq2SSWv+DL1P8\r\nMj2pUPGBzOLA03AkatXDzM7f7+l8aHvNnj3/5Olc5E7SCjmIRdTbumz/Lc6i\r\nevXb/vn4kTUFfJJalcWnsgCY3Ppw23iOnKZa1477E1HjEp2h0k3tO1+Ytj/9\r\noMzYLO9k9PYVbEBrCws1WeSUlBUUbWfN1nukQAzka0kfa6XqFNuCVjZZd2uV\r\nvLHnGUtHwWFQPnvEQH5p5Y92QHsWpzdLK8RHP4BoMeLNt3fZyCrcjXOaMhKu\r\nIHFlqTdHxTSEwiuDiZUEq3xp/MMxhnrN47qu7Y4IXcnyEPgO1Egj2sDVtCkl\r\na+PC7JMxhJwKaT0dMypamEFUN4+vIq11GoMWS9KLLcApVmxMKy3BeqQB0oA2\r\nnaHrDvm+NKLF/gf1TiV3dFKwiGbQxaT8ojJHQzAvrvl/37RFOVlew/ttfwCt\r\nvQHR4ti/kOGIygWhwFl8eMNGUZPWXxb54dU3IxQca515PeANFRFvN4BM+/jn\r\ndSW7n9rxJ6P15h12zx3c0iL94kcT7o5xIe3saLa+pYVIu3SUgnT84FHWlb2M\r\ner4rFiFzAv/qgKYs+/qAR1lzwhBlvLaebhU=\r\n=Ao41\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "novemberborn", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "novemberborn", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ignore-by-default_2.1.0_1651485115213_0.634800554694466"}, "_hasShrinkwrap": false}}, "readme": "# ignore-by-default\n\nThis is a package aimed at Node.js development tools. It provides a list of\ndirectories that should probably be ignored by such tools, e.g. when watching\nfor file changes.\n\nIt's used by [AVA](https://www.npmjs.com/package/ava) and\n[nodemon](https://www.npmjs.com/package/nodemon).\n\n[Please contribute!](./CONTRIBUTING.md)\n\n## Installation\n\n```\nnpm install ignore-by-default\n```\n\n## Usage\n\nThe `ignore-by-default` module exports a `directories()` function, which will\nreturn an array of directory names. These are the ones you should ignore.\n\n```js\n// ['.git', '.sass_cache', …]\nconst ignoredDirectories = require('ignore-by-default').directories()\n```\n", "maintainers": [{"name": "novemberborn", "email": "<EMAIL>"}], "time": {"modified": "2022-06-19T00:22:07.514Z", "created": "2016-02-05T18:06:20.388Z", "1.0.0": "2016-02-05T18:06:20.388Z", "1.0.1": "2016-02-26T14:48:47.510Z", "2.0.0": "2020-06-13T14:08:42.313Z", "2.1.0": "2022-05-02T09:51:55.369Z"}, "homepage": "https://github.com/novemberborn/ignore-by-default#readme", "keywords": ["ignore", "chokidar", "watcher", "exclude", "glob", "pattern"], "repository": {"type": "git", "url": "git+https://github.com/novemberborn/ignore-by-default.git"}, "author": {"name": "<PERSON>", "url": "https://novemberborn.net/"}, "bugs": {"url": "https://github.com/novemberborn/ignore-by-default/issues"}, "license": "ISC", "readmeFilename": "README.md", "users": {"novemberborn": true}}