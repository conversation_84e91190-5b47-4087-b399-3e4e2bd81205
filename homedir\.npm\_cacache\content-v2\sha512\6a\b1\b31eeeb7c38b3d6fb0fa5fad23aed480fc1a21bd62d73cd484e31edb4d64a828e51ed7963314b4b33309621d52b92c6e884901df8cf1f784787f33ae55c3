{"_id": "jws", "_rev": "68-f1457c7f960018e3446f0551365c1375", "name": "jws", "dist-tags": {"latest": "4.0.0"}, "versions": {"0.0.1": {"name": "jws", "version": "0.0.1", "keywords": ["jws", "json", "web", "signatures"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "jws@0.0.1", "maintainers": [{"name": "brianloveswords", "email": "<EMAIL>"}], "dist": {"shasum": "76a4b3afd3e03844ff01c4b3fabc22e0d43d6787", "tarball": "https://registry.npmjs.org/jws/-/jws-0.0.1.tgz", "integrity": "sha512-Z+RLy0vvN1zYkW97tJIOTsYIPhN0l5eXEQ0yPRTm3X8qDbUgAGhuDsA3OZ5MzFR0woG1/kP9F2+w2vXnrNpqvA==", "signatures": [{"sig": "MEQCIBi4CjCoK7wiheZcAgSSnVN8um4dr3Aq0u6AQwgo3AoJAiBKmB5Q+TXPR60VGZnS023yg8ziRfPOyijnVF9Y6cRyCA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "gitHead": "c0f6b27bcea5a2ad2e304d91c2e842e4076a6b03", "scripts": {"test": "make test"}, "_npmUser": {"name": "brianloveswords", "email": "<EMAIL>"}, "deprecated": "Security update: Versions below 3.0.0 are deprecated.", "repository": {"url": "git://github.com/brianloveswords/node-jws.git", "type": "git"}, "_npmVersion": "1.2.0", "description": "Implementation of JSON Web Signatures", "directories": {"test": "test"}, "dependencies": {"tap": "~0.3.3", "base64url": "0.0.3"}}, "0.0.2": {"name": "jws", "version": "0.0.2", "keywords": ["jws", "json", "web", "signatures"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "jws@0.0.2", "maintainers": [{"name": "brianloveswords", "email": "<EMAIL>"}], "dist": {"shasum": "8c6916977183cce3361da48c8c2e0c606e7a95c6", "tarball": "https://registry.npmjs.org/jws/-/jws-0.0.2.tgz", "integrity": "sha512-o7PCeWei86NxZFQmeyvpdwDY7QGSr4D5QkbFBau6cDNwFyG4hi6IGS75Z1EPQ+V7erMCBMKJkQYlPiphMNOWEg==", "signatures": [{"sig": "MEUCIQDoIa3hPyKLHxj5yhA0G1TmUnaTLtbRMbsJgDK/+tP6xQIgaLdGwEAFTai+EVdd+E5PUxVtwLzI29E3X1etCORscL0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "gitHead": "c0f6b27bcea5a2ad2e304d91c2e842e4076a6b03", "scripts": {"test": "make test"}, "_npmUser": {"name": "brianloveswords", "email": "<EMAIL>"}, "deprecated": "Security update: Versions below 3.0.0 are deprecated.", "repository": {"url": "git://github.com/brianloveswords/node-jws.git", "type": "git"}, "_npmVersion": "1.2.0", "description": "Implementation of JSON Web Signatures", "directories": {"test": "test"}, "dependencies": {"tap": "~0.3.3", "base64url": "0.0.3"}}, "0.2.0": {"name": "jws", "version": "0.2.0", "keywords": ["jws", "json", "web", "signatures"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "jws@0.2.0", "maintainers": [{"name": "brianloveswords", "email": "<EMAIL>"}], "dist": {"shasum": "b7db28748768b182b42e5c7ac223902c1a306c06", "tarball": "https://registry.npmjs.org/jws/-/jws-0.2.0.tgz", "integrity": "sha512-kRceOYXq5Kpx5knUU8N1RqgrkuMA+G6503xhApe5onET98G0Pau2+G327bGvnaZrDfiI62D3cNNUCENWEYncDg==", "signatures": [{"sig": "MEQCIGBrVHQRT/v5r+ROZE8Vc/YfXXiN/5hQAf076eXLHdgjAiAyQ+NahqeqsaktQJZ9GYIwd2mBqcacrJJTckBQneW0rQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "gitHead": "c0f6b27bcea5a2ad2e304d91c2e842e4076a6b03", "scripts": {"test": "make test"}, "_npmUser": {"name": "brianloveswords", "email": "<EMAIL>"}, "deprecated": "Security update: Versions below 3.0.0 are deprecated.", "repository": {"url": "git://github.com/brianloveswords/node-jws.git", "type": "git"}, "_npmVersion": "1.2.2", "description": "Implementation of JSON Web Signatures", "directories": {"test": "test"}, "dependencies": {"jwa": "0.0.1", "tap": "~0.3.3", "base64url": "0.0.3"}}, "0.2.1": {"name": "jws", "version": "0.2.1", "keywords": ["jws", "json", "web", "signatures"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "jws@0.2.1", "maintainers": [{"name": "brianloveswords", "email": "<EMAIL>"}], "dist": {"shasum": "361e7750aa40f64913dba402d6f183df64381e2d", "tarball": "https://registry.npmjs.org/jws/-/jws-0.2.1.tgz", "integrity": "sha512-VqkRkYpbeywE4o4qsxlntiBMeJXdWO4tXqm9X2koueLTGJ8EaQ0TCW5PNSv+tw1R37R3cIAiZTBhLRWbgptohQ==", "signatures": [{"sig": "MEQCIEupYMzGNMewvuNBLZ4fW71v2Axs9Lz/8JCFlrUTTy4GAiBKx7hnldEz/NS22zyJbkqxcHfzr+08rEg62xdw+YeBdA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "gitHead": "c0f6b27bcea5a2ad2e304d91c2e842e4076a6b03", "scripts": {"test": "make test"}, "_npmUser": {"name": "brianloveswords", "email": "<EMAIL>"}, "deprecated": "Security update: Versions below 3.0.0 are deprecated.", "repository": {"url": "git://github.com/brianloveswords/node-jws.git", "type": "git"}, "_npmVersion": "1.2.2", "description": "Implementation of JSON Web Signatures", "directories": {"test": "test"}, "dependencies": {"jwa": "0.0.1", "tap": "~0.3.3", "base64url": "0.0.3"}}, "0.2.2": {"name": "jws", "version": "0.2.2", "keywords": ["jws", "json", "web", "signatures"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "jws@0.2.2", "maintainers": [{"name": "brianloveswords", "email": "<EMAIL>"}], "dist": {"shasum": "0889baae5ea0b07f9fc022cbd977debf154ba20b", "tarball": "https://registry.npmjs.org/jws/-/jws-0.2.2.tgz", "integrity": "sha512-mUOiYVD51NCJze4Qdj+19EKxT2+shnNXVUQGlXCEmlukiO1LNU9mZKEX6G9OFF9YhT68R0fwbLMd/uIgDYMfsw==", "signatures": [{"sig": "MEUCIFI3oYvPxDI9dMQUc094l49/yp/RvUmlxE+5GS3Ox4bSAiEAhYZ0h6BiburbJgwM6QS9wbHZdfpDGVuV3wldVn7g10o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "gitHead": "c0f6b27bcea5a2ad2e304d91c2e842e4076a6b03", "scripts": {"test": "make test"}, "_npmUser": {"name": "brianloveswords", "email": "<EMAIL>"}, "deprecated": "Security update: Versions below 3.0.0 are deprecated.", "repository": {"url": "git://github.com/brianloveswords/node-jws.git", "type": "git"}, "_npmVersion": "1.2.2", "description": "Implementation of JSON Web Signatures", "directories": {"test": "test"}, "dependencies": {"jwa": "0.0.1", "tap": "~0.3.3", "base64url": "0.0.3"}}, "0.2.3": {"name": "jws", "version": "0.2.3", "keywords": ["jws", "json", "web", "signatures"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "jws@0.2.3", "maintainers": [{"name": "brianloveswords", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/brianloveswords/node-jws/issues"}, "dist": {"shasum": "0aebe20a042365c4a15de24596dd0ddbe7aa422e", "tarball": "https://registry.npmjs.org/jws/-/jws-0.2.3.tgz", "integrity": "sha512-f4GoHGfA1AqYQPN5AyaCUO0EN1fY//R3MtNnJ5Ffdb5nbvx5VzwvhwXSv+Pv4RjcQpHm3mDELJTQWqnms58LgA==", "signatures": [{"sig": "MEYCIQDnC8OTbpJs40yud/OQwf6JaJQ6o2lFkd3M7/qGmlEdjgIhANZvp0kOaRDGkrY3V6/6fLqRbLttZL3cFQIt3XmQeGXH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "gitHead": "c0f6b27bcea5a2ad2e304d91c2e842e4076a6b03", "scripts": {"test": "make test"}, "_npmUser": {"name": "brianloveswords", "email": "<EMAIL>"}, "deprecated": "Security update: Versions below 3.0.0 are deprecated.", "repository": {"url": "git://github.com/brianloveswords/node-jws.git", "type": "git"}, "_npmVersion": "1.3.11", "description": "Implementation of JSON Web Signatures", "directories": {"test": "test"}, "dependencies": {"jwa": "0.0.1", "base64url": "0.0.3"}, "devDependencies": {"tap": "~0.3.3"}}, "0.2.4": {"name": "jws", "version": "0.2.4", "keywords": ["jws", "json", "web", "signatures"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "jws@0.2.4", "maintainers": [{"name": "brianloveswords", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/brianloveswords/node-jws/issues"}, "dist": {"shasum": "3d37329052f111837867d363c040fb7717c6612b", "tarball": "https://registry.npmjs.org/jws/-/jws-0.2.4.tgz", "integrity": "sha512-qgMnvXv4l43dhF0hH69UKePq4+MArg2zOzq8p8NoCqBHecIVsqGXHCiWf1N8sJ/XtCrOWRj/iHP3wGSLKxuGcQ==", "signatures": [{"sig": "MEQCIBBKHd4fqVOWO5OczPwMtH41XWsYjhh7nR9ckaiMe34QAiBExZoLyteIp9PjUcjeGQDtAt7jzLAudv7ft2NKRUnpNQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "gitHead": "c0f6b27bcea5a2ad2e304d91c2e842e4076a6b03", "scripts": {"test": "make test"}, "_npmUser": {"name": "brianloveswords", "email": "<EMAIL>"}, "deprecated": "Security update: Versions below 3.0.0 are deprecated.", "repository": {"url": "git://github.com/brianloveswords/node-jws.git", "type": "git"}, "_npmVersion": "1.3.11", "description": "Implementation of JSON Web Signatures", "directories": {"test": "test"}, "dependencies": {"jwa": "0.0.1", "base64url": "0.0.6"}, "devDependencies": {"tap": "~0.3.3"}}, "0.2.5": {"name": "jws", "version": "0.2.5", "keywords": ["jws", "json", "web", "signatures"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "jws@0.2.5", "maintainers": [{"name": "brianloveswords", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/brianloveswords/node-jws/issues"}, "dist": {"shasum": "f738d45b38e5d9f6b7171aadd0ed5ac9f5a62555", "tarball": "https://registry.npmjs.org/jws/-/jws-0.2.5.tgz", "integrity": "sha512-ZCeoWn9ZAj5jl3ZID7AuIIB9QTPF/ooxhUR42SkSuRWjqaZTHwuxsJFfPv9F4ICCk/bxOrE8i49LusxrbAGdSQ==", "signatures": [{"sig": "MEUCICJjBm4arhp+dA4mdeDj83ap2FUaXkrmrwXXViLvPO3qAiEA4BRu3I55A5+H7v8HbmIAOlfTBC/ejyEgeU12y+VkOiw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "gitHead": "c0f6b27bcea5a2ad2e304d91c2e842e4076a6b03", "scripts": {"test": "make test"}, "_npmUser": {"name": "brianloveswords", "email": "<EMAIL>"}, "deprecated": "Security update: Versions below 3.0.0 are deprecated.", "repository": {"url": "git://github.com/brianloveswords/node-jws.git", "type": "git"}, "_npmVersion": "1.3.11", "description": "Implementation of JSON Web Signatures", "directories": {"test": "test"}, "dependencies": {"jwa": "0.0.1", "base64url": "0.0.6"}, "devDependencies": {"tap": "~0.3.3"}}, "0.2.6": {"name": "jws", "version": "0.2.6", "keywords": ["jws", "json", "web", "signatures"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "jws@0.2.6", "maintainers": [{"name": "brianloveswords", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}], "homepage": "https://github.com/brianloveswords/node-jws", "bugs": {"url": "https://github.com/brianloveswords/node-jws/issues"}, "dist": {"shasum": "e9b7e9ac8d2ac1067413233bc6c20fbd8868e9ba", "tarball": "https://registry.npmjs.org/jws/-/jws-0.2.6.tgz", "integrity": "sha512-ZjMgMG4UIf1KKjjYY4fKdB6X9W5fyrkFCTC35XgsrIvQGk088xiwGSsGI88e9oadSp+yZ/aRVQ7Cy1CS4SC/hA==", "signatures": [{"sig": "MEUCIQCy5ZEcCqXafJVvrQCVW1I6dbBjaQ1rlWRThuJsijiVwwIgEDAFOy4TmnR+KlDbxxsAFNnaO2zFyHjG49CuXeGH3Pg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "e9b7e9ac8d2ac1067413233bc6c20fbd8868e9ba", "gitHead": "c0f6b27bcea5a2ad2e304d91c2e842e4076a6b03", "scripts": {"test": "make test"}, "_npmUser": {"name": "brianloveswords", "email": "<EMAIL>"}, "deprecated": "Security update: Versions below 3.0.0 are deprecated.", "repository": {"url": "git://github.com/brianloveswords/node-jws.git", "type": "git"}, "_npmVersion": "1.4.14", "description": "Implementation of JSON Web Signatures", "directories": {"test": "test"}, "dependencies": {"jwa": "0.0.1", "base64url": "0.0.6"}, "devDependencies": {"tape": "~2.14.0"}}, "1.0.0": {"name": "jws", "version": "1.0.0", "keywords": ["jws", "json", "web", "signatures"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "jws@1.0.0", "maintainers": [{"name": "brianloveswords", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}], "homepage": "https://github.com/brianloveswords/node-jws", "bugs": {"url": "https://github.com/brianloveswords/node-jws/issues"}, "dist": {"shasum": "327c7690d53a4866624333f041e6156653049a2e", "tarball": "https://registry.npmjs.org/jws/-/jws-1.0.0.tgz", "integrity": "sha512-A1ZPjz9qhgwkA8P/nHYMszNzdHWJce2Y+YAOkJseuNqWw3f7che7+jHLxDM8OwyLRDafglpy9VTKT5hZ8WBXJg==", "signatures": [{"sig": "MEUCIQCOO0d0bbEf0oaytCp2V39wLPIflbqSQ6hdfDUlnmMMdQIgPk/0DYDqkpvPxSexkF/xe+eXIctBFbPvrKwXSGW6APc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "327c7690d53a4866624333f041e6156653049a2e", "gitHead": "c0f6b27bcea5a2ad2e304d91c2e842e4076a6b03", "scripts": {"test": "make test"}, "_npmUser": {"name": "brianloveswords", "email": "<EMAIL>"}, "deprecated": "Security update: Versions below 3.0.0 are deprecated.", "repository": {"url": "git://github.com/brianloveswords/node-jws.git", "type": "git"}, "_npmVersion": "2.1.11", "description": "Implementation of JSON Web Signatures", "directories": {"test": "test"}, "_nodeVersion": "0.10.33", "dependencies": {"jwa": "~0.0.2", "base64url": "~1.0.4"}, "devDependencies": {"tape": "~2.14.0"}}, "1.0.1": {"name": "jws", "version": "1.0.1", "keywords": ["jws", "json", "web", "signatures"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "jws@1.0.1", "maintainers": [{"name": "brianloveswords", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}], "homepage": "https://github.com/brianloveswords/node-jws", "bugs": {"url": "https://github.com/brianloveswords/node-jws/issues"}, "dist": {"shasum": "4c72d091652e63404ee95864954d082aaf334950", "tarball": "https://registry.npmjs.org/jws/-/jws-1.0.1.tgz", "integrity": "sha512-etTaSZOx6ncBF/nc/FD9Df3axk6zjxDCQd4TltsYj5+az87A5unRolWjcNQykpHqaAKvTGHDA29dgBCYxkv7XA==", "signatures": [{"sig": "MEUCIQCygKCOIsmnkdtjhafc+tAvLce16jcyzcLAHUcnTaZn5gIgYrUpBzo8YITPlp2RNcWvDQgvfub/oD3QqrX866qYZrE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "4c72d091652e63404ee95864954d082aaf334950", "gitHead": "c0f6b27bcea5a2ad2e304d91c2e842e4076a6b03", "scripts": {"test": "make test"}, "_npmUser": {"name": "brianloveswords", "email": "<EMAIL>"}, "deprecated": "Security update: Versions below 3.0.0 are deprecated.", "repository": {"url": "git://github.com/brianloveswords/node-jws.git", "type": "git"}, "_npmVersion": "2.1.14", "description": "Implementation of JSON Web Signatures", "directories": {"test": "test"}, "_nodeVersion": "0.10.35", "dependencies": {"jwa": "~1.0.0", "base64url": "~1.0.4"}, "devDependencies": {"tape": "~2.14.0"}}, "2.0.0": {"name": "jws", "version": "2.0.0", "keywords": ["jws", "json", "web", "signatures"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "jws@2.0.0", "maintainers": [{"name": "brianloveswords", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}], "homepage": "https://github.com/brianloveswords/node-jws", "bugs": {"url": "https://github.com/brianloveswords/node-jws/issues"}, "dist": {"shasum": "b494b6b99268b76b21209d52a49a227d412d6bdf", "tarball": "https://registry.npmjs.org/jws/-/jws-2.0.0.tgz", "integrity": "sha512-W5b0BrKQXJ6+SaAKW08Wk5rUYU7vaOafQPR4I7zpmWUiwa9lbnfuYz1nzWsdmbxpKvQev/3S4CcHVcuYuOCPaw==", "signatures": [{"sig": "MEYCIQDB+lwz9joYNRtn8xdtdMUY/y0f2WhG7iMQHI84/3dkJwIhANrkaRxgd1vuAEIYLnYiDCF+72zXjHwGCTgXjD/48tKS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "b494b6b99268b76b21209d52a49a227d412d6bdf", "gitHead": "c0f6b27bcea5a2ad2e304d91c2e842e4076a6b03", "scripts": {"test": "make test"}, "_npmUser": {"name": "brianloveswords", "email": "<EMAIL>"}, "deprecated": "Security update: Versions below 3.0.0 are deprecated.", "repository": {"url": "git://github.com/brianloveswords/node-jws.git", "type": "git"}, "_npmVersion": "2.3.0", "description": "Implementation of JSON Web Signatures", "directories": {"test": "test"}, "_nodeVersion": "0.10.35", "dependencies": {"jwa": "~1.0.0", "base64url": "~1.0.4"}, "devDependencies": {"tape": "~2.14.0"}}, "3.0.0": {"name": "jws", "version": "3.0.0", "keywords": ["jws", "json", "web", "signatures"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "jws@3.0.0", "maintainers": [{"name": "brianloveswords", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}], "homepage": "https://github.com/brianloveswords/node-jws", "bugs": {"url": "https://github.com/brianloveswords/node-jws/issues"}, "dist": {"shasum": "da5f267897dd4e9cf8137979db33fc54a3c05418", "tarball": "https://registry.npmjs.org/jws/-/jws-3.0.0.tgz", "integrity": "sha512-Me<PERSON>ywrnys7VjF8S0ipMSOrQGe5UFeIOg6oLLey7Dqpbtl6ZBRwLeOZ5VAHFmvcxlyPFtey2XAaYKmzG6bjt7lA==", "signatures": [{"sig": "MEUCIQDGJ2TVCRmZikSnGESYYNFvBZP8jfeNHaMwek6J/lUUfgIgIPye4voYOkEE3OCw2zNpYZKn9QjT2ZpwQJSc0NqRUyQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "da5f267897dd4e9cf8137979db33fc54a3c05418", "gitHead": "c0f6b27bcea5a2ad2e304d91c2e842e4076a6b03", "scripts": {"test": "make test"}, "_npmUser": {"name": "brianloveswords", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/brianloveswords/node-jws.git", "type": "git"}, "_npmVersion": "2.7.1", "description": "Implementation of JSON Web Signatures", "directories": {"test": "test"}, "_nodeVersion": "0.12.0", "dependencies": {"jwa": "~1.0.0", "base64url": "~1.0.4"}, "devDependencies": {"tape": "~2.14.0"}}, "3.1.0": {"name": "jws", "version": "3.1.0", "keywords": ["jws", "json", "web", "signatures"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "jws@3.1.0", "maintainers": [{"name": "brianloveswords", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}, {"name": "omsmith", "email": "<EMAIL>"}], "homepage": "https://github.com/brianloveswords/node-jws#readme", "bugs": {"url": "https://github.com/brianloveswords/node-jws/issues"}, "dist": {"shasum": "885a89127d24119a2a93f234ddd492337a7c85a0", "tarball": "https://registry.npmjs.org/jws/-/jws-3.1.0.tgz", "integrity": "sha512-dIhjVxxfhs93IKornyqxfkx/H/fupqwrUzXAXu/zMkgnPyGH0qXKVtet0Fu7I7o0BlV3SDUkAKOCHpzPItPOoQ==", "signatures": [{"sig": "MEUCIFfXzdRty7dOXqJfHGqSZarUPg2EjUZqK5O4W6SIPn6kAiEA+4SRxygM4QFlz9EmQyZ7MQGZ0njlSZrbb7R6yXgb6Pw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "885a89127d24119a2a93f234ddd492337a7c85a0", "gitHead": "c0f6b27bcea5a2ad2e304d91c2e842e4076a6b03", "scripts": {"test": "make test"}, "_npmUser": {"name": "omsmith", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/brianloveswords/node-jws.git", "type": "git"}, "_npmVersion": "2.11.3", "description": "Implementation of JSON Web Signatures", "directories": {"test": "test"}, "_nodeVersion": "2.3.3", "dependencies": {"jwa": "^1.1.0", "base64url": "~1.0.4"}, "devDependencies": {"tape": "~2.14.0"}}, "3.1.1": {"name": "jws", "version": "3.1.1", "keywords": ["jws", "json", "web", "signatures"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "jws@3.1.1", "maintainers": [{"name": "brianloveswords", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}, {"name": "omsmith", "email": "<EMAIL>"}], "homepage": "https://github.com/brianloveswords/node-jws#readme", "bugs": {"url": "https://github.com/brianloveswords/node-jws/issues"}, "dist": {"shasum": "34f5a424e628af4551121e860ba279f55cfa6629", "tarball": "https://registry.npmjs.org/jws/-/jws-3.1.1.tgz", "integrity": "sha512-xWmdc62OIKSOTwuXOD4pffjXQ0J3J7nYS1o2g9/nVrFa6SUcQzveqfnl/hFkfnf2njM1vMABWc7OZkDw0xQ0aw==", "signatures": [{"sig": "MEQCIDrHcXPfYE26LZtt6TgBy6ggSnPO4c3Bj9LFYYClHAl+AiAD7h9IJN34l1nzF5kZ6C3OlEBCD6pnIrcWXEbqTwcQbg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "34f5a424e628af4551121e860ba279f55cfa6629", "gitHead": "c0f6b27bcea5a2ad2e304d91c2e842e4076a6b03", "scripts": {"test": "make test"}, "_npmUser": {"name": "omsmith", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/brianloveswords/node-jws.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "Implementation of JSON Web Signatures", "directories": {"test": "test"}, "_nodeVersion": "5.4.1", "dependencies": {"jwa": "^1.1.0", "base64url": "~1.0.4"}, "devDependencies": {"tape": "~2.14.0", "semver": "^5.1.0"}}, "3.1.2": {"name": "jws", "version": "3.1.2", "keywords": ["jws", "json", "web", "signatures"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "jws@3.1.2", "maintainers": [{"name": "brianloveswords", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}, {"name": "omsmith", "email": "<EMAIL>"}], "homepage": "https://github.com/brianloveswords/node-jws#readme", "bugs": {"url": "https://github.com/brianloveswords/node-jws/issues"}, "dist": {"shasum": "0d675a4612035fb318118e1b2b9de3f8ecaa1ae2", "tarball": "https://registry.npmjs.org/jws/-/jws-3.1.2.tgz", "integrity": "sha512-xWhGGcSJZDVCO/HGu5uMgODlqt91VX953MMAgGsrYKirLdXGXUzw38XaglbhL3JjY+xADJ7WunEgLllDYZXPDg==", "signatures": [{"sig": "MEYCIQDbkCnTVwFYnr9GAYsGsrNvtXwnqEnRpBDRyDmHwsnz+gIhAJBmS+BBCSpoIXC+YD6ApD3VlIAuaKXLpc8Eomk+kqPD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "0d675a4612035fb318118e1b2b9de3f8ecaa1ae2", "gitHead": "c0f6b27bcea5a2ad2e304d91c2e842e4076a6b03", "scripts": {"test": "make test"}, "_npmUser": {"name": "brianloveswords", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/brianloveswords/node-jws.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "Implementation of JSON Web Signatures", "directories": {"test": "test"}, "_nodeVersion": "5.2.0", "dependencies": {"jwa": "^1.1.2", "base64url": "~1.0.4"}, "devDependencies": {"tape": "~2.14.0", "semver": "^5.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jws-3.1.2.tgz_1455805587314_0.43562568444758654", "host": "packages-5-east.internal.npmjs.com"}}, "3.1.3": {"name": "jws", "version": "3.1.3", "keywords": ["jws", "json", "web", "signatures"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "jws@3.1.3", "maintainers": [{"name": "brianloveswords", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}, {"name": "omsmith", "email": "<EMAIL>"}], "homepage": "https://github.com/brianloveswords/node-jws#readme", "bugs": {"url": "https://github.com/brianloveswords/node-jws/issues"}, "dist": {"shasum": "b88f1b4581a2c5ee8813c06b3fdf90ea9b5c7e6c", "tarball": "https://registry.npmjs.org/jws/-/jws-3.1.3.tgz", "integrity": "sha512-KiwFJo7lfoVFV6AbEjZLvfY6dUnFVHliKdoEKtd0P+AN5wBGv8a25qYf0qgXrTzB6vArpnXi67reRpXo2EBUKw==", "signatures": [{"sig": "MEUCIBLdWThaUbeTRrg7buxIcGgm+ZdeC4BlrXHnkGyPp/toAiEA36lcVxROlUXs0FccGK9F5HuLUEZlN+pIW5FH/S30UP8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "b88f1b4581a2c5ee8813c06b3fdf90ea9b5c7e6c", "gitHead": "c0f6b27bcea5a2ad2e304d91c2e842e4076a6b03", "scripts": {"test": "make test"}, "_npmUser": {"name": "brianloveswords", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/brianloveswords/node-jws.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "Implementation of JSON Web Signatures", "directories": {"test": "test"}, "_nodeVersion": "5.2.0", "dependencies": {"jwa": "^1.1.2", "base64url": "~1.0.4"}, "devDependencies": {"tape": "~2.14.0", "semver": "^5.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jws-3.1.3.tgz_1455809983684_0.8235816163942218", "host": "packages-6-west.internal.npmjs.com"}}, "3.1.4": {"name": "jws", "version": "3.1.4", "keywords": ["jws", "json", "web", "signatures"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "jws@3.1.4", "maintainers": [{"name": "brianloveswords", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}, {"name": "omsmith", "email": "<EMAIL>"}], "homepage": "https://github.com/brianloveswords/node-jws#readme", "bugs": {"url": "https://github.com/brianloveswords/node-jws/issues"}, "dist": {"shasum": "f9e8b9338e8a847277d6444b1464f61880e050a2", "tarball": "https://registry.npmjs.org/jws/-/jws-3.1.4.tgz", "integrity": "sha512-9b5xCA+0jei+IJP6rs33ecRBoYIh4vCn8M5wmmlzDIrh755b3UCqUlu9JwWLS+z4ykpI6zZqflAHy92b4HGNZw==", "signatures": [{"sig": "MEUCIDXKU6qLqVJsW5Oq54TVW5JxBo1CCwXe/e+Ub1aTLmWEAiEAo9sVMeM26RAINWRkbd9jliCoKmT/x7bloMRVJ8T4dgI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "f9e8b9338e8a847277d6444b1464f61880e050a2", "gitHead": "c0f6b27bcea5a2ad2e304d91c2e842e4076a6b03", "scripts": {"test": "make test"}, "_npmUser": {"name": "omsmith", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/brianloveswords/node-jws.git", "type": "git"}, "_npmVersion": "2.15.8", "description": "Implementation of JSON Web Signatures", "directories": {"test": "test"}, "_nodeVersion": "4.4.7", "dependencies": {"jwa": "^1.1.4", "base64url": "^2.0.0", "safe-buffer": "^5.0.1"}, "devDependencies": {"tape": "~2.14.0", "semver": "^5.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jws-3.1.4.tgz_1478202748192_0.3927526210900396", "host": "packages-12-west.internal.npmjs.com"}}, "3.1.5": {"name": "jws", "version": "3.1.5", "keywords": ["jws", "json", "web", "signatures"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "jws@3.1.5", "maintainers": [{"name": "brianloveswords", "email": "<EMAIL>"}, {"name": "omsmith", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}], "homepage": "https://github.com/brianloveswords/node-jws#readme", "bugs": {"url": "https://github.com/brianloveswords/node-jws/issues"}, "dist": {"shasum": "80d12d05b293d1e841e7cb8b4e69e561adcf834f", "tarball": "https://registry.npmjs.org/jws/-/jws-3.1.5.tgz", "fileCount": 9, "integrity": "sha512-GsCSexFADNQUr8T5HPJvayTjvPIfoyJPtLQBwn5a4WZQchcrPMPMAWcC1AzJVRDKyD6ZPROPAxgv6rfHViO4uQ==", "signatures": [{"sig": "MEYCIQCxf/702AzBjI3DL97c1FXuB6Reh8WrW3Spv1palMTuHAIhAPJzmMEasoNpLG0rHEMet54BeFiJ1xm2wOvm/nhRBBS2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17500, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+aKxCRA9TVsSAnZWagAAwtYQAKMSOzoyK8RmspA/QiHX\nO66fS11Qddx18S0aHNYIiShPgh2NwForAjdz//BFh2J6NuMu1shJd5lNZfIH\na6uFrlZe+FxKBh2EPpR1EFR6Xf05PsIv+Ap/g3EFRbraD8T3B4OLJLrEOMJX\nFv49UKyZHLIuhjyPmQUmLB9cHXxKQuh9HhLrsqylNKlqPMZhFbMnVXdUguHH\nDK9jyamF7SVVMwtL/Bk+/h3qCjI1TCDDYXmOLptE8DH0SaAm6HBcoiSqHJub\nKI2CK1LKV91YtqfadksFZ6sLTC1CAwOxmvCQbGlMtGzKUWqUWquLy9ZLK+Yy\nMr/0TC8zdfZ7PnfzzUZwBQJJJyEwoMyXcQo5/B5YylAPylaESrZnlCT/s1K6\n//btzibfj9L8lM5NKF9OGr5hBNFlniARZFRa1TgSCcKsUL5VV+l332biUY+A\nF3BUGm5l5puhpQAuyXDBdRLl9OUGg51sfJUhG85uTXVvBDWgYe3Sph6teuYp\n0wmHNc0bz2FLmCYFKnevaYQZ3jlKPs1AGkiJwZSeRBduB4i4/q3p2Eto3O7Y\neqR80QoJijMdl4iTbWkzlss7kz1P6TUZiZvWQ8g1sCHUuEcROgRgZsiACLbB\n19I+VhcLD3EWVP0HJxIgH4S92KnSD+Wl7u906PuFnlpjbuTfzZug3axBBZIQ\nu8bS\r\n=MMRK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "c0f6b27bcea5a2ad2e304d91c2e842e4076a6b03", "scripts": {"test": "make test"}, "_npmUser": {"name": "omsmith", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/brianloveswords/node-jws.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Implementation of JSON Web Signatures", "directories": {"test": "test"}, "_nodeVersion": "8.10.0", "dependencies": {"jwa": "^1.1.5", "safe-buffer": "^5.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "~2.14.0", "semver": "^5.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jws_3.1.5_1526309551808_0.23434356668208833", "host": "s3://npm-registry-packages"}}, "3.2.0": {"name": "jws", "version": "3.2.0", "keywords": ["jws", "json", "web", "signatures"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "jws@3.2.0", "maintainers": [{"name": "brianloveswords", "email": "<EMAIL>"}, {"name": "omsmith", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}], "homepage": "https://github.com/brianloveswords/node-jws#readme", "bugs": {"url": "https://github.com/brianloveswords/node-jws/issues"}, "dist": {"shasum": "b89dcfc20846504506f4dcf69ad5395f8a34680b", "tarball": "https://registry.npmjs.org/jws/-/jws-3.2.0.tgz", "fileCount": 9, "integrity": "sha512-OmaE/N+SlUTMyja5XeM7aaGP3nNCUlypCCEtlDXf3887yq3tm1zk5O7l/dSz7TQ5p+imRxERZ8Fmk4Z6xcWIIw==", "signatures": [{"sig": "MEUCIGs/PQlumVn5maXiBPNcNAaXT+nS7MdWHWMoYVtjzo8qAiEA6H4ZUxK6j8+FgHI4FEQMSDq427CzmjzXMCQ+8hfmK3s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17653, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcS3r0CRA9TVsSAnZWagAAmiYQAIJRG9W8bw9JtKuOGZEO\ne+xyVPRYxSS+bgKrg9cQhv1MZs5RmpIPgcIz+sixakr3pqpLyw9ECKpZKmOg\nvFDuIv8mCWAQye782LNRNltoQ5lbwm1X8KVKv36jw1jb/RLLkqmAzUXKF4lp\n+I4L2d5Hn7kHCYnJDZsMQ53rRUeBiAAh7O7P9/K3fgtSvvEM1b/qrh5ChV0A\nZv70Q8SNsqmUchQivoF+X+wWnRqyLYwh+tGd/8AqfEB4WnSrHvr4hljDm4YA\nk/hi0OmxyvljWgOShHszwTWz1jq1ZP14hHjqJH7OJVfJ6sFTnKi96rdPuiWc\n27HsosJ8b2Wr92WAatkPShFyjYUftw/NYL0nsq4hjHbnzbYmnCKe/Z9wmSWS\nfXV2M9/CJRv+mPj+BZjYQc9rvr6ckfjX/zwdp3mZBBW/FKcpIpKMZa7Larlo\nDkgEubFKAj5Nlvrlh2DBQCSIn6aAVLKKxIntxxCUSbSXOtKZZsRoA1G0N1F7\n1VBOCzHS/oTALO3SaL/0QEESsXMeZaYM0/g/ctSWTT0W6mO2fzXCN8dAD+ji\nhtHEscJK9hpMHULnRKUEPUbBcwc42GB1ryqGWIgvNS6/kxZ8bq/+Xu9jLzq7\nBL9B+w64duJcpfzlKA23AggMtjrW4/XE5bFqYGeru2ZfUcicZ8I3x22u+ikI\njvoF\r\n=o6JA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "c0f6b27bcea5a2ad2e304d91c2e842e4076a6b03", "scripts": {"test": "make test"}, "_npmUser": {"name": "omsmith", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/brianloveswords/node-jws.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Implementation of JSON Web Signatures", "directories": {"test": "test"}, "_nodeVersion": "11.4.0", "dependencies": {"jwa": "^1.2.0", "safe-buffer": "^5.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "~2.14.0", "semver": "^5.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jws_3.2.0_1548450548143_0.774259911509247", "host": "s3://npm-registry-packages"}}, "3.2.1": {"name": "jws", "version": "3.2.1", "keywords": ["jws", "json", "web", "signatures"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "jws@3.2.1", "maintainers": [{"name": "brianloveswords", "email": "<EMAIL>"}, {"name": "omsmith", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}], "homepage": "https://github.com/brianloveswords/node-jws#readme", "bugs": {"url": "https://github.com/brianloveswords/node-jws/issues"}, "dist": {"shasum": "d79d4216a62c9afa0a3d5e8b5356d75abdeb2be5", "tarball": "https://registry.npmjs.org/jws/-/jws-3.2.1.tgz", "fileCount": 9, "integrity": "sha512-bGA2omSrFUkd72dhh05bIAN832znP4wOU3lfuXtRBuGTbsmNmDXMQg28f0Vsxaxgk4myF5YkKQpz6qeRpMgX9g==", "signatures": [{"sig": "MEQCIC3AnBvxONvAouuASyPWY9I6iWU6BPawp/G4+0jN0+3WAiBFXYLMWip/kXI1AnC2LdT6uiGOPpq3eXppGbMBd2Qa/g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17682, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcTbemCRA9TVsSAnZWagAAwgMP/2BojRvzLmvfP74ZfH/U\nAUUG5kll1OaAQ8JskDFuPtjTf35Yn1HRp5mkUJdWPbc1w/9BKCFAdTID4UkT\nhOI1NdFQ5yeXEEG/rTcDsnpbaIzFK+jRQM3t2A5kHDkpsjAbgsRNVhQW3695\nDIh9na7AKoI6WInxl+FZ4tgEcbGVJJGpkGYBZcEpcjiHDMxZ44lh2Jbn0NP7\nQZ34YwBj185F43ImDFXG4kBR1gHfjVlNS88MyOOd4n4FoxNLsd8AOBE5bIN7\n51P7/6TdD+TrTft/yxAby9wK4PL8Wrb2OnNl+tF9jEtF+Ztao7jg8OS/PDdk\nbUFM9FVgSka4NUPYrgnrZRtfoOaXcFB1psjrX43zInaSv3drv6T8wCr9XBhj\nezaMGIVtj4kK9f6B8kvG/a2fqKrcG3y8u5MoQ+0k+bmB09dW3e34ca8D9JSI\nEkEGvwa4Qx7dQUxF6/lnrXL7INmLRl0Eo0pOuGthq79ss87YS4nuy3jxiepc\nBosk9imcAuolIXtt/pAfGGh2yyV7emjT2IKAAlGrEyy3nl6wT76CQd26XnMa\naiyqJ32zOX2g8+HT7X61AcCMWtMi5G9tJFa9mRvNnAVCsCMUHLtw7Hc2VfFc\nYS1U6v5ibe9/3P7wi0kTOkd5Od/AtcHTDAklF7ICoPOdREkyvOFxJgkG6kYc\nSl7H\r\n=zRiv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "c0f6b27bcea5a2ad2e304d91c2e842e4076a6b03", "scripts": {"test": "make test"}, "_npmUser": {"name": "omsmith", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/brianloveswords/node-jws.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Implementation of JSON Web Signatures", "directories": {"test": "test"}, "_nodeVersion": "8.11.1", "dependencies": {"jwa": "^1.2.0", "safe-buffer": "^5.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "~2.14.0", "semver": "^5.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jws_3.2.1_1548597157601_0.25617724445426715", "host": "s3://npm-registry-packages"}}, "3.2.2": {"name": "jws", "version": "3.2.2", "keywords": ["jws", "json", "web", "signatures"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "jws@3.2.2", "maintainers": [{"name": "brianloveswords", "email": "<EMAIL>"}, {"name": "omsmith", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}], "homepage": "https://github.com/brianloveswords/node-jws#readme", "bugs": {"url": "https://github.com/brianloveswords/node-jws/issues"}, "dist": {"shasum": "001099f3639468c9414000e99995fa52fb478304", "tarball": "https://registry.npmjs.org/jws/-/jws-3.2.2.tgz", "fileCount": 9, "integrity": "sha512-YHlZCB6lMTllWDtSPHz/ZXTsi8S00usEV6v1tjq8tOUZzw7DpSDWVXjXDre6ed1w/pd495ODpHZYSdkRTsa0HA==", "signatures": [{"sig": "MEUCICCZrZ6dQdQcO2BAk9GOMoeC9NwCNWgBuLTgN7aNpP/qAiEApVqrLkJjV9glxUXNnyT51jn3oAY+l2i5bE96Q25xDQU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17682, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcjRWaCRA9TVsSAnZWagAAONYP/A00P2s7Yl9ntl1lGwIZ\nSRKS6TG/AA5evv6lmG+/dbExxC2obgDkcxijujfozYhd6j3EvxcBiIN8Clce\nnMdAz42W0p2sdcPL7DPdan1x5mG0Ytic+Hz8AdA/RJn5swV8OfYzvZpgQpll\nfvhRddjNP0c40WF07KehEoLb5fRg34vGxMJFQJNtnOa6KhE6kprhKgs2yvcv\nipf5RmvrsqSYWv3ZAa3iN+R0OVzvoaeFyu3dHH4v9S7eGlPMmNWgrAz5+FQN\nXF6asV2K/v2ErEaDHFc2f8reHNOIG4E61S28D10493TX+U3hsLoZiqOPG56X\niYn2qg2w22PxMrnrGMCOnny8peXgt33sJ8Q48QY4PcdecxWFzmTs5zk6c1Q9\n+NF77T3uWblzb3nPLYgcVoO6N2Wd4GDRD8M59JyIkLCTWVO0HQEZXS5guDia\nLEZEsVaYzRp2d+OCYD7up6eZBryqJYSW8WiSDGGFF5yEiJkE77T9uoa0+ctj\nNNMo07LrZ7RMfxgypchrn5MQLBZ47cUtdzvEr4eu8d2AMFzYuXJuF8Xo75We\nMrecTSA8AIo50CMtuAKKcIMQFNoc6h/L2hDAi6yeAoTLdAK+kbDQjmqLHoYQ\nYB3t09R+XOAN7E6yjURKtumHGoMvILX4bV4Uyc4ilt0PC64Y/aaqGjkj8Rx+\nUQ/E\r\n=i29x\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "c0f6b27bcea5a2ad2e304d91c2e842e4076a6b03", "scripts": {"test": "make test"}, "_npmUser": {"name": "omsmith", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/brianloveswords/node-jws.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "Implementation of JSON Web Signatures", "directories": {"test": "test"}, "_nodeVersion": "11.10.0", "dependencies": {"jwa": "^1.4.1", "safe-buffer": "^5.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "~2.14.0", "semver": "^5.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jws_3.2.2_1552749977567_0.16467472869709399", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "jws", "version": "4.0.0", "keywords": ["jws", "json", "web", "signatures"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "jws@4.0.0", "maintainers": [{"name": "brianloveswords", "email": "<EMAIL>"}, {"name": "omsmith", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}], "homepage": "https://github.com/brianloveswords/node-jws#readme", "bugs": {"url": "https://github.com/brianloveswords/node-jws/issues"}, "dist": {"shasum": "2d4e8cf6a318ffaa12615e9dec7e86e6c97310f4", "tarball": "https://registry.npmjs.org/jws/-/jws-4.0.0.tgz", "fileCount": 9, "integrity": "sha512-KDncfTmOZoOMTFG4mBlG0qUIOlc03fmzH+ru6RgYVZhPkyiy/92Owlt/8UEN+a4TXR1FQetfIpJE8ApdvdVxTg==", "signatures": [{"sig": "MEYCIQClWGMEcyK+Zfk57Pw8NI/EyrmYOGZiO4ZavHySWhRvzAIhAJtRPb/rdx4tvTYqmFWLGkgINc8OgzuZyT3G33JNtvbn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17682, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd971bCRA9TVsSAnZWagAAErsP/ivJGHXJChnbnAhm8xlF\n8vX+WY+3gE6jeQFVU/Au5s/J+LyLF20QrQhiZ4l2O8EHZaO1SiQFnHsz34aG\n3j+Y7mdVCW6M9cW9DTKLCmXpPEUKz7iIaMV65CGDBEmSBbz/+roLiQGV+mDw\noHMMKeSl1xurk0nOODivfknumQchDbT5Mb3DQqT44zjISQTxBuqxGPH7IKau\n/oSymxGDKs1kmzyK2e71wXeHV3hDh0SGVQDQQnXeSwo19zn/8HNOOIXfu9b8\ng5QwDHJu32iVDvuYITuVF8oRMBu5tMosgAWI0ODjRiMXb403d0SNviX992eO\nsdRwqA1ZWrWqWkoeXNH+Dj4Kw8JC5tWy0PcfmWRo9XNg0E/EvaCNZPIW9mYI\nBlgcmEHfvMrjUdELG3uIgro5Z98xh5QJCHjewVUVurBv1bb0Sd/gDeD/aNuh\nn8N7FCh3EyNO3S6YYaL875lFaHhHGGOHFcbZW9QP3nkDQWUBqV20l9UmzXDj\n2SU5szcwm3K//VBYtcHXV7dsoWHKcDhdk6n3FGX1fpZpnf0MeLMw4Fbf6j2p\nUwcg4YlQiSED+Ot9DGk2sIEXVcdpt6eQvF5wiQVcI98wiVqzlJtIrdFGJ+K1\np50ikoPTd9BKz8q/XBzzX/ljwLb2ImqjPT0+0GrFkYZjOh8NSmDYFLycLMJY\nNVm7\r\n=QsmR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "c0f6b27bcea5a2ad2e304d91c2e842e4076a6b03", "scripts": {"test": "make test"}, "_npmUser": {"name": "omsmith", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/brianloveswords/node-jws.git", "type": "git"}, "_npmVersion": "6.12.1", "description": "Implementation of JSON Web Signatures", "directories": {"test": "test"}, "_nodeVersion": "12.13.1", "dependencies": {"jwa": "^2.0.0", "safe-buffer": "^5.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "~2.14.0", "semver": "^5.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jws_4.0.0_1576516954551_0.4290706248028404", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2013-01-16T20:17:38.850Z", "modified": "2025-05-08T09:38:26.847Z", "0.0.1": "2013-01-16T20:17:39.718Z", "0.0.2": "2013-01-18T23:58:55.837Z", "0.2.0": "2013-02-10T20:03:40.963Z", "0.2.1": "2013-02-25T23:12:43.884Z", "0.2.2": "2013-03-12T22:27:43.257Z", "0.2.3": "2013-11-06T02:13:40.508Z", "0.2.4": "2014-01-28T19:40:39.504Z", "0.2.5": "2014-01-30T14:58:39.557Z", "0.2.6": "2014-08-07T17:39:19.155Z", "1.0.0": "2014-12-07T19:40:02.336Z", "1.0.1": "2015-01-11T16:20:20.131Z", "2.0.0": "2015-01-30T15:40:03.901Z", "3.0.0": "2015-04-08T15:21:54.795Z", "3.1.0": "2015-07-16T17:24:01.001Z", "3.1.1": "2016-01-17T20:48:24.996Z", "3.1.2": "2016-02-18T14:26:29.916Z", "3.1.3": "2016-02-18T15:39:48.138Z", "3.1.4": "2016-11-03T19:52:30.393Z", "3.1.5": "2018-05-14T14:52:31.898Z", "3.2.0": "2019-01-25T21:09:08.237Z", "3.2.1": "2019-01-27T13:52:37.693Z", "3.2.2": "2019-03-16T15:26:17.735Z", "4.0.0": "2019-12-16T17:22:34.743Z"}, "bugs": {"url": "https://github.com/brianloveswords/node-jws/issues"}, "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/brianloveswords/node-jws#readme", "keywords": ["jws", "json", "web", "signatures"], "repository": {"url": "git://github.com/brianloveswords/node-jws.git", "type": "git"}, "description": "Implementation of JSON Web Signatures", "maintainers": [{"email": "<EMAIL>", "name": "julien.w<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "charles<PERSON>"}, {"email": "<EMAIL>", "name": "lozano.okta"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "readme": "# node-jws [![Build Status](https://secure.travis-ci.org/brianloveswords/node-jws.svg)](http://travis-ci.org/brianloveswords/node-jws)\n\nAn implementation of [JSON Web Signatures](http://self-issued.info/docs/draft-ietf-jose-json-web-signature.html).\n\nThis was developed against `draft-ietf-jose-json-web-signature-08` and\nimplements the entire spec **except** X.509 Certificate Chain\nsigning/verifying (patches welcome).\n\nThere are both synchronous (`jws.sign`, `jws.verify`) and streaming\n(`jws.createSign`, `jws.createVerify`) APIs.\n\n# Install\n\n```bash\n$ npm install jws\n```\n\n# Usage\n\n## jws.ALGORITHMS\n\nArray of supported algorithms. The following algorithms are currently supported.\n\nalg Parameter Value | Digital Signature or MAC Algorithm\n----------------|----------------------------\nHS256 | HMAC using SHA-256 hash algorithm\nHS384 | HMAC using SHA-384 hash algorithm\nHS512 | HMAC using SHA-512 hash algorithm\nRS256 | RSASSA using SHA-256 hash algorithm\nRS384 | RSASSA using SHA-384 hash algorithm\nRS512 | RSASSA using SHA-512 hash algorithm\nPS256 | RSASSA-PSS using SHA-256 hash algorithm\nPS384 | RSASSA-PSS using SHA-384 hash algorithm\nPS512 | RSASSA-PSS using SHA-512 hash algorithm\nES256 | ECDSA using P-256 curve and SHA-256 hash algorithm\nES384 | ECDSA using P-384 curve and SHA-384 hash algorithm\nES512 | ECDSA using P-521 curve and SHA-512 hash algorithm\nnone | No digital signature or MAC value included\n\n## jws.sign(options)\n\n(Synchronous) Return a JSON Web Signature for a header and a payload.\n\nOptions:\n\n* `header`\n* `payload`\n* `secret` or `privateKey`\n* `encoding` (Optional, defaults to 'utf8')\n\n`header` must be an object with an `alg` property. `header.alg` must be\none a value found in `jws.ALGORITHMS`. See above for a table of\nsupported algorithms.\n\nIf `payload` is not a buffer or a string, it will be coerced into a string\nusing `JSON.stringify`.\n\nExample\n\n```js\nconst signature = jws.sign({\n  header: { alg: 'HS256' },\n  payload: 'h. jon benjamin',\n  secret: 'has a van',\n});\n```\n\n## jws.verify(signature, algorithm, secretOrKey)\n\n(Synchronous) Returns `true` or `false` for whether a signature matches a\nsecret or key.\n\n`signature` is a JWS Signature. `header.alg` must be a value found in `jws.ALGORITHMS`.\nSee above for a table of supported algorithms. `secretOrKey` is a string or\nbuffer containing either the secret for HMAC algorithms, or the PEM\nencoded public key for RSA and ECDSA.\n\nNote that the `\"alg\"` value from the signature header is ignored.\n\n\n## jws.decode(signature)\n\n(Synchronous) Returns the decoded header, decoded payload, and signature\nparts of the JWS Signature.\n\nReturns an object with three properties, e.g.\n```js\n{ header: { alg: 'HS256' },\n  payload: 'h. jon benjamin',\n  signature: 'YOWPewyGHKu4Y_0M_vtlEnNlqmFOclqp4Hy6hVHfFT4'\n}\n```\n\n## jws.createSign(options)\n\nReturns a new SignStream object.\n\nOptions:\n\n* `header` (required)\n* `payload`\n* `key` || `privateKey` || `secret`\n* `encoding` (Optional, defaults to 'utf8')\n\nOther than `header`, all options expect a string or a buffer when the\nvalue is known ahead of time, or a stream for convenience.\n`key`/`privateKey`/`secret` may also be an object when using an encrypted\nprivate key, see the [crypto documentation][encrypted-key-docs].\n\nExample:\n\n```js\n\n// This...\njws.createSign({\n  header: { alg: 'RS256' },\n  privateKey: privateKeyStream,\n  payload: payloadStream,\n}).on('done', function(signature) {\n  // ...\n});\n\n// is equivalent to this:\nconst signer = jws.createSign({\n  header: { alg: 'RS256' },\n});\nprivateKeyStream.pipe(signer.privateKey);\npayloadStream.pipe(signer.payload);\nsigner.on('done', function(signature) {\n  // ...\n});\n```\n\n## jws.createVerify(options)\n\nReturns a new VerifyStream object.\n\nOptions:\n\n* `signature`\n* `algorithm`\n* `key` || `publicKey` || `secret`\n* `encoding` (Optional, defaults to 'utf8')\n\nAll options expect a string or a buffer when the value is known ahead of\ntime, or a stream for convenience.\n\nExample:\n\n```js\n\n// This...\njws.createVerify({\n  publicKey: pubKeyStream,\n  signature: sigStream,\n}).on('done', function(verified, obj) {\n  // ...\n});\n\n// is equivilant to this:\nconst verifier = jws.createVerify();\npubKeyStream.pipe(verifier.publicKey);\nsigStream.pipe(verifier.signature);\nverifier.on('done', function(verified, obj) {\n  // ...\n});\n```\n\n## Class: SignStream\n\nA `Readable Stream` that emits a single data event (the calculated\nsignature) when done.\n\n### Event: 'done'\n`function (signature) { }`\n\n### signer.payload\n\nA `Writable Stream` that expects the JWS payload. Do *not* use if you\npassed a `payload` option to the constructor.\n\nExample:\n\n```js\npayloadStream.pipe(signer.payload);\n```\n\n### signer.secret<br>signer.key<br>signer.privateKey\n\nA `Writable Stream`. Expects the JWS secret for HMAC, or the privateKey\nfor ECDSA and RSA. Do *not* use if you passed a `secret` or `key` option\nto the constructor.\n\nExample:\n\n```js\nprivateKeyStream.pipe(signer.privateKey);\n```\n\n## Class: VerifyStream\n\nThis is a `Readable Stream` that emits a single data event, the result\nof whether or not that signature was valid.\n\n### Event: 'done'\n`function (valid, obj) { }`\n\n`valid` is a boolean for whether or not the signature is valid.\n\n### verifier.signature\n\nA `Writable Stream` that expects a JWS Signature. Do *not* use if you\npassed a `signature` option to the constructor.\n\n### verifier.secret<br>verifier.key<br>verifier.publicKey\n\nA `Writable Stream` that expects a public key or secret. Do *not* use if you\npassed a `key` or `secret` option to the constructor.\n\n# TODO\n\n* It feels like there should be some convenience options/APIs for\n  defining the algorithm rather than having to define a header object\n  with `{ alg: 'ES512' }` or whatever every time.\n\n* X.509 support, ugh\n\n# License\n\nMIT\n\n```\nCopyright (c) 2013-2015 Brian J. Brennan\n\nPermission is hereby granted, free of charge, to any person obtaining a\ncopy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be included\nin all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\nOR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n```\n\n[encrypted-key-docs]: https://nodejs.org/api/crypto.html#crypto_sign_sign_private_key_output_format\n", "readmeFilename": "readme.md", "users": {"ramy": true, "lgh06": true, "panlw": true, "sopov": true, "anoubis": true, "corintho": true, "dfenster": true, "djviolin": true, "josudoey": true, "sternelee": true, "benburwell": true, "nickeltobias": true, "willwolffmyren": true, "brandonpapworth": true}}