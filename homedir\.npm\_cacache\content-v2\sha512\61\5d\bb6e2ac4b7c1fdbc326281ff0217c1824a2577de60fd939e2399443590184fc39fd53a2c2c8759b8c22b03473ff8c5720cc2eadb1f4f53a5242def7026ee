{"_id": "ecdsa-sig-formatter", "_rev": "13-4dfcb52c160d5546f91c284def45d9a0", "name": "ecdsa-sig-formatter", "description": "Translate ECDSA signatures between ASN.1/DER and JOSE-style concatenation", "dist-tags": {"latest": "1.0.11"}, "versions": {"1.0.0": {"name": "ecdsa-sig-formatter", "version": "1.0.0", "description": "Translate ECDSA signatures between ASN.1/DER and JOSE-style concatenation", "main": "src/ecdsa-sig-formatter.js", "scripts": {"test": "istanbul cover --root src _mocha -- spec", "report-cov": "cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "git+ssh://**************/Brightspace/node-cdsa-sig-formatter.git"}, "keywords": ["ecdsa", "der", "asn.1", "jwt", "jwa", "jsonwebtoken", "jose"], "author": {"name": "D2L Corporation"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/Brightspace/node-ecdsa-sig-formatter/issues"}, "homepage": "https://github.com/Brightspace/node-ecdsa-sig-formatter#readme", "dependencies": {"asn1.js": "^2.0.3", "base64-url": "^1.2.1"}, "devDependencies": {"chai": "^3.0.0", "coveralls": "^2.11.2", "istanbul": "^0.3.15", "mocha": "^2.2.5"}, "gitHead": "53a33d2cfc076f24030542692c8a73019f2bbfde", "_id": "ecdsa-sig-formatter@1.0.0", "_shasum": "2925b91c568c1c13d1e55b6fb45917f753f7ca46", "_from": ".", "_npmVersion": "2.11.0", "_nodeVersion": "2.2.1", "_npmUser": {"name": "d2l-travis-deploy", "email": "<EMAIL>"}, "maintainers": [{"name": "d2l-travis-deploy", "email": "<EMAIL>"}], "dist": {"shasum": "2925b91c568c1c13d1e55b6fb45917f753f7ca46", "tarball": "https://registry.npmjs.org/ecdsa-sig-formatter/-/ecdsa-sig-formatter-1.0.0.tgz", "integrity": "sha512-8oki6U/9WCO3Axr5Zcgy+JLrSI+s4vyBahJSYF9UghcZnJqB4bIBC0/yUFRN7XuR/1rcT3YWFKjweJAH1MOAjw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDDxTM5xP8mzD6/uFzS42nwBR4NttkXcSzOE1SBUVAFcAiBL74lC8s/Qe9I1lczcIr84h/QpEzHyMDOsJi48AIbfGA=="}]}, "directories": {}}, "1.0.1": {"name": "ecdsa-sig-formatter", "version": "1.0.1", "description": "Translate ECDSA signatures between ASN.1/DER and JOSE-style concatenation", "main": "src/ecdsa-sig-formatter.js", "scripts": {"test": "istanbul cover --root src _mocha -- spec", "report-cov": "cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "git+ssh://**************/Brightspace/node-ecdsa-sig-formatter.git"}, "keywords": ["ecdsa", "der", "asn.1", "jwt", "jwa", "jsonwebtoken", "jose"], "author": {"name": "D2L Corporation"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/Brightspace/node-ecdsa-sig-formatter/issues"}, "homepage": "https://github.com/Brightspace/node-ecdsa-sig-formatter#readme", "dependencies": {"asn1.js": "^2.0.3", "base64-url": "^1.2.1"}, "devDependencies": {"chai": "^3.0.0", "coveralls": "^2.11.2", "istanbul": "^0.3.15", "mocha": "^2.2.5"}, "gitHead": "28eef3ebc4315db2a2435b427300fd4bddf9074c", "_id": "ecdsa-sig-formatter@1.0.1", "_shasum": "369b867c40080ba35921530df5a26dad3390d4f1", "_from": ".", "_npmVersion": "2.11.0", "_nodeVersion": "2.2.1", "_npmUser": {"name": "d2l-travis-deploy", "email": "<EMAIL>"}, "maintainers": [{"name": "d2l-travis-deploy", "email": "<EMAIL>"}], "dist": {"shasum": "369b867c40080ba35921530df5a26dad3390d4f1", "tarball": "https://registry.npmjs.org/ecdsa-sig-formatter/-/ecdsa-sig-formatter-1.0.1.tgz", "integrity": "sha512-wsxwVpHRXqqxBcIBqlXncYrLDva8RbJ78GQBTAT0yhQp5+T4lDxqRjXk8VG2IJ4gz+eaWgx7BPs0RydVk/C9cA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHhjyxCJ3I6u7YOmPaHvFbgGxdbTho2Ln5ZGfe+kU8PRAiAj8g4glsfMG6Fa5MomiM0deGzzuufLehTxV9h+GpeOhQ=="}]}, "directories": {}}, "1.0.2": {"name": "ecdsa-sig-formatter", "version": "1.0.2", "description": "Translate ECDSA signatures between ASN.1/DER and JOSE-style concatenation", "main": "src/ecdsa-sig-formatter.js", "scripts": {"test": "istanbul cover --root src _mocha -- spec", "report-cov": "cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "git+ssh://**************/Brightspace/node-ecdsa-sig-formatter.git"}, "keywords": ["ecdsa", "der", "asn.1", "jwt", "jwa", "jsonwebtoken", "jose"], "author": {"name": "D2L Corporation"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/Brightspace/node-ecdsa-sig-formatter/issues"}, "homepage": "https://github.com/Brightspace/node-ecdsa-sig-formatter#readme", "dependencies": {"asn1.js": "^2.0.3", "base64-url": "^1.2.1"}, "devDependencies": {"bench": "^0.3.5", "chai": "^3.0.0", "coveralls": "^2.11.2", "istanbul": "^0.3.15", "mocha": "^2.2.5"}, "gitHead": "426ae016eadb72400f96a0b82a29b6500b4289e5", "_id": "ecdsa-sig-formatter@1.0.2", "_shasum": "2074b4bd06be5e7479c9f71e73358bc3deea4a9b", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "d2l-travis-deploy", "email": "<EMAIL>"}, "maintainers": [{"name": "d2l-travis-deploy", "email": "<EMAIL>"}], "dist": {"shasum": "2074b4bd06be5e7479c9f71e73358bc3deea4a9b", "tarball": "https://registry.npmjs.org/ecdsa-sig-formatter/-/ecdsa-sig-formatter-1.0.2.tgz", "integrity": "sha512-1GNuV5W8v1rU+5AxNrPTJxF8pkDk3U2a9kxVcVoer1jpF0ZjzjTcVw8zwbA78UJzObKWaswmwJvVZEglhboCKQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDxXj0KbBWT0BtVOFAAMxetqqHFR/k66Xbb0NkqMcBu0gIgKdha7SkL/Layj2zR7GfwsN/gEEGS9gg/HfwS/C/CiaE="}]}, "directories": {}}, "1.0.5": {"name": "ecdsa-sig-formatter", "version": "1.0.5", "description": "Translate ECDSA signatures between ASN.1/DER and JOSE-style concatenation", "main": "src/ecdsa-sig-formatter.js", "scripts": {"check-style": "eslint .", "pretest": "npm run check-style", "test": "istanbul cover --root src _mocha -- spec", "report-cov": "cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "git+ssh://**************/Brightspace/node-ecdsa-sig-formatter.git"}, "keywords": ["ecdsa", "der", "asn.1", "jwt", "jwa", "jsonwebtoken", "jose"], "author": {"name": "D2L Corporation"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/Brightspace/node-ecdsa-sig-formatter/issues"}, "homepage": "https://github.com/Brightspace/node-ecdsa-sig-formatter#readme", "dependencies": {"base64-url": "^1.2.1"}, "devDependencies": {"bench": "^0.3.6", "chai": "^3.4.1", "coveralls": "^2.11.6", "elliptic": "^6.1.0", "eslint": "^1.10.3", "eslint-config-brightspace": "^0.1.0", "istanbul": "^0.4.2", "jwk-to-pem": "^1.2.4", "mocha": "^2.3.4"}, "gitHead": "a7bed5fbdc737798c0f499156cdbeeeaf7138542", "_id": "ecdsa-sig-formatter@1.0.5", "_shasum": "0d0f32b638611f6b8f36ffd305a3e512ea5444e6", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.4.1", "_npmUser": {"name": "d2l-travis-deploy", "email": "<EMAIL>"}, "dist": {"shasum": "0d0f32b638611f6b8f36ffd305a3e512ea5444e6", "tarball": "https://registry.npmjs.org/ecdsa-sig-formatter/-/ecdsa-sig-formatter-1.0.5.tgz", "integrity": "sha512-+iTWaj9CQHnvYQ7keATosCbg6I60hVQoT3SGAva2dHwGuWe9enc3Irl60Bb6Mm9+eEZ9xukm1UMrEY1dSHC03A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCOneZRXKfJSEsFHR1nCUdzZCFXOEc1qW70pNx913hkKgIgMQmmNLXLmJ79phW/X5gpWZfno6fPZjSxrv2PIiK6mn8="}]}, "maintainers": [{"name": "d2l-travis-deploy", "email": "<EMAIL>"}], "directories": {}}, "1.0.6": {"name": "ecdsa-sig-formatter", "version": "1.0.6", "description": "Translate ECDSA signatures between ASN.1/DER and JOSE-style concatenation", "main": "src/ecdsa-sig-formatter.js", "scripts": {"check-style": "eslint .", "pretest": "npm run check-style", "test": "istanbul cover --root src _mocha -- spec", "report-cov": "cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "git+ssh://**************/Brightspace/node-ecdsa-sig-formatter.git"}, "keywords": ["ecdsa", "der", "asn.1", "jwt", "jwa", "jsonwebtoken", "jose"], "author": {"name": "D2L Corporation"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/Brightspace/node-ecdsa-sig-formatter/issues"}, "homepage": "https://github.com/Brightspace/node-ecdsa-sig-formatter#readme", "dependencies": {"base64-url": "^1.2.1"}, "devDependencies": {"bench": "^0.3.6", "chai": "^3.5.0", "coveralls": "^2.11.9", "elliptic": "^6.3.1", "eslint": "^2.12.0", "eslint-config-brightspace": "^0.2.1", "istanbul": "^0.4.3", "jwk-to-pem": "^1.2.5", "mocha": "^2.5.3"}, "gitHead": "f232d5cddb120b92b552635fca21167b7dd6e8e1", "_id": "ecdsa-sig-formatter@1.0.6", "_shasum": "19968e66a2f4366210ef3f791e5454d760f7722d", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "5.11.1", "_npmUser": {"name": "d2l-travis-deploy", "email": "<EMAIL>"}, "dist": {"shasum": "19968e66a2f4366210ef3f791e5454d760f7722d", "tarball": "https://registry.npmjs.org/ecdsa-sig-formatter/-/ecdsa-sig-formatter-1.0.6.tgz", "integrity": "sha512-Bft5z8c4ga3uS6X47orWnfAKCeeRVP1qYrvo/ndyMTZ4yZxVeV3owSNtcNCYDWvp2CMNyUAcdmtRE/22NDvqIA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG6uVpxwfHRcUprWgDaOf0HnzaoLpybTrMY9q5/icVUWAiEA6cUeTg850+0adAJ2R+x7NoOjcCknHivBbO5X1Iw7gBY="}]}, "maintainers": [{"name": "d2l-travis-deploy", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/ecdsa-sig-formatter-1.0.6.tgz_1466176123955_0.7629159067291766"}, "directories": {}}, "1.0.7": {"name": "ecdsa-sig-formatter", "version": "1.0.7", "description": "Translate ECDSA signatures between ASN.1/DER and JOSE-style concatenation", "main": "src/ecdsa-sig-formatter.js", "scripts": {"check-style": "eslint .", "pretest": "npm run check-style", "test": "istanbul cover --root src _mocha -- spec", "report-cov": "cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "git+ssh://**************/Brightspace/node-ecdsa-sig-formatter.git"}, "keywords": ["ecdsa", "der", "asn.1", "jwt", "jwa", "jsonwebtoken", "jose"], "author": {"name": "D2L Corporation"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/Brightspace/node-ecdsa-sig-formatter/issues"}, "homepage": "https://github.com/Brightspace/node-ecdsa-sig-formatter#readme", "dependencies": {"base64-url": "^1.2.1"}, "devDependencies": {"bench": "^0.3.6", "chai": "^3.5.0", "coveralls": "^2.11.9", "elliptic": "^6.3.1", "eslint": "^2.12.0", "eslint-config-brightspace": "^0.2.1", "istanbul": "^0.4.3", "jwk-to-pem": "^1.2.5", "mocha": "^2.5.3"}, "gitHead": "eed19df2c57d8ab04dee02f151ae7ed7bbdbba8f", "_id": "ecdsa-sig-formatter@1.0.7", "_shasum": "3137e976a1d6232517e2513e04e32f79bcbdf126", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "5.11.1", "_npmUser": {"name": "d2l-travis-deploy", "email": "<EMAIL>"}, "dist": {"shasum": "3137e976a1d6232517e2513e04e32f79bcbdf126", "tarball": "https://registry.npmjs.org/ecdsa-sig-formatter/-/ecdsa-sig-formatter-1.0.7.tgz", "integrity": "sha512-eQGBiG9EKukpMONQkopQv3Xv3JN4fSL+aupxz8o1B4+TSB+fvuFQcfU0u7SMIbLacpOe4c8uS9qnXd/jy3OqOA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCVE8O8XVt/FweesC5XmxKqx9wjbyx07SaDaUAdte68ygIgILGRVYyWG7YqCzg5OJ+0eVBwDpu26awmeEgVHE5FXKE="}]}, "maintainers": [{"name": "d2l-travis-deploy", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/ecdsa-sig-formatter-1.0.7.tgz_1466263566774_0.3799667169805616"}, "directories": {}}, "1.0.8": {"name": "ecdsa-sig-formatter", "version": "1.0.8", "description": "Translate ECDSA signatures between ASN.1/DER and JOSE-style concatenation", "main": "src/ecdsa-sig-formatter.js", "scripts": {"check-style": "eslint .", "pretest": "npm run check-style", "test": "istanbul cover --root src _mocha -- spec", "report-cov": "cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "git+ssh://**************/Brightspace/node-ecdsa-sig-formatter.git"}, "keywords": ["ecdsa", "der", "asn.1", "jwt", "jwa", "jsonwebtoken", "jose"], "author": {"name": "D2L Corporation"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/Brightspace/node-ecdsa-sig-formatter/issues"}, "homepage": "https://github.com/Brightspace/node-ecdsa-sig-formatter#readme", "dependencies": {"base64-url": "^1.2.1", "safe-buffer": "^5.0.1"}, "devDependencies": {"bench": "^0.3.6", "chai": "^3.5.0", "coveralls": "^2.11.9", "elliptic": "^6.3.1", "eslint": "^2.12.0", "eslint-config-brightspace": "^0.2.1", "istanbul": "^0.4.3", "jwk-to-pem": "^1.2.5", "mocha": "^2.5.3"}, "gitHead": "a711c0825818906f24993cffb6d8a02b97070537", "_id": "ecdsa-sig-formatter@1.0.8", "_shasum": "a9e3f5534e3755a56f8c982fb15a05a6364a1dab", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "5.12.0", "_npmUser": {"name": "d2l-travis-deploy", "email": "<EMAIL>"}, "dist": {"shasum": "a9e3f5534e3755a56f8c982fb15a05a6364a1dab", "tarball": "https://registry.npmjs.org/ecdsa-sig-formatter/-/ecdsa-sig-formatter-1.0.8.tgz", "integrity": "sha512-ULDtFASgTauQ2zeSgA4DtdT9C1OVeoJNN1uuUjuAGSZw7z9UUcSGYcqPCfBP/dB+mtNv4WJpuB4AYqHryvXALQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGX0SSyxXZ4PHjxxY2jewmCUXCd6AzmyPAneCR8apU6KAiAqRCu0D123muXY4a6wQ3aRrOlDLAfyZxCxpzm+Oasveg=="}]}, "maintainers": [{"name": "d2l-travis-deploy", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/ecdsa-sig-formatter-1.0.8.tgz_1479225007727_0.3844668928068131"}, "directories": {}}, "1.0.9": {"name": "ecdsa-sig-formatter", "version": "1.0.9", "description": "Translate ECDSA signatures between ASN.1/DER and JOSE-style concatenation", "main": "src/ecdsa-sig-formatter.js", "scripts": {"check-style": "eslint .", "pretest": "npm run check-style", "test": "istanbul cover --root src _mocha -- spec", "report-cov": "cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "git+ssh://**************/Brightspace/node-ecdsa-sig-formatter.git"}, "keywords": ["ecdsa", "der", "asn.1", "jwt", "jwa", "jsonwebtoken", "jose"], "author": {"name": "D2L Corporation"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/Brightspace/node-ecdsa-sig-formatter/issues"}, "homepage": "https://github.com/Brightspace/node-ecdsa-sig-formatter#readme", "dependencies": {"base64url": "^2.0.0", "safe-buffer": "^5.0.1"}, "devDependencies": {"bench": "^0.3.6", "chai": "^3.5.0", "coveralls": "^2.11.9", "elliptic": "^6.3.1", "eslint": "^2.12.0", "eslint-config-brightspace": "^0.2.1", "istanbul": "^0.4.3", "jwk-to-pem": "^1.2.5", "mocha": "^2.5.3"}, "gitHead": "e32ea222f0ffdde667d8e97588135953a4c34555", "_id": "ecdsa-sig-formatter@1.0.9", "_shasum": "4bc926274ec3b5abb5016e7e1d60921ac262b2a1", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "5.12.0", "_npmUser": {"name": "d2l-travis-deploy", "email": "<EMAIL>"}, "dist": {"shasum": "4bc926274ec3b5abb5016e7e1d60921ac262b2a1", "tarball": "https://registry.npmjs.org/ecdsa-sig-formatter/-/ecdsa-sig-formatter-1.0.9.tgz", "integrity": "sha512-J/mTMRuaEY85fUpQmLRXBg3tOk1zshbIIazSoUzdpjdD7hdiYKR/VpiL6JNWbuSjbSYdugmgBv8m/O1kK0CQRw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAwLNEN9wxCvdG7qfyU1uCb+D+a2zIUhiIM+s4p2ej/eAiBTSnzswi7qEEdEy4YXkJjuQVudzuzJVx4Dt6Vvp7CX+Q=="}]}, "maintainers": [{"name": "d2l-travis-deploy", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/ecdsa-sig-formatter-1.0.9.tgz_1480952424192_0.9221549024805427"}, "directories": {}}, "1.0.10": {"name": "ecdsa-sig-formatter", "version": "1.0.10", "description": "Translate ECDSA signatures between ASN.1/DER and JOSE-style concatenation", "main": "src/ecdsa-sig-formatter.js", "scripts": {"check-style": "eslint .", "pretest": "npm run check-style", "test": "istanbul cover --root src _mocha -- spec", "report-cov": "cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "git+ssh://**************/Brightspace/node-ecdsa-sig-formatter.git"}, "keywords": ["ecdsa", "der", "asn.1", "jwt", "jwa", "jsonwebtoken", "jose"], "author": {"name": "D2L Corporation"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/Brightspace/node-ecdsa-sig-formatter/issues"}, "homepage": "https://github.com/Brightspace/node-ecdsa-sig-formatter#readme", "dependencies": {"safe-buffer": "^5.0.1"}, "devDependencies": {"bench": "^0.3.6", "chai": "^3.5.0", "coveralls": "^2.11.9", "eslint": "^2.12.0", "eslint-config-brightspace": "^0.2.1", "istanbul": "^0.4.3", "jwk-to-pem": "^1.2.5", "mocha": "^2.5.3", "native-crypto": "^1.7.0"}, "gitHead": "b07b82c51f9be13e1ca291239983d7b052863f77", "_id": "ecdsa-sig-formatter@1.0.10", "_shasum": "1c595000f04a8897dfb85000892a0f4c33af86c3", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "5.12.0", "_npmUser": {"name": "d2l-travis-deploy", "email": "<EMAIL>"}, "dist": {"shasum": "1c595000f04a8897dfb85000892a0f4c33af86c3", "tarball": "https://registry.npmjs.org/ecdsa-sig-formatter/-/ecdsa-sig-formatter-1.0.10.tgz", "fileCount": 6, "unpackedSize": 19847, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+Z/rCRA9TVsSAnZWagAAzgQQAJOsuIKhrswNT/4bk34b\npKfVaKvj3OveIAUSeoINK9Q1X05xMlR6zu7jw3CL/HMw841L4/UV2BTVGp2y\noX0ES6nrToMTofGv6+0s2PuVYKymS86KKDizDJ5CUGMRTaB4/kTJQqAZDxXZ\nT2FPojKFznTetwB1Vz+hS1qOQfdUQ3QhbjTEj2/ExTSX/8U411+u+VmwT20m\nAiopghYiBs1SN5TcSmG+5a0lSxyh75IzGxkhss0hogk9RGqmp5bsD1j/7cYe\nZCBhadNS+Dyxkt4+h9KXHeCa3VzQub2ejaI1GHasHLQfu4A1W0wcP63HeOBZ\nAKYt1++aSlbefl5BTN712W+FSZuvbXI3uamxS4zR90MukzCr+PBiyo5tq0tm\nckQ00UCj/vxf9hLm9tN2EzjNAsQhXnODXnxzjvqHGb48yaQniR07fm5+z1V1\nrXPwaQLa7qIrE8o7Yl5YMLTGAMFX2f3+nY39rruFPY+/truORu8YmFORxgP6\n5baDe1l0wlvXd68REnNztIbwitksN2GSXWAAMQwCEHrWk3ixdrPi59CZLWqW\nMPKaEJzKaALAStrkz7eBCi3b+4P2lQvPoq/ol57lcUvs3wy4EKF0+aGdfU3S\nx/k9s0XZJvbqAKJOq05oH91pdZngSO//FPtfBOW1Ymkyj+SsdakNABp54CPz\n1cr5\r\n=rR+E\r\n-----END PGP SIGNATURE-----\r\n", "integrity": "sha512-0XqJH78hi7Cx141usy/GT+gSzwZs58W144EhBPCFWM5bQj5nDhC+CnGTAuJDIYkuXZei+m7/isuIBqhaf1XAEA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCk5UMB0RzTTjVow7BvneBS+h8u80wGanpGAu5YvHrJEwIhAJGiEIvmCpo7za0lZQJU5asNWaBKPGXTIhV+drqbP8nj"}]}, "maintainers": [{"name": "d2l-travis-deploy", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ecdsa-sig-formatter_1.0.10_1526308842657_0.3204613548609856"}, "_hasShrinkwrap": false}, "1.0.11": {"name": "ecdsa-sig-formatter", "version": "1.0.11", "description": "Translate ECDSA signatures between ASN.1/DER and JOSE-style concatenation", "main": "src/ecdsa-sig-formatter.js", "scripts": {"check-style": "eslint .", "pretest": "npm run check-style", "test": "istanbul cover --root src _mocha -- spec", "report-cov": "cat ./coverage/lcov.info | coveralls"}, "typings": "./src/ecdsa-sig-formatter.d.ts", "repository": {"type": "git", "url": "git+ssh://**************/Brightspace/node-ecdsa-sig-formatter.git"}, "keywords": ["ecdsa", "der", "asn.1", "jwt", "jwa", "jsonwebtoken", "jose"], "author": {"name": "D2L Corporation"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/Brightspace/node-ecdsa-sig-formatter/issues"}, "homepage": "https://github.com/Brightspace/node-ecdsa-sig-formatter#readme", "dependencies": {"safe-buffer": "^5.0.1"}, "devDependencies": {"bench": "^0.3.6", "chai": "^3.5.0", "coveralls": "^2.11.9", "eslint": "^2.12.0", "eslint-config-brightspace": "^0.2.1", "istanbul": "^0.4.3", "jwk-to-pem": "^1.2.5", "mocha": "^2.5.3", "native-crypto": "^1.7.0"}, "gitHead": "c730235f4a54526f33f5309d80beb12ee298db6b", "_id": "ecdsa-sig-formatter@1.0.11", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.0", "_npmUser": {"name": "d2l-travis-deploy", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-nagl3RYrbNv6kQkeJIpt6NJZy8twLB/2vtz6yN9Z4vRKHN4/QZJIEbqohALSgwKdnksuY3k5Addp5lg8sVoVcQ==", "shasum": "ae0f0fa2d85045ef14a817daa3ce9acd0489e5bf", "tarball": "https://registry.npmjs.org/ecdsa-sig-formatter/-/ecdsa-sig-formatter-1.0.11.tgz", "fileCount": 7, "unpackedSize": 20588, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcS4BeCRA9TVsSAnZWagAAQHgQAJsWXbsQEgHH9CO915pO\nuosLqkOVmPVXT34pulC2fOjiiD3IAr9gCFrup/pfO26c2AInJh8BxCmfEm7z\nbS4QX8d4PUOHhoMDYoTOZnuRT5yiis0nynycjRF1ajexCtU+8jOovmQBo5dn\nfUK0odtULQg/bi84vinmGGK3s57lcD5PxHuzvR45io41wszJB90Pdv2vFMGf\nJUYDybFKHKZJbpbQ93VMvfTWDlFNnlRn4Bz/jrV4uGgDgkHJHlNARZSp/TLa\nwTyRzW98c2PQzMdZJZTnFz6b9hG2Cj0d0lIY1hxX+XQ422lEvaa6nQgKqVb6\nA0qkFRJoP/43/gjt0ZXVu0y09qdQCsnRzN2uxDyUK87QfZcuWPYxmiv1XEfX\n+aDtu1lKLgxT/1r01QleQtN2fw5FZSU57/Zw0KC9uUWDEvTcLMJDT447hvk2\n2cVt5XWOAtFwxAfdtYlGjq0qLSIM/vm8JwNjdaTfbDsLXMSwZrp/7egpZFX6\nsE/XJ4hxC0niiXP6QrrRFKynXm6ZWnzB49dAeDkzCiB4iJao8WKYptdV9F7W\n1dpuWrb28hHY4jaDI/7dumP/fvmCvsGLay9nRH5iE5ePU4JXyJp0AujvqV+c\noDM8h2/+zHkPYffosqmEZf2NxeB5rIAzFZrcBQHKYp7rKqCvztowBC6730Jb\n6eYG\r\n=3ns0\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCFPZcmrEORNGUiOCBVX+Jjwq3ESmM26Z5y6tP8DmGY3gIhANbjcgEPCX68J8RLDb5cvIioQ/ezZPxRw+VYxljOa80M"}]}, "maintainers": [{"name": "d2l-travis-deploy", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ecdsa-sig-formatter_1.0.11_1548451933314_0.5170227155509808"}, "_hasShrinkwrap": false}}, "readme": "# ecdsa-sig-formatter\n\n[![Build Status](https://travis-ci.org/Brightspace/node-ecdsa-sig-formatter.svg?branch=master)](https://travis-ci.org/Brightspace/node-ecdsa-sig-formatter) [![Coverage Status](https://coveralls.io/repos/Brightspace/node-ecdsa-sig-formatter/badge.svg)](https://coveralls.io/r/Brightspace/node-ecdsa-sig-formatter)\n\nTranslate between JOSE and ASN.1/DER encodings for ECDSA signatures\n\n## Install\n```sh\nnpm install ecdsa-sig-formatter --save\n```\n\n## Usage\n```js\nvar format = require('ecdsa-sig-formatter');\n\nvar derSignature = '..'; // asn.1/DER encoded ecdsa signature\n\nvar joseSignature = format.derToJose(derSignature);\n\n```\n\n### API\n\n---\n\n#### `.derToJose(Buffer|String signature, String alg)` -> `String`\n\nConvert the ASN.1/DER encoded signature to a JOSE-style concatenated signature.\nReturns a _base64 url_ encoded `String`.\n\n* If _signature_ is a `String`, it should be _base64_ encoded\n* _alg_ must be one of _ES256_, _ES384_ or _ES512_\n\n---\n\n#### `.joseToDer(Buffer|String signature, String alg)` -> `Buffer`\n\nConvert the JOSE-style concatenated signature to an ASN.1/DER encoded\nsignature. Returns a `Buffer`\n\n* If _signature_ is a `String`, it should be _base64 url_ encoded\n* _alg_ must be one of _ES256_, _ES384_ or _ES512_\n\n## Contributing\n\n1. **Fork** the repository. Committing directly against this repository is\n   highly discouraged.\n\n2. Make your modifications in a branch, updating and writing new unit tests\n   as necessary in the `spec` directory.\n\n3. Ensure that all tests pass with `npm test`\n\n4. `rebase` your changes against master. *Do not merge*.\n\n5. Submit a pull request to this repository. Wait for tests to run and someone\n   to chime in.\n\n### Code Style\n\nThis repository is configured with [EditorConfig][EditorConfig] and\n[ESLint][ESLint] rules.\n\n[EditorConfig]: http://editorconfig.org/\n[ESLint]: http://eslint.org\n", "maintainers": [{"name": "d2l-travis-deploy", "email": "<EMAIL>"}], "time": {"modified": "2022-06-16T04:36:24.613Z", "created": "2015-06-10T19:40:01.611Z", "1.0.0": "2015-06-10T19:40:01.611Z", "1.0.1": "2015-06-10T20:54:59.800Z", "1.0.2": "2015-06-12T20:39:55.056Z", "1.0.5": "2016-01-20T01:20:23.559Z", "1.0.6": "2016-06-17T15:08:45.103Z", "1.0.7": "2016-06-18T15:26:07.961Z", "1.0.8": "2016-11-15T15:50:08.273Z", "1.0.9": "2016-12-05T15:40:24.823Z", "1.0.10": "2018-05-14T14:40:42.729Z", "1.0.11": "2019-01-25T21:32:13.447Z"}, "homepage": "https://github.com/Brightspace/node-ecdsa-sig-formatter#readme", "keywords": ["ecdsa", "der", "asn.1", "jwt", "jwa", "jsonwebtoken", "jose"], "repository": {"type": "git", "url": "git+ssh://**************/Brightspace/node-ecdsa-sig-formatter.git"}, "author": {"name": "D2L Corporation"}, "bugs": {"url": "https://github.com/Brightspace/node-ecdsa-sig-formatter/issues"}, "license": "Apache-2.0", "readmeFilename": "README.md"}