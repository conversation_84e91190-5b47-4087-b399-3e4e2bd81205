{"_id": "ipaddr.js", "_rev": "70-8c1860ba0abbf44b73801aa5036e37b5", "name": "ipaddr.js", "description": "A library for manipulating IPv4 and IPv6 addresses in JavaScript.", "dist-tags": {"latest": "2.2.0"}, "versions": {"0.1.0": {"name": "ipaddr.js", "description": "A library for manipulating IPv4 and IPv6 addresses in JavaScript.", "version": "0.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "directories": {"lib": "./lib"}, "dependencies": {"coffee-script": ">= 1.1.1"}, "devDependencies": {"nodeunit": "0.5.3", "uglify-js": "latest"}, "keywords": ["ip", "ipv4", "ipv6"], "repository": {"type": "git", "url": "git://github.com/whitequark/ipaddr.js.git"}, "main": "./lib/ipaddr", "engines": {"node": ">= 0.2.5"}, "_npmJsonOpts": {"file": "/home/<USER>/.npm/ipaddr.js/0.1.0/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "ipaddr.js@0.1.0", "_engineSupported": true, "_npmVersion": "1.0.22", "_nodeVersion": "v0.4.9", "_defaultsLoaded": true, "dist": {"shasum": "d67fc6dcc153b15a8ed475a44158f854728b037e", "tarball": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-0.1.0.tgz", "integrity": "sha512-ABfg+SYt41g0vF5fVpAEvQDhl/25ukPqG2IB0Ep2zIPxkXV99vORwMQTjUX1L/lb04kWY/MEiTA8Md0ZrKyaQQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDBmZFnUMQ2kZlCruQ98HIexia9x9JEQ7Ta2LzvaYXG+wIhAOtL+mfL6ZifXlILjg5Zi0ngAnbw5kVPysNey4aByaI6"}]}, "scripts": {}}, "0.1.1": {"name": "ipaddr.js", "description": "A library for manipulating IPv4 and IPv6 addresses in JavaScript.", "version": "0.1.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "directories": {"lib": "./lib"}, "dependencies": {"coffee-script": ">= 1.1.1"}, "devDependencies": {"nodeunit": "0.5.3", "uglify-js": "latest"}, "keywords": ["ip", "ipv4", "ipv6"], "repository": {"type": "git", "url": "git://github.com/whitequark/ipaddr.js.git"}, "main": "./lib/ipaddr", "engines": {"node": ">= 0.2.5"}, "_npmJsonOpts": {"file": "/home/<USER>/.npm/ipaddr.js/0.1.1/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "ipaddr.js@0.1.1", "_engineSupported": true, "_npmVersion": "1.0.22", "_nodeVersion": "v0.4.10", "_defaultsLoaded": true, "dist": {"shasum": "28c6a7c116a021c555544f906ab1ad540b1d635a", "tarball": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-0.1.1.tgz", "integrity": "sha512-iHoQay5sZjCfr2TBz7X1H7NvIH5cmWUz+ltiNWm+xa1gJS57hl/VWfXp5jPcCt2mxJrpnVeGJfLs67/uFlt0Sg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDeEIVX16Cz0v1uv2IVfzl2sax4g+AbM2Bh7qTWDH5F2gIgA6dDiM4DxK4BPXB33zO/ziecm3xlNJPpTGJ5aR4i1mE="}]}, "scripts": {}}, "0.1.2": {"name": "ipaddr.js", "description": "A library for manipulating IPv4 and IPv6 addresses in JavaScript.", "version": "0.1.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "directories": {"lib": "./lib"}, "dependencies": {}, "devDependencies": {"coffee-script": "~1.6", "nodeunit": "~0.5.3", "uglify-js": "latest"}, "scripts": {"test": "cake build test"}, "keywords": ["ip", "ipv4", "ipv6"], "repository": {"type": "git", "url": "git://github.com/whitequark/ipaddr.js"}, "main": "./lib/ipaddr", "engines": {"node": ">= 0.2.5"}, "bugs": {"url": "https://github.com/whitequark/ipaddr.js/issues"}, "_id": "ipaddr.js@0.1.2", "dist": {"shasum": "6a1fd3d854f5002965c34d7bbcd9b4a8d4b0467e", "tarball": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-0.1.2.tgz", "integrity": "sha512-MGrEjHz4Hk5UVpJXZQ2tHB+bp6xgdRKCAEWdrgFsoAmXCgKAPtj8LqMxgvlWEAj9aN+PpTcvE051uZU3K3kLSQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIH9Cg1sG0axhKgEoqdCY60HJ9BoBaM3uY1qVKU2VQJcMAiEAzzn+PBNUOAFFZhn0VY+MruX6IntPFBIo3Aw84M/70Uw="}]}, "_from": ".", "_npmVersion": "1.3.10", "_npmUser": {"name": "whitequark", "email": "<EMAIL>"}, "maintainers": [{"name": "whitequark", "email": "<EMAIL>"}]}, "0.1.3": {"name": "ipaddr.js", "description": "A library for manipulating IPv4 and IPv6 addresses in JavaScript.", "version": "0.1.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "directories": {"lib": "./lib"}, "dependencies": {}, "devDependencies": {"coffee-script": "~1.6", "nodeunit": "~0.5.3", "uglify-js": "latest"}, "scripts": {"test": "cake build test"}, "keywords": ["ip", "ipv4", "ipv6"], "repository": {"type": "git", "url": "git://github.com/whitequark/ipaddr.js"}, "main": "./lib/ipaddr", "engines": {"node": ">= 0.2.5"}, "bugs": {"url": "https://github.com/whitequark/ipaddr.js/issues"}, "_id": "ipaddr.js@0.1.3", "dist": {"shasum": "27a9ca37f148d2102b0ef191ccbf2c51a8f025c6", "tarball": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-0.1.3.tgz", "integrity": "sha512-VNTGeNv62HOeCjovaJpfxDMWWy96inlYzMOgXF6HD0Fct1eOx0WgcGuTDCmW7qtwApcCuOlqAkAVVWkDe4l2lg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIENcOFqSGdcHzHsFDFLirtxMSO9N9qouPycqPBphXs71AiBwJpGFpB8tGMmozBILsq9WC9jLJ81QOANjbWbu3vsfZg=="}]}, "_from": ".", "_npmVersion": "1.4.4", "_npmUser": {"name": "whitequark", "email": "<EMAIL>"}, "maintainers": [{"name": "whitequark", "email": "<EMAIL>"}]}, "0.1.4": {"name": "ipaddr.js", "description": "A library for manipulating IPv4 and IPv6 addresses in JavaScript.", "version": "0.1.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "directories": {"lib": "./lib"}, "dependencies": {}, "devDependencies": {"coffee-script": "~1.6", "nodeunit": "~0.5.3", "uglify-js": "latest"}, "scripts": {"test": "cake build test"}, "keywords": ["ip", "ipv4", "ipv6"], "repository": {"type": "git", "url": "git://github.com/whitequark/ipaddr.js"}, "main": "./lib/ipaddr", "engines": {"node": ">= 0.2.5"}, "gitHead": "9119cbbda8189888e326e003560694f77692c624", "bugs": {"url": "https://github.com/whitequark/ipaddr.js/issues"}, "_id": "ipaddr.js@0.1.4", "_shasum": "67b1956263daa9725b507700603b401013d2158c", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "whitequark", "email": "<EMAIL>"}, "maintainers": [{"name": "whitequark", "email": "<EMAIL>"}], "dist": {"shasum": "67b1956263daa9725b507700603b401013d2158c", "tarball": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-0.1.4.tgz", "integrity": "sha512-HZ2R7DFsU1VV8ELQaHcFQZ+zN0gRTYyUBljcz499aqH2iRvvaWaycdKKSGO/JHb0U4mQblcZG3RpJE/LRtmSpA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA6XLO7U+2OMTP6udrToYvN8W0pbgLVHkAfrcCmJ/OMaAiEApp+tLeovpE2K1NMBD8qYnX9vuhr51rzshbQkzy7GWv8="}]}}, "0.1.5": {"name": "ipaddr.js", "description": "A library for manipulating IPv4 and IPv6 addresses in JavaScript.", "version": "0.1.5", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "directories": {"lib": "./lib"}, "dependencies": {}, "devDependencies": {"coffee-script": "~1.6", "nodeunit": "~0.5.3", "uglify-js": "latest"}, "scripts": {"test": "cake build test"}, "keywords": ["ip", "ipv4", "ipv6"], "repository": {"type": "git", "url": "git://github.com/whitequark/ipaddr.js"}, "main": "./lib/ipaddr", "engines": {"node": ">= 0.2.5"}, "gitHead": "5914ebd176f5333977f68ce372cc247f13de2a35", "bugs": {"url": "https://github.com/whitequark/ipaddr.js/issues"}, "_id": "ipaddr.js@0.1.5", "_shasum": "33d2693c95fbd4715165328dbfe25fb4fb5bbed8", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "whitequark", "email": "<EMAIL>"}, "maintainers": [{"name": "whitequark", "email": "<EMAIL>"}], "dist": {"shasum": "33d2693c95fbd4715165328dbfe25fb4fb5bbed8", "tarball": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-0.1.5.tgz", "integrity": "sha512-gKJRe4a4fJOR7bHLhmwvkJoXNp7wuulRYXuNeU5b54CnY93IwQ47FyCs6I/nYwYAxJViHn8Z7eu9FSKNbxByow==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDc2Fp7olSl0rGz43JOIWwBlq2T585cFfsjKXGf9LRHiwIgE6Uf/gqDgGFN3stfSqq2vTaySfDbYEmGpXuL7Q5aLW0="}]}}, "0.1.6": {"name": "ipaddr.js", "description": "A library for manipulating IPv4 and IPv6 addresses in JavaScript.", "version": "0.1.6", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "directories": {"lib": "./lib"}, "dependencies": {}, "devDependencies": {"coffee-script": "~1.6", "nodeunit": "~0.5.3", "uglify-js": "latest"}, "scripts": {"test": "cake build test"}, "keywords": ["ip", "ipv4", "ipv6"], "repository": {"type": "git", "url": "git://github.com/whitequark/ipaddr.js"}, "main": "./lib/ipaddr", "engines": {"node": ">= 0.2.5"}, "license": "MIT", "gitHead": "87595aade23e18114b05efa09e1d46ea2ef20c12", "bugs": {"url": "https://github.com/whitequark/ipaddr.js/issues"}, "_id": "ipaddr.js@0.1.6", "_shasum": "8f0530b217993873025fd4b72bdfd69bc56a9a12", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "whitequark", "email": "<EMAIL>"}, "maintainers": [{"name": "whitequark", "email": "<EMAIL>"}], "dist": {"shasum": "8f0530b217993873025fd4b72bdfd69bc56a9a12", "tarball": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-0.1.6.tgz", "integrity": "sha512-avul71onfwuyssP7kPP7g8/ORmngf9xzi5V46oNAqbdYWUS822vly/eJq32KtWLlohIfnoI/z1catcnCLNaDFQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD8LZgXvkqLiNQZfAaFAj1dse6GLRTwF3ug6SW7Nj3YzwIhAOsRSYZo6ol53vcx2EZxdrkWDoHibCWr5Mk8GYsXYrY1"}]}}, "0.1.7": {"name": "ipaddr.js", "description": "A library for manipulating IPv4 and IPv6 addresses in JavaScript.", "version": "0.1.7", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "directories": {"lib": "./lib"}, "dependencies": {}, "devDependencies": {"coffee-script": "~1.6", "nodeunit": "~0.5.3", "uglify-js": "latest"}, "scripts": {"test": "cake build test"}, "keywords": ["ip", "ipv4", "ipv6"], "repository": {"type": "git", "url": "git://github.com/whitequark/ipaddr.js"}, "main": "./lib/ipaddr", "engines": {"node": ">= 0.2.5"}, "license": "MIT", "gitHead": "5c3e47429c1497d47caeb81e76c99d82da87d4cf", "bugs": {"url": "https://github.com/whitequark/ipaddr.js/issues"}, "_id": "ipaddr.js@0.1.7", "_shasum": "c08aaab60273d6c041e58f0dd2dafad3d43ea40f", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "whitequark", "email": "<EMAIL>"}, "maintainers": [{"name": "whitequark", "email": "<EMAIL>"}], "dist": {"shasum": "c08aaab60273d6c041e58f0dd2dafad3d43ea40f", "tarball": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-0.1.7.tgz", "integrity": "sha512-N58/LoPvv+Pe/Kc2HqML+xcbs249Zk65jClv+qadnh6i1ew5gJ6rOfrKfRnNLACj0wQ2AR3pfKI3hzvkP5BULA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIB9AQgROAvSzaTMbdHlaab36+3X6ca0webDh//wX7JgqAiEAs73h3qziijOjUgJZ6jGXWz+4L8cw4ZhdcZIzYKOp39w="}]}}, "0.1.8": {"name": "ipaddr.js", "description": "A library for manipulating IPv4 and IPv6 addresses in JavaScript.", "version": "0.1.8", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "directories": {"lib": "./lib"}, "dependencies": {}, "devDependencies": {"coffee-script": "~1.6", "nodeunit": "~0.5.3", "uglify-js": "latest"}, "scripts": {"test": "cake build test"}, "keywords": ["ip", "ipv4", "ipv6"], "repository": {"type": "git", "url": "git://github.com/whitequark/ipaddr.js"}, "main": "./lib/ipaddr", "engines": {"node": ">= 0.2.5"}, "license": "MIT", "gitHead": "3099dba20984caa73a83864ee582548413a425d8", "bugs": {"url": "https://github.com/whitequark/ipaddr.js/issues"}, "_id": "ipaddr.js@0.1.8", "_shasum": "27442eda77b626c44724b4aa8a1867e8410579ee", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "whitequark", "email": "<EMAIL>"}, "maintainers": [{"name": "whitequark", "email": "<EMAIL>"}], "dist": {"shasum": "27442eda77b626c44724b4aa8a1867e8410579ee", "tarball": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-0.1.8.tgz", "integrity": "sha512-NyEmy1F0c5JICj23Mgc1rGDYzqFyaYYTbD32hYJVGJegdaNGj61rb3+aTvCDv7ZDEcMeQEo2lXEsA/Jb6Beg8w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDiJHTn8LSqJt0AFAVOVAvjLtHbOCGSi19u0b/qBCdXXgIgaeA5pU8jCBEhpzzn9crW3yg4XICWZlm2vIfDwwjSOcw="}]}}, "0.1.9": {"name": "ipaddr.js", "description": "A library for manipulating IPv4 and IPv6 addresses in JavaScript.", "version": "0.1.9", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "directories": {"lib": "./lib"}, "dependencies": {}, "devDependencies": {"coffee-script": "~1.6", "nodeunit": "~0.5.3", "uglify-js": "latest"}, "scripts": {"test": "cake build test"}, "keywords": ["ip", "ipv4", "ipv6"], "repository": {"type": "git", "url": "git://github.com/whitequark/ipaddr.js"}, "main": "./lib/ipaddr", "engines": {"node": ">= 0.2.5"}, "license": "MIT", "gitHead": "d51df7aa41ef1875215ae4ffbd324c486f8c2799", "bugs": {"url": "https://github.com/whitequark/ipaddr.js/issues"}, "_id": "ipaddr.js@0.1.9", "_shasum": "a9c78ccc12dc9010f296ab9aef2f61f432d69efa", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "whitequark", "email": "<EMAIL>"}, "maintainers": [{"name": "whitequark", "email": "<EMAIL>"}], "dist": {"shasum": "a9c78ccc12dc9010f296ab9aef2f61f432d69efa", "tarball": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-0.1.9.tgz", "integrity": "sha512-dp0C1YYJuZMlFCM9eah1GbCVGRwcvLuCHDWfqUhIS6WqLO6jFK3GJHRQrD+OTID/mS3grl3gqGJ76QsOTpNsRw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCCNremKIvPDnni+d07GF4AOJUfKXjFNqMhs9Wmnd7z7wIhAI9u7gyT67Gnl6Q7aXKlgKDWBoGqlgpkQg58fGK3siEH"}]}}, "1.0.0": {"name": "ipaddr.js", "description": "A library for manipulating IPv4 and IPv6 addresses in JavaScript.", "version": "1.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "directories": {"lib": "./lib"}, "dependencies": {}, "devDependencies": {"coffee-script": "~1.6", "nodeunit": "~0.5.3", "uglify-js": "latest"}, "scripts": {"test": "cake build test"}, "keywords": ["ip", "ipv4", "ipv6"], "repository": {"type": "git", "url": "git://github.com/whitequark/ipaddr.js"}, "main": "./lib/ipaddr", "engines": {"node": ">= 0.2.5"}, "license": "MIT", "gitHead": "f8ee5ce11cd4f1f940903671808505dfe02ce90a", "bugs": {"url": "https://github.com/whitequark/ipaddr.js/issues"}, "_id": "ipaddr.js@1.0.0", "_shasum": "dc6723c4f83913106a6702113a2034696ec03469", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "whitequark", "email": "<EMAIL>"}, "maintainers": [{"name": "whitequark", "email": "<EMAIL>"}], "dist": {"shasum": "dc6723c4f83913106a6702113a2034696ec03469", "tarball": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.0.0.tgz", "integrity": "sha512-WPZr394KElHiLNkCP/RobG5SJW6DWyM14PL6cngn0A9rQY8dxQWJhIQvGvhlbT1tb+SFyS4OuXNZyF1Ve+1nxg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD4VQ0ADBb5XASnV8DU+hPkJ7Kq7T2nFYNcsPE4ws1C9AIhAOPnDOhRZz4TAH8tAd+1GihbS8RFioTRChG1hlYrB28z"}]}}, "1.0.1": {"name": "ipaddr.js", "description": "A library for manipulating IPv4 and IPv6 addresses in JavaScript.", "version": "1.0.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "directories": {"lib": "./lib"}, "dependencies": {}, "devDependencies": {"coffee-script": "~1.6", "nodeunit": "~0.5.3", "uglify-js": "latest"}, "scripts": {"test": "cake build test"}, "keywords": ["ip", "ipv4", "ipv6"], "repository": {"type": "git", "url": "git://github.com/whitequark/ipaddr.js"}, "main": "./lib/ipaddr", "engines": {"node": ">= 0.2.5"}, "license": "MIT", "gitHead": "0a5a26d9317a58d67047e7f32b5b1bbe7f2f7fbf", "bugs": {"url": "https://github.com/whitequark/ipaddr.js/issues"}, "_id": "ipaddr.js@1.0.1", "_shasum": "5f38801dc73e0400fc7076386f6ed5215fbd8f95", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "whitequark", "email": "<EMAIL>"}, "maintainers": [{"name": "whitequark", "email": "<EMAIL>"}], "dist": {"shasum": "5f38801dc73e0400fc7076386f6ed5215fbd8f95", "tarball": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.0.1.tgz", "integrity": "sha512-VDuHXKb7iReNvdDAy3bWIvFsquez+WiGLQGfGoGjo47egY7yfWHhsX3krufz56Q90wbqp5LCyyQookVj6BSLnA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCLw6l7BMvE8jO1WFk4ti4bDFnxFepnacV7Drq4FYU4HgIger+FwWAb3y64kOanFHOrchKK4exMhpOeDDWk8ngV0fQ="}]}}, "1.0.3": {"name": "ipaddr.js", "description": "A library for manipulating IPv4 and IPv6 addresses in JavaScript.", "version": "1.0.3", "author": {"name": "whitequark", "email": "<EMAIL>"}, "directories": {"lib": "./lib"}, "dependencies": {}, "devDependencies": {"coffee-script": "~1.6", "nodeunit": "~0.5.3", "uglify-js": "latest"}, "scripts": {"test": "cake build test"}, "keywords": ["ip", "ipv4", "ipv6"], "repository": {"type": "git", "url": "git://github.com/whitequark/ipaddr.js"}, "main": "./lib/ipaddr", "engines": {"node": ">= 0.2.5"}, "license": "MIT", "gitHead": "bf26b1f5d00cf8526a54f79db994eea3e8526e10", "bugs": {"url": "https://github.com/whitequark/ipaddr.js/issues"}, "_id": "ipaddr.js@1.0.3", "_shasum": "2a9df7be73ea92aadb0d7f377497decd8e6d01bb", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "whitequark", "email": "<EMAIL>"}, "maintainers": [{"name": "whitequark", "email": "<EMAIL>"}], "dist": {"shasum": "2a9df7be73ea92aadb0d7f377497decd8e6d01bb", "tarball": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.0.3.tgz", "integrity": "sha512-SSSKelZ/V01QHZbjgxES5CoO22ABQEebr5vBrsf34jdxnGHINl/Npltz2pHypf5w2fxgxHL2mlKPDnQInezNMg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCcDSr3oV+OXuL770wthTTz0FvfokRGG4vC1ryZdXoDzwIhANy3NBL5H820T6hv6UHSwyW2FKveiaQMtEVm1k9C4uKh"}]}}, "1.0.4": {"name": "ipaddr.js", "description": "A library for manipulating IPv4 and IPv6 addresses in JavaScript.", "version": "1.0.4", "author": {"name": "whitequark", "email": "<EMAIL>"}, "directories": {"lib": "./lib"}, "dependencies": {}, "devDependencies": {"coffee-script": "~1.6", "nodeunit": ">=0.8.2 <0.8.7", "uglify-js": "latest"}, "scripts": {"test": "cake build test"}, "keywords": ["ip", "ipv4", "ipv6"], "repository": {"type": "git", "url": "git://github.com/whitequark/ipaddr.js"}, "main": "./lib/ipaddr", "engines": {"node": ">= 0.10"}, "license": "MIT", "gitHead": "9885a29dfaab9519e3ec46f99c0bc0b81660a0d9", "bugs": {"url": "https://github.com/whitequark/ipaddr.js/issues"}, "_id": "ipaddr.js@1.0.4", "_shasum": "ef715deab1e923fc1fe8fe9ce7a561d9110e52e2", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "whitequark", "email": "<EMAIL>"}, "maintainers": [{"name": "whitequark", "email": "<EMAIL>"}], "dist": {"shasum": "ef715deab1e923fc1fe8fe9ce7a561d9110e52e2", "tarball": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.0.4.tgz", "integrity": "sha512-ts/Pq1YLjuZwvCqxbxE5e1oj05zeltzFenBYEQLiMqswkDahCIlyPkvWP9gxiPrF52EoFAG+kpL8Cqoej2Z5TQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC8JjlORckdEK1AY2JRhgxjwDDCgtp8v41OPZdFW/vEEAiBWRu9+Dpt1jG1/sOsi1zr+boDPG2GMsBivdBRjzLLc6A=="}]}}, "1.0.5": {"name": "ipaddr.js", "description": "A library for manipulating IPv4 and IPv6 addresses in JavaScript.", "version": "1.0.5", "author": {"name": "whitequark", "email": "<EMAIL>"}, "directories": {"lib": "./lib"}, "dependencies": {}, "devDependencies": {"coffee-script": "~1.6", "nodeunit": ">=0.8.2 <0.8.7", "uglify-js": "latest"}, "scripts": {"test": "cake build test"}, "keywords": ["ip", "ipv4", "ipv6"], "repository": {"type": "git", "url": "git://github.com/whitequark/ipaddr.js"}, "main": "./lib/ipaddr", "engines": {"node": ">= 0.10"}, "license": "MIT", "gitHead": "46438c8bfa187505b7007a277f09a4a9e73d5686", "bugs": {"url": "https://github.com/whitequark/ipaddr.js/issues"}, "_id": "ipaddr.js@1.0.5", "_shasum": "5fa78cf301b825c78abc3042d812723049ea23c7", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "whitequark", "email": "<EMAIL>"}, "maintainers": [{"name": "whitequark", "email": "<EMAIL>"}], "dist": {"shasum": "5fa78cf301b825c78abc3042d812723049ea23c7", "tarball": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.0.5.tgz", "integrity": "sha512-wBj+q+3uP78gMowwWgFLAYm/q4x5goyZmDsmuvyz+nd1u0D/ghgXXtc1OkgmTzSiWT101kiqGacwFk9eGQw6xQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDbaG70/dZka1ErJRp7oXOsvg7ieZyRZY3qIp4GxNAElAIhALfUEqUQ9gm+v3YcRXCg0/PnKycGtWIRDTyp0/Z2geTz"}]}}, "1.1.0": {"name": "ipaddr.js", "description": "A library for manipulating IPv4 and IPv6 addresses in JavaScript.", "version": "1.1.0", "author": {"name": "whitequark", "email": "<EMAIL>"}, "directories": {"lib": "./lib"}, "dependencies": {}, "devDependencies": {"coffee-script": "~1.6", "nodeunit": ">=0.8.2 <0.8.7", "uglify-js": "latest"}, "scripts": {"test": "cake build test"}, "keywords": ["ip", "ipv4", "ipv6"], "repository": {"type": "git", "url": "git://github.com/whitequark/ipaddr.js"}, "main": "./lib/ipaddr", "engines": {"node": ">= 0.10"}, "license": "MIT", "gitHead": "9afdebb46eaeac3bee4e98c6a5cd731b210cdee8", "bugs": {"url": "https://github.com/whitequark/ipaddr.js/issues"}, "_id": "ipaddr.js@1.1.0", "_shasum": "5fd380584eb3e2d55904dbe3047a2627d4199a14", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "whitequark", "email": "<EMAIL>"}, "maintainers": [{"name": "whitequark", "email": "<EMAIL>"}], "dist": {"shasum": "5fd380584eb3e2d55904dbe3047a2627d4199a14", "tarball": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.1.0.tgz", "integrity": "sha512-nRv7I+9+mxb3dX8rSY5p7I4DUqPMlosKJqtGhOoRaRhjLsKa3OT9Zf1G3LPnexHPKkFCaWAdJz2FuHHDmuTFpg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDD8YeMpfbzCNQ57hD5LOMJJMUlR0YcmXvVXERloArHwAIgfo7ZKbFiO6KRA3PmAAiRQm+3drWDGSzVHyaH5rCbtqM="}]}}, "1.1.1": {"name": "ipaddr.js", "description": "A library for manipulating IPv4 and IPv6 addresses in JavaScript.", "version": "1.1.1", "author": {"name": "whitequark", "email": "<EMAIL>"}, "directories": {"lib": "./lib"}, "dependencies": {}, "devDependencies": {"coffee-script": "~1.6", "nodeunit": ">=0.8.2 <0.8.7", "uglify-js": "latest"}, "scripts": {"test": "cake build test"}, "keywords": ["ip", "ipv4", "ipv6"], "repository": {"type": "git", "url": "git://github.com/whitequark/ipaddr.js"}, "main": "./lib/ipaddr", "engines": {"node": ">= 0.10"}, "license": "MIT", "gitHead": "dbc7d98bc0d8fff68a894be0c60721566807e2fc", "bugs": {"url": "https://github.com/whitequark/ipaddr.js/issues"}, "_id": "ipaddr.js@1.1.1", "_shasum": "c791d95f52b29c1247d5df80ada39b8a73647230", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "whitequark", "email": "<EMAIL>"}, "maintainers": [{"name": "whitequark", "email": "<EMAIL>"}], "dist": {"shasum": "c791d95f52b29c1247d5df80ada39b8a73647230", "tarball": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.1.1.tgz", "integrity": "sha512-pPr4Rqw2OL4yRfDPljsKTmbOv3GGgSl+ZCnYawcugPh06IFteMV1zU/Zry16qy8bhLMmrlDQh1NcOgtZWwh7gQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICSWy1l6FxR2SI8Kf+/tv23jZwIEBrx7JybHwkSgtjIGAiEA7JPuQk2eYVa/jS4FVY+AAy+tBn+lz/gPP4AOhyuAUwE="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/ipaddr.js-1.1.1.tgz_1464074293475_0.6683731523808092"}}, "1.2.0": {"name": "ipaddr.js", "description": "A library for manipulating IPv4 and IPv6 addresses in JavaScript.", "version": "1.2.0", "author": {"name": "whitequark", "email": "<EMAIL>"}, "directories": {"lib": "./lib"}, "dependencies": {}, "devDependencies": {"coffee-script": "~1.6", "nodeunit": ">=0.8.2 <0.8.7", "uglify-js": "latest"}, "scripts": {"test": "cake build test"}, "keywords": ["ip", "ipv4", "ipv6"], "repository": {"type": "git", "url": "git://github.com/whitequark/ipaddr.js"}, "main": "./lib/ipaddr", "engines": {"node": ">= 0.10"}, "license": "MIT", "gitHead": "87bcb487f1a6739101231e71b111da2823540398", "bugs": {"url": "https://github.com/whitequark/ipaddr.js/issues"}, "_id": "ipaddr.js@1.2.0", "_shasum": "8aba49c9192799585bdd643e0ccb50e8ae777ba4", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "whitequark", "email": "<EMAIL>"}, "maintainers": [{"name": "whitequark", "email": "<EMAIL>"}], "dist": {"shasum": "8aba49c9192799585bdd643e0ccb50e8ae777ba4", "tarball": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.2.0.tgz", "integrity": "sha512-ku//LD7ie/m9UVGCm9KweBIIHP4mB0maNGvav6Hz778fQCNLQF7iZ+H/UuHuqmjJCHCpA5hw8hOeRKxZl8IlXw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBKoHWUxCaFqhyzxUWvK+i3j8+F6Q1EOdRSi/wwSZjtZAiEAw6OC2PX+I6V5wMbzwoApkwFjGCvN4eWpo/X3ezTEe8Q="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/ipaddr.js-1.2.0.tgz_1467971539814_0.6815996605437249"}}, "1.3.0": {"name": "ipaddr.js", "description": "A library for manipulating IPv4 and IPv6 addresses in JavaScript.", "version": "1.3.0", "author": {"name": "whitequark", "email": "<EMAIL>"}, "directories": {"lib": "./lib"}, "dependencies": {}, "devDependencies": {"coffee-script": "~1.6", "nodeunit": ">=0.8.2 <0.8.7", "uglify-js": "latest"}, "scripts": {"test": "cake build test"}, "keywords": ["ip", "ipv4", "ipv6"], "repository": {"type": "git", "url": "git://github.com/whitequark/ipaddr.js.git"}, "main": "./lib/ipaddr", "engines": {"node": ">= 0.10"}, "license": "MIT", "gitHead": "9c557556e495a2c60a3c656e4f9f8b3a1e14dedc", "bugs": {"url": "https://github.com/whitequark/ipaddr.js/issues"}, "homepage": "https://github.com/whitequark/ipaddr.js#readme", "_id": "ipaddr.js@1.3.0", "_shasum": "1e03a52fdad83a8bbb2b25cbf4998b4cffcd3dec", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "whitequark", "email": "<EMAIL>"}, "maintainers": [{"name": "whitequark", "email": "<EMAIL>"}], "dist": {"shasum": "1e03a52fdad83a8bbb2b25cbf4998b4cffcd3dec", "tarball": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.3.0.tgz", "integrity": "sha512-d9mR/MvadaWPcqIpjtiKLgmNlO3587a9a+ZRUcwD4zguIV+zh+OX9U5/+ce5Ouw4DA54f+EMzmwCJDtlYoaR8Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCyp9UWwyzLkDFdoA7QliSAJYpKjsv17g80CYkjCeZjggIhAKLfYGZC0PVxd7HFRxBQ4yKHIXafZMNV+wejui0TMiKh"}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/ipaddr.js-1.3.0.tgz_1489544932893_0.961968986550346"}}, "1.4.0": {"name": "ipaddr.js", "description": "A library for manipulating IPv4 and IPv6 addresses in JavaScript.", "version": "1.4.0", "author": {"name": "whitequark", "email": "<EMAIL>"}, "directories": {"lib": "./lib"}, "dependencies": {}, "devDependencies": {"coffee-script": "~1.12.6", "uglify-js": "~3.0.19", "nodeunit": ">=0.8.2 <0.8.7"}, "scripts": {"test": "cake build test"}, "keywords": ["ip", "ipv4", "ipv6"], "repository": {"type": "git", "url": "git://github.com/whitequark/ipaddr.js.git"}, "main": "./lib/ipaddr", "engines": {"node": ">= 0.10"}, "license": "MIT", "gitHead": "e0f2a074f47c51941cbfd26cf38a327f847e6286", "bugs": {"url": "https://github.com/whitequark/ipaddr.js/issues"}, "homepage": "https://github.com/whitequark/ipaddr.js#readme", "_id": "ipaddr.js@1.4.0", "_shasum": "296aca878a821816e5b85d0a285a99bcff4582f0", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "whitequark", "email": "<EMAIL>"}, "maintainers": [{"name": "whitequark", "email": "<EMAIL>"}], "dist": {"shasum": "296aca878a821816e5b85d0a285a99bcff4582f0", "tarball": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.4.0.tgz", "integrity": "sha512-RbrsPoo4IkisyHhS9VDa3ybxnu0wOo0uTAhaELmwxq244p18X7Dk0fQoJvh/QTkIUO296fbjgvMqK3ry84eVVA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFpPJ+Pbu27EOMBIFXaJvIZP/qKnQXoGnPPVP1BKAU3gAiEA23MM3V/Pi/+NnEwa/Nkon91n1F7SBoKuLkELHImk3t4="}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ipaddr.js-1.4.0.tgz_1498164481108_0.6096577865537256"}}, "1.5.0": {"name": "ipaddr.js", "description": "A library for manipulating IPv4 and IPv6 addresses in JavaScript.", "version": "1.5.0", "author": {"name": "whitequark", "email": "<EMAIL>"}, "directories": {"lib": "./lib"}, "dependencies": {}, "devDependencies": {"coffee-script": "~1.12.6", "uglify-js": "~3.0.19", "nodeunit": ">=0.8.2 <0.8.7"}, "scripts": {"test": "cake build test"}, "keywords": ["ip", "ipv4", "ipv6"], "repository": {"type": "git", "url": "git://github.com/whitequark/ipaddr.js.git"}, "main": "./lib/ipaddr", "engines": {"node": ">= 0.10"}, "license": "MIT", "gitHead": "a54ecebfee155b54a8f9c1296ea5b6110cdc0d9b", "bugs": {"url": "https://github.com/whitequark/ipaddr.js/issues"}, "homepage": "https://github.com/whitequark/ipaddr.js#readme", "_id": "ipaddr.js@1.5.0", "_shasum": "48a04be500e66e6922a9e2aa3f54926eaeb96473", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "whitequark", "email": "<EMAIL>"}, "maintainers": [{"name": "whitequark", "email": "<EMAIL>"}], "dist": {"shasum": "48a04be500e66e6922a9e2aa3f54926eaeb96473", "tarball": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.5.0.tgz", "integrity": "sha512-7tMJAxffR7I9I+KIMdZGsb9RqUTpvfGqrsWrIMTBBI4REA15/aK4BRMb319v2iIkz2s72DkiD+mw/yK1bcTmkg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDboTYeLJsTPWXU6Jn+ISFCbr10lTMBD5e8IMztvj/y4AIhAJBvQxo+xhUoVuEMfUxuJ9+SOgZRNdqIUn4Jr9lvB8lS"}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ipaddr.js-1.5.0.tgz_1503324740920_0.6814808119088411"}}, "1.5.1": {"name": "ipaddr.js", "description": "A library for manipulating IPv4 and IPv6 addresses in JavaScript.", "version": "1.5.1", "author": {"name": "whitequark", "email": "<EMAIL>"}, "directories": {"lib": "./lib"}, "dependencies": {}, "devDependencies": {"coffee-script": "~1.12.6", "uglify-js": "~3.0.19", "nodeunit": ">=0.8.2 <0.8.7"}, "scripts": {"test": "cake build test"}, "keywords": ["ip", "ipv4", "ipv6"], "repository": {"type": "git", "url": "git://github.com/whitequark/ipaddr.js.git"}, "main": "./lib/ipaddr", "engines": {"node": ">= 0.10"}, "license": "MIT", "gitHead": "d677623f75bd4c23361ed661d6f8977f572ec18b", "bugs": {"url": "https://github.com/whitequark/ipaddr.js/issues"}, "homepage": "https://github.com/whitequark/ipaddr.js#readme", "_id": "ipaddr.js@1.5.1", "_shasum": "ddafccea41e6f23f7f3ffe65e22d0d8cff6835b3", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "whitequark", "email": "<EMAIL>"}, "maintainers": [{"name": "whitequark", "email": "<EMAIL>"}], "dist": {"shasum": "ddafccea41e6f23f7f3ffe65e22d0d8cff6835b3", "tarball": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.5.1.tgz", "integrity": "sha512-P1lcZq7akihcczwg2EXv5E1DlBXKMp5YJgM4ZPqD8qDHXwQhku2j9uvBuOFrneQaLD7Hfc/XelDM5oBP4W6/KQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCLDfxo71j8WPEtChD53GTfz+9GcvtlUEBevaanlo3crwIgNq/upb4frlQt2BJwl6r4Z6BQNH42YX7fwm52JR4B1Y8="}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ipaddr.js-1.5.1.tgz_1503434118374_0.09781592315994203"}}, "1.5.2": {"name": "ipaddr.js", "description": "A library for manipulating IPv4 and IPv6 addresses in JavaScript.", "version": "1.5.2", "author": {"name": "whitequark", "email": "<EMAIL>"}, "directories": {"lib": "./lib"}, "dependencies": {}, "devDependencies": {"coffee-script": "~1.12.6", "uglify-js": "~3.0.19", "nodeunit": ">=0.8.2 <0.8.7"}, "scripts": {"test": "cake build test"}, "keywords": ["ip", "ipv4", "ipv6"], "repository": {"type": "git", "url": "git://github.com/whitequark/ipaddr.js.git"}, "main": "./lib/ipaddr", "engines": {"node": ">= 0.10"}, "license": "MIT", "gitHead": "8f6e21058792cf6e38c6f461219fb25f0caecf27", "bugs": {"url": "https://github.com/whitequark/ipaddr.js/issues"}, "homepage": "https://github.com/whitequark/ipaddr.js#readme", "_id": "ipaddr.js@1.5.2", "_shasum": "d4b505bde9946987ccf0fc58d9010ff9607e3fa0", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "whitequark", "email": "<EMAIL>"}, "maintainers": [{"name": "whitequark", "email": "<EMAIL>"}], "dist": {"shasum": "d4b505bde9946987ccf0fc58d9010ff9607e3fa0", "tarball": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.5.2.tgz", "integrity": "sha512-zQfLEM7PuWWRDsjXFX7U6ysXZwxO2pp6xqonZgU+TH/aHEZ/1AGN18qv+1vURrblvm/5qdqzXITjWX8W6Evq8g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDqS3CLos7NI+kNfKPq9gZLRDR8oq0ZA66CJmoTp5lRPAiEA+KeC600oXWh2gqn/D6GbFdIaWo9HJO/0gtRcHc7V9Tc="}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ipaddr.js-1.5.2.tgz_1503546462209_0.10381372715346515"}}, "1.5.3": {"name": "ipaddr.js", "description": "A library for manipulating IPv4 and IPv6 addresses in JavaScript.", "version": "1.5.3", "author": {"name": "whitequark", "email": "<EMAIL>"}, "directories": {"lib": "./lib"}, "dependencies": {}, "devDependencies": {"coffee-script": "~1.12.6", "uglify-js": "~3.0.19", "nodeunit": ">=0.8.2 <0.8.7"}, "scripts": {"test": "cake build test"}, "keywords": ["ip", "ipv4", "ipv6"], "repository": {"type": "git", "url": "git://github.com/whitequark/ipaddr.js.git"}, "main": "./lib/ipaddr.js", "engines": {"node": ">= 0.10"}, "license": "MIT", "gitHead": "4be129b1b6e423fbfd236336003582d8a3cfac7d", "bugs": {"url": "https://github.com/whitequark/ipaddr.js/issues"}, "homepage": "https://github.com/whitequark/ipaddr.js#readme", "_id": "ipaddr.js@1.5.3", "_shasum": "ec68539dd70ff0990c3195ab7ec43124a83df589", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "whitequark", "email": "<EMAIL>"}, "maintainers": [{"name": "whitequark", "email": "<EMAIL>"}], "dist": {"shasum": "ec68539dd70ff0990c3195ab7ec43124a83df589", "tarball": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.5.3.tgz", "integrity": "sha512-idPstWCjYHIlHxadbLjZT/EZKe0WkbxUBFnQRi+zQt7yMugMKSfmp8DGIMbS4y1OxbIQv2iXZkeGOAwZ+S6/LA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEuJLVDlj0sNIC2FYKJN16LS96h67zctced7cJ7GQZFcAiByNujFuIHw2q0DNVv5QBVPlm3O8e9n5Xo8JbMSerFF0g=="}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ipaddr.js-1.5.3.tgz_1507823089269_0.021649141097441316"}}, "1.5.4": {"name": "ipaddr.js", "description": "A library for manipulating IPv4 and IPv6 addresses in JavaScript.", "version": "1.5.4", "author": {"name": "whitequark", "email": "<EMAIL>"}, "directories": {"lib": "./lib"}, "dependencies": {}, "devDependencies": {"coffee-script": "~1.12.6", "uglify-js": "~3.0.19", "nodeunit": ">=0.8.2 <0.8.7"}, "scripts": {"test": "cake build test"}, "keywords": ["ip", "ipv4", "ipv6"], "repository": {"type": "git", "url": "git://github.com/whitequark/ipaddr.js.git"}, "main": "./lib/ipaddr.js", "engines": {"node": ">= 0.10"}, "license": "MIT", "gitHead": "d384881bd33cefda9c56eda74bc122350a31bd6a", "bugs": {"url": "https://github.com/whitequark/ipaddr.js/issues"}, "homepage": "https://github.com/whitequark/ipaddr.js#readme", "_id": "ipaddr.js@1.5.4", "_shasum": "962263d9d26132956fc5c630b638a30d3cdffc14", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "whitequark", "email": "<EMAIL>"}, "maintainers": [{"name": "whitequark", "email": "<EMAIL>"}], "dist": {"shasum": "962263d9d26132956fc5c630b638a30d3cdffc14", "tarball": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.5.4.tgz", "integrity": "sha512-1hF5vZCxAB8uJ298CRm953N10KJ4fz2t+jV+cXjXvOml7a+AODOrhflFj2jqzaO4VuPnbqwYO5mOJPn0497eiw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDbAZY8BaBfzt5cSf3z72HJcFi5FWkOgp18DDrRpNK5VwIgUjUthufltx8nVgWlN2zBGu9dn3drzyJwrifdZFa7OpA="}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ipaddr.js-1.5.4.tgz_1508225226077_0.20283467858098447"}}, "1.6.0": {"name": "ipaddr.js", "description": "A library for manipulating IPv4 and IPv6 addresses in JavaScript.", "version": "1.6.0", "author": {"name": "whitequark", "email": "<EMAIL>"}, "directories": {"lib": "./lib"}, "dependencies": {}, "devDependencies": {"coffee-script": "~1.12.6", "uglify-js": "~3.0.19", "nodeunit": ">=0.8.2 <0.8.7"}, "scripts": {"test": "cake build test"}, "keywords": ["ip", "ipv4", "ipv6"], "repository": {"type": "git", "url": "git://github.com/whitequark/ipaddr.js.git"}, "main": "./lib/ipaddr.js", "engines": {"node": ">= 0.10"}, "license": "MIT", "types": "./lib/ipaddr.js.d.ts", "gitHead": "c81198c2540a1df41571623c0bcbdb9d9b73a925", "bugs": {"url": "https://github.com/whitequark/ipaddr.js/issues"}, "homepage": "https://github.com/whitequark/ipaddr.js#readme", "_id": "ipaddr.js@1.6.0", "_shasum": "e3fa357b773da619f26e95f049d055c72796f86b", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "whitequark", "email": "<EMAIL>"}, "maintainers": [{"name": "whitequark", "email": "<EMAIL>"}], "dist": {"shasum": "e3fa357b773da619f26e95f049d055c72796f86b", "tarball": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.6.0.tgz", "integrity": "sha512-8U/ixyn0MtWpX5aLMwbiIUQ8nJk/gbt5qCBfkteJ51jE2xjHjjkfE5jOqRpCVzORydZfAO98/MIhUJ1Onb50ng==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHTBztXIWObFXZK+QctCSREa2aRjoLYxU2Q1YCm3VuZ9AiBJhWtLKiWTOypEIinaskaThCRxXiZBwrQWB8BjtZGGcQ=="}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ipaddr.js-1.6.0.tgz_1517895655914_0.4097069769632071"}}, "1.7.0": {"name": "ipaddr.js", "description": "A library for manipulating IPv4 and IPv6 addresses in JavaScript.", "version": "1.7.0", "author": {"name": "whitequark", "email": "<EMAIL>"}, "directories": {"lib": "./lib"}, "dependencies": {}, "devDependencies": {"coffee-script": "~1.12.6", "uglify-js": "~3.0.19", "nodeunit": ">=0.8.2 <0.8.7"}, "scripts": {"test": "cake build test"}, "files": ["lib/", "ipaddr.min.js"], "keywords": ["ip", "ipv4", "ipv6"], "repository": {"type": "git", "url": "git://github.com/whitequark/ipaddr.js.git"}, "main": "./lib/ipaddr.js", "engines": {"node": ">= 0.10"}, "license": "MIT", "types": "./lib/ipaddr.js.d.ts", "gitHead": "c645e0b0ac8646e2bc3f086399bb5c675d504dd8", "bugs": {"url": "https://github.com/whitequark/ipaddr.js/issues"}, "homepage": "https://github.com/whitequark/ipaddr.js#readme", "_id": "ipaddr.js@1.7.0", "_shasum": "2206ed334afc32e01fed3ee838b6b2521068b9d2", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "whitequark", "email": "<EMAIL>"}, "maintainers": [{"name": "whitequark", "email": "<EMAIL>"}], "dist": {"shasum": "2206ed334afc32e01fed3ee838b6b2521068b9d2", "tarball": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.7.0.tgz", "fileCount": 5, "unpackedSize": 38772, "integrity": "sha512-bqxLrNgcr+u4flOX7vbmJyllc75m0o8Xi1OlfO4NHJ7l7ehOcfjJOjPH+r6YZJKAoy7+rEaZIF8hX+8yzAO6Sw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG+a3BkDRkEqC6Bw28WDT1XjwHFdnH8GmBpvgGAvuhWnAiEArGkafhCeXryhde8aqq9GPQ0Lgc2E88gRN6mc62QO6oU="}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ipaddr.js_1.7.0_1523355133770_0.22257876250113773"}, "_hasShrinkwrap": false}, "1.8.0": {"name": "ipaddr.js", "description": "A library for manipulating IPv4 and IPv6 addresses in JavaScript.", "version": "1.8.0", "author": {"name": "whitequark", "email": "<EMAIL>"}, "directories": {"lib": "./lib"}, "dependencies": {}, "devDependencies": {"coffee-script": "~1.12.6", "uglify-js": "~3.0.19", "nodeunit": ">=0.8.2 <0.8.7"}, "scripts": {"test": "cake build test"}, "files": ["lib/", "ipaddr.min.js"], "keywords": ["ip", "ipv4", "ipv6"], "repository": {"type": "git", "url": "git://github.com/whitequark/ipaddr.js.git"}, "main": "./lib/ipaddr.js", "engines": {"node": ">= 0.10"}, "license": "MIT", "types": "./lib/ipaddr.js.d.ts", "gitHead": "d7f0a9bfea2888ea8b309b97d0eed1709e8c6ead", "bugs": {"url": "https://github.com/whitequark/ipaddr.js/issues"}, "homepage": "https://github.com/whitequark/ipaddr.js#readme", "_id": "ipaddr.js@1.8.0", "_shasum": "eaa33d6ddd7ace8f7f6fe0c9ca0440e706738b1e", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "whitequark", "email": "<EMAIL>"}, "maintainers": [{"name": "whitequark", "email": "<EMAIL>"}], "dist": {"shasum": "eaa33d6ddd7ace8f7f6fe0c9ca0440e706738b1e", "tarball": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.8.0.tgz", "fileCount": 5, "unpackedSize": 39303, "integrity": "sha512-cwzBM2oCXBbr39KDfIi8paXKQO1eQ2UwsglI0txWsoBuygtUORsl8K8FooEEoHCWOJ8JghV3YoCHkQysXg5zjA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGA0tsI/sSkGSuE2u7Is+fIONgzh6kuObSRcHBv5XR5VAiEA1iZhnMWIR2qnXdFQUDU9SRnlEOB+tfVvQRhq72skWh0="}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ipaddr.js_1.8.0_1531193295710_0.6229373074337963"}, "_hasShrinkwrap": false}, "1.8.1": {"name": "ipaddr.js", "description": "A library for manipulating IPv4 and IPv6 addresses in JavaScript.", "version": "1.8.1", "author": {"name": "whitequark", "email": "<EMAIL>"}, "directories": {"lib": "./lib"}, "dependencies": {}, "devDependencies": {"coffee-script": "~1.12.6", "uglify-js": "~3.0.19", "nodeunit": ">=0.8.2 <0.8.7"}, "scripts": {"test": "cake build test"}, "files": ["lib/", "ipaddr.min.js"], "keywords": ["ip", "ipv4", "ipv6"], "repository": {"type": "git", "url": "git://github.com/whitequark/ipaddr.js.git"}, "main": "./lib/ipaddr.js", "engines": {"node": ">= 0.10"}, "license": "MIT", "types": "./lib/ipaddr.js.d.ts", "gitHead": "0f676ef505ae314f62925c1de3640e6d45b2e8c4", "bugs": {"url": "https://github.com/whitequark/ipaddr.js/issues"}, "homepage": "https://github.com/whitequark/ipaddr.js#readme", "_id": "ipaddr.js@1.8.1", "_shasum": "fa4b79fa47fd3def5e3b159825161c0a519c9427", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "whitequark", "email": "<EMAIL>"}, "maintainers": [{"name": "whitequark", "email": "<EMAIL>"}], "dist": {"shasum": "fa4b79fa47fd3def5e3b159825161c0a519c9427", "tarball": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.8.1.tgz", "fileCount": 5, "unpackedSize": 39327, "integrity": "sha512-Genkx0nA8gkH5lAT2j53s5Q0u2S8+Ks9XuXn6uk4aoDiVaU1VnNJQ6OOQYkOZpNgV+EtCjzVakSDnJOXrlPU4A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFFbPqFm5jJV2YKJDZ1qFrhNs/oeXaGoEPCrp/bE607NAiBegLwC9SseL2sNTO3lYtaealhiiMRjdjmmrA3764Pmbw=="}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ipaddr.js_1.8.1_1533051081044_0.9626124293508547"}, "_hasShrinkwrap": false}, "1.9.0": {"name": "ipaddr.js", "description": "A library for manipulating IPv4 and IPv6 addresses in JavaScript.", "version": "1.9.0", "author": {"name": "whitequark", "email": "<EMAIL>"}, "directories": {"lib": "./lib"}, "dependencies": {}, "devDependencies": {"coffee-script": "~1.12.6", "uglify-js": "~3.0.19", "nodeunit": ">=0.8.2 <0.8.7"}, "scripts": {"test": "cake build test"}, "keywords": ["ip", "ipv4", "ipv6"], "repository": {"type": "git", "url": "git://github.com/whitequark/ipaddr.js.git"}, "main": "./lib/ipaddr.js", "engines": {"node": ">= 0.10"}, "license": "MIT", "types": "./lib/ipaddr.js.d.ts", "gitHead": "8bd045f49f5b7695eea2c93e9706f4350e0bbaba", "bugs": {"url": "https://github.com/whitequark/ipaddr.js/issues"}, "homepage": "https://github.com/whitequark/ipaddr.js#readme", "_id": "ipaddr.js@1.9.0", "_npmVersion": "5.8.0", "_nodeVersion": "10.15.0", "_npmUser": {"name": "whitequark", "email": "<EMAIL>"}, "maintainers": [{"name": "whitequark", "email": "<EMAIL>"}], "dist": {"integrity": "sha512-M4Sjn6N/+O6/IXSJseKqHoFc+5FdGJ22sXqnjTpdZweHK64MzEPAyQZyEU3R/KRv2GLoa7nNtg/C2Ev6m7z+eA==", "shasum": "37df74e430a0e47550fe54a2defe30d8acd95f65", "tarball": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.9.0.tgz", "fileCount": 6, "unpackedSize": 42064, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcX9f6CRA9TVsSAnZWagAACN0P/3QaY26R9gIEKJMFRl3f\nComsl89/ZqAisw026wGodTWmYirIR5IbjDNxWETrCHXdriBCrSsDZ1SzzdGk\nTxkjv5/bQjv4CpPZV5/d91q6vkpFjPbVOk/N+K0fOJkmO9Fp4lwUQq4/B0gi\nEj+lPr1a5j5ZQwpMNB1pZrtoy3Fo/y0d5sYTNEQAlAQfjcJI4mFtc92tkc9s\nnzanygidCfoWEctnJIy9/lUjCxY2Irt8tMBa7cVExOLOqn6KegnJOtC+4g19\nCSjmv+lUj/r9QdT1kTbnej+118OQDjaZmjJ18/mxKdVXsYq6RLxKalWiez6E\nWKzCrMBSfwjDcErkaWr8tLn8b5zwwS5oLYG2pbzwDxA92P81LYeu+3wcmxa1\nPpvKkxBHLkUK1+19Xg0t5IP//B/C6ZCS5q+zckr+97U7SitK7aUajtRc3SYu\notiV+5t1Lgl+WLiymOq3WnBXMJwzXsq7nYBJgUlkoj0IE4Du5DFKao0UNvE2\nSBZTM8MJGm2rsm9xGKqWx9rBqWDudpnGipJIOV6l807BRn0yBrQjbx2jhV48\nK8UrwyOwYE1dwEYzhuviczDvE5o6qriTiFyYfA4DW0w1h3X3VDGWoI81g4dh\nGzP3V8SjsKV8g80HkrU8rboh1UKUIMjhINunU6PBD6YR+2fUqos3YlMxbprM\nOThn\r\n=Tk1a\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICgnqXelHCQg8GAbYLeF6dM3h40+drk6u0cBsZWHQh4aAiEAsPpJEFr8mmZpRsis2YzZqiityZ4ZQISmUpwqFMiKsD8="}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ipaddr.js_1.9.0_1549785081591_0.5220196467104794"}, "_hasShrinkwrap": false}, "1.9.1": {"name": "ipaddr.js", "description": "A library for manipulating IPv4 and IPv6 addresses in JavaScript.", "version": "1.9.1", "author": {"name": "whitequark", "email": "<EMAIL>"}, "directories": {"lib": "./lib"}, "dependencies": {}, "devDependencies": {"coffee-script": "~1.12.6", "nodeunit": "^0.11.3", "uglify-js": "~3.0.19"}, "scripts": {"test": "cake build test"}, "keywords": ["ip", "ipv4", "ipv6"], "repository": {"type": "git", "url": "git://github.com/whitequark/ipaddr.js.git"}, "main": "./lib/ipaddr.js", "engines": {"node": ">= 0.10"}, "license": "MIT", "types": "./lib/ipaddr.js.d.ts", "gitHead": "760416b27a4b348e7a39fdacfccd6cb047026c98", "bugs": {"url": "https://github.com/whitequark/ipaddr.js/issues"}, "homepage": "https://github.com/whitequark/ipaddr.js#readme", "_id": "ipaddr.js@1.9.1", "_npmVersion": "5.8.0", "_nodeVersion": "10.15.2", "_npmUser": {"name": "whitequark", "email": "<EMAIL>"}, "maintainers": [{"name": "whitequark", "email": "<EMAIL>"}], "dist": {"integrity": "sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g==", "shasum": "bff38543eeb8984825079ff3a2a8e6cbd46781b3", "tarball": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.9.1.tgz", "fileCount": 6, "unpackedSize": 42145, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdLofECRA9TVsSAnZWagAAuqsQAIOiqKtRllNg8pMg89ea\nLFtGW49UBn0+nYUg9CSQM/h8nOYi+NH6Puw5dpDasTWt2tRmPtzqx/1vxaFk\nPmg73w49mtBPAxu2YLngv8tSx4dNvsClTe5RpLvpZFmopIRQojKUl0VSj3RW\nMtbkj/KozwgiYzguvHwGhjIhjerutQ1zoH0kJCZCRvVeLJrkdyj/PPzvLyF2\n3MZ7peqpGXg4k92ljrN7Q49itLIdYtrcm3tv2659e2W80E63RJUw0Buw9IGl\nYGX/1EfpBKW2l3QADjgW9Lz7/3oWt7NEDWNGGw+kMsivfAFAIKDiZ93v5H1M\ntaH+cLv3rHJ1s3cgz8gdlp3VGunG/OfP179e9J19AvcsLKhJ/Kho+fz0OD7d\nAEg+wMFlRLP5QR+x9X4naIN44VgjMZkTWZq8blx5hvJVCqSPtnYuORfYJstu\npQx9lSmmZl2Ma2LRjtpXUnbgtUOXIYpvPhn2+OV7NN/RbrigOPFHEHYw9YDQ\ndvChMV7RSmvRvDS+9YDqB+71/pcJljeztfGkznErW6xqc1DtdKg6WCw6k58H\nUTGjiMj+vytXJ3g9YnN5OlTmRAZCDj1XaHWPpGybfnm5j8MPetary6Dobian\nSbAaS1eqlMEhPd1G5t1RsLOy1ePI80HsARHZDQhqUY2Kl0/vIgtnWsx3KPHc\nUDnk\r\n=j6kw\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC2FrMYz92YWCl72GTm5CZtY3CiaIuNG2WbvKC5Wn5DbQIgDW4nM80bQTTfY8VDPpIw/9bPP+udmKuWghCRTMut4Cw="}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ipaddr.js_1.9.1_1563330499468_0.2217102903600776"}, "_hasShrinkwrap": false}, "2.0.0": {"name": "ipaddr.js", "description": "A library for manipulating IPv4 and IPv6 addresses in JavaScript.", "version": "2.0.0", "author": {"name": "whitequark", "email": "<EMAIL>"}, "directories": {"lib": "./lib"}, "dependencies": {}, "devDependencies": {"eslint": "*", "mocha": "*", "uglify-es": "*"}, "scripts": {"lint": "npx eslint lib test", "lintfix": "npx eslint --fix lib test", "build": "npx uglifyjs --compress --mangle --wrap=window -o ipaddr.min.js lib/ipaddr.js", "test": "npx _mocha"}, "keywords": ["ip", "ipv4", "ipv6"], "repository": {"type": "git", "url": "git://github.com/whitequark/ipaddr.js.git"}, "main": "./lib/ipaddr.js", "engines": {"node": ">= 10"}, "license": "MIT", "types": "./lib/ipaddr.js.d.ts", "gitHead": "88f9dde64ec4e2314063446492416fd6206f6e4d", "bugs": {"url": "https://github.com/whitequark/ipaddr.js/issues"}, "homepage": "https://github.com/whitequark/ipaddr.js#readme", "_id": "ipaddr.js@2.0.0", "_npmVersion": "5.8.0", "_nodeVersion": "10.21.0", "_npmUser": {"name": "whitequark", "email": "<EMAIL>"}, "maintainers": [{"name": "whitequark", "email": "<EMAIL>"}], "dist": {"integrity": "sha512-S54H9mIj0rbxRIyrDMEuuER86LdlgUg9FSeZ8duQb6CUG2iRrA36MYVQBSprTF/ZeAwvyQ5mDGuNvIPM0BIl3w==", "shasum": "77ccccc8063ae71ab65c55f21b090698e763fc6e", "tarball": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-2.0.0.tgz", "fileCount": 6, "unpackedSize": 55486, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfOgyZCRA9TVsSAnZWagAAxOgP/22JhlisEc6AQ9F4Gytx\n4Sfp2tThv2/zBGNqSQKBnLjBetQerCSCYPT2vli7zgwBs6hcSpLwteQI0VNY\ndTOaChwT6HMATCb4btBfuuZyt+9QLJzdxHwiNR+NtHkpnYJSgcZXNMLW5mMB\n5G6+tJGMit9mr232qEuwPOms2JdtmzWeZVzFu04FF6z6sMDbt/brcL4BhdAZ\n3S39W8dmWz3ETBFYb7gzuLwmAxSAqokpCwYpvZSQOPz9n0cLMuubSqxjxcbL\njukZemfirJv+R6eiQsbFxfkH9TAAswiIfSjIk6Anm1YmaD3FWriLlUhqpIvD\nIzJCXkA6HsEmshXZc44CvfU+HfCtOzZTkEpDcw7hyGZq8MczY81MzJsBXY0l\n+B5WsxVjzTHVzOm8F3RB3xaK+oBGoxmBEX6kCwnlsT6lRkAfLU+KHXB+aMcM\nuci9wwznxTKO/VTzI/TQ4PFDKLtNcFaah3/H5D7PgvvZD5L1yYx9mzwBrgQA\n7336LHkQs2r/KUdhXyhsjjJz0s4zMRiCsiqFbwzxp5OaMNPKEAg47UN0+Gc5\n0FMHP46qldHOP9/O3rs4q9qmF2OpNpbOSxXqE5gh/HAm1Vql3+EyEsguq5Og\nxJyGy5twGCTugwubqc82CYZ5446/zDxiadnx4QaxVb0hKEydGT15k9soxrfh\nlMSW\r\n=xaXW\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBfbjejKXoGXbQIp3j63+RuSsLvJcPypZkxG2nBuVio1AiA+eTKpsxfwhnE5ABAuA3/KzNf+APBNnr1kL3GxdOqc5Q=="}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ipaddr.js_2.0.0_1597639833538_0.966759294124411"}, "_hasShrinkwrap": false}, "2.0.1": {"name": "ipaddr.js", "description": "A library for manipulating IPv4 and IPv6 addresses in JavaScript.", "version": "2.0.1", "author": {"name": "whitequark", "email": "<EMAIL>"}, "directories": {"lib": "./lib"}, "dependencies": {}, "devDependencies": {"eslint": "*", "mocha": "*", "uglify-es": "*"}, "scripts": {"lint": "npx eslint lib test", "lintfix": "npx eslint --fix lib test", "build": "npx uglifyjs --compress --mangle --wrap=window -o ipaddr.min.js lib/ipaddr.js", "test": "npx _mocha"}, "keywords": ["ip", "ipv4", "ipv6"], "repository": {"type": "git", "url": "git://github.com/whitequark/ipaddr.js.git"}, "main": "./lib/ipaddr.js", "engines": {"node": ">= 10"}, "license": "MIT", "types": "./lib/ipaddr.js.d.ts", "gitHead": "5237e6d21d44ca1675adf881651a746cb3699d20", "bugs": {"url": "https://github.com/whitequark/ipaddr.js/issues"}, "homepage": "https://github.com/whitequark/ipaddr.js#readme", "_id": "ipaddr.js@2.0.1", "_nodeVersion": "10.24.0", "_npmVersion": "6.14.9", "_npmUser": {"name": "whitequark", "email": "<EMAIL>"}, "maintainers": [{"name": "whitequark", "email": "<EMAIL>"}], "dist": {"integrity": "sha512-1qTgH9NG+IIJ4yfKs2e6Pp1bZg8wbDbKHT21HrLIeYBTRLgMYKnMTPAuI3Lcs61nfx5h1xlXnbJtH1kX5/d/ng==", "shasum": "eca256a7a877e917aeb368b0a7497ddf42ef81c0", "tarball": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-2.0.1.tgz", "fileCount": 7, "unpackedSize": 59520, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwPA/CRA9TVsSAnZWagAA/LEP/1aejLiFQ5nH98pEvxUu\nLV7Y+lxzJAFiye/hiiZzcjirbwG+KFfb6rqMzEX5PK3PSMIx3b5a4GJ+DNv/\nvdjA8P+YDo7hvh3Gjoquskiv0nKzbyZSm13cobYDX3CiiONqJgLH+GFwAvo5\nbrj59PHiWbIP04Gjb/LL8+9FU4ON3kdWH9/4f0L8cn2LG/cA0NINadoPmAgh\nqLt9fL83Ct5sg3/I6DE4KeFp6iPyKstDiBSd8LNazaIvmDQl6Dv9KaO1PL6s\ndnMiz+rHJZaKyxovdfqOGZqTnV1WWUAZZQ0RiTHjKSfYahAZFTood85UkEcc\nUlTKtHa8IcMXbUvAd59Xg5W+JuqpdjC9JFitNciwa29bCrJe1YM6q2m0RLr5\nSc11UREt+vJbVrJAIgNAUWgYKI25Oc8LrudlVaD2fLdMALhB4XD6YLksvO71\nVA8+LUGvTUGKx4STe9RdbLN0ZI8QAtmfMqdI6GwOR5/r9Naoftc17YuzVF5P\nMElH3x4JOKwRAHRQOceX65FKv1NSLJW3B9qX1xVkEOjJit/hHG3gXfndY6qx\nr9SAxjwBfRZObFYz5ZPuB3yLaMXnqN6zUD4cLGMOyHxobq/Vyf0nx4L20e+z\nntlb+yKMbFWaQhrkIigmSaEo6qem8OqDPhjRVEpURohpLbyzZqCgbYVBs7kb\nNK+S\r\n=IIK1\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCQjS4Ya2D1lm/yXOPWqc0pQb7ObRjSW93JtZmw8fj3YgIgTiGiUN2EZAcE/cXzjnJyDO+MtXsn0oivjMD6aTwOslM="}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ipaddr.js_2.0.1_1623257151408_0.3856082373423191"}, "_hasShrinkwrap": false}, "2.1.0": {"name": "ipaddr.js", "description": "A library for manipulating IPv4 and IPv6 addresses in JavaScript.", "version": "2.1.0", "author": {"name": "whitequark", "email": "<EMAIL>"}, "directories": {"lib": "./lib"}, "dependencies": {}, "devDependencies": {"eslint": "*", "mocha": "*", "uglify-es": "*"}, "scripts": {"lint": "npx eslint lib test", "lintfix": "npx eslint --fix lib test", "build": "npx uglifyjs --compress --mangle --wrap=window -o ipaddr.min.js lib/ipaddr.js", "test": "npx _mocha"}, "keywords": ["ip", "ipv4", "ipv6"], "repository": {"type": "git", "url": "git://github.com/whitequark/ipaddr.js.git"}, "main": "./lib/ipaddr.js", "engines": {"node": ">= 10"}, "license": "MIT", "types": "./lib/ipaddr.js.d.ts", "gitHead": "fb169615d04bb5f1c6042a07d85f9b09bef69af6", "bugs": {"url": "https://github.com/whitequark/ipaddr.js/issues"}, "homepage": "https://github.com/whitequark/ipaddr.js#readme", "_id": "ipaddr.js@2.1.0", "_nodeVersion": "12.22.5", "_npmVersion": "6.14.9", "_npmUser": {"name": "whitequark", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-LlbxQ7xKzfBusov6UMi4MFpEg0m+mAm9xyNGEduwXMEDuf4WfzB/RZwMVYEd7IKGvh4IUkEXYxtAVu9T3OelJQ==", "shasum": "2119bc447ff8c257753b196fc5f1ce08a4cdf39f", "tarball": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-2.1.0.tgz", "fileCount": 7, "unpackedSize": 60229, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDALBzfj8MbPBqbsMnABE064F9MVSTCQK2etq2HFEYm2wIgNKlOYot+5kPNbWphwb2+zOB05dTe9BD3j+PdF0FByqY="}]}, "maintainers": [{"name": "whitequark", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ipaddr.js_2.1.0_1685466491765_0.7323887615719415"}, "_hasShrinkwrap": false}, "2.2.0": {"name": "ipaddr.js", "description": "A library for manipulating IPv4 and IPv6 addresses in JavaScript.", "version": "2.2.0", "author": {"name": "whitequark", "email": "<EMAIL>"}, "directories": {"lib": "./lib"}, "dependencies": {}, "devDependencies": {"eslint": "^8.57.0", "uglify-es": "*"}, "scripts": {"lint": "npx eslint lib", "lintfix": "npx eslint --fix lib test", "build": "npx uglifyjs --compress --mangle --wrap=window -o ipaddr.min.js lib/ipaddr.js", "test": "node --test"}, "keywords": ["ip", "ipv4", "ipv6"], "repository": {"type": "git", "url": "git://github.com/whitequark/ipaddr.js.git"}, "main": "./lib/ipaddr.js", "engines": {"node": ">= 10"}, "license": "MIT", "types": "./lib/ipaddr.js.d.ts", "gitHead": "0cba6a4e924b3286e85512f0949cdf7d6367a783", "bugs": {"url": "https://github.com/whitequark/ipaddr.js/issues"}, "homepage": "https://github.com/whitequark/ipaddr.js#readme", "_id": "ipaddr.js@2.2.0", "_nodeVersion": "18.19.1", "_npmVersion": "9.2.0", "dist": {"integrity": "sha512-Ag3wB2o37wslZS19hZqorUnrnzSkpOVy+IiiDEiTqNubEYpYuHWIf6K4psgN2ZWKExS4xhVCrRVfb/wfW8fWJA==", "shasum": "d33fa7bac284f4de7af949638c9d68157c6b92e8", "tarball": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-2.2.0.tgz", "fileCount": 6, "unpackedSize": 62306, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDPadaBDI1QzCU7zPTJWoM76iW7VDBbulbqlqz5pHq5pQIgISyiFz6w95oYXnjybPBxxaittgpefxBcDRFquugntx4="}]}, "_npmUser": {"name": "whitequark", "email": "<EMAIL>"}, "maintainers": [{"name": "whitequark", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ipaddr.js_2.2.0_1713577016695_0.32980495309177194"}, "_hasShrinkwrap": false}}, "maintainers": [{"name": "whitequark", "email": "<EMAIL>"}], "time": {"modified": "2024-04-20T01:36:57.028Z", "created": "2011-07-28T15:57:38.697Z", "0.1.0": "2011-07-28T15:57:40.643Z", "0.1.1": "2011-07-30T16:00:04.710Z", "0.1.2": "2013-12-06T22:00:21.923Z", "0.1.3": "2014-07-09T08:26:58.804Z", "0.1.4": "2014-11-18T22:00:30.596Z", "0.1.5": "2014-11-19T21:57:24.625Z", "0.1.6": "2014-11-26T16:34:44.619Z", "0.1.7": "2015-01-29T03:39:54.325Z", "0.1.8": "2015-01-29T23:40:08.892Z", "0.1.9": "2015-03-11T10:41:42.690Z", "1.0.0": "2015-04-07T21:16:34.839Z", "1.0.1": "2015-04-08T17:00:18.304Z", "1.0.3": "2015-08-27T06:31:09.446Z", "1.0.4": "2015-11-07T05:46:23.340Z", "1.0.5": "2015-12-09T14:36:42.259Z", "1.1.0": "2016-01-29T22:33:58.126Z", "1.1.1": "2016-05-24T07:18:13.893Z", "1.2.0": "2016-07-08T09:52:20.241Z", "1.3.0": "2017-03-15T02:28:53.138Z", "1.4.0": "2017-06-22T20:48:02.394Z", "1.5.0": "2017-08-21T14:12:21.041Z", "1.5.1": "2017-08-22T20:35:18.455Z", "1.5.2": "2017-08-24T03:47:42.297Z", "1.5.3": "2017-10-12T15:44:50.448Z", "1.5.4": "2017-10-17T07:27:07.211Z", "1.6.0": "2018-02-06T05:40:56.969Z", "1.7.0": "2018-04-10T10:12:13.872Z", "1.8.0": "2018-07-10T03:28:15.831Z", "1.8.1": "2018-07-31T15:31:21.138Z", "1.9.0": "2019-02-10T07:51:21.736Z", "1.9.1": "2019-07-17T02:28:19.618Z", "2.0.0": "2020-08-17T04:50:33.629Z", "2.0.1": "2021-06-09T16:45:51.569Z", "2.1.0": "2023-05-30T17:08:12.012Z", "2.2.0": "2024-04-20T01:36:56.836Z"}, "author": {"name": "whitequark", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git://github.com/whitequark/ipaddr.js.git"}, "readme": "# ipaddr.js — an IPv6 and IPv4 address manipulation library\n\n[![Build Status](https://github.com/whitequark/ipaddr.js/workflows/CI%20Tests/badge.svg)](https://github.com/whitequark/ipaddr.js/actions?query=workflow%3A%22CI+Tests%22)\n\nipaddr.js is a small (1.9K minified and gzipped) library for manipulating\nIP addresses in JavaScript environments. It runs on both CommonJS runtimes\n(e.g. [nodejs]) and in a web browser.\n\nipaddr.js allows you to verify and parse string representation of an IP\naddress, match it against a CIDR range or range list, determine if it falls\ninto some reserved ranges (examples include loopback and private ranges),\nand convert between IPv4 and IPv4-mapped IPv6 addresses.\n\n[nodejs]: http://nodejs.org\n\n## Installation\n\n`npm install ipaddr.js`\n\nor\n\n`bower install ipaddr.js`\n\n## Older Node support\n\nUse 2.x release for nodejs versions 10+.\nUse the 1.x release for versions of nodejs older than 10.\n\n## API\n\nipaddr.js defines one object in the global scope: `ipaddr`. In CommonJS,\nit is exported from the module:\n\n```js\nconst ipaddr = require('ipaddr.js');\n```\n\nThe API consists of several global methods and two classes: ipaddr.IPv6 and ipaddr.IPv4.\n\n### Global methods\n\nThere are four global methods defined: `ipaddr.isValid`, `ipaddr.isValidCIDR`, \n`ipaddr.parse`, and `ipaddr.process`. All of them receive a string as a single\nparameter.\n\nThe `ipaddr.isValid` method returns `true` if the address is a valid IPv4 or\nIPv6 address, and `false` otherwise. It does not throw any exceptions.\n\nThe `ipaddr.isValidCIDR` method returns `true` if the address is a valid IPv4 or\nIPv6 address in CIDR notation, and `false` otherwise. It does not throw any exceptions.\n\nThe `ipaddr.parse` method returns an object representing the IP address,\nor throws an `Error` if the passed string is not a valid representation of an\nIP address.\n\nThe `ipaddr.process` method works just like the `ipaddr.parse` one, but it\nautomatically converts IPv4-mapped IPv6 addresses to their IPv4 counterparts\nbefore returning. It is useful when you have a Node.js instance listening\non an IPv6 socket, and the `net.ivp6.bindv6only` sysctl parameter (or its\nequivalent on non-Linux OS) is set to 0. In this case, you can accept IPv4\nconnections on your IPv6-only socket, but the remote address will be mangled.\nUse `ipaddr.process` method to automatically demangle it.\n\n### Object representation\n\nParsing methods return an object which descends from `ipaddr.IPv6` or\n`ipaddr.IPv4`. These objects share some properties, but most of them differ.\n\n#### Shared properties\n\nOne can determine the type of address by calling `addr.kind()`. It will return\neither `\"ipv6\"` or `\"ipv4\"`.\n\nAn address can be converted back to its string representation with `addr.toString()`.\nNote that this method:\n * does not return the original string used to create the object (in fact, there is\n   no way of getting that string)\n * returns a compact representation (when it is applicable)\n\nA `match(range, bits)` method can be used to check if the address falls into a\ncertain CIDR range. Note that an address can be (obviously) matched only against an address of the same type.\n\nFor example:\n\n```js\nconst addr  = ipaddr.parse('2001:db8:1234::1');\nconst range = ipaddr.parse('2001:db8::');\n\naddr.match(range, 32); // => true\n```\n\nAlternatively, `match` can also be called as `match([range, bits])`. In this way, it can be used together with the `parseCIDR(string)` method, which parses an IP address together with a CIDR range.\n\nFor example:\n\n```js\nconst addr = ipaddr.parse('2001:db8:1234::1');\n\naddr.match(ipaddr.parseCIDR('2001:db8::/32')); // => true\n```\n\nA `range()` method returns one of predefined names for several special ranges defined by IP protocols. The exact names (and their respective CIDR ranges) can be looked up in the source: [IPv6 ranges] and [IPv4 ranges]. Some common ones include `\"unicast\"` (the default one) and `\"reserved\"`.\n\nYou can match against your own range list by using\n`ipaddr.subnetMatch(address, rangeList, defaultName)` method. It can work with a mix of IPv6 or IPv4 addresses, and accepts a name-to-subnet map as the range list. For example:\n\n```js\nconst rangeList = {\n  documentationOnly: [ ipaddr.parse('2001:db8::'), 32 ],\n  tunnelProviders: [\n    [ ipaddr.parse('2001:470::'), 32 ], // he.net\n    [ ipaddr.parse('2001:5c0::'), 32 ]  // freenet6\n  ]\n};\nipaddr.subnetMatch(ipaddr.parse('2001:470:8:66::1'), rangeList, 'unknown'); // => \"tunnelProviders\"\n```\n\nThe addresses can be converted to their byte representation with `toByteArray()`. (Actually, JavaScript mostly does not know about byte buffers. They are emulated with arrays of numbers, each in range of 0..255.)\n\n```js\nconst bytes = ipaddr.parse('2a00:1450:8007::68').toByteArray(); // ipv6.google.com\nbytes // => [42, 0x00, 0x14, 0x50, 0x80, 0x07, 0x00, <zeroes...>, 0x00, 0x68 ]\n```\n\nThe `ipaddr.IPv4` and `ipaddr.IPv6` objects have some methods defined, too. All of them have the same interface for both protocols, and are similar to global methods.\n\n`ipaddr.IPvX.isValid(string)` can be used to check if the string is a valid address for particular protocol, and `ipaddr.IPvX.parse(string)` is the error-throwing parser.\n\n`ipaddr.IPvX.isValid(string)` uses the same format for parsing as the POSIX `inet_ntoa` function, which accepts unusual formats like `0xc0.168.1.1` or `0x10000000`. The function `ipaddr.IPv4.isValidFourPartDecimal(string)` validates the IPv4 address and also ensures that it is written in four-part decimal format.\n\n[IPv6 ranges]: https://github.com/whitequark/ipaddr.js/blob/master/lib/ipaddr.js#L530\n[IPv4 ranges]: https://github.com/whitequark/ipaddr.js/blob/master/lib/ipaddr.js#L182\n\n#### IPv6 properties\n\nSometimes you will want to convert IPv6 not to a compact string representation (with the `::` substitution); the `toNormalizedString()` method will return an address where all zeroes are explicit.\n\nFor example:\n\n```js\nconst addr = ipaddr.parse('2001:0db8::0001');\naddr.toString(); // => '2001:db8::1'\naddr.toNormalizedString(); // => '2001:db8:0:0:0:0:0:1'\n```\n\nThe `isIPv4MappedAddress()` method will return `true` if this address is an IPv4-mapped\none, and `toIPv4Address()` will return an IPv4 object address.\n\nTo access the underlying binary representation of the address, use `addr.parts`.\n\n```js\nconst addr = ipaddr.parse('2001:db8:10::1234:DEAD');\naddr.parts // => [0x2001, 0xdb8, 0x10, 0, 0, 0, 0x1234, 0xdead]\n```\n\nA IPv6 zone index can be accessed via `addr.zoneId`:\n\n```js\nconst addr = ipaddr.parse('2001:db8::%eth0');\naddr.zoneId // => 'eth0'\n```\n\n#### IPv4 properties\n\n`toIPv4MappedAddress()` will return a corresponding IPv4-mapped IPv6 address.\n\nTo access the underlying representation of the address, use `addr.octets`.\n\n```js\nconst addr = ipaddr.parse('***********');\naddr.octets // => [192, 168, 1, 1]\n```\n\n`prefixLengthFromSubnetMask()` will return a CIDR prefix length for a valid IPv4 netmask or\nnull if the netmask is not valid.\n\n```js\nipaddr.IPv4.parse('***************').prefixLengthFromSubnetMask() == 28\nipaddr.IPv4.parse('*************').prefixLengthFromSubnetMask()  == null\n```\n\n`subnetMaskFromPrefixLength()` will return an IPv4 netmask for a valid CIDR prefix length.\n\n```js\nipaddr.IPv4.subnetMaskFromPrefixLength(24) == '*************'\nipaddr.IPv4.subnetMaskFromPrefixLength(29) == '***************'\n```\n\n`broadcastAddressFromCIDR()` will return the broadcast address for a given IPv4 interface and netmask in CIDR notation.\n```js\nipaddr.IPv4.broadcastAddressFromCIDR('*********/24') == '***********'\n```\n`networkAddressFromCIDR()` will return the network address for a given IPv4 interface and netmask in CIDR notation.\n```js\nipaddr.IPv4.networkAddressFromCIDR('*********/24') == '*********'\n```\n\n#### Conversion\n\nIPv4 and IPv6 can be converted bidirectionally to and from network byte order (MSB) byte arrays.\n\nThe `fromByteArray()` method will take an array and create an appropriate IPv4 or IPv6 object\nif the input satisfies the requirements. For IPv4 it has to be an array of four 8-bit values,\nwhile for IPv6 it has to be an array of sixteen 8-bit values.\n\nFor example:\n```js\nconst addr = ipaddr.fromByteArray([0x7f, 0, 0, 1]);\naddr.toString(); // => '127.0.0.1'\n```\n\nor\n\n```js\nconst addr = ipaddr.fromByteArray([0x20, 1, 0xd, 0xb8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1])\naddr.toString(); // => '2001:db8::1'\n```\n\nBoth objects also offer a `toByteArray()` method, which returns an array in network byte order (MSB).\n\nFor example:\n```js\nconst addr = ipaddr.parse('127.0.0.1');\naddr.toByteArray(); // => [0x7f, 0, 0, 1]\n```\n\nor\n\n```js\nconst addr = ipaddr.parse('2001:db8::1');\naddr.toByteArray(); // => [0x20, 1, 0xd, 0xb8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1]\n```\n", "keywords": ["ip", "ipv4", "ipv6"], "bugs": {"url": "https://github.com/whitequark/ipaddr.js/issues"}, "readmeFilename": "README.md", "license": "MIT", "users": {"msmiley": true, "qlqllu": true, "subchen": true, "bacra": true, "mojaray2k": true, "dmdnkv": true, "skellertor": true, "michaelermer": true, "omar84": true, "monjer": true, "keenwon": true, "nazy": true, "asaupup": true, "nuwaio": true, "heineiuo": true, "morogasper": true, "luckyluke": true, "cedx": true}, "homepage": "https://github.com/whitequark/ipaddr.js#readme"}