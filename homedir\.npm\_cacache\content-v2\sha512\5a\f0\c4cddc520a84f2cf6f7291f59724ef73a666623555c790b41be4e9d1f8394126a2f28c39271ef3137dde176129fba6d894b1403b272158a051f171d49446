{"name": "@napi-rs/wasm-runtime", "dist-tags": {"latest": "0.2.11"}, "versions": {"0.1.0": {"name": "@napi-rs/wasm-runtime", "version": "0.1.0", "devDependencies": {"memfs": "^4.6.0", "tslib": "^2.6.2", "buffer": "^6.0.3", "rollup": "^4.9.5", "process": "^0.11.10", "@emnapi/core": "^0.45.0", "@emnapi/runtime": "^0.45.0", "path-browserify": "^1.0.1", "readable-stream": "^4.5.2", "@tybys/wasm-util": "^0.8.1", "@rollup/plugin-json": "^6.0.1", "@rollup/plugin-alias": "^5.1.0", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-replace": "^5.0.5", "node-inspect-extracted": "^3.0.0", "@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-node-resolve": "^15.2.3", "rollup-plugin-polyfill-node": "^0.13.0"}, "dist": {"shasum": "6ceaea751a7cb37318814a492da6011fe56daa8e", "tarball": "https://registry.npmjs.org/@napi-rs/wasm-runtime/-/wasm-runtime-0.1.0.tgz", "fileCount": 4, "integrity": "sha512-4mQ6dIgVmy25ZoZb1u/5ZCswmbeANq7xSpJhcBgtQRs/QQ4HlfntzjkPbp8CJBPX91HRK3Ben8N5RHogDeGcww==", "signatures": [{"sig": "MEUCIAwQnwlLcrJ3iBCKZJkb4+Bsj33wROu5Rj9iJmdGbrI2AiEAr8W/I58ZRKowk3fFPMokCfQ2Gzl2mjk+IFHKQPPBkko=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@napi-rs%2fwasm-runtime@0.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 7708462}}, "0.1.1": {"name": "@napi-rs/wasm-runtime", "version": "0.1.1", "dependencies": {"@emnapi/core": "^0.45.0", "@emnapi/runtime": "^0.45.0", "@tybys/wasm-util": "^0.8.1"}, "devDependencies": {"memfs": "^4.6.0", "tslib": "^2.6.2", "buffer": "^6.0.3", "rollup": "^4.9.5", "process": "^0.11.10", "path-browserify": "^1.0.1", "readable-stream": "^4.5.2", "@rollup/plugin-json": "^6.0.1", "@rollup/plugin-alias": "^5.1.0", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-replace": "^5.0.5", "node-inspect-extracted": "^3.0.0", "@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-node-resolve": "^15.2.3", "rollup-plugin-polyfill-node": "^0.13.0"}, "dist": {"shasum": "ec090e2f46bee2ed0c8486dd9d97ddca836ae30e", "tarball": "https://registry.npmjs.org/@napi-rs/wasm-runtime/-/wasm-runtime-0.1.1.tgz", "fileCount": 5, "integrity": "sha512-ATj9ua659JgrkICjJscaeZdmPr44cb/KFjNWuD0N6pux0SpzaM7+iOuuK11mAnQM2N9q0DT4REu6NkL8ZEhopw==", "signatures": [{"sig": "MEUCIHYon4d549N+iZj/5YAykCdahTb90Jlz9Mco3rHmR0ByAiEAtZszA6TsoinKdLLR6Y38DywiY0BoLpt2OxcAmh1963A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@napi-rs%2fwasm-runtime@0.1.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 7708817}}, "0.1.2": {"name": "@napi-rs/wasm-runtime", "version": "0.1.2", "dependencies": {"@emnapi/core": "^1.1.0", "@emnapi/runtime": "^1.1.0", "@tybys/wasm-util": "^0.8.1"}, "devDependencies": {"memfs": "^4.6.0", "tslib": "^2.6.2", "buffer": "^6.0.3", "rollup": "^4.9.6", "process": "^0.11.10", "path-browserify": "^1.0.1", "readable-stream": "^4.5.2", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-alias": "^5.1.0", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-replace": "^5.0.5", "node-inspect-extracted": "^3.0.0", "@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-node-resolve": "^15.2.3", "rollup-plugin-polyfill-node": "^0.13.0"}, "dist": {"shasum": "cff8330e3728ab4447af82b67277f067132d341f", "tarball": "https://registry.npmjs.org/@napi-rs/wasm-runtime/-/wasm-runtime-0.1.2.tgz", "fileCount": 5, "integrity": "sha512-8JuczewTFIZ/XIjHQ+YlQUydHvlKx2hkcxtuGwh+t/t5zWyZct6YG4+xjHcq8xyc/e7FmFwf42Zj2YgICwmlvA==", "signatures": [{"sig": "MEUCIF+8iUx5jjkdTZIvmiqam0s8tr1jWSjF9ZJFuhGeob0GAiEAgO0Zyg8RyTlPsV5uTNjuUB74lA3j0ONDiIw1Mr89uAw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@napi-rs%2fwasm-runtime@0.1.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 7726780}}, "0.2.0": {"name": "@napi-rs/wasm-runtime", "version": "0.2.0", "dependencies": {"@emnapi/core": "^1.1.0", "@emnapi/runtime": "^1.1.0", "@tybys/wasm-util": "^0.8.2"}, "devDependencies": {"memfs": "^4.6.0", "tslib": "^2.6.2", "buffer": "^6.0.3", "rollup": "^4.9.6", "process": "^0.11.10", "path-browserify": "^1.0.1", "readable-stream": "^4.5.2", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-alias": "^5.1.0", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-replace": "^5.0.5", "node-inspect-extracted": "^3.0.0", "@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-node-resolve": "^15.2.3", "rollup-plugin-polyfill-node": "^0.13.0"}, "dist": {"shasum": "e3ed763c18f4e674ed3f5826318864005f9dd966", "tarball": "https://registry.npmjs.org/@napi-rs/wasm-runtime/-/wasm-runtime-0.2.0.tgz", "fileCount": 5, "integrity": "sha512-ETCooPEZJN7cGp6GwE5mUTrStrhIeuEPo0lOhLe48Iz35z6CkbgDauR8nE/K4GaMEu2BwTOm2Epy7ru8w1ua7w==", "signatures": [{"sig": "MEQCIDrapJlHueDj4sGMiGHmJ2f3EmlpsFADZEMfFYQNJwy8AiAtXY0mXXUt1qd2xRwY04mv+txfb8sZJZQ/iaQpY2VQ/w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@napi-rs%2fwasm-runtime@0.2.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 7750996}}, "0.2.1": {"name": "@napi-rs/wasm-runtime", "version": "0.2.1", "dependencies": {"@emnapi/core": "^1.1.0", "@emnapi/runtime": "^1.1.0", "@tybys/wasm-util": "^0.8.2"}, "devDependencies": {"memfs": "^4.8.2", "tslib": "^2.6.2", "buffer": "^6.0.3", "rollup": "^4.9.6", "process": "^0.11.10", "path-browserify": "^1.0.1", "readable-stream": "^4.5.2", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-alias": "^5.1.0", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-replace": "^5.0.5", "node-inspect-extracted": "^3.0.0", "@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-node-resolve": "^15.2.3", "rollup-plugin-polyfill-node": "^0.13.0"}, "dist": {"shasum": "6c943d07a1a301d774f807fb412afae7b7e63f53", "tarball": "https://registry.npmjs.org/@napi-rs/wasm-runtime/-/wasm-runtime-0.2.1.tgz", "fileCount": 5, "integrity": "sha512-fGODCBcZm6ikQ70X7znOZ122xCmc8GOpV5tIMuUEoFeNKfsokbOw92KwS6bsIkvnR20F2CvRo/e+EDUPW+h7cA==", "signatures": [{"sig": "MEUCIQD3gn6PzJ7pEgflwQy7rNJeKy8FPE82B7J1XIZIyR27egIgCB3VGGiY7bpWmO32ryy2F/OE+FAJNV3bEMAGVwA0+xo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@napi-rs%2fwasm-runtime@0.2.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 7759538}}, "0.2.2": {"name": "@napi-rs/wasm-runtime", "version": "0.2.2", "dependencies": {"@emnapi/core": "^1.1.0", "@emnapi/runtime": "^1.1.0", "@tybys/wasm-util": "^0.8.3"}, "devDependencies": {"memfs": "^4.8.2", "tslib": "^2.6.2", "buffer": "^6.0.3", "rollup": "^4.9.6", "process": "^0.11.10", "path-browserify": "^1.0.1", "readable-stream": "^4.5.2", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-alias": "^5.1.0", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-replace": "^5.0.5", "node-inspect-extracted": "^3.0.0", "@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-node-resolve": "^15.2.3", "rollup-plugin-polyfill-node": "^0.13.0"}, "dist": {"shasum": "9f77911e77c88ab35869536a383c267507bc3201", "tarball": "https://registry.npmjs.org/@napi-rs/wasm-runtime/-/wasm-runtime-0.2.2.tgz", "fileCount": 5, "integrity": "sha512-J+9w4jy5wNrjN0C+PiGE6kqg3s4KKS7IG5lTmOMp487r78CtMXbDpDxVD36w3yW0h8ehzcfgAYyc8BH+OLpODg==", "signatures": [{"sig": "MEUCIEM5vq+kZC1B2wHMaf8ZmF/Oy8fmgf+zTpx0clxBgUglAiEA8wp0sSt5/e7ENfukZHY5VTPd2YGYH0rJYQKSQETTWOE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@napi-rs%2fwasm-runtime@0.2.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 7783008}}, "0.2.3": {"name": "@napi-rs/wasm-runtime", "version": "0.2.3", "dependencies": {"@emnapi/core": "^1.1.0", "@emnapi/runtime": "^1.1.0", "@tybys/wasm-util": "^0.8.3"}, "devDependencies": {"memfs": "^4.8.2", "tslib": "^2.6.2", "buffer": "^6.0.3", "rollup": "^4.9.6", "process": "^0.11.10", "path-browserify": "^1.0.1", "readable-stream": "^4.5.2", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-alias": "^5.1.0", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-replace": "^5.0.5", "node-inspect-extracted": "^3.0.0", "@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-node-resolve": "^15.2.3", "rollup-plugin-polyfill-node": "^0.13.0"}, "dist": {"shasum": "7b49e84e2b83be64c6638ce2de3b1194553d2603", "tarball": "https://registry.npmjs.org/@napi-rs/wasm-runtime/-/wasm-runtime-0.2.3.tgz", "fileCount": 6, "integrity": "sha512-e4qmGDzXu2MYjj/XiKSgJ7XS7Z83MYVRN1yYaYXeQNVEO56zmshqmzFaELfdb612sLq/GmiPfRIwSji+bIlyCw==", "signatures": [{"sig": "MEQCICOaEXd1hZ2VWsjZ2aH0PLvT2bGNNl7GMJ9Cj6iXuljuAiAgc3y9Sl9hJEgmMSHdi0mxNwBZ5VrV4QKvIzPSd+IZIQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@napi-rs%2fwasm-runtime@0.2.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 7788525}}, "0.2.4": {"name": "@napi-rs/wasm-runtime", "version": "0.2.4", "dependencies": {"@emnapi/core": "^1.1.0", "@emnapi/runtime": "^1.1.0", "@tybys/wasm-util": "^0.9.0"}, "devDependencies": {"memfs": "^4.8.2", "tslib": "^2.6.2", "buffer": "^6.0.3", "rollup": "^4.9.6", "process": "^0.11.10", "path-browserify": "^1.0.1", "readable-stream": "^4.5.2", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-alias": "^5.1.0", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-replace": "^5.0.5", "node-inspect-extracted": "^3.0.0", "@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-node-resolve": "^15.2.3", "rollup-plugin-polyfill-node": "^0.13.0"}, "dist": {"shasum": "d27788176f250d86e498081e3c5ff48a17606918", "tarball": "https://registry.npmjs.org/@napi-rs/wasm-runtime/-/wasm-runtime-0.2.4.tgz", "fileCount": 6, "integrity": "sha512-9zESzOO5aDByvhIAsOy9TbpZ0Ur2AJbUI7UT73kcUTS2mxAMHOBaa1st/jAymNoCtvrit99kkzT1FZuXVcgfIQ==", "signatures": [{"sig": "MEUCIQCZrsu9QWOKuWxXtKXLZzAuFi/9fLZnFkF9C+IwcT48owIgc2/JUSmaJ4fHIMnJ4+52UZ2l+U6Eo7ghoAMB7+mp1pA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@napi-rs%2fwasm-runtime@0.2.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 7934811}}, "0.2.5": {"name": "@napi-rs/wasm-runtime", "version": "0.2.5", "dependencies": {"@emnapi/core": "^1.1.0", "@emnapi/runtime": "^1.1.0", "@tybys/wasm-util": "^0.9.0"}, "devDependencies": {"memfs": "^4.8.2", "tslib": "^2.6.2", "buffer": "^6.0.3", "rollup": "^4.9.6", "process": "^0.11.10", "path-browserify": "^1.0.1", "readable-stream": "^4.5.2", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-alias": "^5.1.0", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-replace": "^5.0.5", "node-inspect-extracted": "^3.0.0", "@rollup/plugin-commonjs": "^28.0.0", "@rollup/plugin-node-resolve": "^15.2.3", "rollup-plugin-polyfill-node": "^0.13.0"}, "dist": {"shasum": "b6f5079408305fe6a3529ccb2bb8ba8d9b7a02e7", "tarball": "https://registry.npmjs.org/@napi-rs/wasm-runtime/-/wasm-runtime-0.2.5.tgz", "fileCount": 6, "integrity": "sha512-kwUxR7J9WLutBbulqg1dfOrMTwhMdXLdcGUhcbCcGwnPLt3gz19uHVdwH1syKVDbE022ZS2vZxOWflFLS0YTjw==", "signatures": [{"sig": "MEUCIBaYWQ7dAM6lXREseadtFxXkVJdIP77vjAuIq3uulrn9AiEA1NX9t4PoGZIGEltZGOi3DmcR66D39BhGL544IeL7ql8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@napi-rs%2fwasm-runtime@0.2.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 8148390}}, "0.2.6": {"name": "@napi-rs/wasm-runtime", "version": "0.2.6", "dependencies": {"@emnapi/core": "^1.3.1", "@emnapi/runtime": "^1.3.1", "@tybys/wasm-util": "^0.9.0"}, "devDependencies": {"memfs": "^4.13.0", "tslib": "^2.7.0", "buffer": "^6.0.3", "rollup": "^4.24.0", "process": "^0.11.10", "path-browserify": "^1.0.1", "readable-stream": "^4.5.2", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-alias": "^5.1.1", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-replace": "^6.0.1", "node-inspect-extracted": "^3.0.2", "@rollup/plugin-commonjs": "^28.0.0", "@rollup/plugin-node-resolve": "^16.0.0", "rollup-plugin-polyfill-node": "^0.13.0"}, "dist": {"shasum": "d1413a709622e7d6cf8a5b42fae76609184de6c9", "tarball": "https://registry.npmjs.org/@napi-rs/wasm-runtime/-/wasm-runtime-0.2.6.tgz", "fileCount": 6, "integrity": "sha512-z8YVS3XszxFTO73iwvFDNpQIzdMmSDTP/mB3E/ucR37V3Sx57hSExcXyMoNwaucWxnsWf4xfbZv0iZ30jr0M4Q==", "signatures": [{"sig": "MEUCIQDeoMN3ffgUWWvr2XKSZKPkXEv4712PCr5IP7SY3/eA/gIgKGxHsEuQbY4TVQp7xh9qtoaI2/fXRNmbaEsfWKSEEUg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@napi-rs%2fwasm-runtime@0.2.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 7999548}}, "0.2.7": {"name": "@napi-rs/wasm-runtime", "version": "0.2.7", "dependencies": {"@emnapi/core": "^1.3.1", "@emnapi/runtime": "^1.3.1", "@tybys/wasm-util": "^0.9.0"}, "devDependencies": {"memfs": "^4.17.0", "tslib": "^2.8.1", "buffer": "^6.0.3", "rollup": "^4.34.8", "process": "^0.11.10", "path-browserify": "^1.0.1", "readable-stream": "^4.7.0", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-alias": "^5.1.1", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-replace": "^6.0.1", "node-inspect-extracted": "^3.0.2", "@rollup/plugin-commonjs": "^28.0.0", "@rollup/plugin-node-resolve": "^16.0.0", "rollup-plugin-polyfill-node": "^0.13.0"}, "dist": {"shasum": "288f03812a408bc53c2c3686c65f38fe90f295eb", "tarball": "https://registry.npmjs.org/@napi-rs/wasm-runtime/-/wasm-runtime-0.2.7.tgz", "fileCount": 6, "integrity": "sha512-5yximcFK5FNompXfJFoWanu5l8v1hNGqNHh9du1xETp9HWk/B/PzvchX55WYOPaIeNglG8++68AAiauBAtbnzw==", "signatures": [{"sig": "MEUCIF+N75bn5SonO6G2JZHTkbWKkHmFuKMuofrO1fnY03inAiEAg6zdjwlsVE8yrNWeRe902e2KFiFDE7ZXSwJJnUaMEuI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@napi-rs%2fwasm-runtime@0.2.7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 8039721}}, "0.2.8": {"name": "@napi-rs/wasm-runtime", "version": "0.2.8", "dependencies": {"@emnapi/core": "^1.4.0", "@emnapi/runtime": "^1.4.0", "@tybys/wasm-util": "^0.9.0"}, "devDependencies": {"memfs": "^4.17.0", "tslib": "^2.8.1", "buffer": "^6.0.3", "rollup": "^4.38.0", "process": "^0.11.10", "path-browserify": "^1.0.1", "readable-stream": "^4.7.0", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-alias": "^5.1.1", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-replace": "^6.0.2", "node-inspect-extracted": "^3.0.2", "@rollup/plugin-commonjs": "^28.0.3", "@rollup/plugin-node-resolve": "^16.0.1", "rollup-plugin-polyfill-node": "^0.13.0"}, "dist": {"shasum": "642e8390ee78ed21d6b79c467aa610e249224ed6", "tarball": "https://registry.npmjs.org/@napi-rs/wasm-runtime/-/wasm-runtime-0.2.8.tgz", "fileCount": 6, "integrity": "sha512-OBlgKdX7gin7OIq4fadsjpg+cp2ZphvAIKucHsNfTdJiqdOmOEwQd/bHi0VwNrcw5xpBJyUw6cK/QilCqy1BSg==", "signatures": [{"sig": "MEQCIC8ZPDyK1rZdcZm/+u/NEi4lJBw0+LP4LAHb25MZ61+fAiAU3MfacXmZP84sboe5txonDFNzWTBuGqHoV9jUx1lHzQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@napi-rs%2fwasm-runtime@0.2.8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 8024042}}, "0.2.9": {"name": "@napi-rs/wasm-runtime", "version": "0.2.9", "dependencies": {"@emnapi/core": "^1.4.0", "@emnapi/runtime": "^1.4.0", "@tybys/wasm-util": "^0.9.0"}, "devDependencies": {"memfs": "^4.17.0", "tslib": "^2.8.1", "buffer": "^6.0.3", "rollup": "^4.38.0", "process": "^0.11.10", "path-browserify": "^1.0.1", "readable-stream": "^4.7.0", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-alias": "^5.1.1", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-replace": "^6.0.2", "node-inspect-extracted": "^3.0.2", "@rollup/plugin-commonjs": "^28.0.3", "@rollup/plugin-node-resolve": "^16.0.1", "rollup-plugin-polyfill-node": "^0.13.0"}, "dist": {"shasum": "7278122cf94f3b36d8170a8eee7d85356dfa6a96", "tarball": "https://registry.npmjs.org/@napi-rs/wasm-runtime/-/wasm-runtime-0.2.9.tgz", "fileCount": 6, "integrity": "sha512-OKRBiajrrxB9ATokgEQoG87Z25c67pCpYcCwmXYX8PBftC9pBfN18gnm/fh1wurSLEKIAt+QRFLFCQISrb66Jg==", "signatures": [{"sig": "MEUCIGjTKJDamYyczr1PZ3fQgrkIGwCwwEKMxAMXsTOHDeenAiEApZH9oPR3mCy0QuvGuXS3ptutUlxazQ5my46XXg8Sryk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@napi-rs%2fwasm-runtime@0.2.9", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 8025348}}, "0.2.10": {"name": "@napi-rs/wasm-runtime", "version": "0.2.10", "dependencies": {"@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0"}, "devDependencies": {"memfs": "^4.17.0", "tslib": "^2.8.1", "buffer": "^6.0.3", "rollup": "^4.38.0", "process": "^0.11.10", "path-browserify": "^1.0.1", "readable-stream": "^4.7.0", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-alias": "^5.1.1", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-replace": "^6.0.2", "node-inspect-extracted": "^3.0.2", "@rollup/plugin-commonjs": "^28.0.3", "@rollup/plugin-node-resolve": "^16.0.1", "rollup-plugin-polyfill-node": "^0.13.0"}, "dist": {"shasum": "f3b7109419c6670000b2401e0c778b98afc25f84", "tarball": "https://registry.npmjs.org/@napi-rs/wasm-runtime/-/wasm-runtime-0.2.10.tgz", "fileCount": 6, "integrity": "sha512-bCsCyeZEwVErsGmyPNSzwfwFn4OdxBj0mmv6hOFucB/k81Ojdu68RbZdxYsRQUPc9l6SU5F/cG+bXgWs3oUgsQ==", "signatures": [{"sig": "MEQCIHFHXjJFVv5Sujp0KLnsR2OgMkvcsdBszgmUbqSklDtSAiAU5EFejn4GYhX/5vJ2duGppO8HyYFSpNtfAQONQq+xmQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@napi-rs%2fwasm-runtime@0.2.10", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 8025605}}, "0.2.11": {"name": "@napi-rs/wasm-runtime", "version": "0.2.11", "dependencies": {"@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0"}, "devDependencies": {"@rollup/plugin-alias": "^5.1.1", "@rollup/plugin-commonjs": "^28.0.3", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^16.0.1", "@rollup/plugin-replace": "^6.0.2", "buffer": "^6.0.3", "memfs": "^4.17.0", "node-inspect-extracted": "^3.0.2", "path-browserify": "^1.0.1", "process": "^0.11.10", "readable-stream": "^4.7.0", "rollup": "^4.38.0", "rollup-plugin-polyfill-node": "^0.13.0", "tslib": "^2.8.1"}, "dist": {"integrity": "sha512-9DPkXtvHydrcOsopiYpUgPHpmj0HWZKMUnL2dZqpvC42lsratuBG06V5ipyno0fUek5VlFsNQ+AcFATSrJXgMA==", "shasum": "192c1610e1625048089ab4e35bc0649ce478500e", "tarball": "https://registry.npmjs.org/@napi-rs/wasm-runtime/-/wasm-runtime-0.2.11.tgz", "fileCount": 6, "unpackedSize": 8021938, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@napi-rs%2fwasm-runtime@0.2.11", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQC920kMhAfQQROKV5AJURDgdQgrFV+wX3NMLVUxAWcbtQIhAJRPGqA+zh+qGy5kZnC4Ip9KFZ1f523PlHwL71/VJEnc"}]}}}, "modified": "2025-06-08T13:44:58.417Z"}