{"name": "joi", "dist-tags": {"node-0.x": "6.10.1", "9-beta": "9.0.0-9", "v12-legacy": "12.1.1", "latest": "17.13.3"}, "versions": {"0.0.1": {"name": "joi", "version": "0.0.1", "devDependencies": {"mocha": "1.x.x", "sinon": "1.3.4", "should": "1.x.x"}, "dist": {"shasum": "a3cf9f4cf9bf321d9a9c431c0e1eab91b734e9d0", "tarball": "https://registry.npmjs.org/joi/-/joi-0.0.1.tgz", "integrity": "sha512-KFfGl4ZQCEt3wduwJG0Qu6ZkXGrw6+/20G8J6UN9sChgpGfbp7IqakipTQ5GAKqSvm8i56Fe8MMkZ+vmpzM2Lg==", "signatures": [{"sig": "MEUCIDy5eYQLLkUb3HfP3LOaail+pJe7WKM/ykQJXY3YLxh6AiEA2TsQ6gaXAR8u/Z4e9f3xAyrQmK27BrpJbpIaOdBNsJY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "0.0.2": {"name": "joi", "version": "0.0.2", "dependencies": {"hoek": "0.0.x"}, "devDependencies": {"mocha": "1.x.x", "sinon": "1.3.4", "should": "1.x.x"}, "dist": {"shasum": "b8184362f2df6f04df963c81556c1f11fd5e8066", "tarball": "https://registry.npmjs.org/joi/-/joi-0.0.2.tgz", "integrity": "sha512-86QHq1P44sdsDbzxQjtNzlcMWZnkiVTnYmNv5VoiINESaL4Dt86TSq1R17cwuVVhxXfz3itXN2lXwuFVi7csCg==", "signatures": [{"sig": "MEYCIQDWhZARvC0NPUK7LnN237Oe7vJNZ5tUuD2a1arVEXMDWwIhAM2PV3eHyQ1C4g/++3a1PAvGlXEuAmzOIkqiW2dEYaY/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "0.0.3": {"name": "joi", "version": "0.0.3", "dependencies": {"hoek": "0.0.x"}, "devDependencies": {"mocha": "1.x.x", "sinon": "1.3.4", "should": "1.x.x"}, "dist": {"shasum": "18ebcbf5bab6623f893a76aea07d0662f62cf20d", "tarball": "https://registry.npmjs.org/joi/-/joi-0.0.3.tgz", "integrity": "sha512-Azw4S67e9tskdvjZ9WZdvDu67xKqLog1c5/QRLO/Yt3daXVEuZ8tQXl757c7rDZr1zg0OnUaR5knANabsDKa2Q==", "signatures": [{"sig": "MEUCIEUgKP/EjQbM9Gl+C9NSuA0g/mleotZZl2Yh5nkpbgUtAiEApPYseUqv0gG/MnMgtvZR4+BGMRKkJ1Wr9brvUao3ZAI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "0.0.4": {"name": "joi", "version": "0.0.4", "dependencies": {"hoek": "0.0.x"}, "devDependencies": {"mocha": "1.x.x", "sinon": "1.3.4", "should": "1.x.x"}, "dist": {"shasum": "4d2f4b89b9b64f6dcfffadb20b36e7fb27f8fb8b", "tarball": "https://registry.npmjs.org/joi/-/joi-0.0.4.tgz", "integrity": "sha512-jl4OAN6ncrMvjDWToCkDxktGDbKy3iwvQcF5Vd4Vs+x1SihrwAiRt/4rtWT0DyCr82a5QrQu4GAxMlp20SSISA==", "signatures": [{"sig": "MEYCIQDeS/HHyCC43zuytSpwTYTVd6Bj38zUgWAGd02s4jMINgIhAK99DDhdAZDZfeXECzXUWQhlEBS2qX6fz74kTAlEW9/X", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "0.0.5": {"name": "joi", "version": "0.0.5", "dependencies": {"hoek": "0.0.x"}, "devDependencies": {"mocha": "1.x.x", "sinon": "1.3.4", "should": "1.x.x"}, "dist": {"shasum": "e9133491daa7948821b0df7bbee71c62714362d3", "tarball": "https://registry.npmjs.org/joi/-/joi-0.0.5.tgz", "integrity": "sha512-K4kzlF81JqDdsGs5VArUF4H2WmMAdN9E90u5fvoixRqCM747OwF2qp1WR5z+xoWT0VJTGIm3RsV3xvaQOvUSCg==", "signatures": [{"sig": "MEUCID5RkV5mHp6VGHaX01eBY1YAVIGVFl/QNbxu+zRpHXrzAiEA4xqLTXJXCvYti8TvE6HRefR6cdoAbCu5BT4PFoJuVpA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "0.0.6": {"name": "joi", "version": "0.0.6", "dependencies": {"hoek": "0.0.x"}, "devDependencies": {"chai": "1.0.x", "mocha": "1.x.x"}, "dist": {"shasum": "100f22a8ac69b6193d26d5e7ad6777d4eb0c345f", "tarball": "https://registry.npmjs.org/joi/-/joi-0.0.6.tgz", "integrity": "sha512-qgpkBE9HvbmRC+AUPNF7P3dO+Q8FzTmWeHGlSv5sHS1EtpM60WtJjkarPs0nxkiELGpWXYeBpcPKORGuRJsRpQ==", "signatures": [{"sig": "MEUCIQCjI4fO+FHJg7ecEFhJhsDBuKf1UKpXsKr29+Kaa1wyzwIgXppbPIPLDkInHUTGan3V11gzP5IMKScPxRx1XHRYMpU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "0.0.6-1": {"name": "joi", "version": "0.0.6-1", "dependencies": {"hoek": "0.0.x"}, "devDependencies": {"chai": "1.0.x", "mocha": "1.x.x"}, "dist": {"shasum": "15c571c3f58e85cd03fb73fd895eb1a122772fab", "tarball": "https://registry.npmjs.org/joi/-/joi-0.0.6-1.tgz", "integrity": "sha512-6Il9vSZwIF/2YUlDgIdp2O+VWY2Xdpku6r8aHy5qg0vVcd0euGy426i+Bf4X4bvLuHDo6t0E6Pl7OLTDkcOs7A==", "signatures": [{"sig": "MEQCICU9IP9R3urSsf74mDqNsV/9jED2ycxGH+tKpBjx38jrAiBPMCIr78EoM787MVcplPtLkgknG820p3Uoyyn7raZBgg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "0.0.7": {"name": "joi", "version": "0.0.7", "dependencies": {"hoek": "0.4.x"}, "devDependencies": {"chai": "1.0.x", "mocha": "1.x.x"}, "dist": {"shasum": "bd608504eb2d942ca7fd70174b613d7cecc610bf", "tarball": "https://registry.npmjs.org/joi/-/joi-0.0.7.tgz", "integrity": "sha512-VOkg1jmGX/rlnHMcTeFHNJ23du4EoSo0b0uCKV8bsR05CKQZWRbuCWI77fp4C/tge0E8pTDr9SgEfs9kfiKTBw==", "signatures": [{"sig": "MEMCH0BZ1Kpttj6HIk+rFW6tUUcjP70H7w5Yy1+TRGWPAjYCIDG774X2hLPxwd0tUK2ghYT5jxkhJyj+/4AR9SWBzepx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "0.0.8": {"name": "joi", "version": "0.0.8", "dependencies": {"hoek": "0.4.x"}, "devDependencies": {"chai": "1.0.x", "mocha": "1.x.x"}, "dist": {"shasum": "b7720296e5adafa5e9c72168da65806160820631", "tarball": "https://registry.npmjs.org/joi/-/joi-0.0.8.tgz", "integrity": "sha512-NFrNoOoiq1kt3+rcFrC/VhZODFtO9AMG4UFOmjhkdNQcrvbMn0BD4X2DSAFU/dMajr2Zb/KCpE+D7JFOAVKnRA==", "signatures": [{"sig": "MEYCIQDzCAkjGam+CnnIif7yJjYBiSPojnWFV5f/Q1w/q4vY6gIhANbzO2IMC4OAy4wRGUSWLnKtu7hM0xToQgT+yQPGWlq+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "0.0.9": {"name": "joi", "version": "0.0.9", "dependencies": {"hoek": "0.4.x"}, "devDependencies": {"chai": "1.0.x", "mocha": "1.x.x"}, "dist": {"shasum": "399baf50fd35d9858f971693fd38549ccf8759b6", "tarball": "https://registry.npmjs.org/joi/-/joi-0.0.9.tgz", "integrity": "sha512-2gVwBkGaigQw5/cQxtsjYM68D0I5tIYGK4rErYCgSxGJPADUFExH6mlPk50u+Yl6o4CnNk392fIdfG1vWIvgSw==", "signatures": [{"sig": "MEUCICVFF/usIafazmuHRPpBtCNmW79BAvDtopOh7A+jJJLJAiEAsMeICsm8hrsUxprPbIuUCcwjvArrAgiZurQaef36wYg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "0.0.10": {"name": "joi", "version": "0.0.10", "dependencies": {"hoek": "0.4.x"}, "devDependencies": {"chai": "1.0.x", "mocha": "1.x.x"}, "dist": {"shasum": "5fe0f626f647d99d22c7c67848ad35c456f88292", "tarball": "https://registry.npmjs.org/joi/-/joi-0.0.10.tgz", "integrity": "sha512-aSsGHsOJBGAd2EXh4fVMXhQzVw11umrnUZmeTI5tHt6Ykc/+cr72HV15njrpGoYZqvmrhiyPuYmw44xO8cNyaQ==", "signatures": [{"sig": "MEQCIFYHPqAuZhPialwyBqt5EqH1UQvaKYeNE/wmE5/42qlzAiAn5dE3eymsg3BHNQHvqWLhEjRktMuSH/Z9vMpaqeXTog==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "0.1.0": {"name": "joi", "version": "0.1.0", "dependencies": {"hoek": "0.4.x"}, "devDependencies": {"chai": "1.x.x", "mocha": "1.x.x", "blanket": "1.0.x", "travis-cov": "0.2.x"}, "dist": {"shasum": "68ca0294235a6bee3f1ff394a82e510e1f4ab5ea", "tarball": "https://registry.npmjs.org/joi/-/joi-0.1.0.tgz", "integrity": "sha512-qdNSRd9ep+ma2Krxoaz9cRUqTrzgifC0YQwY8DHG4OmYvh7APhTzjTr1Ja6GKWFwK+w8AERnfJIuzDhEH6S6+g==", "signatures": [{"sig": "MEUCIQDYbRIA2LUXR2e/eriUhgj5Xk51fV5EMYhg1P6ND0yRGgIgVia0PWm0xAjXO/qxao39470lxph7yL/DqC9zxXcSkiY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "0.8.x"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "0.1.1": {"name": "joi", "version": "0.1.1", "dependencies": {"hoek": "0.4.x"}, "devDependencies": {"chai": "1.x.x", "mocha": "1.x.x", "blanket": "1.0.x", "travis-cov": "0.2.x"}, "dist": {"shasum": "1a53595c8bdb0069b53a0bc7df9c176671ee356c", "tarball": "https://registry.npmjs.org/joi/-/joi-0.1.1.tgz", "integrity": "sha512-7wLZMwxAKKaFJ7FlQrd2RzHciHUYmdRml5xFBfCV9vmBJ7lhl4vmQ2CV0EZatnwP/Y9SwJCWKCPQFW9NJ1Wvdg==", "signatures": [{"sig": "MEUCIQCp+vcmGENrBpMKPQx5dfJwmVFwXCYPv+97P21du5K8ugIgU0rt7Rcd8Fk+9aVDpd+3vrtCD0cO/hvqkWhQg6yKyUE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "0.8.x"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "0.1.2": {"name": "joi", "version": "0.1.2", "dependencies": {"hoek": "0.6.x"}, "devDependencies": {"chai": "1.x.x", "mocha": "1.x.x", "blanket": "1.0.x", "travis-cov": "0.2.x"}, "dist": {"shasum": "9959dd2d68d83594941df569adc4dfd1433dffcd", "tarball": "https://registry.npmjs.org/joi/-/joi-0.1.2.tgz", "integrity": "sha512-SazztvtqThOfTZFPjKHLcFyqaRzFQbb75Q/VKo1eN+5Gi/8WRtaNERwmKFNI3UKeXZtkNkaUSMj594otMWIq0A==", "signatures": [{"sig": "MEUCIQCv5oSo2YYi0L/pA/U7BIo4dw0SSijZIDv05IAfUR1vuwIgQU0ilD7tx2aoc4Iv0jUEMKL8u1MuwmXuBVrXeRiMTmE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "0.8.x"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "0.1.3": {"name": "joi", "version": "0.1.3", "dependencies": {"hoek": "0.6.x"}, "devDependencies": {"chai": "1.x.x", "mocha": "1.x.x", "blanket": "1.0.x", "travis-cov": "0.2.x"}, "dist": {"shasum": "1f3ca6963c0966b85a9478750e5530a9b951fcda", "tarball": "https://registry.npmjs.org/joi/-/joi-0.1.3.tgz", "integrity": "sha512-dwWD0bFTI5RAUb4z3nWDN250Vg0458vJ0WU7Yi++gDypuD7CnX6c/3MuMbvGkDxHxfXTaMl2z8oAHgf78eTtiQ==", "signatures": [{"sig": "MEUCIEe0h5ti8YoZmTxw85s11q4Xm1+14a9A3x5huLOqn5xkAiEAvp6lbSuthYrqOkro1s48EkNmSp8KrhzBQBGOGRL9sq0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "0.8.x"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "0.2.0": {"name": "joi", "version": "0.2.0", "dependencies": {"hoek": "0.6.x"}, "devDependencies": {"chai": "1.x.x", "mocha": "1.x.x", "blanket": "1.0.x", "travis-cov": "0.2.x"}, "dist": {"shasum": "ba192601b31a854024ea1498bf3c1ea6d586f78a", "tarball": "https://registry.npmjs.org/joi/-/joi-0.2.0.tgz", "integrity": "sha512-zdXjJCmUx4sbDE07BOeU4KoU4N9Wmmi0uDB0PyMUmVj0XVM3v09RwWc96jKpNG2/ODOjNxlGqEili0N5igM5hQ==", "signatures": [{"sig": "MEUCIQDx54/zRho4hq0qmmir57D5kAk52MuVG98NZ9h8eXFOGQIgBa5dmcoJEhBk+8sKdBtE6gh5Qav02mrIZCeR2wBbT78=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "0.8.x"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "0.2.1": {"name": "joi", "version": "0.2.1", "dependencies": {"hoek": "0.6.x"}, "devDependencies": {"lab": "0.0.x", "complexity-report": "0.x.x"}, "dist": {"shasum": "d95018adc93266b4c4ab306c77b39f1933e9495a", "tarball": "https://registry.npmjs.org/joi/-/joi-0.2.1.tgz", "integrity": "sha512-i0p995kL4DdD+Ym4+mAArjyPLNAc8f63I8rnSUbgT+l5PKNzCGal3dfLXyQMrxgo5ILRWsbJnhMtyVnBEf5b/A==", "signatures": [{"sig": "MEUCIQCZzN3/NoSNOt7uBmKUz9uFALeEcAsnJYeUzHgY5woT2wIgFg7i+R10gGRgkoQnZjczC/d75OuJD5O0vJXJIDi8qbA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "0.8.x"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "0.2.2": {"name": "joi", "version": "0.2.2", "dependencies": {"hoek": "0.7.x"}, "devDependencies": {"lab": "0.0.x", "complexity-report": "0.x.x"}, "dist": {"shasum": "a3b84fc37c86d2b4c44e113d0fa17a6f43c81050", "tarball": "https://registry.npmjs.org/joi/-/joi-0.2.2.tgz", "integrity": "sha512-AVU7wDGpqovLJoglmjwc0gMAiMI/5aDMnnS9CPw4BZIAaOmgIGYse0OKIvzx/yxSVWsHpSrqEgMC/VkmSo6VZw==", "signatures": [{"sig": "MEUCICoTUTAqvZGsmO3aN1de64bDSEjm6UAbGPM85aVJ/0EJAiEAjqQ3yzhOok6wMiiUQz8FHao1ZfpwM3l93kSs8HgNwxk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "0.8.x"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "0.2.3": {"name": "joi", "version": "0.2.3", "dependencies": {"hoek": "0.7.x"}, "devDependencies": {"lab": "0.0.x", "complexity-report": "0.x.x"}, "dist": {"shasum": "6a13a2834d96206c6114f7b5c6dcc83fca3d7af3", "tarball": "https://registry.npmjs.org/joi/-/joi-0.2.3.tgz", "integrity": "sha512-4MOy+ng88ZriYr8LjGoF8sbrCCiGFYt4UKq8u3uzcCTEGE/4xiVXF6jzklAHOwSdV8+fOe2c+Im8BFJVmP7MuQ==", "signatures": [{"sig": "MEUCIQDxybJBmvfS0yUpt/rRZQ0h10KGzt4smYKFR41NiLEnRwIgc8pI0jqH61pZl2KuVLfk9amMiHgdCLkshHJuJHiZEwU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "0.8.x"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "0.2.4": {"name": "joi", "version": "0.2.4", "dependencies": {"hoek": "0.7.x"}, "devDependencies": {"lab": "0.0.x", "complexity-report": "0.x.x"}, "dist": {"shasum": "cab9c06664f2e959a2d0a77ca8740b7f6a9f7a12", "tarball": "https://registry.npmjs.org/joi/-/joi-0.2.4.tgz", "integrity": "sha512-GICF9Q+byhzRgfjDIQWuNDs9p4VB9NElXSrydcm2qbF+2RBaULqAI5HX/exStObBAi771S7qtD74KWaSUTLIZg==", "signatures": [{"sig": "MEUCIGRTmqw97pjexpemAh+U976sXZRSopirCBz2RWv2u3eyAiEA4+B4qxNWmQnNbbyKGEB63NvPt1+aW9C5E1hADJI3Yic=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "0.8.x"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "0.2.5": {"name": "joi", "version": "0.2.5", "dependencies": {"hoek": "0.7.x"}, "devDependencies": {"lab": "0.0.x", "complexity-report": "0.x.x"}, "dist": {"shasum": "9440fce59aaf20998c96426e7ca82bdede57acaf", "tarball": "https://registry.npmjs.org/joi/-/joi-0.2.5.tgz", "integrity": "sha512-vSJ7+kcjj/uPoRRSYnBOMHX/vPBW9R2vQfNuIUe4YbhvVQccIM4clRsgPR/WYPbtcfCW1wKAXams5BQhX42SVg==", "signatures": [{"sig": "MEUCIDZBkyjJ4dM43ZpUZKqad/0BBHskPvK4AP1KwI+Ai5ErAiEAgkKaxyJn+IppGghajwLzFnzVJMSluBYJ+3lCV2dYD7U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "0.8.x"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "0.2.6": {"name": "joi", "version": "0.2.6", "dependencies": {"hoek": "0.7.x"}, "devDependencies": {"lab": "0.0.x", "complexity-report": "0.x.x"}, "dist": {"shasum": "7a8e4b36c4a76e7df8358ba73b7e09a6269d2436", "tarball": "https://registry.npmjs.org/joi/-/joi-0.2.6.tgz", "integrity": "sha512-C5oVF42zyEbBK3du/IH58BaZrwaIUdzr+d4n/WAxhsVTM14/GNDuFNAVCMd8cG++1XVHNGpR5mBCpgLo5ESBuw==", "signatures": [{"sig": "MEYCIQC3ZhcV0zOMWpIyq02KsbiUg+zZAR9ux+lty02NpJLObQIhAIelp/M1TqxQMIkxM4ADzz6Gsl0rC3hnOBqvA80aH6VU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "0.8.x"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "0.3.0": {"name": "joi", "version": "0.3.0", "dependencies": {"hoek": "0.8.x"}, "devDependencies": {"lab": "0.1.x", "complexity-report": "0.x.x"}, "dist": {"shasum": "f043ec8acb82e4242a1d8336989b039d5d092b2a", "tarball": "https://registry.npmjs.org/joi/-/joi-0.3.0.tgz", "integrity": "sha512-FVv0e3qUY/BLGaqUtuI5nWpntY8hf659KX8tvLG7k7xANz34NjpfFIoWCiCYTmEU5pM3f0W5uECvP4yNMf7wRw==", "signatures": [{"sig": "MEUCIB1X8LiTEy/PSx8IpwxPEnaT00o3cGpbl+UCXV8gfTWLAiEA3ehE0GvuPGrP9GLk4fX8kINV/cvXACwIGmXcdghgb+U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "0.10.x"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "0.3.1": {"name": "joi", "version": "0.3.1", "dependencies": {"hoek": "0.8.x"}, "devDependencies": {"lab": "0.1.x", "complexity-report": "0.x.x"}, "dist": {"shasum": "81620076902964181e97a2fb84621ed81544bb53", "tarball": "https://registry.npmjs.org/joi/-/joi-0.3.1.tgz", "integrity": "sha512-Fz6YIKI3IQI5m5coqopTnTm7wYb5adutj3+ceQjwttzCPVtvdibndAz7udAAVBecPUc5+K+dqCXDzq2VLkaMQw==", "signatures": [{"sig": "MEUCIQDl2s9xVdra+r/LaMuEIFGLn3BNfYxcYsra3B1EjZzK5gIgBxmTGUGKszCXaBpcrTmuK7gCHTGIV0i0oLhlsLi8jY0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "0.10.x"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "0.3.2": {"name": "joi", "version": "0.3.2", "dependencies": {"hoek": "0.8.x"}, "devDependencies": {"lab": "0.1.x", "complexity-report": "0.x.x"}, "dist": {"shasum": "0153e3f5375c4ce6dfcc23768b6daaa1eeec00d2", "tarball": "https://registry.npmjs.org/joi/-/joi-0.3.2.tgz", "integrity": "sha512-PHa5BUcfRF6JjiqC4P7Wrc83YqbvgjmafqDbSD4MiuBIECg4myVXy9h2ilZ5ha45EwOCnZBp3wvDoEZiPrSngw==", "signatures": [{"sig": "MEUCIHR1KiMeB1/gG2CXYOtg/5qjcFJCtZo60ENhT4SZTZ8BAiEAj8seczP+ikGcN4Juaos/vpQruv6ExHN0cr6voZBG//I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "0.10.x"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "0.3.3": {"name": "joi", "version": "0.3.3", "dependencies": {"hoek": "0.8.x"}, "devDependencies": {"lab": "0.1.x", "complexity-report": "0.x.x"}, "dist": {"shasum": "6d050a2db5c80ca2bc693ca32fb08504975b06e6", "tarball": "https://registry.npmjs.org/joi/-/joi-0.3.3.tgz", "integrity": "sha512-GgB46UGyvSgCUXFuy1cC5BGliuk1AV72iXBQe+6xXc6Ax84FlR/VubsLMj075AuGZ/1/CMl5mP0eebYdPubZkw==", "signatures": [{"sig": "MEUCIQCi6KpfhD4iLk6G5pTgAJkQGtqhL7H5ejB8LXgCNrKVMgIgIdNJHXCu9ZIDJnmXsKPx1SasFXXyCdQPBZbW9diP5rk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "0.10.x"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "0.3.4": {"name": "joi", "version": "0.3.4", "dependencies": {"hoek": "0.8.x"}, "devDependencies": {"lab": "0.1.x", "complexity-report": "0.x.x"}, "dist": {"shasum": "fe2d2378d5da04612daa2965eb3bc1c1374e4869", "tarball": "https://registry.npmjs.org/joi/-/joi-0.3.4.tgz", "integrity": "sha512-9ITZo9ow1mW6tS/Ufe7YXsPj+HLRfIwnYaldCXadTL7Zn64vQgdswaKB4tcmAiDLB9ea7YKwKoroD/Y2B0aIcQ==", "signatures": [{"sig": "MEUCIQCYok1rnwqDDIdWt/MgjHui4tURjVXRJCDH/HMStXTILQIgO8iDXaZb7k+vz050nQ8eAePFp3BuD/N/P8qMFt+cyuI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "0.10.x"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "0.3.5": {"name": "joi", "version": "0.3.5", "dependencies": {"hoek": "0.9.x"}, "devDependencies": {"lab": "0.x.x", "complexity-report": "0.x.x"}, "dist": {"shasum": "4a054d2aff19175fec6a78622160397923ed2c56", "tarball": "https://registry.npmjs.org/joi/-/joi-0.3.5.tgz", "integrity": "sha512-Nzac5sZQRglr4r4e4cTnfSpjsgDXPOazQUqjQLuDTjrFOfBVXWNSfDnmnOWDT1BcNEL/6JszlmuzdfZ2SEOQhw==", "signatures": [{"sig": "MEUCIQDGhdIshTKUY7jtLdeb8rWc2EYcl6x7+WOwREAMxSa9dAIgELPmDaYPD0Flqy0QYk9sfPqImFK5AdPyQrymtqLyqqo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "0.10.x"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "0.3.6": {"name": "joi", "version": "0.3.6", "dependencies": {"hoek": "0.9.x"}, "devDependencies": {"lab": "0.x.x"}, "dist": {"shasum": "6d3ea89f30648820124301cc029d22480dfb9f4e", "tarball": "https://registry.npmjs.org/joi/-/joi-0.3.6.tgz", "integrity": "sha512-7lKQsA/NfyRNwsnwd0hwxJxMsRqfrFfsMdltWzqX2AQPcSoy8gUENADgirDLEhfdNGcE79WXK9ysF1fepmulhA==", "signatures": [{"sig": "MEUCIQCwRY9R5nUY/0e6j2BbYkb9qHEfK+D9+o/FHoh8HHWqRQIgPxqszSXMT+KNMpT/XA4xb4d2o/wWFsASORO3YE+9g3E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "0.10.x"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "0.4.0": {"name": "joi", "version": "0.4.0", "dependencies": {"hoek": "0.9.x"}, "devDependencies": {"lab": "0.x.x"}, "dist": {"shasum": "64a3b4a51b9a41b0fbff689397b40fc18b7e45ab", "tarball": "https://registry.npmjs.org/joi/-/joi-0.4.0.tgz", "integrity": "sha512-OXiA215pZOyrJbF1/KtNd4aJHVkCImJ6dNOvdkha7VCe8/NMslGh5z8RYc1XI5VV5NwiAXbwRs9TqIF8pagSqA==", "signatures": [{"sig": "MEYCIQD6oM0vntKFtAplhu1YjcbXD+gdQU72cLC0YleKMGLywAIhAJ0NFMF018wVOHlevG1spVB2zXzdELj0r6xXk/Zq2tFo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "0.10.x"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "1.0.0": {"name": "joi", "version": "1.0.0", "dependencies": {"hoek": "0.9.x"}, "devDependencies": {"lab": "0.x.x"}, "dist": {"shasum": "47235a6c987b27770e26106f47eb79c5ee6bc796", "tarball": "https://registry.npmjs.org/joi/-/joi-1.0.0.tgz", "integrity": "sha512-3mtLIPyd4gYrweQXZ72zuQBYORY64+w8mNJt0MoPGIuTWgrNFnV/4OO3UEosHcXkrSek83M84KyRQIcdjnPzAw==", "signatures": [{"sig": "MEUCIQDygdfL1sZX4Q4ltPdrBEXN6mtED0HfCY/rHRn2+5PZsQIgVfvLHHRmJmWUNLQjqOdYBUxWekkn/KOmJ9jHfMPDH68=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "0.10.x"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "1.1.0": {"name": "joi", "version": "1.1.0", "dependencies": {"hoek": "1.0.x"}, "devDependencies": {"lab": "0.x.x"}, "dist": {"shasum": "464e985541295cb44c17d4c7424f002dd0e101e3", "tarball": "https://registry.npmjs.org/joi/-/joi-1.1.0.tgz", "integrity": "sha512-0ho7LcwBwqKKXgJpNvetuuhdj0W0GmwN9uslcgabQx0WRyse6gwmjpiga2X4zNjBkPNAukhnOgPEvgahp4szNQ==", "signatures": [{"sig": "MEUCIG1uaAx6id11WLBYn0EsQg3N9xVdPkD+fMCKxnm+oquMAiEAuY/PDi+GinuPhlpSXk918pw9I9RHpFYutR7Tx0kzmp8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "0.10.x"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "1.2.0": {"name": "joi", "version": "1.2.0", "dependencies": {"hoek": "1.0.x"}, "devDependencies": {"lab": "0.x.x"}, "dist": {"shasum": "313eb2a4c9a716d5b9b5145b6a4c73aa661c2c08", "tarball": "https://registry.npmjs.org/joi/-/joi-1.2.0.tgz", "integrity": "sha512-+CjWvaGG3NPNlkR2PpqboE67kUftuuBWnAxceMm2EsrCJs0UF5hK0ANwi5dlLGUxtOS1ivXBG/hpGzeDNezTbg==", "signatures": [{"sig": "MEYCIQCdVqh0xbDJB1+zf4rkCGGIgj+NyPb/+N0Z1oYU+sQZrwIhAJ6jIoLJUUaHGGDTtxGoCw/QX1TOMv5c69aCLw5v5jZ5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "0.10.x"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "1.2.1": {"name": "joi", "version": "1.2.1", "dependencies": {"hoek": "1.x.x"}, "devDependencies": {"lab": "1.x.x"}, "dist": {"shasum": "d4ada1b25459cdeddf47ce9205b610af3bd9d137", "tarball": "https://registry.npmjs.org/joi/-/joi-1.2.1.tgz", "integrity": "sha512-ifet8x4MSf8mJ+E73NnGIa4DRXARIvjU6LNaAhIfu/gDF27Jyhozw5hsdkVm9K4rbuuxvzEtYJFfwV7W1yCdkg==", "signatures": [{"sig": "MEUCIBLt9tJfUQu4EIZvknmSDMR4MSgLNcG8F8v9xyYE5dO6AiEAyPi/+2pzvI6jK8/tOAoQczDUJCsjC9MPMrkOxfdznYY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "0.10.x"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "1.2.2": {"name": "joi", "version": "1.2.2", "dependencies": {"hoek": "1.x.x"}, "devDependencies": {"lab": "1.x.x"}, "dist": {"shasum": "4825252045a2cfc959952819199ffd2c6b035be1", "tarball": "https://registry.npmjs.org/joi/-/joi-1.2.2.tgz", "integrity": "sha512-7izJ11Ec61FNZrZN8DqFwHyb50f49xSdhrCI0r1UEagf2z0KdNWaeMyFgw2fvW1EBzm9d9+bWwu17Rs2/U37zA==", "signatures": [{"sig": "MEUCIQDRiBY1UgSo2PytfGk8gqR2cO306t+mWowfsUA6S1cvxAIgZtiN87I9eZfGy9BUUAZyzCYNThH5QkCfZhllYDWvzVQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "0.10.x"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "1.2.3": {"name": "joi", "version": "1.2.3", "dependencies": {"hoek": "1.x.x"}, "devDependencies": {"lab": "1.x.x"}, "dist": {"shasum": "6161d1282514922c390f42ae7fbd46da2d774a55", "tarball": "https://registry.npmjs.org/joi/-/joi-1.2.3.tgz", "integrity": "sha512-GS+efaeVR/Pzkw/JbuiPERUM1TR3tH/CX2M2v1V022xv4XLg4p8pdJzppEPfVk0Wdl6NS7/BLohlLmXX4qrmiw==", "signatures": [{"sig": "MEUCIQDx+mJpe5JRZJPsngS7AkUXB6gbogi/Ve6IBrEOj3s3kgIgKvmknAxMsLrXtGRXskLOePlXsGYlJgO8Tbpq27kPi8E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "0.10.x"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "2.0.0": {"name": "joi", "version": "2.0.0", "dependencies": {"hoek": "1.x.x"}, "devDependencies": {"lab": "1.x.x"}, "dist": {"shasum": "64aaefb1ea384e9cadab3cd249d5ccfe1480c078", "tarball": "https://registry.npmjs.org/joi/-/joi-2.0.0.tgz", "integrity": "sha512-uOu5tnY4ED1Df/UnSoZDpQSI9WhRzE7eodZc7mJLoymT3jnueIYw6AMMR1rr8FIolsAWJIffC7VDvC4fnUZ7tg==", "signatures": [{"sig": "MEYCIQCPLMUHhAywi6Va8YC0cRr/xGglVypGzAG5y2PvrKqVKgIhAMcvfYbxbGyZ2Z2ONhBZO/DYLTIcjw5brcGl8mn8OirD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "0.10.x"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "2.0.1": {"name": "joi", "version": "2.0.1", "dependencies": {"hoek": "1.x.x"}, "devDependencies": {"lab": "1.x.x"}, "dist": {"shasum": "fdc9f14b472607fc3ba12775e227fbb6539aa407", "tarball": "https://registry.npmjs.org/joi/-/joi-2.0.1.tgz", "integrity": "sha512-xWYLQU+NUueT4T6LouRgGt1fa+rg3Z6Dytgv+PQ294hxWHFibWlgB6OUL2CvjoYSKXsC+SoedBH1+A/5LjuiRA==", "signatures": [{"sig": "MEYCIQDqL77aaYBllwu46Hd/QQknKguMMJ7F8GSbXSkq+rPqZwIhAMtpkjSSpVaM9jG8yw0CaKSMlE2KlKTyHMnzgrusEZez", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "0.10.x"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "2.0.2": {"name": "joi", "version": "2.0.2", "dependencies": {"hoek": "1.x.x"}, "devDependencies": {"lab": "1.x.x"}, "dist": {"shasum": "d0bd539ffc5a5bfa1c7e57e9e5cdccb6a8b01af4", "tarball": "https://registry.npmjs.org/joi/-/joi-2.0.2.tgz", "integrity": "sha512-l17JI0zzpL3sQI8rWG02erYaLq4AwHuvHp/YcyUcV96lkYwWD8pgbFQ0mk25mRjj/8phIeJZCtBVvLkme3QSrA==", "signatures": [{"sig": "MEUCIQCfZkiHrzBkTYHtFOkHZjO5tM47OuuQ6UC8Db4Bpan4CAIgeZaPadukv0V4hzF54uOgJFXgrFjgspf991fWD0C38nM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "0.10.x"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "2.0.3": {"name": "joi", "version": "2.0.3", "dependencies": {"hoek": "1.x.x"}, "devDependencies": {"lab": "1.x.x"}, "dist": {"shasum": "182645d989d2985e43cdde211588b51f9ff715cb", "tarball": "https://registry.npmjs.org/joi/-/joi-2.0.3.tgz", "integrity": "sha512-wwGMQrBTRNsRZik23oP/VLATWzepYdozPgvzRQMYV3PePa69feDpuVYs5g+DouCQimIXw0VlAg9iRfecJafYng==", "signatures": [{"sig": "MEYCIQDhAFqPLpl12XF+zFdJQ5Mwmg4xZx08TwbuOB+G/IATSAIhAMF8fIaAML8Fu3PpWMstnlHUvKJWUVolK8urX65t2zlt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "0.10.x"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "2.1.0": {"name": "joi", "version": "2.1.0", "dependencies": {"hoek": "1.x.x"}, "devDependencies": {"lab": "1.x.x"}, "dist": {"shasum": "7244670c83b03ffef24bb6519a0e2bb0414aa357", "tarball": "https://registry.npmjs.org/joi/-/joi-2.1.0.tgz", "integrity": "sha512-7k5ZYTPSOea7svcNUUYiDHeMyK1IsqPgqj08l+VcDJhoF3M6mclnQzddY1kp0IOxWBAfxAmR1twHShKFTXkDVQ==", "signatures": [{"sig": "MEQCIGM3GQvGYuO5yxpULMF6hNLR47b8Ic/pws+k+puWq/9FAiBjKPMHjOhZO0P85nVcMJJyjs4x8oGesteys0mkHU2y1w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "0.10.x"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "2.2.0": {"name": "joi", "version": "2.2.0", "dependencies": {"hoek": "1.x.x"}, "devDependencies": {"lab": "1.x.x"}, "dist": {"shasum": "73d84190c21437a09fb20a3db2f47305bd9b1128", "tarball": "https://registry.npmjs.org/joi/-/joi-2.2.0.tgz", "integrity": "sha512-eX6xhUI/AORH8PjygKkd7UD5y2HIXgnb9f8PeSiZAnWYLyU14hVaP/AN+sRip3cXpddMRRTgGyn8tKXNmjvRcw==", "signatures": [{"sig": "MEQCIGvUshaCgk1z4SYmZMzbas+1nJ6Y2nM0IxBQjQQ2jTPPAiA6EIS1AFZIYJy6+HXgrZClXprhJKdOilUuuf/PFZhNTA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "0.10.x"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "2.3.0": {"name": "joi", "version": "2.3.0", "dependencies": {"hoek": "1.x.x"}, "devDependencies": {"lab": "1.x.x"}, "dist": {"shasum": "872a54df20eaab828ad4c276f1044644131b2ad6", "tarball": "https://registry.npmjs.org/joi/-/joi-2.3.0.tgz", "integrity": "sha512-lgt/e0okV/1UGb8+Gny/EZyUIwUTEeGTp+q7BJNcy2Zg44y04nQm61urI4JqA6x5rJitOxyALzIFE0AYylaZUQ==", "signatures": [{"sig": "MEUCIEzc1NKc922uO5R736/Gugx/oIKykjgqZqdQSVXCb4fZAiEAwuTFtok80SEipT+VRbnMDDcXsTVP8aRG9OVv3W9KzzE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "0.10.x"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "2.3.1": {"name": "joi", "version": "2.3.1", "dependencies": {"hoek": "1.x.x"}, "devDependencies": {"lab": "1.x.x"}, "dist": {"shasum": "6818a189485a915900ad235afc06d459a14cf4a8", "tarball": "https://registry.npmjs.org/joi/-/joi-2.3.1.tgz", "integrity": "sha512-3eDZG5VoimnpBaR9TjDlx/O6Ne/qkpS7Qx5BzREXhqz6sGDIns+3X9cNDMN9CNGsSUMfOfea3yHG0d551sMi2w==", "signatures": [{"sig": "MEYCIQDdS//As3NEYrh7EFuMY+uZVFfnQ7OTh+W6gZ3YubC74QIhAMvKwHXVnLim5l7MMRi+VPsBpq119AbP68yURIXEOVT+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "0.10.x"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "2.4.1": {"name": "joi", "version": "2.4.1", "dependencies": {"hoek": "1.x.x"}, "devDependencies": {"lab": "1.x.x"}, "dist": {"shasum": "08c19993a2013cad929886b018c44905e753cd3b", "tarball": "https://registry.npmjs.org/joi/-/joi-2.4.1.tgz", "integrity": "sha512-i9APTUcp4JCGwEtZZdBoAFJJUUAh+KtR3OpYl3rpZHvu2tCIf9zi1/zC96G5meaQ9NBSf1catkteYHRc0YwmnA==", "signatures": [{"sig": "MEYCIQCLj+WF/BPplh7fSLM647TAcVQ+1+B/keUBUpFkyYZyiQIhAOemww/mvaKpRGHVjmt5MaUBlu9cI6xhVwOfoDP5sQky", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "0.10.x"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "2.4.2": {"name": "joi", "version": "2.4.2", "dependencies": {"hoek": "1.x.x"}, "devDependencies": {"lab": "1.x.x"}, "dist": {"shasum": "85a23981d24cc95c8c5fbdf929fe26f26f7e9e80", "tarball": "https://registry.npmjs.org/joi/-/joi-2.4.2.tgz", "integrity": "sha512-Ddxty30BeH3Vvxptngdi+ZB2u5eTAtZPT1v2aKkkoZ8ZgzhPdQSA9HZo1DuMEqCKyIt7XoEysvIm1Wh180oNxg==", "signatures": [{"sig": "MEUCIQD2nYxQ4rJDLb6/PKw3e1Evi7cMndXIQVOJqtV/oXBBgAIgSOKSi1+hO1+2zuYHtr5fstbpbe4lFr09qtOHC+kJtIk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "0.10.x"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "2.4.3": {"name": "joi", "version": "2.4.3", "dependencies": {"hoek": "1.x.x"}, "devDependencies": {"lab": "1.x.x"}, "dist": {"shasum": "7f9d40102d72485f0a28c3db7c3c40c6f6c075d1", "tarball": "https://registry.npmjs.org/joi/-/joi-2.4.3.tgz", "integrity": "sha512-bJkDqE0jiBT44NUtjQUBaCSAMRkVFH86T9d4QnHif6QfvW3SScbKUzE86IYLjIo/uFr0jqTJtq/PYETw/Q1TuQ==", "signatures": [{"sig": "MEYCIQDwk4gEeWy2ouAkxLQtw8OqCJipHaLLYiHGYiIdN7vD0wIhALm6X+x2QzLuSvFhurp9eGj36oUUvrY4WVfHRD5GEwNF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "0.10.x"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "2.5.0": {"name": "joi", "version": "2.5.0", "dependencies": {"hoek": "1.x.x"}, "devDependencies": {"lab": "1.x.x"}, "dist": {"shasum": "5affd1171c02b10c4fbec4f2ce44ea9c6f7d3519", "tarball": "https://registry.npmjs.org/joi/-/joi-2.5.0.tgz", "integrity": "sha512-tUmwE33FfzFcqKZtYc1b18xoDZwwD8voemJzIp+CLhSbbnM0vzxoiqZ1soqUfxIS9/zHOguwdOHkLE+0ULcyYQ==", "signatures": [{"sig": "MEQCIC0Fm9EVwYfCb/X/X0CZUSNOt8oYxhAUMiW4hXDX3bjbAiAosn+IxEA1KqBbsfJnKOBXe8BFTH6Sz/1v+Jq+vs7uiQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "0.10.x"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "2.6.0": {"name": "joi", "version": "2.6.0", "dependencies": {"hoek": "1.x.x"}, "devDependencies": {"lab": "1.x.x"}, "dist": {"shasum": "adda92b9d0e5598239a1d218c12ce3b9c830612b", "tarball": "https://registry.npmjs.org/joi/-/joi-2.6.0.tgz", "integrity": "sha512-s+R7gfRMV6zAcQeg299ILUyBL+wcPRtvx6HqsMMQSK4ocj+Gu1i22YUzv6VaoohMhbTHIOmlZeX4BX0cLwhHKw==", "signatures": [{"sig": "MEQCIFECqNXCGPwPGkAqLSBTJdT1/K6fsIpWtoZGDzPIoGVlAiAd8JB5WV72w3AevghpmmtL00d97mW1MLNYiovBfJpz1g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "0.10.x"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "2.7.0": {"name": "joi", "version": "2.7.0", "dependencies": {"hoek": "1.x.x"}, "devDependencies": {"lab": "1.x.x"}, "dist": {"shasum": "7270757e1cdcbb81a8480ed6059539455758fdf1", "tarball": "https://registry.npmjs.org/joi/-/joi-2.7.0.tgz", "integrity": "sha512-aLJ6HpGZSkNGX+l1qw7Wyzcn8R11njr02zLlhkKGzAnSi0kFToqfFfZvNFiRCvXsElSZedHL9gFvWbtHcTrrVA==", "signatures": [{"sig": "MEYCIQC6BlQdjHn4cuT5UKqsjPDEq1kA8sGvfOy1G+kJeNDz5gIhAJW44F9Ibf+NMFPg04H13fiLiCCNAPI2wLO1HKIDEULI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "0.10.x"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "2.8.0": {"name": "joi", "version": "2.8.0", "dependencies": {"hoek": "1.x.x"}, "devDependencies": {"lab": "1.x.x"}, "dist": {"shasum": "7284aadca6abd407e5aadce771b77f97fe7e4fc9", "tarball": "https://registry.npmjs.org/joi/-/joi-2.8.0.tgz", "integrity": "sha512-YiWJqMxjSj7hcMceaDuwqUF6ItzTExJ6Sy3/BtkChYa5Mk3580peUu84GqDgeBSjJHVGiq4oBVgv/VUZunkAKA==", "signatures": [{"sig": "MEYCIQD5qmTYqMKF0rb/7ExN5FiNkkeCp/+Ut23cNee+qCR1igIhAKAUhunm/zvd8q5qcxkcj/LWBgmaZ/Mh2XpH7+Vzqgi6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.22"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "2.9.0": {"name": "joi", "version": "2.9.0", "dependencies": {"hoek": "1.x.x"}, "devDependencies": {"lab": "1.x.x"}, "dist": {"shasum": "995e0a661c2a847308d5bb990264d439d2ca49d6", "tarball": "https://registry.npmjs.org/joi/-/joi-2.9.0.tgz", "integrity": "sha512-X1wCw+krgtLVmQnUlItVys/iGAnVO8FlWjWGOjkwA7b3UhSlzkS6g98rhZVx+TYVOpAGeYYuKpK9lO4QPhVIww==", "signatures": [{"sig": "MEQCIHZ9binc60yu+jFfVPlSQp9Vh9Te85ddTtZKIVGvecOxAiBcwUy6CM0yCowAZvz/3BHIkIEt8m2Jqu+8twgR2k7P/Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.22"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "3.0.0": {"name": "joi", "version": "3.0.0", "dependencies": {"hoek": "2.x.x"}, "devDependencies": {"lab": "3.x.x"}, "dist": {"shasum": "0bcd8961f4abebcc217b37bb31538b24553bffea", "tarball": "https://registry.npmjs.org/joi/-/joi-3.0.0.tgz", "integrity": "sha512-MLXkAM/HRNdxFQaDYXENVOhvWed573hXgtn/KnFFDhaA9NmDLOHoq2rBcNZl/cag5dxRbjui6GxRkm1OceVSkg==", "signatures": [{"sig": "MEUCIGKXTs4kUVTRlyE0LGOF96s48OJBGfxErt06wusY6msIAiEAgxBJhd3wTGzA5sD5Jpzu1ZFJgR1YhQDIC/Odk+2H71A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.22"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "3.1.0": {"name": "joi", "version": "3.1.0", "dependencies": {"hoek": "2.x.x"}, "devDependencies": {"lab": "3.x.x"}, "dist": {"shasum": "f7444d895a119fa87d78d3113c27055cdca9f875", "tarball": "https://registry.npmjs.org/joi/-/joi-3.1.0.tgz", "integrity": "sha512-TxAYKCRrOCwDtrlxddwyETGQt5+MQ9OsA0E91GnLupLNuAKK8VcIYfGw7PNkoSK9NCU+udBpwSk6MrHCKb+zxQ==", "signatures": [{"sig": "MEYCIQDQbXEviJ1YR45/Zb9UwyBL9UPVCadHHK+iVhtp4v55JQIhAJyHm3LrUgMjmZE4WlUhSqZzlZG6PEWLySLSxE0Cma/p", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.22"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "4.0.0": {"name": "joi", "version": "4.0.0", "dependencies": {"hoek": "^2.1.x", "topo": "1.x.x"}, "devDependencies": {"lab": "3.x.x"}, "dist": {"shasum": "c25945d4a3b0a15910ad7f08ebc5cef51014fd20", "tarball": "https://registry.npmjs.org/joi/-/joi-4.0.0.tgz", "integrity": "sha512-zhhp7ogbL1/0DhDX8BphZf5uPcaeeVc4wLFsQ2jCvsOlCSm/qz1egC7HAsAlI81IInzHTxupKGYEdRrgrCzFPQ==", "signatures": [{"sig": "MEYCIQC5CN83bUo6lAm5uMF+fKUQ7faSkXpsoZlq22loXcpotAIhAKR7WhaIWBTf32dz5H0Gj+ClsfYe9wj+gYViYmmjiuqY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.22"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "4.2.1": {"name": "joi", "version": "4.2.1", "dependencies": {"hoek": "^2.1.x", "topo": "1.x.x"}, "devDependencies": {"lab": "3.x.x"}, "dist": {"shasum": "1b3656f9392661ec03f9f146c7561dd29fb414f5", "tarball": "https://registry.npmjs.org/joi/-/joi-4.2.1.tgz", "integrity": "sha512-di21ib/7IggAOrSGIBoan/S1Azwc6F8BfHEGbBD5r3u5ss5HzupgLhx2Ix33gN1ssAdN9TcSaSO+Y8DuEPgCzg==", "signatures": [{"sig": "MEUCIFJjJk0dFUnPaSu5DCh+CRF8YSnJ2n7fiVHsxhe7OKZOAiEAl/zyEdDuR35bKP0R6Gk5dY6mXvtbav0D++jCIgCt7ZE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.22"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "4.3.0": {"name": "joi", "version": "4.3.0", "dependencies": {"hoek": "^2.1.x", "topo": "1.x.x", "isemail": "0.1.x"}, "devDependencies": {"lab": "3.x.x"}, "dist": {"shasum": "957c2246e1d94d6791085f03cd05bc35d2501450", "tarball": "https://registry.npmjs.org/joi/-/joi-4.3.0.tgz", "integrity": "sha512-tQILePz7Pn5UkZN9xfdkuXv1HuY57U1Rk+2BTdZhS23c755pS+Nyl8BB4McsfrWt2UoTdf+l0igIcmIpkN4gYg==", "signatures": [{"sig": "MEQCIFU88Ei7IqYuCxBHENk/leOVnDSgPQ5yoLMvkv48w48oAiA/flN36gHFL3nIOoeFZEG9dABzV3F0aPk9+DzVGO9wMg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.22"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "4.3.1": {"name": "joi", "version": "4.3.1", "dependencies": {"hoek": "^2.1.x", "topo": "1.x.x", "isemail": "0.1.x"}, "devDependencies": {"lab": "3.x.x"}, "dist": {"shasum": "c43d0d69931f15ff686e23666ebef7ce2d01266c", "tarball": "https://registry.npmjs.org/joi/-/joi-4.3.1.tgz", "integrity": "sha512-48a6B+8MywoPmzpazpSXmPpu00micFwj9I1oKdp2gyleH6sj2NQhJ+qvyl1RpdiqEEXdA+NuhMlxPq7+iwrEcA==", "signatures": [{"sig": "MEUCICt/4QKimyIY4i+39ib/Nsd2x4N68IcTRE0pi2TL3HynAiEA/BnzipSNtyK0+CcVEfVzBPJCOnw5waeJUSH9YPcC34c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.22"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "4.4.0": {"name": "joi", "version": "4.4.0", "dependencies": {"hoek": "^2.1.x", "topo": "1.x.x", "isemail": "0.1.x"}, "devDependencies": {"lab": "3.x.x"}, "dist": {"shasum": "7ecc3f0e13579b0e4c88f83d36ffd9c7bc712a11", "tarball": "https://registry.npmjs.org/joi/-/joi-4.4.0.tgz", "integrity": "sha512-uGtQ0moBDxV+qcJA9E0NBS5rsFDsxRIIe4PEQMdcG8lnm9J3dGBjyyr9H82JUdsj0e8IleROqGVwXAg2j6tQlw==", "signatures": [{"sig": "MEYCIQC4FGr6TVoKVbI8A+rRfoseEXktWW4tuFBOirpMw1JpBwIhAJp/f31Z05dFcSB0nued6DmuVN3BGiLdf19wDX8xwkHA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.22"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "4.5.0": {"name": "joi", "version": "4.5.0", "dependencies": {"hoek": "^2.2.x", "topo": "1.x.x", "isemail": "0.1.x"}, "devDependencies": {"lab": "3.x.x"}, "dist": {"shasum": "061e91a98910067b53cc830853f43a399544cfc8", "tarball": "https://registry.npmjs.org/joi/-/joi-4.5.0.tgz", "integrity": "sha512-iKvNDQgB6ANhV6ua/Sp8vvZjMzNeu+2dW9/dfdjQR14wkV26g2HCMt/v+HSDahvqthX54j+UoYdnWHz1dEwLGQ==", "signatures": [{"sig": "MEUCIQCIINl2pK+4M2UkDmzFer1m7M+Kmp9erJz3N0faLrywLgIgF3XHnz2iwCCtioVsmlEcjqVWQdQeMXmsd01XogMDeqY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.22"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "4.6.0": {"name": "joi", "version": "4.6.0", "dependencies": {"hoek": "^2.2.x", "topo": "1.x.x", "isemail": "1.x.x"}, "devDependencies": {"lab": "3.x.x"}, "dist": {"shasum": "60214a87d297a72543e35bd953e9203599539cf7", "tarball": "https://registry.npmjs.org/joi/-/joi-4.6.0.tgz", "integrity": "sha512-wXRZp6JzkHyuv3RrM6/+SQtxkz8ygfy2yTt5S9nOsErN+28dLvYllTsEvP65gmD9u9dQkgwp2n/TCkbK/dBwuw==", "signatures": [{"sig": "MEYCIQCkREIaxr3H4BIWJfLg/Rd/4FYHynvX9XCuP8aPKEgBGgIhAIDpMpdX4ioNEn75/dtH7CjBajCXOp1wROkRlHNTvp1e", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.22"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "4.6.1": {"name": "joi", "version": "4.6.1", "dependencies": {"hoek": "^2.2.x", "topo": "1.x.x", "isemail": "1.x.x"}, "devDependencies": {"lab": "3.x.x"}, "dist": {"shasum": "8a87e9424ee34ae808fcac3b524ef228565f2a81", "tarball": "https://registry.npmjs.org/joi/-/joi-4.6.1.tgz", "integrity": "sha512-xq4KYBLdVTpMQ/2zab1CU1qOhQOlRzWMywoSutI5rCIIHxWv3xAmUVPuDQMVVFmmT40n24QNIT25acm4AvkI1g==", "signatures": [{"sig": "MEUCIQDepwsBI2fFs0rgI0S13VGgUHAlK9AlXyvhtd+YqvMvKgIgBrb6ecbAn7HAJN96CjNZgZh0nt5O/OUrm5qOhoigQ8k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.22"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "4.6.2": {"name": "joi", "version": "4.6.2", "dependencies": {"hoek": "^2.2.x", "topo": "1.x.x", "isemail": "1.x.x"}, "devDependencies": {"lab": "3.x.x"}, "dist": {"shasum": "d99b77ac18ed9a62f4bed3ddf059b009c227bd24", "tarball": "https://registry.npmjs.org/joi/-/joi-4.6.2.tgz", "integrity": "sha512-BrmSKN7gRBXbZK3oGwTOxIZZpd7gAxxxrh33bRfhH3hzWAppjxNoxSLrt7LzSx7/jLwoLqEGahpZDkjDog9Xdg==", "signatures": [{"sig": "MEUCIQCrwQJVtkk+mdidv+qToAeImELXOp7QdfzYKepMz6N7+wIgOnTe32ZWorpX+F3texw/jm8LprWQp640JrVY3L6M2hk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.30"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "4.7.0": {"name": "joi", "version": "4.7.0", "dependencies": {"hoek": "^2.2.x", "topo": "1.x.x", "isemail": "1.x.x"}, "devDependencies": {"lab": "4.x.x"}, "dist": {"shasum": "a92592cb06b619d245391b09fbd52cb22274186a", "tarball": "https://registry.npmjs.org/joi/-/joi-4.7.0.tgz", "integrity": "sha512-UVSmiPEYDwEOKfBIvKIFxvBZvFneP09ZIHMBvSNI/Kk07rTvA/yCFhsXFM5w+mZPvCo6k1R/DE5G8PEW8CLEmA==", "signatures": [{"sig": "MEYCIQCEsEY94H+j1L9GV8skFUn2wGOKtO08aiY/h7URNaT61QIhAN3HlCnkT/UKMTt9GG/E0I+n6FAvWlyRBwr6lnzvZcEc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.30"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "4.8.0": {"name": "joi", "version": "4.8.0", "dependencies": {"hoek": "^2.2.x", "topo": "1.x.x", "moment": "2.x.x", "isemail": "1.x.x"}, "devDependencies": {"lab": "5.x.x", "code": "1.x.x"}, "dist": {"shasum": "08556bb58736c4847e4d486fd9ae4ee228184220", "tarball": "https://registry.npmjs.org/joi/-/joi-4.8.0.tgz", "integrity": "sha512-Ek7YINDh8JFb4KqIXbNu+nJyyo90ToaEMSSwRz8zFfZymc2deyCs1UXRSmPfRTRh3HqBe/JVoeY15OKk3C4lrQ==", "signatures": [{"sig": "MEQCIGPytpRLlu2wZnMPbCNyE+iW5H48lBCq9IwrDQX6fD6IAiBR6oV2fTdlTqewhSk5XUOy/1JCzLSkkwNxlditcRdPmg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.30"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "4.8.1": {"name": "joi", "version": "4.8.1", "dependencies": {"hoek": "^2.2.x", "topo": "1.x.x", "moment": "2.x.x", "isemail": "1.x.x"}, "devDependencies": {"lab": "5.x.x", "code": "1.x.x"}, "dist": {"shasum": "9f664a76952675c27ff0ddc75a239937587e64e2", "tarball": "https://registry.npmjs.org/joi/-/joi-4.8.1.tgz", "integrity": "sha512-VhB+RcSgPbTqWcMQMlEbtOWwaHuU54pnDMtdwy+sn5H+Bvahmba9sLPVgwYdf+94K/qLDFOqfH9z6YPB03K/0Q==", "signatures": [{"sig": "MEUCIH46b8/dq4YyQZvXmggWCghzfcU9AaIcS8VEfYmhKyFYAiEAmvfuRy4CbsC8klen9EEJvoMg8jV7rIn6BUGqSr+Uhmk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.30"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "4.9.0": {"name": "joi", "version": "4.9.0", "dependencies": {"hoek": "^2.2.x", "topo": "1.x.x", "moment": "2.x.x", "isemail": "1.x.x"}, "devDependencies": {"lab": "5.x.x", "code": "1.x.x"}, "dist": {"shasum": "2355023363089ca01bc1fd079e72949f977baada", "tarball": "https://registry.npmjs.org/joi/-/joi-4.9.0.tgz", "integrity": "sha512-7Vrzrght2JvfdbW/fQh3joGE+oWlAfUKUteZY3V8hDNnoHQI/w7fBrihVnbCY6wiO1RruCqIZDuEdUN6X8i3gQ==", "signatures": [{"sig": "MEYCIQDGSwdA4EcCAvFmKcT/yBVH82XSmCmcwbGNyT3fu00qywIhANy9Dkm0d2iGgugXMk+olL1TAss5XYc2H+U+g4WYzG1w", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.30"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "5.0.0": {"name": "joi", "version": "5.0.0", "dependencies": {"hoek": "^2.2.x", "topo": "1.x.x", "moment": "2.x.x", "isemail": "1.x.x"}, "devDependencies": {"lab": "5.x.x", "code": "1.x.x"}, "dist": {"shasum": "9099da57756ed2fea7ced2304e2c67639fc7c335", "tarball": "https://registry.npmjs.org/joi/-/joi-5.0.0.tgz", "integrity": "sha512-1ITEAaInh241V9Xt2l326lSoF5pdcpe7woOdLISRALpcGMmrIcL/G56QDF6FjNZWT7IHEgFAqnF2nybnGknAQA==", "signatures": [{"sig": "MEYCIQCI4rFj8Y8i+18vpxP0duhfcd78ovQATn0+okxg0fe2XgIhALotUDroKAxaLYiMWCl+g3/GUTK52STiY9HPfa3frCpf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.30"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "5.0.1": {"name": "joi", "version": "5.0.1", "dependencies": {"hoek": "^2.2.x", "topo": "1.x.x", "moment": "2.x.x", "isemail": "1.x.x"}, "devDependencies": {"lab": "5.x.x", "code": "1.x.x"}, "dist": {"shasum": "97b182e7d3642e74fe7d09cd838268848258c989", "tarball": "https://registry.npmjs.org/joi/-/joi-5.0.1.tgz", "integrity": "sha512-817RnDf8YJ8xtCjG0c80+kK6RBMgoggdv+mfEgWVhBqx2zIm5A+sVkDAxQ4A/Z62/mp4DbNN7SaFixPYBrawDA==", "signatures": [{"sig": "MEQCIA+HITzjrZZ3xEEu69hMjskuyAzuVFRZTKfkwVtYlSz5AiBODkwjvPdI9WUchit2Xvv++qyZDnCG3JBOpZiYri45GQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.30"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "5.0.2": {"name": "joi", "version": "5.0.2", "dependencies": {"hoek": "^2.2.x", "topo": "1.x.x", "moment": "2.x.x", "isemail": "1.x.x"}, "devDependencies": {"lab": "5.x.x", "code": "1.x.x"}, "dist": {"shasum": "14309761eae6f4d2d99b6b37886b30ce6d2e2c35", "tarball": "https://registry.npmjs.org/joi/-/joi-5.0.2.tgz", "integrity": "sha512-2A6EMZ/hKVovJ8Jy/2ncsiRCTrZEovXVE1F68R9RaoA/WykuHB/atm0DogoxRchzidoZaEFF0tXFlFywDMXKSw==", "signatures": [{"sig": "MEYCIQC92pkNJ7yEoyDgGy5p8kQxehq2PbUsu+li96QBVSaptAIhAK99YEQRIUbevez503fVRTzrudcz9MxKiYCQx9tJ56vJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.30"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "5.1.0": {"name": "joi", "version": "5.1.0", "dependencies": {"hoek": "^2.2.x", "topo": "1.x.x", "moment": "2.x.x", "isemail": "1.x.x"}, "devDependencies": {"lab": "5.x.x", "code": "1.x.x"}, "dist": {"shasum": "152ad07db8ee9c6401997ff5fd2c12896070bf58", "tarball": "https://registry.npmjs.org/joi/-/joi-5.1.0.tgz", "integrity": "sha512-9wCSMI07M0WgrN+HemBrcnXK24awpmo9T6b/miderN0oFjqJJFzkp5q3yYSw7r0JlhvlMsqS0wcgIDt7aj6E3A==", "signatures": [{"sig": "MEQCIGwDnBC3RVa+CG+g5Qbxp8e88vjcubFYj54VaHgK6Eb7AiBhUb91pKdWkNxJbtQ8CxqL5UvG9cHuI8Yf8jYiACZY5g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.30"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "6.0.0": {"name": "joi", "version": "6.0.0", "dependencies": {"hoek": "^2.2.x", "topo": "1.x.x", "moment": "2.x.x", "isemail": "1.x.x"}, "devDependencies": {"lab": "5.x.x", "code": "1.x.x", "markdown-toc": "^0.7.2"}, "dist": {"shasum": "6e52ea5021937c44e682515b6301f5eda3edddd5", "tarball": "https://registry.npmjs.org/joi/-/joi-6.0.0.tgz", "integrity": "sha512-KafxQtDvTIPlDTByhRjJvN4kk2LjsPQlDPU0WjhcitBI+M3QEbgW8bY50iyTFDq3E/2QiTdIWdMuSgBLfO9wWg==", "signatures": [{"sig": "MEUCIBlRva7hqj9llmq/yZ4eRj4iUQXUaoNsoODIDOtjdBZ9AiEApdIZgatq7wQoOtw5j8Z+Xz3Qix35nA8aKHlcSNp9J2o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.30"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "6.0.1": {"name": "joi", "version": "6.0.1", "dependencies": {"hoek": "^2.2.x", "topo": "1.x.x", "moment": "2.x.x", "isemail": "1.x.x"}, "devDependencies": {"lab": "5.x.x", "code": "1.x.x", "markdown-toc": "^0.7.2"}, "dist": {"shasum": "c358375f10541af60486752f1a90e0f5020d5c79", "tarball": "https://registry.npmjs.org/joi/-/joi-6.0.1.tgz", "integrity": "sha512-rYVid7MV5a8jr01jCHv4PgQPOpmh8Z+zAgIbe4qx9ZouSuGwQuWqjbtURranrnjOd/uay9X9I67e91wyTLk4jg==", "signatures": [{"sig": "MEQCIBxGw30dVe0LcRWUC6sZLZAgh3LTspKBCXQe7olUQ5cXAiAPLqqtWkCDhyM95tBlwJxFoIuyaflmRIbe7OIchrp7EA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.30"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "6.0.2": {"name": "joi", "version": "6.0.2", "dependencies": {"hoek": "^2.2.x", "topo": "1.x.x", "moment": "2.x.x", "isemail": "1.x.x"}, "devDependencies": {"lab": "5.x.x", "code": "1.x.x", "markdown-toc": "^0.7.2"}, "dist": {"shasum": "7c7043b3e8c5a6d31df3242fa9e625fe676cf05a", "tarball": "https://registry.npmjs.org/joi/-/joi-6.0.2.tgz", "integrity": "sha512-g1/VtMdwAUZgPG7lGCAHIt94gpxgcLbiBU24N2hBT1LoIYKoeUitDKWGmn5GVPSh3ssLO3kfEqMPfYbRW+O+lA==", "signatures": [{"sig": "MEUCIQCCFilMZmn6P0s+YhHCjpmh/kq7BMqhkHAOw5SvBnNEUQIgeC+VaaXLGi9RhXea4I8ZCblob7Jr/3IvVOVpYPTUA7I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.30"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "6.0.3": {"name": "joi", "version": "6.0.3", "dependencies": {"hoek": "^2.2.x", "topo": "1.x.x", "moment": "2.x.x", "isemail": "1.x.x"}, "devDependencies": {"lab": "5.x.x", "code": "1.x.x", "markdown-toc": "^0.7.2"}, "dist": {"shasum": "2cf5173cad5156ad27110a050e1cb8f6cbf62cee", "tarball": "https://registry.npmjs.org/joi/-/joi-6.0.3.tgz", "integrity": "sha512-nzsdqGoC7iC/NhDy8C1zVxV8iG7+KAviS4dezPh6InxsmR6uoEwvhWnobLvRCtDVlQrKTonCeRdfKAvlhPpMfw==", "signatures": [{"sig": "MEQCIGk6zoFf4svMJCSwFkNuWIfC2kFFNrxa8qtVODTFVt/qAiA9ZDfImMyKZG94uDXbTpkNLCyZOk0qzpf999RGfEA6Hg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.30"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "6.0.4": {"name": "joi", "version": "6.0.4", "dependencies": {"hoek": "^2.2.x", "topo": "1.x.x", "moment": "2.x.x", "isemail": "1.x.x"}, "devDependencies": {"lab": "5.x.x", "code": "1.x.x", "markdown-toc": "^0.8.1"}, "dist": {"shasum": "df7c08c3f6fd66d2604902cae8145ea245f0c85d", "tarball": "https://registry.npmjs.org/joi/-/joi-6.0.4.tgz", "integrity": "sha512-beNh8eUqbvofCUj3orcsN82ie0mmG7HjoJ+4Hequs/51t5XJCZfrjsLjFLc4FCQyj7C8V/KmyTfCjSfMWDyDMg==", "signatures": [{"sig": "MEQCIE4iV95nnPWMPgHMmelrcC9DPqOLnA/foUdF4apyQHtgAiBKM/tOS1JdU/GKmeT7TxBtPKkpsUPUEnfkPDQux63IkA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.30"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "6.0.5": {"name": "joi", "version": "6.0.5", "dependencies": {"hoek": "^2.2.x", "topo": "1.x.x", "moment": "2.x.x", "isemail": "1.x.x"}, "devDependencies": {"lab": "5.x.x", "code": "1.x.x", "markdown-toc": "^0.8.1"}, "dist": {"shasum": "51b4dde9c157b2f121b6e97e173863ca707a13fc", "tarball": "https://registry.npmjs.org/joi/-/joi-6.0.5.tgz", "integrity": "sha512-EGIlLFzM9E2C4mGVqTm9m3oKTbZL8+Merv9ZNCfMYuZHBqgQ16BArpcSMXhm4l82sEywDRx2LEbv9fdvtZEbGQ==", "signatures": [{"sig": "MEUCID1j0zhu7cftEOQMqv2Rujq6YkbWysBny7mxfkFL9YIoAiEAucj0VQDdQ+2RPLAaJUwWhtROOVf+RG46qQ1zaww7Cw4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.30"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "6.0.6": {"name": "joi", "version": "6.0.6", "dependencies": {"hoek": "^2.2.x", "topo": "1.x.x", "moment": "2.x.x", "isemail": "1.x.x"}, "devDependencies": {"lab": "5.x.x", "code": "1.x.x", "markdown-toc": "^0.8.1"}, "dist": {"shasum": "fc448a19713f2a4ee39f92aee5872512be7f265a", "tarball": "https://registry.npmjs.org/joi/-/joi-6.0.6.tgz", "integrity": "sha512-mCXHdvRncoQFayooknp2sPrx7Ve08hqkqUbTJZ8Byg3ThWl+EGbmkHJgUOP62H1YIE1QHbEbJtWnoFwOTABjBA==", "signatures": [{"sig": "MEUCIQDO/iet/UsjxmqMJvcNNCmkkqMG6pAAKkttlokwid7iigIgVqeb+LqCoN18KTj2vaAgUp+/L2icyrqxxJ7dAGjLZ3I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.30"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "6.0.7": {"name": "joi", "version": "6.0.7", "dependencies": {"hoek": "^2.2.x", "topo": "1.x.x", "moment": "2.x.x", "isemail": "1.x.x"}, "devDependencies": {"lab": "5.x.x", "code": "1.x.x", "markdown-toc": "^0.8.1"}, "dist": {"shasum": "af4586485619b812ada3f3122d6fabff5c421a50", "tarball": "https://registry.npmjs.org/joi/-/joi-6.0.7.tgz", "integrity": "sha512-LZ+Nw4NdY/KFVvnziyjJUc8a/cuSiw9msOl0SMm7afciJWkMpNaoI5lCpSTfocE6UU2ihMjYAh3N4U4XTxtdMQ==", "signatures": [{"sig": "MEYCIQCeqlOUxJZDuOaXKCkL9iy2Zd7uXJytiV/X21ilFMN/ZQIhAICvbzD1DChW8+Dsy53jji7nsSsQ5TZNj5RKDASx6KyJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.30"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "6.0.8": {"name": "joi", "version": "6.0.8", "dependencies": {"hoek": "^2.2.x", "topo": "1.x.x", "moment": "2.x.x", "isemail": "1.x.x"}, "devDependencies": {"lab": "5.x.x", "code": "1.x.x", "markdown-toc": "^0.8.1"}, "dist": {"shasum": "c5e9e3d844f61a4a8d8a24382293d6df1d083be1", "tarball": "https://registry.npmjs.org/joi/-/joi-6.0.8.tgz", "integrity": "sha512-JO15RwqrcNqlzX6cABcND8W1PyoQjm/y2iMw8gGsIjemyZ7C4MWnFhZnbiJk546oo9H+qe31txmmaN0Nnm0R7A==", "signatures": [{"sig": "MEYCIQDTRTr/UkCI3gsvW1t3WXT83872ewmRcVMI9EpXXGky+gIhAOI5xddLAZZFba3MY5K2YA1Dkrvon8UZh5iUxZcvyL9t", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.30"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "6.1.0": {"name": "joi", "version": "6.1.0", "dependencies": {"hoek": "^2.2.x", "topo": "1.x.x", "moment": "2.x.x", "isemail": "1.x.x"}, "devDependencies": {"lab": "5.x.x", "code": "1.x.x", "markdown-toc": "^0.11.0"}, "dist": {"shasum": "4141230a69866d12059c0a3ebe85b09ff8246779", "tarball": "https://registry.npmjs.org/joi/-/joi-6.1.0.tgz", "integrity": "sha512-rBHTw021fAv7oGzHW1m2l5ITfYIXaBs+t4KAKJ2rGS/ptE0O+yes9PYEBXvVhT+uQV9DQ2zU9KVfWwkfiY0frQ==", "signatures": [{"sig": "MEUCICJm46PaZlwwTA/QUoumGFg+p8/EQqcBbe7KJx/GuPQ7AiEA67BhIgLYBk2ftdGZL+uALpPNHDpdX2Ekxa+1MZpe4iI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.30"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "6.1.1": {"name": "joi", "version": "6.1.1", "dependencies": {"hoek": "^2.2.x", "topo": "1.x.x", "moment": "2.x.x", "isemail": "1.x.x"}, "devDependencies": {"lab": "5.x.x", "code": "1.x.x", "markdown-toc": "^0.11.0"}, "dist": {"shasum": "0142361d0ab8fbc2705b9f9fd17d1d5af5a53e22", "tarball": "https://registry.npmjs.org/joi/-/joi-6.1.1.tgz", "integrity": "sha512-52JfsEZyfSQyzAALEaX4forcrbhNBJusnrJu5zAQiCCZ5al1gcJa+h3lh9/3mOa86RoVUGPyUArI3LJ2Fjxtpg==", "signatures": [{"sig": "MEYCIQDBOA0jNhScarxtziYx/Z/LAHVGpeQK5Uiw3C1yCMxIKgIhAJ1aq6znoJmLejviM8KMxeYuB+hqBAdhhkmyKK232UXB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.30"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "6.1.2": {"name": "joi", "version": "6.1.2", "dependencies": {"hoek": "^2.2.x", "topo": "1.x.x", "moment": "2.x.x", "isemail": "1.x.x"}, "devDependencies": {"lab": "5.x.x", "code": "1.x.x", "markdown-toc": "^0.11.0"}, "dist": {"shasum": "98e48ba983665b736063f0b6c5139f83db953630", "tarball": "https://registry.npmjs.org/joi/-/joi-6.1.2.tgz", "integrity": "sha512-Wm7OfbN3aIn6pkYtRv6sP2+2hrGe1a5iVqsHFjVi2c6IYCz3+8GXSoqtLlHLVNTTJj6UOadm3hUIWskTIY2CYw==", "signatures": [{"sig": "MEYCIQClj9DtiNdfOSTioKKZhGMtKXRH2MRLEcPXS+pUpojLbAIhANJZs22QrtWSIn/VtmubPca9E/XDNHkEPEUVqb1OYeXg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.30"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "6.2.0": {"name": "joi", "version": "6.2.0", "dependencies": {"hoek": "^2.2.x", "topo": "1.x.x", "moment": "2.x.x", "isemail": "1.x.x"}, "devDependencies": {"lab": "5.x.x", "code": "1.x.x", "markdown-toc": "^0.11.0"}, "dist": {"shasum": "0d9dabe8c47084597611edd0cdee3c5bcbe8d5da", "tarball": "https://registry.npmjs.org/joi/-/joi-6.2.0.tgz", "integrity": "sha512-j55D7JJY3qgWaAgCj6eLkM4eFtzZJPmdK2/XskwRbfxXGg0X3Gsfo69Mel0WIBbAeQm3hQrkGSC978l8JKRRrg==", "signatures": [{"sig": "MEQCIFXhgk6uQVn4dCqSiQhmKlsq9hcfAK5YiyeLPwOZLefZAiBw85ohOrp0RZ+Yz7axyO9A4XdSztUkLL5uGh4oBct3dA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.30"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "6.3.0": {"name": "joi", "version": "6.3.0", "dependencies": {"hoek": "^2.2.x", "topo": "1.x.x", "moment": "2.x.x", "isemail": "1.x.x"}, "devDependencies": {"lab": "5.x.x", "code": "1.x.x", "markdown-toc": "^0.11.0"}, "dist": {"shasum": "12ebbcf2711c404ed176be4e86528f8d7967add9", "tarball": "https://registry.npmjs.org/joi/-/joi-6.3.0.tgz", "integrity": "sha512-sF7ZBQifZ6OsswiURM5zhrtnZdlbfFLGW7SEYX0reLLuwNbq3vOVukz/tmnfaR3Py/DyB1kcTqMZ9k5XkprotA==", "signatures": [{"sig": "MEUCIQCiVWx6gUCYKwO059BVtaPRRF6hAeXDtb1TB+132MrS7wIgZiPFrc5HSMfaH+o0dYqwFAf44LsKuPikKkjzViHcNBE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.30"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "6.4.0": {"name": "joi", "version": "6.4.0", "dependencies": {"hoek": "^2.2.x", "topo": "1.x.x", "moment": "2.x.x", "isemail": "1.x.x"}, "devDependencies": {"lab": "5.x.x", "code": "1.x.x", "markdown-toc": "^0.11.0"}, "dist": {"shasum": "6d09b81fef9b881df9ca069d908c10fe0b6d674a", "tarball": "https://registry.npmjs.org/joi/-/joi-6.4.0.tgz", "integrity": "sha512-3GrunvjxcLSa9lwxiC7pJGEwikgjxpkHl6kBgc60t4bowEtpJIZUwEdl1J1QPWqfDTpKIyLsvcl99CtQDufcDA==", "signatures": [{"sig": "MEUCIQD0/J4UbQbzPYUvPP9vwXd4jfa/dSGiSzJw4gvqlQSjZAIgD3L3tj7THL1TRjk9ILAEv3t0GQekUggIQtjDvTY5rZQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.30"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "6.4.1": {"name": "joi", "version": "6.4.1", "dependencies": {"hoek": "^2.2.x", "topo": "1.x.x", "moment": "2.x.x", "isemail": "1.x.x"}, "devDependencies": {"lab": "5.x.x", "code": "1.x.x", "markdown-toc": "^0.11.0"}, "dist": {"shasum": "f50f42453560068e6383da15ac2d30dd7ceb6b6e", "tarball": "https://registry.npmjs.org/joi/-/joi-6.4.1.tgz", "integrity": "sha512-<PERSON><PERSON><PERSON>ughohCPLs127n2okpjqyyOgxeYNdybX1eKW+7qpgapszz0LxnGW+TrW8KcQxHRMqOAI3PirUhWrAt2P6w/w==", "signatures": [{"sig": "MEQCIGaCJtSNsG7IpuJ3bLJBIaly5hn9WMckWXNuQmmZiFF1AiBEi0YvYjAIVwd1yJ7ABM1C4nEoc+UFeyYXUrsHMj6MZQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.30"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "6.4.2": {"name": "joi", "version": "6.4.2", "dependencies": {"hoek": "^2.2.x", "topo": "1.x.x", "moment": "2.x.x", "isemail": "1.x.x"}, "devDependencies": {"lab": "5.x.x", "code": "1.x.x", "markdown-toc": "^0.11.0"}, "dist": {"shasum": "7812a31e0d0427437f19b4d6297cf106b72a1505", "tarball": "https://registry.npmjs.org/joi/-/joi-6.4.2.tgz", "integrity": "sha512-4ubfa7AxT9Ei7HhYAEcwhw5aCOjTVFeaafGW1whOUqV1HBGxl2WWYGCBIVxVMJRPtrE76n+oL/i4F6xdOd4QPw==", "signatures": [{"sig": "MEYCIQD9CkowQ+7eje0l5CLFS4AAA71cKglwL3Ne/K/bmFXiCQIhANkANAiwqdpMitCtX2M/jv3LlVLlhnyDbXVqOsULsKWO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.30"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "6.4.3": {"name": "joi", "version": "6.4.3", "dependencies": {"hoek": "^2.2.x", "topo": "1.x.x", "moment": "2.x.x", "isemail": "1.x.x"}, "devDependencies": {"lab": "5.x.x", "code": "1.x.x", "markdown-toc": "^0.11.0"}, "dist": {"shasum": "340176729c65cf11ef063306be7e726a8e38c665", "tarball": "https://registry.npmjs.org/joi/-/joi-6.4.3.tgz", "integrity": "sha512-Z4rnLrAtf23cOX/PK/6WKAItT33BFOMpPRIlQd65fZ3evliUVfEU/K6OHzsZ9mcQtKvR5XN4BKSpHoSa2gFwdA==", "signatures": [{"sig": "MEUCIQCHZhweYK0qw91EgraKLU9DFwo67q3os0MetfZPZ717zAIgRAKAFWD225ftr4jsyrs6FA/DFuhRd2lZ/T9QpIkHypY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": "2.x", "node": ">=0.10.30"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "6.5.0": {"name": "joi", "version": "6.5.0", "dependencies": {"hoek": "^2.2.x", "topo": "1.x.x", "moment": "2.x.x", "isemail": "1.x.x"}, "devDependencies": {"lab": "5.x.x", "code": "1.x.x", "markdown-toc": "^0.11.0"}, "dist": {"shasum": "dcef2643e6e0ded0e680e658912af9658d6ec333", "tarball": "https://registry.npmjs.org/joi/-/joi-6.5.0.tgz", "integrity": "sha512-AOatytc80Jw3nLRW0ROms8FGf82ru77bpKrGafVQaUJGBNRcL3TuFZb38+OiPey5nMcjeBJkJ7w3b+aIh47KkQ==", "signatures": [{"sig": "MEYCIQCfYVew+WPBcZZ35xUHip4slIYr6Kb6uurw7zMnLY2xuwIhAPwQFO8P75LV4ovGJXPxz5RydEc3BURvhr6FzMiIlG0r", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": "2.x", "node": ">=0.10.30"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "6.6.0": {"name": "joi", "version": "6.6.0", "dependencies": {"hoek": "^2.2.x", "topo": "1.x.x", "moment": "2.x.x", "isemail": "1.x.x"}, "devDependencies": {"lab": "5.x.x", "code": "1.x.x", "markdown-toc": "^0.11.0"}, "dist": {"shasum": "f7181ff3d8769e9972cd988c3c2cf5ceeb270f40", "tarball": "https://registry.npmjs.org/joi/-/joi-6.6.0.tgz", "integrity": "sha512-xCNjAREZ5/aVBnY5AgtsAEAN4snkpfHv0IX3C2gzNw+6U/vCHwhWYlz9/Xe5yINW15AGX3MSFOiq3hRDUDVmpA==", "signatures": [{"sig": "MEUCIDRpgjCMc1asHLkb7rAMIzuZkBDwUPg7JLpvzGjxy6KyAiEApk61ewA5PPQJ83UouTATdJ3hNiTab9PkvPAW5ZBYdTU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": "2.x", "node": ">=0.10.30"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "6.6.1": {"name": "joi", "version": "6.6.1", "dependencies": {"hoek": "^2.2.x", "topo": "1.x.x", "moment": "2.x.x", "isemail": "1.x.x"}, "devDependencies": {"lab": "5.x.x", "code": "1.x.x", "markdown-toc": "^0.11.0"}, "dist": {"shasum": "3574766861426f8c4119bf6a80b53d0b489bf507", "tarball": "https://registry.npmjs.org/joi/-/joi-6.6.1.tgz", "integrity": "sha512-XZb9yYqAdGeh5X/tWfAU3E5LSEXX8HgxAB+xNjSsmU9udiPiyLL4eeS8WFMVmpmWNCGrHkFlwUaw/+aDOIEsZA==", "signatures": [{"sig": "MEQCIBcEJaFE660ftmlEaC61QgfXgmM/ronWcTj/6pEnN7/4AiBxABOAXNUU2ZfIQiLJUByhFdUeB3q5vvnQWY8pL82M3w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": "2.x", "node": ">=0.10.30"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "6.7.0": {"name": "joi", "version": "6.7.0", "dependencies": {"hoek": "^2.2.x", "topo": "1.x.x", "moment": "2.x.x", "isemail": "1.x.x"}, "devDependencies": {"lab": "5.x.x", "code": "1.x.x", "markdown-toc": "^0.11.0"}, "dist": {"shasum": "b6c50904f0fc21c13fb8ff49aee19157038f1937", "tarball": "https://registry.npmjs.org/joi/-/joi-6.7.0.tgz", "integrity": "sha512-AXnfmc5BtKG1cCH6EqmfAeu5J1Fbwhdpq5mpeLpAQ2aV9DB8PtdE+zctcCnQsH1TSfzWnH9jqAZyrKmSXFLtNQ==", "signatures": [{"sig": "MEYCIQCtrWxFYkbTm4UE/4CpFDPrYWyISVgJvIcSr3bvfoTJrgIhAMmlp05Wf2CA5PL1dU7elwzRWx1RXjK9mmHeTUhfGnPA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=2.0.0", "node": ">=0.10.40"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "6.7.1": {"name": "joi", "version": "6.7.1", "dependencies": {"hoek": "^2.2.x", "topo": "1.x.x", "moment": "2.x.x", "isemail": "1.x.x"}, "devDependencies": {"lab": "5.x.x", "code": "1.x.x", "markdown-toc": "^0.11.0"}, "dist": {"shasum": "729180681e705d4a0f28c41d7dcb900b9f45e92d", "tarball": "https://registry.npmjs.org/joi/-/joi-6.7.1.tgz", "integrity": "sha512-uAXmqUvd+QOCb03PoQi+xk1eDsMeToIBdCKngEU0AT+spIIiMSdIRplm14VMRGIHvhUkDPODAIrMQqSyhhsoVA==", "signatures": [{"sig": "MEUCIQDeVR7tebRRbIvAT815CubLVDn6/FjY1PW3IatCC5MTKwIgGrpM6nNHl1dMBU6aIqGlNlrEkSunXGc+mGqHoReiaFE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=2.0.0", "node": ">=0.10.40"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "6.8.1": {"name": "joi", "version": "6.8.1", "dependencies": {"hoek": "2.x.x", "topo": "1.x.x", "moment": "2.x.x", "isemail": "1.x.x"}, "devDependencies": {"lab": "6.x.x", "code": "1.x.x", "markdown-toc": "0.11.x"}, "dist": {"shasum": "83de2d6028fc0cd5d1622f3f5747ca082b478eff", "tarball": "https://registry.npmjs.org/joi/-/joi-6.8.1.tgz", "integrity": "sha512-+U+18sF6PmrYTMCn4AnUxoNfoFngbugIsSYsy8Cnsls5P3As316w973NzVyJpRS81ikPUTQXj9yz2rC63ZWqYA==", "signatures": [{"sig": "MEUCIQDI6pFRgKY9eugDzdU/LUUC64GyYgTFsbUCy1zaD+jTyAIgEzPogdAMGzLkmJF+2NFnfawarSi7JT16l0Oe2tC9KNQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=2.0.0", "node": ">=0.10.40"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "6.9.0": {"name": "joi", "version": "6.9.0", "dependencies": {"hoek": "2.x.x", "topo": "1.x.x", "moment": "2.x.x", "isemail": "1.x.x"}, "devDependencies": {"lab": "6.x.x", "code": "1.x.x", "markdown-toc": "0.11.x"}, "dist": {"shasum": "913a1647d512c23d3c26bcc794795d28c93b7fc8", "tarball": "https://registry.npmjs.org/joi/-/joi-6.9.0.tgz", "integrity": "sha512-zhTWOYymYz2UR/2ly/9yiGCvXcG8gMAgYWzf1y+a9c9z8UtT7y9nWCWcWxwl65YrQZUNnH/iAPq96WlAMxSLeQ==", "signatures": [{"sig": "MEUCIQCGL7wGCzy7JdftQP88ISIj54K+3Mutu8KIdv+CMwr/8wIgCHCysRp1oDmHWJjomk63ptWV19muop6+F295c324frQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=2.0.0", "node": ">=0.10.40"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "6.9.1": {"name": "joi", "version": "6.9.1", "dependencies": {"hoek": "2.x.x", "topo": "1.x.x", "moment": "2.x.x", "isemail": "1.x.x"}, "devDependencies": {"lab": "6.x.x", "code": "1.x.x", "markdown-toc": "0.11.x"}, "dist": {"shasum": "7ec16664a18a68f73f91810dbdb483dadf26a95c", "tarball": "https://registry.npmjs.org/joi/-/joi-6.9.1.tgz", "integrity": "sha512-eKHV+0EMSWIHJOudCu6jP3kuL9PgUlfk5u8owERjTNUlqvksahJGsuv1UYjmdANogsQ9aG7yPsnvflQAlOjMkw==", "signatures": [{"sig": "MEQCIADptpK+v+btkBNB/koZa0wmn6/JEB6zMlGcXMEZmIGqAiAzSQbuN+KNSVvPH9ZjO6ioExwoBUTAqIrygBKjvBV3aA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=2.0.0", "node": ">=0.10.40"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "6.10.0": {"name": "joi", "version": "6.10.0", "dependencies": {"hoek": "2.x.x", "topo": "1.x.x", "moment": "2.x.x", "isemail": "1.x.x"}, "devDependencies": {"lab": "6.x.x", "code": "1.x.x", "markdown-toc": "0.11.x"}, "dist": {"shasum": "4e7a99d6e0b3f783ccb4120e1f0f5f9d5dba6ff3", "tarball": "https://registry.npmjs.org/joi/-/joi-6.10.0.tgz", "integrity": "sha512-/x0NLH2jZ8LGwUcS2LrOj5l8X5seXa9sEXPIZCmEuRwyDim1iDMFj6n8Lntq6CMdhQQlBJfX0ypeuN6RlsqgyA==", "signatures": [{"sig": "MEUCIBsxNtChwfVKqV8frLioRyDeToGU9GfiZstvhxpSz9q1AiEArLxFrsXxnaH432j6xqOFKiroI+1SMDhdnToFOLrdofs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=2.0.0", "node": ">=0.10.40"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "7.0.0": {"name": "joi", "version": "7.0.0", "dependencies": {"hoek": "3.x.x", "topo": "2.x.x", "moment": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "7.x.x", "code": "2.x.x", "markdown-toc": "0.11.x"}, "dist": {"shasum": "7f0a5e56c0818aabd7a58366a791a3818c0b63df", "tarball": "https://registry.npmjs.org/joi/-/joi-7.0.0.tgz", "integrity": "sha512-BCrATHdO47Ls0JSNkKPN1qGb3bVcvac2xC8LeO818iXzTc6pzLFIWZLrDNCgzl6+Juu3BD/ySpcNjPwNMMW57A==", "signatures": [{"sig": "MEQCIF18mwQ+9XiLNB2yonVSrHdrprY7Ot9taNWvOeIsfc01AiB+iQIJpcGrPnQvmA4dtorzL0Yss8vzeJ6b1Cf30LXazw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "6.10.1": {"name": "joi", "version": "6.10.1", "dependencies": {"hoek": "2.x.x", "topo": "1.x.x", "moment": "2.x.x", "isemail": "1.x.x"}, "devDependencies": {"lab": "6.x.x", "code": "1.x.x", "markdown-toc": "0.11.x"}, "dist": {"shasum": "4d50c318079122000fe5f16af1ff8e1917b77e06", "tarball": "https://registry.npmjs.org/joi/-/joi-6.10.1.tgz", "integrity": "sha512-K6+OwGaWM1sBEu+XMbgC4zDmg6hnddS2DWiCVtjnhkcrzv+ejSfh7HGUsoxmWQkv6kHEsVFAywttfkpmIE2QwQ==", "signatures": [{"sig": "MEQCIHt2o+ZB+U8u/LxgqKXavLdtF/duSkhds6t6uRwfdmXTAiBmpMl8RLAgdzUtt/S6XQYN2QdNlWXser7WaSe6wjKBeg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=2.0.0", "node": ">=0.10.40"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "7.0.1": {"name": "joi", "version": "7.0.1", "dependencies": {"hoek": "3.x.x", "topo": "2.x.x", "moment": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "7.x.x", "code": "2.x.x", "markdown-toc": "0.11.x"}, "dist": {"shasum": "66a87a918b5dd41670987e00cabee9499260f8ad", "tarball": "https://registry.npmjs.org/joi/-/joi-7.0.1.tgz", "integrity": "sha512-rTltSWSDoxP+ZDMBOmoiIMRRQAfZNRhxynPQNVcg5oGF37L0QwnxiPZy1m10DkPcZdVNmJG4d0EbH7y+fLxyzg==", "signatures": [{"sig": "MEYCIQCQsjZPi3y4k+v+uDPrX1Vq0KUWn7pwrKE0Z/zIgmaSPQIhAMKMvxH1+fJS0WIN/54cplm5yraUSoXB8UUHMwSkl2x4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "7.1.0": {"name": "joi", "version": "7.1.0", "dependencies": {"hoek": "3.x.x", "topo": "2.x.x", "moment": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "8.x.x", "code": "2.x.x", "markdown-toc": "0.12.x"}, "dist": {"shasum": "db5ce95fc509f5c148ff9058ea639c934945d93d", "tarball": "https://registry.npmjs.org/joi/-/joi-7.1.0.tgz", "integrity": "sha512-RvXQ5fgfd2zLPyGE2CKinUdZ17jAvPeCAtx7NoHzrB3ERdY7cMOj/xh2M87uyPGaZwTDSaFKieEYH8QbjONTtg==", "signatures": [{"sig": "MEUCIQC8HI1Es4EX73nlmFvPsTOuMcNoPw2zuisMopnwNmkwcAIgciMTt5VRh4WI/yYIimsjo7kaeobChFKWVNXd4UzkskQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "7.2.0": {"name": "joi", "version": "7.2.0", "dependencies": {"hoek": "3.x.x", "topo": "2.x.x", "moment": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "8.x.x", "code": "2.x.x", "markdown-toc": "0.12.x"}, "dist": {"shasum": "9c6f3c3f50e99e8285b6b624cfd73ad75f54a7e3", "tarball": "https://registry.npmjs.org/joi/-/joi-7.2.0.tgz", "integrity": "sha512-t+jVVhdB5+iQkm5Yq5sgKtXtPLBO0pkeUs6V3Oy2hHhBexS9YmjZwHWN/qvxdgu8Wlj74vq/nUJ15E+mvhF/xw==", "signatures": [{"sig": "MEUCIAUSyka9yad2D5hxU599bZY9EPrMBlXfD4ymTJh6PEJzAiEAo0RxO0fSPXqH5MBTdsPUCer0rIo30UlpzDPdZRELqN4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "7.2.1": {"name": "joi", "version": "7.2.1", "dependencies": {"hoek": "3.x.x", "topo": "2.x.x", "moment": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "8.x.x", "code": "2.x.x", "markdown-toc": "0.12.x"}, "dist": {"shasum": "8377cf5f899dee7db47add16d5327b0773dfb083", "tarball": "https://registry.npmjs.org/joi/-/joi-7.2.1.tgz", "integrity": "sha512-WnkvXvmMURwHCAzrUzLrpcp+rYGkvrRPU+yah0W3VBz+kaD7oexnM9yRdqQpywfEj6T1lxAFXepnuFDPbtZsyw==", "signatures": [{"sig": "MEUCIHwAVthW8Y8usHIOaYX61TK3IH/3aeUYdVMmpW5PayMJAiEAmuWdHS32xLsSLAe2KGghF0wfqYQB8+JPlYTfRZToNCM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "7.2.2": {"name": "joi", "version": "7.2.2", "dependencies": {"hoek": "3.x.x", "topo": "2.x.x", "moment": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "8.x.x", "code": "2.x.x", "markdown-toc": "0.12.x"}, "dist": {"shasum": "a995b2ddcbbf65fcafb3ad705b67d649a6dd803e", "tarball": "https://registry.npmjs.org/joi/-/joi-7.2.2.tgz", "integrity": "sha512-xIf9eNAEWadI347Y7DgfkPWMUN30SBEhLC4mDelEaKbJNmuK9WYuqqbfDfEED33pS02ntcv50HhQ4Cj16Nn5ZQ==", "signatures": [{"sig": "MEYCIQC5zwY6zzGzh/CwKxsP4Wo9olkFEAnU7kL27nr8aJCJCwIhAMKudVOqpgsi6kKZJtTXe04DA/KPIhVT+OwpHhX5Kk5z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "7.2.3": {"name": "joi", "version": "7.2.3", "dependencies": {"hoek": "3.x.x", "topo": "2.x.x", "moment": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "8.x.x", "code": "2.x.x", "markdown-toc": "0.12.x"}, "dist": {"shasum": "c20094fca3be940cf446032099643e7be93f3c97", "tarball": "https://registry.npmjs.org/joi/-/joi-7.2.3.tgz", "integrity": "sha512-9fDoGjeEVhDhwDsjOfb+ol/V4RtiXDoAIX3B90C3QuU9JGo9atYH0gfAi72PlnbfXNbm0JUMmq0zCEdG15z9OQ==", "signatures": [{"sig": "MEYCIQDGK1ojv+irv0SDN67IGvjeE6LvjrQitSRqZhiNdrWf+gIhALiaZ6bC/8ThqaEeZKiGjmSngLfFDM5TbCrW1E3RYzm/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "7.3.0": {"name": "joi", "version": "7.3.0", "dependencies": {"hoek": "3.x.x", "topo": "2.x.x", "moment": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "8.x.x", "code": "2.x.x", "markdown-toc": "0.12.x"}, "dist": {"shasum": "4d9c9f181830444083665b5b6cd5b8ca6779a5e9", "tarball": "https://registry.npmjs.org/joi/-/joi-7.3.0.tgz", "integrity": "sha512-7ysLFfGtSg5L1MWnIkJGvJLAsdZPLZK2+qAi+0D9QdsnlPVSk+dBZxEl1ezveJuhEcvZKLK+AZSkmnXeATcB0A==", "signatures": [{"sig": "MEUCIQDvlwilOx2J08knY/z25MHBDZOx1UjMNbJIy1gRkXFyuAIgCIzcb/hGyJB2TB2wri2QKRLEcFj1S6rGFbTl9YWDsBg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "8.0.0": {"name": "joi", "version": "8.0.0", "dependencies": {"hoek": "3.x.x", "topo": "2.x.x", "moment": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "8.x.x", "code": "2.x.x", "markdown-toc": "0.12.x"}, "dist": {"shasum": "4008ddf56ad97a5172be899b214b682ce1e1620e", "tarball": "https://registry.npmjs.org/joi/-/joi-8.0.0.tgz", "integrity": "sha512-3Ec8D/0jTMzQusIItYI69d6M7gT4ActRADqsLJC5zNuXSUR06SSI2SNf2E6VPLjEllg0NawN4gK0o1c41y9xJA==", "signatures": [{"sig": "MEQCIDifiKljVpS3H0vpLzk4Lgn34TdMoEPe/V5uFM4KxG3wAiB4pdgxJT9X2Oqbyb02iYMSNCH3uJ/GVPJRL/8EyWiQQw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "8.0.1": {"name": "joi", "version": "8.0.1", "dependencies": {"hoek": "3.x.x", "topo": "2.x.x", "moment": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "8.x.x", "code": "2.x.x", "markdown-toc": "0.12.x"}, "dist": {"shasum": "c9f526e623208df42817941819a3cdb59be29ee7", "tarball": "https://registry.npmjs.org/joi/-/joi-8.0.1.tgz", "integrity": "sha512-o<PERSON><PERSON>s9WlcpL8rUkhBwqrHvORjf6RHkT63VUVXscWQhDwQ1KEyn946Oq/POqpgBRvZwh2hwlBsDV51aPPcWkhQg==", "signatures": [{"sig": "MEUCIH8XMXnEadgFRU8jEKYliKdg3bIEKjcd0tsTkRcU3a1cAiEAtPDPL05Clv+7xf0eLY3fPS+z1mncQSyezfejLKV750U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "8.0.2": {"name": "joi", "version": "8.0.2", "dependencies": {"hoek": "3.x.x", "topo": "2.x.x", "moment": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "9.x.x", "code": "2.x.x", "markdown-toc": "0.12.x"}, "dist": {"shasum": "b40e60e57802b9b2720cbda50562d239d7c2daaf", "tarball": "https://registry.npmjs.org/joi/-/joi-8.0.2.tgz", "integrity": "sha512-PqWzzH6dTmbLK+IJSrSbVcutQLRgL3Y8qSCcnlHRIWZ4tvaaimHBiZcrdeqDIcy/ZRq0N7VwpFPPcBgvMci6bw==", "signatures": [{"sig": "MEMCIAg5wgYkn9AssAwwDibmHpW+HE+05csUAdWxEYNkqP9yAh8/4TVeZnCfNrAN/JAc5wGO1cHO16v7heyz5zqc5JYS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "8.0.3": {"name": "joi", "version": "8.0.3", "dependencies": {"hoek": "3.x.x", "topo": "2.x.x", "moment": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "10.x.x", "code": "2.x.x", "markdown-toc": "0.12.x"}, "dist": {"shasum": "85084c9e7c5c615cf4338a092177ef7de2bdd752", "tarball": "https://registry.npmjs.org/joi/-/joi-8.0.3.tgz", "integrity": "sha512-i23FqiBtcvrFHxdBurQs2iAWKP8sn8B8rPFp53gDHZroBadICAdPoMNXohwp/LqllSziR71fqypoiAvoTMMm+g==", "signatures": [{"sig": "MEYCIQDoXP/XWaiybLAhIJ+G2oXuK6TRAhT2nERChD+EHVqjyQIhAJuuqIPrZWfD/Z4nTCurEMCXJz7vR6NgbaRARNP7kIf/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "8.0.4": {"name": "joi", "version": "8.0.4", "dependencies": {"hoek": "3.x.x", "topo": "2.x.x", "moment": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "10.x.x", "code": "2.x.x", "markdown-toc": "0.12.x"}, "dist": {"shasum": "5b1eb1ee4a94dda89d78c577ca21e1be7c3f5d70", "tarball": "https://registry.npmjs.org/joi/-/joi-8.0.4.tgz", "integrity": "sha512-9q4P786VeJgpfzFIsHQnpyArJYJvWTL3/SpWYyWC0EpnEIWoAMK2+5LBEdPo59j5W0VNWatpea3XQaKUUR8BTw==", "signatures": [{"sig": "MEYCIQDsGq+duuCD5DfZfCZypMe5pljKrN7FJKN8tI1BH6a7nQIhAJW9TndbmMU1+Yr+NLKG4kfV9DWbT4iQc8xl1pquKIc9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "8.0.5": {"name": "joi", "version": "8.0.5", "dependencies": {"hoek": "3.x.x", "topo": "2.x.x", "moment": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "10.x.x", "code": "2.x.x", "markdown-toc": "0.12.x"}, "dist": {"shasum": "4c78e773459121710e2cb89dc28572c170115c82", "tarball": "https://registry.npmjs.org/joi/-/joi-8.0.5.tgz", "integrity": "sha512-9Sjk3Hh8KpsNGfiLx1rNITlNqiXt1HXrr/XrjHrzRVpBKjlAV4o6esSEvrR3wgem8RNGfNsyJO1zNlkI5HF9Wg==", "signatures": [{"sig": "MEUCIA2MCGaRPyCLl5b1bx6PQeCmpN98XZr0+1GgVSjDr1vcAiEAwSHe5RaZwtWqrQ7oFpOCgpZiwmZ9+c0lHB4No8WAkhU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "9.0.0-0": {"name": "joi", "version": "9.0.0-0", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "items": "2.x.x", "moment": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "10.x.x", "code": "2.x.x", "markdown-toc": "0.12.x"}, "dist": {"shasum": "a7ca4219602149ae0da7a7c5ca1d63d3c79e096b", "tarball": "https://registry.npmjs.org/joi/-/joi-9.0.0-0.tgz", "integrity": "sha512-1ZHieO/s4JKry7OsiNY0D27BIiaktO+6taY5P0g8pu/s0NYdegdf3ia7heMSdFjmJQHJE+IG1PhTmkrJX0S8AQ==", "signatures": [{"sig": "MEUCIQCykZJA7d2u2lLmNCQMcPY2KkD+QgHjw71cWihBY3lpOwIgRYdrs9oPk7vdVDjlep2MJWe4eIvugdHU79XHpEiWavo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "8.1.0": {"name": "joi", "version": "8.1.0", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "moment": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "10.x.x", "code": "2.x.x", "markdown-toc": "0.12.x"}, "dist": {"shasum": "5a19094cce68009ce11831f3bad3a1e1e0afa2d1", "tarball": "https://registry.npmjs.org/joi/-/joi-8.1.0.tgz", "integrity": "sha512-wlP3pNGd8GtN/jO9ZOsWl4d9qbx5SgYhkBMb1mBipXemHhtdmuUL733AsqKzy8Kp2nwIKuejwuKDiPD2ilFxcg==", "signatures": [{"sig": "MEQCICHD1C1uzSVGJTZdKFn0OPeNNNcZRcWhhdl2VVJnnA+5AiAiZY/TZjtEod0fYajGj8ZNJVWSW5Rzfm1yFAOqBgFr1Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "9.0.0-1": {"name": "joi", "version": "9.0.0-1", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "items": "2.x.x", "moment": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "10.x.x", "code": "2.x.x", "markdown-toc": "0.12.x"}, "dist": {"shasum": "31e9e1af55aa5a121025ad31b2602ae0b751fe9a", "tarball": "https://registry.npmjs.org/joi/-/joi-9.0.0-1.tgz", "integrity": "sha512-kYU6UfZu85gsinreZ/85FIxg7sSss1X0ley9jk34LR62jW92yipL9w1J9PVQ9N5EjkT9DQ+Hi50xz7c+OykIdg==", "signatures": [{"sig": "MEYCIQDalPLXYcZnT6MWjNvcwZeEuPTUrjOTyl2UwcqtvnpNKQIhAMu0ADUAPdOaMDrxJMzltw/mBb3pyQpcL6xnkN1MFKfd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "8.1.1": {"name": "joi", "version": "8.1.1", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "moment": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "10.x.x", "code": "2.x.x", "markdown-toc": "0.12.x"}, "dist": {"shasum": "2d8b52a5d909d217ed47248577eefe8b1798f48f", "tarball": "https://registry.npmjs.org/joi/-/joi-8.1.1.tgz", "integrity": "sha512-fVXkcbz9ZnYpQPk7wIEMK1hEoaKaXyIdRKvhMWvNKuznHWgu4F96R65QtNdAsGEVXEGKOp3bYLXI23JbrZPfjg==", "signatures": [{"sig": "MEQCIGLrW7/Umv55ukeCuN82H8jTdXXB4280gI82E7fToElkAiBf0zNlYN33E3hwQdbETP/g+EVl4v9QeVcRGelvdy0N5A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "9.0.0-2": {"name": "joi", "version": "9.0.0-2", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "items": "2.x.x", "moment": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "10.x.x", "code": "3.x.x", "markdown-toc": "0.12.x"}, "dist": {"shasum": "2816b83c84d8fa369bef577196a229832da08b36", "tarball": "https://registry.npmjs.org/joi/-/joi-9.0.0-2.tgz", "integrity": "sha512-7oMigLCGVSnBT+AaUp2SUPSSeXT9EemUhFR2g/JOphmVPLLPl83KAefg+4at0cPHitJdksyiUoED4hRGMLrBOg==", "signatures": [{"sig": "MEQCICZuokKQJCHarI0BwNTQhLuyHw9klksXd2hbzki8DQVtAiAxtLbSR38ObB2d896QvFXCifYbA9Tc2fbSBx986TIY+g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "8.2.0": {"name": "joi", "version": "8.2.0", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "moment": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "10.x.x", "code": "2.x.x", "markdown-toc": "0.12.x"}, "dist": {"shasum": "1f01a5b1333f944b422915ab536de6d727bfda96", "tarball": "https://registry.npmjs.org/joi/-/joi-8.2.0.tgz", "integrity": "sha512-ofj0mr9afFwUCQZBC2KX5ereip4HAsrelBu8X0ktZI9jthZc/ugxy/BJjQfhWA4AoMIOSmANArVum6kRozkaSg==", "signatures": [{"sig": "MEQCIArCar62SlJqhttW+sOLKeytPNYmBStuzZDUrLluQxXXAiByJ13H6kqwwT90io41tkyUIOHNfXPR4IHLU0T5MKxzgQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "8.2.1": {"name": "joi", "version": "8.2.1", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "moment": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "10.x.x", "code": "2.x.x", "npmignore": "0.2.x", "markdown-toc": "0.12.x"}, "dist": {"shasum": "b9cca6c23a73133710402525f6d4ba481fd75f43", "tarball": "https://registry.npmjs.org/joi/-/joi-8.2.1.tgz", "integrity": "sha512-2C+r2SszpeVgBxX0hto/tFWkHGkVWjNtrZ2ki6GkCp2lDg067MSjfpz0m5WEJyQYTRXsMmJQoZ7bQ8WBVBuR8w==", "signatures": [{"sig": "MEUCIQCLGidQL1KN4Yu4hR/pzDj6pwOVGZqcTsda/1pCb6EgVQIgLi8dDhMfbEfhCPgJYpDIym72iAOLE8eHv3C+ykxsQMk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "8.3.0": {"name": "joi", "version": "8.3.0", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "moment": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "10.x.x", "code": "2.x.x", "npmignore": "0.2.x", "markdown-toc": "0.12.x"}, "dist": {"shasum": "03f2742155484e299da26cc671ef54ddd1740e3d", "tarball": "https://registry.npmjs.org/joi/-/joi-8.3.0.tgz", "integrity": "sha512-QR1LVJFJtHv/dpgxkgDzaZx8c4hPsmYcyuoxbL5i47yLr8+ESqAbRIniQigfaYk7V1EkOivNpB35jSwXAb5SGg==", "signatures": [{"sig": "MEUCIB1W5/atGPaVu5ba83LpOSzlJC9T2A1HQpOn/Y8DqgjEAiEA2Hn5wEGe1U0OLoIAs6SeC6IYBpxhdjzGSuvhX/w/0BE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "8.4.0": {"name": "joi", "version": "8.4.0", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "moment": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "10.x.x", "code": "3.x.x", "npmignore": "0.2.x", "markdown-toc": "0.12.x"}, "dist": {"shasum": "e309944d8690468517ab879f8a94d23858278eac", "tarball": "https://registry.npmjs.org/joi/-/joi-8.4.0.tgz", "integrity": "sha512-jFAKSORvJc3uS6KCV51+TcqjyLyitboPqjSjnP8pRAcMYpcBmkPVlthvYx9MT5tJSX4D9jcbORd0TYcYcVQ5IQ==", "signatures": [{"sig": "MEQCIGGaev7ZLIgV0mxHVCh5MXPQTtPYUqo62Aj9+sNyPaX6AiBfYnAclIsO7ZY6acy+mMdzETMO/ossASuWCsZfUcNGTA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "8.4.1": {"name": "joi", "version": "8.4.1", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "moment": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "10.x.x", "code": "3.x.x", "npmignore": "0.2.x", "markdown-toc": "0.12.x"}, "dist": {"shasum": "699a9c36facc1bace16a55bd56b2789a0e2f91f5", "tarball": "https://registry.npmjs.org/joi/-/joi-8.4.1.tgz", "integrity": "sha512-5tW/TBCoXFKWHmtajX93xEEB0JlUQnSGm7YU0XtXbP+vEgSoElWczpGWTcMpJiiby3ArGjY+rHNCkFQlZlqCFg==", "signatures": [{"sig": "MEUCIQCyaY6TbqZSFMgS8aadL5zpMT6Z4RRbsgqKbAz6YgCOzgIgH3jdTB7AqBverb7ypk8xtmukZEvraCq4wFMD87kwA50=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "9.0.0-3": {"name": "joi", "version": "9.0.0-3", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "items": "2.x.x", "moment": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "10.x.x", "code": "3.x.x", "npmignore": "0.2.x", "markdown-toc": "0.12.x"}, "dist": {"shasum": "a10a7614ea3534326bc28a5da6184b2c6684e43e", "tarball": "https://registry.npmjs.org/joi/-/joi-9.0.0-3.tgz", "integrity": "sha512-Tey0SreUOTl0F23W8L2rXznRTv1smD0dJWg8C4W7ywxX/+yh4UB9LYS9m3Rg8CuwZRmrS3TNwMIBS0C2ANAR0w==", "signatures": [{"sig": "MEUCICr9LU+dbpTFvf3fMksBpitrZpUtcLGzbROMDBy3HiK0AiEA2bcrwDoacjoHyV+DxNaNHK8IMioAtigsFrOyQpDYNAY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "9.0.0-4": {"name": "joi", "version": "9.0.0-4", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "items": "2.x.x", "moment": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "10.x.x", "code": "3.x.x", "markdown-toc": "0.12.x"}, "dist": {"shasum": "a32f9f8bee1e70edcff7b2c27ffd31e991c3a331", "tarball": "https://registry.npmjs.org/joi/-/joi-9.0.0-4.tgz", "integrity": "sha512-mMnKhRQIaEy+l6+VFljrR1ZqBd+c21ckhN21DTMDTyyWbauMHUQPWiFlLNbYLBQutpZnfrXXqOThLgfVOHrewg==", "signatures": [{"sig": "MEUCIQDtfKVisTNpuUCka8ROsI/BdDSjC9Hy+hlMrOq2weCLZgIgPVsHIgMF38MfnOFgW2dnr9iOQ5vMLrn53gifDI8nRQg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "9.0.0-5": {"name": "joi", "version": "9.0.0-5", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "items": "2.x.x", "moment": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "10.x.x", "code": "3.x.x", "markdown-toc": "0.12.x"}, "dist": {"shasum": "d531d6ae4e6fd0e1a6d5ffffd9e2c2870cfd5ab8", "tarball": "https://registry.npmjs.org/joi/-/joi-9.0.0-5.tgz", "integrity": "sha512-bItM+NDxfGIK66iahRuDYfLPgDyEv/JaKRCX4lHwYaZvA6CrSuZMNuwF3liuL1v6JBioKmY4Hvb6k2P0ztaKvg==", "signatures": [{"sig": "MEYCIQDe4nUyY4jJFPyM15uY2NDE0J2lOZRyfnZFiSAefSG5KAIhAIxISHkYst7z8/ElxC2qz+M3SMR8rN8Jja54ne5qlzbZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "8.4.2": {"name": "joi", "version": "8.4.2", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "moment": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "10.x.x", "code": "3.x.x", "markdown-toc": "0.12.x"}, "dist": {"shasum": "bd7774658fe99058d8994ed1d4b9962484ebb859", "tarball": "https://registry.npmjs.org/joi/-/joi-8.4.2.tgz", "integrity": "sha512-f4dKbttxJ83gDnsWqrgnzR9kQ0c2+YjPQPomiD+wwegH2Tbs9SdjyeLwSKi6a74MTGNXdjbZCR28STbu4rTxfw==", "signatures": [{"sig": "MEUCIQC/qhneV0B5Z7sG4PiDTtEEPzJGX28+mo9goHKJwCqQ9QIgScS76K40xtFGgSGw5dO9YaBo4remPnN1eYvWXsOUrQ8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "9.0.0-6": {"name": "joi", "version": "9.0.0-6", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "items": "2.x.x", "moment": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "10.x.x", "code": "3.x.x", "markdown-toc": "0.12.x"}, "dist": {"shasum": "d3a8703cbe5c10d2e058b7397337c241b7e7556c", "tarball": "https://registry.npmjs.org/joi/-/joi-9.0.0-6.tgz", "integrity": "sha512-mZT+uG/WJ4bEgEay277V+69gQ2ezWpV+4r4C9MyzzCrIj83s8OdMpGYwG3YNKfAk/9NiWbAnKZnsXOixzvvk9g==", "signatures": [{"sig": "MEQCIFD1GqoBv2kNq0zO6XbhUYI/J49cbK6HcNYUo5UDQ2oQAiA5fKs3IV2psjLkWCUMP8TmU7iiwxzYXzEhT2HFHfc4PQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "9.0.0-7": {"name": "joi", "version": "9.0.0-7", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "items": "2.x.x", "moment": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "10.x.x", "code": "3.x.x", "markdown-toc": "0.12.x"}, "dist": {"shasum": "60a5afccb9f300016f757d0e53bf633d620391db", "tarball": "https://registry.npmjs.org/joi/-/joi-9.0.0-7.tgz", "integrity": "sha512-v8HMkoKjgFeeRCmqUEHhpnfvgYTWtkqSHlEG/MGXtVhlxofYS5FPdWZOrgzx4EP4LDGxvda4vYYuivXkjucMUA==", "signatures": [{"sig": "MEUCICRKZl8ILcQUkeZzzbZxgvJUz6gFUYDO9lkf9lWz+nUxAiEAjG9FD4UC0qGfGIuX/z4P1aZWGAGEhTVKnVF/bOcVzfg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "9.0.0-8": {"name": "joi", "version": "9.0.0-8", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "items": "2.x.x", "moment": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "10.x.x", "code": "3.x.x", "markdown-toc": "0.12.x"}, "dist": {"shasum": "eb249fa42660180b430c1b4637fcc1170278b7ee", "tarball": "https://registry.npmjs.org/joi/-/joi-9.0.0-8.tgz", "integrity": "sha512-0Z7GHOiK8jHv0KcUk/U+4s9PN1QmzfluqNF2aAW+RwyyaJraoOmxdeCPXhCW8gP54hb+cU+u/yLgELbqX2Lu4w==", "signatures": [{"sig": "MEYCIQCVeLxUSlnJweuG6Oi62JxLo+y0IxCdKs4xMyPykl9WIQIhAKH3enIZfu+r/1VQqxRfx1/T2gVwAf456yhdtjTI2KFh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "9.0.0-9": {"name": "joi", "version": "9.0.0-9", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "items": "2.x.x", "moment": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "10.x.x", "code": "3.x.x", "markdown-toc": "0.12.x"}, "dist": {"shasum": "39f81ef1cf68ddbd04cc4561899f276550cbcd54", "tarball": "https://registry.npmjs.org/joi/-/joi-9.0.0-9.tgz", "integrity": "sha512-2Naj951QPud1XcRbRSBmL3aZ5seTxdrgUClaChPdXh4EA3r9iFvJA6c9ycvxYkLIMHC2U8rGCG2spxvALUJcqQ==", "signatures": [{"sig": "MEQCIEMO4X0XFEw7QaVIJkN84vGywQH2ZiG0Jl6LI+o3W5nDAiAJ4gClakVxs4uLbGbRgJYcIL7NuPT738pyacTGq1BJww==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "9.0.0": {"name": "joi", "version": "9.0.0", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "items": "2.x.x", "moment": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "10.x.x", "code": "3.x.x", "markdown-toc": "0.12.x"}, "dist": {"shasum": "ee4977ee0198d78948aad9018f71c5c0b50d1b9a", "tarball": "https://registry.npmjs.org/joi/-/joi-9.0.0.tgz", "integrity": "sha512-Na43AbD1d6vaqnTQg78TIyXIP2UtCHJ99Tt6j32u+topGhf0yg15kbWNyk30Z5WONnTfmIwjl944EuJ1u1AHkg==", "signatures": [{"sig": "MEUCIQDk1HdQCQ+MtxZQ8gyAD8khv6eYmq+8Dg9GZFYd5FCDGQIgXhlhno/W9IUuV+oKYshba6x4cjzIblifglq+FXzgoTY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "9.0.1": {"name": "joi", "version": "9.0.1", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "items": "2.x.x", "moment": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "10.x.x", "code": "3.x.x", "markdown-toc": "0.12.x"}, "dist": {"shasum": "1380dc80c23c2d362fb08a6854260c65a6e41c79", "tarball": "https://registry.npmjs.org/joi/-/joi-9.0.1.tgz", "integrity": "sha512-uchc+Oc2U09dxCFwZWqViPCH1e5lbd4AivUPn8OcnCZ/uaJrpkGAM3AJFOe/9SBvtRXH7dsIviVXMwATV1Z7yA==", "signatures": [{"sig": "MEUCIDsVd/vIm7uRP+fJvrU9fTgU/VGSVRdQjJ0sUujTMbXQAiEAhEaddkQxaIqJHjM3I6VCvjtOOpb5FCerRad6nnwemRw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "9.0.2": {"name": "joi", "version": "9.0.2", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "items": "2.x.x", "moment": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "10.x.x", "code": "3.x.x", "markdown-toc": "0.12.x"}, "dist": {"shasum": "ebe93b4a9cb81b0e2407b31e8280a1e0fac07833", "tarball": "https://registry.npmjs.org/joi/-/joi-9.0.2.tgz", "integrity": "sha512-TiBuVrepyruaE1dVfn6061tv252xgETcKMUwjxVw/WVUfOpMl+mdOJDFHK/KJYcTy8ggHmkvijEE1Tg2jT/oHg==", "signatures": [{"sig": "MEUCID8o4zLOUHvYOCFJFCJy5ywuvVT7R5Bk8y8dMYwm6RGNAiEA9mKMtaGNd825lPnYm65O6mZHsgdU6Y3D20VgD7Wsyuk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "9.0.3": {"name": "joi", "version": "9.0.3", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "items": "2.x.x", "moment": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "10.x.x", "code": "3.x.x", "markdown-toc": "0.12.x"}, "dist": {"shasum": "45d295013b66ac53aa4eff94fda9f0094f4bb6ce", "tarball": "https://registry.npmjs.org/joi/-/joi-9.0.3.tgz", "integrity": "sha512-PYXcfPwu2pLzOyMHDoLqxk/5niKR1TgqA93/Q6cZZwMwww3Dx4Q0w8J9G5HoFQuWE0LuC45pZHr346YeO2H++g==", "signatures": [{"sig": "MEYCIQCbgLj7gGBmkKRyWnHPxF8zj+FCaNc2Y/qrBm5F4M9NFAIhAMB+6/ptuPZUXBKqOp/lnyIjL8zuY+stPfdXoRY3+EEJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "9.0.4": {"name": "joi", "version": "9.0.4", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "items": "2.x.x", "moment": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "10.x.x", "code": "3.x.x", "markdown-toc": "0.12.x"}, "dist": {"shasum": "88d64890915abeb127cd757027116d50df3e68df", "tarball": "https://registry.npmjs.org/joi/-/joi-9.0.4.tgz", "integrity": "sha512-DLmywyD9Pf+wumOg/7wTAwMdXL3/1CZO3MNd6lypWDGi4G9Ix1jXdFvexocpmPIbbKJJBETspRGA5DufYt5Lyg==", "signatures": [{"sig": "MEYCIQCkMRy+BZIxC68yMug/gvZu93A0WM8KaKALiL00GXiKhgIhAKgFDnvgnCi5qC2dI4HZHInI4fF5KYiIFI3hQ+lFIEtV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "9.1.0": {"name": "joi", "version": "9.1.0", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "items": "2.x.x", "moment": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "11.x.x", "code": "4.x.x", "markdown-toc": "0.13.x"}, "dist": {"shasum": "ddad93811592b0f9ae7603fe82c4b63e5bb49e40", "tarball": "https://registry.npmjs.org/joi/-/joi-9.1.0.tgz", "integrity": "sha512-HrEgvJforEA7089HvyDidzCjt41b/l0KWdsunv+POlorQmJhBO3aP8uNrd3q6F4DUMwgEv4YPzmE1YnQJWj7gg==", "signatures": [{"sig": "MEUCIC/kfcinuew+KqkdRzvu3AZr/lTjjZtKmnmfHp8eb7oxAiEAijkuTJzadGbqUBZ4rMSFvBx2rWlmvmAuh7d56h+Ck00=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "9.1.1": {"name": "joi", "version": "9.1.1", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "items": "2.x.x", "moment": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "11.x.x", "code": "4.x.x", "markdown-toc": "0.13.x"}, "dist": {"shasum": "357a04c3ad424abcb9790829be8c01a131d93500", "tarball": "https://registry.npmjs.org/joi/-/joi-9.1.1.tgz", "integrity": "sha512-7RNbU5qeYbY12xBh+bpjxjnvsvW7LbUt2o+XNLk8/Pe7qFrqFWYYMit/TRQHTA8CzyBtfNLJ7k9W/NFIejrD9Q==", "signatures": [{"sig": "MEYCIQDwKvgwaqIjxU20EDbfU+B+mJuvBBAE7YwmHLKjEJKmXAIhAJpbX4X3v26b6GENpcv8tAv9gtiCsXhvUpX86uKgQ1eY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "9.2.0": {"name": "joi", "version": "9.2.0", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "items": "2.x.x", "moment": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "11.x.x", "code": "4.x.x", "markdown-toc": "0.13.x"}, "dist": {"shasum": "3385ac790192130cbe230e802ec02c9215bbfeda", "tarball": "https://registry.npmjs.org/joi/-/joi-9.2.0.tgz", "integrity": "sha512-54NcYM0TISAuh6NbaC+Ue7v02jSADQm5Fm/1AITBzx4paoCyQPBFbMkZBKY/qa0JBuR6JzQY/XwHrY82fEUbpg==", "signatures": [{"sig": "MEYCIQDb/c4dYS+MD2RQ2KheMxmcRNAGQZqZbSDmr0zmvUpXygIhAJhSUgt902wQ40UqWt3H4W+EYl+PHAgKmINF+497DBKt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "10.0.0": {"name": "joi", "version": "10.0.0", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "items": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "11.x.x", "code": "4.x.x", "markdown-toc": "0.13.x"}, "dist": {"shasum": "ee951f228ea22f6382b1e651c4db6be771a982e2", "tarball": "https://registry.npmjs.org/joi/-/joi-10.0.0.tgz", "integrity": "sha512-JT+CBdOyAgTqzq0rX6L9AZKQmPb+QwJimr3zG/RL+C5NcpsscTTXS0n52HJrH1/ljTjjkgjiCrJ8iCTMfDQAhw==", "signatures": [{"sig": "MEUCIQCNPKqwBLqoUSgZPgSJK/McKhzAWx8tEOErcQ0ZoIQ6PgIgAm7v1FOCKjljmqT/zMMEP81Faj4hLBfMyQeywa248oM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "10.0.1": {"name": "joi", "version": "10.0.1", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "items": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "11.x.x", "code": "4.x.x", "markdown-toc": "0.13.x"}, "dist": {"shasum": "30ce9f77038b7d9df0825d80108b15c4cad30a0b", "tarball": "https://registry.npmjs.org/joi/-/joi-10.0.1.tgz", "integrity": "sha512-wThgPAHhE3RCbJHGQRCAtPHOJ0VYE5k7eL4Miajlnq1macgZjT2hxotSV6If3R7dnI8Onh4o7/5NN7j+KKTudg==", "signatures": [{"sig": "MEQCIBPNvoge3lClPdOPrua/1/pp0AqVcuMtb7P3LRjtyKAqAiBAcXCPMgvd4A30tPzSkmLaG+0yyeiV1YcTUYFxIJfMEg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "10.0.2": {"name": "joi", "version": "10.0.2", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "items": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "11.x.x", "code": "4.x.x", "markdown-toc": "0.13.x"}, "dist": {"shasum": "02b970ec29251f0c185615056adf3e984af2c426", "tarball": "https://registry.npmjs.org/joi/-/joi-10.0.2.tgz", "integrity": "sha512-l4qStraK0X0npJ75NSCYR2z49i1WKqcCvXmUmVxiXfLSEiXnuilhgPcxTX3OkoXsigRT7OTY1wLk8Fx/H3Jf6g==", "signatures": [{"sig": "MEQCIBuGbhgOHbBek+xVimRG9lnsw5OPeKEpGoDQ7AWG45kkAiA7tmUJ+Km1AWHKXMGpgV5PTO0hj5wL0qLQ4ZhaizHjMA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "10.0.4": {"name": "joi", "version": "10.0.4", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "items": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "11.x.x", "code": "4.x.x", "markdown-toc": "0.13.x"}, "dist": {"shasum": "67a0d7f38753a22ce8f5778f3851035ccfd38743", "tarball": "https://registry.npmjs.org/joi/-/joi-10.0.4.tgz", "integrity": "sha512-qWjqAHzDyffrzyuXFxe3Y+awceJwZiYS3Sz1ZOPAQgCgCjat9h/zNWTiFW5i2YoWcF1QVP7tEzGc0BQp0UK+jA==", "signatures": [{"sig": "MEUCIHL9T6gOjFKHQKRrUYGr53OgXGiqEffQOkQOBwjZ+OiYAiEA1I1EUEt8xAWhyGPQ1k6iDL6Hj2wKIzNPiK2wJK34u90=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "10.0.5": {"name": "joi", "version": "10.0.5", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "items": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "11.x.x", "code": "4.x.x", "markdown-toc": "0.13.x"}, "dist": {"shasum": "2e43af9bf24d2d5745852e9ab968c85be357bd6a", "tarball": "https://registry.npmjs.org/joi/-/joi-10.0.5.tgz", "integrity": "sha512-mub5YMxYjPIrL3HZwOj+2KnonBTMpWyITgCudcZrznNWv9gmrnA89dxSWlvJryjpLFNJUhSnJ48XE8sz2fIf0g==", "signatures": [{"sig": "MEQCICfwnzUSJn5dwj7LF/qPEGr8oC/gHz8G+1iiMG6zREsBAiAowcwDFrYx0BUE62HOReb4dlsi30+8lxcLjhP8lT8ZjQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "10.0.6": {"name": "joi", "version": "10.0.6", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "items": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "11.x.x", "code": "4.x.x", "markdown-toc": "0.13.x"}, "dist": {"shasum": "dbacbbf30b4dbb9d28ef6716d82c4580bb04c096", "tarball": "https://registry.npmjs.org/joi/-/joi-10.0.6.tgz", "integrity": "sha512-ZYs2x5kDSrcF6e2EAF+s0K1wf13f9UL5eEKL6rpAgMNhflUROB6VveIyQ7ta1Iwc6tbTUeTl7d3WL8y/WnOibw==", "signatures": [{"sig": "MEUCIQDrTtrobI+WO/1MX1k7DpaH3XC+iFRUKRxEN+rohydvWAIgTNbFpWnQJKkyCt/IxiBS38nRs7lPnaFGUneuW1wplio=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "10.1.0": {"name": "joi", "version": "10.1.0", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "items": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "11.x.x", "code": "4.x.x", "markdown-toc": "0.13.x"}, "dist": {"shasum": "8c3a87577c159ffeba129a5054f3238f9efd7159", "tarball": "https://registry.npmjs.org/joi/-/joi-10.1.0.tgz", "integrity": "sha512-2Lz/XETppEbuhlmxpbaeOBwBaHruPRaDbO1XdOlSbO47EREKDloszIQpT1SLsdLakj9Q6n0wefmbw9ZJIB+YLg==", "signatures": [{"sig": "MEUCIQD2A2hiTKxyYDPYrqgTMxBaFKwrNXGlPPxrB+AJDylkFgIgaqrt1fJsgGVGXXU6l00QMIAigGwDFhji2M18YbEuujk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "10.2.0": {"name": "joi", "version": "10.2.0", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "items": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "11.x.x", "code": "4.x.x", "markdown-toc": "0.13.x"}, "dist": {"shasum": "2c9dba08240d453e58145667f0d5006de527e328", "tarball": "https://registry.npmjs.org/joi/-/joi-10.2.0.tgz", "integrity": "sha512-srIOAbnXlCAL3URQUg9WZvUpNgLZoN6M9PmVnQInvXJxQ81dGwNhFiWbCTjNBRQlkt/U0XDBWI0ayfGqSg2i5Q==", "signatures": [{"sig": "MEYCIQC4K69lxnUeGtzLB5xaS2T2yGTxtJzEx4mPFbSt1e6C0gIhANW9YjsYqgHpJikaCdOf2WC3jv3sYLWisRlhEzZhNa2A", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "10.2.1": {"name": "joi", "version": "10.2.1", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "items": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "11.x.x", "code": "4.x.x", "markdown-toc": "0.13.x"}, "dist": {"shasum": "f3a5f6a492d311735f17b109487646c2194aa92d", "tarball": "https://registry.npmjs.org/joi/-/joi-10.2.1.tgz", "integrity": "sha512-0MvtXeNNrAGcfUyPawu8XNhqYZyLP569kKgBJN34Zxt77dpSZDCWEdFw5I19da6KQ2SD4k+NthopGA8grkyVSg==", "signatures": [{"sig": "MEUCIQDbO/QWzjRnqf3DCYijXiw00/wxdIJ9Uk1ErNpEtT/+dgIgHG0xk8gtZGBiF56Rx/ij2jLpsVOgxcb947k7GA4UFHA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "10.2.2": {"name": "joi", "version": "10.2.2", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "items": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "11.x.x", "code": "4.x.x", "markdown-toc": "0.13.x"}, "dist": {"shasum": "dc5a792b7b4c6fffa562242a95b55d9d3f077e24", "tarball": "https://registry.npmjs.org/joi/-/joi-10.2.2.tgz", "integrity": "sha512-Ob4w3y3t8Fq+ldYdjL0SMKQEqKddytTB2lZQmxkOqvOS4AB78eopYzkRoaxKKrv12utckhy2bNHhOPEt6KvnCg==", "signatures": [{"sig": "MEUCICzadBMmjV1Ka8HrtbSDcNF5LOy3O0hGD6DLKptN2eIMAiEAlE1sLTBQqwNMhXxd9aVp/6miBvgW/JGAwlNfw2tzhbA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "10.3.0": {"name": "joi", "version": "10.3.0", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "items": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "13.x.x", "code": "4.x.x", "hapitoc": "1.x.x"}, "dist": {"shasum": "f4ec11ace532fdef6ba780297ce8399268551c2e", "tarball": "https://registry.npmjs.org/joi/-/joi-10.3.0.tgz", "integrity": "sha512-xTMELcgXYhuoTOMTPga1dYvSabGAhdIa6SLqlNh0HvTfZQEG+gntRaFE3l81RUi/WdWh4Lgv6Y+a6Grlyf9QCw==", "signatures": [{"sig": "MEQCICzWdjAsJOZ3TktOQWUlKupeTR94kQ21nuUWFvcrB71XAiB4dTH1lQ9o3W5/pYtqFrPeWpmL0Vjfs59Az3hSpOs70A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "10.3.1": {"name": "joi", "version": "10.3.1", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "items": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "13.x.x", "code": "4.x.x", "hapitoc": "1.x.x"}, "dist": {"shasum": "2d22cab6eea650f02601144e51c35e2ba5ec497a", "tarball": "https://registry.npmjs.org/joi/-/joi-10.3.1.tgz", "integrity": "sha512-GhHdZd3fMWzFcduhDuXlFomTuZKjr5d2WjB7kvybnyeqG/OB00AXPFpnPhLRmwh9U/SdBI6Jmsu+3ZcK7178Fg==", "signatures": [{"sig": "MEUCIQCvcOVL2pqPrx4bntRcmvAWszLgwINqyZVvDe1k/cit6QIgSWw698p/1T1gKVuVsV/cXjwyyRbhLblqaSLKMDtjlHQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "10.3.2": {"name": "joi", "version": "10.3.2", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "items": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "13.x.x", "code": "4.x.x", "hapitoc": "1.x.x"}, "dist": {"shasum": "bc9b3c6ff49b5bb6866685cb504da9e01288b025", "tarball": "https://registry.npmjs.org/joi/-/joi-10.3.2.tgz", "integrity": "sha512-ucDQL/Ehg9YJhCkMmm4Ktd/lZeoYT354ZZ7zpoiZ1Ga4DcUTrL5T1HSuYBKogVlxx5vhZmXK6o0OJQJX6ZKaJQ==", "signatures": [{"sig": "MEUCIH6F1cx/aYht1tGxZ0flKaIeMjktacaZAey0+BRAF0MGAiEAkCF8yBzaEumIY2Gan21RZtSc+ozA/x/dVkS9aaLg67g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "10.3.3": {"name": "joi", "version": "10.3.3", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "items": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "13.x.x", "code": "4.x.x", "hapitoc": "1.x.x"}, "dist": {"shasum": "acb22f94a7867b2b26123139ceb60ea3185890ca", "tarball": "https://registry.npmjs.org/joi/-/joi-10.3.3.tgz", "integrity": "sha512-CEIJnHYtW4qmEzUn5M/e5jL3shQ3FsUYDZSjsua80k6B9tQqG/FaI4NMQVYul/B5iEhBUFG4uIcDojqxJZyJNQ==", "signatures": [{"sig": "MEQCIGVhKk6C0/YPi2QDx7rsbBvXGUfbBEw1bkNw2CWj/fPiAiBhZfc82iYn2dZS0UhQZjKmvibz+wcZ9E21I1/LZG1dQA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "10.3.4": {"name": "joi", "version": "10.3.4", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "items": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "13.x.x", "code": "4.x.x", "hapitoc": "1.x.x"}, "dist": {"shasum": "909ce094531dfac6212a142c324c6492022f6132", "tarball": "https://registry.npmjs.org/joi/-/joi-10.3.4.tgz", "integrity": "sha512-b6WU3Y0OBMNIX50BNn4vCSz/dMAAiyDv34w2oAGdU1Nvj1lBgy7rXP0RFFl0K4AA3F428BMGwk/tzwP3aSQ+uQ==", "signatures": [{"sig": "MEUCIAL4OelhuJeeysL8CqvJV6wmReUpNaFb6uF50Yub+rYvAiEA1GeEVISsnbelT883fprLOv0G4gmv+ROcH3AGaScT7G8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "10.4.0": {"name": "joi", "version": "10.4.0", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "items": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "13.x.x", "code": "4.x.x", "hapitoc": "1.x.x"}, "dist": {"shasum": "75b698b4adaef445187599e6d71bed406f7000cf", "tarball": "https://registry.npmjs.org/joi/-/joi-10.4.0.tgz", "integrity": "sha512-//4Wnb9/mjQDurg18DWCeZ2k7sUUiSuQC8Lh87deLGloBV5rp05dD1GSjXRe4x1tbWolsBvMHtiJKJixMg0oDQ==", "signatures": [{"sig": "MEUCIFdBfzRkZ2SZ7rT32MGfTTx3aHXb7Mzh1mEbetSdSk0+AiEAnW9B8WZ+ElZGxX+2a7PqXFfHBqJnVOblhM1pVl1pzuM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "10.4.1": {"name": "joi", "version": "10.4.1", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "items": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "13.x.x", "code": "4.x.x", "hapitoc": "1.x.x"}, "dist": {"shasum": "a2fca1f0d603d1b843f2c1e086b52461f6be1f36", "tarball": "https://registry.npmjs.org/joi/-/joi-10.4.1.tgz", "integrity": "sha512-E8R0tVNC4sO0VhOuTYWL/50Z2YyfONYVj1obK+ZzbitLfWPkbbeMZMgerhM/CGfQ83q91e+VQ2JNPI5VcJMpMA==", "signatures": [{"sig": "MEQCIAXyRT6bOneWU1dW8PBTFFTy6IQ+AbjfiYAMBQr0srngAiBbs0BY54cAR0KF88vPUAeWl0L8uHF+AvhSiRJimx+C2A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "10.4.2": {"name": "joi", "version": "10.4.2", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "items": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "13.x.x", "code": "4.x.x", "hapitoc": "1.x.x"}, "dist": {"shasum": "bc934aed34f8d0d0c84365c8e52fb6178ceb926a", "tarball": "https://registry.npmjs.org/joi/-/joi-10.4.2.tgz", "integrity": "sha512-LA8I5esNDTGeEftoWXGwJU3aYPeT2XEfFGEWH86JabXojmHtyLzkiwM0u3EU/RfZHDr2bOsF+uXat+KFRHbxiA==", "signatures": [{"sig": "MEUCIHkCS2LWnMMKkniHu38ARVOaUt7RqJ3Oo+6Rjhf1ndHEAiEA26W49asgijzR5qY1KaGimp/TsthbHbCmsR7fT3762ko=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "10.5.0": {"name": "joi", "version": "10.5.0", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "items": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "13.x.x", "code": "4.x.x", "hapitoc": "1.x.x"}, "dist": {"shasum": "4e3172a199c993b36aabe5a987b00b1b541c3059", "tarball": "https://registry.npmjs.org/joi/-/joi-10.5.0.tgz", "integrity": "sha512-5daUVp9m1B/9HKsCWxxSOHFChCkfiz95sTQg+XQawtVd9OekXcRWE8R6a7X4F/Es0/CPnF7cRA5+NstMJB0X7g==", "signatures": [{"sig": "MEQCIE6HTchnFHIISiPAicQ8H92uoUpMvdxU9kRebMa9pNwyAiAiwgRoMtYIH8Ykc1L4Nsa7yBy8MTMGzFrhaUYXt/JzqQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "10.5.1": {"name": "joi", "version": "10.5.1", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "items": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "13.x.x", "code": "4.x.x", "hapitoc": "1.x.x"}, "dist": {"shasum": "22c608d106c1f7fe18c6f1bf38a228fdf2daab7f", "tarball": "https://registry.npmjs.org/joi/-/joi-10.5.1.tgz", "integrity": "sha512-er+WKYoH23sYMbguYR3l+YiRjDs1WdYjSsnSx9xo7mDS0rACr5KhaEZS3tzWVz2vgrMBAy5N+Kfs+biEqZLRHw==", "signatures": [{"sig": "MEUCIQCrkyT84zkonvZRTm2cPR7rK50q02iJkxSsc2fMCUVsSAIgVghbAWTIWXpSkzDjdI9H9wEhCenTDsa6ddu8NTIfUI4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "10.5.2": {"name": "joi", "version": "10.5.2", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "items": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "13.x.x", "code": "4.x.x", "hapitoc": "1.x.x"}, "dist": {"shasum": "64f6853b080e9df0cf4cc9e204fa12cc8f792c48", "tarball": "https://registry.npmjs.org/joi/-/joi-10.5.2.tgz", "integrity": "sha512-Do/NXTokq86nvFfggE4UbwNnyzvALn3Ay+gMwQSAkrQoWTYc4LvkabhypVZiqDZq0TbtVhUpOphe4dmKB6/Pxg==", "signatures": [{"sig": "MEYCIQDDy7oR6MHzp1AwPpiSR0EC4sNNVfsuU62w8o6+4UOR3wIhAOkZ+esjoSG+EwbpZxQ+kaIebF2knqEGpJpdEl2Pax/R", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "10.6.0": {"name": "joi", "version": "10.6.0", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "items": "2.x.x", "isemail": "2.x.x"}, "devDependencies": {"lab": "13.x.x", "code": "4.x.x", "hapitoc": "1.x.x"}, "dist": {"shasum": "52587f02d52b8b75cdb0c74f0b164a191a0e1fc2", "tarball": "https://registry.npmjs.org/joi/-/joi-10.6.0.tgz", "integrity": "sha512-hBF3LcqyAid+9X/pwg+eXjD2QBZI5eXnBFJYaAkH4SK3mp9QSRiiQnDYlmlz5pccMvnLcJRS4whhDOTCkmsAdQ==", "signatures": [{"sig": "MEUCIDK9aZA03AtSkeZBHriUi42XFnTyJh+m7CkRWf1Xe66sAiEAyOvqJgCTQy2kUKAQZJQr49yCRa5vATxrXl1IU9nWgkw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "11.0.0": {"name": "joi", "version": "11.0.0", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "items": "2.x.x", "isemail": "3.x.x"}, "devDependencies": {"lab": "14.x.x", "hapitoc": "1.x.x"}, "dist": {"shasum": "31f87aca7d59665172dd11c361613632a9e1d974", "tarball": "https://registry.npmjs.org/joi/-/joi-11.0.0.tgz", "integrity": "sha512-HqouwTIROEEfQ/buAUrEHazNKlpaxJdJYsJkzJzfunNVXXxsA/OJgwcT1SiTSefME3I8CqT2DRjnDCErFROFNw==", "signatures": [{"sig": "MEYCIQDp0DD3oFewU9k9yPcKLsmiYT7SaMVzBh7XY85qJ5AlFQIhAM4Aucg/UwApkxKNN4wmtf2DIIbUc3ApTuP3GCnqHM8l", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "11.0.1": {"name": "joi", "version": "11.0.1", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "items": "2.x.x", "isemail": "3.x.x"}, "devDependencies": {"lab": "14.x.x", "hapitoc": "1.x.x"}, "dist": {"shasum": "a97bafd15b4cea708462f862a4e8e8b4072c5985", "tarball": "https://registry.npmjs.org/joi/-/joi-11.0.1.tgz", "integrity": "sha512-Al8J5abXq4efYMq0+WsGZCJMEQXTiEZgpQ8pN/xlZnwqJ0xUMGlxB8tQElrkGDTQfpUpvnmbDwGO5/b2YuOb8w==", "signatures": [{"sig": "MEQCIDj+JFfKpcdeIPSr8n89DS758niCampINnF3cRfyMqzgAiARbv8PIB5NIJi6etUzEvVGFoFCOBlq2QWn1/zDWFuKJg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "11.0.2": {"name": "joi", "version": "11.0.2", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "items": "2.x.x", "isemail": "3.x.x"}, "devDependencies": {"lab": "14.x.x", "hapitoc": "1.x.x"}, "dist": {"shasum": "5345ccf26f070e00b91add74ecbf3356df38e364", "tarball": "https://registry.npmjs.org/joi/-/joi-11.0.2.tgz", "integrity": "sha512-VoWLK6UAF92nBmF/cZnkX/un3Rz8J3t+BDJ59Q1Etm1utJU000wRuhWLSkU3OcIM7+MA5A0hjClp6AWU0k8G2w==", "signatures": [{"sig": "MEUCIFF0iYWtowmZuqnZj7qbbLITc8z8dwbUjSijrUozMIEgAiEAph1JGIJzpiV0qpE9kSMkgSMaDPa0y0CINuQPfySyYRY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "11.0.3": {"name": "joi", "version": "11.0.3", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "items": "2.x.x", "isemail": "3.x.x"}, "devDependencies": {"lab": "14.x.x", "hapitoc": "1.x.x"}, "dist": {"shasum": "6b43faa805da32e54ab0722b38058c25cc7c3910", "tarball": "https://registry.npmjs.org/joi/-/joi-11.0.3.tgz", "integrity": "sha512-K3unbIK4ayKbqxs/W/bOHAKEzy8pXmJEW5xqpPxUN/zjWxlKryEn+GxwSxTIyAHEoKeYWRLiatNqZDEb9r7fIg==", "signatures": [{"sig": "MEYCIQDJ2CYehgqWFmnAM7S4EjsKPCzXBCxIKCWdko8PD/jo5wIhAI9yI+2iY0KdIgqnnSUWyDMjmDQh93/WxKf+PL2eS2zS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "11.1.0": {"name": "joi", "version": "11.1.0", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "items": "2.x.x", "isemail": "3.x.x"}, "devDependencies": {"lab": "14.x.x", "hapitoc": "1.x.x"}, "dist": {"shasum": "3abcfe3ee14036fe9554c3d26b5457075c3dc267", "tarball": "https://registry.npmjs.org/joi/-/joi-11.1.0.tgz", "integrity": "sha512-Ow1urWlw1PyFI+QbYfOW9PsB5odzDV6Ol8R4J3/gyxKJpuCTlzMztfYmXYg8I0oqCe0cyUEY9Bf9CMIRDDremA==", "signatures": [{"sig": "MEQCIHTcV2wkcSbW20XirD2L5nhwoC9a4a5TPnpGQCUch/cWAiAKzVRNAPB/LTMQjSaFyLwKwpnpI3YbUKuzrFnFU7negw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "11.1.1": {"name": "joi", "version": "11.1.1", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "isemail": "3.x.x"}, "devDependencies": {"lab": "14.x.x", "hapitoc": "1.x.x"}, "dist": {"shasum": "08194537d3b9c796bac8666deb3a825c1a5d83c1", "tarball": "https://registry.npmjs.org/joi/-/joi-11.1.1.tgz", "integrity": "sha512-hffQzH42mYLvUCqhUPZZGegiiIjVvHcOV8mrxXPci8qZFOp2sHK4778GPyI3ZlvqTOHs8qZN6DovDnBR1slO4g==", "signatures": [{"sig": "MEUCIQDYtIhQ1dmS4aYWIM8yOpyphTYanNAReVIIwJR+GmltYwIgb6iiLrCgPE6UGnnTiVAJqj21nkGZ7Ced3MKZK9rcS9s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "11.2.0": {"name": "joi", "version": "11.2.0", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "isemail": "3.x.x"}, "devDependencies": {"lab": "14.x.x", "hapitoc": "1.x.x"}, "dist": {"shasum": "31a86929d4a125057212340d8ad5442ad4f1da9b", "tarball": "https://registry.npmjs.org/joi/-/joi-11.2.0.tgz", "integrity": "sha512-q5EQ2G0bO8QbZhxUKnmrO8nVgibpTIScQQE+fEd9OjMQom/b7ToaMWlzplZirlJvOg6YKrEQ7nRX2cQ85ndP6w==", "signatures": [{"sig": "MEQCIFzTdryY1Z9S+PE3iHR3srTjxE60aMrIALd4ZlW7UP9NAiB2Jye6pk3wIDdPEtoYRdFzDPo3EboHFMNqxchTTjcrmw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "11.3.0": {"name": "joi", "version": "11.3.0", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "isemail": "3.x.x"}, "devDependencies": {"lab": "14.x.x", "hapitoc": "1.x.x"}, "dist": {"shasum": "3be20a7e7f198b1af3bcce6570ba71ff17f4201b", "tarball": "https://registry.npmjs.org/joi/-/joi-11.3.0.tgz", "integrity": "sha512-uX4kgvcpkhOnWaZR4XAKNCjxbRylatfEqomdGWghU/bmlBUHhfCeAbqhy4vdhLw2ctblKWkUAxGYcvRQCW6KGA==", "signatures": [{"sig": "MEUCIGbAYRzsE8sCKr2uVvR0+tZ4Kaz6WPmF/AL9XXz2f1+gAiEAuHn0RoIRIdM+ag8exozIQhTJQrspzU78PiDrXApwE4A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "11.3.2": {"name": "joi", "version": "11.3.2", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "isemail": "3.x.x"}, "devDependencies": {"lab": "14.x.x", "hapitoc": "1.x.x"}, "dist": {"shasum": "e3ed11fef34131b4a10eefd9de606a07055ac10c", "tarball": "https://registry.npmjs.org/joi/-/joi-11.3.2.tgz", "integrity": "sha512-YLR+6l5eaV8/tM6zvDmYpOh1i4no4n0CmKbwH1xEbSUpSgf8MOqWLtcuMmwcfjKXmBC1Y9ZZDRMUDpXuEyQDIg==", "signatures": [{"sig": "MEUCIQCIYvUxhqdKA//wNfjg2VjpZk5uxufiE1ym6cngr/mmdgIgKwskJcDbJPZ9s4Rh0oHOE3Sp/hIyCz9nh9Eprufjwm4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "11.3.3": {"name": "joi", "version": "11.3.3", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "isemail": "3.x.x"}, "devDependencies": {"lab": "14.x.x", "hapitoc": "1.x.x"}, "dist": {"shasum": "dcc95755df2b1a7576c21375728c05e4b8a5c924", "tarball": "https://registry.npmjs.org/joi/-/joi-11.3.3.tgz", "integrity": "sha512-bSugc0Bw2IuKz6ppVP35nBjAmXDXhfFU+rvn19xWladv09/lcOQW2fAWA/ZrE/xZniIksaj03AXYKWQEo0w43A==", "signatures": [{"sig": "MEQCICryuiSlVCAJW4drtR/L2G5NsmoQJEXbEptxp/B5AnLyAiB8rqp4/AuTMTNX/tgVhK/IWdL6FpFGlVhqf1/xvRfudA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "11.3.4": {"name": "joi", "version": "11.3.4", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "isemail": "3.x.x"}, "devDependencies": {"lab": "14.x.x", "hapitoc": "1.x.x"}, "dist": {"shasum": "c25fc2598c3847865f92c51b5249b519af3e51cb", "tarball": "https://registry.npmjs.org/joi/-/joi-11.3.4.tgz", "integrity": "sha512-2eytkbHJ0qhXaNgElrKFrruv1sZ8MtuFg7fJe1Linb028x/3+flYsrtZIc+7wYVY7oCtXv6Ou7dCzAZaV/Qubg==", "signatures": [{"sig": "MEUCIQCE/OKuYjKgQsaT1BNE3QhBQNPDvrisKZPIv1toNifw6QIgBuuhpvV8Y8yadnICeybWKb4AU9GZAElRrTGCyk/qTQk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "11.4.0": {"name": "joi", "version": "11.4.0", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "isemail": "3.x.x"}, "devDependencies": {"lab": "14.x.x", "hapitoc": "1.x.x"}, "dist": {"shasum": "f674897537b625e9ac3d0b7e1604c828ad913ccb", "tarball": "https://registry.npmjs.org/joi/-/joi-11.4.0.tgz", "integrity": "sha512-O7Uw+w/zEWgbL6OcHbyACKSj0PkQeUgmehdoXVSxt92QFCq4+1390Rwh5moI2K/OgC7D8RHRZqHZxT2husMJHA==", "signatures": [{"sig": "MEUCIBMYko3vYBwQ1q44kEqPWEBMGBcLUxtJ07IIQtA8vhv9AiEA6HTl70cdacdOsvOcd0yitRg7jVFGLWWV3o9HC1z0+EM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "12.0.0": {"name": "joi", "version": "12.0.0", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "isemail": "3.x.x"}, "devDependencies": {"lab": "14.x.x", "hapitoc": "1.x.x"}, "dist": {"shasum": "46f55e68f4d9628f01bbb695902c8b307ad8d33a", "tarball": "https://registry.npmjs.org/joi/-/joi-12.0.0.tgz", "integrity": "sha512-z0FNlV4NGgjQN1fdtHYXf5kmgludM65fG/JlXzU6+rwkt9U5UWuXVYnXa2FpK0u6+qBuCmrm5byPNuiiddAHvQ==", "signatures": [{"sig": "MEUCIHc/nznRbkclyZ86AGfZDyBs5olaccCVsjf+k0q+XlKPAiEAt8c62QAOM+FL+gWAKlYDjFmxUY/195l7jKPotnfFInc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "13.0.0": {"name": "joi", "version": "13.0.0", "dependencies": {"hoek": "5.x.x", "topo": "3.x.x", "isemail": "3.x.x"}, "devDependencies": {"lab": "14.x.x", "hapitoc": "1.x.x"}, "dist": {"shasum": "9bfb1603164cac8787350ff6d41ea9769780707e", "tarball": "https://registry.npmjs.org/joi/-/joi-13.0.0.tgz", "integrity": "sha512-MelJVGcIxm7CqInXnDvubmjnOQgJudjQDcsRKxYcDckxaZao+1q5MRp9cNZ6ZTbKuN714PLljNDEWD57pk4bxw==", "signatures": [{"sig": "MEQCID4MzgluoXTZmN8PYffuZp6voo5h3qMb4iZMknPT1xrjAiBJK0gUqw2V3IB5BqCfY01nBAOWMMy8l3G8JPg9lx9T5A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=8.3.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "13.0.1": {"name": "joi", "version": "13.0.1", "dependencies": {"hoek": "5.x.x", "topo": "3.x.x", "isemail": "3.x.x"}, "devDependencies": {"lab": "14.x.x", "hapitoc": "1.x.x"}, "dist": {"shasum": "97df285450e3ff5bc4db9eccf40a1cd5cf3ec189", "tarball": "https://registry.npmjs.org/joi/-/joi-13.0.1.tgz", "integrity": "sha512-ChTMfmbIg5yrN9pUdeaLL8vzylMQhUteXiXa1MWINsMUs3jTQ8I87lUZwR5GdfCLJlpK04U7UgrxgmU8Zp7PhQ==", "signatures": [{"sig": "MEYCIQDbwiRk0YKiAPHSrWai+HnfZpGHrJeq1asnqrzU9KK38gIhAIQzGGSmjWy8AnHGTLk9UVtY1r6Ru1lY85aSZDwYtAZv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=8.3.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "13.0.2": {"name": "joi", "version": "13.0.2", "dependencies": {"hoek": "5.x.x", "topo": "3.x.x", "isemail": "3.x.x"}, "devDependencies": {"lab": "15.x.x", "code": "5.x.x", "hapitoc": "1.x.x"}, "dist": {"shasum": "8cc57a573b7c0b64108fa6fd85061c20fcb0d6b0", "tarball": "https://registry.npmjs.org/joi/-/joi-13.0.2.tgz", "integrity": "sha512-kVka3LaHQyENvcMW4WJPSepGM43oCofcKxfs9HbbKd/FrwBAAt4lNNTPKOzSMmV53GIspmNO4U3O2TzoGvxxCA==", "signatures": [{"sig": "MEUCIBTEvvjN5wWe2RNORVNAEkFf3AEypi2NneMaOR/jLhNQAiEAnzxedZO4FHvEbu3fU0DAIrd99ab3SCVokw3Aa0Icheo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=8.9.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "13.1.0": {"name": "joi", "version": "13.1.0", "dependencies": {"hoek": "5.x.x", "topo": "3.x.x", "isemail": "3.x.x"}, "devDependencies": {"lab": "15.x.x", "code": "5.x.x", "hapitoc": "1.x.x"}, "dist": {"shasum": "59e7b8714b932a1e342c3583d5841d7169ff1822", "tarball": "https://registry.npmjs.org/joi/-/joi-13.1.0.tgz", "integrity": "sha512-x6pGmDYI6hwNi3skP6irQqRaJntzeaWmZ4rsnjc/NTlf6P5Gp3Aw/O8REe8oLJ6wPhrzd9K3RW1m3Yz/Hx4Weg==", "signatures": [{"sig": "MEUCIQDC66wxI7y4vSqAxJCgL8hk6RbjlS5NCcLQL8yQ6OqzFQIgcxypVw42WC7sF5d1ZgOVv/aoI0j3vi4zWIq9dzw66eY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=8.9.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "13.1.1": {"name": "joi", "version": "13.1.1", "dependencies": {"hoek": "5.x.x", "topo": "3.x.x", "isemail": "3.x.x"}, "devDependencies": {"lab": "15.x.x", "code": "5.x.x", "hapitoc": "1.x.x"}, "dist": {"shasum": "1f306a5c3ded496da46629915d09e1ae3825185c", "tarball": "https://registry.npmjs.org/joi/-/joi-13.1.1.tgz", "integrity": "sha512-Y44bDwIoeCjFDRO18VaMRc0hIdPkLbZaF2VqU7t1tCcno3S3XzsmlYYpOu0Qk6nkzoI5RSao7W57NTvPKxbkcg==", "signatures": [{"sig": "MEUCIEk4694otHcpH0BvE9i+uSNRqJWr/yXdceljL9HxkdfrAiEAyH0u0L2SCj5B9JHLf4KmZKYpi4u0Ohy5VvxTKXobXEA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=8.9.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "13.1.2": {"name": "joi", "version": "13.1.2", "dependencies": {"hoek": "5.x.x", "topo": "3.x.x", "isemail": "3.x.x"}, "devDependencies": {"lab": "15.x.x", "code": "5.x.x", "hapitoc": "1.x.x"}, "dist": {"shasum": "b2db260323cc7f919fafa51e09e2275bd089a97e", "tarball": "https://registry.npmjs.org/joi/-/joi-13.1.2.tgz", "integrity": "sha512-bZZSQYW5lPXenOfENvgCBPb9+H6E6MeNWcMtikI04fKphj5tvFL9TOb+H2apJzbCrRw/jebjTH8z6IHLpBytGg==", "signatures": [{"sig": "MEUCIG4quZYrMe1GfK5kjs1tAxxwwYaUr+znhuFnQ3hZQeZBAiEAi8mctRai8AX2/Whk9jb/bE8zw3f87kB308zMGxZWeXk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=8.9.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "13.2.0": {"name": "joi", "version": "13.2.0", "dependencies": {"hoek": "5.x.x", "topo": "3.x.x", "isemail": "3.x.x"}, "devDependencies": {"lab": "15.x.x", "code": "5.x.x", "hapitoc": "1.x.x"}, "dist": {"shasum": "72307f1765bb40b068361f9368a4ba1092b8478e", "tarball": "https://registry.npmjs.org/joi/-/joi-13.2.0.tgz", "fileCount": 26, "integrity": "sha512-VUzQwyCrmT2lIpxBCYq26dcK9veCQzDh84gQnCtaxCa8ePohX8JZVVsIb+E66kCUUcIvzeIpifa6eZuzqTZ3NA==", "signatures": [{"sig": "MEYCIQD3M8hEejSHppaZxdI71n2gKGUpVbEKBGijYx4wnQviJQIhANXmOZsPQOKu9O1z9Xicg/8kDvdEl71c+b5xT8bIrc6b", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 173869, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa1eNqCRA9TVsSAnZWagAA89UP/0qWHKbfpR1Q2np+d3lS\nsl3msHZgmcHoF0sq50ZOiEs6t5y2WnLS5FNM9xPgGPGOz+ctjA094NIqMtOJ\nsvxHZH+B2Y0fBWTNJVweIQtq1w3Fa3IoWTcIlJ2J6Nsjm96h8zZszqamWa/l\nEMjIqcf2yFpNGY1njfXHuWRoEBEsDXefL+A4qSAXsDoFmXnogqPIe9kLUvl7\nBgU0b/e1sCcXyMHu3qYn0jtE/KjjmNRhEMJ0TdQ6/DuzPH1F3m3Fjwux1dbw\nYVJCgGCJ2qitAe3ZN3+6gp1TvbMotfdGmnID4Qm5wo9xsfjwO+6nsS+E+CGw\nAMGFjrDEGgMJDK7/3OYhQeVFFp94K1WPrRiaSKhKXpq6ohKKARGlPn38++4Q\nE/GC47+I4LJrPbC6d4kfDRdE01MLxAPVEzuFgVC6nCIZfrBahnsW5JLaoPbC\naG9w4W+PCnJx2wE+8UBto8ayNl/1+pqSK8J9sIfYUCnggPWoLFtksoDbSmCQ\nzRaGL7+VOoHvYXgTYFjvd4maNKF+mxEV4ZmnI5MaqV+JuKME+tRzGI7ZlP60\niIS88fV+hwJSn4nfpwAYOx2AaB4mqF255frlru5CQ/Xi8wBfdg389eah0WyX\n7Vp69dgDWuyFQTIGv7/2wTlDDazn37UuAWScY4R8OH5h0EThH5+FlKnZe5h/\ngdbc\r\n=iS+Y\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.9.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "13.3.0": {"name": "joi", "version": "13.3.0", "dependencies": {"hoek": "5.x.x", "topo": "3.x.x", "isemail": "3.x.x"}, "devDependencies": {"lab": "15.x.x", "code": "5.x.x", "hapitoc": "1.x.x"}, "dist": {"shasum": "4defd4333b539c5d10e444ab44f5a5583480f17c", "tarball": "https://registry.npmjs.org/joi/-/joi-13.3.0.tgz", "fileCount": 26, "integrity": "sha512-iF6jEYVfBIoYXztYymia1JfuoVbxBNuOcwdbsdoGin9/jjhBLhonKmfTQOvePss8r8v4tU4JOcNmYPHZzKEFag==", "signatures": [{"sig": "MEYCIQCoixJ05y6JBuLOoVQBBbSNz+d3wXC7ijTnC60w5BNRMQIhAMdnx9ZFIBUlPyaHd5kXZ2vQ56zx89dB9Mlqwvk2u6r+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 176040, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa8dHVCRA9TVsSAnZWagAAvlsP/0Jp6jqz6xUzEhy+uZNR\nkFCYrulOdrWaMw/hxLqa1i8s5ebl4/4RMt6JHM1JESZ3vsza0SIAdBCwwNgc\nCnRBkGc7XJ/6dnasnyW//SZhEGTdAbLEkrF9iTc8bNlh9uqWaGPgukIY95jm\nThED4TKGwm+6XMLFCti3pUJ7EPfHbm44OvAgDfRkt+wD+mVl+y38HPZs7frt\nTUYatpzrNyUJ0gbKb8m841dHBVC/kKgNEdMoQuetRr9qFqboqGHHwQ0yS87z\ndJt2B70fF7spziiXU6rACSDJ3+4HHrPXi+LOPrIUy9M/ZEqJgBMj81TIRWzo\nHSsTbqB32GpwIVeNJnaeoJ4/Drq9Ehd13ERaI6eg8HdzDiMFWORKmh6ZTbDJ\n4tj6upJurk6ttCXe40xr9R8QgPAqkxdpEhOzayuj02UXPDqvDnE9Sx3KnxlQ\nfoHt1nE15n0gGHQ62oQKdabol2V+11gDsDHdmU5JvMHmTHSYYB1JyyI6kyXJ\n5/z7JSZajoL7i5u4aXIx6DcD875bB+d8/EwkXR/Nkssau6/OoTf63dhMebfl\npw4dzsGGxSK4vZBcvZPCb89LUwXbGB+fujH36GaNBZBVe5xFA8ZAlVCCgrYk\nOFVH9trXvSqVjyfGpN7BW1FVpXq6IDGU61u8VLgr5FSOe/7iSiVFQcr80hqA\ngCnN\r\n=SWcZ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.9.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "13.4.0": {"name": "joi", "version": "13.4.0", "dependencies": {"hoek": "5.x.x", "topo": "3.x.x", "isemail": "3.x.x"}, "devDependencies": {"lab": "15.x.x", "code": "5.x.x", "hapitoc": "1.x.x"}, "dist": {"shasum": "afc359ee3d8bc5f9b9ba6cdc31b46d44af14cecc", "tarball": "https://registry.npmjs.org/joi/-/joi-13.4.0.tgz", "fileCount": 26, "integrity": "sha512-JuK4GjEu6j7zr9FuVe2MAseZ6si/8/HaY0qMAejfDFHp7jcH4OKE937mIHM5VT4xDS0q7lpQbszbxKV9rm0yUg==", "signatures": [{"sig": "MEUCIQDvxK1Gn9CvXX5Nz59toWts8zWqnFqzKk5ksablrvIgpQIgH+ascZJCPJU1IsYhDRT89Y7wesQ+B/xQOwp4jNh5uiA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 177085, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbF6zcCRA9TVsSAnZWagAAwCwP/0e2J4OjLrQqXl1mk9iR\nbH+xdrZnAbyadBCV/OC1u3r2EvpDVnyggkhfUh1+SB5oiP0pzy5mPwnptRm+\nHERxMO4cAeept6r1nyOtnyBPiCFwZtwWjvQx3yKncOC7teap9Syb3Wc2jIhm\n60XbFjQ5YLqu1xmZebllmpA9i3MsQHpbL1ZemAc2qQGjdCt3XFVYt0opYVBQ\nZ0Kek5OsTwOKPQK4I+dszab5e5ZrfUg4eezUG79WllLBr7RSCr7TWLlGPNI+\nGZfmjRt5IEtk1xm9xCqvg9mHYpqtAve9Ly6i0cOx0tbZ0NLQGCAlF0bnMmfz\nqu9JuYBSnvREl1Qrn47ZWRFy4jSc7aJ2zQzmS9kn1b0cG4A054syZgit7ekY\nU9NIrK1snNZyBXlS7LK8jB18MSP8OXEKs0ycOHAbnB99hA7UFBmgLqMbJSUW\nI5TNAEh9txrVu9hhxmIkZrebfRrOgrNF1N8Wpnfk9ELBr+lSCVfaGjdFMH6j\nnSA1tXnHxhlqjNLsygZ7C/bFvwydkXLlA7ldpxsfXdOjy7Q2O+KPgXEAS1ZD\njCtQEclVdkWX+NqlAGJDEe29kqVpbNKqhEfLi1SLQ4rdXAkKYu2u/gxKg1Gz\nYoWzbfjgWgJFVRV+LgqO9ng9uIpzvV+W10QGiaVMKdaG8sd7ShEqyYLh2zaI\nDuKs\r\n=ZTD/\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.9.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "13.5.0": {"name": "joi", "version": "13.5.0", "dependencies": {"hoek": "5.x.x", "topo": "3.x.x", "isemail": "3.x.x"}, "devDependencies": {"lab": "15.x.x", "code": "5.x.x", "hapitoc": "1.x.x"}, "dist": {"shasum": "c84ce7d539f80bfad1b6232b6775cf18da178634", "tarball": "https://registry.npmjs.org/joi/-/joi-13.5.0.tgz", "fileCount": 26, "integrity": "sha512-brcQASga7Bo/czBjP+0lYFniQCuX927OFsmUmGz8Xre77PeUe0EOaXmVMFmb1GQgeVfsTa2w6mYulODKRXKpag==", "signatures": [{"sig": "MEQCIHILfi7Iz+LwuyuphYzABPaZSTp1lLEKMnrrS1BjSe6GAiAuBn2i6gVuM+rtNKp2U6VQaHG3B90G/XUMmxrxiq4fpQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 179887, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbXy9+CRA9TVsSAnZWagAA6g4P/2zADqOdgr3Ly8amSDUx\ne7mdHjCqG3OSp23TgR/9wdd+S+ZSPRKBE/urSxVPlQMdvp/VV+7zosQCHpkr\nqEQbyaPPUIkYIxS/MOhKrXEFP81aNM59+nQsnJH2DVXGWnQCtY6N9vtTWImU\nlU6kZU9Y1Eh3CXiGftj4h0UeAD+E05SGR0joEmNFGJwB+Pm8+BWHEBVoN4kR\nU4PX0TUL2ThnxLRmdmADX819pk+JQJ1xSeyK9glgYud66Ysnbf3/Mrq19832\nZhGS/eoCtql7xhA+mk/y5QrPWVJIIl2Am6+8llSnrIlfhQun+jiUhO4Bn+yL\naDbPMeBLIW67XNatolKN1tzJwacWmAiGA0KQcCopGDUvrwdylv+DGRKZtKG+\n/p5E6XT5c/f2mo0eamfzXjao1WKeng9JchEQ5lapWCBuAMINsOgMmbpGtpqn\nfogcIM8bel0Z2QiIof+yr4Cl89lPaLnX5gJKVfTsusunJ28hdkJR90gT/1mj\nephC3YGyAiKA44I2Nig4jGfooOu5Ia41r9qEGZaLalAP19lBshwIbwmvs4Lr\nBM5lLYlu9cX5Ge5XsM59X6Ykpvp2mLwR0dLcg0VlRNThvxHyPvo3YN6jZxcr\nDdf1BuvxYnqSiaQ08ujshL0NldTfEM2zR5ikkdvD6PjMyei8OVM1HcjTQLqE\nIQGU\r\n=XMcW\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.9.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "13.5.1": {"name": "joi", "version": "13.5.1", "dependencies": {"hoek": "5.x.x", "topo": "3.x.x", "isemail": "3.x.x"}, "devDependencies": {"lab": "15.x.x", "code": "5.x.x", "hapitoc": "1.x.x"}, "dist": {"shasum": "5b367ccf26683622932f2fa34d35f1103687f1df", "tarball": "https://registry.npmjs.org/joi/-/joi-13.5.1.tgz", "fileCount": 26, "integrity": "sha512-tEa/bEwhpMAOLrPwdHJI6Ud9ir70vjKhudMt76vIKDUXRHouLsvGQDqE1kneipwWw520jHz8N3asXQZ1evT1Ug==", "signatures": [{"sig": "MEQCIBylXZ4CxLg0SGHl2cS/AAyzz2agV2bRL6I74skn2eg3AiA0YZK5zrSa93vwzU5fNN06WRwMLApr735817zRg1dhWQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 179887, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbXzfOCRA9TVsSAnZWagAA5JQP/06EWxzg3b1cLRXhu7rL\ny+c90UpQoxfIinp1TQp0psaEmTUjmKAgTerGa9sqX7Io5KTZ3P3yZ2X1mNge\nN5cz73hJutHOi4qhNpKNER9tXzCCb60gPA51/42wWOVY+NP0OemszrYOoW1d\n2aJIT+c1awF/jQeq6EJvjnYBjGW2NxkGfVZmd/y7okEpKRFffHcHZ6tpsMn/\ncceYcOaBU0uj7UkvNcna2/yA/nFsniMfy3Aqw0N1JLlLvHJtMHzsmd9WuiSI\nY6DH/sSI3zcSzMK2rCv2Qq75bw4XM4zS/Lx6PZy66L0s3e3YkDfyxJYI7tWE\nSiQCw7L/oMokyvEFmwxiLGVM/4tMtuil5qkYtQiMkLK2Dg5erPWRE1RUZa1L\n29wZVYIcu36rOmVJX2GEGtn1hqdjkAmqNklXMm1aHO/84I+KeijyAgXgZtT3\nvP7uVPSkE/+3et8dQJCgAAh92tEU4M1v/5inqj/PZc6gAfBrQotYGdkTmeVU\n0uGoFHA0JoDl4t3XJ8n13ShnifqB8r++ayVjdNPBjIjw6pe03Sqdej+lF5UC\nLWJ9NKQddIh+8+g5NenMcje/ZfjtYiXYxh64S1oVrYFQMR+0onKaTmQa8NC8\nZWo+NrfmkNyWqxWktL2j0LWhhtQ8dLVQ1v3tySxX9nVhW8EmVzyhyWPjY5no\noNj8\r\n=SjNf\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.9.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "13.5.2": {"name": "joi", "version": "13.5.2", "dependencies": {"hoek": "5.x.x", "topo": "3.x.x", "isemail": "3.x.x"}, "devDependencies": {"lab": "15.x.x", "code": "5.x.x", "hapitoc": "1.x.x"}, "dist": {"shasum": "32207c85fa76d889f1e971c7eaaf69b232259a91", "tarball": "https://registry.npmjs.org/joi/-/joi-13.5.2.tgz", "fileCount": 26, "integrity": "sha512-3HrFXLC57iU5CzYth3cJRdYEo4/Dr+tXmCQ+BHyiTTKnKxJ9ICkI/WJGPwUUXj3dWA4tO2hwZO5oCdBNhAYuRg==", "signatures": [{"sig": "MEUCIEAj7YxO5hyqExNNFJywaWoSiewmaUKR2desfnelCjVVAiEA1d4y/FAcw9ePgpgwgqzPCYbKRdzsnXD0EN0LYyCjtVQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 179960, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbX2DMCRA9TVsSAnZWagAAGdsQAJtruUhk9ke021zsUEm+\nN7fsYRiyTsQ7VBUg3BT7Ucq8GC2h4hN5ImNWgJOLlsndouKnbr7SbxRL68tE\nK2wj0sKLmPXhNOHo215568fQkV45Cit2wFbNfEMDz8PZ2KF2+jP0sH1P7C2O\nBgmkekw5fmvoLno+AF9beKuOXzrVX5P7fB+GS3TG/lc3wY3M70wGVwWUPVZm\n5Qt7qC25e3W7jZdAFwLI6+QZnvwIErKgX/XWj/rb/c09DBb6sGDYzsI2+CnE\nFJiKPQFVRXXSz+WPbK/zmWD/P6gFKV1/5Bj4sQG0zUTK2A54qGbF/rlkrHAk\nkML13GrqUE4qoqdaNisidFichjqfN1oq6/2kLiDYLzHjNktocrJ0yy+gf2nn\nXJL2H87CFz2L2hGp/1UaIn4bU75H2UBk2Y6NBLBm+aAmx6ItRrN6quy6vi5b\n2ylpAoVyoFSSiO+jL7O32rnWWZWPuQzw0A8287eHfe0xJj8ggI8qAUw9yuuu\nPTLDVa33q1VLSmzJamFrSlSNyGYBg3hLeFcjbd12eccC4D51B0TCSQ4VSEPH\nqBEjdRowubmAykVJXnYqszQMo3M1CMZd+cFHRyyFjZOyotrbiQPryZc91Fxh\nWsQsh5ce49d047czhEdr7iMbtxI4HWYSg+XkbAoa+PJF+B463sg8VjvriGJ1\n1jeJ\r\n=fArZ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.9.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "13.6.0": {"name": "joi", "version": "13.6.0", "dependencies": {"hoek": "5.x.x", "topo": "3.x.x", "isemail": "3.x.x"}, "devDependencies": {"lab": "15.x.x", "code": "5.x.x", "hapitoc": "1.x.x"}, "dist": {"shasum": "877d820e3ad688a49c32421ffefc746bfbe2d0a0", "tarball": "https://registry.npmjs.org/joi/-/joi-13.6.0.tgz", "fileCount": 26, "integrity": "sha512-E4QB0yRgEa6ZZKcSHJuBC+QeAwy+akCG0Bsa9edLqljyhlr+GuGDSmXYW1q7sj/FuAPy+ECUI3evVtK52tVfwg==", "signatures": [{"sig": "MEQCIEDR4PveIpDoH/LwTdmmBgFgNdb+NxZ2g0nBZgsXPyr9AiBQZePilTjpjFYoVuORSgcTRkrkO2aV8O7/jt9KUA1kUw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 179897, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbbkqCRA9TVsSAnZWagAA79YP/0lLj/IayKM/Q0bzr4j+\n40t2NuovJxMASQSVfKDOOWcfRVAWi4T7MaV0nXm0+wu5EEwwIKTPegljsPZA\nj40qG8hoAKEEvnW4QvFXUrtcmmt3lgHEIhtBEX/Ojv+INC8bi0bFrir56miV\niPLuSXFLNeA7GMH83iVAPdw3gvmAreSN67Ayi4OCGQU57FsCWtfwc0oXdJFQ\nff9Bb3gakuwRU4z7m/Hr1asz/cVbK7GojWnouC9g7K6j69oBJhgVHT5hdY94\nWhD94hID/4Kh62xeXFV7Hc1EsJOIGa4+S+HdHzs4dDB6HL7RaxwXmS/JsZmt\nW/UjlktWKGxIxG2YbvexrL8Y91+r/132Ecz5nef+XxOHqCjSmv2Pt0SYhsNO\nbG+VeNXR3Kl1MhSi/q+KX6JBY0PE9vJWEpcggBqeIf7zJ046aKrJLZR/MAgz\n6mwQCypQfBdRMypeCZYqD7dMHQlHufeVM0lP2sqaYa7oxdfMWKQ++RviuA9m\ndYTkRLnqCKUlUk0V7PeUeIZGSljliojT4Etnvac1t16opOjPIu8Lu7bmoOh8\n5Dz69kyRPtR/vBUfzJ9jYhg5fo6ujoUqukiUtC8HrBoaqmfYUlwO+LTrafOw\nkQOqy1uJFVWcV9FOqVoDXznK+xu8j/QgxNSVBeVQwokznAq8uEPYQ3GTkKrd\nj+mm\r\n=hDtT\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.9.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "13.7.0": {"name": "joi", "version": "13.7.0", "dependencies": {"hoek": "5.x.x", "topo": "3.x.x", "isemail": "3.x.x"}, "devDependencies": {"lab": "15.x.x", "code": "5.x.x", "hapitoc": "1.x.x"}, "dist": {"shasum": "cfd85ebfe67e8a1900432400b4d03bbd93fb879f", "tarball": "https://registry.npmjs.org/joi/-/joi-13.7.0.tgz", "fileCount": 27, "integrity": "sha512-xuY5VkHfeOYK3Hdi91ulocfuFopwgbSORmIwzcwHKESQhC7w1kD5jaVSPnqDxS2I8t3RZ9omCKAxNwXN5zG1/Q==", "signatures": [{"sig": "MEQCIGnSUABjx5PK3YSsufH+UGtPyrYm0EVKds3+0SC35Z4cAiAiEo64zLhDOjujFHb0UYl7JBR7eHN/G//Kp5LTejqb/w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 182434, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbrzyTCRA9TVsSAnZWagAAkSAP/AyX63qSDf4Nhk0O6Fp/\nPTBTqLhC8CSqmOxtBmqvpAlLKpZVEuKOgh04faMbk3QJDi9L/yXMaIeYcF22\nQPrTga96/5DTvWeTSBhpZjCz3gzjsGXqonwXT1rb9VoUOV6s/6QrG1Sg/zw/\nfLbpxG+oeIWAIFXsXNFh2sFqajAz6gxCOup2apu8AK/vCSTqQ05B7tjr3cKq\nBrdc/eZ+lPmrUHiTtbWCUQNtaOcerc9q+OdwDrUAQYI84uW/EYN2c93TgO23\nxt5TCkPO7qOGHX7yLjXnZj7y3EbhT7fMvqDZYWM2+Sn90Y3ZEalIlhxQ22jS\nsWy/PrJZtsKhQfmv7bsW21K00ESpAkZsWskDQKCx+H6CgtvjNUpk1hotdB7W\nPqvIbZqrHizAhldve79o2LZg7DIdF+EXV7ubhCtVb02HExcU9fxVUxLQJc8E\n+sK8XknAK0tnBp+QlgSRc9tdfMzf/+qIiy8z+qRDg4UoragwPZ170UxcwL4c\nk/udGO2Qq65Vr1ZBUEtsphT8mwlbZipPV92uMLvINHO+LkovyB7jwaEAjejO\np9T5Ehjz/ew7xM62P7w+cUiViOc9MNT71VQn6BPW1RcG3hwQOgMFgMK3vAIy\n+Gz8rTYlmEg6f0NsCV5DZXepoTLim9eXVQRNt0hZ2RVoy/8ikx61GItoPo+U\np+Lo\r\n=99c4\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.9.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "14.0.0": {"name": "joi", "version": "14.0.0", "dependencies": {"hoek": "5.x.x", "topo": "3.x.x", "isemail": "3.x.x"}, "devDependencies": {"lab": "16.x.x", "code": "5.x.x", "hapitoc": "1.x.x"}, "dist": {"shasum": "05a206b259e702f426eb2b2e523d642fb383e6ad", "tarball": "https://registry.npmjs.org/joi/-/joi-14.0.0.tgz", "fileCount": 27, "integrity": "sha512-jEu+bPFcsgdPr85hVyjb5D5grxLEZniT6AB1vjewrRDbuYxe2r5quyxs3E32dF8fCXcaJnlRSy4jehSpDuNMNg==", "signatures": [{"sig": "MEUCIQD9m9nJkjTdROtRpgorby/pinXDKVLqGSZ/y7DO+L6QSQIgXSTxg7Lmek4XMVVoWtIWJp0OzHEhILIn6r2NOAOVogk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 188238, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbw2o9CRA9TVsSAnZWagAAwBsQAKL/81piyu/UFIwlCgG9\n7mLa5lBIv1nv+fxSWPtZYrDRhA+1oyak2+qWdob5OZOhgB6qBm4mM9Om2fdJ\nh3GZuGLjtJH2q7rhr0+eGTHcE6ueOUkBnGMoQdQ8eAmv6ySXIdWWAhJPUesy\novGNMXE+Jj99GFIi6PS/YuneWMgr1Yq8jV7sAlXAJdAvzr580DS1FSrghqJN\nK1N4bOTW/tp+8LQm7dO7fh+X449wJLvZr4ScT+GnZUqSiUYO+o+eaqI0Ri7B\nUlA2OtPdMcdqpcoL1AxLvEjS+76LHb6OxvnFZFsQdd+1YNgcwaAaDiJSDKqd\n9LpMPAQArd7z2/q99pQ6FcNMVegbWlRnudbbVFGjhuY5MatfjCSMsx6Ca9dh\na+oUQGJBWE9ziXQbQlCBaIz899fM7z0zssBbiRRxauuiAO+c3gIaTLOIX6W+\nZOtIJ94kjbtlex2ld/XgKtqHfozyBKyyXhy44OHfcOWE4GrTwIPCf+NGhvoj\n+gbocYYlkMu38N6ms1Qz90CLrhfV050rmkbiVjetGY/CfqPm+r1qb49p3hsH\n3J/XQahzzY6zDYl6aA5slBkgiP7K9zt1fBIbe2Z8LaEctDC1dbfJyhPP33X6\nui/tWsO1FxpJ4VkM2VJawO3slhLGcwXSOHAbQ/EpLPG/G+cF5xZrFGHyzYy1\neXkJ\r\n=tOPz\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.9.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "14.0.1": {"name": "joi", "version": "14.0.1", "dependencies": {"hoek": "5.x.x", "topo": "3.x.x", "isemail": "3.x.x"}, "devDependencies": {"lab": "16.x.x", "code": "5.x.x", "hapitoc": "1.x.x"}, "dist": {"shasum": "5bc53e8fff6e6da19d6cd8c76e2afe5c0815a2d3", "tarball": "https://registry.npmjs.org/joi/-/joi-14.0.1.tgz", "fileCount": 29, "integrity": "sha512-M8yD7j1G/9UfJ40h97ophm/flC0zDXUn1RrK3GEm2IsyxOOJAnjQiYLLFS5p75gDGtaKwAPMB4JNLLr5yvCadw==", "signatures": [{"sig": "MEUCIEMhMK6tP2MIl3A4Fz0LaF8AvdjmAXpX6x/e0ZEVjdpWAiEAha0P019zQl5Jt8a3pQKmjz+Wv+O0ySC1hXtmMnHm1W4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 189073, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbzj7gCRA9TVsSAnZWagAAgzMP/A0DPcZe9YdBnL7tWXDN\n9+b8GRQacg1CKnwdgdHOzjdbYMzsJU4xGfiXmpWzQY5va3aSpfdTZnGnwzGA\nnNTyhnsvpBcCVJc0D5Ne+0LIGnHTq16q7NAmIAnda52V6l0wrgHWYXGedRxc\ns532RDmiPvwQ5GA1//SmIb7D2uW4Y+uH6Rxbmrx59056HfmiuOb49cRgY+cI\nlM367SlaVTG6FlbDztWZrEK7LgnKAyuRpGC3a1uQg+eT3Pr0GASceUtdivn5\nzpALjG8EdGR/nF5JAuiAKbpJQuJJvMdhqGiuJEluZF/ZO0foE76W+AikUPrC\n26vEb6DiMc5EBC/b1o+zGU1r6lWuDVLgmPT9iTM38YEqWSWzVStMXXbMJhiJ\nZNNzX6UceV8z9A2suXeEiwboGvgkg7+F0V4OAm06lyQKKhWQRh7pdLFf9DMk\nJ/iNC5KEnGl1YY33jPMKcGAPPoqGOmCNPbAmXe0UCLFNNeAeXdhwnD1Uipa5\nYvtK/npg1ZF4WLocs5uvGcNMlZ/lXnHFTcUrKtUuGol+mbMBVkFVp/da657Y\n9y4o6iu+x38gyPW4a28EKkEMrWoxFPFW/uTDZKG+K9a4G+GNeglWHp5VJhxT\nGK4rqpbKYZ0A+3YiqwNe/f9WIGP9wfxW0qCArPJDTHJGnwoYktYzOTbbRC5a\ngUHP\r\n=zMql\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.9.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "14.0.2": {"name": "joi", "version": "14.0.2", "dependencies": {"hoek": "5.x.x", "topo": "3.x.x", "isemail": "3.x.x"}, "devDependencies": {"lab": "17.x.x", "code": "5.x.x", "hapitoc": "1.x.x"}, "dist": {"shasum": "b8116fa85d35bf0be5ab885a5afb0201882d7e43", "tarball": "https://registry.npmjs.org/joi/-/joi-14.0.2.tgz", "fileCount": 29, "integrity": "sha512-s7SfvNw0iQj1YQZe7xgltVI+rF6YZf/8sWoMiS0WhBAdxQcdmsRnY8Uo++XivsyZI8UmYw2nFKJ24L0qca5Bnw==", "signatures": [{"sig": "MEUCIAGddCuZCnPhsXQTaxQdSn1FI1Uz/ZUGYF3IXqi9gQM3AiEA+dUDgwP4ISK4jo7BdfVRW94L33ZA9GJgrOSvf1ziSH8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 189616, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb14TyCRA9TVsSAnZWagAA/I0QAKLL7TGZl+Tz4NmthCKc\nq/+gKes2jxE12ndsAzo/JJ1049OJOK+NvcirBp4A0edOj8uTt0gRdk2OhvoG\n+XCoc9yB3aHVawmJSsW3tufBUYWeDdBKWnfBD5hoY04j5BwJs1B9ZVy0Dt1H\nCnQJaYfXmS90qAI7YDgRpp0E5xYSRzQ+m4xFgTT0T54hYDPa3OYzuu3tzVa0\nZPlUTNFU7TvbNUaYMO5xa680AHAB7SGp6Kt16GK4Sl5l0V2TcA+N40sjZdM8\nc2Betby6PdWAlomWv+QkwWiIYN+OGkCL/PytyNjC4pk7bXE2bSFjuE6VhtUw\neoi7lpmYqQOt8F4FVMhCp56MdREzqksc5S8MiTC1e+8UmmxBSnF3PamstOZU\nL64OY5j6cyDx9AkjPJE6RWhQzWtXSAYME/SsvLKHtS+fauVIj/Y3mnnIpvDn\nw3jiMilMkWz89EZx2FLJqbxSVGsFZ8F9Y0BfFxWbs7xvZcmSHoqQ6G9zLg8E\n/jawcmEQcjNYleR4lWZLl4tcT0Gk7VfdichnJIEhxHb80aJDegg9scTnR07W\n5s6N+peTVgh0KDsFz2eu4zmnzLh6MLgr5qKwqxuZXgspT2RwG18zUodb8MXc\nrpXXZfdt3DS3/d/FnkH/TTHUe4l27wdg0AmR36aYOFRk95tQ1frN9zxPUOsf\nGKI2\r\n=D4lX\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.9.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "14.0.3": {"name": "joi", "version": "14.0.3", "dependencies": {"hoek": "6.x.x", "topo": "3.x.x", "isemail": "3.x.x"}, "devDependencies": {"lab": "17.x.x", "code": "5.x.x", "hapitoc": "1.x.x"}, "dist": {"shasum": "5ab9091ec972837b348c05177c860e004f08991f", "tarball": "https://registry.npmjs.org/joi/-/joi-14.0.3.tgz", "fileCount": 29, "integrity": "sha512-EtFiturEwhrNuTn1bpR4VodSPsi6r1A661BjLC6OMnETU+ClDdyV1aKWXgPTYKs+mVjvsPRtdn7rsgxbXU+jiQ==", "signatures": [{"sig": "MEYCIQCjWn2E/hjnzRN8SJMVx6PXSMTRX3J6xPGwW8DYBEW1twIhAPr3eonXtrZCEiTBjumGmMKx0l7PuMK2fs1nYDyE7E/b", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 189477, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb2ra0CRA9TVsSAnZWagAAUjEP/18w535csy/NEIAHZ/ME\nI5PUXUe9e5v9aB4Wrj15KgQlsjf1S0NlTZvLtouruNLMi5pPMegIjHSSNHDp\n1iv0LlhhjHRFDf+GQJMg+8B7gILBokAq4xY7KDINKIQr68g5iz33oFWQshYC\nRjeh5t+iH6YFjFOK2dt3aGi1BAQAnDfuPY0yt2owzTiWIYX/VCp1xEohaKfZ\nXFWCMcUBHlTbFbSdlY+Qj1E21axbNVm3F+FCfpAdOl48ggPMoODxSXNDHXsk\nWSpKY5WO0z+Rbs0bHC4EXSl2Efp6ZtdybDCJBuDiL51ltFm/A6cbGtYj7Xel\nEDL2nJM9m3iYSqoC51AvcdqV/EM8kctmJZD6+zKrq3Hv+F8AMTsDlN/fKxa5\nZvd3R9h7Cv+PZi+363XYfPjkbKPnh6IbBhRUACZ9CHGz1TBal9LZTXUQ8J0L\nSUYSuV1YPkJMMuljg3e285H9nVfLi6ZtaYV8HSOLKsOtSR5Icay+bIiY+aWp\ngqyzMNdCk0s09JhoJbTOysjIHuxFhauceVKOZKGzL43khrFMHhJdE6ifRhKd\nR7pY90KjTxCuD5VraWqdDTNEA96Hw9TMxQ2/f5A8YvE1gcNBAoBRsQqiIm25\notNEsjsSMP/7cJN0RmsLjSohEu9XVvIejbpR99Iik0DvFr2H3nzJy/jggCuC\nQegS\r\n=Ioui\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.9.0"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "14.0.4": {"name": "joi", "version": "14.0.4", "dependencies": {"hoek": "6.x.x", "topo": "3.x.x", "isemail": "3.x.x"}, "devDependencies": {"lab": "17.x.x", "code": "5.x.x", "hapitoc": "1.x.x"}, "dist": {"shasum": "f066f79330f6bd6f3dda243be6d2c211f83a5f9b", "tarball": "https://registry.npmjs.org/joi/-/joi-14.0.4.tgz", "fileCount": 29, "integrity": "sha512-KUXRcinDUMMbtlOk7YLGHQvG73dLyf8bmgE+6sBTkdJbZpeGVGAlPXEHLiQBV7KinD/VLD5OA0EUgoTTfbRAJQ==", "signatures": [{"sig": "MEUCICZyp1f2GUEE4HIC1vXNvzRtj2sM0Lgqf10gZVKjF7O3AiEA7BEqIH+Q6QNEMj5FdCvjvkVq5bYsEPYSyXam5kRFsxk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 194150, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb3OwjCRA9TVsSAnZWagAAFGwQAJ+Va3luZMBPRHraroLt\nQBCXRB0EsETfXEUHO2G5WD3uCTJ/GWCJ7ynroXrkXXSpo2tWnlmcpTaCs8Um\nMYIcEmi0Zzh4b5axr134S2mOEP1PKHVOyZNn2SLaMnWKKUObOt0YeZ3Bl2M/\n+8Q1jOeJ6nfzsQ1fB7urLa4KOvrO7hlT3ClE+j/4ZeZY9O2N8uSjbwQIlMB9\nWezw3e2A3sfvGCcHRcA/babylIBmc+ehG2N0Qlogq54JrTapP7SLAzAQpv6W\nBa7J3+DB8mLdy+D8gp+i1x7B6+Hgxw7rFjV8Y5f228N4SlN9RVF5Nh7zCRSs\n33UJQGFkixmxzp34ccyM4SK7EaUinxyJ0LmuvRED7tH2XZ6IaV3HJhJEfZBt\n0aEstOZb6ZQgtg78Do0VUqfriNSVf748EhXUeP5rzaLZRrAT8onrjGGkTDUc\nFIqRm5HCozulkS7wOebWPYmdXLxKpOK7Mcpi/2wcOj4GCHiywGq8zqWvGvKn\nBet3C4u4IwqAl7uN3oZ0vWy3iwe4ZxYywRb7LbdJIKVH+dBGmrXPJgQWAyYD\nt1uLw2iTq6brXYqD5VszhrqYy13Lg4chPpEC7CSFXVvPXpvIDh++FzqaHU4x\nOnauew7eZ5FO2pUadB8gsNwRg9PYsRe2XT+zTpv3SFAlKDFpt5JTzFz1OZfW\nbO0V\r\n=lghS\r\n-----END PGP SIGNATURE-----\r\n"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "14.0.5": {"name": "joi", "version": "14.0.5", "dependencies": {"hoek": "6.x.x", "topo": "3.x.x", "isemail": "3.x.x"}, "devDependencies": {"lab": "17.x.x", "code": "5.x.x", "hapitoc": "1.x.x"}, "dist": {"shasum": "c6942f739a37d581da38ac812f3e0490228b549b", "tarball": "https://registry.npmjs.org/joi/-/joi-14.0.5.tgz", "fileCount": 29, "integrity": "sha512-FGlQ4EjNNPp+v+Hx19VWcG1sgODOpUCQDyvepgSZgZx7ld2Kl4ZzMq/UsqZwvY9IXDYAcJy5oQ1+xOhWZIcc7Q==", "signatures": [{"sig": "MEUCIDzUOAlWRQFESeXPCpQseicwVz86Xh4z9WPedvSGgdQqAiEAqYhkcUum9TS/8zHNVHst/tvMPh+WXHaMXvQO56UgU1M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 189324, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb6DejCRA9TVsSAnZWagAAyeQP/jW9ukHn9TTcVCwl8FA7\nn2Aar5tcGY8NB/TkV1NhHPMUkgfxtJSFul6hGxg7kM3oNZ6jXFjoiDg7UYeP\n4srh/yRGL1pb7LIVNgHFF3kaRUnBbUp0E5MsbhdHoE7MwbBIepuJCOKRI9AY\nGvhhuV+1GLEzrugMufDUIO82PSw/Rb+oYMKfkNNek4kiYnO4oO1MoDXkyzQ8\n0gHg8TbWW5gc3D7zzD6rownM5GLJSjZjX4a5NBrTry7gxh3QW6JTNAAg/LYQ\nF2J9Q7+lWPYPteUUPiUZ0tJ0HDMikG3bqlEeqe97o71ep5vU5LtJXWa/FhH4\n7AAaJ5PHiMycIno0BmnnOlMHAmRIa4xGYQV7vODMefsSbI1vLRY2p412crpP\nbpes7Agm7D/4/3nnbDH1RrjR9A9a49gDEnuPOmVZvpP1HCsTk1i0qzlpvb7Y\n8Z+qV+0fjjaSk0zYzXmQo8uEhWprayvTKSlG4G9KDhbKbeJlXVP217RRC780\nbVjLsGmh9P6z/PydDrt9OO2AgauDVtj6WNgxUVRnEOCHWjQNxU0Dkt8K3dJ0\nHofWuz4TEbHzNwqnlhKDZwxsMSvvrRhggValgA9ok2tuH1neabp6t2AlEnNJ\ns1dIgecHbUOSvg/t86qJFLK3O9Ac3ZMUEDQ/2RUv8CermfF5PvBkF79qx057\ne51T\r\n=Oq7G\r\n-----END PGP SIGNATURE-----\r\n"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "14.0.6": {"name": "joi", "version": "14.0.6", "dependencies": {"hoek": "6.x.x", "topo": "3.x.x", "isemail": "3.x.x"}, "devDependencies": {"lab": "17.x.x", "code": "5.x.x", "hapitoc": "1.x.x"}, "dist": {"shasum": "8c72314af6dba13ca48028a4f0af992ddc5a7472", "tarball": "https://registry.npmjs.org/joi/-/joi-14.0.6.tgz", "fileCount": 29, "integrity": "sha512-mKXPSNYMG3taVBZemQj3pPOlMZGrnnu14JKAC6AHg6jJVX35L7oDk89ss1sHj+laDSEh3mD2PmeheI5k6Aw8nQ==", "signatures": [{"sig": "MEYCIQC1Vv141MwBrhF+TC7vZhZZeR+A7T4HR4p2FR9usy4TTQIhALSHieGMZ3KvTebLkBZRCPzTu5YMAdXvCK9hQ+C+pwGG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 189333, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb6MTDCRA9TVsSAnZWagAAobkP/icCRqY43xyZuT341BGh\nTxYvhyTVueC0uVRy+ZabbeyjJTm1pc9LvgSzsdN8dQMI4WsfuBK1NSX5+/0M\n7qthtGSNDNe+kbpyXoCXxBMwhlknJbgHWCyW8RX4c13JX3E4XE/eVR8n3/Ls\nmQWU9FKnQPI6uQY/bUPaMc4SN4jkDrprVyVh6NUnoqxaSbDIR8tq3tT2/2oA\nHorVqojTay07hBKUvtmRKW8rXIKZBCx9SBgR3tdWRyzMrVWxIidRkrvwDBCk\ngGcVvelZ79Fn1lCE5beOLq2iEDI74Q+fs4gRQkdmb+PmLpIqiG8C2ozxlPyj\nEOLlK+DnuD4Cyy9AculpnspF2fyLoriLrYeQAIZitXnTM1o2zxpQ99wJ4FA8\n/bkn2XKXTJLliobY4I9AsQHV2k4ZXHGA4LW3+NK/G/qYTl/OA1mHM6gNopNI\nmJLCN18UGEXPKZbblz+D6BIq09Yqu/wFYXFsGxy+L0yHZoeG3dGABuceRr04\nQzVh0H19j5C+euPGBqrEd63eADQWFqnnc8QBLvL342tEJshBj2Azw++iplI5\ni0RQJkqtrOWNwYXhN/n7/x4dyrxFObuGX8Gal7yYLKvrlVkx7bmYROcI+qfF\niPuohyHvahPdudJ+/9EOd4yFOPIhVbvCRwKzYoS90rsNkITgpD/g+J8ng9OQ\nlpn8\r\n=BJff\r\n-----END PGP SIGNATURE-----\r\n"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "14.1.0": {"name": "joi", "version": "14.1.0", "dependencies": {"hoek": "6.x.x", "topo": "3.x.x", "isemail": "3.x.x"}, "devDependencies": {"lab": "18.x.x", "code": "5.x.x", "hapitoc": "1.x.x"}, "dist": {"shasum": "98a7923108a9f361192551b29f538488d07953cc", "tarball": "https://registry.npmjs.org/joi/-/joi-14.1.0.tgz", "fileCount": 29, "integrity": "sha512-oJDBwllD6VjSMquuv56W0fQlegJdsr8+905zuEQhnE/eTSh5GYUHlwn/UATnlDepuXZF8BnxggdxqQTYrtit6Q==", "signatures": [{"sig": "MEUCIQCySnY//NVkt07M9kZ4TQhydq3IEjgwFC900aPeE0IzHgIgWMYV6JkZcPk28WfxAnOBhKrLBaUAz5PK0+XiymKZ+O8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 190021, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb8zjMCRA9TVsSAnZWagAADDYQAISz405p7NzHvGRtudMc\nU1Q+jAoXtfsRaxgsf3Jtu/18P0gqZ+hrIvGea0cVeZoQFqnJF/17Xk/5e8m/\nIPRSEJkTgmWQUqsmv6Z0tDvhqT7geU6GT4ynTw+N1nZ5VxeodYKfmrS33YB7\n2xcLKwUQ4MMBsIcz5/Syg3MljpHBtKFQwzs9ZNWaTaL2WQRZRtqn4qoj36Zv\nYWf+fBSHQFN3LEW9rZTAW6dG3Sf0JyiaC5qISlGC9fOOB3z0js+nYOmaf7BX\n+eLMQMENFRwC5WWYcTtVxT53jBXbdX81iBfBBc55ZkKFnbOwwvqIv5dn4S2t\n/qb3Ry6TKZdpq3RKx1cYQNtHjRzyEDeBEssTsIOxxruQxjB3VSzeAegvj+Ly\ncvja7KOs8ggL+gExhDbu1LF37/T5MvDqMRwNAb85zp3JxZb5QbSpQmLY1Wg4\n5zoiWfE1I/mN69hOslLJFmvep5nBpJE8m5RSBQtP6PcI1ksS+o4uw90Sy8Me\ngPjW9ZPMYidFmHe3vCofmhBclDLWT0tR3N8vvxBeEP3zPz5NWYgkSIFfRi39\nQAOEQnnnBVsN3SWb3HQIDfQugc1zP7rT+ivucl0zCJQunc9uoZMUzn7Bb/NM\nxU5fxGePVBAeBfnlPIt9jkXI1gJ451QMIg92As4TYvRQ3QWjVCngc/UxEYvg\nHtuv\r\n=U3lH\r\n-----END PGP SIGNATURE-----\r\n"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "14.1.1": {"name": "joi", "version": "14.1.1", "dependencies": {"hoek": "6.x.x", "topo": "3.x.x", "isemail": "3.x.x"}, "devDependencies": {"lab": "18.x.x", "code": "5.x.x", "hapitoc": "1.x.x"}, "dist": {"shasum": "7b548b146b7809b9e3495a18ede234748642262e", "tarball": "https://registry.npmjs.org/joi/-/joi-14.1.1.tgz", "fileCount": 29, "integrity": "sha512-v/aWz/iam4olepSLgdA8St8eA7W3AQPvwAJd8Qi7I8UDuoR9rLXF8CJfrakORgOPf7vDEQihPUS6kXN3Rl1ycA==", "signatures": [{"sig": "MEQCIHP+GkNvtSGyNiWAQlNuplK8djIdCiTuf6lWnq5D0nPlAiByo4pbVXCwvhC8YMqpVQUpu8IhwLC9JLYL5hnX9NvlcA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 190069, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb+TKpCRA9TVsSAnZWagAAjkAQAJ98FWUJe63FMl3LzK/6\nrqSU1szmGMQK+rXWQxObQYorKPtJ4XfccrZYag4n0wl1G4j8dtSguS/JWecT\n+l25FG9/YqcO+X0O8Wg8+tY21RJ7lMZcfBXEl3B7yaQaYPfcIXcSw/6gb04R\nEJ0XYVJEgjdk2RMPp46gqCwMZyjGRrSoUYp42jtb8ju2EnQdgYWlYWuWoTg3\nJHwU4FcviloOr+YW7vgOA3SySeg1a1FC/Bvq+oHrnSM4OxyOLYyLMb1eUJll\n4UhzldaTjrdVXmQcTzVty/uX6bkVKO6CXXPkW/1IignSm6XXPlOBN6d1yJKN\nWZJ8QGMcHYhMql22/ubhV5T0FCxSzERyd7pz6kIi2VEVsMphLTFMx3pr4uha\nj08MR6g+ts3ZvJ1n+IEsELxcluUl5kW0g0X6KuegdXUnjceFaeSWPtkf3Nc7\nIztVqujAidC2wHIA4sof4xpVEqdeq/xTxigwDN0XgHgLmWIbQF9rTI4FIKgh\ntE2fUfXgkKdpPxV1vZQdBBETOhFQtydkOXudqfTsUggWPourywoDKVKAT4a7\nkGK+4/mH/yTEJqIi/wEOa1+iJkrC32/0vyk4LgGLZeWKkpZzLgJvUr/7Oqq6\nOKRrp+G7hRCa3d+RbIh04AnWXKekm8RJPLuRNHsc6/KqGIsNsLOPozizuGN9\n8HLP\r\n=yfKO\r\n-----END PGP SIGNATURE-----\r\n"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "14.2.0": {"name": "joi", "version": "14.2.0", "dependencies": {"hoek": "6.x.x", "topo": "3.x.x", "isemail": "3.x.x"}, "devDependencies": {"lab": "18.x.x", "code": "5.x.x", "hapitoc": "1.x.x"}, "dist": {"shasum": "225610f2e41279d56c7b2f4f5489384cd60693ab", "tarball": "https://registry.npmjs.org/joi/-/joi-14.2.0.tgz", "fileCount": 29, "integrity": "sha512-Z9fR1yK9usPlxo2lRUaeDfXciKUSsDaSnDBT7iZg9/zU9+6EvZdM1cEMqNOrjWV4obZmNnHmTjth1RVKA1LiHg==", "signatures": [{"sig": "MEQCICLOuUfGNg9h76rzFLxOdG/yeFzHgZXwTQUHjkevrK0HAiBzdO6fgW2KlWwQ3rcejfZhaj0tvZcA72QXzyhQB+nqXw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 192013, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb+qnzCRA9TVsSAnZWagAAvXYQAJRA0nT8nyJaTyIlStyQ\nEM+Iq1bhTlVuBclATzmiaE7uyfnWq1KSBz3XebR/8JFgDl5OYNSXRBcnrF+D\nTYzApz7ASvvWpFjbzHyXO83D+MHw2pZnhiFj/5Si9MDgb3ZtXkLhgEK5bQC3\nzT6S2hLB6ycwx/WzcmhrdYTx3lrg7bi3glsMH6ivtZoMXQC9OMyCmzHouDYy\nXiaXuRmYHGFn8lq50bSAXyWeNSz5hIq65niknmsLoK06HPeDDd07xP5i7fi0\nZ+JCI3mCKuklvN0amVpuj063QwLTLX541ohzCzLa7uo6rxj3LneEsKb0yWu5\nvErz+TFc/8p/vUJSEKu9xoEz3cMEItoO6EnmFfKH+7Pp1sUE8GrQ9X/7VpGM\npKBEjIrdxDGwh92Yp0KI5ApSumfXfuFiPpYCHe/KwV/eCt49syaVaZHEez+I\n9ZxLDfMHgsOKFvMK3Ixl9RqIHmt0j3Q4naI886nXw5848dExT9pXkO8uoVv0\niqMSWLpAcMhE5g704Oy79ReMObNcc9hQkK88WtX76Md/GHGokLrkkclQAY7p\n0y47JHJIqyTq3ljgN3LA8cgSQuQjVy6Lsxiw3HL7KoRWZwQQvcakcSZt+0X0\nkQ3vRPc9GZ5prXyEEG1Ua5/BNgVbZ4dibDVzjBSaumUuW22zXrhcR3zXiOet\nFzFx\r\n=/5FY\r\n-----END PGP SIGNATURE-----\r\n"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "14.3.0": {"name": "joi", "version": "14.3.0", "dependencies": {"hoek": "6.x.x", "topo": "3.x.x", "isemail": "3.x.x"}, "devDependencies": {"lab": "18.x.x", "code": "5.x.x", "hapitoc": "1.x.x"}, "dist": {"shasum": "55f7c5caa8256de74ccb12eb22ab1c19eea02db3", "tarball": "https://registry.npmjs.org/joi/-/joi-14.3.0.tgz", "fileCount": 29, "integrity": "sha512-0HKd1z8MWogez4GaU0LkY1FgW30vR2Kwy414GISfCU41OYgUC2GWpNe5amsvBZtDqPtt7DohykfOOMIw1Z5hvQ==", "signatures": [{"sig": "MEUCIAnjSwKrnKbE9hVpDWDBshuKKib/G102TaeLdrnXyzXnAiEApDmU5ncfQGPHCNr63ys7o40OZro6yVOFRl4uyrl5QtM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 192729, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb+vdACRA9TVsSAnZWagAA+eIQAJ3y2BnDrUqv8dcfWSjN\nge2xuSQdsmsEEkcmr2O7iZCE4hNq7tIoRKEnULDQocyZ7e7j7bUaeOm6Pf25\n7gS829ReWFhUSagy8s81gG6pkKUkOeQyD49akgv4MnAM1ym8XXeIu+mdQW7O\nACr8UXcdmbTzHIfoA9lD2zXu7fOrQEE7V1QNtbFK1wvjP7qJ+SCdOCwmij+B\nPIXqbr7UcATHrwt5CWNJcJJ9zryBXuURvgwry3dqWNiZU/bec+fXxEe93bmf\nIartWc2fih5YmigcppHHq9PSjQc5AN4/t0F38KID1IjQO1pozN7mZQardG+I\nTxg1nheh5MJURiZ6/VzUrjN6evv8ywFwTHtNaiz7+SyBfxBhS5uMFtMjllkd\nvba7zO2bnRV3d7OBQoZrxLmdGoEs26mxtl7WZJ/w33Wfi7vcLn5uQart8cOB\nJ+Gn0hpn5k+BAfFzcjLdELpCsifv2ePqPehWhFWJcf5wC2yA/mPgxg59VqMq\nSDu7HkymOO4mWPB31ca2WhDsNur74OJX+LTj0wqh/ceUPVdtyjYg7QRyGEiE\njxbZJiFHKV5n+DlFyK2IcSPE439pm/au+kvHMMvGRjqcsD0hIc5okFBpttZm\nV+upHZX+BtzrJvkIy7MRScmgeMbs5B4bRfzNJojnyvZZPLVl30hn1yeY8FyM\nnY22\r\n=8AUq\r\n-----END PGP SIGNATURE-----\r\n"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial)."}, "14.3.1": {"name": "joi", "version": "14.3.1", "dependencies": {"hoek": "6.x.x", "topo": "3.x.x", "isemail": "3.x.x"}, "devDependencies": {"lab": "18.x.x", "code": "5.x.x", "hapitoc": "1.x.x"}, "dist": {"shasum": "164a262ec0b855466e0c35eea2a885ae8b6c703c", "tarball": "https://registry.npmjs.org/joi/-/joi-14.3.1.tgz", "fileCount": 29, "integrity": "sha512-LQDdM+pkOrpAn4Lp+neNIFV3axv1Vna3j38bisbQhETPMANYRbFJFUyOZcOClYvM/hppMhGWuKSFEK9vjrB+bQ==", "signatures": [{"sig": "MEUCIQC3Ax/hBHg1PdBTKP7PJy1jQTTkrPQSqg69Fl21zhum1wIgK9aC39NiYq8l7nbuQ8AiqtV5aoPfpmKBpwqrOji+2YM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 192877, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcJeXVCRA9TVsSAnZWagAAlogP/j1opdOVHUSuIYIwWlq9\nbZZYC1ULv0hs6KldTcf8khezpduhdRuVlBCvLS8s/C1ILHBxz0eHfwHyRlEb\nszHIWqg8+4l5tXZpDxDLUWZaBy8eGnethfzZWH1fQsVU+MSuqtDosu09jMMQ\nNdHH5byUev31Y8vp8vfvl3KAp5dd/D1B/yKnORJU4WNalBvH5DnLfk6yQfvn\nIkAyNQdYMiGoYi8jONJS1uLCqSn9GijyRaaytRwwAJ95nHW9mkNRzmh95ATs\nvqYZJzRwMo9Icspm+JhwJ+/q9iEAjVZZRQuALOx1F3n9CsB0NbR8efFACXDF\nJ2Va19Vnc6rPJXK6fmTMQc0DVGwYvXZOejevun3Veu7nVzjluSsQkbsL1dSt\nWKDshdE4qDzOTJRU1OFHY9NoUqE8CtOvPsUX4qNweguYTdltlXT2fbW0PJYx\n/y9IOfhPeo1kuoW3zb1eYm7iPQrwOwNeyPj5VJB+Vs7sZZWG48Xf+NL21Drc\nz7dpksTlHxLxmd0nzNM6ZwPHi9u2gtlW7uV8RbEhxBtpzgBWYWTQWyRgs6Pl\nAfLN3jU9vT3bfYgIGk6ZBRr9IwWZIPDLstbVi1HCyTvZ+I7cM4GdWI1NO8GU\ni/4sNcFJjSNJUp29ESzAtEli7dEAn7KC4p/myLS+Vun2TPZUwWDV2BlbQVmu\nyqmP\r\n=bLT2\r\n-----END PGP SIGNATURE-----\r\n"}, "deprecated": "This module has moved and is now available at @hapi/joi. Please update your dependencies as this version is no longer maintained an may contain bugs and security issues."}, "17.1.1": {"name": "joi", "version": "17.1.1", "dependencies": {"@hapi/hoek": "^9.0.0", "@hapi/topo": "^5.0.0", "@hapi/address": "^4.0.1", "@hapi/formula": "^2.0.0", "@hapi/pinpoint": "^2.0.0"}, "devDependencies": {"@hapi/lab": "22.x.x", "@hapi/code": "8.x.x", "@hapi/bourne": "2.x.x", "@hapi/joi-legacy-test": "npm:@hapi/joi@15.x.x"}, "dist": {"shasum": "b7339e8a509510a04ce9cdeccb35a6e188080287", "tarball": "https://registry.npmjs.org/joi/-/joi-17.1.1.tgz", "fileCount": 35, "integrity": "sha512-fww3Ae9cRyj6yHy90cpxvL2y39V5JCY2KaXV3KfALhoFfFcAuyQBPOq+2q6EZ2QNMn1FhkDy+eRkGVG7J+BvyA==", "signatures": [{"sig": "MEYCIQD4vrveW3KCf1SYfeXsMCL9E7jvsGrsGB5YcKdilLtwagIhAOSxMQG+nf4F4szvl51Q010+jGWRVHiCpFu0KqBEqzld", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 428922, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfEBFSCRA9TVsSAnZWagAAX10P/i/UAo7L2DtFuxMIuloz\nqWgUe7/9mxlb9zb7gFAU6EMfPEZEsuyLX80CuyG0HqgY7+yh4h7BtuunkC+z\nIqJ0L8/3wzYJeosAsIQG+85o9sGRmyiwg5uph5Qi2CeNCTB64jCGs4bRkUX4\n/0AK+my3pTfZ8Jt7rbAsp3RxuzpPmXFS6l2oaXmKHpK6LwGpnSRPJRIw8BbM\nPMoebnoqjs0CgShT7B+9X2VJ/XB1czLztmSrqodYOFvXX1wVZI4Iw/qrYTjA\nfZvYR55CWop8g+OyJJ+CGYfqI3avDmaTLDrGAqKwg7Y8/0Xk2clG/NWNewKg\nzYwMiQjr2YO9ZHiCS4dMEFTTUhpvTjzejQg3Xc6aOVl1hfibXqjdqHdxkoYA\noOOph/pDDVkHPs+WQcEwKrxJb0IY7NUBfqQPUXy84SHR6273w8QWuSeHCzLn\nNhSbO2AkC9J5o3EL0qWjgozghbudKe+Z3fxciBemkNZb7Vp51ouUtMOS4M/F\nWAHQp7YZbr/Q13qsJnWQnpcOCO3IfxTz+VHEhonL28undOkk1NHtWgcnz95s\nxQQ5c2yTg3fe9SW16Z1dwdL8bL+YxlsGFYXuA887BPZCkdrsxK6uhKyLGdvP\nms8atD6hcP6qmI6OW+E7BKnCGYcq3MKWtig7gxkh5B/x4mjy74eu3il/ZzVO\neBAs\r\n=Hm/f\r\n-----END PGP SIGNATURE-----\r\n"}}, "17.2.0": {"name": "joi", "version": "17.2.0", "dependencies": {"@hapi/hoek": "^9.0.0", "@hapi/topo": "^5.0.0", "@hapi/address": "^4.1.0", "@hapi/formula": "^2.0.0", "@hapi/pinpoint": "^2.0.0"}, "devDependencies": {"@hapi/lab": "23.x.x", "@hapi/code": "8.x.x", "@hapi/bourne": "2.x.x", "@hapi/joi-legacy-test": "npm:@hapi/joi@15.x.x"}, "dist": {"shasum": "81cba6c1145130482d57b6d50129c7ab0e7d8b0a", "tarball": "https://registry.npmjs.org/joi/-/joi-17.2.0.tgz", "fileCount": 36, "integrity": "sha512-9ZC8pMSitNlenuwKARENBGVvvGYHNlwWe5rexo2WxyogaxCB5dNHAgFA1BJQ6nsJrt/jz1p5vSqDT6W6kciDDw==", "signatures": [{"sig": "MEYCIQDK8W41xb84OGBPGBWOPd/GsAIE7FmF7LyXQ/1Ky0MTrQIhAOkPNusHQer5El2ENzol82sOnwmdQAZwJfQOBR/LlhJD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 512628, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfKhNUCRA9TVsSAnZWagAASIAP/j+WevCRlafsztecNmvx\naedhsbjPBYtUBn53crpf1OOK4yg0AW6AjIsiFMAWYmlpKrKJjo2v/SiPm9cN\ngjRRLx6r50nyraCli7Ib+Y2ufHg/auXcp6KYVdZ+h8uYynrCM5YWbAryZbYc\n3hawju7q7QZUFz8QiArJDcJNqiOSqvvbONBrw8sDULkEwjWNEDbAH8yUdwiu\naMpeM0RpxbRu+16gwnLIjXEkXsF/mskkbvC0oewWPWG3RnRB60aNACQJBvQP\n2i9cbO7rY4VDZV1GxG1mqN0kYQBovtaJedwOztgrqY+s1yjWTtc7UxWJsXTV\nWB6vkfKiCuYTT3YPiuEh/hVOivFsffIO9o8qaPYh+OLjRmZa3Z/F/V45ExNf\nlYOtMLOhE/1ZmLVWdxnQOB7DRTZ9awRu3PFos6VwpeGqWkl/O4TmSvJ8mFvJ\nKdyG3OtLwLvZE+yca4R1DPJQ5KilKTllZd+aWPTOozDWaKQsPNp4eVTGTFC8\nvmpoNY1rWziiqDd4T3H4/bLczfikjWtKwiYAgf3YbV6+uDkPux356l5TjT1q\nWbK8fzfZKJxls0JQePw+Hy9uXgayFPxE7f8CwzTN0h3GReImshX0rGzLUBuQ\naS3SXUb1ACySVWPpwVc87yoaXRazhkBeb9tbH/0Rk90BVkSSgbeTaPjVwUIl\nwJnI\r\n=z9ZB\r\n-----END PGP SIGNATURE-----\r\n"}}, "17.2.1": {"name": "joi", "version": "17.2.1", "dependencies": {"@hapi/hoek": "^9.0.0", "@hapi/topo": "^5.0.0", "@hapi/address": "^4.1.0", "@hapi/formula": "^2.0.0", "@hapi/pinpoint": "^2.0.0"}, "devDependencies": {"@hapi/lab": "23.x.x", "@hapi/code": "8.x.x", "@hapi/bourne": "2.x.x", "@hapi/joi-legacy-test": "npm:@hapi/joi@15.x.x"}, "dist": {"shasum": "e5140fdf07e8fecf9bc977c2832d1bdb1e3f2a0a", "tarball": "https://registry.npmjs.org/joi/-/joi-17.2.1.tgz", "fileCount": 36, "integrity": "sha512-YT3/4Ln+5YRpacdmfEfrrKh50/kkgX3LgBltjqnlMPIYiZ4hxXZuVJcxmsvxsdeHg9soZfE3qXxHC2tMpCCBOA==", "signatures": [{"sig": "MEUCIQDwEwbERfdprCEm4zsMIqBogd/cl9Ih2Lx2WaIVgF0NrwIgMoZQq+iMZ0MDJSOkKC39UH7uW1/e/I9hVkdCPk8Lv28=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 512556, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfPVoXCRA9TVsSAnZWagAAxEUP/3yJEIfAeXa/qKkFiLPS\nsb8NJky1eUReFnnoaF4GxXk1sAwspAPSmy5xTtEQike1cA5OM8iDvUgi0oGT\n+/ZJTHo9Lz+7478sTAdYBe9RuP2doVLLx7+4OhWEtgQyPx3l6CoNfaGMjXSR\ngtaHDfekhdDp3k93CE4KbXAO2pI4rrQDdBD8JkkHEKVLkWhD9MUwMhcXYzZu\npEqo7vxM7zQ4s0mS+xYcykHIXjcw/+uTz/BKPu73vyrid4yRjNFvjJ/B/Pt0\n4CMdOosHGQOjl6Ynl7PP1Yq5Apph8kzw+XK8dYE+32c9ZR2xpmSc/a0Y9Jki\n8TgzEms1CwAEhxEdChzgCesa4t86tYavd3oxh1u6E3ib6NvtKEB82kK+uDEB\nn3MQZbsXMfAc+oF1WOi6xbDNjPwXkonTPK8WZdWsVyZJapF2uylvdFykHqyb\njpG+mESxidV3Oh+kPenQQcIN7lhNmvO2GYNMnVqb9nb+aXOwpULPVa0DCViW\n3Y0Ob7Uac9/c32VrYuqpnQZilSP6XwpBQ0LW7aE0ZvPSuMz7o8tHludW4aKP\nWiX0DDIr3u6aSsa49zMvKFB9NlcpriZX1hiAkEgI7GwsHr5NZXSR1d+KBZPk\na0MhY6Fz914SWRyduaRSjtKmxXUhKroNBbn95Ki9+hHaK3YcroM2CY8tshXF\n2Riy\r\n=Iebs\r\n-----END PGP SIGNATURE-----\r\n"}}, "17.3.0": {"name": "joi", "version": "17.3.0", "dependencies": {"@hapi/hoek": "^9.0.0", "@hapi/topo": "^5.0.0", "@sideway/address": "^4.1.0", "@sideway/formula": "^3.0.0", "@sideway/pinpoint": "^2.0.0"}, "devDependencies": {"@hapi/lab": "24.x.x", "@hapi/code": "8.x.x", "typescript": "4.0.x", "@hapi/bourne": "2.x.x", "@hapi/joi-legacy-test": "npm:@hapi/joi@15.x.x"}, "dist": {"shasum": "f1be4a6ce29bc1716665819ac361dfa139fff5d2", "tarball": "https://registry.npmjs.org/joi/-/joi-17.3.0.tgz", "fileCount": 36, "integrity": "sha512-Qh5gdU6niuYbUIUV5ejbsMiiFmBdw8Kcp8Buj2JntszCkCfxJ9Cz76OtHxOZMPXrt5810iDIXs+n1nNVoquHgg==", "signatures": [{"sig": "MEQCIBbOU+oBSidsjggCkNQ7LzTqKCkwSjPwQfr3ZPHnBdkeAiAors2pPupR9Qfn0DAxx2kq2IyxCPYThpAULpuNJcgBjA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 515922, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfk9c1CRA9TVsSAnZWagAA+70P/3xwNnMK9jUriZksxgUs\nwM7KaTGpz78I/XIY8Y49vuR/ByRvDptkDpjR4q4BFiJI9yZ/68LWMB0xsJoS\nNgtK1RWw/ZcyyabctIRLsnMNa2NLV7L2CdG4tVoZEUS0KZLGgcOPJnbAJOZ4\n/pAG2TSmnz0e3e2T/PQ0N82i/tDrTDVlcnBaLtGg/IjDPr/Va11s6SZi5AgI\npPwanBcIs/XX3LojFQ2VCT8xssqHuPBDjvF5KTHoYurXDPpROBpRHIUbuQ34\n7uMHseN8g5FnRBRqqAN2PiTQKJyGRPlqLnSQj6Ck15Xv1cSSkhz/Wg2cnSnT\nNMgrJJntDkl1zQnPVSaKr/eNF9cZb+/bRIs+bohP+2SycjcYShB9knYmFlZR\nyt165rsz2jZR4fFS1kz4sKn6h4EcrtJstTleE+Qw4+8xJUXZroYawv01uWtv\nj5+BUVFQvmHvK5rUZmUNjACE99hD/xEkyWtNaUr7Fvfv6slFNg6wjIWGWKHi\nKngHpZ9I35954pWG+vbFmEXYegWKBEsRUH5NF2GSU+3Z9u3JlmTrW32Xyezo\nEZApQJctCRUZd+i1xm4LA/PPdL0jSOP7t8tU5UfOtKZKmhJPfzoDQ/cJAY3U\nUNXXR3KMvfcgsOLUZrm55Wbaw106uCfdMK+kzaZMDA2IecG+/CnS0VAm7Mdk\nQ9bw\r\n=HI0P\r\n-----END PGP SIGNATURE-----\r\n"}}, "17.4.0": {"name": "joi", "version": "17.4.0", "dependencies": {"@hapi/hoek": "^9.0.0", "@hapi/topo": "^5.0.0", "@sideway/address": "^4.1.0", "@sideway/formula": "^3.0.0", "@sideway/pinpoint": "^2.0.0"}, "devDependencies": {"@hapi/lab": "24.x.x", "@hapi/code": "8.x.x", "typescript": "4.0.x", "@hapi/bourne": "2.x.x", "@hapi/joi-legacy-test": "npm:@hapi/joi@15.x.x"}, "dist": {"shasum": "b5c2277c8519e016316e49ababd41a1908d9ef20", "tarball": "https://registry.npmjs.org/joi/-/joi-17.4.0.tgz", "fileCount": 36, "integrity": "sha512-F4WiW2xaV6wc1jxete70Rw4V/VuMd6IN+a5ilZsxG4uYtUXWu2kq9W5P2dz30e7Gmw8RCbY/u/uk+dMPma9tAg==", "signatures": [{"sig": "MEQCIArlNkoePyllhRkdiOwY1XloWWMVg2bwbeFIud29WDHbAiBOEp9uMmUb/ZZcEEK8KT5ZQ/LDsnUC+LAVqvkgrnPcGA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 515094, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgII6HCRA9TVsSAnZWagAAe0gP/iZYI4pOQJy20qteQfu6\nWwsxUBc76EOJrp85NUzF8oUvSUFbLbrLdvOneiCGPYuTRx401Zo2riKJ0UC/\nZi7OC8PdpClifr+QtZcbSD2EeQRl5IwSEOoTfOZSPaOIEjopIWkI903ylO+I\n4eoGv3RzMcnG30WCNhUOstgwSN6v9ez+68DMmcuwDyZdpdVSkvVuCzBIpIUO\nb2TH3F02+xvaapEWCvQbD9j68QKhOUmKl6m0o7p0ttUUDVb0S8xdulW1/lJM\nDIFfRUFxMQZCe5hgnsAigJeegrsl7EtrTFjdF2dKXfA5JHIczXvwF8NcaJs5\ngcXif83YRksgkAFre0ngWcUa95wI4xJysO42hJh9p6JbNEtUMCKdrOfeF52Q\nv/UoaITRF6rpI0XhIieIHgdzrdUIPIlYRCn9mNkUw1RVL7N734Ofu63J+ftP\nf7Yjvj2o6S1T61lBqUxkvhOxCpwwdykE09tU+nAhq0S62T5pD/c6BxY4KcB7\ndayZgDuOrM1lbQYT4NarZa3pWU/j7519sXN+oVaJAR0mBtjecSG1jEtWQy26\nu6oXlv6G4LQ+z4Uqmc0uYLvpn4r1TIzrN/6+hE0ELI5h7pOIYa5Y8mRv14FQ\nmAv2KV0FNBA/NcyWJI/iSiWWAgXnyjTWvgJfZDn41Iwv3bc4EVTNDYCgy3cr\nBzGM\r\n=Jq8Y\r\n-----END PGP SIGNATURE-----\r\n"}}, "17.4.1": {"name": "joi", "version": "17.4.1", "dependencies": {"@hapi/hoek": "^9.0.0", "@hapi/topo": "^5.0.0", "@sideway/address": "^4.1.0", "@sideway/formula": "^3.0.0", "@sideway/pinpoint": "^2.0.0"}, "devDependencies": {"@hapi/lab": "24.x.x", "@hapi/code": "8.x.x", "typescript": "4.3.x", "@hapi/bourne": "2.x.x", "@hapi/joi-legacy-test": "npm:@hapi/joi@15.x.x"}, "dist": {"shasum": "15d2f23c8cbe4d1baded2dd190c58f8dbe11cca0", "tarball": "https://registry.npmjs.org/joi/-/joi-17.4.1.tgz", "fileCount": 36, "integrity": "sha512-gDPOwQ5sr+BUxXuPDGrC1pSNcVR/yGGcTI0aCnjYxZEa3za60K/iCQ+OFIkEHWZGVCUcUlXlFKvMmrlmxrG6UQ==", "signatures": [{"sig": "MEUCIE5QIzqHbaLnV0GiMuQXY0Xynv2b+d81f364BFz0qs6IAiEA6r9+svicFLcy11USssxHgbIS8VRcnR7HahyiscBAx4M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 516081, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg6nvMCRA9TVsSAnZWagAAtyYP/2hIIYS4irfdg/S9hbp3\nSzmKlKPqMp19L6xrKbqizQQy+Mp4fDNp54gwR49hNImlYj5K86spXOCB0EGG\nt0JKtm9gM0GnLqALAFMh4B1DKc0qtHeYSFbIRGGQDZUOdVYnjX0fXKBKqYrp\nTNMW/3KGKDHlY7BC+eBoZxWQ0lyyL9SEfjgRzYAcQkJg9TmS2kJ8/XgL+7n2\nhXWafxrvEyuFtoxwVzL03F3nc1+EPiRjb5NakbjB4io6JBkXzMCXf2m1lPrn\nyGa+tpoKw+xpfCx/v4w/TvjNXB+R8VHOIW7YhWJCmO0KATY+KWsbpWLiiIla\nforNClmZWfzRjmEdTkmJgO60nHsjihRbe3+esTRo2KRrJENaaOQu+XHCXisr\nfHuqsoDxkcMHG/7dF4itz77xBlJkNZ8DU2PBuARtxlbB6qlsNrJePH8XCGBM\nuYVHIfwjDV0Fq/WvodqOZaJlk8dUE11hu6mD935WuHqM3vhp8yV4gR4n6kRa\n9SxLV5euAUxmEVLX2Yk+exB6N6ADMlYUwbW70IXJzl1RnVAKGogOtHHPCDFd\nzLH4XQzio6/EKFHNrsE/d5Iqx39KSovYEk70lEz2T0V4omhQ2yI6E9Pfulrv\nbXlEZkZWtoyPNKTjDeOZYyOBj25cwFFVu/Fla+fo935PRmxAxR3VTfgVFKTF\nkkw9\r\n=yyjP\r\n-----END PGP SIGNATURE-----\r\n"}}, "17.4.2": {"name": "joi", "version": "17.4.2", "dependencies": {"@hapi/hoek": "^9.0.0", "@hapi/topo": "^5.0.0", "@sideway/address": "^4.1.0", "@sideway/formula": "^3.0.0", "@sideway/pinpoint": "^2.0.0"}, "devDependencies": {"@hapi/lab": "24.x.x", "@hapi/code": "8.x.x", "typescript": "4.3.x", "@hapi/bourne": "2.x.x", "@hapi/joi-legacy-test": "npm:@hapi/joi@15.x.x"}, "dist": {"shasum": "02f4eb5cf88e515e614830239379dcbbe28ce7f7", "tarball": "https://registry.npmjs.org/joi/-/joi-17.4.2.tgz", "fileCount": 36, "integrity": "sha512-Lm56PP+n0+Z2A2rfRvsfWVDXGEWjXxatPopkQ8qQ5mxCEhwHG+Ettgg5o98FFaxilOxozoa14cFhrE/hOzh/Nw==", "signatures": [{"sig": "MEYCIQC0D6bo47Rme1cUeQbb9ITFs8AovhbiT1bqrwUcgggBEQIhAIQFhT4PF7ESM9oBpj4s+4UoaOXF6HYRNiJwUpOnWFOy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 515682, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhBw9uCRA9TVsSAnZWagAAvKkP/1GHf6G6CB/eQrnDg1o5\nY31AQPdYghgrT30q/M4t7Tkbycl8zliTSb6SFJEE5vnHZcKbH5mvpqGLiPFF\nKKdJLEIKfNZZ/tGIEXO1/fdQGQgdK/91m9C5Gicji9q9tzzP5CYycIv3J6JQ\nKDHsV6gs0J2jwtngxnMUZFhCPHPn1p22ijgk4JwhyMpW/rYTARkk+ZYRT50e\npx9ZnHxdw0qpE1VYYDW6A6Vgx5IjE8nKEsnGeEHLTDDALb3RBEGMnKEu+lyT\nXhYSzDBvW179EcCNnzU5rY6Dp3/IjftLMbQ0w+brj2woGZgNeva2GtieLIUs\nVq99p3v7JzdRsFQRm3ybS5cK2bBMeVYfzNxJplO/jKLp5DITWOedZJt5+70d\n1dbgcAZRPjIxl4GaW5GroKtw+ypPl9hx9IxE2dVKUmOVyj2IpBpKdXx0vXZb\n11vwmOAsRV/SKxecan1gBTvvLj+RovCvgJ1nXrfMkV8idAg4sjBcGIec01i4\nCoaoYNRvmG6bFJx12kQ9pe103Ix1yGH06piQduVZdHM3CMISMOwRcbdHxkQJ\n49EroXhYYQx8aMmnqFXUsYFgpe4IFAayb0ssWRwQYZUlDuffdPpl6iJ6BHNH\nEw97y/orBOoiikhCO4hnm/5Y7kwNmnhkHY8Hx/09SjX6+upBiSJImeaq7ePN\nyGlo\r\n=BkYr\r\n-----END PGP SIGNATURE-----\r\n"}}, "17.4.3": {"name": "joi", "version": "17.4.3", "dependencies": {"@hapi/hoek": "^9.0.0", "@hapi/topo": "^5.0.0", "@sideway/address": "^4.1.3", "@sideway/formula": "^3.0.0", "@sideway/pinpoint": "^2.0.0"}, "devDependencies": {"@hapi/lab": "24.x.x", "@hapi/code": "8.x.x", "typescript": "4.3.x", "@hapi/bourne": "2.x.x", "@hapi/joi-legacy-test": "npm:@hapi/joi@15.x.x"}, "dist": {"shasum": "462941fd994f11fcb92f59ba9f004706b9a45d17", "tarball": "https://registry.npmjs.org/joi/-/joi-17.4.3.tgz", "fileCount": 36, "integrity": "sha512-8W3oOogFRuy2aLAdlhMpzS4fNBIMiyIa3xBaBYMFgA272/d5sob1DAth6jjo+5VrOlzbEgmbBGbU4cLrffPKog==", "signatures": [{"sig": "MEUCIE7lRomFyJ/wJS7786/ma6LqcCFl/HJ58FxHzY3abucrAiEA8EM+PaR6Mb02AJ/SiykiZ1LH70FPZG0gFsWwLysrP28=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 516304, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpzbbCRA9TVsSAnZWagAA2EoP/0m/ueas/N1Xu6A4XEQX\nD4859UtNMDvHOk3Tf4iPDAWNMAIu6dG2Omt7RqmJRjzK3O4SX+0XKlDUwwDr\nSRVhkkYYgbeT5AE/Fmd+XVt1vtjiAUfGkxQ8J/oOy6B12W6FGSiL+zG+r47N\nN799AycKWXah3mgSYEEneqeYpUbP2baC9J9h1roRr12NUO8O99oO2NXOKT5v\nTDekO1Uz0014TLvzwkM9e7tSLRSqDmUq4DAL7EIaAD7eiqTc6r+eIkLZWnBT\nkwMmcba/LtTNzIRuGTWMOorDEQcz3LFHF+GRRn/S8UK9cYtHX8GAkcErmtqR\nrv5nlUr5WTrkB2zXu71hmhAVMQzpM4uKLhh1p1gkxclOv2DvR0Ijz/0G8wvO\ncOqCkk0jQoKWb14IaadmzlDcJymHJHF2NX90AaSlhOqvXSdfGQwmTwZ5V/RW\n+HKsWg+ssX+R/JH05BuTFNwqORxA3c/yvuorPDBZ+mvUulq1TOg2cFnOjaHZ\nbFmd5EJapUm2O0HI8CH2Smj3wiz3CRxKACfVT22EJLvAmxke8alMjbiw2ooR\n8c4ptJ/s/c0aiz3pBcXKmEQU0gbEUjGEc9ICgAwClCUTPRPlG90r/TP132DI\nDrSUL0Cy+VIUbtgt91vgBo9FqzLRGQwKod5T8nu//gp7RigrvdHBY6BJutQ+\ntDD5\r\n=oTRp\r\n-----END PGP SIGNATURE-----\r\n"}}, "17.5.0": {"name": "joi", "version": "17.5.0", "dependencies": {"@hapi/hoek": "^9.0.0", "@hapi/topo": "^5.0.0", "@sideway/address": "^4.1.3", "@sideway/formula": "^3.0.0", "@sideway/pinpoint": "^2.0.0"}, "devDependencies": {"@hapi/lab": "24.x.x", "@hapi/code": "8.x.x", "typescript": "4.3.x", "@hapi/bourne": "2.x.x", "@hapi/joi-legacy-test": "npm:@hapi/joi@15.x.x"}, "dist": {"shasum": "7e66d0004b5045d971cf416a55fb61d33ac6e011", "tarball": "https://registry.npmjs.org/joi/-/joi-17.5.0.tgz", "fileCount": 36, "integrity": "sha512-R7hR50COp7StzLnDi4ywOXHrBrgNXuUUfJWIR5lPY5Bm/pOD3jZaTwpluUXVLRWcoWZxkrHBBJ5hLxgnlehbdw==", "signatures": [{"sig": "MEYCIQDZDjZZj0s5dNKRhKjAQ2zVmCeRzaTe+uoNSWzHyqNdpAIhAOjBr2Xui82nnjrFINT9X7nDnm0mTCvg982PHzqdW+bf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 518480, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhqGorCRA9TVsSAnZWagAAg/8P/RHXjHFBMg4NmX/p29f/\nTUAhE0M2OR5tWXKaGjN46A6yZ2SyjBbQkJHyzeJxZT0oulO8n+PYq9DI/aNo\nFSo0t7x1IqbAvFxKYDqY1HXkfv1U0qAM0Jyy68yw9NuwwuOPjxFmvojR+mxM\n+8sN5Rx19dKfdAwlK2BewLiHxvWkPfrT7iSEc4tVX5hVAApLOSr/fO765FWU\nvgh2cctuTMPK09jcypAcQhVZy9KfSzEf0s6yIHLvvLc87InpSFirA/7pxnfq\npF1TRkiRSh2GmP2d+eBnu6BRy00W+IWoR8xUCLkm4spIDq86aiOV4F/nx6sO\nNZ16ruA1KlZ2Vq3rmIBIszZgYdgYCOaX07DKTY60gQbVCep3uSRVqNoREhi4\nZoxrJcMvNGSjKfW+90tWGj5YdtetVcwkeY+2OFTH3AzogE4X7Ly77GmbcdhM\nUpZNGt+DKm7JMEFIXmPvPvopudatWO6F4Ri0RlEhUEpZ/KAW9eepjADQcPFO\n2xCbT8NK995DGOgapM795nmh06vQv+xA22PfxKdJhZSdRy98eEj62mGSKoOi\nyWRaPpfcZGYdbu5FiV7TeYTlX1bUPy+kxGGCk58ianRMqE0xB1CScr6X0A0Q\n1cCcHB7CUGTeZo6nfQu1a9QdZw7u8uqXCCuaJ1jpATop7gZBYHB04Q8qllQ1\nV2gk\r\n=kh3D\r\n-----END PGP SIGNATURE-----\r\n"}}, "17.6.0": {"name": "joi", "version": "17.6.0", "dependencies": {"@hapi/hoek": "^9.0.0", "@hapi/topo": "^5.0.0", "@sideway/address": "^4.1.3", "@sideway/formula": "^3.0.0", "@sideway/pinpoint": "^2.0.0"}, "devDependencies": {"@hapi/lab": "24.x.x", "@hapi/code": "8.x.x", "typescript": "4.3.x", "@hapi/bourne": "2.x.x", "@hapi/joi-legacy-test": "npm:@hapi/joi@15.x.x"}, "dist": {"shasum": "0bb54f2f006c09a96e75ce687957bd04290054b2", "tarball": "https://registry.npmjs.org/joi/-/joi-17.6.0.tgz", "fileCount": 36, "integrity": "sha512-OX5dG6DTbcr/kbMFj0KGYxuew69HPcAE3K/sZpEV2nP6e/j/C0HV+HNiBPCASxdx5T7DMoa0s8UeHWMnb6n2zw==", "signatures": [{"sig": "MEQCIFOkd6aBO8Z+S0SY2IqsxmIx2kihYKc6racrhe5f2+frAiB8F+7KkKlXj2C/t4qlzcIF7eYEyRZYDC6tUfqiw2tImA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 518908, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8dQrCRA9TVsSAnZWagAA+zYP/38o/MYYBhvqhXcltXJP\nRsDDjS+bt/pSaZ4cov0/N+F41npBb1UNLHjU30OW+ZpPRRTPFfTrWjyELS/J\nRaJayCAT2F5zNxEEXd9eZSGg1jSd1AyFUmvWlnI8CtHZ8MdCzOdcFHz04Wmx\n1ODUqTKsZuzdLSx/vwgfLwZsMORag+6S8kXYcD8tLzN4efxACy7ftMs8awuI\n8WLYPzW5CNPhO34hsdXREawOUV/cmsE0I3OLGQUieUij2ze+32sVgc1bMerH\nOB8dgtt5nipFZCZANNAap6DIvksWNYu3j4d1JQieON2lzip6QFbbj<PERSON>TokcL\nVpJQWFDW2S6ccwD4FNU8vwUY/W/cdbtMEnqlDzhYM9vTTnek/79DrgwbWWd8\npiqhBGfpVut3Lv/VmvWJ4zkHCFsrWWEl9kghhnrXTFDK2ORQkR76fLWYQ665\nN1Q4QVBIvWUg9kGFhgBYGGkbfC9TroxFV6bvRFZfYJa7q9owbR0vws4i2vAI\nGoP8IJJjTjIXFjiJ23SKisZgOutFhJ0KJUWmuAjUjOzhU34Zh5w24aak7WyT\n1fu4DxLmjUnC1y1aYk1HyOMuSWgTbLn9dqaxNSMWL1V8lqdagN93WUzz4/KC\nS4rg0i0JmZwS7PcIKGrdVZNZ2QQHLSPatJdlrpJFhkPCK+0pflWr4IveBzGr\nOLSR\r\n=J7om\r\n-----END PGP SIGNATURE-----\r\n"}}, "17.6.1": {"name": "joi", "version": "17.6.1", "dependencies": {"@hapi/hoek": "^9.0.0", "@hapi/topo": "^5.0.0", "@sideway/address": "^4.1.3", "@sideway/formula": "^3.0.0", "@sideway/pinpoint": "^2.0.0"}, "devDependencies": {"@hapi/lab": "^25.0.1", "@hapi/code": "8.x.x", "typescript": "4.3.x", "@types/node": "^14.18.24", "@hapi/bourne": "2.x.x", "@hapi/joi-legacy-test": "npm:@hapi/joi@15.x.x"}, "dist": {"shasum": "e77422f277091711599634ac39a409e599d7bdaa", "tarball": "https://registry.npmjs.org/joi/-/joi-17.6.1.tgz", "fileCount": 36, "integrity": "sha512-Hl7/iBklIX345OCM1TiFSCZRVaAOLDGlWCp0Df2vWYgBgjkezaR7Kvm3joBciBHQjZj5sxXs859r6eqsRSlG8w==", "signatures": [{"sig": "MEUCIEbmrPTSsiI7+W2aucxum4LRu0wgWn69wwbFcAOK9685AiEAoVWqdAvoeDAjCPJEO5v9Hx+SmgKJqwd5Xs8czCC0SnU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 523718, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjLE51ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp/QBAAkNJk8jrCkZPmwTnLtQDszKsIu1Be0ZjVYUYiqucdlw/2f97t\r\nGQqrC2TFu8j9oqyvXx/3CR3nqdyhc5+9Na32J8cLlmhFPxMfqFgiAresGqWx\r\nR0VcZ3GlXwrpn21XGaRWDCgZ3X/zWsXWTzmmOKhN9/tQNeNQ5pqy4q06rYeM\r\n9QEHzMEl95kv+HKs6M/UQWGCZFkah7w3ZuZ3frL8WwskYNTxogGs2AfaT/Ud\r\nabIAspoGO2P5hS7olX7wFx43u7/NCav8IcKhXkLGpBgVKxsBa7bEH/sQta12\r\n2PetIzYYHJfHSWdA+U2LNlnrdUr/qQ7MHWK3jPd75UT0sTGKmXZyQg5x4ixY\r\nBOIj3Q79l0G8opCn5iq5cOZL8gzGRY0Dy1zpUniibin4TyaaQ4efdbHGRtiA\r\n2mt8Sdxp59wNVwm0a3hgSd8eSzJfHO/fb5jr+RhG+mQRFV+Inr4GyEMD+29U\r\nEYyj7BHtN39xnRGcikm4GmOJ4vXL2vlOrRFNI3k7SWbWvP4Xx+plFKaw/96p\r\nqq9mscKuyi9cS/UfgGhu7BrKq8hKw8L2R11QMDPHzsh/ncxEyqfVSBLPqpm2\r\nSo7WwD6iUKVruQOgJC8OjmSZ4LyGRQOGbbtm+l14VqEWGj9WdkMU2pkP8TVa\r\nA02NIIO3tO+8RboZjQ4lyZlGKKI+Ic8AORw=\r\n=Njf0\r\n-----END PGP SIGNATURE-----\r\n"}}, "17.6.2": {"name": "joi", "version": "17.6.2", "dependencies": {"@hapi/hoek": "^9.0.0", "@hapi/topo": "^5.0.0", "@sideway/address": "^4.1.3", "@sideway/formula": "^3.0.0", "@sideway/pinpoint": "^2.0.0"}, "devDependencies": {"@hapi/lab": "^25.0.1", "@hapi/code": "8.x.x", "typescript": "4.3.x", "@types/node": "^14.18.24", "@hapi/bourne": "2.x.x", "@hapi/joi-legacy-test": "npm:@hapi/joi@15.x.x"}, "dist": {"shasum": "00ac55ce6495596545cce45309f38738cfbd7cd3", "tarball": "https://registry.npmjs.org/joi/-/joi-17.6.2.tgz", "fileCount": 36, "integrity": "sha512-+gqqdh1xc1wb+Lor0J9toqgeReyDOCqOdG8QSdRcEvwrcRiFQZneUCGKjFjuyBWUb3uaFOgY56yMaZ5FIc+H4w==", "signatures": [{"sig": "MEQCIFCJbjUohzqI9Gs7YVzdESW/BfCo3mgnue+Te9nSpGV/AiArWXPayhPAUT5rIYJRF9WRFuomHnKxF/jcRC1Qab+VpA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 524233, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNcMbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpgVg//bnqfF5t93jWNZWSy93juFHfgl/IG8UXXW5kualKg4sQYr49I\r\nWAUIg3X60gAw6e7XUYujs9xOy78APnNcVI1aWbBGkPbN6F7Mv+qsadwU0F0j\r\ndiH/BcwZqged9tVvH6erae6NovgVAzDtHeCld8qgK5Psv90DohCOlGveJSDx\r\nHYhqLFpnzxkE5LAEelTmPlGuBrbGwO1VWvnl9LiSZwoJTR1JVUZ1Fumuws3a\r\njz3zZ0qtuiw+ibE3CZf2pFWXs+7g1/CKk7KxKyZe/MbzGbESZMDSdxqJI2KE\r\nQw1OPjE7TfEEfFguNhjIGRd7zOcKvx7oqEgbVHKOi1REU5PShpPYgAPtp/C5\r\nfzEVIXwYaze5kXYQ59a1tHh6JyU41GG98118HefEE7EPfCPdwk2uz31/l00A\r\nDMW4OIneS3VGllOJL/NoOCXsK3zeE+2wuJmtXt1G0or3cMUBohRjo8mfX1FT\r\nYBVkKiXjvyROJKb6ZqQUdr9MnBxElrxp9ViHTemXTG7vk43YtvomUQDqwu3W\r\ngCNLDym3yijA5/2dz8TsTXINhSPa5kjpufdw/xCg0R1TKe7lmFMVosHWvcVX\r\nnK+Yxmq8EFQxblnUK50aqJU7ihqqN/LjO27ddYR7kqWNh4Hdkl2DJtTqhG7H\r\nyTQ1dWoIao4gKVBXZRnpp3sEPBnUN7xfGIE=\r\n=3xOV\r\n-----END PGP SIGNATURE-----\r\n"}}, "17.6.3": {"name": "joi", "version": "17.6.3", "dependencies": {"@hapi/hoek": "^9.0.0", "@hapi/topo": "^5.0.0", "@sideway/address": "^4.1.3", "@sideway/formula": "^3.0.0", "@sideway/pinpoint": "^2.0.0"}, "devDependencies": {"@hapi/lab": "^25.0.1", "@hapi/code": "8.x.x", "typescript": "4.3.x", "@types/node": "^14.18.24", "@hapi/bourne": "2.x.x", "@hapi/joi-legacy-test": "npm:@hapi/joi@15.x.x"}, "dist": {"shasum": "b8e9e143f0188884563e6de50f8b23ddcd3cb2f5", "tarball": "https://registry.npmjs.org/joi/-/joi-17.6.3.tgz", "fileCount": 36, "integrity": "sha512-YlQsIaS9MHYekzf1Qe11LjTkNzx9qhYluK3172z38RxYoAUf82XMX1p1DG1H4Wtk2ED/vPdSn9OggqtDu+aTow==", "signatures": [{"sig": "MEUCICc0qHZi3pc4jual6DIL7u5ECT+6UOd+QnprIGjbWQIgAiEA8ElJfP1fllIHoycrIu5GQndGqGVKdGQn7rHDEoAMakE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 524947, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRT9yACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoQ0Q/9GTGRLESuDKOg7gEMHQJMjOI7R3/P5yxFjLQqZB5J/r+W1WvT\r\n7Smi3zykZ3RJOGBb3Kcl/dhK4b2S9z7YqDDg4ZPuRsuOHsSZ76Mwyd3XT2xm\r\nGjaEXtzWB8onI+fTINnS/GEtv2lvQjcj3Fn6mgULW5306sTEC1tBkPIXHxQy\r\n27Wk8M/zuOy2GZ97uUfWfdME+SU1RHN3iuBT+70DfoWC0AUUdcYcT4H1HiHB\r\nNSIK2clIamZB+WjBCmZAbGS5chFSPtGK/uFby8KBlfQC17RxOMIWRfFUz4IJ\r\n85ztalN5n7lQIjcS0pID5Z3YlfnjEas3XWxTfFTb28phjDj9yrqPCjpb23vw\r\nEn7VTYpb4qfrQxQY/amdFRzv27ynKhA6nSL2NUoZQDsC71AVQsv2Rm5B46lS\r\nVv9+hiVFrZaDwCO5FnvPqbWBT0lRqcziCJst38CRt811xnusmRw16JbR2GFT\r\nJzu3rUypj3RVWULIiQYw94/mXv6xjPKFiKAP4nPo7iE4Ydwk6Mu9Bs9DdoUg\r\npjPL3omENMFfZ2TFSVvW++WfGMHv1AMWwXjRhmzo0sR5/le3eeXGKzm8100i\r\n12+jkiCgT0Dn9OCri6XxX75SP1GLnSApx/zEbGI6zr5tu94l6CWV4iqduUoO\r\n6/ptAI1r18CqKN9rh6wVxf8U3pO23+um30U=\r\n=KwQB\r\n-----END PGP SIGNATURE-----\r\n"}}, "17.6.4": {"name": "joi", "version": "17.6.4", "dependencies": {"@hapi/hoek": "^9.0.0", "@hapi/topo": "^5.0.0", "@sideway/address": "^4.1.3", "@sideway/formula": "^3.0.0", "@sideway/pinpoint": "^2.0.0"}, "devDependencies": {"@hapi/lab": "^25.0.1", "@hapi/code": "8.x.x", "typescript": "4.3.x", "@types/node": "^14.18.24", "@hapi/bourne": "2.x.x", "@hapi/joi-legacy-test": "npm:@hapi/joi@15.x.x"}, "dist": {"shasum": "4d9536a059ef0762c718ae98673016b3ec151abd", "tarball": "https://registry.npmjs.org/joi/-/joi-17.6.4.tgz", "fileCount": 36, "integrity": "sha512-tPzkTJHZQjSFCc842QpdVpOZ9LI2txApboNUbW70qgnRB14Lzl+oWQOPdF2N4yqyiY14wBGe8lc7f/2hZxbGmw==", "signatures": [{"sig": "MEUCIDRuCtxGBenllFYK5kYWhjtFTfLaSce0zI5/c9T2Q77ZAiEAiFIBWuGXvRKnDbKpCzwUmcI5xjjVjsO4nuru4IZplaM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 524942, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjU8GNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoAthAAor/x09xAWMgdeEPL/wBAzPxJq+imz3/FoNwTwbsAR+MnPolc\r\niZ9ydUvcX3WfGcWzLLprWSbvVopIC3m/WzaM6f8voTNcNKLpmlZRn66yqu87\r\nuZodWrmAU98C/YgFgU5Z0MjUXZjo57B7ABKPH+l2bgg2Mgwz1wWwL3VF9uJl\r\nE4uPRw13fN/GN3Jp5xrCh5q3lIOdGxw4LkAiPcw8vPeG+jDV7pkzuzXq+Rqh\r\nfaGaqdJzMoraAtvqGuBNGIGVZcS9jyu2+o/Ni/6uAjV9vHWmg08gcp1jezUZ\r\no1+eHRubZrobE/qrlu1NYxMNVsYdDyz0hU2U+JBLMoy2BdNvp9M5LAwvCb+G\r\nBM7bL6pwCBy4TZziJVng38xwmoVubuCB6J8vSdwgo1Xhbam2vt9Rhg/Qdr8x\r\nFRGtWbBn250Dx2R4ppNMHIqDDA1b6NCwP+afo6wlTGOU2XvsVDsqn9Zxm3l8\r\nX/EDi/KmNxiXu91K3rwyWL2phIIYQpww1nowQClSEX8RM+pOexHAS/7OILly\r\niwt0+dMQ4vlFzA1JidbsAGcUheBMVC6gw6L/Elij3Ndkf85/qWaXmaZ30YNg\r\ngKzEjRAtExc258WUCqTW36I/XlSnI3FBtyBCBbvHw33MAUFjYEJPil0RNqMm\r\neiS5/gx1KbgVCEjnCYd1ZARG4U7CZv0Gv+0=\r\n=Khb8\r\n-----END PGP SIGNATURE-----\r\n"}}, "17.7.0": {"name": "joi", "version": "17.7.0", "dependencies": {"@hapi/hoek": "^9.0.0", "@hapi/topo": "^5.0.0", "@sideway/address": "^4.1.3", "@sideway/formula": "^3.0.0", "@sideway/pinpoint": "^2.0.0"}, "devDependencies": {"@hapi/lab": "^25.0.1", "@hapi/code": "8.x.x", "typescript": "4.3.x", "@types/node": "^14.18.24", "@hapi/bourne": "2.x.x", "@hapi/joi-legacy-test": "npm:@hapi/joi@15.x.x"}, "dist": {"shasum": "591a33b1fe1aca2bc27f290bcad9b9c1c570a6b3", "tarball": "https://registry.npmjs.org/joi/-/joi-17.7.0.tgz", "fileCount": 36, "integrity": "sha512-1/ugc8djfn93rTE3WRKdCzGGt/EtiYKxITMO4Wiv6q5JL1gl9ePt4kBsl1S499nbosspfctIQTpYIhSmHA3WAg==", "signatures": [{"sig": "MEUCIG0MeRfhM8JOF5qypBUlvPnycPJ34mvo15MxGGljGlvOAiEAt/P/ODRAyZVupeFHY8kSJsTZTIqvJ6/w3XiVmHNhLbg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 526599, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjYO8eACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrgQRAAlVUmRAF8cmFym+2XBJplHCCW82q5eDqeTQPpDH7OlYRibEGJ\r\n8ftJ8m/kyN4CCj2/nUyZ01j2eICqupV6YpLjkwu6MdyMS/3J5eqpz9OLo9tC\r\n6gN1BQ45H3KSI9ycr0LxPiYFFoHN+eBGMrIq+JHYoyYtwNozaofMloNN4iQS\r\nUTDCmmT68QVzdPb3U/Bbh18HmWkl2hLc43Sd9GEDJ5kKplmVM/O8FglT6BCO\r\n9IPY2P6mKiHu5wYSMtnDhsPYaehoBUSWETqK8TO5X2/tKTG9HZqLd+cJcDpH\r\nuC5oKXRiqMkdqaBb9CTXmOqfMpufR4TqqGfDPOiEc1VtjsqB1uxMtC+q7Ag/\r\neHQcTH61yW3i4Z5EGFY0XgREF/rWLIJQTN+WahtvDaqu0LRJOrOyh5XMIg3r\r\n8Kvuu9OPSHBI1bskNi8E2kOARKD4FVLWYBrPp1Uklap3CuqVInPurSUEgemd\r\ni//Q3he4SYO0dsRZ1i0mbmbbUKCSw544WvuLtsvWo/OJ0u0fyQXp8KxjIhvE\r\nX/IyxG96CnsPrLepV+34HXUEC4hRlUH7cYX5u1eCem4H9dtQtGMaOIWFKzCB\r\nRg2dy25IQg2y7scLHKIdcEX2h+WU8WOmJFc4tQftmFcNc5KiZKL2K881w7GH\r\nuOy+kwJUCRSxrB5fjHZO+fijYoLci+KG+rs=\r\n=DckO\r\n-----END PGP SIGNATURE-----\r\n"}}, "17.7.1": {"name": "joi", "version": "17.7.1", "dependencies": {"@hapi/hoek": "^9.0.0", "@hapi/topo": "^5.0.0", "@sideway/address": "^4.1.3", "@sideway/formula": "^3.0.1", "@sideway/pinpoint": "^2.0.0"}, "devDependencies": {"@hapi/lab": "^25.0.1", "@hapi/code": "8.x.x", "typescript": "4.3.x", "@types/node": "^14.18.24", "@hapi/bourne": "2.x.x", "@hapi/joi-legacy-test": "npm:@hapi/joi@15.x.x"}, "dist": {"shasum": "854fc85c7fa3cfc47c91124d30bffdbb58e06cec", "tarball": "https://registry.npmjs.org/joi/-/joi-17.7.1.tgz", "fileCount": 36, "integrity": "sha512-teoLhIvWE298R6AeJywcjR4sX2hHjB3/xJX4qPjg+gTg+c0mzUDsziYlqPmLomq9gVsfaMcgPaGc7VxtD/9StA==", "signatures": [{"sig": "MEQCIBiGk/J5obQsv0P0qScN3dBFH6wukV/6KN9jI1/AkxRtAiAPyaoUs+tM3U08rrCWqrAi+L7o1NIx6Oybefk6RXs5cA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 526390, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj5ezoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoUQA//Zeri9ojdOMueQpTa37lFz6xBkh3obfALdCzeWTEfWKB62clK\r\nQvLQjrs1VwrYDUAo7Rq9JeFA9VIQMk4dbD1hmES3PUkcwaCewTnntMxyDuYr\r\nJMYslJlnaK6sWHfbP25xDoaDhrOvzKwPdTdF1Ks/juNlpSdl4nB+TpJPEAdH\r\nGrKIhhE+lV+k3JbX07NXu6qVdrAgD3zwqzfUgu811XNfMjVMdKQP92qai7I0\r\nLD5etp49BnLVuyAUfYj5GCeSc3EZEH6xDw7tEC7WfzcYR0yt/JDF62Zn+sSn\r\ntbCVlYqvaIchVagrVozirXlhdWMlgld+nHXFzG8tymXtXtFLFwFh3vozjUcQ\r\nBwt/pAolAIWFgkbiTFC0+5yFstd0p66TzRitz0HlDQGFmCgZtHnba58NGUtr\r\ngUpe+rVJhyHOdh2+upcdi997POBAziw+xLoxPWtwaoq8Q0iYBmeGeOAYzkdT\r\nqOB+B6gASCr3EqOQD60RRLwYWfLnT5TM4WccxtoQ9ouahl1osXpM3wZsYAeh\r\n3IKOgWUdzkHIztqNGmQPg1kgKBMrSe+qXG4tAwWMGfj0y2YcedpC8Ay2+R+k\r\n9HUuF7gqojwrPoaQ5Fsqk83HKCkFfU8mwwSt5RMu1ar7yBSG9FAVshqodFQS\r\nZCabHMTVYLheHTGIITW5H8/Xf3r+K3Mj6fk=\r\n=qa9e\r\n-----END PGP SIGNATURE-----\r\n"}}, "17.8.0": {"name": "joi", "version": "17.8.0", "dependencies": {"@hapi/hoek": "^11.0.2", "@hapi/tlds": "^1.0.1", "@hapi/topo": "^6.0.1", "@hapi/address": "^5.1.0", "@hapi/formula": "^3.0.2", "@hapi/pinpoint": "^2.0.1"}, "devDependencies": {"@hapi/lab": "^25.1.2", "@hapi/code": "^9.0.3", "typescript": "4.3.x", "@types/node": "^14.18.24", "@hapi/bourne": "^3.0.0", "@hapi/joi-legacy-test": "npm:@hapi/joi@15.x.x"}, "dist": {"shasum": "6b2dd135eaffd93e46e2a3bcfc652cc6b4a163e4", "tarball": "https://registry.npmjs.org/joi/-/joi-17.8.0.tgz", "fileCount": 36, "integrity": "sha512-2XKR4IFBiB15t/HBx73Xc0/2/X05++GW/mDpNVOb96EscWFx13Rf9sfaTOa9i53F6AKtRPCtX53kfOTDddVL/Q==", "signatures": [{"sig": "MEYCIQCl1bXxMPT375ot5rQdaxL7sQTh6NrwnuiyUK18mhPoJgIhAPQeQvQKs68lhAW7ONG9Nxr9CxhAAiOMsdqhpV5B1nFI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 527290, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj8gznACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmon/g/9E1zJmPeZ47aYUiJKTb64YRUZW3gsXxQi4fDyq/pwxCBsFf0+\r\nHr7v4mHm8OQmNaUp/r/YWCJS7ZeLZ9m0XpwKtBAh24HwPW79XVFljRXqgf63\r\nEOJ82Gb+tFQ2IY7riP3LOQfXp3kNH/0TOcwt2fN9RaTIzP+1y4JLdrelzmLr\r\nQNGKts47LgIyxnR9TwUwlp/5K6jxvjEXMU2I1tVNQ/a+l3EKQkNxxFNOm4BU\r\nfzYbpF13Sye6eDiTprExRc3oCfE8K9ekC/8FRnBWd/KaveAkCbLPyOUqGngg\r\nOpvx+TfkPwwgPDAq6ZXLXMOoKtS1XGLbUOFnZBXNdfdq0v9vU2YR84bfosbD\r\nG3rPmYRK5I8ePNUnmt7ZF1MLU2DtoMork1HxPJsaS8BiH+vraQDyBOKCtwRX\r\ny81P324jVeD5ypx9WlStuqeSJogjv3fjSZ3aQ/hOygtlwByNh9qvxcBC8aW1\r\nSefY7QQJKxojr+0Tv8HbRM061L1Eq+U766ZjL/GO8QQ/1SFXX7EiFQSxiOmz\r\n7usk6WSUXgz2I3GTs0RisNTCApb7YlkD1yyg1TR0a3GOKfVbL+YcoEudBSnV\r\nHOGKpYIvS9125UYP1PDPYyxp6CeSrfp8yv2ItD8W3ZSD0If3p3VMwfAb7x8O\r\nVO56CVqcfC2p07jcbgBBiKUdNVAU66WyGYU=\r\n=1rhn\r\n-----END PGP SIGNATURE-----\r\n"}, "deprecated": "this package has been deprecated"}, "17.8.1": {"name": "joi", "version": "17.8.1", "dependencies": {"@hapi/hoek": "^11.0.2", "@hapi/tlds": "^1.0.1", "@hapi/topo": "^6.0.1", "@hapi/address": "^5.1.0", "@hapi/formula": "^3.0.2", "@hapi/pinpoint": "^2.0.1"}, "devDependencies": {"@hapi/lab": "^25.1.2", "@hapi/code": "^9.0.3", "typescript": "4.3.x", "@types/node": "^14.18.24", "@hapi/bourne": "^3.0.0", "@hapi/joi-legacy-test": "npm:@hapi/joi@15.x.x"}, "dist": {"shasum": "82f08b9b7b16e770537914155e78f3fe185b9d18", "tarball": "https://registry.npmjs.org/joi/-/joi-17.8.1.tgz", "fileCount": 36, "integrity": "sha512-Zoafe6E2kQ+hkkJQSBnOxA8beb2LF33keCxZP3vSy7qTHj9/2GAEQ9KLwfe+PR35WvspwNz0jWajDIHLnCKfpg==", "signatures": [{"sig": "MEYCIQDO4/KkM4yfrRD8rvnAhLkZOwnf7HFPxEMD2DB8lt4bdAIhAI5CqR8O5t77cYM9CRvQepSEP40rZSIvWJXYUDrXxdNx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 527579, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj8hd8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrl/A//Ygn17x8czK2KW5+ulfc47feQti6EacQEq7toQZ4zE6D/fz6y\r\nJ373Jy0ww3ZEFcGjgD9T3fDLM0rcPduKNXhqR4dAh9zpxixAQ2BnmckqIlcb\r\nkWd2NtHxSqNEzbQ0OsIiOokAeSf5ZHs33ZpQ/UZPUvq1MK7Bqj4jWFolK4rI\r\nB0nTUnzwDAJrvt/Ue4h/Yk3ADse2nhN2owmYqP6XSeSdSLpHsnv9BOM/XnYx\r\nC82U43K/6PY5h8W07aia7BLmFhklEJHhWcTbt2Z6EhoTanHto5swrX2nE0C7\r\nDN8tXf91t1Jkcdfz/f8g1doJVBvlty/GmNMKHv/nzuqKi8d/FcTXsKmGdqoY\r\nRzV1vb2C10gdjphl7F0uLlWXGLTCY4R8vQbpitd9TI6FlbzjxyCVAUxoPtMt\r\noL0NIFZqM8v7I4FHSMIMvsQnTlf7NcjWryd5PGmuVAsTu2wlVrZDbc4kVKNu\r\nDN/8vzk7B/KsIojI7RxJdwSCm75b6SeKwjleULuTOqhBDDZgTh3bBiw7V1S4\r\nmHIbY8vQuE21IplkqjtrPnlEf1NWOREqGPUvWwbJvOoBSTXDI8PEQKzonHrv\r\nveLk/9JbWfTeyk7XTYCFoMT0TkdOioVu3+ZG+tejOEIlUFhEQIiIdb66ByRc\r\nXwxK+1GrbjnISiWYpV7aiYF9GISgpuTAJ6U=\r\n=NRNW\r\n-----END PGP SIGNATURE-----\r\n"}, "deprecated": "this package has been deprecated"}, "17.8.2": {"name": "joi", "version": "17.8.2", "dependencies": {"@hapi/hoek": "^11.0.2", "@hapi/tlds": "^1.0.1", "@hapi/topo": "^6.0.1", "@hapi/address": "^5.1.0", "@hapi/formula": "^3.0.2", "@hapi/pinpoint": "^2.0.1"}, "devDependencies": {"@hapi/lab": "^25.1.2", "@hapi/code": "^9.0.3", "typescript": "4.3.x", "@types/node": "^14.18.24", "@hapi/bourne": "^3.0.0", "@hapi/joi-legacy-test": "npm:@hapi/joi@15.x.x"}, "dist": {"shasum": "4a68e5e634885cdd4ac64f24c30e3bdd4d96da73", "tarball": "https://registry.npmjs.org/joi/-/joi-17.8.2.tgz", "fileCount": 36, "integrity": "sha512-ngyxfqOHLVfaiNycu9IpKGRKn8M0nYqMi6kw7UE3HwuQNzRcx92zctu167G7mNHf5CV2DPdV7APGXB5Lsbr1gQ==", "signatures": [{"sig": "MEQCICMPDVnguz/9u2zsArjXgH9Vd6Rs61r+gzJIDuCIo6+OAiAB0dvnClVBuqLVL+PhoKlvmIuER2GGauOCSMptZK9sIQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 527612, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9HaDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmonyg/7BwVc2O1D9NcTsMEbf+8zBibEo0fPOboYiQIkV499kQ3kasdu\r\npQVlxuG2RGCWxoJtJOF1K/b88hHoSlbplUz0UsYUYevOQuAlTtFcLtV/25pt\r\nKQ12u0uOjUQWAf1JUqlZTujDj4Jdi6iovqTycMh1CqHJ9vVTOVHaZxVx/WtD\r\nI2ZsaAWgSvG25vqc8hAl5+FttSMe3Lykvlt8iK3ern3v9nE6P9JGEK2aDafr\r\nMTBRSn3WKLHPcP3arakPAgemOTWqG0y0ckRYZqC9E2CPl8Gj9HLXZWqOruSr\r\n0SoYk4g34jcordRZGyL9HzGdl+JwirBYOOE3u7cxHyFR7+zqZgSUG59GEx0H\r\nD+yZpqSqzEhazkNQpQZPn6wFuOX9wuc4KAX01B5z6GzdZSKQQcBATLwUCLvv\r\ndbQiEaLY5fjwJWIdGVLxZptbsZHc00YgrO+SmlE9SBLtdF91o76sSvCHR0ly\r\nTWS52uS69AMB18cX5g7/zzV+YlwCm73DC5IrWdREsAR3F1Hr4+79heR0tx9i\r\n1kdhDBauy5RdkMMTSgRTdzWh2zSWagxCLc6gHw3OqlC7aLQ8wZLVy+tzS/eX\r\nyWOm5GMbTJlMebnZxScyNWvG2pGLVd96yIaQ+ah240/bTEFec9UiI//4wytO\r\nGKDXd9imKG3Ca0XkMYHHkK4QN9MRZSe+CZk=\r\n=+FJJ\r\n-----END PGP SIGNATURE-----\r\n"}, "deprecated": "this package has been deprecated"}, "17.8.3": {"name": "joi", "version": "17.8.3", "dependencies": {"@hapi/hoek": "^9.0.0", "@hapi/topo": "^5.0.0", "@sideway/address": "^4.1.3", "@sideway/formula": "^3.0.1", "@sideway/pinpoint": "^2.0.0"}, "devDependencies": {"@hapi/lab": "^25.0.1", "@hapi/code": "8.x.x", "typescript": "4.3.x", "@types/node": "^14.18.24", "@hapi/bourne": "2.x.x", "@hapi/joi-legacy-test": "npm:@hapi/joi@15.x.x"}, "dist": {"shasum": "d772fe27a87a5cda21aace5cf11eee8671ca7e6f", "tarball": "https://registry.npmjs.org/joi/-/joi-17.8.3.tgz", "fileCount": 36, "integrity": "sha512-q5Fn6Tj/jR8PfrLrx4fpGH4v9qM6o+vDUfD4/3vxxyg34OmKcNqYZ1qn2mpLza96S8tL0p0rIw2gOZX+/cTg9w==", "signatures": [{"sig": "MEUCIAsLQg/3/x+nbwfzp6T0JLkV/XZbhFlavgLFnWEkSeeaAiEAold7+Q/UaPoQjgdrR129AMGNISi9UvcMeL8v++08/6w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 526390, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9Ik4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqRjA//RJEqH4DfExixxY3jC5DdA0c0RbWN8oARzPJEhs3gcuZIzKAS\r\nYbkJLGdjVNo0U9+JIgvnFWdHSI5PXP/FUMsQuxRMIrdOWPcubbDn+mOZ4nAJ\r\nr5dzOXavmCu/HxDiEbs08agIlvDsDlitryxlddLLi450R1kZ+AccxDyuJIIn\r\npz6A2Cte4EXP05QNWdoauBZ9jZTjFJxqLF8zcBsEjQo+bT2rDaAiIYoDO3Ty\r\nZQB/rua0vI+vxsEbQ4uak9h1oW6nE0sYSk4d0Zs7Cn+GrPph8lkHlyU02aNo\r\n712EbZoCAx8lsZzKSj2pPFGJ0tUSbiOgmyRDXOUzAp5PZUMk9uGOr4kSXAGH\r\nPzho/+KTt3OPquUZKF9tQTfov0maApYIfZ/ZreN9BfD995YflKwAFuP41vTA\r\n8tejjSvIARyOb//hpk5xs+CDNrtKAL0hda4bOZvKlQt66RdSX7PovQmm4nv3\r\norvgoFvZ0Vq17vjM78iqHJrdnzWOC+MFyF64Md0lNF9pg0tHAR5EpMdVq9gz\r\nIUM4hKltIvXk3oOhId4qc4rKzg2lKj333sp1UaUcc4savQMxUapX2KgPl2wC\r\nZzlRxDe43QDf6QUPoKkUzxIKRhJR+k/jtWZWQm973fLH064PctGpdIj0ECtf\r\nGD3O1LAZrl0RlMaIWyfD0svH7cIQz0fv0BM=\r\n=B7vY\r\n-----END PGP SIGNATURE-----\r\n"}}, "17.8.4": {"name": "joi", "version": "17.8.4", "dependencies": {"@hapi/hoek": "^9.0.0", "@hapi/topo": "^5.0.0", "@sideway/address": "^4.1.3", "@sideway/formula": "^3.0.1", "@sideway/pinpoint": "^2.0.0"}, "devDependencies": {"@hapi/lab": "^25.0.1", "@hapi/code": "8.x.x", "typescript": "4.3.x", "@types/node": "^14.18.24", "@hapi/bourne": "2.x.x", "@hapi/joi-legacy-test": "npm:@hapi/joi@15.x.x"}, "dist": {"shasum": "f2d91ab8acd3cca4079ba70669c65891739234aa", "tarball": "https://registry.npmjs.org/joi/-/joi-17.8.4.tgz", "fileCount": 36, "integrity": "sha512-jjdRHb5WtL+KgSHvOULQEPPv4kcl+ixd1ybOFQq3rWLgEEqc03QMmilodL0GVJE14U/SQDXkUhQUSZANGDH/AA==", "signatures": [{"sig": "MEUCIQDeJWrpi8zYuV1tH9wVq+vXOOMUJal1zloVAVR9f7I7JwIgXEMmTK33GnAF7V3Ug6RDw00HEjdt5tf+ctDn/EtzNXo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 526497, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkEBLCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpQFw/+Oks1G3PGsn607RcCyL27jH2LrWdu5H5HiUCpFqUl8sio8kmJ\r\ni5gIKU9UHsw51htj3by8Lrl6CFIjTwBzCLr6wB9iwtXLH89b4f5zIpMb45Dg\r\nOXLmX0Sv7Sc65StvkMvAqAREgdzqNBJ+gjp0o820Ckl9C96ljjgH+5KEYref\r\nB74ByZIwCEQqiDF7mMlB5U7MAkhzp0hUpTS8CSFX+Wj2xtYAEyL9fYUzWT3V\r\n+vSTLzYEKx4uALwxdQ4KivS5DCudcYsdAs20l7AwgjFEKcUF31U+w9ptOF26\r\nFJWj1ZJe9km8xC9AHk7CJV1J+tKiD1hVA5X1uS9k/p7SZ04c0QY07nH9HvQW\r\nVMXzVHnJrLyjUVzJU+sIfvlZgzCFo/gmv65xUslX0/v7PJJGDGVta4L1wUn+\r\nJIduyVEAuU1qmbKW+djePfkfvtkSpBoWLVr83YDXgVBYb7R1cW1aL0qGdQAU\r\nfmTTwEegM9PU4g1luyu5A7diOWv2j0vIzPv3qg9850NjYnIYdDLEUsbzNhtE\r\nDqmdKciWG2d9NQ4y+q2SOnOEQnPUhU2P2Hlj6tjnB5btcPZU9P7HtPhi/EPJ\r\nIIRAK364d1mh3jmTJP5cMy+kd5GhuVcwrwsdwyqNOFUdYWX2KD4xdsiXFEY/\r\nhIaPEdZ7eGzF0stsvuEX6olPwMtHf8wPu+c=\r\n=oUNL\r\n-----END PGP SIGNATURE-----\r\n"}}, "17.9.0": {"name": "joi", "version": "17.9.0", "dependencies": {"@hapi/hoek": "^9.0.0", "@hapi/topo": "^5.0.0", "@sideway/address": "^4.1.3", "@sideway/formula": "^3.0.1", "@sideway/pinpoint": "^2.0.0"}, "devDependencies": {"@hapi/lab": "^25.0.1", "@hapi/code": "8.x.x", "typescript": "4.3.x", "@types/node": "^14.18.24", "@hapi/bourne": "2.x.x", "@hapi/joi-legacy-test": "npm:@hapi/joi@15.x.x"}, "dist": {"shasum": "52e161b6c91fc229799c3c948f0d219628423393", "tarball": "https://registry.npmjs.org/joi/-/joi-17.9.0.tgz", "fileCount": 36, "integrity": "sha512-PWirKfKoZL3kWHfkGKzdCLGv23c00rI31PKVDMg33b/ANe+bMC/ZPdEnHAoHzn5Hy6BTg3J0A2yRVKzT64e6Xw==", "signatures": [{"sig": "MEQCIF4jH/y326BoyUFKpg3UbH+caSm0WZ7GP8FAeL8uT8FmAiAkbyoMTCjwa48L/cp2f30HV4IqkvLTki1sGdHGPAWD4w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 530168, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkGBPgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo3RBAAljHjQ+59Hm6TCS+lfgoi51o8UqUIkMEalAzuSbvJQJKbsmNq\r\nW4sVoGZxlnWEXOUSwrs8YFH34ut88CZTiH8fqGNKoGAsvpdvXnSsWxSr65td\r\nIkJAOKhS84nSnhrtAZwb8KAJMLsjkEXaHe13/gKJO0Jb4d2nM3vt5bjDFqUJ\r\nCYmhL9XfBUfL6KO1dtV1ETwHVPwtBHEMN5vRh6oNRqtm5F6zjYqdrwGxEuLe\r\nn1TrLrIbrcfDNAT3EgQXZ1pPuLjAqgpN4lSykmXhO3v+e1YetD6ICeayQUmX\r\nRkmsNa31YZZho+AKiIe3+cIx0nic1yzA4LczSAvbjtdPe/W5ad6yiyfgfzm5\r\nkWlIZkp3OeNl68Dq8gzTUBonYBSTKYNnoa1tL88QYBMLzaw4kiUPEuUnLDyi\r\nyl7DXx2z1qdy/iIv1Xeua4CkuynYJjkrctjbl0CaRHcmrKgTiE1UPjO9+s14\r\nwktHhdByowEtwsakTVym8VQy2AwI9Gts1yjCNHnu47HLi+ThfnS8qQEWlNSa\r\nm8gaAsXflKSdi+ANF4U+uZ60NeFGzplPmHyszrzeC5Uc/kV9Qx9TCwDxNqg3\r\nF3fp2N3HGScb+ojk0uIS/dJUCVEJ6qrHRU05tMbjhzUS0nU5IwzpZI1bYZYb\r\n7Kwz4au0ex4YngT3SwqNI2xM3/5atqGW0wc=\r\n=Lyty\r\n-----END PGP SIGNATURE-----\r\n"}}, "17.9.1": {"name": "joi", "version": "17.9.1", "dependencies": {"@hapi/hoek": "^9.0.0", "@hapi/topo": "^5.0.0", "@sideway/address": "^4.1.3", "@sideway/formula": "^3.0.1", "@sideway/pinpoint": "^2.0.0"}, "devDependencies": {"@hapi/lab": "^25.0.1", "@hapi/code": "8.x.x", "typescript": "4.3.x", "@types/node": "^14.18.24", "@hapi/bourne": "2.x.x", "@hapi/joi-legacy-test": "npm:@hapi/joi@15.x.x"}, "dist": {"shasum": "74899b9fa3646904afa984a11df648eca66c9018", "tarball": "https://registry.npmjs.org/joi/-/joi-17.9.1.tgz", "fileCount": 36, "integrity": "sha512-FariIi9j6QODKATGBrEX7HZcja8Bsh3rfdGYy/Sb65sGlZWK/QWesU1ghk7aJWDj95knjXlQfSmzFSPPkLVsfw==", "signatures": [{"sig": "MEYCIQC3x/AHaLrmjEFK6zfU5KatwNaPiqPGA3jTUeOnrBR+EwIhANQ7eP1bWIRu7n3tCnRNFlCweKqaqGLrJmZlWpXDWa0X", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 531249, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkGWZoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpt1RAAmfhzuGVbpKtq2KwN35kIUjfq2uT89V+it+0hrEMWNdLQKqaz\r\nfeoNq0/6dV0P4TexCG0w0UtSuK36da3e5+Yq/4i0CobCJVk2JQc/UdCdSAeh\r\npXBWuxPI+fa2dubu2Zr4qdlyqz9gUEKAxt8KnhzN12OBXAG9X2+COcg7PlJk\r\nmHIrajG4U0NtjXPad5oKDpspH4kzeyNnPkVeOORdG6LN5twm/wHTIA70OBQP\r\nsHhVHicGcKWJ6Jz3qcZHlc1BZL1+hKZcFgvmvDmmC7SO8bFlHV5V3aCs4uRP\r\nM6tXFa23bV7O8OUmE2UxQ3eCrAF6uh2cjJBXhtqgTGilPi+vcc3ZnDVyRx/r\r\ncXz3Jj31Ms6H3yNGfWBXZ+nzARWPfCOGqyscYjuYUQe3IRMiB0yMjv/saCSI\r\n+jYbMZu7xuPaxI6ashWNYKtaOLMkcIXnRHqwuI53uOekp3KiLpwt2FsqGuCs\r\nS764ERpj69HVJ+4hYXfk4V04TAPHSCR2W57P06+G5Id5g6YXr0kZlrc+Fck8\r\ncvtqX1kA64GaQ9cNaWu8pBLSPVcQOVEbKyG9T+XPaos0B/5Nsrbc+ctlyq6w\r\nLix0n8uBJ4f4OHBThI6ZpKczeuKx/5+vkcWzXugwgLUoX/zufsF6/oQFd0O4\r\n3+FiKo5faflUiqNg90SchhB0mOvvN9k9LIk=\r\n=ZgVo\r\n-----END PGP SIGNATURE-----\r\n"}}, "17.9.2": {"name": "joi", "version": "17.9.2", "dependencies": {"@hapi/hoek": "^9.0.0", "@hapi/topo": "^5.0.0", "@sideway/address": "^4.1.3", "@sideway/formula": "^3.0.1", "@sideway/pinpoint": "^2.0.0"}, "devDependencies": {"@hapi/lab": "^25.0.1", "@hapi/code": "8.x.x", "typescript": "4.3.x", "@types/node": "^14.18.24", "@hapi/bourne": "2.x.x", "@hapi/joi-legacy-test": "npm:@hapi/joi@15.x.x"}, "dist": {"shasum": "8b2e4724188369f55451aebd1d0b1d9482470690", "tarball": "https://registry.npmjs.org/joi/-/joi-17.9.2.tgz", "fileCount": 36, "integrity": "sha512-Itk/r+V4Dx0V3c7RLFdRh12IOjySm2/WGPMubBT92cQvRfYZhPM2W0hZlctjj72iES8jsRCwp7S/cRmWBnJ4nw==", "signatures": [{"sig": "MEYCIQDeybuyYCV865UMHPWlXkA7NTB41a3/8WOWethiJQIy2wIhAPlmdXaXnM6b3APt6uJEoFdfBz7DcSjrQiyhmRZ8pYth", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 531910, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkRurUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmop6Q//UnbO1kvZ562B3nSecVreWCDyyEeet5fgsNqeIbVpnkVgUveH\r\nfi1Et5AAqE43nH2iUzhfUMvPCTuUWt7bs3QDWl3t49KTuXTts+8L5tVOG3Gp\r\nffGJ5oxDWL+tvm9u4RO7FALu5LKTh1Jqg6MtTPd2Xn+LHp+c+sOuWwjgDlcb\r\nBCYW4fbuI+CVMyAyuo2qND1+fajAZsmhu0NKwdIVA08VSGRFFTevaMDMN9EW\r\nj62OoeeosMoaHBNQ6kNJqcS15F2DWQLtFi9rrsvR/1a0mv/qeeffGyHLXy52\r\nmw9vFEpfRVcvVAknjXh4BAy2RDPRiMFMHgt50v5JkI9Dt4F7J2WZcneu7yiW\r\niK97YXft/6k2xul45pfx7/QjKTenEhj+ulGlYDmAB55eXUzBMiPwbcjFDAyY\r\nEEmItOholW+HCF5xwqJayP3HUITb2v/vvbPOogKQwYzw/xMLj/pQm0IX+o/c\r\nqGc0/hhoFUFmtxGgA0T/iH+VAJxb9xYs1zZUjhBiHUS5NWULEYieVL6+Qs+9\r\n21K+wfVoGzOq8U8wnAnp+6DtFSgg6dDEWigFVG61Ysk9buGV37zRutZIwOoc\r\nes1KsYy9cEX3rSO9I+GjSJeRDPNEcSmi9MRqs1o3mw+9H/5ljjq0w/4tSsHZ\r\nYACNzKMc8gyZrMrHSopDFZO7JRwuNrCVp7I=\r\n=N82k\r\n-----END PGP SIGNATURE-----\r\n"}}, "17.10.0": {"name": "joi", "version": "17.10.0", "dependencies": {"@hapi/hoek": "^9.0.0", "@hapi/topo": "^5.0.0", "@sideway/address": "^4.1.3", "@sideway/formula": "^3.0.1", "@sideway/pinpoint": "^2.0.0"}, "devDependencies": {"@hapi/lab": "^25.0.1", "@hapi/code": "8.x.x", "typescript": "4.3.x", "@types/node": "^14.18.24", "@hapi/bourne": "2.x.x", "@hapi/joi-legacy-test": "npm:@hapi/joi@15.x.x"}, "dist": {"shasum": "04e249daa24d48fada2d34046a8262e474b1326f", "tarball": "https://registry.npmjs.org/joi/-/joi-17.10.0.tgz", "fileCount": 36, "integrity": "sha512-hrazgRSlhzacZ69LdcKfhi3Vu13z2yFfoAzmEov3yFIJlatTdVGUW6vle1zjH8qkzdCn/qGw8rapjqsObbYXAg==", "signatures": [{"sig": "MEUCIQCtM3Ruq2t4yULeno4vJEBQf0DB2dEGzsNK0LNWl6YpmAIgPS9Dm3TQXNek3Lk6QF4DtM08W0Mg4Xs9Lw5iyFRfDeU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 532254}}, "17.10.1": {"name": "joi", "version": "17.10.1", "dependencies": {"@hapi/hoek": "^9.0.0", "@hapi/topo": "^5.0.0", "@sideway/address": "^4.1.3", "@sideway/formula": "^3.0.1", "@sideway/pinpoint": "^2.0.0"}, "devDependencies": {"@hapi/lab": "^25.0.1", "@hapi/code": "8.x.x", "typescript": "4.3.x", "@types/node": "^14.18.24", "@hapi/bourne": "2.x.x", "@hapi/joi-legacy-test": "npm:@hapi/joi@15.x.x"}, "dist": {"shasum": "f908ee1617137cca5d83b91587cde80e472b5753", "tarball": "https://registry.npmjs.org/joi/-/joi-17.10.1.tgz", "fileCount": 36, "integrity": "sha512-vIiDxQKmRidUVp8KngT8MZSOcmRVm2zV7jbMjNYWuHcJWI0bUck3nRTGQjhpPlQenIQIBC5Vp9AhcnHbWQqafw==", "signatures": [{"sig": "MEQCIFMbvFBL6QREKnhbf49Wu4/gJ3FTB1bGyxv3J1ro2qxsAiAi0TreoG90msqqm/OmXHnXSGofKrhh3643IBfP3m8UHQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 532270}}, "17.10.2": {"name": "joi", "version": "17.10.2", "dependencies": {"@hapi/hoek": "^9.0.0", "@hapi/topo": "^5.0.0", "@sideway/address": "^4.1.3", "@sideway/formula": "^3.0.1", "@sideway/pinpoint": "^2.0.0"}, "devDependencies": {"@hapi/lab": "^25.0.1", "@hapi/code": "8.x.x", "typescript": "4.3.x", "@types/node": "^14.18.24", "@hapi/bourne": "2.x.x", "@hapi/joi-legacy-test": "npm:@hapi/joi@15.x.x"}, "dist": {"shasum": "4ecc348aa89ede0b48335aad172e0f5591e55b29", "tarball": "https://registry.npmjs.org/joi/-/joi-17.10.2.tgz", "fileCount": 36, "integrity": "sha512-hcVhjBxRNW/is3nNLdGLIjkgXetkeGc2wyhydhz8KumG23Aerk4HPjU5zaPAMRqXQFc0xNqXTC7+zQjxr0GlKA==", "signatures": [{"sig": "MEUCIQCbAg48+A0Ya4etWvszODPzo/4dIdq8sQ8jLCw/oQytWgIgOv0J0MXdAbiZkkd2WA1u6FqL7aiLewgLfubdV/YmPsE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 532421}}, "17.11.0": {"name": "joi", "version": "17.11.0", "dependencies": {"@hapi/hoek": "^9.0.0", "@hapi/topo": "^5.0.0", "@sideway/address": "^4.1.3", "@sideway/formula": "^3.0.1", "@sideway/pinpoint": "^2.0.0"}, "devDependencies": {"@hapi/lab": "^25.0.1", "@hapi/code": "8.x.x", "typescript": "4.3.x", "@types/node": "^14.18.24", "@hapi/bourne": "2.x.x", "@hapi/joi-legacy-test": "npm:@hapi/joi@15.x.x"}, "dist": {"shasum": "aa9da753578ec7720e6f0ca2c7046996ed04fc1a", "tarball": "https://registry.npmjs.org/joi/-/joi-17.11.0.tgz", "fileCount": 36, "integrity": "sha512-NgB+lZLNoqISVy1rZocE9PZI36bL/77ie924Ri43yEvi9GUUMPeyVIr8KdFTMUlby1p0PBYMk9spIxEUQYqrJQ==", "signatures": [{"sig": "MEYCIQCPrY2bICGevfhzrRF6EdqC9wUoamnXwLZqWz9Cuu2zIQIhAMjaB9jNA0lp5+VBjV9Mnhp7r7u79Fh03Jw3YaHcYmuU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 533785}}, "12.1.1": {"name": "joi", "version": "12.1.1", "dependencies": {"hoek": "4.x.x", "topo": "2.x.x", "@hapi/address": "1.x.x"}, "devDependencies": {"lab": "14.x.x", "code": "4.x.x"}, "dist": {"shasum": "b88bc96f0e757d37d7d63cbd2f00ba353d6caf85", "tarball": "https://registry.npmjs.org/joi/-/joi-12.1.1.tgz", "fileCount": 24, "integrity": "sha512-Gh3iTjGLqGmQKTDuMFfsV7zT4uHtckqtrp88VFOc89V/sNtshOlAAtEkMM8TxJqRt1Cei00Hlh6/Bp7WjcqEEg==", "signatures": [{"sig": "MEYCIQDFQgyVSA7jvZ0BMe7Hn6aIdeFZ/3JSmcYHu687dgrGWwIhAIGYIJY53BHKOYXBgWCp6qNl8IsxXJz23mlGiTdohwhZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 172192}, "engines": {"node": ">=6.0.0"}, "deprecated": "Please update your dependencies as this version is no longer maintained and may contain bugs and security issues."}, "17.11.1": {"name": "joi", "version": "17.11.1", "dependencies": {"@hapi/hoek": "^9.3.0", "@hapi/topo": "^5.1.0", "@sideway/address": "^4.1.4", "@sideway/formula": "^3.0.1", "@sideway/pinpoint": "^2.0.0"}, "devDependencies": {"@hapi/lab": "^25.1.3", "@hapi/code": "8.x.x", "typescript": "4.3.x", "@types/node": "^14.18.63", "@hapi/bourne": "2.x.x", "@hapi/joi-legacy-test": "npm:@hapi/joi@15.x.x"}, "dist": {"shasum": "f42951137d25c27f61807502b0af71f7abb885ba", "tarball": "https://registry.npmjs.org/joi/-/joi-17.11.1.tgz", "fileCount": 36, "integrity": "sha512-671acnrx+w96PCcQOzvm0VYQVwNL2PVgZmDRaFuSsx8sIUmGzYElPw5lU8F3Cr0jOuPs1oM56p7W2a1cdDOwcw==", "signatures": [{"sig": "MEUCIQDdP6Vd6i2b3W0Qn9xQ9mDVS80nClYca3g32AoG75wZ2gIgLDv3IK7cXxObuUTVb7WfueLOyzKjC56OX99vI+QFVcQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 529049}}, "17.12.0": {"name": "joi", "version": "17.12.0", "dependencies": {"@hapi/hoek": "^9.3.0", "@hapi/topo": "^5.1.0", "@sideway/address": "^4.1.4", "@sideway/formula": "^3.0.1", "@sideway/pinpoint": "^2.0.0"}, "devDependencies": {"@hapi/lab": "^25.1.3", "@hapi/code": "8.x.x", "typescript": "4.3.x", "@types/node": "^14.18.63", "@hapi/bourne": "2.x.x", "@hapi/joi-legacy-test": "npm:@hapi/joi@15.x.x"}, "dist": {"shasum": "a3fb5715f198beb0471cd551dd26792089c308d5", "tarball": "https://registry.npmjs.org/joi/-/joi-17.12.0.tgz", "fileCount": 36, "integrity": "sha512-HSLsmSmXz+PV9PYoi3p7cgIbj06WnEBNT28n+bbBNcPZXZFqCzzvGqpTBPujx/Z0nh1+KNQPDrNgdmQ8dq0qYw==", "signatures": [{"sig": "MEUCIBZYZ0mgxQ5wGeOIPkobcc3U/nM3pG25YoSLM+NIudbFAiEAsRXXjamLu2u4i+ADa2QHm7Vv6s0/FWtiS96bpAlQtkM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 530255}}, "17.12.1": {"name": "joi", "version": "17.12.1", "dependencies": {"@hapi/hoek": "^9.3.0", "@hapi/topo": "^5.1.0", "@sideway/address": "^4.1.5", "@sideway/formula": "^3.0.1", "@sideway/pinpoint": "^2.0.0"}, "devDependencies": {"@hapi/lab": "^25.1.3", "@hapi/code": "8.x.x", "typescript": "4.3.x", "@types/node": "^14.18.63", "@hapi/bourne": "2.x.x", "@hapi/joi-legacy-test": "npm:@hapi/joi@15.x.x"}, "dist": {"shasum": "3347ecf4cd3301962d42191c021b165eef1f395b", "tarball": "https://registry.npmjs.org/joi/-/joi-17.12.1.tgz", "fileCount": 36, "integrity": "sha512-vtxmq+Lsc5SlfqotnfVjlViWfOL9nt/avKNbKYizwf6gsCfq9NYY/ceYRMFD8XDdrjJ9abJyScWmhmIiy+XRtQ==", "signatures": [{"sig": "MEYCIQDQrnZar14r1zy+nNQE5DUF1weosloCmdR0xPz6NCbZlgIhALj3TtgtuCyNwwTexrcr1eJBiJKUFGyWM/zp9UABATop", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 530311}}, "17.12.2": {"name": "joi", "version": "17.12.2", "dependencies": {"@hapi/hoek": "^9.3.0", "@hapi/topo": "^5.1.0", "@sideway/address": "^4.1.5", "@sideway/formula": "^3.0.1", "@sideway/pinpoint": "^2.0.0"}, "devDependencies": {"@hapi/lab": "^25.1.3", "@hapi/code": "8.x.x", "typescript": "4.3.x", "@types/node": "^14.18.63", "@hapi/bourne": "2.x.x", "@hapi/joi-legacy-test": "npm:@hapi/joi@15.x.x"}, "dist": {"shasum": "283a664dabb80c7e52943c557aab82faea09f521", "tarball": "https://registry.npmjs.org/joi/-/joi-17.12.2.tgz", "fileCount": 36, "integrity": "sha512-RonXAIzCiHLc8ss3Ibuz45u28GOsWE1UpfDXLbN/9NKbL4tCJf8TWYVKsoYuuh+sAUt7fsSNpA+r2+TBA6Wjmw==", "signatures": [{"sig": "MEUCIDYSlb3qKSU9fyp5z4d9nqvTLxulqrpQTbSOCuPcqjQ2AiEAsqGvhbhdI2tohaY9ugojCedq//DdJ3TBo3KqstjJUyk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 530324}}, "17.12.3": {"name": "joi", "version": "17.12.3", "dependencies": {"@hapi/hoek": "^9.3.0", "@hapi/topo": "^5.1.0", "@sideway/address": "^4.1.5", "@sideway/formula": "^3.0.1", "@sideway/pinpoint": "^2.0.0"}, "devDependencies": {"@hapi/lab": "^25.1.3", "@hapi/code": "8.x.x", "typescript": "4.3.x", "@types/node": "^14.18.63", "@hapi/bourne": "2.x.x", "@hapi/joi-legacy-test": "npm:@hapi/joi@15.x.x"}, "dist": {"shasum": "944646979cd3b460178547b12ba37aca8482f63d", "tarball": "https://registry.npmjs.org/joi/-/joi-17.12.3.tgz", "fileCount": 36, "integrity": "sha512-2RRziagf555owrm9IRVtdKynOBeITiDpuZqIpgwqXShPncPKNiRQoiGsl/T8SQdq+8ugRzH2LqY67irr2y/d+g==", "signatures": [{"sig": "MEUCIDOT0sefbOiikv+Zvwxl2jhdx0cJfhflf6PXXRKyWBBOAiEAjidyVg6qX5eWWeaS6egScNFvENst1+MztG+wCclwwUQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 530400}}, "17.13.0": {"name": "joi", "version": "17.13.0", "dependencies": {"@hapi/hoek": "^9.3.0", "@hapi/topo": "^5.1.0", "@sideway/address": "^4.1.5", "@sideway/formula": "^3.0.1", "@sideway/pinpoint": "^2.0.0"}, "devDependencies": {"@hapi/lab": "^25.1.3", "@hapi/code": "8.x.x", "typescript": "4.3.x", "@types/node": "^14.18.63", "@hapi/bourne": "2.x.x", "@hapi/joi-legacy-test": "npm:@hapi/joi@15.x.x"}, "dist": {"shasum": "b6f340b8029ee7af2397f821d17a4f03bf34b043", "tarball": "https://registry.npmjs.org/joi/-/joi-17.13.0.tgz", "fileCount": 36, "integrity": "sha512-9qcrTyoBmFZRNHeVP4edKqIUEgFzq7MHvTNSDuHSqkpOPtiBkgNgcmTSqmiw1kw9tdKaiddvIDv/eCJDxmqWCA==", "signatures": [{"sig": "MEQCIGMry+iCdpt+Ctk8W5GAWCJMJaCmMwLMgMZ+uMrw/PCQAiB1YiblKj5RhIB70MAy2q03sfIVmpEpH/ZqmOgWeb6i5Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 530927}}, "17.13.1": {"name": "joi", "version": "17.13.1", "dependencies": {"@hapi/hoek": "^9.3.0", "@hapi/topo": "^5.1.0", "@sideway/address": "^4.1.5", "@sideway/formula": "^3.0.1", "@sideway/pinpoint": "^2.0.0"}, "devDependencies": {"@hapi/lab": "^25.1.3", "@hapi/code": "8.x.x", "typescript": "4.3.x", "@types/node": "^14.18.63", "@hapi/bourne": "2.x.x", "@hapi/joi-legacy-test": "npm:@hapi/joi@15.x.x"}, "dist": {"shasum": "9c7b53dc3b44dd9ae200255cc3b398874918a6ca", "tarball": "https://registry.npmjs.org/joi/-/joi-17.13.1.tgz", "fileCount": 36, "integrity": "sha512-vaBlIKCyo4FCUtCm7Eu4QZd/q02bWcxfUO6YSXAZOWF6gzcLBeba8kwotUdYJjDLW8Cz8RywsSOqiNJZW0mNvg==", "signatures": [{"sig": "MEYCIQDtbzqnfA2LraIholPu0+1s+nkGauURRXBuCBAPmvFT/wIhAN1IOSauKcjZu+OrUiuVaWOwHl5f0/npWgV/bKdzY/UU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 530927}}, "17.13.2": {"name": "joi", "version": "17.13.2", "dependencies": {"@hapi/hoek": "^9.3.0", "@hapi/topo": "^5.1.0", "@sideway/address": "^4.1.5", "@sideway/formula": "^3.0.1", "@sideway/pinpoint": "^2.0.0"}, "devDependencies": {"@hapi/lab": "^25.1.3", "@hapi/code": "8.x.x", "typescript": "4.3.x", "@types/node": "^14.18.63", "@hapi/bourne": "2.x.x", "@hapi/joi-legacy-test": "npm:@hapi/joi@15.x.x"}, "dist": {"shasum": "21adee350465211cbc5e92281cdc0d058b1974c1", "tarball": "https://registry.npmjs.org/joi/-/joi-17.13.2.tgz", "fileCount": 36, "integrity": "sha512-QJQKY10YowIi6yUpDQG9YpeWLD+OHYlY/La8gk7VitrXfy34quHwCu4QKNlBV1rpgQj0YpnWWl4JM+3DU6GapQ==", "signatures": [{"sig": "MEQCIDERkNQd7cbOu6meLxUb02x5GtZ6G7ZIci73NED4JFUSAiAFag2e5AydicxZw/OE49ndfnpN8YiTnhT7AcXWoe+Vqw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 530965}}, "17.13.3": {"name": "joi", "version": "17.13.3", "dependencies": {"@hapi/hoek": "^9.3.0", "@hapi/topo": "^5.1.0", "@sideway/address": "^4.1.5", "@sideway/formula": "^3.0.1", "@sideway/pinpoint": "^2.0.0"}, "devDependencies": {"@hapi/lab": "^25.1.3", "@hapi/code": "8.x.x", "typescript": "4.3.x", "@types/node": "^14.18.63", "@hapi/bourne": "2.x.x", "@hapi/joi-legacy-test": "npm:@hapi/joi@15.x.x"}, "dist": {"shasum": "0f5cc1169c999b30d344366d384b12d92558bcec", "tarball": "https://registry.npmjs.org/joi/-/joi-17.13.3.tgz", "fileCount": 36, "integrity": "sha512-otDA4ldcIx+ZXsKHWmp0YizCweVRZG96J10b0FevjfuncLO1oX59THoAmHkNubYJ+9gWsYsp5k8v4ib6oDv1fA==", "signatures": [{"sig": "MEQCIA/D3mUa1zn8bpwLTbAajPvUltcfRR+DDPEv80RAST5OAiBDMeppcisUA3IWQQBqw7yGXgrK4XKCSOUONizjWLJ65g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 531117}}}, "modified": "2025-01-10T00:31:55.557Z"}