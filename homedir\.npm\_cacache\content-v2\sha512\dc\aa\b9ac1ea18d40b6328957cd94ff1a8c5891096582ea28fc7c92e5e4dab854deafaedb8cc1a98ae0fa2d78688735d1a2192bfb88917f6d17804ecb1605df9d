{"_id": "fastest-le<PERSON><PERSON><PERSON>", "_rev": "17-06fb60def3bd14efea8e34a793a86f5f", "name": "fastest-le<PERSON><PERSON><PERSON>", "dist-tags": {"latest": "1.0.16"}, "versions": {"1.0.0": {"name": "fastest-le<PERSON><PERSON><PERSON>", "version": "1.0.0", "description": "Fastest Levenshtein distance implementation in JS.", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/ka-weihe/fastest-levenshtein.git"}, "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "distance", "fast", "fastest", "edit", "string", "similarity", "algorithm", "match", "comparison", "fuzzy", "search", "string", "matching", "similar", "node", "difference"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/ka-weihe/fastest-levenshtein/issues"}, "homepage": "https://github.com/ka-weihe/fastest-levenshtein#README", "scripts": {"test": "jest", "test:coverage": "jest --coverage", "test:coveralls": "jest --coverage --coverageReporters=text-lcov | coveralls"}, "devDependencies": {"benchmark": "^2.1.4", "coveralls": "^3.1.0", "eslint": "^7.5.0", "eslint-config-airbnb": "^18.2.0", "eslint-config-airbnb-base": "^14.2.0", "eslint-config-node": "^4.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-jsx-a11y": "^6.3.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^3.1.4", "eslint-plugin-react": "^7.20.3", "eslint-plugin-react-hooks": "^4.0.0", "fast-levenshtein": "^2.0.6", "jest": "^26.1.0", "js-levenshtein": "^1.1.6", "leven": "^3.1.0", "natural": "^2.1.5", "prettier": "^2.0.5", "talisman": "^1.1.2"}, "dependencies": {"levenshtein-edit-distance": "^2.0.5"}, "gitHead": "a38af767bd457180af5583bc5498df02992814f2", "_id": "fastest-<PERSON><PERSON><PERSON><PERSON>@1.0.0", "_nodeVersion": "14.5.0", "_npmVersion": "6.14.6", "dist": {"integrity": "sha512-SjW8H8odT35mYXM7XV9lFM3VEM+a38whN+SarzzSFbHBtU3YfqZSfyEWVs2zwXvkr1bpL2KzJE12dDk2zUpvqw==", "shasum": "c443b000b7146371e823db607c4d1a848d84141d", "tarball": "https://registry.npmjs.org/fastest-levenshtein/-/fastest-levenshtein-1.0.0.tgz", "fileCount": 9, "unpackedSize": 13369, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfGCevCRA9TVsSAnZWagAAgeUP/jgv9Hnki1X4Vmfw8/iH\neSZ4BxSnM4tNFf07YZ9/nrREjFAlb89RpNNlxyZnQlnItNFwAtkJMpAhB2s0\nwg2NxiF75BF+fqhJKQO1NX/75E+uxR66MSWaTzoSUPbuXp4u86kF2TuM1hPS\n2vjCaSIGZCZjaTgO06QjfYhx9O7KbyMxK+7GwVIHEaEnPS+C+dseE5ilFGJT\n+8vG0Ytnn+nG0u7hFjIqE3rmns3jRkPc4O4vsBAynbY4pOyJKCs2NZqVgHXu\njjD44Ff5OrmY6FHfseuoT3jUoO8K1HCdTtQDYDifvVEmFHkUsZaETg/7823H\nP0Upr2fv+xDl6Qxq6m6TKOT+zqRk57vrGuNYCDnxWI6f8rE+9QXgayq5aF5B\nuQyHHYt54hIbub5cvv42gP6bHZOILxOYfyN4s7b9LPTzxI+yHAzMovJtfwya\nUdcuneHVZmbFnJLjy1xxCYGhuMttZwpJWnkX0puKA8XwRaco1gU9hNQcrkXL\nQYqi27Aqsl9YoNklqt+46DxkWDCvr4cgpgic3nwubXqY/dXycNDTxgXlSNwl\n/BbIAh/4Une/TVrBxsaYmmfgSimGoFPiJY5JcQ3bwIWxuKl0YZ2unoEKowU5\ngNxRDhtkwvaLmik4OX6vS07w3gncnzw0g1uCrj0mNRz8j87ZtOFWY2iYSWfk\n5Mcw\r\n=WKzH\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD+5KXDDXtY5p9/AawIiNhLouansibzbaGT7Dy3UZ+hUQIgAy3yKKY4WePi0hDDlZB3+cmWhR2ZMEmGB75uiq7c560="}]}, "maintainers": [{"name": "ka-weihe", "email": "<EMAIL>"}], "_npmUser": {"name": "ka-weihe", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fastest-levenshtein_1.0.0_1595418542705_0.7712623768542421"}, "_hasShrinkwrap": false}, "1.0.1": {"name": "fastest-le<PERSON><PERSON><PERSON>", "version": "1.0.1", "description": "Fastest Levenshtein distance implementation in JS.", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/ka-weihe/fastest-levenshtein.git"}, "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "distance", "fast", "fastest", "edit", "string", "similarity", "algorithm", "match", "comparison", "fuzzy", "search", "string", "matching", "similar", "node", "difference"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/ka-weihe/fastest-levenshtein/issues"}, "homepage": "https://github.com/ka-weihe/fastest-levenshtein#README", "scripts": {"test": "jest", "test:coverage": "jest --coverage", "test:coveralls": "jest --coverage --coverageReporters=text-lcov | coveralls"}, "devDependencies": {"benchmark": "^2.1.4", "coveralls": "^3.1.0", "eslint": "^7.5.0", "eslint-config-airbnb": "^18.2.0", "eslint-config-airbnb-base": "^14.2.0", "eslint-config-node": "^4.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-jsx-a11y": "^6.3.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^3.1.4", "eslint-plugin-react": "^7.20.3", "eslint-plugin-react-hooks": "^4.0.0", "fast-levenshtein": "^2.0.6", "jest": "^26.1.0", "js-levenshtein": "^1.1.6", "leven": "^3.1.0", "natural": "^2.1.5", "prettier": "^2.0.5", "talisman": "^1.1.2"}, "dependencies": {"levenshtein-edit-distance": "^2.0.5"}, "gitHead": "a38af767bd457180af5583bc5498df02992814f2", "_id": "fastest-<PERSON><PERSON><PERSON><PERSON>@1.0.1", "_nodeVersion": "14.5.0", "_npmVersion": "6.14.6", "dist": {"integrity": "sha512-OZsU5tOkxCE1qVlKiwUvp8tci2M6+hfePVzY0GHg+La9o/rAWTpiSZgqH2F5ep3Y6dLvQRfPSQumVHF6M0q4sQ==", "shasum": "9a39ad9499db3dd6f6974722d9394b5f28bc3fad", "tarball": "https://registry.npmjs.org/fastest-levenshtein/-/fastest-levenshtein-1.0.1.tgz", "fileCount": 9, "unpackedSize": 13369, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfGCgRCRA9TVsSAnZWagAAtN0P/0zXw2Mn/hl/M+REIOyL\nm9ul0Aq5hdoiLW7qBNllkAJpZJXAgYtir1S0XLfAWzqLv1k67leyr97X7eDp\nqKhA+z7IS5phpA9qpFMUFbCS7ox45TU92t9vFWyM4KN6ZJQ4UipXAG+xPA5W\n06amnbJmu+Xv2+VOd+lx+CEkQfNAta/gaYnEMUkseVyTstiNkMsJ/lU3XFca\nfWaQHDcfmNOFgeulJrJkxKDp583a3nTJadotZs0E0CaNp8Dis/wC6HQ1wUNa\nODQAAWzbgM7eb2JJef1+ZleRiOz1s8pDUdafjUDBiPshDvHNFiZeSVFRlKdN\nXeScBS6AHLsX2y0tdg/EeE4qaGs9RzTBrPxMrw26IQYXJvlx9CM7VXIjS11Q\nhV684nFe173kq97bbHbBNqVVB6nbNxY5IOB/Ce60mzDijSPIHPDJRxcwhNXK\nZyUITfOgkhdYQ9oNoJoEZJWgSLGADTMlDoLKLqgFlXAn8mX0pcqxD6xQVBc6\nTO28zVpvnCqDMd1w/SaxYx89YYkzvp9VnfVrthIli7HFTVdCFe49FHO5MeBP\nDENC1ZxR7NKC4xe8p8x3AVNa5r/JzmUk9g4+MBqvWxHPLcHiPheBkwv+Y6Sl\naTWbO3WirB+Ekyi7fkHTcKLbNlnE32Mlg0LfOXxZdPP/f/a7X4oFQ8MUSjRH\nXun/\r\n=F9Ur\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHxRTWmGuwN6rAWBphSQ7WUnEDc8rl492S5klA+Ct5ezAiEAvR4/7/35WNAmHpc72d7hWiDp9SUw2wl0wSljMBdU4oM="}]}, "maintainers": [{"name": "ka-weihe", "email": "<EMAIL>"}], "_npmUser": {"name": "ka-weihe", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fastest-levenshtein_1.0.1_1595418641040_0.03117367511379676"}, "_hasShrinkwrap": false}, "1.0.2": {"name": "fastest-le<PERSON><PERSON><PERSON>", "version": "1.0.2", "description": "Fastest Levenshtein distance implementation in JS.", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/ka-weihe/fastest-levenshtein.git"}, "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "distance", "fast", "fastest", "edit", "string", "similarity", "algorithm", "match", "comparison", "fuzzy", "search", "string", "matching", "similar", "node", "difference"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/ka-weihe/fastest-levenshtein/issues"}, "homepage": "https://github.com/ka-weihe/fastest-levenshtein#README", "scripts": {"test": "jest", "test:coverage": "jest --coverage", "test:coveralls": "jest --coverage --coverageReporters=text-lcov | coveralls"}, "devDependencies": {"benchmark": "^2.1.4", "coveralls": "^3.1.0", "eslint": "^7.5.0", "eslint-config-airbnb": "^18.2.0", "eslint-config-airbnb-base": "^14.2.0", "eslint-config-node": "^4.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-jsx-a11y": "^6.3.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^3.1.4", "eslint-plugin-react": "^7.20.3", "eslint-plugin-react-hooks": "^4.0.0", "fast-levenshtein": "^2.0.6", "jest": "^26.1.0", "js-levenshtein": "^1.1.6", "leven": "^3.1.0", "natural": "^2.1.5", "prettier": "^2.0.5", "talisman": "^1.1.2"}, "dependencies": {"levenshtein-edit-distance": "^2.0.5"}, "gitHead": "a38af767bd457180af5583bc5498df02992814f2", "_id": "fastest-<PERSON><PERSON><PERSON><PERSON>@1.0.2", "_nodeVersion": "14.5.0", "_npmVersion": "6.14.6", "dist": {"integrity": "sha512-DAprMRpJ5Tco8PL2spQmzce5ftROltH2uNJrvxTxzIYDvP/9JEVjaYlHPi5Zf5yBHY/j6r5TLJg/O3V0qIcH5Q==", "shasum": "dfd2e19d301e5100b59373a4155f7957e07c9e26", "tarball": "https://registry.npmjs.org/fastest-levenshtein/-/fastest-levenshtein-1.0.2.tgz", "fileCount": 9, "unpackedSize": 13356, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfGCmfCRA9TVsSAnZWagAA1AIQAJxHpa/QtxsbmOnteloY\nTYTszIbAz6BZipjucSfgf3cOPO6ebS5So6emB1p4Wn237oSeNh9GGw57wt2K\nbZBxOHQ22tRVlvSmnVK9gF3V3ZLd1di6I//4CGdhWHQhH0tFBhKIMFnG0zyn\nQn8O6n0uNUQvEPunG8yErQijrCGRao+zi+fXZ+ScvyJ780vQ4Ml2FWZSG46U\nBhGx5oSoIOer4fC8JnE3ZDHVs8c4xyLDF58vjuKz3YNoe9lk4d7C6NSWFhfV\nafNFiesW7FnypsScz7ybKTO4bGj5ERnOvejHw5v5NlpwTWtd+SQuNzaw973H\nsBF+H6BPJffuo2FGa2e5hi5hv085kBLp2DSWIhM04o2aejxF8WmfJNl/7mEx\nXCzO9ngLnvqnSkCagS1ae9aubH53UjG0ux8Pirg2dE2yTMLu8ppV9YEsejzZ\nwIfVopb3nuzy/S4F5nAlR2g6ZI9ALhkJ4zdJ+A7caLOIBKkuQuaWRJp+N/0j\nvuoOyO8T/jE4TdSl3I6rak/ruE2jG9EUrnvtW4Zb6InD6MHXMSBjhsXHU/BQ\n5hyg42uep7EjddeokgV9AC6FatNUh8dTfXs7114Zup5eDtUhqEquQ7XGEa+a\nCB8KrQ4MNLsw5Rz39MiRZvC9Y7BzQtHCvnueuJbEyP3zQOq1A+AxgQGHsf6z\ntFhj\r\n=tf4m\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD176J/4jG7e1DeGdt083X+/uZuSwPPr2ybn3NXftDXbAIgNh5s/BQYEtaAU4Z9eCvMkD3CM0Lhx/H2eM7W8n1ozhw="}]}, "maintainers": [{"name": "ka-weihe", "email": "<EMAIL>"}], "_npmUser": {"name": "ka-weihe", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fastest-levenshtein_1.0.2_1595419039053_0.26236486706310447"}, "_hasShrinkwrap": false}, "1.0.3": {"name": "fastest-le<PERSON><PERSON><PERSON>", "version": "1.0.3", "description": "Fastest Levenshtein distance implementation in JS.", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/ka-weihe/fastest-levenshtein.git"}, "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "distance", "fast", "fastest", "edit", "string", "similarity", "algorithm", "match", "comparison", "fuzzy", "search", "string", "matching", "similar", "node", "difference"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/ka-weihe/fastest-levenshtein/issues"}, "homepage": "https://github.com/ka-weihe/fastest-levenshtein#README", "scripts": {"test": "jest", "test:coverage": "jest --coverage", "test:coveralls": "jest --coverage --coverageReporters=text-lcov | coveralls"}, "devDependencies": {"benchmark": "^2.1.4", "coveralls": "^3.1.0", "eslint": "^7.5.0", "eslint-config-airbnb": "^18.2.0", "eslint-config-airbnb-base": "^14.2.0", "eslint-config-node": "^4.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-jsx-a11y": "^6.3.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^3.1.4", "eslint-plugin-react": "^7.20.3", "eslint-plugin-react-hooks": "^4.0.0", "fast-levenshtein": "^2.0.6", "jest": "^26.1.0", "js-levenshtein": "^1.1.6", "leven": "^3.1.0", "natural": "^2.1.5", "prettier": "^2.0.5", "talisman": "^1.1.2"}, "dependencies": {"levenshtein-edit-distance": "^2.0.5"}, "gitHead": "a38af767bd457180af5583bc5498df02992814f2", "_id": "fastest-<PERSON><PERSON><PERSON><PERSON>@1.0.3", "_nodeVersion": "14.5.0", "_npmVersion": "6.14.6", "dist": {"integrity": "sha512-1WfTDEFhS8ZPFGaRoYYQ5JAafpcFYrG/U4isWr0evD09NT+GOAtjrkk2tufx1PTI2BD27a1MEyp0lSRBcSTXNA==", "shasum": "331b5dfc56bc75cf140d45a764a7fc83a6b28d28", "tarball": "https://registry.npmjs.org/fastest-levenshtein/-/fastest-levenshtein-1.0.3.tgz", "fileCount": 8, "unpackedSize": 10411, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfGCnICRA9TVsSAnZWagAApSUQAINpMkzmU0Ir6cxAKV8g\nVNXy9Qxfq6OpAAQHgwK1Q8scVv0bqi8CImjNNTk4XGXjxLCbo9/s92b+gnlV\nI830BHCAuA7Rx6etEK8kLM04e07hJn1YQztRGGlsF6dHPcsH+mQ8anWLMWzU\nBBrAcR8dWh4b5pJRMh9SdTJPgUTEz8ioukJJQIpxQdRyaia+Cm1Mokbzc2NN\ndfAnQ1KNW6s2EoQYeKERv/yn7NZsinVRh5/XgY0mjAyqrNvO1KCr+4LvVil+\n+qXfkseMOx1GWu4WfZPihitwFQVvtup9N8EET6RnzoTvcZO2RIPmFOuhx3jv\neckK0XwjWWEZ0hgwtiIHFAvRRxhjSWXuPYwiRsjh31WMhnnAvC/R66SQygoj\nXhT3/c5xfCRdyoo3H52NVqAKxphEIl/bFmvwcq39hM6F2wDGxxPZgzHMYgGW\nAVbUl3QZOhJuZ4ZR2yV1RG7cKZs1SpKWzSeqcxmk9EcoUn57PRHvNaIrXu+B\n3EwLXNQx4OE5uP3zpXln/clt07UPfug/Uuq58gKBZOA0xkhcL2WRdZI9QcH7\nL1tKWxrn/17pr0iUcHBiE8re7QsAR+qbBFxd540sDq7WCjXcM2PsETQH031e\nQug7JQYhB88AIslcJE+S1eKTVY/bZld+6Z6KZrlwh48WkHgKfw2Gg7n4cf1d\nkTxA\r\n=po5J\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGQUgOMEIOpKJuzaZAOe0kdhflGwVnWFMpfgjb+XSsxTAiBP6OkEvZdB6E0KFpBl7sc8i+ZcbpzoFmOoTiQR2/kBoA=="}]}, "maintainers": [{"name": "ka-weihe", "email": "<EMAIL>"}], "_npmUser": {"name": "ka-weihe", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fastest-levenshtein_1.0.3_1595419079791_0.4955876172421205"}, "_hasShrinkwrap": false}, "1.0.4": {"name": "fastest-le<PERSON><PERSON><PERSON>", "version": "1.0.4", "description": "Fastest Levenshtein distance implementation in JS.", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/ka-weihe/fastest-levenshtein.git"}, "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "distance", "fast", "fastest", "edit", "string", "similarity", "algorithm", "match", "comparison", "fuzzy", "search", "string", "matching", "similar", "node", "difference"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/ka-weihe/fastest-levenshtein/issues"}, "homepage": "https://github.com/ka-weihe/fastest-levenshtein#README", "scripts": {"test": "jest", "test:coverage": "jest --coverage", "test:coveralls": "jest --coverage --coverageReporters=text-lcov | coveralls"}, "devDependencies": {"benchmark": "^2.1.4", "coveralls": "^3.1.0", "eslint": "^7.5.0", "eslint-config-airbnb": "^18.2.0", "eslint-config-airbnb-base": "^14.2.0", "eslint-config-node": "^4.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-jsx-a11y": "^6.3.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^3.1.4", "eslint-plugin-react": "^7.20.3", "eslint-plugin-react-hooks": "^4.0.0", "fast-levenshtein": "^2.0.6", "jest": "^26.1.0", "js-levenshtein": "^1.1.6", "leven": "^3.1.0", "natural": "^2.1.5", "prettier": "^2.0.5", "talisman": "^1.1.2"}, "dependencies": {"levenshtein-edit-distance": "^2.0.5"}, "gitHead": "1be0816f236bbab9eaf9dafdd4ac77565d14adff", "_id": "fastest-<PERSON><PERSON><PERSON><PERSON>@1.0.4", "_nodeVersion": "14.5.0", "_npmVersion": "6.14.6", "dist": {"integrity": "sha512-ILKpZE1g25w18wiH53MMTcpYOoxeaPbc7vzjhCsUXhB+D86Dn508DM/Bk4x1rE2liub1h1I8G4rI63njzcVkrg==", "shasum": "0aa9ba58c770f9472b7fd8ea1819fd52ad390b88", "tarball": "https://registry.npmjs.org/fastest-levenshtein/-/fastest-levenshtein-1.0.4.tgz", "fileCount": 8, "unpackedSize": 10403, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfGCu0CRA9TVsSAnZWagAAC0gP/iudhNUU0IB1uT3duJoB\nNcddGLodCkAZYCQsukmOhsj9U61CnO0DK21HlSdGoK2nTkWiKcH+AlllYaFq\nt6J3BHgrAgkAZh5EHbt8/91aB7jlVQb1rdaMLx0sYeY4uO92TmByJO5UkQ3H\nIzmF/DArIDfbFm6/znOJX7cq/7q6wAxoh4L+W5hpUagUc1aRIZxUwpvRt22V\nApf1Q1HiplI+TTbNEnKFV2wyjBk6ywM3bJGfqlW29n1A/pONf426Ux1/cXhF\nRBHyrVafVeUl6UMzL+z4E139A7vMG4p4v1Lmu3lCbY9AHdJmwMkzAWW+X4I7\nL4avwX9doSkFeiWFe6UjTAxIqUjngFgwOzEBg3sBq4nFGXqlLJqqLkVAoPS7\nnCtV1fvr2sob5khWci9VLLzLq6ZbOh7sk5BlRU7B0Ti+9gfrk/GDwmXavotF\nlHZqMyKCdBE7mRgOoyhxm+yi3KyTOwdgjyzXRJwdxdAPVrfyd8N5k5NuMkdV\n+4o1q3wvvm0ONdoxB47ylkVh7B5mh4c5goFoc0XfsxQsIpUeYx6T3qZqPzXL\nBr6TaveiCgzDVxh8IyxtomNYljXH3Ic0GpNIamay68r61GMiCOH4ObQ9hKGO\nuYePROYLeM3w/yLkZjsYeSLivZoBfpvycWsZlXMMmRmuXFk/U+WRwAaKm7x/\nqjLq\r\n=EdUh\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDojPIxOD1SYgSQwvOnH49qMiPl7MN3Bw3yyKbooK9GtQIhAOpfXtRGpZWLTA8uo3gdif4fwUsy8xAoe41WIs12P38d"}]}, "maintainers": [{"name": "ka-weihe", "email": "<EMAIL>"}], "_npmUser": {"name": "ka-weihe", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fastest-levenshtein_1.0.4_1595419572367_0.9276024738059763"}, "_hasShrinkwrap": false}, "1.0.5": {"name": "fastest-le<PERSON><PERSON><PERSON>", "version": "1.0.5", "description": "Fastest Levenshtein distance implementation in JS.", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/ka-weihe/fastest-levenshtein.git"}, "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "distance", "fast", "fastest", "edit", "string", "similarity", "algorithm", "match", "comparison", "fuzzy", "search", "string", "matching", "similar", "node", "difference"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/ka-weihe/fastest-levenshtein/issues"}, "homepage": "https://github.com/ka-weihe/fastest-levenshtein#README", "scripts": {"test": "jest", "test:coverage": "jest --coverage", "test:coveralls": "jest --coverage --coverageReporters=text-lcov | coveralls"}, "devDependencies": {"benchmark": "^2.1.4", "coveralls": "^3.1.0", "eslint": "^7.5.0", "eslint-config-airbnb": "^18.2.0", "eslint-config-airbnb-base": "^14.2.0", "eslint-config-node": "^4.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-jsx-a11y": "^6.3.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^3.1.4", "eslint-plugin-react": "^7.20.3", "eslint-plugin-react-hooks": "^4.0.0", "fast-levenshtein": "^2.0.6", "jest": "^26.1.0", "js-levenshtein": "^1.1.6", "leven": "^3.1.0", "natural": "^2.1.5", "prettier": "^2.0.5", "talisman": "^1.1.2"}, "dependencies": {"levenshtein-edit-distance": "^2.0.5"}, "gitHead": "1be0816f236bbab9eaf9dafdd4ac77565d14adff", "_id": "fastest-<PERSON><PERSON><PERSON><PERSON>@1.0.5", "_nodeVersion": "14.5.0", "_npmVersion": "6.14.6", "dist": {"integrity": "sha512-vqVqAjWp4Vhn2rQzhG4SzAvAv2967qn3opdJkYqkSyQ3ojZp+4OnQkdRQWAYdmjt291MAUI2kmfQGdVQHdM0/A==", "shasum": "439feb33158621c9d284b0c40622bfd850bbaf37", "tarball": "https://registry.npmjs.org/fastest-levenshtein/-/fastest-levenshtein-1.0.5.tgz", "fileCount": 8, "unpackedSize": 10403, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfGCvCCRA9TVsSAnZWagAAJuMP/igw1PvLO5WVlWmTXIWG\nLx+1ZXqdd46FaHYVZUHH9tRfA65RBJ/qWLk476q2WLKQYFOx9w4P1edZypN2\naxyM2SFtaKgaw4LtUk0bp979DgovV7cpq67xOV4THpkSg2uGWtYm4PJZVAcJ\nS6x8zlcKTRaHQXEKGXt53E5iFCX4as/y7Uqmt2tOdKk2vs6BoK4AcoihfyeB\nrGHWFssYQeAcj4p9wZdSz+Vv1dHBd8RKWkI7LUpMgqDihQPog/a3/9HlNyYQ\nJJAHX6aUFzAlUOu8Iuanl83VbNz6ShbLaKeqKsdHZEV1G0ZoDg6tLwaRABFv\n7Wf+jwSGZ0YXj0RwhOJTicCcAc7et8fHiMW1KoxKdgDam4TyA+Emq9VMfJVQ\nmrIrVy5DIKTQ6K7wfJA3usCHeRQeBVkhjQ9BtTWbAaiVyWwQlCFC6+TmX5F/\nrggRmOrMWBtAD34yjnVNsz60IGXkr6HHxzI4KZKmpQhNjNYDaXAvBK+7CUDM\nns9lPXJ2jeca/stcefdIMLi8TXJ4IXBpdN3IcHN45irlmAgxjlDTIkAci3hM\n17LFygDbETZg/W0xAIAnCUZ+c77LLxhNXzbRb4XmPgS/9EdM3jVp8bepP4Ze\n/yKVzp9yc38HrJk3KcIyowsuL7zlHEQHuFRILLkI/9NatKcVAsQ3DzLDrpmT\nWiOl\r\n=Khka\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBFFQ7BMDl6nw79hW23r8XMBmqiKkWgD0kljA3pBSYxuAiEAy2t0OQgQMVrsX7QE3SXNBc4X/cQ5Sy853YYLpb+NEmM="}]}, "maintainers": [{"name": "ka-weihe", "email": "<EMAIL>"}], "_npmUser": {"name": "ka-weihe", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fastest-levenshtein_1.0.5_1595419586226_0.9741853930586375"}, "_hasShrinkwrap": false}, "1.0.6": {"name": "fastest-le<PERSON><PERSON><PERSON>", "version": "1.0.6", "description": "Fastest Levenshtein distance implementation in JS.", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/ka-weihe/fastest-levenshtein.git"}, "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "distance", "fast", "fastest", "edit", "string", "similarity", "algorithm", "match", "comparison", "fuzzy", "search", "string", "matching", "similar", "node", "difference"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/ka-weihe/fastest-levenshtein/issues"}, "homepage": "https://github.com/ka-weihe/fastest-levenshtein#README", "scripts": {"test": "jest", "test:coverage": "jest --coverage", "test:coveralls": "jest --coverage --coverageReporters=text-lcov | coveralls"}, "devDependencies": {"benchmark": "^2.1.4", "coveralls": "^3.1.0", "eslint": "^7.5.0", "eslint-config-airbnb": "^18.2.0", "eslint-config-airbnb-base": "^14.2.0", "eslint-config-node": "^4.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-jsx-a11y": "^6.3.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^3.1.4", "eslint-plugin-react": "^7.20.3", "eslint-plugin-react-hooks": "^4.0.0", "fast-levenshtein": "^2.0.6", "jest": "^26.1.0", "js-levenshtein": "^1.1.6", "leven": "^3.1.0", "natural": "^2.1.5", "prettier": "^2.0.5", "talisman": "^1.1.2"}, "dependencies": {"levenshtein-edit-distance": "^2.0.5"}, "gitHead": "1be0816f236bbab9eaf9dafdd4ac77565d14adff", "_id": "fastest-<PERSON><PERSON><PERSON><PERSON>@1.0.6", "_nodeVersion": "14.5.0", "_npmVersion": "6.14.6", "dist": {"integrity": "sha512-d+X/iPpkhOyqR8C8xbw7B53qj0UVIUiERfYXES996QsKDoAYgJIDj6L6V3n9QNvT0iLrYZOE9xO2LKSd7lAk7g==", "shasum": "8fdee08c0913186c4dac4a7393674fee09e74323", "tarball": "https://registry.npmjs.org/fastest-levenshtein/-/fastest-levenshtein-1.0.6.tgz", "fileCount": 8, "unpackedSize": 10403, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfGCvYCRA9TVsSAnZWagAAmHUP/jlg/Yo1zdK4ajyqwYEF\nGUFQF+fiJ3apNfPXu6m4oFhWFUtK0dfqJByt1PaeW3bKgGapJ7SRvnbLzjku\nEDiAgj9a8va1Xy9ehljjrF5P75LfZRc48U3bWwmnhMPFf4cvqRpoQx3IGTBM\nxcOye7PuNfJ32ls1fubXV9VC1p2IiTkrlSkVBsNPV/wJHU7HMU5l7v4srVQU\nyMnxzm0iAtCXYaRGnnZ5Aot3f0zi+//UT8jVFxK5pGKrN9nNZBVrWZwIFfwj\n8mJWMjqnHd/70zqwwjkkQFDWa2yNZ288BThC2Uem64A9ws5lhbDeHkR2c+C3\nH2ZLsYQyDJn1RdMgx5PyNDKGrz0Nwi0jHIs8RqAQvS7hjeDogI6e3UVxoVqH\nj4wdjbFmb0xlA3KEGZetv6urVUB+HolFyAYevuQTyb+3sVWiZTLrokgnMDmy\nRCChwTAQkA2EtV7RGz9f2cUaUXZHC09PDr2A5Qw08hOKt+kHFFtNUKEii51E\nECwxei+tLb9QGfdoqyiK8eVwUXOpHr9Zb7Hrtc4UEE+U8Uf3Fj8KiXo+zf5S\nuZV90CvMhtuZS5J3F4fy8711Ol4O+UB0pT9oFP0h8zOvUd9viaSl0hUM/1ER\nIOj6ZWdvJyFBQErCb+E/J+rxXTBcNuIYdsvZm7pWgMHz2alPO5/OmNf+v0eC\nBvTN\r\n=rDJ4\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDT7wZP6NwJBFa4cmYDeDoWJQzLul2uVD4VXDty/16k6wIgKC5dUMPu+9Nh4hIGeslR4bHfjGA+1075kgMc2aQxuls="}]}, "maintainers": [{"name": "ka-weihe", "email": "<EMAIL>"}], "_npmUser": {"name": "ka-weihe", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fastest-levenshtein_1.0.6_1595419607635_0.382976613216933"}, "_hasShrinkwrap": false}, "1.0.7": {"name": "fastest-le<PERSON><PERSON><PERSON>", "version": "1.0.7", "description": "Fastest Levenshtein distance implementation in JS.", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/ka-weihe/fastest-levenshtein.git"}, "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "distance", "fast", "fastest", "edit", "string", "similarity", "algorithm", "match", "comparison", "fuzzy", "search", "string", "matching", "similar", "node", "difference"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/ka-weihe/fastest-levenshtein/issues"}, "homepage": "https://github.com/ka-weihe/fastest-levenshtein#README", "scripts": {"test": "jest", "test:coverage": "jest --coverage", "test:coveralls": "jest --coverage --coverageReporters=text-lcov | coveralls"}, "devDependencies": {"benchmark": "^2.1.4", "coveralls": "^3.1.0", "eslint": "^7.5.0", "eslint-config-airbnb": "^18.2.0", "eslint-config-airbnb-base": "^14.2.0", "eslint-config-node": "^4.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-jsx-a11y": "^6.3.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^3.1.4", "eslint-plugin-react": "^7.20.3", "eslint-plugin-react-hooks": "^4.0.0", "fast-levenshtein": "^2.0.6", "jest": "^26.1.0", "js-levenshtein": "^1.1.6", "leven": "^3.1.0", "natural": "^2.1.5", "prettier": "^2.0.5", "talisman": "^1.1.2", "levenshtein-edit-distance": "^2.0.5"}, "gitHead": "1be0816f236bbab9eaf9dafdd4ac77565d14adff", "_id": "fastest-<PERSON><PERSON><PERSON><PERSON>@1.0.7", "_nodeVersion": "14.5.0", "_npmVersion": "6.14.6", "dist": {"integrity": "sha512-A0UHMIY8uWcxbAf0kkFJqhwgDdYABMypMAINWYfyFs6LKKKCsjgEah0eu0tcGUfTKP+3SV9uCHI6OqhEPKhJjw==", "shasum": "849704e7798b7012caeef13817c7739da073e612", "tarball": "https://registry.npmjs.org/fastest-levenshtein/-/fastest-levenshtein-1.0.7.tgz", "fileCount": 8, "unpackedSize": 10379, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfGDFmCRA9TVsSAnZWagAAfG4P/0ZX3bxMw1AjOVN3FPos\nV3YnLguT8NId4oDBNb6y3Q495CsfPnRcj61JaM3Z4dm/tNNR4aWJeBz1oW3G\nRdpUilWzdWarBgc52KEtQTruBHSuXKpeZ7zJEVQQlk454LNReB7wvksSitgc\nHADmTiaNpnebhDc208sN/Ai55iR1OEjIg9X7UBc2ZDv7GZiJ3S1U0BPmSONx\nLLBN3gr5L3obrl01LOmQaa+L0D9tCSrK8JeEeXgwOMuL5mPCPeLdQtIOpN8z\nFKJBTRpcqMazLzEjYr0dmp2JkQo5yZqzHrBBJ2oTBwDrnP4NoD0UeGiAmYmN\n+ggMOMzctdReXoDx+Oq6liFhVjM75h/+zY69Y/dJb2SpBTZNxtD4xSmXsY8F\nVx5s+8J9mUE36KrcBPsg2bCHUzzk3niXt1T3RBfM9hkHhwx4Y7ikCN/ch5yK\nMijDMOBM7U7K2hAnAPLZclmwwcO+kOalWuvDh80cOmiAZQxr73pUb8pO3gYL\nfn250+sBjA+wYXkRdiDVY8htcW3qoeXV2jfRaJApWtmnqiAdEm8lv/uqAemk\nwpTNCv182GiVxqrqR3M84teGCjiziiCWEaimwIJYU4u+8q0gx4/PWyIs9Ttj\n8lRa92gV4eTFkMAs2sScIkZ2ZrUCNwNCBlmnKtF/pOl9Jcp4kasdEF5itj2H\nIM3P\r\n=D78K\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAd23NgJ4R2cmX7Df3fW2VOlVw87gmntVXL8yTUcLksoAiBYIb+Ybb0h8guhEn49libD72joTjJ2saLEs9zaJuqbDg=="}]}, "maintainers": [{"name": "ka-weihe", "email": "<EMAIL>"}], "_npmUser": {"name": "ka-weihe", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fastest-levenshtein_1.0.7_1595421030391_0.7686889118743314"}, "_hasShrinkwrap": false}, "1.0.8": {"name": "fastest-le<PERSON><PERSON><PERSON>", "version": "1.0.8", "description": "Fastest Levenshtein distance implementation in JS.", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/ka-weihe/fastest-levenshtein.git"}, "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "distance", "fast", "fastest", "edit", "string", "similarity", "algorithm", "match", "comparison", "fuzzy", "search", "string", "matching", "similar", "node", "difference"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/ka-weihe/fastest-levenshtein/issues"}, "homepage": "https://github.com/ka-weihe/fastest-levenshtein#README", "scripts": {"test": "jest", "test:coverage": "jest --coverage", "test:coveralls": "jest --coverage --coverageReporters=text-lcov | coveralls"}, "devDependencies": {"benchmark": "^2.1.4", "coveralls": "^3.1.0", "eslint": "^7.5.0", "eslint-config-airbnb": "^18.2.0", "eslint-config-airbnb-base": "^14.2.0", "eslint-config-node": "^4.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-jsx-a11y": "^6.3.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^3.1.4", "eslint-plugin-react": "^7.20.3", "eslint-plugin-react-hooks": "^4.0.0", "fast-levenshtein": "^2.0.6", "jest": "^26.1.0", "js-levenshtein": "^1.1.6", "leven": "^3.1.0", "natural": "^2.1.5", "prettier": "^2.0.5", "talisman": "^1.1.2", "levenshtein-edit-distance": "^2.0.5"}, "gitHead": "95e22024015a2b3033190a5d35fe1669b1e5be2e", "_id": "fastest-<PERSON><PERSON><PERSON><PERSON>@1.0.8", "_nodeVersion": "14.5.0", "_npmVersion": "6.14.6", "dist": {"integrity": "sha512-SRj7muXzcgIvuq8wYyANmMRwuCWhE6e+HIKlLAUNICRaxNRc0ZAhpayNVKefoscbP5mGBorLy0RTdrkH7l5IIA==", "shasum": "96684719abfc533c2d0b47bbdad0eb60ec0edbe4", "tarball": "https://registry.npmjs.org/fastest-levenshtein/-/fastest-levenshtein-1.0.8.tgz", "fileCount": 8, "unpackedSize": 10410, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfGFSwCRA9TVsSAnZWagAAiPwP/AoF5QxrXyMZjPJRQfen\nGuIGlkNr59pY3Sx/7Xqe4TX+W25BQ6U2u3+KelM5Zk/3AhHm9x6emw96Pg5v\nad1Qis8bxF2VCxSV+281DmpS9Mw+q9CxhdMh/SOF47V5O7i0Kt2KH39PDZ5K\n/UDdN7eYgS9Qd7fuXclaHNJ+MnNSnZqGSWmbjI9xGE3Suv90H1AKVh+WCRch\nMkmBYU7cmsW0OvfV2ibhX5rFEazOX28Slq4CgY6mn7nLmaXDDarKa/lSOTnP\njh64Pk901pEvWoKJzCckd3X9yetL2BBPJ6vroSlxLB0f+Jc1n9iPCtkNxd3R\nv1wUYaBmrWpTXavqCoj9MUJtqn6bkupTQ7hfOrDYPr10jefvE1F+bEAEgMWT\n6y+s/mBK50UUhtAv/tZlHINeiYZCg7GmgW+cqIjOhe3epZG3o1+EeUeXcy8z\nUzVaxGneoZL6AwtZzo6Pw/RQ5UdQbCFflBCl7gybQ7h9Tbcl4g3HnrcfKzej\nyJQuWbRmTWVrH9b/ORtu+ixRho82BpYUInR7UKURnZPD9ITbQR5bpNiCInSD\n2fUsdnAxgqrNfM7lIt/DXpbE9UY+DSVGTNVmeyzA7QHIix2EpxP9lWCQkJco\nz2dosk9ztQRIhnhuTwkuJLiUUxlFYLZ0Z526ZGlWuRkx2N9VOZLntJms8ACH\nPs5b\r\n=Xo3U\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICcdUtDr3xTiO+e+DM6gH4BdlYoctd75MMYCiRP9xkxYAiEA+vA5cHuAEy5IWjVELk+aWN8h/IIq1DYixPRzdsZG/XU="}]}, "maintainers": [{"name": "ka-weihe", "email": "<EMAIL>"}], "_npmUser": {"name": "ka-weihe", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fastest-levenshtein_1.0.8_1595430063688_0.06678373643534208"}, "_hasShrinkwrap": false}, "1.0.9": {"name": "fastest-le<PERSON><PERSON><PERSON>", "version": "1.0.9", "description": "Fastest Levenshtein distance implementation in JS.", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/ka-weihe/fastest-levenshtein.git"}, "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "distance", "fast", "fastest", "edit", "string", "similarity", "algorithm", "match", "comparison", "fuzzy", "search", "string", "matching", "similar", "node", "difference"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/ka-weihe/fastest-levenshtein/issues"}, "homepage": "https://github.com/ka-weihe/fastest-levenshtein#README", "scripts": {"test": "jest", "test:coverage": "jest --coverage", "test:coveralls": "jest --coverage --coverageReporters=text-lcov | coveralls"}, "devDependencies": {"benchmark": "^2.1.4", "coveralls": "^3.1.0", "eslint": "^7.5.0", "eslint-config-airbnb": "^18.2.0", "eslint-config-airbnb-base": "^14.2.0", "eslint-config-node": "^4.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-jsx-a11y": "^6.3.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^3.1.4", "eslint-plugin-react": "^7.20.3", "eslint-plugin-react-hooks": "^4.0.0", "fast-levenshtein": "^2.0.6", "jest": "^26.1.0", "js-levenshtein": "^1.1.6", "leven": "^3.1.0", "natural": "^2.1.5", "prettier": "^2.0.5", "talisman": "^1.1.2", "levenshtein-edit-distance": "^2.0.5"}, "gitHead": "c0fa81ce12983b1f43c529478489f404c7bee087", "_id": "fastest-<PERSON><PERSON><PERSON><PERSON>@1.0.9", "_nodeVersion": "14.5.0", "_npmVersion": "6.14.6", "dist": {"integrity": "sha512-8DIQrqkmGTMfjcwiebj28Y1l6eCoLCU+TppdZa5zp5ZyNO12oUhtEEeCZjojZZarD8+peRg3CxL4Zm5BrKr9Fw==", "shasum": "8ff918fc56736a95b207f90989f4f36705e1bcb7", "tarball": "https://registry.npmjs.org/fastest-levenshtein/-/fastest-levenshtein-1.0.9.tgz", "fileCount": 10, "unpackedSize": 10757, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfGJ8KCRA9TVsSAnZWagAAEUkP/0XOyfc5fzzOlcvzW01S\ntuXOmbdTRYWRpBiJaf2Q11I4FJi70o+IA7+/humDHKdoE7bOBDWEzHpm+mnT\nKRIIWBJRwmI+mha9XYc5JhyTwniLON6pzrq7GuZc52rGNodt1Oe5Wb9TKP7d\n7UKipCYOVIyDIXUSsOnP4U7VgXBdWxKao+YXcdKPS6bMVcrPT7SqxIu1eygZ\nuyaJv2jX09zZteUrNfbftnDBPly6VWvL9DY2nfuNDjYHBymk7LhtZal7dkJt\nHjIQXGr0quTQeeQwU8zLlZYPpGho08mUF+vg5rqLiAf9lalAL0ZFXKY5E5OL\nEOdAg3Klm5HsXS1lKMThix//yL30EMjmmGjVPC8/3regJc4eYUNvFKtwTNov\nIjKLbp/QIQywAZ5oFZAMqqKXnfCCTvpWnyGcwl/Yu+4xKPrhgbicntP0yu0a\nDa9s/IjVGkDCz04Uwa1qdWKM7cPh4f9Y7lqvj5srQYGsqAl9XnfqoIo7puHT\neUs7utlfJShOPEyhQrOPuYajCqmQwbELhOoyIFIPc7VRkcylTLG4+x2ZdaNU\n/IYauvwhxRloR24Z5wjzHoeHo54pk8QMuvYScYEFWi8aupS+eRovXwYbz5Md\n/brdAvzRB62zK9ruwS7Pcoi8dKkBt9TBsKv6/qRmATmWMA4ltgyk/wimR18n\nzU1C\r\n=cDZy\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDVbvzerBMCLXqpvCiJO/SsyT2AIoSoJBcqkEZ9YKwVBAiASnlHINDgrPSbmUK78MaGchKEGME5+jk2QmtqI1kt3sQ=="}]}, "maintainers": [{"name": "ka-weihe", "email": "<EMAIL>"}], "_npmUser": {"name": "ka-weihe", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fastest-levenshtein_1.0.9_1595449097451_0.7625643389583809"}, "_hasShrinkwrap": false}, "1.0.10": {"name": "fastest-le<PERSON><PERSON><PERSON>", "version": "1.0.10", "description": "Fastest Levenshtein distance implementation in JS.", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/ka-weihe/fastest-levenshtein.git"}, "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "distance", "fast", "fastest", "edit", "string", "similarity", "algorithm", "match", "comparison", "fuzzy", "search", "string", "matching", "similar", "node", "difference"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/ka-weihe/fastest-levenshtein/issues"}, "homepage": "https://github.com/ka-weihe/fastest-levenshtein#README", "scripts": {"test": "jest", "test:coverage": "jest --coverage", "test:coveralls": "jest --coverage --coverageReporters=text-lcov | coveralls"}, "devDependencies": {"benchmark": "^2.1.4", "coveralls": "^3.1.0", "eslint": "^7.5.0", "eslint-config-airbnb": "^18.2.0", "eslint-config-airbnb-base": "^14.2.0", "eslint-config-node": "^4.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-jsx-a11y": "^6.3.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^3.1.4", "eslint-plugin-react": "^7.20.3", "eslint-plugin-react-hooks": "^4.0.0", "fast-levenshtein": "^2.0.6", "jest": "^26.1.0", "js-levenshtein": "^1.1.6", "leven": "^3.1.0", "natural": "^2.1.5", "prettier": "^2.0.5", "talisman": "^1.1.2", "levenshtein-edit-distance": "^2.0.5"}, "gitHead": "fc4b4f0d07ae96c6e69c1a74067068da20cb0446", "_id": "fastest-<PERSON><PERSON><PERSON><PERSON>@1.0.10", "_nodeVersion": "13.13.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-47cjEZM1VsM21NQtVunzK/c4ZezeS0FIYRNZ/ovHY234NQ2BNvLMmAIjYfb+lVZAKI1h3GZwYAl1Pu6isaIJIQ==", "shasum": "c2e0f0914cedd1af2f180a5c281d0d2791f8585a", "tarball": "https://registry.npmjs.org/fastest-levenshtein/-/fastest-levenshtein-1.0.10.tgz", "fileCount": 10, "unpackedSize": 14561, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfHHOlCRA9TVsSAnZWagAAzysP/0+G9v8pdAwDE1z6rwuP\nVGeEcVaioJ46WIvSGsSQaHi7b9mQR937v7sHGl+b9OpE+dL5JIyJ0xjrdPJb\ntX/+1ervq14lvzHCkIayqvfLaim94elr+mBD38bH7p8mCNnbl9/72pz7O1Im\nrrwxuwj9X5WptNal7nxn8rhddAmP+/oKVfBY3/xr7RXQHHUeh5iCYMTEy9c8\n7wQo6I5B4lQGOJo/IyavPg6RBYj4hqhHkGo1PPZn5SAzQS4r6/YCxa7jtDlS\n36UgMkcxI5Bxmi7VsddMRMJQH1Nzb7VRWIP4tMUiQLXlOkMPSZUGEDZoGmx0\n0Ilh1q/AONDmQ9i2pSl3V8l3IIfX2KulAd/eWDkiZ+9w/u8y5wwR9LO1R5F1\nSJx9O/zmJAtwscIATe4jYou2Zt0niEw/aLnWAzfsDdMXu7XO7qKubX+ilqe7\ngbhjMcn4stTZBKWxbUtwKftdXAKRdBjasXVByF9+1gHXytHfVQLzq8+fum6p\n8M5vSwrXIC3yhsOoqzJMYMxWbbI0YL3zD09vIee1AnBRcBvXnTqo9r3QzSeq\nD69yDo5qgBQpp2Wu9GNpSyaI42CNEWU9uj1NMmQ0d/Vrop4NfftYSlmOHS74\nmoCwIVMPFLKMP29O+WJB3IpM2hbDFzLGWQ58szXVd5sn6dXYMwlIlQ22dmaC\nk2m7\r\n=3pyX\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGGiShuTm2DcSrZyCq4ZeGI5DZ/49wY1F8P9kdDbezq2AiBXXBKBNqFsz/FSHhqUhRZLjs+IuQtaIxGqkq2jHmFYhg=="}]}, "maintainers": [{"name": "ka-weihe", "email": "<EMAIL>"}], "_npmUser": {"name": "ka-weihe", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fastest-levenshtein_1.0.10_1595700132927_0.643306198526524"}, "_hasShrinkwrap": false}, "1.0.11": {"name": "fastest-le<PERSON><PERSON><PERSON>", "version": "1.0.11", "description": "Fastest Levenshtein distance implementation in JS.", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/ka-weihe/fastest-levenshtein.git"}, "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "distance", "fast", "fastest", "edit", "string", "similarity", "algorithm", "match", "comparison", "fuzzy", "search", "string", "matching", "similar", "node", "difference"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/ka-weihe/fastest-levenshtein/issues"}, "homepage": "https://github.com/ka-weihe/fastest-levenshtein#README", "scripts": {"test": "jest", "test:coverage": "jest --coverage", "test:coveralls": "jest --coverage --coverageReporters=text-lcov | coveralls"}, "devDependencies": {"benchmark": "^2.1.4", "coveralls": "^3.1.0", "eslint": "^7.5.0", "eslint-config-airbnb": "^18.2.0", "eslint-config-airbnb-base": "^14.2.0", "eslint-config-node": "^4.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-jsx-a11y": "^6.3.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^3.1.4", "eslint-plugin-react": "^7.20.3", "eslint-plugin-react-hooks": "^4.0.0", "fast-levenshtein": "^2.0.6", "jest": "^26.1.0", "js-levenshtein": "^1.1.6", "leven": "^3.1.0", "natural": "^2.1.5", "prettier": "^2.0.5", "talisman": "^1.1.2", "levenshtein-edit-distance": "^2.0.5"}, "gitHead": "606c132c58039c22989fa0d2d91d4e2d8bbb2404", "_id": "fastest-<PERSON><PERSON><PERSON><PERSON>@1.0.11", "_nodeVersion": "14.5.0", "_npmVersion": "6.14.7", "dist": {"integrity": "sha512-L2nJ<PERSON>+w/ShMIELX6W6hAXe8XJuNJNANKuznyeA7qRa4fSbrVNYBR31pzlEeXkru8SOCGgwcOfBJmS9gBDxqNMA==", "shasum": "377d05d763ceca89303325d5b08c140fffddade2", "tarball": "https://registry.npmjs.org/fastest-levenshtein/-/fastest-levenshtein-1.0.11.tgz", "fileCount": 11, "unpackedSize": 15916, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfLeGcCRA9TVsSAnZWagAA3R0P/RD0rtwA6ETaFIvaN+fw\nARFHSWPpW5KtRmmvcdHFr9SN57Rykfob2hd1rvw/v1xypuCggJ3Nch9SGk4M\nSf05D7drVzlzVcPZrifdHz2tZ6jY53sq4WIKaU1iURQnIiErSephXLZBQc1b\nCih+U2/yBPwi9qzsfRp+m4L61qEnxLaamwfgvks7dc0M939ZMBQDqL7cTrfe\nYt6waIRoJ1bWJWlkIRxrUmFg/SMcarvi+kRV7Bejo8aLXW5jpDFj7Rw5vJrH\nejoXTuOZ9cZ9Sme8ccKsjIfBHtBAWAZ4d2vHJOBHhTe7A7hs5z5QeL+Oj0Qv\nXSG7h1+39M90wu/Vn5s/GuGYADsh2PtRQOyPDJMnIhRqY8rd7cviihrCWTNS\nsXYZ1YdyULenrx2LzEZaU/IKJqrB3ZVyn+ICvhp0o3kvVcHIRb8nhnz4Dbac\ng9O6n/pydXJPtpLYC8MurbZMFraeQSu/x7gVNo2kjKjlT8EOyv5VMqELJVvQ\n9uXC5FBRvkv7zSTcRAVXkXLUSPztOZNzP8pw1tZUFzHWNOo7JqWYUVPAuRek\nOmwL/BN2GEmLNxuTubN/uUhtPxcckfrWWXfV57N/DUOtlQ36zUf3tMY30wxq\ngiBIJqJnnuU9waLh4CIIcCM1BjRY7PYZeQNIT6mcGLJtBxNCAL9LXI4sy/AK\nFcma\r\n=ylph\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBjzX0YjL3ns6ZbpTirra5L+/a0BcMyNbKiEZpOc1lYYAiBMVQ/l+yiy2TKQ32oHbkJX79xwSbOBIbhh0yjYfpbQ6w=="}]}, "maintainers": [{"name": "ka-weihe", "email": "<EMAIL>"}], "_npmUser": {"name": "ka-weihe", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fastest-levenshtein_1.0.11_1596842396181_0.2583491223387686"}, "_hasShrinkwrap": false}, "1.0.12": {"name": "fastest-le<PERSON><PERSON><PERSON>", "version": "1.0.12", "description": "Fastest Levenshtein distance implementation in JS.", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/ka-weihe/fastest-levenshtein.git"}, "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "distance", "fast", "fastest", "edit", "string", "similarity", "algorithm", "match", "comparison", "fuzzy", "search", "string", "matching", "similar", "node", "difference"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/ka-weihe/fastest-levenshtein/issues"}, "homepage": "https://github.com/ka-weihe/fastest-levenshtein#README", "scripts": {"test": "jest", "test:coverage": "jest --coverage", "test:coveralls": "jest --coverage --coverageReporters=text-lcov | coveralls"}, "devDependencies": {"benchmark": "^2.1.4", "coveralls": "^3.1.0", "eslint": "^7.5.0", "eslint-config-airbnb": "^18.2.0", "eslint-config-airbnb-base": "^14.2.0", "eslint-config-node": "^4.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-jsx-a11y": "^6.3.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^3.1.4", "eslint-plugin-react": "^7.20.3", "eslint-plugin-react-hooks": "^4.0.0", "fast-levenshtein": "^2.0.6", "jest": "^26.1.0", "js-levenshtein": "^1.1.6", "leven": "^3.1.0", "natural": "^2.1.5", "prettier": "^2.0.5", "talisman": "^1.1.2", "levenshtein-edit-distance": "^2.0.5"}, "gitHead": "606c132c58039c22989fa0d2d91d4e2d8bbb2404", "_id": "fastest-<PERSON><PERSON><PERSON><PERSON>@1.0.12", "_nodeVersion": "14.5.0", "_npmVersion": "6.14.7", "dist": {"integrity": "sha512-On2N+BpYJ15xIC974QNVuYGMOlEVt4s0EOI3wwMqOmK1fdDY+FN/zltPV8vosq4ad4c/gJ1KHScUn/6AWIgiow==", "shasum": "9990f7d3a88cc5a9ffd1f1745745251700d497e2", "tarball": "https://registry.npmjs.org/fastest-levenshtein/-/fastest-levenshtein-1.0.12.tgz", "fileCount": 9, "unpackedSize": 11002, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfLeIfCRA9TVsSAnZWagAAS7EP/Rg9t46qar4K6UTCbpdW\ntmLs27BC/8asIkHvS9a0A6ML185F/7EbPIO87JaIMXiu4SkBhRXmTLlIateT\nTaIcJhVlpiCKLE5Cbe4U4PUypIAoBUsxhoCU69jh8Hsx6cgjMoG6F8DO5s4+\ni6cPPnYCVWhxHlCDI6Zfl4jO/ZxiQx4bB+tNWGhx8l8sI1kYuJZY2IUUgu5A\nNIe7Md9nXLjUYM5Vt8jyJmfQBAw3efG6yKElY0xlKq6B2D7Fc1eqFKPp2y+c\nYNweUTFCC1+sVY8z5VizLoxsVFHTyc9gpE+niLLcQ+3aY8HNFdipkVG4/vTp\nHXZMapd/IN+97f/vTCJpb6Lst0wj5F2EBYCv4TWC0eSH/HXUNQ3LKgg7K0xN\ntZ2OYQJcmRRuzHxM9X2qUHaIfwluUwobajPp+dZWppf7HnqUxIuTE4Fhj0nV\n4ACdZ3JmqpGpFuTFIEP6nUZWSuQRWkMu5uuoGWe9BAuzo9ePHcvCEcMJl0On\n1r1/RhFqRFK/laXXlgmQ1OL0QIZNczwa8NkBwNSsNtSg0hW6rz9yhQAtGQHt\nvPtKUUeBmYwC/8/b2TEsS6SqdwP2vpn8f3JkuwTT+wRLlVKlqrMJbIGbNnTa\nz8b7UzraHfMGeOEG/ESpIJDg9tAEQ7iXlgqTuyAaioWO71psviZenfeolSfI\nm1+z\r\n=2crz\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCc/n3KCQeYy59aLGvrXteKQ9/LhxmEoHPXqiHGs2d/7QIhAJJf2uxxE959TP+8lkymEDgOmhmBiICmod3sn5AmkrLw"}]}, "maintainers": [{"name": "ka-weihe", "email": "<EMAIL>"}], "_npmUser": {"name": "ka-weihe", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fastest-levenshtein_1.0.12_1596842527222_0.4139455019824829"}, "_hasShrinkwrap": false}, "1.0.13": {"name": "fastest-le<PERSON><PERSON><PERSON>", "version": "1.0.13", "description": "Fastest Levenshtein distance implementation in JS.", "main": "mod.js", "types": "mod.d.ts", "repository": {"type": "git", "url": "git+https://github.com/ka-weihe/fastest-levenshtein.git"}, "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "distance", "fast", "fastest", "edit", "string", "similarity", "algorithm", "match", "comparison", "fuzzy", "search", "string", "matching", "similar", "node", "difference"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/ka-weihe/fastest-levenshtein/issues"}, "homepage": "https://github.com/ka-weihe/fastest-levenshtein#README", "scripts": {"build": "tsc mod.ts --declaration", "prepare": "npm run build", "bench": "npm run build && tsc bench.ts && node bench.js", "test": "npm run build && tsc test.ts && jest test.js", "test:coverage": "npm run build && jest --coverage", "test:coveralls": "npm run build && jest --coverage --coverageReporters=text-lcov | coveralls"}, "devDependencies": {"@types/benchmark": "^1.0.33", "@types/jest": "^26.0.15", "@typescript-eslint/eslint-plugin": "^4.7.0", "@typescript-eslint/parser": "^4.7.0", "benchmark": "^2.1.4", "coveralls": "^3.1.0", "eslint": "^7.13.0", "eslint-config-node": "^4.1.0", "eslint-config-prettier": "^6.15.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^3.1.4", "fast-levenshtein": "^2.0.6", "jest": "^26.6.3", "js-levenshtein": "^1.1.6", "leven": "^3.1.0", "levenshtein-edit-distance": "^2.0.5", "natural": "^2.1.5", "prettier": "^2.1.2", "talisman": "^1.1.3", "typescript": "^4.0.5"}, "engines": {"node": ">= 4.9.1"}, "gitHead": "45d58d245e0d75138bb7da00dd1188ef8d6fdb84", "_id": "fastest-<PERSON><PERSON><PERSON><PERSON>@1.0.13", "_nodeVersion": "18.5.0", "_npmVersion": "8.13.2", "dist": {"integrity": "sha512-ojjkFNxaL1Zg07zV4tp4IjqJZ/Qp66F7SFNqCDNMlQacJWJgPdEYKecCkWZrzoB3bGWX5ZU2V7Gxc8sDuvTOTQ==", "shasum": "4adeb1ec5b70c1aebfc8448821863b1ef3148130", "tarball": "https://registry.npmjs.org/fastest-levenshtein/-/fastest-levenshtein-1.0.13.tgz", "fileCount": 11, "unpackedSize": 17054, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFBT2S8DnbVaFtfQF7E3OWuVU1HcI4l1jmjjFu5qPTcdAiEA9VSA9dEzgy1Myi9RlV3uniM6jvuQBqUbVGpds89ToX4="}], "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1r4fACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo7ohAAkhe/OEDihZtg7+X5t9KyYsT+B7mQrMSS3TJjuk04XDm8tDGP\r\nc9RNIlNLvA2tpNUq/xg2loB52fNhJfflbEYJusuxHDUdgSCSNBrhu2plQqdX\r\nDWJAfQ1apdq52VvP1UZtr69jKpLLSyhOpzQfSIRelMZWnONLM09BwhMf9vLi\r\nJQysgB+OVIuTIm/dY0Ccw25SGKs1U/j0Z2ayiViV8hEMbONZDn3fA9lZGKig\r\n6zazjEC5wILWw/PvgJTdF/vlFr/TnSvJxLfKKO8/4UakxBmarfpuW9vxg3JC\r\n+rHTqQPqBtJwZIiKQPp/3kemAQkuBdQLLVE0rNxRfzPAhm0Nhf/Y4sbcqVR3\r\nZ7gg3L/sHnrF0GVC/fT9fzP2kcQsFLqFXdv9bRvBaHuKcCYI54nyRXVWl/EV\r\ngSTo/5W9tF+aQQfNnw0E50okm7ALlNehRnL83dDjHLD2wQn9dBcrHfi7Xl4m\r\nSEQlhKTMxXXyf/voLsrJA8jcnkWpLmLb68ljCarchkrAOCkMKpAJj3uCWfk6\r\nETY5tMwfEY7uoUuLR44FAXlzge0aMGHOz8VmpKen5Ym5sfSf/yS4cyicF5co\r\nseoNe/GOouNsYWxs6KEUet87IvclVAkVaCan4jV9ZyqgA2JrmAbP4iwGtRoR\r\nQ2gc98fHDs+JNnta3V0ZUdRpB8b8SSUJN0E=\r\n=9hX2\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "ka-weihe", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ka-weihe", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fastest-levenshtein_1.0.13_1658240543763_0.28259775342773796"}, "_hasShrinkwrap": false}, "1.0.14": {"name": "fastest-le<PERSON><PERSON><PERSON>", "version": "1.0.14", "description": "Fastest Levenshtein distance implementation in JS.", "main": "mod.js", "types": "mod.d.ts", "module": "./esm/mod.esm.js", "repository": {"type": "git", "url": "git+https://github.com/ka-weihe/fastest-levenshtein.git"}, "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "distance", "fast", "fastest", "edit", "string", "similarity", "algorithm", "match", "comparison", "fuzzy", "search", "string", "matching", "similar", "node", "difference"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/ka-weihe/fastest-levenshtein/issues"}, "homepage": "https://github.com/ka-weihe/fastest-levenshtein#README", "scripts": {"build": "tsc mod.ts --declaration", "prepare": "npm run build", "bench": "npm run build && tsc bench.ts && node bench.js", "test": "npm run build && tsc test.ts && jest test.js", "test:coverage": "npm run build && jest --coverage", "test:coveralls": "npm run build && jest --coverage --coverageReporters=text-lcov | coveralls"}, "devDependencies": {"@types/benchmark": "^1.0.33", "@types/jest": "^26.0.15", "@typescript-eslint/eslint-plugin": "^4.7.0", "@typescript-eslint/parser": "^4.7.0", "benchmark": "^2.1.4", "coveralls": "^3.1.0", "eslint": "^7.13.0", "eslint-config-node": "^4.1.0", "eslint-config-prettier": "^6.15.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^3.1.4", "fast-levenshtein": "^2.0.6", "jest": "^26.6.3", "js-levenshtein": "^1.1.6", "leven": "^3.1.0", "levenshtein-edit-distance": "^2.0.5", "natural": "^2.1.5", "prettier": "^2.1.2", "talisman": "^1.1.3", "typescript": "^4.0.5"}, "engines": {"node": ">= 4.9.1"}, "gitHead": "37bd0917de8347c73d67467bd1c5ea803cba5f94", "_id": "fastest-<PERSON><PERSON><PERSON><PERSON>@1.0.14", "_nodeVersion": "18.5.0", "_npmVersion": "8.13.2", "dist": {"integrity": "sha512-tFfWHjnuUfKE186Tfgr+jtaFc0mZTApEgKDOeyN+FwOqRkO/zK/3h1AiRd8u8CY53owL3CUmGr/oI9p/RdyLTA==", "shasum": "9054384e4b7a78c88d01a4432dc18871af0ac859", "tarball": "https://registry.npmjs.org/fastest-levenshtein/-/fastest-levenshtein-1.0.14.tgz", "fileCount": 12, "unpackedSize": 17386, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCRQS8p3P+lzgSlyA0hsnHkOa3uJLbUgdUvITqSuFQS7QIhAM24ca9ZdJSoxGcna5olmFw6Sol5yhCBKF8VPzz4o2mU"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1r5jACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpmyQ//Qb/a35x+ppxSpgF3lGD4P8LjNkoSsVUhuBerPBg3WeG2zXFo\r\niwp8exxGNeyMMZabHiI3Md/7bD2t4PwgkesZxOLk8lV1cvlKWns0TRQYcVKF\r\nPTnUmvTx+X+2T57Mp2E9EI3RkQsEyonnMbfCf6HOEYL5CA1IXAFwBdrE5XYR\r\nK8n3kgq4yVdbU/tEQ6K2sPsgU3tImCkcI5Ng1n3g9AACEXZuCYw3I97tWOpE\r\n1zr5KhHLxftZRiHi9frhLxkQsajlLFHjD5Z3+x/Xh60bSdkNWtMMEDrkfAhB\r\nva+FjoiCUTDuq7qW3fU2RSjWlDgTPeWXOmBUKkvsRHAugX/LpZ1LE6X9nYHR\r\nMxyz4fEgS75Qrv+m2HaDq5mfxmwjEakfujwrwbUNEsshiZwgW1+E8RMewsVw\r\nRfVQtj+lJWpE9GcdvU/msW0pW5G1qWBYTtsxNafa6BU7NtczL1jPokvkb8qO\r\n825I1xtiWMyMKIwu5WcYDr9OgrY3f6xEj7bUd7ACVK2NV3e+Nr8Q0K6uw8MW\r\nEr+dHhLZtLjXAKoHL4vR34FEublbO2OWXwUP6H4OG/Ihn9htTauj8UXqkndJ\r\n2kUAzPUJtSf2dfUVHgln+FSZRL2BVv5gjSzc/mh++C4ptQRxWX4y8QGdzFdj\r\n7Nql3Ak9F8rcNFauxun66vS7QGzbKm0GDUE=\r\n=OBwx\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "ka-weihe", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ka-weihe", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fastest-levenshtein_1.0.14_1658240611657_0.7221766227228412"}, "_hasShrinkwrap": false}, "1.0.15": {"name": "fastest-le<PERSON><PERSON><PERSON>", "version": "1.0.15", "description": "Fastest Levenshtein distance implementation in JS.", "main": "mod.js", "types": "mod.d.ts", "module": "./esm/mod.esm.js", "repository": {"type": "git", "url": "git+https://github.com/ka-weihe/fastest-levenshtein.git"}, "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "distance", "fast", "fastest", "edit", "string", "similarity", "algorithm", "match", "comparison", "fuzzy", "search", "string", "matching", "similar", "node", "difference"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/ka-weihe/fastest-levenshtein/issues"}, "homepage": "https://github.com/ka-weihe/fastest-levenshtein#README", "scripts": {"build": "tsc mod.ts --declaration", "build:esm": "tsc --declaration -p tsconfig.esm.json", "prepare": "npm run build && npm run build:esm", "bench": "npm run build && tsc bench.ts && node bench.js", "test": "npm run build && tsc test.ts && jest test.js", "test:coverage": "npm run build && jest --coverage", "test:coveralls": "npm run build && jest --coverage --coverageReporters=text-lcov | coveralls"}, "devDependencies": {"@types/benchmark": "^1.0.33", "@types/jest": "^26.0.15", "@typescript-eslint/eslint-plugin": "^4.7.0", "@typescript-eslint/parser": "^4.7.0", "benchmark": "^2.1.4", "coveralls": "^3.1.0", "eslint": "^7.13.0", "eslint-config-node": "^4.1.0", "eslint-config-prettier": "^6.15.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^3.1.4", "fast-levenshtein": "^2.0.6", "jest": "^26.6.3", "js-levenshtein": "^1.1.6", "leven": "^3.1.0", "levenshtein-edit-distance": "^2.0.5", "natural": "^2.1.5", "prettier": "^2.1.2", "talisman": "^1.1.3", "typescript": "^4.0.5"}, "engines": {"node": ">= 4.9.1"}, "gitHead": "9752c858e5ffa456cc2e6b814c69f8e0f08bda97", "_id": "fastest-<PERSON><PERSON><PERSON><PERSON>@1.0.15", "_nodeVersion": "18.6.0", "_npmVersion": "8.14.0", "dist": {"integrity": "sha512-aYZINfCOOFyc7ESp43on+C4h5nptX4NtS+PcZ7laFSmzINN00MJ9MfV7WUAuKC8OPNpSwz5CbJa10NaxUwmBzA==", "shasum": "f231491d8705b114d3fa254abecec0d93fc245cd", "tarball": "https://registry.npmjs.org/fastest-levenshtein/-/fastest-levenshtein-1.0.15.tgz", "fileCount": 14, "unpackedSize": 21285, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCfAhIeXEHj08jbNxVI9v25239gXRc39w2PmwySKEOdPwIhAJX+zEiEiafiTSd1RHcWoXwtpClaV0LWb9YYpBpPX5nH"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi6T0qACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrXdw//YkZPyKRfciJ2XUvkn49yXuNqsHcOQQCMEDlCFVpBdpDnAEop\r\nOmdlJIKzCRCIcy2Vbwr9vDFbxaIaYgoD64cVfLrsafZOHbfQ93H1pQ+HqMSN\r\ns+n6OXhf+KOF29uZkAdYV4skmaSzPEKYDf3fYLLYVyc9tuNpl+FWOOngL68K\r\n7gONyC1sWSeK4b3N7NJW3Luq2Vzlb35HbnjGaDkAKH7kMVqC0008Jpantgxs\r\ntAUqzsH4jCGz1Qh4I54kUxqlOR7/G6VbGhgb9v81TJVqXOH8HYQ278MW8Kch\r\nGb13k8XmFPY4beMKgKhkwlYZMIzmAIBboChCheMjh6lgAGcG2gDqcg+bksuR\r\nc00dFwmNQHa8PhqbqpkStc+43bzARBarLGlSaJNR0A01izvQojKgLKTDD9UJ\r\nird+YkPg+JJZYnpwA/zygAZC2hMRn/CkJCBgeAvrSXBufIAU/UqJ4bE7Z5yx\r\niRfoSjWyxp1hqTKpvFzy9IWveZEgRzfMHdgd7E7YheTgjEfSWDpvaMocP4iL\r\nkyIRiUIsrUYHk7Ss/9VO5CWDKiHeV6F6pF67gpMJl7iWa/c9Yl5wupLqsETV\r\nAn/FMntdwUCAH8sCKxd4LKq1WFfwTxqoT4IzSdENSIVqE/RLcZvyH5oKJeQ4\r\nIVCRm0ARfGEOM96MA6Wc4hOSsFOcdOd6lF0=\r\n=ZI1J\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "ka-weihe", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ka-weihe", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fastest-levenshtein_1.0.15_1659452714251_0.47510417740836375"}, "_hasShrinkwrap": false}, "1.0.16": {"name": "fastest-le<PERSON><PERSON><PERSON>", "version": "1.0.16", "description": "Fastest Levenshtein distance implementation in JS.", "main": "mod.js", "types": "mod.d.ts", "module": "./esm/mod.js", "repository": {"type": "git", "url": "git+https://github.com/ka-weihe/fastest-levenshtein.git"}, "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "distance", "fast", "fastest", "edit", "string", "similarity", "algorithm", "match", "comparison", "fuzzy", "search", "string", "matching", "similar", "node", "difference"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/ka-weihe/fastest-levenshtein/issues"}, "homepage": "https://github.com/ka-weihe/fastest-levenshtein#README", "scripts": {"build": "tsc mod.ts --declaration", "build:esm": "tsc --declaration -p tsconfig.esm.json", "prepare": "npm run build && npm run build:esm", "bench": "npm run build && tsc bench.ts && node bench.js", "test": "npm run build && tsc test.ts && jest test.js", "test:coverage": "npm run build && jest --coverage", "test:coveralls": "npm run build && jest --coverage --coverageReporters=text-lcov | coveralls"}, "devDependencies": {"@types/benchmark": "^1.0.33", "@types/jest": "^26.0.15", "@typescript-eslint/eslint-plugin": "^4.7.0", "@typescript-eslint/parser": "^4.7.0", "benchmark": "^2.1.4", "coveralls": "^3.1.0", "eslint": "^7.13.0", "eslint-config-node": "^4.1.0", "eslint-config-prettier": "^6.15.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^3.1.4", "fast-levenshtein": "^2.0.6", "jest": "^26.6.3", "js-levenshtein": "^1.1.6", "leven": "^3.1.0", "levenshtein-edit-distance": "^2.0.5", "natural": "^2.1.5", "prettier": "^2.1.2", "talisman": "^1.1.3", "typescript": "^4.0.5"}, "engines": {"node": ">= 4.9.1"}, "gitHead": "03d621ba324d0f665b3b7f557429ca622560d9a3", "_id": "fastest-<PERSON><PERSON><PERSON><PERSON>@1.0.16", "_nodeVersion": "18.6.0", "_npmVersion": "8.14.0", "dist": {"integrity": "sha512-eRnCtTTtGZFpQCwhJiUOuxPQWRXVKYDn0b2PeHfXL6/Zi53SLAzAHfVhVWK2AryC/WH05kGfxhFIPvTF0SXQzg==", "shasum": "210e61b6ff181de91ea9b3d1b84fdedd47e034e5", "tarball": "https://registry.npmjs.org/fastest-levenshtein/-/fastest-levenshtein-1.0.16.tgz", "fileCount": 14, "unpackedSize": 21281, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFOdJoO0kJEfMuU5uPTk6jy8YDO55fM9wES/xF5T7nIIAiEAr5w8LF+q1xww07bsScUdkh7mEPXUXExZmVBCqaLSkoE="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi6XfgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp0ow//Tjdiz25jpEBdf1aVaHPxDbj/X4CY21LW+ORCj/yxGSqb7Mc3\r\n8DQ89Jj5EG9ov68ykWWd+afGZxtB4GD/NBAjBKibmekcf3UsnNP/1Jgc//hV\r\nAIIeA8BXkDTwIn5snxvqrrc8yZ1d0YwTW+PvcCsBU3nywDDS8J0kwQopYvcX\r\nLQ5OV7QjPOnPZKHmaX0+j73artWER4x2lRYOFLTDt+dTl680mvzApHjWVRAL\r\n0A1t2t+UU8aMfPYF0BzFQGqfkoqaP2DwDNgGoyJH2RGw/4LZmlPrg+sVZQoo\r\nqO4mclkCwvwMRGnZ44PZSwAqO16ZpeD+k8JOPP1DqrwWtcGRPZlfh4orLquS\r\nnYDjc4mJ7pCzz3C4zr9QC2Nj74o2rVoXlblz3aqI92bKtFxgG8l4lbibmQdX\r\n/a0fe1ev/Fal49RcSCrMEYruQJTh87sJUw0KmJue8026R8+ivBXtqxwHJbry\r\nPVWegjXlzQ69yaJSmZfjJ0RIhKUuzRL1b/bQhq1JIZMbvyDcLYq/SeTRQ2ox\r\nPBNAXZQuyBX0LGk8HCN6KOKDppMzNIwVGEiOpPG7Txs7KQP584aImxUqV1Fk\r\nOhmpM6FvSzXmstsgrI5PFK9FFqwZg7modEuQrqv+mryn9nJ+8sxvkQ1qBgTo\r\njgTB9Ssvgm7RZT4i5tEC/4m1iDSmQ4yy2xY=\r\n=BnC5\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "ka-weihe", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ka-weihe", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fastest-levenshtein_1.0.16_1659467744307_0.9200916589622106"}, "_hasShrinkwrap": false}}, "time": {"created": "2020-07-22T11:49:02.705Z", "1.0.0": "2020-07-22T11:49:02.848Z", "modified": "2022-08-02T19:15:44.558Z", "1.0.1": "2020-07-22T11:50:41.187Z", "1.0.2": "2020-07-22T11:57:19.377Z", "1.0.3": "2020-07-22T11:57:59.914Z", "1.0.4": "2020-07-22T12:06:12.506Z", "1.0.5": "2020-07-22T12:06:26.329Z", "1.0.6": "2020-07-22T12:06:47.793Z", "1.0.7": "2020-07-22T12:30:30.520Z", "1.0.8": "2020-07-22T15:01:03.928Z", "1.0.9": "2020-07-22T20:18:17.572Z", "1.0.10": "2020-07-25T18:02:13.036Z", "1.0.11": "2020-08-07T23:19:56.292Z", "1.0.12": "2020-08-07T23:22:07.332Z", "1.0.13": "2022-07-19T14:22:23.930Z", "1.0.14": "2022-07-19T14:23:31.774Z", "1.0.15": "2022-08-02T15:05:14.434Z", "1.0.16": "2022-08-02T19:15:44.474Z"}, "maintainers": [{"name": "ka-weihe", "email": "<EMAIL>"}], "description": "Fastest Levenshtein distance implementation in JS.", "homepage": "https://github.com/ka-weihe/fastest-levenshtein#README", "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "distance", "fast", "fastest", "edit", "string", "similarity", "algorithm", "match", "comparison", "fuzzy", "search", "string", "matching", "similar", "node", "difference"], "repository": {"type": "git", "url": "git+https://github.com/ka-weihe/fastest-levenshtein.git"}, "author": {"name": "<PERSON><PERSON>"}, "bugs": {"url": "https://github.com/ka-weihe/fastest-levenshtein/issues"}, "license": "MIT", "readme": "# fastest-levenshtein :rocket: \n> Fastest JS/TS implemenation of [Levenshtein distance](https://en.wikipedia.org/wiki/Levenshtein_distance).<br>\n> Measure the difference between two strings.\n\n[![Build Status](https://travis-ci.org/ka-weihe/fastest-levenshtein.svg?branch=master)](https://travis-ci.org/ka-weihe/fastest-levenshtein)\n[![Coverage Status](https://coveralls.io/repos/github/ka-weihe/node-levenshtein/badge.svg?branch=master)](https://coveralls.io/github/ka-weihe/node-levenshtein?branch=master)\n[![Language grade: JavaScript](https://img.shields.io/lgtm/grade/javascript/g/ka-weihe/fastest-levenshtein.svg?logo=lgtm&logoWidth=18)](https://lgtm.com/projects/g/ka-weihe/fastest-levenshtein/context:javascript)\n![npm](https://img.shields.io/npm/dm/fastest-levenshtein)\n```bash\n$ npm i fastest-levenshtein\n```\n\n## Usage\n### Node\n```javascript\nconst {distance, closest} = require('fastest-levenshtein')\n\n// Print levenshtein-distance between 'fast' and 'faster' \nconsole.log(distance('fast', 'faster'))\n//=> 2\n\n// Print string from array with lowest edit-distance to 'fast'\nconsole.log(closest('fast', ['slow', 'faster', 'fastest']))\n//=> 'faster'\n```\n\n### Deno\n```javascript\nimport {distance, closest} from 'https://deno.land/x/fastest_levenshtein/mod.ts'\n\n// Print levenshtein-distance between 'fast' and 'faster' \nconsole.log(distance('fast', 'faster'))\n//=> 2\n\n// Print string from array with lowest edit-distance to 'fast'\nconsole.log(closest('fast', ['slow', 'faster', 'fastest']))\n//=> 'faster'\n```\n\n## Benchmark\nI generated 500 pairs of strings with length N. I measured the ops/sec each library achieves to process all the given pairs. Higher is better. \n\n| Test Target               | N=4   | N=8   | N=16  | N=32 | N=64  | N=128 | N=256 | N=512 | N=1024 |\n|---------------------------|-------|-------|-------|------|-------|-------|-------|-------|--------|\n| fastest-levenshtein       | 44423 | 23702 | 10764 | 4595 | 1049  | 291.5 | 86.64 | 22.24 | 5.473  |\n| js-levenshtein            | 21261 | 10030 | 2939  | 824  | 223   | 57.62 | 14.77 | 3.717 | 0.934  |\n| leven                     | 19688 | 6884  | 1606  | 436  | 117   | 30.34 | 7.604 | 1.929 | 0.478  |\n| fast-levenshtein          | 18577 | 6112  | 1265  | 345  | 89.41 | 22.70 | 5.676 | 1.428 | 0.348  |\n| levenshtein-edit-distance | 22968 | 7445  | 1493  | 409  | 109   | 28.07 | 7.095 | 1.789 | 0.445  |\n\n### Relative Performance\nThis image shows the relative performance between `fastest-levenshtein` and `js-levenshtein` (the 2nd fastest). `fastest-levenshtein` is always a lot faster. y-axis shows \"times faster\".\n\n![Benchmark](/images/relaperf.png)\n\n## License\nThis project is licensed under the MIT License - see the [LICENSE.md](LICENSE.md) file for details\n", "readmeFilename": "README.md"}