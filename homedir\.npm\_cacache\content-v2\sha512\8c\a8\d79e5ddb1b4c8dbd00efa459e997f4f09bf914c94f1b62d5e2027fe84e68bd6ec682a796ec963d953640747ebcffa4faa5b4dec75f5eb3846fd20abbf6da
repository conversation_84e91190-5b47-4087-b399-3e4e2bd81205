{"_id": "seq-queue", "_rev": "15-c077212bfc497f2d9d7f1fcaa5f81ad2", "name": "seq-queue", "description": "A simple tool to keep requests to be executed in order.", "dist-tags": {"latest": "0.0.5"}, "versions": {"0.0.1": {"name": "seq-queue", "author": {"name": "changchang", "email": "<EMAIL>"}, "version": "0.0.1", "description": "A simple tool to keep requests to be executed in order.", "homepage": "https://github.com/changchang/seq-queue", "repository": {"type": "git", "url": "**************:changchang/seq-queue.git"}, "dependencies": {}, "devDependencies": {"mocha": ">=0.0.1", "should": ">=0.0.1"}, "_npmUser": {"name": "changchang", "email": "<EMAIL>"}, "_id": "seq-queue@0.0.1", "optionalDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.1.1", "_nodeVersion": "v0.6.0", "_defaultsLoaded": true, "dist": {"shasum": "ff270df3fd3cf38c0e450af8dd1461b85ad23bca", "tarball": "https://registry.npmjs.org/seq-queue/-/seq-queue-0.0.1.tgz", "integrity": "sha512-M3tzJRSFaKwwJ3rYAdv9NIm0GbzL9+hhPEb5BsRqvtARy2XDri3a28Nq8Vyh7GG3SEaSuNd1Uj2+7ehzW5dxHw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEnGaLvjN5ePF/X1i8TvVB5KrDMcjwC+QAwfudxIDtmzAiEAupkFRlAOp3LFKk2vzsJWLKfJUZrnNnFWjoCyTJ9qV/4="}]}, "maintainers": [{"name": "changchang", "email": "<EMAIL>"}], "directories": {}}, "0.0.4": {"name": "seq-queue", "author": {"name": "changchang", "email": "<EMAIL>"}, "version": "0.0.4", "description": "A simple tool to keep requests to be executed in order.", "homepage": "https://github.com/changchang/seq-queue", "repository": {"type": "git", "url": "**************:changchang/seq-queue.git"}, "dependencies": {}, "devDependencies": {"mocha": ">=0.0.1", "should": ">=0.0.1"}, "_npmUser": {"name": "changchang", "email": "<EMAIL>"}, "_id": "seq-queue@0.0.4", "optionalDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.1.1", "_nodeVersion": "v0.6.0", "_defaultsLoaded": true, "dist": {"shasum": "52b52f0161dba324a85cfc32f3f1978e04cc3370", "tarball": "https://registry.npmjs.org/seq-queue/-/seq-queue-0.0.4.tgz", "integrity": "sha512-k5Vg/9To/i+DZTLKO/Lq8g4Sp27ZtZ8E5btorHxK15YXJf5wyoA77CpWEcPyH+TyMV09P9jntRGxpPIZNJH+9g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCuL/yspGGBjyNs20GCrLu85+fbi1FUy4V4bfMPct5FzQIhAJ9eGGXQYQ361wfOkHTXfvKfNFvuHAlLyofV6n98QRJ3"}]}, "maintainers": [{"name": "changchang", "email": "<EMAIL>"}], "directories": {}}, "0.0.5": {"name": "seq-queue", "author": {"name": "changchang", "email": "<EMAIL>"}, "version": "0.0.5", "description": "A simple tool to keep requests to be executed in order.", "homepage": "https://github.com/changchang/seq-queue", "repository": {"type": "git", "url": "**************:changchang/seq-queue.git"}, "dependencies": {}, "devDependencies": {"mocha": ">=0.0.1", "should": ">=0.0.1"}, "contributors": [{"name": "* <PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "seq-queue - queue to keep request process in sequence\n=====================================================\n\nSeq-queue is simple tool to keep requests to be executed in order.\n\nAs we known, Node.js codes run in asynchronous mode and the callbacks are unordered. But sometimes we may need the requests to be processed in order. For example, in a game, a player would do some operations such as turn right and go ahead. And in the server side, we would like to process these requests one by one, not do them all at the same time.\n\nSeq-queue takes the responsibility to make the asynchronous, unordered processing flow into serial and ordered. It's simple but not a repeated wheel.\n\nSeq-queue is a FIFO task queue and we can push tasks as we wish, anytime(before the queue closed), anywhere(if we hold the queue instance). A task is known as a function and we can do anything in the function and just need to call `task.done()` to tell the queue current task has finished. It promises that a task in queue would not be executed util all tasks before it finished.\n\nSeq-queue add timeout for each task execution. If a task throws an uncaught exception in its call back or a developer forgets to call `task.done()` callback, queue would be blocked and would not execute the left tasks. To avoid these situations, seq-queue set a timeout for each task. If a task timeout, queue would drop the task and notify develop by a 'timeout' event and then invoke the next task. Any `task.done()` invoked in a timeout task would be ignored.\n\n * Tags: node.js\n\n##Installation\n```\nnpm install seq-queue\n```\n\n##Usage\n``` javascript\nvar seqqueue = require('seq-queue');\n\nvar queue = seqqueue.createQueue(1000);\n\nqueue.push(\n  function(task) {\n    setTimeout(function() {\n      console.log('hello ');\n      task.done();\n    }, 500);\n  }, \n  function() {\n    console.log('task timeout');\n  }, \n  1000\n);\n\nqueue.push(\n  function(task) {\n    setTimeout(function() {\n      console.log('world~');\n      task.done();\n    }, 500);\n  }\n);\n``` \n\n##API\n###seqqueue.createQueue(timeout)\nCreate a new queue instance. A global timeout value in ms for the new instance can be set by `timeout` parameter or use the default timeout (3s) by no parameter.\n\n###queue.push(fn, ontimeout, timeout)\nAdd a task into the queue instance. \n####Arguments\n+ fn(task) - The function that describes the content of task and would be invoke by queue. `fn` takes a arguemnt task and we *must* call task.done() to tell queue current task has finished.\n+ ontimeout() - Callback for task timeout. \n+ timeout - Timeout in ms for `fn`. If specified, it would overwrite the global timeout that set by `createQueue` for `fn`.\n\n###queue.close(force)\nClose the queue. A closed queue would stop receiving new task immediately. And the left tasks would be treated in different ways decided by `force`.\n####Arguments\n+ force - If true, queue would stop working immediately and ignore any tasks left in queue. Otherwise queue would execute the tasks in queue and then stop.\n\n##Event\nSeq-queue instances extend the EventEmitter and would emit events in their life cycles.\n###'timeout'(totask)\nIf current task not invoke task.done() within the timeout ms, a timeout event would be emit. totask.fn and totask.timeout is the `fn` and `timeout` arguments that passed by `queue.push(2)`.\n###'error'(err, task)\nIf the task function (not callbacks) throws an uncaught error, queue would emit an error event and passes the err and task informations by event callback arguments.\n###'closed'\nEmit when the close(false) is invoked.\n###'drained'\nEmit when close(true) is invoked or all tasks left have finished in closed status.\n", "_id": "seq-queue@0.0.5", "dist": {"shasum": "d56812e1c017a6e4e7c3e3a37a1da6d78dd3c93e", "tarball": "https://registry.npmjs.org/seq-queue/-/seq-queue-0.0.5.tgz", "integrity": "sha512-hr3Wtp/GZIc/6DAGPDcV4/9WoZhjrkXsi5B/07QgX8tsdc6ilr7BFM6PM6rbdAX1kFSDYeZGLipIZZKyQP0O5Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC5O10pOQd34HasUZnt95P361vYETAYkYfhaVv83n8gnAiEApQQa4uxCeYvaJrhqutmAt1/CLKoFwXkT30bTAoPUgxs="}]}, "_npmVersion": "1.1.59", "_npmUser": {"name": "changchang", "email": "<EMAIL>"}, "maintainers": [{"name": "changchang", "email": "<EMAIL>"}]}}, "readme": "#seq-queue\nSeq-queue is simple tool to keep requests to be executed in order.\n\nAs we known, Node.js codes run in asynchronous mode and the callbacks are unordered. But sometimes we may need the requests to be processed in order. For example, in a game, a player would do some operations such as turn right and go ahead. And in the server side, we would like to process these requests one by one, not do them all at the same time.\n\nSeq-queue takes the responsibility to make the asynchronous, unordered processing flow into serial and ordered. It's simple but not a repeated wheel.\n\nSeq-queue is a FIFO task queue and we can push tasks as we wish, anytime(before the queue closed), anywhere(if we hold the queue instance). A task is known as a function and we can do anything in the function and just need to call `task.done()` to tell the queue current task has finished. It promises that a task in queue would not be executed util all tasks before it finished.\n\nSeq-queue add timeout for each task execution. If a task throws an uncaught exception in its call back or a developer forgets to call `task.done()` callback, queue would be blocked and would not execute the left tasks. To avoid these situations, seq-queue set a timeout for each task. If a task timeout, queue would drop the task and notify develop by a 'timeout' event and then invoke the next task. Any `task.done()` invoked in a timeout task would be ignored.\n\n##Installation\n```\nnpm install seq-queue\n```\n\n##Usage\n``` javascript\nvar seqqueue = require('seq-queue');\n\nvar queue = seqqueue.createQueue(1000);\n\nqueue.push(function(task) {\n\tsetTimeout(function() {\n\t\tconsole.log('hello ');\n\t\ttask.done();\n\t}, 1000);\n});\n\nqueue.push(function(task) {\n\tsetTimeout(function() {\n\t\tconsole.log('world~');\n\t\ttask.done();\n\t}, 500);\n});\n``` \n\n##API\n###seqqueue.createQueue(timeout)\nCreate a new queue instance. A gloabal timeout value in ms for the new instance can be set by `timeout` parameter or use the default timeout (3ms) by no parameter.\n\n###queue.push(fn, timeout)\nAdd a task into the queue instance. \n####Arguments\n+ fn(task) - The function that describes the content of task and would be invoke by queue. `fn` takes a arguemnt task and we *must* call task.done() to tell queue current task has finished. \n+ timeout - Timeout in ms for `fn`. If specified, it would overwrite the gloabal timeout that set by `createQueue` for `fn`.\n\n###queue.close(force)\nClose the queue. A closed queue would stop receiving new task immediately. And the left tasks would be treated in different ways decided by `force`.\n####Arguments\n+ force - If true, queue would stop working immediately and ignore any tasks left in queue. Otherwise queue would execute the tasks in queue and then stop.\n\n##Event\nSeq-queue instances extend the EventEmitter and would emit events in their life cycles.\n###'timeout'(totask)\nIf current task not invoke task.done() within the timeout ms, a timeout event would be emit. totask.fn and totask.timeout is the `fn` and `timeout` arguments that passed by `queue.push(2)`.\n###'closed'\nEmit when the close(false) is invoked.\n###'drained'\nEmit when close(true) is invoked or all tasks left have finished in closed status.", "maintainers": [{"name": "changchang", "email": "<EMAIL>"}], "time": {"modified": "2022-06-26T17:46:59.708Z", "created": "2012-02-20T09:41:17.949Z", "0.0.1": "2012-02-20T09:41:32.861Z", "0.0.4": "2012-03-30T06:36:52.992Z", "0.0.5": "2012-11-20T09:20:01.692Z"}, "author": {"name": "changchang", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "**************:changchang/seq-queue.git"}}