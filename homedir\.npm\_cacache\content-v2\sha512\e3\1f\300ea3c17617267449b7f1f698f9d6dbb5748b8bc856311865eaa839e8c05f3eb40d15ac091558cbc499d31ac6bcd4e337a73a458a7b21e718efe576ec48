{"_id": "tweetnacl", "_rev": "52-83193cdfd3a79b0947836e7950a9cfdd", "name": "tweetnacl", "time": {"modified": "2023-04-12T02:21:08.029Z", "created": "2014-07-08T17:17:59.887Z", "0.9.0": "2014-07-08T17:17:59.887Z", "0.9.1": "2014-07-08T17:27:03.089Z", "0.9.2": "2014-07-22T11:51:50.399Z", "0.10.0": "2014-07-26T14:13:21.181Z", "0.10.1": "2014-07-31T08:56:22.521Z", "0.11.0": "2014-08-03T12:57:58.614Z", "0.11.1": "2014-08-06T09:51:16.595Z", "0.11.2": "2014-08-08T11:33:58.342Z", "0.12.0": "2014-09-03T12:53:37.141Z", "0.12.1": "2014-09-09T19:10:37.958Z", "0.12.2": "2014-09-18T16:07:52.210Z", "0.13.0": "2014-12-26T16:18:57.757Z", "0.13.1": "2015-04-15T16:16:46.594Z", "0.13.2": "2015-09-17T14:24:35.137Z", "0.13.3": "2016-01-06T23:08:59.717Z", "0.14.0": "2016-02-20T16:07:29.491Z", "0.14.1": "2016-02-25T00:35:25.704Z", "0.14.2": "2016-03-28T17:49:52.954Z", "0.14.3": "2016-03-29T04:15:52.637Z", "0.14.4": "2016-12-02T18:10:24.264Z", "0.14.5": "2016-12-13T11:11:56.945Z", "1.0.0-rc.1": "2017-02-22T11:39:29.982Z", "1.0.0": "2017-05-22T18:43:50.045Z", "1.0.1": "2019-01-24T14:52:36.909Z", "1.0.2": "2020-01-16T19:28:56.851Z", "1.0.3": "2020-02-10T19:49:05.829Z"}, "maintainers": [{"name": "dchest", "email": "<EMAIL>"}], "dist-tags": {"latest": "1.0.3"}, "description": "Port of TweetNaCl cryptographic library to JavaScript", "readme": "TweetNaCl.js\n============\n\nPort of [TweetNaCl](http://tweetnacl.cr.yp.to) / [NaCl](http://nacl.cr.yp.to/)\nto JavaScript for modern browsers and Node.js. Public domain.\n\n[![Build Status](https://travis-ci.org/dchest/tweetnacl-js.svg?branch=master)\n](https://travis-ci.org/dchest/tweetnacl-js)\n\nDemo: <https://dchest.github.io/tweetnacl-js/>\n\nDocumentation\n=============\n\n* [Overview](#overview)\n* [Audits](#audits)\n* [Installation](#installation)\n* [Examples](#examples)\n* [Usage](#usage)\n  * [Public-key authenticated encryption (box)](#public-key-authenticated-encryption-box)\n  * [Secret-key authenticated encryption (secretbox)](#secret-key-authenticated-encryption-secretbox)\n  * [Scalar multiplication](#scalar-multiplication)\n  * [Signatures](#signatures)\n  * [Hashing](#hashing)\n  * [Random bytes generation](#random-bytes-generation)\n  * [Constant-time comparison](#constant-time-comparison)\n* [System requirements](#system-requirements)\n* [Development and testing](#development-and-testing)\n* [Benchmarks](#benchmarks)\n* [Contributors](#contributors)\n* [Who uses it](#who-uses-it)\n\n\nOverview\n--------\n\nThe primary goal of this project is to produce a translation of TweetNaCl to\nJavaScript which is as close as possible to the original C implementation, plus\na thin layer of idiomatic high-level API on top of it.\n\nThere are two versions, you can use either of them:\n\n* `nacl.js` is the port of TweetNaCl with minimum differences from the\n  original + high-level API.\n\n* `nacl-fast.js` is like `nacl.js`, but with some functions replaced with\n  faster versions. (Used by default when importing NPM package.)\n\n\nAudits\n------\n\nTweetNaCl.js has been audited by [Cure53](https://cure53.de/) in January-February\n2017 (audit was sponsored by [Deletype](https://deletype.com)):\n\n> The overall outcome of this audit signals a particularly positive assessment\n> for TweetNaCl-js, as the testing team was unable to find any security\n> problems in the library. It has to be noted that this is an exceptionally\n> rare result of a source code audit for any project and must be seen as a true\n> testament to a development proceeding with security at its core.\n>\n> To reiterate, the TweetNaCl-js project, the source code was found to be\n> bug-free at this point.\n>\n> [...]\n>\n> In sum, the testing team is happy to recommend the TweetNaCl-js project as\n> likely one of the safer and more secure cryptographic tools among its\n> competition.\n\n[Read full audit report](https://cure53.de/tweetnacl.pdf)\n\n\nInstallation\n------------\n\nYou can install TweetNaCl.js via a package manager:\n\n[Yarn](https://yarnpkg.com/):\n\n    $ yarn add tweetnacl\n\n[NPM](https://www.npmjs.org/):\n\n    $ npm install tweetnacl\n\nor [download source code](https://github.com/dchest/tweetnacl-js/releases).\n\n\nExamples\n--------\nYou can find usage examples in our [wiki](https://github.com/dchest/tweetnacl-js/wiki/Examples).\n\n\nUsage\n-----\n\nAll API functions accept and return bytes as `Uint8Array`s.  If you need to\nencode or decode strings, use functions from\n<https://github.com/dchest/tweetnacl-util-js> or one of the more robust codec\npackages.\n\nIn Node.js v4 and later `Buffer` objects are backed by `Uint8Array`s, so you\ncan freely pass them to TweetNaCl.js functions as arguments. The returned\nobjects are still `Uint8Array`s, so if you need `Buffer`s, you'll have to\nconvert them manually; make sure to convert using copying: `Buffer.from(array)`\n(or `new Buffer(array)` in Node.js v4 or earlier), instead of sharing:\n`Buffer.from(array.buffer)` (or `new Buffer(array.buffer)` Node 4 or earlier),\nbecause some functions return subarrays of their buffers.\n\n\n### Public-key authenticated encryption (box)\n\nImplements *x25519-xsalsa20-poly1305*.\n\n#### nacl.box.keyPair()\n\nGenerates a new random key pair for box and returns it as an object with\n`publicKey` and `secretKey` members:\n\n    {\n       publicKey: ...,  // Uint8Array with 32-byte public key\n       secretKey: ...   // Uint8Array with 32-byte secret key\n    }\n\n\n#### nacl.box.keyPair.fromSecretKey(secretKey)\n\nReturns a key pair for box with public key corresponding to the given secret\nkey.\n\n#### nacl.box(message, nonce, theirPublicKey, mySecretKey)\n\nEncrypts and authenticates message using peer's public key, our secret key, and\nthe given nonce, which must be unique for each distinct message for a key pair.\n\nReturns an encrypted and authenticated message, which is\n`nacl.box.overheadLength` longer than the original message.\n\n#### nacl.box.open(box, nonce, theirPublicKey, mySecretKey)\n\nAuthenticates and decrypts the given box with peer's public key, our secret\nkey, and the given nonce.\n\nReturns the original message, or `null` if authentication fails.\n\n#### nacl.box.before(theirPublicKey, mySecretKey)\n\nReturns a precomputed shared key which can be used in `nacl.box.after` and\n`nacl.box.open.after`.\n\n#### nacl.box.after(message, nonce, sharedKey)\n\nSame as `nacl.box`, but uses a shared key precomputed with `nacl.box.before`.\n\n#### nacl.box.open.after(box, nonce, sharedKey)\n\nSame as `nacl.box.open`, but uses a shared key precomputed with `nacl.box.before`.\n\n#### Constants\n\n##### nacl.box.publicKeyLength = 32\n\nLength of public key in bytes.\n\n##### nacl.box.secretKeyLength = 32\n\nLength of secret key in bytes.\n\n##### nacl.box.sharedKeyLength = 32\n\nLength of precomputed shared key in bytes.\n\n##### nacl.box.nonceLength = 24\n\nLength of nonce in bytes.\n\n##### nacl.box.overheadLength = 16\n\nLength of overhead added to box compared to original message.\n\n\n### Secret-key authenticated encryption (secretbox)\n\nImplements *xsalsa20-poly1305*.\n\n#### nacl.secretbox(message, nonce, key)\n\nEncrypts and authenticates message using the key and the nonce. The nonce must\nbe unique for each distinct message for this key.\n\nReturns an encrypted and authenticated message, which is\n`nacl.secretbox.overheadLength` longer than the original message.\n\n#### nacl.secretbox.open(box, nonce, key)\n\nAuthenticates and decrypts the given secret box using the key and the nonce.\n\nReturns the original message, or `null` if authentication fails.\n\n#### Constants\n\n##### nacl.secretbox.keyLength = 32\n\nLength of key in bytes.\n\n##### nacl.secretbox.nonceLength = 24\n\nLength of nonce in bytes.\n\n##### nacl.secretbox.overheadLength = 16\n\nLength of overhead added to secret box compared to original message.\n\n\n### Scalar multiplication\n\nImplements *x25519*.\n\n#### nacl.scalarMult(n, p)\n\nMultiplies an integer `n` by a group element `p` and returns the resulting\ngroup element.\n\n#### nacl.scalarMult.base(n)\n\nMultiplies an integer `n` by a standard group element and returns the resulting\ngroup element.\n\n#### Constants\n\n##### nacl.scalarMult.scalarLength = 32\n\nLength of scalar in bytes.\n\n##### nacl.scalarMult.groupElementLength = 32\n\nLength of group element in bytes.\n\n\n### Signatures\n\nImplements [ed25519](http://ed25519.cr.yp.to).\n\n#### nacl.sign.keyPair()\n\nGenerates new random key pair for signing and returns it as an object with\n`publicKey` and `secretKey` members:\n\n    {\n       publicKey: ...,  // Uint8Array with 32-byte public key\n       secretKey: ...   // Uint8Array with 64-byte secret key\n    }\n\n#### nacl.sign.keyPair.fromSecretKey(secretKey)\n\nReturns a signing key pair with public key corresponding to the given\n64-byte secret key. The secret key must have been generated by\n`nacl.sign.keyPair` or `nacl.sign.keyPair.fromSeed`.\n\n#### nacl.sign.keyPair.fromSeed(seed)\n\nReturns a new signing key pair generated deterministically from a 32-byte seed.\nThe seed must contain enough entropy to be secure. This method is not\nrecommended for general use: instead, use `nacl.sign.keyPair` to generate a new\nkey pair from a random seed.\n\n#### nacl.sign(message, secretKey)\n\nSigns the message using the secret key and returns a signed message.\n\n#### nacl.sign.open(signedMessage, publicKey)\n\nVerifies the signed message and returns the message without signature.\n\nReturns `null` if verification failed.\n\n#### nacl.sign.detached(message, secretKey)\n\nSigns the message using the secret key and returns a signature.\n\n#### nacl.sign.detached.verify(message, signature, publicKey)\n\nVerifies the signature for the message and returns `true` if verification\nsucceeded or `false` if it failed.\n\n#### Constants\n\n##### nacl.sign.publicKeyLength = 32\n\nLength of signing public key in bytes.\n\n##### nacl.sign.secretKeyLength = 64\n\nLength of signing secret key in bytes.\n\n##### nacl.sign.seedLength = 32\n\nLength of seed for `nacl.sign.keyPair.fromSeed` in bytes.\n\n##### nacl.sign.signatureLength = 64\n\nLength of signature in bytes.\n\n\n### Hashing\n\nImplements *SHA-512*.\n\n#### nacl.hash(message)\n\nReturns SHA-512 hash of the message.\n\n#### Constants\n\n##### nacl.hash.hashLength = 64\n\nLength of hash in bytes.\n\n\n### Random bytes generation\n\n#### nacl.randomBytes(length)\n\nReturns a `Uint8Array` of the given length containing random bytes of\ncryptographic quality.\n\n**Implementation note**\n\nTweetNaCl.js uses the following methods to generate random bytes,\ndepending on the platform it runs on:\n\n* `window.crypto.getRandomValues` (WebCrypto standard)\n* `window.msCrypto.getRandomValues` (Internet Explorer 11)\n* `crypto.randomBytes` (Node.js)\n\nIf the platform doesn't provide a suitable PRNG, the following functions,\nwhich require random numbers, will throw exception:\n\n* `nacl.randomBytes`\n* `nacl.box.keyPair`\n* `nacl.sign.keyPair`\n\nOther functions are deterministic and will continue working.\n\nIf a platform you are targeting doesn't implement secure random number\ngenerator, but you somehow have a cryptographically-strong source of entropy\n(not `Math.random`!), and you know what you are doing, you can plug it into\nTweetNaCl.js like this:\n\n    nacl.setPRNG(function(x, n) {\n      // ... copy n random bytes into x ...\n    });\n\nNote that `nacl.setPRNG` *completely replaces* internal random byte generator\nwith the one provided.\n\n\n### Constant-time comparison\n\n#### nacl.verify(x, y)\n\nCompares `x` and `y` in constant time and returns `true` if their lengths are\nnon-zero and equal, and their contents are equal.\n\nReturns `false` if either of the arguments has zero length, or arguments have\ndifferent lengths, or their contents differ.\n\n\nSystem requirements\n-------------------\n\nTweetNaCl.js supports modern browsers that have a cryptographically secure\npseudorandom number generator and typed arrays, including the latest versions\nof:\n\n* Chrome\n* Firefox\n* Safari (Mac, iOS)\n* Internet Explorer 11\n\nOther systems:\n\n* Node.js\n\n\nDevelopment and testing\n------------------------\n\nInstall NPM modules needed for development:\n\n    $ npm install\n\nTo build minified versions:\n\n    $ npm run build\n\nTests use minified version, so make sure to rebuild it every time you change\n`nacl.js` or `nacl-fast.js`.\n\n### Testing\n\nTo run tests in Node.js:\n\n    $ npm run test-node\n\nBy default all tests described here work on `nacl.min.js`. To test other\nversions, set environment variable `NACL_SRC` to the file name you want to test.\nFor example, the following command will test fast minified version:\n\n    $ NACL_SRC=nacl-fast.min.js npm run test-node\n\nTo run full suite of tests in Node.js, including comparing outputs of\nJavaScript port to outputs of the original C version:\n\n    $ npm run test-node-all\n\nTo prepare tests for browsers:\n\n    $ npm run build-test-browser\n\nand then open `test/browser/test.html` (or `test/browser/test-fast.html`) to\nrun them.\n\nTo run tests in both Node and Electron:\n\n    $ npm test\n\n### Benchmarking\n\nTo run benchmarks in Node.js:\n\n    $ npm run bench\n    $ NACL_SRC=nacl-fast.min.js npm run bench\n\nTo run benchmarks in a browser, open `test/benchmark/bench.html` (or\n`test/benchmark/bench-fast.html`).\n\n\nBenchmarks\n----------\n\nFor reference, here are benchmarks from MacBook Pro (Retina, 13-inch, Mid 2014)\nlaptop with 2.6 GHz Intel Core i5 CPU (Intel) in Chrome 53/OS X and Xiaomi Redmi\nNote 3 smartphone with 1.8 GHz Qualcomm Snapdragon 650 64-bit CPU (ARM) in\nChrome 52/Android:\n\n|               | nacl.js Intel | nacl-fast.js Intel  |   nacl.js ARM | nacl-fast.js ARM  |\n| ------------- |:-------------:|:-------------------:|:-------------:|:-----------------:|\n| salsa20       | 1.3 MB/s      | 128 MB/s            |  0.4 MB/s     |  43 MB/s          |\n| poly1305      | 13 MB/s       | 171 MB/s            |  4 MB/s       |  52 MB/s          |\n| hash          | 4 MB/s        | 34 MB/s             |  0.9 MB/s     |  12 MB/s          |\n| secretbox 1K  | 1113 op/s     | 57583 op/s          |  334 op/s     |  14227 op/s       |\n| box 1K        | 145 op/s      | 718 op/s            |  37 op/s      |  368 op/s         |\n| scalarMult    | 171 op/s      | 733 op/s            |  56 op/s      |  380 op/s         |\n| sign          | 77  op/s      | 200 op/s            |  20 op/s      |  61 op/s          |\n| sign.open     | 39  op/s      | 102  op/s           |  11 op/s      |  31 op/s          |\n\n(You can run benchmarks on your devices by clicking on the links at the bottom\nof the [home page](https://tweetnacl.js.org)).\n\nIn short, with *nacl-fast.js* and 1024-byte messages you can expect to encrypt and\nauthenticate more than 57000 messages per second on a typical laptop or more than\n14000 messages per second on a $170 smartphone, sign about 200 and verify 100\nmessages per second on a laptop or 60 and 30 messages per second on a smartphone,\nper CPU core (with Web Workers you can do these operations in parallel),\nwhich is good enough for most applications.\n\n\nContributors\n------------\n\nSee AUTHORS.md file.\n\n\nThird-party libraries based on TweetNaCl.js\n-------------------------------------------\n\n* [forward-secrecy](https://github.com/alax/forward-secrecy) — Axolotl ratchet implementation\n* [nacl-stream](https://github.com/dchest/nacl-stream-js) - streaming encryption\n* [tweetnacl-auth-js](https://github.com/dchest/tweetnacl-auth-js) — implementation of [`crypto_auth`](http://nacl.cr.yp.to/auth.html)\n* [tweetnacl-sealed-box](https://github.com/whs/tweetnacl-sealed-box) — implementation of [`sealed boxes`](https://download.libsodium.org/doc/public-key_cryptography/sealed_boxes.html)\n* [chloride](https://github.com/dominictarr/chloride) - unified API for various NaCl modules\n\n\nWho uses it\n-----------\n\nSome notable users of TweetNaCl.js:\n\n* [GitHub](https://github.com)\n* [MEGA](https://github.com/meganz/webclient)\n* [Stellar](https://www.stellar.org/)\n* [miniLock](https://github.com/kaepora/miniLock)\n", "versions": {"0.9.1": {"name": "tweetnacl", "version": "0.9.1", "description": "Port of TweetNaCl cryptographic library to JavaScript", "main": "nacl.js", "directories": {"test": "test"}, "scripts": {"build": "uglifyjs nacl.js -c -m -o nacl.min.js && uglifyjs nacl-fast.js -c -m -o nacl-fast.min.js", "test": "tape test/*.js | faucet", "testall": "make -C test/c && tape test/*.js test/c/*.js | faucet", "browser": "browserify test/browser/init.js test/*.js | uglifyjs -c -m -o test/browser/_bundle.js 2>/dev/null", "browser-quick": "browserify test/browser/init.js test/*.quick.js | uglifyjs -c -m -o test/browser/_bundle-quick.js 2>/dev/null", "testling": "browserify test/browser/testling_init.js test/*.js | testling | faucet", "firefox": "browserify test/browser/testling_init.js test/*.js | testling -x firefox | faucet", "chrome": "browserify test/browser/testling_init.js test/*.js | testling -x google-chrome | faucet", "bench": "node test/benchmark/bench.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/11..latest", "chrome/22..latest", "firefox/16..latest", "safari/latest", "opera/11.0..latest", "iphone/6..latest", "ipad/6..latest", "android-browser/latest"]}, "repository": {"type": "git", "url": "https://github.com/dchest/tweetnacl-js.git"}, "keywords": ["crypto", "cryptography", "curve25519", "ed25519", "encrypt", "hash", "key", "nacl", "poly1305", "public", "salsa20", "signatures"], "author": {"name": "TweetNaCl-js contributors"}, "license": "Public domain", "bugs": {"url": "https://github.com/dchest/tweetnacl-js/issues"}, "homepage": "https://dchest.github.io/tweetnacl-js", "devDependencies": {"browserify": "^4.2.0", "faucet": "0.0.1", "tap-browser-color": "^0.1.2", "tape": "^2.13.3", "testling": "^1.7.0", "uglify-js": "^2.4.14"}, "gitHead": "84a604351a03eca9a9737594cf5b45b31122183f", "_id": "tweetnacl@0.9.1", "_shasum": "061d781c759b0f4ba6f8c93e71b9cba6d3e61db7", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "dchest", "email": "<EMAIL>"}, "maintainers": [{"name": "dchest", "email": "<EMAIL>"}], "dist": {"shasum": "061d781c759b0f4ba6f8c93e71b9cba6d3e61db7", "tarball": "https://registry.npmjs.org/tweetnacl/-/tweetnacl-0.9.1.tgz", "integrity": "sha512-q2T8LwkQiCw4LdrNBQiCwCgfeH+7jF4BQO06vJla6WMy9t7rfHQedyE4X1i4fFLz4hVG9T2GSOOsNX65KzNNLQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDRV90oSzuG0wkTrw/bD4RnLvF/yKNLSgWxGqQHS71bqwIhAIvufGLFjis9my4Mzd+tL6ik8Po+K9JXFAOlkVX4lapl"}]}}, "0.9.2": {"name": "tweetnacl", "version": "0.9.2", "description": "Port of TweetNaCl cryptographic library to JavaScript", "main": "nacl.js", "directories": {"test": "test"}, "scripts": {"build": "uglifyjs nacl.js -c -m -o nacl.min.js && uglifyjs nacl-fast.js -c -m -o nacl-fast.min.js", "test": "tape test/*.js | faucet", "testall": "make -C test/c && tape test/*.js test/c/*.js | faucet", "browser": "browserify test/browser/init.js test/*.js | uglifyjs -c -m -o test/browser/_bundle.js 2>/dev/null", "browser-quick": "browserify test/browser/init.js test/*.quick.js | uglifyjs -c -m -o test/browser/_bundle-quick.js 2>/dev/null", "testling": "browserify test/browser/testling_init.js test/*.js | testling | faucet", "firefox": "browserify test/browser/testling_init.js test/*.js | testling -x firefox | faucet", "chrome": "browserify test/browser/testling_init.js test/*.js | testling -x google-chrome | faucet", "bench": "node test/benchmark/bench.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/11..latest", "chrome/22..latest", "firefox/16..latest", "safari/latest", "opera/11.0..latest", "iphone/6..latest", "ipad/6..latest", "android-browser/latest"]}, "repository": {"type": "git", "url": "https://github.com/dchest/tweetnacl-js.git"}, "keywords": ["crypto", "cryptography", "curve25519", "ed25519", "encrypt", "hash", "key", "nacl", "poly1305", "public", "salsa20", "signatures"], "author": {"name": "TweetNaCl-js contributors"}, "license": "Public domain", "bugs": {"url": "https://github.com/dchest/tweetnacl-js/issues"}, "homepage": "https://dchest.github.io/tweetnacl-js", "devDependencies": {"browserify": "^4.2.0", "faucet": "0.0.1", "tap-browser-color": "^0.1.2", "tape": "^2.13.3", "testling": "^1.7.0", "uglify-js": "^2.4.14"}, "gitHead": "4389a049194c4bd557f17c7910b278aaf6bbe43f", "_id": "tweetnacl@0.9.2", "_shasum": "ce6690698ccfa0bb0fe0b12997ec0211900add38", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "dchest", "email": "<EMAIL>"}, "maintainers": [{"name": "dchest", "email": "<EMAIL>"}], "dist": {"shasum": "ce6690698ccfa0bb0fe0b12997ec0211900add38", "tarball": "https://registry.npmjs.org/tweetnacl/-/tweetnacl-0.9.2.tgz", "integrity": "sha512-/CE4y3TM7nM3uPs4AtsjR9mCRJ6WIZlZO7XyzuBtknbjj45acC/bEAqws51yKUrofXWM7MuZHJzM8nYUgb7NJg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDnm6bIArwde9RYnycO6suT7y1FVWOFwM3Wd9jJASJl8QIgTK9b/EB++phwZiNyurk6FlwuJFh4R17IFrHqSTL6pv8="}]}}, "0.10.0": {"name": "tweetnacl", "version": "0.10.0", "description": "Port of TweetNaCl cryptographic library to JavaScript", "main": "nacl.js", "directories": {"test": "test"}, "scripts": {"build": "uglifyjs nacl.js -c -m -o nacl.min.js && uglifyjs nacl-fast.js -c -m -o nacl-fast.min.js", "test": "tape test/*.js | faucet", "testall": "make -C test/c && tape test/*.js test/c/*.js | faucet", "browser": "browserify test/browser/init.js test/*.js | uglifyjs -c -m -o test/browser/_bundle.js 2>/dev/null", "browser-quick": "browserify test/browser/init.js test/*.quick.js | uglifyjs -c -m -o test/browser/_bundle-quick.js 2>/dev/null", "testling": "browserify test/browser/testling_init.js test/*.js | testling | faucet", "firefox": "browserify test/browser/testling_init.js test/*.js | testling -x firefox | faucet", "chrome": "browserify test/browser/testling_init.js test/*.js | testling -x google-chrome | faucet", "bench": "node test/benchmark/bench.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/11..latest", "chrome/22..latest", "firefox/16..latest", "safari/latest", "opera/11.0..latest", "iphone/6..latest", "ipad/6..latest", "android-browser/latest"]}, "repository": {"type": "git", "url": "https://github.com/dchest/tweetnacl-js.git"}, "keywords": ["crypto", "cryptography", "curve25519", "ed25519", "encrypt", "hash", "key", "nacl", "poly1305", "public", "salsa20", "signatures"], "author": {"name": "TweetNaCl-js contributors"}, "license": "Public domain", "bugs": {"url": "https://github.com/dchest/tweetnacl-js/issues"}, "homepage": "https://dchest.github.io/tweetnacl-js", "devDependencies": {"browserify": "^4.2.0", "faucet": "0.0.1", "tap-browser-color": "^0.1.2", "tape": "^2.13.3", "testling": "^1.7.0", "uglify-js": "^2.4.14"}, "gitHead": "4e193660e644b3fcd23eb9404dafc4a143b07fa2", "_id": "tweetnacl@0.10.0", "_shasum": "4d202f18aa36f1290e09a56150776072310aeec2", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "dchest", "email": "<EMAIL>"}, "maintainers": [{"name": "dchest", "email": "<EMAIL>"}], "dist": {"shasum": "4d202f18aa36f1290e09a56150776072310aeec2", "tarball": "https://registry.npmjs.org/tweetnacl/-/tweetnacl-0.10.0.tgz", "integrity": "sha512-qBW2B4bsqa1cGWQZGiIDT8SP5/U8Qn1XRAMArRQ7oiOCz7kk5pLInVRZIcvNdy4HA6V0g+p8EAK1HD+XfBqvsw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDpbSTLhRrxvfJjoHGxUqcRr8F3cxO682N1PIkJdQR6egIhALK2yHMcwQMt6NRsd2l//mpY1CbVQGQryNozfAN8X0i+"}]}}, "0.10.1": {"name": "tweetnacl", "version": "0.10.1", "description": "Port of TweetNaCl cryptographic library to JavaScript", "main": "nacl.js", "directories": {"test": "test"}, "scripts": {"build": "uglifyjs nacl.js -c -m -o nacl.min.js && uglifyjs nacl-fast.js -c -m -o nacl-fast.min.js", "test": "tape test/*.js | faucet", "testall": "make -C test/c && tape test/*.js test/c/*.js | faucet", "browser": "browserify test/browser/init.js test/*.js | uglifyjs -c -m -o test/browser/_bundle.js 2>/dev/null", "browser-quick": "browserify test/browser/init.js test/*.quick.js | uglifyjs -c -m -o test/browser/_bundle-quick.js 2>/dev/null", "testling": "browserify test/browser/testling_init.js test/*.js | testling | faucet", "firefox": "browserify test/browser/testling_init.js test/*.js | testling -x firefox | faucet", "chrome": "browserify test/browser/testling_init.js test/*.js | testling -x google-chrome | faucet", "bench": "node test/benchmark/bench.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/11..latest", "chrome/22..latest", "firefox/16..latest", "safari/latest", "opera/11.0..latest", "iphone/6..latest", "ipad/6..latest", "android-browser/latest"]}, "repository": {"type": "git", "url": "https://github.com/dchest/tweetnacl-js.git"}, "keywords": ["crypto", "cryptography", "curve25519", "ed25519", "encrypt", "hash", "key", "nacl", "poly1305", "public", "salsa20", "signatures"], "author": {"name": "TweetNaCl-js contributors"}, "license": "Public domain", "bugs": {"url": "https://github.com/dchest/tweetnacl-js/issues"}, "homepage": "https://dchest.github.io/tweetnacl-js", "devDependencies": {"browserify": "^4.2.0", "faucet": "0.0.1", "tap-browser-color": "^0.1.2", "tape": "^2.13.3", "testling": "^1.7.0", "uglify-js": "^2.4.14"}, "gitHead": "decc137b6c651bb4123fddf8c8afe34e86bb3701", "_id": "tweetnacl@0.10.1", "_shasum": "d0cb2f3d62977b1b9a9ec4060644e99419644d6d", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "dchest", "email": "<EMAIL>"}, "maintainers": [{"name": "dchest", "email": "<EMAIL>"}], "dist": {"shasum": "d0cb2f3d62977b1b9a9ec4060644e99419644d6d", "tarball": "https://registry.npmjs.org/tweetnacl/-/tweetnacl-0.10.1.tgz", "integrity": "sha512-crt/YvtJXoS8uZMDOUB9fBtYvqa4hBlq+rvjugqTupZM0WwML73rubhURGze2MJPyIxIoXvx83nhbw0ybHorkw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCU0GgHn7QjXk0UO0+Uf/Kv1KXXKmlr5k1Bd2eCcAU6lgIhAJPEi+xhK+6qEVnFflCc1g0VWcMD1WJljW5GTWmd2BOg"}]}}, "0.11.0": {"name": "tweetnacl", "version": "0.11.0", "description": "Port of TweetNaCl cryptographic library to JavaScript", "main": "nacl.js", "directories": {"test": "test"}, "scripts": {"build": "uglifyjs nacl.js -c -m -o nacl.min.js && uglifyjs nacl-fast.js -c -m -o nacl-fast.min.js", "test": "tape test/*.js | faucet", "testall": "make -C test/c && tape test/*.js test/c/*.js | faucet", "browser": "browserify test/browser/init.js test/*.js | uglifyjs -c -m -o test/browser/_bundle.js 2>/dev/null", "browser-quick": "browserify test/browser/init.js test/*.quick.js | uglifyjs -c -m -o test/browser/_bundle-quick.js 2>/dev/null", "testling": "browserify test/browser/testling_init.js test/*.js | testling | faucet", "firefox": "browserify test/browser/testling_init.js test/*.js | testling -x firefox | faucet", "chrome": "browserify test/browser/testling_init.js test/*.js | testling -x google-chrome | faucet", "bench": "node test/benchmark/bench.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/11..latest", "chrome/22..latest", "firefox/16..latest", "safari/latest", "opera/11.0..latest", "iphone/6..latest", "ipad/6..latest", "android-browser/latest"]}, "repository": {"type": "git", "url": "https://github.com/dchest/tweetnacl-js.git"}, "keywords": ["crypto", "cryptography", "curve25519", "ed25519", "encrypt", "hash", "key", "nacl", "poly1305", "public", "salsa20", "signatures"], "author": {"name": "TweetNaCl-js contributors"}, "license": "Public domain", "bugs": {"url": "https://github.com/dchest/tweetnacl-js/issues"}, "homepage": "https://dchest.github.io/tweetnacl-js", "devDependencies": {"browserify": "^4.2.0", "faucet": "0.0.1", "tap-browser-color": "^0.1.2", "tape": "^2.13.3", "testling": "^1.7.0", "uglify-js": "^2.4.14"}, "gitHead": "111adafd548a97d729ae32f27d709457adddb250", "_id": "tweetnacl@0.11.0", "_shasum": "80e2c0f6cd02a4cd877551647957bd66dead4d8f", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "dchest", "email": "<EMAIL>"}, "maintainers": [{"name": "dchest", "email": "<EMAIL>"}], "dist": {"shasum": "80e2c0f6cd02a4cd877551647957bd66dead4d8f", "tarball": "https://registry.npmjs.org/tweetnacl/-/tweetnacl-0.11.0.tgz", "integrity": "sha512-k1Iuvk/Dx8D+aZI0I3fRz+xRx5Xv8A9BNPyHRm1MMSJI6OOXKQRGtLMyEiGHrKPeAJdqtavkK7H6higeRsK0lA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD4M6N7whxXd9NsA24F7yb4Z7HtQfsEXbNgcbxNkwpC9AIgB5eJwIu6WicI4+rWlhW8sUaJxtBlKsepUi5wRP1g5Eg="}]}}, "0.11.1": {"name": "tweetnacl", "version": "0.11.1", "description": "Port of TweetNaCl cryptographic library to JavaScript", "main": "nacl.js", "directories": {"test": "test"}, "scripts": {"build": "uglifyjs nacl.js -c -m -o nacl.min.js && uglifyjs nacl-fast.js -c -m -o nacl-fast.min.js", "test": "tape test/*.js | faucet", "testall": "make -C test/c && tape test/*.js test/c/*.js | faucet", "browser": "browserify test/browser/init.js test/*.js | uglifyjs -c -m -o test/browser/_bundle.js 2>/dev/null", "browser-quick": "browserify test/browser/init.js test/*.quick.js | uglifyjs -c -m -o test/browser/_bundle-quick.js 2>/dev/null", "testling": "browserify test/browser/testling_init.js test/*.js | testling | faucet", "firefox": "browserify test/browser/testling_init.js test/*.js | testling -x firefox | faucet", "chrome": "browserify test/browser/testling_init.js test/*.js | testling -x google-chrome | faucet", "bench": "node test/benchmark/bench.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/11..latest", "chrome/22..latest", "firefox/16..latest", "safari/latest", "opera/11.0..latest", "iphone/6..latest", "ipad/6..latest", "android-browser/latest"]}, "repository": {"type": "git", "url": "https://github.com/dchest/tweetnacl-js.git"}, "keywords": ["crypto", "cryptography", "curve25519", "ed25519", "encrypt", "hash", "key", "nacl", "poly1305", "public", "salsa20", "signatures"], "author": {"name": "TweetNaCl-js contributors"}, "license": "Public domain", "bugs": {"url": "https://github.com/dchest/tweetnacl-js/issues"}, "homepage": "https://dchest.github.io/tweetnacl-js", "devDependencies": {"browserify": "^4.2.0", "faucet": "0.0.1", "tap-browser-color": "^0.1.2", "tape": "^2.13.3", "testling": "^1.7.0", "uglify-js": "^2.4.14"}, "gitHead": "255f22b2f91b295b47e0980d7af52db0e5c3ee03", "_id": "tweetnacl@0.11.1", "_shasum": "c79336c9e5b37858a05e541a7f94861ead96e2e9", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "dchest", "email": "<EMAIL>"}, "maintainers": [{"name": "dchest", "email": "<EMAIL>"}], "dist": {"shasum": "c79336c9e5b37858a05e541a7f94861ead96e2e9", "tarball": "https://registry.npmjs.org/tweetnacl/-/tweetnacl-0.11.1.tgz", "integrity": "sha512-4pkPxsZZKVkfMR6DlLhnUvI+ZNgZAvCQNxImYwcj3lA/QG0Qq/0VQwJYJGO876zqHQjVMzD1HTwRh7PjKxTpVQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAqmSaa5YunmHxvYZmPeMfQt6Xkpw2E62kURoCBajSm/AiBWLUqZA/EeSHGQYUlawU93gAIsWmt3ugz8c/PbQV6VoA=="}]}}, "0.11.2": {"name": "tweetnacl", "version": "0.11.2", "description": "Port of TweetNaCl cryptographic library to JavaScript", "main": "nacl.js", "directories": {"test": "test"}, "scripts": {"build": "uglifyjs nacl.js -c -m -o nacl.min.js && uglifyjs nacl-fast.js -c -m -o nacl-fast.min.js", "test": "tape test/*.js | faucet", "testall": "make -C test/c && tape test/*.js test/c/*.js | faucet", "browser": "browserify test/browser/init.js test/*.js | uglifyjs -c -m -o test/browser/_bundle.js 2>/dev/null", "browser-quick": "browserify test/browser/init.js test/*.quick.js | uglifyjs -c -m -o test/browser/_bundle-quick.js 2>/dev/null", "testling": "browserify test/browser/testling_init.js test/*.js | testling | faucet", "firefox": "browserify test/browser/testling_init.js test/*.js | testling -x firefox | faucet", "chrome": "browserify test/browser/testling_init.js test/*.js | testling -x google-chrome | faucet", "bench": "node test/benchmark/bench.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/11..latest", "chrome/22..latest", "firefox/16..latest", "safari/latest", "opera/11.0..latest", "iphone/6..latest", "ipad/6..latest", "android-browser/latest"]}, "repository": {"type": "git", "url": "https://github.com/dchest/tweetnacl-js.git"}, "keywords": ["crypto", "cryptography", "curve25519", "ed25519", "encrypt", "hash", "key", "nacl", "poly1305", "public", "salsa20", "signatures"], "author": {"name": "TweetNaCl-js contributors"}, "license": "Public domain", "bugs": {"url": "https://github.com/dchest/tweetnacl-js/issues"}, "homepage": "https://dchest.github.io/tweetnacl-js", "devDependencies": {"browserify": "^4.2.0", "faucet": "0.0.1", "tap-browser-color": "^0.1.2", "tape": "^2.13.3", "testling": "^1.7.0", "uglify-js": "^2.4.14"}, "gitHead": "d3597da76ed0141d08565ed329bcda7076fccfcd", "_id": "tweetnacl@0.11.2", "_shasum": "ed4bce52d1ddbceea3a2c504cd17d364a34f414b", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "dchest", "email": "<EMAIL>"}, "maintainers": [{"name": "dchest", "email": "<EMAIL>"}], "dist": {"shasum": "ed4bce52d1ddbceea3a2c504cd17d364a34f414b", "tarball": "https://registry.npmjs.org/tweetnacl/-/tweetnacl-0.11.2.tgz", "integrity": "sha512-o0pZa42jygNGW2Mnu9xhuOI/o0PpDIpKfTLD/nzK2c982C9U8fXwmr3J70Rcueh3aYfm56dNi+uTUZU2Ad/8Eg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC6E3uUpqlwFHZksANd4IPPvjPYk6KmHWfzCm7bGiu1GAiEA5dzIwKqXWqby5RWyRTUjWZbaDdhDBJDeIXz77e29pfc="}]}}, "0.12.0": {"name": "tweetnacl", "version": "0.12.0", "description": "Port of TweetNaCl cryptographic library to JavaScript", "main": "nacl.js", "directories": {"test": "test"}, "scripts": {"build": "uglifyjs nacl.js -c -m -o nacl.min.js && uglifyjs nacl-fast.js -c -m -o nacl-fast.min.js", "test": "tape test/*.js | faucet", "testall": "make -C test/c && tape test/*.js test/c/*.js | faucet", "browser": "browserify test/browser/init.js test/*.js | uglifyjs -c -m -o test/browser/_bundle.js 2>/dev/null", "browser-quick": "browserify test/browser/init.js test/*.quick.js | uglifyjs -c -m -o test/browser/_bundle-quick.js 2>/dev/null", "testling": "browserify test/browser/testling_init.js test/*.js | testling | faucet", "firefox": "browserify test/browser/testling_init.js test/*.js | testling -x firefox | faucet", "chrome": "browserify test/browser/testling_init.js test/*.js | testling -x google-chrome | faucet", "bench": "node test/benchmark/bench.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/11..latest", "chrome/22..latest", "firefox/16..latest", "safari/latest", "opera/11.0..latest", "iphone/6..latest", "ipad/6..latest", "android-browser/latest"]}, "repository": {"type": "git", "url": "https://github.com/dchest/tweetnacl-js.git"}, "keywords": ["crypto", "cryptography", "curve25519", "ed25519", "encrypt", "hash", "key", "nacl", "poly1305", "public", "salsa20", "signatures"], "author": {"name": "TweetNaCl-js contributors"}, "license": "Public domain", "bugs": {"url": "https://github.com/dchest/tweetnacl-js/issues"}, "homepage": "https://dchest.github.io/tweetnacl-js", "devDependencies": {"browserify": "4.x.x", "faucet": "0.x.x", "tap-browser-color": "0.x.x", "tape": "2.x.x", "testling": "1.x.x", "uglify-js": "2.x.x"}, "gitHead": "defcf332adb945e5659d34bf3ee13e2d325a6bce", "_id": "tweetnacl@0.12.0", "_shasum": "2a23aee15925799bd627fc586642d7feaea37bbf", "_from": ".", "_npmVersion": "2.0.0-beta.0", "_npmUser": {"name": "dchest", "email": "<EMAIL>"}, "maintainers": [{"name": "dchest", "email": "<EMAIL>"}], "dist": {"shasum": "2a23aee15925799bd627fc586642d7feaea37bbf", "tarball": "https://registry.npmjs.org/tweetnacl/-/tweetnacl-0.12.0.tgz", "integrity": "sha512-NWh/vV2Asa2V24qLfHSS3SRK4tz/h9jpGaWaWZPC/rVX+YF46S+YVFnbWo7KXUgMWYZhyc3YC/irXdmZZA6sqg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEb3+z9KLPrE4qSfYCJpRS99t7clGRNkvWx/ygZFCuqAAiBWdlSVE4+lDaIi0Xo2qvsgYyRbz8y7AdSW7D5nrz9tpg=="}]}}, "0.12.1": {"name": "tweetnacl", "version": "0.12.1", "description": "Port of TweetNaCl cryptographic library to JavaScript", "main": "nacl.js", "directories": {"test": "test"}, "scripts": {"build": "uglifyjs nacl.js -c -m -o nacl.min.js && uglifyjs nacl-fast.js -c -m -o nacl-fast.min.js", "test": "tape test/*.js | faucet", "testall": "make -C test/c && tape test/*.js test/c/*.js | faucet", "browser": "browserify test/browser/init.js test/*.js | uglifyjs -c -m -o test/browser/_bundle.js 2>/dev/null", "browser-quick": "browserify test/browser/init.js test/*.quick.js | uglifyjs -c -m -o test/browser/_bundle-quick.js 2>/dev/null", "testling": "browserify test/browser/testling_init.js test/*.js | testling | faucet", "firefox": "browserify test/browser/testling_init.js test/*.js | testling -x firefox | faucet", "chrome": "browserify test/browser/testling_init.js test/*.js | testling -x google-chrome | faucet", "bench": "node test/benchmark/bench.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/11..latest", "chrome/22..latest", "firefox/16..latest", "safari/latest", "opera/11.0..latest", "iphone/6..latest", "ipad/6..latest", "android-browser/latest"]}, "repository": {"type": "git", "url": "https://github.com/dchest/tweetnacl-js.git"}, "keywords": ["crypto", "cryptography", "curve25519", "ed25519", "encrypt", "hash", "key", "nacl", "poly1305", "public", "salsa20", "signatures"], "author": {"name": "TweetNaCl-js contributors"}, "license": "Public domain", "bugs": {"url": "https://github.com/dchest/tweetnacl-js/issues"}, "homepage": "https://dchest.github.io/tweetnacl-js", "devDependencies": {"browserify": "4.x.x", "faucet": "0.x.x", "tap-browser-color": "0.x.x", "tape": "2.x.x", "testling": "1.x.x", "uglify-js": "2.x.x"}, "gitHead": "a811228a7c40929a76ff17fc4a8da39bcc064dd6", "_id": "tweetnacl@0.12.1", "_shasum": "11231bac11b56f88e2dd594f283ef14d47b943e0", "_from": ".", "_npmVersion": "2.0.0-beta.0", "_npmUser": {"name": "dchest", "email": "<EMAIL>"}, "maintainers": [{"name": "dchest", "email": "<EMAIL>"}], "dist": {"shasum": "11231bac11b56f88e2dd594f283ef14d47b943e0", "tarball": "https://registry.npmjs.org/tweetnacl/-/tweetnacl-0.12.1.tgz", "integrity": "sha512-An9aguC+043ncSoir3Igo7lGvpWD7SuLQqHmuNiwp2wMQus8ha9s8f7XOxFEocbmk6bPM4/J7lilzF7f0DkYjA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF2qOiEFcNAxwYc2Wwco4dDbBerDpbo1H0ARLOMlgZpEAiEAhCYFk8GFinoy4aGquO9YgGA5WKXoWJ5HwsyHBUctbyg="}]}}, "0.12.2": {"name": "tweetnacl", "version": "0.12.2", "description": "Port of TweetNaCl cryptographic library to JavaScript", "main": "nacl.js", "directories": {"test": "test"}, "scripts": {"build": "uglifyjs nacl.js -c -m -o nacl.min.js && uglifyjs nacl-fast.js -c -m -o nacl-fast.min.js", "test": "tape test/*.js | faucet", "testall": "make -C test/c && tape test/*.js test/c/*.js | faucet", "browser": "browserify test/browser/init.js test/*.js | uglifyjs -c -m -o test/browser/_bundle.js 2>/dev/null", "browser-quick": "browserify test/browser/init.js test/*.quick.js | uglifyjs -c -m -o test/browser/_bundle-quick.js 2>/dev/null", "testling": "browserify test/browser/testling_init.js test/*.js | testling | faucet", "firefox": "browserify test/browser/testling_init.js test/*.js | testling -x firefox | faucet", "chrome": "browserify test/browser/testling_init.js test/*.js | testling -x google-chrome | faucet", "bench": "node test/benchmark/bench.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/11..latest", "chrome/22..latest", "firefox/16..latest", "safari/latest", "opera/11.0..latest", "iphone/6..latest", "ipad/6..latest", "android-browser/latest"]}, "repository": {"type": "git", "url": "https://github.com/dchest/tweetnacl-js.git"}, "keywords": ["crypto", "cryptography", "curve25519", "ed25519", "encrypt", "hash", "key", "nacl", "poly1305", "public", "salsa20", "signatures"], "author": {"name": "TweetNaCl-js contributors"}, "license": "Public domain", "bugs": {"url": "https://github.com/dchest/tweetnacl-js/issues"}, "homepage": "https://dchest.github.io/tweetnacl-js", "devDependencies": {"browserify": "4.x.x", "faucet": "0.x.x", "tap-browser-color": "0.x.x", "tape": "2.x.x", "testling": "1.x.x", "uglify-js": "2.x.x"}, "gitHead": "8a21381d696acdc4e99c9f706f1ad23285795f79", "_id": "tweetnacl@0.12.2", "_shasum": "bd59f890507856fb0a1136acc3a8b44547e29ddb", "_from": ".", "_npmVersion": "2.0.0-beta.0", "_npmUser": {"name": "dchest", "email": "<EMAIL>"}, "maintainers": [{"name": "dchest", "email": "<EMAIL>"}], "dist": {"shasum": "bd59f890507856fb0a1136acc3a8b44547e29ddb", "tarball": "https://registry.npmjs.org/tweetnacl/-/tweetnacl-0.12.2.tgz", "integrity": "sha512-daw2PNhLZNN5sqvT7NfRcI2uH25gHpbaHxzWJMrF8IiWRW+RkFnD3tr3N5F2tXw1vj7VHI54Hyed5WKgU6I58g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDPxnzqgUzHkkSiuoenviWDJe48hUrTD9fZQSph6TEIUwIhAPvaNpBmUHf0B7WwtTcqmrB6mhcvBUHeRmUeaAb9gYeA"}]}}, "0.13.0": {"name": "tweetnacl", "version": "0.13.0", "description": "Port of TweetNaCl cryptographic library to JavaScript", "main": "nacl-fast.js", "directories": {"test": "test"}, "scripts": {"build": "uglifyjs nacl.js -c -m -o nacl.min.js && uglifyjs nacl-fast.js -c -m -o nacl-fast.min.js", "test": "tape test/*.js | faucet", "testall": "make -C test/c && tape test/*.js test/c/*.js | faucet", "browser": "browserify test/browser/init.js test/*.js | uglifyjs -c -m -o test/browser/_bundle.js 2>/dev/null", "browser-quick": "browserify test/browser/init.js test/*.quick.js | uglifyjs -c -m -o test/browser/_bundle-quick.js 2>/dev/null", "testling": "browserify test/browser/testling_init.js test/*.js | testling | faucet", "firefox": "browserify test/browser/testling_init.js test/*.js | testling -x firefox | faucet", "chrome": "browserify test/browser/testling_init.js test/*.js | testling -x google-chrome | faucet", "bench": "node test/benchmark/bench.js"}, "testling": {"files": "test/*.js", "browsers": ["chrome/22..latest", "firefox/16..latest", "safari/latest", "opera/11.0..latest", "iphone/6..latest", "ipad/6..latest", "android-browser/latest"]}, "repository": {"type": "git", "url": "https://github.com/dchest/tweetnacl-js.git"}, "keywords": ["crypto", "cryptography", "curve25519", "ed25519", "encrypt", "hash", "key", "nacl", "poly1305", "public", "salsa20", "signatures"], "author": {"name": "TweetNaCl-js contributors"}, "license": "Public domain", "bugs": {"url": "https://github.com/dchest/tweetnacl-js/issues"}, "homepage": "https://dchest.github.io/tweetnacl-js", "devDependencies": {"browserify": "4.x.x", "faucet": "0.x.x", "tap-browser-color": "0.x.x", "tape": "2.x.x", "testling": "1.x.x", "uglify-js": "2.x.x"}, "gitHead": "b40db2cad07ceb9285ca616afc84318b9b751770", "_id": "tweetnacl@0.13.0", "_shasum": "884ce855c8f2bb03bfcbe9c7e82d854229fbee67", "_from": ".", "_npmVersion": "2.1.14", "_nodeVersion": "0.10.35", "_npmUser": {"name": "dchest", "email": "<EMAIL>"}, "maintainers": [{"name": "dchest", "email": "<EMAIL>"}], "dist": {"shasum": "884ce855c8f2bb03bfcbe9c7e82d854229fbee67", "tarball": "https://registry.npmjs.org/tweetnacl/-/tweetnacl-0.13.0.tgz", "integrity": "sha512-lssn34Avxz7qAK8ykc9sW/dCFyic/Ts2mKcrykO5g6j+3/096qdzreypQLzVCUMxrZeJPWWe4tLZZufilge+9w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGSU1mBk7gh0WTnTV3Nb5wQR+snBuqNxrnmUkp9or1ISAiEAsGnuJ7RlF3xW2rvMlG2MTTIvCB+cMy6q/YZDH0dSIuM="}]}}, "0.13.1": {"name": "tweetnacl", "version": "0.13.1", "description": "Port of TweetNaCl cryptographic library to JavaScript", "main": "nacl-fast.js", "directories": {"test": "test"}, "scripts": {"build": "uglifyjs nacl.js -c -m -o nacl.min.js && uglifyjs nacl-fast.js -c -m -o nacl-fast.min.js", "test": "tape test/*.js | faucet", "testall": "make -C test/c && tape test/*.js test/c/*.js | faucet", "browser": "browserify test/browser/init.js test/*.js | uglifyjs -c -m -o test/browser/_bundle.js 2>/dev/null", "browser-quick": "browserify test/browser/init.js test/*.quick.js | uglifyjs -c -m -o test/browser/_bundle-quick.js 2>/dev/null", "testling": "browserify test/browser/testling_init.js test/*.js | testling | faucet", "firefox": "browserify test/browser/testling_init.js test/*.js | testling -x firefox | faucet", "chrome": "browserify test/browser/testling_init.js test/*.js | testling -x google-chrome | faucet", "bench": "node test/benchmark/bench.js"}, "testling": {"files": "test/*.js", "browsers": ["chrome/22..latest", "firefox/16..latest", "safari/latest", "opera/11.0..latest", "iphone/6..latest", "ipad/6..latest", "android-browser/latest"]}, "repository": {"type": "git", "url": "https://github.com/dchest/tweetnacl-js.git"}, "keywords": ["crypto", "cryptography", "curve25519", "ed25519", "encrypt", "hash", "key", "nacl", "poly1305", "public", "salsa20", "signatures"], "author": {"name": "TweetNaCl-js contributors"}, "license": "Public domain", "bugs": {"url": "https://github.com/dchest/tweetnacl-js/issues"}, "homepage": "https://dchest.github.io/tweetnacl-js", "devDependencies": {"browserify": "4.x.x", "faucet": "0.x.x", "tap-browser-color": "0.x.x", "tape": "2.x.x", "testling": "1.x.x", "uglify-js": "2.x.x"}, "browser": {"buffer": false, "crypto": false}, "gitHead": "abfbce7c68c8ad0b3b8a90b769e1f67885237aac", "_id": "tweetnacl@0.13.1", "_shasum": "568d436b13aeb7e32adba6e16df9173008a581ad", "_from": ".", "_npmVersion": "2.7.6", "_nodeVersion": "0.12.1", "_npmUser": {"name": "dchest", "email": "<EMAIL>"}, "dist": {"shasum": "568d436b13aeb7e32adba6e16df9173008a581ad", "tarball": "https://registry.npmjs.org/tweetnacl/-/tweetnacl-0.13.1.tgz", "integrity": "sha512-ttOOE2Rj8zynAlplICrHt4p/w7HB5SyKTn2x/Fr+9bBef0HD1yZK1lnyeWDX8ltHbq6y/tFj351s5ahvUxtufg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEKxnbEE/57HK1QBXxiSouMVTlpMGnQZyX03FJ45ZU6nAiAlgLNGVv3Q5TwQ5eNOiWb2XrJdUbYoskvWjt7/gF9dwg=="}]}, "maintainers": [{"name": "dchest", "email": "<EMAIL>"}]}, "0.13.2": {"name": "tweetnacl", "version": "0.13.2", "description": "Port of TweetNaCl cryptographic library to JavaScript", "main": "nacl-fast.js", "directories": {"test": "test"}, "scripts": {"build": "uglifyjs nacl.js -c -m -o nacl.min.js && uglifyjs nacl-fast.js -c -m -o nacl-fast.min.js", "test": "tape test/*.js | faucet", "testall": "make -C test/c && tape test/*.js test/c/*.js | faucet", "browser": "browserify test/browser/init.js test/*.js | uglifyjs -c -m -o test/browser/_bundle.js 2>/dev/null", "browser-quick": "browserify test/browser/init.js test/*.quick.js | uglifyjs -c -m -o test/browser/_bundle-quick.js 2>/dev/null", "testling": "browserify test/browser/testling_init.js test/*.js | testling | faucet", "firefox": "browserify test/browser/testling_init.js test/*.js | testling -x firefox | faucet", "chrome": "browserify test/browser/testling_init.js test/*.js | testling -x google-chrome | faucet", "bench": "node test/benchmark/bench.js", "lint": "eslint nacl.js nacl-fast.js test/*.js test/benchmark/*.js"}, "testling": {"files": "test/*.js", "browsers": ["chrome/22..latest", "firefox/16..latest", "safari/latest", "opera/11.0..latest", "iphone/6..latest", "ipad/6..latest", "android-browser/latest"]}, "repository": {"type": "git", "url": "git+https://github.com/dchest/tweetnacl-js.git"}, "keywords": ["crypto", "cryptography", "curve25519", "ed25519", "encrypt", "hash", "key", "nacl", "poly1305", "public", "salsa20", "signatures"], "author": {"name": "TweetNaCl-js contributors"}, "license": "CC0-1.0", "bugs": {"url": "https://github.com/dchest/tweetnacl-js/issues"}, "homepage": "https://dchest.github.io/tweetnacl-js", "devDependencies": {"browserify": "^10.1.3", "eslint": "^1.4.3", "faucet": "0.0.1", "tap-browser-color": "^0.1.2", "tape": "^4.0.0", "testling": "^1.7.1", "uglify-js": "^2.4.21"}, "browser": {"buffer": false, "crypto": false}, "gitHead": "f093f0c34fbab5caf51f3ed9a48149c37041abb3", "_id": "tweetnacl@0.13.2", "_shasum": "453161770469d45cd266c36404e2bc99a8fa9944", "_from": ".", "_npmVersion": "2.12.1", "_nodeVersion": "0.12.7", "_npmUser": {"name": "dchest", "email": "<EMAIL>"}, "dist": {"shasum": "453161770469d45cd266c36404e2bc99a8fa9944", "tarball": "https://registry.npmjs.org/tweetnacl/-/tweetnacl-0.13.2.tgz", "integrity": "sha512-0GNqTnmku6EA6waS9inyln37bhdSJFGZsusHB4OEN+qew6uAwqx9fQEujTzM1CIQUWJlAESNlEtqdJdl2sa13Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHaVf3Z2dVYsLcpTg7mJXFi9PQ9WIBHWEW4yrT0bhh76AiEA9Z0NpdefJR+24fLBgNW8jvKKVXeXpBvqFqck4C/XLS4="}]}, "maintainers": [{"name": "dchest", "email": "<EMAIL>"}]}, "0.13.3": {"name": "tweetnacl", "version": "0.13.3", "description": "Port of TweetNaCl cryptographic library to JavaScript", "main": "nacl-fast.js", "directories": {"test": "test"}, "scripts": {"build": "uglifyjs nacl.js -c -m -o nacl.min.js && uglifyjs nacl-fast.js -c -m -o nacl-fast.min.js", "test": "tape test/*.js | faucet", "testall": "make -C test/c && tape test/*.js test/c/*.js | faucet", "browser": "browserify test/browser/init.js test/*.js | uglifyjs -c -m -o test/browser/_bundle.js 2>/dev/null", "browser-quick": "browserify test/browser/init.js test/*.quick.js | uglifyjs -c -m -o test/browser/_bundle-quick.js 2>/dev/null", "testling": "browserify test/browser/testling_init.js test/*.js | testling | faucet", "firefox": "browserify test/browser/testling_init.js test/*.js | testling -x firefox | faucet", "chrome": "browserify test/browser/testling_init.js test/*.js | testling -x google-chrome | faucet", "bench": "node test/benchmark/bench.js", "lint": "eslint nacl.js nacl-fast.js test/*.js test/benchmark/*.js"}, "testling": {"files": "test/*.js", "browsers": ["chrome/22..latest", "firefox/16..latest", "safari/latest", "opera/11.0..latest", "iphone/6..latest", "ipad/6..latest", "android-browser/latest"]}, "repository": {"type": "git", "url": "git+https://github.com/dchest/tweetnacl-js.git"}, "keywords": ["crypto", "cryptography", "curve25519", "ed25519", "encrypt", "hash", "key", "nacl", "poly1305", "public", "salsa20", "signatures"], "author": {"name": "TweetNaCl-js contributors"}, "license": "Public domain", "bugs": {"url": "https://github.com/dchest/tweetnacl-js/issues"}, "homepage": "https://dchest.github.io/tweetnacl-js", "devDependencies": {"browserify": "^10.1.3", "eslint": "^1.4.3", "faucet": "0.0.1", "tap-browser-color": "^0.1.2", "tape": "^4.0.0", "testling": "^1.7.1", "uglify-js": "^2.4.21"}, "browser": {"buffer": false, "crypto": false}, "gitHead": "2bb422cb707fba4a5ec9654688564a4fb861b068", "_id": "tweetnacl@0.13.3", "_shasum": "d628b56f3bcc3d5ae74ba9d4c1a704def5ab4b56", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.3", "_npmUser": {"name": "dchest", "email": "<EMAIL>"}, "dist": {"shasum": "d628b56f3bcc3d5ae74ba9d4c1a704def5ab4b56", "tarball": "https://registry.npmjs.org/tweetnacl/-/tweetnacl-0.13.3.tgz", "integrity": "sha512-iNWodk4oBsZ03Qfw/Yvv0KB90uYrJqvL4Je7Gy4C5t/GS3sCXPRmIT1lxmId4RzvUp0XG62bcxJ2CBu/3L5DSg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGRbAOBkCDIQbCmxIDQtLeQ0mDdMFSzDVcG75BFc7RksAiBb7hE/vjmmqlZHMJgfHIlpXEnxLSW2gFGqEt17Li017w=="}]}, "maintainers": [{"name": "dchest", "email": "<EMAIL>"}]}, "0.14.0": {"name": "tweetnacl", "version": "0.14.0", "description": "Port of TweetNaCl cryptographic library to JavaScript", "main": "nacl-fast.js", "directories": {"test": "test"}, "scripts": {"build": "uglifyjs nacl.js -c -m -o nacl.min.js && uglifyjs nacl-fast.js -c -m -o nacl-fast.min.js", "test-node": "tape test/*.js | faucet", "test-node-all": "make -C test/c && tape test/*.js test/c/*.js | faucet", "test-browser": "NACL_SRC=${NACL_SRC:='nacl.min.js'} && npm run build-test-browser && cat $NACL_SRC test/browser/_bundle.js | tape-run | faucet", "build-test-browser": "browserify test/browser/init.js test/*.js | uglifyjs -c -m -o test/browser/_bundle.js 2>/dev/null && browserify test/browser/init.js test/*.quick.js | uglifyjs -c -m -o test/browser/_bundle-quick.js 2>/dev/null", "test": "npm run test-node-all && npm run test-browser", "bench": "node test/benchmark/bench.js", "lint": "eslint nacl.js nacl-fast.js test/*.js test/benchmark/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/dchest/tweetnacl-js.git"}, "keywords": ["crypto", "cryptography", "curve25519", "ed25519", "encrypt", "hash", "key", "nacl", "poly1305", "public", "salsa20", "signatures"], "author": {"name": "TweetNaCl-js contributors"}, "license": "Public domain", "bugs": {"url": "https://github.com/dchest/tweetnacl-js/issues"}, "homepage": "https://tweetnacl.js.org", "devDependencies": {"browserify": "^13.0.0", "eslint": "^2.2.0", "faucet": "^0.0.1", "tap-browser-color": "^0.1.2", "tape": "^4.4.0", "tape-run": "^2.1.3", "tweetnacl-util": "^0.13.3", "uglify-js": "^2.6.1"}, "browser": {"buffer": false, "crypto": false}, "gitHead": "eff9ede198032894b29065e486f34de8b892b428", "_id": "tweetnacl@0.14.0", "_shasum": "59e665d00089311259954eac16cfb35d1c7eb130", "_from": ".", "_npmVersion": "3.6.0", "_nodeVersion": "5.6.0", "_npmUser": {"name": "dchest", "email": "<EMAIL>"}, "dist": {"shasum": "59e665d00089311259954eac16cfb35d1c7eb130", "tarball": "https://registry.npmjs.org/tweetnacl/-/tweetnacl-0.14.0.tgz", "integrity": "sha512-N8+mQV47WUS0j6oXMMzYnteQfyotAUyPXaXlz6t80mlaZYVHz/bSTiRpkNReD949gfbLK6B/gcP6nZRXV7RRDw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEj65yMT0aaRm/A14qX7443NhkU4lSflCZ6DaDiS3MWGAiEAj9emnABk9goklS1knYJQtgpOlycI24L7mhzD8vP4Mbw="}]}, "maintainers": [{"name": "dchest", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-9-west.internal.npmjs.com", "tmp": "tmp/tweetnacl-0.14.0.tgz_1455984444410_0.7439826326444745"}}, "0.14.1": {"name": "tweetnacl", "version": "0.14.1", "description": "Port of TweetNaCl cryptographic library to JavaScript", "main": "nacl-fast.js", "directories": {"test": "test"}, "scripts": {"build": "uglifyjs nacl.js -c -m -o nacl.min.js && uglifyjs nacl-fast.js -c -m -o nacl-fast.min.js", "test-node": "tape test/*.js | faucet", "test-node-all": "make -C test/c && tape test/*.js test/c/*.js | faucet", "test-browser": "NACL_SRC=${NACL_SRC:='nacl.min.js'} && npm run build-test-browser && cat $NACL_SRC test/browser/_bundle.js | tape-run | faucet", "build-test-browser": "browserify test/browser/init.js test/*.js | uglifyjs -c -m -o test/browser/_bundle.js 2>/dev/null && browserify test/browser/init.js test/*.quick.js | uglifyjs -c -m -o test/browser/_bundle-quick.js 2>/dev/null", "test": "npm run test-node-all && npm run test-browser", "bench": "node test/benchmark/bench.js", "lint": "eslint nacl.js nacl-fast.js test/*.js test/benchmark/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/dchest/tweetnacl-js.git"}, "keywords": ["crypto", "cryptography", "curve25519", "ed25519", "encrypt", "hash", "key", "nacl", "poly1305", "public", "salsa20", "signatures"], "author": {"name": "TweetNaCl-js contributors"}, "license": "SEE LICENSE IN COPYING.txt", "bugs": {"url": "https://github.com/dchest/tweetnacl-js/issues"}, "homepage": "https://tweetnacl.js.org", "devDependencies": {"browserify": "^13.0.0", "eslint": "^2.2.0", "faucet": "^0.0.1", "tap-browser-color": "^0.1.2", "tape": "^4.4.0", "tape-run": "^2.1.3", "tweetnacl-util": "^0.13.3", "uglify-js": "^2.6.1"}, "browser": {"buffer": false, "crypto": false}, "gitHead": "e4e8b4d8c882f5de3bcfc4262ea15d86f7f482d5", "_id": "tweetnacl@0.14.1", "_shasum": "37c6a1fb5cd4b63b7acee450d8419d9c0024cc03", "_from": ".", "_npmVersion": "3.6.0", "_nodeVersion": "5.6.0", "_npmUser": {"name": "dchest", "email": "<EMAIL>"}, "dist": {"shasum": "37c6a1fb5cd4b63b7acee450d8419d9c0024cc03", "tarball": "https://registry.npmjs.org/tweetnacl/-/tweetnacl-0.14.1.tgz", "integrity": "sha512-<PERSON><PERSON><PERSON>wu8ZaZ54O1L7Ow61WcfqqqKYXruYacRkAH1GYBiXSP4Rq2PXBzP37Xh5iE6vn06/tCmo5pUZhSTW8ccUYuA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAPQRrBz8LJmA3lyy5HSuQOQJVmyLy8fHVJ1Md4cbcIeAiBkEUkL9ugvmg93wcQPpOzYvMkSLU/Kb/2H6ACnz0MnGQ=="}]}, "maintainers": [{"name": "dchest", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-9-west.internal.npmjs.com", "tmp": "tmp/tweetnacl-0.14.1.tgz_1456360522655_0.3124228978995234"}}, "0.14.2": {"name": "tweetnacl", "version": "0.14.2", "description": "Port of TweetNaCl cryptographic library to JavaScript", "main": "nacl-fast.js", "directories": {"test": "test"}, "scripts": {"build": "uglifyjs nacl.js -c -m -o nacl.min.js && uglifyjs nacl-fast.js -c -m -o nacl-fast.min.js", "test-node": "tape test/*.js | faucet", "test-node-all": "make -C test/c && tape test/*.js test/c/*.js | faucet", "test-browser": "NACL_SRC=${NACL_SRC:='nacl.min.js'} && npm run build-test-browser && cat $NACL_SRC test/browser/_bundle.js | tape-run | faucet", "build-test-browser": "browserify test/browser/init.js test/*.js | uglifyjs -c -m -o test/browser/_bundle.js 2>/dev/null && browserify test/browser/init.js test/*.quick.js | uglifyjs -c -m -o test/browser/_bundle-quick.js 2>/dev/null", "test": "npm run test-node-all && npm run test-browser", "bench": "node test/benchmark/bench.js", "lint": "eslint nacl.js nacl-fast.js test/*.js test/benchmark/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/dchest/tweetnacl-js.git"}, "keywords": ["crypto", "cryptography", "curve25519", "ed25519", "encrypt", "hash", "key", "nacl", "poly1305", "public", "salsa20", "signatures"], "author": {"name": "TweetNaCl-js contributors"}, "license": "SEE LICENSE IN COPYING.txt", "bugs": {"url": "https://github.com/dchest/tweetnacl-js/issues"}, "homepage": "https://tweetnacl.js.org", "devDependencies": {"browserify": "^13.0.0", "eslint": "^2.2.0", "faucet": "^0.0.1", "tap-browser-color": "^0.1.2", "tape": "^4.4.0", "tape-run": "^2.1.3", "tweetnacl-util": "^0.13.3", "uglify-js": "^2.6.1"}, "browser": {"buffer": false, "crypto": false}, "gitHead": "5869cd970287971fd4ebeb3e80093e382014c0ec", "_id": "tweetnacl@0.14.2", "_shasum": "79f38003a8be74757846ff6479f2152034808134", "_from": ".", "_npmVersion": "3.7.3", "_nodeVersion": "5.6.0", "_npmUser": {"name": "dchest", "email": "<EMAIL>"}, "dist": {"shasum": "79f38003a8be74757846ff6479f2152034808134", "tarball": "https://registry.npmjs.org/tweetnacl/-/tweetnacl-0.14.2.tgz", "integrity": "sha512-cS2P0drHOGRMmFRiVcG280112+oUY0yms1HQPfOl2yUXXO5o2QjjxHJCyNikr8ccLdZJqd9LJZivM+vSyxI9Kg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBCNeNkY7XvM0lWetT1AbshdhjiUEu07R4PvMrTOFJcfAiAS9Sg+ScmnPbn6HJISsCThpIwKGfPWaorqK/HL3HuVyw=="}]}, "maintainers": [{"name": "dchest", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/tweetnacl-0.14.2.tgz_1459187390463_0.6325501962564886"}}, "0.14.3": {"name": "tweetnacl", "version": "0.14.3", "description": "Port of TweetNaCl cryptographic library to JavaScript", "main": "nacl-fast.js", "directories": {"test": "test"}, "scripts": {"build": "uglifyjs nacl.js -c -m -o nacl.min.js && uglifyjs nacl-fast.js -c -m -o nacl-fast.min.js", "test-node": "tape test/*.js | faucet", "test-node-all": "make -C test/c && tape test/*.js test/c/*.js | faucet", "test-browser": "NACL_SRC=${NACL_SRC:='nacl.min.js'} && npm run build-test-browser && cat $NACL_SRC test/browser/_bundle.js | tape-run | faucet", "build-test-browser": "browserify test/browser/init.js test/*.js | uglifyjs -c -m -o test/browser/_bundle.js 2>/dev/null && browserify test/browser/init.js test/*.quick.js | uglifyjs -c -m -o test/browser/_bundle-quick.js 2>/dev/null", "test": "npm run test-node-all && npm run test-browser", "bench": "node test/benchmark/bench.js", "lint": "eslint nacl.js nacl-fast.js test/*.js test/benchmark/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/dchest/tweetnacl-js.git"}, "keywords": ["crypto", "cryptography", "curve25519", "ed25519", "encrypt", "hash", "key", "nacl", "poly1305", "public", "salsa20", "signatures"], "author": {"name": "TweetNaCl-js contributors"}, "license": "SEE LICENSE IN COPYING.txt", "bugs": {"url": "https://github.com/dchest/tweetnacl-js/issues"}, "homepage": "https://tweetnacl.js.org", "devDependencies": {"browserify": "^13.0.0", "eslint": "^2.2.0", "faucet": "^0.0.1", "tap-browser-color": "^0.1.2", "tape": "^4.4.0", "tape-run": "^2.1.3", "tweetnacl-util": "^0.13.3", "uglify-js": "^2.6.1"}, "browser": {"buffer": false, "crypto": false}, "gitHead": "3eb4fc544a2a1d6c0a41b98b9906288ca8b087e4", "_id": "tweetnacl@0.14.3", "_shasum": "3da382f670f25ded78d7b3d1792119bca0b7132d", "_from": ".", "_npmVersion": "3.7.3", "_nodeVersion": "5.6.0", "_npmUser": {"name": "dchest", "email": "<EMAIL>"}, "dist": {"shasum": "3da382f670f25ded78d7b3d1792119bca0b7132d", "tarball": "https://registry.npmjs.org/tweetnacl/-/tweetnacl-0.14.3.tgz", "integrity": "sha512-wv+W579MMQ7iwO2XcHk72RPnBwAyRFMjizSdncxpyCHgA3jzbe8VQ5yGY5HEejR3AyywFaGIo5cJRR6ENdzFIA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDIAqfZRj1n+TMiv4wZuEYf0QWZ5eW2sbwkpyhJoDu4QgIgCHueNuirXC6baR4UaMCEzUYvAqAzcq9fxGOBtzCGDRA="}]}, "maintainers": [{"name": "dchest", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/tweetnacl-0.14.3.tgz_1459224951636_0.7403244483284652"}}, "0.14.4": {"name": "tweetnacl", "version": "0.14.4", "description": "Port of TweetNaCl cryptographic library to JavaScript", "main": "nacl-fast.js", "types": "nacl.d.ts", "directories": {"test": "test"}, "scripts": {"build": "uglifyjs nacl.js -c -m -o nacl.min.js && uglifyjs nacl-fast.js -c -m -o nacl-fast.min.js", "test-node": "tape test/*.js | faucet", "test-node-all": "make -C test/c && tape test/*.js test/c/*.js | faucet", "test-browser": "NACL_SRC=${NACL_SRC:='nacl.min.js'} && npm run build-test-browser && cat $NACL_SRC test/browser/_bundle.js | tape-run | faucet", "build-test-browser": "browserify test/browser/init.js test/*.js | uglifyjs -c -m -o test/browser/_bundle.js 2>/dev/null && browserify test/browser/init.js test/*.quick.js | uglifyjs -c -m -o test/browser/_bundle-quick.js 2>/dev/null", "test": "npm run test-node-all && npm run test-browser", "bench": "node test/benchmark/bench.js", "lint": "eslint nacl.js nacl-fast.js test/*.js test/benchmark/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/dchest/tweetnacl-js.git"}, "keywords": ["crypto", "cryptography", "curve25519", "ed25519", "encrypt", "hash", "key", "nacl", "poly1305", "public", "salsa20", "signatures"], "author": {"name": "TweetNaCl-js contributors"}, "license": "SEE LICENSE IN COPYING.txt", "bugs": {"url": "https://github.com/dchest/tweetnacl-js/issues"}, "homepage": "https://tweetnacl.js.org", "devDependencies": {"browserify": "^13.0.0", "eslint": "^2.2.0", "faucet": "^0.0.1", "tap-browser-color": "^0.1.2", "tape": "^4.4.0", "tape-run": "^2.1.3", "tweetnacl-util": "^0.13.3", "uglify-js": "^2.6.1"}, "browser": {"buffer": false, "crypto": false}, "gitHead": "53024cf517ead075666dd430295bd0992dd2a48d", "_id": "tweetnacl@0.14.4", "_shasum": "8c9dbfb52795686f166cd2023794bcf103d13c2b", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "7.0.0", "_npmUser": {"name": "dchest", "email": "<EMAIL>"}, "dist": {"shasum": "8c9dbfb52795686f166cd2023794bcf103d13c2b", "tarball": "https://registry.npmjs.org/tweetnacl/-/tweetnacl-0.14.4.tgz", "integrity": "sha512-yW3BDg8BqldPyxqXj3HYWU2ZC3OF21KEyP1ISf0Q6QyCEb1/qJpU9nACEHfgn4zK40Py/yxi9vIdwu2n20CMxg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCCrHNGrl/lcGZZmY0WWVvrWiy8k4p9HQ9OL57pbu/rCQIgRcsGWwCy1+XFD7Bf+xA9fVbwZnwfHm8Wi5RM65elujk="}]}, "maintainers": [{"name": "dchest", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/tweetnacl-0.14.4.tgz_1480702221977_0.6762865034397691"}}, "0.14.5": {"name": "tweetnacl", "version": "0.14.5", "description": "Port of TweetNaCl cryptographic library to JavaScript", "main": "nacl-fast.js", "types": "nacl.d.ts", "directories": {"test": "test"}, "scripts": {"build": "uglifyjs nacl.js -c -m -o nacl.min.js && uglifyjs nacl-fast.js -c -m -o nacl-fast.min.js", "test-node": "tape test/*.js | faucet", "test-node-all": "make -C test/c && tape test/*.js test/c/*.js | faucet", "test-browser": "NACL_SRC=${NACL_SRC:='nacl.min.js'} && npm run build-test-browser && cat $NACL_SRC test/browser/_bundle.js | tape-run | faucet", "build-test-browser": "browserify test/browser/init.js test/*.js | uglifyjs -c -m -o test/browser/_bundle.js 2>/dev/null && browserify test/browser/init.js test/*.quick.js | uglifyjs -c -m -o test/browser/_bundle-quick.js 2>/dev/null", "test": "npm run test-node-all && npm run test-browser", "bench": "node test/benchmark/bench.js", "lint": "eslint nacl.js nacl-fast.js test/*.js test/benchmark/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/dchest/tweetnacl-js.git"}, "keywords": ["crypto", "cryptography", "curve25519", "ed25519", "encrypt", "hash", "key", "nacl", "poly1305", "public", "salsa20", "signatures"], "author": {"name": "TweetNaCl-js contributors"}, "license": "Unlicense", "bugs": {"url": "https://github.com/dchest/tweetnacl-js/issues"}, "homepage": "https://tweetnacl.js.org", "devDependencies": {"browserify": "^13.0.0", "eslint": "^2.2.0", "faucet": "^0.0.1", "tap-browser-color": "^0.1.2", "tape": "^4.4.0", "tape-run": "^2.1.3", "tweetnacl-util": "^0.13.3", "uglify-js": "^2.6.1"}, "browser": {"buffer": false, "crypto": false}, "gitHead": "cce829e473b1ae299a9373b5140c713ee88f577f", "_id": "tweetnacl@0.14.5", "_shasum": "5ae68177f192d4456269d108afa93ff8743f4f64", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "7.0.0", "_npmUser": {"name": "dchest", "email": "<EMAIL>"}, "dist": {"shasum": "5ae68177f192d4456269d108afa93ff8743f4f64", "tarball": "https://registry.npmjs.org/tweetnacl/-/tweetnacl-0.14.5.tgz", "integrity": "sha512-KXXFFdAbFXY4geFIwoyNK+f5Z1b7swfXABfL7HXCmoIWMKU3dmS26672A4EeQtDzLKy7SXmfBu51JolvEKwtGA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDHhdkxfW1hN14i/xMCWfhygkl+52bR+DkNVmdARtwc6wIgPxfsfj8jgtNcMyBXTnqqri0hI2MhOcZniVHZ8VDCcFk="}]}, "maintainers": [{"name": "dchest", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/tweetnacl-0.14.5.tgz_1481627515097_0.015130913350731134"}}, "1.0.0-rc.1": {"name": "tweetnacl", "version": "1.0.0-rc.1", "description": "Port of TweetNaCl cryptographic library to JavaScript", "main": "nacl-fast.js", "types": "nacl.d.ts", "directories": {"test": "test"}, "scripts": {"build": "uglifyjs nacl.js -c -m -o nacl.min.js && uglifyjs nacl-fast.js -c -m -o nacl-fast.min.js", "test-node": "tape test/*.js | faucet", "test-node-all": "make -C test/c && tape test/*.js test/c/*.js | faucet", "test-browser": "NACL_SRC=${NACL_SRC:='nacl.min.js'} && npm run build-test-browser && cat $NACL_SRC test/browser/_bundle.js | tape-run | faucet", "build-test-browser": "browserify test/browser/init.js test/*.js | uglifyjs -c -m -o test/browser/_bundle.js 2>/dev/null && browserify test/browser/init.js test/*.quick.js | uglifyjs -c -m -o test/browser/_bundle-quick.js 2>/dev/null", "test": "npm run test-node-all && npm run test-browser", "bench": "node test/benchmark/bench.js", "lint": "eslint nacl.js nacl-fast.js test/*.js test/benchmark/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/dchest/tweetnacl-js.git"}, "keywords": ["crypto", "cryptography", "curve25519", "ed25519", "encrypt", "hash", "key", "nacl", "poly1305", "public", "salsa20", "signatures"], "author": {"name": "TweetNaCl-js contributors"}, "license": "Unlicense", "bugs": {"url": "https://github.com/dchest/tweetnacl-js/issues"}, "homepage": "https://tweetnacl.js.org", "devDependencies": {"browserify": "^13.1.1", "electron": "^1.4.12", "eslint": "^3.12.1", "faucet": "^0.0.1", "tap-browser-color": "^0.1.2", "tape": "^4.6.3", "tape-run": "^2.1.4", "tweetnacl-util": "^0.13.5", "uglify-js": "^2.7.5"}, "browser": {"buffer": false, "crypto": false}, "gitHead": "88b8ea49b771f15d9e447bfc3eaba260bed2daff", "_id": "tweetnacl@1.0.0-rc.1", "_shasum": "1198f1dfccb24859a4091c82787d5882425f0cde", "_from": ".", "_npmVersion": "4.0.5", "_nodeVersion": "7.4.0", "_npmUser": {"name": "dchest", "email": "<EMAIL>"}, "dist": {"shasum": "1198f1dfccb24859a4091c82787d5882425f0cde", "tarball": "https://registry.npmjs.org/tweetnacl/-/tweetnacl-1.0.0-rc.1.tgz", "integrity": "sha512-XpmupDGsLWOcAFjfNRm8wyYGQOhWR0R42X+VMMMqB6g1V/heXvP4NQfvBkK6XTlQp8KMvcOo7xCTDTQXtJmAWg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAUBb5lQzA5/gqkyG/d2fZaL1OBWMOWBGwUNA4USk04mAiBjVGsIXzyo51FWy/rZHJXraeFV6DSwHdLhOC88446MAw=="}]}, "maintainers": [{"name": "dchest", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/tweetnacl-1.0.0-rc.1.tgz_1487763567946_0.9748900488484651"}}, "1.0.0": {"name": "tweetnacl", "version": "1.0.0", "description": "Port of TweetNaCl cryptographic library to JavaScript", "main": "nacl-fast.js", "types": "nacl.d.ts", "directories": {"test": "test"}, "scripts": {"build": "uglifyjs nacl.js -c -m -o nacl.min.js && uglifyjs nacl-fast.js -c -m -o nacl-fast.min.js", "test-node": "tape test/*.js | faucet", "test-node-all": "make -C test/c && tape test/*.js test/c/*.js | faucet", "test-browser": "NACL_SRC=${NACL_SRC:='nacl.min.js'} && npm run build-test-browser && cat $NACL_SRC test/browser/_bundle.js | tape-run | faucet", "build-test-browser": "browserify test/browser/init.js test/*.js | uglifyjs -c -m -o test/browser/_bundle.js 2>/dev/null && browserify test/browser/init.js test/*.quick.js | uglifyjs -c -m -o test/browser/_bundle-quick.js 2>/dev/null", "test": "npm run test-node-all && npm run test-browser", "bench": "node test/benchmark/bench.js", "lint": "eslint nacl.js nacl-fast.js test/*.js test/benchmark/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/dchest/tweetnacl-js.git"}, "keywords": ["crypto", "cryptography", "curve25519", "ed25519", "encrypt", "hash", "key", "nacl", "poly1305", "public", "salsa20", "signatures"], "author": {"name": "TweetNaCl-js contributors"}, "license": "Unlicense", "bugs": {"url": "https://github.com/dchest/tweetnacl-js/issues"}, "homepage": "https://tweetnacl.js.org", "devDependencies": {"browserify": "^13.1.1", "electron": "^1.4.12", "eslint": "^3.12.1", "faucet": "^0.0.1", "tap-browser-color": "^0.1.2", "tape": "^4.6.3", "tape-run": "^2.1.4", "tweetnacl-util": "^0.13.5", "uglify-js": "^2.7.5"}, "browser": {"buffer": false, "crypto": false}, "gitHead": "988c41917199cd33a93896c52fcad29fb96833d8", "_id": "tweetnacl@1.0.0", "_shasum": "713d8b818da42068740bf68386d0479e66fc8a7b", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.7.1", "_npmUser": {"name": "dchest", "email": "<EMAIL>"}, "dist": {"shasum": "713d8b818da42068740bf68386d0479e66fc8a7b", "tarball": "https://registry.npmjs.org/tweetnacl/-/tweetnacl-1.0.0.tgz", "integrity": "sha512-Nvl4Z9G3mH2/OBrODQlNhZ2T+ZaNe3AQoBHn0IxH2Klv9J45QYUY5trS/Dsk+Psmn3kCxUNPjsLtgwrofpFASA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICVhY79wZZWqhLNjofB6XhR1N8/D7CreHXNkXhiK45+6AiBCGPWcFksm5DPNnaWWR9BoGzzkHsWuhi4wBsw2xJw26Q=="}]}, "maintainers": [{"name": "dchest", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tweetnacl-1.0.0.tgz_1495478628926_0.32991178915835917"}}, "1.0.1": {"name": "tweetnacl", "version": "1.0.1", "description": "Port of TweetNaCl cryptographic library to JavaScript", "main": "nacl-fast.js", "types": "nacl.d.ts", "directories": {"test": "test"}, "scripts": {"build": "uglifyjs nacl.js -c -m -o nacl.min.js && uglifyjs nacl-fast.js -c -m -o nacl-fast.min.js", "test-node": "tape test/*.js | faucet", "test-node-all": "make -C test/c && tape test/*.js test/c/*.js | faucet", "build-test-browser": "browserify test/browser/init.js test/*.js | uglifyjs -c -m -o test/browser/_bundle.js 2>/dev/null && browserify test/browser/init.js test/*.quick.js | uglifyjs -c -m -o test/browser/_bundle-quick.js 2>/dev/null", "test": "npm run test-node-all", "bench": "node test/benchmark/bench.js", "lint": "eslint nacl.js nacl-fast.js test/*.js test/benchmark/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/dchest/tweetnacl-js.git"}, "keywords": ["crypto", "cryptography", "curve25519", "ed25519", "encrypt", "hash", "key", "nacl", "poly1305", "public", "salsa20", "signatures"], "author": {"name": "TweetNaCl-js contributors"}, "license": "Unlicense", "bugs": {"url": "https://github.com/dchest/tweetnacl-js/issues"}, "homepage": "https://tweetnacl.js.org", "devDependencies": {"browserify": "^13.1.1", "eslint": "^3.12.1", "faucet": "^0.0.1", "tap-browser-color": "^0.1.2", "tape": "^4.6.3", "tweetnacl-util": "^0.13.5", "uglify-js": "^2.7.5"}, "browser": {"buffer": false, "crypto": false}, "gitHead": "acab4d4883e7a0be0b230df7b42c0bbd25210d39", "_id": "tweetnacl@1.0.1", "_npmVersion": "6.5.0", "_nodeVersion": "11.6.0", "_npmUser": {"name": "dchest", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-kcoMoKTPYnoeS50tzoqjPY3Uv9axeuuFAZY9M/9zFnhoVvRfxz9K29IMPD7jGmt2c8SW7i3gT9WqDl2+nV7p4A==", "shasum": "2594d42da73cd036bd0d2a54683dd35a6b55ca17", "tarball": "https://registry.npmjs.org/tweetnacl/-/tweetnacl-1.0.1.tgz", "fileCount": 11, "unpackedSize": 174133, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcSdE1CRA9TVsSAnZWagAAMd4P/jpUtuLLm3oh5982OTgS\nAKBPCOMPJ1ueiNwFIgvfHTpt1mSRg5NFnVZELLkks9apvaiuGUbL47DXIacn\nA+EHYf2uhmWbQltjQvxb5APr8C4VDJDyjdu9ixAmoN3mqSYgva1BEuxz/4LN\nvnABgNxO6jNiM3pZbJ2LRDbe1N0Bnjjv7mYbcmfAK8ittPkEXiXMbKyZBLay\nhZal8Ih+oZP1MhyyBrjLJvTpJu2WrT48gUUUZk9gXhMtFL4QaViufOW3CLy3\nOSlug2DYTY9CkouUxaLibYQN+fTIATinJoq0NDXg+97SKEplS1I/J9Atv54W\nvt5ZYt8goUAg+tpyC8zYHXNZp5XPLqT3aVbmTn6xMYUtCVkW+IeqwUQpT+Y3\nYwraC0qVVTWEp5tEmXI07+FJAH6aF8dJ8hyT4AxtL0ndoNOo6iyhPpp2+LYH\nUgY1orXVbxAr5Roz5wk4ej10SvqjFVrETJeR6Avtm6d9bkytLsrJr6N8Q2MK\n2xy7Ik1PA1S7yNkCVhDn9AocdhmcAXRWtV2qib4ySqtQ6vevJtKndJOVMFlP\nue4qSieg2vH1S9K+qwzLSil8Y3gMWrXpmaQ2YmR6S1uxfA1aHDRFHHHt5WFQ\n+AtJe9l5Q/N7FTw+w3YJvOmVLl+RZRKicKlZOMMwEwWFKKrbHFEQnW06A1TW\nAyz5\r\n=dIRt\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGa++eVgGLtCz/lsJSZ7X7p8cQnhVQoivQRHOnKx+4nNAiEAkmUpZuSjZANfK+cVoH+tRqeifUH4FWT25hkaRYOSP2E="}]}, "maintainers": [{"name": "dchest", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tweetnacl_1.0.1_1548341556828_0.49747684050344443"}, "_hasShrinkwrap": false}, "1.0.2": {"name": "tweetnacl", "version": "1.0.2", "description": "Port of TweetNaCl cryptographic library to JavaScript", "main": "nacl-fast.js", "types": "nacl.d.ts", "directories": {"test": "test"}, "scripts": {"build": "uglifyjs nacl.js -c -m -o nacl.min.js && uglifyjs nacl-fast.js -c -m -o nacl-fast.min.js", "test-node": "tape test/*.js | faucet", "test-node-all": "make -C test/c && tape test/*.js test/c/*.js | faucet", "build-test-browser": "browserify test/browser/init.js test/*.js | uglifyjs -c -m -o test/browser/_bundle.js 2>/dev/null && browserify test/browser/init.js test/*.quick.js | uglifyjs -c -m -o test/browser/_bundle-quick.js 2>/dev/null", "test": "npm run test-node-all", "bench": "node test/benchmark/bench.js", "lint": "eslint nacl.js nacl-fast.js test/*.js test/benchmark/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/dchest/tweetnacl-js.git"}, "keywords": ["crypto", "cryptography", "curve25519", "ed25519", "encrypt", "hash", "key", "nacl", "poly1305", "public", "salsa20", "signatures"], "author": {"name": "TweetNaCl-js contributors"}, "license": "Unlicense", "bugs": {"url": "https://github.com/dchest/tweetnacl-js/issues"}, "homepage": "https://tweetnacl.js.org", "devDependencies": {"browserify": "^16.2.3", "eslint": "^6.8.0", "faucet": "^0.0.1", "tap-browser-color": "^0.1.2", "tape": "^4.13.0", "tweetnacl-util": "^0.15.0", "uglify-js": "^3.7.5"}, "browser": {"buffer": false, "crypto": false}, "gitHead": "73a94a24306d7a195b4c756e0ccf1c89c742f64f", "_id": "tweetnacl@1.0.2", "_nodeVersion": "13.6.0", "_npmVersion": "6.13.4", "_npmUser": {"name": "dchest", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-+8aPRjmXgf1VqvyxSlBUzKzeYqVS9Ai8vZ28g+mL7dNQl1jlUTCMDZnvNQdAS1xTywMkIXwJsfipsR/6s2+syw==", "shasum": "39fba8d1f7a34706a3fc1fd8c04afcad7bb9e689", "tarball": "https://registry.npmjs.org/tweetnacl/-/tweetnacl-1.0.2.tgz", "fileCount": 11, "unpackedSize": 174513, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeILl5CRA9TVsSAnZWagAAtXwQAIzLkk1dQ17wF7XbKkaK\net9jGjgZ+GQIOaWKUhgIHLTTEZvL+xsxZt2yuzdtVRkmNYDxEmzTvXIlpOmM\nPkyc7SDj4NhQ72L9XG61I4A+SZZQEFzm+CFSDwzPSnUA2+LFp9t3TUSSUpma\nMItZk3dhRpE4ruLAGckYSOGlFs9wbKLTZuPHo3Z60klIxCILWVlFSh0P+vs0\nbXEqhWFp5rsVDiVoGI31rE65fSA1KM07Y0noNW5tDcsMl/9MMjYS2F0PrwWj\n0K/sdmwPGCH9gGQCPWNeqdtqaHobOiNCVPVRnmy7f1EZoFQufJoYDPHK/KA1\nGkrXzj0PHm+ehTbCBNlDTRfXd/ty8QFj1tfkwmlBk2jCQJihQgtIpSOfyrkd\n1t71Sf5S3FzHSQhSCkbjr7gq7R+KBftdZYnoJ9n5+fcUqwQOjBXHc9xn1rr8\nNYvJfv9bDQApIA5m2KBhymot8AmqoLvxjhydFiRDNf7ytrg9BrrczVCaaRe5\nzRXOG7lOzOWR/ezLYKqep3AryI99ilddUJrVVU86B4vkaXlD5PkQIqYsXIcA\n7JIYu0DmKMPeSLUxM3KVwzy4l9NisBI0sgnq/HaapQWN0PUnjevwp5RA/vKA\nGNKEfJAl3QuOOMV7YOUYKvhHq7k/91iYnTGbG/yo+yXuWQOrVUkxB8JmVaNR\nErNf\r\n=6YoX\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDiq8TCYn3ZQwwHYGW29cf5Kkn2bt6F5hhr5979CNOgjQIhAPmvVaa/OfotYvPObOxuclpKlGx5iIIZ59NxJdIdaEyG"}]}, "maintainers": [{"name": "dchest", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tweetnacl_1.0.2_1579202936724_0.9972577727428134"}, "_hasShrinkwrap": false}, "1.0.3": {"name": "tweetnacl", "version": "1.0.3", "description": "Port of TweetNaCl cryptographic library to JavaScript", "main": "nacl-fast.js", "types": "nacl.d.ts", "directories": {"test": "test"}, "scripts": {"build": "uglifyjs nacl.js -c -m -o nacl.min.js && uglifyjs nacl-fast.js -c -m -o nacl-fast.min.js", "test-node": "tape test/*.js | faucet", "test-node-all": "make -C test/c && tape test/*.js test/c/*.js | faucet", "build-test-browser": "browserify test/browser/init.js test/*.js | uglifyjs -c -m -o test/browser/_bundle.js 2>/dev/null && browserify test/browser/init.js test/*.quick.js | uglifyjs -c -m -o test/browser/_bundle-quick.js 2>/dev/null", "test": "npm run test-node-all", "bench": "node test/benchmark/bench.js", "lint": "eslint nacl.js nacl-fast.js test/*.js test/benchmark/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/dchest/tweetnacl-js.git"}, "keywords": ["crypto", "cryptography", "curve25519", "ed25519", "encrypt", "hash", "key", "nacl", "poly1305", "public", "salsa20", "signatures"], "author": {"name": "TweetNaCl-js contributors"}, "license": "Unlicense", "bugs": {"url": "https://github.com/dchest/tweetnacl-js/issues"}, "homepage": "https://tweetnacl.js.org", "devDependencies": {"browserify": "^16.2.3", "eslint": "^6.8.0", "faucet": "^0.0.1", "tap-browser-color": "^0.1.2", "tape": "^4.13.0", "tweetnacl-util": "^0.15.0", "uglify-js": "^3.7.5"}, "browser": {"buffer": false, "crypto": false}, "gitHead": "5bf1ff5fa15e89ae249401b0d5aa54c5c5955041", "_id": "tweetnacl@1.0.3", "_nodeVersion": "13.8.0", "_npmVersion": "6.13.7", "_npmUser": {"name": "dchest", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-6rt+RN7aOi1nGMyC4Xa5DdYiukl2UWCbcJft7YhxReBGQD7OAM8Pbxw6YMo4r2diNEA8FEmu32YOn9rhaiE5yw==", "shasum": "ac0af71680458d8a6378d0d0d050ab1407d35596", "tarball": "https://registry.npmjs.org/tweetnacl/-/tweetnacl-1.0.3.tgz", "fileCount": 11, "unpackedSize": 174912, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeQbOyCRA9TVsSAnZWagAAzkUP/2YF9RSZ+Z1ybmfA4rtG\nCbDALyjdbYfsja/u90GhcXRVL0b2q5kQMUBpRjhkqUG5l8OisnmugsyBeYn6\n6HQje44Kbtf1cB9ZC2Wv/du1s5wu+TPGRTopL5eUrQcAC0i9pI7g+DGYqJvq\nIL2MjIMzWJWSNgYhgiZ/3+Jn6CqQLZwsZzTnNTF4zBuYX62U6V0ByhWmc/Le\nz/aBKlW0nlphH6yTW2ayP2JPPoJGmIzPAetd4LfmlZp2gQZMejjJUWX9SWBW\nTB++QrDNGMJ7aEEN/2+O3CD5+UXH8cFIzgBPgF1MWcUCWJsRpULKz+55yA0x\n/SN9nWzwo8LQCdPRmxYUpOS4wLZYH6zCUwwTq66BhBG1SklnB2fWH+D9MSvm\nNH242Y3nU4uokxQo0mJgdBszBZQJT98FpvCM/zzU44WjeGPwXoYhoYw/FbYo\n78zou/vA6EV8VeGhVEBU7njwr4/MtgCNuD9hpOfp+e/Fhk80Do9v/1ZJ70de\n0R423mABDo67sHQ7zV+bCNRfu675GWgLCQnUCf+czwxFreDb5f5BQWrDCCTz\nZMRJTrgwmGSOrjppWSji51J0Zx4Qu3wAFmfN6nZD4jgnSvvZdkiUHhLxGLfO\n522xA9hSPqQiWsKAyTQ/cvRyd9kabjqV45YBIFEBU3MTb4vvtKk59Zay/Qy+\ngELA\r\n=D+R+\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHawD1xgYWQsk1QfWs8/CS2AL/8v9okSMlqK5x/Jrpm1AiAO64ieSTtN9pGcYYlLmKGw5naX/t0TIujwMeWir8uT2Q=="}]}, "maintainers": [{"name": "dchest", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tweetnacl_1.0.3_1581364145675_0.799665616296863"}, "_hasShrinkwrap": false}}, "homepage": "https://tweetnacl.js.org", "keywords": ["crypto", "cryptography", "curve25519", "ed25519", "encrypt", "hash", "key", "nacl", "poly1305", "public", "salsa20", "signatures"], "repository": {"type": "git", "url": "git+https://github.com/dchest/tweetnacl-js.git"}, "author": {"name": "TweetNaCl-js contributors"}, "bugs": {"url": "https://github.com/dchest/tweetnacl-js/issues"}, "license": "Unlicense", "readmeFilename": "README.md", "users": {"dchest": true, "tcrowe": true, "antanst": true, "kerwyn": true, "mojaray2k": true, "anoubis": true, "meeh": true, "dogun098": true, "aditya_1806": true, "flumpus-dev": true}}