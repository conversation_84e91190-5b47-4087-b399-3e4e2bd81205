{"name": "mime", "dist-tags": {"latest": "4.0.7"}, "versions": {"1.0.0": {"name": "mime", "version": "1.0.0", "dist": {"shasum": "0650d4779569617b3ee8bec7b8b7522e74af05be", "tarball": "https://registry.npmjs.org/mime/-/mime-1.0.0.tgz", "integrity": "sha512-DxDSRAKoDACl1BjhZL+ZJYuj4FrYMmEzF0+guodP54ddgpFIWHQgzQ4fmWbocQnPzAj7L5zE48xwDAnA8Oo2gA==", "signatures": [{"sig": "MEQCIF2xZptZ8E1iAxEjDOTmg5F2W/B+66M40YeiheKQX4NxAiBlJw+P71HlcSkg7cVozdSWVMBZsVNUiaF8YQ41nNYlQA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "1.1.0": {"name": "mime", "version": "1.1.0", "dist": {"shasum": "a067f5be8a6c9eeb83f3733c8c22dd142a603add", "tarball": "https://registry.npmjs.org/mime/-/mime-1.1.0.tgz", "integrity": "sha512-kh8izK42wjDEzrBNKebCgoz4WI6pb1x52InyVFZwWOf4qobU8Y1O/rSXXelUDyImItZKxyczQIu+DcEnAjH14Q==", "signatures": [{"sig": "MEQCIA0j8W1AM9xGV00bSROFbwnKmLmGzZmLCilnDff+NfduAiBTowrU3TMRM/ncmdhLU6f/8OFJlhtSD0wGP6WM2RhByQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "1.2.1": {"name": "mime", "version": "1.2.1", "dist": {"shasum": "9876d4db9491091d154288a32893564839b8e04e", "tarball": "https://registry.npmjs.org/mime/-/mime-1.2.1.tgz", "integrity": "sha512-4273benBd0SRRn/Z8fXg1u2LOM4+m/IwzjnSe6Iomame67KWXbG8b/w0qwL7ecHEumqYkVlLk2VOIQB0wcZLsA==", "signatures": [{"sig": "MEQCICqmMFeVRB2IikrNPQszq+L/ppegNbaZhwKMPT6EfQFtAiAhLeYvLfwTiXatTE+HcCxVlG6pEF9OfkuTRhWmVYp7NA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "1.2.2": {"name": "mime", "version": "1.2.2", "dist": {"shasum": "b9d6355bf53e8d7d56693130e451daff340148cf", "tarball": "https://registry.npmjs.org/mime/-/mime-1.2.2.tgz", "integrity": "sha512-lNLN1kF9wEWH/ONdxQa+rIlI5mivJv62036NP1Qt7Sh45ZxazBLPFvQB3+IBg3octwyrORhWyv/xcH+8bxv1eg==", "signatures": [{"sig": "MEQCIFp4IhshRVVIC5JWeIqvdMq63sBYSyo47UfKLb6w7qeXAiBfyKyXjDMQwdVEQuCmBlKakTzc0oyowd5o6IYuNaC4eg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "1.2.3": {"name": "mime", "version": "1.2.3", "dist": {"shasum": "7717bad7444f42d0c7d98cdc2a7b20068f837b68", "tarball": "https://registry.npmjs.org/mime/-/mime-1.2.3.tgz", "integrity": "sha512-zhhFTETSQymFiajm78xeYMCO+a+q1X0FscqGlU1viA7fl/fXphsWsTPstGIL3IHkrQ3E4xHun/GY+3w8RelUVg==", "signatures": [{"sig": "MEUCID4Qxe64cS5vvmmZ82QsX99NWTUuhMrSJeNcJCWFfyG2AiEAkP0+R0m1BJbo3We57JdrBYOdV7YTGw9Hi/iW0F07EqM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "1.2.4": {"name": "mime", "version": "1.2.4", "devDependencies": {"async_testing": ""}, "dist": {"shasum": "11b5fdaf29c2509255176b80ad520294f5de92b7", "tarball": "https://registry.npmjs.org/mime/-/mime-1.2.4.tgz", "integrity": "sha512-u4y8WFj7SnfbHxyzCMSV2nL/GKbfZ+lidPf1V4/XUKCJ49N6ttQWp/xnqpbMQEx3naBSt93i2qtPLFj/mjPDaA==", "signatures": [{"sig": "MEQCIEgwNNq13IP7UOknigez3rMehFn5AqE0K+yeHPaj5q+cAiB2nl/yn6w/OSN493tA4n2d+1be/ljdaxAuM9U+ftPyoQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "1.2.5": {"name": "mime", "version": "1.2.5", "dist": {"shasum": "9eed073022a8bf5e16c8566c6867b8832bfbfa13", "tarball": "https://registry.npmjs.org/mime/-/mime-1.2.5.tgz", "integrity": "sha512-QfwTOA+zRHSZXxl9Y7ue5ifKDhU1prnh0dO67Vgcl7Lcx0+79vL9A1ln0qtVur8CFSdYq5Zhnw9DDZQgwDh8Ng==", "signatures": [{"sig": "MEUCIE7pvhnfFOU9eC//hNuyCx8zBM5Apg3q/KxVkZiuSRccAiEAp5w64bikrgEb3gXtDlOsZCwQknNOzRqqcnmqGnG8bVs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "1.2.6": {"name": "mime", "version": "1.2.6", "dist": {"shasum": "b1f86c768c025fa87b48075f1709f28aeaf20365", "tarball": "https://registry.npmjs.org/mime/-/mime-1.2.6.tgz", "integrity": "sha512-S4yfg1ehMduQ5F3NeTUUWJesnut4RvymaRSatO4etOm68yZE98oCg2GtgG0coGYx03GCv240sezMvRwFk8DUKw==", "signatures": [{"sig": "MEYCIQDxFWVAdlixVNKiQzMw7YxVxSooqgCoEIW0Y8E5TY4LngIhANS+9uS1Zz1YrGL1W8A3hVm91E6ipDtHA7iAHeXYjxtz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "1.2.7": {"name": "mime", "version": "1.2.7", "dist": {"shasum": "c7a13f33a7073d9900f288436b06b3a16200865b", "tarball": "https://registry.npmjs.org/mime/-/mime-1.2.7.tgz", "integrity": "sha512-dpy+nvloUnk2kSbmdZ6PFEuGUV+gYLF3YIAxkOuvD3N0pZiUM4UiNBW9516oVHcOLfRVTIgShgQVyXQu7K+XIA==", "signatures": [{"sig": "MEQCIDCh4VFsV6FHbQurkf+FjI90nGfgQ0xCsFTLOksO2gFpAiAot656hsqOuoQVKnpF5Vf3voK+RrYZ9zPie9jJXnTXQw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.2.8": {"name": "mime", "version": "1.2.8", "dist": {"shasum": "59178be248b0e06df58f6e04db3c8ee30084e110", "tarball": "https://registry.npmjs.org/mime/-/mime-1.2.8.tgz", "integrity": "sha512-p87ZHgdpEQtjHivZmlGkskw+4EGzC2DbW4m2bBQoj4iRKA2wPWW7ZgbB6d+641593GDHl1peKBENgRxorahsgA==", "signatures": [{"sig": "MEUCIQDjzLPJoBpfEduPBVqC2YUrW0aBs4/bGJ7y4iui8WEmOgIgHs8srCuSSepshhqxqfUsFTLb3QB9MrMQn2nvDzbVx4Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.2.9": {"name": "mime", "version": "1.2.9", "dist": {"shasum": "009cd40867bd35de521b3b966f04e2f8d4d13d09", "tarball": "https://registry.npmjs.org/mime/-/mime-1.2.9.tgz", "integrity": "sha512-WiLgbHTIq5AYUvU/Luli4mZ1bUcHpGNHyCsbl+KPMg4zt+XUDpQehWjuBjdLaEvDTinvKj/FgfQt3fPoT7j08g==", "signatures": [{"sig": "MEUCIQCcKwEoHY7V0oKfluiUQecac43n+SLU16eFFBi8pPk+agIgNfrdssZsn3oxtYsUD3wFWY2qmSRXzKVUUDh/g5XRf8E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.2.10": {"name": "mime", "version": "1.2.10", "dist": {"shasum": "066380acbc3d78d4f4a51004d8988425dc68b9b1", "tarball": "https://registry.npmjs.org/mime/-/mime-1.2.10.tgz", "integrity": "sha512-CK9lz4+xh2+Oe/yJoAVw/zPwt1TRfWqIZwaVW0G7gWcCReZZ6IoUjr80aroG4fR7GZdtUzyQDFCEyiEK3CaO9Q==", "signatures": [{"sig": "MEUCIDFSzh0O/e4qpUIoXUp2Lmhxs9capZkdDVZZ+jdnvZzqAiEA4BVgvX45V/1/JfV0KJTHlyREjYfeYyCJPKk/dMAOXMQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.2.11": {"name": "mime", "version": "1.2.11", "dist": {"shasum": "58203eed86e3a5ef17aed2b7d9ebd47f0a60dd10", "tarball": "https://registry.npmjs.org/mime/-/mime-1.2.11.tgz", "integrity": "sha512-Ysa2F/nqTNGHhhm9MV8ure4+Hc+Y8AWiqUdHxsO7xu8zc92ND9f3kpALHjaP026Ft17UfxrMt95c50PLUeynBw==", "signatures": [{"sig": "MEYCIQC/zbLJOkpof8MPt6nf5FIrOxx4eJM+KpUgQJNwPgOFygIhANpTJTGW3jTLu8mr6L0MCaqyyuBGFtgNj5GmGsV/GRMb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.3.0": {"name": "mime", "version": "1.3.0", "dependencies": {"mime-db": "^1.2.0"}, "dist": {"shasum": "447c1ac7a6e4df33e3eebf13419bd736f99067f0", "tarball": "https://registry.npmjs.org/mime/-/mime-1.3.0.tgz", "integrity": "sha512-pN80pM+IXnKBbOaWgcbDUnnDubVX+a8xolEdPum8NurvpldUJUhqnRx9ReAq79hIDOFXuU97Tubqwwf/k5ZJsg==", "signatures": [{"sig": "MEUCIQDY9gKt/aTV5dk0LZ5+VavANesTqoDD3edmZ3OB6KLvQwIgQXGIswnOPGKlab4wo7DrDCBMOLEpyh/Vrsn9+KbQYzc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "hasInstallScript": true}, "1.3.2": {"name": "mime", "version": "1.3.2", "dependencies": {"mime-db": "^1.2.0"}, "bin": {"mime": "cli.js"}, "dist": {"shasum": "10d5293d23d8d4086cd2666a936477a49764c3bf", "tarball": "https://registry.npmjs.org/mime/-/mime-1.3.2.tgz", "integrity": "sha512-9CoMgrvwObFziXKxHKrNAxVObZTg5YKeQNNRNlaVjYuAq/WEQ04GKiGsHcfszqLJ8deneZBIsZJXFZ5tVBLvUg==", "signatures": [{"sig": "MEQCIEXWQMhdvy/xRNqx03XmFZWttHx1QsnLBDCqPFLFVCOaAiA5YuQORMrHM83nZ8kpT2VORNzNEn+1zY37FMLs53z2yQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "hasInstallScript": true}, "1.3.3": {"name": "mime", "version": "1.3.3", "devDependencies": {"mime-db": "^1.2.0"}, "bin": {"mime": "cli.js"}, "dist": {"shasum": "6b681b6c0c0f6b41aec7eb5ced09f43fc81d6caf", "tarball": "https://registry.npmjs.org/mime/-/mime-1.3.3.tgz", "integrity": "sha512-/rRh9GNoompeFdb8jeFQ9Bd7RwK5dl8Wt9loG0jGZ44XzzwzMsaqeqlNTBIDjVPbr/+a2xbm+B6TnhXi0u1y3g==", "signatures": [{"sig": "MEYCIQDJFrY22JT801lR1h+bvEAAJRohYEGdKbU9+qc/B7YlqgIhALAgNPwgIMNeXOJXJU6en5GjilEGPUmEO8TQQz7VcUJN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.3.4": {"name": "mime", "version": "1.3.4", "devDependencies": {"mime-db": "^1.2.0"}, "bin": {"mime": "cli.js"}, "dist": {"shasum": "115f9e3b6b3daf2959983cb38f149a2d40eb5d53", "tarball": "https://registry.npmjs.org/mime/-/mime-1.3.4.tgz", "integrity": "sha512-sAaYXszED5ALBt665F0wMQCUXpGuZsGdopoqcHPdL39ZYdi7uHoZlhrfZfhv8WzivhBzr/oXwaj+yiK5wY8MXQ==", "signatures": [{"sig": "MEQCIHUAsreLb91h0EjYHueYs+0SOQEr/RtM9Z/7unw/dpgvAiBQm9upSJVUldmqp9s6h8ZEKWPYfTYbUF7KIkR8hHFViw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.3.5": {"name": "mime", "version": "1.3.5", "devDependencies": {"mime-db": "^1.22.0"}, "bin": {"mime": "cli.js"}, "dist": {"shasum": "dc1ee70b80fee4999cb0775c9f94beefc9a779a3", "tarball": "https://registry.npmjs.org/mime/-/mime-1.3.5.tgz", "integrity": "sha512-G<PERSON>cJukJQMhMNZTwfxOqzCwt2G2rzbA6lf4OawMGVPlj/iTaSGzHuRqpCQ0P+LwOzHFnlZvfYXXzBZ3jsEj2ZZQ==", "signatures": [{"sig": "MEQCIHKZpciBY+TjK1AkCrGiyeOdxMEzxfKD6mIejOzPZeb0AiB3ZUGcmXQliojXdwOSxirWgPWNFddM807mkUB3QkFcDQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Breaks custom overrides.  Upgrade to v1.3.6"}, "1.3.6": {"name": "mime", "version": "1.3.6", "devDependencies": {"mime-db": "^1.22.0"}, "bin": {"mime": "cli.js"}, "dist": {"shasum": "591d84d3653a6b0b4a3b9df8de5aa8108e72e5e0", "tarball": "https://registry.npmjs.org/mime/-/mime-1.3.6.tgz", "integrity": "sha512-a/kG+3WTtU8GJG1ngpkkHOHcH6zNjGrI47OQyoFsFBN0QpYYJ4u2yEORsGK5cZMI+cfu9HbSCCfGfRzG0fWE9A==", "signatures": [{"sig": "MEUCIQCOIauoLwcjTUax/1y4UnJmTPjbjoAnZBzsr+eD2YTm9QIgQMq8CoP9hjAoqrFSOAAVt/cYf3uDPyrT51kprkK48WU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.4.0": {"name": "mime", "version": "1.4.0", "devDependencies": {"mime-db": "1.30.0"}, "bin": {"mime": "cli.js"}, "dist": {"shasum": "69e9e0db51d44f2a3b56e48b7817d7d137f1a343", "tarball": "https://registry.npmjs.org/mime/-/mime-1.4.0.tgz", "integrity": "sha512-n9ChLv77+QQEapYz8lV+rIZAW3HhAPW2CXnzb1GN5uMkuczshwvkW7XPsbzU0ZQN3sP47Er2KVkp2p3KyqZKSQ==", "signatures": [{"sig": "MEUCIQCPVJe27g20iTw4eykAj52aHnYDeCfDF4Gbep3M/T5twgIgLdbEMGZmOwXkpWKX3jb1kL4vU8k381S7+FZCZtul17U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.0.0": {"name": "mime", "version": "2.0.0", "devDependencies": {"chalk": "1.1.3", "mocha": "3.5.3", "runmd": "0.1.8", "mime-db": "1.30.0", "mime-types": "2.1.15", "github-release-notes": "0.9.0"}, "bin": {"mime": "cli.js"}, "dist": {"shasum": "e58e03970e44e57ed1394893b07e6312f71925e3", "tarball": "https://registry.npmjs.org/mime/-/mime-2.0.0.tgz", "integrity": "sha512-yL1fzI0icKnnR0cPYDshcwDe9cHi9GD+iKd6xKFScb9SHOTpgnQ1rg8BwgZZ2fa7yQSaupcdFYmNsYAXdh6b0Q==", "signatures": [{"sig": "MEYCIQDfxd/dkVPnbyK3p3D9UVFUPAAhGMFTyK+1vrNy5IGhZgIhAPU/0F1j9FGELMpg8z9lS86JqWnzPyGvxz53UR4uRekY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.0.1": {"name": "mime", "version": "2.0.1", "devDependencies": {"chalk": "1.1.3", "mocha": "3.5.3", "runmd": "0.1.8", "eslint": "4.6.1", "mime-db": "1.30.0", "mime-types": "2.1.15", "github-release-notes": "0.9.0"}, "bin": {"mime": "cli.js"}, "dist": {"shasum": "73dd0bc6a678836cc8322db5d8e4c3e6f3c06471", "tarball": "https://registry.npmjs.org/mime/-/mime-2.0.1.tgz", "integrity": "sha512-n2tQ4rs2+GMucMG2YHTwaONXZ/31ZS/vseUOzK5mTecwE91c33tapG51M0kB2R30hi7sY5nKvIkLCdCgUzx6FQ==", "signatures": [{"sig": "MEUCIFKMJtWn7eNza13Cu66VdxJwcFQNypwOiJ/A1790kTnOAiEAv2KT/m2fax5IydE8SFNp2ALF72Mx3Mlwnl8x8OX8VDY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=6.0.0"}}, "2.0.2": {"name": "mime", "version": "2.0.2", "devDependencies": {"chalk": "1.1.3", "mocha": "3.5.3", "runmd": "0.1.8", "eslint": "4.6.1", "mime-db": "1.30.0", "mime-types": "2.1.15", "github-release-notes": "0.9.0"}, "bin": {"mime": "cli.js"}, "dist": {"shasum": "097fd2c88c652eae48b2702d7cbf54c08d8ef50a", "tarball": "https://registry.npmjs.org/mime/-/mime-2.0.2.tgz", "integrity": "sha512-Oy3+E5KrKoJ99krrYGn+u6jkCEdyFiZX2plVzbuuGLWo5X5K2Oej0KcbF1vHsrB7WFPMSaNqfHjJ6ksLT6kxSg==", "signatures": [{"sig": "MEYCIQCIGW3V24odQfHJh2MBguACXk1T68Ch+NPffNsazVHhIgIhALLkdmAHC/+glbATTG+PuPuFssmJ7gdWkYrzKwT0+b3q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=6.0.0"}}, "1.4.1": {"name": "mime", "version": "1.4.1", "devDependencies": {"mime-db": "1.30.0"}, "bin": {"mime": "cli.js"}, "dist": {"shasum": "121f9ebc49e3766f311a76e1fa1c8003c4b03aa6", "tarball": "https://registry.npmjs.org/mime/-/mime-1.4.1.tgz", "integrity": "sha512-KI1+qOZu5DcW6wayYHSzR/tXKCDC5Om4s1z2QJjDULzLcmf3DvzS7oluY4HCTrc+9FiKmWUgeNLg7W3uIQvxtQ==", "signatures": [{"sig": "MEUCIQCzt3weEJXoBMrSyc/YLDzxPjKW2DMeTcpHmNOamb1qKgIgXuE6Zv/0tYTRk5+hbdKSLMHVX7YLTU+h9O9DxVwoTns=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.0.3": {"name": "mime", "version": "2.0.3", "devDependencies": {"chalk": "1.1.3", "mocha": "3.5.3", "runmd": "0.1.8", "eslint": "4.6.1", "mime-db": "1.30.0", "mime-types": "2.1.15", "github-release-notes": "0.9.0"}, "bin": {"mime": "cli.js"}, "dist": {"shasum": "4353337854747c48ea498330dc034f9f4bbbcc0b", "tarball": "https://registry.npmjs.org/mime/-/mime-2.0.3.tgz", "integrity": "sha512-TrpAd/vX3xaLPDgVRm6JkZwLR0KHfukMdU2wTEbqMDdCnY6Yo3mE+mjs9YE6oMNw2QRfXVeBEYpmpO94BIqiug==", "signatures": [{"sig": "MEYCIQDVjl0nD7zEf/X25XIr7fmqEV7Sw7cv//lLlpyHouCF2QIhAKNd3MKSQdQSQ/MpY488OPMQlLh6QDITjaoP/FvbgGhV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=6.0.0"}}, "1.5.0": {"name": "mime", "version": "1.5.0", "devDependencies": {"mime-db": "1.31.0", "github-release-notes": "0.13.1"}, "bin": {"mime": "cli.js"}, "dist": {"shasum": "59c20e03ae116089edeb7d3b34a6788c5b3cccea", "tarball": "https://registry.npmjs.org/mime/-/mime-1.5.0.tgz", "integrity": "sha512-v/jMDoK/qKptnTuC3YUNbIj8uUYvTCIHzVu9BHldKSWja48wusAtfjlcBlqnFrqClu3yf69ScDxBPrIyFnF51g==", "signatures": [{"sig": "MEYCIQDP7gH/P12bCaL8Nh8Lh3KMBH7RcVyNYkr82Ayh6H7wiQIhAMyOd/9nSwa+VLDPnuOOdEjn9cM/FwjHOc3IVCALSpDu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.6.0": {"name": "mime", "version": "1.6.0", "devDependencies": {"mime-db": "1.31.0", "mime-score": "1.1.0", "github-release-notes": "0.13.1"}, "bin": {"mime": "cli.js"}, "dist": {"shasum": "32cd9e5c64553bd58d19a568af452acff04981b1", "tarball": "https://registry.npmjs.org/mime/-/mime-1.6.0.tgz", "integrity": "sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg==", "signatures": [{"sig": "MEYCIQCvbpmo9GAfwi2MC0EJ2R5ASwpXcAZ5KZ9K+UdI6PaqZwIhAKK0FP4aX3+UxS7fUAiyiIQleqNq4aKIoTkjeTnn6xaB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}}, "2.0.5": {"name": "mime", "version": "2.0.5", "devDependencies": {"chalk": "1.1.3", "mocha": "3.5.3", "runmd": "1.0.1", "eslint": "4.6.1", "mime-db": "1.30.0", "mime-score": "1.0.1", "mime-types": "2.1.15", "github-release-notes": "0.9.0"}, "bin": {"mime": "cli.js"}, "dist": {"shasum": "6cce36408c28535b29088d9d263288e72c786775", "tarball": "https://registry.npmjs.org/mime/-/mime-2.0.5.tgz", "integrity": "sha512-345FGKrFL5gB4gCt9tdpMJvjYnx6G3z2qBLB7SIHbZui0h1L9GKd6fXqiqa0ShzrIFy1VSydJOCrtRSi907Ggw==", "signatures": [{"sig": "MEUCIQCQGQJ7+4HNSW3U30Me/oORzhoEdCTaj5GIr89D97whZwIgFAik2y0ezLBlZyqrV968qHk2P9fI0z8XXXCM85QSfZs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=6.0.0"}}, "2.1.0": {"name": "mime", "version": "2.1.0", "devDependencies": {"chalk": "1.1.3", "mocha": "3.5.3", "runmd": "1.0.1", "eslint": "4.6.1", "mime-db": "1.32.0", "mime-score": "1.0.1", "mime-types": "2.1.15", "standard-version": "4.2.0"}, "bin": {"mime": "cli.js"}, "dist": {"shasum": "1022a5ada445aa30686e4059abaea83d0b4e8f9c", "tarball": "https://registry.npmjs.org/mime/-/mime-2.1.0.tgz", "integrity": "sha512-jPEuocEVyg24I7hWcF6EL5qH0OQ3Ficy95tXA9eNBN6qXsIopYi/CJl3ldTUR+Sljt2rP2SkWpeTcAMon/pjKA==", "signatures": [{"sig": "MEQCIHYuxelokYYOY1Yn68NN2rz9VIN+Fn9O7zCOTcCxhifaAiBTM1C8qNDaBmvBZgKFYxhQyM9Tr1OpWlDBrijMl6Zd/Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=6.0.0"}}, "2.2.0": {"name": "mime", "version": "2.2.0", "devDependencies": {"chalk": "1.1.3", "mocha": "3.5.3", "runmd": "1.0.1", "eslint": "4.6.1", "mime-db": "1.32.0", "mime-score": "1.0.1", "mime-types": "2.1.15", "standard-version": "4.2.0"}, "bin": {"mime": "cli.js"}, "dist": {"shasum": "161e541965551d3b549fa1114391e3a3d55b923b", "tarball": "https://registry.npmjs.org/mime/-/mime-2.2.0.tgz", "integrity": "sha512-0Qz9uF1ATtl8RKJG4VRfOymh7PyEor6NbrI/61lRfuRe4vx9SNATrvAeTj2EWVRKjEQGskrzWkJBBY5NbaVHIA==", "signatures": [{"sig": "MEUCIEssoGOkMUimn7rJExusG7cBdbbitLc18phT8d6IiEMgAiEAod9TolkRFSCziRq7rjJzRpKgAgvy+uciDtQQf1binQs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=6.0.0"}}, "2.2.1": {"name": "mime", "version": "2.2.1", "devDependencies": {"chalk": "1.1.3", "mocha": "3.5.3", "runmd": "1.0.1", "eslint": "4.6.1", "mime-db": "1.33.0", "mime-score": "1.0.1", "mime-types": "2.1.15", "standard-version": "4.2.0"}, "bin": {"mime": "cli.js"}, "dist": {"shasum": "3a5e605c59bba00fb731f6a1f84701638131671d", "tarball": "https://registry.npmjs.org/mime/-/mime-2.2.1.tgz", "fileCount": 16, "integrity": "sha512-8QKdX8CfqnkIn19mnv3Zq78RugzDXZNrcewbZrjf8h0R6aN5Daizum/OoXxqVVhkFW3Ow4LFSn5iOi7qJJOMoA==", "signatures": [{"sig": "MEUCIBClROzrHeY4FKKAcWRp1jIPCT9QQUJ5AlP3YF3kggaEAiEAnRqCxRWovwCBQbVKiaGu1G+11ODUaJ7du3iJ0pgt2LA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70404}, "engines": {"node": ">=6.0.0"}}, "2.2.2": {"name": "mime", "version": "2.2.2", "devDependencies": {"chalk": "1.1.3", "mocha": "3.5.3", "runmd": "1.0.1", "eslint": "4.6.1", "mime-db": "1.33.0", "mime-score": "1.0.1", "mime-types": "2.1.15", "standard-version": "4.2.0"}, "bin": {"mime": "cli.js"}, "dist": {"shasum": "6b4c109d88031d7b5c23635f5b923da336d79121", "tarball": "https://registry.npmjs.org/mime/-/mime-2.2.2.tgz", "fileCount": 16, "integrity": "sha512-A7PDg4s48MkqFEcYg2b069m3DXOEq7hx+9q9rIFrSSYfzsh35GX+LOVMQ8Au0ko7d8bSQCIAuzkjp0vCtwENlQ==", "signatures": [{"sig": "MEUCIHN+6wNCf7xJhCn1+h/tApBd/yXELPJtbkuQZEURRCGAAiEAk79oCkelOETMRyLkTD5XYyuQ0LgL+bUkLIz/AfJRjkU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70633}, "engines": {"node": ">=6.0.0"}}, "2.3.0": {"name": "mime", "version": "2.3.0", "devDependencies": {"chalk": "1.1.3", "mocha": "3.5.3", "runmd": "1.0.1", "eslint": "4.6.1", "mime-db": "1.33.0", "mime-score": "1.0.1", "mime-types": "2.1.15", "standard-version": "4.2.0"}, "bin": {"mime": "cli.js"}, "dist": {"shasum": "e3e1ae7f594b15ef331b79083216b50333ed2ebd", "tarball": "https://registry.npmjs.org/mime/-/mime-2.3.0.tgz", "fileCount": 18, "integrity": "sha512-9dE160rWamibtUmS5kbAuu8Fbk9FihwN7ZYuWXbd6hSFKEKCqRe0hJ8pnqvmNOt5ljmXHmnKTxinIkdM1mKuPw==", "signatures": [{"sig": "MEUCIQDnPdkUlYK4+UUm1xGIO01dVlQOjpmjZgPAlqbJwS25UQIgWEkKn5I0s+7TRPzwIDa1PtxWU8RXY79WjANWbCQsb+o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71566}, "engines": {"node": ">=6.0.0"}}, "2.3.1": {"name": "mime", "version": "2.3.1", "devDependencies": {"chalk": "1.1.3", "mocha": "3.5.3", "runmd": "1.0.1", "eslint": "4.6.1", "mime-db": "1.33.0", "mime-score": "1.0.1", "mime-types": "2.1.15", "standard-version": "4.2.0"}, "bin": {"mime": "cli.js"}, "dist": {"shasum": "b1621c54d63b97c47d3cfe7f7215f7d64517c369", "tarball": "https://registry.npmjs.org/mime/-/mime-2.3.1.tgz", "fileCount": 18, "integrity": "sha512-OEUllcVoydBHGN1z84yfQDimn58pZNNNXgZlHXSboxMlFvgI6MXSWpWKpFRra7H1HxpVhHTkrghfRW49k6yjeg==", "signatures": [{"sig": "MEUCIFwwLHTC6MNnqp5q0wH4Ofc5atElqv1DwCgQbhNUOrrpAiEA5k8NbzuIYVzCk4L2KTdbFS/9C/NBqruK3awLecFI9/4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71814}, "engines": {"node": ">=4.0.0"}}, "2.4.0": {"name": "mime", "version": "2.4.0", "devDependencies": {"chalk": "1.1.3", "mocha": "5.2.0", "runmd": "1.0.1", "eslint": "^5.9.0", "mime-db": "^1.37.0", "mime-score": "1.0.1", "mime-types": "2.1.15", "standard-version": "^4.4.0"}, "bin": {"mime": "cli.js"}, "dist": {"shasum": "e051fd881358585f3279df333fe694da0bcffdd6", "tarball": "https://registry.npmjs.org/mime/-/mime-2.4.0.tgz", "fileCount": 18, "integrity": "sha512-ikBcWwyqXQSHKtciCcctu9YfPbFYZ4+gbHEmE0Q8jzcTYQg5dHCr3g2wwAZjPoJfQVXZq6KXAjpXOTf5/cjT7w==", "signatures": [{"sig": "MEQCIEnuLkdlSRZdSg+pFEKyXv2dAFh/FuLJu4CKe9hNteQYAiA4FCC4XO+flp+Pn1ZHvn0EOjiYz4vM2UkyBaaLkRL6mA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73137, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb/HUOCRA9TVsSAnZWagAAivMP/3yoL2JUahYb3gcIho77\no1CWe26C8rnZwNnYXzGv9WaaTJ/xfKr6enMW7+G6moemwgX+3h3roAp7eVFz\nGKpkhLNO07geRl0rETZafFaHyxVZd9BuKy+OR/Q/DkjNvza+xeDqJ2SB4wdt\n8WzNT7rH9Espnxvxf9KhA0YWoDUPSOqL/PbAZySuYcmxn4keNJQ+yp0PLDOE\n63shHVVsL7/Vy3qKKUMRAmfwuQCl8CpLL8wi2r/FfNbuvaAKC34d8Y33Aus2\naN2ehHBcUKFjrGor4VwU+G2MYPhGnZovc5w7QIMcBovRRoM8kQKXOyItvF/X\nuW5uzUY9wFRBnkj9tlGTEa4RvFBCp2PvkWpczQsorZZjYbKUC4C9ISSCUzN6\nELoGMxMkQsLIZdwVEFgYUxsN/9Xzjgdac7KEyFw+cQhGfNGrl9rathNWXUEF\nYQn8XMW4jghZ7ZfftVaPARo8mX/Eg1x8VAujrOoupOAPhCgp9sBbb0s00TqW\njdpF5PD3Ny5O6SPFweDwka2WsSV9/elxeDDg8cWTpj+p0SSFbT0d4b/kUWZq\nzgaAWqNSVoC6BpcGS5I7svxJ4dlQbL2BTyu6VXQdzKJ++CGsUn5X+7UBHAe4\nenT4ZUCHJP3mhSNE582PMO/HwNKUYraVHHHAmRfyGBGqnS/mg33WxZrtPgcs\n2zvm\r\n=7Hld\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4.0.0"}}, "2.4.1": {"name": "mime", "version": "2.4.1", "devDependencies": {"chalk": "2.4.2", "mocha": "6.0.2", "runmd": "1.2.1", "eslint": "5.16.0", "mime-db": "1.38.0", "benchmark": "^2.1.4", "mime-score": "1.1.2", "mime-types": "2.1.22", "standard-version": "5.0.2"}, "bin": {"mime": "cli.js"}, "dist": {"shasum": "19eb7357bebbda37df585b14038347721558c715", "tarball": "https://registry.npmjs.org/mime/-/mime-2.4.1.tgz", "fileCount": 19, "integrity": "sha512-VRUfmQO0rCd3hKwBymAn3kxYzBHr3I/wdVMywgG3HhXOwrCQgN84ZagpdTm2tZ4TNtwsSmyJWYO88mb5XvzGqQ==", "signatures": [{"sig": "MEYCIQC4KPFxPVAFjZHjLInTpAHb3K0pr2VlPkCTsoGJShEDGwIhAJccJ/16f7gif5F0dHLIetpv69f5l6nuFc1ighewkKym", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74127, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcpQClCRA9TVsSAnZWagAAYVUP/3jqCEnfpQ5MM2Z1eM9X\nvrFQx51FLk41ONqqaw8W82zXdWdQUMmNPMFRrqccP2SLl6tcWPoYZABMTJBB\nQy40c6FE2gSyI5M+S3hUUP8p06UkD0g8uKUr4vCtVflCAQ9O4cdw/LK/Rh7x\n5MrV9NH38H3r4+G7xz5vthmVG3LM/mexaYXyD+7dn7aV4QolPgqslMNoYKjo\n25a/D5ZPvAuQOoVYF2yrHJs17mSiPVnIxA7WwkueF9EKttYOCPZRsEUUxcWC\neXCweZ0GF1a5JuFDpJrK3TctjBxbiFbUgJ5cR6NKTf9rmROTZ/iy2HIenahS\nPpRr/KnynSFosPB2NZnjpU5JXq4rW80dYOgjx36CQ2/8ndTsewL8h9GWUqA/\no/A5TjxG8rfIlva+7K/z0DDrBRke9woHEJl9wOHfuIHuu+2AdxuLd7XKOcEj\ny9ZpvaZnLejPgYxs3DgbOb9jugy0DGGzvkzVBySsc6q/moHNx2epBMcYw0ti\nK9bUFRoqFFbAa6y9DBn0vnONV2+FSCvdXqL528Ygk8HQ3JUEAIZHPP45J6H5\nW/o9dgebeOPRGFFfBgod7o53KbK+Jh3iQ6Gr2AsYYBkYa2cLqQ4jaaIplD3q\naPvaJK9razBBKTB5a9vvXbSNhc7hfp8L0NhiRH6E00olnKggjH3NoYQgm4+/\nOJjP\r\n=UFF/\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4.0.0"}}, "2.4.2": {"name": "mime", "version": "2.4.2", "devDependencies": {"chalk": "2.4.2", "mocha": "6.0.2", "runmd": "1.2.1", "eslint": "5.16.0", "mime-db": "1.38.0", "benchmark": "^2.1.4", "mime-score": "1.1.2", "mime-types": "2.1.22", "standard-version": "5.0.2"}, "bin": {"mime": "cli.js"}, "dist": {"shasum": "ce5229a5e99ffc313abac806b482c10e7ba6ac78", "tarball": "https://registry.npmjs.org/mime/-/mime-2.4.2.tgz", "fileCount": 19, "integrity": "sha512-zJBfZDkwRu+j3Pdd2aHsR5GfH2jIWhmL1ZzBoc+X+3JEti2hbArWcyJ+1laC1D2/U/W1a/+Cegj0/OnEU2ybjg==", "signatures": [{"sig": "MEYCIQCoqlgfF0Uovpx4x3VjvEXBbFuLoEXqeD/EO8J147xKGAIhALO8y0FSaeASB9CxQOntV7sQ06cHrgCH5WkYmIrxUXo6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74360, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcqkA1CRA9TVsSAnZWagAAggkP/RAyGuatr02wOmMFLwLz\nlC20G3opBTIEuh+hknPf/CcDnEWK+WF3Wc255bEr9z9ocInDa/dj9KpZj1LC\nCfdXWFjDsTHsRYeAUmS770Yc6+8e8GE8vc2UFM7jk6ZnwG9xagV+v8Xm9YAs\nRtd7BN7tXre/fngrG5kcaLHvcDC72+VQ7dkvIDJq0Im9mdI5VmFmP20A60Ng\nkC03fLT2bB53WNLsXGUoX2TEwVF7BW8i5IgLwuVjfCrO3B9n1DToqHht5u9u\n8/K99pwX73Ps/na/iPppvLRcwCFIiL8hcZ7yH95Lr05tFZPiadcJQ5zEyFcj\n2C+kFRpI5FkCVyS356+z2csvWY7XXhclWKxKWHC9/Uxq7qf5QvukQbagwdrh\nLtn9WyRPYf4vZ9BgdAhsAjLfEHOG5aek8hzI2mrJJ60kL/SuKLTVwC7AZkQ2\nwM3lFSp7dWxczqp1tP9E9bOYIj/s7FdeauWpoYSMfTQuDiLCGblR/b6ieB6g\nXVk/2FMtCvAhPlKsBUjP0kG1Y81TbUVgxu30cfjDQWPKZ6v2zLjg+NY8skPS\nMAlLuA3Eju89X7gQykBwlv9RNNXVv1y0D2LlnsIjttPfo3RX+/PmrD1GhN7P\nt9A4GKJ9WTUwK2njcLt8qHHehTTnHF/qM8/idGoez98Z2Z/yGuWRoD8FH4yK\n2Uzj\r\n=chBD\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4.0.0"}}, "2.4.3": {"name": "mime", "version": "2.4.3", "devDependencies": {"chalk": "2.4.2", "mocha": "6.0.2", "runmd": "1.2.1", "eslint": "5.16.0", "mime-db": "1.38.0", "benchmark": "^2.1.4", "mime-score": "1.1.2", "mime-types": "2.1.22", "standard-version": "5.0.2"}, "bin": {"mime": "cli.js"}, "dist": {"shasum": "229687331e86f68924e6cb59e1cdd937f18275fe", "tarball": "https://registry.npmjs.org/mime/-/mime-2.4.3.tgz", "fileCount": 19, "integrity": "sha512-QgrPRJfE+riq5TPZMcHZOtm8c6K/yYrMbKIoRfapfiGLxS8OTeIfRhUGW5LU7MlRa52KOAGCfUNruqLrIBvWZw==", "signatures": [{"sig": "MEQCIDkEdyc6Aet3g7Wr7wK9vpZKwOYtH9aaju6OUG52mWJ+AiBg7jTyiQJ/GxRv5DQz3xms3GT1txRIse76bLkvxG06OA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74508, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc3B0tCRA9TVsSAnZWagAA448QAKQAmUAlViGYaqHe5CyB\n1th8GxX+J9t6msjV0ffe1ZGti+RZWQ30rCRuLCkyg4GxQ0lBkWbzOPe/IA6B\ntq6lQ3UZAU8j/bjApETdsbktCiHFStB8/XPpnh4q3kG3o0BTyGZn2564PR//\nZH+rPhZNmpQe/6oqF3CAUa5ilXxOENGcFWxlwKLzCVULP50TiA+bgSxyMdT2\n/x6SBImaasjkW3XyOjQbjWbxu6JLmiutUGxSkUDXPeKyGbEOCHYwL6bSCbrr\nucdBEZpz9p7v/hdm8J2E1DbJZHhSH79wy1b0jeH1MFUckfLLPgRIxaa4jhZY\nRuL80s4UBPQZqcUEOYwnoiAedUT0u4Yq4d0n807jHKpmQ6Lcjwbr9xxF8Bd9\nlpt6c9V/ckV/2VWi1+44MLDJ/vd+Os4wDs9PWcSaYoD97k/5xjrdj/9Y808S\nzByUs330xcgLtElwgqcAySu3PC122RKQI6g+06JHZR+X2yqpSsQV1PeumBdY\nC4XcXUlxobxVC3rXE7fnXyr53I+Nqi4WdEFYLPwYEGpqG6T7MaQa5B2doqy8\naZ1PSS3klKF2qTpG4vAO9POqZysuxD1epqG+G1gQYuaVWZQeuOtgCcMiG4YR\n0T2N40r+8kAaLNgB9vUcUibm4bHIJwS1KCJ54Pz6ut3XxzfTBlGtZjfIe4nC\nie3z\r\n=kNJP\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4.0.0"}}, "2.4.4": {"name": "mime", "version": "2.4.4", "devDependencies": {"chalk": "*", "mocha": "6.1.4", "runmd": "*", "eslint": "*", "mime-db": "1.40.0", "benchmark": "*", "mime-score": "*", "mime-types": "2.1.24", "standard-version": "6.0.1"}, "bin": {"mime": "cli.js"}, "dist": {"shasum": "bd7b91135fc6b01cde3e9bae33d659b63d8857e5", "tarball": "https://registry.npmjs.org/mime/-/mime-2.4.4.tgz", "fileCount": 19, "integrity": "sha512-LRxmNwziLPT828z+4YkNzloCFC2YM4wrB99k+AV5ZbEyfGNWfG8SO1FUXLmLDBSo89NrJZ4DIWeLjy1CHGhMGA==", "signatures": [{"sig": "MEUCIQDiRB0rNN/AsjA3Y7i0pb2gAwbEHwOcFv0fS/ZpYXBwmAIgMBhuFFSb0A8tBais6qC+eCGfb1KfBqgWo6SVtHxJstg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74946, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc+q7iCRA9TVsSAnZWagAAIMMP/1ZtPOiAAL1b2hbekcva\nTgHAroKFU8SdPR3d8nXnDd56g4KbQ5rlWM1EgVlbVSAuuCYQU5R0MKqgaq9P\np27JNBEQTPHsmTAZeN9mZ1R2TTqAwped5RcjOFsrZDYvGPGSOiTKPtv6ZGzg\n/zVJwLNR3DaWNSsu3lnw2/K2kyucZ3xSPRnGgIpzbdJ/pheNbXS6lCA7mWMZ\nDFkhAOB3Z19J3ahFPd/+AdxswtknFOmKEefudXXbdezjYyf355LWz7Cta/OM\nc+B8DRq/TrBqcko2ixY1dJwAs9h8eRnO/QnJT/ysRwFC/YnnrNyq/S77WbfP\nlkW1QZfLFDOt6qsK3ll3VLLf/hxK4Sdsvc+aaFvK4f8rRK8WSppMgPaVLAtV\nilOUETPqj0wp/ycurCxwVJi+Hm8hQyfh05X1glFVUD8YYp9IZIWpOVOqqLdN\n7TL1UDx4oJ0/wpdjy3wSAKfavGk5JuL9TnXa3TbJoRJ8T1FkGhWvAmnRsO20\ncrNCIK1kwl2OBnYjMx54uEs7783DTntg3f/HWGg73tfmv9RLe5ngWYK81FrU\nV5AfTrtiAFyYspJug9uHfRkeoBX5eEhzfOzGyVgcyoMcVMCWY/+dtYMQeAeh\n9ImJrg9jQT7wFMPxeFEaCIYGhnsqjFl7N/8jJwNH6dKfsPxA9KuDf7PVhUG9\ne0MT\r\n=TmH0\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4.0.0"}}, "2.4.5": {"name": "mime", "version": "2.4.5", "devDependencies": {"chalk": "*", "mocha": "7.1.2", "runmd": "*", "eslint": "*", "mime-db": "1.44.0", "benchmark": "*", "mime-score": "1.2.0", "mime-types": "2.1.27", "standard-version": "7.1.0"}, "bin": {"mime": "cli.js"}, "dist": {"shasum": "d8de2ecb92982dedbb6541c9b6841d7f218ea009", "tarball": "https://registry.npmjs.org/mime/-/mime-2.4.5.tgz", "fileCount": 10, "integrity": "sha512-3hQhEUF027BuxZjQA3s7rIv/7VCQPa27hN9u9g87sEkWaKwQPuXOkVKtOeiyUrnWqTDiOs8Ed2rwg733mB0R5w==", "signatures": [{"sig": "MEUCIGgVebuCWRXVGnBBFH9OvWHh0y8ekQuJQhccWJwuV62gAiEA1FuZpnjq3TC4SLeoGQhyNtbIKT7xSDSDiVUxdEd+dHA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57965, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJerKx5CRA9TVsSAnZWagAA4pUP/1WBKA/862DBDbaBOo/I\no6LAeulSAUufwBKXAY6G521veNZtHcKSuzd63y8l+2RCAou5qplQrZLYn/Pk\nXd9lLcHNKk3zamil9sgjJn7biQMmxnFriz/2fO/Q2ZDXNK17nqIfmbz7a9Xs\noZhXsdVfRYY+kD+MEdTO4VAJdxZ/TsZUO6HbBSoyCynG6QxnbxqKKjG9FAgv\nbMYacbihCVXcI2yCa3bo3QWRgJepD8hYPZG0YzW5NDowolOitSHSDIN2HnTJ\nAEmUJuNhktFIiv1BZNbEwqUIZly6JQhlEUweIyaQG0+zFkO7Ea8S4VP4ZnIL\nfpZBAYtuybtgzJaN6poT9AwEgLxFQv8jkbuULA+Ma33WrOINinersZtb+laM\npcjRhq0ItxoYLBkraWYUmAC1g4d3ia39hzO68fLvvw96VkDnJlTGkIubg5UH\nguuGTMFPEJCyCWcT2mXtTwwepWuygSMCAkLlDIisb/4bcxx+8CyagpGdobS0\n1J+GxCbXrocWprb0ZGYaFR3hk9Z2hbKu3JRN1XOgICd5psUbi7tvlonbpmPD\n7r50RZn48VTMyKdmMuonFSZ7QzZBUVecoc91dioLBHPE3yRJ8JPok0/8edk4\nwm2EE7lrO1JNxJq9VvGMaPlDE/LQus34NoBIAnyCodgx0ZcF4hmTJnWlPAaK\nVNqo\r\n=xHhY\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4.0.0"}}, "2.4.6": {"name": "mime", "version": "2.4.6", "devDependencies": {"chalk": "*", "mocha": "7.1.2", "runmd": "*", "eslint": "*", "mime-db": "1.44.0", "benchmark": "*", "mime-score": "1.2.0", "mime-types": "2.1.27", "standard-version": "7.1.0"}, "bin": {"mime": "cli.js"}, "dist": {"shasum": "e5b407c90db442f2beb5b162373d07b69affa4d1", "tarball": "https://registry.npmjs.org/mime/-/mime-2.4.6.tgz", "fileCount": 10, "integrity": "sha512-RZKhC3EmpBchfTGBVb8fb+RL2cWyw/32lshnsETttkBAyAUXSGHxbEJWWRXc751DrIxG1q04b8QwMbAwkRPpUA==", "signatures": [{"sig": "MEUCIFfL6XblF6Yvsz2YA3z5QGLd/rI/lTiplOdPABehLmiTAiEArk3P1THhQMpHbzoUrBf5C8srH480meK97VV0i/23Imc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57564, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezpQLCRA9TVsSAnZWagAAw7QP/1YrEg60oynV6aaZqeGO\nm8DhTTe29D2i7rj4uKotubnKClrS49729E8aO86DBJP0UqNStnobuegOQL6F\nqdLLJgi5B1CCyuezV54uKA5evj6jBObPsfFfWnDDY4jmReRQfS2qPyOzPtdG\nKs8RdF+j9DknwBYjGm2dOGjX94tYZoiLY6ZSPS4mDZ2bAnMt9zKwYONMbvV2\nwzqR2Rawu5CyVOr2UnervidQvy74A0jIXKGBoyhzSJ+VXqevMLiJGWWnGoz+\nSBM25Vg6Rdu1hHnlmiaLoMEN8QF78lmgMcQunA/61nDkAkICGo8QUubp/j18\najUrKY4+y96VIuf15doCrrqQbNIMeCG8gCLZ2deI7A5c96at0xgIa2+67n5E\nz+G5jEvoHzPCYApDpCKg1RD2vIhJv6WywzhlHGXVEL7k/DgUcN/PLENqPykC\nGxgz5MxGU7hVkQYc+EQHnHRLWq8A9/YedNqOYpP5UCJoWfAT9M4ypRjjNa3n\ntgVVzv6cpzwBoBhh/2qV1NSvftnudyRZpj42s8RMQUCJgT/g5hwXZxQ43hg/\nIzrHaEkvXpr/MENUZIRXWUIPx6Iy4EONYox0VJ7P4/7zDO1AboNUGO0s8wD5\n+7mwLy1/ne+8ns7FisY0ArSxjz/egyln9n4MNzItCOwpRvy5F9x1JU7kqruF\n7uCG\r\n=i4GN\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4.0.0"}}, "2.4.7": {"name": "mime", "version": "2.4.7", "devDependencies": {"chalk": "4.1.0", "mocha": "8.2.1", "runmd": "*", "eslint": "7.15.0", "mime-db": "1.45.0", "benchmark": "*", "mime-score": "1.2.0", "mime-types": "2.1.27", "standard-version": "9.0.0"}, "bin": {"mime": "cli.js"}, "dist": {"shasum": "962aed9be0ed19c91fd7dc2ece5d7f4e89a90d74", "tarball": "https://registry.npmjs.org/mime/-/mime-2.4.7.tgz", "fileCount": 10, "integrity": "sha512-dhNd1uA2u397uQk3Nv5LM4lm93WYDUXFn3Fu291FJerns4jyTudqhIWe4W04YLy7Uk1tm1Ore04NpjRvQp/NPA==", "signatures": [{"sig": "MEUCIBnn4oCabYOkpmM8IrOOMoth0XuRyDYYD3syaiACJijAAiEA3zhZMcz8qiNb3TmoteHbxpjpLtGmK4JTG5qQLLmdzNc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58019, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf2XFbCRA9TVsSAnZWagAAGBIP/2wnqIr5EzLqIXw7b3Sn\nuBwB9awWa8A+4QA6oUp+KN03WQ0j38FXTyl9dJjE10w5TrQNDUeVZWAWZ5Xo\nOt9v8ZlpVYpvF/4oRhSByvOu0aNX5gBs0nSeWSIkGj0K8cMxcFrMQOO9FFlB\neuiTsLAty7l1lB/p/2CAxSJVUtdGdordPe2t5VMHzO9T/CDlMNOWcb1a5cuq\nXLPzHOTOjTmraZWvTg0OWjWEb3PQkR7D+DKgPbIyVuBjqbvelPilVHwZzR4b\n1zFBLyNBrteu19aBgNd6gOumIgTmGlTpFWfxl75hjhfh8Itt1BpkTQwJgwCW\n195bdYNl/D9xaedrbRpu98kaEAXCpwVOFaA4syMJa3TS3wB1FzlYSG1sovaa\nSddAw45zlT0KPz6+/MDK/q0qWS/Wb5S0uim2Griz9WLK8aWj6KoJgMi/YGZ6\nElPafMFYjWYtcpmyMds5Bpj65mfd1/m4ODhy2XZh/ye5gjwftBtlH9pyWwbd\n1ZONAKgR/k4mZ4BTLSXy4sF8lLRKlGsO/6EDsE1GghaiLs1CY9Khy5dDXUIR\n/zF0hQY+tVunBtYnIuU7iY5uY5kWrUO1eW2IX60+Iqf0BUPVIWDRS7AfM63x\n7OPbkleWlq+nORS9Z5q2NkuxC1C/I418nwGFjodOt02SwRc+HgUckd4UxvQq\nsGZm\r\n=46Lb\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4.0.0"}}, "2.5.0": {"name": "mime", "version": "2.5.0", "devDependencies": {"chalk": "4.1.0", "mocha": "7.2.0", "runmd": "*", "eslint": "7.15.0", "mime-db": "1.45.0", "benchmark": "*", "mime-score": "1.2.0", "mime-types": "2.1.27", "standard-version": "9.0.0"}, "bin": {"mime": "cli.js"}, "dist": {"shasum": "2b4af934401779806ee98026bb42e8c1ae1876b1", "tarball": "https://registry.npmjs.org/mime/-/mime-2.5.0.tgz", "fileCount": 10, "integrity": "sha512-ft3WayFSFUVBuJj7BMLKAQcSlItKtfjsKDDsii3rqFDAZ7t11zRe8ASw/GlmivGwVUYtwkQrxiGGpL6gFvB0ag==", "signatures": [{"sig": "MEQCIBNUejVcJZ/H6r9H4Mc1XYxgYZPII/LwQ+MZEp4M7tcTAiBRcXD3s0TRSj8lDgsY8/adu6zrD5jDgZtEwGC2dCCZcA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59334, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgAjgjCRA9TVsSAnZWagAA+o8P/0+GZju4bxlcw7ovI3mp\nIAZ7a5EKqnEGeV4q2Crj9LTWTezX1rpytEZXJLMrUHHs2IGa7UYHE3fYE3zN\nxwMbE/pGh/WvTdknObSVF3PpcOlJM6Otp+tZ7V3IgzmDucy8QffPyJpMu+6y\nk348N4mBvTH/jA3ylRI4imEp0ZDisnPHfHp5wMMU64KTVKvcec0m7HjhuW1Z\nQ0zKGNWUPa5FUdFH9fIQ50B4Ph0maziVSjNdZO7FT68dvyb29rCV1jfSGXFb\n1qcuQfoUC7zKAQi7yBQvEC6OesYWhZaJrY902cdC80IyAxqDoXN1dcIiWunv\nCCYvDb5ViTrA/ATlLgeToYNWgQIOaYv13rwf2GBba3EblpbTcJVJXgYcIIvm\nTMfV5UmYCq/mUjNQlbs1R/U1YCIP/lLPYdICanis9Pv4Ihc0qowL9ujCPMLN\nHoj8jJSVfqR7UyBC56p4APFvAaSl62autYOp3b0aGmKo8hAZU5Qbw0QYDkTG\nOlvPHyY0tXXMPYQnTf5GS+tLx/+HwlE76+O9OmL5o114z/KZezKWBvm+o7KD\nvtZGfR8icY0iNw5OZoXKCGSlnrtXOrkzuBYj6Fwt0AIgI3u5uIntr83Py9HD\ntnibfsRG8zr7sEVhzAQ5uRlV/pZAELk2Uq4g+tZJy9dipksjmjYodKt1wcxN\nXmdt\r\n=k+zX\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4.0.0"}}, "2.5.2": {"name": "mime", "version": "2.5.2", "devDependencies": {"chalk": "4.1.0", "mocha": "8.3.0", "runmd": "*", "eslint": "7.20.0", "mime-db": "1.46.0", "benchmark": "*", "mime-score": "1.2.0", "mime-types": "2.1.28", "standard-version": "9.1.0"}, "bin": {"mime": "cli.js"}, "dist": {"shasum": "6e3dc6cc2b9510643830e5f19d5cb753da5eeabe", "tarball": "https://registry.npmjs.org/mime/-/mime-2.5.2.tgz", "fileCount": 10, "integrity": "sha512-tqkh47FzKeCPD2PUiPB6pkbMzsCasjxAfC62/Wap5qrUWcb+sFasXUC5I3gYM5iBM8v/Qpn4UK0x+j0iHyFPDg==", "signatures": [{"sig": "MEUCIAgoxrv4IKg5wGOLt2kNYD8+QyqudcHIjDCZtb9wpAUsAiEA1GUmu9dPJ84zQ880ccL489IZfJEG2SnDs2NDU7zMBkY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59664, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgLVliCRA9TVsSAnZWagAADsoP/3fqPwQLQl/bpVjg5cxP\nIbYPkY3c6h6wvNBPLPby/nooeQ+dqCUkj8+2mU1VnsEwVcBa7mY3QLt5Bmca\nyr4HHTZNVyHXEPEYP85adkT5SlC4l2uW6PcoSEqaZFf+oAVOWhHy+BFSz3oQ\nYyf0TObWLwZH5PGpkYtoff6FfHuF6HJRHf80UcnZUQUGQxMpeItre4HJMGeu\nwkq4UTBwCvFB/baJH+zPNH9vQcLqZeRu/z3+D5KdnMAPCm2AhU/VEhKVgvo1\nOWsu2Imu4C7Zj+p43mDf768FsMLS9bP3+fkxjvQNNFjEM8gOA2tswbV6sfeI\nV1fvj9xd8LZpHdna2p96MtQ8+84Hh3t/dxgWFutUb1UUnhihpvTU7uNgwhC+\nYutgkr0s7Cgw3NBiUyOOtsI50QsOppUQNiVuOPGRJrxndHFWyEtonBe3AQwu\nX4hLmYQeKppfxR1cWAWm+KrGv7O6+tP34bMDbUOg08hWUrzFoCKboFunthAU\nlUsZwJ9k3TnFyPJaziZNifgsvQD35OeeCTokSpXqvc4JWYikh0MRYWVAquXc\nYeryrs82NYgO2RXVYNwkiaJx2qAmBmijGzpQ3yIAa9mykv16HzTkuoXpEOTt\nL0vQ1j1cZtEZsFPOM0ZUmmHXDvnqy+isFm555PFOz11fC4tiRtQBXHZIbpSt\nb6Kb\r\n=cO7j\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4.0.0"}}, "2.6.0": {"name": "mime", "version": "2.6.0", "devDependencies": {"chalk": "4.1.2", "mocha": "9.1.3", "runmd": "*", "eslint": "8.1.0", "mime-db": "1.50.0", "benchmark": "*", "mime-score": "1.2.0", "mime-types": "2.1.33", "standard-version": "9.3.2"}, "bin": {"mime": "cli.js"}, "dist": {"shasum": "a2a682a95cd4d0cb1d6257e28f83da7e35800367", "tarball": "https://registry.npmjs.org/mime/-/mime-2.6.0.tgz", "fileCount": 10, "integrity": "sha512-USPkMeET31rOMiarsBNIHZKLGgvKc/LrjofAnBlOttf5ajRvqiRA8QsenbcooctK6d6Ts6aqZXBA+XbkKthiQg==", "signatures": [{"sig": "MEUCIQCQHntgGtT5DldwbJiHu3qu4qUM3UHkkBmBF3qkNQID1wIgW6oVqr8JbT6VW6FLZr+/3rlk/rqXBMe/6h8Hf8PNqFg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 60114}, "engines": {"node": ">=4.0.0"}}, "3.0.0": {"name": "mime", "version": "3.0.0", "devDependencies": {"chalk": "4.1.2", "mocha": "9.1.3", "runmd": "*", "eslint": "8.1.0", "mime-db": "1.50.0", "benchmark": "*", "mime-score": "1.2.0", "mime-types": "2.1.33", "standard-version": "9.3.2"}, "bin": {"mime": "cli.js"}, "dist": {"shasum": "b374550dca3a0c18443b0c950a6a58f1931cf7a7", "tarball": "https://registry.npmjs.org/mime/-/mime-3.0.0.tgz", "fileCount": 10, "integrity": "sha512-jSCU7/VB1loIWBZe14aEYHU/+1UMEHoaO7qxCOVJOw9GgH72VAWppxNcjU+x9a2k3GSIBXNKxXQFqRvvZ7vr3A==", "signatures": [{"sig": "MEUCIBhnXucK4OPoLLDCde+9AQTOCqqQOUxSn1BxmJoHoqeHAiEA4iljIXGD4nU/nw8YotjndgSq+dvT6oAw2ffYPX7NPK0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 60080, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2zPtCRA9TVsSAnZWagAAQicP/3+OTGtMdIsDR6cQSTU5\nu7vySR8/a5QM8CnMJ771HFUmGo7km8q87nqjSesxMDysNNgCKhrlKe3C2Wbe\nA4lRrzLh8XH7khgqEIgM3BkQvpPoUkHgMHbLyv1VTgvGy2vP1aBqoA/zrM1X\n+RZ04xGhq61gt2aKZisGZ//2rrgGXF+dJncfq7PwjyPf8UNkeeRrt46c6gUq\nSSUDJd7vGUGDCfpZgqfP7BEqy76BtRILYKh6mr8y/Yazk+MhQiefpM3PZEyN\nQY1Dz1f5zqYJiKCY5DSxXBnNTaP0IAQSiYppcls0E8gdUzaSq/QLKwCHMQ3m\nmOIO+YmrxtZfQ7GYy01lHBslkATVZ7jGMx4owYg5rHbwJYfyNuSfuhvYmkyz\n51PmZBkawFPwguWWxe2nmF6FULNx1HxjQt4+2lL+uwiUkDmqFslQdAMnoDrp\ntlRReLi/IgcNiDc8VFijVDPS31SsnWrPnBi+3j6lb++/NaPOO/Og7lnG9ucN\nSl3hXF/DOZ1aYiaB2efIrAawwi94t2h4ITuXA6cd37k0/LxnYRmDrvm01NMt\nKhyddCzZxPeNprYuEwqrIVhW6QAqeMo+ReLYW9vBZAUrBGWek32bpz4Jo2Uh\nGeHFP4z48ijCQymGYMEdKp+OV1hPfBo2X8Wha4H/bGfFfR4UDsPRDSUEKgkx\nwgGe\r\n=Ziw/\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.0.0"}}, "4.0.0-beta.0": {"name": "mime", "version": "4.0.0-beta.0", "devDependencies": {"chalk": "5.3.0", "runmd": "1.3.9", "prettier": "3.0.3", "mime-score": "2.0.3", "mime-types": "2.1.35", "typescript": "5.2.2", "@types/node": "20.5.9", "@types/mime-db": "1.*", "standard-version": "9.5.0", "@types/mime-types": "2.1.1", "@typescript-eslint/parser": "6.6.0", "@typescript-eslint/eslint-plugin": "6.6.0"}, "bin": {"mime": "bin/cli.js"}, "dist": {"shasum": "cdb2421a5cee72a8972fd9074d7039eb6ab40d1e", "tarball": "https://registry.npmjs.org/mime/-/mime-4.0.0-beta.0.tgz", "fileCount": 16, "integrity": "sha512-lomNMTINxQMU2dr87MFB8XEQKSNDQlSaxEy5q6aCKe1d7PJqsI5kBkjUxuvpgkh8bR8UAX9RzoTk4nrCwVO6zg==", "signatures": [{"sig": "MEQCIDKrHUCuJpkavf/G55xki5vxJuEyi9zAa7rlmche8qkzAiA9X6HMG1NTt+ySdGqlkavdacckDwstvH3PidZb9dxCOA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54414}, "engines": {"node": ">=16"}, "funding": ["https://github.com/sponsors/broofa"]}, "4.0.0-beta.1": {"name": "mime", "version": "4.0.0-beta.1", "devDependencies": {"chalk": "5.3.0", "runmd": "1.3.9", "prettier": "3.0.3", "mime-score": "2.0.3", "mime-types": "2.1.35", "typescript": "5.2.2", "@types/node": "20.5.9", "@types/mime-db": "1.*", "standard-version": "9.5.0", "@types/mime-types": "2.1.1", "@typescript-eslint/parser": "6.6.0", "@typescript-eslint/eslint-plugin": "6.6.0"}, "bin": {"mime": "bin/cli.js"}, "dist": {"shasum": "95654505b491bfae783e8364fcb80d70d49ff656", "tarball": "https://registry.npmjs.org/mime/-/mime-4.0.0-beta.1.tgz", "fileCount": 22, "integrity": "sha512-8/p99P1RV17prytee/A6D+8shNqdDzyvGJ/CVfiuXwh4cTsv3P3qGyaYSx2hdqnqbKKqYUfTC5zAjCtcd1BShw==", "signatures": [{"sig": "MEQCIHCZaSFeKinQKFcoUZetFfBABFvRKvQWqTMv2N200SyYAiAnd6jVITiC7ahtpPKaDffyBh6D4JUyRGhK/ag0LRjspw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 94438}, "engines": {"node": ">=16"}, "funding": ["https://github.com/sponsors/broofa"]}, "4.0.0": {"name": "mime", "version": "4.0.0", "devDependencies": {"chalk": "5.3.0", "runmd": "1.3.9", "prettier": "3.0.3", "mime-score": "2.0.3", "mime-types": "2.1.35", "typescript": "5.2.2", "@types/node": "20.5.9", "@types/mime-db": "1.*", "standard-version": "9.5.0", "@types/mime-types": "2.1.1", "@typescript-eslint/parser": "6.6.0", "@typescript-eslint/eslint-plugin": "6.6.0"}, "bin": {"mime": "bin/cli.js"}, "dist": {"shasum": "ae999669db9568c31a8da18dff6c696663f65486", "tarball": "https://registry.npmjs.org/mime/-/mime-4.0.0.tgz", "fileCount": 22, "integrity": "sha512-pzhgdeqU5pJ9t5WK9m4RT4GgGWqYJylxUf62Yb9datXRwdcw5MjiD1BYI5evF8AgTXN9gtKX3CFLvCUL5fAhEA==", "signatures": [{"sig": "MEYCIQC3ZdgBNYkRN4B5OocXYe30+pV6BpUmKm7itxjospt61gIhALMzBGdFSR+djEbdHIbqHpnzjLX67oikYUrNWgChQFfx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 93625}, "engines": {"node": ">=16"}, "funding": ["https://github.com/sponsors/broofa"]}, "4.0.1": {"name": "mime", "version": "4.0.1", "devDependencies": {"chalk": "5.3.0", "runmd": "1.3.9", "prettier": "3.0.3", "mime-score": "2.0.4", "mime-types": "2.1.35", "typescript": "5.2.2", "@types/node": "20.5.9", "@types/mime-db": "1.*", "standard-version": "9.5.0", "@types/mime-types": "2.1.1", "@typescript-eslint/parser": "6.6.0", "@typescript-eslint/eslint-plugin": "6.6.0"}, "bin": {"mime": "bin/cli.js"}, "dist": {"shasum": "ad7563d1bfe30253ad97dedfae2b1009d01b9470", "tarball": "https://registry.npmjs.org/mime/-/mime-4.0.1.tgz", "fileCount": 22, "integrity": "sha512-5lZ5tyrIfliMXzFtkYyekWbtRXObT9OWa8IwQ5uxTBDHucNNwniRqo0yInflj+iYi5CBa6qxadGzGarDfuEOxA==", "signatures": [{"sig": "MEQCIHIo+nnF7oMTVLJ+GrRMBU6BojIOm7vSm1KGQDOq9inPAiB8Hh8TdxemVlnnJbjxbLHpHSFd7lJZEyr3h5d6MLBP7A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 93596}, "engines": {"node": ">=16"}, "funding": ["https://github.com/sponsors/broofa"]}, "4.0.2": {"name": "mime", "version": "4.0.2", "devDependencies": {"chalk": "5.3.0", "runmd": "1.3.9", "prettier": "3.0.3", "mime-score": "2.0.4", "mime-types": "2.1.35", "typescript": "5.2.2", "@types/node": "20.5.9", "@types/mime-db": "1.*", "standard-version": "9.5.0", "@types/mime-types": "2.1.1", "@typescript-eslint/parser": "6.6.0", "@typescript-eslint/eslint-plugin": "6.6.0"}, "bin": {"mime": "bin/cli.js"}, "dist": {"shasum": "2da851f9b6545ed402525fa9075c440d76f20ef5", "tarball": "https://registry.npmjs.org/mime/-/mime-4.0.2.tgz", "fileCount": 26, "integrity": "sha512-rSR2L7RhEm0ifwn6lZAa+CcLy7EEl6POovp3QcnFHb/C5P4B+h6u+yCQPQaWzNdecHL8p85zRowrAjpF9F46Og==", "signatures": [{"sig": "MEUCID4YI4nvC6BIbaOe0l7G86bruE4d565gJ/4nX5ULUVcWAiEAvIpi/u6rzRZ+ZLu4invMj6dMLg1qvFEg01zJQWEZBZM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 100081}, "engines": {"node": ">=16"}, "funding": ["https://github.com/sponsors/broofa"]}, "4.0.3": {"name": "mime", "version": "4.0.3", "devDependencies": {"chalk": "5.3.0", "runmd": "1.3.9", "prettier": "3.0.3", "mime-score": "2.0.4", "mime-types": "2.1.35", "typescript": "5.2.2", "@types/node": "20.5.9", "@types/mime-db": "1.*", "standard-version": "9.5.0", "@types/mime-types": "2.1.1", "@typescript-eslint/parser": "6.6.0", "@typescript-eslint/eslint-plugin": "6.6.0"}, "bin": {"mime": "bin/cli.js"}, "dist": {"shasum": "cd4a1aa052fc980dfc34f111fe1be9e8b878c5d2", "tarball": "https://registry.npmjs.org/mime/-/mime-4.0.3.tgz", "fileCount": 28, "integrity": "sha512-KgUb15Oorc0NEKPbvfa0wRU+PItIEZmiv+pyAO2i0oTIVTJhlzMclU7w4RXWQrSOVH5ax/p/CkIO7KI4OyFJTQ==", "signatures": [{"sig": "MEUCIF/I/xPlnp+uKUhSdGNnDKenwkW4Qfb9UySpgzJ+lkglAiEAjjQhh6VBwI9MVo5insDnvpo+Wjmj0QVr8x/ONjvlg00=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 136681}, "engines": {"node": ">=16"}, "funding": ["https://github.com/sponsors/broofa"]}, "4.0.4": {"name": "mime", "version": "4.0.4", "devDependencies": {"chalk": "5.3.0", "runmd": "1.3.9", "prettier": "3.3.2", "mime-score": "2.0.4", "mime-types": "2.1.35", "typescript": "5.5.3", "@types/node": "20.14.10", "@types/mime-db": "1.*", "standard-version": "9.5.0", "@types/mime-types": "2.1.4", "@typescript-eslint/parser": "7.15.0", "@typescript-eslint/eslint-plugin": "7.15.0"}, "bin": {"mime": "bin/cli.js"}, "dist": {"shasum": "9f851b0fc3c289d063b20a7a8055b3014b25664b", "tarball": "https://registry.npmjs.org/mime/-/mime-4.0.4.tgz", "fileCount": 28, "integrity": "sha512-v8yqInVjhXyqP6+Kw4fV3ZzeMRqEW6FotRsKXjRS5VMTNIuXsdRoAvklpoRgSqXm6o9VNH4/C0mgedko9DdLsQ==", "signatures": [{"sig": "MEYCIQDDmO5/rEA+dPMptTxGMfCNyqCEdMk+sIXmr6TPxHxGVwIhAKMaVgDTGgm9GTAVnhZxSeJ5brXGGDabNrvRJOeBugPg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 136891}, "engines": {"node": ">=16"}, "funding": ["https://github.com/sponsors/broofa"]}, "4.0.5": {"name": "mime", "version": "4.0.5", "devDependencies": {"chalk": "5.3.0", "runmd": "1.3.9", "prettier": "3.4.2", "mime-score": "2.0.4", "mime-types": "2.1.35", "typescript": "5.7.2", "@types/node": "22.10.2", "@types/mime-db": "1.*", "release-please": "16.15.0", "@types/mime-types": "2.1.4", "@typescript-eslint/parser": "8.18.1", "@typescript-eslint/eslint-plugin": "8.18.1"}, "bin": {"mime": "bin/cli.js"}, "dist": {"shasum": "d521fad949526ffed7470be0f4222564de15bad6", "tarball": "https://registry.npmjs.org/mime/-/mime-4.0.5.tgz", "fileCount": 28, "integrity": "sha512-IDS8u2pHzy+1rCZT/JZZwj96BFmb2AUPrzK2cURorK6NfJCTnhrPevAxbDfC4TO+Xg2ieWM+K3Za+gtzSVaN4w==", "signatures": [{"sig": "MEUCIQDr/o4fczY1P54hqSb6/Gcc9nz5Z1bAK+omVmpq+YgZKQIgUy8HrXEszyywPR6BMNw+JhN3UDYzkV+t8MJHViQAhEI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 147206}, "engines": {"node": ">=16"}, "funding": ["https://github.com/sponsors/broofa"]}, "4.0.6": {"name": "mime", "version": "4.0.6", "devDependencies": {"chalk": "5.3.0", "runmd": "1.3.9", "prettier": "3.4.2", "mime-score": "2.0.4", "mime-types": "2.1.35", "typescript": "5.7.2", "@types/node": "22.10.2", "@types/mime-db": "1.*", "release-please": "16.15.0", "@types/mime-types": "2.1.4", "@typescript-eslint/parser": "8.18.1", "@typescript-eslint/eslint-plugin": "8.18.1"}, "bin": {"mime": "bin/cli.js"}, "dist": {"shasum": "ca83bec0bcf2a02353d0e02da99be05603d04839", "tarball": "https://registry.npmjs.org/mime/-/mime-4.0.6.tgz", "fileCount": 22, "integrity": "sha512-4rGt7rvQHBbaSOF9POGkk1ocRP16Md1x36Xma8sz8h8/vfCUI2OtEIeCqe4Ofes853x4xDoPiFLIT47J5fI/7A==", "signatures": [{"sig": "MEYCIQDcDA+8drml0tMFCNxDG4+hw73Vjn31lRszlbQqXOsWawIhAMNlI9q74nGsMnO7ZaSgESluuLzxje1KuuvkX1yIOtrW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 105887}, "engines": {"node": ">=16"}, "funding": ["https://github.com/sponsors/broofa"]}, "4.0.7": {"name": "mime", "version": "4.0.7", "devDependencies": {"@types/mime-db": "1.*", "@types/mime-types": "2.1.4", "@types/node": "22.14.0", "@typescript-eslint/eslint-plugin": "8.29.0", "@typescript-eslint/parser": "8.29.0", "chalk": "5.4.1", "mime-score": "2.0.4", "mime-types": "3.0.1", "prettier": "3.5.3", "release-please": "17.0.0", "runmd": "1.4.1", "typescript": "5.8.2"}, "bin": {"mime": "bin/cli.js"}, "dist": {"integrity": "sha512-2OfDPL+e03E0LrXaGYOtTFIYhiuzep94NSsuhrNULq+stylcJedcHdzHtz0atMUuGwJfFYs0YL5xeC/Ca2x0eQ==", "shasum": "0b7a98b08c63bd3c10251e797d67840c9bde9f13", "tarball": "https://registry.npmjs.org/mime/-/mime-4.0.7.tgz", "fileCount": 22, "unpackedSize": 108025, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCeN7rIllTJpdJI1Ovprh4R6o0rftLHcQaWyucgViQy/QIgR/H1OSgvPvI6B/0Hf62PipMiatN7PmH688OPoM65Td0="}]}, "engines": {"node": ">=16"}, "funding": ["https://github.com/sponsors/broofa"]}}, "modified": "2025-04-03T14:31:47.136Z"}