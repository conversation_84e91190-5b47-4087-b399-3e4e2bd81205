{"name": "ajv", "dist-tags": {"4.x": "4.11.8", "beta": "8.11.1", "latest": "8.17.1"}, "versions": {"0.0.4": {"name": "ajv", "version": "0.0.4", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"mocha": "^2.2.5", "JSON-Schema-Test-Suite": "git+ssh://**************/json-schema/JSON-Schema-Test-Suite.git"}, "dist": {"shasum": "c10b1df9b45809e005f01f6b11ec896482433578", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.0.4.tgz", "integrity": "sha512-LhWIj0z6EmAyUeDmvTlNVCM0uF+ZHdq2snlQb7awdqsLpMH9ENc3RN1UE4zIiER9nzT0oG3GUOHcMvMV9m0ytw==", "signatures": [{"sig": "MEUCIQDomK4dWHZP50PDTYb7EkjhjNf+Oq2kLYEUMs8OkiI47wIgXoLLAuLAN877GsXpmLytFRl8/Gqdskt6ZO1BQSb/oew=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.0.5": {"name": "ajv", "version": "0.0.5", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"mocha": "^2.2.5", "JSON-Schema-Test-Suite": "git+ssh://**************/json-schema/JSON-Schema-Test-Suite.git"}, "dist": {"shasum": "ff59bfff3a593983b8bbf9526eba284e945198ca", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.0.5.tgz", "integrity": "sha512-kJEX3o+pQ6nSfr9ZOEvhDqVGqZO3dJbKCUXOpYqUWR+tSyHhuPjUKpQLMy1anUL1wJX+UqhEdiPQOcRv56aUxA==", "signatures": [{"sig": "MEUCIQD8q1tYnFxUnYULyZU1GXpMGkAO6slciwd/h1Pqo28MmAIgUWLx/A9zD1LJAfa5iZ3bEoT9fdzm3fi5T552AVpeoMg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.0.6": {"name": "ajv", "version": "0.0.6", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"mocha": "^2.2.5", "JSON-Schema-Test-Suite": "git+ssh://**************/json-schema/JSON-Schema-Test-Suite.git"}, "dist": {"shasum": "351cec5f18a1bcb8237f9739dc49111263cd8a43", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.0.6.tgz", "integrity": "sha512-RCsa9CkFZx9eHtUgTbgf8v4LUKVMdqTZ1pYbrBoKk/WdHUN15o8hwBOwZtjBrx9kp9u1HB9FoE831kAjxsINew==", "signatures": [{"sig": "MEUCIQD9abEH7qoEinZpvQw4/C0Bgwiaf1TmYsq2n0YkXa/tIAIgIDYtvXC5MRpdQOWQ4bDeMIwkoyu9sxE59vDBEVH5hU8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.0.7": {"name": "ajv", "version": "0.0.7", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"mocha": "^2.2.5", "JSON-Schema-Test-Suite": "git+ssh://**************/json-schema/JSON-Schema-Test-Suite.git"}, "dist": {"shasum": "7ec1188a852d1e55a48063a75b40952178bb160e", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.0.7.tgz", "integrity": "sha512-MjGaCP2kUNQCeYtvbFgm1s1f+doM285v4Lpbw0FA8jvLCyHG9ksoS6p9kFaBoSUygS0v1cyQtnrfcp3M33hOHQ==", "signatures": [{"sig": "MEUCIQCMwrAgyOSACrY2sM3fW+pmsOW510JvYQ15ydrLfgwuLQIgARV7OCKRL+Ale72RQ1q+Dk2xhwRM0XNMI0cZ0nnh2i4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.0.8": {"name": "ajv", "version": "0.0.8", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"mocha": "^2.2.5", "JSON-Schema-Test-Suite": "git+ssh://**************/json-schema/JSON-Schema-Test-Suite.git"}, "dist": {"shasum": "365c94f9f6ce0b2152d006a5c1dad2fa7929cc94", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.0.8.tgz", "integrity": "sha512-OQddU+5hE6HUdgOevKl963n6iX6muzi9ogTTiqDezbXFZVZ2htq1HtDGQ6sdgfSRVnYug114DHni08ebPMGGFQ==", "signatures": [{"sig": "MEYCIQDuLnQMeRPQF9A1dL6yIRJ8fJhsomexysHMTGqui5C9zwIhAKQy5J8CmfkgYO5ssIWpQdM8Ah2MA8FTOR5qDi+nUq6d", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.0.9": {"name": "ajv", "version": "0.0.9", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"mocha": "^2.2.5", "JSON-Schema-Test-Suite": "git+ssh://**************/json-schema/JSON-Schema-Test-Suite.git"}, "dist": {"shasum": "18776686ec87bf6888f81b8a720c1dfd92beef70", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.0.9.tgz", "integrity": "sha512-ap45c+EUYy2o9nrDUBBNgeaNiq1KPaUxu6St8htEUFC7W/D/IjHrdO0bt3b0CA8kQWpNyqCGNij4adpMTW5N2Q==", "signatures": [{"sig": "MEYCIQCsFMhrmkeb/7VrWnckLNlz8jpSllfLtWUCygIFbQzSSgIhAPyShRHrBtqRrfdLK8mqhxgJ1tYusyua7A4LOrD9JibH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.0.10": {"name": "ajv", "version": "0.0.10", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"mocha": "^2.2.5", "JSON-Schema-Test-Suite": "git+ssh://**************/json-schema/JSON-Schema-Test-Suite.git"}, "dist": {"shasum": "9c6d4bcb58eab1d01cf3cc291343a4ab2f16c7e7", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.0.10.tgz", "integrity": "sha512-U8Odxioe+41SCWO1YjJVHGRPXXqrQd3kTEHYiUIyABwWglHNHT9s/sK2EFcVGPNO1ZARYMfYQsGPTwRfrLN+hA==", "signatures": [{"sig": "MEYCIQDQrnOGZRQ9SdvnRgQBVyTU6R9s0vWoYuSf+BYK629spgIhAOrk7ba+wfElJTMXhF7Kd05CBliyvOQCARQAxAr9Dg3k", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.0.11": {"name": "ajv", "version": "0.0.11", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"mocha": "^2.2.5", "JSON-Schema-Test-Suite": "git+ssh://**************/json-schema/JSON-Schema-Test-Suite.git"}, "dist": {"shasum": "cf909bf5478ca1b4f42777cb4ac0577e2be5d17f", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.0.11.tgz", "integrity": "sha512-EL4ej5FQ8cM9aZLa5sbDK1RaxZuRoG0A6nv+P/+cyU3lrHUmUZEIwrwrGpHN3XX+cbqmZ1x8lPe5yj2cfDSzag==", "signatures": [{"sig": "MEQCIG5jBA4xcaBQWnbXQ6ROHsykHrh56Vx3JmlnJvES2UHyAiBIa++KboR/xNBRd8Okt0OjQXQWXQmcbtPJMkUe7llPEw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.0.12": {"name": "ajv", "version": "0.0.12", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"mocha": "^2.2.5", "JSON-Schema-Test-Suite": "git+ssh://**************/json-schema/JSON-Schema-Test-Suite.git"}, "dist": {"shasum": "04a9c98e3cfdeb73d77c3c5d79e41a9bcd6972f0", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.0.12.tgz", "integrity": "sha512-IaKu5hqQIeZ4NM9ROD3DX9JCiNigeGqF3RPSrhaM1hROqIlQ433py4pg3S2pOuWGkFTFW4aJY1bIJSyebkm9eQ==", "signatures": [{"sig": "MEQCIHEn/kaC6ytNst3lfx40DDK/vTnMHqdkpA1sPrhs51MTAiA+wGyrrGjx+IE3pnti0ZKdsqKUO5q+Rgvd3ZXXo/04ww==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.0": {"name": "ajv", "version": "0.1.0", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"mocha": "^2.2.5", "JSON-Schema-Test-Suite": "git+ssh://**************/json-schema/JSON-Schema-Test-Suite.git"}, "dist": {"shasum": "61c4827ad18049abbd6d29696c1b95b1ceae5641", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.1.0.tgz", "integrity": "sha512-1/qN1+zuK/SrDod00uoC6V8Y/cJZt5zyWyMzJpbtdV7o3Tw7o7h8tbMH2So9Mi0RoH6cro2RApc+uKyQdeUrpA==", "signatures": [{"sig": "MEQCIGLhWwCHla6ixW7yDnxjy7CivwYOb4g95DkLLEgp6VbqAiAfKLoVJ7VxFp0j3Zz08huYRmjRdHR4AHQ3iNG7JkJlug==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.1": {"name": "ajv", "version": "0.1.1", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"mocha": "^2.2.5", "JSON-Schema-Test-Suite": "git+ssh://**************/json-schema/JSON-Schema-Test-Suite.git"}, "dist": {"shasum": "2c1c0c523b933b665e36661e5563a812817c89c2", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.1.1.tgz", "integrity": "sha512-/f9bk16ytLKFy320I39pqAnU9EYkqwYeiyejWEGshd+kydqWRvCs3/HJy2Vs/dlP1NJAWN6jrlB4hINbtxaKFg==", "signatures": [{"sig": "MEUCICq6w40+PIL87lDppyzWWa/lrNeHIecMjX7MeEtEVHQXAiEAsB4+0VLfTDAjr7+lWb+MXW9MdtqA6/WHZLLvZggqC/M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.2": {"name": "ajv", "version": "0.1.2", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"mocha": "^2.2.5", "JSON-Schema-Test-Suite": "git+ssh://**************/json-schema/JSON-Schema-Test-Suite.git"}, "dist": {"shasum": "0b79bc422e9f566b86f38e5e872d386546896a85", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.1.2.tgz", "integrity": "sha512-pe24mYIqtpWJj/Ck6gS/xGX336JQbc4t++Fw+7huryrCu4OfUA2/LBY5HDMA4CA5AphHyrdYbxFSMRPkmIGDZw==", "signatures": [{"sig": "MEYCIQDlnATmdRE9NDk0/2cZnfwU1MbrIB5kWeMbUvMA7NosBwIhAND72S7fEiWhjk+MDwgVBmWpZUCYDAVEaJVTiRTVUxqZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.3": {"name": "ajv", "version": "0.1.3", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"mocha": "^2.2.5", "JSON-Schema-Test-Suite": "git+ssh://**************/json-schema/JSON-Schema-Test-Suite.git"}, "dist": {"shasum": "36764f041954a1984f16cc65f0ed323dff896b55", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.1.3.tgz", "integrity": "sha512-wjPbGNQpC5+0eewNqBgwkRKdSoqaXVh4NktZ6hlDwnUCNfuOxZWo4wXcJuIbBuSDR2s+z7i1SZBkKFqLbXHU8g==", "signatures": [{"sig": "MEYCIQCzF2TgqSyCv5wERtkvSy+Gb3O9cgHGDtf19MIZLibdDAIhAK+TOCAR9dc7DrzP0UzM4K4d1Obr8FDsDBV6152lWMOf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.4": {"name": "ajv", "version": "0.1.4", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"mocha": "^2.2.5", "JSON-Schema-Test-Suite": "git+ssh://**************/json-schema/JSON-Schema-Test-Suite.git"}, "dist": {"shasum": "a1fbc17647dad24a78a48afc38fbf8424e93e789", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.1.4.tgz", "integrity": "sha512-8F93AzSCspXRbwZfhRr+pCF7UD/+N/tgCmoVfWGxfxxmKvxejypCHtxA3jF7zj493c7SVzq0+LP5qU6g6fpWdA==", "signatures": [{"sig": "MEQCIFt0CLoETJzThJ11fNurJgs0jkSs1AKrSWdFNfuSNeFaAiAC/517o/hZr2D9jHjUv1mLBY8QrSDOv1SQvp3Vmu52cQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.5": {"name": "ajv", "version": "0.1.5", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"mocha": "^2.2.5", "JSON-Schema-Test-Suite": "git+ssh://**************/json-schema/JSON-Schema-Test-Suite.git"}, "dist": {"shasum": "41be6f7b79bc2e60bd23a93386c9fd96f312e332", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.1.5.tgz", "integrity": "sha512-X3KHgjJN/dhWk+JBWP1NhTZrbewDISnPCcq9oQft0aE2mId8TkANTZSxPPBIt3Rv448/8e+UClp5niNSeGVvwA==", "signatures": [{"sig": "MEQCIG2btMV5xJ5eL+2hHiAU/AGj1SBkyyYUaD910AnV+ffWAiBQe4MQTgQ1ES5v0lTuxD1xvpqswdklGMiBrIpjM8Xyog==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.6": {"name": "ajv", "version": "0.1.6", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"glob": "^5.0.10", "mocha": "^2.2.5", "JSON-Schema-Test-Suite": "git+ssh://**************/json-schema/JSON-Schema-Test-Suite.git"}, "dist": {"shasum": "a9f8e95b0a5f5cbc1a26c480303b663d80cb83f1", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.1.6.tgz", "integrity": "sha512-VvYU2xGXhWYC8hdDqhio17thxkTpZXpAxrLhUxjTfqcJC/q+Y9gw0h9yCc23nu5P8AVOkQz3XZiMKrRmt4YRYA==", "signatures": [{"sig": "MEQCIDwQplHDiFJzRrkOYoeCncRKpr1KQlAg9xjOu8nVHN3yAiA1Z3NW+KCpQM21Vb6OgLpWUZjKhxBzZQDFOZaZ7yYAPw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.7": {"name": "ajv", "version": "0.1.7", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"glob": "^5.0.10", "mocha": "^2.2.5", "JSON-Schema-Test-Suite": "git+ssh://**************/json-schema/JSON-Schema-Test-Suite.git"}, "dist": {"shasum": "7e6838d21cb1e3ac75f73df851e672f92992a420", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.1.7.tgz", "integrity": "sha512-vxItE1ehGZkwisML6iVFZLLnoH0S5F0BCeEeGiNnWUJ4k55brpZPkSfCOvU+iPGDzYS6Ooko+I4kUCVtMNFnaQ==", "signatures": [{"sig": "MEUCIQC8ala6KYkRHQUkeTXT1RIAH/lOAMYvGK827yM+YQ5xVAIgJP/DFBFy05bKWXjBRgldRtLscJJSR2c5f3ETY75cC9U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.8": {"name": "ajv", "version": "0.1.8", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"glob": "^5.0.10", "mocha": "^2.2.5", "JSON-Schema-Test-Suite": "git+ssh://**************/json-schema/JSON-Schema-Test-Suite.git"}, "dist": {"shasum": "90ffe13c988cf463e4f882fdd0cd305c8092187d", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.1.8.tgz", "integrity": "sha512-j7c6F4jvTQyhn2dpVp4G3delswiITOQzzoqzgYc/b4BuzcMDwPoDhhn08VsImWgNUTGn0XNOZeDArLSRYmUmVA==", "signatures": [{"sig": "MEYCIQDuHgm2yVCR2IW1oLOKPAXU0TIgSfbKmpsZGWX6ZfPehwIhAOdY+hYsYpAnUCqQ93BqXWXjOphX5xFPyo9+4J5GmIgN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.9": {"name": "ajv", "version": "0.1.9", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"glob": "^5.0.10", "mocha": "^2.2.5", "JSON-Schema-Test-Suite": "git+ssh://**************/json-schema/JSON-Schema-Test-Suite.git"}, "dist": {"shasum": "e2368e97d7e8cee5e18a2a8477b4225748bfb53f", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.1.9.tgz", "integrity": "sha512-ZRFRU4/OzxieWLaUseLkY0kZBePYLbqtACQoOnLiIemU1gPZFW/5BtZ39i0fL+D2gqbdIeNyzlwO9A8dANmSZg==", "signatures": [{"sig": "MEUCIQCt8NULaCm7cjLVMXujoX+f04cnMJMr+rYIrRKdGhR0+gIgcngFBBegEYoDxvMikZSxVlVjKUF9cP/hhay1h7vHrPE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.10": {"name": "ajv", "version": "0.1.10", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"glob": "^5.0.10", "mocha": "^2.2.5", "JSON-Schema-Test-Suite": "git+ssh://**************/json-schema/JSON-Schema-Test-Suite.git"}, "dist": {"shasum": "594152455cde16629da52c0180ff03f2e945ef97", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.1.10.tgz", "integrity": "sha512-6oAefkLI/qyOipQR13sekjPgL0zawzmkgdgtowkQk+3YhJEt9JHd2+1ShbfK7BEFVRCl2sb6Uwmw2M2ejfbP8Q==", "signatures": [{"sig": "MEUCIHG287oKS9EtctLfSXS9lcy7S5O0ZKHOJOOeE5trteJzAiEAmeYPxmTABvMzUOyzDp1OVg3zZhKwoUd3L5qcTEPnWA0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.11": {"name": "ajv", "version": "0.1.11", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"glob": "^5.0.10", "mocha": "^2.2.5", "JSON-Schema-Test-Suite": "git+ssh://**************/json-schema/JSON-Schema-Test-Suite.git"}, "dist": {"shasum": "f884ca43885c44adbd9562ef331f4fb00b098ad0", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.1.11.tgz", "integrity": "sha512-OnOu3GpTML4Ggx0btqjwYKqwtmdOGJI17obVSzrKokMprdm7RkK7IBSoZ36P9RP0KK3xULCNLtl+rG62pzGwMg==", "signatures": [{"sig": "MEUCIQCZcbg0X3vu3Pyfe0L6/8ZokemQlsWD4gn+0waawf84uQIgbuakNAU3a32M3bP1/3YXT37EAvUVTKcCHlwxhrjzxuk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.12": {"name": "ajv", "version": "0.1.12", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"glob": "^5.0.10", "mocha": "^2.2.5", "JSON-Schema-Test-Suite": "git+ssh://**************/json-schema/JSON-Schema-Test-Suite.git"}, "dist": {"shasum": "007c63e295f7f820d6affef57de2c512749e27b8", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.1.12.tgz", "integrity": "sha512-dnZ6pp1BDC/pI8tc09klIAlKnigX9uBWLe204/3fukyPnhowcPhB0+zMBvF9++xAeR8xahJ/W3lcbdisAqpP3A==", "signatures": [{"sig": "MEUCIQDdCf/ErXd6R8ke/CtdX0S0Hq04yJL9L1m7mwcetIhbFwIgb5RGRHZLw08kbEocqPzy4wzgVa4WkCbl/VeTlewIEGo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.13": {"name": "ajv", "version": "0.1.13", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"glob": "^5.0.10", "mocha": "^2.2.5", "JSON-Schema-Test-Suite": "git+ssh://**************/json-schema/JSON-Schema-Test-Suite.git"}, "dist": {"shasum": "2726615d782c6ec08ac8a73d87946e6527230477", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.1.13.tgz", "integrity": "sha512-MajVC3luUMVSJRkMkQBjoa+PYoAYMTHUDsS3MqN1Sn4pbkEYdSrgWa7domh2VFyI8tyIRBxRl9eAOupWnqnTKA==", "signatures": [{"sig": "MEYCIQCBYQd9BkiKKkF/9JLlKTtjktRLvi1nbPNoPD8fYs79CQIhAIlLu67CBjIJQfUF9jXfn1I6pmSf72NBDzeVhYf2vhfs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.14": {"name": "ajv", "version": "0.1.14", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"glob": "^5.0.10", "mocha": "^2.2.5", "JSON-Schema-Test-Suite": "git+ssh://**************/json-schema/JSON-Schema-Test-Suite.git"}, "dist": {"shasum": "2305806949c845668cf9986bc0724eb5c9f8fb92", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.1.14.tgz", "integrity": "sha512-uwoDWNTi2zUaZnx6o9dnlpIV132Mi/dp06EvR7vc++/jypWwWhd0yIegDwD1PU8sH1Ye0kyhWp/vEDLiT4857w==", "signatures": [{"sig": "MEYCIQDV7UW0EgQKcFHl7p1+paL6qNLbd3HDDGMN/H6Kk8lIjwIhAMF8pX/i5M2bOqAJnQRuOUcjHq4kID6YcHFCSqH8Cjlj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.15": {"name": "ajv", "version": "0.1.15", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"glob": "^5.0.10", "mocha": "^2.2.5", "JSON-Schema-Test-Suite": "git+ssh://**************/json-schema/JSON-Schema-Test-Suite.git"}, "dist": {"shasum": "77eec6af77d45b3a5da04743fb67d7e987336044", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.1.15.tgz", "integrity": "sha512-BTphpcy/RmMmUir5x5ov1StWTCf07t6vYSHJSXDOqNaqUHKHSn1Occ7oDG0sG/9HfQoOhjVmlC08X8dcud8GzQ==", "signatures": [{"sig": "MEUCIQCLl9J9yCbf/zGZJQUYT4KuGsdTUjwj9vJOJLX29H+TcgIgGSk4IYqCMkDZILjcJRpPyIfF7OpyWPAabo2s3rqwAsE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.16": {"name": "ajv", "version": "0.1.16", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"glob": "^5.0.10", "mocha": "^2.2.5", "JSON-Schema-Test-Suite": "git+ssh://**************/json-schema/JSON-Schema-Test-Suite.git"}, "dist": {"shasum": "6c7f39b60622f735abbde084b3b4d57f1119a2a9", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.1.16.tgz", "integrity": "sha512-vqQXyZEDIwOt6NB+gODUozeEtk63MfrwizU38lQGQOacUrzbFCxMv3OfxGVWyxoLdlptWmNU+GJtEJme0rG5Ug==", "signatures": [{"sig": "MEUCIDG8Cv0+nM4QaGcN0Udv+TK7a36f2xAzpc6uF9gfYWMlAiEAideq4URLQHuMbzOv+uRt35pfoftZFHbj/P1abgaBHEM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.2.0": {"name": "ajv", "version": "0.2.0", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"glob": "^5.0.10", "mocha": "^2.2.5", "JSON-Schema-Test-Suite": "git+ssh://**************/json-schema/JSON-Schema-Test-Suite.git"}, "dist": {"shasum": "b9955a9456b7fea300c0e4a5a2850ad2327d3973", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.2.0.tgz", "integrity": "sha512-f8n74mq7DfSlsyrUIMhE/iOCSLctUiwaU7mTtFmzbOUhdEjJ1fI/OHWofaRK9agXfx+J1D1YhzWAgGO2AQPUnw==", "signatures": [{"sig": "MEYCIQDgpiN33izUReNVQxnFrISBdzPCUgRnBgkte0ae77XvuwIhAI7+7yJRtKVlxrLQ4PHQhHAtOR75lYcF3RuuJ0uIlEQ2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.2.1": {"name": "ajv", "version": "0.2.1", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"glob": "^5.0.10", "mocha": "^2.2.5", "JSON-Schema-Test-Suite": "git+ssh://**************/json-schema/JSON-Schema-Test-Suite.git"}, "dist": {"shasum": "96f10b8ed0e413f93d46b15d24eea80d995dae4b", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.2.1.tgz", "integrity": "sha512-ZsxbFwcCssFqFGoTwHpvZg8kt5juJiFPmC7LqcGtSsCHb72y4RYWHaxIbYyu7nRSQ8RmOkbzr/+TwcsPrpvUmg==", "signatures": [{"sig": "MEUCIDymZW5cj9ipH7jdQZ+p9CRRb42HAd5jKdWBBHS6SK8BAiEA73F3OAd2EYTDMJuO1SII0ajxtfTIWfq6QF8ARyNwfUI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.2.2": {"name": "ajv", "version": "0.2.2", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"glob": "^5.0.10", "mocha": "^2.2.5", "JSON-Schema-Test-Suite": "git+ssh://**************/json-schema/JSON-Schema-Test-Suite.git"}, "dist": {"shasum": "8aa98412de47b01eb989fd5a5cfbeeb2874466d2", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.2.2.tgz", "integrity": "sha512-2pmq4YWqN5yEhSUC6whVM5dnCiVXgSEde3eXOPurMmNnO5hAiD4opl2p7zwHKEQgZvc19yBvK6DNkluFjh4kjw==", "signatures": [{"sig": "MEQCIG1Q6oiZ28Ga1RmSHUnkQQWSISl3wLOgu9W6pLDyXMuyAiBkQV1Lf08FfkqNLL7AaTEcEnqK9/MoG+OceWn/ttxjVQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.2.3": {"name": "ajv", "version": "0.2.3", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"glob": "^5.0.10", "mocha": "^2.2.5", "JSON-Schema-Test-Suite": "git+ssh://**************/json-schema/JSON-Schema-Test-Suite.git"}, "dist": {"shasum": "4269ec8baa110fe9f5d45e6d9e15636d4c77a73e", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.2.3.tgz", "integrity": "sha512-hpoirsLIZ8kw3OIV/JSZMVaKea7VUsLDl6wmkxoYPMVbMp8RuioPrPbKc7bNSnmQGVKOizXxUXQG80uS1JNPKg==", "signatures": [{"sig": "MEUCIQCtCdFMeMZ3kRvKDTDwk83o7ITCTH/ZfixvMnL+6g4NtgIgFzWiUxD+7tG+JgJyj5PMrziu1gxskOIKbrx972kto9k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.2.4": {"name": "ajv", "version": "0.2.4", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"glob": "^5.0.10", "mocha": "^2.2.5"}, "dist": {"shasum": "ee0069828424dc3ffa08c57237e98f8d1dc6938e", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.2.4.tgz", "integrity": "sha512-AcCUp1XFs0mJEtizQCZ2HPTH03UHZzqDWW4LNnnIKZkMbU6tgYb+iHs+p/k+SdvwO984x3qOI4H5XKnMNGZhnQ==", "signatures": [{"sig": "MEYCIQCoOdav5mm5JQK/c0FhR9NX+Fmp2JVdbWYq45AB7BRhVwIhALpalUgoGbJ846k+7gfq3/6oodRbWZrMIr/Ls7kWpMN1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.2.5": {"name": "ajv", "version": "0.2.5", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"glob": "^5.0.10", "mocha": "^2.2.5"}, "dist": {"shasum": "0651458348692265779f550b190f3a5e81b55510", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.2.5.tgz", "integrity": "sha512-myemGBdByT53KubmprDnLLHIBKYS42xP2BuNEREmmqYObd7mCbty7BXJb+fXhYPh8g4kdWB2+rDGQWZOCHpfQg==", "signatures": [{"sig": "MEQCIDlFGlZM1+nqKcuQoCNZiGK4OgDzDJYi4G6++H6+IpW6AiBZ+Eq6/IICbCEce0hIB5ouJAwhq7cBcrjS/hlEx6e2YA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.2.6": {"name": "ajv", "version": "0.2.6", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"glob": "^5.0.10", "mocha": "^2.2.5"}, "dist": {"shasum": "1f123c36e496b342f02cb4ecd34a6e1b05b281ce", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.2.6.tgz", "integrity": "sha512-mZS+eBUUaY9e9GSx7oFMCYbqpmlCEGoFuO/+WIoN6Jm2S8QfDoa//AqZzg8ykDarE/Q1USy2WzWDPIhaIGnqfg==", "signatures": [{"sig": "MEQCIHTTw1p+jJ/fBVigXsOkWn2Fl4J85uflCFh6prjjYlH2AiBv344psQbvwAIqQjuO3R0iUA9AcGT4gdRpAOFyxjdf9A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.2.7": {"name": "ajv", "version": "0.2.7", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"glob": "^5.0.10", "mocha": "^2.2.5", "js-beautify": "^1.5.6"}, "dist": {"shasum": "611a22a669cb46d0acb12c03752650d65d895515", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.2.7.tgz", "integrity": "sha512-Mi8VL801CXxbIunAQHtbRJr3/bUc6+MCB5YW/YyXExYTT7YnQyY0dWAMC1G2/3pUkGdhbih1S5mSO5j+Djd+xg==", "signatures": [{"sig": "MEUCIQCvVqkkCiMAtKT8ZbXy53BEzWz6ft+y+V9U0vRjVdwZuQIgMdehVR5IgvHTnepW1+oHTI7nSOFieDv/swqOINnotio=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.2.8": {"name": "ajv", "version": "0.2.8", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"glob": "^5.0.10", "mocha": "^2.2.5", "js-beautify": "^1.5.6"}, "dist": {"shasum": "18a2ea749ef3341102e966f3767e93c98ea85f70", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.2.8.tgz", "integrity": "sha512-nIlDMsFE8nUCUmPKZHbFWFtFqqV3Rsbjf4Sgqong4x1jqVXxbbcqQ7j7LbQQ7Wze+NcUSXzoxitQG3ECaPKUog==", "signatures": [{"sig": "MEYCIQDlEL72Nv0wxhONOH0jISpriX+utrsp+E/22G3j580HAAIhANltyssAi0QNCECRggWgu5Z+cQbfgFwPvzW7nLo1q5Bl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.2.9": {"name": "ajv", "version": "0.2.9", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"glob": "^5.0.10", "mocha": "^2.2.5", "js-beautify": "^1.5.6"}, "dist": {"shasum": "b1a5c39c1d509e976107b0bf137b83b3a6ca21af", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.2.9.tgz", "integrity": "sha512-dgmu5kNr4NfZGMdeLsS7RSPxyChpgu5GhB4hOpUFwphEHC56hm/PJUXUx5lEODBF7xg4UaR5OUbvI4VDmlDL1A==", "signatures": [{"sig": "MEUCIQDAhXBK1CJIve18Mi5UMBUnV+2N4FVqSYhJI+sRan5nvgIgaOMJ1Xw1wHh0gsOfybXzaO4POIYJPxUBh9M0zqh+k0E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.3.0": {"name": "ajv", "version": "0.3.0", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"chai": "^3.0.0", "glob": "^5.0.10", "mocha": "^2.2.5", "js-beautify": "^1.5.6"}, "dist": {"shasum": "f291a7bede43fe032cf3511b170be0e77025ce63", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.3.0.tgz", "integrity": "sha512-EGIWoBdZcGYcq4cuaf0zp4TWE/YO8oifeRHTc3aAnFshT9NfZR41RfE1ZA0Zos1AF/CuPK1lrVnbPWMvZen2XA==", "signatures": [{"sig": "MEUCIC0V73lR2us7vbpfgJpnK50EiejwDVxlNMsnXkKijULqAiEAnOTnQqbIZEUIN9B8+d8HocjperT+RpyBiXUFySgdUo0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.3.1": {"name": "ajv", "version": "0.3.1", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"chai": "^3.0.0", "glob": "^5.0.10", "mocha": "^2.2.5", "js-beautify": "^1.5.6"}, "dist": {"shasum": "d6b5e36f15b836bb80f0d3136ff29ea7ae40db4d", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.3.1.tgz", "integrity": "sha512-weYJPVg7rs7jP5vOJwqMoRI/rALWldCXBxcAK/S5ItRrcxBN4TRPKNFV02j2WCw09CSzOdEZzCQcbgZ6wPjnVQ==", "signatures": [{"sig": "MEQCICqqb9W8WK3XGolRWuCdfuGnvnhfDihasIYKQ/lQukcyAiBYuFJvXwxCY41KHVmrohqwM34BntG2q8EdaVag8hgV0Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.3.2": {"name": "ajv", "version": "0.3.2", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"chai": "^3.0.0", "glob": "^5.0.10", "mocha": "^2.2.5", "js-beautify": "^1.5.6"}, "dist": {"shasum": "c8cc0f1b26b5e804f4fc087332d2c17a76166b03", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.3.2.tgz", "integrity": "sha512-qVdP8z08gH9PaSRKTNBHp6DejclcIyfsVIhK/cKDtBYpzf8NK0iCEx2+sJRt/KU3Coa6nz8lmFZQVLx2Ek8dyA==", "signatures": [{"sig": "MEUCIGAl3JRvkn1/qI1Y6DEzOeDzw45q6Wyr6wy/kMze95NqAiEAtM1dc7gamko5PsbFnFALTCzLbWDDoZVEl5ahOAsMK+M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.3.3": {"name": "ajv", "version": "0.3.3", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"chai": "^3.0.0", "glob": "^5.0.10", "mocha": "^2.2.5", "js-beautify": "^1.5.6"}, "dist": {"shasum": "830e64b8e2d1ca43a5962fb8c7df39ea880f0e5b", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.3.3.tgz", "integrity": "sha512-4fg9hHRDY++Wg4Mkz2DDUAjoQ8C/DFyLuTj++nQcmtoCX+RW/n7Mn28IpGiwaT7VwqGtgk/h7I2NZTPTj/QhTg==", "signatures": [{"sig": "MEQCIFgW9F8DIMixDsAnVKUREm9wnOIw/kfpSb+a3KLPg8IyAiBuYnbHYgF7w3Fku8vcS1A5/5RGKKbAAu98haQNwCV5TQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.3.4": {"name": "ajv", "version": "0.3.4", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"chai": "^3.0.0", "glob": "^5.0.10", "mocha": "^2.2.5", "js-beautify": "^1.5.6"}, "dist": {"shasum": "175ff5d8623aa70a6e6552b916f2dd3e51fd9a85", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.3.4.tgz", "integrity": "sha512-CETpTXa+ID2lZ7YWv44lR1qsyRKeidbEYxwGag9DHoS+QZKdFBbe1pldywIBpMeFlXjwKqWO2x0X/Q5h3OrlpQ==", "signatures": [{"sig": "MEUCIQDVHXUosNdZiSPFaHn804I6GXmDxzroDqRo4SXM0VxdIgIgZQdWV8z0mmlhrx+GFjPcPMbY2cClGZ5hIgvE55zViF8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.3.5": {"name": "ajv", "version": "0.3.5", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"chai": "^3.0.0", "glob": "^5.0.10", "mocha": "^2.2.5", "js-beautify": "^1.5.6"}, "dist": {"shasum": "b9a49b5b6accff4db66178e4b99d50b14f749ce3", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.3.5.tgz", "integrity": "sha512-kO0SsR1JRKQCMbe2oCJg737bes1+KdbWpuUV/B2+qNU5x7CL3cQWiUOh70Thznc/htmyzkSCR0j+D2RBxNMLBg==", "signatures": [{"sig": "MEUCIQCF4+ZQqJtKYWLqEwUl/EGVdx8kcpI7E9fYbcDnp6VvNAIgZMCfKsAz38k3+AtUbcGfwDiUFxUd80d3eVceg5kJhSg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.3.6": {"name": "ajv", "version": "0.3.6", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"chai": "^3.0.0", "glob": "^5.0.10", "mocha": "^2.2.5", "js-beautify": "^1.5.6"}, "dist": {"shasum": "f47f03b82ab4e00a6893f515ef880beca3709aad", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.3.6.tgz", "integrity": "sha512-/3TlGY7tKFy6DnL+I2Sckig7fk8j9WbS3dE0BtZZmiPHZRpsGqyHtuBN+PTwJ4PoNtJcl0fWYJJ+WyUftQwXJw==", "signatures": [{"sig": "MEYCIQCE9F6IpD7HPp9q3V+iaj6WTUPSbuJ2Jix1GLbxgAfWGgIhAJGi/H/ZSXlAPjLpME6OV6Ds2AdjYt7pT3Y7x7Q2UJzD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.3.7": {"name": "ajv", "version": "0.3.7", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"chai": "^3.0.0", "glob": "^5.0.10", "mocha": "^2.2.5", "js-beautify": "^1.5.6"}, "dist": {"shasum": "64020b71c9ed1689eeeec1084c92a99970c6c7f4", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.3.7.tgz", "integrity": "sha512-QAvaSWYr3p8FOjxPDQkWe9BDmHXlM4LXvY9w+TZYyGytMz9RSp4w5SCwOT5IfcNSmhRmD09+ed24CTxso7UYnA==", "signatures": [{"sig": "MEQCIBLaXY3wW7UR4e/Bb+ZlafPVj9KOSOdHvajsAOaGkAKPAiB81oyxFRIqZqV6PZcOnGh82uzU6Yv++SKI6gAWy5vTqA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.3.8": {"name": "ajv", "version": "0.3.8", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"chai": "^3.0.0", "glob": "^5.0.10", "mocha": "^2.2.5", "js-beautify": "^1.5.6"}, "dist": {"shasum": "02cec1102bf176e25843562a10fa3c9680a05f20", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.3.8.tgz", "integrity": "sha512-RtOC63HQ+1As5dGLgYxNrkKkyrpf7G7rKTl54dvfrfXq4gbVWCU1CJmbr/Do7/AtHN04VTcm2m6Yei4GcyDDyA==", "signatures": [{"sig": "MEQCIBWG/lUJCNR9PXo+OKXFUN30qLRPLk/QlQFPciH9oJtLAiAUIKFR3Alqm4JHh+ay7BPGHsoggI0LkPWEzwBX+C3s3Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.3.11": {"name": "ajv", "version": "0.3.11", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"chai": "^3.0.0", "glob": "^5.0.10", "mocha": "^2.2.5", "js-beautify": "^1.5.6"}, "dist": {"shasum": "d3c7fd53721ea7bf557ceefede56888ba976d60b", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.3.11.tgz", "integrity": "sha512-ppndMbDZ5qBZv6AmHAPD42v8X0YZc1j+/mULouCUA07jBhZANRkahStnxfnDGxC5sO0lHIjOuhkrpFZalguf4Q==", "signatures": [{"sig": "MEUCIQCmAxrVsZdAG+zs7lqu7hpOe+IC7wcKxB7YHUTqMzvwxQIgX54gLk8FJqP/NfV49BiCTp/CIJAC9Ob5n9KMjP2yjUQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.3.12": {"name": "ajv", "version": "0.3.12", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"chai": "^3.0.0", "glob": "^5.0.10", "mocha": "^2.2.5", "js-beautify": "^1.5.6"}, "dist": {"shasum": "8c2717c93f6cf044ee7bcfd53ba4c212916b1610", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.3.12.tgz", "integrity": "sha512-lJIIK+9xkFjhVpb2xmHhtdbI8ONmhM34cM21xFxTHVO6YhSVIQFBgTIZNxxnB++kwzL85uOqXMFzLzlXj/i7Lw==", "signatures": [{"sig": "MEQCICceK8KT7Njkg4HEDBi8M+SDYxFE7bcx9ANZBKSM3QuRAiAAoD407sNGPSEM1zVlMx6XhhAAv3o7B1NLR6o3DnNRFA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.4.0": {"name": "ajv", "version": "0.4.0", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"chai": "^3.0.0", "glob": "^5.0.10", "mocha": "^2.2.5", "js-beautify": "^1.5.6"}, "dist": {"shasum": "0065f6b0d7c569fb48e00394e3a6ae11b9044b2a", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.4.0.tgz", "integrity": "sha512-gjET86IB2HqTZKSTb+LWqzRbtvm7ndZxjayTimYQFIzOkD9Lq14mjgzRKQEoVCqbd3B3rZFVKUhDbp6v+4gf7g==", "signatures": [{"sig": "MEYCIQDqZfoOjA8jV+G28j+US8HxPApLzudTkoEv7IJux1zNiQIhALPsKP+WMe7qGGpJ9ULekCU3ptAAIXYwAihEsBO3fdl9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.4.1": {"name": "ajv", "version": "0.4.1", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"chai": "^3.0.0", "glob": "^5.0.10", "mocha": "^2.2.5", "js-beautify": "^1.5.6"}, "dist": {"shasum": "04d5be7d0d87523fc5c14431e6c95561b9798e0c", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.4.1.tgz", "integrity": "sha512-9W9+xRL42fLB2wHr8VF15Brm9Y1/pcxX+GWRuLHclaRGWtH8BRcCu28+ihyLr2f4X+ZyN0H34krtDcQnUPL25g==", "signatures": [{"sig": "MEQCICq3Too4xxdXxljFmuWpfZIL93ZePiL9KL0OQ2MmDtesAiA7/KCbrCZLG4P2QrpxaptMf9ofECV0qKKI2vjCPTyChQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.4.2": {"name": "ajv", "version": "0.4.2", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"chai": "^3.0.0", "glob": "^5.0.10", "mocha": "^2.2.5", "js-beautify": "^1.5.6"}, "dist": {"shasum": "8a6ff37adcf656095096c2f5485ee90a76dbf346", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.4.2.tgz", "integrity": "sha512-GplUED64pnv/xOUzGTAy0anxnMFnqdKkvQF2xaq29eYfUFAK6QR8OKGOac1tKIAiIMnLN5pdMjXesj93fnjQ+A==", "signatures": [{"sig": "MEUCIC7K3f3lpeJ3l6obsMBgF/+0hO+WnLUS1Y2yPgSRS97rAiEAv6Sh+oNnP31NKfbKWelrmP1yC3dfgMrboYjwjJhYYsY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.4.3": {"name": "ajv", "version": "0.4.3", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"chai": "^3.0.0", "glob": "^5.0.10", "mocha": "^2.2.5", "js-beautify": "^1.5.6"}, "dist": {"shasum": "e9d2b45a67458a7acfec0932fd1c9119d8ddfb38", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.4.3.tgz", "integrity": "sha512-I51rn7P4VuQdcCHiFeL8fi4n4PKGPZDdyFs6iOor3vhwssGjPByVTznlKo7cWipn7s7jG3vLeWqt2zPE5effcQ==", "signatures": [{"sig": "MEUCIQDYY1xQugrAubajO18X0FKf1CYik4ydRP0/h3rMcgx4eQIgLlcT0Bl+r2TErs8Rr1MJ4tiZdjT+kpREyuwnA4Q0Z5Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.4.4": {"name": "ajv", "version": "0.4.4", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"chai": "^3.0.0", "glob": "^5.0.10", "mocha": "^2.2.5", "js-beautify": "^1.5.6"}, "dist": {"shasum": "ffcc8fe0756a2b5ce679d8fbf2776015bbe1a460", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.4.4.tgz", "integrity": "sha512-1iGjl/uTGz1HrjNQ3t6m2sX7BfY14NO0iGE0U52uXSdVqs4SLxZGDm8JgklQGiT4wukyW9djOjBkjfBXRuKbpQ==", "signatures": [{"sig": "MEUCIQDHIMkbMO/rdVxOMwZ89ihi6r33Hr3spOmexCmwvD61jQIgIHR0cxIvQzTdO2bbQ2gmWe76RyAu3Vas+SVPG3AAPNw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.4.5": {"name": "ajv", "version": "0.4.5", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"chai": "^3.0.0", "glob": "^5.0.10", "mocha": "^2.2.5", "js-beautify": "^1.5.6"}, "dist": {"shasum": "9f67c9c1249d072af62199911c419b2f1cca823f", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.4.5.tgz", "integrity": "sha512-jWGn1NGGcmX7v8E3hizL1Gu1+NOP+0at7L3eIPWZKGx0mPHcj4dKaWE1QnbyDpSAoA4xZB3abT2HmqeZHo3Htw==", "signatures": [{"sig": "MEQCIE/fZilGROWr7S3GOczH3cuwjdRMayEDh9XDfBKp95TAAiANb8Fgb1I1DqHE12snT0F3NN093xB00Z+J/IrYO3XseA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.4.6": {"name": "ajv", "version": "0.4.6", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"chai": "^3.0.0", "glob": "^5.0.10", "mocha": "^2.2.5", "js-beautify": "^1.5.6"}, "dist": {"shasum": "88151e677cbd8ce4700a8a64f1ba34589536e023", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.4.6.tgz", "integrity": "sha512-DY9YG25bIU91MOkxO3cpMyJx9rWjohOQm+O3LsWar1odZYWzmpl267iCW/4ozYQ+5wFy25YdlU5WsX6qQ9CNcQ==", "signatures": [{"sig": "MEUCIQCMjCqgkLhAOoWVMIDfK63l64jjeIIJBt023fQOZiKYgAIgR7fZdb2WAwGBi2QaoIQurqnw33zc+CPl3lLKqFecfLI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.4.7": {"name": "ajv", "version": "0.4.7", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"chai": "^3.0.0", "glob": "^5.0.10", "mocha": "^2.2.5", "js-beautify": "^1.5.6"}, "dist": {"shasum": "668cc5563cbac6a687f58b2efd35f96f4d72bc41", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.4.7.tgz", "integrity": "sha512-KbWyTM+goBV4rVuPck100SNVvDx2JJ7XRAbzw1zK+WHcVDaGD9X0mEstWIFuk5oUvrk3g92bQribbyF8eJLUvA==", "signatures": [{"sig": "MEQCIFg5LcUsLqqENxs+OangQCmiSZSFHl/FGl8hJza+nMPcAiAwS8hVvynbczqK8WTFFCLiLNM+SdamkBQulZrXYZoxdg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.4.8": {"name": "ajv", "version": "0.4.8", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"chai": "^3.0.0", "glob": "^5.0.10", "mocha": "^2.2.5", "js-beautify": "^1.5.6"}, "dist": {"shasum": "f8b9a1892c75e4c89c7e167eb3376141f212ec0d", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.4.8.tgz", "integrity": "sha512-QJF7/9iSECD0z+HyeV7BWfLUtvStEU8OKSqZH8YLSrI2+VWmJnNOJjMPNNLYwF05EHUtkLX5AIOgCzCwHBM8jA==", "signatures": [{"sig": "MEQCIDtfB2nP+N2k5EKc0IKHy4E1tHZ8IPM6SFWo9+RA2nhaAiBOlQozznms1cCVL6p4Gn9uxpS7NN2A5b6XCu1eAIb7vA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.4.9": {"name": "ajv", "version": "0.4.9", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"chai": "^3.0.0", "glob": "^5.0.10", "mocha": "^2.2.5", "js-beautify": "^1.5.6"}, "dist": {"shasum": "fdeb463d3b11948f096a261ba746fec9b812c4f1", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.4.9.tgz", "integrity": "sha512-nyihgsypVykDnxctgM8cag3IEsdD8zA+PpRkQL+AY9RhlogqOgEYrENxzUd2LZO5qjXof8wdQnm9GIKl92hVMA==", "signatures": [{"sig": "MEUCIHGYUZGXzn4zc3rnD2jy8cCiYWs6ZVIB+2GQ2z4klQ1BAiEA/HI7s99Din2gYEXryUhWP0dUmPsYsIxXWcMC9OKTjg4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.4.10": {"name": "ajv", "version": "0.4.10", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"chai": "^3.0.0", "glob": "^5.0.10", "mocha": "^2.2.5", "js-beautify": "^1.5.6"}, "dist": {"shasum": "b1734c9ef70e39c618f69520294dea19e690dc18", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.4.10.tgz", "integrity": "sha512-cpGCkedEIWi0MFl+MHRqsscCoggHlWso8URiPUluwoKCi8NNIdmtc1ZxtgWqnUXDY/wH+13VkXYEC9gNYBCk6g==", "signatures": [{"sig": "MEUCIGFoWsIDaYzbxIt2E3cMiNrZNz9QXXLPIOER6pYDPxmBAiEAx0ZIlMPqX1eV8rs73gkmC0EgVSkQYdilUawuT/mgfAQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.4.12": {"name": "ajv", "version": "0.4.12", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"chai": "^3.0.0", "glob": "^5.0.10", "mocha": "^2.2.5", "js-beautify": "^1.5.6"}, "dist": {"shasum": "4e5d10eec6d2beeec87f81976bd80f6765437fd2", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.4.12.tgz", "integrity": "sha512-f0H7PAES7pXS3WwpD+oLkDzrs1yNRcOUQB919ifzFylo9puKUMZc9zpfrk8eMZUerDe2HAgPDcwFbVVStsilpw==", "signatures": [{"sig": "MEUCIDDOCK3ivwVgOLFB+/Zk3Aj66qoPdAd7SSKteEc/+LtUAiEAglsEupPINj94mgszOGfpoeA00Vikj2JBGg99VobJqFg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.4.14": {"name": "ajv", "version": "0.4.14", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"chai": "^3.0.0", "glob": "^5.0.10", "mocha": "^2.2.5", "js-beautify": "^1.5.6"}, "dist": {"shasum": "add519fe135be3fe34677292938573c1b4270344", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.4.14.tgz", "integrity": "sha512-Kg4PdZky4tIeeVMbhBC+6tRBRV/mOWAKtNy9xPZSH6TXPccMnzF3tOTPWgO3Vs6iuuKQoRn+sYEcp8MzH8Dckw==", "signatures": [{"sig": "MEUCIH9BttISHzcT+NUr93JST2yO7Y3Y8J60i1MmQMRwDt1mAiEAwvJ3UI5pmauthiDjhg5v3+7u2BGadf3KSfR1wlgUQ6Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.4.15": {"name": "ajv", "version": "0.4.15", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"chai": "^3.0.0", "glob": "^5.0.10", "mocha": "^2.2.5", "js-beautify": "^1.5.6"}, "dist": {"shasum": "bb3e61a8dedf1d07c93b5ffe031c81e95fa48569", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.4.15.tgz", "integrity": "sha512-9Ikbhk/hbThKYtkZwnUrgc0Rf1mni94JrJG9Fzbt0aOpuAleYPvEIemYy4mdGvJRj7v8ir0E9rcG3s45ooEkmQ==", "signatures": [{"sig": "MEQCICpk8h2wUPAJ6ORYQ0SXDdJqTz7xIH5FyZajx281Nx1tAiBdcjFvNR9v5ywPcrMxvibJyS3dU73UIVrYKdYn099m7A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.5.0": {"name": "ajv", "version": "0.5.0", "dependencies": {"dot": "^1.0.3", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"chai": "^3.0.0", "glob": "^5.0.10", "mocha": "^2.2.5", "js-beautify": "^1.5.6"}, "dist": {"shasum": "3b8ca1bbc48841d375317c05bebd1f7c332dcc2d", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.5.0.tgz", "integrity": "sha512-LjDSJuRkLe6e6nazOqOb6+FQJqs11tXO8YbyQcI8s8JTPovt19OnJrPLLcGjHbQkUs0RNH/LWh/iGsv3Q4L+LA==", "signatures": [{"sig": "MEQCIE/YS8sXPX1RgwiRpBSEsZ9FiTXxiFsof9I41UgtOV/YAiBP4novfQPHZO1mtWzubGxaV/HcOwfRhV2crkmFspCnlw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.5.2": {"name": "ajv", "version": "0.5.2", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "chai": "^3.0.0", "glob": "^5.0.10", "mocha": "^2.2.5", "watch": "^0.16.0", "js-beautify": "^1.5.6"}, "dist": {"shasum": "adb697af8aded8912dd619900d8291135cb111b4", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.5.2.tgz", "integrity": "sha512-d4tiy7mbLphnjjLnLMyZLrE029HtMF0NXPBjpCNQEL3Z5EwLFQ90dXE/vw27AwyDp/vgN7JsP1gGWMVNfTRgdQ==", "signatures": [{"sig": "MEUCIQDWX2XME5NmvLHmgdmrOFyIgV5qlHBFVo1aUfcfz8X9XAIgXTM6JMd7fKskH7fIHqb36iPI/HLVI/xBh43b+lNUD1w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.5.3": {"name": "ajv", "version": "0.5.3", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "chai": "^3.0.0", "glob": "^5.0.10", "mocha": "^2.2.5", "watch": "^0.16.0", "js-beautify": "^1.5.6"}, "dist": {"shasum": "8f1cbcac7e0d17b6c422a5f33cff10b4df671e8b", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.5.3.tgz", "integrity": "sha512-jFpt95OdWgrZ9GHlS+ZWpgwZ4u4n4dKSTVYAwSTNb1uJMvCXrR/XKac2LPKzE2FbQHQJrTe9PlLoaN2FglFQUg==", "signatures": [{"sig": "MEQCIFU+gfKIxiOcymesrtzCIMsIYaSASdwc7fUYp57ADbT8AiAA1o3z50NNTo+2iArOR2KIgQ+563pQhCauOivAzTDrXw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.5.4": {"name": "ajv", "version": "0.5.4", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "chai": "^3.0.0", "glob": "^5.0.10", "mocha": "^2.2.5", "watch": "^0.16.0", "js-beautify": "^1.5.6"}, "dist": {"shasum": "ebb3ef86ee158cf518e3d8b2a198a7c890ce3b7d", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.5.4.tgz", "integrity": "sha512-jUfmoNbX7ifYA4IVyoGoDjw8zmkn7TamCg3uXysgqIkvxcouEZtXJzscjlr57q43eAWwns6URrz4iAiup27ApA==", "signatures": [{"sig": "MEQCIBYupmOUv66foJrUrPkgWPntt/c04dE6AwiSHYp6IxAoAiBqRo3szBsIjPlQ91ojouyn/6C2DpxQLFq2/sTNdY94gQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.5.5": {"name": "ajv", "version": "0.5.5", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "chai": "^3.0.0", "glob": "^5.0.10", "mocha": "^2.2.5", "watch": "^0.16.0", "js-beautify": "^1.5.6"}, "dist": {"shasum": "cf0bebd534567976e43f3f003abfa171965afd21", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.5.5.tgz", "integrity": "sha512-QERWrNy5TFi24XroeB2Vp/cFREu5FEp6oNClCSNBbaf4GhhEF7zczQDQ+zZLHjw4K7i5Ovt4srwUcUhYjuwu1Q==", "signatures": [{"sig": "MEQCIHSfB/uISDuJYROm+NPEQScdNX5/TkdBa/P1CcvthdeTAiBp0DjjpYEuN7QYtD2vRsPxQCDbx6nV9WegLQvrMwuUgA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.5.6": {"name": "ajv", "version": "0.5.6", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "chai": "^3.0.0", "glob": "^5.0.10", "mocha": "^2.2.5", "watch": "^0.16.0", "js-beautify": "^1.5.6"}, "dist": {"shasum": "18af2e496e41929f8e82d542904666573879025c", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.5.6.tgz", "integrity": "sha512-6N/Nthl8Dzb+OwNHpJf42Ir8c9wnYuVOnGWAG/nEf7jnX6iMb16LfaVVmW9OMrRrlnR0mJ01wCZwatwZA2b1eQ==", "signatures": [{"sig": "MEUCICmc6V6muhgdnnxBxUEuFJvLSZxqmuaaWEucsKd9Wa2yAiEAvlNJXZt+BUqL03ZpJTL1cOKaphKMnOj2biZi5aZkKxg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.5.7": {"name": "ajv", "version": "0.5.7", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "chai": "^3.0.0", "glob": "^5.0.10", "mocha": "^2.2.5", "watch": "^0.16.0", "js-beautify": "^1.5.6", "json-schema-test": "0.0.3"}, "dist": {"shasum": "ed4b476e58114cc42e24d3c2521832b5de35447d", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.5.7.tgz", "integrity": "sha512-dpvV14vskjsxMVr4LWCZi81XS5QUk+elCJ0gYqGPhmio4bxeUtsCj6D4WOyFPw9LMILGLi6mK7t/vd3xUqY6sA==", "signatures": [{"sig": "MEYCIQCwU0jo89YKVkprdOyc/uKuKXsU8MpLKFdy5+10sIRR7wIhAKPfKj6H216eTmaQ9P1oc1E4cRM5tZnRfYFixkqbcY7t", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.5.8": {"name": "ajv", "version": "0.5.8", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "chai": "^3.0.0", "glob": "^5.0.10", "mocha": "^2.2.5", "watch": "^0.16.0", "js-beautify": "^1.5.6", "json-schema-test": "0.0.3"}, "dist": {"shasum": "0b3f93b6fd9eab90543ba9fdd5ebdfb505cfbb22", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.5.8.tgz", "integrity": "sha512-pPdpPTDmdDUsLGjSGhJ9217LaV1u36bCKIUT6zXSDTUbj+KcMCV8sNCZhtRww2bT449mRIEZB/xEgmU4y9mhNQ==", "signatures": [{"sig": "MEQCIGOqVY+b7tsWJKNFnczlTH5ZSqzWM82C4Lvb8Dd6Nny4AiBNsaZ0UabbrgH6/HJ2anyAI+KgjqSERhsBzBVG0jbbbA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.5.9": {"name": "ajv", "version": "0.5.9", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "chai": "^3.0.0", "glob": "^5.0.10", "mocha": "^2.2.5", "watch": "^0.16.0", "js-beautify": "^1.5.6", "json-schema-test": "0.0.3"}, "dist": {"shasum": "cf1ab553ee6b0909cd1bef6ec04da9c407e96f10", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.5.9.tgz", "integrity": "sha512-NgcMIVmrgcQ18uoc5kdEuLNEOtd9gnI0156bn/9QEUoZWZb4UGhBQ3Kzg3VKlM73WeRPceyzw0YcyzvjTVz+BQ==", "signatures": [{"sig": "MEYCIQCdmmlpQZvApZtEd6rTQCIlW41nO6uSbpzX+oHPLDtrdwIhAJg5ikqLKQv7h13UVPXEPExoez5mMy4bnqZjO/LYlbzg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.5.10": {"name": "ajv", "version": "0.5.10", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "chai": "^3.0.0", "glob": "^5.0.10", "mocha": "^2.2.5", "watch": "^0.16.0", "js-beautify": "^1.5.6", "json-schema-test": "0.0.3"}, "dist": {"shasum": "1654f77126ea70611f2795dbda0a2cd70fd80676", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.5.10.tgz", "integrity": "sha512-I73ztnAN/7bD0WovCrhMxv3Jypu/epIEjj2tglivJjxoj6R/mDrDqcTULRvvVnnu5rVmCJk249n8cUuQs+FLEA==", "signatures": [{"sig": "MEUCIQCDvQw2Cjb08/qBedWX2plLGsqGWHWhZok5EIFbYT9HwQIgAyVu+tp9XjFZoMMN4VRGyVsb9LPBHUZNSg+nz/eqh5Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.5.11": {"name": "ajv", "version": "0.5.11", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "chai": "^3.0.0", "glob": "^5.0.10", "mocha": "^2.2.5", "watch": "^0.16.0", "js-beautify": "^1.5.6", "json-schema-test": "0.0.3"}, "dist": {"shasum": "7e960255239d153731b71e28704a0c8041ec2f3f", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.5.11.tgz", "integrity": "sha512-pw1kpvEdjAUw4XxEm5AvtI/rtWVPcEo+Lt/Y83trh3kZfym7NjNQCypCW3jHs4wUAxdZjsVq3vrG4Nmk4izv4g==", "signatures": [{"sig": "MEYCIQCM1EA69/6xcja739jUAZqdru15CQ1733RCxrVG1UMsJAIhAJmDOk0lygLJE7u3Rn/ODgxlyfrYxpm+pghdu8bTi/QJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.5.12": {"name": "ajv", "version": "0.5.12", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "chai": "^3.0.0", "glob": "^5.0.10", "mocha": "^2.2.5", "watch": "^0.16.0", "js-beautify": "^1.5.6", "json-schema-test": "0.0.3"}, "dist": {"shasum": "97fb8276bd7922d75a435574cca1639487796358", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.5.12.tgz", "integrity": "sha512-/LrdmFm2uz2PQQxlKzcWG/yashHLRxx8LEeYrMbVZIHIY5NFkKG4whEiikfW60549Hs8n9lvpw8S2u1YGCBPjg==", "signatures": [{"sig": "MEQCIHTsDmshdm4FO7rkranTsFoayyRovYziy1r/s9aagVWGAiAMASlF80q7G5YNDyRlQcqMaQlCxmBv9CdZNq7sy3HqdQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.6.0": {"name": "ajv", "version": "0.6.0", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "chai": "^3.0.0", "glob": "^5.0.10", "mocha": "^2.2.5", "watch": "^0.16.0", "js-beautify": "^1.5.6", "json-schema-test": "0.0.3"}, "dist": {"shasum": "ec55c262ad9b00c2f3767f7e5d1a6f4240b37b4d", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.6.0.tgz", "integrity": "sha512-zF1k3GIy6UgcFJvIMl62aPbM6VtM/KiGhUJWPF1DdTbmvflJ+EVVh4/g625wX7dY7lmUZNaIqbUvr9JXSjulnw==", "signatures": [{"sig": "MEYCIQCGCfCE2Xfl8nhNDt6UOPJ853khntJXouh/G05SJvsqGgIhAO5uSFY3iezMlTlSaxxA/a57QjhHCWMEQikSrN0pLW5m", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.6.1": {"name": "ajv", "version": "0.6.1", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "chai": "^3.0.0", "glob": "^5.0.10", "mocha": "^2.2.5", "watch": "^0.16.0", "js-beautify": "^1.5.6", "json-schema-test": "0.0.3"}, "dist": {"shasum": "a8f0928c36da5f851a0f483b62e486d4c055c3d3", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.6.1.tgz", "integrity": "sha512-JyTHAbf98kgAt1QaP8A4exiuYbzhWt8CMSGwdpuGQXqPzKMCy6uG5o9+GLEgXEjbcfaW0ykN/QrS812zn0Q7nw==", "signatures": [{"sig": "MEUCIQC3Y8J03ZKZ0VIMPqCjvHAAxiuMmK5iknaCQxklHHUmiAIgYkZPTnpNzHYNbZXYhHAi2QQ+6EqfrRDLcxUNlfWyqqM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.6.2": {"name": "ajv", "version": "0.6.2", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "chai": "^3.0.0", "glob": "^5.0.10", "mocha": "^2.2.5", "watch": "^0.16.0", "js-beautify": "^1.5.6", "json-schema-test": "0.0.3"}, "dist": {"shasum": "1e480465a26f894b1c6ffe40e54f9233d8e6c022", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.6.2.tgz", "integrity": "sha512-OxnpwHkCawb4N4z/pk1E6bsgiRyWoF/8ulZf20ruYIN+v4piuulyd/4b0I8zJ4Eh4ScxmKt/l2G9djiv77eFSg==", "signatures": [{"sig": "MEYCIQCTIPmahZNs9J9nxX6YTWREKOO/OGV5cWGT/q0pLJDcrwIhAPDmwSNLtq/sAQrMmfvnRtuMTDTfcpBgw/wDJpKfqfJE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.6.3": {"name": "ajv", "version": "0.6.3", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "chai": "^3.0.0", "glob": "^5.0.10", "mocha": "^2.2.5", "watch": "^0.16.0", "js-beautify": "^1.5.6", "json-schema-test": "0.0.3"}, "dist": {"shasum": "054def33682288282e3ad718ad39b5df1992c75c", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.6.3.tgz", "integrity": "sha512-FvChaOtQx4xm3d0wR3ehmwoPcS2EQdktxh2NDRs+87vK9YELwEHrZr4AUeXLL7KqQ+4OUSmFn89Cxu4OmYb7NQ==", "signatures": [{"sig": "MEUCIQDTbJhY5INUoIbn7wBc2S99lyMCWV9NQW1iPpdQPMc88gIgJMsuUzU1WB+BOVV3sUHCI3O2fNTQFioSTzce1v/nxSc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.6.4": {"name": "ajv", "version": "0.6.4", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "chai": "^3.0.0", "glob": "^5.0.10", "mocha": "^2.2.5", "watch": "^0.16.0", "js-beautify": "^1.5.6", "json-schema-test": "0.0.3"}, "dist": {"shasum": "f6e85ba26c1a1166bb6519ccd7b0b26a1d879169", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.6.4.tgz", "integrity": "sha512-h+6Idtop5yHd3nZ3h3tIcQrb4EYZvWQX59ESGF7eGZG5yDApRopoYkvIyLCTw/GU8s+oavuoG4Ny3zkBb4SlYg==", "signatures": [{"sig": "MEYCIQDZ7WMu0A8q+bkbwL/mg7BrX5tQrrNS0adu8fKxEHgEtQIhAMIWXgRibmKVromCGZPRLaKRHHGW8oj8cT5KL3ewHGlg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.6.5": {"name": "ajv", "version": "0.6.5", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "chai": "^3.0.0", "glob": "^5.0.10", "mocha": "^2.2.5", "watch": "^0.16.0", "js-beautify": "^1.5.6", "json-schema-test": "0.0.3"}, "dist": {"shasum": "5e6ddd5df6c8c680889dbba70b8b9bebe5e90c79", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.6.5.tgz", "integrity": "sha512-BF3Qt77HivXqZkpu+73/nDhZN1jVVd2y8zDgtFpLhRYi/e5mV4Hm4wbHa43L6lz38MJD6FJQu+vOxtxq1gsMLA==", "signatures": [{"sig": "MEUCIB+OqmzEgwngoPgLJldhRBsx+XlWNAhtLtLjrSCv4rFrAiEA4v2lqWyyeX1FiG3a6+ilGL9daKVUnK8YMPTQU+jd43w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.6.6": {"name": "ajv", "version": "0.6.6", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "chai": "^3.0.0", "glob": "^5.0.10", "mocha": "^2.2.5", "watch": "^0.16.0", "js-beautify": "^1.5.6", "json-schema-test": "0.0.3"}, "dist": {"shasum": "1f3483f9e33be6ad28a6e493865bbe8e3e5c643b", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.6.6.tgz", "integrity": "sha512-lvPhMKJbt+B6IkgJyO5+Qvq03korlgV+8pu84e3d9cC6rxBBPjaZmWCIfBu86PMelauO6XCCDlfCJJ4xBK2Zpw==", "signatures": [{"sig": "MEQCIFPAQabMcszYgLM9bxpJCxhTMi6jXuSDmtggkdZ3FcblAiBRjqq/RqA+ffUWLoJxVy0lXsUV67Bze4cn2zoAxhu2nw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.6.7": {"name": "ajv", "version": "0.6.7", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "chai": "^3.0.0", "glob": "^5.0.10", "mocha": "^2.2.5", "watch": "^0.16.0", "js-beautify": "^1.5.6", "json-schema-test": "0.0.3"}, "dist": {"shasum": "651d5bd79b8362b6777fefcb8c9991e609cee390", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.6.7.tgz", "integrity": "sha512-CTf/wgQGAK8p4fYHqVbBC/aQ8NXDYm3nLCFgnASqoAzPjlue6tsYYPZLXh4Hmtz6HNAz7fxionDy8Lt+HYo7Pw==", "signatures": [{"sig": "MEUCIGBcKeCESRACPHuBxmitBeetlWNp9i9q6eAebiwWruXSAiEAuFe1av/Vs41H4IrZ0c1tCjiTYz+ixb1GLXEBgrsgVeU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.6.8": {"name": "ajv", "version": "0.6.8", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "json-schema-test": "0.1.1", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0"}, "dist": {"shasum": "0bd5fa2537d99920aed01ae60e001242a7932683", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.6.8.tgz", "integrity": "sha512-hQswXpYTksKEp4d+oxwdLtu7hOv7TP/5aJyE26ZpkGMk8vZVuRvWLsPE3LDiwC/IluZHAGNSuF3IEyluLhSWJQ==", "signatures": [{"sig": "MEYCIQC6ZS3tXu9Av9FttAUoOGy5OmDdHNcfJJtxhsB6S4ppVgIhAIJyJC0rr7jZ5dKmHDoATRVf/qukIXOphjCremthCbME", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.6.9": {"name": "ajv", "version": "0.6.9", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.2.1", "json-schema-test": "0.1.1", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0"}, "dist": {"shasum": "b7b97126701713983c30cf9828626b760df5cd3b", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.6.9.tgz", "integrity": "sha512-qIN4CMHoZKpkgl3uTnt6UxIlf7CNq1EIG2Eh/aDE+4Y2fLT6AZOd1xQrgbEmKNtNjt0ffZMYAkXlcVYBv4WiwA==", "signatures": [{"sig": "MEYCIQD8+NkUnj51e3i0UCt1zIJ0yEJ9vKnV9qobpwNT99frVgIhAP4mJpLCtJnyBC1O+mAsBlzJ3rB4BCGPPuZLDRnXdGqL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.6.10": {"name": "ajv", "version": "0.6.10", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.2.1", "json-schema-test": "0.1.1", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0"}, "dist": {"shasum": "b4a414d3225840e8e0773df29869fae2768b7599", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.6.10.tgz", "integrity": "sha512-YD2zg5zhz9aqKFwp/syg5R6TSIniExU5GnIuBTfCBt0MX/tOXlCs5dt12GktWPg9QVa/A/+9F+w2AKENGa210g==", "signatures": [{"sig": "MEYCIQDIW0GQ9xa4Ijul8rGhaLpmIrKK/dPcUnOPo5suxZUTlAIhAOMWdN6IxA4rVpC/KW63wuUY1fypCjkg6etWH4AUxSwa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.6.11": {"name": "ajv", "version": "0.6.11", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.2.1", "json-schema-test": "0.1.1", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0"}, "dist": {"shasum": "75b0901f8f1b4a94be2b875410dd9f3155fead22", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.6.11.tgz", "integrity": "sha512-kfwFKMM9X1MIX3NVl+tQmGfMevmOkCQkCfxNLTQbTDqAOgZeTp3FLNf0mZMbwqUumZoueBUWkupDrnoChskbYA==", "signatures": [{"sig": "MEUCIQCoCRXjtmkaEP0R2GNd4u5cev+sz7Xe19ICnGmsRkQDygIgNOeUHzB/0cjUbgIXPx+lQjyZam8/+AJfk6csMr726Ks=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.6.12": {"name": "ajv", "version": "0.6.12", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.2.1", "json-schema-test": "0.1.1", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0"}, "dist": {"shasum": "3f2a9bd7fc906bc97a78f8b4142273808df183f9", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.6.12.tgz", "integrity": "sha512-MrlSVeONhmWcRnjaXScyh2r8iczamq9gYGN5qtb01L2CPs5dlNDhFUaR1njL/V0i8OC+AYt31sWOxamTLgviMw==", "signatures": [{"sig": "MEUCIHJIN9Iwro++v5XuHaLsrvxp/ZJfFN/PZ7LeyxS7zBUaAiEAzvD7J6qsVwGyu+dhU9cN3E7cLl62QNAnWLPJjNM5DyA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.6.13": {"name": "ajv", "version": "0.6.13", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.2.1", "json-schema-test": "0.1.1", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0"}, "dist": {"shasum": "07d5ec983882eadfa89e54bdef70005f0b6e95a2", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.6.13.tgz", "integrity": "sha512-puMBaBa0FB4tXfwcNFBnbTsqQxikQlcehto0yPMn0xHhwDUq+c/cp0kaZSnP5NLl8/lSKK6wW0BDu3kPD+er7A==", "signatures": [{"sig": "MEYCIQCdAe5iwYHMQepZ3zH0rJw2QJeeo1KvpZ5d/E23ixu9FwIhAKFX0w34g4ckEIlgJzN4FYoL8pnMKGU0SVxjCt/TPKXJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.6.14": {"name": "ajv", "version": "0.6.14", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.2.1", "json-schema-test": "0.1.1", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0"}, "dist": {"shasum": "d58af179883e22dabe46947479f3b15682a685ef", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.6.14.tgz", "integrity": "sha512-XUmZkF23329xfeaN3lN/w1CKjgxDgWY0Bw4RDkoforyQmezEjqcHGK6tH08CiXXDz2n3cVLBap9zzoMGHZOTwA==", "signatures": [{"sig": "MEYCIQDmO6rGtW+00r4igYau2prd3xcdVD5pFkuIoC/zyuOY4QIhAL+JgvJ/35fnICX5h3+rmgAQVccy6htYIgkcF4imtUQ1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.6.15": {"name": "ajv", "version": "0.6.15", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.2.1", "json-schema-test": "0.1.1", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0"}, "dist": {"shasum": "f40acedda3f0c2efba3a6beca0df63bdcb4edc2b", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.6.15.tgz", "integrity": "sha512-9yDMzBrYzgdRQgB/VYzfRl6q8NdQqHIJmtbQZXZBm7YcIB+Y6TrCODnmJyw61KKC2IRdScvf/U2a4b+8o4Wjhw==", "signatures": [{"sig": "MEQCIDoWSbnpLlFKBW/b/MYjnrColdsfDIp79tMNlERdw8ShAiBaRZArvoGk70aDgHQqP4vUt0/ix4X71toxCKFDUfLb1Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.7.0": {"name": "ajv", "version": "0.7.0", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.2.1", "json-schema-test": "0.1.1", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0"}, "dist": {"shasum": "a3ce4683ae7223a3f60a2dc0bd61f11ede17cab1", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.7.0.tgz", "integrity": "sha512-3dEaoaQBOh/USVZt7vnUaKBByvC1QxOkppkEZ+/hREc1fC49izsUiKaTzxqRwSzaz7U68T0gkhN7f/nShPV82Q==", "signatures": [{"sig": "MEUCIFoTolDLxxEGR1dWGgfkYWs2TFccBjjr2Oy7A1Kcg3b3AiEAqbwHSy0LyCg1KcGkCC1urFcMGkvKISU7M8FViCBoFAY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.7.1": {"name": "ajv", "version": "0.7.1", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "istanbul": "^0.3.17", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.2.1", "json-schema-test": "0.1.1", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0"}, "dist": {"shasum": "5d57aa3b039aafa4c6045b0b48ec2885d884971e", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.7.1.tgz", "integrity": "sha512-Cyow0bicJqAKPtU8vAeTmefRDxHHCY3tp49qJVJvSo/O+sstUuJCabdO/Gs3bbQUY4wTGkkWRf0vPjZjDFmdkg==", "signatures": [{"sig": "MEQCIHIGdRtHvKsqXN1nuzQ5ZCQiercbLUnJa7cYKpPo+NXUAiB2gaVw4w7lB43dp0W57JLb7/tsHKMb1veKQdlbAYDTcA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.7.2": {"name": "ajv", "version": "0.7.2", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "istanbul": "^0.3.17", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.2.1", "json-schema-test": "0.1.1", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0"}, "dist": {"shasum": "2b21b19135696f5c6fca37ed78fbf4c9ee139ad0", "tarball": "https://registry.npmjs.org/ajv/-/ajv-0.7.2.tgz", "integrity": "sha512-r2JiZ0zJsac0h6++PLR/O7ZYQVEDaq2qUG4DZ+T1zporzC8HcZ6rvs9bLURRdTwQ/vXh+2RRct9OBr0clF42hQ==", "signatures": [{"sig": "MEYCIQDRwHqD4S8zDiuL38rnaM7WN4qWPmbKHqbGJV8484hkUwIhANCExT4ZTGnTETNdWEdjBIg5pSp3Jlh7a6hWBXb2VB3d", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.0": {"name": "ajv", "version": "1.0.0", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "istanbul": "^0.3.17", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.2.1", "json-schema-test": "0.1.1", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0"}, "dist": {"shasum": "59281fec5b63be50e3a8f3937e03ee478c6b0d83", "tarball": "https://registry.npmjs.org/ajv/-/ajv-1.0.0.tgz", "integrity": "sha512-dihLPTwA46ycbv5wbz3ICpI4c//zxuOHTibOSrHlo2/VaV6qwe1tMVyAozI44P5Xtg31b2jZYYC597BCwSrKxg==", "signatures": [{"sig": "MEQCIFV4lV58XdmII+OmZ7sRWHTXiv/EXAyEvL9r530iCGLgAiASlsShpvlY8ltPIDgFOZG+yLIZ4+QZudsivxVdnvV0sg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.1": {"name": "ajv", "version": "1.0.1", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "istanbul": "^0.3.17", "phantomjs": "^1.9.18", "browserify": "^11.0.1", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.2.1", "json-schema-test": "0.1.1", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "d4d9714fed9a6ec2080b15babae569d590c93b53", "tarball": "https://registry.npmjs.org/ajv/-/ajv-1.0.1.tgz", "integrity": "sha512-BLeMHPSYnrkmcS4RwoLKYbHfOr4Q5gcYnHXCfKDlU1/e1n340w+m/Rt+qn13D0AJf5mgKkbMyc7mpDASYu77zQ==", "signatures": [{"sig": "MEQCIDz5x8DEtJINFGKbR9AV3nvci2h+UkuJnaF3AtfxilNyAiAaudBi5/6wsE0HdM0R7tqu/wZBgk9naAcQ96hRYo/J+g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.1": {"name": "ajv", "version": "1.1.1", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "istanbul": "^0.3.17", "phantomjs": "^1.9.18", "browserify": "^11.0.1", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.2.1", "json-schema-test": "0.1.1", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "264e4f1e68115be218f4df532f2f1ed259c2ad78", "tarball": "https://registry.npmjs.org/ajv/-/ajv-1.1.1.tgz", "integrity": "sha512-QnVWCLl+VPBbvvwZuVP1EKNnvg36fM+J1HrGoE7XxevQObiotAm2LrYzpF/pnADh5siOAjRjqKq2zVM5Tj7Dhg==", "signatures": [{"sig": "MEQCICqdxs62dQF8MuFosziIFHAazwxdaVNXdWDQUUrRXW2lAiAblkiaIAHegZvZLT7CGqbTNvuiAhQ+xC10mgkj79mw2A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.2.0": {"name": "ajv", "version": "1.2.0", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "istanbul": "^0.3.17", "phantomjs": "^1.9.18", "browserify": "^11.0.1", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.2.1", "json-schema-test": "0.1.1", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "11e1e2263464f4f129a1d8deb84671bdcfc3e601", "tarball": "https://registry.npmjs.org/ajv/-/ajv-1.2.0.tgz", "integrity": "sha512-yHRG5VmcIibcisVGJs1ZsAH7IhyitKZimt0h5fOzSbadtc0jTibkyF9JOAu4E/ZHQRPW0gY62A7x7HKYeH4g0w==", "signatures": [{"sig": "MEYCIQCjVaAzOGiWiPPl2u3dRxOJCJsCNSyAj9IsMB7lblxNoAIhAMrl7XRGKHqvr/r2onk/+1vE8Jq7naQA3vIwXdS8Q5JG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.2.1": {"name": "ajv", "version": "1.2.1", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "istanbul": "^0.3.17", "phantomjs": "^1.9.18", "browserify": "^11.0.1", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.2.1", "json-schema-test": "^0.1.3", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "c9e11406966922c0aa540eea52a30e3ac81ae534", "tarball": "https://registry.npmjs.org/ajv/-/ajv-1.2.1.tgz", "integrity": "sha512-ODCbIj6s+GvFJWTidBFZDkwlAIf8VxUtl+fjp3QhchISurWU7T/OykxTQW3Fd8u0hOJ1veZsEbqS4PUTny21Ow==", "signatures": [{"sig": "MEQCIDI/Z4TbVwIRJVeUYLJVURprNngUd6830Ph+fXi7KGckAiABSN2psQpKqxOwUJL/c5GekZqbDkpBwnXlmLpv6QHeBQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.3.0": {"name": "ajv", "version": "1.3.0", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "jshint": "^2.8.0", "istanbul": "^0.3.17", "phantomjs": "^1.9.18", "browserify": "^11.0.1", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.3.0", "json-schema-test": "^0.1.3", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "c54c1faade886a300186ec9df19c279d7a752248", "tarball": "https://registry.npmjs.org/ajv/-/ajv-1.3.0.tgz", "integrity": "sha512-4iwVhXLjMx+coXLUHdVZmxtOfAHg5UpZ2F7K4TKFtSfip4FpFNRQDEcHLK/AVneMTWvF+J9Uv1T0XP/FrogmHA==", "signatures": [{"sig": "MEUCIFK1r/sRG/POTQFWAj6kiPe0Bp+nrOhnKGOhRJ/Pw17bAiEA6GDoKIi+zXeUd2l3ViSSLUotdGfcD3hXJDp5p7gqdOQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.3.1": {"name": "ajv", "version": "1.3.1", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "jshint": "^2.8.0", "istanbul": "^0.3.17", "phantomjs": "^1.9.18", "browserify": "^11.0.1", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.3.0", "json-schema-test": "^0.1.3", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "477358f31fd76398c8d33daab2e27f222183f39f", "tarball": "https://registry.npmjs.org/ajv/-/ajv-1.3.1.tgz", "integrity": "sha512-HFruxK8uKquiCYAbamPGQFZqpPuFqzSYWrxA5eWKZK8dyniBmaDIboFm9UeEqMgw3lNH1NhVKZFA3twgbG3/eA==", "signatures": [{"sig": "MEQCIF80wb3iKsbUyCwL5TPgdthUdIHEaz0yu49+M3B3vZl4AiAieG/m31ff//d2hGZFbJUwGBpiIvuKmsI68PD8oMyvQA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.3.2": {"name": "ajv", "version": "1.3.2", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "jshint": "^2.8.0", "istanbul": "^0.3.17", "phantomjs": "^1.9.18", "browserify": "^11.0.1", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.3.0", "json-schema-test": "^0.1.3", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "72d472a3b803300c5ca72d0170a4482963669fa1", "tarball": "https://registry.npmjs.org/ajv/-/ajv-1.3.2.tgz", "integrity": "sha512-zfIngJRdo4FanO2DGL2mUyG5eeQwKzxPXGOPMmK9z6VHcq2/KmSoNfW5zc/7O44eVQFW+WpyQLEV7A7GdieG1g==", "signatures": [{"sig": "MEUCIDLEhung/YlbCD2fIJ6pAWr0/AmDoqoJ/hhXewwdBwOIAiEAh2vNMM4whCaGNzF2Pfl/Rkmxn+VS6LidOHy5R8/bZLw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.4.0": {"name": "ajv", "version": "1.4.0", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "jshint": "^2.8.0", "istanbul": "^0.3.17", "phantomjs": "^1.9.18", "browserify": "^11.0.1", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.3.0", "json-schema-test": "^0.1.3", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "38b7dca92b00bf465dbc24683c5da8e3e19e5945", "tarball": "https://registry.npmjs.org/ajv/-/ajv-1.4.0.tgz", "integrity": "sha512-bcr3Gpl+PMFi49NODPMBOMUy+IqILVj4V4na+3T5uBnFqVXb/dlxWNh2XxL26fVyxWkNUb4obMCRLgmUYpkhGA==", "signatures": [{"sig": "MEQCICYuKBLNzdHZdvxC/saKc+MTZGCIC9MGE8/78ph7NTtgAiA5VsLIB76kJP+8B1Dios3mrthznOcQVTBsU0mbJjuLcQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.4.1": {"name": "ajv", "version": "1.4.1", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "jshint": "^2.8.0", "istanbul": "^0.3.17", "phantomjs": "^1.9.18", "browserify": "^11.0.1", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.3.0", "json-schema-test": "^0.1.3", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "e4569a89ed220fc718ee156181312023f6b51a44", "tarball": "https://registry.npmjs.org/ajv/-/ajv-1.4.1.tgz", "integrity": "sha512-5uczglNk/kAJHJp2O4f6XqaOdrpTaJus8QjLcPfWqxa7Ak6hrvF+S9oRi/WnnOEAkpKSzHv16enjOkmAb1fAxw==", "signatures": [{"sig": "MEYCIQCf5TxSc/2PliY4gBeur39njaplTW/QXgAqt7bU2fxTCAIhANDKAU+tcfHIaiRgrePb0GPxb3aLioGPLRsFxKmB2kLG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.4.2": {"name": "ajv", "version": "1.4.2", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "jshint": "^2.8.0", "istanbul": "^0.3.17", "phantomjs": "^1.9.18", "browserify": "^11.0.1", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.3.0", "json-schema-test": "^0.1.3", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "42d4f25a39387685f2b2fc73aa76aabd60e0d0e3", "tarball": "https://registry.npmjs.org/ajv/-/ajv-1.4.2.tgz", "integrity": "sha512-sAXN9xH4RstkmBp3dWg7JX1FIWrV1MxU56MMG5LNFjarEe+qicwiQmp74z/J2wKS7s7q4uYg1QrW+UIXWcmZXw==", "signatures": [{"sig": "MEUCIHyKzOk6xdfbwrW4Q56x/CXoH22wDkoKeG7LNO+wHlJjAiEAoeqz7jFDK1LFI0SYKhOVV7YXED7nl3E1UJHmz0CaQxk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.4.3": {"name": "ajv", "version": "1.4.3", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "jshint": "^2.8.0", "istanbul": "^0.3.17", "phantomjs": "^1.9.18", "browserify": "^11.0.1", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.3.0", "json-schema-test": "^0.1.3", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "f73af9741a9a504cb3e0b01e0cc7012d79d48358", "tarball": "https://registry.npmjs.org/ajv/-/ajv-1.4.3.tgz", "integrity": "sha512-tVqUdjAC3+KxgZS0F5PeqIjyWmvlKC04hQOz52q0PQwA2naC65BJcmk477w6131smFXxXTYkBYPsF90WU+w3/Q==", "signatures": [{"sig": "MEQCIHJHhqE2DbU7YQUC6qhpCT8aAZfKEiy52oKCCusWhyonAiAI8GRA9EwEKqQ7093BdtVFRpvUmlmqk9pTpkCjGNj+Aw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.4.4": {"name": "ajv", "version": "1.4.4", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "jshint": "^2.8.0", "istanbul": "^0.3.17", "phantomjs": "^1.9.18", "browserify": "^11.0.1", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.3.0", "json-schema-test": "^0.1.3", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "26020524ec250be2feaa94c7a549e7ed3b1fd3d2", "tarball": "https://registry.npmjs.org/ajv/-/ajv-1.4.4.tgz", "integrity": "sha512-ol0Lzm2CyFyC1Fjz792LEC75D2mfl22ptrIKV6IBoZe4Gh6erQcX7XnMiR1Ily558Bc9QaEl7kGALa6+HJuUYw==", "signatures": [{"sig": "MEYCIQC7uJzlWNI4/HXDHKuDal+zf5btiNK4M8ttvUFE4znUpwIhAKDHkZVBCtPYijdFa4vakIdPbgglyKPjtvtDXXvTxExK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.4.5": {"name": "ajv", "version": "1.4.5", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "jshint": "^2.8.0", "istanbul": "^0.3.17", "phantomjs": "^1.9.18", "browserify": "^11.0.1", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.3.0", "json-schema-test": "^0.1.3", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "eee657045a1cbaa3a17445455e5fe4c0d6787442", "tarball": "https://registry.npmjs.org/ajv/-/ajv-1.4.5.tgz", "integrity": "sha512-cAtsxrZoQ2lzO2INa/cC0aMrhw9e4P+vgTfuS4FN6Kl7s3uwqlEfbLwFDwh28NxAN7+Ubrq6SBrejf0tJfFgkg==", "signatures": [{"sig": "MEUCIQDalqS/8nFr+BFuykGIds2OY0paOerdzcKYh5MndKl0OAIgU/Yafx1M+5aAKah4+jUlyip5TyJkfIdaDq+cvzv20+8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.4.6": {"name": "ajv", "version": "1.4.6", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "jshint": "^2.8.0", "istanbul": "^0.3.17", "phantomjs": "^1.9.18", "browserify": "^11.0.1", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.3.0", "json-schema-test": "^0.1.3", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "51a204bc1be7ed630259c9aedbc488536eed76f2", "tarball": "https://registry.npmjs.org/ajv/-/ajv-1.4.6.tgz", "integrity": "sha512-dtAtYlf+H7vHlff2OWvzFvEk4xX5R66tEH/FhBcz89uOfYLLdAzxmS67Gi/yRwTC8rLnTFIZKhd9aEzkqc5SOg==", "signatures": [{"sig": "MEUCIDAPvI4tvLCtYJVGZsiYSaYxu2JaHizRfDOknOGKrn4/AiEAupDmnhiGy4QpekWocfzC7TJJj0aWxw+3OTNLvzJuCHw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.4.7": {"name": "ajv", "version": "1.4.7", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "jshint": "^2.8.0", "istanbul": "^0.3.17", "phantomjs": "^1.9.18", "browserify": "^11.0.1", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.3.0", "json-schema-test": "^0.1.5", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "43e23670b11fe171311874bc152d3704554e4882", "tarball": "https://registry.npmjs.org/ajv/-/ajv-1.4.7.tgz", "integrity": "sha512-br5iUGviL/BpMWIG+s0yOPSM3JnBI8K0TpXnpqFJuKp8zRbxdetnp1lxy5y9lPdtgBwImMbOyT6PAyhXb2Smyw==", "signatures": [{"sig": "MEQCICZc5Szrrff2zikSIdPzoaQ2rGjX0hGwcUNZEmTFYsuPAiB5664FdhIZoGtFmzsYIbpQkfIuV8d/tMLeMw3d0328IQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.4.8": {"name": "ajv", "version": "1.4.8", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "jshint": "^2.8.0", "istanbul": "^0.3.17", "phantomjs": "^1.9.18", "browserify": "^11.0.1", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.3.0", "json-schema-test": "^0.1.5", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "19cfe885c41f1af0d74207ac8f75362f114bf0b7", "tarball": "https://registry.npmjs.org/ajv/-/ajv-1.4.8.tgz", "integrity": "sha512-3TSykIA7otGlHCQU+U9Sg48JPMz8V5jDKPRMeRWEAk5IywJJai3oeRaTIlskdMG21G0KKyYZEqQSEhKVRhW3HQ==", "signatures": [{"sig": "MEQCIHpPAKlmcak6E6Tnl9H+K+gVBqqu0uyyduN3XAAACmgvAiBhpSl0I2yxd9CmAVhOAr+Dlt8wNF1G2nI1aJ1MUanzRw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.4.9": {"name": "ajv", "version": "1.4.9", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "jshint": "^2.8.0", "istanbul": "^0.3.17", "phantomjs": "^1.9.18", "browserify": "^11.0.1", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.3.0", "json-schema-test": "^0.1.5", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "6a832425277d1a7b4f739099b757324838fc6870", "tarball": "https://registry.npmjs.org/ajv/-/ajv-1.4.9.tgz", "integrity": "sha512-47hnl5qUM1cmcD//Zzds6+BNLxC3ydoo3rtyQmriKMoLHfN94KYMZjn+H6jB1sdiL8PRzbd+Lpefp5jBICsosg==", "signatures": [{"sig": "MEUCIBx+LBVd3N5GG27FbtUHXu07/0aIYjiQmPepHbuWQBnAAiEA3cio1/JjPmrOb6+9uCmc6yqWlZAUnfvh9DtHfhHfvjI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.0.0-beta.0": {"name": "ajv", "version": "2.0.0-beta.0", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "jshint": "^2.8.0", "istanbul": "^0.3.17", "phantomjs": "^1.9.18", "browserify": "^11.0.1", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.3.0", "json-schema-test": "^0.1.5", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "76d787e5e59c56d4ce24ae1df0d3bb750094d257", "tarball": "https://registry.npmjs.org/ajv/-/ajv-2.0.0-beta.0.tgz", "integrity": "sha512-7sCiu4pwV7I0i0b42ClEjWR+VbV6Ry1EtMYDfOxh4Jp9YlpW+z3p0vSjuyPw3MLHckdBEycF5tnDcvTkYPoMHA==", "signatures": [{"sig": "MEYCIQDkwidgXaTg6kAHquaD2nFPcRvjzcxZ1FIyU4AqlwplBQIhALpDuQbTtHw1FF20WUpILYELJtwkYT9TZxRFekkUbIzd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.0.0-beta.1": {"name": "ajv", "version": "2.0.0-beta.1", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "jshint": "^2.8.0", "istanbul": "^0.3.17", "phantomjs": "^1.9.18", "browserify": "^11.0.1", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.3.0", "json-schema-test": "^0.1.5", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "17ee6d4b90b6e234885313e3b9d249f328ab27e6", "tarball": "https://registry.npmjs.org/ajv/-/ajv-2.0.0-beta.1.tgz", "integrity": "sha512-E2x0EVJHcO1galySx3yZKM2XRgPfX2BJmVIYJzvQMZV9Vq32lEvx3U5AVuDV5YN41BZ45jmDkmBKe8OwXOoCLw==", "signatures": [{"sig": "MEUCIQDulRZEnS9AjgxO334b/yfWDbn5O7M4ZMedt26XncHk5QIgXipHemwqMA5+gct4Df+ilUO1SKqz8+xE2IymWJdaI6g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.4.10": {"name": "ajv", "version": "1.4.10", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "jshint": "^2.8.0", "istanbul": "^0.3.17", "phantomjs": "^1.9.18", "browserify": "^11.0.1", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.3.0", "json-schema-test": "^0.1.5", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "peerDependencies": {"ajv-i18n": "0.1.x"}, "dist": {"shasum": "20183830abe29a646851612603a7cdada611bccf", "tarball": "https://registry.npmjs.org/ajv/-/ajv-1.4.10.tgz", "integrity": "sha512-meogfF2UopCRVJe5b5EcmCI6B3GW7L+i783AhVKJANnOpMnW3ULPwGz2ipF4VrdGnApcMh12ePpoFzUzjCmg8A==", "signatures": [{"sig": "MEUCIQDg7StNpvk8y11vjxinFuDNuOFLTzRFquJ5OvuVTExN4gIgWXeL8aDy2DTwMMITS1sJ78r81P1riG7kAxmth5ULF6I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.0.0-beta.2": {"name": "ajv", "version": "2.0.0-beta.2", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "jshint": "^2.8.0", "istanbul": "^0.3.17", "phantomjs": "^1.9.18", "browserify": "^11.0.1", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.3.0", "json-schema-test": "^0.1.5", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "5f9f88d190fca94b7db83d6096b7f4216611a9a5", "tarball": "https://registry.npmjs.org/ajv/-/ajv-2.0.0-beta.2.tgz", "integrity": "sha512-Py5niSVdPY9H8ryv38tNSpzmUWcdFrfdaLbNmjSpr+AovWnhpKlU0eet9ILA7nXMoIiideKI+7YkdE86WrLdtw==", "signatures": [{"sig": "MEYCIQD5QrDDKCHXo6e/BOGc0UIZqMju2aMkZWRRvPliGsSsBgIhAMq3XT8PJO4+PvD8dLpgEB4eHHf0TjiLBbwhvbMzn+Ru", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.0.0-beta.3": {"name": "ajv", "version": "2.0.0-beta.3", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "jshint": "^2.8.0", "istanbul": "^0.3.17", "phantomjs": "^1.9.18", "browserify": "^11.0.1", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.3.0", "json-schema-test": "^0.1.5", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "peerDependencies": {"ajv-i18n": "^1.0.0-beta.0"}, "dist": {"shasum": "b94e323c63d5458114b79d68943787e459afe562", "tarball": "https://registry.npmjs.org/ajv/-/ajv-2.0.0-beta.3.tgz", "integrity": "sha512-myxkEAx5KtCnCYNnGg1espGCaYwJhKIG/XZ4WRWxlULmHAV37taf8mFVvCdmVsttrTU6V9o2Iq6K85WFo8j07Q==", "signatures": [{"sig": "MEUCIQDnIj4ToLmlaPZjIaX3aPRrJ/YXxWMPOtq3k6RxGNLmVQIgKGuSJZpeTgsALJAH8yn9XmbCwmbFEciGDXWSLJwcDmo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.0.0": {"name": "ajv", "version": "2.0.0", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^0.0.8", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "jshint": "^2.8.0", "istanbul": "^0.3.17", "phantomjs": "^1.9.18", "browserify": "^11.0.1", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.3.0", "json-schema-test": "^0.1.5", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "peerDependencies": {"ajv-i18n": "^1.0.0"}, "dist": {"shasum": "a1608acb68c16b9a1776fb7e042a2ae5ebaf6d7e", "tarball": "https://registry.npmjs.org/ajv/-/ajv-2.0.0.tgz", "integrity": "sha512-kznnbXWmVhFNFtMjScdzEtnZ0xmaTZqUqDL+3Slv5xFCp5edlkOTxS2kwkSesY0lPKoX8ytnCprYTbqnnqpKBA==", "signatures": [{"sig": "MEQCIEQHlGVeUFN1d7actksYBUYfPcsvQSaRPn/EW/TsmDrQAiA9T3LwODCtinE6e8G/x/xO2BEBsx4ue2Z3+JJEGbPLJg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.0.1": {"name": "ajv", "version": "2.0.1", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^0.0.8", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "jshint": "^2.8.0", "istanbul": "^0.3.17", "phantomjs": "^1.9.18", "browserify": "^11.0.1", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.3.0", "json-schema-test": "^0.1.5", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "peerDependencies": {"ajv-i18n": "^1.0.0"}, "dist": {"shasum": "70c1faed67768ac068e855bf53f67db011b4f14c", "tarball": "https://registry.npmjs.org/ajv/-/ajv-2.0.1.tgz", "integrity": "sha512-om3UpfZnuMcgQIaFvxDC3G8/T2BEue9A2VeNmdSGIZ6l7z9WClXGYCQYpAP2bOqPQN0BhPL7/kbca+B2sSJ2Jg==", "signatures": [{"sig": "MEUCIQD3K2DhfAfVVuIQZi39/XfB4P4QvX/ozRXXl+X/n9SiiQIgQkOWsjl97uYI8uQ9Q0BmI9dcf9MrtfOjIMQc57yR7Mk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.0.2": {"name": "ajv", "version": "2.0.2", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^0.0.8", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "jshint": "^2.8.0", "istanbul": "^0.3.17", "phantomjs": "^1.9.18", "browserify": "^11.0.1", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.3.0", "json-schema-test": "^0.1.5", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "774fe1dc6547a8281d7ce403237369f1d713be67", "tarball": "https://registry.npmjs.org/ajv/-/ajv-2.0.2.tgz", "integrity": "sha512-v5kxaUz1sdVldbV5pmbJeQQcT+kF7bWyA7zZO6r7pTCBUdktILA5wqVe8JnKe9dN5jFc0D46xvdiDXY/LbMqxg==", "signatures": [{"sig": "MEYCIQC6rtDkX8xo9yFwNppZu/6V6pHDzq/LVrv/iCerDHyzmgIhANGqPuUJgLHWCc928rxQfeyvTfFld1fF6VUYfmGjMfbf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.0.3": {"name": "ajv", "version": "2.0.3", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^0.0.8", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "jshint": "^2.8.0", "istanbul": "^0.3.17", "phantomjs": "^1.9.18", "browserify": "^11.0.1", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.3.0", "json-schema-test": "^0.1.5", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "3ccbbe11a6d276866207935d05c1f5f614d2823b", "tarball": "https://registry.npmjs.org/ajv/-/ajv-2.0.3.tgz", "integrity": "sha512-k6tRNMxIe3R0jusg0JJSxEnrFIR6lm4I7r/KVBava6cxqQ1ZIVH/psD9NHvbToPJDMtuMfJEHihGSCi/liKOVA==", "signatures": [{"sig": "MEQCIBAfJIOpUBv6Qt/FqjHjwsiQ7ytfSS4HFHsuDWd0FdE5AiAIbQfgBqUiXD2UEydQXSJzeM+kq51sFbpFMX9aiGTEYw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.0.4": {"name": "ajv", "version": "2.0.4", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^0.0.8", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "jshint": "^2.8.0", "istanbul": "^0.3.17", "phantomjs": "^1.9.18", "browserify": "^11.0.1", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.3.0", "json-schema-test": "^0.1.5", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "40a059356838878f1edba84f70ffc10450a0c0bb", "tarball": "https://registry.npmjs.org/ajv/-/ajv-2.0.4.tgz", "integrity": "sha512-MDGUYlfvhxXi1aXZdZ25+Ua5avTiu76UwQikQfQQ1A4XorUiy0g1iv0DJd0SCqYkl+PkoyM0pjOGlGZsdUl22g==", "signatures": [{"sig": "MEQCID6P6Xi3Soza6wy2MH6hEOqaWju48D9pA9g5iPg0s30yAiA+BOAvblvFPT88X/Npioo/GGbpKE08xBUfLcNsapAN4A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.1.0": {"name": "ajv", "version": "2.1.0", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^0.0.8", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "jshint": "^2.8.0", "istanbul": "^0.3.17", "phantomjs": "^1.9.18", "browserify": "^11.0.1", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.3.0", "json-schema-test": "^0.1.5", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "f0bdfb90fcf511e7ceaa6586d0e8714a96863358", "tarball": "https://registry.npmjs.org/ajv/-/ajv-2.1.0.tgz", "integrity": "sha512-h7pc7VIAqhw7w5SQBwzVu0wP0Zx7PSop6SKFq2xk1j8u6LXD1WNi+0LWnI8fT4wZCTCY8GJz2WRomrHWSYFjSA==", "signatures": [{"sig": "MEQCIHXWdWLW13x2cILL8RoggbrZ0ZjG9ocsEYQOn80+d8+iAiAnqh1Tkqy908D2KzrnZ6CBNe0ZZYg+8TA/rQLpZmM+4A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.1.2": {"name": "ajv", "version": "2.1.2", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^0.0.8", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "jshint": "^2.8.0", "istanbul": "^0.3.17", "phantomjs": "^1.9.18", "browserify": "^11.0.1", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.3.0", "json-schema-test": "^0.1.5", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "de9a213c4a45808a948926dec2bc965ee2452e96", "tarball": "https://registry.npmjs.org/ajv/-/ajv-2.1.2.tgz", "integrity": "sha512-2MEB4FvTVo03bDvHP3QTxjhp5/x8HOI1JB+cWweekjcxN2dzPKa/ANlrZXIDtopG+B6/BydQCf0/8XJyIIXEqA==", "signatures": [{"sig": "MEUCIQDdQBm9+eWZekCUoZQWwGMvA6F1IEeFox2D0YsM29FdlQIgMrdz1pHoCLOWWJMxSeJJ0tUktyo4aMF0+RnDm5lcx0I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.1.3": {"name": "ajv", "version": "2.1.3", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^0.0.8", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "jshint": "^2.8.0", "istanbul": "^0.3.17", "phantomjs": "^1.9.18", "browserify": "^11.0.1", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.3.0", "json-schema-test": "^0.1.5", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "fc6c2345346d6f9b44500bdac6659e3744e7bf3a", "tarball": "https://registry.npmjs.org/ajv/-/ajv-2.1.3.tgz", "integrity": "sha512-grbQ5OD0sV2vlBSNUiSmT5EzLb3Zg0fK9BG6TbL3ePJLvo6OlFyVGtSb98GxeUlX3F4cOEd1j3/haqOQzPQYfA==", "signatures": [{"sig": "MEUCIEgDWwgVRzEZhbJG5TBQle6aSNHqqiGB0FlF130yu+PJAiEA1aZc2amnQdBa6E1kdZ6z/OfZuFSx6NQqhb4++CPbbPg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.1.4": {"name": "ajv", "version": "2.1.4", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^0.0.8", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "jshint": "^2.8.0", "istanbul": "^0.3.17", "phantomjs": "^1.9.18", "browserify": "^11.0.1", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.3.0", "json-schema-test": "^0.1.5", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "80a2e4eb798e951694577c65f96be9cb0b3a566c", "tarball": "https://registry.npmjs.org/ajv/-/ajv-2.1.4.tgz", "integrity": "sha512-SDxgRFgJoDneBIefO3CQQXr/+IOf5Sv+jJ04ReIit/WGwC7j11Yf8ReWv5AP66pmcq45zwhcBqK4N13RT3H8+g==", "signatures": [{"sig": "MEUCIEgdVH87TE1bAwGVEQie7HlLr8NuRbqb0rvZwEjKKr5PAiEAnkt1ie6Hz1rekIaprNpBefjOKbnJxVjvgC/MhAISH/U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.2.0": {"name": "ajv", "version": "2.2.0", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^0.0.8", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "jshint": "^2.8.0", "istanbul": "^0.3.17", "phantomjs": "^1.9.18", "browserify": "^11.0.1", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.3.0", "json-schema-test": "^0.1.5", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "49bce9e8b21553130bd0d16a4a1bc054f1b48976", "tarball": "https://registry.npmjs.org/ajv/-/ajv-2.2.0.tgz", "integrity": "sha512-vAgMkF819DENP3s7WUwJCzd1b+cXyVJ93/wHOdEX4639hUnwgVFztBF83Ia9cNNDq7Vk2crYkjf0sQJAluOj6g==", "signatures": [{"sig": "MEYCIQDt7M11YSS8MDJRf67DH1tBg4fTdIEk79JCqlUMpW8ZfgIhAIAeLwum+Jy0+YDwmyW6epgMTySK95leMMdiRKQWknmj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.2.1": {"name": "ajv", "version": "2.2.1", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^0.0.8", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "jshint": "^2.8.0", "istanbul": "^0.3.17", "coveralls": "^2.11.4", "phantomjs": "^1.9.18", "browserify": "^11.0.1", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.3.0", "json-schema-test": "^0.1.5", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "dc15b5a385ea9127acc81ef8d9523e3e034c9ab6", "tarball": "https://registry.npmjs.org/ajv/-/ajv-2.2.1.tgz", "integrity": "sha512-l1cBD6I+JFZaDThfTyXomFM3ITmFfatc2isQfYwp3NsGcTxPrBTl2wzmDwr1XQsRa7KAs6ntRmZ46ucGKvcmUQ==", "signatures": [{"sig": "MEUCIQCb4gSo7vzgZEscQUW0K9TOBFbE0qIBKePHQzIzadi1MQIgCDjU7EU37gU9jF+Jxon5+KaV2CJMU6T9PvloP/VzTK4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.2.2": {"name": "ajv", "version": "2.2.2", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^0.0.8", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "jshint": "^2.8.0", "istanbul": "^0.3.17", "coveralls": "^2.11.4", "phantomjs": "^1.9.18", "browserify": "^11.0.1", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.3.0", "json-schema-test": "^0.1.5", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "3cef9f5ccb50a32fd9bdeb5eb16dae3dd8299724", "tarball": "https://registry.npmjs.org/ajv/-/ajv-2.2.2.tgz", "integrity": "sha512-OOfaAf05yL1YUoI9+b+pFoL8cxlLceXRUBPUEhQIIt33AXDRTPz6WvaURKvPM/rYGBTnCjNehahv4XX2BR81QA==", "signatures": [{"sig": "MEUCIGoKDt62yBOQkPrUu4umpB0qKhcU86TNjrsSmvMVqMuMAiEAv0vW/q7qTZ7iXXq6zexOzk3jhHm3MPxeUS1Uuv3TfOk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.3.0": {"name": "ajv", "version": "2.3.0", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^0.0.8", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "jshint": "^2.8.0", "istanbul": "^0.3.17", "coveralls": "^2.11.4", "phantomjs": "^1.9.18", "browserify": "^11.0.1", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.3.0", "json-schema-test": "^0.1.5", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "8ee6da40996dadf7df39669f0548f3c4f45d03fd", "tarball": "https://registry.npmjs.org/ajv/-/ajv-2.3.0.tgz", "integrity": "sha512-jSSuYY2NnIB2ZxlC7w9lvzYTp+AcnseYJr6SaTlkwCz7tA47AOGRwTGnpap5egibw/Nn1q2rVZcjJIntGe4rNQ==", "signatures": [{"sig": "MEUCIQDXtGfhoOFCDjxjhQXNM0ey1Hejb6j+wvmWzMbVbsNiEgIgVV0TXjdsQfiZTMWeEfBcA4g8+GQ1VahUZORwz3PYccU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.4.0": {"name": "ajv", "version": "2.4.0", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^0.0.8", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "jshint": "^2.8.0", "istanbul": "^0.3.17", "coveralls": "^2.11.4", "phantomjs": "^1.9.18", "browserify": "^11.0.1", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.3.0", "json-schema-test": "^0.1.6", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "932826bc9a04483594bd266d5278c06731cd096b", "tarball": "https://registry.npmjs.org/ajv/-/ajv-2.4.0.tgz", "integrity": "sha512-YaSoGFgwF9LxxLNiqwmNUc/JxOOSzUdmiZBf4uFlQOpX50Fei3Qb5fC15Cg+aHO/F9hMnaM4ZBKmMeWmYRCbLQ==", "signatures": [{"sig": "MEUCIQCNcNk6G/qi1lbEj/Gl3xh+Xq2+pdRVSLCESyVJ182tjwIgECuexuFwxo5JaGev4V07m4dmdzy0ZlurI4dCcya22Yc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.5.0": {"name": "ajv", "version": "2.5.0", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^0.0.8", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "jshint": "^2.8.0", "istanbul": "^0.3.17", "coveralls": "^2.11.4", "phantomjs": "^1.9.18", "browserify": "^11.0.1", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.3.0", "json-schema-test": "^0.1.6", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "8d5b5686df236cf1474a7525581fd11c02e957bf", "tarball": "https://registry.npmjs.org/ajv/-/ajv-2.5.0.tgz", "integrity": "sha512-c+NuTak9dF7Bq4CU/HbK6OVooRZuQEUUTeZ/vC1DG8fOAHxIxVRpLXaHN1c+oLXx2NGEMdn58Ju40iodweHvhQ==", "signatures": [{"sig": "MEUCIHdUpaK1Qn073J/kx7W/jN+uGvGgg6GzM7TArIdME3ejAiEAoHSDXQo9+O3jwKcAgO7FOhwPajon2jXRYk1hQV7N3VM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.0.0": {"name": "ajv", "version": "3.0.0", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^0.0.8", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "jshint": "^2.8.0", "istanbul": "^0.3.17", "coveralls": "^2.11.4", "phantomjs": "^1.9.18", "uglify-js": "^2.6.1", "browserify": "^11.0.1", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.3.0", "json-schema-test": "^1.0.0", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "2b76518cf0a239ac4bce6c0ed043ff3ce545a083", "tarball": "https://registry.npmjs.org/ajv/-/ajv-3.0.0.tgz", "integrity": "sha512-8jBWMJVV0Vvm3iKU3P01YOiLmxZ6a79vuZtlL/+e99Hi/43Vbt/KAduc2g9MLBk4PrEfWgf+E3tYt1UXkawE9Q==", "signatures": [{"sig": "MEYCIQDZWuTP6nvyFsvMsk4lJft2c4Yi0tsEEBpMxX0oTASntAIhAL9/pHhSWMmOJYF5uImzt78PT9BCZM/+y6PKOYEY+7iM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.0.1": {"name": "ajv", "version": "3.0.1", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^0.0.8", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "jshint": "^2.8.0", "istanbul": "^0.3.17", "coveralls": "^2.11.4", "phantomjs": "^1.9.18", "uglify-js": "^2.6.1", "browserify": "^11.0.1", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.3.0", "json-schema-test": "^1.0.0", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "13d385d6bca1ec19896ddd7a5ae7af56ae947e0b", "tarball": "https://registry.npmjs.org/ajv/-/ajv-3.0.1.tgz", "integrity": "sha512-ykcFyegRsbmjib3jZ0Y1LQ0rENudr66AlbsWFCSm1oYtRb4Ltt6DDioEKq2W8PdfVTx1lNwB+/2A8Q9aqXf0Fg==", "signatures": [{"sig": "MEUCIQDbsyx54cr6DWoXlAnDtzjj++tnIslPUy6RwYJ4xiUDTgIgH/SkS6zV6pOC2Mm3FqHP6hriPHLqXrX0/Cc4DIVhoKk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.0.2": {"name": "ajv", "version": "3.0.2", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^0.0.8", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "jshint": "^2.8.0", "istanbul": "^0.3.17", "coveralls": "^2.11.4", "phantomjs": "^1.9.18", "uglify-js": "^2.6.1", "browserify": "^11.0.1", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.3.0", "json-schema-test": "^1.0.0", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "d226fc24224b6c20dd75391b764bc902a9a99cb0", "tarball": "https://registry.npmjs.org/ajv/-/ajv-3.0.2.tgz", "integrity": "sha512-1l6vI7OZVwaja6tqcyR7CKHaz5ThocawPqGEm2F5KBbQ6OTBRj9a6J8LBLcLhCvOM8/gqBimU104BNJTstd3WQ==", "signatures": [{"sig": "MEUCIBDzEuRRPaqO5VAxXr88d41ntPHO9BZvpkF8VK6clU2VAiEAz9otuLniJT7IeRlOZcJQjovJgr3lxz07/nPbn4bDYrI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.0.3": {"name": "ajv", "version": "3.0.3", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^0.0.8", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "jshint": "^2.8.0", "istanbul": "^0.3.17", "coveralls": "^2.11.4", "phantomjs": "^1.9.18", "uglify-js": "^2.6.1", "browserify": "^11.0.1", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.3.0", "json-schema-test": "^1.0.0", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "b9f38fc3e1894ca231c99028ebe428188a35a316", "tarball": "https://registry.npmjs.org/ajv/-/ajv-3.0.3.tgz", "integrity": "sha512-+IWu4it6GPY1UhuuHDcrXPWe7aoHUTrnd3qaMsS6ahb35gacomjO7if0ACUULmK8lS8E+T1+BPmEYzd/8EPVaA==", "signatures": [{"sig": "MEQCIBiqYkZQIz8e2oriAJ3QTTlKsa5O6ZPI5nk+tPGBsoOhAiBZDBzc1/YuWe6oBWaOvxwU0kVHIwNhqQ+TX6KDl4K7aw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.0.4": {"name": "ajv", "version": "3.0.4", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^0.0.8", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "jshint": "^2.8.0", "istanbul": "^0.3.17", "coveralls": "^2.11.4", "phantomjs": "^1.9.18", "uglify-js": "^2.6.1", "browserify": "^11.0.1", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.3.0", "json-schema-test": "^1.0.0", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "dff63fad8f12e3c52f3ddd1210810e80866a0cf4", "tarball": "https://registry.npmjs.org/ajv/-/ajv-3.0.4.tgz", "integrity": "sha512-p11T0vc/Qmd81ujWkUAh3UoVVyJ9SmiwFvmxD5pj5wPIHjyioFQnEwCg965Zua2vlABYVHswFlzldqKwMEkwCw==", "signatures": [{"sig": "MEUCIQCS6nqvmQ8ph0exUl0u2j1l2gyV97KIelUr1SjzeBmQBwIgEU3DLByhkreIJGiDqjA5Wcd9TxNi0Uaynpbz9NdfmDU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.1.0": {"name": "ajv", "version": "3.1.0", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^0.0.8", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "jshint": "^2.8.0", "istanbul": "^0.3.17", "coveralls": "^2.11.4", "phantomjs": "^1.9.18", "uglify-js": "^2.6.1", "browserify": "^11.0.1", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.3.0", "json-schema-test": "^1.0.0", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "b236b6b5727e533e947a7189562c8c96276f1179", "tarball": "https://registry.npmjs.org/ajv/-/ajv-3.1.0.tgz", "integrity": "sha512-ikcvfPbpaLP30YPQhJH88I1lcro+7lcXOKMNjEIhKy52jnaHyCqhnuNKknOzt5NGAppMBSxMJ2iWjgeDD3IJyw==", "signatures": [{"sig": "MEQCIFkfXu6IN8PPVdM4do+E3zayvgiSfAx0m3mxOfg6/adWAiBlkHlKscOxovotVC499CFRiezHLpLnbrt5F7+to1srhA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.1.1": {"name": "ajv", "version": "3.1.1", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^0.0.8", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "jshint": "^2.8.0", "istanbul": "^0.3.17", "coveralls": "^2.11.4", "phantomjs": "^1.9.18", "uglify-js": "^2.6.1", "browserify": "^11.0.1", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.3.0", "json-schema-test": "^1.0.0", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "15de82f0bbec488f65770e0a76df718d4c0a786f", "tarball": "https://registry.npmjs.org/ajv/-/ajv-3.1.1.tgz", "integrity": "sha512-qRvxfKJMlwlJRMgqnsfZgE8NgDLVaUpRhEyUjcGHX2AiN9qogDs2cN59auAsWA88mh/jzk91ym5h0RdNNm/dEA==", "signatures": [{"sig": "MEQCIFLp6WpwQYbXSq5zHulmS+YpZaozh1RTKaQQnjE6oGuoAiBCdt8ujoFcL/Ql7fjuog+TPSxt0Tst5/dX8Q9kVskutg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.2.0": {"name": "ajv", "version": "3.2.0", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^0.0.8", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "jshint": "^2.8.0", "istanbul": "^0.3.17", "coveralls": "^2.11.4", "phantomjs": "^1.9.18", "uglify-js": "^2.6.1", "browserify": "^11.0.1", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.3.0", "json-schema-test": "^1.0.0", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "017102288e62231385dcfa5b85319a7e6b2827e4", "tarball": "https://registry.npmjs.org/ajv/-/ajv-3.2.0.tgz", "integrity": "sha512-OONOaO3P2ub4AfHiWEYAASH6qPqH4kppjqUAqpkzlCdtYqetI55ba/RPjpNbRft6yzDXzy+tQylyG5Cj9qMYjw==", "signatures": [{"sig": "MEQCIBxybn/2rG9kFoOKYPpS/LBSPLWt+6vw/WbyXQDAkHe7AiAplxdo+iwQ32PFcKCARqLvSfZxWN87iqWmXiCE6I/Ojw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.2.1": {"name": "ajv", "version": "3.2.1", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^0.0.8", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "jshint": "^2.8.0", "istanbul": "^0.3.17", "coveralls": "^2.11.4", "phantomjs": "^1.9.18", "uglify-js": "^2.6.1", "browserify": "^11.0.1", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.3.0", "json-schema-test": "^1.0.0", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "574b7154b76af20ede2a44815633322a49e8de6f", "tarball": "https://registry.npmjs.org/ajv/-/ajv-3.2.1.tgz", "integrity": "sha512-fXtGdqYfKXqsnZi/LACeqdiE0PyBlf5E+P5WGv7R66OI/BY4aJzeyHp+PYmyvDOz1vcoIA52kmxtF9b4Q9Y7Xw==", "signatures": [{"sig": "MEQCIBke0vggdUgrtWo7ZLsWXvGj2ocTQmLtxXWeg7xUxFbfAiBRyn0WxA3evi/fCbv0QxsceWhoOVQFnYZ/KySceCBpTw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.2.2": {"name": "ajv", "version": "3.2.2", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^0.0.8", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "jshint": "^2.8.0", "istanbul": "^0.3.17", "coveralls": "^2.11.4", "phantomjs": "^1.9.18", "uglify-js": "^2.6.1", "browserify": "^11.0.1", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.3.0", "json-schema-test": "^1.0.0", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "dc968e3b455d446069b4d17ca5f6272efb839593", "tarball": "https://registry.npmjs.org/ajv/-/ajv-3.2.2.tgz", "integrity": "sha512-lcwBckqGNjaQNjIa2oFdYgwlpY5mFGcpXSWP0pyc0os8/qzPyuDoaPGs2jEoFpmBGkvbRS+ay7t1uFlxQNmWoQ==", "signatures": [{"sig": "MEQCIG+fWmAZW6Wm1Ykx65wdi+ctqwsXye35eR2QyY9Vj3O8AiBP27FTyPeniOwFeSLyrhLhZet2P3aAL4z+5Uu4DPJk/g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.2.3": {"name": "ajv", "version": "3.2.3", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^0.0.8", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "jshint": "^2.8.0", "istanbul": "^0.3.17", "coveralls": "^2.11.4", "phantomjs": "^1.9.18", "uglify-js": "^2.6.1", "browserify": "^11.0.1", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.3.0", "json-schema-test": "^1.0.0", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "9438afbed7a65e4edf3b8bdcd819fb52533b6495", "tarball": "https://registry.npmjs.org/ajv/-/ajv-3.2.3.tgz", "integrity": "sha512-Qdti8kRlgZuit9ncpDwnnWSf2JdzEkE+RsSzmovobrjagTMWxh0auLE1kACl67y5V/6k3H6ljbCRTObiaCWRRQ==", "signatures": [{"sig": "MEQCICnz8JRaTWu57HOHY7lGfx0G++kNj3OHGNujbLnXfi/tAiB7sTm+D5LxeV9mCxRIzunZEQP0JSEUg/A2m99wE4Q/SQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.3.0": {"name": "ajv", "version": "3.3.0", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^0.0.8", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "jshint": "^2.8.0", "istanbul": "^0.3.17", "coveralls": "^2.11.4", "phantomjs": "^1.9.18", "uglify-js": "^2.6.1", "browserify": "^11.0.1", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.3.0", "json-schema-test": "^1.0.0", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "8caee66a6f823d72551810b1a1e430e78bf486bf", "tarball": "https://registry.npmjs.org/ajv/-/ajv-3.3.0.tgz", "integrity": "sha512-Z0xeNrv7k6aNCSGAyKGUh1LAa/FCDxOejmIithWxAXB0o8Kb8BuqJB0UHb2h2nDKgM4XFuCdUuTiV8Ggf0l7cw==", "signatures": [{"sig": "MEUCIGWebGYCij6nDLPTi9mWY6xJBgElRztcW/PplXsx/77jAiEA5ptdlFtUfLDQVvIq1JreGSoyt7I0JcucKcyyChKc5L8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.3.1": {"name": "ajv", "version": "3.3.1", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^0.0.8", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "jshint": "^2.8.0", "istanbul": "^0.3.17", "coveralls": "^2.11.4", "phantomjs": "^1.9.18", "uglify-js": "^2.6.1", "browserify": "^11.0.1", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.3.0", "json-schema-test": "^1.0.0", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "e99a399a36086b1e84f20c2219f3ab0107676d15", "tarball": "https://registry.npmjs.org/ajv/-/ajv-3.3.1.tgz", "integrity": "sha512-LlazGqnkFZ8eiG/dAbYa/2fYugQEakfGMlERBNsHd2p4eQpjjrcz413e7ZWUvXa0w/amC3c43FS0ckJfHH+dTQ==", "signatures": [{"sig": "MEQCIHSBpPa04yCHsF4hcvJmiwT4p2GbzcWEiAIaNAx0khk9AiAEaiOnG9ilExbHBmRx2mJQ0V3ZWtp16WNr3hEBFgcrxQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.4.0": {"name": "ajv", "version": "3.4.0", "dependencies": {"json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^0.0.8", "chai": "^3.0.0", "glob": "^5.0.10", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.16.0", "jshint": "^2.8.0", "istanbul": "^0.3.17", "coveralls": "^2.11.4", "phantomjs": "^1.9.18", "uglify-js": "^2.6.1", "browserify": "^11.0.1", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "require-globify": "^1.3.0", "json-schema-test": "^1.0.0", "karma-sauce-launcher": "^0.2.14", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "172c70640b750239f9fffff46c3485577fd30690", "tarball": "https://registry.npmjs.org/ajv/-/ajv-3.4.0.tgz", "integrity": "sha512-wwBU7SddLaAWn8E8N9uIFurt6aYGJhiIsCfD9A+JZQs4tEehM3DHLaaN/VwHLw6VCKr+b0mXC9kvchllKhJ+5A==", "signatures": [{"sig": "MEUCIQCMMBdPPQ5F+jeXaOrgYzTqzpWoTS6ZiaVR6Pwpax34hgIgKAsspkoiBxUjE0VjUnjprfBO68wUFh/tOBmV566AVVk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.5.0": {"name": "ajv", "version": "3.5.0", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^1.4.3", "chai": "^3.0.0", "glob": "^6.0.4", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.17.1", "jshint": "^2.8.0", "nodent": "^2.3.11", "bluebird": "^3.1.5", "istanbul": "^0.4.2", "coveralls": "^2.11.4", "phantomjs": "^2.1.2", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "regenerator": "^0.8.42", "require-globify": "^1.3.0", "json-schema-test": "^1.0.0", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "63b9be83bb7bfe04e41afd79b58338f144e05672", "tarball": "https://registry.npmjs.org/ajv/-/ajv-3.5.0.tgz", "integrity": "sha512-r+EPLCvjvuA38ozI42rj2GWJeoOLRg+2mJ9H0RRNXwV/FBe0aUqEuext1Fg+L0M2ltJy5p0zIsKhN0TQSDdF6g==", "signatures": [{"sig": "MEQCIBbTcwX0mh/D4DragW7MSHY2yBCQVmpsdEd/KWR8klztAiABfWy+PhTiNOjVWf6SIHP1wbWZ4sSb894YJKDPFAoKww==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.5.1": {"name": "ajv", "version": "3.5.1", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.0"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^1.4.3", "chai": "^3.0.0", "glob": "^6.0.4", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.17.1", "jshint": "^2.8.0", "nodent": "^2.3.11", "bluebird": "^3.1.5", "istanbul": "^0.4.2", "coveralls": "^2.11.4", "phantomjs": "^2.1.2", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "regenerator": "^0.8.42", "require-globify": "^1.3.0", "json-schema-test": "^1.0.0", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "f3ed6f6bd410768fe872ef130077ef1dd6123309", "tarball": "https://registry.npmjs.org/ajv/-/ajv-3.5.1.tgz", "integrity": "sha512-iNJ8CclgxvPpVtxypsHN9+yTdI3x5w5dJEO+fZz8zEjxXaK0Tn28DapjmvDaVyRm4zI0HSUDmYrVgJLNuavpIg==", "signatures": [{"sig": "MEUCIQDtQBKjvfca/O9JiEEUC9c6HqOiCMhKZM/SB7qlQ3yhswIgF+vOC1ICfNkVqxt0DNFQo/WzWgBHZ2AwMxuECfIAzsM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.5.2": {"name": "ajv", "version": "3.5.2", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^1.4.3", "chai": "^3.0.0", "glob": "^6.0.4", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.17.1", "eslint": "^1.10.3", "jshint": "^2.8.0", "nodent": "^2.3.13", "bluebird": "^3.1.5", "istanbul": "^0.4.2", "coveralls": "^2.11.4", "phantomjs": "^2.1.2", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "regenerator": "^0.8.42", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "4f4c0ac48035c41a213e74e0afca1ba5e1f7e4ad", "tarball": "https://registry.npmjs.org/ajv/-/ajv-3.5.2.tgz", "integrity": "sha512-LgYe5Z36wFd9Jcj/ooaaBI7Oj+VJxMAXnPegm2UG2R1AdMgxs+8p+vb5Wd73uf1Py/pHfy+Z2ZCdluHZ/7Dbjw==", "signatures": [{"sig": "MEQCIHy7p1MsZ1KISL7Wooe0jmKjnbmn4ch7CrDVTUrOTNhnAiA5Y7dyrgJ/rioScFz5GH2RoFr3zrqr4rNjRse2fcoIJg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.5.3": {"name": "ajv", "version": "3.5.3", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^1.4.3", "chai": "^3.0.0", "glob": "^6.0.4", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.17.1", "eslint": "^2.0.0-rc.0", "jshint": "^2.8.0", "nodent": "^2.3.13", "bluebird": "^3.1.5", "istanbul": "^0.4.2", "coveralls": "^2.11.4", "phantomjs": "^2.1.2", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "regenerator": "^0.8.42", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "037dab3bdc80736fa929a7cd78c4aca6a9d3119f", "tarball": "https://registry.npmjs.org/ajv/-/ajv-3.5.3.tgz", "integrity": "sha512-UHzyDYZUvFlJZdmAonKw2NJhgOAQoAJFu1F92EtjAwGMFyBZts5Is2A8oc2yA2XYgOzc5gB2o3vaq4+IOcyz2g==", "signatures": [{"sig": "MEUCIAsKZOc8jMDiT3st/OobMyNx4PCeWJdw80C8T4kB/fOhAiEA4I8hzH8eVYGgZgc8Xfdcwg9UuYmKYwJtg/8bDStMUAQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.6.0": {"name": "ajv", "version": "3.6.0", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^1.4.3", "chai": "^3.0.0", "glob": "^7.0.0", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.17.1", "eslint": "^2.0.0-rc.0", "jshint": "^2.8.0", "nodent": "^2.3.13", "bluebird": "^3.1.5", "istanbul": "^0.4.2", "coveralls": "^2.11.4", "phantomjs": "^2.1.2", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "regenerator": "^0.8.42", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "ce935e2a2c484a76d8f58a3a69c7a1898ca439d3", "tarball": "https://registry.npmjs.org/ajv/-/ajv-3.6.0.tgz", "integrity": "sha512-qYb91EKEIZEL1B2lX2h4q9qJy/CMvjEZQDDRe9gpr5HosCcg5D7nThB6Xm6+KgDE4Tw48gfSvgTLh9D38Uawtg==", "signatures": [{"sig": "MEQCIFMnovhTT1avdg13HhtnPuwCB7S9ARpNzUOVVRJntf0WAiBfWZQOjxxsSNLItZ58+w5ysN7JYI8C4u5supJqyAEnbw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.6.1": {"name": "ajv", "version": "3.6.1", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^1.4.3", "chai": "^3.0.0", "glob": "^7.0.0", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.17.1", "eslint": "^2.0.0-rc.0", "jshint": "^2.8.0", "nodent": "^2.3.13", "bluebird": "^3.1.5", "istanbul": "^0.4.2", "coveralls": "^2.11.4", "phantomjs": "^2.1.2", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "regenerator": "^0.8.42", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "3dbb99d1043db40db471146e82c05983bab49aea", "tarball": "https://registry.npmjs.org/ajv/-/ajv-3.6.1.tgz", "integrity": "sha512-gMWJ+za6DP9ImF3FI2lETnnHiG6o6aNz8nQ8vWApmVh/4fPgotBC68t4lc0hWZd6UazLOEL49zr9XEfOSFrnGg==", "signatures": [{"sig": "MEUCIDzXHCuMu0c4unpLuEs7Ggz0Mt4q4WwVhjxePuzSg827AiEA5rgfEkmc7opDqV8gXfhY2DprFKYUKSix9McUUGQaLKg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.6.2": {"name": "ajv", "version": "3.6.2", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^1.4.3", "chai": "^3.0.0", "glob": "^7.0.0", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.17.1", "eslint": "^2.0.0-rc.0", "jshint": "^2.8.0", "nodent": "^2.3.13", "bluebird": "^3.1.5", "istanbul": "^0.4.2", "coveralls": "^2.11.4", "phantomjs": "^2.1.2", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "regenerator": "^0.8.42", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "f8dc6edeaf18a4ee54d8040469f253221e847aa4", "tarball": "https://registry.npmjs.org/ajv/-/ajv-3.6.2.tgz", "integrity": "sha512-2fygp94ra68u0RN56FjowiICJ4y4igUnNtbd3SPAgXLRaPDVjogLtWMUL4NhPfnQYgWjtpBU0LpIlaW9vQQ/cQ==", "signatures": [{"sig": "MEQCIEO5La8Hrhj4d3mqXQ4winvZX6Y7iHQ/gUL0T6OD0avxAiB+Egzyebd6nuQQcbSylWwZb+mzx6hboYKpNuAEeWg28Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.7.0": {"name": "ajv", "version": "3.7.0", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^1.4.3", "chai": "^3.0.0", "glob": "^7.0.0", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.17.1", "eslint": "^2.0.0-rc.0", "jshint": "^2.8.0", "nodent": "^2.3.13", "bluebird": "^3.1.5", "istanbul": "^0.4.2", "coveralls": "^2.11.4", "phantomjs": "^2.1.2", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "regenerator": "^0.8.42", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "77fc88ec89697be252341fd631b3485776b50812", "tarball": "https://registry.npmjs.org/ajv/-/ajv-3.7.0.tgz", "integrity": "sha512-n4P8+lZyRUS5wdVHa3cO7qOF3rRb9iDGwmaXp0O2eXvM6FWFsLk2q8oA2oHCESqeUVUmNTjOc1iD1WKDTKT5fQ==", "signatures": [{"sig": "MEUCIQCR3xj1JAz1jWZbo0HU4JGvKVnscm3QwEAxdiB3WlbSAAIge4W4Pg2YLPEkNqeNHGYXz6M4bDCURf1zKgYevs39rww=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.7.1": {"name": "ajv", "version": "3.7.1", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^1.4.3", "chai": "^3.0.0", "glob": "^7.0.0", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.17.1", "eslint": "^2.0.0-rc.0", "jshint": "^2.8.0", "nodent": "^2.3.13", "bluebird": "^3.1.5", "istanbul": "^0.4.2", "coveralls": "^2.11.4", "phantomjs": "^2.1.2", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "regenerator": "^0.8.42", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "d85ecc3ac88778972fd74368817505f5c28e6a5a", "tarball": "https://registry.npmjs.org/ajv/-/ajv-3.7.1.tgz", "integrity": "sha512-yvDhVbClDvLaNMShDuuLSS+hoGllPqTA6JnGVxiU9NyFxSOCwSvA41J4HBI9kkAWf9g+SlBW7lummLB7xRWnug==", "signatures": [{"sig": "MEUCIQCKtInxFfNIXv//+9EK9kRb0b3n95ZB/InBTNLfeXaJewIgF7ApKILKwEpMn8Zy8OwTyMC8ddt8AcA7m1jes/0UwdY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.7.2": {"name": "ajv", "version": "3.7.2", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^1.4.3", "chai": "^3.0.0", "glob": "^7.0.0", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.17.1", "eslint": "^2.0.0-rc.0", "jshint": "^2.8.0", "nodent": "^2.3.13", "bluebird": "^3.1.5", "istanbul": "^0.4.2", "coveralls": "^2.11.4", "phantomjs": "^2.1.2", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "regenerator": "^0.8.42", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "6d1ff4234752a4e91957cd1facef6182316d1b02", "tarball": "https://registry.npmjs.org/ajv/-/ajv-3.7.2.tgz", "integrity": "sha512-svrV858thFUmPgkIGBEUvg004WhKJPzQyOsD43VWMZbMYPl0SXw7gKZopW01PgUImmlQucgrfL9jhTn3JTMOwA==", "signatures": [{"sig": "MEUCIDY/WLnIoIap/tQL4Xttsev2ZCGJ2yA3rILqvA41kN2vAiEAwV8s2a45SeyWUUfe249FFpw851J7xNt5+PeT3uKAmIo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.8.0": {"name": "ajv", "version": "3.8.0", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^1.4.3", "chai": "^3.0.0", "glob": "^7.0.0", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.17.1", "eslint": "^2.0.0-rc.0", "jshint": "^2.8.0", "nodent": "^2.3.13", "bluebird": "^3.1.5", "istanbul": "^0.4.2", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "regenerator": "^0.8.42", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "b8e5b2daf798b60d61c6a583a9396ec6203458a6", "tarball": "https://registry.npmjs.org/ajv/-/ajv-3.8.0.tgz", "integrity": "sha512-dpL9KOfsV8ikPlcLdbwDYzS+QzVgS/uOL1IkReja4D6es+1Qd/nvuPgqLngGmjap26MpN1kMO+PyUv7GAdHxOA==", "signatures": [{"sig": "MEUCIAeXfQESVYHrHmWcubia38Q/sX613RJTWn4q/6T4BegGAiEAsVR63eTwn9oWKlYXZXc+1Kq6r32juoGbab2VZZGUY9o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.8.1": {"name": "ajv", "version": "3.8.1", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^1.4.3", "chai": "^3.0.0", "glob": "^7.0.0", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.17.1", "eslint": "^2.0.0-rc.0", "jshint": "^2.8.0", "nodent": "^2.3.13", "bluebird": "^3.1.5", "istanbul": "^0.4.2", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "regenerator": "^0.8.42", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "52acd02d37f2e733bd8befaff6bbf8df3cf92e9f", "tarball": "https://registry.npmjs.org/ajv/-/ajv-3.8.1.tgz", "integrity": "sha512-137SxhUHKBEhvCicINjBoI6Qyy1R5bxTYpkoA81IIl5L2JidM7xr3FQeVHOo8rkormisXYcpyCYuC1Qj5y/iLA==", "signatures": [{"sig": "MEUCIBEYeCzrvQhbtsWWHRzW3FC5QnZOPYtqiEEQ3I9BRQXPAiEAxKdIFVUSlSA7Gh9LeS0VPWgRYLakQdNNcr4P4Tv9zEM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.8.2": {"name": "ajv", "version": "3.8.2", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^1.4.3", "chai": "^3.0.0", "glob": "^7.0.0", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.17.1", "eslint": "^2.0.0-rc.0", "jshint": "^2.8.0", "nodent": "^2.3.13", "bluebird": "^3.1.5", "istanbul": "^0.4.2", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "regenerator": "^0.8.42", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "4460402b40bf9f763ddbfb1d374c878ebe9869d9", "tarball": "https://registry.npmjs.org/ajv/-/ajv-3.8.2.tgz", "integrity": "sha512-w2VQ33uJFjaL3+jUNrsG60kNWSf/kNvwIsixgK1pFy7Naf2G2QwBB06w7hi+sMfNqYeu7KASnO0+mZMDUfKBOw==", "signatures": [{"sig": "MEQCIDwKjndxGpuZHnuk9g7k3bGB3fcb1FsQiycHgmr0Oq6EAiBY3sMiU7dfK8gvrB6MJhRP8aIdLc03l3n0qmbiu+5HRA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.8.3": {"name": "ajv", "version": "3.8.3", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^1.4.3", "chai": "^3.0.0", "glob": "^7.0.0", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.17.1", "eslint": "^2.0.0-rc.0", "jshint": "^2.8.0", "nodent": "^2.3.13", "bluebird": "^3.1.5", "istanbul": "^0.4.2", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "regenerator": "^0.8.42", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "2f6e9d03e4b9f75ac1e4a391ac6e75d335b4cd49", "tarball": "https://registry.npmjs.org/ajv/-/ajv-3.8.3.tgz", "integrity": "sha512-5UBRBNh/Dk88WF6flSfrcVWE5n/VmcnojvAV8TJDN/EqFpwXxiCL9hT3+tJVHgwbdqfeqDKeLAaxzetvefA+3g==", "signatures": [{"sig": "MEQCIBwms1onm6x82eRPWjw2u4zcg4SgqBJ6YSn4JceC7ZlZAiBuYWglSwLont6EPi8oSUaLPKmYS8Kai0TNYqtu8deQow==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.8.4": {"name": "ajv", "version": "3.8.4", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^1.4.3", "chai": "^3.0.0", "glob": "^7.0.0", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.17.1", "eslint": "^2.0.0-rc.0", "jshint": "^2.8.0", "nodent": "^2.3.13", "bluebird": "^3.1.5", "istanbul": "^0.4.2", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "regenerator": "^0.8.42", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "73b49e161e850870dbb8a752900b74a7dee33fb0", "tarball": "https://registry.npmjs.org/ajv/-/ajv-3.8.4.tgz", "integrity": "sha512-A62+7/1gK+3BL+56GcDds6pq3bzItF3Kh4AQ0gh/EsC72a6ydxJhOVt3no2CmoihPKCpAbi4+nBTp+Mi6sj0dg==", "signatures": [{"sig": "MEUCIFS+8/Iu1zKhkZZ9exEhqMtg388c5Kb+sgsAAV+47aZYAiEA+rNZlqvbhHhTpYainHk9h0cJ5LlTB+e5HFVH4ojTWbQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.8.5": {"name": "ajv", "version": "3.8.5", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^1.4.3", "chai": "^3.0.0", "glob": "^7.0.0", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.17.1", "eslint": "^2.0.0-rc.0", "jshint": "^2.8.0", "nodent": "^2.3.13", "bluebird": "^3.1.5", "istanbul": "^0.4.2", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "regenerator": "^0.8.42", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "c8ded3806a97a6bc56fba27cb9e044283f3f7db3", "tarball": "https://registry.npmjs.org/ajv/-/ajv-3.8.5.tgz", "integrity": "sha512-GjnCyfOLGcwXLGfiDeQ3632S74NVkiXawakZSr+qQkJFW5KnzS8+6mXSem/ls2nSqm2gWDVqYl90Ev7QsmdjZw==", "signatures": [{"sig": "MEQCIFwzCzsDtc5VTwqm7vq/xbbZJ6+0TU6bGzOAKLHG3tPQAiBOjNr2ElngAZE1zW+YIsSxGbjRsnY6CCQEpsnFUtNjNw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.8.6": {"name": "ajv", "version": "3.8.6", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^1.4.3", "chai": "^3.0.0", "glob": "^7.0.0", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.17.1", "eslint": "^2.0.0-rc.0", "jshint": "^2.8.0", "nodent": "^2.3.13", "bluebird": "^3.1.5", "istanbul": "^0.4.2", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "regenerator": "^0.8.42", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "bf4c5526b62ea2c57b08d443c78370a7c65d5f87", "tarball": "https://registry.npmjs.org/ajv/-/ajv-3.8.6.tgz", "integrity": "sha512-+5n9qUfVdYPki2frhHunoMK7gfkuJ009LkaAoRxYP/gWJ9RFesae8P8uAouvZj2rMr2eVcTsQhERqnllmi+tyw==", "signatures": [{"sig": "MEUCIFMesUwW9C9yd88c/fHPCxCYTQd0cyLiPr9Tos69k0TiAiEAnhl6Uml6ewX/NHxMXoT0rZP61QZhQXhYeusnUyrEjM4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.8.7": {"name": "ajv", "version": "3.8.7", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^1.4.3", "chai": "^3.0.0", "glob": "^7.0.0", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.17.1", "eslint": "2.4.0", "jshint": "^2.8.0", "nodent": "^2.3.13", "bluebird": "^3.1.5", "istanbul": "^0.4.2", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "regenerator": "^0.8.42", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "5f2a4b6026176f6da9ef991f9f46ab768497e6b5", "tarball": "https://registry.npmjs.org/ajv/-/ajv-3.8.7.tgz", "integrity": "sha512-APrcwae/nSw50c1bV32H87HSFLXsaj2nfnUb3PoJ23Aty4k8p1i1ayJN4QA8+dn5Ft4DyxSHR5lSY7PdK34JLg==", "signatures": [{"sig": "MEYCIQDBk+G3q8aqgA5BBDdIysK0jlm17bXdgzVE5zEiDmAzEwIhAL54/veOhHxPFV3BtJlzL5+L/W/jHiFkM8VETPPizvx3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.8.8": {"name": "ajv", "version": "3.8.8", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^1.4.3", "chai": "^3.0.0", "glob": "^7.0.0", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.17.1", "eslint": "^2.5.1", "jshint": "^2.8.0", "nodent": "^2.3.13", "bluebird": "^3.1.5", "istanbul": "^0.4.2", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "regenerator": "^0.8.42", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "c4e70ec0c590442625291b51af7fc6a46df852a8", "tarball": "https://registry.npmjs.org/ajv/-/ajv-3.8.8.tgz", "integrity": "sha512-ZoVNz+kmsCfHXpvlrRFC3p3RAsG1W1o1ARu1corrh2e/wYfyb6fLuPqbbjEWfbK/nUNo2OAT2UrRFjzQpPmGvg==", "signatures": [{"sig": "MEQCIA59WFMp1CdKgDdcYRio7QR2LSjdFJbe5uZoU/TxCyjAAiAzv+9lOt7cExPpGsWbsABDBcQv0r2DgiqbFz2ZtSDshQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.8.9": {"name": "ajv", "version": "3.8.9", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^1.4.3", "chai": "^3.0.0", "glob": "^7.0.0", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.17.1", "eslint": "^2.5.1", "jshint": "^2.8.0", "nodent": "^2.3.13", "bluebird": "^3.1.5", "istanbul": "^0.4.2", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "regenerator": "^0.8.42", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "cd5a9a9fe55f0a1baead78240aee8a8c64e65b4b", "tarball": "https://registry.npmjs.org/ajv/-/ajv-3.8.9.tgz", "integrity": "sha512-l65dRIU5vTAf5m2Zc+1/3iYuHvp7Z5+Eoq3ckSbA8UrwZqHam28JQUCrrO+6gqvqQm9T1aK+yZcfrXrwKO2lLQ==", "signatures": [{"sig": "MEQCIA3/2fXm1aFERUgWceiGNHL6vJE6GTacS7cBooVxyvAVAiAQDOpwnlyG3cFe6m9B00Gbv2g17OeWDInTV1ieIddtKw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.8.10": {"name": "ajv", "version": "3.8.10", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^1.4.3", "chai": "^3.0.0", "glob": "^7.0.0", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.17.1", "eslint": "^2.5.1", "jshint": "^2.8.0", "nodent": "^2.3.13", "bluebird": "^3.1.5", "istanbul": "^0.4.2", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "regenerator": "^0.8.42", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "78dfd5615e2cf4c0f8cb7f3284e0257dbbe867c9", "tarball": "https://registry.npmjs.org/ajv/-/ajv-3.8.10.tgz", "integrity": "sha512-h74deHfbgeB8TuWq6UQxP4fwsCbo9T+pvWofl4pEdZzI6lMSSFAWZr3PsNXHjSFABCj49j6fgbzUTHWIrMdHMw==", "signatures": [{"sig": "MEQCIGrvcFbTbgUPmyFd3MGEGGGBkh9/Qg/157Zu82wOaALaAiBBW5C5KCRG4ORxNVpchwagx/p5X+oS8oZ+QL3Mx8gzKQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.0.0": {"name": "ajv", "version": "4.0.0", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^1.4.3", "chai": "^3.0.0", "glob": "^7.0.0", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.17.1", "eslint": "^2.5.1", "jshint": "^2.8.0", "nodent": "^2.3.13", "bluebird": "^3.1.5", "istanbul": "^0.4.2", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "regenerator": "^0.8.42", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "52c64e4ea85dd6506f5b8944172848d61a94ebff", "tarball": "https://registry.npmjs.org/ajv/-/ajv-4.0.0.tgz", "integrity": "sha512-q24/F9KCSZUuYkGYo3M0kAOLRdi7RrKUj4Pgt6JBQETgsQJC2YBo1xVpLE7qJVfn/bjXkXMEu056bhb6WPpqMw==", "signatures": [{"sig": "MEUCIFJU2kpI2WCCaZ9BR2qLnMosb+MPj3z01XlV06jAsK63AiEAhpc3tp8pikwMuLPDSPWT4jryxa8R8z//3FSbPwR3uFk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.0.1": {"name": "ajv", "version": "4.0.1", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^1.4.3", "chai": "^3.0.0", "glob": "^7.0.0", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.17.1", "eslint": "^2.5.1", "jshint": "^2.8.0", "nodent": "^2.3.13", "bluebird": "^3.1.5", "istanbul": "^0.4.2", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "regenerator": "^0.8.42", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "55ec59f2c512afbb2c807f1411da1fcddbedcab6", "tarball": "https://registry.npmjs.org/ajv/-/ajv-4.0.1.tgz", "integrity": "sha512-CGFOYyHH3uwZi5N/zvyqi7AAtAsuQpArroq7qZSrBNFXVbHBlEbYgcuKD9cXRGw4kTM3j/1MLguXbYnbe5VGhA==", "signatures": [{"sig": "MEUCIE0ezdOVxTJtjZ53fMGAkKArAvYoop7OvK2sCiNYrcR5AiEA/VyqYNbDcbxP3/TfBEGMO3FPYxR+rkEeZpkHg48fC1g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.0.2": {"name": "ajv", "version": "4.0.2", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^1.4.3", "chai": "^3.0.0", "glob": "^7.0.0", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.17.1", "eslint": "^2.5.1", "jshint": "^2.8.0", "nodent": "^2.3.13", "bluebird": "^3.1.5", "istanbul": "^0.4.2", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "typescript": "^1.8.10", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "regenerator": "^0.8.42", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "626c92324c6906956e057574565d3bea8cf17e53", "tarball": "https://registry.npmjs.org/ajv/-/ajv-4.0.2.tgz", "integrity": "sha512-km8HHifTW41Jv8S9LZG7MWZyQ0r40aqoVn6pTO6xwZ+0B/VdnQzczkBq1w2/YPWkk38r806Ng4jzNlaWaZlFAA==", "signatures": [{"sig": "MEUCICyFksmwadu6XHHtFTom7ae8U1Yfib9LiAOOXcE8Te0sAiEA1HQl6Ltca1rTj6ZZFgyoVdR9FhKrCvgRyurueE3NV2Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.0.3": {"name": "ajv", "version": "4.0.3", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^1.4.3", "chai": "^3.0.0", "glob": "^7.0.0", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.17.1", "eslint": "^2.5.1", "jshint": "^2.8.0", "nodent": "^2.3.13", "bluebird": "^3.1.5", "istanbul": "^0.4.2", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "typescript": "^1.8.10", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "regenerator": "^0.8.42", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "c242f3e5db15011b766792e4bbfdc5e60cb6a6aa", "tarball": "https://registry.npmjs.org/ajv/-/ajv-4.0.3.tgz", "integrity": "sha512-5LKjUPgpiIQqNfEc40T6XDSYF/frcujJaThEP9k/ql1SLoSfvx8mAsKr2UPdZjlu9s21vGAjw5YciDDlWWc4jA==", "signatures": [{"sig": "MEYCIQCP6xh0X+KLGPH3fATbrZdEWn5W09DF5KYpzQEu0gQ2EgIhAOcXkPyfkhMHj1JrlwdCjF8b1B3CV0JJhSfFWleWvO4t", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.0.4": {"name": "ajv", "version": "4.0.4", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^1.4.3", "chai": "^3.0.0", "glob": "^7.0.0", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.17.1", "eslint": "^2.5.1", "jshint": "^2.8.0", "nodent": "^2.3.13", "bluebird": "^3.1.5", "istanbul": "^0.4.2", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "typescript": "^1.8.10", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "regenerator": "^0.8.42", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "8bfc3e1bcb2fca11e345e5cfc37568f647d3514f", "tarball": "https://registry.npmjs.org/ajv/-/ajv-4.0.4.tgz", "integrity": "sha512-Ka9gxjNvN1LxTMGwkOcYXIUHCkDOlmEbL01ddpV95EpQFIr9x+VUa+P1rEs41tfnCjR3PDbZf4VrQZJEf8rsUQ==", "signatures": [{"sig": "MEQCIFryM5sztSJynU0JOTP3rQlOYX1mAoeEXHzYkdlOKyZ6AiBamSdJwlDVOIucn7urQXr8q3g8O8S89j5i2DWgf8iZxw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.0.5": {"name": "ajv", "version": "4.0.5", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^1.4.3", "chai": "^3.0.0", "glob": "^7.0.0", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.18.0", "eslint": "^2.5.1", "jshint": "^2.8.0", "nodent": "^2.3.13", "bluebird": "^3.1.5", "istanbul": "^0.4.2", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "typescript": "^1.8.10", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "regenerator": "0.8.42", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "c5fd98dd79a48054c943301a4a3f273b662d72ed", "tarball": "https://registry.npmjs.org/ajv/-/ajv-4.0.5.tgz", "integrity": "sha512-WMLiYNRoJYtH3yWfFJZnywU9fBCYDAM0FipYGKNdJCONQMxI9fRtvnHW8aSRNqldWljb/nvaqGWmfX+QSUDTUA==", "signatures": [{"sig": "MEUCIQCNmA265h1wBP0Vrg05NnBG03K8Qwqbvz12mwqGtuc9PAIgTC/hHaf7o+aEXVuMQ24uqqE9/Hg9RtvIDg4zjMAqiW0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.0.6": {"name": "ajv", "version": "4.0.6", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.18.0", "eslint": "^2.10.1", "jshint": "^2.8.0", "nodent": "^2.5.3", "bluebird": "^3.1.5", "istanbul": "^0.4.2", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "typescript": "^1.8.10", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "regenerator": "0.8.42", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "995c08692871bba6c6fa75b894fb2676bc9a842d", "tarball": "https://registry.npmjs.org/ajv/-/ajv-4.0.6.tgz", "integrity": "sha512-ODpQVPnQjvh4RqZj6KjLCokf7YWwIZRVliQvFjbit4djdisMplK7MVOzhqiUqMUHK+b+sx4ZqwhWXAlevw9v4A==", "signatures": [{"sig": "MEYCIQCpWv68iNIPQZGYxx0A55jSuvioamB+/sRkRckUGwjcZgIhANGoMLfy+X9Rm28BSQUSanwVeIhxG1ZK7Qr7JddjTOVv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.1.0": {"name": "ajv", "version": "4.1.0", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.18.0", "eslint": "^2.10.1", "jshint": "^2.8.0", "nodent": "^2.5.3", "bluebird": "^3.1.5", "istanbul": "^0.4.2", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "typescript": "^1.8.10", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "regenerator": "0.8.42", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "947fefd215466af4ca79b24edc39db7aa139f59f", "tarball": "https://registry.npmjs.org/ajv/-/ajv-4.1.0.tgz", "integrity": "sha512-UEtc7qVWi9wg8LsxQI8iG3uJjGN1bMBenACS1LTzDqdlscuXdpq260ctrVKIOPnVAm3ezI2nZW/Tz8PxPwAvYw==", "signatures": [{"sig": "MEYCIQDmQi+Fz0/DKhNOj5pCsNxj8sIIRquX9rMd/zTQIDhzSAIhANfLOA+xIvbgKCwgNVY45v9CzGnsgL4FJFlJK2qxFW7J", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.1.1": {"name": "ajv", "version": "4.1.1", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.18.0", "eslint": "^2.10.1", "jshint": "^2.8.0", "nodent": "^2.5.3", "bluebird": "^3.1.5", "istanbul": "^0.4.2", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "typescript": "^1.8.10", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "regenerator": "0.8.42", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "ce826fb62cf9fd10c85c4e04e6da354c49091964", "tarball": "https://registry.npmjs.org/ajv/-/ajv-4.1.1.tgz", "integrity": "sha512-oxe1tx4CqgcYoP3IbyHu/EPmqc1Atn7lpnCgsedFca+iaLaDH+tIDnMsU6meKnduV8wJwLIsAvEJsU39HbziIg==", "signatures": [{"sig": "MEUCIFoO6n7qup7R0BgFalT0OxRBOxgoVJMTl5yceEDDD6zgAiEApbAQiZMiGFdQpCseVNIRBotzvbBKUu37zd2QdsjJ45o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.1.2": {"name": "ajv", "version": "4.1.2", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.18.0", "eslint": "^2.10.1", "jshint": "^2.8.0", "nodent": "^2.5.3", "bluebird": "^3.1.5", "istanbul": "^0.4.2", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "typescript": "^1.8.10", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "regenerator": "0.8.42", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "12fc187d65fad2b848331533fca11c749d3c2128", "tarball": "https://registry.npmjs.org/ajv/-/ajv-4.1.2.tgz", "integrity": "sha512-RFUXdCdB0IR0OPW9bUvcBwdbAk93VZmGFDRixlLIh/JbqhqTHoiVbvYFuB97eIH4GhhqikOoBQbhJo4K5n86/Q==", "signatures": [{"sig": "MEUCIQDQ9uIhfX+Rct1FZ50ecD3aTThmMBozbQCRAuF2E8igBwIgQ/mwPA4CrgtUF5RBbDvq/6f/DhpUI8tVayCj07kKmRY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.1.3": {"name": "ajv", "version": "4.1.3", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^0.13.3", "mocha": "^2.2.5", "watch": "^0.18.0", "eslint": "^2.10.1", "jshint": "^2.8.0", "nodent": "^2.5.3", "bluebird": "^3.1.5", "istanbul": "^0.4.2", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "typescript": "^1.8.10", "js-beautify": "^1.5.6", "karma-mocha": "^0.2.0", "regenerator": "0.8.42", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "53c369f3a4bb467669b1c6c6d85e6c579e3d7973", "tarball": "https://registry.npmjs.org/ajv/-/ajv-4.1.3.tgz", "integrity": "sha512-yZWza5fsKRxv4pSd/+8bvI6iMhWv+9jxxkrO8stV1f7DbGbFauK+lhjGUtHwtd8oPhKOlk7EEG92hhRQyIAYEA==", "signatures": [{"sig": "MEQCIBJTTsXuF9mqF+IHBlX0pApo8mOSm9qJbTT/M0y2hxttAiBbcIkYP4MexpW8QzDwQ1wnNpMx2VoAP71U3hIzIO0kJQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.1.4": {"name": "ajv", "version": "4.1.4", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^2.5.0", "watch": "^0.19.1", "eslint": "^2.10.1", "jshint": "^2.8.0", "nodent": "^2.5.3", "bluebird": "^3.1.5", "istanbul": "^0.4.2", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "typescript": "^1.8.10", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.8.42", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^1.0.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "ba0a9230a9281cd5cc40f3b8e0fa3466ac085489", "tarball": "https://registry.npmjs.org/ajv/-/ajv-4.1.4.tgz", "integrity": "sha512-7R+NUMRXcOjYkq3VZqT/SQG/dtEhD5R/xFllwufh56nYWYldWejEUnneO0Q0ze7P6WPf20Y4C+g9K1Ekwa2KeA==", "signatures": [{"sig": "MEYCIQC1TR4YgkVyjA9mUZ6a4toIZAgBm4i6+9hX3BLbjDXJ3AIhAL55D6TvLe10My2ZdAHu7e2ajxnojXGQOcO6UKiPEjUE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.1.5": {"name": "ajv", "version": "4.1.5", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^2.5.0", "watch": "^0.19.1", "eslint": "^2.10.1", "jshint": "^2.8.0", "nodent": "^2.5.3", "bluebird": "^3.1.5", "istanbul": "^0.4.2", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "typescript": "^1.8.10", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.8.42", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^1.0.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "ea283a594f579b06467ce05daa5422d73b116b99", "tarball": "https://registry.npmjs.org/ajv/-/ajv-4.1.5.tgz", "integrity": "sha512-Y1fUvBQqZ56vZe1nK8fZCHEaBpBEH1IRZp/hnq32383UZXHr2HELnzFPfrmYEVJ/mojaCnC8Xi/MxcuHqxCKuQ==", "signatures": [{"sig": "MEQCIDhSVo7K5FtlpfBWz0wWEQYW5kbvWb37+/llXnaIeY5xAiBn9A0Jx1uKU36saTYEVnT3K7z/H7nkRtD0o2NJh8ct+Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.1.6": {"name": "ajv", "version": "4.1.6", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^2.5.0", "watch": "^0.19.1", "eslint": "^2.10.1", "jshint": "^2.8.0", "nodent": "^2.5.3", "bluebird": "^3.1.5", "istanbul": "^0.4.2", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "typescript": "^1.8.10", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.8.42", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^1.0.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "b0319c5bd8da7ffb3e85c696a1663aaceaa431ab", "tarball": "https://registry.npmjs.org/ajv/-/ajv-4.1.6.tgz", "integrity": "sha512-GqzEJEi4P4sz+AbwoT0mIhqtjeaZA5MKh8JtOvykkKpKyZ667FYbpBF7Uxh+8ZdlEeZdOrO41AzvW2k0dCTYaQ==", "signatures": [{"sig": "MEYCIQCKWxC1FhcmmmUAK7GbP3djCl4DLKI5UlBml2U7tw6enwIhAPiyPz6Esj7C6fDTTjwXhVbCSV1ccMAo5FeiN54vxGst", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.1.7": {"name": "ajv", "version": "4.1.7", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^2.5.0", "watch": "^0.19.1", "eslint": "^2.10.1", "jshint": "^2.8.0", "nodent": "^2.5.3", "bluebird": "^3.1.5", "istanbul": "^0.4.2", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "typescript": "^1.8.10", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.8.42", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^1.0.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "1b1e58cf7356ce813516c239ec92894924513a99", "tarball": "https://registry.npmjs.org/ajv/-/ajv-4.1.7.tgz", "integrity": "sha512-Hwzb700P98NpTTljTIN3R9vkJMs61zblzMZe7esC78bSEXifltJ6LtXEK+5kp/elG3RFz9xgwk7gVajeI8e3nA==", "signatures": [{"sig": "MEUCIQCEAkOay5w1vqsrwI3UyX3LQbFgw8IuhUzOFdUGFKvjwwIgbTxEfYKZ929wjTIFAsdHa5/p7iJX2ysEMePcRlGXmi8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.1.8": {"name": "ajv", "version": "4.1.8", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^2.5.0", "watch": "^0.19.1", "eslint": "^2.10.1", "jshint": "^2.8.0", "nodent": "^2.5.3", "bluebird": "^3.1.5", "istanbul": "^0.4.2", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "typescript": "^1.8.10", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.8.42", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^1.0.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "e9e2ff896794a3d5941545e4e325ce94876defbc", "tarball": "https://registry.npmjs.org/ajv/-/ajv-4.1.8.tgz", "integrity": "sha512-S0H0WhY7fbsWSFiKTUAa0bZ547v5WT+rP3/nD2P6OrgNDlp9zSzRVYFakAT32LDG5VCj8zGHuj4mL42gV7Qt4Q==", "signatures": [{"sig": "MEYCIQCm+r3xVvnWeA/C/qokK+eLoRcKse0vcG8iBG57hcX6ngIhAKgYsJfApzU+6Ajeblxvwkbi1X7pue+d/7ysXxwM8pyr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.2.0": {"name": "ajv", "version": "4.2.0", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^2.5.0", "watch": "^0.19.1", "eslint": "^3.1.1", "jshint": "^2.8.0", "nodent": "^2.5.3", "bluebird": "^3.1.5", "istanbul": "^0.4.2", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "typescript": "^1.8.10", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.8.42", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^1.0.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "5605296096b376f7f8f64e25234d163dbd634d17", "tarball": "https://registry.npmjs.org/ajv/-/ajv-4.2.0.tgz", "integrity": "sha512-hgQFLNEq4aUb9dK/AJlSXxkgzm16eTf5LIURqf18xI773U9MXnT6ChcURO+uxZBApHPyiQYkFhxePQduu9G4Og==", "signatures": [{"sig": "MEQCIA55o7PrS2R/EfskkWjEOI7cnb9VVYRfi9kPOi3WhjOBAiBV2LdAyEFJ2l10FEP5WM50APUPl+xhvEIwuwpILFFQiw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.3.0": {"name": "ajv", "version": "4.3.0", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^2.5.0", "watch": "^0.19.1", "eslint": "^3.1.1", "jshint": "^2.8.0", "nodent": "^2.5.3", "bluebird": "^3.1.5", "istanbul": "^0.4.2", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "typescript": "^1.8.10", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.8.42", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^1.0.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "bbd712c17c5a513af297457c58db8108e54ba10e", "tarball": "https://registry.npmjs.org/ajv/-/ajv-4.3.0.tgz", "integrity": "sha512-OGb4fBuIshUwrT1O9mIWH771QmN+1SK0jcHoDAZfTwr7wtjAJjNEuBQgOiVWDKmzzsnmrKP+Nbx4ojWEvcYI7A==", "signatures": [{"sig": "MEQCIAHIj0yQUVi7zixwMM72TZ7Blj4zIAtotlHTDdpisCFLAiBj0g6MEBsSTIou3IaH7+FJGKp7Dx80+4lkVsbXqXK44w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.3.1": {"name": "ajv", "version": "4.3.1", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^0.19.1", "eslint": "^3.2.2", "jshint": "^2.8.0", "nodent": "^2.5.3", "bluebird": "^3.1.5", "istanbul": "^0.4.2", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "typescript": "^1.8.10", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.8.42", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^1.0.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "027652caa6c99391adc511fc5872625024d9a43a", "tarball": "https://registry.npmjs.org/ajv/-/ajv-4.3.1.tgz", "integrity": "sha512-QaI135j81KQt3T4Jn9IqhU+D6i6LvqEXf5hI72/f8Cr7Tx4GP1upGiR/I4iRhJ+7pdKFAgHIxisQ08TpL2xMfQ==", "signatures": [{"sig": "MEQCIHXqtzcwSUddbcUtBGDIHc+L+GD8W26LPm8j1GH9LH5KAiAT3eQ3EbPum0y1O671XtuyE4oWIISf/MqXtZ+Ag4a/wg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.4.0": {"name": "ajv", "version": "4.4.0", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^0.19.1", "eslint": "^3.2.2", "jshint": "^2.8.0", "nodent": "^2.5.3", "bluebird": "^3.1.5", "istanbul": "^0.4.2", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "typescript": "^1.8.10", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.8.42", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^1.0.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "2cfc3f095210946b90cc2625173d11a7efe4ab8b", "tarball": "https://registry.npmjs.org/ajv/-/ajv-4.4.0.tgz", "integrity": "sha512-Tsc6wsVlzKfDTwi/JhbbHuVZSlpEPVum4AuIoQFEw5jQkL0p7PJ9lLX7tmU2j1sgpay8z512+5lSmUk2fZsiJg==", "signatures": [{"sig": "MEUCICw+HBCYOdBwmKlq/SrEgUDcAfO920iNkqNsGkfZXVfdAiEAowBS9z5MyW3eZGoU7Lh3ner75GkKWq0a7cfZ+LOQyJ4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.4.1": {"name": "ajv", "version": "4.4.1", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^0.19.1", "eslint": "^3.2.2", "jshint": "^2.8.0", "nodent": "^2.5.3", "bluebird": "^3.1.5", "istanbul": "^0.4.2", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "typescript": "^1.8.10", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.8.42", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^1.0.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "92d0d9e291d6e453ef5e9055a4371c736e8109e2", "tarball": "https://registry.npmjs.org/ajv/-/ajv-4.4.1.tgz", "integrity": "sha512-i73wxn7XvYdrZrcs0tV3DYUclWrthm/ySUXgGEgVakvf0KEY4b5C3ODCQ0EtvByrSBdLG/g0MEsJjDAKBJC8gQ==", "signatures": [{"sig": "MEUCIQD42dVvomrC7NLkWgF3nDDLA2PppZRT1HquNXVEvR5E3AIgam6oXCJU5TimM7+VcTH4MDadfncEkeWe/nsbw6Amfi4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.5.0": {"name": "ajv", "version": "4.5.0", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^0.19.1", "eslint": "^3.2.2", "jshint": "^2.8.0", "nodent": "^2.5.3", "bluebird": "^3.1.5", "istanbul": "^0.4.2", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "typescript": "^1.8.10", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.8.42", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^1.0.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "536cc01d1e7e0f0fe3278b405b35ac3a2417ecef", "tarball": "https://registry.npmjs.org/ajv/-/ajv-4.5.0.tgz", "integrity": "sha512-W42zj4Y2sRzoeItJv3iPM9EOfnMz3hBRDb+Okt98gISjhyF2TxgWo+dspQiq9r5AUICtcYP7S7gCN6NHBRcXLQ==", "signatures": [{"sig": "MEQCIEZ7c+7OJFQi3nDXfiHEQVCqwX1UG/FQHLUVaFPoViOxAiBpiQBn3QGxl0TQaBFlFHg59UrVoEbDS+c5kj+Oa4YhGQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.6.0": {"name": "ajv", "version": "4.6.0", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^0.19.1", "eslint": "^3.2.2", "jshint": "^2.8.0", "nodent": "^2.5.3", "bluebird": "^3.1.5", "istanbul": "^0.4.2", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "typescript": "^1.8.10", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.8.42", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "71a150742689ab6dcfb9c78cb92b35d0d098d7a8", "tarball": "https://registry.npmjs.org/ajv/-/ajv-4.6.0.tgz", "integrity": "sha512-lVnUuMafXVIFojUH/eiaZjeEvS6mLmdDFnZeHzaMeuEmbX5f/ri/TFLgNyphf/K36OvZtFRYBgUCC+wa6a4C/g==", "signatures": [{"sig": "MEYCIQCAmc6dWirTbeRLuA2IlmYgqITEj4RwmZY70LxEZ20CrwIhAPz3Z0HszFGud4u2gJTTSXtwvZOdMMHVCIStaVGx9pHX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.6.1": {"name": "ajv", "version": "4.6.1", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^0.19.1", "eslint": "^3.2.2", "jshint": "^2.8.0", "nodent": "^2.5.3", "bluebird": "^3.1.5", "istanbul": "^0.4.2", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "typescript": "^1.8.10", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.8.42", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "4af22bd954e88f483bf5c491ba47215584436699", "tarball": "https://registry.npmjs.org/ajv/-/ajv-4.6.1.tgz", "integrity": "sha512-AFd7jZEqVlPq3vxOjGmkCzjE8/egrpuKwoTZxzO3c3BpOHIKmrHHzL0mfdUlFqsh4gjZIliS5dL5NogUhe01Uw==", "signatures": [{"sig": "MEUCIHqr5I4p2IvEPMCMH/60L6AhBeo4uI+GRaCa/1ugG1iFAiEAyOItvGEEtHF+MhtTUfHUXbyI51vu6esNKuaTHNtIeuY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.7.0": {"name": "ajv", "version": "4.7.0", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^0.19.1", "eslint": "^3.2.2", "jshint": "^2.8.0", "nodent": "^2.5.3", "bluebird": "^3.1.5", "istanbul": "^0.4.2", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "typescript": "^1.8.10", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.8.42", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "f7e9e19740549c0f9762a56c3bc887e7cd3b735a", "tarball": "https://registry.npmjs.org/ajv/-/ajv-4.7.0.tgz", "integrity": "sha512-QdE6KRcbF73vKByPs1ODSyWgkfjkTZTIxYKo54pB6Od+RsuhBFpcWB46/P4QHciTpTDyn1K/S7ZHIc/w4XGx2Q==", "signatures": [{"sig": "MEUCIHu+LZYiG/o1KTkFDiKpLy/mue4RNxT7rfUrk+I8Hk47AiEA7kUhqFj8nD6laAulTtyxUL9esOjbD8W4Dt8Wlm8dIas=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.7.1": {"name": "ajv", "version": "4.7.1", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^0.19.1", "eslint": "^3.2.2", "jshint": "^2.8.0", "nodent": "^2.5.3", "bluebird": "^3.1.5", "istanbul": "^0.4.2", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "typescript": "^1.8.10", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.8.42", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "7bea0df4b98c6cce5f12b65496f33fa4489b7513", "tarball": "https://registry.npmjs.org/ajv/-/ajv-4.7.1.tgz", "integrity": "sha512-06dPKupP657eSxfcuUI3RZTh/DT/QttqEWUpzfZekUlo8cat/O1v16VknwDqyepnz1heaedgfUxsfQp5X0o4xA==", "signatures": [{"sig": "MEQCIDzA9N6NLS7uYuizg2IJ9G/9R/xTjKu5MO7lvFqw/DPsAiB8t9akYaczAa1RH3/cHqXJ2b6K4cbnBxO7UKP5rOvUEg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.7.2": {"name": "ajv", "version": "4.7.2", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^0.19.1", "eslint": "^3.2.2", "jshint": "^2.8.0", "nodent": "^2.5.3", "bluebird": "^3.1.5", "istanbul": "^0.4.2", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "typescript": "^1.8.10", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.8.42", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "ad1a8a461ac96df1b1dee030a18ebd72225b1b22", "tarball": "https://registry.npmjs.org/ajv/-/ajv-4.7.2.tgz", "integrity": "sha512-7Aax/5M8ZkW/85YK3Js2afILq5D58iHBCnocsOEbmAzmfUfPGrqh576QVb0o8Q5UAi1XuCUoqK0zhsa8EdIfRg==", "signatures": [{"sig": "MEUCIQCmKvnPBcG07Rpqnd40BJfbaIrTRV0tWbiE2Rx/hOYr8gIgWVmfekPLFSaYupsH+9CwlS/dLETBvXQsmbhfYMzxuNc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.7.3": {"name": "ajv", "version": "4.7.3", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^0.19.1", "eslint": "^3.2.2", "jshint": "^2.8.0", "nodent": "^2.5.3", "bluebird": "^3.1.5", "istanbul": "^0.4.2", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.8.42", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "cc16021677f17d6f222b94b29899d99b270f82c5", "tarball": "https://registry.npmjs.org/ajv/-/ajv-4.7.3.tgz", "integrity": "sha512-wAq612d45b48PJlq6D8MR4yBGeWe6tciF7B+SvCq+2/uKUatKi0c5E6QbsUzuVfvtXWqlLwif2kAD2X8MmDVKw==", "signatures": [{"sig": "MEUCIBLSrQvy5FUhtfzK63DCDbZfgEzEQUixz94+UeZk7DspAiEA6frQjBKyvr24/EGPsRGKDipSmttHqHhFGwlbU3HFgxg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.7.4": {"name": "ajv", "version": "4.7.4", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^0.19.1", "eslint": "^3.2.2", "jshint": "^2.8.0", "nodent": "^2.5.3", "ajv-pack": "^0.1.0", "bluebird": "^3.1.5", "istanbul": "^0.4.2", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.8.42", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "a1cbebe691f5b2abe3338ca2c4db7beec5cfb5e1", "tarball": "https://registry.npmjs.org/ajv/-/ajv-4.7.4.tgz", "integrity": "sha512-yETkyr3dpsCxbTdQsGf43qrouiMfM+l+Qb7FxdleGz+7p9B8N506Zo3q0TVmAWt3egBk9lAavYT3c27rvf7BTQ==", "signatures": [{"sig": "MEQCIBmly5xm/D6UWDaBBhMp8RNZWk99i+cT+ZwcVBJUJcWqAiBQ5rlIO/WyOv4nIOUZw7TukyOoYIa2zr8dWUZWJvbpuA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.7.5": {"name": "ajv", "version": "4.7.5", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^0.19.1", "eslint": "^3.2.2", "jshint": "^2.8.0", "nodent": "^2.5.3", "bluebird": "^3.1.5", "istanbul": "^0.4.2", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.8.42", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "f44172aec18514e6ba6350cc5fae0ee9b142e68c", "tarball": "https://registry.npmjs.org/ajv/-/ajv-4.7.5.tgz", "integrity": "sha512-WEjAzXZbjfWkhpuWE78pEwaDOML+JQvzxalzewzEkf8A4Sw6KjPUrAotKyC8ck0+gDEWzxyr55ZNvew55oSFCg==", "signatures": [{"sig": "MEYCIQDGpvqFSErf4/nBWLiF4nQ6ub11ZTdpxoupNNtY9cvYhAIhALsXBrtLuC574LMgdcIyJNZwovJl7RE9vrJbmaUHuiaw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.7.6": {"name": "ajv", "version": "4.7.6", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^0.19.1", "eslint": "^3.2.2", "jshint": "^2.8.0", "nodent": "^2.5.3", "bluebird": "^3.1.5", "istanbul": "^0.4.2", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.8.42", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "a5c1da3f901ab7943e874a6c7820510f375aa996", "tarball": "https://registry.npmjs.org/ajv/-/ajv-4.7.6.tgz", "integrity": "sha512-YtDrTy5roHsF8iFzQsY9383p9rAwBz5r4SRRDnhNqwlej69DcWlev3bTQiLaeo3ICD3p271W2o2Ufm54IQN5mw==", "signatures": [{"sig": "MEQCIDXcnNJN3eP43Dabcu7l+tWl/R9DSJz3YX/OhqIBL81jAiBymzVnaj7Mu+hRpDNqBrX+qpJ0akLJnh2BgroVwJBccg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.7.7": {"name": "ajv", "version": "4.7.7", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^8.3.0", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^0.19.1", "eslint": "^3.2.2", "jshint": "^2.8.0", "nodent": "^2.5.3", "bluebird": "^3.1.5", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.8.42", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "4980d5f65ce90a2579532eec66429f320dea0321", "tarball": "https://registry.npmjs.org/ajv/-/ajv-4.7.7.tgz", "integrity": "sha512-MHYBmwmlJ4Yi8+KEvRrvdVz7pKlFfPccs/kFTkygD9KOjqXQtKah0S2ZCJo3OF3YrVNrFLgqQ4MBQ7OmEFo1og==", "signatures": [{"sig": "MEQCIHs7F68uB+OEM8m7iDXZdUw3wfRUGkcNVQ7K1350DsChAiASWaJlmGf+g6ecF/NnmYWgLHAiTJjuGbM4PuFUwIjKhQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.8.0": {"name": "ajv", "version": "4.8.0", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^8.3.0", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^1.0.0", "eslint": "^3.2.2", "jshint": "^2.8.0", "nodent": "^3.0.2", "bluebird": "^3.1.5", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.8.42", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "011df5c4a08edb29574a477269afb15a6f97abe5", "tarball": "https://registry.npmjs.org/ajv/-/ajv-4.8.0.tgz", "integrity": "sha512-+XK3Lh2Lt0MOSOnHoEffC4GLg8mDX/VdwxA31uPrOWRgg1ad2aHNRA56GBRk0F2hEMmhkbpfSi3IPTQSd8Kq2Q==", "signatures": [{"sig": "MEUCIQDbt+CA0fwrSCplQJs0yWoOrQI/gH5c3tcGpMMAD3QkrwIgRxe2ZJMa8TbA30lXXGghk/zKIhr9YlkYAnViVZ29I6A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.8.1": {"name": "ajv", "version": "4.8.1", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^8.3.0", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^1.0.0", "eslint": "^3.2.2", "jshint": "^2.8.0", "nodent": "^3.0.2", "bluebird": "^3.1.5", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.8.42", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "708213577289a84a8148e66daa980c32da6a562e", "tarball": "https://registry.npmjs.org/ajv/-/ajv-4.8.1.tgz", "integrity": "sha512-Do74Mch6PoBCYtjM7WIia7C3PD7ts/akFSRYHplaTalgfWKTNUK1Q6Z1q2XP2l3dTv9GjXMsRDeyDhF7QxQnmQ==", "signatures": [{"sig": "MEUCIDQMeDWHShdx8sv3F94+ZCg94a2/rpUI5nSmrH1iCtTcAiEA6TYCUpERcy2gVFjDElixv8I83lOP7vbzBek/rLl/73k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.8.2": {"name": "ajv", "version": "4.8.2", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^8.3.0", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^1.0.0", "eslint": "^3.2.2", "jshint": "^2.8.0", "nodent": "^3.0.2", "bluebird": "^3.1.5", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.8.42", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "65486936ca36fea39a1504332a78bebd5d447bdc", "tarball": "https://registry.npmjs.org/ajv/-/ajv-4.8.2.tgz", "integrity": "sha512-wyMuXI8/9giNjPjht43OJFI39DVJzP4qwCPTgmQxqHZB2VLXF7Fc8btRweX+3+kU3mYmRQNghTr42Nf86sRSQQ==", "signatures": [{"sig": "MEQCIFQI3tn7wtKDJDObGyUtLSvBu7st/6TabEVgHYdYJ8jDAiALcxyqLn08dzSCpLGPn9qxxDaWSxDAqAaOuLyUk4W6OQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.0.0-beta.0": {"name": "ajv", "version": "5.0.0-beta.0", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^8.3.0", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^1.0.0", "eslint": "^3.2.2", "nodent": "^3.0.2", "bluebird": "^3.1.5", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.8.42", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "22563d59b329eda26fe668cdbd7ba858e3e40016", "tarball": "https://registry.npmjs.org/ajv/-/ajv-5.0.0-beta.0.tgz", "integrity": "sha512-igWqAb+ByBG+9uiN0Zp2m37gH4OC4p6BiM5HT6YR2vWIA3qO7+5IzE3XyZKBszE2MawYngQSXvwB51FRw6uArg==", "signatures": [{"sig": "MEQCICK7AFEhdoVajrdOu4zfh9pNh/g1t5b7wKt/q5YHRMy7AiAhJ/Mt3RkUiCqyl30aUmqKmTOLT79eGmzeAxE2Us/uEw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.9.0": {"name": "ajv", "version": "4.9.0", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^8.3.0", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^1.0.0", "eslint": "^3.2.2", "jshint": "^2.8.0", "nodent": "^3.0.2", "bluebird": "^3.1.5", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.8.42", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "5a358085747b134eb567d6d15e015f1d7802f45c", "tarball": "https://registry.npmjs.org/ajv/-/ajv-4.9.0.tgz", "integrity": "sha512-O0z4y7Q2TThSwNZMegvZwgnDa9SYhWUbuQy6ryLhoISaeAqISvD20xoAjzZPgMMRe3LglAqnkTtzJEZQ+akzFw==", "signatures": [{"sig": "MEUCID0925T2PCIFPV3tqhNYhjZnbZZi/0B6ih3+GMSksKgJAiEA9HGmdyWHGht5UgCP4sKD/BbMIqQTqgjzTDw43pwcMMo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.0.0-beta.1": {"name": "ajv", "version": "5.0.0-beta.1", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^8.3.0", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^1.0.0", "eslint": "^3.2.2", "nodent": "^3.0.2", "bluebird": "^3.1.5", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.8.42", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "ca45fe972aa0b86ffb5af555961594e0d4ae05e8", "tarball": "https://registry.npmjs.org/ajv/-/ajv-5.0.0-beta.1.tgz", "integrity": "sha512-ZNVkmibEngHJblBeQ1srNB34TTRzA0D/BpIdUjN4EhFyq2GKTWlgUpkVw3w1+D0L0e0ZGfvbl995EpTLEIFeqA==", "signatures": [{"sig": "MEYCIQDXdKcyL3kMT3b8VW8hqfudd6v2WxYmBVskY/whAPbySAIhAM4eK/2EHTVpHmizF58gqj8J9zsWtrZp3Y5wXa56gJyA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.9.1": {"name": "ajv", "version": "4.9.1", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^10.0.0", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^1.0.0", "eslint": "^3.2.2", "jshint": "^2.8.0", "nodent": "^3.0.2", "bluebird": "^3.1.5", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.9.5", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "08e1b0a5fddc8b844d28ca7b03510e78812ee3a0", "tarball": "https://registry.npmjs.org/ajv/-/ajv-4.9.1.tgz", "integrity": "sha512-TrRbj4hdDrFJzBaYFhZ0gjRdPfTwcSttCjsYSaKuknWT04nJp99Y+PcKpX4dL8m8LuC1BEUtDz2r4nVjVFCctw==", "signatures": [{"sig": "MEYCIQCjDAM726Yo4qNN1GhuXh9XO3VOcq1K8Ld/7KGBywAEigIhAJUwsWpesKH8hicpAIZaI4wh3VkeY0QQk1o+dgbkk+Em", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.9.2": {"name": "ajv", "version": "4.9.2", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^10.0.0", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^1.0.0", "eslint": "^3.2.2", "jshint": "^2.8.0", "nodent": "^3.0.2", "bluebird": "^3.1.5", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.9.5", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "3f7dcda95b0c34bceb2d69945117d146219f1a2c", "tarball": "https://registry.npmjs.org/ajv/-/ajv-4.9.2.tgz", "integrity": "sha512-gXtK+jN8qN2Z4fKFyIFPQPn495RI/PxLWI4OcN6cc5W5NEOkMaRlYKyMn+frErxNf11jrL7u//2sLYJ8aqwVWw==", "signatures": [{"sig": "MEUCIQDYKf2vCL3J9rOflB6Xr5aiqcNTt2MW/RkmJvFFgGe8wQIgdBv7J1YHzGfVw3BX8dt+9DuufdHcqOu2pOD6jYsb0iQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.9.3": {"name": "ajv", "version": "4.9.3", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^10.0.0", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^1.0.0", "eslint": "^3.2.2", "jshint": "^2.8.0", "nodent": "^3.0.2", "bluebird": "^3.1.5", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.9.5", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "ed9953a96d5584ce180f25757cd23504daff59ca", "tarball": "https://registry.npmjs.org/ajv/-/ajv-4.9.3.tgz", "integrity": "sha512-t7ewGUZ8+w9g6d9ZleeUW80+gihcMrM4Ym8KFZ9sUdLsUJvFB3e+cduFhCuCTur/Y4xpi8U5KSQ1zy5fqi4cBw==", "signatures": [{"sig": "MEUCIEiOLXGAuR8jIBlOTtpx2zQguzOCP7QIBXJlaXeTYEmZAiEA2fLkLPRG27yFZmxGPxnpdYTFPKHN3nZ/iuYresp2ZS8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.10.0": {"name": "ajv", "version": "4.10.0", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^10.0.0", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^1.0.0", "eslint": "^3.2.2", "jshint": "^2.8.0", "nodent": "^3.0.2", "bluebird": "^3.1.5", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.9.5", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "7ae6169180eb199192a8b9a19fd0f47fc9ac8764", "tarball": "https://registry.npmjs.org/ajv/-/ajv-4.10.0.tgz", "integrity": "sha512-MyMSPSi5pQZZjTKLSJMs2Ww7HnQ6usesH8x2Z9KtQ+2dw0f5tnTdtfAmiAP+BxPfer8R3H1m91+51sIB9j7dtQ==", "signatures": [{"sig": "MEUCIQCHYcLvVfbYo1TH294rC2/NCRwEIj4oaGhWN5LZB2d+QgIgLZoYoQREQ4rXWcgVUBiima3NQ4E0ZtmPbwLYryseZlA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.10.1": {"name": "ajv", "version": "4.10.1", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^10.0.0", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^1.0.0", "eslint": "^3.2.2", "jshint": "^2.8.0", "nodent": "^3.0.2", "bluebird": "^3.1.5", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.9.5", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "6e1669b62d752424a73da9175901d0327adfc2a5", "tarball": "https://registry.npmjs.org/ajv/-/ajv-4.10.1.tgz", "integrity": "sha512-LCrPD5PSMdb5QJeSmP4tVXel5oEYfxfzfgVXLQCOjF//SS3g7PNROXhC4KOz3JrXvGd+8u+k90jGRluiQ4a2Mw==", "signatures": [{"sig": "MEYCIQCfY41cgp8mhe7ijKHm0kg6gsxC9e4MqxRMcQmWm//qhAIhANX246TJXkXr0VYst7HfGvP7rapRqrUmiYTn/8hK2IIi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.10.2": {"name": "ajv", "version": "4.10.2", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^10.0.0", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^1.0.0", "eslint": "^3.2.2", "jshint": "^2.8.0", "nodent": "^3.0.2", "bluebird": "^3.1.5", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.9.5", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "27a61437962cb6daf8023ca9ad2a30337d918dda", "tarball": "https://registry.npmjs.org/ajv/-/ajv-4.10.2.tgz", "integrity": "sha512-rSvNEnWpgw7ArP2FdV4kbSK7t6vckFdaIVtOrKF7SshiIBjDJ+bxkKF6V6LIwcvxWti5P0yMTGzt3WkCPMCwwA==", "signatures": [{"sig": "MEUCIA7zbm/5ILRQ2exl41OHzZWZjORsGaoOqR7apLfPI2tSAiEA6+BXpX46xpksKDSj2Iydl1gVDjyhJR1LM2UCM51WopY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.10.3": {"name": "ajv", "version": "4.10.3", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^10.0.0", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^1.0.0", "eslint": "^3.2.2", "jshint": "^2.8.0", "nodent": "^3.0.2", "bluebird": "^3.1.5", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.9.5", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "3e4fea9675b157de7888b80dd0ed735b83f28e11", "tarball": "https://registry.npmjs.org/ajv/-/ajv-4.10.3.tgz", "integrity": "sha512-ASRaFnLojvzA75SFS9Alex/S/V2gX+1xvmE5ZyUwaS6CQtd6nx6xdEAbvhRsCRxeGE4L5z1und47fov3iIZAIQ==", "signatures": [{"sig": "MEYCIQDND1l69H+PlhmE142UdeXlJxieyzLcb1Rbwkt8RNei+QIhALISqjRQzhGucKNGMTPgYGSoOOGG77tFnFkaZpYgsAKl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.0.1-beta.0": {"name": "ajv", "version": "5.0.1-beta.0", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^10.0.0", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^1.0.0", "eslint": "^3.2.2", "nodent": "^3.0.2", "bluebird": "^3.1.5", "ajv-async": "^0.1.0", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.9.5", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.3.0", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "93dda18a3ed7fa3d29c8ca27e4398484e0929f19", "tarball": "https://registry.npmjs.org/ajv/-/ajv-5.0.1-beta.0.tgz", "integrity": "sha512-pyxQIPQB8ZLAvQkY7OzLW/0/q4X57T5SNF7XJyjY+vwUagoahx2rmDJB3YdUyNkmUVwxp0QU2vK84K5qvAPJGQ==", "signatures": [{"sig": "MEUCICO1xBiSqmho6E1uiV4q4g1UzTjXPOUQ1YlKyAGJQGBbAiEAvaSIarr3i706azDgYb2HvoxNSH+esp4e1F8B68wrl5U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.10.4": {"name": "ajv", "version": "4.10.4", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^10.0.0", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^1.0.0", "eslint": "^3.2.2", "jshint": "^2.8.0", "nodent": "^3.0.2", "bluebird": "^3.1.5", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.9.5", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "c0974dd00b3464984892d6010aa9c2c945933254", "tarball": "https://registry.npmjs.org/ajv/-/ajv-4.10.4.tgz", "integrity": "sha512-cxctC1bFbzySg37ywWib1huYFFe341i36SRp16FimGzkblS4mjkO4hK+cbOFRglBNaFYiiEzPxjfOIxxa6yrbw==", "signatures": [{"sig": "MEUCID73jN6hFxWxJvoJ2mUbkBks7CV+5+nT8ZgF6h4zuFe3AiEAySJKLwecS6s+frJFJnuHE3a2a101Ykn5S7GSXMx/hsA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.0.1-beta.1": {"name": "ajv", "version": "5.0.1-beta.1", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^10.0.0", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^1.0.0", "eslint": "^3.2.2", "nodent": "^3.0.2", "bluebird": "^3.1.5", "ajv-async": "^0.1.0", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.9.5", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.3.0", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "1833fdf34b88642c737ab3ce2d75367897afbbde", "tarball": "https://registry.npmjs.org/ajv/-/ajv-5.0.1-beta.1.tgz", "integrity": "sha512-LzFyxO5wmNIh4fZDtPGm+HKlU8+QF+vNiyZ67kuKYvPcUUJtfMYtSVD6zn4KF4EDOpT0GV+cvkur1ZxYFXRUQQ==", "signatures": [{"sig": "MEUCIBsPwUHlf44+K7csGCjjePYGPGkTrBcLrXdo9v5du91TAiEAnhqdkuxSf84rVVBfHMqebau8QcZrDcfkrdnWCWnRh3M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.11.0": {"name": "ajv", "version": "4.11.0", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^10.0.0", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^1.0.0", "eslint": "^3.2.2", "jshint": "^2.8.0", "nodent": "^3.0.2", "bluebird": "^3.1.5", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.9.5", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "bcea8caf88a79be08a7fa1061f8c57a3db393fb7", "tarball": "https://registry.npmjs.org/ajv/-/ajv-4.11.0.tgz", "integrity": "sha512-dmrCiOfQgmR2+nf1cGZeZXMNkUjnBv9k+rD0Xi+TVaM2wA9WP8/xBJ6v5DKciwHK+hbA0uATqHxnXNIxTBxxDw==", "signatures": [{"sig": "MEQCID3AZkcKJOiAg6Qm6/6t0g1ZCEZuoRGw+v3+wI+0L0ZDAiAAjgP1jD/f0ugbdV+QRtTkt5ttHCHjwhIdkot8MGFr7g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.11.1": {"name": "ajv", "version": "4.11.1", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^10.0.0", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^1.0.0", "eslint": "^3.2.2", "jshint": "^2.8.0", "nodent": "^3.0.2", "bluebird": "^3.1.5", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.9.5", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "a903487faabf8608aa22871032ca447440afe494", "tarball": "https://registry.npmjs.org/ajv/-/ajv-4.11.1.tgz", "integrity": "sha512-vsCfUD9l+io7TLrCbhOWxpccQqfP1Hw2xVkOJ0ztBhd6JYD/Pn4sC06gFraFI7RK9vsuBfPLiFJWqeX08BS/cQ==", "signatures": [{"sig": "MEUCIES2/fE6D0ZA3fTbuc3reSQkBZbccT/kdi3674JlXPKCAiEAq2oyddObJuLoJCmPEauFd6A3XXY4A2toNP+jO6XiOuk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.11.2": {"name": "ajv", "version": "4.11.2", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^10.0.0", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^1.0.0", "eslint": "^3.2.2", "jshint": "^2.8.0", "nodent": "^3.0.2", "bluebird": "^3.1.5", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.9.5", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "f166c3c11cbc6cb9dcc102a5bcfe5b72c95287e6", "tarball": "https://registry.npmjs.org/ajv/-/ajv-4.11.2.tgz", "integrity": "sha512-KpqvHT4/zBEVy1YzyYEI++gQ9LxKtaI6FTspIwEgP8Ht+80wwhiFpM5TB4wv1CEpY4Ab+AijaFebzZ9bE8tTYA==", "signatures": [{"sig": "MEYCIQDohnBYnlOQAqQulhxe+odXaDIyE9LQxhvjy92tYAisewIhANdDJD0QYaOOpQ+Ltv8Scm9f4c1MonKNIm42eXsJmg1Y", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.0.1-beta.2": {"name": "ajv", "version": "5.0.1-beta.2", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^10.0.0", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^1.0.0", "eslint": "^3.2.2", "nodent": "^3.0.2", "bluebird": "^3.1.5", "ajv-async": "^0.1.0", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.9.5", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.3.0", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "7fca7683b67a159e4583b54ea8392060f10105de", "tarball": "https://registry.npmjs.org/ajv/-/ajv-5.0.1-beta.2.tgz", "integrity": "sha512-07SOKRKh8QD4kwAZ+0a0eER7kscg/8tR0+OBgoiDRjOv5gYuERFNVgRwTjXr3ilcJHim1datua7vR2bLfQ/7hw==", "signatures": [{"sig": "MEYCIQDvFGSPYIqYW08B1BRBrsotkVGsszB3XcRlagq0VRQKdAIhAP6vM9hZFlW3InL9kdV/GuH9Qi6KISKCM5aYinZqMbUG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.0.1-beta.3": {"name": "ajv", "version": "5.0.1-beta.3", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^10.0.0", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^1.0.0", "eslint": "^3.2.2", "nodent": "^3.0.2", "bluebird": "^3.1.5", "ajv-async": "^0.1.0", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.9.5", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.3.0", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "4eac3b28af93f001c9f3ea944df2386f12fa1fef", "tarball": "https://registry.npmjs.org/ajv/-/ajv-5.0.1-beta.3.tgz", "integrity": "sha512-eLTnM80kF6ehYWI/fiXE7wxjirZqyJ9avRRKkYcZTvuwj7/EU+1SXlfwPICtVU+nb5nhu0yyzLh/e0qJclrFmw==", "signatures": [{"sig": "MEUCIAflmo9xXhAO3Cpl14gTCZ8xjVVmbBvIjuj+UrT6SJ4+AiEA2HXuKBgLfvpPvjPNVn+nu+TIHfGmTuScjacY5ptGpLU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.11.3": {"name": "ajv", "version": "4.11.3", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^10.0.0", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^1.0.0", "eslint": "^3.2.2", "jshint": "^2.8.0", "nodent": "^3.0.2", "bluebird": "^3.1.5", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.9.5", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "ce30bdb90d1254f762c75af915fb3a63e7183d22", "tarball": "https://registry.npmjs.org/ajv/-/ajv-4.11.3.tgz", "integrity": "sha512-6HPnEv7e2ruV3hugsg10xFwVWY4ojMZqfy+ZaOvVXNzWQJSdtZGmBDypY4Ky5RE1Rz48l/Hgpy7nXCNC1Sr18A==", "signatures": [{"sig": "MEUCIQDoloZJ2Mnr+sI0jNElSFgqmliUYOrLfWCltVzCDoG7xAIgTqVTfJkYUcCrlqfXTyaoRn4X4m8wl6ub6Brm4Z8/xAs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.0.2-beta.0": {"name": "ajv", "version": "5.0.2-beta.0", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^10.0.0", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^1.0.0", "eslint": "^3.2.2", "nodent": "^3.0.2", "bluebird": "^3.1.5", "ajv-async": "^0.1.0", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^13.0.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.9.5", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.3.0", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "0d760d028bb655b2680486bc9f4c53b584fcee97", "tarball": "https://registry.npmjs.org/ajv/-/ajv-5.0.2-beta.0.tgz", "integrity": "sha512-RzsN8s1U5ypkhsFt2nGVGcXsdGuGc8eGd+SARNFpSDGGgvy1FNUQMvokAz3TLSeHwNAfGa7b82vRgIdqlBNVPw==", "signatures": [{"sig": "MEUCIGQS681W5KC8cT/uLvbdD71Soa8esS+okCDFzcK7q8O/AiEAq3cszJT7QV4vPAwOe05NJ/LVSomPL9fAPTCcn4AWlPI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.0.3-beta.0": {"name": "ajv", "version": "5.0.3-beta.0", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^10.0.0", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^1.0.0", "eslint": "^3.2.2", "nodent": "^3.0.2", "bluebird": "^3.1.5", "ajv-async": "^0.1.0", "coveralls": "^2.11.4", "uglify-js": "2.6.1", "browserify": "^14.1.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.9.5", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.3.0", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "4ee44b57ab3bb43bc00fe727af9b350c9d9712fa", "tarball": "https://registry.npmjs.org/ajv/-/ajv-5.0.3-beta.0.tgz", "integrity": "sha512-r4+l7XT+miKZ2kVJEaFMmO7Ni6SOjW8Q4bbUdwL/CKL2d8itIH939WGtmaZS0gZl8HLIWnwJRc6/44RaSczo/g==", "signatures": [{"sig": "MEYCIQC/Y0PdyV9QZKziIpl6wCypFHEqOk8DRE/cXvbw0pN0bAIhANr4Acwjx3L9+O5RRtCEDorY5FhFyPn9+O4FpUwgGfa+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.11.4": {"name": "ajv", "version": "4.11.4", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^10.0.0", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^1.0.0", "eslint": "^3.2.2", "jshint": "^2.8.0", "nodent": "^3.0.2", "bluebird": "^3.1.5", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^14.1.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.9.5", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "ebf3a55d4b132ea60ff5847ae85d2ef069960b45", "tarball": "https://registry.npmjs.org/ajv/-/ajv-4.11.4.tgz", "integrity": "sha512-<PERSON>zt28drB6Hep1bItI0nLHzPHMbqvfICC0meANNMX+0I3i5s6zDGZu9cyUIA2CoQd3jLAtPhDZD6ezLDhSUbOg==", "signatures": [{"sig": "MEUCIQD4spS2T4VAJscuVW4/GjaOy71ftyyY8CbrsHeCSA/Z0QIgJG8OEU6UVxNUKa/tTUNMp1NnZzYqZcis5XUbgLBjnYs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.11.5": {"name": "ajv", "version": "4.11.5", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^10.0.0", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^1.0.0", "eslint": "^3.2.2", "jshint": "^2.8.0", "nodent": "^3.0.2", "del-cli": "^0.2.1", "bluebird": "^3.1.5", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^14.1.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.9.5", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "b6ee74657b993a01dce44b7944d56f485828d5bd", "tarball": "https://registry.npmjs.org/ajv/-/ajv-4.11.5.tgz", "integrity": "sha512-3fmOjaKrxgFuUjMyDV0GUcIm/8VovYtOWcUGF27HRcMr3Nz9koujegRDXf67/DniuzliiwZUEqe5WIbvW27Qiw==", "signatures": [{"sig": "MEQCIFZz+qGjrcofMUe0s4vWD5lkiZ+0mH/7jUNyt/lGQFYYAiBFj5OtwgBkJ3waW30iCdr22tEZPMStAdVIM2Us7l0Oxg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.0.4-beta.0": {"name": "ajv", "version": "5.0.4-beta.0", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^10.0.0", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^1.0.0", "eslint": "^3.2.2", "nodent": "^3.0.2", "del-cli": "^0.2.1", "bluebird": "^3.1.5", "ajv-async": "^0.1.0", "coveralls": "^2.11.4", "uglify-js": "2.6.1", "browserify": "^14.1.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.9.7", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.3.0", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "7d929baca196a46f81a8802ac21271fd0d1b6186", "tarball": "https://registry.npmjs.org/ajv/-/ajv-5.0.4-beta.0.tgz", "integrity": "sha512-grNVWmOiiTbtvne+FT4MkRcoO3Cfz+vdcP1+A+Rtf/sCtR4wOQG65OnzB/bFbbXOsmji+s1bO/i32enuJ81IzA==", "signatures": [{"sig": "MEUCIQD9R4/g9MvU1+gPPd0SGxi8DIK30+cUn4+WMDWEfzM2AQIgUYZRpz1raOEQeH4so3jMic3nJUm5sNHHp3763vmaY6w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.0.4-beta.1": {"name": "ajv", "version": "5.0.4-beta.1", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^10.0.0", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^1.0.0", "eslint": "^3.2.2", "nodent": "^3.0.2", "del-cli": "^0.2.1", "bluebird": "^3.1.5", "ajv-async": "^0.1.0", "coveralls": "^2.11.4", "uglify-js": "2.6.1", "browserify": "^14.1.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.9.7", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.3.0", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "a4bcf1c8654005fa97ec18bf794784224ca4c72a", "tarball": "https://registry.npmjs.org/ajv/-/ajv-5.0.4-beta.1.tgz", "integrity": "sha512-FmFOb45Z8jAnCRSUo6cvB6h9GjiuuYp5O1V5wRZwGVozdlHcOUiezqgT5H/811icSgyAYd1GxSzs3ARs69LY6g==", "signatures": [{"sig": "MEUCIQD6r9ZNyHDqeDYSkhlWxbnrOxKodxo12rmV8Wv5d2nqrwIgQ/1YDHdsY6JqqNGgTpuA2IzAqispU7ELo9LSLz86DEs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.0.4-beta.2": {"name": "ajv", "version": "5.0.4-beta.2", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^10.0.0", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^1.0.0", "eslint": "^3.2.2", "nodent": "^3.0.2", "del-cli": "^0.2.1", "bluebird": "^3.1.5", "ajv-async": "^0.1.0", "coveralls": "^2.11.4", "uglify-js": "2.6.1", "browserify": "^14.1.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.9.7", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.3.0", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "2b1dbc09cbd69ee0797489894ab38d12c914e121", "tarball": "https://registry.npmjs.org/ajv/-/ajv-5.0.4-beta.2.tgz", "integrity": "sha512-YPvEAwavNXLPeLQ6uSL+QUDH0HGLtZiZf40mrjw1r2XdSmgoZqIjLjbvXJiy/qLBoAGngGdXcShbTzbFf6IkNg==", "signatures": [{"sig": "MEYCIQCmzUobSqtvWDvweGGPsk0tC1dB+j1Z2WScg4xgfNCHLgIhALlOS+rOoO6sIrrzpvaHf0+Hv5Wc80Q6/UZvtIZsEtTG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.11.6": {"name": "ajv", "version": "4.11.6", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^10.0.0", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^1.0.0", "eslint": "^3.2.2", "jshint": "^2.8.0", "nodent": "^3.0.17", "del-cli": "^0.2.1", "bluebird": "^3.1.5", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^14.1.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.9.7", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "947e93049790942b2a2d60a8289b28924d39f987", "tarball": "https://registry.npmjs.org/ajv/-/ajv-4.11.6.tgz", "integrity": "sha512-xrmZAObHtKwKabRD3emjratF1xto1iofa4qjXaII4BGY6I2C2BQysedk28bG92KK7UrzNWBJ2KWfS7YOlmEYeA==", "signatures": [{"sig": "MEQCIEtCWZ4c2v1ux8VW9FdeXs/xWrID/fuXhmz1k7NYuwXfAiAB5pT5sGwqsorr9H7WeqeSk42xhx1eiESJDy3F63ucMg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.0.4-beta.3": {"name": "ajv", "version": "5.0.4-beta.3", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^10.0.0", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^1.0.0", "eslint": "^3.2.2", "nodent": "^3.0.17", "del-cli": "^0.2.1", "bluebird": "^3.1.5", "ajv-async": "^0.1.0", "coveralls": "^2.11.4", "uglify-js": "2.6.1", "browserify": "^14.1.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.9.7", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.3.0", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "bb87e35a8f04787a3b7e9b7b2756a6acb6ac926c", "tarball": "https://registry.npmjs.org/ajv/-/ajv-5.0.4-beta.3.tgz", "integrity": "sha512-NMgpGC7c77x6se7iQa+i8i9lelFr+o5LwuDq9W04cX59CyzYVOxdofaKmpHTjZmKKZk67GQXjVKu/IYnYeMljg==", "signatures": [{"sig": "MEQCIEiooAUV/4pa3/9pDSy7306rTDh49uazo8GKJDV8UCcMAiB8qp5KBJ1+AfQ/z6WGe467eexaAbhSZ3ZdH+2aubXsrg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.11.7": {"name": "ajv", "version": "4.11.7", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^10.0.0", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^1.0.0", "eslint": "^3.2.2", "jshint": "^2.8.0", "nodent": "^3.0.17", "del-cli": "^0.2.1", "bluebird": "^3.1.5", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^14.1.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.9.7", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "8655a5d86d0824985cc471a1d913fb6729a0ec48", "tarball": "https://registry.npmjs.org/ajv/-/ajv-4.11.7.tgz", "integrity": "sha512-2MVO0/CRfG5g+iNr8pwb3S6RrlCjfu8/A0eIZbrL+VU/GDocPj3Yxuu/mhpXCPOLheSju2Lv+UvhNvbew56Q7Q==", "signatures": [{"sig": "MEYCIQDyWgT8TIMe+M8bjG70xGekxN6f6yFFmgOodDdtzQ0BiAIhAPXF8kikoo4n6ewsso+vVv0IS94KfPpmsLGLNflMbIVU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.0.0": {"name": "ajv", "version": "5.0.0", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^10.0.0", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^1.0.0", "eslint": "^3.2.2", "nodent": "^3.0.17", "del-cli": "^0.2.1", "bluebird": "^3.1.5", "ajv-async": "^0.1.0", "coveralls": "^2.11.4", "uglify-js": "2.6.1", "browserify": "^14.1.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.9.7", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.3.0", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "a2c717764e8036d15fd227b070ddaf7867ab413a", "tarball": "https://registry.npmjs.org/ajv/-/ajv-5.0.0.tgz", "integrity": "sha512-Ox7i7Qi/ypfWtCqYDWr40p2W1NKCFpDHy8C6SkEDY+h+t1nYjbtnbx7iw0iVuVtaHwpK7ephvWIRi1iMZvP8xQ==", "signatures": [{"sig": "MEUCIQD+sHUb6jmX0ZmsBypfTuLUVEsmKbcqJgnqeRzKefE5TQIgaMfXe7iB4KAfDxXTjyOVX5aA+GsLRE4FD4Z6AjDTEZk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.11.8": {"name": "ajv", "version": "4.11.8", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^10.0.0", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^1.0.0", "eslint": "^3.2.2", "jshint": "^2.8.0", "nodent": "^3.0.17", "del-cli": "^0.2.1", "bluebird": "^3.1.5", "coveralls": "^2.11.4", "uglify-js": "^2.6.1", "browserify": "^14.1.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.9.7", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.1.1", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "82ffb02b29e662ae53bdc20af15947706739c536", "tarball": "https://registry.npmjs.org/ajv/-/ajv-4.11.8.tgz", "integrity": "sha512-I/bSHSNEcFFqXLf91nchoNB9D1Kie3QKcWdchYUaoIg1+1bdWDkdfdlvdIOJbi9U8xR0y+MWc5D+won9v95WlQ==", "signatures": [{"sig": "MEUCIQCqnfANQ4jUkQIkIdCfYwgweMN0DgfPxkM4MeXewWZBGgIgVKc/jySK2jEe0o2ZTc5lPPspzsRqorwrJ2hlQizBzsA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.0.1": {"name": "ajv", "version": "5.0.1", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^10.0.0", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^1.0.0", "eslint": "^3.2.2", "nodent": "^3.0.17", "del-cli": "^0.2.1", "bluebird": "^3.1.5", "ajv-async": "^0.1.0", "coveralls": "^2.11.4", "uglify-js": "2.6.1", "browserify": "^14.1.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.9.7", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.3.0", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "5fd1a8f5cc92b371aa86445b1152fd4dec844ac9", "tarball": "https://registry.npmjs.org/ajv/-/ajv-5.0.1.tgz", "integrity": "sha512-35Wt++979JAxASxi4YQrDek+5Lq6TbXje+FK8sKtToUAAFOARGCkHprVIsFwQftMkLbdzN1L/rs2VmVadpOd3Q==", "signatures": [{"sig": "MEUCICfXtPr6C2EpQ8yjMmEFnjCIgezffA1ss+ebtEZMeQvtAiEAtG7EWu/MxE8a3T83lJpMbHTf4HdufyXkxghHv2Dyzgg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.1.0": {"name": "ajv", "version": "5.1.0", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^10.0.0", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^1.0.0", "eslint": "^3.2.2", "nodent": "^3.0.17", "del-cli": "^0.2.1", "bluebird": "^3.1.5", "ajv-async": "^0.1.0", "coveralls": "^2.11.4", "uglify-js": "2.6.1", "browserify": "^14.1.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.9.7", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.3.0", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "6e3bee45129b124bcd7a172859aeda6adbadef7a", "tarball": "https://registry.npmjs.org/ajv/-/ajv-5.1.0.tgz", "integrity": "sha512-gBkjlrJx1TCztp+VNqMue/yOQD/lN/C5PMKlY39HW5oBJXQ6eSiT3XrVOVYQzE70WNtAHWQTRTLaWB4e+Wmtyw==", "signatures": [{"sig": "MEYCIQD0TneE812E2AqHixSTBFudiSR+bDt4L1G4iR/rE4XT1wIhAJIJzoI5D/kqVBkT+X+C2hqdEc37pj6oJ7WBO41trdfS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.1.1": {"name": "ajv", "version": "5.1.1", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^10.0.0", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^1.0.0", "eslint": "^3.2.2", "nodent": "^3.0.17", "del-cli": "^0.2.1", "bluebird": "^3.1.5", "ajv-async": "^0.1.0", "coveralls": "^2.11.4", "uglify-js": "2.6.1", "browserify": "^14.1.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.9.7", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.3.0", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "6d9495b78eec4f2930536b2778ea40aa8645647a", "tarball": "https://registry.npmjs.org/ajv/-/ajv-5.1.1.tgz", "integrity": "sha512-2J9wdjhBHOhJ1iTwIR/fWy9g2fY49V0c9cvZuwZT023Hf1TxWBc+mkhGa8O2EWPuvWXhHeRfq4Cr2kfVGN+l9w==", "signatures": [{"sig": "MEUCIB2TpGLCrGqp3fWLg5L9aOLys7cvPQQVwUhE/ZP/NvdGAiEAj72s0LxR2OaSDTaU1Vfi7sn+vlsJr4EiD/aCujWR5bA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.1.2": {"name": "ajv", "version": "5.1.2", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^10.0.0", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^1.0.0", "eslint": "^3.2.2", "nodent": "^3.0.17", "del-cli": "^0.2.1", "bluebird": "^3.1.5", "ajv-async": "^0.1.0", "coveralls": "^2.11.4", "uglify-js": "^3.0.8", "browserify": "^14.1.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.9.7", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.3.0", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "c2be11aff5de51613592913bc820224906da84a1", "tarball": "https://registry.npmjs.org/ajv/-/ajv-5.1.2.tgz", "integrity": "sha512-ynPERnbOZ7w9SUYedDzTsfF3XS6GBRyHWwkXLjq1nKuSYcKYKwd3v4zh5IfMjDgFbkGLW95lKxFPkRaTQsQ8Ew==", "signatures": [{"sig": "MEQCIB4Ig/xlD6XsGhxhr4Z+vJibzHyvGBh1VIlaceSx5prqAiBSCe7MCd3e2iIEHEeC81gBscR/KnsHb0KkQVa3NhjU+A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.1.3": {"name": "ajv", "version": "5.1.3", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^10.0.0", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^1.0.0", "eslint": "^3.2.2", "nodent": "^3.0.17", "del-cli": "^0.2.1", "bluebird": "^3.1.5", "ajv-async": "^0.1.0", "coveralls": "^2.11.4", "uglify-js": "^3.0.8", "browserify": "^14.1.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.9.7", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.3.0", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "423d1c302c61e617081b30ca05f595ec51408e33", "tarball": "https://registry.npmjs.org/ajv/-/ajv-5.1.3.tgz", "integrity": "sha512-WoGet1wdEj0RysG8R2sQR2gsRwf16p9YHk7ZpJsjNSZS5hgMCTyr0EKN0Qv8WsW1S3c+OBShgZqyvgoYCyCnIA==", "signatures": [{"sig": "MEUCIQDIGsCFZE5OcDctDRnRK8ZMP1xK2vycG/7dwvhm6gminwIgEph48+Gyj6EjH3pZrUudA9CxuSJtoTs9wOzcW8ynieg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.1.4": {"name": "ajv", "version": "5.1.4", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^10.0.0", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^1.0.0", "eslint": "^3.2.2", "jshint": "^2.9.4", "nodent": "^3.0.17", "del-cli": "^0.2.1", "bluebird": "^3.1.5", "ajv-async": "^0.1.0", "coveralls": "^2.11.4", "uglify-js": "^3.0.8", "browserify": "^14.1.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.9.7", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.3.0", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "56f4ab21d42c2ae59e07de655dca6f8f3549809b", "tarball": "https://registry.npmjs.org/ajv/-/ajv-5.1.4.tgz", "integrity": "sha512-jBdYHIkAxi/H9xPvzGZ8k7RxO9Wnd/0WsoD12tpc3BjK8OH71OsS+nU6i8dvhw36Yhm+CP3cADpcM5qYysv+hA==", "signatures": [{"sig": "MEUCIA1o1o6GAuUcvWgajE4JCIPIiYBVEHcMI7roFzgJN5aFAiEAyxv/Vq3sfCAscEpyxaTGpFcxW+bSC1f4fuREvag4C7A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.1.5": {"name": "ajv", "version": "5.1.5", "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^10.0.0", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^1.0.0", "eslint": "^3.2.2", "jshint": "^2.9.4", "nodent": "^3.0.17", "del-cli": "^0.2.1", "bluebird": "^3.1.5", "ajv-async": "^0.1.0", "coveralls": "^2.11.4", "uglify-js": "^3.0.8", "browserify": "^14.1.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.9.7", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.3.0", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "8734931b601f00d4feef7c65738d77d1b65d1f68", "tarball": "https://registry.npmjs.org/ajv/-/ajv-5.1.5.tgz", "integrity": "sha512-Joc9LAW0F5NQLn1NWQb4gtD89rA7ag0Y9p5jIA9M/lEAhipaoPTd7VDWe/k2nirYFU2yb6mgJOA6S8sPmHS19w==", "signatures": [{"sig": "MEYCIQCaK8FN+ObEyGl7/RwfSLHElbXxtGUlLM1hKWf7EVdXnQIhAIdSSf1mbX+v4ff3Ti765ohfesBeI1Av3R4cJZFby3Q9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.1.6": {"name": "ajv", "version": "5.1.6", "dependencies": {"co": "^4.6.0", "json-schema-traverse": "^0.3.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^11.0.2", "brfs": "^1.4.3", "chai": "^4.0.1", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^1.0.0", "eslint": "^3.2.2", "jshint": "^2.9.4", "nodent": "^3.0.17", "del-cli": "^0.2.1", "bluebird": "^3.1.5", "ajv-async": "^0.1.0", "coveralls": "^2.11.4", "uglify-js": "^3.0.8", "browserify": "^14.1.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.9.7", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.3.0", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "4b2f1a19dece93d57ac216037e3e9791c7dd1564", "tarball": "https://registry.npmjs.org/ajv/-/ajv-5.1.6.tgz", "integrity": "sha512-K/r7dMp3q7rKzhx6v6deMuxVuQCw0w/789F75BooHOOVBEXzejwUq3LwO4x41C/xzXNKSNzqoAAS48Sx2a2Qxg==", "signatures": [{"sig": "MEUCIQDr40Pfqk9jVpEzrK2U24DWOKt/69vys7FEJm73FAUpXwIgaxBhHU8gkIEZOxKyKkvfWPpA9QbmeHzW0Dc1FoPWGDo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.2.0": {"name": "ajv", "version": "5.2.0", "dependencies": {"co": "^4.6.0", "fast-deep-equal": "^0.1.0", "json-schema-traverse": "^0.3.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^11.0.2", "brfs": "^1.4.3", "chai": "^4.0.1", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^1.0.0", "eslint": "^3.2.2", "jshint": "^2.9.4", "nodent": "^3.0.17", "del-cli": "^0.2.1", "bluebird": "^3.1.5", "ajv-async": "^0.1.0", "coveralls": "^2.11.4", "uglify-js": "^3.0.8", "browserify": "^14.1.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.9.7", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.3.0", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "c1735024c5da2ef75cc190713073d44f098bf486", "tarball": "https://registry.npmjs.org/ajv/-/ajv-5.2.0.tgz", "integrity": "sha512-aoGhU3DP+5oyyMkVP8yOEmnh169eJgPAL2ioe3ioii/qMpbAGHazYD1OgpQYF1BqZ36BEW1QUlt2BkIbsEmNfQ==", "signatures": [{"sig": "MEYCIQDTGHMUCkpzASbgnu03hPXvp7SfM51wRF1zRfNngp7wRgIhAPD7HzRODi2FWrIOPbm5njdl8LO+0+R0zOljgz0MnAin", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.2.1": {"name": "ajv", "version": "5.2.1", "dependencies": {"co": "^4.6.0", "fast-deep-equal": "^1.0.0", "json-schema-traverse": "^0.3.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^11.0.2", "brfs": "^1.4.3", "chai": "^4.0.1", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^1.0.0", "eslint": "^4.1.0", "jshint": "^2.9.4", "nodent": "^3.0.17", "del-cli": "^1.1.0", "bluebird": "^3.1.5", "ajv-async": "^0.1.0", "coveralls": "^2.11.4", "uglify-js": "^3.0.8", "browserify": "^14.1.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.9.7", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.3.0", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "dcd03045175883ba1b636e5ae9ec3df9ab85323a", "tarball": "https://registry.npmjs.org/ajv/-/ajv-5.2.1.tgz", "integrity": "sha512-vTN6ZRxAzj6in04mSZ7Lr/+vYsdAlSlQuat/wR2o+LxTbMupfYY01D+gyfj/H1myiMLkBwgPoPPI/ndpy4Ijug==", "signatures": [{"sig": "MEYCIQDX0mN6qjVF4EuF+98Od/TyIdCjSy54acMhoYlTspsoiAIhALq5t5RoWKvU71wIvFuGrUFJgUX572qD1AXe0vEg02j3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.2.2": {"name": "ajv", "version": "5.2.2", "dependencies": {"co": "^4.6.0", "fast-deep-equal": "^1.0.0", "json-schema-traverse": "^0.3.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^11.0.2", "brfs": "^1.4.3", "chai": "^4.0.1", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^1.0.0", "eslint": "^4.1.0", "jshint": "^2.9.4", "nodent": "^3.0.17", "del-cli": "^1.1.0", "bluebird": "^3.1.5", "ajv-async": "^0.1.0", "coveralls": "^2.11.4", "uglify-js": "^3.0.8", "browserify": "^14.1.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.5.6", "karma-mocha": "^1.1.1", "regenerator": "0.9.7", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.3.0", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "47c68d69e86f5d953103b0074a9430dc63da5e39", "tarball": "https://registry.npmjs.org/ajv/-/ajv-5.2.2.tgz", "integrity": "sha512-wrg7+QzNeuvzrL3ymA2RenaOhh+1AOli5DEWw534oJrso+HZBau4qO1WMX/X48+V9+AvfP+dJB8ScVVMdHBuDg==", "signatures": [{"sig": "MEUCIEb65WY+AKf+12QF/icNKjMDTmge8VXlw4J555O9wKujAiEA8npjPa/TuUuvG7ppi4BG/rVtCH/cYEOB2H/KK9qiWRk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.2.3": {"name": "ajv", "version": "5.2.3", "dependencies": {"co": "^4.6.0", "fast-deep-equal": "^1.0.0", "json-schema-traverse": "^0.3.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^11.0.2", "brfs": "^1.4.3", "chai": "^4.0.1", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^3.0.0", "watch": "^1.0.0", "eslint": "^4.1.0", "jshint": "^2.9.4", "nodent": "^3.0.17", "del-cli": "^1.1.0", "bluebird": "^3.1.5", "ajv-async": "^0.1.0", "coveralls": "^2.11.4", "uglify-js": "^3.0.8", "browserify": "^14.1.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "1.7.3", "karma-mocha": "^1.1.1", "regenerator": "0.9.7", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.3.0", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "c06f598778c44c6b161abafe3466b81ad1814ed2", "tarball": "https://registry.npmjs.org/ajv/-/ajv-5.2.3.tgz", "integrity": "sha512-UqTPrCL3Ij19z2oc3PsVZf/DRYkeIbUPqt9kkEyazgGtyImF+23YLtJs5cKgCW5/sDfaCdXEO4cPAyadvVAqlw==", "signatures": [{"sig": "MEUCIQDS4/V6tlP3gEzwbCWE+yrRex1AzjEFnydTqREF87vaSQIgSbwPooqqXzlYfGMz6iKqNtC8EVA5D6rawHTBlsxRkAg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.2.4": {"name": "ajv", "version": "5.2.4", "dependencies": {"co": "^4.6.0", "fast-deep-equal": "^1.0.0", "json-schema-traverse": "^0.3.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^11.0.2", "brfs": "^1.4.3", "chai": "^4.0.1", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^4.0.0", "watch": "^1.0.0", "eslint": "^4.1.0", "jshint": "^2.9.4", "nodent": "^3.0.17", "del-cli": "^1.1.0", "bluebird": "^3.1.5", "ajv-async": "^0.1.0", "coveralls": "^3.0.0", "uglify-js": "^3.1.5", "browserify": "^14.1.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.7.3", "karma-mocha": "^1.1.1", "regenerator": "0.10.0", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.3.0", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "3daf9a8b67221299fdae8d82d117ed8e6c80244b", "tarball": "https://registry.npmjs.org/ajv/-/ajv-5.2.4.tgz", "integrity": "sha512-TTF/6qHL2clhjDWHMpJuLBIQeHQ/kNMQ9fImkwwn3q8sVheLguRDfWlyqK/gAC6ccYTge7nBeqTIL6u0wUpidg==", "signatures": [{"sig": "MEUCIQDVBQVFwQ8Pb4sFy3Q38rqpsZyQCMwjdOFrfFvzxNLAbAIgT0zpw2LYZm756Y/0iDwJCgCj2QyZw0swoylIXFbgVH0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.2.5": {"name": "ajv", "version": "5.2.5", "dependencies": {"co": "^4.6.0", "fast-deep-equal": "^1.0.0", "json-schema-traverse": "^0.3.0", "json-stable-stringify": "^1.0.1"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^11.0.2", "brfs": "^1.4.3", "chai": "^4.0.1", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^4.0.0", "watch": "^1.0.0", "eslint": "^4.1.0", "jshint": "^2.9.4", "nodent": "^3.0.17", "del-cli": "^1.1.0", "bluebird": "^3.1.5", "ajv-async": "^0.1.0", "coveralls": "^3.0.0", "uglify-js": "^3.1.5", "browserify": "^14.1.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.7.3", "karma-mocha": "^1.1.1", "regenerator": "0.10.0", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.3.0", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "b637234d3e2675eb5f79fc652242a853a48cb49f", "tarball": "https://registry.npmjs.org/ajv/-/ajv-5.2.5.tgz", "integrity": "sha512-lhBCO8ZRekUVifgHf+8V/VO2h8/TJWQtxeXdTOWv14sVWmJjcxvjH5J38MBLipxVpXmyX1a/lyBom8y8MLkvZw==", "signatures": [{"sig": "MEUCICAdtC7aIop7cf1zDyYCYgj86HUqmI5bfMCBgdjZfYZEAiEA6w4El020AIfvW8/9aq1eItoslM080DBfg20xbMQf/OI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.3.0": {"name": "ajv", "version": "5.3.0", "dependencies": {"co": "^4.6.0", "fast-deep-equal": "^1.0.0", "json-schema-traverse": "^0.3.0", "fast-json-stable-stringify": "^2.0.0"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^11.0.2", "brfs": "^1.4.3", "chai": "^4.0.1", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^4.0.0", "watch": "^1.0.0", "eslint": "^4.1.0", "jshint": "^2.9.4", "nodent": "^3.0.17", "del-cli": "^1.1.0", "bluebird": "^3.1.5", "ajv-async": "^0.1.0", "coveralls": "^3.0.0", "uglify-js": "^3.1.5", "browserify": "^14.1.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.7.3", "karma-mocha": "^1.1.1", "regenerator": "0.10.0", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.3.0", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "4414ff74a50879c208ee5fdc826e32c303549eda", "tarball": "https://registry.npmjs.org/ajv/-/ajv-5.3.0.tgz", "integrity": "sha512-8nU5XnCRAmlQcv7xo7YxcmVqwDdU2k7UzCzViWlU4ueURyKIF1xrgCtTSUo/F2B/IgWLEhPO4VJJI9Vp0ITyfQ==", "signatures": [{"sig": "MEUCIQCyxJdsdd7fzmBx8vYMCpxfbJIPscfb8IvL4AzUBuyr7wIgECeQeIFkz1w8e4x+csqX67UdMOrWp6i6pmErFgWygiQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "6.0.0-beta.0": {"name": "ajv", "version": "6.0.0-beta.0", "dependencies": {"fast-deep-equal": "^1.0.0", "json-schema-traverse": "^0.3.0", "fast-json-stable-stringify": "^2.0.0"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^11.0.2", "brfs": "^1.4.3", "chai": "^4.0.1", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^4.0.0", "watch": "^1.0.0", "eslint": "^4.1.0", "jshint": "^2.9.4", "del-cli": "^1.1.0", "bluebird": "^3.1.5", "ajv-async": "^1.0.0-beta.0", "coveralls": "^3.0.0", "uglify-js": "^3.1.5", "browserify": "^14.1.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.7.3", "karma-mocha": "^1.1.1", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.3.0", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "6948adce420ded6b1ebdf961a4fc87b7cc007f3a", "tarball": "https://registry.npmjs.org/ajv/-/ajv-6.0.0-beta.0.tgz", "integrity": "sha512-0D+JqLRnEDTFQVz3JUa9DdbGKdGeZsDfZUkTUzM4mN7UqfjRwNxZ1qD6tlaykCWrTUJQyVwnMgCiAqzLBiyScw==", "signatures": [{"sig": "MEYCIQCvWaAEFsLNI3o0TtJvhzuqCpQiq0qrgdDSjXTVZVUxSwIhAP4OOdmuUaQ93mrQjvkrR5SmJTXuOCw70fVxq+dBU4eJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "6.0.0-beta.1": {"name": "ajv", "version": "6.0.0-beta.1", "dependencies": {"fast-deep-equal": "^1.0.0", "json-schema-traverse": "^0.3.0", "fast-json-stable-stringify": "^2.0.0"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^11.0.2", "brfs": "^1.4.3", "chai": "^4.0.1", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^4.0.0", "watch": "^1.0.0", "eslint": "^4.1.0", "jshint": "^2.9.4", "nodent": "^3.1.3", "del-cli": "^1.1.0", "bluebird": "^3.1.5", "ajv-async": "^1.0.0-beta.0", "coveralls": "^3.0.0", "uglify-js": "^3.1.5", "browserify": "^14.1.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.7.3", "karma-mocha": "^1.1.1", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.3.0", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "9cb8aa4e8f81120c1e9ff8bddd2e1f72450ed499", "tarball": "https://registry.npmjs.org/ajv/-/ajv-6.0.0-beta.1.tgz", "integrity": "sha512-N4glv82lEo+MbhqHKi+00NFg5EsJQ0jrry9TEuzVer4M5fGbZjlRUICG1R8chStgH2BGvMiQsSgaKp6tDoiPwQ==", "signatures": [{"sig": "MEQCIDkRaghdiP8ObMqi5b9U/Eh7ZaebmXY+2kb58hvbFkfeAiBbeECWTgo0W8oREhPhoL8I8wyZ2UeeRJcpp2MJ5LNV3Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "6.0.0-beta.2": {"name": "ajv", "version": "6.0.0-beta.2", "dependencies": {"fast-deep-equal": "^1.0.0", "json-schema-traverse": "^0.3.0", "fast-json-stable-stringify": "^2.0.0"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^11.0.2", "brfs": "^1.4.3", "chai": "^4.0.1", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^4.0.0", "watch": "^1.0.0", "eslint": "^4.1.0", "jshint": "^2.9.4", "del-cli": "^1.1.0", "bluebird": "^3.1.5", "ajv-async": "^1.0.0-beta.0", "coveralls": "^3.0.0", "uglify-js": "^3.1.5", "browserify": "^14.1.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.7.3", "karma-mocha": "^1.1.1", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.3.0", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "3b27bc918fe934e8aaaaf636430732ec5622a0c4", "tarball": "https://registry.npmjs.org/ajv/-/ajv-6.0.0-beta.2.tgz", "integrity": "sha512-Zi5zxbE0smPoPGTWkb+/oXChHeVkEVqtmjWP4VryeZ+Tf5krKLqdKMBg7vxeaFDcKgl0N1FF/AUaKoYLtgs0ig==", "signatures": [{"sig": "MEYCIQCdJTUXeKv1dq+6VgV46IT/wFiamzuXeiKb3GMBz5BXrAIhAIeLZNXVF+4WlvXgeycGDPRm0+PY+KYFT6V4IqQ63449", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.4.0": {"name": "ajv", "version": "5.4.0", "dependencies": {"co": "^4.6.0", "fast-deep-equal": "^1.0.0", "json-schema-traverse": "^0.3.0", "fast-json-stable-stringify": "^2.0.0"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^11.0.2", "brfs": "^1.4.3", "chai": "^4.0.1", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^4.0.0", "watch": "^1.0.0", "eslint": "^4.1.0", "jshint": "^2.9.4", "nodent": "^3.0.17", "del-cli": "^1.1.0", "bluebird": "^3.1.5", "ajv-async": "^0.1.0", "coveralls": "^3.0.0", "uglify-js": "^3.1.5", "browserify": "^14.1.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.7.3", "karma-mocha": "^1.1.1", "regenerator": "0.10.0", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.3.0", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "32d1cf08dbc80c432f426f12e10b2511f6b46474", "tarball": "https://registry.npmjs.org/ajv/-/ajv-5.4.0.tgz", "integrity": "sha512-XbC09YLKiH9lr2Km/bYxJ/J7i/WU/9yozocw6rQd49nuW+Nw4xZ29FQvSKCDsqZisGc1/EhCfC5rOOAcEDN+5g==", "signatures": [{"sig": "MEUCIQDO756tSj1HyC1PsxD7awHtee1I1Ap6YuyRO7Ogq8DQWwIgb2niIoiOcn3LjFaGNluqii7XC5l51GfEnKYVryJZAY8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.5.0": {"name": "ajv", "version": "5.5.0", "dependencies": {"co": "^4.6.0", "fast-deep-equal": "^1.0.0", "json-schema-traverse": "^0.3.0", "fast-json-stable-stringify": "^2.0.0"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^11.0.2", "brfs": "^1.4.3", "chai": "^4.0.1", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^4.0.0", "watch": "^1.0.0", "eslint": "^4.1.0", "jshint": "^2.9.4", "nodent": "^3.0.17", "del-cli": "^1.1.0", "bluebird": "^3.1.5", "ajv-async": "^0.1.0", "coveralls": "^3.0.0", "uglify-js": "^3.1.5", "browserify": "^14.1.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.7.3", "karma-mocha": "^1.1.1", "regenerator": "0.11.1", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.3.0", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "eb2840746e9dc48bd5e063a36e3fd400c5eab5a9", "tarball": "https://registry.npmjs.org/ajv/-/ajv-5.5.0.tgz", "integrity": "sha512-B+mXip5xc2RtctLFXcjEd6rPtkYWNYOaue1UrRQMkw+Ypx2Fv2Y5xal3pO4V+R54bCW/z5AunNrQkP0BH2M4Pg==", "signatures": [{"sig": "MEUCIQCmzy5kTo/E+JAimR2YLPLlKu9sof8QkSrIr0Dqt7QGPgIgWvq4qhJAsOvcQF6nh0AkH9SnAeD6AHwOax8E93CCGKg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "6.0.0-rc.0": {"name": "ajv", "version": "6.0.0-rc.0", "dependencies": {"fast-deep-equal": "^1.0.0", "json-schema-traverse": "^0.3.0", "fast-json-stable-stringify": "^2.0.0"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^11.0.2", "brfs": "^1.4.3", "chai": "^4.0.1", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^4.0.0", "watch": "^1.0.0", "eslint": "^4.1.0", "jshint": "^2.9.4", "del-cli": "^1.1.0", "bluebird": "^3.1.5", "ajv-async": "^1.0.0-beta.0", "coveralls": "^3.0.0", "uglify-js": "^3.1.5", "browserify": "^14.1.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.7.3", "karma-mocha": "^1.1.1", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.3.0", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "fd9262d6d14e16f5685f952341c613bc00a2bf8f", "tarball": "https://registry.npmjs.org/ajv/-/ajv-6.0.0-rc.0.tgz", "integrity": "sha512-kMFvER3YXweJskR+0OyVNgO1BQfzk0kf2rA4YNZ+fmdVp7Jm0ZWGD4eXSE6dGQHflhCuGQ0JRm8UIf8NEK+5aA==", "signatures": [{"sig": "MEUCIGIIlahc84rOQ0SM7Iau22rZJisdPhFRJ3XbeCDeAkerAiEAv7SSK7qYbWZlgcmltBYxCAejXG4jyuBzwaVSwWd/BUE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.5.1": {"name": "ajv", "version": "5.5.1", "dependencies": {"co": "^4.6.0", "fast-deep-equal": "^1.0.0", "json-schema-traverse": "^0.3.0", "fast-json-stable-stringify": "^2.0.0"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^11.0.2", "brfs": "^1.4.3", "chai": "^4.0.1", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^4.0.0", "watch": "^1.0.0", "eslint": "^4.1.0", "jshint": "^2.9.4", "nodent": "^3.0.17", "del-cli": "^1.1.0", "bluebird": "^3.1.5", "ajv-async": "^0.1.0", "coveralls": "^3.0.0", "uglify-js": "^3.1.5", "browserify": "^14.1.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.7.3", "karma-mocha": "^1.1.1", "regenerator": "0.11.1", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^1.3.0", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "b38bb8876d9e86bee994956a04e721e88b248eb2", "tarball": "https://registry.npmjs.org/ajv/-/ajv-5.5.1.tgz", "integrity": "sha512-64SZdr7DVN8QFT68w4MuCqwrSV38VHVfmr2JExL9Pgg5YYYDbJLHplr2GJ5FAJMWKStVEgF4vYLoXNn/ctojHw==", "signatures": [{"sig": "MEUCIA9eOiptBVbpPd1pzVrS/GoIgggH3DYKq6Hc3/oIQwjaAiEA8RlPd3GWjP/bS8qUnlYg3mVr4ECPawlOKJcLu+nFxYE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "6.0.0-rc.1": {"name": "ajv", "version": "6.0.0-rc.1", "dependencies": {"fast-deep-equal": "^1.0.0", "json-schema-traverse": "^0.3.0", "fast-json-stable-stringify": "^2.0.0"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^11.0.2", "brfs": "^1.4.3", "chai": "^4.0.1", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^4.0.0", "watch": "^1.0.0", "eslint": "^4.1.0", "jshint": "^2.9.4", "del-cli": "^1.1.0", "bluebird": "^3.1.5", "ajv-async": "^1.0.0-beta.0", "coveralls": "^3.0.0", "uglify-js": "^3.1.5", "browserify": "^14.1.0", "pre-commit": "^1.1.1", "typescript": "^2.0.3", "js-beautify": "^1.7.3", "karma-mocha": "^1.1.1", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^2.0.0", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "1fe616f4282e171deeb57f9bfad610d6c16d7b69", "tarball": "https://registry.npmjs.org/ajv/-/ajv-6.0.0-rc.1.tgz", "integrity": "sha512-f9x3pWBLqMqB6j7PYftin1diazhS9FL8VhJKHV+XiRLE++6kBLjmKLaHqijuXVDKDh0C2j22S9mqwnE5ogi9dw==", "signatures": [{"sig": "MEUCIQDNYil/T4DEvRE7hssGBqueV6vhGMlnaYzqiIrps8Ya7gIgOy1kLn1ozbBgdaiFcLVTe3cJpXaFuv5h+h43I6Tz9i4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.5.2": {"name": "ajv", "version": "5.5.2", "dependencies": {"co": "^4.6.0", "fast-deep-equal": "^1.0.0", "json-schema-traverse": "^0.3.0", "fast-json-stable-stringify": "^2.0.0"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^11.0.2", "brfs": "^1.4.3", "chai": "^4.0.1", "glob": "^7.0.0", "karma": "^1.0.0", "mocha": "^4.0.0", "watch": "^1.0.0", "eslint": "^4.1.0", "jshint": "^2.9.4", "nodent": "^3.0.17", "del-cli": "^1.1.0", "bluebird": "^3.1.5", "ajv-async": "^0.1.0", "coveralls": "^3.0.0", "uglify-js": "^3.1.5", "browserify": "^14.1.0", "pre-commit": "^1.1.1", "typescript": "^2.6.2", "js-beautify": "^1.7.3", "karma-mocha": "^1.1.1", "regenerator": "^0.12.2", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^2.0.0", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "73b5eeca3fab653e3d3f9422b341ad42205dc965", "tarball": "https://registry.npmjs.org/ajv/-/ajv-5.5.2.tgz", "integrity": "sha512-Ajr4IcMXq/2QmMkEmSvxqfLN5zGmJ92gHXAeOXq1OekoH2rfDNsgdDoL2f7QaRCy7G/E6TpxBVdRuNraMztGHw==", "signatures": [{"sig": "MEUCIQCKSHHq1t+D8Fk5UMI0JbyBG8am+YkNjR+R5F3a0pS2qAIgANgicms3HsBcMp8MGK7uN6QKXEKGqX0zjEpf1MQhpQo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "6.0.0": {"name": "ajv", "version": "6.0.0", "dependencies": {"fast-deep-equal": "^1.0.0", "json-schema-traverse": "^0.3.0", "fast-json-stable-stringify": "^2.0.0"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^11.0.2", "brfs": "^1.4.3", "chai": "^4.0.1", "glob": "^7.0.0", "karma": "^2.0.0", "mocha": "^4.0.0", "watch": "^1.0.0", "eslint": "^4.14.0", "jshint": "^2.9.4", "del-cli": "^1.1.0", "bluebird": "^3.1.5", "ajv-async": "^1.0.0", "coveralls": "^3.0.0", "uglify-js": "^3.3.1", "browserify": "^15.0.0", "pre-commit": "^1.1.1", "typescript": "^2.6.2", "js-beautify": "^1.7.3", "karma-mocha": "^1.1.1", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^2.0.0", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "093bec4d9bac8e4505e541ae10eb6150268684c2", "tarball": "https://registry.npmjs.org/ajv/-/ajv-6.0.0.tgz", "integrity": "sha512-rVv6EKcNMG7GmCUf8ZF04xJioIs5EGX7pfzFt896WwY2g3Zfjyc8ToTgdvN0PJYBV+bFPitwLokTE3zaFG0u8Q==", "signatures": [{"sig": "MEQCIGisgmMYh8Ph4F4LPx4FsAu7UKHb7+wh6M6XaXPJ9+qeAiB9rqDLOsC+zWEmD7Mpr2VRydEndtzJU9RBsJWPYt5NdA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "6.0.1": {"name": "ajv", "version": "6.0.1", "dependencies": {"fast-deep-equal": "^1.0.0", "json-schema-traverse": "^0.3.0", "fast-json-stable-stringify": "^2.0.0"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^11.0.2", "brfs": "^1.4.3", "chai": "^4.0.1", "glob": "^7.0.0", "karma": "^2.0.0", "mocha": "^4.0.0", "watch": "^1.0.0", "eslint": "^4.14.0", "jshint": "^2.9.4", "del-cli": "^1.1.0", "bluebird": "^3.1.5", "ajv-async": "^1.0.0", "coveralls": "^3.0.0", "uglify-js": "^3.3.1", "browserify": "^15.0.0", "pre-commit": "^1.1.1", "typescript": "^2.6.2", "js-beautify": "^1.7.3", "karma-mocha": "^1.1.1", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^2.0.0", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "2898580a9f3def5f9c85dfead7a2223ef13cf3da", "tarball": "https://registry.npmjs.org/ajv/-/ajv-6.0.1.tgz", "integrity": "sha512-aZbM5MqJ/qr9ISiQGR7rZ58O2KsMRhzr39GnTRHZd4+A+00qp4nb8gNUXInKL+b6jh9nWMXNxgjJ8pMfzWs0bA==", "signatures": [{"sig": "MEUCIDi6PHD74t2Kn8BdvASAamJUmUnEEo9K/DQuCtrC59ekAiEAkMv4ZS4wNAqOsKwsTjN1qhOR/60iPNnTRfwfAgKYv70=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "6.1.0": {"name": "ajv", "version": "6.1.0", "dependencies": {"fast-deep-equal": "^1.0.0", "json-schema-traverse": "^0.3.0", "fast-json-stable-stringify": "^2.0.0"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^11.0.2", "brfs": "^1.4.3", "chai": "^4.0.1", "glob": "^7.0.0", "karma": "^2.0.0", "mocha": "^5.0.0", "watch": "^1.0.0", "eslint": "^4.14.0", "jshint": "^2.9.4", "del-cli": "^1.1.0", "bluebird": "^3.1.5", "ajv-async": "^1.0.0", "coveralls": "^3.0.0", "uglify-js": "^3.3.1", "browserify": "^15.0.0", "pre-commit": "^1.1.1", "typescript": "^2.6.2", "js-beautify": "^1.7.3", "karma-mocha": "^1.1.1", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^2.0.0", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "adc4b3dd64b2d8740d13c5b38e4596115970e59d", "tarball": "https://registry.npmjs.org/ajv/-/ajv-6.1.0.tgz", "integrity": "sha512-yz7cw89Dv2R3QPw+5YA6hv7lFRHyrTJvbmXRa5jsQ5Mdsrgel7rqjsr3ltbptXUgjduImxNZsdWe5bwnU8+c7A==", "signatures": [{"sig": "MEQCIEr0ARRQ9AgDzvjEV1hruIHZ+e2T+m6ZkJ69PewE7+r9AiAPNr+jr6piF4aFzj3+ke1XQ38qaZ9lcRxpbq5Noudszw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "6.1.1": {"name": "ajv", "version": "6.1.1", "dependencies": {"fast-deep-equal": "^1.0.0", "json-schema-traverse": "^0.3.0", "fast-json-stable-stringify": "^2.0.0"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^11.0.2", "brfs": "^1.4.3", "chai": "^4.0.1", "glob": "^7.0.0", "karma": "^2.0.0", "mocha": "^5.0.0", "watch": "^1.0.0", "eslint": "^4.14.0", "jshint": "^2.9.4", "del-cli": "^1.1.0", "bluebird": "^3.1.5", "ajv-async": "^1.0.0", "coveralls": "^3.0.0", "uglify-js": "^3.3.1", "browserify": "^15.0.0", "pre-commit": "^1.1.1", "typescript": "^2.6.2", "js-beautify": "^1.7.3", "karma-mocha": "^1.1.1", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^2.0.0", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "978d597fbc2b7d0e5a5c3ddeb149a682f2abfa0e", "tarball": "https://registry.npmjs.org/ajv/-/ajv-6.1.1.tgz", "integrity": "sha512-twePFPI+vu/jS+TdZXNtc+SHalI8VUXtzcMIFqIiOWlRoHuJF4jGMY/PXiM4hMAaHflK5nR3/OWBW2DitYu+Ug==", "signatures": [{"sig": "MEUCIQDlwoGnMsraadulus6NPd/elDU9avMQkUPZeEkwwrsoEAIgag/o18Ipl75SttXQlggRhRrfxGDxXxUXkrnuhgnDT+s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "6.2.0": {"name": "ajv", "version": "6.2.0", "dependencies": {"fast-deep-equal": "^1.0.0", "json-schema-traverse": "^0.3.0", "fast-json-stable-stringify": "^2.0.0"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^11.0.2", "brfs": "^1.4.3", "chai": "^4.0.1", "glob": "^7.0.0", "karma": "^2.0.0", "mocha": "^5.0.0", "watch": "^1.0.0", "eslint": "^4.14.0", "jshint": "^2.9.4", "del-cli": "^1.1.0", "bluebird": "^3.1.5", "ajv-async": "^1.0.0", "coveralls": "^3.0.0", "uglify-js": "^3.3.1", "browserify": "^16.0.0", "pre-commit": "^1.1.1", "typescript": "^2.6.2", "js-beautify": "^1.7.3", "karma-mocha": "^1.1.1", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^2.0.0", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "afac295bbaa0152449e522742e4547c1ae9328d2", "tarball": "https://registry.npmjs.org/ajv/-/ajv-6.2.0.tgz", "fileCount": 90, "integrity": "sha512-WVQXRJVS1kkVkJ47ovVauCOyUXIolm13NR1YiJ1LtkeaNW824SYEr8XcqFpY8e9F9UYNodmGVOSXZN0GWE/IdQ==", "signatures": [{"sig": "MEYCIQCMcgSV40CLc29vo0AfeThDJhvA7CDGleWt2SHclpyorQIhAPvBaBTu5iW9Mix2SUWrSxLMPzojEwB0HSMMDv8YLfFV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 863237}}, "6.2.1": {"name": "ajv", "version": "6.2.1", "dependencies": {"fast-deep-equal": "^1.0.0", "json-schema-traverse": "^0.3.0", "fast-json-stable-stringify": "^2.0.0"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^11.0.2", "brfs": "^1.4.3", "chai": "^4.0.1", "glob": "^7.0.0", "karma": "^2.0.0", "mocha": "^5.0.0", "watch": "^1.0.0", "eslint": "^4.14.0", "jshint": "^2.9.4", "del-cli": "^1.1.0", "bluebird": "^3.1.5", "ajv-async": "^1.0.0", "coveralls": "^3.0.0", "uglify-js": "^3.3.1", "browserify": "^16.0.0", "pre-commit": "^1.1.1", "typescript": "^2.6.2", "js-beautify": "^1.7.3", "karma-mocha": "^1.1.1", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^2.0.0", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "28a6abc493a2abe0fb4c8507acaedb43fa550671", "tarball": "https://registry.npmjs.org/ajv/-/ajv-6.2.1.tgz", "fileCount": 90, "integrity": "sha512-voYPSH/zkuz4stgIR6ykq1ymkaqlYFODpc9stdkYqmuSHy9qvUpFrQSFYcl9AyB+9sl9n28m5xQtpqDaRvfjyA==", "signatures": [{"sig": "MEUCIQDYp7SEHuE17xtfrb7qVZYGCrKn9EB0cPTvd4XUq0M60QIgbv/Vyes/7i/mbN1n4kce314mCncpD+1aQ8KN4uYrIw0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 865547}}, "6.3.0": {"name": "ajv", "version": "6.3.0", "dependencies": {"fast-deep-equal": "^1.0.0", "json-schema-traverse": "^0.3.0", "fast-json-stable-stringify": "^2.0.0"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^11.0.2", "brfs": "^1.4.3", "chai": "^4.0.1", "glob": "^7.0.0", "karma": "^2.0.0", "mocha": "^5.0.0", "watch": "^1.0.0", "eslint": "^4.14.0", "jshint": "^2.9.4", "del-cli": "^1.1.0", "bluebird": "^3.1.5", "ajv-async": "^1.0.0", "coveralls": "^3.0.0", "uglify-js": "^3.3.1", "browserify": "^16.0.0", "pre-commit": "^1.1.1", "typescript": "^2.6.2", "js-beautify": "^1.7.3", "karma-mocha": "^1.1.1", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^2.0.0", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "1650a41114ef00574cac10b8032d8f4c14812da7", "tarball": "https://registry.npmjs.org/ajv/-/ajv-6.3.0.tgz", "fileCount": 90, "integrity": "sha512-6TQywaGYtRub2fqHkSXfVANlhfja2nbF33wCCHnt3aQstOrtd9jsQGiRUTIOlkEqcxpzRd2akfnqvBBPmLxs8g==", "signatures": [{"sig": "MEYCIQC4p0zw7eN4XPqDah81i1Fxm5OkktLU8n6zQw4SWRKCzwIhAKq5+AD2vbKzrGieZOmg86TZ/oBoqOOhcvswbqKK8Cjl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 865500}}, "6.4.0": {"name": "ajv", "version": "6.4.0", "dependencies": {"uri-js": "^3.0.2", "fast-deep-equal": "^1.0.0", "json-schema-traverse": "^0.3.0", "fast-json-stable-stringify": "^2.0.0"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^11.0.2", "brfs": "^1.4.3", "chai": "^4.0.1", "glob": "^7.0.0", "karma": "^2.0.0", "mocha": "^5.0.0", "watch": "^1.0.0", "eslint": "^4.14.0", "jshint": "^2.9.4", "del-cli": "^1.1.0", "bluebird": "^3.1.5", "ajv-async": "^1.0.0", "coveralls": "^3.0.0", "uglify-js": "^3.3.1", "browserify": "^16.0.0", "pre-commit": "^1.1.1", "typescript": "^2.6.2", "js-beautify": "^1.7.3", "karma-mocha": "^1.1.1", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^2.0.0", "gh-pages-generator": "^0.2.0", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "d3aff78e9277549771daf0164cff48482b754fc6", "tarball": "https://registry.npmjs.org/ajv/-/ajv-6.4.0.tgz", "fileCount": 90, "integrity": "sha512-uWK8ISUH3jdo9gTIgylnj4QQDKFL6SbQcx9LtwXJ+2biBCSNIhc41aYJO0W1/w+6tEMPsopQ0cOCnWX39u5o0w==", "signatures": [{"sig": "MEYCIQDwr0s08qnYWS8oHwcIl2ak8DuQeYMLOGG6TogItBh2ewIhAOUYKA+KXbDA2XVNJZAAAR/P7YAGF7IEFaGMqCHE+iIh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 879725}}, "6.5.0": {"name": "ajv", "version": "6.5.0", "dependencies": {"uri-js": "^4.2.1", "fast-deep-equal": "^2.0.1", "json-schema-traverse": "^0.3.0", "fast-json-stable-stringify": "^2.0.0"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^11.7.1", "brfs": "^1.6.1", "chai": "^4.0.1", "glob": "^7.0.0", "karma": "^2.0.2", "mocha": "^5.1.1", "watch": "^1.0.0", "eslint": "^4.14.0", "jshint": "^2.9.4", "del-cli": "^1.1.0", "bluebird": "^3.1.5", "ajv-async": "^1.0.0", "coveralls": "^3.0.1", "uglify-js": "^3.3.24", "browserify": "^16.2.0", "pre-commit": "^1.1.1", "typescript": "^2.8.3", "js-beautify": "^1.7.3", "karma-mocha": "^1.1.1", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^2.0.0", "gh-pages-generator": "^0.2.3", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "4c8affdf80887d8f132c9c52ab8a2dc4d0b7b24c", "tarball": "https://registry.npmjs.org/ajv/-/ajv-6.5.0.tgz", "fileCount": 90, "integrity": "sha512-VDUX1oSajablmiyFyED9L1DFndg0P9h7p1F+NO8FkIzei6EPrR6Zu1n18rd5P8PqaSRd/FrWv3G1TVBqpM83gA==", "signatures": [{"sig": "MEUCICL/mwPKsL9eH5Hmzh782nAwKUQwc8OpkX8ddiKeKH5ZAiEA9SguGB/nLj7uRoO5uADyr35Vvw/7lwRcPwZ4yDWJDHk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 891744, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa8g1cCRA9TVsSAnZWagAAmbgP/0rJ8DQWkzmVwsSe8hGC\nPiHzsSEYwno+vPnVOtGKJoRrz+lFpOfAHGNvjCkv8GHJeL7AljwtnYTZK2Kx\nSRERVeQPBr3Y56rXcuMJ+nU2aPKF8FcstaRzRAZtKCeQrl5YOzZXGNmJXgra\ntpEqEZ8hsF7aNLlIqqCHDyW88pwDzu2GZ5WcAbnY0seKjv0HHxGQIgWXTgWk\nzp/eqH1JUACwYEe0mDsIA4ANXXsROJa21bRg/IRQVYahd10u5M4agE5k7kxB\nkFP50ZB8dL91FTSoWPnSETZXCxA0fDd1Qne4dsnIq0iV4RwOd8GG/k6OJOXu\n8/ceftM1Gzcz408tW6HibmQoN3tkuEeMcXsnXQPpbeVXrmroUF8SiKte4pds\nyBD7Js/KG8T9XDeBA3C+hO5hEIq11cgckCSohd/CTZAr4tr/4N2lzFWGXxvJ\nzUp6tgn9PQKDmZvjpigYRkkpaapevAdFxOK7O6O3fAK+eK6xICl61ied+AOx\nAL0RIbLrqXYlsqI/4kkNphyMZcIvKQj3wzeL8s9u91Se+/402AQN2ilFjjTQ\nR85x8M7XnozSsGx+FgOkSudEkf+ztEEF6BhQ1B/i2C0F28AqNAJ1BGRQk6cw\naTB7zmauezrqfgdOnqh1TNAJKYhRlzidGfM//bDvGqXhTitRipLUdn/ez27K\nwUs6\r\n=+V84\r\n-----END PGP SIGNATURE-----\r\n"}}, "6.5.1": {"name": "ajv", "version": "6.5.1", "dependencies": {"uri-js": "^4.2.1", "fast-deep-equal": "^2.0.1", "json-schema-traverse": "^0.4.1", "fast-json-stable-stringify": "^2.0.0"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^12.0.1", "brfs": "^1.6.1", "chai": "^4.0.1", "glob": "^7.0.0", "karma": "^2.0.2", "mocha": "^5.1.1", "watch": "^1.0.0", "eslint": "^4.14.0", "jshint": "^2.9.4", "del-cli": "^1.1.0", "bluebird": "^3.1.5", "ajv-async": "^1.0.0", "coveralls": "^3.0.1", "uglify-js": "^3.3.24", "browserify": "^16.2.0", "pre-commit": "^1.1.1", "typescript": "^2.8.3", "js-beautify": "^1.7.3", "karma-mocha": "^1.1.1", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^2.0.0", "gh-pages-generator": "^0.2.3", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "88ebc1263c7133937d108b80c5572e64e1d9322d", "tarball": "https://registry.npmjs.org/ajv/-/ajv-6.5.1.tgz", "fileCount": 90, "integrity": "sha512-pgZos1vgOHDiC7gKNbZW8eKvCnNXARv2oqrGQT7Hzbq5Azp7aZG6DJzADnkuSq7RH6qkXp4J/m68yPX/2uBHyQ==", "signatures": [{"sig": "MEUCIHLctCllYEsay7DxDfFsL2wS7V1IyD3/aVKSZmSBUOHSAiEA0LBmh1ZRzcxXlCxfWSIpIt1yKD+fnyH3ZhFFNo5xM5g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 891938, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbHRSiCRA9TVsSAnZWagAAVH8P/R7Lhx6SisFM8zk3WXF+\nn0jcTZ5womW1HvZ1z0bjFMFop58KIj/J8bgFVcyZNaX9w24XNu1Z2XU++Ule\ndo5wMgcgIVQyfiDS/HYmLkD7ZNrNKqZ7dGWI6NR+NTFNNTKlvOkyr9tYHrZj\nM6HAT3UL/N18kYVSx0TpDZ+9z2WLQGmut0rFJsEvfSZ3YBfaq3ilgPfw/amv\nGs77T3DMm0LtVIeKGrJYC+Ja6ykcigLX2GAZOn3Hk6bYxZl9YFHNZcvpXeF8\nw4QGF5RCd9XpwbtjuSCsBx+5fiy0rL59ixSmczmylj2G/Hf+QxI3ugoGakTI\nEaFj3/6JKED8F3gNFrNrLqwJIGFNSw8qS44uIYIyAry3NeBGgaM9LvtsYB0m\nGzLIvLx5Pv3tOXJgTpyd3nBroFjWED1AYwuiHKarv6THSLRtjAInaTb3fxzM\nDPmckb5NVKJNCNjwYblwct/g5sHgQMrcOXiuTb5R6LeE1wAnl4gjeOBEWuyH\nN++zkzvd92pluSCIMWLGW3tHUdbts1J7qpZeUr0ZvIdqu+tDaeqY1X54hdty\nHz7pmICTKNsvd+Jlrz8L9XVEuLAnMJSWOlGH2cezLZXMHpsXRBYbhHQGcutC\nrIqlWIqudgYbr9qqwMKxnU+7YhOB7wb8p+/xC23lBUeyUoCU66mC4u1cpedT\nfbvN\r\n=UyAw\r\n-----END PGP SIGNATURE-----\r\n"}}, "6.5.2": {"name": "ajv", "version": "6.5.2", "dependencies": {"uri-js": "^4.2.1", "fast-deep-equal": "^2.0.1", "json-schema-traverse": "^0.4.1", "fast-json-stable-stringify": "^2.0.0"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^12.0.1", "brfs": "^2.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "karma": "^2.0.2", "mocha": "^5.1.1", "watch": "^1.0.0", "eslint": "^5.0.0", "jshint": "^2.9.4", "del-cli": "^1.1.0", "bluebird": "^3.1.5", "ajv-async": "^1.0.0", "coveralls": "^3.0.1", "uglify-js": "^3.3.24", "browserify": "^16.2.0", "pre-commit": "^1.1.1", "typescript": "^2.8.3", "js-beautify": "^1.7.3", "karma-mocha": "^1.1.1", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^2.0.0", "gh-pages-generator": "^0.2.3", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "678495f9b82f7cca6be248dd92f59bff5e1f4360", "tarball": "https://registry.npmjs.org/ajv/-/ajv-6.5.2.tgz", "fileCount": 90, "integrity": "sha512-hOs7GfvI6tUI1LfZddH82ky6mOMyTuY0mk7kE2pWpmhhUSkumzaTO5vbVwij39MdwPQWCV4Zv57Eo06NtL/GVA==", "signatures": [{"sig": "MEYCIQC9Hmyr3qKJUe/TzOsKYDLlgA0Hc39GvlFexa9h0UwJXgIhAIlHe8mgFnGg+sEPeYKLi5HKZUbhtCwybFJsZYN+77vf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 892093, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbN9KrCRA9TVsSAnZWagAAONgP/jH6Q565uMn41cuy5l4F\ngHMXQfpGmMcjwGR2Vbv8amT9jTz/WbjEQrw/llC4i0Qxc0YGgPS7yjLvG2Iw\n2YqW7NHXzaNkxgQOKNmSZ6SkZjDwwZTgf6HJbgp4S7CgOms6+ZdjxmXskge5\nfycRDicFJNK3L1cXhZd0OT3HxVm8lKPQBl4ks0WDNEhPAORa27vYXeh+F1jV\niyZ2ym1xvUbVsAqT4m00UWVD5DG9RVvj3cgslXMOMbdL+gvAQ271W00Bvs5u\nlaeP23cw6urcVE5yR7lcWvi1F+R0cuWdNCMibFXiGa8/BMugc7ebz8zc/6uB\nTGaBHHjpV9HKkXRS7LKCzxJ+jcxBLoCuuqUhzGxmqhSTC3hLgD9cijGo5ks9\nsQLbKQL55SpsDBW3+z8lWmfqfMV+d/nGMY3WngH7dwlp5HM0nqxtU3dJR2BA\n+GMmxX8Uxh7w3pLdp4s/lBYJ+DEwXWNuh5Liw59uzUX1eZ85Mr2Scz+nIM78\nb/ChJE7KchZJfwaJPauQX923z4Yk3gmVI9Zy2AmB5NO0TXhnfVZuai6wnbgH\nxf4m8riaRuD6VrAJVBq5Ue+QlFA7+f8wZ6gL2xT4yX/1ZYM3ch7vmNEmBbt7\n3h6JtDC7YvlRczuKXrMp5B9X56JSZVN3Bv9W28YvIv99O5UKKK/WRqycZ8mj\nRSRQ\r\n=RKky\r\n-----END PGP SIGNATURE-----\r\n"}}, "6.5.3": {"name": "ajv", "version": "6.5.3", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^2.0.1", "json-schema-traverse": "^0.4.1", "fast-json-stable-stringify": "^2.0.0"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^12.0.1", "brfs": "^2.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "karma": "^3.0.0", "mocha": "^5.1.1", "watch": "^1.0.0", "eslint": "^5.0.0", "jshint": "^2.9.4", "del-cli": "^1.1.0", "bluebird": "^3.1.5", "ajv-async": "^1.0.0", "coveralls": "^3.0.1", "uglify-js": "^3.3.24", "browserify": "^16.2.0", "pre-commit": "^1.1.1", "typescript": "^2.8.3", "js-beautify": "^1.7.3", "karma-mocha": "^1.1.1", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^2.0.0", "gh-pages-generator": "^0.2.3", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "71a569d189ecf4f4f321224fecb166f071dd90f9", "tarball": "https://registry.npmjs.org/ajv/-/ajv-6.5.3.tgz", "fileCount": 90, "integrity": "sha512-LqZ9wY+fx3UMiiPd741yB2pj3hhil+hQc8taf4o2QGRFpWgZ2V5C8HA165DY9sS3fJwsk7uT7ZlFEyC3Ig3lLg==", "signatures": [{"sig": "MEUCIQDmV8x5Qu+C+vjcyibhdXfCpHIi5L15ho+gq8ZB4K2pNgIgYccco/VdmHeH05AWq5JujmmRvboI4tkXNvzLIbQ1lJw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 892205, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbdvfFCRA9TVsSAnZWagAAqQ4P/Ap2itS5hVYsGrfjOG6A\n6ZzrufcY+1FltJgYi6jFMyViB0UJMUtFNQjoGzUHzEFrUXODGfa+i/qjn9iq\nSk7RRyMKlLWAQU9QulDsWGfXfMLlJbHCKunHjptcPqE6m9/t9sgHZaks/Qxw\ngtCOrq/LhEJf20uYTXIjAfSRiVsJHUCM82BYelgbQkclY+Oxl09zVqU9V8uz\ne1ec55Mz2RAz47Ht0ch18W7OIsIKkNVAf9fSyVl+l7JR5sN6IEm8Vs7Bk6rh\n5pah5yQyqpf97gmO+LSQYrsSX3gCRuRf3wsfzrtJJ3Kec0SKRBqlyt3UoiJ6\neo78n0VfW+4ZTu7JzgRLfbmGx8IVdXW3eZLDL/4TivzCO8tzmCthrSkxu1Ns\nb+egkQO8TyKCvY4k4tVaPTLzTqbKxkkEqbW194QHTNzv38WgFiSdUVyCgD2K\nWag7zHjdUjV9djt7idvpA9w8tSMWr2Mv06g254JH90QtYj1425enUwjgvEvp\nrXlyEPejNuPvhSvgI5k3Y1X+hkNredOdocOFOxVxZ1OGLty00OjEPuQkzn5v\nh1z5cRvGo5JqeftoTzNqqSmcQpI/gZ/Nz+6AzA5scRXElQ7L6jnh7hS6V7Vv\nUWKTyD08XxndISZrdrzQ4Sh+qep6KOiKPEd8MZYH2Lafg7yI1RszgRnB8yaW\nb9Fz\r\n=/zCV\r\n-----END PGP SIGNATURE-----\r\n"}}, "6.5.4": {"name": "ajv", "version": "6.5.4", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^2.0.1", "json-schema-traverse": "^0.4.1", "fast-json-stable-stringify": "^2.0.0"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^12.0.1", "brfs": "^2.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "karma": "^3.0.0", "mocha": "^5.1.1", "watch": "^1.0.0", "eslint": "^5.0.0", "jshint": "^2.9.4", "del-cli": "^1.1.0", "bluebird": "3.5.1", "ajv-async": "^1.0.0", "coveralls": "^3.0.1", "uglify-js": "^3.3.24", "browserify": "^16.2.0", "pre-commit": "^1.1.1", "typescript": "^2.8.3", "js-beautify": "^1.7.3", "karma-mocha": "^1.1.1", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^2.0.0", "gh-pages-generator": "^0.2.3", "phantomjs-prebuilt": "^2.1.4", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "247d5274110db653706b550fcc2b797ca28cfc59", "tarball": "https://registry.npmjs.org/ajv/-/ajv-6.5.4.tgz", "fileCount": 90, "integrity": "sha512-4Wyjt8+t6YszqaXnLDfMmG/8AlO5Zbcsy3ATHncCzjW/NoPzAId8AK6749Ybjmdt+kUY1gP60fCu46oDxPv/mg==", "signatures": [{"sig": "MEUCIELMeNQWg98qB6r6CGrkvCpy30Ns71z6d+fEik2Z4Jw8AiEAkN0ROEXhdaM8xWN8WzvwMK6uSsQR+uIJCYumNBXmYXg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 892200, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbp3MpCRA9TVsSAnZWagAAVOQQAJaOUGtdxORh29IhM4o4\ndB8XjyC+wwWqCBZmcbCHCYvSvVU3W3lnttY2d+3/c6P3KJ01iP2CPWg8rptB\nYe39jV10BUoHew9v0PfUWON5M+8cxM/FE5IXuriTkEYSbMCwdz2TUgD+wziJ\n+7UjEza+1Z2MVZ0ukPk1H34HIiwAAHJy9uK7Mf0bZBrl3d+p/NzZZt7HdH3O\nFp0zXMV4anL13SR2ILS5F7Jvt1RXCdKZXj85JKYX8SvRt7Z+CPSkKgW+DknU\n7TQ/NLbOx3yX6JSHl1FeF1vo3cnpg1W8C3D+oK1k5x0axAEp3OH4H+/GrQcc\nMKDOnE8V11AVrWgxi3fuiU6wdzMS8xo5N+yL3BYTu9Q1aUTaJm4dbM6pQUHX\nRw+ApVrHUx8PNW208hBoipxFY0eBLHFAbpnq3nrRpclN4vhFPtRvn/s2mfj6\njZSqymub9RIt5Nm8kXOZtpjKJignFCqYCJJFgW5zEvhMK0BDeaLb7XKMIJ16\nOCbefUu3ItKX4jsKI3/VE7laarteIuBnyvA1VB8jgb+QIlXdSbumMVINLzzz\nZV0wvcosY+1dtqauaIHbjceGtukM3UllEROYEgDKWej/NxxWvKBytZR+QybN\ngpJ/fOpeATZScKDXmSEXtoBscoUsE15esG9di215RgOCuCBldjEYEV2v7mWK\nXPZr\r\n=AxBO\r\n-----END PGP SIGNATURE-----\r\n"}}, "6.5.5": {"name": "ajv", "version": "6.5.5", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^2.0.1", "json-schema-traverse": "^0.4.1", "fast-json-stable-stringify": "^2.0.0"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^12.0.1", "brfs": "^2.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "karma": "^3.0.0", "mocha": "^5.1.1", "watch": "^1.0.0", "eslint": "^5.0.0", "jshint": "^2.9.4", "del-cli": "^1.1.0", "bluebird": "3.5.1", "ajv-async": "^1.0.0", "coveralls": "^3.0.1", "uglify-js": "^3.3.24", "browserify": "^16.2.0", "pre-commit": "^1.1.1", "typescript": "^2.8.3", "js-beautify": "^1.7.3", "karma-mocha": "^1.1.1", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^2.0.0", "gh-pages-generator": "^0.2.3", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.2.0"}, "dist": {"shasum": "cf97cdade71c6399a92c6d6c4177381291b781a1", "tarball": "https://registry.npmjs.org/ajv/-/ajv-6.5.5.tgz", "fileCount": 90, "integrity": "sha512-7q7gtRQDJSyuEHjuVgHoUa2VuemFiCMrfQc9Tc08XTAc4Zj/5U1buQJ0HU6i7fKjXU09SVgSmxa4sLvuvS8Iyg==", "signatures": [{"sig": "MEUCIQD/WlfpxYJLwdFZPaedm4yfmMeSsUpTs6V+R+WvGE0l+gIgI/VzmvBsVVMqfAuwxD+802FHhkGeKmoff3CeLSl9ii0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 892244, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb32jmCRA9TVsSAnZWagAAupcP/1lj6/uhQQ5h45uEYppt\nIbOXNGoToU4FpcCfBgA6HFcaJCYKi3WecJvatWj4wAyK8XlV+QtdXB717pLa\nZWvYPvy7TVzkDHcFMIJnThejYnNdekLK/ao3Uj//YXDBlO7ddGVZjS5c9b+p\n+nbvt1HIpLovnnd4deRKA/fzaj4TeEMSQEnUzho31ve7hxg0oZ9zYrLfRkMc\n1M1k7zyqQPj71mi2mziR5YozQR0uTloimG8/0pnxjZgVYUdDzb1ymJbTzG92\nig6r7yd28fCBDEGKiaPOcsWlE4ktaVOn3uPRO0Z3FmKN32QWCXdiLvK+V/0J\nVo0TrjG5XE5JM3JXo6aaBIHlpwLwCsCa1183IgDA9Ncu0RwHYqQCrRPKFLCv\nWusRLDB1NXjESHJNOKHYcGWrFisrAz0PMWULw5lC+i+1ECIqlmGtE/YkBnn7\nsCfjnDoaMXmMZ5ResgFPp9sT4/KBm9JId+sf+AFyHyHL5c/M48+Kn3TipEIp\nB8+T4i9feTOcZvCBWwy9oBW5OG5nAYkVh/YKtrgnjIgl5Rr/fzsEN1giRDVT\ncrMli3XOtFOElEp5FG0ECInwqZI7/45VgITaL5HnFAdQjvcd5oAtnpxkc49a\nN32Sx3udrhMoh+b8LLcvsBVlJUyCy8+/VmPOSB6rrkvOVEWw0MZ2ao68AZmF\nt9WH\r\n=UNv/\r\n-----END PGP SIGNATURE-----\r\n"}}, "6.6.0": {"name": "ajv", "version": "6.6.0", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^2.0.1", "json-schema-traverse": "^0.4.1", "fast-json-stable-stringify": "^2.0.0"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^12.0.1", "brfs": "^2.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "karma": "^3.0.0", "mocha": "^5.1.1", "watch": "^1.0.0", "eslint": "^5.0.0", "jshint": "^2.9.4", "del-cli": "^1.1.0", "bluebird": "^3.5.3", "ajv-async": "^1.0.0", "coveralls": "^3.0.1", "uglify-js": "^3.3.24", "browserify": "^16.2.0", "pre-commit": "^1.1.1", "typescript": "^2.8.3", "js-beautify": "^1.7.3", "karma-mocha": "^1.1.1", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^2.0.0", "gh-pages-generator": "^0.2.3", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.2.0"}, "dist": {"shasum": "762e4a2f97cf423c9a2472b819f227c3a081a895", "tarball": "https://registry.npmjs.org/ajv/-/ajv-6.6.0.tgz", "fileCount": 90, "integrity": "sha512-ZCIMdm75ps9usdeb0GQL9rKOUlTtS0p0vU2nQVXgRIu6Yb00G9GY7AvbVLwTQ5Po4JDKIwJlT5nwggoRDrDVAw==", "signatures": [{"sig": "MEQCIBC9yd6JB/rnGd+2/McNn5RdpSUX9pArIrmhFfaU1PabAiAez8MOkJRxdsDKEj/cOcez/Yeigov9xJ76mDC9YS3UeQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 900678, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb/5PICRA9TVsSAnZWagAAxnIP/RkTBGrEiiW4dtyjniKN\n4qMWoDJ0yYKqQTZJgf9RigMmeFonohNEJdjsIheoz2aRPWHxFINRdpPO5/fL\n+/oCx9a6Y34heGS86MR0E2PtAVb0mBcke3J/sDDXdUEp6yAh6eMt11c7qVso\nuJQYkpVvrAWi1Q55lGiUj3/VDYd58QF3DYEwwLGVrMhJyV4+7ZTjgcLSkWZ2\n+n8um1/RjelH/6KDSX+KnLcpnHEaBL/lU87S2mF8aPe/sbNc6GW7SMcyx8Tr\nKI4+eRtvP3qQRxRTr2JX1xunNDm908kAQljMWzmeoe66DB2CZJPjDDlTuQvl\nbYoPzoFeucTVn0hL+00whRYFAlcvXmZbaYl2VTABWf2mMTydFRKGSeKXEudN\nnSOGUMuk/GYCL4JRq1SbxRjSS0mb9z8+9I0BmsZu6dCaLzq/ShUf9qPxSTIU\nXnzwY3bLs5HN5OO8P4xZGuNhNLHZeM4bEnOeltIYYaLukTbezjv/sfMzBeHN\n/7BIveLRDeKUhcf6xN5YO3ZUMPAbZx+Jdl2lqc2dJ8qRVwFcihB93iaTqNpI\nqhU6ZWl5moYjTulUYzkja3TvaCQHgN+jNf/RiVUGtIpy9ZLSLq6+31/0bjKf\nBMEtuiH0wYlh9S/T7kjOMW7Ujfz+UwqAhBjFzYHp5g0VzN4ppF48Gqz8o1aa\nbAaQ\r\n=udiE\r\n-----END PGP SIGNATURE-----\r\n"}}, "6.6.1": {"name": "ajv", "version": "6.6.1", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^2.0.1", "json-schema-traverse": "^0.4.1", "fast-json-stable-stringify": "^2.0.0"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^12.0.1", "brfs": "^2.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "karma": "^3.0.0", "mocha": "^5.1.1", "watch": "^1.0.0", "eslint": "^5.0.0", "jshint": "^2.9.4", "del-cli": "^1.1.0", "bluebird": "^3.5.3", "ajv-async": "^1.0.0", "coveralls": "^3.0.1", "uglify-js": "^3.3.24", "browserify": "^16.2.0", "pre-commit": "^1.1.1", "typescript": "^2.8.3", "js-beautify": "^1.7.3", "karma-mocha": "^1.1.1", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^2.0.0", "gh-pages-generator": "^0.2.3", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.2.0"}, "dist": {"shasum": "6360f5ed0d80f232cc2b294c362d5dc2e538dd61", "tarball": "https://registry.npmjs.org/ajv/-/ajv-6.6.1.tgz", "fileCount": 91, "integrity": "sha512-ZoJjft5B+EJBjUyu9C9Hc0OZyPZSSlOF+plzouTrg6UlA8f+e/n8NIgBFG/9tppJtpPWfthHakK7juJdNDODww==", "signatures": [{"sig": "MEQCIFU1+e8kb6jeYuLf2L6I+77Orq0J8k02bPz+x2A6AKodAiA2j3wg9XHfVUl1YLtqd20bCUAbzbsZavupVSz/CegU6Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 900738, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb/8aoCRA9TVsSAnZWagAAUKsP/1WCWmYE3pPpeJJyFDTh\nw3Ei/qFEsA/ueg3K843jSEz6Q/DAAxuPc3l/hZqn4+K1WuoNiJS0URK1uz8L\nVv53WtWUtCS9hs4VZpNukGxZF12WGWwIV6Zsvb5GLJh8K7a5vDHc0N9MIbzn\nS60XmW1vrtMQj3lQMzTtG4/FfHyZK8yAv7b13YKmZtRdu6NvZqqOykDcJ+Pk\n8/U7kQ7LmwjA01Auqwob2tC0qjWBDd2K2PIc4UeRfbmMGgVIBRbuOLUI/H7D\nsqxtZtrkgcLKD1mMLQJ5s18W6/tsBmV6gr7mspCKTwVZkI3cCzf/MJ40bAwj\nO6eMe5RPL3sREwUls9jFAYi3ZSSa0Wz3vzZO/C7EC8rAUa7/oIvCdz038Do7\ndme47dTpf1mSRB6c//ZEmrS4K4ClF6kMzEmmnN+PEDN/d8ZheI9uhc56rDrJ\n91GGYwckc9vn+aOZskvFG0qUN2Wp/6rYd8uXLdiZRoxIz6lebO4ssc3PW4IY\nJu3h8G67FjcLtlCkA+vCdHPihymFjnGfP1BNFl1Gs1YVsHATZDQscFCSO2g8\nbgkCLiW8B8gjsAzkVd2BkEVBNM0HffcajSzPtY33pefxPp6ddhKcksjUA18U\nNIJbYYJTmCabNqUSpj3BFHGvHNF5zVAWcauTRvwQfJOCgjuhBIRU/74c2fXs\noP/4\r\n=pikB\r\n-----END PGP SIGNATURE-----\r\n"}}, "6.6.2": {"name": "ajv", "version": "6.6.2", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^2.0.1", "json-schema-traverse": "^0.4.1", "fast-json-stable-stringify": "^2.0.0"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^12.0.1", "brfs": "^2.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "karma": "^3.0.0", "mocha": "^5.1.1", "watch": "^1.0.0", "eslint": "^5.0.0", "jshint": "^2.9.4", "del-cli": "^1.1.0", "bluebird": "^3.5.3", "ajv-async": "^1.0.0", "coveralls": "^3.0.1", "uglify-js": "^3.3.24", "browserify": "^16.2.0", "pre-commit": "^1.1.1", "typescript": "^2.8.3", "js-beautify": "^1.7.3", "karma-mocha": "^1.1.1", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^2.0.0", "gh-pages-generator": "^0.2.3", "karma-sauce-launcher": "^2.0.0", "karma-chrome-launcher": "^2.2.0"}, "dist": {"shasum": "caceccf474bf3fc3ce3b147443711a24063cc30d", "tarball": "https://registry.npmjs.org/ajv/-/ajv-6.6.2.tgz", "fileCount": 91, "integrity": "sha512-FBHEW6Jf5TB9MGBgUUA9XHkTbjXYfAUjY43ACMfmdMRHniyoMHjHjzD50OK8LGDWQwp4rWEsIq5kEqq7rvIM1g==", "signatures": [{"sig": "MEUCICimcddjeA0fFSNpWvqgECq0Agvw24nPtLAp/omjfm3CAiEAneYVhvdunD0u11FTaIoBnTvRRvFLGE218vrghpClC5c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 899259, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcFr9kCRA9TVsSAnZWagAArncP/04gwvhF96AqLh9qIapm\n3DARhIvWkdJmWNKvLNLGcP8MUp82B/7kNgyMNeXbku3oLDUmuvQNWg9jFWny\nwrODNnnE7vUGkFnyLqmnkt2g+wZVW32yAa1AVpDl+xjE8F8Zv9jR6habQJ25\nJGHm9JJ91eMeQ71425HsnMDIUcszHPD2Z8XZwPuY/iY7gRNbIX5YuKQfydYd\nPkjHISnuOZFhFG/rxK9O8KNczsBxZ4V8f0UoTqLjliFXA4u0u6mc7g4iUyCN\n0Yj3qiV1sy1Yn/ZegIETdXkWyh1u0x5qUTHYC908UEGRjs2D8ym7XbTGQFzG\n7lEJN9hy3jxllV8IE2/H6LmMrYpFLaIflWeEhTQz+5oSPHBycTE++VhZf4+L\npcVFPHZk+DeD0mkNzVrAGDFhIqxs+ezkWn5z3p2RDh1zgSezOHM+xymX3fuR\npCWAUFynbtTZ7n9X4MiUk7h8Wmc096mFDDxpJ0HeAQsknsxCPpW6PAQEpcB3\nUOyctDQwFfIfM0thYPTSStkqin+zY/4AavmrnMdC2/I3W82ziBKGWtnO+Wy+\nPMpLGTtpkIZudfpT+A4KCnU++Ypk09i1ArG0X94kcAQ0Ur7XyZ3WjUfWsqBE\nCl+GwrfInoFNJ6Q3pdxmcudNALYdbAHMjwyNOnTQtOwb04sa+kIeSm6AX49D\n6grN\r\n=YOEY\r\n-----END PGP SIGNATURE-----\r\n"}}, "6.7.0": {"name": "ajv", "version": "6.7.0", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^2.0.1", "json-schema-traverse": "^0.4.1", "fast-json-stable-stringify": "^2.0.0"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^12.0.1", "brfs": "^2.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "karma": "^3.0.0", "mocha": "^5.1.1", "watch": "^1.0.0", "eslint": "^5.0.0", "jshint": "^2.9.4", "del-cli": "^1.1.0", "bluebird": "^3.5.3", "ajv-async": "^1.0.0", "coveralls": "^3.0.1", "uglify-js": "^3.3.24", "browserify": "^16.2.0", "pre-commit": "^1.1.1", "typescript": "^2.8.3", "js-beautify": "^1.7.3", "karma-mocha": "^1.1.1", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^2.0.0", "gh-pages-generator": "^0.2.3", "karma-sauce-launcher": "^2.0.0", "karma-chrome-launcher": "^2.2.0"}, "dist": {"shasum": "e3ce7bb372d6577bb1839f1dfdfcbf5ad2948d96", "tarball": "https://registry.npmjs.org/ajv/-/ajv-6.7.0.tgz", "fileCount": 91, "integrity": "sha512-RZXPviBTtfmtka9n9sy1N5M5b82CbxWIR6HIis4s3WQTXDJamc/0gpCWNGz6EWdWp4DOfjzJfhz/AS9zVPjjWg==", "signatures": [{"sig": "MEQCIFW83s60m+jy6+yXQIaeJ+1eBjzQ20PrrR+xzHVmAAFBAiBeoA0KHwfe1QAvu44S96c9IB4g1cSQ8YdnU6Zw2125Lw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 899255, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcO3yMCRA9TVsSAnZWagAAFRIP/RH6OW13kfVTZiorupft\n9JtEIUwjnpbpJkUF5Zu/+SDcMQ11M5hMlO4vnRKQID4N1K3WwNLwGzM7DW1e\n6N8GayDsBWh/sC+iCkCqTXLsLg4zG9uqIJjKxPaOXX0rZ28JsYQJbDo4Zf4W\n2Iw/9xc0X+I2CaGEONxVUHyCPyC32lOqLjQ/M86TqGK0BAyibYKvMAOz3glJ\nlm6ESu01hzjv/JLSR6iLLyjdj670DwdsmH8BYmMPnVImuyGCitktymseyXmc\nL7TdV1CbiSDr+lLXoahpY5qyCTZC24tQcccd1HIQk1/ZueD1+jUjjRRUjddm\numw+F4SJ6dp9oQzpYVUiG1bsqcj5aYSLSOe2HSvm4pw2CIgSOlJEhtHXELye\nmoYY49jPfvz6OTYDGgWpEB2zY+hpcSsS+A2iIK+anOOA/Gl0OdDPehfF4VeO\nI4o8e30SdFLb4y4iy45yTOy+pEtVBKcqyVs/QTfiUzWn++sxNCBFn8oIz22d\nB6cHWJTsc8aplW9HSbMQ4vJVA7BvwnT3Eq/ZVU0wyyNfyYMVwXoVj0kvwSLO\nOk5fc98gPP+mvMKTShIJBTFbQQfi3K5Flxgg0deKBjS1cOYFRCWRWWz7M9UN\nsdZsUs8ixKEz3bGgQ8lta5wKxjH9OXaoRoHqOcSfYVxyBVo6BgU6eBlSSUG7\nVKsf\r\n=ByaT\r\n-----END PGP SIGNATURE-----\r\n"}}, "6.8.0": {"name": "ajv", "version": "6.8.0", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^2.0.1", "json-schema-traverse": "^0.4.1", "fast-json-stable-stringify": "^2.0.0"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^12.0.1", "brfs": "^2.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "karma": "^3.0.0", "mocha": "^5.1.1", "watch": "^1.0.0", "eslint": "^5.0.0", "jshint": "^2.9.4", "del-cli": "^1.1.0", "bluebird": "^3.5.3", "ajv-async": "^1.0.0", "coveralls": "^3.0.1", "uglify-js": "^3.3.24", "browserify": "^16.2.0", "pre-commit": "^1.1.1", "typescript": "^2.8.3", "js-beautify": "^1.7.3", "karma-mocha": "^1.1.1", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^2.0.0", "gh-pages-generator": "^0.2.3", "karma-sauce-launcher": "^2.0.0", "karma-chrome-launcher": "^2.2.0"}, "dist": {"shasum": "2beb1d7e08e172a6d69f906d4ea673090c8a8ce7", "tarball": "https://registry.npmjs.org/ajv/-/ajv-6.8.0.tgz", "fileCount": 92, "integrity": "sha512-S0MzH/VXaOsLwL7WrqH2xXDN9DNJdUhWoGNVHx4FGcGHcnqyiArq6DPimEmFCEVs59MAxBApkkAO7XCCa1fhug==", "signatures": [{"sig": "MEUCIQDwTeOPfaKb2GlGbjbtNqeeRR2/GnOer2fHksITch97jwIgSvZQG8SblduT2fmk9Vi2tgsQQ6XIsRVFb5bZMOdEpnI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 904894, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcVfe4CRA9TVsSAnZWagAAUKYP/3ltofFqfkp9zQLfGUE8\n9R+Dar9+QTxGtTNteb9KEZWsKTrq+cKOvvvNnvQu/yuhTGS/MxbGKkqRSPJc\nA9AQDneQU6Jao3LBfhgfkcNDf789VOCZmZVxurVBVJkeQfSCc37Nn08q5ZTI\nemMBu6BfMxXGi/CTELYd4jc95UZj/Ae+cq5bQfm7Oue2h9+ENb45fY+9gl1w\nNTX/8lBK0qQDdSbeKSJ/HfLXglYrJC+0Vi35f0/HVCrh+g1yhXsTXXNerHhw\n7BxV4DQ7kOt9l6eoZSWo4yz5jmnLHvspqg8GtRL7MjLfSKRvrfRWERKn6rtO\no5tJtFkhLAKrXeRWkb4LxDJv1wv6iFOBxAJ0tCpqazFppB/RTE+USJm7pQZ4\nFnIB5v1Xuw+WztUk/7VfsW38b1Eog6LnFFgdcTRSXxic3feyhzO05q6SM7ex\n3lt7Ux/k1BDFHU5mmoHC4rwJh9hgezG8yhgUqAtoMt2mz/TKouH7a1GZXgtu\naWo0YWwxUUU4Ar0okNYM4SZiBNhMu4P/tmR+VVSQa/IqWAnHHLgQfO/euQlV\nwDWxkXqYbUiNh2IedNI1L7zPACASTga8fuec7+tkyM6hN47LfhZ3DakvgYg1\n66I8qqtHlVCiHHLKj9zfwgGpxOYbE5krLIHcwjUNt2GF2gm8J3j4Yj2J5hUU\nr/vq\r\n=fbsf\r\n-----END PGP SIGNATURE-----\r\n"}}, "6.8.1": {"name": "ajv", "version": "6.8.1", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^2.0.1", "json-schema-traverse": "^0.4.1", "fast-json-stable-stringify": "^2.0.0"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^12.0.1", "brfs": "^2.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "karma": "^3.0.0", "mocha": "^5.1.1", "watch": "^1.0.0", "eslint": "^5.0.0", "jshint": "^2.9.4", "del-cli": "^1.1.0", "bluebird": "^3.5.3", "ajv-async": "^1.0.0", "coveralls": "^3.0.1", "uglify-js": "^3.3.24", "browserify": "^16.2.0", "pre-commit": "^1.1.1", "typescript": "^2.8.3", "js-beautify": "^1.7.3", "karma-mocha": "^1.1.1", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^2.0.0", "gh-pages-generator": "^0.2.3", "karma-sauce-launcher": "^2.0.0", "karma-chrome-launcher": "^2.2.0"}, "dist": {"shasum": "0890b93742985ebf8973cd365c5b23920ce3cb20", "tarball": "https://registry.npmjs.org/ajv/-/ajv-6.8.1.tgz", "fileCount": 92, "integrity": "sha512-eqxCp82P+JfqL683wwsL73XmFs1eG6qjw+RD3YHx+Jll1r0jNd4dh8QG9NYAeNGA/hnZjeEDgtTskgJULbxpWQ==", "signatures": [{"sig": "MEUCIHJUnYW5kxi7IUJ20OFxYVgyR35SZCtowwYElS/OqzVnAiEAlRu6YI/yrV7+uWDkO7sBLvhCKfKZPAIY5tT4WaYXzJc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 904894, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcVfhtCRA9TVsSAnZWagAAz7MP/3VWO/Q4xt3SUyZAMcdz\na/2ToeegY82l0VO02DazCASQyC4ftRByuzzZIogFYo9ZrtBfwQ6hDVAyvik3\nQxJhllV+hPLeLuSsAhaa9uPzSKe7Izu7rq74e0oLYtOk601AFoQnIj0MweP0\n4T+LgPv1qJQgpFW58ESt3O/NghtjjgUx29lifi0IA8uyDpILwbujh9wIXgiH\n2ZyS/47Ry1qD+7GUcPTqDrZ32Aj1mx6osurFW3A504k+JRVs1nKr6RFhvdpL\nv/lokRuuh0lTO7dZDzV/4pcFNALjvkAQ+hOoDxmDyH4lFmTfbH11x2+dU75B\nk4NBYy6bgDf8x3JpaVXAxg080j2ASE1B92vxtVAn2ZdPDbb/7Fm7YSFagnJS\nTEpZSlot6MeK1PkLFZHybXuZxdtbMhW9gzvyigICQViiLyKhDZqD5ulQIh/B\nEBjzVV3LzbGopw7pqQygcBTbdVLtDSTJxYGPLZ2w+Q5sLEy5wuty3xRFJIhh\nYYjr/McTqcMNIVpuhRNNRZe/pouNuqPuLr+ucdYaNIIEQDFzAPu+xv8juzdz\n0PMzxPqmijNBLifo7LzMnUd737kS9wSis6VxCZuHNMfbNaW49SbUiUumsOzM\nNskMOVmUlxoYvMsuFgqukIR69rROAcWe8JaVJkpV1fitv4pprUCH/Ok2qN0J\nLuAG\r\n=XsVJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "6.9.0": {"name": "ajv", "version": "6.9.0", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^2.0.1", "json-schema-traverse": "^0.4.1", "fast-json-stable-stringify": "^2.0.0"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^12.0.1", "brfs": "^2.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "karma": "^3.0.0", "mocha": "^5.1.1", "watch": "^1.0.0", "eslint": "^5.0.0", "jshint": "2.9.7", "del-cli": "^1.1.0", "bluebird": "^3.5.3", "ajv-async": "^1.0.0", "coveralls": "^3.0.1", "uglify-js": "^3.3.24", "browserify": "^16.2.0", "pre-commit": "^1.1.1", "typescript": "^2.8.3", "js-beautify": "^1.7.3", "karma-mocha": "^1.1.1", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^2.0.0", "gh-pages-generator": "^0.2.3", "karma-sauce-launcher": "^2.0.0", "karma-chrome-launcher": "^2.2.0"}, "dist": {"shasum": "06458dbfb789366d703e308f180e0db195b7f2fa", "tarball": "https://registry.npmjs.org/ajv/-/ajv-6.9.0.tgz", "fileCount": 92, "integrity": "sha512-VsK2jpqRno3Hn+at4NGtBRpR5q3OW7n5INrTKqENDNQJB99DXATQEVHlnoD1BA7Uo/qGO+ijGA/vgSAlxP9E4A==", "signatures": [{"sig": "MEUCIQD59JM10nHbA2BxKhiSw82tG/XYBCrMKgqGDwq7/HoNMQIgdD3yY6hW9Q3wU6fsSdqSUAHsg7oxrwNgeIB0zQx8swk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 907978, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcX1KfCRA9TVsSAnZWagAA/KkP/RioZyKqGxOOQciyIz9L\nNwcfs6bLLEUHihIXXooCVai1Id4YYJApvHjYI4GOoPul630b6QpRt+DVd3aT\nRVwN2wSCXNGAlz2/vHMnAAvcn0Mk5NcMf7HW69wgsr25iR2cuoNNC1Zn+Hm9\neLS8iRTGla6nkESPVvaNiYZfN46GNM4gPO7l1JBWQoY/BCDtOtGeDODbdsk+\nSA56T/th+X4PZFxILQcbQFshyjGm/WyYACDEbn/ocIYsyEZxa0ztjYZZ46xZ\nxFfujwC7zciXrI3BruoL8OW+RPegSnfRdI2mQeghUL3ZV82tdMZ2GdfxTFph\n74f1sYk4tOqSwNhY8jnZjP9UZ2eeog83ehkSvYGTY6/apVosQ9GYU5EVmG9X\nvLkM512bqFUE3x8sE0ni54cyZzynnFOJrysllfNNOLgjXc3LEKcGHx4kjM9u\n0LJzjvJAujVcyG7eH+RQB7/VRwiC2QW8HAnYZRWQPno/2Is9DFPrnqFJOQ6o\n71qIIZwnks/3npM+zVnCpEq1oxuVUIwht/bE4nyxlcTRSe89k4Vc1NofgiHY\nYsvPiSsSFZxO47w9VRQrjQDKWG19ziCGaAM6oHAxuWqW0IsZFotvxiT+rRlT\nythYO0Sxv82bcHgJ9t7amdqt2xcbZrXpvM4J+xFF50zGeoNBQfcnHBo+fGWQ\nsOqo\r\n=KJP3\r\n-----END PGP SIGNATURE-----\r\n"}}, "6.9.1": {"name": "ajv", "version": "6.9.1", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^2.0.1", "json-schema-traverse": "^0.4.1", "fast-json-stable-stringify": "^2.0.0"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^12.0.1", "brfs": "^2.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "karma": "^3.0.0", "mocha": "^5.1.1", "watch": "^1.0.0", "eslint": "^5.0.0", "jshint": "2.9.7", "del-cli": "^1.1.0", "bluebird": "^3.5.3", "ajv-async": "^1.0.0", "coveralls": "^3.0.1", "uglify-js": "^3.3.24", "browserify": "^16.2.0", "pre-commit": "^1.1.1", "typescript": "^2.8.3", "js-beautify": "^1.7.3", "karma-mocha": "^1.1.1", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^2.0.0", "gh-pages-generator": "^0.2.3", "karma-sauce-launcher": "^2.0.0", "karma-chrome-launcher": "^2.2.0"}, "dist": {"shasum": "a4d3683d74abc5670e75f0b16520f70a20ea8dc1", "tarball": "https://registry.npmjs.org/ajv/-/ajv-6.9.1.tgz", "fileCount": 92, "integrity": "sha512-XDN92U311aINL77ieWHmqCcNlwjoP5cHXDxIxbf2MaPYuCXOHS7gHH8jktxeK5omgd52XbSTX6a4Piwd1pQmzA==", "signatures": [{"sig": "MEUCIQDGAT0DkdSV7IPHNM+pET2YjZR3R7dVycVwvHyzsmi9WgIgIrJIkyFd75gkOSi/idvAttGG9jZDfQQWThzDwTSRfIA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 908183, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcX+P1CRA9TVsSAnZWagAA+L0P/j7H6scmYx7gXO4ajAXD\nOZNIHU3wb9wRxzEJL2YLexSvH86QJJqqVZC3RPkhvWMF0tJ5ulEHdylRx5+A\njabAv75Ceolc02mX+wUczS2Ye8J4CcLiyqYkx6cxFwp+45HIqNfUUNzWZ5t2\nT0tjO9/0X+fbQNoWPu1et0Hbc92JRoOCRJbpWWNz0FKhRxLrOyUMvO+570Ur\n2G7rdud03KdjEnFU81WNxNN0lcUl7I64NgS1w2w7Z3PqOM1fD4GLA1uKMc6o\nbZxnsbcKfmC2iU9z5N4wMKkuvjYwxa2LteyVk67PEChs4g48ztoPfQSF4Iet\nkWMDgQrBzGGodUjkxGb3msVw5hdLTkblAJiSDbkIogHgjQgPcKLvXWR712T8\nx57aFhhDOPeFduUoOyykloUiRSsb9Oa26Xd+UKBJNyXOhXf6uPHrLgyKG5Dt\n3XALjQj08UnF8hoGH4h1m+gvpvCpJpe2OFZjEEhABHz9Yi+LaHDOhi2AOlgD\nAiFJ+0RdeTtDycx5NRSiVMrMYpZf42JPlH+hsa5gAfAZiRe1osVe2FuwX8Ot\nN70ZRy12PqSBFtKpyh/gSu4uzufm4piFie88Ki2OGR7ELQeRV46BEYqJj/Tm\n4aH4KlTm1mV9poKnKLXdlsdCC/CPaKEic4rNxk+cumkhZFt87PpVRs3F/fDU\nZu6q\r\n=3VVf\r\n-----END PGP SIGNATURE-----\r\n"}}, "6.9.2": {"name": "ajv", "version": "6.9.2", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^2.0.1", "json-schema-traverse": "^0.4.1", "fast-json-stable-stringify": "^2.0.0"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^13.2.0", "brfs": "^2.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "karma": "^3.0.0", "mocha": "^6.0.0", "watch": "^1.0.0", "eslint": "^5.0.0", "jshint": "2.9.7", "del-cli": "^1.1.0", "bluebird": "^3.5.3", "ajv-async": "^1.0.0", "coveralls": "^3.0.1", "uglify-js": "^3.3.24", "browserify": "^16.2.0", "pre-commit": "^1.1.1", "typescript": "^2.8.3", "js-beautify": "^1.7.3", "karma-mocha": "^1.1.1", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^2.0.0", "gh-pages-generator": "^0.2.3", "karma-sauce-launcher": "^2.0.0", "karma-chrome-launcher": "^2.2.0"}, "dist": {"shasum": "4927adb83e7f48e5a32b45729744c71ec39c9c7b", "tarball": "https://registry.npmjs.org/ajv/-/ajv-6.9.2.tgz", "fileCount": 92, "integrity": "sha512-4UFy0/LgDo7Oa/+wOAlj44tp9K78u38E5/359eSrqEp1Z5PdVfimCcs7SluXMP755RUQu6d2b4AvF0R1C9RZjg==", "signatures": [{"sig": "MEUCIQDmAE3XAqMaxuTvq7pOtBR0Yf+RORYHUKHr3OiWSffCtwIgXXHuhAr63je+bY5Fjsohw6WPByujkwBCW3PFVVi8m7o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 910208, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJccFigCRA9TVsSAnZWagAAdoUP/i8mJDv1HocDoEu3/6Jz\nUA0catB6ebIJmijE57tklFGSMFZq3DsaDGHl8lsZrT5qi2EMWDoRG/z6jVvH\n9B8fahN4G8pcItOSGKSyowecR6S3yPKCfQVq2bNBehykUr4AHeHMa7/4yWNR\nvV9Y5O9PApLje7zDQ+2Fj6wEO6LgIeXmKFGXhYIKHTRK0zfvHftfJo2XhNXY\n1rI+nMhHgo03nhMAEBrTCxEW3KotDuRhybaoUg8Lt7RoIN1JU2uWpjEKWyTq\ndSC5VQGKPlvClpinhdK89pzg+2QXe4ig9+I1cfd1A+0yiq6+WDlH4h+MzoVp\n04mx1+HVyIhXL5f+a0tLMHgbOitlw6AMW9a1bOxMqGEh3XXWWtENCwIVL/Bj\nesb/1m4wayI+IBLaQumLl2esPnWanAo2GQ2Cz/wvL9CiZTN/bCWXEvQnkSML\nrdy2IKzuNFNd9tPsBfs3tmWl5fcKndoeDN84qhujBQiA18ZqF4HCWEH7C7Sx\np+6xAGI16YL5Epc1UeEgLs2pe1Xn0frwkY8nv5KC13D9R+vtdzJQwftMVS9F\nrQZrs+y0VkQYwZioGrsQYB2ZpUx0l86zcuvRgfFuBO4vzxOdLZj4MllKgvHv\npD3n4lA2ZLpzFkcFYaMjBI/NPQhrKFXy9GyUXcgy8YYaVAsZ/i3B7i7fUjtY\nG+/x\r\n=4MAj\r\n-----END PGP SIGNATURE-----\r\n"}}, "6.10.0": {"name": "ajv", "version": "6.10.0", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^2.0.1", "json-schema-traverse": "^0.4.1", "fast-json-stable-stringify": "^2.0.0"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^13.2.0", "brfs": "^2.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "karma": "^3.0.0", "mocha": "^6.0.0", "watch": "^1.0.0", "eslint": "^5.0.0", "jshint": "2.9.7", "del-cli": "^1.1.0", "bluebird": "^3.5.3", "ajv-async": "^1.0.0", "coveralls": "^3.0.1", "uglify-js": "^3.3.24", "browserify": "^16.2.0", "pre-commit": "^1.1.1", "typescript": "^2.8.3", "js-beautify": "^1.7.3", "karma-mocha": "^1.1.1", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^2.0.0", "gh-pages-generator": "^0.2.3", "karma-sauce-launcher": "^2.0.0", "karma-chrome-launcher": "^2.2.0"}, "dist": {"shasum": "90d0d54439da587cd7e843bfb7045f50bd22bdf1", "tarball": "https://registry.npmjs.org/ajv/-/ajv-6.10.0.tgz", "fileCount": 92, "integrity": "sha512-nffhOpkymDECQyR0mnsUtoCE8RlX38G0rYP+wgLWFyZuUyuuojSSvi/+euOiQBIn63whYwYVIIH1TvE3tu4OEg==", "signatures": [{"sig": "MEYCIQCWspap5cHY9FjheD6FfXdQ6h6lgVz49MknefKdt1yAKAIhANwO0NurMS/lJmUfitQmS7aqPwtKyo0qJeVjE6N5a22a", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 916965, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJce7o9CRA9TVsSAnZWagAAacEP/3w6WeBk3jqRzafNOpMV\nJ5lSPFG4PI8tdopK6Vk9HMDn6Rq4aoS3mkvfwbOHOV8RYctTVjEmkmIxMzo1\nQ72ZvU7w4DOPE6JOd6YxRHAwr4W69gDoPdb3bjet9UM6kqQvW2URyiuTKWCH\nj59Cl4AWfS2x5tc2NbnUqEQlA6GIX+xwPu9mFQL+IocYXB5eY/EI6/3nXPc7\n5k9ctCYRtKS0lDTFJjIYkxOLWzmZ4tRH9bwGTeWfDBYcl1pDPXrVPfTyq8GL\nPJD9hqA+M2vq7oRZSX1TnU+OvGzKL4AZsp66UPbDEBe4YlSudVYZCnczh8lN\nvYwF5OMenEqbOCRL56PfpEWgz9nXSAhgvUQwXa6D1rR3oT+N/sqTvd0G+21p\nM2yHoQIPptfwnkH9Skh/qigNO3Vjm/TlsVaA2+RIXg3Th47462QlI8rzqLUZ\nmmFKPjl0FA2gw6qcv5VyHA+aHd1Y8tcUFsQMFwXuRHhECJjXWOTinfduMORL\nGMlxHFv+YfprJtV48j24ec+NXwEE+PEJprQVEN/OpN2PzRXvIjeLjtSensnt\nqa3dYk0MXgGA0kDSArbXuJkNNafVq8puPc9TOXu/d4Etn1yZaaO1zRpSkVKO\nQ8B1YgaFHVOsEvuQJFqM8ZXJDwzS+rJCFTL6tlVQpNj6989gCuMeataJht33\nvVhH\r\n=WXw0\r\n-----END PGP SIGNATURE-----\r\n"}}, "6.10.1": {"name": "ajv", "version": "6.10.1", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^2.0.1", "json-schema-traverse": "^0.4.1", "fast-json-stable-stringify": "^2.0.0"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^14.0.0", "brfs": "^2.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "karma": "^4.0.1", "mocha": "^6.0.0", "watch": "^1.0.0", "eslint": "^6.0.0", "jshint": "^2.10.2", "del-cli": "^2.0.0", "bluebird": "^3.5.3", "ajv-async": "^1.0.0", "coveralls": "^3.0.1", "uglify-js": "^3.3.24", "browserify": "^16.2.0", "pre-commit": "^1.1.1", "typescript": "^2.8.3", "js-beautify": "^1.7.3", "karma-mocha": "^1.1.1", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^2.0.0", "gh-pages-generator": "^0.2.3", "karma-sauce-launcher": "^2.0.0", "karma-chrome-launcher": "^2.2.0"}, "dist": {"shasum": "ebf8d3af22552df9dd049bfbe50cc2390e823593", "tarball": "https://registry.npmjs.org/ajv/-/ajv-6.10.1.tgz", "fileCount": 92, "integrity": "sha512-w1YQaVGNC6t2UCPjEawK/vo/dG8OOrVtUmhBT1uJJYxbl5kU2Tj3v6LGqBcsysN1yhuCStJCCA3GqdvKY8sqXQ==", "signatures": [{"sig": "MEQCIE/q57iNZMonPWMPWHv5+RKxxki50dwxBqBc11d0vva0AiAclGR4iNRAEu1RGDzXliDx3I+knvdpHKeu07e+QfbOng==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 911527, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdIOUrCRA9TVsSAnZWagAAiCYP/3VRKf98i1Elj84iPOU2\nILyn+DhMvypaiUShrErAYeJjwXJSh7mHm6924X2MisJk1i5WS5/kTvtOJVjS\nGPJ4Vb1jzMVpqgXktFVWHrOsNDjaaxhh1Fcwzs4fO8qxXhgkJZZ06P8eYIKW\n9U5nMEW2BP+xgqwTAm1acpBevTZzSpC+cQ4ltXxtP1Vix0Lc5FY3fpDwbWVm\nbmCwGlPyWEuX9X9uWY5Baj+QhgnggrbbdFHhsiNunPsAqm2W63K72armEbnk\nnd8uMTneG3CM4AnHgnTpb6dauokxWsY/lcZoHGrjbCE79t80tTTsRmWWC7eo\nxaElIakuifJo0N45OgKQ5FM3TJ2IhtoBcT9nq6UCsNA8KJLBALvocif7t4ri\nf9Z6eT15vvZh9ygPX/syQB5mdwL8FhWeyJ6H1JTVcJOfmk0myhzgjpQL39xU\nVL1ZJPigE7EVXxliMimCHAD9cCV8sI9c5fQ1e3fQt7EjfaI/Zbqn1PARIz9Q\nVW9Ey/nOeSV/hasUrLcWVvc7HLd7kqQjAINR6ac66DQfpd2JbbvwF7OA+T+h\n+Az4I9cN7IQ00RRl9SeIB4odDwURDc8bOlDhXJqJg93gEqQ/UljCaeDbqvB3\nMYpzEExCHko4dDNTFCyUatphh7pbIhhDTMAT0d47bjZ1SDYlXljP5dH6iaMj\nDSQ6\r\n=FSfR\r\n-----END PGP SIGNATURE-----\r\n"}}, "6.10.2": {"name": "ajv", "version": "6.10.2", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^2.0.1", "json-schema-traverse": "^0.4.1", "fast-json-stable-stringify": "^2.0.0"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^14.0.0", "brfs": "^2.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "karma": "^4.0.1", "mocha": "^6.0.0", "watch": "^1.0.0", "eslint": "^6.0.0", "jshint": "^2.10.2", "del-cli": "^2.0.0", "bluebird": "^3.5.3", "ajv-async": "^1.0.0", "coveralls": "^3.0.1", "uglify-js": "^3.3.24", "browserify": "^16.2.0", "pre-commit": "^1.1.1", "typescript": "^2.8.3", "js-beautify": "^1.7.3", "karma-mocha": "^1.1.1", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^2.0.0", "gh-pages-generator": "^0.2.3", "karma-sauce-launcher": "^2.0.0", "karma-chrome-launcher": "^2.2.0"}, "dist": {"shasum": "d3cea04d6b017b2894ad69040fec8b623eb4bd52", "tarball": "https://registry.npmjs.org/ajv/-/ajv-6.10.2.tgz", "fileCount": 92, "integrity": "sha512-TXtUUEYHuaTEbLZWIKUr5pmBuhDLy+8KYtPYdcV8qC+pOZL+NKqYwvWSRrVXHn+ZmRRAu8vJTAznH7Oag6RVRw==", "signatures": [{"sig": "MEUCIQDcTWxaB5RlcXKqtcwxM/eeYSVkqPTpgaGS42vHarN1oQIgSHJ5YkdTLlfqPUfuPGECIe5vG6QRHCMKWQBNDAY8Tvw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 917050, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdKzkpCRA9TVsSAnZWagAAnmwP/33u+sU3tuv7hPmFgM56\nBY0KZK4ZQXRvOCBocfnKSYCrDTuqp2tpkIvO+NrjaGNZyII/k+1Vatrw7dbM\nRJ9PejJiHurFw0WplNV0rdN7K8/g/wN+bgqmH95lrVVRNlYDpUlFgR88VPzE\nm6aq4vRfuXeryvRUGV0VMc8dnHv0QVOp6iHLmeCaF2n8c/4ZEy1q56eStEy/\nTo7T67k8XapRKGcXdjlzhjIfRnJmwF8uiZhMuk/lblPQ/9eYoDFyv7kWM5X7\nOO0WAQNyjbtipB9carSjmAFmi57dE3mLOmrmhwA/Ynxd5bxszzrVgUDnk7aY\n+P971HqTGC3XP/r2RRq/Yr+4jd63vmoZLI4tKSVAlv5mxIfdX+CQtDdU+Lzv\nHJyc6ra33/hBZSy8BSPTdVyO8ZTU/Q7z/3wTFB6nsl8TjgdCYVhGlTGZZYB5\nBDX2PYpDaSu4iGrbpGHfY9HSlAgg2JX2jHfoBEgNLuC/O/PeH1lqGCM5MYfc\n7uG99/MQnFZSd/poLKz2maaHAbR4ydrqj/vi4RIn/27jQXcanEAkik6OZ9p4\nfjf4AfuQDj7cYPEL4gpvZ++Y8uY14QKaS1MOJLBxtS97OfoXIHTfZcbGDFs9\nxo87ifGakfVMfdAkE60LO7rLk1+OOFU++6ZAvHkUKl8klcOpg8wLmvxp6IFJ\nxoIQ\r\n=XOWc\r\n-----END PGP SIGNATURE-----\r\n"}}, "6.11.0": {"name": "ajv", "version": "6.11.0", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "json-schema-traverse": "^0.4.1", "fast-json-stable-stringify": "^2.0.0"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^15.0.0", "brfs": "^2.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "karma": "^4.0.1", "mocha": "^6.0.0", "watch": "^1.0.0", "eslint": "^6.0.0", "jshint": "^2.10.2", "del-cli": "^3.0.0", "bluebird": "^3.5.3", "ajv-async": "^1.0.0", "coveralls": "^3.0.1", "uglify-js": "^3.6.9", "browserify": "^16.2.0", "pre-commit": "^1.1.1", "typescript": "^2.8.3", "js-beautify": "^1.7.3", "karma-mocha": "^1.1.1", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^2.0.0", "gh-pages-generator": "^0.2.3", "karma-sauce-launcher": "^2.0.0", "karma-chrome-launcher": "^3.0.0"}, "dist": {"shasum": "c3607cbc8ae392d8a5a536f25b21f8e5f3f87fe9", "tarball": "https://registry.npmjs.org/ajv/-/ajv-6.11.0.tgz", "fileCount": 92, "integrity": "sha512-nCprB/0syFYy9fVYU1ox1l2KN8S9I+tziH8D4zdZuLT3N6RMlGSGt5FSTpAiHB/Whv8Qs1cWHma1aMKZyaHRKA==", "signatures": [{"sig": "MEYCIQDuCyA4ykEqv5hcpQVWt+XI04vjorqML58ttzoBaHRULwIhAPaoXKm9Dr52JenNF8TDeWxkjgwe0umOpST7APeEcPLK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 919270, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeIsuxCRA9TVsSAnZWagAAFUwQAIedVWzdcKLv9bbMD7jH\nKiPoJvRjiL894Dhbsh0H+fBlTd+xGIU0EQlQva8H/tHD/Nfi7ahIO21p7PnW\nT7Wv2EnOaal2hL6nMIPTQEU1thL0LqFVT531ol581c4mdjMa0REyp/qCQRCv\n979Mno1Fbpr2PewXnU/RlYfrfu7Hxof9Ijo67rNZ9dvEBo4+GzOYCgeBB+ap\nQC8AhvSKCn+LxqSGslbyGpEJZIMgO9YSNUT9L03s3/9s1QU0rFV7IFBIvLQW\nm800bN/ATsVbj5VpqLtAR2yeMGpvZ5S8kk2bI7KjF+w1d4LZJIGFIK0Y6F+Q\nMqr3WErA/0tijcG1IS6giJTCCmMT5s1rqAxzhZ7G/IS75VLTyP2OLNXbK7L/\nOVSmThxslU/wrlbPRqCRXV9dggsJNdiY4qmRPGsq3M1YB68K+vJhYg8UWmKC\nwt+2nwsWZJ6ih/btlZqmoJiXi0PhiNApee6lRFrEZnx9o0RkGmkpECW7bW/y\n8YCmatKYZT103f5uYjIhXSzAOui6DFKFbjCPID6Cl9wRuVZ8uEeIR2II4rb1\nf1ioP6z+zliFEwcZxsKo00Coq4t6y3ugvX2jRHl3QhKHUSvSJ/cSeUiRGbvW\nBJNIgBCKNmlm3OEwi6P2u0J8FlkKZqv7c02Yfwd4dxEQPL79M5k0JdRcHC5D\n+YhA\r\n=wug1\r\n-----END PGP SIGNATURE-----\r\n"}}, "6.12.0": {"name": "ajv", "version": "6.12.0", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "json-schema-traverse": "^0.4.1", "fast-json-stable-stringify": "^2.0.0"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^15.0.0", "brfs": "^2.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "karma": "^4.0.1", "mocha": "^7.0.1", "watch": "^1.0.0", "eslint": "^6.0.0", "jshint": "^2.10.2", "del-cli": "^3.0.0", "bluebird": "^3.5.3", "ajv-async": "^1.0.0", "coveralls": "^3.0.1", "uglify-js": "^3.6.9", "browserify": "^16.2.0", "pre-commit": "^1.1.1", "typescript": "^2.8.3", "js-beautify": "^1.7.3", "karma-mocha": "^1.1.1", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^2.0.0", "gh-pages-generator": "^0.2.3", "karma-sauce-launcher": "^2.0.0", "karma-chrome-launcher": "^3.0.0"}, "dist": {"shasum": "06d60b96d87b8454a5adaba86e7854da629db4b7", "tarball": "https://registry.npmjs.org/ajv/-/ajv-6.12.0.tgz", "fileCount": 92, "integrity": "sha512-D6gFiFA0RRLyUbvijN74DWAjXSFxWKaWP7mldxkVhyhAV3+SWA9HEJPHQ2c9soIeTFJqcSdFDGFgdqs1iUU2Hw==", "signatures": [{"sig": "MEQCIE8j725Djn+nGZDh8WpNsRJd7/kWjjiMURopph4DXFhRAiBnbfj1itALHYBtwk6l+1Nzgnil4aBG0wZwxqgmwNKqZw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 920879, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeUTH1CRA9TVsSAnZWagAAypQQAIDpP/yXWVJGK+pkG4KP\nmxsRZQpDrH/i0H9TjXBg3/TQ5GJbSejECLJUTd3VNmg3hs3mTQ3Dm01jQxMc\nFXrOeqvNMo+lN412X8QBs4Eazb8YPlLnOnlnH4gOpePDA0GZc+6fUEAI7TfE\nk+ycrFWLKwUeZS9ySK2cZha7agSFgTdgomoaulGehr3gjYi4AEREuNqm/cIF\n47zPWvrE8asEBEmo9bVTJ/7mFGFlu26eVUypG9y2UbZ3gMfceBl/RdLAEJTV\nVPM/RwH4FG243looAAgUxdANhPTVT+zD++d+pbOA8i08owO30+PwMIN6ZqzI\n6WQcDnfbfHF0BMMxm1fUk7BZ6hC+m7aUBVgMuTccjcHJ+hVxZ3lvHk1lyQEi\nHP9emeCqyoK1czPkRczz0xrcZzLy4xJ3DjRy1yhHWI43e41CYnaMXZpEFDEg\ntqWDYU9kzryaVysuyJGZH1NFWGsi+x/ehtUzXByjV3EHUOEGHu2BMBakC6to\nDBTms0MJpA/j68Ptw7SYfFWWd4Qhg8pc28LpYzCsxOs9P8aBqjD/Y2HCr7Oo\nkdG5CLx8h5oZnqk3FUxciv2kS0JzMtcB3YKy9AazIkRmh4nCSLPmuGTWt3du\nj+krzgfZdsfQaWRCTHu44s8+CipkC3ZdoaJLgYzfTnz7N65l+Nqp/yim9i3k\nP4jl\r\n=7EPf\r\n-----END PGP SIGNATURE-----\r\n"}}, "6.12.1": {"name": "ajv", "version": "6.12.1", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "json-schema-traverse": "^0.4.1", "fast-json-stable-stringify": "^2.0.0", "opencollective-postinstall": "^2.0.2"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^15.0.0", "brfs": "^2.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "karma": "^5.0.0", "mocha": "^7.0.1", "watch": "^1.0.0", "eslint": "^6.0.0", "jshint": "^2.10.2", "del-cli": "^3.0.0", "bluebird": "^3.5.3", "ajv-async": "^1.0.0", "coveralls": "^3.0.1", "uglify-js": "^3.6.9", "browserify": "^16.2.0", "pre-commit": "^1.1.1", "typescript": "^2.8.3", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^2.0.0", "gh-pages-generator": "^0.2.3", "karma-sauce-launcher": "^4.1.3", "karma-chrome-launcher": "^3.0.0"}, "dist": {"shasum": "cce4d7dfcd62d3c57b1cd772e688eff5f5cd3839", "tarball": "https://registry.npmjs.org/ajv/-/ajv-6.12.1.tgz", "fileCount": 92, "integrity": "sha512-AUh2mDlJDAnzSRaKkMHopTD1GKwC1ApUq8oCzdjAOM5tavncgqWU+JoRu5Y3iYY0Q/euiU+1LWp0/O/QY8CcHw==", "signatures": [{"sig": "MEQCIHvxW7Ytx538Gi9hbb4SUbuZZJT5t1ltJLIETse2Z/4FAiAblB/O3wwZWOfDAGFvlWcgCNkIdqcXs8Fx/CpC5Gx8UA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 923762, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJem1c2CRA9TVsSAnZWagAAhlEP/j1bColyJ1oCZ6nHBGHM\nz6o5utyy2J7qp+hZASn1qoEVTJW3uW3p2stITRh1ExjUp6xoctVISQLBgm+T\nYwinycioBQTyQCtOesWL8DBkZGmzuUVrP/cqvwnqTGB63CBQisI5kEBQQEgb\nY/m974pL+lTZRJg7dFyT7Kzyio8bShSrYyQ667IZhTXOWWXZMJOap96wQp2b\nnAvxIll1R48/0V7z0kHfyrrd/9TwrQlBTXvHGTR86L6bjnBbnh2QQaKULxav\nfZWdJQKunyVkb082XkWpVQSI4Ei0Or06om3DTAgrx0jZxJ1sX1IpN/tHupSG\nP8+mqJcTvbBHQSXpn9Z7gliXKWambtv/Wv7VaFmGmxMOPQI4OW0Ci1f23uZn\n6ddGHyABnFMuDl2qwtWVQYzsiDW0i2foy8qvq7Bau7m9BubKopQFjmkuPkbi\nR+oWPCvRcKy5pwX8xStP0s5jvn0xJ6l3MqRKNMX1joTuv6+YrB42W0BSygs1\n/UR3TA7+BQ9MbsVy0KjnN5LAf7TleamWmsBrxZkYNXmO2/YGb0J8TrF6dfQU\nF0gS9T2Pj7p85TLC9XERNMAFH/Oo5vA5lByRKNAoKx4ZWXR6wJJCEtgDy+jy\nEbgl3+FGjAC4A/0kvHH448gajAOnE24uKJPE54SAZL+ClW2jFClgrzzemuCt\nxesy\r\n=ASYy\r\n-----END PGP SIGNATURE-----\r\n"}, "hasInstallScript": true}, "6.12.2": {"name": "ajv", "version": "6.12.2", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "json-schema-traverse": "^0.4.1", "fast-json-stable-stringify": "^2.0.0"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^15.0.0", "brfs": "^2.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "karma": "^5.0.0", "mocha": "^7.0.1", "watch": "^1.0.0", "eslint": "^6.0.0", "jshint": "^2.10.2", "del-cli": "^3.0.0", "bluebird": "^3.5.3", "ajv-async": "^1.0.0", "coveralls": "^3.0.1", "uglify-js": "^3.6.9", "browserify": "^16.2.0", "pre-commit": "^1.1.1", "typescript": "^2.8.3", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^2.0.0", "gh-pages-generator": "^0.2.3", "karma-sauce-launcher": "^4.1.3", "karma-chrome-launcher": "^3.0.0"}, "dist": {"shasum": "c629c5eced17baf314437918d2da88c99d5958cd", "tarball": "https://registry.npmjs.org/ajv/-/ajv-6.12.2.tgz", "fileCount": 92, "integrity": "sha512-k+V+hzjm5q/Mr8ef/1Y9goCmlsK4I6Sm74teeyGvFk1XrOsbsKLjEdrvny42CZ+a8sXbk8KWpY/bDwS+FLL2UQ==", "signatures": [{"sig": "MEQCIHw1sy77g8YEa8jmWZvx+lx5MP2Mk2f3ICNGkUTNPnmyAiA9Cy0zP3mVKsc7RJvpcajMDErSCpnucrJd3vPm9+qy1Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 923661, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJenNw+CRA9TVsSAnZWagAAr7cP/A6G0EypcfnurVXY+CIl\nmYD8pCwvjUa64E8PgN0h9y0OPzFhjzU57D4v/1q+950cmAHkU4VDJEPNnYdq\nBSXViGLKBAhjEM9W0A9mo5dC0Tqj0+sojGE1GamTQbCfZeqFrCsogRYK3+Ec\n12ynfBaYA5wAL5IOaaRSlpwmRPOCDjzEC6RLRyVmtnqFfSVZymAV473cnPk0\nVuwP5iLocanVAQrZOOYB78D+M6Y5mmh+LiDB/joLd2cjiRlmebcJQVR80/TV\nsh4r47OTCUixR0Qmmtns0R3E+IDs3F0x3trKMV77y+BwzgSzBVzSLfvXCkGe\ndvMZCG6qPfneP77hp2v16YCKay1rALYdQf1m7htZxicQHl+WLyyWOY1I8GG6\nBsauR+vCiwmF4loUbjlhwUSS42HCvrg0t9DHZAawp8ukwZ+MH59wsaESe+zj\noBRc1NEnI8CqFpp7raiSyfQHBSxwaIIxWCWz/YUiKoKfJn+uEgqLL7eALE7c\nRmqKuZ43xoa7YNrvALaBgg9WrdzuzfitFOK0zok08hYMPWFE22dFEg77mUuO\nPXJsBJSD1Ljkl8UZfr0ZKjuBkttaAOEbSJeDz6oAxLQ0jaTaW6NKSgGCUPHf\nq/HYeMSPc0jN8PWYcDdGX13gBXdCtqrVSvZx2Y8thwaxVLRXSs8ubhFXyVff\nFLNr\r\n=UP+u\r\n-----END PGP SIGNATURE-----\r\n"}}, "6.12.3": {"name": "ajv", "version": "6.12.3", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "json-schema-traverse": "^0.4.1", "fast-json-stable-stringify": "^2.0.0"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^15.0.0", "brfs": "^2.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "karma": "^5.0.0", "mocha": "^8.0.1", "watch": "^1.0.0", "eslint": "^7.3.1", "jshint": "^2.10.2", "del-cli": "^3.0.0", "bluebird": "^3.5.3", "ajv-async": "^1.0.0", "coveralls": "^3.0.1", "uglify-js": "^3.6.9", "browserify": "^16.2.0", "pre-commit": "^1.1.1", "typescript": "^3.9.5", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^2.0.0", "gh-pages-generator": "^0.2.3", "karma-sauce-launcher": "^4.1.3", "karma-chrome-launcher": "^3.0.0"}, "dist": {"shasum": "18c5af38a111ddeb4f2697bd78d68abc1cabd706", "tarball": "https://registry.npmjs.org/ajv/-/ajv-6.12.3.tgz", "fileCount": 92, "integrity": "sha512-4K0cK3L1hsqk9xIb2z9vs/XU+PGJZ9PNpJRDS9YLzmNdX6jmVPfamLvTJr0aDAusnHyCHO6MjzlkAsgtqp9teA==", "signatures": [{"sig": "MEQCIGzLUsHaWOWR67O8jFeedg52+iAQcOOP9qaTBe/+Hy/UAiAJWGedI2D29+vGvF4H7z0R6isEEurBZlxqhDN9hkMFIQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 923810, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfAKgFCRA9TVsSAnZWagAARSUP/2npvdus2Ao4WXe+MpO+\nKDzL0sOmJh7Ide6uAwvL06dBgR5HGNn6QL/qaBM9FEQ8wmbgDoRuVjJsCHeE\nzijZGiGgccmG0f3ZeoFI3OTdZEkzopWJ6M8iAbI3rrWR3O7kAkutpWS68lK6\njzc8Lwgvp0AorJoVv7Me8fn7j41qBzzmpjNwQx8dPKRnRnn5oEgAe7AtjauL\nDHMggEv/J8yC7U/RZn/gamTW3cqvyCfGuAhHecKpUqwbVxRrva862tbS1Noq\n3jiHW+f5IcrcEbtzRfde0vgCjJa5y31eupu2+S243EM/FZS4ROHb4ftE3zto\nW9I5ujy8X1LO+8n4802WK2baWCZhwDBcBPizwJYka5M9ICc7s8OsHJv8xQYX\nRHeHdpq0ARKT49y56tGSz5fr9XGwv57MTLXgFGxbyfy8uczLxfNPXQB3qsvC\n5zxvuxi+ADC60zxtvHFND9XW2rMLRww8XHnhiMV3zXKsD8lxExtaDbMFAUrr\nSwPU4FHaURMbMqafCUVEec0739WUYJ5xBSYHiSbvkc41sExqLOjo++cJftJO\nEhvmHDhVh6l5BTa+EylK/8cuKWh6nzgpJeKjiztEgsvLPYCGt+qk/2O46K9J\nM+e1WrtQigpOLn2AbNA4dlz5QeD046zu956XELQYJCkDdawCZQFJQWllat8/\nilzI\r\n=iCMb\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "6.12.4": {"name": "ajv", "version": "6.12.4", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "json-schema-traverse": "^0.4.1", "fast-json-stable-stringify": "^2.0.0"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^15.0.0", "brfs": "^2.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "karma": "^5.0.0", "mocha": "^8.0.1", "watch": "^1.0.0", "eslint": "^7.3.1", "jshint": "^2.10.2", "del-cli": "^3.0.0", "bluebird": "^3.5.3", "ajv-async": "^1.0.0", "coveralls": "^3.0.1", "uglify-js": "^3.6.9", "browserify": "^16.2.0", "pre-commit": "^1.1.1", "typescript": "^3.9.5", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^2.0.0", "gh-pages-generator": "^0.2.3", "karma-sauce-launcher": "^4.1.3", "karma-chrome-launcher": "^3.0.0"}, "dist": {"shasum": "0614facc4522127fa713445c6bfd3ebd376e2234", "tarball": "https://registry.npmjs.org/ajv/-/ajv-6.12.4.tgz", "fileCount": 92, "integrity": "sha512-eienB2c9qVQs2KWexhkrdMLVDoIQCz5KSeLxwg9Lzk4DOfBtIK9PQwwufcsn1jjGuf9WZmqPMbGxOzfcuphJCQ==", "signatures": [{"sig": "MEQCIB2Uetm8+SsvhLmDJ+sI9sju9WqfAgfKjs6AZsQHJ+AIAiA4a+gyz0EVHiqQG6kbnQOHNXaS4I81mPgRW51MepiK7A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 922968, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfN6XtCRA9TVsSAnZWagAA98UP/id3Zll7iD01lzF1vuxR\nZLDR5XgrsPeJzvz12wrQA53NlD1cXfizlErBbU+gMQLIWVyqdSV8Jk+pM046\nRlM529g4siIuSytl2n2VBr5IkCLYAR68aG3BqRzBVqp6NzRhCCMyHdXPo7Cs\nM0myHVjvefdYhOWzDnHIaX5ccZesluJKV2IL/tufZtm1yPunQZi3sfQ9A871\nYAjnetGym/pXciNEd3wzXWrs81ig+yxEJ/TqjAbIiD1u44yOaHO1+x/grcCh\nuJ+3IVfS6w2KNbEj4wAxeFrYOyIaTvvyELPVfZs+W/BPWn2Z1qS6h01aMOnT\nhxdoDMwoMOpg42jZi/3y29qgqLc+YesY810CSsLK0zdu4BtCYw+nw8I6vi94\n6Df3rjz0GJMqS8mT4c8q6EklYPKHoHbUDhmObjQnnV9fB+ojPGPeV/GYzwCD\nXY0dh4elozqd5pxnQQ3ydLY/E3AxiXFPeuMweAM1rMM0dT/13CwxEUnYZRBu\niqF1AxzRklO4+/qu9vvd9fxIcayPPE+0H2gQIEnxV2qzLA4u5lM0nk1utFy8\nfPIh8yhpq895zgIt5mxD2xejopVGMpOmlUe5VGJncG/TzL9SgoWfzJ1cY+9g\nJ/KFMzKYwPfUDjxhrmStVW+KlhyXEVEa3i2fvl0SaqBSPDyYh4YqDpNDH//h\nCBI6\r\n=Ybfj\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "6.12.5": {"name": "ajv", "version": "6.12.5", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "json-schema-traverse": "^0.4.1", "fast-json-stable-stringify": "^2.0.0"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^15.0.0", "brfs": "^2.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "karma": "^5.0.0", "mocha": "^8.0.1", "watch": "^1.0.0", "eslint": "^7.3.1", "jshint": "^2.10.2", "del-cli": "^3.0.0", "bluebird": "^3.5.3", "ajv-async": "^1.0.0", "coveralls": "^3.0.1", "uglify-js": "^3.6.9", "browserify": "^16.2.0", "pre-commit": "^1.1.1", "typescript": "^3.9.5", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^2.0.0", "gh-pages-generator": "^0.2.3", "karma-sauce-launcher": "^4.1.3", "karma-chrome-launcher": "^3.0.0"}, "dist": {"shasum": "19b0e8bae8f476e5ba666300387775fb1a00a4da", "tarball": "https://registry.npmjs.org/ajv/-/ajv-6.12.5.tgz", "fileCount": 92, "integrity": "sha512-lRF8RORchjpKG50/WFf8xmg7sgCLFiYNNnqdKflk63whMQcWR5ngGjiSXkL9bjxy6B2npOK2HSMN49jEBMSkag==", "signatures": [{"sig": "MEUCIB9cUr7giGV2/NfVWnjHToe42+DQtUKGQDKRmPtlY0O0AiEAvjPnvQRXL814nTcq2Ap1rh4wf6GLbUH7ztAGFCfFh0I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 927868, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfXkd0CRA9TVsSAnZWagAA2oQP/2+LZMv7ZpiOSFefAjXC\n7bmWS3cgZ0S7KyZ+L7JcjalJtFtrsebQQ6g6f4JD/8vnbplr2bFgJ7e6Iult\nny1TlzpWkDRdN1og5b5X+nDImqBkjRqjHyC9jG626Cu36vKJnQjN+oz18bnO\nRjsTzqb+7+NY08oiXoPDRGPGk9vnaZzaGtKoxMFSuTTPKJi/HYGQrDf952fN\ngnphNY3TPAWDcE1BdaUGZVsdkoU59zuIFPwJ2NtSRC95JzzI90bbBtqpJgO0\n9SMRuJhk18AN81cSdURyJY33H8Q04M9/4G7D5lgnkosoZ4qr7q1i8WPj3Dsh\nB57L8aRH1jbXc59zDVOT2o9R+AzdHb6AaUO0Yef0TjMPdEBGYSkZVBhYh6iH\nC08jBEbcv0HzS0V6JvNwotzEYX9m/4jLZR/6VJo6eOB3H7h3mkmSQ1J8F6Xq\nbf5zVjjKIRavacTgR2zyJ9qGrs5+c2BsaakQjg0+qBBmEwlY15gSJEH8krVE\n/SPO8iC/o9H/S20ySnE3vXh85x1X1C1LRJ3eF/bnfLaIpfxo2bPdVVjrI98h\nQGKzsnZ015tgSQN7kel2CorBvZJcQURo2BdRnUo2ibt4NnHv69wHKxOO0f1X\nplI8r//0Me8c59VxSbdiEN2NlNfxzySPFqgW5GIxAcqRut5+6DuuLPTWFGPD\nMHOJ\r\n=8Dh/\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "7.0.0-alpha.0": {"name": "ajv", "version": "7.0.0-alpha.0", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "json-schema-traverse": "^0.5.0", "fast-json-stable-stringify": "^2.0.0"}, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "husky": "^4.2.5", "karma": "^5.0.0", "mocha": "^8.0.1", "tsify": "^5.0.2", "watch": "^1.0.0", "eslint": "^7.8.1", "terser": "^5.2.1", "ts-node": "^9.0.0", "prettier": "^2.0.5", "coveralls": "^3.0.1", "cross-env": "^7.0.2", "browserify": "^16.2.0", "typescript": "^4.0.0", "@types/chai": "^4.2.12", "@types/node": "^14.0.27", "ajv-formats": "^0.2.0", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^10.2.11", "@types/mocha": "^8.0.3", "if-node-version": "^1.0.0", "json-schema-test": "^2.0.0", "gh-pages-generator": "^0.2.3", "@ajv-validator/config": "^0.2.2", "karma-chrome-launcher": "^3.0.0", "eslint-config-prettier": "^6.11.0", "@typescript-eslint/parser": "^3.8.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "4534c309d621a570dd4644b46129227e533516c8", "tarball": "https://registry.npmjs.org/ajv/-/ajv-7.0.0-alpha.0.tgz", "fileCount": 250, "integrity": "sha512-Zbe5fRhojt5dN8shJ2cPJftrvJBnrRvPZEw+NQT72XX/yZ81kDc52hGqI0Kw7VBaWlyUPAJTdkAG/Hx2hSpXkA==", "signatures": [{"sig": "MEYCIQCPFud4TU8UWm1DMbGfRN1EY/MfXbvL0sj2KzZBd7HTxQIhAM7NMl3KVu+HIOEPJ4yApDjZTO1qGyx+9lGV6qnq7e6N", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 594593, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfYNaRCRA9TVsSAnZWagAA+D4P/0R0OufSH5U+ZirtwhTE\nQtULT2vbshO0+bNcg8MZ3dDtctLGSEpagX1QFhmY5PZ79IW7LZgZlj9vgYa2\nJ2jj7kDERruX7L0GsDCkw+coYf7VDZCwKR7y1uzaXnbDnJ0jvaCRg7hKLsHg\nEhH8ywuwM+f/KQHE0+zk7vFjwHz+nfrnnR5kHRknn3dOeiV6p+wbnAkMJdCC\nCpxjAfBXQFTpvTgjGfzY856S0IEW7WOwR9kw6EWuHvM1lVtOgyxJLx2WMMIW\nu88lq3ev7zk3j4osYZrjfoZNCya741SvxP4GsADiVCkHLPxY5uonMhMnTgJ2\nmND/L8+2KYzPN1FRl4Pe57hAFlPy+CcWchX/MaFowdpUX4fvEtyAtB30BsyF\n5a9eoCeNiQB3DosbXB3GIj6sLH+/UMOe13PvEIJUL9GwmO73fn3yHsQgL5q/\nYEosX4qdPtuKvYAmMtrbxw67HUcFH9/UchPgnsA4p4icOAm9v4LpV2/3/Sou\nKJDpU/jhJbvddGKP3sJbuEwhz1YYT5DgWanycz3Dd1aFexkQ1hy0Hcobd8Hu\ntb0yyjEt7lyD+8h9hJszvDrnCKv5ADPkpoWIMh4Zw6Lu2gk3g3OIwU9bYMyG\nxPyLBdP1KzAK/TkQf65TtBvwu1o4xhW8kPHK17T19grA1aCSDgCs9uDePHTo\nGke3\r\n=vtbO\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "7.0.0-alpha.1": {"name": "ajv", "version": "7.0.0-alpha.1", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "json-schema-traverse": "^0.5.0", "fast-json-stable-stringify": "^2.0.0"}, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "husky": "^4.2.5", "karma": "^5.0.0", "mocha": "^8.0.1", "tsify": "^5.0.2", "watch": "^1.0.0", "eslint": "^7.8.1", "terser": "^5.2.1", "ts-node": "^9.0.0", "prettier": "^2.0.5", "coveralls": "^3.0.1", "cross-env": "^7.0.2", "browserify": "^16.2.0", "typescript": "^4.0.0", "@types/chai": "^4.2.12", "@types/node": "^14.0.27", "ajv-formats": "^0.3.0", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^10.2.11", "@types/mocha": "^8.0.3", "if-node-version": "^1.0.0", "json-schema-test": "^2.0.0", "gh-pages-generator": "^0.2.3", "@ajv-validator/config": "^0.2.3", "karma-chrome-launcher": "^3.0.0", "eslint-config-prettier": "^6.11.0", "@typescript-eslint/parser": "^3.8.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "19d5bcec514ac4f31125a68e2ecf6f747e9fb0ae", "tarball": "https://registry.npmjs.org/ajv/-/ajv-7.0.0-alpha.1.tgz", "fileCount": 250, "integrity": "sha512-vOWutHnEGQJ6AWg7JFil9yhZ0OJQMHkCbUJrrDWcnHYcUMBHkxf+5mCAU16dCz3eZh3Tkaf/JsyFVbTQkqjzNw==", "signatures": [{"sig": "MEUCIAaSswMYxyB/2YnUSlu241Ua/FJp8MwDGwF6dW3G8OcIAiEAsYfnnuztl8+wDA9Zg9GRqmP4d8glsr6ax4KVhYXcJx0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 595851, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfYhXHCRA9TVsSAnZWagAATGIP/2m6xiuTGIwORcnwkbhW\nyDjojpmM0pdjsbIGxtuo3+VwpUtdoRLZqb3HdN4P5/hBW6XDyU22C0KuXQvu\nhApKLUJ12ZH7JaEUeB3uWozP3ppTpS9c0TCoIp4T/PHfFZriiXyuPgavlfG9\n+yZwz/n+I63rqxrKzUNCHzH+9AxjVrt6mbYDAka4AzJqapCvoIqEdRRuB7RB\nGPutS/az2qcu1V4/wg7DePgg8A3gOHpqPGy0mCRu//5qKR6hRiR4+mqu0gpM\n9NaRe7DduFcte4kyQGBzF1FZy5jprXgjkdCVmaijWpJZOz32hwhio65JQT++\ntfLS1tK3w1CeLIN/fnFR8MZO0HjR+5Q5eww5sUK/88SS4SVDQJh18TrQxo8q\n9eiQkMgn8OUV4blLCoKLCxRKlL73ku3d3FR86R/lqBPdBwEMAJyaNC6MLTtR\ndEZOXIrKVoZ9c5zzrz1+IOqupnvU6YYdHBKTNdHHA2IDxtEYbr+6r+HD9GG9\ngc5AoDpZ/mFhMU2kW0RLgSYdBkhvkDFRglbHASWV5oHfUnt3MkFU4piGEEIU\nvD31R1p48qPq1iyDSIF5v25q/iWPJ4yFWxu6wcQP2Z96tRYyrWDEZGTHXaca\nLc5NJZYnnrCtrGV+IOwBFhsU4EM2nAkJ64iM6idiN8medWg4o9SCmpActIPb\nVGec\r\n=kyiv\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "7.0.0-beta.0": {"name": "ajv", "version": "7.0.0-beta.0", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "json-schema-traverse": "^0.5.0"}, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "husky": "^4.2.5", "karma": "^5.0.0", "mocha": "^8.0.1", "tsify": "^5.0.2", "watch": "^1.0.0", "eslint": "^7.8.1", "terser": "^5.2.1", "ts-node": "^9.0.0", "prettier": "^2.0.5", "cross-env": "^7.0.2", "browserify": "^16.2.0", "typescript": "^4.0.0", "@types/chai": "^4.2.12", "@types/node": "^14.0.27", "ajv-formats": "^0.3.2", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^10.2.11", "@types/mocha": "^8.0.3", "if-node-version": "^1.0.0", "json-schema-test": "^2.0.0", "gh-pages-generator": "^0.2.3", "@ajv-validator/config": "^0.2.3", "karma-chrome-launcher": "^3.0.0", "eslint-config-prettier": "^6.11.0", "@typescript-eslint/parser": "^3.8.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "6bea4bf29c9ca253d7de11d8cfb869026381c645", "tarball": "https://registry.npmjs.org/ajv/-/ajv-7.0.0-beta.0.tgz", "fileCount": 255, "integrity": "sha512-7hwYZ5gefadrdKkDNYzOSVtHsZR9I1wOn8Urve2xA9aBnT9aijBDoRl2pu51Wpz/iSLP6EULgGxog6yjCki/GA==", "signatures": [{"sig": "MEUCIGIpqqB9z09XenvadvRQgBBMJ1oP1iI8lk48eT2/Ihc1AiEA2Cv78i7p8CG6g+tTQle/A8k2TEm6EjrnlVYbNw2kLP0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 690746, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfa58oCRA9TVsSAnZWagAAm/8P/0XlKYf9Mrl1e6UOwHGV\n0Ru0MdorXd5ScN6AMWfc+hfqatSPeQKi/E1SX00NGXQlFI4IxibMYPTuqTgG\nbYj+IyhPvkd7yFOxdHE4wIhb3GjhZK32Zhjj2b/i5Qk82E6rZBzfG7Vhbg5R\nkYuUjqjDgtI8I2vjV/zhtYq+mCCRPn9vQIlHXLH5DnPz/FOwpRcyGkgG+1az\nfRh7sIaq4I3gn9dKStYqH2i9l7zB/xQyRtYInOc5MEuPe66v+nwypNXbDl6E\nm59r6Ep4cq8gF+gTRvLI6aaCSIpQCEUSdnrFTdn1cnNp4lL6Pu/oXDaUZ7hJ\neflJ+PPT/5pK4MtzIIKFaCLUH0RTp32PaKdgj2L1frr3baX/4/6JMVcsMSsx\nkffgzQP2qhgstBoxqD4uSBHHISI5MsrDrPSq3Fzu+o/v8QBSuwhchbC0A5Gy\niz+v2LX0krVW2qanCaAzUHD4kYeDlxpadLZVSz4Eg7RB1jn3e8rAn1ihqCFD\nNe9MeQIEeXvRp5Ur4dnrTwdiqRboX5d+9RD1vGfMasrWweNq48xA9NjS8g7m\nHy77ed4mTCeKjf5ipSElyGm+5DwAxVuOunZumVVXHWiq4B3Rbc6OBbzXkvrU\nIYQtu8OXwSQ0QXN+TtFL5x4zmLUrXWVRcXqZNk3SeHOg9ql5c53Y7JA0IVLK\n6Dx0\r\n=PCmN\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "6.12.6": {"name": "ajv", "version": "6.12.6", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "json-schema-traverse": "^0.4.1", "fast-json-stable-stringify": "^2.0.0"}, "devDependencies": {"dot": "^1.0.3", "nyc": "^15.0.0", "brfs": "^2.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "karma": "^5.0.0", "mocha": "^8.0.1", "watch": "^1.0.0", "eslint": "^7.3.1", "jshint": "^2.10.2", "del-cli": "^3.0.0", "bluebird": "^3.5.3", "ajv-async": "^1.0.0", "coveralls": "^3.0.1", "uglify-js": "^3.6.9", "browserify": "^16.2.0", "pre-commit": "^1.1.1", "typescript": "^3.9.5", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "if-node-version": "^1.0.0", "require-globify": "^1.3.0", "json-schema-test": "^2.0.0", "gh-pages-generator": "^0.2.3", "karma-sauce-launcher": "^4.1.3", "karma-chrome-launcher": "^3.0.0"}, "dist": {"shasum": "baf5a62e802b07d977034586f8c3baf5adf26df4", "tarball": "https://registry.npmjs.org/ajv/-/ajv-6.12.6.tgz", "fileCount": 92, "integrity": "sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==", "signatures": [{"sig": "MEUCIDkPRWEQ/1xKKRlM+xlc9LGcUY+TnlcRMLKDo9GML2A/AiEA/+QmhzkvJWIPyOj7OL175pm72Hst85SfxBAt+rGSmzI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 929154, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfgej1CRA9TVsSAnZWagAAR0YP/jHA0M1PzBq8lWSpR5ri\n/7QS2sKRHRXJWldbw9lQNe2bIyCbFdzWfCTiDZjWv8g+8/CsJ/PkwmdAoCS6\nAIKXIMrPMFZ5lG6uNSX+pzPtaS2N/E74iaofRgkN1hTqb/5P1o8Dxsbb7SG1\nuxVUFnW+RzKEzWVji48/bCPmSvGoGl1fWU75aHbSwET5YA5hmNL1A6jbBffa\ns0tNJocggD3tZLKYzw5CbqEwsSLmzOmBg1HIhFUDtTelzDo7CCRAUU6rPdHp\nGbeR9KjsUwQstAiwukLUWBAmkEG55ACaS1FsZ9DDy7sny/g1ohcUXZVaC3tb\nYiNhkh5X2GSqCGaN8qmgmPezFtCfbQe8+oQk4i7jnxUnqujJOOSKAmThhER1\nWII8HaTfUYQj5cUWLc/Te0d8HlJwVyTFmzTPjE58zQS5xyIXhOLn/jGgP2Qp\nP4t1S7s8IrnovefPhCj1RSwzbJRYkU/XRr4mvYpwMTn06TE1GhlN6hkzjfL/\n3noaTGYXgxPzJ5LCjmhINan1BrqIx7oUyeAwPsevz5TnEignDl/NcdjhOnXr\nuyvfU8arfygvmzacp+1XMS2b5w+FLbbqrh+s1SI4iJHBxamhxE5R3jbqovXI\npClzWbOwOrZ2lzNBiojI3vacgk0LHGVy8Zhn+8jbhlkmIrTiiqMTVDBN9UvF\nGSGr\r\n=Vg+s\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "7.0.0-beta.1": {"name": "ajv", "version": "7.0.0-beta.1", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "json-schema-traverse": "^0.5.0"}, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "husky": "^4.2.5", "karma": "^5.0.0", "mocha": "^8.0.1", "tsify": "^5.0.2", "watch": "^1.0.0", "eslint": "^7.8.1", "terser": "^5.2.1", "ts-node": "^9.0.0", "prettier": "^2.0.5", "cross-env": "^7.0.2", "browserify": "^16.2.0", "typescript": "^4.0.0", "@types/chai": "^4.2.12", "@types/node": "^14.0.27", "ajv-formats": "^0.3.2", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^10.2.11", "@types/mocha": "^8.0.3", "if-node-version": "^1.0.0", "json-schema-test": "^2.0.0", "gh-pages-generator": "^0.2.3", "@ajv-validator/config": "^0.2.3", "karma-chrome-launcher": "^3.0.0", "eslint-config-prettier": "^6.11.0", "@typescript-eslint/parser": "^3.8.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "7427b34592edaf2eaa97655ec398e5d17991b817", "tarball": "https://registry.npmjs.org/ajv/-/ajv-7.0.0-beta.1.tgz", "fileCount": 255, "integrity": "sha512-rKX+VjvPBBBTw+fXQ1b37Z9sarOr49/1QP5Clb1PSy/f8pKiU/+cQ2uBIFtX3QanL/3/LW+mKiw/G8gwRdyTEA==", "signatures": [{"sig": "MEUCIQCysd9B4O0F/87aH1U57e7JfoVBZIiyXqiFDInyz+l45AIgfE4CYZpAJkUgcZwCIhs2rtTLpv/42Y2gFGHCNIyxH4I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 738367, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfgfoTCRA9TVsSAnZWagAACS0P+QCqwKJSfn22MXvmNM6V\n4srVm7ktrXU1FJGyw96wMusIp0KpYs/Pezy2HDXUV4ArH6fDoHiAaXfBwsYh\njkL+LYs2psWX/57Bd1jpzTAAWEvmh6Hj/Ep7zrbSdmia6uzf53jITdgEKE68\n2xuuE/B23ChYaA/gTyQUDS/h2zdgXGmsuP2j+94TX0Py+FMP2zfKS4IeHFOE\n+rJvmCA3X32SnRTq+lKskRB2JRfxLFzcB9co1IHnRDWLVo8ceb/yLKdsjAEb\nPV9BydxFOPK659ckLjYOFfoM/vQN0MMJJDq8aLOKjzmzgVE5uUJjdpo8IC52\njs4TOcZZ+Tli9qH2D2dFfX6vulXXhnRVHhvh8x7ZXMwnMzzlZPs2Du42Y3Ca\ny5pU2e7NHb0koOQAbpENvw6+n3ZrlyGLzEFEcEkwZkneR3Zj1PExtzvtsS5K\nGcDi+Tx1JB+Lkf32TtMBYqFItq78zyX3KP8qAZWYKI7Yum4cUALJVmT3pYdl\nGFfmTRLWhJ7d2qnZROlTL2dLsLMWqDn96RA/KVHo/AZ8va3xNu92rB6CdG4a\n158PzlxfMYS/CsS/OSXa+Ck1jYpu/8iBInHNqDts5bhfMTrU1sc/WWquhhv9\nX4/Ep7LNgWlMSZcov6WxRRrLWYlLADYlgHd0OBMv8FniXtwE6CJohVckde6/\nDBgm\r\n=VDyd\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "7.0.0-beta.2": {"name": "ajv", "version": "7.0.0-beta.2", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "json-schema-traverse": "^0.5.0"}, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "husky": "^4.2.5", "karma": "^5.0.0", "mocha": "^8.0.1", "tsify": "^5.0.2", "watch": "^1.0.0", "eslint": "^7.8.1", "terser": "^5.2.1", "ts-node": "^9.0.0", "prettier": "^2.0.5", "cross-env": "^7.0.2", "browserify": "^16.2.0", "typescript": "^4.0.0", "@types/chai": "^4.2.12", "@types/node": "^14.0.27", "ajv-formats": "^0.3.2", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^10.2.11", "@types/mocha": "^8.0.3", "if-node-version": "^1.0.0", "json-schema-test": "^2.0.0", "gh-pages-generator": "^0.2.3", "@ajv-validator/config": "^0.2.3", "karma-chrome-launcher": "^3.0.0", "eslint-config-prettier": "^6.11.0", "@typescript-eslint/parser": "^3.8.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "709980b7850183a027631160c01954409c05cfac", "tarball": "https://registry.npmjs.org/ajv/-/ajv-7.0.0-beta.2.tgz", "fileCount": 255, "integrity": "sha512-QNYgGgTZ370cYyxsIpQWt8HG7fxBlYrb3wW/SVxsAJ9qqVh8bmbszwNWut93EEhK+rTOY2IKj+SOQRo/7mvQiw==", "signatures": [{"sig": "MEUCIQDj+BDeichbcl7iIjatpAqBz+ncNYf6zs2DXtxgCvWE8AIgAOdXs2DRgrPyKOVHJKyF+KDcVl6yeJN3l9H79zJeLXs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 732099, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJflG8DCRA9TVsSAnZWagAAge8P/iVGNEvLlGQ9F17X43Br\nCMV/muJDLIPuwZelOmCuMUTl0Oj2zsaGdhkWHAgIZUQsvrkBqdGPiTFwv53c\nzqt1AkX7ov9a2gHQWtfjaJcMB5rglB1PsH9f2g8WrIZjbz4PLpmfhQviB0fO\nLCLABl8sw0JmfQ/JyhVAdM0aUdPPzdGIMPE0u0hbhwEPBzoy63rlfOTWz7sv\nXh4eg4wSbn9aKsf/VPTc2rOY6sABgK2o/KlyrAjLlXzenF3hhQguWCe5oijz\n1LLw5iyzxar/noWf9WCXE/1V/dStbWQAwJ9uRcEzAKe3Zw5vkX/48pJqpQuJ\n0MR8i6y+P6LDdQJ0ZUMDlHpIiXfAPM7jTTi8Fm88J8oJLnHnnldCtBDuEj98\nhL5w4kqPrePlTyau8MjUu5twncLJWcAnIPEtM+NECsHvFLlMILQBjxNGzUSg\nVFKmtKGX4Th5wxZop4/zssZ5lhazB2mSs2FvXvrcB8bFUdgZbLGbs4hjjl7g\nn3Sh//GmTpSrh/5wGcyzjKkJq9McBO+5ZjEn0T1zDe1fQFZrI1pj+wCNmDWo\nYRc4htL4d9PynPSQZ0K9FkF8pGbBwMbXbisaLxJEyCUwg0vnEcvRr0ildRB0\nk+UEHvKcf263vkDTbaQYXjl0PZTtb/5zRFi5MeiIVXgN92WI25D9L6FpsO8b\nlECF\r\n=gMrc\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "7.0.0-beta.3": {"name": "ajv", "version": "7.0.0-beta.3", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "json-schema-traverse": "^0.5.0"}, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "husky": "^4.2.5", "karma": "^5.0.0", "mocha": "^8.0.1", "tsify": "^5.0.2", "watch": "^1.0.0", "eslint": "^7.8.1", "terser": "^5.2.1", "ts-node": "^9.0.0", "prettier": "^2.0.5", "cross-env": "^7.0.2", "browserify": "^16.2.0", "typescript": "^4.0.0", "@types/chai": "^4.2.12", "@types/node": "^14.0.27", "ajv-formats": "^0.3.2", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^10.2.11", "@types/mocha": "^8.0.3", "if-node-version": "^1.0.0", "json-schema-test": "^2.0.0", "gh-pages-generator": "^0.2.3", "@ajv-validator/config": "^0.2.3", "karma-chrome-launcher": "^3.0.0", "eslint-config-prettier": "^6.11.0", "@typescript-eslint/parser": "^3.8.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "d34861ccfbdebb55bf9f49a08b29f76bf656fc5c", "tarball": "https://registry.npmjs.org/ajv/-/ajv-7.0.0-beta.3.tgz", "fileCount": 283, "integrity": "sha512-gUGVvM4NmyqrFvCNAQnP4P7FC0RjxMQyRnrXpozNglBkDJnTysVbvycyOZUy5n6yLKSqVDUqWZBXj7dXINrSqw==", "signatures": [{"sig": "MEUCIQCxR32lV1GIO/3sPW1dwEhRn1Ehvr+lrCCp60xrpgJQMgIgS6u2Xz+Q4XFKJkzPHHF1hQG/6vuKn5TeiSq27xh2G+A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 795088, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfo7VOCRA9TVsSAnZWagAAMkoP/0WFOe2VsVhWGMmVditG\ndIikPWVO60u1rvWjPqH6w3FR/+HRxgAykGaIWdwllZyAgvDCrtik7JnuVw+C\niUhZOyPiihOn2EybrGs61fZpV4Cfjy33XmzIGdk5smkezWYq3sapPhifIrJn\nXm3NjFrudtVe0TnSbnM/yIcArK3CK1ZeaZuleBJhbtNlJ1IjjmlbiTPU49B3\nVseinnkTvPoIBYot+b3WYpjO0ddyuZaJwjLdvAaffmRy2VMBnd+xdecXEDQc\ntRttk/3lnZ+kMu/kOwgDOfqjRMrFpOG43H1YFdbEj5dqXle6REubw/7Vdv10\nHdLSPmv4IOn9s8tRpDJMDJS/3kzY3V8olknTBzTIu0VPcwyw5aYKQwOQpQ+K\neK8jlx4D1KTQFb3LKzMjw9y90n8J98YZr6d5vauJv68UOPd1jlKMzXGKRcYr\njwlRPMaB38sbBZaaLYRIgCKArTcr1XQbpDRP2/cThL+3mxFb50qw+Z9cuqNy\nNJLU68bZdLjdaV7ZXlcf7L3Pp/SDM0EHeiS5HqpFdJseEMKEMVzPH6RPOQoK\n0kLWGOryqXJe1wIzbaX0XULdESj5QtYiIm0r24MdT1rlVkGAaFzqr3wSvTI7\n/T95Gia27tE+1ZFWBY1Yd0E3I2QFsofdof4kX7f5HpuCO/A4uAwWJ70NYF3f\nWFaa\r\n=Swfi\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "7.0.0-beta.4": {"name": "ajv", "version": "7.0.0-beta.4", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "json-schema-traverse": "^0.5.0"}, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "husky": "^4.2.5", "karma": "^5.0.0", "mocha": "^8.0.1", "tsify": "^5.0.2", "watch": "^1.0.0", "eslint": "^7.8.1", "terser": "^5.2.1", "ts-node": "^9.0.0", "prettier": "^2.0.5", "cross-env": "^7.0.2", "browserify": "^16.2.0", "typescript": "^4.0.0", "@types/chai": "^4.2.12", "@types/node": "^14.0.27", "ajv-formats": "^0.5.0", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^10.2.11", "@types/mocha": "^8.0.3", "if-node-version": "^1.0.0", "json-schema-test": "^2.0.0", "gh-pages-generator": "^0.2.3", "@ajv-validator/config": "^0.2.3", "karma-chrome-launcher": "^3.0.0", "eslint-config-prettier": "^6.11.0", "@typescript-eslint/parser": "^3.8.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "1885f0fe044922ca468729f6b299328463379d37", "tarball": "https://registry.npmjs.org/ajv/-/ajv-7.0.0-beta.4.tgz", "fileCount": 321, "integrity": "sha512-c6ROIXt0YR6uu3/XfwpXhRetEjNrmaa6WkofjGvO45rUXYNi0S5kI2Cwdm6vQBOnvzjCHSzUrRnCx54SaE1dgQ==", "signatures": [{"sig": "MEUCIQCAffpPCYJ21wN2qcfen/XpN/qM6ncwSLd+3wP5f/9iwwIgLoaHCh+odS3l6Ll2OD/Hfa50odKi0djNaSj/pLJN2cQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 838351, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfqk5rCRA9TVsSAnZWagAACeQP/R5V17qvH3RcBCxsng1f\nGosK+slAqLSddLYX9WmdKbCutRi/f1Zv/GsSF4IhFJc0fvBvo9/qsGdVM6dm\n2KRWWz1QUHJOgcssO576l9Pce/nd81oUwbcAVZdDG3MgcKfrndCmHiDG6yln\nyDcg2POOviQ0WkNq0PgkZj8nmpFDPcg+xfwVRathx0wOygnrvaWYEro39l7V\nR/fsEdH72lrn7y59UWTr96EOHbpOdXBeNha2KvFmY7cxx2BdKFTGnIuAA5Bi\n8HMbEPQ+JnrgiGR6P7fhP7Ar0TfJ+jSO6MQ8/mcE/XIFZWSap2oeLaiyE7kR\n73x9Q0AoJXBW/9mMDUq6h4B6hj6TbmhE1vhD8FG9D9ScpNmVWX4oFcmQvWXI\nIfUDRKp/Ze5AIrY/ipIGs6NuVifAwGuhX0cVrl0iBfWw3stWM0GiJ9U66KRD\nVwr2mSojJaVfA2QBh0VDHUSaxDy6TdzXjA+MKiTI3e+tgM+4iQBAgyZJ7lCd\nWUEdqhVmmDZYWfLB03OykvI4i2Tq+HevCMFYwgmowgjk5K11YgD5c15S8IFa\nigCaXG1LqM5F4erYWqE8c9dUieK3HQDtgHYVXQbIbMx/6U8cpW7SicWxs9MR\nvv4waoSNx6LdnkMBMN6BI8YrGGv9Mbe7z8GnZO03XtZrxfxdQG6Zs6H/NrJV\n+nhp\r\n=97lt\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "7.0.0-beta.5": {"name": "ajv", "version": "7.0.0-beta.5", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "json-schema-traverse": "^0.5.0"}, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "husky": "^4.2.5", "karma": "^5.0.0", "mocha": "^8.0.1", "tsify": "^5.0.2", "watch": "^1.0.0", "eslint": "^7.8.1", "terser": "^5.2.1", "ts-node": "^9.0.0", "prettier": "^2.0.5", "cross-env": "^7.0.2", "browserify": "^17.0.0", "typescript": "^4.0.0", "@types/chai": "^4.2.12", "@types/node": "^14.0.27", "ajv-formats": "^0.5.0", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^10.2.11", "@types/mocha": "^8.0.3", "if-node-version": "^1.0.0", "json-schema-test": "^2.0.0", "gh-pages-generator": "^0.2.3", "@ajv-validator/config": "^0.2.3", "karma-chrome-launcher": "^3.0.0", "eslint-config-prettier": "^6.11.0", "@typescript-eslint/parser": "^3.8.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "9aab801e671726954e2875fa8f87b0fec1a1082e", "tarball": "https://registry.npmjs.org/ajv/-/ajv-7.0.0-beta.5.tgz", "fileCount": 333, "integrity": "sha512-Pa/C51m4ICBeeIXopCOzeAtbw44rxA9O3oFCP1N8y3Y6TZU3YMSfobhEipvEma5ln7tYF6vOmEuaDbc7ByX/Bg==", "signatures": [{"sig": "MEQCIFpWG4JfBdeJgNypPV1z06x++Ybd5GPa837xIVIUA/JyAiB8eJvye2piN3AqiUKshfow1WDQovAtyxT/OyHcdmvePw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 842070, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfsVzACRA9TVsSAnZWagAAS0IP/i4jA0DSOtBmz/4utf//\n/klbz6y0LGAi8qidgICaF0Va17GG/Tlmc3TM5DJlbjH4X7r6KdkHI9r3oQYt\nNxMulnWzvXirNwaKdJO0/jWPjU2A5uCaOAHJhu5ZYOvGh5fxudpfGr7BeV8u\nPfIJFScC6UIeAJE96fBsHEBabQ7hzSEYTFLjSp7EhPhQeL2dbtJwGBR2lBrM\nRpnHhGVfK2S47p9e863eUd+hRFvim3Gsendzq8/h0amfirMPb/WSclPM8ZRh\naWCC4IEv9FdnokdjrrsINzbh/fbNroqgVSRwDimIazf+XGC3OZKLutDYmq2G\nteI09KdLElM/anCulSvAlXpTH674ffOGoboxRhCmrnNSuHBwhmOZXn0B+FWj\ntzM9a8KjE5m8fAvuzY5Eqv2YFpo6xeATdA3eUAnm8oQ5B24fHvgGLCZ6cPAN\n8FZ5bZznmTd39bSPSHJLyn0vAW6GRJQO9dnJMcFcAH9SdfllfY5zm1oTTk2E\nxAHzXy4XayHMH6mAgx/qv3rFNP7X/+c+DSBnvOJ8BiRNM31yTqV4ri1wHGdR\nD/6RHAVkJKMzFwp7iMex+pzfzMUM9aW2AcUvvKTsLEBX0VwEfyEaiAVYZHMe\naaN9cCF7MgbszBRuHef3KyauwL8yP3aHYZ4kIMl2OMEyAYbfTfQjxG5bVu4v\nqOM6\r\n=naQA\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "7.0.0-beta.6": {"name": "ajv", "version": "7.0.0-beta.6", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "json-schema-traverse": "^0.5.0"}, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "husky": "^4.2.5", "karma": "^5.0.0", "mocha": "^8.0.1", "tsify": "^5.0.2", "watch": "^1.0.0", "eslint": "^7.8.1", "terser": "^5.2.1", "ts-node": "^9.0.0", "prettier": "^2.0.5", "cross-env": "^7.0.2", "browserify": "^17.0.0", "typescript": "^4.0.0", "@types/chai": "^4.2.12", "@types/node": "^14.0.27", "ajv-formats": "^0.5.0", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^10.2.11", "@types/mocha": "^8.0.3", "if-node-version": "^1.0.0", "json-schema-test": "^2.0.0", "gh-pages-generator": "^0.2.3", "@ajv-validator/config": "^0.2.3", "karma-chrome-launcher": "^3.0.0", "eslint-config-prettier": "^6.11.0", "@typescript-eslint/parser": "^3.8.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "506037b27b7bd62e0e23a237319d2d11dcaba939", "tarball": "https://registry.npmjs.org/ajv/-/ajv-7.0.0-beta.6.tgz", "fileCount": 333, "integrity": "sha512-9aDR4p4ReYBS1XxrYONdWuFVRweLjJTv8RaNkBEpJm09jkVcVYhtaul5IL7Y/x1RJ9UakETm0oBze4VHIjq4nA==", "signatures": [{"sig": "MEQCIEaWecikWXxwH69reIZ1irX9vHgko7QONX8RARKRHGICAiBxQ25+uJENzrckSdMw5Harw1zQjqp5pXTY++ftlRduyw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 842286, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfsva0CRA9TVsSAnZWagAAkacP+wfksrhIiE8XoF5A2AWm\nGhnxw8zMkOydVgdGr4CUDc/UC/syvvDEWnm7/Y97pWyb8d2NtHVZkW2p8wiO\ngTAqX0/Xnf2J/qTB67M0qt7rYD1SWFO5Zc/w7Erd5iEdYUDw/ZKYNyr1NERR\ngKLZ6j4atelYJmxDrpE43z/43eZ4zjVsdix1TBDHeUJhqUBaIXZBDAid8LOk\nLbN7dTYNkpVfKLIflzScBdE7CeKcovopYTJUNaUkedp6mPVsA4f7aF2TwGsG\n+evcXs9xiZU4UFttvs5RElkVBKtktUrE2ixpnT5Oa8WxZYycVZ2kcE1NVq1Q\na9370wmH1P/QMjfvtNJQcXLjDkwVd4ja7PXhAu8KJtH9gZOGS0dz3wKR+715\nfRteR/9GTgy9orEaRQwwm5qAaJk4zjHGXOtnY8kqwFBH7VLCn6k1U7RSwSPi\ntaBVEsxHg4dX/0xG5/8uSEwIiGMwSD4uFRxM7e+zkxAEle+Et29LiASNewNL\nAosaA+C5g5J8ksDloYQjoILhIcqkbHHcW5h+rkIuSmqfwO8W4yZceJcpk/0D\nIsWMpe+AjdeM0qYIiFTzRqpHOdsCOwOEaAHCvNP8vo4J3y9hor3BHbOTRApu\nW2gwqNwypLYHLnNx9doBXWFIgg9AO3qUU4DflpelDeXnVIIga8xL2GRovwuQ\n3YfO\r\n=4SIg\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "7.0.0-beta.7": {"name": "ajv", "version": "7.0.0-beta.7", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "json-schema-traverse": "^0.5.0"}, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "husky": "^4.2.5", "karma": "^5.0.0", "mocha": "^8.0.1", "tsify": "^5.0.2", "watch": "^1.0.0", "eslint": "^7.8.1", "terser": "^5.2.1", "ts-node": "^9.0.0", "prettier": "^2.0.5", "cross-env": "^7.0.2", "browserify": "^17.0.0", "typescript": "^4.0.0", "@types/chai": "^4.2.12", "@types/node": "^14.0.27", "ajv-formats": "^0.5.0", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^10.2.11", "@types/mocha": "^8.0.3", "if-node-version": "^1.0.0", "json-schema-test": "^2.0.0", "gh-pages-generator": "^0.2.3", "@ajv-validator/config": "^0.2.3", "karma-chrome-launcher": "^3.0.0", "eslint-config-prettier": "^6.11.0", "@typescript-eslint/parser": "^3.8.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "39f2c8c8da65aa6889bbf9160ab6c050687eb495", "tarball": "https://registry.npmjs.org/ajv/-/ajv-7.0.0-beta.7.tgz", "fileCount": 333, "integrity": "sha512-1VY5E4aN4P1lNL+Y+qCGlL0IxT4WCt/f2xWnoJ5qsXCc8fxrKHQqoscXIAkjwhXX+iw10cOtPgPNUto/08Mnkg==", "signatures": [{"sig": "MEYCIQCN6gnYTZyTTF5UsEuJh3dxKPJW/fjn5m3nfWurxifH5gIhAJ11gsVhvg0F5epSBPbIm0qMyC6uelo1F+/bzMq5luaI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 843794, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfuk2SCRA9TVsSAnZWagAAIt8P/0xnHparSC3yvxj/gftG\n4roRdQZfBfgzcpqMZLivVZhQJ85alB0JcdnWgfIKW5gqdPqhkfcbvDSAR8Am\nwZrnEiChtZOBCmBAI5TO0cWoz3jSsvddZ0YppDbDNMFJnQKT4CaQeK5nTpGi\n/TCPDQ8iWTQk2LQWzARy6vOZeYM76Kh+nCWoqXhycde0n5G28XNB1n82eQe9\nB8rUeIcR4YlnK6jD0kzf+I3GnQCfQiPO/gsEGZwRxg/WeYt74cUpRLpf5CLd\nu0mMis3NkX8Mc+IGBGC+VM4ChPGgTlFQHiV99ukMFbbci6rVTa/FqtCc6swd\nR60iuQEjL4RaW2hd/42u6pgZiyqUEZSGsGArJxyihzTGzUjLFiWm2UL1c7y4\nE0+ZzcRx+woCmDf0X+a7urCloAKorwUVTIfFG2mcoyqRA5cE30Dv9AMppUHL\n/ofhRcxA8jDu9eqNYVOTqQ4kUWxZIAK8hB7g/ZWOhWxoj0SwP6eDXr8JtQ36\n+Vt2/dJiGYNpg+9WQo6nBPZXRGYjE5srLBnxZfyTj2jFuN/3a1I6f+mwpVYW\nIWmm+x2EOjHrt0nVS3FYN1tubAshQGIFpZi3/K/WeXtnXyxU+eMXnxDDPgXN\nLrO1+dP3UyJjBEzxPLUVMG0JrDoclAo3M+d34N93+oZqzftY8DryWHgq056I\nsKyo\r\n=PgEE\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "7.0.0-beta.8": {"name": "ajv", "version": "7.0.0-beta.8", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "require-from-string": "^2.0.2", "json-schema-traverse": "^0.5.0"}, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "husky": "^4.2.5", "karma": "^5.0.0", "mocha": "^8.0.1", "tsify": "^5.0.2", "watch": "^1.0.0", "eslint": "^7.8.1", "terser": "^5.2.1", "ts-node": "^9.0.0", "prettier": "^2.0.5", "cross-env": "^7.0.2", "browserify": "^17.0.0", "typescript": "^4.0.0", "@types/chai": "^4.2.12", "@types/node": "^14.0.27", "ajv-formats": "^0.6.1", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^10.2.11", "@types/mocha": "^8.0.3", "if-node-version": "^1.0.0", "json-schema-test": "^2.0.0", "gh-pages-generator": "^0.2.3", "@ajv-validator/config": "^0.2.3", "karma-chrome-launcher": "^3.0.0", "eslint-config-prettier": "^6.11.0", "@typescript-eslint/parser": "^3.8.0", "@types/require-from-string": "^1.2.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "9eb3b9277f18e0021d41823e8d9a2585da3d397d", "tarball": "https://registry.npmjs.org/ajv/-/ajv-7.0.0-beta.8.tgz", "fileCount": 346, "integrity": "sha512-vzGJf12dayS+Z+eX36ONWWZGop6psAtCU7xDoGHtpPbdr6kDr1VOKSyWcTJyFsnm9n9IafUDYCaSC8kF6qGUaA==", "signatures": [{"sig": "MEUCIHm9inzLFcLwlhUnnybgqFoUsSx9veEwEifrVeKYWZcrAiEA8W9VdmlDhIT0PLuxTvuDyRjGfw+n2ZRzOIgW6+SM8U4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 863223, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfw/b6CRA9TVsSAnZWagAAVQgP/2Boc6ZPFO+EsQf4evce\ntj8UMzZj0KwiJ+IHtrqwooVkEYOqLxeSJ5q1/Pw15VGDruz++9tPJFd7ilZO\nBca3r0VFAkNJKcNvKcHbtMIod5URXRQv9Hewji1ERva0kr1l2v92UdMhhH9/\n0jBb4JKO+5J7LZVaP29ch3Bff0oU60eM41DKe6dig2lMUDhPvWKqaaKrqTGc\n4lnlZCeVuADQhpaMie1uqDkX3w5sEdmW64ll6LUqPrEqGPvbqE60PrMY5ksd\nP4VHF+wEaGBB2RKfkymI4hKp7Kw7QMPRTaVlBSx98bE0an0Lm8OT0Bt+3tav\nJ40760czWVqVdtckCoDW3p6jnzmJWNeoKDGtjYm5yWfDAU8FXZrDV1cR95hL\nqgPRC/8y8z7PK38utUhiwHWvl/R0LcsI4/VZpaZbAidDASnctbwB0wyGCjMC\nmCtFG9Xcd7e+Hi0ssSnP0Cr0egAsEn3tiYBWAglta9FhdL6iYhrSSnr3gQiP\nt+GOQ1tJSGaXYpD+teGAZppDN/KD9ZmC5YxOrydkvHCn36pUFtrS3ccFCJJ6\n6Ohnrw+flKR6tD2f1EkV4Nh/Zoh9hciqDTbjq04w2Gxa3zVAWzMFnprScccg\nZVSxitsdLq5ck61gnIhG+FkHTvEIZGcM06pfpJPUZoHpQzlYnZ3H93iB4/yp\n+rzn\r\n=hMCQ\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "7.0.0-beta.9": {"name": "ajv", "version": "7.0.0-beta.9", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "require-from-string": "^2.0.2", "json-schema-traverse": "^0.5.0"}, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "husky": "^4.2.5", "karma": "^5.0.0", "mocha": "^8.0.1", "tsify": "^5.0.2", "watch": "^1.0.0", "eslint": "^7.8.1", "terser": "^5.2.1", "ts-node": "^9.0.0", "prettier": "^2.0.5", "cross-env": "^7.0.2", "browserify": "^17.0.0", "typescript": "^4.0.0", "@types/chai": "^4.2.12", "@types/node": "^14.0.27", "ajv-formats": "^0.6.1", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^10.2.11", "@types/mocha": "^8.0.3", "if-node-version": "^1.0.0", "json-schema-test": "^2.0.0", "gh-pages-generator": "^0.2.3", "@ajv-validator/config": "^0.2.3", "karma-chrome-launcher": "^3.0.0", "eslint-config-prettier": "^6.11.0", "@typescript-eslint/parser": "^3.8.0", "@types/require-from-string": "^1.2.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "f67e6890e15b95b302e93338c573b34762f64332", "tarball": "https://registry.npmjs.org/ajv/-/ajv-7.0.0-beta.9.tgz", "fileCount": 346, "integrity": "sha512-XTTuOzIBFMakbATMb54pjbqFzMTXqHgWmahPZu7erwOQRXpGGelAyZd3S5g6iBiMg4R60fQUyWm0wc8W3Sh5OQ==", "signatures": [{"sig": "MEQCIGUEGRVSr+MrPNekIh0L6CB8yv2quc4GSchkWFUohs/KAiBws25fwe+k/2VlgjDRBLDhQSGMXUpyYT71ms+y57Awgg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 864148, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfx03RCRA9TVsSAnZWagAAOn4P/26ANbLohfWbnwpE4CU+\nJagtw1XYRMe79TgERZTbkBhBjivAOoTWoSnYnvpiVtUvpDih6vdq9l67tBZY\nP9jne1VhvLnLpgamJOLrLi422NlANd8/DQ4p8fCyFdPGhGKgibEVBe/4MwDq\nk3MQRIxFCQ2/ZrHZhL1IDHww4a24toe5tnbfAGE12mn1037l5ew6rsOftK1t\nEJRzEG13YTW5WaT8i6jI0oIkWmnChOP5yj/fBGG+ir2F1qo3mRTwk0geE6rn\njRQ0YoPh34hyfS5MyqZNJ5hquWJ8AtzOLlyH9LGaXDOwiqCpYlJuH+rD4NGI\n67JwdK7U2nHHKKST+CHTBKa7m2K9Rd4FKrtJU3b/urEim44KwHoQZJ9fagan\ncbV6woYpwBWpHYCfOamdiOvZWZ6a/UntuQZ1BhnFqrcktDSeHyeVwkazlgMg\nJ9SWGZOdq4jBiPIbcDJzfZar6DQoK0xaaTmRA8GFKsMSomuvCSqAlyMb5tuI\nva1J/PZDekVty/mK9uZ1/Jf920swpXdZvpKjSgMGY5arjfWYui2Fo96VKifo\ndt3O/YfBQEfLTSrNmt+zpuUbTru9EPVgAp7MhcvUcVr6FgP2KzjudGH6jCKx\nNDMw3dKbijcVD2wk1/wr15QgUlRuvR+z+uVLZVQhqKVQW8jeVyvVOr3sqLs8\nw0SQ\r\n=mmJM\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "7.0.0-rc.0": {"name": "ajv", "version": "7.0.0-rc.0", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "require-from-string": "^2.0.2", "json-schema-traverse": "^0.5.0"}, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "husky": "^4.2.5", "karma": "^5.0.0", "mocha": "^8.0.1", "tsify": "^5.0.2", "watch": "^1.0.0", "eslint": "^7.8.1", "terser": "^5.2.1", "ts-node": "^9.0.0", "prettier": "^2.0.5", "cross-env": "^7.0.2", "browserify": "^17.0.0", "typescript": "^4.0.0", "@types/chai": "^4.2.12", "@types/node": "^14.0.27", "ajv-formats": "^0.6.1", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^10.2.11", "@types/mocha": "^8.0.3", "if-node-version": "^1.0.0", "json-schema-test": "^2.0.0", "gh-pages-generator": "^0.2.3", "@ajv-validator/config": "^0.2.3", "karma-chrome-launcher": "^3.0.0", "eslint-config-prettier": "^6.11.0", "@typescript-eslint/parser": "^3.8.0", "@types/require-from-string": "^1.2.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "ed024372c6adbc9ff634bb621505c1ce85cedbb2", "tarball": "https://registry.npmjs.org/ajv/-/ajv-7.0.0-rc.0.tgz", "fileCount": 346, "integrity": "sha512-dG0RJVjrKMXyo69dCGl3qSbMVsEiNSgNaUAzNicH88lAKPeBBtepiR7fPf+JZvlA8mXQAqVxIFTurbatuAxXug==", "signatures": [{"sig": "MEUCIQDRCJ/7sXCciVNJpDKqiuGUbQMl7uoOJFkawmFkQOQRqQIgNjb0Etab7Pc3mTcRf8DIT9aQHj5P56+DHvf/mDSYFdY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 866450, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfzPseCRA9TVsSAnZWagAAyRMQAJQ3MsA3vTkd2nzMgTkN\nxNMYWHFhIv987/tdzQfNw50AkT87O6w+/LO6oZ82lU2SjiX/9QcPNuA/6EEt\n//TfYJ+ppl+m88/VpbHP9mR+fhhLLQraEXj7GVu4+JtLCVn86HpIKohuOXi8\nz05457dGPVXnu3ODdhbxKfpll5/RRTF4IfBOJNdXKEAXlpUbudUSqz6EYiMV\n7iHHggehTwKdyWO4C4Ev2PxyaUTJJAaCij/eLvHqPqqANf1ld1AodgLENLUA\n9e+5Ag4DqH9DmMhWlBlY4JvupKx2BHEfOs+BAKG4E/Rv46uhO57QsGWe4hnD\nd+OFYY1qHkYR/fX3lN/12tTkuNCyAsDbfPFHFnaMmq/rLwbxPy6AOoupSDOj\npkQyhdl37u9jIH6xuwo6cV4F8SoaXur5ojSKZ4Xkq8epG1XY5829rPI7fC6x\nfMYJYCusHu5bwREhWrjDjWbIbertajHHJTf4dV5oT9PWc+Q4Zy0cnDE9VxQX\nKqF6N82F2jXMCtHs43Wu0CHPMMsx+Jli2VcHh1GfaQBx0baL7CLh1DMZwa93\nV0pm2hq+8RU9sqnmcEFzslPNSMgDIv4J7u9RMC9cvMX5I9uImp/Z7neEdfXR\nFedtppP968Nsx3gVHsXNysKpVif7X8AJuULd9jTlvp+ocYOXxELrwTAaOAa3\nHQz/\r\n=RPYG\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "7.0.0-rc.1": {"name": "ajv", "version": "7.0.0-rc.1", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "require-from-string": "^2.0.2", "json-schema-traverse": "^0.5.0"}, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "husky": "^4.2.5", "karma": "^5.0.0", "mocha": "^8.0.1", "tsify": "^5.0.2", "watch": "^1.0.0", "eslint": "^7.8.1", "terser": "^5.2.1", "ts-node": "^9.0.0", "prettier": "^2.0.5", "cross-env": "^7.0.2", "browserify": "^17.0.0", "typescript": "^4.0.0", "@types/chai": "^4.2.12", "@types/node": "^14.0.27", "ajv-formats": "^0.6.1", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^10.2.11", "@types/mocha": "^8.0.3", "if-node-version": "^1.0.0", "json-schema-test": "^2.0.0", "gh-pages-generator": "^0.2.3", "@ajv-validator/config": "^0.3.0", "karma-chrome-launcher": "^3.0.0", "eslint-config-prettier": "^6.11.0", "@typescript-eslint/parser": "^3.8.0", "@types/require-from-string": "^1.2.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "e69bf2e131a6c4f5c24c7500ec83b62a2812bf29", "tarball": "https://registry.npmjs.org/ajv/-/ajv-7.0.0-rc.1.tgz", "fileCount": 346, "integrity": "sha512-<PERSON><PERSON><PERSON>6nQmALezUc2p8J3eHqlRYXK1bVPyHBd4IGBOaCgCCTM0naZssHIy7SpFX2ZqDe7A0DJi5oCljCwT6WXLrg==", "signatures": [{"sig": "MEYCIQC6O4pyF6QZUlJjSnATN+Ylw+i9JfBXE+EKn0WIfyMAPgIhAJ2speMpry3+GI0dbvSinMCItUtQEEXTOwd71odINiO6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 868411, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf0TheCRA9TVsSAnZWagAAUbwP/iDtCnjy4dMvQ5Qgdp0c\nnG9taR5R+Mjg9cVI+FzUXrsajtukBSOiHelGRtcdqmgshXBekfMoxuEpVeHW\nEdDDD92slmzpTi+UsYP6E2DZRLKLXxCjf5qjUc2PwzMq/ev9y5I/PcmGYNyI\nNZ333jbX4p0ZxhcXPSVBbOlTOWIA/kMcdJmsuUo8EI2CLR/uPuMwgS+klzj4\ncYo5toTa8Xmn3MqXNrQAhAnwpeZSzxmI5qijwFVaKeTQfg4ebjicvROnSTBb\nQSNV0pQ0tLwieMF+9GwUbI7TOW/Xw3wVpsSg9AyHWgUQidhZ9zDZ7o/F1m88\nGVYvCRhLbeFsE3MoLvrPzWnOVWjOwiNzBUsFsK1hXWWE8YDMKCubxVHsg9WD\nkqGyTN5G2ivROnyAgJ5tQCsMAO7cQZAZZSEKjDly8K9GkB99ZeqYCIGaR3XZ\nxzlNBoWMs5+gmULzqNWY57TYTTTwGdg8cUxTLTQHctvwlALpRpuEdNdkklQe\nk6dU94dubnUnqpAoN/rjkK+oMe1Z4OsaU7z+W0UJkYEgqKA61M9Mmc8TUp5V\n/ezGeufSLRaKjfpz0J3XiUhXB0UvLUHtWdXVQKnHMg4i3jNtKQTZmyQKMDsf\nAtPS/iB/XHEU0S3U6XTL+l5uPi1lGZRQUduTq6Ez//Uk6YKMjNsfAlyPREW3\nJf0G\r\n=NfUC\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "7.0.0-rc.2": {"name": "ajv", "version": "7.0.0-rc.2", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "require-from-string": "^2.0.2", "json-schema-traverse": "^1.0.0"}, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "husky": "^4.2.5", "karma": "^5.0.0", "mocha": "^8.0.1", "tsify": "^5.0.2", "eslint": "^7.8.1", "terser": "^5.2.1", "ts-node": "^9.0.0", "prettier": "^2.0.5", "cross-env": "^7.0.2", "browserify": "^17.0.0", "typescript": "^4.0.0", "@types/chai": "^4.2.12", "@types/node": "^14.0.27", "ajv-formats": "^1.5.0", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^10.2.11", "@types/mocha": "^8.0.3", "if-node-version": "^1.0.0", "json-schema-test": "^2.0.0", "gh-pages-generator": "^0.2.3", "@ajv-validator/config": "^0.3.0", "karma-chrome-launcher": "^3.0.0", "eslint-config-prettier": "^7.0.0", "@typescript-eslint/parser": "^3.8.0", "@types/require-from-string": "^1.2.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "9c237b95072c1ee8c38e2df76422f37bacc9ae5e", "tarball": "https://registry.npmjs.org/ajv/-/ajv-7.0.0-rc.2.tgz", "fileCount": 347, "integrity": "sha512-D2iqHvbT3lszv5KSsTvJL9PSPf/2/s45i68vLXJmT124cxK/JOoOFyo/QnrgMKa2FHlVaMIsp1ZN1P4EH3bCKw==", "signatures": [{"sig": "MEQCIBBQAJ8YPqiIfkDxRi2sUho5H6G7DfX67ukBwEymTQ/OAiAgb877IJTjLDDHqDDVgTl9ofRLS68YCvbtuBT6VAivTA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 874034, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf1m1kCRA9TVsSAnZWagAATIMP/1+dyjMTADUY+4yFg3El\nBrlL9ueY/bRCsZ6uSufwY+CzoFs7FTn0WghEPgBhgN9tSdTklGBbNoCqdHmf\nuLK0mkGW/tOKPbm4d0myuaTJ4tqeG156Mnol5guvH4his54UolkaTmm7zGjN\nDhD95J3XeBHcTyUigVPcmfNIkeaZCxZX1srtgbIbDgOl1MApxB+KheaXEYEf\nWdO7/LGI3Jg1Te6Mwr9xjvZjWBFnhrHYAUgQ9vFI3J1YV3ZyQGA2+KiiIEdH\nAG1soX7X2s2mkavGgoLKumbQQMxIqjmSoVRt++MyM9CgdZiqP8C/jUHdI7t0\nlPav1xBjgfta8voU1t3PE60sj8VINsMJcUf2TW8XrhSG6fE3s5f2ImtRo5x3\nJ3LWz6zq9VMwolL7uNd0LCUIvZTgN+ZGCJbSlVvqsEjk5/Xm+cx6118iaCy0\nB4jTmH8Jx4gZXKYEAsd+vNJEtWWh+g688G/jgeg8Jbt7+3V9socUw6dkxhyI\nf0uPascnoZ1KXUQ6h20AgsVBbJkvmBs6qlCtwvsaKdiKQtKgvUF7jLwpM+1R\nsiurtzJG9PVwmz7PCK7ieO886bPuLEPgGkoJLK5yx4oWaNcpppsv9PLNDs+i\nHGazdH9OVFFT8+glOM8kFpijyZ1pn4nt1/X4zOKYBiUnMijNu3TImc4EYIuz\nIgB1\r\n=B8wt\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "7.0.0-rc.3": {"name": "ajv", "version": "7.0.0-rc.3", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "require-from-string": "^2.0.2", "json-schema-traverse": "^1.0.0"}, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "husky": "^4.2.5", "karma": "^5.0.0", "mocha": "^8.0.1", "tsify": "^5.0.2", "eslint": "^7.8.1", "terser": "^5.2.1", "ts-node": "^9.0.0", "prettier": "^2.0.5", "cross-env": "^7.0.2", "browserify": "^17.0.0", "typescript": "^4.0.0", "@types/chai": "^4.2.12", "@types/node": "^14.0.27", "ajv-formats": "^1.5.0", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^10.2.11", "@types/mocha": "^8.0.3", "if-node-version": "^1.0.0", "json-schema-test": "^2.0.0", "gh-pages-generator": "^0.2.3", "@ajv-validator/config": "^0.3.0", "karma-chrome-launcher": "^3.0.0", "eslint-config-prettier": "^7.0.0", "@typescript-eslint/parser": "^3.8.0", "@types/require-from-string": "^1.2.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "2454f695767ababa8d3a176b04cc0ac96a0e8f6a", "tarball": "https://registry.npmjs.org/ajv/-/ajv-7.0.0-rc.3.tgz", "fileCount": 347, "integrity": "sha512-znrbMViJVAr+wDDup8uVW0QDseb2PfAlfnOb8LrpRBoi03LdXoDLhBH59+kpSV+jH7hJvSz9Wdxe7ZaIo5mMzA==", "signatures": [{"sig": "MEYCIQC4Hsi4VzL3DnaqOZz1QpUeF6EIATcgx4Dmmi/gZl6DmQIhAIzvPhg6SC/YtplGN2iGDYiuV9bjani3moOCyak0XHOE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 874385, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf17jPCRA9TVsSAnZWagAA0fQP/A0MYjzYtE6Ksxy5cl8G\nfSLtcukH5BO36FT2OfFb9aLhr536r19OvQ6fc6fyzH8ZTSiUufd3+sX82ubp\n4jstmer5PZyK0poJcyypnIyYDoxsFfuMSsjaAJCpkWXaDehoj8iu9zgfGVy6\nIhn9eTPCv+QHTvtzQHXAt8jyIlMBNqOaIWsktGal1f+7Zd7Mz8AA760/wlzR\nIeIYNQYVW8mwH24V076i6CWV7VhD2ihhPpTknEF9Mha0LA1aZqqMyvGdnGHP\nFfYA/9BrNEPhA522O7pUaeUi2tCVigfpU2G6ULxkbqeqHMON5/llpTPqwYVM\nEcr0QQaZbRp54+LVJC68ki6QIAFnNPwAFcrEnhHYhw8ghjSN2SWAP/A0pxri\nzSoXR6vFBM775K84mJAVKnmuFbcUwUKnrBB+Fo4l6qTbXPQzJha1C9A4sdfW\nJ0Jo9uHAeyaNRSPNYvJ0xUbgaNqB0LSrankDJIuw57fx5RE4mOQSBj3iVRnP\nH0BgZL4vPt9ngpBenH3w3FiH6W8VlRD5Z9+lIZ0tBOsWECO4e2huQ9e0aFEB\nTzdVrfU1t4Tmouy6yshyIrLI/fXMgefuAnk6PR3y6KOuRmrsXX+WeyP0fWO1\ncB5h9JW1HVRtBfwQ3F/d1iFnXtHpLasc5CEi5iqQuO+kH2VztxXbhDo7E+AP\n8tz3\r\n=EwF9\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "7.0.0-rc.4": {"name": "ajv", "version": "7.0.0-rc.4", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "require-from-string": "^2.0.2", "json-schema-traverse": "^1.0.0"}, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "husky": "^4.2.5", "karma": "^5.0.0", "mocha": "^8.0.1", "tsify": "^5.0.2", "eslint": "^7.8.1", "terser": "^5.2.1", "ts-node": "^9.0.0", "prettier": "^2.0.5", "cross-env": "^7.0.2", "browserify": "^17.0.0", "typescript": "^4.0.0", "@types/chai": "^4.2.12", "@types/node": "^14.0.27", "ajv-formats": "^1.5.0", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^10.2.11", "@types/mocha": "^8.0.3", "if-node-version": "^1.0.0", "json-schema-test": "^2.0.0", "gh-pages-generator": "^0.2.3", "@ajv-validator/config": "^0.3.0", "karma-chrome-launcher": "^3.0.0", "eslint-config-prettier": "^7.0.0", "@typescript-eslint/parser": "^3.8.0", "@types/require-from-string": "^1.2.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "a957c2f07dd7b86db2875c7b40f9305ff11f18e8", "tarball": "https://registry.npmjs.org/ajv/-/ajv-7.0.0-rc.4.tgz", "fileCount": 347, "integrity": "sha512-maq+H2VHruKQqN6vBRZr/CSnDKHzzxn9wnZhgv11oj7g/ka5h+sk9+DiTjSEoAxuBYEM+vP9xTfZJQMX0+pY9g==", "signatures": [{"sig": "MEUCIQDqmDluPn1byLgPk1X3q1qpsjmOXMN0HV0898zesGR2EgIgEJ7dUsXjFz8T2A7KcbrGedQPw0NTjF49TJIAXsNJZ8g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 874391, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf175WCRA9TVsSAnZWagAAhSsQAIQoNUbj4c+ONZppl2JI\nQNU1YQHaJ6hXBgLkighCimK4BdawXoCr9mv2u/e4N5jWTf/3Ec9BsHfHgC4Q\nhHyOIFovNsIvJcsY5julrY5VTJJGG18/ZoeL7mRfcceUt3Ej8cK3CnHk3c0V\n4CClLsyj0953VmZs1IR0SIDDAJQQ9L3HW0ylCq/CHnxMZOcNGMyoR5n5BM1+\n+triRBu69r5rTkWnWUlDiqTlgTWMg445lxXAFNvkbTkCMuFadXiiRJL6vGIp\nbEb2G27FwGlfpdJacwqm2l4loWC3ROBqNONwguKuuMlMVpiFvxwYhMbu8KXo\n0sj8LQzIPz8OobzcPYSY34txmksSmoPZuaojDtv+DpbwHvmdq39O4POp/hKD\nJNzv+UN732Ee9Q5z0MUE5y2V8ZevTM+37x3hX09BLliDtP4aNi6aFih4GtCA\ndS3A+j+VBMwLVStgcMlwtzofdw2xvPojTwzozmF4QvaypBwaz5nkmuUosvSb\nfp6uV9p29Dfm7YMshuBDOwNAITwk2LBMs3eg9BW9VFRjzw9fO5FoUwc9NnUv\nP6Wm4eV+ryW1Ex2uMshXxpCfRnO9YVghrPeKjv3Vn6XkSpFAiUz0LIqU+/i0\n7hf+38v+DpQryEXdYKyfvZ1GJKz3QpZQNhDMdTjptHztPU7ThbfxrlLria2h\n+SpO\r\n=PPp2\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "7.0.0-rc.5": {"name": "ajv", "version": "7.0.0-rc.5", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "require-from-string": "^2.0.2", "json-schema-traverse": "^1.0.0"}, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "husky": "^4.2.5", "karma": "^5.0.0", "mocha": "^8.0.1", "tsify": "^5.0.2", "eslint": "^7.8.1", "terser": "^5.2.1", "ts-node": "^9.0.0", "prettier": "^2.0.5", "cross-env": "^7.0.2", "browserify": "^17.0.0", "typescript": "^4.0.0", "@types/chai": "^4.2.12", "@types/node": "^14.0.27", "ajv-formats": "^1.5.0", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^10.2.11", "@types/mocha": "^8.0.3", "if-node-version": "^1.0.0", "json-schema-test": "^2.0.0", "gh-pages-generator": "^0.2.3", "@ajv-validator/config": "^0.3.0", "karma-chrome-launcher": "^3.0.0", "eslint-config-prettier": "^7.0.0", "@typescript-eslint/parser": "^3.8.0", "@types/require-from-string": "^1.2.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "3bcffe7d85ca7199d90bdc48825a1f45628533cf", "tarball": "https://registry.npmjs.org/ajv/-/ajv-7.0.0-rc.5.tgz", "fileCount": 347, "integrity": "sha512-HMSdbwBa1Ep0HJgGSk+CYWCvbMhbu6hrQJsP/KyB0mt6WIN9jct5Xl4Z72Fkj+2JXCd5d0hguT2Vc/CHZtlHuw==", "signatures": [{"sig": "MEYCIQCTkmbkyzD3a72GyXvoi3eQ6Vn6re2mpNw7xGvezLX78AIhAJULKCkARnv/KhnfAq7sEx7rD2RAHn2CnBJlAqMd3TRd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 874408, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf18ZNCRA9TVsSAnZWagAAlFEP/0DMh+ExyEdb0tfxohYO\nWEhNBNFr34veohbvQFY/TEW2vT9V6C184KJC6Yg5QWM4YNjKhWNW9iUZMdsG\nwFPyd1Xjjhoc6oP6mtaajAldP0Toz7Z9o/LLsYtBwcbd2zYPtee7Yf/fVb5G\nqrD8JGQwDfjyHRDSX3/QwLORKJR5TCvCv6GjTIi8tdcgvjYiNGckig7uS6jj\nbZ+Zp/I7N9abf8Vt7+XExgZJRO57dpQiTUj46l6rQmuogSVhfJuYfrrEhgSe\nZ16PAGHXoJmAr1ooscmWjNJnUCNvlEu0IVA9d0PugGTYIB/13yTZlzzNLEfH\nNfA9rANWKUtIsGJI85FjSC29UVj40QNQmJxjupH1sn+GT7fTYFv3Gz5CYwiV\nUn4vY46ElEmbPy24dPXlVMnR8kdg7hExA1duE5vaAnWeYTOOp/hz6cHFmchM\nE51gMyBhp5JpmbNhD6TUl7qmdPHnApN/TJwp+Utcv9u9VhRckOm3HL4RuGdn\nffz7yjq6MDZ1sQkCZkSes6F2LI9VuAqmb4jQ3hnIx8n3y3BkybMI5N8yt7Gt\nPmGZhCPUTfLCL9vskvSSTx8ITVkQhKWQWeSSNVPrMXpK0Sr0rEzHMO6a+XEy\n6NxxHNC3uCUBFZqNvp3xLlZ49f0SvsvN9R2qGA+KLNiNOCY4vtWxnOuTQhTS\nl/jl\r\n=e24Y\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "7.0.0": {"name": "ajv", "version": "7.0.0", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "require-from-string": "^2.0.2", "json-schema-traverse": "^1.0.0"}, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "husky": "^4.2.5", "karma": "^5.0.0", "mocha": "^8.0.1", "tsify": "^5.0.2", "eslint": "^7.8.1", "terser": "^5.2.1", "ts-node": "^9.0.0", "prettier": "^2.0.5", "cross-env": "^7.0.2", "browserify": "^17.0.0", "typescript": "^4.0.0", "@types/chai": "^4.2.12", "@types/node": "^14.0.27", "ajv-formats": "^1.5.0", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^10.2.11", "@types/mocha": "^8.0.3", "if-node-version": "^1.0.0", "json-schema-test": "^2.0.0", "@ajv-validator/config": "^0.3.0", "karma-chrome-launcher": "^3.0.0", "eslint-config-prettier": "^7.0.0", "@typescript-eslint/parser": "^3.8.0", "@types/require-from-string": "^1.2.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "59804586d998d19efe446fbbf821d147d300d8e2", "tarball": "https://registry.npmjs.org/ajv/-/ajv-7.0.0.tgz", "fileCount": 347, "integrity": "sha512-hT38y4TE4E8WkqIZMFhGnd9F4Afn75YtFPJlMv8/j4RY2Vz48dYxn6AiD6CeE6JMo96F3Ldgb+j8qmgvcYT9Qg==", "signatures": [{"sig": "MEQCIFpQAR9ekoEl7d2y1jEspxp6CF7XfQEJP78X3+O17EaFAiAIAeVkkmSSU5NVlUg4pB/z8YuHS4lZFgajtBUzSL/61A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 874072, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf2QqpCRA9TVsSAnZWagAAgN4QAJcz+GqWp/qj2LHOsCyK\nuyd6tp6hZpEk4dMOV8MR202F3XuejLjpx/vzTiHkt6XfXrUrYmk92SsJMQ0o\np5EZCTn2r4i5MTXhNviR0pUdyKWWsiSzeMJ3ouhnkUy5cPZQiGvh5U2nNCg/\n15Sl36Df/EZYeaOZyEKwlhsHlRM7DCh5511LTR0xCV33C3FzkoCN3qhrSkqE\n2Et1e/lk1QZI6vr8ykxTPyJuD/bF0vTKe7KEc2YqpXbAZYAF0tJfFFb10494\ncT74khut2i4sgsT8YdC79thsMzZOFSHoWgSnM3Vld6C5EWho8d3eeQwHw1Xt\nUOwBUuzAFhbfBXd6VFp3sL7tl9YldrRI5PL8PJAugDL8JCf5z98DaFBoaFhS\nArGG21s5/7HtOjtRGsypUtl+4FGJuMqO1iWS7MofW4WpO8sr8k1zqui2ksYv\n48wJ2nfPGPwwTGqm7wReO2MGP3HV9sqquJ3BP3DW3BfSiVbK214V78fOth3t\nSOjbUA7pnIBC0Bd7OeXGpxHll6bpg27hdel/R+ADcAPae3H0GZkkFLNCqn8Y\nuWdfpIIPO0beNqYe9EJiIpRKVDAe5r7JlPqYAqUFaCDEoPq5zkOnDq72Wu6h\ndCsMK+6x7DVSDLc3WYtxNNp3TjSZghwzPKAKFfA368DLE56n3xyXr4me9hT1\nJN2W\r\n=wcw9\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "7.0.1": {"name": "ajv", "version": "7.0.1", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "require-from-string": "^2.0.2", "json-schema-traverse": "^1.0.0"}, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "husky": "^4.2.5", "karma": "^5.0.0", "mocha": "^8.0.1", "tsify": "^5.0.2", "eslint": "^7.8.1", "terser": "^5.2.1", "ts-node": "^9.0.0", "prettier": "^2.0.5", "cross-env": "^7.0.2", "browserify": "^17.0.0", "typescript": "^4.0.0", "@types/chai": "^4.2.12", "@types/node": "^14.0.27", "ajv-formats": "^1.5.0", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^10.2.11", "@types/mocha": "^8.0.3", "if-node-version": "^1.0.0", "json-schema-test": "^2.0.0", "@ajv-validator/config": "^0.3.0", "karma-chrome-launcher": "^3.0.0", "eslint-config-prettier": "^7.0.0", "@typescript-eslint/parser": "^3.8.0", "@types/require-from-string": "^1.2.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "d39ed49159672a91f796e2563e1e96984956e338", "tarball": "https://registry.npmjs.org/ajv/-/ajv-7.0.1.tgz", "fileCount": 347, "integrity": "sha512-D2UYOXvDzGLVwld2/EyRu3xiJG885QUz2xS1phZzebYLPMPBFbPK4naXGDCtPltZoOG+I1+ZkNAJ65SJ3vqbsg==", "signatures": [{"sig": "MEUCIQCPl8HdCtmJrAhQRdc0F2Tg+vTErBDqwU7T5fWT2ZlHCAIgMd99r0jybi4MrcIXD1loeSd7JDezEQDsgGiXyx8kgTA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 874082, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf2mMQCRA9TVsSAnZWagAAQnoP/1hvG1UWt8Y2S9hWrHCl\nf73dHRyilVNNtIhYKR5MQ27laVtRR7A6eg1d/7hjxAOklAGzp+5L8nd2sNhw\ncJA8GhdlbjaJmnKewDtPzy9hUXRyjhtOalzHPSoj25U+iJBrOrdbQlZMwj8S\nDHwiUIGJm2r1tien/bAUpUTbgImz1HDZDeK20NoXSPtmRXzcJRMxxiF0L0qQ\nYlG4xlywG3ELx72aeLQ/HuDkN4AwGW+v/IBMHT21STrPLoIiimzobqMByXWA\ncPATHr020Rr9RzqUjUZduW0ZGNeYkDFBb91GgZAs8uK0WPp3luZqNkxR6+zc\nDorMUtjtCfyVScjl/yywcnAYZaxudkx8q98KyvzO0reKrtAKiaUGgtq6qt5s\n9jvLD7kMNICVUOpgLUjyEfAuHc3aFlkSnXsPI3D7vZB0qrc/LQ79lCVA2udx\nCZVXDXK1LfdjpaK4w/UJ+f4auFo168K4HXNs3ba6nyqH9MvxLwmk4GcSpTcY\nhTeX8Tap5j/E4Ft4OeK7Wkcu8eFJXnSr2xexVYAzR3UZVVnOeb1fOmkjnmDk\nmY0p1tQt32XwshQdtxmCSp8Ql5GhT7RfuT1HoNOOdj+jyNo0PvH1KZNk1jxw\nWJ7byYTmI8zfXNuIy2wazT/HBkTG154d3R+hFfuhWaLh3zc0Mlb3vhZcZktN\nmlzc\r\n=up5U\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "7.0.2": {"name": "ajv", "version": "7.0.2", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "require-from-string": "^2.0.2", "json-schema-traverse": "^1.0.0"}, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "husky": "^4.2.5", "karma": "^5.0.0", "mocha": "^8.0.1", "tsify": "^5.0.2", "eslint": "^7.8.1", "terser": "^5.2.1", "ts-node": "^9.0.0", "prettier": "^2.0.5", "cross-env": "^7.0.2", "browserify": "^17.0.0", "typescript": "^4.0.0", "@types/chai": "^4.2.12", "@types/node": "^14.0.27", "ajv-formats": "^1.5.0", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^10.2.11", "@types/mocha": "^8.0.3", "if-node-version": "^1.0.0", "json-schema-test": "^2.0.0", "@ajv-validator/config": "^0.3.0", "karma-chrome-launcher": "^3.0.0", "eslint-config-prettier": "^7.0.0", "@typescript-eslint/parser": "^3.8.0", "@types/require-from-string": "^1.2.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "04ccc89a93449c64382fe0846d45a7135d986dbc", "tarball": "https://registry.npmjs.org/ajv/-/ajv-7.0.2.tgz", "fileCount": 347, "integrity": "sha512-qIaxO9RXjaXyO21tJr0EvwPcZa49m64GcXCU8ZrLjCAlFyMuOcPpI560So4A11M1WsKslJYIXn6jSyG5P0xIeg==", "signatures": [{"sig": "MEUCIBserjB9EKqT+ncYEC8byVqZOd7a8gwqtv/9OZ6BzHX/AiEA0clgGRJ+f7b90oK5sU8fJUuEmwnPswYQHzp/FgIOowM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 874916, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf3kmKCRA9TVsSAnZWagAAxwsQAJ1U4YT+s+hoP9/xah38\njyhT5m4jV3Msb8RO8PpiQvQLbSeYk67R495B0riRLeetOFu8gFGI/3iW7p0w\nuL57gDFmewIM7wXEVfFRQ8z3JrhoB51czB7bFAifVyqhX7m9H3fje+wIsOnd\nXkW33zLWsVnWJo5bH8aEen3ewYj3eLukP+1toyQ1vDOAls0Vrci9xkhfLlov\nGkPE8rHL0/U1kW1WW9pDftO3K7aSOlREJtLk30BdH81WQnsuRugAxJ2LzPSI\nzP20151KnVVAb4SjW61LxhplfShTXwr+/n8R/SvdrJQXDxMA8eYzYEPqrsAl\nwtFmiB+dX2Ylka4JSW7qMrPayqD5UcOCcs0bZClLJde6GohnDyAMwDTPMNYK\ndHygqNGp2NmaXBSOcRXRCZz3KgiIesdHIbRBOmv34R+T+idyA0BQkwcvxUuF\n7P82EOxpmr5Ob4CRuSfR9H6y4VMV0EaQWgYtFu1eKJwAfoVG0ZQiKcVSIHSY\nxNBRH4xzumWRk8Z48Jgs4AEonobTGngpQcuCZ4vrIvhQ/2lZkHRC/hkjnT3I\naNl5Q7oKZZzEMlxMFpf8oRbi2VUlUoX+iEFlJ04d1e/zSbcIBixBDUY1XwD2\n9bCRPuC4Makpb5klD1ztHpkx/fsYuNdIScfXFtE/3hKCG2e1F3ClbfocPXhC\nTtkB\r\n=8xVj\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "7.0.3": {"name": "ajv", "version": "7.0.3", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "require-from-string": "^2.0.2", "json-schema-traverse": "^1.0.0"}, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "husky": "^4.2.5", "karma": "^5.0.0", "mocha": "^8.0.1", "tsify": "^5.0.2", "eslint": "^7.8.1", "terser": "^5.2.1", "ts-node": "^9.0.0", "prettier": "^2.0.5", "cross-env": "^7.0.2", "browserify": "^17.0.0", "typescript": "^4.0.0", "@types/chai": "^4.2.12", "@types/node": "^14.0.27", "ajv-formats": "^1.5.0", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^10.2.11", "@types/mocha": "^8.0.3", "if-node-version": "^1.0.0", "json-schema-test": "^2.0.0", "@ajv-validator/config": "^0.3.0", "karma-chrome-launcher": "^3.0.0", "eslint-config-prettier": "^7.0.0", "@typescript-eslint/parser": "^3.8.0", "@types/require-from-string": "^1.2.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "13ae747eff125cafb230ac504b2406cf371eece2", "tarball": "https://registry.npmjs.org/ajv/-/ajv-7.0.3.tgz", "fileCount": 347, "integrity": "sha512-R50QRlXSxqXcQP5SvKUrw8VZeypvo12i2IX0EeR5PiZ7bEKeHWgzgo264LDadUsCU42lTJVhFikTqJwNeH34gQ==", "signatures": [{"sig": "MEUCIQD9XsHclJZIzxza6le7zmd4nIZdqCKEThGt68xF/yu6JAIgLpobs6W2cXagZ9yynkclSZg5OTKSJ0n6X7LMBDRX7+Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 875320, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf8FRZCRA9TVsSAnZWagAA6lAP/RIhBDnqDD401IjIF2U/\nJHXWh4WUocvjCiUANsPeU9T884nKOQg8l8yn2Gb3QfZmd0FTQj2ihGlXsyM+\n5DJ94Ow6/N1xv6+c8C8O7FE1omEiP5GaDUgtwc4FIujwDIRyJ5plaxsmY06+\npSPRdtFyUBRJg7z308n2iBDmfovh7DTgmbYifd8e0KVOsJ4NmTUiU8RF9mhk\nyGn+c5vPvbyoJNpR1UEAyKk927GXhCZXVgJnW1FrFvpfShX8ohIL5v+IItEA\nYXaRUBCLSJNFHLlO8v6EfzMP/eT9zmt1mUnly9qIxhPRWwUs3LH8IWKyh3H6\n2nE5TSxiT/Q1ufXsZu13AqBc7nHQkqXQnvSBz7xyY6yJpzaxwnGIt3pi9vhh\ngobMQ58FxLoUlTyrmXVGILub8v2lnx2F9NfTOwQasQVeNB1EyyTbDdvAbTsC\nJ/HMb2ohQB6ZFwUrrqQPl6gg9qYSkMs/8IMBlV4H0/hNGTxKbV/JBdkTBhlJ\nXu33OOESi05SEmikd697IRxnCJHaQAvQ46gouOfuKR4X0PX8xOtHCJJLX4g1\nhz3HEdwZMhhN4Hx+bERBYjB9U1o0pe1SruJS3S8vdXMwi6/1PDNK/1gTXNV2\nrtkKtls8FcTUgkdkCFIgnqChzW7bqsGLXxbteFcc67RvceHQNc6jwH2sYNPY\nc5B9\r\n=dlPY\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "7.0.4": {"name": "ajv", "version": "7.0.4", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "require-from-string": "^2.0.2", "json-schema-traverse": "^1.0.0"}, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "husky": "^4.2.5", "karma": "^5.0.0", "mocha": "^8.0.1", "tsify": "^5.0.2", "eslint": "^7.8.1", "terser": "^5.2.1", "ts-node": "^9.0.0", "prettier": "^2.0.5", "cross-env": "^7.0.2", "browserify": "^17.0.0", "typescript": "^4.0.0", "@types/chai": "^4.2.12", "@types/node": "^14.0.27", "ajv-formats": "^1.5.0", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^10.2.11", "@types/mocha": "^8.0.3", "if-node-version": "^1.0.0", "json-schema-test": "^2.0.0", "@ajv-validator/config": "^0.3.0", "karma-chrome-launcher": "^3.0.0", "eslint-config-prettier": "^7.0.0", "@typescript-eslint/parser": "^3.8.0", "@types/require-from-string": "^1.2.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "827e5f5ae32f5e5c1637db61f253a112229b5e2f", "tarball": "https://registry.npmjs.org/ajv/-/ajv-7.0.4.tgz", "fileCount": 347, "integrity": "sha512-xzzzaqgEQfmuhbhAoqjJ8T/1okb6gAzXn/eQRNpAN1AEUoHJTNF9xCDRTtf/s3SKldtZfa+RJeTs+BQq+eZ/sw==", "signatures": [{"sig": "MEQCIBmlVgZlzoaS2DiDDWRYfDolH8xVOpPCHHHM+MfJncWQAiBxxa8C7nLEHWE6X8ZnDCvyWR0LmJr/Txx5SZJCTq70/A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 878305, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGGz1CRA9TVsSAnZWagAAZ04P/Ale7eikSPzCLRqavHgb\nxvVYUg6a/I+T6xRS0NF05J4bKcQ9i7m7hvJ/IioIJgyWi8HMSMvBD1w6yqgu\nv5qTJa+Xsw5cBzhU02EmsnWhxq6JHHvOHqdqj7I5JiLDF7OMILX/GdzwkhkA\nKxB6g027W2wov1hnDJRx7eSzoXjg5JMZ+P+Csbu7iPDCQR5cT1SdL6nwlRja\n+WDWZ3QVp5ojJUbS6Poo1ChHkJp0Au2I3YDvKnNNQaKptaoN52Mm+GBzxuhL\nCZkNmTKFEP57DafGOS97qPp/leggYOTIftE5EoQ4eRxOjgR0Dd5SGT5YoT6A\nwDjiLIWQnQe/mC78ms4WIDX6h/yRsPy/WQMWllz3iNnLGcTQFQvBh0nds//v\ng1Uec8qPnrNVSAvzK/+cvRPf0doA8V8KQXhzZ1P7WBHYOXJpHfaq3ka+eHYQ\n+TDikSMyETeh+K8gwpX/HkMKXnQeLHmiBVM6B7s8ApAZfzUzAPE/wQGtoQaT\ngQ6xpkE4J3lq2XZokwFnlLoZy9vWNRFoSHNTgjxZ8h1Uu84pd3KfMzuki3tJ\nbGMLklMWHoeXF0Amhi4Bf0CwA7iw3kJauBg6R7wi5mFiJCcyzyGObU89TiSo\n42yq2jRlrVmVt4HlhnYmQW9yWOzEsLxxR0Zgmhj3/KL6yrWCwiJq/xzmXGIN\nZRmi\r\n=cAqv\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "7.1.0": {"name": "ajv", "version": "7.1.0", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "require-from-string": "^2.0.2", "json-schema-traverse": "^1.0.0"}, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "husky": "^5.0.9", "karma": "^5.0.0", "mocha": "^8.0.1", "tsify": "^5.0.2", "eslint": "^7.8.1", "terser": "^5.2.1", "ts-node": "^9.0.0", "prettier": "^2.0.5", "cross-env": "^7.0.2", "browserify": "^17.0.0", "typescript": "^4.0.0", "@types/chai": "^4.2.12", "@types/node": "^14.0.27", "ajv-formats": "^1.5.0", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^10.2.11", "@types/mocha": "^8.0.3", "if-node-version": "^1.0.0", "json-schema-test": "^2.0.0", "@ajv-validator/config": "^0.3.0", "karma-chrome-launcher": "^3.0.0", "eslint-config-prettier": "^7.0.0", "@typescript-eslint/parser": "^3.8.0", "@types/require-from-string": "^1.2.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "f982ea7933dc7f1012eae9eec5a86687d805421b", "tarball": "https://registry.npmjs.org/ajv/-/ajv-7.1.0.tgz", "fileCount": 408, "integrity": "sha512-svS9uILze/cXbH0z2myCK2Brqprx/+JJYK5pHicT/GQiBfzzhUVAIT6MwqJg8y4xV/zoGsUeuPuwtoiKSGE15g==", "signatures": [{"sig": "MEUCIQD2Nidp4M+FkDFxXOSzZivs/cSb6SiEx702hBDGcBmmzgIgG4AZ6hEnzMdOoiLYcG2gmHdMsAuw46N+O/Tn/jJ4Chk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 972115, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgJO3hCRA9TVsSAnZWagAAq6UP/3G0XSdUFCCvw8//bGU8\n70GilKD2UmHBrSbW+mHlTiNWBXzsX0KCY/nF1IgPP2UT2pI8HZnOZjahpZol\n4Vo4N8YtK7WlFR4JuUrMaY3kSlWQ7DV0J361MWLYwshrWeQWtYBEYymb6zkc\n4OBkDRqOYQZKqyTu3cA191k501CWzYSDSpWu15AzjCm+yGAKgJRj3HLyqb2e\nniLseKVw+6PMcSnoZJCM7unSIdFP22IXusAif8NSug+B5YEkPnwX19JTeFuZ\nbtyKs3s7cLrEPSpETMS8jXfp7uVClRnDGfvgIRY0f066kb+gnmEFfCB85TI1\nVGDv9V78lb3a7xjO/YL4+kG3JW7gL56EUvFsWSYh1IhKs0FAaVATVZoysHto\ne0SkoS4/RCI6eA0JKlLb4u50bv5XcGvN59IqhO7l8Xe7XljF5pMznzrWf0If\n8ileFgOA/zZxRrjay3KOgqBn+AwuiNvbtD42RYEc7bRpyDD5UujDfkwHv5i3\nVhgg3ySSFDHYfRxekVkpFV7Jy3dXSjmET3CPN4+mxMdaDygClz41kN0DJt1o\nd+KAnAJpTO1zTVz6sI2CPxEslm7KyhXig3NUCGnCzvzMWtEOeYPgCGZj2ZmL\nFV8zy8b1DtrD2gy9Cp+oJG76FebYp5ppClx1L/mleVsjWwrNBZaPAKfMVL+D\ne0xh\r\n=nSFU\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "7.1.1": {"name": "ajv", "version": "7.1.1", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "require-from-string": "^2.0.2", "json-schema-traverse": "^1.0.0"}, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "husky": "^5.0.9", "karma": "^5.0.0", "mocha": "^8.0.1", "tsify": "^5.0.2", "eslint": "^7.8.1", "terser": "^5.2.1", "ts-node": "^9.0.0", "prettier": "^2.0.5", "cross-env": "^7.0.2", "browserify": "^17.0.0", "typescript": "^4.0.0", "@types/chai": "^4.2.12", "@types/node": "^14.0.27", "ajv-formats": "^1.5.0", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^10.2.11", "@types/mocha": "^8.0.3", "if-node-version": "^1.0.0", "json-schema-test": "^2.0.0", "@ajv-validator/config": "^0.3.0", "karma-chrome-launcher": "^3.0.0", "eslint-config-prettier": "^7.0.0", "@typescript-eslint/parser": "^3.8.0", "@types/require-from-string": "^1.2.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "1e6b37a454021fa9941713f38b952fc1c8d32a84", "tarball": "https://registry.npmjs.org/ajv/-/ajv-7.1.1.tgz", "fileCount": 408, "integrity": "sha512-ga/aqDYnUy/o7vbsRTFhhTsNeXiYb5JWDIcRIeZfwRNCefwjNTVYCGdGSUrEmiu3yDK3vFvNbgJxvrQW4JXrYQ==", "signatures": [{"sig": "MEQCIEkE7yfzIO8TG/Bk83efSz/A0C1YFBN22gJF3uQjpovdAiBU09lTKUFyaxSwmq7wgvnrz+v8OiG9sN6ckMU+yiACqA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 972134, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgLN1mCRA9TVsSAnZWagAA6EMQAKTl+oR/orO40FZrqFuh\nhvyWHSptWknDB61/qX07dlWekfeHlfiFJD2JQJQKO6YsEXnOQ5UDdzrXtAU2\ngXLWoOyKhju8U1l9HHtq87XpQHjlvZ0kfSUFqd3t5fJx0FzddM1VCPMHBb+M\nGbWPCTGoUGkU6hQthywA8cl1kDKAJ0xFutP0xF9eq/TLosbQV1EIptpNef3c\na15P3bbM4ZNmhQjUGdrL8Xqz6LLm+V8bat0bePcz+Ez4Hadtw6meyKjSxUko\nAV1i1N5eFuwfmx7ukeV7zhR6eJHTS58QmiPuzFB8qSTuGtirY7+HIIW8qVWr\nA167VZAqYTWtxaBRujLB2s0BV1FBCyorSBgPDGoDd2xr+xXqohhgEzV1AmfW\nD02yB+TV01XQqch+4gba9OWqIPa5c/QWx3ON9G2r8jYVwuTaPfFqGsD89UF2\n5KNYjB+R31GjZ0in5k9JjboJcaby9d3NG1wesUm2IFP5EnEzujl2U49oAmty\ntNKXfxp4iSsJsnqyHIn0wOn+Lv780ga3cRgd4ctjf+33UQZXdoMWvmdqCNex\nB1Xay8H1GueDtjAizwBrlg6zZsXURUhm4Jlb3BCrosI5C16cc3ks5OUaRnsS\nk/yMLEPJ8qYKjEMah83OSKsou2ukTSqsDtSiC5NUmsfUsEveOXmha3kzCPUG\nmOCS\r\n=Vi5t\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "7.2.0": {"name": "ajv", "version": "7.2.0", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "require-from-string": "^2.0.2", "json-schema-traverse": "^1.0.0"}, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "husky": "^5.0.9", "karma": "^5.0.0", "mocha": "^8.0.1", "tsify": "^5.0.2", "eslint": "^7.8.1", "terser": "^5.2.1", "ts-node": "^9.0.0", "prettier": "^2.0.5", "vuepress": "^1.8.2", "cross-env": "^7.0.2", "browserify": "^17.0.0", "typescript": "^4.2.0", "@types/chai": "^4.2.12", "@types/node": "^14.0.27", "ajv-formats": "^1.5.0", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^10.2.11", "@types/mocha": "^8.0.3", "if-node-version": "^1.0.0", "json-schema-test": "^2.0.0", "@ajv-validator/config": "^0.3.0", "karma-chrome-launcher": "^3.0.0", "@vuepress/shared-utils": "^1.8.2", "eslint-config-prettier": "^7.0.0", "@typescript-eslint/parser": "^3.8.0", "@types/require-from-string": "^1.2.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "54948182f79f2e18c70a501700d78f7a44e6e398", "tarball": "https://registry.npmjs.org/ajv/-/ajv-7.2.0.tgz", "fileCount": 483, "integrity": "sha512-51Na3IUg3uOACsQ7hzTUCjSzGy8xROySgI8tmNJ+y9JF2hfDS6qkTP7+Ep3htUtSQG1t17QMbe+jZFTlaU4dDQ==", "signatures": [{"sig": "MEYCIQCbUCzvpL0ONMaCVoHrt8s0GFhv5338A0xsTRrK2e2dUgIhALvX+L+MA+UFLy+qIln9xYkcpo44y/DAJymXTcFY2/pH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1241049, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgRKRaCRA9TVsSAnZWagAABBYP/0OsoiXZ6unLTw/AF1g9\nNY0nUOoKOdO9sodY9qX/j5jNGLrdi39APSQd/6kSw/L0fm2Ab8C50RNTrgpY\nKgRMpyf1+VkoF14SEzIpojt6dIrEbnIEKc5VFgt8Rrk4NHWweN5yCK1q0o/m\ngRWlM+vT2ET4MufN/5TEbvOyg8yIRX5ODRgYf7Jjg5qYXE93paBikW+Izf43\ngiSOEyhz6KtKBHj0Jl7a/wdIU01TFwYhkeVbZL+vrAEFh12p311ZItGSXgSL\nA6oo/UV9a+GGzgwtfpHv16zoESjO3Xrrxk8Vj9fFMDNYoONbu7CXQcpYOz8H\nKah2bYVii6bhAPP4BPTxULVXxDI1dKMqAIa5DuS7lH8OOwbRs8+Pgk0iZdvs\nToLyx53Dy5KRBJp3SarpClHHJoLwVjw5wLVTlPQjfdBn7sqExckQcngG+bqv\nSWq5Gobmy1RnzvD59PVBB9Gi2W/HZSCHB69E/7n7TpDcPZA5xiyFYZ8axUeu\n2Y0n32gUQWYFxM2pZscAObDF2rvNGPEb2/n85StYz1rL2XuRBRQMQ1wpAyCU\nKrNrzeHW06XI6OZb8wtiUV3fN9Jh8uRezpX7NzjnXn2ZqJhFwi/ossXckg+s\nge4sCSykA/iofPwW2TysVZgsGBJSwbCxqc7m6fA95t6b/iLvm9yWv8u7zD/J\nv8Up\r\n=XBgu\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "7.2.1": {"name": "ajv", "version": "7.2.1", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "require-from-string": "^2.0.2", "json-schema-traverse": "^1.0.0"}, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "husky": "^5.0.9", "karma": "^5.0.0", "mocha": "^8.0.1", "tsify": "^5.0.2", "eslint": "^7.8.1", "terser": "^5.2.1", "ts-node": "^9.0.0", "prettier": "^2.0.5", "vuepress": "^1.8.2", "cross-env": "^7.0.2", "browserify": "^17.0.0", "typescript": "^4.2.0", "@types/chai": "^4.2.12", "@types/node": "^14.0.27", "ajv-formats": "^1.5.0", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^10.2.11", "@types/mocha": "^8.0.3", "if-node-version": "^1.0.0", "json-schema-test": "^2.0.0", "@ajv-validator/config": "^0.3.0", "karma-chrome-launcher": "^3.0.0", "@vuepress/shared-utils": "^1.8.2", "eslint-config-prettier": "^7.0.0", "@typescript-eslint/parser": "^3.8.0", "@types/require-from-string": "^1.2.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "a5ac226171912447683524fa2f1248fcf8bac83d", "tarball": "https://registry.npmjs.org/ajv/-/ajv-7.2.1.tgz", "fileCount": 484, "integrity": "sha512-+nu0HDv7kNSOua9apAVc979qd932rrZeb3WOvoiD31A/p1mIE5/9bN2027pE2rOPYEdS3UHzsvof4hY+lM9/WQ==", "signatures": [{"sig": "MEYCIQCs8tcPApx3rDC1YQCow/VSKJ6Y1yldt+had85eYwZXQwIhAOH+kt+dhPD52JOxObm4cbo8F8dB9RWfZHSkDEgYKCeG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1239266, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgRSNrCRA9TVsSAnZWagAA3W4P/0YNT/vgZ6xrk+Ok+9sE\n9VYQEjsv/3LuoxKU3WwOoKrhpa1hTS8gO7tWTBKWhFPb2E9mxomSkZbJfiyt\nM7lbkWwtxC4QjbvAlWIcf2d5eGz0XAs0iw6v4kep1FeOmvcFX/kq85yxbVwL\n7CSA57yCPlU8G7RG5GNB8UXQtNNQZFfAMLsL5fiwvAoIz9sCYLCLBHn7Ep+6\n5MP55QTVuli5qjz4RkrLMYs6kGgVFXo2rXvuaJLHuYBlB8MAYyB8PwOeiM9k\nI4J7+dpctrVLszrpQwQksxC1jdDT3pZ6AhGo2fXYP8gGeOcf8s50wh7I2B4q\nBPoLxeM9orc/62D/5bNG26mTP5G1adlp60MWn68RCesVxMG1OHACOWeDTHuU\nveRBuMhti9QHcMEsV1UNkFiVbSDLVnqcZ1iGz3OPef9Knt5sHCMgmfR1Hd8D\nvzYQ/sdhxKvrJg63+aPIXR37cJUQ30ccb9c8HcP1TOPmvpH7mLXb4vEuSxSM\nVePL8HJh2i3xdGpEZafzoH5uHsaWh9MN/7boe0QUbvEUdNpJbNYuUhxDEvOg\n94dhboXKHTE3RHC2jshMM6JFFVaRCSsNBBmS+OWA5G35hjUqpCYhfXWBtYtY\ncx3bN513Yn8EaiQNsjneF/oRD5Dwvl0eA9PuRW4GShEn66lEWd0WGEFjO/74\ntVWE\r\n=DKCA\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "8.0.0-beta.0": {"name": "ajv", "version": "8.0.0-beta.0", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "require-from-string": "^2.0.2", "json-schema-traverse": "^1.0.0"}, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "husky": "^5.0.9", "karma": "^5.0.0", "mocha": "^8.0.1", "tsify": "^5.0.2", "eslint": "^7.8.1", "terser": "^5.2.1", "ts-node": "^9.0.0", "prettier": "^2.0.5", "vuepress": "^1.8.2", "cross-env": "^7.0.2", "browserify": "^17.0.0", "typescript": "^4.2.0", "@types/chai": "^4.2.12", "@types/node": "^14.0.27", "ajv-formats": "^1.5.0", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^10.2.11", "@types/mocha": "^8.0.3", "if-node-version": "^1.0.0", "json-schema-test": "^2.0.0", "@ajv-validator/config": "^0.3.0", "karma-chrome-launcher": "^3.0.0", "@vuepress/shared-utils": "^1.8.2", "eslint-config-prettier": "^7.0.0", "@typescript-eslint/parser": "^3.8.0", "@types/require-from-string": "^1.2.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "4d822a9671f73008d418aebdec2f005c58c6111f", "tarball": "https://registry.npmjs.org/ajv/-/ajv-8.0.0-beta.0.tgz", "fileCount": 490, "integrity": "sha512-S3sxD8f96ENFLFguo+Ibo0d1bP30WE+Xw7vu7HHdVUfQW/mapSGGj/0m66vgMxLS4lUe1YxNtKxanyHihTzNCw==", "signatures": [{"sig": "MEQCICeZL5kLM/Z1P87KxSwB2KA6uo84Ulji82fxDA5OHotFAiB22efDHfNU6n2ShhK1pNeYctOAD2wWMhC0tRA/d4kZrw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1259826, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgTJ0pCRA9TVsSAnZWagAAZYwQAJyMZXymuV2qkP1WZFX9\n6ztWCQkdrAt0KWWR0+yOvY3/c8Sp+53ZbGKT4nNHxOwzCvng9baaVko3Nti0\nF7zOwbrnSIwokUDEZisMnGnSzfZYk3FU4zTkOaEi8OlbYDPGz5UYc3zWBdLs\n1Cg8f/0Pi42wkoVkszJgQE4dKZ0nEkS4qQrz/yKvVjCNjSUt4MVFO6o8xRpi\nwxtfskuO5upYiB/W2zxEk295258ROd2AyWA1jZv00LErTg7hNhMxjuEOsQ3G\nl0lFTzwWYvuZqYAEZVIK0Jm0r772y6yKBmhedwydcqfbZHknDVRoVb4pl7Ys\nx7v0CHsKF6NoK2Y3CefSccevxVQhBImtE2BXXcWjB/7BYlfCVRRuB/YXJmuT\nsBU6656UqrHBRwQIQciTmRlTc0Yp9hnVXiVA2aLYRrGWmlNachOg1+TilOW2\niIP5CA24bATt6F3e4HUY7fxQaGRj9skgfU9iCatWns+yWNKqEOHVTVJR6M1k\nXoT80jEFXZXacxqwomu43oj2en08pXMxx5eyu+k09L6rXf3orru9NSn6p5th\ne95+OGeDzzzjdw2bLpEjCxAibWTHkYY4eyVIZbjcyjF9GGbv/2XO8oOFO+sF\nR3CHElqE5zNZZ+5MPJxZm1pkAYUorLe/E59WmguQj18h9sZMpfNZlbR0yMMu\nLLjj\r\n=Ac8z\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "8.0.0-beta.1": {"name": "ajv", "version": "8.0.0-beta.1", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "require-from-string": "^2.0.2", "json-schema-traverse": "^1.0.0"}, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "jimp": "^0.16.1", "husky": "^5.0.9", "karma": "^5.0.0", "mocha": "^8.0.1", "tsify": "^5.0.2", "eslint": "^7.8.1", "terser": "^5.2.1", "ts-node": "^9.0.0", "prettier": "^2.0.5", "vuepress": "^1.8.2", "cross-env": "^7.0.2", "browserify": "^17.0.0", "node-fetch": "^2.6.1", "typescript": "^4.2.0", "@types/chai": "^4.2.12", "@types/node": "^14.0.27", "ajv-formats": "^2.0.0-beta.0", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^10.2.11", "@types/mocha": "^8.0.3", "if-node-version": "^1.0.0", "json-schema-test": "^2.0.0", "@ajv-validator/config": "^0.3.0", "karma-chrome-launcher": "^3.0.0", "@vuepress/shared-utils": "^1.8.2", "eslint-config-prettier": "^7.0.0", "@typescript-eslint/parser": "^3.8.0", "@types/require-from-string": "^1.2.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "d39cf119edece647ac2beaf39796c5dde645e7da", "tarball": "https://registry.npmjs.org/ajv/-/ajv-8.0.0-beta.1.tgz", "fileCount": 488, "integrity": "sha512-Gv2uhXJEeah+8m3LtVW3rm8epYdhMw73O+1QjZsjOLUPoeDEtOnhfFKXk566Dk0L4E3SQ2qmw265qjSZSo3gxw==", "signatures": [{"sig": "MEUCIHdIdAyxoJqpfwITS1f3YAbKyiXcadr1wjyG0zmIDFcgAiEAlPq72oKdM/6Ul0tziLEwK83WEwdUOsLGI4hPxJH8DM0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1265822, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgTxNDCRA9TVsSAnZWagAAFaIP/3gTJi7EDQ76lDV385Th\nZUP05Sd2ihKbOPPoeCAuCqKnzVO0GGT3IbsrOAnGBKVIHEjvAXdUAkRvTtXW\nSrlE5H5a5wd62ovK6VZ+944sWVPi4ep/k7pb/XtYABSMMkMYK0KJAV1nt17O\nWNfWMnQpw6er8EVuoC9huxGai/QkYHiB7bcMci2U4yN8nfQtEiSZPZV1xoh5\nl9dgYlkdSSgHbEPvioiP7NbdgTdMu3KNm7lFLlFo+z5bJ94uBJYC3OovH+Ql\nanW+y3+V3coYzNPGzFdxtaP96UxGWTbmN5260MJ03MX7OP2xpPXrNXR/Yqh7\nnSK/MnJ0k7a4qOOdoO3nmgYqHeit0no//p893HkolNtRL8niYQop6E9aoYB5\nYrKRSbXvnZ169dBdFWUaKoenR+ueOVpTwHhZj+eoZ6Onx9LBzwjolaSTgcE1\nacFDku+kpUUQOO+s3QpODCA8yHiDmKUbWF8+bTj6AzgVt/1WRaY4XQZqmzIE\ndtGJqaO2B/guMJJo3UUilxsxp/m88lSXJ0KcqlxQdMyOV7Cj3GCU43uVEJJc\nbIbM4Z/zagby2kQjRfKlYN6rTVSkJZBtMhGkwP5ocp2yfCYOTLmJjp1u3DXa\ni0401Y5He8S8hQW2LXpUUaWfJI9uV1MR7kDuXjaejvFithAY0L4DSy+cd83+\nzlyg\r\n=7wHA\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "8.0.0-beta.2": {"name": "ajv", "version": "8.0.0-beta.2", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "require-from-string": "^2.0.2", "json-schema-traverse": "^1.0.0"}, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "jimp": "^0.16.1", "husky": "^5.0.9", "karma": "^5.0.0", "mocha": "^8.0.1", "tsify": "^5.0.2", "eslint": "^7.8.1", "terser": "^5.2.1", "ts-node": "^9.0.0", "prettier": "^2.0.5", "vuepress": "^1.8.2", "cross-env": "^7.0.2", "browserify": "^17.0.0", "node-fetch": "^2.6.1", "typescript": "^4.2.0", "@types/chai": "^4.2.12", "@types/node": "^14.0.27", "ajv-formats": "^2.0.0-beta.0", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^10.2.11", "@types/mocha": "^8.0.3", "if-node-version": "^1.0.0", "json-schema-test": "^2.0.0", "@ajv-validator/config": "^0.3.0", "karma-chrome-launcher": "^3.0.0", "@vuepress/shared-utils": "^1.8.2", "eslint-config-prettier": "^7.0.0", "@typescript-eslint/parser": "^3.8.0", "@types/require-from-string": "^1.2.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "3e357bc3944d30a8facc2b89ad2cfb9fccb884fa", "tarball": "https://registry.npmjs.org/ajv/-/ajv-8.0.0-beta.2.tgz", "fileCount": 496, "integrity": "sha512-M0YXx/qvObxf4bG2lCvU14pVLvm5vAd87u/KylxCXrB/5YmDpGk9gACkPDLCWjiz62Rgzahlx1xvdEPdCdhGnA==", "signatures": [{"sig": "MEYCIQCDCN5s5zN/7BjPDyc9nACtIzStVAAhIu5yrg2pnbY6/AIhANI2/mGTAzQDEHS5hEPYXJa6ip7dAyK/kBQXqbLWAbon", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1284713, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgURONCRA9TVsSAnZWagAAGXoP/3sRTKAbfcw3Ghbl0+1j\neEI6AVfvGHqhFSoo+l8S2SEHa1f5dI9+DV2FJo/reP2sb5gzCDKqg8vqAYzc\n9x0SKCuo2ta7LeKXVc6wR/2lHKa1xTr715wmZr1+b5/JWJs+IqRsMAZlmw3o\n9jjg54Ydjo8FS47VdR3UjuDoZVd7h//2w0DJi9IaxByG7Eh5yYX2SYm2YB6N\nOWrXkfhNFI13fTVR7Typh9D+6PuE2hqu7Iew5mAnVBfX6cfjgZDGvc2pedJl\nDarpaVijbxNQjGaLJnCnaIJWm/FHNa647C/8LS8jWaxW+J/c61iNdrVHQTau\nScj2E+RA7/bW5nnZtfEF5FzvFvfjbqA7bxeyoESSB7OE96R5h8PId14HCUcX\neuB+uUPUHQoy5TFB9ID8LNPpZydxY6qsyPtrfZ0OzCnDSsGkbhntzNRrqOUN\nwG8gjZMNLCHjN49EJQIGC4j7vCsDhW4Q9S8rdgaD2RKEgirdhCJ2+mOFhdn4\niYL/KxMDFTLCeWOwmzWtQ7mWvA0IEM0JikNb2GOKT0pybDEj+RPVlPOXYCd9\nUEpAjQqyDVRe9WObjJVQ/keWdeO8Ny4xxYrL0urDZrOjySeaPLwrtjPHnTRQ\nCVooW1IE+eqNgCK/Kwvi0Fd+Z6nXdMgz66PwiwPStFJkKiRvqPwkqNfjHLzo\noNl1\r\n=O0ay\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "7.2.2": {"name": "ajv", "version": "7.2.2", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "require-from-string": "^2.0.2", "json-schema-traverse": "^1.0.0"}, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "jimp": "^0.16.1", "husky": "^5.0.9", "karma": "^5.0.0", "mocha": "^8.0.1", "tsify": "^5.0.2", "eslint": "^7.8.1", "terser": "^5.2.1", "ts-node": "^9.0.0", "prettier": "^2.0.5", "vuepress": "^1.8.2", "cross-env": "^7.0.2", "browserify": "^17.0.0", "node-fetch": "^2.6.1", "typescript": "^4.2.0", "@types/chai": "^4.2.12", "@types/node": "^14.0.27", "ajv-formats": "^1.5.0", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^10.2.11", "@types/mocha": "^8.0.3", "if-node-version": "^1.0.0", "json-schema-test": "^2.0.0", "@ajv-validator/config": "^0.3.0", "karma-chrome-launcher": "^3.0.0", "@vuepress/shared-utils": "^1.8.2", "eslint-config-prettier": "^7.0.0", "@typescript-eslint/parser": "^3.8.0", "@types/require-from-string": "^1.2.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "33e4d3fe9e6ba9f4beb116d08c788160979b2a69", "tarball": "https://registry.npmjs.org/ajv/-/ajv-7.2.2.tgz", "fileCount": 488, "integrity": "sha512-uEsX8jbbSu5MfRDQfao+AGt2QdThdRysvCFo7arlv5e2qQzRaCUV28TcS7TCzfGWD1ZLZIlFV+AOvoqQRARuOQ==", "signatures": [{"sig": "MEQCICrUqy2B1v4n118I75y5bUjY5BtW7EqD/7yNC/ON2DLAAiBgXAWt5+/WWL6bSbAh0j6/eVu2esM0HCwbUgr4bSiBnA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1244759, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgVZmMCRA9TVsSAnZWagAAg5YQAIOIsjgkFms2iG8JjgRb\nC13bqvrecdrjzBQoe2eFu2tirrJknet7/iBIpyUL0IPfIP9PUDcCnfBXvbrS\nZJ/Kvirg9ebK+GASXCdGIBoWBEnPa51M7XUM1JmBCrtPveL3uRRQ/kiYCFer\n+tDB7LiEhBEZ8zlUQm2Gp3phhknHnf4wDqoClQQU9KewW/U+gmxtEpLBxSVe\nXqieU6mmed2kkHt9+uzGu99eKt1nbN+tjzrppY26/ZWr15zuHSsIPsh3W1/8\nkE1PHZTM/Vz0/Hq2f+9twglv4KLz1k3d+WA2xflPc1n8WEOLQuXMU51xsLLn\ncpY4yUHyWn84hHwyhd/aCIuhF+lDZmg1D73NtdBrXsxTDySulL8eCVf5IGrl\nciDt/Row1I+luL33zf3IpUAJDfZ6lNu6oHsiFHVJw1TVZDhK6XvPm60prgh8\nSD3mV8eIvNmyVXRNWTJsqWDstro/BxPBx5pUO+xPPRoG48zK1NzTjQOB8WbN\nXton72D/Am+N+6BcoRedfDprEYFxTvYvPadYmwAdWqOp0WB5VIbFMEtPVnad\noughf/cgFdcnCH/CB9o011a1/MHGGaPsqeSUcwm4QGol11+Mw3jAE8g7AGDL\nWWUkzBMiB9bSKnXxP2zHvwlgBRg7ht0DNIPhS0qYGubaTg9MV6AIG2AuxDjN\ns+P3\r\n=AV3+\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "7.2.3": {"name": "ajv", "version": "7.2.3", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "require-from-string": "^2.0.2", "json-schema-traverse": "^1.0.0"}, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "jimp": "^0.16.1", "husky": "^5.0.9", "karma": "^5.0.0", "mocha": "^8.0.1", "tsify": "^5.0.2", "eslint": "^7.8.1", "terser": "^5.2.1", "ts-node": "^9.0.0", "prettier": "^2.0.5", "vuepress": "^1.8.2", "cross-env": "^7.0.2", "browserify": "^17.0.0", "node-fetch": "^2.6.1", "typescript": "^4.2.0", "@types/chai": "^4.2.12", "@types/node": "^14.0.27", "ajv-formats": "^1.5.0", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^10.2.11", "@types/mocha": "^8.0.3", "if-node-version": "^1.0.0", "json-schema-test": "^2.0.0", "@ajv-validator/config": "^0.3.0", "karma-chrome-launcher": "^3.0.0", "@vuepress/shared-utils": "^1.8.2", "eslint-config-prettier": "^7.0.0", "@typescript-eslint/parser": "^3.8.0", "@types/require-from-string": "^1.2.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "ca78d1cf458d7d36d1c3fa0794dd143406db5772", "tarball": "https://registry.npmjs.org/ajv/-/ajv-7.2.3.tgz", "fileCount": 422, "integrity": "sha512-idv5WZvKVXDqKralOImQgPM9v6WOdLNa0IY3B3doOjw/YxRGT8I+allIJ6kd7Uaj+SF1xZUSU+nPM5aDNBVtnw==", "signatures": [{"sig": "MEQCIFx+gRhjmJ7iVpuQagsuWk5WxvVqMJrC58nAsBmW82MZAiAk11wwe9zU8u0gdrmdzxelY5tEr/PKKpWsFKiFj8Gj2A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 927078, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgVZ2+CRA9TVsSAnZWagAA+JAP/AvYiapJNCZsuRrT4ZIC\nWsjZuI+bXrxCcdpBs2bUkIHQ0XpWtN2OVWm5ta1MvJUVMbv+SnkhcsIDRtAB\nXqskcDoQ7TCoIDjBPKyGrDeq7SiJ79/uacpJLKfWbIGgbqKku5+2qmcM2TEJ\nLbUL+roivOQccA5ev2NBd96lBh/no/4dVul7pc1u/wyJaf4am7NR4aRttS9I\nA9mawiWlEJtc5kV3wufCX0sgxcsqKNV9x+i3TGx4VxCzXqm5lgGySRel9csy\n9mdglzTJMA3gIibVj+7abLRy/BCXvWj7CRkrz84tx4ykBfaZOdhbHJLngS4t\ndR/hbyt5hGQQbzrcHD5efHughbwez6IqVllGSm/IX9cvCyOm/OXmYBpzpbDb\n8yQN4T9SLBzljR4Qy8ECGBINlBtKicrCA8vw7bxMGQBxCd24GEbSemq19fcz\n8d25Qlxr8PX45k5H3GpkyTy5GFt18U/e6DMWiDh1aPi30TypVc0yfNyUDimO\nvvc7z6IntYoPAFS+51lGXagnZQ04Fdc6uJ0wPPDeFkvQO4UZvGMBDKo5qyCr\neQTkH7p72327K78pGmCYFacaaNg5NW3H22t9T2xaB9PsdkYeM5PK+sLCMyyp\nfAaQ+jGLQhfL5HH8uCb2erCVunjrYd7W7Twkxslki244vVPq5+7v8E2IZFCn\nSsV0\r\n=nDn7\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "8.0.0-beta.3": {"name": "ajv", "version": "8.0.0-beta.3", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "require-from-string": "^2.0.2", "json-schema-traverse": "^1.0.0"}, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "jimp": "^0.16.1", "husky": "^5.0.9", "karma": "^5.0.0", "mocha": "^8.0.1", "tsify": "^5.0.2", "eslint": "^7.8.1", "terser": "^5.2.1", "ts-node": "^9.0.0", "prettier": "^2.0.5", "vuepress": "^1.8.2", "cross-env": "^7.0.2", "browserify": "^17.0.0", "node-fetch": "^2.6.1", "typescript": "^4.2.0", "@types/chai": "^4.2.12", "@types/node": "^14.0.27", "ajv-formats": "^2.0.0-beta.0", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^10.2.11", "@types/mocha": "^8.0.3", "if-node-version": "^1.0.0", "json-schema-test": "^2.0.0", "@ajv-validator/config": "^0.3.0", "karma-chrome-launcher": "^3.0.0", "@vuepress/shared-utils": "^1.8.2", "eslint-config-prettier": "^7.0.0", "@typescript-eslint/parser": "^3.8.0", "@types/require-from-string": "^1.2.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "8f859668516e093ac829616ba80152a1818a15a8", "tarball": "https://registry.npmjs.org/ajv/-/ajv-8.0.0-beta.3.tgz", "fileCount": 470, "integrity": "sha512-bgtJ6Qi7Cw9SRf5ghhJO8TpQSFR21fI8Y+j2VT7NxPY19klb+aQrHqat/KbAIduEmDV4cZgWFrTbEf4O2oCeww==", "signatures": [{"sig": "MEQCIBqFMzRjO7SFkpBirKRe/qzlAy/+p9Zj0a/D0KCvm4kpAiB88OjmsfTcIEA5RNN/ExQuT91uiz6wk4k7fUDbMgBu3A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 998452, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgV5QVCRA9TVsSAnZWagAAfX0P+gNZcYup83qkH2zjl5bm\nOeb/N3dSu+JLwi5Nc5docxB1Cwjs60w3vvzCYAqEPEYNzYehUe6TzQQ3EPqc\n81+0TXhnBxs+LALn8L4b6nMQ0UHIh2MMF8334x8tQzWRM7p+bG2z9hTS0ikn\n0e7hvS6auA7Ka+P/MUmHcftYs2faCmAt2u4LKRBKNl90PAtLrO1m7irNLqxP\nna5FLFuoqEUxngWQrZkOrU//08uYlEy1zvd12+Va/oQZ6E6Jm29e3QpSEzzo\nlIsMP8i34WrbgZEz5L+c0Y0jrq6Zmk4oAZyA6P7P/81lYZXp3oocRzZdidpy\neG8ArPtTqq4PW8pw4F0caLaat8PBILY/7cEJtagqBBoGJ1yhMvNyrqhII4/6\n9AfrDIuQTlSDKhKV8od8NFsIxCjGxV5sapHiD1FnuewMH+y/E6e5b/CGl7S6\nGANTZotlplhRZ5bWlUz4PkwlJPCjIIp/9ZPs6Q8vlP+CXA0mD/hYKGfs77el\nm4CBfYB/TDh/7g04HzYjb215WCQ9TdSUCexsOXjLRyhKTs6JJymrGlsHaBGt\nhiz9aToIW7sD6mnm/Rr0i/c1ML8UpXYj/fFfEbzF4E70NjbRSE0ypN3mLXiM\ntpZ5hXuwlqf0lxMaZcG1tweUanDB1nSwljX2vrzAIgAQAAgy+nu0gb4Dl8WV\nuZJW\r\n=TlW9\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "8.0.0-beta.4": {"name": "ajv", "version": "8.0.0-beta.4", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "require-from-string": "^2.0.2", "json-schema-traverse": "^1.0.0"}, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "jimp": "^0.16.1", "husky": "^5.0.9", "karma": "^5.0.0", "mocha": "^8.0.1", "tsify": "^5.0.2", "eslint": "^7.8.1", "terser": "^5.2.1", "ts-node": "^9.0.0", "prettier": "^2.0.5", "vuepress": "^1.8.2", "cross-env": "^7.0.2", "browserify": "^17.0.0", "node-fetch": "^2.6.1", "typescript": "^4.2.0", "@types/chai": "^4.2.12", "@types/node": "^14.0.27", "ajv-formats": "^2.0.0-beta.2", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^10.2.11", "@types/mocha": "^8.0.3", "if-node-version": "^1.0.0", "json-schema-test": "^2.0.0", "@ajv-validator/config": "^0.3.0", "karma-chrome-launcher": "^3.0.0", "@vuepress/shared-utils": "^1.8.2", "eslint-config-prettier": "^7.0.0", "@typescript-eslint/parser": "^3.8.0", "@types/require-from-string": "^1.2.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "9f6779a0dac21a2ac0e1ca9604276eb0c9bf63e4", "tarball": "https://registry.npmjs.org/ajv/-/ajv-8.0.0-beta.4.tgz", "fileCount": 478, "integrity": "sha512-M4SfW/InvqHRdSKcgVttiVzN824DOhX4fHYvSWjO1wyEz1+9AdZGbV0o4IA47IverLnyrZxAAd1w5B7rjI3Xhw==", "signatures": [{"sig": "MEQCIA/8KwqswGNsE3fNMq39IGdnP21Lo/O7r84oABP5rwRMAiA4jb71e8ymUrG4HPco5zD4bPnt+hKG9oliIgf3FJ+5lA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1036616, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWZqhCRA9TVsSAnZWagAA8g4P/j7y8wcOdFGZe3z6lGrH\nUdfLwRENr+HNrQ7NDGI0uIXp+axLCxv0lNrL4KhRjp+5NmjE7ULoXR7PD3WE\nsrNVDcrnRB0fakkecEA3h72fokO1ni796b4SfKvPzIGmP7RrYCJQPInjo4eL\nbo7RJ8oLg8kXcdk1097BTb3xrkdOkiVSEeKdcYN925Y2kKa9DlSnXZG7EHWE\nc7JbYMayl32GqnGMW9sFUDuQ52j4NFm91xH2lF1SqWuchkPMp+JoxYRgIRiO\n2YIQ3QA3MxzVpt1qjaTCD8b9ef4aknquFlpY1FrLusvzqjUIOrp+Wh+dmaFu\nEA8IDdmhhRgMiPx6UlXGWDkvImMhtGW/sNYHlzVDMkxN3AhuQsLPoYeeVIfp\nXckE6bmSbSI5S5QX6Xk1r7JlFLEnZYT2fXe6JIqgtnKcWJy0fXK7jeVRiaF+\nbuoVn5Nn74jGbPdkvie7AAjXL8EoCxDhm0xcPf4EGtv7eg5/U7Gntr5XZ38y\nS4VCcz8omE2+ZSLh6kBdlyCrg0syGhOD3pAVy9dFMdALuT8UCnXbby9uU6In\ninfFxMtcbn3H6ioNYJ4lPCCG1YAPqUPiKzBlV9FAhEIladZvPooLjcvUYlmU\njWjiz0RVH7XdNNyOeOKbF1BDWcQwtwAg7EgPrnINeTB4kOgww/DOcIsR7lkR\njYSG\r\n=9C6X\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "7.2.4": {"name": "ajv", "version": "7.2.4", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "require-from-string": "^2.0.2", "json-schema-traverse": "^1.0.0"}, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "jimp": "^0.16.1", "husky": "^5.0.9", "karma": "^5.0.0", "mocha": "^8.0.1", "tsify": "^5.0.2", "eslint": "^7.8.1", "terser": "^5.2.1", "ts-node": "^9.0.0", "prettier": "^2.0.5", "vuepress": "^1.8.2", "cross-env": "^7.0.2", "browserify": "^17.0.0", "node-fetch": "^2.6.1", "typescript": "^4.2.0", "@types/chai": "^4.2.12", "@types/node": "^14.0.27", "ajv-formats": "^1.5.0", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^10.2.11", "@types/mocha": "^8.0.3", "if-node-version": "^1.0.0", "json-schema-test": "^2.0.0", "@ajv-validator/config": "^0.3.0", "karma-chrome-launcher": "^3.0.0", "@vuepress/shared-utils": "^1.8.2", "eslint-config-prettier": "^7.0.0", "@typescript-eslint/parser": "^3.8.0", "@types/require-from-string": "^1.2.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "8e239d4d56cf884bccca8cca362f508446dc160f", "tarball": "https://registry.npmjs.org/ajv/-/ajv-7.2.4.tgz", "fileCount": 422, "integrity": "sha512-nBeQgg/ZZA3u3SYxyaDvpvDtgZ/EZPF547ARgZBrG9Bhu1vKDwAIjtIf+sDtJUKa2zOcEbmRLBRSyMraS/Oy1A==", "signatures": [{"sig": "MEYCIQDNJKqAdfaVQ30SayRh2YK1iqXKI/35kZhirz0fRDRtTwIhAMX/6pxnoLEtmdCYC/PWgoM4ro2yynd9Vv2LeW2XbRbA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 927198, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXZkVCRA9TVsSAnZWagAAw1YP/jk7Js2lJcqcE55VRo1K\ngOzMriEjh7AJD8qbTG5X4BERkgv32c8eDxa7zb59xwQ07JPwUQWk+auyHTVR\n0ZC0aXute717jyZahx1q1VyOvoXwuAbJDtbSwxo95JPtbZdr3U3ylsV/mg8R\nciNU8p12Okwvos53bjppIr/QW0CoJK2128yDYGZmQwoP8k/FCvcoyTL3x8Ge\n5YH9ROkk7tua9mj7nlpOY7lr5rO0FlY8yZLQVJBASk3YlQsmE7Ztrwy65g92\nFJ9ru4WmV4dq2H3Q/c0pXr1BG5Wx3WfirYsOi0s9KuuF9NgyHdpyvelHTxb1\nMkueJJjRIxjvuf0TMyg8YAtwwWfPfXOXfd2LKEcHPV7AGSBaHfwbzJX+8cvR\nEkUuEvgUKIovdLZ4LZKeG3XbsDH5hXeYJu8CX8STuySWm10/6fpz+tJLjZXj\n1uOFhMza11ZT7VeSFYFLdIF3OcpSbOIzbFNnhIgKrttsvaTbAicDi6+W/dYn\nXtImw5EGkES0/SMMk1RVBEOYk2/M1LjnmIC/eIDHwtQotZIph/k/R4K4y9MN\nsqd0MaF+8kFWJzqfjXTmbm2sWUIWFVNySrmzS8fyn1g8PX28V0RoQAk/TElw\ntoliF60Aqt/UadoHeG7io0TJ87IAkuwbJ9DX0zAa3HGAOEkYhazrCNi6KqP3\nTAbi\r\n=yhsH\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "8.0.0": {"name": "ajv", "version": "8.0.0", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "require-from-string": "^2.0.2", "json-schema-traverse": "^1.0.0"}, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "jimp": "^0.16.1", "dayjs": "^1.10.4", "husky": "^5.0.9", "karma": "^5.0.0", "mocha": "^8.0.1", "tsify": "^5.0.2", "eslint": "^7.8.1", "terser": "^5.2.1", "ts-node": "^9.0.0", "prettier": "^2.0.5", "vuepress": "^1.8.2", "cross-env": "^7.0.2", "browserify": "^17.0.0", "node-fetch": "^2.6.1", "typescript": "^4.2.0", "@types/chai": "^4.2.12", "@types/node": "^14.0.27", "ajv-formats": "^2.0.0", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^10.2.11", "@types/mocha": "^8.0.3", "if-node-version": "^1.0.0", "dayjs-plugin-utc": "^0.1.2", "json-schema-test": "^2.0.0", "@ajv-validator/config": "^0.3.0", "karma-chrome-launcher": "^3.0.0", "@vuepress/shared-utils": "^1.8.2", "eslint-config-prettier": "^7.0.0", "@typescript-eslint/parser": "^3.8.0", "@types/require-from-string": "^1.2.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "392f0aee6a4b6ee8367f5b0bf516e43210762ae7", "tarball": "https://registry.npmjs.org/ajv/-/ajv-8.0.0.tgz", "fileCount": 467, "integrity": "sha512-AgKgeO3Y58RMZoi3S+6OYqGjndt8VlrCwGM3bQEMtbGTLOO+VrymV1Fb0TnffzG4gSwa6MEgIreOn9y3Q9rt+A==", "signatures": [{"sig": "MEQCIDcY6pYSb3nS+t/lxi3csI2BoEb0xT8/vMmOnGCFdIcBAiBeRky9OirBB/4lUBWenotLjFlWWfQqy95oAMqcilz7zA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 985539, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXyiZCRA9TVsSAnZWagAAFv8P/1GqaRqICQMKkvGiyBj7\nWrnSaIWD3khl07mB+r0mXQvMqCUUxfZ4dG1B1tNoftfxesvPBMYsHEGK0cCI\n2tWON7F+l3VVjtdslbgWiIWAlbg1Xlh+jZA76EIdSei+ONzgkdnVJmIiCydj\nsuqkMP/DgsKoLqSrJc7BReerajNY3uycGP0nYZBp3erKIhS/5P8NfC47YpFo\ncGS4ZY2Vj8cn/LAXx1AWis32Ix24Hj/jjM+n5TnJh1v/+Xs3aQpPp8+Q9HTD\nrR0hINVmLdhIDzydF221gA4xJlewGc5Ps2K9fnkBQqFJqzzTnKZ60VckOlrd\nUGRClo5aNEHL6Z1wUGL2qmci7yU/JmqjL2FeLqwKXI3Gb+o2+zilRzZsfRpP\n/+fS0MTW6FFe8sKHjdlklcG2EPmUuklnsW9OlUJjH1lM4SV4YupRGpIKM9yl\nhuuCxmsAOqICzw57JXM1F3YzYObmUS0o2yZzDBLa7yjHe6JEjIEk1ham4+Ul\ns9FdnnLa8ih8fX0YPIDLx7j+NVlyKt/DBp3PbAET31ryzsI3mbq54lnVWB7T\nNtWC3vgruEdHXk8aeMT17X5eap5bEfFlqNJa9Brt+kXlcVLkuk+p21jF14VN\ndEX7mFzrTjng7JMtZG+2sfrVOyQdBlf5Og79CG2+TbBsOEdHrXGT6LX2LRzx\n9agx\r\n=/ZGD\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "8.0.1": {"name": "ajv", "version": "8.0.1", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "require-from-string": "^2.0.2", "json-schema-traverse": "^1.0.0"}, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "jimp": "^0.16.1", "dayjs": "^1.10.4", "husky": "^5.0.9", "karma": "^5.0.0", "mocha": "^8.0.1", "tsify": "^5.0.2", "eslint": "^7.8.1", "terser": "^5.2.1", "ts-node": "^9.0.0", "prettier": "^2.0.5", "vuepress": "^1.8.2", "cross-env": "^7.0.2", "browserify": "^17.0.0", "node-fetch": "^2.6.1", "typescript": "^4.2.0", "@types/chai": "^4.2.12", "@types/node": "^14.0.27", "ajv-formats": "^2.0.0", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^10.2.11", "@types/mocha": "^8.0.3", "if-node-version": "^1.0.0", "dayjs-plugin-utc": "^0.1.2", "json-schema-test": "^2.0.0", "@ajv-validator/config": "^0.3.0", "karma-chrome-launcher": "^3.0.0", "@vuepress/shared-utils": "^1.8.2", "eslint-config-prettier": "^7.0.0", "@typescript-eslint/parser": "^3.8.0", "@types/require-from-string": "^1.2.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "dac101898a87f8ebb57fea69617e8096523c628c", "tarball": "https://registry.npmjs.org/ajv/-/ajv-8.0.1.tgz", "fileCount": 467, "integrity": "sha512-46ZA4TalFcLLqX1dEU3dhdY38wAtDydJ4e7QQTVekLUTzXkb1LfqU6VOBXC/a9wiv4T094WURqJH6ZitF92Kqw==", "signatures": [{"sig": "MEUCIFuDQ1F1NpyL0eYI03ERv/quneJsh5JE6IvaXeuSAFxdAiEA/nd9evG7NzXk+5qCtm88/Eo+h6lgNyuDNWfZCpi1bFk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 985725, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgX7YNCRA9TVsSAnZWagAA1VcP/13GKSKzGg3wLYTYyurJ\nrkCAT+NJ/JrtnIC1f2HSqgxlEcnVuIU8nqafs0eHaRdoiJfLc6Hs3zMqIUAx\nwQ7CVX2vgk6D5ijS2e9nMpcqq1bgoALUIrk7qM56vYN/X53Hj+an2KDcJnyT\nzHPQdM9SDgY2QLRg48f+h7g8/iJVDGxkDoSwnwXBU9imSMNCphbeN1H0d/yR\ny/e66EIH7yQ/AN1Wa916OLbaAAYoE0ypzx+PSP4UJMsgLU6NV2+6DFm5/iOZ\nMTc2BlnbdUdF9l034pkXO/HA8R6n0MCy3glEL8k21cpzB5fDlEKQUXogD6fz\n7okTGeiarYOqsCznv4izmaM3F+KkbzOapPh2neiv//qfjwBIWLlYvBIO/CaA\nuExNtArnyzP7UrjyzWvcZdHyPrpJXbzvxIyuLcY6Dzt81nwG0A/I1TEbfSr1\nQGDr5eTii21vzPulnHS0e0C+EGva2M4PdiMUxi80AK6Ts1KtrXvUlB8xTEoB\nC/DWfPU8OQvWhuuinKNAdrB2zwx0yzkB1te18xwe4W6CB9QZjunWYIdmnjSR\nq04NLIw6M3Iu/aCsWrrnhjI+orDEhfvyRGWoZf1IkC3iChy3wct0zdIdEJaO\nzodIPJs3VCMdIkISMIFDclyKd5R9UaSmjpATzJzxyj64Cmb2VpvqBajdqnBg\nzJWc\r\n=ZuFv\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "8.0.2": {"name": "ajv", "version": "8.0.2", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "require-from-string": "^2.0.2", "json-schema-traverse": "^1.0.0"}, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "jimp": "^0.16.1", "dayjs": "^1.10.4", "husky": "^5.0.9", "karma": "^5.0.0", "mocha": "^8.0.1", "tsify": "^5.0.2", "eslint": "^7.8.1", "terser": "^5.2.1", "ts-node": "^9.0.0", "prettier": "^2.0.5", "vuepress": "^1.8.2", "cross-env": "^7.0.2", "browserify": "^17.0.0", "node-fetch": "^2.6.1", "typescript": "^4.2.0", "@types/chai": "^4.2.12", "@types/node": "^14.0.27", "ajv-formats": "^2.0.0", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^10.2.11", "@types/mocha": "^8.0.3", "if-node-version": "^1.0.0", "dayjs-plugin-utc": "^0.1.2", "json-schema-test": "^2.0.0", "@ajv-validator/config": "^0.3.0", "karma-chrome-launcher": "^3.0.0", "@vuepress/shared-utils": "^1.8.2", "eslint-config-prettier": "^7.0.0", "@typescript-eslint/parser": "^3.8.0", "@types/require-from-string": "^1.2.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "1396e27f208ed56dd5638ab5a251edeb1c91d402", "tarball": "https://registry.npmjs.org/ajv/-/ajv-8.0.2.tgz", "fileCount": 467, "integrity": "sha512-V0HGxJd0PiDF0ecHYIesTOqfd1gJguwQUOYfMfAWnRsWQEXfc5ifbUFhD3Wjc+O+y7VAqL+g07prq9gHQ/JOZQ==", "signatures": [{"sig": "MEUCIEv3RUkOq37H02WkPdaZF2lTCn1zDmJzE48xs+EkthkGAiEAg4vHigPWBV1jrJHSRIqNtbzGYxDKIQoq6+wQAYYVnUw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 986792, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgZCwSCRA9TVsSAnZWagAAHTgP/3dfcNOMYHxTn/qBwi/C\ncVINUw0imhxIYtU4jEpXuVg+SRLey4n1HhyJUQU81fE4gIuRDL0X4q4qsbOM\nY72109mtFNjwZNaGm2KWF+d5ZfwDE24K0QwvwaD0LjSVwErKeUW63pgelEDm\n9Tfyt781Vw+t5cqJXWjv4oqH4uqXU4BLnFzzMqLzEjZIjWfkLTpOdMmMuJi9\n+nobdIhCTGHSCNimRaOYxD0syh1vYgRjzPERjLixerFDZvtgcCg5MV6TtIWJ\n/C/vtukvdK21kE8nhBmaU+p2WZxfOIFbCQbkEXJrBU0Ry6UnyqWBDLDoBdZJ\niNmyx4lhN/cyJggje7K0CTAXMHKMd9P8LorWCtOdKOAkAveLxU+sDhaFLFT6\nU3mv3laWrYDdQ7OkEFsUeVNWBF720sSsu3ohvXiNrCZG/mV9H46arYiIqgh8\ncrqHAURTcMGp2SgKAQVVcTI5tjwfzbx8OypGvskdbvExXn9g2XnQCWSguJhS\n+TuEllgxW/9pRICjWpo2t5Y9/yukwQKoM/897XlWnxrPnnFoWtxlIfq95i5M\nmllA67QRWbvFtk3l3ezjmMqjKubKi2K78c7Ph55mtfCC4xlShz52FDgn0NCC\nUUL9TjfL5MPve13StG5oEOWLCuMOcrhPwDZQtdpUaftZj/FThOZebHeSzGPw\n6wTq\r\n=sGU5\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "8.0.3": {"name": "ajv", "version": "8.0.3", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "require-from-string": "^2.0.2", "json-schema-traverse": "^1.0.0"}, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "jimp": "^0.16.1", "dayjs": "^1.10.4", "husky": "^5.0.9", "karma": "^5.0.0", "mocha": "^8.0.1", "tsify": "^5.0.2", "eslint": "^7.8.1", "terser": "^5.2.1", "ts-node": "^9.0.0", "prettier": "^2.0.5", "vuepress": "^1.8.2", "cross-env": "^7.0.2", "browserify": "^17.0.0", "node-fetch": "^2.6.1", "typescript": "^4.2.0", "@types/chai": "^4.2.12", "@types/node": "^14.0.27", "ajv-formats": "^2.0.0", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^10.2.11", "@types/mocha": "^8.0.3", "if-node-version": "^1.0.0", "dayjs-plugin-utc": "^0.1.2", "json-schema-test": "^2.0.0", "@ajv-validator/config": "^0.3.0", "karma-chrome-launcher": "^3.0.0", "@vuepress/shared-utils": "^1.8.2", "eslint-config-prettier": "^7.0.0", "@typescript-eslint/parser": "^3.8.0", "@types/require-from-string": "^1.2.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "81f1b07003b329f000b7912e59a24f52392867b6", "tarball": "https://registry.npmjs.org/ajv/-/ajv-8.0.3.tgz", "fileCount": 467, "integrity": "sha512-Df6NAivu9KpZw+q8ySijAgLvr1mUA5ihkRvCLCxpdYR21ann5yIuN+PpFxmweSj7i3yjJ0x5LN5KVs0RRzskAQ==", "signatures": [{"sig": "MEUCIEY31Pjia+GLn7Wx1fLqtVozYs2TMpy6Cm+9BxoPXiCdAiEAv93QkN9BmtkbjY78j8XKgAcVQ7s2FCouAeoBV1pUVfo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 986930, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgZXCECRA9TVsSAnZWagAAS/sP/3zRVlsGEQqEi3LvDmhJ\nFvwMZIgnALIcHF2GlZ2WidJHAhWGfzxzxduq66Sp91GOhLrzpVmow4Qdne/6\noGFEMZzvKiIDJWQMff/Y6TnIC0hVHpwnhbFom0hwVxCE8i58v1acfxfwUj2N\nVwEQT1gIA6rtEAZb1yiiJbXjtr72jyWz4wpaxRhd1z/S1u1pHMrt8PshSHbr\nw3IgTmvnLPfvhiEwUdqFtgTpKdIVsU3otzEVaXsNnaw7SWE1JSZtOphz9BNk\nsZa4rDCXZK8YSd5hGh5OJvSCaODtRTyhrDml4yDNAR11mfV4i/km6U+znj3Z\nKPwzZFN/llwxHyI/h7Bc4cQysyRiAaKoVCDwg9JFohfeeQebmLXVah13ZmE+\noz+aGfhQtKq0gZOuuRaLq0YEDveKMgABzF/5v25U0sk05yrNKCsdIlspH1xV\naRazKZ3YpnPVLM8IkCEGKJvPqdUyn7qT5BWK5EoGH5FJANJOeZScll4r512i\nHY/oc4NEw6HXCR4GMm7riPXxkbugDfTWWE3740F1xorGRr6GP27cSg5J1Ijv\npm3KsZnls7nzJOb56lDizWOzIBSTmPQyVTxoBSffcQWIA7OG2SOpKlaCPA4U\n1P9F4um2DaCb+8jKKCpUfA14YejIpsHonKu5EXoYZqFdzfx6GJdH2sNJXnOX\nj2KE\r\n=rT//\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "8.0.4": {"name": "ajv", "version": "8.0.4", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "require-from-string": "^2.0.2", "json-schema-traverse": "^1.0.0"}, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "jimp": "^0.16.1", "dayjs": "^1.10.4", "husky": "^6.0.0", "karma": "^5.0.0", "mocha": "^8.0.1", "tsify": "^5.0.2", "eslint": "^7.8.1", "terser": "^5.2.1", "ts-node": "^9.0.0", "prettier": "^2.0.5", "vuepress": "^1.8.2", "cross-env": "^7.0.2", "browserify": "^17.0.0", "node-fetch": "^2.6.1", "typescript": "^4.2.0", "@types/chai": "^4.2.12", "@types/node": "^14.0.27", "ajv-formats": "^2.0.0", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^10.2.11", "@types/mocha": "^8.0.3", "if-node-version": "^1.0.0", "dayjs-plugin-utc": "^0.1.2", "json-schema-test": "^2.0.0", "@ajv-validator/config": "^0.3.0", "karma-chrome-launcher": "^3.0.0", "@vuepress/shared-utils": "^1.8.2", "eslint-config-prettier": "^7.0.0", "@typescript-eslint/parser": "^3.8.0", "@types/require-from-string": "^1.2.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "7dc77632eb5dcde646b3aa18dd3bfe0d8965f379", "tarball": "https://registry.npmjs.org/ajv/-/ajv-8.0.4.tgz", "fileCount": 335, "integrity": "sha512-v1qwknPv7rNGqtiaC4ywb3OZ3LNrEjbJL5igAe8eTbXOj8ye0XVul2pFRulwl/j3QfUKdQ/J9HZaYfQCnR7cvA==", "signatures": [{"sig": "MEUCICYZ6WWGGqBl1w1EOsjtp8vpHVo0XngwEDWLXx5YhLSVAiEAivGvOurhLYMrCes/2XUxhS6Ut/DY7rXIx0C/3P1waDI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 657421, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgZxPfCRA9TVsSAnZWagAAip0P/005O6aUyMLJRCFwOxBI\ni/e7C3Mpgkd+JvaEEVu45SjRZPqdkXwCSMYr/n1avzNSCrzFD3whM9Xk+DU3\nRia10HtrMjgHZnn4x2lTMtf5f6kcAcx3iUqEb2fVFUC6uY2KYBZHabW2HTu8\nQb0eNqRDWLahXGjeDf6xsA5pr7OFcRJr/YJsiCCn4RRe8yxNQBT4+++emz9/\nLkl62CydLzk2J7TMEtlOEKlRBpvjGqYh8jDTAxiRGzby+Rb66HD3ox3IoM6Q\n0mR0PvSAdUCjLZCQw/5gekD71Xp3ecMHXHFpQwPd9I0BgO1vTwRtFuce32tP\nSXk2eYsXQq45bJ67MO4LWf0smsmLyrLI7c11p/H6gU7ItFoBesxd0Q9Z39is\noQtRTX4TEoGFnwXjCRvdQpVMyg+E+VwyR7J3XK7jp1i2ja9UaWLG7L+fIxh9\nEnJ2zF0zQLMAeFve8JpiUXWB6h1dH5w3NxoUZLSvY68UTwJEv+1uB1eQC4th\ngp8WU79fRM4rG1KhGUxTcr1bXa6JIq8NccPebT9FyPE7hhIJEP7zJFMQ7m15\n+CWPcBOO+A82CQPKaKmnA8y18s/vnUyx1RzYnW5/giSjhGjN7Ix7P/X2BAyw\n4fn/+v1KMqOgA0WzBxCqq0q3i4tZRlV27ljzzl2Ph3MBCzK2WtmCMDHaK3tH\nNJjB\r\n=qGVY\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "8.0.5": {"name": "ajv", "version": "8.0.5", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "require-from-string": "^2.0.2", "json-schema-traverse": "^1.0.0"}, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "jimp": "^0.16.1", "dayjs": "^1.10.4", "husky": "^6.0.0", "karma": "^5.0.0", "mocha": "^8.0.1", "tsify": "^5.0.2", "eslint": "^7.8.1", "terser": "^5.2.1", "ts-node": "^9.0.0", "prettier": "^2.0.5", "vuepress": "^1.8.2", "cross-env": "^7.0.2", "browserify": "^17.0.0", "node-fetch": "^2.6.1", "typescript": "^4.2.0", "@types/chai": "^4.2.12", "@types/node": "^14.0.27", "ajv-formats": "^2.0.0", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^10.2.11", "@types/mocha": "^8.0.3", "if-node-version": "^1.0.0", "dayjs-plugin-utc": "^0.1.2", "json-schema-test": "^2.0.0", "@ajv-validator/config": "^0.3.0", "karma-chrome-launcher": "^3.0.0", "@vuepress/shared-utils": "^1.8.2", "eslint-config-prettier": "^7.0.0", "@typescript-eslint/parser": "^3.8.0", "@types/require-from-string": "^1.2.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "f07d6fdeffcdbb80485570ce3f1bc845fcc812b9", "tarball": "https://registry.npmjs.org/ajv/-/ajv-8.0.5.tgz", "fileCount": 458, "integrity": "sha512-RkiLa/AeJx7+9OvniQ/qeWu0w74A8DiPPBclQ6ji3ZQkv5KamO+QGpqmi7O4JIw3rHGUXZ6CoP9tsAkn3gyazg==", "signatures": [{"sig": "MEUCIQD++bp9pUeiuAJ9vcAN4dRPQ/zKYuJNqaxQOmxpmpQTMgIgSeWXMgboUQ33t4NwayN8vVfYXKU10qp+nSH1cTPGtMg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 979565, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgZz4cCRA9TVsSAnZWagAAPzAP/RKxknmY2CmCYsXDDlhr\nLsWdnI6Ek35rfrUQfH6xseTvlvjUhb3hySbiUh4+3MH8VQpnBEGhLC80tJ9S\nvAVwfi9pa+MT/RnfEgZi6vx9GA4lLIA/xNyHCDN2RZUSrl6bM/0Dk7eJi806\nwO+QVCMr+vT7GfzrRMw3mKnOn3XJMoj0cmqcT7UyJFgB4URLtxaZ0frCnCXP\nZ3nfIsvuE3P7O/POBNUhHCXOblJJL7q1CAgHqAPyK2Y4lfb92U5D6u67cBn+\nY0X4a73thrxYsN3Eis0m9ziQ8Vu/WQHGYeE/wnCjdIamn3AWBIlznJ2oU4cP\nZgrzQnkRcl4by9hQ24NGDhE+wWsaxEJgSEPPHcpjODml1FYfrePFsXk3PzrA\nIwUgkQ7wMrORQRA4qZgzkttqgdtQqgrwI5UzZeealOyfznkkPi2nyWB5i2WM\nhkdHrS/J+GjESCQdCwqEw0oD7gbKN00wNax3o3tXh2UW/zgGDjAr5KIBF+6E\nsJg7wcGy9qosmOK62b3wg4FqN0s/aXrg3wNL1w9uTgU5jdj+1rRPSlN06kmF\njD/BivOK+uxrrD+ZGalTBezRc50ITjdFDuyakEPrF6FMs2b0/rGFmrLuebso\npSW6quOCAVdXkt+XCgpFZn7ZC/aCax+2kibBnEGpOqXe+WgY+t+UQJ/69Z0z\nibUx\r\n=iMZC\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "8.1.0": {"name": "ajv", "version": "8.1.0", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "require-from-string": "^2.0.2", "json-schema-traverse": "^1.0.0"}, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "jimp": "^0.16.1", "dayjs": "^1.10.4", "husky": "^6.0.0", "karma": "^5.0.0", "mocha": "^8.0.1", "tsify": "^5.0.2", "eslint": "^7.8.1", "terser": "^5.2.1", "ts-node": "^9.0.0", "prettier": "^2.0.5", "vuepress": "^1.8.2", "cross-env": "^7.0.2", "browserify": "^17.0.0", "node-fetch": "^2.6.1", "typescript": "^4.2.0", "@types/chai": "^4.2.12", "@types/node": "^14.0.27", "ajv-formats": "^2.0.0", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^10.2.11", "@types/mocha": "^8.0.3", "if-node-version": "^1.0.0", "dayjs-plugin-utc": "^0.1.2", "json-schema-test": "^2.0.0", "@ajv-validator/config": "^0.3.0", "karma-chrome-launcher": "^3.0.0", "@vuepress/shared-utils": "^1.8.2", "eslint-config-prettier": "^7.0.0", "@typescript-eslint/parser": "^3.8.0", "@types/require-from-string": "^1.2.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "45d5d3d36c7cdd808930cc3e603cf6200dbeb736", "tarball": "https://registry.npmjs.org/ajv/-/ajv-8.1.0.tgz", "fileCount": 458, "integrity": "sha512-B/Sk2Ix7A36fs/ZkuGLIR86EdjbgR6fsAcbx9lOP/QBSXujDNbVmIS/U4Itz5k8fPFDeVZl/zQ/gJW4Jrq6XjQ==", "signatures": [{"sig": "MEYCIQDOWPTXo53rjt+4rzB54ZDoARLxZV4uRs7a5BTorTmnIQIhAJvguHB5e4Jrs74nAvJhYJodt1z22rIUsy6xRa0cSW/Q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 980522, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgcylACRA9TVsSAnZWagAAObEP/jqDELz9d8KSWuuZe7NG\nIhYLxtX6UZw5ETimXgMebaQA0GMaD++s9iVuNOtjfcUtt9Q4jkugpqhDIpFv\n9lF6NI8kciK+pGVlGlng2X45k7hg+zlL8JWIekRWoFZ+80xc1AaPiV69mXWc\noWea9FVxiYs5FpQog2KIxA6EoPf5ne8Yu0lW4SJASt7iCEbQos3APzBmTVrz\nIZVVPnunSqdaXOqr1rOkRJFLjHHT+HHcXjNFw3TpUQKULwBl8zEu6jtSGLkl\n9qNRXhGbPNui0UHGiCsYd6prI0qsLdufTxW0eAfAQiush5QQJEGSk8Y9IGuo\nbai11J12be4g5f8FcCkHS1loM+aI5Pe+c6YB8zaqxCPX4O+vMpi4YyGqRuAO\n3d7ZcrVIIDtFQMzX0vtMR1CgQCm4sU44Zx/oDjBAYD7dZ/1UhRgrER/iL84E\nzhGff21C4b+2YYl8a+Wh/PJfZNe4bbozBE7Qq0swvJRIb9QnnBQOMIllPrQQ\nH/UqnYVKoPzQEi67IdeQni69ojjwV4wIjVhdJ9f/sx0PIrl5ix19TBuBGr1v\n1SsFLjR763FTmR3ZqTuO++FSAYIXKY8pmjdgFReyahv0ujONXcRLm2yFi91C\niloI1yN2HBaYuMxlbwe+PiQ0s+3iHZd5o7zvAeQL5pQHi5chXtik7WyBI25W\nIcr9\r\n=fARX\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "8.2.0": {"name": "ajv", "version": "8.2.0", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "require-from-string": "^2.0.2", "json-schema-traverse": "^1.0.0"}, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "jimp": "^0.16.1", "dayjs": "^1.10.4", "husky": "^6.0.0", "karma": "^5.0.0", "mocha": "^8.0.1", "tsify": "^5.0.2", "eslint": "^7.8.1", "rollup": "^2.44.0", "ts-node": "^9.0.0", "prettier": "^2.0.5", "vuepress": "^1.8.2", "cross-env": "^7.0.2", "node-fetch": "^2.6.1", "typescript": "^4.2.0", "@types/chai": "^4.2.12", "@types/node": "^14.0.27", "ajv-formats": "^2.0.0", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^10.2.11", "@types/mocha": "^8.0.3", "if-node-version": "^1.0.0", "dayjs-plugin-utc": "^0.1.2", "json-schema-test": "^2.0.0", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^7.0.2", "@ajv-validator/config": "^0.3.0", "karma-chrome-launcher": "^3.0.0", "@vuepress/shared-utils": "^1.8.2", "eslint-config-prettier": "^7.0.0", "@rollup/plugin-commonjs": "^18.0.0", "@rollup/plugin-typescript": "^8.2.1", "@typescript-eslint/parser": "^3.8.0", "@types/require-from-string": "^1.2.0", "@rollup/plugin-node-resolve": "^11.2.1", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "c89d3380a784ce81b2085f48811c4c101df4c602", "tarball": "https://registry.npmjs.org/ajv/-/ajv-8.2.0.tgz", "fileCount": 458, "integrity": "sha512-WSNGFuyWd//XO8n/m/EaOlNLtO0yL8EXT/74LqT4khdhpZjP7lkj/kT5uwRmGitKEVp/Oj7ZUHeGfPtgHhQ5CA==", "signatures": [{"sig": "MEUCIGrGE74bPNrzTBZTl23kHIn7KT/JEWPki5jUSNf9htKEAiEAs3OIfI7974YZDP5ai4FMGWsJtDGNS562csrlt0QCr2U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 985073, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgiCkHCRA9TVsSAnZWagAAnZYQAIvRTdYlDVEsdKwKSOa4\nheKz1UH0mQsPDylFUOf1HVqkaKE9MTVoSsj6PFrLBbs7zqLituSIlHk0Uxkc\n/ySzmfLURro2MFhwg4iL6JJRDSdzYy7PJMGxOMFNj4V/wL8BfrXLhBlmljrd\nlgLQG2fI2QEonKbJNN0rU11fPhxLVo0PPl8jrmAlfPKfuwh50yAqBF8/F61M\nvsJajs+l6+szx4kk7/WWtShWo3UNoxkgUb5TOIruTPSj3/VtPCau2I75V49x\nawH04jjjPNtZdavOxoZ8krLhesIvLGbdva3zwrKMausiH8QhKVcBrvrCpgFk\nHsVNX2cG2vRDash9y+M5mot1aVuXOWh86MkzQ4l+g+qfyR4sJiaoOBCXWczI\ntiugAlYNoVR9XH+7h8v8QnUjdLuIj/vq6sg76GAlMoAL3nuO+WjjXKW9p+92\nfU7st0L8LVvfhEMQUltqsgGfvh8NvfKmIUSie4feR4kdoTGVq4JF01nbzhSD\nSq0HApNB2QZpNmfn3VBMTKm8zw1laTJ1OyuBwsLw6twB/XqRN0JDELL9r62B\nPcwzWKD9GH/Yp2vsfSgC/KLAisjUrR68VYwE7ORMYMpEYPiS+GnTFeWlyFTe\nJhJzFPSK3XbW4Dv2xC0z/rr8Fb9luv1EGh2IJp/LoOfOV6MBf1io5FAVVZ05\nXN82\r\n=CrVI\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "8.3.0": {"name": "ajv", "version": "8.3.0", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "require-from-string": "^2.0.2", "json-schema-traverse": "^1.0.0"}, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "jimp": "^0.16.1", "dayjs": "^1.10.4", "husky": "^6.0.0", "karma": "^5.0.0", "mocha": "^8.0.1", "tsify": "^5.0.2", "eslint": "^7.8.1", "rollup": "^2.44.0", "ts-node": "^9.0.0", "prettier": "^2.0.5", "vuepress": "^1.8.2", "cross-env": "^7.0.2", "node-fetch": "^2.6.1", "typescript": "^4.2.0", "@types/chai": "^4.2.12", "@types/node": "^15.0.2", "ajv-formats": "^2.0.0", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^10.2.11", "@types/mocha": "^8.0.3", "if-node-version": "^1.0.0", "dayjs-plugin-utc": "^0.1.2", "json-schema-test": "^2.0.0", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^7.0.2", "@ajv-validator/config": "^0.3.0", "karma-chrome-launcher": "^3.0.0", "@vuepress/shared-utils": "^1.8.2", "eslint-config-prettier": "^7.0.0", "@rollup/plugin-commonjs": "^18.0.0", "@rollup/plugin-typescript": "^8.2.1", "@typescript-eslint/parser": "^3.8.0", "@types/require-from-string": "^1.2.0", "@rollup/plugin-node-resolve": "^13.0.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "25ee7348e32cdc4a1dbb38256bf6bdc451dd577c", "tarball": "https://registry.npmjs.org/ajv/-/ajv-8.3.0.tgz", "fileCount": 458, "integrity": "sha512-RYE7B5An83d7eWnDR8kbdaIFqmKCNsP16ay1hDbJEU+sa0e3H9SebskCt0Uufem6cfAVu7Col6ubcn/W+Sm8/Q==", "signatures": [{"sig": "MEUCIQC4j1lqnk7YEO4Kw7j4i3DvkKp9s3MZPBbQRfJIQFRsggIgSlB+nLAR9NpWlYy3I3DsfZRRyQe6EP1d5oNttQocfI0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 988475, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgl8jDCRA9TVsSAnZWagAA2MsQAKL7BzqpySQtuka+6IGW\nVPeBWRuE1/qqPOj5rY/qBZyNo6VnqMSF9gQBmCv/s3GA/f3yn7SyN1AzhM2O\nOAxrpUlfx6+wClpr+ZB9g13CM+dMtu9GvsHanJBj9iVgrp6FtFDc3eUPbxXS\nUhmYfGLDb3PGBuzHMq7meDQIjxmG+Wi/AiwKjPIWVspBK5FrgvF6zVpqiZVE\n5fdIoMO+GBhYeZI40AMlvKtzhhgRYU6OOvZidX/+84TWXgsmoS0FhhypI2C2\nRwglxpRTNR2mRTlP4mNycIsu3qIzNdqVg42QHW9MNS+8V0zY0nw+ktL0xFxp\n0+wrH0GyYv38lKAmt9h2wY7XFYROlN6SCCoUn4umtOSQnrCLHT76fUotFhJg\nlr6ewCjEE+wUGQ6YzeEeoRhoFvURkwX1yFQc3PldC6zSklB282V6+3fIADCj\no21z3BvHMLUKipe2+xGUsbyDbhq+P7JGVH3eh4KhJQafd1Uf73U+ZEWXPeyG\nBZu4eZl6GstC8VXzDQbfXRbU1XvIHbcTq1qAI1E4apiCzo2h0piEGC9IDknz\n4qj6apHAmWjznQuJ9sxDaMi4Xzfp3BHCEeOrTP37R/Ct9YvUQTVQquJHXi0q\nFM2lye0yLPkmkcjhCeWRwmuLiu1mDUL12Os5WIKkiKwaWQPiVwoN5em9LM/H\nBKGm\r\n=S0Mu\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "8.4.0": {"name": "ajv", "version": "8.4.0", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "require-from-string": "^2.0.2", "json-schema-traverse": "^1.0.0"}, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "jimp": "^0.16.1", "dayjs": "^1.10.4", "husky": "^6.0.0", "karma": "^6.0.0", "mocha": "^8.0.1", "tsify": "^5.0.2", "eslint": "^7.8.1", "rollup": "^2.44.0", "ts-node": "^9.0.0", "prettier": "^2.0.5", "vuepress": "^1.8.2", "cross-env": "^7.0.2", "node-fetch": "^2.6.1", "typescript": "^4.2.0", "@types/chai": "^4.2.12", "@types/node": "^15.0.2", "ajv-formats": "^2.0.0", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^10.2.11", "@types/mocha": "^8.0.3", "if-node-version": "^1.0.0", "dayjs-plugin-utc": "^0.1.2", "json-schema-test": "^2.0.0", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^7.0.2", "@ajv-validator/config": "^0.3.0", "karma-chrome-launcher": "^3.0.0", "@vuepress/shared-utils": "^1.8.2", "eslint-config-prettier": "^7.0.0", "@rollup/plugin-commonjs": "^18.0.0", "@rollup/plugin-typescript": "^8.2.1", "@typescript-eslint/parser": "^3.8.0", "@types/require-from-string": "^1.2.0", "@rollup/plugin-node-resolve": "^13.0.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "48984fdb2ce225cab15795f0772a8d85669075e4", "tarball": "https://registry.npmjs.org/ajv/-/ajv-8.4.0.tgz", "fileCount": 458, "integrity": "sha512-7QD2l6+KBSLwf+7MuYocbWvRPdOu63/trReTLu2KFwkgctnub1auoF+Y1WYcm09CTM7quuscrzqmASaLHC/K4Q==", "signatures": [{"sig": "MEQCIBot+BlwnKf64Iv4BgOxrZPjiiGOsGpKaTz5Mhp/Y5g6AiBQCR3hUDossDcWXSIMgp4xcIh/EJEUdG7VyJk0OBEsOw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 990584, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgntmYCRA9TVsSAnZWagAAKCsP/RyDKU6Hrq7mEyyK/Soj\n6VyWke78FdXVo+HJETg2FO3Nn81h/SARxClnu1aAulEpZvCZMg2k64/vk0Vb\nXO2gfgHMH/uObBFBAONpUECpIH96WbmwfwCr550p7VDQf3aC3IUfBNosAZF/\ntJIhHZiB9phi1PlHKZ8sDlI6thc+MKsSmYQ/UdL0uvLN0J0GeujwRcpPZf3y\nW9IzUitZpiyCj3bv2i+jtcD3gdd0Yus2DhF5llWgW9rrZ0bo8oc4RjEwP8/J\nM8o7fMqiAXNCktUhJROofHpobJEuyh4I8J2r/nYBjJBA2HOeQnIGfw9lXFRa\nXloSH3iVZKIiBc0mKsdXmnRJZdULWOYNS7L710tND5Rr4zoGvIXyUfW/GlZN\n3yJYPEu69cHj/tzAms8gpvJyblEsEvLqj1DF9Aa49TLmnoUI+zEqZXrfWxGu\nEnzRsFdrLdAHjfXDdSFk24dbT92JxsWvEAN3s22gnpTgOfBbfxUvNLiHriZP\nhwy4nntzI2uNJXrsERgN7XdHZI+0Q5qoUU6m3ZSPGlbsRDyoni0LvIDX8IsB\n55vnDiqmD/tvDSph+OBYZYhsrJLUY+W7vbfuxVJkEbmAtG5bDj8OsXvt7RWm\nwe8So8Lm8XJAWEn9+RI+ybqEW5nhZMbFIvjuF+tGI2JZ7crJOEZsRspHh+7k\nl3MW\r\n=XWRn\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "8.5.0": {"name": "ajv", "version": "8.5.0", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "require-from-string": "^2.0.2", "json-schema-traverse": "^1.0.0"}, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "jimp": "^0.16.1", "dayjs": "^1.10.4", "husky": "^6.0.0", "karma": "^6.0.0", "mocha": "^8.0.1", "tsify": "^5.0.2", "eslint": "^7.8.1", "rollup": "^2.44.0", "ts-node": "^9.0.0", "prettier": "^2.0.5", "vuepress": "^1.8.2", "cross-env": "^7.0.2", "node-fetch": "^2.6.1", "typescript": "^4.2.0", "@types/chai": "^4.2.12", "@types/node": "^15.0.2", "ajv-formats": "^2.0.0", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^10.2.11", "@types/mocha": "^8.0.3", "if-node-version": "^1.0.0", "dayjs-plugin-utc": "^0.1.2", "json-schema-test": "^2.0.0", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^7.0.2", "@ajv-validator/config": "^0.3.0", "karma-chrome-launcher": "^3.0.0", "@vuepress/shared-utils": "^1.8.2", "eslint-config-prettier": "^7.0.0", "@rollup/plugin-commonjs": "^18.0.0", "@rollup/plugin-typescript": "^8.2.1", "@typescript-eslint/parser": "^3.8.0", "@types/require-from-string": "^1.2.0", "@rollup/plugin-node-resolve": "^13.0.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "695528274bcb5afc865446aa275484049a18ae4b", "tarball": "https://registry.npmjs.org/ajv/-/ajv-8.5.0.tgz", "fileCount": 458, "integrity": "sha512-Y2l399Tt1AguU3BPRP9Fn4eN+Or+StUGWCUpbnFyXSo8NZ9S4uj+AG2pjs5apK+ZMOwYOz1+a+VKvKH7CudXgQ==", "signatures": [{"sig": "MEYCIQCTkvnAMcGGT66hJq1NGiKAITcp3a3n/5j4aE0Ea6oqKwIhAI1MA5ySZF03P9+hFW/Jnd0QRrhaRGfOojA0y+H2nQdZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 993887, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgpmU4CRA9TVsSAnZWagAAicwQAJidyvbz3bIAeGYHai01\n2HCpG6psPIsYHA27ukfrQPloRKsEYa5Fav7oH3mfy9kSbxQpE+zWmR9RMWlI\ntea/4TvERhYFYPunKLozKxzapKYUNg+c2srXt9Tl4W28YBMav9/sicU9FH0w\n/qBd5t9LxMdz0DbyrbDcgcq81EfOysqgku4WNNvdna6HYyaCcfVhm3T0wTdS\nmI1HTz76b6bhL6I5U2ckSMDuJW4GCR1P7KAIhIdtQ6tNSLcntt96HltCpxbI\npCuK1vWTKyWzCBzInJmkAd1t6J2cS6jusYRYsyqPiE91u4dAeLsZQrsBzfkN\nWc2+3BppvVY1D8xyPVXPZaggOaXrwu8GqRWLIYbrMm09d+H76lgWJNpoMFmZ\niLzKMNDz04n8s0rGS5k20kCDVrLxraSXjXcadjcv2+mVBIErk/Riw1SZhZSS\n+uCcGHw2Bw7mOocu70437fwG4Js5QeUnF8mg+HdxlLvpUNhRsEZm0NFwNmaa\nq/h9Xl8rmeqVySuZgF2tuJBtAB/HaDh0JwmaWhN+22lI2+E2hgd/X/+dfAeH\nruvhdxfdN+BA1/2Z1oDt9j0IbvnI0iqJUduhjo8Kbw/nA8ztaU3OWg4eVp7h\nCkNdB8vXoM6f+jNAV7rtiPWOcgcNiJD//+s+0q42YTqqUdaVL0uuBzN8LQjH\nBiVm\r\n=Rki5\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "8.6.0": {"name": "ajv", "version": "8.6.0", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "require-from-string": "^2.0.2", "json-schema-traverse": "^1.0.0"}, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "jimp": "^0.16.1", "dayjs": "^1.10.4", "husky": "^6.0.0", "karma": "^6.0.0", "mocha": "^8.0.1", "tsify": "^5.0.2", "eslint": "^7.8.1", "rollup": "^2.44.0", "ts-node": "^10.0.0", "prettier": "^2.3.1", "vuepress": "^1.8.2", "cross-env": "^7.0.2", "node-fetch": "^2.6.1", "typescript": "^4.2.0", "@types/chai": "^4.2.12", "@types/node": "^15.0.2", "ajv-formats": "^2.0.0", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^11.0.0", "@types/mocha": "^8.0.3", "if-node-version": "^1.0.0", "dayjs-plugin-utc": "^0.1.2", "json-schema-test": "^2.0.0", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^7.0.2", "@ajv-validator/config": "^0.3.0", "karma-chrome-launcher": "^3.0.0", "@vuepress/shared-utils": "^1.8.2", "eslint-config-prettier": "^7.0.0", "@rollup/plugin-commonjs": "^19.0.0", "@rollup/plugin-typescript": "^8.2.1", "@typescript-eslint/parser": "^3.8.0", "@types/require-from-string": "^1.2.0", "@rollup/plugin-node-resolve": "^13.0.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "60cc45d9c46a477d80d92c48076d972c342e5720", "tarball": "https://registry.npmjs.org/ajv/-/ajv-8.6.0.tgz", "fileCount": 458, "integrity": "sha512-cnUG4NSBiM4YFBxgZIj/In3/6KX+rQ2l2YPRVcvAMQGWEPKuXoPIhxzwqh31jA3IPbI4qEOp/5ILI4ynioXsGQ==", "signatures": [{"sig": "MEUCIQDzOCkWwgnrkiVKpWfwnJf96Pr4LfCA2wa8VoBwkwBj2AIgTDrWy4QNFSK9a/8mqVHUCK3opLQNmqum9ZtLu16oRQ0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 997144, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgvOJcCRA9TVsSAnZWagAAvQEP/iz/nIYLWARPdb7aFYVu\nVUJ9QXJx0pf0j+wzhQRznxoMWK5obpJY5cadEFlBmvsz9yu1RKt9N1wf6Wsv\n0GEm76248GuCi4+add6kQwSaODYK2wnQM9G8wpBZny4i/Eci1ylRoaNsCH7w\n4o5stbuPHCc7IP5diH6ZfFixX7LuRGrFEMpHHHfnGXLVNJd/n0RIFaEyNE3u\nxvZMH1fMTdwvC33MOQTcel19fZ1k0MDag45qKYbvpvZZCNtv8VjeMDT4/tL5\n0p0tXkAzTPqP4r0DuwPha8QLhzq4Tp7GwaxhqT0sPV7V0MntLqKj/qxiZTyt\n2rDXAEGg7PbMg59zKsmKsC8X+BVqQqtoA9FRHywKFEAtAe1XnSeFn/WTMb+T\n9q5inxs1MOMRlgnm2JfmT1IleCBWOe2jfyspH9OjHkNkQQpRqxt5h4bHNDrn\nfQJF3cgdyI4VIVUWUHHRd2iT4wgA8g//qT0cePf2VPOaD+CpVYrx1Pa7O9Ju\nOVJ6tfgAKxq4vzI2UZtR2BBQtYO0tIgkv7KXVS5xHAuz8MhJUcfRl3LJTm4W\nceFWq6OioF7HdbRieekLlFpn2r0+Kioo7ecyuVEA6H92cs8ROGfPJwu7DHNM\neseLo1X6bYyq0NY8tvB7TEDW2+iIsjfZlOls6w37JTMbLqS/95/WFBfrHncN\ne7IE\r\n=gfVm\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "8.6.1": {"name": "ajv", "version": "8.6.1", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "require-from-string": "^2.0.2", "json-schema-traverse": "^1.0.0"}, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "jimp": "^0.16.1", "dayjs": "^1.10.4", "husky": "^6.0.0", "karma": "^6.0.0", "mocha": "^8.0.1", "tsify": "^5.0.2", "eslint": "^7.8.1", "rollup": "^2.44.0", "ts-node": "^10.0.0", "prettier": "^2.3.1", "vuepress": "^1.8.2", "cross-env": "^7.0.2", "node-fetch": "^2.6.1", "typescript": "^4.2.0", "@types/chai": "^4.2.12", "@types/node": "^15.0.2", "ajv-formats": "^2.0.0", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^11.0.0", "@types/mocha": "^8.0.3", "if-node-version": "^1.0.0", "dayjs-plugin-utc": "^0.1.2", "json-schema-test": "^2.0.0", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^7.0.2", "@ajv-validator/config": "^0.3.0", "karma-chrome-launcher": "^3.0.0", "@vuepress/shared-utils": "^1.8.2", "eslint-config-prettier": "^7.0.0", "@rollup/plugin-commonjs": "^19.0.0", "@rollup/plugin-typescript": "^8.2.1", "@typescript-eslint/parser": "^3.8.0", "@types/require-from-string": "^1.2.0", "@rollup/plugin-node-resolve": "^13.0.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "ae65764bf1edde8cd861281cda5057852364a295", "tarball": "https://registry.npmjs.org/ajv/-/ajv-8.6.1.tgz", "fileCount": 458, "integrity": "sha512-42VLtQUOLefAvKFAQIxIZDaThq6om/PrfP0CYk3/vn+y4BMNkKnbli8ON2QCiHov4KkzOSJ/xSoBJdayiiYvVQ==", "signatures": [{"sig": "MEQCIF8TBhbrFUCmH/r4cXBp/9hvGxTDd+/8d3UFvV7a9AJhAiBUS6cRsHzlzKPSgoiqe7rstwB7UO0y60Pjvwr3/eT8LA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 997724, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg4YUOCRA9TVsSAnZWagAA9/MP/A/JVYP3mXrMtputqlQ7\n+oRL2PiVO5J5n95KG5ueDG2mSi3w9rDMdVc+mG6iUuxWic4fwwptkIXycGQE\nQNkZfKOli2afry4HCEj2ECbaZDuMzxKfs9j5+io72ocDvAxOBfbZylC7UE4b\nNr4ZtRdUGyzIgfM2vzdqfyIgIMFVYHFM7h8jwyZaVPBGOHnIEKz1XWbmSKgf\nsNDx7AFBDqQr4ghW9HQ9zS16xTJ+ezNk3sAoSwUZim6vPvyWMjbGOQ7UDxur\nA0ia6ZSw15S7dxOvKDutn8o46Xpcf+AlyOT/8KlblSfEDmvR08yVs1w3QNrx\nAlQFspdawHhkCmAoaGbRTEGrAIveApZssus/mJ6EvN7KGar+mB3WNIoXX9Qq\n04HB4UwfVA3v96D8GcwCPy1b2Vdavxz1QkG7LMm3F4ekhttHI2Uj8CjABouT\nDMzAvFanvth9C8t6cr4ohrVU6O1V3eJnlvLGkrJAsY+wrXwc5CTKvL8tYosZ\ncC6NQSXHEtgdPxG0wvv3AfsjHrRdJafuzFAR8Iu8YiLWYvFi1enH9f9m83oy\njQQIOE/W8ZnhHWtFasx5DhGmta6C+VeOtJG0NHXo2u21A/jlS1KMfZohflHN\n72Qy2bUsIi7tKu8BZTWHWv5VepFqkPtyUWvRTsu3WbSgwSRJB1YUa+xsGL3n\ntEJI\r\n=qUzO\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "8.6.2": {"name": "ajv", "version": "8.6.2", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "require-from-string": "^2.0.2", "json-schema-traverse": "^1.0.0"}, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "jimp": "^0.16.1", "dayjs": "^1.10.4", "husky": "^7.0.1", "karma": "^6.0.0", "mocha": "^8.0.1", "tsify": "^5.0.2", "eslint": "^7.8.1", "rollup": "^2.44.0", "ts-node": "^10.0.0", "prettier": "^2.3.1", "vuepress": "^1.8.2", "cross-env": "^7.0.2", "node-fetch": "^2.6.1", "typescript": "^4.2.0", "@types/chai": "^4.2.12", "@types/node": "^16.3.2", "ajv-formats": "^2.0.0", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^11.0.0", "@types/mocha": "^8.0.3", "if-node-version": "^1.0.0", "dayjs-plugin-utc": "^0.1.2", "json-schema-test": "^2.0.0", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^7.0.2", "@ajv-validator/config": "^0.3.0", "karma-chrome-launcher": "^3.0.0", "@vuepress/shared-utils": "^1.8.2", "eslint-config-prettier": "^7.0.0", "@rollup/plugin-commonjs": "^19.0.0", "@rollup/plugin-typescript": "^8.2.1", "@typescript-eslint/parser": "^3.8.0", "@types/require-from-string": "^1.2.0", "@rollup/plugin-node-resolve": "^13.0.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "2fb45e0e5fcbc0813326c1c3da535d1881bb0571", "tarball": "https://registry.npmjs.org/ajv/-/ajv-8.6.2.tgz", "fileCount": 458, "integrity": "sha512-9807RlWAgT564wT+DjeyU5OFMPjmzxVobvDFmNAhY+5zD6A2ly3jDp6sgnfyDtlIQ+7H97oc/DGCzzfu9rjw9w==", "signatures": [{"sig": "MEUCIQCXcqA9FO+MAGIBg/47nXYXVxCIMZnSwpMUu7mEk2u+ewIgW7/ckkv/SBNOMu2v+D6BT5cC627yIKHYiHYM8fRsKWI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 997742, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg8JbbCRA9TVsSAnZWagAAdDAQAIjez5ftPLlfAdXrUOWn\nmSBuMmagbRbIJ9GzQEq8lqnE3B2bT7ChnpAsv+V1fQ5CI4nSG2Ov50hW0Scc\n3E6tTBqW8mq7VyaNX9vdgbV7GQaA53dE1a0L3oA8K6nFjr3+CsjIFj4dgWO2\nzAPr1r64FLAghNEV0hAo5m/8agM29+mSTxkVzGrnX7mP6P3RvHFiEzqsCUu/\n10R5Urko5Mr0BaHC2hbkMoa4SzaqGr6Z+duD5Y+3JEDQjbrtmQZ50SouO4vc\nf9Ste+450bH2fJ+yGVrzJFglaOZ/U25CrmJTP2bKLS7E/aHW5liTioLRIwWo\nq8VdZ2qRomfzDCV/s5xMvZ2wjgQxamfnqRxPt60+QzKWNonUrltdDm7wupeH\nHyoSIx4Z2NU9Ldpz/qWN8rh4Zd1anAFTQBOw9GcRL3nUqYcUZNG9diXNjxVR\nVEvD88doQwdkIFXGHYhPVx97ql+deqOgrL+S5stoWtCNMGS3vA2jh3wKPYRu\nmIHTNd20o1c0n1u9EVMkkr69i6yDtGqgRHZg8IT1pQYcVreYwwSjBRD7nQ6R\n/pyG0fbz8B4SZaTVtBm6XGp6bSO4MmsIH8aWwtb6RD8mXymbYOiw+O13NIag\nFtn32Bjwsa5r2ZtfuqeLgktOHQ+uMIZT+N55OdJ0JGBpQwlx/6tZoyY3EJ97\nAQQD\r\n=zw9V\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "8.6.3": {"name": "ajv", "version": "8.6.3", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "require-from-string": "^2.0.2", "json-schema-traverse": "^1.0.0"}, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "jimp": "^0.16.1", "dayjs": "^1.10.4", "husky": "^7.0.1", "karma": "^6.0.0", "mocha": "^9.0.2", "tsify": "^5.0.2", "eslint": "^7.8.1", "rollup": "^2.44.0", "ts-node": "^10.0.0", "prettier": "^2.3.1", "vuepress": "^1.8.2", "cross-env": "^7.0.2", "node-fetch": "^3.0.0", "typescript": "^4.2.0", "@types/chai": "^4.2.12", "@types/node": "^16.3.2", "ajv-formats": "^2.0.0", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^11.0.0", "@types/mocha": "^9.0.0", "if-node-version": "^1.0.0", "dayjs-plugin-utc": "^0.1.2", "json-schema-test": "^2.0.0", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^7.0.2", "@ajv-validator/config": "^0.3.0", "karma-chrome-launcher": "^3.0.0", "@vuepress/shared-utils": "^1.8.2", "eslint-config-prettier": "^7.0.0", "@rollup/plugin-commonjs": "^20.0.0", "@rollup/plugin-typescript": "^8.2.1", "@typescript-eslint/parser": "^3.8.0", "@types/require-from-string": "^1.2.0", "@rollup/plugin-node-resolve": "^13.0.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "11a66527761dc3e9a3845ea775d2d3c0414e8764", "tarball": "https://registry.npmjs.org/ajv/-/ajv-8.6.3.tgz", "fileCount": 458, "integrity": "sha512-SMJOdDP6LqTkD0Uq8qLi+gMwSt0imXLSV080qFVwJCpH9U6Mb+SUGHAXM0KNbcBPguytWyvFxcHgMLe2D2XSpw==", "signatures": [{"sig": "MEUCIQDcELRVVCV9F8AQ3pLL+z5KQdB/OcPtV10DDUsH2FTCBQIgRMVDebyEibshAJ+wsP+6DO0LyVPB2YvVKtLsUNrhMy0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1005628, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhPkT8CRA9TVsSAnZWagAAZIAP/1318U1NmCG2kzms99Bv\nCjJhsQ2UIe6KdQJRb9l7PA5xgmDvgex8ET0A8KXCKH3XZDjMm0cuXW8qjjDn\n+D8SJ4UfPR6xNvY7vINnR+dqFUgRJAZ53eafO8sWHfNXq+pBa+yPbt6J7Av8\nE2IagMeqJIqA5UrYrnzyCrEsCd0g1lRf/1ct5FLfKS/U41052hU90Pvg9acp\nH0T8sBnCnOwKZboUUTC+dbz7nDUs9ujnsNGg2b1FJbgtsTV40RgWprEvt561\nCXhsJtFgu+DSWWe4BE45G32HUWlynrq/oCPIE4Rxa/EC/wAILdY9m/Ba4UuP\nEF4sQqwhlPtYv3ghgHI9bqP0xa8B+oNUjdd47NrAoexaSNA2U19I4dxHSBDx\nwPmp+YvYj+wizso5fz5e4dIQ/Jn0h8Wq72OOH+eiE7jM1yF85iZph247/KAv\n08mCriWN3pawdvHPCtwCYVFipKrtcLgu72R0GvLtCGfExzMiYDJe38AnNT9K\ndEDfyECfREaJTpDfj4AchrEmbmMKV64tiC2FRgDXlK8yuSIaC1IE8vGvAd/S\nQSGUD0oOSapnU+52HuaTTKROKz0zp1HgR5V3gkAgV54VlAY6oRRR+lCQ9fsZ\nwxL+WPAVfXi0MJjptPLDgAKjP8y43xiWRMMjN80bC2Xwu72nBfhPzpCn4pDP\nNcg9\r\n=htFk\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "8.7.0": {"name": "ajv", "version": "8.7.0", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "require-from-string": "^2.0.2", "json-schema-traverse": "^1.0.0"}, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "jimp": "^0.16.1", "dayjs": "^1.10.4", "husky": "^7.0.1", "karma": "^6.0.0", "mocha": "^9.0.2", "tsify": "^5.0.2", "eslint": "^7.8.1", "rollup": "^2.44.0", "ts-node": "^10.0.0", "prettier": "^2.3.1", "cross-env": "^7.0.2", "node-fetch": "^3.0.0", "typescript": "^4.2.0", "@types/chai": "^4.2.12", "@types/node": "^16.3.2", "ajv-formats": "^3.0.0-rc.0", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^11.0.0", "@types/mocha": "^9.0.0", "if-node-version": "^1.0.0", "dayjs-plugin-utc": "^0.1.2", "json-schema-test": "^2.0.0", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^7.0.2", "@ajv-validator/config": "^0.3.0", "karma-chrome-launcher": "^3.0.0", "eslint-config-prettier": "^7.0.0", "@rollup/plugin-commonjs": "^21.0.0", "@rollup/plugin-typescript": "^8.2.1", "@typescript-eslint/parser": "^3.8.0", "@types/require-from-string": "^1.2.0", "@rollup/plugin-node-resolve": "^13.0.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "643a9dd10bcaf6b084162f9d1f551091dd6650aa", "tarball": "https://registry.npmjs.org/ajv/-/ajv-8.7.0.tgz", "fileCount": 458, "integrity": "sha512-fX9/Yiy9YwnP/QB/4zqBpTavtL4YuXpiHlXlkE0y2itGcO++ixFIg+NFk1l0TfHjt11EDDhHAhLVe0rFgTBaGA==", "signatures": [{"sig": "MEYCIQDNHi3jqKAt2+zgV5gPIn6jxGkaYBxsaU6orWK9Hqm4HAIhAKTOOfmooV9slwrIRSwPomOgtEEHRecdPEuazNFhgRjA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1006454}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "8.7.1": {"name": "ajv", "version": "8.7.1", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "require-from-string": "^2.0.2", "json-schema-traverse": "^1.0.0"}, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.0.1", "glob": "^7.0.0", "jimp": "^0.16.1", "dayjs": "^1.10.4", "husky": "^7.0.1", "karma": "^6.0.0", "mocha": "^9.0.2", "tsify": "^5.0.2", "eslint": "^7.8.1", "rollup": "^2.44.0", "ts-node": "^10.0.0", "prettier": "^2.3.1", "cross-env": "^7.0.2", "node-fetch": "^3.0.0", "typescript": "^4.2.0", "@types/chai": "^4.2.12", "@types/node": "^16.3.2", "ajv-formats": "^3.0.0-rc.0", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^11.0.0", "@types/mocha": "^9.0.0", "if-node-version": "^1.0.0", "dayjs-plugin-utc": "^0.1.2", "json-schema-test": "^2.0.0", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^7.0.2", "@ajv-validator/config": "^0.3.0", "karma-chrome-launcher": "^3.0.0", "eslint-config-prettier": "^7.0.0", "@rollup/plugin-commonjs": "^21.0.0", "@rollup/plugin-typescript": "^8.2.1", "@typescript-eslint/parser": "^3.8.0", "@types/require-from-string": "^1.2.0", "@rollup/plugin-node-resolve": "^13.0.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "52be6f1736b076074798124293618f132ad07a7e", "tarball": "https://registry.npmjs.org/ajv/-/ajv-8.7.1.tgz", "fileCount": 458, "integrity": "sha512-gPpOObTO1QjbnN1sVMjJcp1TF9nggMfO4MBR5uQl6ZVTOaEPq5i4oq/6R9q2alMMPB3eg53wFv1RuJBLuxf3Hw==", "signatures": [{"sig": "MEUCIENJFi1pQWtMvB8AbMubpJNBknIPneCZZwtoS+EIEK1oAiEAxQeXwuKJkd7UNkh0i9ibH90ckLqJ6IyXG/EkutU4dfY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1006454}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "8.8.0": {"name": "ajv", "version": "8.8.0", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "require-from-string": "^2.0.2", "json-schema-traverse": "^1.0.0"}, "devDependencies": {"nyc": "^15.0.0", "re2": "^1.16.0", "chai": "^4.0.1", "glob": "^7.0.0", "jimp": "^0.16.1", "dayjs": "^1.10.4", "husky": "^7.0.1", "karma": "^6.0.0", "mocha": "^9.0.2", "tsify": "^5.0.2", "eslint": "^7.8.1", "rollup": "^2.44.0", "ts-node": "^10.0.0", "prettier": "^2.3.1", "cross-env": "^7.0.2", "browserify": "^17.0.0", "node-fetch": "^3.0.0", "typescript": "^4.2.0", "@types/chai": "^4.2.12", "@types/node": "^16.3.2", "ajv-formats": "^3.0.0-rc.0", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^11.0.0", "@types/mocha": "^9.0.0", "if-node-version": "^1.0.0", "dayjs-plugin-utc": "^0.1.2", "json-schema-test": "^2.0.0", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^7.0.2", "@ajv-validator/config": "^0.3.0", "karma-chrome-launcher": "^3.0.0", "eslint-config-prettier": "^7.0.0", "@rollup/plugin-commonjs": "^21.0.0", "@rollup/plugin-typescript": "^8.2.1", "@typescript-eslint/parser": "^3.8.0", "@types/require-from-string": "^1.2.0", "@rollup/plugin-node-resolve": "^13.0.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "c501f10df72914bb77a458919e79fc73e4a2f9ef", "tarball": "https://registry.npmjs.org/ajv/-/ajv-8.8.0.tgz", "fileCount": 462, "integrity": "sha512-L+cJ/+pkdICMueKR6wIx3VP2fjIx3yAhuvadUv/osv9yFD7OVZy442xFF+Oeu3ZvmhBGQzoF6mTSt+LUWBmGQg==", "signatures": [{"sig": "MEUCIGczusX+XAC1cCnow7urv1N/8Vg4jaHzg8zVKFsaVp3TAiEAgTyTAHMSyga24rnMRF3rPKEODMaYm1dBzwVDrX7ZimA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1009069}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "8.8.1": {"name": "ajv", "version": "8.8.1", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "require-from-string": "^2.0.2", "json-schema-traverse": "^1.0.0"}, "devDependencies": {"nyc": "^15.0.0", "re2": "^1.16.0", "chai": "^4.0.1", "glob": "^7.0.0", "jimp": "^0.16.1", "dayjs": "^1.10.4", "husky": "^7.0.1", "karma": "^6.0.0", "mocha": "^9.0.2", "tsify": "^5.0.2", "eslint": "^7.8.1", "rollup": "^2.44.0", "ts-node": "^10.0.0", "prettier": "^2.3.1", "cross-env": "^7.0.2", "browserify": "^17.0.0", "node-fetch": "^3.0.0", "typescript": "^4.2.0", "@types/chai": "^4.2.12", "@types/node": "^16.3.2", "ajv-formats": "^3.0.0-rc.0", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^11.0.0", "@types/mocha": "^9.0.0", "if-node-version": "^1.0.0", "dayjs-plugin-utc": "^0.1.2", "json-schema-test": "^2.0.0", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^7.0.2", "@ajv-validator/config": "^0.3.0", "karma-chrome-launcher": "^3.0.0", "eslint-config-prettier": "^7.0.0", "@rollup/plugin-commonjs": "^21.0.0", "@rollup/plugin-typescript": "^8.2.1", "@typescript-eslint/parser": "^3.8.0", "@types/require-from-string": "^1.2.0", "@rollup/plugin-node-resolve": "^13.0.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "e73dd88eeb4b10bbcd82bee136e6fbe801664d18", "tarball": "https://registry.npmjs.org/ajv/-/ajv-8.8.1.tgz", "fileCount": 462, "integrity": "sha512-6CiMNDrzv0ZR916u2T+iRunnD60uWmNn8SkdB44/6stVORUg0aAkWO7PkOhpCmjmW8f2I/G/xnowD66fxGyQJg==", "signatures": [{"sig": "MEQCIDuEboHjiHs8QJJiwgK2BKLZPCZTkYowq2iRbw65BqqPAiAD46ZydAdQIjoOsnyTb+NiIDfnAT3yuYHgXXT6ZBWYgA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1009069, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhlBJ1CRA9TVsSAnZWagAAxMgP/AnECjaA9T13kBqBSS0R\nJDN/MVF2SHf76Bzhs2rShJz/HeU6NUB83Me072lE89imrmT+l1uUuDB2GGjf\ngjPp9A0vrR1hf4BByK6/ZrsN4idoxJGfErMMGf+gyLbYcGyo+3arHRZbecmh\nkQN+4E9Ub+soPDlxcpvHkzUJRFLybiIIJawBYFf0GavzCicy42fGXGo/j6RU\ni6LeMMhSnmyCdeSIEwCywlxNXVNx85j6uRNeI1ZZq2X3HR8bG5IgMD44nCaM\n1PWHvDvlbEgL2Z4rWtweWiiXwvtyIc16DRlLjQvexAzaruh4xhB6MUDeTZCa\nGie4Gmw/Es1Ec/2DkPCovBXXz/ncAD7vb+8kdmU4l7vCeMCNhi1XneyCpyUc\nedhBsGjkJp4MNjPqCnBaSewpqhkS7DInPWWM37mKDFYk4jbSfDMJeUTpiVU7\nug1y/VWuHNQUV2mDbsM4GJmgxsy/qWvjGy8EkZ2h6JuXTCSEo9upzfeyeJiK\nJ36lBGr+yluHQAsY2L8J8jGoogufEqGx/qobOJpXrnWyg8yBbGbIj2hEdvCK\nFV0Q6cKZWGT1NIo2toriRTmgeL+/vlE1Rbg6QxmZwCVdLiqCFDs3ys2wppzd\nn6w/BB4sd33QACwt6/hPznk2W1wzdpQRZmD5PPP1dukh2rufyaaLC1zaBO67\nS+fD\r\n=Smc7\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "8.8.2": {"name": "ajv", "version": "8.8.2", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "require-from-string": "^2.0.2", "json-schema-traverse": "^1.0.0"}, "devDependencies": {"nyc": "^15.0.0", "re2": "^1.16.0", "chai": "^4.0.1", "glob": "^7.0.0", "jimp": "^0.16.1", "dayjs": "^1.10.4", "husky": "^7.0.1", "karma": "^6.0.0", "mocha": "^9.0.2", "tsify": "^5.0.2", "eslint": "^7.8.1", "rollup": "^2.44.0", "ts-node": "^10.0.0", "prettier": "^2.3.1", "cross-env": "^7.0.2", "browserify": "^17.0.0", "node-fetch": "^3.0.0", "typescript": "^4.2.0", "@types/chai": "^4.2.12", "@types/node": "^16.3.2", "ajv-formats": "^3.0.0-rc.0", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^11.0.0", "@types/mocha": "^9.0.0", "if-node-version": "^1.0.0", "dayjs-plugin-utc": "^0.1.2", "json-schema-test": "^2.0.0", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^7.0.2", "@ajv-validator/config": "^0.3.0", "karma-chrome-launcher": "^3.0.0", "eslint-config-prettier": "^7.0.0", "@rollup/plugin-commonjs": "^21.0.0", "@rollup/plugin-typescript": "^8.2.1", "@typescript-eslint/parser": "^3.8.0", "@types/require-from-string": "^1.2.0", "@rollup/plugin-node-resolve": "^13.0.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "01b4fef2007a28bf75f0b7fc009f62679de4abbb", "tarball": "https://registry.npmjs.org/ajv/-/ajv-8.8.2.tgz", "fileCount": 462, "integrity": "sha512-x9VuX+R/jcFj1DHo/fCp99esgGDWiHENrKxaCENuCxpoMCmAt/COCGVDwA7kleEpEzJjDnvh3yGoOuLu0Dtllw==", "signatures": [{"sig": "MEQCICGbLDLh1Sg6J8k4x7+lAXjngPb307Jb8a3fVmy2RWQFAiAJE/DuZzXPfdogOdB6ca0Q3aZqfXZMHHwQ8vwWUpb+4Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1009162, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhmpjiCRA9TVsSAnZWagAAxJcP/R1lntz7NwGw0cWVRUl7\nMTU1lSwCwW7vQo+lq56HzyyboZWr2eOpZUwQtb72FB0ehE4tPb/NF4IWivBq\nNXJmti6++KMMVkpXUJEy79go96O7CuBz7GEAkpo2k91Ce22qvEHeI4iNRgHw\najqdDW7FXMIFfHebkICOTxVdP0ER3r+gMAhS4qSNbhxWFvOVOzQm3TvIB99q\nQh6VYXA9wcSe6BfsjT4wIUogNrRi5dEkN2HrUmAB8UaZsdpapzVOcPgMgU+Q\n1V+4ZFnya0cfSZ1Bkyrzajmh70Q1yG/fka8tMxSYuauihvmPJWbuAfmO2wxn\nT0AbxFSg5R58vJEk6rR7co3DtPphq9AMcC2NWukZJE0XmbRik4/T2B8rWj8s\nt8ULJYsC/rl1hV+VH7OfheKrAPaRYj1gc4IxQBiP5YGX3//9h2HQAVoZzyt3\nBn/BP6v1Q2+rp4W5JYc6UvEjaMi/ZXdhs0o6285HzZ4VXvK+TdmlEwun/q3a\nTr3G17vt/g/EviQOad++poqe9rgBwlzuDRe4TbcqQQueowSRQrmmSaB1CXyb\n45qBlvcpfMNHNSl6lGH/n0rP4U0yjEv3vbf6oK16cgMWEgWrwQeUvuhqwSb+\nYkpc7qzRIvfzFdqjLGi5OqMJ7gC/Ns/4WMJa1JQWeaiFsSWwpTVzUQ86w1H6\nBwcs\r\n=/ftt\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "8.9.0": {"name": "ajv", "version": "8.9.0", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "require-from-string": "^2.0.2", "json-schema-traverse": "^1.0.0"}, "devDependencies": {"nyc": "^15.0.0", "re2": "^1.16.0", "chai": "^4.0.1", "glob": "^7.0.0", "jimp": "^0.16.1", "dayjs": "^1.10.4", "husky": "^7.0.1", "karma": "^6.0.0", "mocha": "^9.0.2", "tsify": "^5.0.2", "eslint": "^7.8.1", "rollup": "^2.44.0", "ts-node": "^10.0.0", "prettier": "^2.3.1", "cross-env": "^7.0.2", "browserify": "^17.0.0", "node-fetch": "^3.0.0", "typescript": "^4.2.0", "@types/chai": "^4.2.12", "@types/node": "^17.0.0", "ajv-formats": "^3.0.0-rc.0", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^12.1.1", "@types/mocha": "^9.0.0", "if-node-version": "^1.0.0", "dayjs-plugin-utc": "^0.1.2", "json-schema-test": "^2.0.0", "module-from-string": "^3.1.3", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^7.0.2", "@ajv-validator/config": "^0.3.0", "karma-chrome-launcher": "^3.0.0", "eslint-config-prettier": "^7.0.0", "@rollup/plugin-commonjs": "^21.0.0", "@rollup/plugin-typescript": "^8.2.1", "@typescript-eslint/parser": "^3.8.0", "@types/require-from-string": "^1.2.0", "@rollup/plugin-node-resolve": "^13.0.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "738019146638824dea25edcf299dcba1b0e7eb18", "tarball": "https://registry.npmjs.org/ajv/-/ajv-8.9.0.tgz", "fileCount": 462, "integrity": "sha512-qOKJyNj/h+OWx7s5DePL6Zu1KeM9jPZhwBqs+7DzP6bGOvqzVCSf0xueYmVuaC/oQ/VtS2zLMLHdQFbkka+XDQ==", "signatures": [{"sig": "MEYCIQC2j0sp3f0aJHDXI55OkG41nMQlp6RuD/j0EPsjPfKQWwIhAKMUEyb7iLxqNtwugL+iYhW9zzgohj2s+7sxHKbXdqs4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1012740, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4sWMCRA9TVsSAnZWagAA/CcP/iHvDdkHXiGZLHzWVinB\nZpPtuEY0mq3TtfsK2npviTzJ7R1KUCbg7NZS4eduqaUxZRmbKMDksbWNhHQ4\nRFmiVYUaRnLPvX3FsFMgQ165xm5fnzenXWzoHZyO9m8snZcA80Aqa+aEryEb\n5C2cWbHE5sCylrptDDu1m4uJoo8xJZnQ05n3UqqnUt9T0YWMrzdREQGOjqhU\nZURHlguTKamVbHd25APWJZNc4J6EL/ejqJc1pbwM7KHxeBYmrn3DPhtXR5x8\n3SWqVx57lCNiMVGJqYfaNryer+5e4cybfxb+EE2Rx0XNuSZ40DarFDXQGsRD\nMTHqLXB6OIeYNtOaaN+B7muccmGsg1fFu9Yb6OPPSIXornh4fSbG0WFjjwmp\n0zIT+dhO+P3mwJ0nn6xHebXVIn4FlZ1gv68fQg7d3cb8XB3spYK0ASLrEEoY\n/8E9XecsCFlLWGjfrEi+0iuM31me2+efSnfizylJkpaHXX4LsLCwfAPWXRnf\n/JZ3h1BuEUmbqP8guHTgyC4hlgpzUSj636Aguvy5e75mY6UyKm674Cu8YQdO\nOCCddmE12ezY41BtAX5vsn4IMvHUvWleUk8EL4wG6ydRwKV9qMQES3nEbLZQ\nmfLelhmj9InZvQi0x4zBUoTrMIgFBFOruWb4g8sYf2y17E/c81Haa37nHfD0\naXs8\r\n=ZiQ8\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "8.10.0": {"name": "ajv", "version": "8.10.0", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "require-from-string": "^2.0.2", "json-schema-traverse": "^1.0.0"}, "devDependencies": {"nyc": "^15.0.0", "re2": "^1.16.0", "chai": "^4.0.1", "glob": "^7.0.0", "jimp": "^0.16.1", "dayjs": "^1.10.4", "husky": "^7.0.1", "karma": "^6.0.0", "mocha": "^9.0.2", "tsify": "^5.0.2", "eslint": "^7.8.1", "rollup": "^2.44.0", "ts-node": "^10.0.0", "fast-uri": "^1.0.0", "prettier": "^2.3.1", "cross-env": "^7.0.2", "browserify": "^17.0.0", "node-fetch": "^3.0.0", "typescript": "^4.2.0", "@types/chai": "^4.2.12", "@types/node": "^17.0.0", "ajv-formats": "^3.0.0-rc.0", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^12.1.1", "@types/mocha": "^9.0.0", "if-node-version": "^1.0.0", "dayjs-plugin-utc": "^0.1.2", "json-schema-test": "^2.0.0", "module-from-string": "^3.1.3", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^7.0.2", "@ajv-validator/config": "^0.3.0", "karma-chrome-launcher": "^3.0.0", "eslint-config-prettier": "^7.0.0", "@rollup/plugin-commonjs": "^21.0.0", "@rollup/plugin-typescript": "^8.2.1", "@typescript-eslint/parser": "^3.8.0", "@types/require-from-string": "^1.2.0", "@rollup/plugin-node-resolve": "^13.0.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "e573f719bd3af069017e3b66538ab968d040e54d", "tarball": "https://registry.npmjs.org/ajv/-/ajv-8.10.0.tgz", "fileCount": 466, "integrity": "sha512-bzqAEZOjkrUMl2afH8dknrq5KEk2SrwdBROR+vH1EKVQTqaUbJVPdc/gEdggTMM0Se+s+Ja4ju4TlNcStKl2Hw==", "signatures": [{"sig": "MEQCIHHNWCWcdd1ReXhYOCyYylkQD2Bzd0a5N/2mD20RyHnvAiBBvuozay7qa3D7seQyAKyGetQUPqiAfZnx6dmlE4mA4g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1016629, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh/W7iCRA9TVsSAnZWagAAaLUP/1hksgauRb5lI5wLT16b\nyaTP4MwaNuUzUyp5IPmMLPD7gecxDy+AMHLSw3Q98qbZqVu72l8voPQxjTn8\nTUpWO/Mj6R2DcwDHPZfKqM1ZZ1Z4dB1HgF3QQfS/0NXMZj7JZLv64hLsO+Aj\nEiR3rHqvWvxmzP2fUDDKQiDLBQ8Jpkt3wuj4sHno9aFMIO61I7FwGMNmzJQN\nNEp4l5lTkDV4roSBDLswpXOM8STdaFSmbKAfKcrz65S8yYWmpjcYjCHPVYa6\nLnRfsQ1N7UZLTZVgvMut16w6fqKp9kA7beHIJ1S863PGSLtVbkmNgCEcUyF5\nBk0urjDkORs1BoS+NgpYcByyhUsfBaJMcVS2viIsKmcaMKRv46rnBJge0QEx\nF8H85JyoeBcj6MC874e19CWAgCBroivQ/LOuLxCkrYl6SLnosDl8WzKq3EQ+\nbsuTSA8rp2W6e/jEpfWZR/CBV3g+GfrdISwqAx9bQwr1U2ZZUwoCyI1KYIuT\nX4t6WaPbwAk+/PRyH6moaupfktpuUZm9ZyHl6ZIkXFmHxgdPAJa4HXW0d6v2\nvY0eI5a9gB3I5/Juz147V8IMMS+ExsMQ8OdBCIiwm34ri5hp7lEArTktisHV\n7QyadS+0VuY7ZJghBXHxTIeQgZ5wwXmLbe6e8NNCRz+4aRbrLo5HsSUISzyM\nKL1z\r\n=HLGP\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "8.11.0": {"name": "ajv", "version": "8.11.0", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "require-from-string": "^2.0.2", "json-schema-traverse": "^1.0.0"}, "devDependencies": {"nyc": "^15.0.0", "re2": "^1.16.0", "chai": "^4.0.1", "glob": "^7.0.0", "jimp": "^0.16.1", "dayjs": "^1.10.4", "husky": "^7.0.1", "karma": "^6.0.0", "mocha": "^9.0.2", "tsify": "^5.0.2", "eslint": "^7.8.1", "rollup": "^2.44.0", "ts-node": "^10.0.0", "fast-uri": "^1.0.0", "prettier": "^2.3.1", "cross-env": "^7.0.2", "browserify": "^17.0.0", "node-fetch": "^3.0.0", "typescript": "^4.2.0", "@types/chai": "^4.2.12", "@types/node": "^17.0.0", "ajv-formats": "^3.0.0-rc.0", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^12.1.1", "@types/mocha": "^9.0.0", "if-node-version": "^1.0.0", "dayjs-plugin-utc": "^0.1.2", "json-schema-test": "^2.0.0", "module-from-string": "^3.1.3", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^7.0.2", "@ajv-validator/config": "^0.3.0", "karma-chrome-launcher": "^3.0.0", "eslint-config-prettier": "^7.0.0", "@rollup/plugin-commonjs": "^21.0.0", "@rollup/plugin-typescript": "^8.2.1", "@typescript-eslint/parser": "^3.8.0", "@types/require-from-string": "^1.2.0", "@rollup/plugin-node-resolve": "^13.0.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "977e91dd96ca669f54a11e23e378e33b884a565f", "tarball": "https://registry.npmjs.org/ajv/-/ajv-8.11.0.tgz", "fileCount": 466, "integrity": "sha512-wGgprdCvMalC0BztXvitD2hC04YffAvtsUn93JbGXYLAtCUO4xd17mCCZQxUOItiBwZvJScWo8NIvQMQ71rdpg==", "signatures": [{"sig": "MEUCIBvHJRPXJkPRNoscrSJ8kMjkbE/LlkpAlxhMcZma8ljPAiEAoT5UriPgOHdP72VlfTAzIq6qQyMFATTzsNuwOhTbU1U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1016907, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiOktjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoJaxAAlaVvTQ2Odj3CLA5v+enMW0jEAGeGlyYxD0Ts+DiQAB4WfJNX\r\nVe0RbcTH/zgnFoDlDEOfcx1/1fEDGn92EfMNH4B+ltJE5qyOPaAVYZeE4+A3\r\nl7P3z01LhKtp911KBXT4DaZ1VXAwvr9rc5tubleEy5iv51Pr9Y2ytB8jNXtC\r\nUpTlwJbKlgP8xvyBs/rZLG7igxdWsA1UWSWU3o90eKBB1Bg6fL8is8ZPMabR\r\nPSINIR4L7T2dSSUlsdzs7RijmZQbu9rzc/Wzz+JR9rc7eufoMUkj0nXkMg84\r\nFP7A3zVGuRZyw0/2dOL3yqMn58DHQaVayFiHQhPDWfG2OJwpTIvDKAuMP2kE\r\nIT3215DU3aUdQvPLNCsSz2B1LnjVPJKOIwAOCRCx5AG+S6jQf2cZWs4QHBzH\r\n6LczlMxWyktXuZvW7a5EvuQGPfaWXGC/uhzXoFUQdukvyez6kR8Ud5tm2yDZ\r\n1KVRxuvq9y+7ZrtHgn4EquzXanJRny6glk5qwlNcxVY5mKPWnxoFgX2W5f4+\r\navGLINZXtGr9prKOyobeQ0kmRP6BPs7QG2NR8eCGrZXVNDD5PU0qM8qPpCoq\r\nnns6qwr7wk3reYcMXewd7ZDP1FaXED/VOmZFCLkK5DlWO12BHCcddpOfZiS5\r\n+l0dQlmSC3TBRvuzsAv2BzTSw19SeHFebto=\r\n=GDNg\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "8.11.1": {"name": "ajv", "version": "8.11.1", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "require-from-string": "^2.0.2", "json-schema-traverse": "^1.0.0"}, "devDependencies": {"nyc": "^15.0.0", "re2": "^1.16.0", "chai": "^4.0.1", "glob": "^8.0.2", "jimp": "^0.16.1", "dayjs": "^1.10.4", "husky": "^8.0.2", "karma": "^6.0.0", "mocha": "^10.0.0", "tsify": "^5.0.2", "eslint": "^7.8.1", "rollup": "^2.44.0", "ts-node": "^10.0.0", "fast-uri": "^2.1.0", "prettier": "^2.3.1", "cross-env": "^7.0.2", "browserify": "^17.0.0", "node-fetch": "^3.0.0", "typescript": "^4.8.0", "@types/chai": "^4.2.12", "@types/node": "^18.11.9", "ajv-formats": "^3.0.0-rc.0", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^13.0.3", "@types/mocha": "^9.0.0", "if-node-version": "^1.0.0", "dayjs-plugin-utc": "^0.1.2", "json-schema-test": "^2.0.0", "module-from-string": "^3.1.3", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^7.0.2", "@ajv-validator/config": "^0.3.0", "karma-chrome-launcher": "^3.0.0", "eslint-config-prettier": "^7.0.0", "@rollup/plugin-commonjs": "^23.0.2", "@rollup/plugin-typescript": "^8.2.1", "@typescript-eslint/parser": "^3.8.0", "@types/require-from-string": "^1.2.0", "@rollup/plugin-node-resolve": "^15.0.1", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "4c3fd464000b36609195c767c88f20f23f372058", "tarball": "https://registry.npmjs.org/ajv/-/ajv-8.11.1.tgz", "fileCount": 466, "integrity": "sha512-hTXUQ/hG+UjV4LdRdaaXBqjXg1w0DOT7VhCeWRZxt6QKLZy7T+aMQ0pZQkMn3QcWi52q7Zo/8EvW9Eg8htad5Q==", "signatures": [{"sig": "MEYCIQD+hoAkSlwJQlyy/4ZXuahT8eK6cARS6Pnlmub5VbZy+gIhANkL3X4ow9WgbJGNPl4OW3NlUzVA9VR2ONlEboTyWJqX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1021810, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjcWoWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp0rA//XPVVyIRStAeCnBmKzfRj84KTOmVGkALd7+yYgDJKEWSlsb3Z\r\nl+r8Alw7GZ4+M2NloIt8951jt3KGs+u5n5kuTFvMpQDpUU+h/cdrou+7YCU4\r\nhsYxA5CT3Gzh1pTtP0KpnR1mE4O4ib+9hsl9biSCuyHd+oji+hws/n0tVG9j\r\nnQdf/ENw9kXv5WqopU+KRlCzX5mo5MbAwYDaL0h5Su2SmLlgxM/kRb12fSqy\r\n5SbfV2/IAhUekWUBImiw2nk/o/HAKWs6DTHgTG3hiI2EdnmFkOvnn69TstA4\r\n4wpXNirJfi0JCQA64g7qRiPL7nvT08Tg/unUkm5tdlHYYyfqNRDMCY5ITEKZ\r\nx+u/hSz8NcVz8f0CeuBktwHC+ISfuX1cpRO7YBrcg0X+oFfnpcgKUhXSwy3d\r\nOjTY1XmneLklBTwBAm2AOJaOQz0nxoEnF+jtN8gNrFk2HBpvBrjrMWXq9r5q\r\nieM+0SI1T/XfMPnDIoOBzxgAhcSUmFYkuOO+tYxg3DZqtqGxpWmVTWA9y7Uu\r\nnb67WUXN+nfMtB12WCN6V7r6OCYsrYKSkcJrkV/wgdeuSqeiAZPD9SG1wJH/\r\nyVV6hZV7RKqlWyBhci6pXIdeEcu1ATxEa4gBajq3Sk8J9EDxiy1Op/fq+KQS\r\n6C2tIFuPTVo0ipC9ahkpbe4BKwxolRcvpOU=\r\n=jloN\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "8.11.2": {"name": "ajv", "version": "8.11.2", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "require-from-string": "^2.0.2", "json-schema-traverse": "^1.0.0"}, "devDependencies": {"nyc": "^15.0.0", "re2": "^1.16.0", "chai": "^4.0.1", "glob": "^8.0.2", "jimp": "^0.16.1", "dayjs": "^1.10.4", "husky": "^8.0.2", "karma": "^6.0.0", "mocha": "^10.0.0", "tsify": "^5.0.2", "eslint": "^7.8.1", "rollup": "^2.44.0", "ts-node": "^10.0.0", "fast-uri": "^2.1.0", "prettier": "^2.3.1", "cross-env": "^7.0.2", "browserify": "^17.0.0", "node-fetch": "^3.0.0", "typescript": "^4.8.0", "@types/chai": "^4.2.12", "@types/node": "^18.11.9", "ajv-formats": "^3.0.0-rc.0", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^13.0.3", "@types/mocha": "^9.0.0", "if-node-version": "^1.0.0", "dayjs-plugin-utc": "^0.1.2", "json-schema-test": "^2.0.0", "module-from-string": "^3.1.3", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^7.0.2", "@ajv-validator/config": "^0.3.0", "karma-chrome-launcher": "^3.0.0", "eslint-config-prettier": "^7.0.0", "@rollup/plugin-commonjs": "^23.0.2", "@rollup/plugin-typescript": "^8.2.1", "@typescript-eslint/parser": "^3.8.0", "@types/require-from-string": "^1.2.0", "@rollup/plugin-node-resolve": "^15.0.1", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "aecb20b50607acf2569b6382167b65a96008bb78", "tarball": "https://registry.npmjs.org/ajv/-/ajv-8.11.2.tgz", "fileCount": 466, "integrity": "sha512-E4bfmKAhGiSTvMfL1Myyycaub+cUEU2/IvpylXkUu7CHBkBj1f/ikdzbD7YQ6FKUbixDxeYvB/xY4fvyroDlQg==", "signatures": [{"sig": "MEUCIDOb+BMH16l/smGAuAnsoy0cVXZGqd0Xno4rsVIDPZYTAiEAymL3h/6Prh02hjqtm/Yl0AEKJTL3x+OdkYjsqLla/rc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1021810, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjcWzDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpAmQ/7Bp/MwLy0DCOfCVPnIJgIp6QBfNeJb2MwP1Uc1Sbsr2U4y/Co\r\nB5efXR87kkbUH/JajGYTeEOTKfHw9hFodxu428XCm5L1+7wlubzObIPfvmln\r\nUwkHPRepcJ3Acrf65MBB/i7CoRsLRZWMr+chVJgt1FkjkGeJry4+HJkkefsD\r\nZdtAKKjfrJ2r8dHgjZ+R7eOkOvHF/dXy854sxBQ/jMp3mOZX6x/L1sNgf8xu\r\n2lM5sscPfrUQajsOtixhRP/1SYM1XsxT+rsTYF9OOdqi9HpEMfZszgiQMuZZ\r\noub6F/IoN9ugD0d8gNJeJScYVEFtFfN9rBtTUXfhddqwhiJ5AuChkMv9nMzP\r\n6nV+3YajEXQihvhEcCWplSmHM6ar6yX75CboaR0h+/lc33Onx4/FwGTLajK1\r\npPrI7vX/+MYpPEsSpbf2ssJnF12bYEIyU9F7LW/kl3CcCtwdzzSrlsTzERPU\r\nxFcfSGv3wODEye/SK/CaDV3mX7U2Y+Ea0sk/Mcx9sXHiSidnQ/IFOkOLthH1\r\napMoD5P+Q04jo1nMGs0vpOTT5QQm+xdeyK+oBvbbhs3Du1Eo2JG3uQbWCggr\r\naxfSHpK+y9J7EJiEq9JzPsGv2vmfA30FQzDuEmIOF/xDDUUXZcBvEeUH3s/Y\r\nHcz2rV9LfwBukSMmjDJV+hOblvFWF3RfMe8=\r\n=Y278\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "8.12.0": {"name": "ajv", "version": "8.12.0", "dependencies": {"uri-js": "^4.2.2", "fast-deep-equal": "^3.1.1", "require-from-string": "^2.0.2", "json-schema-traverse": "^1.0.0"}, "devDependencies": {"nyc": "^15.0.0", "re2": "^1.16.0", "chai": "^4.0.1", "glob": "^8.0.2", "jimp": "^0.16.1", "dayjs": "^1.10.4", "husky": "^8.0.2", "karma": "^6.0.0", "mocha": "^10.0.0", "tsify": "^5.0.2", "eslint": "^7.8.1", "rollup": "^2.44.0", "ts-node": "^10.0.0", "fast-uri": "^2.1.0", "prettier": "^2.3.1", "cross-env": "^7.0.2", "browserify": "^17.0.0", "node-fetch": "^3.0.0", "typescript": "^4.8.0", "@types/chai": "^4.2.12", "@types/node": "^18.11.9", "ajv-formats": "^3.0.0-rc.0", "js-beautify": "^1.7.3", "karma-mocha": "^2.0.0", "lint-staged": "^13.0.3", "@types/mocha": "^10.0.0", "if-node-version": "^1.0.0", "dayjs-plugin-utc": "^0.1.2", "json-schema-test": "^2.0.0", "module-from-string": "^3.1.3", "@rollup/plugin-json": "^6.0.0", "rollup-plugin-terser": "^7.0.2", "@ajv-validator/config": "^0.3.0", "karma-chrome-launcher": "^3.0.0", "eslint-config-prettier": "^7.0.0", "@rollup/plugin-commonjs": "^24.0.0", "@rollup/plugin-typescript": "^10.0.1", "@typescript-eslint/parser": "^3.8.0", "@types/require-from-string": "^1.2.0", "@rollup/plugin-node-resolve": "^15.0.1", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "dist": {"shasum": "d1a0527323e22f53562c567c00991577dfbe19d1", "tarball": "https://registry.npmjs.org/ajv/-/ajv-8.12.0.tgz", "fileCount": 466, "integrity": "sha512-sRu1kpcO9yLtYxBKvqfTeh9KzZEwO3STyX1HT+4CaDzC6HpTGYhIhPIzj9XuKU7KYDwnaeh5hcOwjy1QuJzBPA==", "signatures": [{"sig": "MEUCIDUlNfrmPPpdyazCfMxzsfFxNq12gvw03YQC4NSX8HyjAiEArtmE6LpBj27OI47NQ8rz1edXIGPDHYY6UN/lIvHFndQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1024047, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjtDlrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpAYw//TMH1RvcZLymhiE/1HiaNjVvimnOUAktvya3J0wX7z6LhFrJN\r\n8AaXkZw9/dMXkCC7mC+SFXNR6hvLNlh3SdGzUnqQJHs8HPF9wDU1e2ZtRflU\r\nC9SIOr7kclT/TRhNFVHqWhDr6IG+VSTBKjmyLcRZhMHdm//i7NuGJDuUVTPK\r\nhJzFnWCNAQscAvlMoal8TND1hhWrMTeJ5S1yxMY4nJ2VB2oJLdK++xJ9fuki\r\nDt4rMFuOnDjW71JQgCmcrH+fGehn7c+Ub/jQRi1NczGLFT4LEiAV9TRlT8RW\r\nuFA8BUPQ/+GqPcgcsUDiwXqDZajoy88KoCHgHQ8KzdutJHxwY3ZinJJjFb9I\r\n2sQiwnXZfLBHt/MTCvp6BGGNhsJvedtFxnSITCgOXeE9kad33xBo5Ga1dOrK\r\n7fWRS7Q5gjzEshCWbzwsNic1YdVg/5L4IM3TXzT/yoNpS8DADZMEGq6bl6pW\r\nScZveGxdCaHT19fYXcq8gzFJk60pRLR6TcBjdu2ns3Z1ZNgDOJrqCk0pixCv\r\nXYf3ymaeWPKkTGHyPcjhb0Cfx/i7XI12PgHeQKq2XyW9TbQXqyUbRnUg+6Lx\r\n5PPInCZLOlStp1MukwAhdzxv1MZR8WyDeeYZM+L0PG67moTsYBkJnhKczaNB\r\nnpN7RVaoSHV+CokI6gImGtWOuPvahBTlBkk=\r\n=3rMY\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "8.13.0": {"name": "ajv", "version": "8.13.0", "dependencies": {"uri-js": "^4.4.1", "fast-deep-equal": "^3.1.3", "require-from-string": "^2.0.2", "json-schema-traverse": "^1.0.0"}, "devDependencies": {"nyc": "^15.1.0", "re2": "^1.20.9", "chai": "^4.4.1", "glob": "^10.3.10", "jimp": "^0.22.10", "dayjs": "^1.11.10", "husky": "^9.0.11", "karma": "^6.4.2", "mocha": "^10.3.0", "tsify": "^5.0.4", "eslint": "^8.57.0", "rollup": "^2.79.1", "ts-node": "^10.9.2", "fast-uri": "^2.3.0", "prettier": "3.0.3", "cross-env": "^7.0.3", "browserify": "^17.0.0", "node-fetch": "^3.3.2", "typescript": "5.3.3", "@types/chai": "^4.3.11", "@types/node": "^20.11.30", "ajv-formats": "^3.0.1", "js-beautify": "^1.15.1", "karma-mocha": "^2.0.1", "lint-staged": "^15.2.2", "@types/mocha": "^10.0.6", "if-node-version": "^1.1.1", "dayjs-plugin-utc": "^0.1.2", "json-schema-test": "^2.0.0", "module-from-string": "^3.3.0", "@rollup/plugin-json": "^6.1.0", "rollup-plugin-terser": "^7.0.2", "@ajv-validator/config": "^0.5.0", "karma-chrome-launcher": "^3.2.0", "eslint-config-prettier": "^9.1.0", "@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-typescript": "^11.1.6", "@typescript-eslint/parser": "^7.3.1", "@types/require-from-string": "^1.2.3", "@rollup/plugin-node-resolve": "^15.2.3", "@typescript-eslint/eslint-plugin": "^7.3.1"}, "dist": {"shasum": "a3939eaec9fb80d217ddf0c3376948c023f28c91", "tarball": "https://registry.npmjs.org/ajv/-/ajv-8.13.0.tgz", "fileCount": 466, "integrity": "sha512-PRA911Blj99jR5RMeTunVbNXMF6Lp4vZXnk5GQjcnUWUTsrXtekg/pnmFFI2u/I36Y/2bITGS30GZCXei6uNkA==", "signatures": [{"sig": "MEYCIQDNFqslEktujuk6W6MO0HOe90DvUZv2I0VQDGlyg2orUwIhAJJ7qttNPog4On2244hu0X8eGakzGcVBZ2iTC6j6NZIB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1030239}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "8.14.0": {"name": "ajv", "version": "8.14.0", "dependencies": {"uri-js": "^4.4.1", "fast-deep-equal": "^3.1.3", "require-from-string": "^2.0.2", "json-schema-traverse": "^1.0.0"}, "devDependencies": {"nyc": "^15.1.0", "re2": "^1.20.9", "chai": "^4.4.1", "glob": "^10.3.10", "jimp": "^0.22.10", "dayjs": "^1.11.10", "husky": "^9.0.11", "karma": "^6.4.2", "mocha": "^10.3.0", "tsify": "^5.0.4", "eslint": "^8.57.0", "rollup": "^2.79.1", "ts-node": "^10.9.2", "fast-uri": "^2.3.0", "prettier": "3.0.3", "cross-env": "^7.0.3", "browserify": "^17.0.0", "node-fetch": "^3.3.2", "typescript": "5.3.3", "@types/chai": "^4.3.11", "@types/node": "^20.11.30", "ajv-formats": "^3.0.1", "js-beautify": "^1.15.1", "karma-mocha": "^2.0.1", "lint-staged": "^15.2.2", "@types/mocha": "^10.0.6", "if-node-version": "^1.1.1", "dayjs-plugin-utc": "^0.1.2", "json-schema-test": "^2.0.0", "module-from-string": "^3.3.0", "@rollup/plugin-json": "^6.1.0", "rollup-plugin-terser": "^7.0.2", "@ajv-validator/config": "^0.5.0", "karma-chrome-launcher": "^3.2.0", "eslint-config-prettier": "^9.1.0", "@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-typescript": "^11.1.6", "@typescript-eslint/parser": "^7.3.1", "@types/require-from-string": "^1.2.3", "@rollup/plugin-node-resolve": "^15.2.3", "@typescript-eslint/eslint-plugin": "^7.3.1"}, "dist": {"shasum": "f514ddfd4756abb200e1704414963620a625ebbb", "tarball": "https://registry.npmjs.org/ajv/-/ajv-8.14.0.tgz", "fileCount": 466, "integrity": "sha512-oYs1UUtO97ZO2lJ4bwnWeQW8/zvOIQLGKcvPTsWmvc2SYgBb+upuNS5NxoLaMU4h8Ju3Nbj6Cq8mD2LQoqVKFA==", "signatures": [{"sig": "MEYCIQDboJr77iJs+8PtQ176Ap+wQj14nojdAST68QqHIe0nZQIhAK5t9aq0pSmzAWFu1+itcFSJprmsKMoyTVh/dKb5w/gz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1030755}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "8.15.0": {"name": "ajv", "version": "8.15.0", "dependencies": {"fast-uri": "^2.3.0", "fast-deep-equal": "^3.1.3", "require-from-string": "^2.0.2", "json-schema-traverse": "^1.0.0"}, "devDependencies": {"nyc": "^15.1.0", "re2": "^1.20.9", "chai": "^4.4.1", "glob": "^10.3.10", "jimp": "^0.22.10", "dayjs": "^1.11.10", "husky": "^9.0.11", "karma": "^6.4.2", "mocha": "^10.3.0", "tsify": "^5.0.4", "eslint": "^8.57.0", "rollup": "^2.79.1", "uri-js": "^4.4.1", "ts-node": "^10.9.2", "prettier": "3.0.3", "cross-env": "^7.0.3", "browserify": "^17.0.0", "node-fetch": "^3.3.2", "typescript": "5.3.3", "@types/chai": "^4.3.11", "@types/node": "^20.11.30", "ajv-formats": "^3.0.1", "js-beautify": "^1.15.1", "karma-mocha": "^2.0.1", "lint-staged": "^15.2.2", "@types/mocha": "^10.0.6", "if-node-version": "^1.1.1", "dayjs-plugin-utc": "^0.1.2", "json-schema-test": "^2.0.0", "module-from-string": "^3.3.0", "@rollup/plugin-json": "^6.1.0", "rollup-plugin-terser": "^7.0.2", "@ajv-validator/config": "^0.5.0", "karma-chrome-launcher": "^3.2.0", "eslint-config-prettier": "^9.1.0", "@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-typescript": "^11.1.6", "@typescript-eslint/parser": "^7.3.1", "@types/require-from-string": "^1.2.3", "@rollup/plugin-node-resolve": "^15.2.3", "@typescript-eslint/eslint-plugin": "^7.3.1"}, "dist": {"shasum": "d918c661e3e820bbbc65a320e182ee56a1aa978a", "tarball": "https://registry.npmjs.org/ajv/-/ajv-8.15.0.tgz", "fileCount": 466, "integrity": "sha512-15BTtQUOsSrmHCy+B4VnAiJAJxJ8IFgu6fcjFQF3jQYZ78nLSQthlFg4ehp+NLIyfvFgOlxNsjKIEhydtFPVHQ==", "signatures": [{"sig": "MEUCIQCLH4Phs7ynrYlQWVsup5dDbidiJ+1IFKAn4bsZI1O1UwIgWASN+6//7jgjZDELWL/xkdHSvrdzJt8NK/9hyXH/Zy8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1030771}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "8.16.0": {"name": "ajv", "version": "8.16.0", "dependencies": {"uri-js": "^4.4.1", "fast-deep-equal": "^3.1.3", "require-from-string": "^2.0.2", "json-schema-traverse": "^1.0.0"}, "devDependencies": {"nyc": "^15.1.0", "re2": "^1.20.9", "chai": "^4.4.1", "glob": "^10.3.10", "jimp": "^0.22.10", "dayjs": "^1.11.10", "husky": "^9.0.11", "karma": "^6.4.2", "mocha": "^10.3.0", "tsify": "^5.0.4", "eslint": "^8.57.0", "rollup": "^2.79.1", "ts-node": "^10.9.2", "fast-uri": "^2.3.0", "prettier": "3.0.3", "cross-env": "^7.0.3", "browserify": "^17.0.0", "node-fetch": "^3.3.2", "typescript": "5.3.3", "@types/chai": "^4.3.11", "@types/node": "^20.11.30", "ajv-formats": "^3.0.1", "js-beautify": "^1.15.1", "karma-mocha": "^2.0.1", "lint-staged": "^15.2.2", "@types/mocha": "^10.0.6", "if-node-version": "^1.1.1", "dayjs-plugin-utc": "^0.1.2", "json-schema-test": "^2.0.0", "module-from-string": "^3.3.0", "@rollup/plugin-json": "^6.1.0", "rollup-plugin-terser": "^7.0.2", "@ajv-validator/config": "^0.5.0", "karma-chrome-launcher": "^3.2.0", "eslint-config-prettier": "^9.1.0", "@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-typescript": "^11.1.6", "@typescript-eslint/parser": "^7.3.1", "@types/require-from-string": "^1.2.3", "@rollup/plugin-node-resolve": "^15.2.3", "@typescript-eslint/eslint-plugin": "^7.3.1"}, "dist": {"shasum": "22e2a92b94f005f7e0f9c9d39652ef0b8f6f0cb4", "tarball": "https://registry.npmjs.org/ajv/-/ajv-8.16.0.tgz", "fileCount": 466, "integrity": "sha512-F0twR8U1ZU67JIEtekUcLkXkoO5mMMmgGD8sK/xUFzJ805jxHQl92hImFAqqXMyMYjSPOyUPAwHYhB72g5sTXw==", "signatures": [{"sig": "MEUCIQCFQ611wYaYEFUNq8GGDOlSz9MN2z20RUOCxGukKrLFKwIgP+OxqsP4z0wSORclpm3/TlIY7zp3dCdb2FoHIVmYd58=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1030755}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}, "8.17.1": {"name": "ajv", "version": "8.17.1", "dependencies": {"fast-uri": "^3.0.1", "fast-deep-equal": "^3.1.3", "require-from-string": "^2.0.2", "json-schema-traverse": "^1.0.0"}, "devDependencies": {"nyc": "^15.1.0", "re2": "^1.20.9", "chai": "^4.4.1", "glob": "^10.3.10", "jimp": "^0.22.10", "dayjs": "^1.11.10", "husky": "^9.0.11", "karma": "^6.4.2", "mocha": "^10.3.0", "tsify": "^5.0.4", "eslint": "^8.57.0", "rollup": "^2.79.1", "uri-js": "^4.4.1", "ts-node": "^10.9.2", "prettier": "3.0.3", "cross-env": "^7.0.3", "browserify": "^17.0.0", "node-fetch": "^3.3.2", "typescript": "5.3.3", "@types/chai": "^4.3.11", "@types/node": "^20.11.30", "ajv-formats": "^3.0.1", "js-beautify": "^1.15.1", "karma-mocha": "^2.0.1", "lint-staged": "^15.2.2", "@types/mocha": "^10.0.6", "if-node-version": "^1.1.1", "dayjs-plugin-utc": "^0.1.2", "json-schema-test": "^2.0.0", "module-from-string": "^3.3.0", "@rollup/plugin-json": "^6.1.0", "rollup-plugin-terser": "^7.0.2", "@ajv-validator/config": "^0.5.0", "karma-chrome-launcher": "^3.2.0", "eslint-config-prettier": "^9.1.0", "@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-typescript": "^11.1.6", "@typescript-eslint/parser": "^7.3.1", "@types/require-from-string": "^1.2.3", "@rollup/plugin-node-resolve": "^15.2.3", "@typescript-eslint/eslint-plugin": "^7.3.1"}, "dist": {"shasum": "37d9a5c776af6bc92d7f4f9510eba4c0a60d11a6", "tarball": "https://registry.npmjs.org/ajv/-/ajv-8.17.1.tgz", "fileCount": 466, "integrity": "sha512-B/gBuNg5SiMTrPkC+A2+cW0RszwxYmn6VYxB/inlBStS5nx6xHIt/ehKRhIMhqusl7a8LjQoZnjCs5vhwxOQ1g==", "signatures": [{"sig": "MEUCIQDw1zJHeG73mZVjAPba7aayPKlrtoT3zSlbaJR486CJQAIgP7tUXsjHnEo/ERKurYXQq98nNaXtM7Ivj5h8AMhmqnU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1030888}, "funding": {"url": "https://github.com/sponsors/epoberezkin", "type": "github"}}}, "modified": "2025-06-09T04:08:01.833Z"}