{"name": "email-verifier", "dist-tags": {"latest": "0.4.1"}, "versions": {"0.0.1": {"name": "email-verifier", "version": "0.0.1", "dist": {"shasum": "95ae4fae1519af4f7adf5ca5cd377d3129079825", "tarball": "https://registry.npmjs.org/email-verifier/-/email-verifier-0.0.1.tgz", "integrity": "sha512-ER1bO+h9yZ3xKT+USdwOx7uBG8wj13RlAImjQUec2JaaKjVU5QMTq2j7sC+v8p2+zowLkEuI0cjDMBZr+25+Fw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFec363Y1We3B7sXp0sQADibTdL9nfXJ/b7gCO8xQQNHAiEApR+G1XWlEo8lTWsB75ehmHXhakeXneF5j2IP5KyM/Rc="}]}}, "0.1.0": {"name": "email-verifier", "version": "0.1.0", "dependencies": {"backoff": "^2.5.0", "request": "^2.82.0"}, "devDependencies": {"mocha": "^3.5.3"}, "dist": {"integrity": "sha512-rgxhPsAPKUm7LVVfmuMQfAhly6KgqTakMWW89LOESZ59sLNgubTrjWqoxm2Br8XousQpYC/qR2Rn9pK6ibrtyA==", "shasum": "ab335fe04991a40ce428e2c6107bdd50babd75dc", "tarball": "https://registry.npmjs.org/email-verifier/-/email-verifier-0.1.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDv0uZXW5H9G9G5yQYiLu3DaENcEiJJsqKFewryxdtLWQIhAP+jbU/GAJ60sgTDqx3wZOACHUz4nrWFe1nKXlyLEgh7"}]}}, "0.2.0": {"name": "email-verifier", "version": "0.2.0", "dependencies": {"backoff": "^2.5.0", "request": "^2.82.0"}, "devDependencies": {"mocha": "^3.5.3"}, "dist": {"integrity": "sha512-+ALdkH5nQYCiyuUSzuuaUjc1mi8/zysMdnC95g19LZVJX/hx1p0Yg7vGr5vqUuvQh6nEMbQTvHQCtW27+d5N9Q==", "shasum": "70a320f5eb2a6011d7ad92cb99d8b8d2f3b9c39e", "tarball": "https://registry.npmjs.org/email-verifier/-/email-verifier-0.2.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDaQ5xsLOQfQbn4HE9drUdT0c5Bba80kBQ9Z5cMDhb0pwIgIZolGgvZWuyuh1ZfIPnt5/Tlbtz7t/YUw6NzkhZ4V18="}]}}, "0.2.1": {"name": "email-verifier", "version": "0.2.1", "dependencies": {"backoff": "^2.5.0", "request": "^2.82.0"}, "devDependencies": {"mocha": "^3.5.3"}, "dist": {"integrity": "sha512-n//XSFEtSwy4I80QLTOqp2FpVWd4EFbHBwURMTbFOJ8U1xSM9opqoxA3k54WtJnQIQ0THI77vRTXA/jXC4WeLg==", "shasum": "3caa9d85a87f21723b607b2ee0cb0666e6383ea4", "tarball": "https://registry.npmjs.org/email-verifier/-/email-verifier-0.2.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCx87/ZfIs0brA/NC5lcUVnWWmp4ZczaTnM6YSO5owaIQIhAL1zoGntsY3xRZJvgSQ4cYVj+5LyjynXpnuIC7gMGjSA"}]}}, "0.3.0": {"name": "email-verifier", "version": "0.3.0", "dependencies": {"backoff": "^2.5.0", "request": "^2.82.0"}, "devDependencies": {"mocha": "^3.5.3"}, "dist": {"shasum": "e4f56d2f84263cfd8bb5b3208149798da3bad51a", "tarball": "https://registry.npmjs.org/email-verifier/-/email-verifier-0.3.0.tgz", "fileCount": 7, "unpackedSize": 34195, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa1DF9CRA9TVsSAnZWagAAeK8P/RZIN3fUI3BGV8DCYCKZ\nhkiQ80Iulb2e2I1Ye4WAYst0PZ3X0lczjcNXr1/Tp7HDeOzcE8n2PRvze1SF\n8MnGBFyG0M5ZN32Cxa+Plqf2RpK27GorGu6BxIGETNqJnhCWmSjD5DDKAQKz\nBksAPc3Zzf7x+G6QLtQ3NfjY25nychAcKP58XNMejz8Rur90agW55TWRj+4p\nuprgqXtWTukLLQa64imLf3CVUqcEvOiXO5Fb74e+Ovb97p75rd2cflv/yTga\nSupmSj09l9xykBfXzV7j20Sin3dCNtZ2F2ZO77kJwS6ZEIi8LRcsSMQ9aq+R\nOxKz2HsGjEI8KYkSchUP6R0c8PTEAYSsutZkn3Qe0iLo104DI61DQ6mUdY+t\n4zlYTAjkfTtaM7e4C6YuqvGTtzStSIq3pu4p6JRP3XfjhvZVNwEMJjhgbPKt\nWpDdXkunYwEOsgU+DCLuc7Ui0ioF1UvIGPaT2/01+RWZ/Mlro8R/YxVB59rW\nWMw7UCfGe6sQ7hO3wTdpoytwn90+/XMOUWlWxm+QyTsGctHBw9CzKm12ZCmp\nvR7BDXHsnyUxcWbu95dSFvn9Igk8o6k/7V/QPfN0U0Q+U1bVzXevzees1a6b\n5XjWLCCtTse9zZMtz5VLGOcp1WFO+Mj//yzJG7LJgIMDyyjfhKGn/JftGwnE\nx0tO\r\n=w1vD\r\n-----END PGP SIGNATURE-----\r\n", "integrity": "sha512-2CQtIVyM9c9sSbNd8LCkW7ntr24skJzv9gDzE3kbaIe5JvOzkOFXblf3OCMiy2XZzLCdp6BtKfK6w6Robgm0Kg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC5s8FsMvWGE7xAdOGZrkcaXwJ4hWd7mg0udoWwhGUshAiEAx+K4Pn6elbCIOT5xntKqmLAPSx7IZCXd6z2zQXS3HQ8="}]}}, "0.4.0": {"name": "email-verifier", "version": "0.4.0", "dependencies": {"backoff": "^2.5.0", "request": "^2.82.0"}, "devDependencies": {"mocha": "^3.5.3"}, "dist": {"integrity": "sha512-l6P4iItwG4jSkOWIq/zXdUk58Rpjv0Q45zuXmXapEXGcUSA600K8oH/RyNpoy/By0PhdhmvAxyz94f2IHxq6EA==", "shasum": "4c85599480558de3d0c856bbdaf4573f900a5fa7", "tarball": "https://registry.npmjs.org/email-verifier/-/email-verifier-0.4.0.tgz", "fileCount": 5, "unpackedSize": 12342, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa72N0CRA9TVsSAnZWagAATRcQAJWDg5S1luO4rYzvhAPD\nq/nf1TnToydi4REjLBTkFYc5PcPVWvWaolaUv1ojwDEGoyQ9ejmcaQxYznOa\nGZEUlsN25l4pdu7BboglOPprgRZe5stpY0MlmZ5HPP30pa3ILGCzbe38EdGb\n5VMZPo6GDxrJYEQy3okMeo3TJg+Xve094wEXY7VlOySuV72BZAOaShHLMu+u\n54bZYrnW669nNJGTG6EM/2VGR+6hZWUXPEqDItyPxJ44OUzfS8jZdTMNQ5Gk\n4YJ3wVkoUL/dq5RfOvrTE4+VTsdwv8VVYSz8QVMLdMMpxo/2UgEQE3ja03HH\nxQgnWugJhmMmYLnENJ7T0FpVV8s6BiN7f8+sgPGa9DkIawrIxmbMZ2tZWugl\niidlA4anl2UZoKuCISGuYkttG17N3udx25nKrVnalHEhjTLLhi7iqQgBxKvq\n72n+jIX/575PRws6otdLatDbW2Lj2LLA8z5lYfKtijmnAUBb+6QlGLOz9R1l\neTyJKQYbemtha/JEq8ggKsI9YIm2TXm9sOJBv+0FymZaEOv0veOQ20wbf7+C\n7cnVEFqvm4j3iVbGv9p1R3CPKIN9L6k1rLLIwz+TMAWUh3zqev2gy++RsRHX\nEbZFl+TgIsVC22rRvZlJ/KPC31oQevHnZrZkCP+83p7G7oVYPKh6qOqiEDmM\n0Zrx\r\n=Avxm\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFgjmnWZwMG+tzQmR+LgKoeCgw0N+n013zJ8XiMTIdsUAiEArnfjGM/j4wTlCP0zqAfVjrf2jYnBAhG5Elc3g/WiY0A="}]}}, "0.4.1": {"name": "email-verifier", "version": "0.4.1", "dependencies": {"backoff": "^2.5.0", "request": "^2.82.0"}, "devDependencies": {"mocha": "^5.2.0"}, "dist": {"integrity": "sha512-WZAeqKgsGTCKfuvHimWTYF4Lix1sq7pUeDDAMLT1qrmo7iu8hgxGqZ+lTJn7SEL/RcYqXrjDL2U+NK3SWVkgFA==", "shasum": "925d81adf44ad9fa1246a1eb8db83a7c22835af3", "tarball": "https://registry.npmjs.org/email-verifier/-/email-verifier-0.4.1.tgz", "fileCount": 5, "unpackedSize": 12342, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBkFqCRA9TVsSAnZWagAAgDgP/3CVwxywqniN1uQZH5ux\nEkv0UiW+W9Y8aYDMECZ3lBy/6gddY86+Mr789+YKDHb3Mw2DsaVi1qku8TIM\nDIMble2b3uzwpBqQ3jmnw2r9XsrbQDWAnRbH7KtLEG6wKe80IkBN7ezHOd5e\n+PIMQDSuzjXxP+U2YtdxwUTN4/LA2RdRhjHAm4crNPyBa3mlakDm43g67RQh\n9QDcc1bDz212t4Q2JMIo4gRmA2Xy6mGyLWepBQfHxXfzaW6c2IrPdylxYS8L\n4S+apvX1kvrD36NviH+A0vuxVuvEYN95iHD5QWmjjAZN2S2wi80Ay4QBbR2v\nq0fYEOqim0RtS7BmKS8mAOsMCdqjOm45tevOjEI1NG14/MonNOcHbfOEwD0n\nN8RvTWwu88lXMIZukXH2jwXCbQd+nRojTxZFTOBx4NI/UukSze0/tJR0pRfC\nsSWY7EPzuGMQ3AiHe2vpJpkTgu2a/56xA3fSZtVLKg3iyWfvLMp3P3jhPfps\n15g6ogR0mX5TTjbNtYUFmyFPZN1F3Ewd3HYbD9ZMlp0HAWk0rAd/IzZ6rcZZ\n7HaF9QqNpJK4DHrFqCHHUWq9HTy8Gu/LLSGRHik7yOu0RI9fBQungf+yQPjf\nGyOv+wUFe0k36ctzdC6sxcwQKvsvxM2+XovHOUjghcFbBS62bNquHFL5r5Ui\nm+U7\r\n=yroe\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCQZofLT/w7YMm8TZGzPY+uHi8Pt28DE7Xz7gEfKU0yWQIhAMPkdYNfh354a/kVpiHAX/5zZVTNDqdtICfGC5q0pSMv"}]}}}, "modified": "2022-06-16T09:26:14.986Z"}