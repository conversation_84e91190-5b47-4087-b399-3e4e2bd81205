{"name": "@tailwindcss/oxide-win32-arm64-msvc", "dist-tags": {"next": "4.0.0", "latest": "4.1.10", "insiders": "0.0.0-insiders.c5a997c"}, "versions": {"0.0.0-insiders.8fb9ae8f": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.8fb9ae8f", "dist": {"shasum": "db211108605c0cfff59da4969d117708186df57f", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.8fb9ae8f.tgz", "fileCount": 2, "integrity": "sha512-li3dj1loR4iFLLFN/80Hsv1h1O5modaa49wt3HmSd4Ik14d38Xc+yntqIkyofo18J+8KPRDBg6VdR/ajRsnFyg==", "signatures": [{"sig": "MEYCIQCJCcww3VMN56BEReIcPlKv9jlOEO6P7nSARXE/Lr81DgIhAJvk0+exlWMB7dgggl/tDJFiyVfgjsiV7ZykWA+3pdAT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 437, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjhOzaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo63g/9EgX4aSEGbbnuuJf1Nu2tVtn1TCLD5jIjjjSSCm7tsBO6Eosx\r\nO6An+AHUSxkGpZTjKo0xYH5dWhfI+oFoPO0lY3xScUJoZV2gXCRYhJ3LmCA6\r\nK9/sGhJBnvyJaIenaMTw+VAueZOHW/OR6yxMiuuuRfGPkb1FOGInLaAfrITX\r\nF+aEtAQ/+patZtxgfUpqM/HhDksL0sj5U1su+jaeNAhKbyMps+jj/5D8bHBl\r\nz7VkxkhBJuXnAiCXSCIArgpptEax66bhffnFWX3aD+tyF69fuEDXRBiZCCt7\r\naONy9ZLNAS2+hp0Vln+vM/1pSCd0wat6tX5pqdFSpoj4yZLYdstOB5CnBzf5\r\nSkKb9zJ9HR2cliBALEZGW7p6B5F8nyRUN0P1Y6jSBFkyPZMKJOGARHp+1yxa\r\nratwjdJS6xGdBNO/yY+KfLKVFoxxfW9QJe3SKNt3mVwQB65lJY2j9SfPQMib\r\ndKjQLk+SxBpD98rW0nnQNgtcxX2HWW3+c6zVqZJlqYC8JEQxSaVK+p/11G9Z\r\nYjUAF4v7Qp48n1yZLKl9NKXII+JOqiXgqu6Wx3QtliLACgFE7F39boWI6wH4\r\ncjY4+x5sMBi5vl3uth4JlqKfy7fON89XkxdSNJMtJx8YXMgiSninSWnW5WYC\r\nyvjgWC0UGBmWHDyQdLKuFUdW59geniwWenw=\r\n=ayiS\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.4bd4f518": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.4bd4f518", "dist": {"shasum": "3459df6814ef50a94e56b8d85f86ffb2f1d03dcb", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.4bd4f518.tgz", "fileCount": 2, "integrity": "sha512-ZI1NMEdYpOuqrH50q3p0vlJ422Jiw9nHJaF//F6QzIfg6jVXodVa6TLWKZbtJ07G+giba9Voje9GGQ6Q+PReAw==", "signatures": [{"sig": "MEYCIQC+GgGui7ue23JeNYv7SQ5+ACwa7f+vMDRtZaBCNcoeYAIhAM7Rim9ibKcEkljNHiNiT7mRsyp6O2/m0pi1fX/c+GKF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 437, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjhSLiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoWqQ/+Kui8N3Encns+zRtz9tueGP/67wejG0LFJ+IW3WvxX5MvQw8V\r\nuJKUyK4t8KHgqjYRgomWMAa20hlSMJjSFK1j82N/MCMqn/Nbbnn216K987ki\r\nTKuS1Qn3FZb6fnMD3j2q5/YmfBRP8Ed99uTEBgS1liMS2+ilh4lzAEQGHy0S\r\nJf/AI7Bicm1CWlMm04w0E6PUpgUzl2puHHC/WWm6m7/hytZdzjki9LtC84ZV\r\n0UP/eltnyx8tkq933ZTvA2uqT2Fu2NADt9sewb75A7bsArZV2qfejDL5V7tl\r\nlU8cj3ZT8BlNNPJAJmJZwArPQ5O1S6Sp/9pYBBhlgJMHrU1eyJFucUCEcuIr\r\nkUE0Pmngw9am9YFswWMAWehsaLHLGaJ1LnOFw8cs6sN4ZIIa9joU+O46SHDO\r\n4fe7djpiaHvPRfFcq+0GCV+K/vDtz1MSYnr9YwcSbbCdy2xoAE8GlTm142CP\r\nIxfwHxPSEy5s2MsgpI42/kVYwgRsLnghOjqqKyi2r/j2JkFEgTy3fdEZSt4N\r\nZBinGeLT+y0XkiowJN5W6Gk635hSnZP1aOWsltf2gQnlH7s7LCOaArkLvUVD\r\nqyBGsQ9U47H5ouQvcN4NgQfYAQq13tQ+rO33lBjmTYAmCe3mZMm2Sw22zO8X\r\nNPKGhWMDu7Z9Nzd7qwprMDQW3iZlQ7aGUfE=\r\n=3Jnb\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.e8333c94": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.e8333c94", "dist": {"shasum": "6a8119491975f2f3ee4a22200a2b2b579637852e", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.e8333c94.tgz", "fileCount": 2, "integrity": "sha512-R34Y8c146yVZ8LUXPu79h040bo6uuVKqUyJ0HKfxlPNxHvPFyNtN+57vTgi7r5QsSkWZRzjIee9r2b9G+HqpvQ==", "signatures": [{"sig": "MEQCIGazlq6YmAVvy8j5vFlrRx71CrM4Bi9dn+xPLdjhyetwAiARYv0xdbeR5DD1c/PYzXMHWWF+9HZbLIWn9yGIpcOjFA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 437, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjhSt3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr0yg//dMB0+GlCP1g4rrgha4zOwJDTynBNXCkTuKe1RZWjj5mKFVHB\r\nor3mGp3hCGWViQ4XqsEBhTwdc9oGCbKR7y0JuBntK/sHTsaKmtVk9iBNUBcP\r\n9Bd9Tj9dtc+xso8YRd4DbtbHiDqUtD1NbDWaix+2WCZj+YYvZw8N3ZMi+QJD\r\nlr2zuX0Dea3pLwr5ZuGp3UTTSp+/EPNY529PZYYeFGJEjRqLxR9WgDL3rHHD\r\nZkJRpFTewg6BMnXeWP9XCbUOXXdYc68UqI1HoGjXn3Wp1jScqrxMO1M7xPx4\r\nR+xP6zNCX7nva7hYrWuc9s+lUYrgmK9jk4UJ2nsXBcYllD2VbsI8qUs92TIM\r\n6/I7O9Ys05v4eUJ2gyWw22CL8QSfhJJv0/ecIM3dUr1h7SRvVSOdmg4pg3TI\r\nt3Tf0TEUrSqMqwxMbaqQOcE67msR30ltB1yiHbgfI3XeCgacjtidlg/xy4Lc\r\nGkpWevO8Uhx5qnM9jWv+0c6BvX8Gy8g8TVzlE5F7WoUfB3R5tGhvBpE/gp4O\r\n77t6sfAwpaweDvhX2nTQSQiiDdq5X2JbT+nYNI/g/p8bpQ/Cng1/ssKT75An\r\nivd6p//1JOuuKSkKc5vMXIOHQzgg9pWcgwMEpe+HUA1nBSI6OrG91uqKxmFb\r\n9aaylwuMxQCHhBaF6eyRCuZYc0J/S2UUGA8=\r\n=OrPy\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.2201e49d": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.2201e49d", "dist": {"shasum": "fdc3b6cfce3b61c9a68aee8647e268b56f8fcb21", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.2201e49d.tgz", "fileCount": 2, "integrity": "sha512-7n3RdWyvj0d+0hfaGv+/ZBo78zzOlM7MkMAZ/JbRfS364tgVGXvRv/yMDjVf+nPUdqrxsqrLsM4G9FBcY5GZxg==", "signatures": [{"sig": "MEUCIE2P3NDRC02WLOUCDtLYfYtYGblJpZVNj6aq/FKBcWgBAiEA5E9GILpbIS+LQwAnbWhrvYLkO9SFYlX2khsS0tjLc1E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 437, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjhTjUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrj/A//Sz4PfEm6eIHffLaKKedSDwbk+N2Vyea85HvmeN+kZxSWOiuI\r\nKXSOaolwoWjjZ7yQQG+2U0432zyNkd0Nv42bVR9FaMuFQniyfZXc4ZlWuaDp\r\n06BeS9uwFncyVkaW1CxgxAGPAUspDp1ORxLwuH2BrDIleI1mi4Wxn8UUISDg\r\nyeHpqw7hxmyuOe60vx2zTx9ERQMTOrCrufj1kTioDL39RNeoy2asmkjs1NKF\r\nOSFXt8V6IFx7W6G+ukoGrS+uvV+eM969UIm25siVbD0HWNxi2YeHM7w0NQKC\r\noIcY9FZbKaxnBNubbvsTLm/5tg8FOqd5e/48XY4qnsVCCdb3TPxSQCAf6ou+\r\nLuNgpD6y9Vc5Zy8K7shQ87Kp5TzTi6c1m9+KLk5JiIruIqi1AjVB1AXkitn3\r\n/mZPOds7kL0R+lYlIHH1W0EEuu7hxnnrHbPQ7evBcevKiJQx5jV7ew7zDzz5\r\nzgOGRHQoneva8VogOr5QjkSPl9I5T/FAKYMD67wl7mF2JCFA/ICU/wwE0EIU\r\nevnEZkr8XVe5BMbuKctzEGyRKW0oINFDsRScYaCC+UrIHa578z0YcjbOS4ZI\r\nLD2ADT5UNankm3OBEAZIvR2hvfkqfujoLMs9PZuGfkOcSUXnXar5kR9hmE2/\r\nBvovadgu1sNbLbHpsZDp/C8wSOSh9lKjg/s=\r\n=SXVn\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.806775ca": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.806775ca", "dist": {"shasum": "c9414326c739a972992ade1f7df0f5ef2cd3f210", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.806775ca.tgz", "fileCount": 2, "integrity": "sha512-yFAx7EDfFUnmhWJUZJSE1ZU/RdMgmzumiE09Pa4wKGXKESQVbdqPJQ6NPBmDhPJyV6jJY7xwLDE+OPEmZP8i0g==", "signatures": [{"sig": "MEYCIQCCOn0I1anR/4rxWL4wGbhPugl7eEvrNU6VFLkcna9xdwIhAITq/Vx3UKCaHBNxcF5zxhKB12JACN0T5mRdqKo4aXCZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 437, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjhT9xACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrLNw//cgP23ax1LeRluW2G6DaAo/G4PYsxK8UZw4QxNMPvvP8cgF8C\r\npRwSe8ZIPg/hYlAS7Eq14rDMr7bv2WXdflXktbrfXErBDKzZhN+zPPkthnf6\r\nXqa+X83PL1LOvzc3Kbt4CUQvqV9FvQlcHDIE340D4cAoS7H4oc22yEo2M8Ba\r\nVslPzbmY+KQgyRxdD7QsfbW4xBbxEurcpjTSErcaKJBdq9tUMGHIT5ZQNBgH\r\nJFMQJyDfELhHd74Ii8HD0WxajWxqmKokW89B59yKAPyHcmjBlacovgN4/1Gg\r\nfx1f5psYN52iobxlHablVfhCLrh+8t+MR+6mLF6oJ7jGj4ZMJJNK31+DwG0L\r\nTfRKkU0R+HQ3JFLcLz1I1mtapBFtLtCzu94lw3qeIN1PHa3WVBEHC2Vx4RlA\r\nqn56Eic1vDiss3sLbWY9DclcU/RgsnnW5pejC2jxYunGZHpo/LMviy4obI2v\r\nZHdkfLRHX6lXzqh6Ou5InWIQTZpUSVftXZHUJ6gaall8pTMiMVhpjpA9bq1L\r\nt3NH4esmFL6AmqKH5FPoybZ5KETuqHoN7q3dWbFgPbd4KEX3x9/iRbh7QHyK\r\n+m3/jw39ZDdolyoaRKHMct/gImHkG0ozpvf0TC2mnm7BVa8cpqmRCvUDO+uc\r\nnis5531IC9ZPimJZLdqGml4uezj+ihf92tc=\r\n=vuSW\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.1bc08c85": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.1bc08c85", "dist": {"shasum": "22014debfdf424489bd5306f9cbaf39317f59d98", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.1bc08c85.tgz", "fileCount": 2, "integrity": "sha512-AKJ18GERX/VyL+tv2Fa7pZFe66dPBPPwKYZy2cvVI3KQL+Rzw0FxwKPeQc91/5Kt8jbqa0Z2dSEaDMDi+r9Awg==", "signatures": [{"sig": "MEUCIGpIAVYoxe2w+hv/wrObyBiJoAqMnqZM17RXlweSlb/+AiEA499NgC3FXNIcfEsgkRxZ6oXxqSxUvHtCqEXU6IMCKmo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 437, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjhUsrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpJEQ//cllJJlheHLQ6zIBzh6g6MwHWace0rsdzSpb6cdajKh3dD816\r\nHqxgv2zWXlgCOXE750ZLkNyotvlAlzPNNHo4Lr+L3vPkapAncgE9VEQLNbST\r\nVho3SOMpo5ugdH7v7x1QATa7z3/CGRvEfCwwF2yPsh7eRY765bkI75n/tOos\r\nFzpn0dhELUEiJjXk1fnq3rIf58kdeGmVdf4gmxjZY01nBZI4QVqF3F08H0tG\r\nvclJhXSPJf8jO6s95yRQcxdhcgckWeLWzWJqvgCqrZgXexxppkdtdHAyHPQY\r\nGcl3x1JfRDXFvKXW/yoQM+3oA5siBOc1qgDqyr2lGe+DEfMb9djsoP7KJtYf\r\nRMPaYYyZNGO3mEdTVhcM32D/g8rHyLvM1iazpIyYywWB5M7P2S1hZodoj4Eo\r\nz0/+oD74H6cxNbMXaKCc8df145rqVHI0paAnmNbrKoVqBjgmS+ashBK62nLz\r\nkJYVq0OZbxjLoftWGaNR53FrxGgn4YT417mlktzEj68jW4kXa71BryHVXEzZ\r\nHREjAnAaUrSMM7PAv8+hMsl/chK/GS8ftAc7Es5p9UUxjvZZoN7j/nlqMi0/\r\nitJR6Ax5M9B3h0tRxOR4FYLZGPwaA7xKttxzI1JQbdBr8bw7WBZe38X2mA6Y\r\nWUdu1/1n11PUCa71+/Vww/6BtvIdm3hwUuI=\r\n=/LRa\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.143eff4a": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.143eff4a", "dist": {"shasum": "f942d4676393d7eba159aa4f29104824dda2b92e", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.143eff4a.tgz", "fileCount": 2, "integrity": "sha512-SMOy01q9QidZFgQq3LydPW646p6sniYVHSm5gFmH2FpXdjJZhaTfxaokj5COuMTbHHV11Lu1Wpbyj/NH0UPmlQ==", "signatures": [{"sig": "MEUCIEk9V9rXm43iW4ogUtCVnj2i7FniAP+lLIpeWitiSIMgAiEA18e8q5dQBDZVilXwJjWO9j/gpLeAxIGXUH/qWb40x8c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 437, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjhVDlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmozKw/9G/TNALl1yYHXQ0n1n23iWg+SfwQX3VLwcn0/vvqo/XzGJULR\r\nyyfoVYkNpANs0SyB1qzeBPeDXT990gr7vhezlAZlLv5qe5bTXTdIYPUkNR/j\r\nwGxjOjgkpTjJ3U9yEjriZbaRp0WVow/hbeenMt+gtnD8kExMDMd460JnMtAK\r\nsAoTS54FWl6ivHJPvz49siJ91JFs7jMzjDX7TwIQ7PbLoAQP1klAxnfAn+Iw\r\n6n5GpLbob3VgX9iIZa6o8cjEry/h4CL//tFOiCkpPOriFV3fDXzYUywSeKQ7\r\nUu7+zn5fgb6+cZhPHI5ElUghGHdCqeJrPmmwJxDCH9YnZEMxp60qVOG48J3G\r\nKktAWsFR/j24edftQTinJWHjvtusAP4R2RbF4CrjHPtfPBhMyARv39QX+/rI\r\n6jJzk961frV7IMzs8piH7vaLdfBCR0a5deIE8QahjZILaL8nIa5cEwHdd+Vf\r\nG5Y7r+mx4I3/1LJW5lj4R4NAjBCyvSxS4g7auUCgyC77fS6M6Ed3znAa3/zL\r\n4XTR7mJfUDr+hVk2eZKe/E/4GmlbPkvMDDx7TFFyZ0LGBul8XXMAXL+Teo5r\r\nIxzjzrBef6MZQZBXTigq2t9kPYVTUMnCmJkhpggDM/VgkEm0QxhQZLnqYvkY\r\nzwTcha9ntHNl/SEK+6zyI8Q7fiivG2ptNMM=\r\n=vfFd\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.04469dbe": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.04469dbe", "dist": {"shasum": "56b870b2553df6a11e50b9ddf2148c1273763a46", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.04469dbe.tgz", "fileCount": 2, "integrity": "sha512-udPRzwKb8Z6wyIaoEfni7T98hPMVrqAyOq7ImrJ1IiWaCbeGJgsmK3ePkwPWXAQALBH0LhmBGoijX4FVm8hXXw==", "signatures": [{"sig": "MEYCIQCtReUNnXjCIT0NREbTLQmSbHMYWS8A/xNx87MgFojG6gIhAMNuH6RKWn6QHAiGzGt7qX0xEI1qb8l19W58SWk8fVOJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 437, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjhVQaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqlig//eEPnU3nnoRlm92nOdFTPrRdzvKyPX8EM2F1OAPuD/UT1Zjf9\r\ntnRkhzuBnJAzyiGdmw/83MQGGjG9qNkoXqV5nYZcAdBD+e/v9G8MUo1Bltzt\r\nAq+1XhqzxvQQdZBDabblkLc2f5j9Sa32MkRBZfbG3szNzllFnTV+EbauHebg\r\nr+L6NzbEegSU/XWqR3tnS++0AH28+6Iq6zc3D14gv4vHsGzXbBhBzXSSJZCJ\r\nHokSmKqV9QZhmZYp2EEkr4D5i2f3iytxjsRassBid3UAznJhbBjV5V48ryBr\r\n4/Qq9o4EGw18ln5bFs3wCJ1opHbba37xCEQyzmCtoBE4+Fub200A2nZysgfX\r\njhHm4Dht/1y4Vvvf1a9MoHv382nU8E0zG+nioZtqCZGHb6+rWuSFckqWFp6c\r\nfjngrQxlhEr+/2+QQkJvNqD2b070n8JwA6S1OYEZjJdjbYtwQ71HUgDlIf45\r\nHyyotzEQfi1upbuX3Jzapk4OTP+FK1e5PpzPXMWYkWA853dg8cqHAufAS9D+\r\nnxmpCWVJSWcZaUHAx5TYIn4BfpKUs1K65r6PpSbLEiQpohSMa7+OUZxRQn0X\r\nWY4Fcab2fzUdNvQyztYgB0XmPJHQVMjwCO+0F5SFxLPi06ZYDOq/YGsz8Cki\r\n0UbSnhat/L1Czu87RRNdkfDrZvVuGU9K6yo=\r\n=e1ct\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.45789143": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.45789143", "dist": {"shasum": "0d09be4983026b7ffc5ec8c5fae8cc9ac6d642fd", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.45789143.tgz", "fileCount": 2, "integrity": "sha512-NUfjKAVIvROeUGrwqdMpAC/0cbupg5HFpoSbD/wBUo1xhP8XBlFB2FlpFbPCrUfNuuopF3BZarT9ezdr5SBa9A==", "signatures": [{"sig": "MEQCIEfZl9tGwxRCCWFg60ARhMEWtTrz4Ltjp8evM+CS61AVAiAX4W2tnjGRBQ8LRetp+DREe5LZuq/j20hOReb+WNs26w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 437, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjhgkxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr3Gw//V6r5rHbADy1Jba5s6A3Ygw0NmwAA4l2vqLO8+bmHCZV9xyX1\r\nXLNerFvnXRcRF3WJF6CWtz1ymjuEtGMviKbSr0LWn4+ALwTBhMDi3YBPyhFA\r\nc6GExburfBKrN8QEYRwXm4hX0BuVh5osUg0Kbg0ZPHQiANkExrIABG2F8fhC\r\nI5nf+uG1NTApBGT3pS16/wfIJ70LQPH9/kuLTo4itr5arjSfhGcZNSk3xX1w\r\n5619yNa+Esx1L0E3SDyHxdOydoQja9ToHQZcVup3kGA72uQH/Y/lmWSBpIFr\r\n/FEZwnzzhuqgJFwWOH/Gd+V00ce5uGcRNoWjt2w4YdnMtYia/ZO2GTH30Hiv\r\nr3zuoOFzCGjFl/svj3ax8hWP69btCctrIbtGMHVDboXR/obbu3lvaxlui4iS\r\nDCGyIN3NBtFANiiamX6DzLpfKilmcIgq2aO3qqs/4bRH956ZOWLTdzrOHY2E\r\n7BFC2NTZpDXeK1nQyArxjJla87pjUNVPg19hLVhwjjNznzjszcISFHuNtG2R\r\n4Ix6UKrfZ/aIGGWaeIr5Dkqgx/EMfbqcqBP3cMQkMrYNH94OVI4nhA2PIIQh\r\n6t2cjHw33Ys1rTilGCdnarlLIAwegUdahvW7gHJMrKp2pWUxPEdPn9Dp/yHn\r\nMM7XRX+dz1ey98TkjN8+Mm9GZfmbhsC1zfA=\r\n=O/im\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.fbb28312": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.fbb28312", "dist": {"shasum": "e80fa2c0c01dda4c3171250886299c69d4c0df2f", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.fbb28312.tgz", "fileCount": 2, "integrity": "sha512-v3TF3SICz85vegRwMaqeW4SM9POXBDOktdibdLDV3308idYumjHIJDZna9YJYhk7NLpnkgu27tLoxbXvQ3QCoQ==", "signatures": [{"sig": "MEQCIC2PUVlSJsBqDPaS1EMnOd1Sqt9t916WwgrcBqxu3fSoAiBToPVP4aV+N29XX7GARRB/3paH9Ql6t485hrQFFABWjg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 437, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjhkERACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqBUg//UYzV2zGgn8w9KACh9JBfnTzuuI3QbmtXkmexukXAVNwAMr5U\r\nVRizFChOa29RHMUMYGKp+C5CWQUm8Ko/J5og2neWeyPyo1FzQN3LXk3ihUEr\r\nlIzJMOwjh6zTSyKAS21YhX3Po2pdAfw2cK7FvRx86y+wWF9/t16iHtLJOgbd\r\nEinGSfZzulEUIAuLLrwvuZRF7Y5h9kUc9Bxt7Mn/f4ZmoNhsI+EUFrOIwfCp\r\nUKgIkWIEm8dv1m8A+rGgWHOzX4vbkVm18TGMt+bUcxELNHws71QDRX4+6857\r\nATOf+rG/YHaST8+CzPijVofowtc2Jim9beYyOS+MCyzUDiAr6ZOw5NFz8X8Y\r\njRK8a212YBAKvST9Ch7xYiw5C2yaKHS+j1+rKeZUf0wd/Q3biU/RdthJxg0l\r\niTWvpNr8rBKNHMkpwkdPUA1E4cSdafMiLhQJ9YTfaM3FFT1Md6ZBYmGk79aH\r\nI14Q1Wkd1I9O6aK8DExIWOBOLnppYSWxfSdlxgCgtKQwEIG8GJrkOLER9O5n\r\nCl3WBZkUbwS2U3MakjrJpgqgdcy2pGV3KHKKnRcaE6GWAhJFGbmLBXAH3GDU\r\nfFgqYbvHS/a6rqhYsInNMweCT4axKyhLTki14j2jYlPvMm2uNtGspklTtRhh\r\njkDzl1ftPRGSSxP0pCjfQ1vGcyRrnCDdMWY=\r\n=rFbz\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "4.0.0-alpha.31": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "4.0.0-alpha.31", "dist": {"shasum": "cae8be4380fc32f9990a0f55e173d6fc4cd7b217", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-4.0.0-alpha.31.tgz", "fileCount": 3, "integrity": "sha512-6oX/ljyyKjPnSBYiuKyeCSlFfZiCecDD3E6ZpUCpSvgg+foSsyA7kqSEtGbjHA+v5dyuFLRWI8O8Ju4DjYUEQA==", "signatures": [{"sig": "MEUCIQDOmafp9s8vVIK9h/sAJPqGnmy5wR5+WbTuiS/HqCXfrgIgcfBH+6PoCL0pLj11j7CLbjAi/pst1OM2vp5soaUcLF4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@4.0.0-alpha.31", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1729}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "4.0.0-alpha.32": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "4.0.0-alpha.32", "dist": {"shasum": "1208e8645957e7f4ab13e1120c999e467156dba6", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-4.0.0-alpha.32.tgz", "fileCount": 3, "integrity": "sha512-+9pdfMNhE5cNiFuQgM8Q4A5N2MvCA6Mo9lF4jNC654HjawVgg09Lig93mxyvl0qz1sWFqtU7dddhH0mHLzj0mQ==", "signatures": [{"sig": "MEUCIBC0UgcEwjTwiVWAzw0fgcpo4nfHNdqa9bs13ALFokkxAiEAjhbNq2dtVHtUk8hYXhoj4e1fWI+RGxloup897lQou3k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@4.0.0-alpha.32", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1729}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "4.0.0-alpha.33": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "4.0.0-alpha.33", "dist": {"shasum": "5d64bc166e7e911a13e990901fc87e8e253dd412", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-4.0.0-alpha.33.tgz", "fileCount": 3, "integrity": "sha512-ZmFedijQ4k3CvWwwMExJimK/pdeNCSXHn8BCZdbBcMCbobnW1+Id3kXNFxE3fUozfm1YZhHj9Uq6fDpw337a0w==", "signatures": [{"sig": "MEUCIHwT3aHtFmL3t+kQFF1zAkmWLUrZUc8cBbZPOrHgEDBUAiEAi+nkw1+W37zWbqvbyIxDAoFuQMjHfIzCZcq86p6pqe4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@4.0.0-alpha.33", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1729}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "4.0.0-alpha.34": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "4.0.0-alpha.34", "dist": {"shasum": "3c1cfbff0032fe635751e8e4925fdf7a2faf7b13", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-4.0.0-alpha.34.tgz", "fileCount": 3, "integrity": "sha512-rwuBrvlWgY0/VZzyiV/HRZdHEorrqf/BY2R+LeS/meo4oW2eHNnjl8+GUN6M1XMWyYiGsoEyokxE1BpQQb5g+A==", "signatures": [{"sig": "MEQCICPK+IO+qpw46/U1jnTX5BxCZ58s+H6ZjpIJHz78zuHwAiAjCRcmEhZWMtW6Sug+TDy6QuAFVkyPioY91nkYUz2CVQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@4.0.0-alpha.34", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1729}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "4.0.0-alpha.35": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "4.0.0-alpha.35", "dist": {"shasum": "e6cb406cfc1ada9d0f973f0b4e31613f94866e39", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-4.0.0-alpha.35.tgz", "fileCount": 3, "integrity": "sha512-H1r7XAbLgo1ShE35N5c4zSlctjwgWnPOuSdKGLYNIdPTRzy/467oSFrkH6j+9jfDwrUP7wKyeTKyhEvonGIhcA==", "signatures": [{"sig": "MEYCIQDsmURy7aZSvoOl5mqShvnNs3p1VbPDEAiqYLE0gloHYwIhAPjA+d/yRHAUFN76F8p96XCVw/4hebqm7vCA7h50CFG9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@4.0.0-alpha.35", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1729}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "4.0.0-alpha.36": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "4.0.0-alpha.36", "dist": {"shasum": "42fbf0e1eee82826ff9ebb58ae0534d984e50fbe", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-4.0.0-alpha.36.tgz", "fileCount": 3, "integrity": "sha512-5nZYJUu2mhY1guBABAHjWHsYQHn+XjFlDYC3JOU53FKrT8jhbFcePWvrBDPf85ykzGsabbpphCMKrfGR5A30Gg==", "signatures": [{"sig": "MEUCIQC5FQJ6cVZIfdQFmLRoZtNPtpp9Po1+I3B7806/MtZuggIgc3OFdT4AsQvNhdotfkSNqewFS8Vwmw7wjHEoLH7q7Uw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@4.0.0-alpha.36", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1729}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "4.0.0-beta.1": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "4.0.0-beta.1", "dist": {"shasum": "aba1c5ebdfb2482e468cb08798ab28a3c55f9788", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-4.0.0-beta.1.tgz", "fileCount": 3, "integrity": "sha512-OdcL9PqV2vkk+QWnERL/8IIy+3IbNqYOCFnZhNMeYSO361L+nfDOjR/E7RS0UaZVWrRjWXohBG1oCH9upg5lBg==", "signatures": [{"sig": "MEQCIAJHeKmo7UtIIkkVo1P5vdlXAYeN9DB6MMDiCWqoFYdAAiBNunYeaFNtfj2PzX9f/a6Y1WpZwxEnbOTOj0g0b0Wy+A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@4.0.0-beta.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1727}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "4.0.0-beta.2": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "4.0.0-beta.2", "dist": {"shasum": "5459e4d59ce1e295d02f6a5d121d25f3044af3e4", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-4.0.0-beta.2.tgz", "fileCount": 3, "integrity": "sha512-fCvsXiFKHg304wHprQhpX0O3QT8McC7pyXnc9x2MDs0l0/mG+8ibMzguAbObnRP5GZ5JbbVJ2sQe6JnMyxWWQw==", "signatures": [{"sig": "MEQCIDeKDDeF0XfMrXAAvtJtyHCAGgM83oOyihwDk2ZkscjDAiBfGCCXqsENvILq6pZUdRBN+el664ZNhmx/0+vZoQGvew==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@4.0.0-beta.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1727}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "4.0.0-beta.3": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "4.0.0-beta.3", "dist": {"shasum": "8863ec54171474737ff0c4a46466b4c086623e39", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-4.0.0-beta.3.tgz", "fileCount": 4, "integrity": "sha512-AEHX9depJFa3GCxmV9s+abklPPo0OmL2uNpPBCumk6N4GLUpmXNpkDlEYeSgggHPRwoSyfvbs957Ahvf5E4v4w==", "signatures": [{"sig": "MEYCIQCVWJDMNbSN77R8jXFdkkyE9QIvlC8vDgRRDIKGRvul2AIhAMQbzuYJL4kfEhuvPBdiBHMJAuLbM7MoRdl3jUTQHXOh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@4.0.0-beta.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1865407}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "4.0.0-beta.4": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "4.0.0-beta.4", "dist": {"shasum": "59ca946d0e146c459f339c072da94866ed16247a", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-4.0.0-beta.4.tgz", "fileCount": 4, "integrity": "sha512-MYfGNg1nikym/F2aCx0ni5ZWKsChVwWTsjgmDo9ZOiqhWE/7yeX0Ej+lugEh4gPHyUpcMbeTZySZx0OJ8o+X1Q==", "signatures": [{"sig": "MEUCIQDX+3g0slT/PMQCEiz0CmFvJkaeo3bEbaQwoS7aaBGfLgIgbGu2wxz18xjvHGW1MUOj41WxfXoi4F+emjROLVfEtBo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@4.0.0-beta.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1865407}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "4.0.0-beta.5": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "4.0.0-beta.5", "dist": {"shasum": "9c8ab30ef6c967834650840bec3aa2f95dd3e5f4", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-4.0.0-beta.5.tgz", "fileCount": 4, "integrity": "sha512-Mstca4eNIMwMr5JvjYaE7C/MmGyQeYVxYRMGCNGbW9UDzxRNNVrg9Z4YgXZ2NdnjyhQF7N6BysPMhfQjv0Czng==", "signatures": [{"sig": "MEYCIQDZkzydmVSKcW49VahUDdNEsUfpgQxEOWL+JO/xlk3dQgIhAKtTU0rHO8ln2xXwoNr6qnCOCxyoDRc0/Yrua2Z9mwBu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@4.0.0-beta.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1946303}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "4.0.0-beta.6": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "4.0.0-beta.6", "dist": {"shasum": "1962a75d8fda444f6c3269643b622c20a1d11a3d", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-4.0.0-beta.6.tgz", "fileCount": 4, "integrity": "sha512-DUs1A5rVK1Da7XvY2FzVgS+5vinwVDOeXgOsL6dblL8ag70wlwzZjyB3YRbxKRekPynwKdILgIv8/TYE3cv7AQ==", "signatures": [{"sig": "MEQCIARzzuIggVWEusSdmCBcscLnvbkpMhQKl4E6xR1Ai7/wAiAXF6SdCYrubsNA+OT9cfho32LLhDNA5SRmkKOjFw5yRw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@4.0.0-beta.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1946303}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "4.0.0-beta.7": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "4.0.0-beta.7", "dist": {"shasum": "1c020743419f26f22be3049a1e17cbf6e6867f41", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-4.0.0-beta.7.tgz", "fileCount": 4, "integrity": "sha512-JD3Gy2KrDa76bZl/qMZa4jIz+HDrwipjrlpYai58aMNBlCbzDVuYvAx5D79OwjHNPU/6yFFPNxJ7R1Ca0UILOw==", "signatures": [{"sig": "MEUCIE2yR7MxHH5ujPVtgHrjxO9M2dxyrTxJsm9P4F7hQfCPAiEApZN/7SmCgD021D48onmtELyf4z8mnucWOGVgqKDS0cI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@4.0.0-beta.7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1946303}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "4.0.0-beta.8": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "4.0.0-beta.8", "dist": {"shasum": "e30eec39c821f15ed9c0638b63e631b1bc61252b", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-4.0.0-beta.8.tgz", "fileCount": 4, "integrity": "sha512-+FQFS2XjsHGlh+U/paIcUULLfkSmcBp9QzXkTu8UsEH6Ygp7L8RmMZshAr5dQDjXFKBvKHKJX4oIg/SP+VThgA==", "signatures": [{"sig": "MEUCIBTW2SO74kcwgwjUd+vgVeHkaOu7SNOFp3/VdUTakE1iAiEA5N9IEFiM4kboWaxuKx6Jq3qBhKyDkOJokXckfrz4fIk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@4.0.0-beta.8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1946303}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "4.0.0-beta.9": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "4.0.0-beta.9", "dist": {"shasum": "724b9424655b7d6a9f081ec11ca0fa2b1581d5ea", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-4.0.0-beta.9.tgz", "fileCount": 4, "integrity": "sha512-FCpprAxJqDT27C2OaJTAR06+BsmHS2gW7Wu0lC9E6DwiizYP0YjSVFeYvnkluE5O2J4uVR3X2GAaqxbtG4z9Ug==", "signatures": [{"sig": "MEUCIQDlva3I2x2OSfW+j0J2883Ui7kjpOu9rEFLo2U/wCv7/AIgaBN45j3X5pYYjGZqxGsDhmzdANfVjyghl0QcX7CIqqM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@4.0.0-beta.9", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1946303}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "4.0.0-beta.10": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "4.0.0-beta.10", "dist": {"shasum": "7fe559e823f7d993e7d019b6ea7e012c31bc21df", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-4.0.0-beta.10.tgz", "fileCount": 4, "integrity": "sha512-Gq4bQm1QsaMIX95/zkpnI3J+8CMAgZSWLIYeAq2ehom9AG/Qe9AaT7dDzIzI68uovqwScnKWiJJLo1SfHveoRA==", "signatures": [{"sig": "MEUCIAz2H8FJtftXqyTjMH22yo1mOBLPebPfXHbFEcJVK2r6AiEAw81sEjeeHzsxwTSBttDWrodMI69nfQvCelWRjLXGxVY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@4.0.0-beta.10", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1946304}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "4.0.0": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "4.0.0", "dist": {"shasum": "c10510769387fbe4aef27e336c89f9933772584a", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-4.0.0.tgz", "fileCount": 4, "integrity": "sha512-qEcgTIPcWY5ZE7f6VxQ/JPrSFMcehzVIlZj7sGE3mVd5YWreAT+Fl1vSP8q2pjnWXn0avZG3Iw7a2hJQAm+fTQ==", "signatures": [{"sig": "MEUCIDzQLruxntKhCAvPB4jKmr1plr1zVPgUzLCYoYOnflB/AiEAmYYjOb63XEySyzqNbz2OZtzrfRhEB6nEfzY4D7DmYD0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@4.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1946296}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.515a9bd": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.515a9bd", "dist": {"shasum": "92eed79c3f640b6645924529d44c1394cee25b9f", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.515a9bd.tgz", "fileCount": 4, "integrity": "sha512-w1WYfrrEvClyU54lnk+pAuOodm6hx6vW+tHHBfm6YulnIxIytMZVK5Hh68pqMO8me5PAUinpqvqVjbxZCPvBrQ==", "signatures": [{"sig": "MEQCIFYgWXYN8+4U0RT61naRHdj0fSAhadCGeHtrfd9a7k5eAiBLioXkMumE6SnUkYArf+7qJ3tGwOt0RBI4q2tbiTPpNA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.515a9bd", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1946313}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.28008f1": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.28008f1", "dist": {"shasum": "8a47824b76df7325e336205b01783a3afd6f1d58", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.28008f1.tgz", "fileCount": 4, "integrity": "sha512-03iuM5mYxDdD48CCTgiZjb/YgiVip91TgR2/Q/giBRrvUTnkQdUIYk3OT6VUAuGaRnk8pZU1KJReIq14jQbQew==", "signatures": [{"sig": "MEUCIQCC8FXczIFHWeU2tmEhcpiQmfumwq/KeQirGW2lbjGvewIgbz5Cbv6tPhQZ/Lh5AfajnYGW2fgeDNQ+Krny5lWC4c4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.28008f1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1946313}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.e1c084c": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.e1c084c", "dist": {"shasum": "ff6b1750da9ac4a577d317af818e45a7c1299910", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.e1c084c.tgz", "fileCount": 4, "integrity": "sha512-tAosXxjl7gFXFIbZkeaoGGK3yB4I3m1tzQQcZztURN8AY3VxXhDWr+iYlxesWdEFrpqC9qZG53JYjXv1ZXF07A==", "signatures": [{"sig": "MEUCIF3t+0XEGVI2G3suyKaw7qYeAb96kDBrpzL4hvMFzAGJAiEAqY7A9A4MUggU9YSmhTOl9k3DoIl34kwHAkTyV2FhU3Y=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.e1c084c", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1946313}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.d66d7ab": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.d66d7ab", "dist": {"shasum": "b79b1c56783c644c4b4ddd93f6e0d110b4e5460e", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.d66d7ab.tgz", "fileCount": 4, "integrity": "sha512-UKNEJH0yg8jItpOhSz7PzvxNHAQasdzsk0L1wot9Pu5gCmaevO+FKqMRQmamyOWsDOk2ROEo1Mm7Yw1WFsGiQg==", "signatures": [{"sig": "MEUCIQC4dLrWJcoVBK65jiOeEf/dHeaQ8ad/9rGd9aFxiQd3wgIgBtEYUKjEiNK/SQivE49aeaguIDS3rCo8A4bIje1oHLU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.d66d7ab", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1946313}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.82ddc24": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.82ddc24", "dist": {"shasum": "e40a02660e91ae7f3d3ae19d6cf012e146cd2fe1", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.82ddc24.tgz", "fileCount": 4, "integrity": "sha512-7lhvzlW+Ie40u49L4Zs/1gUIlHzZ+nQdvIU2ICTI7Lw8PsAAMu3R8KNn0pgiKTTCOJfB12uGXvZ3ICGW270q3w==", "signatures": [{"sig": "MEUCIQD5BsTvuliFdaK4tUDTOq5Gj/nVITVSqy4ZTr2YF8ZEfQIgdhMFbn7lr9MKvU4yRwICqecYX2/2qCfou/f1HIVUOzY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.82ddc24", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1946313}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.b009afa": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.b009afa", "dist": {"shasum": "4a27b5426d70ef5490583ab5b1471d7654191842", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.b009afa.tgz", "fileCount": 4, "integrity": "sha512-htBM+MCYZG0EbzCqHChcZcf/EOJToRTiELaAv78nzpujC+sGqRvMump5RvfeoyECP115+WCs2D+XjPQbvod9BA==", "signatures": [{"sig": "MEUCIQCKylYYxTT4jSijtPgY2jgVabcu42R/9RoudBtxdUFIGwIgQLRWwzCMhI4XXfdFXHLN+E0uAoQ6MMGADm5zHZqpTm8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.b009afa", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1946313}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.86264a9": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.86264a9", "dist": {"shasum": "3c313bd7ce9692052fc19b56b1cd1eaa40f56ac4", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.86264a9.tgz", "fileCount": 4, "integrity": "sha512-Viw8VkGwfS1cZ5IWa2RMZT4bx3fjKFXVaQUh88Frv2VOVY8/acTlapLfES6oH4UgdZPiyOsjX6e3DeDgbpAd2A==", "signatures": [{"sig": "MEUCIQCkjL5Zas0Xs+traxEw1/ihCXwPet5fG74FJzMYk+DRzQIgNxtKZdMAuFC5IlKpeJ5G47HPvAKcurwXWuHF9lEd4UA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.86264a9", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1946313}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.e02a29f": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.e02a29f", "dist": {"shasum": "3aba096120994fb6b9dad6e168ef154f1a1fe1ae", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.e02a29f.tgz", "fileCount": 4, "integrity": "sha512-qvfsZtnl2R1+UhbQi6bU4RAVms7j1s+k57LTtQH8k4lR6VaoqA6JQA60djvENbLDTbc/SJLRGaMPSPCyT0eQ3A==", "signatures": [{"sig": "MEUCIQC0qPK9teAx2Rx23Lstp1UB7jMwk8yz6xKNpXiXBVb/XAIgDQdGde7FOtFvflGlPpjXl6q9arp8OrzWajuH6XA3B/o=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.e02a29f", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1948873}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.b492187": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.b492187", "dist": {"shasum": "a3c5c1be907f6c44da7f1fbbcfcb2097c079f38f", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.b492187.tgz", "fileCount": 4, "integrity": "sha512-jQdo5iXYvQzIn/StTt4M1KPk41Bmn4aHnx1d2ngqjcRugt2ugxK26rT+48jsR/aLOzLWGFys3lH1qFxy9yX6zQ==", "signatures": [{"sig": "MEUCIDjQYMgpSA024ONdb/ju1wWERaQxFGwcy8EvIdxyeQh0AiEAkoLByekK7EGwjmqD3ugy83vnq97Z2Z52tkfpJRDGRi0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.b492187", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.2d3c196": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.2d3c196", "dist": {"shasum": "ad7c64500092f9c2f4ab15446dc57ceb7a5096e9", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.2d3c196.tgz", "fileCount": 4, "integrity": "sha512-iyncK+A09Rj6TMqsHwrb8Sy/oBQDAEBfFpNPL1pfUfJn9YwjIAToDJSlntZpSzHyvivlgy0cIgsISFJHWcaxnQ==", "signatures": [{"sig": "MEUCIQCIWci5mGSqm4T/eGPjGnQa8gtLqCS4kzjvc0R3PYL/SAIgVwXDMErc91FCy1/am8qm5S7+lwBnnG8J5qOcHAlEI00=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.2d3c196", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.f237f59": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.f237f59", "dist": {"shasum": "db612bd527e2bb71a4e7fdc2e7e88cb192ec97fe", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.f237f59.tgz", "fileCount": 4, "integrity": "sha512-aUS3rOgJPhyVJywrztsUbUn4+yuTyMQWVW9LFN04H4rjtVIs8m5j6mGBzCBc17Wdz3tPtwDQOboSLKtTp/EKBw==", "signatures": [{"sig": "MEUCIBwyEhSh13Y1sVr6Vr+7dtJd9ItXUOXWrTvWXOeTPy1MAiEAiBU47d3wkTGnY2vW5tKUUxnwiLsXj0z4mAntVc8fCe0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.f237f59", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.9bfeb33": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.9bfeb33", "dist": {"shasum": "6a0d23e33bb9a88270d80cf44b9c096879cb0309", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.9bfeb33.tgz", "fileCount": 4, "integrity": "sha512-KuCGDmtmEHygoU/WchJ7M1Ze6HaFYPu3PlhNX0YF4Ro4WepyWL/1U5y93NeyqkxFt6krQXMHHHz24O5M7IgB9A==", "signatures": [{"sig": "MEUCIQDmbGPjIQSf5MKMD3TZecTx2kGwPWXQgTaD2WVapzeZkgIgYm5pVtIOhC20twxKrqWwVj7H8VZ0Jmt+kQC3m9FzZRI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.9bfeb33", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.125c089": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.125c089", "dist": {"shasum": "ae941bb1f5fded37e861349cf34e9b875556239d", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.125c089.tgz", "fileCount": 4, "integrity": "sha512-X5NR6DoYZpNRRnLLI3at0IiZzsbjQP94+0ZmkmbK+SnWuAsZ4tZ8W69uK8Gv6JXGIWsejbq4VCiM752gQYu6HA==", "signatures": [{"sig": "MEUCIQDo62Ah0wXzyYMaDu78Flcl44bFWqJAkm6YvkcsAo8XWgIgWhYD9IwOYB+9Tc6GPPoLRH3WWuvG6YupG/FSPYFNWcc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.125c089", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.3da9d61": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.3da9d61", "dist": {"shasum": "06a6fd7770da31354d1e015de0324f711b913874", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.3da9d61.tgz", "fileCount": 4, "integrity": "sha512-hc1Xf+DuE2I6xRPLM13R2i1h8yRs6VoOsEwnmP9XB1DHQ86loG3cC7BCsRH5ksj017jLGJLCaI3Gj3xENQ1qBg==", "signatures": [{"sig": "MEQCIBnrM2IwTQC4VQIcbZyEMZMjGrCTC88eDi4N2c42XREdAiBFyF+9JDqYdth6xSf8tz+5rYUHpnbR4xsr8QaALWeEjA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.3da9d61", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.965048c": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.965048c", "dist": {"shasum": "f326c07d45152e0839cfbc9eb672f4e648a7d233", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.965048c.tgz", "fileCount": 4, "integrity": "sha512-e+vxFzeM67v4sVftwTpsNlicX5GLcUDAkAiUMhZgQll3OwHKbIwhXiAas4/GSRT/4nw8kOhG7nfhD6No72D22Q==", "signatures": [{"sig": "MEUCIQDlx0WUiq3TQbre6X9/iNl5S8+KVufcWGw2T/oAfW1QdQIgIDC4qrdzxB5gwhxFMJCl451k2myTbsNV+0Ra3Yl1YIo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.965048c", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.924dd69": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.924dd69", "dist": {"shasum": "d38fd48e5135f1a5ee6cec54023bd39166f14987", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.924dd69.tgz", "fileCount": 4, "integrity": "sha512-cSHQA43qmFHF7JX+3tm0AY9haUMMJQfL6ZDHdqMN1ia7Glb9EzDV5nPFC0Z+NEqhvX99B+IeL3QBxL7f5c6aag==", "signatures": [{"sig": "MEYCIQDdJ4Iff0B76+B/CQXWWHCRNWqCfbB0BLUlULVIJoVRGQIhANoMCqsszTA2AqjWOOtK9QR4Yainf17BIrLrR9meOqi+", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.924dd69", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.9fef2bd": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.9fef2bd", "dist": {"shasum": "2d68aa905b1c127d150962157effe13d1f7af4a6", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.9fef2bd.tgz", "fileCount": 4, "integrity": "sha512-NZmcB9Z9ob2HltWFUX2tuAGHFqyrqTP/5lk4UH0mV3AzHbgmXtmnKElS2QSr0vO7quZbgSlrBvXleoIeFFgwVw==", "signatures": [{"sig": "MEUCIQDa7DjKUw5Atg07DoEMYGmnY6R/FwTg1YCZoLWqj+mMMwIgdsHN/SuA/lZQ0uy5V3lBRLy7GaNTGfrK70/fxpAS8qU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.9fef2bd", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.a4761ea": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.a4761ea", "dist": {"shasum": "2f225e70b87de6d1d4d972c2af00217f3e01cd58", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.a4761ea.tgz", "fileCount": 4, "integrity": "sha512-LHPs61hVHOaSnUzIIZVwqPL+Sj9VryhPnBHQRr6FPF9ySnwrgwYpKhGIyICUHpG5yZK0Sfx/6IVM9srSGcRjjg==", "signatures": [{"sig": "MEQCIAkPOQlzYTFklmU6AscIMj25KEtE+GkbBJ52LfdUMMjHAiBLVS5d5bdR2YGdQMjbLXQnJv84k9TXC/7e/yfvhU3OUw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.a4761ea", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.655209": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.655209", "dist": {"shasum": "bd19e6645fdb3185da406993b7885edb68742017", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.655209.tgz", "fileCount": 4, "integrity": "sha512-s3u+yKCaNe3nMLzPuaQA69S0RV9xhF1D7VNRQvxO8e8qTieLo7xosZq43imtzRG4bdZ3UUNDhJ+alVMV0i91tQ==", "signatures": [{"sig": "MEUCIQD5EHm+84r80y4wdxomhGdG8dwxqc3bQZQAySCvIEN1ngIgf+Wte2uvhr90E5vRKp3hVqV+tJy4BeKXF3b244WBsFs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.655209", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "4.0.1": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "4.0.1", "dist": {"shasum": "c52f624c5e339e8b2e4923a3ffb5e0d0872520e6", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-4.0.1.tgz", "fileCount": 4, "integrity": "sha512-BUNL2isUZ2yWnbplPddggJpZxsqGHPZ1RJAYpu63W4znUnKCzI4m/jiy0WpyYqqOKL9jDM5q0QdsQ9mc3aw5YQ==", "signatures": [{"sig": "MEYCIQDQzUFWulAlFtpDllrAFkXYbbqaIx2D287bcgUga7Rd3QIhAJMqKZ9HPv5XTXHtZ3fWPvLFepOUIFPibxauw8rL67n1", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@4.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950904}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.ea24995": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.ea24995", "dist": {"shasum": "df1643274221dd746c234797a8421d01c575ba3c", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.ea24995.tgz", "fileCount": 4, "integrity": "sha512-uE2adQLAnjGDa3JFUJVyM4tkYe3swsQbnpSlA+yuXLaX7Grneyy7Bw6dPTHbcSTuH9mTdSFH8750b+ishskEGw==", "signatures": [{"sig": "MEUCIQDCNytEh1bguulxpeUH30V4/QJjyqtAaWhPAzdXmnchCgIgFTJ9rLodu05GVA4qhy0xyK5b2WXyK1FGWUbiV6l5j9Y=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.ea24995", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.0d5e2be": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.0d5e2be", "dist": {"shasum": "f39ae748679eed6d893c22a01e113ccd260c0c90", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.0d5e2be.tgz", "fileCount": 4, "integrity": "sha512-8hGp7QDqqryYI3VzutFDSsYFSSNirFE6GaSS2htKBoMrVKGybS5IbyJfTzYjomxguvqJ6d/zI8tI+GOEcpzPRw==", "signatures": [{"sig": "MEQCIA24pB+ngWg7BRKV1pHEC2g7s58GUT9KrmgsiNEthhp/AiBqIQMJ77UkDSxkBQPvFQtmAPHn+bxVENcIUR9FTUjDlQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.0d5e2be", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.2242941": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.2242941", "dist": {"shasum": "fac63ef7a9f6a95a5840365fad584cdf88f986f8", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.2242941.tgz", "fileCount": 4, "integrity": "sha512-0kizmQNizsxTO0cK57qmf90REEmyNTQNuhooorFky/8uFE1Q6qWGKBMzoJuuPEhXtqX3NNB6ZZWzjUjOB0BNYw==", "signatures": [{"sig": "MEQCIE4lGJKFoW92TkawS+HMxjXnI7GyTY/CxEdziwtEM2BjAiAjh1AS1pQr+ce7ho7VZNeROh2RdquvMj4KSd3R7gv9KA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.2242941", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.c09bb5e": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.c09bb5e", "dist": {"shasum": "2a37ddd7785970373cbee4f2c64f51f46ad30d73", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.c09bb5e.tgz", "fileCount": 4, "integrity": "sha512-ZVBW7iOnjf/6/WpCS7Fp257u4dwzIiTpo+ArmdUv78kCCT66u1WTCET3wPJbp17L2/uFrOXnA3FpSojbbJXwDA==", "signatures": [{"sig": "MEUCIQCRME4ZZyMMex6C8i6aBNHyevqqcjNlO0NfPp38ZQjaxAIgKfypx0jwNejGETWy8KCnskpgToZomYjp6QmUiL4U2mo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.c09bb5e", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.0589d7d": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.0589d7d", "dist": {"shasum": "69a978dd14d140790515f1bf06f61e1d6366d2a8", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.0589d7d.tgz", "fileCount": 4, "integrity": "sha512-XYk3FUBx9Z/ImPyME6KMNm0v2ayW3HbCEoqpia/GSYBwtUzpH09bV+Zi9JngxRYbrh05NViveEMU9UtFn5cOiQ==", "signatures": [{"sig": "MEUCIQCuauQO1bXZkuysyd71C9FdhummGyVPtym+HqsRtVTmVAIgIhGkXKDSrnjXOEKnAN2IiRq0rEFhJGBuDdDYAcog4aA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.0589d7d", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.d85e9cf": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.d85e9cf", "dist": {"shasum": "324a2a6c6ce50c8356cbf9979cae568ba6b5f9e7", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.d85e9cf.tgz", "fileCount": 4, "integrity": "sha512-EDOZC3vAMEFHIqCjaJNIIr4D+1E/jllI/O00fzlV4esPUzjTLw0jPUUgUvOidHV+0bjXP/1xTZo7vyTvLNdfLA==", "signatures": [{"sig": "MEUCIFpao8snuJFyTQGj51W4QmgFlcQ0KiWRUC8dwEzNpcQiAiEAkMUNJML7vkVNr7bPAZYuyZXMLoV/vAOt2LWx7AO+BTw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.d85e9cf", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.88c8906": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.88c8906", "dist": {"shasum": "23da867c61817bcccd05f38c4bdef9d963f7aaaa", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.88c8906.tgz", "fileCount": 4, "integrity": "sha512-03x0h6Kme/3bTD2s2HD0M+wcuW5uH3qVQZ2KHsNhZvsBr3FKwDKdGiWom1CDSoVEKwHDiBHNFoc98B5E8zu9Cg==", "signatures": [{"sig": "MEUCIQCjiQB04X9rhmZ4rw4fo7nVrM4ntFOMUDVLlgSgoUbtGgIgCr98shrxo8+CWLn4O/jr3Y/bA4gsN0S9DEGsdRYtpys=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.88c8906", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.3aa0e49": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.3aa0e49", "dist": {"shasum": "665e872d9a1b8c924db50c6df2c0e79dd3484eb9", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.3aa0e49.tgz", "fileCount": 4, "integrity": "sha512-/FKR5b9HSF0eF4+C8gtgYhjH2CipXt8Oyf6G9WyMchO6keR+jMbwkr6/8WBDoKeoNnOkg9FXMHHIVizqVdkkjw==", "signatures": [{"sig": "MEYCIQCw7ajUt9qKk2LPYM6zzc81ttzk9AMp15+yjEDpK8WfhwIhALXoKvuoPmg3jwOg8zIUdEYfanDs/pQU3yD4/Ld5+99u", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.3aa0e49", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.60e6195": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.60e6195", "dist": {"shasum": "ff703811fb3255e3106b1d0d1bc0d6c84e425c61", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.60e6195.tgz", "fileCount": 4, "integrity": "sha512-snrtogttsVGHKdcJoba1Z3+9jmAz+75nlxalzYRmzJilZqmoCe9l/gGtJ7p1cZsmNevBsbSP8jtA4qXe6K5LFw==", "signatures": [{"sig": "MEUCIEtR5MBmX1z5j/l2dwBWFMfjYWJ2Rcw3/CM/65LkNOSwAiEAtMc79n9X8Pa6kIysnkOiZZn59G+nPVkQrEAC+LcF/Rw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.60e6195", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.deb33a9": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.deb33a9", "dist": {"shasum": "1f6379cbabbb97c46410b7b2992abfaaa3a827e7", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.deb33a9.tgz", "fileCount": 4, "integrity": "sha512-zrA6y0sofP2/+1pydouMgVdMnAjHi1BjYT11ZDzduakY+THNJMwu+B+rSpJWgNvsph0Kt12aClpsHsOaJKMIJA==", "signatures": [{"sig": "MEUCIQDsfIY5VnLBrgWvm6Msfr6/FrkYNj/nWfjf/PPsfy7iegIgVwbHCcmtf9AsP1nlmPMis44bzWFOBdPsiS4t9L7/59Y=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.deb33a9", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.9572202": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.9572202", "dist": {"shasum": "cae4879e5cf03ada74ba7b11232144f3ecfa37c9", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.9572202.tgz", "fileCount": 4, "integrity": "sha512-EzoowTxVdgpFJIn6rtZ1c7RwoM/XY3qNTuN/BOlXkImgFuGl4Oi+TBi/6oUHE6UlCTZr/zUJXibY7FUOMEvbdw==", "signatures": [{"sig": "MEQCIHKL3RZcwzAvKJmHwQCEEITK12cBxnE3zVfcux9h1u9eAiBiKVNSrb/nCO3vVQQbp8xJW6UkLuVDpoYdQ4nk/kZX8g==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.9572202", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.35a5e8c": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.35a5e8c", "dist": {"shasum": "e0af86486add02c987888cb9b16c3294410cd5f7", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.35a5e8c.tgz", "fileCount": 4, "integrity": "sha512-LIgkVjdzpNdqhXp2zPA8M+plbgacwKn+Q7AwNo4Jo5z5rCrmJYtfjf0cdKilePxfgEVghTGTZDoMZRQyKwiPOQ==", "signatures": [{"sig": "MEUCID4RLAdkVF4VjRu+OyR1eS/kEyFhSq33yu6sKXnr8DUGAiEA+SM4SF0CiHs11rE+83nIvGD0T36JJt5wOelIBIC1ico=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.35a5e8c", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.7f1d097": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.7f1d097", "dist": {"shasum": "d1bc1398f37fa62ff2ad53a754a25441edd4138d", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.7f1d097.tgz", "fileCount": 4, "integrity": "sha512-SqWnab3T00cLrZRLH+5BFUrV5fGQsJ5NQyBVsyJlKiwSrQEDrBjIAnjYdGrMZRabqNcmiMz8SxQXm7eWWToW0A==", "signatures": [{"sig": "MEYCIQC0OriXVkslbzGr4lJV5EVNEKJWmo+J8FBbkKxodbiR1AIhAPBvLtSBjbT9Ufosn4IjSsG/7wFP8KqK+MWkoHSXU7Lc", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.7f1d097", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.4052eb2": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.4052eb2", "dist": {"shasum": "75e0323f2a77d5a5e8a0ec977b8e372f2638a219", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.4052eb2.tgz", "fileCount": 4, "integrity": "sha512-k0LFJ/965XdRkCFbuO23oQyO6SCGLNxrbbjL0Ht4mgYYUm5HS2wJTcjRRM/ZNISTnqN0c8IgLsVutKZYAy90Yw==", "signatures": [{"sig": "MEQCIDP8whO1FbOyd488wlEUHaIXQ0tSG2LLXEcL1hzaodiCAiA6Gvpsrxd9uZ7ypmbqI0CzSIqB+RtMWYahxXhIjqZbRw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.4052eb2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.50bafce": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.50bafce", "dist": {"shasum": "87ad12882146afb378845e013781646f1e8af9d3", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.50bafce.tgz", "fileCount": 4, "integrity": "sha512-oa+JzhbeVRXu9BHNKQTSHD/WgjIcod5hgjrYAAQmLMGZAkTsjKieXI5IHKjys8a1PGgFAWe2CXdkXf5O+hy34A==", "signatures": [{"sig": "MEYCIQC1twenRWeUpQ4vj6WB6ekGb6emFwjx8BtaR+D5z6UrHgIhALTZSWI+YwlMTgyb6poqbw993nUbj3vdkoyOkfd08Uty", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.50bafce", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "4.0.2": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "4.0.2", "dist": {"shasum": "7eb86e22de942b608515bb52942cbe634a1907b3", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-4.0.2.tgz", "fileCount": 4, "integrity": "sha512-7SXjWupT8FTeTHVQUrckxXwWWaFsareruW8ZdaLEYoVtA8TjfdUdDVj9xGjfVB6C1lwvC3Ok5fkVLdmfbeMBzw==", "signatures": [{"sig": "MEUCIQDZzqsc0Gzpiwyp0B9PeSkAjiVi3BTDfp5B9TP6JBQpjAIgS6ydxIQexNH++H4HcAM9KUn0nhaxxsmOk23rfbBfidQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@4.0.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950904}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.b7436f8": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.b7436f8", "dist": {"shasum": "33895e56d198aff56e53b207401cd06d5cb40ca2", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.b7436f8.tgz", "fileCount": 4, "integrity": "sha512-EM1HeDx9SUeOuPvbLPL2mrOnyz6piW4C4r6tTiNDH5ooeaSlv9mRvrX1EkKC7luG9oXHjH376SqnwSJSHsQ2Hg==", "signatures": [{"sig": "MEUCIQCh1JxN57uLJC5T8920U/hYBLN8/p9pMBIdCDU+kSHfdAIgJym034n6vuQiMXinfHxLISJcYHQa8dW1yS6pa+IT1ic=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.b7436f8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.b7c3f50": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.b7c3f50", "dist": {"shasum": "481b7f2e94ec6945484dd05ffa1d0a6b07f5d128", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.b7c3f50.tgz", "fileCount": 4, "integrity": "sha512-nLEadfNT73E/Y2L2XN6d2dU0wVncO2UJiUxL/v6v1bf1297DDmSJbC+N4sCqdTrqgaX3UI8tAHUzPi/z8JSiwQ==", "signatures": [{"sig": "MEUCIQCL7h1msmb9bSVKuzxk4SDkFys0nnLJb9mLEFiXLUqzAQIgQ4kjtuHhgi1nxzEmNqoT7B2H0Nb5Hb+Apf4UGAATbTM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.b7c3f50", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "4.0.3": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "4.0.3", "dist": {"shasum": "6acd7aa99103427d7c55be4ba7ec4274cdcaa108", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-4.0.3.tgz", "fileCount": 4, "integrity": "sha512-qHPDMl+UUwsk1RMJMgAXvhraWqUUT+LR/tkXix5RA39UGxtTrHwsLIN1AhNxI5i2RFXAXfmFXDqZCdyQ4dWmAQ==", "signatures": [{"sig": "MEYCIQD5U82lvHQJRdN+Xl8rMuL3BwhoNVwARnhsaWXFeNLdkwIhAIsKYkbZcr9eGujC9cHVYeSlQQqe4c5RnsHkdE4/nAO+", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@4.0.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950904}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.b8d8548": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.b8d8548", "dist": {"shasum": "93929645b157bf570884dcab41b58d279a07186a", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.b8d8548.tgz", "fileCount": 4, "integrity": "sha512-l8FtJc5r6x0Y9KoFSzGh0xML/qIYZXT80UKBW1PE/v0hZM84qyF9ah25mqvhY/sgpwBg0/eGdMHGKhpqNjm96Q==", "signatures": [{"sig": "MEUCIFULKjAqmQiMdpaeK5XL1BOiR2fb8qBZxCR9G+pF26OPAiEAo6CXpAaCd84Bm3buBukPAQuCUXwJArDiMpokx/gSmSI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.b8d8548", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.ac202ff": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.ac202ff", "dist": {"shasum": "59a7c1552b7aa48248a025c4327a76098d8c38e9", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.ac202ff.tgz", "fileCount": 4, "integrity": "sha512-vgMIYhC3c3x9jdma7zsmRUuil1qN6kTmJ8J/TddTWXyOEAyfKanbutGkypoU+lx9+rvb0pgjepZvCnfkg/opgw==", "signatures": [{"sig": "MEUCIBnDIlN7vVbZaHv87NK+/yz/uXQycSqX1e6umD/x5P51AiEAqA9c0e17kcwZGiiz8I+v3r18/j7s3NfYFQvkyps6XDo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.ac202ff", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.06dfa39": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.06dfa39", "dist": {"shasum": "8c23a7b0cf2672899a2a79cf73abdd1657f5ac18", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.06dfa39.tgz", "fileCount": 4, "integrity": "sha512-EoNTINdDNR+C9l97AcfuCQ3787S/9qWMZZi3m5300yF78bW1lkM4Q7zdt/fRvp5Bmkt3MXE4qnmY9adSze2Tow==", "signatures": [{"sig": "MEQCIFi4p60NfSuZ0wz+LLj6/4JmsDEe/I0j2hjzWKLmC7kiAiB2mfYxhnpjP+ZFDGHUZpppaQKT0+ItEEaP9+3qFjbgvQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.06dfa39", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.e1a85ac": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.e1a85ac", "dist": {"shasum": "bb7d7b4e281b596c653218347043457ed60938df", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.e1a85ac.tgz", "fileCount": 4, "integrity": "sha512-UjFGKFfD2Rny39KkWKUxSYyJF8u69MiZdUL6GFBn5/pZP9wQnnEkJlya2TwFg/CrAuD6WVdARy71WGcxASl1Nw==", "signatures": [{"sig": "MEYCIQCKaCuihFiADw98IPxvlhDrq9gU3WXQw2/lkZt1H/zQQAIhAPEBZMPEqf8GBotJQal4Hl4jOko1SI18wiAfcNjnXHdZ", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.e1a85ac", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.9fd6766": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.9fd6766", "dist": {"shasum": "b9675647630ad7795b1795582a0b20aafcd9512d", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.9fd6766.tgz", "fileCount": 4, "integrity": "sha512-Xeu1ylVFfhITFnyz70TOMni+XeZou8cpLxzjb3CLBXUV7vWNaKJYxAwSPEbjoUGNe8Ybl8lVdzU1tTEkAb8TgQ==", "signatures": [{"sig": "MEUCIDu1EPDO3UtW/Ky1c2jKidwW4oOCLSHZaLC7W+XPUaI6AiEA4M7gziqIJVBBQzlD1a/xfXR12oB6gbOggMgYl+q+8DY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.9fd6766", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.5601fb5": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.5601fb5", "dist": {"shasum": "bc8f94ba425d08bd5e327fab4adefa7b1105cd71", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.5601fb5.tgz", "fileCount": 4, "integrity": "sha512-tBbxMCTyvSS0WIcWZFWF4U5TaDNUsiTnEae+3rd5tEHO6knCyGDX/OpgA/Wl3Ucf7lo8ZT+3RRsKMQl4Ofa3WA==", "signatures": [{"sig": "MEYCIQDb7pP9XO1GBohQBCBf7m3O8LWzj3E0P3SlW+Rishsk5wIhAMtLK9JkmbULy0MIrrjzkBlGNb3HZ0oe80/EcBYdyj2u", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.5601fb5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.3b61277": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.3b61277", "dist": {"shasum": "013a6455d798109528d9a99ef7d4da717a234e12", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.3b61277.tgz", "fileCount": 4, "integrity": "sha512-zvpRh50u39M4rgpM3R1HSiMbaa1HqZbeUV5CeioSRjTTYMsRFH2wBY/GdQqTIvzOLjGWp/gvYoxJw+rl4fM3Mw==", "signatures": [{"sig": "MEUCIQCj99pOpmoOi0bBG4/RkrEZlls+PswKg667V/I8nhAZvwIgVbqHoY7ObSXX1mAQaTyvAkspfo2lS9e7AT85qUMi31s=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.3b61277", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.82d486a": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.82d486a", "dist": {"shasum": "25bd04295ecc794a0152661ae9b2df470b275658", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.82d486a.tgz", "fileCount": 4, "integrity": "sha512-TqokGbHqYQiCkPrgO2opXIiUO0AE3I7uWCAhCsomnkbMhP+3rdahfxkocJxGwoVULYl7Bhx3cVp+1VycKg1ouA==", "signatures": [{"sig": "MEUCIQDqCvvRAs6u2/aSgMcOSj8YRzf5+zNWrA2e9PCqPLJsKgIgd0hW0MncEEIitu3qGOsjT4ONduyv0qS0k1GM7f8uHbw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.82d486a", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.3f8e764": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.3f8e764", "dist": {"shasum": "8d356630985fde0c344b268f93f208c3132e662f", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.3f8e764.tgz", "fileCount": 4, "integrity": "sha512-Xwi6yokfr5h/T6ecccr51cHpU/vsXS+AteEQ5i4zGgwgnDkWIOxGZeYVtIfJX1mosoeCioR2ZoOEZHk0kL67qw==", "signatures": [{"sig": "MEUCIAvxH5VpYen5O630RgfIy6xuaLpPsh6xcHzJfE+VAreBAiEA3Hr7fzriznPbU5iB+hAkEK0+0Lcq8OuPqMCoKiIJCiE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.3f8e764", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.0ecc22b": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.0ecc22b", "dist": {"shasum": "5e76efa9fbc2d7faff4a02f2db85869eba7da16e", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.0ecc22b.tgz", "fileCount": 4, "integrity": "sha512-vglu3yxayLKiIL4bAfWjT0LgQNGqi7YdObi1O1/uHzfxOo64q/UrbDYo4MImysIIpNNxdTVF4V2luKAFC1HA+A==", "signatures": [{"sig": "MEQCIHyUbmMp36+ftfftsNxzvt+kvR4MNxPCjET0uFzYRirQAiA6EjXSwDLx27o79EdX3Ip6rNqKRgjX+vEdyNi+RMffBw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.0ecc22b", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.837e240": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.837e240", "dist": {"shasum": "d27833f30f0b9203fb072b30df2e8fdfad4d2a03", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.837e240.tgz", "fileCount": 4, "integrity": "sha512-4o4xaSC/W7TTVJCMBiOLC6jCzgaCPfyfBJ3KXP4Yfx3W5sgPZR54q+I2P7KfyOhuPP01GGRAIbdhu527MEZLPg==", "signatures": [{"sig": "MEYCIQColr9N8nMpVt2qn3djNIuBAP2cZLFvIvRNILnrAbN8sAIhAK2UrwBoIhNf0kDmhrNKussmLxv3woGpxU+w9ReiHnpk", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.837e240", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.d566dbd": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.d566dbd", "dist": {"shasum": "5675bde2f27ad551054edaaf135dcb4a5cc0cbfc", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.d566dbd.tgz", "fileCount": 4, "integrity": "sha512-kTORMgA5Wa2Uv4n2noBgJfa2AeCyi4mRuM0bjj5tShUnSBPmnwUrIOPpf9EpLP+JvLAljIiYWEVoz21iJQxTdQ==", "signatures": [{"sig": "MEQCIGwQI1x8trYg8MH9XXcG9pxylZDJ5SngzXMFMDCpFKwUAiAv79siXHu4FNSsKehowNihBnWGw9IYE+cutJLKtPWRng==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.d566dbd", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.144581d": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.144581d", "dist": {"shasum": "2c27696a102fc52fa15b8b7db1bcc5320a3002a9", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.144581d.tgz", "fileCount": 4, "integrity": "sha512-gglIPXKvnDAhgtWIwFnG+87RMh53fIcEHoSzygnJoOAaNONu6YLh8X69kbV9txx3XJRCVKmKUR0htiJFN3Wbnw==", "signatures": [{"sig": "MEUCIAUaE3Fsw/yCJFA1df9tnt9zM6xs2asZ5m5qGDgCPH6lAiEAx551QSUWQGX7C6g4t5/Citp3xtWT9haMplqgGY7P3PA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.144581d", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.1e949af": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.1e949af", "dist": {"shasum": "94e9e835f9af445dff4962dc3d1daaa84162e152", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.1e949af.tgz", "fileCount": 4, "integrity": "sha512-THNNJiTxcmyePHNFZz3a7BqzMAL72pQFGj/5+hR4c2tSQKLQKRFC0mmcNBcLbBogHlqEFE65xdU38RUIBhux5w==", "signatures": [{"sig": "MEUCIEV4EXMrj/VtJCOIOqvKYQsl95SljRONbyxGn18+feg6AiEAqh4m8NL1EGPzDTZYk3sUZGha7208VRYZCmLXKJZaBIc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.1e949af", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.83fdf37": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.83fdf37", "dist": {"shasum": "52e0164ebe1338d8e829caf6cff2758d317946c9", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.83fdf37.tgz", "fileCount": 4, "integrity": "sha512-Bz89iiWJRs9WpV/X+mpquLF7vzv8itn2HQBJjD8u4gK0hVeLHzlDNhPBxb0HD3mzRrr/2/hfG6IBEz7M5JPrNg==", "signatures": [{"sig": "MEQCIHX9EleYnELuUQ2MkaXerO0NgTXV7lp40gY9XVeeCjdLAiA3a4u+ygzHun+HMx6aib+fUOdtZp2SRXAkrYmJzbh2Dw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.83fdf37", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "4.0.4": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "4.0.4", "dist": {"shasum": "a8e8ca5f9a3a7e376c41f5318e651d46e8be7f53", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-4.0.4.tgz", "fileCount": 4, "integrity": "sha512-flFaaMc77NQbz0Fq73wBs9EH2lX1Oc2Z/3JuxoewpnGHpAGJ/j05tvBNMyTaGrKcHvf/+dk+mCDxb6+PmzGgnQ==", "signatures": [{"sig": "MEYCIQD0SIxpudA/muCGB674IROvZ3aiUwVOBeFpdSunZEgI8AIhAJrUBPTYHE6ta4p5bFpWqSiaukV3RMyb0THXLpKs+Red", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@4.0.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950904}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.25b4278": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.25b4278", "dist": {"shasum": "693534f3deb590f7802baceb960106f39dd4f17e", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.25b4278.tgz", "fileCount": 4, "integrity": "sha512-/f0pxT2TomfSIfBGcs3SxwGHshU0qRoU1mcteDJtWcncotoOeF1d7vbHeqVleXcuYe3BJfUa0elzwWMSHr5ofA==", "signatures": [{"sig": "MEQCIGqbvCbzDW+lINNokbChLVJXuCjUSC9FIUyBFwxdJj8YAiBMPDgcvNpAIblEdQYEPRywBs6io3u87SLm/fRxJZduGg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.25b4278", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.81de67c": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.81de67c", "dist": {"shasum": "7acfd32de0ba37bc006a21289d07060c04001506", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.81de67c.tgz", "fileCount": 4, "integrity": "sha512-mOp15nzjOvyscm9TVsObcO36NsmR2XraTMApGtSYDqJoJK/iJ/IkQQyQKvfHteHFMKNGv50EW98JsXIFFcw5KQ==", "signatures": [{"sig": "MEUCIQDtJakg5ySeski0hNSqLKs+RbU7lYPTzIJRJXIhc3v3iQIgKQIGHJalx3dUm6uLghPVmDv4VNcJAggDUTEEBaKI6os=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.81de67c", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.d684733": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.d684733", "dist": {"shasum": "d748e284be82975c7849283f73716872223f6f80", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.d684733.tgz", "fileCount": 4, "integrity": "sha512-i2e4TL/WkjqSzTZzIHai3n1bLjB5G6VxH+//g7A9FNfenDzRH2+mYsXdduMrHQIXUnY1+2q2ANC4z0TbmD29kA==", "signatures": [{"sig": "MEQCIAlsSr2YMDmst7tWuNinMTeCs0wW5IDPxnQjH1z5cMk7AiBQvVmlI68YQDAKuduCUVCRzE3eh8jaOoA6z6rL9aqYfQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.d684733", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.ad00119": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.ad00119", "dist": {"shasum": "419b3e912bfaef0d10c77db1d55e51922e8b217a", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.ad00119.tgz", "fileCount": 4, "integrity": "sha512-NPoH5J8L5duuljfqY8D04Vh7Je1QklN6znsyGY1i0wpSke3b5R/chqv+XOpGBIOjZvZV9CKa83Df+UusafDSHg==", "signatures": [{"sig": "MEUCID0nRl/82YlEesZlsYYlVAxP3N6P7EZ3BuATAMKloj1nAiEA2JRqGpDOABROcR/+VqbQCAHVyPyj3e/Umhx7O5TNzBk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.ad00119", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "4.0.5": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "4.0.5", "dist": {"shasum": "67487ec8c396a08b56dd5d77b26755d7ec9c0c43", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-4.0.5.tgz", "fileCount": 4, "integrity": "sha512-ho1dJ4o5Q8nAOxdMkbfBu5aSqI+/bzQ0jEeHcXaEdEJzf2fSWs3HY7bIKtE6vQS8c4SmSBvls7IhGPuJxNg+2Q==", "signatures": [{"sig": "MEUCIAlvRGGgPIyYrN9rYFXdmsEYBq4J6Q/UHdF1Nqbh+wzNAiEAyjn0lI+U9C50X32G7xWAmDx4lapJnEYQ56XFJcp1vNI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@4.0.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950904}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.aaf66b0": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.aaf66b0", "dist": {"shasum": "cfd6714944c985a8662079b71c9dab517bb6ae2e", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.aaf66b0.tgz", "fileCount": 4, "integrity": "sha512-EjT7+whX8SZePfKlcyGcgdieqCv1mfZGfQf/Jx1/RHI42cbGiC5DzM8oJDHHv2i7oc1DzdV4fgUToQQPz+jolQ==", "signatures": [{"sig": "MEQCIH2iSLBv3aAKr8Gn8Vk8YpEgKVEy1S7t+XgV7V0k/6UbAiBtAdX35oL3xJeox2mRXQFTyi8lJgi2DvnnowXf8jhbCA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.aaf66b0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.a659159": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.a659159", "dist": {"shasum": "e0c6f871afd3f09273b2a6d397beb179787bc5c2", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.a659159.tgz", "fileCount": 4, "integrity": "sha512-QPrADD4Ed1c+auSCxrNaQXree1AzRos/k/d2uW4yxAEbIzpN7WPXnG0Wi3TKvcdGAjJNnSaHGJGGIspUcOtC/A==", "signatures": [{"sig": "MEUCIQD849B7Oo1EpZB6ix+JIYkjunJD1cnt1CIi5h6DdGKBLQIgHzy9ffCjoqO41Ds9tRHj3grk8/nd32tIUEd2ub12j7E=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.a659159", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.1f84241": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.1f84241", "dist": {"shasum": "ade124a07ee1656c53f532911cdb33d37a261cae", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.1f84241.tgz", "fileCount": 4, "integrity": "sha512-HGHJhNrSRFNsrJWDGGAzx5Fmp5RfZlxoqqaid7pW1cnuDv6wxLrNdpOIcnPIo/zMOdF2NTeaea5NDjAL0DeDOw==", "signatures": [{"sig": "MEUCIFL5HTSDREOj52NkJ59p5lv4UP/g0a+UHvU5mTvMgWqHAiEAkgTeweXV2Hh7HcjH0CC8b91XoSaFOUOSNrIRym0Ti/k=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.1f84241", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.9bbe2e3": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.9bbe2e3", "dist": {"shasum": "5735735ba37b5ccee22ef3a106871a1377965c74", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.9bbe2e3.tgz", "fileCount": 4, "integrity": "sha512-9Fdo4bsfnfgjG5402UOy5OXF/KW1bL9CRPIIE66lDLcEg/3GjkAA3di/zU7dcioldm3Ll1UOi/Vij8WATDCEvw==", "signatures": [{"sig": "MEUCIQCXbY6v3Q7QEQXovTtw1FGROEGtuCf10AWx9LH7ezAsnwIgDlSwOstIl82Y0tU9C3bHTTSPVb53ULxjqaRbs8V+MdE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.9bbe2e3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.d045aaa": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.d045aaa", "dist": {"shasum": "4ac2fa165e15bff0feabe3b6da4a3948234a6436", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.d045aaa.tgz", "fileCount": 4, "integrity": "sha512-lPgReX3VQIKON7YTPSRXWptXcSEUWdxZI1Pk+8OqFgpGbziiplNi6A9DPRBNbK0/N1bYXf/5+hfj6tgYUYUzqQ==", "signatures": [{"sig": "MEQCIBLVjGKlcn1lLbNPz/+tU25SyFbunuZ3BKvyYApQqvLcAiA/LoiwfxS4ObzJ777DnKyZjMzW9MqI1hJHRC1tPrsrbg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.d045aaa", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "4.0.6": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "4.0.6", "dist": {"shasum": "c7e3650b19a24bbebc9d513f188e1d75e1fccf2a", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-4.0.6.tgz", "fileCount": 4, "integrity": "sha512-+oka+dYX8jy9iP00DJ9Y100XsqvbqR5s0yfMZJuPR1H/lDVtDfsZiSix1UFBQ3X1HWxoEEl6iXNJHWd56TocVw==", "signatures": [{"sig": "MEYCIQCMCa6FyL24xf9JzzmV2sPkqG4ZAPUkMicvR5s+bMdVTwIhAM1v+RipgnT/woDNy1w0fUmAscHp7aNOqbWDD2BZ8QZc", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@4.0.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950904}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.272c6df": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.272c6df", "dist": {"shasum": "6928383ff6bc70b8aa4fbc271a5c30782f53f60c", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.272c6df.tgz", "fileCount": 4, "integrity": "sha512-ro7dOf+aXHckr4unVytOM5gl4Y95sPGs55ExO203R6P00JufgdBbtarMJECcm9Ew9f0eueAWMnD1WyfWdvnU7A==", "signatures": [{"sig": "MEQCIBeultZUVLZZaAYx3SOKbiHZHGH1pXvdisqzurx8kpfzAiBsI1xxsRryeH8PEAGy+9M8P2c5xd+J5q+qvRpspmbNKg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.272c6df", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.121cf6b": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.121cf6b", "dist": {"shasum": "8bf9c26e3425d4220f8eca617c39e9403744907f", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.121cf6b.tgz", "fileCount": 4, "integrity": "sha512-mwl81iJPHMBYX+H3nBfvfXC14KN2A+xn3HC3kkH7m7YyMFMWbDj07SekKOVx2Kz2q0ObsfUQCxoo8EuZCcAk+Q==", "signatures": [{"sig": "MEUCIQC3GmNyBMMmBlwmcwU4JtNbrmXCPTJyhFwpBlxIERETngIgUTORld0UXL3kzBVPPEqJf3rGQPgT2PLqkVx+n34HCPU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.121cf6b", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.f678a70": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.f678a70", "dist": {"shasum": "ddc6b5cdd72b8e1ed3918edb00c30fd83f554596", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.f678a70.tgz", "fileCount": 4, "integrity": "sha512-MfAYq/yCMUYrz2ZLE/Vj112uo3yzPYpXZrI3j3r+cb7vq7X7t8UCX9qV4HffFO/VyFeoL5UAleUZHnro4aRA8w==", "signatures": [{"sig": "MEQCIFxgKDJ9B66LGE8O+Ma3UfyEePe//hXa/YCl89nk70qaAiAUhtON5qubUJVYYfVtqlU934Y9v2n3a6OIFFynf5Y1cg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.f678a70", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.17c7c7e": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.17c7c7e", "dist": {"shasum": "b8999fb60f9d5d28fb357eb40c83f2b9101901eb", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.17c7c7e.tgz", "fileCount": 4, "integrity": "sha512-Di<PERSON><PERSON>lkuy9Sjq/7E230VCgFrwVJ47HXBUCtDwUIW/cuMJ0EhgGFBoZj85S2v0hLWb2oh43U+oXiKAvMfUo4xxg==", "signatures": [{"sig": "MEQCIAC72HgE5iuA0OOYgIh5vYRp5GaL09ppykGcB315A63DAiAHatlZscbH3LyLHvNRyBd2WSR81UiFyvAXm0uHJgCspw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.17c7c7e", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.f995dae": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.f995dae", "dist": {"shasum": "0090adacece8c0e5a4008a1ee0a696f6c7b70726", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.f995dae.tgz", "fileCount": 4, "integrity": "sha512-tTINM8cYAsYGxC37VrvkzRqEmAbgWk0vcwW4ZmZbeELjQpNqA4Nhgz9xNFTh+75nS2pvkbYbHaW18vITXz4ccg==", "signatures": [{"sig": "MEYCIQCp98qUELrulxbGy7ADLX9FtvMvY2xM7RlJidaXGMOpCQIhALFr3bQhOInMnctqTzUwoOaNuO8Jqi6Lszq5Z/ysemue", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.f995dae", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.aad440e": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.aad440e", "dist": {"shasum": "4d17b3ebe33cf0445a450bfd0568fdc9fd4e297f", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.aad440e.tgz", "fileCount": 4, "integrity": "sha512-UYzlybtOJ8ZFSaGDOgqMKUnVWHjCZteqCGrfyjVpxXmBQpkZdi9qx33BJdwhV6lRVqgSNoOM5ic8nYgpYgQ3Yw==", "signatures": [{"sig": "MEUCIQCWI70AdQ61cfvHok13NMenXz9WCSgQgrO3W0BHXHNahAIgaNVf34n+jgDFppgEKMEgzzKIW1pyU9yvOYcfVE2QZog=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.aad440e", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.ff8fd8c": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.ff8fd8c", "dist": {"shasum": "f73ed7550bb0893c51d8c5bad5be97dcc2c339c3", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.ff8fd8c.tgz", "fileCount": 4, "integrity": "sha512-pBUE+VGafRdCPo0TND6DG3vGrnAU6Bb7q7/YSyWCoTOzYx1WijX3QzOoYCLXCOuQvjXT5BPTBHicdzvjNMyq3A==", "signatures": [{"sig": "MEUCIQDUquX5Ca97K9r7ul/1H99eJJLDeATSgg7Zukyy+evtDwIgTVLaVuXPJDAODAqZpN/tQ0r8Q1VANygSL9lJnlC1qEo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.ff8fd8c", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.c80c0b5": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.c80c0b5", "dist": {"shasum": "e73d9e77da9e2deaffa2b544d041b9866af83a50", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.c80c0b5.tgz", "fileCount": 4, "integrity": "sha512-kF0zGpx/yKkCzwQkZcdsyiY3q61/YU+Gu4WuOqu+O3xOMqyQ6JhHPMdAltBR6vrWH4G8rhvdt5RbYuus73eM0A==", "signatures": [{"sig": "MEUCIQDBHKjwQQJv59nvsr0pVTFml4s9ecOoU6Wwzfq0q3vVSgIgX2CzSP+PDv3VLTwJBpIcuYi/T/+GDnvr/zvrVi8Su4c=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.c80c0b5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.7326f64": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.7326f64", "dist": {"shasum": "2f461f87cabbb443b977c8c509ef326f95846925", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.7326f64.tgz", "fileCount": 4, "integrity": "sha512-U8nql3QJCLLGU99NTwBBIrkmg0RHEvyxF0aMA6qSb0SY86DBqeGzQNH/roPaCpmrSoXys0uP7uBpE14pbsEUwA==", "signatures": [{"sig": "MEUCIQCJ8lL8VUvKghuWQK22Vv+yPzeAZ5bVk7LFviDBaDsA8AIgUHSnFIPkeTeARhfbK4IGPg8YtRsU4Qo6PAMiLb2euk4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.7326f64", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.53749c3": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.53749c3", "dist": {"shasum": "d8491cabdb2c568d1aa9fc78408cf4c42c9b2c4c", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.53749c3.tgz", "fileCount": 4, "integrity": "sha512-UevPdZ6VBeQQc8FbeaitSdi6se0a/Y0ixCcc/zK5krlx6wSljqU56MCKW7veeTZyFvCkVbuKJWSVpDd+Lt4LgA==", "signatures": [{"sig": "MEUCIDE28h8q0tDvWsB0lKPqwzWc2YFRvmyPu99m/VbYRWffAiEAvWk/vSlxsFmqND4EHOq6DIUy+GttDjjBUmxZenF8r6g=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.53749c3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.6164783": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.6164783", "dist": {"shasum": "c095b4d8b6fc1c7d43513bdf33f347c04d982138", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.6164783.tgz", "fileCount": 4, "integrity": "sha512-iUPjYyONTmU9xulQWksonvvxYN23gxCUrnL1JxgopnDZEyKXxeD0lSJUd45CgjHEVp5bauesrm6OKC6xzE/QrA==", "signatures": [{"sig": "MEQCIFJOWTu+tUZJWTp0jVyKy3Fj/oBUlXpAYL8kAq2KSshrAiBUNhz3pkGKjzLHAWIZSjOgr1V7hzepoaEP22KsIhkw/g==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.6164783", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.4f18f90": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.4f18f90", "dist": {"shasum": "af60958603c557c3c6d4db2cd0dc584acf129686", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.4f18f90.tgz", "fileCount": 4, "integrity": "sha512-QsNfijKoRFj4KccP7Pa2+YLesOXS3LfFYDsLC6xZb/eYzQy+7aK5H2kFMxZip8V8w37sy+o0V/S+hqIuZLjYNw==", "signatures": [{"sig": "MEUCICbrHVFcptYa1ZrOBX9Oyfi/GmDl4T2GFG/JM5mSRFNBAiEAsjYVuC/3VSF04vpFmsPUen5MtjuZkw8mLhOc1+QHISE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.4f18f90", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.dec6c8c": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.dec6c8c", "dist": {"shasum": "d51e5b410e925d16ddfc8584bfe1c435faeec851", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.dec6c8c.tgz", "fileCount": 4, "integrity": "sha512-8FVyBU7qJC0ZQZ3lLABfWWHyLoOnJU000cjcqrjx5BaDJBZVu/xmRF7A84ehV1nPihzWxgWZo9PatUSmUI9wjw==", "signatures": [{"sig": "MEUCIHmKnapaSo4G8sCYcjZJ+IrcKMvGtATkWyTAGZWbBFkLAiEA9g9WRHufqoVYmeFpBGOdG6LpwsAQRy4sSk5O05JC4To=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.dec6c8c", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.63b9be9": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.63b9be9", "dist": {"shasum": "b0e6cbf44e1690f6bc1241bcb11913437a625930", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.63b9be9.tgz", "fileCount": 4, "integrity": "sha512-0msBp/PNvBx5b51f+kaRG2NASz+BbhU3K2C1Rxd0RH9EiRv5JTlaNh0h/OPaqUW/Y8kBpfjTBDnCF5R3HfkbhA==", "signatures": [{"sig": "MEUCIB/gC2yd6SX4YdB0az6jquLzuHt9AVoIzVc931CVaP+TAiEAipxA3A7K30QTPpgykMU98NP7IbD6lIqh6dskSSfjD+8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.63b9be9", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.a1e083a": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.a1e083a", "dist": {"shasum": "083a3ae7928858cf800664f5fceb12667d9d371f", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.a1e083a.tgz", "fileCount": 4, "integrity": "sha512-V42tjZYUHySCaoPj0nCCCJNiykx94K1wQTHip6kqXaV05OompSH4hlb7T6Yn8Xw01KW5Hj20dJrUAmtz7qaDog==", "signatures": [{"sig": "MEQCIAIeNvGpnZMcHYIovFqgRUVHEB9C6uiAcLAuU5o0f4IvAiAUEwvdG9Pk4/bH5o05/WCQ452jfx2aDj/TYRi15BSe9A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.a1e083a", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.7d51e38": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.7d51e38", "dist": {"shasum": "fbd239604fb1e6f5e89b68f63c54c9bca979d959", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.7d51e38.tgz", "fileCount": 4, "integrity": "sha512-ZRwGkk0AiTCXHYW+J1mYeMSgqNEGscs1ztsnB9+4LDaSZh0Infz4gP1VebkQYkS9mpM8F6fPkJj0g09nR8hYmQ==", "signatures": [{"sig": "MEYCIQC82r5oMYtoeufoiqRcZ01PyoTyA4egPQSCZ3Er2KI6ngIhALHP4hTaj/w/ziP9yARifGDlAzF3lJgkozkdgg6N+Emv", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.7d51e38", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.fadf442": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.fadf442", "dist": {"shasum": "39a8223ac49f58b87c35af7978fbba79e12a29f6", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.fadf442.tgz", "fileCount": 4, "integrity": "sha512-+f7nKBFN1l/Ig/KyoDoedG+pfQpvFZ/tVRxMUcQG9D4nfMQyZ4eDdn4BuR/JvLEtBKxs0jDO8yMgEO24qx+XfA==", "signatures": [{"sig": "MEQCIEK9M9JZcQv9SFBup6epmu3t4MhpFU0uSujYFOjBJSD4AiBivko9W32cp9Xs/EskHHGVLAVGItZ0uIiPmYsyC7itOg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.fadf442", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.14b1337": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.14b1337", "dist": {"shasum": "b2bb3908406b6ea534fa039bd9163b81cb1ef42b", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.14b1337.tgz", "fileCount": 4, "integrity": "sha512-kpQFpL9NMzMSjRg5G1X/RS4r4Nk950Jgkoc6V1ajB7A1q9xCQ+X+m48BzP0ae07Lm+TuSS92/25PYrz7dr8T2A==", "signatures": [{"sig": "MEQCIFosZ3/5MfFooxduMRksQUqYnuoDlSIx23m3rdtXvKNMAiBXQAhDGeiVQoaDalXzpqm6pq/ESeOD4V25mxI2zceWLw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.14b1337", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.c504f78": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.c504f78", "dist": {"shasum": "7a8237e417af8271b6f736642caebaf74fc14600", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.c504f78.tgz", "fileCount": 4, "integrity": "sha512-x1dRiPPtl5A5JQDxa8wEv4kWdXkJQl6NuSB0cPO3oUd+P9+XxMs/akxP0cuGjjNxeLAs0QkP7uZEWRNH7Vtzog==", "signatures": [{"sig": "MEUCIQDxQzaJwjRdZDPmNJgWGF2oA83MJt5m84dL2157T2bKrQIgev9yF5a0Uo47y3XtDdxAd1lSkiU/FBW45gOOiy/vABE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.c504f78", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.34505b9": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.34505b9", "dist": {"shasum": "9dce76342bbce7c5fd0e940fb584b580641844eb", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.34505b9.tgz", "fileCount": 4, "integrity": "sha512-aJKEk+l7Zn6M0halYH4hoq73yTXeTiiiHaWc533mnJpzdKf84GznLxsPgzkh+WCP4H4dETux7BtxSYPDBu90Uw==", "signatures": [{"sig": "MEQCIAq7SES5VlIFXo3Y5J7gObKbpRSQeigGs4wle1I4+nV/AiAPnzkZmgc3bo8jtFZJSq3qW6/JnsVRWYfc+NIzk+/8Sg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.34505b9", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.bbc2f3f": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.bbc2f3f", "dist": {"shasum": "9737a232717fcce4e257271d9cacbdf462a35603", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.bbc2f3f.tgz", "fileCount": 4, "integrity": "sha512-vXCojyf9CO2nIu5Tw6UMI8wPHI8NJHl+NrMB3rwy5csFb8PU9Dz32dRAkti8U9/9U5r7ghtqwVgbkthQ7QZRCg==", "signatures": [{"sig": "MEUCIQDGgE6w7uwDRuFGPejpbZdoWmx0MZ5K60SW8s3FX7eeWwIgdn0gepe4AgVM2uHmLmJEoJj8kBMSsFaFmfM92h2MNEI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.bbc2f3f", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.8b13076": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.8b13076", "dist": {"shasum": "741242f3d53518068173928ec10b8d81ac54326e", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.8b13076.tgz", "fileCount": 4, "integrity": "sha512-OBVN/Kwzb658PZj/ltRZlit+Tb0cuRkCeRZByiLtpuqrL4AzQPsZDNLb0eIFCMkNejmc/kvr5oHG26PBICyP9A==", "signatures": [{"sig": "MEQCIDsLVPCV1laUgyqAqeiqF90ao7obcRRzjy5pWi/FSbgJAiBIKSwjdJ6X/DST0kpt/jSzJpYuoW6RGoty0WiXc4YAWg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.8b13076", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.1c905f2": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.1c905f2", "dist": {"shasum": "51edd9d76733a15725283e59cfebb5dcf2052737", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.1c905f2.tgz", "fileCount": 4, "integrity": "sha512-CZ/JB4DZ4N4CHutYIJB+wYk9P1sEChGVs04dsDwQzUS6mZOposhz5eiVYGgiGtwKiKTQ3HKK12SacTxoNS49ZQ==", "signatures": [{"sig": "MEUCIGt3DMUUVZa8BgRBwNyBmthYkZuWsD5skhzxVXnVyqo7AiEA49kUkL6R9HXImYAmr80aGarSk4rJJ1jTz/nVJl6xLks=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.1c905f2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.f014108": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.f014108", "dist": {"shasum": "8bd09dca8fdb8c5ce5185ad6f5be5b9d73060c8b", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.f014108.tgz", "fileCount": 4, "integrity": "sha512-L3hg1PVNym//yRGEw3kjpwkiBZqukelBxEib/KbjJKgavFOAb2cCVDGed+siq8rwKKBgyJLKrnmfe+VIr70Zcg==", "signatures": [{"sig": "MEYCIQC6fGCwjB9w+96CrWHvwrnO/Sx1XrRAMx1jGFsVl0D7QgIhAO048JLbiwPfxPuWsoRLIFU6twpc4LK4+uzdeSlN2s52", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.f014108", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1950921}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.08972f2": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.08972f2", "dist": {"shasum": "7aa13ea1ec19280fd8e12d0d5bf3768d44730f7a", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.08972f2.tgz", "fileCount": 4, "integrity": "sha512-9XsIfYlqfkb/o5mM30EYqmH8UNGOF62dj+nGwqNJRDIZGG5dwbFGIxGr8kV2cVnPeAOkJb/StjgMs2Nf/xE7JQ==", "signatures": [{"sig": "MEUCIQCCHDZXV72OGKU4PlvO2IyP4QcELrt/aXptgHFuTKlwQgIgN7oKmIJg4Dn1yEKIpGwrw6z7t9UXcC49FIMoa6Xw55A=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.08972f2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1952457}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.7ba4659": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.7ba4659", "dist": {"shasum": "eafcabfa612db351d34035fcfbff7dc4e8f60996", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.7ba4659.tgz", "fileCount": 4, "integrity": "sha512-L/bkoubljpszjN0lO5JgHdVIH2A9EO2oK9XzUwVD07v6JM6QOe6J7jSIJSzQcWVYAm3oU00gIybEsBbUnfqhbQ==", "signatures": [{"sig": "MEQCIF4tGlTcLDOvzP9ovZbUu9aEvFXNIaKn3BOqI5N1Xi2LAiAlX/bPGtDBPMF/WOAf4rXw4J0ocPN8KPQ1/4u7N/ey3Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.7ba4659", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1952457}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.ec1d7d4": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.ec1d7d4", "dist": {"shasum": "a0baa4dd3f007f9ad516ff5a8b25804ab5a36b0c", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.ec1d7d4.tgz", "fileCount": 4, "integrity": "sha512-SSZ199RSnsZZKpxOGdnjpIHIlT2yX9YR+NDVaSW3qySm1DSsx0D1WnUA+YaqaKDjR7jbAnyGe+Wnw9MAPVKNtw==", "signatures": [{"sig": "MEUCIGlCW1UcJ/+dIXigLT3fDdo5Y1vFvCEolBKUjxhROEqkAiEAkM1N7b32HQRxwpfmDjJVPK8cThQdGxWlxYf4VfwInvI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.ec1d7d4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1952457}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.23723e1": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.23723e1", "dist": {"shasum": "95dfb4872c0bf7b9bb2139a7fff0823a3b2d5305", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.23723e1.tgz", "fileCount": 4, "integrity": "sha512-kIDmeZHUPBjfeKnODitcbF1mBpibrZdT8h3neZB9DV9P/eYaKsLxGgzlwvqi/dhQGvFOGAE3f3p54B7VFKwYdQ==", "signatures": [{"sig": "MEUCIDlgGozYnL/r5KR1Xlan+6oRet+xiQ3ryqTFvqUQlEcPAiEApTMPD8j4DWoEVrmGDOGU87pKaraTWcGbsftacjQ9Jsk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.23723e1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2044105}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.541c3d2": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.541c3d2", "dist": {"shasum": "851cbf0ab0218757cd9a1b5c851df36a60591d04", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.541c3d2.tgz", "fileCount": 4, "integrity": "sha512-WwSEac31ly3qdKnq1TSJAQvizS7U9Dqu1WnO0YW4CK8cjnT4kysTxEQVCAdJqDYdZ8r6BbbmljiIzswkHjbgvw==", "signatures": [{"sig": "MEUCIGOFaP+zcY1RBEMoEwzRkZEd59x/ecJVh9ois9gJAoWpAiEAi3FnfbZC6UIOkFKvyGhuefi/YI6TOt+zlB+L5dNPETM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.541c3d2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2044105}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "4.0.7": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "4.0.7", "dist": {"shasum": "cceea5857939092d5149e6afc8da1b8d3a7464b2", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-4.0.7.tgz", "fileCount": 4, "integrity": "sha512-aH530NFfx0kpQpvYMfWoeG03zGnRCMVlQG8do/5XeahYydz+6SIBxA1tl/cyITSJyWZHyVt6GVNkXeAD30v0Xg==", "signatures": [{"sig": "MEQCIGGbubmWwGyqFcWoCKGeOrlhmnaPO7iQKv3Qjtolt+zUAiBUu6Wg8daAdBsOHfZLGvA8kft1mzJzpVSw01grdaHEkA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@4.0.7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2044088}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.61af484": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.61af484", "dist": {"shasum": "6633721260794d598d97fa1dd4850b5f1c6800fd", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.61af484.tgz", "fileCount": 4, "integrity": "sha512-XJ/wf7d8BK+4EJkcb9hcfJm1PTgV5YC/LPEDfpJiGJgGX0qDbztxhvK7lDQg/ZqxEr4arAjeAYex8GqqBtSqKQ==", "signatures": [{"sig": "MEUCIQC2Vaepy1KiNLKs19URbmfEyiofUcT3I5v0o69Fq3TJCgIgKw3CVkRPiHQFPgvcAp/V02XK0AL5ek6mDSsCEeELao8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.61af484", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2044105}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.b9af722": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.b9af722", "dist": {"shasum": "998e4766a3e4a0f0c9c01475434413bde86dc05f", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.b9af722.tgz", "fileCount": 4, "integrity": "sha512-GpAqJSxNTKciOpKTA6baiA+B6/+E8m89oYq2GtBuWRjN+PXlXRE5pKhfKaGdV4DNQDDlFrg5gG4uGHKZY1wiWA==", "signatures": [{"sig": "MEUCIQC/CpIuN5l6imtLUW0YG/rCACGLyurCmlrMLNZTKQJVSgIgLVVo3H6XBLJkiX+5Jqf6j1lqgqu10KJv9BJsVpZsO0g=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.b9af722", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2044105}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.88b762b": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.88b762b", "dist": {"shasum": "8ae7f203a94d2849b7903f8d5a52bdd16efdf0d1", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.88b762b.tgz", "fileCount": 4, "integrity": "sha512-RUmZWRANzrPI3nuXlMkpaAjXa5PEpHKFPS1AAZlsxCngjel4YjE3AFTk9H+auLQfy1nNApjm75A9saaLqzDzPA==", "signatures": [{"sig": "MEUCIQCpj6IN341oL2R1f8kzkCfOoFqK/pbHTOc2dirbKXeX7QIgNVfqTztt5he2CiZaL/clkW6z/bLnv1UYW4yFpL1Mt90=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.88b762b", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2044105}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.3f270d2": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.3f270d2", "dist": {"shasum": "b80564618de1e20a743c675fce0672462607e774", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.3f270d2.tgz", "fileCount": 4, "integrity": "sha512-StKgKY2KYm3//jFuZgVxyfW+Tq7+EJSctAKlg7qkVH/zBrJ3D8C3fT4XjkjtOPF1/ygBTMgO3WI5Mo9VKgOlbg==", "signatures": [{"sig": "MEQCIASXzKn3MZNIsjhUbJOv/SZrSlyuvTjmL26CUl3OwB4mAiAB34r7xEMFo9WE0IhXfSwMiapdI1WcyN1JoDy6jlFsNg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.3f270d2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2044105}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.1d56525": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.1d56525", "dist": {"shasum": "caf22c5153cfb9bcb5b9b436d9823c9da240cd02", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.1d56525.tgz", "fileCount": 4, "integrity": "sha512-LIMGlwadEfbugPLTRMUkmq0PyG3BrN165wWYyB9LEdCHMdFOjadMrz7GFtRlrJhFbRX4E6tCtCv08/5k7AOM4w==", "signatures": [{"sig": "MEQCIHjTdSWqR/Bie4ZpYQcLOzt3ENlBvUVEWsaT01IFu4sNAiBLgDDsJGxbzZSGH0MbC4MDvdAX+a4BbxNGcwJkOCkT6A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.1d56525", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2044105}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.ec0049a": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.ec0049a", "dist": {"shasum": "70377f1b15de13181467f69350488cabf1734936", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.ec0049a.tgz", "fileCount": 4, "integrity": "sha512-K/PtCL23/mC5Xv4vmAdZRRAHLPcrxqANRp16ayHlVtcAKoIjW/Brn/HzpiojDvl9sFnbdfXj25QyAoWk+b0ivQ==", "signatures": [{"sig": "MEYCIQD1+FeNeva/3rywA8DM958VVlyQ7t+prFDgwCBwPqOFegIhAMNQRiC7gKkUvKXq3mYAqvcZBkkNMTefXPcdqeDMMaqk", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.ec0049a", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2044105}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.dd7d8fd": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.dd7d8fd", "dist": {"shasum": "0a697a5cb8d4a19867fb500eaf7a19099cbf75ef", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.dd7d8fd.tgz", "fileCount": 4, "integrity": "sha512-c9vkiu56/dB7+LH+jYEYN161J+YyojZXeQLPPk9RW/kaVoWL5oI9y8IXCo+ftVBptM4UrPG2t7nUBv0ndwuVvA==", "signatures": [{"sig": "MEQCICEEYCP5bRPrDZfmJXQXtSRdAPTwXumwuE08vfUx0dCvAiAEThBC8B9y4pO9ZlPUdQX+7p0kSSsiMurmKlINYSFmaw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.dd7d8fd", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2044105}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.7bece4d": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.7bece4d", "dist": {"shasum": "5d26e0e92d819df6f84ed56bae7dca247a1e3246", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.7bece4d.tgz", "fileCount": 4, "integrity": "sha512-uA6AgfQ/C901/3jgxMkahYUoHwBmMaZzM1g7VaRciH2VjiDgo15P9foQOTbppkGwj17qOdxT8pvNNQiuvHDrgw==", "signatures": [{"sig": "MEUCICgZYuQfnoWjjmO6adtPY1tj+P3h5xSuW/sDMp6tO4gfAiEA8gBCVk2wIFrn+hbp2gg202cRCRu5o5YgXqX2kxvxxV8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.7bece4d", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2044105}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.f8d7623": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.f8d7623", "dist": {"shasum": "f7316cf2c39306a36857fe8a76e83231a7311ecc", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.f8d7623.tgz", "fileCount": 4, "integrity": "sha512-ROy20CYQrcCaOfl77ocZ0IRZowoiX4KWCxyPjQ5p+E0uDmNqww3ZG/YwaxgBm5/3xcazf227B9ucXKsSqP4ESA==", "signatures": [{"sig": "MEUCIC6RUrWAzmHUNv5xVHJKSO4bvI541zUdqMUq7MZykxYqAiEAmI4nO2tEzpD8XgGzebJriAdtgy6L15VuRNc5eWINplo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.f8d7623", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2044105}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.113142a": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.113142a", "dist": {"shasum": "2180677aec499133036e8478ce29076d824c7e77", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.113142a.tgz", "fileCount": 4, "integrity": "sha512-0n3V1nZMFSqDKsOmv9KeoB+WfkkYn8Cs+HcaACZIDMUVmSkSwVxf3NtWYJZN4xRl1cBx+teG5uUafDl5bo35+w==", "signatures": [{"sig": "MEUCICHw6K+6Lx6fk16YCBVhDc4QgbCCq7JLFZohKGk8hfXtAiEA1jt6t4/0Set0YUkZh6cvYCzADOZTizZQtTFVO/N/Khs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.113142a", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2044105}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.b47b6d2": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.b47b6d2", "dist": {"shasum": "6f84f5b2eef066b74b7bbfaa92db85b930608be7", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.b47b6d2.tgz", "fileCount": 4, "integrity": "sha512-13qL3dzwHu8d6K4W+OZhGVpIrOYP4/DDiPe0auXEOJvoCKVGGQoBEVMG60k+5dctlofl2m4WMxVe8pXknw4D9A==", "signatures": [{"sig": "MEUCIBUyhWALGspPvvHMAyRZz7f7zE/pOP5i6EGuOatZ0ATWAiEA/yId4bVqZLg7IEVUmt6r3ABVKRDeFbnKbTFH9+oLogU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.b47b6d2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2044105}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.419b3dc": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.419b3dc", "dist": {"shasum": "5a69718246574f6456fcda06e21b1473e4cff292", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.419b3dc.tgz", "fileCount": 4, "integrity": "sha512-n6RobM2Al4UKVq7uzBCACIpLbcETwT1CUgfD7i7jSzilBMrX9yHpLgsTZNMKZKw8GYQsUU5c+f/qx9Z8a8XSxw==", "signatures": [{"sig": "MEQCIGGBbfHNycNo6rFEtBiemk8Fo34Gls4zRgA/g8MKqoSEAiAEymNV2gz/z4jgp9bfsOpKWSef5jN1OYI1tNCqNEo4qw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.419b3dc", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2044105}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "4.0.8": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "4.0.8", "dist": {"shasum": "cb2e59d56c1846e841f02d7d36d2bcb4471b8ecd", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-4.0.8.tgz", "fileCount": 4, "integrity": "sha512-tndGujmCSba85cRCnQzXgpA2jx5gXimyspsUYae5jlPyLRG0RjXbDshFKOheVXU4TLflo7FSG8EHCBJ0EHTKdQ==", "signatures": [{"sig": "MEUCICqKGfC7JLWS3sGDzqKCGXSaQ8GnR41CmQpfmtzp+FuaAiEA6QOpX3GrHmIEtndU3GTpZMVOZhvCb9QeZRlvc2XK8Rc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@4.0.8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2044088}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.62d3e74": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.62d3e74", "dist": {"shasum": "a75e590ef52469b6b80a9a73c27f39e3099dcce6", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.62d3e74.tgz", "fileCount": 4, "integrity": "sha512-kLBglJevPpQGPfXF5EcDleD8yM9+ql9MrWIruPG/CEW7gZox+aPyNlj/Rq+Hd5fC/MRpTOr5yZ/qzlbcHA4khg==", "signatures": [{"sig": "MEYCIQCRLHmNuv/4xzEIVgcJDEqE+AdIC3gpnTCMJZgpt7XngwIhAOOA4CLROx3CNkbvJ8K3h5mrPnHkSMJtpi78NKlJOvyL", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.62d3e74", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2044105}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.751eb74": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.751eb74", "dist": {"shasum": "f38cb0f97bdf1c0bad49972d0230059efcdad830", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.751eb74.tgz", "fileCount": 4, "integrity": "sha512-ysZn9UkhhsbSIiObGeiLJ13O6eNJLArr82VCpPCFA4wkn3R34kbPNSjQjBxD7gdvDrqZ9lPQt5F85vTqWiEMuQ==", "signatures": [{"sig": "MEYCIQDUYMxxaUaJEgG1CPmtf/o5bkXGggJlpOTFjqfqpyK9QwIhAImoil43CFNcD2ddFw4jbwfMn/37Ii5BC7iPOnv9/pO/", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.751eb74", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2044105}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.604be56": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.604be56", "dist": {"shasum": "2961736a034397979c28100a2f17f2bdf1484c3f", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.604be56.tgz", "fileCount": 4, "integrity": "sha512-7QwoY0zmwjHxh9UpXI4EHoui1UtWpiOkfXRKWTL1TZs0f0kkrzsx5niUhjHU7yzgUoW3I2y1voa6TwAPrOaLLg==", "signatures": [{"sig": "MEUCIQCT9zjm8blT9WbneQk6GUYdD7N1GNzhXrBLGUnmyQx7gAIgUuPpsBCZXQwTbOsUZib56XgFkwzrPyrLkmuKLeNe8Go=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.604be56", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2044105}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.a893de2": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.a893de2", "dist": {"shasum": "a36a336eab13a9a56909e434f56b6a377be19941", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.a893de2.tgz", "fileCount": 4, "integrity": "sha512-kZO/SRbbe7E4Y7HlgurLt5bA6r7NpXA0ptdfw+9IVnZ/VIkvKVISwvMPn3dEaMOnz3ce7lejUxhSp8rFcIKkZA==", "signatures": [{"sig": "MEUCIDqzAE2MGD9bPeBwtdpRa8Q//RQtvijz8d5yJjFsvKCvAiEAiTNpJbV6zfqzArZ37c7iPZFAo8Pz+0XgIJcTf6uxvKc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.a893de2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2044105}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.37ea0c3": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.37ea0c3", "dist": {"shasum": "4dc7cba6bcd6bd592a53151c89016544ff61529f", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.37ea0c3.tgz", "fileCount": 4, "integrity": "sha512-uhVTqLqLNEn2GhDG6pOThfgYps2SUjnysxHdNY6BvApPZotAqEUZHQA56eoYdg0Rd50G52NcoCDSbqAZb6OBMQ==", "signatures": [{"sig": "MEYCIQC7yfx5wUyg3DLo7LyvHOkmQ2ilLm1Qta8TqEDMA1dTqQIhALrOPBNLWs45fbdy9MSWwJZZiKDKKh4H5Dd6cHQONvy/", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.37ea0c3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2044105}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.59e003e": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.59e003e", "dist": {"shasum": "ee0e655504409f0bf5778bdbcd6bb4edaff9723c", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.59e003e.tgz", "fileCount": 4, "integrity": "sha512-8DOKha0x8A17HDrldBTqdQsfMZH3NPG5gQ1BGFI/vj1yQwBx3Q6rnqnPMjLzH1y/fogwxw1c8S4yTFAbSw8uRg==", "signatures": [{"sig": "MEUCIQCGXGrkfdNWF2CnUJ3+zZ+bQraTYi2PcwjQHnhwf9YKqgIgCwESlx4n03kg0foDMfRx03Nirx9xGLjhKHSHtbmooLk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.59e003e", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2044105}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.b389483": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.b389483", "dist": {"shasum": "5e47fb6d470e250d167ed4f5ba631c24c2de9fc1", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.b389483.tgz", "fileCount": 4, "integrity": "sha512-gpRe7UgJvoWpW50+aUkJEWU22wMHFJVqwTSZzrTRPOEb6vgt3MAc9mLBf2EkcwInD9nX+0cj8fPeyxGc7YOZSQ==", "signatures": [{"sig": "MEUCIGM8IVhUFa3Dn+D3Lm2qoUBO30gZ+rG4qXJJDIWT6H9gAiEA3WgcpcDXP+s+ZS/KUO7wwm36Le32m9uErkRIAAxCLno=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.b389483", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2044105}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.ef57e6e": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.ef57e6e", "dist": {"shasum": "c1c5b8e6dc0ef61af3fefb9ffa2bfa0923de9d8a", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.ef57e6e.tgz", "fileCount": 4, "integrity": "sha512-G131QnMpM/oK3mfGIGP7lDgMjWtCjo9WVSmBSQM3VUOP9ou0X6pDGZVqs4JWdlNAKt3DbRJwKb2U478KFIhpOQ==", "signatures": [{"sig": "MEUCIQDcGq+RePkF9R6OIGrxcFjYLFDFdNDbfTLyRoDelZp7EwIgVi1rwU8gGzzLcUXBmO8kQ5j7/hwj4hGW+3KUNfKUWZU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.ef57e6e", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2044105}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.662c686": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.662c686", "dist": {"shasum": "d97029dca45535a3a447e5ebbd9079929b7a015f", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.662c686.tgz", "fileCount": 4, "integrity": "sha512-22XVPBf/s0r8pbJaWrruK16rQ1Sh3l9qkcI3b7epX1JmC2zVjaXdSWSDI0/j/IE/X2XvyJl3TZhBCrkMj0lvJw==", "signatures": [{"sig": "MEUCIE3jdmBTNjOmdxlmpc/hOSN/1bRtHgnTTSGGdYRHqqSnAiEAi36LjGCM6NNn5mRIBcaQbirOvi/ywEtude7Q0wA0v4M=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.662c686", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2044105}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.a8a2a43": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.a8a2a43", "dist": {"shasum": "e7ef0f386473b97c67c81f17cd0d88c007d4a7b4", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.a8a2a43.tgz", "fileCount": 4, "integrity": "sha512-ZkQLzqqNJVdibNFrjlO8bKPkRa0b1p4wNthf4fVIPCuR8vdy/uAMC1Ryle2z5fpD9PkXVzjCFFtV8CuruavvTA==", "signatures": [{"sig": "MEUCICYHTLqGi0FXFmSjzLwrLOaupQzCQCng0FyeEW+93qCdAiEA02bxkeKpHanJvdqT7PjRnb5DctEb0/Zuef6/amOTXvM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.a8a2a43", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2044105}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "4.0.9": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "4.0.9", "dist": {"shasum": "5e51fe3859216703c72c3fabde8e86b45b2786b3", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-4.0.9.tgz", "fileCount": 4, "integrity": "sha512-m3+60T/7YvWekajNq/eexjhV8z10rswcz4BC9bioJ7YaN+7K8W2AmLmG0B79H14m6UHE571qB0XsPus4n0QVgQ==", "signatures": [{"sig": "MEUCIQDNqWddgC7zdX/6oLp8N+waZLukM9TP3Wuae391EfdwVQIgTOaac57u/Ce1PvxCsJLM/sdNaEQDRSJss+3OxaG1CyI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@4.0.9", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2044088}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.ab9df29": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.ab9df29", "dist": {"shasum": "39e25161516d6e7e7e28362a31fb8eabca42dff7", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.ab9df29.tgz", "fileCount": 4, "integrity": "sha512-+s5WDsWcsn1OJqIFsLP08cDA8AbvL/kdDQPgLVwUg1WCE2lVy0pSHjb1Mrqb2XT22gDAczTzh6m5vlj5zgXA2w==", "signatures": [{"sig": "MEYCIQDcf9pdIaPfMT8U/3KyxOyROI87lJ2PkxMNPcKjEnFOxQIhAPEKjMu6zZ9evc/Ea+R2go/oFxsMoGRQkXG5GNI+Ip0v", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.ab9df29", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2044105}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.124b82b": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.124b82b", "dist": {"shasum": "a2ad1c1f2f5370c79cc61395fab18cb9896ba3c5", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.124b82b.tgz", "fileCount": 4, "integrity": "sha512-urb98Upxbq6m6utsq1E0S58/e0wzVqVl45V/myjO618+a6NetZmKXPYzFKroVkpvkgAQdD+xYPWRR/vOvuv++w==", "signatures": [{"sig": "MEQCIDgfKtTD18NbE7lR7BvlipXIV2aRqRXDuWqtyRbXXAhKAiBeFqidrBxQY1vg2zh8skwGf3WZj0YvONQXb1Bt+pC82Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.124b82b", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2044105}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.e938c58": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.e938c58", "dist": {"shasum": "be469cae90f26a65d4b1754df2aaacd50c22b2be", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.e938c58.tgz", "fileCount": 4, "integrity": "sha512-OqNS86VW8oWZk26nPEA8do1v4b4aHYGu99Jx8snKGH/SMkSeSptY3kuXOcySqoBqj34KfmhL16PQet6Gdl07hA==", "signatures": [{"sig": "MEQCIFf7K+2XplIpWwX5Wy51QkEnFeSDKC+AIOcrijaElgoqAiAxsXmo1P9zOm5CxQostjt4+YSwjLSl+EqPByIp/HIdgg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.e938c58", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2044105}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.5532d48": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.5532d48", "dist": {"shasum": "8e25287503cfa037de00ed2d02337a15bca6d4ef", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.5532d48.tgz", "fileCount": 4, "integrity": "sha512-g4hFLbjGqoja51NEE2mVkyjfCWD3989D+2a3rav9z4jpDzKPtT3StFGp/BVAw6VGTK6wQZ346dvIZLdP5QvX8w==", "signatures": [{"sig": "MEUCIGqNm7bZAhSf5jhDJeVOB+sDyTuesWdyN4023qDuuqedAiEA9ryUx0Z+ERxhETJdTB/C3wCNpnXtPQdfxeo/Qy/xpAs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.5532d48", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2044105}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.66ef77c": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.66ef77c", "dist": {"shasum": "9642e96c16f4c4f950c91899033a0eb8e2e576b5", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.66ef77c.tgz", "fileCount": 4, "integrity": "sha512-EyFLAQ738nKYxxVhbYxwYRWka//rzAVdpFQ75nixoKNhJ+XwdmhKzjwLIVPu+uosdEJT1TKt18rqNbSiKtPINQ==", "signatures": [{"sig": "MEUCICNUOBz5ssqbYwhg+hM1CZZd/aUJEmh3AIXr9a8WuLbCAiEArgL4n6eWWVucbbBSBMmcCnTeUmdJCiJiWyRVJUvpM1Q=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.66ef77c", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2044105}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.595b88f": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.595b88f", "dist": {"shasum": "e1bf4fac6718c7b848781b695760ca23a9cc7ae1", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.595b88f.tgz", "fileCount": 4, "integrity": "sha512-h5qg+8XmfhRnFLBxUsq5JuZ59jBAFB+2Ob3DWSsJ6poOf8W9Hw4zkxtxeyx/XmeH5VW26SeWLHgl/VyK1JzFEg==", "signatures": [{"sig": "MEQCIB5DW1nmXXTBhRXu5VCAzIEbZeXmgvD/91eYuMfP1lnOAiAAzUWW+Htjf7rEXKbmzHhTaHDfZg3sBeui1iCrETbF5Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.595b88f", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2044105}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.a98ebac": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.a98ebac", "dist": {"shasum": "6e0a754c88ca6e23f806070fc4e7f574e0e06fa9", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.a98ebac.tgz", "fileCount": 4, "integrity": "sha512-k74P271CPTHKRPHB8HtW1Vcymkh8DJB8bGTT4BQ0Gm0NEQIT3OCefLZO7SU1BuAgreNB8xED2VmP9g8qX6ymtg==", "signatures": [{"sig": "MEUCIHSodaOzxgiHa3GzE0t2UaxSHY/1FBzaavTIlsKeVWl8AiEA07ckYjBTEq4l0mOkg8MykEU4XdlvmGZTvaDpOOjIezY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.a98ebac", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2044105}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.0b36dd5": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.0b36dd5", "dist": {"shasum": "0f95d2c84f234fd2fc1f0ad551f8b21356f530ec", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.0b36dd5.tgz", "fileCount": 4, "integrity": "sha512-q7mDODz3L39CcPHG5DM9NN/VZ7q82fmjbLsEuPMSG2FHqGug6A93Mw/DXxDKSe1c+mllSRlZXYrL0/JD/75a1Q==", "signatures": [{"sig": "MEUCIDEQEl92ED5elCalz0IWUJt4osGY1TPis4Dr+bcHCy/oAiEAneMMYoEriL6abF9Wh/gZvcTPxRvQRShsnZ2bqjoRxlo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.0b36dd5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2095817}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.4c11001": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.4c11001", "dist": {"shasum": "556f233c1002375f451bfb4cf9ab48dc62307d37", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.4c11001.tgz", "fileCount": 4, "integrity": "sha512-TzTfOTrQqug3NeIrMuKOLceKmV30VRgG8x41ryrD5a+T76bjfHbzMfyS5yRc8Vjv6cxNUkr2/qod5FuWKd6mxg==", "signatures": [{"sig": "MEYCIQC/ybtkNBwoNapqg4Q+nJYFBUtw02IqOnWL850Umo30sAIhAIt+/IKsiL9taVMtofN70WdjA6FN0G8J0GN03PSn4V7R", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.4c11001", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2094793}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.a8b64f3": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.a8b64f3", "dist": {"shasum": "5f3fd466619750e01b93b060d10c04964e90d423", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.a8b64f3.tgz", "fileCount": 4, "integrity": "sha512-2pKBsRnKkIgRwI0o3WqvJfc0+saLwbH6FUMSW08U5FXBvD06JnZuGtKHA+kC2ajcUSkZITcYwNmmtX/NbmOVbw==", "signatures": [{"sig": "MEUCIQCTe95yduLwKtqk2D75+h16FTiWbxgxsSHNuTnXDzaSEwIgHneWgTcmiXZ0v07StIiHrYvtrwCHQd/nlLB4WRneMkg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.a8b64f3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2094793}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.1638b16": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.1638b16", "dist": {"shasum": "5da70d5968425d7c5446f7ad17ffdaaa5f5c1e1c", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.1638b16.tgz", "fileCount": 4, "integrity": "sha512-NrrIdiykTgT3UVEGpsuSnkZgopvrAHwKAfhKzqW944nlM68cI0NCQB+34hCoMz31ZSno+sdleD+NDt1Z38ZPlA==", "signatures": [{"sig": "MEYCIQCpNo+VnYuD3FkfGOj02v02uqa1ggunqbbSWzlpU+zu8wIhAJaRWf9Xe4WXCwzjxwSU+Q14XjBTRXIi1SN8L3E559sL", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.1638b16", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2094793}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "4.0.10": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "4.0.10", "dist": {"shasum": "7942506f31bf0ca00b4c4540f8f360809f4eb3ae", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-4.0.10.tgz", "fileCount": 4, "integrity": "sha512-IXNvUmLBmTJNcMofOl8B0fzNvwUFPNvFE799THaEPgi16zj+WqFLVQh4N5+zuI1vgtZTaIJrZmqHhjqNPLOItg==", "signatures": [{"sig": "MEQCIBsD7qZtxjJUfB0KhE9pWzHAGuRu2tYEpU1lXQg1V3HeAiBUiP6dK39DUaBuKZQFXSPuMYR/T8jVNItrEp6HherEcA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@4.0.10", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2094777}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.9c59b07": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.9c59b07", "dist": {"shasum": "ab04ba9d8ef5551e3d7d3986139a27ebe715782e", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.9c59b07.tgz", "fileCount": 4, "integrity": "sha512-uhzsAIL4KGaoINtgDXAk/f85yueFHaNWUb0Odbue77SiIaQ4NpyqH7/0xTaVHohOP+Esn8QqQHppCfEtLT1a0w==", "signatures": [{"sig": "MEYCIQD5f8ctg12S8HWOo4Wgo/ygMCgw+rv8OuumC4lrv2lV5QIhANpxbF89JS3GTnCaH3abb+pJaqY4ZFcGgf88dN2FdcJC", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.9c59b07", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2094793}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.4a02364": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.4a02364", "dist": {"shasum": "2c32389d0d25d34e82cb52f8e500d08a1ff709ab", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.4a02364.tgz", "fileCount": 4, "integrity": "sha512-D1NDA/2L3U/O3/yL3K5Va+CCKp/bcC+jEjRST4QmoynWH1jIA+84mVtp810RQWs8XgOBGoBYMY0/371w6etXpA==", "signatures": [{"sig": "MEUCIAVusDRwKWHnXCM5l37ioWEGYvtdtCN9x1el09NAk3K2AiEA/k4CJt1Os2yZkJeMasK48Enw8kwJlO8Unbv1FXLDfEw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.4a02364", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2094793}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.617b7ab": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.617b7ab", "dist": {"shasum": "360d7fe478fd5ae181424d08dbfa61562a7642c9", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.617b7ab.tgz", "fileCount": 4, "integrity": "sha512-erqh8U7G+t/VpQoaIz/fqgFN93LV7+STeTdPZZVF+8qHpZOp2z3PHMjdYXl7xEmj6wFztyyJnGXku7BE7OwWlg==", "signatures": [{"sig": "MEUCIQDLnQMimWd5T/XgQnGJKyBelFiPi8j6ehUrMabb/5XCGAIgcEUs7c+rQ80zA4X4daNmrUFCjpAS3+gVuOvd80WbHgw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.617b7ab", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2094793}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.af132fb": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.af132fb", "dist": {"shasum": "73ac5b339175747633eb58c8dba3b79ae6298c34", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.af132fb.tgz", "fileCount": 4, "integrity": "sha512-rx2J2vFD/E9TyPGMfnQP3lkSIxVYUKuenApJwCUQ1U25v5uO6+eQVWJo0uNc9cv1dssKiwKmj7nTbmpbwfQyfg==", "signatures": [{"sig": "MEQCIBevcjH+qdnVlH7G9QmatDqtgGCAfKPnTYDGNhgmH/spAiB+i3kZmUB+KU41Wg4ac781DTsQsiAIgrzKgcoPKFReog==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.af132fb", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2097353}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.bff387b": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.bff387b", "dist": {"shasum": "87e620ea7792a43416cd63d756675c088a1bfdef", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.bff387b.tgz", "fileCount": 4, "integrity": "sha512-46yrmRjHu/OXmawBwKGszaMIvrTe030wrrMa6bH9g49qjk2Dpefuguj+tm+xWU9F+AbJeEgvF2e9oeR9zAievQ==", "signatures": [{"sig": "MEYCIQCZ8MMiTWCo/rmm+NrDrIfdLWg8HTOLVMoTTIJ6SRw2QQIhAIOIm8WpZaVAVhqzEHMjjrEzzNh0J0wuQPmuNs6gOXT4", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.bff387b", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2096841}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.b676da8": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.b676da8", "dist": {"shasum": "7d2e4c969624a603093be1e7d5a796e20dde48d2", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.b676da8.tgz", "fileCount": 4, "integrity": "sha512-1Kn7SKS8yCMXP6vQ2aB4AsehSewx3uclRN3j5nR+C+4stjdKmHzvndl+iWP1pbLBUx3dLUJs+t91HK8t1lCMwA==", "signatures": [{"sig": "MEUCICyw2nRkgJOellTMu0P4XU5XuUsLvA1qmaKCF2+ExzaKAiEAi2Yub4hAp7PncVhFhFAWtT7dGOofvGVSMXWqv75VOBA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.b676da8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2096841}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "4.0.11": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "4.0.11", "dist": {"shasum": "3271c1266f6c428189592d09b3221a2ecb510863", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-4.0.11.tgz", "fileCount": 4, "integrity": "sha512-ofgW1IugQDJR+fGJUZMniwTzrwHvaw6wpoOE1mIXBFP2wWoDjvNTXUJyMDxF2N6UypXGYCJMDdEohB1CyWf9cg==", "signatures": [{"sig": "MEUCIA5NYBB3zDomtkYQNJsdFl2rTwuBctKmmUYXA9gCRkLmAiEAjcvV5aMD+A6JnACjWYjmMUz2ftCz5XcyhHoSeX8LV40=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@4.0.11", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2096825}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.3d0606b": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.3d0606b", "dist": {"shasum": "8ee1d1bddd8ee2aaf351772b3fac60976d2c0f93", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.3d0606b.tgz", "fileCount": 4, "integrity": "sha512-ZH+30CzQTCeSveBrlZ5vZoesm8ngwaHWUGxYM1FNC3i0S+746oxXvnWEZmV8ayIVRhv4xGli0JlCNa9TkOvcHw==", "signatures": [{"sig": "MEQCIA6xQEMWRfVg4KQkVGqKe5HENBAzqdBp/QtGp4ZQU5VKAiAla+GNukXU6gvKj3A6i/laKoAtpYxIQtKdLRrCEhKnXQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.3d0606b", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2096841}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.85c6e04": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.85c6e04", "dist": {"shasum": "e2a4eeddd37a7f5488aa93f0382b292f36ddb850", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.85c6e04.tgz", "fileCount": 4, "integrity": "sha512-XDHc3+Guq6n+NyUX5vqwoZOWb/Hg/8hmwclyIMKKBRe4WKYbB60bKvYBsFMWutf8+xu3yXf9roDDYUVpxfBBgw==", "signatures": [{"sig": "MEUCIG8QUkOhEYtiLJ+z6+1O7rHwg9L/3DlBlzgaeFgkFiwwAiEAtMz0njNZxKA5AOMb/Ovya9DS0wtDXzKnRVHbPmI0/dE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.85c6e04", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2096841}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.57e91a6": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.57e91a6", "dist": {"shasum": "0f43b9658e9cfda8b4a119c06dd1bbc78b8ede07", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.57e91a6.tgz", "fileCount": 4, "integrity": "sha512-4Nq9W3paqee/fre79mou1W93aJE3kUiW9IkiOoa4vOqAnLNiQt/oEktdglEtw1D8IIArppP55HtujyS0zeh2dw==", "signatures": [{"sig": "MEYCIQD1dQp95otFF2d9j0jVA+LAUkgFrz5RSJ8GKAiY4efJKgIhAN4kdIPuYbZ59i54x2Re1EMn0o/iKMDp4DA8p/ltk7wX", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.57e91a6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2096841}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.d0a9746": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.d0a9746", "dist": {"shasum": "5da780be5d7de363de96968d0bafda7d31bb0919", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.d0a9746.tgz", "fileCount": 4, "integrity": "sha512-RBTg6RgbBFnDnitnSiR4MDcLd5dH/9bteccp1YC56vQQzapBPGKkR6rp+yzES77Ks6jVQ1G2HrawdDswQpqOdw==", "signatures": [{"sig": "MEQCIEJkHywK7OKIpylM78T6sdebCSd1DNcXiUIU6HYHJQLEAiBdPPZf++Hjg1JEGIHHmJ6nNYtULh2LK8PRidboJ26d7Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.d0a9746", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2097353}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.bd1e540": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.bd1e540", "dist": {"shasum": "f9ee91d020b23e4c0994774c43cff0b7a9852038", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.bd1e540.tgz", "fileCount": 4, "integrity": "sha512-QEGw1Ufu84ijJbcufib5NJxreQcRYE3gtnIbvKmcX5qraYRTGfvvDXaNccfTaXt0uB4el3SQy3yB+LAG7smqBg==", "signatures": [{"sig": "MEUCIH3JyyXr4bqAeQSd6hbxZaZoqoojsvCoTJ81XzOHSPk0AiEAkF45pVBtyNN2XhXyBnv45tHUWJ6+uJstn1UN92qC9PA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.bd1e540", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2097353}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.d18fed1": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.d18fed1", "dist": {"shasum": "394f07c70720c0a410c6bbbb07233833fb5dcc1b", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.d18fed1.tgz", "fileCount": 4, "integrity": "sha512-YjsWW3g1DVPs5PWDzgS8GZtQdnuf26lY6+TWAic3ehN5wP7CUGL+v0+VdRASLEcp5Al7OWHoK9MkxGG/sMcWlA==", "signatures": [{"sig": "MEUCIQCF2zGzChcqcsWwKLJ+OhmqRwtKGJQfTUNuFn4sJzGyJgIgFhMgnxMHLLWMDeydIXRGtYADZDjiwLAzVkx00Kf2d0w=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.d18fed1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2098377}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.225f323": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.225f323", "dist": {"shasum": "b56125b00920243b3e7f1cc54165f70823cf105a", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.225f323.tgz", "fileCount": 4, "integrity": "sha512-8IbHi3qRYGW6MiQtjp0GUmREJPKwaJ2Wxc0OmSx/XCb9zr/G5f8c6sxQ/pu++B31NIvinh7BJaDVLq/3MHXLbg==", "signatures": [{"sig": "MEUCIQCihIzCcaYQRyBZedEO/G90DZ0BXowSy3m/uGc/+GivFwIgbTuGnrO4dI8juvgr+Zp1m6OlzZtVW0F86UncduJxQG8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.225f323", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2098377}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.f498e4a": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.f498e4a", "dist": {"shasum": "a5823becca071163c3133447646d8851b18dada1", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.f498e4a.tgz", "fileCount": 4, "integrity": "sha512-Qf0Bqbb6p3t4O9hsnMDg+3KC3QO7ZQnLb5bzZot/i9MfPjTWD+yxzmrTxAc2VjD7QM1L3vnZwWpGF4XO6B5kcA==", "signatures": [{"sig": "MEYCIQCRgpsqyDozHDN+HxQZtH/xichdtQGeSHqYQRYNgDmgNAIhAJ0q0+Mh3PeMD8mJye2ZaGUDXgZIxyXm3OSpm8BX+6xr", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.f498e4a", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2098377}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.2f28e5f": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.2f28e5f", "dist": {"shasum": "6acd642d08d53349fcb4bb320486d9e819d1a0a3", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.2f28e5f.tgz", "fileCount": 4, "integrity": "sha512-vYrn8BK5d+YVObZI6HwfCdzOylaPgvNm0WqkCQC1kk/WdGfVyeg1aB1pF3jmqHFDcvlqd3SuKjQ4yI4J0eoBbg==", "signatures": [{"sig": "MEYCIQDtMO9rdIPxZgdee7oKAHGq9J+ciGKhdh4L2LgzAWeEqgIhAJqQiMhYfdnP8nOyulD4HcV/XfqM8nv4y+Q9QiEFn5Qq", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.2f28e5f", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2098377}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "4.0.12": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "4.0.12", "dist": {"shasum": "63c0befad43355fb5cea6c64efd946a718acb06b", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-4.0.12.tgz", "fileCount": 4, "integrity": "sha512-XDLnhMoXZEEOir1LK43/gHHwK84V1GlV8+pAncUAIN2wloeD+nNciI9WRIY/BeFTqES22DhTIGoilSO39xDb2g==", "signatures": [{"sig": "MEUCIQC0Dy6OSTS7gu7gM7nPYgH9MLUSPwip4Wajv7ukFfrOvgIgZtF3ilYepZsgsFPVI7hR1TavdRpowm4S3ZBFWEx9bmc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@4.0.12", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2098361}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.7005ad7": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.7005ad7", "dist": {"shasum": "5557a872fbd9da8cf6e6407b9962d5d0f77d4199", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.7005ad7.tgz", "fileCount": 4, "integrity": "sha512-BbMLdx78XOPtyCQGrEQcIUrnyFMFKpe7eyZmNPIUipdxWwPc+NbCHmtYCVP2hyIp3yMjaYkn59CFeGnmt45COw==", "signatures": [{"sig": "MEUCIQCYgs13nvigkrxbIOgRQToeU1bOlzXCprAwP+xwkCOTLAIgFoM/BhJdRMc/nCvyanBErDsSPeBrDy/8qoB1XIL0m58=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.7005ad7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2099913}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.bc5a8c3": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.bc5a8c3", "dist": {"shasum": "bda40669d8417b80c76489a900be18cb935d47bc", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.bc5a8c3.tgz", "fileCount": 4, "integrity": "sha512-QCbDijgXPpF2uzWR+LxqdHGKzc5rvaS3Elwe4LdztnmcX8W2c6PLrOHZEOPfX5GgF3ubaENbftjyV1HggpPBJw==", "signatures": [{"sig": "MEYCIQCHlpaofYvON1SeQT+FcWCu636pT6JVs9UnXQ0u4QesgAIhAL4OgadmaEKKrIpTKZbRTY8TFPxy6MhQf/dEDCz4FnN3", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.bc5a8c3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2099913}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.cc3e852": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.cc3e852", "dist": {"shasum": "f5545bf23ab64e37a63c6508479913d3c28c3785", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.cc3e852.tgz", "fileCount": 4, "integrity": "sha512-jQViYqnTI/2C9sUuMAmoHmXo7YMom5iqXIKD65uuqykV+CIX/szmq+Pa1rAu9sieycUujwOBIS/TSAp8ObxFow==", "signatures": [{"sig": "MEUCIQCjfAPXkuJs6wmFKVwK7PBPsAksJo1nycm7SCfNfPzE+gIgAy7fnaqDhFzf9Lh7SNhKvhElQcuIYXz9Qr5IU+EKehI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.cc3e852", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2098377}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.de145c5": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.de145c5", "dist": {"shasum": "9a218ba203d0115800aefc37b516c09c29a0b7ec", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.de145c5.tgz", "fileCount": 4, "integrity": "sha512-qQS03Ia5gGzgINzDDG9yhkSShRUK/VyT2VJpXDQiQoKGLEXz8YcDR5/PXUFacl4TFxisy8dZ6fobhvDRMKzTHw==", "signatures": [{"sig": "MEQCIA5Upkd3MwnhR3/Jg6SmV9gk4Fciz88GJkZfYIkwrka9AiAaMpj4liIkEKkefaJUem9P75DzDmf2NMZ3vTqAjtSJLw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.de145c5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2111177}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.a57cd2d": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.a57cd2d", "dist": {"shasum": "16a26128f54fe758bfa5a1cd4ad6f6d0d54b5d33", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.a57cd2d.tgz", "fileCount": 4, "integrity": "sha512-u6qZmMQpS8qTcPih2MOuoxw4nFIe1+X6YEwPbKFpQU9nK96WgkS7cuWhHqaSt5aa1gXw/Dq7j369/LZeUYwXUg==", "signatures": [{"sig": "MEQCIB/HZhrmp0eYAQcCyJ5jbYJ8/h9Qfp8IW07IMv8ykGyfAiBXWQ5U1bKpi2Df3Tj6rUqh2fU7akhWA/XRMXOAlVA2Gg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.a57cd2d", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2111177}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.5e2633b": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.5e2633b", "dist": {"shasum": "ea62cab1628cfbbc362179a4703ae58ac8f06f82", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.5e2633b.tgz", "fileCount": 4, "integrity": "sha512-u/T6tIiJDcHOagcGakiuzXKoBL/MFNev6LYZT6ovmTiT1L4TAOtxsm7tx4tOfi0CKq1Ram7EmS79Oe3vmzEBcA==", "signatures": [{"sig": "MEYCIQC6eqWz7sRMjZtVU1y46qV7i7Qux1tdjng8mtVxEFvBTAIhAOadq1pQ/36hTaNBTi2/xUqowZKeh/o4ZNrIb9eiE23e", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.5e2633b", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2111177}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.8bc633b": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.8bc633b", "dist": {"shasum": "0ead9ff432d55d125fa3b1a0aa9afb02d181369d", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.8bc633b.tgz", "fileCount": 4, "integrity": "sha512-e7xPvTbGO7DqKqHCq6okW8BqP8DMlaeVX+cdeDBNreMfrKp4C+UgdOBhnrLvYJT4tZF3boenT/B5N6RqC0qFcA==", "signatures": [{"sig": "MEUCIQD80Qob6QrL2WF3iUneEYQpPCipWqISPI1XDtwcKWDzRAIgDv4/uH4PZi6yvAnaRXsOVFeWx1SYIcoP6XCfrBFCmBU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.8bc633b", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2111177}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.9ddeb09": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.9ddeb09", "dist": {"shasum": "09f236bb872b58e81945d0791c37a810ac5edb14", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.9ddeb09.tgz", "fileCount": 4, "integrity": "sha512-DCa07JSmtg7vBhwA/bRYkq/tKpGgwpQrSPP4hUrPsmxZtIrZvluOMigByuQ+PCcHlOxKLqLJDVQSNfP/Bj/wCg==", "signatures": [{"sig": "MEQCIE8bAPvdW59c1l1or7zVXeMv9hcZkUKXOSeMF/zCy9zpAiBLnvhpCllYpgMIqWtjAsxCiCiarlNEFTZY38Cv4xMnjw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.9ddeb09", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2113225}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.785cade": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.785cade", "dist": {"shasum": "212c01beea6cb721e7c047b6b7f7e8dabc0116a7", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.785cade.tgz", "fileCount": 4, "integrity": "sha512-9R6S8u1HW1KfM5Bn5T7MPyPdAh3NrFAAG67FYx56DsY1ni7SVnlxcDnWpGYTiuiFoOfSEgTwK88l04FvStvvFg==", "signatures": [{"sig": "MEUCIQD2lYXmK8ud+kwPRw7y5OvpYMKA+9oxUmHprliA/S+9rQIgZBTYLi5DVb5drj81rUeIofX36rUms+InqfNY8AA7jQ4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.785cade", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2113225}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.9d7f253": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.9d7f253", "dist": {"shasum": "1f51e61e641748414c689a99d4bdb1db4c9cb633", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.9d7f253.tgz", "fileCount": 4, "integrity": "sha512-qzVydnMam9QG86utxdvaGufsgFX3KCxHt/CZQ1r+zWc1Wt0xQcEn2Zbuj3KSp49WIqhgMefepbtyAkTfoKaZfA==", "signatures": [{"sig": "MEUCIGQIc4gd3HlkGHPqV3Et5+Fp7/DQx6QbrdzIaoezV3c/AiEA3oIfQlGjcE9kv35geB3vItwUvSWpMkxrn3Ib9tz322A=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.9d7f253", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2113737}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.22746e6": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.22746e6", "dist": {"shasum": "db3e777bfbbb93f4c25d8669df6b8ca26c961a0f", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.22746e6.tgz", "fileCount": 4, "integrity": "sha512-jVY+AbQmh2M2PPrIbg1OYSPvasN3HbgAgtRXsncmDxqDt8zAznZ+MHJoqCiwyesexmZs/grqq5STJPpY4japxw==", "signatures": [{"sig": "MEYCIQCLRVCfLyMILZCHW1UNtlsOv3BegLlZ9K6dW/cbhWQE2wIhAKKnvZVPjGC4pDo9OjK5y5iQQKkfGSI8IWXmVsmRrjPE", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.22746e6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2113737}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.3706292": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.3706292", "dist": {"shasum": "483214faea2f701cfe95b9f9e103ae802a9f7798", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.3706292.tgz", "fileCount": 4, "integrity": "sha512-JQJQzjy9tdbGfg0kBh1R83iIfQA/IqtatEsxpWgczr3BsJxMJC9wZmKOAe/nLrHkRQXl+PCbJ4nra34qnDcFAw==", "signatures": [{"sig": "MEYCIQDrfCVvKK4NI5+Dwe6qtTzAIxxeXT9/OsZL/ntELstZEwIhAOjK/DcUe8iX5nSsbN1NsKB1K9IN4LJt1Z8O4Zcnx9Ji", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.3706292", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2130121}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.4455048": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.4455048", "dist": {"shasum": "59f03309a47cc42fb8422b5735667f9ab7ef4d56", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.4455048.tgz", "fileCount": 4, "integrity": "sha512-4+/lGkUaF3tJN7e1aGwdcM5DVzdInH10VgIVBIudFbXH9rjBuXKwJ58+rjZGs3O0irwKg9IulSzoop61qRIqkA==", "signatures": [{"sig": "MEUCID8gC1feR9CtO1E+8gLwB8sizovBKEqTXkRYY4HrtiEmAiEAtgD7P18Qs6yVJ9DJQxaP6Jmw3H92HX3rBZSHY2mvyHA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.4455048", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2130121}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "4.0.13": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "4.0.13", "dist": {"shasum": "682db74f734c446579993ce3c043755e6b6fc57f", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-4.0.13.tgz", "fileCount": 4, "integrity": "sha512-u2mQyqCFrr9vVTP6sfDRfGE6bhOX3/7rInehzxNhHX1HYRIx09H3sDdXzTxnZWKOjIg3qjFTCrYFUZckva5PIg==", "signatures": [{"sig": "MEUCIARMVXCjEcmwt7GDNoA43dIfSEXJ5a6joUUzUaVenAVAAiEAkilapIHQkSY3TABDq2PAVIPqeGAUqYtWedsAsIHKymY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@4.0.13", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2130105}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.ca408d0": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.ca408d0", "dist": {"shasum": "0e9f9dfe09d519f8a3c338b2d34e19265304db7d", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.ca408d0.tgz", "fileCount": 4, "integrity": "sha512-UWAm9oPezmxmfBplpzFW9Si6o4+47IhgyB4W5hUHiMqpOr8f9YxXxvn93bwmb3TM19dEnfLqP675ypkx5LlDuA==", "signatures": [{"sig": "MEYCIQCiumrVWzCoKfphbel+/cvt+bRIsUigU6Q0HeOc5RKfzgIhAPC73mBDlN1DVCiTmpx+7oIJaHac00kGT4R6AFxjyHMg", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.ca408d0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2134217}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.215f4f3": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.215f4f3", "dist": {"shasum": "fb82c1d3b8302ac7e05f369917587d25a0c2ad44", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.215f4f3.tgz", "fileCount": 4, "integrity": "sha512-7IB6ZWky2nV/il2Mo7Fmj7YEWojqKt2PnkAE1DxcqZBMawCc6ueKMnBMRptROQciQq5zBHD4iVX8dBKSaV1AZg==", "signatures": [{"sig": "MEYCIQCc7y0AIJxDpD5z+mEsLcaD4+3fvphq9HwXpz5lhK6oWAIhAOpVGFU1l1XdBpe6uILT3VPea1dPHdMQqG9+ONklyYG1", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.215f4f3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2134217}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.cedd54f": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.cedd54f", "dist": {"shasum": "13aeb41369ef3501cfc98aef6e198144dd1bc089", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.cedd54f.tgz", "fileCount": 4, "integrity": "sha512-g5dIycE69M+GV5EzPC+D+JWRYOgpJnkhnQ2g1NeJFXcqY8mU1o1CqPKKblmM4fCZb9vpRWnKSiNXeH2gYvGthg==", "signatures": [{"sig": "MEUCIQC1eUKfa6dkhlwIOPRB/fFKKrxj190tnuW8pyQ1OOP53AIgKTiUReUUYuu4/qTDROz7bLEvDymCKI4kyzf30vyfNRU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.cedd54f", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2136777}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.26f91d2": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.26f91d2", "dist": {"shasum": "59ec78803bedc4d1f6772655f7ea7390c3fb2c10", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.26f91d2.tgz", "fileCount": 4, "integrity": "sha512-v+UbBQj6ZQ/zrYc0sTa5SWXf7H7oQtpZp+2H8or1o7Y1mroLgDQh3y0KnV26twtupSjed+AfK6WNI12vpwo4mA==", "signatures": [{"sig": "MEQCID/qvmj4mTC3pQm9sTFn+faJ51tQsXyxzOXv7RauYRVhAiAOT3HlQ1roU8/ufnqpmYtTEw7ZTYYrUwQAskJpPTBq4w==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.26f91d2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2136777}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.74ccde4": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.74ccde4", "dist": {"shasum": "af01c80562e38099938fda74e8e0c5daecc9be83", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.74ccde4.tgz", "fileCount": 4, "integrity": "sha512-TuwEns4P5luydWaiBhIJL743LZWyIMjz8nAVbllXHuQqlyk1yj+bjVOaDyTYIdC9gMYyF1+mw49ii5LDBd0KAw==", "signatures": [{"sig": "MEYCIQDdQjsneqxjU1W3O+aO7u48O7+HY+ezoavdKjYN68b8XQIhAJ632OlzqAZ+QcmrUGrqk/WijLRrzOvTFMYb2asnfaYn", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.74ccde4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2136777}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.221855b": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.221855b", "dist": {"shasum": "28209c768e89a709f5fb2f1af66e34d3ee330eef", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.221855b.tgz", "fileCount": 4, "integrity": "sha512-3VeLSW3r5rpSXLCGk4wWz9WawLj6xvWE5v0F0nAhuzFp1fF4Fsclkddu+T4kA/kckLHjy8JxQMnjUQ7xlzkIoQ==", "signatures": [{"sig": "MEYCIQDP3mHd0m/4c3GUvfk9fNemqL44IHjtjGuvkxrR7q/tXAIhAIxL57apRO+XhcReA6AEDwRYxoovQr4nAeOMJcFePaix", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.221855b", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2132169}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.3c5903c": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.3c5903c", "dist": {"shasum": "f764dfa73186b2ff15a9c946ff98769f1ebd5ff5", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.3c5903c.tgz", "fileCount": 4, "integrity": "sha512-8pYl+TSmb5fuJgxDz3gDREEBANsuEDGwER1D+crxiHKAhaq9JkSqs+3Hw3HFMFsL2vy+6HCT3r5n7ANckcApSA==", "signatures": [{"sig": "MEUCIQC6oSqIIYS++xPHkHlyBSyA2rtqRZpZtQbEnWeZ37wgiQIgWE15ju95MaCBrOZy060fjGCDa2rSKAQbqABVmzYR+Pw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.3c5903c", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2132169}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "4.0.14": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "4.0.14", "dist": {"shasum": "8867b3b0d61ca6e775e4a217a27da3fbe1c9620d", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-4.0.14.tgz", "fileCount": 4, "integrity": "sha512-AHObFiFL9lNYcm3tZSPqa/cHGpM5wOrNmM2uOMoKppp+0Hom5uuyRh0QkOp7jftsHZdrZUpmoz0Mp6vhh2XtUg==", "signatures": [{"sig": "MEQCIFZiQWNgQcgxG0XVKcoFL/QnQSNg6QzBwE3peX7Nv3OmAiAwPMSFBekw5i7mLLIjgNxCfAJ69uxicmFzvfCZ9FDRFQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@4.0.14", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2132153}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.50562a9": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.50562a9", "dist": {"shasum": "03ca8dcf7507b972bad8fc9c9df816ee46d119f0", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.50562a9.tgz", "fileCount": 4, "integrity": "sha512-L1RwE7c/hFj531mMpcboTIwzcbwXvWWXxWAFCeFA53J93kRIM80sp7Zv5U4+4B1VZVDEvBymqBw2VUvwBTHyaQ==", "signatures": [{"sig": "MEUCICquJV/GsrCg716SwIBhkOwxf3OOqR7ko15xu2UbiPgBAiEA3dmIBkWI7WB36FYf/zDYMHB9qRWg82oa3qSNrYApnYM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.50562a9", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2132169}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.48957c5": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.48957c5", "dist": {"shasum": "7da8adae52c706b0d8fa139802dc398d7e28fd3c", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.48957c5.tgz", "fileCount": 4, "integrity": "sha512-MdSw2/tbu+lhM3x6ulqdo3ymAWRYGVSl2C1mu0AWfdLyqhsSqWcjUv+y6f9//8OteGfHt71WUz9VFgDqMcDLKA==", "signatures": [{"sig": "MEYCIQDRzBJw7ypgNQXlIpxNAjR/2MIzCNb7XBg7yePaPNtuqgIhAKWoxG89+0kBN9bb3L65eOA/snvVEwA84AkXubbyUjJS", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.48957c5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2132169}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.6b2b262": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.6b2b262", "dist": {"shasum": "161c909508855f3e30c589e3c6b3fd500bea557c", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.6b2b262.tgz", "fileCount": 4, "integrity": "sha512-pTMSxECLiPX6vXaDgeBRy7PAbJHBSln6sdKIqtxxn58H9lAg7nFxdUNrK1Pobsa1rElV7RcSRe+Px5Y7bcNdig==", "signatures": [{"sig": "MEUCIF4gt8UmdawQAlSIeCEZoRhaw7Dcd2oTzgfJrj2ix0riAiEAylt78y5jZNzXDC1rvKBDJIS70/qzmX0EPmlymFT8O/c=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.6b2b262", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2132169}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.1a88518": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.1a88518", "dist": {"shasum": "3e5a2fd41633e90009f9f00565efb8437dfedfc2", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.1a88518.tgz", "fileCount": 4, "integrity": "sha512-2P6myRliO07g4PFQg8ze5hXypiPZ8YAVzTly7rDwakGxgQUB9DNGhWWTvzNhMNqAYZ5OMGrq9pF4rgkcxj3iqg==", "signatures": [{"sig": "MEUCIFfqX22+xCDdNpUft3ynWcriaQkvPQTXdElZ6Kdy+BJQAiEAjTHCCoVMp5uA3udNw36FZc7sWWZXeUQOsfjxVfFdixU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.1a88518", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2132169}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.4489493": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.4489493", "dist": {"shasum": "b1a3a6c1f7a0a07d8685d7f22161c46b045cc595", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.4489493.tgz", "fileCount": 4, "integrity": "sha512-oLbBxagv5oTWEj7ylthBfvqgO321kRZEAK6lTDoLIF87NuIVwketXyAFDZGwJQkeuu93u0lKDKLqmZTVGL4d9g==", "signatures": [{"sig": "MEUCIQD/hp5z+43nIgOiimYPPZPq8F6uMnk852whEYRkthBPsgIgCLneNDo1NloaIgigHuUjPLu9tMZ19ZPa7sBf4B72R3k=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.4489493", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2132169}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.d6d913e": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.d6d913e", "dist": {"shasum": "84708a413a4d85556a0d79b8b708c171045bb342", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.d6d913e.tgz", "fileCount": 4, "integrity": "sha512-WpVIsgPtwc+tTU1RyEzLWw6VnSox2iSleFmhzX1QlqAtNXO1RKzvnxokQodYvw66Il2SGsbz3Cw5FYNrXkF+zg==", "signatures": [{"sig": "MEQCICpLKjWBionbmOcS0LlevUOegiX0tzUN2XEkNC+DFFKhAiAkUsbNgvIIWoZxLaNob1T70nqQzcbKzgsa0qMAFURcFw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.d6d913e", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2132169}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.1d2d50e": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.1d2d50e", "dist": {"shasum": "d85a94ace9113b3718e05587afd867cebb7efec2", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.1d2d50e.tgz", "fileCount": 4, "integrity": "sha512-A/46aHRLCgD1thWlpQG9lmFHIqmeWfkq0LtE+QHjBiaUkypWUnCSzqpbgZ3mcHBYMIfN+QDkpq6XkCUeCxK4kw==", "signatures": [{"sig": "MEUCIQCzGGAIO4lpPAdi9n6HLgkWejd9t8ZWye+YmEiSw+xVlwIgIQdONFRWruUzcNpzSoVL2ruk3ugJkkta1IhMAu8o0fg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.1d2d50e", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2132169}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.ebfde31": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.ebfde31", "dist": {"shasum": "3b03d19d8bc9f20e49a0494e23a9ca488d41d96b", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.ebfde31.tgz", "fileCount": 4, "integrity": "sha512-4fRSFv/DOIoqEzdN9dgS19Aqmdt1CbaSjfXVg1gEofy+9hO/uj59hldcGzh8sfWpSpaGEGeBELpVP560u62gAw==", "signatures": [{"sig": "MEYCIQDBDCbN8Yhmtn0HWo/mKtglIFUV+tsCugy8SPzx+/iOagIhAODFyMN3L3RUMVWZUUzkN2EkYcwbg3Pb4cyM5S4Thq1g", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.ebfde31", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2132169}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.d7c8116": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.d7c8116", "dist": {"shasum": "93d0d064eb770304d2e97f3bbd2a2537f276b8fa", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.d7c8116.tgz", "fileCount": 4, "integrity": "sha512-fJJ0eqDLRbSzKSGqfNh5HRnpo3cqSPTrmRblGcGU1RKjyRJiZHTNeaBCX0sFsdo8AEh2BSRgUqWwKvdYMJBk0g==", "signatures": [{"sig": "MEYCIQDEM7XR+3HkfzDdNIaZiea16VtOiFHKRuGZ2qxd/0DrDgIhALOz626Wy0DMo3ZOwFtyBH8zk910bJO1p+486ulcBnwC", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.d7c8116", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2539721}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.1564bf0": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.1564bf0", "dist": {"shasum": "384988131f4c2afc535a69202ae8b253ad06c57e", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.1564bf0.tgz", "fileCount": 4, "integrity": "sha512-ooJ6pz1/kJazrYfu65xBt32lRpC8qcKMHN39wCQHlqhlIMHzB7II3iCskWualuM4h2bpFVRIl8b9QdYraqQcbQ==", "signatures": [{"sig": "MEYCIQCch/FdDBOTHZ+naW/RkOcTRGSFK/DFXeXxBbFvKJ9KaQIhAOKu6sFylyJlwmUQCSR7ZrrQeNqrf3o9xQUZoUdX4KMe", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.1564bf0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2539721}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.cec7f05": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.cec7f05", "dist": {"shasum": "247724ef517cf52ffea67f07af38c6902b212131", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.cec7f05.tgz", "fileCount": 4, "integrity": "sha512-ght3UokWTLqcL4qguttiJPiWXYOZrd8su/MqDqrx2Z8pyP4/O4zH7LV3a3uVHnb5mgTpgq+Ch2UcT+3+O5fyXg==", "signatures": [{"sig": "MEUCIQCtwyAdr6eF4u+VYbPtoV15S5OppAzJxISbiACwc6HGdwIgDP7G9CokirEe/oce4B1EekM0Q26GR7Rxs3qWqMJH4wc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.cec7f05", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2539721}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.d698c10": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.d698c10", "dist": {"shasum": "0b914e482363b6fc6359c94acbd85441bdb5b665", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.d698c10.tgz", "fileCount": 4, "integrity": "sha512-73oltcN697eUiKd2vvFNsbI2ul7MJLRth46sRjjgodwficd9CKBbwVX5xI0ERnNwpBTYesTMZZ9NEmxijHtVtg==", "signatures": [{"sig": "MEUCIQC58XXOD2X8LxI3EB1HM1tackJcAjklR4UYvxJMN9tGmwIgZJNwJ20D4PO8VmlcA305Mpx4mB38CH15mo3iv2luSAY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.d698c10", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2539721}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.f369e22": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.f369e22", "dist": {"shasum": "4e7810770365faad803182b00bdd4be951ce9af4", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.f369e22.tgz", "fileCount": 4, "integrity": "sha512-PcQI0sVunNau6DoGETtRth5paxrSntH2Zk3C90dlycxpRDMV6UhUNBHbvuGfsvG0//pd4Yn24RlDy396LpGvIw==", "signatures": [{"sig": "MEQCIHe1sykL5jKVux7Y3svHiss9BSFNzSo3EoQIbA2UBK1aAiAKfk9tzez54vSd/UNEBpyR1aDEfLJij3O2o8De5RcJag==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.f369e22", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2541769}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.503bad4": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.503bad4", "dist": {"shasum": "8b6a52a66d298c80eed2e37ac96285511c2a92ed", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.503bad4.tgz", "fileCount": 4, "integrity": "sha512-UMjc3PWSUIyqQ6PG/WFl4ohuMHkFkdNBJEXLMeiHmUTFH8cW4aIVF8mdnhqC6JyZ49YmqhWfSMHsjBTR+/Qaeg==", "signatures": [{"sig": "MEQCIHHuXxxeEOzSuDq9tFsFhDm4Phm0L2K2HuuQIRdIblZzAiB9m+Sa8KIMCeY5Zx+zFpgYkMCmoe388kA5T+oBEJzt1w==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.503bad4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2541769}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.a1acaee": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.a1acaee", "dist": {"shasum": "5933df82add3fc5ceef3f11e31e25663783ebfd7", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.a1acaee.tgz", "fileCount": 4, "integrity": "sha512-Z6OX4xIClBlqYErKcIxD123CVKcHfRNgueMGdV6dI+B2zOFEEEygAZahhUcXhcaorJR9VqIIUxvLrKF0uRV+tA==", "signatures": [{"sig": "MEUCIQCTrn6LCokIeNO9SEiGnoDJ3f4iau6xZ+0YFByJGlyUaQIgRq+0/UmD8u8SGkcnXieMNYXxk8830yjNGm93fVLIXi0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.a1acaee", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2541769}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.3f313b4": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.3f313b4", "dist": {"shasum": "85f3a0cc1f4a67a6f4175c271e527ffa63bf54e4", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.3f313b4.tgz", "fileCount": 4, "integrity": "sha512-m1FbiV4LfRXF+2M1FqPO3rSd9ChQ6ZwD5hTy/zJiDJIHI0gwNMOFS2HwVD5r9ZKN/b7ZeGdTO9Fu3tA0V/1u0Q==", "signatures": [{"sig": "MEUCIQDacBYZWgfUla3a3pmukHtfMnEQdJHPmxUvfQmWHiNWowIgc2rmPrkEOxrzd6hSODG7Z/35jugF0Ee+2IN3c/epw9I=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.3f313b4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2541769}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.ca7b10e": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.ca7b10e", "dist": {"shasum": "bed8ac9279c28b5faee001deed7e026c4a833bd7", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.ca7b10e.tgz", "fileCount": 4, "integrity": "sha512-9a8Is2Zhf2c6WsqHQM/qbSjdEgNKkoq0quYMv+qny2c5O1la+5YomIwxyN6BhnQUUjl6O+vqD51H0JpMh3aqtA==", "signatures": [{"sig": "MEUCIHjwKsMrrvbYeM6BmhnOTCbuEYxTIZCOCgGBN49lduKUAiEA6xoFDjvjjU7sPGbk6Oa6w8Zu8kkob5HCrqXP2VbxXvk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.ca7b10e", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2541769}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.40a76e3": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.40a76e3", "dist": {"shasum": "7fcaca64479b58b6db3716d17efb6a83fe8922a0", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.40a76e3.tgz", "fileCount": 4, "integrity": "sha512-jQqFWdXbjzf1Zo9gyUEWcsev6dPUJTR46Ob6/1Y6wYnlw4wDHel13vc0WUgMkUL6BicCDRzVOO21NPI6qxpyIA==", "signatures": [{"sig": "MEQCIEtOGj9WxC4nB7sGgZsm6zVa+HrZ+nnI3h4Et7tzvEKZAiBzbA7pEdEgPZYb3Dt/h8wRrFaU2t2jl/sWGXUggX15EQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.40a76e3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2541769}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.4c57d9f": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.4c57d9f", "dist": {"shasum": "ec35e278b624c9d5606d16d50f28a0e52765253d", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.4c57d9f.tgz", "fileCount": 4, "integrity": "sha512-g53djal7+0M873k2bNTwGoZ8Orc7Nx9s2TJOkPoKFC15BI7uyHv/x2D4IvfU2OnWIdC5Tj4bRg70OSdX8sQMtg==", "signatures": [{"sig": "MEUCICST9JXp3UXCKdRnl5sSyVTzP0/Lon8DIMquSsIRW96nAiEA/MWfHPoTMyToBq93zbj+H2e+JjEHg9SpQSZ2J+WFShU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.4c57d9f", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2541769}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.a3316f2": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.a3316f2", "dist": {"shasum": "1111f69b4ba23c837330120c85a760d2418e5993", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.a3316f2.tgz", "fileCount": 4, "integrity": "sha512-j+49c8vLwUgRwBo5Tst5YCbWG391ihEHHwD4LMJBSE0reFnTJCyw6w/i293QUahMUUpyZ9kGn3Y1922X4jVEkQ==", "signatures": [{"sig": "MEUCIAvbQ//q7A6eH31Mk8RE0IkhL7o/am3hrhpjFhz6qT1XAiEAh6yYxJZaPJbIjjPthh19dc9Emb+pny7VI0DvA9lPho4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.a3316f2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2541769}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.250c843": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.250c843", "dist": {"shasum": "dd6612686d6aeb1c234f9efb27a8bf89c510314b", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.250c843.tgz", "fileCount": 4, "integrity": "sha512-m5Yp0andR5TDEvYSDau3fb6xtUEJlJqBbbT/agDYQJ3hWTbNA69r+3Bc45FMBE868TCwCAwksHgFZY3KM0oilA==", "signatures": [{"sig": "MEUCIQDkwXhO2dntAmMwYDZyR4v4q/AKIJWzwVbCz9LSsIV6LAIgbivzRQQzmFiQZN3Ix/FCoM+Jh2mLHYgbKWoiEe8gt88=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.250c843", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2541769}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.508746b": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.508746b", "dist": {"shasum": "498cb2576cca96fbe2abc9246fc9f5534c588ca4", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.508746b.tgz", "fileCount": 4, "integrity": "sha512-A<PERSON>b4nz32dA9bMhgNhnaxlsEG2eqwmUUOTn9WphYO8C54HKJW3gCu1DLstHz1hXWkWxGLGY5uSxear2tquzZ+9A==", "signatures": [{"sig": "MEUCIGbeaALnhHA+vwoFmtwvwof2qtClNbyW33GY+IcQYL+sAiEA6DhkpDG8urp/poid1DBie7QL2jGpi5FifXBzPQmwwKE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.508746b", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2541769}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.1aab04c": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.1aab04c", "dist": {"shasum": "28f10f38768976a39ad6c9c87af63372330474f4", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.1aab04c.tgz", "fileCount": 4, "integrity": "sha512-g2KTnJVi5CupiVa66dUAqElspKkqTk7sfZf4iSj52zyKh5y7r4IuK7137OPH+xJFTmancnw1yq69BPsdO/VD3w==", "signatures": [{"sig": "MEUCIQCWV6ZV16cwm+mFW9mbyR9MgaykMNB6/cG4BEY7yJQZEAIgeo8XxYw7eHburdhmj1FlfURYCL0wpQIlrZDFpRXU4wg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.1aab04c", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2541769}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.498b06f": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.498b06f", "dist": {"shasum": "867001d2648a8baa378fb04d18bf0f97560c436e", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.498b06f.tgz", "fileCount": 4, "integrity": "sha512-XRU5XOEJ37S2JfXKnzlkxqjPGHHysf9LAkdRTNeBQODXjKpUctrLS1YUSBGfQjEQjhZi667be43CnYLG26nGOg==", "signatures": [{"sig": "MEYCIQDAR354Z50ofshxrrt0nATMqBUOPLYO9MEa2UfvHU4aCAIhANuCIb7Ac7wsP8vf1ymTXnKsXIbA3yV7+SMgm8uipA/r", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.498b06f", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2541769}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "4.0.15": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "4.0.15", "dist": {"shasum": "1edd70f0fec7e0087379664e7cf4a7a45bead3dd", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-4.0.15.tgz", "fileCount": 4, "integrity": "sha512-7QtSSJwYZ7ZK1phVgcNZpuf7c7gaCj8Wb0xjliligT5qCGCp79OV2n3SJummVZdw4fbTNKUOYMO7m1GinppZyA==", "signatures": [{"sig": "MEQCIFOPDuw/jRaadBD+U2dq/9FBQOckaPZ4QG9TjoYwODlLAiABkPtkLSyNpLyxML1kwjOyTt7XDBixv7EGtH3KP2uVfg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@4.0.15", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2541753}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.91c0d56": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.91c0d56", "dist": {"shasum": "03f9edf3a2a277dae4dd44360b233a48fc5768ac", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.91c0d56.tgz", "fileCount": 4, "integrity": "sha512-txqP8IMkD0JRqj2UYBE1keuJ1Bp6qg8LUdsHXTGC20Y+XmYPbs5y9JHUmNRlOJxa4wtgikLGCwg6ZS44a7ZmSw==", "signatures": [{"sig": "MEYCIQCs6v7KFzN129/xaLSaX/e9UhsupaLxyR4ipt16d8ZmygIhAPolOV+KfHex9gNjDjEMRNAhsDeGBeJvc+P/TdPiy/mo", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.91c0d56", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2541769}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.5426baf": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.5426baf", "dist": {"shasum": "0599622ed6412a174bfea0a686cdb85cabb534f9", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.5426baf.tgz", "fileCount": 4, "integrity": "sha512-sGD7rWyz6EYE/B37JzFIp6+VcMvsmR8fNZyPU97Ow78+897c4d+U+AfT43X2cl/2JBoTzaZ3RFuXvfiZkD8YJA==", "signatures": [{"sig": "MEUCIQDSs6JiWy1JbjGM2nmMdpGne99Ct68E2l5GB2yQlnv6MAIgE8Ls+C4djcfYPCeQhefM1lHlkffvV3fbVqGOUgBEPq4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.5426baf", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2538697}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.1c481b8": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.1c481b8", "dist": {"shasum": "e5e512ee56df55287c7666c496674ed16aa08c48", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.1c481b8.tgz", "fileCount": 4, "integrity": "sha512-tYnXtYPaBCegCsDp0uiqKYe3UDICJLwoCP8/mRBzIcOjzJCY0brUTBAVDhRKgnAttfFhw/K0d9ESdH1+f/+Tbw==", "signatures": [{"sig": "MEYCIQDusG/wwg/l8rPPE3WFabLG5XrRqviBlMcrUleAMyisqgIhAN+DJPLESysFB4wWN28K+xXmDQreXsQXylKS/qb1EUpT", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.1c481b8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2538697}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.42f68bb": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.42f68bb", "dist": {"shasum": "d5f3a37f8e4a13c1379307961ba4f94d8aa58935", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.42f68bb.tgz", "fileCount": 4, "integrity": "sha512-+FADsbH4CujJGFa2rP+dJHjaIlDdrpQpHgJJxTqPC+o2E1meYvnAxA/bhe7kB9EfTO9lP4XgI9OWHC0BfFBLdg==", "signatures": [{"sig": "MEUCICSIIdH4C/KXTZf/t+R71SLfY7XF53YJhPttGW47OYE0AiEAkJbBDpxL1PJ3qxt7vDkPH0duWxKj5YUZ89gcmlLE3s8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.42f68bb", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2538697}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.fac8f7d": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.fac8f7d", "dist": {"shasum": "b66b74245f9a9cdfeeabdc71394739c5f4018dd4", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.fac8f7d.tgz", "fileCount": 4, "integrity": "sha512-4b0Uo2U/wfWOy4JF0ZfnEdmfPQluodRXcYq80n3iCcrnPdxkCySWUBnzy1TT5HboHgM5EKFxBZwKUNFxoJ9RYA==", "signatures": [{"sig": "MEYCIQDNkMZBRlnpt0MXOxiJTthk4P47ZIpArirkFjtLV1nV2gIhAMZ1kUOO5YQiCkTUT7dvkg9C5l2KESha7yJ4ZMIpkoBP", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.fac8f7d", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2538697}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.711b9cd": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.711b9cd", "dist": {"shasum": "40c7051f53eb675a77a870e5cc7a470a62aaef66", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.711b9cd.tgz", "fileCount": 4, "integrity": "sha512-K6+7TFbOfveiUzC2YbV46gGtqQR2wh3BDwiecH0UWj5+I5pnTGJR5hL2SOyLHyWivk9kBcntHmFHjkNKQIUIVg==", "signatures": [{"sig": "MEQCIGmZGCX/D36iRP4kjymYDC8OJ4xlXCujclsa3yV30esXAiB4F07Y3ciQAcv/T1/dhvJvSPNi5AYsM1r8wqLuGwSjhQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.711b9cd", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2802889}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.baa016a": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.baa016a", "dist": {"shasum": "34f6ded5b101d332f882a09c421a538cd9ba485a", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.baa016a.tgz", "fileCount": 4, "integrity": "sha512-nk2syd6FhQCf+tDtaKDUwAk6ra+0Ov+NlOpK442q/+w3nXcPMCZ8ggXYwJ6olUA0F9nDm3QiyxV5/WSDdfjYcQ==", "signatures": [{"sig": "MEUCIFo+mX9peV1ztjy6W/ZRCiD48c5uLIkjJvcIoNgdffaxAiEAnDB0KzZB4oouTh6mkJOWdCWbtMPFLgbJ7sKj8Wpj0BM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.baa016a", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2802889}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.e8715d0": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.e8715d0", "dist": {"shasum": "fd23da91c7cea3f28366fc17513630308a845d2c", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.e8715d0.tgz", "fileCount": 4, "integrity": "sha512-UER+I0WJHHAms39YaabEwMoXZxvmwVwzuRWRMKuJLVi66axiQrko9lhBfvAqxaZkWg6l2IWL+hFdFPe7XEDoxg==", "signatures": [{"sig": "MEUCIFcjGAMC7Nbmhhhvk+FqKOlBEHjXhisXixIa7mkEDaC0AiEA9ZeaWqFO/FO9MtDCd8p5EmCIgghDro+fgqaBZTIuY2U=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.e8715d0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2802889}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.bd501e8": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.bd501e8", "dist": {"shasum": "d29e701f185b0177b9968dc3d48dce37f2b43546", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.bd501e8.tgz", "fileCount": 4, "integrity": "sha512-Tu0dhDJztrToFOtTHYBVvfMvE20u30ONgj25zS2/BXZexjNM99ki+ne0E3r9WJ/jTpn1zBCCTma1ZfazcuKoig==", "signatures": [{"sig": "MEUCIQCbhCgOLE9Ce4Ae/KTP4iHPdNeiKHIugD+mlCe6jhDrPgIgSl1f6oC83ZHC9Qt87/9mJ58u8W3ASosHleyD2edEiFU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.bd501e8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2802889}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.1c50b5c": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.1c50b5c", "dist": {"shasum": "56262d3f746ed729b7b80e87d45d26aebc0c094c", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.1c50b5c.tgz", "fileCount": 4, "integrity": "sha512-DQMNAje8izjJKiZU3ryvyf85MIAPKZxXSH2j4uSBglzxjz3Wg2x+i/NRaBB8cxSMgmJ4VWN3h4DpDrhoslkXqw==", "signatures": [{"sig": "MEUCIAoeviwPadM1rWzgaqX0sTmSmvQNbfliAc4OB2gvrtcIAiEA+XSuaDmJO/pRam2hb6a2jxem1tKu0RYPqzkJpKqVzVw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.1c50b5c", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2802889}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "4.0.16": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "4.0.16", "dist": {"shasum": "de183bc9767d783273fdf7a7c75f212616ceef78", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-4.0.16.tgz", "fileCount": 4, "integrity": "sha512-Pj9eaAtXYH7NrvVx8Jx0U/sEaNpcIbb8d+2WnC8a+xL0LfIXWsu4AyeRUeTeb8Ty4fTGhKSJTohdXj1iSdN9WQ==", "signatures": [{"sig": "MEUCIQDW6Cy5CQh6bbdRk8NQlRvNXMHptZlzdRKlRkw1SFntugIgU7a8/b46xfOES+r01/DZYb9E3hCLA9cMyAvaCSE9RnQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@4.0.16", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2802873}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.c45616f": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.c45616f", "dist": {"shasum": "6aa6d24fd7da425cfb6b151d558618c0c8ed17d2", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.c45616f.tgz", "fileCount": 4, "integrity": "sha512-Ogj9KBmjI/wWoRrR8prNWjMiGl5STQjOnpEGqsxHdHnRLT1XorH7nU51LxQhq8/uFJVlaXc4JdMnsezwkl2cDw==", "signatures": [{"sig": "MEUCIQD5TaT8Dmd6wWsQWRiUlkuypIWv8Y0+fRiQC+vTURqpcQIgWBPemZ2Or60tLHI8BmjikzMvIPVDDONzIJ5Dh+zWIwI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.c45616f", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2851017}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.1ef9775": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.1ef9775", "dist": {"shasum": "6e6ec614f3f055f2b568c75bb9c8bde7b94acb12", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.1ef9775.tgz", "fileCount": 4, "integrity": "sha512-uaOyaDiM7fWQ2IzVTU69hz7EAvS37Khq24vRCOsVi53SgKBqOb89ZUskBVR+6ylK3g1ONJcZuHoO3nFQN7qW/w==", "signatures": [{"sig": "MEUCIQCF/RUA75yXC/XbcFqm60IL+SKlyV2qM1HlclWKIjJTqgIgSMGkS99EApIOVD9PmFDw5hrm5Rf4hY9hH3qZnOCgu1s=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.1ef9775", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2851017}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.3dcd615": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.3dcd615", "dist": {"shasum": "89ab67118970f0f5195be0050224a475e2a532c3", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.3dcd615.tgz", "fileCount": 4, "integrity": "sha512-lEh09XhTL4fp0QE5TqhKrImn4lVcMaaClVmjdJTv7hjb5Arro0GLaUqtRZ80FZ+8CVDBqGIcE3WYWWg+OfNnDg==", "signatures": [{"sig": "MEUCIQDISIxse/64gd9C5nABs13WHa4W1tnbVTwCSPL7sazi8wIgZ6UUMlGSo9ddHmGCiBEiuMDg3FkNV6IiJGORmZzs2GA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.3dcd615", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2601673}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "4.0.17": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "4.0.17", "dist": {"shasum": "f30694a6bea7f84c4b367b2af81085cb3bf569bc", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-4.0.17.tgz", "fileCount": 4, "integrity": "sha512-AkBO8efP2/7wkEXkNlXzRD4f/7WerqKHlc6PWb5v0jGbbm22DFBLbIM19IJQ3b+tNewQZa+WnPOaGm0SmwMNjw==", "signatures": [{"sig": "MEUCIQDmFnqpuKVjw3SJR+VZHtmNwrGfszVYZqo+MylowL//+AIgAKFCwaDsD3AESztoNpxivFhREcUqdUpGlNsrvFIPQEo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@4.0.17", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2555577}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.18365ff": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.18365ff", "dist": {"shasum": "81baaed5fb15fa707b34da193513dbbb5e6e6f4d", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.18365ff.tgz", "fileCount": 4, "integrity": "sha512-A0kmXKJqw5J5bPpLl2ofQwDBWG4Uni4QDjdUUKaFyvVNXDag1A6knXdzfZJ17mlmuSexxaH6l0RZ4Un1ZlO2hw==", "signatures": [{"sig": "MEYCIQDcWv2Q0YE/SeRzXr1VeKNvimci0j7cdFMNvTAWMDjzEAIhAJnvMLHh6BucITV3aj8NE3qNLnpjI/asaWME4Z8Yc7b+", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.18365ff", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2601673}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.b0c48c3": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.b0c48c3", "dist": {"shasum": "548cefc3dc6f013036cf8f1dab362523edb9ebd9", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.b0c48c3.tgz", "fileCount": 4, "integrity": "sha512-hCgPlcZKm5aLdSCYrUqWiBlS5MvA19UYItFpnE3bMdyC4mNeh1czxFvZzKq6WI17jMXFETtTby3FH578V9eNSg==", "signatures": [{"sig": "MEYCIQCVE3ucE8nFHy7d/b6np2Ah21tI6j5yva966l3sl0GWmQIhAJXW8lMFdAvr/0Uge8zAfEgTJ8AUoZAQo5OLBa/i3zYA", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.b0c48c3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2602697}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.224ce0b": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.224ce0b", "dist": {"shasum": "4d51f0d3834acf95e1eb77b9ae3d8761d59da68a", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.224ce0b.tgz", "fileCount": 4, "integrity": "sha512-Fbqe4AfwSkr2Y6J1zFUYHx95lNV2aapaZJS7VC1QOxYurwAT3LH7DCigo4xK9WxUjKkhcNZig8hh0jTFVwkrmQ==", "signatures": [{"sig": "MEUCIAk+vGDYpasoezvso28S0PfWitZzAhXsPxQaZPHgfOrrAiEA8FaiBhUJ0XkV0htccs7vWOtjQW/4F55yfQRXhAjTKu4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.224ce0b", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2602697}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.eecb6f7": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.eecb6f7", "dist": {"shasum": "af9605256ce556c3c1b7201c7b6ed99c62be5083", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.eecb6f7.tgz", "fileCount": 4, "integrity": "sha512-mvsc4rTZPKeZBWiBcffCOcDeZoeS9FSAzLiv3RKYTuKM/rx/0AADs5+hLv5ViiuQmeob4h9VFEWefKTR6B2WnQ==", "signatures": [{"sig": "MEYCIQCJttRGRCGrPfSNWViYC4PKNe7UjvC+EwJJCtFbX3bfeQIhANxqUHHDU2r7UZFEaVQKPplEHVutG+qtRDSTfFa845PA", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.eecb6f7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2601673}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.3e53e25": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.3e53e25", "dist": {"shasum": "1ea4e2670966fb1da423c3e5419430f4875638c5", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.3e53e25.tgz", "fileCount": 4, "integrity": "sha512-inPeMWm9c7zaAWVmMHk8j3a9N791ymekuRqSrbTP99KlE88vF7B2LRiKu7QMk9pVzEvegbqs44/ta7wpH2R4Fg==", "signatures": [{"sig": "MEUCIExb7RqrcwJ2zHXs0NIThPyYfLDECirQSdGhO6QAhnFIAiEAsnvxZrGB5L9P+xjACjyogSJtYZV3l3qfqwHPC1OzJes=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.3e53e25", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2601673}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.ec2f3bb": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.ec2f3bb", "dist": {"shasum": "73f34d33640b42349b1f442be5a56c38ea1cfe96", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.ec2f3bb.tgz", "fileCount": 4, "integrity": "sha512-W5hAdRBBpfDpDH+/Hn8pqC5LlciyJgaQEUwaQXnM7Xk5FHbafebH7idWLbumANOep6HjQ9wL3xISfjwAgWcocw==", "signatures": [{"sig": "MEQCIDzCh2HXmeqieS5kFzQSWAQeVUbCGfg2hqX04viiw1XHAiBqaIGaXCWkErscsizh8m+ey1nqmEHRvo0uVCsHvmvNFw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.ec2f3bb", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2601673}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.c3ae9d1": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.c3ae9d1", "dist": {"shasum": "b0ad6a7a21978783d19858de8515cc841812693a", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.c3ae9d1.tgz", "fileCount": 4, "integrity": "sha512-39syelBLuwGxSUWSCETrQr2pKTuXCaFLd2ogCFmuZCvMUBMRqMkYQJcxdpJX/+BUennhugMwj6ijkSM3R+5P1g==", "signatures": [{"sig": "MEQCIBxnC5ss19HE6oAq7NzbeQ/Is1flSLYFj8NZZZiMPLY3AiAidd5/eiCgxNjSUdt0wC60PkmUjReQt29y1KpCeV5lAQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.c3ae9d1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2601673}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.601f369": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.601f369", "dist": {"shasum": "cbdc187f7be386c431387b942bdb0afc68ee4667", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.601f369.tgz", "fileCount": 4, "integrity": "sha512-WQV6ekGKic2eaZAbRfTWgWddPvVjmaPX4wQpZ1oi/I3GhVr4XQRNc8ZCzpHjFsa5bwWgNbNNUXN/KD92v4q6iQ==", "signatures": [{"sig": "MEUCIHZJs5tgpnGR697RMDZhdz46/NrrSahr3gocLHDM27HhAiEA1AQzfaudC7Ppt5ecVnB9t+kY7XbHX9lhqnkx7vJU/Sk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.601f369", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2604233}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.9cceeaa": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.9cceeaa", "dist": {"shasum": "03397b45075e93697d566730f3ffa6f2d760e4f9", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.9cceeaa.tgz", "fileCount": 4, "integrity": "sha512-Iqq3Y3hKqzUUwQ2wY7RMkn24z0H2mBR9/VQk/XH0QC6l7X79fhKZDNw9GPRZXyqt2jMXw5t7o4RIW2nZ+IgHTQ==", "signatures": [{"sig": "MEUCIQDyJLJYm3aDkXe2nUUbYibLajK43iSL586JTFKnU6bBwwIgIPpRwIdJCkGNg6AagNl+ePW7J5iWAIvvOLdQLomrHo4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.9cceeaa", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2604233}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.1b6230f": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.1b6230f", "dist": {"shasum": "455151bdf6b59b0238852665ec6a5e79d1c356dc", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.1b6230f.tgz", "fileCount": 4, "integrity": "sha512-EOAhm/m4Zk9MNKUq5ODSomENeJFq761j+Co7EhhT736yfqTGK+3dPNNOARi1B2JMiV4Bkn6ZSM4X0T8Xg1FGdA==", "signatures": [{"sig": "MEUCIBKDxb7tislieXBAFr21LKVG4ToFv0zNJ/GUrDaV2uHfAiEAloW5ZHWlVte+OGPdqb/XgUjqFS7BrLt+U1+3nchZZmo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.1b6230f", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2604233}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.ab868c6": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.ab868c6", "dist": {"shasum": "816d218076af030dd51cc363f88de31b3a492af8", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.ab868c6.tgz", "fileCount": 4, "integrity": "sha512-EI0Zy6wVRoouzoh5f5Z32BtQZiiBpyMYAQh6yKAIyX9tOjxr1fJbMNoJ3m1obzvGnxzpZ9L6lyOlbmS5/osFIQ==", "signatures": [{"sig": "MEUCIQCabU/RECzwjUfZi2JWQqbV3a6TfwGe0n+Oiy5cVsaVDwIgfBjQn2SoSyWWtHPJkPOgQRbFMpGq7m3UhLkkuHpeArs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.ab868c6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2604233}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.3412a96": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.3412a96", "dist": {"shasum": "3e85b23bf7d78d84636323736a57b7fa3b7b5dab", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.3412a96.tgz", "fileCount": 4, "integrity": "sha512-BgshPgySHZg0dZWsmHYYr86DzWdOemkSt4OPra2oL1HhYzevvmsW83lwPK2wvSbXO0w9J5HC+WW54w/WgOQwzA==", "signatures": [{"sig": "MEUCIEYIehmOi93O8HFMkeSoNgXe68JrK5ZpBrfrp3h3uMBGAiEAstOApJ0OSGRniAXEw1zGr32afekiTt0w6jDRRFN3Jdw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.3412a96", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2604233}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.2af7c57": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.2af7c57", "dist": {"shasum": "d2c842f45cbffcfa3fff5bf5ad973124bf9af5f3", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.2af7c57.tgz", "fileCount": 4, "integrity": "sha512-SmTfqILkFks/lK9BCkmhF1G3qxVgg+lWft/WLDJthTV1SN9Lg5SSLgHRKDYC5Jc9AM5+mND4ekMEFQD/6o5YqA==", "signatures": [{"sig": "MEQCIAx7K9NEvoHIapYspPZ5zVc7Kue4UJAv8LEJfZy7SfL7AiB0vZcpmQ/CD5fAb/UFRxmbOXyQuunVpoAXd+28PcxEIA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.2af7c57", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2631881}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.5e255de": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.5e255de", "dist": {"shasum": "e1b1aac92dbaded8e2a7d8af85a2e3b66b9fc2e5", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.5e255de.tgz", "fileCount": 4, "integrity": "sha512-PHjCYTrYK57YLO6WdXzq7nonfySmt5yLlwKN5SwkEzwOOohaZvA99vpVtwNGzmNPzRMPAQgo9KzjP0hsHbXqIQ==", "signatures": [{"sig": "MEYCIQD5bUsJ/g70UKp5KBE4zzvzRbPySeIOMQ0jCU0pawW5QgIhAPUUPqSP8hHA07Jrt5+af2jBsy15+UOsmmvEDZyHJ5z7", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.5e255de", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2631881}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.1a68b99": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.1a68b99", "dist": {"shasum": "e1a289d6a542382d5b0e221a95b21ba76a61d7e3", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.1a68b99.tgz", "fileCount": 4, "integrity": "sha512-Q27BrdPCaDWhL+/GOi5ALnmnXmX3+7poA8ujurHPWPDYXTh444+U640VSGt07LtgfJxbZzqjQ70gI2UyOHR6Pg==", "signatures": [{"sig": "MEUCIQCj1Gui2zdNnQy/StCg78NqGTe6lxcDBxaZP8gjVwRwlwIgCReGbuJ4tZ31CuVODrzHkeg8xhbYagTScB8CpqjJhiU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.1a68b99", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2631881}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.f772266": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.f772266", "dist": {"shasum": "8d746d5fe2d8c7733acf65b68d4f7137b06c484b", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.f772266.tgz", "fileCount": 4, "integrity": "sha512-jQnPM4d0AzFC+Ma4vpRB1ju6QI9uoGw2l7mM8UitHDk/qo2OBD99beEt9Ea3Dgw+N696DlAXSiwVJq9klt2a0Q==", "signatures": [{"sig": "MEUCIG5vCTnv4f1ape4LinxS90bLnEmRh3YVITXIE9KioLGrAiEApKxJNykhzjjHkB9hoBLy8cYjzgeyMzDNHu6DQCyIOO8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.f772266", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2631881}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.c32b608": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.c32b608", "dist": {"shasum": "8f1c792c17786ae8354aae34f93d70854f5d880a", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.c32b608.tgz", "fileCount": 4, "integrity": "sha512-zcMQ/YVWMP+5lrq++7+NTkSsB6xc8unKExIyXSIZrC3rj8GSwMx6pFTkYFL0mXIWr22MJWTNIwHUGV6QsA/WaQ==", "signatures": [{"sig": "MEUCIQDmQisByPBIQ8Jq3voPcu33WCte5ZRAu+YboFCh+fPoTAIgWW7tvBwo3e/LtIp8cFw8hXxKwPlrdvsI8VLhY+aBHBk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.c32b608", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2631881}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.eec1bf2": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.eec1bf2", "dist": {"shasum": "131d14709a6d17cb08c49bd13d2330a71fbf256a", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.eec1bf2.tgz", "fileCount": 4, "integrity": "sha512-ybUSDS6s2FEasb7dPXqNcxNvwmTmy9/JzqSUo7B22ENTDCeLPolLHeera2oLVhfnOl+jZFO+g+mweQ4iyDy1SQ==", "signatures": [{"sig": "MEUCIGRYJ3kYq9mlMHau95Un+5NiniL880cnMQvNynkMX5PHAiEA5ZVQJ7ljDjlvIPowCT9fCG5hGt7XXIQfTXp+amMty1I=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.eec1bf2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2631881}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.c7ba564": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.c7ba564", "dist": {"shasum": "d93762e07e4684f212bed0722e5f4e5931851ec0", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.c7ba564.tgz", "fileCount": 4, "integrity": "sha512-iBCZl7xgUiS4qF3JF2aDwk56iQVlt+JV9G06LP22qfWS4dPD50vd0sE4X2zfgtcWpxqvkxkKoGDNdq6rpC/BrA==", "signatures": [{"sig": "MEYCIQDQksY9Ip7z5OyTga91qOJ8ZSNGvKN29JsuEDE0aHjRQAIhAMffy+fIoq8c7x6bXqhTnt+PvA4faiBp+x9rRZAw5hTo", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.c7ba564", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2631881}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.5380109": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.5380109", "dist": {"shasum": "490c3bd44eb3bfb5c4015c81436cba21052606ef", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.5380109.tgz", "fileCount": 4, "integrity": "sha512-ubd5WUbdEprPUxuMGOU0aCcthFooD1mf9oGsDCAti2N43vGHUXbT1B0FsiuJOI+YLozS5w9L9o7Omr66MtsXWw==", "signatures": [{"sig": "MEUCIQDUoLMzzmMwKPUXDIk3O1RQ8YiQjRSYyY3pIuxlBhNDGAIgdZEhhNToRIRx8rPCGAYo2aw1AHB2Dnqx8mxqZ6RAsP4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.5380109", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2634953}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.d54e23d": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.d54e23d", "dist": {"shasum": "654d0d46488f49b7e3c528f92fd1166e2ba12b97", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.d54e23d.tgz", "fileCount": 4, "integrity": "sha512-zFIfinhBQsgLOZPDc3t5BrGPVhOoH3gqaOiGgUfSBsUGwg+Iynx6QCtSF9qIEE71H0vBDwXs8WUifx3Xedh/ww==", "signatures": [{"sig": "MEUCIQC5LS+cGEjCEK1obUm0Ow43CSFF99/+sB3dsAanosqlxQIgRfjhy+w/N0BxYM4bgPW8Vl8yIY+VeKFDe27/jKCotv8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.d54e23d", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2634953}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.80017eb": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.80017eb", "dist": {"shasum": "1747a45ac667bf8c955cb11281e5813acf015a1d", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.80017eb.tgz", "fileCount": 4, "integrity": "sha512-VaIQKLS8Yv4B4NFgb6EWGdPOF4/tNYBMu/o5pgSqcYpYu471KfBowtb1jHt6aXfKw39qjl2oQYbpMprx0Xeweg==", "signatures": [{"sig": "MEQCIGbxny9noTw7ABe97Lz64hwntOP3TabmSI8jRqY5hBkEAiA9gdlFkIOkbn5C3Xt+UUJBqrxqS0EPhM4kl6aCxvyyBw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.80017eb", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2634953}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.156afc6": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.156afc6", "dist": {"shasum": "7cac9e9a80769fcdff91e14301ad100ca306d877", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.156afc6.tgz", "fileCount": 4, "integrity": "sha512-Ov05+DTdUbAapLvh8MPddKB1UhjhdU4+lWnpUIXUZ7RrLrUoYmIZ7CsNiNB6TfcnGsChU64XzobR241QQDu/pg==", "signatures": [{"sig": "MEUCIDiJTr8bfShX9ro66YKLoLchn4woVuRHxjkLdZp9qj1zAiEApqplJsZU2OgyOo+pxBIr2O/deuL7QpALiw82eGEhGwI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.156afc6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2634953}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.f590be4": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.f590be4", "dist": {"shasum": "7cb5402db0d45c505c3a9cdf4de1aa9198b5328e", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.f590be4.tgz", "fileCount": 4, "integrity": "sha512-4wmbFo0JgE8LGxTQEIDmYuX564TFqYNCYZdsznX2ESSqID5ztmVO80Kro9xZhYCdly6ani/UVHSXqpArrKJ3rQ==", "signatures": [{"sig": "MEUCIQCMo2mCzGKVwxuqSxqZI22Xck8Q8QwhljtwMRWrwsxNggIgHTemnHXzgC0Sx3ildfGlH8xdUsfNe//+LDVtu9PKJSM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.f590be4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2634953}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.55d7a65": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.55d7a65", "dist": {"shasum": "c7c5cdfe1911434d87684c39ec2a5e02244d5796", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.55d7a65.tgz", "fileCount": 4, "integrity": "sha512-R5qpp0FwAnQGiR35A0bZFIngWhErHEYOFH1INywLYhYhw6d8cRM0E9OdCflV7X969BpKPyI2V6URioafgDMFZg==", "signatures": [{"sig": "MEUCIQD+Qg8qUto5fL3s5FSu99pBJUuAhPhEvJvDlFnZZ9eDzgIgFWhI+zfpSm++9OAePfbv0qqepDnfBNyUQdEU8JIrnKY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.55d7a65", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2634953}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.9374647": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.9374647", "dist": {"shasum": "a23c905244da1eadbc153c18d8993915343fe093", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.9374647.tgz", "fileCount": 4, "integrity": "sha512-X7sIQCWd+AFOOUKMNVvXLooCd7bp5PRudP3fULuUeAAauPO9BC3d2sQIveDbvHY33mj8nHzg9DGRZzgKFSIZIg==", "signatures": [{"sig": "MEYCIQCLCmur8sEMA3u3xRAyvVtzcLL390Jb1gOkdEIzDBzVzgIhAMrGtuQ1w1QZz3DLsVad3eOB5e29ORfS5Ux4rmAflVU/", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.9374647", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2634953}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.b94720a": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.b94720a", "dist": {"shasum": "ece43dc564a5ab414640b48de08fc34e6abe221b", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.b94720a.tgz", "fileCount": 4, "integrity": "sha512-ecqaXfWAUWv7Qbx+Tg1VnTZoHpykUNcTRiEq4rljguBwYC8zCcD2fssGmy0OQiNz/ueVq3rJ3hWTXeRAhIAX8g==", "signatures": [{"sig": "MEYCIQD3HGomdf7sGZ4c81pM9xyO55BZeTk3oAwMDLgsEZAceQIhANjJT2OuMUiov5Uaa+rM6++KQ0yMJ6e7rNK7aIiPDIlh", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.b94720a", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2634953}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.8f631d0": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.8f631d0", "dist": {"shasum": "72dbbbc37f8dc54384f96581d02927ea584beefc", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.8f631d0.tgz", "fileCount": 4, "integrity": "sha512-tdXCrFKgTDrFYtz+97CcPOAHTVlKiWWyd7sBhhip2xt44wlIqNtdLhesHLnT68RMbYeGzCtRwn8aUn4/4qn9kg==", "signatures": [{"sig": "MEUCIAGqx3/nV4MuviL/TazjkMV563lmwMEX9DgGj2A+SB+0AiEAv42Y1cVB/4OSMNsISKVBA64ROCdFF3gjog7j8xh3Q+U=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.8f631d0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2634953}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "4.1.0": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "4.1.0", "dist": {"shasum": "cd413ea129a37c35eebaa84c721fcdb5ddc26f64", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-4.1.0.tgz", "fileCount": 4, "integrity": "sha512-caXJJ0G6NwGbcoxEYdH3MZYN84C3PldaMdAEPMU6bjJXURQlKdSlQ/Ecis7/nSgBkMkicZyhqWmb36Tw/BFSIw==", "signatures": [{"sig": "MEYCIQC0QVBgmGjAzObNNtgg5gmmgaJiQmYLRWfjoqMb+cqbowIhAIflnYOAGcI0VIbOBiD+8NC35zR8ABy6NjAniY5bN5tk", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@4.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2634936}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.3c937ec": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.3c937ec", "dist": {"shasum": "361db0b010e61dc8e8bc4e66bd6529c04bc540c1", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.3c937ec.tgz", "fileCount": 4, "integrity": "sha512-Jr1v2bQoQsimsKfYnXVwjN1hwdDIwitzLCStY22JY7OtZ1DqokaXMC1k1kZrm4ayrACEeRl+WnPfeZkPpdujQw==", "signatures": [{"sig": "MEYCIQCJvCRbq74lVtBx7x589ev6jxuKCM2j9XdO1QhRZJrI2QIhANFFRAn1PAHLdxqFkvVoIFPJuaTz2LsyYl28fDA9cGaq", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.3c937ec", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2634953}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.a429462": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.a429462", "dist": {"shasum": "5e0316964d6533c4692f379d7278677a716f5f55", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.a429462.tgz", "fileCount": 4, "integrity": "sha512-5BdZFoSwjfI3hLZtByZ/VDU8/tqtLsRkw9VNyf6ev8Q19nnbsROSHjz3No/TEubKzml+8ydpWu3LuokZL+64kQ==", "signatures": [{"sig": "MEUCIQDfiIPQrtcW4d27eqLp9CnAfp2YbzwHoW5yvrdH/pbfbQIgLHBsP2KaaC6MJ4z1kk3oEJlgjaz5uBImwXxVOFVxNQ0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.a429462", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2634953}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.6a0a3ec": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.6a0a3ec", "dist": {"shasum": "7aa4f72d0d14e4bb8451bd40eb6a52be040800f5", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.6a0a3ec.tgz", "fileCount": 4, "integrity": "sha512-VL2YIfYScWxe+yhi3jtRMkmvtu80LXmAlWHC/Vs32Ie3I5uPnZF9v7yzLfvUwjyTVqNMtU+qFW/DTzJ0pgtbsg==", "signatures": [{"sig": "MEUCIDdT6adf4U65fb1THag+PRdbRGSPw4zDqE8jzOksSGcSAiEA+KIDrehMZJB77+ty/9Wxh5fMVe3I0xCaXVpu8XqbGW4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.6a0a3ec", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2634953}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "4.1.1": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "4.1.1", "dist": {"shasum": "b938ac42d05643c37fb588aa23aeea621d864d59", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-4.1.1.tgz", "fileCount": 4, "integrity": "sha512-0KpqsovgHcIzm7eAGzzEZsEs0/nPYXnRBv+aPq/GehpNQuE/NAQu+YgZXIIof+VflDFuyXOEnaFr7T5MZ1INhA==", "signatures": [{"sig": "MEUCIAwqLVeOJ5EEZkCw1kBqXHlSbiWuyj0xDDOqlIZaTDOIAiEAy+YD0iYgF2XQ0oQO3WR/aQ+jegE5qjQoy+QYyryZS94=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@4.1.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2634936}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.4484192": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.4484192", "dist": {"shasum": "af780eafc9e43dbf38f9e5498b1e02712cd20e9c", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.4484192.tgz", "fileCount": 4, "integrity": "sha512-oKMhndNxK1taqw1dTOMlpVP7ylWli3cqiRotZvQF7VgTGWywmtpBVvGBlx9036meiC3erA0RnAAMakGuYGuzhA==", "signatures": [{"sig": "MEUCIF0c1FMUL2IgKR6zLBorXTs/zT7cMedagnRS/3GzeCV0AiEAmeF/S9kI+VAgTIfvz/exBWgQAN55sGeYEaksci7wKpc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.4484192", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2634953}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.80f9578": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.80f9578", "dist": {"shasum": "787b9aa0835a22b16843649d5e854243a71e15dd", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.80f9578.tgz", "fileCount": 4, "integrity": "sha512-1<PERSON><PERSON>uePSVZ5IVCDiGTjGMKSBk44YsqfxnXnNe6+hlIHenowiiZYZAqBAcZg9Roij0JJqkP3GHcLz4xkxOq1IZVw==", "signatures": [{"sig": "MEYCIQC5Tmp0iQJgP2S29mS7qyovmw9LSesjVdRtKOTqFDhX3AIhAI7CWfcoVxgC2+1J6JTDX51QCxu7dnwit3NIjB09DcFZ", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.80f9578", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2634953}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.e5b2b0f": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.e5b2b0f", "dist": {"shasum": "371a6d8c9bfdd3a80a1cac20579c5d02a21031d6", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.e5b2b0f.tgz", "fileCount": 4, "integrity": "sha512-y1yYOe7daDJ4IrIAtcYdnArPMbo2mnvrkR0BUjTIb90aEcqF73JzmyGBSYY5rVzigN8FDDa/u8v2dj+lI1azEg==", "signatures": [{"sig": "MEUCIGw4wKwkpJ5hZ8yJ9lIDvleMX49M75yPSBSjrXtYEpn4AiEA21twxi8B0Rw1QIIVtTjr59HFLIxxeuaQN06KgKb1Smw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.e5b2b0f", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2634953}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.f8b9aa9": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.f8b9aa9", "dist": {"shasum": "9d9712f3e4234b7a2b2bbc82f5a4cc8fdb31726d", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.f8b9aa9.tgz", "fileCount": 4, "integrity": "sha512-FbzuiENG0W4z5aaShOtvfqHyXEfO0Xx0Fs/vhxfZCKSZBWjIC1Nl1cVxy87ljXg/lBhdmThqaZ/uCNpvjO6Z/w==", "signatures": [{"sig": "MEUCIG+QvXVIkCujenteCc/mQCUbBmkpxeiU269V9EJHgMlQAiEA+2bwnePyVFJnLYzvHUmZENYoO3E9YYjSP5tTgmj8iFE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.f8b9aa9", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2634953}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.3e41e9f": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.3e41e9f", "dist": {"shasum": "ab13c77831599a8365879048e167e8171503c34c", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.3e41e9f.tgz", "fileCount": 4, "integrity": "sha512-dHbd0nemdG0sq2eSNL9PWsfwBO7RezfyBWiR3S/fbcLH+uH5WkjYrB906t8IfOO6tvwCwu88lKAZdXXaE2mD7Q==", "signatures": [{"sig": "MEUCIQCym+jDnljOIOtPj8mahT3T6E7vQV0DIi41diel7rPIFgIgVnVWcxmG1COswqdPiMadhqvkkbqFgq4jHXnCWReLs9Y=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.3e41e9f", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2634953}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.e45302b": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.e45302b", "dist": {"shasum": "70c02a49feb455d12a0131080bda34d3d09c0f1b", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.e45302b.tgz", "fileCount": 4, "integrity": "sha512-E8F5OHy6tQ4H7/hm1eUCtndc5yeZhhe7rBVVKvH6NNeYyXKxhlU4fLjG5HrKPAswyD0JDCY3uTGxyz8Qv51xmA==", "signatures": [{"sig": "MEUCIEYWg3+lBMSKnvkEIQDTHg4dmWJXS0/37YkaOsX4VlEOAiEAiJV3SEJSE8qQzZDwf14z+IKhd0VCK4pSuvk6y8GbCig=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.e45302b", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2634953}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.81a676f": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.81a676f", "dist": {"shasum": "1566bb794ad03c038f3401f9522c0586e5caffdb", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.81a676f.tgz", "fileCount": 4, "integrity": "sha512-PvRtFU/SPw9Um5Xz7y6AGZ4cKokrf48Rru9vGRLkSm0hG3UrDiDtD8VnumEGaJSznaJC0BsGSQxgQEjdqTZ83Q==", "signatures": [{"sig": "MEUCIEwlBe4owQznEQ+aIxOP9f7JMgNhcOsoLgLPvMOMGYq4AiEAhFlDCROsGCTZsR/uUxkBTc1yF88gaJYtVoSXFUtIncc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.81a676f", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2634953}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.60b0da9": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.60b0da9", "dist": {"shasum": "dec5764fb5fce06ec154e50e21e8a148a3394ad2", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.60b0da9.tgz", "fileCount": 4, "integrity": "sha512-WXVYO8YT2MUuTP7XroC9XQKf0cGXUbF6cAZBnJ5S7eKYn5QX7Lp1Q2QWt+6GmROEy32e/IMnyzCPRRTKWr3jTQ==", "signatures": [{"sig": "MEYCIQDAqj35tA4PM9Yot8o65pEulREcU7C1cAHNsNeANkwUsAIhAL6z/agCpU4CzIMH0pUtMGDbXL2Kw59utw4w3nLKDl3B", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.60b0da9", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2634953}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.4200a1e": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.4200a1e", "dist": {"shasum": "0da0805c068f847e2908ac7fe4b6958367959380", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.4200a1e.tgz", "fileCount": 4, "integrity": "sha512-tA8v1om/m0S7wipuzur4dXVYbteN1HhHhFtqA9QPr9rNTcQjq0EdZkpZlVL7cQC+SDSiwj2qZhjqZXRxvufa9A==", "signatures": [{"sig": "MEUCIQCJwNfy8u2mEuEIB+9u777esaYbJp1k6R+G/Mt/Py44LQIgLS2KsoG0QESjP7mpuOtNL110Bx8dRydo9OJc7WqmeF4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.4200a1e", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2629833}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.5a9d1f4": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.5a9d1f4", "dist": {"shasum": "0f159b2e2cc919f21fbba27a47652ae0c8644323", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.5a9d1f4.tgz", "fileCount": 4, "integrity": "sha512-PU81eVlDNUHYnYHceNL8NJlW8rg/SqGfjml6WAewDbA8k8wG98TkwF9527HMM0z3BxGte7M6HSw9bhCXIYjYVA==", "signatures": [{"sig": "MEUCIQDgRk+4k6V19hfNsI07UE+uzW/OPc4jcMO5oNpssWIMvgIgHFoMZqSYD4tSjPs62yc314H1Pf2xyCH0d9YL9KPgo9Q=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.5a9d1f4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2629833}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.4c99367": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.4c99367", "dist": {"shasum": "0cf65d7354feee0f4b22ac5a2aeb9a652d577f76", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.4c99367.tgz", "fileCount": 4, "integrity": "sha512-rEkG+NJx4KcxAvdLWEJPHrHsgtC/uff/KO6BbyeEvtdLl8oRpiu8dKRRYL5unCaGIa1mpAu9FwbhGl7BwwgUwg==", "signatures": [{"sig": "MEYCIQDjl78dzo2oKd+lXiVP18riXoeYg8x7WjUdXjKf8IWtxwIhANe5+9+tFAXEo01D5CwljuEndBC/fOVYCQ6pXCsYvQeJ", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.4c99367", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2629833}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.3f434a6": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.3f434a6", "dist": {"shasum": "82d2a2b0c049cdb380f09acc422224eddeec8d9e", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.3f434a6.tgz", "fileCount": 4, "integrity": "sha512-Gae6b6P9ux3UGP0gu/Ht28SSbHU2fYD4IXnAU+w0U3MlNVwsa3euuNYY9RZnEyrQ29NTurcAgl7HtYvipICuog==", "signatures": [{"sig": "MEUCIQDXuNuj1DGXy1EpnaQUMdCMboilS6yYvbH9A6Xxj4g2ZwIgKbkbKR86NnYpgMF21ymzUxs1Z68A225dvfNj0GziEsA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.3f434a6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2629833}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "4.1.2": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "4.1.2", "dist": {"shasum": "c85f836280c95fbeba2e5f4ff70df6d309000f6d", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-4.1.2.tgz", "fileCount": 4, "integrity": "sha512-r8QaMo3QKiHqUcn+vXYCypCEha+R0sfYxmaZSgZshx9NfkY+CHz91aS2xwNV/E4dmUDkTPUag7sSdiCHPzFVTg==", "signatures": [{"sig": "MEUCICdHsQ5K++SkvRuxQwq+oGTcVu0UIW2+k5cv2+tcuYx7AiEAy/9KRGlxC4FrNwnBeMAF0y/F9OQ1alZw3LNIWNYHwsQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@4.1.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2629816}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.fc94ab4": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.fc94ab4", "dist": {"shasum": "9ba344542db4149ff93062b535b76b2aaca6e66f", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.fc94ab4.tgz", "fileCount": 4, "integrity": "sha512-HCRSIg60j1Vl5M5VauOVhBXBduMvFLhPRS+Eu+FxpXPMxffBNDUnb1Kmez8LNIPlBR5J8ZMK5eMfkrE/NsLijg==", "signatures": [{"sig": "MEUCIG7d9kHzpeoZDsKZE7kM0FRPj1/vEG2BKHTMLl16QJfoAiEA8W1obkHXXc4FpUh06RGQCVS336/oWlKOV7cFAiiyrss=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.fc94ab4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2629833}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.57e55a6": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.57e55a6", "dist": {"shasum": "220597ac2a4acad893d144dd897019cf604ded62", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.57e55a6.tgz", "fileCount": 4, "integrity": "sha512-UQ55Q/YSTgz/8dm5QrV0NYWeUUwPhw5LGGwvdWj6Wz6AkPWjFmi2Y7386mwp0u0yMWItbWyZLcvW/jm8YESsag==", "signatures": [{"sig": "MEUCIQCpKcpR2b8Xq+ZYTlJwgwxoIIUW8/cdG6wGVxs5sUkQwAIgPshqMfzYGvF1T+B+rMKk0rXLyftEtCT76FHLSpw+Zdw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.57e55a6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2629833}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.2fd7c8d": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.2fd7c8d", "dist": {"shasum": "f894913ed41c6dae6df774582a037ec3acbf2cb7", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.2fd7c8d.tgz", "fileCount": 4, "integrity": "sha512-ByApVFgycWjOGlwreYzNOXRi0HIVTIpEdEwy+4m6tPe0Ax/2UaxyjT0rAzUqUjPA4N2AM2+dK3JVgWQyozNaFg==", "signatures": [{"sig": "MEQCIEZ6oxkg8m8+9qoQjgY3kjd3Gx8PCQ+prWV6xunMv70GAiBD1sP00tppJLrxU3rv6SD0FxP2/R3kMCvSGZfXDAhJAg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.2fd7c8d", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2629833}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.e085977": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.e085977", "dist": {"shasum": "27a2003121da47899f671b0e40b9037cf869220c", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.e085977.tgz", "fileCount": 4, "integrity": "sha512-G<PERSON>wusWHCKfJ3T9WI0jsUZvXkjeypITEsLCMTwxlTh0AI7+e1SxhO3tMoEpoNYo9mOPh1NuuubGTC/WOtzcKmFA==", "signatures": [{"sig": "MEQCIEWcohspy/EemNFNzIox/oKaR/dgBjqFLnhcLu0H9JnVAiA+GoI1378VF5/byN1V0BeqczYGaA3V3YbVuDlZzYbtOQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.e085977", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2629833}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.7d31725": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.7d31725", "dist": {"shasum": "872f26759acd608e5eb4821e1192eb53c0a4b17a", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.7d31725.tgz", "fileCount": 4, "integrity": "sha512-g7CzNXcjfilMbW1IDvjsjpDbxboTwJ+XaSXckudAe2oz/g6u7cMrouwnipdMZ+ey98T805p5Q97y1YknWpvLKQ==", "signatures": [{"sig": "MEUCIQDJCC5s3sl+044FVMi7yWIFndzzSD0ZlxAyBEaXUvMqMQIgWTU2ZfZzbO6odkMKYEO15wsXj6qxLDS74ByVGWp1z+w=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.7d31725", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2636489}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.5a77c9d": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.5a77c9d", "dist": {"shasum": "c68a75f3d1a4906404a5bbd74a05d4b907f27cf9", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.5a77c9d.tgz", "fileCount": 4, "integrity": "sha512-4Wog07ev/HHYPLDa2psL6phEadaSkqhCB/Xaro9aHiGDFR7APB/7OB0eFj5Qj+r3r0cCDKpqTAPPanui06XoVQ==", "signatures": [{"sig": "MEYCIQCxj/CoGF4wQ+XtCpLjVSqeAcpaKUzVmmaBhHxXEuD8lAIhAOaSNArqqzmbdxI2N0Vm1qYMk1sHsdPm6AXjofngp/G+", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.5a77c9d", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2636489}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "4.1.3": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "4.1.3", "dist": {"shasum": "0cc2bc59c228ce1d64156089af21acc4302081da", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-4.1.3.tgz", "fileCount": 4, "integrity": "sha512-PEj7XR4OGTGoboTIAdXicKuWl4EQIjKHKuR+bFy9oYN7CFZo0eu74+70O4XuERX4yjqVZGAkCdglBODlgqcCXg==", "signatures": [{"sig": "MEUCIQDzhfpNZXWrQzgmwpOD1CG2Vwh+rlLiuqiHlC8FKLG5KQIgUWOgyfSOinV0i3M+U1CrdPqBJTv+kgikdwMxk4JtwL4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@4.1.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2636472}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.811e97d": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.811e97d", "dist": {"shasum": "f822478a6f240aea8cda0231a12a306ab43c0a81", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.811e97d.tgz", "fileCount": 4, "integrity": "sha512-tSgCS32pyXEtflsG5iDt80sPt7CjOD1+Tj0Ywq/B9IbGtTngl+95kDcU515S5TDPQDA7qmZBCtR8j5Eb6UHYKQ==", "signatures": [{"sig": "MEYCIQDDYEzm0PgyrzLA41hClROo0094r3+eTJM+NWZeJH4uxQIhANjgljVIATWQMbLZUTbKg3JLqqeApidAWR9+kFyU6BL+", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.811e97d", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2636489}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.3e9cf87": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.3e9cf87", "dist": {"shasum": "2e8529e3a698f9886030d5e9ab7b5085e1407c6a", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.3e9cf87.tgz", "fileCount": 4, "integrity": "sha512-7m6mXzDHe7P0jlwdICr+VunZPxyvgXt/yjHWJuvXAPekISpgzjzPJf98z0IxUOXGvvTlEa6mUfDAtFND6nzUbQ==", "signatures": [{"sig": "MEQCIBIpY//gjYo9TAQlIUKbjbd4T5gi42RGeN5sjhC3I5sQAiA4WSCNAvD/BMFoYyTeQMIX0WMv4y3u9mcqLCATDNbj/w==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.3e9cf87", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2636489}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.76e18e6": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.76e18e6", "dist": {"shasum": "c0692f5fbd4e8a9b718cdc3e3c99b09cd8117fd2", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.76e18e6.tgz", "fileCount": 4, "integrity": "sha512-lJK/2OigzbCuK03d8NuMZp7PSI9yi4p36cin5TL5GEQOdgoSImcVaYs78Fgad32ZbVjQFWbBHrbbUIDii89bLQ==", "signatures": [{"sig": "MEYCIQDJoaSI6nivkDMKxNZri6pVpC9v1gmyUA7SeW/gCAj6QQIhAIe5LVxGArHhIugw+WTTNm7hzTrZAMWiC8jAk78oJlDW", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.76e18e6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2636489}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.f66d287": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.f66d287", "dist": {"shasum": "5efba97e0e97a30e18139e7e2c46ce55ed7a3e57", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.f66d287.tgz", "fileCount": 4, "integrity": "sha512-DOnETqxj6LAq+EMRUsHuX+YBxGUcvTulUOZvAIis5E+MuqoCx84qJDVQMzhDO3wP3PZ7V0l4PV/1cFMDP+srMg==", "signatures": [{"sig": "MEQCIG21c7o9Gl7GMkyxZaeaGHJ5sJxhxxKc5WhPgS4x2o/5AiBf9wQIvE6Ks0qQ2se5Y5ZzuvOoDk4mz21V2QETGgO38Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.f66d287", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2636489}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.cdecb55": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.cdecb55", "dist": {"shasum": "bf155448584d44694f68a785f930c1e2d342f2c5", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.cdecb55.tgz", "fileCount": 4, "integrity": "sha512-VBbRxsRdWhpIrgtjJjHVY+z24hE/sfHZ/nfRok9MPO/KvlQUG3rxmn8phmEPMvC5gexCogsRoNkphF27KGMw+Q==", "signatures": [{"sig": "MEUCIBPmoJHFUuVups42NNYrWm3J9AKekg6Avgy83c+RZkO3AiEA1NknV349jjHD8DCoW7tl74AxmTPXd8bRbquYUVt/Ry0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.cdecb55", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2636489}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.6e1f533": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.6e1f533", "dist": {"shasum": "e8c0983f02b76c3891a7c927222af37aca0681bf", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.6e1f533.tgz", "fileCount": 4, "integrity": "sha512-TWUpIOaPxudXpX29+mMQpZEusWAd3z2iV752VsX7Qhpz5+NbPbtXXwg4LTOFQysRapiskx+Tj8itc4Trko9mSg==", "signatures": [{"sig": "MEYCIQDQYjOAkXEydOVy1ZVO1blPdtMVTHXgx8lHDgtQqXPpywIhAJbBlnTkC2iVKfOx6vQs4EA28++jfqt6yaNneNmtX1f0", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.6e1f533", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2636489}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.25539e3": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.25539e3", "dist": {"shasum": "721ab22e1821e1073232da296e0c0302ba13705b", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.25539e3.tgz", "fileCount": 4, "integrity": "sha512-zJLsF1hf7fGnqJ0tFAwSR/sUq2DA5BIanjPqtlZFx8E4bDtJZwYceDNxly0X4gJgSWPdK/7eNgbhPNa598l8qQ==", "signatures": [{"sig": "MEYCIQDIEGkOMhoLoEzGGGEmfQ2BT+2SRF9FNVJSEnakgT35PwIhAKFcNonFZd9wT+msMsoQ97rY7zgQFWgVtMBBwr6Y3OTo", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.25539e3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2631881}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "4.1.4": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "4.1.4", "dist": {"shasum": "ffdfed3d61203428d448f52e35185f85a0ef9856", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-4.1.4.tgz", "fileCount": 4, "integrity": "sha512-VlnhfilPlO0ltxW9/BgfLI5547PYzqBMPIzRrk4W7uupgCt8z6Trw/tAj6QUtF2om+1MH281Pg+HHUJoLesmng==", "signatures": [{"sig": "MEUCIH5qNDWj+i8Chq8ipx0gj8qhNydhNMPIZMnkF4bU+3VNAiEAuCljMA5MvFV5/vX+tdW1GHTODm6uulauqyDKwvAcCbY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@4.1.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2631864}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.adcf1de": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.adcf1de", "dist": {"shasum": "5bf51710335488478b615c930946c244665d08f2", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.adcf1de.tgz", "fileCount": 4, "integrity": "sha512-HSLeTppoAXyuENGKpOa+fBV4Ym9E/erw9j8DWFqe3N5uiI3pZ9Mk8bUtaVBUzScJOvJsVx1v3qDl4FFCX0WZlw==", "signatures": [{"sig": "MEQCIEbTKOjeapzJazEOPn4xFuklQxaxpacsYWoUjR5ZlUQjAiApAaDDji+iee9bWsaYLT60y2xS4ULWp2+AlM1fogdBSQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.adcf1de", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2631881}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.8feb6a7": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.8feb6a7", "dist": {"shasum": "80f968e3fddbf98bb26d97da57b7e9aebfe8d46d", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.8feb6a7.tgz", "fileCount": 4, "integrity": "sha512-x4mxQC3uODhdF5qUXO4dBAgztgaZL1VxjAxJLqttdrqbfHCjxA59GeGIL8rmQsI6aevESItZMVeaFdVM+jV8dw==", "signatures": [{"sig": "MEQCIFFOoUPzOEEYIU7CLqdQ/rT3rECAwP90fs/CX/7QV0h0AiBoqsLX8QXd3mZIa15EbP8E4x2uYy1q44/PpMIlv3JwZw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.8feb6a7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2631881}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.fc4afc2": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.fc4afc2", "dist": {"shasum": "2f1a0a1aa75242bb9b9e218528e5ebec88aa2d55", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.fc4afc2.tgz", "fileCount": 4, "integrity": "sha512-iWSqMLvjTujOEyp2V9Zf84Ughg02+X4QxMAYEAPKBIo9qM7MpJ6V6zrUvS3O8Ri5XIiO0Vm2ra9U8MD5m1oj+g==", "signatures": [{"sig": "MEUCIBXu85AULW0Q8V6+HpJEgLOR972+lG2pDqvhPTpyrAyJAiEAsEb7qK4kaX8ADY4tUwFGmI4pkQl23XjdXZLCw6RaAAE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.fc4afc2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2631881}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.650558d": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.650558d", "dist": {"shasum": "4b8baea16f106419178c9ddd9bb9fea7fa432f96", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.650558d.tgz", "fileCount": 4, "integrity": "sha512-HsYoC38TWEM7dKH0fPtmNFrH203+lssgQLH+bT1Yq+OCXsSF9HsVUpKtiv434o5fQ+asSWJaITnSeL+O6H4H/w==", "signatures": [{"sig": "MEQCICldB5zWd5F4Nkc1FvzlIUCc0VBtvmlbB1Y5bXKDTnk7AiBjhtClOKX+6aOE9qIx6Xj+e2Iw+xN86w/Q0jCqlTwuZQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.650558d", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2631881}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.ee0d752": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.ee0d752", "dist": {"shasum": "0f019051d0039914657a7f3dee877b4a6be5d689", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.ee0d752.tgz", "fileCount": 4, "integrity": "sha512-N7DBvgRpg1e150AMDrQLJUp666nfuvF7pH2Jiet9O9WjgyUo+nShvBN1WMDg3PtTn+SKYCUTmMJtf/VC5G5Wcg==", "signatures": [{"sig": "MEUCIQDSdrfLbFcR/97XcZK8o6DM4wDmKqKiRXmcgwSvqipRCgIgGYZB0QjRuGxJPcc5ttqpjAb1MPp4RF/pV5e42y+QFxo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.ee0d752", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2631881}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.8bf06ab": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.8bf06ab", "dist": {"shasum": "c75e6c336729c33d25e993b59e8bd8d0ab7716ca", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.8bf06ab.tgz", "fileCount": 4, "integrity": "sha512-0QxIp7LUCQjCrRz4mYK2W7WqAvPS/yH0KH21zp1oOWYEvRhIp+pRPqGyxUt0CTTiNYmpfKchbSPm41146e0t0g==", "signatures": [{"sig": "MEUCIADv5z65SyHAxatYOZYHfllz52Yi0PNWVdZWmIcCG5nxAiEA+yvNFDET10kLIlKmfvscEKgTDgkVzP0g8uD2cczsuv0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.8bf06ab", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2631881}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.25ec6a3": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.25ec6a3", "dist": {"shasum": "e8b66b84c3a390792231903ddd5bf92c64a61d35", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.25ec6a3.tgz", "fileCount": 4, "integrity": "sha512-JdKM3OmMakKjgSNqF7LTx+CYVujB2OZuYUtHB6yaPd9A8E4QkqFqSgVZvCsqANSwXd2OuQ8nkJo2UKqGj6nqQQ==", "signatures": [{"sig": "MEUCIQDl94ClpYIfjz2UehPHKBal1koVYtsWDSGfjPsbl9noFQIgIVLYVlg05Yi6Hl36wrSzZqjTPMITo9LBIWYrpKUm40U=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.25ec6a3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2631881}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.8e826b1": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.8e826b1", "dist": {"shasum": "875602aca2750caccb1d689b1fe877bf607904e7", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.8e826b1.tgz", "fileCount": 4, "integrity": "sha512-JLz9PNP8YIazAMNWmdhlfMvXKRs/K/3QgTnfgjDYuBob8oxg1ZO8uaiINoodXkxTbTAkjeAsTDJPhBVgRI5Fog==", "signatures": [{"sig": "MEYCIQDn12YYhvzRawuTAcykvuXi71NUb/9b4ZkC1V3HcyB4SQIhAMuIJHesizedMp8pOR03Xw9+DsH/tbMSZOnA/loNTVYP", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.8e826b1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2631881}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.a7f4a4d": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.a7f4a4d", "dist": {"shasum": "fd63e77fdf1ea5bf913c33246ee95baf6dc27592", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.a7f4a4d.tgz", "fileCount": 4, "integrity": "sha512-UvShAfjnoPUIMbFQamcROsgAkdarhghHUx0pSFte6atRjzCsRFVuHJjit3GeihDC/qXHiqONIaoNJEXBdJETXg==", "signatures": [{"sig": "MEYCIQDwa6WU+RIxqzW6LaGgEC9IKZEee4mS9CI6RDzW59XVsgIhAI6IVuvxxrv00cUAJ7doiqanBanXMyoMgKPcFDGH9/Gr", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.a7f4a4d", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2631881}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.46758f7": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.46758f7", "dist": {"shasum": "e1a0d5f5a5c4407f43d8e521ebbaacfd5d6e7861", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.46758f7.tgz", "fileCount": 4, "integrity": "sha512-t7P3Sf/5gfhOaQxXOiuRTTpH9ZSQxJMhQj1ZGIUruAPtcdImOPqKemdDlJDvQMJNapmLscqshdgNgkfyLayu5w==", "signatures": [{"sig": "MEUCIQC3LJdBz7nfKPmZr8X2ZKduxaWdb+1d0gZLy48bOtMS/AIgVUVz5fug7XojO7mezm8VotdoJh0dYsgKaKAcS9pK1WE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.46758f7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2631881}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.2bf2b4d": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.2bf2b4d", "dist": {"shasum": "d5551cc02ad8ca3cd58f8844da4e4af8b993211c", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.2bf2b4d.tgz", "fileCount": 4, "integrity": "sha512-EFiJ+GfI6c4WsLPvainCuqea3dzr5ZGBEPmjlkRDa4dDInROyTtMBVEe/Ahoerik/gkItIalpCXOPI/pCAwWuQ==", "signatures": [{"sig": "MEUCIQCL3Xd7Oxx1tXe1jJBTwbZXIzjajOlg3rE8h/XqMKkL+QIgIliYBU654N7DYBwY68vyX8ZXG/z2n34XDeWx7LTNaYo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.2bf2b4d", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2631881}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.d780a55": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.d780a55", "dist": {"shasum": "66cc118c95f08ded857eadbbc3d6d6c7109477d8", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.d780a55.tgz", "fileCount": 4, "integrity": "sha512-GBHPzzaAC0MDdfJluEf18MH+6NHCceAmOE/lCNgk8DHZaW4V/LIFuhp60tmJA80sC6CAlknb3AnJnTVzIRSNAA==", "signatures": [{"sig": "MEUCID5kWordzLQN1L+nTRHMjB/a+Zt6ylwyxpwyPKuq64I6AiEAk8JdKdXitFEuLipZG/mtJ5p2mYrC2qgkXcqkTUM27RI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.d780a55", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2631881}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.231cddd": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.231cddd", "dist": {"shasum": "f44f51ea1c674fc9830c66ea12b18eb8c6806a72", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.231cddd.tgz", "fileCount": 4, "integrity": "sha512-ZR2EFYufe7XBIdVrL5mxFI4gdcW23WDV43P9HRdrueeD7dsO8/KPDd3tEx9ITrD0TDgn1e7X5hrXmGjMkLxROg==", "signatures": [{"sig": "MEUCIGQ71c04R/FA1ELHi+xfTpWKo7n0JMZ2f66ywpOD4lViAiEA3pPjmNcUNaV20pTd7aqKLnH4tPq4NPUHaGWy2PkQf1Y=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.231cddd", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2631881}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.52000a3": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.52000a3", "dist": {"shasum": "fc84ea1003facfb085fc2f18c9c21a6e37357cd1", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.52000a3.tgz", "fileCount": 4, "integrity": "sha512-K7Xsta1J9fWkfpi+auuD9/dpcFt1TrQCai+ZAaC4SMix0yCbRdZB0D33HRgyxyMTM0fcSQAfrxCWxa0NTZTxGw==", "signatures": [{"sig": "MEQCIHVYCdjGy8z/nri5SwEhIufD5xxhgFIjiJO5Mvy6hZPIAiAlHKKtVBhkzzmHYAXOeZZGhfxvUK3sl1mAOB7v0992uQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.52000a3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2631881}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.62ca1ec": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.62ca1ec", "dist": {"shasum": "a0152eeda0f4fa28166c3153f864ec9d49d2a310", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.62ca1ec.tgz", "fileCount": 4, "integrity": "sha512-BiC11Cu/S5cwDLy+0lz2gGukTw0CfHD6vXaOBKW497ufpYahwmzxcR5MBucHIg/xk+xCLsSXiXZNd7hdYX+hdA==", "signatures": [{"sig": "MEYCIQC30as64DKG0t9YKWV/BqbEjbBQ+OxYxxnewTcD4ZQMKwIhAJf5JZnlnlfE4xcEAlh1Zv8jqv43jbmmhwBv1Jop36qi", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.62ca1ec", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2631881}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.ba10379": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.ba10379", "dist": {"shasum": "512557f2a821c3160a52858754f7b0c373cd146c", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.ba10379.tgz", "fileCount": 4, "integrity": "sha512-wmF6oNuXChv1dzBhf20sRBlmwBNdnjxDFeOvzdHZdyuWpMcsF9NrkYPSNaci4DK/PozmUianXX7bpFNOMwzjkw==", "signatures": [{"sig": "MEQCIDfU4ctTyBEOw8gvQDrFTDQ6XrxVR16bbLq5gXfXqzKYAiBgU1DgHayFnN5V5CA9gprPJzUSchtAjEMph0NQrWl8vg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.ba10379", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2631881}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.af1d4aa": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.af1d4aa", "dist": {"shasum": "446a16b12d3d0542615d832a42dab7661c09fefb", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.af1d4aa.tgz", "fileCount": 4, "integrity": "sha512-PjE7hdkwfE+Gt0K6JoGIC/3TdjO4XSetcNXnTspWamXubS53JLSznz/qWnvQ1JxuBCLEMkffIEIHiQngTMfRNQ==", "signatures": [{"sig": "MEUCIQDZOiiSJD/J8TmXK7HDeiVZpgJwd/tj1PBLVTahaquzQwIgd3qZofzC0GM3+Sm0QKBLHjbewaX5K5YhnYi7j2qoKqY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.af1d4aa", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2631881}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.3a1b27e": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.3a1b27e", "dist": {"shasum": "997d9a55c860a6027823beb7f677c0edbaa7a577", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.3a1b27e.tgz", "fileCount": 4, "integrity": "sha512-/gwBVP0qgwigJp52Z9jMR+2c5ebNxLjemPzRrFyrnQT+D8GI+dN7QRRz1V9VkZQTGkt2uNYGn1cQCFLCHtnGVg==", "signatures": [{"sig": "MEYCIQD1RHikXVr6XDbVTx8aCQAY3lk7AI4LcGPV/45pCagcuAIhANyf5xe6WVkswYK/Tzh9ECyCRy+dHVbtKMWww4I/tUzw", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.3a1b27e", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2631881}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.d2daf59": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.d2daf59", "dist": {"shasum": "453d0856dd56c484bdd569005ac16d0e7b49d92a", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.d2daf59.tgz", "fileCount": 4, "integrity": "sha512-HMDaoZYq9Ue0M1Gx1F+EBnrS/MXIDa2qUEsvklvZnlQiE51hnxCvJ2OweMOvulmdgFZeiTGFKU9lzO4d6aEFPw==", "signatures": [{"sig": "MEUCIC4krE/s5jbPqf6OsB87rDIHwZMeG6JCFc1R/exNsXGgAiEA9qavquuTiLQEL+WdfiOm4+lu4yea55Ju1x0NDd+ALas=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.d2daf59", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2631881}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.9fec4ef": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.9fec4ef", "dist": {"shasum": "dfe37df2271f8ee205a51139173ed629070befe7", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.9fec4ef.tgz", "fileCount": 4, "integrity": "sha512-V0y5TKBEfJsBHoU16KxyqrPHu2Bmjc0phbnZvOWUBYH3j2jv2eQ0Kncp4S6lE71M8yp+cd9TDFC4tlPN/rjZOQ==", "signatures": [{"sig": "MEQCIC99kTJSimMmNRNHPMpgK3e/pLL5mFLe30omNBDPutH6AiADzdzRZyg1Cx+PChXLn9/OBDeTSmR+KTKPaFHUajG9NA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.9fec4ef", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2631881}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.d3846a4": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.d3846a4", "dist": {"shasum": "a69a4218dbb72bd0ca34432caa34c2560c27f3fa", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.d3846a4.tgz", "fileCount": 4, "integrity": "sha512-Phod+eDn/WPiCHdG3xrdbJiFUkQLDaRDEsjseC/PVyDvqPoIviI92PS8ZzX5FElfBaOyItRrx5lDi8V4hYAloQ==", "signatures": [{"sig": "MEUCIFvmJKD74mTHu555y+7+1QQPjKYJT5wRXcUaVUmEGGUTAiEA+g7bjRXJBxnbfBcG2wrwMzoF80E15Rj+JllGW26y9pM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.d3846a4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2631881}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.dbc8023": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.dbc8023", "dist": {"shasum": "46fab938a7785d3c94af42a0df6e8a2fa0e6c2d9", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.dbc8023.tgz", "fileCount": 4, "integrity": "sha512-VUxQZgSIioMM5O7WbbYvdQ9Re4WIFqF2PsLM0btS/ThCuunF8MORzsQztjOORYGPR+2nMglwqfkeypMcwte+Rw==", "signatures": [{"sig": "MEYCIQDPkp39hcAe3aJ5u9donA1O+kvyXMhtiVRSczAJz44t9QIhANQ5NMbB1UAp358qalpwowPjqjr+ap6yDk4/m/j8trcT", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.dbc8023", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2631881}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.ab4eb18": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.ab4eb18", "dist": {"shasum": "39ed9f85b8491e6fa0079f0d3aaf1c4f95ac296b", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.ab4eb18.tgz", "fileCount": 4, "integrity": "sha512-rlWz7tF9fPjgrDJZnqlJICvaPrJYu+t4kM0fyzzldEi1MtBB/6M/qk5Fn8Bdu6yB3Brt3lFmdfcx1Ra3yP3kNw==", "signatures": [{"sig": "MEYCIQDPK+UAEJQrKU2yJPPPP++oHgfzLY23TNm0LyZYqSOJRAIhAMQKgny2BPZKqfyvCl0cOEvi2huoESqQbp2uZnkz/jfU", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.ab4eb18", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2631881}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.a636933": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.a636933", "dist": {"shasum": "a99b4322c5589cd4c013db050aa557ab74f64a40", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.a636933.tgz", "fileCount": 4, "integrity": "sha512-DyyF6icJxDXcVF7ly91UHNvimGdmguuHr+pk+x0g66xK2GxtkywhIezy5gm/UR2AXDs7qekTxowrDTF0dDPOLg==", "signatures": [{"sig": "MEQCIFmbejyM5nap+cuFrjj/YSqqKBEMEHGBQJLKlFuySmTnAiA3G64zvQfZ4mdYKOeP2nrT37WMDAO6JlNLaiYks0rSHQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.a636933", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2631881}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.45cd32e": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.45cd32e", "dist": {"shasum": "a4f6fceaa66e024e631fed16234b4bbed8dababa", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.45cd32e.tgz", "fileCount": 4, "integrity": "sha512-HmNRq47kVuzZSonJ8ll0t5mClen/laAJx4licQ7RgIaDvGnPdxIXaq7CVeQQ4Gm+x1/4rFmBaUAcelQdd/IDoA==", "signatures": [{"sig": "MEUCIQDjQdj+fItr2+32njmKbrptB0i3V79lte9R59+k/BlM1wIgLmNgv4jG5dC76maosrZGTr/kQcT2SQG6c0mIYzHBv3Y=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.45cd32e", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2631881}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "4.1.5": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "4.1.5", "dist": {"shasum": "ebece1488e280f4407324842489059b1be01aaa9", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-4.1.5.tgz", "fileCount": 4, "integrity": "sha512-oDKncffWzaovJbkuR7/OTNFRJQVdiw/n8HnzaCItrNQUeQgjy7oUiYpsm9HUBgpmvmDpSSbGaCa2Evzvk3eFmA==", "signatures": [{"sig": "MEUCIEPrZ7ffMgkVhTQTBlGJUEbvWJQ+VAs0Mm7ktawkqIKbAiEAiMIfRaX04tpF8U92xcc1HIvwpq3q1wIg7NqWFPhN9/E=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@4.1.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2631864}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.4e42756": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.4e42756", "dist": {"shasum": "8a18f58807ca55d8fc08daae3a2ea61aed7f7e13", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.4e42756.tgz", "fileCount": 4, "integrity": "sha512-fJNCQ8yMICIEUsgIH5I+VhJWY97kWbBk/+KiWBE6VfoHetST5qzA1DxUlqMrsVE/KHOI69n/i+oSVHRBccV4VA==", "signatures": [{"sig": "MEUCIGJWm/l66sXOq3TvfzFw1YLbWZMM4FLCI/j5IslI6O2iAiEA6unz83Mboka2Njz0xSg+XvxOMSWWiVj6eRQ88MpSsfA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.4e42756", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2631881}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.c095071": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.c095071", "dist": {"shasum": "315b7355311555763424cba4520f32b878d69e6e", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.c095071.tgz", "fileCount": 4, "integrity": "sha512-7m4hNtqKv0EMaTj+DudzeX4KdoB6Zz19qzpvjLt3tz1pzkk07V6vqqtTaAQkx1ZgY4XVLET7lSSQBEgcPRYjZA==", "signatures": [{"sig": "MEQCIAWEHpBeZWt2LvZODKVxHxLzAlt2tu1rk16ea3D/OYB5AiBoaJqQ5u3EQ32SaAx2EmiUwTxNtLCZL7/4Tgf7JOhT4Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.c095071", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2631881}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.dd5ec49": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.dd5ec49", "dist": {"shasum": "0cf7e22510603f891129bdec4a21b0e3b492948d", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.dd5ec49.tgz", "fileCount": 4, "integrity": "sha512-uSVDGkYViTl+8HNaX9Ryy5SGdRvLPmNdSzAncZSaIkMvDZKLDnF3Wio2H+QpHAvXS+lERF+UmBlkykphFv12/w==", "signatures": [{"sig": "MEUCIGGnXJvNPXMojo6ywe3/xKa0spOinKg4+tkNAuiJBil2AiEA9FbRkafV569oGlXthXrakL9cN7uctwjLytJslgWc9vY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.dd5ec49", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2631881}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.e00d092": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.e00d092", "dist": {"shasum": "b8a16ea99dfb3b4e17bebfa86b7ae6bf2589f3d1", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.e00d092.tgz", "fileCount": 4, "integrity": "sha512-qRn3fJl82LbfQS3+p4+gsSv35P20bRn+DnTQqt0CbiSFykzNgjKjELdaqmRgJgDaM/1EwHkGp+ejIv5LG0sswg==", "signatures": [{"sig": "MEQCIBb8gsiPWIfTmqBi+5VazJtgYCgkfO2BnhHXhENTWJxeAiASKj0qE6+C3ufOmUMc2xdsfW7RmUrP66hePCg44zDqiQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.e00d092", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2631881}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.473f024": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.473f024", "dist": {"shasum": "b391292c26ce90a15fb6ce3480928775efd7af31", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.473f024.tgz", "fileCount": 4, "integrity": "sha512-gh1qwatkuHSknYGfU+G5RMfkgRs4P+rKTnkIdCbJhIGK7jWGQCRuE8OyQmuOvCHKwLJTx8sSFLMb+BlahJMGTQ==", "signatures": [{"sig": "MEUCIGsHVVXqmjPVMO69c0N5AU0itpsnMXhwxnXNj5MNPscBAiEAhlCDJZaWRFgisrwd2FbCjCWLIT/qX8NvCpStjHSad90=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.473f024", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2631881}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.d38554d": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.d38554d", "dist": {"shasum": "859e9ed02486fd09ec539dd37dc1fc54988312e8", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.d38554d.tgz", "fileCount": 4, "integrity": "sha512-GYfvGG4ScPuOTA5UN7+1kurPBcb2mQr7QmophT/VWT799jMSgYuUOzJA/ZVU2iv0jAwc9tD3fTMO8XBwEK8RpQ==", "signatures": [{"sig": "MEUCIQDLQLTzsmZpMGJhqSd7SlA7UOA77rsByikU3RPDZWt+OwIgcCqvFYgvdWVxXaRXF04QQAxIx8yh4GgTd1077duto2s=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.d38554d", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2644681}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.6a1df6a": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.6a1df6a", "dist": {"shasum": "bbe8cd85d749e90fd07eb2abda025e4f282b33e1", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.6a1df6a.tgz", "fileCount": 4, "integrity": "sha512-DrNfmtDDaHuXFPcv8g4A3yu6z3MOAP99jwpWHgweebtgk8iwg41QkSstwtxjcBzzaisStky67V7Cg8H7sTW0sQ==", "signatures": [{"sig": "MEUCIQCi88WdPU1rQ1n1lLAFGJSUav1DXhNWt3iUbaDYpZ6QDgIgeXSQBoVjTVoa93A3soWwjlirfWsedNmfFucpVB6CFF4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.6a1df6a", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2644681}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.5c5ae04": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.5c5ae04", "dist": {"shasum": "61a015ffce77f7e00b1536618312b0e0e9882a35", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.5c5ae04.tgz", "fileCount": 4, "integrity": "sha512-AUqO5e6jtFoIacbvtzO+CUHQq81sToz1tj7iafOH2jEAbGGA+elAr13FbjxfFXGNn5lHx62z8t+0Nznmip3CdQ==", "signatures": [{"sig": "MEUCIH4WtIrxGmqndoKH9sNCtjHJqOR96uoDS/jWlXxuckOjAiEAjp6sHB/vQnxFu0aYkFdI0swBM8WN+7dygMedydKNaw8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.5c5ae04", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2644681}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.ed45952": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.ed45952", "dist": {"shasum": "ed457a8d1ece07c1186c50863dab12f0fbd33524", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.ed45952.tgz", "fileCount": 4, "integrity": "sha512-cv5zcNIhfNE0/PqRwwaOYKLFm2Lf0V7wQ0VPFdX/5JSE6tj6A8E0IlZI3crD3xOdc9kICeY9mmhe2X/1o0c0NA==", "signatures": [{"sig": "MEUCIQCh0fezxGOB8AZa3OgptQRJhIV1wmgWto873k38NpE/ngIgS1Vo9lgwNonmYTDrXzyl1Sqp4w6UiPAtUHqk9R6x8EI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.ed45952", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2644681}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.4f8539c": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.4f8539c", "dist": {"shasum": "2d31f87bc5c7753b013ceb055bbd7f3291b8a59a", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.4f8539c.tgz", "fileCount": 4, "integrity": "sha512-vXlmNzhqcw+48qQ1GWcO+dTPPgsk6isYNTa+3aaR6cUb7DMGwm8WjUBQFUsCs2mHvxrDFUOFJA4gz4qzyAPKgQ==", "signatures": [{"sig": "MEUCIBq9SNhGgVOLAzrfjUO4CncRmtH8k3z+ORq+40qKkrfOAiEArbUwmuPI8wGYkscvFz5q3vqoWkvRk8ybdetHy5B491E=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.4f8539c", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2644681}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.449dfcf": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.449dfcf", "dist": {"shasum": "c41aa650295a998bbc6545b56efb39a7efeff341", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.449dfcf.tgz", "fileCount": 4, "integrity": "sha512-LFu6gk1tC6qlurjE8MVFJz9G3Y57QGlLtfsD50/Oz2iPDj8fqCveVxrw0TvLlwbpP3/ES6D053tlolm7yeGqdw==", "signatures": [{"sig": "MEQCIFyYuDXXQLZUPYDhkXidaajXwrMXdtuuPOgVha4OOVVsAiBsV14qXk/7244kRwfJXOPhUBmi1kJYP0nsYVKREBWUOw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.449dfcf", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2644681}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.d8c4df8": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.d8c4df8", "dist": {"shasum": "b24ae665b4e4dec24beb3a36caa97f02c23ac7f7", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.d8c4df8.tgz", "fileCount": 4, "integrity": "sha512-2UvQgQlsh0RsmT4NVldXz6+j+YaFZVLgT991CSzhT25ZnnWK5u7epzDWl4Qh5i5plCcP8EWdxsSmwS9iSnoElg==", "signatures": [{"sig": "MEUCIQDy6oxIInRb/rpkR64yN16SXDLYnCmRFu2VGyasXhWc6gIgFt/JxVsUaiMcgcRQbzMDTZqriVEExPEda7NPjynTSiE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.d8c4df8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2639049}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.179e5dd": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.179e5dd", "dist": {"shasum": "c66affddb5098ecc7a41d456d160aefc3bf8baad", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.179e5dd.tgz", "fileCount": 4, "integrity": "sha512-EszcVCUnVFdNpYFiwlBDSOYIE2i40gXurlH9bZSS+yYIeBmeGVZD76WGR7GVAj6dZ7MD+crzWLy1ZVqBMtfASA==", "signatures": [{"sig": "MEYCIQCv01kFf5N/d22rXBTH3HID1eMIh0jgZWuiAAYIsvYaHAIhAIuAA/yL4NwnPw4QjBf1zPEhknX7fX0DUm4qwxI1rmbD", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.179e5dd", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2639049}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.17ca56d": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.17ca56d", "dist": {"shasum": "cd9cdb1ea05e4bb79630e6ecdeb255a1069b2877", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.17ca56d.tgz", "fileCount": 4, "integrity": "sha512-YBo6WcsL5MPlPpyWNZwrR7/GE1qBUmjrciN7nHWXvVGZpuWcGCAmX/3gE06xXHtSVI0EkIHJv8tnVmJPHNhfWQ==", "signatures": [{"sig": "MEQCIBKMlDd0yDurj9SBaDXU0n4FhuRVIQSvZ9cVYweihVNKAiAbCIs4XP+XxnuPS5QCL7BE9LHhJu+RdsbACtXsY9xMCA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.17ca56d", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2639049}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.62706dc": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.62706dc", "dist": {"shasum": "1a3d8249cfc19faa3662b4230cb3455f055c413d", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.62706dc.tgz", "fileCount": 4, "integrity": "sha512-qh/x2HYqVe3+gr1d2A650Sbpgd7IE4G69Xa8kuVGerkai0SOZY0xtF9WJHgwbwFQjLWK7gPZClY5yekJHXc3Jg==", "signatures": [{"sig": "MEUCIC6oTLjvmzN3+aMMXq7xgZGohpag+SnFd4FUSRtPN3nYAiEA+33pju04RWQu8nEkzZur0aBwPojAJwvrTMrhQRwJj9k=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.62706dc", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2639049}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.56b22bb": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.56b22bb", "dist": {"shasum": "4ce908c3b7aa56172126cfea114a25f776ddde79", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.56b22bb.tgz", "fileCount": 4, "integrity": "sha512-27bHWm7mwDB6qPN6woMcSXcSVR7X8zxihlu6+aep8bWm8P7K5y4pvUWS8FapIj5mZXBpFwK1q4YfoA/Qsyx0Dw==", "signatures": [{"sig": "MEUCIFh7E8v0iR4YMTnI/CJ9i2P2UKnZyG4UWobeKs4rrmRfAiEA7E8X+gzV5gdLIJ1vGcwHcYqMgiPvKcJqPQmobXaovgk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.56b22bb", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2639049}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.ff9f183": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.ff9f183", "dist": {"shasum": "07465a44329df7373e0e684aed1dbb26c27ceaa5", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.ff9f183.tgz", "fileCount": 4, "integrity": "sha512-8PU9VHP2CGPKl+qYvtTxoiKKzh9mXxMQt4XH+LmoY054CZwK6weFvI17gy09x8gzMukI+KBEOgc/YCyt2jB1ZA==", "signatures": [{"sig": "MEUCICo2malN7wfWLzYS0tILhAWKW1NXWndY3LdGsLsUJh0SAiEA+iHdv0N4mxbDBzkJacXR6vF+98H8DpY3babr3MHZsr8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.ff9f183", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2639049}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.ae57d26": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.ae57d26", "dist": {"shasum": "75af747112a8de576186a29c8f63160264288c0d", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.ae57d26.tgz", "fileCount": 4, "integrity": "sha512-cGbsrwCnsrU+BEXqybDs5g9urWECSYbCXUW95oIx7vER/cZdqNMGxHvqPcNOUILCq0KI5MqRRGALrsc8YXrm6A==", "signatures": [{"sig": "MEYCIQCL5zXl2LLoyOwViV4tX1B2Wul2jTyZwdGwQz8+NtkZaQIhAPur2TF6XNgRYY75/DGo9/m/QIg8gUFmlLeow59Shbv4", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.ae57d26", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2639049}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.2f6679a": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.2f6679a", "dist": {"shasum": "968eb6dd510f94a934e77d2d68d70c088a21fccf", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.2f6679a.tgz", "fileCount": 4, "integrity": "sha512-FR40PVcAcX1gcS+CrUbynDHQdIbhUWcraww1VapgUEn3elCs079v5dKHsykPuSeh6Eg1mk65wJfHNKYa3UWcow==", "signatures": [{"sig": "MEQCIBSm3rNjTjUb40NKEDf3h9dYX3U4B7k6odEp2VJyY594AiAjaUjjtimWAA+GGR017fpKHqUoWdLr4qXnWWjKLOzQtw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.2f6679a", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2647753}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.47bb007": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.47bb007", "dist": {"shasum": "bbd93e48f667fadb518b9e22be924fb95172bfb0", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.47bb007.tgz", "fileCount": 4, "integrity": "sha512-6mrX/ZoXkvO0lrn281yLO2Oxq8W1hfaYF2apOLr1ZA062DO/7TEQA0a+8ww4R4Z73zIqlE3MgE2Wzq+6Gazcqw==", "signatures": [{"sig": "MEQCIE8zQQRIqNcpM+5923fxgmrEoFzEwcvKoDBvZZJ7VotfAiAaPaQS9NCP4LzvVZWzTfe9g8GiXEwnp+Xo/Pz3X2aIPg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.47bb007", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2647753}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.2d13998": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.2d13998", "dist": {"shasum": "2570d2ca35b07f406939c17908c92440b2487300", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.2d13998.tgz", "fileCount": 4, "integrity": "sha512-+lasK4M1cGpmBZapbs1+k61tDZtsr5ks4wgxraK4PVIxB5Z3+ptsViA6F7MLQDjBQW7NAGMqG+8zqv8oNDNxIg==", "signatures": [{"sig": "MEQCIHwLc2TvouZcwL5dLopGhDXaGFlRsAImjOSOhsUdz4qhAiBx35TtcHKLJwYwjx4CaHn2ci2vwKvnQqYsK/XDDy5XLw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.2d13998", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2647753}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "4.1.6": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "4.1.6", "dist": {"shasum": "9b445635928a43b92ffb7b52bb063a549d7df980", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-4.1.6.tgz", "fileCount": 4, "integrity": "sha512-nqpDWk0Xr8ELO/nfRUDjk1pc9wDJ3ObeDdNMHLaymc4PJBWj11gdPCWZFKSK2AVKjJQC7J2EfmSmf47GN7OuLg==", "signatures": [{"sig": "MEUCIC2/ygkt9WpcYRqgLIPhZsCb+9CQdgLJ/M5reH8SAksUAiEAjUoo659KVLCtbp2WlhUFkh2Iw2uYJxMwrFvWKrwroD8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@4.1.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2647736}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.737994b": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.737994b", "dist": {"shasum": "161d25d313a69b14fc118490324264f7428b9f03", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.737994b.tgz", "fileCount": 4, "integrity": "sha512-ZKCnXnaITxmqLaRkBTYTsJyboDcjr7ODR9Q3mEOar3liRgdVjM02Zc9wn2iVIurxA30Ot3hlHELqzDBO6kacJw==", "signatures": [{"sig": "MEUCIQDiPRF5fdiyjlPp6SyAzQVabfptg7sOJ2t0YiZ3NcZeewIgF0wflusIlm3JngC9oU1DMNYiq63f8H7ALI5n33LdXGk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.737994b", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2647753}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.3386049": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.3386049", "dist": {"shasum": "805325ba7c04f96e248b093b5ba29cc8e425f136", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.3386049.tgz", "fileCount": 4, "integrity": "sha512-Fay39a37FLjHzktWbonCtGd0rwHipTGrPyH/fqkwozC+/s/64SbJ9m0rrw/2V081YhfeL0MfocTokbEwM01BpQ==", "signatures": [{"sig": "MEQCIARzBWEYw2rKHKDFq6fCua5AvPT5Nmmh5vntcjgWvqR0AiAOKApgqJu5TNH+12TviLlkWlaga3IvJBtcjJZ1NmcZlw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.3386049", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2647753}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.f0986ce": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.f0986ce", "dist": {"shasum": "84be47f9746f64c2771fe581e22483548abd534c", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.f0986ce.tgz", "fileCount": 4, "integrity": "sha512-Hah2QRFCpO0qVYNXKaHj3jti43H+9RFnwTBs7fxCsyPINhoMGTmxmu/IF63xJptUDo9UBmQfLp8pkG8QsdWpKg==", "signatures": [{"sig": "MEQCIGXiwNLZXcDdL92i5Zi7ABKlv0Pc1+CgiGgiLe0vb/TGAiBltUbA//iuaVNa3XgsyZOlAJAIjlrWoj1/oOS3WfHq3A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.f0986ce", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2647753}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.0d975f5": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.0d975f5", "dist": {"shasum": "e230038eb38abe36842a62730d1dd04c09ea7170", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.0d975f5.tgz", "fileCount": 4, "integrity": "sha512-xlxiTXUYEVmFHb1eDzYIGEtH47+wOlGYvY7A+JTFWJPxgvA/KL3AJxKrC521Xm28kBh2DI3QFzLY54B6eZSPEg==", "signatures": [{"sig": "MEYCIQDSyCGUn63b+oE5IQkgd+6t7SSm2VmyEk6ORgkmEEkiyQIhALBKbP7scoT2aQbnmSzkMMNuwoC3sRyd2dZqDiwUuFHJ", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.0d975f5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2647753}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.19e2b29": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.19e2b29", "dist": {"shasum": "51b5eb6eacfcaf79abdb52882e94decf1efa4df6", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.19e2b29.tgz", "fileCount": 4, "integrity": "sha512-AC7M8AzvyMiN/AF/YxNF/H5mnOMBGxH2ME5iOaqaPTncIY+VJtmHdgVlvXbV4eMoMVLCl1iNskQk/W/jw2oqyA==", "signatures": [{"sig": "MEYCIQCTDG0C6oL27Otb5Wb385aOrPasqrs6OOzPeuASSihC2wIhAOxyCMudlAJICaOp7SiH3FWybW75Np/YRzYitVn38Kw2", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.19e2b29", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2647753}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.5688f0a": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.5688f0a", "dist": {"shasum": "38d009c7e8a11825e1a24996aa96ba6301dd8afd", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.5688f0a.tgz", "fileCount": 4, "integrity": "sha512-FQeWwvdE1i5YnnhLcygpSRNIMkhsLYEd8FzJKY3oFuSYeRbFEa1ZR/XW/CP5H55Zc7W5Pv7UPBHHWsQSSFCj1A==", "signatures": [{"sig": "MEYCIQD/8tvkvddcNwieVLI2x519LLgc2se4ieojNEV/N6byQQIhAJeV8T/C9kjMAeBNJ13pgJ92S/Ucu2+9XN5gsO3v4LQr", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.5688f0a", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2647753}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.ba944ca": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.ba944ca", "dist": {"shasum": "94534bc8875e2688691566f44a9746fb3a18337a", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.ba944ca.tgz", "fileCount": 4, "integrity": "sha512-U0KE1QOHY7OmofuC16I6N0JUgDW/j7KecEsiv6IvSbkVR/7j6D3qhpl1WCpDBxugs8z45Dpefylc3aLMxRRlKA==", "signatures": [{"sig": "MEQCIAMPnIAvXsqBap+6pWak03orR+1BTXlAmAq6Rs7jfWcmAiAgdxDNw4gi6apfl0/LcOzBuKxZdoGCiakipwhykgjfYA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.ba944ca", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2647753}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.4fba87b": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.4fba87b", "dist": {"shasum": "9e14ac3cab7ec4f3f7cc1f8ced371b7c8b566b70", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.4fba87b.tgz", "fileCount": 4, "integrity": "sha512-yNbQUx7BXMItYOTvHPxzWKkMDsFi7vhq8dtHpTA2PBW6Z5ItlCiI4OJXmZW1kDh+v9uUx5vCwxiHhHqjY5kTtw==", "signatures": [{"sig": "MEUCIEhmbJig4VVlQhSnY0lW938Md/6k0eahLjm5+3hbCRv+AiEAv686i6GCkLzTEwwT7x4GngGQDlM1nVVD7CFAhmpzLvM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.4fba87b", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2647753}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.ef2e6c7": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.ef2e6c7", "dist": {"shasum": "b06fbd441a9091debff15ea06539be8ea392398e", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.ef2e6c7.tgz", "fileCount": 4, "integrity": "sha512-YoCt2DcYnLgrelc84cXdGPZsoT60ivP/6CYUEpKTaiWAUSgGoKgrRyK03ekbhIKG516srctQpbMxl7QGGbjoDg==", "signatures": [{"sig": "MEQCH0CEyJ3gfQ0gA4cJSHKTIK93ZuQ8jhHSKR+Lqi8u+vUCIQCIAWiBIgXzxc2Ptj0Nl74uuYghgxhbpPO1sSU4eLi36A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.ef2e6c7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2647753}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.498f9ff": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.498f9ff", "dist": {"shasum": "656223372f1192d67a0b78ed592f32c5e20516a3", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.498f9ff.tgz", "fileCount": 4, "integrity": "sha512-6moQCwzwcpWkCyW+Mufdbj5ay54o+Kd14JmEIydIjIVGLHba8zXWt+8uLwc/wOcDPAq1pCNGmfdVAq1JjHI51w==", "signatures": [{"sig": "MEUCIHS/rSpxV5PBD/ltm2rgfJeGwWFiA2A3fWeY/DOxVCpkAiEA29JA8V1a7uSF7HEOqq/ALd8wUQRiPchJoJAEvagOc0w=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.498f9ff", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2647753}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.4db711d": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.4db711d", "dist": {"shasum": "1196cade42ca8077f720652fb03aa245ccb93bf3", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.4db711d.tgz", "fileCount": 4, "integrity": "sha512-QzUfygPYCHqFwhSIS9XdEVgkwJ9Wy+p7Nl2UYSqWV8/I3WFhHnR/RH5oKpPiNQ2uZXfe/e/d8CrVcWT7S6wKuw==", "signatures": [{"sig": "MEUCIQDffkrTVtDJn3nPRjCTs23e+SJr5XP2KWqGk08wP1h82gIgQGQZmJ5LmGzFXIcivS19mhWJWbYk63My6P/YfmvUvKk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.4db711d", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2647753}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.e57a2f5": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.e57a2f5", "dist": {"shasum": "3d86d6eaabbbfe7f430d867717c9fea91835f49d", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.e57a2f5.tgz", "fileCount": 4, "integrity": "sha512-x5Abep1DqqNkUTdpT6XNZrKtNmFP8RJyNtfKIVX8U0pPbIv04FybbpMAgxoLISgpK80qaJd7ToSfCre2UBUFEw==", "signatures": [{"sig": "MEQCIEC8LstF86uh/SVU1TBHtfFkZoN4skjRsxPUgOBFRkQ+AiBuLQUoIlGoNpkYcZTJvEYX1J+1VcYLjjxoEc3xgslziw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.e57a2f5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2647753}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.f3157cd": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.f3157cd", "dist": {"shasum": "734958028061392770d1e2f9cde07a984ccfecbf", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.f3157cd.tgz", "fileCount": 4, "integrity": "sha512-7fHsnR1PsgtOd8IGSaQn8Uuio+h1wEdasBP0iBC27R6XiP1CfS9yY25vjg1nFdGyHJr/sutSKTS4iBC1dAqxzA==", "signatures": [{"sig": "MEUCIBtchxEIs4CPDTCSQINeRWf+EGkiMAot3bAUqqtFQkCzAiEA+d80UtrmM18dFko+EjjO6vGpluibTh200aFi4m7CMYU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.f3157cd", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2647753}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.6fb98d2": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.6fb98d2", "dist": {"shasum": "a55ace685d037ebe2c884c9d3fb77dcb20c4c17e", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.6fb98d2.tgz", "fileCount": 4, "integrity": "sha512-eVgut2Upl9/niTj038kSmMmbIXmLVBskgGCi9D12QtO6WRB+4iZ2TRLZWgqeZMlgy9/EbdOY0bm15HlEiAR9Ow==", "signatures": [{"sig": "MEQCIDjxv4JzsZuQHDM6b9gkx4sy6JXOHtV2I81JzsDHH6IxAiBSgIHmJ1XO/SbOIz2hb8QoPktdNamdO6K9mO6q+ZcXLg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.6fb98d2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2647753}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.1ada8e0": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.1ada8e0", "dist": {"shasum": "00c6a66f92784ca16aed2419ef4db71621fef597", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.1ada8e0.tgz", "fileCount": 4, "integrity": "sha512-MHtLorzGpefxGi1EpTfW3wX8kgPK7Wq6jafLQ4AAy5uxviM3aliKdwgy2r9vtrqTwBwFh+m5ElKvCk9us6ynGw==", "signatures": [{"sig": "MEQCID4CzVnk8LsHMSiOCBEAGd6snOdvILtB5VbpuVQJmUb3AiAtsy7VI9EmEJYz43ZVHAl1e0UFtNCptzkRlT8b95Nk6w==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.1ada8e0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2647753}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.bf591fe": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.bf591fe", "dist": {"shasum": "bbbf59fdf38df75fdb171590b543b6d58c8926e2", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.bf591fe.tgz", "fileCount": 4, "integrity": "sha512-ce09FVWv3PqObQsHFidyr1b1Sy8TlrhD+RAdqYlBs9xTDqp7NnecMqGrhz84PgmS3IDSIQccVjMg06TTWz32LA==", "signatures": [{"sig": "MEUCIDSW5t9KQaJOWMTHu2bTNTFFz9SMdxidbbWGUo6HhaNXAiEA6u22HE3nQzG/dC5/N9ajZON7VKOxETHD8nKNdxdez5I=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.bf591fe", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2647753}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.74e084a": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.74e084a", "dist": {"shasum": "026ff2202f92bf9264c53a809a4c2b6f307d1746", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.74e084a.tgz", "fileCount": 4, "integrity": "sha512-RTUbYrwfuZe8kkajD0492MCVjgN4Pkw00Y1x4EO5fICc1JEaIRdT9hiZHl4/JxIh9BFG5nFA2QRMu2y2cw/bNQ==", "signatures": [{"sig": "MEQCIA8+wKlpgZ7xAiuqYGaQoUMwE2BasOAzeeAz5JSsyO88AiAtQ360Af1akfa0Psku/Car4zUClqG5uv9DGX1JwYq0TA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.74e084a", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2647753}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "4.1.7": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "4.1.7", "dist": {"shasum": "6c695b870eb8a3f9a958d0afccc67b1b6ee7e78d", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-4.1.7.tgz", "fileCount": 4, "integrity": "sha512-HUiSiXQ9gLJBAPCMVRk2RT1ZrBjto7WvqsPBwUrNK2BcdSxMnk19h4pjZjI7zgPhDxlAbJSumTC4ljeA9y0tEw==", "signatures": [{"sig": "MEYCIQDS9E2Afcz5AkmyUDe4++itR7DXqsYBZbeNDecOECOauwIhANsqK4914IpRYCJEi4h8rJ6dFvfQnJLXNI1jpu/ZBJUt", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@4.1.7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2647736}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.d69604e": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.d69604e", "dist": {"shasum": "cd475a3d301d7c8065ea9d2462855e293915d882", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.d69604e.tgz", "fileCount": 4, "integrity": "sha512-d0oIYgb4/dGAmJeZJDDNnH0bitIjoQ6tFYfu/3VPP6bW4ixvc7yPoDA1iKXX+NHCvABxOmqD2xIdzt/Km7MOhw==", "signatures": [{"sig": "MEUCIQCtm5N2NoTS+cQol2SXz81sq+whKz5yH4PnFIcWXR/ASwIgQxzChr4a6J4MDs/viiMIc9D15XBwF+V/S6X8AWm8Yj4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.d69604e", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2647753}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.c7d368b": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.c7d368b", "dist": {"shasum": "984cac24745055d2f2f236c6a4bf914cf48bfdc9", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.c7d368b.tgz", "fileCount": 4, "integrity": "sha512-q7uLa/w8oOmXFgohKQsecqe2tUxoIezRnW8Q4J14G6yxjbVuvbI9yw79Q+OGIJHGj5CLfDNYRRAABG1/Gjp6WA==", "signatures": [{"sig": "MEUCIQDc62H74n1riatjo732eRSBV1vIt1Y00rPuvyFzXg9A2AIgd37+MfaDulMZKLAtWifnJunbXFbYxYZmIgJbXF/qv+0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.c7d368b", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2647753}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.71fb9cd": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.71fb9cd", "dist": {"shasum": "35a24de1b6cf6849be2e57723540aa595bf539d9", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.71fb9cd.tgz", "fileCount": 4, "integrity": "sha512-WWp6Vxm3B76gAlyPYWAFRAICuoI8S38J39Dj7yPA0d3D0tyBWsIesZNfJY4kDeXqQZkA/UhCbsy6V2zrxshDIw==", "signatures": [{"sig": "MEYCIQD5ordL2k5Q5/FThT2dUy0PhvP39vcwnYfLNv8/yIZwGAIhAKu6lnG/8zDrxQSuq9yyMotjt4FGqO2FERLAnQLXBd2e", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.71fb9cd", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2647753}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.a42251c": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.a42251c", "dist": {"shasum": "4cc936a4d1301e93da33679d212ea507b5708105", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.a42251c.tgz", "fileCount": 4, "integrity": "sha512-RQKaW/RfkIvjYqN0Gb7z1DwTy1HJaGdOgH2tEMOnpTrZesn628vyQJAbNTZohGR0CSSYMSTxmTMmN8Y0e8guNA==", "signatures": [{"sig": "MEUCIHFrNM1h4Ker9CwerX0E3ELgwtRbR5zZeDPt3o38tmdNAiEAmarYe8OUufHLcE8u8ETdos0vzt3p4r+Sw+2yNlw1Ans=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.a42251c", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2647753}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.9df5ba7": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.9df5ba7", "dist": {"shasum": "83e2fb7f55bdb86228d22009d246d18cafd50682", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.9df5ba7.tgz", "fileCount": 4, "integrity": "sha512-jhhIXMDEk+BCx1zucUTL8/g0Lc+mOf0usmEbORPvwuetPdVLRsVEI7guftf0uMZbYarayeYG09zBpKSIHZkNAQ==", "signatures": [{"sig": "MEYCIQCzfTFuuPdjNkyCYVl4UYR3GIwa/+zR3Wa8SSk6f47XzwIhAIje66+ExUlvfmkoGXMKW0ooG2QtUV8CZ7gnJLq/d2IH", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.9df5ba7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2647753}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.7013ca3": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.7013ca3", "dist": {"shasum": "9140e2288ef2e7ae45c7b3e39f7679086379f6bf", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.7013ca3.tgz", "fileCount": 4, "integrity": "sha512-LXAo+MsoR2N87yB0MU5vFkTw1HtE2szkDtBZtSN/cZZVEgvPMy9A5YMT0Y/fPE4B5jv5/RpGQqt1RoUpr6oclw==", "signatures": [{"sig": "MEUCIQDnIo5J9Pjn3YZGYdhi4NOIC53DnCCKsZ4ix7/wG7EgrgIgMEHZtEZMU78ySTYiG0RXOybIUWtnstPz7sfdqQ/a1ZI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.7013ca3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2647753}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.24ed64e": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.24ed64e", "dist": {"shasum": "3452628add23e12049f6bb2a54b86e1d87ca715b", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.24ed64e.tgz", "fileCount": 4, "integrity": "sha512-CtVjGc1Y8O99oSbFFHun1nooxXBwnWxhOwDQp/ZkZaMs6KjZsShkdTGwtjgUytzP4j5VRsxt1N3oFk5i0zgeaA==", "signatures": [{"sig": "MEQCIFxVmKeqrgmSHb2QIeoe2vMIT6gD4d1GUWuDmTFdAk65AiBVyCI7v5l2OLmHFetiUdfn8yWkJ8fPrVsSYbg55KS9fA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.24ed64e", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2647753}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.ed3cecd": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.ed3cecd", "dist": {"shasum": "1847cee7ca34ab175524b0a3eca6176296ad1e22", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.ed3cecd.tgz", "fileCount": 4, "integrity": "sha512-6qDPhmBuifhFA2UhWLHhWKOUF3vM/xLdnJPJcoAYblKkrGnfOTANNtYR0QSVUVbbBnY8XkZVMQ51e8NpEUjUbg==", "signatures": [{"sig": "MEQCIGVFptGn1cWqvrRjZ1EIwHt1QiblEgAY1yehv/06iA0lAiBCHZtrB3HWz6bGaDWnAJJ0JYy1VRZwVe/K+pHszBwS8A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.ed3cecd", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2647753}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.9cb3899": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.9cb3899", "dist": {"shasum": "e6115d1012f617cfbc686191519ba470de58b510", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.9cb3899.tgz", "fileCount": 4, "integrity": "sha512-wdg927CroiZ/TH5uQRQZ2Nmxl+Y0gWGtZchvPa1V5CuzVva2ABevD55PnqUQ0ybK9XV4vF9+37N1ttqgi81msg==", "signatures": [{"sig": "MEYCIQDu/VkZW45HQfU3/4ie8Dwi4KhZD1uGIMv7jzXK1XEhSwIhANLhRCo0upGZam0q2Y17ShSxu5LgmGOI1etd7FHGMwKP", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.9cb3899", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2647753}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.884f02c": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.884f02c", "dist": {"shasum": "2b4933f9599bee37c4392d60b3e65fdbbe0ca662", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.884f02c.tgz", "fileCount": 4, "integrity": "sha512-wf2f5EVa+p+P30Ml9PMpNJ/HC7/nyTUgN+5gO6ciuIZa5woFQm67jWofLskptplYmxYl4DUS7jdZ8FQL0O4DNQ==", "signatures": [{"sig": "MEMCIBc8Zbmpb5KfOu5igmNF3sYmn8+6qj76A6taOCYJEjpLAh8eV4ylVbuFArFjhnklND2XIIiwHEkdCky8JbO52tT6", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.884f02c", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2647753}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.8fcc633": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.8fcc633", "dist": {"shasum": "489560b3fd492ab1ebb6f0ad1724e36e9e993675", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.8fcc633.tgz", "fileCount": 4, "integrity": "sha512-7CtkEgc+2Tk8rw0g5L9f8j/eu8jC5LzYehGsIUjnlTUmQB1cRuADYnUEptUBvNrns5WToo0ABVpuOOyNrylSyA==", "signatures": [{"sig": "MEYCIQD2cn76vJg8rKE8QiO/EN+8Hirqoy8lP27p/aKnPuY0dAIhAKBLHPk/2Q8UcU+gy7TWTt1FL253Ol9hJ08SesaQD2Ua", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.8fcc633", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2647753}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.5d4e8f0": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.5d4e8f0", "dist": {"shasum": "89d6407148f19c5d115b46059244c3159ff3ea7e", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.5d4e8f0.tgz", "fileCount": 4, "integrity": "sha512-F8sBzFaQOJv1IZ/eK8mg/cj9rhDUpUzMXm7Fu4zvuyThqPxutLgOdGuQf6HVU2Ov/GLpCECxE8KdnYnr2Op5hA==", "signatures": [{"sig": "MEYCIQDX9L92byWp6HdJ6UGR9Z28KZYiUnmxxEYMMIjHUqicdAIhAJvSdgKOMMQKa3gbSgJlkTXA44nvXDu0KyhdV8V3ssdM", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.5d4e8f0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2647753}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.5131237": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.5131237", "dist": {"shasum": "5e394dabd3a41cd758d7e5bddc6568f4fef5c702", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.5131237.tgz", "fileCount": 4, "integrity": "sha512-BclByrf5Y3wxeBm5CoF4meOtM7LIkvEBXMjuec8E4gsJkj3BcwogiWPSAn4jdcFwb/kfYwZiNHrzYcQNE8UATA==", "signatures": [{"sig": "MEYCIQDJFknyHrjuMGDtJtS6K8jCqLlhjPJAaSdB6TI7fN/cSQIhAPsBZKg2LWz5YVsr68nDC46RnbLTBfzTsRsBQLgXrEFS", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.5131237", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2647753}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.1d4c263": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.1d4c263", "dist": {"shasum": "13a576f3c9aec26709ed372c813bc69d974bd6ee", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.1d4c263.tgz", "fileCount": 4, "integrity": "sha512-0cTCyuM2IfgryD4W3LweXgp646WEuIGmC7THtp38P3XdcTx/xVMwaILSNmCj4L6jnDtq1NgCyW8nkmUjcFgj3A==", "signatures": [{"sig": "MEUCIHhV+sKEFPikc1zONBWN29fHTQkUiErl3za8HEldgW9GAiEAkJi9zqXsNbxAm1Bsvy3P3e1TQWVd/IJ41HxIAsH2ZE8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.1d4c263", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2648265}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.a37f6ba": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.a37f6ba", "dist": {"shasum": "47168042bdbb567d31ec4d901f2ce1bb57ec9ee6", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.a37f6ba.tgz", "fileCount": 4, "integrity": "sha512-k4lorDmrOV8/7cg1a5SPcwQYkOxyIYa8mCyeBon+N7vV3KNYkidtKtn246sG4RdoWFD897zaWKT+SdjORCBb3Q==", "signatures": [{"sig": "MEUCIQCnkLgsF5Quzk79s75zDDgrt4sD59TZinqvUwgu0cKNkAIgHSEJ7WF2wXJqt3Y4p8rhfCWs+VegqiFctVYoRfbGd0I=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.a37f6ba", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2648265}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.58a6ad0": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.58a6ad0", "dist": {"shasum": "9a18f6a96328439cb7808f6b67afd19878dfe5a8", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.58a6ad0.tgz", "fileCount": 4, "integrity": "sha512-UP2Ovae+kTTKgkjen16m2GFt5ieAW8cm4PdxPo1xVCV6Mi0hng++gW5/5e6td4N92EgqWEBu3+WOah8SPQ1PyA==", "signatures": [{"sig": "MEUCIDUYqmnHLUKERZtAu8nawzPZPy3HOYu9+7csYJSQ4OwoAiEAu2a3CjFAzalBE/7v4Ke1g5GkBWA/M4VXnfRH4gmaXXE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.58a6ad0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2648265}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.4bfacb3": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.4bfacb3", "dist": {"shasum": "8cc642958a02a416191aa0d0201af5828d7fd4d2", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.4bfacb3.tgz", "fileCount": 4, "integrity": "sha512-ijSuOCBWDWtlUWkeVJagS4968ZyXAxUFwNDJuOBkwDiqaXbqVFiBjeffr+vHbu8QFIoLf2qoELt+v+U0nYtyQA==", "signatures": [{"sig": "MEUCIQC5uw8OpEdDBN991Sa6kCIf96+I6P2ESvgm1pGZYre5UgIgVk5Watyxj0hGv4kyTRinCqeXTc29rJGcqgv8dcSzsLA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.4bfacb3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2648265}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.193eb84": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.193eb84", "dist": {"shasum": "c479230993dee744f5c927bb18ad6842044fec72", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.193eb84.tgz", "fileCount": 4, "integrity": "sha512-GrpQRfGCHobXM769VHtAQTm+/GadhfitkpYP8drSJaR4e6o3m4n05BQWViOUCb06jqbngZGfl13dAsjzUnZFWQ==", "signatures": [{"sig": "MEUCICjjh/viorQfnGdMD5CA9znwmCIzrFunZ4JjyA7ggjj9AiEA/6OBbU+DAWZdFEXg4dJyADJusr5E4DoNIbcdcOoW0u0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.193eb84", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2648265}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "4.1.8": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "4.1.8", "dist": {"shasum": "77521f23f91604c587736927fd2cb526667b7344", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-4.1.8.tgz", "fileCount": 4, "integrity": "sha512-7GmYk1n28teDHUjPlIx4Z6Z4hHEgvP5ZW2QS9ygnDAdI/myh3HTHjDqtSqgu1BpRoI4OiLx+fThAyA1JePoENA==", "signatures": [{"sig": "MEUCIH+/JAMMRQ83DhFAFrRqVwpnml3cgeOqdWkaR6e4w9S5AiEAxHUYBLc3vTMFu5XQfIozDNyl+EAM0EuafZGs1hIDHkM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@4.1.8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2648248}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.3c629de": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.3c629de", "dist": {"shasum": "70b9397d26dfbe26c0f4b101d3e5e2f01fcfa2ea", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.3c629de.tgz", "fileCount": 4, "integrity": "sha512-un1got750+bXn0nlp7W8QD2PGbyKqjulHHGdvIIDbvL7+n9sTwRN2BEHPC2kpllJexrQS8wQhrqoNP+1QWjPeQ==", "signatures": [{"sig": "MEUCIQCNoC7dJdC5AofROnK9zwzwDvNFy6T2TW9Qd96wgDYa0AIgSBeSrxELFfiDgxk9zdUhzN9mYn2EGACWEW/ZqKN5bIg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.3c629de", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2648265}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.31c0a21": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.31c0a21", "dist": {"shasum": "50382eeea78c11c4956063788871f0df1658e729", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.31c0a21.tgz", "fileCount": 4, "integrity": "sha512-byu5+p5O/Rb60CPfrmpEq9Ok6Pdv021h2P+89G+HcXreROgvqkxsVPMSIrwVOds1WlvtiLRUZI8Y+jT5j0m7rg==", "signatures": [{"sig": "MEQCIDEBYmUV6O28PTosZO54kcF1zk+LOqTrii3YfwXGBHK6AiAOjtlBRrukZvX1hqLVuqjBak8ZejOE2sHJnUJwECEEkw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.31c0a21", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2648265}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.54c86d4": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.54c86d4", "dist": {"shasum": "4208a977865b3c6aeb84ecf2db3786d417d0176d", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.54c86d4.tgz", "fileCount": 4, "integrity": "sha512-jtFx6fnL2yeI8m1F4+JiIa+MXhxJVS3/4IC3i49yPXAphDTc8uN5XQTfIWLLwSS26iypfHJY+t8YShNY8qeFPw==", "signatures": [{"sig": "MEUCIQDD2MftQeQPqMrCyT8vwdfYNad8CK9puq2k7zGhhFtGkwIgR7MuXlXemZl12pLdxTHQyp9h/A8TlU5bN850zFwCLw0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.54c86d4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2648265}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.191195a": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.191195a", "dist": {"shasum": "77c3811aacfafc5ddbee3f474d94d0a3e4bfd5ab", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.191195a.tgz", "fileCount": 4, "integrity": "sha512-bajKj9OrJsXpN9bCDVQ4/meupZnXBI4nOFng8gtOsyArRy+i27lY3Jkj5xCpmlJpb9JkS8TfZIbEw7FVjZCKzg==", "signatures": [{"sig": "MEUCIDWk0rHrjgFzIBiPjuRw1FN2dYrHwjL+3cjUPMFhC9NPAiEA1Pd/hEXFxWFgTOeag0u+y5iiPYzPqobL7YVVUHKdpxE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.191195a", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2648265}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.b3fde17": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.b3fde17", "dist": {"shasum": "5ee61a3e941c8ec13e509965de11b34b29519509", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.b3fde17.tgz", "fileCount": 4, "integrity": "sha512-UXNsd0/GeCWniB5c+n5wDqOukzWL0617YF3QJSUCCRWjXsIXJk6eV+SR4Kh/Rs8LpGQFfDCv0t/0tYTO4Np//A==", "signatures": [{"sig": "MEUCIQDP8bCCqneJnpTe2Ql5P86q+Uxv7l7R7/SMN0pL3CQENgIgXj/fchDCbAZhKt3YEpr0g1WPLykRuxAkcHJPRsaKeMc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.b3fde17", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2648265}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.f425720": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.f425720", "dist": {"shasum": "55185c17800f7e829a552c4d0f58387c86f64725", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.f425720.tgz", "fileCount": 4, "integrity": "sha512-j6mn2uoTcQgfxyDnsMDgVm4ot4JI2liLMGkJfqVndM97Wfox0iF3FvchzFRG0kkkXfkSRwpJECsL5ufGqHXCXQ==", "signatures": [{"sig": "MEYCIQDxjLx0Ha4cYUPx/ptgsk2A4hw0YHxLWrfKow0+2xyHtAIhAJnbpdmn6KANvzGMfqmrrAycdDSy8nADzWiDVOiqTrFm", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.f425720", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2648265}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.0c1c0c4": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.0c1c0c4", "dist": {"shasum": "a2781f5a0797f9d42c5cd701466c4bc77e91e63f", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.0c1c0c4.tgz", "fileCount": 4, "integrity": "sha512-n6e830DaJoTk8gKK4BKFeSmAzIeydfRQM5ajGCluFaE8ea0JCxbi+WkRSYtKKUJKO2Dioh7O7GhmDRXxfVoGQQ==", "signatures": [{"sig": "MEUCIQD+E0t5DyjXVO3PjscBoKu3P4XB4SlLGQg6Ym1txUSs5wIgehavib/9lnRj1v4jH44xRiGwnS3Q9Us6D51O63Abc8w=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.0c1c0c4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2648265}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.8bfbac5": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.8bfbac5", "dist": {"shasum": "6c35f61c94d6c3fea4d5d3bd4357f03648f8af0d", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.8bfbac5.tgz", "fileCount": 4, "integrity": "sha512-Dj4U4NhnyIZI3+3Sk9IojxCjsrSuwjK9TrABVQ6vE1gP6TFqtcEC72V8Fz7p+U80HT1Ci3Syw29abhZrIO8UqA==", "signatures": [{"sig": "MEUCIEnLxw5RheCGY0sLiGZ6+JPB31d2e7Xj+KILr9aMd2/DAiEA+2qYS4kozs2gzjw7cI6vkgX44zbKCBHfnsoHgRGQPzE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.8bfbac5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2648265}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.288ab3e": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.288ab3e", "dist": {"shasum": "7430c8236e561efae4d798407a3173ea92d1eb3d", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.288ab3e.tgz", "fileCount": 4, "integrity": "sha512-dJcM+UjVgG/DnWdBPWqtaWgGrA1tOSp/4cAxbpPoSiL6q3/G0N2MuyDrRvJ/LEAFsgVxcURhzs+trJKo8s3vwA==", "signatures": [{"sig": "MEYCIQDTYufwnijthgPw1necZ5YoytJECm5DFEKnXimR8d8LKwIhANPvvfPSDrjkWUmCJCXclXwFSkq9uRwOYev/OJGoIrwq", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.288ab3e", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2648265}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.63f6a6c": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.63f6a6c", "dist": {"shasum": "28ac1625e034c3a20f6bec4e4dc722503cabd697", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.63f6a6c.tgz", "fileCount": 4, "integrity": "sha512-ZUkwroSEPwNJ3jT8Zq+kr5AwbSbfFXR4VKzqTc8TGPsy5A4wsq40l+i2PL62xnEZhW/7a/aCKGL/XzzEM7xQ/Q==", "signatures": [{"sig": "MEQCICpRIR30HsNnw2T45PlQGQcqfnjc9CgSUhUn4v114+laAiAreKIvguOPHaqfiTWg6xYPuG4HPjhjCgY2NX5MR8pIYw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.63f6a6c", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2648265}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.21ece6c": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.21ece6c", "dist": {"shasum": "8626a425d60f2031bb9e39cf0f88fd841973df5e", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.21ece6c.tgz", "fileCount": 4, "integrity": "sha512-7Nhmf03YUfUYjAkEbQjBT42DCnrFWvTQEH22h93/0nGtfCA/Pl3yZpywQ/hbTwQw4hAy8JDkUD/cAYc0URoBhQ==", "signatures": [{"sig": "MEUCIQD4zWOXrrtqxi8LGTAc8AD5L1oTpovvy3AgUj20LeTPyQIgRfFcC8lZsEh6s+9ys8U8jngwpmdcC5cNESUE6zz+na0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.21ece6c", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2648265}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.ada85b1": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.ada85b1", "dist": {"shasum": "6ee3d96eb57f37fd5b8475e2139bc46775067ecf", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.ada85b1.tgz", "fileCount": 4, "integrity": "sha512-MBRhI94i/VBbG7cj8zwClTn/d13/I1aqiVC05VzJuemS3WEam/asXGmxr5QEW+63kCQazDl9NJqichiFQOEtCw==", "signatures": [{"sig": "MEUCIQCsrxCvOcGn498apZHAt0MVEEJabTDqTdfFR+rY9rjgEQIgRwZeweAmCTGdFiH6JWUEtEr/Stu6KU88rYSsdxpr9b8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.ada85b1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2648265}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.f0f42f6": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.f0f42f6", "dist": {"shasum": "5060c920554d2ad391f9ce636008298f60748b1b", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.f0f42f6.tgz", "fileCount": 4, "integrity": "sha512-lyuPjCpe/ro4ooYyeRzvdY/ok4/jHPMgPX2bcV5WaWm+c6f71slmeHUQaBrjLaEgM39y7J3T7y6zOE9MQ+2TZw==", "signatures": [{"sig": "MEUCIQDYgdFyMSdhQcGqRFKO4S8Wz69yv9dBjG04n/fj3CoRsQIgZMYCbarxcR1uHj4fvgHffHwZL5c61wxDoqC4UJTi3EA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.f0f42f6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2648265}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.fd95af4": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.fd95af4", "dist": {"shasum": "486d0876a772cbdd40fd35f75138fa4524af467f", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.fd95af4.tgz", "fileCount": 4, "integrity": "sha512-IzkOKAa/q46mt2XhlmCJUX3gbfgOx0mAmG6qkLa0SMJXCfXtgHcvS8epR2wbn6kAYuND8+CWkRHReE7cNqRJYg==", "signatures": [{"sig": "MEUCICIb4MQCRzEFhYOoXjDNNkIalol3FzI0ZxpmZnIXf/b1AiEAgPZl/4ZcB3vmcwojLONx3+NqGkirlsEigLJJcawHM4c=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.fd95af4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2648265}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.bea843c": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.bea843c", "dist": {"shasum": "9d0b42a5841e2d080d0822e35b158847c6fd2e60", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.bea843c.tgz", "fileCount": 4, "integrity": "sha512-HWZySeymv0JAUa3b05xUbMFV+H4BF07nk00bBqABqctAmVzKlTHxBkqW52SsTcvCUOPFZ8509wTLvElffn+E4Q==", "signatures": [{"sig": "MEYCIQCXcWOzBz/lAWpROF5iTWRgvXPgfgahb4Py6XuP720f1AIhALzboq2N9rTNfb5dVejNc7q/zupdCXyIORHud2qNr1ac", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.bea843c", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2648265}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.da08956": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.da08956", "dist": {"shasum": "77199d042f5d5799b2fd6e0e0a9a2d1c8c09bc6d", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.da08956.tgz", "fileCount": 4, "integrity": "sha512-p7h3pLWeVKi1GcfJDIu5wMkzwSyjaD0XMtf2gYH3sc7qzswldynwM2KdNIGvqjAsl5kfiYaqzYH56XON2GeVSg==", "signatures": [{"sig": "MEUCIQC0hJeEwMD+PaKKbAEzHVsr9cdvSiCDndV9diviHEep3gIgBXrxazcQTg4GBYpJZkcrbkyvKc4RysDrRnYQHQTJyO8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.da08956", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2648265}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.aa817fb": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.aa817fb", "dist": {"shasum": "b72bc946e4266c1b2264cf9fe8b21905ec7ec83b", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.aa817fb.tgz", "fileCount": 4, "integrity": "sha512-RKrRO+E/wdKtHjJ5TyHQ5lSnv1HJCnNLIIqQNyhm4PpdJoqHw1IOK6zx+XGcXadmMS3tAU47cpSydHrIrgCv+g==", "signatures": [{"sig": "MEQCIBmCAYEEWsSnW9meMZ9z1aHOrUcNz1tnggoaTbRkWlXMAiAXh7KYQANM7H/ztF4ETTeCTtqpsinNdlE0nWohUODGbQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.aa817fb", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2648265}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.b88371a": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.b88371a", "dist": {"shasum": "ed3d4d1bf526b8c7a32df64578524b440c0f55d7", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.b88371a.tgz", "fileCount": 4, "integrity": "sha512-1XHo9+gM4oev8T65r4AKcIhZQFILOr15despbGexYBpS2qglGcjc/ohF9ms623Dc8E8mthMmq2MJ3Q1nZ4SWGA==", "signatures": [{"sig": "MEUCIE0I9CWvV/M7MjgPIFzVoXtLuDgwC3E+U5Rp5/ahjf8mAiEAw0hIREJUQbfeKqq8hUygwyuX7LlpcAE3WT4434l30FI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.b88371a", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2648265}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "4.1.9": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "4.1.9", "dist": {"shasum": "4bc27e3c72f698a866c3687dc6d81c06c01c58de", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-4.1.9.tgz", "fileCount": 4, "integrity": "sha512-Eq9FZzZe/NPkUiSMY+eY7r5l7msuFlm6wC6lnV11m8885z0vs9zx48AKTfw0UbVecTRV5wMxKb3Kmzx2LoUIWg==", "signatures": [{"sig": "MEQCIC/ASvOCGPY6dpnaq4zmiZWXnXB5g2Y4BfWh5vNIPESlAiAAlgE9d/kLFBGZb+KjfatB8EtY6rAiqsMXiXmqdRuTPQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@4.1.9", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2648248}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.427649e": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.427649e", "dist": {"shasum": "7cca184c365db1ba724a64d32810e7667a5fdfa8", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.427649e.tgz", "fileCount": 4, "integrity": "sha512-shHb8ObufajVygdL2TRXOIrSI6gxQ0BcnHYHDVFHGMvHJuMTCzQoDiTbhayk4Z77qkJ8jM0HqCv1NHjhw/acSQ==", "signatures": [{"sig": "MEYCIQCTYR31UPQQ63cPy8s6XJfQTzvSQl87a7M6/I54w4SvaAIhAMX6o6oJLw4Vp89O6cMApgnFHjtUInHNbpno5vKUnyO3", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.427649e", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2648265}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.ddb0bef": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.ddb0bef", "dist": {"shasum": "8bc6c02c1ac93c091d71c11370615543e68f4f00", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.ddb0bef.tgz", "fileCount": 4, "integrity": "sha512-smh6W+u29fS4hlsOsFpFoSXTfE65B9GmoZuegMmfyeRHQkeTg64RJF9fnslhCR+jysIDcYZSHp/63NYY6kdbHQ==", "signatures": [{"sig": "MEYCIQCdAo1umQDuNxK1RKvEZAJoa+zuEOl8f0k4OWmhlNybcAIhAMWDW1XJJURDOLZvlIqRRpa9Au05naOUakai0GwdNrln", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.ddb0bef", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2648265}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.d06bbb8": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.d06bbb8", "dist": {"shasum": "990b360749238689e9ab364d1a340526c6f84b40", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.d06bbb8.tgz", "fileCount": 4, "integrity": "sha512-JWmWfGzNu1saqGLAcy5ejqKsIGkxSNXrD9xGqFQuesFphK0z+22p7q4O6GUXRqhN19Nrgq41delA9Ym9jmvrIw==", "signatures": [{"sig": "MEYCIQDkvKa4lh9XUfKBmmT4NhOlu+EaTGIZwUEj1uAN4sRYKwIhALWPXfkdmnlYXsscZI46ZA+uTEWgSHni7usQER+FMzIK", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.d06bbb8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2648265}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "4.1.10": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "4.1.10", "dist": {"shasum": "e1663b5a95425f0f458f616399ed9f6707d4a786", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-4.1.10.tgz", "fileCount": 4, "integrity": "sha512-i1Iwg9gRbwNVOCYmnigWCCgow8nDWSFmeTUU5nbNx3rqbe4p0kRbEqLwLJbYZKmSSp23g4N6rCDmm7OuPBXhDA==", "signatures": [{"sig": "MEUCIFM3PXZ4L0LQNGLZL+30lqkVCxfkU/c5/12rPBew9M19AiEAveHp5u7KZVOW65Dp3yyCQdQZD90lA9pjfejBeEIOJuY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@4.1.10", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2648249}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.2ebaff2": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.2ebaff2", "dist": {"shasum": "06ae681c6cee4d2b0f389629f969343da0cc28ed", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.2ebaff2.tgz", "fileCount": 4, "integrity": "sha512-cdbeg1Vdy0Vl0b65hsuy68qyeYJ0Rya2mUyHxGMYq3ndgnVUDhagR9ssJ0Dpx0dcTrsHWqNl/eIcX/7KeLOMJw==", "signatures": [{"sig": "MEUCIEfCbRa/UQ2FTqVCYFVjbTgMJ9LATxKWQx0jBD86SZX/AiEA3mEORNo9p8UgEcBJChW+36L/V0njIhxuGIom/hsM4WY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.2ebaff2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2648265}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.7f97179": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.7f97179", "dist": {"shasum": "d94dd151377dcdc5aa9952ccaa9762333156e833", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.7f97179.tgz", "fileCount": 4, "integrity": "sha512-Dw2pXsd+MiE9pEbgAQcuemeF8xIKxNaPdGzjfg8I//6iEwdSwjvbECGjxbAkZ6VMOoMZtMf1BHhAvXiexxZFHg==", "signatures": [{"sig": "MEQCIDQIgXPmjtO055IGIS73OhkNu8POK5xMH/tlRY+Iyue4AiB8HkkJ2nNCNxqeivpc6zaOP5JQO5Y541LbcYgkABZZoQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.7f97179", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2648265}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.bab16ae": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.bab16ae", "dist": {"shasum": "7b22cb651158675bfe5d16a8e7ca54a848fedf29", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.bab16ae.tgz", "fileCount": 4, "integrity": "sha512-JhVhLtI36Ep+zUvWiOZQRKw17SzFhJwkG3nbBYAhN1hckZkDm8gMahYKJBfEnldSVfQgFXLUoLLtAz+T9YXbtg==", "signatures": [{"sig": "MEUCIBV9xkXOsELdE9rXzgiYRacHWIq6Wn++XUlFAPPNdBQ9AiEAtrCCSPN/uWNYZb+fTa/jAFpbatWCF/BP9VuP5ztd5+U=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.bab16ae", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2648265}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.4453496": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.4453496", "dist": {"shasum": "fe40855165c2766bf2dd82ca7e6571a5ef245dd7", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.4453496.tgz", "fileCount": 4, "integrity": "sha512-Ai1Uo8qcOZ337DdgGlbn1m0UPhxnb5oEqGi9S/oHWeRkgPN6oGcVdIItPezxGiPYqdH36ZHxGZcJXXiYbEgRtA==", "signatures": [{"sig": "MEYCIQCJec/M6wZLrB9/p4RURrAAHrGOAVuiHZ+Dq9z4z/haugIhAMFgi8MpWENhJY5tGsKMJs4TNwnXWFbFTtH60Os9FG7V", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.4453496", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2648265}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.63b5d7b": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.63b5d7b", "dist": {"shasum": "350e877f9d3f58445cbfddd4271f5594a93780e6", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.63b5d7b.tgz", "fileCount": 4, "integrity": "sha512-jm/jZkS/2Kiq+mnl0GYhTC6CCiL8KZS0teMYJce18uxLAmEDzgp+qpEz1ugRNvfX2dmtkT8OFeUI4soW5Jy+cw==", "signatures": [{"sig": "MEQCIAZtVtSsvkoqtt5aZj8Ny8Mtj19bHeCBy8Z3kzeaVSDsAiArVgviKLafqvSnOAxCEHx8BBmhSrtvBpVigFap8L8yYw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.63b5d7b", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2648265}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.75cbfc2": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.75cbfc2", "dist": {"shasum": "2c1341a1132e432db7e6e68daf8f136ab370a80f", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.75cbfc2.tgz", "fileCount": 4, "integrity": "sha512-FExntZwb+WC6IxWwgKaelIdBBItZ5ViTh/v+0NDLeP42PmTXCBIJKAWmfUyC9+ea0qQys5EmRAdmuInTb0qvyQ==", "signatures": [{"sig": "MEUCICCcWTjCEXGcfEZ2Tc0ernv9mvrP8y8R47K2UosVOOi2AiEAlHkjockBfF5bLIqxqnvztQRlWRuhoLHdcL+RRHGiyio=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.75cbfc2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2648265}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.f4a7eea": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.f4a7eea", "dist": {"shasum": "af08338f86f9e53ecaf2f5d0e330565d01181a63", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.f4a7eea.tgz", "fileCount": 4, "integrity": "sha512-wz/8yX+jqyi/Vc1OICB/WHTmef0UUL8f9jUcs7XfCRYmIp+xzr4J3w/s7Z6YeXMVlk066Yh/h/2LG9W/Iw5ZoQ==", "signatures": [{"sig": "MEQCIGn0aSDZu8OvTeXir9oLgNO/1J7DglF/3658yCOIpACDAiARPIDk0ORQ1TKyCtl/C8zoAvhltzV2t+deWvUe6GAgFw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.f4a7eea", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2648265}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}, "0.0.0-insiders.c5a997c": {"name": "@tailwindcss/oxide-win32-arm64-msvc", "version": "0.0.0-insiders.c5a997c", "dist": {"integrity": "sha512-FMdPFI69AgLcXELdBawAou0HKB2mHTuwxbf7hEM2ipjLiZzKLHYmfnNJ90fQKE3pupO9t+M5EFEjANda6OnQPw==", "shasum": "7b659bade96e2f1c9b72fb7efe1262fcc8ae4cd3", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-0.0.0-insiders.c5a997c.tgz", "fileCount": 4, "unpackedSize": 2648265, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-win32-arm64-msvc@0.0.0-insiders.c5a997c", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCjlW09mwRlUCW9fNyK+bi6Wtcri8bPRG70HJjnv03ZawIgelPgBeJ8ij6DrliC4YBRqcFc7g2pF5Yp19ykjZzidHs="}]}, "engines": {"node": ">= 10"}, "os": ["win32"], "cpu": ["arm64"]}}, "modified": "2025-06-19T19:00:39.239Z"}