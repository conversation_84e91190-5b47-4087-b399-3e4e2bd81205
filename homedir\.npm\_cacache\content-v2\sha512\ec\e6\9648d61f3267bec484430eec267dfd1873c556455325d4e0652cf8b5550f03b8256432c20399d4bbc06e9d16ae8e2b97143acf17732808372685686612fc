{"_id": "shebang-command", "_rev": "9-7bdded90db21b23fc40fc28e11256ba5", "name": "shebang-command", "description": "Get the command from a shebang", "dist-tags": {"latest": "2.0.0"}, "versions": {"1.0.0": {"name": "shebang-command", "version": "1.0.0", "description": "Get the command from a shebang", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/kevva/shebang-command.git"}, "author": {"name": "<PERSON>", "email": "kevin<PERSON><PERSON><PERSON>@gmail.com", "url": "github.com/kevva"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["cmd", "command", "parse", "shebang"], "dependencies": {"shebang-regex": "^1.0.0"}, "devDependencies": {"ava": "*", "xo": "*"}, "xo": {"ignores": ["test.js"]}, "gitHead": "3e8916b560d65c8a23cb1ade598b65a8217fd59b", "bugs": {"url": "https://github.com/kevva/shebang-command/issues"}, "homepage": "https://github.com/kevva/shebang-command#readme", "_id": "shebang-command@1.0.0", "_shasum": "f5982f749b7bff0371127a947cb7d99ab64c142c", "_from": ".", "_npmVersion": "3.5.1", "_nodeVersion": "5.1.1", "_npmUser": {"name": "k<PERSON><PERSON>", "email": "kevin<PERSON><PERSON><PERSON>@gmail.com"}, "dist": {"shasum": "f5982f749b7bff0371127a947cb7d99ab64c142c", "tarball": "https://registry.npmjs.org/shebang-command/-/shebang-command-1.0.0.tgz", "integrity": "sha512-h5gnT0WELZMZj5KZ25gjOBx2UeZ7czNOQa6AJgVtsuj0p8DJ1mnVQoddpfkQ5MYosmAfKkyTF4uhnPTU2VCxiw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDLuRrHmtxhe7w7Gdx0aMmQrvyJ9a2hxUVtwyEEgik6vAIgBu88QkmYAyLk8c1iv6rhys5A0FgAUiJWwgVeEx1wk7o="}]}, "maintainers": [{"name": "k<PERSON><PERSON>", "email": "kevin<PERSON><PERSON><PERSON>@gmail.com"}], "directories": {}}, "1.1.0": {"name": "shebang-command", "version": "1.1.0", "description": "Get the command from a shebang", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/kevva/shebang-command.git"}, "author": {"name": "<PERSON>", "email": "kevin<PERSON><PERSON><PERSON>@gmail.com", "url": "github.com/kevva"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["cmd", "command", "parse", "shebang"], "dependencies": {"shebang-regex": "^1.0.0"}, "devDependencies": {"ava": "*", "xo": "*"}, "xo": {"ignores": ["test.js"]}, "gitHead": "18fc73453b88e4e964bb3e1fac3ae182a592b29b", "bugs": {"url": "https://github.com/kevva/shebang-command/issues"}, "homepage": "https://github.com/kevva/shebang-command#readme", "_id": "shebang-command@1.1.0", "_shasum": "dfccfee5147efa8c280055a959b699c9face4556", "_from": ".", "_npmVersion": "3.7.0", "_nodeVersion": "5.9.0", "_npmUser": {"name": "k<PERSON><PERSON>", "email": "kevin<PERSON><PERSON><PERSON>@gmail.com"}, "dist": {"shasum": "dfccfee5147efa8c280055a959b699c9face4556", "tarball": "https://registry.npmjs.org/shebang-command/-/shebang-command-1.1.0.tgz", "integrity": "sha512-jteqK3ZnSWdwTGS+BpO7izPhx7Gh7NfVgCAhgRVbS558cBnjYj6nJ69mM0MtsxK4K3ODzrEg0BmiiTM+wINnKA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDLN+3lXXTWHR+lbMq57zwsRn6qZW3frkndL1osUWhDUwIhAMbV4HzMwpivOnbiCrukSFhXB4HX5hPogEM69afpTc+g"}]}, "maintainers": [{"name": "k<PERSON><PERSON>", "email": "kevin<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/shebang-command-1.1.0.tgz_1460047240896_0.7961676171980798"}, "directories": {}}, "1.2.0": {"name": "shebang-command", "version": "1.2.0", "description": "Get the command from a shebang", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/kevva/shebang-command.git"}, "author": {"name": "<PERSON>", "email": "kevin<PERSON><PERSON><PERSON>@gmail.com", "url": "github.com/kevva"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["cmd", "command", "parse", "shebang"], "dependencies": {"shebang-regex": "^1.0.0"}, "devDependencies": {"ava": "*", "xo": "*"}, "xo": {"ignores": ["test.js"]}, "gitHead": "01de9b7d355f21e00417650a6fb1eb56321bc23c", "bugs": {"url": "https://github.com/kevva/shebang-command/issues"}, "homepage": "https://github.com/kevva/shebang-command#readme", "_id": "shebang-command@1.2.0", "_shasum": "44aac65b695b03398968c39f363fee5deafdf1ea", "_from": ".", "_npmVersion": "3.10.6", "_nodeVersion": "6.6.0", "_npmUser": {"name": "k<PERSON><PERSON>", "email": "kevin<PERSON><PERSON><PERSON>@gmail.com"}, "dist": {"shasum": "44aac65b695b03398968c39f363fee5deafdf1ea", "tarball": "https://registry.npmjs.org/shebang-command/-/shebang-command-1.2.0.tgz", "integrity": "sha512-EV3L1+UQWGor21OmnvojK36mhg+TyIKDh3iFBKBohr5xeXIhNBcx8oWdgkTEEQ+BEFFYdLRuqMfd5L84N1V5Vg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCgSopTfjAnMXoDCjmax5R4af/XRcr1cfGgVK3kih8zIgIgbl3xlJnri72mABew1nAx9AUUg1hjE5DmTmkFcV/9/+o="}]}, "maintainers": [{"name": "k<PERSON><PERSON>", "email": "kevin<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/shebang-command-1.2.0.tgz_1474530105733_0.9689246460329741"}, "directories": {}}, "2.0.0": {"name": "shebang-command", "version": "2.0.0", "description": "Get the command from a shebang", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/kevva/shebang-command.git"}, "author": {"name": "<PERSON>", "email": "kevin<PERSON><PERSON><PERSON>@gmail.com", "url": "github.com/kevva"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava"}, "keywords": ["cmd", "command", "parse", "shebang"], "dependencies": {"shebang-regex": "^3.0.0"}, "devDependencies": {"ava": "^2.3.0", "xo": "^0.24.0"}, "gitHead": "003c4c7d6882d029aa8b3e5777716d726894d734", "bugs": {"url": "https://github.com/kevva/shebang-command/issues"}, "homepage": "https://github.com/kevva/shebang-command#readme", "_id": "shebang-command@2.0.0", "_nodeVersion": "12.10.0", "_npmVersion": "6.10.3", "dist": {"integrity": "sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==", "shasum": "ccd0af4f8835fbdc265b82461aaf0c36663f34ea", "tarball": "https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz", "fileCount": 4, "unpackedSize": 2556, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdcnLmCRA9TVsSAnZWagAAROUP/3bwpa2H+ex3cAS2e/+8\n5eIXgsHtrSb03h3rZ4rDbpeSBgcpZaH3hBCLTpptdDT+CU781SZI9Dk+XZLJ\npD3f/FT9tt9JSkIUlVqBi1lR2zSY+Ao2cdZMgz6eGW0DN1lIWX6+39p2D3I+\nX09wEatT/wxLRGmu6cjrcSV4KxcvrikLXfSWC5xF892Rzkhs+P6n79KLgsoU\niISEGGg91MPQEB2hyMRCBzFgSTo3UPVtRNaX4joz6CU8Xc9+FSLq/bKLWEUy\nZmPAnqr0B5GhMtqge8bG9NNU/RMg9LV30FUZt0wVCPwWYewxXayOONXmmncw\nudV/EgufhR9M5af2Ah3oJut0BVtTAol1J0+Uvfc3xNjrU/LYsldUHYd/lv8o\ngXL4eg21xqbh+6MmTViC1pyeoY/S0K2YErgii9ZCraf0MAfS+Goj3RaEv974\nNWE+73eyFtPgdTebV2qkCkV+fhCGPOIb4dOIVlcIswyPOBLDQDdSkZQpR4Ja\nSIkNTAFI7CnvEDt0CRjDiEKIdq6t21g6zSBEUmt4eGy8yuG+IJMl294T0NWv\nB+YUWol5Q0KKNopeU2Ss863DuVbL+o2jkP1zkz1RCqzWtwtU3KVfHMi/53tA\ntUUFXbfNT2+dIfRZ+AvBcowtHF+BiIKfQ+C+RH7KQvHi5zQIi7+Vl5t6rC7M\noLaS\r\n=x6EW\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC25PLpUCo3H8DMi9MpLxwheAbym3M3S5MW5QaCZpOUEwIhAIka9U63aHBdDqIcMUiGesgVKHjIiySGTAsxdQJx7wSP"}]}, "maintainers": [{"name": "k<PERSON><PERSON>", "email": "kevin<PERSON><PERSON><PERSON>@gmail.com"}], "_npmUser": {"name": "k<PERSON><PERSON>", "email": "kevin<PERSON><PERSON><PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/shebang-command_2.0.0_1567781605734_0.5864042905313611"}, "_hasShrinkwrap": false}}, "readme": "# shebang-command [![Build Status](https://travis-ci.org/kevva/shebang-command.svg?branch=master)](https://travis-ci.org/kevva/shebang-command)\n\n> Get the command from a shebang\n\n\n## Install\n\n```\n$ npm install shebang-command\n```\n\n\n## Usage\n\n```js\nconst shebangCommand = require('shebang-command');\n\nshebangCommand('#!/usr/bin/env node');\n//=> 'node'\n\nshebangCommand('#!/bin/bash');\n//=> 'bash'\n```\n\n\n## API\n\n### shebangCommand(string)\n\n#### string\n\nType: `string`\n\nString containing a shebang.\n", "maintainers": [{"name": "k<PERSON><PERSON>", "email": "kevin<PERSON><PERSON><PERSON>@gmail.com"}], "time": {"modified": "2023-06-22T16:33:55.946Z", "created": "2015-12-04T12:34:38.703Z", "1.0.0": "2015-12-04T12:34:38.703Z", "1.1.0": "2016-04-07T16:40:43.453Z", "1.2.0": "2016-09-22T07:41:46.448Z", "2.0.0": "2019-09-06T14:53:25.901Z"}, "homepage": "https://github.com/kevva/shebang-command#readme", "keywords": ["cmd", "command", "parse", "shebang"], "repository": {"type": "git", "url": "git+https://github.com/kevva/shebang-command.git"}, "author": {"name": "<PERSON>", "email": "kevin<PERSON><PERSON><PERSON>@gmail.com", "url": "github.com/kevva"}, "bugs": {"url": "https://github.com/kevva/shebang-command/issues"}, "license": "MIT", "readmeFilename": "readme.md", "users": {"kakaman": true, "flumpus-dev": true}}