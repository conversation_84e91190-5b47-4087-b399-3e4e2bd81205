{"_id": "@webpack-cli/serve", "_rev": "41-2e4ce71913036f8e4aed5d67a532f7c2", "name": "@webpack-cli/serve", "dist-tags": {"beta": "1.0.1-alpha.5", "next": "1.0.1-rc.1", "latest": "3.0.1"}, "versions": {"0.0.4": {"name": "@webpack-cli/serve", "version": "0.0.4", "keywords": [], "author": "", "license": "MIT", "_id": "@webpack-cli/serve@0.0.4", "maintainers": [{"name": "ev1stensberg", "email": "<EMAIL>"}], "dist": {"shasum": "099e2c9255b6a0d71b3fe51710816462635b42f2", "tarball": "https://registry.npmjs.org/@webpack-cli/serve/-/serve-0.0.4.tgz", "fileCount": 2, "integrity": "sha512-CkteBMpxE+yBUuuZZKgl/smH0gjdsUW5uekmkuvOrolcqRuD5J2ChkO6UHzmYXgg3IDG+roZsqfUXGbeStxpPw==", "signatures": [{"sig": "MEYCIQC7cAJ/WPHO2a8Cbh/PghDHK02mAefFy0Jtgo4lSHvmSAIhAIR/pfCH97qgnqKYRh605f6seBT4eUr1jRfCngONoKvt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4758, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa/ZLjCRA9TVsSAnZWagAA2QsQAJ5FidR3tpbHYCkW0nud\ng6lnf1amVf+6P2cZSYLShWyZBX9XTJY3sNZfx9Wic8kCdJBXfTxCZbyx73rc\nvjMFDvB58El//z/6aH8K1y2z6RZqZZrGhMCVFUlvEauXZec7u1/wUY8QFaJU\ndE7gcSwM5nvryq3kQUybeAJyQN8ucceY9IFJ+uQld2B//7Yn6vv2SBzk1ZQU\nfJAxpZDA66hT4jBE91srsDwxu/Q10bZDWa9jvAbXwtOGZUO7TRkDW4bw+PYg\nzdD6v9/zR4O/QAv/0wAHI55Xz5h7D3WK8dBpZu32akQkiys/H5TwbST32G5g\nE030JUDkDzCGz6HNZcJ3ZKzukgO8PEAF7BVwCIlSev+JyvRM3Lrw8PFX72se\nTp9wHhTnlQraRZZOeRGVXnO9/g4wHTNsTk1NoXtIo+cML3ybrpCXZKBiEX0+\nPOeOWV3T9qqhkG985xhsa7Bb8/hnA0tDtIYnR8/IxCyaH7QbNpikTMzEYIds\np533FULFm2ORG0IJNcc7yYR5O8GNJh9gys5kAJJURlVaDBQZ8isLMhO7V/60\n0GvTwTxZzImnJ415PGN04tdGIWYR2wCk7tOAoabtqB4K9dmRcNl4Agp0xz0e\noOiBv++sm4YQKAf/KWFD9MG40euDMI7NnF0UHQdd0SPyVUarVc8g3Jedosxe\ns+kw\r\n=r30k\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "_npmUser": {"name": "ev1stensberg", "email": "<EMAIL>"}, "_npmVersion": "6.0.1", "description": "", "directories": {}, "_nodeVersion": "9.6.1", "dependencies": {"chalk": "^2.4.1", "inquirer": "^5.2.0", "cross-spawn": "^6.0.5", "@webpack-cli/webpack-scaffold": "^0.0.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/serve_0.0.4_1526567650414_0.7850199049495412", "host": "s3://npm-registry-packages"}}, "0.0.7": {"name": "@webpack-cli/serve", "version": "0.0.7", "keywords": [], "author": "", "license": "MIT", "_id": "@webpack-cli/serve@0.0.7", "maintainers": [{"name": "ev1stensberg", "email": "<EMAIL>"}], "dist": {"shasum": "a218e64299bdc125c609f7dcd93252ee10cd5e39", "tarball": "https://registry.npmjs.org/@webpack-cli/serve/-/serve-0.0.7.tgz", "fileCount": 2, "integrity": "sha512-xzG8/JOvi6WNuUtypgbnn7NxVDrm26A7ahc1c0g7bUU17M5F6vSfVsQYfi3YBUoYuxaG1QnmajrVogNLSP3CtQ==", "signatures": [{"sig": "MEYCIQDtZqjtNMisZtOJGD+jrodC9yVBu1Qw4zh9ae4MBBecVwIhAJHR1z8E0/rM3oZUi+x0ExcTA2qEpM/lzNENytAGB4uQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4758, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbEfYCCRA9TVsSAnZWagAAm2sP/2wH7qv7UmYRkffoAJIp\nHkI7iNAmNsiUIDgwXqMxv7CSBkw5bHtG3VzSpAqTS3d9YyQM2vcDPriutWPk\nD+UbItnr+K/GNdPyMEQkzHfBGmv0EYCqQVFJtgYKA7ggKB94cxtu9z2hU4Ou\nMOFeZJmCO9E8f+FKCyQbmfjmHR0XVbqmwWhJuOv2aU4K6MOB78zeqL2TYFst\n6Rz0OjM4vJTX+IIVoy4v8aroKbnSHNGS5eRm7YcMsVt9TvafMZn4wato6NjM\ndFImGqQX1v1VwxHvfJJzfxn2Dt6BHJYe+Znxj1aLwhF7vn2h/KBqOFjPYDos\ngdeiedZfUOfSt8OLaeS3eUbmvQTJn52jMasS4sQPCj0doTzRRsheeGM01uLO\nYionlfe579PwIt5rJ094O9sbATUo20yll6FeXEXzlepfpSpfdoql+pz5WCIC\nhCI4d8WnNhCPBc8tca4vVIz1qgGD0dhTp/wCwHVllgXnlSMqo0T8+jCYKORX\ncOCmTLQQon1EvMOIQl/gA/3yNl9CkcZhMaruWrkXfYI+ej+2WrFx+3LQZZ8S\n5aDga60sVsCR5CqxqVOBZdMEB+Z9nuhxHYLk2Gnr1aui2wThc4bp7hwZ/waQ\nL5yxvLI9q2HquwFAW5updADi/NHonEHwYtcuUwcPbfmhrqKt6auHHKfobcHL\nnqpu\r\n=X0dT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "_npmUser": {"name": "ev1stensberg", "email": "<EMAIL>"}, "_npmVersion": "6.1.0", "description": "", "directories": {}, "_nodeVersion": "9.6.1", "dependencies": {"chalk": "^2.4.1", "inquirer": "^5.2.0", "cross-spawn": "^6.0.5", "@webpack-cli/webpack-scaffold": "^0.0.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/serve_0.0.7_1527903746147_0.09844570269707598", "host": "s3://npm-registry-packages"}}, "0.0.8": {"name": "@webpack-cli/serve", "version": "0.0.8", "keywords": [], "author": "", "license": "MIT", "_id": "@webpack-cli/serve@0.0.8", "maintainers": [{"name": "ev1stensberg", "email": "<EMAIL>"}], "dist": {"shasum": "1ed9fc0bbefee6d079143057bd31c6a6fec941b3", "tarball": "https://registry.npmjs.org/@webpack-cli/serve/-/serve-0.0.8.tgz", "fileCount": 2, "integrity": "sha512-Y6K+yHtbZg9pWfHxwy/PL33tbox+1HKdHWCDTrkesy/qdf2uLHUH5pmqhOCVldb4kf2X7ZlAWrmyAL55p4smMw==", "signatures": [{"sig": "MEUCIQDiOJzfr4HxHnzqgO9waNBrxLqxPhyww8PPEbvjqZo6uAIgAnxGciqyOmLoICOTyx5Nouxhn4egCvm6g+3oERFu0bM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4758, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbEfY2CRA9TVsSAnZWagAA4/wP/0CglrQcGU4F/VeDEJG2\nDeK3jXUJyi+nmO+Wa/QtMln7l6YRj63Fm5ADuIr5cVNcWGJet3zuQz+DR2/Q\nvr1TO6M1x4v7ydog7yg7uSqnLyiyNpPB1dXGfJhUHFu6IKgAjch43hOfV1WE\njFjUf7Cri7592vXvIaPQjGjHaSWnP4+irCxbIZEwXk1vmC2KPGHNYbX7kS0G\n2RTZXkrUUPLfpqjxx8OaAxCLRnMRaOWl2rPYjYvor76nak2Dh3uqlMWrMEMA\ndxow6zYr1R94ZZNW87noUmf+z57l4RoEUFGoJmzVdbitGClb4HKWfo/UbXT0\njsCIll6Yq92vgpo3X2n3uY1KeM3SE2D1DN4Lq4qGh01B6IZm8lsJ+Vr7EDUY\nNlpzX7eSZjowQguVmnfGyyZZGqVp/xLS6X3TYFziQg9BncF8+mZL9H0jzyrc\nDlTeP1LSZb5NE59JTsBwUZP3l/8T+xtdmoomKrR1l90fwiWxpofnp/oJA3F3\nd5x+aF/43KeRpvw9nfA4GZpZcqMaVmvRe2ZyMj9pDa+BLUfIMkUmtjw4LI4w\n0O1asy+uGQ9Yz335jNXyc1ZoXsWsSHpqlAdnnD1YM7UqMKHXhWg60rT4c6p+\n4PMGNs99eYIoaWBQJ0U6ZLoc3mhXpg0RNdYc7E9d+Lg3v1AoByrGGGlPkT3j\nBlt3\r\n=nk4A\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "_npmUser": {"name": "ev1stensberg", "email": "<EMAIL>"}, "_npmVersion": "6.1.0", "description": "", "directories": {}, "_nodeVersion": "9.6.1", "dependencies": {"chalk": "^2.4.1", "inquirer": "^5.2.0", "cross-spawn": "^6.0.5", "@webpack-cli/webpack-scaffold": "^0.0.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/serve_0.0.8_1527903797980_0.29943488726694634", "host": "s3://npm-registry-packages"}}, "0.1.0": {"name": "@webpack-cli/serve", "version": "0.1.0", "keywords": [], "author": "", "license": "MIT", "_id": "@webpack-cli/serve@0.1.0", "maintainers": [{"name": "ev1stensberg", "email": "<EMAIL>"}], "dist": {"shasum": "86c03ac5a6f889a280c11d8207a62745454ac34f", "tarball": "https://registry.npmjs.org/@webpack-cli/serve/-/serve-0.1.0.tgz", "fileCount": 3, "integrity": "sha512-0+2ub74QqmyZTfYz1CyBZdzEM0anzNxrJKRi4tvc0/4/fZnXHoxsd0iDWWQwrV/kNZ219iAQcQc7lowjhz9Bew==", "signatures": [{"sig": "MEUCIF+xhp4DH+QBzMtNjn/r1FxzkeDsqhAUIyY8D6vtqJVZAiEAr7NWS4DT7lT0MvaOs3PEAuniBHaLreag81D+I0gXPFU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6495, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbT1WJCRA9TVsSAnZWagAAc7oP/16TP27ABx0qCRpr87l2\nVlHVxaPZZpqDmDN2bfpShSyfQ6dS+DtvwUFAD+8e82+sompXfYJXiqPMMLOC\nz17A6ebB/oFifML18iNKV+srvU09XaXlb5FQoRjcXiMg8YKSi80mdwj07LAP\n/WYbZEifghyrebRAjUXYmJNzqm8MLHWaXmauA8NVxCfMI5i20b1xY8N0sdcf\n1/yVJU8Tved0YZ3WUYReTMdp1eYxcV+w89HbMzwDQ4MQ6WNSD8+Ls6VHY7UR\nqU+0fuY7eD573ipAmlNPn3+0O4iwrIcONB9cl20tpcIhJilOxcqqQ2SCMWPq\nR6yU/HctDzK1Iip8Vj9NrCIroGxSUqUCeTan9MW4yApKNly+aE+pq0oa5P8u\nnQ+ZOTtMUdw6GXpKZ7lVS8tYgXxX+F1mJKbEswylnyXbF/ljgSp70uBIgkGv\nTRgJShiUbTbRB2rfCIk56WNyruwk/VHjJp/bfHGeJjUID6LEGnwb2lp7oDbO\nrGZ19T5+rHtQCI3FQHjgCpv1oduQrnrzT0yOPZ3+Z5+W6cgX7eQ1mnoQ6or6\nCVczN6Cgiv6slnhl6YdezOFJk/l1KD8ovZR8sgYtBB+1ENnSNGFQEnNvT0xk\nRz+UbxETUYGqkV4xsbMryzMk0VWPtrTzfdph41m7ZgNyicAFlaLhQfU1Opu1\nhrbS\r\n=FHk8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "scripts": {"build": "tsc"}, "_npmUser": {"name": "ev1stensberg", "email": "<EMAIL>"}, "_npmVersion": "6.2.0", "description": "## Description", "directories": {}, "_nodeVersion": "10.4.1", "dependencies": {"chalk": "^2.4.1", "inquirer": "^5.2.0", "cross-spawn": "^6.0.5", "@webpack-cli/utils": "^0.1.0", "@webpack-cli/webpack-scaffold": "^0.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"typescript": "^2.9.2", "@types/node": "^10.5.1", "@types/inquirer": "0.0.42", "@types/cross-spawn": "^6.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/serve_0.1.0_1531925897374_0.18050454393593673", "host": "s3://npm-registry-packages"}}, "0.1.1": {"name": "@webpack-cli/serve", "version": "0.1.1", "keywords": [], "author": "", "license": "MIT", "_id": "@webpack-cli/serve@0.1.1", "maintainers": [{"name": "ev1stensberg", "email": "<EMAIL>"}], "dist": {"shasum": "dd37a17e2188d54f95bf3d4dd32ce47094d7793c", "tarball": "https://registry.npmjs.org/@webpack-cli/serve/-/serve-0.1.1.tgz", "fileCount": 4, "integrity": "sha512-F/Cyhd12ZMvzD5QgACdJ+7wQO9H186k9uMJpNrKeSNCPUpMiO8yUhNl/JGKC8kpRjvE0UQx7tKtId3QwRRBmag==", "signatures": [{"sig": "MEYCIQCa4TNcnilWIAH7iXScdPiwgdsDD7rtrua4zXIEKo+zaQIhAIWg3ZmSnwrHFJ32uxbok5ASNwT4suFaQTNbohXFozcC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7696, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbp1biCRA9TVsSAnZWagAAnqAQAJ0Qqn0J5EqxJHP5xvTM\nx/axsIwtd3Nbq4rHzG5OLtdSmKOwoWIDy3rr50POuui3WSu5aNRw1eXvH4dp\nDEKLXNfAph/O9HB4tt0JlNPvqFYLVjFkLFfsQtR0IYF9dSQrcDe5ZVoeIBpB\nSAHLqqxqYZc8cyS/0VR/aiImDV5bQ5/t9M0rIRctUM11WgHdbfgSwBE0KQ5Z\n7YwQ76ED+lCkZrAKVFsB2zddQMBky7AxlpMW+WtrD2XecH7TVDJtJr1lBsxV\nGf0llzWM6sr1BahLgMIwuVsn/UrxMJFeqGdqA3rbnCU7WS6NO7uUqhPYy8zt\n9JFBhLDtnW4r7o1m1wi9XLfUltbsJcxp3At5AQjLLotGS+XTjufTJwwONCSv\nZyJxPiX6DJIQVBrhEvstVSiizyW5W+lIAmpncAt2l1FtlIDqHSPuCFdKrGbo\n5wPG4H+CDWyf0Ipp0SoDOY+4jXt9URFW6EtBbmwUMhMf6TSRwSqVeOk3HGTo\nFIpzOSfziw1ILtdA3kbaIKJvf/xrh9QDfYbZhu8uRyWok/frBw/GeKCc/z2w\nXf6MMm0mBwZ4RZWlbT4AtmdAD5TQUpg1J5EWguwUJ6x3FyIh0C8QA3UpJ3KZ\ng/2NcUUJzSqZSfizS04qqR0dnN2MnZzRCirio28VK2JgMmLG9g8PVlQdob0a\ntag4\r\n=SMAa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "_from": "file:webpack-cli-serve-0.1.1.tgz", "types": "index.d.ts", "gitHead": "e3119b687842e24f1640c92852b7a7b09e0c6bf1", "scripts": {"build": "tsc", "watch": "npm run build && tsc -w"}, "_npmUser": {"name": "ev1stensberg", "email": "<EMAIL>"}, "_resolved": "", "_integrity": "", "_npmVersion": "6.4.1", "description": "## Description", "directories": {}, "_nodeVersion": "10.4.1", "dependencies": {"chalk": "^2.4.1", "inquirer": "^6.2.0", "cross-spawn": "^6.0.5", "@webpack-cli/utils": "^0.1.1", "@webpack-cli/webpack-scaffold": "^0.1.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"typescript": "^2.9.2", "@types/node": "^10.9.4", "@types/inquirer": "0.0.43", "@types/cross-spawn": "^6.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/serve_0.1.1_1537693409529_0.33975157180401583", "host": "s3://npm-registry-packages"}}, "0.1.2": {"name": "@webpack-cli/serve", "version": "0.1.2", "keywords": [], "author": "", "license": "MIT", "_id": "@webpack-cli/serve@0.1.2", "maintainers": [{"name": "ev1stensberg", "email": "<EMAIL>"}], "dist": {"shasum": "5dda0df8e9f335514622e4ae637c567b86c79236", "tarball": "https://registry.npmjs.org/@webpack-cli/serve/-/serve-0.1.2.tgz", "fileCount": 4, "integrity": "sha512-9/B8n9ZOZDn6nYhGoJOF3M5M6YwSURnKmNUWVezjbuKCgTGbU33kxMs4dfrxxC/oKsZqipOML/udXhW5WfHgzQ==", "signatures": [{"sig": "MEUCIDE1Q2LlmFMfQsVWr/MITjajz9UnXS/FgvBWFhxMi64JAiEAv56HB1WyUO16Tugtvk4JRN7ScPVMM7FF29K2hNdXMAI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7696, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbr7L2CRA9TVsSAnZWagAAkkQP/2amw4Gk9d0MM3mBslYI\nv9/PmiA5fTait3yd5vR4Lb23JbOVanlL1s+j6gFQ0zLUoa+pAPFeF8gLTaAC\n5OYxVZQWW8knin6Rt+TpvUp0BptraWLRkk3R4CMccGyz+R2Sf3dJTecw+G7n\nJ76IZVo8FAHCVt2wzAZY37TuNSYW+n9+smENIEdwob2YhbH/aEIB/Npswg92\n0FiafqrgAMkJMH1UlDg+EPsaZhoT1oIC/zJg4n5BGwr8Sm7ddJKavnzB7Z/L\n67WlyTMgG6C0U1mYzNtDSb1Frat+LZWV6jUGmGVHgcrd/nGYfMzXVlIm4/f5\np+gpW0ZbOsY/FqWX5tijN/+E88Xl8x9Y7OUyAMCDP6/s/wYBnfhrS8ciCgy/\naFxNT1cXzPtZKDXS8bIYp1zFiRYuEgbDbiB/W0HjAFe8mlFea8gSpRAdSiP2\n2hm5c5n1so8wc5Lb3z3gwJaYYTM3Drazh/cL2v+hvoJLRZh03MimlVGiFu/3\nADXBWkQQEGO+O5r5kj1xvRuSrTZjoglYNrhTRUKJdx65/QBFPdzIijOxeYZp\nR0x3UPAdN2qXe+Ol+Nk0aYXQUk2C/kNTs94zkoliIp/dszhQ/az4SKYv3Tje\nHvdMqr7TIjGwU0FP9FZ8LbseBamia9XGKR5DFe6/z7BAcycBfVqS4ocU/2C8\nwJO7\r\n=0wUF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "_from": "file:webpack-cli-serve-0.1.2.tgz", "types": "index.d.ts", "gitHead": "795ac486c374f1ee71cbbd6fa1ab82257a8a3c17", "scripts": {"build": "tsc", "watch": "npm run build && tsc -w"}, "_npmUser": {"name": "ev1stensberg", "email": "<EMAIL>"}, "_resolved": "", "_integrity": "", "_npmVersion": "6.4.1", "description": "## Description", "directories": {}, "_nodeVersion": "10.4.1", "dependencies": {"chalk": "^2.4.1", "inquirer": "^6.2.0", "cross-spawn": "^6.0.5", "@webpack-cli/utils": "^0.1.2", "@webpack-cli/webpack-scaffold": "^0.1.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"typescript": "^2.9.2", "@types/node": "^10.9.4", "@types/inquirer": "0.0.43", "@types/cross-spawn": "^6.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/serve_0.1.2_1538241270281_0.7839094362746719", "host": "s3://npm-registry-packages"}}, "0.1.3": {"name": "@webpack-cli/serve", "version": "0.1.3", "keywords": [], "author": "", "license": "MIT", "_id": "@webpack-cli/serve@0.1.3", "maintainers": [{"name": "ev1stensberg", "email": "<EMAIL>"}], "dist": {"shasum": "2ecb86578bc8088a1cc9bd1f99cc6693a3e25d52", "tarball": "https://registry.npmjs.org/@webpack-cli/serve/-/serve-0.1.3.tgz", "fileCount": 4, "integrity": "sha512-8+iMICFLGR4JTw18NcB/Seh+yElJ301EEus28IC/qOgEuMEfuGZzSIW85lq7MHiWdeqHjVP+drRQPgfINdaaIQ==", "signatures": [{"sig": "MEUCICaYPK/OgZDiN5Dl1ZrJCJF5Qc1inDprVH6ZgqcT1Vm3AiEAi9BsBcqtnuyKUXZNptYQfULFNi7mXp+AbnYs/CvVvFY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7703, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcLZimCRA9TVsSAnZWagAAW/MP/1f53urmhXDrhNdsQFTl\nzKNvmfnMKmkKN3ATPTRCKrekSAr8B66kYrlrz8yG9QD4mOhuwn1NRGtslalR\n4Tb1YQxmooG2KagmnT9gp/bRDM9+AAKKtUf1FjR6TaIOXRqO/AQ+Qn1GF+gh\nJXXr0stPpGUoy8kXBgHOuPOnrcSQ1N5XxtUDWbiOzeLV9TK6jW6KOWJ0UXV+\n6s8NIRvQJUpx+Ln/nYzqxuACFjRYKKtYTXJLzuPf6O+htfPTolR2lKtMJJ+C\nFHU4BkR3BBhpdE2cOkJ9sqH8XK2bDv33APD5aQsaklR/KL7PHOIGaxlgtavU\nlEH83x2rwfYDQIz9dwJUnbnIdZAKvUBlfBdL75VvGl6DhUknA6r7SPGEaKTp\nmWDtuDJKrpkYaEthth+Oc1u3gqwgoXihBsGm2pWUlBCKmfuy6OMPAuNHmKNw\nu1ASitGJq5pt0QruGDr5dmCHpKpcY9RncusuiMDAJstbtSiSqmURXNRavlWD\nKKQ2bx1ijG8G0dH4FJ0YOP7JT0UDk/SAjph71tBvs5zGHd0bQNCtmmCqnllb\nDgP2ypcJuMMdLIVfyTTOWXC1TtH94eXlWcOsOTZ0ova4yJxRzmEKLG5gWfu7\nCVRAoCYG08sQLq3/XGezmKb7+tAnQ79CxTSS2USsffGTPXr+QYmMyO88CgL8\nHBU4\r\n=NzrR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "gitHead": "0deb89f5668f6a79bbfb066b09826d93e7d0a7b6", "scripts": {"build": "tsc", "watch": "npm run build && tsc -w"}, "_npmUser": {"name": "ev1stensberg", "email": "<EMAIL>"}, "_npmVersion": "lerna/3.8.4/node@v11.4.0+x64 (darwin)", "description": "## Description", "directories": {}, "_nodeVersion": "11.4.0", "dependencies": {"chalk": "^2.4.1", "inquirer": "^6.2.0", "cross-spawn": "^6.0.5", "@webpack-cli/utils": "^0.1.3", "@webpack-cli/webpack-scaffold": "^0.1.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"typescript": "^3.1.6", "@types/node": "^10.12.9", "@types/inquirer": "0.0.43", "@types/cross-spawn": "^6.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/serve_0.1.3_1546492070036_0.11365269229117159", "host": "s3://npm-registry-packages"}}, "0.1.5": {"name": "@webpack-cli/serve", "version": "0.1.5", "keywords": [], "author": "", "license": "MIT", "_id": "@webpack-cli/serve@0.1.5", "maintainers": [{"name": "ev1stensberg", "email": "<EMAIL>"}], "dist": {"shasum": "0ef6175ee744661abe4920ae5527ce819411aa3c", "tarball": "https://registry.npmjs.org/@webpack-cli/serve/-/serve-0.1.5.tgz", "fileCount": 4, "integrity": "sha512-SrzdnypwN0777woAhlNzifuNI/xBCBGBJJf+zgZF1xcA87++GOJqoivudLRtRbmP8woeui4b9IZmu/rjNYMJdA==", "signatures": [{"sig": "MEUCIQCpxo9+zoHPYn99VUPNjeokIes0uZVSkan+fLndvwwV2AIge61SiATNLCju4wrtmhFSTpB/DU3x6v94CtOnW+Xytho=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7703, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJci/eACRA9TVsSAnZWagAAOqIP/0C3hxyoSd8ex1uN+JAk\nGLLrdVjdatAjwUSM1mH3Vw1bh5hF4j5mNxpSOr2xF53nxbWrWMtPcXTLJcjk\nLsmiGEvWJ8ajyNng+ON3yt27FEik8r3WWjUmn6Hq0+zTUP2EHehfHnUjrUBh\n++WGEQjWiTDZds4GJIfoAKjsFUZNN0U/dv6OM+uWQBc1HvjJwbpvQYn7sjml\nnoIez2yM1QWilscd4a3bYs8nqGa7So/Jyp3TjkrOeEy/2xu4pk3Ddew8mmp9\nOExQMaxMoFbwyDbNLAXqOc7S3dyJ2qJLzoKeTELGK6AiwoutVKAWKzaLT+yi\n4WTPgPgvRNT1WJ5ngTKp37pb+4Ty/OoQgaPEhz0h9eAaFOu4KQo7GJ2KVeTg\nzFSEyh0NS0sm6Ia1+lgFH818GLZQ4/X6sYOBEBo6C/RArUlCFs630zPrrUP+\njc0+zDbLczuQo5tV6xjDZZVmzyuo3l6qwk9JA4W6V7J4XyqaTpHSSsUBa2a0\ndVwrheqFumJqpoZVGBGDbKgQ+tjw6KVVlEp1PYy4lioDeaHDqgulsT62xERj\nur4QrJFUIAJ17YEy5UYv4lmOU9rwx+8SsW1JMXDU9c8XOHM1T4l4U1s7ls+2\nDAUeIJbPorOC4M689wQ5DPsc+vLCf0SFhcsk196tFmcoiYEuHGOj9ch63lrp\n248o\r\n=A5Vt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "gitHead": "1630615841776fb39a0934459154cc5c55c0d12c", "scripts": {"build": "tsc", "watch": "npm run build && tsc -w"}, "_npmUser": {"name": "ev1stensberg", "email": "<EMAIL>"}, "_npmVersion": "lerna/3.13.1/node@v11.4.0+x64 (darwin)", "description": "## Description", "directories": {}, "_nodeVersion": "11.4.0", "dependencies": {"chalk": "^2.4.1", "inquirer": "^6.2.0", "cross-spawn": "^6.0.5", "@webpack-cli/utils": "^0.1.5", "@webpack-cli/webpack-scaffold": "^0.1.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"typescript": "^3.1.6", "@types/node": "^10.12.9", "@types/inquirer": "0.0.43", "@types/cross-spawn": "^6.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/serve_0.1.5_1552676735600_0.3809619675546623", "host": "s3://npm-registry-packages"}}, "0.1.6": {"name": "@webpack-cli/serve", "version": "0.1.6", "keywords": [], "author": "", "license": "MIT", "_id": "@webpack-cli/serve@0.1.6", "maintainers": [{"name": "ev1stensberg", "email": "<EMAIL>"}], "dist": {"shasum": "48f2319509aa3ca1d0ffc4a8be825de5b7bbd9b3", "tarball": "https://registry.npmjs.org/@webpack-cli/serve/-/serve-0.1.6.tgz", "fileCount": 5, "integrity": "sha512-YeIZvv4xMW1ammDqz1BNEq04Wb9IfTDv09NGz904Y3uAF1ogHqM2HYBFFZRD3QQBN8/GFtjUExW2wUDXDfR40g==", "signatures": [{"sig": "MEQCIAQt/OfZCqSCVSJ8DwfRCeqkO3fmV2hdtFUFXhP7qMNCAiBhYNmLEJGWYDHMyPYsAhH47pUFB8vmiaxqY99tSPRDRg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6686, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc+kJOCRA9TVsSAnZWagAABTIP/AsFCp6ub0spOYXXd+cy\nEep0FYw2eimIVJZfM0EbKFoIobh+JXZeL9j06LeZQRRxvXWIPnGUfLnDHyZx\nVzEBSGH/S51j41R4h7Zupwl/mzi/5u5iI9FPEAM2QsyJ0Tgpk1ABcgdMi79H\nDdqAmzMANzxEOZ5BoR3Shzm48DJnmi1fIoJg2bQQ9kpCBqaxJoDwaFHFO/Tq\nzYzsEL8a5ij4KC5DFiYFJcOpjLafS76IuSUYPKPNoVsU4V+ZS/cdtlDBUxju\nzbjQ2tV0Hxgy9Xf7Gnxk/RVlSr2i2CIASVhj3qZdJ5WpraZG0aTJhCgp8dQJ\nyI929+35gRBXKYDunBAyTVvWK8kg7FkBAVzikkDzkY5jGRR3XzwZY3ho6CN/\ncOqcJtCpjJesyCvA/828CA9GrjL+62ykLbBNAGlJ2a306d2RuUsQ+ftw6tyI\nVIuu0zGvTdKlkY0beljD+9XFGCp93S+/Xjo3oF6yukctznK4dkOvPY2C4BDy\nrAcakrpkiKXM8jsGOfNTHzW5vTBNM4T4kUyK6i9AAclRMLM3s+dnRPtLNN3c\nOCY88v3r0sAMN1SBDhaFcIBwkJK3a8JLUQMSgb30zhEzP2PBUishffWD74U2\nPL2z1nnEd8OfzOqM4GkaVvitGygBZxouoUXz03JoFuB4z4desyVhfCU/xPj/\n1w6j\r\n=sTx4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "gitHead": "ace0d4a9d06b56652a99577a4ec96f9f706cf877", "scripts": {"build": "tsc", "watch": "npm run build && tsc -w"}, "_npmUser": {"name": "ev1stensberg", "email": "<EMAIL>"}, "_npmVersion": "lerna/3.14.1/node@v11.4.0+x64 (darwin)", "description": "[![NPM Downloads][downloads]][downloads-url]", "directories": {}, "_nodeVersion": "11.4.0", "dependencies": {"chalk": "^2.4.1", "inquirer": "^6.2.0", "cross-spawn": "^6.0.5", "@webpack-cli/utils": "^0.2.0", "@webpack-cli/webpack-scaffold": "^0.1.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"typescript": "^3.1.6", "@types/node": "^10.12.9", "@types/inquirer": "0.0.43", "@types/cross-spawn": "^6.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/serve_0.1.6_1559904845961_0.04193219835046058", "host": "s3://npm-registry-packages"}}, "0.1.7": {"name": "@webpack-cli/serve", "version": "0.1.7", "keywords": [], "author": "", "license": "MIT", "_id": "@webpack-cli/serve@0.1.7", "maintainers": [{"name": "ev1stensberg", "email": "<EMAIL>"}], "dist": {"shasum": "3e3447eac1a56eeabe06b42fec8723c33f3b6db6", "tarball": "https://registry.npmjs.org/@webpack-cli/serve/-/serve-0.1.7.tgz", "fileCount": 5, "integrity": "sha512-L5JIseQrb8gxpME3Wvtn8pn+afBb2ZyUL0HdhndLJ6oMGMbzzR7hzwCJSGtVW/ct71Y7Qxr8252F2HEvAvvwuw==", "signatures": [{"sig": "MEUCIBRNoK8BAPAF/JY17Op08wPSNXPsLkp84GsIqkLRuNbiAiEAmKNwOrNMsxzJRRaVeFjpkAomiLQYVRcgIrmT7qXrZGs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7171, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc+koYCRA9TVsSAnZWagAAaMgP/1MY52nlGgro/bEaF/v2\ngFhTxsav5bzHAN4gZ0nt2F48pF9MnRhA/vlBWjPQ8ETpVWjBO8T6n140Hntt\nW3f0kkVDLf7CvsCK86gIAvXIh1hDQSIluQ2pccK/vWKolcshuGAtKJnGPoID\n8JlvLUwUFPgTcNMg/nu9yg4pSVYuixG5fQI+v3SSff7ryD3blaQzJsp/4RDt\njdKgibgULni3fDYgoEwkLOi3plc1BLpdcAvL3F5X0kfChSdoYPAQfQNzZM5/\n+ZY8cBXtrwPEivmfo9p1RBuWFaBrjs1e0ccHitvdYzS74VSXEQnUIdbxj4tz\nTkfJFYLxWWiuikOy7alqj+OkHTHdAUjUT3h/eowvBANKFb1lFVKQET39JOdT\nLMV/LkpcI95FFIi/A/a25qChCr/OVVjXzoLqWY7FdePKdyJY+MckfH+aZ0sG\nHtiLLzlyYKVlKAlDLE60uPgjMjr/5abE2UV8gPo8ZnxSpQADhQQaJL1QDWfu\nnblDQ91Q/dleVFQ8fdIoOBEMJdsKQVC/D9QkctvRRTVT8werpm1iZBUkWqSQ\nW3IT2pN7XZJj4L+hreotFPsTLpxzmYNX3Y+hfXC4lK0vPvCU6duEFCwCRXVZ\nGRcBPL1HEkhCNSvjEuMGoVUN4S9xYLLTQGugJYE26HRkVQFynpK9ls/VA6mp\njkXs\r\n=ODzy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "gitHead": "12a38be95041fe1cb84e8d654f4550e2ab9defaa", "scripts": {"build": "tsc", "watch": "npm run build && tsc -w"}, "_npmUser": {"name": "ev1stensberg", "email": "<EMAIL>"}, "_npmVersion": "lerna/3.14.1/node@v11.4.0+x64 (darwin)", "description": "[![NPM Downloads][downloads]][downloads-url]", "directories": {}, "_nodeVersion": "11.4.0", "dependencies": {"chalk": "^2.4.1", "inquirer": "^6.2.0", "cross-spawn": "^6.0.5", "@webpack-cli/utils": "^0.2.1", "@webpack-cli/webpack-scaffold": "^0.1.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"typescript": "^3.1.6", "@types/node": "^10.12.9", "@types/inquirer": "0.0.43", "@types/cross-spawn": "^6.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/serve_0.1.7_1559906840322_0.4255247532515205", "host": "s3://npm-registry-packages"}}, "0.1.8": {"name": "@webpack-cli/serve", "version": "0.1.8", "keywords": [], "author": "", "license": "MIT", "_id": "@webpack-cli/serve@0.1.8", "maintainers": [{"name": "ev1stensberg", "email": "<EMAIL>"}], "dist": {"shasum": "b9a48d709ba73ab23158dec39ea957cc84c97720", "tarball": "https://registry.npmjs.org/@webpack-cli/serve/-/serve-0.1.8.tgz", "fileCount": 5, "integrity": "sha512-gZUEX/12h3DL0+FAxkCbuVCK3LH0+p7jmpu2MFCi2g4FZWs37ZrBEtG06h5OZpx3yAaKzOtBe6vYP+0D7b3tlA==", "signatures": [{"sig": "MEYCIQCePcd24P9Tkn/E6MtY/ZcHuBpdCzAl83qjC9T4lm/3zQIhAJeTzqxS3lFf/rfcqhRwl6gInyaSsOg/3yXewHyOFQZc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7171, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc+kyNCRA9TVsSAnZWagAAEq4P/2jUHTitsb2p4t0wEEvZ\nHCGLgJv11iydGM9dRYkV3tbRvPXAW8iEEY0RpTSHVyF6qQM2vSY9QoLBPyzq\n/GkCE9t57b59DQwdqCxeXklHnunYxx6tTxaZUmK1XpUq6fuc7sUTgLnlUfP1\ntEWCDd6B36uemQ96QpvPVAxrMsJVcxorAtUOO0qI7CQsr3cqgHrTS+9agmbk\nEynqsqHvut+QCFJfdaz+ETW5Zcf4qgMIbC9a47Tm0gcWiTTmt1EaJJQK6AVY\ndJ/98N9dXUmyA5FJNe3NXBEYSCjhHNXb8kTyjkT/W6yYhQT6GYplmkSaN/0X\nxeI3uMi35o1A/wgQNENTdcej3sd8/8aSpRNCw6/iRBonLuypJyFIQAoASvi7\nXlCwFtYBezb/TD328xVThzyE+GnGZH3TSZqj5mIrvR/naCHglAU3Wo87FR8E\nDh8Lq4U35COxcenJoRR43MDIM4fQS6U5FoKyAFFCKIP7j+tV3fT/PCQQ8zeN\nNwDWdmp85Kl60ATIq1oQCkDI8Moq+4OOK31e6B/UitiUHcmeiQD5J4WVVmnX\nh5fdzzFE6Bu67J/vfYpbxPdbvvblzobHKK9uXvGOv3kbA2ZrVRub6OmxZDUy\ngboU38ZvA9nJ6UVH7j3hfYJHopTSrOJFNj/JX6jBSiPXb96neJI5rqNl0n2s\nkLe4\r\n=PwYU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "gitHead": "69f7683a5c4b541308e9f0fbf649cd09f51a0c88", "scripts": {"build": "tsc", "watch": "npm run build && tsc -w"}, "_npmUser": {"name": "ev1stensberg", "email": "<EMAIL>"}, "_npmVersion": "lerna/3.14.1/node@v11.4.0+x64 (darwin)", "description": "[![NPM Downloads][downloads]][downloads-url]", "directories": {}, "_nodeVersion": "11.4.0", "dependencies": {"chalk": "^2.4.1", "inquirer": "^6.2.0", "cross-spawn": "^6.0.5", "@webpack-cli/utils": "^0.2.2", "@webpack-cli/webpack-scaffold": "^0.1.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"typescript": "^3.1.6", "@types/node": "^10.12.9", "@types/inquirer": "0.0.43", "@types/cross-spawn": "^6.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/serve_0.1.8_1559907468943_0.6490342828284204", "host": "s3://npm-registry-packages"}}, "0.2.0": {"name": "@webpack-cli/serve", "version": "0.2.0", "keywords": [], "author": "", "license": "MIT", "_id": "@webpack-cli/serve@0.2.0", "maintainers": [{"name": "ev1stensberg", "email": "<EMAIL>"}], "dist": {"shasum": "f4ec1f51a88522b65acebb3b36efc7dca2fb6669", "tarball": "https://registry.npmjs.org/@webpack-cli/serve/-/serve-0.2.0.tgz", "fileCount": 10, "integrity": "sha512-y6v6dbvL51r9PCpxEUkR8CM33xRd/crmQcpxVmSB88alojJVzYDgu/3nLE3Fn4MLeJ51PwsXqHXltXIuFFFLjA==", "signatures": [{"sig": "MEUCIQCdG7Tom0SAyQOzpoZqOt5Ql2mEbR3XGN0I8vlEM5zslwIgZlEf52vmAgWeZBc/XJvy7dkg9YtbJAiSwO+qWMt8c1k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9511, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdyuqnCRA9TVsSAnZWagAA3lgP/00wZJfM2z4dWkl2cHcf\nJGq/oIJHGT/H39rO0h17RKMY76/O515oDcV2wpa6gnYDURk3ngvGiYMM7l61\nKzf4NFbQYE3kkZnSRtWzxotjeeEpW54AAEqB4Fy2kBbG30xDikXpcsOtc0zb\nI+b5PI5zR/ErCfTeSARD7yg0YSfj3r66ib87pKSA6yvAHlRa1xuBGvAJOhyv\nTx4ZSSaP6x8/OFnXoedvJ7PUaXGS11IUThhyfgjj+XUEynX0CjT6d1lllj8F\nneDso4UmadCvKjvz5GM7KlLu36PTyu5tECCMQgkoDEhbocRHb2zjIeOgSScc\n2c49pXPRw4cPAthGQcZscLsWytqhYWvvdaKVdPEqQmwnD4v6VAkLrnMdP0a8\nl+escA2pswGdyOhvqvFXDSWwjtLkOqpg2s2/MUBPucCs0pGMtRkq+R4HVWcR\nQaw1/shBMEt2OU+CrwDOb0vaOMWQAef/y/s/hvn2hN6/2ReBvuFkJB63cq6K\nTmTfFLOUF9TI9Zm4LxuZCVaeqGtaolgSOiLebrtQtXGircznbI9dYa0nqbjv\ndGhdS+XB3tKMFJ/1xVE+sGUK7Hsu7P2LCVwv8cE8OlhL6/nw+q2Zr18XNtFf\nytwfoAYrjdm3Tcnr/2DSimB74+sVjs7Uf8O25bLYeIPVOTYHYnI+y8M6x/L2\nuJpo\r\n=bR5n\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "gitHead": "8097c5cf0fb6d2fa533168b4d97fbb373fa806ce", "scripts": {"build": "tsc", "watch": "npm run build && tsc -w"}, "_npmUser": {"name": "ev1stensberg", "email": "<EMAIL>"}, "_npmVersion": "lerna/3.18.3/node@v10.16.3+x64 (darwin)", "description": "[![NPM Downloads][downloads]][downloads-url]", "directories": {}, "_nodeVersion": "10.16.3", "dependencies": {"chalk": "2.4.2", "inquirer": "6.5.1", "cross-spawn": "6.0.5", "@webpack-cli/utils": "^0.2.3", "webpack-dev-server": "^3.9.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"typescript": "3.5.3", "@types/node": "12.7.2", "@types/inquirer": "6.5.0", "@types/cross-spawn": "6.0.0"}, "peerDependencies": {"webpack-dev-server": ">=3.0.0"}, "peerDependenciesMeta": {"webpack-dev-server": {"optional": false}}, "_npmOperationalInternal": {"tmp": "tmp/serve_0.2.0_1573579430878_0.54467136966096", "host": "s3://npm-registry-packages"}}, "1.0.1-alpha.3": {"name": "@webpack-cli/serve", "version": "1.0.1-alpha.3", "keywords": [], "author": "", "license": "MIT", "_id": "@webpack-cli/serve@1.0.1-alpha.3", "maintainers": [{"name": "ematipico", "email": "<EMAIL>"}, {"name": "ev1stensberg", "email": "<EMAIL>"}], "dist": {"shasum": "0feed1a89e29d833888d9707c70bee8789770aee", "tarball": "https://registry.npmjs.org/@webpack-cli/serve/-/serve-1.0.1-alpha.3.tgz", "fileCount": 13, "integrity": "sha512-cQi6ZGu+dz9axm66+ukasdbyAuALCrHemqBOTP0AWpyuLoY92sPpJngLZHf1KMtlyIVHQxOuQs1sloNcMycXNA==", "signatures": [{"sig": "MEUCIFr3BMR2643ni5e4sG4YrUGsrpoPS5FaGp7iF3AGkBjAAiEAz6We4ofKqQI0mPsx2g2OLBeQdng4/qEdO/EbtDwlpvo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11079, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeUlUaCRA9TVsSAnZWagAAKKAP/2kD6oHuAgH9fqHkCc9l\nij1VeDTMzg0FUl3xbYQDVa9xvF8fLoSyD4MUY8dNx38fZ5g7KSmcYZCgfBMT\n9fZKuqubVV/0Tvi9KnV9MVvo3ApxuL8gjC6yEr3UuUDE0Xg8ZA1m6QPsapOg\n1U0q9PJ3UDrMUn88nJpT5hx+miUYDsdMkQY5rjp+El1BlMUFxHG3+zEMz2MC\n95VjRs1vWln/p3sD14dBfIo4cJVR2Qc2PuJ75Kls38If1EfCihdPK5dNyqKK\niS/P9EH8Wy7fpHN+2a7C9A6JinvF2PtZVduuX3A1OKxu8PlQ4H/APRiZRoBD\nlB19anmyx2+BwZTN0V2KWs2NObxpO5DQmQiRsYuZj8ZRS00UWCS+AL4GlyE0\nHg2KMzpM29dZf6c17HEtDAoDkqhIhaD5gF8EmPgYpa8S4Gf8EADaGmkbm/a7\n57CYQ1IHSVXB+pUX7Fy6ySsIxQhQP36JjpacbWfR3IQmIOQyOwjlzioAK0dH\n/CPLmolKagO6K3CL54LjyY9Hez9WCWs3mPt06qd0RjrK1DScLyC3jCi236vD\nKdp8TmIN901FYZ8YYLkaHMMv6AvyGkeHEUEFFpZycqxznZaEyx/HvjswPWon\n5fs67YFDH3NPVc6w6pLn2nSbJ3gUWO8pu7FbYaNHkIYC+1SWMW1GNAOaYC9I\n4KG4\r\n=EKD5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "types": "./lib/index.d.ts", "gitHead": "2ea31742bdd4657a14735a0b4776a4a76137fa69", "scripts": {"build": "tsc", "watch": "npm run build && tsc -w"}, "_npmUser": {"name": "ematipico", "email": "<EMAIL>"}, "_npmVersion": "lerna/3.20.2/node@v10.16.3+x64 (darwin)", "description": "[![NPM Downloads][downloads]][downloads-url]", "directories": {}, "_nodeVersion": "10.16.3", "dependencies": {"chalk": "3.0.0", "inquirer": "6.5.1", "cross-spawn": "6.0.5", "webpack-cli": "^4.0.0-beta.6", "@webpack-cli/utils": "^1.0.1-alpha.3", "webpack-dev-server": "^3.9.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"typescript": "3.5.3", "@types/node": "12.7.2", "@types/inquirer": "6.5.0", "@types/cross-spawn": "6.0.0"}, "peerDependencies": {"webpack-dev-server": ">=3.0.0"}, "peerDependenciesMeta": {"webpack-dev-server": {"optional": false}}, "_npmOperationalInternal": {"tmp": "tmp/serve_1.0.1-alpha.3_1582454041950_0.30573791219389745", "host": "s3://npm-registry-packages"}}, "1.0.1-alpha.4": {"name": "@webpack-cli/serve", "version": "1.0.1-alpha.4", "keywords": [], "author": "", "license": "MIT", "_id": "@webpack-cli/serve@1.0.1-alpha.4", "maintainers": [{"name": "ematipico", "email": "<EMAIL>"}, {"name": "ev1stensberg", "email": "<EMAIL>"}], "dist": {"shasum": "fdde7e692acce1ccf5a5fd19442bd295acbedf64", "tarball": "https://registry.npmjs.org/@webpack-cli/serve/-/serve-1.0.1-alpha.4.tgz", "fileCount": 4, "integrity": "sha512-I8lI4LlrcSC+Z+WmIObUDEPZzUvUMju2Z+G2ysjmEiYFxmAiDANFBLGhLJuPe+MVT+xWU6DZFqQUQQJbeegrvw==", "signatures": [{"sig": "MEQCICr0OVfar8fdC6ZLQOVSWJfJLaCGGkbMAHri6XBNqUfRAiAU3QnICBDoEeN2QMlB2oUNGT/OSXod4V8Lx29Fs8DsAw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3731, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeWjibCRA9TVsSAnZWagAAjf0P/3k0XyW5ba6OFSZC8Xkz\nWSTqI9JLVN8U8VUxV3W1MShmugK8/gZC8n4PknIYiNAArCyYNUTSEXew+iEt\np/rul4TAHB7+Q132P8yBIJzmvjD++i8G1gfhfpbWwoZQf8qPYLcxat3O/s6r\ndycLkv6r5wYx+NhR5lHy4bpYZ6pFE7q8TWArKhU7K/p1pMzeQW3XMnmxDneb\nGOPSMUgUouiv1s3TtrkCdCjxe5vI4c4omm+lgriq9WiGOkbuQKluPenRxsnu\n/Kkz/AZEFeggm3dX0ZkvnHdZ7RuWaldwPAm8VCXi2WuC/wxVQbeXFhZKR+vy\nbcT00V/wKr4OZlUWNCQQJq/gGATI/NN5amfcG+qY/BwPRjjsQWlXLnH70OtW\nxQUe/IGHxmyolmvh2rrF3EHdHD3jtKagszSCUI+2bHqBWKlfeINbARyNniVy\n4t/1HSc9BPspDCBFX8BiyYA4+xY8feNcz24h6qmJOEw3k5trE/5GGhoh7bgC\nqO2u6wCOq8wTP2nEs/SBmj8jsdK59mGaBV26xhGIM7QTWxy2mYIpNdvhXf/M\n+dukGAiJDlVtXsqS8DsuNL+iIE53r5IUlpCiXi2Kpr9GGqLPCsssTkm7epKv\nXTGSxS4OpylRYn9fluy1ABu+0wHKV2ooOX5Le+xvoalZNg5grwfrjp7rRszd\nbqk7\r\n=BS9S\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "types": "./lib/index.d.ts", "gitHead": "c0f6f01c277fea9531de51add78e0706775f7fed", "scripts": {"build": "tsc", "watch": "npm run build && tsc -w"}, "_npmUser": {"name": "ematipico", "email": "<EMAIL>"}, "_npmVersion": "lerna/3.20.2/node@v12.16.0+x64 (darwin)", "description": "[![NPM Downloads][downloads]][downloads-url]", "directories": {}, "_nodeVersion": "12.16.0", "dependencies": {"chalk": "3.0.0", "inquirer": "6.5.1", "cross-spawn": "6.0.5", "webpack-cli": "^4.0.0-beta.7", "@webpack-cli/utils": "^1.0.1-alpha.4", "webpack-dev-server": "^3.9.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"typescript": "3.5.3", "@types/node": "12.7.2", "@types/inquirer": "6.5.0", "@types/cross-spawn": "6.0.0"}, "peerDependencies": {"webpack-dev-server": ">=3.0.0"}, "peerDependenciesMeta": {"webpack-dev-server": {"optional": false}}, "_npmOperationalInternal": {"tmp": "tmp/serve_1.0.1-alpha.4_1582971034692_0.7939491489623847", "host": "s3://npm-registry-packages"}}, "1.0.1-alpha.5": {"name": "@webpack-cli/serve", "version": "1.0.1-alpha.5", "keywords": [], "author": "", "license": "MIT", "_id": "@webpack-cli/serve@1.0.1-alpha.5", "maintainers": [{"name": "ematipico", "email": "<EMAIL>"}, {"name": "ev1stensberg", "email": "<EMAIL>"}], "dist": {"shasum": "4a78207a1f57db831b7e054c4dacc77a58852bab", "tarball": "https://registry.npmjs.org/@webpack-cli/serve/-/serve-1.0.1-alpha.5.tgz", "fileCount": 13, "integrity": "sha512-xNKZWlX0RYHZTfKOxuWCrjdqYe0oYpFH1u4zCbuHpIw09mQGDbUMyzny6mluk+qCUwe3DEpkE626bd4nGfnh2Q==", "signatures": [{"sig": "MEQCIG3aoRZH1dO+/D1nh5UI/Whekn/CG0YAAx7ftqdgZJdWAiAFTTWRB2zvDfpwX2gRlBre+t8WReZw5dGBRq61qgpr6A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11473, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeXSrjCRA9TVsSAnZWagAACQ8P/04wPp8cvFq59MOrA2Uw\nI8+utS5MzsNBcwf1Gx7SdRI0GQ9MIkAQlISMzBjOzVN1mN/wT1YSiHF0A9wR\nK0u3vTWuCNkXxWhSBiDemMhzt7sYAdgZCuaPOwTkdDFKtSp+SsV1RQiMDFkc\n/CBHI3SI1fapEAC/nsEm9AWUtrG1frDXLuhsvQ9PQ17/lke0GH6CRGU5R1l/\nazKoIWXt7Cm0F/yPdPKCXk4C2gtY0jGFv9YC3fBxIrCdegCllozSEQySjBHw\nFFfREBkl4bcNVFDhxaI8oExMxV569V0w2ow+/kNNo6piDBPjvwKgcXTelrrs\n00JZKnxp8NAiiGKeWpBoLP/RrYH14AaOtDOr0itUORSNvD9NpXeBlchvvZOd\nd1mYqDNQXbESS+ucnNynjsZHpUBaNaEV/t3DPIkYF/FS8pq996rF1rytpi6G\npahb9CoU4ZV0UF+aPnpGsQCQVBaLBKKS+86w6d02lDAgcoDHPg8xuRUd59s+\naSv8nDNBj1fpds1lguYWwBovYRC+oSY1HA6A/Gq9NZdju3A2Sj2gC27nwIis\nlY8TvZyVpVHMGX/knLxRa2krCq+u+d3766zUimrThXT4L8JbsUWyHqTYUBtW\nZ3hh0PAx92lkc4tZlCXnzs4asj1SAqwZjEIKuXVBXmZrvxU2ZUZrINMOYIpT\noi/B\r\n=Qfpo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "types": "./lib/index.d.ts", "gitHead": "516a5ec0cc0ff3a664d13aa7d603c9b6efdab9d4", "scripts": {"build": "tsc", "watch": "npm run build && tsc -w"}, "_npmUser": {"name": "ematipico", "email": "<EMAIL>"}, "_npmVersion": "lerna/3.20.2/node@v10.16.3+x64 (darwin)", "description": "[![NPM Downloads][downloads]][downloads-url]", "directories": {}, "_nodeVersion": "10.16.3", "dependencies": {"chalk": "3.0.0", "inquirer": "6.5.1", "cross-spawn": "6.0.5", "webpack-cli": "^4.0.0-beta.8", "@webpack-cli/utils": "^1.0.1-alpha.5", "webpack-dev-server": "^3.9.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"typescript": "3.5.3", "@types/node": "12.7.2", "@types/inquirer": "6.5.0", "@types/cross-spawn": "6.0.0"}, "peerDependencies": {"webpack-dev-server": ">=3.0.0"}, "peerDependenciesMeta": {"webpack-dev-server": {"optional": false}}, "_npmOperationalInternal": {"tmp": "tmp/serve_1.0.1-alpha.5_1583164130657_0.010877780452638985", "host": "s3://npm-registry-packages"}}, "1.0.1-rc.0": {"name": "@webpack-cli/serve", "version": "1.0.1-rc.0", "keywords": [], "license": "MIT", "_id": "@webpack-cli/serve@1.0.1-rc.0", "maintainers": [{"name": "ematipico", "email": "<EMAIL>"}, {"name": "ev1stensberg", "email": "<EMAIL>"}], "dist": {"shasum": "7b39258d3266258fad486fb2dbce86d0118907b2", "tarball": "https://registry.npmjs.org/@webpack-cli/serve/-/serve-1.0.1-rc.0.tgz", "fileCount": 18, "integrity": "sha512-6hfeH3B2MPD/yj3ex175BIS1FWJRT0onHb8B2Of5zUf6t0bUAPkijQCc1NTs81x+t3kL015hVbkuoPtYIErSxw==", "signatures": [{"sig": "MEYCIQD0t4ETVFVqAPdo/pFVB5YLr50D5EJguc9UbVansgM4EQIhAIQ5g1QoJU5ALsP5YDoW2zFdb92CPjaJyowzxOv5ljEJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13589, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfamW4CRA9TVsSAnZWagAAnIsP/R48jz/7Kex9rlTsWcWr\ngC7mPBG2nmV3uPS+0i1+hthbK46/ooKOCqAYdeBMOv+Lh3lD0FMJT0ny0Mi8\njDsUN5ZDqjpc/rBKnYDqoMs8avfBc9VMC1EO69DnSXX+/ctqH2xaJfgtTjEp\nzGKxZIkUkIw84lYqhdP9cg347u2nGC+6/bYPy44WlfmU+5EE4vbiACYpA6d0\nbhc1BD++D06Bsk4HHTw/71N95bT72HlMj/BItiYLYvqblsG3stmAGVWaAqly\nsq32hMNRhhLSfHM4YVmqBgJNpV45gXsBh/yfoxc6tlnDF6+fM8PTN6YSFlCi\npAqfwVuE6TaoY/Qirer5FXOyB0Hl3fbdhYYTlyLXn0NNugB9mGVx5sf7VMt/\njwh7qxtwQYw6/uDAtluah/UhfEhe7K2w/hEUkF5ZUPhBswX3sEDWehTQJt2w\nNdU5NnUch64c6/kE+sC9kKvDhI4kZbbLeieGg7Y+kdUXjHiKUX9qiVO4NBZK\nTLgLXO+kjmh5PmcN/TDWzL5bdu2fgsS9yQlMXqkPZ+SB/2VyE1lthMUTd+XZ\nXzeELEp3UJy7+Op23t2ELaP/T5irYT+mtfwwHx6JO1Q4xPHLNyCRq8vOVFxh\nR/5HLCVkdi1edKmeVt6fjApL1pN2MBUdfMHizncQPs8RfSwGWhrKRo+GC7cB\nLHVT\r\n=xROo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "3a38f9947cb2a0e2e676620d247e4055d451946d", "_npmUser": {"name": "ev1stensberg", "email": "<EMAIL>"}, "_npmVersion": "lerna/3.22.1/node@v10.21.0+x64 (darwin)", "description": "[![NPM Downloads][downloads]][downloads-url]", "directories": {}, "_nodeVersion": "10.21.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"webpack-dev-server": "3.10.3"}, "peerDependencies": {"webpack-cli": "4.x.x"}, "peerDependenciesMeta": {"webpack-dev-server": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/serve_1.0.1-rc.0_1600808375806_0.33091994648863166", "host": "s3://npm-registry-packages"}}, "1.0.1-rc.1": {"name": "@webpack-cli/serve", "version": "1.0.1-rc.1", "keywords": [], "license": "MIT", "_id": "@webpack-cli/serve@1.0.1-rc.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ematipico", "email": "<EMAIL>"}, {"name": "ev1stensberg", "email": "<EMAIL>"}], "dist": {"shasum": "852cc5aa99a41ba4c236e0b9f31a9df94edd1d32", "tarball": "https://registry.npmjs.org/@webpack-cli/serve/-/serve-1.0.1-rc.1.tgz", "fileCount": 18, "integrity": "sha512-bsmP4yA64NNGnoYLSf5FvYh9lrVdN7b8ekUga2U4zmepvprFaGYgut25utIwJ5IEZvnNWIv4FVVT9XE67EynRw==", "signatures": [{"sig": "MEYCIQDJ+iXLFOv/kBe1gRdH8dPJ/pbGtmoRG1asoj1ZtomHpQIhAM4srWn9EaSuVAXlDSdM8RGlL490gLBIYD1bVmgA8tMs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15018, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJffKyLCRA9TVsSAnZWagAATlAQAJD04jYDWIR0Etj0ULK6\nRciQMs08HW/DeC63hRTaDtLsv3geTupH8qOz6zYZaENdG/+VkPNnwnY9Rzdq\neEewSZL4kvXV7zsKWH/X6DBWfvTLCs3pvNvWewntRYCK6NUD9N+gPIQG4nEG\nesYIicKLKVBhJWzxgGu6XgT6yuAlDCfP5XI5UvaSu5IsnudZXYC+NYiS7aOW\n1f20+Qv0JQ67UzaXN/WZuUZGkb9vMgQGzyKZecqJ7cSLfbFshEm+N1xsGsds\n51lM/rrHZoWM4IeFvrdaIFSavTE3qzt+x8u94+HO/Dusie0EOs5WMzULBMok\nME4ygRtWS5x0g/GrYoW+6PJRcmVw78u+8/q2oPhksA8cXdTy1FiDTtKuELRS\no64WaLqRgx4U31R/Xe4jhprTZLX6vXwHOSMuwWWDrwJ2qPWpNMpj7oEPw/Kd\nZhW770fWP2MLiJIduhRvA8FeZMtkFp9rgE2+t9vdpRnGCpdH8wh/wRrIj2tg\naUEfmTE1Q2yDPsAPeHA9+HPDkMWyGUmLR+EElsXQhLckzhL/rkkEgm6qodl8\nG5FK/xyNEaQkNjWGkE2IyNRI+Jqx6uS+h33q/2Jbw3NhCh1Z3cW5QTONS+H2\nkybcE3gIK8poAqCqcLYyAuv8itXDv9mvloh160vMeV/FWl0/Rpl/T4iKUotZ\nb4q3\r\n=0fo8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "148ab84de37a8a5b39e8c6da9efffcd85bf63002", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "lerna/3.22.1/node@v10.22.0+x64 (linux)", "description": "[![NPM Downloads][downloads]][downloads-url]", "directories": {}, "_nodeVersion": "10.22.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"webpack-dev-server": "3.10.3"}, "peerDependencies": {"webpack-cli": "4.x.x"}, "peerDependenciesMeta": {"webpack-dev-server": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/serve_1.0.1-rc.1_1602006155097_0.27215264475610823", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "@webpack-cli/serve", "version": "1.0.1", "keywords": [], "license": "MIT", "_id": "@webpack-cli/serve@1.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ematipico", "email": "<EMAIL>"}, {"name": "ev1stensberg", "email": "<EMAIL>"}], "dist": {"shasum": "28abe7dcb18224ccd4b4e2d37f70e5be66c3d6a9", "tarball": "https://registry.npmjs.org/@webpack-cli/serve/-/serve-1.0.1.tgz", "fileCount": 17, "integrity": "sha512-WGMaTMTK6NOe29Hw1WBEok9vGLfKg5C6jWzNOS/6HH1YadR+RL+TRWRcSyc81Dzulljhk/Ree9mrDM4Np9GGOQ==", "signatures": [{"sig": "MEUCIQDt9LGpJo2d6/+lvlaKlYR5HZdN1WxFafwIIJr8udNLNAIgaABoe1rs0MYtGVy5gN7ReqiUWERtqb+Dhx6gwspl3rM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14189, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfgfnQCRA9TVsSAnZWagAAo14QAID571ql/pc/412HCgRU\nEc6W6MlQvGYHt0L+/kWhnIvb1cQW3OgtMNYeM4mtZ3xIfd1WqgpX4I+SLjeD\nxSyADh26RanTkfeZBeRiWgSfE77zlzxbyesFWz9by1Nxfv7xSnJEaEGIKQwj\naQmYlZRDUV7c4onW/D058bTtyqu2HAh+xdeLmO7zQKtjLp/TIn6VMF/X8sf/\nm2iNThFERiSAswRDo2keD6I0S5JzY8O1JclV5y/mD52axawGPNSL8BE7uKF+\nab7v7MYYpTctaq68B/Dco9IZ3tETiSVrUkiasROgOTV3Br7b3oEeH9/0vORV\nOw44mSiQonhu73AWDnsg855id2cVF7xNEH2LCCpqjZeUwLPA8QEv4R2Pmo30\n6nifAgUagxCTe3t2m0mtp72omDukZYGJ2j7fObWIsqjE5Eg7hs1Kw5AbfFHS\nWVkBQjeErZkuGMFW1IKmsnC7I0a3dsu9wgY2PeFaPzLkt/k5gE4VNNCt/dt7\noCV/YolsafyF3EOcd+rX4UaCbW/SUh3BjzkI5r1cbDw2M6513mriaB17rvfh\nvnj1p8FVFuYePWW7EdPV7zMYOFMeB51gF1hMcehgNR5FrYwgqmCujZEI3sUh\npJKEpbL1pqom/pP79F4BpyWG3OE7tvY/HJvf2Ffz1ypPCJroksPAu11j28GB\n9eLC\r\n=NhUx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "fb50f766851f500ca12867a2aa9de81fa6e368f9", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "6.14.6", "description": "[![NPM Downloads][downloads]][downloads-url]", "directories": {}, "_nodeVersion": "10.22.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"webpack-cli": "4.x.x"}, "peerDependenciesMeta": {"webpack-dev-server": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/serve_1.0.1_1602353616038_0.426521043317849", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "@webpack-cli/serve", "version": "1.1.0", "keywords": [], "license": "MIT", "_id": "@webpack-cli/serve@1.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ematipico", "email": "<EMAIL>"}, {"name": "ev1stensberg", "email": "<EMAIL>"}], "dist": {"shasum": "13ad38f89b6e53d1133bac0006a128217a6ebf92", "tarball": "https://registry.npmjs.org/@webpack-cli/serve/-/serve-1.1.0.tgz", "fileCount": 18, "integrity": "sha512-7RfnMXCpJ/NThrhq4gYQYILB18xWyoQcBey81oIyVbmgbc6m5ZHHyFK+DyH7pLHJf0p14MxL4mTsoPAgBSTpIg==", "signatures": [{"sig": "MEUCIHDr6MPAdjIxL3V6/v9z9wCy4aPYX5VfSid0Vg59ZLKOAiEAt2Ii6A11AFSi4sfj8SGxgo14egcYmvB5ywZo/8z9p38=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17737, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfovY6CRA9TVsSAnZWagAA69sP/04rHteYT57C/cbz1rmv\nVN42/ZOhWuPmpsHVLqNn/WIJBK2quwqLG/STVFsbpvfy20k/0Duvu5XMjszb\nFowv+mKz7PikAKgkvZmha2/loopAzI4csCCRPipuhy96Zpf3jqPWRngCzfUr\nwVVkOHs6avYFPgCBNyLAqRpiHG3EhqPsT52C4PgqSY0kHDw3sOc8NzCq+eIC\ncZG7jrWX6PDOjskf8H9tHB9xhmU1AwpHpMB12OS5IymFb3OIMgWKCKcfnZZ1\nA9fm3d0P9wSGV89k88C3vjOXBHDCuFFEdm7gTg/e9cNU8mCInVD4eDd/RHVb\nV60XJl/BzJXan+W9kGUWC5QFGJceTZsoaY3vmVdhrKmTRL/Uc7P/YqVZ35ZE\nvEvv4Q5Vgq20OvjXJWDzsPKFVx4VV+EDirAxOgR0rOZT3wDdRcG4y09ea6kl\nCT4rVfEEQWEsauFuybBXHj+GIV/UF2y3ADKminquNb40bNorrwg+s0OQFkdB\nXB8WW/OGXa4z/xVorDo42sFd0P0TF3yzbcBsEjMCHhwFe8Mp05Mkoh4dflAv\ntAxqwIhGawzaQhrdIPD/wqNq2o2+NSLf7jkbj5IB7ICA2zE/WWiWCNPhCXUO\nP20VaaRxWuVqZjnSymG2g1sLgrwFOWdCVuvM8w7v9lPbEkpHc+mFoZe4I9b0\nXKfA\r\n=qzBx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "0caa9184e22ed857e175c8dc0dd1e0a26f216374", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "lerna/3.22.1/node@v12.19.0+x64 (linux)", "description": "[![NPM Downloads][downloads]][downloads-url]", "directories": {}, "_nodeVersion": "12.19.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"webpack-cli": "4.x.x"}, "peerDependenciesMeta": {"webpack-dev-server": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/serve_1.1.0_1604515385966_0.9367998070765162", "host": "s3://npm-registry-packages"}}, "1.2.0": {"name": "@webpack-cli/serve", "version": "1.2.0", "keywords": [], "license": "MIT", "_id": "@webpack-cli/serve@1.2.0", "maintainers": [{"name": "ev1stensberg", "email": "<EMAIL>"}, {"name": "ematipico", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "8cb2c1e95426f5caed1f3bf9d7ccf3ea41d85f52", "tarball": "https://registry.npmjs.org/@webpack-cli/serve/-/serve-1.2.0.tgz", "fileCount": 12, "integrity": "sha512-jI3P7jMp/AXDSPkM+ClwRcJZbxnlvNC8bVZBmyRr4scMMZ4p5WQcXkw3Q+Hc7RQekomJlBMN+UQGliT4hhG8Vw==", "signatures": [{"sig": "MEQCIBh93ItrkpBcHi1SlA1+EJOXKANpFf8coTIfJZZvfDGmAiAAkCspXi7OEnbZWgasqchV7i8B2Cu7JqkMHUQqiuEFzQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18687, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf5iRtCRA9TVsSAnZWagAAIfMP+wf2SpsnkqPOZ4lNHVk5\npl6O/R0EsGwAOB5ms+nQ8cAT0RGJvs73iauwDUBCVWa8feO9UM6MY4XqGY+j\nyJG0BXHV24ZtTTWC1IzzKXkasexs5wROAc9xX+MdkVv27b/OSbQDY5/zlbD4\n5qM9Qy1yMYOHkwsJslldRTJ8V2CIZTe8bbnjiLnRAb+bck7ACmVpvKvnBEUX\n5Qd2sW1Y7lVs8QzpD09lbecuSDeCB73rU343EeE5VA4ZGP04/vBK3d3wVKul\nwb/nMsDxcvGOB6aUPt7Lfx7CdT7n8c1OgYrMCHlbma6TWLqjwE4pzbr2nCs0\nMG6eUj5jB5wOcT4U4wsH+xaIHjbdI+vfrplXJFv/uyd/ntl9r/XZVstA+qJg\n2/gq+GMJBRL2FX+BrefPZ8rk6dL/lkV1lGmCJCqdPlWxmFV315hpFDTjOUuE\nzf5tc4zoz4i6RJ38W0+RF/swYakqL3ju81OWWZcFNVqPIVNRZnu/iQhBN1nH\npPeqhpafCnhUTT/OLChpB+2FWQpCVTukpeGNxjv86OHxnzAcklXu6p1L2LGV\ngKpDXXjz5nYyrGtFUkq6Z6u0LJFMYkzTWzk+nwUxHspj0Vs9N2QrN1hbyYFi\nI++/fOMcEWtwjGuzdz0ZwIyVj0GgS58Xlf1QQM93ETlj5QqJ2BUTSbHVAFUS\n+PTk\r\n=zJPI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "7c2311a541d93e67d9c328f26b96d36418eac823", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "lerna/3.22.1/node@v12.20.0+x64 (linux)", "description": "[![NPM Downloads][downloads]][downloads-url]", "directories": {}, "_nodeVersion": "12.20.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"webpack-cli": "4.x.x"}, "peerDependenciesMeta": {"webpack-dev-server": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/serve_1.2.0_1608918124987_0.8889456286364528", "host": "s3://npm-registry-packages"}}, "1.2.1": {"name": "@webpack-cli/serve", "version": "1.2.1", "keywords": [], "license": "MIT", "_id": "@webpack-cli/serve@1.2.1", "maintainers": [{"name": "ev1stensberg", "email": "<EMAIL>"}, {"name": "ematipico", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "7513d7a769e3f97958de799b5b49874425ae3396", "tarball": "https://registry.npmjs.org/@webpack-cli/serve/-/serve-1.2.1.tgz", "fileCount": 9, "integrity": "sha512-Zj1z6AyS+vqV6Hfi7ngCjFGdHV5EwZNIHo6QfFTNe9PyW+zBU1zJ9BiOW1pmUEq950RC4+Dym6flyA/61/vhyw==", "signatures": [{"sig": "MEUCIQDZbrA3AiyKxFFNyWCzD1S2gargywRp9M/pSz8SuUEzPgIgXxu4sVA3hH6XgD6OYJgQlhtJXsKJzxTotwOZFqLEpaY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18167, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf7gsYCRA9TVsSAnZWagAAFloQAIkZEw4nC2vrXK4kTsPV\nwGTDYDRhN+9d/4jtVeCWwz8xLDZrZf7px9nxrI5EsG+wqGiGFkJNxNGXHAG5\nPfhRUiJD/LYW9ckZA/77jC8Q7yE4X+Fcko1/oUUFVJG8VWCV0lQwlzjh1saB\nJkdkC7l7X/lhf34ZVnOkMUVUlg4Z/2hpj5ZAB1JvuHnWSC5Pov0DZ0Tyh6hN\nLDzzwvT+wEp1MuVcvrVRoUqIh0CONYZBCWdFEXR2FTet9KeXqws+lCtmx5Lo\n8QZjQh01Z2vkIKg3F4qrLz+x/kHWbN4KJPoUYyrGZqe7dNDtsmB9HhcvfHpW\nrRCC0bgwCaLWHuXshrrKXo7puTHb0NS2wl5xa6agcvd42GG3fShewGZFhi50\n3SetZmGKn1S1SGQF5o/z1Ksh4mw8Z2voipoWPBKUJdzLGPn2o0GQBZrCgoC8\nbGo3jQhRNaIu+P6ZUNtzbqW/dDQCuYJin59Lw88WM7jDhkJTlP5anblx92zO\nx/ibmPsa6n8ZGqdJ7/l8q9xBsurnX5eCA7K7wsLui7ZickaZLYzCiaIaY+BQ\nGE05Vdgq0lGJVO87Q0aSYv8ynbp2AUJ4tqcbt276D/l+51f2+TukHPlweMRy\nvntuqBsEOJ9BIQAHrOOwjqe1njUvcP173cAodDGZwdBmUmL6wJbKwi/Jiwq7\n8PMS\r\n=Jyjl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "fb50f766851f500ca12867a2aa9de81fa6e368f9", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "6.14.10", "description": "[![NPM Downloads][downloads]][downloads-url]", "directories": {}, "_nodeVersion": "12.20.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"webpack-cli": "4.x.x"}, "peerDependenciesMeta": {"webpack-dev-server": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/serve_1.2.1_1609435927543_0.5055963981647782", "host": "s3://npm-registry-packages"}}, "1.2.2": {"name": "@webpack-cli/serve", "version": "1.2.2", "keywords": [], "license": "MIT", "_id": "@webpack-cli/serve@1.2.2", "maintainers": [{"name": "ev1stensberg", "email": "<EMAIL>"}, {"name": "ematipico", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "1f8eee44f96524756268f5e3f43e9d943f864d41", "tarball": "https://registry.npmjs.org/@webpack-cli/serve/-/serve-1.2.2.tgz", "fileCount": 10, "integrity": "sha512-03GkWxcgFfm8+WIwcsqJb9agrSDNDDoxaNnexPnCCexP5SCE4IgFd9lNpSy+K2nFqVMpgTFw6SwbmVAVTndVew==", "signatures": [{"sig": "MEUCIHO1u1p26IeI++AWPjmhq3fx6YLNH/S1Gz3/mzZ8WlUvAiEA36pXtZT8CCIIMb1RaZZFIrxwivh7mtlwEUyc6hFF+B8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21058, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgBsIzCRA9TVsSAnZWagAA6yYP/1h32as7anVmbW0aA8/R\nVtLOvkTO8rZUL/VBmsg31nsGN045xmkQ7N+TBOJy9KMui2OEEOZwvWiaAMl5\nlBBr0+oMldyfWQV8hvbTefAModlvWE5ruwqDWJXlZmgV+yDYsvDzFJOxSc1M\n0etf+Gvu/p8rr14U1nZFsL1pqEQT86ystO6lB3HAdpF/rtuYgF4Hg2pjPMep\nFh9nqpl6Kpry80pRGEDMNzEzcnWAP7qX+XuoIG/niGTLMEfuVRnhgLII3jcz\nIFB8UFmRJLygQzkF/WvLpdl1tEOrcgXtns233EXPUOcyuzZ/BkGjqQbKtbQ4\npAvu8g/igkTx6PQWKSFwdi1ud9lTEfeI63NwvgP92QfZ2K4OTm/0sDtIhafl\n7HLJxKQyVL8DeTOzgS8JBslYgaEug56/NqRdtUpqFoqSVU5X83/dkfzU0Gd7\nVtLQJxghME/ZiB2TtTwiHOveIe1KVyChVQiSIDFc9Z/O1UMuVvOuVP2FbVVo\n/E6lWo/UHv0oOYwDBxHGWYnYM2mxO8osJeWOMStOkYgM1vyetZeZP1fxgWAF\ncWZEbWOXBfibq8wVvzVUqOePpC6JkB+Zl88tu6UVsaVCUpRM/+yD7Z/GXLsn\nGeg2It1abv0k6OS3qGz9rwLBpzjgHJUOHVTiOC/zhwo5M+xV5I2qYiHGO1Kr\nq/MJ\r\n=3J6d\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "f55d44d243d6e101f7e7c88ddf0e7c567e33bd08", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "lerna/3.22.1/node@v12.20.1+x64 (linux)", "description": "[![NPM Downloads][downloads]][downloads-url]", "directories": {}, "_nodeVersion": "12.20.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"webpack-cli": "4.x.x"}, "peerDependenciesMeta": {"webpack-dev-server": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/serve_1.2.2_1611055666773_0.9420330654867468", "host": "s3://npm-registry-packages"}}, "1.3.0": {"name": "@webpack-cli/serve", "version": "1.3.0", "keywords": [], "license": "MIT", "_id": "@webpack-cli/serve@1.3.0", "maintainers": [{"name": "ev1stensberg", "email": "<EMAIL>"}, {"name": "ematipico", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "2730c770f5f1f132767c63dcaaa4ec28f8c56a6c", "tarball": "https://registry.npmjs.org/@webpack-cli/serve/-/serve-1.3.0.tgz", "fileCount": 10, "integrity": "sha512-k2p2VrONcYVX1wRRrf0f3X2VGltLWcv+JzXRBDmvCxGlCeESx4OXw91TsWeKOkp784uNoVQo313vxJFHXPPwfw==", "signatures": [{"sig": "MEUCIQDklVcy3CDQpC0I0K5dkA9ykb69/+cHTkrxehZEVZQEqwIgH2BnOgUEeyK2adCsqFlV0j8R/VUrhXL0Aeg5zLcD9lU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21899, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGYXsCRA9TVsSAnZWagAAH58P/1G5UggSEgQVmSN5Tw1Q\naBusNJDztz3BQIc7EnsR4WZ01kg9SKK1JX0SEZDxIVlml9RNt2JqSvbpLx3k\nyNe1Mf7HspUq0UOe9NIoVg/caof3CCgaZ2yO4kT9dQgZJL6prrTqc/AROkAd\nB3CDtdT5L0XhiGScX/QbKbja7h3BtEU6ahZV1rrtqv+rzwoQpK1yekf7D/iz\ncbQHgAfhgPPj245oDgkg+yA5tvVsxqPq3zGR7vh8fcpY8TkAGPr6svgyj/ws\nfQeER8eDAhgpNFMyPwBNt1A1ZfTeIav5frJVQ/WXnt9YzkMsIEAjYTvlLlcs\nBhzWnkNZC9ra/KV1IIUf1xLNexQrWR8a6SMjHFgqPZvFnEYtKVv2WGKaLwer\nyRxMbEgGbe0BUvw0Kd+0y5SjOd2xvotvNqmFfaNK6mAtpY8gHeo8Adwu21Rx\nZ1GHGiwd6RTZYzc/SS2rndWwlgQBuK+BRwLi2E8wRpqJhD4gnJfvMkQrSmIP\nIQNFGzWcb/kBCnLxquIWX75bXYlCgOGJddQbkY8iuD8T8KYSpAcowNGrt5GC\nlMNV9H10QrVFp2xU9cqc7wRALt8QCfsHYHutsbFPVS1NwrgjpLwdJWCPQypl\n/y97MvWnojZV1dQ5zGDQFr3ppViZFwx7CrhO8jAxx6Ks6TJqq/U6PT9gPqPK\nZ3ls\r\n=tO/k\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "3bbda71e9637b7d20f3f49f9e080e27d8d8ae929", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "lerna/3.22.1/node@v12.20.1+x64 (linux)", "description": "[![NPM Downloads][downloads]][downloads-url]", "directories": {}, "_nodeVersion": "12.20.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"webpack-cli": "4.x.x"}, "peerDependenciesMeta": {"webpack-dev-server": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/serve_1.3.0_1612285419975_0.893384273293707", "host": "s3://npm-registry-packages"}}, "1.3.1": {"name": "@webpack-cli/serve", "version": "1.3.1", "keywords": [], "license": "MIT", "_id": "@webpack-cli/serve@1.3.1", "maintainers": [{"name": "ev1stensberg", "email": "<EMAIL>"}, {"name": "ematipico", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "911d1b3ff4a843304b9c3bacf67bb34672418441", "tarball": "https://registry.npmjs.org/@webpack-cli/serve/-/serve-1.3.1.tgz", "fileCount": 11, "integrity": "sha512-0qXvpeYO6vaNoRBI52/UsbcaBydJCggoBBnIo/ovQQdn6fug0BgwsjorV1hVS7fMqGVTZGcVxv8334gjmbj5hw==", "signatures": [{"sig": "MEYCIQDByGnUKAQCiqMzpXcbqkStiCKVi5CdLBGEKVlJ74jZDAIhAN18YxU+7wLWYAbmdla9JfHjhoIxa4aSgVQ8qlvwZ+/s", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21467, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgX1K/CRA9TVsSAnZWagAA+t8P/R36qaabdgemGfesoKkz\nwPV0ehiej67EvW1sDvD9xYPZrhc6YzyconyDumx2PR5BKm4zp/Hn8sF9EFrM\ncKYeysQt7RirPdveHuPS5XOab49BLa7wxu8og+nuOh9dZOL1GcaO0uJAdW7B\nEbld3BFf6SMhxVvGVJSjX+1+wAL4XhKusjbIoQ5RgAxTIbiL0bPvwVeg1XVo\nyu3kKKfni64d87WKypSOkCWWB+LcZDN3MX6Uy3MGWfFavZ2kjiOfbihSM5G2\nWT/kth9Nr/Wa9rgxpLU8TSPMjJvsHwUnG7EeBJsD50L+TxLtrNtgqkxvoqHR\nocKQ+IS0/BFQuo28kYhGYDK9NpNbY8uug5oQfDiAnD+UUfps6EnrHkTcghT7\npVwJmOvxDM2fr1w8ymscSH5yXhNN6V6lFaipGmUAQOVB2HcFj8I70OiCCmVx\nAQSZP6NxuTpJg09LyqsH+AeXSZH3RY0knbL6b/1VUjVvtqJY+PiXKBxIS2Nb\nVX40g5we61Ujt2Ru4jGSxsCgKFJ1xmKFQfMJZqMajHSqdk7aQkyZRpRnFmIX\nSY+ZZuHh+QMpYHHtjrOyPVRZnFTD+Zv/u1bQcXVoJZIORpQoqGAtyouI0p0s\nm0CefASEpE1w3aVzRquYcvqREJAfNFVLOY6zwMMPZtdanvfd1MXdK0Ozz2Tw\nMX2J\r\n=ZZxg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "fb50f766851f500ca12867a2aa9de81fa6e368f9", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "description": "[![NPM Downloads][downloads]][downloads-url]", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"webpack-cli": "4.x.x"}, "peerDependenciesMeta": {"webpack-dev-server": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/serve_1.3.1_1616859839036_0.5899753942234356", "host": "s3://npm-registry-packages"}}, "1.4.0": {"name": "@webpack-cli/serve", "version": "1.4.0", "keywords": [], "license": "MIT", "_id": "@webpack-cli/serve@1.4.0", "maintainers": [{"name": "ev1stensberg", "email": "<EMAIL>"}, {"name": "ematipico", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-cli/tree/master/packages/serve", "bugs": {"url": "https://github.com/webpack/webpack-cli/issues"}, "dist": {"shasum": "f84fd07bcacefe56ce762925798871092f0f228e", "tarball": "https://registry.npmjs.org/@webpack-cli/serve/-/serve-1.4.0.tgz", "fileCount": 10, "integrity": "sha512-xgT/HqJ+uLWGX+Mzufusl3cgjAcnqYYskaB7o0vRcwOEfuu6hMzSILQpnIzFMGsTaeaX4Nnekl+6fadLbl1/Vg==", "signatures": [{"sig": "MEUCIQD7b5r7eS9gaweAuAcrAhmoBHxmIEFTFiMI6ccCl/zqggIgXqERmgH18u3N5dCQkJY8BQjYhrlqF5Rhwrn0mnEDpSA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23973, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgk+rrCRA9TVsSAnZWagAA7w4P/3f2TflrYBbYjiwS11YE\nFiCsjdZsQA32Z8jMNUJ+duSVfqcVm2wyHZ+lGN3FEzLB+LRiMBlGhqHm8gEK\ns2AYDVTIo5m16QYUzcUFe94GKPpiW2jim7UiouceKcRI4POEvs7nr0eklttw\nzDSvjedET3AjXVvyliV04E7Xd4d33Cyrpz4VlN/RsBEroW7s+rdoDWDjBWPA\nxxQCyWGx5xbYmCdde00d/C4IvkJ/rB+nlxbv7bt7aKMqyM8wzfWqnKyluAT+\nFrXKnnd0jZzZae7hfdfYCrWEHDagVT3GCEppo3IJUsdJdhfo8FqSkxMj5Nvv\nkzSjNmWVC0LaCgkNFRbnJTX/KJexq3CrRCOR43cmoiWr1jFBa29ard32t278\npcLpnvb6Hv3x/AwwHTphr2/NqEAX8z59VlDQc++dABGP0y83PmuKEBC6s3JK\ne+UKYv+2mO7jN1YDOuXOAgmuDug6BA6ChR9f4HuBAIImipjObRuaZ+vkhA8y\nrTTOR9OA2/f53zYZycXeQzCI217xzZXYNYQe7iy65MkcOfmuYilwPQE0jPvU\n2h2WPQoTeCDZxREdtv/KCHoCN3j7KYV5CTz18UzOU6xSqsWyp8POQ90GmQLt\nOH23szJPVKTi6Nn4xxHq6mdvAxwkHP2lBNZDvUlwNt8/RWzNi7Mcr1m6YyeS\nXdl3\r\n=o445\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "4edf3acf1541344f71cf7da7c3c654347f19aea7", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-cli.git", "type": "git"}, "_npmVersion": "lerna/4.0.0/node@v12.22.1+x64 (linux)", "description": "[![NPM Downloads][downloads]][downloads-url]", "directories": {}, "_nodeVersion": "12.22.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"webpack-cli": "4.x.x"}, "peerDependenciesMeta": {"webpack-dev-server": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/serve_1.4.0_1620306667158_0.7715220388691932", "host": "s3://npm-registry-packages"}}, "1.5.0": {"name": "@webpack-cli/serve", "version": "1.5.0", "keywords": [], "license": "MIT", "_id": "@webpack-cli/serve@1.5.0", "maintainers": [{"name": "ev1stensberg", "email": "<EMAIL>"}, {"name": "ematipico", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-cli/tree/master/packages/serve", "bugs": {"url": "https://github.com/webpack/webpack-cli/issues"}, "dist": {"shasum": "dfdf928ca722c4d8d2e9fe5da91e61dea94ed79e", "tarball": "https://registry.npmjs.org/@webpack-cli/serve/-/serve-1.5.0.tgz", "fileCount": 9, "integrity": "sha512-AxpcbdkUhl4248H33LDFgXpFpDE5/BU9rHi2Oj8J4z8JldoMXoSmCe1DXDKcw1ClK64g6fY1Hg+dW20vH81JvQ==", "signatures": [{"sig": "MEQCIFzoJY1PsEofL0b8U2YoARmyW3ngSYw2I/DuXxTo3bPOAiAk66aVri3k51sj58Zi11+Eoq9BPPpK2TgqvCljPsKM/Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18726, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgvhoBCRA9TVsSAnZWagAAgCoP+wca+t7qFAe5Lm++w17u\nZKulnjlhbl3lFGQVYs69OZWO4K/4orNp2IC1zA92wA2Ta+uHHOGfz6O6r/72\nUSSGGZyJJOgn2eyXpSkQI4WWcUtStXTftKk+BW0lH5athD3q47FjdDhaEv4B\niLWRQmrjAKA4ni0OkWGsTd/SXHF49tg/ecqVs9D4znbz6XLZHwEdSSEJsZkG\nUDG5lPeBNllnmN2PsxJGdfIKbbbJ3QIhastrE04NAdECGQf0mSpFpqAZbkmF\nJ2zcUqGSPkLNuUaLp5bUnX4zpM0z6OLrvs8Ryz4YJVS43CjHTmC7EiNO0Q3o\nAMSZcAVHGo3gS6hbNQL3N2ltg8tP1py6Hy28/fM+L3lkhx976DNMd5IfBZgT\nLuAMwwx5MOHIrQOzs8PRXy3m0iu7B3fgEWFB6a+fzDcv6uoYHnkMNcynSa1i\nQggoS85lMypkffHgILjvjiunvZ6AQ4L5V7Yxuna780SFqY97m+LVgsGd7rCt\nhRlOIEKQA9M1Bgxx+voqdAu1SOVrtX0yz5zNBDQsZir+FXaaOJFPiJjAPkLs\nT36/FNoS0K+YK6m8j1DJQwAy2u9qt5TgxKzVjv7rycpxr5hNKDITXF5CarBk\n7S5otXkkIUv4hdCYdbLVPnIhW9IlzT2g/nNGAoOO118SvQcPXOJUv9sKs7Mn\nGuCy\r\n=C3CU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "2be9b9254009598e021b830091fba8832dfdb57b", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-cli.git", "type": "git"}, "_npmVersion": "lerna/4.0.0/node@v12.22.1+x64 (linux)", "description": "[![NPM Downloads][downloads]][downloads-url]", "directories": {}, "_nodeVersion": "12.22.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"webpack-cli": "4.x.x"}, "peerDependenciesMeta": {"webpack-dev-server": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/serve_1.5.0_1623071233694_0.881311157016321", "host": "s3://npm-registry-packages"}}, "1.5.1": {"name": "@webpack-cli/serve", "version": "1.5.1", "keywords": [], "license": "MIT", "_id": "@webpack-cli/serve@1.5.1", "maintainers": [{"name": "ev1stensberg", "email": "<EMAIL>"}, {"name": "ematipico", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-cli/tree/master/packages/serve", "bugs": {"url": "https://github.com/webpack/webpack-cli/issues"}, "dist": {"shasum": "b5fde2f0f79c1e120307c415a4c1d5eb15a6f278", "tarball": "https://registry.npmjs.org/@webpack-cli/serve/-/serve-1.5.1.tgz", "fileCount": 9, "integrity": "sha512-4vSVUiOPJLmr45S8rMGy7WDvpWxfFxfP/Qx/cxZFCfvoypTYpPPL1X8VIZMe0WTA+Jr7blUxwUSEZNkjoMTgSw==", "signatures": [{"sig": "MEQCIBELcc7/LxgUmnl02oaRhl2VO18TJYhupXAhGAaDky5BAiACNh70D/ZdvBXzA3KBETbonKQYmnqGVu0Bo0hN3OwwgA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18756, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgvkuQCRA9TVsSAnZWagAAGbYP/A0+eeVHWFKVbOyPxYuH\nys5bOEBenWi8tFiNvilfx7nxs9ssEt8qkqQb5ArWMsKIkMEC/ISmkW2699jg\nM3393QEt49ULFpXgn0Qz5MJSlqnOgR7aRRO6pe5Cf9XGhD+DxXEEQleWwOlF\nTltz43g400cwXJAeBNAbRs4qX+GSF8qmIYOqjz9xe4GrOXfJFXD2riOicQ32\nYX7qqMte4uqFAVwdIWWRZ4oBHVeQwvvrsasRS91dt5Jk/WsuofXfgPrWb6/r\nVNcOR3TxfTML4Fzt1UeqYZ2a9LHzy+vFodGnD5LM5pKUw2x4uPbWEBZmi4Hl\n5026XxEu5ajlmrpErufrGZXL4ocX4sc3UqXmzZpWqTDEZrwMuFKtLeVntDaf\nd/bHUn53smuz0q8y11Dhk6Y/r7MuJCA89R9nVYaQd+7EAHzQTwO8qx44G4yW\nbCdiF2SndimDhI0YxONIASgI6+24F1CA0xPha2zSWtRaoq4pVqE51GfCieu4\npiwfQf38fIifyzGpFXBYfa5gqk/wzLxiKw/jW3Ybww8GDAtOZA5HH0Ha3swl\nx1vGIst5ahAEPK++63B2KO+S3/5iSUFaX6MtEJTlaZrdo4teQgGSvYXACnLL\nbMer55WRkpJ2hpLxI0c3ER/YoOXlLY7PySDssxCVa7lhUmQsPg0e6wEqigTO\nIC81\r\n=wrZz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "68ef0563afd105652dc0fd0b2391a0a766cd24fe", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-cli.git", "type": "git"}, "_npmVersion": "lerna/4.0.0/node@v12.22.1+x64 (linux)", "description": "[![NPM Downloads][downloads]][downloads-url]", "directories": {}, "_nodeVersion": "12.22.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"webpack-cli": "4.x.x"}, "peerDependenciesMeta": {"webpack-dev-server": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/serve_1.5.1_1623083920744_0.3489250285977723", "host": "s3://npm-registry-packages"}}, "1.5.2": {"name": "@webpack-cli/serve", "version": "1.5.2", "keywords": [], "license": "MIT", "_id": "@webpack-cli/serve@1.5.2", "maintainers": [{"name": "ev1stensberg", "email": "<EMAIL>"}, {"name": "ematipico", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-cli/tree/master/packages/serve", "bugs": {"url": "https://github.com/webpack/webpack-cli/issues"}, "dist": {"shasum": "ea584b637ff63c5a477f6f21604b5a205b72c9ec", "tarball": "https://registry.npmjs.org/@webpack-cli/serve/-/serve-1.5.2.tgz", "fileCount": 7, "integrity": "sha512-vgJ5OLWadI8aKjDlOH3rb+dYyPd2GTZuQC/Tihjct6F9GpXGZINo3Y/IVuZVTM1eDQB+/AOsjPUWH/WySDaXvw==", "signatures": [{"sig": "MEUCIEC0aIMdJH7veu0hQb8exLDn8XUGzyP2HgKGTXLl1WcqAiEAwCCVjHsXmq88R5QwxvlU8nuYDIZ+5HE0opv5SbZHrrI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20569, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhGXOSCRA9TVsSAnZWagAAxdcP/im+CnwRUwp36mFpx/S3\n0TOMgRpdcsbRuJjCm0KHhTWAn2L2BC5En07rMn9y5GQYSWUXbxxRERdfoDJS\nAyL15WkvvBqhnD6SnP0k0fkDfejKyTagncsNE6nW46oMt7OEvnsdtAckLFm/\n6FrqXETPrD6MCAvGYVBbKDCkQ3KaGLK+Le/HkGjf5UTIzkMFOecqqTBmRuCg\nQR18QzYHNOO21OuwNn2p/17NxSzrYjNCHGEQQ74MaK7ihQZll4OMJC3cPhDD\nn8uxiQTTEtviBfnwW6o9zoz0zzE6K4vze2SEII+g9U7KuuCawe3w3W9NehF1\nVcnwX7uPqy65bsgg04TEFvvvwnoJepjhp6OwQYCETmzLeNianoS7zPDfokl9\nT19eKOFXuZKvebQcC+VVoZTvHS0O07Aeo945uZ9ayHFRrKidNtQEiYxZr+On\nDzk9O032RPS/mM0OaF+DO9PWoZwtRfWiNIKrLLxK+f95AzK+iIK+x8evRxU1\nyvFyOTu/M7AXZb9BZrR2/m/KJBGUl3TdwMydv75+415pWEK998SbFux5RgEp\n76XquXRJ9evSbv8YiUi8P/u8If/4R2tBmmCK3ugMQT1fpBNScW58xOWStXX1\ne05L/gZxiKakqqiUAVmPNlhDfvz2/VzSuPJ1YVZaqNQyY21URmda052/fXTJ\n8elw\r\n=M+Zy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "b66fdc010f029f0079c7511557f5460fd555b9c1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-cli.git", "type": "git"}, "_npmVersion": "lerna/4.0.0/node@v16.6.1+x64 (linux)", "description": "[![NPM Downloads][downloads]][downloads-url]", "directories": {}, "_nodeVersion": "16.6.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"webpack-cli": "4.x.x"}, "peerDependenciesMeta": {"webpack-dev-server": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/serve_1.5.2_1629057937941_0.5098547140723522", "host": "s3://npm-registry-packages"}}, "1.6.0": {"name": "@webpack-cli/serve", "version": "1.6.0", "keywords": [], "license": "MIT", "_id": "@webpack-cli/serve@1.6.0", "maintainers": [{"name": "ev1stensberg", "email": "<EMAIL>"}, {"name": "ematipico", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-cli/tree/master/packages/serve", "bugs": {"url": "https://github.com/webpack/webpack-cli/issues"}, "dist": {"shasum": "2c275aa05c895eccebbfc34cfb223c6e8bd591a2", "tarball": "https://registry.npmjs.org/@webpack-cli/serve/-/serve-1.6.0.tgz", "fileCount": 6, "integrity": "sha512-ZkVeqEmRpBV2GHvjjUZqEai2PpUbuq8Bqd//vEYsp63J8WyexI8ppCqVS3Zs0QADf6aWuPdU+0XsPI647PVlQA==", "signatures": [{"sig": "MEUCIA2xqUOaQsLeRveVwkC8pmtgMCR9OfxRlZIniDr1+48uAiEAwh6r4lf0uv9X4uqemXv9cx6Wcwuzy8xrEtExBnTFfjE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19584}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "fb50f766851f500ca12867a2aa9de81fa6e368f9", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-cli.git", "type": "git"}, "_npmVersion": "7.24.0", "description": "[![NPM Downloads][downloads]][downloads-url]", "directories": {}, "_nodeVersion": "12.22.6", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"webpack-cli": "4.x.x"}, "peerDependenciesMeta": {"webpack-dev-server": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/serve_1.6.0_1633553267683_0.0032804271358444215", "host": "s3://npm-registry-packages"}}, "1.6.1": {"name": "@webpack-cli/serve", "version": "1.6.1", "keywords": [], "license": "MIT", "_id": "@webpack-cli/serve@1.6.1", "maintainers": [{"name": "ev1stensberg", "email": "<EMAIL>"}, {"name": "ematipico", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-cli/tree/master/packages/serve", "bugs": {"url": "https://github.com/webpack/webpack-cli/issues"}, "dist": {"shasum": "0de2875ac31b46b6c5bb1ae0a7d7f0ba5678dffe", "tarball": "https://registry.npmjs.org/@webpack-cli/serve/-/serve-1.6.1.tgz", "fileCount": 6, "integrity": "sha512-gNGTiTrjEVQ0OcVnzsRSqTxaBSr+dmTfm+qJsCDluky8uhdLWep7Gcr62QsAKHTMxjCS/8nEITsmFAhfIx+QSw==", "signatures": [{"sig": "MEUCIBRg//BnWtqmm3wpD7kMgtSn0mYuoN68zmQ2A9RBkMAVAiEAwV7ymfvlDM+Havy0zQob0RAxyfAVrpASW2WAhzu92tQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20333, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh7qjlCRA9TVsSAnZWagAAO5MP/jnoDXSNrjQ54O+hkExZ\n0L6S34g4t4j5hG3MOyVWOgp7f7VSqOz30xitsjqavyYxGGzZes3vpM9qdk75\nz2lDslZPTV9kLYA9/p8Pg5jqKi8LWbacd2EKF1+E0mdJzmsMGoAWtRfnKfzV\nXJ0tBJgGJN+5pzroih2zhhd0xG28dv9RMBwwRzya6Qn9B30xBfdKYk0cnlEW\n9UpqVJtFaT24yvTbqmgxKFYErZaGNGc0IEsATW+tVT4Y3coa5r+wN2bY<PERSON>iO\nO9P9kcFmsan8ChZrwh0WVaDGRML6zHKSO4b99Yrf//TrmKMfhB+EbxbvnaM2\nlIe/0z7yNQj/+de+kk5gCNKnI6beKtn0R7quzfVlsD8wECZI4Fz19mxPFcfC\n14x2BNJ9OQ/Pne8CQgeSCvOQXr+AtZ2veybyumWWEzZGP3HFLUkxRMMBS7L4\n04co7+micE5f2bVZky7+d/e4kxit2JHsaRYrQpxAVDgkLWGO9o/ev/oBGrxA\nYKyShPUBnY9kJ+zhRW62ZV0s+xgZKJshjF2V2HezUIf7ci/S3icyflAQpU9e\nWPCtnyJ2mjDLbkPQBUP50RBEGfLi9oxjrFo0qKlw749WqwiTqDkd2DODucku\nZP4UzTLH/tu1AwXhAZb19JidV2U4awZH40ca68JBB4/7zN+Obqin6Ayvbs0N\n9K3Z\r\n=+OFm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "fb50f766851f500ca12867a2aa9de81fa6e368f9", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-cli.git", "type": "git"}, "_npmVersion": "8.3.0", "description": "[![NPM Downloads][downloads]][downloads-url]", "directories": {}, "_nodeVersion": "12.22.9", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"webpack-cli": "4.x.x"}, "peerDependenciesMeta": {"webpack-dev-server": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/serve_1.6.1_1643030757737_0.4513740498486718", "host": "s3://npm-registry-packages"}}, "1.7.0": {"name": "@webpack-cli/serve", "version": "1.7.0", "keywords": [], "license": "MIT", "_id": "@webpack-cli/serve@1.7.0", "maintainers": [{"name": "ev1stensberg", "email": "<EMAIL>"}, {"name": "ematipico", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-cli/tree/master/packages/serve", "bugs": {"url": "https://github.com/webpack/webpack-cli/issues"}, "dist": {"shasum": "e1993689ac42d2b16e9194376cfb6753f6254db1", "tarball": "https://registry.npmjs.org/@webpack-cli/serve/-/serve-1.7.0.tgz", "fileCount": 5, "integrity": "sha512-oxnCNGj88fL+xzV+dacXs44HcDwf1ovs3AuEzvP7mqXw7fQntqIhQ1BRmynh4qEKQSSSRSWVyXRjmTbZIX9V2Q==", "signatures": [{"sig": "MEYCIQD9frAQGB6KeN8UvTH7pDPZ6J0SNOvPT9UKKDRIKUG5ugIhAMIH3uZTCw2SahrJ7VpZRB2+RRb24XWT07U6KvSWL7xu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17448, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJipodgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqF6Q//YwluHXU/s4Iv+coAkbIpNBf8GU+WYJmfn1dXhVqXyEzQcr8O\r\nDiCBI8q32sjj57YrItIRNckL3GjdDjAPe6b6dEeqQrMFCHTKcxiS0jkWgyiC\r\nrg8Q6yqu4wjvEjqga9iB7ND7L96UP3sJWGcSHMiH4tGpt68+Yntfh1Iot7Wn\r\n7bbGUpqesqHLPEiKsrsFOLqbCkwTc0cMRe1ErUYPfyS+hK0O9nmovvnSMqU8\r\nN07saRrU0BncMf8rFsVicT9rPU3QnsrBSXRzqf/gjdUbfvrQJZl9Ad4jkn0l\r\nG9EGloBGH9oRrImV/TKwR0YMRAT0BglcnnXReLV3Sw/c8OEVfzxfNHYkCtor\r\n//EZe1F4iNRL5HT//zqlvsoYPVi/SJXwsbRG6yF/U5S94/ifRQHBdpEb6bNk\r\nWLBxkcvojlBaax5a6Akosi3ssT/pz44SGkp68NgbLYykd2YXnQR2llXqS4Fc\r\n25QmJvTmCI08W9Z4zOIHr3OdPnCybsXilvTlW5Zm+XgFGaBRZ03lvOzkat5u\r\n480lDhiRZj4yJcl1pNyeRZRkBtEurQfegkFuaWbjQfTvXBea0jhFP8zy37R0\r\nTVVSaAU01QQzOMQxRpTCPZWGNaXl9RYctnk8VMHghlOj/B7eTKgxscEsZK6y\r\n4JIH57barepZmjtyfU3fWcVT8TsFaWX0nKE=\r\n=xa9I\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "20882d463450d010bb76e0824fe555e9785e9561", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-cli.git", "type": "git"}, "_npmVersion": "lerna/4.0.0/node@v18.1.0+x64 (linux)", "description": "[![NPM Downloads][downloads]][downloads-url]", "directories": {}, "_nodeVersion": "18.1.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"webpack-cli": "4.x.x"}, "peerDependenciesMeta": {"webpack-dev-server": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/serve_1.7.0_1655080800442_0.6273126441520676", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "@webpack-cli/serve", "version": "2.0.0", "keywords": [], "license": "MIT", "_id": "@webpack-cli/serve@2.0.0", "maintainers": [{"name": "ev1stensberg", "email": "<EMAIL>"}, {"name": "ematipico", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-cli/tree/master/packages/serve", "bugs": {"url": "https://github.com/webpack/webpack-cli/issues"}, "dist": {"shasum": "f08ea194e01ed45379383a8886e8c85a65a5f26a", "tarball": "https://registry.npmjs.org/@webpack-cli/serve/-/serve-2.0.0.tgz", "fileCount": 5, "integrity": "sha512-Rumq5mHvGXamnOh3O8yLk1sjx8dB30qF1OeR6VC00DIR6SLJ4bwwUGKC4pE7qBFoQyyh0H9sAg3fikYgAqVR0w==", "signatures": [{"sig": "MEUCIQC5bXrUdF/JnRPgFi9Mca1n/ZU3T0VwfFisyOpQUIHELgIgfHfFir5+f0e6u/8+3ItUufpbqVPy5COB6jB+jBIeUuE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11957, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjdbmwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoTeA//bGDbjv2NKOVOtWZSvBjaOGpYZfOcANta4P7BWcUXInYkf1yp\r\nr8ZExth50SKRtPQQnQtga5AeL/+oWaEfN8rkgubMODi/U8BzzftqVqir2vZm\r\nxvubCub88+qTPymj8VXom9vdROJ4yH5ao+5D2AQ5wT5Wi5YDb5MAy8ZtNDex\r\n9xjrvUeV6YngP5DpQ6Gcn9KjaZQFI3I49MEFAQN2LhC8D7KsZHOYM2flG9Ec\r\nbul0xBaNFOvL8VWyjWYiDRQMtz4xPUYw6HIrjoR5+hMplukjdRHm1zeIHQLq\r\n1UVJ4rvlcrqnfNkWL8fOX9M3KgL4xLBSuG4D3SEIc7uXZ8RmD+AZwQfOKHy7\r\nUzj4R39fJmWZLd/SrbLvRAjQxxHDiu/MA16FsNzeFEsKr3UwqcRoGigZiD5u\r\nNylT11kPduY+DWuR/UigjjNQM8ZXqxnJoOd/tsaghMcWDflQ9msF09sbULYZ\r\nppEA9fX+Uc6GbOaS/ZSY/VHoMK2EOjoVXXU9a2hXEXXsV4eeKBt3jriEnhsJ\r\n7MzcSkrgGCh9Mi1pnv/8946yjmJeV9XYeC0f9ui/YrBU3RgW51rrx9tfV6zS\r\nMelGoHKlYpIsazWXIJJEFGHqBO2k9CnYKjEHNy9KVBCDqXbbgQJaMPT+6L9j\r\nxkRNMvbnmf7e3o39K9HyA5zD82yi0ELzI1Q=\r\n=kp4u\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "engines": {"node": ">=14.15.0"}, "gitHead": "1d6ada1a84c68a00e56c536d2f004f60939bd946", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-cli.git", "type": "git"}, "_npmVersion": "lerna/6.0.3/node@v18.7.0+x64 (linux)", "description": "[![NPM Downloads][downloads]][downloads-url]", "directories": {}, "_nodeVersion": "18.7.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"webpack": "5.x.x", "webpack-cli": "5.x.x"}, "peerDependenciesMeta": {"webpack-dev-server": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/serve_2.0.0_1668659632250_0.6092670512008738", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "@webpack-cli/serve", "version": "2.0.1", "keywords": [], "license": "MIT", "_id": "@webpack-cli/serve@2.0.1", "maintainers": [{"name": "ev1stensberg", "email": "<EMAIL>"}, {"name": "ematipico", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-cli/tree/master/packages/serve", "bugs": {"url": "https://github.com/webpack/webpack-cli/issues"}, "dist": {"shasum": "34bdc31727a1889198855913db2f270ace6d7bf8", "tarball": "https://registry.npmjs.org/@webpack-cli/serve/-/serve-2.0.1.tgz", "fileCount": 5, "integrity": "sha512-0G7tNyS+yW8TdgHwZKlDWYXFA6OJQnoLCQvYKkQP0Q2X205PSQ6RNUj0M+1OB/9gRQaUZ/ccYfaxd0nhaWKfjw==", "signatures": [{"sig": "MEUCIQDkdDg3Jr5B8b/vWCiaozRvw3gX/GHfRIj9Z+OGlfgz6AIgKpPYROxtHEc+uM034Zz8ScvPFt/F2QjK0mU5Vib85/E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11590, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjjitvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrENg//W+n6vxU+eYEp3ILhDPfFYfv9ntAhH0/cYE1iAAkkFQcq3oEh\r\nwJby+22Vvn8KjO6KfcmnK9l6+pOJJaGCT8n84aA3sfSyvzukO2+wPiM30AvW\r\nCszCWTTApcJV/+wMm3vtFcpcA45LBT+fE+MyqbVD6x3wWIjTOIWoDXIeb/Fq\r\nn5v5kiH/Y/XvdDVaK8rJ1HBOG6SlHkWWxjq9zNs7/q9/vwRcOz6X8sVnnpm3\r\nLbT3+uJj3BpHoKAQZq8AtCEg0sLGsz81fD927dLSOg7qQVtxCePx9n9ORV4l\r\nP8p1kLxaE0pi8cTv/+eyCuY+8/QT1kjX2talU4j44DJDfci6HLoKIuUmNYgh\r\nrU1Hcavhmq1t8gI4H6hZ3AWLBUHbZRDJknlHofWsH9av/EEhxaiy5T6lhhp6\r\ngIw79WqeQ4vsuZ7IVvmk9wf3l9qi1TOjTtLZcHNYm4Vuv+LWL4RbvaahqwaM\r\nI3Uc6/yw5+SaYX8Tp+pC4RhmUpNw07KQsPc7+I5RdVaoCPGrR3C6Q8yZxMlW\r\nCkgNWz6WWCGWEWMzGRDfmgwtnF67tsYOnv1aKj/uM9n6biFIYRwhUtJfP1tS\r\nM9jRQV1GLCKlJpVQH2qpZm4l65Nb6Q8BHNmcQS1MNS1a52Qg6W5x93G2kKdt\r\nLAQmhtSO/Kl5++qWcAbs+IJ+5079HmpP2jk=\r\n=46WM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "engines": {"node": ">=14.15.0"}, "gitHead": "4a0f89380b3f920652bda7dfc0c23405099b94b8", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-cli.git", "type": "git"}, "_npmVersion": "lerna/6.1.0/node@v18.7.0+x64 (linux)", "description": "[![NPM Downloads][downloads]][downloads-url]", "directories": {}, "_nodeVersion": "18.7.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"webpack": "5.x.x", "webpack-cli": "5.x.x"}, "peerDependenciesMeta": {"webpack-dev-server": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/serve_2.0.1_1670261615675_0.9283898733445695", "host": "s3://npm-registry-packages"}}, "2.0.2": {"name": "@webpack-cli/serve", "version": "2.0.2", "keywords": [], "license": "MIT", "_id": "@webpack-cli/serve@2.0.2", "maintainers": [{"name": "ev1stensberg", "email": "<EMAIL>"}, {"name": "ematipico", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-cli/tree/master/packages/serve", "bugs": {"url": "https://github.com/webpack/webpack-cli/issues"}, "dist": {"shasum": "10aa290e44a182c02e173a89452781b1acbc86d9", "tarball": "https://registry.npmjs.org/@webpack-cli/serve/-/serve-2.0.2.tgz", "fileCount": 5, "integrity": "sha512-S9h3GmOmzUseyeFW3tYNnWS7gNUuwxZ3mmMq0JyW78Vx1SGKPSkt5bT4pB0rUnVfHjP0EL9gW2bOzmtiTfQt0A==", "signatures": [{"sig": "MEUCIC/u4Kll1yb+q3xIr7/QmpL01gL6QUGZ4jq0CD9G2Cg1AiEAyGd1aVerFXB53UHkT2c/EBRIuoaXBqV5zfI0PjFp1B0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11590, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkQrSdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoJcw/8DiKmGMll99R4jzfpj0skxrovkorkesB1tXPD4+dH4A+rKU94\r\n8XasdoBJTulzZVHfVzIiBasZVuGu+KQBw0N/jmA2J1ZDWeCMMzfN1IpwxBKm\r\n5VRxT66wI8x7nzN0UQNc94+LNABuXR2LrDoUyOnmZCEy/DfimFZPoOIGSQG0\r\nrhiH05M0j9MLtaQctf7m1pCaxCreuBMPwnvZVY4V29Jr0Lpgo5RmWesmWCxL\r\nWr+hsXUK8Upg+YCd3vKMFCQpzR0I3yuairt/1tB20xo0tjwlI/Grudxl3aye\r\nImpKTQC5aA+eFEq5K17SzBxUT9zJ/Hhj94KL5vYicd68pJZGSbiOvRQaa5tl\r\nl+MgaTwKx9nhnMMbUM38cUGDC4xBvdNDsAnsh+kZ/N9H7t+hN57SjCJ/sCjL\r\nrH9Eiu3igz8EaMfJiINONEgR6+aJjGf7QBMQE5CvkYdGn+KDQUizebaJ1uYj\r\n5W5b/bZeWsTy14uCZwAARKIvS9M7wFx87p1XqPdut7aSZ7ySKQhIXgK915M7\r\n53vhYE1hkS38uZ1V8bLl1cejbgCHELyNwW3bAyfFe9l0VZwxxBCQ6OGwQ1pm\r\nw0iaj0QIfzrHvL1ipYPMfIn00GpmT14Upd10d/dJOhHnv8nONJV2xJwDYTU0\r\nGX9FUx0gDK+RX45DyrFYAnxBmCZ5Ak+Z4Lk=\r\n=LpSG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "engines": {"node": ">=14.15.0"}, "gitHead": "a7946f240d74a887b42fdfdf7aae3ae6e0ee4214", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-cli.git", "type": "git"}, "_npmVersion": "lerna/6.6.1/node@v20.0.0+x64 (linux)", "description": "[![NPM Downloads][downloads]][downloads-url]", "directories": {}, "_nodeVersion": "20.0.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"webpack": "5.x.x", "webpack-cli": "5.x.x"}, "peerDependenciesMeta": {"webpack-dev-server": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/serve_2.0.2_1682093213693_0.7722503741903499", "host": "s3://npm-registry-packages"}}, "2.0.3": {"name": "@webpack-cli/serve", "version": "2.0.3", "keywords": [], "license": "MIT", "_id": "@webpack-cli/serve@2.0.3", "maintainers": [{"name": "ev1stensberg", "email": "<EMAIL>"}, {"name": "ematipico", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-cli/tree/master/packages/serve", "bugs": {"url": "https://github.com/webpack/webpack-cli/issues"}, "dist": {"shasum": "c00c48d19340224242842e38b8f7b76c308bbd3f", "tarball": "https://registry.npmjs.org/@webpack-cli/serve/-/serve-2.0.3.tgz", "fileCount": 5, "integrity": "sha512-Bwxd73pHuYc0cyl7vulPp2I6kAYtmJPkfUivbts7by6wDAVyFdKzGX3AksbvCRyNVFUJu7o2ZTcWXdT90T3qbg==", "signatures": [{"sig": "MEQCIHJx9dprEgOZqIF0jOQp/8vSR1lcyYssFySgj1xSb4r2AiB5b6DmqvxPsNSQl2TCAF5UzhxdWyXEE6o/I0Ly4JczlQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11590}, "main": "lib/index.js", "types": "lib/index.d.ts", "engines": {"node": ">=14.15.0"}, "gitHead": "5aeab7cb04cfbf42bf23dbb32e28a1e78c941887", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-cli.git", "type": "git"}, "_npmVersion": "lerna/6.6.1/node@v18.15.0+x64 (linux)", "description": "[![NPM Downloads][downloads]][downloads-url]", "directories": {}, "_nodeVersion": "18.15.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"webpack": "5.x.x", "webpack-cli": "5.x.x"}, "peerDependenciesMeta": {"webpack-dev-server": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/serve_2.0.3_1683421296971_0.26924104704439356", "host": "s3://npm-registry-packages"}}, "2.0.4": {"name": "@webpack-cli/serve", "version": "2.0.4", "keywords": [], "license": "MIT", "_id": "@webpack-cli/serve@2.0.4", "maintainers": [{"name": "ev1stensberg", "email": "<EMAIL>"}, {"name": "ematipico", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-cli/tree/master/packages/serve", "bugs": {"url": "https://github.com/webpack/webpack-cli/issues"}, "dist": {"shasum": "3982ee6f8b42845437fc4d391e93ac5d9da52f0f", "tarball": "https://registry.npmjs.org/@webpack-cli/serve/-/serve-2.0.4.tgz", "fileCount": 5, "integrity": "sha512-0xRgjgDLdz6G7+vvDLlaRpFatJaJ69uTalZLRSMX5B3VUrDmXcrVA3+6fXXQgmYz7bY9AAgs348XQdmtLsK41A==", "signatures": [{"sig": "MEUCIFWaAvYRX4p1PlcvP3/sTqkSvyh2FHBWuhnf4HfsNfieAiEA/2tmxudLNTegMbZBpOgL/9Z5wcDOvEISRJ4Llo7/Rbk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11518}, "main": "lib/index.js", "types": "lib/index.d.ts", "engines": {"node": ">=14.15.0"}, "gitHead": "f9fd17975c9de1a3c129bf2e47c8fdf9bf8c1664", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-cli.git", "type": "git"}, "_npmVersion": "lerna/6.6.2/node@v18.15.0+x64 (linux)", "description": "[![NPM Downloads][downloads]][downloads-url]", "directories": {}, "_nodeVersion": "18.15.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"webpack": "5.x.x", "webpack-cli": "5.x.x"}, "peerDependenciesMeta": {"webpack-dev-server": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/serve_2.0.4_1683613788547_0.28960140637278364", "host": "s3://npm-registry-packages"}}, "2.0.5": {"name": "@webpack-cli/serve", "version": "2.0.5", "keywords": [], "license": "MIT", "_id": "@webpack-cli/serve@2.0.5", "maintainers": [{"name": "ev1stensberg", "email": "<EMAIL>"}, {"name": "ematipico", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-cli/tree/master/packages/serve", "bugs": {"url": "https://github.com/webpack/webpack-cli/issues"}, "dist": {"shasum": "325db42395cd49fe6c14057f9a900e427df8810e", "tarball": "https://registry.npmjs.org/@webpack-cli/serve/-/serve-2.0.5.tgz", "fileCount": 5, "integrity": "sha512-lqaoKnRYBdo1UgDX8uF24AfGMifWK19TxPmM5FHc2vAGxrJ/qtyUyFBWoY1tISZdelsQ5fBcOusifo5o5wSJxQ==", "signatures": [{"sig": "MEUCIQCrwPHlxP4sJLcBIofaqzeCnOliplp6uxFHcd2BbdLatAIgDs6+r2OKJWMmkC9b8eXm2CCqz3ETEZZurx9av/FUjh4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11571}, "main": "lib/index.js", "types": "lib/index.d.ts", "engines": {"node": ">=14.15.0"}, "gitHead": "e879ce4ef91a9a89ca5ef74f533391cef5ba009d", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-cli.git", "type": "git"}, "_npmVersion": "lerna/6.6.2/node@v14.21.3+x64 (linux)", "description": "[![NPM Downloads][downloads]][downloads-url]", "directories": {}, "_nodeVersion": "14.21.3", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"webpack": "5.x.x", "webpack-cli": "5.x.x"}, "peerDependenciesMeta": {"webpack-dev-server": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/serve_2.0.5_1685844835382_0.7886563082320042", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "@webpack-cli/serve", "version": "3.0.0", "keywords": [], "license": "MIT", "_id": "@webpack-cli/serve@3.0.0", "maintainers": [{"name": "ev1stensberg", "email": "<EMAIL>"}, {"name": "ematipico", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-cli/tree/master/packages/serve", "dist": {"shasum": "ce7a5daa6941c77929191a58e630326ff803bb14", "tarball": "https://registry.npmjs.org/@webpack-cli/serve/-/serve-3.0.0.tgz", "fileCount": 5, "integrity": "sha512-oX0XqXHb0IgD2jfzxM5sOGuwFTrLpOpfyPT0t4QIXHS69eRRliyuKzbavXgDnOENIs9BxbNnAaDFhTpAEPEChQ==", "signatures": [{"sig": "MEQCIAwozD4P9vuOUpeqSIHaaIwdwkDT9dc12aM+vGgub58KAiAQzq3bV8pxetnGRncIc2xjme9VoGCoZVteUzCYVYCrTw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11814}, "main": "lib/index.js", "types": "lib/index.d.ts", "engines": {"node": ">=18.12.0"}, "gitHead": "1bbeaf0479e9b911f80b302c60cda2339a80511d", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/webpack/webpack-cli.git", "type": "git"}, "_npmVersion": "lerna/8.1.9/node@v22.11.0+x64 (linux)", "description": "", "directories": {}, "_nodeVersion": "22.11.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"webpack": "5.x.x", "webpack-cli": "5.x.x"}, "peerDependenciesMeta": {"webpack-dev-server": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/serve_3.0.0_1734625971902_0.14269944675889978", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.1": {"name": "@webpack-cli/serve", "version": "3.0.1", "description": "", "main": "lib/index.js", "types": "lib/index.d.ts", "keywords": [], "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/webpack/webpack-cli.git"}, "homepage": "https://github.com/webpack/webpack-cli/tree/master/packages/serve", "license": "MIT", "engines": {"node": ">=18.12.0"}, "peerDependencies": {"webpack": "^5.82.0", "webpack-cli": "6.x.x"}, "peerDependenciesMeta": {"webpack-dev-server": {"optional": true}}, "gitHead": "480b33d23b277b3a55310bfc6dec8bcd3d4ed404", "_nodeVersion": "22.11.0", "_npmVersion": "lerna/8.1.9/node@v22.11.0+x64 (linux)", "_id": "@webpack-cli/serve@3.0.1", "dist": {"integrity": "sha512-sbgw03xQaCLiT6gcY/6u3qBDn01CWw/nbaXl3gTdTFuJJ75Gffv3E3DBpgvY2fkkrdS1fpjaXNOmJlnbtKauKg==", "shasum": "bd8b1f824d57e30faa19eb78e4c0951056f72f00", "tarball": "https://registry.npmjs.org/@webpack-cli/serve/-/serve-3.0.1.tgz", "fileCount": 5, "unpackedSize": 11816, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCKWsg6q+zHK1SdUI8oxbCKhc8SsPdLjv/8gYAMn4qPdAIhAOdUbLfaNZr+1BZSLDRWditKwMNILRx+cn17adc46WBz"}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ev1stensberg", "email": "<EMAIL>"}, {"name": "ematipico", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/serve_3.0.1_1734703926696_0.29524490769688594"}, "_hasShrinkwrap": false}}, "time": {"created": "2018-05-17T14:34:10.347Z", "modified": "2024-12-20T14:12:07.153Z", "0.0.4": "2018-05-17T14:34:11.004Z", "0.0.7": "2018-06-02T01:42:26.219Z", "0.0.8": "2018-06-02T01:43:18.600Z", "0.1.0": "2018-07-18T14:58:17.451Z", "0.1.1": "2018-09-23T09:03:29.746Z", "0.1.2": "2018-09-29T17:14:30.428Z", "0.1.3": "2019-01-03T05:07:50.154Z", "0.1.5": "2019-03-15T19:05:35.759Z", "0.1.6": "2019-06-07T10:54:06.069Z", "0.1.7": "2019-06-07T11:27:20.518Z", "0.1.8": "2019-06-07T11:37:49.039Z", "0.2.0": "2019-11-12T17:23:51.020Z", "1.0.1-alpha.3": "2020-02-23T10:34:02.099Z", "1.0.1-alpha.4": "2020-02-29T10:10:34.857Z", "1.0.1-alpha.5": "2020-03-02T15:48:50.793Z", "1.0.1-rc.0": "2020-09-22T20:59:35.966Z", "1.0.1-rc.1": "2020-10-06T17:42:35.250Z", "1.0.1": "2020-10-10T18:13:36.154Z", "1.1.0": "2020-11-04T18:43:06.159Z", "1.2.0": "2020-12-25T17:42:05.094Z", "1.2.1": "2020-12-31T17:32:07.659Z", "1.2.2": "2021-01-19T11:27:46.905Z", "1.3.0": "2021-02-02T17:03:40.089Z", "1.3.1": "2021-03-27T15:43:59.197Z", "1.4.0": "2021-05-06T13:11:07.295Z", "1.5.0": "2021-06-07T13:07:13.847Z", "1.5.1": "2021-06-07T16:38:40.898Z", "1.5.2": "2021-08-15T20:05:38.064Z", "1.6.0": "2021-10-06T20:47:47.805Z", "1.6.1": "2022-01-24T13:25:57.926Z", "1.7.0": "2022-06-13T00:40:00.592Z", "2.0.0": "2022-11-17T04:33:52.461Z", "2.0.1": "2022-12-05T17:33:35.820Z", "2.0.2": "2023-04-21T16:06:53.864Z", "2.0.3": "2023-05-07T01:01:37.192Z", "2.0.4": "2023-05-09T06:29:48.779Z", "2.0.5": "2023-06-04T02:13:55.554Z", "3.0.0": "2024-12-19T16:32:52.083Z", "3.0.1": "2024-12-20T14:12:06.922Z"}, "license": "MIT", "homepage": "https://github.com/webpack/webpack-cli/tree/master/packages/serve", "keywords": [], "repository": {"type": "git", "url": "https://github.com/webpack/webpack-cli.git"}, "maintainers": [{"name": "ev1stensberg", "email": "<EMAIL>"}, {"name": "ematipico", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}