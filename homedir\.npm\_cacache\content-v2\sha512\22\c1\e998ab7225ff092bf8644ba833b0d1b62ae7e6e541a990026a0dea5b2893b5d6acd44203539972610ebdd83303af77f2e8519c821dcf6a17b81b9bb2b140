{"name": "toidentifier", "dist-tags": {"latest": "1.0.1"}, "versions": {"0.0.1": {"name": "toidentifier", "version": "0.0.1", "devDependencies": {"ava": "^0.22.0", "babel-cli": "^6.26.0", "babel-preset-env": "^1.6.1", "codecov": "^2.3.0", "cross-env": "^5.0.5", "eslint": "^4.5.0", "eslint-config-prettier": "^2.3.0", "eslint-plugin-prettier": "^2.2.0", "husky": "^0.14.3", "lint-staged": "^4.0.4", "nyc": "^11.1.0", "prettier": "^1.6.1", "remark-cli": "^4.0.0", "remark-preset-github": "^0.0.6", "xo": "^0.19.0"}, "dist": {"integrity": "sha512-rbnQkrIivqT1LG6lv4zIbjXG43nmtQvugnEK6E5tA0OlDJcK6e0O6DkxYmaV/zcB4Sd7Hb2uB0892I7CMYJdww==", "shasum": "32fe700072972c0689f67fdb0e1eb92b2d18d413", "tarball": "https://registry.npmjs.org/toidentifier/-/toidentifier-0.0.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC/eOjM0uezUs+kx47nk72iDpnSF+Gl/8r43cZsi86SWgIgO+67KxFYScCzv4QX0+zfbABPjhHfeUNMonZqPRIId/U="}]}, "engines": {"node": ">=0.6"}}, "1.0.0": {"name": "toidentifier", "version": "1.0.0", "devDependencies": {"eslint": "4.19.1", "eslint-config-standard": "11.0.0", "eslint-plugin-import": "2.11.0", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-node": "6.0.1", "eslint-plugin-promise": "3.7.0", "eslint-plugin-standard": "3.1.0", "mocha": "1.21.5", "nyc": "11.8.0"}, "dist": {"integrity": "sha512-yaOH/Pk/VEhBWWTlhI+qXxDFXlejDGcQipMlyxda9nthulaxLZUNcUqFxokp0vcYnvteJln5FNQDRrxj3YcbVw==", "shasum": "7e1be3470f1e77948bc43d94a3c8f4d7752ba553", "tarball": "https://registry.npmjs.org/toidentifier/-/toidentifier-1.0.0.tgz", "fileCount": 4, "unpackedSize": 4327, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbQ4XTCRA9TVsSAnZWagAA7DoP/j60iZmXQNybYW88ghH2\nb0/OM4HaSAJFZaPLs/QWHAG+1njmk4Inxr0YPeqAcU8bkh8UvUBAsf/qnNOV\nVx8R5MQspibif04/f/nB+ZZyoFvv45270S5M+hb22colM3BK0FnfImVZHqI6\n0n+fbicoYYCg3KxEpLC4GdXbJ2R6iSJ+kCnvTFX7/EzDoDjZJjzSMn96HpH+\nakvIo0kEXsTMVmjWwQSo++7JzsfBJs7Z2X+ixOdhf1HHYU5yiDS/8blXY5hN\nG5tcXbsBsFMPSRwKzArB8SqTejls6uRY21DmzvXnNCfS5k5FyftGLtBTpmwk\n2mHLENZ+79t+HP4tXmF/2scY/XjIWCtxjmOPBHg9eMoCe7uiEf/MkNoPQ29a\nKYMYz3gePkHG6NH+IN69e0KYmdzyowxtQy0Oel3L71nnguFD0DzJuhODahvl\nWdrzkyKWYHkrByIQmsYhLei67I+fgiAEURFhXHwRQ6TfW+i7I+0vx6qVZ7Uz\n8kCo7SELv6Suo62QXxB3O9u0qZyISXWfxbV48+T5KDayWmBVNEo9yktzPMF6\nIv49uczFdrGI5rAI2/zW5Ss7I00OnbY1I4va36JkEcoxqriiYGhLK+a7/061\nRKoB8fSKWCaiXEYcNkjiEIhI+5n57xOA4PuEUnfpqizo8yvxVLvOLUmNCtm3\nBVOV\r\n=Yk0D\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGDZnL/1DtfLLYqNeYrAohDMZTA5ggzdE3GmbaMpzwJ6AiEA3nhWweuNqSyI+CwGfBB1QKUVTG53UPqlOlVCiQfUDaQ="}]}, "engines": {"node": ">=0.6"}}, "1.0.1": {"name": "toidentifier", "version": "1.0.1", "devDependencies": {"eslint": "7.32.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.25.3", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "4.3.1", "eslint-plugin-standard": "4.1.0", "mocha": "9.1.3", "nyc": "15.1.0"}, "dist": {"integrity": "sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA==", "shasum": "3be34321a88a820ed1bd80dfaa33e479fbb8dd35", "tarball": "https://registry.npmjs.org/toidentifier/-/toidentifier-1.0.1.tgz", "fileCount": 5, "unpackedSize": 4685, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2+EKCRA9TVsSAnZWagAABkgP/07O+fXm6BSS5cLKq9x8\nth8+fBLY9B1JdIPUcjmGmJEYOx9hNUSs18+UTFtMDxR4lYcL9aS0NJ/5oDRF\n0k4g05VoZftdjbknI3KksHIO2ZWVBvia1hr050Aj0OZt73EThaq2jwauyv/o\nW43gKnzZDzlPqp5nW+K1ZyyZAO6x18VgPfCHaVfIdQVpXlewkMtGdMwgcIyH\nf32U7FezcqErukxl+I5JloPk9zMfnPBm1aa8Bx/YF6Y08ctsHihlUcGyYQBd\ny29fDkqwd+NCJueWxBCN6uE1IENeZSmCCB0AZqlruNZiV/hZQA4riqy/G5hH\nMpjgodOjZTknH6vdq1j6/DYaATlWz6AzelKOTRXRHpsrFbZ/2W3LaHm5KuCD\nT2hPpRLPu2mCf1JBOdmi4KDC5KaqSRMp3g+9xbwdIGxuIczK1XZzfwNUfRPE\nQpxtaLUw0PpJxWkSWE2rkMhfJN4U8XMIT5Ramn9FgAPjw9XaBF67t8FQNk8m\nuPiv8315TAfFh90Fy1fmd3aXx7fuCGx2XsT2u78uZCBlsQ0WEzyGqczhcqF0\nfQ0Oc2hOeVDQgvo06uUklk/4GQe9czuDfA9aFZEnItgQU72iFA10gmyP7TZj\ncwW7N0bQw1Ko+8y3ISRQSOcSXBbYNu16inAoco4s2V0D5JvV13+kIFAhdvo+\nH2aa\r\n=RxN1\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE4DF65GEHAFzBdSMxDw1aIpaLFVT9vueS5Mc1ScnWR7AiA+cRv4ND36Q8EITeR79WydYYcihBtwhEJ2MsP+8ySyYg=="}]}, "engines": {"node": ">=0.6"}}}, "modified": "2022-05-22T00:38:34.815Z"}