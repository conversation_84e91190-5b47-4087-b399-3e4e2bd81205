{"_id": "lodash.isstring", "_rev": "37-75ff67500c557f51b18119c287f4feb7", "name": "lodash.isstring", "description": "The lodash method `_.isString` exported as a module.", "dist-tags": {"latest": "4.0.1"}, "versions": {"2.0.0": {"name": "lodash.isstring", "version": "2.0.0", "description": "The Lo-Dash function `_.isString` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["customize", "functional", "lodash", "performance", "speed", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "_id": "lodash.isstring@2.0.0", "dist": {"shasum": "652105b4e99cde5642fd8aced2b622ae05898419", "tarball": "https://registry.npmjs.org/lodash.isstring/-/lodash.isstring-2.0.0.tgz", "integrity": "sha512-wQ9cuxmJuYw6PdBbcxtyzSJXWxSU0JCujpx9k1R2yDo9p+jHKCm0VMAa4iwGa47f2VvUzI3e/A/L+nQxPH7O8Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCO60YKYgDKdW2tWK+QD8X1TxsQSNxmcXt1R7EGd1u5/gIgbBZGwvl5VymwmMqUsUluUyQBQFPdJd7k/vOWYyfSHno="}]}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}}, "2.1.0": {"name": "lodash.isstring", "version": "2.1.0", "description": "The Lo-Dash function `_.isString` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "performance", "speed", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "_id": "lodash.isstring@2.1.0", "dist": {"shasum": "fe07b0fd817cfad55c3f0f283c19c963669af4fe", "tarball": "https://registry.npmjs.org/lodash.isstring/-/lodash.isstring-2.1.0.tgz", "integrity": "sha512-tnRCYFFDrBGDJ52/tRhjZO+x+Fs4+WjkKTpw+fu4ddXmB3PkrtB6TFJUhijETnvtewk3AmH+IYTRADZuE2SEPw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFBOU7+FxhfwNea201OyDWLFbsT8yLMp3gwhMxS28esMAiAQ5AIfOTa1GZBbfjA2ans22NXGhzi9hVzsnwLprHpasw=="}]}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}]}, "2.2.0": {"name": "lodash.isstring", "version": "2.2.0", "description": "The Lo-Dash function `_.isString` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "performance", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "_id": "lodash.isstring@2.2.0", "dist": {"shasum": "d2358715bf7a704117b781cfd59e3c55a76d7bb0", "tarball": "https://registry.npmjs.org/lodash.isstring/-/lodash.isstring-2.2.0.tgz", "integrity": "sha512-cAFU09Nb87NsfQbWS4SfqyZXDzdtPvTu4h1X8WypmxRZSjZluUKs/igUOIMGEESdi57ZziJLNs6K3PT829/mhA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDMryQTomTsKbUjJ6gTjL1E3PR/p6fzsNIDntNAoQKQpgIhAMax7EpVnETCke2oO66++3ivQqAgZJsG+DnFuPRV5P7y"}]}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}]}, "2.2.1": {"name": "lodash.isstring", "version": "2.2.1", "description": "The Lo-Dash function `_.isString` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "_id": "lodash.isstring@2.2.1", "dist": {"shasum": "da8ad3fb99fa0ae8139f53cd75a26e7beedf9461", "tarball": "https://registry.npmjs.org/lodash.isstring/-/lodash.isstring-2.2.1.tgz", "integrity": "sha512-8hXoxRt6WIFGt/4/YOYz7raQtf/1GTs7ubX1Lm8+eZdMmrDTUj54WIQRKE95uLMSGShC90E4mM0U0qSqYeEmmA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD2Qn7ehj1Ns34RGd4FYw1qWHCBOMleZGBkvdFC9cAEYAIgbL2upFTbs//gQyKjM+4t25risRntlMAEk6OSxBJAsmE="}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}]}, "2.3.0": {"name": "lodash.isstring", "version": "2.3.0", "description": "The Lo-Dash function `_.isString` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "_id": "lodash.isstring@2.3.0", "dist": {"shasum": "c0007a465efd8bd6b30a1adb51d050d43aba3963", "tarball": "https://registry.npmjs.org/lodash.isstring/-/lodash.isstring-2.3.0.tgz", "integrity": "sha512-gykVX+taxLW1wo/YOXMgpCSPourI8u0q0ZLGaK2blyGufs6gLkRCv4SizWoZSpfzL12Tz8iI5QbfDFNGm0YCEA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDNpUa6GBI1teKlHBBfom+Dhee63ANEOrYjuIM6NfHv9gIhAJCKdOvyr7dBFwixt41BBwp+2e6uys0IsoK3zyDETCY8"}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}]}, "2.4.0": {"name": "lodash.isstring", "version": "2.4.0", "description": "The Lo-Dash function `_.isString` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "_id": "lodash.isstring@2.4.0", "dist": {"shasum": "6c3a746c93bf949c7b61e6c5fb5f3f99502832b8", "tarball": "https://registry.npmjs.org/lodash.isstring/-/lodash.isstring-2.4.0.tgz", "integrity": "sha512-uDmRJntVKuCI3Lac8B1JRuSxlzaAIim4HGMVCvRVkWWvoQJZ0tzy8xuwIkS8t0XlKBTnTixXe1AF1DkG7aC52w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG/UGOG0ZUN12eBFQMHpQZ1ACQlTADRpNe2RU3cvJKA9AiBJ7A+rNmwseGhZeUUqDeEFan9WIZWe2szwwoQLwYOdrg=="}]}, "_from": ".", "_npmVersion": "1.3.14", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}]}, "2.4.1": {"name": "lodash.isstring", "version": "2.4.1", "description": "The Lo-Dash function `_.isString` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "_id": "lodash.isstring@2.4.1", "dist": {"shasum": "3a4a42ee344b5bc09aeaa87ef1dc3097789d0792", "tarball": "https://registry.npmjs.org/lodash.isstring/-/lodash.isstring-2.4.1.tgz", "integrity": "sha512-HKYXjltWqkqFqI77A0OwI1p2Ed8qKVGDvNxDQE36uCn3QDQd5zMNHBaRyCzJz/xC7vchWr3OaCoANJAluDV0VQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC66H9nRGJTP5KzdZVwRHvXdxNb31jOG/CvHAVelr23wwIhAMiVAHDrIYoWAe4AmWhdszpktcbKpvXvLxwrV4ySqQsH"}]}, "_from": ".", "_npmVersion": "1.3.14", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}]}, "3.0.0": {"name": "lodash.isstring", "version": "3.0.0", "description": "The modern build of lodash’s `_.isString` as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "https://github.com/lodash/lodash"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isstring@3.0.0", "_shasum": "c8ebb05b1106973b8ceff29481ec0c2387b52522", "_from": ".", "_npmVersion": "2.3.0", "_nodeVersion": "0.10.35", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "dist": {"shasum": "c8ebb05b1106973b8ceff29481ec0c2387b52522", "tarball": "https://registry.npmjs.org/lodash.isstring/-/lodash.isstring-3.0.0.tgz", "integrity": "sha512-5hPgquHsLlAqJt4x8flsnPFSLFFvZhcA7R91m1kRTIx/jH67E9q3wMbZ4zPB+Bx09GhHzWja7UMM7w7NOmmR9g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDEg/N6753ui93k4bo55Eh+zGE0XHNdVpdzwnS8uU0wDgIgGOTNCzgIj5onjVL2aXis2NpKOyUo/4lOqt37oPNXWMM="}]}}, "3.0.1": {"name": "lodash.isstring", "version": "3.0.1", "description": "The modern build of lodash’s `_.isString` as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "https://github.com/lodash/lodash"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isstring@3.0.1", "_shasum": "41638944ea042ef67ad67c293aa541d3f3d6e53c", "_from": ".", "_npmVersion": "2.7.3", "_nodeVersion": "0.12.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "dist": {"shasum": "41638944ea042ef67ad67c293aa541d3f3d6e53c", "tarball": "https://registry.npmjs.org/lodash.isstring/-/lodash.isstring-3.0.1.tgz", "integrity": "sha512-UiX/KDS9ryxRpGR1q6zSwV9LxsG6PbN3OCD73O48JiRfG1z0cG3LCl3X9AEEdluRNuxduz1u94rS6BW9vSl9Vw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFR6w9zFt1yceCVnbw4hBXxvyLZk/Y/9nKDDPmMGCf3YAiAip+MNY1EQj+nb6ER11NTq+CLUV9Te9AtTmxftmyTalQ=="}]}}, "4.0.0": {"name": "lodash.isstring", "version": "4.0.0", "description": "The lodash method `_.isString` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util", "is<PERSON>ring"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isstring@4.0.0", "_shasum": "d3eada1270d17579d423c0d3dfbec85907569cfc", "_from": ".", "_npmVersion": "2.14.15", "_nodeVersion": "5.4.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "d3eada1270d17579d423c0d3dfbec85907569cfc", "tarball": "https://registry.npmjs.org/lodash.isstring/-/lodash.isstring-4.0.0.tgz", "integrity": "sha512-ClsvzyAOZnZBSwgooYIuDAdh0Zig+DI88pRluS+BISaxakylVtfrev7CvdtFkksxgWwpx6ikWZ/DEcvKBtto/g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCw6Ei0P6dSluywfwnnjcWUJVJZ1/tuBgk3JdAd4a1L5AIhAJ/5DTrKOZybo4yJC4RhQQUDXAbki2zxTpFWYfDQUEfh"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}]}, "4.0.1": {"name": "lodash.isstring", "version": "4.0.1", "description": "The lodash method `_.isString` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "is<PERSON>ring"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isstring@4.0.1", "_shasum": "d527dfb5456eca7cc9bb95d5daeaf88ba54a5451", "_from": ".", "_npmVersion": "2.14.15", "_nodeVersion": "5.4.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "d527dfb5456eca7cc9bb95d5daeaf88ba54a5451", "tarball": "https://registry.npmjs.org/lodash.isstring/-/lodash.isstring-4.0.1.tgz", "integrity": "sha512-0wJxfxH1wgO3GrbuP+dTTk7op+6L41QCXbGINEmD+ny/G/eCqGzxyCsh7159S+mgDDcoarnBw6PC1PS5+wUGgw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC4KUOnn96ciYbxtS3ew/yU0m+c0XDB+Zwkn4kVy7BGwQIgHhwCtl8t5C9Ajl/JXOO2XjfrliSxDVGQeIY51jJ75vg="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-5-east.internal.npmjs.com", "tmp": "tmp/lodash.isstring-4.0.1.tgz_1454484537621_0.8814679116476327"}}}, "readme": "# lodash.isstring v4.0.1\n\nThe [lodash](https://lodash.com/) method `_.isString` exported as a [Node.js](https://nodejs.org/) module.\n\n## Installation\n\nUsing npm:\n```bash\n$ {sudo -H} npm i -g npm\n$ npm i --save lodash.isstring\n```\n\nIn Node.js:\n```js\nvar isString = require('lodash.isstring');\n```\n\nSee the [documentation](https://lodash.com/docs#isString) or [package source](https://github.com/lodash/lodash/blob/4.0.1-npm-packages/lodash.isstring) for more details.\n", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "time": {"modified": "2022-06-19T13:36:14.947Z", "created": "2013-09-23T06:34:48.737Z", "2.0.0": "2013-09-23T07:38:23.911Z", "2.1.0": "2013-09-23T07:56:46.906Z", "2.2.0": "2013-09-29T22:09:59.038Z", "2.2.1": "2013-10-03T18:50:34.673Z", "2.3.0": "2013-11-11T16:47:56.139Z", "2.4.0": "2013-11-26T19:55:59.447Z", "2.4.1": "2013-12-03T17:14:51.456Z", "3.0.0": "2015-01-26T15:29:27.553Z", "3.0.1": "2015-03-25T23:36:10.847Z", "4.0.0": "2016-01-13T11:06:24.069Z", "4.0.1": "2016-02-03T07:28:59.695Z"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "homepage": "https://lodash.com/", "keywords": ["lodash-modularized", "is<PERSON>ring"], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "license": "MIT", "readmeFilename": "README.md"}