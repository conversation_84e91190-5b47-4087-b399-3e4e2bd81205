{"_id": "is-property", "_rev": "12-3535dea4a9298a2704e00d76b66c3979", "name": "is-property", "description": "Tests if a JSON property can be accessed using . syntax", "dist-tags": {"latest": "1.0.2"}, "versions": {"0.0.0": {"name": "is-property", "version": "0.0.0", "description": "Tests if a JSON property can be accessed using . syntax", "main": "is-property.js", "directories": {"test": "test"}, "dependencies": {}, "devDependencies": {"tape": "~1.0.4"}, "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "git://github.com/mi<PERSON><PERSON><PERSON><PERSON>/is-property.git"}, "keywords": ["is", "property", "json", "dot", "bracket", ".", "[]"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "gitHead": "0a85ea5b6b1264ea1cdecc6e5cf186adbb3ffc50", "bugs": {"url": "https://github.com/mi<PERSON><PERSON><PERSON><PERSON>/is-property/issues"}, "_id": "is-property@0.0.0", "dist": {"shasum": "433eda4f3d8af5918b2d25b3760d30544fa3f5fc", "tarball": "https://registry.npmjs.org/is-property/-/is-property-0.0.0.tgz", "integrity": "sha512-bLB7Hm24xzG0pBeehAIZ0WfhmLpFhwAYrBsLahO8VfgApkt2vSmO2e0hBAXlL6yuAoZHXKjPagm9cjxgd09sfg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDjOVUwK0sV4q/DCOyTbdFUR2aZqf2rubGWrWLybEmtnwIhAJbeTNFPsOSchtcZdEhmP+LUlYzVRYaYW4RM3NtBkdL2"}]}, "_from": ".", "_npmVersion": "1.2.24", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}]}, "1.0.0": {"name": "is-property", "version": "1.0.0", "description": "Tests if a JSON property can be accessed using . syntax", "main": "is-property.js", "directories": {"test": "test"}, "dependencies": {}, "devDependencies": {"tape": "~1.0.4"}, "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "git://github.com/mi<PERSON><PERSON><PERSON><PERSON>/is-property.git"}, "keywords": ["is", "property", "json", "dot", "bracket", ".", "[]"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "gitHead": "0a85ea5b6b1264ea1cdecc6e5cf186adbb3ffc50", "bugs": {"url": "https://github.com/mi<PERSON><PERSON><PERSON><PERSON>/is-property/issues"}, "homepage": "https://github.com/mi<PERSON><PERSON><PERSON><PERSON>/is-property", "_id": "is-property@1.0.0", "dist": {"shasum": "a638fd20fe4ff37a73c78fbb306f8639e18d3d46", "tarball": "https://registry.npmjs.org/is-property/-/is-property-1.0.0.tgz", "integrity": "sha512-3IW5/0B0ltNAriS+0T7vC5lMITvn6GoaoRPmNIYTKO1IaIKsn8Fj37Vu5eqfzRjxoDs46IGNUDlmKGpB1HcK/w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID+FbjWjafiV3GAE+Z1cqQ0b90Z4FLOo1G7JAOrDd4uZAiA2g30OgaNXm00soLX6h6YYMEp5IWJbO+uAwz+9qWiQ1g=="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}]}, "1.0.1": {"name": "is-property", "version": "1.0.1", "description": "Tests if a JSON property can be accessed using . syntax", "main": "is-property.js", "directories": {"test": "test"}, "dependencies": {}, "devDependencies": {"tape": "~1.0.4"}, "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "git://github.com/mi<PERSON><PERSON><PERSON><PERSON>/is-property.git"}, "keywords": ["is", "property", "json", "dot", "bracket", ".", "[]"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "gitHead": "0a85ea5b6b1264ea1cdecc6e5cf186adbb3ffc50", "bugs": {"url": "https://github.com/mi<PERSON><PERSON><PERSON><PERSON>/is-property/issues"}, "homepage": "https://github.com/mi<PERSON><PERSON><PERSON><PERSON>/is-property", "_id": "is-property@1.0.1", "_shasum": "1805f426a444a65aead0ef0c21bc8713690fa0ec", "_from": ".", "_npmVersion": "2.1.4", "_nodeVersion": "0.10.26", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "1805f426a444a65aead0ef0c21bc8713690fa0ec", "tarball": "https://registry.npmjs.org/is-property/-/is-property-1.0.1.tgz", "integrity": "sha512-rR7yTZ+303q3LBEkgPXXwbm3lhAObvhlz3MzqmMC8LEi5mNmZFqMO7R5IY3TsCWGas/e7U+0CMXbxWe2jo+jig==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDQggrxx9U1VNg8yu3DQ++9gK4iZvYjc1IVxgpEiqC+VAIhANEAtiSbCY5ZNVKqM1H3iO4HGgxy8fCI1/Ye02Wymqd+"}]}}, "1.0.2": {"name": "is-property", "version": "1.0.2", "description": "Tests if a JSON property can be accessed using . syntax", "main": "is-property.js", "directories": {"test": "test"}, "dependencies": {}, "devDependencies": {"tape": "~1.0.4"}, "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "git://github.com/mi<PERSON><PERSON><PERSON><PERSON>/is-property.git"}, "keywords": ["is", "property", "json", "dot", "bracket", ".", "[]"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "gitHead": "0a85ea5b6b1264ea1cdecc6e5cf186adbb3ffc50", "bugs": {"url": "https://github.com/mi<PERSON><PERSON><PERSON><PERSON>/is-property/issues"}, "homepage": "https://github.com/mi<PERSON><PERSON><PERSON><PERSON>/is-property", "_id": "is-property@1.0.2", "_shasum": "57fe1c4e48474edd65b09911f26b1cd4095dda84", "_from": ".", "_npmVersion": "2.1.4", "_nodeVersion": "0.10.26", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "57fe1c4e48474edd65b09911f26b1cd4095dda84", "tarball": "https://registry.npmjs.org/is-property/-/is-property-1.0.2.tgz", "integrity": "sha512-Ks/IoX00TtClbGQr4TWXemAnktAQvYB7HzcCxDGqEZU6oCmb2INHuOoKxbtR+HFkmYWBKv/dOZtGRiAjDhj92g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCjTC6tT1G4fjbfTMIW6jZ8Xaijp8BrtlQIzyYDiCwgMAIhAMNj2p8iwFzFgh2iTvMLJum7UHmxkwbqowpm8LLDvZRf"}]}}}, "readme": "is-property\n===========\nTests if a property of a JavaScript object can be accessed using the dot (.) notation or if it must be enclosed in brackets, (ie use x[\" ... \"])\n\nExample\n-------\n\n```javascript\nvar isProperty = require(\"is-property\")\n\nconsole.log(isProperty(\"foo\"))  //Prints true\nconsole.log(isProperty(\"0\"))    //Prints false\n```\n\nInstall\n-------\n\n    npm install is-property\n    \n### `require(\"is-property\")(str)`\nChecks if str is a property\n\n* `str` is a string which we will test if it is a property or not\n\n**Returns** true or false depending if str is a property\n\n## Credits\n(c) 2013 <PERSON><PERSON><PERSON>. MIT License", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}], "time": {"modified": "2022-06-19T02:48:52.177Z", "created": "2013-07-18T14:13:28.789Z", "0.0.0": "2013-07-18T14:13:30.325Z", "1.0.0": "2014-07-30T14:50:33.406Z", "1.0.1": "2014-12-25T14:48:06.931Z", "1.0.2": "2014-12-25T14:50:43.208Z"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "repository": {"type": "git", "url": "git://github.com/mi<PERSON><PERSON><PERSON><PERSON>/is-property.git"}, "homepage": "https://github.com/mi<PERSON><PERSON><PERSON><PERSON>/is-property", "keywords": ["is", "property", "json", "dot", "bracket", ".", "[]"], "bugs": {"url": "https://github.com/mi<PERSON><PERSON><PERSON><PERSON>/is-property/issues"}, "license": "MIT", "readmeFilename": "README.md", "users": {"mojaray2k": true}}