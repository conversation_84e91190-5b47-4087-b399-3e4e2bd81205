{"_id": "has-tostringtag", "_rev": "3-e000ca38bc06031d385168717b376307", "name": "has-tostringtag", "dist-tags": {"latest": "1.0.2"}, "versions": {"1.0.0": {"name": "has-tostringtag", "version": "1.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}], "description": "Determine if the JS environment has `Symbol.toStringTag` support. Supports spec, or shams.", "license": "MIT", "main": "index.js", "exports": {".": "./index.js", "./shams": "./shams.js", "./package.json": "./package.json"}, "scripts": {"prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "pretest": "npm run --silent lint", "test": "npm run tests-only", "posttest": "aud --production", "tests-only": "npm run test:stock && npm run test:staging && npm run test:shams", "test:stock": "nyc node test", "test:staging": "nyc node --harmony --es-staging test", "test:shams": "npm run --silent test:shams:getownpropertysymbols && npm run --silent test:shams:corejs", "test:shams:corejs": "nyc node test/shams/core-js.js", "test:shams:getownpropertysymbols": "nyc node test/shams/get-own-property-symbols.js", "lint": "eslint --ext=js,mjs .", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/has-tostringtag.git"}, "bugs": {"url": "https://github.com/inspect-js/has-tostringtag/issues"}, "homepage": "https://github.com/inspect-js/has-tostringtag#readme", "keywords": ["javascript", "ecmascript", "symbol", "symbols", "tostringtag", "Symbol.toStringTag"], "dependencies": {"has-symbols": "^1.0.2"}, "devDependencies": {"@ljharb/eslint-config": "^17.6.0", "aud": "^1.1.5", "auto-changelog": "^2.3.0", "core-js": "^2.6.12", "eslint": "^7.32.0", "get-own-property-symbols": "^0.9.5", "nyc": "^10.3.2", "safe-publish-latest": "^1.1.4", "tape": "^5.3.0"}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "gitHead": "011dfc074077ece0eaa7aa740bb4d50623dec774", "_id": "has-tostringtag@1.0.0", "_nodeVersion": "16.6.0", "_npmVersion": "7.19.1", "dist": {"integrity": "sha512-kFjcSNhnlGV1kyoGk7OXKSawH5JOb/LzUc5w9B02hOTO0dfFRjbHQKvg1d6cf3HbeUmtU9VbbV3qzZ2Teh97WQ==", "shasum": "7e133818a7d394734f941e73c3d3f9291e658b25", "tarball": "https://registry.npmjs.org/has-tostringtag/-/has-tostringtag-1.0.0.tgz", "fileCount": 12, "unpackedSize": 10887, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhC/zdCRA9TVsSAnZWagAAh0YP/2Jft8cAPNb3ledjiN+i\nk5EoUUy9qk+4tP0EDAEIV0vTEMze3J4i5nAI99YsXuGgwWjpLUWdvI9dxZwD\n4ycWRkA/b2YZk+uEVfSF8fqTO8E8WsCxSGTdO/AFj6MFaey65MtI2Qv1Agad\n9JtFpd2nQAyWQbNM16u5l4YQ87Xoov0m8vZilyprUBj9c1dgXej8816pWfxj\nJSdQCoiYgrCw5iIAQBu1NZXU8DDstypf6IsZFBraVKWnw9omBhGFjG+596ok\n/hfDlFByNhzOHyqm2bKwggO1jWKLxqapRXIqUoyYvSPe7uabhbGL7o9vynR9\n8dKejfkUxFTSdVoR+uCCxW/JTcP6IQpcP7P6jvIwE+SqUXpp16YBOXpaQT71\nyne2+aQMhJNjMgaBhDXRbKnQH9IBKL9a/JUja+ATWAAsWVGaOFitCclR8JQA\nIPY0ylCl+nFgzPZFWCWlcNP8AhmebnZaQiU0JJl2UcACH1H01HDvlTcrf85d\nUGBb6tHNACSRcmA9kzBLzPC3+xIiQDAKan72TtJCpcHGx3neWHfIlV9RtXaY\nw4iN3NUSvFnWo0Sk+bdgV2Lm3SfqxiCe+eIrnU8h1RGkRi0WDu4KwvB84p2J\nv6DLPmj7+IVth2LnvFFjssedV/dNOPpusA2KqsJ1XCaQImQFIKE9Ns2txH1C\n5cwN\r\n=76y9\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF1IQW6z6akiZGUZ8D4cgtUGGJYWgczJ7wTQtLJy+tnAAiBtLLAbSDafCePYq8rrp0DQ3YxULeZRewPv+y9AX3hgsw=="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/has-tostringtag_1.0.0_1628175581281_0.9026137242597037"}, "_hasShrinkwrap": false}, "1.0.1": {"name": "has-tostringtag", "version": "1.0.1", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}], "description": "Determine if the JS environment has `Symbol.toStringTag` support. Supports spec, or shams.", "license": "MIT", "main": "index.js", "types": "./index.d.ts", "exports": {".": [{"types": "./index.d.ts", "default": "./index.js"}, "./index.js"], "./shams": [{"types": "./shams.d.ts", "default": "./shams.js"}, "./shams.js"], "./package.json": "./package.json"}, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "pretest": "npm run --silent lint", "test": "npm run tests-only", "posttest": "aud --production", "tests-only": "npm run test:stock && npm run test:shams", "test:stock": "nyc node test", "test:staging": "nyc node --harmony --es-staging test", "test:shams": "npm run --silent test:shams:getownpropertysymbols && npm run --silent test:shams:corejs", "test:shams:corejs": "nyc node test/shams/core-js.js", "test:shams:getownpropertysymbols": "nyc node test/shams/get-own-property-symbols.js", "lint": "eslint --ext=js,mjs .", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/has-tostringtag.git"}, "bugs": {"url": "https://github.com/inspect-js/has-tostringtag/issues"}, "homepage": "https://github.com/inspect-js/has-tostringtag#readme", "keywords": ["javascript", "ecmascript", "symbol", "symbols", "tostringtag", "Symbol.toStringTag"], "devDependencies": {"@ljharb/eslint-config": "^21.1.0", "@types/has-symbols": "^1.0.2", "@types/tape": "^5.6.4", "aud": "^2.0.4", "auto-changelog": "^2.4.0", "core-js": "^2.6.12", "eslint": "=8.8.0", "get-own-property-symbols": "^0.9.5", "has-symbols": "^1.0.3", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.7.4", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}, "_id": "has-tostringtag@1.0.1", "gitHead": "ba6941b9021b04f26bd4ba27ad25881b4660c9de", "_nodeVersion": "21.6.0", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-6J4rC9ROz0UkOpjn0BRtSSqlewDTDYJNQvy8N8RSrPCduUWId1o9BQPEVII/KKBqRk/ZIQff1YbRkUDCH2N5Sg==", "shasum": "5d242715a441a1c9a46d543e6dbead8defdfc226", "tarball": "https://registry.npmjs.org/has-tostringtag/-/has-tostringtag-1.0.1.tgz", "fileCount": 16, "unpackedSize": 17364, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEQD0z8/EDu3xUfK0Hg35JMg0nJ8hzkFMhOMYyfldsWLAiEA9g35sZ0/MtEWQ1ZWxmtjKFKfntdzxeCrBPsmOh2+rlA="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/has-tostringtag_1.0.1_1706806405748_0.*****************"}, "_hasShrinkwrap": false}, "1.0.2": {"name": "has-tostringtag", "version": "1.0.2", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}], "description": "Determine if the JS environment has `Symbol.toStringTag` support. Supports spec, or shams.", "license": "MIT", "main": "index.js", "types": "./index.d.ts", "exports": {".": [{"types": "./index.d.ts", "default": "./index.js"}, "./index.js"], "./shams": [{"types": "./shams.d.ts", "default": "./shams.js"}, "./shams.js"], "./package.json": "./package.json"}, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "pretest": "npm run --silent lint", "test": "npm run tests-only", "posttest": "aud --production", "tests-only": "npm run test:stock && npm run test:shams", "test:stock": "nyc node test", "test:staging": "nyc node --harmony --es-staging test", "test:shams": "npm run --silent test:shams:getownpropertysymbols && npm run --silent test:shams:corejs", "test:shams:corejs": "nyc node test/shams/core-js.js", "test:shams:getownpropertysymbols": "nyc node test/shams/get-own-property-symbols.js", "lint": "eslint --ext=js,mjs .", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/has-tostringtag.git"}, "bugs": {"url": "https://github.com/inspect-js/has-tostringtag/issues"}, "homepage": "https://github.com/inspect-js/has-tostringtag#readme", "keywords": ["javascript", "ecmascript", "symbol", "symbols", "tostringtag", "Symbol.toStringTag"], "devDependencies": {"@ljharb/eslint-config": "^21.1.0", "@types/has-symbols": "^1.0.2", "@types/tape": "^5.6.4", "aud": "^2.0.4", "auto-changelog": "^2.4.0", "core-js": "^2.6.12", "eslint": "=8.8.0", "get-own-property-symbols": "^0.9.5", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.7.4", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}, "dependencies": {"has-symbols": "^1.0.3"}, "_id": "has-tostringtag@1.0.2", "gitHead": "690da6a3afbddcf018aa162c42869dcf4f8375f1", "_nodeVersion": "21.6.0", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==", "shasum": "2cdc42d40bef2e5b4eeab7c01a73c54ce7ab5abc", "tarball": "https://registry.npmjs.org/has-tostringtag/-/has-tostringtag-1.0.2.tgz", "fileCount": 16, "unpackedSize": 17603, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDwaEewk4FAepBQKCDj0seyR7ZKezvK5CJ0YvtztxxqFAiEAkN4iJZyh2p8ZPX79StbnsrdpN2J/uwZPWwysiZ1rbGw="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/has-tostringtag_1.0.2_1706823839933_0.6747160079598669"}, "_hasShrinkwrap": false}}, "time": {"created": "2021-08-05T14:59:41.280Z", "1.0.0": "2021-08-05T14:59:41.457Z", "modified": "2024-02-01T21:44:00.323Z", "1.0.1": "2024-02-01T16:53:25.940Z", "1.0.2": "2024-02-01T21:44:00.130Z"}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "description": "Determine if the JS environment has `Symbol.toStringTag` support. Supports spec, or shams.", "homepage": "https://github.com/inspect-js/has-tostringtag#readme", "keywords": ["javascript", "ecmascript", "symbol", "symbols", "tostringtag", "Symbol.toStringTag"], "repository": {"type": "git", "url": "git+https://github.com/inspect-js/has-tostringtag.git"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}, "bugs": {"url": "https://github.com/inspect-js/has-tostringtag/issues"}, "license": "MIT", "readme": "# has-tostringtag <sup>[![Version Badge][2]][1]</sup>\n\n[![github actions][actions-image]][actions-url]\n[![coverage][codecov-image]][codecov-url]\n[![dependency status][5]][6]\n[![dev dependency status][7]][8]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][11]][1]\n\nDetermine if the JS environment has `Symbol.toStringTag` support. Supports spec, or shams.\n\n## Example\n\n```js\nvar hasSymbolToStringTag = require('has-tostringtag');\n\nhasSymbolToStringTag() === true; // if the environment has native Symbol.toStringTag support. Not polyfillable, not forgeable.\n\nvar hasSymbolToStringTagKinda = require('has-tostringtag/shams');\nhasSymbolToStringTagKinda() === true; // if the environment has a Symbol.toStringTag sham that mostly follows the spec.\n```\n\n## Supported Symbol shams\n - get-own-property-symbols [npm](https://www.npmjs.com/package/get-own-property-symbols) | [github](https://github.com/WebReflection/get-own-property-symbols)\n - core-js [npm](https://www.npmjs.com/package/core-js) | [github](https://github.com/zloirock/core-js)\n\n## Tests\nSimply clone the repo, `npm install`, and run `npm test`\n\n[1]: https://npmjs.org/package/has-tostringtag\n[2]: https://versionbadg.es/inspect-js/has-tostringtag.svg\n[5]: https://david-dm.org/inspect-js/has-tostringtag.svg\n[6]: https://david-dm.org/inspect-js/has-tostringtag\n[7]: https://david-dm.org/inspect-js/has-tostringtag/dev-status.svg\n[8]: https://david-dm.org/inspect-js/has-tostringtag#info=devDependencies\n[11]: https://nodei.co/npm/has-tostringtag.png?downloads=true&stars=true\n[license-image]: https://img.shields.io/npm/l/has-tostringtag.svg\n[license-url]: LICENSE\n[downloads-image]: https://img.shields.io/npm/dm/has-tostringtag.svg\n[downloads-url]: https://npm-stat.com/charts.html?package=has-tostringtag\n[codecov-image]: https://codecov.io/gh/inspect-js/has-tostringtag/branch/main/graphs/badge.svg\n[codecov-url]: https://app.codecov.io/gh/inspect-js/has-tostringtag/\n[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/inspect-js/has-tostringtag\n[actions-url]: https://github.com/inspect-js/has-tostringtag/actions\n", "readmeFilename": "README.md"}