{"_id": "normalize-path", "_rev": "31-be06cc659503294cf909e3251f7ba3ad", "name": "normalize-path", "description": "Normalize slashes in a file path to be posix/unix-like forward slashes. Also condenses repeat slashes to a single slash and removes and trailing slashes, unless disabled.", "dist-tags": {"latest": "3.0.0"}, "versions": {"0.1.0": {"name": "normalize-path", "description": "Normalize file path slashes to be unix-like forward slashes, regardless of OS (since in reality Windows doesn't care about slash direction anyway). Also condenses repeat slashes to a single slash and removes and trailing slashes.", "version": "0.1.0", "homepage": "https://github.com/jonschlinkert/normalize-path", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git://github.com/jonschlinkert/normalize-path.git"}, "bugs": {"url": "https://github.com/jonschlinkert/normalize-path/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/jonschlinkert/normalize-path/blob/master/LICENSE-MIT"}], "keywords": ["normalize", "path", "filepath", "slash", "slashes"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha -R spec"}, "devDependencies": {"verb": "~0.2.6", "chai": "~1.9.1", "mocha": "*"}, "_id": "normalize-path@0.1.0", "_shasum": "68d5c21b44abc2e8cc7538b89ffc07bb8908f009", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "68d5c21b44abc2e8cc7538b89ffc07bb8908f009", "tarball": "https://registry.npmjs.org/normalize-path/-/normalize-path-0.1.0.tgz", "integrity": "sha512-QOuJ/3QnlwNVP3UzISGsIh283WX+rKI87jodo+jkAmN7XAdwoHgpRc9MxjyMjV4lEPx610plRH24jcKO6gOuzg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID1JlS3DT75tb6dO/pf1neoACE3UEDJXY7WA5S+wdx0XAiEAp4E7FWdWnS+3usXfMsEA1pNLONww/t2GhqZkRKZnGVU="}]}, "directories": {}}, "0.1.1": {"name": "normalize-path", "description": "Normalize file path slashes to be unix-like forward slashes, regardless of OS (since in reality Windows doesn't care about slash direction anyway). Also condenses repeat slashes to a single slash and removes and trailing slashes.", "version": "0.1.1", "homepage": "https://github.com/jonschlinkert/normalize-path", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git://github.com/jonschlinkert/normalize-path.git"}, "bugs": {"url": "https://github.com/jonschlinkert/normalize-path/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/jonschlinkert/normalize-path/blob/master/LICENSE-MIT"}], "keywords": ["normalize", "path", "filepath", "slash", "slashes"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha -R spec"}, "devDependencies": {"verb": "~0.2.6", "chai": "~1.9.1", "mocha": "*"}, "_id": "normalize-path@0.1.1", "_shasum": "6c701248fdb9896d1fee1aa6620da77feed4d367", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "6c701248fdb9896d1fee1aa6620da77feed4d367", "tarball": "https://registry.npmjs.org/normalize-path/-/normalize-path-0.1.1.tgz", "integrity": "sha512-a563+0LbpFRHIC/TzrhZmRz8yq3dkv7EjPO55JLsuysJcG9HB3Tivyjasoy7rj/edeSy75RbuW5unIQWNq1t3Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCDjBbL1qau/qyMOP3gqEsvkMI+H+RXIsD/tqzN/9DUBgIgJRgzzXsT4KUF5PqYVYgACaYgwjG1kH8zdI40I6OpbcQ="}]}, "directories": {}}, "0.2.0": {"name": "normalize-path", "description": "Normalize file path slashes to be unix-like forward slashes. Also condenses repeat slashes to a single slash and removes and trailing slashes.", "version": "0.2.0", "homepage": "https://github.com/jonschlinkert/normalize-path", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git://github.com/jonschlinkert/normalize-path.git"}, "bugs": {"url": "https://github.com/jonschlinkert/normalize-path/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/jonschlinkert/normalize-path/blob/master/LICENSE-MIT"}], "keywords": ["file", "filepath", "fp", "normalize", "path", "slash", "slashes", "trailing", "unix"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha -R spec"}, "devDependencies": {"benchmarked": "^0.1.1", "mocha": "*", "should": "^4.0.4", "verb": "^0.2.15"}, "_id": "normalize-path@0.2.0", "_shasum": "a7f3f2b3920ff92746ebae7d753d6b60b8a0043a", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "a7f3f2b3920ff92746ebae7d753d6b60b8a0043a", "tarball": "https://registry.npmjs.org/normalize-path/-/normalize-path-0.2.0.tgz", "integrity": "sha512-CXqiGRxOBjGIoh41wjA//2cUqmFHBkJk45GR/GLZyg22kPKJiQgSOvTdriOEMAXO7ThdqQXxgU+FfUJYHBJ6Qg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDL+ATvIvrTVUBUY8y0PBKtMD6PrlK0T1B9Eq2es1ParQIgNXjaIWxXRnV3zvIMdMoiRlfvxI1IhTBNf6jBepNQ82o="}]}, "directories": {}}, "0.2.1": {"name": "normalize-path", "description": "Normalize file path slashes to be unix-like forward slashes. Also condenses repeat slashes to a single slash and removes and trailing slashes.", "version": "0.2.1", "homepage": "https://github.com/jonschlinkert/normalize-path", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git://github.com/jonschlinkert/normalize-path.git"}, "bugs": {"url": "https://github.com/jonschlinkert/normalize-path/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/jonschlinkert/normalize-path/blob/master/LICENSE-MIT"}], "keywords": ["file", "filepath", "fp", "normalize", "path", "slash", "slashes", "trailing", "unix"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha -R spec"}, "devDependencies": {"benchmarked": "^0.1.1", "mocha": "*", "should": "^4.0.4", "verb": "^0.2.15"}, "_id": "normalize-path@0.2.1", "_shasum": "961a3caf0a03c6dfde3804f47855d6e6ad14f15a", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "961a3caf0a03c6dfde3804f47855d6e6ad14f15a", "tarball": "https://registry.npmjs.org/normalize-path/-/normalize-path-0.2.1.tgz", "integrity": "sha512-2/yUi2t3d4gHMGaQJygv0WAUl/8l2WCS1YiztwloTo/ns3JdBWf9KANdC8689nvUnEh55MdKXOaiyRRcPUu5Ng==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBLpTkSC6XJRm+NiSFladu20GwKGALM9sYLUVvctnp3eAiEAqN4wvibY+waGZPArL4c8sCUQAj3DYFapyQSZDq6BAbI="}]}, "directories": {}}, "0.3.0": {"name": "normalize-path", "description": "Normalize file path slashes to be unix-like forward slashes. Also condenses repeat slashes to a single slash and removes and trailing slashes.", "version": "0.3.0", "homepage": "https://github.com/jonschlinkert/normalize-path", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git://github.com/jonschlinkert/normalize-path.git"}, "bugs": {"url": "https://github.com/jonschlinkert/normalize-path/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/jonschlinkert/normalize-path/blob/master/LICENSE-MIT"}], "keywords": ["file", "filepath", "fp", "normalize", "path", "slash", "slashes", "trailing", "unix"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha -R spec"}, "devDependencies": {"benchmarked": "^0.1.1", "mocha": "*", "should": "^4.0.4", "verb": "^0.2.15"}, "_id": "normalize-path@0.3.0", "_shasum": "128d0dcc3c553a61c38c36c17410bb1e147eec7c", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "128d0dcc3c553a61c38c36c17410bb1e147eec7c", "tarball": "https://registry.npmjs.org/normalize-path/-/normalize-path-0.3.0.tgz", "integrity": "sha512-t4lGJnqyshije+i5EO/VGp8v14dxGSWizkdUDkCf73OTn9zLQB6HvYcRR+lbIQJLK0KQOGQza/DB2O4eIHkLfw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFvqfUSgs6C5cOVsgYrLW05ulpdKd5sSiDKuF+AMQe+FAiEAh/oldTYLNzSvsIpQTphTZWIjr2jiaKHX3vT/JOsTqR0="}]}, "directories": {}}, "1.0.0": {"name": "normalize-path", "description": "Normalize file path slashes to be unix-like forward slashes. Also condenses repeat slashes to a single slash and removes and trailing slashes.", "version": "1.0.0", "homepage": "https://github.com/jonschlinkert/normalize-path", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git://github.com/jonschlinkert/normalize-path.git"}, "bugs": {"url": "https://github.com/jonschlinkert/normalize-path/issues"}, "license": {"type": "MIT", "url": "https://github.com/jonschlinkert/normalize-path/blob/master/LICENSE-MIT"}, "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha -R spec"}, "devDependencies": {"benchmarked": "^0.1.1", "mocha": "*"}, "keywords": ["file", "filepath", "fp", "normalize", "path", "slash", "slashes", "trailing", "unix"], "gitHead": "f52e588fcc18ad2d8b7b3e8fa294290d80e51ae5", "_id": "normalize-path@1.0.0", "_shasum": "32d0e472f91ff345701c15a8311018d3b0a90379", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "32d0e472f91ff345701c15a8311018d3b0a90379", "tarball": "https://registry.npmjs.org/normalize-path/-/normalize-path-1.0.0.tgz", "integrity": "sha512-7WyT0w8jhpDStXRq5836AMmihQwq2nrUVQrgjvUo/p/NZf9uy/MeJ246lBJVmWuYXMlJuG9BNZHF0hWjfTbQUA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC/ep7OZKMNDlQNGRKSNiY0sc5UCy5hxYoU9CIp2qz++QIgR7XZBba/GlnjWq7ROAD32aQu7sx8Q7GxPqidDWyqoek="}]}, "directories": {}}, "2.0.0": {"name": "normalize-path", "description": "Normalize file path slashes to be unix-like forward slashes. Also condenses repeat slashes to a single slash and removes and trailing slashes.", "version": "2.0.0", "homepage": "https://github.com/jonschlinkert/normalize-path", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/normalize-path.git"}, "bugs": {"url": "https://github.com/jonschlinkert/normalize-path/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"benchmarked": "^0.1.1", "minimist": "^1.2.0", "mocha": "*"}, "keywords": ["backslash", "file", "fs", "filepath", "fix", "forward", "fp", "normalize", "path", "slash", "slashes", "trailing", "unix", "u<PERSON>"], "verb": {"related": {"description": "Other useful libraries for working with paths in node.js:", "list": ["rewrite-ext", "contains-path", "ends-with", "path-ends-with", "path-segments", "is-absolute", "is-relative", "parse-filepath", "unixify"]}}, "gitHead": "defaa4b483a56e8ee93539b74e9de4df59a2a172", "_id": "normalize-path@2.0.0", "_shasum": "2dba8b5aa9acb130a60c560b8ee26b71872543d9", "_from": ".", "_npmVersion": "2.14.4", "_nodeVersion": "4.1.1", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "2dba8b5aa9acb130a60c560b8ee26b71872543d9", "tarball": "https://registry.npmjs.org/normalize-path/-/normalize-path-2.0.0.tgz", "integrity": "sha512-lZDv5ZJ4ePnjwOsOnnT9hjSWt7nYXP+KpjGvOXfXf/PFtbulVlIsnyQwiW2WRUNEJT9p+WN2x63o/Da17pRQ3g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDeERrhWJ0vd1osuMSnL/IaU9oonv2Hwlz/r8otSFxRQAIgD4AiZkYveLKa2i9CVsMGtrPEODyUTQae6WWOHhyHlAQ="}]}, "directories": {}}, "2.0.1": {"name": "normalize-path", "description": "Normalize file path slashes to be unix-like forward slashes. Also condenses repeat slashes to a single slash and removes and trailing slashes.", "version": "2.0.1", "homepage": "https://github.com/jonschlinkert/normalize-path", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/normalize-path.git"}, "bugs": {"url": "https://github.com/jonschlinkert/normalize-path/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"benchmarked": "^0.1.1", "minimist": "^1.2.0", "mocha": "*"}, "keywords": ["backslash", "file", "filepath", "fix", "forward", "fp", "fs", "normalize", "path", "slash", "slashes", "trailing", "unix", "u<PERSON>"], "verb": {"related": {"list": ["rewrite-ext", "contains-path", "ends-with", "path-ends-with", "path-segments", "is-absolute", "is-relative", "parse-filepath", "unixify"], "description": "Other useful libraries for working with paths in node.js:"}}, "gitHead": "ca536e0e8755d3ed04f3ba4d21cc9e122e0f749f", "_id": "normalize-path@2.0.1", "_shasum": "47886ac1662760d4261b7d979d241709d3ce3f7a", "_from": ".", "_npmVersion": "3.3.6", "_nodeVersion": "5.0.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "doowb", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "47886ac1662760d4261b7d979d241709d3ce3f7a", "tarball": "https://registry.npmjs.org/normalize-path/-/normalize-path-2.0.1.tgz", "integrity": "sha512-jxWwhoRh27+8aiYjkOl0pPfGPvYr2Y6iMC71HUtSGz2BwSvxlxjv8o0bNF28ex6zY02Yn2FJLWFOpEkZGWFo3A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCqG4uN7T8uVH7lH/dv/MQf6PKyTfyvXVgBUwxQIEEOJQIgJrcJqHJ9vUlDm0Atg3Lw7tjseGRhihZXzSLUx5DJLkE="}]}, "directories": {}}, "2.1.0": {"name": "normalize-path", "description": "Normalize file path slashes to be unix-like forward slashes. Also condenses repeat slashes to a single slash and removes and trailing slashes unless disabled.", "version": "2.1.0", "homepage": "https://github.com/jonschlinkert/normalize-path", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/normalize-path.git"}, "bugs": {"url": "https://github.com/jonschlinkert/normalize-path/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"remove-trailing-separator": "^1.0.1"}, "devDependencies": {"benchmarked": "^0.1.1", "gulp-format-md": "^0.1.11", "minimist": "^1.2.0", "mocha": "*"}, "keywords": ["backslash", "file", "filepath", "fix", "forward", "fp", "fs", "normalize", "path", "slash", "slashes", "trailing", "unix", "u<PERSON>"], "verb": {"related": {"list": ["rewrite-ext", "contains-path", "ends-with", "path-ends-with", "path-segments", "is-absolute", "is-relative", "parse-filepath", "unixify"], "description": "Other useful libraries for working with paths in node.js:"}, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}}, "gitHead": "503ced70bb1d38cc8ad3b263234918029aab58dc", "_id": "normalize-path@2.1.0", "_shasum": "dafd5ec29715fe03224064887a26cd499052f0e4", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.7.3", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "doowb", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "dafd5ec29715fe03224064887a26cd499052f0e4", "tarball": "https://registry.npmjs.org/normalize-path/-/normalize-path-2.1.0.tgz", "integrity": "sha512-1g4HW+SVpjJxuMNvCo6lFsMn5BTKf3Dm3Z5lSx9WyfGQuO9FcwyLq0cWH6wHr1EBHmVw0z8Gx+fq2HG2wsRqpg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDGUOkbWPD7CjfRVseboJN3GpFIQWEF0E9k6fM26awB0gIhAIhAztlIX2gxmz4sOYma+qS0kulvbAki/PAhD5zQcgjt"}]}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/normalize-path-2.1.0.tgz_1490811082626_0.5028359685093164"}, "directories": {}}, "2.1.1": {"name": "normalize-path", "description": "Normalize file path slashes to be unix-like forward slashes. Also condenses repeat slashes to a single slash and removes and trailing slashes unless disabled.", "version": "2.1.1", "homepage": "https://github.com/jonschlinkert/normalize-path", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://twitter.com/Blaine<PERSON><PERSON>litz"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://twitter.com/jonschlinkert"}], "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/normalize-path.git"}, "bugs": {"url": "https://github.com/jonschlinkert/normalize-path/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"remove-trailing-separator": "^1.0.1"}, "devDependencies": {"benchmarked": "^0.1.1", "gulp-format-md": "^0.1.11", "minimist": "^1.2.0", "mocha": "*"}, "keywords": ["backslash", "file", "filepath", "fix", "forward", "fp", "fs", "normalize", "path", "slash", "slashes", "trailing", "unix", "u<PERSON>"], "verb": {"related": {"list": ["contains-path", "ends-with", "is-absolute", "is-relative", "parse-filepath", "path-ends-with", "path-segments", "rewrite-ext", "unixify"], "description": "Other useful libraries for working with paths in node.js:"}, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}}, "gitHead": "da1a45e7a514910ce39875b5327b1d0fa9be3d3e", "_id": "normalize-path@2.1.1", "_shasum": "1ab28b556e198363a8c1a6f7e6fa20137fe6aed9", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.7.3", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "doowb", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "1ab28b556e198363a8c1a6f7e6fa20137fe6aed9", "tarball": "https://registry.npmjs.org/normalize-path/-/normalize-path-2.1.1.tgz", "integrity": "sha512-3pKJwH184Xo/lnH6oyP1q2pMd7HcypqqmRs91/6/i2CGtWwIKGCkOOMTm/zXbgTEWHw1uNpNi/igc3ePOYHb6w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCUzqyGFE4B9NwIOiFPQGjxFGRofsA4LUeOZaCyB7raDwIhALdJOcs2i/kKiqttPcYCheQtKrwlVqhU+cN5t1r6UeJw"}]}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/normalize-path-2.1.1.tgz_1490811684604_0.9070707836654037"}, "directories": {}}, "3.0.0": {"name": "normalize-path", "description": "Normalize slashes in a file path to be posix/unix-like forward slashes. Also condenses repeat slashes to a single slash and removes and trailing slashes, unless disabled.", "version": "3.0.0", "homepage": "https://github.com/jonschlinkert/normalize-path", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "https://twitter.com/Blaine<PERSON><PERSON>litz"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}], "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/normalize-path.git"}, "bugs": {"url": "https://github.com/jonschlinkert/normalize-path/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"gulp-format-md": "^1.0.0", "minimist": "^1.2.0", "mocha": "^3.5.3"}, "keywords": ["absolute", "backslash", "delimiter", "file", "file-path", "filepath", "fix", "forward", "fp", "fs", "normalize", "path", "relative", "separator", "slash", "slashes", "trailing", "unix", "u<PERSON>"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"description": "Other useful path-related libraries:", "list": ["contains-path", "is-absolute", "is-relative", "parse-filepath", "path-ends-with", "path-ends-with", "unixify"]}, "lint": {"reflinks": true}}, "gitHead": "0979eb807a1725d83d5a996347d41067cf773d1f", "_id": "normalize-path@3.0.0", "_npmVersion": "5.8.0", "_nodeVersion": "9.9.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==", "shasum": "0dcd69ff23a1c9b11fd0978316644a0388216a65", "tarball": "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz", "fileCount": 4, "unpackedSize": 9219, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa2K24CRA9TVsSAnZWagAAdR8P/R4ubfpxtTGUUKJcaehy\nyZ+sUPoy4dsPEGTHneRUd4NtcK3SibgO9jXUOl/XBk3Yt6XtShZSiaa0mRax\nD0kIOVVreql7kL6QESkW7xeGFRUc5ANGoWufoOUUfbQOVw7PJCLoxaFdkvh+\njyi4Ef1FIhwT6bnr/73lmK8cb6EJS9jfq7Bby3sL/LSGbAyhA/tRpMAHxhfl\n+uMytQhc/7gPq1fIXL3dkfbrgyIS5nM1oIJCj9pFPy8zljN84tQRczPKFPnE\nKRQxwe9F4uRNtFkWWvyqwrMQtP8wRNVQuyUXGynzHkcIRJaIGKoWqhLjEK9C\nyMLVvhXNK7KXWlWJ7ePLK0jQPAaTNZBjcEFxuOlgxUrJIWnosqY0eWdTaj8D\nZzbYZh8v21ZVbpJ/xma1s0tTifql2+OHoiVZm35RLaRng6pqpWt9ZzwyVenY\n529WrHOyH2DGGrKx8EIN4E/nriZsn+mBx3zM+XR07eL/V2NumPtTUq27+bpy\nDQp5lyt9f2IjXbEWF7KMx6Sv1uega7EuPN/zxZBUQwYVE5jkJ8hYAyO4Kluh\nBDR1xk+T0fPoLEHMayYdtasiJN0WvSJ4HeBNbcuzhTnTeGACdZjJmMa93Mu0\nmSHhfkJ8UThy9odceZvrTdsEGESV082ujONOE8fIlEShmjVxtN0gTgcftH3T\nfY0I\r\n=iebM\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCpKkm7u38oNeP9cx8lnDmHp4lkV2PN0yfcOPaqC8sONgIgJZDQUn6SYFBPTnbCbg+7pBqHeN8xGJLmNwMn7HtwX/E="}]}, "maintainers": [{"email": "<EMAIL>", "name": "doowb"}, {"email": "<EMAIL>", "name": "j<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "phated"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/normalize-path_3.0.0_1524149687530_0.3686172043068001"}, "_hasShrinkwrap": false}}, "readme": "# normalize-path [![NPM version](https://img.shields.io/npm/v/normalize-path.svg?style=flat)](https://www.npmjs.com/package/normalize-path) [![NPM monthly downloads](https://img.shields.io/npm/dm/normalize-path.svg?style=flat)](https://npmjs.org/package/normalize-path) [![NPM total downloads](https://img.shields.io/npm/dt/normalize-path.svg?style=flat)](https://npmjs.org/package/normalize-path) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/normalize-path.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/normalize-path)\n\n> Normalize slashes in a file path to be posix/unix-like forward slashes. Also condenses repeat slashes to a single slash and removes and trailing slashes, unless disabled.\n\nPlease consider following this project's author, [<PERSON>](https://github.com/jonschlinkert), and consider starring the project to show your :heart: and support.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install --save normalize-path\n```\n\n## Usage\n\n```js\nconst normalize = require('normalize-path');\n\nconsole.log(normalize('\\\\foo\\\\bar\\\\baz\\\\')); \n//=> '/foo/bar/baz'\n```\n\n**win32 namespaces**\n\n```js\nconsole.log(normalize('\\\\\\\\?\\\\UNC\\\\Server01\\\\user\\\\docs\\\\Letter.txt')); \n//=> '//?/UNC/Server01/user/docs/Letter.txt'\n\nconsole.log(normalize('\\\\\\\\.\\\\CdRomX')); \n//=> '//./CdRomX'\n```\n\n**Consecutive slashes**\n\nCondenses multiple consecutive forward slashes (except for leading slashes in win32 namespaces) to a single slash.\n\n```js\nconsole.log(normalize('.//foo//bar///////baz/')); \n//=> './foo/bar/baz'\n```\n\n### Trailing slashes\n\nBy default trailing slashes are removed. Pass `false` as the last argument to disable this behavior and _**keep** trailing slashes_:\n\n```js\nconsole.log(normalize('foo\\\\bar\\\\baz\\\\', false)); //=> 'foo/bar/baz/'\nconsole.log(normalize('./foo/bar/baz/', false)); //=> './foo/bar/baz/'\n```\n\n## Release history\n\n### v3.0\n\nNo breaking changes in this release.\n\n* a check was added to ensure that [win32 namespaces](https://msdn.microsoft.com/library/windows/desktop/aa365247(v=vs.85).aspx#namespaces) are handled properly by win32 `path.parse()` after a path has been normalized by this library.\n* a minor optimization was made to simplify how the trailing separator was handled\n\n## About\n\n<details>\n<summary><strong>Contributing</strong></summary>\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).\n\n</details>\n\n<details>\n<summary><strong>Running Tests</strong></summary>\n\nRunning and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:\n\n```sh\n$ npm install && npm test\n```\n\n</details>\n\n<details>\n<summary><strong>Building docs</strong></summary>\n\n_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_\n\nTo generate the readme, run the following command:\n\n```sh\n$ npm install -g verbose/verb#dev verb-generate-readme && verb\n```\n\n</details>\n\n### Related projects\n\nOther useful path-related libraries:\n\n* [contains-path](https://www.npmjs.com/package/contains-path): Return true if a file path contains the given path. | [homepage](https://github.com/jonschlinkert/contains-path \"Return true if a file path contains the given path.\")\n* [is-absolute](https://www.npmjs.com/package/is-absolute): Returns true if a file path is absolute. Does not rely on the path module… [more](https://github.com/jonschlinkert/is-absolute) | [homepage](https://github.com/jonschlinkert/is-absolute \"Returns true if a file path is absolute. Does not rely on the path module and can be used as a polyfill for node.js native `path.isAbolute`.\")\n* [is-relative](https://www.npmjs.com/package/is-relative): Returns `true` if the path appears to be relative. | [homepage](https://github.com/jonschlinkert/is-relative \"Returns `true` if the path appears to be relative.\")\n* [parse-filepath](https://www.npmjs.com/package/parse-filepath): Pollyfill for node.js `path.parse`, parses a filepath into an object. | [homepage](https://github.com/jonschlinkert/parse-filepath \"Pollyfill for node.js `path.parse`, parses a filepath into an object.\")\n* [path-ends-with](https://www.npmjs.com/package/path-ends-with): Return `true` if a file path ends with the given string/suffix. | [homepage](https://github.com/jonschlinkert/path-ends-with \"Return `true` if a file path ends with the given string/suffix.\")\n* [unixify](https://www.npmjs.com/package/unixify): Convert Windows file paths to unix paths. | [homepage](https://github.com/jonschlinkert/unixify \"Convert Windows file paths to unix paths.\")\n\n### Contributors\n\n| **Commits** | **Contributor** | \n| --- | --- |\n| 35 | [jonschlinkert](https://github.com/jonschlinkert) |\n| 1 | [phated](https://github.com/phated) |\n\n### Author\n\n**Jon Schlinkert**\n\n* [LinkedIn Profile](https://linkedin.com/in/jonschlinkert)\n* [GitHub Profile](https://github.com/jonschlinkert)\n* [Twitter Profile](https://twitter.com/jonschlinkert)\n\n### License\n\nCopyright © 2018, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT License](LICENSE).\n\n***\n\n_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.6.0, on April 19, 2018._", "maintainers": [{"email": "<EMAIL>", "name": "doowb"}, {"email": "<EMAIL>", "name": "j<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "phated"}], "time": {"modified": "2023-03-04T04:01:45.310Z", "created": "2014-06-26T01:32:19.923Z", "0.1.0": "2014-06-26T01:32:19.923Z", "0.1.1": "2014-07-06T12:22:37.474Z", "0.2.0": "2014-10-19T01:03:13.494Z", "0.2.1": "2014-10-19T01:19:01.698Z", "0.3.0": "2014-10-24T07:35:00.031Z", "1.0.0": "2015-01-24T02:00:30.511Z", "2.0.0": "2015-10-04T06:22:46.669Z", "2.0.1": "2015-11-17T12:32:25.098Z", "2.1.0": "2017-03-29T18:11:23.340Z", "2.1.1": "2017-03-29T18:21:25.307Z", "3.0.0": "2018-04-19T14:54:47.609Z"}, "homepage": "https://github.com/jonschlinkert/normalize-path", "keywords": ["absolute", "backslash", "delimiter", "file", "file-path", "filepath", "fix", "forward", "fp", "fs", "normalize", "path", "relative", "separator", "slash", "slashes", "trailing", "unix", "u<PERSON>"], "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/normalize-path.git"}, "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/normalize-path/issues"}, "readmeFilename": "README.md", "users": {"jonschlinkert": true, "shakakira": true, "kserks": true, "shiva127": true, "flumpus-dev": true}, "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://twitter.com/Blaine<PERSON><PERSON>litz"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}]}