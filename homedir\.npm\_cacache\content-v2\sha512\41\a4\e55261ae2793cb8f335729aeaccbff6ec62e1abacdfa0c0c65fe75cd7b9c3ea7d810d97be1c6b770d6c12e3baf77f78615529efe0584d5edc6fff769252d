{"_id": "ajv-keywords", "_rev": "53-41b0a2bd594525961f07d9302932a8a8", "name": "ajv-keywords", "description": "Additional JSON-Schema keywords for Ajv JSON validator", "dist-tags": {"latest": "5.1.0", "beta": "5.0.0-beta.1"}, "versions": {"0.1.0": {"name": "ajv-keywords", "version": "0.1.0", "description": "Custom JSON-Schema keywords for ajv validator", "main": "index.js", "scripts": {"test": "npm run test-cov", "test-spec": "mocha spec/*.spec.js -R spec", "test-cov": "istanbul cover -x 'spec/**' node_modules/mocha/bin/_mocha -- spec/*.spec.js -R spec"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/ajv-keywords.git"}, "keywords": ["JSON-Schema", "ajv", "keywords"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/ajv-keywords/issues"}, "homepage": "https://github.com/epoberezkin/ajv-keywords#readme", "devDependencies": {"ajv": "^4.1.2", "chai": "^3.5.0", "istanbul": "^0.4.3", "mocha": "^2.5.3"}, "gitHead": "cb43cd1c598ad1dbea066d9bc2960919b81b4060", "_id": "ajv-keywords@0.1.0", "_shasum": "3270be5a388fc73f3427e88c5c552f0bf161cdab", "_from": ".", "_npmVersion": "2.15.1", "_nodeVersion": "4.4.4", "_npmUser": {"name": "esp", "email": "<EMAIL>"}, "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "dist": {"shasum": "3270be5a388fc73f3427e88c5c552f0bf161cdab", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-0.1.0.tgz", "integrity": "sha512-U6u+XsUZ8vzo7je3O7SbugkpWWbEANGNRAOlsHAgO/O3zHJK9lsrDkOEL84PRp2gFIz5htDsAHCeymF27F/KmA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC+Hs+djdoXAlrnzxBSOZm9w0BP5xYziwEEtWQcb5E+YwIgDvpn9ZuGJZMrIFqZd9rTEzA+9rAAI3MXRrVcDJQ9Baw="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/ajv-keywords-0.1.0.tgz_1465160440778_0.5483748007100075"}, "directories": {}}, "0.1.1": {"name": "ajv-keywords", "version": "0.1.1", "description": "Custom JSON-Schema keywords for ajv validator", "main": "index.js", "scripts": {"test": "npm run eslint && npm run test-cov", "eslint": "eslint index.js keywords/*.js", "test-spec": "mocha spec/*.spec.js -R spec", "test-cov": "istanbul cover -x 'spec/**' node_modules/mocha/bin/_mocha -- spec/*.spec.js -R spec"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/ajv-keywords.git"}, "keywords": ["JSON-Schema", "ajv", "keywords"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/ajv-keywords/issues"}, "homepage": "https://github.com/epoberezkin/ajv-keywords#readme", "devDependencies": {"ajv": "^4.1.2", "chai": "^3.5.0", "coveralls": "^2.11.9", "eslint": "^2.11.1", "istanbul": "^0.4.3", "mocha": "^2.5.3"}, "gitHead": "638d93be16e603e7ed09676594b19cd8d7690cd8", "_id": "ajv-keywords@0.1.1", "_shasum": "d90d08fd88b48e3d81c8e7a29fa6761545796d7f", "_from": ".", "_npmVersion": "2.15.1", "_nodeVersion": "4.4.4", "_npmUser": {"name": "esp", "email": "<EMAIL>"}, "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "dist": {"shasum": "d90d08fd88b48e3d81c8e7a29fa6761545796d7f", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-0.1.1.tgz", "integrity": "sha512-K7WRAtr0Ux6eDzFpQO7iDAjHhuEZTdukR697NSbUhFhONtHhqQPUkOgs9nbJSUQ2nQkTj80G0h/ht2rt/YzjiA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDIIOwc1TGj42Bu37FNMPowJ3MPrVOAPcV/O9aucAoaRQIhAIyPXwSMhwYnsvi0J08tJc1d5ynDEqcZ2SEimXLnxXej"}]}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/ajv-keywords-0.1.1.tgz_1465162125668_0.10025326372124255"}, "directories": {}}, "0.2.0": {"name": "ajv-keywords", "version": "0.2.0", "description": "Custom JSON-Schema keywords for ajv validator", "main": "index.js", "scripts": {"test": "npm run eslint && npm run test-cov", "eslint": "eslint index.js keywords/*.js", "test-spec": "mocha spec/*.spec.js -R spec", "test-cov": "istanbul cover -x 'spec/**' node_modules/mocha/bin/_mocha -- spec/*.spec.js -R spec"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/ajv-keywords.git"}, "keywords": ["JSON-Schema", "ajv", "keywords"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/ajv-keywords/issues"}, "homepage": "https://github.com/epoberezkin/ajv-keywords#readme", "devDependencies": {"ajv": "^4.1.2", "chai": "^3.5.0", "coveralls": "^2.11.9", "eslint": "^2.11.1", "istanbul": "^0.4.3", "mocha": "^2.5.3", "pre-commit": "^1.1.3"}, "gitHead": "1eba9eca976270cf180a529e9c1ad6c0d2eb2cba", "_id": "ajv-keywords@0.2.0", "_shasum": "0f2660200504451e90c5a8d9d8fbff45844a7ec9", "_from": ".", "_npmVersion": "2.15.1", "_nodeVersion": "4.4.4", "_npmUser": {"name": "esp", "email": "<EMAIL>"}, "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "dist": {"shasum": "0f2660200504451e90c5a8d9d8fbff45844a7ec9", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-0.2.0.tgz", "integrity": "sha512-9JA5nZLp2oWgSFbxBGqistzjXYb67oGqWbEApbboHulAWsn0snY5hk4D/gM/OmInk8enPhJY1QNF1GcUtbl9dg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDcHSeKkTQ7cPVZBMTieJJt7VRRvpKX4UJtyqZzAUyVswIhAPAtz3aiKCWuY0XfhXBBnL2iwcAM3zUtQXQ0kMyuGvIX"}]}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/ajv-keywords-0.2.0.tgz_1465646441172_0.7174067301675677"}, "directories": {}}, "1.0.0": {"name": "ajv-keywords", "version": "1.0.0", "description": "Custom JSON-Schema keywords for ajv validator", "main": "index.js", "scripts": {"test": "npm run eslint && npm run test-cov", "eslint": "eslint index.js keywords/*.js", "test-spec": "mocha spec/*.spec.js -R spec", "test-cov": "istanbul cover -x 'spec/**' node_modules/mocha/bin/_mocha -- spec/*.spec.js -R spec"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/ajv-keywords.git"}, "keywords": ["JSON-Schema", "ajv", "keywords"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/ajv-keywords/issues"}, "homepage": "https://github.com/epoberezkin/ajv-keywords#readme", "peerDependencies": {"ajv": ">=4.2.0"}, "devDependencies": {"ajv": "^4.6.1", "chai": "^3.5.0", "coveralls": "^2.11.9", "eslint": "^2.11.1", "istanbul": "^0.4.3", "mocha": "^2.5.3", "pre-commit": "^1.1.3"}, "gitHead": "5ec0b06fa8a19a14b2f45edd17387cdde1fc940b", "_id": "ajv-keywords@1.0.0", "_shasum": "3dc461b3ef08f87242657419b062369bd758dd8d", "_from": ".", "_npmVersion": "2.15.1", "_nodeVersion": "4.4.4", "_npmUser": {"name": "esp", "email": "<EMAIL>"}, "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "dist": {"shasum": "3dc461b3ef08f87242657419b062369bd758dd8d", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-1.0.0.tgz", "integrity": "sha512-mUBcLUJOErLcFwdxDHEVOtsgfPAqIzhzzZQBATm7ueRjlktmCWEAIKiacGOYCTVho9OVwFQCJQ0gW721RQCPzA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCo3HRbqQLz1y8TWdnZwDvW87191LkZuV2wpdPMVPLs0gIgZyRr8Vo3NF3j+ZSNFmZKOeAiV9ahw0lShv352Vcl9jw="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/ajv-keywords-1.0.0.tgz_1473894306858_0.5649349656887352"}, "directories": {}}, "1.1.0": {"name": "ajv-keywords", "version": "1.1.0", "description": "Custom JSON-Schema keywords for ajv validator", "main": "index.js", "scripts": {"test": "npm run eslint && npm run test-cov", "eslint": "eslint index.js keywords/*.js", "test-spec": "mocha spec/*.spec.js -R spec", "test-cov": "istanbul cover -x 'spec/**' node_modules/mocha/bin/_mocha -- spec/*.spec.js -R spec"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/ajv-keywords.git"}, "keywords": ["JSON-Schema", "ajv", "keywords"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/ajv-keywords/issues"}, "homepage": "https://github.com/epoberezkin/ajv-keywords#readme", "peerDependencies": {"ajv": ">=4.2.0"}, "devDependencies": {"ajv": "^4.6.1", "chai": "^3.5.0", "coveralls": "^2.11.9", "eslint": "^2.11.1", "istanbul": "^0.4.3", "mocha": "^2.5.3", "pre-commit": "^1.1.3"}, "gitHead": "e88027057246a69452ccd2259c4b9facf1c89daa", "_id": "ajv-keywords@1.1.0", "_shasum": "9ec1600c61080ab32140fceacb09163c2ca41e5b", "_from": ".", "_npmVersion": "2.15.1", "_nodeVersion": "4.4.4", "_npmUser": {"name": "esp", "email": "<EMAIL>"}, "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "dist": {"shasum": "9ec1600c61080ab32140fceacb09163c2ca41e5b", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-1.1.0.tgz", "integrity": "sha512-IB0NB4NqiGmoGmERVdiyV3scWTbJ54Vh92yJryKW9J9BZ/J4YAlvujqcWiL88k2QeLfiuZVmCypnFMMTjZo5qA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB1HFSXbYoTTvWHkr7vt0Gm7GjQ1xu6PjoD0NTdxSuTIAiAuoVeQSGHSUorD8D53tZGR3luoDXArxNwG0ZAAVjutag=="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/ajv-keywords-1.1.0.tgz_1474494620403_0.38224623957648873"}, "directories": {}}, "1.1.1": {"name": "ajv-keywords", "version": "1.1.1", "description": "Custom JSON-Schema keywords for ajv validator", "main": "index.js", "scripts": {"test": "npm run eslint && npm run test-cov", "eslint": "eslint index.js keywords/*.js", "test-spec": "mocha spec/*.spec.js -R spec", "test-cov": "istanbul cover -x 'spec/**' node_modules/mocha/bin/_mocha -- spec/*.spec.js -R spec"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/ajv-keywords.git"}, "keywords": ["JSON-Schema", "ajv", "keywords"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/ajv-keywords/issues"}, "homepage": "https://github.com/epoberezkin/ajv-keywords#readme", "peerDependencies": {"ajv": ">=4.2.0"}, "devDependencies": {"ajv": "^4.7.4", "ajv-pack": "^0.2.0", "chai": "^3.5.0", "coveralls": "^2.11.9", "eslint": "^2.11.1", "istanbul": "^0.4.3", "mocha": "^2.5.3", "pre-commit": "^1.1.3"}, "gitHead": "0d3270f022b24be277952ae9ffdab533464412ed", "_id": "ajv-keywords@1.1.1", "_shasum": "02550bc605a3e576041565628af972e06c549d50", "_from": ".", "_npmVersion": "2.15.1", "_nodeVersion": "4.4.4", "_npmUser": {"name": "esp", "email": "<EMAIL>"}, "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "dist": {"shasum": "02550bc605a3e576041565628af972e06c549d50", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-1.1.1.tgz", "integrity": "sha512-tE/AzlA++Yh5NVn8FXoyZO/HUBfqUbMTpDSAanIYMQy54bQenQCYiE4d+a9CIEUsS2xTphyeSswkaodj0hekLQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDYPNF1UoEsQmwsj/SJvqfoVP4jJWtynmK5oFUU48bkUwIhAMpE51rvlryq6N+wsMBwQtqFIAU2uSOU6muaUWh3XYD0"}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/ajv-keywords-1.1.1.tgz_1474741068795_0.7051337675657123"}, "directories": {}}, "1.2.0": {"name": "ajv-keywords", "version": "1.2.0", "description": "Custom JSON-Schema keywords for ajv validator", "main": "index.js", "scripts": {"test": "npm run eslint && npm run test-cov", "eslint": "eslint index.js keywords/*.js", "test-spec": "mocha spec/*.spec.js -R spec", "test-cov": "istanbul cover -x 'spec/**' node_modules/mocha/bin/_mocha -- spec/*.spec.js -R spec"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/ajv-keywords.git"}, "keywords": ["JSON-Schema", "ajv", "keywords"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/ajv-keywords/issues"}, "homepage": "https://github.com/epoberezkin/ajv-keywords#readme", "peerDependencies": {"ajv": ">=4.9.0"}, "devDependencies": {"ajv": "^4.9.0", "ajv-pack": "^0.2.0", "chai": "^3.5.0", "coveralls": "^2.11.9", "eslint": "^3.6.0", "istanbul": "^0.4.3", "mocha": "^3.0.2", "pre-commit": "^1.1.3", "uuid": "^3.0.1"}, "gitHead": "7ecec64e70bf9eba48ee22b460e1bdd066ad1410", "_id": "ajv-keywords@1.2.0", "_shasum": "676c4f087bfe1e8b12dca6fda2f3c74f417b099c", "_from": ".", "_npmVersion": "2.15.9", "_nodeVersion": "4.6.1", "_npmUser": {"name": "esp", "email": "<EMAIL>"}, "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "dist": {"shasum": "676c4f087bfe1e8b12dca6fda2f3c74f417b099c", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-1.2.0.tgz", "integrity": "sha512-3L44bBuV9JpvY7Tildrc6mdwD1LtAoFz7cgFEoUiMNk7/xEXKT7JgjW/u2bkgBb1JVIGM04n53a2gRaV2r6E4A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAvh3Y0myaHvi0O/WlVnV7XEdS60n9aSrnDKbRVBXTUIAiEA75pWq0MWziNkLJI21b1Zclw9wVYQVfK+UlxjZz7gwdg="}]}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/ajv-keywords-1.2.0.tgz_1480671962589_0.01684238761663437"}, "directories": {}}, "1.2.1": {"name": "ajv-keywords", "version": "1.2.1", "description": "Custom JSON-Schema keywords for ajv validator", "main": "index.js", "scripts": {"build": "node node_modules/ajv/scripts/compile-dots.js node_modules/ajv/lib keywords", "prepublish": "npm run build", "test": "npm run build && npm run eslint && npm run test-cov", "eslint": "eslint index.js keywords/*.js", "test-spec": "mocha spec/*.spec.js -R spec", "test-cov": "istanbul cover -x 'spec/**' node_modules/mocha/bin/_mocha -- spec/*.spec.js -R spec"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/ajv-keywords.git"}, "keywords": ["JSON-Schema", "ajv", "keywords"], "files": ["index.js", "keywords"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/ajv-keywords/issues"}, "homepage": "https://github.com/epoberezkin/ajv-keywords#readme", "peerDependencies": {"ajv": ">=4.10.0"}, "devDependencies": {"ajv": "^4.10.0", "ajv-pack": "^0.2.0", "chai": "^3.5.0", "coveralls": "^2.11.9", "dot": "^1.1.1", "eslint": "^3.6.0", "glob": "^7.1.1", "istanbul": "^0.4.3", "js-beautify": "^1.6.4", "json-schema-test": "^1.2.1", "mocha": "^3.0.2", "pre-commit": "^1.1.3", "uuid": "^3.0.1"}, "gitHead": "f09fdb7f45e0c0be1e8d8e346ef8d7a15ab391ce", "_id": "ajv-keywords@1.2.1", "_shasum": "2e1cbda5f82b2466f9fbcc8c6d6986d8b44df27e", "_from": ".", "_npmVersion": "2.15.9", "_nodeVersion": "4.6.1", "_npmUser": {"name": "esp", "email": "<EMAIL>"}, "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "dist": {"shasum": "2e1cbda5f82b2466f9fbcc8c6d6986d8b44df27e", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-1.2.1.tgz", "integrity": "sha512-fzIW24GBrnWKE1WzuHiZ16e1o6DY5d/VgdBAmwIxAyJB9kOUKjIJ6XowIBz8v6ETvwO36UYtcTUC60JQch6JHA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDWKe87BBGucd50dSz9BUv2o9w2kUA4apgKPLB61Z71nwIgCiehf6QcQTHEO+nfzfib3wE31vHfPTZNJG+2CZ5PPfo="}]}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/ajv-keywords-1.2.1.tgz_1482440490693_0.8673460697755218"}, "directories": {}}, "1.3.0": {"name": "ajv-keywords", "version": "1.3.0", "description": "Custom JSON-Schema keywords for ajv validator", "main": "index.js", "scripts": {"build": "node node_modules/ajv/scripts/compile-dots.js node_modules/ajv/lib keywords", "prepublish": "npm run build", "test": "npm run build && npm run eslint && npm run test-cov", "eslint": "eslint index.js keywords/*.js", "test-spec": "mocha spec/*.spec.js -R spec", "test-cov": "istanbul cover -x 'spec/**' node_modules/mocha/bin/_mocha -- spec/*.spec.js -R spec"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/ajv-keywords.git"}, "keywords": ["JSON-Schema", "ajv", "keywords"], "files": ["index.js", "keywords"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/ajv-keywords/issues"}, "homepage": "https://github.com/epoberezkin/ajv-keywords#readme", "peerDependencies": {"ajv": ">=4.10.0"}, "devDependencies": {"ajv": "^4.10.0", "ajv-pack": "^0.2.0", "chai": "^3.5.0", "coveralls": "^2.11.9", "dot": "^1.1.1", "eslint": "^3.6.0", "glob": "^7.1.1", "istanbul": "^0.4.3", "js-beautify": "^1.6.4", "json-schema-test": "^1.2.1", "mocha": "^3.0.2", "pre-commit": "^1.1.3", "uuid": "^3.0.1"}, "gitHead": "b5fee6a11aec11ad4e2ea69c4aa5652bfefb1926", "_id": "ajv-keywords@1.3.0", "_shasum": "b2dbcdb32ce40b7a64ce5bc6e4ec9b0a918b455a", "_from": ".", "_npmVersion": "2.15.9", "_nodeVersion": "4.6.1", "_npmUser": {"name": "esp", "email": "<EMAIL>"}, "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "dist": {"shasum": "b2dbcdb32ce40b7a64ce5bc6e4ec9b0a918b455a", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-1.3.0.tgz", "integrity": "sha512-dZig+w5scKjitVeBDFJGDrE93itaivBQYhjvAxRowm9Z+ZxamaNHZNtxwoqenYhrBXwL878KFUmhbGShR4jAkg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDxBN9K5xDUff71lBR+DfbiU1q9xZnFPM5dp8ljwuYmvQIhAMUzq6gO8uhgCQZnHevtn4Y+YXCrCeTR1o5etn1Q/mEq"}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/ajv-keywords-1.3.0.tgz_1482444828317_0.886456849751994"}, "directories": {}}, "1.4.0": {"name": "ajv-keywords", "version": "1.4.0", "description": "Custom JSON-Schema keywords for ajv validator", "main": "index.js", "scripts": {"build": "node node_modules/ajv/scripts/compile-dots.js node_modules/ajv/lib keywords", "prepublish": "npm run build", "test": "npm run build && npm run eslint && npm run test-cov", "eslint": "eslint index.js keywords/*.js", "test-spec": "mocha spec/*.spec.js -R spec", "test-cov": "istanbul cover -x 'spec/**' node_modules/mocha/bin/_mocha -- spec/*.spec.js -R spec"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/ajv-keywords.git"}, "keywords": ["JSON-Schema", "ajv", "keywords"], "files": ["index.js", "keywords"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/ajv-keywords/issues"}, "homepage": "https://github.com/epoberezkin/ajv-keywords#readme", "peerDependencies": {"ajv": ">=4.10.0"}, "devDependencies": {"ajv": "^4.10.0", "ajv-pack": "^0.2.0", "chai": "^3.5.0", "coveralls": "^2.11.9", "dot": "^1.1.1", "eslint": "^3.6.0", "glob": "^7.1.1", "istanbul": "^0.4.3", "js-beautify": "^1.6.4", "json-schema-test": "^1.2.1", "mocha": "^3.0.2", "pre-commit": "^1.1.3", "uuid": "^3.0.1"}, "gitHead": "f2898ac4e97b6c55cf5c219a1ea9f5fb1ffa305f", "_id": "ajv-keywords@1.4.0", "_shasum": "87db6a428bac4a5057a772fa83c6c22b6ec2768e", "_from": ".", "_npmVersion": "2.15.9", "_nodeVersion": "4.6.1", "_npmUser": {"name": "esp", "email": "<EMAIL>"}, "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "dist": {"shasum": "87db6a428bac4a5057a772fa83c6c22b6ec2768e", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-1.4.0.tgz", "integrity": "sha512-vpx09L9MuK3+NyCJq6HiR08I6K38bOvJYRhI+3Q7+83UyH9b3ZPBwensfeSefZZuhcwi56geMthfo9I+tIK5FQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDISX95IL5inVfuTAScpGxeXC68wt92SwGyU1rZRF0atwIgCoVkg4Y1LKu2m2BoB7qTQMqGAvvXtkKvi6YiQmcM7J0="}]}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/ajv-keywords-1.4.0.tgz_1482510420260_0.4964188707526773"}, "directories": {}}, "1.4.1": {"name": "ajv-keywords", "version": "1.4.1", "description": "Custom JSON-Schema keywords for ajv validator", "main": "index.js", "scripts": {"build": "node node_modules/ajv/scripts/compile-dots.js node_modules/ajv/lib keywords", "prepublish": "npm run build", "test": "npm run build && npm run eslint && npm run test-cov", "eslint": "eslint index.js keywords/*.js", "test-spec": "mocha spec/*.spec.js -R spec", "test-cov": "istanbul cover -x 'spec/**' node_modules/mocha/bin/_mocha -- spec/*.spec.js -R spec"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/ajv-keywords.git"}, "keywords": ["JSON-Schema", "ajv", "keywords"], "files": ["index.js", "keywords"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/ajv-keywords/issues"}, "homepage": "https://github.com/epoberezkin/ajv-keywords#readme", "peerDependencies": {"ajv": ">=4.10.0"}, "devDependencies": {"ajv": "^4.10.0", "ajv-pack": "^0.2.0", "chai": "^3.5.0", "coveralls": "^2.11.9", "dot": "^1.1.1", "eslint": "^3.6.0", "glob": "^7.1.1", "istanbul": "^0.4.3", "js-beautify": "^1.6.4", "json-schema-test": "^1.2.1", "mocha": "^3.0.2", "pre-commit": "^1.1.3", "uuid": "^3.0.1"}, "gitHead": "f1e1000458c8ca6bfd9b54133bc5c73392127df7", "_id": "ajv-keywords@1.4.1", "_shasum": "f080e635e230baae26537ce727f260ae62b43802", "_from": ".", "_npmVersion": "2.15.9", "_nodeVersion": "4.6.1", "_npmUser": {"name": "esp", "email": "<EMAIL>"}, "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "dist": {"shasum": "f080e635e230baae26537ce727f260ae62b43802", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-1.4.1.tgz", "integrity": "sha512-Nzs7RomP93A2i93P0Nlp109zGgrPdlG4icdGfplMYMu9nxrBvaygy6A5Og8+zx5tU7xFkXv7NqGzYUVE+yo6lg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCv1PLbos/pT9Nu4JMNdIf65fR8LDFTa5kSjtNzYwH8xwIgElBl7pLefQo9+hNQ6dtZVr4JRKpXjCF0xSqSF7Qcm88="}]}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/ajv-keywords-1.4.1.tgz_1482515496271_0.6470574194099754"}, "directories": {}}, "1.5.0": {"name": "ajv-keywords", "version": "1.5.0", "description": "Custom JSON-Schema keywords for ajv validator", "main": "index.js", "scripts": {"build": "node node_modules/ajv/scripts/compile-dots.js node_modules/ajv/lib keywords", "prepublish": "npm run build", "test": "npm run build && npm run eslint && npm run test-cov", "eslint": "eslint index.js keywords/*.js", "test-spec": "mocha spec/*.spec.js -R spec", "test-cov": "istanbul cover -x 'spec/**' node_modules/mocha/bin/_mocha -- spec/*.spec.js -R spec"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/ajv-keywords.git"}, "keywords": ["JSON-Schema", "ajv", "keywords"], "files": ["index.js", "keywords"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/ajv-keywords/issues"}, "homepage": "https://github.com/epoberezkin/ajv-keywords#readme", "peerDependencies": {"ajv": ">=4.10.0"}, "devDependencies": {"ajv": "^4.10.0", "ajv-pack": "^0.2.0", "chai": "^3.5.0", "coveralls": "^2.11.9", "dot": "^1.1.1", "eslint": "^3.6.0", "glob": "^7.1.1", "istanbul": "^0.4.3", "js-beautify": "^1.6.4", "json-schema-test": "^1.2.1", "mocha": "^3.0.2", "pre-commit": "^1.1.3", "uuid": "^3.0.1"}, "gitHead": "0677af4020d8aee0e322b78932662a2fd59bd93f", "_id": "ajv-keywords@1.5.0", "_shasum": "c11e6859eafff83e0dafc416929472eca946aa2c", "_from": ".", "_npmVersion": "2.15.9", "_nodeVersion": "4.6.1", "_npmUser": {"name": "esp", "email": "<EMAIL>"}, "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "dist": {"shasum": "c11e6859eafff83e0dafc416929472eca946aa2c", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-1.5.0.tgz", "integrity": "sha512-VpV3NY65ZMIwYNX59aIygyx4z89VVMGCxPqAYbvAEZKMivVbZFpWwbeomRJNw8pRkQk66Mg8o0/d8N82bs8QKA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBx8/hrzKOWDBoAhW//Hv1oJVGutINSBWw6hJ4h7ZMVfAiEAqSo2BRRRz6OYOcAMSkeAq4jVfoWJHkYUI3mWezRb+Ls="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/ajv-keywords-1.5.0.tgz_1482960345081_0.35162315983325243"}, "directories": {}}, "2.0.0-beta.0": {"name": "ajv-keywords", "version": "2.0.0-beta.0", "description": "Custom JSON-Schema keywords for Ajv validator", "main": "index.js", "scripts": {"build": "node node_modules/ajv/scripts/compile-dots.js node_modules/ajv/lib keywords", "prepublish": "npm run build", "test": "npm run build && npm run eslint && npm run test-cov", "eslint": "eslint index.js keywords/*.js", "test-spec": "mocha spec/*.spec.js -R spec", "test-cov": "istanbul cover -x 'spec/**' node_modules/mocha/bin/_mocha -- spec/*.spec.js -R spec"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/ajv-keywords.git"}, "keywords": ["JSON-Schema", "ajv", "keywords"], "files": ["index.js", "keywords"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/ajv-keywords/issues"}, "homepage": "https://github.com/epoberezkin/ajv-keywords#readme", "peerDependencies": {"ajv": ">=5.0.1-beta.0"}, "devDependencies": {"ajv": "^5.0.1-beta.0", "ajv-pack": "^0.2.0", "chai": "^3.5.0", "coveralls": "^2.11.9", "dot": "^1.1.1", "eslint": "^3.6.0", "glob": "^7.1.1", "istanbul": "^0.4.3", "js-beautify": "^1.6.4", "json-schema-test": "^1.3.0", "mocha": "^3.0.2", "pre-commit": "^1.1.3", "uuid": "^3.0.1"}, "gitHead": "a6ea94628725358179d26f6146f74fa2a2f69f71", "_id": "ajv-keywords@2.0.0-beta.0", "_shasum": "913711c5efea0ce53af27bea199873a3e4cf17bd", "_from": ".", "_npmVersion": "2.15.9", "_nodeVersion": "4.6.1", "_npmUser": {"name": "esp", "email": "<EMAIL>"}, "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "dist": {"shasum": "913711c5efea0ce53af27bea199873a3e4cf17bd", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-2.0.0-beta.0.tgz", "integrity": "sha512-Y+i0ADj178I1Rs0op3tXKe+CC1kJGHcft1Pb7OYNTRwKLLgUSn8wJ9gjLfEc9stVsIoWijM39O5yG0Odw7DBLw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC5H8mIT1e53qwWtT4FLQT5meHAQ/zKjovgX//6bxEjhQIhANUdZjA9oImrrDhwyG97YALLSXM0jc1jsHLJKFPRLDRP"}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/ajv-keywords-2.0.0-beta.0.tgz_1483146249137_0.8622809236403555"}, "directories": {}}, "2.0.0-beta.1": {"name": "ajv-keywords", "version": "2.0.0-beta.1", "description": "Custom JSON-Schema keywords for Ajv validator", "main": "index.js", "scripts": {"build": "node node_modules/ajv/scripts/compile-dots.js node_modules/ajv/lib keywords", "prepublish": "npm run build", "test": "npm run build && npm run eslint && npm run test-cov", "eslint": "eslint index.js keywords/*.js", "test-spec": "mocha spec/*.spec.js -R spec", "test-cov": "istanbul cover -x 'spec/**' node_modules/mocha/bin/_mocha -- spec/*.spec.js -R spec"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/ajv-keywords.git"}, "keywords": ["JSON-Schema", "ajv", "keywords"], "files": ["index.js", "keywords"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/ajv-keywords/issues"}, "homepage": "https://github.com/epoberezkin/ajv-keywords#readme", "peerDependencies": {"ajv": ">=5.0.1-beta.0"}, "devDependencies": {"ajv": "^5.0.1-beta.0", "ajv-pack": "^0.2.0", "chai": "^3.5.0", "coveralls": "^2.11.9", "dot": "^1.1.1", "eslint": "^3.6.0", "glob": "^7.1.1", "istanbul": "^0.4.3", "js-beautify": "^1.6.4", "json-schema-test": "^1.3.0", "mocha": "^3.0.2", "pre-commit": "^1.1.3", "uuid": "^3.0.1"}, "gitHead": "1567814d6faf32332eb1088c20a2a21b6831c47c", "_id": "ajv-keywords@2.0.0-beta.1", "_shasum": "30c7a6720aca04af02012afd06ed626339c79de1", "_from": ".", "_npmVersion": "2.15.9", "_nodeVersion": "4.6.1", "_npmUser": {"name": "esp", "email": "<EMAIL>"}, "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "dist": {"shasum": "30c7a6720aca04af02012afd06ed626339c79de1", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-2.0.0-beta.1.tgz", "integrity": "sha512-sKl7nJx8blRcRcPfHz/4n1v2QhCMDYAFrkh+OX5kwXR1tdHxHMwNtSErAh7mA8qKLZbt6yZYPnoHPOdLfUNS1Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDlfU9g8YbicysvY0a9naDTlH0ZBvyRNLhM+PaKpuKKCAiEAsFehbRx7wkGYgRc7hVgbpRCKABFt3NjYW+rjqH+d3KY="}]}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/ajv-keywords-2.0.0-beta.1.tgz_1485107416083_0.1587244407273829"}, "directories": {}}, "1.5.1": {"name": "ajv-keywords", "version": "1.5.1", "description": "Custom JSON-Schema keywords for ajv validator", "main": "index.js", "scripts": {"build": "node node_modules/ajv/scripts/compile-dots.js node_modules/ajv/lib keywords", "prepublish": "npm run build", "test": "npm run build && npm run eslint && npm run test-cov", "eslint": "eslint index.js keywords/*.js", "test-spec": "mocha spec/*.spec.js -R spec", "test-cov": "istanbul cover -x 'spec/**' node_modules/mocha/bin/_mocha -- spec/*.spec.js -R spec"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/ajv-keywords.git"}, "keywords": ["JSON-Schema", "ajv", "keywords"], "files": ["index.js", "keywords"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/ajv-keywords/issues"}, "homepage": "https://github.com/epoberezkin/ajv-keywords#readme", "peerDependencies": {"ajv": ">=4.10.0"}, "devDependencies": {"ajv": "^4.10.0", "ajv-pack": "^0.2.0", "chai": "^3.5.0", "coveralls": "^2.11.9", "dot": "^1.1.1", "eslint": "^3.6.0", "glob": "^7.1.1", "istanbul": "^0.4.3", "js-beautify": "^1.6.4", "json-schema-test": "^1.2.1", "mocha": "^3.0.2", "pre-commit": "^1.1.3", "uuid": "^3.0.1"}, "gitHead": "33c43a2b190c9929fe9e3e9a32a38dace146abf4", "_id": "ajv-keywords@1.5.1", "_shasum": "314dd0a4b3368fad3dfcdc54ede6171b886daf3c", "_from": ".", "_npmVersion": "2.15.9", "_nodeVersion": "4.6.1", "_npmUser": {"name": "esp", "email": "<EMAIL>"}, "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "dist": {"shasum": "314dd0a4b3368fad3dfcdc54ede6171b886daf3c", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-1.5.1.tgz", "integrity": "sha512-vuBv+fm2s6cqUyey2A7qYcvsik+GMDJsw8BARP2sDE76cqmaZVarsvHf7Vx6VJ0Xk8gLl+u3MoAPf6gKzJefeA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCfYecWZCvVTwhYy7pMYI5WiPtCnTMxGZtq+zCnWRfJSwIgZJXH+9GqIH+CHHXcAOVy647QbK6S4fdgz8gufa+vAJ4="}]}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/ajv-keywords-1.5.1.tgz_1485107517951_0.29220994655042887"}, "directories": {}}, "2.0.0-beta.2": {"name": "ajv-keywords", "version": "2.0.0-beta.2", "description": "Custom JSON-Schema keywords for Ajv validator", "main": "index.js", "scripts": {"build": "node node_modules/ajv/scripts/compile-dots.js node_modules/ajv/lib keywords", "prepublish": "npm run build", "test": "npm run build && npm run eslint && npm run test-cov", "eslint": "eslint index.js keywords/*.js", "test-spec": "mocha spec/*.spec.js -R spec", "test-cov": "istanbul cover -x 'spec/**' node_modules/mocha/bin/_mocha -- spec/*.spec.js -R spec"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/ajv-keywords.git"}, "keywords": ["JSON-Schema", "ajv", "keywords"], "files": ["index.js", "keywords"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/ajv-keywords/issues"}, "homepage": "https://github.com/epoberezkin/ajv-keywords#readme", "peerDependencies": {"ajv": ">=5.0.1-beta.0"}, "devDependencies": {"ajv": "^5.0.1-beta.0", "ajv-pack": "^0.2.0", "chai": "^3.5.0", "coveralls": "^2.11.9", "dot": "^1.1.1", "eslint": "^3.6.0", "glob": "^7.1.1", "istanbul": "^0.4.3", "js-beautify": "^1.6.4", "json-schema-test": "^1.3.0", "mocha": "^3.0.2", "pre-commit": "^1.1.3", "uuid": "^3.0.1"}, "gitHead": "062871cf76d10fb8b9aa7f0ae45f2ce7b1d61e78", "_id": "ajv-keywords@2.0.0-beta.2", "_shasum": "42ee1ed41c251b5aa4a66f5bf5a2b6a130054ae9", "_from": ".", "_npmVersion": "2.15.9", "_nodeVersion": "4.6.1", "_npmUser": {"name": "esp", "email": "<EMAIL>"}, "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "dist": {"shasum": "42ee1ed41c251b5aa4a66f5bf5a2b6a130054ae9", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-2.0.0-beta.2.tgz", "integrity": "sha512-PtUCcaVUFeGVs5I6xEUPl8oS9quz2yNzoB3ATTn3ix7GYKwR2w3UWbkirMdsKljex7V1H9PTQoQ9bYnNkfRMmA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIB9R3ehJLHW4lvGhhHF1a70IjoikSXvBOw9CKPzYgcuIAiEA3vQnTYAnDNYnfBBJsLDg0URCNpYK5elnfyvtIX4tMyY="}]}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/ajv-keywords-2.0.0-beta.2.tgz_1485108043949_0.15907507669180632"}, "directories": {}}, "2.0.1-beta.0": {"name": "ajv-keywords", "version": "2.0.1-beta.0", "description": "Custom JSON-Schema keywords for Ajv validator", "main": "index.js", "scripts": {"build": "node node_modules/ajv/scripts/compile-dots.js node_modules/ajv/lib keywords", "prepublish": "npm run build", "test": "npm run build && npm run eslint && npm run test-cov", "eslint": "eslint index.js keywords/*.js", "test-spec": "mocha spec/*.spec.js -R spec", "test-cov": "istanbul cover -x 'spec/**' node_modules/mocha/bin/_mocha -- spec/*.spec.js -R spec"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/ajv-keywords.git"}, "keywords": ["JSON-Schema", "ajv", "keywords"], "files": ["index.js", "keywords"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/ajv-keywords/issues"}, "homepage": "https://github.com/epoberezkin/ajv-keywords#readme", "peerDependencies": {"ajv": ">=5.0.2-beta.0"}, "devDependencies": {"ajv": "^5.0.2-beta.0", "ajv-pack": "^0.2.0", "chai": "^3.5.0", "coveralls": "^2.11.9", "dot": "^1.1.1", "eslint": "^3.6.0", "glob": "^7.1.1", "istanbul": "^0.4.3", "js-beautify": "^1.6.4", "json-schema-test": "^1.3.0", "mocha": "^3.0.2", "pre-commit": "^1.1.3", "uuid": "^3.0.1"}, "gitHead": "6bfcd3dc773e21e7677ba9f36de1bd90892d3886", "_id": "ajv-keywords@2.0.1-beta.0", "_shasum": "aa242a131ae362b8bf72ff9ad07b24d0918cdfab", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "esp", "email": "<EMAIL>"}, "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "dist": {"shasum": "aa242a131ae362b8bf72ff9ad07b24d0918cdfab", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-2.0.1-beta.0.tgz", "integrity": "sha512-vjFnZgUC0klJeUJ/FvIB9eA0RYlh18+AH+2mUHH/ZMF5mzxGdZpS/93JVvlVr447IicaQZPADVRv60UdofUXyQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICepThugSItbspELtrTRHjkOv91cTcGntdxg83fOMSqRAiEAgpD4i3PjsT1sPrxYxRb+xwlO3grOJ+pBu+353/kw7J8="}]}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/ajv-keywords-2.0.1-beta.0.tgz_1487025611248_0.4257139232940972"}, "directories": {}}, "2.0.1-beta.1": {"name": "ajv-keywords", "version": "2.0.1-beta.1", "description": "Custom JSON-Schema keywords for Ajv validator", "main": "index.js", "scripts": {"build": "node node_modules/ajv/scripts/compile-dots.js node_modules/ajv/lib keywords", "prepublish": "npm run build", "test": "npm run build && npm run eslint && npm run test-cov", "eslint": "eslint index.js keywords/*.js", "test-spec": "mocha spec/*.spec.js -R spec", "test-cov": "istanbul cover -x 'spec/**' node_modules/mocha/bin/_mocha -- spec/*.spec.js -R spec"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/ajv-keywords.git"}, "keywords": ["JSON-Schema", "ajv", "keywords"], "files": ["index.js", "keywords"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/ajv-keywords/issues"}, "homepage": "https://github.com/epoberezkin/ajv-keywords#readme", "peerDependencies": {"ajv": ">=5.0.3-beta.0"}, "devDependencies": {"ajv": "^5.0.3-beta.0", "ajv-pack": "^0.2.0", "chai": "^3.5.0", "coveralls": "^2.11.9", "dot": "^1.1.1", "eslint": "^3.6.0", "glob": "^7.1.1", "istanbul": "^0.4.3", "js-beautify": "^1.6.4", "json-schema-test": "^1.3.0", "mocha": "^3.0.2", "pre-commit": "^1.1.3", "uuid": "^3.0.1"}, "gitHead": "3a4f32afdf17c168b614ce16dc0ad2f759a64fd6", "_id": "ajv-keywords@2.0.1-beta.1", "_shasum": "f724f5495a1c71b09d1772560df8a4c5a4d018a5", "_from": ".", "_npmVersion": "2.15.9", "_nodeVersion": "4.6.1", "_npmUser": {"name": "esp", "email": "<EMAIL>"}, "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "dist": {"shasum": "f724f5495a1c71b09d1772560df8a4c5a4d018a5", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-2.0.1-beta.1.tgz", "integrity": "sha512-C7IqPzkKAce5JmYTfh2gGXktbigAMJZXc5qm7/yGg8FYkOPGd2uuxtcf/nRneIWx8IPb85qS4Sz+71tltzJ9LA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGxrGje5NMAlch5z1jF0Yw8vr5VYwLMtmQA590rBI/LhAiA2PqfC3wjiWHq/Ja8NP3vRXCHFxolH5MMMLVTMmD/qIA=="}]}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/ajv-keywords-2.0.1-beta.1.tgz_1488662981440_0.284121610224247"}, "directories": {}}, "2.0.1-beta.2": {"name": "ajv-keywords", "version": "2.0.1-beta.2", "description": "Custom JSON-Schema keywords for Ajv validator", "main": "index.js", "scripts": {"build": "node node_modules/ajv/scripts/compile-dots.js node_modules/ajv/lib keywords", "prepublish": "npm run build", "test": "npm run build && npm run eslint && npm run test-cov", "eslint": "eslint index.js keywords/*.js", "test-spec": "mocha spec/*.spec.js -R spec", "test-cov": "istanbul cover -x 'spec/**' node_modules/mocha/bin/_mocha -- spec/*.spec.js -R spec"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/ajv-keywords.git"}, "keywords": ["JSON-Schema", "ajv", "keywords"], "files": ["index.js", "keywords"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/ajv-keywords/issues"}, "homepage": "https://github.com/epoberezkin/ajv-keywords#readme", "peerDependencies": {"ajv": ">=5.0.4-beta.0"}, "devDependencies": {"ajv": "^5.0.4-beta.0", "ajv-pack": "^0.2.0", "chai": "^3.5.0", "coveralls": "^2.11.9", "dot": "^1.1.1", "eslint": "^3.6.0", "glob": "^7.1.1", "istanbul": "^0.4.3", "js-beautify": "^1.6.4", "json-schema-test": "^1.3.0", "mocha": "^3.0.2", "pre-commit": "^1.1.3", "uuid": "^3.0.1"}, "gitHead": "03f19f98ad123baf0dcdf5b624aa181975bf9f00", "_id": "ajv-keywords@2.0.1-beta.2", "_shasum": "b48f36d63e9334c5045bafde090db006328a0972", "_from": ".", "_npmVersion": "2.15.9", "_nodeVersion": "4.6.1", "_npmUser": {"name": "esp", "email": "<EMAIL>"}, "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "dist": {"shasum": "b48f36d63e9334c5045bafde090db006328a0972", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-2.0.1-beta.2.tgz", "integrity": "sha512-ROrphaG6XepvpZDqHy0zwZVXBR+QZ7hsFaa9Vlg6fYHmp8kKsaD2iLENu72jDbWkNNYkZ8g5c+gqoZ1j7hYaCg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCQxstHfFkq/P8SrkASg52dC1xKe8vVs5XmupD8EIEmVAIhAL4Uz3Rjfy/7KcXJq77xuTMAtX+sgExL9qZCiZ/ki081"}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/ajv-keywords-2.0.1-beta.2.tgz_1489780873913_0.7835183856077492"}, "directories": {}}, "2.0.0": {"name": "ajv-keywords", "version": "2.0.0", "description": "Custom JSON-Schema keywords for Ajv validator", "main": "index.js", "scripts": {"build": "node node_modules/ajv/scripts/compile-dots.js node_modules/ajv/lib keywords", "prepublish": "npm run build", "test": "npm run build && npm run eslint && npm run test-cov", "eslint": "eslint index.js keywords/*.js", "test-spec": "mocha spec/*.spec.js -R spec", "test-cov": "istanbul cover -x 'spec/**' node_modules/mocha/bin/_mocha -- spec/*.spec.js -R spec"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/ajv-keywords.git"}, "keywords": ["JSON-Schema", "ajv", "keywords"], "files": ["index.js", "keywords"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/ajv-keywords/issues"}, "homepage": "https://github.com/epoberezkin/ajv-keywords#readme", "peerDependencies": {"ajv": ">=5.0.0"}, "devDependencies": {"ajv": "^5.0.0", "ajv-pack": "^0.2.0", "chai": "^3.5.0", "coveralls": "^2.11.9", "dot": "^1.1.1", "eslint": "^3.6.0", "glob": "^7.1.1", "istanbul": "^0.4.3", "js-beautify": "^1.6.4", "json-schema-test": "^1.3.0", "mocha": "^3.0.2", "pre-commit": "^1.1.3", "uuid": "^3.0.1"}, "gitHead": "da2257de9f437f7f1248aadd401f6a44b49e4824", "_id": "ajv-keywords@2.0.0", "_shasum": "a37d02f845b6f52569804164270b24cb6c6cee61", "_from": ".", "_npmVersion": "2.15.9", "_nodeVersion": "4.6.1", "_npmUser": {"name": "esp", "email": "<EMAIL>"}, "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "dist": {"shasum": "a37d02f845b6f52569804164270b24cb6c6cee61", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-2.0.0.tgz", "integrity": "sha512-oRMPh0lQFsBYDa5/srclbFUxEUtflMIz1Evpx/gSEACwBs+bDCRcEtrgTOKYU5PGSk6qbpJFcxkqilDPVM1lcA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFE/6hOzA2JaQynPXUKzMChe3azgDyT+rjSK9yoNxMEzAiEAhYw0WAIkoiVys76zYE1kQGl3PbhHFyHd3l9jCfKSc7Y="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/ajv-keywords-2.0.0.tgz_1492441766352_0.30086178914643824"}, "directories": {}}, "2.1.0": {"name": "ajv-keywords", "version": "2.1.0", "description": "Custom JSON-Schema keywords for Ajv validator", "main": "index.js", "scripts": {"build": "node node_modules/ajv/scripts/compile-dots.js node_modules/ajv/lib keywords", "prepublish": "npm run build", "test": "npm run build && npm run eslint && npm run test-cov", "eslint": "eslint index.js keywords/*.js", "test-spec": "mocha spec/*.spec.js -R spec", "test-cov": "istanbul cover -x 'spec/**' node_modules/mocha/bin/_mocha -- spec/*.spec.js -R spec"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/ajv-keywords.git"}, "keywords": ["JSON-Schema", "ajv", "keywords"], "files": ["index.js", "keywords"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/ajv-keywords/issues"}, "homepage": "https://github.com/epoberezkin/ajv-keywords#readme", "peerDependencies": {"ajv": ">=5.0.0"}, "devDependencies": {"ajv": "^5.0.0", "ajv-pack": "^0.3.0", "chai": "^3.5.0", "coveralls": "^2.11.9", "dot": "^1.1.1", "eslint": "^3.6.0", "glob": "^7.1.1", "istanbul": "^0.4.3", "js-beautify": "^1.6.4", "json-schema-test": "^1.3.0", "mocha": "^3.0.2", "pre-commit": "^1.1.3", "uuid": "^3.0.1"}, "gitHead": "19fdbe34e9d9067f19142a6e94815b33ff66edcd", "_id": "ajv-keywords@2.1.0", "_shasum": "a296e17f7bfae7c1ce4f7e0de53d29cb32162df0", "_from": ".", "_npmVersion": "2.15.9", "_nodeVersion": "4.6.1", "_npmUser": {"name": "esp", "email": "<EMAIL>"}, "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "dist": {"shasum": "a296e17f7bfae7c1ce4f7e0de53d29cb32162df0", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-2.1.0.tgz", "integrity": "sha512-UVqQ2fa3ELt80oJ/QctN+25n/ccCQf+YXZxYyj2nO1wiIZ58poVAuWpuIkQ//RUqNr/nWK4Byqflyn1omwXlyQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC8TGMoGEMK7RpubL9UN+4GDzgvlPmQr8OcuWteuo4I0gIhALmrG9HE3XWPB94adJbyAeL32SOfuLM4ip7ycT+i++K8"}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ajv-keywords-2.1.0.tgz_1496088346630_0.025764777790755033"}, "directories": {}}, "2.1.1": {"name": "ajv-keywords", "version": "2.1.1", "description": "Custom JSON-Schema keywords for Ajv validator", "main": "index.js", "scripts": {"build": "node node_modules/ajv/scripts/compile-dots.js node_modules/ajv/lib keywords", "prepublish": "npm run build", "test": "npm run build && npm run eslint && npm run test-cov", "eslint": "eslint index.js keywords/*.js", "test-spec": "mocha spec/*.spec.js -R spec", "test-cov": "istanbul cover -x 'spec/**' node_modules/mocha/bin/_mocha -- spec/*.spec.js -R spec"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/ajv-keywords.git"}, "keywords": ["JSON-Schema", "ajv", "keywords"], "files": ["index.js", "keywords"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/ajv-keywords/issues"}, "homepage": "https://github.com/epoberezkin/ajv-keywords#readme", "peerDependencies": {"ajv": "^5.0.0"}, "devDependencies": {"ajv": "^5.0.0", "ajv-pack": "^0.3.0", "chai": "^4.0.2", "coveralls": "^3.0.0", "dot": "^1.1.1", "eslint": "^4.9.0", "glob": "^7.1.1", "istanbul": "^0.4.3", "js-beautify": "^1.7.4", "json-schema-test": "^1.3.0", "mocha": "^4.0.0", "pre-commit": "^1.1.3", "uuid": "^3.0.1"}, "gitHead": "535a42399435b56d8e68b1ebce3375de78c878b7", "_id": "ajv-keywords@2.1.1", "_shasum": "617997fc5f60576894c435f940d819e135b80762", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "esp", "email": "<EMAIL>"}, "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "dist": {"shasum": "617997fc5f60576894c435f940d819e135b80762", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-2.1.1.tgz", "integrity": "sha512-ZFztHzVRdGLAzJmpUT9LNFLe1YiVOEylcaNpEutM26PVTCtOD919IMfD01CgbRouB42Dd9atjx1HseC15DgOZA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDEAt1qnFoY4JjsYrbwwEKPfNuixYZQGKI1Kcuz4Pzc2wIgFY0lyHdPDGQmzjk6ycMMYx1i3/uBNiK6QiWu0/DALjk="}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ajv-keywords-2.1.1.tgz_1509278419470_0.5681772974785417"}, "directories": {}}, "3.0.0-beta.0": {"name": "ajv-keywords", "version": "3.0.0-beta.0", "description": "Custom JSON-Schema keywords for Ajv validator", "main": "index.js", "scripts": {"build": "node node_modules/ajv/scripts/compile-dots.js node_modules/ajv/lib keywords", "prepublish": "npm run build", "test": "npm run build && npm run eslint && npm run test-cov", "eslint": "eslint index.js keywords/*.js", "test-spec": "mocha spec/*.spec.js -R spec", "test-cov": "istanbul cover -x 'spec/**' node_modules/mocha/bin/_mocha -- spec/*.spec.js -R spec"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/ajv-keywords.git"}, "keywords": ["JSON-Schema", "ajv", "keywords"], "files": ["index.js", "keywords"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/ajv-keywords/issues"}, "homepage": "https://github.com/epoberezkin/ajv-keywords#readme", "peerDependencies": {"ajv": ">=5.0.0"}, "devDependencies": {"ajv": "^5.0.0", "ajv-pack": "^0.3.0", "chai": "^4.0.2", "coveralls": "^3.0.0", "dot": "^1.1.1", "eslint": "^4.9.0", "glob": "^7.1.1", "istanbul": "^0.4.3", "js-beautify": "^1.7.4", "json-schema-test": "^1.3.0", "mocha": "^4.0.0", "pre-commit": "^1.1.3", "uuid": "^3.0.1"}, "gitHead": "506b850aed7eba872c7e877b5d0e3671366b03d2", "_id": "ajv-keywords@3.0.0-beta.0", "_shasum": "0acee9de32190b79a2fea9a0f2345838d4752525", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "esp", "email": "<EMAIL>"}, "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "dist": {"shasum": "0acee9de32190b79a2fea9a0f2345838d4752525", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-3.0.0-beta.0.tgz", "integrity": "sha512-9FKPiAOvGv9fbMgdF6A35rFgyn9b7bXBkgNk0vyKpk8Y7apGo2uC9wOymF2joYsafw9QK7OpHUjabR+Q/35f/A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD0uDveEF1bMUmysml8M0qJeH37kd33Py4W2diG6k99CwIhANDbKzMVzE0pO7oXypfgxtkEeKa649XJ1CAVQyMWtuFG"}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ajv-keywords-3.0.0-beta.0.tgz_1509278471241_0.7103001922369003"}, "directories": {}}, "3.0.0": {"name": "ajv-keywords", "version": "3.0.0", "description": "Custom JSON-Schema keywords for Ajv validator", "main": "index.js", "scripts": {"build": "node node_modules/ajv/scripts/compile-dots.js node_modules/ajv/lib keywords", "prepublish": "npm run build", "test": "npm run build && npm run eslint && npm run test-cov", "eslint": "eslint index.js keywords/*.js", "test-spec": "mocha spec/*.spec.js -R spec", "test-cov": "istanbul cover -x 'spec/**' node_modules/mocha/bin/_mocha -- spec/*.spec.js -R spec"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/ajv-keywords.git"}, "keywords": ["JSON-Schema", "ajv", "keywords"], "files": ["index.js", "keywords"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/ajv-keywords/issues"}, "homepage": "https://github.com/epoberezkin/ajv-keywords#readme", "peerDependencies": {"ajv": "^6.0.0"}, "devDependencies": {"ajv": "^6.0.0", "ajv-pack": "^0.3.0", "chai": "^4.0.2", "coveralls": "^3.0.0", "dot": "^1.1.1", "eslint": "^4.9.0", "glob": "^7.1.1", "istanbul": "^0.4.3", "js-beautify": "^1.7.4", "json-schema-test": "^2.0.0", "mocha": "^4.0.0", "pre-commit": "^1.1.3", "uuid": "^3.0.1"}, "gitHead": "2ba1418206710e1ee178c33f0cfda606b908476e", "_id": "ajv-keywords@3.0.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.4", "_npmUser": {"name": "esp", "email": "<EMAIL>"}, "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "dist": {"integrity": "sha512-uMRktPTgqPL+jx4aRg29iv9+jNPDH3NGw4tn/dKONgwd53XPdmZe433n4eyjsj92v0Koe0qqHwAodZCzIriwrQ==", "shasum": "d1c2d845e2664dd3b95551d0cedc7675d6d1cec3", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-3.0.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGfP7j46tqBazJ6qSfdjMpFK05K9nsw0SII1k086z7NLAiEAkKfq+pSp9lFfTuGPA0OXoSk/ztRSSTiUUiHBL6f+Xoc="}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ajv-keywords-3.0.0.tgz_1515349055682_0.41996692842803895"}, "directories": {}}, "3.1.0": {"name": "ajv-keywords", "version": "3.1.0", "description": "Custom JSON-Schema keywords for Ajv validator", "main": "index.js", "scripts": {"build": "node node_modules/ajv/scripts/compile-dots.js node_modules/ajv/lib keywords", "prepublish": "npm run build", "test": "npm run build && npm run eslint && npm run test-cov", "eslint": "eslint index.js keywords/*.js", "test-spec": "mocha spec/*.spec.js -R spec", "test-cov": "istanbul cover -x 'spec/**' node_modules/mocha/bin/_mocha -- spec/*.spec.js -R spec"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/ajv-keywords.git"}, "keywords": ["JSON-Schema", "ajv", "keywords"], "files": ["index.js", "keywords"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/ajv-keywords/issues"}, "homepage": "https://github.com/epoberezkin/ajv-keywords#readme", "peerDependencies": {"ajv": "^6.0.0"}, "devDependencies": {"ajv": "^6.0.0", "ajv-pack": "^0.3.0", "chai": "^4.0.2", "coveralls": "^3.0.0", "dot": "^1.1.1", "eslint": "^4.9.0", "glob": "^7.1.1", "istanbul": "^0.4.3", "js-beautify": "^1.7.4", "json-schema-test": "^2.0.0", "mocha": "^4.0.0", "pre-commit": "^1.1.3", "uuid": "^3.0.1"}, "gitHead": "5f9eccc2e2b95745f659ef1abf5488f70da496f2", "_id": "ajv-keywords@3.1.0", "_shasum": "ac2b27939c543e95d2c06e7f7f5c27be4aa543be", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.10.1", "_npmUser": {"name": "esp", "email": "<EMAIL>"}, "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "dist": {"shasum": "ac2b27939c543e95d2c06e7f7f5c27be4aa543be", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-3.1.0.tgz", "integrity": "sha512-CRpBPN8yJY/ySBYc93INtFdQNctYjxlCvOmB8zRKEaIGk4fvawQiNAGiPOv3eWJ6NBmotfTJi/06TXfEKw0hVg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG6bqvZmdg9OPGz2BMF4ijaFvKOYdKPxDnjrbUxCtus+AiA/WW7xxn8GDTyZ85VnHu6yBzEEPAMT9/MfW+qdZv+3DQ=="}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ajv-keywords-3.1.0.tgz_1517430918912_0.6375202517956495"}, "directories": {}}, "3.2.0": {"name": "ajv-keywords", "version": "3.2.0", "description": "Custom JSON-Schema keywords for Ajv validator", "main": "index.js", "scripts": {"build": "node node_modules/ajv/scripts/compile-dots.js node_modules/ajv/lib keywords", "prepublish": "npm run build", "test": "npm run build && npm run eslint && npm run test-cov", "eslint": "eslint index.js keywords/*.js", "test-spec": "mocha spec/*.spec.js -R spec", "test-cov": "istanbul cover -x 'spec/**' node_modules/mocha/bin/_mocha -- spec/*.spec.js -R spec"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/ajv-keywords.git"}, "keywords": ["JSON-Schema", "ajv", "keywords"], "files": ["index.js", "keywords"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/ajv-keywords/issues"}, "homepage": "https://github.com/epoberezkin/ajv-keywords#readme", "peerDependencies": {"ajv": "^6.0.0"}, "devDependencies": {"ajv": "^6.0.0", "ajv-pack": "^0.3.0", "chai": "^4.0.2", "coveralls": "^3.0.0", "dot": "^1.1.1", "eslint": "^4.9.0", "glob": "^7.1.1", "istanbul": "^0.4.3", "js-beautify": "^1.7.4", "json-schema-test": "^2.0.0", "mocha": "^5.0.0", "pre-commit": "^1.1.3", "uuid": "^3.0.1"}, "gitHead": "97c8570371708f9a9e706f76073a68debb438bc5", "_id": "ajv-keywords@3.2.0", "_shasum": "e86b819c602cf8821ad637413698f1dec021847a", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.10.1", "_npmUser": {"name": "esp", "email": "<EMAIL>"}, "dist": {"shasum": "e86b819c602cf8821ad637413698f1dec021847a", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-3.2.0.tgz", "fileCount": 29, "unpackedSize": 65030, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa5D8gCRA9TVsSAnZWagAA0dgP/3wEqJkIgz8c7N8eQSX+\nV7gTjqtoeHLh8Ne7MJyAlJ7eKabvEVwoOjeqYnVEoh8htsE6IZyHg9TNFFJv\nS3IZCqf77RCNpl/rWDDyHbfbjPv7Cnytiz0RdejwORV6mQkYvzW4Wst0V7it\nIwOwpUg/AiOmU55z9kEKkufSFb7CdTm07pTyG6X+ieHn2Tf94b4wa8jjW7Sp\nxj4x2R7GHlDzCxTrm2LGjJ1mJRky63BLtli6/lUI/wxW4ZYfnxssZFtjz5X8\n9WZAdhnWIcvTTRvRH50MuhtPxvrZw3LxkPVckqPZUYBUmc8VkywJWAJWOSuI\nfPuRrxZ6MTIp0VIrIwsy4+Mg2bTBWHJ26JT6hh7lqPQYNTLGUzzIuYPixvxC\n6CGEzeC0NiojSpXbj+WMHXdSbSe7Wpnx4ghXbc/XE5huJgZK2jmeJHZqTtjq\n/HX68q5zQy+TUbK8eYJJ9bEkQy6tQvCkrYIep837QFcmEUnGutCwenRr9ocf\n6ro37wc+MLuB7hzrEQ09GvbLzC1P48HNi/xMt1pGdyRLg4m+PLt3vwoTJONd\nfBGwxN1a+qOTL7TXCsHL3PLmlYMvxTGzSDW0f0IQVP8XHuqdrjEtcrt4O0ZY\neuS6z4L0rmA/LgZAmuS81t4usbEgwgJE2K30iiFfQhDn2NbGT4jNPXujMDBE\ny6U9\r\n=GXSy\r\n-----END PGP SIGNATURE-----\r\n", "integrity": "sha512-UPjC/WMTkkh8SoNBayj3ZGsPLYOelXyEDThWIRymcvSowMhXORI5bBgm/3u2mz5mi50CFUhGsMy88USWUl4txw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEgRqY5nYcDZaQaQ4l/0Dvusdh07GYglYdtEN1paGAwqAiEA9FpR8va9MD5RXODB+BmpKisJlHGOy3F+ZbDZHXnABw4="}]}, "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ajv-keywords_3.2.0_1524907806828_0.6143512626006866"}, "_hasShrinkwrap": false}, "3.3.0": {"name": "ajv-keywords", "version": "3.3.0", "description": "Custom JSON-Schema keywords for Ajv validator", "main": "index.js", "scripts": {"build": "node node_modules/ajv/scripts/compile-dots.js node_modules/ajv/lib keywords", "prepublish": "npm run build", "test": "npm run build && npm run eslint && npm run test-cov", "eslint": "eslint index.js keywords/*.js", "test-spec": "mocha spec/*.spec.js -R spec", "test-cov": "istanbul cover -x 'spec/**' node_modules/mocha/bin/_mocha -- spec/*.spec.js -R spec"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/ajv-keywords.git"}, "keywords": ["JSON-Schema", "ajv", "keywords"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/ajv-keywords/issues"}, "homepage": "https://github.com/epoberezkin/ajv-keywords#readme", "peerDependencies": {"ajv": "^6.0.0"}, "devDependencies": {"ajv": "^6.0.0", "ajv-pack": "^0.3.0", "chai": "^4.0.2", "coveralls": "^3.0.0", "dot": "^1.1.1", "eslint": "^5.0.0", "glob": "^7.1.1", "istanbul": "^0.4.3", "js-beautify": "^1.7.4", "json-schema-test": "^2.0.0", "mocha": "^5.0.0", "pre-commit": "^1.1.3", "uuid": "^3.0.1"}, "gitHead": "10647aa790a0f74098b332fab7ede4e31162dec2", "_id": "ajv-keywords@3.3.0", "_npmVersion": "5.6.0", "_nodeVersion": "10.0.0", "_npmUser": {"name": "esp", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-CMzN9S62ZOO4sA/mJZIO4S++ZM7KFWzH3PPWkveLhy4OZ9i1/VatgwWMD46w/XbGCBy7Ye0gCk+Za6mmyfKK7g==", "shasum": "cb6499da9b83177af8bc1732b2f0a1a1a3aacf8c", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-3.3.0.tgz", "fileCount": 29, "unpackedSize": 66623, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcTYA3CRA9TVsSAnZWagAA5FwQAID/Xlz4cQ6g73jBf5GR\nL5mp9aBGULDA9k/ArzFrDmAxzNG9kM/TprIFtewGYQf0hOy1FMJxLTN3s9a0\nLX8lzZzl4pKSHC9Vfwhiu/bYFXhzmHTI/3rqqdbBxVmAeBXLNg39Yhffitn7\n0dNrow0cLxd4a+TXj3Hmqq8XfzasSbrvErm98wlsEBgWEj8aMsyfl0LzDjj/\nM2EwuP1Evb/wBJpuTufT24W8uCuHR/SY0EBzku0RucZhjRw4WH+Vi9pgzA19\nCQKwq0R14OlELQ8la7R5f1x4SsAVaKnmnzLAcAG1DXSAJSfgic6PERTJHeP0\nwyRg+oSD6/5lnmNU3Ca+VOB6w6uWx5418QZ5Ph06IdpKJWY2kKcutldHZclv\nCiOB+FTPo4tMhL8gFq8wWLSOo0MeSCLkax9szNAPZMGTzobUiOmCxCLLo1tF\n/hCoxuFfXyy8YpspLrHEFiakpUZ8PO5uZQhUC49le92tK/kiU6DQwTXsSMNp\nwA6D0r/7WSraTdXu1nlaQvoRkkKy8rQVI9a40b6YwntZgI7x1+wxlqdPe5Uf\nKlLBALhy+9sLrfJ6CmsDPt7zWRygece8OvusWYGPZAlIaHouKXo9XzDTQqtj\ni+yovlqVip0kvdojLFleUUKps7Mnz351WD/oOqjnxFlanTr3t+HBQg/3/d0E\n+hRK\r\n=QBp9\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICa5l5S/0Q4/ChYBmOHvGHOxu/XjKfwUZ+h+TAZcAzaxAiEAkiWP0a6WeuZlhKVBHbfxYWAYVUY1DzRSSJp837ZIOMo="}]}, "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ajv-keywords_3.3.0_1548582966668_0.3989718573015928"}, "_hasShrinkwrap": false}, "3.4.0": {"name": "ajv-keywords", "version": "3.4.0", "description": "Custom JSON-Schema keywords for Ajv validator", "main": "index.js", "scripts": {"build": "node node_modules/ajv/scripts/compile-dots.js node_modules/ajv/lib keywords", "prepublish": "npm run build", "test": "npm run build && npm run eslint && npm run test-cov", "eslint": "eslint index.js keywords/*.js spec", "test-spec": "mocha spec/*.spec.js -R spec", "test-cov": "istanbul cover -x 'spec/**' node_modules/mocha/bin/_mocha -- spec/*.spec.js -R spec"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/ajv-keywords.git"}, "keywords": ["JSON-Schema", "ajv", "keywords"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/ajv-keywords/issues"}, "homepage": "https://github.com/epoberezkin/ajv-keywords#readme", "peerDependencies": {"ajv": "^6.9.1"}, "devDependencies": {"ajv": "^6.9.1", "ajv-pack": "^0.3.0", "chai": "^4.2.0", "coveralls": "^3.0.2", "dot": "^1.1.1", "eslint": "^5.0.0", "glob": "^7.1.3", "istanbul": "^0.4.3", "js-beautify": "^1.8.9", "json-schema-test": "^2.0.0", "mocha": "^5.2.0", "pre-commit": "^1.1.3", "uuid": "^3.3.2"}, "gitHead": "1ec78c7c01ad29d9ff650536b8a722f3cfa73229", "_id": "ajv-keywords@3.4.0", "_npmVersion": "5.6.0", "_nodeVersion": "10.0.0", "_npmUser": {"name": "esp", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-aUjdRFISbuFOl0EIZc+9e4FfZp0bDZgAdOOf30bJmw8VM9v84SHyVyxDfbWxpGYbdZD/9XoKxfHVNmxPkhwyGw==", "shasum": "4b831e7b531415a7cc518cd404e73f6193c6349d", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-3.4.0.tgz", "fileCount": 31, "unpackedSize": 69839, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcYEXQCRA9TVsSAnZWagAAQeAP/ja42ryFTjcfufvCZU6y\nviBfm5K7aGpfl1LbpWEwKHs3q8XwE0oqF1cs8Uummz15No2nuP/ZjzNJ9sn0\nAoH8m1JBlplPL8Md34FJJU22sch6mMSTeiVi24vr17jcc8ZE2ADshygMy3bQ\njXhXzw8JV4CoYwZFkuZtbJoOMpy8dB3IChu5QKNiTCu9vJx5sHoG1mvJeuOT\nDFH+beHbr9kmtet7afgB73IByfYsY/yvDkzwTGygVK/OUvun+GglrpieqD1k\n5FAhfEtnRCIcMsM1T8+IPv+Kt4org3zSppgIfAvr36sN1/sDyPTe5bfx5HlN\nTu4+oBMi4fqP4qiJfh0nnR4jRbNR9QG/8fcxI8GbIxHyauq50s/UuG+w8xSz\nJqrLXJjtJgyJf8bfc6k1jkH8TT6/c2c1vKYTdGYqs0Q2lIsjDw6W+zLhQf9W\nCBBu8AWpeAzvBOnWC9W8lE7DXKLAu/cncfBZygjL7EYmGCGJR8wdarYA22+I\n6N35T2CL0xZNrJ1joxSnTLZpy9YO6zA6WCrfo+zdZavsZKWHCXskElOaVLEf\n6w/gL9khv+o1WZGjCs2gDI/m9wl85ZOTm3JyHTwhVPzlvI8Lt6BXMAxokFkm\nTqcasLdYrHqaMYhJLHqGpixUGVm/nvVcjBkFbCg5t/rGArUHbxul9wC9us91\n/aZV\r\n=NIo5\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD3GiCMpaXApMgqBML3xyXuZUizl2DQuIDZHNDdpK6o5AIhAKwdBPpfv0rC9CsJpCKURO9L0J+7YP7oFxBr3vKn7ZLR"}]}, "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ajv-keywords_3.4.0_1549813199872_0.8232900085799115"}, "_hasShrinkwrap": false}, "3.4.1": {"name": "ajv-keywords", "version": "3.4.1", "description": "Custom JSON-Schema keywords for Ajv validator", "main": "index.js", "scripts": {"build": "node node_modules/ajv/scripts/compile-dots.js node_modules/ajv/lib keywords", "prepublish": "npm run build", "test": "npm run build && npm run eslint && npm run test-cov", "eslint": "eslint index.js keywords/*.js spec", "test-spec": "mocha spec/*.spec.js -R spec", "test-cov": "istanbul cover -x 'spec/**' node_modules/mocha/bin/_mocha -- spec/*.spec.js -R spec"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/ajv-keywords.git"}, "keywords": ["JSON-Schema", "ajv", "keywords"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/ajv-keywords/issues"}, "homepage": "https://github.com/epoberezkin/ajv-keywords#readme", "peerDependencies": {"ajv": "^6.9.1"}, "devDependencies": {"ajv": "^6.9.1", "ajv-pack": "^0.3.0", "chai": "^4.2.0", "coveralls": "^3.0.2", "dot": "^1.1.1", "eslint": "^6.0.0", "glob": "^7.1.3", "istanbul": "^0.4.3", "js-beautify": "^1.8.9", "json-schema-test": "^2.0.0", "mocha": "^6.0.0", "pre-commit": "^1.1.3", "uuid": "^3.3.2"}, "gitHead": "daa7d9159fda5adf94b380a009b8d5ff09a21b10", "_id": "ajv-keywords@3.4.1", "_npmVersion": "5.6.0", "_nodeVersion": "10.0.0", "_npmUser": {"name": "esp", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-RO1ibKvd27e6FEShVFfPALuHI3WjSVNeK5FIsmme/LYRNxjKuNj+Dt7bucLa6NdSv3JcVTyMlm9kGR84z1XpaQ==", "shasum": "ef916e271c64ac12171fd8384eaae6b2345854da", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-3.4.1.tgz", "fileCount": 32, "unpackedSize": 71883, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdIPsXCRA9TVsSAnZWagAAaBcQAIJ5ZqQTcNxoI0cVtkbT\n2Zj1e66mkfZ2Bm9k5fCwHcHMQNKqpl/aElfETYkzVZAwOlwQfI1XrxncNWqF\nxwRpYHZDOCLPnanfPOc7jF6wJkspQrN/iE4ghI8BxpGKqjVbYBOtSxd85WEL\nNbR8xXTepBK4A9Wd7SYot3M0rcxrUuMN920r4aAvYQ9WhCSMFtvhFlpFp4Sj\nknc06OKrmSyacSFgijUSYOfzuzumsw1h3GDFChDfKSbOw2lFw0pIEAaK+Pbt\nlK98HEQHrFGByATWSn4l4kmPcMU7gH3zAARJbye1BaIGSGB3ec6a6TMw2t++\nPkuvVK7813GDT1owgMOrsQvwLSAEBHTywLL3IKHFJHz+6Q4Y0x0etdgpXy9H\nETCGQHGyrkb3oEgNCFPNTeU9a0+vDWr8fmXfJI7Rhb3rv43Hu5UyO84gYNtA\n08LnqIG64XqDGen0M64lL11j5Dy1Fkg+H3OxGdUiWhKtj0mFzVi+W+XkVJdX\nv2HTtq543TOaoMrzoPP0H/OgMAiTOCuBzAPAcvRn3dtbDyOsh/LFgWdeeEdL\ny7xSnHlTqxEwHESSp2vDouBV0Xpx8pZGyADF9cbVWs4iE5qIjfyHyXoJTIvN\nGMrdlDrmH56MAiqlhK96Wv6hw3bZsaOVz/tg51fn0Kzy8nTEyDjavXJxrad+\noZ40\r\n=eDCH\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAXcq3NsMOrqNet+xiqqzjbnN5znJkrcCmMLqJSd+NyuAiAwT/ogRLL1pMlEbwjyLbY9lQlrkhC3EROxUp6SZhVL0g=="}]}, "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ajv-keywords_3.4.1_1562442518740_0.9436686733094879"}, "_hasShrinkwrap": false}, "3.5.0": {"name": "ajv-keywords", "version": "3.5.0", "description": "Custom JSON-Schema keywords for Ajv validator", "main": "index.js", "typings": "ajv-keywords.d.ts", "scripts": {"build": "node node_modules/ajv/scripts/compile-dots.js node_modules/ajv/lib keywords", "prepublish": "npm run build", "test": "npm run build && npm run eslint && npm run test-cov", "eslint": "eslint index.js keywords/*.js spec", "test-spec": "mocha spec/*.spec.js -R spec", "test-cov": "istanbul cover -x 'spec/**' node_modules/mocha/bin/_mocha -- spec/*.spec.js -R spec"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/ajv-keywords.git"}, "keywords": ["JSON-Schema", "ajv", "keywords"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/ajv-keywords/issues"}, "homepage": "https://github.com/epoberezkin/ajv-keywords#readme", "peerDependencies": {"ajv": "^6.9.1"}, "devDependencies": {"ajv": "^6.9.1", "ajv-pack": "^0.3.0", "chai": "^4.2.0", "coveralls": "^3.0.2", "dot": "^1.1.1", "eslint": "^7.2.0", "glob": "^7.1.3", "istanbul": "^0.4.3", "js-beautify": "^1.8.9", "json-schema-test": "^2.0.0", "mocha": "^8.0.1", "pre-commit": "^1.1.3", "uuid": "^8.1.0"}, "gitHead": "3b67320b7494325ac6891d2caec028658bab5f7d", "_id": "ajv-keywords@3.5.0", "_nodeVersion": "14.4.0", "_npmVersion": "6.14.5", "dist": {"integrity": "sha512-eyoaac3btgU8eJlvh01En8OCKzRqlLe2G5jDsCr3RiE2uLGMEEB1aaGwVVpwR8M95956tGH6R+9edC++OvzaVw==", "shasum": "5c894537098785926d71e696114a53ce768ed773", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-3.5.0.tgz", "fileCount": 32, "unpackedSize": 72698, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe7M8eCRA9TVsSAnZWagAAhEEP/2vHbksny97ge7XgHe+O\naESf3r5hD6GZcfhrCSvjnKbwbiav2Nyjw2SFuEiRkcXSZlX6/HsW6kDlEdG0\nVtCxEzPqYqst/IBGbmpJbRlOaK+SCvtLKgnYgGk8OUbeCi6UNf3Ce8i6OHF1\nnmPlOp1i/L+c6m7tRyKx7o7t+q53XlGkEuG0xXlPZ/WEkpS7Su8CYg8vJd5J\nq02QJOSNW4MK5OQqm15JNf5iMxZEIFfgglfep8FoLFRcd9RZN4fVyXXGUvoP\nI4clIp/5/JKjycse/kuKwQPkMGjGPqANKDVGlUmbnzmLc/mxBs/GHMlDTvsH\nl+AepETZgD8MncMn0DRE3EvbMrd6UnS8YtYOYj2qjD9JIQkjzDCbe9QmYXdt\nLHUoLZ+9o5c5/nFfIqGHESDp7Fud1edxtYGPv1hy8cpsEvFeHQAqcVQ6kwHl\nxEcHaI6ZJ1m8PtqfDMAgwk/OVUqdoPRRXMbV3y2sG5cPxakVVKR6NrPylFfx\nsse11jNBfqPxZGMvwKnCqtq+Llhv0ck/2CD5hW0h2VmcVbh9NNt2MSMQKcmU\n9spZr4Pah5sH+WzSGPhjK0xrP0WIfZu5DoFVm5WfEfsTIqwiXcDT84m/DYJX\nMXHmjLFXuH+oj2PV3PzNEYlcoT+3EwbAoHZvxEZPlI4dvVjJyGwOd7luVe7C\nUTWn\r\n=k9xg\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDl45aXxtpVkUK4St+DPiDj7eFdx68Nfb3PUtyOwwuWCAiARiGGdL2kIQfFCtlEcU9cKbNfOnQOLFaM/cOfKIKKytA=="}]}, "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "_npmUser": {"name": "esp", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ajv-keywords_3.5.0_1592577822248_0.19134927788556677"}, "_hasShrinkwrap": false}, "3.5.1": {"name": "ajv-keywords", "version": "3.5.1", "description": "Custom JSON-Schema keywords for Ajv validator", "main": "index.js", "typings": "ajv-keywords.d.ts", "scripts": {"build": "node node_modules/ajv/scripts/compile-dots.js node_modules/ajv/lib keywords", "prepublish": "npm run build", "test": "npm run build && npm run eslint && npm run test-cov", "eslint": "eslint index.js keywords/*.js spec", "test-spec": "mocha spec/*.spec.js -R spec", "test-cov": "istanbul cover -x 'spec/**' node_modules/mocha/bin/_mocha -- spec/*.spec.js -R spec"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/ajv-keywords.git"}, "keywords": ["JSON-Schema", "ajv", "keywords"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/ajv-keywords/issues"}, "homepage": "https://github.com/epoberezkin/ajv-keywords#readme", "peerDependencies": {"ajv": "^6.9.1"}, "devDependencies": {"ajv": "^6.9.1", "ajv-pack": "^0.3.0", "chai": "^4.2.0", "coveralls": "^3.0.2", "dot": "^1.1.1", "eslint": "^7.2.0", "glob": "^7.1.3", "istanbul": "^0.4.3", "js-beautify": "^1.8.9", "json-schema-test": "^2.0.0", "mocha": "^8.0.1", "pre-commit": "^1.1.3", "uuid": "^8.1.0"}, "gitHead": "dfce7d2cd3a7d73b05182ac31681912e572ab780", "_id": "ajv-keywords@3.5.1", "_nodeVersion": "14.4.0", "_npmVersion": "6.14.5", "dist": {"integrity": "sha512-KWcq3xN8fDjSB+IMoh2VaXVhRI0BBGxoYp3rx7Pkb6z0cFjYR9Q9l4yZqqals0/zsioCmocC5H6UvsGD4MoIBA==", "shasum": "b83ca89c5d42d69031f424cad49aada0236c6957", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-3.5.1.tgz", "fileCount": 32, "unpackedSize": 72711, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfBN+nCRA9TVsSAnZWagAAlo0P/3DPrCEJ0oqskSkuWfQw\nfhVI1qwC4mtiM8/LkpJqIVjnTiFSU53qRq+DAXfw4XkaIPXEYoDvjaxbbdpZ\nROo4bmlqy31X8KCVQ3pV19Hg3+GtMUGDPHoYX6ptvxI8Q1wVx6K8AgRYcdHU\niY9+KcMu8exQs8NMhGLOUMICDgmDquIPDcLQCP55IEhElmkKejN0DL8yJ97m\nxD1Go9TjxMuH5uj5R0f/oq5mO7ZoEEg/bIY4vvnbg6rARENlMsh9hbwmSle2\n/voUCeuR+JyXhWsZO+naIa7Ej/F+OVCWTAu5+w2rby38UQhgrGyPPu7aPuVX\nrX4XVXn76qpMZmJ6EG4d86J34oEmFrcSNGcQrojRN4XY+45KPAkc/JZHzu2H\n4Gz2Y1EgP13f5rdQw2eDLQBoMKSaS26HCfGXD3eNE3C+CXPI3dEzOZmOVkng\ncaFCLpA2MM3rSAnGZCH6cq9eYSTlLPfdATYsUI0VehOHO+a1mULETOOb6+Bg\nWuktkoHTwsN6Sqb8658ngRWzAN9nhyjkvpU7MgN0i3NdgqxmF0x0yj46shxb\nVOKk9ZqIHYtunvTInPVTaOic5/FKqjFmMWBEnlGTFv2YkfFyXQYvi0i7OT13\nuyViWH0ZGNbUcNlDvhXEWUVeUid+t0mHbFnT7Svr2xckkP3E49aRSpH5zUBG\nuVHL\r\n=5yto\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDVULGyMDfFVDSad2NaZIX2N9qPe/8o4SWx5rp/u7pgLwIhAOTZEJlIjnC/ymG/zhqpd6v2N8laIwMkxbqHCl3cr13X"}]}, "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "_npmUser": {"name": "esp", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ajv-keywords_3.5.1_1594154919074_0.5319144046472377"}, "_hasShrinkwrap": false}, "3.5.2": {"name": "ajv-keywords", "version": "3.5.2", "description": "Custom JSON-Schema keywords for Ajv validator", "main": "index.js", "typings": "ajv-keywords.d.ts", "scripts": {"build": "node node_modules/ajv/scripts/compile-dots.js node_modules/ajv/lib keywords", "prepublish": "npm run build", "test": "npm run build && npm run eslint && npm run test-cov", "eslint": "eslint index.js keywords/*.js spec", "test-spec": "mocha spec/*.spec.js -R spec", "test-cov": "istanbul cover -x 'spec/**' node_modules/mocha/bin/_mocha -- spec/*.spec.js -R spec"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/ajv-keywords.git"}, "keywords": ["JSON-Schema", "ajv", "keywords"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/ajv-keywords/issues"}, "homepage": "https://github.com/epoberezkin/ajv-keywords#readme", "peerDependencies": {"ajv": "^6.9.1"}, "devDependencies": {"ajv": "^6.9.1", "ajv-pack": "^0.3.0", "chai": "^4.2.0", "coveralls": "^3.0.2", "dot": "^1.1.1", "eslint": "^7.2.0", "glob": "^7.1.3", "istanbul": "^0.4.3", "js-beautify": "^1.8.9", "json-schema-test": "^2.0.0", "mocha": "^8.0.1", "pre-commit": "^1.1.3", "uuid": "^8.1.0"}, "gitHead": "9e16d50b2604611868972ddb52c3be61c7f8cbdb", "_id": "ajv-keywords@3.5.2", "_nodeVersion": "12.6.0", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-5p6WTN0DdTGVQk6VjcEju19IgaHudalcfabD7yhDGeA6bcQnmL+CpveLJq/3hvfwd1aof6L386Ougkx6RfyMIQ==", "shasum": "31f29da5ab6e00d1c2d329acf7b5929614d5014d", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-3.5.2.tgz", "fileCount": 33, "unpackedSize": 72887, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfHuErCRA9TVsSAnZWagAA8ScP/ju99xLCkS/HZ22XR+9e\nOdif6HER8Hv1nt8EoN0Qxw2y68xMw2tJJv8zVHd9mBnSxuV5L4zg4yghB6da\nPjCHhKJK4omTOjEpX/rqqSu3C6c1BZhQoHUHYI9s4AgoUpe9SXng3fcLmvwU\nl0geOryzjQszhfwhAaYFaNwb1c/VeST7usuXdwYz51x+2qDvkbw0fOUyIPSO\nRtYdYnuL1VxFJj2352YY2GiMaqGs2M/4zX+iEgP5L7lZLQqS1G00mH27QzvT\n1+l8B8mY0gFVPkk9VdPxU17zUlwzIx1bri4QjurxOqNarYeThTRUh4U4o41L\ngGzB6D1//PBT+cnIAIRvt5uEmuiMTyGBQdWoy1vaTQ0PTHssko2JZMV0TOu+\ne8n5DY5CygN31cpkKjJ20ByItKjavkRTPHEdexY+KyGkUjxIgnX2Ug+mb4G3\nrNw9TC1IjrMsF2nJv8pQvf4mXSOaf06hq86o93UukhsKq6t4/3XLFRGLFmay\n8TKdPVEA7dd2DF4o1okea92BH6j4IFpv8ZWt8wTV4pIhoOT/sBNOBU+aWMDb\nMU1dkMeFet2cGSa81tLu89BOXSmZq1pwlT0dY/X81Y1u6zfhbw1Oywm9XGhR\nRYPqMm5M4RZ4d2QPoXYZ7uTmX1lwYP09QjvrtK/yLXYCPWJ+B0TLdxbDtTnM\nqvQg\r\n=EI5j\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEiuzyQT9Pmj4Y1jO8PIpWyqTLg2PiYZOjyfDgxQDRR3AiBRvMiuAfsK0bxz6NH+qB0oE0cBCpjJ4+VBnGpEuJXeKQ=="}]}, "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "_npmUser": {"name": "esp", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ajv-keywords_3.5.2_1595859243247_0.028313188838655412"}, "_hasShrinkwrap": false}, "4.0.0-beta.0": {"name": "ajv-keywords", "version": "4.0.0-beta.0", "description": "Custom JSON-Schema keywords for Ajv validator", "main": "dist/index.js", "typings": "ajv-keywords.d.ts", "scripts": {"build": "rm -rf dist && tsc", "prepublish": "npm run build", "prettier:write": "prettier --write \"./**/*.{md,json,yaml,js,ts}\"", "prettier:check": "prettier --list-different \"./**/*.{md,json,yaml,js,ts}\"", "test": "npm run build && npm run eslint && npm run test-cov", "eslint": "eslint \"src/**/*.*s\" \"spec/**/*.*s\" --ignore-pattern dotjs", "test-spec": "jest spec/*.ts", "test-cov": "jest spec/*.ts --coverage"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/ajv-keywords.git"}, "keywords": ["JSON-Schema", "ajv", "keywords"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/ajv-keywords/issues"}, "homepage": "https://github.com/epoberezkin/ajv-keywords#readme", "peerDependencies": {"ajv": "^7.0.0-beta.2"}, "devDependencies": {"@ajv-validator/config": "^0.2.3", "@types/jest": "^26.0.14", "@types/node": "^14.11.10", "@types/uuid": "^8.3.0", "@typescript-eslint/eslint-plugin": "^4.4.1", "@typescript-eslint/parser": "^4.4.1", "ajv": "^7.0.0-beta.2", "ajv-formats": "^0.3.4", "chai": "^4.2.0", "dot": "^1.1.1", "eslint": "^7.2.0", "eslint-config-prettier": "^6.13.0", "glob": "^7.1.3", "husky": "^4.3.0", "jest": "^26.5.3", "js-beautify": "^1.8.9", "json-schema-test": "^2.0.0", "lint-staged": "^10.4.2", "prettier": "^2.1.2", "ts-jest": "^26.4.1", "typescript": "^4.0.3", "uuid": "^8.1.0"}, "prettier": "@ajv-validator/config/prettierrc.json", "husky": {"hooks": {"pre-commit": "lint-staged && npm test"}}, "lint-staged": {"*.{md,json,yaml,js,ts}": "prettier --write"}, "dependencies": {"@types/chai": "^4.2.14"}, "readme": "# ajv-keywords\n\nCustom JSON-Schema keywords for [Ajv](https://github.com/epoberezkin/ajv) validator\n\n[![Build Status](https://travis-ci.org/ajv-validator/ajv-keywords.svg?branch=master)](https://travis-ci.org/ajv-validator/ajv-keywords)\n[![npm](https://img.shields.io/npm/v/ajv-keywords.svg)](https://www.npmjs.com/package/ajv-keywords)\n[![npm (beta)](https://img.shields.io/npm/v/ajv-keywords/beta)](https://www.npmjs.com/package/ajv-keywords/v/4.0.0-beta.0)\n[![npm downloads](https://img.shields.io/npm/dm/ajv-keywords.svg)](https://www.npmjs.com/package/ajv-keywords)\n[![Coverage Status](https://coveralls.io/repos/github/ajv-validator/ajv-keywords/badge.svg?branch=master)](https://coveralls.io/github/ajv-validator/ajv-keywords?branch=master)\n[![Dependabot](https://api.dependabot.com/badges/status?host=github&repo=ajv-validator/ajv-keywords)](https://app.dependabot.com/accounts/ajv-validator/repos/********)\n[![Gitter](https://img.shields.io/gitter/room/ajv-validator/ajv.svg)](https://gitter.im/ajv-validator/ajv)\n\n**Please note**: [ajv-keywords v4.0.0-beta](https://github.com/ajv-validator/ajv-keywords/releases/tag/v4.0.0-beta.0) should be used with [ajv v7 (beta)](https://github.com/ajv-validator/ajv/tree/v7-beta)\n\n## Contents\n\n- [Install](#install)\n- [Usage](#usage)\n- [Keywords](#keywords)\n  - [Types](#types)\n    - [typeof](#typeof)\n    - [instanceof](#instanceof)\n  - [Keywords for numbers](#keywords-for-numbers)\n    - [range and exclusiveRange](#range-and-exclusiverange)\n  - [Keywords for strings](#keywords-for-strings)\n    - [regexp](#regexp)\n    - [transform](#transform)<sup>\\*</sup>\n  - [Keywords for arrays](#keywords-for-arrays)\n    - [uniqueItemProperties](#uniqueitemproperties)\n  - [Keywords for objects](#keywords-for-objects)\n    - [allRequired](#allrequired)\n    - [anyRequired](#anyrequired)\n    - [oneRequired](#onerequired)\n    - [patternRequired](#patternrequired)\n    - [prohibited](#prohibited)\n    - [deepProperties](#deepproperties)\n    - [deepRequired](#deeprequired)\n    - [dynamicDefaults](#dynamicdefaults)<sup>\\*</sup>\n  - [Keywords for all types](#keywords-for-all-types)\n    - [select/selectCases/selectDefault](#selectselectcasesselectdefault)\n- [Security contact](#security-contact)\n- [Open-source software support](#open-source-software-support)\n- [License](#license)\n\n<sup>\\*</sup> - keywords that modify data\n\n## Install\n\n```\nnpm install ajv-keywords\n```\n\n## Usage\n\nTo add all available keywords:\n\n```javascript\nconst Ajv = require(\"ajv\")\nconst ajv = new Ajv()\nrequire(\"ajv-keywords\")(ajv)\n\najv.validate({instanceof: \"RegExp\"}, /.*/) // true\najv.validate({instanceof: \"RegExp\"}, \".*\") // false\n```\n\nTo add a single keyword:\n\n```javascript\nrequire(\"ajv-keywords\")(ajv, \"instanceof\")\n```\n\nTo add multiple keywords:\n\n```javascript\nrequire(\"ajv-keywords\")(ajv, [\"typeof\", \"instanceof\"])\n```\n\nTo add a single keyword directly (to avoid adding unused code):\n\n```javascript\nrequire(\"ajv-keywords/dist/keywords/select\")(ajv, opts)\n```\n\nTo add all keywords via Ajv options:\n\n```javascript\nconst ajv = new Ajv({keywords: require(\"ajv-keywords/dist/definitions\")(opts)})\n```\n\nTo add one or several keywords via options:\n\n```javascript\nconst ajv = new Ajv({\n  keywords: [\n    require(\"ajv-keywords/dist/definitions/typeof\")(),\n    require(\"ajv-keywords/dist/definitions/instanceof\")(),\n    // select exports an array of 3 definitions - see \"select\" in docs\n    ...require(\"ajv-keywords/dist/definitions/select\")(opts),\n  ],\n})\n```\n\n`opts` is an object with a property `defaultMeta` - URI of meta-schema to use for keywords that use subschemas (`select` and `deepProperties`).\n\n## Keywords\n\n### Types\n\n#### `typeof`\n\nBased on JavaScript `typeof` operation.\n\nThe value of the keyword should be a string (`\"undefined\"`, `\"string\"`, `\"number\"`, `\"object\"`, `\"function\"`, `\"boolean\"` or `\"symbol\"`) or an array of strings.\n\nTo pass validation the result of `typeof` operation on the value should be equal to the string (or one of the strings in the array).\n\n```javascript\najv.validate({typeof: \"undefined\"}, undefined) // true\najv.validate({typeof: \"undefined\"}, null) // false\najv.validate({typeof: [\"undefined\", \"object\"]}, null) // true\n```\n\n#### `instanceof`\n\nBased on JavaScript `instanceof` operation.\n\nThe value of the keyword should be a string (`\"Object\"`, `\"Array\"`, `\"Function\"`, `\"Number\"`, `\"String\"`, `\"Date\"`, `\"RegExp\"` or `\"Promise\"`) or an array of strings.\n\nTo pass validation the result of `data instanceof ...` operation on the value should be true:\n\n```javascript\najv.validate({instanceof: \"Array\"}, []) // true\najv.validate({instanceof: \"Array\"}, {}) // false\najv.validate({instanceof: [\"Array\", \"Function\"]}, function () {}) // true\n```\n\nYou can add your own constructor function to be recognised by this keyword:\n\n```javascript\nclass MyClass {}\nconst instanceofDef = require(\"ajv-keywords/dist/definitions/instanceof\")\ninstanceofDef.CONSTRUCTORS.MyClass = MyClass\najv.validate({instanceof: \"MyClass\"}, new MyClass()) // true\n```\n\n### Keywords for numbers\n\n#### `range` and `exclusiveRange`\n\nSyntax sugar for the combination of minimum and maximum keywords (or exclusiveMinimum and exclusiveMaximum), also fails schema compilation if there are no numbers in the range.\n\nThe value of these keywords must be an array consisting of two numbers, the second must be greater or equal than the first one.\n\nIf the validated value is not a number the validation passes, otherwise to pass validation the value should be greater (or equal) than the first number and smaller (or equal) than the second number in the array.\n\n```javascript\nconst schema = {type: \"number\", range: [1, 3]}\najv.validate(schema, 1) // true\najv.validate(schema, 2) // true\najv.validate(schema, 3) // true\najv.validate(schema, 0.99) // false\najv.validate(schema, 3.01) // false\n\nconst schema = {type: \"number\", exclusiveRange: [1, 3]}\najv.validate(schema, 1.01) // true\najv.validate(schema, 2) // true\najv.validate(schema, 2.99) // true\najv.validate(schema, 1) // false\najv.validate(schema, 3) // false\n```\n\n### Keywords for strings\n\n#### `regexp`\n\nThis keyword allows to use regular expressions with flags in schemas, and also without `\"u\"` flag when needed (the standard `pattern` keyword does not support flags and implies the presence of `\"u\"` flag).\n\nThis keyword applies only to strings. If the data is not a string, the validation succeeds.\n\nThe value of this keyword can be either a string (the result of `regexp.toString()`) or an object with the properties `pattern` and `flags` (the same strings that should be passed to RegExp constructor).\n\n```javascript\nconst schema = {\n  type: \"object\",\n  properties: {\n    foo: {type: \"string\", regexp: \"/foo/i\"},\n    bar: {type: \"string\", regexp: {pattern: \"bar\", flags: \"i\"}},\n  },\n}\n\nconst validData = {\n  foo: \"Food\",\n  bar: \"Barmen\",\n}\n\nconst invalidData = {\n  foo: \"fog\",\n  bar: \"bad\",\n}\n```\n\n#### `transform`\n\nThis keyword allows a string to be modified during validation.\n\nThis keyword applies only to strings. If the data is not a string, the `transform` keyword is ignored.\n\nA standalone string cannot be modified, i.e. `data = 'a'; ajv.validate(schema, data);`, because strings are passed by value\n\n**Supported transformations:**\n\n- `trim`: remove whitespace from start and end\n- `trimStart`/`trimLeft`: remove whitespace from start\n- `trimEnd`/`trimRight`: remove whitespace from end\n- `toLowerCase`: convert to lower case\n- `toUpperCase`: convert to upper case\n- `toEnumCase`: change string case to be equal to one of `enum` values in the schema\n\nTransformations are applied in the order they are listed.\n\nNote: `toEnumCase` requires that all allowed values are unique when case insensitive.\n\n**Example: multiple transformations**\n\n```javascript\nrequire(\"ajv-keywords\")(ajv, \"transform\")\n\nconst schema = {\n  type: \"array\",\n  items: {\n    type: \"string\",\n    transform: [\"trim\", \"toLowerCase\"],\n  },\n}\n\nconst data = [\"  MixCase  \"]\najv.validate(schema, data)\nconsole.log(data) // ['mixcase']\n```\n\n**Example: `enumcase`**\n\n```javascript\nrequire(\"ajv-keywords\")(ajv, [\"transform\"])\n\nconst schema = {\n  type: \"array\",\n  items: {\n    type: \"string\",\n    transform: [\"trim\", \"toEnumCase\"],\n    enum: [\"pH\"],\n  },\n}\n\nconst data = [\"ph\", \" Ph\", \"PH\", \"pH \"]\najv.validate(schema, data)\nconsole.log(data) // ['pH','pH','pH','pH']\n```\n\n### Keywords for arrays\n\n#### `uniqueItemProperties`\n\nThe keyword allows to check that some properties in array items are unique.\n\nThis keyword applies only to arrays. If the data is not an array, the validation succeeds.\n\nThe value of this keyword must be an array of strings - property names that should have unique values across all items.\n\n```javascript\nconst schema = {\n  type: \"array\",\n  uniqueItemProperties: [\"id\", \"name\"],\n}\n\nconst validData = [{id: 1}, {id: 2}, {id: 3}]\n\nconst invalidData1 = [\n  {id: 1},\n  {id: 1}, // duplicate \"id\"\n  {id: 3},\n]\n\nconst invalidData2 = [\n  {id: 1, name: \"taco\"},\n  {id: 2, name: \"taco\"}, // duplicate \"name\"\n  {id: 3, name: \"salsa\"},\n]\n```\n\nThis keyword is contributed by [@blainesch](https://github.com/blainesch).\n\n### Keywords for objects\n\n#### `allRequired`\n\nThis keyword allows to require the presence of all properties used in `properties` keyword in the same schema object.\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value of this keyword must be boolean.\n\nIf the value of the keyword is `false`, the validation succeeds.\n\nIf the value of the keyword is `true`, the validation succeeds if the data contains all properties defined in `properties` keyword (in the same schema object).\n\nIf the `properties` keyword is not present in the same schema object, schema compilation will throw exception.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  properties: {\n    foo: {type: 'number'},\n    bar: {type: 'number'}\n  }\n  allRequired: true\n};\n\nconst validData = { foo: 1, bar: 2 };\nconst alsoValidData = { foo: 1, bar: 2, baz: 3 };\n\nconst invalidDataList = [ {}, { foo: 1 }, { bar: 2 } ];\n```\n\n#### `anyRequired`\n\nThis keyword allows to require the presence of any (at least one) property from the list.\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value of this keyword must be an array of strings, each string being a property name. For data object to be valid at least one of the properties in this array should be present in the object.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  anyRequired: [\"foo\", \"bar\"],\n}\n\nconst validData = {foo: 1}\nconst alsoValidData = {foo: 1, bar: 2}\n\nconst invalidDataList = [{}, {baz: 3}]\n```\n\n#### `oneRequired`\n\nThis keyword allows to require the presence of only one property from the list.\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value of this keyword must be an array of strings, each string being a property name. For data object to be valid exactly one of the properties in this array should be present in the object.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  oneRequired: [\"foo\", \"bar\"],\n}\n\nconst validData = {foo: 1}\nconst alsoValidData = {bar: 2, baz: 3}\n\nconst invalidDataList = [{}, {baz: 3}, {foo: 1, bar: 2}]\n```\n\n#### `patternRequired`\n\nThis keyword allows to require the presence of properties that match some pattern(s).\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value of this keyword should be an array of strings, each string being a regular expression. For data object to be valid each regular expression in this array should match at least one property name in the data object.\n\nIf the array contains multiple regular expressions, more than one expression can match the same property name.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  patternRequired: [\"f.*o\", \"b.*r\"],\n}\n\nconst validData = {foo: 1, bar: 2}\nconst alsoValidData = {foobar: 3}\n\nconst invalidDataList = [{}, {foo: 1}, {bar: 2}]\n```\n\n#### `prohibited`\n\nThis keyword allows to prohibit that any of the properties in the list is present in the object.\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value of this keyword should be an array of strings, each string being a property name. For data object to be valid none of the properties in this array should be present in the object.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  prohibited: [\"foo\", \"bar\"],\n}\n\nconst validData = {baz: 1}\nconst alsoValidData = {}\n\nconst invalidDataList = [{foo: 1}, {bar: 2}, {foo: 1, bar: 2}]\n```\n\n**Please note**: `{prohibited: ['foo', 'bar']}` is equivalent to `{not: {anyRequired: ['foo', 'bar']}}` (i.e. it has the same validation result for any data).\n\n#### `deepProperties`\n\nThis keyword allows to validate deep properties (identified by JSON pointers).\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value should be an object, where keys are JSON pointers to the data, starting from the current position in data, and the values are JSON schemas. For data object to be valid the value of each JSON pointer should be valid according to the corresponding schema.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  deepProperties: {\n    \"/users/1/role\": {enum: [\"admin\"]},\n  },\n}\n\nconst validData = {\n  users: [\n    {},\n    {\n      id: 123,\n      role: \"admin\",\n    },\n  ],\n}\n\nconst alsoValidData = {\n  users: {\n    1: {\n      id: 123,\n      role: \"admin\",\n    },\n  },\n}\n\nconst invalidData = {\n  users: [\n    {},\n    {\n      id: 123,\n      role: \"user\",\n    },\n  ],\n}\n\nconst alsoInvalidData = {\n  users: {\n    1: {\n      id: 123,\n      role: \"user\",\n    },\n  },\n}\n```\n\n#### `deepRequired`\n\nThis keyword allows to check that some deep properties (identified by JSON pointers) are available.\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value should be an array of JSON pointers to the data, starting from the current position in data. For data object to be valid each JSON pointer should be some existing part of the data.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  deepRequired: [\"/users/1/role\"],\n}\n\nconst validData = {\n  users: [\n    {},\n    {\n      id: 123,\n      role: \"admin\",\n    },\n  ],\n}\n\nconst invalidData = {\n  users: [\n    {},\n    {\n      id: 123,\n    },\n  ],\n}\n```\n\nSee [json-schema-org/json-schema-spec#203](https://github.com/json-schema-org/json-schema-spec/issues/203#issue-197211916) for an example of the equivalent schema without `deepRequired` keyword.\n\n### Keywords for all types\n\n#### `select`/`selectCases`/`selectDefault`\n\nThese keywords allow to choose the schema to validate the data based on the value of some property in the validated data.\n\nThese keywords must be present in the same schema object (`selectDefault` is optional).\n\nThe value of `select` keyword should be a [\\$data reference](https://github.com/ajv-validator/ajv/blob/v7-beta/docs/validation.md#data-reference) that points to any primitive JSON type (string, number, boolean or null) in the data that is validated. You can also use a constant of primitive type as the value of this keyword (e.g., for debugging purposes).\n\nThe value of `selectCases` keyword must be an object where each property name is a possible string representation of the value of `select` keyword and each property value is a corresponding schema (from draft-06 it can be boolean) that must be used to validate the data.\n\nThe value of `selectDefault` keyword is a schema (also can be boolean) that must be used to validate the data in case `selectCases` has no key equal to the stringified value of `select` keyword.\n\nThe validation succeeds in one of the following cases:\n\n- the validation of data using selected schema succeeds,\n- none of the schemas is selected for validation,\n- the value of select is undefined (no property in the data that the data reference points to).\n\nIf `select` value (in data) is not a primitive type the validation fails.\n\n**Please note**: these keywords require Ajv `$data` option to support [\\$data reference](https://github.com/epoberezkin/ajv/tree/5.0.2-beta.0#data-reference).\n\n```javascript\nrequire('ajv-keywords')(ajv, 'select');\n\nconst schema = {\n  type: \"object\",\n  required: ['kind'],\n  properties: {\n    kind: { type: 'string' }\n  },\n  select: { $data: '0/kind' },\n  selectCases: {\n    foo: {\n      required: ['foo'],\n      properties: {\n        kind: {},\n        foo: { type: 'string' }\n      },\n      additionalProperties: false\n    },\n    bar: {\n      required: ['bar'],\n      properties: {\n        kind: {},\n        bar: { type: 'number' }\n      },\n      additionalProperties: false\n    }\n  },\n  selectDefault: {\n    propertyNames: {\n      not: { enum: ['foo', 'bar'] }\n    }\n  }\n};\n\nconst validDataList = [\n  { kind: 'foo', foo: 'any' },\n  { kind: 'bar', bar: 1 },\n  { kind: 'anything_else', not_bar_or_foo: 'any value' }\n];\n\nconst invalidDataList = [\n  { kind: 'foo' }, // no propery foo\n  { kind: 'bar' }, // no propery bar\n  { kind: 'foo', foo: 'any', another: 'any value' }, // additional property\n  { kind: 'bar', bar: 1, another: 'any value' }, // additional property\n  { kind: 'anything_else', foo: 'any' } // property foo not allowed\n  { kind: 'anything_else', bar: 1 } // property bar not allowed\n];\n```\n\n#### `dynamicDefaults`\n\nThis keyword allows to assign dynamic defaults to properties, such as timestamps, unique IDs etc.\n\nThis keyword only works if `useDefaults` options is used and not inside `anyOf` keywords etc., in the same way as [default keyword treated by Ajv](https://github.com/epoberezkin/ajv#assigning-defaults).\n\nThe keyword should be added on the object level. Its value should be an object with each property corresponding to a property name, in the same way as in standard `properties` keyword. The value of each property can be:\n\n- an identifier of dynamic default function (a string)\n- an object with properties `func` (an identifier) and `args` (an object with parameters that will be passed to this function during schema compilation - see examples).\n\nThe properties used in `dynamicDefaults` should not be added to `required` keyword in the same schema (or validation will fail), because unlike `default` this keyword is processed after validation.\n\nThere are several predefined dynamic default functions:\n\n- `\"timestamp\"` - current timestamp in milliseconds\n- `\"datetime\"` - current date and time as string (ISO, valid according to `date-time` format)\n- `\"date\"` - current date as string (ISO, valid according to `date` format)\n- `\"time\"` - current time as string (ISO, valid according to `time` format)\n- `\"random\"` - pseudo-random number in [0, 1) interval\n- `\"randomint\"` - pseudo-random integer number. If string is used as a property value, the function will randomly return 0 or 1. If object `{ func: 'randomint', args: { max: N } }` is used then the default will be an integer number in [0, N) interval.\n- `\"seq\"` - sequential integer number starting from 0. If string is used as a property value, the default sequence will be used. If object `{ func: 'seq', args: { name: 'foo'} }` is used then the sequence with name `\"foo\"` will be used. Sequences are global, even if different ajv instances are used.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  dynamicDefaults: {\n    ts: \"datetime\",\n    r: {func: \"randomint\", args: {max: 100}},\n    id: {func: \"seq\", args: {name: \"id\"}},\n  },\n  properties: {\n    ts: {\n      type: \"string\",\n      format: \"date-time\",\n    },\n    r: {\n      type: \"integer\",\n      minimum: 0,\n      exclusiveMaximum: 100,\n    },\n    id: {\n      type: \"integer\",\n      minimum: 0,\n    },\n  },\n}\n\nconst data = {}\najv.validate(data) // true\ndata // { ts: '2016-12-01T22:07:28.829Z', r: 25, id: 0 }\n\nconst data1 = {}\najv.validate(data1) // true\ndata1 // { ts: '2016-12-01T22:07:29.832Z', r: 68, id: 1 }\n\najv.validate(data1) // true\ndata1 // didn't change, as all properties were defined\n```\n\nWhen using the `useDefaults` option value `\"empty\"`, properties and items equal to `null` or `\"\"` (empty string) will be considered missing and assigned defaults. Use `allOf` [compound keyword](https://github.com/epoberezkin/ajv/blob/master/KEYWORDS.md#compound-keywords) to execute `dynamicDefaults` before validation.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  allOf: [\n    {\n      dynamicDefaults: {\n        ts: \"datetime\",\n        r: {func: \"randomint\", args: {min: 5, max: 100}},\n        id: {func: \"seq\", args: {name: \"id\"}},\n      },\n    },\n    {\n      properties: {\n        ts: {\n          type: \"string\",\n        },\n        r: {\n          type: \"number\",\n          minimum: 5,\n          exclusiveMaximum: 100,\n        },\n        id: {\n          type: \"integer\",\n          minimum: 0,\n        },\n      },\n    },\n  ],\n}\n\nconst data = {ts: \"\", r: null}\najv.validate(data) // true\ndata // { ts: '2016-12-01T22:07:28.829Z', r: 25, id: 0 }\n```\n\nYou can add your own dynamic default function to be recognised by this keyword:\n\n```javascript\nconst uuid = require(\"uuid\")\n\nconst def = require(\"ajv-keywords/dist/definitions/dynamicDefaults\")\ndef.DEFAULTS.uuid = () => uuid.v4\n\nconst schema = {\n  dynamicDefaults: {id: \"uuid\"},\n  properties: {id: {type: \"string\", format: \"uuid\"}},\n}\n\nconst data = {}\najv.validate(schema, data) // true\ndata // { id: 'a1183fbe-697b-4030-9bcc-cfeb282a9150' };\n\nconst data1 = {}\najv.validate(schema, data1) // true\ndata1 // { id: '5b008de7-1669-467a-a5c6-70fa244d7209' }\n```\n\nYou also can define dynamic default that accept parameters, e.g. version of uuid:\n\n```javascript\nconst uuid = require(\"uuid\")\n\nfunction getUuid(args) {\n  const version = \"v\" + ((arvs && args.v) || \"4\")\n  return uuid[version]\n}\n\nconst def = require(\"ajv-keywords/dist/definitions/dynamicDefaults\")\ndef.DEFAULTS.uuid = getUuid\n\nconst schema = {\n  dynamicDefaults: {\n    id1: \"uuid\", // v4\n    id2: {func: \"uuid\", v: 4}, // v4\n    id3: {func: \"uuid\", v: 1}, // v1\n  },\n}\n```\n\n**Please note**: dynamic default functions differented by the number of parameters they have (`function.length`). Functions that do not expect default must have one non-optional argument so that `function.length` > 0.\n\n## Security contact\n\nTo report a security vulnerability, please use the\n[Tidelift security contact](https://tidelift.com/security).\nTidelift will coordinate the fix and disclosure.\n\nPlease do NOT report security vulnerabilities via GitHub issues.\n\n## Open-source software support\n\nAjv-keywords is a part of [Tidelift subscription](https://tidelift.com/subscription/pkg/npm-ajv-keywords?utm_source=npm-ajv-keywords&utm_medium=referral&utm_campaign=readme) - it provides a centralised support to open-source software users, in addition to the support provided by software maintainers.\n\n## License\n\n[MIT](https://github.com/epoberezkin/ajv-keywords/blob/master/LICENSE)\n", "readmeFilename": "README.md", "gitHead": "89d371f19d72898043b43dac72c8e689017654bd", "_id": "ajv-keywords@4.0.0-beta.0", "_nodeVersion": "14.14.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-+FDLq/nSDmtqixRZ2FQqB431EAxvxBmVoClfwDeSqT4/1IjzoEm4sJSE1KWi2W1C2AQwkfTTPUJuS5NdtFVhlQ==", "shasum": "3080dc66f89b75248afce0a7047c853e96d07e35", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-4.0.0-beta.0.tgz", "fileCount": 160, "unpackedSize": 124885, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJflIAFCRA9TVsSAnZWagAA5ZsP+gO4MC2fUba/bUCs+J03\n0wV3wxQVkPaV41MO7/60b9UlTrPVRnPQrLF2bGwbzDErAJTrGVislL0rv70x\nur5GfDLwbFZPj1shhfyJfGINfD2PgMzSjhcDBwQ6vGyvk7jKbNY8Ja1QuJOG\nQYtHuquobzD03tw3l/mtNVgm0aYBgEbJMpnTzfAnCqsud4Jk+SFe7bXTo4qw\nrJb/sQEr63aps5YNn/eI2z7JpDjL9QEXDVEACo5auCR/Nn4rjTi5oH9i37Yi\nGGfsuFkcls/mrQC8QOmucwIh7lXL1bkKZ8hkmKTX88Yn/MDD0aiSrcHjXtgs\nrbybKj6XaT4GM0wdyOThsPwlsn9BsbKSXyrHiXlpElo2op/g00kQWyqRbwj4\nJkLL2gez6Stf4vk7o0gI0vEORkfbyC2/40HnFJ4yFGmBDDyK1poT//xVicSi\naP0NM8Dj3Dj6RD6p4xos2FTm1NTM3aPmh2ASTIc75TD7QGKYrKnGgymOOv9d\nrDyIJDQ5tqhtxKFSUSjrEKEmgrr8J7QNwPkHv0XRhl/Kw8+uOPsV/AE3TjA6\ndF7g/V7zTszXZrquaPFKU8QZ0cME3kUJA7RJKcMlN1jkZZuOux4kp8y5zn+/\nOjaHMSSGsR8x8ZipKwC4GMl/MILQRXqcwIbDSjKrJQHSp8zolWr0jJW8BkjU\nbWFF\r\n=MYic\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDMLZrWTz2JpABdTaQwcO/W9qKfMfr5Tlug0ofYrtGzaAIhAOZyWchLKbiOw4bf/m8wt+5JOYVqfudrSj0BNINMPPQG"}]}, "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "_npmUser": {"name": "esp", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ajv-keywords_4.0.0-beta.0_1603567620823_0.8978516285533538"}, "_hasShrinkwrap": false}, "4.0.0-beta.1": {"name": "ajv-keywords", "version": "4.0.0-beta.1", "description": "Custom JSON-Schema keywords for Ajv validator", "main": "dist/index.js", "typings": "ajv-keywords.d.ts", "scripts": {"build": "rm -rf dist && tsc", "prepublish": "npm run build", "prettier:write": "prettier --write \"./**/*.{md,json,yaml,js,ts}\"", "prettier:check": "prettier --list-different \"./**/*.{md,json,yaml,js,ts}\"", "test": "npm run build && npm run eslint && npm run test-cov", "eslint": "eslint \"src/**/*.*s\" \"spec/**/*.*s\" --ignore-pattern dotjs", "test-spec": "jest spec/*.ts", "test-cov": "jest spec/*.ts --coverage"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/ajv-keywords.git"}, "keywords": ["JSON-Schema", "ajv", "keywords"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/ajv-keywords/issues"}, "homepage": "https://github.com/epoberezkin/ajv-keywords#readme", "peerDependencies": {"ajv": "^7.0.0-beta.3"}, "devDependencies": {"@ajv-validator/config": "^0.2.3", "@types/jest": "^26.0.14", "@types/node": "^14.11.10", "@types/uuid": "^8.3.0", "@typescript-eslint/eslint-plugin": "^4.4.1", "@typescript-eslint/parser": "^4.4.1", "ajv": "^7.0.0-beta.3", "ajv-formats": "^0.3.4", "chai": "^4.2.0", "dot": "^1.1.1", "eslint": "^7.2.0", "eslint-config-prettier": "^6.13.0", "glob": "^7.1.3", "husky": "^4.3.0", "jest": "^26.5.3", "js-beautify": "^1.8.9", "json-schema-test": "^2.0.0", "lint-staged": "^10.4.2", "prettier": "^2.1.2", "ts-jest": "^26.4.1", "typescript": "^4.0.3", "uuid": "^8.1.0"}, "prettier": "@ajv-validator/config/prettierrc.json", "husky": {"hooks": {"pre-commit": "lint-staged && npm test"}}, "lint-staged": {"*.{md,json,yaml,js,ts}": "prettier --write"}, "dependencies": {"@types/chai": "^4.2.14"}, "readme": "# ajv-keywords\n\nCustom JSON-Schema keywords for [Ajv](https://github.com/epoberezkin/ajv) validator\n\n[![Build Status](https://travis-ci.org/ajv-validator/ajv-keywords.svg?branch=master)](https://travis-ci.org/ajv-validator/ajv-keywords)\n[![npm](https://img.shields.io/npm/v/ajv-keywords.svg)](https://www.npmjs.com/package/ajv-keywords)\n[![npm (beta)](https://img.shields.io/npm/v/ajv-keywords/beta)](https://www.npmjs.com/package/ajv-keywords/v/4.0.0-beta.1)\n[![npm downloads](https://img.shields.io/npm/dm/ajv-keywords.svg)](https://www.npmjs.com/package/ajv-keywords)\n[![Coverage Status](https://coveralls.io/repos/github/ajv-validator/ajv-keywords/badge.svg?branch=master)](https://coveralls.io/github/ajv-validator/ajv-keywords?branch=master)\n[![Dependabot](https://api.dependabot.com/badges/status?host=github&repo=ajv-validator/ajv-keywords)](https://app.dependabot.com/accounts/ajv-validator/repos/********)\n[![Gitter](https://img.shields.io/gitter/room/ajv-validator/ajv.svg)](https://gitter.im/ajv-validator/ajv)\n\n**Please note**: [ajv-keywords v4.0.0-beta](https://github.com/ajv-validator/ajv-keywords/releases/tag/v4.0.0-beta.0) should be used with [ajv v7 (beta)](https://github.com/ajv-validator/ajv/tree/v7-beta)\n\n## Contents\n\n- [Install](#install)\n- [Usage](#usage)\n- [Keywords](#keywords)\n  - [Types](#types)\n    - [typeof](#typeof)\n    - [instanceof](#instanceof)\n  - [Keywords for numbers](#keywords-for-numbers)\n    - [range and exclusiveRange](#range-and-exclusiverange)\n  - [Keywords for strings](#keywords-for-strings)\n    - [regexp](#regexp)\n    - [transform](#transform)<sup>\\*</sup>\n  - [Keywords for arrays](#keywords-for-arrays)\n    - [uniqueItemProperties](#uniqueitemproperties)\n  - [Keywords for objects](#keywords-for-objects)\n    - [allRequired](#allrequired)\n    - [anyRequired](#anyrequired)\n    - [oneRequired](#onerequired)\n    - [patternRequired](#patternrequired)\n    - [prohibited](#prohibited)\n    - [deepProperties](#deepproperties)\n    - [deepRequired](#deeprequired)\n    - [dynamicDefaults](#dynamicdefaults)<sup>\\*</sup>\n  - [Keywords for all types](#keywords-for-all-types)\n    - [select/selectCases/selectDefault](#selectselectcasesselectdefault)\n- [Security contact](#security-contact)\n- [Open-source software support](#open-source-software-support)\n- [License](#license)\n\n<sup>\\*</sup> - keywords that modify data\n\n## Install\n\n```\nnpm install ajv-keywords\n```\n\nTo install version 4 beta to use with [Ajv v7 beta](https://github.com/ajv-validator/ajv/tree/v7-beta):\n\n```\nnpm install ajv-keywords@beta\n```\n\n## Usage\n\nTo add all available keywords:\n\n```javascript\nconst Ajv = require(\"ajv\")\nconst ajv = new Ajv()\nrequire(\"ajv-keywords\")(ajv)\n\najv.validate({instanceof: \"RegExp\"}, /.*/) // true\najv.validate({instanceof: \"RegExp\"}, \".*\") // false\n```\n\nTo add a single keyword:\n\n```javascript\nrequire(\"ajv-keywords\")(ajv, \"instanceof\")\n```\n\nTo add multiple keywords:\n\n```javascript\nrequire(\"ajv-keywords\")(ajv, [\"typeof\", \"instanceof\"])\n```\n\nTo add a single keyword directly (to avoid adding unused code):\n\n```javascript\nrequire(\"ajv-keywords/dist/keywords/select\")(ajv, opts)\n```\n\nTo add all keywords via Ajv options:\n\n```javascript\nconst ajv = new Ajv({keywords: require(\"ajv-keywords/dist/definitions\")(opts)})\n```\n\nTo add one or several keywords via options:\n\n```javascript\nconst ajv = new Ajv({\n  keywords: [\n    require(\"ajv-keywords/dist/definitions/typeof\")(),\n    require(\"ajv-keywords/dist/definitions/instanceof\")(),\n    // select exports an array of 3 definitions - see \"select\" in docs\n    ...require(\"ajv-keywords/dist/definitions/select\")(opts),\n  ],\n})\n```\n\n`opts` is an object with a property `defaultMeta` - URI of meta-schema to use for keywords that use subschemas (`select` and `deepProperties`).\n\n## Keywords\n\n### Types\n\n#### `typeof`\n\nBased on JavaScript `typeof` operation.\n\nThe value of the keyword should be a string (`\"undefined\"`, `\"string\"`, `\"number\"`, `\"object\"`, `\"function\"`, `\"boolean\"` or `\"symbol\"`) or an array of strings.\n\nTo pass validation the result of `typeof` operation on the value should be equal to the string (or one of the strings in the array).\n\n```javascript\najv.validate({typeof: \"undefined\"}, undefined) // true\najv.validate({typeof: \"undefined\"}, null) // false\najv.validate({typeof: [\"undefined\", \"object\"]}, null) // true\n```\n\n#### `instanceof`\n\nBased on JavaScript `instanceof` operation.\n\nThe value of the keyword should be a string (`\"Object\"`, `\"Array\"`, `\"Function\"`, `\"Number\"`, `\"String\"`, `\"Date\"`, `\"RegExp\"` or `\"Promise\"`) or an array of strings.\n\nTo pass validation the result of `data instanceof ...` operation on the value should be true:\n\n```javascript\najv.validate({instanceof: \"Array\"}, []) // true\najv.validate({instanceof: \"Array\"}, {}) // false\najv.validate({instanceof: [\"Array\", \"Function\"]}, function () {}) // true\n```\n\nYou can add your own constructor function to be recognised by this keyword:\n\n```javascript\nclass MyClass {}\nconst instanceofDef = require(\"ajv-keywords/dist/definitions/instanceof\")\ninstanceofDef.CONSTRUCTORS.MyClass = MyClass\najv.validate({instanceof: \"MyClass\"}, new MyClass()) // true\n```\n\n### Keywords for numbers\n\n#### `range` and `exclusiveRange`\n\nSyntax sugar for the combination of minimum and maximum keywords (or exclusiveMinimum and exclusiveMaximum), also fails schema compilation if there are no numbers in the range.\n\nThe value of these keywords must be an array consisting of two numbers, the second must be greater or equal than the first one.\n\nIf the validated value is not a number the validation passes, otherwise to pass validation the value should be greater (or equal) than the first number and smaller (or equal) than the second number in the array.\n\n```javascript\nconst schema = {type: \"number\", range: [1, 3]}\najv.validate(schema, 1) // true\najv.validate(schema, 2) // true\najv.validate(schema, 3) // true\najv.validate(schema, 0.99) // false\najv.validate(schema, 3.01) // false\n\nconst schema = {type: \"number\", exclusiveRange: [1, 3]}\najv.validate(schema, 1.01) // true\najv.validate(schema, 2) // true\najv.validate(schema, 2.99) // true\najv.validate(schema, 1) // false\najv.validate(schema, 3) // false\n```\n\n### Keywords for strings\n\n#### `regexp`\n\nThis keyword allows to use regular expressions with flags in schemas, and also without `\"u\"` flag when needed (the standard `pattern` keyword does not support flags and implies the presence of `\"u\"` flag).\n\nThis keyword applies only to strings. If the data is not a string, the validation succeeds.\n\nThe value of this keyword can be either a string (the result of `regexp.toString()`) or an object with the properties `pattern` and `flags` (the same strings that should be passed to RegExp constructor).\n\n```javascript\nconst schema = {\n  type: \"object\",\n  properties: {\n    foo: {type: \"string\", regexp: \"/foo/i\"},\n    bar: {type: \"string\", regexp: {pattern: \"bar\", flags: \"i\"}},\n  },\n}\n\nconst validData = {\n  foo: \"Food\",\n  bar: \"Barmen\",\n}\n\nconst invalidData = {\n  foo: \"fog\",\n  bar: \"bad\",\n}\n```\n\n#### `transform`\n\nThis keyword allows a string to be modified during validation.\n\nThis keyword applies only to strings. If the data is not a string, the `transform` keyword is ignored.\n\nA standalone string cannot be modified, i.e. `data = 'a'; ajv.validate(schema, data);`, because strings are passed by value\n\n**Supported transformations:**\n\n- `trim`: remove whitespace from start and end\n- `trimStart`/`trimLeft`: remove whitespace from start\n- `trimEnd`/`trimRight`: remove whitespace from end\n- `toLowerCase`: convert to lower case\n- `toUpperCase`: convert to upper case\n- `toEnumCase`: change string case to be equal to one of `enum` values in the schema\n\nTransformations are applied in the order they are listed.\n\nNote: `toEnumCase` requires that all allowed values are unique when case insensitive.\n\n**Example: multiple transformations**\n\n```javascript\nrequire(\"ajv-keywords\")(ajv, \"transform\")\n\nconst schema = {\n  type: \"array\",\n  items: {\n    type: \"string\",\n    transform: [\"trim\", \"toLowerCase\"],\n  },\n}\n\nconst data = [\"  MixCase  \"]\najv.validate(schema, data)\nconsole.log(data) // ['mixcase']\n```\n\n**Example: `enumcase`**\n\n```javascript\nrequire(\"ajv-keywords\")(ajv, [\"transform\"])\n\nconst schema = {\n  type: \"array\",\n  items: {\n    type: \"string\",\n    transform: [\"trim\", \"toEnumCase\"],\n    enum: [\"pH\"],\n  },\n}\n\nconst data = [\"ph\", \" Ph\", \"PH\", \"pH \"]\najv.validate(schema, data)\nconsole.log(data) // ['pH','pH','pH','pH']\n```\n\n### Keywords for arrays\n\n#### `uniqueItemProperties`\n\nThe keyword allows to check that some properties in array items are unique.\n\nThis keyword applies only to arrays. If the data is not an array, the validation succeeds.\n\nThe value of this keyword must be an array of strings - property names that should have unique values across all items.\n\n```javascript\nconst schema = {\n  type: \"array\",\n  uniqueItemProperties: [\"id\", \"name\"],\n}\n\nconst validData = [{id: 1}, {id: 2}, {id: 3}]\n\nconst invalidData1 = [\n  {id: 1},\n  {id: 1}, // duplicate \"id\"\n  {id: 3},\n]\n\nconst invalidData2 = [\n  {id: 1, name: \"taco\"},\n  {id: 2, name: \"taco\"}, // duplicate \"name\"\n  {id: 3, name: \"salsa\"},\n]\n```\n\nThis keyword is contributed by [@blainesch](https://github.com/blainesch).\n\n### Keywords for objects\n\n#### `allRequired`\n\nThis keyword allows to require the presence of all properties used in `properties` keyword in the same schema object.\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value of this keyword must be boolean.\n\nIf the value of the keyword is `false`, the validation succeeds.\n\nIf the value of the keyword is `true`, the validation succeeds if the data contains all properties defined in `properties` keyword (in the same schema object).\n\nIf the `properties` keyword is not present in the same schema object, schema compilation will throw exception.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  properties: {\n    foo: {type: 'number'},\n    bar: {type: 'number'}\n  }\n  allRequired: true\n};\n\nconst validData = { foo: 1, bar: 2 };\nconst alsoValidData = { foo: 1, bar: 2, baz: 3 };\n\nconst invalidDataList = [ {}, { foo: 1 }, { bar: 2 } ];\n```\n\n#### `anyRequired`\n\nThis keyword allows to require the presence of any (at least one) property from the list.\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value of this keyword must be an array of strings, each string being a property name. For data object to be valid at least one of the properties in this array should be present in the object.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  anyRequired: [\"foo\", \"bar\"],\n}\n\nconst validData = {foo: 1}\nconst alsoValidData = {foo: 1, bar: 2}\n\nconst invalidDataList = [{}, {baz: 3}]\n```\n\n#### `oneRequired`\n\nThis keyword allows to require the presence of only one property from the list.\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value of this keyword must be an array of strings, each string being a property name. For data object to be valid exactly one of the properties in this array should be present in the object.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  oneRequired: [\"foo\", \"bar\"],\n}\n\nconst validData = {foo: 1}\nconst alsoValidData = {bar: 2, baz: 3}\n\nconst invalidDataList = [{}, {baz: 3}, {foo: 1, bar: 2}]\n```\n\n#### `patternRequired`\n\nThis keyword allows to require the presence of properties that match some pattern(s).\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value of this keyword should be an array of strings, each string being a regular expression. For data object to be valid each regular expression in this array should match at least one property name in the data object.\n\nIf the array contains multiple regular expressions, more than one expression can match the same property name.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  patternRequired: [\"f.*o\", \"b.*r\"],\n}\n\nconst validData = {foo: 1, bar: 2}\nconst alsoValidData = {foobar: 3}\n\nconst invalidDataList = [{}, {foo: 1}, {bar: 2}]\n```\n\n#### `prohibited`\n\nThis keyword allows to prohibit that any of the properties in the list is present in the object.\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value of this keyword should be an array of strings, each string being a property name. For data object to be valid none of the properties in this array should be present in the object.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  prohibited: [\"foo\", \"bar\"],\n}\n\nconst validData = {baz: 1}\nconst alsoValidData = {}\n\nconst invalidDataList = [{foo: 1}, {bar: 2}, {foo: 1, bar: 2}]\n```\n\n**Please note**: `{prohibited: ['foo', 'bar']}` is equivalent to `{not: {anyRequired: ['foo', 'bar']}}` (i.e. it has the same validation result for any data).\n\n#### `deepProperties`\n\nThis keyword allows to validate deep properties (identified by JSON pointers).\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value should be an object, where keys are JSON pointers to the data, starting from the current position in data, and the values are JSON schemas. For data object to be valid the value of each JSON pointer should be valid according to the corresponding schema.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  deepProperties: {\n    \"/users/1/role\": {enum: [\"admin\"]},\n  },\n}\n\nconst validData = {\n  users: [\n    {},\n    {\n      id: 123,\n      role: \"admin\",\n    },\n  ],\n}\n\nconst alsoValidData = {\n  users: {\n    1: {\n      id: 123,\n      role: \"admin\",\n    },\n  },\n}\n\nconst invalidData = {\n  users: [\n    {},\n    {\n      id: 123,\n      role: \"user\",\n    },\n  ],\n}\n\nconst alsoInvalidData = {\n  users: {\n    1: {\n      id: 123,\n      role: \"user\",\n    },\n  },\n}\n```\n\n#### `deepRequired`\n\nThis keyword allows to check that some deep properties (identified by JSON pointers) are available.\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value should be an array of JSON pointers to the data, starting from the current position in data. For data object to be valid each JSON pointer should be some existing part of the data.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  deepRequired: [\"/users/1/role\"],\n}\n\nconst validData = {\n  users: [\n    {},\n    {\n      id: 123,\n      role: \"admin\",\n    },\n  ],\n}\n\nconst invalidData = {\n  users: [\n    {},\n    {\n      id: 123,\n    },\n  ],\n}\n```\n\nSee [json-schema-org/json-schema-spec#203](https://github.com/json-schema-org/json-schema-spec/issues/203#issue-197211916) for an example of the equivalent schema without `deepRequired` keyword.\n\n### Keywords for all types\n\n#### `select`/`selectCases`/`selectDefault`\n\nThese keywords allow to choose the schema to validate the data based on the value of some property in the validated data.\n\nThese keywords must be present in the same schema object (`selectDefault` is optional).\n\nThe value of `select` keyword should be a [\\$data reference](https://github.com/ajv-validator/ajv/blob/v7-beta/docs/validation.md#data-reference) that points to any primitive JSON type (string, number, boolean or null) in the data that is validated. You can also use a constant of primitive type as the value of this keyword (e.g., for debugging purposes).\n\nThe value of `selectCases` keyword must be an object where each property name is a possible string representation of the value of `select` keyword and each property value is a corresponding schema (from draft-06 it can be boolean) that must be used to validate the data.\n\nThe value of `selectDefault` keyword is a schema (also can be boolean) that must be used to validate the data in case `selectCases` has no key equal to the stringified value of `select` keyword.\n\nThe validation succeeds in one of the following cases:\n\n- the validation of data using selected schema succeeds,\n- none of the schemas is selected for validation,\n- the value of select is undefined (no property in the data that the data reference points to).\n\nIf `select` value (in data) is not a primitive type the validation fails.\n\nThis keyword correctly tracks evaluated properties and items to work with `unevaluatedProperties` and `unevaluatedItems` keywords - only properties and items from the subschema that was used (one of `selectCases` subschemas or `selectDefault` subschema) are marked as evaluated.\n\n**Please note**: these keywords require Ajv `$data` option to support [\\$data reference](https://github.com/epoberezkin/ajv/tree/5.0.2-beta.0#data-reference).\n\n```javascript\nrequire('ajv-keywords')(ajv, 'select');\n\nconst schema = {\n  type: \"object\",\n  required: ['kind'],\n  properties: {\n    kind: { type: 'string' }\n  },\n  select: { $data: '0/kind' },\n  selectCases: {\n    foo: {\n      required: ['foo'],\n      properties: {\n        kind: {},\n        foo: { type: 'string' }\n      },\n      additionalProperties: false\n    },\n    bar: {\n      required: ['bar'],\n      properties: {\n        kind: {},\n        bar: { type: 'number' }\n      },\n      additionalProperties: false\n    }\n  },\n  selectDefault: {\n    propertyNames: {\n      not: { enum: ['foo', 'bar'] }\n    }\n  }\n};\n\nconst validDataList = [\n  { kind: 'foo', foo: 'any' },\n  { kind: 'bar', bar: 1 },\n  { kind: 'anything_else', not_bar_or_foo: 'any value' }\n];\n\nconst invalidDataList = [\n  { kind: 'foo' }, // no propery foo\n  { kind: 'bar' }, // no propery bar\n  { kind: 'foo', foo: 'any', another: 'any value' }, // additional property\n  { kind: 'bar', bar: 1, another: 'any value' }, // additional property\n  { kind: 'anything_else', foo: 'any' } // property foo not allowed\n  { kind: 'anything_else', bar: 1 } // property bar not allowed\n];\n```\n\n#### `dynamicDefaults`\n\nThis keyword allows to assign dynamic defaults to properties, such as timestamps, unique IDs etc.\n\nThis keyword only works if `useDefaults` options is used and not inside `anyOf` keywords etc., in the same way as [default keyword treated by Ajv](https://github.com/epoberezkin/ajv#assigning-defaults).\n\nThe keyword should be added on the object level. Its value should be an object with each property corresponding to a property name, in the same way as in standard `properties` keyword. The value of each property can be:\n\n- an identifier of dynamic default function (a string)\n- an object with properties `func` (an identifier) and `args` (an object with parameters that will be passed to this function during schema compilation - see examples).\n\nThe properties used in `dynamicDefaults` should not be added to `required` keyword in the same schema (or validation will fail), because unlike `default` this keyword is processed after validation.\n\nThere are several predefined dynamic default functions:\n\n- `\"timestamp\"` - current timestamp in milliseconds\n- `\"datetime\"` - current date and time as string (ISO, valid according to `date-time` format)\n- `\"date\"` - current date as string (ISO, valid according to `date` format)\n- `\"time\"` - current time as string (ISO, valid according to `time` format)\n- `\"random\"` - pseudo-random number in [0, 1) interval\n- `\"randomint\"` - pseudo-random integer number. If string is used as a property value, the function will randomly return 0 or 1. If object `{ func: 'randomint', args: { max: N } }` is used then the default will be an integer number in [0, N) interval.\n- `\"seq\"` - sequential integer number starting from 0. If string is used as a property value, the default sequence will be used. If object `{ func: 'seq', args: { name: 'foo'} }` is used then the sequence with name `\"foo\"` will be used. Sequences are global, even if different ajv instances are used.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  dynamicDefaults: {\n    ts: \"datetime\",\n    r: {func: \"randomint\", args: {max: 100}},\n    id: {func: \"seq\", args: {name: \"id\"}},\n  },\n  properties: {\n    ts: {\n      type: \"string\",\n      format: \"date-time\",\n    },\n    r: {\n      type: \"integer\",\n      minimum: 0,\n      exclusiveMaximum: 100,\n    },\n    id: {\n      type: \"integer\",\n      minimum: 0,\n    },\n  },\n}\n\nconst data = {}\najv.validate(data) // true\ndata // { ts: '2016-12-01T22:07:28.829Z', r: 25, id: 0 }\n\nconst data1 = {}\najv.validate(data1) // true\ndata1 // { ts: '2016-12-01T22:07:29.832Z', r: 68, id: 1 }\n\najv.validate(data1) // true\ndata1 // didn't change, as all properties were defined\n```\n\nWhen using the `useDefaults` option value `\"empty\"`, properties and items equal to `null` or `\"\"` (empty string) will be considered missing and assigned defaults. Use `allOf` [compound keyword](https://github.com/epoberezkin/ajv/blob/master/KEYWORDS.md#compound-keywords) to execute `dynamicDefaults` before validation.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  allOf: [\n    {\n      dynamicDefaults: {\n        ts: \"datetime\",\n        r: {func: \"randomint\", args: {min: 5, max: 100}},\n        id: {func: \"seq\", args: {name: \"id\"}},\n      },\n    },\n    {\n      properties: {\n        ts: {\n          type: \"string\",\n        },\n        r: {\n          type: \"number\",\n          minimum: 5,\n          exclusiveMaximum: 100,\n        },\n        id: {\n          type: \"integer\",\n          minimum: 0,\n        },\n      },\n    },\n  ],\n}\n\nconst data = {ts: \"\", r: null}\najv.validate(data) // true\ndata // { ts: '2016-12-01T22:07:28.829Z', r: 25, id: 0 }\n```\n\nYou can add your own dynamic default function to be recognised by this keyword:\n\n```javascript\nconst uuid = require(\"uuid\")\n\nconst def = require(\"ajv-keywords/dist/definitions/dynamicDefaults\")\ndef.DEFAULTS.uuid = () => uuid.v4\n\nconst schema = {\n  dynamicDefaults: {id: \"uuid\"},\n  properties: {id: {type: \"string\", format: \"uuid\"}},\n}\n\nconst data = {}\najv.validate(schema, data) // true\ndata // { id: 'a1183fbe-697b-4030-9bcc-cfeb282a9150' };\n\nconst data1 = {}\najv.validate(schema, data1) // true\ndata1 // { id: '5b008de7-1669-467a-a5c6-70fa244d7209' }\n```\n\nYou also can define dynamic default that accept parameters, e.g. version of uuid:\n\n```javascript\nconst uuid = require(\"uuid\")\n\nfunction getUuid(args) {\n  const version = \"v\" + ((arvs && args.v) || \"4\")\n  return uuid[version]\n}\n\nconst def = require(\"ajv-keywords/dist/definitions/dynamicDefaults\")\ndef.DEFAULTS.uuid = getUuid\n\nconst schema = {\n  dynamicDefaults: {\n    id1: \"uuid\", // v4\n    id2: {func: \"uuid\", v: 4}, // v4\n    id3: {func: \"uuid\", v: 1}, // v1\n  },\n}\n```\n\n**Please note**: dynamic default functions differented by the number of parameters they have (`function.length`). Functions that do not expect default must have one non-optional argument so that `function.length` > 0.\n\n## Security contact\n\nTo report a security vulnerability, please use the\n[Tidelift security contact](https://tidelift.com/security).\nTidelift will coordinate the fix and disclosure.\n\nPlease do NOT report security vulnerabilities via GitHub issues.\n\n## Open-source software support\n\nAjv-keywords is a part of [Tidelift subscription](https://tidelift.com/subscription/pkg/npm-ajv-keywords?utm_source=npm-ajv-keywords&utm_medium=referral&utm_campaign=readme) - it provides a centralised support to open-source software users, in addition to the support provided by software maintainers.\n\n## License\n\n[MIT](https://github.com/epoberezkin/ajv-keywords/blob/master/LICENSE)\n", "readmeFilename": "README.md", "gitHead": "85e17053e8499b97c5f5b4bb8dce87de16e5e183", "_id": "ajv-keywords@4.0.0-beta.1", "_nodeVersion": "14.14.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-L69r+w17gfiv2OeaJzutSvscG182a2w4vNHSZMgFAxdgG9+jAGPCHTEd6Z9dAMEF1brhYWP3QeNyKUNwMcwoJw==", "shasum": "f6d5e721ec2567221d9489208aa6c99ac319add8", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-4.0.0-beta.1.tgz", "fileCount": 160, "unpackedSize": 125735, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfo79PCRA9TVsSAnZWagAA6N4P/i3GoiyMU8/Kxe3oMFNl\nNceRm9fXaBx+ZUIBiHXq17Jp6S5MsniNtj2pPCfVEXlu896gGM7+E7STxzmW\n5ZhyL5ISCJCLV0P6GSRI3jwMyulrbOc7RUzGeJq2COgtlQuY3+dXGEBRfkcK\nED5QM3xYcGLNJiXrK5lI2d56+dvcr0OPDTKi07xDNFSlJZk+bLqHFaD2yXIk\nB+lfsxOo/AxV4S2809V51+tGMsAgXDem0tgPcD8cSkbs3QkF2wZb4Bx3Bop7\nIX3xdaTjaSR3Jp87GXP/7i1Bf6icu2koNT5xkXfNk8SP6wsprtz5G/lRRlmJ\nsNq2n8BJ6PAEIMD/6I4LuX/5LuDd+uM/LVo4Ay5Bw22D1bNOX6DxleDtcPVM\n5AzvhnrD1SHt8jkGtolhh+SXpW6D+v6b4HPuc51/I3sEe/JzYd6Ps7GCfbRi\nFGtDUgkcn/emxVFo0WY3HD+MYg9mKThF/oA6znyqqN0EI6DSd0hiLdesZ3EL\nXzqYKYm0SmHkoa5EpTSTV62752VMacJ8qwCyYEy+TLZexps+ta3NyqHsPsDY\nfMd1nPIArmCRcBZGVn4n5EJoqi7Ou5nwbFVMmTruAvjbspEfNTUf6YzSQFCy\nWeKzUUv7PPfZ9Rf6XeIwQInHFKdzr1rGyRdT4ycGO5D3rAQRotaZHx48LvQx\nVpFY\r\n=JIuY\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDIgpIAtblK3WKaS6neWNWy6k8Isg/fsfezthY3eCq9MgIhAMvNQoIGeSw2rNI+aSzttdDWpa6lUJAXYkZw1Q7v07iR"}]}, "_npmUser": {"name": "esp", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ajv-keywords_4.0.0-beta.1_1604566862806_0.2434480125555738"}, "_hasShrinkwrap": false}, "4.0.0-beta.2": {"name": "ajv-keywords", "version": "4.0.0-beta.2", "description": "Custom JSON-Schema keywords for Ajv validator", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "rm -rf dist && tsc", "prepublish": "npm run build", "prettier:write": "prettier --write \"./**/*.{md,json,yaml,js,ts}\"", "prettier:check": "prettier --list-different \"./**/*.{md,json,yaml,js,ts}\"", "test": "npm run build && npm run eslint && npm run test-cov", "eslint": "eslint \"src/**/*.*s\" \"spec/**/*.*s\" --ignore-pattern dotjs", "test-spec": "jest spec/*.ts", "test-cov": "jest spec/*.ts --coverage"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/ajv-keywords.git"}, "keywords": ["JSON-Schema", "ajv", "keywords"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/ajv-keywords/issues"}, "homepage": "https://github.com/epoberezkin/ajv-keywords#readme", "peerDependencies": {"ajv": "^7.0.0-beta.6"}, "devDependencies": {"@ajv-validator/config": "^0.2.3", "@types/jest": "^26.0.14", "@types/node": "^14.11.10", "@types/uuid": "^8.3.0", "@typescript-eslint/eslint-plugin": "^4.4.1", "@typescript-eslint/parser": "^4.4.1", "ajv": "^7.0.0-beta.6", "ajv-formats": "^0.3.4", "chai": "^4.2.0", "dot": "^1.1.1", "eslint": "^7.2.0", "eslint-config-prettier": "^6.13.0", "glob": "^7.1.3", "husky": "^4.3.0", "jest": "^26.5.3", "js-beautify": "^1.8.9", "json-schema-test": "^2.0.0", "lint-staged": "^10.4.2", "prettier": "^2.1.2", "ts-jest": "^26.4.1", "typescript": "^4.0.3", "uuid": "^8.1.0"}, "prettier": "@ajv-validator/config/prettierrc.json", "husky": {"hooks": {"pre-commit": "lint-staged && npm test"}}, "lint-staged": {"*.{md,json,yaml,js,ts}": "prettier --write"}, "dependencies": {"@types/chai": "^4.2.14"}, "readme": "# ajv-keywords\n\nCustom JSON-Schema keywords for [Ajv](https://github.com/epoberezkin/ajv) validator\n\n[![Build Status](https://travis-ci.org/ajv-validator/ajv-keywords.svg?branch=master)](https://travis-ci.org/ajv-validator/ajv-keywords)\n[![npm](https://img.shields.io/npm/v/ajv-keywords.svg)](https://www.npmjs.com/package/ajv-keywords)\n[![npm (beta)](https://img.shields.io/npm/v/ajv-keywords/beta)](https://www.npmjs.com/package/ajv-keywords/v/4.0.0-beta.1)\n[![npm downloads](https://img.shields.io/npm/dm/ajv-keywords.svg)](https://www.npmjs.com/package/ajv-keywords)\n[![Coverage Status](https://coveralls.io/repos/github/ajv-validator/ajv-keywords/badge.svg?branch=master)](https://coveralls.io/github/ajv-validator/ajv-keywords?branch=master)\n[![Dependabot](https://api.dependabot.com/badges/status?host=github&repo=ajv-validator/ajv-keywords)](https://app.dependabot.com/accounts/ajv-validator/repos/********)\n[![Gitter](https://img.shields.io/gitter/room/ajv-validator/ajv.svg)](https://gitter.im/ajv-validator/ajv)\n\n**Please note**: [ajv-keywords v4.0.0-beta](https://github.com/ajv-validator/ajv-keywords/releases/tag/v4.0.0-beta.0) should be used with [ajv v7 (beta)](https://github.com/ajv-validator/ajv/tree/v7-beta)\n\n## Contents\n\n- [Install](#install)\n- [Usage](#usage)\n- [Keywords](#keywords)\n  - [Types](#types)\n    - [typeof](#typeof)\n    - [instanceof](#instanceof)\n  - [Keywords for numbers](#keywords-for-numbers)\n    - [range and exclusiveRange](#range-and-exclusiverange)\n  - [Keywords for strings](#keywords-for-strings)\n    - [regexp](#regexp)\n    - [transform](#transform)<sup>\\*</sup>\n  - [Keywords for arrays](#keywords-for-arrays)\n    - [uniqueItemProperties](#uniqueitemproperties)\n  - [Keywords for objects](#keywords-for-objects)\n    - [allRequired](#allrequired)\n    - [anyRequired](#anyrequired)\n    - [oneRequired](#onerequired)\n    - [patternRequired](#patternrequired)\n    - [prohibited](#prohibited)\n    - [deepProperties](#deepproperties)\n    - [deepRequired](#deeprequired)\n    - [dynamicDefaults](#dynamicdefaults)<sup>\\*</sup>\n  - [Keywords for all types](#keywords-for-all-types)\n    - [select/selectCases/selectDefault](#selectselectcasesselectdefault)\n- [Security contact](#security-contact)\n- [Open-source software support](#open-source-software-support)\n- [License](#license)\n\n<sup>\\*</sup> - keywords that modify data\n\n## Install\n\n```\nnpm install ajv-keywords\n```\n\nTo install version 4 beta to use with [Ajv v7 beta](https://github.com/ajv-validator/ajv/tree/v7-beta):\n\n```\nnpm install ajv-keywords@beta\n```\n\n## Usage\n\nTo add all available keywords:\n\n```javascript\nconst Ajv = require(\"ajv\")\nconst ajv = new Ajv()\nrequire(\"ajv-keywords\")(ajv)\n\najv.validate({instanceof: \"RegExp\"}, /.*/) // true\najv.validate({instanceof: \"RegExp\"}, \".*\") // false\n```\n\nTo add a single keyword:\n\n```javascript\nrequire(\"ajv-keywords\")(ajv, \"instanceof\")\n```\n\nTo add multiple keywords:\n\n```javascript\nrequire(\"ajv-keywords\")(ajv, [\"typeof\", \"instanceof\"])\n```\n\nTo add a single keyword directly (to avoid adding unused code):\n\n```javascript\nrequire(\"ajv-keywords/dist/keywords/select\")(ajv, opts)\n```\n\nTo add all keywords via Ajv options:\n\n```javascript\nconst ajv = new Ajv({keywords: require(\"ajv-keywords/dist/definitions\")(opts)})\n```\n\nTo add one or several keywords via options:\n\n```javascript\nconst ajv = new Ajv({\n  keywords: [\n    require(\"ajv-keywords/dist/definitions/typeof\")(),\n    require(\"ajv-keywords/dist/definitions/instanceof\")(),\n    // select exports an array of 3 definitions - see \"select\" in docs\n    ...require(\"ajv-keywords/dist/definitions/select\")(opts),\n  ],\n})\n```\n\n`opts` is an optional object with a property `defaultMeta` - URI of meta-schema to use for keywords that use subschemas (`select` and `deepProperties`). The default is `\"http://json-schema.org/schema\"`.\n\n## Keywords\n\n### Types\n\n#### `typeof`\n\nBased on JavaScript `typeof` operation.\n\nThe value of the keyword should be a string (`\"undefined\"`, `\"string\"`, `\"number\"`, `\"object\"`, `\"function\"`, `\"boolean\"` or `\"symbol\"`) or an array of strings.\n\nTo pass validation the result of `typeof` operation on the value should be equal to the string (or one of the strings in the array).\n\n```javascript\najv.validate({typeof: \"undefined\"}, undefined) // true\najv.validate({typeof: \"undefined\"}, null) // false\najv.validate({typeof: [\"undefined\", \"object\"]}, null) // true\n```\n\n#### `instanceof`\n\nBased on JavaScript `instanceof` operation.\n\nThe value of the keyword should be a string (`\"Object\"`, `\"Array\"`, `\"Function\"`, `\"Number\"`, `\"String\"`, `\"Date\"`, `\"RegExp\"` or `\"Promise\"`) or an array of strings.\n\nTo pass validation the result of `data instanceof ...` operation on the value should be true:\n\n```javascript\najv.validate({instanceof: \"Array\"}, []) // true\najv.validate({instanceof: \"Array\"}, {}) // false\najv.validate({instanceof: [\"Array\", \"Function\"]}, function () {}) // true\n```\n\nYou can add your own constructor function to be recognised by this keyword:\n\n```javascript\nclass MyClass {}\nconst instanceofDef = require(\"ajv-keywords/dist/definitions/instanceof\")\ninstanceofDef.CONSTRUCTORS.MyClass = MyClass\najv.validate({instanceof: \"MyClass\"}, new MyClass()) // true\n```\n\n### Keywords for numbers\n\n#### `range` and `exclusiveRange`\n\nSyntax sugar for the combination of minimum and maximum keywords (or exclusiveMinimum and exclusiveMaximum), also fails schema compilation if there are no numbers in the range.\n\nThe value of these keywords must be an array consisting of two numbers, the second must be greater or equal than the first one.\n\nIf the validated value is not a number the validation passes, otherwise to pass validation the value should be greater (or equal) than the first number and smaller (or equal) than the second number in the array.\n\n```javascript\nconst schema = {type: \"number\", range: [1, 3]}\najv.validate(schema, 1) // true\najv.validate(schema, 2) // true\najv.validate(schema, 3) // true\najv.validate(schema, 0.99) // false\najv.validate(schema, 3.01) // false\n\nconst schema = {type: \"number\", exclusiveRange: [1, 3]}\najv.validate(schema, 1.01) // true\najv.validate(schema, 2) // true\najv.validate(schema, 2.99) // true\najv.validate(schema, 1) // false\najv.validate(schema, 3) // false\n```\n\n### Keywords for strings\n\n#### `regexp`\n\nThis keyword allows to use regular expressions with flags in schemas, and also without `\"u\"` flag when needed (the standard `pattern` keyword does not support flags and implies the presence of `\"u\"` flag).\n\nThis keyword applies only to strings. If the data is not a string, the validation succeeds.\n\nThe value of this keyword can be either a string (the result of `regexp.toString()`) or an object with the properties `pattern` and `flags` (the same strings that should be passed to RegExp constructor).\n\n```javascript\nconst schema = {\n  type: \"object\",\n  properties: {\n    foo: {type: \"string\", regexp: \"/foo/i\"},\n    bar: {type: \"string\", regexp: {pattern: \"bar\", flags: \"i\"}},\n  },\n}\n\nconst validData = {\n  foo: \"Food\",\n  bar: \"Barmen\",\n}\n\nconst invalidData = {\n  foo: \"fog\",\n  bar: \"bad\",\n}\n```\n\n#### `transform`\n\nThis keyword allows a string to be modified during validation.\n\nThis keyword applies only to strings. If the data is not a string, the `transform` keyword is ignored.\n\nA standalone string cannot be modified, i.e. `data = 'a'; ajv.validate(schema, data);`, because strings are passed by value\n\n**Supported transformations:**\n\n- `trim`: remove whitespace from start and end\n- `trimStart`/`trimLeft`: remove whitespace from start\n- `trimEnd`/`trimRight`: remove whitespace from end\n- `toLowerCase`: convert to lower case\n- `toUpperCase`: convert to upper case\n- `toEnumCase`: change string case to be equal to one of `enum` values in the schema\n\nTransformations are applied in the order they are listed.\n\nNote: `toEnumCase` requires that all allowed values are unique when case insensitive.\n\n**Example: multiple transformations**\n\n```javascript\nrequire(\"ajv-keywords\")(ajv, \"transform\")\n\nconst schema = {\n  type: \"array\",\n  items: {\n    type: \"string\",\n    transform: [\"trim\", \"toLowerCase\"],\n  },\n}\n\nconst data = [\"  MixCase  \"]\najv.validate(schema, data)\nconsole.log(data) // ['mixcase']\n```\n\n**Example: `enumcase`**\n\n```javascript\nrequire(\"ajv-keywords\")(ajv, [\"transform\"])\n\nconst schema = {\n  type: \"array\",\n  items: {\n    type: \"string\",\n    transform: [\"trim\", \"toEnumCase\"],\n    enum: [\"pH\"],\n  },\n}\n\nconst data = [\"ph\", \" Ph\", \"PH\", \"pH \"]\najv.validate(schema, data)\nconsole.log(data) // ['pH','pH','pH','pH']\n```\n\n### Keywords for arrays\n\n#### `uniqueItemProperties`\n\nThe keyword allows to check that some properties in array items are unique.\n\nThis keyword applies only to arrays. If the data is not an array, the validation succeeds.\n\nThe value of this keyword must be an array of strings - property names that should have unique values across all items.\n\n```javascript\nconst schema = {\n  type: \"array\",\n  uniqueItemProperties: [\"id\", \"name\"],\n}\n\nconst validData = [{id: 1}, {id: 2}, {id: 3}]\n\nconst invalidData1 = [\n  {id: 1},\n  {id: 1}, // duplicate \"id\"\n  {id: 3},\n]\n\nconst invalidData2 = [\n  {id: 1, name: \"taco\"},\n  {id: 2, name: \"taco\"}, // duplicate \"name\"\n  {id: 3, name: \"salsa\"},\n]\n```\n\nThis keyword is contributed by [@blainesch](https://github.com/blainesch).\n\n### Keywords for objects\n\n#### `allRequired`\n\nThis keyword allows to require the presence of all properties used in `properties` keyword in the same schema object.\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value of this keyword must be boolean.\n\nIf the value of the keyword is `false`, the validation succeeds.\n\nIf the value of the keyword is `true`, the validation succeeds if the data contains all properties defined in `properties` keyword (in the same schema object).\n\nIf the `properties` keyword is not present in the same schema object, schema compilation will throw exception.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  properties: {\n    foo: {type: 'number'},\n    bar: {type: 'number'}\n  }\n  allRequired: true\n};\n\nconst validData = { foo: 1, bar: 2 };\nconst alsoValidData = { foo: 1, bar: 2, baz: 3 };\n\nconst invalidDataList = [ {}, { foo: 1 }, { bar: 2 } ];\n```\n\n#### `anyRequired`\n\nThis keyword allows to require the presence of any (at least one) property from the list.\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value of this keyword must be an array of strings, each string being a property name. For data object to be valid at least one of the properties in this array should be present in the object.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  anyRequired: [\"foo\", \"bar\"],\n}\n\nconst validData = {foo: 1}\nconst alsoValidData = {foo: 1, bar: 2}\n\nconst invalidDataList = [{}, {baz: 3}]\n```\n\n#### `oneRequired`\n\nThis keyword allows to require the presence of only one property from the list.\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value of this keyword must be an array of strings, each string being a property name. For data object to be valid exactly one of the properties in this array should be present in the object.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  oneRequired: [\"foo\", \"bar\"],\n}\n\nconst validData = {foo: 1}\nconst alsoValidData = {bar: 2, baz: 3}\n\nconst invalidDataList = [{}, {baz: 3}, {foo: 1, bar: 2}]\n```\n\n#### `patternRequired`\n\nThis keyword allows to require the presence of properties that match some pattern(s).\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value of this keyword should be an array of strings, each string being a regular expression. For data object to be valid each regular expression in this array should match at least one property name in the data object.\n\nIf the array contains multiple regular expressions, more than one expression can match the same property name.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  patternRequired: [\"f.*o\", \"b.*r\"],\n}\n\nconst validData = {foo: 1, bar: 2}\nconst alsoValidData = {foobar: 3}\n\nconst invalidDataList = [{}, {foo: 1}, {bar: 2}]\n```\n\n#### `prohibited`\n\nThis keyword allows to prohibit that any of the properties in the list is present in the object.\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value of this keyword should be an array of strings, each string being a property name. For data object to be valid none of the properties in this array should be present in the object.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  prohibited: [\"foo\", \"bar\"],\n}\n\nconst validData = {baz: 1}\nconst alsoValidData = {}\n\nconst invalidDataList = [{foo: 1}, {bar: 2}, {foo: 1, bar: 2}]\n```\n\n**Please note**: `{prohibited: ['foo', 'bar']}` is equivalent to `{not: {anyRequired: ['foo', 'bar']}}` (i.e. it has the same validation result for any data).\n\n#### `deepProperties`\n\nThis keyword allows to validate deep properties (identified by JSON pointers).\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value should be an object, where keys are JSON pointers to the data, starting from the current position in data, and the values are JSON schemas. For data object to be valid the value of each JSON pointer should be valid according to the corresponding schema.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  deepProperties: {\n    \"/users/1/role\": {enum: [\"admin\"]},\n  },\n}\n\nconst validData = {\n  users: [\n    {},\n    {\n      id: 123,\n      role: \"admin\",\n    },\n  ],\n}\n\nconst alsoValidData = {\n  users: {\n    1: {\n      id: 123,\n      role: \"admin\",\n    },\n  },\n}\n\nconst invalidData = {\n  users: [\n    {},\n    {\n      id: 123,\n      role: \"user\",\n    },\n  ],\n}\n\nconst alsoInvalidData = {\n  users: {\n    1: {\n      id: 123,\n      role: \"user\",\n    },\n  },\n}\n```\n\n#### `deepRequired`\n\nThis keyword allows to check that some deep properties (identified by JSON pointers) are available.\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value should be an array of JSON pointers to the data, starting from the current position in data. For data object to be valid each JSON pointer should be some existing part of the data.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  deepRequired: [\"/users/1/role\"],\n}\n\nconst validData = {\n  users: [\n    {},\n    {\n      id: 123,\n      role: \"admin\",\n    },\n  ],\n}\n\nconst invalidData = {\n  users: [\n    {},\n    {\n      id: 123,\n    },\n  ],\n}\n```\n\nSee [json-schema-org/json-schema-spec#203](https://github.com/json-schema-org/json-schema-spec/issues/203#issue-197211916) for an example of the equivalent schema without `deepRequired` keyword.\n\n### Keywords for all types\n\n#### `select`/`selectCases`/`selectDefault`\n\nThese keywords allow to choose the schema to validate the data based on the value of some property in the validated data.\n\nThese keywords must be present in the same schema object (`selectDefault` is optional).\n\nThe value of `select` keyword should be a [\\$data reference](https://github.com/ajv-validator/ajv/blob/v7-beta/docs/validation.md#data-reference) that points to any primitive JSON type (string, number, boolean or null) in the data that is validated. You can also use a constant of primitive type as the value of this keyword (e.g., for debugging purposes).\n\nThe value of `selectCases` keyword must be an object where each property name is a possible string representation of the value of `select` keyword and each property value is a corresponding schema (from draft-06 it can be boolean) that must be used to validate the data.\n\nThe value of `selectDefault` keyword is a schema (also can be boolean) that must be used to validate the data in case `selectCases` has no key equal to the stringified value of `select` keyword.\n\nThe validation succeeds in one of the following cases:\n\n- the validation of data using selected schema succeeds,\n- none of the schemas is selected for validation,\n- the value of select is undefined (no property in the data that the data reference points to).\n\nIf `select` value (in data) is not a primitive type the validation fails.\n\nThis keyword correctly tracks evaluated properties and items to work with `unevaluatedProperties` and `unevaluatedItems` keywords - only properties and items from the subschema that was used (one of `selectCases` subschemas or `selectDefault` subschema) are marked as evaluated.\n\n**Please note**: these keywords require Ajv `$data` option to support [\\$data reference](https://github.com/epoberezkin/ajv/tree/5.0.2-beta.0#data-reference).\n\n```javascript\nrequire('ajv-keywords')(ajv, 'select');\n\nconst schema = {\n  type: \"object\",\n  required: ['kind'],\n  properties: {\n    kind: { type: 'string' }\n  },\n  select: { $data: '0/kind' },\n  selectCases: {\n    foo: {\n      required: ['foo'],\n      properties: {\n        kind: {},\n        foo: { type: 'string' }\n      },\n      additionalProperties: false\n    },\n    bar: {\n      required: ['bar'],\n      properties: {\n        kind: {},\n        bar: { type: 'number' }\n      },\n      additionalProperties: false\n    }\n  },\n  selectDefault: {\n    propertyNames: {\n      not: { enum: ['foo', 'bar'] }\n    }\n  }\n};\n\nconst validDataList = [\n  { kind: 'foo', foo: 'any' },\n  { kind: 'bar', bar: 1 },\n  { kind: 'anything_else', not_bar_or_foo: 'any value' }\n];\n\nconst invalidDataList = [\n  { kind: 'foo' }, // no propery foo\n  { kind: 'bar' }, // no propery bar\n  { kind: 'foo', foo: 'any', another: 'any value' }, // additional property\n  { kind: 'bar', bar: 1, another: 'any value' }, // additional property\n  { kind: 'anything_else', foo: 'any' } // property foo not allowed\n  { kind: 'anything_else', bar: 1 } // property bar not allowed\n];\n```\n\n#### `dynamicDefaults`\n\nThis keyword allows to assign dynamic defaults to properties, such as timestamps, unique IDs etc.\n\nThis keyword only works if `useDefaults` options is used and not inside `anyOf` keywords etc., in the same way as [default keyword treated by Ajv](https://github.com/epoberezkin/ajv#assigning-defaults).\n\nThe keyword should be added on the object level. Its value should be an object with each property corresponding to a property name, in the same way as in standard `properties` keyword. The value of each property can be:\n\n- an identifier of dynamic default function (a string)\n- an object with properties `func` (an identifier) and `args` (an object with parameters that will be passed to this function during schema compilation - see examples).\n\nThe properties used in `dynamicDefaults` should not be added to `required` keyword in the same schema (or validation will fail), because unlike `default` this keyword is processed after validation.\n\nThere are several predefined dynamic default functions:\n\n- `\"timestamp\"` - current timestamp in milliseconds\n- `\"datetime\"` - current date and time as string (ISO, valid according to `date-time` format)\n- `\"date\"` - current date as string (ISO, valid according to `date` format)\n- `\"time\"` - current time as string (ISO, valid according to `time` format)\n- `\"random\"` - pseudo-random number in [0, 1) interval\n- `\"randomint\"` - pseudo-random integer number. If string is used as a property value, the function will randomly return 0 or 1. If object `{ func: 'randomint', args: { max: N } }` is used then the default will be an integer number in [0, N) interval.\n- `\"seq\"` - sequential integer number starting from 0. If string is used as a property value, the default sequence will be used. If object `{ func: 'seq', args: { name: 'foo'} }` is used then the sequence with name `\"foo\"` will be used. Sequences are global, even if different ajv instances are used.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  dynamicDefaults: {\n    ts: \"datetime\",\n    r: {func: \"randomint\", args: {max: 100}},\n    id: {func: \"seq\", args: {name: \"id\"}},\n  },\n  properties: {\n    ts: {\n      type: \"string\",\n      format: \"date-time\",\n    },\n    r: {\n      type: \"integer\",\n      minimum: 0,\n      exclusiveMaximum: 100,\n    },\n    id: {\n      type: \"integer\",\n      minimum: 0,\n    },\n  },\n}\n\nconst data = {}\najv.validate(data) // true\ndata // { ts: '2016-12-01T22:07:28.829Z', r: 25, id: 0 }\n\nconst data1 = {}\najv.validate(data1) // true\ndata1 // { ts: '2016-12-01T22:07:29.832Z', r: 68, id: 1 }\n\najv.validate(data1) // true\ndata1 // didn't change, as all properties were defined\n```\n\nWhen using the `useDefaults` option value `\"empty\"`, properties and items equal to `null` or `\"\"` (empty string) will be considered missing and assigned defaults. Use `allOf` [compound keyword](https://github.com/epoberezkin/ajv/blob/master/KEYWORDS.md#compound-keywords) to execute `dynamicDefaults` before validation.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  allOf: [\n    {\n      dynamicDefaults: {\n        ts: \"datetime\",\n        r: {func: \"randomint\", args: {min: 5, max: 100}},\n        id: {func: \"seq\", args: {name: \"id\"}},\n      },\n    },\n    {\n      properties: {\n        ts: {\n          type: \"string\",\n        },\n        r: {\n          type: \"number\",\n          minimum: 5,\n          exclusiveMaximum: 100,\n        },\n        id: {\n          type: \"integer\",\n          minimum: 0,\n        },\n      },\n    },\n  ],\n}\n\nconst data = {ts: \"\", r: null}\najv.validate(data) // true\ndata // { ts: '2016-12-01T22:07:28.829Z', r: 25, id: 0 }\n```\n\nYou can add your own dynamic default function to be recognised by this keyword:\n\n```javascript\nconst uuid = require(\"uuid\")\n\nconst def = require(\"ajv-keywords/dist/definitions/dynamicDefaults\")\ndef.DEFAULTS.uuid = () => uuid.v4\n\nconst schema = {\n  dynamicDefaults: {id: \"uuid\"},\n  properties: {id: {type: \"string\", format: \"uuid\"}},\n}\n\nconst data = {}\najv.validate(schema, data) // true\ndata // { id: 'a1183fbe-697b-4030-9bcc-cfeb282a9150' };\n\nconst data1 = {}\najv.validate(schema, data1) // true\ndata1 // { id: '5b008de7-1669-467a-a5c6-70fa244d7209' }\n```\n\nYou also can define dynamic default that accept parameters, e.g. version of uuid:\n\n```javascript\nconst uuid = require(\"uuid\")\n\nfunction getUuid(args) {\n  const version = \"v\" + ((arvs && args.v) || \"4\")\n  return uuid[version]\n}\n\nconst def = require(\"ajv-keywords/dist/definitions/dynamicDefaults\")\ndef.DEFAULTS.uuid = getUuid\n\nconst schema = {\n  dynamicDefaults: {\n    id1: \"uuid\", // v4\n    id2: {func: \"uuid\", v: 4}, // v4\n    id3: {func: \"uuid\", v: 1}, // v1\n  },\n}\n```\n\n**Please note**: dynamic default functions differented by the number of parameters they have (`function.length`). Functions that do not expect default must have one non-optional argument so that `function.length` > 0.\n\n## Security contact\n\nTo report a security vulnerability, please use the\n[Tidelift security contact](https://tidelift.com/security).\nTidelift will coordinate the fix and disclosure.\n\nPlease do NOT report security vulnerabilities via GitHub issues.\n\n## Open-source software support\n\nAjv-keywords is a part of [Tidelift subscription](https://tidelift.com/subscription/pkg/npm-ajv-keywords?utm_source=npm-ajv-keywords&utm_medium=referral&utm_campaign=readme) - it provides a centralised support to open-source software users, in addition to the support provided by software maintainers.\n\n## License\n\n[MIT](https://github.com/epoberezkin/ajv-keywords/blob/master/LICENSE)\n", "readmeFilename": "README.md", "gitHead": "5644d48a7db07dea2554d4b2fe21b201c9b8a938", "_id": "ajv-keywords@4.0.0-beta.2", "_nodeVersion": "14.14.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-XNxBBpjezomZlXKOO4KVu/rrgEHsjEdj+CQNA0ENsLxBbthcoQDQ4o4Rf8xdudCU6iUBsuE8n4izwN5Og7C3xw==", "shasum": "cf60b4fa8e64c39e51e5529f9ce014265554c3ae", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-4.0.0-beta.2.tgz", "fileCount": 159, "unpackedSize": 127056, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfs406CRA9TVsSAnZWagAAa8wP/1vkfq+CUaWfiWNzN3TM\nNhL4bjpX0mT+6TQdL1cGZbRF47lKBnYWxA2ZwKoyuIRNu6piFDKnbcWQq9DY\n0AXjAKPo0rCQzAdw7jviE+lUAHLdFvOVfPudKR5XTvi09YdBK3OOoFyFp42D\nwP20joRnFoUdSD5SXSq245h+M4jOnz23ezwl5h/q4uFl+F3zpWrwJWk+DDu/\n/ImKu+aWBnyV45MI/I29xCImErXw4ZiApskFXl8FsMW1cQrmRHyRrJJqpW7Y\nLlLmXQtNBrbS1mywoDaQUSHl9HjB2pJ2uaPQziCLHyt6v/xPLmVDapeiZClI\nm8q31LWSB//uicVunaIkPQwjUZ86nAtwk1d0k+u9PUE00CkfOrObIgShqIbB\nGGwy6AzmV3RX0GRLxuZ/OUdRuMxKV1k5q4Wlxc3a+DFZb/GN8WR5pHeuUgUi\nh+grFyjsazrbsf4YCnm4kgk1NiAOKdv/W7IiQaagS3ygd5CgL/IqJ4/q1INN\nFXhFUfmmkytu0wyVH7uheJspWZApZlCV9YIGCDtvKBem06BQQojbi4P4a4KJ\nKSyRf9R5e152bEepkIRjHRrfwrSRnnK9VvXsh/zivSqUVoBzfvhahUQB7VNS\nNXUHk3TOFfr+rzVZShnC2ZsA9+f91MmxVJDLRZUkuSu2gLveze4ZJYdKiW/5\nTclT\r\n=1Pee\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDuI9RCl32QlXXjWcoOvUs0rHdu43qUsWOVjL6V8BqthgIgX/T9SKPjfizBKf2ZoVbFbvx8HP632wF8bfzJTIsKetg="}]}, "_npmUser": {"name": "esp", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ajv-keywords_4.0.0-beta.2_1605602617955_0.24919677111512661"}, "_hasShrinkwrap": false}, "4.0.0-beta.3": {"name": "ajv-keywords", "version": "4.0.0-beta.3", "description": "Custom JSON-Schema keywords for Ajv validator", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "rm -rf dist && tsc", "prepublish": "npm run build", "prettier:write": "prettier --write \"./**/*.{md,json,yaml,js,ts}\"", "prettier:check": "prettier --list-different \"./**/*.{md,json,yaml,js,ts}\"", "test": "npm link && npm link ajv-keywords && npm run eslint && npm run test-cov", "eslint": "eslint \"src/**/*.*s\" \"spec/**/*.*s\"", "test-spec": "jest spec/*.ts", "test-cov": "jest spec/*.ts --coverage"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/ajv-keywords.git"}, "keywords": ["JSON-Schema", "ajv", "keywords"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/ajv-keywords/issues"}, "homepage": "https://github.com/epoberezkin/ajv-keywords#readme", "peerDependencies": {"ajv": "^7.0.0-beta.8"}, "devDependencies": {"@ajv-validator/config": "^0.2.3", "@types/jest": "^26.0.14", "@types/node": "^14.11.10", "@types/uuid": "^8.3.0", "@typescript-eslint/eslint-plugin": "^4.4.1", "@typescript-eslint/parser": "^4.4.1", "ajv": "^7.0.0-beta.8", "ajv-formats": "^0.3.4", "chai": "^4.2.0", "dot": "^1.1.1", "eslint": "^7.2.0", "eslint-config-prettier": "^6.13.0", "glob": "^7.1.3", "husky": "^4.3.0", "jest": "^26.5.3", "js-beautify": "^1.8.9", "json-schema-test": "^2.0.0", "lint-staged": "^10.4.2", "prettier": "^2.1.2", "ts-jest": "^26.4.1", "typescript": "^4.0.3", "uuid": "^8.1.0"}, "prettier": "@ajv-validator/config/prettierrc.json", "husky": {"hooks": {"pre-commit": "lint-staged && npm test"}}, "lint-staged": {"*.{md,json,yaml,js,ts}": "prettier --write"}, "dependencies": {"@types/chai": "^4.2.14"}, "readme": "# ajv-keywords\n\nCustom JSON-Schema keywords for [Ajv](https://github.com/epoberezkin/ajv) validator\n\n[![build](https://github.com/ajv-validator/ajv-keywords/workflows/build/badge.svg)](https://github.com/ajv-validator/ajv-keywords/actions?query=workflow%3Abuild)\n[![npm](https://img.shields.io/npm/v/ajv-keywords.svg)](https://www.npmjs.com/package/ajv-keywords)\n[![npm (beta)](https://img.shields.io/npm/v/ajv-keywords/beta)](https://www.npmjs.com/package/ajv-keywords/v/4.0.0-beta.3)\n[![npm downloads](https://img.shields.io/npm/dm/ajv-keywords.svg)](https://www.npmjs.com/package/ajv-keywords)\n[![Coverage Status](https://coveralls.io/repos/github/ajv-validator/ajv-keywords/badge.svg?branch=master)](https://coveralls.io/github/ajv-validator/ajv-keywords?branch=master)\n[![Gitter](https://img.shields.io/gitter/room/ajv-validator/ajv.svg)](https://gitter.im/ajv-validator/ajv)\n\n**Please note**: [ajv-keywords v4.0.0-beta](https://github.com/ajv-validator/ajv-keywords/releases/tag/v4.0.0-beta.0) should be used with [ajv v7 (beta)](https://github.com/ajv-validator/ajv/tree/v7-beta)\n\n## Contents\n\n- [Install](#install)\n- [Usage](#usage)\n- [Keywords](#keywords)\n  - [Types](#types)\n    - [typeof](#typeof)\n    - [instanceof](#instanceof)<sup>\\+</sup>\n  - [Keywords for numbers](#keywords-for-numbers)\n    - [range and exclusiveRange](#range-and-exclusiverange)\n  - [Keywords for strings](#keywords-for-strings)\n    - [regexp](#regexp)\n    - [transform](#transform)<sup>\\*</sup>\n  - [Keywords for arrays](#keywords-for-arrays)\n    - [uniqueItemProperties](#uniqueitemproperties)<sup>\\+</sup>\n  - [Keywords for objects](#keywords-for-objects)\n    - [allRequired](#allrequired)\n    - [anyRequired](#anyrequired)\n    - [oneRequired](#onerequired)\n    - [patternRequired](#patternrequired)\n    - [prohibited](#prohibited)\n    - [deepProperties](#deepproperties)\n    - [deepRequired](#deeprequired)\n    - [dynamicDefaults](#dynamicdefaults)<sup>\\*</sup><sup>\\+</sup>\n  - [Keywords for all types](#keywords-for-all-types)\n    - [select/selectCases/selectDefault](#selectselectcasesselectdefault)\n- [Security contact](#security-contact)\n- [Open-source software support](#open-source-software-support)\n- [License](#license)\n\n<sup>\\*</sup> - keywords that modify data\n<sup>\\+</sup> - keywords that are not supported in [standalone validation code](https://github.com/ajv-validator/ajv/blob/master/docs/standalone.md)\n\n## Install\n\n```\nnpm install ajv-keywords\n```\n\nTo install version 4 beta to use with [Ajv v7 beta](https://github.com/ajv-validator/ajv/tree/v7-beta):\n\n```\nnpm install ajv-keywords@beta\n```\n\n## Usage\n\nTo add all available keywords:\n\n```javascript\nconst Ajv = require(\"ajv\")\nconst ajv = new Ajv()\nrequire(\"ajv-keywords\")(ajv)\n\najv.validate({instanceof: \"RegExp\"}, /.*/) // true\najv.validate({instanceof: \"RegExp\"}, \".*\") // false\n```\n\nTo add a single keyword:\n\n```javascript\nrequire(\"ajv-keywords\")(ajv, \"instanceof\")\n```\n\nTo add multiple keywords:\n\n```javascript\nrequire(\"ajv-keywords\")(ajv, [\"typeof\", \"instanceof\"])\n```\n\nTo add a single keyword directly (to avoid adding unused code):\n\n```javascript\nrequire(\"ajv-keywords/dist/keywords/select\")(ajv, opts)\n```\n\nTo add all keywords via Ajv options:\n\n```javascript\nconst ajv = new Ajv({keywords: require(\"ajv-keywords/dist/definitions\")(opts)})\n```\n\nTo add one or several keywords via options:\n\n```javascript\nconst ajv = new Ajv({\n  keywords: [\n    require(\"ajv-keywords/dist/definitions/typeof\")(),\n    require(\"ajv-keywords/dist/definitions/instanceof\")(),\n    // select exports an array of 3 definitions - see \"select\" in docs\n    ...require(\"ajv-keywords/dist/definitions/select\")(opts),\n  ],\n})\n```\n\n`opts` is an optional object with a property `defaultMeta` - URI of meta-schema to use for keywords that use subschemas (`select` and `deepProperties`). The default is `\"http://json-schema.org/schema\"`.\n\n## Keywords\n\n### Types\n\n#### `typeof`\n\nBased on JavaScript `typeof` operation.\n\nThe value of the keyword should be a string (`\"undefined\"`, `\"string\"`, `\"number\"`, `\"object\"`, `\"function\"`, `\"boolean\"` or `\"symbol\"`) or an array of strings.\n\nTo pass validation the result of `typeof` operation on the value should be equal to the string (or one of the strings in the array).\n\n```javascript\najv.validate({typeof: \"undefined\"}, undefined) // true\najv.validate({typeof: \"undefined\"}, null) // false\najv.validate({typeof: [\"undefined\", \"object\"]}, null) // true\n```\n\n#### `instanceof`\n\nBased on JavaScript `instanceof` operation.\n\nThe value of the keyword should be a string (`\"Object\"`, `\"Array\"`, `\"Function\"`, `\"Number\"`, `\"String\"`, `\"Date\"`, `\"RegExp\"` or `\"Promise\"`) or an array of strings.\n\nTo pass validation the result of `data instanceof ...` operation on the value should be true:\n\n```javascript\najv.validate({instanceof: \"Array\"}, []) // true\najv.validate({instanceof: \"Array\"}, {}) // false\najv.validate({instanceof: [\"Array\", \"Function\"]}, function () {}) // true\n```\n\nYou can add your own constructor function to be recognised by this keyword:\n\n```javascript\nclass MyClass {}\nconst instanceofDef = require(\"ajv-keywords/dist/definitions/instanceof\")\ninstanceofDef.CONSTRUCTORS.MyClass = MyClass\najv.validate({instanceof: \"MyClass\"}, new MyClass()) // true\n```\n\n**Please note**: currently `instanceof` is not supported in [standalone validation code](https://github.com/ajv-validator/ajv/blob/master/docs/standalone.md) - it has to be implemented as [`code` keyword](https://github.com/ajv-validator/ajv/blob/master/docs/keywords.md#define-keyword-with-code-generation-function) to support it (PR is welcome).\n\n### Keywords for numbers\n\n#### `range` and `exclusiveRange`\n\nSyntax sugar for the combination of minimum and maximum keywords (or exclusiveMinimum and exclusiveMaximum), also fails schema compilation if there are no numbers in the range.\n\nThe value of these keywords must be an array consisting of two numbers, the second must be greater or equal than the first one.\n\nIf the validated value is not a number the validation passes, otherwise to pass validation the value should be greater (or equal) than the first number and smaller (or equal) than the second number in the array.\n\n```javascript\nconst schema = {type: \"number\", range: [1, 3]}\najv.validate(schema, 1) // true\najv.validate(schema, 2) // true\najv.validate(schema, 3) // true\najv.validate(schema, 0.99) // false\najv.validate(schema, 3.01) // false\n\nconst schema = {type: \"number\", exclusiveRange: [1, 3]}\najv.validate(schema, 1.01) // true\najv.validate(schema, 2) // true\najv.validate(schema, 2.99) // true\najv.validate(schema, 1) // false\najv.validate(schema, 3) // false\n```\n\n### Keywords for strings\n\n#### `regexp`\n\nThis keyword allows to use regular expressions with flags in schemas, and also without `\"u\"` flag when needed (the standard `pattern` keyword does not support flags and implies the presence of `\"u\"` flag).\n\nThis keyword applies only to strings. If the data is not a string, the validation succeeds.\n\nThe value of this keyword can be either a string (the result of `regexp.toString()`) or an object with the properties `pattern` and `flags` (the same strings that should be passed to RegExp constructor).\n\n```javascript\nconst schema = {\n  type: \"object\",\n  properties: {\n    foo: {type: \"string\", regexp: \"/foo/i\"},\n    bar: {type: \"string\", regexp: {pattern: \"bar\", flags: \"i\"}},\n  },\n}\n\nconst validData = {\n  foo: \"Food\",\n  bar: \"Barmen\",\n}\n\nconst invalidData = {\n  foo: \"fog\",\n  bar: \"bad\",\n}\n```\n\n#### `transform`\n\nThis keyword allows a string to be modified during validation.\n\nThis keyword applies only to strings. If the data is not a string, the `transform` keyword is ignored.\n\nA standalone string cannot be modified, i.e. `data = 'a'; ajv.validate(schema, data);`, because strings are passed by value\n\n**Supported transformations:**\n\n- `trim`: remove whitespace from start and end\n- `trimStart`/`trimLeft`: remove whitespace from start\n- `trimEnd`/`trimRight`: remove whitespace from end\n- `toLowerCase`: convert to lower case\n- `toUpperCase`: convert to upper case\n- `toEnumCase`: change string case to be equal to one of `enum` values in the schema\n\nTransformations are applied in the order they are listed.\n\nNote: `toEnumCase` requires that all allowed values are unique when case insensitive.\n\n**Example: multiple transformations**\n\n```javascript\nrequire(\"ajv-keywords\")(ajv, \"transform\")\n\nconst schema = {\n  type: \"array\",\n  items: {\n    type: \"string\",\n    transform: [\"trim\", \"toLowerCase\"],\n  },\n}\n\nconst data = [\"  MixCase  \"]\najv.validate(schema, data)\nconsole.log(data) // ['mixcase']\n```\n\n**Example: `enumcase`**\n\n```javascript\nrequire(\"ajv-keywords\")(ajv, [\"transform\"])\n\nconst schema = {\n  type: \"array\",\n  items: {\n    type: \"string\",\n    transform: [\"trim\", \"toEnumCase\"],\n    enum: [\"pH\"],\n  },\n}\n\nconst data = [\"ph\", \" Ph\", \"PH\", \"pH \"]\najv.validate(schema, data)\nconsole.log(data) // ['pH','pH','pH','pH']\n```\n\n### Keywords for arrays\n\n#### `uniqueItemProperties`\n\nThe keyword allows to check that some properties in array items are unique.\n\nThis keyword applies only to arrays. If the data is not an array, the validation succeeds.\n\nThe value of this keyword must be an array of strings - property names that should have unique values across all items.\n\n```javascript\nconst schema = {\n  type: \"array\",\n  uniqueItemProperties: [\"id\", \"name\"],\n}\n\nconst validData = [{id: 1}, {id: 2}, {id: 3}]\n\nconst invalidData1 = [\n  {id: 1},\n  {id: 1}, // duplicate \"id\"\n  {id: 3},\n]\n\nconst invalidData2 = [\n  {id: 1, name: \"taco\"},\n  {id: 2, name: \"taco\"}, // duplicate \"name\"\n  {id: 3, name: \"salsa\"},\n]\n```\n\nThis keyword is contributed by [@blainesch](https://github.com/blainesch).\n\n**Please note**: currently `uniqueItemProperties` is not supported in [standalone validation code](https://github.com/ajv-validator/ajv/blob/master/docs/standalone.md) - it has to be implemented as [`code` keyword](https://github.com/ajv-validator/ajv/blob/master/docs/keywords.md#define-keyword-with-code-generation-function) to support it (PR is welcome).\n\n### Keywords for objects\n\n#### `allRequired`\n\nThis keyword allows to require the presence of all properties used in `properties` keyword in the same schema object.\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value of this keyword must be boolean.\n\nIf the value of the keyword is `false`, the validation succeeds.\n\nIf the value of the keyword is `true`, the validation succeeds if the data contains all properties defined in `properties` keyword (in the same schema object).\n\nIf the `properties` keyword is not present in the same schema object, schema compilation will throw exception.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  properties: {\n    foo: {type: 'number'},\n    bar: {type: 'number'}\n  }\n  allRequired: true\n};\n\nconst validData = { foo: 1, bar: 2 };\nconst alsoValidData = { foo: 1, bar: 2, baz: 3 };\n\nconst invalidDataList = [ {}, { foo: 1 }, { bar: 2 } ];\n```\n\n#### `anyRequired`\n\nThis keyword allows to require the presence of any (at least one) property from the list.\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value of this keyword must be an array of strings, each string being a property name. For data object to be valid at least one of the properties in this array should be present in the object.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  anyRequired: [\"foo\", \"bar\"],\n}\n\nconst validData = {foo: 1}\nconst alsoValidData = {foo: 1, bar: 2}\n\nconst invalidDataList = [{}, {baz: 3}]\n```\n\n#### `oneRequired`\n\nThis keyword allows to require the presence of only one property from the list.\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value of this keyword must be an array of strings, each string being a property name. For data object to be valid exactly one of the properties in this array should be present in the object.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  oneRequired: [\"foo\", \"bar\"],\n}\n\nconst validData = {foo: 1}\nconst alsoValidData = {bar: 2, baz: 3}\n\nconst invalidDataList = [{}, {baz: 3}, {foo: 1, bar: 2}]\n```\n\n#### `patternRequired`\n\nThis keyword allows to require the presence of properties that match some pattern(s).\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value of this keyword should be an array of strings, each string being a regular expression. For data object to be valid each regular expression in this array should match at least one property name in the data object.\n\nIf the array contains multiple regular expressions, more than one expression can match the same property name.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  patternRequired: [\"f.*o\", \"b.*r\"],\n}\n\nconst validData = {foo: 1, bar: 2}\nconst alsoValidData = {foobar: 3}\n\nconst invalidDataList = [{}, {foo: 1}, {bar: 2}]\n```\n\n#### `prohibited`\n\nThis keyword allows to prohibit that any of the properties in the list is present in the object.\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value of this keyword should be an array of strings, each string being a property name. For data object to be valid none of the properties in this array should be present in the object.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  prohibited: [\"foo\", \"bar\"],\n}\n\nconst validData = {baz: 1}\nconst alsoValidData = {}\n\nconst invalidDataList = [{foo: 1}, {bar: 2}, {foo: 1, bar: 2}]\n```\n\n**Please note**: `{prohibited: ['foo', 'bar']}` is equivalent to `{not: {anyRequired: ['foo', 'bar']}}` (i.e. it has the same validation result for any data).\n\n#### `deepProperties`\n\nThis keyword allows to validate deep properties (identified by JSON pointers).\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value should be an object, where keys are JSON pointers to the data, starting from the current position in data, and the values are JSON schemas. For data object to be valid the value of each JSON pointer should be valid according to the corresponding schema.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  deepProperties: {\n    \"/users/1/role\": {enum: [\"admin\"]},\n  },\n}\n\nconst validData = {\n  users: [\n    {},\n    {\n      id: 123,\n      role: \"admin\",\n    },\n  ],\n}\n\nconst alsoValidData = {\n  users: {\n    1: {\n      id: 123,\n      role: \"admin\",\n    },\n  },\n}\n\nconst invalidData = {\n  users: [\n    {},\n    {\n      id: 123,\n      role: \"user\",\n    },\n  ],\n}\n\nconst alsoInvalidData = {\n  users: {\n    1: {\n      id: 123,\n      role: \"user\",\n    },\n  },\n}\n```\n\n#### `deepRequired`\n\nThis keyword allows to check that some deep properties (identified by JSON pointers) are available.\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value should be an array of JSON pointers to the data, starting from the current position in data. For data object to be valid each JSON pointer should be some existing part of the data.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  deepRequired: [\"/users/1/role\"],\n}\n\nconst validData = {\n  users: [\n    {},\n    {\n      id: 123,\n      role: \"admin\",\n    },\n  ],\n}\n\nconst invalidData = {\n  users: [\n    {},\n    {\n      id: 123,\n    },\n  ],\n}\n```\n\nSee [json-schema-org/json-schema-spec#203](https://github.com/json-schema-org/json-schema-spec/issues/203#issue-197211916) for an example of the equivalent schema without `deepRequired` keyword.\n\n### Keywords for all types\n\n#### `select`/`selectCases`/`selectDefault`\n\nThese keywords allow to choose the schema to validate the data based on the value of some property in the validated data.\n\nThese keywords must be present in the same schema object (`selectDefault` is optional).\n\nThe value of `select` keyword should be a [\\$data reference](https://github.com/ajv-validator/ajv/blob/v7-beta/docs/validation.md#data-reference) that points to any primitive JSON type (string, number, boolean or null) in the data that is validated. You can also use a constant of primitive type as the value of this keyword (e.g., for debugging purposes).\n\nThe value of `selectCases` keyword must be an object where each property name is a possible string representation of the value of `select` keyword and each property value is a corresponding schema (from draft-06 it can be boolean) that must be used to validate the data.\n\nThe value of `selectDefault` keyword is a schema (also can be boolean) that must be used to validate the data in case `selectCases` has no key equal to the stringified value of `select` keyword.\n\nThe validation succeeds in one of the following cases:\n\n- the validation of data using selected schema succeeds,\n- none of the schemas is selected for validation,\n- the value of select is undefined (no property in the data that the data reference points to).\n\nIf `select` value (in data) is not a primitive type the validation fails.\n\nThis keyword correctly tracks evaluated properties and items to work with `unevaluatedProperties` and `unevaluatedItems` keywords - only properties and items from the subschema that was used (one of `selectCases` subschemas or `selectDefault` subschema) are marked as evaluated.\n\n**Please note**: these keywords require Ajv `$data` option to support [\\$data reference](https://github.com/epoberezkin/ajv/tree/5.0.2-beta.0#data-reference).\n\n```javascript\nrequire('ajv-keywords')(ajv, 'select');\n\nconst schema = {\n  type: \"object\",\n  required: ['kind'],\n  properties: {\n    kind: { type: 'string' }\n  },\n  select: { $data: '0/kind' },\n  selectCases: {\n    foo: {\n      required: ['foo'],\n      properties: {\n        kind: {},\n        foo: { type: 'string' }\n      },\n      additionalProperties: false\n    },\n    bar: {\n      required: ['bar'],\n      properties: {\n        kind: {},\n        bar: { type: 'number' }\n      },\n      additionalProperties: false\n    }\n  },\n  selectDefault: {\n    propertyNames: {\n      not: { enum: ['foo', 'bar'] }\n    }\n  }\n};\n\nconst validDataList = [\n  { kind: 'foo', foo: 'any' },\n  { kind: 'bar', bar: 1 },\n  { kind: 'anything_else', not_bar_or_foo: 'any value' }\n];\n\nconst invalidDataList = [\n  { kind: 'foo' }, // no propery foo\n  { kind: 'bar' }, // no propery bar\n  { kind: 'foo', foo: 'any', another: 'any value' }, // additional property\n  { kind: 'bar', bar: 1, another: 'any value' }, // additional property\n  { kind: 'anything_else', foo: 'any' } // property foo not allowed\n  { kind: 'anything_else', bar: 1 } // property bar not allowed\n];\n```\n\n#### `dynamicDefaults`\n\nThis keyword allows to assign dynamic defaults to properties, such as timestamps, unique IDs etc.\n\nThis keyword only works if `useDefaults` options is used and not inside `anyOf` keywords etc., in the same way as [default keyword treated by Ajv](https://github.com/epoberezkin/ajv#assigning-defaults).\n\nThe keyword should be added on the object level. Its value should be an object with each property corresponding to a property name, in the same way as in standard `properties` keyword. The value of each property can be:\n\n- an identifier of dynamic default function (a string)\n- an object with properties `func` (an identifier) and `args` (an object with parameters that will be passed to this function during schema compilation - see examples).\n\nThe properties used in `dynamicDefaults` should not be added to `required` keyword in the same schema (or validation will fail), because unlike `default` this keyword is processed after validation.\n\nThere are several predefined dynamic default functions:\n\n- `\"timestamp\"` - current timestamp in milliseconds\n- `\"datetime\"` - current date and time as string (ISO, valid according to `date-time` format)\n- `\"date\"` - current date as string (ISO, valid according to `date` format)\n- `\"time\"` - current time as string (ISO, valid according to `time` format)\n- `\"random\"` - pseudo-random number in [0, 1) interval\n- `\"randomint\"` - pseudo-random integer number. If string is used as a property value, the function will randomly return 0 or 1. If object `{ func: 'randomint', args: { max: N } }` is used then the default will be an integer number in [0, N) interval.\n- `\"seq\"` - sequential integer number starting from 0. If string is used as a property value, the default sequence will be used. If object `{ func: 'seq', args: { name: 'foo'} }` is used then the sequence with name `\"foo\"` will be used. Sequences are global, even if different ajv instances are used.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  dynamicDefaults: {\n    ts: \"datetime\",\n    r: {func: \"randomint\", args: {max: 100}},\n    id: {func: \"seq\", args: {name: \"id\"}},\n  },\n  properties: {\n    ts: {\n      type: \"string\",\n      format: \"date-time\",\n    },\n    r: {\n      type: \"integer\",\n      minimum: 0,\n      exclusiveMaximum: 100,\n    },\n    id: {\n      type: \"integer\",\n      minimum: 0,\n    },\n  },\n}\n\nconst data = {}\najv.validate(data) // true\ndata // { ts: '2016-12-01T22:07:28.829Z', r: 25, id: 0 }\n\nconst data1 = {}\najv.validate(data1) // true\ndata1 // { ts: '2016-12-01T22:07:29.832Z', r: 68, id: 1 }\n\najv.validate(data1) // true\ndata1 // didn't change, as all properties were defined\n```\n\nWhen using the `useDefaults` option value `\"empty\"`, properties and items equal to `null` or `\"\"` (empty string) will be considered missing and assigned defaults. Use `allOf` [compound keyword](https://github.com/epoberezkin/ajv/blob/master/KEYWORDS.md#compound-keywords) to execute `dynamicDefaults` before validation.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  allOf: [\n    {\n      dynamicDefaults: {\n        ts: \"datetime\",\n        r: {func: \"randomint\", args: {min: 5, max: 100}},\n        id: {func: \"seq\", args: {name: \"id\"}},\n      },\n    },\n    {\n      properties: {\n        ts: {\n          type: \"string\",\n        },\n        r: {\n          type: \"number\",\n          minimum: 5,\n          exclusiveMaximum: 100,\n        },\n        id: {\n          type: \"integer\",\n          minimum: 0,\n        },\n      },\n    },\n  ],\n}\n\nconst data = {ts: \"\", r: null}\najv.validate(data) // true\ndata // { ts: '2016-12-01T22:07:28.829Z', r: 25, id: 0 }\n```\n\nYou can add your own dynamic default function to be recognised by this keyword:\n\n```javascript\nconst uuid = require(\"uuid\")\n\nconst def = require(\"ajv-keywords/dist/definitions/dynamicDefaults\")\ndef.DEFAULTS.uuid = () => uuid.v4\n\nconst schema = {\n  dynamicDefaults: {id: \"uuid\"},\n  properties: {id: {type: \"string\", format: \"uuid\"}},\n}\n\nconst data = {}\najv.validate(schema, data) // true\ndata // { id: 'a1183fbe-697b-4030-9bcc-cfeb282a9150' };\n\nconst data1 = {}\najv.validate(schema, data1) // true\ndata1 // { id: '5b008de7-1669-467a-a5c6-70fa244d7209' }\n```\n\nYou also can define dynamic default that accept parameters, e.g. version of uuid:\n\n```javascript\nconst uuid = require(\"uuid\")\n\nfunction getUuid(args) {\n  const version = \"v\" + ((arvs && args.v) || \"4\")\n  return uuid[version]\n}\n\nconst def = require(\"ajv-keywords/dist/definitions/dynamicDefaults\")\ndef.DEFAULTS.uuid = getUuid\n\nconst schema = {\n  dynamicDefaults: {\n    id1: \"uuid\", // v4\n    id2: {func: \"uuid\", v: 4}, // v4\n    id3: {func: \"uuid\", v: 1}, // v1\n  },\n}\n```\n\n**Please note**: dynamic default functions are differentiated by the number of parameters they have (`function.length`). Functions that do not expect default must have one non-optional argument so that `function.length` > 0.\n\n`dynamicDefaults` is not supported in [standalone validation code](https://github.com/ajv-validator/ajv/blob/master/docs/standalone.md).\n\n## Security contact\n\nTo report a security vulnerability, please use the\n[Tidelift security contact](https://tidelift.com/security).\nTidelift will coordinate the fix and disclosure.\n\nPlease do NOT report security vulnerabilities via GitHub issues.\n\n## Open-source software support\n\nAjv-keywords is a part of [Tidelift subscription](https://tidelift.com/subscription/pkg/npm-ajv-keywords?utm_source=npm-ajv-keywords&utm_medium=referral&utm_campaign=readme) - it provides a centralised support to open-source software users, in addition to the support provided by software maintainers.\n\n## License\n\n[MIT](https://github.com/epoberezkin/ajv-keywords/blob/master/LICENSE)\n", "readmeFilename": "README.md", "gitHead": "afd590fce6084e2bf5e37b1f90bae6b9ecffc996", "_id": "ajv-keywords@4.0.0-beta.3", "_nodeVersion": "14.14.0", "_npmVersion": "6.14.9", "dist": {"integrity": "sha512-aEKdUuq8Ww5l7Ialc74xA5Sc5zAT7FOyIaMh/7fd0mKmfc9mV4f/B1T4DNxpROFVdbVixjB73l4M61RkS8Jm0w==", "shasum": "3ee3e3005e081bc2b38d31f52d600c456e234d57", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-4.0.0-beta.3.tgz", "fileCount": 159, "unpackedSize": 129091, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfw/5UCRA9TVsSAnZWagAAlSAP/3blkO9k/Rlq2vmZIjWS\n6s/MkzuDM8u6z1/WsbRr/us0hCJKlJXzRw5r5b6GKed9ewe2v28ycqpgoYSL\natgwh+AYNf0Om0g3b9sdOyhLrFyQ+kbS5FyrBYsAS7YsXkCp8d20x6svVFwH\nzT8AQVLWow6vHl/FRqM6zccn9xKoBs691/cCfOamcXyHOU3tAcNcmizaN7Ol\n9X7yh4Qr7KCDvx5rose88FlxngO3Z3DuP+yAB8kmag8jF9iP40pvUjTlxfBa\nSgB2SqWsvK3lESr3CQB69yA/zeLSnNDVa0KXyJiEqNbuC0v/PfQl/xK8xN5n\nAAZv1EIU8UHt1wPhoXTOrZBUrWNL7/Y4qQ+gKpxrj7Br/N2qYmV6B4xYQnK6\ntUB2iqwq9SVLTPjAHyWycR5F3/nZ6gjphgGA51fIh4Yw+fsQl3PvWi3C04Cx\nV7ePLaTghAWKwTy+z7HrJYC5Zb14VIp+Veecsr9x8hx2rDzZmPCB8EkfWkNF\nmcq0rVl6uYe7KHYch8JRLtZ2jSEH8a3qrFfN5iZCpweY9eqhZz7a59eJvgSN\nbrZoFf/uS0TBb6i/zrp+CQACNWQPVYSdaXpke/sr+TH8PMyHAvqvmEfuXnK1\nBRK0yDwhNu27m15Xo1fUPB5BTvqGqykc+Zxah0DXzkfNPbmoQFODD3pajGEm\ngyS0\r\n=k0wu\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC0l361/aGK8dvK1T+DLRPSSuUL8hj665W9Gb5MPUQ2qgIgKY0taoVtAFxVH8tzuwWEThQwWFi9P3GK2McgBHd+cSc="}]}, "_npmUser": {"name": "esp", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ajv-keywords_4.0.0-beta.3_1606680147764_0.5966893346422146"}, "_hasShrinkwrap": false}, "4.0.0-rc.0": {"name": "ajv-keywords", "version": "4.0.0-rc.0", "description": "Custom JSON-Schema keywords for Ajv validator", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "rm -rf dist && tsc", "prepublish": "npm run build", "prettier:write": "prettier --write \"./**/*.{md,json,yaml,js,ts}\"", "prettier:check": "prettier --list-different \"./**/*.{md,json,yaml,js,ts}\"", "test": "npm link && npm link ajv-keywords && npm run eslint && npm run test-cov", "eslint": "eslint \"src/**/*.*s\" \"spec/**/*.*s\"", "test-spec": "jest spec/*.ts", "test-cov": "jest spec/*.ts --coverage"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/ajv-keywords.git"}, "keywords": ["JSON-Schema", "ajv", "keywords"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/ajv-keywords/issues"}, "homepage": "https://github.com/epoberezkin/ajv-keywords#readme", "peerDependencies": {"ajv": "^7.0.0-rc.1"}, "devDependencies": {"@ajv-validator/config": "^0.2.3", "@types/chai": "^4.2.14", "@types/jest": "^26.0.14", "@types/node": "^14.11.10", "@types/uuid": "^8.3.0", "@typescript-eslint/eslint-plugin": "^4.4.1", "@typescript-eslint/parser": "^4.4.1", "ajv": "^7.0.0-rc.1", "ajv-formats": "^0.3.4", "chai": "^4.2.0", "eslint": "^7.2.0", "eslint-config-prettier": "^6.13.0", "husky": "^4.3.0", "jest": "^26.5.3", "json-schema-test": "^2.0.0", "lint-staged": "^10.4.2", "prettier": "^2.1.2", "ts-jest": "^26.4.1", "typescript": "^4.0.3", "uuid": "^8.1.0"}, "prettier": "@ajv-validator/config/prettierrc.json", "husky": {"hooks": {"pre-commit": "lint-staged && npm test"}}, "lint-staged": {"*.{md,json,yaml,js,ts}": "prettier --write"}, "gitHead": "7a05a1a05da729743c6a049a267fe615d84806c6", "readme": "# ajv-keywords\n\nCustom JSON-Schema keywords for [Ajv](https://github.com/epoberezkin/ajv) validator\n\n[![build](https://github.com/ajv-validator/ajv-keywords/workflows/build/badge.svg)](https://github.com/ajv-validator/ajv-keywords/actions?query=workflow%3Abuild)\n[![npm](https://img.shields.io/npm/v/ajv-keywords.svg)](https://www.npmjs.com/package/ajv-keywords)\n[![npm (beta)](https://img.shields.io/npm/v/ajv-keywords/beta)](https://www.npmjs.com/package/ajv-keywords/v/4.0.0-rc.0)\n[![npm downloads](https://img.shields.io/npm/dm/ajv-keywords.svg)](https://www.npmjs.com/package/ajv-keywords)\n[![Coverage Status](https://coveralls.io/repos/github/ajv-validator/ajv-keywords/badge.svg?branch=master)](https://coveralls.io/github/ajv-validator/ajv-keywords?branch=master)\n[![Gitter](https://img.shields.io/gitter/room/ajv-validator/ajv.svg)](https://gitter.im/ajv-validator/ajv)\n\n**Please note**: This readme file is for [ajv-keywords v4.0.0-beta](https://github.com/ajv-validator/ajv-keywords/releases/tag/v4.0.0-beta.0) that should be used with [ajv v7 (beta)](https://github.com/ajv-validator/ajv).\n\n[ajv-keywords v3](https://github.com/ajv-validator/ajv-keywords/tree/v3) should be used with [ajv v6](https://github.com/ajv-validator/ajv/tree/v6).\n\n## Contents\n\n- [Install](#install)\n- [Usage](#usage)\n- [Keywords](#keywords)\n  - [Types](#types)\n    - [typeof](#typeof)\n    - [instanceof](#instanceof)<sup>\\+</sup>\n  - [Keywords for numbers](#keywords-for-numbers)\n    - [range and exclusiveRange](#range-and-exclusiverange)\n  - [Keywords for strings](#keywords-for-strings)\n    - [regexp](#regexp)\n    - [transform](#transform)<sup>\\*</sup>\n  - [Keywords for arrays](#keywords-for-arrays)\n    - [uniqueItemProperties](#uniqueitemproperties)<sup>\\+</sup>\n  - [Keywords for objects](#keywords-for-objects)\n    - [allRequired](#allrequired)\n    - [anyRequired](#anyrequired)\n    - [oneRequired](#onerequired)\n    - [patternRequired](#patternrequired)\n    - [prohibited](#prohibited)\n    - [deepProperties](#deepproperties)\n    - [deepRequired](#deeprequired)\n    - [dynamicDefaults](#dynamicdefaults)<sup>\\*</sup><sup>\\+</sup>\n  - [Keywords for all types](#keywords-for-all-types)\n    - [select/selectCases/selectDefault](#selectselectcasesselectdefault)\n- [Security contact](#security-contact)\n- [Open-source software support](#open-source-software-support)\n- [License](#license)\n\n<sup>\\*</sup> - keywords that modify data\n<sup>\\+</sup> - keywords that are not supported in [standalone validation code](https://github.com/ajv-validator/ajv/blob/master/docs/standalone.md)\n\n## Install\n\n```\nnpm install ajv-keywords\n```\n\nTo install version 4 beta to use with [Ajv v7 beta](https://github.com/ajv-validator/ajv/tree/v7-beta):\n\n```\nnpm install ajv-keywords@beta\n```\n\n## Usage\n\nTo add all available keywords:\n\n```javascript\nconst Ajv = require(\"ajv\")\nconst ajv = new Ajv()\nrequire(\"ajv-keywords\")(ajv)\n\najv.validate({instanceof: \"RegExp\"}, /.*/) // true\najv.validate({instanceof: \"RegExp\"}, \".*\") // false\n```\n\nTo add a single keyword:\n\n```javascript\nrequire(\"ajv-keywords\")(ajv, \"instanceof\")\n```\n\nTo add multiple keywords:\n\n```javascript\nrequire(\"ajv-keywords\")(ajv, [\"typeof\", \"instanceof\"])\n```\n\nTo add a single keyword directly (to avoid adding unused code):\n\n```javascript\nrequire(\"ajv-keywords/dist/keywords/select\")(ajv, opts)\n```\n\nTo add all keywords via Ajv options:\n\n```javascript\nconst ajv = new Ajv({keywords: require(\"ajv-keywords/dist/definitions\")(opts)})\n```\n\nTo add one or several keywords via options:\n\n```javascript\nconst ajv = new Ajv({\n  keywords: [\n    require(\"ajv-keywords/dist/definitions/typeof\")(),\n    require(\"ajv-keywords/dist/definitions/instanceof\")(),\n    // select exports an array of 3 definitions - see \"select\" in docs\n    ...require(\"ajv-keywords/dist/definitions/select\")(opts),\n  ],\n})\n```\n\n`opts` is an optional object with a property `defaultMeta` - URI of meta-schema to use for keywords that use subschemas (`select` and `deepProperties`). The default is `\"http://json-schema.org/schema\"`.\n\n## Keywords\n\n### Types\n\n#### `typeof`\n\nBased on JavaScript `typeof` operation.\n\nThe value of the keyword should be a string (`\"undefined\"`, `\"string\"`, `\"number\"`, `\"object\"`, `\"function\"`, `\"boolean\"` or `\"symbol\"`) or an array of strings.\n\nTo pass validation the result of `typeof` operation on the value should be equal to the string (or one of the strings in the array).\n\n```javascript\najv.validate({typeof: \"undefined\"}, undefined) // true\najv.validate({typeof: \"undefined\"}, null) // false\najv.validate({typeof: [\"undefined\", \"object\"]}, null) // true\n```\n\n#### `instanceof`\n\nBased on JavaScript `instanceof` operation.\n\nThe value of the keyword should be a string (`\"Object\"`, `\"Array\"`, `\"Function\"`, `\"Number\"`, `\"String\"`, `\"Date\"`, `\"RegExp\"` or `\"Promise\"`) or an array of strings.\n\nTo pass validation the result of `data instanceof ...` operation on the value should be true:\n\n```javascript\najv.validate({instanceof: \"Array\"}, []) // true\najv.validate({instanceof: \"Array\"}, {}) // false\najv.validate({instanceof: [\"Array\", \"Function\"]}, function () {}) // true\n```\n\nYou can add your own constructor function to be recognised by this keyword:\n\n```javascript\nclass MyClass {}\nconst instanceofDef = require(\"ajv-keywords/dist/definitions/instanceof\")\ninstanceofDef.CONSTRUCTORS.MyClass = MyClass\najv.validate({instanceof: \"MyClass\"}, new MyClass()) // true\n```\n\n**Please note**: currently `instanceof` is not supported in [standalone validation code](https://github.com/ajv-validator/ajv/blob/master/docs/standalone.md) - it has to be implemented as [`code` keyword](https://github.com/ajv-validator/ajv/blob/master/docs/keywords.md#define-keyword-with-code-generation-function) to support it (PR is welcome).\n\n### Keywords for numbers\n\n#### `range` and `exclusiveRange`\n\nSyntax sugar for the combination of minimum and maximum keywords (or exclusiveMinimum and exclusiveMaximum), also fails schema compilation if there are no numbers in the range.\n\nThe value of these keywords must be an array consisting of two numbers, the second must be greater or equal than the first one.\n\nIf the validated value is not a number the validation passes, otherwise to pass validation the value should be greater (or equal) than the first number and smaller (or equal) than the second number in the array.\n\n```javascript\nconst schema = {type: \"number\", range: [1, 3]}\najv.validate(schema, 1) // true\najv.validate(schema, 2) // true\najv.validate(schema, 3) // true\najv.validate(schema, 0.99) // false\najv.validate(schema, 3.01) // false\n\nconst schema = {type: \"number\", exclusiveRange: [1, 3]}\najv.validate(schema, 1.01) // true\najv.validate(schema, 2) // true\najv.validate(schema, 2.99) // true\najv.validate(schema, 1) // false\najv.validate(schema, 3) // false\n```\n\n### Keywords for strings\n\n#### `regexp`\n\nThis keyword allows to use regular expressions with flags in schemas, and also without `\"u\"` flag when needed (the standard `pattern` keyword does not support flags and implies the presence of `\"u\"` flag).\n\nThis keyword applies only to strings. If the data is not a string, the validation succeeds.\n\nThe value of this keyword can be either a string (the result of `regexp.toString()`) or an object with the properties `pattern` and `flags` (the same strings that should be passed to RegExp constructor).\n\n```javascript\nconst schema = {\n  type: \"object\",\n  properties: {\n    foo: {type: \"string\", regexp: \"/foo/i\"},\n    bar: {type: \"string\", regexp: {pattern: \"bar\", flags: \"i\"}},\n  },\n}\n\nconst validData = {\n  foo: \"Food\",\n  bar: \"Barmen\",\n}\n\nconst invalidData = {\n  foo: \"fog\",\n  bar: \"bad\",\n}\n```\n\n#### `transform`\n\nThis keyword allows a string to be modified during validation.\n\nThis keyword applies only to strings. If the data is not a string, the `transform` keyword is ignored.\n\nA standalone string cannot be modified, i.e. `data = 'a'; ajv.validate(schema, data);`, because strings are passed by value\n\n**Supported transformations:**\n\n- `trim`: remove whitespace from start and end\n- `trimStart`/`trimLeft`: remove whitespace from start\n- `trimEnd`/`trimRight`: remove whitespace from end\n- `toLowerCase`: convert to lower case\n- `toUpperCase`: convert to upper case\n- `toEnumCase`: change string case to be equal to one of `enum` values in the schema\n\nTransformations are applied in the order they are listed.\n\nNote: `toEnumCase` requires that all allowed values are unique when case insensitive.\n\n**Example: multiple transformations**\n\n```javascript\nrequire(\"ajv-keywords\")(ajv, \"transform\")\n\nconst schema = {\n  type: \"array\",\n  items: {\n    type: \"string\",\n    transform: [\"trim\", \"toLowerCase\"],\n  },\n}\n\nconst data = [\"  MixCase  \"]\najv.validate(schema, data)\nconsole.log(data) // ['mixcase']\n```\n\n**Example: `enumcase`**\n\n```javascript\nrequire(\"ajv-keywords\")(ajv, [\"transform\"])\n\nconst schema = {\n  type: \"array\",\n  items: {\n    type: \"string\",\n    transform: [\"trim\", \"toEnumCase\"],\n    enum: [\"pH\"],\n  },\n}\n\nconst data = [\"ph\", \" Ph\", \"PH\", \"pH \"]\najv.validate(schema, data)\nconsole.log(data) // ['pH','pH','pH','pH']\n```\n\n### Keywords for arrays\n\n#### `uniqueItemProperties`\n\nThe keyword allows to check that some properties in array items are unique.\n\nThis keyword applies only to arrays. If the data is not an array, the validation succeeds.\n\nThe value of this keyword must be an array of strings - property names that should have unique values across all items.\n\n```javascript\nconst schema = {\n  type: \"array\",\n  uniqueItemProperties: [\"id\", \"name\"],\n}\n\nconst validData = [{id: 1}, {id: 2}, {id: 3}]\n\nconst invalidData1 = [\n  {id: 1},\n  {id: 1}, // duplicate \"id\"\n  {id: 3},\n]\n\nconst invalidData2 = [\n  {id: 1, name: \"taco\"},\n  {id: 2, name: \"taco\"}, // duplicate \"name\"\n  {id: 3, name: \"salsa\"},\n]\n```\n\nThis keyword is contributed by [@blainesch](https://github.com/blainesch).\n\n**Please note**: currently `uniqueItemProperties` is not supported in [standalone validation code](https://github.com/ajv-validator/ajv/blob/master/docs/standalone.md) - it has to be implemented as [`code` keyword](https://github.com/ajv-validator/ajv/blob/master/docs/keywords.md#define-keyword-with-code-generation-function) to support it (PR is welcome).\n\n### Keywords for objects\n\n#### `allRequired`\n\nThis keyword allows to require the presence of all properties used in `properties` keyword in the same schema object.\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value of this keyword must be boolean.\n\nIf the value of the keyword is `false`, the validation succeeds.\n\nIf the value of the keyword is `true`, the validation succeeds if the data contains all properties defined in `properties` keyword (in the same schema object).\n\nIf the `properties` keyword is not present in the same schema object, schema compilation will throw exception.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  properties: {\n    foo: {type: 'number'},\n    bar: {type: 'number'}\n  }\n  allRequired: true\n};\n\nconst validData = { foo: 1, bar: 2 };\nconst alsoValidData = { foo: 1, bar: 2, baz: 3 };\n\nconst invalidDataList = [ {}, { foo: 1 }, { bar: 2 } ];\n```\n\n#### `anyRequired`\n\nThis keyword allows to require the presence of any (at least one) property from the list.\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value of this keyword must be an array of strings, each string being a property name. For data object to be valid at least one of the properties in this array should be present in the object.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  anyRequired: [\"foo\", \"bar\"],\n}\n\nconst validData = {foo: 1}\nconst alsoValidData = {foo: 1, bar: 2}\n\nconst invalidDataList = [{}, {baz: 3}]\n```\n\n#### `oneRequired`\n\nThis keyword allows to require the presence of only one property from the list.\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value of this keyword must be an array of strings, each string being a property name. For data object to be valid exactly one of the properties in this array should be present in the object.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  oneRequired: [\"foo\", \"bar\"],\n}\n\nconst validData = {foo: 1}\nconst alsoValidData = {bar: 2, baz: 3}\n\nconst invalidDataList = [{}, {baz: 3}, {foo: 1, bar: 2}]\n```\n\n#### `patternRequired`\n\nThis keyword allows to require the presence of properties that match some pattern(s).\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value of this keyword should be an array of strings, each string being a regular expression. For data object to be valid each regular expression in this array should match at least one property name in the data object.\n\nIf the array contains multiple regular expressions, more than one expression can match the same property name.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  patternRequired: [\"f.*o\", \"b.*r\"],\n}\n\nconst validData = {foo: 1, bar: 2}\nconst alsoValidData = {foobar: 3}\n\nconst invalidDataList = [{}, {foo: 1}, {bar: 2}]\n```\n\n#### `prohibited`\n\nThis keyword allows to prohibit that any of the properties in the list is present in the object.\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value of this keyword should be an array of strings, each string being a property name. For data object to be valid none of the properties in this array should be present in the object.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  prohibited: [\"foo\", \"bar\"],\n}\n\nconst validData = {baz: 1}\nconst alsoValidData = {}\n\nconst invalidDataList = [{foo: 1}, {bar: 2}, {foo: 1, bar: 2}]\n```\n\n**Please note**: `{prohibited: ['foo', 'bar']}` is equivalent to `{not: {anyRequired: ['foo', 'bar']}}` (i.e. it has the same validation result for any data).\n\n#### `deepProperties`\n\nThis keyword allows to validate deep properties (identified by JSON pointers).\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value should be an object, where keys are JSON pointers to the data, starting from the current position in data, and the values are JSON schemas. For data object to be valid the value of each JSON pointer should be valid according to the corresponding schema.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  deepProperties: {\n    \"/users/1/role\": {enum: [\"admin\"]},\n  },\n}\n\nconst validData = {\n  users: [\n    {},\n    {\n      id: 123,\n      role: \"admin\",\n    },\n  ],\n}\n\nconst alsoValidData = {\n  users: {\n    1: {\n      id: 123,\n      role: \"admin\",\n    },\n  },\n}\n\nconst invalidData = {\n  users: [\n    {},\n    {\n      id: 123,\n      role: \"user\",\n    },\n  ],\n}\n\nconst alsoInvalidData = {\n  users: {\n    1: {\n      id: 123,\n      role: \"user\",\n    },\n  },\n}\n```\n\n#### `deepRequired`\n\nThis keyword allows to check that some deep properties (identified by JSON pointers) are available.\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value should be an array of JSON pointers to the data, starting from the current position in data. For data object to be valid each JSON pointer should be some existing part of the data.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  deepRequired: [\"/users/1/role\"],\n}\n\nconst validData = {\n  users: [\n    {},\n    {\n      id: 123,\n      role: \"admin\",\n    },\n  ],\n}\n\nconst invalidData = {\n  users: [\n    {},\n    {\n      id: 123,\n    },\n  ],\n}\n```\n\nSee [json-schema-org/json-schema-spec#203](https://github.com/json-schema-org/json-schema-spec/issues/203#issue-197211916) for an example of the equivalent schema without `deepRequired` keyword.\n\n### Keywords for all types\n\n#### `select`/`selectCases`/`selectDefault`\n\nThese keywords allow to choose the schema to validate the data based on the value of some property in the validated data.\n\nThese keywords must be present in the same schema object (`selectDefault` is optional).\n\nThe value of `select` keyword should be a [\\$data reference](https://github.com/ajv-validator/ajv/blob/v7-beta/docs/validation.md#data-reference) that points to any primitive JSON type (string, number, boolean or null) in the data that is validated. You can also use a constant of primitive type as the value of this keyword (e.g., for debugging purposes).\n\nThe value of `selectCases` keyword must be an object where each property name is a possible string representation of the value of `select` keyword and each property value is a corresponding schema (from draft-06 it can be boolean) that must be used to validate the data.\n\nThe value of `selectDefault` keyword is a schema (also can be boolean) that must be used to validate the data in case `selectCases` has no key equal to the stringified value of `select` keyword.\n\nThe validation succeeds in one of the following cases:\n\n- the validation of data using selected schema succeeds,\n- none of the schemas is selected for validation,\n- the value of select is undefined (no property in the data that the data reference points to).\n\nIf `select` value (in data) is not a primitive type the validation fails.\n\nThis keyword correctly tracks evaluated properties and items to work with `unevaluatedProperties` and `unevaluatedItems` keywords - only properties and items from the subschema that was used (one of `selectCases` subschemas or `selectDefault` subschema) are marked as evaluated.\n\n**Please note**: these keywords require Ajv `$data` option to support [\\$data reference](https://github.com/epoberezkin/ajv/tree/5.0.2-beta.0#data-reference).\n\n```javascript\nrequire('ajv-keywords')(ajv, 'select');\n\nconst schema = {\n  type: \"object\",\n  required: ['kind'],\n  properties: {\n    kind: { type: 'string' }\n  },\n  select: { $data: '0/kind' },\n  selectCases: {\n    foo: {\n      required: ['foo'],\n      properties: {\n        kind: {},\n        foo: { type: 'string' }\n      },\n      additionalProperties: false\n    },\n    bar: {\n      required: ['bar'],\n      properties: {\n        kind: {},\n        bar: { type: 'number' }\n      },\n      additionalProperties: false\n    }\n  },\n  selectDefault: {\n    propertyNames: {\n      not: { enum: ['foo', 'bar'] }\n    }\n  }\n};\n\nconst validDataList = [\n  { kind: 'foo', foo: 'any' },\n  { kind: 'bar', bar: 1 },\n  { kind: 'anything_else', not_bar_or_foo: 'any value' }\n];\n\nconst invalidDataList = [\n  { kind: 'foo' }, // no property foo\n  { kind: 'bar' }, // no property bar\n  { kind: 'foo', foo: 'any', another: 'any value' }, // additional property\n  { kind: 'bar', bar: 1, another: 'any value' }, // additional property\n  { kind: 'anything_else', foo: 'any' } // property foo not allowed\n  { kind: 'anything_else', bar: 1 } // property bar not allowed\n];\n```\n\n#### `dynamicDefaults`\n\nThis keyword allows to assign dynamic defaults to properties, such as timestamps, unique IDs etc.\n\nThis keyword only works if `useDefaults` options is used and not inside `anyOf` keywords etc., in the same way as [default keyword treated by Ajv](https://github.com/epoberezkin/ajv#assigning-defaults).\n\nThe keyword should be added on the object level. Its value should be an object with each property corresponding to a property name, in the same way as in standard `properties` keyword. The value of each property can be:\n\n- an identifier of dynamic default function (a string)\n- an object with properties `func` (an identifier) and `args` (an object with parameters that will be passed to this function during schema compilation - see examples).\n\nThe properties used in `dynamicDefaults` should not be added to `required` keyword in the same schema (or validation will fail), because unlike `default` this keyword is processed after validation.\n\nThere are several predefined dynamic default functions:\n\n- `\"timestamp\"` - current timestamp in milliseconds\n- `\"datetime\"` - current date and time as string (ISO, valid according to `date-time` format)\n- `\"date\"` - current date as string (ISO, valid according to `date` format)\n- `\"time\"` - current time as string (ISO, valid according to `time` format)\n- `\"random\"` - pseudo-random number in [0, 1) interval\n- `\"randomint\"` - pseudo-random integer number. If string is used as a property value, the function will randomly return 0 or 1. If object `{ func: 'randomint', args: { max: N } }` is used then the default will be an integer number in [0, N) interval.\n- `\"seq\"` - sequential integer number starting from 0. If string is used as a property value, the default sequence will be used. If object `{ func: 'seq', args: { name: 'foo'} }` is used then the sequence with name `\"foo\"` will be used. Sequences are global, even if different ajv instances are used.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  dynamicDefaults: {\n    ts: \"datetime\",\n    r: {func: \"randomint\", args: {max: 100}},\n    id: {func: \"seq\", args: {name: \"id\"}},\n  },\n  properties: {\n    ts: {\n      type: \"string\",\n      format: \"date-time\",\n    },\n    r: {\n      type: \"integer\",\n      minimum: 0,\n      exclusiveMaximum: 100,\n    },\n    id: {\n      type: \"integer\",\n      minimum: 0,\n    },\n  },\n}\n\nconst data = {}\najv.validate(data) // true\ndata // { ts: '2016-12-01T22:07:28.829Z', r: 25, id: 0 }\n\nconst data1 = {}\najv.validate(data1) // true\ndata1 // { ts: '2016-12-01T22:07:29.832Z', r: 68, id: 1 }\n\najv.validate(data1) // true\ndata1 // didn't change, as all properties were defined\n```\n\nWhen using the `useDefaults` option value `\"empty\"`, properties and items equal to `null` or `\"\"` (empty string) will be considered missing and assigned defaults. Use `allOf` [compound keyword](https://github.com/epoberezkin/ajv/blob/master/KEYWORDS.md#compound-keywords) to execute `dynamicDefaults` before validation.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  allOf: [\n    {\n      dynamicDefaults: {\n        ts: \"datetime\",\n        r: {func: \"randomint\", args: {min: 5, max: 100}},\n        id: {func: \"seq\", args: {name: \"id\"}},\n      },\n    },\n    {\n      properties: {\n        ts: {\n          type: \"string\",\n        },\n        r: {\n          type: \"number\",\n          minimum: 5,\n          exclusiveMaximum: 100,\n        },\n        id: {\n          type: \"integer\",\n          minimum: 0,\n        },\n      },\n    },\n  ],\n}\n\nconst data = {ts: \"\", r: null}\najv.validate(data) // true\ndata // { ts: '2016-12-01T22:07:28.829Z', r: 25, id: 0 }\n```\n\nYou can add your own dynamic default function to be recognised by this keyword:\n\n```javascript\nconst uuid = require(\"uuid\")\n\nconst def = require(\"ajv-keywords/dist/definitions/dynamicDefaults\")\ndef.DEFAULTS.uuid = () => uuid.v4\n\nconst schema = {\n  dynamicDefaults: {id: \"uuid\"},\n  properties: {id: {type: \"string\", format: \"uuid\"}},\n}\n\nconst data = {}\najv.validate(schema, data) // true\ndata // { id: 'a1183fbe-697b-4030-9bcc-cfeb282a9150' };\n\nconst data1 = {}\najv.validate(schema, data1) // true\ndata1 // { id: '5b008de7-1669-467a-a5c6-70fa244d7209' }\n```\n\nYou also can define dynamic default that accept parameters, e.g. version of uuid:\n\n```javascript\nconst uuid = require(\"uuid\")\n\nfunction getUuid(args) {\n  const version = \"v\" + ((arvs && args.v) || \"4\")\n  return uuid[version]\n}\n\nconst def = require(\"ajv-keywords/dist/definitions/dynamicDefaults\")\ndef.DEFAULTS.uuid = getUuid\n\nconst schema = {\n  dynamicDefaults: {\n    id1: \"uuid\", // v4\n    id2: {func: \"uuid\", v: 4}, // v4\n    id3: {func: \"uuid\", v: 1}, // v1\n  },\n}\n```\n\n**Please note**: dynamic default functions are differentiated by the number of parameters they have (`function.length`). Functions that do not expect default must have one non-optional argument so that `function.length` > 0.\n\n`dynamicDefaults` is not supported in [standalone validation code](https://github.com/ajv-validator/ajv/blob/master/docs/standalone.md).\n\n## Security contact\n\nTo report a security vulnerability, please use the\n[Tidelift security contact](https://tidelift.com/security).\nTidelift will coordinate the fix and disclosure.\n\nPlease do NOT report security vulnerabilities via GitHub issues.\n\n## Open-source software support\n\nAjv-keywords is a part of [Tidelift subscription](https://tidelift.com/subscription/pkg/npm-ajv-keywords?utm_source=npm-ajv-keywords&utm_medium=referral&utm_campaign=readme) - it provides a centralised support to open-source software users, in addition to the support provided by software maintainers.\n\n## License\n\n[MIT](https://github.com/epoberezkin/ajv-keywords/blob/master/LICENSE)\n", "readmeFilename": "README.md", "_id": "ajv-keywords@4.0.0-rc.0", "_nodeVersion": "14.15.1", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-MTXCXzH1J/Iy2Fo0c/OoAIiScuaLak7vzm70Ky1Sfz9G2V2DwlwnoJv0qHQZu0ArtTu7kUD/CDMY0yBgJrFEMw==", "shasum": "b69f11ba027457811857088592653d3a1745ab1e", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-4.0.0-rc.0.tgz", "fileCount": 159, "unpackedSize": 130581, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf0zRxCRA9TVsSAnZWagAAE9oP/0GrQ9r3SImR8Ea0+d5C\nWWr/4BIDk6w3vOPXv78YjIzLjQStKUVrjx5p1xGQGborVSyl6BWTldjrSqOg\n35Cfs4cGXydFJ+vB2ZywiSGWENUA56vyxOZQlEtmu8YOMbQe36Hpna8hQUAU\nuuo6yw8h3emJHB6FyNXrr0jcPNUDKMCf20MLsIh+EQ4C+aUZg1z6LzleSMPL\nN3FQDMCqbGkLCp7ivGLLiZo1nj/+pjrPq+4pxeo9bMfth1l3uYZcCUHo7HnX\nWJCkUneRHNUsMl5gCgFMHTLI7U8uLjYbtG2YrwMY7SLiD1OYSCMrJ2nekf5G\nk02/dlyp6dsiV4zcRCY7B2/zfwXNybrjp8koUG3F4HBlz48Wn2i0mSFP8EsS\nT5qZPECPqZH++HUsgBH8+KGGBJAHy5cuxN1k7dG4qHiuD8epbfAKdbuYC/4f\nNScKUlaQV4J+ivbMjZSCKAZ3YOUOnorK4ffyWT3Mb5FG8dU0T0GLA0u4BKVM\n7G2dbVHpQ5ldFmu6+E/YP1iDV7CSUrtYVEDsSl3KNGHJNDYvwvDBwvwVWyx6\nnn5xUzBc+xUkO22qLgdYD0dsKM8zaiTiFKsp6poZUjHsa3brhoqMkwtyhJug\nQscBfSlboDmP5mPs9BNnfIColvLsgn2tzrrZiSLJ5+J5kmPSDJL9+Fhmp7eL\ndUWg\r\n=TFj1\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCqEi6Tkr8Q8/0A3I4sJwWbwXXD5aW8JcJGocKFLtt17wIgGz12YZUnbnF+yU8E1XO5l/owQhXPPqkCzig1gArDG4E="}]}, "_npmUser": {"name": "esp", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ajv-keywords_4.0.0-rc.0_1607677040851_0.5811356955760025"}, "_hasShrinkwrap": false}, "4.0.0": {"name": "ajv-keywords", "version": "4.0.0", "description": "Custom JSON-Schema keywords for Ajv validator", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "rm -rf dist && tsc", "prepublish": "npm run build", "prettier:write": "prettier --write \"./**/*.{md,json,yaml,js,ts}\"", "prettier:check": "prettier --list-different \"./**/*.{md,json,yaml,js,ts}\"", "test": "npm link && npm link ajv-keywords && npm run eslint && npm run test-cov", "eslint": "eslint \"src/**/*.*s\" \"spec/**/*.*s\"", "test-spec": "jest spec/*.ts", "test-cov": "jest spec/*.ts --coverage"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/ajv-keywords.git"}, "keywords": ["JSON-Schema", "ajv", "keywords"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/ajv-keywords/issues"}, "homepage": "https://github.com/epoberezkin/ajv-keywords#readme", "peerDependencies": {"ajv": "^7.0.0"}, "devDependencies": {"@ajv-validator/config": "^0.2.3", "@types/chai": "^4.2.14", "@types/jest": "^26.0.14", "@types/node": "^14.11.10", "@types/uuid": "^8.3.0", "@typescript-eslint/eslint-plugin": "^4.4.1", "@typescript-eslint/parser": "^4.4.1", "ajv": "^7.0.0", "ajv-formats": "^1.5.1", "chai": "^4.2.0", "eslint": "^7.2.0", "eslint-config-prettier": "^7.0.0", "husky": "^4.3.0", "jest": "^26.5.3", "json-schema-test": "^2.0.0", "lint-staged": "^10.4.2", "prettier": "^2.1.2", "ts-jest": "^26.4.1", "typescript": "^4.0.3", "uuid": "^8.1.0"}, "prettier": "@ajv-validator/config/prettierrc.json", "husky": {"hooks": {"pre-commit": "lint-staged && npm test"}}, "lint-staged": {"*.{md,json,yaml,js,ts}": "prettier --write"}, "gitHead": "6a6b8edfebc361355382a7b8c7102e69ce345028", "_id": "ajv-keywords@4.0.0", "_nodeVersion": "14.15.1", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-baL4pEYniCF5E/5Cj28f1DmPXGGASQIeCFfntY94vJPtrq0fei3iNt/TP5f2IwEH4opCzcOOvL6hKsi2IHaecg==", "shasum": "d0ffb23189d5002b234ad54c1a1b620a5398db58", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-4.0.0.tgz", "fileCount": 159, "unpackedSize": 130594, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf2RLgCRA9TVsSAnZWagAAtd8P/Rjge43ik1zeswMK/kIv\np+eDA0XAnYma4zpBpVEK9MJG8X9vLcRVZKRXVH8oBoGFOfJrwcKlrBp58D7q\nARsI0zHSDv9eG083n3sohEsgW8cY6VpDsOMf2E7uS6U46DKFH51PcUaX+ol+\nhRCrPg4yXJnlJ11BOq5O20Mzu9C7/K1RyU2gzVrsa6evO2/zOTG8s9vu+bD6\nemDMwxEOcC0Hti2eZ9yfBI1G4UEJkqqlNYhbFsYI0sCR9WjiaOp1X4Dur6Rl\ny+c6ZiWLJ/mliA2GOKc96/h52xcTj0Sl2//PPTmzCPlDIBssQuNdsO2HS9y8\nPDtf8dVpXqvss1J1FJelqoABDtR0X2M3qcCUhTWAO3EEi28EIEv1x6Lzpc/m\ngh4qZeWbKlTGXZLrWTJ++cXdKr+jrqqGfeYIV/NNAorTY0sv6JyO7gP5Fy7t\nh2NnBtEm0NsDUyQHPocubhOVWxTIgU9TIVvVPlqSrJFtS/SXc1SV8mu6rOow\n9lqPPm9UXjH3qavkUyBJIgRK+bJ8khhJRx6CaZ1PzcdSD35aI6uNKr9mdMW3\nsUezIeb08IKHh0B/qhj5Gn2baTNqhUpEOCYt7I7SZixLxW8zHK5r0w8VbvWv\nHMvXT9PnjkMLBsp6VspAOcdCzgcnE9lM7fYPly7/cIM90e8ckJspZrbT7Mog\nW25Z\r\n=V4RA\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDzNZ2/NXVk0pJ32Sk8lDcV08R/Jo96/KJuGIXKflYMBwIgXhq8AlFsGftifaGuxsJHyspyvjMM8TL3L/rq1/VvlGk="}]}, "_npmUser": {"name": "esp", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ajv-keywords_4.0.0_1608061664077_0.24744371309162405"}, "_hasShrinkwrap": false}, "5.0.0-beta.0": {"name": "ajv-keywords", "version": "5.0.0-beta.0", "description": "Custom JSON-Schema keywords for Ajv validator", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "rm -rf dist && tsc", "prepublish": "npm run build", "prettier:write": "prettier --write \"./**/*.{md,json,yaml,js,ts}\"", "prettier:check": "prettier --list-different \"./**/*.{md,json,yaml,js,ts}\"", "test": "npm link && npm link ajv-keywords && npm run eslint && npm run test-cov", "eslint": "eslint \"src/**/*.*s\" \"spec/**/*.*s\"", "test-spec": "jest spec/*.ts", "test-cov": "jest spec/*.ts --coverage"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/ajv-keywords.git"}, "keywords": ["JSON-Schema", "ajv", "keywords"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/ajv-keywords/issues"}, "homepage": "https://github.com/epoberezkin/ajv-keywords#readme", "peerDependencies": {"ajv": "^8.0.0-beta.0"}, "devDependencies": {"@ajv-validator/config": "^0.2.3", "@types/chai": "^4.2.14", "@types/jest": "^26.0.14", "@types/node": "^14.11.10", "@types/uuid": "^8.3.0", "@typescript-eslint/eslint-plugin": "^4.4.1", "@typescript-eslint/parser": "^4.4.1", "ajv": "^8.0.0-beta.0", "ajv-formats": "^1.5.1", "chai": "^4.2.0", "eslint": "^7.2.0", "eslint-config-prettier": "^7.0.0", "husky": "^4.3.0", "jest": "^26.5.3", "json-schema-test": "^2.0.0", "lint-staged": "^10.4.2", "prettier": "^2.1.2", "ts-jest": "^26.4.1", "typescript": "^4.2.0", "uuid": "^8.1.0"}, "prettier": "@ajv-validator/config/prettierrc.json", "husky": {"hooks": {"pre-commit": "lint-staged && npm test"}}, "lint-staged": {"*.{md,json,yaml,js,ts}": "prettier --write"}, "gitHead": "5665c70188e85d4077a7ba833dc4444d92e96e61", "readme": "# ajv-keywords\n\nCustom JSON-Schema keywords for [Ajv](https://github.com/epoberezkin/ajv) validator\n\n[![build](https://github.com/ajv-validator/ajv-keywords/workflows/build/badge.svg)](https://github.com/ajv-validator/ajv-keywords/actions?query=workflow%3Abuild)\n[![npm](https://img.shields.io/npm/v/ajv-keywords.svg)](https://www.npmjs.com/package/ajv-keywords)\n[![npm downloads](https://img.shields.io/npm/dm/ajv-keywords.svg)](https://www.npmjs.com/package/ajv-keywords)\n[![coverage](https://coveralls.io/repos/github/ajv-validator/ajv-keywords/badge.svg?branch=master)](https://coveralls.io/github/ajv-validator/ajv-keywords?branch=master)\n[![gitter](https://img.shields.io/gitter/room/ajv-validator/ajv.svg)](https://gitter.im/ajv-validator/ajv)\n\n**Please note**: This readme file is for [ajv-keywords v4.0.0](https://github.com/ajv-validator/ajv-keywords/releases/tag/v4.0.0) that should be used with [ajv v7](https://github.com/ajv-validator/ajv).\n\n[ajv-keywords v3](https://github.com/ajv-validator/ajv-keywords/tree/v3) should be used with [ajv v6](https://github.com/ajv-validator/ajv/tree/v6).\n\n## Contents\n\n- [Install](#install)\n- [Usage](#usage)\n- [Keywords](#keywords)\n  - [Types](#types)\n    - [typeof](#typeof)\n    - [instanceof](#instanceof)<sup>\\+</sup>\n  - [Keywords for numbers](#keywords-for-numbers)\n    - [range and exclusiveRange](#range-and-exclusiverange)\n  - [Keywords for strings](#keywords-for-strings)\n    - [regexp](#regexp)\n    - [transform](#transform)<sup>\\*</sup>\n  - [Keywords for arrays](#keywords-for-arrays)\n    - [uniqueItemProperties](#uniqueitemproperties)<sup>\\+</sup>\n  - [Keywords for objects](#keywords-for-objects)\n    - [allRequired](#allrequired)\n    - [anyRequired](#anyrequired)\n    - [oneRequired](#onerequired)\n    - [patternRequired](#patternrequired)\n    - [prohibited](#prohibited)\n    - [deepProperties](#deepproperties)\n    - [deepRequired](#deeprequired)\n    - [dynamicDefaults](#dynamicdefaults)<sup>\\*</sup><sup>\\+</sup>\n  - [Keywords for all types](#keywords-for-all-types)\n    - [select/selectCases/selectDefault](#selectselectcasesselectdefault)\n- [Security contact](#security-contact)\n- [Open-source software support](#open-source-software-support)\n- [License](#license)\n\n<sup>\\*</sup> - keywords that modify data\n<sup>\\+</sup> - keywords that are not supported in [standalone validation code](https://github.com/ajv-validator/ajv/blob/master/docs/standalone.md)\n\n## Install\n\nTo install version 4 to use with [Ajv v7](https://github.com/ajv-validator/ajv):\n\n```\nnpm install ajv-keywords\n```\n\n## Usage\n\nTo add all available keywords:\n\n```javascript\nconst Ajv = require(\"ajv\")\nconst ajv = new Ajv()\nrequire(\"ajv-keywords\")(ajv)\n\najv.validate({instanceof: \"RegExp\"}, /.*/) // true\najv.validate({instanceof: \"RegExp\"}, \".*\") // false\n```\n\nTo add a single keyword:\n\n```javascript\nrequire(\"ajv-keywords\")(ajv, \"instanceof\")\n```\n\nTo add multiple keywords:\n\n```javascript\nrequire(\"ajv-keywords\")(ajv, [\"typeof\", \"instanceof\"])\n```\n\nTo add a single keyword directly (to avoid adding unused code):\n\n```javascript\nrequire(\"ajv-keywords/dist/keywords/select\")(ajv, opts)\n```\n\nTo add all keywords via Ajv options:\n\n```javascript\nconst ajv = new Ajv({keywords: require(\"ajv-keywords/dist/definitions\")(opts)})\n```\n\nTo add one or several keywords via options:\n\n```javascript\nconst ajv = new Ajv({\n  keywords: [\n    require(\"ajv-keywords/dist/definitions/typeof\")(),\n    require(\"ajv-keywords/dist/definitions/instanceof\")(),\n    // select exports an array of 3 definitions - see \"select\" in docs\n    ...require(\"ajv-keywords/dist/definitions/select\")(opts),\n  ],\n})\n```\n\n`opts` is an optional object with a property `defaultMeta` - URI of meta-schema to use for keywords that use subschemas (`select` and `deepProperties`). The default is `\"http://json-schema.org/schema\"`.\n\n## Keywords\n\n### Types\n\n#### `typeof`\n\nBased on JavaScript `typeof` operation.\n\nThe value of the keyword should be a string (`\"undefined\"`, `\"string\"`, `\"number\"`, `\"object\"`, `\"function\"`, `\"boolean\"` or `\"symbol\"`) or an array of strings.\n\nTo pass validation the result of `typeof` operation on the value should be equal to the string (or one of the strings in the array).\n\n```javascript\najv.validate({typeof: \"undefined\"}, undefined) // true\najv.validate({typeof: \"undefined\"}, null) // false\najv.validate({typeof: [\"undefined\", \"object\"]}, null) // true\n```\n\n#### `instanceof`\n\nBased on JavaScript `instanceof` operation.\n\nThe value of the keyword should be a string (`\"Object\"`, `\"Array\"`, `\"Function\"`, `\"Number\"`, `\"String\"`, `\"Date\"`, `\"RegExp\"` or `\"Promise\"`) or an array of strings.\n\nTo pass validation the result of `data instanceof ...` operation on the value should be true:\n\n```javascript\najv.validate({instanceof: \"Array\"}, []) // true\najv.validate({instanceof: \"Array\"}, {}) // false\najv.validate({instanceof: [\"Array\", \"Function\"]}, function () {}) // true\n```\n\nYou can add your own constructor function to be recognised by this keyword:\n\n```javascript\nclass MyClass {}\nconst instanceofDef = require(\"ajv-keywords/dist/definitions/instanceof\")\ninstanceofDef.CONSTRUCTORS.MyClass = MyClass\najv.validate({instanceof: \"MyClass\"}, new MyClass()) // true\n```\n\n**Please note**: currently `instanceof` is not supported in [standalone validation code](https://github.com/ajv-validator/ajv/blob/master/docs/standalone.md) - it has to be implemented as [`code` keyword](https://github.com/ajv-validator/ajv/blob/master/docs/keywords.md#define-keyword-with-code-generation-function) to support it (PR is welcome).\n\n### Keywords for numbers\n\n#### `range` and `exclusiveRange`\n\nSyntax sugar for the combination of minimum and maximum keywords (or exclusiveMinimum and exclusiveMaximum), also fails schema compilation if there are no numbers in the range.\n\nThe value of these keywords must be an array consisting of two numbers, the second must be greater or equal than the first one.\n\nIf the validated value is not a number the validation passes, otherwise to pass validation the value should be greater (or equal) than the first number and smaller (or equal) than the second number in the array.\n\n```javascript\nconst schema = {type: \"number\", range: [1, 3]}\najv.validate(schema, 1) // true\najv.validate(schema, 2) // true\najv.validate(schema, 3) // true\najv.validate(schema, 0.99) // false\najv.validate(schema, 3.01) // false\n\nconst schema = {type: \"number\", exclusiveRange: [1, 3]}\najv.validate(schema, 1.01) // true\najv.validate(schema, 2) // true\najv.validate(schema, 2.99) // true\najv.validate(schema, 1) // false\najv.validate(schema, 3) // false\n```\n\n### Keywords for strings\n\n#### `regexp`\n\nThis keyword allows to use regular expressions with flags in schemas, and also without `\"u\"` flag when needed (the standard `pattern` keyword does not support flags and implies the presence of `\"u\"` flag).\n\nThis keyword applies only to strings. If the data is not a string, the validation succeeds.\n\nThe value of this keyword can be either a string (the result of `regexp.toString()`) or an object with the properties `pattern` and `flags` (the same strings that should be passed to RegExp constructor).\n\n```javascript\nconst schema = {\n  type: \"object\",\n  properties: {\n    foo: {type: \"string\", regexp: \"/foo/i\"},\n    bar: {type: \"string\", regexp: {pattern: \"bar\", flags: \"i\"}},\n  },\n}\n\nconst validData = {\n  foo: \"Food\",\n  bar: \"Barmen\",\n}\n\nconst invalidData = {\n  foo: \"fog\",\n  bar: \"bad\",\n}\n```\n\n#### `transform`\n\nThis keyword allows a string to be modified during validation.\n\nThis keyword applies only to strings. If the data is not a string, the `transform` keyword is ignored.\n\nA standalone string cannot be modified, i.e. `data = 'a'; ajv.validate(schema, data);`, because strings are passed by value\n\n**Supported transformations:**\n\n- `trim`: remove whitespace from start and end\n- `trimStart`/`trimLeft`: remove whitespace from start\n- `trimEnd`/`trimRight`: remove whitespace from end\n- `toLowerCase`: convert to lower case\n- `toUpperCase`: convert to upper case\n- `toEnumCase`: change string case to be equal to one of `enum` values in the schema\n\nTransformations are applied in the order they are listed.\n\nNote: `toEnumCase` requires that all allowed values are unique when case insensitive.\n\n**Example: multiple transformations**\n\n```javascript\nrequire(\"ajv-keywords\")(ajv, \"transform\")\n\nconst schema = {\n  type: \"array\",\n  items: {\n    type: \"string\",\n    transform: [\"trim\", \"toLowerCase\"],\n  },\n}\n\nconst data = [\"  MixCase  \"]\najv.validate(schema, data)\nconsole.log(data) // ['mixcase']\n```\n\n**Example: `enumcase`**\n\n```javascript\nrequire(\"ajv-keywords\")(ajv, [\"transform\"])\n\nconst schema = {\n  type: \"array\",\n  items: {\n    type: \"string\",\n    transform: [\"trim\", \"toEnumCase\"],\n    enum: [\"pH\"],\n  },\n}\n\nconst data = [\"ph\", \" Ph\", \"PH\", \"pH \"]\najv.validate(schema, data)\nconsole.log(data) // ['pH','pH','pH','pH']\n```\n\n### Keywords for arrays\n\n#### `uniqueItemProperties`\n\nThe keyword allows to check that some properties in array items are unique.\n\nThis keyword applies only to arrays. If the data is not an array, the validation succeeds.\n\nThe value of this keyword must be an array of strings - property names that should have unique values across all items.\n\n```javascript\nconst schema = {\n  type: \"array\",\n  uniqueItemProperties: [\"id\", \"name\"],\n}\n\nconst validData = [{id: 1}, {id: 2}, {id: 3}]\n\nconst invalidData1 = [\n  {id: 1},\n  {id: 1}, // duplicate \"id\"\n  {id: 3},\n]\n\nconst invalidData2 = [\n  {id: 1, name: \"taco\"},\n  {id: 2, name: \"taco\"}, // duplicate \"name\"\n  {id: 3, name: \"salsa\"},\n]\n```\n\nThis keyword is contributed by [@blainesch](https://github.com/blainesch).\n\n**Please note**: currently `uniqueItemProperties` is not supported in [standalone validation code](https://github.com/ajv-validator/ajv/blob/master/docs/standalone.md) - it has to be implemented as [`code` keyword](https://github.com/ajv-validator/ajv/blob/master/docs/keywords.md#define-keyword-with-code-generation-function) to support it (PR is welcome).\n\n### Keywords for objects\n\n#### `allRequired`\n\nThis keyword allows to require the presence of all properties used in `properties` keyword in the same schema object.\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value of this keyword must be boolean.\n\nIf the value of the keyword is `false`, the validation succeeds.\n\nIf the value of the keyword is `true`, the validation succeeds if the data contains all properties defined in `properties` keyword (in the same schema object).\n\nIf the `properties` keyword is not present in the same schema object, schema compilation will throw exception.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  properties: {\n    foo: {type: 'number'},\n    bar: {type: 'number'}\n  }\n  allRequired: true\n};\n\nconst validData = { foo: 1, bar: 2 };\nconst alsoValidData = { foo: 1, bar: 2, baz: 3 };\n\nconst invalidDataList = [ {}, { foo: 1 }, { bar: 2 } ];\n```\n\n#### `anyRequired`\n\nThis keyword allows to require the presence of any (at least one) property from the list.\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value of this keyword must be an array of strings, each string being a property name. For data object to be valid at least one of the properties in this array should be present in the object.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  anyRequired: [\"foo\", \"bar\"],\n}\n\nconst validData = {foo: 1}\nconst alsoValidData = {foo: 1, bar: 2}\n\nconst invalidDataList = [{}, {baz: 3}]\n```\n\n#### `oneRequired`\n\nThis keyword allows to require the presence of only one property from the list.\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value of this keyword must be an array of strings, each string being a property name. For data object to be valid exactly one of the properties in this array should be present in the object.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  oneRequired: [\"foo\", \"bar\"],\n}\n\nconst validData = {foo: 1}\nconst alsoValidData = {bar: 2, baz: 3}\n\nconst invalidDataList = [{}, {baz: 3}, {foo: 1, bar: 2}]\n```\n\n#### `patternRequired`\n\nThis keyword allows to require the presence of properties that match some pattern(s).\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value of this keyword should be an array of strings, each string being a regular expression. For data object to be valid each regular expression in this array should match at least one property name in the data object.\n\nIf the array contains multiple regular expressions, more than one expression can match the same property name.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  patternRequired: [\"f.*o\", \"b.*r\"],\n}\n\nconst validData = {foo: 1, bar: 2}\nconst alsoValidData = {foobar: 3}\n\nconst invalidDataList = [{}, {foo: 1}, {bar: 2}]\n```\n\n#### `prohibited`\n\nThis keyword allows to prohibit that any of the properties in the list is present in the object.\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value of this keyword should be an array of strings, each string being a property name. For data object to be valid none of the properties in this array should be present in the object.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  prohibited: [\"foo\", \"bar\"],\n}\n\nconst validData = {baz: 1}\nconst alsoValidData = {}\n\nconst invalidDataList = [{foo: 1}, {bar: 2}, {foo: 1, bar: 2}]\n```\n\n**Please note**: `{prohibited: ['foo', 'bar']}` is equivalent to `{not: {anyRequired: ['foo', 'bar']}}` (i.e. it has the same validation result for any data).\n\n#### `deepProperties`\n\nThis keyword allows to validate deep properties (identified by JSON pointers).\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value should be an object, where keys are JSON pointers to the data, starting from the current position in data, and the values are JSON schemas. For data object to be valid the value of each JSON pointer should be valid according to the corresponding schema.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  deepProperties: {\n    \"/users/1/role\": {enum: [\"admin\"]},\n  },\n}\n\nconst validData = {\n  users: [\n    {},\n    {\n      id: 123,\n      role: \"admin\",\n    },\n  ],\n}\n\nconst alsoValidData = {\n  users: {\n    1: {\n      id: 123,\n      role: \"admin\",\n    },\n  },\n}\n\nconst invalidData = {\n  users: [\n    {},\n    {\n      id: 123,\n      role: \"user\",\n    },\n  ],\n}\n\nconst alsoInvalidData = {\n  users: {\n    1: {\n      id: 123,\n      role: \"user\",\n    },\n  },\n}\n```\n\n#### `deepRequired`\n\nThis keyword allows to check that some deep properties (identified by JSON pointers) are available.\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value should be an array of JSON pointers to the data, starting from the current position in data. For data object to be valid each JSON pointer should be some existing part of the data.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  deepRequired: [\"/users/1/role\"],\n}\n\nconst validData = {\n  users: [\n    {},\n    {\n      id: 123,\n      role: \"admin\",\n    },\n  ],\n}\n\nconst invalidData = {\n  users: [\n    {},\n    {\n      id: 123,\n    },\n  ],\n}\n```\n\nSee [json-schema-org/json-schema-spec#203](https://github.com/json-schema-org/json-schema-spec/issues/203#issue-197211916) for an example of the equivalent schema without `deepRequired` keyword.\n\n### Keywords for all types\n\n#### `select`/`selectCases`/`selectDefault`\n\nThese keywords allow to choose the schema to validate the data based on the value of some property in the validated data.\n\nThese keywords must be present in the same schema object (`selectDefault` is optional).\n\nThe value of `select` keyword should be a [\\$data reference](https://github.com/ajv-validator/ajv/blob/master/docs/validation.md#data-reference) that points to any primitive JSON type (string, number, boolean or null) in the data that is validated. You can also use a constant of primitive type as the value of this keyword (e.g., for debugging purposes).\n\nThe value of `selectCases` keyword must be an object where each property name is a possible string representation of the value of `select` keyword and each property value is a corresponding schema (from draft-06 it can be boolean) that must be used to validate the data.\n\nThe value of `selectDefault` keyword is a schema (also can be boolean) that must be used to validate the data in case `selectCases` has no key equal to the stringified value of `select` keyword.\n\nThe validation succeeds in one of the following cases:\n\n- the validation of data using selected schema succeeds,\n- none of the schemas is selected for validation,\n- the value of select is undefined (no property in the data that the data reference points to).\n\nIf `select` value (in data) is not a primitive type the validation fails.\n\nThis keyword correctly tracks evaluated properties and items to work with `unevaluatedProperties` and `unevaluatedItems` keywords - only properties and items from the subschema that was used (one of `selectCases` subschemas or `selectDefault` subschema) are marked as evaluated.\n\n**Please note**: these keywords require Ajv `$data` option to support [\\$data reference](https://github.com/ajv-validator/ajv/blob/master/docs/validation.md#data-reference).\n\n```javascript\nrequire('ajv-keywords')(ajv, 'select');\n\nconst schema = {\n  type: \"object\",\n  required: ['kind'],\n  properties: {\n    kind: { type: 'string' }\n  },\n  select: { $data: '0/kind' },\n  selectCases: {\n    foo: {\n      required: ['foo'],\n      properties: {\n        kind: {},\n        foo: { type: 'string' }\n      },\n      additionalProperties: false\n    },\n    bar: {\n      required: ['bar'],\n      properties: {\n        kind: {},\n        bar: { type: 'number' }\n      },\n      additionalProperties: false\n    }\n  },\n  selectDefault: {\n    propertyNames: {\n      not: { enum: ['foo', 'bar'] }\n    }\n  }\n};\n\nconst validDataList = [\n  { kind: 'foo', foo: 'any' },\n  { kind: 'bar', bar: 1 },\n  { kind: 'anything_else', not_bar_or_foo: 'any value' }\n];\n\nconst invalidDataList = [\n  { kind: 'foo' }, // no property foo\n  { kind: 'bar' }, // no property bar\n  { kind: 'foo', foo: 'any', another: 'any value' }, // additional property\n  { kind: 'bar', bar: 1, another: 'any value' }, // additional property\n  { kind: 'anything_else', foo: 'any' } // property foo not allowed\n  { kind: 'anything_else', bar: 1 } // property bar not allowed\n];\n```\n\n#### `dynamicDefaults`\n\nThis keyword allows to assign dynamic defaults to properties, such as timestamps, unique IDs etc.\n\nThis keyword only works if `useDefaults` options is used and not inside `anyOf` keywords etc., in the same way as [default keyword treated by Ajv](https://github.com/epoberezkin/ajv#assigning-defaults).\n\nThe keyword should be added on the object level. Its value should be an object with each property corresponding to a property name, in the same way as in standard `properties` keyword. The value of each property can be:\n\n- an identifier of dynamic default function (a string)\n- an object with properties `func` (an identifier) and `args` (an object with parameters that will be passed to this function during schema compilation - see examples).\n\nThe properties used in `dynamicDefaults` should not be added to `required` keyword in the same schema (or validation will fail), because unlike `default` this keyword is processed after validation.\n\nThere are several predefined dynamic default functions:\n\n- `\"timestamp\"` - current timestamp in milliseconds\n- `\"datetime\"` - current date and time as string (ISO, valid according to `date-time` format)\n- `\"date\"` - current date as string (ISO, valid according to `date` format)\n- `\"time\"` - current time as string (ISO, valid according to `time` format)\n- `\"random\"` - pseudo-random number in [0, 1) interval\n- `\"randomint\"` - pseudo-random integer number. If string is used as a property value, the function will randomly return 0 or 1. If object `{ func: 'randomint', args: { max: N } }` is used then the default will be an integer number in [0, N) interval.\n- `\"seq\"` - sequential integer number starting from 0. If string is used as a property value, the default sequence will be used. If object `{ func: 'seq', args: { name: 'foo'} }` is used then the sequence with name `\"foo\"` will be used. Sequences are global, even if different ajv instances are used.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  dynamicDefaults: {\n    ts: \"datetime\",\n    r: {func: \"randomint\", args: {max: 100}},\n    id: {func: \"seq\", args: {name: \"id\"}},\n  },\n  properties: {\n    ts: {\n      type: \"string\",\n      format: \"date-time\",\n    },\n    r: {\n      type: \"integer\",\n      minimum: 0,\n      exclusiveMaximum: 100,\n    },\n    id: {\n      type: \"integer\",\n      minimum: 0,\n    },\n  },\n}\n\nconst data = {}\najv.validate(data) // true\ndata // { ts: '2016-12-01T22:07:28.829Z', r: 25, id: 0 }\n\nconst data1 = {}\najv.validate(data1) // true\ndata1 // { ts: '2016-12-01T22:07:29.832Z', r: 68, id: 1 }\n\najv.validate(data1) // true\ndata1 // didn't change, as all properties were defined\n```\n\nWhen using the `useDefaults` option value `\"empty\"`, properties and items equal to `null` or `\"\"` (empty string) will be considered missing and assigned defaults. Use `allOf` [compound keyword](https://github.com/epoberezkin/ajv/blob/master/KEYWORDS.md#compound-keywords) to execute `dynamicDefaults` before validation.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  allOf: [\n    {\n      dynamicDefaults: {\n        ts: \"datetime\",\n        r: {func: \"randomint\", args: {min: 5, max: 100}},\n        id: {func: \"seq\", args: {name: \"id\"}},\n      },\n    },\n    {\n      properties: {\n        ts: {\n          type: \"string\",\n        },\n        r: {\n          type: \"number\",\n          minimum: 5,\n          exclusiveMaximum: 100,\n        },\n        id: {\n          type: \"integer\",\n          minimum: 0,\n        },\n      },\n    },\n  ],\n}\n\nconst data = {ts: \"\", r: null}\najv.validate(data) // true\ndata // { ts: '2016-12-01T22:07:28.829Z', r: 25, id: 0 }\n```\n\nYou can add your own dynamic default function to be recognised by this keyword:\n\n```javascript\nconst uuid = require(\"uuid\")\n\nconst def = require(\"ajv-keywords/dist/definitions/dynamicDefaults\")\ndef.DEFAULTS.uuid = () => uuid.v4\n\nconst schema = {\n  dynamicDefaults: {id: \"uuid\"},\n  properties: {id: {type: \"string\", format: \"uuid\"}},\n}\n\nconst data = {}\najv.validate(schema, data) // true\ndata // { id: 'a1183fbe-697b-4030-9bcc-cfeb282a9150' };\n\nconst data1 = {}\najv.validate(schema, data1) // true\ndata1 // { id: '5b008de7-1669-467a-a5c6-70fa244d7209' }\n```\n\nYou also can define dynamic default that accept parameters, e.g. version of uuid:\n\n```javascript\nconst uuid = require(\"uuid\")\n\nfunction getUuid(args) {\n  const version = \"v\" + ((arvs && args.v) || \"4\")\n  return uuid[version]\n}\n\nconst def = require(\"ajv-keywords/dist/definitions/dynamicDefaults\")\ndef.DEFAULTS.uuid = getUuid\n\nconst schema = {\n  dynamicDefaults: {\n    id1: \"uuid\", // v4\n    id2: {func: \"uuid\", v: 4}, // v4\n    id3: {func: \"uuid\", v: 1}, // v1\n  },\n}\n```\n\n**Please note**: dynamic default functions are differentiated by the number of parameters they have (`function.length`). Functions that do not expect default must have one non-optional argument so that `function.length` > 0.\n\n`dynamicDefaults` is not supported in [standalone validation code](https://github.com/ajv-validator/ajv/blob/master/docs/standalone.md).\n\n## Security contact\n\nTo report a security vulnerability, please use the\n[Tidelift security contact](https://tidelift.com/security).\nTidelift will coordinate the fix and disclosure.\n\nPlease do NOT report security vulnerabilities via GitHub issues.\n\n## Open-source software support\n\nAjv-keywords is a part of [Tidelift subscription](https://tidelift.com/subscription/pkg/npm-ajv-keywords?utm_source=npm-ajv-keywords&utm_medium=referral&utm_campaign=readme) - it provides a centralised support to open-source software users, in addition to the support provided by software maintainers.\n\n## License\n\n[MIT](https://github.com/epoberezkin/ajv-keywords/blob/master/LICENSE)\n", "readmeFilename": "README.md", "_id": "ajv-keywords@5.0.0-beta.0", "_nodeVersion": "14.16.0", "_npmVersion": "6.14.11", "dist": {"integrity": "sha512-RK9YuOTgWiINFzKqayKBJpOOxeK3nTjFiJTaqZvselMKgs6jRE1Q9zMYdm6dXS1pwXzVy0x6hV1G877wbbpOhA==", "shasum": "f98eac76a08cbafb9300594ddd7e813a16def629", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-5.0.0-beta.0.tgz", "fileCount": 159, "unpackedSize": 130926, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgTNIICRA9TVsSAnZWagAAMCEP/ifBiGt3GGEI7lMrXBKc\nQH6qt4GMm9SijHHvVc4EsnBOfSA/VgEa5TJTYNkXEUXoxFPvNPumNGYs+a31\nXRQXl/pzMkYLUAB1aBVEUE//Hz/W+3ITPut7Vnh7zIHCxIVdeSKPTDKvn0p1\n8nF8C/j0s/BoBV2hk9kUEVTGEX8a/5OrOJPmSRcuc9upymI/JPkLSgPUboar\n0ktVFI+PTxaRzsgw5BDNTwOWUuFseL539W1KVLEds+c1n2MFnrqS/46+sAov\nYRkBrhyHJuWSof0sFHBwhesqu8XfGv/qnAc+H3vO1mHJACxD/HBcpUrcAhru\nldPEh0CM1LepjiBUlVfHU/JwxuGXd01l+/CaS/Xf0M0AFN+ERtHQ5b9Qs05J\nqVrd2C7FdbI8LrOK/Z7aJa1llBSU06eddV1sI7UTFtBd/1fTv6RRQgVn+o/9\nX7NeRjZ/eG6IsJSZEL6BwNff6r6Hg/CvG0eAmhmZ+zTWwADmaTyGGMA1i5lO\nBH7nd8wbEOPfOqDopNMOb9Q0kkecFkiL06KQb1XbDVRhdbxw+ykL1XjmQM5R\nMZG3ePCeBD7wJv6Vwo58SjOuU+F0XeaDskni0I8HjM4POQbKQKH69dn9A9AC\nq9hE6jXfkblHopli5WGjmk5fbvA51t+NHlwxnRKLJpKEy4pKPwRRhrLvSfjD\nDYw3\r\n=Bn8C\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE/K01mRXXtX2Ps97IqT13daqhWkbfn4EcRrT9iypYQlAiBMuvWDb2gdBISCMVLUWK6M3iXl92a0qlDFd+Fa0r0HMA=="}]}, "_npmUser": {"name": "esp", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ajv-keywords_5.0.0-beta.0_1615647240071_0.9473416793777658"}, "_hasShrinkwrap": false}, "5.0.0-beta.1": {"name": "ajv-keywords", "version": "5.0.0-beta.1", "description": "Custom JSON-Schema keywords for Ajv validator", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "rm -rf dist && tsc", "prepublish": "npm run build", "prettier:write": "prettier --write \"./**/*.{md,json,yaml,js,ts}\"", "prettier:check": "prettier --list-different \"./**/*.{md,json,yaml,js,ts}\"", "test": "npm link && npm link ajv-keywords && npm run eslint && npm run test-cov", "eslint": "eslint \"src/**/*.*s\" \"spec/**/*.*s\"", "test-spec": "jest spec/*.ts", "test-cov": "jest spec/*.ts --coverage"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/ajv-keywords.git"}, "keywords": ["JSON-Schema", "ajv", "keywords"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/ajv-keywords/issues"}, "homepage": "https://github.com/epoberezkin/ajv-keywords#readme", "dependencies": {"fast-deep-equal": "^3.1.3"}, "peerDependencies": {"ajv": "^8.0.0-beta.4"}, "devDependencies": {"@ajv-validator/config": "^0.2.3", "@types/chai": "^4.2.14", "@types/jest": "^26.0.14", "@types/node": "^14.11.10", "@types/uuid": "^8.3.0", "@typescript-eslint/eslint-plugin": "^4.4.1", "@typescript-eslint/parser": "^4.4.1", "ajv": "^8.0.0-beta.4", "ajv-formats": "^2.0.0-beta.2", "chai": "^4.2.0", "eslint": "^7.2.0", "eslint-config-prettier": "^7.0.0", "husky": "^4.3.0", "jest": "^26.5.3", "json-schema-test": "^2.0.0", "lint-staged": "^10.4.2", "prettier": "^2.1.2", "ts-jest": "^26.4.1", "typescript": "^4.2.0", "uuid": "^8.1.0"}, "prettier": "@ajv-validator/config/prettierrc.json", "husky": {"hooks": {"pre-commit": "lint-staged && npm test"}}, "lint-staged": {"*.{md,json,yaml,js,ts}": "prettier --write"}, "gitHead": "8d2f4baa5fbe42b13edcc14e3b3cdf627efb93f4", "readme": "# ajv-keywords\n\nCustom JSON-Schema keywords for [Ajv](https://github.com/epoberezkin/ajv) validator\n\n[![build](https://github.com/ajv-validator/ajv-keywords/workflows/build/badge.svg)](https://github.com/ajv-validator/ajv-keywords/actions?query=workflow%3Abuild)\n[![npm](https://img.shields.io/npm/v/ajv-keywords.svg)](https://www.npmjs.com/package/ajv-keywords)\n[![npm downloads](https://img.shields.io/npm/dm/ajv-keywords.svg)](https://www.npmjs.com/package/ajv-keywords)\n[![coverage](https://coveralls.io/repos/github/ajv-validator/ajv-keywords/badge.svg?branch=master)](https://coveralls.io/github/ajv-validator/ajv-keywords?branch=master)\n[![gitter](https://img.shields.io/gitter/room/ajv-validator/ajv.svg)](https://gitter.im/ajv-validator/ajv)\n\n**Please note**: This readme file is for [ajv-keywords v4.0.0](https://github.com/ajv-validator/ajv-keywords/releases/tag/v4.0.0) that should be used with [ajv v7](https://github.com/ajv-validator/ajv).\n\n[ajv-keywords v3](https://github.com/ajv-validator/ajv-keywords/tree/v3) should be used with [ajv v6](https://github.com/ajv-validator/ajv/tree/v6).\n\n## Contents\n\n- [Install](#install)\n- [Usage](#usage)\n- [Keywords](#keywords)\n  - [Types](#types)\n    - [typeof](#typeof)\n    - [instanceof](#instanceof)<sup>\\+</sup>\n  - [Keywords for numbers](#keywords-for-numbers)\n    - [range and exclusiveRange](#range-and-exclusiverange)\n  - [Keywords for strings](#keywords-for-strings)\n    - [regexp](#regexp)\n    - [transform](#transform)<sup>\\*</sup>\n  - [Keywords for arrays](#keywords-for-arrays)\n    - [uniqueItemProperties](#uniqueitemproperties)<sup>\\+</sup>\n  - [Keywords for objects](#keywords-for-objects)\n    - [allRequired](#allrequired)\n    - [anyRequired](#anyrequired)\n    - [oneRequired](#onerequired)\n    - [patternRequired](#patternrequired)\n    - [prohibited](#prohibited)\n    - [deepProperties](#deepproperties)\n    - [deepRequired](#deeprequired)\n    - [dynamicDefaults](#dynamicdefaults)<sup>\\*</sup><sup>\\+</sup>\n  - [Keywords for all types](#keywords-for-all-types)\n    - [select/selectCases/selectDefault](#selectselectcasesselectdefault)\n- [Security contact](#security-contact)\n- [Open-source software support](#open-source-software-support)\n- [License](#license)\n\n<sup>\\*</sup> - keywords that modify data\n<sup>\\+</sup> - keywords that are not supported in [standalone validation code](https://github.com/ajv-validator/ajv/blob/master/docs/standalone.md)\n\n## Install\n\nTo install version 4 to use with [Ajv v7](https://github.com/ajv-validator/ajv):\n\n```\nnpm install ajv-keywords\n```\n\n## Usage\n\nTo add all available keywords:\n\n```javascript\nconst Ajv = require(\"ajv\")\nconst ajv = new Ajv()\nrequire(\"ajv-keywords\")(ajv)\n\najv.validate({instanceof: \"RegExp\"}, /.*/) // true\najv.validate({instanceof: \"RegExp\"}, \".*\") // false\n```\n\nTo add a single keyword:\n\n```javascript\nrequire(\"ajv-keywords\")(ajv, \"instanceof\")\n```\n\nTo add multiple keywords:\n\n```javascript\nrequire(\"ajv-keywords\")(ajv, [\"typeof\", \"instanceof\"])\n```\n\nTo add a single keyword directly (to avoid adding unused code):\n\n```javascript\nrequire(\"ajv-keywords/dist/keywords/select\")(ajv, opts)\n```\n\nTo add all keywords via Ajv options:\n\n```javascript\nconst ajv = new Ajv({keywords: require(\"ajv-keywords/dist/definitions\")(opts)})\n```\n\nTo add one or several keywords via options:\n\n```javascript\nconst ajv = new Ajv({\n  keywords: [\n    require(\"ajv-keywords/dist/definitions/typeof\")(),\n    require(\"ajv-keywords/dist/definitions/instanceof\")(),\n    // select exports an array of 3 definitions - see \"select\" in docs\n    ...require(\"ajv-keywords/dist/definitions/select\")(opts),\n  ],\n})\n```\n\n`opts` is an optional object with a property `defaultMeta` - URI of meta-schema to use for keywords that use subschemas (`select` and `deepProperties`). The default is `\"http://json-schema.org/schema\"`.\n\n## Keywords\n\n### Types\n\n#### `typeof`\n\nBased on JavaScript `typeof` operation.\n\nThe value of the keyword should be a string (`\"undefined\"`, `\"string\"`, `\"number\"`, `\"object\"`, `\"function\"`, `\"boolean\"` or `\"symbol\"`) or an array of strings.\n\nTo pass validation the result of `typeof` operation on the value should be equal to the string (or one of the strings in the array).\n\n```javascript\najv.validate({typeof: \"undefined\"}, undefined) // true\najv.validate({typeof: \"undefined\"}, null) // false\najv.validate({typeof: [\"undefined\", \"object\"]}, null) // true\n```\n\n#### `instanceof`\n\nBased on JavaScript `instanceof` operation.\n\nThe value of the keyword should be a string (`\"Object\"`, `\"Array\"`, `\"Function\"`, `\"Number\"`, `\"String\"`, `\"Date\"`, `\"RegExp\"` or `\"Promise\"`) or an array of strings.\n\nTo pass validation the result of `data instanceof ...` operation on the value should be true:\n\n```javascript\najv.validate({instanceof: \"Array\"}, []) // true\najv.validate({instanceof: \"Array\"}, {}) // false\najv.validate({instanceof: [\"Array\", \"Function\"]}, function () {}) // true\n```\n\nYou can add your own constructor function to be recognised by this keyword:\n\n```javascript\nclass MyClass {}\nconst instanceofDef = require(\"ajv-keywords/dist/definitions/instanceof\")\ninstanceofDef.CONSTRUCTORS.MyClass = MyClass\najv.validate({instanceof: \"MyClass\"}, new MyClass()) // true\n```\n\n**Please note**: currently `instanceof` is not supported in [standalone validation code](https://github.com/ajv-validator/ajv/blob/master/docs/standalone.md) - it has to be implemented as [`code` keyword](https://github.com/ajv-validator/ajv/blob/master/docs/keywords.md#define-keyword-with-code-generation-function) to support it (PR is welcome).\n\n### Keywords for numbers\n\n#### `range` and `exclusiveRange`\n\nSyntax sugar for the combination of minimum and maximum keywords (or exclusiveMinimum and exclusiveMaximum), also fails schema compilation if there are no numbers in the range.\n\nThe value of these keywords must be an array consisting of two numbers, the second must be greater or equal than the first one.\n\nIf the validated value is not a number the validation passes, otherwise to pass validation the value should be greater (or equal) than the first number and smaller (or equal) than the second number in the array.\n\n```javascript\nconst schema = {type: \"number\", range: [1, 3]}\najv.validate(schema, 1) // true\najv.validate(schema, 2) // true\najv.validate(schema, 3) // true\najv.validate(schema, 0.99) // false\najv.validate(schema, 3.01) // false\n\nconst schema = {type: \"number\", exclusiveRange: [1, 3]}\najv.validate(schema, 1.01) // true\najv.validate(schema, 2) // true\najv.validate(schema, 2.99) // true\najv.validate(schema, 1) // false\najv.validate(schema, 3) // false\n```\n\n### Keywords for strings\n\n#### `regexp`\n\nThis keyword allows to use regular expressions with flags in schemas, and also without `\"u\"` flag when needed (the standard `pattern` keyword does not support flags and implies the presence of `\"u\"` flag).\n\nThis keyword applies only to strings. If the data is not a string, the validation succeeds.\n\nThe value of this keyword can be either a string (the result of `regexp.toString()`) or an object with the properties `pattern` and `flags` (the same strings that should be passed to RegExp constructor).\n\n```javascript\nconst schema = {\n  type: \"object\",\n  properties: {\n    foo: {type: \"string\", regexp: \"/foo/i\"},\n    bar: {type: \"string\", regexp: {pattern: \"bar\", flags: \"i\"}},\n  },\n}\n\nconst validData = {\n  foo: \"Food\",\n  bar: \"Barmen\",\n}\n\nconst invalidData = {\n  foo: \"fog\",\n  bar: \"bad\",\n}\n```\n\n#### `transform`\n\nThis keyword allows a string to be modified during validation.\n\nThis keyword applies only to strings. If the data is not a string, the `transform` keyword is ignored.\n\nA standalone string cannot be modified, i.e. `data = 'a'; ajv.validate(schema, data);`, because strings are passed by value\n\n**Supported transformations:**\n\n- `trim`: remove whitespace from start and end\n- `trimStart`/`trimLeft`: remove whitespace from start\n- `trimEnd`/`trimRight`: remove whitespace from end\n- `toLowerCase`: convert to lower case\n- `toUpperCase`: convert to upper case\n- `toEnumCase`: change string case to be equal to one of `enum` values in the schema\n\nTransformations are applied in the order they are listed.\n\nNote: `toEnumCase` requires that all allowed values are unique when case insensitive.\n\n**Example: multiple transformations**\n\n```javascript\nrequire(\"ajv-keywords\")(ajv, \"transform\")\n\nconst schema = {\n  type: \"array\",\n  items: {\n    type: \"string\",\n    transform: [\"trim\", \"toLowerCase\"],\n  },\n}\n\nconst data = [\"  MixCase  \"]\najv.validate(schema, data)\nconsole.log(data) // ['mixcase']\n```\n\n**Example: `enumcase`**\n\n```javascript\nrequire(\"ajv-keywords\")(ajv, [\"transform\"])\n\nconst schema = {\n  type: \"array\",\n  items: {\n    type: \"string\",\n    transform: [\"trim\", \"toEnumCase\"],\n    enum: [\"pH\"],\n  },\n}\n\nconst data = [\"ph\", \" Ph\", \"PH\", \"pH \"]\najv.validate(schema, data)\nconsole.log(data) // ['pH','pH','pH','pH']\n```\n\n### Keywords for arrays\n\n#### `uniqueItemProperties`\n\nThe keyword allows to check that some properties in array items are unique.\n\nThis keyword applies only to arrays. If the data is not an array, the validation succeeds.\n\nThe value of this keyword must be an array of strings - property names that should have unique values across all items.\n\n```javascript\nconst schema = {\n  type: \"array\",\n  uniqueItemProperties: [\"id\", \"name\"],\n}\n\nconst validData = [{id: 1}, {id: 2}, {id: 3}]\n\nconst invalidData1 = [\n  {id: 1},\n  {id: 1}, // duplicate \"id\"\n  {id: 3},\n]\n\nconst invalidData2 = [\n  {id: 1, name: \"taco\"},\n  {id: 2, name: \"taco\"}, // duplicate \"name\"\n  {id: 3, name: \"salsa\"},\n]\n```\n\nThis keyword is contributed by [@blainesch](https://github.com/blainesch).\n\n**Please note**: currently `uniqueItemProperties` is not supported in [standalone validation code](https://github.com/ajv-validator/ajv/blob/master/docs/standalone.md) - it has to be implemented as [`code` keyword](https://github.com/ajv-validator/ajv/blob/master/docs/keywords.md#define-keyword-with-code-generation-function) to support it (PR is welcome).\n\n### Keywords for objects\n\n#### `allRequired`\n\nThis keyword allows to require the presence of all properties used in `properties` keyword in the same schema object.\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value of this keyword must be boolean.\n\nIf the value of the keyword is `false`, the validation succeeds.\n\nIf the value of the keyword is `true`, the validation succeeds if the data contains all properties defined in `properties` keyword (in the same schema object).\n\nIf the `properties` keyword is not present in the same schema object, schema compilation will throw exception.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  properties: {\n    foo: {type: 'number'},\n    bar: {type: 'number'}\n  }\n  allRequired: true\n};\n\nconst validData = { foo: 1, bar: 2 };\nconst alsoValidData = { foo: 1, bar: 2, baz: 3 };\n\nconst invalidDataList = [ {}, { foo: 1 }, { bar: 2 } ];\n```\n\n#### `anyRequired`\n\nThis keyword allows to require the presence of any (at least one) property from the list.\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value of this keyword must be an array of strings, each string being a property name. For data object to be valid at least one of the properties in this array should be present in the object.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  anyRequired: [\"foo\", \"bar\"],\n}\n\nconst validData = {foo: 1}\nconst alsoValidData = {foo: 1, bar: 2}\n\nconst invalidDataList = [{}, {baz: 3}]\n```\n\n#### `oneRequired`\n\nThis keyword allows to require the presence of only one property from the list.\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value of this keyword must be an array of strings, each string being a property name. For data object to be valid exactly one of the properties in this array should be present in the object.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  oneRequired: [\"foo\", \"bar\"],\n}\n\nconst validData = {foo: 1}\nconst alsoValidData = {bar: 2, baz: 3}\n\nconst invalidDataList = [{}, {baz: 3}, {foo: 1, bar: 2}]\n```\n\n#### `patternRequired`\n\nThis keyword allows to require the presence of properties that match some pattern(s).\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value of this keyword should be an array of strings, each string being a regular expression. For data object to be valid each regular expression in this array should match at least one property name in the data object.\n\nIf the array contains multiple regular expressions, more than one expression can match the same property name.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  patternRequired: [\"f.*o\", \"b.*r\"],\n}\n\nconst validData = {foo: 1, bar: 2}\nconst alsoValidData = {foobar: 3}\n\nconst invalidDataList = [{}, {foo: 1}, {bar: 2}]\n```\n\n#### `prohibited`\n\nThis keyword allows to prohibit that any of the properties in the list is present in the object.\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value of this keyword should be an array of strings, each string being a property name. For data object to be valid none of the properties in this array should be present in the object.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  prohibited: [\"foo\", \"bar\"],\n}\n\nconst validData = {baz: 1}\nconst alsoValidData = {}\n\nconst invalidDataList = [{foo: 1}, {bar: 2}, {foo: 1, bar: 2}]\n```\n\n**Please note**: `{prohibited: ['foo', 'bar']}` is equivalent to `{not: {anyRequired: ['foo', 'bar']}}` (i.e. it has the same validation result for any data).\n\n#### `deepProperties`\n\nThis keyword allows to validate deep properties (identified by JSON pointers).\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value should be an object, where keys are JSON pointers to the data, starting from the current position in data, and the values are JSON schemas. For data object to be valid the value of each JSON pointer should be valid according to the corresponding schema.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  deepProperties: {\n    \"/users/1/role\": {enum: [\"admin\"]},\n  },\n}\n\nconst validData = {\n  users: [\n    {},\n    {\n      id: 123,\n      role: \"admin\",\n    },\n  ],\n}\n\nconst alsoValidData = {\n  users: {\n    1: {\n      id: 123,\n      role: \"admin\",\n    },\n  },\n}\n\nconst invalidData = {\n  users: [\n    {},\n    {\n      id: 123,\n      role: \"user\",\n    },\n  ],\n}\n\nconst alsoInvalidData = {\n  users: {\n    1: {\n      id: 123,\n      role: \"user\",\n    },\n  },\n}\n```\n\n#### `deepRequired`\n\nThis keyword allows to check that some deep properties (identified by JSON pointers) are available.\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value should be an array of JSON pointers to the data, starting from the current position in data. For data object to be valid each JSON pointer should be some existing part of the data.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  deepRequired: [\"/users/1/role\"],\n}\n\nconst validData = {\n  users: [\n    {},\n    {\n      id: 123,\n      role: \"admin\",\n    },\n  ],\n}\n\nconst invalidData = {\n  users: [\n    {},\n    {\n      id: 123,\n    },\n  ],\n}\n```\n\nSee [json-schema-org/json-schema-spec#203](https://github.com/json-schema-org/json-schema-spec/issues/203#issue-197211916) for an example of the equivalent schema without `deepRequired` keyword.\n\n### Keywords for all types\n\n#### `select`/`selectCases`/`selectDefault`\n\nThese keywords allow to choose the schema to validate the data based on the value of some property in the validated data.\n\nThese keywords must be present in the same schema object (`selectDefault` is optional).\n\nThe value of `select` keyword should be a [\\$data reference](https://github.com/ajv-validator/ajv/blob/master/docs/validation.md#data-reference) that points to any primitive JSON type (string, number, boolean or null) in the data that is validated. You can also use a constant of primitive type as the value of this keyword (e.g., for debugging purposes).\n\nThe value of `selectCases` keyword must be an object where each property name is a possible string representation of the value of `select` keyword and each property value is a corresponding schema (from draft-06 it can be boolean) that must be used to validate the data.\n\nThe value of `selectDefault` keyword is a schema (also can be boolean) that must be used to validate the data in case `selectCases` has no key equal to the stringified value of `select` keyword.\n\nThe validation succeeds in one of the following cases:\n\n- the validation of data using selected schema succeeds,\n- none of the schemas is selected for validation,\n- the value of select is undefined (no property in the data that the data reference points to).\n\nIf `select` value (in data) is not a primitive type the validation fails.\n\nThis keyword correctly tracks evaluated properties and items to work with `unevaluatedProperties` and `unevaluatedItems` keywords - only properties and items from the subschema that was used (one of `selectCases` subschemas or `selectDefault` subschema) are marked as evaluated.\n\n**Please note**: these keywords require Ajv `$data` option to support [\\$data reference](https://github.com/ajv-validator/ajv/blob/master/docs/validation.md#data-reference).\n\n```javascript\nrequire('ajv-keywords')(ajv, 'select');\n\nconst schema = {\n  type: \"object\",\n  required: ['kind'],\n  properties: {\n    kind: { type: 'string' }\n  },\n  select: { $data: '0/kind' },\n  selectCases: {\n    foo: {\n      required: ['foo'],\n      properties: {\n        kind: {},\n        foo: { type: 'string' }\n      },\n      additionalProperties: false\n    },\n    bar: {\n      required: ['bar'],\n      properties: {\n        kind: {},\n        bar: { type: 'number' }\n      },\n      additionalProperties: false\n    }\n  },\n  selectDefault: {\n    propertyNames: {\n      not: { enum: ['foo', 'bar'] }\n    }\n  }\n};\n\nconst validDataList = [\n  { kind: 'foo', foo: 'any' },\n  { kind: 'bar', bar: 1 },\n  { kind: 'anything_else', not_bar_or_foo: 'any value' }\n];\n\nconst invalidDataList = [\n  { kind: 'foo' }, // no property foo\n  { kind: 'bar' }, // no property bar\n  { kind: 'foo', foo: 'any', another: 'any value' }, // additional property\n  { kind: 'bar', bar: 1, another: 'any value' }, // additional property\n  { kind: 'anything_else', foo: 'any' } // property foo not allowed\n  { kind: 'anything_else', bar: 1 } // property bar not allowed\n];\n```\n\n#### `dynamicDefaults`\n\nThis keyword allows to assign dynamic defaults to properties, such as timestamps, unique IDs etc.\n\nThis keyword only works if `useDefaults` options is used and not inside `anyOf` keywords etc., in the same way as [default keyword treated by Ajv](https://github.com/epoberezkin/ajv#assigning-defaults).\n\nThe keyword should be added on the object level. Its value should be an object with each property corresponding to a property name, in the same way as in standard `properties` keyword. The value of each property can be:\n\n- an identifier of dynamic default function (a string)\n- an object with properties `func` (an identifier) and `args` (an object with parameters that will be passed to this function during schema compilation - see examples).\n\nThe properties used in `dynamicDefaults` should not be added to `required` keyword in the same schema (or validation will fail), because unlike `default` this keyword is processed after validation.\n\nThere are several predefined dynamic default functions:\n\n- `\"timestamp\"` - current timestamp in milliseconds\n- `\"datetime\"` - current date and time as string (ISO, valid according to `date-time` format)\n- `\"date\"` - current date as string (ISO, valid according to `date` format)\n- `\"time\"` - current time as string (ISO, valid according to `time` format)\n- `\"random\"` - pseudo-random number in [0, 1) interval\n- `\"randomint\"` - pseudo-random integer number. If string is used as a property value, the function will randomly return 0 or 1. If object `{ func: 'randomint', args: { max: N } }` is used then the default will be an integer number in [0, N) interval.\n- `\"seq\"` - sequential integer number starting from 0. If string is used as a property value, the default sequence will be used. If object `{ func: 'seq', args: { name: 'foo'} }` is used then the sequence with name `\"foo\"` will be used. Sequences are global, even if different ajv instances are used.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  dynamicDefaults: {\n    ts: \"datetime\",\n    r: {func: \"randomint\", args: {max: 100}},\n    id: {func: \"seq\", args: {name: \"id\"}},\n  },\n  properties: {\n    ts: {\n      type: \"string\",\n      format: \"date-time\",\n    },\n    r: {\n      type: \"integer\",\n      minimum: 0,\n      exclusiveMaximum: 100,\n    },\n    id: {\n      type: \"integer\",\n      minimum: 0,\n    },\n  },\n}\n\nconst data = {}\najv.validate(data) // true\ndata // { ts: '2016-12-01T22:07:28.829Z', r: 25, id: 0 }\n\nconst data1 = {}\najv.validate(data1) // true\ndata1 // { ts: '2016-12-01T22:07:29.832Z', r: 68, id: 1 }\n\najv.validate(data1) // true\ndata1 // didn't change, as all properties were defined\n```\n\nWhen using the `useDefaults` option value `\"empty\"`, properties and items equal to `null` or `\"\"` (empty string) will be considered missing and assigned defaults. Use `allOf` [compound keyword](https://github.com/epoberezkin/ajv/blob/master/KEYWORDS.md#compound-keywords) to execute `dynamicDefaults` before validation.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  allOf: [\n    {\n      dynamicDefaults: {\n        ts: \"datetime\",\n        r: {func: \"randomint\", args: {min: 5, max: 100}},\n        id: {func: \"seq\", args: {name: \"id\"}},\n      },\n    },\n    {\n      properties: {\n        ts: {\n          type: \"string\",\n        },\n        r: {\n          type: \"number\",\n          minimum: 5,\n          exclusiveMaximum: 100,\n        },\n        id: {\n          type: \"integer\",\n          minimum: 0,\n        },\n      },\n    },\n  ],\n}\n\nconst data = {ts: \"\", r: null}\najv.validate(data) // true\ndata // { ts: '2016-12-01T22:07:28.829Z', r: 25, id: 0 }\n```\n\nYou can add your own dynamic default function to be recognised by this keyword:\n\n```javascript\nconst uuid = require(\"uuid\")\n\nconst def = require(\"ajv-keywords/dist/definitions/dynamicDefaults\")\ndef.DEFAULTS.uuid = () => uuid.v4\n\nconst schema = {\n  dynamicDefaults: {id: \"uuid\"},\n  properties: {id: {type: \"string\", format: \"uuid\"}},\n}\n\nconst data = {}\najv.validate(schema, data) // true\ndata // { id: 'a1183fbe-697b-4030-9bcc-cfeb282a9150' };\n\nconst data1 = {}\najv.validate(schema, data1) // true\ndata1 // { id: '5b008de7-1669-467a-a5c6-70fa244d7209' }\n```\n\nYou also can define dynamic default that accept parameters, e.g. version of uuid:\n\n```javascript\nconst uuid = require(\"uuid\")\n\nfunction getUuid(args) {\n  const version = \"v\" + ((arvs && args.v) || \"4\")\n  return uuid[version]\n}\n\nconst def = require(\"ajv-keywords/dist/definitions/dynamicDefaults\")\ndef.DEFAULTS.uuid = getUuid\n\nconst schema = {\n  dynamicDefaults: {\n    id1: \"uuid\", // v4\n    id2: {func: \"uuid\", v: 4}, // v4\n    id3: {func: \"uuid\", v: 1}, // v1\n  },\n}\n```\n\n**Please note**: dynamic default functions are differentiated by the number of parameters they have (`function.length`). Functions that do not expect default must have one non-optional argument so that `function.length` > 0.\n\n`dynamicDefaults` is not supported in [standalone validation code](https://github.com/ajv-validator/ajv/blob/master/docs/standalone.md).\n\n## Security contact\n\nTo report a security vulnerability, please use the\n[Tidelift security contact](https://tidelift.com/security).\nTidelift will coordinate the fix and disclosure.\n\nPlease do NOT report security vulnerabilities via GitHub issues.\n\n## Open-source software support\n\nAjv-keywords is a part of [Tidelift subscription](https://tidelift.com/subscription/pkg/npm-ajv-keywords?utm_source=npm-ajv-keywords&utm_medium=referral&utm_campaign=readme) - it provides a centralised support to open-source software users, in addition to the support provided by software maintainers.\n\n## License\n\n[MIT](https://github.com/epoberezkin/ajv-keywords/blob/master/LICENSE)\n", "readmeFilename": "README.md", "_id": "ajv-keywords@5.0.0-beta.1", "_nodeVersion": "14.16.0", "_npmVersion": "6.14.11", "dist": {"integrity": "sha512-qFMpDpknrsJ1SUF8AV5ucmS8ZmoCz0YXuxIPWUBMhhl3JBAcUpgZuS79LMn6BmHcurLE4z2siHDnoNRTMNTWPA==", "shasum": "6d6505d88fa9a33f1a434e423c7597a13daa18ed", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-5.0.0-beta.1.tgz", "fileCount": 159, "unpackedSize": 130811, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWkAwCRA9TVsSAnZWagAAvHYQAI05MFVjAqRmE1CxF5No\ndyJcrciXp2qG4Mqg7sD6HICh6qowkddrp8MGvov5swyRn2+/SWWiqmZw0+tw\ndxGJisQJ0FCdwjx5B9Sp1X74wqwsSFsew7Az/okwpEMHAwC7XDeXXWmiYP36\nEnF8pWSWvZX4Ig9r10Xavf5O6vB4zBVowY+D2n8WFMBm8rBtGGRqVCbjGBBr\nSPlH1hJw6n+0iR5dQL8OVV3BnlZqlDFummEZ+9XMTgeB/pDqz5vO7O1A3PAE\nXgXYpTNv+M0SU5PSphATGgskZ36IEpDQZZhHBMnkzj0TbWr7CztdvhSrNGrf\nGmeyzLx3usb86i3DLq9C/UzLuNLLiw+Z17W4/eXyZ2Xupm4qVdm9vMtsBNTB\narcHhK8lOn6bxmb/9VTvemoRgNxUAGahlamR+mksRrzO+qyDj9kulshLW0sY\nS9eMI+6/KOFNaMB+Mdnxs+mcUc7kRoVM7QstABAt3ESowm2GBU/ECpSW6VrW\nEhVfdgyiG0puZft9jIK0kJqDlntEYHk5My9lGLg9D0l6f4J2iq1kQMj/SFTg\nVuqvhztyjosg1Dwtt/umrAxRGSKeb0OrhZCI9+tlXq23Ae/E8sxrZtDQSC+W\nBPKBenQqPCLgRaZ2ulnjr/3xRN7jVmn0v3Oc6UaZSwHOp655XYQtB6jHKfca\nuyxp\r\n=67JG\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIExzP8FOGUjv9PuxEsuSl5eC/PiHDTfZt8l7o2bjOV4dAiADI2tjKlXRDAJXy+Hj/YsH4ZlzmB+xEt0dvzxPXzFlrw=="}]}, "_npmUser": {"name": "esp", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ajv-keywords_5.0.0-beta.1_1616527407716_0.6754463728083031"}, "_hasShrinkwrap": false}, "4.0.1": {"name": "ajv-keywords", "version": "4.0.1", "description": "Custom JSON-Schema keywords for Ajv validator", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "rm -rf dist && tsc", "prepublish": "npm run build", "prettier:write": "prettier --write \"./**/*.{md,json,yaml,js,ts}\"", "prettier:check": "prettier --list-different \"./**/*.{md,json,yaml,js,ts}\"", "test": "npm link && npm link ajv-keywords && npm run eslint && npm run test-cov", "eslint": "eslint \"src/**/*.*s\" \"spec/**/*.*s\"", "test-spec": "jest spec/*.ts", "test-cov": "jest spec/*.ts --coverage"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/ajv-keywords.git"}, "keywords": ["JSON-Schema", "ajv", "keywords"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/ajv-keywords/issues"}, "homepage": "https://github.com/epoberezkin/ajv-keywords#readme", "peerDependencies": {"ajv": "^7.0.0"}, "devDependencies": {"@ajv-validator/config": "^0.2.3", "@types/chai": "^4.2.14", "@types/jest": "^26.0.14", "@types/node": "^14.11.10", "@types/uuid": "^8.3.0", "@typescript-eslint/eslint-plugin": "^4.4.1", "@typescript-eslint/parser": "^4.4.1", "ajv": "^7.0.0", "ajv-formats": "^1.5.1", "chai": "^4.2.0", "eslint": "^7.2.0", "eslint-config-prettier": "^7.0.0", "husky": "^4.3.0", "jest": "^26.5.3", "json-schema-test": "^2.0.0", "lint-staged": "^10.4.2", "prettier": "^2.1.2", "ts-jest": "^26.4.1", "typescript": "^4.0.3", "uuid": "^8.1.0"}, "prettier": "@ajv-validator/config/prettierrc.json", "husky": {"hooks": {"pre-commit": "lint-staged && npm test"}}, "lint-staged": {"*.{md,json,yaml,js,ts}": "prettier --write"}, "gitHead": "0270b73e82fe752ad57c36d613e746938d24d030", "_id": "ajv-keywords@4.0.1", "_nodeVersion": "14.16.0", "_npmVersion": "6.14.11", "dist": {"integrity": "sha512-vPmwZT6AL4R7kKrjKOMoKgnompJJS8MUgTB7dPBEknSSv4ahKgu046H+bGOH2eMRbAcz5pYz3fwaSwrCGNvuxA==", "shasum": "3fcd1988846b741ec958b2555c96ccecce2b3a59", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-4.0.1.tgz", "fileCount": 159, "unpackedSize": 130915, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXDW5CRA9TVsSAnZWagAA9YYP/00wFj752UIBkHePRRAd\nMpNYGtq9qj6dJElU0F2XCVo4iu8slKj3vEC4/mfzmgqRPFdErEpMasEQz4xF\n1MXKD863T0EutqhPddk+23+B6ElW2UmyG50yLuPgF75xFHK7oWQJje8A7b0c\nTWhNL8e0IwDazfa4n8o043e31OHAog5NzezjlkrSGOg1g9RBqtwlQXkl1ky8\nja58g8u6zkNVaw4SoW75VpT2opxt/IVkfgey6Hy8tNmOR6qwINP2FwfvIJh5\nLtRF0+RSomhgt0JDp2MeBBBbl1bBwrPvqobhix9remCvPa+YCE6cTnbLKAKN\nrd3AZxzdCJY7h+XXn1X9xYZ05zDm2QUMLD0E6x5pBbckNxpqCyLKP3TDPVH0\nBEkwrF5aAD4avV/mOvA4iAKw10+y4hCwO/fb91jajJ4FkDkjwfEKNs7egK0B\ndsqsi+Bu8Bg08CTqL7ookH+JJh3re/4134Fjc8Mz2GVgM8vkEBcDztDFKC+S\nEpCbWqfhhSpqdnbSSc5IMpznIFxfoM+Zscm7rjICKiyTDi33VosCvEaRHjyO\nr0rHJKeTn/QKtAMgEXQbLCqNUvWvEkIooI27wETcTYbZPBl69Zb3o2cvLx5U\nsNaLNJ0dSAvsi5vyDv6uMamHawHbSr1BoA4sxHq/gVD+tX6wu5PBiOL5p9CC\nLsMo\r\n=PFBf\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBJ4iW9YdOXK7vUzhcrc8jl+NYTb3SVPvxdNsDkE4E2nAiEAhwin4AvcXfG/2DzTlC+djt1+v216R2pTmkeTPftNExM="}]}, "_npmUser": {"name": "esp", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ajv-keywords_4.0.1_1616655800873_0.0741801034295777"}, "_hasShrinkwrap": false}, "5.0.0": {"name": "ajv-keywords", "version": "5.0.0", "description": "Additional JSON-Schema keywords for Ajv JSON validator", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "rm -rf dist && tsc", "prepublish": "npm run build", "prettier:write": "prettier --write \"./**/*.{md,json,yaml,js,ts}\"", "prettier:check": "prettier --list-different \"./**/*.{md,json,yaml,js,ts}\"", "test": "npm link && npm link ajv-keywords && npm run eslint && npm run test-cov", "eslint": "eslint \"src/**/*.*s\" \"spec/**/*.*s\"", "test-spec": "jest spec/*.ts", "test-cov": "jest spec/*.ts --coverage"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/ajv-keywords.git"}, "keywords": ["JSON-Schema", "ajv", "keywords"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/ajv-keywords/issues"}, "homepage": "https://github.com/epoberezkin/ajv-keywords#readme", "dependencies": {"fast-deep-equal": "^3.1.3"}, "peerDependencies": {"ajv": "^8.0.0"}, "devDependencies": {"@ajv-validator/config": "^0.2.3", "@types/chai": "^4.2.14", "@types/jest": "^26.0.14", "@types/node": "^14.11.10", "@types/uuid": "^8.3.0", "@typescript-eslint/eslint-plugin": "^4.4.1", "@typescript-eslint/parser": "^4.4.1", "ajv": "^8.0.0", "ajv-formats": "^2.0.0", "chai": "^4.2.0", "eslint": "^7.2.0", "eslint-config-prettier": "^7.0.0", "husky": "^4.3.0", "jest": "^26.5.3", "json-schema-test": "^2.0.0", "lint-staged": "^10.4.2", "prettier": "^2.1.2", "ts-jest": "^26.4.1", "typescript": "^4.2.0", "uuid": "^8.1.0"}, "prettier": "@ajv-validator/config/prettierrc.json", "husky": {"hooks": {"pre-commit": "lint-staged && npm test"}}, "lint-staged": {"*.{md,json,yaml,js,ts}": "prettier --write"}, "gitHead": "b698f4b882cda0326224feae554a56327889be0c", "_id": "ajv-keywords@5.0.0", "_nodeVersion": "14.16.0", "_npmVersion": "6.14.11", "dist": {"integrity": "sha512-ULd1QMjRoH6JDNUQIfDLrlE+OgZlFaxyYCjzt58uNuUQtKXt8/U+vK/8Ql0gyn/C5mqZzUWtKMqr/4YquvTrWA==", "shasum": "d01b3b21715b2f63d02aa511b82fc6eb3b30083c", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-5.0.0.tgz", "fileCount": 159, "unpackedSize": 131000, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgX6qhCRA9TVsSAnZWagAAWcwP/3doxt6kuGipC+osK0GL\nGhA3tLCqfn6G4ItkWr+/NK5AWqDPQ/vGkhYgQX4BNNb3FU/rFX0a1Txfixkt\n91+WJKM0I4Iffk0B0MkBU6oVB2LZ/JKE1rEHAFtoQrVL8HtuqEw3cKLk8P46\nRUlwJTL4IauQ+vuOVnnhAGigukr08ZpZCFX4N7Sht2I1Xr40VlT/AgeMDYJU\nGJjiFv8tX1ZlmjW39QPA+3zulZpkdRkSYfujJMrSxzlnPZV8OZr+T6po5vxZ\nbcbE+eJR8gu0AAev0fQRRSNz1gwJZwYi6REUummE8vhBI5vCd+zXrAbw4BMq\n6phVUplN0OCgMiUPU1kFejXYy2YWMbEFptQF2QwJxFc6O2lGmLsJl41F8l6R\nO7BUdOfR2gvCNI8asQKSLQ+qfMmjLZ3V8I+VESHD4gvOYUnXL0KD/zKMlwnI\nzwCAqsAyDJ690hgvy8ksrYbrnZr9rvj6jVcZRPzk7Xsaw/GdUo5pnuUhHD1v\nQ/sCz+ZdGQedOVWfyqhr/YV6yzp2TXmE9vzGS/J8+VAKCpJq5KWOo67SRriC\n9En0ji2SDzQ8sj0t9UXBYG5Yor8yw/80xdwJdNsPENhdnyfQoTnWJjN+BPA7\ncBxTyUM+BUuMO1ng4tcq3a53URlpcJck9RUA0IJ0Zbu2Tfaf4R5KqVImvIe6\nqL00\r\n=H9SU\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBuPn7vP1NeTh8bw28wqlzfagVjQKSoylqblcjk3dbFsAiEAjk/T3oKII3/14ZU9o0V0ebEpYH7qBT0WON3gJMLv8bc="}]}, "_npmUser": {"name": "esp", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ajv-keywords_5.0.0_1616882335692_0.6368208389442258"}, "_hasShrinkwrap": false}, "5.1.0": {"name": "ajv-keywords", "version": "5.1.0", "description": "Additional JSON-Schema keywords for Ajv JSON validator", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "rm -rf dist && tsc", "prepublish": "npm run build", "prettier:write": "prettier --write \"./**/*.{md,json,yaml,js,ts}\"", "prettier:check": "prettier --list-different \"./**/*.{md,json,yaml,js,ts}\"", "test": "npm link && npm link ajv-keywords && npm run eslint && npm run test-cov", "eslint": "eslint \"src/**/*.*s\" \"spec/**/*.*s\"", "test-spec": "jest spec/*.ts", "test-cov": "jest spec/*.ts --coverage"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/ajv-keywords.git"}, "keywords": ["JSON-Schema", "ajv", "keywords"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/ajv-keywords/issues"}, "homepage": "https://github.com/epoberezkin/ajv-keywords#readme", "dependencies": {"fast-deep-equal": "^3.1.3"}, "peerDependencies": {"ajv": "^8.8.2"}, "devDependencies": {"@ajv-validator/config": "^0.2.3", "@types/chai": "^4.2.14", "@types/jest": "^26.0.14", "@types/node": "^16.4.10", "@types/uuid": "^8.3.0", "@typescript-eslint/eslint-plugin": "^4.4.1", "@typescript-eslint/parser": "^4.4.1", "ajv": "^8.8.2", "ajv-formats": "^2.0.0", "chai": "^4.2.0", "eslint": "^7.2.0", "eslint-config-prettier": "^7.0.0", "husky": "^7.0.1", "jest": "^26.5.3", "json-schema-test": "^2.0.0", "lint-staged": "^11.1.1", "prettier": "^2.1.2", "ts-jest": "^26.4.1", "typescript": "^4.2.0", "uuid": "^8.1.0"}, "prettier": "@ajv-validator/config/prettierrc.json", "husky": {"hooks": {"pre-commit": "lint-staged && npm test"}}, "lint-staged": {"*.{md,json,yaml,js,ts}": "prettier --write"}, "gitHead": "7703b7826a284b851edb6f0004d997e8bbe2581c", "_id": "ajv-keywords@5.1.0", "_nodeVersion": "14.18.1", "_npmVersion": "6.14.15", "dist": {"integrity": "sha512-YCS/JNFAUyr5vAuhk1DWm1CBxRHW9LbJ2ozWeemrIqpbsqKjHVxYPyi5GC0rjZIT5JxJ3virVTS8wk4i/Z+krw==", "shasum": "69d4d385a4733cdbeab44964a1170a88f87f0e16", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-5.1.0.tgz", "fileCount": 159, "unpackedSize": 131806, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhmqEdCRA9TVsSAnZWagAAeHgQAJcRTbLU28z+qJjSAqt2\nj0KyFJUEu2/LC95IyLZx1GUvx5HrCmOdGvyyqIkM79iS8puffaEwjkZ03OmS\nRDpglaHF/aCf2Rh8Wa8y92+d2js8SPL7KnGEzKXR/V+hoCzjWobo3rtDRkYd\naBTQVFO8uOx9iHtqJY82IkF9xbzOTzG0MG4oRsUA95Ke3btn9B6OTXvZkV1H\nx/bowdTslZ6lYQQViH4N2VDhSgJguRYRRy83BBRQPH4Y+x6h1kaJ6SMLlKlR\nGRbE78aTqZRE8boOSUi72nGYR1dMMfD/bSeOIFmenKRuDSadR+as60enoa53\nBXBLwcJ9vEv2qHPhSZhM9i/OOtw83kkwjyiMZ15HmdspdUZrsNJMjvX3JoPj\nXyFkVi8//9TuxhgxfImitidsAHzoSLUXG8MjtJxoRKSvkuQbRZJ4rREjVtHH\n8BPn/mEzPK1RFGJq3px2+Ifiij/YXkU5HE3wqpeCWwlOqLz6Skg8VImEUFFN\nZOO8yva+jc6JlTaB5Jlay3jYixJTbsKWT//7sxf065bBHNk40tcZv7fuzS8j\n6l1qkhEuqi4+wO0OtVWVT6blZF2oKf6xX8s6HzaNt6rmrkdLX4b47Qrlkprb\naeVbvCbBaUUMrx6020JEpXZXsp65gE5YlEbHKigFOXWiPeRMMzooPISMrWZ7\nypdU\r\n=rGFp\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDGXa9A2eVjIcA38NFZcv3gJvfFP4As4/M9Lx2EmJxobQIgIKxEh6JHT86f2iX0PPZWWShpMPZ305MroBAqJ8/X55Q="}]}, "_npmUser": {"name": "esp", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ajv-keywords_5.1.0_1637523740930_0.18850100681118498"}, "_hasShrinkwrap": false}}, "readme": "# ajv-keywords\n\nCustom JSON-Schema keywords for [Ajv](https://github.com/epoberezkin/ajv) validator\n\n[![build](https://github.com/ajv-validator/ajv-keywords/workflows/build/badge.svg)](https://github.com/ajv-validator/ajv-keywords/actions?query=workflow%3Abuild)\n[![npm](https://img.shields.io/npm/v/ajv-keywords.svg)](https://www.npmjs.com/package/ajv-keywords)\n[![npm downloads](https://img.shields.io/npm/dm/ajv-keywords.svg)](https://www.npmjs.com/package/ajv-keywords)\n[![coverage](https://coveralls.io/repos/github/ajv-validator/ajv-keywords/badge.svg?branch=master)](https://coveralls.io/github/ajv-validator/ajv-keywords?branch=master)\n[![gitter](https://img.shields.io/gitter/room/ajv-validator/ajv.svg)](https://gitter.im/ajv-validator/ajv)\n\n**Please note**: This readme file is for [ajv-keywords v5.0.0](https://github.com/ajv-validator/ajv-keywords/releases/tag/v5.0.0) that should be used with [ajv v8](https://github.com/ajv-validator/ajv).\n\n[ajv-keywords v3](https://github.com/ajv-validator/ajv-keywords/tree/v3) should be used with [ajv v6](https://github.com/ajv-validator/ajv/tree/v6).\n\n## Contents\n\n- [Install](#install)\n- [Usage](#usage)\n- [Keywords](#keywords)\n  - [Types](#types)\n    - [typeof](#typeof)\n    - [instanceof](#instanceof)<sup>\\+</sup>\n  - [Keywords for numbers](#keywords-for-numbers)\n    - [range and exclusiveRange](#range-and-exclusiverange)\n  - [Keywords for strings](#keywords-for-strings)\n    - [regexp](#regexp)\n    - [transform](#transform)<sup>\\*</sup>\n  - [Keywords for arrays](#keywords-for-arrays)\n    - [uniqueItemProperties](#uniqueitemproperties)<sup>\\+</sup>\n  - [Keywords for objects](#keywords-for-objects)\n    - [allRequired](#allrequired)\n    - [anyRequired](#anyrequired)\n    - [oneRequired](#onerequired)\n    - [patternRequired](#patternrequired)\n    - [prohibited](#prohibited)\n    - [deepProperties](#deepproperties)\n    - [deepRequired](#deeprequired)\n    - [dynamicDefaults](#dynamicdefaults)<sup>\\*</sup><sup>\\+</sup>\n  - [Keywords for all types](#keywords-for-all-types)\n    - [select/selectCases/selectDefault](#selectselectcasesselectdefault)\n- [Security contact](#security-contact)\n- [Open-source software support](#open-source-software-support)\n- [License](#license)\n\n<sup>\\*</sup> - keywords that modify data\n<sup>\\+</sup> - keywords that are not supported in [standalone validation code](https://github.com/ajv-validator/ajv/blob/master/docs/standalone.md)\n\n## Install\n\nTo install version 4 to use with [Ajv v7](https://github.com/ajv-validator/ajv):\n\n```\nnpm install ajv-keywords\n```\n\n## Usage\n\nTo add all available keywords:\n\n```javascript\nconst Ajv = require(\"ajv\")\nconst ajv = new Ajv()\nrequire(\"ajv-keywords\")(ajv)\n\najv.validate({instanceof: \"RegExp\"}, /.*/) // true\najv.validate({instanceof: \"RegExp\"}, \".*\") // false\n```\n\nTo add a single keyword:\n\n```javascript\nrequire(\"ajv-keywords\")(ajv, \"instanceof\")\n```\n\nTo add multiple keywords:\n\n```javascript\nrequire(\"ajv-keywords\")(ajv, [\"typeof\", \"instanceof\"])\n```\n\nTo add a single keyword directly (to avoid adding unused code):\n\n```javascript\nrequire(\"ajv-keywords/dist/keywords/select\")(ajv, opts)\n```\n\nTo add all keywords via Ajv options:\n\n```javascript\nconst ajv = new Ajv({keywords: require(\"ajv-keywords/dist/definitions\")(opts)})\n```\n\nTo add one or several keywords via options:\n\n```javascript\nconst ajv = new Ajv({\n  keywords: [\n    require(\"ajv-keywords/dist/definitions/typeof\")(),\n    require(\"ajv-keywords/dist/definitions/instanceof\")(),\n    // select exports an array of 3 definitions - see \"select\" in docs\n    ...require(\"ajv-keywords/dist/definitions/select\")(opts),\n  ],\n})\n```\n\n`opts` is an optional object with a property `defaultMeta` - URI of meta-schema to use for keywords that use subschemas (`select` and `deepProperties`). The default is `\"http://json-schema.org/schema\"`.\n\n## Keywords\n\n### Types\n\n#### `typeof`\n\nBased on JavaScript `typeof` operation.\n\nThe value of the keyword should be a string (`\"undefined\"`, `\"string\"`, `\"number\"`, `\"object\"`, `\"function\"`, `\"boolean\"` or `\"symbol\"`) or an array of strings.\n\nTo pass validation the result of `typeof` operation on the value should be equal to the string (or one of the strings in the array).\n\n```javascript\najv.validate({typeof: \"undefined\"}, undefined) // true\najv.validate({typeof: \"undefined\"}, null) // false\najv.validate({typeof: [\"undefined\", \"object\"]}, null) // true\n```\n\n#### `instanceof`\n\nBased on JavaScript `instanceof` operation.\n\nThe value of the keyword should be a string (`\"Object\"`, `\"Array\"`, `\"Function\"`, `\"Number\"`, `\"String\"`, `\"Date\"`, `\"RegExp\"` or `\"Promise\"`) or an array of strings.\n\nTo pass validation the result of `data instanceof ...` operation on the value should be true:\n\n```javascript\najv.validate({instanceof: \"Array\"}, []) // true\najv.validate({instanceof: \"Array\"}, {}) // false\najv.validate({instanceof: [\"Array\", \"Function\"]}, function () {}) // true\n```\n\nYou can add your own constructor function to be recognised by this keyword:\n\n```javascript\nclass MyClass {}\nconst instanceofDef = require(\"ajv-keywords/dist/definitions/instanceof\")\ninstanceofDef.CONSTRUCTORS.MyClass = MyClass\najv.validate({instanceof: \"MyClass\"}, new MyClass()) // true\n```\n\n**Please note**: currently `instanceof` is not supported in [standalone validation code](https://github.com/ajv-validator/ajv/blob/master/docs/standalone.md) - it has to be implemented as [`code` keyword](https://github.com/ajv-validator/ajv/blob/master/docs/keywords.md#define-keyword-with-code-generation-function) to support it (PR is welcome).\n\n### Keywords for numbers\n\n#### `range` and `exclusiveRange`\n\nSyntax sugar for the combination of minimum and maximum keywords (or exclusiveMinimum and exclusiveMaximum), also fails schema compilation if there are no numbers in the range.\n\nThe value of these keywords must be an array consisting of two numbers, the second must be greater or equal than the first one.\n\nIf the validated value is not a number the validation passes, otherwise to pass validation the value should be greater (or equal) than the first number and smaller (or equal) than the second number in the array.\n\n```javascript\nconst schema = {type: \"number\", range: [1, 3]}\najv.validate(schema, 1) // true\najv.validate(schema, 2) // true\najv.validate(schema, 3) // true\najv.validate(schema, 0.99) // false\najv.validate(schema, 3.01) // false\n\nconst schema = {type: \"number\", exclusiveRange: [1, 3]}\najv.validate(schema, 1.01) // true\najv.validate(schema, 2) // true\najv.validate(schema, 2.99) // true\najv.validate(schema, 1) // false\najv.validate(schema, 3) // false\n```\n\n### Keywords for strings\n\n#### `regexp`\n\nThis keyword allows to use regular expressions with flags in schemas, and also without `\"u\"` flag when needed (the standard `pattern` keyword does not support flags and implies the presence of `\"u\"` flag).\n\nThis keyword applies only to strings. If the data is not a string, the validation succeeds.\n\nThe value of this keyword can be either a string (the result of `regexp.toString()`) or an object with the properties `pattern` and `flags` (the same strings that should be passed to RegExp constructor).\n\n```javascript\nconst schema = {\n  type: \"object\",\n  properties: {\n    foo: {type: \"string\", regexp: \"/foo/i\"},\n    bar: {type: \"string\", regexp: {pattern: \"bar\", flags: \"i\"}},\n  },\n}\n\nconst validData = {\n  foo: \"Food\",\n  bar: \"Barmen\",\n}\n\nconst invalidData = {\n  foo: \"fog\",\n  bar: \"bad\",\n}\n```\n\n#### `transform`\n\nThis keyword allows a string to be modified during validation.\n\nThis keyword applies only to strings. If the data is not a string, the `transform` keyword is ignored.\n\nA standalone string cannot be modified, i.e. `data = 'a'; ajv.validate(schema, data);`, because strings are passed by value\n\n**Supported transformations:**\n\n- `trim`: remove whitespace from start and end\n- `trimStart`/`trimLeft`: remove whitespace from start\n- `trimEnd`/`trimRight`: remove whitespace from end\n- `toLowerCase`: convert to lower case\n- `toUpperCase`: convert to upper case\n- `toEnumCase`: change string case to be equal to one of `enum` values in the schema\n\nTransformations are applied in the order they are listed.\n\nNote: `toEnumCase` requires that all allowed values are unique when case insensitive.\n\n**Example: multiple transformations**\n\n```javascript\nrequire(\"ajv-keywords\")(ajv, \"transform\")\n\nconst schema = {\n  type: \"array\",\n  items: {\n    type: \"string\",\n    transform: [\"trim\", \"toLowerCase\"],\n  },\n}\n\nconst data = [\"  MixCase  \"]\najv.validate(schema, data)\nconsole.log(data) // ['mixcase']\n```\n\n**Example: `enumcase`**\n\n```javascript\nrequire(\"ajv-keywords\")(ajv, [\"transform\"])\n\nconst schema = {\n  type: \"array\",\n  items: {\n    type: \"string\",\n    transform: [\"trim\", \"toEnumCase\"],\n    enum: [\"pH\"],\n  },\n}\n\nconst data = [\"ph\", \" Ph\", \"PH\", \"pH \"]\najv.validate(schema, data)\nconsole.log(data) // ['pH','pH','pH','pH']\n```\n\n### Keywords for arrays\n\n#### `uniqueItemProperties`\n\nThe keyword allows to check that some properties in array items are unique.\n\nThis keyword applies only to arrays. If the data is not an array, the validation succeeds.\n\nThe value of this keyword must be an array of strings - property names that should have unique values across all items.\n\n```javascript\nconst schema = {\n  type: \"array\",\n  uniqueItemProperties: [\"id\", \"name\"],\n}\n\nconst validData = [{id: 1}, {id: 2}, {id: 3}]\n\nconst invalidData1 = [\n  {id: 1},\n  {id: 1}, // duplicate \"id\"\n  {id: 3},\n]\n\nconst invalidData2 = [\n  {id: 1, name: \"taco\"},\n  {id: 2, name: \"taco\"}, // duplicate \"name\"\n  {id: 3, name: \"salsa\"},\n]\n```\n\nThis keyword is contributed by [@blainesch](https://github.com/blainesch).\n\n**Please note**: currently `uniqueItemProperties` is not supported in [standalone validation code](https://github.com/ajv-validator/ajv/blob/master/docs/standalone.md) - it has to be implemented as [`code` keyword](https://github.com/ajv-validator/ajv/blob/master/docs/keywords.md#define-keyword-with-code-generation-function) to support it (PR is welcome).\n\n### Keywords for objects\n\n#### `allRequired`\n\nThis keyword allows to require the presence of all properties used in `properties` keyword in the same schema object.\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value of this keyword must be boolean.\n\nIf the value of the keyword is `false`, the validation succeeds.\n\nIf the value of the keyword is `true`, the validation succeeds if the data contains all properties defined in `properties` keyword (in the same schema object).\n\nIf the `properties` keyword is not present in the same schema object, schema compilation will throw exception.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  properties: {\n    foo: {type: \"number\"},\n    bar: {type: \"number\"},\n  },\n  allRequired: true,\n}\n\nconst validData = {foo: 1, bar: 2}\nconst alsoValidData = {foo: 1, bar: 2, baz: 3}\n\nconst invalidDataList = [{}, {foo: 1}, {bar: 2}]\n```\n\n#### `anyRequired`\n\nThis keyword allows to require the presence of any (at least one) property from the list.\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value of this keyword must be an array of strings, each string being a property name. For data object to be valid at least one of the properties in this array should be present in the object.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  anyRequired: [\"foo\", \"bar\"],\n}\n\nconst validData = {foo: 1}\nconst alsoValidData = {foo: 1, bar: 2}\n\nconst invalidDataList = [{}, {baz: 3}]\n```\n\n#### `oneRequired`\n\nThis keyword allows to require the presence of only one property from the list.\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value of this keyword must be an array of strings, each string being a property name. For data object to be valid exactly one of the properties in this array should be present in the object.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  oneRequired: [\"foo\", \"bar\"],\n}\n\nconst validData = {foo: 1}\nconst alsoValidData = {bar: 2, baz: 3}\n\nconst invalidDataList = [{}, {baz: 3}, {foo: 1, bar: 2}]\n```\n\n#### `patternRequired`\n\nThis keyword allows to require the presence of properties that match some pattern(s).\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value of this keyword should be an array of strings, each string being a regular expression. For data object to be valid each regular expression in this array should match at least one property name in the data object.\n\nIf the array contains multiple regular expressions, more than one expression can match the same property name.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  patternRequired: [\"f.*o\", \"b.*r\"],\n}\n\nconst validData = {foo: 1, bar: 2}\nconst alsoValidData = {foobar: 3}\n\nconst invalidDataList = [{}, {foo: 1}, {bar: 2}]\n```\n\n#### `prohibited`\n\nThis keyword allows to prohibit that any of the properties in the list is present in the object.\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value of this keyword should be an array of strings, each string being a property name. For data object to be valid none of the properties in this array should be present in the object.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  prohibited: [\"foo\", \"bar\"],\n}\n\nconst validData = {baz: 1}\nconst alsoValidData = {}\n\nconst invalidDataList = [{foo: 1}, {bar: 2}, {foo: 1, bar: 2}]\n```\n\n**Please note**: `{prohibited: ['foo', 'bar']}` is equivalent to `{not: {anyRequired: ['foo', 'bar']}}` (i.e. it has the same validation result for any data).\n\n#### `deepProperties`\n\nThis keyword allows to validate deep properties (identified by JSON pointers).\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value should be an object, where keys are JSON pointers to the data, starting from the current position in data, and the values are JSON schemas. For data object to be valid the value of each JSON pointer should be valid according to the corresponding schema.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  deepProperties: {\n    \"/users/1/role\": {enum: [\"admin\"]},\n  },\n}\n\nconst validData = {\n  users: [\n    {},\n    {\n      id: 123,\n      role: \"admin\",\n    },\n  ],\n}\n\nconst alsoValidData = {\n  users: {\n    1: {\n      id: 123,\n      role: \"admin\",\n    },\n  },\n}\n\nconst invalidData = {\n  users: [\n    {},\n    {\n      id: 123,\n      role: \"user\",\n    },\n  ],\n}\n\nconst alsoInvalidData = {\n  users: {\n    1: {\n      id: 123,\n      role: \"user\",\n    },\n  },\n}\n```\n\n#### `deepRequired`\n\nThis keyword allows to check that some deep properties (identified by JSON pointers) are available.\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value should be an array of JSON pointers to the data, starting from the current position in data. For data object to be valid each JSON pointer should be some existing part of the data.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  deepRequired: [\"/users/1/role\"],\n}\n\nconst validData = {\n  users: [\n    {},\n    {\n      id: 123,\n      role: \"admin\",\n    },\n  ],\n}\n\nconst invalidData = {\n  users: [\n    {},\n    {\n      id: 123,\n    },\n  ],\n}\n```\n\nSee [json-schema-org/json-schema-spec#203](https://github.com/json-schema-org/json-schema-spec/issues/203#issue-197211916) for an example of the equivalent schema without `deepRequired` keyword.\n\n### Keywords for all types\n\n#### `select`/`selectCases`/`selectDefault`\n\n**Please note**: these keywords are deprecated. It is recommended to use OpenAPI [discriminator](https://ajv.js.org/json-schema.html#discriminator) keyword supported by Ajv v8 instead of `select`.\n\nThese keywords allow to choose the schema to validate the data based on the value of some property in the validated data.\n\nThese keywords must be present in the same schema object (`selectDefault` is optional).\n\nThe value of `select` keyword should be a [\\$data reference](https://github.com/ajv-validator/ajv/blob/master/docs/validation.md#data-reference) that points to any primitive JSON type (string, number, boolean or null) in the data that is validated. You can also use a constant of primitive type as the value of this keyword (e.g., for debugging purposes).\n\nThe value of `selectCases` keyword must be an object where each property name is a possible string representation of the value of `select` keyword and each property value is a corresponding schema (from draft-06 it can be boolean) that must be used to validate the data.\n\nThe value of `selectDefault` keyword is a schema (also can be boolean) that must be used to validate the data in case `selectCases` has no key equal to the stringified value of `select` keyword.\n\nThe validation succeeds in one of the following cases:\n\n- the validation of data using selected schema succeeds,\n- none of the schemas is selected for validation,\n- the value of select is undefined (no property in the data that the data reference points to).\n\nIf `select` value (in data) is not a primitive type the validation fails.\n\nThis keyword correctly tracks evaluated properties and items to work with `unevaluatedProperties` and `unevaluatedItems` keywords - only properties and items from the subschema that was used (one of `selectCases` subschemas or `selectDefault` subschema) are marked as evaluated.\n\n**Please note**: these keywords require Ajv `$data` option to support [\\$data reference](https://github.com/ajv-validator/ajv/blob/master/docs/validation.md#data-reference).\n\n```javascript\nrequire(\"ajv-keywords\")(ajv, \"select\")\n\nconst schema = {\n  type: \"object\",\n  required: [\"kind\"],\n  properties: {\n    kind: {type: \"string\"},\n  },\n  select: {$data: \"0/kind\"},\n  selectCases: {\n    foo: {\n      required: [\"foo\"],\n      properties: {\n        kind: {},\n        foo: {type: \"string\"},\n      },\n      additionalProperties: false,\n    },\n    bar: {\n      required: [\"bar\"],\n      properties: {\n        kind: {},\n        bar: {type: \"number\"},\n      },\n      additionalProperties: false,\n    },\n  },\n  selectDefault: {\n    propertyNames: {\n      not: {enum: [\"foo\", \"bar\"]},\n    },\n  },\n}\n\nconst validDataList = [\n  {kind: \"foo\", foo: \"any\"},\n  {kind: \"bar\", bar: 1},\n  {kind: \"anything_else\", not_bar_or_foo: \"any value\"},\n]\n\nconst invalidDataList = [\n  {kind: \"foo\"}, // no property foo\n  {kind: \"bar\"}, // no property bar\n  {kind: \"foo\", foo: \"any\", another: \"any value\"}, // additional property\n  {kind: \"bar\", bar: 1, another: \"any value\"}, // additional property\n  {kind: \"anything_else\", foo: \"any\"}, // property foo not allowed\n  {kind: \"anything_else\", bar: 1}, // property bar not allowed\n]\n```\n\n#### `dynamicDefaults`\n\nThis keyword allows to assign dynamic defaults to properties, such as timestamps, unique IDs etc.\n\nThis keyword only works if `useDefaults` options is used and not inside `anyOf` keywords etc., in the same way as [default keyword treated by Ajv](https://github.com/epoberezkin/ajv#assigning-defaults).\n\nThe keyword should be added on the object level. Its value should be an object with each property corresponding to a property name, in the same way as in standard `properties` keyword. The value of each property can be:\n\n- an identifier of dynamic default function (a string)\n- an object with properties `func` (an identifier) and `args` (an object with parameters that will be passed to this function during schema compilation - see examples).\n\nThe properties used in `dynamicDefaults` should not be added to `required` keyword in the same schema (or validation will fail), because unlike `default` this keyword is processed after validation.\n\nThere are several predefined dynamic default functions:\n\n- `\"timestamp\"` - current timestamp in milliseconds\n- `\"datetime\"` - current date and time as string (ISO, valid according to `date-time` format)\n- `\"date\"` - current date as string (ISO, valid according to `date` format)\n- `\"time\"` - current time as string (ISO, valid according to `time` format)\n- `\"random\"` - pseudo-random number in [0, 1) interval\n- `\"randomint\"` - pseudo-random integer number. If string is used as a property value, the function will randomly return 0 or 1. If object `{ func: 'randomint', args: { max: N } }` is used then the default will be an integer number in [0, N) interval.\n- `\"seq\"` - sequential integer number starting from 0. If string is used as a property value, the default sequence will be used. If object `{ func: 'seq', args: { name: 'foo'} }` is used then the sequence with name `\"foo\"` will be used. Sequences are global, even if different ajv instances are used.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  dynamicDefaults: {\n    ts: \"datetime\",\n    r: {func: \"randomint\", args: {max: 100}},\n    id: {func: \"seq\", args: {name: \"id\"}},\n  },\n  properties: {\n    ts: {\n      type: \"string\",\n      format: \"date-time\",\n    },\n    r: {\n      type: \"integer\",\n      minimum: 0,\n      exclusiveMaximum: 100,\n    },\n    id: {\n      type: \"integer\",\n      minimum: 0,\n    },\n  },\n}\n\nconst data = {}\najv.validate(data) // true\ndata // { ts: '2016-12-01T22:07:28.829Z', r: 25, id: 0 }\n\nconst data1 = {}\najv.validate(data1) // true\ndata1 // { ts: '2016-12-01T22:07:29.832Z', r: 68, id: 1 }\n\najv.validate(data1) // true\ndata1 // didn't change, as all properties were defined\n```\n\nWhen using the `useDefaults` option value `\"empty\"`, properties and items equal to `null` or `\"\"` (empty string) will be considered missing and assigned defaults. Use `allOf` [compound keyword](https://github.com/epoberezkin/ajv/blob/master/KEYWORDS.md#compound-keywords) to execute `dynamicDefaults` before validation.\n\n```javascript\nconst schema = {\n  type: \"object\",\n  allOf: [\n    {\n      dynamicDefaults: {\n        ts: \"datetime\",\n        r: {func: \"randomint\", args: {min: 5, max: 100}},\n        id: {func: \"seq\", args: {name: \"id\"}},\n      },\n    },\n    {\n      properties: {\n        ts: {\n          type: \"string\",\n        },\n        r: {\n          type: \"number\",\n          minimum: 5,\n          exclusiveMaximum: 100,\n        },\n        id: {\n          type: \"integer\",\n          minimum: 0,\n        },\n      },\n    },\n  ],\n}\n\nconst data = {ts: \"\", r: null}\najv.validate(data) // true\ndata // { ts: '2016-12-01T22:07:28.829Z', r: 25, id: 0 }\n```\n\nYou can add your own dynamic default function to be recognised by this keyword:\n\n```javascript\nconst uuid = require(\"uuid\")\n\nconst def = require(\"ajv-keywords/dist/definitions/dynamicDefaults\")\ndef.DEFAULTS.uuid = () => uuid.v4\n\nconst schema = {\n  dynamicDefaults: {id: \"uuid\"},\n  properties: {id: {type: \"string\", format: \"uuid\"}},\n}\n\nconst data = {}\najv.validate(schema, data) // true\ndata // { id: 'a1183fbe-697b-4030-9bcc-cfeb282a9150' };\n\nconst data1 = {}\najv.validate(schema, data1) // true\ndata1 // { id: '5b008de7-1669-467a-a5c6-70fa244d7209' }\n```\n\nYou also can define dynamic default that accept parameters, e.g. version of uuid:\n\n```javascript\nconst uuid = require(\"uuid\")\n\nfunction getUuid(args) {\n  const version = \"v\" + ((arvs && args.v) || \"4\")\n  return uuid[version]\n}\n\nconst def = require(\"ajv-keywords/dist/definitions/dynamicDefaults\")\ndef.DEFAULTS.uuid = getUuid\n\nconst schema = {\n  dynamicDefaults: {\n    id1: \"uuid\", // v4\n    id2: {func: \"uuid\", v: 4}, // v4\n    id3: {func: \"uuid\", v: 1}, // v1\n  },\n}\n```\n\n**Please note**: dynamic default functions are differentiated by the number of parameters they have (`function.length`). Functions that do not expect default must have one non-optional argument so that `function.length` > 0.\n\n`dynamicDefaults` is not supported in [standalone validation code](https://github.com/ajv-validator/ajv/blob/master/docs/standalone.md).\n\n## Security contact\n\nTo report a security vulnerability, please use the\n[Tidelift security contact](https://tidelift.com/security).\nTidelift will coordinate the fix and disclosure.\n\nPlease do NOT report security vulnerabilities via GitHub issues.\n\n## Open-source software support\n\nAjv-keywords is a part of [Tidelift subscription](https://tidelift.com/subscription/pkg/npm-ajv-keywords?utm_source=npm-ajv-keywords&utm_medium=referral&utm_campaign=readme) - it provides a centralised support to open-source software users, in addition to the support provided by software maintainers.\n\n## License\n\n[MIT](https://github.com/epoberezkin/ajv-keywords/blob/master/LICENSE)\n", "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "time": {"modified": "2022-06-13T02:34:05.200Z", "created": "2016-06-05T21:00:43.250Z", "0.1.0": "2016-06-05T21:00:43.250Z", "0.1.1": "2016-06-05T21:28:47.272Z", "0.2.0": "2016-06-11T12:00:43.707Z", "1.0.0": "2016-09-14T23:05:08.615Z", "1.1.0": "2016-09-21T21:50:22.216Z", "1.1.1": "2016-09-24T18:17:50.700Z", "1.2.0": "2016-12-02T09:46:03.211Z", "1.2.1": "2016-12-22T21:01:31.368Z", "1.3.0": "2016-12-22T22:13:50.376Z", "1.4.0": "2016-12-23T16:27:01.104Z", "1.4.1": "2016-12-23T17:51:36.947Z", "1.5.0": "2016-12-28T21:25:47.137Z", "2.0.0-beta.0": "2016-12-31T01:04:11.339Z", "2.0.0-beta.1": "2017-01-22T17:50:16.765Z", "1.5.1": "2017-01-22T17:51:58.685Z", "2.0.0-beta.2": "2017-01-22T18:00:44.614Z", "2.0.1-beta.0": "2017-02-13T22:40:12.000Z", "2.0.1-beta.1": "2017-03-04T21:29:42.409Z", "2.0.1-beta.2": "2017-03-17T20:01:16.136Z", "2.0.0": "2017-04-17T15:09:28.339Z", "2.1.0": "2017-05-29T20:05:47.796Z", "2.1.1": "2017-10-29T12:00:20.862Z", "3.0.0-beta.0": "2017-10-29T12:01:12.358Z", "3.0.0": "2018-01-07T18:17:35.778Z", "3.1.0": "2018-01-31T20:35:20.085Z", "3.2.0": "2018-04-28T09:30:06.942Z", "3.3.0": "2019-01-27T09:56:06.825Z", "3.4.0": "2019-02-10T15:39:59.976Z", "3.4.1": "2019-07-06T19:48:38.848Z", "3.5.0": "2020-06-19T14:43:42.379Z", "3.5.1": "2020-07-07T20:48:39.245Z", "3.5.2": "2020-07-27T14:14:03.393Z", "4.0.0-beta.0": "2020-10-24T19:27:00.954Z", "4.0.0-beta.1": "2020-11-05T09:01:02.968Z", "4.0.0-beta.2": "2020-11-17T08:43:38.049Z", "4.0.0-beta.3": "2020-11-29T20:02:27.938Z", "4.0.0-rc.0": "2020-12-11T08:57:21.057Z", "4.0.0": "2020-12-15T19:47:44.246Z", "5.0.0-beta.0": "2021-03-13T14:54:00.223Z", "5.0.0-beta.1": "2021-03-23T19:23:27.860Z", "4.0.1": "2021-03-25T07:03:20.999Z", "5.0.0": "2021-03-27T21:58:55.898Z", "5.1.0": "2021-11-21T19:42:21.146Z"}, "homepage": "https://github.com/epoberezkin/ajv-keywords#readme", "keywords": ["JSON-Schema", "ajv", "keywords"], "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/ajv-keywords.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "bugs": {"url": "https://github.com/epoberezkin/ajv-keywords/issues"}, "license": "MIT", "readmeFilename": "README.md", "users": {"esp": true, "kakaman": true, "shuoshubao": true, "sinfex": true, "deividasjackus": true, "thevikingcoder": true, "esilva2902": true}}