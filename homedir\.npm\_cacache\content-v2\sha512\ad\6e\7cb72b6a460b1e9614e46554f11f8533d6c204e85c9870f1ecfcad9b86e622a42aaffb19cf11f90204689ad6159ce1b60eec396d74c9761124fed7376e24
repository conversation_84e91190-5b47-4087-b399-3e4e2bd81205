{"name": "braces", "dist-tags": {"latest": "3.0.3"}, "versions": {"0.1.0": {"name": "braces", "version": "0.1.0", "dependencies": {"expand-range": "^0.1.0"}, "devDependencies": {"verb": ">= 0.2.6", "mocha": "*", "lodash": "^2.4.1", "should": "^4.1.0", "minimatch": "^1.0.0", "benchmarked": "^0.1.1", "brace-expansion": "0.0.0", "verb-tag-jscomments": ">= 0.2.0"}, "dist": {"shasum": "2ea567d1f9622ba2395135bbf81fe140da7fefeb", "tarball": "https://registry.npmjs.org/braces/-/braces-0.1.0.tgz", "integrity": "sha512-59/QQZW1FIbvnxuRWTNaulRIvblwjvGKwz88r/HJIN1TE0N12M2pbRNZpYUWuE7DAyl8C2ucWeX9M7DYWnZVpg==", "signatures": [{"sig": "MEYCIQCOL3eGBoq0k7nH2F691tD0m6exLWdtfTnzmOp3Z53VJAIhAJMIiKzk64c+FOlumGZrtXflk2hTJ61cqjUxmG7RDMbN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "0.1.1": {"name": "braces", "version": "0.1.1", "dependencies": {"expand-range": "^0.1.0"}, "devDependencies": {"verb": ">= 0.2.6", "mocha": "*", "lodash": "^2.4.1", "should": "^4.1.0", "minimatch": "^1.0.0", "benchmarked": "^0.1.1", "brace-expansion": "0.0.0", "verb-tag-jscomments": ">= 0.2.0"}, "dist": {"shasum": "9b3c273b426d1a7b3c9265094a9434ab84643ba3", "tarball": "https://registry.npmjs.org/braces/-/braces-0.1.1.tgz", "integrity": "sha512-biKFllQsz6LzOEwAFWX/CMy8NtDzGwXYUXFgNWWRuehPC3X0okAb5lEFf2PtnHwHYe6MoJZD0fuy+BFtoJC5Sg==", "signatures": [{"sig": "MEQCIHpjFQZQTuLjI2kVC0/j4P7nCNB6h/bqDAwNiyjYMUucAiBrPb8F3o5/9t0ABtKhRDH6uEUPCU48IUyR+H84pUiqrQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "0.1.2": {"name": "braces", "version": "0.1.2", "dependencies": {"expand-range": "^0.1.0"}, "devDependencies": {"verb": ">= 0.2.6", "mocha": "*", "lodash": "^2.4.1", "should": "^4.1.0", "minimatch": "^1.0.0", "benchmarked": "^0.1.1", "brace-expansion": "0.0.0", "verb-tag-jscomments": ">= 0.2.0"}, "dist": {"shasum": "f794ff9aabe60eaf557e9607cc850c8f531a12be", "tarball": "https://registry.npmjs.org/braces/-/braces-0.1.2.tgz", "integrity": "sha512-tQ1ltvLIdYGIpKDwnjDqqJ7ZYxDOHsJiBNgp2N9RsEAtPzTcKq3aJlQ40+qXWYHFpy4cC5wthznUE2M89xlMSw==", "signatures": [{"sig": "MEUCIQCNq6CFY2R4ob2MAgFcDmF2kxY/bNOvA0IFG49cedj+wgIgVqDI7ub/vsFCPDEb5ljAVtDVisI9QjnxJIMzi04RaKU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "0.1.4": {"name": "braces", "version": "0.1.4", "dependencies": {"expand-range": "^0.1.0"}, "devDependencies": {"mocha": "*", "lodash": "^2.4.1", "should": "^4.1.0", "minimatch": "^1.0.0", "benchmarked": "^0.1.1", "brace-expansion": "0.0.0"}, "dist": {"shasum": "45ed440222debb0fec60b06bb75059e421c590fd", "tarball": "https://registry.npmjs.org/braces/-/braces-0.1.4.tgz", "integrity": "sha512-5ZaWT3BXeL4rfMmOgZUktJ3YExoTc/tfHFV8xJRb5At1uATRxLwXJY/cItMFzCF4e33yPZWOR08mbmUJneCQIw==", "signatures": [{"sig": "MEQCIDY8GVKmrYczFFWJwUqAAHdRXL7ERQbSaGpn+Z+Eov/TAiBn/z7Xq1jxNbeoZd7j8GgmZVjOZm7KegKIOPxFzCtxgw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "0.1.5": {"name": "braces", "version": "0.1.5", "dependencies": {"expand-range": "^0.1.0"}, "devDependencies": {"mocha": "*", "lodash": "^2.4.1", "should": "^4.1.0", "minimatch": "^1.0.0", "benchmarked": "^0.1.1", "brace-expansion": "0.0.0"}, "dist": {"shasum": "c085711085291d8b75fdd74eab0f8597280711e6", "tarball": "https://registry.npmjs.org/braces/-/braces-0.1.5.tgz", "integrity": "sha512-EIMHIv2UXHWFY2xubUGKz+hq9hNkENj4Pjvr7h58cmJgpkK2yMlKA8I484f7MSttkzVAy/lL7X9xDaILd6avzA==", "signatures": [{"sig": "MEYCIQCDFqf6OmryqizB/xGTkW3Sv+ZJAWKx7c1g6DJJpjfrGAIhAO+baQ23z9OplIU4Ba9KhNamedIpq8a9EarynCSgYPck", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.0.0": {"name": "braces", "version": "1.0.0", "dependencies": {"expand-range": "^0.3.1"}, "devDependencies": {"mocha": "^2.1.0", "write": "^0.1.1", "should": "^4.4.1", "minimatch": "^2.0.1", "benchmarked": "^0.1.3", "brace-expansion": "^1.1.0"}, "dist": {"shasum": "991c57970805294a6f1a8aade96c9037fcea1246", "tarball": "https://registry.npmjs.org/braces/-/braces-1.0.0.tgz", "integrity": "sha512-96tjnHiAQnke1h8ibSkJXk60GMG2JFSad0zwnM2duZDfWtJXEyJ8xDlRoMLvPPotk/0uJtAk2XIp0VwLrJ+GTg==", "signatures": [{"sig": "MEYCIQC9Sj6lFLjKhO6jg4sjR7WZRVfDkhcIsbNSjDL3LhJXLwIhANRKzJ078ZWecvsGRHEWRwDm0fDmioQBnsEVueKuqwn0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.1.0": {"name": "braces", "version": "1.1.0", "dependencies": {"preserve": "^0.2.0", "arr-filter": "^1.0.0", "expand-range": "^1.1.0"}, "devDependencies": {"chalk": "^0.5.1", "mocha": "*", "write": "^0.1.1", "should": "*", "minimist": "^1.1.0", "minimatch": "^2.0.1", "benchmarked": "^0.1.3", "brace-expansion": "^1.1.0"}, "dist": {"shasum": "1fd01ee8420a7196d234f2a29dc1bfe6e259854b", "tarball": "https://registry.npmjs.org/braces/-/braces-1.1.0.tgz", "integrity": "sha512-/41xAQljVWmYtUjUrfjRGdEAUD/Z4fEJKziIDuQN20hniJX/opnWHGjyvNhf7u920PinSkh58l7vsjM43HpcmA==", "signatures": [{"sig": "MEUCIQDwFF1Vw2Yqpj+A/cPHCFIYGCArjD7YQ6B4hLdWHdyUxwIgd851beoqMm41jkTBCOGwhdHzLTAL83818OMgVcjMNh8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.2.0": {"name": "braces", "version": "1.2.0", "dependencies": {"preserve": "^0.2.0", "arr-filter": "^1.0.2", "expand-range": "^1.2.0"}, "devDependencies": {"chalk": "^0.5.1", "mocha": "*", "write": "^0.1.1", "should": "*", "minimist": "^1.1.0", "minimatch": "^2.0.1", "benchmarked": "^0.1.3", "brace-expansion": "^1.1.0"}, "dist": {"shasum": "b86f8f3898d11dc1008d29176a45b4863190e7b9", "tarball": "https://registry.npmjs.org/braces/-/braces-1.2.0.tgz", "integrity": "sha512-LrWXOZAaORkFo9btgFznnPWxqe0xfUUIse+7OLWzt84ddPX0747PZS+BV86W1F3VyIeOhckQ4CRBhp6DnPhgYw==", "signatures": [{"sig": "MEUCIFfkmBRM20jzAJxcfeIGGBQ7QJQSuDljwofQxSFYrxowAiEAz8Z3AdNhmnzaFspZXctXsDMM5WAxEXqomDoorCWvcaI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.3.0": {"name": "braces", "version": "1.3.0", "dependencies": {"preserve": "^0.2.0", "arr-filter": "^1.0.2", "expand-range": "^1.4.0"}, "devDependencies": {"chalk": "^0.5.1", "mocha": "*", "write": "^0.1.1", "should": "*", "minimist": "^1.1.0", "minimatch": "^2.0.1", "benchmarked": "^0.1.3", "brace-expansion": "^1.1.0"}, "dist": {"shasum": "e0495cf4796c137fb0d80a922ffef431fbde6bef", "tarball": "https://registry.npmjs.org/braces/-/braces-1.3.0.tgz", "integrity": "sha512-5T2hlMwxt1IhrjcL6wdbCi/wOTwtdEFc58D/G0ER1MkO8syeMF0TcCf0KTSUpuEdrbN3FOCf+SN/xl8vWRHBAA==", "signatures": [{"sig": "MEYCIQDN/7B8Og/Wy28kURLe2hLBCnnOnmSukcH27p7q2DJkPwIhAIRKMM4e+qvYvR+qghOhJYWkIjWj8o+fTVtcfPeKDqUC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.4.0": {"name": "braces", "version": "1.4.0", "dependencies": {"preserve": "^0.2.0", "arr-filter": "^1.0.2", "expand-range": "^1.5.0"}, "devDependencies": {"chalk": "^0.5.1", "mocha": "*", "write": "^0.1.1", "should": "*", "minimist": "^1.1.0", "minimatch": "^2.0.1", "benchmarked": "^0.1.3", "brace-expansion": "^1.1.0"}, "dist": {"shasum": "c95b1b8fd045b765703c2c0cd95e0369e5696492", "tarball": "https://registry.npmjs.org/braces/-/braces-1.4.0.tgz", "integrity": "sha512-A7mrTs9OAQrOpAHne8Ukiit7Aue9TNkcg0QO/hw/U8T43VUWPvIbhCCbgpoCgCEza8Y50YpKpC89+0SlWa9jWQ==", "signatures": [{"sig": "MEQCIEzrJDaiFot3RuXZxYT5quEj3HsGh34zG6msGJcqORFhAiBouLfTe9foAWFl0dfYujva7ubt6sfpihmjKqZNdHUvNw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.5.0": {"name": "braces", "version": "1.5.0", "dependencies": {"preserve": "^0.2.0", "arr-filter": "^1.0.2", "expand-range": "^1.5.0", "repeat-element": "^1.1.0"}, "devDependencies": {"chalk": "^0.5.1", "mocha": "*", "write": "^0.1.1", "should": "*", "minimist": "^1.1.0", "minimatch": "^2.0.1", "benchmarked": "^0.1.3", "brace-expansion": "^1.1.0"}, "dist": {"shasum": "3600d82f04390dfc7c6016f091657b2d86189a15", "tarball": "https://registry.npmjs.org/braces/-/braces-1.5.0.tgz", "integrity": "sha512-lxYGtXXPqFES/5rDs1l01Fy6GM3hlZm65uxHgbcHvkcz0TenXPm7rbye2JaKl/RSJ6/cJwW+tC1ax34rKrk3IQ==", "signatures": [{"sig": "MEYCIQCsG1NX/QLtOljA09R70Mar4YY8P/Fw0DRlToIDsDF01QIhAP7C2kx+EL6qNY1F2E0te1BgJ9mq4Fy4pv4QQKyyYAR4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.5.1": {"name": "braces", "version": "1.5.1", "dependencies": {"preserve": "^0.2.0", "arr-filter": "^1.0.2", "expand-range": "^1.5.0", "repeat-element": "^1.1.0"}, "devDependencies": {"chalk": "^0.5.1", "mocha": "*", "write": "^0.1.1", "should": "*", "minimist": "^1.1.0", "minimatch": "^2.0.1", "benchmarked": "^0.1.3", "brace-expansion": "^1.1.0"}, "dist": {"shasum": "faefcd7e5251c84c21127e8f079b14775b6c06ce", "tarball": "https://registry.npmjs.org/braces/-/braces-1.5.1.tgz", "integrity": "sha512-PhxDD2jLYwNFNO0hYkYGTMIh+n+ioNXi6cM+TYyWaqruoGOpjWTLGj5XFnIHciR5snxwtQtLzfOU85gEqR0QPA==", "signatures": [{"sig": "MEQCIFW3wVrwoHuYTlPJptKRAC1TFsjBsdKJpQh7w2KPgSC6AiBuJh+IuAE0FAo9CsCP8ceL7TBqQllQCSarmURQZ3qNkw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.6.0": {"name": "braces", "version": "1.6.0", "dependencies": {"preserve": "^0.2.0", "arr-filter": "^1.0.2", "expand-range": "^1.8.0", "repeat-element": "^1.1.0"}, "devDependencies": {"chalk": "^0.5.1", "mocha": "*", "write": "^0.1.1", "should": "*", "minimist": "^1.1.0", "minimatch": "^2.0.1", "benchmarked": "^0.1.3", "brace-expansion": "^1.1.0"}, "dist": {"shasum": "438c23185022e8fa968af9feeaa056723a3efe55", "tarball": "https://registry.npmjs.org/braces/-/braces-1.6.0.tgz", "integrity": "sha512-bq8Ul/s4vyD8GX2jSqUT/Db+BIPktohKewWzaczlB9+BVMVx0Y423mbsPlbRussx5F5n0OH6n0PJODyqWugCBg==", "signatures": [{"sig": "MEUCIQDP3e1ne1dpP/BKoALWvQemr1ChV7BXzXEe/F1UeJdYlwIgIPcZ0OzKtsifQXsxYRjvHGTozSVFl5o3d6M9riTEUIY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.7.0": {"name": "braces", "version": "1.7.0", "dependencies": {"arr-map": "^1.0.0", "preserve": "^0.2.0", "arr-filter": "^1.0.2", "expand-range": "^1.8.0", "repeat-element": "^1.1.0"}, "devDependencies": {"chalk": "^0.5.1", "mocha": "*", "should": "*", "minimist": "^1.1.0", "minimatch": "^2.0.1", "benchmarked": "^0.1.3", "brace-expansion": "^1.1.0"}, "dist": {"shasum": "ed954c25e8a170971202316c1899fa98fdae6d6d", "tarball": "https://registry.npmjs.org/braces/-/braces-1.7.0.tgz", "integrity": "sha512-vUUn0ZC0UApO70cQQfi6Pd7HNU6drH7opymAFr3gYtZ+Go45nimpawAdnDnKj3wrPDFsmqqHc7JUE7PZHznmvA==", "signatures": [{"sig": "MEYCIQCvOmFd0P4aAwckgwbe5LQxlio5OQaTO4xDHkcQtnhsHAIhAN+Z+xknTEdwP/amfK1Lc8ArDjjGI4jrRI4NnhU0R2F8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.8.0": {"name": "braces", "version": "1.8.0", "dependencies": {"preserve": "^0.2.0", "expand-range": "^1.8.1", "repeat-element": "^1.1.0"}, "devDependencies": {"chalk": "^0.5.1", "mocha": "*", "should": "*", "minimist": "^1.1.0", "minimatch": "^2.0.1", "benchmarked": "^0.1.3", "brace-expansion": "^1.1.0"}, "dist": {"shasum": "3a4a005aae0391817c17b074dca8f08e6fc9e4c4", "tarball": "https://registry.npmjs.org/braces/-/braces-1.8.0.tgz", "integrity": "sha512-zmetXdAbxy7iu++21YqfIvdufiV+ndJzUck78ObHHMMiM/HRNLWGiI052nUT5A623rRudpld0hfkOBiwoIiCIw==", "signatures": [{"sig": "MEUCIQDXD59y/lpCgSIGsGDtSM5xoDm0fx71LLvRdfhZoe5mQgIgNQnPJ1UcgREamXcgQY+eCelSIA9XDdiuqUyTmEbCBvA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.8.1": {"name": "braces", "version": "1.8.1", "dependencies": {"preserve": "^0.2.0", "lazy-cache": "^0.2.3", "expand-range": "^1.8.1", "repeat-element": "^1.1.2"}, "devDependencies": {"chalk": "^0.5.1", "mocha": "*", "should": "*", "minimist": "^1.1.0", "minimatch": "^2.0.1", "benchmarked": "^0.1.3", "brace-expansion": "^1.1.0"}, "dist": {"shasum": "2d195b85a0a997ec21be78a7f1bc970480b12a1a", "tarball": "https://registry.npmjs.org/braces/-/braces-1.8.1.tgz", "integrity": "sha512-92peuXcKVAXcbTGlsftd1PHiVAi363Ijh/IkNA6+7tWCupPvZDsNn9p5/7ecs7SUz1SrJrAPf0WM2vU8Vl8qqw==", "signatures": [{"sig": "MEUCIQCw5WQkzyCgll3e+W4R/nh78vzg539fX8CK2iW8Di4X2wIgIVd55OQ6w4vdS7V5/RMulNvGZaG228oonKMPh2Bi/rE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.8.2": {"name": "braces", "version": "1.8.2", "dependencies": {"preserve": "^0.2.0", "lazy-cache": "^0.2.3", "expand-range": "^1.8.1", "repeat-element": "^1.1.2"}, "devDependencies": {"chalk": "^0.5.1", "mocha": "*", "should": "*", "minimist": "^1.1.0", "minimatch": "^2.0.1", "benchmarked": "^0.1.3", "brace-expansion": "^1.1.0"}, "dist": {"shasum": "036e024051d4bbc7096428b4d6f20ea1f137a794", "tarball": "https://registry.npmjs.org/braces/-/braces-1.8.2.tgz", "integrity": "sha512-SY1uQ2Z0f8nLzUp6jJIvNVMLmzmzpVb6C13ipLEJlPL4XbFc7fEhVYJxMNpqt05SKg46oX3h1clGdqEbfBtwyA==", "signatures": [{"sig": "MEYCIQC/5jL4D38DykU/Z1jdJjEH3L2LcWpB1ku55nt1+4VfZgIhAOVXa+j8zMmIBNXiyChoYtdJ88PgZZpYWRFnzwfveVQR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.8.3": {"name": "braces", "version": "1.8.3", "dependencies": {"preserve": "^0.2.0", "expand-range": "^1.8.1", "repeat-element": "^1.1.2"}, "devDependencies": {"chalk": "^0.5.1", "mocha": "*", "should": "*", "minimist": "^1.1.0", "minimatch": "^2.0.1", "benchmarked": "^0.1.3", "brace-expansion": "^1.1.0"}, "dist": {"shasum": "35d4e7dda632b33e215d38a8a9cf4329c9c75d2c", "tarball": "https://registry.npmjs.org/braces/-/braces-1.8.3.tgz", "integrity": "sha512-a1Y3ZnTdzkI10NdmKkDpgJGq4pXLTy/QCvsyHFEhMM+8fMnx2+SB8r+7HoKYMmqlSv8tgbs3nKypNEEZSTC4uA==", "signatures": [{"sig": "MEUCIBkqnveOyF2nqvCby4J8lhsJdOLAlgdtbOFWfVLhV9DpAiEA2XOrMIrwocYjaHeB3/XjNKrdKaA1H50ZVcnMubh3eks=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.8.4": {"name": "braces", "version": "1.8.4", "dependencies": {"preserve": "^0.2.0", "expand-range": "^1.8.1", "repeat-element": "^1.1.2"}, "devDependencies": {"chalk": "^1.1.3", "mocha": "^2.4.5", "should": "^8.3.1", "minimist": "^1.2.0", "minimatch": "^3.0.0", "benchmarked": "^0.1.5", "gulp-format-md": "^0.1.8", "brace-expansion": "^1.1.3"}, "dist": {"shasum": "75e2d6456d48b06dbb5205ed63442a3bfc5eefce", "tarball": "https://registry.npmjs.org/braces/-/braces-1.8.4.tgz", "integrity": "sha512-Ed5g6san2X6MCCzrwAPTAfe9Ak9zkgXQ9jUhgSRiUsb5+/ceptlRtHNbHOlc2e2SVkrGW1Jayf6AMR8YFyMoQg==", "signatures": [{"sig": "MEUCIQDusQlg241xbLlkaSpcXF+SxZ1+DU8p6zyaFcQa4RnhrQIgf7kVVR4gRv810KJNL090zGwLSPG9eoZ4ksO7Cpq4ITE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.8.5": {"name": "braces", "version": "1.8.5", "dependencies": {"preserve": "^0.2.0", "expand-range": "^1.8.1", "repeat-element": "^1.1.2"}, "devDependencies": {"chalk": "^1.1.3", "mocha": "^2.4.5", "should": "^8.3.1", "minimist": "^1.2.0", "minimatch": "^3.0.0", "benchmarked": "^0.1.5", "gulp-format-md": "^0.1.8", "brace-expansion": "^1.1.3"}, "dist": {"shasum": "ba77962e12dff969d6b76711e914b737857bf6a7", "tarball": "https://registry.npmjs.org/braces/-/braces-1.8.5.tgz", "integrity": "sha512-xU7bpz2ytJl1bH9cgIurjpg/n8Gohy9GTw81heDYLJQ4RU60dlyJsa+atVF2pI0yMMvKxI9HkKwjePCj5XI1hw==", "signatures": [{"sig": "MEUCICyEz9Yu77563hTY24b9FT8m3wzp6vo4XAXfMvaMnUq1AiEAxO+ub+3gGJDKnFmASOt4CsPYLHU6RpwJK4YeGKH6RiQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.0.0": {"name": "braces", "version": "2.0.0", "dependencies": {"debug": "^2.2.0", "isobject": "^2.1.0", "to-regex": "^3.0.1", "regex-not": "^1.0.0", "fill-range": "^3.0.3", "snapdragon": "^0.8.1", "arr-flatten": "^1.0.1", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "repeat-element": "^1.1.2", "define-property": "^0.2.5"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.1.2", "ansi-cyan": "^0.1.1", "minimatch": "^3.0.3", "time-diff": "^0.3.1", "gulp-mocha": "^3.0.1", "is-windows": "^0.2.0", "text-table": "^0.2.0", "benchmarked": "^0.2.5", "cross-spawn": "^4.0.2", "gulp-eslint": "^3.0.1", "gulp-unused": "^0.2.0", "pretty-bytes": "^4.0.2", "yargs-parser": "^4.0.2", "gulp-istanbul": "^1.1.1", "noncharacters": "^1.1.0", "fs-exists-sync": "^0.1.0", "gulp-format-md": "^0.1.11", "brace-expansion": "^1.1.6"}, "dist": {"shasum": "3d809da0a3e2024761d48651955fd9452e25ec85", "tarball": "https://registry.npmjs.org/braces/-/braces-2.0.0.tgz", "integrity": "sha512-oUsVNHRH2pbD2nhJuU6oHgMTpwH/V4ABbzRshMPcIc9GZeYi57sfTrPGGXyLfBreFlGlbvv7aFrQd+GYLwo8yw==", "signatures": [{"sig": "MEUCIQDxUfIisBerizKkTa5SADKwhyszEM9xpZXE6c0bUhF1owIgcp6xX/ckp8i4gi36xqZWUi7r6nK1bWN7gghavF8BDxQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.0.1": {"name": "braces", "version": "2.0.1", "dependencies": {"debug": "^2.2.0", "isobject": "^2.1.0", "to-regex": "^3.0.1", "regex-not": "^1.0.0", "fill-range": "^3.0.3", "snapdragon": "^0.8.1", "arr-flatten": "^1.0.1", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "repeat-element": "^1.1.2", "define-property": "^0.2.5"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.1.2", "ansi-cyan": "^0.1.1", "minimatch": "^3.0.3", "time-diff": "^0.3.1", "gulp-mocha": "^3.0.1", "is-windows": "^0.2.0", "text-table": "^0.2.0", "benchmarked": "^0.2.5", "cross-spawn": "^4.0.2", "gulp-eslint": "^3.0.1", "gulp-unused": "^0.2.0", "pretty-bytes": "^4.0.2", "yargs-parser": "^4.0.2", "gulp-istanbul": "^1.1.1", "noncharacters": "^1.1.0", "fs-exists-sync": "^0.1.0", "gulp-format-md": "^0.1.11", "brace-expansion": "^1.1.6"}, "dist": {"shasum": "aef8fb862f7f429d9840a931cbbbbf8355107905", "tarball": "https://registry.npmjs.org/braces/-/braces-2.0.1.tgz", "integrity": "sha512-qRHNcprYklsNT+kuUQLUxluiz3eFb7utwCuTBhrlfA4tYFG4cyUSw0vgTs8Z6yFgpssXiQiDHsoeU0wDaW+5Jw==", "signatures": [{"sig": "MEUCID4Q+PwlbsCMVk+Tuir5q6OdH/CVmuLIpopwGcsX++P8AiEA6lbfaZamu62BIk7ZovfkmgG3MZgW0OnxWSBHiZX6mD0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.0.2": {"name": "braces", "version": "2.0.2", "dependencies": {"debug": "^2.2.0", "isobject": "^2.1.0", "to-regex": "^3.0.1", "regex-not": "^1.0.0", "fill-range": "^3.0.3", "snapdragon": "^0.8.1", "arr-flatten": "^1.0.1", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "repeat-element": "^1.1.2", "define-property": "^0.2.5"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.1.2", "ansi-cyan": "^0.1.1", "minimatch": "^3.0.3", "time-diff": "^0.3.1", "gulp-mocha": "^3.0.1", "is-windows": "^0.2.0", "text-table": "^0.2.0", "benchmarked": "^0.2.5", "cross-spawn": "^4.0.2", "gulp-eslint": "^3.0.1", "gulp-unused": "^0.2.0", "pretty-bytes": "^4.0.2", "yargs-parser": "^4.0.2", "gulp-istanbul": "^1.1.1", "noncharacters": "^1.1.0", "fs-exists-sync": "^0.1.0", "gulp-format-md": "^0.1.11", "brace-expansion": "^1.1.6"}, "dist": {"shasum": "d4c9b44820e63a7b79437a4ef094b8ab53a4ae44", "tarball": "https://registry.npmjs.org/braces/-/braces-2.0.2.tgz", "integrity": "sha512-WhhjRmZ3ksdEwciX8KbK3E0FjExG4aSetO70d3X4hM/g0elE8XYJNLBq1ov82HTPXEGg0/0DGaSqpvqwzwXyGw==", "signatures": [{"sig": "MEUCIHsG4SCjwwH/U3d1vDFts54s+M0cbp97J5yX1TZcPwxyAiEAvHEFatnqFrFkKqfgxAw/VGZnfnCE6Mf5oLZOEYvmDW8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.0.3": {"name": "braces", "version": "2.0.3", "dependencies": {"isobject": "^3.0.0", "to-regex": "^3.0.1", "regex-not": "^1.0.0", "fill-range": "^3.0.3", "snapdragon": "^0.8.1", "arr-flatten": "^1.0.1", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "repeat-element": "^1.1.2", "define-property": "^0.2.5"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.2.0", "ansi-cyan": "^0.1.1", "minimatch": "^3.0.3", "time-diff": "^0.3.1", "gulp-mocha": "^3.0.1", "is-windows": "^1.0.0", "text-table": "^0.2.0", "benchmarked": "^0.2.5", "cross-spawn": "^5.0.1", "gulp-eslint": "^3.0.1", "gulp-unused": "^0.2.0", "pretty-bytes": "^4.0.2", "yargs-parser": "^4.2.0", "gulp-istanbul": "^1.1.1", "noncharacters": "^1.1.0", "fs-exists-sync": "^0.1.0", "gulp-format-md": "^0.1.11", "brace-expansion": "^1.1.6"}, "dist": {"shasum": "7ac586e593bdc43a262daf45673661464844198f", "tarball": "https://registry.npmjs.org/braces/-/braces-2.0.3.tgz", "integrity": "sha512-vp/DHMo3GvQgTvOp5Nvm+Rcd5DAWCYT+vDL9A3ZTm6flVOw0WH8IjcyBleC9H5BrNu2JhEQmyPUYgFHExUUY/w==", "signatures": [{"sig": "MEUCIBrHNTok99w4c6d2y9xPUhsRta0Z8O4D38Ct5JW3dh/5AiEA+zHGbtHmbJ3N+JdiIHtLPkGn8sUbjQ7x2/GvLTbgPus=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.0.4": {"name": "braces", "version": "2.0.4", "dependencies": {"isobject": "^3.0.0", "to-regex": "^3.0.1", "regex-not": "^1.0.0", "fill-range": "^3.1.1", "snapdragon": "^0.8.1", "arr-flatten": "^1.0.1", "array-unique": "^0.3.2", "split-string": "^2.0.0", "extend-shallow": "^2.0.1", "repeat-element": "^1.1.2", "define-property": "^0.2.5"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.2.0", "ansi-cyan": "^0.1.1", "minimatch": "^3.0.3", "time-diff": "^0.3.1", "gulp-mocha": "^3.0.0", "is-windows": "^1.0.0", "text-table": "^0.2.0", "benchmarked": "^0.2.5", "cross-spawn": "^5.1.0", "gulp-eslint": "^3.0.1", "gulp-unused": "^0.2.1", "pretty-bytes": "^4.0.2", "yargs-parser": "^5.0.0", "gulp-istanbul": "^1.1.1", "noncharacters": "^1.1.0", "fs-exists-sync": "^0.1.0", "gulp-format-md": "^0.1.12", "brace-expansion": "^1.1.7"}, "dist": {"shasum": "6381bfe9154fbc2b0b1b420aab574b1ac87534ee", "tarball": "https://registry.npmjs.org/braces/-/braces-2.0.4.tgz", "integrity": "sha512-+fxIrbtFLL6T/b1N86npW5J3WFiGNo6mKBel0tLT6zLDRK5uggXgaPW3T3Bua4JCUTmktzENYjY6P82Dcv40kw==", "signatures": [{"sig": "MEQCIALpwIdl1nf9/9Jsl28FFlQWsZOEXXY9CP2wsrj2/QHWAiA44HRDhvZm3tc69EN/y8GW11M2HkDf5GjQhsC5ETDfVA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.1.0": {"name": "braces", "version": "2.1.0", "dependencies": {"isobject": "^3.0.0", "to-regex": "^3.0.1", "fill-range": "^4.0.0", "snapdragon": "^0.8.1", "arr-flatten": "^1.0.3", "array-unique": "^0.3.2", "split-string": "^2.0.0", "extend-shallow": "^2.0.1", "repeat-element": "^1.1.2", "define-property": "^1.0.0"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.2.0", "ansi-cyan": "^0.1.1", "minimatch": "^3.0.3", "time-diff": "^0.3.1", "gulp-mocha": "^3.0.0", "is-windows": "^1.0.0", "text-table": "^0.2.0", "benchmarked": "^1.0.0", "cross-spawn": "^5.1.0", "gulp-eslint": "^3.0.1", "gulp-unused": "^0.2.1", "pretty-bytes": "^4.0.2", "yargs-parser": "^5.0.0", "gulp-istanbul": "^1.1.1", "noncharacters": "^1.1.0", "fs-exists-sync": "^0.1.0", "gulp-format-md": "^0.1.12", "brace-expansion": "^1.1.7"}, "dist": {"shasum": "27dc86dfb1add13b2974fc4276e01941a246e14a", "tarball": "https://registry.npmjs.org/braces/-/braces-2.1.0.tgz", "integrity": "sha512-qrKq9EBrp6asnvy70BJGJzjtIoirGrpteVrEQwWkzxFFbVy6z7vgijSXsI2z3mcjsCnjrJE4huxvlIcyQWBlVw==", "signatures": [{"sig": "MEUCIBPA5mxXXONzyGbNeV2zSWtHNJTlyCF9DskHDlcaOfXqAiEA5KgFuWAc6M5i4TZbDWyceenEy8QVJQzFIPgPVP4l0OI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.1.1": {"name": "braces", "version": "2.1.1", "dependencies": {"isobject": "^3.0.0", "to-regex": "^3.0.1", "fill-range": "^4.0.0", "snapdragon": "^0.8.1", "arr-flatten": "^1.0.3", "array-unique": "^0.3.2", "split-string": "^2.1.0", "extend-shallow": "^2.0.1", "repeat-element": "^1.1.2", "define-property": "^1.0.0"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.2.0", "ansi-cyan": "^0.1.1", "minimatch": "^3.0.3", "time-diff": "^0.3.1", "gulp-mocha": "^3.0.0", "is-windows": "^1.0.0", "text-table": "^0.2.0", "benchmarked": "^1.0.0", "cross-spawn": "^5.1.0", "gulp-eslint": "^3.0.1", "gulp-unused": "^0.2.1", "pretty-bytes": "^4.0.2", "yargs-parser": "^5.0.0", "gulp-istanbul": "^1.1.1", "noncharacters": "^1.1.0", "fs-exists-sync": "^0.1.0", "gulp-format-md": "^0.1.12", "brace-expansion": "^1.1.7"}, "dist": {"shasum": "b33581be8553a651fdc79012760a7e767f82b834", "tarball": "https://registry.npmjs.org/braces/-/braces-2.1.1.tgz", "integrity": "sha512-hUsQnnSix9sYcu2hgn/CwF7LR5YP+ICow571lAORgz3R++N0zA51MV5z41skZdE1XsEU9+4eHUro/K8/SVLh2A==", "signatures": [{"sig": "MEUCIQD9/Owdr7T9KbKxBHv0fnvkY+G9qIa+WyQYL1mDyHzgLgIgIyCZreoctCMLnv87jl2B8IhfoV38OmhudkRVTvYU//U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.2.0": {"name": "braces", "version": "2.2.0", "dependencies": {"isobject": "^3.0.0", "to-regex": "^3.0.1", "fill-range": "^4.0.0", "snapdragon": "^0.8.1", "arr-flatten": "^1.0.3", "array-unique": "^0.3.2", "split-string": "^2.1.0", "extend-shallow": "^2.0.1", "repeat-element": "^1.1.2", "define-property": "^1.0.0", "snapdragon-node": "^2.0.1"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.2.0", "ansi-cyan": "^0.1.1", "minimatch": "^3.0.3", "time-diff": "^0.3.1", "gulp-mocha": "^3.0.0", "is-windows": "^1.0.0", "text-table": "^0.2.0", "benchmarked": "^1.0.0", "cross-spawn": "^5.1.0", "gulp-eslint": "^3.0.1", "gulp-unused": "^0.2.1", "pretty-bytes": "^4.0.2", "yargs-parser": "^5.0.0", "gulp-istanbul": "^1.1.1", "noncharacters": "^1.1.0", "gulp-format-md": "^0.1.12", "brace-expansion": "^1.1.7"}, "dist": {"shasum": "9edd1d6ee0acd760f1d1e5c13bc66d428ad0a3c0", "tarball": "https://registry.npmjs.org/braces/-/braces-2.2.0.tgz", "integrity": "sha512-RZWGwDQ3cdGQTqajHnk4KXjmTdpdwzlotZ1nZa9GCoyG/fkzyjuMvDXGlkgzNDSvcxGfApZmIwVtpXUzht6vLA==", "signatures": [{"sig": "MEYCIQDL/PnMphK2sHIdNRkP3ikXsL9fRLYAB7nC8Vgl8UtrkAIhALh7CYG1iEUQtDMItshXr1LX0xQdLf3VWONaHVc+exd0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.2.1": {"name": "braces", "version": "2.2.1", "dependencies": {"isobject": "^3.0.0", "to-regex": "^3.0.1", "fill-range": "^4.0.0", "snapdragon": "^0.8.1", "arr-flatten": "^1.0.3", "array-unique": "^0.3.2", "split-string": "^2.1.0", "extend-shallow": "^2.0.1", "repeat-element": "^1.1.2", "define-property": "^1.0.0", "snapdragon-node": "^2.0.1"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.2.0", "ansi-cyan": "^0.1.1", "minimatch": "^3.0.3", "time-diff": "^0.3.1", "gulp-mocha": "^3.0.0", "is-windows": "^1.0.0", "text-table": "^0.2.0", "benchmarked": "^1.0.0", "cross-spawn": "^5.1.0", "gulp-eslint": "^3.0.1", "gulp-unused": "^0.2.1", "pretty-bytes": "^4.0.2", "yargs-parser": "^5.0.0", "gulp-istanbul": "^1.1.1", "noncharacters": "^1.1.0", "gulp-format-md": "^0.1.12", "brace-expansion": "^1.1.7"}, "dist": {"shasum": "cccce9e092a93faa0bba2c1174cd262d2369794d", "tarball": "https://registry.npmjs.org/braces/-/braces-2.2.1.tgz", "integrity": "sha512-saw1qqYe6p9B1zaZJco2fuIk1xl+NvrvXg29DYjUEv4S1FvSFZFzjHMtfDFbKkXHKKZsQDLBgASKRaz5mnV2nw==", "signatures": [{"sig": "MEQCIA+pYRljfNCuSindKQX68ZhW1zLoYJ9EVWmpp/dd7AZ8AiBBeY5kU/JjY3uoOMvAf516KfL0va8D71A4m1fHl8fRVw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.2.2": {"name": "braces", "version": "2.2.2", "dependencies": {"isobject": "^3.0.0", "to-regex": "^3.0.1", "fill-range": "^4.0.0", "snapdragon": "^0.8.1", "arr-flatten": "^1.0.3", "array-unique": "^0.3.2", "split-string": "^2.1.0", "extend-shallow": "^2.0.1", "repeat-element": "^1.1.2", "define-property": "^1.0.0", "snapdragon-node": "^2.0.1"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.2.0", "ansi-cyan": "^0.1.1", "minimatch": "^3.0.3", "time-diff": "^0.3.1", "gulp-mocha": "^3.0.0", "is-windows": "^1.0.0", "text-table": "^0.2.0", "benchmarked": "^1.0.0", "cross-spawn": "^5.1.0", "gulp-eslint": "^3.0.1", "gulp-unused": "^0.2.1", "pretty-bytes": "^4.0.2", "yargs-parser": "^5.0.0", "gulp-istanbul": "^1.1.1", "noncharacters": "^1.1.0", "gulp-format-md": "^0.1.12", "brace-expansion": "^1.1.7"}, "dist": {"shasum": "241f868c2b2690d9febeee5a7c83fbbf25d00b1b", "tarball": "https://registry.npmjs.org/braces/-/braces-2.2.2.tgz", "integrity": "sha512-LsV+Z9xUYWR9XX0fnDceeu4+X+RCZ18P8YHJlgSUEzRjsOFfMl1gjzIg7vfbW6cJg8mfJIuoWpVbgAkimkAWAA==", "signatures": [{"sig": "MEUCICdbpOu8hITnf0KOknAb8ZxnfWySKMPgm8zHfmwcFaTnAiEA2kBUKns5XT+Cmf1umeVBAhgxYDPBnEyqT4IiIOX88ik=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.3.0": {"name": "braces", "version": "2.3.0", "dependencies": {"isobject": "^3.0.1", "to-regex": "^3.0.1", "fill-range": "^4.0.0", "snapdragon": "^0.8.1", "arr-flatten": "^1.1.0", "array-unique": "^0.3.2", "split-string": "^3.0.2", "extend-shallow": "^2.0.1", "repeat-element": "^1.1.2", "define-property": "^1.0.0", "snapdragon-node": "^2.0.1"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.2.0", "ansi-cyan": "^0.1.1", "minimatch": "^3.0.4", "time-diff": "^0.3.1", "gulp-mocha": "^3.0.1", "is-windows": "^1.0.1", "text-table": "^0.2.0", "benchmarked": "^2.0.0", "cross-spawn": "^5.1.0", "gulp-eslint": "^4.0.0", "gulp-unused": "^0.2.1", "pretty-bytes": "^4.0.2", "yargs-parser": "^8.0.0", "gulp-istanbul": "^1.1.2", "noncharacters": "^1.1.0", "gulp-format-md": "^1.0.0", "brace-expansion": "^1.1.8"}, "dist": {"shasum": "a46941cb5fb492156b3d6a656e06c35364e3e66e", "tarball": "https://registry.npmjs.org/braces/-/braces-2.3.0.tgz", "integrity": "sha512-P4O8UQRdGiMLWSizsApmXVQDBS6KCt7dSexgLKBmH5Hr1CZq7vsnscFh8oR1sP1ab1Zj0uCHCEzZeV6SfUf3rA==", "signatures": [{"sig": "MEYCIQCElhIYU0FtxiFWvH1X8pU0dIJgLTkBzB5lxzEIrqL26AIhAKq5gmsttltOw8L9sHpxi3uymMPwtrx+iP6EOoO45LBm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.3.1": {"name": "braces", "version": "2.3.1", "dependencies": {"kind-of": "^6.0.2", "isobject": "^3.0.1", "to-regex": "^3.0.1", "fill-range": "^4.0.0", "snapdragon": "^0.8.1", "arr-flatten": "^1.1.0", "array-unique": "^0.3.2", "split-string": "^3.0.2", "extend-shallow": "^2.0.1", "repeat-element": "^1.1.2", "define-property": "^1.0.0", "snapdragon-node": "^2.0.1"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.2.0", "ansi-cyan": "^0.1.1", "minimatch": "^3.0.4", "time-diff": "^0.3.1", "gulp-mocha": "^3.0.1", "is-windows": "^1.0.1", "text-table": "^0.2.0", "benchmarked": "^2.0.0", "cross-spawn": "^5.1.0", "gulp-eslint": "^4.0.0", "gulp-unused": "^0.2.1", "yargs-parser": "^8.0.0", "gulp-istanbul": "^1.1.2", "noncharacters": "^1.1.0", "gulp-format-md": "^1.0.0", "brace-expansion": "^1.1.8"}, "dist": {"shasum": "7086c913b4e5a08dbe37ac0ee6a2500c4ba691bb", "tarball": "https://registry.npmjs.org/braces/-/braces-2.3.1.tgz", "fileCount": 12, "integrity": "sha512-SO5lYHA3vO6gz66erVvedSCkp7AKWdv6VcQ2N4ysXfPxdAlxAMMAdwegGGcv1Bqwm7naF1hNdk5d6AAIEHV2nQ==", "signatures": [{"sig": "MEYCIQC3TrmkxatUqP/YQ8Xdctd63DNG0KvHv/WU5tjqlXHJEwIhAJ/oVFHtrRi0CF/wAyHNCw8J0A9gtQnHWAv57REQLlJu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 94768}, "engines": {"node": ">=0.10.0"}}, "2.3.2": {"name": "braces", "version": "2.3.2", "dependencies": {"isobject": "^3.0.1", "to-regex": "^3.0.1", "fill-range": "^4.0.0", "snapdragon": "^0.8.1", "arr-flatten": "^1.1.0", "array-unique": "^0.3.2", "split-string": "^3.0.2", "extend-shallow": "^2.0.1", "repeat-element": "^1.1.2", "snapdragon-node": "^2.0.1"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.2.0", "ansi-cyan": "^0.1.1", "minimatch": "^3.0.4", "time-diff": "^0.3.1", "gulp-mocha": "^3.0.1", "is-windows": "^1.0.1", "text-table": "^0.2.0", "benchmarked": "^2.0.0", "cross-spawn": "^5.1.0", "gulp-eslint": "^4.0.0", "gulp-unused": "^0.2.1", "yargs-parser": "^8.0.0", "gulp-istanbul": "^1.1.2", "noncharacters": "^1.1.0", "gulp-format-md": "^1.0.0", "brace-expansion": "^1.1.8"}, "dist": {"shasum": "5979fd3f14cd531565e5fa2df1abfff1dfaee729", "tarball": "https://registry.npmjs.org/braces/-/braces-2.3.2.tgz", "fileCount": 8, "integrity": "sha512-aNdbnj9P8PjdXU4ybaWLK2IF3jc/EoDYbC7AazW6to3TRsfXxscC9UXOB5iDiEQrkyIbWp2SLQda4+QAa7nc3w==", "signatures": [{"sig": "MEUCIQCYZtepWok2C8goON8SfWxHt1DfjzDxpj/PbdbbMbUYBAIgJ+xU01i1GI7aH1A3DoH1eDubI2rkP43XWZa9fLtVmNk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59699}, "engines": {"node": ">=0.10.0"}}, "3.0.0": {"name": "braces", "version": "3.0.0", "dependencies": {"fill-range": "^7.0.1"}, "devDependencies": {"mocha": "^6.1.1", "bash-path": "^2.0.1", "ansi-colors": "^3.2.4", "gulp-format-md": "^2.0.0"}, "dist": {"shasum": "1d9a1a84daea4606d3a6a03d1fd97cc6bd37d543", "tarball": "https://registry.npmjs.org/braces/-/braces-3.0.0.tgz", "fileCount": 12, "integrity": "sha512-JrED+3ZoiTW3KmWkrajE5zm5Pl69XD1DjItKTX9KS+dsfrge66nho5fAOC+tBwMH5p5bPv97EYm8loTb7mxPKg==", "signatures": [{"sig": "MEQCIHl0v+gsmlp96abkAFFO7qd+hGWtiD+zTmrqGNDGhOFKAiBDTo73OfMndcELEAXDM1WPVLNGT8M3u0tXgAR9peqiHg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55270, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcq89rCRA9TVsSAnZWagAAY1oP/Ak0JTSaZUQv/2xXrF1Z\nD4zJ0bXdRNtkOIRwO1wqrl5Jn6LKc0lQmhfAanVxFE7LhuCFvS0f3bi/yMBY\nTkKlnMy376/Z5VIpxqwtOprvx0UG2k1/1LTgfLWt1sLlC1exAxK1HDnUUHhi\nGCiiPlAKvhB+ygp5334F2Mcwp9keW2yIMjH+7vZTxVToBwJYv3lHvcc3U8c0\nqifRFQTLK3vyoKTXCkP0EhmzbPBtcs5PFsi6b5qv5N8/3uHphqMg8YFhfpcr\nFtwh+NWLFdLEYIpFa2dHbIzISiRFFIkFIqTmPg7P89IPHXQAQL6xGi50VedN\nOvWxVQZyV1yGE/xVJgfR66FSZUGvL61ScW5zJ2noBqjsKwjv0XJoQL+aWDH0\ny0HgWdTQ0uylZjXqoL9h7Y8+jMfUO4DyukdqiqmPJmqziA4wJcyK8XKjfqZ/\nH88ej3z9TitURBb5ZJpGGEuMKkGSY2My03fq5aac//428dZ0BsXKBe2G5j/b\nzIZ6gNUVLWC6EpztEEC4S90Q3H/uoDan4i/rpw9rM9buPw4tUyqL2tU0YuBo\nu1/aDTuOOh+BHp8MAVzn/EmsfivhEh9uBZhrF+yxKMlSWanUkPmytMUcIoPq\nE/SAO3Ho2LSd3b+vZvi0ZcX5R1bLUdcx29wSxtux8QjbCHLAVifz+1S4Y2IJ\nquHV\r\n=Z/eb\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "3.0.1": {"name": "braces", "version": "3.0.1", "dependencies": {"fill-range": "^7.0.1"}, "devDependencies": {"mocha": "^6.1.1", "bash-path": "^2.0.1", "ansi-colors": "^3.2.4", "gulp-format-md": "^2.0.0"}, "dist": {"shasum": "dd8f330ba1c895e39de73ec33e99275443ff0fed", "tarball": "https://registry.npmjs.org/braces/-/braces-3.0.1.tgz", "fileCount": 12, "integrity": "sha512-Vmyh3JAr5DRUKCdRrC+WyAAsWBez8HLnBmVb6Ux2VYbvC8DjqMC228WHx24fiQG5BiDOVo+otK1scdkK5S6YNg==", "signatures": [{"sig": "MEUCIQCd3BZh7HjN+utxxh8q58KmnQIGBV386u8+wMZqkpwULwIgJw5ekWt4cyzV93JOp8IHlTgJM63DFCuZDVh11jqIotE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55387, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcrdPFCRA9TVsSAnZWagAA9ZsP/1rIkCKn2vfIdy1HvAGt\nY532MHHBmRKiy5UhFTnR9xvYpQYMOFgiXnuClYJBh0j/u3CtzgDkP+T/zDjw\nteMLJxcat86yNrt42ZBHRBjRPHjRy7ALyk/ocgC2M5bXENaWKVRGAq15vIXU\nQM3kqjAvN78Ea7y9DKA1yesI+1xLTVjBZQ1iAFFkRzYZPhKMX15xRHMnMKPE\n+9ZQCQjUBj0SEimbxV87UeTorj2RSGuhjkNurLAMvvUyQ5thPGGBiGt7JKSF\niR81fJBXijrOtzNle5sENmP3OkBLN5JEhksGwBUxS1h/XJhr+FBOiaI/64Wk\niZbzg0EtoaJ509oOzy1laWL9YAo+kiPKKVVeCYD+nWpNLxumWNq9hrOSgtM3\nRc/C9Q/91B03IWeaYUZFXaLW7zh+VSKAqFl0V1L5O7O/e1OrDg88f8FBzdQA\nnijtV51AuO9pTcvtHNlhKwYwV6+wYO08xMEn0KUCTiPNugrdvsvHPlRfk+hd\nWf9Igdp6pq1PYYtVjTPcCev3bRzKNo/3p755k/4sqgpfMX3QSQ/zOanED3QO\nfvaDEBerTLtfzFhTZfK/wySUHzb4hTCO1qcOeXj7qbDnVJvVUiEwZnKsMQlm\nWM7os3QUzYuIRtCzemsxcgYgWN7TB3SkwABGlUKmot9xWrXxSmvJw0GP+LPt\nuuaR\r\n=QRl/\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "3.0.2": {"name": "braces", "version": "3.0.2", "dependencies": {"fill-range": "^7.0.1"}, "devDependencies": {"mocha": "^6.1.1", "bash-path": "^2.0.1", "ansi-colors": "^3.2.4", "gulp-format-md": "^2.0.0"}, "dist": {"shasum": "3454e1a462ee8d599e236df336cd9ea4f8afe107", "tarball": "https://registry.npmjs.org/braces/-/braces-3.0.2.tgz", "fileCount": 11, "integrity": "sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A==", "signatures": [{"sig": "MEYCIQDx963+PwpwtRhYsYrHDTB5Dz59LG71waTrE16auk36hgIhAJ2pBgwmBsPPT+35dD0W86nmV5KBEScDs2J8+olS4QX1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49239, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJctjIxCRA9TVsSAnZWagAAwA4P/i6j6DyxGZfojgDNzaNX\n6GlgCFb4mF64au4BAi+9q2gJTJOVbr2P/0iEl9tjdM5LU+tqEv1eLKkHenBi\ndc+ktaVD/c+XaTMubGNhtBfDxQUiU/QqI6dLMCEOb5EOlSclHsTiLV3frQxI\nEUzh9+LL3V6okecvADaWgt17xTWXSDRktZJoFeAK5q0017ras8yWLqp+5uDO\nRpapuec1wK4RRUDUS9/I+K9R7Kxv9aTyfdGQ5sueLj1QdAmNKDAVH77OG9Rl\nACDijw5L1KpzRkGxGkrwUHyjh0V7EMDwj74XgddEEE3eUgn5UN5n+Dmyj0/H\nP5tcGxOqNEpj9UueU8m6A18URJatSzDwqhAEKbJ1jyYs3LyElDUdiOxYedib\nHz8Ny9+5TJqEeEhU0de/8bsafjV4QsC/JZLI8nc9W65HVQ8M1fAgvxQ6O7Uj\naoZiqhME6OaE43tR4ldNnnYVujQInyOnTocL3GkbxNWvuQQieLNvBsV0p1H4\n6A9Q4y0fzsfdH+NoEJ3ghgSMn2P5Bb9EuExg0McW//H1Gj5+V2nfXEFGXoQE\n874CBL5gIxyAGSGiWB57y6bGCMWvo6gKhFdeEF+msvMSiv18hw4CcgogVzy/\n1pWI+R9hSWbaWD6q/Em9HmIJDqNS633NWy+KMMrEQLGZjfrEa3VsRjiXGCaH\nUQTq\r\n=kTks\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "3.0.3": {"name": "braces", "version": "3.0.3", "dependencies": {"fill-range": "^7.1.1"}, "devDependencies": {"mocha": "^6.1.1", "bash-path": "^2.0.1", "ansi-colors": "^3.2.4", "gulp-format-md": "^2.0.0"}, "dist": {"shasum": "490332f40919452272d55a8480adc0c441358789", "tarball": "https://registry.npmjs.org/braces/-/braces-3.0.3.tgz", "fileCount": 10, "integrity": "sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==", "signatures": [{"sig": "MEQCICfWLKNcFCGtU07ikrPJ+s1dTDH3VZTE+vQZzC22s6o+AiAEVUE/YXRqHYgctcwHqNawvsOviSDFZPvgNm8+SUXD3w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44635}, "engines": {"node": ">=8"}}}, "modified": "2024-09-18T05:27:12.449Z"}