{"_id": "statuses", "_rev": "75-840f45060c26a2ac20c2c7fa881967cc", "name": "statuses", "dist-tags": {"latest": "2.0.2"}, "versions": {"1.0.1": {"name": "statuses", "version": "1.0.1", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "statuses@1.0.1", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/expressjs/statuses", "bugs": {"url": "https://github.com/expressjs/statuses/issues", "email": "<EMAIL>"}, "dist": {"shasum": "e6e059e1bc769dfccd80fe7e9901a20a48b8ce9a", "tarball": "https://registry.npmjs.org/statuses/-/statuses-1.0.1.tgz", "integrity": "sha512-JylOaJZ6XGtQWZFfJejcFHMa2M+n02ITXF7Jh2/xDDC4Y2/SzqNpdp90/FOrnxyh+T0MV0mlKMcGUTrLXfLixw==", "signatures": [{"sig": "MEQCIE4rbNFY0uPZHOO09WOiVJOgQlGdzveaCZTq9cTWhfIWAiBJHHgSAPYiyGEm88aBMlJW3cRJ79EOvYq+VbDBD6f0uA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "https://github.com/expressjs/statuses.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "HTTP status utility", "directories": {}}, "1.0.2": {"name": "statuses", "version": "1.0.2", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "statuses@1.0.2", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/expressjs/statuses", "bugs": {"url": "https://github.com/expressjs/statuses/issues", "email": "<EMAIL>"}, "dist": {"shasum": "6e8a73c2fb0886f5eecf78e0aa2a1b9ae92ab73b", "tarball": "https://registry.npmjs.org/statuses/-/statuses-1.0.2.tgz", "integrity": "sha512-Hz2OR81FBcidKYak2XFcj/YgrjJB45v6TZnmHUUFIS9qWi9ojtfwT9BWquiXc1sxGHwGHMGRT7mcjCupWapIzg==", "signatures": [{"sig": "MEQCIB/nNhxZpB8K9XGL3OZg1NSBrCevrkHyX7bFdSuxLy19AiAkd/vv/Uprxx9y4sGPYZ6D40XxfQRGEMPy3LjT4lHjJA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "https://github.com/expressjs/statuses.git", "type": "git"}, "_npmVersion": "1.4.4", "description": "HTTP status utility", "directories": {}}, "1.0.3": {"name": "statuses", "version": "1.0.3", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "statuses@1.0.3", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/statuses", "bugs": {"url": "https://github.com/expressjs/statuses/issues", "email": "<EMAIL>"}, "dist": {"shasum": "a7d9bfb30bce92281bdba717ceb9db10d8640afb", "tarball": "https://registry.npmjs.org/statuses/-/statuses-1.0.3.tgz", "integrity": "sha512-pJmrwwECRNgoxk8Vrtlsj84Rj8Igio12GsZ/mnVV63KnS7U4mQC6RejZbOHK9mWl6jLUR2veapO6cHh37rsB/g==", "signatures": [{"sig": "MEQCIBM5g3sC9aeYYaByQfTGFQ3o+16XaAtaEwxtm/zKld85AiAVF+CUV9Xt8ArWRraSm5YCLbm8TGdFaPQDdTbxaLiPAA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "a7d9bfb30bce92281bdba717ceb9db10d8640afb", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "https://github.com/expressjs/statuses.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "HTTP status utility", "directories": {}}, "1.0.4": {"name": "statuses", "version": "1.0.4", "keywords": ["http", "status", "code"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "statuses@1.0.4", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/statuses", "bugs": {"url": "https://github.com/jshttp/statuses/issues"}, "dist": {"shasum": "a8b203f645cf475a66426f6be690205c85f3ebdd", "tarball": "https://registry.npmjs.org/statuses/-/statuses-1.0.4.tgz", "integrity": "sha512-GGd3mCpMMbcG/2T2GmDvJyL3sqn61uWSOL/4Fe2bGP3YCLiAioC1jHD5eNVhZrvPCMspr+uJQ4rsiDj2drppvg==", "signatures": [{"sig": "MEUCIEbbHjY0367sSaR1woavrRtnyQZIniKQco7bgXbOnJT8AiEAyIWNeQBW1HCK4Da0dgWqwLJBV42HgyVKHoxBS9wt8mk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "a8b203f645cf475a66426f6be690205c85f3ebdd", "gitHead": "0315a85435546839b4bcafaf5c1e6ac2acab660a", "scripts": {}, "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "https://github.com/jshttp/statuses", "type": "git"}, "_npmVersion": "1.4.21", "description": "HTTP status utility", "directories": {}}, "1.1.0": {"name": "statuses", "version": "1.1.0", "keywords": ["http", "status", "code"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "statuses@1.1.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/statuses", "bugs": {"url": "https://github.com/jshttp/statuses/issues"}, "dist": {"shasum": "937882caad053f8d808d845b333cfab9def03222", "tarball": "https://registry.npmjs.org/statuses/-/statuses-1.1.0.tgz", "integrity": "sha512-S0gvf+K300Uz85OwqUb2uNYRyvA5MDstr7WWatEdwIcpDLISadWNIRoqAJqTDNEtmCDLUtm65kjxPUvzjzOTcA==", "signatures": [{"sig": "MEYCIQDPHhJTqE3Oe/bJZE/Xn64SluadR8O6t3vWbI4lgLIRxAIhAM7s9LjczwDG91EZBh65H/0ExoINDMUQDthTzj6tESL7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "codes.json", "LICENSE"], "_shasum": "937882caad053f8d808d845b333cfab9def03222", "gitHead": "4d540d3e25368b97d1410bf8bd5637a5c9e20ce8", "scripts": {"test": "mocha --reporter spec --bail --check-leaks", "update": "node update.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks"}, "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "https://github.com/jshttp/statuses", "type": "git"}, "_npmVersion": "2.0.2", "description": "HTTP status utility", "directories": {}, "_nodeVersion": "0.11.13", "devDependencies": {"mocha": "1", "istanbul": "0"}}, "1.1.1": {"name": "statuses", "version": "1.1.1", "keywords": ["http", "status", "code"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "statuses@1.1.1", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/statuses", "bugs": {"url": "https://github.com/jshttp/statuses/issues"}, "dist": {"shasum": "10d1811e1bd3182ea3f566bf6b4745cf8edee6cc", "tarball": "https://registry.npmjs.org/statuses/-/statuses-1.1.1.tgz", "integrity": "sha512-gGzaYe65QnAp6o09Do0bSVNYLY2+e415gR98CGyz0VOIe5Jl3nETBpXZupvsYl/4mTrqqNlRBCFnWFeiylTB3Q==", "signatures": [{"sig": "MEUCIGG1rtwrjd8YAvta60rs4aiYN3TW65voLCeqG4HRrUXmAiEA9MLTxziZinKzAw52h5p8QllwdRFp+Iv1lZxZjSumB/4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "codes.json", "LICENSE"], "_shasum": "10d1811e1bd3182ea3f566bf6b4745cf8edee6cc", "gitHead": "9b775578df2b528c377f53f3ed3cfa03da4d1274", "scripts": {"test": "mocha --reporter spec --bail --check-leaks", "update": "node update.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks"}, "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "https://github.com/jshttp/statuses", "type": "git"}, "_npmVersion": "2.0.0", "description": "HTTP status utility", "directories": {}, "devDependencies": {"mocha": "1", "istanbul": "0"}}, "1.2.0": {"name": "statuses", "version": "1.2.0", "keywords": ["http", "status", "code"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "statuses@1.2.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/statuses", "bugs": {"url": "https://github.com/jshttp/statuses/issues"}, "dist": {"shasum": "4445790d65bec29184f50d54810f67e290c1679e", "tarball": "https://registry.npmjs.org/statuses/-/statuses-1.2.0.tgz", "integrity": "sha512-sLTc87cPYZEN78h6sYinpXrkcswUHidR+UmXXSu+Ar1Jpu84uevYUqAb2wrUVtq8xoPjvItswLum3x/rz8bi3g==", "signatures": [{"sig": "MEUCIQDlB3vajFX7t86hGejlwJGNb9aU56ITAlnXo/xTSGhmgwIgQmMMc78BxhCbERJuVXZZ6clDDCs0MXuqYpQwTCUbp24=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "codes.json", "LICENSE"], "_shasum": "4445790d65bec29184f50d54810f67e290c1679e", "gitHead": "64dc7753f28f0302e4140602e36f0e270ddbb1bd", "scripts": {"test": "mocha --reporter spec --bail --check-leaks", "build": "node scripts/build.js", "update": "node scripts/update.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks"}, "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "https://github.com/jshttp/statuses", "type": "git"}, "_npmVersion": "2.0.2", "description": "HTTP status utility", "directories": {}, "_nodeVersion": "0.11.14", "devDependencies": {"mocha": "1", "request": "^2.44.0", "istanbul": "0", "csv-parse": "0.0.6", "stream-to-array": "^2.0.2"}}, "1.2.1": {"name": "statuses", "version": "1.2.1", "keywords": ["http", "status", "code"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "statuses@1.2.1", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/statuses", "bugs": {"url": "https://github.com/jshttp/statuses/issues"}, "dist": {"shasum": "dded45cc18256d51ed40aec142489d5c61026d28", "tarball": "https://registry.npmjs.org/statuses/-/statuses-1.2.1.tgz", "integrity": "sha512-pVEuxHdSGrt8QmQ3LOZXLhSA6MP/iPqKzZeO6Squ7PNGkA/9MBsSfV0/L+bIxkoDmjF4tZcLpcVq/fkqoHvuKg==", "signatures": [{"sig": "MEUCIGUQ+PudkiVen/346JbgSkgWTnt4c1Bn40bA0hWpPgGDAiEA9t/Y++fyJADXq+6kdkH2W2JpmZ3wo+ye7FgcET2yBAg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "codes.json", "LICENSE"], "_shasum": "dded45cc18256d51ed40aec142489d5c61026d28", "gitHead": "49e6ac7ae4c63ee8186f56cb52112a7eeda28ed7", "scripts": {"test": "mocha --reporter spec --bail --check-leaks", "build": "node scripts/build.js", "update": "node scripts/update.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/statuses", "type": "git"}, "_npmVersion": "1.4.28", "description": "HTTP status utility", "directories": {}, "devDependencies": {"mocha": "1", "istanbul": "0", "csv-parse": "0.0.6", "stream-to-array": "2"}}, "1.3.0": {"name": "statuses", "version": "1.3.0", "keywords": ["http", "status", "code"], "license": "MIT", "_id": "statuses@1.3.0", "maintainers": [{"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/statuses", "bugs": {"url": "https://github.com/jshttp/statuses/issues"}, "dist": {"shasum": "8e55758cb20e7682c1f4fce8dcab30bf01d1e07a", "tarball": "https://registry.npmjs.org/statuses/-/statuses-1.3.0.tgz", "integrity": "sha512-Dps+XXZtE54j0Umv4yQ8mEmXv+ZI9J/wEnEyNy3N2S0W7x9jlfAHbVmOd2awZ6oCumyF5uzyuI2KZfwXu9LB5w==", "signatures": [{"sig": "MEQCIDsMfpob17X9W50vOS4yPFxnR3lKx/sG3Wb9s5xJpHBLAiB/6T8Y3HMs+RIYLgJdSTEIdWyPNjE2mjOGxre80XorvQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "index.js", "codes.json", "LICENSE"], "_shasum": "8e55758cb20e7682c1f4fce8dcab30bf01d1e07a", "engines": {"node": ">= 0.6"}, "gitHead": "b3e31e8c32dd8107e898b44b8c0b2dfff3cba495", "scripts": {"lint": "eslint **/*.js", "test": "mocha --reporter spec --check-leaks --bail test/", "build": "node scripts/build.js", "fetch": "node scripts/fetch.js", "update": "npm run fetch && npm run build", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/statuses", "type": "git"}, "_npmVersion": "1.4.28", "description": "HTTP status utility", "directories": {}, "devDependencies": {"mocha": "1.21.5", "eslint": "2.10.2", "istanbul": "0.4.3", "csv-parse": "1.0.1", "stream-to-array": "2.2.0", "eslint-plugin-promise": "1.1.0", "eslint-config-standard": "5.3.1", "eslint-plugin-standard": "1.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/statuses-1.3.0.tgz_1463517875633_0.19560232176445425", "host": "packages-12-west.internal.npmjs.com"}}, "1.3.1": {"name": "statuses", "version": "1.3.1", "keywords": ["http", "status", "code"], "license": "MIT", "_id": "statuses@1.3.1", "maintainers": [{"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/statuses", "bugs": {"url": "https://github.com/jshttp/statuses/issues"}, "dist": {"shasum": "faf51b9eb74aaef3b3acf4ad5f61abf24cb7b93e", "tarball": "https://registry.npmjs.org/statuses/-/statuses-1.3.1.tgz", "integrity": "sha512-wuTCPGlJONk/a1kqZ4fQM2+908lC7fa7nPYpTC1EhnvqLX/IICbeP1OZGDtA374trpSq68YubKUMo8oRhN46yg==", "signatures": [{"sig": "MEUCIQC1ylWZdeb8cZPn+QaDXe9QYqwMhiirDWsahvWXlOaZ2wIgJw+jyrnNi7Oh3Cfw6+EMTIWlZm27N1J+wAgGIbRrvZ4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "index.js", "codes.json", "LICENSE"], "_shasum": "faf51b9eb74aaef3b3acf4ad5f61abf24cb7b93e", "engines": {"node": ">= 0.6"}, "gitHead": "28a619be77f5b4741e6578a5764c5b06ec6d4aea", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --check-leaks --bail test/", "build": "node scripts/build.js", "fetch": "node scripts/fetch.js", "update": "npm run fetch && npm run build", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/statuses", "type": "git"}, "_npmVersion": "1.4.28", "description": "HTTP status utility", "directories": {}, "devDependencies": {"mocha": "1.21.5", "eslint": "3.10.0", "istanbul": "0.4.5", "csv-parse": "1.1.7", "stream-to-array": "2.3.0", "eslint-plugin-promise": "3.3.2", "eslint-config-standard": "6.2.1", "eslint-plugin-standard": "2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/statuses-1.3.1.tgz_1478923281491_0.5574048184789717", "host": "packages-12-west.internal.npmjs.com"}}, "1.4.0": {"name": "statuses", "version": "1.4.0", "keywords": ["http", "status", "code"], "license": "MIT", "_id": "statuses@1.4.0", "maintainers": [{"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/statuses#readme", "bugs": {"url": "https://github.com/jshttp/statuses/issues"}, "dist": {"shasum": "bb73d446da2796106efcc1b601a253d6c46bd087", "tarball": "https://registry.npmjs.org/statuses/-/statuses-1.4.0.tgz", "integrity": "sha512-zhSCtt8v2NDrRlPQpCNtw/heZLtfUDqxBM1udqikb/Hbk52LK4nQSwr10u77iopCW5LsyHpuXS0GnEc48mLeew==", "signatures": [{"sig": "MEUCIE05ZFmW4e7OnVGy51mc7urCqXus3b6J0+XxsZfdm1tCAiEAulv80S9I07GjoAuxzWeZUNM5m3fsxWn44K3wf4PKYJs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["HISTORY.md", "index.js", "codes.json", "LICENSE"], "engines": {"node": ">= 0.6"}, "gitHead": "f76682144d9f0ed2c726bf0a8c868a33e393a8e5", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --check-leaks --bail test/", "build": "node scripts/build.js", "fetch": "node scripts/fetch-apache.js && node scripts/fetch-iana.js && node scripts/fetch-nginx.js && node scripts/fetch-node.js", "update": "npm run fetch && npm run build", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/statuses.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "HTTP status utility", "directories": {}, "_nodeVersion": "6.11.4", "devDependencies": {"mocha": "1.21.5", "eslint": "3.19.0", "istanbul": "0.4.5", "raw-body": "2.3.2", "csv-parse": "1.2.4", "stream-to-array": "2.3.0", "eslint-plugin-node": "5.2.0", "eslint-plugin-import": "2.8.0", "eslint-plugin-promise": "3.6.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/statuses-1.4.0.tgz_1508525480889_0.6966120798606426", "host": "s3://npm-registry-packages"}}, "1.5.0": {"name": "statuses", "version": "1.5.0", "keywords": ["http", "status", "code"], "license": "MIT", "_id": "statuses@1.5.0", "maintainers": [{"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/statuses#readme", "bugs": {"url": "https://github.com/jshttp/statuses/issues"}, "dist": {"shasum": "161c7dac177659fd9811f43771fa99381478628c", "tarball": "https://registry.npmjs.org/statuses/-/statuses-1.5.0.tgz", "fileCount": 6, "integrity": "sha512-OpZ3zP+jT1PI7I8nemJX4AKmAX070ZkYPVWV/AaKTJl+tXCTGyVdC1a4SL8RUQYEwk/f34ZX8UTykN68FwrqAA==", "signatures": [{"sig": "MEQCIHJTQRxNoxo56lD+unriOhDa3oz9f4ovNC827kcNJpw9AiBSL32RNMG32/bqsaIikw3Yd5cI9AwzT3uDfQeDLmihKw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11034}, "_from": ".", "files": ["HISTORY.md", "index.js", "codes.json", "LICENSE"], "_shasum": "161c7dac177659fd9811f43771fa99381478628c", "engines": {"node": ">= 0.6"}, "gitHead": "4fcf6fb80ef50e8f0603b87946b0fa7868c815e7", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --check-leaks --bail test/", "build": "node scripts/build.js", "fetch": "node scripts/fetch-apache.js && node scripts/fetch-iana.js && node scripts/fetch-nginx.js && node scripts/fetch-node.js", "update": "npm run fetch && npm run build", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/statuses.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "HTTP status utility", "directories": {}, "_nodeVersion": "6.13.1", "_hasShrinkwrap": false, "devDependencies": {"mocha": "1.21.5", "eslint": "4.19.1", "istanbul": "0.4.5", "raw-body": "2.3.2", "csv-parse": "1.2.4", "stream-to-array": "2.3.0", "eslint-plugin-node": "6.0.1", "eslint-plugin-import": "2.9.0", "eslint-plugin-promise": "3.7.0", "eslint-config-standard": "11.0.0", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/statuses_1.5.0_1522201397898_0.27375877363523005", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "statuses", "version": "2.0.0", "keywords": ["http", "status", "code"], "license": "MIT", "_id": "statuses@2.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/statuses#readme", "bugs": {"url": "https://github.com/jshttp/statuses/issues"}, "dist": {"shasum": "aa7b107e018eb33e08e8aee2e7337e762dda1028", "tarball": "https://registry.npmjs.org/statuses/-/statuses-2.0.0.tgz", "fileCount": 6, "integrity": "sha512-w9jNUUQdpuVoYqXxnyOakhckBbOxRaoYqJscyIBYCS5ixyCnO7nQn7zBZvP9zf5QOPZcz2DLUpE3KsNPbJBOFA==", "signatures": [{"sig": "MEUCIHs8qIh/YcnFiMVVWZOEu1fA7JnnKxXZc9opUfHOoNVAAiEAmoGX1VW/Dg4zbMSFZeefgzXcvEQwBZJS4qHwyw4bqRs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11666, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJenLN9CRA9TVsSAnZWagAATRcQAJUaqCVG9040tRrUxUdA\nIw3zgdZE2bFqllS/qFdwrho8dnxFhvVTZH9ufDQKFrnD52j7Zq/aF3j0UGEF\nW+1dSPzmleON/wagvwsi61GqsyNZWUOYYXsQtfJw6UtLrgcmAeNkXvcWyqvY\n9gWW8/27sMqpcECZLdXX9CaH9ZnBdJr8hIx/qUCAVKBhKLXTVN23/vrlguUM\nXPavbi0uuWy4qFYx1Pejs1BBgizSvD1/8oNf9IldtEX0vtIvE8JHBxCc1nFE\noQiTH+G8PUHUDvW14pxvMge+sXiLIYYfUipxBjzUVimv3QkeBUq9kLDjuwUO\nhegOeaxCqti73VlNHS+Xrgyqsr6nxcyDAiQcfVA4Kq8D3DEOUsBQK1BaDX3D\nuWNRi7mxDxP0I9IE6waj1kH8W4aaPY5q7rN4FDkeM5Cqd0BIQmlzOh1KrRsq\npgAAh+Jke6fDvegcPhNFXUsikAxbggbkBZtbana6lIcevuufOGQTmvktmcuw\nUHKixZEnuuhxR1OuGLU+PPGGugJcckRY56Xe37RriVXUZmWgPcBfb4mMKC2L\n52zMuRvy8j87j+uMN79TYdrZQw9DH+A428o22kKamIGBkGquXRIQu8sK1Is9\n6jOnhoilhjBZ7bgEa4snAlL1s2VeRpqdKWkwIVlGMYUWR73E0khTqgJ8Oblv\n9n90\r\n=DsWi\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.8"}, "gitHead": "3bf877301beccf9a4dd0814ff8e2e0e260a7e7c6", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --check-leaks --bail test/", "build": "node scripts/build.js", "fetch": "node scripts/fetch-apache.js && node scripts/fetch-iana.js && node scripts/fetch-nginx.js && node scripts/fetch-node.js", "update": "npm run fetch && npm run build", "test-ci": "nyc --reporter=text npm test", "version": "node scripts/version-history.js && git add HISTORY.md", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/statuses.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "HTTP status utility", "directories": {}, "_nodeVersion": "13.12.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.0.1", "mocha": "7.1.1", "eslint": "6.8.0", "raw-body": "2.4.1", "csv-parse": "4.8.8", "stream-to-array": "2.3.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.20.2", "eslint-plugin-promise": "4.2.1", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "1.0.2", "eslint-plugin-standard": "4.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/statuses_2.0.0_1587327868540_0.816822952129105", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "statuses", "version": "2.0.1", "keywords": ["http", "status", "code"], "license": "MIT", "_id": "statuses@2.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/statuses#readme", "bugs": {"url": "https://github.com/jshttp/statuses/issues"}, "dist": {"shasum": "55cb000ccf1d48728bd23c685a063998cf1a1b63", "tarball": "https://registry.npmjs.org/statuses/-/statuses-2.0.1.tgz", "fileCount": 6, "integrity": "sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ==", "signatures": [{"sig": "MEUCIDRzdBTcfCOLqzQDeOxgfMJsncdHm8/L78QTe8wCntYCAiEA6K0rLVJPl3RU7x89NRnJlVA3eZjpsP/4r80JQ1+giJc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12116, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf8WY8CRA9TVsSAnZWagAA5DAP/1u/ij728Le5810ddr92\nvmj83m+au1Dta0RO+rmFDQRCAXkh+gaDNLaLdDWnta9P/UkinjyygyHlyp3W\nog9H66bYGpC8X0BRSViqecioHol381r0zyPqYiMuqdjGXY3T1YTJlaP3pljk\nyquW47UJZVFKtcYziaIPR/19sv7rOORMESl9FWMgsfytqb/Q0i2Nv67/KIRI\nU8YhoQRSJmW8pIjV5xuu4DQUxSoOb8Cbf8I6EcW5e+eCkWVijls039QuJOHT\na6UwRPFnt2RS85NGWhS+sUPzjsKCmmCvFjljy0bM1BdpG1eCEMq7poZH+VDX\n9bwnr/Bpn6xaZLUFEImHLnLhbd8h+UX5YkzCqjGFLzy/kUbEIgGO+J2XRjao\nV8GmfWF+EMX0ndjL/KNXhOoFkYOiPdOmMJKCIhlmNa84u98eI0jE9ZLlyr0P\ncYgR6j54cbyLFlu/aYnRunORd5ym56b0c+2jk4WS/F8uuUJplgkj+ItCacwN\nfVywKbyRp82SdKWCM2/LLz9rJp3oZeQm/iyLrPF68dAi3rcUc0Zx5+QwLej2\nSofaV+5/ZYe8VY+81d7Aij83i5lGINmA2lF4V+NW3SQYMmKwusWoOmtSfb4F\noMgJ1+DVNwjdjhio5+CATfSA7/MuIvHLBp5TIhd2A/93od3IQobWc1B6ePGK\n7yaN\r\n=RV4K\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.8"}, "gitHead": "d6b7ddc474bd621bb9539b611e4da9dfd845d30e", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --check-leaks --bail test/", "build": "node scripts/build.js", "fetch": "node scripts/fetch-apache.js && node scripts/fetch-iana.js && node scripts/fetch-nginx.js && node scripts/fetch-node.js", "update": "npm run fetch && npm run build", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "version": "node scripts/version-history.js && git add HISTORY.md", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/statuses.git", "type": "git"}, "_npmVersion": "6.14.6", "description": "HTTP status utility", "directories": {}, "_nodeVersion": "12.18.3", "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "mocha": "8.2.1", "eslint": "7.17.0", "raw-body": "2.4.1", "csv-parse": "4.14.2", "stream-to-array": "2.3.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.22.1", "eslint-plugin-promise": "4.2.1", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "1.0.2", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/statuses_2.0.1_1609655867375_0.20255809874210162", "host": "s3://npm-registry-packages"}}, "2.0.2": {"name": "statuses", "description": "HTTP status utility", "version": "2.0.2", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "repository": {"type": "git", "url": "git+https://github.com/jshttp/statuses.git"}, "license": "MIT", "keywords": ["http", "status", "code"], "devDependencies": {"csv-parse": "4.16.3", "eslint": "7.19.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.31.0", "eslint-plugin-markdown": "1.0.2", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "4.3.1", "eslint-plugin-standard": "4.1.0", "mocha": "8.4.0", "nyc": "15.1.0", "raw-body": "2.5.2", "stream-to-array": "2.3.0"}, "engines": {"node": ">= 0.8"}, "scripts": {"build": "node scripts/build.js", "fetch": "node scripts/fetch-apache.js && node scripts/fetch-iana.js && node scripts/fetch-nginx.js && node scripts/fetch-node.js", "lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --check-leaks --bail test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test", "update": "npm run fetch && npm run build", "version": "node scripts/version-history.js && git add HISTORY.md"}, "_id": "statuses@2.0.2", "gitHead": "33ec007ae094c35edb35d9edd293b9524b3adcb9", "bugs": {"url": "https://github.com/jshttp/statuses/issues"}, "homepage": "https://github.com/jshttp/statuses#readme", "_nodeVersion": "22.10.0", "_npmVersion": "10.9.0", "dist": {"integrity": "sha512-DvEy55V3DB7uknRo+4iOGT5fP1slR8wQohVdknigZPMpMstaKJQWhwiYBACJE3Ul2pTnATihhBYnRhZQHGBiRw==", "shasum": "8f75eecef765b5e1cfcdc080da59409ed424e382", "tarball": "https://registry.npmjs.org/statuses/-/statuses-2.0.2.tgz", "fileCount": 6, "unpackedSize": 12505, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIGdlp4os1/qU9ULEMhLjHpQiZGr8yEkpurzU9ZmelTFXAiEAiCEy/wZhVF5XVkdLMBpc1sD46S2fBzFbjmXGFOdFVqk="}]}, "_npmUser": {"name": "ulisesgascon", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ulisesgascon", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/statuses_2.0.2_1749239761158_0.34081017305854444"}, "_hasShrinkwrap": false}}, "time": {"created": "2014-03-13T01:21:33.978Z", "modified": "2025-06-06T19:56:01.588Z", "1.0.1": "2014-03-13T01:21:33.978Z", "1.0.2": "2014-03-14T19:13:18.390Z", "1.0.3": "2014-06-09T07:25:34.449Z", "1.0.4": "2014-08-21T08:55:36.731Z", "1.1.0": "2014-09-22T00:42:03.183Z", "1.1.1": "2014-09-25T00:25:01.485Z", "1.2.0": "2014-09-29T04:11:14.857Z", "1.2.1": "2015-02-01T23:52:50.008Z", "1.3.0": "2016-05-17T20:44:38.170Z", "1.3.1": "2016-11-12T04:01:23.592Z", "1.4.0": "2017-10-20T18:51:21.858Z", "1.5.0": "2018-03-28T01:43:17.941Z", "2.0.0": "2020-04-19T20:24:28.697Z", "2.0.1": "2021-01-03T06:37:47.488Z", "2.0.2": "2025-06-06T19:56:01.385Z"}, "bugs": {"url": "https://github.com/jshttp/statuses/issues"}, "license": "MIT", "homepage": "https://github.com/jshttp/statuses#readme", "keywords": ["http", "status", "code"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/statuses.git"}, "description": "HTTP status utility", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "maintainers": [{"name": "ulisesgascon", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "readme": "# statuses\n\n[![NPM Version][npm-version-image]][npm-url]\n[![NPM Downloads][npm-downloads-image]][npm-url]\n[![Node.js Version][node-version-image]][node-version-url]\n[![Build Status][ci-image]][ci-url]\n[![Test Coverage][coveralls-image]][coveralls-url]\n[![OpenSSF Scorecard Badge][ossf-scorecard-badge]][ossf-scorecard-visualizer]\n\nHTTP status utility for node.\n\nThis module provides a list of status codes and messages sourced from\na few different projects:\n\n  * The [IANA Status Code Registry](https://www.iana.org/assignments/http-status-codes/http-status-codes.xhtml)\n  * The [Node.js project](https://nodejs.org/)\n  * The [NGINX project](https://www.nginx.com/)\n  * The [Apache HTTP Server project](https://httpd.apache.org/)\n\n## Installation\n\nThis is a [Node.js](https://nodejs.org/en/) module available through the\n[npm registry](https://www.npmjs.com/). Installation is done using the\n[`npm install` command](https://docs.npmjs.com/getting-started/installing-npm-packages-locally):\n\n```sh\n$ npm install statuses\n```\n\n## API\n\n<!-- eslint-disable no-unused-vars -->\n\n```js\nvar status = require('statuses')\n```\n\n### status(code)\n\nReturns the status message string for a known HTTP status code. The code\nmay be a number or a string. An error is thrown for an unknown status code.\n\n<!-- eslint-disable no-undef -->\n\n```js\nstatus(403) // => 'Forbidden'\nstatus('403') // => 'Forbidden'\nstatus(306) // throws\n```\n\n### status(msg)\n\nReturns the numeric status code for a known HTTP status message. The message\nis case-insensitive. An error is thrown for an unknown status message.\n\n<!-- eslint-disable no-undef -->\n\n```js\nstatus('forbidden') // => 403\nstatus('Forbidden') // => 403\nstatus('foo') // throws\n```\n\n### status.codes\n\nReturns an array of all the status codes as `Integer`s.\n\n### status.code[msg]\n\nReturns the numeric status code for a known status message (in lower-case),\notherwise `undefined`.\n\n<!-- eslint-disable no-undef, no-unused-expressions -->\n\n```js\nstatus['not found'] // => 404\n```\n\n### status.empty[code]\n\nReturns `true` if a status code expects an empty body.\n\n<!-- eslint-disable no-undef, no-unused-expressions -->\n\n```js\nstatus.empty[200] // => undefined\nstatus.empty[204] // => true\nstatus.empty[304] // => true\n```\n\n### status.message[code]\n\nReturns the string message for a known numeric status code, otherwise\n`undefined`. This object is the same format as the\n[Node.js http module `http.STATUS_CODES`](https://nodejs.org/dist/latest/docs/api/http.html#http_http_status_codes).\n\n<!-- eslint-disable no-undef, no-unused-expressions -->\n\n```js\nstatus.message[404] // => 'Not Found'\n```\n\n### status.redirect[code]\n\nReturns `true` if a status code is a valid redirect status.\n\n<!-- eslint-disable no-undef, no-unused-expressions -->\n\n```js\nstatus.redirect[200] // => undefined\nstatus.redirect[301] // => true\n```\n\n### status.retry[code]\n\nReturns `true` if you should retry the rest.\n\n<!-- eslint-disable no-undef, no-unused-expressions -->\n\n```js\nstatus.retry[501] // => undefined\nstatus.retry[503] // => true\n```\n\n## License\n\n[MIT](LICENSE)\n\n[ci-image]: https://badgen.net/github/checks/jshttp/statuses/master?label=ci\n[ci-url]: https://github.com/jshttp/statuses/actions?query=workflow%3Aci\n[coveralls-image]: https://badgen.net/coveralls/c/github/jshttp/statuses/master\n[coveralls-url]: https://coveralls.io/r/jshttp/statuses?branch=master\n[node-version-image]: https://badgen.net/npm/node/statuses\n[node-version-url]: https://nodejs.org/en/download\n[npm-downloads-image]: https://badgen.net/npm/dm/statuses\n[npm-url]: https://npmjs.org/package/statuses\n[npm-version-image]: https://badgen.net/npm/v/statuses\n[ossf-scorecard-badge]: https://api.securityscorecards.dev/projects/github.com/jshttp/statuses/badge\n[ossf-scorecard-visualizer]: https://kooltheba.github.io/openssf-scorecard-api-visualizer/#/projects/github.com/jshttp/statuses\n", "readmeFilename": "README.md", "users": {"j3kz": true, "bengi": true, "eyson": true, "456wyc": true, "daizch": true, "h0ward": true, "hualei": true, "mimmo1": true, "quafoo": true, "tedyhy": true, "ziflex": true, "cwagner": true, "xueboren": true, "darkowlzz": true, "igorissen": true, "mojaray2k": true, "snowdream": true, "unboundev": true, "goodseller": true, "kodekracker": true, "tunnckocore": true, "wangnan0610": true, "nickeltobias": true, "zhenguo.zhao": true, "docksteaderluke": true, "maximilianschmitt": true}}