{"name": "cors", "dist-tags": {"latest": "2.8.5"}, "versions": {"0.0.1": {"name": "cors", "version": "0.0.1", "devDependencies": {"mocha": "latest", "should": "latest"}, "dist": {"shasum": "4354f78c02e659e6199d6b9c6f2199763e387f0d", "tarball": "https://registry.npmjs.org/cors/-/cors-0.0.1.tgz", "integrity": "sha512-ieXsXHf1hcDNmmPmLt+j5eKZ3ldJ3BxpVVFWFt/eIBo0N7dSd5XZ/3VNWN5OpqXmc/+U0ho2qPqLuvq6zAkAUw==", "signatures": [{"sig": "MEUCIQDt0oP0d2biqs8KmQwPNaOIb+Q8m94Tn5lmSQZASbvnFgIgPYCoSkpNxqxRKnynUF9qwWvTbtvEbEEVJcqr5HrpoT0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}}, "0.0.2": {"name": "cors", "version": "0.0.2", "devDependencies": {"mocha": "latest", "should": "latest"}, "dist": {"shasum": "1eb6033bf2939081e3868be6b71e43641b0218ff", "tarball": "https://registry.npmjs.org/cors/-/cors-0.0.2.tgz", "integrity": "sha512-iyrZSKt29ezaedRf5ED6eMlo7TnKxqfI0YVtffDVQLNlTHKsQAbTFYMrcUeiPxkSg2CEN2FovW/E8txNuWU8ow==", "signatures": [{"sig": "MEUCIQCgUXVsemwtopSNAuDFKyu72UZHK5IY+KZYxCne6tk1xQIgS5PTax66gkowtLbRFw4TuaXacgDTLOiex8xm3UdDe2I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}}, "0.0.3": {"name": "cors", "version": "0.0.3", "devDependencies": {"mocha": "latest", "should": "latest"}, "dist": {"shasum": "93fe11c0749e942e7d5347eb52ea10c8dfec44e7", "tarball": "https://registry.npmjs.org/cors/-/cors-0.0.3.tgz", "integrity": "sha512-NIcy+BSGDJyy/vVluBaXIXW8e44HH5Ov0+Xwibv57sgm3vaaTh5ih/EsElnB558H9qb7F78oX+ft3slvLQByhw==", "signatures": [{"sig": "MEYCIQCByK/tVyUk887VLNgtkPMVFhgyRZQKDnsSR4oZuWNPvQIhAKDGCiPj+ntT3q+WGsLaYyOprYCdulIoq3lU0YNKGUgE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}}, "0.0.4": {"name": "cors", "version": "0.0.4", "devDependencies": {"mocha": "latest", "should": "latest"}, "dist": {"shasum": "5131fc707f51dc9d4afae8eececd7e6cc32cd96b", "tarball": "https://registry.npmjs.org/cors/-/cors-0.0.4.tgz", "integrity": "sha512-wzh67PmTTGpFToaeMDkEs8HDdyGG0Ir8ObigHwIlVn+CixYRaHChRrqjZRJ4lKaD66hA3BfwovCofyRLwsMQ6A==", "signatures": [{"sig": "MEYCIQDc9Bu+C8mtGoCoAHTuO+VrKDjWempLhln7Y1ANNfbBqQIhAJahy2h2mNKN9El4LAJZVG8fiMKEtANhWTNNqWMTlv7/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}}, "0.0.5": {"name": "cors", "version": "0.0.5", "devDependencies": {"mocha": "latest", "should": "latest"}, "dist": {"shasum": "7f7130d17191d635dc437ad2b0688ae53865c524", "tarball": "https://registry.npmjs.org/cors/-/cors-0.0.5.tgz", "integrity": "sha512-aVmn/vDG85ehM2HC5IDqMi5yAjf5/UFJaxUIibMgiAg2aeZd1G47a2fxb1/2Y8jxZPglag7jvlj4IBU6F7VJOA==", "signatures": [{"sig": "MEQCIDlLRzhsb3OlBkNhcAVN5p6hcOmL2s151XQGMW+MkgPsAiA3T6L7xMdXMbQja7wD1SoZPdZPlcIlMzsBeSsRFbyVXA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}}, "0.1.0": {"name": "cors", "version": "0.1.0", "devDependencies": {"lint": "*", "mocha": "*", "should": "*", "express": "*", "supertest": "*"}, "dist": {"shasum": "20bb4acc3021029bda8a21c8349c48bb29d2d9f2", "tarball": "https://registry.npmjs.org/cors/-/cors-0.1.0.tgz", "integrity": "sha512-yaaZEPubze7fIwpAS+Qx73F8p7Ij0OpEvRSvFd5EEyVKP6yFdtus9C49g0O36A8zBUChIz4uY6GWjTLVZB3Pfw==", "signatures": [{"sig": "MEQCIEye/T9a1hFzdfRwLIZEaRylTdf/TiIt/JoGb8mp+vlHAiAFgjQnQmi2UPjTU+gjVTKc+TC1KbZI1a/KJrTLym7UOA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}}, "0.1.1": {"name": "cors", "version": "0.1.1", "devDependencies": {"lint": "*", "mocha": "*", "should": "*", "express": "*", "supertest": "*"}, "dist": {"shasum": "917cf65c8cff90971f2e41443d808253d454f7ea", "tarball": "https://registry.npmjs.org/cors/-/cors-0.1.1.tgz", "integrity": "sha512-MQRv3vhcgnlmEBwfb/6VOI+cor/d3IcXeSL80kIdobWQlqIIz+/rRVZVo41+5LeRU35s45aQQrsUlxyD8CA4Fg==", "signatures": [{"sig": "MEUCIFXtqgzR2U37MaYeIyiImWPJ48CJw0JhhvRIaNnxFl3eAiEA1eVOlaaiPlr7sT/kITAjBhQtVMxV9X/NxTUSSUJ3tas=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}}, "1.0.0": {"name": "cors", "version": "1.0.0", "devDependencies": {"lint": "*", "mocha": "*", "should": "*", "express": "*", "supertest": "*"}, "dist": {"shasum": "a1e478967477209d204340ab1991aa4e1ac72a99", "tarball": "https://registry.npmjs.org/cors/-/cors-1.0.0.tgz", "integrity": "sha512-ziL2NKl6A2Dz6Q94Nmm0Wt/xfLepQ+sriiILzORG002nHbRluDrkfnhLIY0YBwRkXobX5nC1cdmshzgPcvfU5Q==", "signatures": [{"sig": "MEYCIQD1BlEsxuLBPOG6CmHlu75acgqGPLTBAbqXjiqjxYjLgQIhALd8RBTh65K+xY/jwaQUdbROXvDlHWrlIVegAMGVK8cJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}}, "1.0.1": {"name": "cors", "version": "1.0.1", "devDependencies": {"lint": "*", "mocha": "*", "should": "*", "express": "*", "supertest": "*"}, "dist": {"shasum": "d8b18bac9460aefd5bfbb06f5d8684f7206f6d9d", "tarball": "https://registry.npmjs.org/cors/-/cors-1.0.1.tgz", "integrity": "sha512-ZeuCU/xztHR2OfehqFP8B+l/1fgVu+qtBeXg5T8iQSUKHObmCpjcAXw4Z7U8lW+pv8cHwT8xHDIYJRoaFaK2Hw==", "signatures": [{"sig": "MEQCIBDZ1c4ZVkw7ymhtfp4A0gMsGkHu10lQ2Md8MNDjPs/7AiAJW6LbXCw+kG9QgdoC/A9GE6ipQ4cgOrOOggWOOJPcnQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}}, "2.0.0": {"name": "cors", "version": "2.0.0", "devDependencies": {"lint": "*", "mocha": "*", "should": "*", "express": "*", "supertest": "*"}, "dist": {"shasum": "426a7060f35347ae3235b594a42bc09d0f766d54", "tarball": "https://registry.npmjs.org/cors/-/cors-2.0.0.tgz", "integrity": "sha512-8CpE/Dw+A4pZuOb3awDKnrmyj8ZBVYVd2Y111YIxGG0bbPBkNtRlvDRvjt5CmnqRVJ1N/bj6/ZZtXU3RBN2Rpg==", "signatures": [{"sig": "MEUCIQDDCFYBLw+qok3dwf8b+BF7OSoxLDExwQG2sgLQBL3hMwIgCj5tJlWem2f2AkinU8A5BNE1dNhhbmqFlBaJncBmVgQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}}, "2.1.0": {"name": "cors", "version": "2.1.0", "devDependencies": {"lint": "*", "mocha": "*", "should": "*", "express": "*", "supertest": "*"}, "dist": {"shasum": "7f8404a5762456021884e99da31d39b7e90bb994", "tarball": "https://registry.npmjs.org/cors/-/cors-2.1.0.tgz", "integrity": "sha512-a3sHBnRcr9bVVLFxxXiggJUFrIRXnC/RqyH8l3F/ix6LW3AqZEzcaTyCKmxGG41QO1QTYu42bWYiTaI0Jg73Dg==", "signatures": [{"sig": "MEQCIGLMP0EMlXAKhvEcIWppMIlcQLTNF33EKZ3Fvxa4WRroAiBOih+CrYxIImEnWDXb5JGGvgSDJR6nCiT8qDYHcUPN2g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}}, "2.1.1": {"name": "cors", "version": "2.1.1", "devDependencies": {"lint": "*", "mocha": "*", "should": "*", "express": "*", "supertest": "*"}, "dist": {"shasum": "affc232dd9f87c27fa8b03cc1aeac85284441786", "tarball": "https://registry.npmjs.org/cors/-/cors-2.1.1.tgz", "integrity": "sha512-tmLYKZEsfe+Z5ps0CKNZfrwyYYRrA7A4QC4gDSYBz1oX6z5hX7jeM1oaRGzkpYq0Bmde7oHTYodmaIqGrAwPOQ==", "signatures": [{"sig": "MEUCIQCQSiykG0vtW0i4SoJlRQ2zwxDn+0mnmDdn8dq2FmsstQIgNA9+wSVADI+XVNNZ0UyXAlWELsZGcGrMLhcNKVoorgM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}}, "2.2.0": {"name": "cors", "version": "2.2.0", "devDependencies": {"lint": "*", "mocha": "*", "should": "*", "express": "*", "supertest": "*"}, "dist": {"shasum": "d36a4c32917a7831cb652191f702929bbd61b6ad", "tarball": "https://registry.npmjs.org/cors/-/cors-2.2.0.tgz", "integrity": "sha512-w4DCLUil2p9eDVhWVrDoEVJFQCNBm9I8GRp3xMYTdLV/EgZOqduxSSTeyR8MSJ2OLXapwYYr1khCmuDNsemj5A==", "signatures": [{"sig": "MEYCIQDuuzT7DYa9Kba6aYiRs3ld4+x8RrtaclqTahAZSl1O2QIhALGUdBCkBw7RAyG2fYmiCtCE5CCjMVhZ2nIK3ucXYr2V", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}}, "2.3.0": {"name": "cors", "version": "2.3.0", "devDependencies": {"lint": "^1.1.2", "mocha": "^1.18.2", "should": "^3.3.1", "express": "^3", "supertest": "^0.12.0"}, "dist": {"shasum": "d7bfe9598bf427d54983cd7fab59629099bb0f17", "tarball": "https://registry.npmjs.org/cors/-/cors-2.3.0.tgz", "integrity": "sha512-jUtYSMYGFmiiXQrLuNCD/9a2sjEH0VTq2paUDgut0Nl7ocLx6RBwsTlV9fjt2c7YHxKV+78QqrhvdMMMF7Ti2A==", "signatures": [{"sig": "MEQCIB4j6ZMuu9nzASFwF7xTCgS7fQCK2yu5Az0LkHdleCk/AiAIWeUavdyu/7rfuHGRUoa2CiOa9dmqaOsggTmfGZql6Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}}, "2.3.1": {"name": "cors", "version": "2.3.1", "devDependencies": {"lint": "^1.1.2", "mocha": "^1.18.2", "should": "^3.3.1", "express": "^3", "supertest": "^0.12.0"}, "dist": {"shasum": "d223fb3763eb9475a4d05406d30e7e0344f5d00d", "tarball": "https://registry.npmjs.org/cors/-/cors-2.3.1.tgz", "integrity": "sha512-WnBAYYiTTyT+9mkmwtdIBfx/+UNrVM/PmMG8l0k7TyR9+FHu7RvPuvwbtKizaiaDlWTavazATHjPDUXhQhm+wA==", "signatures": [{"sig": "MEQCIGB3ZcywNB8AlRm9UFU+r5CyRGuaDcxA4HXHuQrPeGrTAiATZ0/bBnGKX6aiWC/4JxNOQULozGkXnoQrnhLKulW8LA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": "^1.3", "node": ">=0.8.0"}}, "2.3.2": {"name": "cors", "version": "2.3.2", "devDependencies": {"lint": "^1.1.2", "mocha": "^1.18.2", "should": "^3.3.1", "express": "^4", "supertest": "^0.12.0"}, "dist": {"shasum": "d8e4cbf8dabbc04e4b9956fd0f708c63ccf20cc8", "tarball": "https://registry.npmjs.org/cors/-/cors-2.3.2.tgz", "integrity": "sha512-odlEDqFm+0KrIY9aycBQKJ5DtVjHri/gadwRT9NNW3CU/qOAjIL/T/rHJ+qsGZjfOxfGLqfdCPOx5PjjztYqcw==", "signatures": [{"sig": "MEUCIAW8Wk+VZEULuESAnHwWu+CSWdAHxvPIMGlmDrHyRZLyAiEA6XsUP+jfLPS2ZwiunY4GBPOig6bQxSTeKEYUcKmz2Z0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": "^1.4", "node": ">=0.10.0"}}, "2.4.0": {"name": "cors", "version": "2.4.0", "devDependencies": {"lint": "^1.1.2", "mocha": "^1.18.2", "should": "^3.3.1", "express": "^4", "supertest": "^0.12.0", "body-parser": "^1.4.3"}, "dist": {"shasum": "4190112c6a79c5d128c193557481b51593bb77a7", "tarball": "https://registry.npmjs.org/cors/-/cors-2.4.0.tgz", "integrity": "sha512-5P21nCRJ4HM59MwUqvmalnG5XyPO7rLPATJrZ8vE32hxOEkR9OioU0tv2X8IRFPQCuM+ZGamyV9/TQ2O1n3plA==", "signatures": [{"sig": "MEQCIEb3/wPOiJ2HB+6RguF49LJ0Bq3kNq45jfUeUNFT4sL/AiBh/siO1ulKegL5IX8A5vJ4Rkr1V3geAanum0z/PJCdBA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": "^1.4", "node": ">=0.10.0"}}, "2.4.1": {"name": "cors", "version": "2.4.1", "devDependencies": {"lint": "^1.1.2", "mocha": "^1.18.2", "should": "^3.3.1", "express": "^4", "supertest": "^0.12.0", "body-parser": "^1.4.3"}, "dist": {"shasum": "b09c049a7ac8d5a8f757d9a4162b6dab51621581", "tarball": "https://registry.npmjs.org/cors/-/cors-2.4.1.tgz", "integrity": "sha512-FHxWgCy+mCYk+qYD+Ka30iIzC8XaaonFunZAiDpMI8UtzI0/sob81LW19Y4JwY1LS7dl5fNH84RZgzruk/3GHA==", "signatures": [{"sig": "MEQCIC1IjFN46kUxucAwi67y5w6WRUPblK1es80sZekj8AtDAiBJtV0/G2qyjjpKIJhqzfnSHdowKZPQ+XV/yXJt0ba8bw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": "^1.4", "node": ">=0.10.0"}}, "2.4.2": {"name": "cors", "version": "2.4.2", "devDependencies": {"lint": "^1.1.2", "mocha": "^1.18.2", "should": "^3.3.1", "express": "^4", "supertest": "^0.12.0", "body-parser": "^1.4.3", "basic-auth-connect": "^1"}, "dist": {"shasum": "2d0fbd504855a0a76a469b3baf5bff5bb80fa331", "tarball": "https://registry.npmjs.org/cors/-/cors-2.4.2.tgz", "integrity": "sha512-1uM9X29E6xDIJnBrmQPbzq00BhifHnjMKR+eRfS0ZHJcRk03aAG4j5zYvQq51nyy0wNxBnOoEfKspgwO58lFaw==", "signatures": [{"sig": "MEQCIBpIgnZcb/MzMJsls185PHGy7wMpOm8SSFRyTCmu8xgLAiBFwpA4y+6pSIhet5bZ/aDyB4oh4ukfe8ulZOcSWHUhbg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.5.0": {"name": "cors", "version": "2.5.0", "devDependencies": {"lint": "^1.1.2", "mocha": "^1.18.2", "should": "^3.3.1", "express": "^4", "supertest": "^0.12.0", "body-parser": "^1.4.3", "basic-auth-connect": "^1"}, "dist": {"shasum": "78dfe532496fec6c3e37d08dbe7cf1d3f7ad2586", "tarball": "https://registry.npmjs.org/cors/-/cors-2.5.0.tgz", "integrity": "sha512-qnWQAFBLnHKmMuVVM4h+eaAFEUHWI0OsvzTb+2TRh8MHUeerTyxkRogCll2WyFN88ETsTL0wfMVHORye/iARxg==", "signatures": [{"sig": "MEYCIQDCLJzdU+qgQ2rKz4yxbcmkvOAgk4uXFC9UwoE+mnd6cQIhANj3W7U5Du4aY4kSGslCo1qoWb+9TmQ2Flt72R73EdaZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.5.1": {"name": "cors", "version": "2.5.1", "dependencies": {"vary": "^1"}, "devDependencies": {"lint": "^1.1.2", "mocha": "^1.18.2", "should": "^3.3.1", "express": "^4", "supertest": "^0.12", "body-parser": "^1.4.3", "basic-auth-connect": "^1"}, "dist": {"shasum": "0fe1985195ce355c775bbd36e1cd397d5e1ce069", "tarball": "https://registry.npmjs.org/cors/-/cors-2.5.1.tgz", "integrity": "sha512-yXXpIYjplnGcVz/5gzvJm6PzDeTxomt57ckW6doR78nNQ/5TYsB8EUQs4knOQrvFqe2W6P+8tkUiIjPEvN8ZOQ==", "signatures": [{"sig": "MEQCICjIuSZ91KgDjKK2qaFZAzU1f/e1jFIFgiOjQOnMKD1rAiBT9dTAB2ckURa4iKpH4iPrW4jy0zIEQzUGhzGaKJsCaA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.5.2": {"name": "cors", "version": "2.5.2", "dependencies": {"vary": "^1"}, "devDependencies": {"lint": "^1.1.2", "mocha": "^1.18.2", "should": "^3.3.1", "express": "^4", "supertest": "^0.12", "body-parser": "^1.4.3", "basic-auth-connect": "^1"}, "dist": {"shasum": "00d5c0d1ced95001c998fa66b52c4ef931e6f8b7", "tarball": "https://registry.npmjs.org/cors/-/cors-2.5.2.tgz", "integrity": "sha512-IuoQaP+q8zND//sld7XRGbCl31Qo3/a1X9NbOFHMnEONH3jHfDe8WmrIKOuQkS4oqFx+hURLwmYxYEA3OuvY6A==", "signatures": [{"sig": "MEUCIQD7bd4pqBxAN9KnIqi8GdbKXUeXXy4GIQJgvGGTEmx5fAIgGkR6Up+07xwkXq5KlNMP8cobRjavf70ipgOdxaggMsY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.5.3": {"name": "cors", "version": "2.5.3", "dependencies": {"vary": "^1"}, "devDependencies": {"lint": "^1.1.2", "mocha": "^1.18.2", "should": "^3.3.1", "express": "^4", "supertest": "^0.12", "body-parser": "^1.4.3", "basic-auth-connect": "^1"}, "dist": {"shasum": "0d70a211ec3b6cc9824e6cdc299c0630ef69c392", "tarball": "https://registry.npmjs.org/cors/-/cors-2.5.3.tgz", "integrity": "sha512-QxDg4+bMIpDffByfCNUFlVmIj8anyoaPnvH54axXKVKKIj1DnJS9FgDurDZKSqzCNRC+QbkxPu1Ysmnhy7VbIg==", "signatures": [{"sig": "MEQCIGVCtrV9R39+KwrXxKNYG6LVnSBo1MHOeTlmNpwDlWb8AiADcT7noVrZz3cJdEu3nxxhGgMBMjFPvWWmPP3MAal9dg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.6.0": {"name": "cors", "version": "2.6.0", "dependencies": {"vary": "^1"}, "devDependencies": {"lint": "^1.1.2", "mocha": "^1.18.2", "should": "^3.3.1", "express": "^4", "supertest": "^0.12", "body-parser": "^1.4.3", "basic-auth-connect": "^1"}, "dist": {"shasum": "ed0ef328fca50e13e902f1bb7063fc61d89f2974", "tarball": "https://registry.npmjs.org/cors/-/cors-2.6.0.tgz", "integrity": "sha512-Ma<PERSON>r6gtz7l92KrLw475emM6/7evSu/nZoec7ZSVCJjEese6WxpNU6KAFjqYvR7VqKoMwVkE+CDqZLLMuvfWvhQ==", "signatures": [{"sig": "MEUCIDXFtBEzwZy2yJ4Q8tbjYoUNr2bJIyP8a/5kyfhz/yUaAiEA9ENpMpYvcSgQsDiJ/d9ZndwOomCNWxpn1nOtyXop7Go=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.6.1": {"name": "cors", "version": "2.6.1", "dependencies": {"vary": "^1"}, "devDependencies": {"lint": "^1.1.2", "mocha": "^1.18.2", "should": "^3.3.1", "express": "^4", "supertest": "^0.12", "body-parser": "^1.4.3", "basic-auth-connect": "^1"}, "dist": {"shasum": "5d27270a5fb8ae28fa2a7aa38613f7ec980792e9", "tarball": "https://registry.npmjs.org/cors/-/cors-2.6.1.tgz", "integrity": "sha512-ydxE9Jmm/J24MVH+qnlVbDBPLlM1BInOFV1OOpXzT+7MuKuE734ANAsaEO4KeKMd28wjhN28nrxs3Vd0O1l0kw==", "signatures": [{"sig": "MEYCIQC7ADDxnlMliTp4PfDk0UW94bfSVu6H/c7pVMEgPJBZNAIhAOp63MJVw3iT8iqTj9nOdBxuJuL+uQ87XD9xQm4LYKrD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.7.0": {"name": "cors", "version": "2.7.0", "dependencies": {"vary": "^1"}, "devDependencies": {"mocha": "^2.2.5", "eslint": "^0.21.2", "should": "^6.0.3", "express": "^4.12.4", "supertest": "^1.0.1", "body-parser": "^1.12.4", "basic-auth-connect": "^1.0.0"}, "dist": {"shasum": "8e21190caccab56d0651de4a89208ea977c6a377", "tarball": "https://registry.npmjs.org/cors/-/cors-2.7.0.tgz", "integrity": "sha512-BhZ1eFCRBm+DAvIV7//EGFAr7uL//khLcb/0Ff8F3jfxHXJTEr2TJ0aOI6/+hWA9gGTZx0s7pzfr2v7Kdh0YUw==", "signatures": [{"sig": "MEUCIQDxYgs+m4s1eKgY+wtP92Rav7seq2dCXo1S71aDhcGkYgIgVHSyB336ewItmaq5KEdwcbrPPl7VJKWeNhFxv+sxVKM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.7.1": {"name": "cors", "version": "2.7.1", "dependencies": {"vary": "^1"}, "devDependencies": {"mocha": "^2.2.5", "eslint": "^0.21.2", "should": "^6.0.3", "express": "^4.12.4", "supertest": "^1.0.1", "body-parser": "^1.12.4", "basic-auth-connect": "^1.0.0"}, "dist": {"shasum": "3c2e50a58af9ef8c89bee21226b099be1f02739b", "tarball": "https://registry.npmjs.org/cors/-/cors-2.7.1.tgz", "integrity": "sha512-s8O0W7pnSUNauMz02MUIFjRCfM9Cq1q2Dgcz6Lg+zMMPK79RrWj6VggI3E1eKpKrcickW2MbItQF+Vg1ApNnOA==", "signatures": [{"sig": "MEQCIEOHPMRLLDQggRegPTVIq6EClKzwHDOuAoZRHvnZ20aDAiA4hTW2C7oosc32FAzBk6a3wt+5gE6JYxwMIhlzmPMA6g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.7.2": {"name": "cors", "version": "2.7.2", "dependencies": {"vary": "^1"}, "devDependencies": {"mocha": "^2.2.5", "eslint": "^0.21.2", "should": "^6.0.3", "express": "^4.12.4", "supertest": "^1.0.1", "body-parser": "^1.12.4", "basic-auth-connect": "^1.0.0"}, "dist": {"shasum": "21385debfff24c223a10605b82311452999a1cbb", "tarball": "https://registry.npmjs.org/cors/-/cors-2.7.2.tgz", "integrity": "sha512-Y2arOFKVr8sp8CznsXEi/VaCNgCsXI5VbUNx574rmRw5BTzvt1v5znyTP/+RR33ENcSFgx5LIhW5UV35F1uSMA==", "signatures": [{"sig": "MEUCICLAHrwZfk0pxc9BTo2p9+DNEnLLrwj2psz4BO8GJ7g4AiEA+sWMDB2tYvXf0nkkPHVClNbETiqRenMfZVHxn8GZY2E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.8.0": {"name": "cors", "version": "2.8.0", "dependencies": {"vary": "^1"}, "devDependencies": {"mocha": "^2.2.5", "eslint": "^0.21.2", "should": "^6.0.3", "express": "^4.12.4", "supertest": "^1.0.1", "body-parser": "^1.12.4", "basic-auth-connect": "^1.0.0"}, "dist": {"shasum": "6262888a49f9ce4c5d189d29e1d5710ab73e6a85", "tarball": "https://registry.npmjs.org/cors/-/cors-2.8.0.tgz", "integrity": "sha512-hBj2Hnsmtcy3buHGWETxMUuBLvSvoeSNoG54jxpVpiXGm08MwBIiydQTv4VQ3033vBz5U55so+o206K7YanXSw==", "signatures": [{"sig": "MEUCIDk9Ng/NHE0OHFwPcKSiaZOcvqdyt7+LI1O7Jqkr0gcsAiEA4M/Ezvf66ASllJ9W3axwcUg/epSN3DAKDC5CDWQSQwo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.8.1": {"name": "cors", "version": "2.8.1", "dependencies": {"vary": "^1"}, "devDependencies": {"mocha": "^2.2.5", "eslint": "^0.21.2", "should": "^6.0.3", "express": "^4.12.4", "supertest": "^1.0.1", "body-parser": "^1.12.4", "basic-auth-connect": "^1.0.0"}, "dist": {"shasum": "6181aa56abb45a2825be3304703747ae4e9d2383", "tarball": "https://registry.npmjs.org/cors/-/cors-2.8.1.tgz", "integrity": "sha512-s7rWgFmYGmV3YJZ2hBl6VqvIYgos1NFaoOyVKlx/mKYUGD0MU11Bw2KWKCt49O75mfFD/oUHHhKIXDS1CYqw8g==", "signatures": [{"sig": "MEUCIDxINA2JZ+W/uLJ5ojZOUmKEwsajpBckifyYNABW112+AiEA7PLxbKdzNd0x6k7Ls4g//+oPaYlZ5bXmv0LWOSNCN3E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.8.2": {"name": "cors", "version": "2.8.2", "dependencies": {"vary": "^1", "object-assign": "^4"}, "devDependencies": {"mocha": "^2.2.5", "eslint": "^0.21.2", "should": "^6.0.3", "express": "^4.12.4", "istanbul": "^0.4.5", "supertest": "^1.0.1", "body-parser": "^1.12.4", "basic-auth-connect": "^1.0.0"}, "dist": {"shasum": "3a6c31f5a398c87394e31555b627ea3523839080", "tarball": "https://registry.npmjs.org/cors/-/cors-2.8.2.tgz", "integrity": "sha512-3YP/ZBGAAUKg5DKzd4sFlfcbhO3ynFANTWLBJ+UJHke4KmJTuGUcWhNIYpA84F2VeqcLzj3kwviUD2rzWz7lJw==", "signatures": [{"sig": "MEYCIQCeYrGdg0mxuJhG37sV74uipiGPku0MWyoLB2BRbd3nBwIhAIslsOkOx46IvsW9aq95UcIyuU6uaT7Mt10/vNzBQFhu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.8.3": {"name": "cors", "version": "2.8.3", "dependencies": {"vary": "^1", "object-assign": "^4"}, "devDependencies": {"mocha": "^2.2.5", "eslint": "^0.21.2", "should": "^6.0.3", "express": "^4.12.4", "istanbul": "^0.4.5", "supertest": "^1.0.1", "body-parser": "^1.12.4", "basic-auth-connect": "^1.0.0"}, "dist": {"shasum": "4cf78e1d23329a7496b2fc2225b77ca5bb5eb802", "tarball": "https://registry.npmjs.org/cors/-/cors-2.8.3.tgz", "integrity": "sha512-anT5RmfPJjvXdnFfu7Ft+V+5qOoCHaFSVDIRUAaj8NNQFv8gP0Ew0hxltS03M33A6OH0UxhLTv4s55Zco/R8qQ==", "signatures": [{"sig": "MEQCIC5LzRvaZ8hVki45r/oxQbJ62R1yyT3rN/jcosrK5bRHAiAzkUma9cg3EBw+Muy2SHkgzgfUidOQ3K+sjjTELuYbXg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.8.4": {"name": "cors", "version": "2.8.4", "dependencies": {"vary": "^1", "object-assign": "^4"}, "devDependencies": {"mocha": "3.4.2", "eslint": "^0.21.2", "should": "11.2.1", "express": "^4.12.4", "istanbul": "^0.4.5", "supertest": "3.0.0", "body-parser": "^1.12.4", "basic-auth-connect": "^1.0.0"}, "dist": {"shasum": "2bd381f2eb201020105cd50ea59da63090694686", "tarball": "https://registry.npmjs.org/cors/-/cors-2.8.4.tgz", "integrity": "sha512-ZEDmjGp8X+Slw7pFoWntR1aGcULLoVeKzcyTdR8dfP4LcVooO6oQ2n/vElN7L2RmoJ7mpJGGiseaDr3m3g85eg==", "signatures": [{"sig": "MEUCIBBYYZQwsUf+OvBYMSoESd/ElxMI5Mf9Ap8dLSG7u/cAAiEAh33uf3TvNL2eGk63sc7Ws05gII4bNTu3t0KsTTBtvw4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.8.5": {"name": "cors", "version": "2.8.5", "dependencies": {"vary": "^1", "object-assign": "^4"}, "devDependencies": {"nyc": "13.1.0", "after": "0.8.2", "mocha": "5.2.0", "eslint": "2.13.1", "express": "4.16.3", "supertest": "3.3.0"}, "dist": {"shasum": "eac11da51592dd86b9f06f6e7ac293b3df875d29", "tarball": "https://registry.npmjs.org/cors/-/cors-2.8.5.tgz", "fileCount": 6, "integrity": "sha512-KIHbLJqu73RGr/hnbrO9uBeixNGuvSQjul/jdFvS/KFSIH1hWVd1ng7zOHx+YrEfInLG7q4n6GHQ9cDtxv/P6g==", "signatures": [{"sig": "MEUCIHzJBLZQzZrohsa0JrSIjzuf1IQnRkM4nZCLWNPVsjOOAiEAzBozqnbTfeHT7nvI/pTbAPTSu8OkOMDBcFGVUeab0A8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19988, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb313dCRA9TVsSAnZWagAApVgP/30ky26N6zFR3CDubvgd\nvrQRFNnbrB3n80hIoyBOVnGARmeO2LX+Cx2BvuQc10omCjLWDO8pS8FQCasg\nV0rGsbMId632LvJ598RnfyWw2qwwP2jBKWqtiAa+sg2nizqvvVnXlp/OapbH\nvSFncO9EFR8f5f0j61/Ue3SfFlak5d1RNOrCYprNHL6gzNS54IPzQkYix9H9\n9yih7kL82nijOPAe0wSRQGF++lW3Cs4oM3h+U/cT+dPEU+eB6JWKc92uXtkt\n7bd68ZWeUDyUA86SvxZfsOqiTakojR248wUPmC91X9wXFRRbcGP6U7HfCxSh\n7YihwCW06LnFDsaZzMZHNUfnrilsehT80rfQBGCG7cGll3c0O53ZT7otJT9g\nQJlHyNGhqzgwku1/WPMPL7UciFGy9HJsleXAYt+iUzJiMWBdprAmxVP/yJkl\nFvlsiqvz/R247da8ONXJqg5xis7+nIlB31ccPqmchbSIRhfDYVEpFUnn0des\nmtoBpqW+xuRxR7Atko65CyizTc/8ZM0mbkYeQdrSnbt34K/BY/bbMwy/bJhX\n7wb8xWmRPZEi7LTRHDqJkBj2+kv1FzCkZZk1srhy1uFIX4rnf5KJq0aj7BK7\nLfLk/eekTQETfvi9ZefYuCgBcE7YVFZ9faYAXtx6J08OShR17NsPDnYxVPpc\nxCzl\r\n=ec0y\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.10"}}}, "modified": "2025-04-09T02:30:00.440Z"}