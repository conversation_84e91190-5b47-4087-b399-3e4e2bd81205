{"_id": "aws-ssl-profiles", "_rev": "5-afe055f9b3bda551121310cb5f0c80c8", "name": "aws-ssl-profiles", "dist-tags": {"latest": "1.1.2"}, "versions": {"0.1.0": {"name": "aws-ssl-profiles", "version": "0.1.0", "author": {"name": "https://github.com/wellwelwel"}, "license": "MIT", "_id": "aws-ssl-profiles@0.1.0", "maintainers": [{"name": "weslley.io", "email": "w.es<PERSON><EMAIL>"}], "homepage": "https://github.com/mysqljs/aws-ssl-profiles#readme", "bugs": {"url": "https://github.com/mysqljs/aws-ssl-profiles/issues"}, "dist": {"shasum": "5d5ff77c40aeda8b615e1b3495ac99361a0e9988", "tarball": "https://registry.npmjs.org/aws-ssl-profiles/-/aws-ssl-profiles-0.1.0.tgz", "fileCount": 9, "integrity": "sha512-P9e9Kh4Blfps90k24J/9HQUzndnNME+bnZ4vysDCD9bIwYAL0v5BMnu6j4+aJO5W2vZ3a/R+xrXY3irRrUKatA==", "signatures": [{"sig": "MEUCIGaGA2rkkFNRXR2C6SIj8JbR6wF262TCL+Ukw9uXWgKHAiEAg5BN4riiTdh0S6fWJBnPrO5nAhDl8tpiel+RgmtvL68=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 218070}, "main": "lib/index.js", "types": "./lib/index.d.ts", "engines": {"node": ">= 6.0.0"}, "gitHead": "ffdaf1ddd74283a4f02d26a93e245afa1e4cfd82", "scripts": {"lint": "eslint . --ext .js,.ts && prettier --check .", "test": "poku --parallel ./test", "build": "npx tsc", "test:ci": "npm run lint && npm run build -- --noEmit && npm run test", "lint:fix": "eslint . --fix --config ./.eslintrc.json && prettier --write ."}, "_npmUser": {"name": "weslley.io", "email": "w.es<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/mysqljs/aws-ssl-profiles.git", "type": "git"}, "_npmVersion": "10.5.2", "description": "AWS RDS certificates bundles.", "directories": {}, "_nodeVersion": "21.7.3", "_hasShrinkwrap": false, "devDependencies": {"tsx": "^4.7.2", "poku": "^1.9.3", "eslint": "^8.57.0", "x509.js": "^1.0.0", "prettier": "^3.2.5", "typescript": "^5.4.5", "@types/node": "^20.12.7", "@types/x509.js": "^1.0.3", "eslint-plugin-import": "^2.29.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "@typescript-eslint/parser": "^7.7.0", "@typescript-eslint/eslint-plugin": "^7.7.0", "eslint-import-resolver-typescript": "^3.6.1"}, "_npmOperationalInternal": {"tmp": "tmp/aws-ssl-profiles_0.1.0_1713367407436_0.1959272391632494", "host": "s3://npm-registry-packages"}}, "1.0.0": {"name": "aws-ssl-profiles", "version": "1.0.0", "keywords": ["mysql", "mysql2", "pg", "postgres", "aws", "rds", "ssl", "certificates", "ca", "bundle"], "author": {"name": "https://github.com/wellwelwel"}, "license": "MIT", "_id": "aws-ssl-profiles@1.0.0", "maintainers": [{"name": "weslley.io", "email": "w.es<PERSON><EMAIL>"}], "homepage": "https://github.com/mysqljs/aws-ssl-profiles#readme", "bugs": {"url": "https://github.com/mysqljs/aws-ssl-profiles/issues"}, "dist": {"shasum": "af08d2c5d9122eefdf93041b3cc803bd63393a86", "tarball": "https://registry.npmjs.org/aws-ssl-profiles/-/aws-ssl-profiles-1.0.0.tgz", "fileCount": 9, "integrity": "sha512-8d0ANNUrS1iAzPXfj7QS8zKJ9OnXVRA5VJ7NVQyLU5zKDVQOfojSqN63awAfi7Ji8LCRnXPjLvcdwm1TsJe/Qw==", "signatures": [{"sig": "MEYCIQCkpUtyHYUAk4CGe4KWnm0gdgzM1EMf/GF+wFAxuyufCgIhAMdfcc9FGzWKFbZB4ke3opqLwzSd2eyG1OnYaNs8By8S", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 219951}, "main": "lib/index.js", "types": "./lib/index.d.ts", "engines": {"node": ">= 6.0.0"}, "gitHead": "7c93bec5e82ce4029f52d2b46d048f44e106e067", "scripts": {"lint": "eslint . --ext .js,.ts && prettier --check .", "test": "poku --parallel ./test", "build": "npx tsc", "test:ci": "npm run lint && npm run build -- --noEmit && npm run test", "lint:fix": "eslint . --fix --config ./.eslintrc.json && prettier --write ."}, "_npmUser": {"name": "weslley.io", "email": "w.es<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/mysqljs/aws-ssl-profiles.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "AWS RDS SSL certificates bundles.", "directories": {}, "_nodeVersion": "20.12.2", "_hasShrinkwrap": false, "devDependencies": {"tsx": "^4.7.2", "poku": "^1.9.3", "eslint": "^8.57.0", "x509.js": "^1.0.0", "prettier": "^3.2.5", "typescript": "^5.4.5", "@types/node": "^20.12.7", "@types/x509.js": "^1.0.3", "eslint-plugin-import": "^2.29.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "@typescript-eslint/parser": "^7.7.0", "@typescript-eslint/eslint-plugin": "^7.7.0", "eslint-import-resolver-typescript": "^3.6.1"}, "_npmOperationalInternal": {"tmp": "tmp/aws-ssl-profiles_1.0.0_1713706012931_0.8357874343270244", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "aws-ssl-profiles", "version": "1.1.0", "keywords": ["mysql", "mysql2", "pg", "postgres", "aws", "rds", "ssl", "certificates", "ca", "bundle"], "author": {"name": "https://github.com/wellwelwel"}, "license": "MIT", "_id": "aws-ssl-profiles@1.1.0", "maintainers": [{"name": "sidor<PERSON>", "email": "<EMAIL>"}, {"name": "weslley.io", "email": "w.es<PERSON><EMAIL>"}], "homepage": "https://github.com/mysqljs/aws-ssl-profiles#readme", "bugs": {"url": "https://github.com/mysqljs/aws-ssl-profiles/issues"}, "dist": {"shasum": "f7854213ac3bb55d88ec5ce452351d817d1ba35f", "tarball": "https://registry.npmjs.org/aws-ssl-profiles/-/aws-ssl-profiles-1.1.0.tgz", "fileCount": 11, "integrity": "sha512-5HcV4J4dvPcD6NEhx1C3Gqt7GCU1gOFyNMRXcHF+IeEn+rbKiwk9jwu4ziEtkT5RAKJ3nPzNhQ3FjLOgLFnwCQ==", "signatures": [{"sig": "MEQCIGj4KUoX76h77RGB5rPRdbOQSBIQ2db6WU9L8kzO2BVPAiAkHTxltxq4Jj6ojfdTBxa8qsqiGn79Yu/6cd63ZsKHAg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 227860}, "main": "lib/index.js", "types": "./lib/index.d.ts", "engines": {"node": ">= 6.0.0"}, "gitHead": "becb048901083d1c5b806cafce91dd2f96f9e8d7", "scripts": {"lint": "npx @biomejs/biome lint && prettier --check .", "test": "poku --parallel ./test", "build": "npx tsc", "test:ci": "npm run lint && npm run build -- --noEmit && npm run test", "lint:fix": "npx @biomejs/biome lint --write . && prettier --write ."}, "_npmUser": {"name": "weslley.io", "email": "w.es<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/mysqljs/aws-ssl-profiles.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "AWS RDS SSL certificates bundles.", "directories": {}, "_nodeVersion": "20.15.0", "_hasShrinkwrap": false, "devDependencies": {"tsx": "^4.16.2", "poku": "^2.0.0", "x509.js": "^1.0.0", "prettier": "^3.3.3", "typescript": "^5.5.3", "@biomejs/biome": "^1.8.3", "@types/x509.js": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/aws-ssl-profiles_1.1.0_1721012956014_0.14544327071393015", "host": "s3://npm-registry-packages"}}, "1.1.1": {"name": "aws-ssl-profiles", "version": "1.1.1", "keywords": ["mysql", "mysql2", "pg", "postgres", "aws", "rds", "ssl", "certificates", "ca", "bundle"], "author": {"name": "https://github.com/wellwelwel"}, "license": "MIT", "_id": "aws-ssl-profiles@1.1.1", "maintainers": [{"name": "sidor<PERSON>", "email": "<EMAIL>"}, {"name": "weslley.io", "email": "w.es<PERSON><EMAIL>"}], "homepage": "https://github.com/mysqljs/aws-ssl-profiles#readme", "bugs": {"url": "https://github.com/mysqljs/aws-ssl-profiles/issues"}, "dist": {"shasum": "21ef8ad77d753927f6c01b144c5ef4cc4f150cdc", "tarball": "https://registry.npmjs.org/aws-ssl-profiles/-/aws-ssl-profiles-1.1.1.tgz", "fileCount": 11, "integrity": "sha512-+H+kuK34PfMaI9PNU/NSjBKL5hh/KDM9J72kwYeYEm0A8B1AC4fuCy3qsjnA7lxklgyXsB68yn8Z2xoZEjgwCQ==", "signatures": [{"sig": "MEUCIQCbyYiZtyzsebw0PYNOgrUnlL0A42BbE0qX93q6fCj4xgIgFm/ewDVx8jNMw9qyDRaylU3J18G1dSo6B9RVoTz/q9c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 228104}, "main": "lib/index.js", "types": "./lib/index.d.ts", "engines": {"node": ">= 6.0.0"}, "gitHead": "d8f28d397a6b948ac73d90113e1fe67f9265af4e", "scripts": {"lint": "npx @biomejs/biome lint && prettier --check .", "test": "poku --parallel ./test", "build": "npx tsc", "pretest": "npm run build", "test:ci": "npm run lint && npm run test", "lint:fix": "npx @biomejs/biome lint --write . && prettier --write .", "postbuild": "cp src/index.d.ts lib/index.d.ts"}, "_npmUser": {"name": "weslley.io", "email": "w.es<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/mysqljs/aws-ssl-profiles.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "AWS RDS SSL certificates bundles.", "directories": {}, "_nodeVersion": "20.15.0", "_hasShrinkwrap": false, "devDependencies": {"tsx": "^4.16.2", "poku": "^2.0.0", "x509.js": "^1.0.0", "prettier": "^3.3.3", "typescript": "^5.5.3", "@types/node": "^20.14.10", "@biomejs/biome": "^1.8.3", "@types/x509.js": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/aws-ssl-profiles_1.1.1_1721022447801_0.3668274734703558", "host": "s3://npm-registry-packages"}}, "1.1.2": {"name": "aws-ssl-profiles", "version": "1.1.2", "main": "lib/index.js", "author": {"name": "https://github.com/wellwelwel"}, "description": "AWS RDS SSL certificates bundles.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/mysqljs/aws-ssl-profiles.git"}, "bugs": {"url": "https://github.com/mysqljs/aws-ssl-profiles/issues"}, "devDependencies": {"@biomejs/biome": "^1.8.3", "@types/node": "^22.5.1", "@types/x509.js": "^1.0.3", "poku": "^2.5.0", "prettier": "^3.3.3", "tsx": "^4.19.0", "typescript": "^5.5.4", "x509.js": "^1.0.0"}, "engines": {"node": ">= 6.0.0"}, "keywords": ["mysql", "mysql2", "pg", "postgres", "aws", "rds", "ssl", "certificates", "ca", "bundle"], "scripts": {"build": "npx tsc", "postbuild": "cp src/index.d.ts lib/index.d.ts", "lint": "npx @biomejs/biome lint && prettier --check .", "lint:fix": "npx @biomejs/biome lint --write . && prettier --write .", "pretest": "npm run build", "test": "poku --parallel ./test", "test:ci": "npm run lint && npm run test"}, "_id": "aws-ssl-profiles@1.1.2", "gitHead": "98d7cf207ea071b9b093c0fa3597d0902f028805", "types": "./lib/index.d.ts", "homepage": "https://github.com/mysqljs/aws-ssl-profiles#readme", "_nodeVersion": "20.17.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-NZKeq9AfyQvEeNlN0zSYAaWrmBffJh3IELMZfRpJVWgrpEbtEpnjvzqBPf+mxoI287JohRDoa+/nsfqqiZmF6g==", "shasum": "157dd77e9f19b1d123678e93f120e6f193022641", "tarball": "https://registry.npmjs.org/aws-ssl-profiles/-/aws-ssl-profiles-1.1.2.tgz", "fileCount": 11, "unpackedSize": 228109, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/aws-ssl-profiles@1.1.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCCu3Gc86Cy8OcSftHa5BUx8uXfL0uqvHW1ah4KLtB3TQIgP/4MybsNpUIv3yFjv3XMKUgfC+wWe8545UFLT9uVH/A="}]}, "_npmUser": {"name": "weslley.io", "email": "w.es<PERSON><EMAIL>"}, "directories": {}, "maintainers": [{"name": "sidor<PERSON>", "email": "<EMAIL>"}, {"name": "weslley.io", "email": "w.es<PERSON><EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/aws-ssl-profiles_1.1.2_1724980822724_0.8019137951705613"}, "_hasShrinkwrap": false}}, "time": {"created": "2024-04-17T15:23:27.436Z", "modified": "2024-08-30T01:20:23.319Z", "0.1.0": "2024-04-17T15:23:27.641Z", "1.0.0": "2024-04-21T13:26:53.121Z", "1.1.0": "2024-07-15T03:09:16.162Z", "1.1.1": "2024-07-15T05:47:27.964Z", "1.1.2": "2024-08-30T01:20:22.868Z"}, "bugs": {"url": "https://github.com/mysqljs/aws-ssl-profiles/issues"}, "author": {"name": "https://github.com/wellwelwel"}, "license": "MIT", "homepage": "https://github.com/mysqljs/aws-ssl-profiles#readme", "keywords": ["mysql", "mysql2", "pg", "postgres", "aws", "rds", "ssl", "certificates", "ca", "bundle"], "repository": {"type": "git", "url": "git+https://github.com/mysqljs/aws-ssl-profiles.git"}, "description": "AWS RDS SSL certificates bundles.", "maintainers": [{"name": "sidor<PERSON>", "email": "<EMAIL>"}, {"name": "weslley.io", "email": "w.es<PERSON><EMAIL>"}], "readme": "# AWS SSL Profiles\n\n[**AWS RDS**](https://aws.amazon.com/rds/) **SSL** Certificates Bundles.\n\n**Table of Contents**\n\n- [Installation](#installation)\n- [Usage](#usage)\n  - [**mysqljs/mysql**](#mysqljsmysql)\n  - [**MySQL2**](#mysql2)\n  - [**node-postgres**](#node-postgres)\n  - [Custom `ssl` options](#custom-ssl-options)\n- [License](#license)\n- [Security](#security)\n- [Contributing](#contributing)\n- [Acknowledgements](#acknowledgements)\n\n---\n\n## Installation\n\n```bash\nnpm install --save aws-ssl-profiles\n```\n\n---\n\n## Usage\n\n### [mysqljs/mysql](https://github.com/mysqljs/mysql)\n\n```js\nconst mysql = require('mysql');\nconst awsCaBundle = require('aws-ssl-profiles');\n\n// mysql connection\nconst connection = mysql.createConnection({\n  //...\n  ssl: awsCaBundle,\n});\n\n// mysql connection pool\nconst pool = mysql.createPool({\n  //...\n  ssl: awsCaBundle,\n});\n```\n\n### [MySQL2](https://github.com/sidorares/node-mysql2)\n\n```js\nconst mysql = require('mysql2');\nconst awsCaBundle = require('aws-ssl-profiles');\n\n// mysql2 connection\nconst connection = mysql.createConnection({\n  //...\n  ssl: awsCaBundle,\n});\n\n// mysql2 connection pool\nconst pool = mysql.createPool({\n  //...\n  ssl: awsCaBundle,\n});\n```\n\n### [node-postgres](https://github.com/brianc/node-postgres)\n\n```js\nconst pg = require('pg');\nconst awsCaBundle = require('aws-ssl-profiles');\n\n// pg connection\nconst client = new pg.Client({\n  // ...\n  ssl: awsCaBundle,\n});\n\n// pg connection pool\nconst pool = new pg.Pool({\n  // ...\n  ssl: awsCaBundle,\n});\n```\n\n### Custom `ssl` options\n\nUsing **AWS SSL Profiles** with custom `ssl` options:\n\n```js\n{\n  // ...\n  ssl: {\n    ...awsCaBundle,\n    rejectUnauthorized: true,\n    // ...\n  }\n}\n```\n\n```js\n{\n  // ...\n  ssl: {\n    ca: awsCaBundle.ca,\n    rejectUnauthorized: true,\n    // ...\n  }\n}\n```\n\n### Custom bundles\n\n```js\nconst { proxyBundle } = require('aws-ssl-profiles');\n\n{\n  // ...\n  ssl: proxyBundle,\n}\n```\n\n---\n\n## License\n\n**AWS SSL Profiles** is under the [**MIT License**](./LICENSE).\n\n---\n\n## Security\n\nPlease check the [**SECURITY.md**](./SECURITY.md).\n\n---\n\n## Contributing\n\nPlease check the [**CONTRIBUTING.md**](./CONTRIBUTING.md) for instructions.\n\n---\n\n## Acknowledgements\n\n[**Contributors**](https://github.com/mysqljs/aws-ssl-profiles/graphs/contributors).\n", "readmeFilename": "README.md"}