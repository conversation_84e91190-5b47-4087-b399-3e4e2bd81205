{"_id": "@rollup/rollup-win32-arm64-msvc", "_rev": "152-9bfe7ac75c978d283adf49dd1ec5a07e", "name": "@rollup/rollup-win32-arm64-msvc", "dist-tags": {"beta": "4.33.0-0", "latest": "4.44.0"}, "versions": {"4.0.0-0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.0-0", "keywords": ["modules", "bundler", "bundling", "es6", "optimizer"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.0.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "d1aa9e9bb7d036898cce66e2a628866fa9496a1d", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.0-0.tgz", "fileCount": 2, "integrity": "sha512-TzG3kpfIKdQOAq+gURHANATZj5Do6T6323V+F/ATkNWiG1cWR5wCtMfYfuNcVXm26z5r2cI3pi5EiOM42jpmDQ==", "signatures": [{"sig": "MEYCIQCt50YvfzSJ2wa315v4QEYU5qQF+UGR7g20eiMIxdUNygIhAPnAWcmTlomgLiPSoiHO1gXnTtkIdci5gwg2wYY7bbhs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 735}, "main": "native/rollup.win32-arm64-msvc.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "580d17223962a0a359da88420001bbbc738e633a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Next-generation ES module bundler", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.0.0-0_1690831074113_0.6666528336610902", "host": "s3://npm-registry-packages"}}, "4.0.0-1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.0.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "5104a39c0aeda01935065498ad16c0c941e91689", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.0-1.tgz", "fileCount": 3, "integrity": "sha512-fjfp9qaJha1W4Zvcns7csAMPIlJMDvNid2dLFwBKKFppGV2NVS1mTq2q5Z2YC4HG9bOT8shPmYnq0mN99HuE1w==", "signatures": [{"sig": "MEUCIQC5mfDwYTiuJUqDKqfwQfLNXlOXcgCGncYI9QpsP6ubUwIgPfTRNr1tCeAOzkU8HewerwJOT88kwaBYIJCMomNH474=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2744880}, "main": "rollup.win32-arm64-msvc.node", "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "gitHead": "d5b6ec3f77c860c048e2830353f5af4593ffaf20", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.0.0-1_1690865337886_0.02801411964037781", "host": "s3://npm-registry-packages"}}, "4.0.0-2": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.0-2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.0.0-2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "8ea8ad6a50ea492651e303ecc9d4a703cf8e057a", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.0-2.tgz", "fileCount": 3, "integrity": "sha512-U298hzXxAo3rMxXA0fLpzFgGvHRSBLXw+YAEAfHATizCqUO+6x0AWooIqHTp3EpE0LROSOX96k306A7wlXq1Sg==", "signatures": [{"sig": "MEQCIFpc5jWHa7BVZKVo9IzdVvtsu1IjT/1lWmvu0CL0N1SRAiBkZX9FpIZV4l4QMmWpILhSsHxuB985dBj2AyxtJGGZPA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2744880}, "main": "rollup.win32-arm64-msvc.node", "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "gitHead": "d62558dbc45912c9c4478dc761bb290738c3b968", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.0.0-2_1690888590833_0.5699681687683182", "host": "s3://npm-registry-packages"}}, "4.0.0-3": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.0-3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.0.0-3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "09bda3dca670917f19ff54e4f0676f3dd528d2fb", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.0-3.tgz", "fileCount": 3, "integrity": "sha512-hHNMqTLtPRa7lkJYIRivR/K6kXPN3NiSaZoZUb6y9yYaCDYNZUufE04fIqjJDCDLLI+EbRrAQEcv+6pvUw9CXw==", "signatures": [{"sig": "MEYCIQCXnzoLS4nbEGPIJ/hoD6ksEZ4xMigU91x5aESD5P68CgIhAKl6No+qlpMB2lOwIoLy7+QZBxRwc0So1RFNEWUzOafn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2739248}, "main": "rollup.win32-arm64-msvc.node", "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "gitHead": "d9deb724f026a6f3e429509fce2d920e75d6a1ae", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.0.0-3_1691137012771_0.39221954694134276", "host": "s3://npm-registry-packages"}}, "4.0.0-4": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.0-4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.0.0-4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "09b586105ba65b114f8b9a08554833cf9b06494d", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.0-4.tgz", "fileCount": 3, "integrity": "sha512-FIsLT66CcQr24+<PERSON><PERSON><PERSON><PERSON>ai8d4cGyDBowoKLad56MUEfvo05+9CZLhr8f+j9lqxdhgnN2L5MrIV8yJE93Ro/vlg==", "signatures": [{"sig": "MEUCIQDAAxta9dBHj3NNjbxoQ6QM+MZWS8taRzFqfwdDTkALKgIgP29B5QXwcwff6nuhfOgxloToNBLl8H1w59DjIi1N8A4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2739760}, "main": "rollup.win32-arm64-msvc.node", "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "gitHead": "c416e3eb3d2d6055d6567cac6e8747b992eec1de", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.0.0-4_1691148995499_0.8626498580790858", "host": "s3://npm-registry-packages"}}, "4.0.0-5": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.0-5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.0.0-5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "37a0be7063c9fbd049603ab9f10feec55cd30715", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.0-5.tgz", "fileCount": 3, "integrity": "sha512-2G7SH+MbZ1wjiilx/3FGDffGZi28KD//0t2dVQOewA9kbiZbE0zQFEgtrsiCvGstYCMr6Om5sexad7eCiiRF/A==", "signatures": [{"sig": "MEUCIFAuIT1IBxq66JIxd4iFW2NNeA5Sl3mcyISkFLxdT7ZyAiEA0R8RmjpjR4ccbrNvQpc6DiFmg/BlQTEYH1Cb3qyaQF8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3130019}, "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.win32-arm64-msvc.node"}}, "gitHead": "6284e58c1be160b656b9f2b44e8e2b1e5a93f9df", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.0.0-5_1692514607616_0.8612684052391522", "host": "s3://npm-registry-packages"}}, "4.0.0-6": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.0-6", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.0.0-6", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "f782c9f04732cfb9fa11671e280543405c543972", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.0-6.tgz", "fileCount": 3, "integrity": "sha512-z08+47Yh6tp2dZBeR6PxkbTjJLaIdXFiBY/S8U3Q3ya0Jx7UQApEP7azvspJdcIRdD7JbW5J1JRZ05GNlBBaJw==", "signatures": [{"sig": "MEYCIQDuXS6sLwGKYwYG9vM2GhYmykWgCEvOwzdS0L2vsBiRogIhANRvWe3uSuFGch0uOkeQPUr9UXU6v5OsLNE3Zg4MknW3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3130019}, "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.win32-arm64-msvc.node"}}, "gitHead": "39e7492a12eca9107c929d533c16608c9a0054be", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.0.0-6_1692517896757_0.22756201309832091", "host": "s3://npm-registry-packages"}}, "4.0.0-7": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.0-7", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.0.0-7", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "fa08a29e2ba3b71a82f9436a47f8f34a6d84b42a", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.0-7.tgz", "fileCount": 7, "integrity": "sha512-s9UNX4tod5DCDtOt0R1fAjpwZelflRulbuS6Z53IA6Xns5BdlSfdCZpGLFmlUx7c0WCy2R5bxyhY09shI2VAlw==", "signatures": [{"sig": "MEUCIFi+/BSC3r9fXm0bjAPtKJyTd+LJqYYpzx5dqin5+3fHAiEAlCSvdfO/OZyL0jFS/iWB/4ZqjAI5+ziJC9sVEv33LTw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8584941}, "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.win32-arm64-msvc.node"}}, "gitHead": "afaa754955a083970b389711127e368d6f4d235b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.0.0-7_1692527603923_0.2084538362547219", "host": "s3://npm-registry-packages"}}, "4.0.0-8": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.0-8", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.0.0-8", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "25d6dcfc9e689e1f9a792bcc47710afb7fd97e0a", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.0-8.tgz", "fileCount": 7, "integrity": "sha512-mdlbEf6ElLzjevxjTYH8OaiKUNQoffZC2YYbKfVEzwPSQbZOe3BNutoW9Yvy3z8niOm90C6LTJOUxA3Oag66Gw==", "signatures": [{"sig": "MEUCIQCNAJqBXe6Op2rVJoEr2NNoZ1KHbVu2Y7KoPCXSVRqluwIgSSSdRyePJJP/hdJZx/JOON1p5tgLsktQ9sRfJo3umh0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8584941}, "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.win32-arm64-msvc.node"}}, "gitHead": "5bfa022de96252b5eaf0bdab90be6bcfefcccb57", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.0.0-8_1692530535010_0.9231160738893072", "host": "s3://npm-registry-packages"}}, "4.0.0-9": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.0-9", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.0.0-9", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "5479a0d763d46ebbe681654f9e41ec232e0d9bc2", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.0-9.tgz", "fileCount": 7, "integrity": "sha512-23dVTBJyMjrXfYSHoFrq4x7xMmjE2aU/6JfVpIof29/MHzRzn3OHHiRdg8GTHl05SYK2WZBAq8SS/8zprrVOuQ==", "signatures": [{"sig": "MEYCIQC4iY3e7A8bHpHeNlC6uUBwP533nucp98em0vk7rJFdGQIhALFJzwOpJrs/IyUnIQz+nKiPszEHW0Qyflb9fgDPoJ/E", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8584941}, "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.win32-arm64-msvc.node"}}, "gitHead": "e4d55671a81334ddc59fdbcd81ceabdb77d96974", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.0.0-9_1692541741461_0.6956883954924955", "host": "s3://npm-registry-packages"}}, "4.0.0-10": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.0-10", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.0.0-10", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "0e214f90b79b50a5cdf018d29473e4f286974033", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.0-10.tgz", "fileCount": 7, "integrity": "sha512-ytyIBfpA5BmU616Q+NXeliapb4PSIILgUjwhs8xaI6GyXCsdpcestoLazjHcqlT9PGNsrR2iitsn4wi+cgVvjQ==", "signatures": [{"sig": "MEUCIGBnBGSUhhKXzaCrF9px4sKU1Xycc92dENI3+52ig+DVAiEAoiFlJNhNfWKJwsj+0nz7sLmL02ShMBxOMOfSCwX+FpA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8593353}, "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.win32-arm64-msvc.node"}}, "gitHead": "2c7e3e32f5d56c60d92907a9ceacd338aa99ca82", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.0.0-10_1692631799393_0.10567629667551381", "host": "s3://npm-registry-packages"}}, "4.0.0-11": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.0-11", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.0.0-11", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "7700d703b1dc82fd5e78623e02b5450c75af4375", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.0-11.tgz", "fileCount": 3, "integrity": "sha512-XioucLV16m1PR6FVxIva8t9k1UpqCqs+w7QGjs5pIp4qGMLTtW8U1HqYdfIh7O8Hn1qxYTrG1osT4rLJX8kc7g==", "signatures": [{"sig": "MEYCIQC5fIFVkHaCgoi52+jPr8ekEWsuHkHF3PNDXiL1xTSHQwIhAIvTBgeiqsBT+ifszBNsUha+nzjVMWOD5UceuQRTfHy1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3148340}, "main": "./rollup.win32-arm64-msvc.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "3fc8b18da06fc76c386527cebadec4d8936b0f7a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.0.0-11_1692785747614_0.24166119912177608", "host": "s3://npm-registry-packages"}}, "4.0.0-12": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.0-12", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.0.0-12", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "76d66a2da668bafae61fe9a492bf881750603a48", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.0-12.tgz", "fileCount": 3, "integrity": "sha512-CAnKKJkAKIHR0yK/fFpXtb4EP0gTkXtV8l0PpLVdZmucJl5vK7aHpsGorq+FZ+9RiYU6MBorxdU/Uqzf/+Akaw==", "signatures": [{"sig": "MEYCIQDMlYAfkkY+F9+IUOQ3gtVt6+kk61O+YphTw3fpNNCdiAIhANgt8AbvL1IM+Z9owp6Y9tr4j2a06kH5Qeu0LruIa19q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3148340}, "main": "./rollup.win32-arm64-msvc.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "b6eec18d711348e3b177ef58dc2836cdf75e0432", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.0.0-12_1692801621887_0.1654772356158989", "host": "s3://npm-registry-packages"}}, "4.0.0-13": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.0-13", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.0.0-13", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "d2e12c4fb51c9ade7ed738fb84f08161276c4bfd", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.0-13.tgz", "fileCount": 3, "integrity": "sha512-ZMLLhPv6lrlPtBTCB6WI0DFZkNTusuEO114Vou+ZcJTKvRP2gdrDzntXBTNfiph+Lgju0mrHH7Mal8Qlf/1Pig==", "signatures": [{"sig": "MEUCIQDyYp8Cuqz2Yjdovmbwz2uR+L8wK31FNdz1S9RadT4R9QIgWabxZxFd9wRXZen8PpkmQiBPHVYnDN4VdPJn6N7ykGA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3112500}, "main": "./rollup.win32-arm64-msvc.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "07d3baeb218f6d1084e9d1b17a429ca84cb92561", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.0.0-13_1692892112465_0.20887324354676973", "host": "s3://npm-registry-packages"}}, "4.0.0-14": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.0-14", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.0.0-14", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "008045105bcab665f22c029ab8622cb8b48a94a3", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.0-14.tgz", "fileCount": 3, "integrity": "sha512-0m6xFB7qd0WYLUGvHcZeZ7LBVoLegXaMssD0GZ2RSeOwJjkRnRagJgsm+bJN07H3INshVC9zPlrJcgBG4v26dQ==", "signatures": [{"sig": "MEUCIHPQ9kDUv4Gw1OGltZyJBpKNMzWMYL/Ccn6QkTOT4SrvAiEAh0+H0QR05sefH3nzb2W3UM6d4iN7VZ1aXKVLNzcLGRQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3127860}, "main": "./rollup.win32-arm64-msvc.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "ec2f8ec863d8d896aef0dd0097f2d73f59e8213a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.0.0-14_1694781264646_0.9267406390701576", "host": "s3://npm-registry-packages"}}, "4.0.0-15": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.0-15", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.0.0-15", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "d3ca1dc364ef0f8a1faa5f982b514d04c99918df", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.0-15.tgz", "fileCount": 3, "integrity": "sha512-vPH0NYQbt3D8/tv1QiOwFgdD8qaGAyHJDmh3m4FpDUdaWElIvPgnHqSz3SVLkCSJrBfAfBuwteXt8LqHORt94g==", "signatures": [{"sig": "MEUCIQDrGYLyxVziEXvTCY6d4SUxd4NAh/BvuNb3AsBq6vnkbQIgHGIfftYfff/HVhQqjkLM8RREAvDEoPLgCVB6HKIdAU8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3127860}, "main": "./rollup.win32-arm64-msvc.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "6e6186636ebb169611373a0e430853eb3b6ce8e0", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.0.0-15_1694783211801_0.5260590330212929", "host": "s3://npm-registry-packages"}}, "4.0.0-16": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.0-16", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.0.0-16", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "7f990c044b77597d39d266fddab420987e70202f", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.0-16.tgz", "fileCount": 3, "integrity": "sha512-pEanYAzKkT1qPn6375AhIyPnpOG35R+3wDboyiXPsLuynAmLjUc1YiPNtIwvCGteNAFWS8iGwof3j0jJBwHTjA==", "signatures": [{"sig": "MEUCIQDnSzRGMeizeJb8FaedTx0ehWuBAM9ziDYUCfTHhehA8QIgA5HYRD30MCn/w1ASh0yl/uNQMa0CiBF1k/NXjEVf5zY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3127860}, "main": "./rollup.win32-arm64-msvc.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "fd025bcfab85bdecba183367d11c13a1f99c4f10", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.0.0-16_1694787434317_0.884666219383546", "host": "s3://npm-registry-packages"}}, "4.0.0-17": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.0-17", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.0.0-17", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "1b24a2a758aa8a01475edd88f16303d821bb4bdf", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.0-17.tgz", "fileCount": 3, "integrity": "sha512-9B730yqHhwEvlwZKmkhjXU9Tmzprd0Mxw4s2FBD5XBT2boNIhVm6qWm2FfItwyuahHiXABZxZ2tgMG4eImAKJw==", "signatures": [{"sig": "MEYCIQDI+R54IgQ7PiGvAA3mpZFk4dhCNe2Nf3rxTrU3qh9BfAIhAI5enSsddHzmYfQ1aTYa+dfqf9jYtzkRZO/3q3GprOlb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3127860}, "main": "./rollup.win32-arm64-msvc.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "f7eb39f003eaa325451091faec04dd51d774ae3b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.0.0-17_1694789945341_0.07104057242221984", "host": "s3://npm-registry-packages"}}, "4.0.0-18": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.0-18", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.0.0-18", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "7056b7903c8290a07238aa1416cc38afee63c099", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.0-18.tgz", "fileCount": 3, "integrity": "sha512-a2IAxT9MzKpqi30jkxL5rRuaSKlxr4a1LaY6UoN75Vclx+luWarRo9DVCPiAEzmosVDrWJWSvsWsNcMPL5wn4Q==", "signatures": [{"sig": "MEYCIQC3hyXcm+pqmaVgeEP7OjgnVQHfkBe8QQQRThlHOWyvIgIhAN3j5ju6bSlT6O4RzBogUPGOCPUwWDLvQKizk0+l6D6L", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3127860}, "main": "./rollup.win32-arm64-msvc.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "646171ff58e4f31127714ff8c78868c79b77d596", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.0.0-18_1694794196912_0.6857637771201748", "host": "s3://npm-registry-packages"}}, "4.0.0-19": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.0-19", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.0.0-19", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "9523dab0ff53e8367f4ede40fdc00889ba2a1ff4", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.0-19.tgz", "fileCount": 3, "integrity": "sha512-/XykBfL7naa1xS19Uj9LdkkzyXxFniRVHfsfGnI9AxzmZPz32hJVFdT5nLU86UixmHaJPt+xydwacnZ6yxs+Bw==", "signatures": [{"sig": "MEUCIGUMaGCFobi24aqCRUR/ZY26ebm8L4wmL04T+Tl6ULE/AiEAiJGdQsyTNCVe5GfBb2+1S6eZJDxKMQ4ebPn9juoe3Vo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3127860}, "main": "./rollup.win32-arm64-msvc.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "25753ad04d73429f0d7b4d5dc85df09aeae78485", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.0.0-19_1694803856166_0.5905525803763616", "host": "s3://npm-registry-packages"}}, "4.0.0-20": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.0-20", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.0.0-20", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "aa61a4efa56f31f8ce9e9d4c5dd80572dfaaf484", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.0-20.tgz", "fileCount": 3, "integrity": "sha512-4lh051uRJPTKXjqRaW3zWlYKuR0RmI+bssKgaEQkO0crh6Bzv4aG2qToDe4nT43CLU5TZT1LY1z7DihgANWvvQ==", "signatures": [{"sig": "MEUCIQDKrzFGYZ1gWlBcwR+AdYLuej818XSSnHXnrWk4cDsKvwIgcf7ZFV2FcsB79oCmuwsgp6E28oowsy9tQxzQrZaXxlQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3119602}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "9d6dc574c6dca3d85e9eda512b09797a6d15462f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.0.0-20_1695535836769_0.767428283922363", "host": "s3://npm-registry-packages"}}, "4.0.0-21": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.0-21", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.0.0-21", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "7da95939af0b32a66c2aad991f3f89c3fc0812c8", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.0-21.tgz", "fileCount": 3, "integrity": "sha512-fjHgYLZrSrY8N4jut6SpA2hpX+2MCqZnprjJd3fIBxoarkJ1+unN+PYTwfQAmz/7bOCb+uVdWPLm5ywjPS4SGg==", "signatures": [{"sig": "MEQCIHxUvQ6m+VtmtdGJKWnVRzegn24WWPP8eNn3g339VUARAiApejQwjYt/K7/XQVVSl6Fk16S8cbuGAMHWkoNMPaA1yA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3119602}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "fa868ad975b9ae6007ddc64b1a9e82766de6fa9e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.0.0-21_1695576143058_0.47884103232151065", "host": "s3://npm-registry-packages"}}, "4.0.0-22": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.0-22", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.0.0-22", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "77a56539f65e812832facc9a9e96ba6aa6f538b6", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.0-22.tgz", "fileCount": 3, "integrity": "sha512-I5tzD1RqkJyiSrOpWwURFh2WQeICfgX+s0ZPmG2TEpo8fAQbWmNacoiEmmaotaXo7D6gCQPTMuvNpFXkr0zUhQ==", "signatures": [{"sig": "MEUCIQD65RiWyqxsDlebFqL7V0YaeaxlOQq832SBQVVQM5v3tQIgF4mhxp96kah35J6yT7zYYYfJxjjZ6BpxpqOPAvebsx0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3124210}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "38be49cf19099321f935c1ad5968e76fb30e0957", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.0.0-22_1695745037942_0.23863511964677087", "host": "s3://npm-registry-packages"}}, "4.0.0-23": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.0-23", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.0.0-23", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "11f720784a91e69c9deb2229d626e35d7ee1bafb", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.0-23.tgz", "fileCount": 3, "integrity": "sha512-pEWZzHhioEt8HrM5PV7YiDbPmrZ8ceWtmoL/7xhvOgQ4xD3SCr5kych1gv1YKuWxLNKVl2S3SanARSvPQVcdog==", "signatures": [{"sig": "MEUCIBjTVOCNsHjagwzzTxugg8rWYoBTu7XzM2mG+tep0ceDAiEAkpyN+utMW02W//B7mk72C8nudZvHm7rR1ejz1a68pqU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3124210}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "f1d93caae901c556ffb1e2f553428754038d65c1", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.0.0-23_1695759261741_0.5669274319164077", "host": "s3://npm-registry-packages"}}, "4.0.0-24": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.0-24", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.0.0-24", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "c6ad6b8c9bad415ae7fc6077597618eb6af0a3ea", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.0-24.tgz", "fileCount": 3, "integrity": "sha512-xT8djNMxVB84cCm1XILpQXkMcu8S+GBKjurXM4sc6eB1FQpFpcTAOsuQSg9xOhfPqm1xa7qqXA6ZpUhoUMboVQ==", "signatures": [{"sig": "MEUCIQDbZslG++SqaICvaMxct80dseuTDqfulvAqEypwpOSZeQIgTt8t5KkkwA23w8/OThFtgMAOIgCrWobA3h3yrAAN+rg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3309554}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "ced077f2920c473c4c2ca31a8d72b259bec91f67", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.0.0-24_1696309964747_0.8008757789154648", "host": "s3://npm-registry-packages"}}, "4.0.0-25": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.0-25", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.0.0-25", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "ae42aec04841626b2e458c2f75b48eabd62a741d", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.0-25.tgz", "fileCount": 3, "integrity": "sha512-3iXMflVqSCDjmuYKwHllmS9nLLF9qKiQB5zeZDyhlvsV2QDIqdu6hb5Nv55rmR1zGEfYVSszOL7RGtCVgbecaw==", "signatures": [{"sig": "MEYCIQCKg19jGIUZwbpdJAcRyfRfCTTA2g+6pWDamVuDc9C0JgIhAL6RisFbNzOZ7jrftnx/inB9daAzfBNp8cI5KgODBPSA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3306482}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "1ac6bbc437c7ed0de3ad23e4e0904f00783e703d", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.0.0-25_1696515163112_0.041415107232930515", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.0.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "98cab6ea9398adbe07573acc22a69661f1b22a67", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.0.tgz", "fileCount": 3, "integrity": "sha512-h2r04SsqVMbmaIRSMN3HKQLYpKewJ7rWQx1SwEZQMeXRkecWFBBNOfoB3iMlvvUfc3VUOonR/3Dm/Op6yOD2Lg==", "signatures": [{"sig": "MEUCIQDfwgAvvwoqg6qO10BWbvk9PWiP4CXGNGjaMv7RQlJG8AIgVzUXbL+DUNBwEuxLg/qyhVtPqL/Mt9giSTvU+4iOObs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3306479}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "2f261358c62b4f9e62cb86bf99de8d4ff3668994", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.0.0_1696518869901_0.6416132453333874", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.0.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "d17f3f54886f38490c24eb22d465ae3cc6c7feb2", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.1.tgz", "fileCount": 3, "integrity": "sha512-AJ0NKL2X/eMq2ED7APz1zDrf70sqIJPdtD1zqgGnqKN0ipn8iDQCiNF9xRolx7ImH6CF+jA1n3/VzgD4a8eVNA==", "signatures": [{"sig": "MEUCIQC784qTiXlqVKH5LnNEJUu7doHJQk60+GQOql8k1/suUwIgemzPIPoK9tzzqoMp7WjQyQJTud5rnGM82/Nh/rY9wZ0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3256303}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "fcab1f610fefb24621ce001dfb0831dd30e59ab3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.0.1_1696595798645_0.35549581832993615", "host": "s3://npm-registry-packages"}}, "4.0.2": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.0.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "853acc6ec7eb573fba1161c5707d7e1d1c12b3b0", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.2.tgz", "fileCount": 3, "integrity": "sha512-ZkS2NixCxHKC4zbOnw64ztEGGDVIYP6nKkGBfOAxEPW71Sji9v8z3yaHNuae/JHPwXA+14oDefnOuVfxl59SmQ==", "signatures": [{"sig": "MEUCIHGwpN87+Sq++qxeVi1y6mtBIZADrfCX9jUg59zqKkfQAiEAuch0bRHTQJ/t+BNtRTl7f1LEdTPNFc7hHCYgpcl0reA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3256303}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "3d9c833c4fcb666301967554bac7ab0a0a698efe", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.0.2_1696601919707_0.5564613271055543", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.1.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.1.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "09563571c801091472552f2413eef47759679d8c", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.1.0.tgz", "fileCount": 3, "integrity": "sha512-7lqRlilcPpA88eCtFxl7zaeRH4nZlPy/qECsrRe9JQzqANa3wPYmjgwLQbduN8PuZ9dQ6HD09BFm7v+Pc0dNTQ==", "signatures": [{"sig": "MEUCIQDv5wuAGzIAlp0/rEbmJzcUq7deAlPYwTWCIc2CILYmxAIgMOtbkeUK/PvNoTaGNhDqKO458Q86+pze5cudAU5fKL4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3365871}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "cb144b2be4262b3743b31983b26f7fa985be3ceb", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.1.0_1697262732650_0.032722337828594794", "host": "s3://npm-registry-packages"}}, "4.1.1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.1.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.1.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "af47c951ebd197931953df735ae4fbb0d77ffce5", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.1.1.tgz", "fileCount": 3, "integrity": "sha512-q04G+SLoMmLewIHc2ZVrsAzskXRXnzc5lUqXpOvwzgBSG+cBI1aBd+sLCGiOWezwMvcoa9yiPrkg+s5t0cZqSQ==", "signatures": [{"sig": "MEUCIEVcXB35CSJhDpOm1a93q9sLit0nBjJEXwoCdcmfnr7zAiEA/aTsMkMbEot/n206DjOh9S+OETbnTXo+R9TxRkvRHvM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3129327}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "d8b31a202a246758b8d67eefe77361a894d37005", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.1.1_1697351506475_0.5721399132761051", "host": "s3://npm-registry-packages"}}, "4.1.3": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.1.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.1.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "f65b2112e3fd581895d1572e278a74eeefce24e2", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.1.3.tgz", "fileCount": 3, "integrity": "sha512-rTxOCH2ZLWkbZJh621qNmS4zDjvIOnvwXdd0Zvkm8twvVoyWATbn6q/bI3bXjQeV7mEASXU1atUWzdeovTcrrw==", "signatures": [{"sig": "MEUCIQCXPxsKvigh98tZWdc+6eMoGpAKpkIJGEGdGqvq0I7TBgIgTpSFQOuI9ChcghfPWCfpdB0hwnuCbzR/kSPQYF/Dwto=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3129327}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "c61a1507a88fc71be431550642b040da4b9422b0", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.1.3_1697392105066_0.8369470864283883", "host": "s3://npm-registry-packages"}}, "4.1.4": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.1.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.1.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "acb619a959c7b03fad63017b328fa60641b75239", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.1.4.tgz", "fileCount": 3, "integrity": "sha512-ZY5BHHrOPkMbCuGWFNpJH0t18D2LU6GMYKGaqaWTQ3CQOL57Fem4zE941/Ek5pIsVt70HyDXssVEFQXlITI5Gg==", "signatures": [{"sig": "MEUCICLC/SgfmQ+tBZqCJEAeh9FlRCwaNk7MTqqJgTotJWmeAiEA7urtXgwwcOSQSWB78L7PsXfOv8SB/Q8nYrZDt7lTPKI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3097583}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "061a0387c8654222620f602471d66afd3c582048", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.1.4_1697430847834_0.18828884575988836", "host": "s3://npm-registry-packages"}}, "4.1.5": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.1.5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.1.5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "d4a674b472b0a31d8a8f9406b79933a6ccdb9efb", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.1.5.tgz", "fileCount": 3, "integrity": "sha512-5oxhubo0A3J8aF/tG+6jHBg785HF8/88kl1YnfbDKmnqMxz/EFiAQDH9cq6lbnxofjn8tlq5KiTf0crJGOGThg==", "signatures": [{"sig": "MEUCIQD01hKCwUUFZSg9XkEvij1DpHDdvSNdQAcg7kCbUqL+aQIgeWWgq+nhecouL+MPStARPp9oBKWCM1oGFI/QYZ26ReU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3116015}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "1cbb382b0dd3ab70541671c105f96eff283904ec", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.1.5_1698485009646_0.7315548184835701", "host": "s3://npm-registry-packages"}}, "4.1.6": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.1.6", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.1.6", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "7ec7aa0d21395d8e54c8c67bb7b7a0bcb2990f62", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.1.6.tgz", "fileCount": 3, "integrity": "sha512-lEpjGR/ZeX1fjj2ho9p5OT9Xc+egePdpB/S7ZPjjct9GctVpHgwfbxARAOMiCuZ8/fWkTERg+4qs1MG7Dm8cNw==", "signatures": [{"sig": "MEUCIA+qFSDr/3mM4MmNzgEIqHn2wXQETieNAAa4Yw03KEAPAiEAoRNpqKGq0BXFuBLwRAsEXPqtca8uwdl4mk1Zl9QCa0c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3116015}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "5901e545697b36326110d89ed02964fdaffd9f6f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.1.6_1698731112588_0.3764098072632913", "host": "s3://npm-registry-packages"}}, "4.2.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.2.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.2.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "0047005faab99cdea50b594f76388ed09ed44650", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.2.0.tgz", "fileCount": 3, "integrity": "sha512-vYxF3tKJeUE4ceYzpNe2p84RXk/fGK30I8frpRfv/MyPStej/mRlojztkN7Jtd1014HHVeq/tYaMBz/3IxkxZw==", "signatures": [{"sig": "MEUCIFQvJCiFzwJPiC+ozqJNmKDAaPMWZMnd6XnLLpcdVNquAiEAoN3duslYItSRt339m5vdIIp6MT4mHgkPGfnhMQ73a40=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3131887}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "fbf806aceffd822d43e4603b664c54165c72cf36", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.2.0_1698739838769_0.8410763806868988", "host": "s3://npm-registry-packages"}}, "4.3.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.3.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.3.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "1a36aba17c7efe6d61e98b8049e70b40e33b1f45", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.3.0.tgz", "fileCount": 3, "integrity": "sha512-OKGxp6kATQdTyI2DF+e9s+hB3/QZB45b6e+dzcfW1SUqiF6CviWyevhmT4USsMEdP3mlpC9zxLz3Oh+WaTMOSw==", "signatures": [{"sig": "MEUCIEokxloOUM8UusagTzidU7KL2KJ+OkEQMOKqPw4L3an9AiEAvYewucc85P/5EJFK+8u6SD3sKkK9COGvAxK/2YOshMo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3124207}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "937d9911376574c42f893e1cd14b55418c4f7b68", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.3.0_1699042380883_0.18505058874675906", "host": "s3://npm-registry-packages"}}, "4.3.1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.3.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.3.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "fae8d013b3b8ea75a7a37fa82c89ac0243bbf07c", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.3.1.tgz", "fileCount": 3, "integrity": "sha512-aYKKmlrLL7C0oY43B2Q4uMIlfF1BsSlSYf3R7q7SGB/SrK7Tkj2DHuxqBSYuFqSxuYuAP4PaHt230McvMpZg5A==", "signatures": [{"sig": "MEUCIQD/NDrLnINWGvLaJH0v79Kc0ppCxItQ2fC9tYqkKJU06wIgXEWHMmg3FTKhJShONN+nQdAWVFSazslRN2SNU5fVgDo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3206127}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "52c55bb1e17154ae6d01fb40e0e4a3589bc20a8f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.3.1_1699689473122_0.2777425092835788", "host": "s3://npm-registry-packages"}}, "4.4.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.4.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.4.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "acabac95fff647d666a5d649909612aad72bd3d0", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.4.0.tgz", "fileCount": 3, "integrity": "sha512-jnoDRkg5Ve6Y1qx2m1+ehouOLQ4ddc15/iQSfFjcDUL6bqLdJJ5c4CKfUy/C6W1oCU4la+hMkveE9GG7ECN7dg==", "signatures": [{"sig": "MEYCIQDYCO4J6i6QUVbjK+0/GICfnKeBefu8fc/mXX6IuoiphQIhALE5x/SS0g68ZoPofvVtrp09/uIfN3k5YnOy5qamOgI1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2856431}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "53d636051ac60da9b302c4bd6b7eaaccb4871f4b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.4.0_1699775392871_0.2329971354979481", "host": "s3://npm-registry-packages"}}, "4.4.1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.4.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.4.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "0f0d0c6b75c53643fab8238c76889a95bca3b9cc", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.4.1.tgz", "fileCount": 3, "integrity": "sha512-Hdn39PzOQowK/HZzYpCuZdJC91PE6EaGbTe2VCA9oq2u18evkisQfws0Smh9QQGNNRa/T7MOuGNQoLeXhhE3PQ==", "signatures": [{"sig": "MEQCIBeR8aLAxHghcRjQUdEycrEe9QeXWcRhIE/dZG8RENgwAiA7ImVMSroKuXHY4d/vsBMB0FEIQNHFPNWhSZtPRPDEmw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2856431}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "01d8c9d1b68919c2c429427ae7e60f503a8bb5f4", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.4.1_1699939507955_0.15938605462077038", "host": "s3://npm-registry-packages"}}, "4.5.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.5.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.5.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "f145f10c33aa187a11fd60933465be46667e6e42", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.5.0.tgz", "fileCount": 3, "integrity": "sha512-xaOHIfLOZypoQ5U2I6rEaugS4IYtTgP030xzvrBf5js7p9WI9wik07iHmsKaej8Z83ZDxN5GyypfoyKV5O5TJA==", "signatures": [{"sig": "MEYCIQCGwKxLWz5+L8VzxFhqgzYyZ5uI4thq2CYqO1jzcPVivQIhAPGzYPtkHa/rbKd0pfb6WoZezI5ahc7lFCWAsw1K5ycz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2836975}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "86efc769f693516a29047c8d160c6d7287fb965d", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.5.0_1700286730847_0.7552241614908168", "host": "s3://npm-registry-packages"}}, "4.5.1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.5.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.5.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "df70597f6639549e79f0801004525d6a7a0075e4", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.5.1.tgz", "fileCount": 3, "integrity": "sha512-ihqfNJNb2XtoZMSCPeoo0cYMgU04ksyFIoOw5S0JUVbOhafLot+KD82vpKXOurE2+9o/awrqIxku9MRR9hozHQ==", "signatures": [{"sig": "MEUCIQDPnccC3dPtIxnKZl/z7NMDBmdGXOYRMefbrn1VHtUlhQIgXllrRgsyep2ngf90/iuvhrqbfcAZqoMD+t2dH786BWA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2837487}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "a083019c7f0c18a1c17260ab1239b12400984a88", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.5.1_1700597588065_0.6444298684692589", "host": "s3://npm-registry-packages"}}, "4.5.2": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.5.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.5.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "b880eb1d349f166939ffbe27cbf1efd2e1923819", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.5.2.tgz", "fileCount": 3, "integrity": "sha512-NCKuuZWLht6zj7s6EIFef4BxCRX1GMr83S2W4HPCA0RnJ4iHE4FS1695q6Ewoa6A9nFjJe1//yUu0kgBU07Edw==", "signatures": [{"sig": "MEQCIGvlbXRVR0SQPirg67h2mrLz348C6hogGWlkYYgu3w0fAiBHxxgp1cZ+Ie28TvHzYHUdhpj38wZ1C132eSi1hT/n0g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2830831}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "2e94641971195c1a4eb9e1a3fe6d73b9d04ffae0", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.5.2_1700807386420_0.8098900131191715", "host": "s3://npm-registry-packages"}}, "4.6.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.6.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.6.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "9a7bfc660ac088d447858fc5223984deb979a55a", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.6.0.tgz", "fileCount": 3, "integrity": "sha512-+MRMcyx9L2kTrTUzYmR61+XVsliMG4odFb5UmqtiT8xOfEicfYAGEuF/D1Pww1+uZkYhBqAHpvju7VN+GnC3ng==", "signatures": [{"sig": "MEQCIAKt3KnGQJu8Cj5q5gR08KpDiKpKQtssus+epGSoO3EFAiB/syb3xnp3lLPG04eNP48LLBsbN/Z1PAaRWOFAw1RbOg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2830831}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "020774d0c7b1371865b20878e59dd3a6a45d3b31", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.6.0_1701005952743_0.9538580827339687", "host": "s3://npm-registry-packages"}}, "4.6.1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.6.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.6.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "b6e97fd38281667e35297033393cd1101f4a31be", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.6.1.tgz", "fileCount": 3, "integrity": "sha512-v2FVT6xfnnmTe3W9bJXl6r5KwJglMK/iRlkKiIFfO6ysKs0rDgz7Cwwf3tjldxQUrHL9INT/1r4VA0n9L/F1vQ==", "signatures": [{"sig": "MEQCIB15fD7gFfT7odtMOiO9caqsdpF6fZSZJmX/mGOkrnk9AiBG7dYMTrw5v5hFSsmLZBj0f9+R/Ssgv9+i+7igqH/Jng==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2830831}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "ded37aa8f95d5ba9786fa8903ef3424fd0549c73", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.6.1_1701321785710_0.8420280506332605", "host": "s3://npm-registry-packages"}}, "4.7.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.7.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.7.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "95902325d07919e25dff32be9428acbc1b889101", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.7.0.tgz", "fileCount": 3, "integrity": "sha512-CPtgaQL1aaPc80m8SCVEoxFGHxKYIt3zQYC3AccL/SqqiWXblo3pgToHuBwR8eCP2Toa+X1WmTR/QKFMykws7g==", "signatures": [{"sig": "MEUCIFsbLz31d7YcyYKH6XiHQH70Esw5UTJe7h23Pqries/FAiEA3iatqMDRj31RJeGX7xuFrHjJO81/GASe2WGsnJFfPx8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2862063}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "098e29ca3e0643006870f9ed94710fd3004a9043", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.7.0_1702022279188_0.3407015462192724", "host": "s3://npm-registry-packages"}}, "4.8.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.8.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.8.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "7cce8efc5c9239a1bafe7ac2a52743bc5734471f", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.8.0.tgz", "fileCount": 3, "integrity": "sha512-ge7saUz38aesM4MA7Cad8CHo0Fyd1+qTaqoIo+Jtk+ipBi4ATSrHWov9/S4u5pbEQmLjgUjB7BJt+MiKG2kzmA==", "signatures": [{"sig": "MEQCIFUqKXjkSZQ21745dMdkCZt/uzgrK/3o/RioGqNglQ34AiBO8i8VmGyJWZREmRexTmc0cSKNCgAygYBC2r8Z6jeCCQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2862063}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "62b648e1cc6a1f00260bb85aa2050097bb4afd2b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.8.0_1702275892214_0.5193412874197734", "host": "s3://npm-registry-packages"}}, "4.9.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.9.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.9.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "bf2dbad350376e46cb77fab408bb398ad5f3648d", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.9.0.tgz", "fileCount": 3, "integrity": "sha512-9jPgMvTKXARz4inw6jezMLA2ihDBvgIU9Ml01hjdVpOcMKyxFBJrn83KVQINnbeqDv0+HdO1c09hgZ8N0s820Q==", "signatures": [{"sig": "MEYCIQDBWL+Ems7xH8OEbAZxdFCxc4WPowJSIxBDEvQrUKgMDwIhAJc3cQVJzzy1EEukOrwKnLHsngT6UwyhRElbTQTfH4Xo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2862063}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "c5337ef28a71c796e768a9f0edb3d7259a93f1aa", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.9.0_1702459455579_0.45836551948245696", "host": "s3://npm-registry-packages"}}, "4.9.1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.9.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.9.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "5bebc66e3a7f82d4b9aa9ff448e7fc13a69656e9", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.9.1.tgz", "fileCount": 3, "integrity": "sha512-7XI4ZCBN34cb+BH557FJPmh0kmNz2c25SCQeT9OiFWEgf8+dL6ZwJ8f9RnUIit+j01u07Yvrsuu1rZGxJCc51g==", "signatures": [{"sig": "MEUCIQCQHyw/O8qmEFUnT7LfyHn8LHq1YsgcSlSlXomOzUkVfQIgTfFwLyxKgEgqiiIZ/q8HuqLjU38GkICbDxnv2Dcd/yw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2854895}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "d56ac63dc0452820272a0d7536340277f7db68bf", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.9.1_1702794369798_0.6117636176861649", "host": "s3://npm-registry-packages"}}, "4.9.2": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.9.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.9.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "60152948f9fb08e8c50c1555e334ca9f9f1f53aa", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.9.2.tgz", "fileCount": 3, "integrity": "sha512-x1CWburlbN5JjG+juenuNa4KdedBdXLjZMp56nHFSHTOsb/MI2DYiGzLtRGHNMyydPGffGId+VgjOMrcltOksA==", "signatures": [{"sig": "MEQCIEUYbO4Wfg/HtEOI/WZthGdEHHSXpx2SIDPximqd0/W6AiAqP/ROe1DrNPCmudStv/7MUwGyOgSdjDHBzNOLTOldjQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2858479}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "347a34745b2679c1192535db3c0f60889861d3ad", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.9.2_1703917406544_0.7609767309538868", "host": "s3://npm-registry-packages"}}, "4.9.3": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.9.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.9.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "53fafdaca77027c12171a60c27ca249cf981a4b9", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.9.3.tgz", "fileCount": 3, "integrity": "sha512-z5uvoMvdRWggigOnsb9OOCLERHV0ykRZoRB5O+URPZC9zM3pkoMg5fN4NKu2oHqgkzZtfx9u4njqqlYEzM1v9A==", "signatures": [{"sig": "MEUCIQCBNTcgm+DtyHV50HQcaoDl2L7H026tr+gN6dQFQq1/lAIgDRHJ/Sfbg1/IpvtoAq+84BmmXyzQTufu8z7/lEBYdBk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2864111}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "4ab3ad360457cd79f4ea852447d3ddca22da95d6", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.9.3_1704435645591_0.40080545631007025", "host": "s3://npm-registry-packages"}}, "4.9.4": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.9.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.9.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "95957067eb107f571da1d81939f017d37b4958d3", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.9.4.tgz", "fileCount": 3, "integrity": "sha512-T8Q3XHV+Jjf5e49B4EAaLKV74BbX7/qYBRQ8Wop/+TyyU0k+vSjiLVSHNWdVd1goMjZcbhDmYZUYW5RFqkBNHQ==", "signatures": [{"sig": "MEQCIEWb2weDPcJfPbX6lEfHRormKVLaGx6uvKH4HjdaOvfuAiBJaNJjn8H8Wus8Ef0nhG1OWP1hJNdpbS8d9tHykh9Pag==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2864111}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "18372035f167ec104280e1e91ef795e4f7033f1e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.9.4_1704523138843_0.8850386814122175", "host": "s3://npm-registry-packages"}}, "4.9.5": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.9.5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.9.5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "422661ef0e16699a234465d15b2c1089ef963b2a", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.9.5.tgz", "fileCount": 3, "integrity": "sha512-aHSsMnUw+0UETB0Hlv7B/ZHOGY5bQdwMKJSzGfDfvyhnpmVxLMGnQPGNE9wgqkLUs3+gbG1Qx02S2LLfJ5GaRQ==", "signatures": [{"sig": "MEQCIB4WVwAhsiZcBrlcJlKuF0/idj2qudvAx78U2MED6PmwAiBEV+wOREBwxM0ZP+oJ55gfUlYm5m8fnMtMqMKw4vH4JA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2847215}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "7fa474cc5ed91c96a4ff80e286aa8534bc15834f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.9.5_1705040172465_0.03090970766433432", "host": "s3://npm-registry-packages"}}, "4.9.6": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.9.6", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.9.6", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "1cc3416682e5a20d8f088f26657e6e47f8db468e", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.9.6.tgz", "fileCount": 3, "integrity": "sha512-VD6qnR99dhmTQ1mJhIzXsRcTBvTjbfbGGwKAHcu+52cVl15AC/kplkhxzW/uT0Xl62Y/meBKDZvoJSJN+vTeGA==", "signatures": [{"sig": "MEYCIQCc6t9XqxBXw3nAogNj9Uo0sGXTb1R4gXdah58mdolwmwIhAOoBFI5oPw6exMLWoS1//7nVP6MccKJIZN1vbr5lr31m", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2871791}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "ecb6b0a430098052781aa6ee04ec92ee70960321", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.9.6_1705816337858_0.9481162215041194", "host": "s3://npm-registry-packages"}}, "4.10.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.10.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.10.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "6ad0d4fb0066f240778ee3f61eecf7aa0357f883", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.10.0.tgz", "fileCount": 3, "integrity": "sha512-NrR6667wlUfP0BHaEIKgYM/2va+Oj+RjZSASbBMnszM9k+1AmliRjHc3lJIiOehtSSjqYiO7R6KLNrWOX+YNSQ==", "signatures": [{"sig": "MEQCIFy0RUQ9sc4ULIOPmnTbrb7Bo4awUL7XZ1KjHA6jr+/gAiB6am/X2iaAjWIE5iYxcHlL3+53G9MZcD2JDTfm55cRWw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2950640}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "762420860765e8e46e24d48b38f5b98ca31735fa", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.10.0_1707544720261_0.8058607284155408", "host": "s3://npm-registry-packages"}}, "4.11.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.11.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.11.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "18e49376786def75843e605bdc8059a301c11dad", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.11.0.tgz", "fileCount": 3, "integrity": "sha512-LVXo9dDTGPr0nezMdqa1hK4JeoMZ02nstUxGYY/sMIDtTYlli1ZxTXBYAz3vzuuvKO4X6NBETciIh7N9+abT1g==", "signatures": [{"sig": "MEQCIA/qcDB7qACamZD4AfL0NpKmfYqNn/lGU3eGNfrT/AY2AiAb8ETHApgMEBeyCysUF/vAnTAYhEsp1V7ri3ppfmzYqg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2950640}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "90ad652b745c5fe7167d92b4ad671cc387577a99", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.11.0_1707977376542_0.6897934836840123", "host": "s3://npm-registry-packages"}}, "4.12.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.12.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.12.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "68d233272a2004429124494121a42c4aebdc5b8e", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.12.0.tgz", "fileCount": 3, "integrity": "sha512-JPDxovheWNp6d7AHCgsUlkuCKvtu3RB55iNEkaQcf0ttsDU/JZF+iQnYcQJSk/7PtT4mjjVG8N1kpwnI9SLYaw==", "signatures": [{"sig": "MEUCIHmQ150e/sH7lLVjloXZmv1Q1T//ESHyK3g7teiAzSSiAiEAoSR7rIyvXnhUYe/IbzZFrGoJ8COqX6NoVlUg8o8pSiw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2925040}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "0146b84be33a8416b4df4b9382549a7ca19dd64a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.12.0_1708090334292_0.34068323726685645", "host": "s3://npm-registry-packages"}}, "4.12.1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.12.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.12.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "27977d91f5059645ebb3b7fbf4429982de2278d3", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.12.1.tgz", "fileCount": 3, "integrity": "sha512-<PERSON><PERSON><PERSON>64bnICG42UPL7TrhIwsJW4QcKkIt9gGlj21gq3VV0LL6XNb1yAdHVp1pIi9gkts9gGcT3OfUYHjGP7ETAiw==", "signatures": [{"sig": "MEQCIEQhMshNi5zeJQEwpoC/Q5739VX09q40QDkZgJQb9P0/AiBtaSE/9ptp41HTQyAXID1UGmUXUwKxjZKghRd9zzZg5A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2961392}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "f44dac3170a671b0978afa3af43818617904f544", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.12.1_1709705012264_0.8075603002413951", "host": "s3://npm-registry-packages"}}, "4.13.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.13.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.13.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "8ffecc980ae4d9899eb2f9c4ae471a8d58d2da6b", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.13.0.tgz", "fileCount": 3, "integrity": "sha512-46ue8ymtm/5PUU6pCvjlic0z82qWkxv54GTJZgHrQUuZnVH+tvvSP0LsozIDsCBFO4VjJ13N68wqrKSeScUKdA==", "signatures": [{"sig": "MEQCID6eVE5rXxLYtG0bht4RTlCOYTO3n+6i3YKFq8+Rn6MfAiAi+TA/0sYW+dojYNnzPw0KAMsErPTvBRB/V4F+rha9uQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2970608}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "1c8afed74bd81cd38ad0b373ea6b6ec382975013", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.13.0_1710221311390_0.18954860526592743", "host": "s3://npm-registry-packages"}}, "4.13.1-1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.13.1-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.13.1-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "454e1da64c7bb528a3fba4015204381805e69faa", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.13.1-1.tgz", "fileCount": 3, "integrity": "sha512-ycwUDKpoaafBCk7QiiG0C24L1HAhzLaq6QeVzWJzGX6nPGsyFu0STYIxyaKwLVI/X5360c8EmebsTFZy6ORzNg==", "signatures": [{"sig": "MEYCIQCWTM+u+hV6k65s2YgeizQtxotvJ6NMrfW6wH9yupRLJgIhAPuMaCPiQriTwROVyPuE+u7/gn2J316xnVZ1zmFYpoyS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2961394}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "84797d177bee161df233644292bc8f128b989cea", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.13.1-1_1711265959720_0.5439210873367608", "host": "s3://npm-registry-packages"}}, "4.13.1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.13.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.13.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "6f9359bbec6cb4a2c002642c63e3704b0b5e68b7", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.13.1.tgz", "fileCount": 3, "integrity": "sha512-L+hX8Dtibb02r/OYCsp4sQQIi3ldZkFI0EUkMTDwRfFykXBPptoz/tuuGqEd3bThBSLRWPR6wsixDSgOx/U3Zw==", "signatures": [{"sig": "MEUCIElOi0XQE9KVkxIL3RBiEZM34t4od/SEyRYsC1qv52fPAiEAm4L2e/DnsXwFaTIcpaA23M4DsDHwg/7gfPu+jW3EGTI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2961392}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "fffaedeaa1cf9c8f6efc93d53bb8a81738e0ce87", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.13.1_1711535258199_0.6317919843056932", "host": "s3://npm-registry-packages"}}, "4.13.2": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.13.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.13.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "f8b65a4a7e7a6b383e7b14439129b2f474ff123c", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.13.2.tgz", "fileCount": 3, "integrity": "sha512-2YWwoVg9KRkIKaXSh0mz3NmfurpmYoBBTAXA9qt7VXk0Xy12PoOP40EFuau+ajgALbbhi4uTj3tSG3tVseCjuA==", "signatures": [{"sig": "MEYCIQCb257ZQhNptO0Tz6/RPIDPWc8k6WS9c+OocfN5ai9+uQIhAJDSIoQzK/tWCPOTeL7zHa28WpP/mHuopmHBgUmf6LtB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2961392}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "b379a592234416a2084918b0eea4c81865a1579f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.13.2_1711635213694_0.6253024408310945", "host": "s3://npm-registry-packages"}}, "4.14.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.14.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.14.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "269023332297051d037a9593dcba92c10fef726b", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.14.0.tgz", "fileCount": 3, "integrity": "sha512-Fq52EYb0riNHLBTAcL0cun+rRwyZ10S9vKzhGKKgeD+XbwunszSY0rVMco5KbOsTlwovP2rTOkiII/fQ4ih/zQ==", "signatures": [{"sig": "MEUCIQCMsTuHg05Jd/NfXMF1+abMgJStf5h3DJ8Hff2fzcc8AAIgaLj4dMnyGlWVKTZiJZNZ1ph+vL0jk2MNrhcgl6i+4z4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2944496}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "5abe71bd5bae3423b4e2ee80207c871efde20253", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.14.0_1712121770182_0.14500400962084847", "host": "s3://npm-registry-packages"}}, "4.14.1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.14.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.14.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "84d48c55740ede42c77373f76e85f368633a0cc3", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.14.1.tgz", "fileCount": 3, "integrity": "sha512-ryS22I9y0mumlLNwDFYZRDFLwWh3aKaC72CWjFcFvxK0U6v/mOkM5Up1bTbCRAhv3kEIwW2ajROegCIQViUCeA==", "signatures": [{"sig": "MEQCIGE4XrDVmFKEr90eklDLBtzu4MdDm/LnoPpoOqpAXDtoAiAprhni/A3kBrqa44v1q7i1FjYbLquyEqa8ZNuzTcZHCA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2937328}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "0b665c31833525c923c0fc20f43ebfca748c6670", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.14.1_1712475337811_0.34765709260683586", "host": "s3://npm-registry-packages"}}, "4.14.2": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.14.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.14.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "10f608dfc1e5bb96aca18c7784cc4a94d890c03c", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.14.2.tgz", "fileCount": 3, "integrity": "sha512-H4s8UjgkPnlChl6JF5empNvFHp77Jx+Wfy2EtmYPe9G22XV+PMuCinZVHurNe8ggtwoaohxARJZbaH/3xjB/FA==", "signatures": [{"sig": "MEUCIHqcrKR3khrY7Pwudl+hkh5u67/cx/4/wosWXxW2hP6XAiEAwHmdBUEz/uc16o9NEEB1/Lj8Ig4OPDZSV3t9NYY3hVA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2954224}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "7275328b41b29605142bfdf55d68cb54e895a20c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.14.2_1712903018041_0.00661428094615224", "host": "s3://npm-registry-packages"}}, "4.14.3": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.14.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.14.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "a648122389d23a7543b261fba082e65fefefe4f6", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.14.3.tgz", "fileCount": 3, "integrity": "sha512-+4h2WrGOYsOumDQ5S2sYNyhVfrue+9tc9XcLWLh+Kw3UOxAvrfOrSMFon60KspcDdytkNDh7K2Vs6eMaYImAZg==", "signatures": [{"sig": "MEQCIA5z2BEx3jVgQm28GdgLJ8ZyNAaVB+/L+xmRmQLli9vXAiBVETzz1yfPEcSiwUHJET95iYL479tzWeX6nP2I6qqXKQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2941424}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "e64f3d8d0cdc561f00d3efe503e3081f81889679", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.14.3_1713165510239_0.10114258887794225", "host": "s3://npm-registry-packages"}}, "4.15.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.15.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.15.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "95dae687b645a25aab3a082d987556f58274ffbe", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.15.0.tgz", "fileCount": 3, "integrity": "sha512-nEtaFBHp1OnbOf+tz66DtID579sNRHGgMC23to8HUyVuOCpCMD0CvRNqiDGLErLNnwApWIUtUl1VvuovCWUxwg==", "signatures": [{"sig": "MEQCIBUF+nivjj44VEZJehN1iQh7b42ckI6qEuWy0AuZFupmAiAVcqY2KZ2w8kFq/hzMZQ6Z5lJk57ApFf9sC0j062lAgA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3046384}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "e6e05cde31fc144228bb825c9d4ebba2f377075c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.15.0_1713591431348_0.5694270984601386", "host": "s3://npm-registry-packages"}}, "4.16.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.16.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.16.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "e9117384c4e340370777c5b5974c5cebf84c8807", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.16.0.tgz", "fileCount": 3, "integrity": "sha512-+wtkF+z2nw0ZwwHji01wOW0loxFl24lBNxPtVAXtnPPDL9Ew0EhiCMOegXe/EAH3Zlr8Iw9tyPJXB3DltQLEyw==", "signatures": [{"sig": "MEYCIQDImFVSJQ0BWusY/cfDhY4mAkm7PG/I6rc3CQBHe3RUywIhAJMqBbPITHC01HCQ0ti1cmYvwxtOqRuqMpFJvlB3dyAx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3046384}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "38fe70780cb7e374b47da99e3a3dca6b2a2170d2", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.16.0_1713674510435_0.1394710243424122", "host": "s3://npm-registry-packages"}}, "4.16.1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.16.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.16.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "af113ad682fc13d1f870242c5539031f8cc27cf1", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.16.1.tgz", "fileCount": 3, "integrity": "sha512-1vIP6Ce02L+qWD7uZYRiFiuAJo3m9kARatWmFSnss0gZnVj2Id7OPUU9gm49JPGasgcR3xMqiH3fqBJ8t00yVg==", "signatures": [{"sig": "MEUCIQDFkhINiSGS0DCKYsK3CXfZzDsVF/5ae4ygvMUuugstOAIgV/kwdYIqZkwM6MksWekk0jA0TS07SprC7nccZfTeWMM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3046384}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "5d8019b901e98cc8895751a23e5edfc9135b1a35", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.16.1_1713724196623_0.8925901025179472", "host": "s3://npm-registry-packages"}}, "4.16.2": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.16.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.16.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "edd352302e3fa6a2d612447590b0a0887cdbf762", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.16.2.tgz", "fileCount": 3, "integrity": "sha512-Wg7ANh7+hSilF0lG3e/0Oy8GtfTIfEk1327Bw8juZOMOoKmJLs3R+a4JDa/4cHJp2Gs7QfCDTepXXcyFD0ubBg==", "signatures": [{"sig": "MEQCIFO3ZjpSNY5t48utor1Nc+ikGeMuafFARJ+Oy54rpz09AiBOZBb69iL86hPp4Tmp+J0gkHG5J3Nv2WjWaZHUILgRPA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3046384}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "18839eb234f79adc44a591e355fd7b3243a4cd21", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.16.2_1713799153270_0.7270838723544637", "host": "s3://npm-registry-packages"}}, "4.16.3": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.16.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.16.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "1f24ac280a7e984a62c81b30c26e17314eed4b5f", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.16.3.tgz", "fileCount": 3, "integrity": "sha512-+rxD3memdkhGz0NhNqbYHXBoA33MoHBK4uubZjF1IeQv1Psi6tqgsCcC6vwQjxBM1qoCqOQQBy0cgNbbZKnGUg==", "signatures": [{"sig": "MEUCIENgqY/RIOYygUt5HCTHHPU6Mbqhe6JyuAwyu9Le7N1yAiEA1WtiWZDfL3Kv4utngh/KiVT/mwuZsGRlaDb6+TxXoS4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3046384}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "b9a62fd4cf28538d7c3b268eb25e709b45d44cce", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.16.3_1713849153075_0.7628762762167252", "host": "s3://npm-registry-packages"}}, "4.16.4": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.16.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.16.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "6cc0db57750376b9303bdb6f5482af8974fcae35", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.16.4.tgz", "fileCount": 3, "integrity": "sha512-Ma4PwyLfOWZWayfEsNQzTDBVW8PZ6TUUN1uFTBQbF2Chv/+sjenE86lpiEwj2FiviSmSZ4Ap4MaAfl1ciF4aSA==", "signatures": [{"sig": "MEUCICziaDaogCl5BEDEtARjOCyrzPgIk1MGOvaJOoyoDe2wAiEA34qZ2dJWYzE6o30pMe91Jd9EKsbhT7yUKVPvFEdx6IY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3046384}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "1c404fa352b70007066e94ff4c1981f8046f8cef", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.16.4_1713878105878_0.0691828061519848", "host": "s3://npm-registry-packages"}}, "4.17.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.17.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.17.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "a0403ef24fe50d28b7c18dcf97d88d882950bbd8", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.17.0.tgz", "fileCount": 3, "integrity": "sha512-uL7UYO/MNJPGL/yflybI+HI+n6+4vlfZmQZOCb4I+z/zy1wisHT3exh7oNQsnL6Eso0EUTEfgQ/PaGzzXf6XyQ==", "signatures": [{"sig": "MEUCIQDhqCYhrCoJkm8aLsJFSJ35XitGpD+ey/7xRkaX9VoiZQIgLaE+da37MewHB47e3zZjkBJEBKOM3IkEDO+3VGTT8RA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3033072}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "91352494fc722bcd5e8e922cd1497b34aec57a67", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.17.0_1714217390526_0.07229835233996806", "host": "s3://npm-registry-packages"}}, "4.17.1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.17.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.17.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "f1b28caca6d97beab3e3a5e623b97610a423bea5", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.17.1.tgz", "fileCount": 3, "integrity": "sha512-J0d3NVNf7wBL9t4blCNat+d0PYqAx8wOoY+/9Q5cujnafbX7BmtYk3XvzkqLmFECaWvXGLuHmKj/wrILUinmQg==", "signatures": [{"sig": "MEQCIBYSpgaaJe38vaUzSwuYcRXQeT6wpPkQ2HPvmeahp+k4AiBmOwzTywj5la9osk3HY5+EyAr5r/r8kTx3JLWZHvik/g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3033072}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "dbf0a2e5d3c3eae09ac4d502646d0ecab63f40fd", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.17.1_1714366673287_0.05451779386638966", "host": "s3://npm-registry-packages"}}, "4.17.2": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.17.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.17.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "27f65a89f6f52ee9426ec11e3571038e4671790f", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.17.2.tgz", "fileCount": 3, "integrity": "sha512-tmdtXMfKAjy5+IQsVtDiCfqbynAQE/TQRpWdVataHmhMb9DCoJxp9vLcCBjEQWMiUYxO1QprH/HbY9ragCEFLA==", "signatures": [{"sig": "MEUCIQDIDOMDqPWPNq5Ql9C3xwiN2fDi9Cm6R1ph4qOGI8h7VQIgFDWFSoWKrkF2pM6RyeCJ3nvCdVcwxqoO3e6CqNaZV/Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3033072}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "5e955a1c2c5e080f80f20f650da9b44909d65d56", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.17.2_1714453243191_0.06014036904632869", "host": "s3://npm-registry-packages"}}, "4.18.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.18.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.18.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "ed6603e93636a96203c6915be4117245c1bd2daf", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.18.0.tgz", "fileCount": 3, "integrity": "sha512-7J6TkZQFGo9qBKH0pk2cEVSRhJbL6MtfWxth7Y5YmZs57Pi+4x6c2dStAUvaQkHQLnEQv1jzBUW43GvZW8OFqA==", "signatures": [{"sig": "MEQCIFZymwh6BI/XVRxSiK+sxT10UF9VFsfsyRybO0gWmvXEAiAYdXBd87OAxjlO7E5sk1oKuB5qROY4UNf1XQNBtu8LTA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3055088}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "bb6f069ea3623b0297ef3895f2dcb98a2ca5ef58", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.18.0_1716354222160_0.4623045650353932", "host": "s3://npm-registry-packages"}}, "4.18.1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.18.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.18.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "4a5dcbbe7af7d41cac92b09798e7c1831da1f599", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.18.1.tgz", "fileCount": 3, "integrity": "sha512-W2ZNI323O/8pJdBGil1oCauuCzmVd9lDmWBBqxYZcOqWD6aWqJtVBQ1dFrF4dYpZPks6F+xCZHfzG5hYlSHZ6g==", "signatures": [{"sig": "MEUCIQCG5AItzPsrDySHTEl8Wu2noFUMRRX8bJa7nx08aNifJgIga/IhajS3kpImAGQfqoIhuf+R57OT7/YsEgnNa19lktQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2904560}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "21f9a4949358b60801c948cd4777d7a39d9e6de0", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.18.1_1720452310708_0.6712877791837246", "host": "s3://npm-registry-packages"}}, "4.19.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.19.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.19.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "1eed24b91f421c2eea8bb7ca8889ba0c867e1780", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.19.0.tgz", "fileCount": 3, "integrity": "sha512-HxDMKIhmcguGTiP5TsLNolwBUK3nGGUEoV/BO9ldUBoMLBssvh4J0X8pf11i1fTV7WShWItB1bKAKjX4RQeYmg==", "signatures": [{"sig": "MEUCIC1ew6LteTRZkVMMCAJYwCcYQIRVe+haezmdVih0DUlYAiEAkrLVvfD2pZKBEk0vuXJwojX+1eUL8OVp0GXRpkhNXfg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2893808}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "28546b5821efcb72c2eb05f422d986524647a0e3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.19.0_1721454373546_0.740393938986827", "host": "s3://npm-registry-packages"}}, "4.19.1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.19.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.19.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "21ac5ed84d914bc31821fec3dd909f7257cfb17b", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.19.1.tgz", "fileCount": 3, "integrity": "sha512-88brja2vldW/76jWATlBqHEoGjJLRnP0WOEKAUbMcXaAZnemNhlAHSyj4jIwMoP2T750LE9lblvD4e2jXleZsA==", "signatures": [{"sig": "MEQCIAKSw90JHDghiOwBs/tIQGpK3beSM/NFaJIlWOqVdlhtAiB8lZbsLM3I0PQFKlHhlhMNdZm+Oq0f4A5O84dGYuhHBg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2833904}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "8b967917c2923dc6a02ca1238261387aefa2cb2f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.19.1_1722056040139_0.3905244759104194", "host": "s3://npm-registry-packages"}}, "4.19.2": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.19.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.19.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "bada17b0c5017ff58d0feba401c43ff5a646c693", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.19.2.tgz", "fileCount": 3, "integrity": "sha512-ayVstadfLeeXI9zUPiKRVT8qF55hm7hKa+0N1V6Vj+OTNFfKSoUxyZvzVvgtBxqSb5URQ8sK6fhwxr9/MLmxdA==", "signatures": [{"sig": "MEUCIApPF7zNQwBvA9FamDdOlK2kX20vLepQwawatA/OXjETAiEAw8vYKCFRFAOVev92x7S9SQfspmLrTLRaUXdSvGp/Wvk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2833904}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "39955e55dbc12ec379a21efcf8fc21e55ec6ce3a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.19.2_1722501172725_0.4406872380445286", "host": "s3://npm-registry-packages"}}, "4.20.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.20.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.20.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "86a221f01a2c248104dd0defb4da119f2a73642e", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.20.0.tgz", "fileCount": 3, "integrity": "sha512-psegMvP+Ik/Bg7QRJbv8w8PAytPA7Uo8fpFjXyCRHWm6Nt42L+JtoqH8eDQ5hRP7/XW2UiIriy1Z46jf0Oa1kA==", "signatures": [{"sig": "MEUCIQCnfOrGrrnDYmZO3qZ+ejNgbjHQ8w1139cqWAjyiijEBQIgKeWt8ZCK4PKxbS4ssJ8qDK6GhEMhNEKbxROuMFPb1V8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2833392}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "df12edfea6e9c1a71bda1a01bed1ab787b7514d5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.20.0_1722660530075_0.8047893918399636", "host": "s3://npm-registry-packages"}}, "4.21.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.21.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.21.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "2fef1a90f1402258ef915ae5a94cc91a5a1d5bfc", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.21.0.tgz", "fileCount": 3, "integrity": "sha512-s5oFkZ/hFcrlAyBTONFY1TWndfyre1wOMwU+6KCpm/iatybvrRgmZVM+vCFwxmC5ZhdlgfE0N4XorsDpi7/4XQ==", "signatures": [{"sig": "MEYCIQCI0uOkU+8922ORVTt/60xIRKfOLQA5MVoLO1xfcI2CYwIhAL0WGHr7rVOnrPi5sfXfxEbPl0jTp9R+r2dEeEehubCe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2741232}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "c4bb050938778bcbe7b3b3ea3419f7fa70d60f5b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.21.0_1723960533896_0.7736036598135376", "host": "s3://npm-registry-packages"}}, "4.21.1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.21.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.21.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "18349db8250559a5460d59eb3575f9781be4ab98", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.21.1.tgz", "fileCount": 3, "integrity": "sha512-3Q3brDgA86gHXWHklrwdREKIrIbxC0ZgU8lwpj0eEKGBQH+31uPqr0P2v11pn0tSIxHvcdOWxa4j+YvLNx1i6g==", "signatures": [{"sig": "MEUCICzlI8EJWmBgcYRbk+/KIjDI/dsjI7/Edndf40HmjJAHAiEAu7uD+cFvSHUuM6/PqdkgisrTZHQGTk7vufcYAKaI4qI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2738672}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "c33c6ceb7da712c3d14b67b81febf9303fbbd96c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.21.1_1724687652633_0.8004691705718407", "host": "s3://npm-registry-packages"}}, "4.21.2": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.21.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.21.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "a0ca0c5149c2cfb26fab32e6ba3f16996fbdb504", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.21.2.tgz", "fileCount": 3, "integrity": "sha512-9rRero0E7qTeYf6+rFh3AErTNU1VCQg2mn7CQcI44vNUWM9Ze7MSRS/9RFuSsox+vstRt97+x3sOhEey024FRQ==", "signatures": [{"sig": "MEUCIQDksdicDjAZ9Yo8HwCxX7Xy6SEg82LMThxKnmkdJ03lHAIgK2ifEmruBVgo/P567jk2BLMJcsc4TFQgD2JI/sm1P5A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2729456}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "f83b3151e93253a45f5b8ccb9ccb2e04214bc490", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.21.2_1725001465436_0.9959006777512065", "host": "s3://npm-registry-packages"}}, "4.21.3": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.21.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.21.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "fbb6ef5379199e2ec0103ef32877b0985c773a55", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.21.3.tgz", "fileCount": 3, "integrity": "sha512-9isNzeL34yquCPyerog+IMCNxKR8XYmGd0tHSV+OVx0TmE0aJOo9uw4fZfUuk2qxobP5sug6vNdZR6u7Mw7Q+Q==", "signatures": [{"sig": "MEUCIQCjmXXvy5MZag7X/bk09LLi+NYGLFwvQuhOfKVlPPx4NQIgHLgpjxDJ8+qwVbQ8M8SvYs+2AlTQCOuaIGgZUY8nU6o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2755056}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "9f5a735524a5c56ba61a8dc6989374917f5aceb1", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.21.3_1726124748949_0.9384773973054874", "host": "s3://npm-registry-packages"}}, "4.22.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.22.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.22.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "6bd66c198f80c8e7050cfd901701cfb9555d768a", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.22.0.tgz", "fileCount": 3, "integrity": "sha512-7LB+Bh+Ut7cfmO0m244/asvtIGQr5pG5Rvjz/l1Rnz1kDzM02pSX9jPaS0p+90H5I1x4d1FkCew+B7MOnoatNw==", "signatures": [{"sig": "MEUCIQC1QaRaiR0LLM5rM4EjmipceCKfHfsYXePR6YoRLIuqLwIgeJHv/i8pLSoQLBwIPqLEcGrtd6eAVKFvn3azUXPJ790=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2746864}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "5e7a3631a28a863ddb97a64189c3b76eec9983ca", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.22.0_1726721730894_0.8506786655802039", "host": "s3://npm-registry-packages"}}, "4.22.1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.22.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.22.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "df2283dabb7ebc54ef6bd2e0e5415d8dc04c5a8f", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.22.1.tgz", "fileCount": 3, "integrity": "sha512-jDS/ShZxlA3HKtgm25CcbApOVsr/0Zkdu/E+3xK4UO0PT912yqyh7jNpTmZZJAiPDQoSDI9FOqrjSbnlpW6IFg==", "signatures": [{"sig": "MEUCIQCcd4w7JU8BLwuHuUp/Jo+T8/ku4+r+LNk1Ucl6etrtEwIgC/btzD4diI/OyDufwgxWzzVEccgpqCKbT/F2susFaK0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2743280}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "76e962daca5b7352bf199c28fa0a10ad4745c5e7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.22.1_1726820512924_0.4229877732127416", "host": "s3://npm-registry-packages"}}, "4.22.2": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.22.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.22.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "8ae561401b92acb8ca7a842ffadececb22a2247e", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.22.2.tgz", "fileCount": 3, "integrity": "sha512-t/YgCbZ638R/r7IKb9yCM6nAek1RUvyNdfU0SHMDLOf6GFe/VG1wdiUAsxTWHKqjyzkRGg897ZfCpdo1bsCSsA==", "signatures": [{"sig": "MEUCIQC+AL75v01tzqmzf9g623jH/Sp3jvHS6b6xuNbVeQIfhQIgd4ZzQA2WU3KmztRTfcmtF24izBCosfqsxPE6m5qCqmM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2743280}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "b86ffd776cfa906573d36c3f019316d02445d9ef", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.22.2_1726824824572_0.782851378262718", "host": "s3://npm-registry-packages"}}, "4.22.3-0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.22.3-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.22.3-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "a43036b6ba8b93446127b4c8c33d9caf1f958338", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.22.3-0.tgz", "fileCount": 3, "integrity": "sha512-QjWy6iX2PrHzghSjNEJpkoU3w4LqD+fHWxdwt5ebe3Pr/DX7OxeGO5lC05fiAZv9mR/ZuZ2UVPDQSb98SIT4yg==", "signatures": [{"sig": "MEYCIQCFlscjgK87lFKix7NKTjULJP7oxMoMMDQPSxhSjjO8owIhAMU8cOrsjVyxnrrPbpzQPhcT6b6jTxn91twcumxb0L5H", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2743282}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "9e04b4849db9134473b84e4b94aa353ae4fd8754", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.22.3-0_1726843676781_0.49938132216912057", "host": "s3://npm-registry-packages"}}, "4.22.3": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.22.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.22.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "57ed663fb87588718d3f367d36aaca94fade66e0", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.22.3.tgz", "fileCount": 3, "integrity": "sha512-IWxGO/k2gysJPXDmUAqmpcayaX6P+RB10JXJrV4QjcN7ipKWFcHYfTDG1EGyXLEZhHMQe3Ed0OjvPe4JYZvINA==", "signatures": [{"sig": "MEUCIGDCJaCaWclChzcqLwsd1tCWdEP+DpNxlgZr0El969TSAiEAxvwogNWlN5S7aWJRywIwCB9L4dtjcs/ZrwmkACAnyCo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2743280}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "e1cba8e84a0c01dd16580ba7a2536a988dfb4e18", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.22.3_1726894988330_0.3639983980922825", "host": "s3://npm-registry-packages"}}, "4.22.4": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.22.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.22.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "4349482d17f5d1c58604d1c8900540d676f420e0", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.22.4.tgz", "fileCount": 3, "integrity": "sha512-BjI+NVVEGAXjGWYHz/vv0pBqfGoUH0IGZ0cICTn7kB9PyjrATSkX+8WkguNjWoj2qSr1im/+tTGRaY+4/PdcQw==", "signatures": [{"sig": "MEUCIQCz/+2qytEh2hLSyrier/YnOCjLBZdYKUfGdEKMRmU5yQIgTxJWjpWGfm8E97e/31yo7yFg955jSYAzjm1L7Twx7jE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2743280}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "79c0aba353ca84c0e22c3cfe9eee433ba83f3670", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.22.4_1726899081723_0.02110964868255305", "host": "s3://npm-registry-packages"}}, "4.22.5": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.22.5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.22.5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "12ee49233b1125f2c1da38392f63b1dbb0c31bba", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.22.5.tgz", "fileCount": 3, "integrity": "sha512-RXT8S1HP8AFN/Kr3tg4fuYrNxZ/pZf1HemC5Tsddc6HzgGnJm0+Lh5rAHJkDuW3StI0ynNXukidROMXYl6ew8w==", "signatures": [{"sig": "MEYCIQCLFx/XT8UD9vj/Y0F3VDV3XWrBTH1q+oAgiJoy9QNCcwIhAIA5MSqAkO+hNqu63HoExpM+eLc+IHTOpQAXiM5LmE5D", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2751472}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "bc7780c322e134492f40a76bf64afe561670425c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.22.5_1727437695188_0.7685111192913245", "host": "s3://npm-registry-packages"}}, "4.23.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.23.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.23.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "9e09307dd0656a63db9ef86a6004679f56d9ddcf", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.23.0.tgz", "fileCount": 3, "integrity": "sha512-+5Ky8dhft4STaOEbZu3/NU4QIyYssKO+r1cD3FzuusA0vO5gso15on7qGzKdNXnc1gOrsgCqZjRw1w+zL4y4hQ==", "signatures": [{"sig": "MEUCIQCGsA9BvyR9XMHeJ8NB6YpC+d37MOgFn4dSFLHKuV/WfwIgQWpItMcow0q0aDta6zMC8KARxIa83h2UoEMfdqnFZyg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2751472}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "ed98e0821e6ad064839f0af46ceca061adbe3f14", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.23.0_1727766606032_0.46299889021671503", "host": "s3://npm-registry-packages"}}, "4.24.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.24.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.24.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "71fa3ea369316db703a909c790743972e98afae5", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.24.0.tgz", "fileCount": 3, "integrity": "sha512-VXBrnPWgBpVDCVY6XF3LEW0pOU51KbaHhccHw6AS6vBWIC60eqsH19DAeeObl+g8nKAz04QFdl/Cefta0xQtUQ==", "signatures": [{"sig": "MEQCIG2vSRT+fBauaTopRN9DIYcQqis9EkjPfX+JgJEDWU3qAiBO7bQzElhZrF+8KH0J1PtrGxwDj9jT7DQveb+FivEO/Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2760176}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "d3c000f4fd453e39a354299f0cfaa6831f56d7d8", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.24.0_1727861839460_0.30083771304548423", "host": "s3://npm-registry-packages"}}, "4.24.1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.24.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.24.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "bc7c6677060c84213938f4f0cbe3a78ddf846ab4", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.24.1.tgz", "fileCount": 3, "integrity": "sha512-wexHPBkBa2/tPhbGcxLqOM2AFZ7BQsZ0pk3dVxRL5Ec0SsXnkpcMucZ4j4woyoD5DbRdFP6Roptd9TRsGVTvUA==", "signatures": [{"sig": "MEUCIQDOfLRtCeaXE3vCh6ovUzfGuO+8PuaPOUW5P89rKxfP/AIgXX/bIaBpNMCr74yW5D/NrLooPGD9Tpgawnrh6fLYia0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2759664}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "88a54d892dacbb0efdbcade263a32d9df1a77b37", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.24.1_1730011382458_0.5632957502883698", "host": "s3://npm-registry-packages"}}, "4.24.2": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.24.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.24.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "c80e2c33c952b6b171fa6ad9a97dfbb2e4ebee44", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.24.2.tgz", "fileCount": 3, "integrity": "sha512-A+JAs4+EhsTjnPQvo9XY/DC0ztaws3vfqzrMNMKlwQXuniBKOIIvAAI8M0fBYiTCxQnElYu7mLk7JrhlQ+HeOw==", "signatures": [{"sig": "MEQCIFvuGsCu3C7wuArtKCLPkTvt5bHBqnkrvrqS+vVXTIMTAiBanZJyx/4DDyXrJTppDXGZtnx7USwUETsOeNdm/scj4w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2759664}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "32d0e7dae85121ac0850ec28576a10a6302f84a9", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.24.2_1730043607643_0.02999956542064397", "host": "s3://npm-registry-packages"}}, "4.25.0-0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.25.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.25.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "05ec6c0a9b27c4822207f135a3a18e6b95c65856", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.25.0-0.tgz", "fileCount": 3, "integrity": "sha512-8pPB123mQiJiQ5SBv8vqhkUA2s0UPnInttZruPpG41SzDr4ku8EJQqxMNPnox9iikeggmcX9kQK+8UOjcOovOg==", "signatures": [{"sig": "MEUCIQDn5oeIDgVbkF02PG4vF1KCDJ3KKnkWCsKuJ+lDg434aQIgTiVrvWzcCAWg+XEmQJCMWa+asvSNG3WeueJb46Ieuik=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2759666}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "b7fcaba12e863db516f39de74c1eacfe5329a5c3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.25.0-0_1730182510094_0.06290339064036976", "host": "s3://npm-registry-packages"}}, "4.24.3": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.24.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.24.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "11d6a59f651a3c2a9e5eaab0a99367b77a29c319", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.24.3.tgz", "fileCount": 3, "integrity": "sha512-2lg1CE305xNvnH3SyiKwPVsTVLCg4TmNCF1z7PSHX2uZY2VbUpdkgAllVoISD7JO7zu+YynpWNSKAtOrX3AiuA==", "signatures": [{"sig": "MEUCIQDXmwKfEfkTPVvXiJI9EjvQjxst8Ms4sGMDRXP9YViHMQIgJ2OQDOZ/we8f5EsoIzg6O7iCwj1+mnatsHEILgKBBPg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2759664}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "69353a84d70294ecfcd5e1ab8e372e21e94c9f8e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.24.3_1730211247364_0.640490804504958", "host": "s3://npm-registry-packages"}}, "4.24.4": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.24.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.24.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "e48e78cdd45313b977c1390f4bfde7ab79be8871", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.24.4.tgz", "fileCount": 3, "integrity": "sha512-ku2GvtPwQfCqoPFIJCqZ8o7bJcj+Y54cZSr43hHca6jLwAiCbZdBUOrqE6y29QFajNAzzpIOwsckaTFmN6/8TA==", "signatures": [{"sig": "MEYCIQCUv5zFK9NqFV1VD9e0FfTIUWCtYLaFkVX1sTsmuohtiwIhANXeW6B3HSubJyqFpynQgrk4lGNm0rfJltmV3Cpz17UD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2751984}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "cdf34ab5411aac6ac3f6cd21b10d2e58427e88ec", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.24.4_1730710028919_0.6932387941578353", "host": "s3://npm-registry-packages"}}, "4.25.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.25.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.25.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "f4ec076579634f780b4e5896ae7f59f3e38e0c60", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.25.0.tgz", "fileCount": 3, "integrity": "sha512-JT8tcjNocMs4CylWY/CxVLnv8e1lE7ff1fi6kbGocWwxDq9pj30IJ28Peb+Y8yiPNSF28oad42ApJB8oUkwGww==", "signatures": [{"sig": "MEUCIQC1YROmaasmurTM6hiaM+pi0yXVg9/una4iqvFrJcQC2gIgZzNfYFSxjJkq3aIZUgTVD+nHUyapdueu7P6mJj5RfY0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2730992}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "42e587e0e37bc0661aa39fe7ad6f1d7fd33f825c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.25.0_1731141442093_0.316088161729964", "host": "s3://npm-registry-packages"}}, "4.26.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.26.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.26.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "f4b4e0747710ba287eb2e2a011538ee2ed7f74d3", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.26.0.tgz", "fileCount": 3, "integrity": "sha512-WUQzVFWPSw2uJzX4j6YEbMAiLbs0BUysgysh8s817doAYhR5ybqTI1wtKARQKo6cGop3pHnrUJPFCsXdoFaimQ==", "signatures": [{"sig": "MEUCIGvVc8guYvnab+mqOqqeloZ9tb72lD4iqRgSe/ixr+72AiEAj6oqqTc6POpmFgxRj0I/VdAli+sazj42Cm2c9ubXv8A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2730992}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "ae1d14b7855ff6568a6697d37271a5eb4d8e2d3e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.26.0_1731480299356_0.023605070170603426", "host": "s3://npm-registry-packages"}}, "4.27.0-0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.27.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.27.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "8c79bf8d4fdec83896e1625d087a98d4fb36d7a9", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.27.0-0.tgz", "fileCount": 3, "integrity": "sha512-furEmp7NutOcj9fpe1kUhtZUzgzJH4m5cTzq+w3vkqhNn5TtgoE1t1yEShSQQl8CvPkBX2aQcD3/DCRLjA0Yaw==", "signatures": [{"sig": "MEYCIQCI4Em3HF5kPgotvwm1VTkvsGZdA+xoiJ0HbQsJwjcdsAIhAO46tMPD+7fUg6Xfusb+aOIir4LtVqZz1H+8jAMSA4B5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2730994}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "5e6074f07843bcbcf26b916c557fdfd81d2adece", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.27.0-0_1731481393860_0.5716545981366179", "host": "s3://npm-registry-packages"}}, "4.27.0-1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.27.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.27.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "6650a958ab63187bc5b95e3c3eabc49d6ff17a10", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.27.0-1.tgz", "fileCount": 3, "integrity": "sha512-ol2hOOigawL5L4JPj40Jrhgxbltj3ty6REYwcEu5quhE052p/FE5Oyxt3jnhgf/K/MsZm/TJnSvqXmASOMk8hA==", "signatures": [{"sig": "MEUCIFr0BXFxs53h4NRkJV360eDeE1BAWJqZQuVbrSh1qefhAiEAs12GGtQAN7lMyZoSxDYqAOoNLdAV1kxPcUwVuv58tSE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2730994}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "81f5021d7d7e2a488639dc036f2334995b3761fc", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.27.0-1_1731565990724_0.3028024961225093", "host": "s3://npm-registry-packages"}}, "4.27.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.27.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.27.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "efe48aa0820420f71b9b54eccea9924a59429671", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.27.0.tgz", "fileCount": 3, "integrity": "sha512-VrYQHY5+Y71OU/uOSRE9lLhph16bbuWGrMwGwZDPxCUXUW5NgLA+K+q0kv7rafHRlnrsZSVcMOkZskzTNnR3ZQ==", "signatures": [{"sig": "MEYCIQDAu3wri5lG9jBxqXJQAeE7L0H1AhAHSmIwuavlwFCSVQIhAJHG1r6bkmGcVfceIPErV1VulhP5noUGEKoopxVmDC8y", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2761200}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "c035068dfebeb959a35a8acf3ff008a249e2af73", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.27.0_1731667235965_0.889495344625767", "host": "s3://npm-registry-packages"}}, "4.27.1-0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.27.1-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.27.1-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "576e78bf6db69e35b59cc7e9160e0abc0fdca658", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.27.1-0.tgz", "fileCount": 3, "integrity": "sha512-NP6bgGeWN524Urogt11q/tWVecrkL2tyT4Ct559//AiW9UYgQ4xEGCLjo5LY5VZDj+RdiJwRiWibxpYt340xRg==", "signatures": [{"sig": "MEUCIQCmt49/O/6HlNFG4oyezrtXHeghrjvEDpldzFAhvQX73wIgPceCDjhQ2t6dEkQqGVE8G8d34ua/hdKXanYBODtGBPc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2761202}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "a80f6a94d720224a44331d5a50745e9887619703", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.27.1-0_1731677287581_0.019509376229615016", "host": "s3://npm-registry-packages"}}, "4.27.1-1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.27.1-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.27.1-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "fa040fd37e05007c0bca4270f688ba4df543613f", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.27.1-1.tgz", "fileCount": 3, "integrity": "sha512-3nvzHq2GU/NAkj+FDslPJfSomLbk7VLlNa8v3Xik8IJ8uPWY6QZEGnHK7C1BMfJwTl14cVsTLG1TEyIUNcayqQ==", "signatures": [{"sig": "MEUCIG3yNTmxJar8sZFicY/eLJQmbmCobzCknR9h0XyIwntcAiEA8zMgxG2q6+WDjU7qQVETx2TED86xAxgKNkPeZB7RuiI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2761202}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "892ce0206dbf4fbf656b2f0563ef803c5e5a0016", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.27.1-1_1731685083063_0.4865424263887359", "host": "s3://npm-registry-packages"}}, "4.27.1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.27.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.27.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "928391f58dd298d7c2a69170fcf5984d788dec63", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.27.1.tgz", "fileCount": 3, "integrity": "sha512-AcQsa9FF6T9FrHXWXGAqJ6Kjcae2lYEDZA7wRQmK/3Bvv/0hH38tJL51CYclTY90fRf1mtKuwpC4LRcMkZuV1w==", "signatures": [{"sig": "MEYCIQDml8NTTnZbRLacdVhZ9ppolc3bfL2zJ+6jFHauku52UQIhALKvTlUcfrMMc/H3AzT0FecwPjxaF+qB0l/5Zgtjo1S2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2761200}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "aaf38b725dd142b1da4190a91de8b04c006fead5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.27.1_1731686861754_0.9699401634618721", "host": "s3://npm-registry-packages"}}, "4.27.2": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.27.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.27.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "3ccf1f8a7e74ca22ad9cf258d31077320cfac19e", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.27.2.tgz", "fileCount": 3, "integrity": "sha512-euMIv/4x5Y2/ImlbGl88mwKNXDsvzbWUlT7DFky76z2keajCtcbAsN9LUdmk31hAoVmJJYSThgdA0EsPeTr1+w==", "signatures": [{"sig": "MEUCIEncpYBqeQKp6/is3ueWcQBvEvWc/6s/YcLFZScvw9joAiEAq4v+blLfMVqEC0114/tf4gwwrMB7duT9cic8gO0ROx0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2761200}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "a503a4dd9982bf20fd38aeb171882a27828906ae", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.27.2_1731691203286_0.3782801489659764", "host": "s3://npm-registry-packages"}}, "4.27.3": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.27.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.27.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "3f2db9347c5df5e6627a7e12d937cea527d63526", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.27.3.tgz", "fileCount": 3, "integrity": "sha512-ogfbEVQgIZOz5WPWXF2HVb6En+kWzScuxJo/WdQTqEgeyGkaa2ui5sQav9Zkr7bnNCLK48uxmmK0TySm22eiuw==", "signatures": [{"sig": "MEQCIF22/DvizJPhNhPx/RhVJwirQHDYnoASkEm/7xZTCcJ5AiA2UASUM0+CqTdqgWW5owo4Rsh5syPx3fibjiFAspSb3Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2761200}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "7c0b1f8810013b5a351a976df30a6a5da4fa164b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.27.3_1731947976161_0.4458770440249713", "host": "s3://npm-registry-packages"}}, "4.27.4": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.27.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.27.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "030b6cc607d845da23dced624e47fb45de105840", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.27.4.tgz", "fileCount": 3, "integrity": "sha512-yOpVsA4K5qVwu2CaS3hHxluWIK5HQTjNV4tWjQXluMiiiu4pJj4BN98CvxohNCpcjMeTXk/ZMJBRbgRg8HBB6A==", "signatures": [{"sig": "MEUCIBKm0lQeG0jf2257l2dbWH2PrLRSo2cDvxHDOfhKsEBFAiEAg5EVdoqClWgqYYCBtaK5bShIMAmykhF80316ZnsWrsU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2751472}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "e805b546405a4e6cfccd3fe73e9f4df770023824", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.27.4_1732345221430_0.4128535177123134", "host": "s3://npm-registry-packages"}}, "4.28.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.28.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.28.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "69682a2a10d9fedc334f87583cfca83c39c08077", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.28.0.tgz", "fileCount": 3, "integrity": "sha512-Vi+WR62xWGsE/Oj+mD0FNAPY2MEox3cfyG0zLpotZdehPFXwz6lypkGs5y38Jd/NVSbOD02aVad6q6QYF7i8Bg==", "signatures": [{"sig": "MEUCIQDmse+Ou79r0wladdACQkVS4+QzNQM5UmW2X5tK8xpPaQIgdjVDAKk1h+0NAgYrma6l8cAO43vANCuDqfA9iD78sZA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2748912}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "0595e433edec3608bfc0331d8f02912374e7f7f7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.28.0_1732972547625_0.2812330032091377", "host": "s3://npm-registry-packages"}}, "4.28.1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.28.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.28.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "631ffeee094d71279fcd1fe8072bdcf25311bc11", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.28.1.tgz", "fileCount": 3, "integrity": "sha512-wSXmDRVupJstFP7elGMgv+2HqXelQhuNf+IS4V+nUpNVi/GUiBgDmfwD0UGN3pcAnWsgKG3I52wMOBnk1VHr/A==", "signatures": [{"sig": "MEQCIFoVLsTG8wXYb+gSf/WIzdoyzw0abvwrYj0K+cmtc11jAiBn/GyodH11zRXh8qnWeE9OHZoMB7w3yIh946jDDbxezQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2751472}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "e60fb1c5d4e54ed5257495215eeda1bb43cf54ba", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.28.1_1733485496652_0.34680105562472496", "host": "s3://npm-registry-packages"}}, "4.29.0-0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.29.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.29.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "7e48cd880183efe32db4ff9d9742e8b2d21b221c", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.29.0-0.tgz", "fileCount": 3, "integrity": "sha512-UaTUHv9yk9PPTvd3q7dXepvC3rhL45XeMmDHnZe0KrzBOXlady2Q9BdOtcKe6uvDpyZoqIwjlNhnYWoQjcueQA==", "signatures": [{"sig": "MEUCIQDhsnyP6D7RaPB8j1im1NNt1JjemNoLz37EzfyvzAFFDwIgJO7lZZY+mccHdOyio2NeTzgWaRS6gBqxBZP/llZPkM8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2748914}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "879d03d68890f365f880e30c69b58377b8743407", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.29.0-0_1734331193145_0.5082836522475351", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.0-1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.29.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.29.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "9fcbcf9e5835b57c16a7619ef6d870cce93e8a52", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.29.0-1.tgz", "fileCount": 3, "integrity": "sha512-E3AqECbYWNAlCS9OelgGqAgbdPr8/cQGNcpY/IdkXdPuNwWXZLdI76aM+xXmYWTsY1RQjHhI2qKnt3bUxDniWA==", "signatures": [{"sig": "MEYCIQCUu/GvOw3jkODXdtD9U2YGcVUJF/8buI6lyAm+4Dnq/AIhALrdFBfnCZeq0Ym3Uy2MT1NGsDDqJmcq7fbvCv7H9ZOa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2748914}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "fa5064084196636acd98263f95ffea59f8362e32", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.29.0-1_1734590251871_0.7860825709170882", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.0-2": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.29.0-2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.29.0-2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "c548fd1a70da1efbc9d471228b244e9fb1ea51ca", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.29.0-2.tgz", "fileCount": 3, "integrity": "sha512-09oF/8AHlVErs/4ZuX2tMfkr9uHd/inXSXb+AqfnNXvJx2nrbPUWi+SZpwzHp3v3eGHATzpg8GFYP653Opxh2A==", "signatures": [{"sig": "MEQCIDuqOgVCy/iXDug6Lw7vR48r4zfcCipBUKBY4xFHMJTnAiB7Ys/DrZOf6IDwTslVrTraRb186Qxbqv30UBs3qGduJQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2748914}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "bbb7e7b1d4e208a923b0f18ceb8dd886838e1a01", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.29.0-2_1734677763079_0.762975030054557", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.29.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.29.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "d7b388fc20d421db007e3078b934f3a40d8c75c5", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.29.0.tgz", "fileCount": 3, "integrity": "sha512-jA4+oxG7QTTtSQxwSHzFVwShcppHO2DpkbAM59pfD5WMG/da79yQaeBtXAfGTI+ciUx8hqK3RF3H2KWByITXtQ==", "signatures": [{"sig": "MEYCIQC+PY8mo5zBVE9LWoU/GtOcYDKzRWNZgBfcMa9LRgvfZgIhAJyKB7EtkP3/2Ouy8Ai3BUJzkQhXGY/qsU5ecDMH8tAh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2743792}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "dadd4882c4984d7875af799ad56e506784d50e1c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.29.0_1734719844821_0.7627809578941189", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.29.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.29.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "7f12efb8240b238346951559998802722944421e", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.29.1.tgz", "fileCount": 3, "integrity": "sha512-F2OiJ42m77lSkizZQLuC+jiZ2cgueWQL5YC9tjo3AgaEw+KJmVxHGSyQfDUoYR9cci0lAywv2Clmckzulcq6ig==", "signatures": [{"sig": "MEUCIQD8hbDocSaxs/3EtWBWSo+ZG7Y1VVWmt0YxBGJGXoufQQIgUC+7VrjJ4CtMjsDykolHCfTqAwqdvf/1eXS5SV7CmzQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2743792}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "5d3777803404c67ce14c62b8b05d6e26e46856f5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.29.1_1734765362613_0.9498926054212233", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.0-0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.30.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.30.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "557cd145b94c4d6e860f7decc522ffd0edf94013", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.30.0-0.tgz", "fileCount": 3, "integrity": "sha512-nLm/SeSV7fs63mwIaFlW7NmHjf1uyin2aSdQHBC1nFJFd0wYYnDTYpvR0RSCDdspCbyaYUrmylfO4IEZ8nv3Yg==", "signatures": [{"sig": "MEUCIQDFggzyBqu+bglPdFZx8tsTIfWUdET9ZCOQ9gg+wNfCfAIgBvbeNPQ4OwDf4ThtkZjKMS8a5QDpDZmmvSvkS/r5k5s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2743794}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "2339f1d8384a8999645823f83f9042a9fc7b3bf7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.30.0-0_1734765435114_0.6277029123323348", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.0-1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.30.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.30.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "f91b791eb4f7c58d7a900c3c8040903d046da963", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.30.0-1.tgz", "fileCount": 3, "integrity": "sha512-VupblZvQfSf1BFjn48GH9xBjGwns3fSXUNsXCEbbr8QqXig7nMEaWHuSpmaWOzDVNKqtkPwFRk8HNU7ktfizzQ==", "signatures": [{"sig": "MEYCIQCTwYO8c+OlashlnXHUDRWLVekuYcwNaCFZkaxdRpo2ZwIhAKV8a3JIBBo8He10TOG7aZjFifnmZKxz1HPnMd6ePL6p", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2746866}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "41ab39a6e4a5181e9be21e816dd6f11c57e1c52a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.30.0-1_1735541536855_0.9315339342112761", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.2": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.29.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.29.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "7c56efd576618db251909e21818d473cbcf96786", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.29.2.tgz", "fileCount": 3, "integrity": "sha512-S2V0LlcOiYkNGlRAWZwwUdNgdZBfvsDHW0wYosYFV3c7aKgEVcbonetZXsHv7jRTTX+oY5nDYT4W6B1oUpMNOg==", "signatures": [{"sig": "MEUCIQCxDwcharRwaWGXdzt7lljAcfTxqUXv5i+5scxbyOU8UgIgGJxg+41WuALNfJkw8SlXp+eonxauMrN8fZAzENeDDeM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2746864}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "f5c349e5bb4cb40b0cc1a1b2a3fb5de415946406", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.29.2_1736078863278_0.29150204780232425", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.30.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.30.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "6b968f5b068469db16eac743811ee6c040671042", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.30.0.tgz", "fileCount": 3, "integrity": "sha512-jROwnI1+wPyuv696rAFHp5+6RFhXGGwgmgSfzE8e4xfit6oLRg7GyMArVUoM3ChS045OwWr9aTnU+2c1UdBMyw==", "signatures": [{"sig": "MEQCIA7T3/JMXezfEk3awkxdFVvvq9oCllyMS4G/nR7muMLOAiBR2vw6wg3VhCp+KvXmOi8TbbUACOov4rlpvdjvPeGqqw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2746864}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "958d5ebabd49297e9a4b78ad34ac0a0132305dea", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.30.0_1736145401353_0.7053314552499155", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.30.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.30.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "51cad812456e616bfe4db5238fb9c7497e042a52", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.30.1.tgz", "fileCount": 3, "integrity": "sha512-WabtHWiPaFF47W3PkHnjbmWawnX/aE57K47ZDT1BXTS5GgrBUEpvOzq0FI0V/UYzQJgdb8XlhVNH8/fwV8xDjw==", "signatures": [{"sig": "MEUCIQDYocZ/iLBO8iDwmc+sjvVyVNwS55dcyf0CT88QDxkHbgIgB05fimlIqo43+neUsaYAuyhhUw4+Alb1voQAjN8sLMc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2746864}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "94917087deb9103fbf605c68670ceb3e71a67bf7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.30.1_1736246153032_0.9144180177453756", "host": "s3://npm-registry-packages-npm-production"}}, "4.31.0-0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.31.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.31.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "2356c25427553c3cab3fe89b8b9259a9f290a453", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.31.0-0.tgz", "fileCount": 3, "integrity": "sha512-I5sBb+c7mL3qQ0WzpVo9WnoWBtH6VBTJntsnJHupXZlvK0ZVkCn7hQdYEs6b05gBf9oHo7g9eCUIrHHQYl38hw==", "signatures": [{"sig": "MEUCIEmMY15ryShPIKO00FlxQPL5hj4xATVniHfZZBUTt0UeAiEAsw5Wte60OtZ89rde8HVtJN79XCObhEw2jOUBD1ICaHI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2760690}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "8c80d5f657f0777d14bd75d446fee3fa4b7639fc", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.31.0-0_1736834262629_0.9682330132528654", "host": "s3://npm-registry-packages-npm-production"}}, "4.31.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.31.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.31.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "32ed85810c1b831c648eca999d68f01255b30691", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.31.0.tgz", "fileCount": 3, "integrity": "sha512-JuhN2xdI/m8Hr+aVO3vspO7OQfUFO6bKLIRTAy0U15vmWjnZDLrEgCZ2s6+scAYaQVpYSh9tZtRijApw9IXyMw==", "signatures": [{"sig": "MEQCIGd1V/6aPLtv4pB3KvRH3p1VjxR/inGEUbMkqRxmqfUIAiAdH+85pvxcubE5zzGMB96KAc17FjFLCJ2w/pKoNnxiJg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2787312}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "15c264d59e0768b7d283a7bb8ded0519d1b5199e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.31.0_1737291407599_0.20957758282836214", "host": "s3://npm-registry-packages-npm-production"}}, "4.32.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.32.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.32.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "6534a09fcdd43103645155cedb5bfa65fbf2c23f", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.32.0.tgz", "fileCount": 3, "integrity": "sha512-pFDdotFDMXW2AXVbfdUEfidPAk/OtwE/Hd4eYMTNVVaCQ6Yl8et0meDaKNL63L44Haxv4UExpv9ydSf3aSayDg==", "signatures": [{"sig": "MEYCIQCrL29LmKec44AwrYK14PnZ+caFlriypftcORpcLUabogIhAJv3GX9T/VyyrSei4lkYd6SuVE8rTG4YH+DC+PSd5n0Y", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2800112}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "2538304efdc05ecb7c52e6376d5777565139f075", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.32.0_1737707255614_0.3002173333110183", "host": "s3://npm-registry-packages-npm-production"}}, "4.33.0-0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.33.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.33.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "bd39ed7307a4e6471212d36cd420d19caad5c885", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.33.0-0.tgz", "fileCount": 3, "integrity": "sha512-IEbgraO79IG9UF8cMymmtoxd14Xkd/HJF6faJ/A2pM25R02ABkHvTIUXGBuFOAgG/4531rTJrq+YMwh1RpH0cQ==", "signatures": [{"sig": "MEUCIQDCliWi7pgBaw9ab2Ae26caQcl8T2zfYAK/DAPcbfPSUQIgZs1WKitCrtMXb834UY08mvKZs5vIhphnD2OnwBLR6xk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2800114}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "f854e1988542d09f9691923eddd80888e92240d3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.33.0-0_1738053009250_0.09175165469654978", "host": "s3://npm-registry-packages-npm-production"}}, "4.32.1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.32.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.32.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "fa106304818078f9d3fc9005642ad99f596eed2d", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.32.1.tgz", "fileCount": 3, "integrity": "sha512-w2l3UnlgYTNNU+Z6wOR8YdaioqfEnwPjIsJ66KxKAf0p+AuL2FHeTX6qvM+p/Ue3XPBVNyVSfCrfZiQh7vZHLQ==", "signatures": [{"sig": "MEQCIF4J6dNNgSVbKXX4FEKSbVFiqp3Oso6dMCVB245hjf4VAiB5l8MIrMH8c2Q7+LPDttPdz5TRY32gDBBxbySAFlX1vg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2800112}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "abcf4febe11f3d313fae41ddca35fc60670b9ff8", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.32.1_1738053198074_0.4278804042514486", "host": "s3://npm-registry-packages-npm-production"}}, "4.33.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.33.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.33.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "e52d57829bac75ed398c6a38cd1114c275eb45e1", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.33.0.tgz", "fileCount": 3, "integrity": "sha512-Wmr+r26PU9fu2BMGA2Rb7FJf6yNUcnWrPvDHf0h15iOsY3iMxSCk98/Ez/QQOjLP6F0hTCCP7hWUuruoPbjAgQ==", "signatures": [{"sig": "MEYCIQDvdDcSH3t3w3ukeuJP8Iap1dDfUS4npJnYElczcZkKSwIhAOW1rAD2wWxYBO+DuNK5ouH2GPrWPuzqOoB2soE/upNA", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2744816}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "494483e8df7b5d04796b30e37f54d7e96fa91a97", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.33.0_1738393919566_0.323759864087223", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.34.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.34.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "e60db53ff6b3bd00baf13b04f91fddb43740077f", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.34.0.tgz", "fileCount": 3, "integrity": "sha512-Vmg0NhAap2S54JojJchiu5An54qa6t/oKT7LmDaWggpIcaiL8WcWHEN6OQrfTdL6mQ2GFyH7j2T5/3YPEDOOGA==", "signatures": [{"sig": "MEUCIGL3XSVHiL7Xqap/536Q3bs/XK+BEzqMBNucra5OM5lGAiEApoEnqtezjxM0mTPu6JRkQe40QCJwZ47nPVaKIfSJZZU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2744816}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "979d62888dbe75f92e50fdd64246c737c52f5f1f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.34.0_1738399223861_0.37401692525846797", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.34.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.34.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "125f09f40719ea7a02a76607428a0366ff9dca4d", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.34.1.tgz", "fileCount": 3, "integrity": "sha512-RnHy7yFf2Wz8Jj1+h8klB93N0NHNHXFhNwAmiy9zJdpY7DE01VbEVtPdrK1kkILeIbHGRJjvfBDBhnxBr8kD4g==", "signatures": [{"sig": "MEYCIQCVW96coRzkW3qJz2lDUb1qqSPiXt3UcvuiPXBWtyeoBQIhAMG24+EJ6XmeAUwhDlohUW2Jb9A3JFBScVXSKrcPDV76", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2744816}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "0f20524ad9ecd166a900d43af93f05a3405d2a45", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.34.1_1738565894056_0.3450857713524653", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.2": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.34.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.34.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "8bbf8dfb84aac3a013baaa15c1d5340a84326cea", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.34.2.tgz", "fileCount": 3, "integrity": "sha512-LQRkCyUBnAo7r8dbEdtNU08EKLCJMgAk2oP5H3R7BnUlKLqgR3dUjrLBVirmc1RK6U6qhtDw29Dimeer8d5hzQ==", "signatures": [{"sig": "MEUCIBK4yJWwmeQ+jf+uuDA4/FKpM5GDWDRZo9UEvWmFaj6qAiEAwIAh5T5r2uKOz86F+ekrmRp4VZ2/l/xCHhbb8ic8ml4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2744816}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "615efa045779fae70c4fd5fe64fdb08a039c0442", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.34.2_1738656602596_0.12303251794350611", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.3": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.34.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.34.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "183fb4b849accdf68d430894ada2b88eea95a140", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.34.3.tgz", "fileCount": 3, "integrity": "sha512-ojFOKaz/ZyalIrizdBq2vyc2f0kFbJahEznfZlxdB6pF9Do6++i1zS5Gy6QLf8D7/S57MHrmBLur6AeRYeQXSA==", "signatures": [{"sig": "MEUCIHFo+caCHy3GcJYLXlARPDEp7kkiLxTYTAbiSTFaEihfAiEA/kvjooTfI/gk0jXfRoCviIRrnsjR7yKi4uxVJzJknE4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2744816}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "ac8b06a2b5406f694c38c416912cc2b18ba13355", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.34.3_1738747324987_0.5241975258207028", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.4": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.34.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.34.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "33423f0b5e763aa79d9ef14aed9e22c3217a5051", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.34.4.tgz", "fileCount": 3, "integrity": "sha512-qORc3UzoD5UUTneiP2Afg5n5Ti1GAW9Gp5vHPxzvAFFA3FBaum9WqGvYXGf+c7beFdOKNos31/41PRMUwh1tpA==", "signatures": [{"sig": "MEYCIQCM8ym/aVZHPwiHt6f8BIqwWe7NwAOJMOFFTYjNaQC6DgIhAPu09C4kkbbVaHebqcngjAddouT8/oxnkQw+6YSeYWFf", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2744816}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "19312a762c3cda56a0f6dc80a0887a4499db2257", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.34.4_1738791071287_0.9276114509964968", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.5": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.34.5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.34.5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "d9f4a433addf4d36264ddc15130144ee8506665a", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.34.5.tgz", "fileCount": 3, "integrity": "sha512-PUbWd+h/h6rUowalDYIdc9S9LJXbQDMcJe0BjABl3oT3efYRgZ8aUe8ZZDSie7y+fz6Z+rueNfdorIbkWv5Eqg==", "signatures": [{"sig": "MEUCIBxiKw517wnZUM0YJrALqTJ6uK53rvVrlYEdJc7310DDAiEAzwmbsZVSnzsfza9VwRKABsnb24mcw+h4QUo+DWuhwdQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2719728}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "3426b026e95319048dd5b703f2a0330c1c924e52", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.34.5_1738918382453_0.12346437631214946", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.6": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.34.6", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.34.6", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "c6c5bf290a3a459c18871110bc2e7009ce35b15a", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.34.6.tgz", "fileCount": 3, "integrity": "sha512-3/q1qUsO/tLqGBaD4uXsB6coVGB3usxw3qyeVb59aArCgedSF66MPdgRStUd7vbZOsko/CgVaY5fo2vkvPLWiA==", "signatures": [{"sig": "MEQCIEtaCpY/CPKWXhX1VOOH3j5Xn/Y+wvZPAnmf2RU67z1VAiByvjxcBkEdbZHpb/yn3wAT+CdJWDaQIq0BrvDi2MZ5Qg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2715120}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "4b8745922d37d8325197d5a6613ffbf231163c7d", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.34.6_1738945927750_0.8458255386846991", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.7": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.34.7", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.34.7", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "06cedc0ef3cbf1cbd8abcf587090712e40ae6941", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.34.7.tgz", "fileCount": 3, "integrity": "sha512-MN7aaBC7mAjsiMEZcsJvwNsQVNZShgES/9SzWp1HC9Yjqb5OpexYnRjF7RmE4itbeesHMYYQiAtUAQaSKs2Rfw==", "signatures": [{"sig": "MEQCIB+bREGL1ic5PANehRYrlQIvLy6IlJLbs8O/pxBzH6ByAiBHOWtwFRQZYrintvNEssCCnQsLg8VmKMxi8ryP9o7oog==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2715632}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "f9c52f80074e33f5b0799e8ca215e3bfac7d2755", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.34.7_1739526839251_0.14370273314171067", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.8": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.34.8", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.34.8", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "cbfee01f1fe73791c35191a05397838520ca3cdd", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.34.8.tgz", "fileCount": 3, "integrity": "sha512-YHYsgzZgFJzTRbth4h7Or0m5O74Yda+hLin0irAIobkLQFRQd1qWmnoVfwmKm9TXIZVAD0nZ+GEb2ICicLyCnQ==", "signatures": [{"sig": "MEUCIFuwE385P1d3ThPic2IIAPMM6rCPO41laE8nvKI3zByMAiEArO3BK/b3ATfv/DDiiJBhapjhuIsVCiUwZPjzk3vAFLU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2715632}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "8f667b7c15b176728449a4917cb29fe5ee3a1c0c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.34.8_1739773585687_0.05214642674013281", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.9": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.34.9", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.34.9", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "89427dcac0c8e3a6d32b13a03a296a275d0de9a9", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.34.9.tgz", "fileCount": 3, "integrity": "sha512-z4mQK9dAN6byRA/vsSgQiPeuO63wdiDxZ9yg9iyX2QTzKuQM7T4xlBoeUP/J8uiFkqxkcWndWi+W7bXdPbt27Q==", "signatures": [{"sig": "MEUCIQDFYu5d9OUYKOZLjityoWlkvwVKEVLo7HXoHpfYeBwflQIgI19NyfSAF+kSE5A8xuSCVTUKESg01cEKP99JQaVvt74=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2893296}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "0ab9b9772e24dfe9ef08bfce3132e99a15b793f6", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.34.9_1740814356805_0.6405027723184651", "host": "s3://npm-registry-packages-npm-production"}}, "4.35.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.35.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.35.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "b0b37e2d77041e3aa772f519291309abf4c03a84", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.35.0.tgz", "fileCount": 3, "integrity": "sha512-OUOlGqPkVJCdJETKOCEf1mw848ZyJ5w50/rZ/3IBQVdLfR5jk/6Sr5m3iO2tdPgwo0x7VcncYuOvMhBWZq8ayg==", "signatures": [{"sig": "MEUCIH2QmGv0Fn7rQBVwoTPQwVFHI9i7qELA6EdIU9A7BdJIAiEAnw+KDD93dpS7R6ddcTeSM2jcAh/CRio0MHDSVF+p33g=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2897904}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "70ef1cce7c740030cc2935b563d13950cc1511f5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.35.0_1741415084511_0.5060878016712071", "host": "s3://npm-registry-packages-npm-production"}}, "4.36.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.36.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.36.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "bfda7178ed8cb8fa8786474a02eae9fc8649a74d", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.36.0.tgz", "fileCount": 3, "integrity": "sha512-qbqt4N7tokFwwSVlWDsjfoHgviS3n/vZ8LK0h1uLG9TYIRuUTJC88E1xb3LM2iqZ/WTqNQjYrtmtGmrmmawB6A==", "signatures": [{"sig": "MEUCID33kfalP91EcRKJlmK871948ROdTAYy59aDLF4Yw/MqAiEAkW9Ls07VzPFWWbIaja5C1bTCUAq3k9sLgE8zNGKpzx4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2909680}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "ab7bfa8fe9c25e41cc62058fa2dcde6b321fd51d", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.36.0_1742200543997_0.5140434835469121", "host": "s3://npm-registry-packages-npm-production"}}, "4.37.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.37.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.37.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "8063d5f8195dd1845e056d069366fbe06a424d09", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.37.0.tgz", "fileCount": 3, "integrity": "sha512-Jm7biMazjNzTU4PrQtr7VS8ibeys9Pn29/1bm4ph7CP2kf21950LgN+BaE2mJ1QujnvOc6p54eWWiVvn05SOBg==", "signatures": [{"sig": "MEQCIFERDq8Be2HxG5NX0hPx0rRkMDP6un+aZwEnS9KGTjsZAiBcuQUehgi2vi41KyiSH9ASYVPid6JLEpP8dokm4iidZA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2914800}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "8b1c634d945dda9294cf579de68c4b223c618e7f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.37.0_1742741827681_0.4526816623725194", "host": "s3://npm-registry-packages-npm-production"}}, "4.38.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.38.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.38.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "d27ab565009357014c9f2d6393ee58bd63a63cb8", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.38.0.tgz", "fileCount": 3, "integrity": "sha512-u/Jbm1BU89Vftqyqbmxdq14nBaQjQX1HhmsdBWqSdGClNaKwhjsg5TpW+5Ibs1mb8Es9wJiMdl86BcmtUVXNZg==", "signatures": [{"sig": "MEUCIQDF7fLD2TjUp2ZGD3uEO9U2AieKzRXK850Bk2s7XKOswwIgP/xzGKpkYYXsxy/BNdULyDsbZwX2aSo//oLwm0oUYD4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2908144}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "22b64bcc511dfc40ce463e3f662a928915908713", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.38.0_1743229745804_0.06656309954132267", "host": "s3://npm-registry-packages-npm-production"}}, "4.39.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.39.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.39.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "3a3f421f5ce9bd99ed20ce1660cce7cee3e9f199", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.39.0.tgz", "fileCount": 3, "integrity": "sha512-jDrLm6yUtbOg2TYB3sBF3acUnAwsIksEYjLeHL+TJv9jg+TmTwdyjnDex27jqEMakNKf3RwwPahDIt7QXCSqRQ==", "signatures": [{"sig": "MEQCICC6u8p+cDlhxslCMH0gdOHbpAy3xvmA8i46g8p3iua/AiBYNeZLPxe9WbOM1E1VXW5qe3Nh9A+EzshCrk6GDIVCxQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2908144}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "5c001245779063abac3899aa9d25294ab003581b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.39.0_1743569372148_0.9770852055294168", "host": "s3://npm-registry-packages-npm-production"}}, "4.40.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.40.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.40.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "c5bee19fa670ff5da5f066be6a58b4568e9c650b", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.40.0.tgz", "fileCount": 3, "integrity": "sha512-UtZQQI5k/b8d7d3i9AZmA/t+Q4tk3hOC0tMOMSq2GlMYOfxbesxG4mJSeDp0EHs30N9bsfwUvs3zF4v/RzOeTQ==", "signatures": [{"sig": "MEUCIGvszmj+a0odADjIB/GyYQay5WCY+SzK/JfMyZ82iG8AAiEAzHul/fSaMhHO44UviQ7+MZjj4cCYsiCu6aUCSynr5T8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2922480}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "1f2d579ccd4b39f223fed14ac7d031a6c848cd80", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.40.0_1744447175768_0.24776486361186922", "host": "s3://npm-registry-packages-npm-production"}}, "4.40.1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.40.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.40.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "c7724c386eed0bda5ae7143e4081c1910cab349b", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.40.1.tgz", "fileCount": 3, "integrity": "sha512-b2bcNm9Kbde03H+q+Jjw9tSfhYkzrDUf2d5MAd1bOJuVplXvFhWz7tRtWvD8/ORZi7qSCy0idW6tf2HgxSXQSg==", "signatures": [{"sig": "MEQCIGa0dFvBN4Fhpfb/pDfPzZnaTpAgSjOSqEhVOZefuQLSAiBXLTYNP3SyxoWWINE76JKmZM0QzGw12mD4EX9OzVha2w==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2993136}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "1e6c40f49c428b7657fe3b9a2026f705acd39da1", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.40.1_1745814924236_0.3838260235665385", "host": "s3://npm-registry-packages-npm-production"}}, "4.40.2": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.40.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.40.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "6397e1e012db64dfecfed0774cb9fcf89503d716", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.40.2.tgz", "fileCount": 3, "integrity": "sha512-Bjv/HG8RRWLNkXwQQemdsWw4Mg+IJ29LK+bJPW2SCzPKOUaMmPEppQlu/Fqk1d7+DX3V7JbFdbkh/NMmurT6Pg==", "signatures": [{"sig": "MEQCIAtc4krQC8c2DMsZ6b73wRvMkXWJt6ZKMDnTJIjmIH0ZAiAD3IaIcWa0+ze0xDzGYwUCHKYJJdou0HJWPUJNE4fSkg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2997744}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "02da7efedcf373f0f819b78e3acbe50de05d9a5b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.40.2_1746516413105_0.5282701789678799", "host": "s3://npm-registry-packages-npm-production"}}, "4.41.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.41.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.41.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "3b7bbd9f43f1c380061f306abce6f3f64de20306", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.41.0.tgz", "fileCount": 3, "integrity": "sha512-4yodtcOrFHpbomJGVEqZ8fzD4kfBeCbpsUy5Pqk4RluXOdsWdjLnjhiKy2w3qzcASWd04fp52Xz7JKarVJ5BTg==", "signatures": [{"sig": "MEUCIQDv4oNXEwMD46eKqGUKT0gueG5HYfg6DhgY4XBwHYaZLgIgbZLfJrbsPjGyDx9pS6tcIhfJC558lS7Tz0fgAClW3ZY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3107824}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "0928185cd544907dab472754634ddf988452aae6", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.41.0_1747546413268_0.4718916182828139", "host": "s3://npm-registry-packages-npm-production"}}, "4.41.1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.41.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.41.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "7eeada98444e580674de6989284e4baacd48ea65", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.41.1.tgz", "fileCount": 3, "integrity": "sha512-lZkCxIrjlJlMt1dLO/FbpZbzt6J/A8p4DnqzSa4PWqPEUUUnzXLeki/iyPLfV0BmHItlYgHUqJe+3KiyydmiNQ==", "signatures": [{"sig": "MEQCIDwvhnEGjhHmesMBvFY50scMDg0uZZeN9gAxDZRYoGr9AiAe5pby2CV359ieMblV2wcFzMa4k4a8BeZgct9/LnWyhw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3267568}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "7c469dc4eb8e1cb6def9fdc04581fdfce9975da3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.41.1_1748067271557_0.42276067949263796", "host": "s3://npm-registry-packages-npm-production"}}, "4.41.2": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.41.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.41.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "eeea3d6b73f18f3fdb065f86fe028f190e36e4b6", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.41.2.tgz", "fileCount": 3, "integrity": "sha512-LOXSg8GprvL36erslsrNEUirlxy28JcuyTH5PYSBj8wwa0gDQlR8sZricFRbZGCzLhFixvmW2ozj7Mi+j023sg==", "signatures": [{"sig": "MEQCIGRDjNLuQ1euh2EVmJw0VjKKKDrmAUmgQBMHoKGxMmfTAiBfp/NIp+rPPC9s/k/bqY9yyWUQJxXoB9AuxSK3JKSn+A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3271152}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "13b4669dbc21cb738551cd725d2a18c77b3cea11", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.41.2_1749210032389_0.8163569975324525", "host": "s3://npm-registry-packages-npm-production"}}, "4.42.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.42.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.42.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "395ad8b6b6372a3888d2e96bf6c45392be815f4d", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.42.0.tgz", "fileCount": 3, "integrity": "sha512-+axkdyDGSp6hjyzQ5m1pgcvQScfHnMCcsXkx8pTgy/6qBmWVhtRVlgxjWwDp67wEXXUr0x+vD6tp5W4x6V7u1A==", "signatures": [{"sig": "MEUCIQDwsCZYC3J0epdxCT4Vxdhb9x9kRvJa8azXj5sD3BuMXgIgF0G3YluElnFjGqxK/3CI4VLJzX2VnHgvXgNXce7DPGg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3271152}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "f76339428586620ff3e4c32fce48f923e7be7b05", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.42.0_1749221292839_0.6323625923722607", "host": "s3://npm-registry-packages-npm-production"}}, "4.43.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.43.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.43.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "d6d84aace2b211119bf0ab1c586e29d01e32aa01", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.43.0.tgz", "fileCount": 3, "integrity": "sha512-wVzXp2qDSCOpcBCT5WRWLmpJRIzv23valvcTwMHEobkjippNf+C3ys/+wf07poPkeNix0paTNemB2XrHr2TnGw==", "signatures": [{"sig": "MEUCIFluJvPLlPjn7wsVlORzgRHTVov7/XIy6vv5Cx90PUFfAiEA1h3ZSRX0mIdKhMfNKQV4veUkt/g0YDPWAjcwoGBvS9c=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3271152}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "72858cb1474b81c91902794ab7d28c79f34b8ca8", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.43.0_1749619357839_0.06672660166916211", "host": "s3://npm-registry-packages-npm-production"}}, "4.44.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.44.0", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.44.0", "gitHead": "fa4b2842c823f6a61f6b994a28b7fcb54419b6c6", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-M0CpcHf8TWn+4oTxJfh7LQuTuaYeXGbk0eageVjQCKzYLsajWS/lFC94qlRqOlyC2KvRT90ZrfXULYmukeIy7w==", "shasum": "41ffab489857987c75385b0fc8cccf97f7e69d0a", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.44.0.tgz", "fileCount": 3, "unpackedSize": 3244528, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQD4LhHKvWVDIINvxk3BEyrEanBU8mDY/2sbKD0GOYLr6wIhAJ2y8/AZkHQNarkF+i9VCfh3O3nE1PsuodAmd9GF4LM1"}]}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>", "actor": {"name": "lukastaegert", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-win32-arm64-msvc_4.44.0_1750314177023_0.6760716989748929"}, "_hasShrinkwrap": false}}, "time": {"created": "2023-07-31T19:17:54.047Z", "modified": "2025-06-19T06:22:57.523Z", "4.0.0-0": "2023-07-31T19:17:54.259Z", "4.0.0-1": "2023-08-01T04:48:58.136Z", "4.0.0-2": "2023-08-01T11:16:31.094Z", "4.0.0-3": "2023-08-04T08:16:52.930Z", "4.0.0-4": "2023-08-04T11:36:35.688Z", "4.0.0-5": "2023-08-20T06:56:47.845Z", "4.0.0-6": "2023-08-20T07:51:37.043Z", "4.0.0-7": "2023-08-20T10:33:24.107Z", "4.0.0-8": "2023-08-20T11:22:15.348Z", "4.0.0-9": "2023-08-20T14:29:01.774Z", "4.0.0-10": "2023-08-21T15:29:59.660Z", "4.0.0-11": "2023-08-23T10:15:47.876Z", "4.0.0-12": "2023-08-23T14:40:22.126Z", "4.0.0-13": "2023-08-24T15:48:32.762Z", "4.0.0-14": "2023-09-15T12:34:24.977Z", "4.0.0-15": "2023-09-15T13:06:52.042Z", "4.0.0-16": "2023-09-15T14:17:14.546Z", "4.0.0-17": "2023-09-15T14:59:05.626Z", "4.0.0-18": "2023-09-15T16:09:57.202Z", "4.0.0-19": "2023-09-15T18:50:56.415Z", "4.0.0-20": "2023-09-24T06:10:37.075Z", "4.0.0-21": "2023-09-24T17:22:23.293Z", "4.0.0-22": "2023-09-26T16:17:18.268Z", "4.0.0-23": "2023-09-26T20:14:22.052Z", "4.0.0-24": "2023-10-03T05:12:45.045Z", "4.0.0-25": "2023-10-05T14:12:43.378Z", "4.0.0": "2023-10-05T15:14:30.170Z", "4.0.1": "2023-10-06T12:36:38.933Z", "4.0.2": "2023-10-06T14:18:39.981Z", "4.1.0": "2023-10-14T05:52:12.995Z", "4.1.1": "2023-10-15T06:31:46.732Z", "4.1.3": "2023-10-15T17:48:25.413Z", "4.1.4": "2023-10-16T04:34:08.071Z", "4.1.5": "2023-10-28T09:23:30.089Z", "4.1.6": "2023-10-31T05:45:12.880Z", "4.2.0": "2023-10-31T08:10:39.058Z", "4.3.0": "2023-11-03T20:13:01.119Z", "4.3.1": "2023-11-11T07:57:53.402Z", "4.4.0": "2023-11-12T07:49:53.088Z", "4.4.1": "2023-11-14T05:25:08.131Z", "4.5.0": "2023-11-18T05:52:11.149Z", "4.5.1": "2023-11-21T20:13:08.293Z", "4.5.2": "2023-11-24T06:29:46.605Z", "4.6.0": "2023-11-26T13:39:12.964Z", "4.6.1": "2023-11-30T05:23:05.984Z", "4.7.0": "2023-12-08T07:57:59.448Z", "4.8.0": "2023-12-11T06:24:52.398Z", "4.9.0": "2023-12-13T09:24:15.898Z", "4.9.1": "2023-12-17T06:26:10.095Z", "4.9.2": "2023-12-30T06:23:26.758Z", "4.9.3": "2024-01-05T06:20:45.826Z", "4.9.4": "2024-01-06T06:38:59.065Z", "4.9.5": "2024-01-12T06:16:12.667Z", "4.9.6": "2024-01-21T05:52:18.060Z", "4.10.0": "2024-02-10T05:58:40.484Z", "4.11.0": "2024-02-15T06:09:36.756Z", "4.12.0": "2024-02-16T13:32:14.611Z", "4.12.1": "2024-03-06T06:03:32.472Z", "4.13.0": "2024-03-12T05:28:31.652Z", "4.13.1-1": "2024-03-24T07:39:19.926Z", "4.13.1": "2024-03-27T10:27:38.448Z", "4.13.2": "2024-03-28T14:13:33.899Z", "4.14.0": "2024-04-03T05:22:50.470Z", "4.14.1": "2024-04-07T07:35:38.046Z", "4.14.2": "2024-04-12T06:23:38.284Z", "4.14.3": "2024-04-15T07:18:30.488Z", "4.15.0": "2024-04-20T05:37:11.543Z", "4.16.0": "2024-04-21T04:41:50.657Z", "4.16.1": "2024-04-21T18:29:56.812Z", "4.16.2": "2024-04-22T15:19:13.519Z", "4.16.3": "2024-04-23T05:12:33.316Z", "4.16.4": "2024-04-23T13:15:06.116Z", "4.17.0": "2024-04-27T11:29:50.741Z", "4.17.1": "2024-04-29T04:57:53.503Z", "4.17.2": "2024-04-30T05:00:43.448Z", "4.18.0": "2024-05-22T05:03:42.401Z", "4.18.1": "2024-07-08T15:25:10.939Z", "4.19.0": "2024-07-20T05:46:13.771Z", "4.19.1": "2024-07-27T04:54:00.447Z", "4.19.2": "2024-08-01T08:32:52.875Z", "4.20.0": "2024-08-03T04:48:50.287Z", "4.21.0": "2024-08-18T05:55:34.070Z", "4.21.1": "2024-08-26T15:54:12.865Z", "4.21.2": "2024-08-30T07:04:25.676Z", "4.21.3": "2024-09-12T07:05:49.160Z", "4.22.0": "2024-09-19T04:55:31.105Z", "4.22.1": "2024-09-20T08:21:53.172Z", "4.22.2": "2024-09-20T09:33:44.742Z", "4.22.3-0": "2024-09-20T14:47:57.146Z", "4.22.3": "2024-09-21T05:03:08.733Z", "4.22.4": "2024-09-21T06:11:21.956Z", "4.22.5": "2024-09-27T11:48:15.492Z", "4.23.0": "2024-10-01T07:10:06.266Z", "4.24.0": "2024-10-02T09:37:19.700Z", "4.24.1": "2024-10-27T06:43:02.743Z", "4.24.2": "2024-10-27T15:40:07.904Z", "4.25.0-0": "2024-10-29T06:15:10.503Z", "4.24.3": "2024-10-29T14:14:07.677Z", "4.24.4": "2024-11-04T08:47:09.121Z", "4.25.0": "2024-11-09T08:37:22.287Z", "4.26.0": "2024-11-13T06:44:59.566Z", "4.27.0-0": "2024-11-13T07:03:14.160Z", "4.27.0-1": "2024-11-14T06:33:11.015Z", "4.27.0": "2024-11-15T10:40:36.197Z", "4.27.1-0": "2024-11-15T13:28:07.844Z", "4.27.1-1": "2024-11-15T15:38:03.382Z", "4.27.1": "2024-11-15T16:07:42.003Z", "4.27.2": "2024-11-15T17:20:03.555Z", "4.27.3": "2024-11-18T16:39:36.407Z", "4.27.4": "2024-11-23T07:00:21.698Z", "4.28.0": "2024-11-30T13:15:47.859Z", "4.28.1": "2024-12-06T11:44:56.897Z", "4.29.0-0": "2024-12-16T06:39:53.358Z", "4.29.0-1": "2024-12-19T06:37:32.055Z", "4.29.0-2": "2024-12-20T06:56:03.306Z", "4.29.0": "2024-12-20T18:37:25.058Z", "4.29.1": "2024-12-21T07:16:02.859Z", "4.30.0-0": "2024-12-21T07:17:15.337Z", "4.30.0-1": "2024-12-30T06:52:17.065Z", "4.29.2": "2025-01-05T12:07:43.602Z", "4.30.0": "2025-01-06T06:36:41.603Z", "4.30.1": "2025-01-07T10:35:53.302Z", "4.31.0-0": "2025-01-14T05:57:42.900Z", "4.31.0": "2025-01-19T12:56:47.836Z", "4.32.0": "2025-01-24T08:27:35.866Z", "4.33.0-0": "2025-01-28T08:30:09.507Z", "4.32.1": "2025-01-28T08:33:18.309Z", "4.33.0": "2025-02-01T07:11:59.808Z", "4.34.0": "2025-02-01T08:40:24.070Z", "4.34.1": "2025-02-03T06:58:14.244Z", "4.34.2": "2025-02-04T08:10:02.853Z", "4.34.3": "2025-02-05T09:22:05.218Z", "4.34.4": "2025-02-05T21:31:11.562Z", "4.34.5": "2025-02-07T08:53:02.736Z", "4.34.6": "2025-02-07T16:32:08.106Z", "4.34.7": "2025-02-14T09:53:59.463Z", "4.34.8": "2025-02-17T06:26:25.882Z", "4.34.9": "2025-03-01T07:32:37.070Z", "4.35.0": "2025-03-08T06:24:44.703Z", "4.36.0": "2025-03-17T08:35:44.165Z", "4.37.0": "2025-03-23T14:57:07.923Z", "4.38.0": "2025-03-29T06:29:06.074Z", "4.39.0": "2025-04-02T04:49:32.369Z", "4.40.0": "2025-04-12T08:39:35.968Z", "4.40.1": "2025-04-28T04:35:24.474Z", "4.40.2": "2025-05-06T07:26:53.317Z", "4.41.0": "2025-05-18T05:33:33.495Z", "4.41.1": "2025-05-24T06:14:31.783Z", "4.41.2": "2025-06-06T11:40:32.620Z", "4.42.0": "2025-06-06T14:48:13.088Z", "4.43.0": "2025-06-11T05:22:38.072Z", "4.44.0": "2025-06-19T06:22:57.281Z"}, "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "homepage": "https://rollupjs.org/", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "description": "Native bindings for Rollup", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "readme": "# `@rollup/rollup-win32-arm64-msvc`\n\nThis is the **aarch64-pc-windows-msvc** binary for `rollup`\n", "readmeFilename": "README.md"}