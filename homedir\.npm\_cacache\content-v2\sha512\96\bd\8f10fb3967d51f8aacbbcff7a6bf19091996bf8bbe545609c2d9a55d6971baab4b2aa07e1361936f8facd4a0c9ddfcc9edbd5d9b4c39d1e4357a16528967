{"_id": "locate-path", "_rev": "18-cc7b2e5c1dd3837e69c89e1fe8daf50a", "name": "locate-path", "description": "Get the first path that exists on disk of multiple paths", "dist-tags": {"latest": "7.2.0"}, "versions": {"1.0.0": {"name": "locate-path", "version": "1.0.0", "description": "Get the first path that exists on disk of multiple paths", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/locate-path.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["locate", "path", "paths", "file", "files", "exists", "find", "finder", "search", "searcher", "array", "iterable", "iterator"], "dependencies": {"p-locate": "^1.0.0", "path-exists": "^3.0.0"}, "devDependencies": {"ava": "*", "xo": "*"}, "xo": {"esnext": true}, "gitHead": "53677e8b93f38e84061d131d3d9455a3509bfa58", "bugs": {"url": "https://github.com/sindresorhus/locate-path/issues"}, "homepage": "https://github.com/sindresorhus/locate-path#readme", "_id": "locate-path@1.0.0", "_shasum": "cacf3ab8bb8a3dee6e7f011d0c850f5d516fea1a", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.6.2", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "cacf3ab8bb8a3dee6e7f011d0c850f5d516fea1a", "tarball": "https://registry.npmjs.org/locate-path/-/locate-path-1.0.0.tgz", "integrity": "sha512-r7QN+JQSXE47x/FrbFx5QVcbfB1Icrg/VWiza2Cc07zaZlwF+4xNfUoaavWtK2fnUwokJrJ32eTly/1Zb/zzyg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE+sKAbfOaj4c5Xhz2uetubyQ02+bwnpiRjNmM7MTfJYAiEAniWIP75kaspzTPnialsiBbLpWXE1Yl8FaF+Oqm5vqPw="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/locate-path-1.0.0.tgz_1479637649308_0.8211715335492045"}, "directories": {}}, "1.1.0": {"name": "locate-path", "version": "1.1.0", "description": "Get the first path that exists on disk of multiple paths", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/locate-path.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["locate", "path", "paths", "file", "files", "exists", "find", "finder", "search", "searcher", "array", "iterable", "iterator"], "dependencies": {"p-locate": "^1.0.0", "path-exists": "^3.0.0"}, "devDependencies": {"ava": "*", "xo": "*"}, "xo": {"esnext": true}, "gitHead": "d8ffb20e1aa08e382e61a95e4aa1aae66dcafb0d", "bugs": {"url": "https://github.com/sindresorhus/locate-path/issues"}, "homepage": "https://github.com/sindresorhus/locate-path#readme", "_id": "locate-path@1.1.0", "_shasum": "bef5eea5d511d2e7438dfeedfc6af8ce6a4dd811", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.6.2", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "bef5eea5d511d2e7438dfeedfc6af8ce6a4dd811", "tarball": "https://registry.npmjs.org/locate-path/-/locate-path-1.1.0.tgz", "integrity": "sha512-hGouv6U7pO5rMJKVJT2te8F0QRSG58ChWKKj4dXEeYmJbJr8GZgQGULJZUeuRM6ORDlVyaWAPhN7FDZ+SePqng==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAV8DAaDAU4XHDus9okWVv8SpUxnTNYJ8WDOBQWOjU1HAiEAgeyH9EFyVogqza8/23OAnj33/DDqP6Cdr+jHkdXiDGs="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/locate-path-1.1.0.tgz_1479648048095_0.9578655750956386"}, "directories": {}}, "2.0.0": {"name": "locate-path", "version": "2.0.0", "description": "Get the first path that exists on disk of multiple paths", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/locate-path.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["locate", "path", "paths", "file", "files", "exists", "find", "finder", "search", "searcher", "array", "iterable", "iterator"], "dependencies": {"p-locate": "^2.0.0", "path-exists": "^3.0.0"}, "devDependencies": {"ava": "*", "xo": "*"}, "xo": {"esnext": true}, "gitHead": "a30b86df0934329c66ff6a2be395db03d65478b8", "bugs": {"url": "https://github.com/sindresorhus/locate-path/issues"}, "homepage": "https://github.com/sindresorhus/locate-path#readme", "_id": "locate-path@2.0.0", "_shasum": "2b568b265eec944c6d9c0de9c3dbbbca0354cd8e", "_from": ".", "_npmVersion": "3.10.9", "_nodeVersion": "7.2.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "2b568b265eec944c6d9c0de9c3dbbbca0354cd8e", "tarball": "https://registry.npmjs.org/locate-path/-/locate-path-2.0.0.tgz", "integrity": "sha512-NCI2kiDkyR7VeEKm27Kda/iQHyKJe1Bu0FlTbYp3CqJu+9IFe9bLyAjMxf5ZDDbEg+iMPzB5zYyUTSm8wVTKmA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDtzHbXFgFvWfSFLuiSixV76I9KyhMpEgkWbAX2GKzLTwIgZhNRULv57/cJzLTXA3uMzyOMtnfX674PYpKJHOxSpR4="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/locate-path-2.0.0.tgz_1480310853492_0.9661909360438585"}, "directories": {}}, "3.0.0": {"name": "locate-path", "version": "3.0.0", "description": "Get the first path that exists on disk of multiple paths", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/locate-path.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["locate", "path", "paths", "file", "files", "exists", "find", "finder", "search", "searcher", "array", "iterable", "iterator"], "dependencies": {"p-locate": "^3.0.0", "path-exists": "^3.0.0"}, "devDependencies": {"ava": "*", "xo": "*"}, "gitHead": "d6f19ffa31544161ee94118963deb3b683418151", "bugs": {"url": "https://github.com/sindresorhus/locate-path/issues"}, "homepage": "https://github.com/sindresorhus/locate-path#readme", "_id": "locate-path@3.0.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.2", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-7AO748wWnIhNqAuaty2ZWHkQHRSNfPVIsPIfwEOWO22AmaoVrWavlOcMR5nzTLNYvp36X220/maaRsrec1G65A==", "shasum": "dbec3b3ab759758071b58fe59fc41871af21400e", "tarball": "https://registry.npmjs.org/locate-path/-/locate-path-3.0.0.tgz", "fileCount": 4, "unpackedSize": 3869, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbJsuvCRA9TVsSAnZWagAAKJ8P/j0FJhuUso5Gpt4CxJDh\n8BOK0XgQpBa7/ZywwygXnB5QuPo+Fe+G4t3mrsc+9MRJrbja4q0eqrtutwnb\n3EYSokKlQj+++9DAMyEamET3XbQVKypSkJ/phXXrzYpSafRkM6H7Zmh1PLIK\nuoGtpbYiGATaUFqbz+sYwkQ0RTweEQTfG+XvgOLVviCDF2Q5TE1wD+w/Lk4V\noGyu1vH1O6nyGuI8+OCZtO/7ub628/5n1WmgYId4CG9umDRPAiIKaQ6IXkhI\nmLy/4FbDGW5dn95YfHxKiJyzJOyE3EagyOEDANkO58htWE2WxdTlHLJrKVgD\ngS6ggUfTrslD7gQYNW3mwBEIBxTeRP7FSdLLEhGsJ7x9+bGEdJnNeKHVwNMX\nujKGOYre3xhwOfzNY+RzmHCXDRxf1gDXFcLQASxaS2CkcYjxDukhDxqL0Egi\n90o0xCX8tEGxuTvDC/1Zlxvin+SWWvPgWHgO0FVZyLEwaM/kL6YZPHQ5Qqg2\n1g+kQoMjFwxqXnY14nPIqPqzsLF4XXtnWr6PI9pZacJfU+OCT14b9vcSmQYY\nf6QnwppSq2ND7SnwVrXMB+JSZSrOMoGf5O2XVyhh3YipmryXp+NauT1GIkAd\nWO5/KdhVqUM7EGFAS8y0kchxFRzswxE7aGqn+o2OqNpKQtBXmfkI3547ncrc\n7Exq\r\n=AQcu\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHyfA/w1Yzm/BICvJYkSll/71wk4vQGAueFNY2ErQ9CdAiBp5eocaGZTujCy0OBq9UiX1uubY0c8ldW+t1m+2RGNNg=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/locate-path_3.0.0_1529269166609_0.06080172995584565"}, "_hasShrinkwrap": false}, "4.0.0": {"name": "locate-path", "version": "4.0.0", "description": "Get the first path that exists on disk of multiple paths", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/locate-path.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["locate", "path", "paths", "file", "files", "exists", "find", "finder", "search", "searcher", "array", "iterable", "iterator"], "dependencies": {"p-locate": "^4.1.0", "path-exists": "^4.0.0"}, "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}, "gitHead": "ca633cfe3fd4826a7b20dea83f92a152197947fb", "bugs": {"url": "https://github.com/sindresorhus/locate-path/issues"}, "homepage": "https://github.com/sindresorhus/locate-path#readme", "_id": "locate-path@4.0.0", "_nodeVersion": "8.15.0", "_npmVersion": "6.9.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-z/S+3QXsYrP1Cx2ATKAC1nWDc62X+/kE8idJL5O16l28nPTy1hv1z921vSBbrTwL3F9o5aHDSrzHg7Cgy5OGlw==", "shasum": "c52693fa4f3222cd306ba5302e7250bf9b94d05d", "tarball": "https://registry.npmjs.org/locate-path/-/locate-path-4.0.0.tgz", "fileCount": 5, "unpackedSize": 5255, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJctKm7CRA9TVsSAnZWagAA8sUP/RJrMVKlgUDjbmyXflml\nWBBerBKTqKVH+K0iIlIwzBZc9QcorJ81rHWwPIyQ4DtK+V4ZKI6JMHtlrNxw\nFHjLlmvSCGoYF+dBZozFH0iW1befu/E1ND08w8pMhUAEhkKmH/WP6MWZ3qsi\n7dwFA4idr6j8e0ztWVDmem/mliAQNsrm8pBM4xHhorBO5YlRZ5xhxF1HMbt1\nLPDgy4Voh3v9Z0PIVB9VDAnCoiiXlLSv4U/ur15iaRXHzAiNCK4UTBON/MP7\nD+SmJitW9eiC6yX361aGbZCMb79dMUFIAWRbeQ841EOgt3hgHDFfHyT4IQ9O\nrVPTPZP/dk7vVlBrsBR61DCBxixylBqf6+IhYmdE56nIiRASYJmrB19V2P/v\n4WIAkZsBCIO7ImbPihO0FtsGofeWEwsAEWlkBRX4w4eFVMRJ4Mea5SKh9bVu\ns4yo00vRP95dN/9+h3TmX0ID0NENdEmeJiNa+qRQ4YlFe4v8WG1cEefQWltO\np3gXs/qcHGaZTWO3HUPLZMk0BvK8TALkR965TgcFPuk0wOPgwxSy2h4hEcTi\nOAOjTShbGrU8IG6bbTOnPIewssqqSM3ZwCgjeRHuYlV4Jirlmqv2t2hsfE1q\nGs2y/tGf5w4Uv3P1UTKdppSz0NvXVK7sWt+CN9epvJBCzVKSNleID0OK8DrO\n0Jql\r\n=5JJe\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBtQAbyaD7JgJmPmHnDRdEBnxlvIo0FmKqSjQ8c88QfLAiBq8K37gql6QH/i9ZufqFrdfn5kM5Y9kyzH4acpi5Gukw=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/locate-path_4.0.0_1555343802859_0.6905435600537719"}, "_hasShrinkwrap": false}, "5.0.0": {"name": "locate-path", "version": "5.0.0", "description": "Get the first path that exists on disk of multiple paths", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/locate-path.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["locate", "path", "paths", "file", "files", "exists", "find", "finder", "search", "searcher", "array", "iterable", "iterator"], "dependencies": {"p-locate": "^4.1.0"}, "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}, "gitHead": "630c0bbd609055c1895089c715649e21000a1ab7", "bugs": {"url": "https://github.com/sindresorhus/locate-path/issues"}, "homepage": "https://github.com/sindresorhus/locate-path#readme", "_id": "locate-path@5.0.0", "_nodeVersion": "8.16.0", "_npmVersion": "6.9.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g==", "shasum": "1afba396afd676a6d42504d0a67a3a7eb9f62aa0", "tarball": "https://registry.npmjs.org/locate-path/-/locate-path-5.0.0.tgz", "fileCount": 5, "unpackedSize": 6584, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJczYzYCRA9TVsSAnZWagAAoH4QAJ55Du9nat4GZgsgC/+d\nU8skY+UkpMg7sYrxcLFg7iL/6DUrc1QcavNPbQxANFOubfMvadGmSOZjiZeD\nMY2Sf+01Odcj6kW+hpzO3X03by1zzggXvuX4Wzw2L+XPK9yd2zhQViblkr27\nJzp68UqKwEpq4tQtld0nS31cxEe4Rqjeh7i4f1sDJBxix2CEglgX2/HcnAcU\n/SELUZtunAgRBBIons8FdjL2VOhcesPD2fh8ebBpoSmojDUgzlGIWagsmO6X\nYn0ftdcv77ILQBDitLjPWTGK1riAvfJkSOlK2J0LEFLR//yQByICmvPzXr0x\nZom6AYCdhUy5PJkaPuxEMuKJo5DrXuV+cKN9z4Ek5kumOl4H4sR+viKC+/b4\nX96Vxt0eZfT2nERjA31raJtQQVjyJUrt3QZRhf3CUItMDqE0s6+R2abAzUoA\nmF+dIzizSSZKdfy6/sVVcht4ZyxkgMSBtN6q4Pw/aNUDJEWYfqUZ1b9ucMif\nwLpP2MIH6GLQhKoFymqZx7jWzqGduKX25vS9RLZh6keyF3sZMQ618qfbX5cv\n0PJqf/YOx9N+PUeSW+Eu+JFlXlGvpZV4gLlTIlNIRsyHHavLWpw58q9fuLGy\nqFn1eegD45ITp3WdCCqRT+FTrQwomNcnLoN1ESw/fpJVWRG81RWGwaEj43NO\ntmj8\r\n=qQUv\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF++mOopsOXeFIQqhPvLx8mgWBCx8VcO2mxuq6Fwv2/dAiEA2AG4MBpSq944pjJqxWTDhoYUIEgjI4nWP7GEtqc4OIY="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/locate-path_5.0.0_1556974807265_0.2598589421080095"}, "_hasShrinkwrap": false}, "6.0.0": {"name": "locate-path", "version": "6.0.0", "description": "Get the first path that exists on disk of multiple paths", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/locate-path.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=10"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["locate", "path", "paths", "file", "files", "exists", "find", "finder", "search", "searcher", "array", "iterable", "iterator"], "dependencies": {"p-locate": "^5.0.0"}, "devDependencies": {"ava": "^2.4.0", "tsd": "^0.13.1", "xo": "^0.32.1"}, "gitHead": "3f2dd51787dd7fe22658af7ab817ec542de93b1b", "bugs": {"url": "https://github.com/sindresorhus/locate-path/issues"}, "homepage": "https://github.com/sindresorhus/locate-path#readme", "_id": "locate-path@6.0.0", "_nodeVersion": "14.5.0", "_npmVersion": "6.14.7", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==", "shasum": "55321eb309febbc59c4801d931a72452a681d286", "tarball": "https://registry.npmjs.org/locate-path/-/locate-path-6.0.0.tgz", "fileCount": 5, "unpackedSize": 7017, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfMYiXCRA9TVsSAnZWagAAyK8P/3SNlcCQmgFF6osCwtX0\nHZHL9+x3un+fWB9xVeOxQs5zsVJ3u30+/eGk/L4hLTLdbUOGkfOtynZVVlLB\nVscrbZ6P5pC9xXxva47U1wKG8/tLX0hgA5VgSMLdm3+1ntyGy4PBd5Vq1jP6\nreN2XTCnU52xUM4WJoto21+VrrxeeM0iJBaWCNZQ016Su3BSlb0R2NX81oVD\nmXY/HAYGrVl4gmfIzU/DCR3wVEo7Nn/tSttLPqpp8A0CqWO9CdE8vC0yq1nx\ncR1Rk3xHY4eY+6n2+zB+yTYT2Kh4NI6zLVrqOkrlFCRZBZTas2UH6Txz0I4v\nuJKkBwepTYVJ/CghYPVkRwBZ5xaBTWUpqCAUaEl69QG/ep7xuVorMwRCO6CT\n6fAkQKtUNYnip6bpCBG/OLdPXDlYsyH+FlzIHBcwEf9IWUCrPlRwLuxwdrb9\nok5YB/mneYXsLRCtDaG+uYqx0RrWWAlotkN8mbFUBGCCZ1xOPwrnQ+wULdI+\nb0OHtZGk2FHY2ZouQNlyLPOJj2BKOoS0JoO4KCKxrCAN2jB3WjGwI/Ho03nk\nNMaUciSfAKFNqaKWTPE8HxKRkUuaqO+bdjiZd/YhsXzV+woNkWSPiBpgCrk9\n7gzhx42b6wJEfnFpSXjhpwA83xfjJ0jUMf2vwP6GVXVWxB1pT+d/AzW1U6tT\nksQO\r\n=UukL\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICb64A70FhaJxQ5pK28ZBiYOfWeFgVyL01SM5YyyAyt+AiEArn89bj/M84KSel6MjMrt3f3iLi6kCMlw2ntgaIv5Ms8="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/locate-path_6.0.0_1597081750688_0.7564472004669758"}, "_hasShrinkwrap": false}, "7.0.0": {"name": "locate-path", "version": "7.0.0", "description": "Get the first path that exists on disk of multiple paths", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/locate-path.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["locate", "path", "paths", "file", "files", "exists", "find", "finder", "search", "searcher", "array", "iterable", "iterator"], "dependencies": {"p-locate": "^6.0.0"}, "devDependencies": {"ava": "^3.15.0", "tsd": "^0.17.0", "xo": "^0.44.0"}, "gitHead": "74aba6bd8b7a1ca1b2e8766c697f2ec5cc1b9bf1", "bugs": {"url": "https://github.com/sindresorhus/locate-path/issues"}, "homepage": "https://github.com/sindresorhus/locate-path#readme", "_id": "locate-path@7.0.0", "_nodeVersion": "16.7.0", "_npmVersion": "7.20.3", "dist": {"integrity": "sha512-+cg2yXqDUKfo4hsFxwa3G1cBJeA+gs1vD8FyV9/odWoUlQe/4syxHQ5DPtKjtfm6gnKbZzjCqzX03kXosvZB1w==", "shasum": "f0a60c8dd7ef0f737699eb9461b9567a92bc97da", "tarball": "https://registry.npmjs.org/locate-path/-/locate-path-7.0.0.tgz", "fileCount": 5, "unpackedSize": 6987, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhJjuzCRA9TVsSAnZWagAAbdsP/jau5xOfGKLoxeMXJ92c\noPSoonrWKxnqA/lwXAab9iqqe24x/UrkleTEHxmdzJnqj2sfxNAQwElKwGxv\n+/193p4sir4vrpPqCmRxgEeqxgn+3UGsgQzbLvcLj0XjZH/ZwI8E4UoQLDoq\nspCM1N67oYf4KFYtAWE2/B3gJidnkp8ABPRV2WyHGxX7pvSbci28s4YcpBlu\nqhp41beeosEuqZ8d+jWOVpUdc08Fj2laziBMr3gxITEfzbLc/Qf7+JieiITp\ncz8dZ8wHJt2hdLYpDTzww+bLv+jJmSeRHBYPrz2SfbmpVIiGSuVWFas/XLPX\nSrk5q+KNMVGZbs9lcrFakYdRFZzgFsY/Ll9sN2CW5oLWUO+BsEh5Qc/wpJcj\nRuAtROf5jFsjfk4oZy8CQYDAtj1m7mj0/wTHVdPyNk9RPWuNNdOcb60gVP7H\nG6CwcFonZCcrK4ZFoiS4dpbW5ewImdT7B7D60BLoL6E5NcvOb17/8gk/MlmX\n4hNGZMz/tZOncotOwO5jAJpfy8PnvqH+XYfm3u3EqwB18GdCFJ8ZJEM66fqI\ny9jFXpGgzFr6S0tZ1HVx7uqHm22Ji2qFZG23ztWP5gfhzsxF5EeCOJKxWEki\nAntfCJd2GJDdRtOKmPaG8iotL97Pl2PNKqP9ScCkwf4Ldv/zhmJzZQ2ripBe\nKJoo\r\n=1jZm\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDszx6oB9BEIi0tcnUYUBZtW7dabyD9FdbnInzu5nXayAIgTet7EtBcwqNO/XIJdB/Nx0zJB5nxdh0H/9B0U+Ojp8s="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/locate-path_7.0.0_1629895603416_0.9802738678936131"}, "_hasShrinkwrap": false}, "7.1.0": {"name": "locate-path", "version": "7.1.0", "description": "Get the first path that exists on disk of multiple paths", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/locate-path.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["locate", "path", "paths", "file", "files", "exists", "find", "finder", "search", "searcher", "array", "iterable", "iterator"], "dependencies": {"p-locate": "^6.0.0"}, "devDependencies": {"ava": "^3.15.0", "tsd": "^0.17.0", "xo": "^0.44.0"}, "types": "./index.d.ts", "gitHead": "42df341fcd05f6af53f508a96a9fd826cfafa575", "bugs": {"url": "https://github.com/sindresorhus/locate-path/issues"}, "homepage": "https://github.com/sindresorhus/locate-path#readme", "_id": "locate-path@7.1.0", "_nodeVersion": "16.13.1", "_npmVersion": "8.3.2", "dist": {"integrity": "sha512-HNx5uOnYeK4SxEoid5qnhRfprlJeGMzFRKPLCf/15N3/B4AiofNwC/yq7VBKdVk9dx7m+PiYCJOGg55JYTAqoQ==", "shasum": "241d62af60739f6097c055efe10329c88b798425", "tarball": "https://registry.npmjs.org/locate-path/-/locate-path-7.1.0.tgz", "fileCount": 5, "unpackedSize": 7172, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiAg4JCRA9TVsSAnZWagAAzCEP/3bsM4XniBpaoiTZnhIA\noF68FXGX6pR7NreLwXPllAaSMQl8Fay80uYCo6Q8N19ckBjdPIhXM4ZmZBS9\npxy3Bjei9yFUZVtkUlFTACxyWGQ8Ay2YqwCbmqCgQXbOweBUeMEjqavS3fhK\nu58tQHM4jijqLl06Cse77egDfzHFUi6NxATzDls192eqFdltq2hUEHSYvi3c\nPufr9zUbMyEfEXUQHBiX86keUJHF4rA4/hpBI/ElLTZOth4x4WR24Se1Orwf\n/BpJ7Ug6ZLNBPpC89bB1Kt6woIWuFK14VhO20HaPHzjOTBBBElP8mB/zAIds\n+j77AIItmwTbfQnAr/9pDmzjTukEE6+q2DSbMgOgvHVwrSCMZspkW9BS67u/\n5gxrZi4jPMvMlTd+o9v7CMzAZpY2nbjSHkD6bE7u3tzXNkult9HZl0X1DGnh\nxs5LjV/PA+6DNJdWpRZDPu2t+KcPtGLjwpLjxk7J927mApVuzZ75zMbkFsPJ\nbWPeflrNzsYOYxPRn1/rGrz14ipnHXH/wuL/xYp8pdi9VXcfZIjtCYn51ddz\nintlHWTrw5qOjbWIawoK1A6QjuEYcrbM1HVDN1WMl8HwI7bu3G3xhu032bcN\nEr+fyHCg1xxTOXToFOigU5GYRtT4SqIlHbVmsIrjmx9xh+3SA+fw/fjOTUDE\np9h1\r\n=zQ8M\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFV6hRwIAwFYlF+VZcWOeci1HugyJxXf7ImYb5Oi7p0mAiA8p+WkeY3eCq+cVAUarFAKHsVtL4dSClbFxcWQg2+18A=="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/locate-path_7.1.0_1644301833685_0.6324694567378937"}, "_hasShrinkwrap": false}, "7.1.1": {"name": "locate-path", "version": "7.1.1", "description": "Get the first path that exists on disk of multiple paths", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/locate-path.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["locate", "path", "paths", "file", "files", "exists", "find", "finder", "search", "searcher", "array", "iterable", "iterator"], "dependencies": {"p-locate": "^6.0.0"}, "devDependencies": {"ava": "^3.15.0", "tsd": "^0.17.0", "xo": "^0.44.0"}, "types": "./index.d.ts", "gitHead": "4a5368c38e808a62d86010950e6c1ff2d856b05b", "bugs": {"url": "https://github.com/sindresorhus/locate-path/issues"}, "homepage": "https://github.com/sindresorhus/locate-path#readme", "_id": "locate-path@7.1.1", "_nodeVersion": "14.19.2", "_npmVersion": "8.3.2", "dist": {"integrity": "sha512-vJXaRMJgRVD3+cUZs3Mncj2mxpt5mP0EmNOsxRSZRMlbqjvxzDEOIUWXGmavo0ZC9+tNZCBLQ66reA11nbpHZg==", "shasum": "8e1e5a75c7343770cef02ff93c4bf1f0aa666374", "tarball": "https://registry.npmjs.org/locate-path/-/locate-path-7.1.1.tgz", "fileCount": 5, "unpackedSize": 7176, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHNrtxYcqHHIMti0JiEWG7JlkXMpXI+YkLnXkPzho6O2AiEAzJgoHLhNF0uqKBJFbS4gN//uHmuMGi1qSPtJHd2oK5w="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJinWzEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpWMw//TCETeGsWpBJbiZLHiFeJQ09x3v9oE7Yo+V08jK7GURjs30b9\r\nVZjH0C92lw4EwpmcCUMW0aQIBrf7rRm7TC9bZBj8pIIE3Xm0eFGUbdvisMTX\r\nJWvrukibFUyBerlPa9EkPjCSgfRTmtBIG09OJrjug1imJ/OMXUAr69oj5CGr\r\n4OPWrqyInXx78PJyf8c925hi638clVoMgJvHgqnVE1aQ1ktL8JBHShKcdadE\r\n7uw064ExjY2v3a1kUKO43IdiXbXLtbc2f8xux+x2TTy7ZgClHvkismrIK3le\r\n7wF3OGDURSlZi/GxqlAEVNFQ7NFuFy/TLhdqME+Fe34fwP3Te64lmOUlHxTz\r\nyhaeM0+WABWs9vaSNhc5pmwuQJ768G4B/8gO1XDOkD7n9OYGI0xLLuywONCC\r\nl3d/1+yaTEphexb51hYb2CZSOaDulKtTcIm4fiHzuHfTM4ju4rbaEm5E5M3y\r\nAyIQIfqV65lpAByricXcW0KbOH0FCAgk9hKMCi+xMqhNCvnfKg/QvAVJvwU9\r\nsE7H0JA+CPBl4v51NVXfOn3LXygIbw+7Uwo+g+5YtiGZQRvERYyEm2AVVcxS\r\nymhH08ESTobKbYnAsAFCmW+Sqepl4ne/PQPIEVmasWtGMF1GyF7aUhyxs9Hu\r\nBjBbT2XpLHvQaUAuMHSqZ09cyKGaLazq738=\r\n=sZ5P\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/locate-path_7.1.1_1654484164086_0.9476282177943742"}, "_hasShrinkwrap": false}, "7.2.0": {"name": "locate-path", "version": "7.2.0", "description": "Get the first path that exists on disk of multiple paths", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/locate-path.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["locate", "path", "paths", "file", "files", "exists", "find", "finder", "search", "searcher", "array", "iterable", "iterator"], "dependencies": {"p-locate": "^6.0.0"}, "devDependencies": {"ava": "^3.15.0", "tsd": "^0.17.0", "xo": "^0.44.0"}, "types": "./index.d.ts", "gitHead": "355a681456d79a8506de11120d56b6e34a0389b5", "bugs": {"url": "https://github.com/sindresorhus/locate-path/issues"}, "homepage": "https://github.com/sindresorhus/locate-path#readme", "_id": "locate-path@7.2.0", "_nodeVersion": "14.21.1", "_npmVersion": "9.2.0", "dist": {"integrity": "sha512-gvVijfZvn7R+2qyPX8mAuKcFGDf6Nc61GdvGafQsHL0sBIxfKzA+usWn4GFC/bk+QdwPUD4kWFJLhElipq+0VA==", "shasum": "69cb1779bd90b35ab1e771e1f2f89a202c2a8a8a", "tarball": "https://registry.npmjs.org/locate-path/-/locate-path-7.2.0.tgz", "fileCount": 5, "unpackedSize": 7247, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCbQFsaKA58xZNo/6+mebE/n0YPFGQ891HI29MYXHINTAIgOkYv0LQfkGOKQZr+LMn57eoNs62qMQZBNOCZUU0XWuE="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj5K7/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoRqg/9HUXBE5q5sI5ty76/B+mp2eRYwiO6fg0hyOwgnloMVTH0ZT1N\r\ng3oqdr/O3Fxj1q/C/+/IuV8KHdYUoV1cq6jDHtiIbfDenUTorj/hGqsal6Tf\r\nZVt3r4c/pm61VmiLVs2jqL2AW0CX9DoftKMfMY5s6RkW3GhowUCppyE5c2IM\r\n9J92dICo3/w2oiosi8BHT+ea85zqkbnbemQ5rAVkoO4mObzpK4G12EqBKxBR\r\nq53WycYV7jFUsNJwBzWmNMSXGO267DhmlXXTIZ8D4Ftg5T89MqEuimOCi2gB\r\n4v7YUPPZvqwIQUGhku5R4IYINIskSXZ2l/nw48HBBkkyvc7/H4r8sqQO0fqh\r\nQm3hwlRFhZqFA8y092i3yDw14LczNafQYgz2jGO8YuMwlUTRwGGzOQtsdWX8\r\nkZY/wp3KTNVTjKtlb6wnnddPFedmOBLu7B1tz6s86ZvfhI2YxTfJDvvM7l/l\r\n3XpXYET81zfNoKRdYRSp0JeEcHRzvq+cnhySjCg9tl2VjEYIECK/homxS9LB\r\nvnS6i4qfHyMC+gY7z+009A7g88m3bh6B2umxIFjiKMLqwWMlEf9J8EKFhaXz\r\n1C0sB1zsGc2hoau1MITNnfy1+W15bimJwBQqSY8ImwUWBywc8O0pXfz5FjFs\r\ne9LcQ9Yk1R7LHfmyMzgG7BMnfyms89Z+TJo=\r\n=teux\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/locate-path_7.2.0_1675931391521_0.6232746209558382"}, "_hasShrinkwrap": false}}, "readme": "# locate-path\n\n> Get the first path that exists on disk of multiple paths\n\n## Install\n\n```\n$ npm install locate-path\n```\n\n## Usage\n\nHere we find the first file that exists on disk, in array order.\n\n```js\nimport {locatePath} from 'locate-path';\n\nconst files = [\n\t'unicorn.png',\n\t'rainbow.png', // Only this one actually exists on disk\n\t'pony.png'\n];\n\nconsole(await locatePath(files));\n//=> 'rainbow'\n```\n\n## API\n\n### locatePath(paths, options?)\n\nReturns a `Promise<string>` for the first path that exists or `undefined` if none exists.\n\n#### paths\n\nType: `Iterable<string>`\n\nThe paths to check.\n\n#### options\n\nType: `object`\n\n##### concurrency\n\nType: `number`\\\nDefault: `Infinity`\\\nMinimum: `1`\n\nThe number of concurrently pending promises.\n\n##### preserveOrder\n\nType: `boolean`\\\nDefault: `true`\n\nPreserve `paths` order when searching.\n\nDisable this to improve performance if you don't care about the order.\n\n##### cwd\n\nType: `URL | string`\\\nDefault: `process.cwd()`\n\nThe current working directory.\n\n##### type\n\nType: `string`\\\nDefault: `'file'`\\\nValues: `'file' | 'directory'`\n\nThe type of paths that can match.\n\n##### allowSymlinks\n\nType: `boolean`\\\nDefault: `true`\n\nAllow symbolic links to match if they point to the chosen path type.\n\n### locatePathSync(paths, options?)\n\nReturns the first path that exists or `undefined` if none exists.\n\n#### paths\n\nType: `Iterable<string>`\n\nThe paths to check.\n\n#### options\n\nType: `object`\n\n##### cwd\n\nSame as above.\n\n##### type\n\nSame as above.\n\n##### allowSymlinks\n\nSame as above.\n\n## Related\n\n- [path-exists](https://github.com/sindresorhus/path-exists) - Check if a path exists\n\n---\n\n<div align=\"center\">\n\t<b>\n\t\t<a href=\"https://tidelift.com/subscription/pkg/npm-locate-path?utm_source=npm-locate-path&utm_medium=referral&utm_campaign=readme\">Get professional support for this package with a Tidelift subscription</a>\n\t</b>\n\t<br>\n\t<sub>\n\t\tTidelift helps make open source sustainable for maintainers while giving companies<br>assurances about security, maintenance, and licensing for their dependencies.\n\t</sub>\n</div>\n", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2023-06-17T00:12:11.255Z", "created": "2016-11-20T10:27:29.562Z", "1.0.0": "2016-11-20T10:27:29.562Z", "1.1.0": "2016-11-20T13:20:48.318Z", "2.0.0": "2016-11-28T05:27:33.711Z", "3.0.0": "2018-06-17T20:59:26.704Z", "4.0.0": "2019-04-15T15:56:42.988Z", "5.0.0": "2019-05-04T13:00:07.428Z", "6.0.0": "2020-08-10T17:49:10.766Z", "7.0.0": "2021-08-25T12:46:43.621Z", "7.1.0": "2022-02-08T06:30:33.830Z", "7.1.1": "2022-06-06T02:56:04.260Z", "7.2.0": "2023-02-09T08:29:51.700Z"}, "homepage": "https://github.com/sindresorhus/locate-path#readme", "keywords": ["locate", "path", "paths", "file", "files", "exists", "find", "finder", "search", "searcher", "array", "iterable", "iterator"], "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/locate-path.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/locate-path/issues"}, "license": "MIT", "readmeFilename": "readme.md", "users": {"aretecode": true, "flumpus-dev": true}}