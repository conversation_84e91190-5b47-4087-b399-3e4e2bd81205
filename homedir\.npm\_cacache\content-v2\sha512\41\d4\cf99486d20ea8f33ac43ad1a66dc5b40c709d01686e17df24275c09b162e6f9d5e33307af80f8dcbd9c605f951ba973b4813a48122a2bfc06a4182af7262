{"_id": "schema-utils", "_rev": "56-fafc68df1a981d1aaa9476106d038ec7", "name": "schema-utils", "dist-tags": {"version-3": "3.3.0", "latest": "4.3.2"}, "versions": {"0.1.0": {"name": "schema-utils", "version": "0.1.0", "keywords": ["webpack", "plugin", "es2015"], "author": {"url": "https://github.com/webpack-contrib", "name": "Webpack Contrib"}, "license": "MIT", "_id": "schema-utils@0.1.0", "maintainers": [{"name": "d3viant0ne", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON>", "email": "@bebraw"}, {"name": "<PERSON>", "email": "@d3viant0ne"}], "homepage": "https://github.com/webpack-contrib/schema-utils#readme", "bugs": {"url": "https://github.com/webpack-contrib/schema-utils/issues"}, "dist": {"shasum": "886490266fafde65598a77eef1519ab993196e47", "tarball": "https://registry.npmjs.org/schema-utils/-/schema-utils-0.1.0.tgz", "integrity": "sha512-s6d3k7zTUtx/FqbdInFYVWqsM2r/qc4qz0oxKdPyP5Tu9OTkeraqsAcPujJ8yN3wo1kPz4CNWKRLrqwfaatbIw==", "signatures": [{"sig": "MEUCIGbq5y78DQBFAqcT4EsK8DjlQHjbu1R8+MJ0cfuTUvARAiEA4mdZmMuf/ZDnUVb2w4AYpA5dbgVBsjROHEpamnY6DFo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/index.js", "_from": ".", "files": ["dist"], "_shasum": "886490266fafde65598a77eef1519ab993196e47", "engines": {"node": ">=4.3 <5.0.0 || >=5.10"}, "gitHead": "26f041e0b114cb91382e26c569b7badfe6edc262", "scripts": {"lint": "eslint --cache src test", "test": "jest", "build": "cross-env NODE_ENV=production babel -s true src -d dist --ignore 'src/**/*.test.js'", "start": "yarn run serve:dev src", "release": "yarn run standard-version", "prebuild": "yarn run clean:dist", "security": "nsp check", "serve:dev": "nodemon $2 --exec babel-node", "clean:dist": "del-cli dist", "prepublish": "yarn run build", "lint-staged": "lint-staged", "travis:lint": "yarn run lint && yarn run security", "travis:test": "yarn run test", "test:coverage": "jest --collectCoverageFrom='src/**/*.js' --coverage", "travis:coverage": "yarn run test:coverage", "webpack-defaults": "webpack-defaults"}, "_npmUser": {"name": "d3viant0ne", "email": "<EMAIL>"}, "pre-commit": "lint-staged", "repository": {"url": "git+https://github.com/webpack-contrib/schema-utils.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Webpack Schema Validation Utilities", "directories": {}, "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "_nodeVersion": "6.9.5", "dependencies": {"ajv": "^4.11.2"}, "eslintConfig": {"extends": "webpack", "installedESLint": true}, "devDependencies": {"nsp": "^2.6.2", "shx": "^0.2.2", "chai": "^3.5.0", "jest": "^18.1.0", "eslint": "^3.14.1", "codecov": "^1.0.1", "del-cli": "^0.2.1", "nodemon": "^1.11.0", "babel-cli": "^6.22.2", "cross-env": "^3.1.4", "babel-jest": "^18.0.0", "pre-commit": "^1.2.2", "lint-staged": "^3.3.1", "babel-polyfill": "^6.22.0", "babel-preset-env": "^1.2.1", "standard-version": "^4.0.0", "webpack-defaults": "^0.2.1", "babel-preset-webpack": "^1.0.0", "eslint-plugin-import": "^2.2.0", "eslint-config-webpack": "^1.0.0", "greenkeeper-postpublish": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/schema-utils-0.1.0.tgz_1488866487592_0.1796807344071567", "host": "packages-18-east.internal.npmjs.com"}}, "0.2.1": {"name": "schema-utils", "version": "0.2.1", "keywords": ["webpack", "plugin", "es2015"], "author": {"url": "https://github.com/webpack-contrib", "name": "Webpack Contrib"}, "license": "MIT", "_id": "schema-utils@0.2.1", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "d3viant0ne", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON>", "email": "@bebraw"}, {"name": "<PERSON>", "email": "@d3viant0ne"}], "homepage": "https://github.com/webpack-contrib/schema-utils#readme", "bugs": {"url": "https://github.com/webpack-contrib/schema-utils/issues"}, "dist": {"shasum": "f49634960899ea5315e80d9a540c2c19dfb2cff8", "tarball": "https://registry.npmjs.org/schema-utils/-/schema-utils-0.2.1.tgz", "integrity": "sha512-3Lru850BgT44fM7gWF2dc+EfLzOHIPkQusHRsTu42r9ccP8gJc6bNr8pDB2O7vwV5J24mfA5MfRlmm0SRZS/nA==", "signatures": [{"sig": "MEUCIDV8MCCGWBkuglXR4wKzsPDN+3otoT+s98kedzbT5yikAiEAsWSHU5kogKfl0HQPKXe7kOZIQ5sBRSi8+vzNkotKv1g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/index.js", "_from": ".", "files": ["dist", "lib", "src", ".babelrc"], "_shasum": "f49634960899ea5315e80d9a540c2c19dfb2cff8", "engines": {"node": ">=4.3 <5.0.0 || >=5.10"}, "gitHead": "7152276042d2c5a106cdf9b5877357f910ac4c00", "scripts": {"lint": "eslint --cache src test", "test": "jest", "build": "cross-env NODE_ENV=production babel -s true src -d dist --ignore 'src/**/*.test.js'", "start": "yarn run serve:dev src", "release": "yarn run standard-version", "prebuild": "yarn run clean:dist", "security": "nsp check", "serve:dev": "nodemon $2 --exec babel-node", "clean:dist": "del-cli dist", "prepublish": "yarn run build", "lint-staged": "lint-staged", "postinstall": "node lib/post_install.js", "travis:lint": "yarn run lint && yarn run security", "travis:test": "yarn run test", "test:coverage": "jest --collectCoverageFrom='src/**/*.js' --coverage", "travis:coverage": "yarn run test:coverage", "webpack-defaults": "webpack-defaults"}, "_npmUser": {"name": "bebraw", "email": "<EMAIL>"}, "pre-commit": "lint-staged", "repository": {"url": "git+https://github.com/webpack-contrib/schema-utils.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Webpack Schema Validation Utilities", "directories": {}, "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "_nodeVersion": "6.9.1", "dependencies": {"ajv": "^4.11.2"}, "eslintConfig": {"extends": "webpack", "installedESLint": true}, "devDependencies": {"nsp": "^2.6.2", "shx": "^0.2.2", "chai": "^3.5.0", "jest": "^18.1.0", "eslint": "^3.14.1", "codecov": "^1.0.1", "del-cli": "^0.2.1", "nodemon": "^1.11.0", "babel-cli": "^6.22.2", "cross-env": "^3.1.4", "babel-jest": "^18.0.0", "pre-commit": "^1.2.2", "lint-staged": "^3.3.1", "babel-polyfill": "^6.22.0", "babel-preset-env": "^1.2.1", "standard-version": "^4.0.0", "webpack-defaults": "^0.2.1", "babel-preset-webpack": "^1.0.0", "eslint-plugin-import": "^2.2.0", "eslint-config-webpack": "^1.0.0", "greenkeeper-postpublish": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/schema-utils-0.2.1.tgz_1489388695917_0.07002403354272246", "host": "packages-18-east.internal.npmjs.com"}}, "0.3.0": {"name": "schema-utils", "version": "0.3.0", "keywords": ["webpack", "plugin", "es2015"], "author": {"url": "https://github.com/webpack-contrib", "name": "Webpack Contrib"}, "license": "MIT", "_id": "schema-utils@0.3.0", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "d3viant0ne", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON>", "email": "@bebraw"}, {"name": "<PERSON>", "email": "@d3viant0ne"}, {"name": "<PERSON>", "email": "@micha<PERSON>-c<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/webpack-contrib/schema-utils#readme", "bugs": {"url": "https://github.com/webpack-contrib/schema-utils/issues"}, "dist": {"shasum": "f5877222ce3e931edae039f17eb3716e7137f8cf", "tarball": "https://registry.npmjs.org/schema-utils/-/schema-utils-0.3.0.tgz", "integrity": "sha512-QaVYBaD9U8scJw2EBWnCBY+LJ0AD+/2edTaigDs0XLDLBfJmSUK9KGqktg1rb32U3z4j/XwvFwHHH1YfbYFd7Q==", "signatures": [{"sig": "MEYCIQC8DgTMqh+qH7n0p2yUQd2yztWGVmkPFOiZSPuRPMGZUwIhAPZdfszJInwkIJpjrPUPhCumARm72geJb+4WwF+GZqTW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/cjs.js", "_from": ".", "files": ["dist"], "_shasum": "f5877222ce3e931edae039f17eb3716e7137f8cf", "engines": {"node": ">= 4.3 < 5.0.0 || >= 5.10"}, "gitHead": "96525dd9fd5c33056cbafe5680c5cdd10a994686", "scripts": {"lint": "eslint --cache src test", "test": "jest", "build": "cross-env NODE_ENV=production babel src -d dist --ignore 'src/**/*.test.js'", "clean": "del-cli dist", "start": "yarn run build -- -w", "release": "yarn run standard-version", "prebuild": "yarn run clean", "security": "nsp check", "prepublish": "yarn run build", "test:watch": "jest --watch", "lint-staged": "lint-staged", "travis:lint": "yarn run lint && yarn run security", "travis:test": "yarn run test", "test:coverage": "jest --collectCoverageFrom='src/**/*.js' --coverage", "travis:coverage": "yarn run test:coverage", "webpack-defaults": "webpack-defaults"}, "_npmUser": {"name": "d3viant0ne", "email": "<EMAIL>"}, "pre-commit": "lint-staged", "repository": {"url": "git+https://github.com/webpack-contrib/schema-utils.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "Webpack Schema Validation Utilities", "directories": {}, "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "_nodeVersion": "7.9.0", "dependencies": {"ajv": "^5.0.0"}, "eslintConfig": {"extends": "webpack", "installedESLint": true}, "devDependencies": {"nsp": "^2.6.3", "jest": "^19.0.2", "eslint": "^3.19.0", "codecov": "^2.0.1", "del-cli": "^0.2.1", "babel-cli": "^6.24.1", "cross-env": "^4.0.0", "babel-jest": "^19.0.0", "pre-commit": "^1.2.2", "lint-staged": "^3.4.0", "babel-polyfill": "^6.23.0", "babel-preset-env": "^1.4.0", "standard-version": "^4.0.0", "webpack-defaults": "^0.4.5", "babel-preset-webpack": "^1.0.0", "eslint-plugin-import": "^2.2.0", "eslint-config-webpack": "^1.2.1", "babel-plugin-transform-object-rest-spread": "^6.23.0"}, "_npmOperationalInternal": {"tmp": "tmp/schema-utils-0.3.0.tgz_1493490045779_0.2797495676204562", "host": "packages-12-west.internal.npmjs.com"}}, "0.4.0": {"name": "schema-utils", "version": "0.4.0", "keywords": ["webpack", "webpack-plugin", "schema-utils", "loader"], "author": {"url": "https://github.com/webpack-contrib", "name": "Webpack Contrib"}, "license": "MIT", "_id": "schema-utils@0.4.0", "maintainers": [{"name": "d3viant0ne", "email": "<EMAIL>"}, {"name": "bebraw", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON>", "email": "@bebraw"}, {"name": "<PERSON>", "email": "@d3viant0ne"}, {"name": "<PERSON>", "email": "@micha<PERSON>-c<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/webpack-contrib/schema-utils#readme", "bugs": {"url": "https://github.com/webpack-contrib/schema-utils/issues"}, "dist": {"shasum": "cf98d870a5f74e7526211f6d38caf0d250366688", "tarball": "https://registry.npmjs.org/schema-utils/-/schema-utils-0.4.0.tgz", "integrity": "sha512-2QYsHbnLL40gWxhbAfOWa4hZKc9UcyV18/c/RIX5w3pa3BU5//SU4jnOQ6cPsCrwJl+NGZoaCvMXmXgUMYX0fw==", "signatures": [{"sig": "MEYCIQDZSqbDKYxVT/MQ9xSwO5pbD5HHJshWzkiEOmTmbyaK8wIhAIzx/+0p6iQgRkNan132R+MkJ7cXToCCfPK/YqHT3dQ4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/cjs.js", "files": ["dist"], "engines": {"node": ">= 4.8 < 5.0.0 || >= 5.10"}, "gitHead": "0cbab06711c55b16279fe874871a4ceb553a2320", "scripts": {"lint": "eslint --cache src test", "test": "jest", "build": "cross-env NODE_ENV=production babel src -d dist --ignore 'src/**/*.test.js' --copy-files", "clean": "del-cli dist", "start": "npm run build -- -w", "release": "standard-version", "prebuild": "npm run clean", "security": "nsp check", "prepublish": "npm run build", "test:watch": "jest --watch", "lint-staged": "lint-staged", "travis:lint": "npm run lint && npm run security", "travis:test": "npm run test -- --runInBand", "appveyor:test": "npm run test", "test:coverage": "jest --collectCoverageFrom='src/**/*.js' --coverage", "travis:coverage": "npm run test:coverage -- --runInBand", "webpack-defaults": "webpack-defaults"}, "_npmUser": {"name": "d3viant0ne", "email": "<EMAIL>"}, "pre-commit": "lint-staged", "repository": {"url": "git+https://github.com/webpack-contrib/schema-utils.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "Webpack Schema Validation Utilities", "directories": {}, "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "_nodeVersion": "8.7.0", "dependencies": {"ajv": "^5.0.0", "ajv-keywords": "^2.1.0"}, "devDependencies": {"nsp": "^2.0.0", "jest": "^21.0.0", "eslint": "^4.0.0", "del-cli": "^1.0.0", "babel-cli": "^6.0.0", "cross-env": "^5.0.0", "babel-jest": "^21.0.0", "pre-commit": "^1.0.0", "lint-staged": "^4.0.0", "babel-polyfill": "^6.0.0", "babel-preset-env": "^1.0.0", "standard-version": "^4.0.0", "webpack-defaults": "^1.6.0", "eslint-plugin-import": "^2.0.0", "eslint-config-webpack": "^1.0.0", "babel-plugin-transform-object-rest-spread": "^6.0.0"}, "peerDependencies": {"webpack": "^2.0.0 || ^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/schema-utils-0.4.0.tgz_1509387709032_0.2484780631493777", "host": "s3://npm-registry-packages"}}, "0.4.1": {"name": "schema-utils", "version": "0.4.1", "keywords": ["webpack", "webpack-plugin", "schema-utils", "loader"], "author": {"url": "https://github.com/webpack-contrib", "name": "Webpack Contrib"}, "license": "MIT", "_id": "schema-utils@0.4.1", "maintainers": [{"name": "d3viant0ne", "email": "<EMAIL>"}, {"name": "bebraw", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON>", "email": "@bebraw"}, {"name": "<PERSON>", "email": "@d3viant0ne"}, {"name": "<PERSON>", "email": "@micha<PERSON>-c<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/webpack-contrib/schema-utils#readme", "bugs": {"url": "https://github.com/webpack-contrib/schema-utils/issues"}, "dist": {"shasum": "db935de4227f61700aca2d89682ada32b1b243e3", "tarball": "https://registry.npmjs.org/schema-utils/-/schema-utils-0.4.1.tgz", "integrity": "sha512-VT7bSXSENu+y/a+U8zgIfYSmR8dd/GlnBGoSO5v1kOpLGEgmqJGbf/l2V0PMklVUcZEoGQ/gtKs/LxMnRxFW4A==", "signatures": [{"sig": "MEUCIEWoTCPVRWRSijVZDWkCXShmUajeN06MQKarExWCeT5YAiEAsaUtaL8w8nkbygnATTnB5U2tG7h2jqnYYPVgrDbIYl8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/cjs.js", "files": ["dist"], "engines": {"node": ">= 4.8 < 5.0.0 || >= 5.10"}, "gitHead": "8e242c827bcb4d9f76a722e555cf7d20a6bbaa16", "scripts": {"lint": "eslint --cache src test", "test": "jest", "build": "cross-env NODE_ENV=production babel src -d dist --ignore 'src/**/*.test.js' --copy-files", "clean": "del-cli dist", "start": "npm run build -- -w", "release": "standard-version", "prebuild": "npm run clean", "security": "nsp check", "prepublish": "npm run build", "test:watch": "jest --watch", "lint-staged": "lint-staged", "travis:lint": "npm run lint && npm run security", "travis:test": "npm run test -- --runInBand", "appveyor:test": "npm run test", "test:coverage": "jest --collectCoverageFrom='src/**/*.js' --coverage", "travis:coverage": "npm run test:coverage -- --runInBand", "webpack-defaults": "webpack-defaults"}, "_npmUser": {"name": "d3viant0ne", "email": "<EMAIL>"}, "pre-commit": "lint-staged", "repository": {"url": "git+https://github.com/webpack-contrib/schema-utils.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Webpack Schema Validation Utilities", "directories": {}, "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "_nodeVersion": "8.9.0", "dependencies": {"ajv": "^5.0.0", "ajv-keywords": "^2.1.0"}, "devDependencies": {"nsp": "^2.0.0", "jest": "^21.0.0", "eslint": "^4.0.0", "del-cli": "^1.0.0", "babel-cli": "^6.0.0", "cross-env": "^5.0.0", "babel-jest": "^21.0.0", "pre-commit": "^1.0.0", "lint-staged": "^4.0.0", "babel-polyfill": "^6.0.0", "babel-preset-env": "^1.0.0", "standard-version": "^4.0.0", "webpack-defaults": "^1.6.0", "eslint-plugin-import": "^2.0.0", "eslint-config-webpack": "^1.0.0", "babel-plugin-transform-object-rest-spread": "^6.0.0"}, "peerDependencies": {"webpack": "^2.0.0 || ^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/schema-utils-0.4.1.tgz_1509702693032_0.869106677128002", "host": "s3://npm-registry-packages"}}, "0.4.2": {"name": "schema-utils", "version": "0.4.2", "keywords": ["webpack", "webpack-plugin", "schema-utils", "loader"], "author": {"url": "https://github.com/webpack-contrib", "name": "Webpack Contrib"}, "license": "MIT", "_id": "schema-utils@0.4.2", "maintainers": [{"name": "d3viant0ne", "email": "<EMAIL>"}, {"name": "bebraw", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON>", "email": "@bebraw"}, {"name": "<PERSON>", "email": "@d3viant0ne"}, {"name": "<PERSON>", "email": "@micha<PERSON>-c<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/webpack-contrib/schema-utils#readme", "bugs": {"url": "https://github.com/webpack-contrib/schema-utils/issues"}, "dist": {"shasum": "86d92aedf4dfc51c9b321ef16bd33a9417b122d0", "tarball": "https://registry.npmjs.org/schema-utils/-/schema-utils-0.4.2.tgz", "integrity": "sha512-LCuuUj7L43TbSIqeERp+/Z2FH/NxSA48mqcWlGTSYUUKsevGafj2SpyaFVTxyWWFLkIAS3p7jDTLpNsrU7PXoA==", "signatures": [{"sig": "MEYCIQCfs4i5h/aF8RN5aRi6AXnZSZpH8btORRinR6SipuXBAAIhAJkH1seoZmt65H7ZnnJRIxs5unAASfOEQbXNo6ZwFQjb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/cjs.js", "files": ["dist"], "engines": {"node": ">= 4.8 < 5.0.0 || >= 5.10"}, "gitHead": "9a36941560cf33e5c4f0ddd202b8097974f55b30", "scripts": {"lint": "eslint --cache src test", "test": "cross-env JEST=true jest", "build": "cross-env NODE_ENV=production babel src -d dist --ignore 'src/**/*.test.js' --copy-files", "clean": "del-cli dist", "start": "npm run build -- -w", "release": "standard-version", "prebuild": "npm run clean", "security": "nsp check", "prepublish": "npm run build", "test:watch": "cross-env JEST=true jest --watch", "lint-staged": "lint-staged", "travis:lint": "npm run lint && npm run security", "travis:test": "npm run test -- --runInBand", "appveyor:test": "npm run test", "test:coverage": "cross-env JEST=true jest --collectCoverageFrom='src/**/*.js' --coverage", "travis:coverage": "npm run test:coverage -- --runInBand", "webpack-defaults": "webpack-defaults"}, "_npmUser": {"name": "d3viant0ne", "email": "<EMAIL>"}, "pre-commit": "lint-staged", "repository": {"url": "git+https://github.com/webpack-contrib/schema-utils.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Webpack Schema Validation Utilities", "directories": {}, "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "_nodeVersion": "8.9.0", "dependencies": {"ajv": "^5.0.0", "chalk": "^2.3.0", "ajv-keywords": "^2.1.0"}, "devDependencies": {"nsp": "^2.0.0", "jest": "^21.0.0", "eslint": "^4.0.0", "del-cli": "^1.0.0", "webpack": "^3.8.1", "babel-cli": "^6.0.0", "cross-env": "^5.0.0", "babel-jest": "^21.0.0", "pre-commit": "^1.0.0", "lint-staged": "^4.0.0", "babel-polyfill": "^6.0.0", "babel-preset-env": "^1.0.0", "standard-version": "^4.0.0", "webpack-defaults": "^1.6.0", "eslint-plugin-import": "^2.0.0", "eslint-config-webpack": "^1.0.0", "babel-plugin-transform-object-rest-spread": "^6.0.0"}, "peerDependencies": {"webpack": "^2.0.0 || ^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/schema-utils-0.4.2.tgz_1510258586488_0.7539700423367321", "host": "s3://npm-registry-packages"}}, "0.4.3": {"name": "schema-utils", "version": "0.4.3", "keywords": ["webpack", "webpack-plugin", "schema-utils", "loader"], "author": {"url": "https://github.com/webpack-contrib", "name": "Webpack Contrib"}, "license": "MIT", "_id": "schema-utils@0.4.3", "maintainers": [{"name": "d3viant0ne", "email": "<EMAIL>"}, {"name": "bebraw", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON>", "email": "@bebraw"}, {"name": "<PERSON>", "email": "@d3viant0ne"}, {"name": "<PERSON>", "email": "@micha<PERSON>-c<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/webpack-contrib/schema-utils#readme", "bugs": {"url": "https://github.com/webpack-contrib/schema-utils/issues"}, "dist": {"shasum": "e2a594d3395834d5e15da22b48be13517859458e", "tarball": "https://registry.npmjs.org/schema-utils/-/schema-utils-0.4.3.tgz", "integrity": "sha512-sgv/iF/T4/SewJkaVpldKC4WjSkz0JsOh2eKtxCPpCO1oR05+7MOF+H476HVRbLArkgA7j5TRJJ4p2jdFkUGQQ==", "signatures": [{"sig": "MEQCIHu6NBmdqa9XTAbFY444kj9I28VmXKvgB0HVGwOnObDmAiB0yFvO8W9cE2t0wsNQa8I0FpuydxEbUibUw2Biv+tqYA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/cjs.js", "files": ["dist"], "engines": {"node": ">= 4.8 < 5.0.0 || >= 5.10"}, "gitHead": "57f2820227dfce3ce91651fbe4d8cdfa3c4ae483", "scripts": {"lint": "eslint --cache src test", "test": "cross-env jest", "build": "cross-env NODE_ENV=production babel src -d dist --ignore 'src/**/*.test.js' --copy-files", "clean": "del-cli dist", "start": "npm run build -- -w", "release": "standard-version", "prebuild": "npm run clean", "security": "nsp check", "prepublish": "npm run build", "test:watch": "cross-env jest --watch", "lint-staged": "lint-staged", "travis:lint": "npm run lint && npm run security", "travis:test": "npm run test -- --runInBand", "appveyor:test": "npm run test", "test:coverage": "cross-env jest --collectCoverageFrom='src/**/*.js' --coverage", "travis:coverage": "npm run test:coverage -- --runInBand", "webpack-defaults": "webpack-defaults"}, "_npmUser": {"name": "d3viant0ne", "email": "<EMAIL>"}, "pre-commit": "lint-staged", "repository": {"url": "git+https://github.com/webpack-contrib/schema-utils.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Webpack Schema Validation Utilities", "directories": {}, "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "_nodeVersion": "8.9.3", "dependencies": {"ajv": "^5.0.0", "ajv-keywords": "^2.1.0"}, "devDependencies": {"nsp": "^2.0.0", "jest": "^21.0.0", "eslint": "^4.0.0", "del-cli": "^1.0.0", "webpack": "^3.0.0", "babel-cli": "^6.0.0", "cross-env": "^5.0.0", "babel-jest": "^21.0.0", "pre-commit": "^1.0.0", "lint-staged": "^4.0.0", "babel-polyfill": "^6.0.0", "babel-preset-env": "^1.0.0", "standard-version": "^4.0.0", "webpack-defaults": "^1.6.0", "eslint-plugin-import": "^2.0.0", "eslint-config-webpack": "^1.0.0", "babel-plugin-transform-object-rest-spread": "^6.0.0"}, "peerDependencies": {"webpack": "^2.0.0 || ^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/schema-utils-0.4.3.tgz_1513219740440_0.3938244723249227", "host": "s3://npm-registry-packages"}}, "0.4.4": {"name": "schema-utils", "version": "0.4.4", "author": {"url": "https://github.com/webpack-contrib", "name": "Webpack Contrib"}, "license": "MIT", "_id": "schema-utils@0.4.4", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "d3viant0ne", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/micha<PERSON>-c<PERSON><PERSON><PERSON>/schema", "bugs": {"url": "https://github.com/micha<PERSON>-c<PERSON><PERSON><PERSON>/schema/issues"}, "dist": {"shasum": "6a280d39dfefb1ef84fc2e0f6ac91c249beac7b0", "tarball": "https://registry.npmjs.org/schema-utils/-/schema-utils-0.4.4.tgz", "fileCount": 8, "integrity": "sha512-RtoiBCRDIEJxld0Fl3qUm3HEF8NM35nnBqhTmxzO/9bfWa6t2+zWcQdSWRC1UMW+dfPxAtpChl4JfUIUgy9M1A==", "signatures": [{"sig": "MEQCIGkTA3VmjiPI0BgQWJZ4AGcdnh1zDioeFpKW7w6pbUVgAiAGkl3BXbvDlSBJiSrpaKGxghZ7q58hd3Os3HuHcbUhxA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13585}, "main": "dist/cjs.js", "files": ["dist"], "engines": {"node": ">= 4.8.0 || >= 6.9.0 || >= 8.9.0"}, "gitHead": "65b591017dab9335484dcd976b612759101bceec", "scripts": {"lint": "eslint --cache src test", "test": "jest", "build": "cross-env NODE_ENV=production babel src -d dist --ignore 'src/**/*.test.js' --copy-files", "clean": "del-cli dist", "start": "npm run build -- -w", "ci:lint": "npm run lint && npm run security", "ci:test": "npm run test -- --runInBand", "prepare": "npm run build", "release": "standard-version", "defaults": "webpack-defaults", "prebuild": "npm run clean", "security": "nsp check", "commitmsg": "commitlint -e $GIT_PARAMS", "commitlint": "commitlint", "release:ci": "conventional-github-releaser -p angular", "test:watch": "jest --watch", "ci:coverage": "npm run test:coverage -- --runInBand", "lint-staged": "lint-staged", "test:coverage": "jest --collectCoverageFrom='src/**/*.js' --coverage", "ci:lint:commits": "commitlint --from=${CIRCLE_BRANCH} --to=${CIRCLE_SHA1}", "release:validate": "commitlint --from=$(git describe --tags --abbrev=0) --to=$(git rev-parse HEAD)"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com"}, "pre-commit": "lint-staged", "repository": {"url": "git+https://github.com/micha<PERSON>-c<PERSON><PERSON><PERSON>/schema.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Webpack Schema Validation Utilities", "directories": {}, "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "_nodeVersion": "9.5.0", "dependencies": {"ajv": "^6.1.0", "ajv-keywords": "^3.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^3.0.0", "nsp": "^3.1.0", "jest": "^22.2.2", "husky": "^0.14.3", "eslint": "^4.17.0", "del-cli": "^1.1.0", "webpack": "^3.11.0", "prettier": "^1.10.2", "babel-cli": "^6.26.0", "cross-env": "^5.1.3", "memory-fs": "^0.4.1", "babel-jest": "^22.2.2", "pre-commit": "^1.2.2", "lint-staged": "^6.1.0", "babel-polyfill": "^6.26.0", "@commitlint/cli": "^5.2.8", "babel-preset-env": "^1.6.1", "standard-version": "^4.3.0", "webpack-defaults": "^2.0.0-rc.4", "eslint-plugin-import": "^2.8.0", "eslint-plugin-prettier": "^2.6.0", "@commitlint/config-angular": "^5.1.1", "conventional-github-releaser": "^2.0.0", "@webpack-contrib/eslint-config-webpack": "^2.0.2", "babel-plugin-transform-object-rest-spread": "^6.26.0"}, "peerDependencies": {"webpack": "^2.0.0 || ^3.0.0 || ^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/schema-utils_0.4.4_1518528821343_0.9739770599349351", "host": "s3://npm-registry-packages"}}, "0.4.5": {"name": "schema-utils", "version": "0.4.5", "author": {"url": "https://github.com/webpack-contrib", "name": "Webpack Contrib"}, "license": "MIT", "_id": "schema-utils@0.4.5", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "d3viant0ne", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/webpack-contrib/schema-utils", "bugs": {"url": "https://github.com/webpack-contrib/schema-utils/issues"}, "dist": {"shasum": "21836f0608aac17b78f9e3e24daff14a5ca13a3e", "tarball": "https://registry.npmjs.org/schema-utils/-/schema-utils-0.4.5.tgz", "fileCount": 8, "integrity": "sha512-yYrjb9TX2k/J1Y5UNy3KYdZq10xhYcF8nMpAW6o3hy6Q8WSIEf9lJHG/ePnOBfziPM3fvQwfOwa13U/Fh8qTfA==", "signatures": [{"sig": "MEQCIHtiW5K1JokfrxR42/+Cz9McmVGQgXfpr6NAJT2hPXF0AiBve/hVxSsPIrNjrI7F6OO8sFyuiVBO0Cvdr2nbeL8fPA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13971}, "main": "dist/cjs.js", "files": ["dist"], "engines": {"node": ">= 4.8.0 || >= 6.9.0 || >= 8.9.0"}, "gitHead": "1afd5807c328014616bbaadb5a91a785f24cbdfc", "scripts": {"lint": "eslint --cache src test", "test": "jest", "build": "cross-env NODE_ENV=production babel src -d dist --ignore 'src/**/*.test.js' --copy-files", "clean": "del-cli dist", "start": "npm run build -- -w", "ci:lint": "npm run lint && npm run security", "ci:test": "npm run test -- --runInBand", "prepare": "npm run build", "release": "standard-version", "defaults": "webpack-defaults", "prebuild": "npm run clean", "security": "nsp check", "commitmsg": "commitlint -e $GIT_PARAMS", "commitlint": "commitlint", "release:ci": "conventional-github-releaser -p angular", "test:watch": "jest --watch", "ci:coverage": "npm run test:coverage -- --runInBand", "lint-staged": "lint-staged", "test:coverage": "jest --collectCoverageFrom='src/**/*.js' --coverage", "ci:lint:commits": "commitlint --from=${CIRCLE_BRANCH} --to=${CIRCLE_SHA1}", "release:validate": "commitlint --from=$(git describe --tags --abbrev=0) --to=$(git rev-parse HEAD)"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com"}, "pre-commit": "lint-staged", "repository": {"url": "git+https://github.com/webpack-contrib/schema-utils.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Webpack Schema Validation Utilities", "directories": {}, "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "_nodeVersion": "9.5.0", "dependencies": {"ajv": "^6.1.0", "ajv-keywords": "^3.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^3.0.0", "nsp": "^3.1.0", "jest": "^22.2.2", "husky": "^0.14.3", "eslint": "^4.17.0", "del-cli": "^1.1.0", "webpack": "^3.11.0", "prettier": "^1.10.2", "babel-cli": "^6.26.0", "cross-env": "^5.1.3", "memory-fs": "^0.4.1", "babel-jest": "^22.2.2", "pre-commit": "^1.2.2", "lint-staged": "^6.1.0", "babel-polyfill": "^6.26.0", "@commitlint/cli": "^5.2.8", "babel-preset-env": "^1.6.1", "standard-version": "^4.3.0", "webpack-defaults": "^2.0.0-rc.4", "eslint-plugin-import": "^2.8.0", "eslint-plugin-prettier": "^2.6.0", "@commitlint/config-angular": "^5.1.1", "conventional-github-releaser": "^2.0.0", "@webpack-contrib/eslint-config-webpack": "^2.0.2", "babel-plugin-transform-object-rest-spread": "^6.26.0"}, "peerDependencies": {"webpack": "^2.0.0 || ^3.0.0 || ^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/schema-utils_0.4.5_1518529667219_0.7169207153910973", "host": "s3://npm-registry-packages"}}, "0.4.6": {"name": "schema-utils", "version": "0.4.6", "author": {"url": "https://github.com/webpack-contrib", "name": "webpack Contrib"}, "license": "MIT", "_id": "schema-utils@0.4.6", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "d3viant0ne", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/webpack-contrib/schema-utils", "bugs": {"url": "https://github.com/webpack-contrib/schema-utils/issues"}, "dist": {"shasum": "dab4516a656310a964ca772bd3771819ba2b5cec", "tarball": "https://registry.npmjs.org/schema-utils/-/schema-utils-0.4.6.tgz", "fileCount": 7, "integrity": "sha512-+8HiCq9YzNM3S8vF7w1qsXK30H8dCc8EVduWGJvF0YzMhw7ehi/mLckZR9WfsPjfSnT6btpOL3jljExw8S4K5Q==", "signatures": [{"sig": "MEUCIB8fW7NTsZmWKwRcy5rn1JlGLTTGz3qAVw6WnwUYXRjPAiEAu6DEaE52Lav8YfKRxQ3OFNmucs5/9cr/RvTFY8FGIr4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10238, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbaIAACRA9TVsSAnZWagAAF0UP/0QYVDwCb5lyWSEULdrj\nt9UlCpDZBrtwfJv+tdrlKUEySDNLkpaEDoq72pzmmbA/6fV+ntrTP+SfT8Mg\n7+qmQf+O1w+mRvELiRG39wLi06tyDSJcdYGREER36n+iKvy7e3eIuERow0Ti\n4yF6iwFLASqboMZwM1+avZnO7aAD0+t0ZbFTDSeYYJIaqR7osj1VMgF7Ltnp\nrgFsxB3OT7l1RlE4kJR+lRlNBO2jDtLL5PdjGa9/h+qKK4WxV2ehTKIlqVul\nu6tuNabQSwA2PH85cmxViahQmvu0Ma9Kof4BaO03VP+kg2gmjvNe3YbYXTUz\nGX8PCzyhsNJE+frId03WuxHM6Fb+G1ELNTGk911wge44jRiiVOMtjl0d21Ty\n+Zc1W0hVAvUJFFWCXC/N5kt9sCnzdMxU/5qjgPH+1BCG91IFd1KzF1Gc0vtS\nk21uahiTNfhAgY4T/8xV5ZtzzeWJHOZAqTQCpKznZaCEjRIhCx7AQH+zKHdF\nhGHZimf6vWIHd2lijwUHsdwhHjoHUndREc1R7o5dklYpOV8sSAxlYTu1982i\nuASLDyQUpvVxDANrr1Yo2AWeKm+bt47/Cpata28bfrlCgGNTwT0EaAb4oHgh\nncsYcU0VWVC87ydkpj0ZgCndOS1+uIj33aw7ShLOpMlmqXVkXtCm65xzTW8Y\nWUQk\r\n=u/s3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/index.js", "files": ["src"], "engines": {"node": ">= 4"}, "gitHead": "ce6626b794fd0bf33b28c84b5d8a7354fb9cb524", "scripts": {"lint": "eslint --cache src test", "test": "jest --env node --verbose --coverage", "clean": "del-cli coverage", "commits": "commitlint --from $(git rev-list --tags --max-count=1)", "release": "npm run commits && standard-version"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/webpack-contrib/schema-utils.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "webpack Validation Utils", "directories": {}, "_nodeVersion": "10.8.0", "dependencies": {"ajv": "^6.1.0", "ajv-keywords": "^3.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^22.0.0", "eslint": "^5.0.0", "del-cli": "^1.0.0", "prettier": "^1.0.0", "@commitlint/cli": "^7.0.0", "standard-version": "^4.0.0", "eslint-plugin-import": "^2.0.0", "eslint-plugin-prettier": "^2.0.0", "@commitlint/config-conventional": "^7.0.0", "@webpack-contrib/eslint-config-webpack": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/schema-utils_0.4.6_1533575168399_0.9556367856367722", "host": "s3://npm-registry-packages"}}, "0.4.7": {"name": "schema-utils", "version": "0.4.7", "author": {"url": "https://github.com/webpack-contrib", "name": "webpack Contrib"}, "license": "MIT", "_id": "schema-utils@0.4.7", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "d3viant0ne", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/webpack-contrib/schema-utils", "bugs": {"url": "https://github.com/webpack-contrib/schema-utils/issues"}, "dist": {"shasum": "ba74f597d2be2ea880131746ee17d0a093c68187", "tarball": "https://registry.npmjs.org/schema-utils/-/schema-utils-0.4.7.tgz", "fileCount": 7, "integrity": "sha512-v/iwU6wvwGK8HbU9yi3/nhGzP0yGSuhQMzL6ySiec1FSrZZDkhm4noOSWzrNFo/jEc+SJY6jRTwuwbSXJPDUnQ==", "signatures": [{"sig": "MEYCIQDKxaJX4uRIEVEHbvVIMrZ+ZMivTDHblobJn84dw2CUwQIhAOuP3brO2XHqTBF0HKfB1Cf/THx84oOqbBylxQvCaj7R", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10673, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbaY2yCRA9TVsSAnZWagAAa74P/RK6R+h+/PBoD/aa1UOW\nPHC4dQ0sW2FQvWLCXPkBXESW0RDYHz40ZTSbtgFySdsttZoU6+fSQZLTcRBU\nvthl68o7Du3DSPxgomh7lJmhl2hsUOw3Wxf9aeLBjedMXLDgwpKWqm0WXEud\nFLdHvhVi3NxRkqtFkYXomMTqWVjRYyHi8zMZnL3i21p7JKg1dvb/m4lAdKnF\nmZdmwlcqXrP/6AfD33EilPnWHR6ZxYoViVuhnFaAmIryXKr2q/QNbutKG+XV\n+tH+3wpJh/Qotlc42R8FEDT7oOtNEQkRzE88MQmVvSSa57p5BrvYGI63vx+H\nPiw4MwiTJVJYYegJ5xj9UWIEZ4OVpEx6izcMwU/FHsHjb9VGJwH2aPOf1HgB\nwkNzmRYO6SFsUAS06MQW8cLVvG8plfTZRFb1t+KKItwitH1xrsVSUZKFUewI\nF/7PvK3MfCAMMgFrSeB3+KQa+3ghG9nhx3cadIJPbh0mFOXAkNtYJmVgiIWZ\np3lyQeO+nN1kFJ61cE5TYKVUwSkelSiWI0mFtTfAdYHphGB8XhTt7BiMfpTd\nEmxKz8jfvXGAs72NwCOLOBetQKAMtICv1Dg5sqeIcTYNpllF9jcBQr2C0qQN\nHktMK2uXaN9DUOWVuIuu+hfSLuhS6E9FRFcEcG1gWYCEba1g/dSRpelZD8ZU\nxnul\r\n=C01H\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/index.js", "files": ["src"], "engines": {"node": ">= 4"}, "gitHead": "ebc09b7784df233874a771b03b35b95613bcbad2", "scripts": {"lint": "eslint --cache src test", "test": "jest --env node --verbose --coverage", "clean": "del-cli coverage", "commits": "commitlint --from $(git rev-list --tags --max-count=1)", "release": "npm run commits && standard-version"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/webpack-contrib/schema-utils.git", "type": "git"}, "_npmVersion": "6.3.0", "description": "webpack Validation Utils", "directories": {}, "_nodeVersion": "10.8.0", "dependencies": {"ajv": "^6.1.0", "ajv-keywords": "^3.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^21.0.0", "eslint": "^5.0.0", "del-cli": "^1.0.0", "prettier": "^1.0.0", "@commitlint/cli": "^7.0.0", "standard-version": "^4.0.0", "eslint-plugin-import": "^2.0.0", "eslint-plugin-prettier": "^2.0.0", "@commitlint/config-conventional": "^7.0.0", "@webpack-contrib/eslint-config-webpack": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/schema-utils_0.4.7_1533644210040_0.27989860459207727", "host": "s3://npm-registry-packages"}}, "1.0.0": {"name": "schema-utils", "version": "1.0.0", "author": {"url": "https://github.com/webpack-contrib", "name": "webpack Contrib"}, "license": "MIT", "_id": "schema-utils@1.0.0", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "d3viant0ne", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/webpack-contrib/schema-utils", "bugs": {"url": "https://github.com/webpack-contrib/schema-utils/issues"}, "dist": {"shasum": "0b79a93204d7b600d4b2850d1f66c2a34951c770", "tarball": "https://registry.npmjs.org/schema-utils/-/schema-utils-1.0.0.tgz", "fileCount": 7, "integrity": "sha512-i27Mic4KovM/lnGsy8whRCHhc7VicJajAjTrYg11K9zfZXnYIt4k5F+kZkwjnrhKzLic/HLU4j11mjsz2G/75g==", "signatures": [{"sig": "MEYCIQCBm36D1SfOE1a+bDnGd9XMsdDW/RgFLv5/GUxZPjKEGAIhAKgHz1zgy/MnQ0robaEqYCE+EV1zfXs7ov/muPyx15sR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11504, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbaclaCRA9TVsSAnZWagAAQdQP+wYwozp28/D/pQfqP+8P\nZubNavTF9u29pNaAQap3C1k5AdB0SKz5huvaFaECN0NQZ/qvMsmvBn1b02rD\nu4qByf2okF9ydL/lSrDh9yQ/C0eKxtA3eKQ3wOAb6T74kpYK2ckPCWwpBeIk\nRRvWyb3+Gh8SWJJzglAz2qGiQuAXHdP6s7mTE7RvaSaCReMBIhkvaexycBaY\nWTYlZPGVG9NagREbbEDzH5EXBdzGQRjcAyM7szvCw1s416er7yTrDTw4XSRv\nmt/g9AiSrs2v/jna1BY7AR350Z6VeU2WAv8/Ri0O1umKOsUUVgFzCOwVPGYT\n1ASANqpkX9WTGKECKwulk/JGnDuwmxleRfUYJUX0tJbU2UkMvysFMVvRhovU\nRmyHpr0DhCl/+IOgpxRgqKi6zvR8b4nQN76lAUbsf9MZ2pz9T1ZnSKvEIT+m\ndyDpw/R/0eAdk3NCuaZl9gJ9/hYpJCguvRx9fAjdl7H8Zlx/GCz/H0sxFQuP\nN2nM3mOieryk63L/JK/ncpRFoSAsHmrPDPrlR//jEUshaz7ONxB6Z+guUvTi\n3I9nFD5Xzt9cr7Ev5ABtdb6gOkYDD0C3IJcoWXS5rYQ07KMzbXbKzTHPLKh9\nvW5D/N3rN+DMFaLNztbixM0eunvnc/rswEYhw2bFXpdCJA0VT44TmtOdcWU4\nr1TM\r\n=gDs9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/index.js", "files": ["src"], "engines": {"node": ">= 4"}, "gitHead": "6ca2322d4c8c5818c4a8f71328ac88e3507ec4e8", "scripts": {"lint": "eslint --cache src test", "test": "jest --env node --verbose --coverage", "clean": "del-cli coverage", "commits": "commitlint --from $(git rev-list --tags --max-count=1)", "release": "npm run commits && standard-version"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/webpack-contrib/schema-utils.git", "type": "git"}, "_npmVersion": "6.3.0", "description": "webpack Validation Utils", "directories": {}, "_nodeVersion": "10.8.0", "dependencies": {"ajv": "^6.1.0", "ajv-errors": "^1.0.0", "ajv-keywords": "^3.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^21.0.0", "eslint": "^5.0.0", "del-cli": "^1.0.0", "prettier": "^1.0.0", "@commitlint/cli": "^7.0.0", "standard-version": "^4.0.0", "eslint-plugin-import": "^2.0.0", "eslint-plugin-prettier": "^2.0.0", "@commitlint/config-conventional": "^7.0.0", "@webpack-contrib/eslint-config-webpack": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/schema-utils_1.0.0_1533659472153_0.8504713655306841", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "schema-utils", "version": "2.0.0", "keywords": ["webpack"], "author": {"url": "https://github.com/webpack-contrib", "name": "webpack Contrib"}, "license": "MIT", "_id": "schema-utils@2.0.0", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "d3viant0ne", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/webpack-contrib/schema-utils", "bugs": {"url": "https://github.com/webpack-contrib/schema-utils/issues"}, "dist": {"shasum": "e5bc78a5bff1e771e566b52f6a523b345352a448", "tarball": "https://registry.npmjs.org/schema-utils/-/schema-utils-2.0.0.tgz", "fileCount": 8, "integrity": "sha512-4JfkJmuT78xkJAZrYivuu6RNfX57ul5u+jsfxwRAdWw5eE1qIY/i4go1A3zAdJlTwYXLbvWHWXVvoYu3PjGf9A==", "signatures": [{"sig": "MEYCIQDWBJpciYUblJEjS/i+jfq8KLQSCueAZys1/eSkLSGLbwIhAI2DX+XYzHutJTw25s6uZvy7PtXFwTqwugjn1Gfu2DIl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39155, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdLy3SCRA9TVsSAnZWagAAzQMP/i6pmN8grZ1s/JyPPYpQ\neuGdPGKK0coCyOJWcSPtoIz7e+uGyJRN8wAlTI/fN5ERrSrsM3TX8wge+Dyj\nVeZPDKhONTA0ItVG/BBKkimK+OE4Hm6plZzzX4/DwdyCp/VHkouYxpx3eSCD\n2OO9EIrCy6x/nDq7oX8lm2PRxxuQH6A7jYe+bLDofhtJ8Crve6a1TPZAIhcR\n7REZgEUU88gCX2h5D4FCYyRFbofOirMneWpSitse7N2LBdOtqEwgpwduSGiG\nAOsHKrSJtDwMhwZJNP/40jSQloHizGuNFjeLHItSABIdvJL2rTNqtWZhQ++J\nGy0z7cahF2Z6NcmWwW4bR2K+JgCIib+rchUrbeb8aXjDQDJyXn1pReWRDlMk\nMyZ9pM7w8r0QpP/ceRIb1md0xn4JNiBZH7FMBcDVe/0WSk2PcGvuCRzELozi\nDHj9JERQPHYyxWCtvq/3zmYBsBBU+erZMYEshP7lyJ8MZJs8UK6hQdgxKCkf\nk09L7g1TktaUmNEBcwGr4cGkVwNYPICqDTv8eB38bZX3Hpo39Z8nQFegpjMj\nwgg7vQzbxh/qWRlO7+ZjtVbObeQaKTVsH6ep2fRri+bj8vVQM9Qn8fjY8pZJ\n9wkIniwNS2QKB0Vc+7F1tEFpDPMsgS/8Eo2xTBXpKee73tJs3w5PdmFLal0C\nPaF7\r\n=9s0N\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "engines": {"node": ">= 8.9.0"}, "gitHead": "692b4376aeff71b09997497060c2b967b6318ce6", "scripts": {"lint": "npm-run-all -l -p \"lint:**\"", "test": "cross-env NODE_ENV=test npm run test:coverage", "build": "cross-env NODE_ENV=production babel src -d dist --ignore \"src/**/*.test.js\" --copy-files", "clean": "del-cli dist", "start": "npm run build -- -w", "lint:js": "eslint --cache src test", "prepare": "npm run build", "pretest": "npm run lint", "release": "standard-version", "defaults": "webpack-defaults", "prebuild": "npm run clean", "security": "npm audit", "test:only": "cross-env NODE_ENV=test jest", "commitlint": "commitlint --from=master", "test:watch": "cross-env NODE_ENV=test jest --watch", "lint:prettier": "prettier \"{**/*,*}.{js,json,md,yml,css}\" --list-different", "test:coverage": "cross-env NODE_ENV=test jest --collectCoverageFrom=\"src/**/*.js\" --coverage"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack-contrib/schema-utils.git", "type": "git"}, "_npmVersion": "6.10.1", "description": "webpack Validation Utils", "directories": {}, "_nodeVersion": "10.15.2", "dependencies": {"ajv": "^6.1.0", "ajv-keywords": "^3.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^5.0.0", "jest": "^24.8.0", "husky": "^3.0.0", "eslint": "^6.0.1", "del-cli": "^2.0.0", "prettier": "^1.0.0", "cross-env": "^5.2.0", "@babel/cli": "^7.4.4", "babel-jest": "^24.8.0", "jest-junit": "^6.4.0", "@babel/core": "^7.4.5", "lint-staged": "^9.2.0", "npm-run-all": "^4.1.5", "@commitlint/cli": "^8.0.0", "standard-version": "^6.0.1", "@babel/preset-env": "^7.4.5", "eslint-plugin-import": "^2.0.0", "eslint-config-prettier": "^6.0.0", "@webpack-contrib/defaults": "^5.0.0", "commitlint-azure-pipelines-cli": "^1.0.2", "@commitlint/config-conventional": "^8.0.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/schema-utils_2.0.0_1563373009093_0.465262828175129", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "schema-utils", "version": "2.0.1", "keywords": ["webpack"], "author": {"url": "https://github.com/webpack-contrib", "name": "webpack Contrib"}, "license": "MIT", "_id": "schema-utils@2.0.1", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "d3viant0ne", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/webpack-contrib/schema-utils", "bugs": {"url": "https://github.com/webpack-contrib/schema-utils/issues"}, "dist": {"shasum": "1eec2e059556af841b7f3a83b61af13d7a3f9196", "tarball": "https://registry.npmjs.org/schema-utils/-/schema-utils-2.0.1.tgz", "fileCount": 8, "integrity": "sha512-HJFKJ4JixDpRur06QHwi8uu2kZbng318ahWEKgBjc0ZklcE4FDvmm2wghb448q0IRaABxIESt8vqPFvwgMB80A==", "signatures": [{"sig": "MEQCIHqtmJkr1oG7viz70T3I3f7364JOw0n6xoh0PFVlXPsXAiAFk3Zjd4XgK60yinaVubnfV0kQqsdqE1PSttOFlmYCxw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39449, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdMJp6CRA9TVsSAnZWagAA784P/3w8bWROmRrYWzchLphC\nNdtmZ6LXzFE6Ec+wkJp/NKLpdiTStmkWj7di73GdUVVjm/xqs4YkxTWTfO47\nuUm8LpTBNh9+hv4d9wCObKHbuJJbKUZ10h95mMAbEBLVATUJWKY+tOctVctB\n8cNid0/ICo4VmTQjMZMLRdZ8nWxHHeWHYaBIQcAFzsiUc/4DHClH0COGZcLi\nrkiUb9Wan8zSLcP7p88PFhXeXAFZffqEEuOVfBNTxSXF+8nFeYzcW7PSKyXd\n6q8ZLFllfpIMdIJ9tq8YsVlDogxQ7MuXU6t7jQdBqBGb+P+UcSW1fSY+ePjh\nSmYCX95cKXBBtYRNA17Li7ZI79/i3grZMERyr7uSQ5od3hgCEY0SXDUQO+2D\nh2J+SnpwWx9ZROrcSZN/m/52TiON4ZvkcagrT14/6f32eOvEY7i4cpAuLR7+\nY7Jr+typGh5CN6KcR0mcq7OBZt10sWKDy7YQaiCg8tNv3taz+DNsDN22iL/Q\neoO8oQnkveqQSd7780g8DHW23cZFGiwDA4V3IDqm/mzBHUBQFrdvKTAhGwqI\nJiBsUnySnWusfO43ZlTbw5iikReqRuZiQBF0FdsjM2vStxp1BlMTUYAE6dJY\nXXMzH4S200MgkMpcdWghyazrj5bwrDvQ7uBiL6YKtoCSyTAQYEdjPZ/qqnVs\nAhX4\r\n=RgN6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "engines": {"node": ">= 8.9.0"}, "gitHead": "ebbbe2c6ab023083e726c3ba7f1bc49a6d642d8b", "scripts": {"lint": "npm-run-all -l -p \"lint:**\"", "test": "cross-env NODE_ENV=test npm run test:coverage", "build": "cross-env NODE_ENV=production babel src -d dist --ignore \"src/**/*.test.js\" --copy-files", "clean": "del-cli dist", "start": "npm run build -- -w", "lint:js": "eslint --cache src test", "prepare": "npm run build", "pretest": "npm run lint", "release": "standard-version", "defaults": "webpack-defaults", "prebuild": "npm run clean", "security": "npm audit", "test:only": "cross-env NODE_ENV=test jest", "commitlint": "commitlint --from=master", "test:watch": "cross-env NODE_ENV=test jest --watch", "lint:prettier": "prettier \"{**/*,*}.{js,json,md,yml,css}\" --list-different", "test:coverage": "cross-env NODE_ENV=test jest --collectCoverageFrom=\"src/**/*.js\" --coverage"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack-contrib/schema-utils.git", "type": "git"}, "_npmVersion": "6.10.1", "description": "webpack Validation Utils", "directories": {}, "_nodeVersion": "10.15.2", "dependencies": {"ajv": "^6.1.0", "ajv-keywords": "^3.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^5.0.0", "jest": "^24.8.0", "husky": "^3.0.0", "eslint": "^6.0.1", "del-cli": "^2.0.0", "prettier": "^1.0.0", "cross-env": "^5.2.0", "@babel/cli": "^7.4.4", "babel-jest": "^24.8.0", "jest-junit": "^6.4.0", "@babel/core": "^7.4.5", "lint-staged": "^9.2.0", "npm-run-all": "^4.1.5", "@commitlint/cli": "^8.0.0", "standard-version": "^6.0.1", "@babel/preset-env": "^7.4.5", "eslint-plugin-import": "^2.0.0", "eslint-config-prettier": "^6.0.0", "@webpack-contrib/defaults": "^5.0.0", "commitlint-azure-pipelines-cli": "^1.0.2", "@commitlint/config-conventional": "^8.0.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/schema-utils_2.0.1_1563466362051_0.013276362947875153", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "schema-utils", "version": "2.1.0", "keywords": ["webpack"], "author": {"url": "https://github.com/webpack-contrib", "name": "webpack Contrib"}, "license": "MIT", "_id": "schema-utils@2.1.0", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "d3viant0ne", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/webpack-contrib/schema-utils", "bugs": {"url": "https://github.com/webpack-contrib/schema-utils/issues"}, "dist": {"shasum": "940363b6b1ec407800a22951bdcc23363c039393", "tarball": "https://registry.npmjs.org/schema-utils/-/schema-utils-2.1.0.tgz", "fileCount": 8, "integrity": "sha512-g6SViEZAfGNrToD82ZPUjq52KUPDYc+fN5+g6Euo5mLokl/9Yx14z0Cu4RR1m55HtBXejO0sBt+qw79axN+Fiw==", "signatures": [{"sig": "MEQCIBYn1fs29yzr1cHLXyR5B+k4WSqU8P6+QXxpu9d2TG7/AiBsw76RAMf5x9NVauZaC7t5zLKWiYnycoq///y2jLfoyA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40043, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdSugcCRA9TVsSAnZWagAAJy0P/1tGyg+tNIicQUr3wrH4\nVvt23kt9W8SMe+yo3f/sInikIVnL9+3n3DxVT6HShZvOBiv3DqomaVMKzYOp\nM7gvJb98O+xCM33wiNk7BH0f2nfVaY2PFgyaevSifwximET6G0LSqzjupyRv\nEvpfwjI8tnYQm5jXuywZ/s77xvH5lAxlzS7yZE7jE6nwRQpK8+5XPn5vKfCs\nodcw9ecvLWZch9FXgNcIPPlFDEvwaQ2/jUrctLIgQDdg8T6m8sS5Fquz2+hk\nr080krdgOsNCeYhC+k/a0huvCRZAHYvn0hF1ZQiu4SSoNC/LmoJO7fLXIJtx\nuxOKn2KwxFW9QZ5SiFYpzuostuU0MDtHOrQ9k6Nh3C1ogjanBpr5FeXiPs79\nzttr6H/EMVuPmO/91YOa6wnuWnNbYh3CcuGIaS5UrKOi5mWwR/qFEfZdwqZy\nyt/nzrkydDWsqpmYw0rhz75i/21DGYjdviJU8lx0QLM3sE3Z7Z07/JQvePkS\n5ta8+5VMEv7cNRtVyAGqa0Gk09PgUR1ZqKcaoKExEjh33TZrjku2gcE+YIZe\n2UaqYFHuL52TSv4Qayu7I6HU8vGWTlbuMVbIqU4oRWLaswXEeDpVF3XSuMOE\nWlrCWVJlPcqnXAY0Kcg9RfGvgBs3tW2hfsI0wWDdALMC7LD0R6jZf1+MKEd+\n4ZwL\r\n=thvW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "engines": {"node": ">= 8.9.0"}, "gitHead": "08d8147f2008bdc6403acd45927ae5f1c65e3636", "scripts": {"lint": "npm-run-all -l -p \"lint:**\"", "test": "cross-env NODE_ENV=test npm run test:coverage", "build": "cross-env NODE_ENV=production babel src -d dist --ignore \"src/**/*.test.js\" --copy-files", "clean": "del-cli dist", "start": "npm run build -- -w", "lint:js": "eslint --cache src test", "prepare": "npm run build", "pretest": "npm run lint", "release": "standard-version", "defaults": "webpack-defaults", "prebuild": "npm run clean", "security": "npm audit", "test:only": "cross-env NODE_ENV=test jest", "commitlint": "commitlint --from=master", "test:watch": "cross-env NODE_ENV=test jest --watch", "lint:prettier": "prettier \"{**/*,*}.{js,json,md,yml,css}\" --list-different", "test:coverage": "cross-env NODE_ENV=test jest --collectCoverageFrom=\"src/**/*.js\" --coverage"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack-contrib/schema-utils.git", "type": "git"}, "_npmVersion": "6.10.2", "description": "webpack Validation Utils", "directories": {}, "_nodeVersion": "10.15.2", "dependencies": {"ajv": "^6.1.0", "ajv-keywords": "^3.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^5.0.0", "jest": "^24.8.0", "husky": "^3.0.0", "eslint": "^6.0.1", "del-cli": "^2.0.0", "prettier": "^1.0.0", "cross-env": "^5.2.0", "@babel/cli": "^7.4.4", "babel-jest": "^24.8.0", "jest-junit": "^6.4.0", "@babel/core": "^7.4.5", "lint-staged": "^9.2.0", "npm-run-all": "^4.1.5", "@commitlint/cli": "^8.0.0", "standard-version": "^6.0.1", "@babel/preset-env": "^7.4.5", "eslint-plugin-import": "^2.0.0", "eslint-config-prettier": "^6.0.0", "@webpack-contrib/defaults": "^5.0.0", "commitlint-azure-pipelines-cli": "^1.0.2", "@commitlint/config-conventional": "^8.0.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/schema-utils_2.1.0_1565190172002_0.9844907865540982", "host": "s3://npm-registry-packages"}}, "2.2.0": {"name": "schema-utils", "version": "2.2.0", "keywords": ["webpack"], "author": {"url": "https://github.com/webpack-contrib", "name": "webpack Contrib"}, "license": "MIT", "_id": "schema-utils@2.2.0", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "d3viant0ne", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/webpack/schema-utils", "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "dist": {"shasum": "48a065ce219e0cacf4631473159037b2c1ae82da", "tarball": "https://registry.npmjs.org/schema-utils/-/schema-utils-2.2.0.tgz", "fileCount": 8, "integrity": "sha512-5EwsCNhfFTZvUreQhx/4vVQpJ/lnCAkgoIHLhSpp4ZirE+4hzFvdJi0FMub6hxbFVBJYSpeVVmon+2e7uEGRrA==", "signatures": [{"sig": "MEYCIQDIqmrKYkoK1HJJTy9jkDh0eSn1CUOqZrRIZ2TWWBb/uAIhAIsVACw06d3aU7B66Bb4+m1ulyLZD72PSv8cLLGEnE4q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42197, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdbPWeCRA9TVsSAnZWagAA/5YP/jxgHyUYnYwjtW6li7z+\nlX8lKO/q27P5uujpqy+7AIiAEVHOBz2469zy1bACHDXgihZHF5anSpgBxvhG\ndxDuAT3iSA5a5fBGM2mi77p5ISA1/yglDzW68kHy4WF4RajAvvWE3HDx2mPV\nJqI9KRJJA/PUWoTEaeJtFcjOgcgoc7PskWFSNKcHnTkM7uUcfohlv/TjGRiw\npHxqik1Afd8jbHoMxAYP1oxMx+4foyQu+0TSh5zH+lABtGGQxD8v6bqkIQ0f\n8udyD9C2342uFjmftuVXv2VFNK6vFK9GuknoJAPXcbZLMsmzriO0AhHn2rvM\nvOcUoT5QWOzcdjulnWEV1ClSfn5UzWOafhFkrVR0QdHNT4pMw0tRAKX6EsB/\nDghjzumsjxM08efsBgsiyKDbGe3j+5mM39ZpQxbGjPHJs5aS7tXUqlJD6JKc\nP9HU7Ry05FBAX8BE1OgRo5fpkhoBaW1/ptOMm6zBF7r4n9Ct/to3owwuqacv\nmIEpS1oLfMz0zQJJI8uO19N6W5JcajCQEJJqm/MdzDmEy9TSa0w/Tf4NHQVh\nPY/57SWkRt6yq26ot/FcT0XEYbwNTkO05kxu0jcF+KGiMD72vIOBGfkJRzHH\n01QlW5f6BNvKeGsW2wPFknHHz7Vr2uFuQYhynL2kPpKmi+mzKWHAp28hUkBa\nNiZs\r\n=esEt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "engines": {"node": ">= 8.9.0"}, "gitHead": "c9556505e2a73e41e1213e74fc258a27ed1473b7", "scripts": {"lint": "npm-run-all -l -p \"lint:**\"", "test": "cross-env NODE_ENV=test npm run test:coverage", "build": "cross-env NODE_ENV=production babel src -d dist --ignore \"src/**/*.test.js\" --copy-files", "clean": "del-cli dist", "start": "npm run build -- -w", "lint:js": "eslint --cache src test", "prepare": "npm run build", "pretest": "npm run lint", "release": "standard-version", "defaults": "webpack-defaults", "prebuild": "npm run clean", "security": "npm audit", "test:only": "cross-env NODE_ENV=test jest", "commitlint": "commitlint --from=master", "test:watch": "cross-env NODE_ENV=test jest --watch", "lint:prettier": "prettier \"{**/*,*}.{js,json,md,yml,css}\" --list-different", "test:coverage": "cross-env NODE_ENV=test jest --collectCoverageFrom=\"src/**/*.js\" --coverage"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/schema-utils.git", "type": "git"}, "_npmVersion": "6.11.2", "description": "webpack Validation Utils", "directories": {}, "_nodeVersion": "10.15.2", "dependencies": {"ajv": "^6.10.2", "ajv-keywords": "^3.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^5.1.0", "jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.3.0", "del-cli": "^2.0.0", "prettier": "^1.18.2", "cross-env": "^5.2.1", "@babel/cli": "^7.5.5", "babel-jest": "^24.9.0", "jest-junit": "^8.0.0", "@babel/core": "^7.5.5", "lint-staged": "^9.2.5", "npm-run-all": "^4.1.5", "@commitlint/cli": "^8.0.0", "standard-version": "^7.0.0", "@babel/preset-env": "^7.5.5", "eslint-plugin-import": "^2.18.2", "eslint-config-prettier": "^6.1.0", "@webpack-contrib/defaults": "^5.0.2", "commitlint-azure-pipelines-cli": "^1.0.2", "@commitlint/config-conventional": "^8.0.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/schema-utils_2.2.0_1567421854221_0.7354612062500321", "host": "s3://npm-registry-packages"}}, "2.3.0": {"name": "schema-utils", "version": "2.3.0", "keywords": ["webpack"], "author": {"url": "https://github.com/webpack-contrib", "name": "webpack Contrib"}, "license": "MIT", "_id": "schema-utils@2.3.0", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "d3viant0ne", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/webpack/schema-utils", "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "dist": {"shasum": "592c836507db364139a6dea7f46563d66b688a03", "tarball": "https://registry.npmjs.org/schema-utils/-/schema-utils-2.3.0.tgz", "fileCount": 8, "integrity": "sha512-0NWOS/em29583dLzou07qWp5xoKCnvm5NiDQ+IkI/Q8xP6rm1SMvQrmHseH0RSbBe4tM+LJ7IeZTqb/dF9dTRQ==", "signatures": [{"sig": "MEUCIB3sBxrNfTJC8db0M9lZliOQbssXdzb32uyJYJpCUPb0AiEA035yQ7hjqpHJ0PaHdp3mi83yLn6+8ZT0khTccI4bgR0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42752, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdjL4TCRA9TVsSAnZWagAAMakQAJTNjF6OAQuA0dqwzvgN\n8ot6k2L5XUu10GBvhPeeF5kViY1SKD71ObJFIdvPGIGcECTyvoRM/CvhjuVD\n4Tv7bxiTCwq9S1sPwHSuk8GtxYjuA/RBYSLu6SanyNNnq/eFw245IV3TZgSK\nwHmztK9y8blhWYf4JIb1KPHVBcyTog+B4U+i7RoTzhbcxcCq72vUVqMyo2zs\nrUDpXllGA9SlxxVUZMpSQndFCKdiSwSCsFWCgwUMhG30+Vkwy8vKfzMs3I8m\n5cNUh2Dd3muobTFHw57JOUAcBOVymCaeOagvMOo98toLV36LYox6o+c7V8/H\n6AP7WJt2tPoZPvS4cuoHoFXnK5RUPgGK0/rP+1q91ruIboQoh7TkFPkU87K8\nXrZnTEC0MnzTehXc+C+t1Kr0/K045sJw8TSqiWE2P0k1FI3vUlyk55Z43TPK\nmaVSb8W9S2hIfyKaAfS80rXmxhgHFtr0DClJOyPPpHRGVeKXNhqUwQf1AMHA\n6k19A5fMtgAfnUxZrwgQ5wTZl1shfiG//vYSyqHNAu+CrSx3+4ml+P9n3mue\nuPlM2QPm6jUj89vdjbUIuf4JPv9WJcPvugC1MOu6Z2cI9FfhXvq+VshkVey6\nsDfa8nMnepP7fq2w1Q6pahzfCGMm4cw+ZI1daO09kaHK8ti1iuYU0X1BRKRw\nHze7\r\n=TgeK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "engines": {"node": ">= 8.9.0"}, "gitHead": "ba01fe51c617e0a8b2a36298cf8f71fd8f5b6fef", "scripts": {"lint": "npm-run-all -l -p \"lint:**\"", "test": "cross-env NODE_ENV=test npm run test:coverage", "build": "cross-env NODE_ENV=production babel src -d dist --ignore \"src/**/*.test.js\" --copy-files", "clean": "del-cli dist", "start": "npm run build -- -w", "lint:js": "eslint --cache src test", "prepare": "npm run build", "pretest": "npm run lint", "release": "standard-version", "defaults": "webpack-defaults", "prebuild": "npm run clean", "security": "npm audit", "test:only": "cross-env NODE_ENV=test jest", "commitlint": "commitlint --from=master", "test:watch": "cross-env NODE_ENV=test jest --watch", "lint:prettier": "prettier \"{**/*,*}.{js,json,md,yml,css}\" --list-different", "test:coverage": "cross-env NODE_ENV=test jest --collectCoverageFrom=\"src/**/*.js\" --coverage"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/schema-utils.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "webpack Validation Utils", "directories": {}, "_nodeVersion": "10.15.2", "dependencies": {"ajv": "^6.10.2", "ajv-keywords": "^3.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^5.1.0", "jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.4.0", "del-cli": "^3.0.0", "prettier": "^1.18.2", "cross-env": "^6.0.0", "@babel/cli": "^7.6.2", "babel-jest": "^24.9.0", "jest-junit": "^8.0.0", "@babel/core": "^7.6.2", "lint-staged": "^9.4.0", "npm-run-all": "^4.1.5", "@commitlint/cli": "^8.2.0", "standard-version": "^7.0.0", "@babel/preset-env": "^7.6.2", "eslint-plugin-import": "^2.18.2", "eslint-config-prettier": "^6.3.0", "@webpack-contrib/defaults": "^5.0.2", "commitlint-azure-pipelines-cli": "^1.0.2", "@commitlint/config-conventional": "^8.2.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/schema-utils_2.3.0_1569504786725_0.5259988689114976", "host": "s3://npm-registry-packages"}}, "2.4.0": {"name": "schema-utils", "version": "2.4.0", "keywords": ["webpack"], "author": {"url": "https://github.com/webpack-contrib", "name": "webpack Contrib"}, "license": "MIT", "_id": "schema-utils@2.4.0", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "d3viant0ne", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/schema-utils", "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "dist": {"shasum": "44947c4da4d798a3080ec2343249f9d8e4231fd6", "tarball": "https://registry.npmjs.org/schema-utils/-/schema-utils-2.4.0.tgz", "fileCount": 8, "integrity": "sha512-VJEKOvjynJweKWFgxaikuP22zl9JwvmylH/cW1dIKZyp3DS1adBGaYPtZ6CdBSxtfP0LwQY1gNA4rIMJsnammQ==", "signatures": [{"sig": "MEYCIQDJt78Xb4lmXtRIZLw7+gFUjGi8KcGTJpG0WMwQg4kIOQIhAPAn0l0BKxQfwPmUwGRtKRPNvMSGPgzGeJFYD1pBHF0J", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44780, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdjQuOCRA9TVsSAnZWagAAqsAQAJd3Uq1z2Z2KaSygk6Ot\nJ+AtZeFxA3S6iCqzKrVr34pSdm0eGAgKZnjo2Bv6Ebn4xBUKKoZivFAphXEH\nQUkoCZ9ehbIydiGx3P2ChfYRNyr+z9mZKMDkYsKh4aKp2h4CttxmgWeBo42J\ngFP/ZL+og2bxK6eA0cPOveq3I2VAOB8bo+0alx2L44Ongv+xbRqDV2GYhQEf\ntQn3LoKF33l55rZj1SJTmW8B2LZcKWHxvjKWq4oreDc2GsJ09oZ7+2nnS6CF\nQnwpCb4Ha0eMm+YX3yX5+ZFGY+qeUDXJw556L+bYlwQVEWqNMZUH6r7eWuKR\nLvuFA0hCotnsgCK9Cl7boKN/uShZ0Kd8XOnZGHHj177FFApnyEq/PDrmmCwA\nxZFwu/KU6SaZPeXIo1vOsvQQ7/V8fbn2N87H1Jgr4ZKEZNQVsYGqjeY+jQ7a\nzqktAgft4tZsJyZXGxmy3AOrEn1c4m54RUz7Mq4hSV3jziNCac7n7CYB7HNS\nl6vnTtcMOhKmNTjs7oS+4g2Q/Q3XmrBYG/KaI4p4RowoGk+7M6Dcdj3XBvXB\nE8JniUOn09yHWRIFBONVD46Gp/EbcD7HEYmYVAjKyLqRYkpbnZ5U7kkTnW+W\n58GWvyPI3yLpQduwhIg3tN1rYBQ1mjfscJHeEjDqe6t4XJmXL8wkCQtFs5bp\nhd2N\r\n=DKHc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "index.d.ts", "engines": {"node": ">= 8.9.0"}, "gitHead": "fe4be5dfa85e24606df87ad4087b25c30baa745d", "scripts": {"lint": "npm-run-all -l -p \"lint:**\"", "test": "cross-env NODE_ENV=test npm run test:coverage", "build": "cross-env NODE_ENV=production babel src -d dist --ignore \"src/**/*.test.js\" --copy-files", "clean": "del-cli dist", "start": "npm run build -- -w", "lint:js": "eslint --cache src test", "prepare": "npm run build", "pretest": "npm run lint", "release": "standard-version", "defaults": "webpack-defaults", "prebuild": "npm run clean", "security": "npm audit", "test:only": "cross-env NODE_ENV=test jest", "commitlint": "commitlint --from=master", "test:watch": "cross-env NODE_ENV=test jest --watch", "lint:prettier": "prettier \"{**/*,*}.{js,json,md,yml,css}\" --list-different", "test:coverage": "cross-env NODE_ENV=test jest --collectCoverageFrom=\"src/**/*.js\" --coverage"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/schema-utils.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "webpack Validation Utils", "directories": {}, "_nodeVersion": "10.15.2", "dependencies": {"ajv": "^6.10.2", "ajv-keywords": "^3.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^5.1.0", "jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.4.0", "del-cli": "^3.0.0", "prettier": "^1.18.2", "cross-env": "^6.0.0", "@babel/cli": "^7.6.2", "babel-jest": "^24.9.0", "jest-junit": "^8.0.0", "@babel/core": "^7.6.2", "lint-staged": "^9.4.0", "npm-run-all": "^4.1.5", "@commitlint/cli": "^8.2.0", "standard-version": "^7.0.0", "@babel/preset-env": "^7.6.2", "@types/json-schema": "^7.0.3", "eslint-plugin-import": "^2.18.2", "eslint-config-prettier": "^6.3.0", "@webpack-contrib/defaults": "^5.0.2", "commitlint-azure-pipelines-cli": "^1.0.2", "@commitlint/config-conventional": "^8.2.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/schema-utils_2.4.0_1569524622275_0.30116992902704154", "host": "s3://npm-registry-packages"}}, "2.4.1": {"name": "schema-utils", "version": "2.4.1", "keywords": ["webpack"], "author": {"url": "https://github.com/webpack-contrib", "name": "webpack Contrib"}, "license": "MIT", "_id": "schema-utils@2.4.1", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "d3viant0ne", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/schema-utils", "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "dist": {"shasum": "e89ade5d056dc8bcaca377574bb4a9c4e1b8be56", "tarball": "https://registry.npmjs.org/schema-utils/-/schema-utils-2.4.1.tgz", "fileCount": 9, "integrity": "sha512-RqYLpkPZX5Oc3fw/kHHHyP56fg5Y+XBpIpV8nCg0znIALfq3OH+Ea9Hfeac9BAMwG5IICltiZ0vxFvJQONfA5w==", "signatures": [{"sig": "MEYCIQDgUYHwJFmzzMSUujYHCmPUNxkJtOala/IscLZV1lXc0gIhAI1+s3OLW+8b94ud21NWWlJd709VAwwa0pBtzM+LJDV0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46006, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdjfGSCRA9TVsSAnZWagAAc98P/in/vMgQR0nHyjU8zA9R\nTQCZa7TGQ9NeUzfEKJlMW0+Xac0cKsXf72tXRVpgvXMviAaw5ueGSaVmlq+o\nmryGJOgA/Tai51qd9bSNFkX4jY348mKglMLfYRI4g+q87qnO0NbJPmQ7Sq2L\nl4TwDDXD9UpGZfP+ifwGyDLZoQfhYSlFvXAT9XOf0Ie7hTFUsZ+cSLXHro8/\ngOa5VPQ0sMzfwDkTebi6AIsNnZrdG//PoByqeXOrMLdAmHlCjK/5EXhj+Y8B\nAVA6i+7AWW/ic9mf7g8IDXOq3dEFQ6NK1pAczK+W6xJd5FUoh78tBqME39qX\n1z/wJY7Aar4y789LJ1dzPyPKPoMqWNjJ4vHeRLaLexJGfV8jpFNs1Uh9iWxO\nh5MyuXVIdvQItg0PEW9iTpnlMHNo69FctOV9z1rAWHARRjQhUA7EMRlZTsIE\ncmpjg02M4BbSlB8hrknlvOIhTU6+6lRdJX4INirEslSGz+NETkjEU+ulTZJ5\nS/JLMpUVSh46nwR1X51efw8pUjeVnY89YEhyvRs0TdBtigiBiCYpJEgT2Lp7\nFvyRCyndaZQjCTAtVuuCagH+VxY8T9wnGat3ctcn0T6TyxsL3O2xhEjTIfll\nZe9/3stDL34R4Acy8lBmJ/4uS0bOPrgwi9L21KZFUX+X57g/8wQF9FJXN5EO\ndg/2\r\n=POmY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "index.d.ts", "engines": {"node": ">= 8.9.0"}, "gitHead": "338db8b732aeb247db012b4bfae2fddf17d3e57e", "scripts": {"lint": "npm-run-all -l -p \"lint:**\"", "test": "cross-env NODE_ENV=test npm run test:coverage", "build": "cross-env NODE_ENV=production babel src -d dist --ignore \"src/**/*.test.js\" --copy-files", "clean": "del-cli dist", "start": "npm run build -- -w", "lint:js": "eslint --cache src test", "prepare": "npm run build", "pretest": "npm run lint", "release": "standard-version", "defaults": "webpack-defaults", "prebuild": "npm run clean", "security": "npm audit", "test:only": "cross-env NODE_ENV=test jest", "commitlint": "commitlint --from=master", "test:watch": "cross-env NODE_ENV=test jest --watch", "lint:prettier": "prettier \"{**/*,*}.{js,json,md,yml,css}\" --list-different", "test:coverage": "cross-env NODE_ENV=test jest --collectCoverageFrom=\"src/**/*.js\" --coverage"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/schema-utils.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "webpack Validation Utils", "directories": {}, "_nodeVersion": "10.15.2", "dependencies": {"ajv": "^6.10.2", "ajv-keywords": "^3.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^5.1.0", "jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.4.0", "del-cli": "^3.0.0", "prettier": "^1.18.2", "cross-env": "^6.0.0", "@babel/cli": "^7.6.2", "babel-jest": "^24.9.0", "jest-junit": "^8.0.0", "@babel/core": "^7.6.2", "lint-staged": "^9.4.0", "npm-run-all": "^4.1.5", "@commitlint/cli": "^8.2.0", "standard-version": "^7.0.0", "@babel/preset-env": "^7.6.2", "@types/json-schema": "^7.0.3", "eslint-plugin-import": "^2.18.2", "eslint-config-prettier": "^6.3.0", "@webpack-contrib/defaults": "^5.0.2", "commitlint-azure-pipelines-cli": "^1.0.2", "@commitlint/config-conventional": "^8.2.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/schema-utils_2.4.1_1569583505137_0.7742291363655027", "host": "s3://npm-registry-packages"}}, "2.5.0": {"name": "schema-utils", "version": "2.5.0", "keywords": ["webpack"], "author": {"url": "https://github.com/webpack-contrib", "name": "webpack Contrib"}, "license": "MIT", "_id": "schema-utils@2.5.0", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "d3viant0ne", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/schema-utils", "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "dist": {"shasum": "8f254f618d402cc80257486213c8970edfd7c22f", "tarball": "https://registry.npmjs.org/schema-utils/-/schema-utils-2.5.0.tgz", "fileCount": 10, "integrity": "sha512-32ISrwW2scPXHUSusP8qMg5dLUawKkyV+/qIEV9JdXKx+rsM6mi8vZY8khg2M69Qom16rtroWXD3Ybtiws38gQ==", "signatures": [{"sig": "MEYCIQDKX99UhNT94GNWQArgi/xfDVOEAmzMMSyZP5ACAXYy4QIhAKln68qLwYuNm6zShE9id63mm24jZAWmEEiC/Pplhow7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51438, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdpbZOCRA9TVsSAnZWagAAl1sP/A8SkPNkiuu/mfyoGUR/\nskJ5UdS7VHa3uVaMvkmOYx46bd9zRbC2Q+E0MbVLMXVtePecr6qykbQ31SHa\nCAMoa4OvllhCsCldfYdXH7vSO496CCMu3Km2joeph6q36IOxrjqXSeol9Sbs\nM8VYiMyNRQWUNQhd8IUK6Vk4UtOGcOurzVMghCJBCNK4/KzUSnhhMmHwv1oq\ndrWcvPx5nNN9d2hJxB2jN0kUIM2oVbPKVo4VtJaQzQcxBQkeGmKVRlUIXP5+\n2M1GE717nJF2vicCzphncX7R9p5Dv0NUzjykN+hKtyhXZop0wYkXjFwv2GLj\n+3Qk2VRj3NRwVQZsMoBFZfahsT2xfLQa5k8EO4lKgD5o6UQql7i6L+HIMLzx\n2T0SQPy+QCW9zEHt5SScNtJ27TysvASj/cTjrXYcIQMBji0TW01eJrzf4mBC\nJVdCppp2SG7K53UZGw/eGisEX+e7PvLNHGSQPHsOwy3421dn8rXwHx98aelE\nJpNA4VYJMQRnCFRBAhp87YVvlwEPfGPt4DxsKNIYgR3DydXlFYURAoUOQ18n\nowJjQYfc99ME/TgF43l2tuFz1m2zTOVg5zg07NWhlLY7ZRiRRQwXebRoUS9R\n6XGpzFK07uo8GBqicym+Zd1EyB3hdcBuYM+z/kui4boGq665k4OiDy8LUiqh\n9h4z\r\n=eC5B\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "index.d.ts", "engines": {"node": ">= 8.9.0"}, "gitHead": "b98e06cda3cd207ba9f35ca972c601c017700d2d", "scripts": {"lint": "npm-run-all -l -p \"lint:**\"", "test": "cross-env NODE_ENV=test npm run test:coverage", "build": "cross-env NODE_ENV=production babel src -d dist --ignore \"src/**/*.test.js\" --copy-files", "clean": "del-cli dist", "start": "npm run build -- -w", "lint:js": "eslint --cache src test", "prepare": "npm run build", "pretest": "npm run lint", "release": "standard-version", "defaults": "webpack-defaults", "prebuild": "npm run clean", "security": "npm audit", "test:only": "cross-env NODE_ENV=test jest", "commitlint": "commitlint --from=master", "test:watch": "cross-env NODE_ENV=test jest --watch", "lint:prettier": "prettier \"{**/*,*}.{js,json,md,yml,css}\" --list-different", "test:coverage": "cross-env NODE_ENV=test jest --collectCoverageFrom=\"src/**/*.js\" --coverage"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/schema-utils.git", "type": "git"}, "_npmVersion": "6.12.0", "description": "webpack Validation Utils", "directories": {}, "_nodeVersion": "10.15.2", "dependencies": {"ajv": "^6.10.2", "ajv-keywords": "^3.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^5.1.0", "jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.4.0", "del-cli": "^3.0.0", "prettier": "^1.18.2", "cross-env": "^6.0.0", "@babel/cli": "^7.6.2", "babel-jest": "^24.9.0", "jest-junit": "^8.0.0", "@babel/core": "^7.6.2", "lint-staged": "^9.4.0", "npm-run-all": "^4.1.5", "@commitlint/cli": "^8.2.0", "standard-version": "^7.0.0", "@babel/preset-env": "^7.6.2", "@types/json-schema": "^7.0.3", "eslint-plugin-import": "^2.18.2", "eslint-config-prettier": "^6.3.0", "@webpack-contrib/defaults": "^5.0.2", "commitlint-azure-pipelines-cli": "^1.0.2", "@commitlint/config-conventional": "^8.2.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/schema-utils_2.5.0_1571141197865_0.07724758074500793", "host": "s3://npm-registry-packages"}}, "2.6.0": {"name": "schema-utils", "version": "2.6.0", "keywords": ["webpack"], "author": {"url": "https://github.com/webpack-contrib", "name": "webpack Contrib"}, "license": "MIT", "_id": "schema-utils@2.6.0", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "d3viant0ne", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/schema-utils", "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "dist": {"shasum": "68a259aabbef9d08d1252c2e63c398e476308e80", "tarball": "https://registry.npmjs.org/schema-utils/-/schema-utils-2.6.0.tgz", "fileCount": 14, "integrity": "sha512-UlPB1ME4i/71cih/Rv92gK8043CrJTc2mjkyxDp4pdJ7ZfzY0g0hdGjjDB23jX3X+NXSneCdQbScGhn6K2tbpQ==", "signatures": [{"sig": "MEYCIQC1vwiXb/8bSw3knjgA3le52x2OOuzcV/jTQaio4cKW7AIhAJ+np4kaeLeIGI2JT/a30jglvRWiKUkAaIdVJicrg5La", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70543, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd3mrBCRA9TVsSAnZWagAAJEYQAI/awraKGa/yOZ38J29A\nRRPuUSzqFAolekOXA1y2lRxlkjYKSou2aPqI8hRFZdl9phmSsJ7Q1EW65aC5\ntJ8wpLrPJZC4FQBEPv8YxxGkwRhHxsc+iDL6T5pVs7k/OQ0mTuXVAQRSGBLq\nppFpD1XC24EUKXocQ7mftRpFR0BLPp7mj7r1ezu7UKJIYDtlf5dqVsPjg1YG\nZUDfLnMN8GnK25C50iJCZedrYFYlngva0GayTevkIIuv1Kuj8vcu9ehvcW8f\n74TQyWtutIQh3sk8K6rHLrexuu72BN14pKfDP2IDxh8iJDjBBnte6FtnD98s\n65DFcbmmvGww8MexsQ4gQeeOd2K3Qdgv3qa1Ld4tgZTHZ8R8SfhnUBduQUtr\ng3b7YzBxDQd2/N8bKh5cFCgPI71hCXRgb94DqD8CpTvShIDzyz/S5XDJE2HC\nvLNx0AH9QrivxhkTktZXn0nscq//kzpzABMbrufXONd8/oEkmmd2TFwIQGXg\nmQSVVui1+gXRKs/kmBKXbmmKHJmAK9VfpDaeNr+PaBfBwezM3zw8d0cW7T+o\nCunrRMEWvVubsHuyET3TmVrJWY+vb8a/QVSh4b2rCqbUfZzCW5T9AJxK3sGJ\nHv0NzLXM8Q/LMRQd5WR9ascAhRiAth1wXVdGd8DYV+BMLKabOfrrfMLlHk8M\nhXfK\r\n=7kRr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "declarations/index.d.ts", "engines": {"node": ">= 8.9.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}, "gitHead": "6c6973b9decc1179c064971a770d4027da86e0b2", "scripts": {"lint": "npm-run-all -l -p \"lint:**\"", "test": "npm run test:coverage", "build": "npm-run-all -p \"build:**\"", "clean": "del-cli dist declarations", "start": "npm run build -- -w", "lint:js": "eslint --cache .", "prepare": "npm run build", "pretest": "npm run lint", "release": "standard-version", "defaults": "webpack-defaults", "prebuild": "npm run clean", "security": "npm audit", "test:only": "cross-env NODE_ENV=test jest", "build:code": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "lint:types": "tsc --pretty --noEmit", "test:watch": "npm run test:only -- --watch", "build:types": "tsc --declaration --emitDeclarationOnly --outDir declarations && prettier \"declarations/**/*.ts\" --write", "lint:prettier": "prettier \"{**/*,*}.{js,json,md,yml,css,ts}\" --list-different", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/schema-utils.git", "type": "git"}, "_npmVersion": "6.13.1", "description": "webpack Validation Utils", "directories": {}, "_nodeVersion": "10.15.2", "dependencies": {"ajv": "^6.10.2", "ajv-keywords": "^3.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^5.1.0", "jest": "^24.9.0", "husky": "^3.1.0", "eslint": "^6.7.1", "del-cli": "^3.0.0", "prettier": "^1.19.1", "cross-env": "^6.0.3", "@babel/cli": "^7.7.4", "babel-jest": "^24.9.0", "jest-junit": "^9.0.0", "typescript": "^3.7.2", "@babel/core": "^7.7.4", "lint-staged": "^9.5.0", "npm-run-all": "^4.1.5", "@commitlint/cli": "^8.2.0", "standard-version": "^7.0.1", "@babel/preset-env": "^7.7.4", "@types/json-schema": "^7.0.3", "eslint-plugin-import": "^2.18.2", "eslint-config-prettier": "^6.7.0", "@webpack-contrib/defaults": "^6.2.0", "commitlint-azure-pipelines-cli": "^1.0.2", "@commitlint/config-conventional": "^8.2.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/schema-utils_2.6.0_1574857408783_0.42345536104907167", "host": "s3://npm-registry-packages"}}, "2.6.1": {"name": "schema-utils", "version": "2.6.1", "keywords": ["webpack"], "author": {"url": "https://github.com/webpack-contrib", "name": "webpack Contrib"}, "license": "MIT", "_id": "schema-utils@2.6.1", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "d3viant0ne", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/schema-utils", "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "dist": {"shasum": "eb78f0b945c7bcfa2082b3565e8db3548011dc4f", "tarball": "https://registry.npmjs.org/schema-utils/-/schema-utils-2.6.1.tgz", "fileCount": 14, "integrity": "sha512-0WXHDs1VDJyo+Zqs9TKLKyD/h7yDpHUhEFsM2CzkICFdoX1av+GBq/J2xRTFfsQO5kBfhZzANf2VcIm84jqDbg==", "signatures": [{"sig": "MEQCICbmYhqT+r9jTD5cciSPDFxUi5/FmvacgoJMXuLwbLklAiAyh/Kk8KPnJtVRa4gBHN8gdVoowe51LMXi476fydIKaA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70869, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd38ewCRA9TVsSAnZWagAAJ7oP/3sRJ7QnsHXYZmmw+OAp\nBmsUSJ7ZFIIQhjze0uFRJNepE7glrTiWnMLY56SfWNvZsk0Q6R+56BxdyT45\n7aCuSZXLCxtwZm7CRjcE+A0CWzZk5yQqPVLe5i+RPABE+sSFSA418MU+PoLQ\n5z9HbJl813NRoDb6tyAVBnrDBkiQsBxxFHx/u8K0+M6YRLVtznLb/hA/FYNx\ndxVyAkcnXO88oyji58KAsnQ4UXVLVHh1A9deETW6niQmLRfGl3DlcIJ5uBD4\nNkOxAgK1jvyGD7JlN0hu55yBYdWdSYwluESIRFcK+65V3xQnXz195dwKC4dI\n7hQ15M+rvBbfM7xnFxgOx/3ZEpXyocLI6mYALQokM0n69g2B5Ruefui/jJ0x\nJqYB6gPI9Kok4oqygviTy96NetYq1gmaVZfXCo5O2QO2BTINmMj0chNIAWha\n/H7k53nGk1nXiliRiwbEto9JcRvi3XBqYdL4iXP6JO03BR2uHg9cNyxnq/9f\ngdMAZ/5R3PYXjNyHw0uc/Xgj/56XSPtxio0N/Fi+5FcKB6b1dzolr2cdL1Jo\n9pHaOpoXNiJxtIWY2gmpH4FA3qtapkmZCIswtmgSZTs3soJVFOssD1YdL/y7\ndIGsjp3LauORzz19KW/fD6cR+h1WVvRrP1xA3G8cXUjbGHbROQ+JJfx0OToj\nIxEf\r\n=zyqb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "declarations/index.d.ts", "engines": {"node": ">= 8.9.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}, "gitHead": "6caaa59cf40af948ea3d9658f4c32bd79b6c37a5", "scripts": {"lint": "npm-run-all -l -p \"lint:**\"", "test": "npm run test:coverage", "build": "npm-run-all -p \"build:**\"", "clean": "del-cli dist declarations", "start": "npm run build -- -w", "lint:js": "eslint --cache .", "prepare": "npm run build", "pretest": "npm run lint", "release": "standard-version", "defaults": "webpack-defaults", "prebuild": "npm run clean", "security": "npm audit", "test:only": "cross-env NODE_ENV=test jest", "build:code": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "lint:types": "tsc --pretty --noEmit", "test:watch": "npm run test:only -- --watch", "build:types": "tsc --declaration --emitDeclarationOnly --outDir declarations && prettier \"declarations/**/*.ts\" --write", "lint:prettier": "prettier \"{**/*,*}.{js,json,md,yml,css,ts}\" --list-different", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/schema-utils.git", "type": "git"}, "_npmVersion": "6.13.1", "description": "webpack Validation Utils", "directories": {}, "_nodeVersion": "10.15.2", "dependencies": {"ajv": "^6.10.2", "ajv-keywords": "^3.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^5.1.0", "jest": "^24.9.0", "husky": "^3.1.0", "eslint": "^6.7.1", "del-cli": "^3.0.0", "prettier": "^1.19.1", "cross-env": "^6.0.3", "@babel/cli": "^7.7.4", "babel-jest": "^24.9.0", "jest-junit": "^9.0.0", "typescript": "^3.7.2", "@babel/core": "^7.7.4", "lint-staged": "^9.5.0", "npm-run-all": "^4.1.5", "@commitlint/cli": "^8.2.0", "standard-version": "^7.0.1", "@babel/preset-env": "^7.7.4", "@types/json-schema": "^7.0.3", "eslint-plugin-import": "^2.18.2", "eslint-config-prettier": "^6.7.0", "@webpack-contrib/defaults": "^6.2.0", "commitlint-azure-pipelines-cli": "^1.0.2", "@commitlint/config-conventional": "^8.2.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/schema-utils_2.6.1_1574946735632_0.10563936035910837", "host": "s3://npm-registry-packages"}}, "2.6.2": {"name": "schema-utils", "version": "2.6.2", "keywords": ["webpack"], "author": {"url": "https://github.com/webpack-contrib", "name": "webpack Contrib"}, "license": "MIT", "_id": "schema-utils@2.6.2", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "d3viant0ne", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/schema-utils", "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "dist": {"shasum": "9205ec5978709b0d9edbccb9a316faf11617a017", "tarball": "https://registry.npmjs.org/schema-utils/-/schema-utils-2.6.2.tgz", "fileCount": 14, "integrity": "sha512-sazKNMBX/jwrXRkOI7N6dtiTVYqzSckzol8SGuHt0lE/v3xSW6cUkOqzu6Bq2tW+dlUzq3CWIqHU3ZKauliqdg==", "signatures": [{"sig": "MEUCIHge+b80K1/Xyyxd7FX9xg3L89+LXaZXhT9r9G7Zhg64AiEA2Gg5Oq0xcTdQp1RJeaj3mO1herX8ubyrj6NSkjyVazQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71301, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHb/SCRA9TVsSAnZWagAAiNIP/34eVVyJZnDP48yhpSLv\nmWyly3YEksfSiqIFX74XusSs6H4dclsqSqLUcjR4hxrSBeRwuKwT0pNf8epS\npl7rnC7OUDHSOqBXPpQ58aeudfgPMkLnvNEClGNp+GlN5YmLmMMBSlOPq/gx\neqImYaJxRfXFgip1fv+hZjRLl/6coo4ocpfeACIqXyy8to60HrEpHBmAZXUi\naqwwh7cAVUy+43ii7/wsNkd79HFdfqL8Ms6Amdz88021kUrrh3m380oMF/9q\nTSLtfvoK4Vvvz+ExFEZ7FSJeyyL2cvUHAvi40enoQXg05ZS7ZAFk070CKtD2\nifxAa6uYX858g88vtn9wD4ITBitnl2jb5eCA8w6fd3iOXu00frSF+oZrXxYC\nVetGctiaWu+X/VkpFO/SSzbqUVfuEtdCKqbvQqdNXjr69/3XogbYXoplltlE\nzJh2qNpTdPvqqNzUcJ3pR3f4sSooNwn3F3VJljhWDkw6MDKBnxQK7l63tN2y\nELbRkBQpex4doYIt15tTiwk9LSvLFTTFNL1PJvBSn/13frL2i6dK1MxVf6gD\n9v3zVAu859yqyv+zgDAYHvjGQpvE+CXtqqK/ixke9+I5mPnCADdT3hXPMsSI\n7IewpxJYLk9iNZe679fhGwt05bGPUGsogBqF1IRMQsPuiNTFFHi3ogJ+5yOY\nOSZE\r\n=fPjA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "declarations/index.d.ts", "engines": {"node": ">= 8.9.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}, "gitHead": "94e517b041f9f051e25092ae7162f5b61834a9af", "scripts": {"lint": "npm-run-all -l -p \"lint:**\"", "test": "npm run test:coverage", "build": "npm-run-all -p \"build:**\"", "clean": "del-cli dist declarations", "start": "npm run build -- -w", "lint:js": "eslint --cache .", "prepare": "npm run build", "pretest": "npm run lint", "release": "standard-version", "defaults": "webpack-defaults", "prebuild": "npm run clean", "security": "npm audit", "test:only": "cross-env NODE_ENV=test jest", "build:code": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "lint:types": "tsc --pretty --noEmit", "test:watch": "npm run test:only -- --watch", "build:types": "tsc --declaration --emitDeclarationOnly --outDir declarations && prettier \"declarations/**/*.ts\" --write", "lint:prettier": "prettier \"{**/*,*}.{js,json,md,yml,css,ts}\" --list-different", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/schema-utils.git", "type": "git"}, "_npmVersion": "6.13.6", "description": "webpack Validation Utils", "directories": {}, "_nodeVersion": "10.15.2", "dependencies": {"ajv": "^6.10.2", "ajv-keywords": "^3.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^5.1.0", "jest": "^24.9.0", "husky": "^4.0.6", "eslint": "^6.8.0", "del-cli": "^3.0.0", "prettier": "^1.19.1", "cross-env": "^6.0.3", "@babel/cli": "^7.8.3", "babel-jest": "^24.9.0", "jest-junit": "^10.0.0", "typescript": "^3.7.4", "@babel/core": "^7.8.3", "lint-staged": "^9.5.0", "npm-run-all": "^4.1.5", "@commitlint/cli": "^8.3.4", "standard-version": "^7.0.1", "@babel/preset-env": "^7.8.3", "@types/json-schema": "^7.0.4", "eslint-plugin-import": "^2.20.0", "eslint-config-prettier": "^6.9.0", "@webpack-contrib/defaults": "^6.3.0", "commitlint-azure-pipelines-cli": "^1.0.3", "@commitlint/config-conventional": "^8.3.4", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/schema-utils_2.6.2_1579007953704_0.15739376554868922", "host": "s3://npm-registry-packages"}}, "2.6.3": {"name": "schema-utils", "version": "2.6.3", "keywords": ["webpack"], "author": {"url": "https://github.com/webpack-contrib", "name": "webpack Contrib"}, "license": "MIT", "_id": "schema-utils@2.6.3", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "d3viant0ne", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/schema-utils", "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "dist": {"shasum": "f889690172941febdf56b7e6785428d717d84786", "tarball": "https://registry.npmjs.org/schema-utils/-/schema-utils-2.6.3.tgz", "fileCount": 14, "integrity": "sha512-4vpF5nc+MYrHjsAnA43R75OtvjYUOFDmuwAdhzW7g0/zlFsepaMX/NLhOThg0L+5mqxIw7IK1oR9M3MsrklMsA==", "signatures": [{"sig": "MEYCIQC8Da5j8VWNGDHeayzq/n5X5tSkHuxpo5nEHxNfyXOhywIhAKYg771k1VFe29p8+OAJ7pUaoTQq/QOlhpajeb0sAo4R", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71628, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeIYy3CRA9TVsSAnZWagAADoYP/A0z7Cc9mtjhKasCaKhN\ntCtIm5iuiiNMwLG4mD9Rs5rxXV9TsjzM4dlyU47cqCUfyVEJjipX0MgvlJDK\nWP5f4ThYj4AxYeKcYlfXdgsB92Fk6Mh8eyPWLSTUu2qajYxei5BN4WM5l7on\nK36ZK75smCsCbC1b6lzGuDfMMm7Bqvc+MwBnHYtW3XD5voF6R2IDPSKoq1Gf\nVvXptxY95KgWSIYxPjDMfl8pKy+OHMGL/Fc1xFi7Z/rJWXlA7ubziNLoqJRY\ndJPc12XqWoKl20plfkk4vlRh7GBQZISLQX+p5DNL79IuuJU6GV6LsQ3qOVyz\neY16MW9Gy16iCfmSQZO2oB3Tv6C7UmjaTyC8vy7KGP7icfNFhLdUOOQEzA+c\njyJ3Ka4RhVm+VGkdCn/jjPFYV/1j1HSaf6Vgl93YF5up8hrjejNKoDEAbM/L\nECQ5D92o0q34OYNdrpAgu7z34x8suv5V0Iofbh0ZFJp/Olh6rKQOHO8VwoQb\n7C5H+mzdiUnU2yO9Fms+oP06RRQgMWnP2g45nQyzgcD8NcUyIPhlfKkL+Q9g\nySnHgl0hT2mDIhrPZJOmcJLwe10xko9kQVnV9R6g07PEXxr3tCMStHG76vk8\n0x42IZbHnTxFCJs2B1MN0ehwqrj9YJfX+ZqsB5JbFB3LN+kuxYQWDw05eM2u\n+TV+\r\n=r/i0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "declarations/index.d.ts", "engines": {"node": ">= 8.9.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}, "gitHead": "669ef56bc654b215e3eb8ce02ec531253fbd3054", "scripts": {"lint": "npm-run-all -l -p \"lint:**\"", "test": "npm run test:coverage", "build": "npm-run-all -p \"build:**\"", "clean": "del-cli dist declarations", "start": "npm run build -- -w", "lint:js": "eslint --cache .", "prepare": "npm run build", "pretest": "npm run lint", "release": "standard-version", "defaults": "webpack-defaults", "prebuild": "npm run clean", "security": "npm audit", "test:only": "cross-env NODE_ENV=test jest", "build:code": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "lint:types": "tsc --pretty --noEmit", "test:watch": "npm run test:only -- --watch", "build:types": "tsc --declaration --emitDeclarationOnly --outDir declarations && prettier \"declarations/**/*.ts\" --write", "lint:prettier": "prettier \"{**/*,*}.{js,json,md,yml,css,ts}\" --list-different", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/schema-utils.git", "type": "git"}, "_npmVersion": "6.13.6", "description": "webpack Validation Utils", "directories": {}, "_nodeVersion": "10.15.2", "dependencies": {"ajv": "^6.10.2", "ajv-keywords": "^3.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^5.1.0", "jest": "^24.9.0", "husky": "^4.0.10", "eslint": "^6.8.0", "del-cli": "^3.0.0", "prettier": "^1.19.1", "cross-env": "^6.0.3", "@babel/cli": "^7.8.3", "babel-jest": "^24.9.0", "jest-junit": "^10.0.0", "typescript": "^3.7.5", "@babel/core": "^7.8.3", "lint-staged": "^9.5.0", "npm-run-all": "^4.1.5", "@commitlint/cli": "^8.3.5", "standard-version": "^7.0.1", "@babel/preset-env": "^7.8.3", "@types/json-schema": "^7.0.4", "eslint-plugin-import": "^2.20.0", "eslint-config-prettier": "^6.9.0", "@webpack-contrib/defaults": "^6.3.0", "commitlint-azure-pipelines-cli": "^1.0.3", "@commitlint/config-conventional": "^8.3.4", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/schema-utils_2.6.3_1579257014810_0.07111573525729709", "host": "s3://npm-registry-packages"}}, "2.6.4": {"name": "schema-utils", "version": "2.6.4", "keywords": ["webpack"], "author": {"url": "https://github.com/webpack-contrib", "name": "webpack Contrib"}, "license": "MIT", "_id": "schema-utils@2.6.4", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "d3viant0ne", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/schema-utils", "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "dist": {"shasum": "a27efbf6e4e78689d91872ee3ccfa57d7bdd0f53", "tarball": "https://registry.npmjs.org/schema-utils/-/schema-utils-2.6.4.tgz", "fileCount": 14, "integrity": "sha512-VNjcaUxVnEeun6B2fiiUDjXXBtD4ZSH7pdbfIu1pOFwgptDPLMo/z9jr4sUfsjFVPqDCEin/F7IYlq7/E6yDbQ==", "signatures": [{"sig": "MEUCICQ3eudvy3c9xXJezIPS6LloU0MmmRTHfYNTTEwMAEWPAiEA+R92+EFiENX3lVX4EQh6200la4PLoq1MEZuyRbCYEQE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71936, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeIZ7iCRA9TVsSAnZWagAAi3oP/2y4jSCF7n2QTuCRMa0z\nQRHOnNtZE4+Z0/oe9HWVh7AKQDSUEI5FPJU7qjlSyHAowRWpF6UWcmkm9/JY\nYmJuPAVGoo1ON/rB193Px6D/dgC92u7xAa/LqiMZjLR4+y4r/1lqiWUnhGme\nMJ4c1SLLdJX7CdZrnL//3O6Ox7pCpmFbLrTixjJEa7AU2KUYbUTqVmKc66bG\nSq6iaB7IoqfgrQc+r5XPWw2SVRGfQ4UY5bfoIdHC6nKUz7RgxlRHngo9GDno\n/rcNtZdbZ7kDCJAkNhHzi1xsmxj8IcvXfa0eZd/UCkGFZ1HpVoAGPF0dgruA\nGTKDbYhinDZOQQGb8OQFTaK68azOtL0Yo0KMNn40yAE1k+k5PHHHu5C1CK9Q\nK4i5LuIlxDzVw1afSOvo0QJTAYMzC6upfOplJ/44xyikyYlbzQn7IcOfi0M8\n9E6abwn93MCDSx/ruOAqRoQgTNpiQ2XId08t5YFOstQTsZ8LYY0SAS28PURQ\nNy12hgEIPE8ZQD744XklwOGTIUKSIUKLIIJNv1jCn1fnm3BJkvuC7hZZ+tUM\nqH5qGVCyohI1JQD2tnt4a41eQriwtrlfF5RuCuSs8nme9cDa3qtlcPLJw5v3\nS/Yf+6EiLNw0X2BjhLhB4uiNQie/1MYpLjZNgg8Wpiz/1rgDYTbKT/a5b4Kq\ntbW3\r\n=38ZJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "declarations/index.d.ts", "engines": {"node": ">= 8.9.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}, "gitHead": "72387e3417cf490bc2f7d22ec0d4066227b0696e", "scripts": {"lint": "npm-run-all -l -p \"lint:**\"", "test": "npm run test:coverage", "build": "npm-run-all -p \"build:**\"", "clean": "del-cli dist declarations", "start": "npm run build -- -w", "lint:js": "eslint --cache .", "prepare": "npm run build", "pretest": "npm run lint", "release": "standard-version", "defaults": "webpack-defaults", "prebuild": "npm run clean", "security": "npm audit", "test:only": "cross-env NODE_ENV=test jest", "build:code": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "lint:types": "tsc --pretty --noEmit", "test:watch": "npm run test:only -- --watch", "build:types": "tsc --declaration --emitDeclarationOnly --outDir declarations && prettier \"declarations/**/*.ts\" --write", "lint:prettier": "prettier \"{**/*,*}.{js,json,md,yml,css,ts}\" --list-different", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/schema-utils.git", "type": "git"}, "_npmVersion": "6.13.6", "description": "webpack Validation Utils", "directories": {}, "_nodeVersion": "10.15.2", "dependencies": {"ajv": "^6.10.2", "ajv-keywords": "^3.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^5.1.0", "jest": "^24.9.0", "husky": "^4.0.10", "eslint": "^6.8.0", "del-cli": "^3.0.0", "prettier": "^1.19.1", "cross-env": "^6.0.3", "@babel/cli": "^7.8.3", "babel-jest": "^24.9.0", "jest-junit": "^10.0.0", "typescript": "^3.7.5", "@babel/core": "^7.8.3", "lint-staged": "^9.5.0", "npm-run-all": "^4.1.5", "@commitlint/cli": "^8.3.5", "standard-version": "^7.0.1", "@babel/preset-env": "^7.8.3", "@types/json-schema": "^7.0.4", "eslint-plugin-import": "^2.20.0", "eslint-config-prettier": "^6.9.0", "@webpack-contrib/defaults": "^6.3.0", "commitlint-azure-pipelines-cli": "^1.0.3", "@commitlint/config-conventional": "^8.3.4", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/schema-utils_2.6.4_1579261666107_0.8532225834628222", "host": "s3://npm-registry-packages"}}, "2.6.5": {"name": "schema-utils", "version": "2.6.5", "keywords": ["webpack"], "author": {"url": "https://github.com/webpack-contrib", "name": "webpack Contrib"}, "license": "MIT", "_id": "schema-utils@2.6.5", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "d3viant0ne", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/schema-utils", "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "dist": {"shasum": "c758f0a7e624263073d396e29cd40aa101152d8a", "tarball": "https://registry.npmjs.org/schema-utils/-/schema-utils-2.6.5.tgz", "fileCount": 14, "integrity": "sha512-5KXuwKziQrTVHh8j/Uxz+QUbxkaLW9X/86NBlx/gnKgtsZA2GIVMUn17qWhRFwF8jdYb3Dig5hRO/W5mZqy6SQ==", "signatures": [{"sig": "MEYCIQDYic+lMi0RYpptotHB0CGvbdAIbiNiiwYj8hUwY6Q/ZgIhAJFqUNWG7yJ7khxKV8Mgifuc0MjRyYYlHAuS1QF8juel", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73824, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeaNQGCRA9TVsSAnZWagAAtpUP/jFChxYMT4O2Yvd9+5Jx\nl0UPaBpQSzYq7SWIC9wQjoEUqDKuv9k8XyZc76FFcIeL+LOAnTEJYXPdqTQb\nNlugMKF4AVVBorMeLE2T8wk4FHpGoCBoJn5zcRch919oWNslhaparM8aJY9j\nXvVl1xj3BVxUqMGoOpMKh9GiUpjXvnK82SPlKgJ26Vfdu8K496DFoNIEtbq4\nQrfKhVVW0JffWqmJnqt2Hu6mnwQZd3t7kELObU1j7U4xCLSP/AilmX1XBUmH\nYbMnOvJTMCbx7x65TxNCYRNz7O25CmoDvp2l7plM6wZEuUnTJ2Pty+KgkO7a\n8R+FErzL2QcdxbY2JR8pVVgoA3LIDHD1vSsOgFlM2dyS27IBAw2GBiOQGsXJ\nBuE9fwffymXHxd9rxmzjur37Ng2HD4Qst/ZM4BlD8RnIontiOa3crOIgSOT8\n+0odAcbuIzB2EEOvSPxr0EnzM+UW5uPr8eGTSCzWcAGqgWU2tk6gTvEthF53\naRpNcBBPncCt/sec/4/GiHhY46CA/dAOqU0qUJIM1xdq2rrb9oMXgwkTY/KN\nEelBgW62IlEtG9TEUK4gGQHULbBeFWz9q9HFA9YSelXDPSJ2y3P42cQOM2XV\nXyNWj96GuKlVLdrSCI4cCDqW52Sw4xp0GjrV6Hv9zEQ3W1DyoHDubIYxfn3T\nZ5gD\r\n=1NXg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "declarations/index.d.ts", "engines": {"node": ">= 8.9.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}, "gitHead": "91003752ae6c7f0c38036cb3c4532d88015ce554", "scripts": {"lint": "npm-run-all -l -p \"lint:**\"", "test": "npm run test:coverage", "build": "npm-run-all -p \"build:**\"", "clean": "del-cli dist declarations", "start": "npm run build -- -w", "lint:js": "eslint --cache .", "prepare": "npm run build", "pretest": "npm run lint", "release": "standard-version", "defaults": "webpack-defaults", "prebuild": "npm run clean", "security": "npm audit", "test:only": "cross-env NODE_ENV=test jest", "build:code": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "lint:types": "tsc --pretty --noEmit", "test:watch": "npm run test:only -- --watch", "build:types": "tsc --declaration --emitDeclarationOnly --outDir declarations && prettier \"declarations/**/*.ts\" --write", "lint:prettier": "prettier \"{**/*,*}.{js,json,md,yml,css,ts}\" --list-different", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/schema-utils.git", "type": "git"}, "_npmVersion": "6.14.2", "description": "webpack Validation Utils", "directories": {}, "_nodeVersion": "10.15.2", "dependencies": {"ajv": "^6.12.0", "ajv-keywords": "^3.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^5.1.0", "jest": "^25.1.0", "husky": "^4.2.3", "eslint": "^6.8.0", "del-cli": "^3.0.0", "prettier": "^1.19.1", "cross-env": "^6.0.3", "@babel/cli": "^7.8.4", "babel-jest": "^25.1.0", "jest-junit": "^10.0.0", "typescript": "^3.8.3", "@babel/core": "^7.8.7", "lint-staged": "^10.0.8", "npm-run-all": "^4.1.5", "@commitlint/cli": "^8.3.5", "standard-version": "^7.1.0", "@babel/preset-env": "^7.8.7", "@types/json-schema": "^7.0.4", "eslint-plugin-import": "^2.20.1", "eslint-config-prettier": "^6.10.0", "@webpack-contrib/defaults": "^6.3.0", "commitlint-azure-pipelines-cli": "^1.0.3", "@commitlint/config-conventional": "^8.3.4", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/schema-utils_2.6.5_1583928326140_0.5492828509958352", "host": "s3://npm-registry-packages"}}, "2.6.6": {"name": "schema-utils", "version": "2.6.6", "keywords": ["webpack"], "author": {"url": "https://github.com/webpack-contrib", "name": "webpack Contrib"}, "license": "MIT", "_id": "schema-utils@2.6.6", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "d3viant0ne", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/schema-utils", "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "dist": {"shasum": "299fe6bd4a3365dc23d99fd446caff8f1d6c330c", "tarball": "https://registry.npmjs.org/schema-utils/-/schema-utils-2.6.6.tgz", "fileCount": 14, "integrity": "sha512-wHutF/WPSbIi9x6ctjGGk2Hvl0VOz5l3EKEuKbjPlB30mKZUzb9A5k9yEXRX3pwyqVLPvpfZZEllaFq/M718hA==", "signatures": [{"sig": "MEYCIQCWgdqrrGMPGMIHK+Fis5wzN3kTvrF6R8l2eKyutyQw9QIhAOhGhERxchvWwEYDWDpGfWwkzaM0YcsWDe7XRjQp1yxD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73817, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJemc4yCRA9TVsSAnZWagAAOWcP/RXM5we1QK1Q2aNQUEyr\ngKVM4Y8hMApj2dRcGnuEXVdHpS6XJDa+xq3iKfhiXx4FMU/kBvasorBsBDkX\nNvBpq/2ycCRdJ46Wm9um7CduPLDK6YMZF9jtt3UpbbxkFyz8FHPu0Ig3g4ph\nDMXXxd2nPor7KCEWxcziiB2QwcqOxFGGs1QECbNtT8CpNUMaSK/SXebjmlPD\nk+ql/aAomPkAfUIT/szIBpb48RRtrDuZVjBs1LV5CB8c4Eh9gj158d1EB1L+\nPm+e7NZypqaaG1K634zdxnYg3gZtPcsh9cV7q8scc5mR0kAKkLRGjT+Gf4JN\n8666lNk0A96zFynY9vEbmzXuhRw1eX84eNucLeFcnWhdXu1zEvP3fU6lBOAB\nSSNFGZuVI0t3pONDe3bb/MkA6aHT4ib7e3BweJxTGHDVBxlS5Ka6gHevlWhF\n8sMT3jWR2q83s8p+ZoIQwKbqeLvSP2cgGU1uVK9stWvYAASUBRJ4YTItb+rd\nRmLtqmAwKAD8MeaYBNt6pexv01L5asyrqPlZ4wru7Re/sZg3im6GuQSnrJOH\nNPuzka+1jmOm2LyNrAWxBNjnbSc0rCsSqOR3wyDs5vybuV+2nzL2wF6ka93O\nhcKD8htbk1hI6JCuAY9q1XrArLIxxHT+Kxe2WKlrQdomjH+OiTopoZlJvBlK\nSypG\r\n=7OLx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "declarations/index.d.ts", "engines": {"node": ">= 8.9.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}, "gitHead": "d4bfe210d6087a51716cdb086aa67a9f3292e984", "scripts": {"lint": "npm-run-all -l -p \"lint:**\"", "test": "npm run test:coverage", "build": "npm-run-all -p \"build:**\"", "clean": "del-cli dist declarations", "start": "npm run build -- -w", "lint:js": "eslint --cache .", "prepare": "npm run build", "pretest": "npm run lint", "release": "standard-version", "defaults": "webpack-defaults", "prebuild": "npm run clean", "security": "npm audit", "test:only": "cross-env NODE_ENV=test jest", "build:code": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "lint:types": "tsc --pretty --noEmit", "test:watch": "npm run test:only -- --watch", "build:types": "tsc --declaration --emitDeclarationOnly --outDir declarations && prettier \"declarations/**/*.ts\" --write", "lint:prettier": "prettier \"{**/*,*}.{js,json,md,yml,css,ts}\" --list-different", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/schema-utils.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "webpack Validation Utils", "directories": {}, "_nodeVersion": "10.15.2", "dependencies": {"ajv": "^6.12.0", "ajv-keywords": "^3.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^5.1.0", "jest": "^25.3.0", "husky": "^4.2.5", "eslint": "^6.8.0", "del-cli": "^3.0.0", "prettier": "^1.19.1", "cross-env": "^6.0.3", "@babel/cli": "^7.8.4", "babel-jest": "^25.3.0", "typescript": "^3.8.3", "@babel/core": "^7.9.0", "lint-staged": "^10.0.8", "npm-run-all": "^4.1.5", "@commitlint/cli": "^8.3.5", "standard-version": "^7.1.0", "@babel/preset-env": "^7.9.5", "@types/json-schema": "^7.0.4", "eslint-plugin-import": "^2.20.1", "eslint-config-prettier": "^6.10.0", "@webpack-contrib/defaults": "^6.3.0", "@commitlint/config-conventional": "^8.3.4", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/schema-utils_2.6.6_1587138097694_0.21095751536081075", "host": "s3://npm-registry-packages"}}, "2.7.0": {"name": "schema-utils", "version": "2.7.0", "keywords": ["webpack"], "author": {"url": "https://github.com/webpack-contrib", "name": "webpack Contrib"}, "license": "MIT", "_id": "schema-utils@2.7.0", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "d3viant0ne", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/schema-utils", "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "dist": {"shasum": "17151f76d8eae67fbbf77960c33c676ad9f4efc7", "tarball": "https://registry.npmjs.org/schema-utils/-/schema-utils-2.7.0.tgz", "fileCount": 16, "integrity": "sha512-0ilKFI6QQF5nxDZLFn2dMjvc4hjg/Wkg7rHd3jK6/A4a1Hl9VFdQWvgB1UMGoU94pad1P/8N7fMcEnLnSiju8A==", "signatures": [{"sig": "MEUCIDPM6c8eA0KcXdOV4f2huC0uJSPcC0BzgGCdX+eBVFZsAiEAqauJ32U7yKIlN3Yy3Sg5yjNjBN5if0MDmCkjtOJT4og=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76993, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe0Re5CRA9TVsSAnZWagAAT7AP/39FKmJb7uCEAxAnUxNT\nMcHRMCxmwfBZAHAO1BhfkwVukMsv1Q0CIlk+mAbiYRJ+vTAEPbYY+kDA9tWp\nY754zZSdx/DLferiUrpGRm5oWv+UI9ks1V9Na197FjUkHkNohXYjuKAVP7Aq\ncCIREAi8kbpBE3bmTb5j0TnhRWDFtwfTmNPV4H0NVN2IVB0ND7Da3YJP+L0h\nLqdQ3c3yLS5jCRm8QpV6WUkBKZOGMlgkCC6J/93Y7RW9DFFlkmbH78/luqoh\nB8ECiHYHaLTASugYaBR3cyTZUMAqVRBSCbddy7moTDnrPaxLDAriwFzLrUlY\nVJlv3/5T0CvPUWkpcK3ptSygqmWz6WEVtGuaOwMq+RkcFScBNLQ/JxsX77FW\nkhWWoTwjs9qQ4Zj69vrwnpYkdMhG4LsiozuPBO1KtAD3cqD6rJUs1Hk0JM30\nCEVQw45zoA76tNrs1a+NEjHpebFjdUAvwWPC7IWyo8GovAzP6kadXlyn1cZz\nF+C0RK8W0WThZrG1xlZFylF2xlQq3qptqsImVRz0N6ukmlkQiSY+JyxeJIfB\nnJ83gecGQOxQ4T/YJsXz2R+7OUa8a7ObsO1vWsCTJ9d65wYgMyFFSBo6Nbf7\ntmle7MPAmjY0QYiHadKYZAxRRA8dGAHF7aQoGi0UJvz1wLKUJPTzEdEzbXcB\nH+ma\r\n=dDgv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "declarations/index.d.ts", "engines": {"node": ">= 8.9.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}, "gitHead": "132fee2af33a2087568bd7482687576fbd121905", "scripts": {"lint": "npm-run-all -l -p \"lint:**\"", "test": "npm run test:coverage", "build": "npm-run-all -p \"build:**\"", "clean": "del-cli dist declarations", "start": "npm run build -- -w", "lint:js": "eslint --cache .", "prepare": "npm run build", "pretest": "npm run lint", "release": "standard-version", "defaults": "webpack-defaults", "prebuild": "npm run clean", "security": "npm audit", "test:only": "cross-env NODE_ENV=test jest", "build:code": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "lint:types": "tsc --pretty --noEmit", "test:watch": "npm run test:only -- --watch", "build:types": "tsc --declaration --emitDeclarationOnly --outDir declarations && prettier \"declarations/**/*.ts\" --write", "lint:prettier": "prettier \"{**/*,*}.{js,json,md,yml,css,ts}\" --list-different", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/schema-utils.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "webpack Validation Utils", "directories": {}, "_nodeVersion": "10.15.2", "dependencies": {"ajv": "^6.12.2", "ajv-keywords": "^3.4.1", "@types/json-schema": "^7.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^5.1.0", "jest": "^25.5.4", "husky": "^4.2.5", "eslint": "^6.8.0", "del-cli": "^3.0.1", "prettier": "^1.19.1", "cross-env": "^6.0.3", "@babel/cli": "^7.10.1", "babel-jest": "^25.5.1", "typescript": "^3.9.3", "@babel/core": "^7.10.1", "lint-staged": "^10.2.7", "npm-run-all": "^4.1.5", "@commitlint/cli": "^8.3.5", "standard-version": "^8.0.0", "@babel/preset-env": "^7.10.1", "eslint-plugin-import": "^2.20.2", "eslint-config-prettier": "^6.11.0", "@webpack-contrib/defaults": "^6.3.0", "@commitlint/config-conventional": "^8.3.4", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/schema-utils_2.7.0_1590761400741_0.32278211142016877", "host": "s3://npm-registry-packages"}}, "2.7.1": {"name": "schema-utils", "version": "2.7.1", "keywords": ["webpack"], "author": {"url": "https://github.com/webpack-contrib", "name": "webpack Contrib"}, "license": "MIT", "_id": "schema-utils@2.7.1", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "d3viant0ne", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/schema-utils", "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "dist": {"shasum": "1ca4f32d1b24c590c203b8e7a50bf0ea4cd394d7", "tarball": "https://registry.npmjs.org/schema-utils/-/schema-utils-2.7.1.tgz", "fileCount": 16, "integrity": "sha512-SHiNtMOUGWBQJwzISiVYKu82GiV4QYGePp3odlY1tuKO7gPtphAT5R/py0fA6xtbgLL/RvtJZnU9b8s0F1q0Xg==", "signatures": [{"sig": "MEUCIQDw3ZlPbxlJCn78xF1C4Nv7l7ciszxm5GxUSt4SYetX9AIgDM5F8ScV3Url2GrKNBsbCDzPo8AulIWrKcKPUpz42ks=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77426, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfTNhOCRA9TVsSAnZWagAAXCQP/i/a65Ye+7p9Rt5B4Php\nvretgulRBWeylzj4Bpk1znyY56pb/j1A+VcBPv+aEUMfsDikqZ+/iHI8+fPs\nWjeKtaBOAgXFwe9NhtNsq6vk3BOgmq6Lp7duoqrvEtCaczR2Bqq8gTs42nRM\nV+Xx4zweiCOxUjUZRdeUny2ZWKf3nlr10FH0khWruoAHKIxgtNW5LJ07CQLg\neYZx+/TjIeMY+fP52zUgMvZsCzrM9AMxpZOhyV0Ln6HQuvmI9O9Rv0q5RY15\nt9dHU46LOxyXQ8uZvH1U/et7pgxi6LIDRLuu9g9C+Z/73KzOHYR7AtuqSk3B\nXP0v8AliOD6cU+DCwcszFgI9/GwAO3hEjONupT4ajBBZ+Z5IKvG1Ddecdin9\n0oQN2sjPHF5dS0NlptpkOyw4qZyu66MHCaF2FGVCTHu82oifms6UVB5iSPBQ\nJM/xTVI22BE6cJUVziSiJJc14NoFoXD/ipfmMMGDZAXR/3uQSzo/3i5dKVF5\n4930ySIoU1BctpwqvZpGbJp541sIcASws29nDNTKILg+mddLXRyo5mPAThNo\ny1nQxQd2PZCH7H5NYgGApfq5WYBCeWTcVJZ3eZluIVTThKsCNKXDSUIzvOnk\nIWwuqRRQiYWCEGm0zZmPG4/GhYqTFVI4hemBHiN+xMM2cZUIEwsUbFYSmH/7\nzVXx\r\n=YPzc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "declarations/index.d.ts", "engines": {"node": ">= 8.9.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}, "gitHead": "102d170506640346b8269d7e38a042b6b5b3a444", "scripts": {"lint": "npm-run-all -l -p \"lint:**\"", "test": "npm run test:coverage", "build": "npm-run-all -p \"build:**\"", "clean": "del-cli dist declarations", "start": "npm run build -- -w", "lint:js": "eslint --cache .", "prepare": "npm run build", "pretest": "npm run lint", "release": "standard-version", "defaults": "webpack-defaults", "prebuild": "npm run clean", "security": "npm audit", "test:only": "cross-env NODE_ENV=test jest", "build:code": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "lint:types": "tsc --pretty --noEmit", "test:watch": "npm run test:only -- --watch", "build:types": "tsc --declaration --emitDeclarationOnly --outDir declarations && prettier \"declarations/**/*.ts\" --write", "lint:prettier": "prettier \"{**/*,*}.{js,json,md,yml,css,ts}\" --list-different", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/schema-utils.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "webpack Validation Utils", "directories": {}, "_nodeVersion": "10.15.2", "dependencies": {"ajv": "^6.12.4", "ajv-keywords": "^3.5.2", "@types/json-schema": "^7.0.5"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^5.1.0", "jest": "^25.5.4", "husky": "^4.2.5", "eslint": "^7.7.0", "del-cli": "^3.0.1", "prettier": "^1.19.1", "cross-env": "^6.0.3", "@babel/cli": "^7.10.5", "babel-jest": "^25.5.1", "typescript": "^4.0.2", "@babel/core": "^7.11.4", "lint-staged": "^10.2.13", "npm-run-all": "^4.1.5", "@commitlint/cli": "^10.0.0", "standard-version": "^9.0.0", "@babel/preset-env": "^7.11.0", "eslint-plugin-import": "^2.22.0", "eslint-config-prettier": "^6.11.0", "@webpack-contrib/defaults": "^6.3.0", "@commitlint/config-conventional": "^10.0.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/schema-utils_2.7.1_1598871630144_0.02020404742410964", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "schema-utils", "version": "3.0.0", "keywords": ["webpack"], "author": {"url": "https://github.com/webpack-contrib", "name": "webpack Contrib"}, "license": "MIT", "_id": "schema-utils@3.0.0", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "d3viant0ne", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/schema-utils", "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "dist": {"shasum": "67502f6aa2b66a2d4032b4279a2944978a0913ef", "tarball": "https://registry.npmjs.org/schema-utils/-/schema-utils-3.0.0.tgz", "fileCount": 16, "integrity": "sha512-6D82/xSzO094ajanoOSbe4YvXWMfn2A//8Y1+MUqFAJul5Bs+yn36xbK9OtNDcRVSBJ9jjeoXftM6CfztsjOAA==", "signatures": [{"sig": "MEYCIQCwigP59RI6h8Q6wXbPap1yr/BAkQY5ZBasNoDDrOsj1wIhAMR8EIyuDOUe+Y0LEPThxfhdMUw0vE/XenHDmkIk48Hk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78169, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfe2QxCRA9TVsSAnZWagAAJtQP/i89W9Ag+d3zeqkTFIX6\n+UReX049e+WHsqkVXpREA+Mg5ldKW+GxybrHrogapm7fKAqLDj2lr2fysCAA\nGw/kiUCUTlKaAkj2HMZabnzdlD3DsXVQ+nDm4fp9EFv70GhoK+DBnDLCiMaC\nYmRFXsTa1dZ7wF/TzXyfUSmxKDyZWPpOTcaFntMWSliBUYy7ElbUFAGkfr5P\n+mdefkdtQ3esi+seLgkOuhwCTtm9a+zQJgVrC4SXa6kdyqnspJVaouL91HWD\n1igQynvy7k4EThNDoyMNhhkSOSdCM31e7RrUvNcKRZvCzWn+4hSJIS8cVi4b\n4tOtXnrfweSZz4YiTuFr70CJ6SGTN5olGjpnCiyHzoGC+f9Q3yQH49YhhPma\nZkyg9XgpnMLunYOLofIGo/RUJrGrAiXAtPH6S4PSm+2WNkiBlSmIUhLk+iDF\n66/L7N30Bes26AaN+VOlmUspvZqC4umHc1OOfWpROKRgWYF0YGgCDCnBKusf\n5zkCt681tYYQySsjyowG4aH7+7wKPrjEPu09LPSNG8tdtFUevYS1jKvz9ga2\nPYtWG3aMJSZ/BnwWxqmLffUN3yjOqZceGgWsY7G3HQ9mi80tUa/HezUiYppp\n0I7kDfBm6c88N+xEcWqx27iQsLt5RjoMo+fDH4XkisoqLjGt7EtrQkjnn7ym\nPhwZ\r\n=ywdL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "declarations/index.d.ts", "engines": {"node": ">= 10.13.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}, "gitHead": "cdbf8b714a90e4a6919efff10d7633e413bca758", "scripts": {"lint": "npm-run-all -l -p \"lint:**\"", "test": "npm run test:coverage", "build": "npm-run-all -p \"build:**\"", "clean": "del-cli dist declarations", "start": "npm run build -- -w", "lint:js": "eslint --cache .", "prepare": "npm run build", "pretest": "npm run lint", "release": "standard-version", "defaults": "webpack-defaults", "prebuild": "npm run clean", "security": "npm audit", "test:only": "cross-env NODE_ENV=test jest", "build:code": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "lint:types": "tsc --pretty --noEmit", "test:watch": "npm run test:only -- --watch", "build:types": "tsc --declaration --emitDeclarationOnly --outDir declarations && prettier \"declarations/**/*.ts\" --write", "lint:prettier": "prettier \"{**/*,*}.{js,json,md,yml,css,ts}\" --list-different", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/schema-utils.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "webpack Validation Utils", "directories": {}, "_nodeVersion": "10.15.2", "dependencies": {"ajv": "^6.12.5", "ajv-keywords": "^3.5.2", "@types/json-schema": "^7.0.6"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "jest": "^26.5.0", "husky": "^4.3.0", "eslint": "^7.10.0", "del-cli": "^3.0.1", "prettier": "^2.1.2", "cross-env": "^7.0.2", "@babel/cli": "^7.11.6", "babel-jest": "^26.5.0", "typescript": "^4.0.3", "@babel/core": "^7.11.6", "lint-staged": "^10.4.0", "npm-run-all": "^4.1.5", "@commitlint/cli": "^11.0.0", "standard-version": "^9.0.0", "@babel/preset-env": "^7.11.5", "eslint-plugin-import": "^2.22.1", "eslint-config-prettier": "^6.12.0", "@webpack-contrib/defaults": "^6.3.0", "@commitlint/config-conventional": "^11.0.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/schema-utils_3.0.0_1601922097119_0.04618294561022296", "host": "s3://npm-registry-packages"}}, "3.1.0": {"name": "schema-utils", "version": "3.1.0", "keywords": ["webpack"], "author": {"url": "https://github.com/webpack-contrib", "name": "webpack Contrib"}, "license": "MIT", "_id": "schema-utils@3.1.0", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "d3viant0ne", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/schema-utils", "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "dist": {"shasum": "95986eb604f66daadeed56e379bfe7a7f963cdb9", "tarball": "https://registry.npmjs.org/schema-utils/-/schema-utils-3.1.0.tgz", "fileCount": 15, "integrity": "sha512-tTEaeYkyIhEZ9uWgAjDerWov3T9MgX8dhhy2r0IGeeX4W8ngtGl1++dUve/RUqzuaASSh7shwCDJjEzthxki8w==", "signatures": [{"sig": "MEYCIQC0DfF9lEu6GCWqfy4YmBM1/EdA12CUXxUbG2fKwAh7DAIhAO+UmHiI4Wau/qS7nJvFleEN9O48eQzqlPIXL2//vW5o", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66174, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg4u4TCRA9TVsSAnZWagAAyjwP/R1cP0GMLtU9isqgN3vy\nMfG1tyWqZtj2v2pAdGXwNTIhI4krSEzogupxa7IiR46tZGw5JJ1WWrfPlE0o\n2nB+exFZGjQUpxI6l+9ErSKHSwsg7JpJ+t37Jg7BwqUR7s99WYKkjcUk6Zcn\nrc3EGvNbilQpDj/bEYeEWCUuIMJL63tryeFL7tEPTGj2vnA4fr0xTcds8bJt\nUwYJrKs0gU3sfDcmpbSKLbzrHdtTvvzGVveiJr99aZrzwlRmXeILIsrakOsL\nYQY8p1FFGHKseLZfWgOqSRCRq9zomKyu+teVhWLLOyBXHJAm/om2Mi+9C1DK\n3E0GBUowFY4s57ofGq80+3m+wNxd2ABfvciqbpF0aOx1aCnD7iTeAjGLSRHI\nCopnPr/HMHcHCKrDj/iFBbqqcTQtdyz0j2HeCn3G1oNutIV1t/14ocOC+ETu\nGIgrmVLW64FHHZEyhxe5Daf7m/iLnNdvx0oN2V7ZD5Sqhow6bJs6WHAR0dYy\nPv3ou5Y19x2NkPtcmppn2PJ6eVSt/WzQwdhaI4h/xsJwpRUrJHjHIAuyum18\nDm5wPcHsgNWJH1lNDTB8sIoZuZgTzQuy4NU410N5/PtLO0YGh6L6DoBsN91E\ncosCwCQ1qyplFFZVyNt1XpsHzWVcY+C4hZaLxKPglwRnPserG5W+EMym8w37\n5T+O\r\n=kw22\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "declarations/index.d.ts", "engines": {"node": ">= 10.13.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}, "gitHead": "3915bae9a2ce3206dff5f7f3dff717af2ae1d527", "scripts": {"fix": "npm-run-all fix:js fmt", "fmt": "npm run fmt:check -- --write", "lint": "npm-run-all lint:js lint:types fmt:check", "test": "npm run test:coverage", "build": "npm-run-all -p \"build:**\"", "clean": "del-cli dist declarations", "start": "npm run build -- -w", "fix:js": "npm run lint:js -- --fix", "lint:js": "eslint --cache .", "prepare": "npm run build && husky install", "pretest": "npm run lint", "release": "standard-version", "prebuild": "npm run clean", "security": "npm audit --production", "fmt:check": "prettier \"{**/*,*}.{js,json,md,yml,css,ts}\" --list-different", "test:only": "cross-env NODE_ENV=test jest", "build:code": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "lint:types": "tsc --pretty --noEmit", "test:watch": "npm run test:only -- --watch", "build:types": "tsc --declaration --emitDeclarationOnly --outDir declarations && prettier \"declarations/**/*.ts\" --write", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/schema-utils.git", "type": "git"}, "_npmVersion": "7.16.0", "description": "webpack Validation Utils", "directories": {}, "_nodeVersion": "12.22.1", "dependencies": {"ajv": "^6.12.5", "ajv-keywords": "^3.5.2", "@types/json-schema": "^7.0.7"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "jest": "^27.0.6", "husky": "^6.0.0", "eslint": "^7.29.0", "del-cli": "^3.0.1", "webpack": "^5.41.1", "prettier": "^2.3.2", "cross-env": "^7.0.3", "@babel/cli": "^7.14.3", "babel-jest": "^27.0.6", "typescript": "^4.3.4", "@babel/core": "^7.14.6", "lint-staged": "^11.0.0", "npm-run-all": "^4.1.5", "@commitlint/cli": "^12.1.4", "standard-version": "^9.3.0", "@babel/preset-env": "^7.14.7", "eslint-plugin-import": "^2.23.4", "eslint-config-prettier": "^8.3.0", "@commitlint/config-conventional": "^12.1.4", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/schema-utils_3.1.0_1625484818786_0.06525563801121104", "host": "s3://npm-registry-packages"}}, "3.1.1": {"name": "schema-utils", "version": "3.1.1", "keywords": ["webpack"], "author": {"url": "https://github.com/webpack-contrib", "name": "webpack Contrib"}, "license": "MIT", "_id": "schema-utils@3.1.1", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "d3viant0ne", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/schema-utils", "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "dist": {"shasum": "bc74c4b6b6995c1d88f76a8b77bea7219e0c8281", "tarball": "https://registry.npmjs.org/schema-utils/-/schema-utils-3.1.1.tgz", "fileCount": 15, "integrity": "sha512-Y5PQxS4ITlC+EahLuXaY86TXfR7Dc5lw294alXOq86JAHCihAIZfqv8nNCWvaEJvaC51uN9hbLGeV0cFBdH+Fw==", "signatures": [{"sig": "MEYCIQD22st4TROcG7SMtlNaujBr6OKkF+ljKcFeTmCpYo994QIhAM2s6M8GmixI232bZzMAhpQFkuhIfyz4mrHxEqGgobox", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66175, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg9WRACRA9TVsSAnZWagAAFQ4P/3lbtRaB7G3I5i4KKs13\nRRgnpKzdNcJH2pmkhS3xyHsFttW6P/j8xubfYodlogIa4Na3mlUOmQID9SiX\nZkiXxJbBWh3yif2Eth8qkInCxMhC66dhKRCsLAfqh+9SsqFQyGnQU4yJoavS\nHZ5wtHPHUTSjYnIuQLbrGoWMAXYniqHrMvN/KSPVNu8lqhdBsT8H65hXKkj2\nmMoP2AwYCxIr0T4f7uQ5nIO0URPzmktWp/Wz0wzd3OOtWMhVfijkWkFe3wgA\n5WuD5wBURd/Jf03BlxK+wFiSo1o164HEL+An4ws+qSnrbS3Fyyvm3BQXva0T\nX68JULY4N0XS0IkpfMZ5mahqJVPG0sHo2s8R2gtJHiFNFJl3PwRLTvkFb1A2\n3V7bAR/we/CvJLfrn2liZkoRTkERQzYy7y+/WbWe1CjR1Nx+aGxoIvuYmcc7\nxqBS2oJgeo4kZjnZ1vyoRcis3ZzBisFzdI7woHHlP1DkwIQ9FDao0MJE8rt6\ntTktI+PdmvGRm/gc6rcx24ErOFBezU0vsJATRT48jBSmnmOXRFdnKZt9mOre\nXipNIaJ5ukcXraZJB2t3pIcqc0b37ltDd64Rw7abAkre9iRuyYFSCQKafkqa\nvQtXnLAVGo5ia0vpmdLHJuZRJKHZbQP7UTxFaPrrkoWcOdciRJ45jg3sgdkn\ns8/2\r\n=+mX0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "declarations/index.d.ts", "engines": {"node": ">= 10.13.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}, "gitHead": "faadaf950afd79352d7f9d0c3e0601b7ee45c5f2", "scripts": {"fix": "npm-run-all fix:js fmt", "fmt": "npm run fmt:check -- --write", "lint": "npm-run-all lint:js lint:types fmt:check", "test": "npm run test:coverage", "build": "npm-run-all -p \"build:**\"", "clean": "del-cli dist declarations", "start": "npm run build -- -w", "fix:js": "npm run lint:js -- --fix", "lint:js": "eslint --cache .", "prepare": "npm run build && husky install", "pretest": "npm run lint", "release": "standard-version", "prebuild": "npm run clean", "security": "npm audit --production", "fmt:check": "prettier \"{**/*,*}.{js,json,md,yml,css,ts}\" --list-different", "test:only": "cross-env NODE_ENV=test jest", "build:code": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "lint:types": "tsc --pretty --noEmit", "test:watch": "npm run test:only -- --watch", "build:types": "tsc --declaration --emitDeclarationOnly --outDir declarations && prettier \"declarations/**/*.ts\" --write", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/schema-utils.git", "type": "git"}, "_npmVersion": "7.16.0", "description": "webpack Validation Utils", "directories": {}, "_nodeVersion": "12.22.1", "dependencies": {"ajv": "^6.12.5", "ajv-keywords": "^3.5.2", "@types/json-schema": "^7.0.8"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "jest": "^27.0.6", "husky": "^6.0.0", "eslint": "^7.31.0", "del-cli": "^3.0.1", "webpack": "^5.45.1", "prettier": "^2.3.2", "cross-env": "^7.0.3", "@babel/cli": "^7.14.3", "babel-jest": "^27.0.6", "typescript": "^4.3.5", "@babel/core": "^7.14.6", "lint-staged": "^11.0.1", "npm-run-all": "^4.1.5", "@commitlint/cli": "^12.1.4", "standard-version": "^9.3.1", "@babel/preset-env": "^7.14.7", "eslint-plugin-import": "^2.23.4", "eslint-config-prettier": "^8.3.0", "@commitlint/config-conventional": "^12.1.4", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/schema-utils_3.1.1_1626694720644_0.4370288263243398", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "schema-utils", "version": "4.0.0", "keywords": ["webpack"], "author": {"url": "https://github.com/webpack-contrib", "name": "webpack Contrib"}, "license": "MIT", "_id": "schema-utils@4.0.0", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/schema-utils", "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "dist": {"shasum": "60331e9e3ae78ec5d16353c467c34b3a0a1d3df7", "tarball": "https://registry.npmjs.org/schema-utils/-/schema-utils-4.0.0.tgz", "fileCount": 15, "integrity": "sha512-1edyXKgh6XnJsJSQ8mKWXnN/BVaIbFMLpouRUrXgVq7WYne5kw3MW7UPhO44uRXQSIpTSXoJbmrR2X0w9kUTyg==", "signatures": [{"sig": "MEUCIQDFJndiSlmASezhBi5zFEA/rtBED6DRtD+ruzN6rVxSLAIgcSkWSpvejUGrgB6gQMCBjMFTuBuIB2r65dl3g7l5UmA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66292, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhk8saCRA9TVsSAnZWagAARN8P/AycwzikWZ2sWpocm7hc\nMeHLwhGlX16qUqwImhnrGepd4yfRHVpcRnj49/n9wj2fnMhyo/pLaLGXXfpK\npuIgy85tZkttWNJGlKErr3yXGWD6UjU6BcU+VO3kffbbKjWEl5qBcXqz9GsZ\n/ERn7/yBDKTRP/zZ8nt4AWFBJ08BFtuht2xdXeDLlUO9VKxwLpw8p7TbfdH3\nEGWQZumXgZyPgzbsiaFupR5Iu0VT/4ugxA/HTEv9o6nH1cA2yUi9wM4S8NUU\nr6vIQxckIIeBDw0cZ9EUK6ZagVTyfxGDpjdKnEgNW8u4oXgS6I0iSOBehleW\nK60YaWVzux3awmMlsXSZWlaaJRNtGq8LeAtt0K+EaaajfyGW9yxAZ7k0iett\nunL1ggpdSQTYBZhaByxWifeuzDpRyH0L60BgcPMx3jrzwf1thzfzSYzc3Ua8\nhmP4BSQl35Ttu5aa2JSW2kunxwhqzjgHzFVWmkXdb98IlfbLschsm55KrP51\nIfyJmwWZQsXu+BG46CeoTel0DbJuQEPAaqE2V5xF2Nzz6lqaCjJzUsvpr7hx\nsjhgs7Lc6BXMULr4TwCclTSCk6Id/aLnwNlVjka//VOCJQf/mdZrBvyPsDer\nAt75Lxcbir0gTgEUkJfxCa/lIV9FuwIF9Udy74se+pWcCX1pjMm2qdGpKb4a\ngaNi\r\n=7vkI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "declarations/index.d.ts", "engines": {"node": ">= 12.13.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}, "gitHead": "746a1e827653181dd620b09d0d61584ea4038bb7", "scripts": {"fix": "npm-run-all fix:js fmt", "fmt": "npm run fmt:check -- --write", "lint": "npm-run-all lint:js lint:types fmt:check", "test": "npm run test:coverage", "build": "npm-run-all -p \"build:**\"", "clean": "del-cli dist declarations", "start": "npm run build -- -w", "fix:js": "npm run lint:js -- --fix", "lint:js": "eslint --cache .", "prepare": "npm run build && husky install", "pretest": "npm run lint", "release": "standard-version", "prebuild": "npm run clean", "security": "npm audit --production", "fmt:check": "prettier \"{**/*,*}.{js,json,md,yml,css,ts}\" --list-different", "test:only": "cross-env NODE_ENV=test jest", "build:code": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "lint:types": "tsc --pretty --noEmit", "test:watch": "npm run test:only -- --watch", "build:types": "tsc --declaration --emitDeclarationOnly --outDir declarations && prettier \"declarations/**/*.ts\" --write", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/schema-utils.git", "type": "git"}, "_npmVersion": "8.1.2", "description": "webpack Validation Utils", "directories": {}, "_nodeVersion": "12.22.7", "dependencies": {"ajv": "^8.8.0", "ajv-formats": "^2.1.1", "ajv-keywords": "^5.0.0", "@types/json-schema": "^7.0.9"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "jest": "^27.3.1", "husky": "^7.0.4", "eslint": "^8.0.1", "del-cli": "^4.0.1", "webpack": "^5.64.1", "prettier": "^2.4.1", "cross-env": "^7.0.3", "@babel/cli": "^7.16.0", "babel-jest": "^27.3.1", "typescript": "^4.3.5", "@babel/core": "^7.16.0", "lint-staged": "^12.0.2", "npm-run-all": "^4.1.5", "@commitlint/cli": "^14.1.0", "standard-version": "^9.3.2", "@babel/preset-env": "^7.16.0", "eslint-plugin-import": "^2.25.3", "eslint-config-prettier": "^8.3.0", "@commitlint/config-conventional": "^14.1.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/schema-utils_4.0.0_1637075738265_0.43114987792105897", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "schema-utils", "version": "4.0.1", "keywords": ["webpack"], "author": {"url": "https://github.com/webpack-contrib", "name": "webpack Contrib"}, "license": "MIT", "_id": "schema-utils@4.0.1", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/schema-utils", "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "dist": {"shasum": "eb2d042df8b01f4b5c276a2dfd41ba0faab72e8d", "tarball": "https://registry.npmjs.org/schema-utils/-/schema-utils-4.0.1.tgz", "fileCount": 15, "integrity": "sha512-lELhBAAly9NowEsX0yZBlw9ahZG+sK/1RJ21EpzdYHKEs13Vku3LJ+MIPhh4sMs0oCCeufZQEQbMekiA4vuVIQ==", "signatures": [{"sig": "MEUCIQCebib9i2NX3pRNkHarEweuIu4bRoMTz4KvX+9rCXs3zwIgPDuBo+5qSCncU5PnXVnh2gwgN0Eizib3xY7XNmOjhMw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67278, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkOrrSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoHRg/9GTiMVvg0hRG1yzgvURQDhUeTNGCMDn761tH76vRThhjug6CH\r\nJSHDtLfFyt1yjAx13MoZ80pRdfwyt5nKgUhBnFVP572HsQisCVJQY/K28Hyg\r\nMwYVcBr8y8Psg/uwc3fk4VbLm1c3Iv21jC1vlE58GnYI67c5e9kPml6oer+6\r\nRob/anlMntBRm9x6ibRXBCP9AT54QQ7ost3voEsJnrLITauU61neox1S26c7\r\nJzO3OAqSNon28drzlLLluzZ8RHz6sP4X5HvFMEpGnFD/54zlXJFlyZ7jSvIf\r\n+H8TAEkRSrEgUU8j7wQBA2EZ+Ui+FswwZH5uvjijuXW7VI6OLmG89cKdUzDV\r\ne15lP9JmUhgfNRYMJAOqAhxgz+swDKxj3OgUPnWWlOq+pKUsIv2ONCz55AUk\r\nhjcpgAh+bzrhxSO0VIv+xqJ6gh0ogM/wkVVvH3OGcHPLGfJdsdN+t+87K1Xy\r\n5jz1i99JEtWlg/KhKZu2tZCrqvlOAgvRaV8ttnafiSsJDX6RxCLL2j7Agvps\r\nZCi8NnMDYdMcZZTyeGHBfKDBB9uij82Eq4FznLfaygMmUyTu6Le3DQ9HUfJ+\r\naFTmFQSMSr8Dry6ghjTylZ3SjwQxTIIuE0N1OAIel1jr/4SEBb0ie/QmlNSk\r\nHGneDZzfKjl5F4vPXRXDi3YK2hij35kA8No=\r\n=AjRJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "declarations/index.d.ts", "engines": {"node": ">= 12.13.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}, "gitHead": "490d60a198ce595f7846fe87c3beec399d0aae5b", "scripts": {"fix": "npm-run-all fix:js fmt", "fmt": "npm run fmt:check -- --write", "lint": "npm-run-all lint:js lint:types fmt:check", "test": "npm run test:coverage", "build": "npm-run-all -p \"build:**\"", "clean": "del-cli dist declarations", "start": "npm run build -- -w", "fix:js": "npm run lint:js -- --fix", "lint:js": "eslint --cache .", "prepare": "npm run build && husky install", "pretest": "npm run lint", "release": "standard-version", "prebuild": "npm run clean", "security": "npm audit --production", "fmt:check": "prettier \"{**/*,*}.{js,json,md,yml,css,ts}\" --list-different", "test:only": "cross-env NODE_ENV=test jest", "build:code": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "lint:types": "tsc --pretty --noEmit", "test:watch": "npm run test:only -- --watch", "build:types": "tsc --declaration --emitDeclarationOnly --outDir declarations && prettier \"declarations/**/*.ts\" --write", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/schema-utils.git", "type": "git"}, "_npmVersion": "8.5.5", "description": "webpack Validation Utils", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"ajv": "^8.9.0", "ajv-formats": "^2.1.1", "ajv-keywords": "^5.1.0", "@types/json-schema": "^7.0.9"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "jest": "^27.4.7", "husky": "^7.0.4", "eslint": "^8.8.0", "del-cli": "^4.0.1", "webpack": "^5.68.0", "prettier": "^2.5.1", "cross-env": "^7.0.3", "@babel/cli": "^7.17.0", "babel-jest": "^27.4.6", "typescript": "^4.5.5", "@babel/core": "^7.17.0", "lint-staged": "^12.3.3", "npm-run-all": "^4.1.5", "@commitlint/cli": "^16.1.0", "standard-version": "^9.3.2", "@babel/preset-env": "^7.16.11", "eslint-plugin-import": "^2.25.4", "eslint-config-prettier": "^8.3.0", "@commitlint/config-conventional": "^16.0.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/schema-utils_4.0.1_1681570513924_0.7604472921397365", "host": "s3://npm-registry-packages"}}, "3.1.2": {"name": "schema-utils", "version": "3.1.2", "keywords": ["webpack"], "author": {"url": "https://github.com/webpack-contrib", "name": "webpack Contrib"}, "license": "MIT", "_id": "schema-utils@3.1.2", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/schema-utils", "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "dist": {"shasum": "36c10abca6f7577aeae136c804b0c741edeadc99", "tarball": "https://registry.npmjs.org/schema-utils/-/schema-utils-3.1.2.tgz", "fileCount": 16, "integrity": "sha512-pvjEHOgWc9OWA/f/DE3ohBWTD6EleVLf7iFUkoSwAxttdBhB9QUebQgxER2kWueOvRJXPHNnyrvvh9eZINB8Eg==", "signatures": [{"sig": "MEUCIQCrLMMgL/dQkda27Q+OrZk+Qk+8fVUUkOr2FysWTzveGAIgIt5pXD8Ze3GirKeEeOn9KU203KRLxahGs6W4dUWoVKg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 80291, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkOsDzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoXqQ/+NB/Ts53e8lvnpXVs1g1V00Btb4B7DRg0gZM4bylSqdZum5CV\r\nz3eUK189Bcf2KVpjv0QL7t5HGRHH5kvd3vDHPL2tKDE8/2fxLX0sg73lgpjW\r\nLOegni7I4ZBWvU4oLuB9w6hfEx1+XG+Ia4kpC1AS5V7tNoZC7UjiumxuS0lm\r\nE+erDGqm0VHB3xr+GdjYvfuEf7MxOQtpsTQ0/7gg1OXOC5FQLvBfaeBBx4iL\r\nq96E/cIQEKKkBzgzmtdCMaNNP0xWplkZ9amk2y79c0gP8eWrxfgMnnNOLZ6g\r\n7T2FtnHnAaUyY7bTIAdD8VYEy9mhuPH+ccj2/Lezb2zTr6vvDKRI3aKFO9tz\r\nKH0l36RAFI0auJwkjNbA75jTl+hDuHEywwmoYe4dAb9mlYX2mrxVtjoVCq4Z\r\nMLDPDzCTOV+jlrgqVhacBAXj/CwbbLQmrWQJgC8g8n1kGh6fctr947ZXCjYq\r\ny7HRO8EskM/NLMuPqjv1op8K5aYiYq/HCJFOxVp/fHfuejoY2qfRBih8PrOV\r\nMERLhkzomjDj5Bfy/XUgnFLL0GCI1MShzvnkFjLRaBtgBCfnJk9MXWvx3V5r\r\n53nuu7WLoTowe7mP7leVM4SgZwUdjX/BOlLrSXY733ytGJQM37cxTRgRbjkh\r\nJ7zBYHoogLU9WAl8GrjfG7L9t3MAy4mX2UA=\r\n=Ltuv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "declarations/index.d.ts", "engines": {"node": ">= 10.13.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}, "gitHead": "d350b63e328b416f88ba1f31e3d9aff2550deea4", "scripts": {"fix": "npm-run-all fix:js fmt", "fmt": "npm run fmt:check -- --write", "lint": "npm-run-all lint:js lint:types fmt:check", "test": "npm run test:coverage", "build": "npm-run-all -p \"build:**\"", "clean": "del-cli dist declarations", "start": "npm run build -- -w", "fix:js": "npm run lint:js -- --fix", "lint:js": "eslint --cache .", "prepare": "npm run build && husky install", "pretest": "npm run lint", "release": "standard-version", "prebuild": "npm run clean", "security": "npm audit --production", "fmt:check": "prettier \"{**/*,*}.{js,json,md,yml,css,ts}\" --list-different", "test:only": "cross-env NODE_ENV=test jest", "build:code": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "lint:types": "tsc --pretty --noEmit", "test:watch": "npm run test:only -- --watch", "build:types": "tsc --declaration --emitDeclarationOnly --outDir declarations && prettier \"declarations/**/*.ts\" --write", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/schema-utils.git", "type": "git"}, "_npmVersion": "6.14.18", "description": "webpack Validation Utils", "directories": {}, "_nodeVersion": "14.21.3", "dependencies": {"ajv": "^6.12.5", "ajv-keywords": "^3.5.2", "@types/json-schema": "^7.0.8"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"del": "^6.0.0", "jest": "^27.0.6", "husky": "^6.0.0", "eslint": "^7.31.0", "del-cli": "^3.0.1", "webpack": "^5.45.1", "prettier": "^2.3.2", "cross-env": "^7.0.3", "@babel/cli": "^7.14.3", "babel-jest": "^27.0.6", "typescript": "^4.3.5", "@babel/core": "^7.14.6", "lint-staged": "^11.0.1", "npm-run-all": "^4.1.5", "@commitlint/cli": "^12.1.4", "standard-version": "^9.3.1", "@babel/preset-env": "^7.14.7", "eslint-plugin-import": "^2.23.4", "eslint-config-prettier": "^8.3.0", "@commitlint/config-conventional": "^12.1.4", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/schema-utils_3.1.2_1681572083530_0.7132215357860934", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "schema-utils", "version": "4.1.0", "keywords": ["webpack"], "author": {"url": "https://github.com/webpack-contrib", "name": "webpack Contrib"}, "license": "MIT", "_id": "schema-utils@4.1.0", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/schema-utils", "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "dist": {"shasum": "4cff1e434c12ed39502378b9a3e24787b37df41d", "tarball": "https://registry.npmjs.org/schema-utils/-/schema-utils-4.1.0.tgz", "fileCount": 18, "integrity": "sha512-Jw+GZVbP5IggB2WAn6UHI02LBwGmsIeYN/lNbSMZyDziQ7jmtAUrqKqDja+W89YHVs+KL/3IkIMltAklqB1vAw==", "signatures": [{"sig": "MEUCIB0phsc7FOdQFobn89w8SjxbVMZ3H410gveWAYgflfC4AiEAmqRfHZDiu7povSx3XWsryazYnPR+m+xZT0dPnTOZTA8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82717}, "main": "dist/index.js", "types": "declarations/index.d.ts", "engines": {"node": ">= 12.13.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}, "gitHead": "3731a59e36211cae23c2dbf6cf78642d6e8a28f9", "scripts": {"fix": "npm-run-all fix:js fmt", "fmt": "npm run fmt:check -- --write", "lint": "npm-run-all lint:js lint:types fmt:check", "test": "npm run test:coverage", "build": "npm-run-all -p \"build:**\"", "clean": "del-cli dist declarations", "start": "npm run build -- -w", "fix:js": "npm run lint:js -- --fix", "lint:js": "eslint --cache .", "prepare": "npm run build && husky install", "pretest": "npm run lint", "release": "standard-version", "prebuild": "npm run clean", "security": "npm audit --production", "fmt:check": "prettier \"{**/*,*}.{js,json,md,yml,css,ts}\" --list-different", "test:only": "cross-env NODE_ENV=test jest", "build:code": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "lint:types": "tsc --pretty --noEmit", "test:watch": "npm run test:only -- --watch", "build:types": "tsc --declaration --emitDeclarationOnly --outDir declarations && prettier \"declarations/**/*.ts\" --write", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/schema-utils.git", "type": "git"}, "_npmVersion": "6.14.16", "description": "webpack Validation Utils", "directories": {}, "_nodeVersion": "12.22.12", "dependencies": {"ajv": "^8.9.0", "ajv-formats": "^2.1.1", "ajv-keywords": "^5.1.0", "@types/json-schema": "^7.0.9"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "jest": "^27.4.7", "husky": "^7.0.4", "eslint": "^8.8.0", "del-cli": "^4.0.1", "webpack": "^5.68.0", "prettier": "^2.5.1", "cross-env": "^7.0.3", "@babel/cli": "^7.17.0", "babel-jest": "^27.4.6", "typescript": "^4.9.5", "@babel/core": "^7.17.0", "lint-staged": "^13.2.1", "npm-run-all": "^4.1.5", "@commitlint/cli": "^17.6.1", "standard-version": "^9.3.2", "@babel/preset-env": "^7.16.11", "eslint-plugin-import": "^2.25.4", "eslint-config-prettier": "^8.3.0", "@commitlint/config-conventional": "^16.0.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/schema-utils_4.1.0_1686176540865_0.20236625049838053", "host": "s3://npm-registry-packages"}}, "3.2.0": {"name": "schema-utils", "version": "3.2.0", "keywords": ["webpack"], "author": {"url": "https://github.com/webpack-contrib", "name": "webpack Contrib"}, "license": "MIT", "_id": "schema-utils@3.2.0", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/schema-utils", "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "dist": {"shasum": "7dff4881064a4f22c09f0c6a1457feb820fd0636", "tarball": "https://registry.npmjs.org/schema-utils/-/schema-utils-3.2.0.tgz", "fileCount": 18, "integrity": "sha512-0zTyLGyDJYd/MBxG1AhJkKa6fpEBds4OQO2ut0w7OYG+ZGhGea09lijvzsqegYSik88zc7cUtIlnnO+/BvD6gQ==", "signatures": [{"sig": "MEYCIQCUS02poQSFqNk71EF4f2ta/nLWc0JSaUkGuiTFuwtj8QIhANaYg+Ai3Gdgj+X3l5t4hX3V5CZi9+/p8J6yMlCwIvgc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83407}, "main": "dist/index.js", "types": "declarations/index.d.ts", "engines": {"node": ">= 10.13.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}, "gitHead": "dd3bc4ff9b92537308c16ad4e14219b0d8027b18", "scripts": {"fix": "npm-run-all fix:js fmt", "fmt": "npm run fmt:check -- --write", "lint": "npm-run-all lint:js lint:types fmt:check", "test": "npm run test:coverage", "build": "npm-run-all -p \"build:**\"", "clean": "del-cli dist declarations", "start": "npm run build -- -w", "fix:js": "npm run lint:js -- --fix", "lint:js": "eslint --cache .", "prepare": "npm run build && husky install", "pretest": "npm run lint", "release": "standard-version", "prebuild": "npm run clean", "security": "npm audit --production", "fmt:check": "prettier \"{**/*,*}.{js,json,md,yml,css,ts}\" --list-different", "test:only": "cross-env NODE_ENV=test jest", "build:code": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "lint:types": "tsc --pretty --noEmit", "test:watch": "npm run test:only -- --watch", "build:types": "tsc --declaration --emitDeclarationOnly --outDir declarations && prettier \"declarations/**/*.ts\" --write", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/schema-utils.git", "type": "git"}, "_npmVersion": "6.14.16", "description": "webpack Validation Utils", "directories": {}, "_nodeVersion": "12.22.12", "dependencies": {"ajv": "^6.12.5", "ajv-keywords": "^3.5.2", "@types/json-schema": "^7.0.8"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"del": "^6.0.0", "jest": "^27.0.6", "husky": "^6.0.0", "eslint": "^7.31.0", "del-cli": "^3.0.1", "webpack": "^5.45.1", "prettier": "^2.3.2", "cross-env": "^7.0.3", "@babel/cli": "^7.14.3", "babel-jest": "^27.0.6", "typescript": "^4.3.5", "@babel/core": "^7.14.6", "lint-staged": "^11.0.1", "npm-run-all": "^4.1.5", "@commitlint/cli": "^12.1.4", "standard-version": "^9.3.1", "@babel/preset-env": "^7.14.7", "eslint-plugin-import": "^2.23.4", "eslint-config-prettier": "^8.3.0", "@commitlint/config-conventional": "^12.1.4", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/schema-utils_3.2.0_1686176711175_0.5898371053748572", "host": "s3://npm-registry-packages"}}, "4.2.0": {"name": "schema-utils", "version": "4.2.0", "keywords": ["webpack"], "author": {"url": "https://github.com/webpack-contrib", "name": "webpack Contrib"}, "license": "MIT", "_id": "schema-utils@4.2.0", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/schema-utils", "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "dist": {"shasum": "70d7c93e153a273a805801882ebd3bff20d89c8b", "tarball": "https://registry.npmjs.org/schema-utils/-/schema-utils-4.2.0.tgz", "fileCount": 19, "integrity": "sha512-L0jRsrPpjdckP3oPug3/VxNKt2trR8TcabrM6FOAAlvC/9Phcmm+cuAgTlxBqdBR1WJx7Naj9WHw+aOmheSVbw==", "signatures": [{"sig": "MEYCIQC98uU2itlzCCiOAt59PwWUBWeNBOTL8AEFIQBUsU9vwQIhAN2DslC/mDy79rmHL39A14THr52LfnOQ35zXC2nVcl0/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71643}, "main": "dist/index.js", "types": "declarations/index.d.ts", "engines": {"node": ">= 12.13.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}, "gitHead": "dcd6df12c191ac1b6cf5eda39ba5736c35f01ccb", "scripts": {"fix": "npm-run-all fix:js fmt", "fmt": "npm run fmt:check -- --write", "lint": "npm-run-all lint:js lint:types fmt:check", "test": "npm run test:coverage", "build": "npm-run-all -p \"build:**\"", "clean": "del-cli dist declarations", "start": "npm run build -- -w", "fix:js": "npm run lint:js -- --fix", "lint:js": "eslint --cache .", "prepare": "npm run build && husky install", "pretest": "npm run lint", "release": "standard-version", "prebuild": "npm run clean", "security": "npm audit --production", "fmt:check": "prettier \"{**/*,*}.{js,json,md,yml,css,ts}\" --list-different", "test:only": "cross-env NODE_ENV=test jest", "build:code": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "lint:types": "tsc --pretty --noEmit", "test:watch": "npm run test:only -- --watch", "build:types": "tsc --declaration --emitDeclarationOnly --outDir declarations && prettier \"declarations/**/*.ts\" --write", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/schema-utils.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "webpack Validation Utils", "directories": {}, "_nodeVersion": "18.15.0", "dependencies": {"ajv": "^8.9.0", "ajv-formats": "^2.1.1", "ajv-keywords": "^5.1.0", "@types/json-schema": "^7.0.9"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "jest": "^27.4.7", "husky": "^7.0.4", "eslint": "^8.8.0", "del-cli": "^4.0.1", "webpack": "^5.68.0", "prettier": "^2.5.1", "cross-env": "^7.0.3", "@babel/cli": "^7.17.0", "babel-jest": "^27.4.6", "typescript": "^4.9.5", "@babel/core": "^7.17.0", "lint-staged": "^13.2.1", "npm-run-all": "^4.1.5", "@commitlint/cli": "^17.6.1", "standard-version": "^9.3.2", "@babel/preset-env": "^7.16.11", "eslint-plugin-import": "^2.25.4", "eslint-config-prettier": "^8.3.0", "@commitlint/config-conventional": "^16.0.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/schema-utils_4.2.0_1686752716052_0.04255495863153946", "host": "s3://npm-registry-packages"}}, "3.3.0": {"name": "schema-utils", "version": "3.3.0", "keywords": ["webpack"], "author": {"url": "https://github.com/webpack-contrib", "name": "webpack Contrib"}, "license": "MIT", "_id": "schema-utils@3.3.0", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/schema-utils", "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "dist": {"shasum": "f50a88877c3c01652a15b622ae9e9795df7a60fe", "tarball": "https://registry.npmjs.org/schema-utils/-/schema-utils-3.3.0.tgz", "fileCount": 18, "integrity": "sha512-pN/yOAvcC+5rQ5nERGuwrjLlYvLTbCibnZ1I7B1LaiAz9BRBlE9GMgE/eqV30P7aJQUf7Ddimy/RsbYO/GrVGg==", "signatures": [{"sig": "MEUCIAkxtODDDEpirEdsp3gHLnB3SflMwz0bXAGGrEeodXoqAiEA3Xp6jH1p5ErOkSsRNPBeOi47Yoh5JLoz6K34aHDORKQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84831}, "main": "dist/index.js", "types": "declarations/index.d.ts", "engines": {"node": ">= 10.13.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}, "gitHead": "4afb750ccb668397499f4b01f8bd4dcb4cdba803", "scripts": {"fix": "npm-run-all fix:js fmt", "fmt": "npm run fmt:check -- --write", "lint": "npm-run-all lint:js lint:types fmt:check", "test": "npm run test:coverage", "build": "npm-run-all -p \"build:**\"", "clean": "del-cli dist declarations", "start": "npm run build -- -w", "fix:js": "npm run lint:js -- --fix", "lint:js": "eslint --cache .", "prepare": "npm run build && husky install", "pretest": "npm run lint", "release": "standard-version", "prebuild": "npm run clean", "security": "npm audit --production", "fmt:check": "prettier \"{**/*,*}.{js,json,md,yml,css,ts}\" --list-different", "test:only": "cross-env NODE_ENV=test jest", "build:code": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "lint:types": "tsc --pretty --noEmit", "test:watch": "npm run test:only -- --watch", "build:types": "tsc --declaration --emitDeclarationOnly --outDir declarations && prettier \"declarations/**/*.ts\" --write", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/schema-utils.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "webpack Validation Utils", "directories": {}, "_nodeVersion": "10.13.0", "dependencies": {"ajv": "^6.12.5", "ajv-keywords": "^3.5.2", "@types/json-schema": "^7.0.8"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"del": "^6.0.0", "jest": "^27.0.6", "husky": "^6.0.0", "eslint": "^7.31.0", "del-cli": "^3.0.1", "webpack": "^5.45.1", "prettier": "^2.3.2", "cross-env": "^7.0.3", "@babel/cli": "^7.14.3", "babel-jest": "^27.0.6", "typescript": "^4.3.5", "@babel/core": "^7.14.6", "lint-staged": "^11.0.1", "npm-run-all": "^4.1.5", "@commitlint/cli": "^12.1.4", "standard-version": "^9.3.1", "@babel/preset-env": "^7.14.7", "eslint-plugin-import": "^2.23.4", "eslint-config-prettier": "^8.3.0", "@commitlint/config-conventional": "^12.1.4", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/schema-utils_3.3.0_1686754363552_0.9904057589760691", "host": "s3://npm-registry-packages"}}, "4.3.0": {"name": "schema-utils", "version": "4.3.0", "keywords": ["webpack"], "author": {"url": "https://github.com/webpack-contrib", "name": "webpack Contrib"}, "license": "MIT", "_id": "schema-utils@4.3.0", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/schema-utils", "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "dist": {"shasum": "3b669f04f71ff2dfb5aba7ce2d5a9d79b35622c0", "tarball": "https://registry.npmjs.org/schema-utils/-/schema-utils-4.3.0.tgz", "fileCount": 21, "integrity": "sha512-Gf9qqc58SpCA/xdziiHz35F4GNIWYWZrEshUc/G/r5BnLph6xpKuLeoJoQuj5WfBIx/eQLf+hmVPYHaxJu7V2g==", "signatures": [{"sig": "MEYCIQCPp9xlIdSNi418jvR6QS5uBqStr6+PBSj9tIx4KH7e3QIhAISpHDQ1S0a3a+jSkwhRPvDYJvzBS1cuHj6LEW1Vv2Iu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76517}, "main": "dist/index.js", "types": "declarations/index.d.ts", "engines": {"node": ">= 10.13.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}, "gitHead": "2fd7538e54d84ec5c0b01ce5827c00fc8344e5f7", "scripts": {"fix": "npm-run-all fix:js fmt", "fmt": "npm run fmt:check -- --write", "lint": "npm-run-all lint:js lint:types fmt:check", "test": "npm run test:coverage", "build": "npm-run-all -p \"build:**\"", "clean": "del-cli dist declarations", "start": "npm run build -- -w", "fix:js": "npm run lint:js -- --fix", "lint:js": "eslint --cache .", "prepare": "npm run build && husky install", "pretest": "npm run lint", "release": "standard-version", "prebuild": "npm run clean", "security": "npm audit --production", "fmt:check": "prettier \"{**/*,*}.{js,json,md,yml,css,ts}\" --list-different", "test:only": "cross-env NODE_ENV=test jest", "build:code": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "lint:types": "tsc --pretty --noEmit", "test:watch": "npm run test:only -- --watch", "build:types": "tsc --declaration --emitDeclarationOnly --outDir declarations && prettier \"declarations/**/*.ts\" --write", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/schema-utils.git", "type": "git"}, "_npmVersion": "9.6.0", "description": "webpack Validation Utils", "directories": {}, "_nodeVersion": "22.11.0", "dependencies": {"ajv": "^8.9.0", "ajv-formats": "^2.1.1", "ajv-keywords": "^5.1.0", "@types/json-schema": "^7.0.9"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "jest": "^27.4.7", "husky": "^7.0.4", "eslint": "^8.8.0", "del-cli": "^4.0.1", "webpack": "^5.97.1", "prettier": "^2.5.1", "cross-env": "^7.0.3", "@babel/cli": "^7.17.0", "babel-jest": "^27.4.6", "typescript": "^4.9.5", "@babel/core": "^7.17.0", "lint-staged": "^13.2.1", "npm-run-all": "^4.1.5", "@commitlint/cli": "^17.6.1", "standard-version": "^9.3.2", "@babel/preset-env": "^7.16.11", "eslint-plugin-import": "^2.25.4", "eslint-config-prettier": "^8.3.0", "@commitlint/config-conventional": "^16.0.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/schema-utils_4.3.0_1733940805823_0.5933017449186149", "host": "s3://npm-registry-packages-npm-production"}}, "4.3.1": {"name": "schema-utils", "version": "4.3.1", "keywords": ["webpack"], "author": {"url": "https://github.com/webpack-contrib", "name": "webpack Contrib"}, "license": "MIT", "_id": "schema-utils@4.3.1", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/schema-utils", "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "dist": {"shasum": "0fe982d6bfaf404e77f02ed433d429d0f0ad0b6c", "tarball": "https://registry.npmjs.org/schema-utils/-/schema-utils-4.3.1.tgz", "fileCount": 21, "integrity": "sha512-jjlZ7UknkyQxGnHF1w8wDgWfdtnW0hBX7tmDp04zBwDBZ/6tPJI1+RWfBHGMA4+0nAjGptp+eDpIYP6mldJbqg==", "signatures": [{"sig": "MEUCIB5kJE85FGJ1rpA1+QGwPWBpB4BLGbryuJ2VraigppwfAiEAno5c11QRx8Cy+PEALiYO/e6RHy+0qz344jC5FjFEIys=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 77157}, "main": "dist/index.js", "types": "declarations/index.d.ts", "engines": {"node": ">= 10.13.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}, "gitHead": "1478499e3f2d404355b155690f46ab9b33daa0e6", "scripts": {"fix": "npm-run-all fix:js fmt", "fmt": "npm run fmt:check -- --write", "lint": "npm-run-all lint:js lint:types fmt:check", "test": "npm run test:coverage", "build": "npm-run-all -p \"build:**\"", "clean": "del-cli dist declarations", "start": "npm run build -- -w", "fix:js": "npm run lint:js -- --fix", "lint:js": "eslint --cache .", "prepare": "npm run build && husky install", "pretest": "npm run lint", "release": "standard-version", "prebuild": "npm run clean", "security": "npm audit --production", "fmt:check": "prettier \"{**/*,*}.{js,json,md,yml,css,ts}\" --list-different", "test:only": "cross-env NODE_ENV=test jest", "build:code": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "lint:types": "tsc --pretty --noEmit", "test:watch": "npm run test:only -- --watch", "build:types": "tsc --declaration --emitDeclarationOnly --outDir declarations && prettier \"declarations/**/*.ts\" --write", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/schema-utils.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "webpack Validation Utils", "directories": {}, "_nodeVersion": "22.13.1", "dependencies": {"ajv": "^8.9.0", "ajv-formats": "^2.1.1", "ajv-keywords": "^5.1.0", "@types/json-schema": "^7.0.9"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "jest": "^27.4.7", "husky": "^7.0.4", "eslint": "^8.8.0", "del-cli": "^4.0.1", "webpack": "^5.97.1", "prettier": "^2.5.1", "cross-env": "^7.0.3", "@babel/cli": "^7.17.0", "babel-jest": "^27.4.6", "typescript": "^4.9.5", "@babel/core": "^7.17.0", "lint-staged": "^13.2.1", "npm-run-all": "^4.1.5", "@commitlint/cli": "^17.6.1", "standard-version": "^9.3.2", "@babel/preset-env": "^7.16.11", "eslint-plugin-import": "^2.25.4", "eslint-config-prettier": "^8.3.0", "@commitlint/config-conventional": "^16.0.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/schema-utils_4.3.1_1745327021523_0.8234706197376027", "host": "s3://npm-registry-packages-npm-production"}}, "4.3.2": {"name": "schema-utils", "version": "4.3.2", "description": "webpack Validation Utils", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack/schema-utils.git"}, "author": {"name": "webpack Contrib", "url": "https://github.com/webpack-contrib"}, "homepage": "https://github.com/webpack/schema-utils", "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/index.js", "types": "declarations/index.d.ts", "engines": {"node": ">= 10.13.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist declarations", "prebuild": "npm run clean", "build:types": "tsc --declaration --emitDeclarationOnly --outDir declarations && prettier \"declarations/**/*.ts\" --write", "build:code": "cross-env NODE_ENV=production babel src -d dist --copy-files", "build": "npm-run-all -p \"build:**\"", "commitlint": "commitlint --from=master", "security": "npm audit --production", "fmt:check": "prettier \"{**/*,*}.{js,json,md,yml,css,ts}\" --list-different", "lint:js": "eslint --cache .", "lint:types": "tsc --pretty --noEmit", "lint": "npm-run-all lint:js lint:types fmt:check", "fmt": "npm run fmt:check -- --write", "fix:js": "npm run lint:js -- --fix", "fix": "npm-run-all fix:js fmt", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "npm run build && husky install", "release": "standard-version"}, "dependencies": {"@types/json-schema": "^7.0.9", "ajv": "^8.9.0", "ajv-formats": "^2.1.1", "ajv-keywords": "^5.1.0"}, "devDependencies": {"@babel/cli": "^7.17.0", "@babel/core": "^7.17.0", "@babel/preset-env": "^7.16.11", "@commitlint/cli": "^17.6.1", "@commitlint/config-conventional": "^16.0.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^27.4.6", "cross-env": "^7.0.3", "del": "^6.0.0", "del-cli": "^4.0.1", "eslint": "^8.8.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.25.4", "husky": "^7.0.4", "jest": "^27.4.7", "lint-staged": "^13.2.1", "npm-run-all": "^4.1.5", "prettier": "^2.5.1", "standard-version": "^9.3.2", "typescript": "^4.9.5", "webpack": "^5.97.1"}, "keywords": ["webpack"], "_id": "schema-utils@4.3.2", "gitHead": "2a92a6a229a5a63f6c9eecf5cb9d78add52d59a9", "_nodeVersion": "22.13.1", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-Gn/JaSk/Mt9gYubxTtSn/QCV4em9mpAPiR1rqy/Ocu19u/G9J5WWdNoUT4SiV6mFC3y6cxyFcFwdzPM3FgxGAQ==", "shasum": "0c10878bf4a73fd2b1dfd14b9462b26788c806ae", "tarball": "https://registry.npmjs.org/schema-utils/-/schema-utils-4.3.2.tgz", "fileCount": 21, "unpackedSize": 77279, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIEId7pZd6rWhJTJmvB+ttZUgdBrp7NNPk6cQkear3oQYAiEAtMPKyOCO/iTzT8oQSlmdW2La0Mex1xGgm7Km2GVLFkw="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sokra", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/schema-utils_4.3.2_1745344065064_0.9964303888570767"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-03-07T06:01:28.207Z", "modified": "2025-04-22T17:47:45.430Z", "0.1.0": "2017-03-07T06:01:28.207Z", "0.2.1": "2017-03-13T07:04:56.561Z", "0.3.0": "2017-04-29T18:20:47.722Z", "0.4.0": "2017-10-30T18:21:50.029Z", "0.4.1": "2017-11-03T09:51:33.117Z", "0.4.2": "2017-11-09T20:16:27.548Z", "0.4.3": "2017-12-14T02:49:01.330Z", "0.4.4": "2018-02-13T13:33:41.966Z", "0.4.5": "2018-02-13T13:47:48.167Z", "0.4.6": "2018-08-06T17:06:08.539Z", "0.4.7": "2018-08-07T12:16:50.127Z", "1.0.0": "2018-08-07T16:31:12.254Z", "2.0.0": "2019-07-17T14:16:49.234Z", "2.0.1": "2019-07-18T16:12:42.173Z", "2.1.0": "2019-08-07T15:02:52.138Z", "2.2.0": "2019-09-02T10:57:34.379Z", "2.3.0": "2019-09-26T13:33:07.161Z", "2.4.0": "2019-09-26T19:03:42.469Z", "2.4.1": "2019-09-27T11:25:05.323Z", "2.5.0": "2019-10-15T12:06:37.995Z", "2.6.0": "2019-11-27T12:23:28.917Z", "2.6.1": "2019-11-28T13:12:15.734Z", "2.6.2": "2020-01-14T13:19:13.823Z", "2.6.3": "2020-01-17T10:30:14.977Z", "2.6.4": "2020-01-17T11:47:46.281Z", "2.6.5": "2020-03-11T12:05:26.330Z", "2.6.6": "2020-04-17T15:41:37.894Z", "2.7.0": "2020-05-29T14:10:00.858Z", "2.7.1": "2020-08-31T11:00:30.300Z", "3.0.0": "2020-10-05T18:21:37.231Z", "3.1.0": "2021-07-05T11:33:38.926Z", "3.1.1": "2021-07-19T11:38:40.814Z", "4.0.0": "2021-11-16T15:15:38.469Z", "4.0.1": "2023-04-15T14:55:14.105Z", "3.1.2": "2023-04-15T15:21:23.762Z", "4.1.0": "2023-06-07T22:22:21.141Z", "3.2.0": "2023-06-07T22:25:11.393Z", "4.2.0": "2023-06-14T14:25:16.297Z", "3.3.0": "2023-06-14T14:52:43.727Z", "4.3.0": "2024-12-11T18:13:26.041Z", "4.3.1": "2025-04-22T13:03:41.729Z", "4.3.2": "2025-04-22T17:47:45.257Z"}, "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "author": {"name": "webpack Contrib", "url": "https://github.com/webpack-contrib"}, "license": "MIT", "homepage": "https://github.com/webpack/schema-utils", "keywords": ["webpack"], "repository": {"type": "git", "url": "git+https://github.com/webpack/schema-utils.git"}, "description": "webpack Validation Utils", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "<div align=\"center\">\n  <a href=\"http://json-schema.org\">\n    <img width=\"160\" height=\"160\"\n      src=\"https://raw.githubusercontent.com/webpack-contrib/schema-utils/master/.github/assets/logo.png\">\n  </a>\n  <a href=\"https://github.com/webpack/webpack\">\n    <img width=\"200\" height=\"200\"\n      src=\"https://webpack.js.org/assets/icon-square-big.svg\">\n  </a>\n</div>\n\n[![npm][npm]][npm-url]\n[![node][node]][node-url]\n[![tests][tests]][tests-url]\n[![coverage][cover]][cover-url]\n[![GitHub Discussions][discussion]][discussion-url]\n[![size][size]][size-url]\n\n# schema-utils\n\nPackage for validate options in loaders and plugins.\n\n## Getting Started\n\nTo begin, you'll need to install `schema-utils`:\n\n```console\nnpm install schema-utils\n```\n\n## API\n\n**schema.json**\n\n```json\n{\n  \"type\": \"object\",\n  \"properties\": {\n    \"option\": {\n      \"type\": \"boolean\"\n    }\n  },\n  \"additionalProperties\": false\n}\n```\n\n```js\nimport schema from \"./path/to/schema.json\";\nimport { validate } from \"schema-utils\";\n\nconst options = { option: true };\nconst configuration = { name: \"Loader Name/Plugin Name/Name\" };\n\nvalidate(schema, options, configuration);\n```\n\n### `schema`\n\nType: `String`\n\nJSON schema.\n\nSimple example of schema:\n\n```json\n{\n  \"type\": \"object\",\n  \"properties\": {\n    \"name\": {\n      \"description\": \"This is description of option.\",\n      \"type\": \"string\"\n    }\n  },\n  \"additionalProperties\": false\n}\n```\n\n### `options`\n\nType: `Object`\n\nObject with options.\n\n```js\nimport schema from \"./path/to/schema.json\";\nimport { validate } from \"schema-utils\";\n\nconst options = { foo: \"bar\" };\n\nvalidate(schema, { name: 123 }, { name: \"MyPlugin\" });\n```\n\n### `configuration`\n\nAllow to configure validator.\n\nThere is an alternative method to configure the `name` and`baseDataPath` options via the `title` property in the schema.\nFor example:\n\n```json\n{\n  \"title\": \"My Loader options\",\n  \"type\": \"object\",\n  \"properties\": {\n    \"name\": {\n      \"description\": \"This is description of option.\",\n      \"type\": \"string\"\n    }\n  },\n  \"additionalProperties\": false\n}\n```\n\nThe last word used for the `baseDataPath` option, other words used for the `name` option.\nBased on the example above the `name` option equals `My Loader`, the `baseDataPath` option equals `options`.\n\n#### `name`\n\nType: `Object`\nDefault: `\"Object\"`\n\nAllow to setup name in validation errors.\n\n```js\nimport schema from \"./path/to/schema.json\";\nimport { validate } from \"schema-utils\";\n\nconst options = { foo: \"bar\" };\n\nvalidate(schema, options, { name: \"MyPlugin\" });\n```\n\n```shell\nInvalid configuration object. MyPlugin has been initialised using a configuration object that does not match the API schema.\n - configuration.optionName should be a integer.\n```\n\n#### `baseDataPath`\n\nType: `String`\nDefault: `\"configuration\"`\n\nAllow to setup base data path in validation errors.\n\n```js\nimport schema from \"./path/to/schema.json\";\nimport { validate } from \"schema-utils\";\n\nconst options = { foo: \"bar\" };\n\nvalidate(schema, options, { name: \"MyPlugin\", baseDataPath: \"options\" });\n```\n\n```shell\nInvalid options object. MyPlugin has been initialised using an options object that does not match the API schema.\n - options.optionName should be a integer.\n```\n\n#### `postFormatter`\n\nType: `Function`\nDefault: `undefined`\n\nAllow to reformat errors.\n\n```js\nimport schema from \"./path/to/schema.json\";\nimport { validate } from \"schema-utils\";\n\nconst options = { foo: \"bar\" };\n\nvalidate(schema, options, {\n  name: \"MyPlugin\",\n  postFormatter: (formattedError, error) => {\n    if (error.keyword === \"type\") {\n      return `${formattedError}\\nAdditional Information.`;\n    }\n\n    return formattedError;\n  },\n});\n```\n\n```shell\nInvalid options object. MyPlugin has been initialized using an options object that does not match the API schema.\n - options.optionName should be a integer.\n   Additional Information.\n```\n\n## Examples\n\n**schema.json**\n\n```json\n{\n  \"type\": \"object\",\n  \"properties\": {\n    \"name\": {\n      \"type\": \"string\"\n    },\n    \"test\": {\n      \"anyOf\": [\n        { \"type\": \"array\" },\n        { \"type\": \"string\" },\n        { \"instanceof\": \"RegExp\" }\n      ]\n    },\n    \"transform\": {\n      \"instanceof\": \"Function\"\n    },\n    \"sourceMap\": {\n      \"type\": \"boolean\"\n    }\n  },\n  \"additionalProperties\": false\n}\n```\n\n### `Loader`\n\n```js\nimport { getOptions } from \"loader-utils\";\nimport { validate } from \"schema-utils\";\n\nimport schema from \"path/to/schema.json\";\n\nfunction loader(src, map) {\n  const options = getOptions(this);\n\n  validate(schema, options, {\n    name: \"Loader Name\",\n    baseDataPath: \"options\",\n  });\n\n  // Code...\n}\n\nexport default loader;\n```\n\n### `Plugin`\n\n```js\nimport { validate } from \"schema-utils\";\n\nimport schema from \"path/to/schema.json\";\n\nclass Plugin {\n  constructor(options) {\n    validate(schema, options, {\n      name: \"Plugin Name\",\n      baseDataPath: \"options\",\n    });\n\n    this.options = options;\n  }\n\n  apply(compiler) {\n    // Code...\n  }\n}\n\nexport default Plugin;\n```\n\n### Allow to disable and enable validation (the `validate` function do nothing)\n\nThis can be useful when you don't want to do validation for `production` builds.\n\n```js\nimport { disableValidation, enableValidation, validate } from \"schema-utils\";\n\n// Disable validation\ndisableValidation();\n// Do nothing\nvalidate(schema, options);\n\n// Enable validation\nenableValidation();\n// Will throw an error if schema is not valid\nvalidate(schema, options);\n\n// Allow to undestand do you need validation or not\nconst need = needValidate();\n\nconsole.log(need);\n```\n\nAlso you can enable/disable validation using the `process.env.SKIP_VALIDATION` env variable.\n\nSupported values (case insensitive):\n\n- `yes`/`y`/`true`/`1`/`on`\n- `no`/`n`/`false`/`0`/`off`\n\n## Contributing\n\nPlease take a moment to read our contributing guidelines if you haven't yet done so.\n\n[CONTRIBUTING](./.github/CONTRIBUTING.md)\n\n## License\n\n[MIT](./LICENSE)\n\n[npm]: https://img.shields.io/npm/v/schema-utils.svg\n[npm-url]: https://npmjs.com/package/schema-utils\n[node]: https://img.shields.io/node/v/schema-utils.svg\n[node-url]: https://nodejs.org\n[tests]: https://github.com/webpack/schema-utils/workflows/schema-utils/badge.svg\n[tests-url]: https://github.com/webpack/schema-utils/actions\n[cover]: https://codecov.io/gh/webpack/schema-utils/branch/master/graph/badge.svg\n[cover-url]: https://codecov.io/gh/webpack/schema-utils\n[discussion]: https://img.shields.io/github/discussions/webpack/webpack\n[discussion-url]: https://github.com/webpack/webpack/discussions\n[size]: https://packagephobia.com/badge?p=schema-utils\n[size-url]: https://packagephobia.com/result?p=schema-utils\n", "readmeFilename": "README.md", "users": {"daizch": true, "huarse": true, "flumpus-dev": true}}