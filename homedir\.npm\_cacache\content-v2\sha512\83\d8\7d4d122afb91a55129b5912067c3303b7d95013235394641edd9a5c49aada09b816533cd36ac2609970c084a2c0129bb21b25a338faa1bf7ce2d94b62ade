{"_id": "is-glob", "_rev": "41-c94270ad642115ef42f35b1479541b4a", "name": "is-glob", "description": "Returns `true` if the given string looks like a glob pattern or an extglob pattern. This makes it easy to create code that only uses external modules like node-glob when necessary, resulting in much faster code execution and initialization time, and a bet", "dist-tags": {"latest": "4.0.3"}, "versions": {"0.1.0": {"name": "is-glob", "description": "Returns `true` if the given string looks like a glob pattern.", "version": "0.1.0", "homepage": "https://github.com/jonschlinkert/is-glob", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git://github.com/jonschlinkert/is-glob.git"}, "bugs": {"url": "https://github.com/jonschlinkert/is-glob/issues"}, "license": {"type": "MIT", "url": "https://github.com/jonschlinkert/is-glob/blob/master/LICENSE-MIT"}, "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha -R spec"}, "devDependencies": {"mocha": "*", "should": "*"}, "keywords": ["exec", "expression", "glob", "match", "matches", "pattern", "re", "regex", "regular", "string", "test"], "_id": "is-glob@0.1.0", "_shasum": "d93dae5cf98c8c26bbedd591eee9dfb791be8bf6", "_from": ".", "_npmVersion": "1.4.23", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "d93dae5cf98c8c26bbedd591eee9dfb791be8bf6", "tarball": "https://registry.npmjs.org/is-glob/-/is-glob-0.1.0.tgz", "integrity": "sha512-b2JFy1MSWg5/kit6rig/sAU8tBVRp1QfEMs5skhne+MXgprAzyqYGilFX7s7Xqj9qq3y4QqtT6FH0TcNGqQHZw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDYWLpmJ95kElNvUJ2/q/i4xmuOMyGvt433ufAkCEKV5wIhAMFu5GcKDkU1/vu2keXuBDFAIMskq23K78DnIlXf+DeT"}]}, "directories": {}}, "0.2.0": {"name": "is-glob", "description": "Returns `true` if the given string looks like a glob pattern.", "version": "0.2.0", "homepage": "https://github.com/jonschlinkert/is-glob", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git://github.com/jonschlinkert/is-glob.git"}, "bugs": {"url": "https://github.com/jonschlinkert/is-glob/issues"}, "license": {"type": "MIT", "url": "https://github.com/jonschlinkert/is-glob/blob/master/LICENSE-MIT"}, "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha -R spec"}, "devDependencies": {"mocha": "*", "should": "*"}, "keywords": ["exec", "expression", "glob", "match", "matches", "pattern", "re", "regex", "regular", "string", "test"], "gitHead": "fa474583adbc4c16bd7508d54a5d23627ef7a8d6", "_id": "is-glob@0.2.0", "_shasum": "96c0f79ac570ac428ae1460f97dd63a55d8b43d4", "_from": ".", "_npmVersion": "1.4.23", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "96c0f79ac570ac428ae1460f97dd63a55d8b43d4", "tarball": "https://registry.npmjs.org/is-glob/-/is-glob-0.2.0.tgz", "integrity": "sha512-qbdN3g3q8JoykpVcNvv0drZac8+b6fJN30zKw3wpLNaPIBEtvc9p/WsGUXV4d1bLVRrJ0i8f2w6chM9VjqqNFA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAmg+G9oEvBDCc30oGCXlK7jgfLpfAkRgRIErH8vPKldAiEAud9aTa5wJ7sfYbZvFtk5SeA7J1pGw1UiBRWvqWMZh4w="}]}, "directories": {}}, "0.3.0": {"name": "is-glob", "description": "Returns `true` if the given string looks like a glob pattern.", "version": "0.3.0", "homepage": "https://github.com/jonschlinkert/is-glob", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git://github.com/jonschlinkert/is-glob.git"}, "bugs": {"url": "https://github.com/jonschlinkert/is-glob/issues"}, "license": {"type": "MIT", "url": "https://github.com/jonschlinkert/is-glob/blob/master/LICENSE-MIT"}, "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha -R spec"}, "devDependencies": {"mocha": "*", "should": "*"}, "keywords": ["exec", "expression", "glob", "match", "matches", "pattern", "re", "regex", "regular", "string", "test"], "gitHead": "1a9b2d4dacc0057fdce173eaf344d1cb72583fc6", "_id": "is-glob@0.3.0", "_shasum": "36f358abccfb33836406c44075b121a58736a382", "_from": ".", "_npmVersion": "1.4.23", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "36f358abccfb33836406c44075b121a58736a382", "tarball": "https://registry.npmjs.org/is-glob/-/is-glob-0.3.0.tgz", "integrity": "sha512-Zld7tllwRcN17HMRq1KJVTHRcu1aRV89oHTJezxvjxz340nwg/N9GD+nRyPfNyiBhHEQtT1rv9OfLkcKfvApeA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHXx0zVPfreAn/mSL9KxuIifYBdVTVGToCh3wgz9xgKdAiEAqe0LGKkUszrhfwy5dOjP53VPOZu+SMrIwhrsw7Whpi4="}]}, "directories": {}}, "1.0.0": {"name": "is-glob", "description": "Returns `true` if the given string looks like a glob pattern.", "version": "1.0.0", "homepage": "https://github.com/jonschlinkert/is-glob", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git://github.com/jonschlinkert/is-glob.git"}, "bugs": {"url": "https://github.com/jonschlinkert/is-glob/issues"}, "license": {"type": "MIT", "url": "https://github.com/jonschlinkert/is-glob/blob/master/LICENSE"}, "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha -R spec"}, "devDependencies": {"mocha": "*", "should": "*"}, "keywords": ["exec", "expression", "glob", "match", "matches", "pattern", "re", "regex", "regular", "string", "test"], "gitHead": "d068608972a863d853dee5e75c73b95838e301e4", "_id": "is-glob@1.0.0", "_shasum": "50c5011672a505b1c772bc02cccc078110e47da9", "_from": ".", "_npmVersion": "1.4.23", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "50c5011672a505b1c772bc02cccc078110e47da9", "tarball": "https://registry.npmjs.org/is-glob/-/is-glob-1.0.0.tgz", "integrity": "sha512-puV1SD/GmbrPgELC022GFCJV/OOhOgd5LqSgv28p/WElfjbjOgqOmo2OfD7bIRzSUs5kP19gQxhpCnjFusPDbw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCXe5rx4YwE9pdMZuLN+epHyswnfejKeStvKLB8clvJ5AIgGGaBqtjaTqI5TrU7uAbXzdJGyGWksJFgzhAfVNG7g0g="}]}, "directories": {}}, "1.1.0": {"name": "is-glob", "description": "Returns `true` if the given string looks like a glob pattern.", "version": "1.1.0", "homepage": "https://github.com/jonschlinkert/is-glob", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git://github.com/jonschlinkert/is-glob.git"}, "bugs": {"url": "https://github.com/jonschlinkert/is-glob/issues"}, "license": {"type": "MIT", "url": "https://github.com/jonschlinkert/is-glob/blob/master/LICENSE"}, "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha -R spec"}, "devDependencies": {"mocha": "*", "should": "*"}, "keywords": ["bash", "braces", "check", "exec", "expression", "glob", "globstar", "match", "matches", "pattern", "regex", "regular", "string", "test"], "gitHead": "01054073f4326c63afe61d84b75aee6914e03546", "_id": "is-glob@1.1.0", "_shasum": "e1970788df44c826dbdf8a79b4cf4e951219bd63", "_from": ".", "_npmVersion": "1.4.23", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "e1970788df44c826dbdf8a79b4cf4e951219bd63", "tarball": "https://registry.npmjs.org/is-glob/-/is-glob-1.1.0.tgz", "integrity": "sha512-UdtWSjViHQ9QfaPKSsI/cZxzAXBmfPaM7zA28H7lNlmWqfSTBz5tepla5r7LpAkQg2K040qcsmrGwdf5Ps3b3g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEyzJj4zNxpVpRSxpT0gMngjkfW3S43kolXryB8THQVIAiEAjgiHJ6HzkpVjYLFoxm7vlultdIAjm3KuCAatgvhcj9k="}]}, "directories": {}}, "1.1.1": {"name": "is-glob", "description": "Returns `true` if the given string looks like a glob pattern.", "version": "1.1.1", "homepage": "https://github.com/jonschlinkert/is-glob", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "https://github.com/jonschlinkert/is-glob"}, "bugs": {"url": "https://github.com/jonschlinkert/is-glob/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"mocha": "*", "should": "*"}, "keywords": ["bash", "braces", "check", "exec", "extglob", "expression", "glob", "globbing", "globstar", "match", "matches", "pattern", "regex", "regular", "string", "test"], "gitHead": "f3527a1072e036d007ac3f971d79bcc2f83c36f3", "_id": "is-glob@1.1.1", "_shasum": "06801ba469d88bc46778ef1aa8e265f6acda9856", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "06801ba469d88bc46778ef1aa8e265f6acda9856", "tarball": "https://registry.npmjs.org/is-glob/-/is-glob-1.1.1.tgz", "integrity": "sha512-3pzjjeceplSxBd+Vxe2FX6ewutjQJxf84YramPHLs10+DzFw+UFGnXMqtN7PI0DN2ZylC92POxxBE9WRFpAZNA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGWfmnRdIy0wIasZ0jdjlORiwPrmShqzcYK01ASpjqasAiAu2Gvz0RsCTpTD3+sNvzjkDwzaFerucGF1MWJ5OKCn8w=="}]}, "directories": {}}, "1.1.2": {"name": "is-glob", "description": "Returns `true` if the given string looks like a glob pattern.", "version": "1.1.2", "homepage": "https://github.com/jonschlinkert/is-glob", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "https://github.com/jonschlinkert/is-glob"}, "bugs": {"url": "https://github.com/jonschlinkert/is-glob/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"mocha": "*", "should": "*"}, "keywords": ["bash", "braces", "check", "exec", "extglob", "expression", "glob", "globbing", "globstar", "match", "matches", "pattern", "regex", "regular", "string", "test"], "gitHead": "dbfdb6a271cc0fc46168d23dc0603739ff287e01", "_id": "is-glob@1.1.2", "_shasum": "2d3ea960a5af26ab6d136adb5dc8d6f99e4dc0f6", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "2d3ea960a5af26ab6d136adb5dc8d6f99e4dc0f6", "tarball": "https://registry.npmjs.org/is-glob/-/is-glob-1.1.2.tgz", "integrity": "sha512-Zx4dHN+2ruRq5lrppeVgm/UJMHc3XPqLbzk7NBBPOqFGLkfn0izcxPdK5N8CaFqz4ZdSohJRV/Ty2SB1vHP/rw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCVWW+HozZM1mCd5qYutBdZweXGfhsyggsEVPn3LM37RQIhAJHv1d0M9r3JfkolVpoAZfqLsSZSHOjoyjZUZGNx9hNE"}]}, "directories": {}}, "1.1.3": {"name": "is-glob", "description": "Returns `true` if the given string looks like a glob pattern.", "version": "1.1.3", "homepage": "https://github.com/jonschlinkert/is-glob", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "https://github.com/jonschlinkert/is-glob"}, "bugs": {"url": "https://github.com/jonschlinkert/is-glob/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"mocha": "*", "should": "*"}, "keywords": ["bash", "braces", "check", "exec", "extglob", "expression", "glob", "globbing", "globstar", "match", "matches", "pattern", "regex", "regular", "string", "test"], "gitHead": "dbfdb6a271cc0fc46168d23dc0603739ff287e01", "_id": "is-glob@1.1.3", "_shasum": "b4c64b8303d39114492a460d364ccfb0d3c0a045", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "b4c64b8303d39114492a460d364ccfb0d3c0a045", "tarball": "https://registry.npmjs.org/is-glob/-/is-glob-1.1.3.tgz", "integrity": "sha512-tKLBgs6hhR6eI0mq8M2b91eUynY27ydu7MbY68IxVE1mlX2r7vbvXJ5qNz/KgDGMXAqMis156hxfvQVh7DcYTA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCoFKuGzuxjrUzVL+vbSQ1BDwZ5kQYnrf1B2z5550OB1wIhAJNKV1HezX3GZy7krsOSzT8lGMjCw1RTSLcavLbr5efQ"}]}, "directories": {}}, "2.0.0": {"name": "is-glob", "description": "Returns `true` if the given string looks like a glob pattern.", "version": "2.0.0", "homepage": "https://github.com/jonschlinkert/is-glob", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "https://github.com/jonschlinkert/is-glob"}, "bugs": {"url": "https://github.com/jonschlinkert/is-glob/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"mocha": "*", "should": "*"}, "keywords": ["bash", "braces", "check", "exec", "extglob", "expression", "glob", "globbing", "globstar", "match", "matches", "pattern", "regex", "regular", "string", "test"], "gitHead": "722a6e9f14cb370aa8cc5976d1319bc5ce8dfdfe", "_id": "is-glob@2.0.0", "_shasum": "97fcea442d4cb45cfa79e7447b78cf1d48eee4f5", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "97fcea442d4cb45cfa79e7447b78cf1d48eee4f5", "tarball": "https://registry.npmjs.org/is-glob/-/is-glob-2.0.0.tgz", "integrity": "sha512-tJyEb4gbmP0CkCfWPclnvurHxRNrM351FzJX++MA58eR+Bk081+sXTgmfMWFyzNPuGqqw3WPUarGtqmJuQZLLA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDkFjhoySAtRhFyRAyHTmACTVhm3xMtGci0pFx6QRYAdAiB2SSdn64W5NSkyaWwc7aEvp8dEgaPWOe+CdKhXRDuDkw=="}]}, "directories": {}}, "2.0.1": {"name": "is-glob", "description": "Returns `true` if the given string looks like a glob pattern or an extglob pattern. This makes it easy to create code that only uses external modules like node-glob when necessary, resulting in much faster code execution and initialization time, and a bet", "version": "2.0.1", "homepage": "https://github.com/jonschlinkert/is-glob", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/is-glob.git"}, "bugs": {"url": "https://github.com/jonschlinkert/is-glob/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"is-extglob": "^1.0.0"}, "devDependencies": {"mocha": "*"}, "keywords": ["bash", "braces", "check", "exec", "extglob", "expression", "glob", "globbing", "globstar", "match", "matches", "pattern", "regex", "regular", "string", "test"], "verb": {"related": {"list": ["has-glob", "is-extglob", "is-posix-bracket", "is-valid-glob", "micromatch"]}}, "gitHead": "d7db1b2dd559b3d5a73f89dbe72d9e9f4d6587d7", "_id": "is-glob@2.0.1", "_shasum": "d096f926a3ded5600f3fdfd91198cb0888c2d863", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "dist": {"shasum": "d096f926a3ded5600f3fdfd91198cb0888c2d863", "tarball": "https://registry.npmjs.org/is-glob/-/is-glob-2.0.1.tgz", "integrity": "sha512-a1dBeB19NXsf/E0+FHqkagizel/LQw2DjSQpvQrj3zT+jYPpaUCryPnrQajXKFLCMuf4I6FhRpaGtw4lPrG6Eg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDLgYPv5Ig/JokUR1aN8Fe5JnF/pBTrVC6axZU5i7KirgIhAJU8WmkuExrEwqDQUxoBchYNAzXitVi6pXlMOJDuR5Xm"}]}, "directories": {}}, "3.0.0": {"name": "is-glob", "description": "Returns `true` if the given string looks like a glob pattern or an extglob pattern. This makes it easy to create code that only uses external modules like node-glob when necessary, resulting in much faster code execution and initialization time, and a bet", "version": "3.0.0", "homepage": "https://github.com/jonschlinkert/is-glob", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://tuvistavie.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://twitter.com/jonschlinkert"}], "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/is-glob.git"}, "bugs": {"url": "https://github.com/jonschlinkert/is-glob/issues"}, "license": "MIT", "files": ["index.js", "LICENSE", "README.md"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"is-extglob": "^2.0.0"}, "devDependencies": {"gulp-format-md": "^0.1.10", "mocha": "^3.0.2"}, "keywords": ["bash", "braces", "check", "exec", "expression", "extglob", "glob", "globbing", "globstar", "is", "match", "matches", "pattern", "regex", "regular", "string", "test"], "verb": {"layout": "default", "plugins": ["gulp-format-md"], "related": {"list": ["assemble", "base", "update", "verb"]}, "reflinks": ["assemble", "bach", "base", "composer", "gulp", "has-glob", "is-valid-glob", "micromatch", "npm", "scaffold", "verb", "vinyl"]}, "gitHead": "57657ef7749e447571b01ea6b068e8288ed539a4", "_id": "is-glob@3.0.0", "_shasum": "e433c222db9d77844084d72db1eff047845985c1", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.3.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "dist": {"shasum": "e433c222db9d77844084d72db1eff047845985c1", "tarball": "https://registry.npmjs.org/is-glob/-/is-glob-3.0.0.tgz", "integrity": "sha512-4y0OjbASF2pPRswLjga5rsAm1l0JEfyEq4Cs08aXqfmmUMYMs2quBLwPO0lv6He0EOuDxPOjXe45jkrFL5Ye6w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHJU8ZHbX8DfpOU4A4xDMzpvs9ewCCFglTyE9p9v4e6EAiAZj1e+8LXjgDWV6GNX9kIjYjMXX79Ns9UbxvPE4WqhzQ=="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/is-glob-3.0.0.tgz_1472932646404_0.7158155627548695"}, "directories": {}}, "3.1.0": {"name": "is-glob", "description": "Returns `true` if the given string looks like a glob pattern or an extglob pattern. This makes it easy to create code that only uses external modules like node-glob when necessary, resulting in much faster code execution and initialization time, and a bet", "version": "3.1.0", "homepage": "https://github.com/jonschlinkert/is-glob", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://tuvistavie.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://twitter.com/jonschlinkert"}], "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/is-glob.git"}, "bugs": {"url": "https://github.com/jonschlinkert/is-glob/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"is-extglob": "^2.1.0"}, "devDependencies": {"gulp-format-md": "^0.1.10", "mocha": "^3.0.2"}, "keywords": ["bash", "braces", "check", "exec", "expression", "extglob", "glob", "globbing", "globstar", "is", "match", "matches", "pattern", "regex", "regular", "string", "test"], "verb": {"layout": "default", "plugins": ["gulp-format-md"], "related": {"list": ["assemble", "base", "update", "verb"]}, "reflinks": ["assemble", "bach", "base", "composer", "gulp", "has-glob", "is-valid-glob", "micromatch", "npm", "scaffold", "verb", "vinyl"]}, "gitHead": "4537576b5747e03db2f78235c98ec27755ace63c", "_id": "is-glob@3.1.0", "_shasum": "7ba5ae24217804ac70707b96922567486cc3e84a", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.7.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "dist": {"shasum": "7ba5ae24217804ac70707b96922567486cc3e84a", "tarball": "https://registry.npmjs.org/is-glob/-/is-glob-3.1.0.tgz", "integrity": "sha512-UFpDDrPgM6qpnFNI+rh/p3bUaq9hKLZN8bMUWzxmcnZVS3omf4IPK+BrewlnWjO1WmUsMYuSjKh4UJuV4+Lqmw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDrlFFd9cU6QIFD3snQD6K0AY1xiWCrL5WRBoc+rlRpaAiEAkE7KUcjJ+VQCP6yjgtEDJT0SbhezmDjNNvEs33eDD8I="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/is-glob-3.1.0.tgz_1476311825972_0.002021018648520112"}, "directories": {}}, "4.0.0": {"name": "is-glob", "description": "Returns `true` if the given string looks like a glob pattern or an extglob pattern. This makes it easy to create code that only uses external modules like node-glob when necessary, resulting in much faster code execution and initialization time, and a bet", "version": "4.0.0", "homepage": "https://github.com/jonschlinkert/is-glob", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "https://twitter.com/doowb"}, {"name": "<PERSON>", "url": "https://tuvistavie.com"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}], "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/is-glob.git"}, "bugs": {"url": "https://github.com/jonschlinkert/is-glob/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"is-extglob": "^2.1.1"}, "devDependencies": {"gulp-format-md": "^0.1.10", "mocha": "^3.0.2"}, "keywords": ["bash", "braces", "check", "exec", "expression", "extglob", "glob", "globbing", "globstar", "is", "match", "matches", "pattern", "regex", "regular", "string", "test"], "verb": {"layout": "default", "plugins": ["gulp-format-md"], "related": {"list": ["assemble", "base", "update", "verb"]}, "reflinks": ["assemble", "bach", "base", "composer", "gulp", "has-glob", "is-valid-glob", "micromatch", "npm", "scaffold", "verb", "vinyl"]}, "gitHead": "490b1a22f7a0d054d5fa3485160d331a705ef152", "_id": "is-glob@4.0.0", "_shasum": "9521c76845cc2610a85203ddf080a958c2ffabc0", "_from": ".", "_npmVersion": "4.6.1", "_nodeVersion": "6.10.1", "_npmUser": {"name": "doowb", "email": "<EMAIL>"}, "dist": {"shasum": "9521c76845cc2610a85203ddf080a958c2ffabc0", "tarball": "https://registry.npmjs.org/is-glob/-/is-glob-4.0.0.tgz", "integrity": "sha512-IEg9HSCKitWUYBRkCSztkm2Lenav8e04mlxHjiMRg2w9Bx82TFIDEDamwfn0RgwFgLNLSkZd0YJT2ColdN8KCw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCZECEui7rI43jYW8WQAjedMxoEWzc3qjEoh1yqvBE1YQIhAIK9qUTgPa33fHeK9VwzAP7qNaRdRLcn7r8I5BImNncA"}]}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/is-glob-4.0.0.tgz_1502139259462_0.8305888224858791"}, "directories": {}}, "4.0.1": {"name": "is-glob", "description": "Returns `true` if the given string looks like a glob pattern or an extglob pattern. This makes it easy to create code that only uses external modules like node-glob when necessary, resulting in much faster code execution and initialization time, and a bet", "version": "4.0.1", "homepage": "https://github.com/micromatch/is-glob", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "https://twitter.com/doowb"}, {"name": "<PERSON>", "url": "https://tuvistavie.com"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}], "repository": {"type": "git", "url": "git+https://github.com/micromatch/is-glob.git"}, "bugs": {"url": "https://github.com/micromatch/is-glob/issues"}, "license": "MIT", "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"is-extglob": "^2.1.1"}, "devDependencies": {"gulp-format-md": "^0.1.10", "mocha": "^3.0.2"}, "keywords": ["bash", "braces", "check", "exec", "expression", "extglob", "glob", "globbing", "globstar", "is", "match", "matches", "pattern", "regex", "regular", "string", "test"], "verb": {"layout": "default", "plugins": ["gulp-format-md"], "related": {"list": ["assemble", "base", "update", "verb"]}, "reflinks": ["assemble", "bach", "base", "composer", "gulp", "has-glob", "is-valid-glob", "micromatch", "npm", "scaffold", "verb", "vinyl"]}, "gitHead": "4ac6a0cb019fa39141457946c2d455f281f73659", "_id": "is-glob@4.0.1", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.1", "_npmUser": {"name": "phated", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-5G0tKtBTFImOqDnLB2hG6Bp2qcKEFduo4tZu9MT/H6NQv/ghhy30o55ufafxJ/LdH79LLs2Kfrn85TLKyA7BUg==", "shasum": "7567dbe9f2f5e2467bc77ab83c4a29482407a5dc", "tarball": "https://registry.npmjs.org/is-glob/-/is-glob-4.0.1.tgz", "fileCount": 4, "unpackedSize": 11285, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcm/icCRA9TVsSAnZWagAAjNkP/1NEsRwsDNmHrumIbD19\nzEU1zZjHfN0XqjB286992EaNriCSMXs54xYQmxM6UQ5tHWytmY9caAmc5hFl\nBEm7L0eczOpDGxKpIZWHLy8gEUfLcYRzn6vRvdTpMDRf6zSVrcC7/sebclAT\njdMsZc0HrO3Lg3QZXXkJ6+elOxVrJT5HdVr5avXgneeD6EA3VTFVNRMu5mTv\n/cogXKWvVi0rOgupV4mYKwkAH+9Anh1MbfnPxVg7CLE/GG8AyhtgkTbzovGn\njoQ1rWN4UHYIG3Lguyg966HP48o0GCPklbXrb5a3yDRoZQgjng2C0zpt7dUU\nTINpyDxiQMgjJhriNnuYkr4SkOMg0xahJQjjiP0Zer9MTJPCbty064P4WJJ6\ni8wdxz06qhfb++WF+h3N5YQOT824Cg/cD48KBC/+qXV2ltnvQ+cO767drZXL\nsdqq184pqI/k+VaXFPOW55AJ5H3vbXBdfFj84ys6q2ly/dtiSiWuNV0BBLNk\n4Vp63vHMT/O4hbMStsSFtvGOuc+ym/0+5D8xX3YCjFFDLxlWyr5B89CmVkoe\nwMRwPXbqwds3ZQIiv7UlX+OmkhLscfMI888QEYdZcezOqwfKK2gpk0qbkE6w\nVL4jNOQcgCBmz8AIWSCZM8dTbkLhLGWS7voN5HhOt1LlVByrfYOrlLYYEyxK\n/LsU\r\n=kTf9\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDXRVrxEyqschx5H9kPBFzh6lmmRztFipAqaCxxvIBycgIgcKFC/mkQs0pi4dahvGTj9OuZpwzHhpaOCj4zfvILqck="}]}, "maintainers": [{"email": "<EMAIL>", "name": "doowb"}, {"email": "<EMAIL>", "name": "j<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "phated"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/is-glob_4.0.1_1553725595739_0.7795574194293109"}, "_hasShrinkwrap": false}, "4.0.2": {"name": "is-glob", "description": "Returns `true` if the given string looks like a glob pattern or an extglob pattern. This makes it easy to create code that only uses external modules like node-glob when necessary, resulting in much faster code execution and initialization time, and a bet", "version": "4.0.2", "homepage": "https://github.com/micromatch/is-glob", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "https://twitter.com/doowb"}, {"name": "<PERSON>", "url": "https://tuvistavie.com"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}], "repository": {"type": "git", "url": "git+https://github.com/micromatch/is-glob.git"}, "bugs": {"url": "https://github.com/micromatch/is-glob/issues"}, "license": "MIT", "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"is-extglob": "^2.1.1"}, "devDependencies": {"gulp-format-md": "^0.1.10", "mocha": "^3.0.2"}, "keywords": ["bash", "braces", "check", "exec", "expression", "extglob", "glob", "globbing", "globstar", "is", "match", "matches", "pattern", "regex", "regular", "string", "test"], "verb": {"layout": "default", "plugins": ["gulp-format-md"], "related": {"list": ["assemble", "base", "update", "verb"]}, "reflinks": ["assemble", "bach", "base", "composer", "gulp", "has-glob", "is-valid-glob", "micromatch", "npm", "scaffold", "verb", "vinyl"]}, "gitHead": "7a612a300ac87724ce3cc036aff60b2a8c8130a0", "_id": "is-glob@4.0.2", "_nodeVersion": "16.9.1", "_npmVersion": "7.21.1", "dist": {"integrity": "sha512-ZZTOjRcDjuAAAv2cTBQP/lL59ZTArx77+7UzHdWW/XB1mrfp7DEaVpKmZ0XIzx+M7AxfhKcqV+nMetUQmFifwg==", "shasum": "859fc2e731e58c902f99fcabccb75a7dd07d29d8", "tarball": "https://registry.npmjs.org/is-glob/-/is-glob-4.0.2.tgz", "fileCount": 4, "unpackedSize": 13227, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCXnp3GG7ay1kVRW+QLzH2Uwl/GmXERiE36lcI/h0GUAAIhAKtk+OTaSoqkwSIzAyOG8Nfi0JDDghEGDENPX7d/lSRs"}]}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "phated", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/is-glob_4.0.2_1632695857500_0.3544434838902879"}, "_hasShrinkwrap": false}, "4.0.3": {"name": "is-glob", "description": "Returns `true` if the given string looks like a glob pattern or an extglob pattern. This makes it easy to create code that only uses external modules like node-glob when necessary, resulting in much faster code execution and initialization time, and a bet", "version": "4.0.3", "homepage": "https://github.com/micromatch/is-glob", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "https://twitter.com/doowb"}, {"name": "<PERSON>", "url": "https://tuvistavie.com"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}], "repository": {"type": "git", "url": "git+https://github.com/micromatch/is-glob.git"}, "bugs": {"url": "https://github.com/micromatch/is-glob/issues"}, "license": "MIT", "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha && node benchmark.js"}, "dependencies": {"is-extglob": "^2.1.1"}, "devDependencies": {"gulp-format-md": "^0.1.10", "mocha": "^3.0.2"}, "keywords": ["bash", "braces", "check", "exec", "expression", "extglob", "glob", "globbing", "globstar", "is", "match", "matches", "pattern", "regex", "regular", "string", "test"], "verb": {"layout": "default", "plugins": ["gulp-format-md"], "related": {"list": ["assemble", "base", "update", "verb"]}, "reflinks": ["assemble", "bach", "base", "composer", "gulp", "has-glob", "is-valid-glob", "micromatch", "npm", "scaffold", "verb", "vinyl"]}, "gitHead": "627ca7e552c69e8d62d620b4715a2658267b3d17", "_id": "is-glob@4.0.3", "_nodeVersion": "14.18.0", "_npmVersion": "7.24.1", "dist": {"integrity": "sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==", "shasum": "64f61e42cbbb2eec2071a9dac0b28ba1e65d5084", "tarball": "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz", "fileCount": 4, "unpackedSize": 13609, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2wEnCRA9TVsSAnZWagAA5OsP+gIh2MU1OYWdQkV9CZ6+\nZchbZEI+VOMY6TVRYjVNoBgni3Vfa9XXWXuYLvT6zzwloWKVCaKbb4opcDDL\nbizrciCImbBH3fxgkpgWsVbL7idCO7xpKRy6YgRwG2t1yNVm0N6RqX9qycf5\ndp+5CF8atKPxx6Zhlwv87uAEF4DZ1Y8BpaXLD/rIxnWuf84tyFGR7uRci2Nz\n+jbYP2cKwQf48KJc/FG+9h+PMya+XxV8ryzli14nGgPxZZJSoXnHWUG/XWt0\n36d3D7KpszVojg6G+oxETV598qI/wH2AXouB9gNVDAWbJGCM7Ef1c5txnWRw\nHLysR9fCXUTW5wTQ8uYaeQkpacRxlWHAuQUJ3dleRCS4YaczYxz1q1MdiYFZ\nGPYD8TRm1GFiuRCmjqKBx+0Rsnv+nhbGXtTzgLMnNDoKZjlwOeqgf0RBbKLh\ntxK5FDP4TLFtVmVMcmWEDjPv34NgBxUhOFcmdQC1vATK9xbu6S29Zzz4qhYl\nczfy5/04FtGTps8v6oV61Kh0yIXtsc0kaqer4c3UYwoOVhRgDqVlA+sSi6c/\n4nmB0Z+2GGs9zdl+Xqk+9v1NfUGef40QT55/llyiAeJJL68rr44DqN1NHnSq\nNTxqe32g8g9m7lVFfSp7MAcjE2lhU7L7BUx2Ysl1+OlKcgj/sfAg+FVPyH1M\n1zVW\r\n=O3Fk\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAhQer6Hr/VDkFsMZ/aaZgKbAuOabp2GhKIELxzzGoGYAiAgHCiv1RlXnsiv8SdMYe1mN1kfg/tmPHP80K9jKp50+g=="}]}, "_npmUser": {"name": "phated", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "phated", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/is-glob_4.0.3_1632934367848_0.19558596932963068"}, "_hasShrinkwrap": false}}, "readme": "# is-glob [![NPM version](https://img.shields.io/npm/v/is-glob.svg?style=flat)](https://www.npmjs.com/package/is-glob) [![NPM monthly downloads](https://img.shields.io/npm/dm/is-glob.svg?style=flat)](https://npmjs.org/package/is-glob) [![NPM total downloads](https://img.shields.io/npm/dt/is-glob.svg?style=flat)](https://npmjs.org/package/is-glob) [![Build Status](https://img.shields.io/github/workflow/status/micromatch/is-glob/dev)](https://github.com/micromatch/is-glob/actions)\n\n> Returns `true` if the given string looks like a glob pattern or an extglob pattern. This makes it easy to create code that only uses external modules like node-glob when necessary, resulting in much faster code execution and initialization time, and a better user experience.\n\nPlease consider following this project's author, [<PERSON>](https://github.com/jonschlinkert), and consider starring the project to show your :heart: and support.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install --save is-glob\n```\n\nYou might also be interested in [is-valid-glob](https://github.com/jonschlinkert/is-valid-glob) and [has-glob](https://github.com/jonschlinkert/has-glob).\n\n## Usage\n\n```js\nvar isGlob = require('is-glob');\n```\n\n### Default behavior\n\n**True**\n\nPatterns that have glob characters or regex patterns will return `true`:\n\n```js\nisGlob('!foo.js');\nisGlob('*.js');\nisGlob('**/abc.js');\nisGlob('abc/*.js');\nisGlob('abc/(aaa|bbb).js');\nisGlob('abc/[a-z].js');\nisGlob('abc/{a,b}.js');\n//=> true\n```\n\nExtglobs\n\n```js\nisGlob('abc/@(a).js');\nisGlob('abc/!(a).js');\nisGlob('abc/+(a).js');\nisGlob('abc/*(a).js');\nisGlob('abc/?(a).js');\n//=> true\n```\n\n**False**\n\nEscaped globs or extglobs return `false`:\n\n```js\nisGlob('abc/\\\\@(a).js');\nisGlob('abc/\\\\!(a).js');\nisGlob('abc/\\\\+(a).js');\nisGlob('abc/\\\\*(a).js');\nisGlob('abc/\\\\?(a).js');\nisGlob('\\\\!foo.js');\nisGlob('\\\\*.js');\nisGlob('\\\\*\\\\*/abc.js');\nisGlob('abc/\\\\*.js');\nisGlob('abc/\\\\(aaa|bbb).js');\nisGlob('abc/\\\\[a-z].js');\nisGlob('abc/\\\\{a,b}.js');\n//=> false\n```\n\nPatterns that do not have glob patterns return `false`:\n\n```js\nisGlob('abc.js');\nisGlob('abc/def/ghi.js');\nisGlob('foo.js');\nisGlob('abc/@.js');\nisGlob('abc/+.js');\nisGlob('abc/?.js');\nisGlob();\nisGlob(null);\n//=> false\n```\n\nArrays are also `false` (If you want to check if an array has a glob pattern, use [has-glob](https://github.com/jonschlinkert/has-glob)):\n\n```js\nisGlob(['**/*.js']);\nisGlob(['foo.js']);\n//=> false\n```\n\n### Option strict\n\nWhen `options.strict === false` the behavior is less strict in determining if a pattern is a glob. Meaning that\nsome patterns that would return `false` may return `true`. This is done so that matching libraries like [micromatch](https://github.com/micromatch/micromatch) have a chance at determining if the pattern is a glob or not.\n\n**True**\n\nPatterns that have glob characters or regex patterns will return `true`:\n\n```js\nisGlob('!foo.js', {strict: false});\nisGlob('*.js', {strict: false});\nisGlob('**/abc.js', {strict: false});\nisGlob('abc/*.js', {strict: false});\nisGlob('abc/(aaa|bbb).js', {strict: false});\nisGlob('abc/[a-z].js', {strict: false});\nisGlob('abc/{a,b}.js', {strict: false});\n//=> true\n```\n\nExtglobs\n\n```js\nisGlob('abc/@(a).js', {strict: false});\nisGlob('abc/!(a).js', {strict: false});\nisGlob('abc/+(a).js', {strict: false});\nisGlob('abc/*(a).js', {strict: false});\nisGlob('abc/?(a).js', {strict: false});\n//=> true\n```\n\n**False**\n\nEscaped globs or extglobs return `false`:\n\n```js\nisGlob('\\\\!foo.js', {strict: false});\nisGlob('\\\\*.js', {strict: false});\nisGlob('\\\\*\\\\*/abc.js', {strict: false});\nisGlob('abc/\\\\*.js', {strict: false});\nisGlob('abc/\\\\(aaa|bbb).js', {strict: false});\nisGlob('abc/\\\\[a-z].js', {strict: false});\nisGlob('abc/\\\\{a,b}.js', {strict: false});\n//=> false\n```\n\n## About\n\n<details>\n<summary><strong>Contributing</strong></summary>\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).\n\n</details>\n\n<details>\n<summary><strong>Running Tests</strong></summary>\n\nRunning and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:\n\n```sh\n$ npm install && npm test\n```\n\n</details>\n\n<details>\n<summary><strong>Building docs</strong></summary>\n\n_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_\n\nTo generate the readme, run the following command:\n\n```sh\n$ npm install -g verbose/verb#dev verb-generate-readme && verb\n```\n\n</details>\n\n### Related projects\n\nYou might also be interested in these projects:\n\n* [assemble](https://www.npmjs.com/package/assemble): Get the rocks out of your socks! Assemble makes you fast at creating web projects… [more](https://github.com/assemble/assemble) | [homepage](https://github.com/assemble/assemble \"Get the rocks out of your socks! Assemble makes you fast at creating web projects. Assemble is used by thousands of projects for rapid prototyping, creating themes, scaffolds, boilerplates, e-books, UI components, API documentation, blogs, building websit\")\n* [base](https://www.npmjs.com/package/base): Framework for rapidly creating high quality, server-side node.js applications, using plugins like building blocks | [homepage](https://github.com/node-base/base \"Framework for rapidly creating high quality, server-side node.js applications, using plugins like building blocks\")\n* [update](https://www.npmjs.com/package/update): Be scalable! Update is a new, open source developer framework and CLI for automating updates… [more](https://github.com/update/update) | [homepage](https://github.com/update/update \"Be scalable! Update is a new, open source developer framework and CLI for automating updates of any kind in code projects.\")\n* [verb](https://www.npmjs.com/package/verb): Documentation generator for GitHub projects. Verb is extremely powerful, easy to use, and is used… [more](https://github.com/verbose/verb) | [homepage](https://github.com/verbose/verb \"Documentation generator for GitHub projects. Verb is extremely powerful, easy to use, and is used on hundreds of projects of all sizes to generate everything from API docs to readmes.\")\n\n### Contributors\n\n| **Commits** | **Contributor** |  \n| --- | --- |  \n| 47 | [jonschlinkert](https://github.com/jonschlinkert) |  \n| 5  | [doowb](https://github.com/doowb) |  \n| 1  | [phated](https://github.com/phated) |  \n| 1  | [danhper](https://github.com/danhper) |  \n| 1  | [paulmillr](https://github.com/paulmillr) |  \n\n### Author\n\n**Jon Schlinkert**\n\n* [GitHub Profile](https://github.com/jonschlinkert)\n* [Twitter Profile](https://twitter.com/jonschlinkert)\n* [LinkedIn Profile](https://linkedin.com/in/jonschlinkert)\n\n### License\n\nCopyright © 2019, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT License](LICENSE).\n\n***\n\n_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.8.0, on March 27, 2019._", "maintainers": [{"name": "phated", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "time": {"modified": "2023-06-22T16:32:26.552Z", "created": "2014-12-18T10:42:32.979Z", "0.1.0": "2014-12-18T10:42:32.979Z", "0.2.0": "2014-12-18T18:59:26.170Z", "0.3.0": "2014-12-24T01:37:47.313Z", "1.0.0": "2015-01-13T10:48:33.051Z", "1.1.0": "2015-01-16T10:38:04.362Z", "1.1.1": "2015-02-20T11:25:17.297Z", "1.1.2": "2015-03-04T20:11:05.448Z", "1.1.3": "2015-03-04T20:11:47.875Z", "2.0.0": "2015-05-06T10:46:05.019Z", "2.0.1": "2015-10-02T04:34:41.446Z", "3.0.0": "2016-09-03T19:57:28.446Z", "3.1.0": "2016-10-12T22:37:07.741Z", "4.0.0": "2017-08-07T20:54:20.412Z", "4.0.1": "2019-03-27T22:26:35.952Z", "4.0.2": "2021-09-26T22:37:37.622Z", "4.0.3": "2021-09-29T16:52:47.977Z"}, "homepage": "https://github.com/micromatch/is-glob", "keywords": ["bash", "braces", "check", "exec", "expression", "extglob", "glob", "globbing", "globstar", "is", "match", "matches", "pattern", "regex", "regular", "string", "test"], "repository": {"type": "git", "url": "git+https://github.com/micromatch/is-glob.git"}, "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/micromatch/is-glob/issues"}, "license": "MIT", "readmeFilename": "README.md", "users": {"jonschlinkert": true, "youstrive": true, "chrisyipw": true, "laomu": true, "ninozhang": true, "flumpus-dev": true}, "contributors": [{"name": "<PERSON>", "url": "https://twitter.com/doowb"}, {"name": "<PERSON>", "url": "https://tuvistavie.com"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}]}