{"name": "es-set-tostringtag", "dist-tags": {"latest": "2.1.0"}, "versions": {"1.0.0": {"name": "es-set-tostringtag", "version": "1.0.0", "dependencies": {"get-intrinsic": "^1.1.3", "has-tostringtag": "^1.0.0"}, "devDependencies": {"aud": "^2.0.2", "has": "^1.0.3", "tape": "^5.6.1", "eslint": "=8.8.0", "evalmd": "^0.0.19", "npmignore": "^0.3.0", "in-publish": "^2.0.1", "auto-changelog": "^2.4.0", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.0.0"}, "dist": {"shasum": "5f073226190bb4684e4d6f945a5ea90e7aff9283", "tarball": "https://registry.npmjs.org/es-set-tostringtag/-/es-set-tostringtag-1.0.0.tgz", "fileCount": 7, "integrity": "sha512-izRTRsZgSP3EpSxPE6lSzyIYD+qghxjskRHetmy15DhhPxfTohYk1Gce7sMJ3Sr+KjhGPZesKMANBZGTTxYgEw==", "signatures": [{"sig": "MEUCICc2p/ziZwUlAX2+CPNMHZ1p/mppY7IPZID5YJ+U9r7HAiEA0T83N5CT94y62DM9IGIx5c7gYsQcbQIkMzG/VoKX7XI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7252, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjo0aYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqc9g//ZuZaV205fhQ5Xupsw0/GXazArnaXUXPGd0CTPYmxQN4gBHB3\r\nsKThsLjDfO+J2o2z2x3+TBVTd0ZUWXY4tsunC+YPfv+yFmQjPekhqUTS+CQB\r\nE33GQUl965FLDYID1hkPViDqOE4SlDBqaPOocKEUMcP+eCoRjMggyTV49kAT\r\nf6TbepCbYCqiCClurNPYYkK+X+ZV7ptvOOFO7razTnvTqVfvwHla6Xjs/AR8\r\nbB2z6qoxxkyG6OhxC2X5pgD2bGyGINpA8USeKdpKPXYOsmHf9wVYyit/tLEo\r\n0+AxavnscpDnMaOA0gztMVyyUVF/m9YWacn9OBTd+vv+JBRiwiwvTLFtxK/+\r\nLxnaRGgSr8eaBN96yzHpL1fYwx0YmYbDjaIuLn3+OB/anwEOglA4M4DVaIGp\r\nN9xupKJqJ/Bn3pI/4L3FiT4UZmP5kOs6rFhx3m0h7pC9pvkwidqLCgq+GFjn\r\nKXQx/5w1Y2GCZfk/Ttm+LRyIbuCJ2RMhNczXvT9vazDAelp+NUrcAEcorHlG\r\ntkyvB+BNdqNreuor60ZrR5Eg0AShBjJhhoJ/3yIa/BmziUva2dezoF+ySemc\r\nYNIVDvKfY3UPO2v1X0reUfwnwOi2bpJaYie9qD1WmhntIEe7Ou2TXjPe+uH/\r\nf8OIO/93pA7N6bImJTDqN7/if9c9suw1abI=\r\n=AiPx\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.4"}}, "2.0.0": {"name": "es-set-tostringtag", "version": "2.0.0", "dependencies": {"get-intrinsic": "^1.1.3", "has-tostringtag": "^1.0.0"}, "devDependencies": {"aud": "^2.0.2", "has": "^1.0.3", "tape": "^5.6.1", "eslint": "=8.8.0", "evalmd": "^0.0.19", "npmignore": "^0.3.0", "in-publish": "^2.0.1", "auto-changelog": "^2.4.0", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.0.0"}, "dist": {"shasum": "b33fdef554fb35a264fe022c1f095f1f2022fa85", "tarball": "https://registry.npmjs.org/es-set-tostringtag/-/es-set-tostringtag-2.0.0.tgz", "fileCount": 7, "integrity": "sha512-vZVAIWss0FcR/+a08s6e2/GjGjjYBCZJXDrOnj6l5kJCKhQvJs4cnVqUxkVepIhqHbKHm3uwOvPb8lRcqA3DSg==", "signatures": [{"sig": "MEUCIQC+h9bVwffoj+j7fJWvcdB12dHllqMDWeP7QdsH14VDpwIgU2yrFiCPy1sn2bQOg21/HMnZMjfhJfDQQ77zcF/LfeA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8536, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjo2zVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmphdw//dM8GdCNKocyeti3UYg/iuGoHE+bvBPSwo2aUytzpB7JR6qw/\r\nZWGgduVW/BX0rCCTenSYP25hRCMJ5IIhKwrpy770pVmznccA3luqb+GAi56B\r\n+wSbdfBP8MBH8pzf9dgBUUjSz6lh9a+AfsGCkadT4wwy8MV8CVIr9qIh2zVa\r\nCy0DtMficfH6o2SISN2ypKFwoCmyxkbdD7P7ilKtFjXwAyvnGWXpcqCLxQPR\r\nKruJF8bD0psan5RqT<PERSON><PERSON>6ik25ch7GP17MUQaplqq3Ng0kAqgZBZf4CzFEpZ\r\nTuWcx8oBPz/fBkL7yaEa6DSf9vR6b1XY3nodUlTEeVRC5EMpvvlK3goQNpmU\r\nsSxhRLO+lj04Ea9AELxb2K8A3DsWmxRBJp5C4/tDC/UTbKRdsQPS7/T424rh\r\nc2doBAqaP50sYplEdlGI5oYQ0hHKB/d/WwLnQzJXwXmkTilxsZCshnTVeY51\r\nkS6NIckl4JQBO5o39vGs3ENESPX1lgDMDmvWTHRemGc07V9UZBaMUX+ds5mc\r\nqOHnByqKKVzvq4POeTJ7Pn/Vv4wfAyx/jY5Q98/L1OTW3An7k/vM6fD1EDeB\r\nECIXyX3z0jXA8N0PMl3FeB0x6G6ypp4LJa/Oq3EnZYVqMXBHngShb8YGZx4i\r\notS3weLb/n4UJSNb/VIyn0tMWjui8IuAt9I=\r\n=HWFX\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.4"}}, "2.0.1": {"name": "es-set-tostringtag", "version": "2.0.1", "dependencies": {"has": "^1.0.3", "get-intrinsic": "^1.1.3", "has-tostringtag": "^1.0.0"}, "devDependencies": {"aud": "^2.0.2", "tape": "^5.6.1", "eslint": "=8.8.0", "evalmd": "^0.0.19", "npmignore": "^0.3.0", "in-publish": "^2.0.1", "auto-changelog": "^2.4.0", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.0.1"}, "dist": {"shasum": "338d502f6f674301d710b80c8592de8a15f09cd8", "tarball": "https://registry.npmjs.org/es-set-tostringtag/-/es-set-tostringtag-2.0.1.tgz", "fileCount": 7, "integrity": "sha512-g3OMbtlwY3QewlqAiMLI47KywjWZoEytKr8pf6iTC8uJq5bIAH52Z9pnQ8pVL6whrCto53JZDuUIsifGeLorTg==", "signatures": [{"sig": "MEYCIQC+cLx1Ahq8pA+d0p8a61AB/e3q/EAwQN8X/2t7pa5OsgIhAPrGwNBT275mwZzyAl1dggL/bc/GBAul1P4CFXi73iuu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8908, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjtx8DACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrajA//cEt4C4ZRMR9gLey87iWDD/XGJfrLmwEusDhDPrlTZqeF+afX\r\nFMjcuW4NgjN3Psbn63lJfDCOvzvKmK5pe/ZXOzkv3yzWe3VZmmbfrkxeZWyx\r\nJwPI9rwPvrgFiFLuIbRSbpMIqVqfjX+kwGe4rPLnLnbjsP01v/y16mw0R0YO\r\ndoCrIqqXYSGogPCi7zr7J17WiDBEfuApnADTOASOHhhb22GqttVeIW4vE7Nq\r\niWdaQqkTs6z4wZmS3dujGDSltsqGy53XVU1TgjgANZkCZqnGnVOcf3vAP/NR\r\nwRgnH/+Isbdv3gH3YmyqIYZQmDsqcWNn5Bmy6/iPcicGNkQZIDgsNiAUR/GZ\r\njQYLq1KVkBHF2eznlNm6rGAHDNA2Tp+9UWgiyJyBSaY8ffQiZwAouOFn8Szv\r\na2UMOl4imwgwZ88dNgEwg0OHjCvqmJ6UQtXAvpvw2zO5hlLQLGsiTJHRc/yp\r\nLjsw0c56j8IyE2B6A621uUokjsjmMOo+nndSd0+I35uFXo9ZZphWf3Z0rBKT\r\nf+oPT5PtyUVlbocNBhv2J/74m5vjD4OJOei9SCsx0rsICDmqBnKJWPLjl9Ln\r\npUMxaEtNyZsm54AzpnVrTqLtUi1zcbBfX7VsGJ57pbKt+HnhgoSbcddqJurt\r\nNkXQi9E86I1RMlIu2D1dXPUzeTtNYK6Ieos=\r\n=HzAb\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.4"}}, "2.0.2": {"name": "es-set-tostringtag", "version": "2.0.2", "dependencies": {"hasown": "^2.0.0", "get-intrinsic": "^1.2.2", "has-tostringtag": "^1.0.0"}, "devDependencies": {"aud": "^2.0.3", "tape": "^5.7.2", "eslint": "=8.8.0", "evalmd": "^0.0.19", "npmignore": "^0.3.0", "in-publish": "^2.0.1", "auto-changelog": "^2.4.0", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.1.0"}, "dist": {"shasum": "11f7cc9f63376930a5f20be4915834f4bc74f9c9", "tarball": "https://registry.npmjs.org/es-set-tostringtag/-/es-set-tostringtag-2.0.2.tgz", "fileCount": 7, "integrity": "sha512-<PERSON><PERSON><PERSON>yu<PERSON>Zt65P9D2D2vA/zqcI3G5xRsklm5N3xCwuiy+/vKy8i0ifdsQP1sLgO4tZDSCaQUSnmC48khknGMV3D2Q==", "signatures": [{"sig": "MEQCIFgDTq2MPPFCb9ihbq03OQZwHydYJjnGXTXmgycR6e/pAiAcaL6lgu7jq9+tS/6truURQK91uAc+k66yxvLoyjrY7g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9490}, "engines": {"node": ">= 0.4"}}, "2.0.3": {"name": "es-set-tostringtag", "version": "2.0.3", "dependencies": {"hasown": "^2.0.1", "get-intrinsic": "^1.2.4", "has-tostringtag": "^1.0.2"}, "devDependencies": {"aud": "^2.0.4", "tape": "^5.7.5", "eslint": "=8.8.0", "evalmd": "^0.0.19", "npmignore": "^0.3.1", "in-publish": "^2.0.1", "typescript": "^5.4.0-dev.20240220", "@types/tape": "^5.6.4", "auto-changelog": "^2.4.0", "@types/has-symbols": "^1.0.2", "safe-publish-latest": "^2.0.0", "@types/get-intrinsic": "^1.2.2", "@ljharb/eslint-config": "^21.1.0"}, "dist": {"shasum": "8bb60f0a440c2e4281962428438d58545af39777", "tarball": "https://registry.npmjs.org/es-set-tostringtag/-/es-set-tostringtag-2.0.3.tgz", "fileCount": 9, "integrity": "sha512-3T8uNMC3OQTHkFUsFq8r/BwAXLHvU/9O9mE0fBc/MY5iq/8H7ncvO947LmYA6ldWw9Uh8Yhf25zu6n7nML5QWQ==", "signatures": [{"sig": "MEUCIQCS3PfjKv2wpwZmR1FLXjq4VKJlbV/bzjDog+sSW7gkvAIgQeXu664upJOOI63INe2TIVsB0z3K2DtrEHwkcwe4yDo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13855}, "engines": {"node": ">= 0.4"}}, "2.1.0": {"name": "es-set-tostringtag", "version": "2.1.0", "dependencies": {"es-errors": "^1.3.0", "get-intrinsic": "^1.2.6", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2"}, "devDependencies": {"@arethetypeswrong/cli": "^0.17.2", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.3", "@types/get-intrinsic": "^1.2.3", "@types/has-symbols": "^1.0.2", "@types/tape": "^5.8.0", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "eslint": "=8.8.0", "evalmd": "^0.0.19", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "dist": {"integrity": "sha512-j6vWzfrGVfyXxge+O0x5sh6cvxAog0a/4Rdd2K36zCMV5eJ+/+tOAngRO8cODMNWbVRdVlmGZQL2YS3yR8bIUA==", "shasum": "f31dbbe0c183b00a6d26eb6325c810c0fd18bd4d", "tarball": "https://registry.npmjs.org/es-set-tostringtag/-/es-set-tostringtag-2.1.0.tgz", "fileCount": 10, "unpackedSize": 14544, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDRmqFa+JTxgKAgz1nksHw8sQblfewFR7P48hhVyG3jqgIhALdBnoT0nSf0xq5iqlWTccHPPypdv4/I9vTwxHEQpOLe"}]}, "engines": {"node": ">= 0.4"}}}, "modified": "2025-01-02T04:44:14.598Z"}