{"name": "resolve-from", "dist-tags": {"latest": "5.0.0"}, "versions": {"1.0.0": {"name": "resolve-from", "version": "1.0.0", "devDependencies": {"ava": "0.0.3"}, "dist": {"shasum": "dedda6f6f6346f0211ce4bc25aca0ca7826bb367", "tarball": "https://registry.npmjs.org/resolve-from/-/resolve-from-1.0.0.tgz", "integrity": "sha512-8oho8oGBe8Z3toBV9TeT0AdSx6TNTrWQRvwmkJlj6grJg2IiSt0Ci8r2fe+fhJfYdqLm0zyDYsGYSdjReaf4qA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFZlHVmcKY9yeUwPN+te7LJh112FYcpxZU4IGvzYer7yAiAE9PfMgzOCiBSo+hc9zrf8KF7/3osiadvojlu2sFKfZw=="}]}, "engines": {"node": ">=0.10.0"}}, "1.0.1": {"name": "resolve-from", "version": "1.0.1", "devDependencies": {"ava": "*", "xo": "*"}, "dist": {"shasum": "26cbfe935d1aeeeabb29bc3fe5aeb01e93d44226", "tarball": "https://registry.npmjs.org/resolve-from/-/resolve-from-1.0.1.tgz", "integrity": "sha512-kT10v4dhrlLNcnO084hEjvXCI1wUG9qZLoz2RogxqDQQYy7IxjI/iMUkOtQTNEh6rzHxvdQWHsJyel1pKOVCxg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCiFPYNd6HmGXjSSFtvLIEQ3EZZ9NUGNiFzVww/I1fX5AIgW66unL+VEagO0sNEi0sctaHY1dItR7O3hp13vzbQw50="}]}, "engines": {"node": ">=0.10.0"}}, "2.0.0": {"name": "resolve-from", "version": "2.0.0", "devDependencies": {"ava": "*", "xo": "*"}, "dist": {"shasum": "9480ab20e94ffa1d9e80a804c7ea147611966b57", "tarball": "https://registry.npmjs.org/resolve-from/-/resolve-from-2.0.0.tgz", "integrity": "sha512-qpFcKaXsq8+oRoLilkwyc7zHGF5i9Q2/25NIgLQQ/+VVv9rU4qvr6nXVAw1DsnXJyQkZsR4Ytfbtg5ehfcUssQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBOMts2OuBLdp4jRd+KOvP2IvZY+jvvmCS4w+wN+3saHAiEA+fcSIU6Tu7/TIqHtVL2uvIeNosDu3Sc2YwjEakXhL+M="}]}, "engines": {"node": ">=0.10.0"}}, "3.0.0": {"name": "resolve-from", "version": "3.0.0", "devDependencies": {"ava": "*", "xo": "*"}, "dist": {"shasum": "b22c7af7d9d6881bc8b6e653335eebcb0a188748", "tarball": "https://registry.npmjs.org/resolve-from/-/resolve-from-3.0.0.tgz", "integrity": "sha512-GnlH6vxLymXJNMBo7XP1fJIzBFbdYt49CuTwmB/6N53t+kMPRMFKz783LlQ4tv28XoQfMWinAJX6WCGf2IlaIw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCacMANR7ov42DWDpqlZIJ9j9mm5q42KZbRbXfbdtJ7JgIgTLmInrYvnv7wm5qLr6ZzYIilhyuU4eudr+Ua9OQlHwg="}]}, "engines": {"node": ">=4"}}, "4.0.0": {"name": "resolve-from", "version": "4.0.0", "devDependencies": {"ava": "*", "xo": "*"}, "dist": {"integrity": "sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==", "shasum": "4abcd852ad32dd7baabfe9b40e00a36db5f392e6", "tarball": "https://registry.npmjs.org/resolve-from/-/resolve-from-4.0.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFgqnTs3q1vjji4AAId3bd1CWGThpg9nbR4wgeiSLK4WAiAuJT1LNSXHw3OqLopFsWOw/A/XqpTvH553EzlWG4fXRw=="}]}, "engines": {"node": ">=4"}}, "5.0.0": {"name": "resolve-from", "version": "5.0.0", "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}, "dist": {"integrity": "sha512-qYg9KP24dD5qka9J47d0aVky0N+b4fTU89LN9iDnjB5waksiC49rvMB0PrUJQGoTmH50XPiqOvAjDfaijGxYZw==", "shasum": "c35225843df8f776df21c57557bc087e9dfdfc69", "tarball": "https://registry.npmjs.org/resolve-from/-/resolve-from-5.0.0.tgz", "fileCount": 5, "unpackedSize": 5824, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJctANCCRA9TVsSAnZWagAAk7AQAIcOATtZd/Q0bDCROwwr\ne0iFyIuXPepEyjRgUuflYglfxBa4zg8z6LEKV2PIlVBryh+nw3bv8HIb0I1+\n9CLHFLwRrmjoDAttsm5EU8wY9wqTGzxoY/n72jnq5Nekpw0ENKkXsElZHaiG\ny3abQy+PAI1jtNhlZwxYMPm5v43JnIscxLKIe2+D4Glfy05779edS7BrrPYw\nKLn8bWgijuc27FIXKwoRKvysyH7Rp+7A45UE2MoWemLuPIPoGv0HI9/qUgrW\n3M1wWqLU3jwPvfa8lshrJ4SESHRxZlOCqOStDJqRP8xV80Us4hTfiPDg9MHx\n2MKoAtB7X4dhaqWXg489O3pUzxNazl9euxFRFybq30O/S8git4b2v0ETHpEt\nPLJZBO4p6Qw8x26CjspGbHTJMbMgQsdvR18FiTvq5Pg74ZkXCDEAH5mQcLUr\nPofEpd/QwQGxfM+Y+/q4y4/Z4fKp7M+1fpbCLHwn5c5TIUNc253CvbXhy+Oc\nZNhafcMsudm2NRYQlMLnWnShztMes2wFk+XRFO1S8E2QWktBHh4s1FIoSuYY\nilRxr3BHtf4g8j5gFBorgUx0RyDMrStlNJQG89HQTtEewrOyboUeUfoJl1Un\nAscCCCLsiSjZNyHT/KZmgfMAJP1go8taMEzmfFDllsYy1rg6GJxQpftyA02V\nCkJD\r\n=95CN\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFdfeKkK7ahAmOwRJw9uMCzGkfH4+mLjxb+TuaoM5sWiAiEAy9YosWMTFN9wZVLj8bg13Xa0U9lnl6Hb5RvjkjeE5Ok="}]}, "engines": {"node": ">=8"}}}, "modified": "2023-06-22T16:33:46.245Z"}