{"_id": "side-channel-map", "_rev": "1-5f188f425fbcce8a5e59489da87c1989", "name": "side-channel-map", "dist-tags": {"latest": "1.0.1"}, "versions": {"1.0.0": {"name": "side-channel-map", "version": "1.0.0", "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "side-channel-map@1.0.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/side-channel-map#readme", "bugs": {"url": "https://github.com/ljharb/side-channel-map/issues"}, "dist": {"shasum": "8f63c67a61e3059df12add46a15053778a9bccee", "tarball": "https://registry.npmjs.org/side-channel-map/-/side-channel-map-1.0.0.tgz", "fileCount": 12, "integrity": "sha512-ShdR/lGJ1vZ3Wze14afyMcs/1U0PFlGeArnj+vchXl/dhF/Iuu3QmNDX0oc+YonAdBlnXbtVu7jkBbDTEe7slQ==", "signatures": [{"sig": "MEUCICVdMwpmDZP4k/4jGFpKk1kOT4bW+pHbsglQI31cEofeAiEA12Q6KrIMXIh8tZ4j/5Zpx847MT005C/U5HDLMeWwC2Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12971}, "main": "index.js", "types": "./index.d.ts", "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "e78de60e46e5f8289baa80fa61739afc8c744161", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only", "prelint": "evalmd README.md && eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "postlint": "tsc -p . && attw -P", "posttest": "npx npm@'>= 10.2' audit --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/side-channel-map.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Store information about any JS value in a side channel, using a Map", "directories": {}, "_nodeVersion": "23.3.0", "dependencies": {"es-errors": "^1.3.0", "call-bound": "^1.0.1", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"nyc": "^10.3.2", "tape": "^5.9.0", "eclint": "^2.8.1", "eslint": "=8.8.0", "evalmd": "^0.0.19", "encoding": "^0.1.13", "npmignore": "^0.3.1", "in-publish": "^2.0.1", "typescript": "next", "@types/tape": "^5.6.5", "auto-changelog": "^2.5.0", "@ljharb/tsconfig": "^0.2.2", "safe-publish-latest": "^2.0.0", "@types/get-intrinsic": "^1.2.3", "@arethetypeswrong/cli": "^0.17.1", "@ljharb/eslint-config": "^21.1.1", "@types/object-inspect": "^1.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/side-channel-map_1.0.0_1733858095239_0.****************", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.1": {"name": "side-channel-map", "version": "1.0.1", "description": "Store information about any JS value in a side channel, using a Map", "main": "index.js", "exports": {".": "./index.js", "./package.json": "./package.json"}, "types": "./index.d.ts", "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "prelint": "evalmd README.md && eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "lint": "eslint --ext=js,mjs .", "postlint": "tsc -p . && attw -P", "pretest": "npm run lint", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "npx npm@'>= 10.2' audit --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/side-channel-map.git"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/ljharb/side-channel-map/issues"}, "homepage": "https://github.com/ljharb/side-channel-map#readme", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3"}, "devDependencies": {"@arethetypeswrong/cli": "^0.17.1", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.2", "@types/get-intrinsic": "^1.2.3", "@types/object-inspect": "^1.13.0", "@types/tape": "^5.6.5", "auto-changelog": "^2.5.0", "eclint": "^2.8.1", "encoding": "^0.1.13", "eslint": "=8.8.0", "evalmd": "^0.0.19", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}, "engines": {"node": ">= 0.4"}, "_id": "side-channel-map@1.0.1", "gitHead": "c14f8ee78a05007b6604d88c6219fe4303e693a3", "_nodeVersion": "23.4.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-VCjCNfgMsby3tTdo02nbjtM/ewra6jPHmpThenkTYh8pG9ucZ/1P8So4u4FGBek/BjpOVsDCMoLA/iuBKIFXRA==", "shasum": "d6bb6b37902c6fef5174e5f533fab4c732a26f42", "tarball": "https://registry.npmjs.org/side-channel-map/-/side-channel-map-1.0.1.tgz", "fileCount": 12, "unpackedSize": 13348, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCXM4m2a9YmH7lhTHeXNi5qXGYmKlhviDZCKi/GXBwhQwIhAMD/7l6OGqj2phpFGTtuHtGCdumeQtNLVxDBZJYbJ164"}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/side-channel-map_1.0.1_1733892798714_0.23810029168277125"}, "_hasShrinkwrap": false}}, "time": {"created": "2024-12-10T19:14:55.238Z", "modified": "2024-12-11T04:53:19.134Z", "1.0.0": "2024-12-10T19:14:55.436Z", "1.0.1": "2024-12-11T04:53:18.943Z"}, "bugs": {"url": "https://github.com/ljharb/side-channel-map/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/ljharb/side-channel-map#readme", "keywords": [], "repository": {"type": "git", "url": "git+https://github.com/ljharb/side-channel-map.git"}, "description": "Store information about any JS value in a side channel, using a Map", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "readme": "# side-channel-map <sup>[![Version Badge][npm-version-svg]][package-url]</sup>\n\n[![github actions][actions-image]][actions-url]\n[![coverage][codecov-image]][codecov-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][npm-badge-png]][package-url]\n\nStore information about any JS value in a side channel, using a Map.\n\nWarning: if the `key` is an object, this implementation will leak memory until you `delete` it.\nUse [`side-channel`](https://npmjs.com/side-channel) for the best available strategy.\n\n## Getting started\n\n```sh\nnpm install --save side-channel-map\n```\n\n## Usage/Examples\n\n```js\nconst assert = require('assert');\nconst getSideChannelMap = require('side-channel-map');\n\nconst channel = getSideChannelMap();\n\nconst key = {};\nassert.equal(channel.has(key), false);\nassert.throws(() => channel.assert(key), TypeError);\n\nchannel.set(key, 42);\n\nchannel.assert(key); // does not throw\nassert.equal(channel.has(key), true);\nassert.equal(channel.get(key), 42);\n\nchannel.delete(key);\nassert.equal(channel.has(key), false);\nassert.throws(() => channel.assert(key), TypeError);\n```\n\n## Tests\n\nClone the repo, `npm install`, and run `npm test`\n\n[package-url]: https://npmjs.org/package/side-channel-map\n[npm-version-svg]: https://versionbadg.es/ljharb/side-channel-map.svg\n[deps-svg]: https://david-dm.org/ljharb/side-channel-map.svg\n[deps-url]: https://david-dm.org/ljharb/side-channel-map\n[dev-deps-svg]: https://david-dm.org/ljharb/side-channel-map/dev-status.svg\n[dev-deps-url]: https://david-dm.org/ljharb/side-channel-map#info=devDependencies\n[npm-badge-png]: https://nodei.co/npm/side-channel-map.png?downloads=true&stars=true\n[license-image]: https://img.shields.io/npm/l/side-channel-map.svg\n[license-url]: LICENSE\n[downloads-image]: https://img.shields.io/npm/dm/side-channel-map.svg\n[downloads-url]: https://npm-stat.com/charts.html?package=side-channel-map\n[codecov-image]: https://codecov.io/gh/ljharb/side-channel-map/branch/main/graphs/badge.svg\n[codecov-url]: https://app.codecov.io/gh/ljharb/side-channel-map/\n[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/ljharb/side-channel-map\n[actions-url]: https://github.com/ljharb/side-channel-map/actions\n", "readmeFilename": "README.md"}