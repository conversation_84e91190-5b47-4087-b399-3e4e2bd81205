{"_id": "@types/eslint-scope", "_rev": "546-97b4e51a62202719b0909f47c2d8fa6c", "name": "@types/eslint-scope", "dist-tags": {"ts2.8": "3.7.0", "ts2.7": "3.7.0", "ts2.9": "3.7.0", "ts3.5": "3.7.0", "ts3.4": "3.7.0", "ts3.3": "3.7.0", "ts3.2": "3.7.0", "ts3.1": "3.7.0", "ts3.0": "3.7.0", "ts2.2": "3.7.0", "ts2.3": "3.7.0", "ts2.4": "3.7.0", "ts2.5": "3.7.0", "ts2.6": "3.7.0", "ts3.7": "3.7.1", "ts3.6": "3.7.1", "ts3.9": "3.7.3", "ts3.8": "3.7.3", "ts4.4": "3.7.4", "ts4.2": "3.7.4", "ts4.3": "3.7.4", "ts4.1": "3.7.4", "ts4.0": "3.7.4", "ts5.9": "8.3.0", "ts4.5": "3.7.7", "ts4.6": "3.7.7", "ts4.7": "3.7.7", "ts4.8": "3.7.7", "ts4.9": "3.7.7", "ts5.0": "3.7.7", "ts5.7": "8.3.0", "ts5.2": "8.3.0", "ts5.8": "8.3.0", "ts5.6": "8.3.0", "ts5.3": "8.3.0", "ts5.1": "8.3.0", "ts5.5": "8.3.0", "ts5.4": "8.3.0", "latest": "8.3.0"}, "versions": {"3.7.0": {"name": "@types/eslint-scope", "version": "3.7.0", "license": "MIT", "_id": "@types/eslint-scope@3.7.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/mysticatea", "name": "<PERSON><PERSON>", "githubUsername": "mysticatea"}], "dist": {"shasum": "4792816e31119ebd506902a482caec4951fabd86", "tarball": "https://registry.npmjs.org/@types/eslint-scope/-/eslint-scope-3.7.0.tgz", "fileCount": 4, "integrity": "sha512-O/ql2+rrCUe2W2rs7wMR+GqPRcgB6UiqN5RhrR5xruFlY7l9YLMn0ZkDzjoHLeiFkR8MCQZVudUuuvQ2BLC9Qw==", "signatures": [{"sig": "MEYCIQCCq8REML92UcgXX9Vs9R1IGkKuYl2oriLnhLAT3swiMQIhAMIRdIadIjUtaHzWC4e+738GPbAC3S3g8/ROvgTw3f16", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4297}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for eslint-scope", "directories": {}, "dependencies": {"@types/eslint": "*", "@types/estree": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/eslint-scope_3.7.0_1518918850813_0.8793114696270767", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "0ea662a443d13a5db1cae5afc89fd2aec8155d09ddec7b1a5ba0c6ab4be4cc54"}, "3.7.1": {"name": "@types/eslint-scope", "version": "3.7.1", "license": "MIT", "_id": "@types/eslint-scope@3.7.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/mysticatea", "name": "<PERSON><PERSON>", "githubUsername": "mysticatea"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint-scope", "dist": {"shasum": "8dc390a7b4f9dd9f1284629efce982e41612116e", "tarball": "https://registry.npmjs.org/@types/eslint-scope/-/eslint-scope-3.7.1.tgz", "fileCount": 4, "integrity": "sha512-SCFeogqiptms4Fg29WpOTk5nHIzfpKCemSN63ksBQYKTcXoJEmJagV+DhVmbapZzY4/5YaOV1nZwrsU79fFm1g==", "signatures": [{"sig": "MEQCIAvECIVoquR7caQDFsmZuH892MILWA/l/mvhwW/jnWhtAiA+70sSrkq90niScpdJ7brdbATFaYwrxFMOIde55p+Glw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6771, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg5KsTCRA9TVsSAnZWagAA+yoP/2CDgJ2W3N0CA1y/nvM0\nPIo8/icF8fdblLXfN0XHQUm00z0d05F0iaIB3pJnt8fxO0Ua51RvMgxmnN1c\n8Z64+vRRmwUe9Zqs9c8pqAhkePJZyfmHkm3OUTT8WFHLjK4O6lY9OLinMZNK\nCIzEVkSFnefF0R5hzHeerugGmM7PVPf0Z+G4Rm6gJLqhwHoiSG7dg2u/j2d3\nCu6QwaylSHmST1tpEwPhP1QPuuiA8ULDsHnBmD093wMcY8MH0qeiVn0/g7MD\nsvpKVWQvU+5gCvu57Xi+Miqw1tK3Pk/jyCnLoE7nwoIf+Au2JrREvijXGAR9\nBgy8XniGr36otqeXVYdCXxTiXTiPc5qBCP3VCak31G+pTZpoUz4zkpI97jwu\nmBos2vuPaWvZUlgaBOWe4hYUpm6XcpwdbU+SuO0CZyU+w2GV/f+TmRILVzmF\nat9Ev50zN/n++XCFg140xgn6K3bRBw05vTjuDHhoUsJk9PFfKbqNb5QwQTsR\nbZCERoQDhYfpKOL3sa8sJg5aK5nhfJ7scrYVYzugQ90P/IFLLkF2BPwxB22y\nE1vdueNyYegqI/DDkLNVEMJbZu1JJd3hBCKNCfWY1EtCvJ5awIJ+4iyfLbzo\nR1N85ZPsx0VA8HHVRm0GW1jbEH/usoZwPSYp4Tx6opgHIVFZsfs6BEejuYdS\nD7i1\r\n=d+ZS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/eslint-scope"}, "description": "TypeScript definitions for eslint-scope", "directories": {}, "dependencies": {"@types/eslint": "*", "@types/estree": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.6", "_npmOperationalInternal": {"tmp": "tmp/eslint-scope_3.7.1_1625598739504_0.7464629623076626", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "47224c2bf9a37ca8eb542bd7399915133185a33ed6b71ef30985d3c8a9598e8c"}, "3.7.2": {"name": "@types/eslint-scope", "version": "3.7.2", "license": "MIT", "_id": "@types/eslint-scope@3.7.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/mysticatea", "name": "<PERSON><PERSON>", "githubUsername": "mysticatea"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint-scope", "dist": {"shasum": "11e96a868c67acf65bf6f11d10bb89ea71d5e473", "tarball": "https://registry.npmjs.org/@types/eslint-scope/-/eslint-scope-3.7.2.tgz", "fileCount": 4, "integrity": "sha512-TzgYCWoPiTeRg6RQYgtuW7iODtVoKu3RVL72k3WohqhjfaOLK5Mg2T4Tg1o2bSfu0vPkoI48wdQFv5b/Xe04wQ==", "signatures": [{"sig": "MEQCIHv0h9Eh3tkeGzG40Cc3k81kyl4hsAhDT56owLjS/PocAiABzbwpQs9TTwtKfEeFNkden3s0JS4M8chuWkJbuoWqqw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6774, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhu773CRA9TVsSAnZWagAA2MEQAKFVYqd3DH+d6M6fLM1O\n8e5prDUKinrzc+RpEsZgqkHp0A9qQ0sJq3p2wGgu8EEtXeWP1nQ8reuQh6XV\n2ZOjBXUEhYpQufMxvC1QbL3iVpcRLR+0v1GOvuiyAdt+JRes+EY3BAvR7VDj\nC8NdIsH6UrV9C/mZKbNg/xFZdvkjPnyJG5HzcYDYqAseHYVsZr2UfXuuknOb\ncpnMtnZf7DfzJ5uFSQspQsMDIe1rrwX/1PQPFzrzZuU9HF/2823zYPtnb7C+\nJJGSQ5cD+DyHZ8G3n2bJCc9zZvk5GFxbYwIQllbe9+wDuy2twMPSKarBWRGT\nUPPBz+dmP/lWWc7yOiLrS/ptJFU23FSniEe35LlbvwZ+Y/JeWY0ue3MbjrHQ\n+ZoIATwn95sFatg4O59nhn7rwOQ4IkOpRuQ7mYlIN8QBfGIBA9IBtyMCBcoP\nc8YfZW34lYTf99spMYN+RFsqlrvAO23EC/veKV0Mqp5kTmXWzuV6FU19jPCK\nWUR/69IM545ec5HSZJD2ZUzhwqe81ibRO3zHj2W3OLUw27Ub8prJz3xnAToo\n0jedDZeCf4mntouD4ldOri8deoShZZOdP8YDTgRZ6b/CiRgff1/ZX2A2G/K2\nMPyEh95dS1uT/zfvl6nvmjseAZlfTzK52Tmx7CK8DksET/IJyiIlLV///vy9\n5J89\r\n=bAyf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/eslint-scope"}, "description": "TypeScript definitions for eslint-scope", "directories": {}, "dependencies": {"@types/eslint": "*", "@types/estree": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.8", "_npmOperationalInternal": {"tmp": "tmp/eslint-scope_3.7.2_1639694071095_0.1316537256816448", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "ddd9eb00ca321b3db8649ea442046bb588fab3c97d940a91dff1ce3e8f836335"}, "3.7.3": {"name": "@types/eslint-scope", "version": "3.7.3", "license": "MIT", "_id": "@types/eslint-scope@3.7.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/mysticatea", "name": "<PERSON><PERSON>", "githubUsername": "mysticatea"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint-scope", "dist": {"shasum": "125b88504b61e3c8bc6f870882003253005c3224", "tarball": "https://registry.npmjs.org/@types/eslint-scope/-/eslint-scope-3.7.3.tgz", "fileCount": 4, "integrity": "sha512-PB3ldyrcnAicT35TWPs5IcwKD8S333HMaa2VVv4+wdvebJkjWuW/xESoB8IwRcog8HYVYamb1g/R31Qv5Bx03g==", "signatures": [{"sig": "MEYCIQCkJ5lHca/uF3rzTAmMVxvEwpozU/imr15VUwYPjXGBxAIhALCNTwm14ThfRPqiyzcLwK2FI7lEM/h0pSoDrPxMBEAj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6582, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh3J7hCRA9TVsSAnZWagAAKiUP/2MQATaL15HPxAGlyAe5\nAbNhq5Y2sZgzdtuF6V6jm8HCh6Pg+m03S4eNpfJ1ycwWiHqlpZB4dIbUEX6j\nCaUtYbfqpXk06XFMn2pkTXWNr6q+ehavsoS1QV5bScMh7HyI1PY+iId29fDm\n/Ly6ELwvnpX9ogWaCmxOQHZIGxDU2QA4S4GBaOgoRZn9rc0tNxTClz8+/kbn\neDKTVlveedjvyvHTeHvogb33GWYJFzT2pzH+CErfxpnpWDKU8pZIkKAdUqQx\nC/3zkg5f0suBpe5z4UYGN0QDvWQH1Lro8lQLMoo+WvBCc4y5Blb4vR27dMeq\nS6UVY/mPg0jqMbxm9Z/3dJVJoIOccWrfLJ2iWhHnobPI+5Os7FVpUPR9rHRn\nrcbd8V6CZuMFa5MwFC0RKFVVhrltmyW/LWBLVN1FN8iXOzwS3JEH6gbiOmRa\ntbY9iDu7uOyWVvXr7JubbS90ikIN6yqWe2xEVotxvRi/dNG68CBnRCLU/D/2\nBxUm1aP/9epz7AJ5vI/b06mpcFPj5Ishjc6g5QmeO5kfsCaQzhukWwxc8ztE\nCm8eDux88g6MgH35z2X70dcrYTdNY2wAlhpBP6ro+gIiEnKSRAPdVn/qbFFy\n1zdiQwFwPRO6EIV1abf+/Cuhl5FkAoEX+ijf0uNktBltb15kkPwHZQVIP6Rg\nZqVc\r\n=J2i/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/eslint-scope"}, "description": "TypeScript definitions for eslint-scope", "directories": {}, "dependencies": {"@types/eslint": "*", "@types/estree": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.8", "_npmOperationalInternal": {"tmp": "tmp/eslint-scope_3.7.3_1641848545048_0.8893843650401025", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "8e411ace0ab4265f36d35de1569d64466d2b58696d0afdd65620550a4762f1f4"}, "3.7.4": {"name": "@types/eslint-scope", "version": "3.7.4", "license": "MIT", "_id": "@types/eslint-scope@3.7.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/mysticatea", "name": "<PERSON><PERSON>", "githubUsername": "mysticatea"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint-scope", "dist": {"shasum": "37fc1223f0786c39627068a12e94d6e6fc61de16", "tarball": "https://registry.npmjs.org/@types/eslint-scope/-/eslint-scope-3.7.4.tgz", "fileCount": 5, "integrity": "sha512-9K<PERSON>zoImiZc3HlIp6AVUDE4CWYx22a+lhSZMYNpbjW04+YF0KWj4pJXnEMjdnFTiQibFFmElcsasJXDbdI/EPhA==", "signatures": [{"sig": "MEYCIQDAHk6GW4Hwc7d3OeEfHSexzzHtFfilFvQk+Xi4IYPfFQIhAO+orkggZgQk3L0LOsvNUt81P2tvyglm6o+FUQC9JSgC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6618, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJivfNcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoaow/+LOBFMICwey0TcwmL5qYPzIvBjRDPKPUyZRj0LTNcXW+HVtO1\r\nhXTz1MFOd3H97dvljSIeUa1urHhHqdouK6EqgEGPGnO8WoGfftRs7kTt5Bkz\r\nv6uMfiNE8hwSkmndN8hCt4dHtUICLuyfOuziXb9Aj3jR23NoN9Z2qldSHPMr\r\nKTJZAdHhL+lGSSzG6tqOMjccOFX+G0DA1mlbWFY7Q4GTXI7U4uUnPvPDIZlp\r\nBxLzuRl9klbqASRkceIbutbMRgQPZasqso6bQWjNePaUfhv6MnHtW2NddS2t\r\nhOj9D4hbD/pcAixQMGy3l2646JDzMm3RdgfbIdvjwEmypVdaN/vbR5YpiaHm\r\ncKBEZDBveKInrkaWDFLeyIRjPlcOUUdOcZ8ZPtL/JQs3WhEz6+ENGQvSHhG5\r\nzJPnKPqopC4VdZoxX4puNaQ/VGMQESgTxlxwiQQqkt8vN0+tGQ68eoJL/hls\r\nhlTbbtWF0idSiBHaKgphCrelwzjS1Mz4Ao83ZTqB7vIqxcIWsslnugDkcF8T\r\nhL/giWZLz/M6Zzk0ak4A/JT5XWFtO6JC/rqkdSrWpk2vjSui470tjVFSXTHb\r\n5bOhSaqiWG6T7UCV17E7q8RATQ5DWmkxbIbxdGpU2qSA7j3DTM5EkVHsuYe0\r\nYPk1MUJb4K/xXwi5wIWAQRmwRWn4T/R0wFA=\r\n=VYVR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/eslint-scope"}, "description": "TypeScript definitions for eslint-scope", "directories": {}, "dependencies": {"@types/eslint": "*", "@types/estree": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.0", "_npmOperationalInternal": {"tmp": "tmp/eslint-scope_3.7.4_1656615772682_0.12087072367486873", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "81c8e26e146b6b132a88bc06480ec59c5006561f35388cbc65756710cd486f05"}, "3.7.5": {"name": "@types/eslint-scope", "version": "3.7.5", "license": "MIT", "_id": "@types/eslint-scope@3.7.5", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/mysticatea", "name": "<PERSON><PERSON>", "githubUsername": "mysticatea"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint-scope", "dist": {"shasum": "e28b09dbb1d9d35fdfa8a884225f00440dfc5a3e", "tarball": "https://registry.npmjs.org/@types/eslint-scope/-/eslint-scope-3.7.5.tgz", "fileCount": 5, "integrity": "sha512-JNvhIEyxVW6EoMIFIvj93ZOywYFatlpu9deeH6eSx6PE3WHYvHaQtmHmQeNw7aA81bYGBPPQqdtBm6b1SsQMmA==", "signatures": [{"sig": "MEUCIQD9y42z6eOyvOdKSOaepai33rZfHXbUnsnRUnWMgg0eywIgeE28aDzl3GIqZVOOqjKD9BwC3OKqKeEl/1qsg3jMamo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6798}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/eslint-scope"}, "description": "TypeScript definitions for eslint-scope", "directories": {}, "dependencies": {"@types/eslint": "*", "@types/estree": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/eslint-scope_3.7.5_1695489748197_0.37661231879216905", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "82c0c1f865d0524fab33686434662a4276def24fa0ab46810285b480f58c8602"}, "3.7.6": {"name": "@types/eslint-scope", "version": "3.7.6", "license": "MIT", "_id": "@types/eslint-scope@3.7.6", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/mysticatea", "name": "<PERSON><PERSON>", "githubUsername": "mysticatea"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint-scope", "dist": {"shasum": "585578b368ed170e67de8aae7b93f54a1b2fdc26", "tarball": "https://registry.npmjs.org/@types/eslint-scope/-/eslint-scope-3.7.6.tgz", "fileCount": 5, "integrity": "sha512-zfM4ipmxVKWdxtDaJ3MP3pBurDXOCoyjvlpE3u6Qzrmw4BPbfm4/ambIeTk/r/J0iq/+2/xp0Fmt+gFvXJY2PQ==", "signatures": [{"sig": "MEUCIQDcmVo8+Jr2bhCVX9FN6kxcaqqOLEHsNWEBY9yr/NLL/gIgQTFOna+tUxbMiO/IchofKR2NJXaP6FCPSdkvFtE2rII=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6270}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/eslint-scope"}, "description": "TypeScript definitions for eslint-scope", "directories": {}, "dependencies": {"@types/eslint": "*", "@types/estree": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/eslint-scope_3.7.6_1697594269575_0.5003341173008", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "0e29a669987b6ef9f8c297adcb8b7116023228a89694a45a10863f9d087a1277"}, "3.7.7": {"name": "@types/eslint-scope", "version": "3.7.7", "license": "MIT", "_id": "@types/eslint-scope@3.7.7", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/mysticatea", "name": "<PERSON><PERSON>", "githubUsername": "mysticatea"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint-scope", "dist": {"shasum": "3108bd5f18b0cdb277c867b3dd449c9ed7079ac5", "tarball": "https://registry.npmjs.org/@types/eslint-scope/-/eslint-scope-3.7.7.tgz", "fileCount": 5, "integrity": "sha512-MzMFlSLBqNF2gcHWO0G1vP/YQyfvrxZ0bF+u7mzUdZ1/xK4A4sru+nraZz5i3iEIk1l1uyicaDVTB4QbbEkAYg==", "signatures": [{"sig": "MEQCICc4ujCHzzfdjcHfbL1GknaEVo4TZVPOt8O4sYl4XRrZAiAO7H+K0c4yZNpN8e8LH+4kJVlSXYU0FB16BDrwS9Npdw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6270}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/eslint-scope"}, "description": "TypeScript definitions for eslint-scope", "directories": {}, "dependencies": {"@types/eslint": "*", "@types/estree": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/eslint-scope_3.7.7_1699323750218_0.28011420985629254", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "49eee35b78c19e2c83bc96ce190c7a88329006f876dd7f1fb378c1e8034fc8f2"}, "8.3.0": {"name": "@types/eslint-scope", "version": "8.3.0", "license": "MIT", "_id": "@types/eslint-scope@8.3.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/mysticatea", "name": "<PERSON><PERSON>", "githubUsername": "mysticatea"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint-scope", "dist": {"shasum": "81bcb75433c1c3b531c4eab77aa1586fe383f8df", "tarball": "https://registry.npmjs.org/@types/eslint-scope/-/eslint-scope-8.3.0.tgz", "fileCount": 7, "integrity": "sha512-FaY/QEfIyGJzJdkObuvtaROKv7A0zArw+be0tgXfWd1s1/AqPzEbyf7eyK0Pg0YezUpKrSwK4kgBn/kjzQOjtQ==", "signatures": [{"sig": "MEUCIA85qNai1T8tSyozMJ+IGyhXFHiWuvcAtCr330xLkQh+AiEAwZ5PyviDU1amECbVHf+3YXaZACSffjpM2AB+SiYyNNQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 17148}, "main": "", "type": "module", "types": "index.d.ts", "exports": {".": {"import": "./index.d.mts", "require": "./index.d.cts"}, "./package.json": "./package.json"}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/eslint-scope"}, "description": "TypeScript definitions for eslint-scope", "directories": {}, "dependencies": {"@types/eslint": "*", "@types/estree": "*"}, "_hasShrinkwrap": false, "peerDependencies": {}, "typeScriptVersion": "5.1", "_npmOperationalInternal": {"tmp": "tmp/eslint-scope_8.3.0_1746556744559_0.29061721874896684", "host": "s3://npm-registry-packages-npm-production"}, "typesPublisherContentHash": "493bd42c43ed33601ef780070050b28e691cbd989aa478103e164a5298abd96f"}}, "time": {"created": "2018-02-18T01:54:10.733Z", "modified": "2025-05-06T18:39:11.757Z", "3.7.0": "2018-02-18T01:54:10.920Z", "3.7.1": "2021-07-06T19:12:19.634Z", "3.7.2": "2021-12-16T22:34:31.267Z", "3.7.3": "2022-01-10T21:02:25.202Z", "3.7.4": "2022-06-30T19:02:52.853Z", "3.7.5": "2023-09-23T17:22:28.391Z", "3.7.6": "2023-10-18T01:57:49.809Z", "3.7.7": "2023-11-07T02:22:30.403Z", "8.3.0": "2025-05-06T18:39:04.765Z"}, "license": "MIT", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint-scope", "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/eslint-scope"}, "description": "TypeScript definitions for eslint-scope", "contributors": [{"url": "https://github.com/mysticatea", "name": "<PERSON><PERSON>", "githubUsername": "mysticatea"}], "maintainers": [{"name": "types", "email": "<EMAIL>"}], "readme": "[object Object]", "readmeFilename": "", "users": {"flumpus-dev": true}}