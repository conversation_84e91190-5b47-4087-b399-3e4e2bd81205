{"_id": "tinyglobby", "_rev": "19-0dbe5c54820f0141d545fa3fef1aec0f", "name": "tinyglobby", "dist-tags": {"latest": "0.2.14"}, "versions": {"0.1.0": {"name": "tinyglobby", "version": "0.1.0", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"name": "Superchupu"}, "license": "ISC", "_id": "tinyglobby@0.1.0", "maintainers": [{"name": "superchupu", "email": "<EMAIL>"}], "homepage": "https://github.com/SuperchupuDev/tinyglobby#readme", "bugs": {"url": "https://github.com/SuperchupuDev/tinyglobby/issues"}, "dist": {"shasum": "ebb1ca007848d924bccd5dec34be30cce264f3a7", "tarball": "https://registry.npmjs.org/tinyglobby/-/tinyglobby-0.1.0.tgz", "fileCount": 9, "integrity": "sha512-8d+AJ9Q+O4dPb8XhBmJklsIyRXJoO3X8KTaBXXOwWZrNb+GxPVdqljmqvfYhmcKRNcbQ6M4cRvnF4LGOynwsiw==", "signatures": [{"sig": "MEUCIQCsQEU/ZBSBrKj2jR6LlGh765Np+/kA5TzlwRipZ6+4LgIgKxPgY360h/sB0otUDcFbfuBeEtpMQCBGosrh07ciTco=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tinyglobby@0.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 14678}, "main": "dist/index.js", "_from": "file:tinyglobby-0.1.0.tgz", "types": "dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=12.0.0"}, "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "scripts": {"lint": "biome lint", "test": "node --import tsx --test test/*.test.ts", "build": "tsup", "check": "biome check", "format": "biome format --write", "lint:fix": "biome lint --fix --unsafe", "typecheck": "tsc --noEmit"}, "_npmUser": {"name": "superchupu", "email": "<EMAIL>"}, "_resolved": "/tmp/690fd0b9ea0ae9b2bf9a000931bbffa5/tinyglobby-0.1.0.tgz", "_integrity": "sha512-8d+AJ9Q+O4dPb8XhBmJklsIyRXJoO3X8KTaBXXOwWZrNb+GxPVdqljmqvfYhmcKRNcbQ6M4cRvnF4LGOynwsiw==", "repository": {"url": "git+https://github.com/SuperchupuDev/tinyglobby.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A fast and minimal alternative to globby and fast-glob", "directories": {}, "_nodeVersion": "22.5.1", "dependencies": {"fdir": "^6.2.0", "picomatch": "^4.0.2"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "devDependencies": {"tsx": "^4.16.2", "tsup": "^8.2.2", "typescript": "^5.5.3", "@types/node": "^20.14.11", "@biomejs/biome": "^1.8.3", "@types/picomatch": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/tinyglobby_0.1.0_1721672373182_0.4747570157409138", "host": "s3://npm-registry-packages"}}, "0.1.1": {"name": "tinyglobby", "version": "0.1.1", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"name": "Superchupu"}, "license": "ISC", "_id": "tinyglobby@0.1.1", "maintainers": [{"name": "superchupu", "email": "<EMAIL>"}], "homepage": "https://github.com/SuperchupuDev/tinyglobby#readme", "bugs": {"url": "https://github.com/SuperchupuDev/tinyglobby/issues"}, "dist": {"shasum": "a9b2a1ab4cd086888f45d423660ce9cc2719966f", "tarball": "https://registry.npmjs.org/tinyglobby/-/tinyglobby-0.1.1.tgz", "fileCount": 9, "integrity": "sha512-DTTozc6Ce8ave4bttVQBUf7l4POgqgljzlVBuiJ8hNHuVESjYiZtVEpyRFoq0ripdtOxjlOU9oKy1oSPxsYfFQ==", "signatures": [{"sig": "MEYCIQDZ/94JvoA0/rPZ1uFZ0+0HQ4qqRMwD58HYjRL8BWeDLwIhAKExIcUQORYCWlzQIiTNgBSRpBlwVTNnxZSV8rGfjY3S", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tinyglobby@0.1.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 15919}, "main": "dist/index.js", "_from": "file:tinyglobby-0.1.1.tgz", "types": "dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=12.0.0"}, "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "scripts": {"lint": "biome lint", "test": "node --import tsx --test test/*.test.ts", "build": "tsup", "check": "biome check", "format": "biome format --write", "lint:fix": "biome lint --fix --unsafe", "typecheck": "tsc --noEmit"}, "_npmUser": {"name": "superchupu", "email": "<EMAIL>"}, "_resolved": "/tmp/fcd9f0ea61c407cd2963466f9a81aeda/tinyglobby-0.1.1.tgz", "_integrity": "sha512-DTTozc6Ce8ave4bttVQBUf7l4POgqgljzlVBuiJ8hNHuVESjYiZtVEpyRFoq0ripdtOxjlOU9oKy1oSPxsYfFQ==", "repository": {"url": "git+https://github.com/SuperchupuDev/tinyglobby.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A fast and minimal alternative to globby and fast-glob", "directories": {}, "_nodeVersion": "22.5.1", "dependencies": {"fdir": "^6.2.0", "picomatch": "^4.0.2"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "devDependencies": {"tsx": "^4.16.2", "tsup": "^8.2.2", "typescript": "^5.5.3", "@types/node": "^20.14.11", "@biomejs/biome": "^1.8.3", "@types/picomatch": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/tinyglobby_0.1.1_1721676096586_0.982126401660667", "host": "s3://npm-registry-packages"}}, "0.1.2": {"name": "tinyglobby", "version": "0.1.2", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"name": "Superchupu"}, "license": "ISC", "_id": "tinyglobby@0.1.2", "maintainers": [{"name": "superchupu", "email": "<EMAIL>"}], "homepage": "https://github.com/SuperchupuDev/tinyglobby#readme", "bugs": {"url": "https://github.com/SuperchupuDev/tinyglobby/issues"}, "dist": {"shasum": "ebb67fce47f865ab74f2dec887347c81a50c591e", "tarball": "https://registry.npmjs.org/tinyglobby/-/tinyglobby-0.1.2.tgz", "fileCount": 9, "integrity": "sha512-dCTuCKyovvOdzcdtU8ovtfY3w4OWcRO/h31kim9UV47GFwEqTCCgILZNiF6Kx4+ItjyxX+7lUF2LwGIJjBF9jQ==", "signatures": [{"sig": "MEUCIQDdVESL7u7tma9AwfqutgdNjJDP5L1dpCmdT114pECXUwIgdEmmsgMYrbbbFr9R9PA0BJSaZvxaNDU43FSDQrjtRTA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tinyglobby@0.1.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 16909}, "main": "dist/index.js", "_from": "file:tinyglobby-0.1.2.tgz", "types": "dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=12.0.0"}, "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "scripts": {"lint": "biome lint", "test": "node --import tsx --test test/*.test.ts", "build": "tsup", "check": "biome check", "format": "biome format --write", "lint:fix": "biome lint --fix --unsafe", "typecheck": "tsc --noEmit"}, "_npmUser": {"name": "superchupu", "email": "<EMAIL>"}, "_resolved": "/tmp/aad2a994ac751fea54c3bf21b40f47dc/tinyglobby-0.1.2.tgz", "_integrity": "sha512-dCTuCKyovvOdzcdtU8ovtfY3w4OWcRO/h31kim9UV47GFwEqTCCgILZNiF6Kx4+ItjyxX+7lUF2LwGIJjBF9jQ==", "repository": {"url": "git+https://github.com/SuperchupuDev/tinyglobby.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A fast and minimal alternative to globby and fast-glob", "directories": {}, "_nodeVersion": "22.5.1", "dependencies": {"fdir": "^6.2.0", "picomatch": "^4.0.2"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "devDependencies": {"tsx": "^4.16.2", "tsup": "^8.2.2", "typescript": "^5.5.3", "@types/node": "^20.14.11", "@biomejs/biome": "^1.8.3", "@types/picomatch": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/tinyglobby_0.1.2_1721685034568_0.6056628469510184", "host": "s3://npm-registry-packages"}}, "0.2.0": {"name": "tinyglobby", "version": "0.2.0", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"name": "Superchupu"}, "license": "ISC", "_id": "tinyglobby@0.2.0", "maintainers": [{"name": "superchupu", "email": "<EMAIL>"}], "homepage": "https://github.com/SuperchupuDev/tinyglobby#readme", "bugs": {"url": "https://github.com/SuperchupuDev/tinyglobby/issues"}, "dist": {"shasum": "a9d9ef839f87a505936a31882b1e8564c86a972f", "tarball": "https://registry.npmjs.org/tinyglobby/-/tinyglobby-0.2.0.tgz", "fileCount": 9, "integrity": "sha512-+clyYQfAnNlt5a1x7CCQ6RLuTIztDfDAl6mAANvqRUlz6sVy5znCzJOhais8G6oyUyoeeaorLopO3HptVP8niA==", "signatures": [{"sig": "MEYCIQDUQgHCBlItWao0RbDKzTQ+ws76CfLwmmQKqw87Umlp1QIhAMtKKGO9EkuYvt3xJ/+gISXgs++QjXBbJMNmhKMdy8Df", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tinyglobby@0.2.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 23629}, "main": "dist/index.js", "_from": "file:tinyglobby-0.2.0.tgz", "types": "dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=12.0.0"}, "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "scripts": {"lint": "biome lint", "test": "node --import tsx --test test/*.test.ts", "build": "tsup", "check": "biome check", "format": "biome format --write", "lint:fix": "biome lint --fix --unsafe", "typecheck": "tsc --noEmit"}, "_npmUser": {"name": "superchupu", "email": "<EMAIL>"}, "_resolved": "/tmp/055b7305503871e8d3bf82056bbff112/tinyglobby-0.2.0.tgz", "_integrity": "sha512-+clyYQfAnNlt5a1x7CCQ6RLuTIztDfDAl6mAANvqRUlz6sVy5znCzJOhais8G6oyUyoeeaorLopO3HptVP8niA==", "repository": {"url": "git+https://github.com/SuperchupuDev/tinyglobby.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A fast and minimal alternative to globby and fast-glob", "directories": {}, "_nodeVersion": "22.5.1", "dependencies": {"fdir": "^6.2.0", "picomatch": "^4.0.2"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "devDependencies": {"tsx": "^4.16.2", "tsup": "^8.2.2", "typescript": "^5.5.3", "@types/node": "^20.14.11", "@biomejs/biome": "^1.8.3", "@types/picomatch": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/tinyglobby_0.2.0_1721830039724_0.4383979732144532", "host": "s3://npm-registry-packages"}}, "0.2.1": {"name": "tinyglobby", "version": "0.2.1", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"name": "Superchupu"}, "license": "ISC", "_id": "tinyglobby@0.2.1", "maintainers": [{"name": "superchupu", "email": "<EMAIL>"}], "homepage": "https://github.com/SuperchupuDev/tinyglobby#readme", "bugs": {"url": "https://github.com/SuperchupuDev/tinyglobby/issues"}, "dist": {"shasum": "d6147f5c5c918120634ebb689a3de745465908da", "tarball": "https://registry.npmjs.org/tinyglobby/-/tinyglobby-0.2.1.tgz", "fileCount": 9, "integrity": "sha512-9P+ZLlLTDTjUxMw9aPgWJfBgYiUmW4Ki5XYoaFFwNPgXt4uRDdQOiatse8s/K64DjHNWE94FBe59lJ0oAeCWuQ==", "signatures": [{"sig": "MEUCIFE9iKEKlcyYI65sL8PJYklaaw/95Ha4RGoYO8czB2BWAiEAueWHcJI1M0TyyKNLSZQzmJ5nJqOf+EdD9YbYelvMLhA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tinyglobby@0.2.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 24403}, "main": "dist/index.js", "_from": "file:tinyglobby-0.2.1.tgz", "types": "dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=12.0.0"}, "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "scripts": {"lint": "biome lint", "test": "tsx --test", "build": "tsup", "check": "biome check", "format": "biome format --write", "lint:fix": "biome lint --fix --unsafe", "typecheck": "tsc --noEmit"}, "_npmUser": {"name": "superchupu", "email": "<EMAIL>"}, "_resolved": "/tmp/cb2f58a8bcb473f404d6170bab6aa627/tinyglobby-0.2.1.tgz", "_integrity": "sha512-9P+ZLlLTDTjUxMw9aPgWJfBgYiUmW4Ki5XYoaFFwNPgXt4uRDdQOiatse8s/K64DjHNWE94FBe59lJ0oAeCWuQ==", "repository": {"url": "git+https://github.com/SuperchupuDev/tinyglobby.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A fast and minimal alternative to globby and fast-glob", "directories": {}, "_nodeVersion": "22.5.1", "dependencies": {"fdir": "^6.2.0", "picomatch": "^4.0.2"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "devDependencies": {"tsx": "^4.16.2", "tsup": "^8.2.2", "typescript": "^5.5.3", "@types/node": "^20.14.11", "@biomejs/biome": "^1.8.3", "@types/picomatch": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/tinyglobby_0.2.1_1722765141050_0.44671550594455667", "host": "s3://npm-registry-packages"}}, "0.2.2": {"name": "tinyglobby", "version": "0.2.2", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"name": "Superchupu"}, "license": "ISC", "_id": "tinyglobby@0.2.2", "maintainers": [{"name": "superchupu", "email": "<EMAIL>"}], "homepage": "https://github.com/SuperchupuDev/tinyglobby#readme", "bugs": {"url": "https://github.com/SuperchupuDev/tinyglobby/issues"}, "dist": {"shasum": "c3b21f177d41a3c86b122cfbd3dd618870f3689f", "tarball": "https://registry.npmjs.org/tinyglobby/-/tinyglobby-0.2.2.tgz", "fileCount": 9, "integrity": "sha512-mZ2sDMaySvi1PkTp4lTo1In2zjU+cY8OvZsfwrDrx3YGRbXPX1/cbPwCR9zkm3O/Fz9Jo0F1HNgIQ1b8BepqyQ==", "signatures": [{"sig": "MEYCIQDgjtUaf6DFBnfq5NleIrYR6mUiiOt3E8H6vuYGrsi38wIhAIjU/p+Qob4l/HkZpiZlu9NY5rhY2dgLcnHOJZdPqPfz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tinyglobby@0.2.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 26384}, "main": "dist/index.js", "_from": "file:tinyglobby-0.2.2.tgz", "types": "dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=12.0.0"}, "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "scripts": {"lint": "biome lint", "test": "tsx --test", "build": "tsup", "check": "biome check", "format": "biome format --write", "lint:fix": "biome lint --fix --unsafe", "typecheck": "tsc --noEmit"}, "_npmUser": {"name": "superchupu", "email": "<EMAIL>"}, "_resolved": "/tmp/8b9d6242e92134bf1f30fa55fa02bb27/tinyglobby-0.2.2.tgz", "_integrity": "sha512-mZ2sDMaySvi1PkTp4lTo1In2zjU+cY8OvZsfwrDrx3YGRbXPX1/cbPwCR9zkm3O/Fz9Jo0F1HNgIQ1b8BepqyQ==", "repository": {"url": "git+https://github.com/SuperchupuDev/tinyglobby.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A fast and minimal alternative to globby and fast-glob", "directories": {}, "sideEffects": false, "_nodeVersion": "22.5.1", "dependencies": {"fdir": "^6.2.0", "picomatch": "^4.0.2"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "devDependencies": {"tsx": "^4.16.2", "tsup": "^8.2.2", "typescript": "^5.5.3", "@types/node": "^20.14.11", "@biomejs/biome": "^1.8.3", "@types/picomatch": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/tinyglobby_0.2.2_1722959469117_0.7638349740510866", "host": "s3://npm-registry-packages"}}, "0.2.3": {"name": "tinyglobby", "version": "0.2.3", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"name": "Superchupu"}, "license": "ISC", "_id": "tinyglobby@0.2.3", "maintainers": [{"name": "superchupu", "email": "<EMAIL>"}], "homepage": "https://github.com/SuperchupuDev/tinyglobby#readme", "bugs": {"url": "https://github.com/SuperchupuDev/tinyglobby/issues"}, "dist": {"shasum": "841ef6d6c039eaca6cc2c0d4850df82f4c0acf0e", "tarball": "https://registry.npmjs.org/tinyglobby/-/tinyglobby-0.2.3.tgz", "fileCount": 9, "integrity": "sha512-JvE0yT/RJulHtCPZG0tII18fufosZzNzRn2UFpcZNRScpN7ayko6tKIxa+dCSyPYig5gxz3mM4ReOlSvHNwzSA==", "signatures": [{"sig": "MEUCIQD9yGCVyhKJDvPfubRB/npvjlhlVAUBvvT6YhZyE3RFUgIgLh49NtvW7kyeR27cSEqOLp9MJKnh8j55RFAKTRMWflk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tinyglobby@0.2.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 37976}, "main": "dist/index.js", "_from": "file:tinyglobby-0.2.3.tgz", "types": "dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=12.0.0"}, "exports": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "scripts": {"lint": "biome lint", "test": "node --experimental-transform-types --test test/*.test.ts", "build": "tsup", "check": "biome check", "format": "biome format --write", "lint:fix": "biome lint --fix --unsafe", "test:cov": "node --experimental-transform-types --test --experimental-test-coverage test/*.test.ts", "test:only": "node --experimental-transform-types --test --test-only test/*.test.ts", "typecheck": "tsc --noEmit"}, "_npmUser": {"name": "superchupu", "email": "<EMAIL>"}, "_resolved": "/tmp/05deb23a2d577b70b3afaea9b69dc0a1/tinyglobby-0.2.3.tgz", "_integrity": "sha512-JvE0yT/RJulHtCPZG0tII18fufosZzNzRn2UFpcZNRScpN7ayko6tKIxa+dCSyPYig5gxz3mM4ReOlSvHNwzSA==", "deprecated": "Versions 0.2.3 and 0.2.4 contain a critical bug. Please upgrade to 0.2.5 or higher", "repository": {"url": "git+https://github.com/SuperchupuDev/tinyglobby.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A fast and minimal alternative to globby and fast-glob", "directories": {}, "sideEffects": false, "_nodeVersion": "22.7.0", "dependencies": {"fdir": "^6.2.0", "picomatch": "^4.0.2"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "devDependencies": {"tsup": "^8.2.4", "typescript": "^5.5.4", "@types/node": "^22.5.0", "@biomejs/biome": "^1.8.3", "@types/picomatch": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/tinyglobby_0.2.3_1724501662766_0.5299984526276609", "host": "s3://npm-registry-packages"}}, "0.2.4": {"name": "tinyglobby", "version": "0.2.4", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"name": "Superchupu"}, "license": "ISC", "_id": "tinyglobby@0.2.4", "maintainers": [{"name": "superchupu", "email": "<EMAIL>"}], "homepage": "https://github.com/SuperchupuDev/tinyglobby#readme", "bugs": {"url": "https://github.com/SuperchupuDev/tinyglobby/issues"}, "dist": {"shasum": "a720586c21ed8fba5f485bd5dfbb0d84e3641179", "tarball": "https://registry.npmjs.org/tinyglobby/-/tinyglobby-0.2.4.tgz", "fileCount": 9, "integrity": "sha512-h2O9NpZ4xnq0HIcsS1+5d9CzKSThHSmAO4kMGFMD9rTzUfZWjvMVVj8qq+wpO4l7+xb3YcjtXr5z6N0W0EDPvw==", "signatures": [{"sig": "MEUCICaQQI3OXjmnM/BPtWJysVbowjPOwNhuqCp3uClbCW8zAiEAy/1F8QjKo1SKJjg1ksoKKmSodjhauPJ+x+S393cJPLg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tinyglobby@0.2.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 38286}, "main": "dist/index.js", "_from": "file:tinyglobby-0.2.4.tgz", "types": "dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=12.0.0"}, "exports": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "scripts": {"lint": "biome lint", "test": "node --experimental-transform-types --test test/*.test.ts", "build": "tsup", "check": "biome check", "format": "biome format --write", "lint:fix": "biome lint --fix --unsafe", "test:cov": "node --experimental-transform-types --test --experimental-test-coverage test/*.test.ts", "test:only": "node --experimental-transform-types --test --test-only test/*.test.ts", "typecheck": "tsc --noEmit"}, "_npmUser": {"name": "superchupu", "email": "<EMAIL>"}, "_resolved": "/tmp/03c9cb4ae3e99f4485373c8325eecfa0/tinyglobby-0.2.4.tgz", "_integrity": "sha512-h2O9NpZ4xnq0HIcsS1+5d9CzKSThHSmAO4kMGFMD9rTzUfZWjvMVVj8qq+wpO4l7+xb3YcjtXr5z6N0W0EDPvw==", "deprecated": "Versions 0.2.3 and 0.2.4 contain a critical bug. Please upgrade to 0.2.5 or higher", "repository": {"url": "git+https://github.com/SuperchupuDev/tinyglobby.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A fast and minimal alternative to globby and fast-glob", "directories": {}, "sideEffects": false, "_nodeVersion": "22.7.0", "dependencies": {"fdir": "^6.2.0", "picomatch": "^4.0.2"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "devDependencies": {"tsup": "^8.2.4", "typescript": "^5.5.4", "@types/node": "^22.5.0", "@biomejs/biome": "^1.8.3", "@types/picomatch": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/tinyglobby_0.2.4_1724505575036_0.9521230784953789", "host": "s3://npm-registry-packages"}}, "0.2.5": {"name": "tinyglobby", "version": "0.2.5", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"name": "Superchupu"}, "license": "ISC", "_id": "tinyglobby@0.2.5", "maintainers": [{"name": "superchupu", "email": "<EMAIL>"}], "homepage": "https://github.com/SuperchupuDev/tinyglobby#readme", "bugs": {"url": "https://github.com/SuperchupuDev/tinyglobby/issues"}, "dist": {"shasum": "8cdd1df1b155bf2a3c4d5ea2581489f967a38318", "tarball": "https://registry.npmjs.org/tinyglobby/-/tinyglobby-0.2.5.tgz", "fileCount": 9, "integrity": "sha512-Dlqgt6h0QkoHttG53/WGADNh9QhcjCAIZMTERAVhdpmIBEejSuLI9ZmGKWzB7tweBjlk30+s/ofi4SLmBeTYhw==", "signatures": [{"sig": "MEYCIQCfI1f/DSfgJC20LRDJachSs3n8zqQes0m9ifsNId1QNgIhAJ29Gc2YUuUkrkFoXgWyHCJc75HPKS2qfxqT3ufHcn7O", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tinyglobby@0.2.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 38274}, "main": "dist/index.js", "_from": "file:tinyglobby-0.2.5.tgz", "types": "dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=12.0.0"}, "exports": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "scripts": {"lint": "biome lint", "test": "node --experimental-transform-types --test test/*.test.ts", "build": "tsup", "check": "biome check", "format": "biome format --write", "lint:fix": "biome lint --fix --unsafe", "test:cov": "node --experimental-transform-types --test --experimental-test-coverage test/*.test.ts", "test:only": "node --experimental-transform-types --test --test-only test/*.test.ts", "typecheck": "tsc --noEmit"}, "_npmUser": {"name": "superchupu", "email": "<EMAIL>"}, "_resolved": "/tmp/d886568dbcfe88f25df7bb873d90c9c7/tinyglobby-0.2.5.tgz", "_integrity": "sha512-Dlqgt6h0QkoHttG53/WGADNh9QhcjCAIZMTERAVhdpmIBEejSuLI9ZmGKWzB7tweBjlk30+s/ofi4SLmBeTYhw==", "repository": {"url": "git+https://github.com/SuperchupuDev/tinyglobby.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A fast and minimal alternative to globby and fast-glob", "directories": {}, "sideEffects": false, "_nodeVersion": "22.7.0", "dependencies": {"fdir": "^6.2.0", "picomatch": "^4.0.2"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "devDependencies": {"tsup": "^8.2.4", "typescript": "^5.5.4", "@types/node": "^22.5.0", "@biomejs/biome": "^1.8.3", "@types/picomatch": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/tinyglobby_0.2.5_1724507687772_0.5672911025086269", "host": "s3://npm-registry-packages"}}, "0.2.6": {"name": "tinyglobby", "version": "0.2.6", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"name": "Superchupu"}, "license": "ISC", "_id": "tinyglobby@0.2.6", "maintainers": [{"name": "superchupu", "email": "<EMAIL>"}], "homepage": "https://github.com/SuperchupuDev/tinyglobby#readme", "bugs": {"url": "https://github.com/SuperchupuDev/tinyglobby/issues"}, "dist": {"shasum": "950baf1462d0c0b443bc3d754d0d39c2e589aaae", "tarball": "https://registry.npmjs.org/tinyglobby/-/tinyglobby-0.2.6.tgz", "fileCount": 9, "integrity": "sha512-NbBoFBpqfcgd1tCiO8Lkfdk+xrA7mlLR9zgvZcZWQQwU63XAfUePyd6wZBaU93Hqw347lHnwFzttAkemHzzz4g==", "signatures": [{"sig": "MEUCIQDRT8Z00WUYvS400yal7FRc/0HB/lwXtEoQzNWT+4WgfgIgWgqt1vIyitkfQ4AQEwuAbyWaVXtCVWzbY5mlE4oRV/w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tinyglobby@0.2.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 39458}, "main": "dist/index.js", "_from": "file:tinyglobby-0.2.6.tgz", "types": "dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=12.0.0"}, "exports": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "scripts": {"lint": "biome lint", "test": "node --experimental-transform-types --test test/*.test.ts", "build": "tsup", "check": "biome check", "format": "biome format --write", "lint:fix": "biome lint --fix --unsafe", "test:only": "node --experimental-transform-types --test --test-only test/*.test.ts", "typecheck": "tsc --noEmit", "test:coverage": "node --experimental-transform-types --test --experimental-test-coverage test/*.test.ts"}, "_npmUser": {"name": "superchupu", "email": "<EMAIL>"}, "_resolved": "/tmp/11c7093df29e1bca57c8d22e0aa1a57e/tinyglobby-0.2.6.tgz", "_integrity": "sha512-NbBoFBpqfcgd1tCiO8Lkfdk+xrA7mlLR9zgvZcZWQQwU63XAfUePyd6wZBaU93Hqw347lHnwFzttAkemHzzz4g==", "repository": {"url": "git+https://github.com/SuperchupuDev/tinyglobby.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A fast and minimal alternative to globby and fast-glob", "directories": {}, "sideEffects": false, "_nodeVersion": "22.8.0", "dependencies": {"fdir": "^6.3.0", "picomatch": "^4.0.2"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "devDependencies": {"tsup": "^8.2.4", "fs-fixture": "^2.4.0", "typescript": "^5.5.4", "@types/node": "^22.5.4", "@biomejs/biome": "^1.8.3", "@types/picomatch": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/tinyglobby_0.2.6_1725836529530_0.5881396047599301", "host": "s3://npm-registry-packages"}}, "0.2.7": {"name": "tinyglobby", "version": "0.2.7", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"name": "Superchupu"}, "license": "MIT", "_id": "tinyglobby@0.2.7", "maintainers": [{"name": "superchupu", "email": "<EMAIL>"}], "homepage": "https://github.com/SuperchupuDev/tinyglobby#readme", "bugs": {"url": "https://github.com/SuperchupuDev/tinyglobby/issues"}, "dist": {"shasum": "36f583c166ac2d91f47c799d6e2e256fdcfb3a56", "tarball": "https://registry.npmjs.org/tinyglobby/-/tinyglobby-0.2.7.tgz", "fileCount": 7, "integrity": "sha512-qFWYeNxBQxrOTRHvGjlRdBamy8JFqu6c0bwRru9leE+q8J72tLtlT0L3v+2T7fbLXN7FGzDNBhXkWiJqHUHD9g==", "signatures": [{"sig": "MEUCIQCBKgHMLc9GnINUaFINoWHfzoctIeRGGiNnDgd0qgLgOwIgSVPGvpqiIS9ecmqlWGERpOwR6NA5XPe0sPOWm7Luiic=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tinyglobby@0.2.7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 23531}, "main": "dist/index.js", "_from": "file:tinyglobby-0.2.7.tgz", "types": "dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=12.0.0"}, "exports": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "scripts": {"lint": "biome lint", "test": "node --experimental-transform-types --test test/**/*.test.ts", "build": "tsup", "check": "biome check", "format": "biome format --write", "lint:fix": "biome lint --fix --unsafe", "test:only": "node --experimental-transform-types --test --test-only test/**/*.test.ts", "typecheck": "tsc --noEmit", "test:coverage": "node --experimental-transform-types --test --experimental-test-coverage test/**/*.test.ts"}, "_npmUser": {"name": "superchupu", "email": "<EMAIL>"}, "_resolved": "/tmp/4e9981c4f8494270c6c15951e94ec12d/tinyglobby-0.2.7.tgz", "_integrity": "sha512-qFWYeNxBQxrOTRHvGjlRdBamy8JFqu6c0bwRru9leE+q8J72tLtlT0L3v+2T7fbLXN7FGzDNBhXkWiJqHUHD9g==", "repository": {"url": "git+https://github.com/SuperchupuDev/tinyglobby.git", "type": "git"}, "_npmVersion": "10.8.3", "description": "A fast and minimal alternative to globby and fast-glob", "directories": {}, "sideEffects": false, "_nodeVersion": "22.9.0", "dependencies": {"fdir": "^6.4.0", "picomatch": "^4.0.2"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "devDependencies": {"tsup": "^8.3.0", "fs-fixture": "^2.4.0", "typescript": "^5.6.2", "@types/node": "^22.7.4", "@biomejs/biome": "^1.9.2", "@types/picomatch": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/tinyglobby_0.2.7_1727699426390_0.6174635813713891", "host": "s3://npm-registry-packages"}}, "0.2.8": {"name": "tinyglobby", "version": "0.2.8", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"name": "Superchupu"}, "license": "MIT", "_id": "tinyglobby@0.2.8", "maintainers": [{"name": "superchupu", "email": "<EMAIL>"}], "homepage": "https://github.com/SuperchupuDev/tinyglobby#readme", "bugs": {"url": "https://github.com/SuperchupuDev/tinyglobby/issues"}, "dist": {"shasum": "dfa666c75551828580bfe900b86ebf3952bd4642", "tarball": "https://registry.npmjs.org/tinyglobby/-/tinyglobby-0.2.8.tgz", "fileCount": 7, "integrity": "sha512-AMLZywN0vbhiZi2neFEaj9VIIxC+PjDMsp0nAK6tpR86LJavZgHqGz0S/FOONwBygC+mu7R0/TyAQw0gx0Mu9Q==", "signatures": [{"sig": "MEQCIG0ETR6ZMbfQMn84DdZsIY6OmDPU1TVxT826jzGd9boTAiBhDoxKilIeeGROdYf3n/q/GfrLeI/toBZkTKR/MT+i/w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tinyglobby@0.2.8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 23643}, "main": "dist/index.js", "_from": "file:tinyglobby-0.2.8.tgz", "types": "dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=12.0.0"}, "exports": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "scripts": {"lint": "biome lint", "test": "node --experimental-transform-types --test test/**/*.test.ts", "build": "tsup", "check": "biome check", "format": "biome format --write", "lint:fix": "biome lint --fix --unsafe", "test:only": "node --experimental-transform-types --test --test-only test/**/*.test.ts", "typecheck": "tsc --noEmit", "test:coverage": "node --experimental-transform-types --test --experimental-test-coverage test/**/*.test.ts"}, "_npmUser": {"name": "superchupu", "email": "<EMAIL>"}, "_resolved": "/tmp/f8e1450124368ec313d6d82b41d1cfe0/tinyglobby-0.2.8.tgz", "_integrity": "sha512-AMLZywN0vbhiZi2neFEaj9VIIxC+PjDMsp0nAK6tpR86LJavZgHqGz0S/FOONwBygC+mu7R0/TyAQw0gx0Mu9Q==", "repository": {"url": "git+https://github.com/SuperchupuDev/tinyglobby.git", "type": "git"}, "_npmVersion": "10.8.3", "description": "A fast and minimal alternative to globby and fast-glob", "directories": {}, "sideEffects": false, "_nodeVersion": "22.9.0", "dependencies": {"fdir": "^6.4.0", "picomatch": "^4.0.2"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "devDependencies": {"tsup": "^8.3.0", "fs-fixture": "^2.4.0", "typescript": "^5.6.2", "@types/node": "^22.7.4", "@biomejs/biome": "^1.9.2", "@types/picomatch": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/tinyglobby_0.2.8_1727741929539_0.8451570127510035", "host": "s3://npm-registry-packages"}}, "0.2.9": {"name": "tinyglobby", "version": "0.2.9", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"name": "Superchupu"}, "license": "MIT", "_id": "tinyglobby@0.2.9", "maintainers": [{"name": "superchupu", "email": "<EMAIL>"}], "homepage": "https://github.com/SuperchupuDev/tinyglobby#readme", "bugs": {"url": "https://github.com/SuperchupuDev/tinyglobby/issues"}, "dist": {"shasum": "6baddd1b0fe416403efb0dd40442c7d7c03c1c66", "tarball": "https://registry.npmjs.org/tinyglobby/-/tinyglobby-0.2.9.tgz", "fileCount": 7, "integrity": "sha512-8or1+BGEdk1Zkkw2ii16qSS7uVrQJPre5A9o/XkWPATkk23FZh/15BKFxPnlTy6vkljZxLqYCzzBMj30ZrSvjw==", "signatures": [{"sig": "MEQCIGFLIJGQyecptXzxY5xMmGo9VmLgVTI7bVid+X6pa9B4AiBJ3NvivHLFz8CPx21CC4wRfYjJn3fQMwIdYGKlioe29Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tinyglobby@0.2.9", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 23683}, "main": "dist/index.js", "_from": "file:tinyglobby-0.2.9.tgz", "types": "dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=12.0.0"}, "exports": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "scripts": {"lint": "biome lint", "test": "node --experimental-transform-types --test test/**/*.test.ts", "build": "tsup", "check": "biome check", "format": "biome format --write", "lint:fix": "biome lint --fix --unsafe", "test:only": "node --experimental-transform-types --test --test-only test/**/*.test.ts", "typecheck": "tsc --noEmit", "test:coverage": "node --experimental-transform-types --test --experimental-test-coverage test/**/*.test.ts"}, "_npmUser": {"name": "superchupu", "email": "<EMAIL>"}, "_resolved": "/tmp/e6080e14c7efa38f1f5e59209d41e7eb/tinyglobby-0.2.9.tgz", "_integrity": "sha512-8or1+BGEdk1Zkkw2ii16qSS7uVrQJPre5A9o/XkWPATkk23FZh/15BKFxPnlTy6vkljZxLqYCzzBMj30ZrSvjw==", "repository": {"url": "git+https://github.com/SuperchupuDev/tinyglobby.git", "type": "git"}, "_npmVersion": "10.8.3", "description": "A fast and minimal alternative to globby and fast-glob", "directories": {}, "sideEffects": false, "_nodeVersion": "22.9.0", "dependencies": {"fdir": "^6.4.0", "picomatch": "^4.0.2"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "devDependencies": {"tsup": "^8.3.0", "fs-fixture": "^2.4.0", "typescript": "^5.6.2", "@types/node": "^22.7.4", "@biomejs/biome": "^1.9.2", "@types/picomatch": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/tinyglobby_0.2.9_1727777153146_0.19340250772392076", "host": "s3://npm-registry-packages"}}, "0.2.10": {"name": "tinyglobby", "version": "0.2.10", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"name": "Superchupu"}, "license": "MIT", "_id": "tinyglobby@0.2.10", "maintainers": [{"name": "superchupu", "email": "<EMAIL>"}], "homepage": "https://github.com/SuperchupuDev/tinyglobby#readme", "bugs": {"url": "https://github.com/SuperchupuDev/tinyglobby/issues"}, "dist": {"shasum": "e712cf2dc9b95a1f5c5bbd159720e15833977a0f", "tarball": "https://registry.npmjs.org/tinyglobby/-/tinyglobby-0.2.10.tgz", "fileCount": 7, "integrity": "sha512-Zc+8eJlFMvgatPZTl6A9L/yht8QqdmUNtURHaKZLmKBE12hNPSrqNkUp2cs3M/UKmNVVAMFQYSjYIVHDjW5zew==", "signatures": [{"sig": "MEUCIQDAo8iX7Ppg76qMa4EK7T87KEZ0JUGFjgkDVo/Tvg1RugIgL+ejkOtwGaJHSblO3VaKtHCFp5zrjOjxd49Qcs4qxJY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tinyglobby@0.2.10", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 24406}, "main": "dist/index.js", "_from": "file:tinyglobby-0.2.10.tgz", "types": "dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=12.0.0"}, "exports": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "scripts": {"lint": "biome lint", "test": "node --experimental-transform-types --test test/**/*.test.ts", "build": "tsup", "check": "biome check", "format": "biome format --write", "lint:fix": "biome lint --fix --unsafe", "test:only": "node --experimental-transform-types --test --test-only test/**/*.test.ts", "typecheck": "tsc --noEmit", "test:coverage": "node --experimental-transform-types --test --experimental-test-coverage test/**/*.test.ts"}, "_npmUser": {"name": "superchupu", "email": "<EMAIL>"}, "_resolved": "/tmp/0ad582665271b13263320d43b77fec4a/tinyglobby-0.2.10.tgz", "_integrity": "sha512-Zc+8eJlFMvgatPZTl6A9L/yht8QqdmUNtURHaKZLmKBE12hNPSrqNkUp2cs3M/UKmNVVAMFQYSjYIVHDjW5zew==", "repository": {"url": "git+https://github.com/SuperchupuDev/tinyglobby.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "A fast and minimal alternative to globby and fast-glob", "directories": {}, "sideEffects": false, "_nodeVersion": "22.10.0", "dependencies": {"fdir": "^6.4.2", "picomatch": "^4.0.2"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "devDependencies": {"tsup": "^8.3.4", "fs-fixture": "^2.5.0", "typescript": "^5.6.3", "@types/node": "^22.7.9", "@biomejs/biome": "^1.9.4", "@types/picomatch": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/tinyglobby_0.2.10_1729841668849_0.41059796846433194", "host": "s3://npm-registry-packages"}}, "0.2.11": {"name": "tinyglobby", "version": "0.2.11", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"name": "Superchupu"}, "license": "MIT", "_id": "tinyglobby@0.2.11", "maintainers": [{"name": "superchupu", "email": "<EMAIL>"}], "homepage": "https://github.com/SuperchupuDev/tinyglobby#readme", "bugs": {"url": "https://github.com/SuperchupuDev/tinyglobby/issues"}, "dist": {"shasum": "9182cff655a0e272aad850d1a84c5e8e0f700426", "tarball": "https://registry.npmjs.org/tinyglobby/-/tinyglobby-0.2.11.tgz", "fileCount": 7, "integrity": "sha512-32TmKeeKUahv0Go8WmQgiEp9Y21NuxjwjqiRC1nrUB51YacfSwuB44xgXD+HdIppmMRgjQNPdrHyA6vIybYZ+g==", "signatures": [{"sig": "MEUCIQDgPVO6PgBMj2Y3pe+VffHlAOnt1bk+Losz5VOg3FcYSAIgcqCNOqNSPlN6w0GYfH10UEFaJNuwjfZhAJaYZeupVTs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tinyglobby@0.2.11", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 30739}, "main": "dist/index.js", "_from": "file:tinyglobby-0.2.11.tgz", "types": "dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=12.0.0"}, "exports": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "funding": {"url": "https://github.com/sponsors/SuperchupuDev"}, "scripts": {"lint": "biome lint", "test": "node --experimental-transform-types --test", "build": "tsup", "check": "biome check", "format": "biome format --write", "lint:fix": "biome lint --fix --unsafe", "test:only": "node --experimental-transform-types --test --test-only", "typecheck": "tsc --noEmit", "test:coverage": "node --experimental-transform-types --test --experimental-test-coverage"}, "_npmUser": {"name": "superchupu", "email": "<EMAIL>"}, "_resolved": "/tmp/0127cbfa9def8eb5d7a7105314cf1551/tinyglobby-0.2.11.tgz", "_integrity": "sha512-32TmKeeKUahv0Go8WmQgiEp9Y21NuxjwjqiRC1nrUB51YacfSwuB44xgXD+HdIppmMRgjQNPdrHyA6vIybYZ+g==", "repository": {"url": "git+https://github.com/SuperchupuDev/tinyglobby.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "A fast and minimal alternative to globby and fast-glob", "directories": {}, "sideEffects": false, "_nodeVersion": "22.13.1", "dependencies": {"fdir": "^6.4.3", "picomatch": "^4.0.2"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "devDependencies": {"tsup": "^8.3.6", "fs-fixture": "^2.7.0", "typescript": "^5.7.3", "@types/node": "^22.13.4", "@biomejs/biome": "^1.9.4", "@types/picomatch": "^3.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/tinyglobby_0.2.11_1739820712643_0.46060753182549163", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.12": {"name": "tinyglobby", "version": "0.2.12", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"name": "Superchupu"}, "license": "MIT", "_id": "tinyglobby@0.2.12", "maintainers": [{"name": "superchupu", "email": "<EMAIL>"}], "homepage": "https://github.com/SuperchupuDev/tinyglobby#readme", "bugs": {"url": "https://github.com/SuperchupuDev/tinyglobby/issues"}, "dist": {"shasum": "ac941a42e0c5773bd0b5d08f32de82e74a1a61b5", "tarball": "https://registry.npmjs.org/tinyglobby/-/tinyglobby-0.2.12.tgz", "fileCount": 7, "integrity": "sha512-qkf4trmKSIiMTs/E63cxH+ojC2unam7rJ0WrauAzpT3ECNTxGRMlaXxVbfxMUC/w0LaYk6jQ4y/nGR9uBO3tww==", "signatures": [{"sig": "MEUCIGJLsFelwogkDd3dXMsWAE+oWm0X3iVuYHMxSnl6NsF3AiEAg7/khcs++cI+4ho5DgCWLAPoLFJzpuTAVGVgKXT0P4g=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tinyglobby@0.2.12", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 31060}, "main": "dist/index.js", "_from": "file:tinyglobby-0.2.12.tgz", "types": "dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=12.0.0"}, "exports": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "funding": {"url": "https://github.com/sponsors/SuperchupuDev"}, "scripts": {"lint": "biome lint", "test": "node --experimental-transform-types --test", "build": "tsup", "check": "biome check", "format": "biome format --write", "lint:fix": "biome lint --fix --unsafe", "test:only": "node --experimental-transform-types --test --test-only", "typecheck": "tsc --noEmit", "test:coverage": "node --experimental-transform-types --test --experimental-test-coverage"}, "_npmUser": {"name": "superchupu", "email": "<EMAIL>"}, "_resolved": "/tmp/05b7aaccd0c8a79964ec5e45ebb21b8f/tinyglobby-0.2.12.tgz", "_integrity": "sha512-qkf4trmKSIiMTs/E63cxH+ojC2unam7rJ0WrauAzpT3ECNTxGRMlaXxVbfxMUC/w0LaYk6jQ4y/nGR9uBO3tww==", "repository": {"url": "git+https://github.com/SuperchupuDev/tinyglobby.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "A fast and minimal alternative to globby and fast-glob", "directories": {}, "sideEffects": false, "_nodeVersion": "22.13.1", "dependencies": {"fdir": "^6.4.3", "picomatch": "^4.0.2"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "devDependencies": {"tsup": "^8.3.6", "fs-fixture": "^2.7.0", "typescript": "^5.7.3", "@types/node": "^22.13.4", "@biomejs/biome": "^1.9.4", "@types/picomatch": "^3.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/tinyglobby_0.2.12_1740011155377_0.8659892329912942", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.13": {"name": "tinyglobby", "version": "0.2.13", "keywords": ["glob", "patterns", "fast", "implementation"], "author": {"name": "Superchupu"}, "license": "MIT", "_id": "tinyglobby@0.2.13", "maintainers": [{"name": "superchupu", "email": "<EMAIL>"}], "homepage": "https://github.com/SuperchupuDev/tinyglobby#readme", "bugs": {"url": "https://github.com/SuperchupuDev/tinyglobby/issues"}, "dist": {"shasum": "a0e46515ce6cbcd65331537e57484af5a7b2ff7e", "tarball": "https://registry.npmjs.org/tinyglobby/-/tinyglobby-0.2.13.tgz", "fileCount": 7, "integrity": "sha512-mEwzpUgrLySlveBwEVDMKk5B57bhLPYovRfPAXD5gA/98Opn0rCDj3GtLwFvCvH5RK9uPCExUROW5NjDwvqkxw==", "signatures": [{"sig": "MEQCIEcWOwnZOUyi8rr8gfrWHPoTrBM52myZjCzefAhi1Ns/AiB1Gr34f+ppDrcMUsGRXRLGb0v0V8DTzVE8EETlTaaIyw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tinyglobby@0.2.13", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 31264}, "main": "dist/index.js", "_from": "file:tinyglobby-0.2.13.tgz", "types": "dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=12.0.0"}, "exports": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "funding": {"url": "https://github.com/sponsors/SuperchupuDev"}, "scripts": {"lint": "biome lint", "test": "node --experimental-transform-types --test", "build": "tsup", "check": "biome check", "format": "biome format --write", "lint:fix": "biome lint --fix --unsafe", "test:only": "node --experimental-transform-types --test --test-only", "typecheck": "tsc --noEmit", "test:coverage": "node --experimental-transform-types --test --experimental-test-coverage"}, "_npmUser": {"name": "superchupu", "email": "<EMAIL>"}, "_resolved": "/tmp/65056228b4c15da1798ae873c2e6bc9e/tinyglobby-0.2.13.tgz", "_integrity": "sha512-mEwzpUgrLySlveBwEVDMKk5B57bhLPYovRfPAXD5gA/98Opn0rCDj3GtLwFvCvH5RK9uPCExUROW5NjDwvqkxw==", "repository": {"url": "git+https://github.com/SuperchupuDev/tinyglobby.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "A fast and minimal alternative to globby and fast-glob", "directories": {}, "sideEffects": false, "_nodeVersion": "22.14.0", "dependencies": {"fdir": "^6.4.4", "picomatch": "^4.0.2"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "devDependencies": {"tsup": "^8.4.0", "fs-fixture": "^2.7.1", "typescript": "^5.8.3", "@types/node": "^22.14.1", "@biomejs/biome": "^1.9.4", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/tinyglobby_0.2.13_1745087567198_0.8430586549063641", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.14": {"name": "tinyglobby", "version": "0.2.14", "description": "A fast and minimal alternative to globby and fast-glob", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "sideEffects": false, "author": {"name": "Superchupu"}, "license": "MIT", "keywords": ["glob", "patterns", "fast", "implementation"], "repository": {"type": "git", "url": "git+https://github.com/SuperchupuDev/tinyglobby.git"}, "bugs": {"url": "https://github.com/SuperchupuDev/tinyglobby/issues"}, "homepage": "https://github.com/SuperchupuDev/tinyglobby#readme", "funding": {"url": "https://github.com/sponsors/SuperchupuDev"}, "dependencies": {"fdir": "^6.4.4", "picomatch": "^4.0.2"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@types/node": "^22.15.21", "@types/picomatch": "^4.0.0", "fs-fixture": "^2.7.1", "tsdown": "^0.12.3", "typescript": "^5.8.3"}, "engines": {"node": ">=12.0.0"}, "publishConfig": {"access": "public", "provenance": true}, "scripts": {"build": "tsdown", "check": "biome check", "format": "biome format --write", "lint": "biome lint", "lint:fix": "biome lint --fix --unsafe", "test": "node --experimental-transform-types --test", "test:coverage": "node --experimental-transform-types --test --experimental-test-coverage", "test:only": "node --experimental-transform-types --test --test-only", "typecheck": "tsc --noEmit"}, "_id": "tinyglobby@0.2.14", "_integrity": "sha512-tX5e7OM1HnYr2+a2C/4V0htOcSQcoSTH9KgJnVvNm5zm/cyEWKJ7j7YutsH9CxMdtOkkLFy2AHrMci9IM8IPZQ==", "_resolved": "/tmp/e3d9af7fcfd9393cf9c8400e60fd349f/tinyglobby-0.2.14.tgz", "_from": "file:tinyglobby-0.2.14.tgz", "_nodeVersion": "22.15.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-tX5e7OM1HnYr2+a2C/4V0htOcSQcoSTH9KgJnVvNm5zm/cyEWKJ7j7YutsH9CxMdtOkkLFy2AHrMci9IM8IPZQ==", "shasum": "5280b0cf3f972b050e74ae88406c0a6a58f4079d", "tarball": "https://registry.npmjs.org/tinyglobby/-/tinyglobby-0.2.14.tgz", "fileCount": 7, "unpackedSize": 31120, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tinyglobby@0.2.14", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCZpOR2Ndtzv/PgDNuWql51hjonKXY/551SDuQOyR+RpwIgdOhSpkPjekoiw70WoHzlF1pp6/yOxVVMaPgF/e6nTQo="}]}, "_npmUser": {"name": "superchupu", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "superchupu", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/tinyglobby_0.2.14_1748189945749_0.2069337039407142"}, "_hasShrinkwrap": false}}, "time": {"created": "2024-07-22T18:19:33.107Z", "modified": "2025-05-25T16:19:06.334Z", "0.1.0": "2024-07-22T18:19:33.355Z", "0.1.1": "2024-07-22T19:21:36.789Z", "0.1.2": "2024-07-22T21:50:34.722Z", "0.2.0": "2024-07-24T14:07:19.907Z", "0.2.1": "2024-08-04T09:52:21.246Z", "0.2.2": "2024-08-06T15:51:09.291Z", "0.2.3": "2024-08-24T12:14:22.936Z", "0.2.4": "2024-08-24T13:19:35.254Z", "0.2.5": "2024-08-24T13:54:47.952Z", "0.2.6": "2024-09-08T23:02:09.719Z", "0.2.7": "2024-09-30T12:30:26.545Z", "0.2.8": "2024-10-01T00:18:49.737Z", "0.2.9": "2024-10-01T10:05:53.334Z", "0.2.10": "2024-10-25T07:34:29.010Z", "0.2.11": "2025-02-17T19:31:52.818Z", "0.2.12": "2025-02-20T00:25:55.583Z", "0.2.13": "2025-04-19T18:32:47.367Z", "0.2.14": "2025-05-25T16:19:05.967Z"}, "bugs": {"url": "https://github.com/SuperchupuDev/tinyglobby/issues"}, "author": {"name": "Superchupu"}, "license": "MIT", "homepage": "https://github.com/SuperchupuDev/tinyglobby#readme", "keywords": ["glob", "patterns", "fast", "implementation"], "repository": {"type": "git", "url": "git+https://github.com/SuperchupuDev/tinyglobby.git"}, "description": "A fast and minimal alternative to globby and fast-glob", "maintainers": [{"name": "superchupu", "email": "<EMAIL>"}], "readme": "# tinyglobby\n\n[![npm version](https://img.shields.io/npm/v/tinyglobby.svg?maxAge=3600)](https://npmjs.com/package/tinyglobby)\n[![monthly downloads](https://img.shields.io/npm/dm/tinyglobby.svg?maxAge=3600)](https://npmjs.com/package/tinyglobby)\n\nA fast and minimal alternative to globby and fast-glob, meant to behave the same way.\n\nBoth globby and fast-glob present some behavior no other globbing lib has,\nwhich makes it hard to manually replace with something smaller and better.\n\nThis library uses only two subdependencies, compared to `globby`'s [23](https://npmgraph.js.org/?q=globby@14.1.0)\nand `fast-glob`'s [17](https://npmgraph.js.org/?q=fast-glob@3.3.3).\n\n## Usage\n\n```js\nimport { glob, globSync } from 'tinyglobby';\n\nawait glob(['files/*.ts', '!**/*.d.ts'], { cwd: 'src' });\nglobSync(['src/**/*.ts'], { ignore: ['**/*.d.ts'] });\n```\n\n## API\n\n- `glob(patterns: string | string[], options: GlobOptions): Promise<string[]>`: Returns a promise with an array of matches.\n- `globSync(patterns: string | string[], options: GlobOptions): string[]`: Returns an array of matches.\n- `convertPathToPattern(path: string): string`: Converts a path to a pattern depending on the platform.\n- `escapePath(path: string): string`: Escapes a path's special characters depending on the platform.\n- `isDynamicPattern(pattern: string, options?: GlobOptions): boolean`: Checks if a pattern is dynamic.\n\n## Options\n\n- `patterns`: An array of glob patterns to search for. Defaults to `['**/*']`.\n- `ignore`: An array of glob patterns to ignore.\n- `cwd`: The current working directory in which to search. Defaults to `process.cwd()`.\n- `absolute`: Whether to return absolute paths. Defaults to `false`.\n- `dot`: Whether to allow entries starting with a dot. Defaults to `false`.\n- `deep`: Maximum depth of a directory. Defaults to `Infinity`.\n- `followSymbolicLinks`: Whether to traverse and include symbolic links. Defaults to `true`.\n- `caseSensitiveMatch`: Whether to match in case-sensitive mode. Defaults to `true`.\n- `expandDirectories`: Whether to expand directories. Disable to best match `fast-glob`. Defaults to `true`.\n- `onlyDirectories`: Enable to only return directories. Disables `onlyFiles` if set. Defaults to `false`.\n- `onlyFiles`: Enable to only return files. Defaults to `true`.\n- `debug`: Enable debug logs. Useful for development purposes.\n\n## Used by\n\n`tinyglobby` is downloaded many times by projects all around the world. Here's a partial list of notable projects that use it:\n\n<!-- should be sorted by weekly download count -->\n- [`vite`](https://github.com/vitejs/vite)\n- [`pnpm`](https://github.com/pnpm/pnpm)\n- [`node-gyp`](https://github.com/nodejs/node-gyp)\n- [`eslint-import-resolver-typescript`](https://github.com/import-js/eslint-import-resolver-typescript)\n- [`vitest`](https://github.com/vitest-dev/vitest)\n- [`copy-webpack-plugin`](https://github.com/webpack-contrib/copy-webpack-plugin)\n- [`storybook`](https://github.com/storybookjs/storybook)\n- [`ts-morph`](https://github.com/dsherret/ts-morph)\n- [`nx`](https://github.com/nrwl/nx)\n- [`sort-package-json`](https://github.com/keithamus/sort-package-json)\n- [`unimport`](https://github.com/unjs/unimport)\n- [`tsup`](https://github.com/egoist/tsup)\n- [`lerna`](https://github.com/lerna/lerna)\n- [`cspell`](https://github.com/streetsidesoftware/cspell)\n- [`nuxt`](https://github.com/nuxt/nuxt)\n- [`postcss-mixins`](https://github.com/postcss/postcss-mixins)\n- [`astro`](https://github.com/withastro/astro)\n- [`unocss`](https://github.com/unocss/unocss)\n- [`vitepress`](https://github.com/vuejs/vitepress)\n- [`pkg-pr-new`](https://github.com/stackblitz-labs/pkg.pr.new)\n- Your own project? [Open an issue](https://github.com/SuperchupuDev/tinyglobby/issues)\nif you feel like this list is incomplete.\n", "readmeFilename": "README.md"}