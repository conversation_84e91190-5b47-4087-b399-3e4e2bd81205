{"_id": "find-up", "_rev": "38-1fc8d98a17e4906ad6a56b2fa841f43b", "name": "find-up", "description": "Find a file or directory by walking up parent directories", "dist-tags": {"latest": "7.0.0"}, "versions": {"1.0.0": {"name": "find-up", "version": "1.0.0", "description": "Find a file by walking up parent directories", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/find-up"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["find", "up", "find-up", "findup", "look-up", "look", "file", "search", "match", "package", "resolve", "parent", "parents", "folder", "directory", "dir", "walk", "walking", "path"], "dependencies": {"path-exists": "^2.0.0", "pinkie-promise": "^1.0.0"}, "devDependencies": {"ava": "*", "xo": "*"}, "gitHead": "9bf0f46fd04d04902cc4934f418ca49cdf199d83", "bugs": {"url": "https://github.com/sindresorhus/find-up/issues"}, "homepage": "https://github.com/sindresorhus/find-up", "_id": "find-up@1.0.0", "_shasum": "df0a54abeebdf9984168fa556bd18a8f24b4d15c", "_from": ".", "_npmVersion": "2.11.3", "_nodeVersion": "0.12.7", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "df0a54abeebdf9984168fa556bd18a8f24b4d15c", "tarball": "https://registry.npmjs.org/find-up/-/find-up-1.0.0.tgz", "integrity": "sha512-nYrh/3A5t8tiGC3UYSfjuzAQ9ZZJ/ADe9EJhVyRBIFhLRoRuQTQybvBM0MCcXnHyAT/xi6BBTATsalvyR1hcUA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDtzGpbdhSyA7hxGh7j6qSQpxBcTVbBxbSxv1XsoHQ3HgIgbWBEEGxl9lDN6JAHRdcn6yuexwUEuK91OJg+7mkxEJw="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "1.1.0": {"name": "find-up", "version": "1.1.0", "description": "Find a file by walking up parent directories", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/find-up"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["find", "up", "find-up", "findup", "look-up", "look", "file", "search", "match", "package", "resolve", "parent", "parents", "folder", "directory", "dir", "walk", "walking", "path"], "dependencies": {"path-exists": "^2.0.0", "pinkie-promise": "^2.0.0"}, "devDependencies": {"ava": "*", "xo": "*"}, "gitHead": "06e9dae73659ddf2421440ca4695161c38d7d2fb", "bugs": {"url": "https://github.com/sindresorhus/find-up/issues"}, "homepage": "https://github.com/sindresorhus/find-up", "_id": "find-up@1.1.0", "_shasum": "a63b0eec4625a2902534898a5f9eec8aaed046e9", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.1", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "a63b0eec4625a2902534898a5f9eec8aaed046e9", "tarball": "https://registry.npmjs.org/find-up/-/find-up-1.1.0.tgz", "integrity": "sha512-0dtqiyLPClO4M7Hra31H79HDB1Fk5TEzV3OXaFWX7CVXlmVdK/9zDkDPovclInKYyRcIzWvTHzHPP+4l2phKLQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFEk2oJ9WY6Th/bp1lBz6G+WNqdNK561Gie3fJwfIDe/AiBxNNGus3IJpPZb6uVBX5hW8ohBXyTxezRWMnFfTE5/fA=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "1.1.1": {"name": "find-up", "version": "1.1.1", "description": "Find a file by walking up parent directories", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/find-up"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["find", "up", "find-up", "findup", "look-up", "look", "file", "search", "match", "package", "resolve", "parent", "parents", "folder", "directory", "dir", "walk", "walking", "path"], "dependencies": {"path-exists": "^2.0.0", "pinkie-promise": "^2.0.0"}, "devDependencies": {"ava": "*", "xo": "*"}, "gitHead": "bc536485bfddd2d960f472964e65afef9895f475", "bugs": {"url": "https://github.com/sindresorhus/find-up/issues"}, "homepage": "https://github.com/sindresorhus/find-up", "_id": "find-up@1.1.1", "_shasum": "53bdfb982c41e97ba7f64173d239b406cfd79f85", "_from": ".", "_npmVersion": "2.14.12", "_nodeVersion": "4.3.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "53bdfb982c41e97ba7f64173d239b406cfd79f85", "tarball": "https://registry.npmjs.org/find-up/-/find-up-1.1.1.tgz", "integrity": "sha512-sBmmU1QyxEhdhwpPUEwRzcjK9qEpIjlHX80pdT578SxMSuPQA6annjJF6gNQGK6UWDaoSx4FaUWGsegqi7QoFA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCH8sxc+fRAcnNOBB/sxVO9BAxc5CmI9TmmS8+ackErdAIhAMzHcTnkgrBcxGcDs8bNwSO+AolNIZtWxj6WhJvMtmYN"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-13-west.internal.npmjs.com", "tmp": "tmp/find-up-1.1.1.tgz_1456910658734_0.6790364829357713"}, "directories": {}}, "1.1.2": {"name": "find-up", "version": "1.1.2", "description": "Find a file by walking up parent directories", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/find-up"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["find", "up", "find-up", "findup", "look-up", "look", "file", "search", "match", "package", "resolve", "parent", "parents", "folder", "directory", "dir", "walk", "walking", "path"], "dependencies": {"path-exists": "^2.0.0", "pinkie-promise": "^2.0.0"}, "devDependencies": {"ava": "*", "tempfile": "^1.1.1", "xo": "*"}, "gitHead": "f2d7c1ff74fbac82b2cff038e311ef4b075d9184", "bugs": {"url": "https://github.com/sindresorhus/find-up/issues"}, "homepage": "https://github.com/sindresorhus/find-up", "_id": "find-up@1.1.2", "_shasum": "6b2e9822b1a2ce0a60ab64d610eccad53cb24d0f", "_from": ".", "_npmVersion": "2.14.12", "_nodeVersion": "4.3.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "6b2e9822b1a2ce0a60ab64d610eccad53cb24d0f", "tarball": "https://registry.npmjs.org/find-up/-/find-up-1.1.2.tgz", "integrity": "sha512-jvElSjyuo4EMQGoTwo1uJU5pQMwTW5lS1x05zzfJuTIyLR3zwO27LYrxNg+dlvKpGOuGy/MzBdXh80g0ve5+HA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGaa22RuQqO4i3+W3rE449s/AfrjfY17XG38q7wUTX/3AiAemJrcHUmTx9uyrwxso98RsfpKdyHCnd7syVmS/TdjjA=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/find-up-1.1.2.tgz_1457199955637_0.3445317060686648"}, "directories": {}}, "2.0.0": {"name": "find-up", "version": "2.0.0", "description": "Find a file by walking up parent directories", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/find-up.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["find", "up", "find-up", "findup", "look-up", "look", "file", "search", "match", "package", "resolve", "parent", "parents", "folder", "directory", "dir", "walk", "walking", "path"], "dependencies": {"path-exists": "^2.0.0"}, "devDependencies": {"ava": "*", "tempfile": "^1.1.1", "xo": "*"}, "xo": {"esnext": true}, "gitHead": "80342293c547b6982091f6ff141a76fd70317f57", "bugs": {"url": "https://github.com/sindresorhus/find-up/issues"}, "homepage": "https://github.com/sindresorhus/find-up#readme", "_id": "find-up@2.0.0", "_shasum": "71e6dc2dad9222143cfc0fa5de7ab739e7320c05", "_from": ".", "_npmVersion": "2.15.9", "_nodeVersion": "4.5.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "71e6dc2dad9222143cfc0fa5de7ab739e7320c05", "tarball": "https://registry.npmjs.org/find-up/-/find-up-2.0.0.tgz", "integrity": "sha512-ypUBlopeObhUJyilSEp2aV8J2gTM0X0V3JlW7TpQmnprv+bs6mNe6iRPpruf41nld7Oi39lnSfIxJHXIN1+2PA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC5/6Q/0tq5CrSFYDzePHjrNCe/nfwim+n8wxsb6jWsVAiEA1Qf07VMn+vOIT4svTmk4Bv2RKiQUpBNPI1MJKlcS1Cc="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/find-up-2.0.0.tgz_1474388027708_0.42646232759580016"}, "directories": {}}, "2.1.0": {"name": "find-up", "version": "2.1.0", "description": "Find a file by walking up parent directories", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/find-up.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["find", "up", "find-up", "findup", "look-up", "look", "file", "search", "match", "package", "resolve", "parent", "parents", "folder", "directory", "dir", "walk", "walking", "path"], "dependencies": {"locate-path": "^2.0.0"}, "devDependencies": {"ava": "*", "tempfile": "^1.1.1", "xo": "*"}, "xo": {"esnext": true}, "gitHead": "10202fb1621f0c277d5d5eeaf01c1c32b008fbef", "bugs": {"url": "https://github.com/sindresorhus/find-up/issues"}, "homepage": "https://github.com/sindresorhus/find-up#readme", "_id": "find-up@2.1.0", "_shasum": "45d1b7e506c717ddd482775a2b77920a3c0c57a7", "_from": ".", "_npmVersion": "4.0.2", "_nodeVersion": "6.9.1", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "45d1b7e506c717ddd482775a2b77920a3c0c57a7", "tarball": "https://registry.npmjs.org/find-up/-/find-up-2.1.0.tgz", "integrity": "sha512-NWzkk0jSJtTt08+FBFMvXoeZnOJD+jTtsRmBYbAIzJdX6l7dLgR7CTubCM5/eDdPUBvLCeVasP1brfVR/9/EZQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDYtZn4nc5qdQT53jb5RwoUN4AVT38ylVnz3qdgjSwMEAIgbRORsSK9KWPIJZ/q0zvXTwa12+hW1bVFXgbY7zC1E30="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/find-up-2.1.0.tgz_1480684911017_0.33125952794216573"}, "directories": {}}, "3.0.0": {"name": "find-up", "version": "3.0.0", "description": "Find a file or directory by walking up parent directories", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/find-up.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["find", "up", "find-up", "findup", "look-up", "look", "file", "search", "match", "package", "resolve", "parent", "parents", "folder", "directory", "dir", "walk", "walking", "path"], "dependencies": {"locate-path": "^3.0.0"}, "devDependencies": {"ava": "*", "tempy": "^0.2.1", "xo": "*"}, "gitHead": "2319b79a9e728fc13fc1a1a15e84bf5df100719e", "bugs": {"url": "https://github.com/sindresorhus/find-up/issues"}, "homepage": "https://github.com/sindresorhus/find-up#readme", "_id": "find-up@3.0.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.2", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-1yD6RmLI1XBfxugvORwlck6f75tYL+iR0jqwsOrOxMZyGYqUuDhJ0l4AXdO1iX/FTs9cBAMEk1gWSEx1kSbylg==", "shasum": "49169f1d7993430646da61ecc5ae355c21c97b73", "tarball": "https://registry.npmjs.org/find-up/-/find-up-3.0.0.tgz", "fileCount": 4, "unpackedSize": 4845, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbJ3kZCRA9TVsSAnZWagAAfx0P/i+gkVWTJv3VhXz8EbEf\n3LQ9D2YyTKUseUs5GYfYEmZuJ1QpwqCADP5981jiLwl8MBL1QFq4GqIPD74/\n3XWNSff5pqGg2+/z8fIOKQVi/VcRZYKfZ3BVWMRf0kldgonCxLsQVuOoEXOQ\n90aFj9OsilQRKjwfltiFysvyPFo4rEQRiC4fh0YHant7vtUVIucCQTmWmoB+\n9y9M3nplqA7IiiCZhLX5OKqXW27FzA/em006Fi16LiA6/dOdXAgcWwn49Ha3\n1K0zfxh47sGKA/noxaxVS63h+og9XgYdhewrO7rU2zvtbwyuAgJoAPK/8448\nqk+YEpxG+YuNp4t2eU+XtS5yEKuEEC/R6f3qN7BoNkuS9EKnM56LuWj1yinQ\nyyvbtuuwFbwmtMGLCZVANiDR0+UAGpzJKW0KZs9kg9USpEfkp07oNwuLQQuw\neSdz1vblDju3Txa0N0hFHckn4B8+zyL/VQwVE1aA2bXrfLMby8k1rHQq4r3R\nwAMgU1If5UVS9obuuoYKW9B4EGBabI4o0rvKfsMMNY1+TYi3AUeFArRuYF1D\nJODWneatuNZHU06fMM7+kV5r+5aZfFfqJcbyx0Qdly12X8glIgGciAExeXtb\nB8UImgyhT0Nz2/hpSNgW/6P8+vfFze1uIodDvSezJk3b6Vc7LAH1SNy3kYKh\n9rVa\r\n=sd/n\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEwVgY/Prlza6wM+m4HxNRL18HK9gEoTiXP0RJCnROp7AiEAwVM2X4hQMUg6plwk4Bz4hyU/o3yu1akcEwwnSNkUy30="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/find-up_3.0.0_1529313561272_0.9299595096856903"}, "_hasShrinkwrap": false}, "4.0.0": {"name": "find-up", "version": "4.0.0", "description": "Find a file or directory by walking up parent directories", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/find-up.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["find", "up", "find-up", "findup", "look-up", "look", "file", "search", "match", "package", "resolve", "parent", "parents", "folder", "directory", "walk", "walking", "path"], "dependencies": {"locate-path": "^5.0.0"}, "devDependencies": {"ava": "^1.4.1", "is-path-inside": "^2.1.0", "tempy": "^0.3.0", "tsd": "^0.7.3", "xo": "^0.24.0"}, "gitHead": "a2841237562d1f9996084d16c1bc6b471143892a", "bugs": {"url": "https://github.com/sindresorhus/find-up/issues"}, "homepage": "https://github.com/sindresorhus/find-up#readme", "_id": "find-up@4.0.0", "_nodeVersion": "8.16.0", "_npmVersion": "6.9.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-zoH7ZWPkRdgwYCDVoQTzqjG8JSPANhtvLhh4KVUHyKnaUJJrNeFmWIkTcNuJmR3GLMEmGYEf2S2bjgx26JTF+Q==", "shasum": "c367f8024de92efb75f2d4906536d24682065c3a", "tarball": "https://registry.npmjs.org/find-up/-/find-up-4.0.0.tgz", "fileCount": 5, "unpackedSize": 10221, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc2DutCRA9TVsSAnZWagAAH8kP/jsZxV5KNq5u/5HSXNUA\n3zyTzFjRSRSxgzDv7MyjqkZDZVGaJxoQjXpwC/RS5pHM2R442RQR52xq/CHD\nGvCzTnUe51+XgNJ46pEcwA/t+PRQF2gGHxv7Sp/GbUwVLlXkTjPZpKr7Mzix\nSrB0wWZ97vB+xvr24FAp3T9c8gU0Lr6VpvA1hiLo3ecAWq984aPG2iOnFco1\nQVh2yp5ovblESCyElPNl5ZMPLHeH9ovroOBOs9B3J4yKm4EF/b8YTFlgIuGI\nDD5fDH076ql7pUEqEJKQvijt7W3uIHs8vh3ZLy3ModwN28ix2lzEQt70T+k1\nvjvDm+ZJXSYK88Owhe4qqVmaYkB5sTnChJTunid3wHN4Me3pusuhayIrLddd\nObiq7BHAvmFiqcFfrjtGxWLXnzhybkPAof0ENceSd288+NEY5N201OSsLAiB\naLj2/saDpJm4+FRQkQwj3su9a/dfhBLt2gucpiFkq8Ca4yWagSu2FU1r6fV2\n1hHp8bAALOJNmb+uDcxsdGSaYjXX9ESsLH1hRI+ROqrhOonY6qmgtO0pUUkv\n1VlqgdW7JSKYbafpVPqH4yrC4wPAq4S1XAn3k0GEnS2TrO6sURWZllF+xdPp\nbRi0E77GE3kYr02cOSowEfz6DvonQBkD1Y0A1bKnj3sMc4iaa0twtIXnJmGv\nTAmJ\r\n=wBb5\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEc6lKsPFBhlHF8nlbZyTIKqOnrFBt28/n7IBAscKJB1AiBlgIRoov7nmCN1jUD9zSWAia8sT2ky/6yIhDtTren/Fw=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/find-up_4.0.0_1557674925044_0.058754120668220944"}, "_hasShrinkwrap": false}, "4.1.0": {"name": "find-up", "version": "4.1.0", "description": "Find a file or directory by walking up parent directories", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/find-up.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["find", "up", "find-up", "findup", "look-up", "look", "file", "search", "match", "package", "resolve", "parent", "parents", "folder", "directory", "walk", "walking", "path"], "dependencies": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}, "devDependencies": {"ava": "^2.1.0", "is-path-inside": "^2.1.0", "tempy": "^0.3.0", "tsd": "^0.7.3", "xo": "^0.24.0"}, "gitHead": "6c32f0caed1684ef778053b6f79b13a772e22ba4", "bugs": {"url": "https://github.com/sindresorhus/find-up/issues"}, "homepage": "https://github.com/sindresorhus/find-up#readme", "_id": "find-up@4.1.0", "_nodeVersion": "8.16.0", "_npmVersion": "6.9.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==", "shasum": "97afe7d6cdc0bc5928584b7c8d7b16e8a9aa5d19", "tarball": "https://registry.npmjs.org/find-up/-/find-up-4.1.0.tgz", "fileCount": 5, "unpackedSize": 11611, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdBzEzCRA9TVsSAnZWagAA7ZwQAJUrXCf9gkIYlisJcBE6\n2Wzq80Qhyy1k36PLbZPOIgwJsRyN81OGxhQQ5dlzvSIdyvXmJveNSm85+9pC\naN+th01evbkcCVLo6hoPwZyjv/CxWPjdKKqbF0dTBJwY8zCW+g0zDm/TUdlW\nZvlIncpN/AUSOjBpnmd8jYSC+wbedQ3JBCJi0ETSBYIVOuNwKUr14F0wl25+\nCZIAZB2D9VUMP+ZgT+F5z64OiUz1/+Q+U4VtXU0ivJ7OQfX3obNRqRx33KS7\nvBDHgVpeCh5ryMXNEGMZFblzR1qUOewik9/YdQ9e848SbhNu9oSfh4FaEg5X\n6ybitbPf7oOBQudM+LdJofhFkqJgUM8E+/eKtUxWZx6S2FmxbHltrjchAliW\n8JARBCQ2g0OqqyPPFOyWtQlImWr9BYcm2nyZqjA5Tj6jbGTsxkMaPK0YS89g\nAwXxJA5bXn3Xp8KTxFmxJUNJ0zDf5eRhWaZEIX/Tm2qpdEB2+kYbE9QfdOtV\nTo+Y7ZIg5mE44sDi5oN6LLmic+eHlTqtUaqaRS36Lecx65NbCBQY8fkxPNQp\ndJE/qYMRNOWMiNpA8yXqxpaXTtBUopFCpZ1ahTBUpQVP5pKVxX3uoPAbTT2Q\nzUvs1S2b0zo1sFAt3LpCgs24gyTu0WJTyMTFEKrTsk5nALPCpU93o5rUnJ0L\nSUUW\r\n=2E9L\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHBGxf/EBWSrVAFVhJNToCIC1Kea1biltBR/pXbgCD5YAiAQx1O3FKsgP0EMKJKabUF+1z6isUPkSNEaWr0iaFeSvw=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/find-up_4.1.0_1560752434049_0.7412163695841496"}, "_hasShrinkwrap": false}, "5.0.0": {"name": "find-up", "version": "5.0.0", "description": "Find a file or directory by walking up parent directories", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/find-up.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=10"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["find", "up", "find-up", "findup", "look-up", "look", "file", "search", "match", "package", "resolve", "parent", "parents", "folder", "directory", "walk", "walking", "path"], "dependencies": {"locate-path": "^6.0.0", "path-exists": "^4.0.0"}, "devDependencies": {"ava": "^2.1.0", "is-path-inside": "^2.1.0", "tempy": "^0.6.0", "tsd": "^0.13.1", "xo": "^0.33.0"}, "gitHead": "e852e9cfd020844fac0ea2ff073c378cc302db4d", "bugs": {"url": "https://github.com/sindresorhus/find-up/issues"}, "homepage": "https://github.com/sindresorhus/find-up#readme", "_id": "find-up@5.0.0", "_nodeVersion": "14.5.0", "_npmVersion": "6.14.7", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==", "shasum": "4c92819ecb7083561e4f4a240a86be5198f536fc", "tarball": "https://registry.npmjs.org/find-up/-/find-up-5.0.0.tgz", "fileCount": 5, "unpackedSize": 11761, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfMucJCRA9TVsSAnZWagAA6NcP/34vvIblDCnYQDSTYCrf\nZQmTpKT9t3/IdMqjEVUtoSq39MmWx1UpXL82rLIP8EkahMfwhCv+RWjGBtpw\n0wMYyyTeQ42dC1xV6FbZaTwEPle0co3soTeMSfxdHrFQxriGagsGDPEuNfXG\nlh9XXh2g6MIJ1Skyepcyz9JDVe8C1zJhQPQnGq/39hWVpaYXufra9E4HhRki\nyv204MQlkqEjuh6HK5e52u3x3bmeo/fXQEIk8jzlJB/0hQAtNULzkerZdF++\nq8uw1DHp4bgEwFVEjbepg+UR5Wqp8lCAk3lNrjzKz20DB77qCHL38CJhkg/q\nDgkU4odE7cnH8MvEqlVJSz5B91yUAplDasXVrsThBgQm6oA8olzS4D4QTjbY\nDsO/bzUKAE3PBUO9SZbfvwn6hH1ofIEcv0RFzYge99yybET1aCPB8VJ9fNZ9\n8djNIvaIoIBayfaL7hS+1ixKnA88Cav3Lk6hOkDYgcY1RI9vCRV9nLnKX7CC\ne/u0Hyr5KIRghh572akpGY3G15b6M/caSaiDrJvA2eb3PRE+SV8lx4T7CjZz\niyFQ3S1ZBUDX6XWuYGwfCn8iz3NwF4c1aVQG0AH64UlAS147rAsoiS3RE1nr\nvhsNq4pZBf3gd8jqaG5q15pahcWt4vDiXBpGmh+xl2MWpJ1v9xCPbeyNcaNv\nMh14\r\n=sIO9\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBZTskCIfiMh8wIXWcTPgZ6SWIs4oCxXp2LFj6ZLWYx9AiAifKijEBRhzjpl5raTEkYzRcRRUVeZhnxXJQVwXT0Plw=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/find-up_5.0.0_1597171464642_0.13015534581818233"}, "_hasShrinkwrap": false}, "6.0.0": {"name": "find-up", "version": "6.0.0", "description": "Find a file or directory by walking up parent directories", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/find-up.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["find", "up", "find-up", "findup", "look-up", "look", "file", "search", "match", "package", "resolve", "parent", "parents", "folder", "directory", "walk", "walking", "path"], "dependencies": {"locate-path": "^7.0.0", "path-exists": "^5.0.0"}, "devDependencies": {"ava": "^3.15.0", "is-path-inside": "^4.0.0", "tempy": "^2.0.0", "tsd": "^0.17.0", "xo": "^0.44.0"}, "gitHead": "ace3d10aadce889a78924954a2766af81eb315ff", "bugs": {"url": "https://github.com/sindresorhus/find-up/issues"}, "homepage": "https://github.com/sindresorhus/find-up#readme", "_id": "find-up@6.0.0", "_nodeVersion": "16.7.0", "_npmVersion": "7.20.3", "dist": {"integrity": "sha512-NU20P/qRR4fbjbfQgf5SL6L7AbQbUG69OmBJ3o+DEmHwSwKaaeZ+9ok/zuE5Z8pyFSGPerTen16gLZTs1v1zjQ==", "shasum": "7e321c34ecfea17dfffc4d78017bb025b9dfff71", "tarball": "https://registry.npmjs.org/find-up/-/find-up-6.0.0.tgz", "fileCount": 5, "unpackedSize": 11777, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhJ3kwCRA9TVsSAnZWagAAo1AP/RD2vT41P1PN65bDOlIB\n1s30Aq3cxHYX3xamuefGtSWcyVovvu9HzWwKlZWix4lxyVvEFDNPlXLhKfpx\nqCsDvB8WXQ55Tj5XNen23icl8G9mmtWZwJBiPS1Mhd3l//J/95HbMXPZiMQU\nAnL+dHL46Pk2WQcyD1qZy8NqIZTnd6WhQEjyRzBSHph1rrXKW0or2TI6TCIi\n2uXoBOn8G/bm44SgANyTJB+xI0b+Q+sQ77t5eHFSaOkvhaIts2FSRo5lqCG1\nlSXaH3S4kflZXJAJQ7ugleY+epo5ALPMVRtLQiLjnUquZHtByuEokevL4Yea\nti4+sDMRLV/w0BSA2YeDLnnSGInJYknvUsn/uSGrqlI/wP0CnYflTx2/hkp+\nuchUtWE574RcKQVF/xTmfhDnyG/xx6Q0UZoMurJ9M/n4KUmH+cLRZItfsKgt\ny3dXNe56io3buH+JJW7wZS/3yEMEVoj+skexBqYbNYgc7u1z7I+PP92O1ekm\ngBF4DQ63EVCXq2Kp5r6oHslTDlvW8BqARKmPnDB8bOS2HCqgS6Sjh2v6iJIQ\nRxyNk3Tdm8iEaVNi1/a8x2OVnIj0TsjxQkjZTH2PF9l6v3iSoBzrvGK2AzAd\nplWDLYc6K+d2QWbRG8UKAfSaeR+iCUyKlDKqsm+W/sUem0Oxg6REjPtyQRAl\nRM0b\r\n=1tRf\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCmisFbNbmS6CyJbZe+RrVw4undDHAAMEhk3/8IzIiB5gIhANZZqpSo5emITSExR6fA7PG79gLW+ki4+8iOAG+FFWVY"}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/find-up_6.0.0_1629976880383_0.6096402327348933"}, "_hasShrinkwrap": false}, "6.1.0": {"name": "find-up", "version": "6.1.0", "description": "Find a file or directory by walking up parent directories", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/find-up.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["find", "up", "find-up", "findup", "look-up", "look", "file", "search", "match", "package", "resolve", "parent", "parents", "folder", "directory", "walk", "walking", "path"], "dependencies": {"locate-path": "^7.0.0", "path-exists": "^5.0.0"}, "devDependencies": {"ava": "^3.15.0", "is-path-inside": "^4.0.0", "tempy": "^2.0.0", "tsd": "^0.17.0", "xo": "^0.44.0"}, "gitHead": "d3a3cfc92324904348f921e97ef424189645dd63", "bugs": {"url": "https://github.com/sindresorhus/find-up/issues"}, "homepage": "https://github.com/sindresorhus/find-up#readme", "_id": "find-up@6.1.0", "_nodeVersion": "16.9.1", "_npmVersion": "7.20.3", "dist": {"integrity": "sha512-aBlseiBgQ1RSiF/brMW+toDud3NHJ2Hn3pgNJLmBf2+gBwwNbfhE/Lbg2wwwoHfD3qXReOvDH4hlywQCXp4/Lw==", "shasum": "96009919bff6cfba2bad6ceb5520c26082ecf370", "tarball": "https://registry.npmjs.org/find-up/-/find-up-6.1.0.tgz", "fileCount": 5, "unpackedSize": 12302, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCEt4U540GITJ9O04spQOsmj5r8NnDnTLVBOF45ff345QIhAIm+ZrjS9diY36hrqmpyCjj+CwzmPmrbaOzBGqXT1bB/"}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/find-up_6.1.0_1632654113545_0.09908885893458863"}, "_hasShrinkwrap": false}, "6.2.0": {"name": "find-up", "version": "6.2.0", "description": "Find a file or directory by walking up parent directories", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/find-up.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["find", "up", "find-up", "findup", "look-up", "look", "file", "search", "match", "package", "resolve", "parent", "parents", "folder", "directory", "walk", "walking", "path"], "dependencies": {"locate-path": "^7.0.0", "path-exists": "^5.0.0"}, "devDependencies": {"ava": "^3.15.0", "is-path-inside": "^4.0.0", "tempy": "^2.0.0", "tsd": "^0.17.0", "xo": "^0.44.0"}, "gitHead": "db9962ce02553945edcfddac7d472789c86b6eb6", "bugs": {"url": "https://github.com/sindresorhus/find-up/issues"}, "homepage": "https://github.com/sindresorhus/find-up#readme", "_id": "find-up@6.2.0", "_nodeVersion": "16.10.0", "_npmVersion": "7.20.3", "dist": {"integrity": "sha512-yWHzMzXCaFoABSnFTCPKNFlYoq4mSga9QLRRKOCLSJ33hSkzROB14ITbAWW0QDQDyuzsPQ33S1DsOWQb/oW1yA==", "shasum": "f3b81d633fa83bebe64f83a8bab357f86d5914be", "tarball": "https://registry.npmjs.org/find-up/-/find-up-6.2.0.tgz", "fileCount": 5, "unpackedSize": 16875, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2rEUCRA9TVsSAnZWagAABGwP/2smJ5T+seyAY0yS8den\nPqG8oR4nlBUmyX74KfJv8i92YY17b79K4mb9dwPmuuT0Z3csdDuz+RyERf3x\nNQd81+STj+XzDMKdW79G2Swf2/T09+75WQhVN/g0iuqPWK4a7qMcknXA4ESp\nO00RUNe9tInTxJfRt2yWQTcJLVZ3gfy08odiJ7RkGJKlU/CaJNITUKSSFhPi\nEsIoizAAhQmGuL7eJztXjtccsJTRAAjwWpk1noSFcqV8Ot54gsXfhUk8ktZ6\ng7VRveG6lFCeQMXpLly7AP9AvGxUvpo6MbOJGmfjdwaVI2j97LPFjkCqDVEL\niTEI/19emi/qtzcvim3SZyfIjVw0HO8k4VXcCbqGBOF1N5fibmtXE0wZYFk9\nE6Drmtz1Flthqggi2SQ48D2g2cW8t82iT33aTgTCI2DY/b2LaJmU9ptQ1eqa\n4TjKS+8ygTyFh+5APINRLQHnfBFLz9ZIT00LPmpCo0zFOQLtyzmhjaZDnR1O\numqrWEO0qOvuuqP9q7SVVv8GIF6+cT1LzvcOw0PWw2VEKIOjd0QzlMTpPYMq\n0BGgLysJPEchD9PRCa9cJmw/taLyligtn3u79fcFx8b99pz6ONjGlULIuvPr\nndYDivoEk/dj276PCF37zF1zjwY+K7MUkkpg9uOHBlt7qN2XqNw7UxJhQFtl\ne1sf\r\n=F0Ym\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDorwFuKDFxVoyPAKbLCqLZSVVvxe3hh9uztGDleO851wIgP8SzXkiwZCsFpR3xsw6QEouCIN2BFYGfVvgHH5usJiE="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/find-up_6.2.0_1633618618109_0.5318523093873471"}, "_hasShrinkwrap": false}, "6.3.0": {"name": "find-up", "version": "6.3.0", "description": "Find a file or directory by walking up parent directories", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/find-up.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["find", "up", "find-up", "findup", "look-up", "look", "file", "search", "match", "package", "resolve", "parent", "parents", "folder", "directory", "walk", "walking", "path"], "dependencies": {"locate-path": "^7.1.0", "path-exists": "^5.0.0"}, "devDependencies": {"ava": "^3.15.0", "is-path-inside": "^4.0.0", "tempy": "^2.0.0", "tsd": "^0.17.0", "xo": "^0.44.0"}, "types": "./index.d.ts", "gitHead": "576b442b12cdec7cefb4ace23b70e2d10b8ace14", "bugs": {"url": "https://github.com/sindresorhus/find-up/issues"}, "homepage": "https://github.com/sindresorhus/find-up#readme", "_id": "find-up@6.3.0", "_nodeVersion": "16.13.1", "_npmVersion": "8.3.2", "dist": {"integrity": "sha512-v2ZsoEuVHYy8ZIlYqwPe/39Cy+cFDzp4dXPaxNvkEuouymu+2Jbz0PxpKarJHYJTmv2HWT3O382qY8l4jMWthw==", "shasum": "2abab3d3280b2dc7ac10199ef324c4e002c8c790", "tarball": "https://registry.npmjs.org/find-up/-/find-up-6.3.0.tgz", "fileCount": 5, "unpackedSize": 17031, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiAhnuCRA9TVsSAnZWagAAW0kP/jbiguU11v8qNwWDBMVC\nTJeWhZs2t0IxJyYlUQGt/Rzf0Xmv7wXlnC/CzPvFWlezJ1/zkkn20fsBQFbp\n5VubtppZ0lQs05FnYusZ09vN7ILnX/Qq5IlHPvF2L9I6MZWObSiBCTTJOUBd\noj2XyBZjlRFs3Z9stI1RRa8y1RXqWpsisbj/QYJ1bhvX5on3Xa16IPXy8KTs\nYNUXn8+JMIHMlm+Yab6VFr1BhxxXmUw3EgCRC9jqRpWwa0/ptzVFnPWKJ33y\nk4zbYg5ZCa7xNvCZGpIxsDqHLefqE9VLW5XkOzM0ivqAn1hJdC90esH2hbBP\nLdAkx5uYk6B32tpb3K43IKKMPzbUO3TRoS5lARO/oq+61BpURdfOYwBleTRB\nnRWHsEgzIcQiJo3cwLuvIrFrDc+71e8UuZ8tUOcnqULM7RO+qykGwU6aR1U8\n/fIZx0FQaVWfWpoKzY/7DW28CCrwDGfez3wB1ogsTEyccElSqSk6T7tTee/z\nmO5ENtO+H8ahq9gJyzWY/MgWW1Y1gfqJa2RYXp0bvrVCuC5jKNRCPZqneD7G\nAySRYBzOjMCL7Vf8dThTDv1jf/gpulmLekja2eTnwn4fjLRK37lZsFaOLEZX\nTYH4+he0fTt61N51HMCX2P0V2S0DtPiR2KWUxDByiz/d487/I0mMHYrXLrol\nKMaT\r\n=vDIF\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCZAERjY7ZAWa/aqoVdKv/JUQMUA5vepI1hfzqy1e7f5QIgFjRn9MpPgBa9nm03btUqhM9o1tpptHVMJUc2j/L1Z20="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/find-up_6.3.0_1644304878041_0.12911066976937313"}, "_hasShrinkwrap": false}, "7.0.0": {"name": "find-up", "version": "7.0.0", "description": "Find a file or directory by walking up parent directories", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/find-up.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["find", "up", "find-up", "findup", "look-up", "look", "file", "search", "match", "package", "resolve", "parent", "parents", "folder", "directory", "walk", "walking", "path"], "dependencies": {"locate-path": "^7.2.0", "path-exists": "^5.0.0", "unicorn-magic": "^0.1.0"}, "devDependencies": {"ava": "^5.3.1", "is-path-inside": "^4.0.0", "tempy": "^3.1.0", "tsd": "^0.29.0", "xo": "^0.56.0"}, "types": "./index.d.ts", "gitHead": "b733bb70d3aa21b22fa011be8089110d467c317f", "bugs": {"url": "https://github.com/sindresorhus/find-up/issues"}, "homepage": "https://github.com/sindresorhus/find-up#readme", "_id": "find-up@7.0.0", "_nodeVersion": "18.18.2", "_npmVersion": "9.2.0", "dist": {"integrity": "sha512-YyZM99iHrqLKjmt4LJDj58KI+fYyufRLBSYcqycxf//KpBk9FoewoGX0450m9nB44qrZnovzC2oeP5hUibxc/g==", "shasum": "e8dec1455f74f78d888ad65bf7ca13dd2b4e66fb", "tarball": "https://registry.npmjs.org/find-up/-/find-up-7.0.0.tgz", "fileCount": 5, "unpackedSize": 16488, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF6GkYPEK2pVVhfeEkHhifu18bBVy1leJNjpkw3fl17/AiB8rQ75erx6tPPlfn0ttqhXjbPjk88SEYgjRpsfIH7KHg=="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/find-up_7.0.0_1700137356289_0.20490634487913906"}, "_hasShrinkwrap": false}}, "readme": "# find-up\n\n> Find a file or directory by walking up parent directories\n\n## Install\n\n```sh\nnpm install find-up\n```\n\n## Usage\n\n```\n/\n└── Users\n    └── sindresorhus\n        ├── unicorn.png\n        └── foo\n            └── bar\n                ├── baz\n                └── example.js\n```\n\n`example.js`\n\n```js\nimport path from 'node:path';\nimport {findUp, pathExists} from 'find-up';\n\nconsole.log(await findUp('unicorn.png'));\n//=> '/Users/<USER>/unicorn.png'\n\nconsole.log(await findUp(['rainbow.png', 'unicorn.png']));\n//=> '/Users/<USER>/unicorn.png'\n\nconsole.log(await findUp(async directory => {\n\tconst hasUnicorns = await pathExists(path.join(directory, 'unicorn.png'));\n\treturn hasUnicorns && directory;\n}, {type: 'directory'}));\n//=> '/Users/<USER>'\n```\n\n## API\n\n### findUp(name, options?)\n### findUp(matcher, options?)\n\nReturns a `Promise` for either the path or `undefined` if it could not be found.\n\n### findUp([...name], options?)\n\nReturns a `Promise` for either the first path found (by respecting the order of the array) or `undefined` if none could be found.\n\n### findUpMultiple(name, options?)\n### findUpMultiple(matcher, options?)\n\nReturns a `Promise` for either an array of paths or an empty array if none could be found.\n\n### findUpMultiple([...name], options?)\n\nReturns a `Promise` for either an array of the first paths found (by respecting the order of the array) or an empty array if none could be found.\n\n### findUpSync(name, options?)\n### findUpSync(matcher, options?)\n\nReturns a path or `undefined` if it could not be found.\n\n### findUpSync([...name], options?)\n\nReturns the first path found (by respecting the order of the array) or `undefined` if none could be found.\n\n### findUpMultipleSync(name, options?)\n### findUpMultipleSync(matcher, options?)\n\nReturns an array of paths or an empty array if none could be found.\n\n### findUpMultipleSync([...name], options?)\n\nReturns an array of the first paths found (by respecting the order of the array) or an empty array if none could be found.\n\n#### name\n\nType: `string`\n\nThe name of the file or directory to find.\n\n#### matcher\n\nType: `Function`\n\nA function that will be called with each directory until it returns a `string` with the path, which stops the search, or the root directory has been reached and nothing was found. Useful if you want to match files with certain patterns, set of permissions, or other advanced use-cases.\n\nWhen using async mode, the `matcher` may optionally be an async or promise-returning function that returns the path.\n\n#### options\n\nType: `object`\n\n##### cwd\n\nType: `URL | string`\\\nDefault: `process.cwd()`\n\nThe directory to start from.\n\n##### type\n\nType: `string`\\\nDefault: `'file'`\\\nValues: `'file' | 'directory'`\n\nThe type of path to match.\n\n##### allowSymlinks\n\nType: `boolean`\\\nDefault: `true`\n\nAllow symbolic links to match if they point to the chosen path type.\n\n##### stopAt\n\nType: `URL | string`\\\nDefault: Root directory\n\nA directory path where the search halts if no matches are found before reaching this point.\n\n### pathExists(path)\n\nReturns a `Promise<boolean>` of whether the path exists.\n\n### pathExistsSync(path)\n\nReturns a `boolean` of whether the path exists.\n\n#### path\n\nType: `string`\n\nThe path to a file or directory.\n\n### findUpStop\n\nA [`Symbol`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Symbol) that can be returned by a `matcher` function to stop the search and cause `findUp` to immediately return `undefined`. Useful as a performance optimization in case the current working directory is deeply nested in the filesystem.\n\n```js\nimport path from 'node:path';\nimport {findUp, findUpStop} from 'find-up';\n\nawait findUp(directory => {\n\treturn path.basename(directory) === 'work' ? findUpStop : 'logo.png';\n});\n```\n\n## Related\n\n- [find-up-cli](https://github.com/sindresorhus/find-up-cli) - CLI for this module\n- [package-up](https://github.com/sindresorhus/package-up) - Find the closest package.json file\n- [pkg-dir](https://github.com/sindresorhus/pkg-dir) - Find the root directory of an npm package\n- [resolve-from](https://github.com/sindresorhus/resolve-from) - Resolve the path of a module like `require.resolve()` but from a given path\n", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2023-11-16T12:22:36.697Z", "created": "2015-09-01T07:15:16.509Z", "1.0.0": "2015-09-01T07:15:16.509Z", "1.1.0": "2015-11-14T19:05:49.453Z", "1.1.1": "2016-03-02T09:24:19.277Z", "1.1.2": "2016-03-05T17:45:57.070Z", "2.0.0": "2016-09-20T16:13:51.109Z", "2.1.0": "2016-12-02T13:21:51.615Z", "3.0.0": "2018-06-18T09:19:21.364Z", "4.0.0": "2019-05-12T15:28:45.148Z", "4.1.0": "2019-06-17T06:20:34.221Z", "5.0.0": "2020-08-11T18:44:24.748Z", "6.0.0": "2021-08-26T11:21:20.550Z", "6.1.0": "2021-09-26T11:01:53.697Z", "6.2.0": "2021-10-07T14:56:58.251Z", "6.3.0": "2022-02-08T07:21:18.171Z", "7.0.0": "2023-11-16T12:22:36.533Z"}, "homepage": "https://github.com/sindresorhus/find-up#readme", "keywords": ["find", "up", "find-up", "findup", "look-up", "look", "file", "search", "match", "package", "resolve", "parent", "parents", "folder", "directory", "walk", "walking", "path"], "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/find-up.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/find-up/issues"}, "license": "MIT", "readmeFilename": "readme.md", "users": {"ierceg": true, "mrzmmr": true, "bryan.ygf": true, "pmbenjamin": true, "garthk": true, "xrush": true, "alexreg": true, "soenkekluth": true, "jalcine": true, "monkbroc": true, "losymear": true, "djblue": true, "danday74": true, "flumpus-dev": true}}