{"_id": "kind-of", "_rev": "69-4fecd3cf9af0a815ede2f28c4bfa8ea1", "name": "kind-of", "description": "Get the native type of a value.", "dist-tags": {"latest": "6.0.3"}, "versions": {"0.1.0": {"name": "kind-of", "description": "Get the native type of a value.", "version": "0.1.0", "homepage": "https://github.com/jonschlinkert/kind-of", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git://github.com/jonschlinkert/kind-of.git"}, "bugs": {"url": "https://github.com/jonschlinkert/kind-of/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/jonschlinkert/kind-of/blob/master/LICENSE-MIT"}], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha -R spec"}, "devDependencies": {"mocha": "*", "should": "^4.0.4", "verb": ">= 0.2.6"}, "keywords": ["arguments", "array", "boolean", "date", "function", "javascript", "js", "kind", "kind-of", "node", "node.js", "null", "number", "object", "regexp", "string", "type", "type-of", "typeof", "types", "undefined", "util", "utilities", "utility", "utils"], "_id": "kind-of@0.1.0", "_shasum": "bfeec69c1bf15bb2995cb07af8e58ee36ff37c12", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "bfeec69c1bf15bb2995cb07af8e58ee36ff37c12", "tarball": "https://registry.npmjs.org/kind-of/-/kind-of-0.1.0.tgz", "integrity": "sha512-kBxh/KFrxAiU6kqqzIxHJbf1yoZrGhqpkigk6OysXJMNiSTpKrBdeCdp77vFW4IERZpqDKStKnYv4dUxTpeUrA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDfQxFQ1D/6Am5GBuE9z/8ks9W/IDPCIB1GS88q90+zBAIgaLpyQ3MHWIC4B8gQ47MUmaCzhiypxjNLJHodliRG2X8="}]}, "directories": {}}, "0.1.1": {"name": "kind-of", "description": "Get the native type of a value.", "version": "0.1.1", "homepage": "https://github.com/jonschlinkert/kind-of", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git://github.com/jonschlinkert/kind-of.git"}, "bugs": {"url": "https://github.com/jonschlinkert/kind-of/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/jonschlinkert/kind-of/blob/master/LICENSE-MIT"}], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha -R spec"}, "devDependencies": {"mocha": "*", "should": "^4.0.4", "verb": ">= 0.2.6"}, "keywords": ["arguments", "array", "boolean", "date", "function", "javascript", "js", "kind", "kind-of", "node", "node.js", "null", "number", "object", "regexp", "string", "type", "type-of", "typeof", "types", "undefined", "util", "utilities", "utility", "utils"], "_id": "kind-of@0.1.1", "_shasum": "03095e1705ebc7e4db1f6d3e4127612fc7ea659b", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "03095e1705ebc7e4db1f6d3e4127612fc7ea659b", "tarball": "https://registry.npmjs.org/kind-of/-/kind-of-0.1.1.tgz", "integrity": "sha512-PPqXX8UtPgTxxEYAF//A5eYhYW31zeU/Nq8O5WLkR6xPwqNGHEqP5mTZ3Bpzc3xJqaEX7wGuGsEjvS9lVt1iaw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCqzB0wjfFDmXVmNpbT8bxDwjalOnwZk2qSrMMQWxwJUwIhANIvRtwauaYR/3IK8hLSLufveX56ss9kfDkbtaQbfNRU"}]}, "directories": {}}, "0.1.2": {"name": "kind-of", "description": "Get the native type of a value.", "version": "0.1.2", "homepage": "https://github.com/jonschlinkert/kind-of", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git://github.com/jonschlinkert/kind-of.git"}, "bugs": {"url": "https://github.com/jonschlinkert/kind-of/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/jonschlinkert/kind-of/blob/master/LICENSE-MIT"}], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha -R spec"}, "devDependencies": {"benchmarked": "^0.1.3", "chalk": "^0.5.1", "glob": "^4.0.6", "mocha": "*", "should": "^4.0.4", "type-of": "^2.0.1", "typeof": "^1.0.0", "verb": ">= 0.2.6"}, "keywords": ["arguments", "array", "boolean", "date", "function", "javascript", "js", "kind", "kind-of", "node", "node.js", "null", "number", "object", "regexp", "string", "type", "type-of", "typeof", "types", "undefined", "util", "utilities", "utility", "utils"], "_id": "kind-of@0.1.2", "_shasum": "07bd601cc9433f5d79bd5edd2031ed155f0604a4", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "07bd601cc9433f5d79bd5edd2031ed155f0604a4", "tarball": "https://registry.npmjs.org/kind-of/-/kind-of-0.1.2.tgz", "integrity": "sha512-YQVwwzqzVO4HhTxM5fvTJ5Qe47ln64IM7wa1UP9R5GNNHqY3H3QThzpMDPorp9HLrsMuN2blNhI7bTQV+BMEKA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBlc/vtCK55jrKaE1I6yDd/Zxn/UvATlgBRAim0LoVQjAiEAgvpUIymoSc2kcLW5ASADVCk5UXLKKd2PcftZ+xyxuFY="}]}, "directories": {}}, "1.0.0": {"name": "kind-of", "description": "Get the native type of a value.", "version": "1.0.0", "homepage": "https://github.com/jonschlinkert/kind-of", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git://github.com/jonschlinkert/kind-of.git"}, "bugs": {"url": "https://github.com/jonschlinkert/kind-of/issues"}, "license": {"type": "MIT", "url": "https://github.com/jonschlinkert/kind-of/blob/master/LICENSE"}, "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha -R spec"}, "devDependencies": {"benchmarked": "^0.1.3", "chalk": "^0.5.1", "glob": "^4.3.5", "should": "^4.6.1", "type-of": "^2.0.1", "typeof": "^1.0.0"}, "keywords": ["arguments", "array", "boolean", "check", "date", "function", "is", "is-type", "is-type-of", "kind", "kind-of", "number", "object", "regexp", "string", "test", "type", "type-of", "typeof", "types"], "gitHead": "062a13f09e0f5213379dccdc56bbcda4a524b573", "_id": "kind-of@1.0.0", "_shasum": "de248bdb9baa0a039f057211f38dd3e0455f5570", "_from": ".", "_npmVersion": "1.4.23", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "de248bdb9baa0a039f057211f38dd3e0455f5570", "tarball": "https://registry.npmjs.org/kind-of/-/kind-of-1.0.0.tgz", "integrity": "sha512-Qr31f76kkcL9hw3U//UIiFjAYHbl94B/UbGpW0/F0LzsU+ZlDvSBmAca4bt99a1GkUZ3obLJT9EDUbdsJ3lxUw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIH48OEjhJfwVHo7+nsXY9BhbZJsgA+1xxAPjgKDs4F/JAiEA2JqfF7UHH0ATci9xsY73dK/8kWAas/EK6qtHqc5n5pI="}]}, "directories": {}}, "1.0.1": {"name": "kind-of", "description": "Get the native type of a value.", "version": "1.0.1", "homepage": "https://github.com/jonschlinkert/kind-of", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git://github.com/jonschlinkert/kind-of.git"}, "bugs": {"url": "https://github.com/jonschlinkert/kind-of/issues"}, "license": {"type": "MIT", "url": "https://github.com/jonschlinkert/kind-of/blob/master/LICENSE"}, "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha -R spec"}, "devDependencies": {"benchmarked": "^0.1.3", "chalk": "^0.5.1", "glob": "^4.3.5", "should": "^4.6.1", "type-of": "^2.0.1", "typeof": "^1.0.0"}, "keywords": ["arguments", "array", "boolean", "check", "date", "function", "is", "is-type", "is-type-of", "kind", "kind-of", "number", "object", "regexp", "string", "test", "type", "type-of", "typeof", "types"], "gitHead": "062a13f09e0f5213379dccdc56bbcda4a524b573", "_id": "kind-of@1.0.1", "_shasum": "adbe4591ca983133cb58c44e028eb44c117ca4f5", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "adbe4591ca983133cb58c44e028eb44c117ca4f5", "tarball": "https://registry.npmjs.org/kind-of/-/kind-of-1.0.1.tgz", "integrity": "sha512-siCa2N42mzgfkaQywjZTglFN4pCW68E3ZK8W3Cpc4MqhvsBnRz7YACoovn8oD1oKHbul5op1BR3psy9AXJ/new==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBYv2BvPeGroCuO7c+zMJ1LLSwZ1KmLz+szXgxBfNXhJAiEAiKoYOepZG0sXMDsD27suY0ZGE0evDtAszyn0barQ/OY="}]}, "directories": {}}, "1.1.0": {"name": "kind-of", "description": "Get the native type of a value.", "version": "1.1.0", "homepage": "https://github.com/jonschlinkert/kind-of", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git://github.com/jonschlinkert/kind-of.git"}, "bugs": {"url": "https://github.com/jonschlinkert/kind-of/issues"}, "license": {"type": "MIT", "url": "https://github.com/jonschlinkert/kind-of/blob/master/LICENSE"}, "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"benchmarked": "^0.1.3", "chalk": "^0.5.1", "glob": "^4.3.5", "should": "^4.6.1", "type-of": "^2.0.1", "typeof": "^1.0.0"}, "keywords": ["arguments", "array", "boolean", "check", "date", "function", "is", "is-type", "is-type-of", "kind", "kind-of", "number", "object", "regexp", "string", "test", "type", "type-of", "typeof", "types"], "gitHead": "4ee1c34ffba8743451ddef17b4a6588468ba187d", "_id": "kind-of@1.1.0", "_shasum": "140a3d2d41a36d2efcfa9377b62c24f8495a5c44", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "140a3d2d41a36d2efcfa9377b62c24f8495a5c44", "tarball": "https://registry.npmjs.org/kind-of/-/kind-of-1.1.0.tgz", "integrity": "sha512-aUH6ElPnMGon2/YkxRIigV32MOpTVcoXQ1Oo8aYn40s+sJ3j+0gFZsT8HKDcxNy7Fi9zuquWtGaGAahOdv5p/g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDt61L4ih70QOKHGXGgwFd2tv7P9y9XIveIAMFD5LXlPAiEAgvSY7/HTZKvRI7cMVfd5L9wGi0NaAgH2+FxZU/v3cs8="}]}, "directories": {}}, "2.0.0": {"name": "kind-of", "description": "Get the native type of a value.", "version": "2.0.0", "homepage": "https://github.com/jonschlinkert/kind-of", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/kind-of.git"}, "bugs": {"url": "https://github.com/jonschlinkert/kind-of/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"benchmarked": "^0.1.3", "chalk": "^0.5.1", "glob": "^4.3.5", "mocha": "^2.2.5", "should": "^4.6.1", "type-of": "^2.0.1", "typeof": "^1.0.0"}, "keywords": ["arguments", "array", "boolean", "check", "date", "function", "is", "is-type", "is-type-of", "kind", "kind-of", "number", "object", "regexp", "string", "test", "type", "type-of", "typeof", "types"], "gitHead": "63b4b8a69fff81e9f0020e9faa43920eeff93985", "_id": "kind-of@2.0.0", "_shasum": "9a67651a98cb9a5d5ea3b831241360f8e40518d9", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "9a67651a98cb9a5d5ea3b831241360f8e40518d9", "tarball": "https://registry.npmjs.org/kind-of/-/kind-of-2.0.0.tgz", "integrity": "sha512-7bvJIiSr+oJhWrr7l08YGYedpPokchNaiTnnQ3ZNDU3H8x++PZBiBSGh1eViRT+SRRS3fJoIR/VJ06NVx/6NQg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDNLsNzoMBh4/fGa5cJcmLVfA0iTKb6wj2cl6vVEFqbgAIhANYwQZ6AhjJFbnUplWcBTUrDxznTU0OAFtBYxRsbvVaa"}]}, "directories": {}}, "2.0.1": {"name": "kind-of", "description": "Get the native type of a value.", "version": "2.0.1", "homepage": "https://github.com/jonschlinkert/kind-of", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/kind-of.git"}, "bugs": {"url": "https://github.com/jonschlinkert/kind-of/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha", "prepublish": "browserify -o browser.js -e index.js -s index --bare"}, "devDependencies": {"benchmarked": "^0.1.3", "chalk": "^0.5.1", "glob": "^4.3.5", "mocha": "^2.2.5", "should": "^4.6.1", "type-of": "^2.0.1", "typeof": "^1.0.0"}, "keywords": ["arguments", "array", "boolean", "check", "date", "function", "is", "is-type", "is-type-of", "kind", "kind-of", "number", "object", "regexp", "string", "test", "type", "type-of", "typeof", "types"], "dependencies": {"is-buffer": "^1.0.2"}, "gitHead": "09fcb8b8384932bc5a0009e0b3811998b0387629", "_id": "kind-of@2.0.1", "_shasum": "018ec7a4ce7e3a86cb9141be519d24c8faa981b5", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "dist": {"shasum": "018ec7a4ce7e3a86cb9141be519d24c8faa981b5", "tarball": "https://registry.npmjs.org/kind-of/-/kind-of-2.0.1.tgz", "integrity": "sha512-0u8i1NZ/mg0b+W3MGGw5I7+6Eib2nx72S/QvXa0hYjEkjTknYmEYQJwGu3mLC0BrhtJjtQafTkyRUQ75Kx0LVg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGq+yJUyh9RBT4ER1qK38Cv7c2wX6C3KbyNDmQK9YqnIAiBQMT+MBfoo+LPwb/WJPawy0PoEqb2gyxXW6FZAo5EIYw=="}]}, "directories": {}}, "3.0.0": {"name": "kind-of", "description": "Get the native type of a value.", "version": "3.0.0", "homepage": "https://github.com/jonschlinkert/kind-of", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/kind-of.git"}, "bugs": {"url": "https://github.com/jonschlinkert/kind-of/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha", "prepublish": "browserify -o browser.js -e index.js -s index --bare"}, "dependencies": {"is-buffer": "^1.0.2"}, "devDependencies": {"ansi-bold": "^0.1.1", "benchmarked": "^0.1.3", "browserify": "^11.0.1", "chalk": "^0.5.1", "glob": "^4.3.5", "mocha": "*", "should": "*", "type-of": "^2.0.1", "typeof": "^1.0.0"}, "keywords": ["arguments", "array", "boolean", "check", "date", "function", "is", "is-type", "is-type-of", "kind", "kind-of", "number", "object", "regexp", "string", "test", "type", "type-of", "typeof", "types"], "verb": {"related": {"list": ["is-glob", "is-primitive", "is-number"]}}, "gitHead": "08215c90e237beca1ec297a328db1db0a8c2e598", "_id": "kind-of@3.0.0", "_shasum": "b448471a42ef7eac1246b6490cc5f4318aa7f592", "_from": ".", "_npmVersion": "3.3.6", "_nodeVersion": "5.0.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "dist": {"shasum": "b448471a42ef7eac1246b6490cc5f4318aa7f592", "tarball": "https://registry.npmjs.org/kind-of/-/kind-of-3.0.0.tgz", "integrity": "sha512-iOYzPZ0YpP9YHdf47SUGe3h0XMGYsRwmI0BGXsKEMDzIyPXf2Ei+djakkxC4JuxwTipBM0qJc4GKCX2901gt9g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD9mlFtK9dFqX6UC6XDcq2TBX1CHP+5N4MDaMfJjC4+EQIgTkkVFCWYH7kurXRJ5NhtwIvdD22eiLmfz5drMJfktGQ="}]}, "directories": {}}, "3.0.1": {"name": "kind-of", "description": "Get the native type of a value.", "version": "3.0.1", "homepage": "https://github.com/jonschlinkert/kind-of", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/kind-of.git"}, "bugs": {"url": "https://github.com/jonschlinkert/kind-of/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha", "prepublish": "browserify -o browser.js -e index.js -s index --bare"}, "dependencies": {"is-buffer": "^1.0.2"}, "devDependencies": {"ansi-bold": "^0.1.1", "benchmarked": "^0.1.3", "browserify": "^11.0.1", "chalk": "^0.5.1", "glob": "^4.3.5", "mocha": "*", "should": "*", "type-of": "^2.0.1", "typeof": "^1.0.0"}, "keywords": ["arguments", "array", "boolean", "check", "date", "function", "is", "is-type", "is-type-of", "kind", "kind-of", "number", "object", "regexp", "string", "test", "type", "type-of", "typeof", "types"], "verb": {"related": {"list": ["is-glob", "is-primitive", "is-number"]}}, "gitHead": "adcab2046727f643c07b34a0569ef9dccdc5ee58", "_id": "kind-of@3.0.1", "_shasum": "9a48e2562268501288d4f3922c7d1573f463dd1b", "_from": ".", "_npmVersion": "3.3.6", "_nodeVersion": "5.0.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "dist": {"shasum": "9a48e2562268501288d4f3922c7d1573f463dd1b", "tarball": "https://registry.npmjs.org/kind-of/-/kind-of-3.0.1.tgz", "integrity": "sha512-wQVKQCEhZc09HQ40gnOTwc1tzHa4803j0XSPQi2ZYMwQbGK5Ll15OJPkv/VhNLyMvKmA/wVMMNWvemEAKr2svw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBgxyLtHEVhqfevELYLlC5cvL33Ou5sn8IM40dgYYxT5AiB5LSBzFiYZ94K8UxjRyAOcqIU1k9lNw6fQWaSd/qIysg=="}]}, "directories": {}}, "3.0.2": {"name": "kind-of", "description": "Get the native type of a value.", "version": "3.0.2", "homepage": "https://github.com/jonschlinkert/kind-of", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/kind-of.git"}, "bugs": {"url": "https://github.com/jonschlinkert/kind-of/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha", "prepublish": "browserify -o browser.js -e index.js -s index --bare"}, "dependencies": {"is-buffer": "^1.0.2"}, "devDependencies": {"ansi-bold": "^0.1.1", "benchmarked": "^0.1.3", "browserify": "^11.0.1", "glob": "^4.3.5", "mocha": "*", "should": "*", "type-of": "^2.0.1", "typeof": "^1.0.0"}, "keywords": ["arguments", "array", "boolean", "check", "date", "function", "is", "is-type", "is-type-of", "kind", "kind-of", "number", "object", "regexp", "string", "test", "type", "type-of", "typeof", "types"], "verb": {"related": {"list": ["is-glob", "is-primitive", "is-number"]}}, "gitHead": "917a9701737a64abe0f77441c9ef21afca5ab397", "_id": "kind-of@3.0.2", "_shasum": "187db427046e7e90945692e6768668bd6900dea0", "_from": ".", "_npmVersion": "3.3.6", "_nodeVersion": "5.0.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "dist": {"shasum": "187db427046e7e90945692e6768668bd6900dea0", "tarball": "https://registry.npmjs.org/kind-of/-/kind-of-3.0.2.tgz", "integrity": "sha512-lIgHO5KRO41H1bpNkV8yb1c13xfP6VEB5zpq7LdlQ0EJWsLDnQ0RWHMSwDNoSysbnQZo6ALW7k2Ri3M/Ygwk8A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC6GyJYgtzFTXQ9v3Uq5a1gUSeqzjuUXDkeXicvcofR0wIhAKd75CNLDU8SUlUEXmcviAAVNlj6kyQg5H2yrvDEIJrh"}]}, "directories": {}}, "3.0.3": {"name": "kind-of", "description": "Get the native type of a value.", "version": "3.0.3", "homepage": "https://github.com/jonschlinkert/kind-of", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/kind-of.git"}, "bugs": {"url": "https://github.com/jonschlinkert/kind-of/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha", "prepublish": "browserify -o browser.js -e index.js -s index --bare"}, "dependencies": {"is-buffer": "^1.0.2"}, "devDependencies": {"ansi-bold": "^0.1.1", "benchmarked": "^0.1.3", "browserify": "^11.0.1", "glob": "^4.3.5", "gulp-format-md": "^0.1.9", "mocha": "*", "should": "*", "type-of": "^2.0.1", "typeof": "^1.0.0"}, "keywords": ["arguments", "array", "boolean", "check", "date", "function", "is", "is-type", "is-type-of", "kind", "kind-of", "number", "object", "regexp", "string", "test", "type", "type-of", "typeof", "types"], "verb": {"related": {"list": ["is-glob", "is-number", "is-primitive"]}, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "reflinks": ["verb"]}, "gitHead": "c1023c4839a91abd580a4e71fd0763f7fc2ad3f4", "_id": "kind-of@3.0.3", "_shasum": "c61608747d815b0362556db3276362a7a38aded3", "_from": ".", "_npmVersion": "3.6.0", "_nodeVersion": "5.5.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "dist": {"shasum": "c61608747d815b0362556db3276362a7a38aded3", "tarball": "https://registry.npmjs.org/kind-of/-/kind-of-3.0.3.tgz", "integrity": "sha512-gJdIn9e0spWl92bwG++r+ZDLl/DPrOFD6R2GBn1YY7XZnVj20IiB8xCrdgvIQ0EeaK5C/KRoqwqeh00FdgrJ8g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDqYTZr1/9eWp1mPaZP/5lDiwnLC+xorq00fGq4XP3+7QIhAO6YeEIYQjGULO1Y4NDJKrQcIq1KLYRED1kCD6Ndxr/O"}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/kind-of-3.0.3.tgz_1462262974577_0.29414567071944475"}, "directories": {}}, "3.0.4": {"name": "kind-of", "description": "Get the native type of a value.", "version": "3.0.4", "homepage": "https://github.com/jonschlinkert/kind-of", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/kind-of.git"}, "bugs": {"url": "https://github.com/jonschlinkert/kind-of/issues"}, "license": "MIT", "files": ["index.js", "LICENSE", "README.md"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha", "prepublish": "browserify -o browser.js -e index.js -s index --bare"}, "dependencies": {"is-buffer": "^1.0.2"}, "devDependencies": {"ansi-bold": "^0.1.1", "benchmarked": "^0.2.5", "browserify": "^13.1.0", "glob": "^7.0.5", "gulp-format-md": "^0.1.9", "mocha": "^2.5.3", "type-of": "^2.0.1", "typeof": "^1.0.0"}, "keywords": ["arguments", "array", "boolean", "check", "date", "function", "is", "is-type", "is-type-of", "kind", "kind-of", "number", "object", "of", "regexp", "string", "test", "type", "type-of", "typeof", "types"], "verb": {"related": {"list": ["is-glob", "is-number", "is-primitive"]}, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "reflinks": ["verb"]}, "gitHead": "8f93765a6bbbe350e1ba68e790d1ff337b91331a", "_id": "kind-of@3.0.4", "_shasum": "7b8ecf18a4e17f8269d73b501c9f232c96887a74", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.3.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "dist": {"shasum": "7b8ecf18a4e17f8269d73b501c9f232c96887a74", "tarball": "https://registry.npmjs.org/kind-of/-/kind-of-3.0.4.tgz", "integrity": "sha512-2zjXegUhxKJhXI/BKX9bSK1iXlA7Zi+vOUD9KToLn8f26LxhTx7ZWydfC8NlIYvfKMXZvbqOUVNRtETEhFQ78w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDFgNnocojePAbePctovTlq8CyP7UbJOa2/MVuelYHJnQIgZjuBz0PMZaOzPtyzeOx0YnDT2ilEfhhDznmSGgCiAjI="}]}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/kind-of-3.0.4.tgz_1469811230830_0.8813814013265073"}, "directories": {}}, "3.1.0": {"name": "kind-of", "description": "Get the native type of a value.", "version": "3.1.0", "homepage": "https://github.com/jonschlinkert/kind-of", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://dtothefp.github.io/me"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://miguelmota.com"}, {"name": "<PERSON>", "url": "http://about.me/peterdehaan"}], "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/kind-of.git"}, "bugs": {"url": "https://github.com/jonschlinkert/kind-of/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha", "prepublish": "browserify -o browser.js -e index.js -s index --bare"}, "dependencies": {"is-buffer": "^1.0.2"}, "devDependencies": {"ansi-bold": "^0.1.1", "benchmarked": "^0.2.5", "browserify": "^13.1.0", "glob": "^7.0.5", "gulp-format-md": "^0.1.9", "mocha": "^2.5.3", "type-of": "^2.0.1", "typeof": "^1.0.0"}, "keywords": ["arguments", "array", "boolean", "check", "date", "function", "is", "is-type", "is-type-of", "kind", "kind-of", "number", "object", "of", "regexp", "string", "test", "type", "type-of", "typeof", "types"], "verb": {"related": {"list": ["is-glob", "is-number", "is-primitive"]}, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "reflinks": ["verb"]}, "gitHead": "eb57ad426b39f25902260f315a1f4ae50d2f760e", "_id": "kind-of@3.1.0", "_shasum": "475d698a5e49ff5e53d14e3e732429dc8bf4cf47", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.7.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "dist": {"shasum": "475d698a5e49ff5e53d14e3e732429dc8bf4cf47", "tarball": "https://registry.npmjs.org/kind-of/-/kind-of-3.1.0.tgz", "integrity": "sha512-vPLNVMr7QjxWc6U+pX6I5l9YG3y8cJTrlth+wvi89XhKIBbr7m8V8Q6RoNHA1wwYnO1CnpRSNJN98v4OopGjwg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC7HfHTZWOzYrhR68SOvXxQwcJb+emc7C+6ZKaZi83nPQIhAJ7CuPTQFURH591yNZnb5yotbrElYHrbYpWQJ0cMYaC2"}]}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/kind-of-3.1.0.tgz_1481093733086_0.6179714468307793"}, "directories": {}}, "3.2.0": {"name": "kind-of", "description": "Get the native type of a value.", "version": "3.2.0", "homepage": "https://github.com/jonschlinkert/kind-of", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://dtothefp.github.io/me"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON>", "url": "kensheedlo.com"}, {"name": "laggingreflex", "url": "https://github.com/laggingreflex"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://miguelmota.com"}, {"name": "<PERSON>", "url": "http://about.me/peterdehaan"}], "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/kind-of.git"}, "bugs": {"url": "https://github.com/jonschlinkert/kind-of/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha", "prepublish": "browserify -o browser.js -e index.js -s index --bare"}, "dependencies": {"is-buffer": "^1.1.5"}, "devDependencies": {"ansi-bold": "^0.1.1", "benchmarked": "^1.0.0", "glob": "^7.1.1", "gulp-format-md": "^0.1.12", "mocha": "^3.3.0", "type-of": "^2.0.1", "typeof": "^1.0.0"}, "keywords": ["arguments", "array", "boolean", "check", "date", "function", "is", "is-type", "is-type-of", "kind", "kind-of", "number", "object", "of", "regexp", "string", "test", "type", "type-of", "typeof", "types"], "verb": {"related": {"list": ["is-glob", "is-number", "is-primitive"]}, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "reflinks": ["verb"]}, "gitHead": "9c9423745e2d7fa1a72ed7edf3c2113fe0fa2296", "_id": "kind-of@3.2.0", "_shasum": "b58abe4d5c044ad33726a8c1525b48cf891bff07", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.7.3", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "dist": {"shasum": "b58abe4d5c044ad33726a8c1525b48cf891bff07", "tarball": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.0.tgz", "integrity": "sha512-ZtOIc7vHdaDUN/bZiECSA5/oewCa1q/eOH3FlmY8RiaVYgF1+3LBgtg6ursKOJ3UE3uzA9ELMKrYuvG25cMJbw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCK487LU6Zp6WKy3r8+jA/Ypcf0bpRK9qr/MFo77Mg5WAIhANTwMpRXpJddpiGtgZK3Q/MjYE/3dCl5JzlQZsIkEQl4"}]}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/kind-of-3.2.0.tgz_1493120580182_0.32957054767757654"}, "directories": {}}, "3.2.1": {"name": "kind-of", "description": "Get the native type of a value.", "version": "3.2.1", "homepage": "https://github.com/jonschlinkert/kind-of", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "https://dtothefp.github.io/me"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON>", "url": "kensheedlo.com"}, {"name": "laggingreflex", "url": "https://github.com/laggingreflex"}, {"name": "<PERSON>", "url": "https://miguelmota.com"}, {"name": "<PERSON>", "url": "http://about.me/peterdehaan"}], "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/kind-of.git"}, "bugs": {"url": "https://github.com/jonschlinkert/kind-of/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha", "prepublish": "browserify -o browser.js -e index.js -s index --bare"}, "dependencies": {"is-buffer": "^1.1.5"}, "devDependencies": {"ansi-bold": "^0.1.1", "benchmarked": "^1.0.0", "browserify": "^14.3.0", "glob": "^7.1.1", "gulp-format-md": "^0.1.12", "mocha": "^3.3.0", "type-of": "^2.0.1", "typeof": "^1.0.0"}, "keywords": ["arguments", "array", "boolean", "check", "date", "function", "is", "is-type", "is-type-of", "kind", "kind-of", "number", "object", "of", "regexp", "string", "test", "type", "type-of", "typeof", "types"], "verb": {"related": {"list": ["is-glob", "is-number", "is-primitive"]}, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "reflinks": ["verb"]}, "gitHead": "5bfd67bfaba83a5362f7c5db1dc4133e8ff1d623", "_id": "kind-of@3.2.1", "_shasum": "9713fb3aea9ed3ba2fba907f0dfc4eb8224527f2", "_from": ".", "_npmVersion": "4.5.0", "_nodeVersion": "7.7.3", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "dist": {"shasum": "9713fb3aea9ed3ba2fba907f0dfc4eb8224527f2", "tarball": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.1.tgz", "integrity": "sha512-YsWU3dhGaGBPm6+7UxQ44RmYDP1QqQYMg7ZWcrRPzl7gxLV1UnvTsfskv/99EMnOFYyS3bVLpxHR3C46il4a0g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDQjhOoVLUy+jsEro1X4huPPcZlJIr9PGW8iPu41HWcNAiBYnwVPE4to/xuzLFshXhwzUvF8Bwx+1qyizppjyksd2A=="}]}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/kind-of-3.2.1.tgz_1494958490254_0.12150783301331103"}, "directories": {}}, "3.2.2": {"name": "kind-of", "description": "Get the native type of a value.", "version": "3.2.2", "homepage": "https://github.com/jonschlinkert/kind-of", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "https://dtothefp.github.io/me"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON>", "url": "kensheedlo.com"}, {"name": "laggingreflex", "url": "https://github.com/laggingreflex"}, {"name": "<PERSON>", "url": "https://miguelmota.com"}, {"name": "<PERSON>", "url": "http://about.me/peterdehaan"}], "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/kind-of.git"}, "bugs": {"url": "https://github.com/jonschlinkert/kind-of/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha", "prepublish": "browserify -o browser.js -e index.js -s index --bare"}, "dependencies": {"is-buffer": "^1.1.5"}, "devDependencies": {"ansi-bold": "^0.1.1", "benchmarked": "^1.0.0", "browserify": "^14.3.0", "glob": "^7.1.1", "gulp-format-md": "^0.1.12", "mocha": "^3.3.0", "type-of": "^2.0.1", "typeof": "^1.0.0"}, "keywords": ["arguments", "array", "boolean", "check", "date", "function", "is", "is-type", "is-type-of", "kind", "kind-of", "number", "object", "of", "regexp", "string", "test", "type", "type-of", "typeof", "types"], "verb": {"related": {"list": ["is-glob", "is-number", "is-primitive"]}, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "reflinks": ["verb"]}, "gitHead": "0ffe67cf12f5396047c1bacf04232b7deeb24063", "_id": "kind-of@3.2.2", "_shasum": "31ea21a734bab9bbb0f32466d893aea51e4a3c64", "_from": ".", "_npmVersion": "4.5.0", "_nodeVersion": "7.7.3", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "dist": {"shasum": "31ea21a734bab9bbb0f32466d893aea51e4a3c64", "tarball": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha512-NOW9QQXMoZGg/oqnVNoNTTIFEIid1627WCffUBJEdMxYApq7mNE7CpzucIPc+ZQg25Phej7IJSmX3hO+oblOtQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA+PkCBtTWJTJGp6BmBDfGldIMKBbiUOjpqLF17rq5ChAiByW4/OwEAm6P8sMzKZeuAe6BJfOo7Hw7VOn765GyKKzQ=="}]}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/kind-of-3.2.2.tgz_1494958899918_0.23780996026471257"}, "directories": {}}, "4.0.0": {"name": "kind-of", "description": "Get the native type of a value.", "version": "4.0.0", "homepage": "https://github.com/jonschlinkert/kind-of", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "https://dtothefp.github.io/me"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON>", "url": "kensheedlo.com"}, {"name": "laggingreflex", "url": "https://github.com/laggingreflex"}, {"name": "<PERSON>", "url": "https://miguelmota.com"}, {"name": "<PERSON>", "url": "http://about.me/peterdehaan"}], "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/kind-of.git"}, "bugs": {"url": "https://github.com/jonschlinkert/kind-of/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha", "prepublish": "browserify -o browser.js -e index.js -s index --bare"}, "dependencies": {"is-buffer": "^1.1.5"}, "devDependencies": {"ansi-bold": "^0.1.1", "benchmarked": "^1.1.1", "browserify": "^14.3.0", "glob": "^7.1.1", "gulp-format-md": "^0.1.12", "mocha": "^3.4.1", "type-of": "^2.0.1", "typeof": "^1.0.0"}, "keywords": ["arguments", "array", "boolean", "check", "date", "function", "is", "is-type", "is-type-of", "kind", "kind-of", "number", "object", "of", "regexp", "string", "test", "type", "type-of", "typeof", "types"], "verb": {"related": {"list": ["is-glob", "is-number", "is-primitive"]}, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "reflinks": ["verb"]}, "gitHead": "30bee16e8dffa67417ba35d31bd9bc31517a2d83", "_id": "kind-of@4.0.0", "_shasum": "20813df3d712928b207378691a45066fae72dd57", "_from": ".", "_npmVersion": "4.6.1", "_nodeVersion": "7.7.3", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "dist": {"shasum": "20813df3d712928b207378691a45066fae72dd57", "tarball": "https://registry.npmjs.org/kind-of/-/kind-of-4.0.0.tgz", "integrity": "sha512-24XsCxmEbRwEDbz/qz3stgin8TTzZ1ESR56OMCN0ujYg+vRutNSiOj9bHH9u85DKgXguraugV5sFuvbD4FW/hw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICEDO7b4H78ymvyp0ZQ50tktktRPoIpYh1QdiEFfo5ETAiEAiJoKd6jXsN5++p+lhuuGjjfMZOsccrgyrAGxlWs4/T0="}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/kind-of-4.0.0.tgz_1495218191000_0.2416100692935288"}, "directories": {}}, "5.0.0": {"name": "kind-of", "description": "Get the native type of a value.", "version": "5.0.0", "homepage": "https://github.com/jonschlinkert/kind-of", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON><PERSON><PERSON> Reagent", "url": "https://i.am.charlike.online"}, {"name": "<PERSON>", "url": "https://dtothefp.github.io/me"}, {"name": "<PERSON>", "url": "https://twitter.com/aretecode"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON>", "url": "kensheedlo.com"}, {"name": "laggingreflex", "url": "https://github.com/laggingreflex"}, {"name": "<PERSON>", "url": "https://miguelmota.com"}, {"name": "<PERSON>", "url": "http://about.me/peterdehaan"}], "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/kind-of.git"}, "bugs": {"url": "https://github.com/jonschlinkert/kind-of/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha", "prepublish": "browserify -o browser.js -e index.js -s index --bare"}, "devDependencies": {"ansi-bold": "^0.1.1", "benchmarked": "^1.1.1", "gulp-format-md": "^0.1.12", "matched": "^0.4.4", "mocha": "^3.4.2", "type-of": "^2.0.1", "typeof": "^1.0.0"}, "keywords": ["arguments", "array", "boolean", "check", "date", "function", "is", "is-type", "is-type-of", "kind", "kind-of", "number", "object", "of", "regexp", "string", "test", "type", "type-of", "typeof", "types"], "verb": {"related": {"list": ["is-glob", "is-number", "is-primitive"]}, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "reflinks": ["verb", "type-of", "typeof"]}, "gitHead": "1f3d068629eedec33fc0c7426d9a251791bde6fa", "_id": "kind-of@5.0.0", "_npmVersion": "5.0.3", "_nodeVersion": "7.7.3", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "dist": {"integrity": "sha512-3cdQgopAksY6yov9pwjIhZ46ZuQajM25YhFfbCX/tbIJ0ILpqOyz5tImIResGL8uaMSudk6pGzREGBk89T4BfQ==", "shasum": "9038420f740b2e836ce48b34617bcb855947f2a9", "tarball": "https://registry.npmjs.org/kind-of/-/kind-of-5.0.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFhbKhR3k+K/fesAHJJdDlg5vwcCEANWxFwsp9/pm1JCAiBylWxWQRzHBkPrh5Elti89FysRhdhgyDHmAwSbTs1eKw=="}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/kind-of-5.0.0.tgz_1498078433234_0.4707444754894823"}, "directories": {}}, "5.0.1": {"name": "kind-of", "description": "Get the native type of a value.", "version": "5.0.1", "homepage": "https://github.com/jonschlinkert/kind-of", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON><PERSON><PERSON> Reagent", "url": "https://i.am.charlike.online"}, {"name": "<PERSON>", "url": "https://dtothefp.github.io/me"}, {"name": "<PERSON>", "url": "https://twitter.com/aretecode"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON>", "url": "kensheedlo.com"}, {"name": "laggingreflex", "url": "https://github.com/laggingreflex"}, {"name": "<PERSON>", "url": "https://miguelmota.com"}, {"name": "<PERSON>", "url": "http://about.me/peterdehaan"}], "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/kind-of.git"}, "bugs": {"url": "https://github.com/jonschlinkert/kind-of/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha", "prepublish": "browserify -o browser.js -e index.js -s index --bare"}, "devDependencies": {"ansi-bold": "^0.1.1", "benchmarked": "^1.1.1", "browserify": "^14.4.0", "gulp-format-md": "^0.1.12", "matched": "^0.4.4", "mocha": "^3.4.2", "type-of": "^2.0.1", "typeof": "^1.0.0"}, "keywords": ["arguments", "array", "boolean", "check", "date", "function", "is", "is-type", "is-type-of", "kind", "kind-of", "number", "object", "of", "regexp", "string", "test", "type", "type-of", "typeof", "types"], "verb": {"related": {"list": ["is-glob", "is-number", "is-primitive"]}, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "reflinks": ["verb", "type-of", "typeof"]}, "gitHead": "3b9a4ee6abe4644b4090aa0392bd657a8afe3658", "_id": "kind-of@5.0.1", "_npmVersion": "5.3.0", "_nodeVersion": "7.7.3", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"email": "<EMAIL>", "name": "doowb"}, {"email": "<EMAIL>", "name": "j<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"integrity": "sha512-36SC1Z3wwdlAYNRISZMaTSXaas3aD7dBd2O1OBBs0M/kUUkweXg+HAB+uaH6ACwcS0dkjl3rSKjI/oT42VOSUw==", "shasum": "38d16a3775ad820b56f5ac47b8d71a5bdf4418ce", "tarball": "https://registry.npmjs.org/kind-of/-/kind-of-5.0.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEg2y7QOkqDEumHv8orowMUAeCuoerfDTZfbbCUgWKAHAiEAhpRaxcwTxwM10qptPYj5f5nrcFCvakmLSYIUqjSgJgE="}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/kind-of-5.0.1.tgz_1501502642133_0.6259083212353289"}, "directories": {}}, "5.0.2": {"name": "kind-of", "description": "Get the native type of a value.", "version": "5.0.2", "homepage": "https://github.com/jonschlinkert/kind-of", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON><PERSON><PERSON> Reagent", "url": "https://i.am.charlike.online"}, {"name": "<PERSON>", "url": "https://dtothefp.github.io/me"}, {"name": "<PERSON>", "url": "https://twitter.com/aretecode"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON>", "url": "kensheedlo.com"}, {"name": "laggingreflex", "url": "https://github.com/laggingreflex"}, {"name": "<PERSON>", "url": "https://miguelmota.com"}, {"name": "<PERSON>", "url": "http://about.me/peterdehaan"}], "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/kind-of.git"}, "bugs": {"url": "https://github.com/jonschlinkert/kind-of/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha", "prepublish": "browserify -o browser.js -e index.js -s index --bare"}, "devDependencies": {"ansi-bold": "^0.1.1", "benchmarked": "^1.1.1", "browserify": "^14.4.0", "gulp-format-md": "^0.1.12", "matched": "^0.4.4", "mocha": "^3.4.2", "type-of": "^2.0.1", "typeof": "^1.0.0"}, "keywords": ["arguments", "array", "boolean", "check", "date", "function", "is", "is-type", "is-type-of", "kind", "kind-of", "number", "object", "of", "regexp", "string", "test", "type", "type-of", "typeof", "types"], "verb": {"related": {"list": ["is-glob", "is-number", "is-primitive"]}, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "reflinks": ["type-of", "typeof", "verb"]}, "gitHead": "cb84946f0fd7fc19aefe7b661382b3d3fb036f55", "_id": "kind-of@5.0.2", "_npmVersion": "5.3.0", "_nodeVersion": "7.7.3", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"email": "<EMAIL>", "name": "doowb"}, {"email": "<EMAIL>", "name": "j<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"integrity": "sha512-ru8+TQHbN8956c7ZlkgK5Imjx0GMat3jN45GNIthpPeb+SzLrqSg/NG7llQtIqUTbrdu5Oi0lSnIoJmDTwwSzw==", "shasum": "f57bec933d9a2209ffa96c5c08343607b7035fda", "tarball": "https://registry.npmjs.org/kind-of/-/kind-of-5.0.2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCgoAwmYEgpJP/gfeHeqghDKFN75uTDZq7G/Vdu0LBDZQIgHKbehCTIFEmnSglVajF4qoenpslhRrlmASa0MpfZ6Ug="}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/kind-of-5.0.2.tgz_1501664150102_0.4696969927754253"}, "directories": {}}, "5.1.0": {"name": "kind-of", "description": "Get the native type of a value.", "version": "5.1.0", "homepage": "https://github.com/jonschlinkert/kind-of", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "https://dtothefp.github.io/me"}, {"name": "<PERSON>", "url": "https://twitter.com/aretecode"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON>", "url": "kensheedlo.com"}, {"name": "laggingreflex", "url": "https://github.com/laggingreflex"}, {"name": "<PERSON>", "url": "https://miguelmota.com"}, {"name": "<PERSON>", "url": "http://about.me/peterdehaan"}, {"name": "tunnckoCore", "url": "https://i.am.charlike.online"}], "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/kind-of.git"}, "bugs": {"url": "https://github.com/jonschlinkert/kind-of/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha", "prepublish": "browserify -o browser.js -e index.js -s index --bare"}, "devDependencies": {"ansi-bold": "^0.1.1", "benchmarked": "^1.1.1", "browserify": "^14.4.0", "gulp-format-md": "^0.1.12", "matched": "^0.4.4", "mocha": "^3.4.2", "type-of": "^2.0.1", "typeof": "^1.0.0"}, "keywords": ["arguments", "array", "boolean", "check", "date", "function", "is", "is-type", "is-type-of", "kind", "kind-of", "number", "object", "of", "regexp", "string", "test", "type", "type-of", "typeof", "types"], "verb": {"related": {"list": ["is-glob", "is-number", "is-primitive"]}, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "reflinks": ["type-of", "typeof", "verb"]}, "gitHead": "ed479b6ee194dc1edff852f17095ae1de40bafbc", "_id": "kind-of@5.1.0", "_npmVersion": "5.4.2", "_nodeVersion": "8.7.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-NGEErnH6F2vUuXDh+OlbcKW7/wOcfdRHaZ7VWtqCztfHri/++YKmP51OdWeGPuqCOba6kk2OTe5d02VmTB80Pw==", "shasum": "729c91e2d857b7a419a1f9aa65685c4c33f5845d", "tarball": "https://registry.npmjs.org/kind-of/-/kind-of-5.1.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCdrdtLOptm9UXxo25GTT330Y0GhcXsBRAFWJuR7CKrnwIhAJY139YmGgOhl8ix3NAgtkoTWyKWoarsgQ1ZbICs3s3X"}]}, "maintainers": [{"email": "<EMAIL>", "name": "doowb"}, {"email": "<EMAIL>", "name": "j<PERSON><PERSON><PERSON><PERSON>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/kind-of-5.1.0.tgz_1507878225264_0.114781056297943"}, "directories": {}}, "6.0.0": {"name": "kind-of", "description": "Get the native type of a value.", "version": "6.0.0", "homepage": "https://github.com/jonschlinkert/kind-of", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "https://dtothefp.github.io/me"}, {"name": "<PERSON>", "url": "https://twitter.com/aretecode"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON>", "url": "kensheedlo.com"}, {"name": "laggingreflex", "url": "https://github.com/laggingreflex"}, {"name": "<PERSON>", "url": "https://miguelmota.com"}, {"name": "<PERSON>", "url": "http://about.me/peterdehaan"}, {"name": "tunnckoCore", "url": "https://i.am.charlike.online"}], "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/kind-of.git"}, "bugs": {"url": "https://github.com/jonschlinkert/kind-of/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha", "prepublish": "browserify -o browser.js -e index.js -s index --bare"}, "devDependencies": {"benchmarked": "^2.0.0", "browserify": "^14.4.0", "gulp-format-md": "^1.0.0", "mocha": "^4.0.1", "write": "^1.0.3"}, "keywords": ["arguments", "array", "boolean", "check", "date", "function", "is", "is-type", "is-type-of", "kind", "kind-of", "number", "object", "of", "regexp", "string", "test", "type", "type-of", "typeof", "types"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "related": {"list": ["is-glob", "is-number", "is-primitive"]}, "reflinks": ["type-of", "typeof", "verb"]}, "gitHead": "4b072e165eba5851551dbab509ff5d3acad98a39", "_id": "kind-of@6.0.0", "_npmVersion": "5.4.2", "_nodeVersion": "8.7.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-sUd5AnFyOPh+RW+ZIHd1FHuwM4OFvhKCPVxxhamLxWLpmv1xQ394lzRMmhLQOiMpXvnB64YRLezWaJi5xGk7Dg==", "shasum": "3606e9e2fa960e7ddaa8898c03804e47e5d66644", "tarball": "https://registry.npmjs.org/kind-of/-/kind-of-6.0.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICQg8kgWF1MqrxUEEl0cJKu49BEojyXSoir/m7lf8EkYAiEA5pSVzZup6022T1sXbiUUMbqUmz+NKAegVhQyP5Z2TZY="}]}, "maintainers": [{"email": "<EMAIL>", "name": "doowb"}, {"email": "<EMAIL>", "name": "j<PERSON><PERSON><PERSON><PERSON>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/kind-of-6.0.0.tgz_1507890288379_0.9934709435328841"}, "directories": {}}, "6.0.1": {"name": "kind-of", "description": "Get the native type of a value.", "version": "6.0.1", "homepage": "https://github.com/jonschlinkert/kind-of", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "https://dtothefp.github.io/me"}, {"name": "<PERSON>", "url": "https://twitter.com/aretecode"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON>", "url": "kensheedlo.com"}, {"name": "laggingreflex", "url": "https://github.com/laggingreflex"}, {"name": "<PERSON>", "url": "https://miguelmota.com"}, {"name": "<PERSON>", "url": "http://about.me/peterdehaan"}, {"name": "tunnckoCore", "url": "https://i.am.charlike.online"}], "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/kind-of.git"}, "bugs": {"url": "https://github.com/jonschlinkert/kind-of/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha", "prepublish": "browserify -o browser.js -e index.js -s index --bare"}, "devDependencies": {"benchmarked": "^2.0.0", "browserify": "^14.4.0", "gulp-format-md": "^1.0.0", "mocha": "^4.0.1", "write": "^1.0.3"}, "keywords": ["arguments", "array", "boolean", "check", "date", "function", "is", "is-type", "is-type-of", "kind", "kind-of", "number", "object", "of", "regexp", "string", "test", "type", "type-of", "typeof", "types"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "related": {"list": ["is-glob", "is-number", "is-primitive"]}, "reflinks": ["type-of", "typeof", "verb"]}, "gitHead": "dc8ff7c9c6641bbb8caa6df04f83a20b86a595e0", "_id": "kind-of@6.0.1", "_npmVersion": "5.5.1", "_nodeVersion": "9.1.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-y/15y6w31q9eVnXhVLPQkI7MD6JyuPNqEnetl8bZEc+mngLuonHQJ0x/6BD1WX6ml0Ig/psUlyKZJxz8uUo1xQ==", "shasum": "4948e6263553ac3712fc44d305b77851d9e40ea4", "tarball": "https://registry.npmjs.org/kind-of/-/kind-of-6.0.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDud32W4QCC31xqeU8oOIygNASKrZaGnDe5QuB3ihiM1wIhANZcsGcyxCNC2ULigfYW1tqo4PmF4CEJjs35NJuK6uDT"}]}, "maintainers": [{"email": "<EMAIL>", "name": "doowb"}, {"email": "<EMAIL>", "name": "j<PERSON><PERSON><PERSON><PERSON>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/kind-of-6.0.1.tgz_1511065899851_0.48463770980015397"}, "directories": {}}, "6.0.2": {"name": "kind-of", "description": "Get the native type of a value.", "version": "6.0.2", "homepage": "https://github.com/jonschlinkert/kind-of", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "https://dtothefp.github.io/me"}, {"name": "<PERSON>", "url": "https://twitter.com/aretecode"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON>", "url": "kensheedlo.com"}, {"name": "laggingreflex", "url": "https://github.com/laggingreflex"}, {"name": "<PERSON>", "url": "https://miguelmota.com"}, {"name": "<PERSON>", "url": "http://about.me/peterdehaan"}, {"name": "tunnckoCore", "url": "https://i.am.charlike.online"}], "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/kind-of.git"}, "bugs": {"url": "https://github.com/jonschlinkert/kind-of/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha", "prepublish": "browserify -o browser.js -e index.js -s index --bare"}, "devDependencies": {"benchmarked": "^2.0.0", "browserify": "^14.4.0", "gulp-format-md": "^1.0.0", "mocha": "^4.0.1", "write": "^1.0.3"}, "keywords": ["arguments", "array", "boolean", "check", "date", "function", "is", "is-type", "is-type-of", "kind", "kind-of", "number", "object", "of", "regexp", "string", "test", "type", "type-of", "typeof", "types"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "related": {"list": ["is-glob", "is-number", "is-primitive"]}, "reflinks": ["type-of", "typeof", "verb"]}, "gitHead": "1491da72e8d276479f5f6198a9e79c1379c5d0c7", "_id": "kind-of@6.0.2", "_npmVersion": "5.5.1", "_nodeVersion": "9.1.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-s5kLOcnH0XqDO+FvuaLX8DDjZ18CGFk7VygH40QoKPUQhW4e2rvM0rwUq0t8IQDOwYSeLK01U90OjzBTme2QqA==", "shasum": "01146b36a6218e64e58f3a8d66de5d7fc6f6d051", "tarball": "https://registry.npmjs.org/kind-of/-/kind-of-6.0.2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC5n57EqtixV+3k2fxTw7Ml937V9mJ11AzSzgXDHMg4kAiEAnccrJ2MEFSjsmPDpQIj5E3cUYSpcUYeG+KAdWzqghdo="}]}, "maintainers": [{"email": "<EMAIL>", "name": "doowb"}, {"email": "<EMAIL>", "name": "j<PERSON><PERSON><PERSON><PERSON>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/kind-of-6.0.2.tgz_1512153086430_0.774781379615888"}, "directories": {}}, "6.0.3": {"name": "kind-of", "description": "Get the native type of a value.", "version": "6.0.3", "homepage": "https://github.com/jonschlinkert/kind-of", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "https://dtothefp.github.io/me"}, {"name": "<PERSON>", "url": "https://twitter.com/aretecode"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON>", "url": "kensheedlo.com"}, {"name": "laggingreflex", "url": "https://github.com/laggingreflex"}, {"name": "<PERSON>", "url": "https://miguelmota.com"}, {"name": "<PERSON>", "url": "http://about.me/peterdehaan"}, {"name": "tunnckoCore", "url": "https://i.am.charlike.online"}], "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/kind-of.git"}, "bugs": {"url": "https://github.com/jonschlinkert/kind-of/issues"}, "license": "MIT", "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha", "prepublish": "browserify -o browser.js -e index.js -s index --bare"}, "devDependencies": {"benchmarked": "^2.0.0", "browserify": "^14.4.0", "gulp-format-md": "^1.0.0", "mocha": "^4.0.1", "write": "^1.0.3"}, "keywords": ["arguments", "array", "boolean", "check", "date", "function", "is", "is-type", "is-type-of", "kind", "kind-of", "number", "object", "of", "regexp", "string", "test", "type", "type-of", "typeof", "types"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "related": {"list": ["is-glob", "is-number", "is-primitive"]}, "reflinks": ["type-of", "typeof", "verb"]}, "gitHead": "abab085d65f7ee978011da8f135291892fcd97db", "_id": "kind-of@6.0.3", "_nodeVersion": "12.14.0", "_npmVersion": "6.13.4", "_npmUser": {"name": "doowb", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-dcS1ul+9tmeD95T+x28/ehLgd9mENa3LsvDTtzm3vyBEO7RPptvAD+t44WVXaUjTBRcrpFeFlC8WCruUR456hw==", "shasum": "07c05034a6c349fa06e24fa35aa76db4580ce4dd", "tarball": "https://registry.npmjs.org/kind-of/-/kind-of-6.0.3.tgz", "fileCount": 5, "unpackedSize": 22820, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeIJZoCRA9TVsSAnZWagAA9H0P/R9gM5M0qhbSdpF8PCzd\n9QwWWxPfuh/mgbI4JfpfY6Szpn7YbfjfmKKYJEo01y80jPun+82/fs3oy6hw\njquzgudpUxNbadxutoQk0VRTaiJyIJHg2UaAFlrh+Gp5kUNYYhmrvzgqJ6L6\n23fxIm8ylerpAQSVHBfmiIkRcQeqRWmFgP3K2YdIoGfTSxBwH/f2akEeI5CO\noFsbhgClF6RlA0US7pN9jXhlhWzmG/2o530TNnNajfKRjBtpJaTI94kKr/Qj\n2dSIA+Fn1a7HGtmEQo/S+6RO9W13qwo1QySkNBztacz3hB1CU8Io6wqZafTi\nVBtato0gpDZzn5ZTvSW33p3WOVPY3HTZl8xD9v2v+enugRZAXVcXNiMDaqzY\n0OyWp+2/Wu1b05u0F4pKoMJRQNQ9IJzZlONJbzr9GID5S1J8m6BaADW6lyWp\ndWBpX7DBdvMa8s5MlJHrHFcSYY+W7wxjq8vAHpzeHpMtIAAlDPJuWvH46hhd\nDQ+/QpsJGKafxC7yswei/DG+TGloBdZ+Hh4V/8BCCwt+WdIavGvGPb31F4vG\nPhCeJKjpuoDn3ijzoATC/77nQE0bUZY7QwPiDlbqBlK8w/KH3fTJPl2mWKrQ\nPyJaZGJQj9GduJJ4mrtMYJRFyAn71+kY0nsB+YsF+Itl2R0VLQZCGPZIK8vw\nde/M\r\n=Ia39\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC707TB16hWqpiRQBBYghI7/YsaOHrOX5dLG+Gsdn5/tAIhAKT8l+OTloiD9UH1blB/u8BOUhNe/dm5wZbhgM2KAwhN"}]}, "maintainers": [{"email": "<EMAIL>", "name": "doowb"}, {"email": "<EMAIL>", "name": "j<PERSON><PERSON><PERSON><PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/kind-of_6.0.3_1579193959666_0.3641623588189655"}, "_hasShrinkwrap": false}}, "readme": "# kind-of [![NPM version](https://img.shields.io/npm/v/kind-of.svg?style=flat)](https://www.npmjs.com/package/kind-of) [![NPM monthly downloads](https://img.shields.io/npm/dm/kind-of.svg?style=flat)](https://npmjs.org/package/kind-of) [![NPM total downloads](https://img.shields.io/npm/dt/kind-of.svg?style=flat)](https://npmjs.org/package/kind-of) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/kind-of.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/kind-of)\n\n> Get the native type of a value.\n\nPlease consider following this project's author, [<PERSON>](https://github.com/jonschlinkert), and consider starring the project to show your :heart: and support.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install --save kind-of\n```\n\nInstall with [bower](https://bower.io/)\n\n```sh\n$ bower install kind-of --save\n```\n\n## Why use this?\n\n1. [it's fast](#benchmarks) | [optimizations](#optimizations)\n2. [better type checking](#better-type-checking)\n\n## Usage\n\n> es5, es6, and browser ready\n\n```js\nvar kindOf = require('kind-of');\n\nkindOf(undefined);\n//=> 'undefined'\n\nkindOf(null);\n//=> 'null'\n\nkindOf(true);\n//=> 'boolean'\n\nkindOf(false);\n//=> 'boolean'\n\nkindOf(new Buffer(''));\n//=> 'buffer'\n\nkindOf(42);\n//=> 'number'\n\nkindOf('str');\n//=> 'string'\n\nkindOf(arguments);\n//=> 'arguments'\n\nkindOf({});\n//=> 'object'\n\nkindOf(Object.create(null));\n//=> 'object'\n\nkindOf(new Test());\n//=> 'object'\n\nkindOf(new Date());\n//=> 'date'\n\nkindOf([1, 2, 3]);\n//=> 'array'\n\nkindOf(/foo/);\n//=> 'regexp'\n\nkindOf(new RegExp('foo'));\n//=> 'regexp'\n\nkindOf(new Error('error'));\n//=> 'error'\n\nkindOf(function () {});\n//=> 'function'\n\nkindOf(function * () {});\n//=> 'generatorfunction'\n\nkindOf(Symbol('str'));\n//=> 'symbol'\n\nkindOf(new Map());\n//=> 'map'\n\nkindOf(new WeakMap());\n//=> 'weakmap'\n\nkindOf(new Set());\n//=> 'set'\n\nkindOf(new WeakSet());\n//=> 'weakset'\n\nkindOf(new Int8Array());\n//=> 'int8array'\n\nkindOf(new Uint8Array());\n//=> 'uint8array'\n\nkindOf(new Uint8ClampedArray());\n//=> 'uint8clampedarray'\n\nkindOf(new Int16Array());\n//=> 'int16array'\n\nkindOf(new Uint16Array());\n//=> 'uint16array'\n\nkindOf(new Int32Array());\n//=> 'int32array'\n\nkindOf(new Uint32Array());\n//=> 'uint32array'\n\nkindOf(new Float32Array());\n//=> 'float32array'\n\nkindOf(new Float64Array());\n//=> 'float64array'\n```\n\n## Benchmarks\n\nBenchmarked against [typeof](http://github.com/CodingFu/typeof) and [type-of](https://github.com/ForbesLindesay/type-of).\n\n```bash\n# arguments (32 bytes)\n  kind-of x 17,024,098 ops/sec ±1.90% (86 runs sampled)\n  lib-type-of x 11,926,235 ops/sec ±1.34% (83 runs sampled)\n  lib-typeof x 9,245,257 ops/sec ±1.22% (87 runs sampled)\n\n  fastest is kind-of (by 161% avg)\n\n# array (22 bytes)\n  kind-of x 17,196,492 ops/sec ±1.07% (88 runs sampled)\n  lib-type-of x 8,838,283 ops/sec ±1.02% (87 runs sampled)\n  lib-typeof x 8,677,848 ops/sec ±0.87% (87 runs sampled)\n\n  fastest is kind-of (by 196% avg)\n\n# boolean (24 bytes)\n  kind-of x 16,841,600 ops/sec ±1.10% (86 runs sampled)\n  lib-type-of x 8,096,787 ops/sec ±0.95% (87 runs sampled)\n  lib-typeof x 8,423,345 ops/sec ±1.15% (86 runs sampled)\n\n  fastest is kind-of (by 204% avg)\n\n# buffer (38 bytes)\n  kind-of x 14,848,060 ops/sec ±1.05% (86 runs sampled)\n  lib-type-of x 3,671,577 ops/sec ±1.49% (87 runs sampled)\n  lib-typeof x 8,360,236 ops/sec ±1.24% (86 runs sampled)\n\n  fastest is kind-of (by 247% avg)\n\n# date (30 bytes)\n  kind-of x 16,067,761 ops/sec ±1.58% (86 runs sampled)\n  lib-type-of x 8,954,436 ops/sec ±1.40% (87 runs sampled)\n  lib-typeof x 8,488,307 ops/sec ±1.51% (84 runs sampled)\n\n  fastest is kind-of (by 184% avg)\n\n# error (36 bytes)\n  kind-of x 9,634,090 ops/sec ±1.12% (89 runs sampled)\n  lib-type-of x 7,735,624 ops/sec ±1.32% (86 runs sampled)\n  lib-typeof x 7,442,160 ops/sec ±1.11% (90 runs sampled)\n\n  fastest is kind-of (by 127% avg)\n\n# function (34 bytes)\n  kind-of x 10,031,494 ops/sec ±1.27% (86 runs sampled)\n  lib-type-of x 9,502,757 ops/sec ±1.17% (89 runs sampled)\n  lib-typeof x 8,278,985 ops/sec ±1.08% (88 runs sampled)\n\n  fastest is kind-of (by 113% avg)\n\n# null (24 bytes)\n  kind-of x 18,159,808 ops/sec ±1.92% (86 runs sampled)\n  lib-type-of x 12,927,635 ops/sec ±1.01% (88 runs sampled)\n  lib-typeof x 7,958,234 ops/sec ±1.21% (89 runs sampled)\n\n  fastest is kind-of (by 174% avg)\n\n# number (22 bytes)\n  kind-of x 17,846,779 ops/sec ±0.91% (85 runs sampled)\n  lib-type-of x 3,316,636 ops/sec ±1.19% (86 runs sampled)\n  lib-typeof x 2,329,477 ops/sec ±2.21% (85 runs sampled)\n\n  fastest is kind-of (by 632% avg)\n\n# object-plain (47 bytes)\n  kind-of x 7,085,155 ops/sec ±1.05% (88 runs sampled)\n  lib-type-of x 8,870,930 ops/sec ±1.06% (83 runs sampled)\n  lib-typeof x 8,716,024 ops/sec ±1.05% (87 runs sampled)\n\n  fastest is lib-type-of (by 112% avg)\n\n# regex (25 bytes)\n  kind-of x 14,196,052 ops/sec ±1.65% (84 runs sampled)\n  lib-type-of x 9,554,164 ops/sec ±1.25% (88 runs sampled)\n  lib-typeof x 8,359,691 ops/sec ±1.07% (87 runs sampled)\n\n  fastest is kind-of (by 158% avg)\n\n# string (33 bytes)\n  kind-of x 16,131,428 ops/sec ±1.41% (85 runs sampled)\n  lib-type-of x 7,273,172 ops/sec ±1.05% (87 runs sampled)\n  lib-typeof x 7,382,635 ops/sec ±1.17% (85 runs sampled)\n\n  fastest is kind-of (by 220% avg)\n\n# symbol (34 bytes)\n  kind-of x 17,011,537 ops/sec ±1.24% (86 runs sampled)\n  lib-type-of x 3,492,454 ops/sec ±1.23% (89 runs sampled)\n  lib-typeof x 7,471,235 ops/sec ±2.48% (87 runs sampled)\n\n  fastest is kind-of (by 310% avg)\n\n# template-strings (36 bytes)\n  kind-of x 15,434,250 ops/sec ±1.46% (83 runs sampled)\n  lib-type-of x 7,157,907 ops/sec ±0.97% (87 runs sampled)\n  lib-typeof x 7,517,986 ops/sec ±0.92% (86 runs sampled)\n\n  fastest is kind-of (by 210% avg)\n\n# undefined (29 bytes)\n  kind-of x 19,167,115 ops/sec ±1.71% (87 runs sampled)\n  lib-type-of x 15,477,740 ops/sec ±1.63% (85 runs sampled)\n  lib-typeof x 19,075,495 ops/sec ±1.17% (83 runs sampled)\n\n  fastest is lib-typeof,kind-of\n\n```\n\n## Optimizations\n\nIn 7 out of 8 cases, this library is 2x-10x faster than other top libraries included in the benchmarks. There are a few things that lead to this performance advantage, none of them hard and fast rules, but all of them simple and repeatable in almost any code library:\n\n1. Optimize around the fastest and most common use cases first. Of course, this will change from project-to-project, but I took some time to understand how and why `typeof` checks were being used in my own libraries and other libraries I use a lot.\n2. Optimize around bottlenecks - In other words, the order in which conditionals are implemented is significant, because each check is only as fast as the failing checks that came before it. Here, the biggest bottleneck by far is checking for plain objects (an object that was created by the `Object` constructor). I opted to make this check happen by process of elimination rather than brute force up front (e.g. by using something like `val.constructor.name`), so that every other type check would not be penalized it.\n3. Don't do uneccessary processing - why do `.slice(8, -1).toLowerCase();` just to get the word `regex`? It's much faster to do `if (type === '[object RegExp]') return 'regex'`\n4. There is no reason to make the code in a microlib as terse as possible, just to win points for making it shorter. It's always better to favor performant code over terse code. You will always only be using a single `require()` statement to use the library anyway, regardless of how the code is written.\n\n## Better type checking\n\nkind-of seems to be more consistently \"correct\" than other type checking libs I've looked at. For example, here are some differing results from other popular libs:\n\n### [typeof](https://github.com/CodingFu/typeof) lib\n\nIncorrectly identifies instances of custom constructors (pretty common):\n\n```js\nvar typeOf = require('typeof');\nfunction Test() {}\nconsole.log(typeOf(new Test()));\n//=> 'test'\n```\n\nReturns `object` instead of `arguments`:\n\n```js\nfunction foo() {\n  console.log(typeOf(arguments)) //=> 'object'\n}\nfoo();\n```\n\n### [type-of](https://github.com/ForbesLindesay/type-of) lib\n\nIncorrectly returns `object` for generator functions, buffers, `Map`, `Set`, `WeakMap` and `WeakSet`:\n\n```js\nfunction * foo() {}\nconsole.log(typeOf(foo));\n//=> 'object'\nconsole.log(typeOf(new Buffer('')));\n//=> 'object'\nconsole.log(typeOf(new Map()));\n//=> 'object'\nconsole.log(typeOf(new Set()));\n//=> 'object'\nconsole.log(typeOf(new WeakMap()));\n//=> 'object'\nconsole.log(typeOf(new WeakSet()));\n//=> 'object'\n```\n\n## About\n\n<details>\n<summary><strong>Contributing</strong></summary>\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).\n\n</details>\n\n<details>\n<summary><strong>Running Tests</strong></summary>\n\nRunning and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:\n\n```sh\n$ npm install && npm test\n```\n\n</details>\n\n<details>\n<summary><strong>Building docs</strong></summary>\n\n_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_\n\nTo generate the readme, run the following command:\n\n```sh\n$ npm install -g verbose/verb#dev verb-generate-readme && verb\n```\n\n</details>\n\n### Related projects\n\nYou might also be interested in these projects:\n\n* [is-glob](https://www.npmjs.com/package/is-glob): Returns `true` if the given string looks like a glob pattern or an extglob pattern… [more](https://github.com/micromatch/is-glob) | [homepage](https://github.com/micromatch/is-glob \"Returns `true` if the given string looks like a glob pattern or an extglob pattern. This makes it easy to create code that only uses external modules like node-glob when necessary, resulting in much faster code execution and initialization time, and a bet\")\n* [is-number](https://www.npmjs.com/package/is-number): Returns true if a number or string value is a finite number. Useful for regex… [more](https://github.com/jonschlinkert/is-number) | [homepage](https://github.com/jonschlinkert/is-number \"Returns true if a number or string value is a finite number. Useful for regex matches, parsing, user input, etc.\")\n* [is-primitive](https://www.npmjs.com/package/is-primitive): Returns `true` if the value is a primitive.  | [homepage](https://github.com/jonschlinkert/is-primitive \"Returns `true` if the value is a primitive. \")\n\n### Contributors\n\n| **Commits** | **Contributor** |  \n| --- | --- |  \n| 102 | [jonschlinkert](https://github.com/jonschlinkert) |  \n| 3   | [aretecode](https://github.com/aretecode) |  \n| 2   | [miguelmota](https://github.com/miguelmota) |  \n| 1   | [doowb](https://github.com/doowb) |  \n| 1   | [dtothefp](https://github.com/dtothefp) |  \n| 1   | [ianstormtaylor](https://github.com/ianstormtaylor) |  \n| 1   | [ksheedlo](https://github.com/ksheedlo) |  \n| 1   | [pdehaan](https://github.com/pdehaan) |  \n| 1   | [laggingreflex](https://github.com/laggingreflex) |  \n| 1   | [tunnckoCore](https://github.com/tunnckoCore) |  \n| 1   | [xiaofen9](https://github.com/xiaofen9) |  \n\n### Author\n\n**Jon Schlinkert**\n\n* [GitHub Profile](https://github.com/jonschlinkert)\n* [Twitter Profile](https://twitter.com/jonschlinkert)\n* [LinkedIn Profile](https://linkedin.com/in/jonschlinkert)\n\n### License\n\nCopyright © 2020, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT License](LICENSE).\n\n***\n\n_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.8.0, on January 16, 2020._", "maintainers": [{"email": "<EMAIL>", "name": "doowb"}, {"email": "<EMAIL>", "name": "j<PERSON><PERSON><PERSON><PERSON>"}], "time": {"modified": "2023-07-12T19:12:21.548Z", "created": "2014-09-26T08:08:38.913Z", "0.1.0": "2014-09-26T08:08:38.913Z", "0.1.1": "2014-10-23T23:37:48.921Z", "0.1.2": "2014-10-26T10:11:23.113Z", "1.0.0": "2015-01-19T06:07:32.305Z", "1.0.1": "2015-02-03T06:48:43.433Z", "1.1.0": "2015-02-09T10:09:43.956Z", "1.1.1": "2015-05-31T18:43:51.334Z", "2.0.0": "2015-05-31T20:36:45.457Z", "2.0.1": "2015-08-21T09:13:11.430Z", "3.0.0": "2015-11-17T11:20:13.444Z", "3.0.1": "2015-11-17T13:01:40.860Z", "3.0.2": "2015-11-17T13:04:46.449Z", "3.0.3": "2016-05-03T08:09:36.971Z", "3.0.4": "2016-07-29T16:53:53.986Z", "3.1.0": "2016-12-07T06:55:33.842Z", "3.2.0": "2017-04-25T11:43:00.946Z", "3.2.1": "2017-05-16T18:14:51.514Z", "3.2.2": "2017-05-16T18:21:41.452Z", "4.0.0": "2017-05-19T18:23:12.030Z", "5.0.0": "2017-06-21T20:53:54.320Z", "5.0.1": "2017-07-31T12:04:05.635Z", "5.0.2": "2017-08-02T08:55:51.186Z", "5.1.0": "2017-10-13T07:03:46.381Z", "6.0.0": "2017-10-13T10:24:49.302Z", "6.0.1": "2017-11-19T04:31:41.064Z", "6.0.2": "2017-12-01T18:31:27.385Z", "6.0.3": "2020-01-16T16:59:19.808Z"}, "homepage": "https://github.com/jonschlinkert/kind-of", "keywords": ["arguments", "array", "boolean", "check", "date", "function", "is", "is-type", "is-type-of", "kind", "kind-of", "number", "object", "of", "regexp", "string", "test", "type", "type-of", "typeof", "types"], "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/kind-of.git"}, "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/kind-of/issues"}, "readmeFilename": "README.md", "license": "MIT", "users": {"jonschlinkert": true, "programmer.severson": true, "antixrist": true, "mojaray2k": true, "hifaraz": true, "mrmartineau": true, "arteffeckt": true, "grandsong": true, "wmhilton": true, "iuykza": true, "rocket0191": true, "arttse": true, "oldfish": true, "kazet": true, "456wyc": true, "d-band": true, "croqaz": true, "tjfwalker": true, "leix3041": true, "xinwangwang": true, "daniel-lewis-bsc-hons": true, "danday74": true, "douxuesong": true, "fanjieqi": true, "daizch": true, "hualei": true, "bootta": true, "flumpus-dev": true}, "contributors": [{"name": "<PERSON>", "url": "https://dtothefp.github.io/me"}, {"name": "<PERSON>", "url": "https://twitter.com/aretecode"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON>", "url": "kensheedlo.com"}, {"name": "laggingreflex", "url": "https://github.com/laggingreflex"}, {"name": "<PERSON>", "url": "https://miguelmota.com"}, {"name": "<PERSON>", "url": "http://about.me/peterdehaan"}, {"name": "tunnckoCore", "url": "https://i.am.charlike.online"}]}