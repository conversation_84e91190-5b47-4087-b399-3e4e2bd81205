{"_id": "@webassemblyjs/helper-wasm-section", "_rev": "85-af2cfcaabdeb35ea86fdb0e47a0ce103", "name": "@webassemblyjs/helper-wasm-section", "dist-tags": {"latest": "1.14.1"}, "versions": {"1.1.0": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.1.0", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/helper-wasm-section@1.1.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "7b365f73618f56c04be83a724a5f899214e998cd", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.1.0.tgz", "fileCount": 7, "integrity": "sha512-XgFVeN5IY9JV62fDwn3qVGo7AmrLHBXmN2E7HEKGWUj9ODfWi7gl/Epj4FU9Uz/t7uP5yJoNjxvej0TpQAgxUg==", "signatures": [{"sig": "MEUCIGumgkOQg35mmIpIYMb6d0Hvty92ZSBXsMZjRNCi1YhRAiEA95s86Map+ZOCGbwdqzNBDCMozkD+dyJ/B/fWkEyi+Cw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9887}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.1.0", "@webassemblyjs/wasm-gen": "1.1.0", "@webassemblyjs/helper-buffer": "1.1.0", "@webassemblyjs/helper-wasm-bytecode": "1.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.1.0_1520241562614_0.3134833732378681", "host": "s3://npm-registry-packages"}}, "1.1.1": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.1.1", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/helper-wasm-section@1.1.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "ceafe390238f0a0e16e1f9db5a150c70e3508288", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.1.1.tgz", "fileCount": 9, "integrity": "sha512-Nc2KmHJ47wRquZhxuorHvJzAfHEu3z1aCKRwdKtGu9MfvN4U6kzw5MKCQb8w6YuWnvKx95nTsG91Dvk4JWT+lA==", "signatures": [{"sig": "MEYCIQCY5qrVQpjTR6OlYFRjtL8CNesiAbuEGzj9WLv5rQ+tkgIhAPvvrdjphc/kduS+e3al1MKMzZluaf+YF4JZUCNzsX0+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11624}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.1.1", "@webassemblyjs/wasm-gen": "1.1.1", "@webassemblyjs/helper-buffer": "1.1.1", "@webassemblyjs/helper-wasm-bytecode": "1.1.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.1.1_1520245624380_0.5557945566807869", "host": "s3://npm-registry-packages"}}, "1.1.2-y.0": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.1.2-y.0", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/helper-wasm-section@1.1.2-y.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "28acfd9c1f1aeb3864031f15b4398f9c9ffac8dc", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.1.2-y.0.tgz", "fileCount": 9, "integrity": "sha512-hYrAx8GjpI54Fd6htL3AbrkZKrF9lxEV5xctgriBGPbVXzq0bMFGBk91F5LUUaWu+vrwNwmEwhLoStPbNEufQQ==", "signatures": [{"sig": "MEUCIQDYwp1gFlvKDUX9PiJpE43S5Y7dPGUSCwd0ru2GWMliygIgEsDcRAK0mX1sfzBZSdfVpHS8ejlJKtZrXJcJbCj1OHo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11861}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.1.2-y.0", "@webassemblyjs/wasm-gen": "1.1.2-y.0", "@webassemblyjs/helper-buffer": "1.1.2-y.0", "@webassemblyjs/helper-wasm-bytecode": "1.1.2-y.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.1.2-y.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.1.2-y.0_1520266582783_0.2075576354426396", "host": "s3://npm-registry-packages"}}, "1.1.2-y.1": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.1.2-y.1", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/helper-wasm-section@1.1.2-y.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "5a6c971de193da16e516925ce47fe41713597b68", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.1.2-y.1.tgz", "fileCount": 9, "integrity": "sha512-4Oper+QPedFGmYtMLaDwKWZCBI97E9Orf0Wek2hyL8akrJeI4uaeUgsVF+Emu6SwF1d7TA39bBjm6nr2mdwIEQ==", "signatures": [{"sig": "MEQCIFJL7i0FwaUWkmIdTTY4tvTL+qKauCpL1f0qsyX1esaAAiBdPf6go2ukZEDlOu7DmxZnNQtUz1SEBQRnLuQ8/0sR3g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11861}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.1.2-y.1", "@webassemblyjs/wasm-gen": "1.1.2-y.1", "@webassemblyjs/helper-buffer": "1.1.2-y.1", "@webassemblyjs/helper-wasm-bytecode": "1.1.2-y.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.1.2-y.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.1.2-y.1_1520532617876_0.8946913690596172", "host": "s3://npm-registry-packages"}}, "1.1.2-y.2": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.1.2-y.2", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/helper-wasm-section@1.1.2-y.2", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "fe6ab5e5453a30b936aa99308cf8e7375eef2a36", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.1.2-y.2.tgz", "fileCount": 9, "integrity": "sha512-IUMHmsT+ACEJL5nbh09wnvs2iLQNO37kaIKVhM5ZjUI3MKercb/lkPeP+ANqna9Uip2MG+F4Bs68syqqL/f9Sg==", "signatures": [{"sig": "MEUCIQC9VzpC+0WF5WcNse6eBwk/7C+lzBQqtChEI9mY5xBSRQIgInrINxVUftlIaqo7LtSQWUVgtRJ+FYu7f0uHo+vax+w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11933}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.1.2-y.2", "@webassemblyjs/wasm-gen": "1.1.2-y.2", "@webassemblyjs/helper-buffer": "1.1.2-y.2", "@webassemblyjs/helper-wasm-bytecode": "1.1.2-y.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.1.2-y.2"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.1.2-y.2_1520582281782_0.9031508810851094", "host": "s3://npm-registry-packages"}}, "1.1.2-y.3": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.1.2-y.3", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/helper-wasm-section@1.1.2-y.3", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "66fa9a36fc67e912a6516cac8f08012dd2088e1a", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.1.2-y.3.tgz", "fileCount": 9, "integrity": "sha512-klxb9GAnB+R5lLdq0qhpWbP2HCDK2kS4E57Q9pxnSHNwU3ua5jZGasn/ibF0yAgO69lbztpW1oU8eqr+ATb7XA==", "signatures": [{"sig": "MEQCICMRJsdJX5KU0ps9kfd9FX0/vE0M+TmVPNYigTkeoEn3AiBL+yettSCEeOTcM9BUVP/CGFSyl071SURZSYfsPQE+5Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11933}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.1.2-y.3", "@webassemblyjs/wasm-gen": "1.1.2-y.3", "@webassemblyjs/helper-buffer": "1.1.2-y.3", "@webassemblyjs/helper-wasm-bytecode": "1.1.2-y.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.1.2-y.3"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.1.2-y.3_1520582951523_0.8876156560175956", "host": "s3://npm-registry-packages"}}, "1.1.2-y.4": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.1.2-y.4", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/helper-wasm-section@1.1.2-y.4", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "7a34fa734399858969db60c7c023bd455fdb1023", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.1.2-y.4.tgz", "fileCount": 9, "integrity": "sha512-6OLUu6rHKGGQq4Qy16fOHFTj3kfPCFKkI5m2jtSC3jQD+g2FWB+l0cIgCicFCQxZowfIH33AhDwqzS11QsWmWw==", "signatures": [{"sig": "MEQCIGXv13kBEVLPYl3/+Z8hZn3wjLBPCN6qKwM4Eh1oyXJuAiBibfqlBYQI+bYSq10/UVCqJrWn7+zdW7kYoJ8AGmpsnA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11933}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.1.2-y.4", "@webassemblyjs/wasm-gen": "1.1.2-y.4", "@webassemblyjs/helper-buffer": "1.1.2-y.4", "@webassemblyjs/helper-wasm-bytecode": "1.1.2-y.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.1.2-y.4"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.1.2-y.4_1520583341851_0.07277794565556794", "host": "s3://npm-registry-packages"}}, "1.1.2-y.5": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.1.2-y.5", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/helper-wasm-section@1.1.2-y.5", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "1ac327fba3805c83cfa2d6083e2ae147eb939db7", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.1.2-y.5.tgz", "fileCount": 9, "integrity": "sha512-l1zkGKV13VzDwoc9oo+V0QOZ1XHvCKf96UERAbBPExFi+++yolbAgvVKBvpEAI0sZba1TK9h9l+ucdcmDJGYzA==", "signatures": [{"sig": "MEUCIC6Bpxxw2B8E2GNTZt6K9dT5a8E/QJCIMana7TqjrCXjAiEAzVVMYzjaZZ2APWf0dFpNIFeD//SHTuE8WY6z3tWVa/M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11933}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.1.2-y.5", "@webassemblyjs/wasm-gen": "1.1.2-y.5", "@webassemblyjs/helper-buffer": "1.1.2-y.5", "@webassemblyjs/helper-wasm-bytecode": "1.1.2-y.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.1.2-y.5"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.1.2-y.5_1520590720084_0.05368701669151821", "host": "s3://npm-registry-packages"}}, "1.1.2-y.6": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.1.2-y.6", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/helper-wasm-section@1.1.2-y.6", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "9bd6fccdb8955b0f847a5f11b8672634298ea494", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.1.2-y.6.tgz", "fileCount": 9, "integrity": "sha512-/1JP8ho2wZZ+KFGdRDswPWkeUfhtuGSoXyrOx71NrkiMUMIhDAkqWIndwNsp4L7AS5G86dNevhXEj0md5UHiKg==", "signatures": [{"sig": "MEYCIQD2uhG7hbdq91C98TsyitGoBtDboq9PD+CKNNlNEe/vugIhAJOgnTG3saqCSJoyZM3qZOKF90+5oEZT9OwkgyywT7DF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11933}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.1.2-y.6", "@webassemblyjs/wasm-gen": "1.1.2-y.6", "@webassemblyjs/helper-buffer": "1.1.2-y.6", "@webassemblyjs/helper-wasm-bytecode": "1.1.2-y.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.1.2-y.6"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.1.2-y.6_1520591356910_0.5660756858507014", "host": "s3://npm-registry-packages"}}, "1.1.2-y.7": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.1.2-y.7", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/helper-wasm-section@1.1.2-y.7", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "1015d50cc699a9f6ef354b6a62906e4c098296aa", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.1.2-y.7.tgz", "fileCount": 10, "integrity": "sha512-0mCAWz9RulE5AOKxUNVzoA2BvJKb/ypSS0xo3QIAjR6XRctcd15Rt7adybHIgtIUkCKARWtITG96MzZICoS4Lg==", "signatures": [{"sig": "MEUCIQCHAu1Po9hqqvVoAzczlGMYFJkf/fh8PkDyaeeWzGbYfgIgPEA2GFq17zZESWxYjrYjVewtATEmIjlnCLADOmMVmpQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16387}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.1.2-y.7", "@webassemblyjs/wasm-gen": "1.1.2-y.7", "@webassemblyjs/helper-buffer": "1.1.2-y.7", "@webassemblyjs/helper-wasm-bytecode": "1.1.2-y.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.1.2-y.7"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.1.2-y.7_1520615251450_0.5559452995103895", "host": "s3://npm-registry-packages"}}, "1.1.2-y.8": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.1.2-y.8", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/helper-wasm-section@1.1.2-y.8", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "dd231c5863c14b5f4cda61262f09e394bff59d7c", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.1.2-y.8.tgz", "fileCount": 9, "integrity": "sha512-+dNfHKTKvgf8WLjDhyTbJMkVrROTovVhRWq91wgciynhFMmzleeUUflfhho7QKzSF1f4VTXExAMPVY109n0Vcg==", "signatures": [{"sig": "MEYCIQCCWQVt/92QskwJVOvXbJtFNg9AIZ0bYItOmHm8WzO6HwIhAOKKam4fgOmdKh6yKKiGWz9MeoJ2yLvq0BE69thZOXDT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12159}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.1.2-y.8", "@webassemblyjs/wasm-gen": "1.1.2-y.8", "@webassemblyjs/helper-buffer": "1.1.2-y.8", "@webassemblyjs/helper-wasm-bytecode": "1.1.2-y.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.1.2-y.8"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.1.2-y.8_1520850159741_0.4402858112858288", "host": "s3://npm-registry-packages"}}, "1.1.2-y.9": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.1.2-y.9", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/helper-wasm-section@1.1.2-y.9", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "a7b04a331dc51218955ffb030cb919c257dbdb8d", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.1.2-y.9.tgz", "fileCount": 9, "integrity": "sha512-iqXT+YmIstK1RGzdsW6L5mjUKGV2e5Let0ixBEK2wi86SRtcNDv+OlDZ/pD9pJXKGMKRUECK70baPtQPgMk9Cg==", "signatures": [{"sig": "MEYCIQD1tEUPuuycnGk/QA4ZgVU2BaHT2XVpuyNMhYpZBWo5HwIhAP2rIMhABRZ2HP8C/EQ3mBQY1WiZvEFZrQ5iSjjpzZdj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12159}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.1.2-y.9", "@webassemblyjs/wasm-gen": "1.1.2-y.9", "@webassemblyjs/helper-buffer": "1.1.2-y.9", "@webassemblyjs/helper-wasm-bytecode": "1.1.2-y.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.1.2-y.9"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.1.2-y.9_1520852703417_0.3191242616768002", "host": "s3://npm-registry-packages"}}, "1.1.2-y.10": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.1.2-y.10", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/helper-wasm-section@1.1.2-y.10", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "bd5367b260d2b71f6a45314dc45842c3f0901f1e", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.1.2-y.10.tgz", "fileCount": 9, "integrity": "sha512-iarNYy6/iCKSBGa8C4AbwJVdu3kxnAm4qgt1rVCLwvQIq+4iSCAYiC+7LQpZPzzGyXl90yVzDc6d/8Su2j1QUQ==", "signatures": [{"sig": "MEUCIE6a51MtpmGDYYBXpnDXNprEy6fKzQFy3SooIOhmzlvqAiEA09LctRL94olCCLRcSSoGSqixS/Z5lk5ykUiYOX+RL0E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12397}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.1.2-y.10", "@webassemblyjs/wasm-gen": "1.1.2-y.10", "@webassemblyjs/helper-buffer": "1.1.2-y.10", "@webassemblyjs/helper-wasm-bytecode": "1.1.2-y.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.1.2-y.10"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.1.2-y.10_1520871139836_0.3498540108508783", "host": "s3://npm-registry-packages"}}, "1.2.0": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.2.0", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/helper-wasm-section@1.2.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "c0c3697d9429c3a8f0c26950d0f59571c87c793e", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.2.0.tgz", "fileCount": 9, "integrity": "sha512-4B3QHb60vKjmWzVIAFtoXwooLXjRXm/kKBs8kw8yza4kageDv6odyey0DG0k4EcK/MgAABqhHylO4IoNZV8zsg==", "signatures": [{"sig": "MEUCIQD9GrN32DXthVQsgaWYFuuk7/oz22VJKTy6K/vMPQ8Z/QIgXrlnzoMW8sfZ0NgpuDBordoJ4pukW/3ae+xjl+GOdy4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12367}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.2.0", "@webassemblyjs/wasm-gen": "1.2.0", "@webassemblyjs/helper-buffer": "1.2.0", "@webassemblyjs/helper-wasm-bytecode": "1.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.2.0_1521099323534_0.005548914922186121", "host": "s3://npm-registry-packages"}}, "1.2.1": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.2.1", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/helper-wasm-section@1.2.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "2c7275b67d98a0bac72908f56d0fc258a03abe4e", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.2.1.tgz", "fileCount": 9, "integrity": "sha512-X9jDcQkoTOpJkj5yhuqUsUirglmAMhl9TKM50PaOeYqGs71wBCzysLm8c5yyvlkIs4xTpjSwSK2TfeuccJxurA==", "signatures": [{"sig": "MEUCIHzGWuAq647a4vhdbT0glrnxDaKazJ8mRR/UYnPzAStFAiEAqkid+HUgV8QxkPjT+H2J4R66hE9DNwofP7We9y+s1uc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12844}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.2.1", "@webassemblyjs/wasm-gen": "1.2.1", "@webassemblyjs/helper-buffer": "1.2.1", "@webassemblyjs/helper-wasm-bytecode": "1.2.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.2.1_1521118175597_0.6785924559101302", "host": "s3://npm-registry-packages"}}, "1.2.2": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.2.2", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/helper-wasm-section@1.2.2", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "5a56f867a6d2c222f13a9e4a5c51abbb6764d88f", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.2.2.tgz", "fileCount": 9, "integrity": "sha512-ZVzGHASfDlZr/dxKn7divBN3FNct0wfcy1T66HPcXhZmemQ8f8L2o0eFCtz0fRvIHn2rkR+0R9ft/ZVNnTVX+g==", "signatures": [{"sig": "MEQCIFojNrgaBzhfLA6jS6agrJrlmOLL1K3x35FKXKf8cOtGAiAWgGOGqBgn3BHGXJBlNxmA+sZhqlLZfQ3iFWf/HsUQzQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12844}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.2.2", "@webassemblyjs/wasm-gen": "1.2.2", "@webassemblyjs/helper-buffer": "1.2.2", "@webassemblyjs/helper-wasm-bytecode": "1.2.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.2.2"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.2.2_1522324058356_0.18290298811203365", "host": "s3://npm-registry-packages"}}, "1.2.3": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.2.3", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/helper-wasm-section@1.2.3", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "03cd2f4e3154f855151400315ea0add31d1f4c77", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.2.3.tgz", "fileCount": 9, "integrity": "sha512-WNGjaaL8pJdV36o545TgrtDEUzkf3TwqY8/NQ7v/OaWBG3XLSKlrzDK/wh7YoTGMAoEkFH6D4tu/BRREs2KSTQ==", "signatures": [{"sig": "MEYCIQD25rvHnk/vb7HG9KpE1ttdcrGq4imEIpulgdH0XqKzcAIhANDZSRiTYfTjeShzaIVG3x+JIMCaKuZ7ZmKMytNgEZ3b", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12844}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.2.3", "@webassemblyjs/wasm-gen": "1.2.3", "@webassemblyjs/helper-buffer": "1.2.3", "@webassemblyjs/helper-wasm-bytecode": "1.2.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.2.3"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.2.3_1523096812023_0.08849373617063594", "host": "s3://npm-registry-packages"}}, "1.2.4": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.2.4", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/helper-wasm-section@1.2.4", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "ff6c87a055ee66178e06e734c48bdcaa373fbd36", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.2.4.tgz", "fileCount": 9, "integrity": "sha512-uZAwpKS+OGSO/rpY8uqmSnVTCphWoM/K1Jd2yEPgPE4aHH0MPTUA2DlGoFBHvI0hIvsrNl2CP0VttOF9quS8vw==", "signatures": [{"sig": "MEUCIQCKQLf0sPAK/G4brhNY8NmYEaaZ0D5gsh+ulhChxSTFoQIgO7j4SONefogpCJ4U++ZvgEZM214pHQxFS7r9DysNh0A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12844}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.2.4", "@webassemblyjs/wasm-gen": "1.2.4", "@webassemblyjs/helper-buffer": "1.2.4", "@webassemblyjs/helper-wasm-bytecode": "1.2.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.2.4"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.2.4_1523615494415_0.4175308376754079", "host": "s3://npm-registry-packages"}}, "1.2.5": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.2.5", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/helper-wasm-section@1.2.5", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "037a46be1257c583cbb96683be422d4d0366a649", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.2.5.tgz", "fileCount": 9, "integrity": "sha512-VQphovLvVQdltZYu73prsebXF3CxORKDoT0FLGrgOHCoJR9vOabv2FRCGOhTLvSCfNu2Mk4TyRtQPkUL0f3lzw==", "signatures": [{"sig": "MEUCIQC0dWUXO+LklyJeFUqgQxSSjrMwIvqYe2UU8eIAvsMJIwIgYL8Ujc//1SwnzLKpcd20VWSZuTLgDPrQnsA2VEbxJDQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12844}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.2.5", "@webassemblyjs/wasm-gen": "1.2.5", "@webassemblyjs/helper-buffer": "1.2.5", "@webassemblyjs/helper-wasm-bytecode": "1.2.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.2.5"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.2.5_1523639225293_0.6487737087034187", "host": "s3://npm-registry-packages"}}, "1.2.6": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.2.6", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/helper-wasm-section@1.2.6", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "7ee1d1e029e9e9de9cbdcdb4ef81ddff83f8b47a", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.2.6.tgz", "fileCount": 9, "integrity": "sha512-QO/4gzwQyFnEzj/VwaW4PD6hGxMcdK5Cs5i67wdPAhDIH7GsDeL+G3eapvejPvcS2zwmrxQMqlb1mWVgRmRLOg==", "signatures": [{"sig": "MEYCIQCBCtMdmXrnyn/4cSEqiMsjs+5j8s0NiUm0JtGBiklCDgIhAJxRH2YECOBvyP4GgIHeFKimdIhjPfuAuW7jrngySkRe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12844, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa1hCfCRA9TVsSAnZWagAAIJMP/3D1b9CeKmGnkzHfYws1\nlwgGA4nAi4fEzWcGC6HfgwFBU4auSNxuKzuOe6f5/bjvwEJEOP69Ghbx6/z1\nDiR5LqdnBLcVKBPFczL3ExCjPuZh1dj1g5nGFJfo9bQrIUjKYHqi7k3PeKw6\nXF2Y85sNMmefcEcQq4CgZGqNEVeCtPeji5rRqhFen93JArp+Hnl+jC9b/HW0\nLncdZbvfCOFY/oiGmWnxqrH68k3UBcCEwVMcjVArd5q3ZnOgI3oE22NVYTnX\nNF0dEZSWYwHxJyHiqKqsz29S3C0W8C+8FdqWPmwmYS5d6RuhATxb2s6u8oyh\ngseRtQeM51wgWLQWBZ7Sqz8VlTtYypDS2bTwsVtqTfhwapuzAPm4ZZR0wzOo\nFv1DnRR/kw6/mgVJSZZHTt1CFq1htos7Klo8y83HPBylRgYFiGmTgYyD7W3j\nwFQ0uY+fg4kfG4cIZAvo73eZ52Y8HR+PQwjFptWgjGfUaXVxztHHiWnlb24s\n0l70FC4pSo5pSgQQiThYPBclRAqWjr+JG6Jk+4W3lfYxPtrPna/pFhM/bZu6\nSVhgiz5915VlYuh/CvLc/GECCQJ+wd1INB9m8zmXIwif7oGx71C/fkLnBA7S\nZrUyLVxseI9u4Mx74mqxqilIxL96lJJ+WGvO7k3dI5qqmAbicdPdOVKsKMWT\n8oLn\r\n=CS+W\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.2.6", "@webassemblyjs/wasm-gen": "1.2.6", "@webassemblyjs/helper-buffer": "1.2.6", "@webassemblyjs/helper-wasm-bytecode": "1.2.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.2.6"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.2.6_1523978399105_0.4935365549949218", "host": "s3://npm-registry-packages"}}, "1.2.7": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.2.7", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/helper-wasm-section@1.2.7", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "98ae0312f285fe4ae6ab2c3daac3121cf078a568", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.2.7.tgz", "fileCount": 9, "integrity": "sha512-JbBdxBw/hFN8QnIJ2JtCRIeU1afC3/LRwZx9ZnHcO+AwJDpc4upTAD/fe1sz6CYdoWKgIfr2sI/jYi2fVwuYZw==", "signatures": [{"sig": "MEYCIQCWGKmF2y3vMPnazGryrLGvqFyUAI2v2otbdJ0I+mjMvgIhALcu6dKboYIBJVa1ArxzZiedKO+47lbLjlzq8JL4Ewxl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12844, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa5yuGCRA9TVsSAnZWagAAU1kP/3zdcUNPc/OeQEifOKHt\nI5tQTWvcWxsEPeRkRS0gCDrQdqCWPZp1TfRb318nB26zZ3TfsE6cCZBNLJYk\nxtGKs/A8kH37iJBdtP7gT01prcDlUeImEsXehxhRj9wr7CGJjRak1dds61/9\nv2munuk9lxHQqua9SwoDrZa7L/VevKWJAb7jS21dMG6ywzMUKTSU/gfjSOHG\n/Z78Nfmyxjj3sDspq2/AonxsH4rtxjFl0jQlDTRanMzrNMkqwKQYiEpJHqVm\nk/uRZfPrsji0ov3yrcLtNy+DWjZ7/QGIWb0zq/ngOsfeV/AkdNgT2BRI6S4c\nTC8juaunR8o+8TwkeZF0w+LBueU8MmvoTEa6iJsWc8pSiP0KTkNFSP9wAZqm\nUwIXYpLi3KOiiU3bBqSG8+fIRd1IHKlBK4EAZR+hmqwvuIWqFIm90Q+k9gm2\num0k6d4IsmdFanrcDNBKeYuWh7bwufdeNoJKfKZDaWcWJvQCo5FoMAScraqN\npZNyVnI+mlQmttKMrZ03aquhdxu52BsWsSIbfW6rWJ6bILT0VkxcX6Y+yutI\naHnhcaeUgEmQwVke149uMynZTun8zqf8rvmjzSsXJ5pk2ppMMF/lqBjuiuv7\nBaqo1XmdxOHfihrOfgZMo0Oi0VMpBsXN/GBMn59gEss0mYZkaVyOUDoY3ow1\nuuve\r\n=XwpC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.2.7", "@webassemblyjs/wasm-gen": "1.2.7", "@webassemblyjs/helper-buffer": "1.2.7", "@webassemblyjs/helper-wasm-bytecode": "1.2.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.2.7"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.2.7_1525099397991_0.4365844782143209", "host": "s3://npm-registry-packages"}}, "1.2.8": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.2.8", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/helper-wasm-section@1.2.8", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "a5b4a0d835283d8eadbc949679afc699acae4228", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.2.8.tgz", "fileCount": 9, "integrity": "sha512-a9vH/72X1+32T/8soNHtNc3d+SNzgMk8lyw5Il7Y6GJFMXdu+oUkJQZnnQucKOlwwCRkgoqX0gDCdIJ0MGzPvw==", "signatures": [{"sig": "MEUCIQCeBopOoeZmMZwYmIFcbxzy19yNbCofOF1Hi8Qjg7OiagIgBxp5zMOs4VPCeUloV8wzH3E+ek4TUBziFAz1+HJl6qE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19331, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa6fRgCRA9TVsSAnZWagAAemYP/2y4dzV0I93UyFf/GV0h\nD2KsdYurSXxuEeSyMKqmA94BAsG5TZlqVETJo67GsjYXWWNA7b3oMTXPdCH2\nazClS6m87gTcOamG1paSIda2gWYrJ7f+XXBj4cMfbMj9DfItzQypubaetWbk\ngYALvYcHttpHor0kanF5v9XG8JDXhMY1dPkbag5SyA1D2I7q5uRhKglPcbfX\nByqN2W4f2Un684CjHwAQ+XhonqSSfGNq4wcYwymiz5MiJVl/WwwSJj23shzu\nNUPCglnj8D44F/NlyX/YQB6yr6gJsy0B74UgObqvVMhBjfmCR2gVX92nsg8A\nYfJDj6phATO+dTohmSd+uYmpHNm+jMNmlqeWJH6ts6VWJHTOfM7/VqTqhtML\nZxqFVK+aagrpl3EPaO4U8Mf0AcgQw97rf8HP9ugkEf1GqRZ4YP0GjzTx/d49\nlCm+s5WzmJ4sJGn9WrSxltzYZBq0Xdx7Ip6qUW2KdCnFeJ+cUk87JjfqSTx2\nIzfXFrrx7Oh3C9h3SKWhuVLtGezROCnaqwJEJmy6eDFcQnyOlH6E+vzlmQBr\nzQOk7yjrHDo3iUAM6g25dkTdH+1iIh2rLpFna+OX3Ody1iuLFQuSy4+TLdMV\nSrAxAG26Y0i3WabZTR8X2JkufHyw9aljKxWe0lxdKgFM2v84/IHsjFmQLhHW\nJXEm\r\n=QJj7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.2.8", "@webassemblyjs/wasm-gen": "1.2.8", "@webassemblyjs/helper-buffer": "1.2.8", "@webassemblyjs/helper-wasm-bytecode": "1.2.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.2.8"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.2.8_1525281887701_0.10537263280324116", "host": "s3://npm-registry-packages"}}, "1.3.0": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.3.0", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/helper-wasm-section@1.3.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "a8c9435faca44734fc67dfaee4911ac8e6627bd7", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.3.0.tgz", "fileCount": 10, "integrity": "sha512-1Vt839EyU7YLy40EXF19b9vaYdSeNDFWQHsY2BEOVePatbRaz2ytbVFP3lSjZDTBmOml3nbEdUzHC+XtObBKXg==", "signatures": [{"sig": "MEQCIHcbcgVNjpLiqmfHuoc344eBAuHC9Z6CkMqzUr+1cQTRAiBsuUbWZWptDIQQaMFebYXNgoRWuXfsHjxaTavHp0ci5A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25990, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa7GCVCRA9TVsSAnZWagAAfHUP/jsNi9LxjwQUXD9WFvEQ\nGwy5qNapY//6b9zBE19aM3wwdx55O2afptmD8a3DK7vXwdSdAAvmBlUgKDJj\n5q2/vEPmlFwuGO/C4g6olWuOuYhgQ96+7EjqYLF7lR411QBA82wuNRvYCJ7d\nEYTvyxAVILjvKNa8xpBCjGXoTT4Ff7H+x29c6+fWjMvGoOHau1Gp9EAAamSs\n2N0js+/N8Gc1O0rQoLhdaPERobKUyH8q0H6CgX10QlAmmWncCr287KE16KmQ\nzGwXHC+zmr07UmeMBBgliBgsltVegSDe4q6J+aekTq+vY6o0fSqTVhhVf50X\nyDyEYWoArfrzNg1HCd9/fpcJX0FOIangfcAvsoFJIef2AOl6EnyijbYsSwL2\nXFyBeSnu0dZwFLReXuaZ5Q6j3nxwC60bFsrXdF4P3ReS+sx039ZT/okPD4bc\nDang6aRACqnkRcZKwJT0oud22ilQtg7TRqDjrI+jj4seD8FI+2bl+0a/zu3F\nzJKTW3+SCFB9NRskp34qCBh6Cibr3oswXrWWIhG/QpPWAyMKAZkkcjDkmKtx\n1DHAGOet1eKDoFd8q/JQBHCBkzS7h3+uP9ODZzD78uN9EEXOniThSAJX+e1d\n0YmoLKcPNyqkeyCUF9F1xB/+JQz3Q93nCzUkvv2XfIBqkL8iMVenqg4E5THt\numeL\r\n=1ut3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.3.0", "@webassemblyjs/wasm-gen": "1.3.0", "@webassemblyjs/helper-buffer": "1.3.0", "@webassemblyjs/helper-wasm-bytecode": "1.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.3.0_1525440661131_0.9390415434660526", "host": "s3://npm-registry-packages"}}, "1.3.1": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.3.1", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/helper-wasm-section@1.3.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "3df13898e89a376ffb89439d216d9f0001bf9632", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.3.1.tgz", "fileCount": 9, "integrity": "sha512-mDBlDsrsl1Y7LGEdmlTzdDwr/SCe2l9ZsQZ1GYXird71jlU/0djs5ZXfxyufd1G3wO+yuxW997m50Patk4s1fA==", "signatures": [{"sig": "MEYCIQCIUSUw/gmDZ/BZbFIzk1S1HvMs3QNkmnSsfsEgXQsspQIhAOPWasu5Di6oQ7CmLbWoucyDPFXOEb3n28NySe3cUDfZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19657, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa8JY/CRA9TVsSAnZWagAAwZgP/A1uyidLZO8aRexWNHn6\n7C87r4S2UQASjuq3017cp85RjgJpAgtdw2fL5+GQDzVYAB5xC4Uijxs2erJV\nXDl+TlmxzCy19Zq8A91nOMAJHbXSW2/8gRWacnbNEm1uTSlOt2C4jFQ0MpJc\nRSnn7sGf9MXB53WP5jdg+rc4YXQ06dwBNtdyG+ysF/6iptTWm84njX3YK9qw\nECeFGHYRo+pKJF6ao9HGmiBIM2h9lzpL+IW+7ElT7pDSGc9tGFdNb7EwhboG\nogBKVHooYHlqPGFMCOG9abej7pgjbtIBv+oRFlcbnd6zV7UHmMJ+kbVJV/sF\nhUWmiwOYe/cLsZ2i/BUHbiFzhIJJknGbj6yDjo26hv0hrgttvVwVemAzIGjh\nCljmUfRqI5GQAfdQzkawAGxQkUtVl7klwpVmlJl/5ndXqKGSQuonFpYGC1tr\nipehNnn+kOicjkVqiELCtRY+GpOqV2E2yuVkuRiPxdIbdp5cUukywSyWHQ0a\n48BxkK6NGeb6VYN9NAWxhbPq2utgFI9eXZJFE6kS1AaKUMZgdEm6tgz+dy57\n2S0W4NqmNVobTI21lm8ftYypov/CBozk/s8k7MVKcBeJIk1n27WWuldn1vkI\n4zb/EJvmG/Fr+nEbpwVYF6ycaRARe74RgKG9BdkHHLKfvjvqzZ5Ygy9LJYdx\n/ikM\r\n=fads\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.3.1", "@webassemblyjs/wasm-gen": "1.3.1", "@webassemblyjs/helper-buffer": "1.3.1", "@webassemblyjs/helper-wasm-bytecode": "1.3.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.3.1_1525716543047_0.5660955813697204", "host": "s3://npm-registry-packages"}}, "1.3.2": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.3.2", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.3.2", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "b05ad3b21e2d94847242813ea2df6931413ddd70", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.3.2.tgz", "fileCount": 5, "integrity": "sha512-hF4s1Zy+FyMB1ce6V6w9d3bbF6YaaK6HxFJUo/Qj0frnnpRtKn0z8c+Vi97FbkmZ+9BUzR0Iyyf34MhAIv5u3g==", "signatures": [{"sig": "MEUCIQDU3Rz72j3dm29LdbOl5S/h9TlAKTJTi4jLHPgKPeAAQwIgao9CzNJT50Oa0t8IaQ0PqIY37caaZvzwNjnCSkNLO1E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10693, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa8fCzCRA9TVsSAnZWagAA7R8P/1weaCdBND/YUIW0R11n\njvAjMX+BPkRNk7MCe3109y+JYDK6cfsnqW8fVWjGIfWEa1y62AMDGmEDImay\nBzit+DRrYqby1jKRZeVBLh6i4lIbTIUNKbRbVwujNtRrLHzYZSlzbGiaTTeC\nX2moBRBgLzZnhIQd0moDeALVJvrq0QmE9CtiLBOj/qu1AWSc7vp5JdiF6E+9\nNV7VQVzGsIhUn5n/4pBBKRUT2lIVeFzGyzuKKWBcxERNnw7mtg38G/dPfh1s\nomsnq6eGnRnb6IQPVA6BSK/U5Hgt/ha1ed5KXM1aV75RqTxaVbU1VyjMZ8fv\nh93OmNcQS6Inp06eqMS2pUHAiSOBFqnhyk2JTtuypEOiNNbtNky/rXiNz3JH\nQaCvMRv0qOHZ740NvrsyHd7GdTdIz8WGBIMTp8bp6zMq7oBSC91BwuUDArTm\nnKnMLkfLxdqgHBcl5i8jAGN+SffpH6opS6KzsB31+VPPc11EXp0TPG+QsVBw\nFHCiBDnsF+86swQth5PPIyPhAAKnOfhVb3E7D+B/pQi+Ti6rALfBqVMIHGfz\n18s5DacA/NG30fFTLPTZHXyps8wHR4Ot8DGWTkdRqLHKreh0ti3PLpa5OESc\nEpxyhThBCGkD4jDA8jtVNTzDxJA8aa9Ub33NQQWzpzupzzuzxd5NSv+tqyyH\n1kJs\r\n=lpg4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.3.2", "@webassemblyjs/wasm-gen": "1.3.2", "@webassemblyjs/helper-buffer": "1.3.2", "@webassemblyjs/helper-wasm-bytecode": "1.3.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.3.2_1525805234677_0.9184089655092169", "host": "s3://npm-registry-packages"}}, "1.3.3": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.3.3", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.3.3", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "bd04a10de60de8c969bf66d5c7dda40384d0f133", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.3.3.tgz", "fileCount": 5, "integrity": "sha512-qDwcYn539NEQiwt95GvNhc6Q0vcwohxDxQN7AXK434CMT7HPJyQ1+9tLDCt5zXLpJgXAW+gdmbsW26CT7s1OkQ==", "signatures": [{"sig": "MEUCIQCZdmy/KbmQET5XKt3TX8LsD/vqGp64/RfPoukhTzypdgIgGz9c1gKHPGfbH8mEAwUGAQImTyZekR2F3002sIeML1A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10693, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa8qjlCRA9TVsSAnZWagAA790P/jGm341MorkJi2tfu52t\nfHtWFIiNPx/wCBaXxUZGG004i4pZXuKP5goC9knrhOqaLZEYPaNlOj8FC9ZO\nr7WV8kPnA0Jxv54CdB06qo3DB21uQJy6WvtVO4w+98NCLoQ31y0w1ctwulQ/\naC+UrslPZOzBjlSCu4HZoMPGlY+GZj29UeoZtFa02krkZ2W+ZRjdKVAyoBZL\n3P9i6G/AEAUUiDR3EjPy+0MbaGF03P2hOLdI4lYjzTPt7c5ywH3cFO85XJJ/\n64Vm2R/U1TgbNgjn/74aMXykP2OPtsP71aqgHxANxqoK62RM/cFpNUwAiUry\n8c++tUtVgGI8h8rFutk9iE1W0UqbnHtm3HAGTZOWmKPIixfVVthg8zL+cu+X\njikdh0kTgkWYAqCqaRnl1iKmWDg3DXdnQb8FwF0BaRwqPcThVbeA+1TH642I\n5IUKKskvxP22wzUOkuoX3sDHwixkmY0UM4J2wWzw88SgPgKhlNBBJOo1lDOL\nJZVc/C7OrQGrycB7ixleQJ0uugOX6BrR6vPQskToWtthREPNXr0lUWUVk/Sy\nNw5TIONgICpfGCCmFTybqSvpYSjoZaGUyc8isBxkS3J4Ridp+8mU83DolK3X\nhceEBqFxLcnZ6SyrHz062lP2LdXRY4Fzu1WmlEK7t3ZYv+4f+8Q7ANJpzKS+\nawfU\r\n=qAJ1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.3.3", "@webassemblyjs/wasm-gen": "1.3.3", "@webassemblyjs/helper-buffer": "1.3.3", "@webassemblyjs/helper-wasm-bytecode": "1.3.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.3.3_1525852388863_0.8853775432707645", "host": "s3://npm-registry-packages"}}, "1.4.0": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.4.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.4.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "9a46e637924312efd193a3012899e0aa4ef486aa", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.4.0.tgz", "fileCount": 5, "integrity": "sha512-pjUWVeODogMwgKInNSbEZbcq4wgxrpAzYhA53+Sl+IFN8+3YrJlOyFk8dX3N+Z6JccEuSJlIyl+LKsVe+nfkOQ==", "signatures": [{"sig": "MEQCIE1VkiEV5nGSspMsGq9j8k6wyomrk41cRYpIbwuug+BmAiAvHm/5wFE0SolvWguRR0BZ81T25AkbL596ES6OImMPzQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10152, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa8wySCRA9TVsSAnZWagAARAEP/ibLoUyolW8bNzZGWar3\nBn1pm7vGgbuAAnH7kL7UPmBIpUBXXbhefgxlv8om9GB4swI3aF11pgMEm2Hw\n0rREUqX/nS3Z4BY+HunLsIJfU8IurzUkxLqkj3nl4vr2P0E0ml79Ysw9DsWx\nmMWgSqjqfznJqpqIKo152DVtXjO/7rb2x+Wr/+dB4E3D11xDw8/LZ8VPYzfg\naBEbAUGK0xP8008mziBOeGww/ZYCj6xnc/BN7O05xOIxRbuVTK73FR+e6qCy\nzjQHu2NxAK1Njzyj1SwlfsMLTfZxwuNe0yNir2/05jstFH9avjfWQlYZWvTn\n0rdyLHnvG+XzQJNctXkJ7b09F4O5+8Q06FwUFbzuhDKc9DWXhSqnaGClP/Hx\nxAKn7VuooJ407sLbY/HvbRMQhAyzB5vw6MQ8mdrzo1iIXkQ/9HxRtNq6ysOH\nCnJG4hVQVO7InKMGHiLmA+3nG5USMsXmim3yQ3Lf7Gi2XR69g54+zlBVjdYz\n4KL7AN8WJ0pjOCxfHSNsxcjMH8dsPZgAVYyzJeWI+7p/W8JTTo/qaBTrHGXu\nB+X8vxSSq2m8jFmEpwQxPODoyASt44VXhWoxZOC3sx+kGLstfaSjVrsJ2Uq+\nLQVW+/fvqhSwinT1erLsK1pb+fIfV0FwDAtyE5cM2EMfI3MxtvNI3zIG1Qkv\nP8a9\r\n=QsH7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.4.0", "@webassemblyjs/wasm-gen": "1.4.0", "@webassemblyjs/helper-buffer": "1.4.0", "@webassemblyjs/helper-wasm-bytecode": "1.4.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.4.0_1525877905557_0.2781439025118222", "host": "s3://npm-registry-packages"}}, "1.4.1": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.4.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.4.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "a76753d812c56c692ee8e8b412772e388fe22f63", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.4.1.tgz", "fileCount": 5, "integrity": "sha512-bohsCSdU2hHKgXKFfLEY5dsli7JHvCa+iKATwYkD4IbJj50Ot/fX01vSgEl0BhtsSrjsZ5BKrfC9KnllmTt80w==", "signatures": [{"sig": "MEYCIQCdZK10CXcfMs35myoKA9xKbWO82q/wGcCuUmFBIaQY/QIhAMX/VYWABEyOT9nI1ivSj702jUadfrYT2XXLH10knHCJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10152, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa9KEtCRA9TVsSAnZWagAA6SwP+QA7qf83Hrofz3DnlLxy\nqstxdnnIZVXxdL2eLyHusnuwIY7+2UgLe43M233KIlCSQMmxJGxjjoJ8JzoN\nveRutuk1TclKu9JBdRmdyNOhUZSOKkrYLl289L+jsDe3IT3ZYMndB88NZcm9\nuolFAiNMQSjxOIgSt2Y/lrMb0+ATFUZ+D02BCJgGEmr1Q10pyN83Ve6fn6F5\nF4fT9Gju+J2oN851oDGunG1oI+uICG6lgFrGJfYZ4a1hWN5NTIfxsJk3L1Ol\nvmN5v99sdecFD/JoSjx+sVvXdO9+vyRilm5BarjrryxclO8AEZoTLdC2ce68\nNjdyUP4FsOHXgbmhxaw16xA1N4a8RcY8eKh5M9ZYJqyRiOJpZz6bP8JT4+Eo\n6c500xdiZNtLocAk9Us7AjShp2OMIwikcexJAOEqMVJqRoQTeCtz9kRR/uZp\nNBdQDu2HOhqDJ4Qye5SFsUoYKW1wDc9WkmdYKzGT5/jl4n6FxcaUKOvw7VZL\nzAEthgVLF1Jtp4xOJ/GSvTvTzLIIoe0LFTJ2u316qGRWMrIeq/k4KyHOGkC+\n+1OtD5foxtt7t4c+iYromenHMMcumkdwsSQ6CSLBWtULhxK34uxw4nXLwQH4\nhuiqpvfqWW1UdTMPAJbr8xgdCMybgKlX/4g+ZBAH94HqHlm3kVBkTXIupGPy\nZhTi\r\n=BLF9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.4.1", "@webassemblyjs/wasm-gen": "1.4.1", "@webassemblyjs/helper-buffer": "1.4.1", "@webassemblyjs/helper-wasm-bytecode": "1.4.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.4.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.4.1_1525981485306_0.8961798689603193", "host": "s3://npm-registry-packages"}}, "1.4.2": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.4.2", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.4.2", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "520e02c0cc3e5e9b5f44f58abc04ba5eda6e5476", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.4.2.tgz", "fileCount": 5, "integrity": "sha512-o6qhhJO4EQbvJfK4vVeO1hBiJNyBuZDKoBB1Yq6BEkuLd+cFkhVGFQKGVuZgxTNLuDlrlgyWqL9ZRvUV6JOf3g==", "signatures": [{"sig": "MEYCIQCdK85VkrLfj3fyzH6xLxt6fXG8gUuYS6ziIo92bocwcAIhAMrhRi32soTNIVn9+k1dgXQAS6dKEwbMuvvIONy8dXJn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10152, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa9aTwCRA9TVsSAnZWagAA0dQP/0LKJ7CRmQr3/f84lm+D\n6Pv0gkCNgEXBA9b1d1senxQx3uRlMnrQF1B93U53sHJZ5xPd6HFwxaY7JBfS\n/GY66InY0ETphc8CEN7seQ9VP9TPZpADt2D+dg14Zy4kK9v9O2sqlFHA2tsj\ngPmOuqALTm+PMroZyLG6t89zkVaa01GNavzowo50Ukd0y0+8Zj1xQk1Hfy1O\nDBKZyvHF2x0Hns7ABF66rAth9/VWxJYbZsgDJqwzI2adld0xRiYqFgENxl60\n5x8hcrdeGXZxjGv/YiBTxGpq4wAglMJDYC4HqKHxZPml6/xQkfCycP+uh5+1\nwKd90uXtm4kkJj0+pUrPpYZetqcJYYh4ZKVNIA3b4txAjW6XATR+fDrzQflI\nGHbksr7+BNGwDmr/wkzvCDsqHX2pGS/3I7eMfkb6nbEcxAI0bl+R/ukNmR5c\nuOwrFAl7RFpKzuW2kbB0eQyM9g0z0j1aiK5R8opJTaYvmDIzcZMD5xzab9+V\n2bat4wjb5xpmOsOfJjsjvd1nCDG78rPFEHQPOeg7lpBXPl9O1pUbLaMqwd0x\nedyqNR+rFvqS/IXTdywjzSZDkr1nRUS+yp1Z+C0+K90H15AXY8pGPSMZEZOU\nld+vloy7OmBIgnVr7VXuPcvQL3mQmuJoeV91i4vlY3n/XpaVxwlj7WLq3QQF\nm0QX\r\n=d29/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.4.2", "@webassemblyjs/wasm-gen": "1.4.2", "@webassemblyjs/helper-buffer": "1.4.2", "@webassemblyjs/helper-wasm-bytecode": "1.4.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.4.2"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.4.2_1526047984173_0.2514274630464364", "host": "s3://npm-registry-packages"}}, "1.4.3": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.4.3", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.4.3", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "9ceedd53a3f152c3412e072887ade668d0b1acbf", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.4.3.tgz", "fileCount": 5, "integrity": "sha512-p0yeeO/h2r30PyjnJX9xXSR6EDcvJd/jC6xa/Pxg4lpfcNi7JUswOpqDToZQ55HMMVhXDih/yqkaywHWGLxqyQ==", "signatures": [{"sig": "MEUCIQCdfy7gp7cFETYJGsf+f4CnYUUaH59AnXHzB5x3lrVaowIgOmXpMyxX1McHFOjk0q8BeBxK8dKCjCcFomQXReqz9Q0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10175, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa9rIDCRA9TVsSAnZWagAA1EEP/AtsAByaTCkdfLgYwiWT\n+VBDfbWLI5TxdVgthOnpgbgio1/agw+5FvPVv/Hgfk5VfJj+Vm0/y3CWsqzb\nu+jRV7yWCraCubMMEUrIPR1g2ZlsP1qeRa52xfLoo/7xaJspHkcrUAsJe7m1\nkz+O6gSqMcRlMdhwNC3f3CfBcpRd+mdik9/dZipXQruQZf5hlSX6AeaLQeyq\nJwF8V99lmBSN2vG8x/qHv/hjXlB25Faw5qhdq/hkE2LWFhDOQBYjJV5m/Ghm\n2dbNwiOs8CMKyC4rKVZ7jfClk/1WBdKxAMDZnBzXc/6RXqTUAssPCGxiLLXA\nhzCADVrZoCPS2CxHwJcwZRV3kc6oIR6duRygckM/+m+TN3DPmPso+LoPhIOl\nT7CSoFoSUjhdUGTVeGcf68SViC30U7UIAD6eecLtxlCbMjvxzdP2vmVGGQLp\nBSNKOb6G+UX4OQdbIch8xdVtZCnhiNqslnY7olVrQoaOTExwEsUzCNCEDLcV\nE/hC6Fnb2SmYuzlppgkqr9vv10YujcuUb4TjbFQ1xQaF75jIEaPWD3OE9xHV\nC/KJwuKcO01FJeODdWLfMnMGGq+HfeA+jlfxw88XRoONGkZirIqEyg5JKlgV\nafU2eeq9+i5niXq1CoOVe7t9GV35gIddvl0bUN1Xre0pBg86RBav7SkBeVoC\n+24G\r\n=5MNl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.4.3", "@webassemblyjs/wasm-gen": "1.4.3", "@webassemblyjs/helper-buffer": "1.4.3", "@webassemblyjs/helper-wasm-bytecode": "1.4.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.4.3_1526116865967_0.9228789898618337", "host": "s3://npm-registry-packages"}}, "1.5.0": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.5.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.5.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "d777f1b29394de1d8d1b01f354724c8e6bcc5cce", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.5.0.tgz", "fileCount": 5, "integrity": "sha512-OIebl3Bu2smzjd8P7Exij97pVD8rp6an2H8yv1ZXribu3JVLnZkDh0TKfdAK9OevJ6LmTcvonHMlWxvASVVACg==", "signatures": [{"sig": "MEUCIE2ZMBplHaNk4AY7lt9M6ZByjPvHgJfDDeunPSZkWufZAiEAySgAzpLQzF3mDoU9UtEnNWdp4uc82r37gLHCjKb+cfo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10217, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+b2MCRA9TVsSAnZWagAAHLYP/2G/2J2WKviNkFUY7N0h\nR9vVj/WjecXmAw9HVTxlO9KhvGs436YOn/lkmkgySzekAY5NSkua2Zzvz3LK\nw4ms3u5Iah32D6LxZTxnirkblXhtiyhRQPuX+n/GZLWOO4Gp2oAz6gP6LXlu\neEG5M9EuN79i7HEbhz8r8Xlmfjy+sUenIHXUz5woCLyu5J2F7OpL1M9Q7FXk\ntL3uqcmSsHyjDRip97PkDwO8Z+RMGQDqv/d5rQl+czKjcwsjUPab2pz6sCjL\nY5UAM7OypS47wRvvb9jFtb7NyU5MieCpr22yogCvuz9QOnvGIhHy/dY4mONb\n4zqfkEU+BIcKWHcmrdtm+mLEneO4YnVGvNGZ4khC6H2KzLwVQkYLKZhl4aOq\nyyhHuRaP5G1H9Mzx7qentiHgqSvnC+X1oL13bzdpvx9aJEoA4E1E7P8sBXho\n0Pd0jNn8mTPjLLuzWyPGfxAn71OY+CbjS7ZukjZ5v2/1rRx53Rlp3r20yrwl\nQfyOpgEQv38OiNIfOJvte7kOCGat4RTGrjbgGix1Kvwf7BDFGsUOGJghwlXd\naRdonurt2k3xGop7ELWGOI75byJkJibv/LOSzNJ65Y2PBesLoiHRj041My6E\neuDxDzHgWEHqNk4y7wNyc7uQblzATSW7ZkfI+wwyiOlBJV3lU98bfL7JiaaG\nmniQ\r\n=jOsH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.5.0", "@webassemblyjs/wasm-gen": "1.5.0", "@webassemblyjs/helper-buffer": "1.5.0", "@webassemblyjs/helper-wasm-bytecode": "1.5.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.5.0_1526316426836_0.8935068084600892", "host": "s3://npm-registry-packages"}}, "1.5.1": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.5.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.5.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "c45b3cc233c5067ef44cd35599b07aff279c531a", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.5.1.tgz", "fileCount": 5, "integrity": "sha512-Qq5du6Jb4ec+Xp5C7BrzDZybL9qLRIPQBWETDI0FPBt/Le3NwTIkJL46XTt34Vpt/2GexsC3Gx8pk6jDZPK9mQ==", "signatures": [{"sig": "MEQCIHDDlelJnDk4eTCEd8KHQJ0N2yyLrql8Emzc5CV56O2HAiAvm7Ifm2Tm+weADHmki5nUnOQLCfrVD8kRdtVeMSqG3Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10217, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa/BT7CRA9TVsSAnZWagAA8vMP/jeLidUVjFSBwWnyAoY4\nbZb670EpBSPyniNbAn9wKu8soh5yfOfj0vBRJ4lbGWtlNwk/crEVmweVdPOQ\njs7xOCv9id+jrRxW4cQsK4r3uJJJ+vO2k6IpvXJTpe7Y/lUv44xkktXTfeXt\n4wpDtbhrIxcxrOmhCw5Kh0HgJXIjAb5WR8tSOz3HZ1eVp5rSU9Sl2VN1GHlQ\nePwQd5GWcTFUiI3ecp7h4aR2wYebqUyYebrDKd+XyN4eG1dZ+vwq3tygzTO9\n/jdWP6ECXVGHSeksVJh0sO5MeYShxfHqJCykPEN9JTMU+QPoP8SM6FYm0WZh\n6uIIqwdink18eN52xdFYRgmPkErm/RfgdEDd1wDDIHN1lTE58H5kKJxJt9dc\n0TZEwrD7ws8eSdzU4Q4PO0Ylxq8FsU4YA7pHuaHvMfjkGOzNkB1tPVgkT4v2\nBJ6Kg21L/mQ+PZvmjCr6dbqJZ72k2LOR59b/1+Z1uNWaSrlMNRq7it9YKlL0\nth+LQ0V2wSVX4Er3ldAHcqP0Xp99PGECN2ONIc22pf66zR7oackeB3+B6wML\nqF0OlEWAUowMnd/r3pI1aUu8hOEfYhnCBkSHG2C3WeczXZZ78ZmCux6IhRzZ\nz/XIcKnX0/O/DBaMosXivikXr7BglIqxuo8KDkzA23Bq2RGc2Q2thzGgtWjF\nBqZq\r\n=TNwm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.5.1", "@webassemblyjs/wasm-gen": "1.5.1", "@webassemblyjs/helper-buffer": "1.5.1", "@webassemblyjs/helper-wasm-bytecode": "1.5.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.5.1_1526469882738_0.2324701940397247", "host": "s3://npm-registry-packages"}}, "1.5.2": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.5.2", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.5.2", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "9883f523878ce046494ea1e12accc0b54750df6e", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.5.2.tgz", "fileCount": 5, "integrity": "sha512-7IXmqpppobmpfs5PgJysJpJF3VeQPSdsmr05TnGXxLtbWTCbLDBvtXYOiusS+joutkZmC9F33dB2kkr5YrzSzQ==", "signatures": [{"sig": "MEQCIAFvl449Sm2CvE55X1IMrOT/vjLeg2ul9ZSgV2wCLu4gAiAqvCmItrvh4jD14YJdMZRJa9q/D8Uih9UeBV0ZhwlzkQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10217, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa/XqTCRA9TVsSAnZWagAAHTsP/19j1kMK6fBkHk7CRM+O\n/eIqzYKTjX+pcmGN1fWcIW9mt02hyq/fVVlK3CmpoeUXx96iRdxwHJ2tPMFo\n5KNR+FF/x1MEtr/kKbuDJBNvWJ5+mTyMVejEmSY47t/41H3cwMu0p2vocbLj\nyOa7cXM++PXRvQmy+HcVvmXztQCeLzf9WFz672+f7MaFbBbf9+ShtznnlmEb\nTLDIjLq73oG5ELuLOMWizOghmDCg9+Ro+/6Knwmnocykal2JKu74tAnkX+Ym\nNeXdHdX1oAfDPpY664l7v15AuD5qWXzkm8N+zwWQKBa2jNovO3tY8IHw/3po\nCEMZUFzBySRNrLab05m+fSIJY3/ANONKvJaYFPxQdcghWpISLkfdukMgzsSJ\nuVohjVkx5dkoQ2FPUajEV7NKqBpjq/XcVfFUlVhfQ5O56iZ6+pp/wGDNjU/g\n8hkMiwNznG4739WrlUcqLYoglaqgB4ijarTbalHpxPV1gUU+FGMvJCx38rOa\n3V9/b7ol/T6CHXmEFEpanJx8mFxj+QPaNvOGUKqVgJwgB6HGK/pEmkbgjPrh\n33Sufn9jPueARRFZI4v6HVeutklBXSqW73rP4ugov2BLJqLwFQFVnlMOWwNn\nYN5WO7mEOhTQCZKzRmhPN/ZeX7abDPB59YUU3wL34EuZvS0s4Hbe0N7ukuAl\nS38W\r\n=maTF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.5.2", "@webassemblyjs/wasm-gen": "1.5.2", "@webassemblyjs/helper-buffer": "1.5.2", "@webassemblyjs/helper-wasm-bytecode": "1.5.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.5.2"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.5.2_1526561427023_0.5515614305812975", "host": "s3://npm-registry-packages"}}, "1.5.3": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.5.3", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.5.3", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "dcb5004d138c416cf60dd819554592de75cc6184", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.5.3.tgz", "fileCount": 5, "integrity": "sha512-v5zRMvMY4h7JFgkJmDweNfOx6DER0YSzBmiga2LtrTyw/1QYT+P9/2eeC+9Z9jym5VYPj+JDao8D+vUf8VwhWw==", "signatures": [{"sig": "MEUCIFcuvbzbmbJJ3fE34xw6IBXRAHkwp7oNCc5HZoX7edELAiEAjt9D3vqOkou5TjToGPdObsAKmjWbkZ3Og4eqLyb6eqc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10231, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbAqioCRA9TVsSAnZWagAAGhYP/0u2y/A3qWVfqnMDnwbB\nuwry7bd/PFMpB0ZJZy9HH6i/3iFoLSgnDkcrj3aHKV03glDoz/q2u4Ydd/kw\nZLa5giCsoKnL/1bwWxfO4GclKd2/hs9IoGtPxthmV7X+bSUaJAWG3V9B6dUY\nPW2N4ezTfYMhNrbvrsaVxg8n47Xycmd7djnFlnlnWt/2WPxylT1BHwOvF5Sy\nFyDrHA+vmf9UBIYfn7Fu7hakjEHg9rl9a5+O4bkHlmqiqT32VKGl5fLFInj/\nIj0qcFQEw8wTJVwuNVDzcUKIi4r6eHjseaLEDQp0Aa+7izUEnR7Fia28CAQP\nauL7aQQn0Z7IEI3a3rwZKADSBmRE8ku09tn93sJSpIPsnBInn/K1aIsCR53T\nGi4hE0n8XwgHfGeCNjF6P4bsinYviLMXC3wcYCaNrzVVd7WUCeew3mfcZuCm\n7fdhc3YdC9PKVHqGQR/GEMVb+s0ROKhAWG7q0M8duxBBFhCHnbnShDzuU89h\ntWb7mgkprwHQG232qGQqcPh48/27+WxiyFg7C+KaA6g2UFb6Px4wZdK1xHAS\nMJPqrABcBVn5L6dpD2ctMhA+SUlmN8r0vLjXz7negIgFKMutBN2IYe2G+jjG\nGoD0+1bZMjngaHUNozsZ2M0NwtiRSHRhgjIuuRetRB1i1CAyczzRiqGSr1bF\nIL5V\r\n=LHe4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.5.3", "@webassemblyjs/wasm-gen": "1.5.3", "@webassemblyjs/helper-buffer": "1.5.3", "@webassemblyjs/helper-wasm-bytecode": "1.5.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.5.3"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.5.3_1526900903690_0.620500811207839", "host": "s3://npm-registry-packages"}}, "1.5.4": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.5.4", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.5.4", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "73652f04fbc7cf3efc60eb9474eb2bddea915d1f", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.5.4.tgz", "fileCount": 5, "integrity": "sha512-ZciJVClbMgNa0yNR/5iKq4r5jlx51h6FvCQAi806SeWMNeqedp8kfoFzbkKrtHFmieVqN9m6APGtccGf1QA78Q==", "signatures": [{"sig": "MEYCIQDNWnne78O3hMqrsSDBBTM98EdK6xh8SRcekoJ7i7p06wIhALoXxPWM9PBpQkGAO+PXpt485kj4SDkLvhwa2YEGZK+Q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10231, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbAsImCRA9TVsSAnZWagAAJqsP/0tmDR5Sp/dwuBT53bup\nu2WcJLTBrq9ZIpglD5S804i49ioxF8mP6C4yNwulk3C/p8Q6p9hzCgHiNu0G\nk7wv8NyBJP3ZddjQKQauL3VrKjTNVkSTA7m6JrmmaR4SDDTNXuBhDxWI6TmD\ngUrP7i+jhSBm0BMZ2V3z8s4USaLIaCQyTc0qXKlO7fsOKhrRXy8XJY0DOTY5\nZQnk/tx8AnlBP5cVAf79RXvFkAUJOvm0LFRroH92Ujhgy+9+pdbmzCNokJ4N\nf6HDqrkSQn+HtkP1MUWQmNQa3VELJC12Y9hHrafqsmQSx3/maRPs1sdp1TD/\nXzt5BNI+32TtnAvcgEgwY0elqi2zFwNKkYZN36rkk0dDxzYchZ0uxFmtfuM1\nuDV+608j70x4e694ayfYOwh5L0Bmnj6rn7mVFjmcb2BJz3YUxSGzmxJ6klTM\nXQqQm1KQyZJAWnqaq8qlyCwlHr5Ct8nlvAM6fIvdiLPivum10nzwnP0hww4p\nQjJ34vrSNtisN2h4DHs9EvyGVyXjutR5g6ElTJiAhkxUIzIAIfEwmTy8KAZB\nFRM35DihDm27Gz/rsZ1vlPiyCf0UInfimzBoOVVtvYv7rbMQwEIzBbLAsZOv\n3AbcIhNzTsVWvpbEWf0leytINywgJwG+vKVjxlPdVRINSTSU8v+QgvHK/etT\nFJqB\r\n=kWyb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.5.4", "@webassemblyjs/wasm-gen": "1.5.4", "@webassemblyjs/helper-buffer": "1.5.4", "@webassemblyjs/helper-wasm-bytecode": "1.5.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.5.4"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.5.4_1526907430586_0.00437119964953836", "host": "s3://npm-registry-packages"}}, "1.5.5": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.5.5", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.5.5", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "1a182f43ce21813eb82a589a06e5309305181a11", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.5.5.tgz", "fileCount": 5, "integrity": "sha512-ATDB3n2q+dCDBOVEqzO+vD1YeGNZ/n+Q+3Me/ck59QrqhGLdqYuOK1NvXHeOvrj+IF2W3e53ww10Ov47p9PrIA==", "signatures": [{"sig": "MEYCIQDihUpd0hlyXhcGsd3dRYrdqAyRTrsQkr2WCmwBqbXBgwIhAIxS+iDg197FnxqPi6woQty9EDycxxucNkzs7+wlDAXB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10231, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBnGACRA9TVsSAnZWagAAv/UP/iKsBsb5Oo0nbiMKdFrs\nrh9BhWAix88uLOP4kBvGltwXP+GF8qFbsGoiOF0F+kb9lKWKqjLQqQW6Z7gw\nJtO1SEzfkMRVZwKEjQA/A1KVC1OLJjD57nmD/baP/1JHt99U4WJM0I8xw32r\noH+Sttd7Au3yo+w0fhLH9fynBcjPq9ti4szUXL6mXA1oNUWKd2v9kQ1oJGVG\nc84ey1XN6WD9gSRoZClkx1VYqycagK5ceN1sPZj+eAlcm1D3jdENhWXWC5UX\nYMHHwz8+sRZSFJX4khEO3w3MPNOGZHW0WOX3JtM+rZ7xEkEdKIj125zF2M84\nanmUXplP1tHqxAKDNAtAIfCeTlvilhZEbcVeon0lN9L3Uepcu+lPqckk9gGq\ngoRU0e0VUepCAMLHLNVqjBg+ZMqBS1fwthk2IACoYbBvKwxYOVKer4hq3tTa\nEQ7TNybil7mC4I4aK4Ptn6BQot4IritZTLaXL2eqhsOt029rsEB2rZuH4CF/\nc+YCRtl0qVcEU1VuF6OEwweihX/gi0C3DH3IOZ5weRrsA5wMrcnBLE3dUDx2\noYj/bzsbrKiog5bmf23fSriHtR1/DDipLrnx51Ydeyr765bS1qMHnsFZM7Pd\nyT+wfmLYyGcw2GOoet06XbCSyZy024J3wOLpoWMVSNYvOmihnfVmTC4Enl3X\nc7QK\r\n=SHyT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.5.5", "@webassemblyjs/wasm-gen": "1.5.5", "@webassemblyjs/helper-buffer": "1.5.5", "@webassemblyjs/helper-wasm-bytecode": "1.5.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.5.5"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.5.5_1527148927130_0.115402945107419", "host": "s3://npm-registry-packages"}}, "1.5.6": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.5.6", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.5.6", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "a61e287fa845fc8aa80dfa869919da04da91db82", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.5.6.tgz", "fileCount": 5, "integrity": "sha512-76zOYVb1cByKNN9T5rzxMNdRlSGNu1y2AFOeg2Yp8eIBKD4hqIHSpPnONIBzPOzH5CNnigE9RALeqc3+fUXjLg==", "signatures": [{"sig": "MEYCIQCJ6D/5ymTwuSV+CF3Q1UkrnGK4TUFLy1Rrm4OZvKDU0QIhAP0z6JnTm/uUhEhvhZkVXmfJ88GEPvc8WTZj18hfMRZu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10231, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBsp4CRA9TVsSAnZWagAAfckP/A3WyN23uGi6cUUW7bvK\nq5w2R2SxJJX7PXuHQ62jVHxsrSTY4tzD6ureoQ77NKPgMVdwMmkbSNm86mJf\nDJhDH3FMy+XvVwEt1tuVbCvXan+Z4U/rBq9XwqcAbPXFm1AaBHf9D0ZWdfFV\noLGuJZtf4X6dD+8Ba0Bee91fu3XTqhWXXlcapDxVIZIdvOnYAAntddvkvK1M\nFD2J2VdZbSrl1roLEauqV/rxEQibQ+Bd0/3nUziRDQgHTVtzxeyrNcBIkpxt\n7/NerEXj0w5ZqqZz247yhacMByWamOuOll1k8ksxS2U6zfkMt1zq2C6t+kkH\nKzcRXbBdsYGMCmFIkTLXENm/ws+4/M+yjVD0I5wF0NC2lTi9vyPfZ6vl6GSN\n9zb4iuxlmeNOlfZWV5QkkCD5k4IRVrzSreE0cFA33ik8eBIN0sXrXiqC11Ac\nTMHPvOEJvIoM+fnGmU5hhjXh7EJZwn3WP+fDvLtWzOylGwxwvseHQ0hWoXFg\nHHybkvH7RRGY6CDhfUoJ9QJzbmlpFLxqjZfuqwPJrdunbwWBChQdigEMDNAI\nyDstnqPqC5cEWUZAfcl0lICAFCPC6CgTtLDFcNPF6cGmGZaQr9HYAG85GQyJ\nWLpul54Hg/KNYhJi8uskLEVIz6TUMXU4uesYaOkEXBDKQJEqWVfvruMtVQIc\nTy1E\r\n=eA7Y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.5.6", "@webassemblyjs/wasm-gen": "1.5.6", "@webassemblyjs/helper-buffer": "1.5.6", "@webassemblyjs/helper-wasm-bytecode": "1.5.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.5.6"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.5.6_1527171704188_0.09311197737941779", "host": "s3://npm-registry-packages"}}, "1.5.7": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.5.7", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.5.7", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "8ae8c004b99dc80afcf46a46135c53bc23ec6d71", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.5.7.tgz", "fileCount": 5, "integrity": "sha512-pER3pmcwx9yzQKzeBi59k9lN5W+iU6oQ36yrM6Jv6FlND1PtgHhlfBsIOfwhQ73inPPVXXt+SN3qRWmdGh4jrw==", "signatures": [{"sig": "MEUCIGbpJA82542EA+a+nI6O4F4IKvMdZ9Sofx2Cnv9DmAh4AiEA31po2mKJpB9C42o07LxR+F21x4YrayVJupNdJPSkvZ8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10231, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCAeLCRA9TVsSAnZWagAAAHAP/3dyU/qcHgTlaA/ysKoj\nBT45m2E8vgaWXkXVxfK0UxjcOkdLUjPdk3lkpCFxflDYnT313oxv/8Izrx6j\nNzDnIvk7w4iiaa3dEa/KCcOdoRYID0wlrUxAPs8UD8YE5giDn0Bc29cgiN+h\nS52KjtmCgWunJU3HHdqBiOw5DfiElDaWp5H+Hkur1OWP2pyD94m274lYQQ/H\nrS2XyByHHuAdweW9X+q4v1VxPromtFK0LJDPysbTnZplIUaE/SP9fzWjNwPa\nGMxah5m6v7n5Jvle+YB7YGUEOEkAub515x542xQIcHJtW8ekH2CNTCtbbncX\nxY67QGk1gJJ6s8Tod9VlcDA9qPfyU5OMGRryxr9XDA5W+jb38ZxKIFSyY3Uu\nG32il2Mp0+B83aGh3Jg3Qh3Ig/lMiMr8gpD7N2nLe/SDlBZ8DIB5v+KbvRk/\nRrkq2eOkAJZEMZTB9HY6NFCu7ylr8V+ZYKYUReWbOigBsVJDTpflmabJoiDX\n4u0Q1ps2tnMv2XtHdzVSwt55ENWIsfUsh0A11+0WkGl5jzZk/bWqyDo+l74P\nnA2Ev2fq2jROCygHaP3Xx5+Aq9nu+aQfPMOlToE6pjuUSYP8P8azvbT+umT5\nc05/gpvI2KUgP51WpP6zBVI6yZaaREuAp4r9PQGgwud5IbVLk6+HvH5W+EBw\nkwFn\r\n=bAJq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.5.7", "@webassemblyjs/wasm-gen": "1.5.7", "@webassemblyjs/helper-buffer": "1.5.7", "@webassemblyjs/helper-wasm-bytecode": "1.5.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.5.7"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.5.7_1527252875025_0.7632211671991631", "host": "s3://npm-registry-packages"}}, "1.5.8": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.5.8", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.5.8", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "cda7fdb6f3b7b0d215c8f92b7435d47726822f49", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.5.8.tgz", "fileCount": 5, "integrity": "sha512-g7W0h1E8Cg+oJB5yZ6//XUYC+YLCUN0emNASkcF2mFli4AOB7+3siS+rR1S/8N/91zanaO2q+YAs5hdFtArS1w==", "signatures": [{"sig": "MEQCIHBN4H7OzowGMEN40vMnIhSnvfOUflBzIVpnCC0KGRpaAiA3ezm4j+fHVgrBV1IpZ6b6X06oCVab77bNUS53M+IYjQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10231, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbC/rICRA9TVsSAnZWagAAYEEP/j0WRc0JRSeRNY52vfCu\nYWMVvOI0pU0c5mFzv92Zr6t9yup792eTe2rOKShFJaV5d4vtsxHu2GpCSPSB\numEsIWNf4pioam13phXBitXu77zk9GQZ2H27GveIcguVqyb6gJ+s+F2KbIfJ\nXZsdfwuyRABFkKhLYBjMdrhCRnIpJ0EK/kXF4oHVAuoff54hcutCDLESXK3Z\nMoshzYOZubKY3PBOK7w1rGD2i+IrEL0NQmClK3rd9ZVY4mBId7GkF6adqIuy\nBggl5ZccxvEqMigsmVv+OjxG6PMGGg/6iZq7xMLORAx6d0RvLo1DMYFaiGJB\naA3K+hBfuLdZ5hrY5zs6aR4MMBgiJi60AMsAAJLjLQOu1efmcBDM/fRoqxLG\nQTB9TReAw55xHM34ibPsDJMK1I2/G9X0KVPDiWEXIDaZezqadFytf6y/JC5y\nDHmz9N0ttKU2yOtFm5K1XdNUvvn0T0GfyjBU9XlH60jrNOFGG+MnSBBQQ0vO\n0BRCXU1AFS3j1gEkj2atdR6USYUSvuwOAq4QLlYtymJP6ukVV+8gBdZMBV7O\ndeRCh1/3Q6tIp/K3AGpXvAS2IpfBR8eI6H5bdmtjCVLPMRQqxleRRwFdnR2p\n9WQVLnLG4Crwe9FbBitDGqaqz/n2mByScPw7vgMu998olZGHHMIE348KpTSg\nO1YO\r\n=ig29\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.5.8", "@webassemblyjs/wasm-gen": "1.5.8", "@webassemblyjs/helper-buffer": "1.5.8", "@webassemblyjs/helper-wasm-bytecode": "1.5.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.5.8"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.5.8_1527511751690_0.5729678638147477", "host": "s3://npm-registry-packages"}}, "1.5.9": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.5.9", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.5.9", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "aec9486ab5d56e3cb5252a7ed88777b6792ac624", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.5.9.tgz", "fileCount": 5, "integrity": "sha512-+ff+8Ju6sLCMFNygcDdLRNRsmuD0PHwq77d2mbfWj5YzUvFaKN2q2kRppJSEAixOnM2xLADuG5y/blpMo5G90A==", "signatures": [{"sig": "MEQCIDw4aFto0htfiIpLmUe2kU2DXEEWJ+FZZtNDemtIJp0SAiBt6N7Zznzu+5ekD3I8BZO9fpLlQXnsDzEzoCMmmkTt8g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10231, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbDVIBCRA9TVsSAnZWagAAoAoQAJk1TgO8MW317fwlAtib\nhJEOBUinjaxgwjkmA7oQhYk9ZeIEzeCe/JsAZz9xM2sd/Kq+85l8ARyztvjZ\nC6j1Orns3hE/ERfFSIMkVsu5CJqMGw0D7slL0LP0Dmn5QfTunUZvLlDxDtlz\nb8cwieLaycY4a5GfOISpLkT6QCQrvcWVS+OKclSu5mNbGhjCobcW7fRW26Rb\n6NcPL0YNV6SHt1AFQySPyjrZyTGP1M5NCcXWxRlr27fDfGsck61txOIY6mce\nqiqjU3OPpPkR+Cr4rYD6uPO8+Us5qCESWrGBCjQg2YyYt5E8yGCcvqoonxgY\nms1Aj4InYosuUaT61tbsys+C3Cv5OlxY2OC9zOlkwY+JIIWlPJZrIr6ZbtSe\nFy6LGROksFyQIzf6A23+WMEbbcamY9be0WXTT/NY/rQAEwwpBvUBCGWA2vBK\nOtXkttCkhxWTk9gmUuy69JUlW2FhxGnyvwX2j0U5BiBh8Akmary5JHht3Dai\nYRbwlExKQM/CfDbWcMyIxKF1+CxzcjRLje6XbghusViOybI+bHE/OO3yMfOW\npbcKNb21H/T8CSlzaJfxW/dT4SyCui0KAmcpPB7ZaLeDKRJCtElFUUCZ2aXs\nvck9grI+3/fPo+lHhNDPvMU+vMPfeLatDIV0kyHZ9ewvGxxkedkCjqG20mF7\nalEt\r\n=6OhZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.5.9", "@webassemblyjs/wasm-gen": "1.5.9", "@webassemblyjs/helper-buffer": "1.5.9", "@webassemblyjs/helper-wasm-bytecode": "1.5.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.5.9"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.5.9_1527599616655_0.4324573904286406", "host": "s3://npm-registry-packages"}}, "1.5.10": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.5.10", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.5.10", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "d64292a19f7f357c49719461065efdf7ec975d66", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.5.10.tgz", "fileCount": 5, "integrity": "sha512-rXH6br9w+CYY/tN+N7MFmnUD5J/D4sBsl1K8liqKGpAXlsGp9SmEeqXy8yBWJ1wH3J3rNGaxQNbk9VR3qZgn0w==", "signatures": [{"sig": "MEUCIQDDx1fQd0cWfqtlQN5vO99mWQIllptRnRNG20LnNNsr1gIgXtj0zbJ9hSG77/KNNGIXYuySUIzYOyNOGiSGa+moTj4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10237, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbEUhPCRA9TVsSAnZWagAAFmwP/RmYbqG8qP8bu3hug65T\nvJxkham65Dtc4PfRZKeFAFZMTSg76bxdR0bMw7yd5EJIgFAQyxuyVtinbHn+\nGtoqjE1noWPmgjkIFFT7A/1F7Ekd88rmBj2WtLbqQtwQu5D4EoFCG+KcBsBN\nPm/zRBbCU6kOGPbo2ygdKuw1S1XR7AJ1J0xmq2DjXJ8tPCpNlXIx6G/81qAF\nRRy4p8wEN6kGs86gc4tvBWgJGNZs6r2wgC5nAoVQVydYOOoFkVOhHcDDtidB\nkbZSSwthOhpKcYV8ROtEgDvaoeZifhw7WlrzztTJaONKdYEIuLk1HE3i9gk2\nQyIi+r5X7M96/c/az8xaaZ89CZwZWT3yvdNaeUz54w5LOQsbSSOWXfU0S3uS\ngp3AqxGSA4Qs6Qchg3mPaPYZnOwHfsoL7Iyk4wWr9GEEdUKzdhzWEYPLuyrN\n4Hp43rpLFGxgryWcR3de7BMN1fwDCLMGACy642P3Ny0IoyB0uDQabjndA3WM\nx85PHMk1saUHpWyaet95QKBmJDVfe9U66zRhZxT0aXzh0aNvTcw4zdmX5xUW\nfR3Tqo2qdgxptGgTzMJGUzkt3CbzIhZK4j7Oi8r4EW7eIm/sHQExrBJUgL42\ngCglwdmMVvDmL1bzaYPRH1+kXaFUM1xVLeIICi5d/YE2RAspP4OBem7qbvu9\n9RQB\r\n=/hDM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.5.10", "@webassemblyjs/wasm-gen": "1.5.10", "@webassemblyjs/helper-buffer": "1.5.10", "@webassemblyjs/helper-wasm-bytecode": "1.5.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.5.10"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.5.10_1527859278847_0.7106101004944818", "host": "s3://npm-registry-packages"}}, "1.5.11": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.5.11", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.5.11", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "90b5072407a2d56774dc8d450866746fe6a578a5", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.5.11.tgz", "fileCount": 5, "integrity": "sha512-ad+Hs6Pq2EADEfFlhary5nTaRKC2+XOXP6suALMWEYwcQld8AzHmCUl+42b0okVhnkwgt1uqJ7usmIhiff3PoQ==", "signatures": [{"sig": "MEQCIHQsSqtpXlGrjGtahgS37DFEvwNJEq5sGT9IsgZncQn8AiBOD4PDjn+Ds1hcnmfxHyY/AQuOVT/Wms+6ikVaZP9cXQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10237, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbF6I9CRA9TVsSAnZWagAA14MQAImCdLhKI3Xrf8YIguL8\nuOVaiKwzHpvNDyfw4lYPZlAF63HHyurrcr3jPPd1R46+Pn8mL28KXdHs8TCv\n9evOv+oZztAiKLskdjni+VZBba4f5wwz3JlSjDFBuNpdgMLiLhgrarTOgyOU\nbUkjzEuKyvoWtuL4V2t906UOxVxiRYIjvb8IuplMdB1Ak5l0QjtaRYQsqkXg\nVW2zAxdSEEr79uH7MYsvQXGf6gnF3kMYvkfkYwWwSxTGKBPXh/ocoe2zUabN\nHWd2+pXIRKE4K+dLIBWB+vUKOlON0HxqyVlpzprYcaTsgQMmiDwzYNrpkBzz\nzsQpFXAKqN42BCUIo3FY76ZNSwjLIZ1PL58zeXaD7U9SRz7icPdFZ9UQ+YLm\ntC+v8hGn1/AKME+IK0gce13gFTURxwAH0EJeqIw6Btc4L3Ra1ub/7XS9zZNk\nCLZ+TWweM3sjFA5tbRB26+LKt1EtWpPLkYhScED6LK++ydzIR9OKkGT4CjkC\n6w/1SB2I68JdXxbjnzBqCPgO4RR5GylyiigHpypmQSTmnYT5EfaEfJbIoncJ\nJEeYfFf6FDyt7iGSdbeU3th2jhHJrjDub+MkVz9lA/zObLpdR8XqrJMU9e4e\nGlPOkYj/DxeMHAfIlHAChGM+DD2Fasuu87cLHjlmEw27kwTsVKJEcSAuGjdt\nC64D\r\n=OVbf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.5.11", "@webassemblyjs/wasm-gen": "1.5.11", "@webassemblyjs/helper-buffer": "1.5.11", "@webassemblyjs/helper-wasm-bytecode": "1.5.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.5.11"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.5.11_1528275516903_0.7556916866613028", "host": "s3://npm-registry-packages"}}, "1.5.12": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.5.12", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.5.12", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "ff9fe1507d368ad437e7969d25e8c1693dac1884", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.5.12.tgz", "fileCount": 5, "integrity": "sha512-ge/CKVKBGpiJhFN9PIOQ7sPtGYJhxm/mW1Y3SpG1L6XBunfRz0YnLjW3TmhcOEFozIVyODPS1HZ9f7VR3GBGow==", "signatures": [{"sig": "MEYCIQD0zE9ENZIPDaZaTtxdKerqxfb95LRnS4fICS2HiTSdLQIhANqrC2NMOcpvWGtLWc8ETKDpt/nlGGyQugG1+xKCDm3x", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10237, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbGPoeCRA9TVsSAnZWagAAdP4P/A7ChX918shsO7o00gZl\nhxUa55uQ14o0jl68rR2Iie3DXIm1eHlgpOLXbO742toJU2oA1+YnrImdfINv\nzC+9VWSqxTvoUNjVP/VdUO5pSYsErCrkRtEXOwNwnuFjeBTYrlwGA2nDK85A\nUS7wEDG72LRDsxA/dCDrOlUNttSthWcvRVYC0xHfrCn/Y7NSS0Y0C3X/7aDV\nL8EflcAgsHjsTbzRWXRuOq/jpQK+4bF7p5bhvTqleLfpNi7CW2UVzw1Tn2PN\ne37D0vhG+mVnPJXofQnguc8k4F+Qq+XRTA3e0EJ6CoTfshdDbg9W1msQeTt2\nb62wXt7Is8zLQvp6YzGfrZnrGYP7TgCfuO5wpAC+KU0utBb373eRp9RkEX1D\nKx6YkWTS0Wx+0kth2A9a/4fxZgTNV1c07IrYZa7vn3qz5fSg2A9E0EavIEXP\nqmU30oywP+HMf+8jLInzrRpgoUOAWx7RjT4v9owtfI3NyQuvAGHOEEW59rq/\nDDeUvSRwCRf8XZRIKnhGXa0SHo9PrvOcA+h4/R2oka24ZqellnsL1m+NCIAn\nm+BaGOchwWE4HRQVERZN1sVwvYd8tXMMcDyZRpG5cufpb5nDzDSjT61xNyVv\nrzjowtgGxms6WTwzlvAXXD9WFUReH6zXIEJXUW30quJFzCauyzkM0FSBUkFk\nDTUt\r\n=uP1k\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.5.12", "@webassemblyjs/wasm-gen": "1.5.12", "@webassemblyjs/helper-buffer": "1.5.12", "@webassemblyjs/helper-wasm-bytecode": "1.5.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.5.12"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.5.12_1528363550266_0.4616578426715179", "host": "s3://npm-registry-packages"}}, "1.5.13": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.5.13", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.5.13", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "efc76f44a10d3073b584b43c38a179df173d5c7d", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.5.13.tgz", "fileCount": 5, "integrity": "sha512-IJ/goicOZ5TT1axZFSnlAtz4m8KEjYr12BNOANAwGFPKXM4byEDaMNXYowHMG0yKV9a397eU/NlibFaLwr1fbw==", "signatures": [{"sig": "MEUCIQDnnJX4yktzfJeYxik6EfIpGHKq0+52EhVOG8PEDbFfGwIgRDZDk+vV/OAJ19nqmbEcBgECr+/x2nayzCHco29xNdA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10307, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbN4lcCRA9TVsSAnZWagAAZKIP/3pC+nmxzuv5hwr/mE+3\nK8ZOOF1iaxMnfNVhZs16tgyxKhWyGaGxLAIzdnStA6q1QCogG0CTsGVNWYhR\neiNt9KyoxSY0i3fKwJYBXL2J79UOHDGopde0Y6gOFN6mAiYBJAXV2+/Jt7fp\nL/0uU7NupjGn/raJcd9N489iwlTEqwo9D3KfojAEZgbiaTHVSlsGXVfRnfhm\n7QeXNx+dHqYbWFAklPOrn4dlfI3o9otHEs+v8Y98nnsEYyPbD7U1CwWmwFp+\nl+oOeHOeyuhNLxpoAerKBU2IdcnTuHjh8UtQoXrsR9Yj6IsHVvI5/szcUhsg\nkhdgTIo9VBnrywDtVUqixDy6Xp1Y5rrRjf7lM0c/vj4iq7q0AKnfcdAffWCp\nmLR53VtqOwVpQguu/qWTHJiLac54RrgNrS28/6me8+qmwVQRtWusImeS+FR4\ncUoCgpNrb++2S7yIGXsfqlfHyVwuEv56vAVWCRyBauQ9GBo2DhfV6CKiVmRF\nsPqXdZDASew7Ftm6WaoAm/SzXXL4Ew29coaqAs8Pes38WkEPM+DJYtvvDJ8I\ntP1amImVrJJwPzS8xBGV2rLrTI3/UjdzWjhhKcyuTrceAj5clWlJrYEfhn/D\nuUApzwaqQhfkyOBRYxoBNeQCEUsJiWtTe1kX+H9iWAppFvms/NaKXmhJgHR2\nxOwZ\r\n=wU5m\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.5.13", "@webassemblyjs/wasm-gen": "1.5.13", "@webassemblyjs/helper-buffer": "1.5.13", "@webassemblyjs/helper-wasm-bytecode": "1.5.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.5.13"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.5.13_1530366300897_0.991092459337614", "host": "s3://npm-registry-packages"}}, "1.6.0": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.6.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.6.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "89ca6abd16b8dc0456063980374798c3b0dea8a8", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.6.0.tgz", "fileCount": 5, "integrity": "sha512-0j19mzqsPbpBOFgOqHIv+x9mJglNyYyfqHNf2QzCyxbZ8jYTpmegsiqkzpwpafjyvu2I2faHZW8/FsHOOlR7WA==", "signatures": [{"sig": "MEYCIQCbwzix4iJAd5fHdp9Y1TI5COyskK3n1jIBkl3soKFoHAIhAKPZRCTnCYCL3B6h62/6XneCaSeOyo9pFaC1C3ynehg+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10301, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbTF/nCRA9TVsSAnZWagAA1goP/0Oa+ebRo4xm6TVHGRwS\nCV111DjzZLhZY9S03+XNV3HDCIhxrQNqGepd9ewC8rJC5dvTL+AqL099iHe7\n29gdQTLZHBTbh78vndOjLFFagwD4gcaJgYdcx5YTl+4QLrAoITDok6K/5a/X\nKNAItIPjoFqWvneYqOZu9BX8tARdPvcfBd5mKyIoPRrNGuRxx3hA8+8bKQm+\nMS46PDAdzP4P7fWYrJbziOtpMCL2Rql4RqgOgPfUSk4DS5NkRU+pLOdO0FMS\nqmsviVOZLx11Q8hz9IwS6DcuEbimusChlvm5ezYG8m8uNKvv6lnGGvkVNE6H\nN9mof7+diOCnqNn+AyBl7K6YR291s4PwXS48tzeM2ygNZGCEZV7wX3fUY1Fb\n7EgBOARnQQ/VDb/GmnRyXR7xkLBgzHeo9nCP9Jal7FF6laphK3DRbGeQfJNP\nAvSA6pJLjB8m1XPus1I+OB/pNpHT7xkZ9ewXAKgM1+OpdbRO8Rk0C7Iwa8kr\n9r5TuVJHaR86d6n9x/bMNZC5yP+zQpZTg+/Gg4TxDc9XJ0YyRBqqxT00GV0s\n4od+4ag0OYuN401Vz+aIC2XDvBr1j4Tdw4Co5ZzmdKb9dfmeTy0sGf54cT7j\nf3nTAQWlKT8CyrRqw7lfZ0GWaqWeXje2x5UD1kgtJy7DIJu9cwSZhPKq1EiS\nKE00\r\n=nuPi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.6.0", "@webassemblyjs/wasm-gen": "1.6.0", "@webassemblyjs/helper-buffer": "1.6.0", "@webassemblyjs/helper-wasm-bytecode": "1.6.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.6.0_1531731943739_0.5119405158234462", "host": "s3://npm-registry-packages"}}, "1.7.0-0": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.7.0-0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.7.0-0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "8874cc6d3ac40c7be7959f38d9ffca9fee5b2ec1", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.7.0-0.tgz", "fileCount": 9, "integrity": "sha512-h0VROZjl/ihuyVAPCQie11sjCTKkAIohMY0vJdfcf3q1NB5TxdyradzdWknhlPmsmcso/tU0B5cePAac5EYqBA==", "signatures": [{"sig": "MEUCIENIpL4m86xjrc9IjAJkZlQKksqw+h4VjGSebgITGKCiAiEAtQpVittcVZ9IXojltq3lW1ZHb/sIdBffhSuhDTc8o/w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18685, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbTzl9CRA9TVsSAnZWagAAZb8P/iL50DF9CzqpQH47umLR\nNNKcYATVJaDV8uV9nle8G5QagOO/t9dvqAQ/dup+ZXRyLjy/jR1rH8QcTSbA\naiP/rt24phfk36Deg09hTF6rXE9uh403A4EgvFvg6BG1XaqdYz1GX3fhIAZT\n80lH4klKxsFAezySkWHY74CVlw6D729cddFyw5n0v/YgWtuimaqJc52ihrXk\nS1gHVHN+UynrcYvT/yIEG5HxCZe4eAsbaGeUVCuM08CT2DB5Kfc3ZKh2ByoJ\nCt1DWQ3ue+a4DAYNR31fS/VISQa2KUXsMqgqCmRO61sMGuILeZ2aqoQ6mgP5\nBwmi6LE99J+0qWSITglhxJziybG6VXvv6d3Y7UeaDoxSwSNpfMInCIoEcyxa\nKYRej/NpyfqxbGmakZgQM/EmZClOcyhx/t7ErTJMwxclOK19mfFFj6Ib3OSH\n7ZFBw9yxPSOVzZp8U4Aj7sSGLDTRjE0m1RcurLK8lBiGHKOJm3E4e2sU69oY\n6+eGwd2oDtd2UehVyPcwym1codZ4n5TFwfgjbIElf1rM+46/gQVLVQSnojSU\nY5Ge4EEPKXGExUsGbPiyufjQgVCZXYZEoKOP8oI5ILbFo6KlfNcuXCSwPBkD\nwPVFW7ecY84FfGAgvGttu/xWP/xFsvKshb/aPhVxtRZXe6pupRSpCEF5vCej\ndEJu\r\n=UcPn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.7.0-0", "@webassemblyjs/wasm-gen": "1.7.0-0", "@webassemblyjs/helper-buffer": "1.7.0-0", "@webassemblyjs/helper-wasm-bytecode": "1.7.0-0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.7.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.7.0-0_1531918717009_0.07693456951336874", "host": "s3://npm-registry-packages"}}, "1.7.1-0": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.7.1-0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.7.1-0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "edb9f63a12c72d29ba5984cdba312540605254ed", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.7.1-0.tgz", "fileCount": 9, "integrity": "sha512-IHUDFyJr1icoZgKrypc4jUsrGe/ax6POsR5oLXs754aYl4/7XzUOQd2ByNhqr2dG40TxuGWtXQoCTiN5Cfb6/A==", "signatures": [{"sig": "MEUCIDPXzvv44pZC/ENe+viHs4KQHcyRRR6lOzuVVLuwUZmLAiEAgE+9pliBDpqptkLCogiiVtshKhEanp1tzr67Qod5yt4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18522, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbTzweCRA9TVsSAnZWagAAY4oP/3z1uTOQD4JsXLBiHHOT\nFvT+P3aIy0liqVE/DCpzlipFSPpus5LgsOg0vQak/RCCFYSU3IyRHNIimFUz\n2HOwLfUhG5hAbxyRnQDSAi2w1ggilvxjD7LBevA1PUfd2wQgcJiJ9d8FTTC7\nNrKlcACbD6n3jKN7n6CFEC7xM7X6KMp8MSKkychhHRzOc7vM5DGQlUpSGUf2\nGRUHjmXu4OTaCreY3c9/98EnPDJlqEf1miWjmkXiSCpX+YAVjnwdhAq1TLEK\n0smW6su3VlxUtIcaMETj/7MuYV8E6PSIOquPOUS3NFs7yKDDHl+IYoYm2tF6\nkJhEvz4odFncLQiBUyqOjb5NKdfxU98RGqh1lXMhWmbMsBFIluPxa6V4ZA/1\noH8Ouc714KDXlxYQDorvpiMSE7/1ZGnXhLAQuHxy6wV5/hzGDPfqRfHTmgso\nl7LLC/0GPvdX+tiomlz9qE+r0i/JzeXol+Ad0bLA/eOKpUrwmUbQFCHU7oZb\n7ecYWUeO/OVOyOA2i9rBxwcGma3veLkaE6NxzchveJyqMHrnCAeVe/DSrgmX\nvqkT7A0c/j5EwKYlcQ/CRhzrjVN3AYOFiTxhWgugPw0onzZhA69nSHGq6Omh\nmLmFrlhvaEjwMdn3Cm7V9eNhQ/1rnuYiFOMemZtZmJbunI2Fiqj6DptQ0Vm1\nyBRt\r\n=fP0q\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.7.1-0", "@webassemblyjs/wasm-gen": "1.7.1-0", "@webassemblyjs/helper-buffer": "1.7.1-0", "@webassemblyjs/helper-wasm-bytecode": "1.7.1-0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.7.1-0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.7.1-0_1531919390628_0.9975664612815469", "host": "s3://npm-registry-packages"}}, "1.6.1": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.6.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.6.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "566fac6573dc576cb19b9768393005ea6c798fb2", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.6.1.tgz", "fileCount": 5, "integrity": "sha512-dBD/FjV2wYKrSyn0DDd6D3pNXbPGo+MGIoVGIklzYpv8yEOCGvoTjMow2SbCWfZPFoVFBdnh3eHF4NeoL55EYA==", "signatures": [{"sig": "MEUCIAsDcTZR/0VOj96lK1Exx/MdXMedafezF7RkRezNoHPhAiEA4A3GU6MNoV5sNzs6z17FQqnzIkC2nmvVpJueygR4slA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10301, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbT0XQCRA9TVsSAnZWagAAdvIQAIc0Cb+E2Qa6KYGEjGvz\n7aRZ5xX4mbcxeR5JWPozNxAe6Fwnm0EN/yE8JwHGMAqP1rjRexFvq7OBUFbi\nwkh1yfNLNUu+0SR/S3z9cRLjevPN5QyxYr0bdopAvC8N+/RKkIZDZTFgKTIV\n8iqsU4csUXQim22HFOTAg9oPkW3zrJhI20crrTnw2Vdwv5Wv6MV3+97t0jP3\nXA6s4UsjGCHymX9zvKTkKeMIX5wh3Usc8IPz4FM/vKK4tSa+webTi4Ov0sg+\nCrPS0fXT6heNfg45Vf3SD4FsNYZtzRBeek7zRzBfggF2V3Ty1S/l44Sty5AM\n5Ios8JysIVB4FnO1awD/V+4u0fyAc5sv0dEqkxUvqiCwXK8ZMsm4SQ+bIqBc\n0B4EOyQeDYjce4bO1x3jWeyv3awRdlZZpEzZvxnj8AKTdauoZ9JQH7HGM6Rw\nBzU0vGbmLqW8iW//JJKQeV1K61mi9VvAU7mYG7SwOT+MRT5QTeqPIttSaoUu\nz2Ym4/hUFZxoORpodnCU1wA2gOgtx/kDOIsXI6xyMvynhDdcMQe/a7Ci+v1i\nJRn6qEQnqQPUGQgd6fvwrzvIvmLN/4maMwwD9+JIcDJta+17iLFMsG1bolbk\n0KlPu6pviA5QbK8sy2ZW35PIuHLwukIXFYvqlOqPKe7jp9N1tPhg7DWonrFz\nqW+8\r\n=Ep9L\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.6.1", "@webassemblyjs/wasm-gen": "1.6.1", "@webassemblyjs/helper-buffer": "1.6.1", "@webassemblyjs/helper-wasm-bytecode": "1.6.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.6.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.6.1_1531921872378_0.5062667029624401", "host": "s3://npm-registry-packages"}}, "1.7.0-1": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.7.0-1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.7.0-1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "220b470cfbbd4ef73d7c1ac0b054fc9812eeaf97", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.7.0-1.tgz", "fileCount": 9, "integrity": "sha512-zY8LoXmEKHj7EFGpWgbglQrNB1AGqJVYcVm/gvaLeEAnxxQZvDJEojsdDo0oOuysrD6Af9XJXhKmkQ2e0JjF9g==", "signatures": [{"sig": "MEUCID5hhGpVO0C80K2b9ljhjK7kEO3IQfDvEiNIdBJ1+lguAiEA0tBqi8MauyQfVM+NTgrrAqnOMkzbJpEMydJiIncEAzA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16783, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbT1YkCRA9TVsSAnZWagAAXycP/AobigKs/ytlZs13tpAj\nuvVqEqiV0guymLEfu82jzcEh3hOA1fS9Cu9Pk62uIJSRnAXUTO0w/+Piq1KJ\nCaMhTQt/cjMzsh4WcaceEhRJj2IOdlf5ppDDakXRrZqh750zYPocA18OgLQN\nx8TiNmuFP4MMCA8WTDbYamjiQ/6Qb1l12ivsrt6bVIcJ5yi3wf/M6r7AC6R8\nNd5Ke5gzYRH8+uBCADst3qrTVXfIPmsTx0MahTANRKT0LmYJ6u2YG116qaCG\nitXkFvTHbqQjbjr/Dyz5nxHmCBHSoYDsYqPSFOgzu9bFZj46DridalqvorCz\nlNKhrmtJXIEyeefzkCSuasdEIaJis3/utgM1ilKs3aG2wvf7xdxQZ/+rwiUL\nRbnINNKyuYkt5+P3WKdKuyhDFxGO4BI8LaM/6sSMFIywjRVkAdVpWbl4E5nS\nih9Zhm7V5S1hKlDROEAdjEP58bM0wXoL4JiNwZHBs4Xf08ZzudULSGMSzi2e\nTyIfXbgVpyelwf2ybuXKrf5ku2WzLQC67VT6W2A7Su4QdlJWQahSZObgN5AT\n7URYNIIDrJukbcxd1YtsS2tFcCEI1RDLUoMKmhpjadwEYYZPOkLa6qWVZmV0\nfb9ux/RgYDy5D0ZwPoSGCbjCG6knE7mb4q7WveWW+7ZHUhlxLCvofh2Js00H\nvIxo\r\n=EHY0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.7.0-1", "@webassemblyjs/wasm-gen": "1.7.0-1", "@webassemblyjs/helper-buffer": "1.7.0-1", "@webassemblyjs/helper-wasm-bytecode": "1.7.0-1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.7.0-1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.7.0-1_1531926052056_0.3350482462381612", "host": "s3://npm-registry-packages"}}, "1.7.0-2": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.7.0-2", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.7.0-2", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "95a6a9a8d9e9e7369e86288fd85eadb87139e01b", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.7.0-2.tgz", "fileCount": 9, "integrity": "sha512-y8fDLKrE+gPoZa+Wk0+g8VrrMqwLTum4Pw/SS4jj9kNA4LHgCZVDsuqxqLp0mIQ5CbPp958FYGoKhIO4Juvt/w==", "signatures": [{"sig": "MEQCIGVp9RdKZ+tu+CQrmVG+YQnkydY/I2/3Oct7Fi6S219tAiA0wzRTyNlCyZQPpGfG247nM3JbC0DI7M8//PldnUxQYg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbT1nICRA9TVsSAnZWagAAv/AQAJFW6S3zVmEayup3mPb6\nz55dH+S7AD6Vwa6VuXCOC6r9ZEN5QqJ9IMrUlXD+7fmaFXkBXeuTObdAsDh6\nRQ5/EGsOOtdCzBoT5UP9fmfga3qWpfBORjR92l5CXR/5aEGjUNS0fKx/oVaa\nM9a+HVS5YMtGTpXQ+LZHEhqUa8gfXn6eWoOeHqfaZecpWOZAzNEYxlTmfWid\ngMJe+tq1+pOtgNQgc1j+ghLsowpAd3ogBUW4QDCBMqN6QnOIZCcinb5gMzkw\nSbgyh7dbi7LXvykPsmuBYLE3AcLCjzpXeMkvOZ6W7pzfLW4lqCK7r5yw4rnu\nadhmfrlAqhx4j9ZwHxb6r4O/JzRJCiwQ56Fzjxj0Uu938qfVi+7QIkDCb0jG\n3J60Y3WQPLPjvB8yjRmRLSH1VC21B33tvaV+qefwAKZuObo65IsUz2vjY2jN\n3af+lBZjna+UwqUWsiimS4Y1HZWeYEzDevpwc1vE4o03nISSkcuD0Zki9p1g\n43r0pj72BUa0Zc06HhHqjlEhoQsQONJ9RyGtfxMz82cVXhcPOQpeFrhCYO4t\nPczrhVHU/Pzk7l8SsmXqNmvC0rJsqANGx4iHwCrNZrEp0RkYutlctN2zsB/h\nQnZer/1H7WDyEGdwKKcDI+9SNKbMzHNPC65m1jsz0atTfZeM9ahJpVgSyZiP\n1Ecm\r\n=7CS+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.7.0-2", "@webassemblyjs/wasm-gen": "1.7.0-2", "@webassemblyjs/helper-buffer": "1.7.0-2", "@webassemblyjs/helper-wasm-bytecode": "1.7.0-2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.7.0-2"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.7.0-2_1531926977251_0.8621766597767764", "host": "s3://npm-registry-packages"}}, "1.7.0-3": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.7.0-3", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.7.0-3", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "01cdc102032415c704d492c2fc1685d5a05ed73b", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.7.0-3.tgz", "fileCount": 9, "integrity": "sha512-w7mfFfVksKd5amPLFJ9JR5x7Rf9bxH896gOCh+wArnI7iKAbc3QcRupjUWAEiRGl8XM33FCRtw+kqL81E7MnDg==", "signatures": [{"sig": "MEQCIBiVsxkeqOYYktXctoKxSY4cfjLGlPRgaVnU11W7eaIRAiAl2/i+zK004Wj51qG6iTJUqm09pR80+jKP1UivYUKITQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17576, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbT5FwCRA9TVsSAnZWagAA7s8QAJOnBgdtkbHNQr0LxLet\nQyV7ACBMedTqVBu1Tm8/I+3BfYLI+hOFrPUwGEBGAxMtHcBkhghBKor91/jY\nQQG7ZIHzPDdlwUocyPVPkSS8bIE5f2zkEAWyn9d71/UeUAL/ofh4YCSGo3a+\nGlEzj3jQniYXfXL48HlHArH2Zi6UBciawGSy//8Tzmai/IFSqliWmT/PMSgI\nrIIHcC5BT59OsWLPvWvv+d5+eq1k6XQOBQ030orL+7ufeIlaYHmEl6hKUpAE\nvqLPKOBA4384NBulVWQVn2Yd9yLin6c3kWg+eE7ymhWz2me+oKi8pOEbMpq3\nxqKCJ5qpg855XQxRDjMaE2KPJbocJK6hIs4U+YnaVg+9uex/qEfZAWXJ2JQs\nU0x+M4Wta4Y7qudox/NsU6ThftujLLhxFBRqdWqcDxNyKXmUqt8jwkFLOwYY\nKVFIAThRjGVGaYbLEoTJ7f3s8vfOlMBJZYuhTCitrQDkS9s8cpA3lyJc0o5i\nvXpBI4XcvxaFATsIdNjs2RY2u18UWow521YwqaJJMga3a58yVqpLdf+F6rqQ\n9SlTc0dVu1jY8Qj/BgraE+ME6Ltxn5ovVHAn3218jp7sc2CRfrzsNmBLygGJ\nCY+IxfsxpMCERly8YQl45OkwN7V4x1Ka93Gf4A728t5EBFeZef9FuIo+2+V7\n8Mvd\r\n=zu9z\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.7.0-3", "@webassemblyjs/wasm-gen": "1.7.0-3", "@webassemblyjs/helper-buffer": "1.7.0-3", "@webassemblyjs/helper-wasm-bytecode": "1.7.0-3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.7.0-3"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.7.0-3_1531941232078_0.33674877977553463", "host": "s3://npm-registry-packages"}}, "1.7.0": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.7.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.7.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "87431074918784f4a1b55ec1fbf041c77cd13857", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.7.0.tgz", "fileCount": 9, "integrity": "sha512-Zmq9TWyvwYmz1Bxsh6Mw+MQRL0xeo2NAJgWYwxDCJA7zIFK9NGkGlRoC9GeHFqKrVM4SPNgd/QCvJqz1cxAbag==", "signatures": [{"sig": "MEUCIQDKhr6imtaM5pzW5gj5R8VBQiZ6StRBwgmd6YlwXmgkBgIgc+6bBE8fmDG8L99rAa3WAbvaIODu9p4LbEfsTVR4tLs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17564, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbULXYCRA9TVsSAnZWagAAc30P/1Qj26iyDLw6NFEQfrB8\nR3K6K7Nk6mtVe1KnCM+XP/F2ITRhKj1UpbRw1WK7C1nwxTTy0IXSJBF33y9h\n3JsuisHJtZruFy3jBqBuxbk9guKSbYa2Qs9heBMJWfzshqsjDdSYvxBwZSXO\nwep2DfUU00cFkH2L3WGtw7Ke+s3nRfJaAZVYUEtEkHnFglCXIyV7eMMR/0Ix\nEw42vzyLPhe3vTd2Shh5dHtHL5QojlAkkqQVe8xpJ2t87nSEuaLrcesW0nep\n7gOUzPsrYlFG2FK7NdIrfXE0uiqpACsqIfEbKFD0PRk7hs4ZYjeNiJ92uumS\nM8Yp67NOnXjWsDpciWZXCJizLCKqcysjUH/LiwQc6E1XrJfsZ0QuqYcZi4aH\nQLARJphKnj2kdok09bvyCyLDjkEQMOBus+sCeR1i28DG6UOQoVj7fsFLxDLx\n7i61EXqT0QvA9bUJQiqOk3ZPOwS27rFjfFHPKoF0WqW6VEzR0gByavMeCK77\nFCdbJVkPTqjvCb/+nX1JKL7hly49QlN9tXJyl0nNz8fGUWhTbkF0oAc1MeTC\n0KKUUrQPOB3SRPaR21Dl4xvBC+myg4fDWwkd4Fc25qm6tONSwLgO9poMFLnX\nRy4Ha7cRAmeUS9ViZNn/iz92cDnGuhJMfoiIuiny30t/KumntFTgwkLw7375\n2ocd\r\n=ujoS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.7.0", "@webassemblyjs/wasm-gen": "1.7.0", "@webassemblyjs/helper-buffer": "1.7.0", "@webassemblyjs/helper-wasm-bytecode": "1.7.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.7.0_1532016088885_0.9904899262480085", "host": "s3://npm-registry-packages"}}, "1.7.1": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.7.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.7.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "7f4c9d25c1513706bc6657ef334790e28d4d1f92", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.7.1.tgz", "fileCount": 9, "integrity": "sha512-cDKfrxVsBVqh3kghkODBBksa4tB+DN8cE12op43s9u0UrseIDvmKm2S7upWdq57QK5b/qEtzZcMRZxA2/167Sw==", "signatures": [{"sig": "MEQCIENpZ7NjUXyhjg7InSCGD3dDn+sJjksNudW8iI4zBxhNAiBREAseSiUUkEQulFcYHYicTxAGxU6ZlTa2gMVfdJpK6A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17564, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbUL0LCRA9TVsSAnZWagAAdUsQAJYqiMQ7ovY72AcHaSEM\ndtCT/EXBiZL8dRXuSqTCUfMQwZjsI0dRzvvmLuoudwZMNvFSDmUkOAmHjNtI\nxPykbRq35nv+Je1rk1Ndz8knS8RVst9/4dm4yH5HA4kWuNk7A7dnM7ZXBuGX\n7L1L6cEYIc9TgTNXVzUPVnKJIzbaJpLQhXX0XK0/HwNO7MNLaJnxb+Jop6bQ\no5fQ76cZDylc/yGmmD39KWZ0ZKqDujOHDEYmKXsa6HcYcA+e/oKMRh2LANqE\n5GjPQi5odeDAseu19LrVJZyWbhgTfqTFk7h17YKrwskksc64QjQJfksFdmnh\nNX9rBkVgMK/BZKuP220T9l4ni9Rn1vj9gTfkMsrpcCflsFeB8vHiyp4M6u51\nZQo01jXrglnYjPnOc4L/TnddTKsjh/abJFdbMpURu/BdgbyjO/g6p3P2voMz\ngR6PluXaOG5l7nCOHzCWzxYHdO9ZWCBQ9stWd5AzM/W5ShSh5B3MXSubLrlQ\nL013ZLXKYZ1sXqwznagEBbKU07r1Dc5D8zjF7g/2gp1+RyYhhGP0fAP+nbWM\nQX+9iBaK2Wd0zqJIg3tLbmxqTPFIzAVnCqfMpirdEboM3+Ny3kBfoY20hdSO\nmK/ETxcVyTNZ0gDFvrCcEz6Pv1dTe+e87IdQfnEtCxCyVNAcvtvMJojvzlFR\n8bSG\r\n=wPNC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.7.1", "@webassemblyjs/wasm-gen": "1.7.1", "@webassemblyjs/helper-buffer": "1.7.1", "@webassemblyjs/helper-wasm-bytecode": "1.7.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.7.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.7.1_1532017931859_0.5359319086709025", "host": "s3://npm-registry-packages"}}, "1.7.2-0": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.7.2-0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.7.2-0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "7b2e8070d62567eec094793f78064cb079846d5a", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.7.2-0.tgz", "fileCount": 9, "integrity": "sha512-0fGo2B50c/2G7dAMCn3hVtEhf4BtfLNRgLfaydiggAH1KbYB4p5/QfAw1KYtCDaoD/AsGhWfVUpR5oj63eK3Xg==", "signatures": [{"sig": "MEYCIQD9S5NPfRNbjz3P+vpU/F3PA21+nQcUyRBpxIchcQTXzAIhAKCZc0V6nhKLSnQcHOEWzUsPQij2bo9S8QyOZR80qQZn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17576, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbUNQLCRA9TVsSAnZWagAAc7YP/3ncuhOtfZ8D0PPoqZWC\n1Is2tbaULz0RTDA7/JonvqEDslVB337rw0NR0IfZRb/envFphbuzIdP8DBu/\n+mpYhtBuHaOc52S5QJMA+nN9Inwjg3ApeS0jh0KYCRCmG4GNLCZSr1Ptpm14\nobU7ZIiihdVbduKHtXFMAY1zi1n71bq/3+eFMClAfms3JuGiAvue2fOCQK46\nOdoalCCJm8xEbagdtopAamUvryUCWslM7FWc66iEttCI8ucIcbXDOcWmtXCR\nmiG4PZub4yzH3UzuftCXVyyOANn2xfXQzJeznFTV3m9kAjXB4i5ao7er8FCi\nvnoXqILAOMBGM5JRUBHKMdQoN6BXryfJUHsHnkz/bef3VxF/kCdIvfO4SSBm\ntaP620L1KcK0xHoI4xHO51N5kkm8+KhzA9bPmnOj6qNyE3Z17VH6bqTY9+zT\noGBbYekc63faIEBWXrQ2QDxcbqRLC3ljn5mupXPLL+erXWDLoPdvo5dWXkyl\nS1z2UGMmBtFzQkarN304FbCDZBREs4jgDqt0toTKj0OC/oT+/2QmkflIpnRN\neEHUFrQNjWXtg6SAYY+ium8IEFdByDVx+6BRp2npNAVX2b+F9aDAH5lkwRc2\nI+oId4rM9hPa72qs18prjjL370xUCLhYcyFTemaqrwBix89lC7AMQspi730K\n28m+\r\n=poUH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.7.2-0", "@webassemblyjs/wasm-gen": "1.7.2-0", "@webassemblyjs/helper-buffer": "1.7.2-0", "@webassemblyjs/helper-wasm-bytecode": "1.7.2-0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.7.2-0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.7.2-0_1532023819037_0.5925781626044091", "host": "s3://npm-registry-packages"}}, "1.7.2-1": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.7.2-1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.7.2-1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "0775ca121a5f17e5dc7790e6a8ccaf7642d51794", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.7.2-1.tgz", "fileCount": 9, "integrity": "sha512-ECtTaAw7Hka/dTFx6hxGJM5/w6Xe2N90rIHGRLdjjPi+tXXRhUlHuCMApJoMOPucTDq/95pTGZMSkazUzLgI8w==", "signatures": [{"sig": "MEUCICZ+ixnrlOlsG3Ey0Z0AEfSVuF4pK8ebPbuX6Pmr8w61AiEAqL71dFsvmWV6LmV4Y0oJOFLu1XQ2RFOydQbEc3FJAlY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17576, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbUNePCRA9TVsSAnZWagAA6RAP/R/jbyNXo9j46/cXaL/v\nZ3ccKzjx9FNleB0PKr8fe4BMSs3SEsZlJ7w3XmYBlCu0qhrIFFf879Sl8v6y\nc6e37RJtQm7sB9hdoVWfrrRNE+zBS39SaITF/p4X+sJU8SPf90dOfNoF5hLA\nVy/2xxgCs5QD2j9xu1QcwSYDsj/K54KaxfWky0+05fz3NuMA5bIQtKcUnCir\ng+0G4jqVIgVPJPLMVtsOpq5cKOkN1imXqK7NVw5Xy8UdorehA21JmdEhKAHN\nv8ROwcLDCWfp6+a3vACzPwVo5v7DHILOp+tsLLvr+Mm/LfKB3ywXavBsOEDj\nKCzIZzVyu9CRWnn2F0QOm/19NcgeEJe+aj1lueXBUPBpdQDtNSCGTcjOg67W\nmwBnAvZyNTTXgY/VfW8qqMK0YhqXCXXaUFxhqCNmZWxZ0VpkEK7EXwuWzRrS\nLoUtMVaACc0RVdq3jC96nQfs++TTfsqqM+iejt9Tqg6X/lvP8zqp2s7jZrBw\nETxAg8r44TJsHFlYCIo935JFO3t3Bloao/I7RMpR6ytulSOi9UEhLcJ16ll/\ncCIMzP6x78MIYNMMRxwWD+Gj3/Q7xnI4vAybvLL+1tQVVE131k9TohDjV4r7\nx7Ic0B1JDSM16JVHiL76fv0LzlH4ZC1rt0RZ1KGaZFtLXNPP9nMAPQzbA8QP\nPF1G\r\n=DVMc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.7.2-1", "@webassemblyjs/wasm-gen": "1.7.2-1", "@webassemblyjs/helper-buffer": "1.7.2-1", "@webassemblyjs/helper-wasm-bytecode": "1.7.2-1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.7.2-1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.7.2-1_1532024719117_0.24814310788277227", "host": "s3://npm-registry-packages"}}, "1.7.2": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.7.2", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.7.2", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "915c1871df6d132a683fd31052b05ae85af6c28c", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.7.2.tgz", "fileCount": 9, "integrity": "sha512-50rBQoJvx/iwzE0X+cLojS3BS9dHpXpDTIl4YAMo3Qn2cKp0OFKvM+VuETyFTqLsk551HMHT32B/1MVQbtJkNg==", "signatures": [{"sig": "MEUCIQCIxwiWvZjtdZOZXde0DLIrYyUm/70SSD7b9dygcD37TQIgYN5+meYGGCMfke39aOJs9BS1R5AcMsv8JPxubNPm1/g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17564, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbUNmbCRA9TVsSAnZWagAAbDEP/i5j+RZGK8as7+WZCghE\nmbRc//MBtfiDDAOBk02O5lQ3aZVEBfxNnmxj6KNoHvXcElgiCmre3E7VUNId\nt8g0zqOYnkQzNBuGfQ2nzYE/sfy4Pd5XMhJjadPILzBlLSSbdllpeecuk/af\nfGYjwSXxvhTzEPyAZQB1h7d0nEzXPS+R7E8YOsh7SP7wSvNw1c6rTYg/unJU\nTqW+VmsSY3uwt+IasUjoFMBFKVI3mtmqC0F7pVpArs4QkU0sj0EbCTCJTe6X\npQI6vQGfI+x0JTyukW4xyn9Cg8np805yR9AwQ5bVnLIad15rX+2gCeW/G9SZ\nyBR7cobVujKxDqpLLp+mQQL/slUMw8PDoa1PCJolST6REKBx7I4Q9F/tG9Mk\nQgZzflcFFDet97YISPhVelGsvt153ulvJbUERt+p0GqFSge9v5wzJzKZ8/+3\nPQF2ZZ7dNVnr8IbvMptgYJLJjVY+WvQyiJgfNcihHEKwKngdT1JFLkIvQncu\nKgTY/BXo5qQrZciLhniZnRBASyaS6kV3KXZjovREpo4d+qB/ZMUFYuZsbX8j\nXXo9QFICLHZEv3LSGHCkqcD6b2nC2BXTTBoAsDnMxzRRa7Bigd2Kb80lyGgo\n1DWbQAJEVTtrQJKfZZfnXd1gpOhenKXTKUA4IkckMIM2JNAi/x3EX2L+8Tfj\nxu7L\r\n=6mcI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.7.2", "@webassemblyjs/wasm-gen": "1.7.2", "@webassemblyjs/helper-buffer": "1.7.2", "@webassemblyjs/helper-wasm-bytecode": "1.7.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.7.2"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.7.2_1532025243519_0.8904632723290304", "host": "s3://npm-registry-packages"}}, "1.7.3": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.7.3", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.7.3", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "f663c1e2c9143cccef3c01f56bd7310ff2010ea2", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.7.3.tgz", "fileCount": 9, "integrity": "sha512-GOFCrQtIou/p0tChqbZ240L8qY+6t8m0GgUI5pDJUL9UIUDl8VS+qfH9nb9GWJc1GhRJblXq6Y8m/xf2bdi3Xw==", "signatures": [{"sig": "MEUCIQD4IG+NcR1kSKHrj8sCXAty+LPXMrHzwDDHtmJrgnJ9eQIgTZ3mPe+yoULzz8uQwJvp4z3cn8vQwGrQIuEZygApl7Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17424, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbVX2cCRA9TVsSAnZWagAADd0P/ikVGEX/AjYnCWMI9PcV\n0n/chHv+OJt8XZVhKNEAeLEdLqVsqIOaAmabKTrx23h6W4apV9FzxE1T4cdS\nV44JBHs940x9zz/goylqkAyY629CiD296qTOcvP7IUqm1A7Rwz7FdxqZSaWt\nFwH0T+2nhnpyrQlfhIvd/mGg+4Wa3lDny4SivwgPv+fp51et/vQy7rdmq+pK\ndF2s+Inw8StKjAFMgaBz5j0FQUrdWgO8YmFCYyjgGCp8XKfAVhrVIeqNBPti\nnUgu/atTWJgx2FN6wvam5vO/E7SB1aMxt5eSVypZqozMwOSubOGA5IX3jx1+\nvzMj/848xEG/ESmD9PiW4urjx0A0OwEUgKLm2ebCngD9NzfxAWYDgw7cm2Dy\nyTcZuihSe3q06kPzpVdd01NuhzT5HGjI7NpeFpa3s2V/YTy/+ISo4lNCatET\n7F8YVKziC2qWTk4pfIZRyOklu0tvhEhFrz+Nv9ezGePoTcvtHMBC7jkcArqj\nUB6ngWC2w6mtBo2pRyR9z1EIDjDeOQabjNVr5qQfZnDHu9g69FOzu+PsQoFO\nW2dFy5j1p29SlQpxnT3GJ4f8SJV11beGfZnwDtjEMrKBOCV1DaxOcqaI/sIB\nw03fNPaWIjivoxYWTIgeh3t7S2dd8FTfhC1vyRrfn157K4vJ2bWnyx3o5wN6\nnmMu\r\n=vqPI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.7.3", "@webassemblyjs/wasm-gen": "1.7.3", "@webassemblyjs/helper-buffer": "1.7.3", "@webassemblyjs/helper-wasm-bytecode": "1.7.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.7.3"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.7.3_1532329372524_0.33935249800830936", "host": "s3://npm-registry-packages"}}, "1.7.4": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.7.4", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.7.4", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "7e7bf997234fe3dcd2a10639ae226376b94aa39a", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.7.4.tgz", "fileCount": 9, "integrity": "sha512-+8VEjNJgkZrs1AZiY+NtBDxavn3Q5CSt9y0KEY2/lHiG4OFejkextThu3UPgrWhWvmZcoKSsIKBB5wY35LEo3w==", "signatures": [{"sig": "MEUCIQC1Ba9gEsfU1NoFBiDLOW3u9RCoI4kC/dsonS3IWVqSgAIgBwoLRREuuzjNwc6xtm9w4QuqZfoWPTyYa62gJpbB/r0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17424, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbVs8TCRA9TVsSAnZWagAAH9MP/jBOYQtYuk/5OwO5qg+A\nvRZbYBFaaiwsY8vljrV2az+o4EjVyo/yn0wFFvJ0HHaNu3RuHlO7rzffvKyM\naYwHbBN8zl28gEYuJz9SaA11/82FRyhZXljIjX0GGV6TpBVK4zC9F4DikyEe\nXQNe2PT7S3Cjxd9Zye544FWzl0a9+ev7+28arwo/Q3JoXOpEg+HSQJTlgaTs\nOTW8VABS+nH0A+DJ1v/cpPWUwRgjcj8okuDH+KTc2Q5uOKtC7YwfOBOvE5Nw\nSBO/5nAixiKGx70cTP6kptQvZjuKwW0mdmHaqo4H3u+i1vxOjqKHHoNfXtAh\nTgGhZop3BUzmKKxkv9g3YlXxKDbAYYULMnhu7nplqFL2+GhJ42ECYCIXMdue\nfrjh4L4eM5nvR0rf4oF05ATpzJiB0JIEg5bit6G8DpsqYjRVdBEGM4SU2pVn\nefwsvXG+OWJdHN+CltULpyfLi3z+jH+IgVH99XO9edaRjFGpH5yZLzD6w7qc\nc/eOn42spwnhlPU0GhyYLButTk3uv35LPJYqxXMEIsNfjc1lPn595MV90oe9\nMyES4Kx0CIK+PBu5b+AJKWgcfryuDa5x1QWTNA2sEGVAnz3dS7hNy4y+QSq3\nBLUa7mfGy1CH6pr+B7hHkLPvhPY92t6a+FPHseV2E5fEOJt7xf/SbalmD28O\nEWro\r\n=e4Tj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.7.4", "@webassemblyjs/wasm-gen": "1.7.4", "@webassemblyjs/helper-buffer": "1.7.4", "@webassemblyjs/helper-wasm-bytecode": "1.7.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.7.4"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.7.4_1532415761926_0.3773902868708361", "host": "s3://npm-registry-packages"}}, "1.7.5": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.7.5", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.7.5", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "de198dadad44c58d8bdc22439915f0e1e302bf1e", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.7.5.tgz", "fileCount": 9, "integrity": "sha512-MIJULTUXCHfkl7DZMu+0qvlKVOuhE7jFeOrIOuwyMP/jLdcg+Ylyt8EgmGE0bcjH36PwkNL/paduTTsZotakXg==", "signatures": [{"sig": "MEUCIBJqWANu0HLPWlPwSHUurpjaAaptI+cAz87MEjIC4UsRAiEAktePzy4j3iCPyT5AaILJpZKZKPhDs99LsdAAJuOYauA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17424, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbdZAuCRA9TVsSAnZWagAAdOwQAIjUWkbYU19ltV++UR1d\njEi2xs3wxhtnWvWJu+YtGakyQIWQpjtnB3300QiL9PRsOwEH3DfNrngasx8P\nv+4qgcCpgr72dpkiQpu/Jg/aukzkigW/ErBeGCUomEvxaK4FfpHS3iW9BnhX\n2WITIWmxBl+k4Fu1Kbrk9rLPXOnJwIbCF7OspPQG+9IbAcqPLm43YRqYqgDq\nfCoAMA0uHTqVX4epOZ9Cp0G+yybwU8uq2CmStYsJwDAiUJ4tqlcG52NlyfQl\n2HcQIA48u/v100EH4jMsYZlSIGF+qi72daymOoFJo1/k6jlVwUOhhGRtNurr\ng1PQjCYp0a/2Ek8A5HA3VaR0gUAjPshDRrPDzl1uvKpHrXtYa9WIBa42FLIl\nKUlA926eaSEfWE3gxlnG8++Y0wOTexuCoku2mNTJ0y2EMqhn1ilE+kkKkxBI\nJb5NNID+qdH2BeGQUpBj5u+BbMxm73BcAZV2UzX+dWP3es5zAkkm8jbA5OzV\nkiSmcgTRa/qUkZSoYcaycSJU7hWLkJjYr3MuffwtfRAySF8j1uiNo+Uuvvaw\nH3YS/+rngh5YrdE5tY4kcL4gqUlXGqxd4zIkz+M/nzC9ilvTmZVN2+lMttjy\nIDGUNe/Xl5VQg8EWlG9B98j05U7Vw+ZWLE3moOyiZomVx++qCTgaSttjrYF4\nUGJ7\r\n=QAts\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.7.5", "@webassemblyjs/wasm-gen": "1.7.5", "@webassemblyjs/helper-buffer": "1.7.5", "@webassemblyjs/helper-wasm-bytecode": "1.7.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.7.5"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.7.5_1534431278076_0.4931602833016777", "host": "s3://npm-registry-packages"}}, "1.7.6": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.7.6", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.7.6", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "783835867bdd686df7a95377ab64f51a275e8333", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.7.6.tgz", "fileCount": 9, "integrity": "sha512-3GS628ppDPSuwcYlQ7cDCGr4W2n9c4hLzvnRKeuz+lGsJSmc/ADVoYpm1ts2vlB1tGHkjtQMni+yu8mHoMlKlA==", "signatures": [{"sig": "MEQCIArv1cUtFgnt0f+wrQNaQDmCsScYfWtDQatMNHSp2HtUAiBPKVzFEibbA9v4MYiu+HEx5/Mp2lMdw0Xc1F/QCTtsyg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17710, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbln0QCRA9TVsSAnZWagAA9rAP/1sbRbX7WPSiywd94a6Q\nqb2hpcHm+ZH6G30Dd+2XAfS7ROvZ8JlEQ0ZooC64+FE8+xCvDbGRdrYPKKjo\ngWQchY7g1ttAxFaQbK+6taiTdhCX9Idu9QcCaMuedrBhhqaZWuKYuUbIdtv/\nkCFDRpb1xjJJuRNQ/6ESvIBGnBOgtqIFN/dEnK/tnSkDAac5KYNPvVy7yXdt\n16VhFyK7/FDhtaO3WF6VfQvbh5HaaRoPh7oq00hUGo5N1Kc+cZOsZj0pJiP0\nV9a3947qEbVqSVXFgTB10nexZwIS5ZONLtwYRVx1LZcghvTUKdVNfrnAYMcq\niP1BY4yGsPd11AYEIQHFSbIAcaoo/UfoEhnFxrSTQePtII2k4IacsF/Z/qzT\ndtxXfnko6v04Ju61G2/lCq6FLj+Yq6q086pM6zUyA+s6bvOkkTzuL1OtlqEh\nhUvOlD00a88iGJUIU6zwTVbBWoF7IuqAT1imot92/wfptJQg1qJ8LadePyLq\n7o3MFJ25dCqZM84DcMDU7Z0VzY3GzqcSh2TAiwKSKHPPC0ly5hpsX/ob492D\nQB538WWUeoO2Cx4Je96lTC/AKu6W5ruSFj+e6L4IHieA7dv4GGvDM4rEOY7K\nHLQbgmWdb3Z5MIZcY96dOZP2i/29guoUumJ4SQc9p6QJRn8B23sVU/tF8On5\n/LTW\r\n=/wgy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.7.6", "@webassemblyjs/wasm-gen": "1.7.6", "@webassemblyjs/helper-buffer": "1.7.6", "@webassemblyjs/helper-wasm-bytecode": "1.7.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.7.6"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.7.6_1536589071824_0.999052603828642", "host": "s3://npm-registry-packages"}}, "1.7.7": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.7.7", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.7.7", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "13eabdfef67a76dae199bd8407299e50fa591715", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.7.7.tgz", "fileCount": 9, "integrity": "sha512-LOu9j9Qnq09H/kzqPs9IU8BIF35VI2iDkPeAhvmytTOg/ZgPlUNWY8NtijjTdF9CQfDuxzjt7smL9fUSXkc8HQ==", "signatures": [{"sig": "MEQCIEBZOYIZMX4s0RnC9Kabf0cw8fo0EqJCOxD9o3vAoaK8AiAzgIRBFH6AsH7oOBZ3SyM5fJ0ghNdVOt5EMYnU8wSbtA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17710, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJboeEaCRA9TVsSAnZWagAAx70P/1ugmX+qZ6KO5tkgYZBA\ndyeCQjy4b+oHwsRYV22Yu6/WWIvD9ekyUFQZVodzjHvrQev7ErG+0TpJxZJS\nm64y1vhkNHOy4lIBp/aysTgL6YFQnZPrGFla80i9Gswjf6FNMgLa0XK3dIjk\nHR/az2nU2Q+DHRhWmYfWr43t5C9QwVtkjLSCKWVkQOzLbS00naV9L9MoKDjz\np1yFrJB671o8Zjq0328VCfeAw9KstPsaDhpU7Yy/7edVHk8fZCiq9ieSR/g2\n4nIr+ua/1AmG376WX4VmVqTEMPAiPW19u68s/6iX5R3Njywzz0tC8e+O8K9K\n3tfIoitwqrvNFawtgJmqKWQoWVIaypIHL8B20vZQ/G2ewNqFTwqEGK8mV/Su\n1jbserQevsiFeSmpG5hOiDjHy60OAp7eEA/JH5mvuGiG4omNVKa27PnjUNL6\nwaTQRoq4RnnW7RNWDvnunEmOSwaoWhdsSFJbot638KTRkLhwFHDRR9Xqv9fI\nKc48eghjuCeERrxCJI43j8SR/YHv3iKL3pfiVPSLVRLiUUEmG/i48CSzyELM\np2QLuVWtAtpGDkbHRHAxfFSlPYkOsrLqUF2rNQPk3yCyO4y5u83j5YvjP3FT\nP62vFCqqU+QoWFZbcqgrvqN7eufwoWGWUkrNvlIVBQTTwIdHpnXldiC5asN4\nuDvM\r\n=tTnZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.7.7", "@webassemblyjs/wasm-gen": "1.7.7", "@webassemblyjs/helper-buffer": "1.7.7", "@webassemblyjs/helper-wasm-bytecode": "1.7.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.7.7"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.7.7_1537335577672_0.4360871235116144", "host": "s3://npm-registry-packages"}}, "1.7.8": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.7.8", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.7.8", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "c68ef7d26a6fc12421b2e6e56f9bc810dfb33e87", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.7.8.tgz", "fileCount": 10, "integrity": "sha512-BkBhYQuzyl4hgTGOKo87Vdw6f9nj8HhI7WYpI0MCC5qFa5ahrAPOGgyETVdnRbv+Rjukl9MxxfDmVcVC435lDg==", "signatures": [{"sig": "MEYCIQDMffrZSAOBQ0JPEOcaOZw6RwsYgqK+hmH/3vIhZm/YgQIhANvyPS12AzQQO08kjj0EW54qV85FZ7CJKwhz3kCNvM8t", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18855, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbpJ/oCRA9TVsSAnZWagAAp+YP/jCW881qluk+SNJMFze0\nCxyAclzADp81IktySIXaq61nqSedDLoWws+eu3IKGYp3GblSBPaGiuY6Dqw/\nmJmiyCalQQhkBH40Tjvt+sQYB0bX+qhR87qZgnT0ef3KwOFewyEFzrbc64R+\nygT81+6fgZvo59omY10fdgirYCekWgxzGqSRocQ1m2YPKnUMdjRUvQHyFoGg\nwZ0digGNHtPIfmS+oWhDEsnOGo5q6axEtSHrEtuB2q/zm3VwP9cSjshRZOsF\nO/9FbivmQ/SaqJwW6tgf970qG1YQExp/5KRY6srSfiyPy6uG9QC4nILZPkZU\n9DYjfHTkz/vlZbBpUvfHLOyVEZ8yVL7IcWAgVByjL+9X1Zaob/rxWKt/Bf/l\nGageE4mKliJKq8IQwdqol07fshUUGFntLtDqsw66U1pn5rF1ZYJEKey0A61X\nHnibjzwTL8JbkBinLGIRUHutI2MT2bllRYSyica9rv7q6bqR6j11qNUm2aYs\n+N4XgSJ0zQ08tQ4rri7FArjobchBmOCxwmwuYim2vsb/Cc+dryR4bTFxsJ0g\nxqZpwHRyal6LOSR9CNtIV41+xutahil5rH0tFhiGeRxJ4XZBasD5h1+cBIVL\nguWQFL5x1b6btbwcrqB45/ofM8Lhma0mku7lakC6QJT5V2gfo43maeeMlJ5s\nywen\r\n=4glt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "43b83b600939b19c48c3c27a1733592c493c4386", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "description": "", "directories": {}, "dependencies": {"@webassemblyjs/ast": "1.7.8", "@webassemblyjs/wasm-gen": "1.7.8", "@webassemblyjs/helper-buffer": "1.7.8", "@webassemblyjs/helper-wasm-bytecode": "1.7.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.7.8"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.7.8_1537515495475_0.17683292482126034", "host": "s3://npm-registry-packages"}}, "1.7.9": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.7.9", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.7.9", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "6cba61e056b32676cdba565f93f6245cc75750a4", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.7.9.tgz", "fileCount": 10, "integrity": "sha512-DO8e7SUNF9UVCp1Ts8S2i9Qh+mMvUEr55khTMOobc0sr7mA2dnWpg0PrQo2dPcpROgePvTpLwS1GVs82Tpogbw==", "signatures": [{"sig": "MEQCIH5Nj0I7rF1sol+jWHIFeVpCw3iizld93gVnULnuq+vOAiB4fs1fiUCDZeulPeNfEPqkJcnARa4hY5mdk9T8FSCV4w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18855, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbyLgpCRA9TVsSAnZWagAAWvgP/iLBNy8JQyt/2cLmadZQ\n4oQwps4qjTQtFAmuKsPRWDEUq9YBbksz14lLPbIM0ojUy3t78mH9RDKJjUi9\nsOBz47gaAs6X41SAOcJ5BqGh17+5WXwLDtjnK9GSZCIOzlvXbDcqLMz3/3Sq\nxdHj4k1E8SEos6Y6ghPjQysN0B3Lh5/k/QpfZ4RDqrS3CUvvD4MFFWWE0WYi\ntR4+ep1OVlD0IbE+X8StYXXXQnDZ3G/+YAObKzWpfZjUmTdQIL709vPH4zhD\neQTzjxmHC/bn51xEC/2HmzGcvELsi+Ht/ZwnoFxhC9+ItpH17eOmxWYcB7b4\n/aKg7ZecWNXJtjXTPL+405S/pYmQAT1D7XxaRjXFMSuzJZCBS0cwBL6Eywdi\nuryDwDNfZkF59TJJzX5F2WwO/f1Cxd99fEhxIpt7mmbVnqvK+CtNZY9fr+2s\nbNkQesdBhbiyXR77RxDf9jFGpu1X6iwuECQBGZ1Hm00HqctG63pz3tNJOobL\nJ5N60IAZ3drB5GT0NAgGcsJyJ2b3Fhr9dG4KzSM7lcQe65Tgw96Efay/avc8\nq7x//DowaUlg0i0xEUNXKhJwGBLk+lReDKDSujPu6pMvH1345hFDUUVzH/rv\nE2KytN2C2qMMP2CXZCGnCF/9AU8Ms2k8hHJf587a5wa1vJRu7JGIj6u0NxHl\nkqt0\r\n=cLsq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "6c5bd6e21d734967e12bb7b7aaa38c80697b3b68", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "description": "", "directories": {}, "dependencies": {"@webassemblyjs/ast": "1.7.9", "@webassemblyjs/wasm-gen": "1.7.9", "@webassemblyjs/helper-buffer": "1.7.9", "@webassemblyjs/helper-wasm-bytecode": "1.7.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.7.9"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.7.9_1539881000383_0.6579278178342538", "host": "s3://npm-registry-packages"}}, "1.7.10": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.7.10", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.7.10", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "c0ea3703c615d7bc3e3507c3b7991c8767b2f20e", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.7.10.tgz", "fileCount": 10, "integrity": "sha512-Ecvww6sCkcjatcyctUrn22neSJHLN/TTzolMGG/N7S9rpbsTZ8c6Bl98GpSpV77EvzNijiNRHBG0+JO99qKz6g==", "signatures": [{"sig": "MEUCICpdTxSBZo9cQPhNS0fOkGvaTZLhIi6NjzaaHETSLs30AiEAql5CCmci+K5pnoMmi1oqAXb9RmejM8YCbNIoOQU892U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21663, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbzuv3CRA9TVsSAnZWagAAcF8P/jPX/LXfZRSA0VbQvjbq\nCp6+pP23+yI9lTKXtkHsVBKgLoqGYRxS083xoHyb2KVIgWeoo/mQlU2iRfxJ\n+HdfW/1QI9i+YP1nMm4jGRNdZhZxbYHhbpRQFmcXK5MZOvcEGIOTo4vOmQoq\n8rSPkgo7bJ3aFBh6C8n2ytsffs57oluxpUJI7BMgi8ilyupH2RCwqywQv3jS\ni6nudBWCxP73a1uIlQ7CGo9J/PsT+UOnTl1oLRf/sNn46OKJ3lAWhWMB2k2Q\nJxL9ohFm9wpSoRCJyS5iKGBDpvQT2Kd5dklU90tWVcANTYiZw5x2DRze4Q3W\nj9RMou4K9SgXVce+fXxKcC0ed3cmVw000h8smX+qfc33Gwu1mtCF4BA9GHav\nrjujBzzE5w5aISGxNQie8VEJbDT90RxB3u8TrFJg463G1gWw1qndXSb6/rqH\nDQg96QANPEPY0JCv7sBLPPfo6f5PDoEodOJQgi2y3g1mxwS2JYTG1f4F/Ept\nBx+sQe0Cui+9A/EzpYhmR8OAADSWTVLOdG5zAj78trnzaskMWaIoaArnoqB2\nZ6heIsIgqFv898z2MHa4ncZa1QceNneMn2+GyIjGwQ9/XA5EfrMESPSqb5Fz\nptce/JQ4M5x1v9I2b5VFjikl6cZpXEmiIeia/u4HDQAUT+7MKW7HDnsshXf9\niA9o\r\n=hsak\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "f558c6c047187f24a2200ab04104f173de226794", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "", "directories": {}, "_nodeVersion": "10.11.0", "dependencies": {"@webassemblyjs/ast": "1.7.10", "@webassemblyjs/wasm-gen": "1.7.10", "@webassemblyjs/helper-buffer": "1.7.10", "@webassemblyjs/helper-wasm-bytecode": "1.7.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.7.10"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.7.10_1540287470574_0.15166515390306845", "host": "s3://npm-registry-packages"}}, "1.7.11": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.7.11", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.7.11", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "9c9ac41ecf9fbcfffc96f6d2675e2de33811e68a", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.7.11.tgz", "fileCount": 11, "integrity": "sha512-8ZRY5iZbZdtNFE5UFunB8mmBEAbSI3guwbrsCl4fWdfRiAcvqQpeqd5KHhSWLL5wuxo53zcaGZDBU64qgn4I4Q==", "signatures": [{"sig": "MEUCIQDUDnYns6xOaiPAwh2pehv1CtbgaE+N1KJjQBnwL4hL7wIgQar0J0zZWis7Gg7L1+EcWNujdp4VjUHon1ItKmYQzfs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22751, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb2Jx4CRA9TVsSAnZWagAAenMP/2b1mBeoeZBeQmVSmPz8\nLwuOMACaNqNSwo3K+wd1SHLm7mOGMIwDeNRxs1784flq+AZ0Sb5VOzqGRlpe\n2EsN5bsqPGKD3Bp9C71NEob3Ux8Llvo+a2prR/WAHvYSDemy7hGhJp33jZ9/\nCpyiKOYPBvv4pvq5m3vANEYtitYCIBEtaCANJOA+FSSpkkCooWy24KDPY1uO\ncuYvZmi8JT7kZsT3HXOuW/VydZPcVPs5pQmpUOw3GVYlrlxyJ6XKAfTQHZiD\nEY70+Er0rwg6z7ZAlqbi/vPyDmzJprJ8U3AvTbd27PuKSDRzYY69/bhM6KK/\nNBzUcsEsyzkNSeT8RlIke8P02v7uT3NWAL4RuYq9R+8gYeDxEzQdl/zrXUwq\nrBozPZZ/3OJX+2yMs5SYtpg+D58WK+0ETF/P0L7RG5oIV4dG9vrVfbLgz/bP\nXuV7yg9gj5lRHoMjzrBCK4TQ4qDmuPoreyJjtI/89D/6kdBQKC9rfkwHLQRh\nUQah+qG+8FQg1LAYO1slc1Be+cm3dU5NCl+P8/pbNzx1biTAMJpSGNo8M8qj\nWgZIPk2W4Ze7pPPT0a1jWIZh3hkVd0ZrNZMWQ9odLuhTcYVocSRd4b2HtS9P\nPdVUdMu0eYqDyUQxgcsejcvZt7u/NyeQXpzD+14ZPWisNTeSYmEC+0iJEnUu\ni9b7\r\n=L+Ih\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "4291990bfc4648bc6676091a955d12dc3c7e5909", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "description": "", "directories": {}, "dependencies": {"@webassemblyjs/ast": "1.7.11", "@webassemblyjs/wasm-gen": "1.7.11", "@webassemblyjs/helper-buffer": "1.7.11", "@webassemblyjs/helper-wasm-bytecode": "1.7.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.7.11"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.7.11_1540922487820_0.48011497696309635", "host": "s3://npm-registry-packages"}}, "1.8.0": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.8.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.8.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "3d8f7798c16b078693991ff434b431602c22c1f4", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.8.0.tgz", "fileCount": 11, "integrity": "sha512-d2fpcUdgEpSbb0eRhxeCh2tLkvDmMftcxvX83n23AtNwwK8PYaA4psLf2hRV2S7cZeAMg1QQ4YQz9/7IUjt7og==", "signatures": [{"sig": "MEYCIQDjv92lnFkkxHgSwF7+T7OoxCpHCpcYZs8xFOI9pIbb1gIhAOcKf1R2Xcatkblu0LOdM/SyEFU9EXe8uMapWxb66ERu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22745, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcD2Y/CRA9TVsSAnZWagAAdtUP/1Y0rOx0MY5RJC2JVLIF\nKWC7h5x0wlo8ZUEEyfic/PUQoE3duWMyLLlaeDYZiWaT67az5We4GQdyKx8c\nA/hghcevigrYZhiW9GLi+NuziEqMv8rUy44jFx9YNnie+ExLCc2ev0iVuKVS\ncQ2CFulOH4LvF+Zl5ogSboiUxarGZ5/qRTDWzWPOq/sHWqgDrlrTKs4MPdcr\n2qLQg8NZ7N44LwSrutEYeLl1ZQ5du+E4a4xb1LFgs13ynLKuX/HzZ7iiLEeG\n8oRBfbV67KwuytVHWGOI5SeFhQR7hMYi8QNBuKLysESJ75WXeIxY4uM/ASFl\nDvG78b44MsaHLWD5IpUSGAzCA5mIUAEA1chViMDIccJ1Z6mLtRVc7y6/73Mh\nKCrggxoWWUVbxTTZ1a58F3DqTM4MtnTXHelXp3DLS9gFxjp94sANNNaQTmhf\npFdd1zxn6IC1sr6gPEyO4q3FMQnZ4ZH+QYK3QGkSK/H25r2BEoNtSIxLaQDj\na722ZE6AIoOnstinjNyDwqNhf2ePbIWy32/B2nHUIgGbIaF3qKQF1EzAc8dz\nlJwkEoRfuLVk80nqeMCDFu/dBMA1wD961YJQKkLRgQot2mAAk12U9GLK1GdS\n2slLZuGm3+NyQG7d9gJYwQ9fFXY8F+H/ALGicefopa0ML14HhqRDQ1c9htjS\n2kgJ\r\n=hat0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "8b2d1afa793ea81f20ec63416134c201e39694eb", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "description": "", "directories": {}, "dependencies": {"@webassemblyjs/ast": "1.8.0", "@webassemblyjs/wasm-gen": "1.8.0", "@webassemblyjs/helper-buffer": "1.8.0", "@webassemblyjs/helper-wasm-bytecode": "1.8.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.8.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.8.0_1544513087192_0.20292001263765225", "host": "s3://npm-registry-packages"}}, "1.8.1": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.8.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.8.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "19980204515c38cc570e9ea76e2da646179757bf", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.8.1.tgz", "fileCount": 11, "integrity": "sha512-FlNdlARr+mcP8XL+wg6bXqgC+0ZwnltqXExw63e9cgK84bAdTwKnfX9k6CKg8qvK5e/d9dUmk0dkVrkyEpKx5w==", "signatures": [{"sig": "MEUCIQD6VGUsrTJkkRf5EjPZ4aoRHnybAfnu8Q3FbvCQ/IrDAgIgTdf/jV96gAAUPogBekfufQgOvxTGcF3I59ebvbqe3eA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22745, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcPZgYCRA9TVsSAnZWagAAL+QP/RYRqZ1bu7P2PSpCDMvT\np4jUpnihRNoQZD2l/E+glJOLICQzkhkc1px2SKmDZQ9z4krmDFkSogkxjZf6\nKAMSmk7vNk/eGkr3EWaDNtRVrQLMwBYCgjmK565A0kdwf+5yLEsweWyftAea\nOWNsguRazL0vpuSkPBzTdW6BGiVTyBzj1nb64UD3+aFKnn4MovX5YkEhILu6\nq9xFNrwRQIxLPmZWnwtsxqnFR8ikVubY4DsbdShYffxlw65yIzGMVESpSCno\nKiw+RFDhs3VSORWJ7vxh0fClA0SoWgNuEwXfJMjO8hKetqM+wGFJv4w0/EMR\n8pEbI6agX+YHnR3FFfGgVd4nACwxcOEkjFzOmf6CuQPP0tsbFO6Cm68qcB9k\nJbt1ORYwvh8wTlJD4dMdRFTeJIoDusaDKwX7LUcjF/FUkJSdOM53KO41Uh9C\nOfwcwHpwut3RU23ZDOMMatbx07OhoD3tP0jACwHkUSasnNHWH7Sgg8xQ02bL\nhTuxCVvg3ae6/87ASJYtifcBQZj/6zZMkgOuY77eWKXp6n+Cb/4qGDZHgywT\ndLl4iydEpg03NR+iv/l3mas7lM84uCDQPXYL4mr5eLLkQZA95dqeDLdTI6Ho\nEmbRQYpWVHxfmoU3e3Qw2lZfYjhgrz0z86T7fT3Wk8/08fkOLU43dJ6ClZM3\nqTJs\r\n=35qa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "a2f42245e9b597e3541e0f697253449d60fc4d79", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "description": "", "directories": {}, "dependencies": {"@webassemblyjs/ast": "1.8.1", "@webassemblyjs/wasm-gen": "1.8.1", "@webassemblyjs/helper-buffer": "1.8.1", "@webassemblyjs/helper-wasm-bytecode": "1.8.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.8.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.8.1_1547540503519_0.9803961930846503", "host": "s3://npm-registry-packages"}}, "1.8.2": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.8.2", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.8.2", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "4b36c105bfcc3dbabea9e8d04617127b3178f92f", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.8.2.tgz", "fileCount": 11, "integrity": "sha512-HLHOR6/Vc+f5UziOUNQ3f5YedCMCuU46BdMEhjQBQwlOWqVAxgwqUn/KJkuhMvvjQ2FkASaDup8ohZrjyCKDKg==", "signatures": [{"sig": "MEQCIB6IMSzbIh15LG6q6RctXiJM6IUTXowyfi/Hr03y9v6dAiBtw5UiierirIC/e4P0S8qsxpUf4mw0r9XNVQ9kxA+mBA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22745, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcZZW1CRA9TVsSAnZWagAAVkgP+wfGkW7O68OZkiLoznfe\npV06Fe/yc+ME7P2zGobt4kB6hEZ/kahQMhnTds7FhQI8lfyFU5vDVOUPQ60A\neVwRJEwIl75e6iOxQZ/auQTnQYyoI+bMGOh9DYTVl4tuRCRnzrZB+89SEHyp\nJy0bJwkapIlx6fW+WrJ6gTTiORHyOhxXtAiRSCUHzpp4AznTIifP4X+uWq40\np+w4grLwTyQAN24cYE2i7NsxumcuzyGSM44VrQl6Fru84hdE7Nff8zZY5YFv\nuRHYeV96pqqm67rt35TNhHoX559CvawJ9lY6EqvlVIwKTX/q5XRNh7hTRjf9\n/UB1BVrtdNgGIxtf2b2UhplyH+2SxftzVzGR8xBVVGFKDkmPpb8MfFNMHSZg\nfm31J7V6JyNKJFXaZAWIv8g8I4eKxHJtDJaARr8SdVJ9N2xtrZ4wOJm0TamM\n+npadlW0VnP6eyHGMlGj+OJZNe/UbHc/nH5c9HB4BMiIkjGah21fyEWQb8pF\nky4I2vXq9SYBuIQ9e6sgowRua6m1cFf6abhpBCuQ2OmIwHz1fpo+YK55i4YR\nNhv0CUY0C7hPdkVvu+GcN51B3nKbVINPjqqvItfRUJWF3bYHMrsc0OH/0NP2\n+vNa4o4PG/as6q0KkVLLxNAZ+LH7bLKUmzZfUdq2+7djsz/H0ZC+mVvREp1i\neuFU\r\n=IysP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "02af462b507aa7a24f5d3201178434b181bcdabb", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "description": "", "directories": {}, "dependencies": {"@webassemblyjs/ast": "1.8.2", "@webassemblyjs/wasm-gen": "1.8.2", "@webassemblyjs/helper-buffer": "1.8.2", "@webassemblyjs/helper-wasm-bytecode": "1.8.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.8.2"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.8.2_1550161333007_0.16437213914241755", "host": "s3://npm-registry-packages"}}, "1.8.3": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.8.3", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.8.3", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "9e79456d9719e116f4f8998ee62ab54ba69a6cf3", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.8.3.tgz", "fileCount": 11, "integrity": "sha512-P6F7D61SJY73Yz+fs49Q3+OzlYAZP86OfSpaSY448KzUy65NdfzDmo2NPVte+Rw4562MxEAacvq/mnDuvRWOcg==", "signatures": [{"sig": "MEUCIE9sMlhXy5JdTJerwpfw0PCLPCisvp/YsruEKi5AW6P8AiEAvc/kTEVozvGzeDzd4iYeqPhPFkw4NVLfeZQXw6+MEto=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22745, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcam7PCRA9TVsSAnZWagAAMOcP/0RYG1HUUBttH9tlJP/y\n/8uojTHBny3yezTKK8smlOd25hPI21YQ8IkJ35UJMxpI8+6IhPEjJNhZqVVO\nIo5hEU7kxnn7nw70PfCp8RCfXt1cAN+OaEyv8wBfplc9VNlB1laTJrPWgIud\ngcTavw9JcDZYdHRLUg+jblm8ra5/iaZJcv/QXsZClh+S2wYf7vYpVoNnhEjv\ntlCDCFmvPfKqPYeo6zj+QNBdJbB285/Xx5D/u/nwx3DY/heNiyMJ/CR80tHl\nWf66L3VzBTTlcUqfAzYezQ/h7xCqCYCebosV2r4GfaZVMT+tWV5mB2Beevpw\n7sUqNE2/zFv71QYMg37KAO4MaUmL4Bgfh62sxIWyX8KpmskelkKW/ZklyntG\neNDt6Nu6Ql3v0Lr0wodLwXUfQHvFYlCLcSjH0pcRtd3fam8hrWtGVR24nAgx\niY3RMudpLT/pryKqxGpkzBm0tsDQgSt4h4Sn2bTZrh9RHcMH4kHJdRI5ytvY\npHcELKAaOc+8Kxf7ySbuH0ZSg5Y5DU/z7/9YrhHzb9zBRlnb+7fF+uf1f4jP\ncDx7JtYRnyhG7EpJk310MUU1PcK2NdnXFLbONOooFip+ZeH9u5/IOfAkpApb\nwopQysWtmn2eB11Fh9XT3DMzSoTEEhlVAh9b/h2uTvxDqZoncj7ny4UN/PpA\nrRIZ\r\n=nRwE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "e482c7ec291d61fc46e42c93d3b8ec7517b629e1", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "description": "", "directories": {}, "dependencies": {"@webassemblyjs/ast": "1.8.3", "@webassemblyjs/wasm-gen": "1.8.3", "@webassemblyjs/helper-buffer": "1.8.3", "@webassemblyjs/helper-wasm-bytecode": "1.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.8.3"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.8.3_1550479054808_0.008076062225373715", "host": "s3://npm-registry-packages"}}, "1.8.4": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.8.4", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.8.4", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "2af955a0f2adffd4f6a781c7bc20b3e2a79348b0", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.8.4.tgz", "fileCount": 11, "integrity": "sha512-qaHxTtDdgtU+GUR1ajobnkB0eUGaOz8AfsnBmXgD57tIa2RAuLIIWyLIMzEW6cb1XGwdRKBETLzGPMHsx8kZ5A==", "signatures": [{"sig": "MEUCIQCJoF/myK8CkFMIrdSrF0Y0FOGBCFiab+zQlAHVl3WiGAIgcrXkeqeOOj4z55BSzmRw5M6bUQIa8FAX01/4bDXnRT8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22745, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcbDyVCRA9TVsSAnZWagAAmqgP/Rq/jrx661JDaAytbOXL\nn6OG/NEX/IN73jLZJjC+AuSe/39y9NhGbCvdR7wdpM71/ox1WJ20vngO+Q/E\n2XipEXvBhiCJOO593rj3GKrdsxfTxG9L7Zl3Wybhjz2euzH1/A/ehUqPs7yO\nNF647Zq949hUVa0fLYDxyW/mnCE+EvX3bNDmX0C37YcoQk8h5dzSBQlIJNKh\neot6Mk84Nwf6p9S/Bo+mOLZyBU/GtWes5CNhTZ+hHIeb6KpRhy90a6lCMPAB\n4yPtkmkyU6cSUe48VevmL5KK+24xFuglMyMN1v19O//YzH5L2DNZr43Lq/XH\nnnOit3KTZ/oyxtm6xcqTUDOajnsVKRwphGXTuL9/xrZpoKH5DAuwUpKobI5D\nAgPK8y6m3DTKOdKx7omWdsava9dSo5fUlr7WZqfaDA2vmsU07leZtM9RMsy3\n7kk5CliPPWZw5L6U2nM/iHITKEpvqlBT1jDFy0z9zA60vN0MBUD1oyVQ15dj\nRjLmGM54zaFu5js/KCE+RmJNbY75hd6tvHeEnaHZKRyGJ0/2+zFbqwqzx/xJ\nzWGSOttC+i8L3tS7nrZxtd49A6Obs6dwHUT61xRdjZfx+bSSRj+v7eQguun1\n6N2pT0Kl1GQHVd1QaqYXT3qU3emFa+WAGY4XfEnox0HkO62jSkdztIJFAQgK\n7CaO\r\n=o7z/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "0154b989cc9b41c695724a361b3aa6fa19c5b032", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "description": "", "directories": {}, "dependencies": {"@webassemblyjs/ast": "1.8.4", "@webassemblyjs/wasm-gen": "1.8.4", "@webassemblyjs/helper-buffer": "1.8.4", "@webassemblyjs/helper-wasm-bytecode": "1.8.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.8.4"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.8.4_1550597269382_0.9600460685839718", "host": "s3://npm-registry-packages"}}, "1.8.5": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.8.5", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.8.5", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "74ca6a6bcbe19e50a3b6b462847e69503e6bfcbf", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.8.5.tgz", "fileCount": 11, "integrity": "sha512-VV083zwR+VTrIWWtgIUpqfvVdK4ff38loRmrdDBgBT8ADXYsEZ5mPQ4Nde90N3UYatHdYoDIFb7oHzMncI02tA==", "signatures": [{"sig": "MEUCIH8zWAr8I9slSAZJDmF+nqRRjz+mGRDmGilASb1BZiI7AiEAroMxLH3GW0O9660rKsuZr/y3XpO3HcQbd1Fnb9PpG3M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22873, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJccnVGCRA9TVsSAnZWagAAp8kP/imcgUCXCaM7VBe/6wPI\ns6g/pGDnTgQmNpMTkiJzKJj7h93wN+mq1fXZkEdoIaySjd++666hTtlVSdNq\n8LUAJvuTsljvZwUTT0dBy+nzgxsLl1/9XAEiL5bFFuI/gLlegG175vz7Aggp\nGsiygWjkVawYXorI+cxAjCQuJYGuTtwkvK7snXgCRYZTM7S+/zWF/IJroJKr\nL0tM/iH/+ctx1LjNdyAxbsQbmrhWTvHBnsWR3WJB/TA9pzONoYydY4FSLSUg\np8fqib1sBZPyC/dkcZ6hyrzOjO4PvTtpj8+Woo3Ac+vQ76d0Cg/0mD3Ea7ew\nS9RQ2doyslTa46JnBoRCnnS6Lf2SXhhIQcSIzv3c86Zf9uEbJTobgbg5aaxZ\n7qFwKZgMBY4ftr3/dbs/BnR8Tx7OGiD4n8VnD3+vqnzOk5Bkhbm6SRODMvlR\nmCWFLJyKzDaS5CxE40/UEOAbZz50DmnZzIRAEa8Dn9CHlu45IZ+KLDAvtlHf\nDthJXoGd77UoyQ1a5nhEyFSz6Q8Oe+YRKZDdaHVI0h9F42AbfsO0tXblCBJ+\nm1T/qA7xHJFRpnaDG0d+ZEcNofzXhslXWy5Oslzc3xxjzBgvHATkYGrXvE/n\nNc2II3BeyM8Xr7Cc32lR7lWj7iQnjOfVR9+IEHQGv2ehGkU1gYH/GIepeQmk\nNCi4\r\n=mcIG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "77bc195e9bb463b4fa4cbdb6291eed446d454d49", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "description": "", "directories": {}, "dependencies": {"@webassemblyjs/ast": "1.8.5", "@webassemblyjs/wasm-gen": "1.8.5", "@webassemblyjs/helper-buffer": "1.8.5", "@webassemblyjs/helper-wasm-bytecode": "1.8.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.8.5"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.8.5_1551004997313_0.3067200857324779", "host": "s3://npm-registry-packages"}}, "1.9.0": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.9.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.9.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "5a4138d5a6292ba18b04c5ae49717e4167965346", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.9.0.tgz", "fileCount": 10, "integrity": "sha512-XnMB8l3ek4tvrKUUku+IVaXNHz2YsJyOOmz+MMkZvh8h1uSJpSen6vYnw3IoQ7WwEuAhL8Efjms1ZWjqh2agvw==", "signatures": [{"sig": "MEQCIF/E/bpu7XKZfGnYzPqWK1ETAxZhmNJIW2NCVIF/oC73AiA0TTL43heoI53nhA8PGIHNEGGCHzMOXvSGDMcQkr1Rsg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18983, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeNgezCRA9TVsSAnZWagAAYwIP/RJ3qIou86MaEsLQEhlM\noMW/3wJq6o3SNzSo5fX4k3Um4dqFvotzIaMM6sfL/s0x0abVIvfzOA0aaCCU\n4Ic4E5zTyleDv2mpje6lQ3V448JCt3/KnZW4o1dSwP7EKg+FbppCeKRwDFX8\ny4BjvUwLzV5QUUYDCwk+t/EmpCVm/4nloXNQqNZQ9ztML3NuJxBstTVdi9Mv\nGWcZYGzlDEhxIjN9jkjQfkbH4poeLJ//YvDzpZvbyPls1z4BScKSWla8CtpL\nDqQBQ/N+DEjZ2v9Jv+zuihMebs7fwbinRrez2hiP9Srd82nx547uMwXhOyBN\n3fJOfFzCd3pGt5R7XgahP+rwKpEEmULGaGT674LPoFs7GTHfD/ZsK6h+4sh+\nFuEDdHx7Qsb0bEtUF3RF+5Wxi99G22euddpm24/tHssa7SOt3Xr2NdbmTRYp\npvJGZO5dG5pxFa7poBXKEJnwMJlYZMdALC0Nov/3s23ILIaFwGttTZFKasZA\nrkF6xyuz3DJQIr56TfBXL9K8NGUW3BZXYKFSGgLF2qFPb4nXaAe5JCWxW24b\nemDW6JDWyKyL2NvvYfMxPbS+vHZDykyKHZIChtBHkFJgwaOaGD3O14faDplD\nz1EVMIG6pNix8YBN/jL4p2wtbcr+GWU1XWIt1IRl7nyHz25J9DnH3gdNqApW\nuvKt\r\n=M/jl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "0440b420888c1f7701eb9762ec657775506b87d8", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "lerna/3.13.4/node@v12.3.1+x64 (linux)", "description": "", "directories": {}, "_nodeVersion": "12.3.1", "dependencies": {"@webassemblyjs/ast": "1.9.0", "@webassemblyjs/wasm-gen": "1.9.0", "@webassemblyjs/helper-buffer": "1.9.0", "@webassemblyjs/helper-wasm-bytecode": "1.9.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.9.0_1580599218946_0.5805059183701069", "host": "s3://npm-registry-packages"}}, "1.9.1": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.9.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.9.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "f7988f94c12b01b99a16120cb01dc099b00e4798", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.9.1.tgz", "fileCount": 10, "integrity": "sha512-FetqzjtXZr2d57IECK+aId3D0IcGweeM0CbAnJHkYJkcRTHP+YcMb7Wmc0j21h5UWBpwYGb9dSkK/93SRCTrGg==", "signatures": [{"sig": "MEUCIB+HAkfU0FN5ELb+EymTefxGRgQ9xvukomvN1Vtp+vIGAiEAh1aek6T0wAk0rI1eIGTGtqIp0suvCykLulknAP/8fv0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18983, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfeNotCRA9TVsSAnZWagAAz2cP/iT6tUYHV2tlJ1s9FSWK\nZkHORfIEPlylWYc1tKfmpndJsh2CFETSqoAE42f/s9i9ZYY7V79IlG4CFLy7\nirD7r56IvuUIlMAMOVqKEOWuwml4nCvmNuxY8OY9g2VDPXNlSvABgYREjtFv\nJ9U1Zvdx8+VF624aBUReR+MO43fz37M1oqAEn5jRvXkuu6CBUIY5j9LhqBnt\n8+/xmqbWPqB6rM7eG6GbQjzuVj7bAMAT9Y/nIIbHQfEEH22pN0rd+uI283i0\nW7A3+6aI0YIIVf4b3XhJweduFGQivmDp/ZNQH+aPsDNw0rtNOLVQn3vjrlfA\nXnDJ+I1KeU9Ii9L3L08V+AbAIVMVFhjNUXWKER2OXLl67700SzrkSjOq2lM/\n4vteiypXvizKj+11kthekDUmN4dwX4LWerP3dNPeeEqOt+f2W1kfdy9aLM2s\nrsgkKxhKUAzCCtmssC9qgeVdFPosf/a9X80YjNp+vasjoUhKrmduLoEXGM6P\nqAMT6szowrSKsAkUrPXfo3HhkUgEsHxl70AmLbmA14KH5PoR/QeMHgX0CnCb\nWdFRgzmrq3zFU/7keD88oiMQ6+G9mgW91iHUDNzNytxMXGFNqE+rKRITdHNd\naNu8XSgOnTQTbpkDX1LsE6pAQhoue8lE26ReQGoib5nrTFf9wji9QulFRkNK\nhTp4\r\n=Z5tL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "697a5f63048049e9ecb3205d789c1e80eaadf478", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "lerna/3.13.4/node@v12.2.0+x64 (linux)", "description": "", "directories": {}, "_nodeVersion": "12.2.0", "dependencies": {"@webassemblyjs/ast": "1.9.1", "@webassemblyjs/wasm-gen": "1.9.1", "@webassemblyjs/helper-buffer": "1.9.1", "@webassemblyjs/helper-wasm-bytecode": "1.9.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.9.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.9.1_1601755693329_0.790640829620753", "host": "s3://npm-registry-packages"}}, "1.10.0": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.10.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.10.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "67eeb2075b48456d8144bcb91fa2ed788c5c90b7", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.10.0.tgz", "fileCount": 10, "integrity": "sha512-yDgiU8aAYD4O1HXrUSLDHxHNuHE57WuDNHgQk2PTUY1ilKDxv0mAveb/+tO96nT388Mep5TpIV7MNt1mbR1n8g==", "signatures": [{"sig": "MEUCIHW8VODBpbF3XKEZ1b3vx1rvVm7BQtssh+137srhjHh5AiEAq5x+gU9dOd9PiLLSk7G0/ZuB9obO8OI/3YMTqHUR5oY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18989, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf9zReCRA9TVsSAnZWagAAEMwP/Ak/BCM+oc57IQGnIBZY\nJJIDfBeP2iPzUwuiLilXyG2aS9jZaxx4mjE9bS1bO3AyytAo7tIsdqOLrbUz\na2Et5m9pQ8CxA/rEUDUmSlOvj5xV5VYfm0oXVFoxyHOR42pIv7n/bwdCgbxc\nrW3hheg/rPcrALyHlzx7bmS2FdrSQFar3y4672juzuM9SJP3KLXu9YMTtILS\npGDki5kxeFjs//Ha/7JufT55r+hJczY7IVSWr1eq3vZDtCF6jYHApmNerO9g\n6V29wT9cUL0URg3YHuoaegYBdl1ztizUws3b+dpTl5fUgOgQVdYgNE0zP7pC\nVRTAX3LQRsf0YIeH0b4IP68ZMWuncmqAyFhOEUkzti50R32nVA80ArSc9mit\nKtalAP551gfkWs6XwhUslX4hp+BOZ3QMgrS0uvmDEm7NcDKO5OqLFBKCDIIx\nNssQ5urQi9VMaMcuU/UighO33nIrSVOgIddeuA8ynYlsL0IWlwryp3GnXKru\nWdkRaZpcpKZwuVlD/xNJrUgz1TrvIIU01+3TR6GgOIRiSMycIlCfmhiN5cPC\nwb0izsNnfqm0OQQ4z2ude5cCp/lF//kqeo4p6241C2VeMX6uar55ibJDjTY7\ncTTaIKNMjzEPrVWGCJDr3r35mRIXetDBqxBdsBcUpm2QvIY3RD+mbgDh2HL4\nxKEB\r\n=CFdG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "d00b899ece0242275e2475e75232ae18ddd03aeb", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "lerna/3.13.4/node@v12.2.0+x64 (linux)", "description": "", "directories": {}, "_nodeVersion": "12.2.0", "dependencies": {"@webassemblyjs/ast": "1.10.0", "@webassemblyjs/wasm-gen": "1.10.0", "@webassemblyjs/helper-buffer": "1.10.0", "@webassemblyjs/helper-wasm-bytecode": "1.10.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.10.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.10.0_1610036317602_0.4251002376806967", "host": "s3://npm-registry-packages"}}, "1.10.1": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.10.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.10.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "3ca5d21e8c35dc5ea949d1113b9bbc01e83bf9d1", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.10.1.tgz", "fileCount": 10, "integrity": "sha512-97AjuBOKHnGgf2DLO46YjgpuVsK2CyKpUwb/0YC/bS55azbK+PMTTUji89Iav9v03aN/tAhb/O78Vf6/+OJmLQ==", "signatures": [{"sig": "MEUCIQCbaRopiGU8ikaA5jbVfZ24ltUrRQy1U6/M+j+WmKr5XQIgI86/mxidmr78El0I7lk5TUTAqbPdrIQTHA3bzWNOAmU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18989, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf9zquCRA9TVsSAnZWagAANIQP/29q6dcdPCGHjYhsSdRd\nmC0UHipKrKNiKgfmrNG7Sa8EJ/ttk/1WU2a4y0zRQkFfcWDE388/FSjEUGLh\nmw02v93HWr9aeO5NZL6o4d4rwOrc6VFQUY3GHsY9kM8VRv8qiYygEUAVDUdG\nWNWILbI4KuD/UP0zjKbjBZhUQOxbJXv5cCnJ6LYhZHbI0sTWgBoQKZ9lkXOP\n/hJU93ZmGNGVdQpgdhXRG40IPjJA8sdoNKgAvn0Rj1GdToWuQtL2UyXbJwND\nCSSN75jg/a7gBhW89ihGkw1BA9OyCkCaPZaPrH4+bO6vmexpsBB/N/tXiu+K\nO/FCrdkVAsAiypx520jfn1dGIw2B/xORW2YvcJGIxftTgz6SLMpohjRsKAHl\nJ9km4JyLTo7F6nGpbmJMwu08ww8mOOz9s3yz82IxzzMGYbnhY/+TAbxfhSkA\n3ARp8CLPoGTdQEQuZtAct0GIOUnJy1CP2718LXVoUb1mJrgRywG7Wadj5PJP\nAflWCEeyAh7PVxohAz9Q0FlsuQZ01hyXO99YRcxZMgZkUFSXLrnmoLQxgBgR\n9sXsxf91JeZB2IGyjVqRJK5/esWs1zDdk3fMygLfTDN8azDRPfDNNLxX+8/9\n0yQaWh08TwIvAwVoSrGevIStiCr1dmLZDMYuQODSnbHZBHW7t7U+ApB+bL4O\nF7MC\r\n=+BvM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "f723f2cdd9bfccb5e199962dd8c5c09bdb0faca4", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "lerna/3.13.4/node@v12.2.0+x64 (linux)", "description": "", "directories": {}, "_nodeVersion": "12.2.0", "dependencies": {"@webassemblyjs/ast": "1.10.1", "@webassemblyjs/wasm-gen": "1.10.1", "@webassemblyjs/helper-buffer": "1.10.1", "@webassemblyjs/helper-wasm-bytecode": "1.10.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.10.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.10.1_1610037934025_0.23218704385130362", "host": "s3://npm-registry-packages"}}, "1.11.0": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.11.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.11.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "9ce2cc89300262509c801b4af113d1ca25c1a75b", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.11.0.tgz", "fileCount": 10, "integrity": "sha512-3Eb88hcbfY/FCukrg6i3EH8H2UsD7x8Vy47iVJrP967A9JGqgBVL9aH71SETPx1JrGsOUVLo0c7vMCN22ytJew==", "signatures": [{"sig": "MEQCIG7LF82A/EOYLMaEdIgNxlUiZLfdfw5quYgk9V3tlK87AiBhkalKMzqMyDEoDLfIJQVhGiq577ngdwgOT4ekdqL0wQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18989, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf907qCRA9TVsSAnZWagAAl2AP/2R1vQW32IANgvR7d6H2\nOmCKInqlZ3q+p3cSSNsoLz7NbSRz8/8P6t/o17Ok3jrRZiBWevC3IgQFv9f+\n0HDtKkcfCfibURUNqxsajx/lgfQhonAzwWftRKwJ1J2TsEmKGTKxQlzSEbie\nO/Fh6ew7Ufzgcwg1IUNWdSMza947BbQ2jUBFCOI/y7N78O6+Zai21EOWSAy2\nM/Xu4nCjtS+3davAB4LLY7ar33ig73jbblWQ1zGwZSXWhE1xc7GIwy+qooFj\nYyv+f8XCab0yft/qUsXQPwbi7lkH15swLlqchLY95aVMBeZTqAscFT6dLZMk\naTn48MLc79MDDeR8AEZueI19lGRpHoKOBAf1Xw46U9VbnXX5OAjRyruQOJbP\niZBcolfGIoonCrDyUfxQbrrdmUk/muUJfO9CtggxyHzo6lEesOq8Dn7ooYaX\n+Bpz4pYxfJdpZCJ+7rRn0lOANTU9Spr8dzP6pD5UiQA3/7Mz1R3LJW8OAQms\nl7LnTd8AUPViyOo+4gHpwgGEsNhG03+CcRDcByksZBrL/xHgCzjXERDcdVHw\nL6mzaX175Jd171YykXSOuq6nQsh+n/QP6PiM/W1P7YZBQs6g4mbW8yUFRQ/E\n3OZ0qrmpyodNYCY7KezKx4ofZEwE/fxV2xsuTCbJTA07sycR15mDAXi0zRv+\nX7Ns\r\n=KnN4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "2646d3b7d79bba66c4a5930c52ae99a30a9767db", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "lerna/3.13.4/node@v12.2.0+x64 (linux)", "description": "", "directories": {}, "_nodeVersion": "12.2.0", "dependencies": {"@webassemblyjs/ast": "1.11.0", "@webassemblyjs/wasm-gen": "1.11.0", "@webassemblyjs/helper-buffer": "1.11.0", "@webassemblyjs/helper-wasm-bytecode": "1.11.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.11.0_1610043114168_0.7405299834635519", "host": "s3://npm-registry-packages"}}, "1.11.1": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.11.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.11.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "21ee065a7b635f319e738f0dd73bfbda281c097a", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.11.1.tgz", "fileCount": 10, "integrity": "sha512-10P9No29rYX1j7F3EVPX3JvGPQPae+AomuSTPiF9eBQeChHI6iqjMIwR9JmOJXwpnn/oVGDk7I5IlskuMwU/pg==", "signatures": [{"sig": "MEUCIQCKNXHJ386EgmkW5/hQQwRN13gMjG8OIreOBr4cECbkXAIgVPHU8uZV0hubV9GNjVyxIQnMRujLktFTmvZ/DVQhPv0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18989, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg4sFACRA9TVsSAnZWagAAQMYP/0DCMuZGbpyubqUWQ5s5\nnoW0SKqfp7C4YFz89gse1CqvCtk3gqx7GD3gk4LY8WhKJJ/aryfdhB9sG1zY\nW8bbza7UJNe1WXIMi9hLLkTMOLsH54wzh/RXoMOIV0d4II2jySG2R2taiYkw\nnxrAD1KKbzJX5iyIKkbRiCkGaG2f0yiy7c+tKo+Lxq1jY1diLzjyJ+djgVIo\nL/zX0Fe0J8Eix4loAKlJNxmKn1k5y5pFE7xy72SDbrvjTkcjgHkTP+eAZPnc\nH+ixacMONDuwmgmJm2i38A9TcTiuFrPmPgAtfIc6IzdHUltluIBswvuhiQYM\n3khG/eZ6ggGAynHrw607+1UZKUEBH5+VQ8DwX8ATxUFTngXwB6/aQFkqCrc+\nfqWBVZ60gMYUqwYNSVxbjj5OKfEPATO5DYkrepc4vb66afc9p7YQh9x/oxSf\nmv2pt72LLQJXbH71cQnjUV0UkjZ4awOE8J9eifQ5FRXgCZkjAThXjWwjOXUo\nKjaKMhPAsqBfxHghpvnWK+6qtslmzfyRzzuup/sJMI60Q3MhAfN5uvJLj8gM\nZiKKSJKNPY1kwwK2LPNt+tCiU+o0Rh2HoXAP7a/vD7yXcDMcwuHzcLF0a3EA\nfYkeKHQVbqAOmmsfTx1BLydWD9+dJILrsdVi4MJoalJy8h+M2z8LEqjpTQdp\nyQjQ\r\n=bl6F\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "3f07e2db2031afe0ce686630418c542938c1674b", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "lerna/3.13.4/node@v12.2.0+x64 (linux)", "description": "", "directories": {}, "_nodeVersion": "12.2.0", "dependencies": {"@webassemblyjs/ast": "1.11.1", "@webassemblyjs/wasm-gen": "1.11.1", "@webassemblyjs/helper-buffer": "1.11.1", "@webassemblyjs/helper-wasm-bytecode": "1.11.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.11.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.11.1_1625473343695_0.32420893700787445", "host": "s3://npm-registry-packages"}}, "1.11.3": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.11.3", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.11.3", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "f31d194c5067c5728b9c541e33fe24cab25a3bc6", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.11.3.tgz", "fileCount": 10, "integrity": "sha512-3bTA/tXHV1ro/naGtav4m86XNxiHido8djU5U098qidT2hsbtP+7cq3b+B9As6+hwHxtkjuy1zzXg3xjOfYryw==", "signatures": [{"sig": "MEUCIQCCaFU5qoN31MfMXzJW7PPT5xTWkxegsLxnsp2DMSV2zQIgR1l47bVUWRRfJeVctKRpjWFVUY8ytB0nRV4mwve+c7Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19727, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiwDGaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrsXBAAk9A5w8zqFntyuApWJV0m67Eprmsj8m1Acods5nEuleZJdr+n\r\npHDbp1Uvrcr+ibw7pl96HRdo+bm0/Ght2CtGMuOpa8tm+CNRyx7TC6XbAcdS\r\n1AND42Er7bLOWJM1h8L40eqUBGooTfFlcKGD3uaoSaMSEnnG2CRifGsUYqDT\r\nON+XH53wGhKFJS8LyS0nnp6f2GfeByN8qPHWwNZS9Rzl5H7B0Y0tMCTuFohq\r\nrO/xksFZkFgntEe9VEPmsT2r6mlUL6DwT7gwc6gsTwei2upEvgOznmkuINC5\r\nbmnVauSiZpUcfOg4bsWo1H8ez/ul09D4dmST6rQje5FrmXeQuOG0wr6wNDP8\r\ntZGz4jW67rBaDXvlontpIblILRWrqBe9EvMcAZAZOysDaEJPBDfIOYTt3L4s\r\nogzO6CasKMKamAmaAbnS/CHRAXzzZ9Ub+GDZi9zG5iFXyO0dqV0c6m4NCDmO\r\nA8ayUhKLINV16zg+/FLKB5gAWKpVMqz/HCa5xJms01sQUpX728n/+BrWjD8I\r\n0tYZpwxwYTY5safjcFvsP3FTkFMWKqozfN0ho329kGgcn7/3CEsfo08gnQ29\r\n3LFe1Lv0ArATeyVrs0YVtEY9BaNuo3+ZyX40gf50ti02/FzpOZtZK8tROTo7\r\njGt29ppDVeBfavWeWeMxKYonsUbYCVHjUu8=\r\n=TBKT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "5fd2425602b752576bbe8089c343d5d70ebc861c", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "lerna/5.1.6/node@v16.13.0+x64 (linux)", "description": "", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"@webassemblyjs/ast": "1.11.3", "@webassemblyjs/wasm-gen": "1.11.3", "@webassemblyjs/helper-buffer": "1.11.3", "@webassemblyjs/helper-wasm-bytecode": "1.11.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.11.3"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.11.3_1656762778121_0.5391319359559712", "host": "s3://npm-registry-packages"}}, "1.11.5": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.11.5", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.11.5", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "966e855a6fae04d5570ad4ec87fbcf29b42ba78e", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.11.5.tgz", "fileCount": 5, "integrity": "sha512-uEoThA1LN2NA+K3B9wDo3yKlBfVtC6rh0i4/6hvbz071E8gTNZD/pT0MsBf7MeD6KbApMSkaAK0XeKyOZC7CIA==", "signatures": [{"sig": "MEUCIQCh5K4kBWS7+29aAVdhyA+YmPAMpFJ/BZS7bLaKWeqmgQIgM7WDQrXJpvebI5Hf1p6SgHIo+xJp3zFe3Of/VISy9us=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10868, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkO6BgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoYrQ//V13SUZps3K/ROzGyGAruF+JniZfk0AzyC9W4rC8izMh+1GoC\r\nnOPLW7R18sf5OdEkawla+s+5xdgOkJG8Ht+ngoPyNhRZZXfOX6U/5EpXfKjt\r\nAPDuK3/aQZwv0u4VyHXsPoMrLkLr6IVY5z34roKT4GN3OfRYfZPXmTF7pejs\r\n1PgiWD7D8mL7dawfxUj0XwSY1DKMPdMpN2trA0aWrc9OhatE0v6HbqjhfyRu\r\nvfm9wTIFpcC7eHUsh8cH6NxNax0OtRQelKtJId7ufFLWHKEFoPZZnuKl/3uF\r\nZe1jCtXN6FCiq5K4dYhwO6PpqkRumaoK3mkvhdgO4tI/c9CX/kagy/rNsCUm\r\nMryfUKaQDSf63V08KXjTTS2P6b6egam3QdB0v54gcfUU3b+htDb/52krSITT\r\nvJ8rj6JSouKFgm/ivBkLjGTOB6rMrIUOrx4p5TSEuCiDcJOYGqQag3I2VuTj\r\nW7b2Bcj8sPcQWg80NchWn/vXnC1PcgkVkgvSd4ItKsf4bv9mBpq22xZ1nymr\r\n4BrbQf5zXPy7r0yzuDfezWbDEA6PWnrmlSegrvmX0SGimEHlx1JtzzlVO+Ks\r\nkazdTAy/lzooKeflTnSFOzQ4GiJqbGIJ2/ggNHJ/iJ4BwSWOl8FmteDYQHwk\r\n/pUUl83c4a/ularMNEpK36TLy0vcogbUkDs=\r\n=HVZs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "cc856f3cc847a69c31e92ceb2c6527e1d30a9511", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "", "directories": {}, "_nodeVersion": "19.1.0", "dependencies": {"@webassemblyjs/ast": "1.11.5", "@webassemblyjs/wasm-gen": "1.11.5", "@webassemblyjs/helper-buffer": "1.11.5", "@webassemblyjs/helper-wasm-bytecode": "1.11.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.11.5"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.11.5_1681629280669_0.7850328274648439", "host": "s3://npm-registry-packages"}}, "1.11.6": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.11.6", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.11.6", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "ff97f3863c55ee7f580fd5c41a381e9def4aa577", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.11.6.tgz", "fileCount": 5, "integrity": "sha512-LPpZbSOwTpEC2cgn4hTydySy1Ke+XEu+ETXuoyvuyezHO3Kjdu90KK95Sh9xTbmjrCsUwvWwCOQQNta37VrS9g==", "signatures": [{"sig": "MEUCICzd3ynlKnEvtbNOSii5/cYRshDxWxCR4drBsCcWDeT+AiEA/CA20By3RkfZxCdDkhb6Hrq/Cv46sbyRBjXYERUUUHs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10868}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "58d40904ea7de2dd17f6f8d894ebe611b812a4db", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "", "directories": {}, "_nodeVersion": "19.1.0", "dependencies": {"@webassemblyjs/ast": "1.11.6", "@webassemblyjs/wasm-gen": "1.11.6", "@webassemblyjs/helper-buffer": "1.11.6", "@webassemblyjs/helper-wasm-bytecode": "1.11.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.11.6"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.11.6_1683645053514_0.8122084082797609", "host": "s3://npm-registry-packages"}}, "1.12.1": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.12.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.12.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "3da623233ae1a60409b509a52ade9bc22a37f7bf", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.12.1.tgz", "fileCount": 10, "integrity": "sha512-Jif4vfB6FJlUlSbgEMHUyk1j234GTNG9dBJ4XJdOySoj518Xj0oGsNi59cUQF4RRMS9ouBUxDDdyBVfPTypa5g==", "signatures": [{"sig": "MEUCIQDH7fjlLaGLIc2gKejZpYD/GuDQhQeSgK4u/HP3DOTwrAIgfD3M46J6y0VHjmkWYiFCyronkC8tN80FqZj3IzQzFGk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19727}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "67c75cf2da65e5588b5e81c10b0d7692d174760b", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "lerna/5.1.6/node@v18.18.2+x64 (linux)", "description": "", "directories": {}, "_nodeVersion": "18.18.2", "dependencies": {"@webassemblyjs/ast": "1.12.1", "@webassemblyjs/wasm-gen": "1.12.1", "@webassemblyjs/helper-buffer": "1.12.1", "@webassemblyjs/helper-wasm-bytecode": "1.11.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.12.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.12.1_1710325160130_0.8866582472877464", "host": "s3://npm-registry-packages"}}, "1.13.1": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.13.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.13.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "3f7b438d4226f12fba60bf8e11e871343756f072", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.13.1.tgz", "fileCount": 10, "integrity": "sha512-lcVNbrM5Wm7867lmbU61l+R4dU7emD2X70f9V0PuicvsdVUS5vvXODAxRYGVGBAJ6rWmXMuZKjM0PoeBjAcm2A==", "signatures": [{"sig": "MEUCIHFp+Ego6dhRg9m9tKuh+Jf3Fc7rRFj573UJzivGAi0kAiEA/nxB4I1h+euOcb/qy0OMXXRgpHQxqrT76jVPWAyv3Ec=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19727}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "cfe35c57093d414839b9350398369b78d97815b4", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "lerna/5.1.6/node@v21.7.1+x64 (linux)", "description": "", "directories": {}, "_nodeVersion": "21.7.1", "dependencies": {"@webassemblyjs/ast": "1.13.1", "@webassemblyjs/wasm-gen": "1.13.1", "@webassemblyjs/helper-buffer": "1.13.1", "@webassemblyjs/helper-wasm-bytecode": "1.12.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.13.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.13.1_1730912132400_0.14368797954239398", "host": "s3://npm-registry-packages"}}, "1.13.2": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.13.2", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-wasm-section@1.13.2", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "9513ce6383995a567969840511b185a7eab659b2", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.13.2.tgz", "fileCount": 10, "integrity": "sha512-3fBtdOALDFVZwbKJfLRomUWPAFx99VVndh+VqfJ+GqFrys1zwg2t5zeNTmuo2j9MiNBnmwGH8LzsGBLiRDcHQQ==", "signatures": [{"sig": "MEQCIDQKyqfVQ77M5e/0+xA1Cp7up4lr68CYAiV6lGoeiPX5AiBp6pcssASUvm7AXwNDbVxLPJSNu8RjHtn2IkKIYw8dJA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19727}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "897aeb784f042a46a00626f1d1cca96159aa5db3", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "lerna/5.1.6/node@v21.7.1+x64 (linux)", "description": "", "directories": {}, "_nodeVersion": "21.7.1", "dependencies": {"@webassemblyjs/ast": "1.13.2", "@webassemblyjs/wasm-gen": "1.13.2", "@webassemblyjs/helper-buffer": "1.13.2", "@webassemblyjs/helper-wasm-bytecode": "1.13.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/wasm-parser": "1.13.2"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wasm-section_1.13.2_1730929438024_0.8555757717177985", "host": "s3://npm-registry-packages"}}, "1.14.1": {"name": "@webassemblyjs/helper-wasm-section", "version": "1.14.1", "description": "", "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/xtuc/webassemblyjs.git"}, "publishConfig": {"access": "public"}, "author": {"name": "<PERSON>"}, "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.14.1", "@webassemblyjs/helper-buffer": "1.14.1", "@webassemblyjs/helper-wasm-bytecode": "1.13.2", "@webassemblyjs/wasm-gen": "1.14.1"}, "devDependencies": {"@webassemblyjs/wasm-parser": "1.14.1"}, "gitHead": "25d52b1296e151ac56244a7c3886661e6b4a69ea", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "homepage": "https://github.com/xtuc/webassemblyjs#readme", "_id": "@webassemblyjs/helper-wasm-section@1.14.1", "_nodeVersion": "21.7.1", "_npmVersion": "lerna/5.1.6/node@v21.7.1+x64 (linux)", "dist": {"integrity": "sha512-ds5mXEqTJ6oxRoqjhWDU83OgzAYjwsCV8Lo/N+oRsNDmx/ZDpqalmrtgOMkHwxsG0iI//3BwWAErYRHtgn0dZw==", "shasum": "9629dda9c4430eab54b591053d6dc6f3ba050348", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.14.1.tgz", "fileCount": 10, "unpackedSize": 19727, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCbZsdf52JPJQNW1CqGdDCilLIi/5UOdIRUc9xTAY4fZgIhAPDIZamKAADwCGPIPQTbcHRrrlQ0qsfcuv67q1I14IcD"}]}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/helper-wasm-section_1.14.1_1730930020121_0.19481711903540289"}, "_hasShrinkwrap": false}}, "time": {"created": "2018-03-05T09:19:22.539Z", "modified": "2024-11-06T21:53:40.522Z", "1.1.0": "2018-03-05T09:19:22.661Z", "1.1.1": "2018-03-05T10:27:04.453Z", "1.1.2-y.0": "2018-03-05T16:16:22.837Z", "1.1.2-y.1": "2018-03-08T18:10:17.946Z", "1.1.2-y.2": "2018-03-09T07:58:01.860Z", "1.1.2-y.3": "2018-03-09T08:09:11.566Z", "1.1.2-y.4": "2018-03-09T08:15:41.954Z", "1.1.2-y.5": "2018-03-09T10:18:40.135Z", "1.1.2-y.6": "2018-03-09T10:29:16.988Z", "1.1.2-y.7": "2018-03-09T17:07:31.543Z", "1.1.2-y.8": "2018-03-12T10:22:39.784Z", "1.1.2-y.9": "2018-03-12T11:05:03.543Z", "1.1.2-y.10": "2018-03-12T16:12:19.885Z", "1.2.0": "2018-03-15T07:35:23.603Z", "1.2.1": "2018-03-15T12:49:35.682Z", "1.2.2": "2018-03-29T11:47:38.399Z", "1.2.3": "2018-04-07T10:26:52.128Z", "1.2.4": "2018-04-13T10:31:34.470Z", "1.2.5": "2018-04-13T17:07:05.358Z", "1.2.6": "2018-04-17T15:19:59.166Z", "1.2.7": "2018-04-30T14:43:18.186Z", "1.2.8": "2018-05-02T17:24:47.965Z", "1.3.0": "2018-05-04T13:31:01.269Z", "1.3.1": "2018-05-07T18:09:03.200Z", "1.3.2": "2018-05-08T18:47:14.741Z", "1.3.3": "2018-05-09T07:53:08.932Z", "1.4.0": "2018-05-09T14:58:25.927Z", "1.4.1": "2018-05-10T19:44:45.360Z", "1.4.2": "2018-05-11T14:13:04.253Z", "1.4.3": "2018-05-12T09:21:06.020Z", "1.5.0": "2018-05-14T16:47:06.931Z", "1.5.1": "2018-05-16T11:24:43.129Z", "1.5.2": "2018-05-17T12:50:27.305Z", "1.5.3": "2018-05-21T11:08:23.785Z", "1.5.4": "2018-05-21T12:57:10.643Z", "1.5.5": "2018-05-24T08:02:08.295Z", "1.5.6": "2018-05-24T14:21:44.269Z", "1.5.7": "2018-05-25T12:54:35.099Z", "1.5.8": "2018-05-28T12:49:11.740Z", "1.5.9": "2018-05-29T13:13:36.804Z", "1.5.10": "2018-06-01T13:21:18.942Z", "1.5.11": "2018-06-06T08:58:36.972Z", "1.5.12": "2018-06-07T09:25:50.315Z", "1.5.13": "2018-06-30T13:45:00.989Z", "1.6.0": "2018-07-16T09:05:43.802Z", "1.7.0-0": "2018-07-18T12:58:37.058Z", "1.7.1-0": "2018-07-18T13:09:50.687Z", "1.6.1": "2018-07-18T13:51:12.424Z", "1.7.0-1": "2018-07-18T15:00:52.120Z", "1.7.0-2": "2018-07-18T15:16:24.199Z", "1.7.0-3": "2018-07-18T19:13:52.131Z", "1.7.0": "2018-07-19T16:01:28.997Z", "1.7.1": "2018-07-19T16:32:11.927Z", "1.7.2-0": "2018-07-19T18:10:19.090Z", "1.7.2-1": "2018-07-19T18:25:19.184Z", "1.7.2": "2018-07-19T18:34:03.735Z", "1.7.3": "2018-07-23T07:02:52.587Z", "1.7.4": "2018-07-24T07:02:43.889Z", "1.7.5": "2018-08-16T14:54:38.211Z", "1.7.6": "2018-09-10T14:17:51.986Z", "1.7.7": "2018-09-19T05:39:37.809Z", "1.7.8": "2018-09-21T07:38:15.800Z", "1.7.9": "2018-10-18T16:43:20.446Z", "1.7.10": "2018-10-23T09:37:50.700Z", "1.7.11": "2018-10-30T18:01:27.922Z", "1.8.0": "2018-12-11T07:24:47.368Z", "1.8.1": "2019-01-15T08:21:43.745Z", "1.8.2": "2019-02-14T16:22:13.198Z", "1.8.3": "2019-02-18T08:37:34.972Z", "1.8.4": "2019-02-19T17:27:49.557Z", "1.8.5": "2019-02-24T10:43:17.457Z", "1.9.0": "2020-02-01T23:20:19.036Z", "1.9.1": "2020-10-03T20:08:13.542Z", "1.10.0": "2021-01-07T16:18:37.752Z", "1.10.1": "2021-01-07T16:45:34.127Z", "1.11.0": "2021-01-07T18:11:54.290Z", "1.11.1": "2021-07-05T08:22:23.834Z", "1.11.3": "2022-07-02T11:52:58.239Z", "1.11.5": "2023-04-16T07:14:40.793Z", "1.11.6": "2023-05-09T15:10:53.652Z", "1.12.1": "2024-03-13T10:19:20.289Z", "1.13.1": "2024-11-06T16:55:32.606Z", "1.13.2": "2024-11-06T21:43:58.173Z", "1.14.1": "2024-11-06T21:53:40.371Z"}, "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/xtuc/webassemblyjs#readme", "repository": {"type": "git", "url": "git+https://github.com/xtuc/webassemblyjs.git"}, "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "readme": "ERROR: No README data found!", "readmeFilename": ""}