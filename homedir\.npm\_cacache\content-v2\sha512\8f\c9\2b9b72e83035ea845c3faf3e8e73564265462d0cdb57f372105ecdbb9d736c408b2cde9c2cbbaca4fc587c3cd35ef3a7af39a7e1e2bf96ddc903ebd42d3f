{"_id": "json-schema", "_rev": "26-6e174cac846e644938fdcc401705fee2", "name": "json-schema", "description": "JSON Schema validation and specifications", "dist-tags": {"latest": "0.4.0"}, "versions": {"0.2.0": {"name": "json-schema", "version": "0.2.0", "author": {"name": "<PERSON>"}, "description": "JSON Schema validation and specifications", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "keywords": ["json", "schema"], "licenses": [{"type": "AFLv2.1", "url": "http://trac.dojotoolkit.org/browser/dojo/trunk/LICENSE#L43"}, {"type": "BSD", "url": "http://trac.dojotoolkit.org/browser/dojo/trunk/LICENSE#L13"}], "repository": {"type": "git", "url": "http://github.com/kriszyp/json-schema"}, "directories": {"lib": "./lib"}, "main": "./lib/validate.js", "devDependencies": {"vows": "*"}, "scripts": {"test": "echo TESTS DISABLED vows --spec test/*.js"}, "_id": "json-schema@0.2.0", "dist": {"shasum": "c17432e27bfedeef39c72c8e5cdc7243f46f70d7", "tarball": "https://registry.npmjs.org/json-schema/-/json-schema-0.2.0.tgz", "integrity": "sha512-RlaWU1QWYlw/z3pR5oNbSaGnIi1M/67cKdRXQuJ82zrb3jO0UtLUzR28kpCDg0bdKukW2hi1y72OTb25IaxXTw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGpyj/jlqwTvaIvJlto5hnrrEIQxVuZ3CvvsknJpvlLFAiEAllKaqCriTWEvdcagFSXxR5adjnhyr5BNKjuM+Qc/VQM="}]}}, "0.2.2": {"name": "json-schema", "version": "0.2.2", "author": {"name": "<PERSON>"}, "description": "JSON Schema validation and specifications", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "keywords": ["json", "schema"], "licenses": [{"type": "AFLv2.1", "url": "http://trac.dojotoolkit.org/browser/dojo/trunk/LICENSE#L43"}, {"type": "BSD", "url": "http://trac.dojotoolkit.org/browser/dojo/trunk/LICENSE#L13"}], "repository": {"type": "git", "url": "http://github.com/kriszyp/json-schema"}, "directories": {"lib": "./lib"}, "main": "./lib/validate.js", "devDependencies": {"vows": "*"}, "scripts": {"test": "echo TESTS DISABLED vows --spec test/*.js"}, "_id": "json-schema@0.2.2", "dist": {"shasum": "50354f19f603917c695f70b85afa77c3b0f23506", "tarball": "https://registry.npmjs.org/json-schema/-/json-schema-0.2.2.tgz", "integrity": "sha512-DFzwwaFNKI+cfMyHtcyGA6N9d8jOWEtLc9IrT7WbpZc1V8M5S/HMBjJ3aM2Fx40bcTL2xTuIX/bBr8Zj2soGyg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDis5MTP7qMpbi1cKuqgaXTEITvJL2nrGyr74v2kjqaCgIgPymYJIrpm8ETD8LlpDYdgL7hQOzXSWzesZsTtXHkPM8="}]}, "_npmVersion": "1.1.59", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.2.3": {"name": "json-schema", "version": "0.2.3", "author": {"name": "<PERSON>"}, "description": "JSON Schema validation and specifications", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "keywords": ["json", "schema"], "licenses": [{"type": "AFLv2.1", "url": "http://trac.dojotoolkit.org/browser/dojo/trunk/LICENSE#L43"}, {"type": "BSD", "url": "http://trac.dojotoolkit.org/browser/dojo/trunk/LICENSE#L13"}], "repository": {"type": "git", "url": "git+ssh://**************/kriszyp/json-schema.git"}, "directories": {"lib": "./lib"}, "main": "./lib/validate.js", "devDependencies": {"vows": "*"}, "scripts": {"test": "echo TESTS DISABLED vows --spec test/*.js"}, "gitHead": "07ae2c618b5f581dbc108e065f4f95dcf0a1d85f", "bugs": {"url": "https://github.com/kriszyp/json-schema/issues"}, "homepage": "https://github.com/kriszyp/json-schema#readme", "_id": "json-schema@0.2.3", "_shasum": "b480c892e59a2f05954ce727bd3f2a4e882f9e13", "_from": ".", "_npmVersion": "3.8.9", "_nodeVersion": "6.1.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "b480c892e59a2f05954ce727bd3f2a4e882f9e13", "tarball": "https://registry.npmjs.org/json-schema/-/json-schema-0.2.3.tgz", "integrity": "sha512-a3xHnILGMtk+hDOqNwHzF6e2fNbiMrXZvxKQiEv2MlgQP+pjIOzqAmKYD2mDpXYE/44M7g+n9p2bKkYWDUcXCQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDeoJZeMXAZk/5jEeAOXWGL1/iA+t1RKuaXDTbxtIpuAAIgYlDIsHsgX0eV5T7feg2UU8KEN+eIP1B2dU59AeoU4OM="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/json-schema-0.2.3.tgz_1473699189380_0.7420965158380568"}}, "0.2.4": {"name": "json-schema", "version": "0.2.4", "author": {"name": "<PERSON>"}, "description": "JSON Schema validation and specifications", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "keywords": ["json", "schema"], "license": "(AFL-2.1 OR BSD-3-Clause)", "repository": {"type": "git", "url": "git+ssh://**************/kriszyp/json-schema.git"}, "directories": {"lib": "./lib"}, "main": "./lib/validate.js", "devDependencies": {"vows": "*"}, "scripts": {"test": "echo TESTS DISABLED vows --spec test/*.js"}, "gitHead": "6b8b8cadb5802de44acbb261c3dece147472ebfb", "bugs": {"url": "https://github.com/kriszyp/json-schema/issues"}, "homepage": "https://github.com/kriszyp/json-schema#readme", "_id": "json-schema@0.2.4", "_nodeVersion": "12.9.1", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-Y+abRmOZ+Er9FEEw7d/VACLgSASqjdaPBsJXdVHKnIbJdHq/62Wv5x2Ndh706je7cZfX/qLpfPT4prkbpru71w==", "shasum": "66f1dd303e8672dac684b246cb17859c841f5bb5", "tarball": "https://registry.npmjs.org/json-schema/-/json-schema-0.2.4.tgz", "fileCount": 5, "unpackedSize": 25747, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdcc83CRA9TVsSAnZWagAA5jIP+gK9PPLj9adFOS5ceQGt\nAIXaB2X9KBCVZ/jUt5BnoLLdR1ruZninNgtdcJ9ZJVjFeoRw4BvM1MEDjZj/\npZdKdQjVv1DsBP2rb6pSGlacHaENctlPHuiFf+PFCeO6TYNmt3YIc99TazFf\np2ruF96vglHteGMwOAeULf8DVBOSApnL5AhF8FpenMM9BTYIXPJJh3bSSITU\nkzvO9XeAJtXr5J5cmHnHVrQuFT5NxPpsson8LbDBRtUI4v029/J4jfITX2s7\n0Mh2houFVYwii0AjzLOWKU7QAfbYIzRfh35sERCRh3Uv0g1/usCYZfiUZmyn\nLagOhZCz/mTEL+APQ6eEIGzUzImgiTWxdIacnPhtmoIA7tVAhUSwHRDXfTng\nmh0HBfapO8XJIquN5QwhArtYmlvjkKf/VnAZx/b7aEBReRpXYKFbRXnOAz2C\nUHD/P7hFld6+fbwPzaugVZrRX5vK6lBkGUGx9u4P5PfweHgYXrzycQt7MECo\nvdg7aRUp8dAsHBoN/rUc1X7o0Li5ofOtBXs55BrpkMAUuqeagDRudvZvvVAb\nzviXpNL5Zu/2IeqGH7lyBdxn3PsLnzW0O3H04a+F1Os8AaETKgg1EEG61pL4\nC8IfaqM9FMF+800U1zOjxfk6EJmNtQkGi9HALz/F7fED2XvRdy+pUCgeCOMt\nGzlU\r\n=06/T\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBc54nplHQmH4xN6mxGjJXIiWpYhqVVI+hkXR61hTMJJAiEAsPMH6gFLpbEACIdR1d4Pxo+isTk92hoCh4TkNTieXg4="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/json-schema_0.2.4_1567739703096_0.7037460014130876"}, "_hasShrinkwrap": false}, "0.2.5": {"name": "json-schema", "version": "0.2.5", "author": {"name": "<PERSON>"}, "description": "JSON Schema validation and specifications", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "keywords": ["json", "schema"], "license": "(AFL-2.1 OR BSD-3-Clause)", "repository": {"type": "git", "url": "git+ssh://**************/kriszyp/json-schema.git"}, "directories": {"lib": "./lib"}, "main": "./lib/validate.js", "devDependencies": {"vows": "*"}, "scripts": {"test": "echo TESTS DISABLED vows --spec test/*.js"}, "gitHead": "1e4c8aa55cc7c44105d87031368b6003637d9ef7", "bugs": {"url": "https://github.com/kriszyp/json-schema/issues"}, "homepage": "https://github.com/kriszyp/json-schema#readme", "_id": "json-schema@0.2.5", "_nodeVersion": "12.9.1", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-gWJOWYFrhQ8j7pVm0EM8Slr+EPVq1Phf6lvzvD/WCeqkrx/f2xBI0xOsRRS9xCn3I4vKtP519dvs3TP09r24wQ==", "shasum": "97997f50972dd0500214e208c407efa4b5d7063b", "tarball": "https://registry.npmjs.org/json-schema/-/json-schema-0.2.5.tgz", "fileCount": 5, "unpackedSize": 25605, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdcdMhCRA9TVsSAnZWagAATqYP/0pNXcnFLyHO5aJ00uC+\nTCOVSSOp7/BcffyX+GSvM065jKt6rfL9qJM+mNKs+mOkiPMV0ics5PTJxAnQ\nerq0uEEcwN2AtK/62S1Z583DbDwuD+m9GTUIBKQARdwBRs9ChaDrAXfV2rYI\nr2XygkVdvZTu2ZLOb1FurMsJ69EP5Lv5Nb0R+7sDvglhrLEXHDgRzkpHSBA0\nk2ATuRc75iBMucHq+d5hIM6pU3RorgTDGtWrBE0tHkh/lrDtMSyhM15vZmTo\n49iHc1tCpinJsnlMmtFzGcIcuSOwvwxCFlJeKGL36cfyh7+kK9k8Hl1WU4xP\nlyqmfP5EOdZ4X3Z3FrzvYhVA4SqQ1SfuODOhmSh1uVVfVoENIbeh5bIwfMN5\nAe/6hEi+FQOvuQYv6Xbph+viWCvrRYondHDOn1xDPNSZHCyvxJiChAaGgdtq\nAc9wTeBbNrLOAT7Qje8z4V4xZPI3BJdjuGAZrHyqCVgIpss3U/gnwQyRol3V\nJtQETfxsVlLRsDpQc7wEAdnPbbrR1jV9H30m9uxtI1+QQxP7Jl0Gbs4RDPvq\niNQWoWAMYvy4GlakHc8m8L8Ew9VqsOIi2VLPrQKzQDJANOC0N+YQUBv05nSE\nc4hd2xxgIruPpg/Ic2OyElDkII398K34QkL4CHpIZelQAmfSkZnUh1cpUSQz\nDOXE\r\n=wo3l\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCHzRjqfoM5wAECf2KWSokGdChHJJwXuySkLD+iGS/vesCIQDHBqbpLyjKhtXNRXOAX08lhHFKc58KPFpvuWqXfoozTQ=="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/json-schema_0.2.5_1567740705040_0.3362981539498242"}, "_hasShrinkwrap": false}, "0.3.0": {"name": "json-schema", "version": "0.3.0", "author": {"name": "<PERSON>"}, "description": "JSON Schema validation and specifications", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "keywords": ["json", "schema"], "license": "(AFL-2.1 OR BSD-3-Clause)", "repository": {"type": "git", "url": "git+ssh://**************/kriszyp/json-schema.git"}, "directories": {"lib": "./lib"}, "main": "./lib/validate.js", "devDependencies": {"vows": "*"}, "scripts": {"test": "echo TESTS DISABLED vows --spec test/*.js"}, "gitHead": "3b0cec3042a5aac5c967fd43475f5edc4c5b6eff", "bugs": {"url": "https://github.com/kriszyp/json-schema/issues"}, "homepage": "https://github.com/kriszyp/json-schema#readme", "_id": "json-schema@0.3.0", "_nodeVersion": "15.4.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-TYfxx36xfl52Rf1LU9HyWSLGPdYLL+SQ8/E/0yVyKG8wCCDaSrhPap0vEdlsZWRaS6tnKKLPGiEJGiREVC8kxQ==", "shasum": "90a9c5054bd065422c00241851ce8d59475b701b", "tarball": "https://registry.npmjs.org/json-schema/-/json-schema-0.3.0.tgz", "fileCount": 5, "unpackedSize": 25940, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf795WCRA9TVsSAnZWagAAsT4QAJucAnv8y2ctCH4fC36E\nN9hhTnhzb0bW2qD8OX5wRGRwXQnHkjswfHBIqninRAVXQMYoieMCVw3FbdWw\nRV3Bzf9B5aZMT4TX0wD1J0ZnfY3OlmweO9hs7AFDRnoQ7HSOX8gapJSFxH7k\nUBErWzfeT/djBRiD7j2YWGSSfqln4+siYfW+o/IgDIcijHt0EWTgk6OV+rUo\nNXahaIpCBcl1sa8N/AJNd4lZgMaGVvdlaFcC0UwEenmq4kN+i7Mbasdk3ICe\nDabALjE1/C79xlI2sAY/IzDtS5arGT90hzDIzQv6l3xOIoEHhKM2U53y9yk2\nbLnJWx9m2OYzuw7yC8+QqNDg4HtpAxFn/sbAdQfv+cZS7iJ8NtTBM0q5gfIe\ndxzW3ZvKpuWuGzdiqK/IvGvIp0fqgNff3+BCJ9v9WkbSNL1BWenTt8lCJIR3\n4fasVMp+oeMBFJOBo3xkhS65H9K2mvw/vPE+Q94ErH6Q+3F/M2d/b/G1/kIp\nc+9z+ysi7WL3QEyp8qPQF+sT/C+xywWK9z2TNTUoyXkK+SIN9tI2qb6ghans\nRbHKlE5olcOeQ9KkFwU/346zs/bib6XrUQkotmxBTDLSeFCP4mbfCldkpRg7\n91+KDWChFkCWJFVzqmxoY9SYetxf1d6QPyTwTwkhAq/5PIyCYal6GOUIQnYC\nYuXt\r\n=wMn4\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDT+65DVT80o0st050dYjzeXI/hbz9MOuEqrlyPymBM9gIhAK6zcUaXWSComlv/msBmF5OU14NuJwQPRTqhVK4Vl1Y4"}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/json-schema_0.3.0_1609555541470_0.4721096844310866"}, "_hasShrinkwrap": false}, "0.4.0": {"name": "json-schema", "version": "0.4.0", "author": {"name": "<PERSON>"}, "description": "JSON Schema validation and specifications", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "keywords": ["json", "schema"], "license": "(AFL-2.1 OR BSD-3-Clause)", "repository": {"type": "git", "url": "git+ssh://**************/kriszyp/json-schema.git"}, "directories": {"lib": "./lib"}, "main": "./lib/validate.js", "devDependencies": {"vows": "*"}, "scripts": {"test": "vows --spec test/*.js"}, "gitHead": "f6f6a3b02d667aa4ba2d5d50cc19208c4462abfa", "bugs": {"url": "https://github.com/kriszyp/json-schema/issues"}, "homepage": "https://github.com/kriszyp/json-schema#readme", "_id": "json-schema@0.4.0", "_nodeVersion": "16.9.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-es94M3nTIfsEPisRafak+HDLfHXnKBhV3vU5eqPcS3flIWqcxJWgXHXiey3YrpaNsanY5ei1VoYEbOzijuq9BA==", "shasum": "f7de4cf6efab838ebaeb3236474cbba5a1930ab5", "tarball": "https://registry.npmjs.org/json-schema/-/json-schema-0.4.0.tgz", "fileCount": 5, "unpackedSize": 26079, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2wvUCRA9TVsSAnZWagAA1dwQAI5dKEjw+FfNeDtt+n/V\njfL5tMVIuxErXme517oLMxrXdGxiyMQblE8KWpHOCG70IMOnEsXWbvzD9Pdo\n12JnfcI3qz5VvJyyNeNVdCIfXZzflG2f6+Io0YK+JvGMd7yjmiy0n+saLytc\nHC7jQQBQN/IOjAvJOudKx6rARIWfmb8lY7oapx15lxwtwlti2y9FtMt5yrbf\nGjoNKQUaduKv2rKFJrpSrrQmkrr8sPa0BH6Vv2bGiTwB28w5NV5wmSSXOvq1\nuo3dgWWSkZbW4xJ0rtHPFvGaDVuQpYwLUlhraUvdR6Yo41Uoqv6Wx+A1TVuy\n6bfGhyJDBZxagVL54sZ/QSKbMZAqMKCbh9JW20Jp74OAJMy0U8WgNVFxME1w\nQwz2Tx/7WRunSWRlzxZNuQ68zGYRiYuQCtcuUb3QG5AnwL2wboUPecBpSQZm\nxh11lZJSYP4EjibvIQz/KY7t+CNaqgahyHO549k0SSIiP3DO5n/6J+jSkPCs\nZSq7gBZfcez52SkO1o3jFoMZ4+Ij40iOiNsmAAYQ1yocHbLcD7hTVC4urHI+\nY514uV0uq0vu5ZL6/u6/3YCQMLs7Ls+MTuuX7bjLJzWx7dxlNWj4WzR5yBp0\n4lef7LVhj20oGdINMx61C4xxTZSirsISbbC6iLRCbvNlECVGYSruolqC/836\nRnSL\r\n=yci1\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCNi4WC9rmGGCDlps45dMdKO4jwoZ9HO3+j6/sGoKJ9nQIhAPiOFDPEU0W+HmYun3xka4Fc+ZZtAxPhlWtdtQqxnhVv"}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/json-schema_0.4.0_1636423448426_0.031203107211557946"}, "_hasShrinkwrap": false}}, "readme": "This is a historical repository for the early development of the JSON Schema specification and implementation. This package is considered \"finished\": it holds the earlier draft specification and a simple, efficient, lightweight implementation of the original core elements of JSON Schema. This repository does not house the latest specifications nor does it implement the latest versions of JSON Schema. This package seeks to maintain the stability (in behavior and size) of this original implementation for the sake of the numerous packages that rely on it. For the latest JSON Schema specifications and implementations, please visit the [JSON Schema site](https://json-schema.org/) (or the [respository](https://github.com/json-schema-org/json-schema-spec)).\r\n\r\nCode is licensed under the AFL or BSD 3-Clause license.\r\n", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2022-06-19T06:23:19.805Z", "created": "2012-07-06T21:43:58.134Z", "0.2.0": "2012-07-06T21:43:59.303Z", "0.2.2": "2012-08-23T19:40:16.761Z", "0.2.3": "2016-09-12T16:53:11.378Z", "0.2.4": "2019-09-06T03:15:03.219Z", "0.2.5": "2019-09-06T03:31:45.173Z", "0.3.0": "2021-01-02T02:45:41.618Z", "0.4.0": "2021-11-09T02:04:08.632Z"}, "author": {"name": "<PERSON>"}, "repository": {"type": "git", "url": "git+ssh://**************/kriszyp/json-schema.git"}, "users": {"csardi": true, "syzer": true, "evan.lecompte": true, "ddffx": true, "mxlpitelik": true, "cwagner": true, "mojaray2k": true, "natarajanmca11": true, "kakaman": true, "kentaroutakeda": true}, "homepage": "https://github.com/kriszyp/json-schema#readme", "keywords": ["json", "schema"], "bugs": {"url": "https://github.com/kriszyp/json-schema/issues"}, "readmeFilename": "README.md", "license": "(AFL-2.1 OR BSD-3-Clause)"}