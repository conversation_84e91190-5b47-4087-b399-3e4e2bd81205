{"name": "json-parse-even-better-errors", "dist-tags": {"latest": "4.0.0"}, "versions": {"2.0.0": {"name": "json-parse-even-better-errors", "version": "2.0.0", "devDependencies": {"tap": "^14.6.5"}, "dist": {"shasum": "20adeb0d829d7a576c8b14487546fb4998420f0a", "tarball": "https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-2.0.0.tgz", "fileCount": 5, "integrity": "sha512-2tmDEuXncqeXo2KX6DHJKs0X8nHu+zezlp8wFmpEgQSpdKuqRsXYF33qW3OSbLQbKveL/5jcFCQVD1jD11TCNg==", "signatures": [{"sig": "MEUCIHBI5mGwuC51XwydxuINeRORomLX2krB10zLUUaJs5uFAiEAo0Mn7AR4ZLYOx63g3+B7RFC72vMsAcc/niBX5IvlrTk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7268, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdjmryCRA9TVsSAnZWagAARjAQAISRs8pPgEAMGOeyTiFy\nHUkzJn/jwkM6Yzv8gPaYSfxd6isIRPjW+bXfh5c59xEoFUfzJOiqpNp+DOEU\nHq1WfeKXcewxKJJevmlu4XZttW2pke79zmdixhI1gDasKzYeiSWVIn2jaKks\nUPZCAp+xOkDoK0egXsltdA9bjwdEH7uA5NqDHlp/c6DKXFgPAnlNg/cN5C6R\nP6e01Hd3V/lENVTFtNOwFbS6kq6ljR0SSKE+rSqDwKk1qC0KJYnRPDC79Ec0\nSvF0FHOpgHwlrKogv1XZS+LoC/h8TqYfyJZaZ94WBGAo02chRBlmek8pKcrB\nKNBpkUydhWpml08loSVW6haxGXw0fe2xqG9DjgRmUchxJU/IeiS3796YsKr9\nx1XsyqWvTkQKrufliid2nkX1UQOJ1I89IJkvPhBU2k+E37k8tHsDObqF1SHK\nPDKBqpTvHaQLW5Nhx4Gc29p8crvrGEidmPTNqMB9wx1JR7pwDQvjo6XLgeCu\nxmPjr980ScI/PRApPPz+zvfyUrmu0x2tcW7Tp6AdWw/1vInPQEiicnbNC854\nq0q2g6kHKEi6SHbV2bQbt0wjm2tSe0CuNuPYLizQa/v95PDdqAw9UFMkDqUy\nG0dYmcB5RJxILpFMwgmWUJ5DpAZVXZC2ILGHF6ggpKFOq1M4VjjD/znnD+nP\nd+2t\r\n=lDGc\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.1": {"name": "json-parse-even-better-errors", "version": "2.0.1", "devDependencies": {"tap": "^14.6.5"}, "dist": {"shasum": "ed0009e0f5e7eb21ae0675d0d34782cc7a53c60e", "tarball": "https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-2.0.1.tgz", "fileCount": 5, "integrity": "sha512-XFY2Mbnmg+8r7MRsxfArVkZcfjxGlF/NjM3LsPXVeCX/GBF/1FTCv+idHBYC4qLPtK7q8HC8bapLoWqnhP/bXw==", "signatures": [{"sig": "MEUCIQCzF0AqX+lgrmNyqWgWcILNh5G9yUE8HwA/hLM6Yjwu6gIgLXjD8zbFSv2j2egoIWOFqzZecrCr7ocKhTTndBm0rds=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7432, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdjm0oCRA9TVsSAnZWagAASJ4P/jcg4bfhCOSmTuW71wEK\nwbm9b+peLosC7BSADc1Sd4PRbLPIDrheDom8Pi6AwDJaEKv8trpxhBYR2ZGP\nPTikbQUNU3IeAkExyYLuPDfpvzuWvKaZUBPu8TnOn04J7k7Gh94xg8GSgK/O\nthpClgRLeEpQD7S5/GmAkNeJd6xZjq3FHT+2QTZAG5jZy6em8bttiUjufExc\nXq8nRPZxHkLZBB82A7Hw7XJXZvZjz3+WHBCmeYsLFqFiVMtToKarS7b/1m7+\nzsibK8lS4qbAdJcI0uG9kXbvFW/OPldU6E2JGXCc6TYZfMg/ttl81vLFJOII\n8nKjGZUlazPz9hDUmtuXRwHGYmduzM0J55fOG0eWGg6ucjdlpnnsp9EGfytj\njv1nS2yqYsNqiWOhkLFBiDD9Ds6Ia/3bKGObPIvNyfwMoSdBqVoPrK6K02k8\nZ/z8km+yYp4eVZGTcrunMudfw/1sWHnzjLp+I+SInQ94uuvFPu3RpT6I8oGM\nhDK78OL7B05AznoRkO5ZqCVMnucoJHjnRUdsI9tHTJmtlJbOZM+IppW8mS9E\nJYF17u/iCM5exZQ6u/29pxzMJU396nvC8o0pFzOBuuRdUqMRxtaFKGHECluJ\nZM7y3OmfjwSMGZo8giDGfPVNajB7l0/ilKErNwl+ksRYGnr+zfLC7ZNNj246\njfMY\r\n=pPyj\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.1.0": {"name": "json-parse-even-better-errors", "version": "2.1.0", "devDependencies": {"tap": "^14.6.5"}, "dist": {"shasum": "a72dffaa47924e65fbfe8368d0424bd686076b82", "tarball": "https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-2.1.0.tgz", "fileCount": 5, "integrity": "sha512-pbAhg+qUUH9J7DdiUoX4XstXDya6GWJGnb/08YWnhNiIJr5fvAcF6wPtIWZQyutcxrWR++1nAv9lmEIikQCfFw==", "signatures": [{"sig": "MEQCICQtzsUUm7LPg/5z+wgwPhfXx+G1Ngu/fo6qpI5jmAvTAiBYhL6lzBQHiOg5Tu+RwQxPUoK70qLRV3xZ659Y9MyGQw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8048, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeYJYSCRA9TVsSAnZWagAAT88P/Rv1j048oWuSuSYydC7g\n9mzNJSSJCBG6n7YyPW+lByXb8QFKGC+Nj8NTfaFMUkN9zUfkkfg+YFwGHFs9\nXoyU560XaVlUPElaqLm3TyzX6VCpJlhCR5ci73cNTRK2pxZULjfriKAP2HfE\n9qlne3tHfVvqgHCrLDQ+dnHdhZsRCxcpN6obKAYs90jzXn5WIq2uGVmDt45a\nvnABS8c3TQ2OdljVTDs3TGHjJdHLU26h30bijkKKAjR1r9exZOQO5lp+e3Kp\nJfC2XY4ujWE8f1iur7/8Lc3kMlk6ipiKp9Mxx6sq/0iFTPJh/htYV2fcXd0B\nGNwkMwQplEmSNQlXul0JtqjaK9vm8IIUKmuiRzhQ24wPSkbcgPEYyeOd2Q1k\ns/8SU106QfqSAI7MZkbTZL0akhPKZMDi/Df6mWeux3dw2jXSOgk5CbzdT6E4\n6VPe7g+jgbTWFqeO7WMVR1ZvWNfPoxBRhWPjpAGQ1HI9t6ajaUUjMdNsEtiQ\nrwt5LI+4Kp6OLuDB3+VwCJn8/SZDCnxn8qZdXFtBmx4yZPPF4R4prWZ4rkvX\n4HiHY2gXNQrQQs3Dh49z4kLYtVeifT0amWsaMSc4UloGlA1HC61tcSbUSPU1\nk0FiX2HQNsMpzc+5Gm1p+dzBQD6f+Kx/FE6ZKEAsv48OlPOqwEPuGQ0i0PwV\nF5uL\r\n=XQaz\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.2.0": {"name": "json-parse-even-better-errors", "version": "2.2.0", "devDependencies": {"tap": "^14.6.5"}, "dist": {"shasum": "304d29aa54bb01156a1328c454034ff0ac8a7bf4", "tarball": "https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-2.2.0.tgz", "fileCount": 5, "integrity": "sha512-2tLgY7LRNZ9Hd6gmCuBG5/OjRHQpSgJQqJoYyLLOhUgn8LdOYrjaZLcxkWnDads+AD/haWWioPNziXQcgvQJ/g==", "signatures": [{"sig": "MEUCICzN3TijqwrEBOz1MnoYDmqNBRwOdMqkbPhWPjjHhuxrAiEA5nSv7RcD5QqKgnkQKCB31WaUGQlXmNNqxxORG/DC304=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8560, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeYJo8CRA9TVsSAnZWagAAyGwP/3wvLDiBjHSwUIJWpn+A\nirgtv/gNew7NZNqyFWVOry/cah+3aowPsoVwq2iRvlXRGTx2pYMX2m1reONe\nCd4TW6Snlq1YvVuzG6FoU5hvmk+BNiUDFmI70vdLq4pxog9KYd1PCcqxTtQC\n+W6iSnoDK+ACcUFWENKjZvl3E24Xp8oSIjeh/TKHU4JcbJ43l0dV88E6YV9Y\nP7m14OB2p0objhE5YbBIwz06ZuXXyaZbzUxaMCVWsqMfZJRKzkoG9EfEcPnp\nZa7ZZG99Pe47QPNRfaQ5rUMAPcIJniGpFdF6ayUPiqGXkZ5RqgvtxTp9AU6+\nxIb0UGpXF8wDc5uSiOVGW+2d4ENDLAf8T1bknDj2+VQEawYs3GePQWl7fmcz\ncLORfy3wVoDmyGvkPZZK/nA6fHti8SUYUXnVVOMO/HlU19v3XeyPDUEPlR5v\nj3WVmScJtxktST5fZt9MpSOGz2kxU//hm7Z7EriRvFi4C7xTNAIJnlW8oakm\nWGoVU1s0z8SwuWQG9HE/DxE2Fsb8KHujyhvSn9uJx1StVNglvwKO7oaY2lUk\ngEhgffdn3YwTw+f4HhEsZrrFiU4ip2NR5xJfCY7T0vILPeezgv7FEzLHOsiy\nvREyvUdbB5Cs3RHFk+yOK3PjL9CaE1mfXC00uLFYEh/FJwm4V8EnTy3+MsED\nFCsr\r\n=K9xL\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.3.0": {"name": "json-parse-even-better-errors", "version": "2.3.0", "devDependencies": {"tap": "^14.6.5"}, "dist": {"shasum": "371873c5ffa44304a6ba12419bcfa95f404ae081", "tarball": "https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.0.tgz", "fileCount": 5, "integrity": "sha512-o3aP+RsWDJZayj1SbHNQAI8x0v3T3SKiGoZlNYfbUP1S3omJQ6i9CnqADqkSPaOAxwua4/1YWx5CM7oiChJt2Q==", "signatures": [{"sig": "MEUCIH/w2ZGesosLa0tk3iBHxHyCIo0ehpv15haLng4ck1+8AiEA92QvETYY9+JuH41g2iS1Ba3BTg++lI3m1PZ+9Ut1EcY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10119, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfPrrQCRA9TVsSAnZWagAAJAEP+gPHr1zmySw7YK2r8o0a\nn84wrKT34qIX6bIjWdbzpZZAMKpLnmU12x+YYfgHKw0EqMFK9hkmAcWpSzOY\nqo9bEpToJNOEa+yiamicM1D9tvk7vHBHAH0+JwT/lPOXUbiouxBmrUdJliiQ\nOQWy7XIB+TiASZgKHIfnybG1eR2dx0C7mFXNtLU+gzbd0lr2UHn/OLg0BTAS\ngpDZDFttV9nvD0BNogstbht5fSXRv68CB/uqnLGaoYnb37gHuiy377SqTkdW\nJVy7QxugPLv9QgFQhNRiEM2kvAzp6N6vXHkJ9ncZD46WumO3xkpHt5fcT7wK\nqa0xgj0+0tZfKjXoe0QPOfFP8GMv73u69GC6Jze3cKdX3oteIR9yN1iTVXq7\nJUwRLo8PyOn/BcEBVKcu9nu33Q/CzJdbcpgew8VSNq0q/m7jBOTkMhZhL96w\nKqWXVCEfXsDFNQ2yJIagnWac6Dg63MsmEL+biCIqfKoO/a4rd3DBmM7sHHbi\n57r+O4Zpd9JMWcbje2/ockDJKRZ55a/WIKprC6F4wDAdIZhlFoHk7q0YfI2k\nowCFpbUDjI14M8u+k5BbDD/WKw5Yt/QF+9Jun4S54ljhJJQpWcPipnACYvXE\nCefzJyoOBHQNlIsayS7rscEY6l/lyeiOchtytuNxktIwLbPDmAZKROR47qz+\nejWg\r\n=QOa/\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.3.1": {"name": "json-parse-even-better-errors", "version": "2.3.1", "devDependencies": {"tap": "^14.6.5"}, "dist": {"shasum": "7c47805a94319928e05777405dc12e1f7a4ee02d", "tarball": "https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz", "fileCount": 5, "integrity": "sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==", "signatures": [{"sig": "MEUCIQCtQU19VQxRee+8AoI3w6asS+a9CTxOLmvgksTrr1XiwwIgGo6adyI3xRD3r9QDog0JvHIeXZGSroBI4xvoiDshV6g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10426, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfT8pnCRA9TVsSAnZWagAAXmQP/jlDVVHaZveAjkQwCa2p\nATzj7ZQzyCxxYh2D4I3x1BQTY9T/6ds+7dB/8c6b+r9oSgQyC+5i0DjxQpOP\nkRV6jOQ4qlggEkYOoig0Zr0EpZOwB0E3GtMuwVlbGOB7rFM8Iu1cdkAIQTjq\nnQGYj5eQammyC405dlrEqEhWjCcgOY7B8u1hTnmyknyMnnzi6nWqHljZFxNi\neo1PlfhEnZz5AMT+1hSJq38sXuTfYAtXnKhtgWIyJeoLM/H+2XbJL6pCCHi7\ne0zaWqY4yjrCDSaV1Q+D6gqiFaX1iQMRTtCKGiTzuhHsRoGBsQ5S+uyptpqt\nlsTpnxP0XskPz9EnB8WN3eNIOfzhFjoaabGJwuE8KLYNze/VwuCNE6e+FGDZ\nikRVVHMkdHx5lo9MOrkEzp2ibUJm9fmnepFC0P/g1bOcIK+ooJVGIsH+wiaL\nwrv8nxU/DHdnuaEefE4AtWazo51xe/4AGFg8RAcUWWAncKx8zwXiDoHksfVn\nHM3V00vfD664I6JrvQpXp19AwC4ETfJm5W6eeTHOeuhAjBs63rIg0n7zS6kQ\n62hqjr9/0qM7+kF9tRuprNKARoTk8Xu89c08n5AvI36LiebEh0N0ODs3LtSz\nNVDalcYAU+4dMHAYtGMr1r1+iGXStdRo18uJvxGA2Mr71o2hqYCg2MUtOCLW\niQ+X\r\n=ET/d\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.0.0": {"name": "json-parse-even-better-errors", "version": "3.0.0", "devDependencies": {"tap": "^16.3.0", "@npmcli/template-oss": "4.5.1", "@npmcli/eslint-config": "^3.1.0"}, "dist": {"shasum": "2cb2ee33069a78870a0c7e3da560026b89669cf7", "tarball": "https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-3.0.0.tgz", "fileCount": 4, "integrity": "sha512-iZbGHafX/59r39gPwVPRBGw0QQKnA7tte5pSMrhWOW7swGsVvVTjmfyAV9pNqk8YGT7tRCdxRu8uzcgZwoDooA==", "signatures": [{"sig": "MEUCIBqIDbQtS67O3Gt98ftRyq5r34YqdcP362XANhzSKNDaAiEAzF4bN1VL0k41uAJ+Gg1kHB9MVjXbk6ZM9UVUWRW/Td8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9664, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRIPqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrcbQ/+NbzM7xZLOta7ExriwkuRVMYBWlMrBtfowmE4ypYEq1D/XkLy\r\njnLJckDxW455KxBVGf67XIFfkVuXzZ6ZHAHvGQiNPa7fh3HRXppCppwuaSg/\r\nSIn+6k2pLmB9zLH3ZCubQyBGVMzdnRoLWpjhbeQY3Qe4pDLt5b0JNxxo4pDY\r\n+avw0b+xqA9dldiHWM7Ww9IUPYC7CFkJpC/pwygI4u+isufBmSumKouZNJ2I\r\n8MzLnlLmyKMSvvAPX9vEGH6Gfqv253gISZ9ChKd72GrSqBpFm7B6RWbxip3a\r\nXGOqv9FGTPPjH8dvJ2boPGO/nG+MTsz28HHZn+aFDlQbutLC9Kq2/ic4d5Ao\r\nOv6ck0kFRgSI+m5Q1xSFinZ6c2ReP64BqhGJWJcj6fdbG/wjmxxU3+gTkdh/\r\nYhKLOsO80yvowduWVqh1eyirCNYV2SpHILuyi3aTHOEMV+6VP93d/X/ELYwM\r\nKC88JHDVHLjIPw7sQ8BZdNYDsI5lMC6zrU6A15/o80EcxfxBGsRBjqyiF+0i\r\nqkVBS6zWiPUlpWWSHudIJ35zmuA1UUD3QpoRDCpyU7FFuLKgpqDZN55Y5A9b\r\npRpAEWxN2EkkTxTAcCX87vCzfUFSw8yIH2HV/8s7BWOZ8agG/uBNlxUQ0gmE\r\nEKhp39pZasEBHq4UAIY3r15lHwi7UgANRsg=\r\n=efR1\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "3.0.1": {"name": "json-parse-even-better-errors", "version": "3.0.1", "devDependencies": {"tap": "^16.3.0", "@npmcli/template-oss": "4.20.0", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "02bb29fb5da90b5444581749c22cedd3597c6cb0", "tarball": "https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-3.0.1.tgz", "fileCount": 4, "integrity": "sha512-aatBvbL26wVUCLmbWdCpeu9iF5wOyWpagiKkInA+kfws3sWdBrTnsvN2CKcyCYyUrc7rebNBlK6+kteg7ksecg==", "signatures": [{"sig": "MEUCIQC3Mon7ihx4dRsukomYrwrtnMUMgsUf5bZAFvwHY+kWjAIgU8/9CCMgKXqWZhWNlHi4pktfSKcL/HSmWH5SjrTw5VE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/json-parse-even-better-errors@3.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9873}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "3.0.2": {"name": "json-parse-even-better-errors", "version": "3.0.2", "devDependencies": {"tap": "^16.3.0", "@npmcli/template-oss": "4.22.0", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "b43d35e89c0f3be6b5fbbe9dc6c82467b30c28da", "tarball": "https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-3.0.2.tgz", "fileCount": 4, "integrity": "sha512-fi0NG4bPjCHunUJffmLd0gxssIgkNmArMvis4iNah6Owg1MCJjWhEcDLmsK6iGkJq3tHwbDkTlce70/tmXN4cQ==", "signatures": [{"sig": "MEYCIQCvjPwozpDf6d6/rJYTTfI21LJ9JISA5TbrE/CS4uJ5QwIhAKaPtsPlJ5hAkwtz3+hrkh91POaGJZclf/6qLqEhBUO1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/json-parse-even-better-errors@3.0.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9939}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "4.0.0": {"name": "json-parse-even-better-errors", "version": "4.0.0", "devDependencies": {"tap": "^16.3.0", "@npmcli/template-oss": "4.23.3", "@npmcli/eslint-config": "^5.0.0"}, "dist": {"shasum": "d3f67bd5925e81d3e31aa466acc821c8375cec43", "tarball": "https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-4.0.0.tgz", "fileCount": 4, "integrity": "sha512-lR4MXjGNgkJc7tkQ97kb2nuEMnNCyU//XYVH0MKTGcXEiSudQ5MKGKen3C5QubYy0vmq+JGitUg92uuywGEwIA==", "signatures": [{"sig": "MEQCICAPnjckpb0S+7SRFcYhrLbCsxKs3Rx6aua1PRQ5IPzFAiAaR731u2iiEEDYcZmCwSJtY5A6xVm3Z62FiRKg2NN7CQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/json-parse-even-better-errors@4.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9961}, "engines": {"node": "^18.17.0 || >=20.5.0"}}}, "modified": "2025-05-14T20:03:41.796Z"}