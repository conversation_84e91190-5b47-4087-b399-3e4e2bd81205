{"_id": "content-type", "_rev": "37-442c13f025bb769d4623dc3e3f10d603", "name": "content-type", "dist-tags": {"latest": "1.0.5"}, "versions": {"0.0.1": {"name": "content-type", "version": "0.0.1", "keywords": ["content-type", "parse", "http", "header"], "author": {"name": "<PERSON>", "email": "https://github.com/Acubed"}, "license": "Unlicense <http://unlicense.org/>", "_id": "content-type@0.0.1", "maintainers": [{"name": "deoxxa", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/deoxxa/content-type/issues"}, "dist": {"shasum": "b8dd2786f814b2c8d0985fdbea8a3d361366ea80", "tarball": "https://registry.npmjs.org/content-type/-/content-type-0.0.1.tgz", "integrity": "sha512-O0mfhVaAsEE6lbSrUGqmPSVq3HCUxjuAtOTMYBgBMgtKs5NafvxWGedsE3rLipY0Ih+1/F7JesQEgxd09Lg6Gw==", "signatures": [{"sig": "MEUCIQCE/qwq98FxUvuma/ytWBwFphtBrr2RbHjnAdSU37CTTgIgSfFHbjtrima7vShOZ5JYTPPtC7J6SIfdUpA9VwrQlCY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "content-type.js", "_from": ".", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "deoxxa", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/deoxxa/content-type.git", "type": "git"}, "_npmVersion": "1.3.8", "description": "Javascript/ECMAScript library for parsing Content-Type and Media/MIME type strings", "directories": {}}, "1.0.0": {"name": "content-type", "version": "1.0.0", "keywords": ["content-type", "http", "req", "res", "rfc7231"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "content-type@1.0.0", "maintainers": [{"name": "deoxxa", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/content-type", "bugs": {"url": "https://github.com/jshttp/content-type/issues"}, "dist": {"shasum": "2b66ca456422371bd04e63fdda92501210f40be4", "tarball": "https://registry.npmjs.org/content-type/-/content-type-1.0.0.tgz", "integrity": "sha512-bLJw01/p0oGB1LvEjVqp2fpfD/WjmE/T+cgns1XuNBnHyRbMqKyhBTkRGaoaD5K0rky+0pgrZXGP3PO7GBKBWg==", "signatures": [{"sig": "MEUCIQDlX5CpB3FO7RVTWilGtj+jgA8x+jrdb8AoN4JChQ/QfwIgHG0SR9Aumg8NPArnfLaRoRzp2vejOwrJsSJYyc9dy5k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "2b66ca456422371bd04e63fdda92501210f40be4", "engines": {"node": ">= 0.6"}, "gitHead": "31266966b656ace33556e8dfd432b0790df82870", "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/content-type", "type": "git"}, "_npmVersion": "1.4.28", "description": "Create and parse HTTP Content-Type header", "directories": {}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.3.5"}}, "1.0.1": {"name": "content-type", "version": "1.0.1", "keywords": ["content-type", "http", "req", "res", "rfc7231"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "content-type@1.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/content-type", "bugs": {"url": "https://github.com/jshttp/content-type/issues"}, "dist": {"shasum": "a19d2247327dc038050ce622b7a154ec59c5e600", "tarball": "https://registry.npmjs.org/content-type/-/content-type-1.0.1.tgz", "integrity": "sha512-YE2Hbbw/jcvxJUVaPAxnbirzST4YWiCdJ98ts6s+zS0CelpTZp9S6fIcypEc4cp/ddtNhx+sSPzAFJutT6H4AA==", "signatures": [{"sig": "MEQCIF3Z8VOeFQdVUUBvDbcBs0XgzgkN2hAlm6K5LO9VZleoAiBLwXbEcRmUEEjWUd0a4IPQpGjjuiO/JdhlyGNbSPLmTg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "a19d2247327dc038050ce622b7a154ec59c5e600", "engines": {"node": ">= 0.6"}, "gitHead": "3aa58f9c5a358a3634b8601602177888b4a477d8", "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/content-type", "type": "git"}, "_npmVersion": "1.4.28", "description": "Create and parse HTTP Content-Type header", "directories": {}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.3.5"}}, "1.0.2": {"name": "content-type", "version": "1.0.2", "keywords": ["content-type", "http", "req", "res", "rfc7231"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "content-type@1.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/content-type#readme", "bugs": {"url": "https://github.com/jshttp/content-type/issues"}, "dist": {"shasum": "b7d113aee7a8dd27bd21133c4dc2529df1721eed", "tarball": "https://registry.npmjs.org/content-type/-/content-type-1.0.2.tgz", "integrity": "sha512-TFmXoAjJQD7hApJpE/GttZreniTw+DYE4zlDmPRc8Q75KXrU8hFt3Qeckml/mOTVAxwbMZ3WwdEcQCzTpfV5ZA==", "signatures": [{"sig": "MEQCIDvbW7DQn6Np006H963b0nCuVtDhNgoSpw6ezVBrWWSaAiAyKGFb4QJw1F/3XdVJpyA/LSPsUnSCgAqTnmcYEZnhxw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "b7d113aee7a8dd27bd21133c4dc2529df1721eed", "engines": {"node": ">= 0.6"}, "gitHead": "8118763adfbbac80cf1254191889330aec8b8be7", "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/content-type.git", "type": "git"}, "_npmVersion": "2.15.1", "description": "Create and parse HTTP Content-Type header", "directories": {}, "_nodeVersion": "4.4.3", "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/content-type-1.0.2.tgz_1462852785748_0.5491233412176371", "host": "packages-12-west.internal.npmjs.com"}}, "1.0.3": {"name": "content-type", "version": "1.0.3", "keywords": ["content-type", "http", "req", "res", "rfc7231"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "content-type@1.0.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/content-type#readme", "bugs": {"url": "https://github.com/jshttp/content-type/issues"}, "dist": {"shasum": "da18ef2fb64ca6acc905cc72017d3f38185b91d1", "tarball": "https://registry.npmjs.org/content-type/-/content-type-1.0.3.tgz", "integrity": "sha512-nlrmiLOH5ScOilJszGiFaA2PfBTIyoNTydx5N0+GKv5Nq/CUrqWyyw4xPWDtpJbL3G9Wrqb2gTv7Q1bMbXujhQ==", "signatures": [{"sig": "MEUCIQD3VMA5kK8Q1tWZd71dLeEqvChH9B+ZeyW2kCTgUHe1GAIgFjNftrruAisx3g2MHqE8kOzDFblsITrRWx5JPTje3As=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "da18ef2fb64ca6acc905cc72017d3f38185b91d1", "engines": {"node": ">= 0.6"}, "gitHead": "255c440e81ffe0f3eaa4b4644360d2f352aeff48", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --check-leaks --bail test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/content-type.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Create and parse HTTP Content-Type header", "directories": {}, "_nodeVersion": "6.11.1", "devDependencies": {"mocha": "~1.21.5", "eslint": "3.19.0", "istanbul": "0.4.5", "eslint-plugin-node": "5.1.1", "eslint-plugin-import": "2.7.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-standard": "3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/content-type-1.0.3.tgz_1505105047769_0.7591841116081923", "host": "s3://npm-registry-packages"}}, "1.0.4": {"name": "content-type", "version": "1.0.4", "keywords": ["content-type", "http", "req", "res", "rfc7231"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "content-type@1.0.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/content-type#readme", "bugs": {"url": "https://github.com/jshttp/content-type/issues"}, "dist": {"shasum": "e138cc75e040c727b1966fe5e5f8c9aee256fe3b", "tarball": "https://registry.npmjs.org/content-type/-/content-type-1.0.4.tgz", "integrity": "sha512-hIP3EEPs8tB9AT1L+NUqtwOAps4mk2Zob89MWXMHjHWg9milF/j4osnnQLXBCBFBk/tvIG/tUc9mOUJiPBhPXA==", "signatures": [{"sig": "MEUCIQDXUI1k+Yyuh7ELkCaqdje0TXLtHbvxAa6vAYZHZiFe2QIgNABlZR/wtggmzisHn+WaEQ7ETWKTdCTCZd23/d8POSo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "engines": {"node": ">= 0.6"}, "gitHead": "d22f8ac6c407789c906bd6fed137efde8f772b09", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --check-leaks --bail test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/content-type.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Create and parse HTTP Content-Type header", "directories": {}, "_nodeVersion": "6.11.3", "devDependencies": {"mocha": "~1.21.5", "eslint": "3.19.0", "istanbul": "0.4.5", "eslint-plugin-node": "5.1.1", "eslint-plugin-import": "2.7.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-standard": "3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/content-type-1.0.4.tgz_1505166155546_0.06956395204178989", "host": "s3://npm-registry-packages"}}, "1.0.5": {"name": "content-type", "version": "1.0.5", "keywords": ["content-type", "http", "req", "res", "rfc7231"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "content-type@1.0.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/content-type#readme", "bugs": {"url": "https://github.com/jshttp/content-type/issues"}, "dist": {"shasum": "8b773162656d1d1086784c8f23a54ce6d73d7918", "tarball": "https://registry.npmjs.org/content-type/-/content-type-1.0.5.tgz", "fileCount": 5, "integrity": "sha512-nTjqfcBFEipKdXCv4YDQWCfmcLZKm81ldF0pAopTvyrFGVbcR6P/VAAd5G7N+0tTr8QqiU0tFadD6FK4NtJwOA==", "signatures": [{"sig": "MEUCIAOjshRzvCHEtoGbwMNDa5gcXrVmrEvYFj1lNvszSRDrAiEAz70aazwtwXKqbbN4J19JbeGcEbVkfO3B3/Mmv6OEhfE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10471, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj1shHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoVkQ//W5y8wqmK9RwQlZ+c+Vx9as9LDZ/8f/hKHDOS7/PSl9vTOqdc\r\nWaS0WohfVHqmLvmF2nqFukL8dCtls1MtIopqPz90Z3DoUcTkm+LN0lcjcGd2\r\nOh45l7odtaXzFW5K0kzPlNAbgPPaWL4k6YT0ybdtMvVRHjoLB1V/I0JtUmP7\r\n2I4g5yezk+sS+JSmkgfMW3BiJZmWzLrl5YBtvQVtv1Nfw4S7wGmnfSoVtktq\r\nwbTHnhwsnJQcHfcGrGIZmtDmgySiBoWwURBbDlIthQmSxpscjlvpGSWLygK5\r\n3nEa/e+Pbv23x9gC0DmrnT2WdkI66jLS+56ixhqQGzeW7D6dahFklJQSmN8C\r\n2Aj+8LtNZLjo0DdZ6NW5stezwo/ELBanOmR3cLJHQ/+kwBomrPVYKX72yzd1\r\n4clMw6zFUKFnaZS+8r0R3GYUvoeylueoaBBLRiBB5c8pJNXF3mLZ1Zig47dh\r\npUuvAJ+TCfcFBOFSI/vKsTQ0E8TMSOQYOuM6SLAeWsxjJBLdedLHmKK9Y8VC\r\nWRmOWIrE8j1sc6E4odGPdy/tgpU+gLp5xG2IUF58XIbQih3OoYpN77bRdTYX\r\nUVha8Xf60h6ntj0NuS9zrK3LWWyfCzSPd8bX/caFUUeZjZwNwP2d/SCo8tia\r\nZ9SPx+wvGeJ2bqwqVg5r13fC/iI0+PmAagI=\r\n=Upqr\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}, "gitHead": "6115a4064e4dfd9845241c3f89c233ee2423deeb", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --check-leaks --bail test/", "test-ci": "nyc --reporter=lcovonly --reporter=text npm test", "version": "node scripts/version-history.js && git add HISTORY.md", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/content-type.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "Create and parse HTTP Content-Type header", "directories": {}, "_nodeVersion": "18.13.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "mocha": "10.2.0", "eslint": "8.32.0", "deep-equal": "1.0.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.27.5", "eslint-plugin-promise": "6.1.1", "eslint-config-standard": "15.0.1", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/content-type_1.0.5_1675020359482_0.5719775224587853", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2013-10-08T07:37:45.765Z", "modified": "2025-05-14T14:56:16.092Z", "0.0.1": "2013-10-08T07:37:54.398Z", "1.0.0": "2015-02-02T07:31:29.037Z", "1.0.1": "2015-02-14T00:37:57.925Z", "1.0.2": "2016-05-10T03:59:48.395Z", "1.0.3": "2017-09-11T04:44:08.721Z", "1.0.4": "2017-09-11T21:42:36.476Z", "1.0.5": "2023-01-29T19:25:59.622Z"}, "bugs": {"url": "https://github.com/jshttp/content-type/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/jshttp/content-type#readme", "keywords": ["content-type", "http", "req", "res", "rfc7231"], "repository": {"url": "git+https://github.com/jshttp/content-type.git", "type": "git"}, "description": "Create and parse HTTP Content-Type header", "maintainers": [{"email": "<EMAIL>", "name": "ulisesgascon"}, {"email": "<EMAIL>", "name": "b<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}], "readme": "# content-type\n\n[![NPM Version][npm-version-image]][npm-url]\n[![NPM Downloads][npm-downloads-image]][npm-url]\n[![Node.js Version][node-image]][node-url]\n[![Build Status][ci-image]][ci-url]\n[![Coverage Status][coveralls-image]][coveralls-url]\n\nCreate and parse HTTP Content-Type header according to RFC 7231\n\n## Installation\n\n```sh\n$ npm install content-type\n```\n\n## API\n\n```js\nvar contentType = require('content-type')\n```\n\n### contentType.parse(string)\n\n```js\nvar obj = contentType.parse('image/svg+xml; charset=utf-8')\n```\n\nParse a `Content-Type` header. This will return an object with the following\nproperties (examples are shown for the string `'image/svg+xml; charset=utf-8'`):\n\n - `type`: The media type (the type and subtype, always lower case).\n   Example: `'image/svg+xml'`\n\n - `parameters`: An object of the parameters in the media type (name of parameter\n   always lower case). Example: `{charset: 'utf-8'}`\n\nThrows a `TypeError` if the string is missing or invalid.\n\n### contentType.parse(req)\n\n```js\nvar obj = contentType.parse(req)\n```\n\nParse the `Content-Type` header from the given `req`. Short-cut for\n`contentType.parse(req.headers['content-type'])`.\n\nThrows a `TypeError` if the `Content-Type` header is missing or invalid.\n\n### contentType.parse(res)\n\n```js\nvar obj = contentType.parse(res)\n```\n\nParse the `Content-Type` header set on the given `res`. Short-cut for\n`contentType.parse(res.getHeader('content-type'))`.\n\nThrows a `TypeError` if the `Content-Type` header is missing or invalid.\n\n### contentType.format(obj)\n\n```js\nvar str = contentType.format({\n  type: 'image/svg+xml',\n  parameters: { charset: 'utf-8' }\n})\n```\n\nFormat an object into a `Content-Type` header. This will return a string of the\ncontent type for the given object with the following properties (examples are\nshown that produce the string `'image/svg+xml; charset=utf-8'`):\n\n - `type`: The media type (will be lower-cased). Example: `'image/svg+xml'`\n\n - `parameters`: An object of the parameters in the media type (name of the\n   parameter will be lower-cased). Example: `{charset: 'utf-8'}`\n\nThrows a `TypeError` if the object contains an invalid type or parameter names.\n\n## License\n\n[MIT](LICENSE)\n\n[ci-image]: https://badgen.net/github/checks/jshttp/content-type/master?label=ci\n[ci-url]: https://github.com/jshttp/content-type/actions/workflows/ci.yml\n[coveralls-image]: https://badgen.net/coveralls/c/github/jshttp/content-type/master\n[coveralls-url]: https://coveralls.io/r/jshttp/content-type?branch=master\n[node-image]: https://badgen.net/npm/node/content-type\n[node-url]: https://nodejs.org/en/download\n[npm-downloads-image]: https://badgen.net/npm/dm/content-type\n[npm-url]: https://npmjs.org/package/content-type\n[npm-version-image]: https://badgen.net/npm/v/content-type\n", "readmeFilename": "README.md", "users": {"eyson": true, "hualei": true, "semir2": true, "huhgawz": true, "zuojiang": true, "rbecheras": true, "snowdream": true, "goodseller": true, "qqqppp9998": true, "rocket0191": true, "simplyianm": true, "flumpus-dev": true, "zhenguo.zhao": true, "shanewholloway": true}}