{"name": "neo-async", "dist-tags": {"latest": "2.6.2"}, "versions": {"0.1.0": {"name": "neo-async", "version": "0.1.0", "devDependencies": {"lodash": "^2.4.1", "async": "^0.9.0", "mocha": "*", "power-assert": "^0.8.0"}, "dist": {"shasum": "db2ee753fcaf2b40aa809d5759cf3b622d830122", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-0.1.0.tgz", "integrity": "sha512-jUpwRO396+eou82txL3hYvsP+9mYgoAK9EvZ3QsRE8VHs0O5NcQX1hzOCMvVjuPo7zSvnvVhluxVG2Scn/Txcw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCoFAsiwfTJwRCzsTvdUFdw3Wqul9iKdAOV430E5/evaQIgDUyOh0rlxZKHomYn7IFabNTZwpQI8iddawLbtbnypAs="}]}}, "0.1.1": {"name": "neo-async", "version": "0.1.1", "devDependencies": {"lodash": "^2.4.1", "async": "^0.9.0", "mocha": "*", "power-assert": "^0.8.0"}, "dist": {"shasum": "39fa1e409bc5203c338a09a7f15a4b42184da8e1", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-0.1.1.tgz", "integrity": "sha512-PFv03iFOnKumTjs7/vCh+ua+MCvPeDvUVRUhcfwmXiB96+p5tqcAJo6MoaGD2zx/ngjdQE9kXt+hmO1ikTkawg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDQmHht7eTMGvf4vMVaYNejCImnXGCWif6vGz2uPp+ONAiAxWjYLByORvp47jpQpywZhavMgsAu8nU+6mtH4GIUxrA=="}]}}, "0.2.0": {"name": "neo-async", "version": "0.2.0", "devDependencies": {"async": "^0.9.0", "espower-loader": "^0.10.0", "intelli-espower-loader": "^0.5.0", "lodash": "^2.4.1", "mocha": "^2.0.1", "power-assert": "^0.10.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "fb9a189626c960c9a2a27d18fc6b11c07ce58543", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-0.2.0.tgz", "integrity": "sha512-z1rH8aAKheVX2DrXRXNvidDM3uUzuHGAwD0BAWUWAfxmYRiwHCpFRzDiWkAVkgjwnE4NvnXRkI3Zpnu2Tl2XEA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCnSdV5L8vACIDthPwfBHZh5KvlAq6mS93g2iO9e+KWvwIgBbQqLLD00WAwg0Gw++vfe4GWqwvITUhsrdT3G0yLTTI="}]}}, "0.2.2": {"name": "neo-async", "version": "0.2.2", "devDependencies": {"async": "^0.9.0", "espower-loader": "^0.10.0", "intelli-espower-loader": "^0.5.0", "lodash": "^2.4.1", "mocha": "^2.0.1", "power-assert": "^0.10.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "fedc51d89d9d9fc2c6118f27a5936d9f7e2457a0", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-0.2.2.tgz", "integrity": "sha512-TZGt49mc27h7FpD9a/JhDfJD7KAcgwpfo+VRMr1Dfvmo/yzDZLUzwMo//8Lwom2C5QIYYW4fwuJS98f3sU/fUg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDDQNeZKfyc68uvNjF+bkRiXFrEJYmYyn4LB2Z9lg7IAAiEAm39GWvJGXAJsOLqxI/1fHUtoZQseC+OfoNYSzO99iQA="}]}}, "0.2.3": {"name": "neo-async", "version": "0.2.3", "devDependencies": {"async": "^0.9.0", "espower-loader": "^0.10.0", "intelli-espower-loader": "^0.5.0", "lodash": "^2.4.1", "mocha": "^2.0.1", "power-assert": "^0.10.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "3c41ff622ea56946406b77334cdda255310cc8d6", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-0.2.3.tgz", "integrity": "sha512-tj+0PWEVRsx9F4PO/FRFkpMLpqc/LEzudCuTpTBJ1XqJoq2tJ4abit6J7RDhX6Gd+uy+c7jb6DQeL4ex8X+7FA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBTcpJ7wfjOkwRHlKZM/i+LWGdzs3QCjm9QKiNRaDz38AiEA/N6lsz4L/meI4jmjyRVexwzDZ7awk29cXFpzYIzAlZ8="}]}}, "0.2.4": {"name": "neo-async", "version": "0.2.4", "devDependencies": {"async": "^0.9.0", "espower-loader": "^0.10.0", "intelli-espower-loader": "^0.5.0", "lodash": "^2.4.1", "mocha": "^2.0.1", "power-assert": "^0.10.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "d23adfdf364651708f85b95577a7ee477332092f", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-0.2.4.tgz", "integrity": "sha512-bvOOYg9K8jppcjeH80sOFyCZCHbTQQ/BrCpw5FFyUu1SiD9T3QYwLlLle4M+NszvcTeuOZGHqc1NHcvpABBBlQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCXPPkQsnJAU4tLr24Z7WMdgXHH6i04joSv+qm88ILjngIhAJBi/yD25Po6+2nL1WDEdFlzJSpaSrCU8+wv4qez5NbK"}]}}, "0.2.5": {"name": "neo-async", "version": "0.2.5", "devDependencies": {"async": "^0.9.0", "espower-loader": "^0.10.0", "intelli-espower-loader": "^0.5.0", "lodash": "^2.4.1", "mocha": "^2.0.1", "power-assert": "^0.10.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "cd10d6c6a517e9da986010f6e8ae81b60041934b", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-0.2.5.tgz", "integrity": "sha512-47lIBml2LUCqn+dKLhbGWY4k6kFjNnXdDSbI3iul5i6ztXu13VUsTHKWZEecYhhEmzCQKv8E+WR/sIF3iLrzxQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCNzLH/TtIjjKnzGTMAZ2JmVQ2c06LHHX6j7bwATWvSHgIhAM+dKc7fgxdPoXXJbhBIu5aF4tRhvJr3/l/K70/IsNLf"}]}}, "0.2.6": {"name": "neo-async", "version": "0.2.6", "devDependencies": {"async": "^0.9.0", "coveralls": "^2.11.2", "espower-loader": "^0.10.0", "intelli-espower-loader": "^0.5.0", "istanbul": "^0.3.2", "lodash": "^2.4.1", "mocha": "^2.0.1", "power-assert": "^0.10.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "5c85ef418aa46e5808535d170966a2953c69e827", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-0.2.6.tgz", "integrity": "sha512-yqI3tkICWGRnGOCNpFlxEZFS/U7C85nDUsxrW9mzWtdK7IoSDSbKNQT1lBtUOgQPvPSWHOdjeEbyFdM3jhtc6A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEsK8+QO7I15OkZVfbs3u3HBG/3wwajQMPYEzXN/uyfbAiAq/UFSXdzUhErQqpvMZdrYcA3mKQtCtECutpJi/8pFFg=="}]}}, "0.2.7": {"name": "neo-async", "version": "0.2.7", "devDependencies": {"async": "^0.9.0", "coveralls": "^2.11.2", "espower-loader": "^0.10.0", "intelli-espower-loader": "^0.5.0", "istanbul": "^0.3.2", "lodash": "^2.4.1", "mocha": "^2.0.1", "power-assert": "^0.10.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "6f646ba7664def80ad1744cc68be179dbfd24ce8", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-0.2.7.tgz", "integrity": "sha512-4Yv6Sxnr0ridv7L/APk4IgcVlw77z7GtX4IFWWRbyMb8kMn4ebYdoj5BkQ+CnpsdPs2WzeN2iqCdjcHSCh3v9w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG6UJnMIBCpoNDnLIB5Zer1aFPeugbORIvrQUadjswr1AiEA3b5tHN05XhcLjto/yypWfiqTuYI9Qjgld2RvkTz5GlE="}]}}, "0.2.8": {"name": "neo-async", "version": "0.2.8", "devDependencies": {"async": "^0.9.0", "coveralls": "^2.11.2", "espower-loader": "^0.10.0", "intelli-espower-loader": "^0.5.0", "istanbul": "^0.3.2", "lodash": "^2.4.1", "mocha": "^2.0.1", "power-assert": "^0.10.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "ebf0f349a8b44e43e88c30a22c7acd01b9d90297", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-0.2.8.tgz", "integrity": "sha512-Z0H+zqXK+C0nPjNvRAmXGaBYFLJpAuEXp+jkMJficye7fi4KfwSpSCKXlnCj7awNCKzCw0JXDgMrUy7kIGmRmw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFRUi/kIb03nDkBbWlQ0uEJFZxd5biuIlRx9JD3riocKAiBgDbNlvXmjrYeppSxfcURJ48oRb8ikvLWn4ZTxsCP8Hg=="}]}}, "0.2.9": {"name": "neo-async", "version": "0.2.9", "devDependencies": {"async": "^0.9.0", "coveralls": "^2.11.2", "espower-loader": "^0.10.0", "func-comparator": "^0.2.0", "intelli-espower-loader": "^0.5.0", "istanbul": "^0.3.2", "lodash": "^2.4.1", "mocha": "^2.0.1", "power-assert": "^0.10.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "7907d75bfca7cf4015fdd6ae97411979ab1b549f", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-0.2.9.tgz", "integrity": "sha512-sck79TTW8SFFkU1plqjCRKoyuJf6LEBgIyM7w54uDT6heQoqRsvIPNDGPq0NLasX+xfQqu2i1aDOZfGGiNIDyg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAEuaByTidrOcYiIrFDTKEEobaCP9Vw5pzRuOazzRzGvAiEAzf/ucx6SIqKa59eKfhspqLIJX+Bn1CZeEAnR9uYAXqQ="}]}}, "0.2.10": {"name": "neo-async", "version": "0.2.10", "devDependencies": {"async": "^0.9.0", "coveralls": "^2.11.2", "espower-loader": "^0.10.0", "func-comparator": "^0.2.0", "intelli-espower-loader": "^0.5.0", "istanbul": "^0.3.2", "lodash": "^2.4.1", "mocha": "^2.0.1", "power-assert": "^0.10.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "f74d816a69ed2c2775451bd9fa5c1d7537649b62", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-0.2.10.tgz", "integrity": "sha512-5xiuUGIA1Yu4gz5cmkvgyBHtoNurwx7XICF5/+YAltLsCoeScJ9nmfZM607Bgrzgp97gY9kl1BaFZowHeBw/dg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAeeKV4FCCPfU8pgBAVqsRyYCeSuNhoPWwAjMt9gdXDUAiEA8pYNONZCdHqt/rcb4T532kxXs3W1Mgg8KuUlnM0rZRA="}]}}, "0.2.11": {"name": "neo-async", "version": "0.2.11", "devDependencies": {"async": "^0.9.0", "coveralls": "^2.11.2", "func-comparator": "^0.2.0", "intelli-espower-loader": "^0.5.0", "istanbul": "^0.3.2", "lodash": "^2.4.1", "mocha": "^2.0.1", "power-assert": "^0.10.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "6d4106a760f5bdd294080dcfcc6ebee35543b6b4", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-0.2.11.tgz", "integrity": "sha512-ti7WkvFi7YUfdcgUtqYq/924YgUSUMxa4NRZ7mn4rQQz04xvpa4ZAHq3dN52F7HMBf85RUvxwJHk7YYdFV3d+Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDNsZz2eYxA9tlJx3qayPBXqPzQOeA5x2XNFoKosGcsiAIhAMW8T1R751+Tia8Wv/efohSXmK/LTsfRhKioXAhxfiie"}]}}, "0.3.0": {"name": "neo-async", "version": "0.3.0", "devDependencies": {"async": "^0.9.0", "coveralls": "^2.11.2", "func-comparator": "^0.2.0", "intelli-espower-loader": "^0.5.0", "istanbul": "^0.3.2", "lodash": "^2.4.1", "mocha": "^2.0.1", "power-assert": "^0.10.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "cb9ea31157b8b25eea70980a4801f3f593ebd146", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-0.3.0.tgz", "integrity": "sha512-LsLQlKS1W7bc4bNgfSIvrNmftocOrNz59j/QWe01NddsuPvxw694J/Nf4I76FjlJXNdiN/wPyKphg+tJ4E/lHQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHU1RZzLtnue0cdejr412I/OzjWKXvZ9jaeW6efEKTeSAiEAyRf73hKBfYVR1WrD7vFmDlT+pEYSx0jq48HzBWowcQI="}]}}, "0.3.1": {"name": "neo-async", "version": "0.3.1", "devDependencies": {"async": "^0.9.0", "coveralls": "^2.11.2", "func-comparator": "^0.2.0", "intelli-espower-loader": "^0.5.0", "istanbul": "^0.3.2", "lodash": "^2.4.1", "mocha": "^2.0.1", "power-assert": "^0.10.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "c07d9c0d34673767b0bc6e6cb281505df76a58d8", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-0.3.1.tgz", "integrity": "sha512-fHiqNmepIjS0g1jGhn6GY8X3Gk+6WQ1/0AFMQgrSBiHxNk5h7msh5qw941/5gCtNqrTYmsZYAgrzM3eWhW9gpA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBt7S6mswKn5ygKmyunRhYkwsc5kTCJCOhuL8EFWYoeqAiBWwS159LbBultvqckKLS2OvIXwZ5Duy2LLCL2fMSli/A=="}]}}, "0.3.2": {"name": "neo-async", "version": "0.3.2", "devDependencies": {"async": "^0.9.0", "coveralls": "^2.11.2", "func-comparator": "^0.2.0", "intelli-espower-loader": "^0.5.0", "istanbul": "^0.3.2", "lodash": "^2.4.1", "mocha": "^2.0.1", "power-assert": "^0.10.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "499d4d8e2384ea40b9c508d48d66f8ab7767084c", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-0.3.2.tgz", "integrity": "sha512-2CrYKZMc4XF06SG+eGScI3EyE6g62FQKBLMJttS0gmmFHugsCwM418Q9n0wHkAdiVmC4Hnx88g0HRSF2efR5kQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDpozEVj5fBdQScqOXlk2ryiPFOIPVAN28d7GF3+ytueQIgX3rkjcb4UE0GXzOX8JhfAI+/FrZLTXXWe0IHpCT8imU="}]}}, "0.3.3": {"name": "neo-async", "version": "0.3.3", "devDependencies": {"async": "^0.9.0", "coveralls": "^2.11.2", "func-comparator": "^0.2.0", "intelli-espower-loader": "^0.5.0", "istanbul": "^0.3.2", "lodash": "^2.4.1", "mocha": "^2.0.1", "power-assert": "^0.10.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "aacf1bc6cd602a5f307ef253eb573b0c829ed852", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-0.3.3.tgz", "integrity": "sha512-1R4HKQQoCjXIXq5znQ2oJRPc0erguNDt2NJD1qGeril4yIKHIXpCDwzMgwAHnJg5J14BSv6SOmqM1/AZHog55Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICHomRKK+be58UgseCpjGPfsk2P/WohNJeWP6rMQccQlAiAyZ3wM9lodHH1iqU2qIkyI2dzORgj10303e8vaRsJaxw=="}]}}, "0.3.4": {"name": "neo-async", "version": "0.3.4", "devDependencies": {"async": "^0.9.0", "coveralls": "^2.11.2", "func-comparator": "^0.2.0", "intelli-espower-loader": "^0.5.0", "istanbul": "^0.3.2", "lodash": "^2.4.1", "mocha": "^2.0.1", "power-assert": "^0.10.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "a83c5cfc65e47f16711418a80cffc110f9e3902c", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-0.3.4.tgz", "integrity": "sha512-JJfAwLj5kXJuS6ZkQMD3TE7Y1qiQTlvKdy4nPScXPPCwoS+sEUK4QvVJ/HjsoIkLVaGIM01VcEU/cZ+rBXh/1Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCWzhtR+rq5aP2JPNAreWFNr6YvhBgpvuJrgmH94jfk5wIhAMpKHXa9BE25uubruF6BCTL4ku1NwA38GKhNDdmYo7XZ"}]}}, "0.4.0": {"name": "neo-async", "version": "0.4.0", "devDependencies": {"async": "^0.9.0", "codecov.io": "0.0.8", "func-comparator": "^0.2.0", "intelli-espower-loader": "^0.5.0", "istanbul": "^0.3.2", "lodash": "^2.4.1", "mocha": "^2.0.1", "power-assert": "^0.10.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "4adbe1cb0144372159fd2a8d45954da2f1e53598", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-0.4.0.tgz", "integrity": "sha512-fqXQdqbmqUfhBLlRgjxPDafuKk+49ND9shcZm0okkwMRE8KMFVGjOLF3Tf8prhZ+vR/l2+VJQ1S4I+5YPh4P6w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC3kuLsi+t3290JZWca/3xjydLVkgONKX9Ja2gUouz1MAIhAIv0WYoVLqrXQ6K4xlkNqbUp3u0mjr+7WfaaKrmR3+R6"}]}}, "0.4.1": {"name": "neo-async", "version": "0.4.1", "devDependencies": {"async": "^0.9.0", "codecov.io": "0.0.8", "func-comparator": "^0.2.0", "intelli-espower-loader": "^0.5.0", "istanbul": "^0.3.2", "lodash": "^2.4.1", "mocha": "^2.0.1", "power-assert": "^0.10.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "b41c54d6cb41b0c2df8660125712aeaeb1bfd671", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-0.4.1.tgz", "integrity": "sha512-/ix8ZmHW2PzY0u2ANUAa862Ac5lWvEtC6j26B08Loi82D8qlz4Xe/Fl4O6/NTGYDmk5mp3sb3F27LzVwtgojWQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGHdM7h0C4S1y2Aveb/HWUWskHhQQK5y5Fzw0DqZnfc5AiEA8sYAo4kmuV2v2J7NnZk6AwLioRHDpekku0V2bh6KDEk="}]}}, "0.4.2": {"name": "neo-async", "version": "0.4.2", "devDependencies": {"async": "^0.9.0", "codecov.io": "0.0.8", "func-comparator": "^0.2.0", "intelli-espower-loader": "^0.5.0", "istanbul": "^0.3.2", "lodash": "^2.4.1", "mocha": "^2.0.1", "power-assert": "^0.10.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "851c482cf673dbae204822eeca96145c21c0934f", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-0.4.2.tgz", "integrity": "sha512-70y8e762n4q4PQksUDVJrATIepSO60PME4qDFjWVoRgHVtKpl9Y9SceZc4i4Y6X8mXgPSJYNiKdO1LMW3RA0dA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE29XHkqurjGJ/jCTDpOQbVg/DMukNRJ9BJU4p98qf+MAiBGHD9kl8AVJ+xG0wjeuI6l+1Z5ntVS/73AbTm0koUAfg=="}]}}, "0.4.3": {"name": "neo-async", "version": "0.4.3", "devDependencies": {"async": "^0.9.0", "codecov.io": "0.0.8", "func-comparator": "^0.2.0", "intelli-espower-loader": "^0.5.0", "istanbul": "^0.3.2", "lodash": "^2.4.1", "mocha": "^2.0.1", "power-assert": "^0.10.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "ec24872054e17d0a65ac932e73ca93fd7ceb579f", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-0.4.3.tgz", "integrity": "sha512-nCYTWjKwLfHqhMLMfnQMtun8r24V7evWNpSOhtHRDVbL6BKj55gx/jexeeetQtJdHOxNhZLEsECSQ8uljxSCeg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDxumvM8tL2IKPmuwEjp3X2hNFa8RhqmAayhRO9BOZt6wIhANYTlm5vaayfNOfLyVbfOi77Mr0SiJCO4hImTv0qLTAS"}]}}, "0.4.4": {"name": "neo-async", "version": "0.4.4", "devDependencies": {"async": "^0.9.0", "codecov.io": "0.0.8", "func-comparator": "^0.2.0", "intelli-espower-loader": "^0.5.0", "istanbul": "^0.3.2", "lodash": "^2.4.1", "mocha": "^2.0.1", "power-assert": "^0.10.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "502cabdba0721b3885f5bee6985e141a53631834", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-0.4.4.tgz", "integrity": "sha512-88T7+vUX82eVhmCULlwP/aJJ42G2/4e+uLy+pxnsWzcPgsnGlklXnzHM4zBCOg9W5byH5GpwHANb5HifrVcCyA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHuoDTcU2WoYQ/mCV2gLx+aPyZOn0luefx1SpgiM0ElZAiAfYWbWfZUNYHstf+aT/B/mhu2koOGNPMjktouLLPf4Mw=="}]}}, "0.4.5": {"name": "neo-async", "version": "0.4.5", "devDependencies": {"async": "^0.9.0", "codecov.io": "0.0.8", "func-comparator": "^0.2.0", "intelli-espower-loader": "^0.5.0", "istanbul": "^0.3.2", "lodash": "^2.4.1", "mocha": "^2.0.1", "power-assert": "^0.10.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "c04e93a450b223ebada0e00c0571ed4112a2daab", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-0.4.5.tgz", "integrity": "sha512-xbt8eTBjkDTnquPQt4QTCsnKemr6hahbV3COPa4/OYZkYI1lqlvrXGRLOWb++d/U1kq6CiGZVBQXN7Blrqr6Rg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBjjdr055J9hhpM7GVfcMlrVy66uTmjYt6h68gUkEsv0AiBmXa0tjq+a0hbnTOLBwluO9HRJ3XuFw1casNvguyZM/g=="}]}}, "0.4.6": {"name": "neo-async", "version": "0.4.6", "devDependencies": {"async": "^0.9.0", "codecov.io": "0.0.8", "func-comparator": "^0.2.0", "intelli-espower-loader": "^0.5.0", "istanbul": "^0.3.2", "lodash": "^2.4.1", "mocha": "^2.0.1", "power-assert": "^0.10.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "8f848f42974f785dbb07590b5387a4cd7a2b47b4", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-0.4.6.tgz", "integrity": "sha512-I1/60F7/17en0DcGk7fRJszKUqeUng+/a9Jv5hRCwt4SrK44gp6Nr+GiTnpsekxSDpMwd+3kep1BvFetkRhdKA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDj99D62eKHh6m1EsU3cfbD9Z0Z+FFOw2XbtLHIh7W7MwIgCcCDdmFa5g+5jAJQQO7hrCMsYwIf9yW5I1+fGrrfG2k="}]}}, "0.4.7": {"name": "neo-async", "version": "0.4.7", "devDependencies": {"async": "^0.9.0", "codecov.io": "0.0.8", "func-comparator": "^0.2.0", "intelli-espower-loader": "^0.5.0", "istanbul": "^0.3.2", "lodash": "^2.4.1", "mocha": "^2.0.1", "power-assert": "^0.10.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "0ced9796f41ed02ddc29b318edb127600483d412", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-0.4.7.tgz", "integrity": "sha512-BMd1TKOiZ4IqEJNy7u4B7WUO4vHtowZaMYPo3ANq6kGskRWFSwJWHYhy7tTeBXgpm00gDoPe9K3SW42iVCJwEg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC0MkBD79kr5VKH9lHFuxuMgf4xUkjSBX9My4ibx1bRiAiEAvhuLSg/nEtlp7WwSwMFlkMzUBKJoV49XCQyalqa91zs="}]}}, "0.4.8": {"name": "neo-async", "version": "0.4.8", "devDependencies": {"async": "^0.9.0", "codecov.io": "0.0.8", "func-comparator": "^0.2.0", "intelli-espower-loader": "^0.5.0", "istanbul": "^0.3.2", "lodash": "^2.4.1", "mocha": "^2.0.1", "power-assert": "^0.10.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "e7e357a55259b859a1fa88f3534651e211afb103", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-0.4.8.tgz", "integrity": "sha512-27vUR0zSxP5PXlZKVjKhJGGAm5Z5BNbOW0k/BHjAEaInTbn7E8CRlFSMvYaYxAz5NOCgf7AaXE5QYT6hyy8pYw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD+NLFHbrsTFUdQW4OyjXapaFATC0RhrsvOwXIEDOr9GgIgQJu2isjdtiF6lQp72oqw2dS6CMcfd0Nj94cMp/LLOLA="}]}}, "0.4.9": {"name": "neo-async", "version": "0.4.9", "devDependencies": {"async": "^0.9.0", "codecov.io": "0.0.8", "func-comparator": "^0.2.0", "intelli-espower-loader": "^0.5.0", "istanbul": "^0.3.2", "lodash": "^2.4.1", "mocha": "^2.0.1", "power-assert": "^0.10.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "169cc96a74e1daa6fc585914db07aca5f41badba", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-0.4.9.tgz", "integrity": "sha512-glaIPDwniWU3OW9mdUMbVR6O+/DEuXtIuDWuCJuIXqhSVtccF90HGpKHPE/iCTEcnGhLStOIdAqGl542G51etw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCYNsuMRVcQIh5OvKbwoYoF0Yc8s5JPQ41XqMiRvSR/oAIhAO9xEpf2Aao0zEGN6ibaq1/Vm8iS7fLASHR+Wr2zxJLg"}]}}, "0.5.0": {"name": "neo-async", "version": "0.5.0", "devDependencies": {"async": "^0.9.0", "codecov.io": "0.0.8", "func-comparator": "^0.2.0", "intelli-espower-loader": "^0.5.0", "istanbul": "^0.3.2", "lodash": "^2.4.1", "mocha": "^2.0.1", "power-assert": "^0.10.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "8d23bedded5447e9df4c1bd093211ca94b6b5bbf", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-0.5.0.tgz", "integrity": "sha512-tsQoooCX/wIqulxwkiSvS/a5Xwi6tp7ihexIl/eNZ0TiSrE/ql2KeQQvMCREBZGjaw/JLJmOIJ7Ja6rP0IueXA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC8J3JNSbuHnuUREW9IMOn8dJZeKoNfAjJdhTOgQpo1uAIgDwbCIwJUfhI+IbofbjWS0B3knrUoWjdJZDx8jJNynCs="}]}}, "0.5.1": {"name": "neo-async", "version": "0.5.1", "devDependencies": {"async": "^0.9.0", "codecov.io": "0.0.8", "func-comparator": "^0.4.1", "intelli-espower-loader": "^0.5.0", "istanbul": "^0.3.4", "lodash": "^2.4.1", "mocha": "^2.0.1", "power-assert": "^0.10.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "a20a3cc4d7e18c3180707362f2d751bd405cb4c4", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-0.5.1.tgz", "integrity": "sha512-VWqrV3ie2Am5ZVbrNSTSbXWIohB7Zc60NPbZzxTpKpHJqw0jZo2yjquR3LesnJxo5sqYr9tWJYN9b57CY5D1Ag==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC5zroezC8ZWwdR3U7mLayZXE7hUVikTo/rxAHapV0vjQIhAJeOFy8w5kJcCoKYW0RNbTOStFUbf3fj1BiYSIHLQqZp"}]}}, "0.5.2": {"name": "neo-async", "version": "0.5.2", "devDependencies": {"async": "^0.9.0", "codecov.io": "0.0.8", "func-comparator": "^0.4.1", "intelli-espower-loader": "^0.5.0", "istanbul": "^0.3.4", "lodash": "^2.4.1", "mocha": "^2.0.1", "power-assert": "^0.10.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "7cee71cfd9833463632d820ec2b08b657cf3f40b", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-0.5.2.tgz", "integrity": "sha512-ZqvGFu6KgdVeNKJ0l2xjRw4fj9AT6zUh0x2CZ6spiDFFhVjoGs2LhKqQSfR4/Wz2WrnXSSeYjpdbm2mphovAeA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDdYWYdZC2hqTYZYEYDVaUeT2hfGGJN/HSBH6/HNg/4HwIhAPLjGR8SR/H6lOGkRVI25YRWNlcwTlNh7uAkwPCeudyb"}]}}, "0.5.3": {"name": "neo-async", "version": "0.5.3", "devDependencies": {"async": "^0.9.0", "codecov.io": "0.0.8", "func-comparator": "^0.4.1", "intelli-espower-loader": "^0.5.0", "istanbul": "^0.3.4", "lodash": "^2.4.1", "mocha": "^2.0.1", "power-assert": "^0.10.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "ea4802add142925b6be566179e0b5808090ad030", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-0.5.3.tgz", "integrity": "sha512-yupCdIe7Z4lLY+TAAAYA6zO0dZcuGCJGYwUspTYf9XwfiOkd8UDGThDIYDsfRu+/1Cb+flviuYjZmxOj38V/fQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDUrByKctc7QDXky6fjFTP3uy0YqoYseVvklJMbaCqCmAiAnXwzA0HgppcX4NRSXCKk24ZivO7xKPIgqqWDGmi7vNQ=="}]}}, "0.6.0": {"name": "neo-async", "version": "0.6.0", "devDependencies": {"async": "^0.9.0", "codecov.io": "0.0.8", "func-comparator": "^0.4.1", "gulp": "^3.8.11", "gulp-exit": "0.0.2", "gulp-mocha": "^2.0.0", "gulp-util": "^3.0.3", "intelli-espower-loader": "^0.5.0", "istanbul": "^0.3.4", "lodash": "^2.4.1", "mocha": "^2.0.1", "power-assert": "^0.10.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "bc326f1ab83938dc186946ea01819aba282af6a5", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-0.6.0.tgz", "integrity": "sha512-yZZbGbfpDsakJmZ3LO8fs3FcmcmV8wG7A6lqvL4Y9pVxyohAf7PjChC+5iOjqcM81G6pm8cnEZXHgebNh7sakw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGJmx9Ieh94Y+fwmUJFURbp8vPYJKvPo+59GTL9f+a54AiEA79RFmFEBi2dKty8Zki5KIpMdcgSIXM+CxfknFp/A4fY="}]}}, "0.6.1": {"name": "neo-async", "version": "0.6.1", "devDependencies": {"async": "^0.9.0", "codecov.io": "0.0.8", "func-comparator": "^0.4.1", "gulp": "^3.8.11", "gulp-exit": "0.0.2", "gulp-mocha": "^2.0.0", "gulp-util": "^3.0.3", "intelli-espower-loader": "^0.5.0", "istanbul": "^0.3.4", "lodash": "^2.4.1", "mocha": "^2.0.1", "power-assert": "^0.10.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "4bc5868018baa273751645f1a6375edf9a8ceb1f", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-0.6.1.tgz", "integrity": "sha512-vTRSg9LW9jB81pY2Y4U4/TnqEKw2lQBLO8giviv9Tfi5fRub/nuW9apceLmSQPDSvV95Ywl2fnZIJXXPQ96c8Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH01EmQ/K9J2aGn2J8CjYdIZsafiJQBw4Gd10K7pz8iYAiBB3nPU0U0TjzzOhjan/SWKuzPvo+3Y4iJGF95FxO5bOg=="}]}}, "0.6.2": {"name": "neo-async", "version": "0.6.2", "devDependencies": {"async": "^0.9.0", "codecov.io": "0.0.8", "event-stream": "^3.2.2", "func-comparator": "^0.4.1", "gulp": "^3.8.11", "gulp-exit": "0.0.2", "gulp-jsbeautifier": "0.0.4", "gulp-jscs": "^1.4.0", "gulp-mocha": "^2.0.0", "gulp-util": "^3.0.3", "intelli-espower-loader": "^0.5.0", "istanbul": "^0.3.4", "lodash": "^3.3.0", "mocha": "^2.0.1", "power-assert": "^0.10.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "6de30c0fd5fa1592a28e17132cbc1b91b9b8c21b", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-0.6.2.tgz", "integrity": "sha512-DgnsRaxuwCFqj0BXq85S+615bkDcJUkztMjTNQ32mPSYKOJZR86AqJqYde6+Fr0NZHUvZxINsNF7RFkfBa8hQQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGZixAF+YM1TnSGepmHfTgalh93PSD1C4AMzI6fu4uHVAiEAsr9gCwOLlq8UlOTx12tBORdO8xQuDoR2Ex0Pvg+f0ow="}]}}, "0.6.3": {"name": "neo-async", "version": "0.6.3", "devDependencies": {"async": "^0.9.0", "codecov.io": "0.0.8", "event-stream": "^3.2.2", "func-comparator": "^0.4.1", "gulp": "^3.8.11", "gulp-exit": "0.0.2", "gulp-jsbeautifier": "0.0.4", "gulp-jscs": "^1.4.0", "gulp-mocha": "^2.0.0", "gulp-util": "^3.0.3", "intelli-espower-loader": "^0.5.0", "istanbul": "^0.3.4", "lodash": "^3.3.0", "mocha": "^2.0.1", "power-assert": "^0.10.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "f5c9c66e599b19d214a2935d9406faa68161b2d9", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-0.6.3.tgz", "integrity": "sha512-xsB1rPbScmV7crjk0HL5ZwABdZ5vJPlmGrFxrk3Udrlak3r0Y+/ycm54b7iB03SwbgNG5uC3uSQ9oPqwSLvScA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDXwO1R/8mWWmDbHdem3QEtcFK6KfMlhWVmMgCvS8/NHgIhAJoEAqcgWcnCMOgcUlPmB7TJUdbuA/pMQbsvzzKzSiva"}]}}, "0.6.4": {"name": "neo-async", "version": "0.6.4", "devDependencies": {"async": "^0.9.0", "codecov.io": "0.0.8", "event-stream": "^3.2.2", "func-comparator": "^0.4.1", "gulp": "^3.8.11", "gulp-exit": "0.0.2", "gulp-jsbeautifier": "0.0.4", "gulp-jscs": "^1.4.0", "gulp-mocha": "^2.0.0", "gulp-util": "^3.0.3", "intelli-espower-loader": "^0.5.0", "istanbul": "^0.3.4", "lodash": "^3.3.0", "mocha": "^2.0.1", "power-assert": "^0.10.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "f4df301907a34586888dc79411b1813fda44b547", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-0.6.4.tgz", "integrity": "sha512-q5uD8uNd+H9i+nnJXMYjfLQECYwyLdGU955Atu8WLyBF/mYrgOj/tSSdMu6XN9T/e7sdRWrHyvHBkuiVU3e+1w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF9BmAhMjkRCSb+sTsWSd06n8p4eLo6Ue3w2UWT5N6IJAiAAqsC7bSjz4uPMJIbGPIaPI6X03cmdhCp3BZV75toJrg=="}]}}, "1.0.0": {"name": "neo-async", "version": "1.0.0", "devDependencies": {"async": "^0.9.0", "codecov.io": "0.0.8", "event-stream": "^3.3.0", "func-comparator": "^0.4.1", "gulp": "^3.8.11", "gulp-exit": "0.0.2", "gulp-git": "^1.2.0", "gulp-jsbeautifier": "0.0.4", "gulp-jscs": "^1.4.0", "gulp-mocha": "^2.0.0", "gulp-util": "^3.0.3", "intelli-espower-loader": "^0.5.0", "istanbul": "^0.3.4", "jsdoc": "^3.3.0-beta3", "lodash": "^3.6.0", "mocha": "^2.0.1", "neo-async": "^0.6.4", "power-assert": "^0.10.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "76a059fe032c6ea43fb0783215bd464709457165", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-1.0.0.tgz", "integrity": "sha512-q5cXeYwQTSsjxkADld1ivO2MAfA71hDyU4np9SNntmubQN8tz6m5lKhI8rYXNC6kFBBzyFvGZyeaHTW50WpGGA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDuMkVpwKeczs2YCO0G1zwG16bxjCeYzHnoLtNRLm8hTQIhAIq+uoB7tj2EcVIXgXlMRGy/w2CPh+FjMFEOBsLoIXJw"}]}}, "1.0.1": {"name": "neo-async", "version": "1.0.1", "devDependencies": {"async": "^0.9.0", "codecov.io": "0.0.8", "event-stream": "^3.3.0", "func-comparator": "^0.4.1", "gulp": "^3.8.11", "gulp-exit": "0.0.2", "gulp-git": "^1.2.0", "gulp-jsbeautifier": "0.0.4", "gulp-jscs": "^1.4.0", "gulp-mocha": "^2.0.0", "gulp-util": "^3.0.3", "intelli-espower-loader": "^0.5.0", "istanbul": "^0.3.4", "jsdoc": "^3.3.0-beta3", "lodash": "^3.6.0", "mocha": "^2.0.1", "neo-async": "^0.6.4", "power-assert": "^0.10.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "f5337efe3bb2fa9c9b168a21bd4b34fa13d6868e", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-1.0.1.tgz", "integrity": "sha512-dKLXfiTiRQv2E2fMp5V/AdsrE20cL/9lbNZSXmd3wQEOTzp/vBbyypXqX0oNYIIp+zlDLucIocnowli67r3xYg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDj4crNvP+5OZn4kJvhtppUQvb5sRkFJGhPc60iHIi2wAIgC1rREUrqt4R4LE+5zCGQzLO03gyKukkJH80EkqF8oTw="}]}}, "1.1.0": {"name": "neo-async", "version": "1.1.0", "devDependencies": {"async": "^0.9.0", "codecov.io": "0.0.8", "event-stream": "^3.3.0", "func-comparator": "^0.4.1", "gulp": "^3.8.11", "gulp-exit": "0.0.2", "gulp-git": "^1.2.0", "gulp-jsbeautifier": "0.0.4", "gulp-jscs": "^1.4.0", "gulp-mocha": "^2.0.0", "gulp-util": "^3.0.3", "intelli-espower-loader": "^0.5.0", "istanbul": "^0.3.4", "jsdoc": "^3.3.0-beta3", "lodash": "^3.6.0", "mocha": "^2.0.1", "neo-async": "^0.6.4", "power-assert": "^0.10.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "975c74e1a8d70ea78144b834bc49e0a79d1d1901", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-1.1.0.tgz", "integrity": "sha512-cigy0Mv2izN23LoCzFYaYHf+0hhc2n13GVR4GS50MXokgxZmdRBiiYJyQh/HzHlcr8W8O2mX7OpgRElOkdhaTg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB8eBdxIJpug7xI270Jq9Jxycjz7g9mZiFT3OXzEYg6VAiAFSNSn1VK86FU5m4YsoRAq3m1dVlUae87N72AXDl54Fw=="}]}}, "1.1.1": {"name": "neo-async", "version": "1.1.1", "devDependencies": {"async": "^0.9.0", "codecov.io": "^0.1.0", "event-stream": "^3.3.0", "func-comparator": "^0.4.1", "gulp": "^3.8.11", "gulp-exit": "0.0.2", "gulp-git": "^1.2.0", "gulp-jsbeautifier": "0.0.4", "gulp-jscs": "^1.4.0", "gulp-mocha": "^2.0.0", "gulp-util": "^3.0.3", "intelli-espower-loader": "^0.5.0", "istanbul": "^0.3.4", "jsdoc": "^3.3.0-beta3", "lodash": "^3.6.0", "mocha": "^2.0.1", "neo-async": "^0.6.4", "power-assert": "^0.10.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "06b34e45cdca048800fd4b5a19022d54816d6330", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-1.1.1.tgz", "integrity": "sha512-WQYj40LkHvAaPnVtG9CZQJvFTDmp6FB7UsdOXXyzr1EF5pMNR+uEBeMWdNaIIl17f7ypEkDaujOBmHk64rrLBQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCO8x3GKRvSJdtGfjwkub8EfFPb730gphX0QkjZlOIlqAIgd7otqaoCr2jhJ68FDXOeCpqxay2W7BXgx0he7pWnGO8="}]}}, "1.1.2": {"name": "neo-async", "version": "1.1.2", "devDependencies": {"async": "^0.9.0", "codecov.io": "^0.1.0", "event-stream": "^3.3.0", "func-comparator": "^0.4.1", "gulp": "^3.8.11", "gulp-exit": "0.0.2", "gulp-git": "^1.2.0", "gulp-jsbeautifier": "0.0.4", "gulp-jscs": "^1.4.0", "gulp-mocha": "^2.0.0", "gulp-util": "^3.0.3", "intelli-espower-loader": "^0.5.0", "istanbul": "^0.3.4", "jsdoc": "^3.3.0-beta3", "lodash": "^3.6.0", "mocha": "^2.0.1", "neo-async": "^0.6.4", "power-assert": "^0.10.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "3a1f32ce3364c40d5d4cb4125bd2a1dfa6af4d14", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-1.1.2.tgz", "integrity": "sha512-zFbWwqHeBDZj2HBfVHf5MWNSabTZ/AJkUqCwSjAryYKPeG0nTgvB6/Gz/i/4ZTLwj1ktbd0XB8onDO6KXwb5pw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDIpFx2h3P0FB5McErLX/yqWKG4JI4F4IsnMcl3lASQQQIga2bfbIWLH+PFtKCcy77LNnEm2S55CSoWqZAV5diTJPQ="}]}}, "1.2.0": {"name": "neo-async", "version": "1.2.0", "devDependencies": {"async": "^0.9.0", "codecov.io": "^0.1.0", "event-stream": "^3.3.0", "func-comparator": "^0.4.1", "gulp": "^3.8.11", "gulp-exit": "0.0.2", "gulp-git": "^1.2.0", "gulp-jsbeautifier": "0.0.4", "gulp-jscs": "^1.4.0", "gulp-mocha": "^2.0.0", "gulp-util": "^3.0.3", "intelli-espower-loader": "^0.5.0", "istanbul": "^0.3.4", "jsdoc": "^3.3.0-beta3", "lodash": "^3.6.0", "mocha": "^2.0.1", "neo-async": "^0.6.4", "power-assert": "^0.10.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "a078eadc9f5b30172d13af372a9ad9ae44d6327a", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-1.2.0.tgz", "integrity": "sha512-i8mKr1TxuhMKgOdRCm2rgLF7l2LjiK/6DozUq52n+6QW86EK73FqZn+eURUZw31eZgkD1pvJK61opgJ0//ehGw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCYFB55Onr6MO1x2rUwuc3Xfgn6ng/Q76MFaUjCPb5zFQIhAOi3XGSE0ZNhtdaV2Tf7jHOt8FBkCJ2Uu+0Tm6VGDvI8"}]}}, "0.6.5": {"name": "neo-async", "version": "0.6.5", "devDependencies": {"async": "^0.9.0", "codecov.io": "0.0.8", "event-stream": "^3.2.2", "func-comparator": "^0.4.1", "gulp": "^3.8.11", "gulp-exit": "0.0.2", "gulp-jsbeautifier": "0.0.4", "gulp-jscs": "^1.4.0", "gulp-mocha": "^2.0.0", "gulp-util": "^3.0.3", "intelli-espower-loader": "^0.5.0", "istanbul": "^0.3.4", "lodash": "^3.3.0", "mocha": "^2.0.1", "power-assert": "^0.10.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "c74b509f7d401009c90230345999e0c61c6798d4", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-0.6.5.tgz", "integrity": "sha512-PADC+y7cDYUbG8OymC6lEoQqOZ+86amSNkQu0JsRy4eyuB915TwFKMMRIjq7Ut7ZfU4KRnKk2sDSgZf30agprg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCCk8qoshMkq0JJnNs3i2kFE6fJ5A0Kq47VWNQC4z00XgIhAIjR4Z0N+vlPBq6sm9Z1TpSgx+sAC0wO8iFz8Uv0HQzl"}]}}, "1.2.1": {"name": "neo-async", "version": "1.2.1", "devDependencies": {"async": "^1.0.0", "changelog-generator": "0.0.2", "codecov.io": "^0.1.0", "event-stream": "^3.3.0", "func-comparator": "^0.6.1", "gulp": "^3.8.11", "gulp-exit": "0.0.2", "gulp-git": "^1.2.0", "gulp-jsbeautifier": "0.0.4", "gulp-jscs": "^1.4.0", "gulp-mocha": "^2.0.0", "gulp-util": "^3.0.3", "intelli-espower-loader": "^0.5.0", "istanbul": "^0.3.4", "jsdoc": "^3.3.0-beta3", "lodash": "^3.6.0", "mocha": "^2.0.1", "neo-async": "^0.6.4", "power-assert": "^0.10.0", "require-dir": "^0.3.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "1d74b90d66ce96c070a367f9a470182e284eb3d2", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-1.2.1.tgz", "integrity": "sha512-99A3Q18pH9idIKIZw2oApRu+W7MdAXh2kGWS6eyVb1UcftUZ2dO1xMF/2v7v4Vwj5SYyQogqPz7vRDHg+cIBOw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHuSwvOAIusSmSqxY3HlETLm6pnq7CpQ0JP8n0dxx//tAiB39FdFLycN0F6glMV3ha/vzodGqPSQ7krq1fy6nvZuQg=="}]}}, "1.3.0": {"name": "neo-async", "version": "1.3.0", "devDependencies": {"async": "^1.3.0", "benchmark": "^1.0.0", "changelog-generator": "0.0.2", "codecov.io": "^0.1.0", "event-stream": "^3.3.0", "func-comparator": "^0.6.1", "gulp": "^3.8.11", "gulp-exit": "0.0.2", "gulp-git": "^1.2.0", "gulp-jsbeautifier": "0.0.4", "gulp-jscs": "^1.4.0", "gulp-mocha": "^2.0.0", "gulp-util": "^3.0.3", "intelli-espower-loader": "^0.5.0", "istanbul": "^0.3.4", "jsdoc": "^3.3.0-beta3", "lodash": "^3.6.0", "minimist": "^1.1.1", "mocha": "^2.0.1", "neo-async": "^0.6.5", "power-assert": "^0.10.0", "require-dir": "^0.3.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "cff879521877f22478dc43cbab2650353b91d62b", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-1.3.0.tgz", "integrity": "sha512-XvuRApsXgwvKhf8JYyAL4cy8UmvjJhHEdrIMW+nZ4/Zl6veeeVlNPH59dbOp2VNML30Jdonb7rwMYNa7XltJrg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD1j2DKaN6yXz10XTFk+pxQqNitbTAzHi/Au9F74pMlQgIhAPvUYW04Jdw41fjezxmP9kpMoAAsHaieP6SyorT0VJOk"}]}}, "1.3.1": {"name": "neo-async", "version": "1.3.1", "devDependencies": {"async": "^1.3.0", "benchmark": "^1.0.0", "changelog-generator": "0.0.4", "codecov.io": "^0.1.0", "event-stream": "^3.3.0", "func-comparator": "^0.6.1", "gulp": "^3.8.11", "gulp-exit": "0.0.2", "gulp-git": "^1.2.0", "gulp-jsbeautifier": "0.0.4", "gulp-jscs": "^1.4.0", "gulp-mocha": "^2.0.0", "gulp-util": "^3.0.3", "intelli-espower-loader": "^0.5.0", "istanbul": "^0.3.4", "jsdoc": "^3.3.0-beta3", "lodash": "^3.6.0", "minimist": "^1.1.1", "mocha": "^2.0.1", "neo-async": "^0.6.5", "power-assert": "^0.10.0", "q": "~1.4.1", "require-dir": "^0.3.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "1e6bc30a6630d13d8eccc123d3152e4d9b810782", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-1.3.1.tgz", "integrity": "sha512-98c/JphK+s+n49r3gCWgYvHPwZsjRihxMbfJVAsh5QcXW7wQGFWw11MjebQrKoBauc9ecDAh2tskGS2H0bpolQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDFn5tfy3Z+ODUy5GuYxklsZFSG+XRbHYeN2vfvOzwNCAiBjfka4YePgRWr93C8d45E422enE+EXiOBAzRuo/CwOpA=="}]}}, "1.3.2": {"name": "neo-async", "version": "1.3.2", "devDependencies": {"async": "^1.3.0", "benchmark": "^1.0.0", "changelog-generator": "0.0.4", "codecov.io": "^0.1.0", "event-stream": "^3.3.0", "func-comparator": "^0.6.1", "gulp": "^3.8.11", "gulp-exit": "0.0.2", "gulp-git": "^1.2.0", "gulp-jsbeautifier": "0.0.4", "gulp-jscs": "^1.4.0", "gulp-mocha": "^2.0.0", "gulp-util": "^3.0.3", "intelli-espower-loader": "^0.5.0", "istanbul": "^0.3.4", "jsdoc": "^3.3.0-beta3", "lodash": "^3.6.0", "minimist": "^1.1.1", "mocha": "^2.0.1", "neo-async": "^0.6.5", "power-assert": "^0.10.0", "q": "~1.4.1", "require-dir": "^0.3.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "1011f27ae1750b03128d2ff6150bec359d152cf0", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-1.3.2.tgz", "integrity": "sha512-DEy137rE6js4QEFxj81qhZNOhA4kHP25DkAXpqmeKOkqNml/pNa/OzY6EPPwQoHOzMQ+6XEoO6X0f60msLElYA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB7wKu6q/s1WXQgfSaQPzOOs5LaFswuMyAd55jA4WrXNAiBtZvHbVEO1FNIEQ5TVT60xEXF8ez0prfPXNeAcG0NAzQ=="}]}}, "1.4.0": {"name": "neo-async", "version": "1.4.0", "devDependencies": {"async": "^1.4.0", "benchmark": "^1.0.0", "changelog-generator": "0.0.4", "codecov.io": "^0.1.0", "event-stream": "^3.3.0", "func-comparator": "^0.6.1", "gulp": "^3.8.11", "gulp-exit": "0.0.2", "gulp-git": "^1.2.0", "gulp-jsbeautifier": "0.0.4", "gulp-jscs": "^1.4.0", "gulp-mocha": "^2.0.0", "gulp-util": "^3.0.3", "intelli-espower-loader": "^0.5.0", "istanbul": "^0.3.4", "jsdoc": "^3.3.0-beta3", "lodash": "^3.6.0", "minimist": "^1.1.1", "mocha": "^2.0.1", "neo-async": "^0.6.5", "power-assert": "^0.10.0", "q": "~1.4.1", "require-dir": "^0.3.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "3922fb9348c597e4bd629613bf18351313c1dd57", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-1.4.0.tgz", "integrity": "sha512-A9Zvqf3FlrgmWaICQQ+/xzC5gNl+MlB1oJbgMaXmqfLydFmOAzR3hB3LhnWKO6O14kflz/j/iWgw0oEEVHO6yA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFtgKeNfbZtbN2tahvyvNBPIWo8mrp77Qi5OCLfHhTFpAiBKcFzFXzgzzYC9Bahp5xWcpzyLiV7TDAZ/sK8LR/TsJA=="}]}}, "1.4.1": {"name": "neo-async", "version": "1.4.1", "devDependencies": {"async": "^1.4.0", "benchmark": "^1.0.0", "changelog-generator": "0.0.4", "codecov.io": "^0.1.0", "event-stream": "^3.3.0", "func-comparator": "^0.6.1", "gulp": "^3.8.11", "gulp-exit": "0.0.2", "gulp-git": "^1.2.0", "gulp-jsbeautifier": "0.0.4", "gulp-jscs": "^1.4.0", "gulp-mocha": "^2.0.0", "gulp-util": "^3.0.3", "intelli-espower-loader": "^0.5.0", "istanbul": "^0.3.4", "jsdoc": "^3.3.0-beta3", "lodash": "^3.6.0", "minimist": "^1.1.1", "mocha": "^2.0.1", "neo-async": "^0.6.5", "power-assert": "^0.10.0", "q": "~1.4.1", "require-dir": "^0.3.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "4153cfdb8aa92ef5c2fe4b94058f9cb54644f23f", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-1.4.1.tgz", "integrity": "sha512-rkWGIa3GlMqii9prdQT42Jy23zmTuK4had9MKNwWbIieX6Ex0y0yq8p+L5G7eVDA2GivDI+jthNQfBdPaTgRog==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFESPekxCPJLWw7gKNrWcagTqrutDCEVql9yqjeVnLjFAiAQKa/FKM0Mfw+kFB0qP+ISTCZ4K+SADPAYsCilg8tdag=="}]}}, "1.5.0": {"name": "neo-async", "version": "1.5.0", "devDependencies": {"async": "^1.4.0", "benchmark": "^1.0.0", "changelog-generator": "0.0.4", "codecov.io": "^0.1.0", "event-stream": "^3.3.0", "func-comparator": "^0.6.1", "gulp": "^3.8.11", "gulp-exit": "0.0.2", "gulp-git": "^1.2.0", "gulp-jsbeautifier": "0.0.4", "gulp-jscs": "^1.4.0", "gulp-mocha": "^2.0.0", "gulp-util": "^3.0.3", "intelli-espower-loader": "^0.5.0", "istanbul": "^0.3.4", "jsdoc": "^3.3.0-beta3", "lodash": "^3.6.0", "minimist": "^1.1.1", "mocha": "^2.0.1", "neo-async": "^0.6.5", "power-assert": "^0.10.0", "q": "~1.4.1", "require-dir": "^0.3.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "0e4146725882aa6a5f38dc1fd8518af498414a1f", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-1.5.0.tgz", "integrity": "sha512-9X9DrI5ipH4ant8Zk0rt0hmZaE4Iq41Kpq0xo3Zf5k0RFc+u2/plTP9E3C/H4hyvlli4ChmpCyXdlk8WY5G/Xg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCRtTo8eNc+o4PmABoS79CvUSTzfN+X4QLlLqddFvKTmgIgA+IBgQdKUS4tRXYoqAJ4iqmQ8lHjZhA7QrdCBLoL+HY="}]}}, "1.5.1": {"name": "neo-async", "version": "1.5.1", "devDependencies": {"async": "^1.4.0", "benchmark": "^1.0.0", "changelog-generator": "0.0.4", "codecov.io": "^0.1.0", "event-stream": "^3.3.0", "func-comparator": "^0.6.1", "gulp": "^3.8.11", "gulp-exit": "0.0.2", "gulp-git": "^1.2.0", "gulp-jsbeautifier": "0.0.4", "gulp-jscs": "^1.4.0", "gulp-mocha": "^2.0.0", "gulp-util": "^3.0.3", "intelli-espower-loader": "^0.5.0", "istanbul": "^0.3.4", "jsdoc": "^3.3.0-beta3", "lodash": "^3.6.0", "minimist": "^1.1.1", "mocha": "^2.0.1", "mocha.parallel": "^0.7.1", "neo-async": "^0.6.5", "power-assert": "^0.10.0", "q": "~1.4.1", "require-dir": "^0.3.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "56357b0c01f1d21fde8ac866b5fdcd864ac45688", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-1.5.1.tgz", "integrity": "sha512-dI2xIZw/J66dBamXsqNeeoLZTFu2akbI7O/zSRpe8/UFvB8P272yI6JpzdvVBNVxCVm0rNc8CBjDilTxUuh5ew==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFhOutCY5tgXQxr1gSx0DaC6FawYvl6wo8LrtErBTrNIAiEAz+azX9z8uBED5lgkTT9V+oBcXgrkQMY5qd0a0qm4kh4="}]}}, "1.6.0": {"name": "neo-async", "version": "1.6.0", "devDependencies": {"async": "^1.4.0", "benchmark": "^1.0.0", "changelog-generator": "0.0.4", "codecov.io": "^0.1.0", "event-stream": "^3.3.0", "func-comparator": "^0.6.1", "gulp": "^3.8.11", "gulp-exit": "0.0.2", "gulp-git": "^1.2.0", "gulp-jsbeautifier": "0.0.4", "gulp-jscs": "^1.4.0", "gulp-mocha": "^2.0.0", "gulp-util": "^3.0.3", "intelli-espower-loader": "^0.5.0", "istanbul": "^0.3.4", "jsdoc": "^3.3.0-beta3", "lodash": "^3.6.0", "minimist": "^1.1.1", "mocha": "^2.0.1", "mocha.parallel": "^0.7.1", "neo-async": "^0.6.5", "power-assert": "^0.10.0", "q": "~1.4.1", "require-dir": "^0.3.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "81b14bb2e467acfc3424020fad8d2c42d1cfd50e", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-1.6.0.tgz", "integrity": "sha512-rKyESl4IX3IsDEBu1kSwhtcotu3bpsQZXa7UCgarSwx+d2FD1dd4kwb+tDtVBvm/D4K9hecozomGAAbI4Morgg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC9Um19hZhf3Zqt8z+o+bcnW3C8xWcMlFbr9Qr6FVnEAQIgcFQ7h7odwMyoSviH/chVFIR450mWqWAhSSGFOCz9hKQ="}]}}, "1.7.0": {"name": "neo-async", "version": "1.7.0", "devDependencies": {"async": "^1.4.0", "benchmark": "^1.0.0", "changelog-generator": "0.0.4", "codecov.io": "^0.1.0", "event-stream": "^3.3.0", "func-comparator": "^0.6.1", "gulp": "^3.8.11", "gulp-exit": "0.0.2", "gulp-git": "^1.2.0", "gulp-jsbeautifier": "0.0.4", "gulp-jscs": "^1.4.0", "gulp-mocha": "^2.0.0", "gulp-util": "^3.0.3", "intelli-espower-loader": "^0.5.0", "istanbul": "^0.3.4", "jsdoc": "^3.3.0-beta3", "lodash": "^3.6.0", "minimist": "^1.1.1", "mocha": "^2.0.1", "mocha.parallel": "^0.7.1", "neo-async": "^0.6.5", "power-assert": "^0.10.0", "q": "~1.4.1", "require-dir": "^0.3.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "8eca240d6f2abfa53aae7adb8649028894486544", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-1.7.0.tgz", "integrity": "sha512-5QhOFtVgVWTg6hQyzL2EzLZ2z12Hh5AnvgMcrBNXjOIWEk/KoKxoiOTillMXqM2d7YOWIJDbUVovmZEqiDH+FA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDhTpiH2V5CAFfJUTYMkhMpRYl5ye323Wtgrj7LhsHbewIhAN6JrdpSHW5rirHVsmbhbRSfqxCT/X1mLjmEtlHZiWn4"}]}}, "1.7.1": {"name": "neo-async", "version": "1.7.1", "devDependencies": {"async": "^1.4.0", "benchmark": "^1.0.0", "changelog-generator": "0.0.4", "codecov.io": "^0.1.0", "event-stream": "^3.3.0", "func-comparator": "^0.6.1", "gulp": "^3.8.11", "gulp-exit": "0.0.2", "gulp-git": "^1.2.0", "gulp-jsbeautifier": "0.0.4", "gulp-jscs": "^1.4.0", "gulp-mocha": "^2.0.0", "gulp-util": "^3.0.3", "intelli-espower-loader": "^0.5.0", "istanbul": "^0.3.4", "jsdoc": "^3.3.0-beta3", "lodash": "^3.6.0", "minimist": "^1.1.1", "mocha": "^2.0.1", "mocha.parallel": "^0.7.1", "neo-async": "^0.6.5", "power-assert": "^0.10.0", "q": "~1.4.1", "require-dir": "^0.3.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "37686a5812070d0b08877d519f6cafda9bc6d6c3", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-1.7.1.tgz", "integrity": "sha512-5r/asn54iX1b1ENq8ViyQzcOQJulz9aFvlnatcm2N66+XZOVFVo7BqVSdtKkhaZshlRU8Ca4pQxNSAe4Rz5x6A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC/eHWYzelkjJKaw0OEzn2LY1kC4iulBCjU8D3Q9qB78wIhAIj0NEGJyw9veVJJw33Scpqs75d93dCpS6GOlQsxL/7k"}]}}, "1.7.2": {"name": "neo-async", "version": "1.7.2", "devDependencies": {"async": "^1.4.0", "benchmark": "^1.0.0", "changelog-generator": "0.0.4", "codecov.io": "^0.1.0", "event-stream": "^3.3.0", "func-comparator": "^0.6.1", "gulp": "^3.8.11", "gulp-exit": "0.0.2", "gulp-git": "^1.2.0", "gulp-jsbeautifier": "0.0.4", "gulp-jscs": "^1.4.0", "gulp-mocha": "^2.0.0", "gulp-util": "^3.0.3", "intelli-espower-loader": "^0.5.0", "istanbul": "^0.3.4", "jsdoc": "^3.3.0-beta3", "lodash": "^3.6.0", "minimist": "^1.1.1", "mocha": "^2.0.1", "mocha.parallel": "^0.7.1", "neo-async": "^0.6.5", "power-assert": "^0.10.0", "q": "~1.4.1", "require-dir": "^0.3.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "e220d9c293602ac90c6c22a021dffabe96409423", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-1.7.2.tgz", "integrity": "sha512-dYPNjXW1xsgrm5VgQIJIld6PAmqmW1+nU6Ka70Gn4uCbCZLO9GrXqAiKRMreGzoE17oyzzgsZ5aQCZrD3mt+wQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHA5Hp85D2O9066a1PnamNZHuxy4abkbbWLeIeqJi1cbAiByToy7RiRg7fT7GqUGuDMY7hyWicyIwnf59e5odUn7+g=="}]}}, "1.7.3": {"name": "neo-async", "version": "1.7.3", "devDependencies": {"async": "^1.4.0", "benchmark": "^1.0.0", "changelog-generator": "0.0.4", "codecov.io": "^0.1.0", "event-stream": "^3.3.0", "func-comparator": "^0.6.1", "gulp": "^3.8.11", "gulp-exit": "0.0.2", "gulp-git": "^1.2.0", "gulp-jsbeautifier": "0.0.4", "gulp-jscs": "^1.4.0", "gulp-mocha": "^2.0.0", "gulp-util": "^3.0.3", "intelli-espower-loader": "^0.5.0", "istanbul": "^0.3.4", "jsdoc": "^3.3.0-beta3", "lodash": "^3.6.0", "minimist": "^1.1.1", "mocha": "^2.0.1", "mocha.parallel": "^0.7.1", "neo-async": "^0.6.5", "power-assert": "^0.10.0", "q": "~1.4.1", "require-dir": "^0.3.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "875d0f1f050fa6fea39583c55d44185991117146", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-1.7.3.tgz", "integrity": "sha512-wY+/4/BlASAz0LKBRSGs4w+eEKTFvKqWgyhPavSJJL104jzpeGDshJyOjiZBb30y6CcMbw2EmOa1o8Tpz6E/Nw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCGbAVnsfYSutI3o2IjXaZyZCLGA5kJbmTAzs57PJgu5gIhANjBVUBWCDANoXA4GY+pT5YJeIlcQysuJysX0nPYUVZw"}]}}, "1.7.4": {"name": "neo-async", "version": "1.7.4", "devDependencies": {"async": "^1.4.0", "benchmark": "^1.0.0", "changelog-generator": "0.0.4", "codecov.io": "^0.1.0", "event-stream": "^3.3.0", "func-comparator": "^0.6.1", "gulp": "^3.8.11", "gulp-exit": "0.0.2", "gulp-git": "^1.2.0", "gulp-jsbeautifier": "0.0.4", "gulp-jscs": "^1.4.0", "gulp-mocha": "^2.0.0", "gulp-util": "^3.0.3", "intelli-espower-loader": "^0.5.0", "istanbul": "^0.3.4", "jsdoc": "^3.3.0-beta3", "lodash": "^3.6.0", "minimist": "^1.1.1", "mocha": "^2.0.1", "mocha.parallel": "^0.7.1", "neo-async": "^0.6.5", "power-assert": "^0.10.0", "q": "~1.4.1", "require-dir": "^0.3.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "ca758808a42015ba3e719d66a09b8765da3d91f4", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-1.7.4.tgz", "integrity": "sha512-+3d8KbXKy+uuMJ+ud6HYU93PrL4lvDHXeFfVGlkb0XT1G4HlwVrm2O9x/sqmfCfwTuCeWmqaLioJmQvTgMrseg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIETKl16pBrcK/wZY7thqDvWGIuMn6ShQnoc8SenHIuM4AiB3soGynODR3+ibzOM380coPcpqqQRaC0kHs7VWIM3E0Q=="}]}}, "1.7.5": {"name": "neo-async", "version": "1.7.5", "devDependencies": {"async": "^1.4.0", "benchmark": "^1.0.0", "changelog-generator": "0.0.4", "codecov.io": "^0.1.0", "event-stream": "^3.3.0", "func-comparator": "^0.6.1", "gulp": "^3.8.11", "gulp-exit": "0.0.2", "gulp-git": "^1.2.0", "gulp-jsbeautifier": "0.0.4", "gulp-jscs": "^1.4.0", "gulp-mocha": "^2.0.0", "gulp-util": "^3.0.3", "intelli-espower-loader": "^0.5.0", "istanbul": "^0.3.4", "jsdoc": "^3.3.0-beta3", "lodash": "^3.6.0", "minimist": "^1.1.1", "mocha": "^2.0.1", "mocha.parallel": "^0.7.1", "neo-async": "^0.6.5", "power-assert": "^0.10.0", "q": "~1.4.1", "require-dir": "^0.3.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "50a0c31605c38d8b1b92d81dc850717facdb592b", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-1.7.5.tgz", "integrity": "sha512-TLIKwrM4FQd9q2not2/UMMD+R9An0u2hoZ/8T7q+irWx2tf5fyZhKQfqpH+HMnMKIb3ivU4tvi7YJ9EdJM39Xg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICToEsZmA3Yy7wo3dQjEUWghyseLtj+pJxOm9VaN2g73AiBguyY7DYsXlHc+KcsxDZRy51QWNCoIYw9+2s6873UltA=="}]}}, "1.8.0": {"name": "neo-async", "version": "1.8.0", "devDependencies": {"async": "^1.4.0", "benchmark": "^1.0.0", "changelog-generator": "0.0.4", "codecov.io": "^0.1.0", "event-stream": "^3.3.0", "func-comparator": "^0.6.1", "gulp": "^3.8.11", "gulp-exit": "0.0.2", "gulp-git": "^1.2.0", "gulp-jsbeautifier": "0.0.4", "gulp-jscs": "^1.4.0", "gulp-mocha": "^2.0.0", "gulp-util": "^3.0.3", "intelli-espower-loader": "^0.5.0", "istanbul": "^0.3.4", "jsdoc": "^3.3.0-beta3", "lodash": "^3.6.0", "minimist": "^1.1.1", "mocha": "^2.0.1", "mocha.parallel": "^0.7.1", "neo-async": "^0.6.5", "power-assert": "^0.10.0", "q": "~1.4.1", "require-dir": "^0.3.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "6318daf23126073401688ea6ac92918cce0f9977", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-1.8.0.tgz", "integrity": "sha512-rHGhw0mBjG7O1gtN2UeL4pk7aoIBEZ+mMXifvm+gVg77oyJFgNdc6yHA/Vq0uUX1jm4b4xJpOuuQUB//SpdOKg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFc4rhgZAEqohsuB492GDCuuK8loCFy1wW5cy0rzw4b0AiA3GcaMNb0L2u3WtCp2K4m/NPBEqhY75kyHXZRmL6vSCA=="}]}}, "1.8.1": {"name": "neo-async", "version": "1.8.1", "devDependencies": {"async": "^1.4.0", "benchmark": "^1.0.0", "changelog-generator": "0.0.4", "codecov.io": "^0.1.0", "event-stream": "^3.3.0", "func-comparator": "^0.6.1", "gulp": "^3.8.11", "gulp-exit": "0.0.2", "gulp-git": "^1.2.0", "gulp-jsbeautifier": "0.0.4", "gulp-jscs": "^1.4.0", "gulp-mocha": "^2.0.0", "gulp-util": "^3.0.3", "intelli-espower-loader": "^0.5.0", "istanbul": "^0.3.4", "jsdoc": "^3.3.0-beta3", "lodash": "^3.6.0", "minimist": "^1.1.1", "mocha": "^2.0.1", "mocha.parallel": "^0.7.1", "neo-async": "^0.6.5", "power-assert": "^0.10.0", "q": "~1.4.1", "require-dir": "^0.3.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "9a8b2bd3c532d263a8a3271ffe858d5af21bbfed", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-1.8.1.tgz", "integrity": "sha512-/wYVScYeSZs0xumAomGIMIkYX2sYDm3U2XaQqsqeYyrQ5m8IPaSkHGRZiuKKWUdv8KclggK3TrNS3qIknqjR/A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHEQ6f8yBnVAA+9En4u0/N3QLYWbrZlFlslQDUCz/qhkAiBO3+G7t1uCkhpC/pHVvH3FRqOY1964S0K2paWgokpAEA=="}]}}, "1.8.2": {"name": "neo-async", "version": "1.8.2", "devDependencies": {"async": "^1.4.0", "benchmark": "^1.0.0", "changelog-generator": "0.0.4", "codecov.io": "^0.1.0", "event-stream": "^3.3.0", "func-comparator": "^0.6.1", "gulp": "^3.8.11", "gulp-exit": "0.0.2", "gulp-git": "^1.2.0", "gulp-jsbeautifier": "0.0.4", "gulp-jscs": "^1.4.0", "gulp-mocha": "^2.0.0", "gulp-util": "^3.0.3", "intelli-espower-loader": "^0.5.0", "istanbul": "^0.3.4", "jsdoc": "^3.3.0-beta3", "lodash": "^3.6.0", "minimist": "^1.1.1", "mocha": "^2.0.1", "mocha.parallel": "^0.7.1", "neo-async": "^0.6.5", "power-assert": "^0.10.0", "q": "~1.4.1", "require-dir": "^0.3.0"}, "directories": {"test": "test/"}, "dist": {"shasum": "31795888b79dd04357a7c52113a65183e93b6735", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-1.8.2.tgz", "integrity": "sha512-KcnnB2lXd2H1/HqOsMBnZCW227F4nRgQQnVWRmkPTKx/t7rdwiPTHguWCBHAM0VQyanjHs5wAVbPN/mhqu2c5A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC5mAShWj4E3AKmHTWdgXPWbT6vSavB3WylmTy91UuFpAiAT2wdUApMPZxFppWL5mmlOTfdJ257FVAbaAb786RjVRw=="}]}}, "2.0.0-rc.1": {"name": "neo-async", "version": "2.0.0-rc.1", "devDependencies": {"async": "git://github.com/caolan/async.git", "benchmark": "^2.1.0", "changelog-generator": "0.0.4", "codecov.io": "^0.1.6", "event-stream": "^3.3.0", "func-comparator": "^0.7.0", "gulp": "^3.9.1", "gulp-exit": "0.0.2", "gulp-git": "^1.7.1", "gulp-jsbeautifier": "^2.0.3", "gulp-jscs": "^3.0.2", "gulp-mocha": "^2.2.0", "gulp-util": "^3.0.7", "istanbul": "^0.4.3", "jsdoc": "^3.4.0", "jshint": "^2.9.2", "lodash": "^4.11.2", "minimist": "^1.2.0", "mocha": "^2.4.5", "mocha.parallel": "^0.12.0", "neo-async": "^0.6.5", "q": "^1.4.1", "require-dir": "^0.3.0"}, "dist": {"shasum": "79de2ab0b172cb0dd42d1b90a7e68cb4f37dd516", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-2.0.0-rc.1.tgz", "integrity": "sha512-Q7+eev/WbYGOeL5nLgdt59pDQRcwe1DBybztV4J8VLPbWcJFNOXgrUuvCl6XTHiD+uSZ9xPuebVgwlhPC+PnFA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCfl/Tdy+JwgwzuHZUrQ2bXiAQ1p9qmh8y8HewFyxTPdAIhAMaj9egc84r6rhIoWWaMqKU/f+rmAEhwn1yRB2JvO/Aq"}]}}, "2.0.0": {"name": "neo-async", "version": "2.0.0", "devDependencies": {"async": "git://github.com/caolan/async.git", "benchmark": "^2.1.1", "bluebird": "^3.4.1", "changelog-generator": "0.0.4", "codecov.io": "^0.1.6", "event-stream": "^3.3.3", "func-comparator": "^0.7.2", "gulp": "^3.9.1", "gulp-exit": "0.0.2", "gulp-git": "^1.8.0", "gulp-jsbeautifier": "^2.0.3", "gulp-jscs": "^4.0.0", "gulp-mocha": "^2.2.0", "gulp-util": "^3.0.7", "istanbul": "^0.4.3", "jsdoc": "^3.4.0", "jshint": "^2.9.2", "lodash": "^4.13.1", "minimist": "^1.2.0", "mocha": "^2.5.3", "mocha-parallel-executor": "^0.2.1", "mocha.parallel": "^0.12.0", "require-dir": "^0.3.0", "run-sequence": "^1.2.2"}, "dist": {"shasum": "ec6babe4f1c30fdc817f9186ae19e446cb804968", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-2.0.0.tgz", "integrity": "sha512-cZ5oEEQDcBeoyNJzO9uyGIH+qxQ7ha5qA4bDvugiDSGUmOZlrwwaPHGa5D7IgQFFEuhWG2z9lDo1IDLv5XUz1A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGxLYPSGpEckf/VRgMHGJtrxWnGQZEvzfpUYX4WktdEtAiBfYtZA8NWWyf94P2vWXyJ6Uaj/te1odKYoxTs5n9FACA=="}]}}, "2.0.1": {"name": "neo-async", "version": "2.0.1", "devDependencies": {"async": "git://github.com/caolan/async.git", "benchmark": "^2.1.1", "bluebird": "^3.4.1", "changelog-generator": "0.0.4", "codecov.io": "^0.1.6", "event-stream": "^3.3.3", "func-comparator": "^0.7.2", "gulp": "^3.9.1", "gulp-exit": "0.0.2", "gulp-git": "^1.10.0", "gulp-jsbeautifier": "^2.0.3", "gulp-jscs": "^4.0.0", "gulp-mocha": "^3.0.0", "gulp-util": "^3.0.7", "istanbul": "^0.4.3", "jsdoc": "^3.4.0", "jshint": "^2.9.2", "lodash": "^4.14.0", "minimist": "^1.2.0", "mocha": "^3.0.0", "mocha-parallel-executor": "^0.3.0", "mocha.parallel": "^0.12.0", "require-dir": "^0.3.0", "run-sequence": "^1.2.2"}, "dist": {"shasum": "641707f379ca589f460869097bac4905a9c88a31", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-2.0.1.tgz", "integrity": "sha512-9fiUUhbZQXiJLYqMzjxdiOYrs+7tmAwxPKN839qs2urM45fjbGWeJegqQIkQFn1EQ9R2RRuUhQtLBlbJj/b/GQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBIfncBbO+RUnDgaLDWYLJxZz5jKBONJeL7UKIe/LhlwAiBEnYLLJkpyQs929iGuNr0WbTUFxUFAQiZ9e9AFnDzjdw=="}]}}, "2.1.0": {"name": "neo-async", "version": "2.1.0", "devDependencies": {"async": "git://github.com/caolan/async.git", "benchmark": "^2.1.1", "bluebird": "^3.4.7", "changelog-generator": "0.0.4", "codecov.io": "^0.1.6", "event-stream": "^3.3.3", "func-comparator": "^0.7.2", "gulp": "^3.9.1", "gulp-exit": "0.0.2", "gulp-git": "^1.10.0", "gulp-jsbeautifier": "^2.0.3", "gulp-jscs": "^4.0.0", "gulp-mocha": "^3.0.0", "gulp-util": "^3.0.7", "istanbul": "^0.4.3", "jsdoc": "^3.4.0", "jshint": "^2.9.2", "lodash": "^4.16.6", "minimist": "^1.2.0", "mocha": "^3.1.0", "mocha-parallel-executor": "^0.3.0", "mocha.parallel": "^0.15.0", "require-dir": "^0.3.0", "run-sequence": "^1.2.2"}, "dist": {"shasum": "54e01a060b84b8731c62778b8aa9341892662c89", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-2.1.0.tgz", "integrity": "sha512-/fiCcWmP9wka1ZM6+/sbqTjBCgEKh4m++ARH8cvNBN5y85xiTeyiCGNLAG8cXH1QtMN+sbBiKt5aG9mC3X47dA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCzZZ21bB5GtNJWBXJlIJXfzOycHUaThSQlDcrZQcolXwIgcsS/lVrljgibOz2qUnmgYkM55GMyyBag/5g5m/cePf8="}]}}, "2.2.0": {"name": "neo-async", "version": "2.2.0", "devDependencies": {"async": "git://github.com/caolan/async.git", "benchmark": "^2.1.1", "bluebird": "^3.4.7", "changelog-generator": "0.0.4", "codecov.io": "^0.1.6", "event-stream": "^3.3.3", "func-comparator": "^0.7.2", "gulp": "^3.9.1", "gulp-bump": "^2.7.0", "gulp-exit": "0.0.2", "gulp-git": "^2.3.0", "gulp-jsbeautifier": "^2.0.3", "gulp-jscs": "^4.0.0", "gulp-mocha": "^4.2.0", "gulp-tag-version": "^1.3.0", "gulp-util": "^3.0.7", "istanbul": "^0.4.3", "jsdoc": "^3.4.0", "jshint": "^2.9.2", "lodash": "^4.16.6", "minimist": "^1.2.0", "mocha": "^3.1.0", "mocha-parallel-executor": "^0.3.0", "mocha.parallel": "^0.15.0", "require-dir": "^0.3.0", "run-sequence": "^1.2.2"}, "dist": {"shasum": "eb7cb7dbb85b821ce5168d0c65f9029e5c9b4648", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-2.2.0.tgz", "integrity": "sha512-tFll3WV5fenC6IaZX3lXxqo5lJp6MZZc3NkPff3kSitgTTpXYZAPRvmUGxM5InvmWwED/oeRd/tges2E94ILkg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCpgGpXmdtko5BWl6R3rlHrQrBIFsM5tpORD/7NhXiJJwIhAL4vklRIGvrx8YBxWfj8HKHA7csUvdu/oM9+RAYTP/Yw"}]}}, "2.2.2": {"name": "neo-async", "version": "2.2.2", "devDependencies": {"async": "git://github.com/caolan/async.git", "benchmark": "^2.1.1", "bluebird": "^3.4.7", "changelog-generator": "0.0.4", "codecov.io": "^0.1.6", "event-stream": "^3.3.3", "func-comparator": "^0.7.2", "gulp": "^3.9.1", "gulp-bump": "^2.7.0", "gulp-exit": "0.0.2", "gulp-git": "^2.3.0", "gulp-jsbeautifier": "^2.0.3", "gulp-jscs": "^4.0.0", "gulp-mocha": "^4.2.0", "gulp-tag-version": "^1.3.0", "gulp-util": "^3.0.7", "istanbul": "^0.4.3", "jsdoc": "^3.4.0", "jshint": "^2.9.2", "lodash": "^4.16.6", "minimist": "^1.2.0", "mocha": "^3.1.0", "mocha-parallel-executor": "^0.3.0", "mocha.parallel": "^0.15.0", "require-dir": "^0.3.0", "run-sequence": "^1.2.2"}, "dist": {"integrity": "sha512-13J1Tbgzi4h3KphInj3naEsZi+ZCo0q09DCh/nzig2eQdbNxpC7vR03dxE0KGJQsI9eglm8W7gxlKOdFyVSxqA==", "shasum": "202694aa9f66c85e8c3893c630ecb15453da5471", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-2.2.2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIApZ+yx4DK7DnYQFRW5ZYfmUc/BcMWODyqpTspnR0gH3AiBTWgGjrcC/XyVsxCCGH+Juk4KKHpB6KAfHLbYpziG6Xw=="}]}}, "2.3.0": {"name": "neo-async", "version": "2.3.0", "devDependencies": {"async": "^2.5.0", "benchmark": "^2.1.1", "bluebird": "^3.4.7", "changelog-generator": "0.0.4", "codecov.io": "^0.1.6", "event-stream": "^3.3.3", "func-comparator": "^0.7.2", "gulp": "^3.9.1", "gulp-bump": "^2.7.0", "gulp-exit": "0.0.2", "gulp-git": "^2.3.0", "gulp-jsbeautifier": "^2.0.3", "gulp-jscs": "^4.0.0", "gulp-mocha": "^4.2.0", "gulp-tag-version": "^1.3.0", "gulp-util": "^3.0.7", "istanbul": "^0.4.3", "jsdoc": "^3.4.0", "jshint": "^2.9.2", "lodash": "^4.16.6", "minimist": "^1.2.0", "mocha": "^3.1.0", "mocha-parallel-executor": "^0.3.0", "mocha.parallel": "^0.15.0", "require-dir": "^0.3.0", "run-sequence": "^1.2.2"}, "dist": {"integrity": "sha512-uosU5x49SkCn02FFxK3xtWUl+UJUgmeRVh+bnFQZW1Cu9MwjCWBZ7LYWQAN+04coieDj7HcAnz9uZWbAhQMFVA==", "shasum": "24798bdfbe9f33ad29ad121e51c4e55726536638", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-2.3.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDQ6at3SlE1M7qAbeZVoW6Z92vQX7a5Ha0qqsNkvPne0wIhAOrLqLF70NxOBF3FSTTSGfqKnmzSvS/SEYzikMJWv2/y"}]}}, "2.4.0-0": {"name": "neo-async", "version": "2.4.0-0", "devDependencies": {"aigle": "^1.7.0", "async": "^2.5.0", "benchmark": "^2.1.1", "bluebird": "^3.4.7", "changelog-generator": "0.0.4", "codecov.io": "^0.1.6", "event-stream": "^3.3.3", "fs-extra": "^4.0.1", "func-comparator": "^0.7.2", "gulp": "^3.9.1", "gulp-bump": "^2.7.0", "gulp-exit": "0.0.2", "gulp-git": "^2.3.0", "gulp-jsbeautifier": "^2.0.3", "gulp-jscs": "^4.0.0", "gulp-mocha": "^4.2.0", "gulp-tag-version": "^1.3.0", "gulp-util": "^3.0.7", "istanbul": "^0.4.3", "jsdoc": "^3.4.0", "jshint": "^2.9.2", "lodash": "^4.16.6", "minimist": "^1.2.0", "mocha": "^3.1.0", "mocha-parallel-executor": "^0.3.0", "mocha.parallel": "^0.15.0", "require-dir": "^0.3.0", "run-sequence": "^1.2.2"}, "dist": {"integrity": "sha512-SiViWDo8FCeVexN6opVDJfO7/mQz5Yl+fiAvPeHVUCdBJEpzzQODmdssfxolMTW0sEPPq9v/JEt2q8mY3BAHbA==", "shasum": "2ead44febc45642e8f3c1b29c7cd25274a5039fb", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-2.4.0-0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFbd9FPBK+lLmeL51LZ5fbd/54QbXod1Bnzw9EMWjE9WAiEA+qlf2JE8BXYTJ22QXaqWePF7QASSWIxP/yuAeEJ8+yM="}]}}, "2.4.0": {"name": "neo-async", "version": "2.4.0", "devDependencies": {"aigle": "^1.7.0", "async": "^2.5.0", "benchmark": "^2.1.1", "bluebird": "^3.4.7", "changelog-generator": "0.0.4", "codecov.io": "^0.1.6", "event-stream": "^3.3.3", "fs-extra": "^4.0.1", "func-comparator": "^0.7.2", "gulp": "^3.9.1", "gulp-bump": "^2.7.0", "gulp-exit": "0.0.2", "gulp-git": "^2.3.0", "gulp-jsbeautifier": "^2.0.3", "gulp-jscs": "^4.0.0", "gulp-mocha": "^4.2.0", "gulp-tag-version": "^1.3.0", "gulp-util": "^3.0.7", "istanbul": "^0.4.3", "jsdoc": "^3.4.0", "jshint": "^2.9.2", "lodash": "^4.16.6", "minimist": "^1.2.0", "mocha": "^3.1.0", "mocha-parallel-executor": "^0.3.0", "mocha.parallel": "^0.15.0", "require-dir": "^0.3.0", "run-sequence": "^1.2.2"}, "dist": {"integrity": "sha512-RroV5OfVwGt7hQPZH4J5UIWKwpyIL1r4kfLQC+aDE17Ftc1T8KXAKxpvHXS3qKkxDHAVxYFVWYbpXIxvCq7HWw==", "shasum": "0254ed156d2c264a45863f2d12b76492131da757", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-2.4.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHqMt181FCPkKmpn8mQVf9szZ/ergYchNhxhoAZzw4OkAiEAyvhwihVQ58pAu5tpkhhS519L94eV8+AeeynoNyflqgM="}]}}, "2.5.0": {"name": "neo-async", "version": "2.5.0", "devDependencies": {"aigle": "^1.7.0", "async": "^2.5.0", "benchmark": "^2.1.1", "bluebird": "^3.4.7", "changelog-generator": "0.0.4", "codecov.io": "^0.1.6", "event-stream": "^3.3.3", "fs-extra": "^4.0.1", "func-comparator": "^0.7.2", "gulp": "^3.9.1", "gulp-bump": "^2.7.0", "gulp-exit": "0.0.2", "gulp-git": "^2.3.0", "gulp-jsbeautifier": "^2.0.3", "gulp-jscs": "^4.0.0", "gulp-mocha": "^4.2.0", "gulp-tag-version": "^1.3.0", "gulp-util": "^3.0.7", "istanbul": "^0.4.3", "jsdoc": "^3.5.5", "jshint": "^2.9.2", "lodash": "^4.16.6", "minimist": "^1.2.0", "mocha": "^3.1.0", "mocha-parallel-executor": "^0.3.0", "mocha.parallel": "^0.15.0", "require-dir": "^0.3.0", "run-sequence": "^1.2.2"}, "dist": {"integrity": "sha512-nJmSswG4As/MkRq7QZFuH/sf/yuv8ODdMZrY4Bedjp77a5MK4A6s7YbBB64c9u79EBUOfXUXBvArmvzTD0X+6g==", "shasum": "76b1c823130cca26acfbaccc8fbaf0a2fa33b18f", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-2.5.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGoaACl4pfBSq+zEky6skrocMZUvlkU/75keEyqNJpCPAiEAvpnO3ChDz00G0LnA3r/M//U2tqIYWRlRxbswgM8PiL4="}]}}, "2.5.1": {"name": "neo-async", "version": "2.5.1", "devDependencies": {"aigle": "^1.8.1", "async": "^2.6.0", "benchmark": "^2.1.1", "bluebird": "^3.5.1", "changelog-generator": "0.0.4", "codecov.io": "^0.1.6", "event-stream": "^3.3.3", "fs-extra": "^4.0.2", "func-comparator": "^0.7.2", "gulp": "^3.9.1", "gulp-bump": "^2.8.0", "gulp-exit": "0.0.2", "gulp-git": "^2.4.2", "gulp-jsbeautifier": "^2.1.1", "gulp-jscs": "^4.0.0", "gulp-mocha": "^4.2.0", "gulp-tag-version": "^1.3.0", "gulp-util": "^3.0.7", "istanbul": "^0.4.3", "jsdoc": "^3.5.5", "jshint": "^2.9.5", "lodash": "^4.16.6", "minimist": "^1.2.0", "mocha": "^3.5.3", "mocha-parallel-executor": "^0.3.0", "mocha.parallel": "^0.15.3", "require-dir": "^0.3.0", "run-sequence": "^1.2.2"}, "dist": {"integrity": "sha512-3KL3fvuRkZ7s4IFOMfztb7zJp3QaVWnBeGoJlgB38XnCRPj/0tLzzLG5IB8NYOHbJ8g8UGrgZv44GLDk6CxTxA==", "shasum": "acb909e327b1e87ec9ef15f41b8a269512ad41ee", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-2.5.1.tgz", "fileCount": 118, "unpackedSize": 289746, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG+jtBhM1wQqGhfMfDxDSzq+faJLzzcYOPa+a9H4xCHGAiBCLkco0H/ibMjXvjDi3U3ujHlmG66ZTJEnIWe4OTjefA=="}]}}, "2.5.2": {"name": "neo-async", "version": "2.5.2", "devDependencies": {"aigle": "^1.8.1", "async": "^2.6.0", "benchmark": "^2.1.1", "bluebird": "^3.5.1", "changelog-generator": "0.0.4", "codecov.io": "^0.1.6", "event-stream": "^3.3.3", "fs-extra": "^4.0.2", "func-comparator": "^0.7.2", "gulp": "^3.9.1", "gulp-bump": "^2.8.0", "gulp-exit": "0.0.2", "gulp-git": "^2.4.2", "gulp-jsbeautifier": "^2.1.1", "gulp-jscs": "^4.0.0", "gulp-mocha": "^4.2.0", "gulp-tag-version": "^1.3.0", "gulp-util": "^3.0.7", "istanbul": "^0.4.3", "jsdoc": "^3.5.5", "jshint": "^2.9.5", "lodash": "^4.16.6", "minimist": "^1.2.0", "mocha": "^3.5.3", "mocha-parallel-executor": "^0.3.0", "mocha.parallel": "^0.15.3", "require-dir": "^0.3.0", "run-sequence": "^1.2.2"}, "dist": {"integrity": "sha512-vdqTKI9GBIYcAEbFAcpKPErKINfPF5zIuz3/niBfq8WUZjpT2tytLlFVrBgWdOtqI4uaA/Rb6No0hux39XXDuw==", "shasum": "489105ce7bc54e709d736b195f82135048c50fcc", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-2.5.2.tgz", "fileCount": 119, "unpackedSize": 290906, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbccekCRA9TVsSAnZWagAAj88P/iWSuHzbEQP8TpZxjCth\nQKQTB01BkjRH7SyYpNt/nHELV8v0k2HNFNCI4255GPrBsRgAnwEiHTTkHhlW\nC4ql/lEVC64wo0cyABzTcwa+0QL1MljQ4tFBwmGLuB3w85qTnMIphF/xgT6r\npKcvLchYzaoXHdSTXD642cuiIwy4Z+g84rRDIKSjHJxE+PFwF8Uwa7YudgXI\nrR7uyTVpTn7tviTn2IPaVQF6Tfg/t3MabyRy+PrqfxGddz98DggHx6VhQUe1\neKULmXXjP0Egxi3/x1DyOGFqgQ4pVSRSjtaUv19asX7/Kwp2XPNqOX4nj37l\nuAUInAXGkNCgRRRMz7BmaWnaAEcOvcgTWL4fcPTaRi9Q3FpvbsoNmgcZq9AY\nYTRVOlqKhu4pGf4nVZhVhc6apjJAkjkl+RlCP77BLETW+oj5YvNk7Fzd/w4f\nzRQjCFj5tvTH7BidWq1KJgElPr3+tBPagBE4PGp5Yhd77wwu984BvqRMxVTb\nCYZStVesPVX2Hz5EdgJXMULDyNj/zNPXO9dAjAlJCi6EKPZMXykqgrqYTVwV\nOWP7XmF7CSmxB12jES62ergU00ftCU3FORCfVVBM1LpKg5x1/aOX8vGIJ6hR\neimaINhNZ9nOfxnD5Hmk58sd/HtTARDXNe36qD1DrgewVMAedh4tKmLx/tXA\nYFiu\r\n=3H63\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA55AguuO7+j+Z9+OlvcqDw7LbShtXPzpvjO9E9pS9JSAiBGEEs5QhbbEYOY5n4bPGqcbqpWZFGrPAGOp2iawUQo8w=="}]}}, "2.6.0": {"name": "neo-async", "version": "2.6.0", "devDependencies": {"aigle": "^1.8.1", "async": "^2.6.0", "benchmark": "^2.1.1", "bluebird": "^3.5.1", "changelog-generator": "0.0.4", "codecov.io": "^0.1.6", "event-stream": "^3.3.3", "fs-extra": "^4.0.2", "func-comparator": "^0.7.2", "gulp": "^3.9.1", "gulp-bump": "^2.8.0", "gulp-exit": "0.0.2", "gulp-git": "^2.4.2", "gulp-jsbeautifier": "^2.1.1", "gulp-jscs": "^4.0.0", "gulp-mocha": "^4.2.0", "gulp-tag-version": "^1.3.0", "gulp-util": "^3.0.7", "istanbul": "^0.4.3", "jsdoc": "^3.5.5", "jshint": "^2.9.5", "lodash": "^4.16.6", "minimist": "^1.2.0", "mocha": "^3.5.3", "mocha-parallel-executor": "^0.3.0", "mocha.parallel": "^0.15.3", "require-dir": "^0.3.0", "run-sequence": "^1.2.2"}, "dist": {"integrity": "sha512-MFh0d/Wa7vkKO3Y3LlacqAEeHK0mckVqzDieUKTT+KGxi+zIpeVsFxymkIiRpbpDziHc290Xr9A1O4Om7otoRA==", "shasum": "b9d15e4d71c6762908654b5183ed38b753340835", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-2.6.0.tgz", "fileCount": 119, "unpackedSize": 296684, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbxCZWCRA9TVsSAnZWagAA5dQP/34THcwvNR5olvrEl2Vc\nuzMxAQWDFL9btkqW79dv8sLNcT8gIsPC2KOMLNA1vlX2GLVzKUUcmqVhoG2r\nbxdFrkAF6zuKjP4fHTkN3/DuSlq7ST+QfSjajR2fb2mt47BGmHV0NGj5uOH4\nsCyvZ2iB12oC/vEZiiPpgt+v+Vk9CQuX5eN9JLF2CuMzn+/KU78mzBWN5uCw\nYak9DyRnk/XbnL73mbSXFDCYhcdNKrx+FLnWy3TihtqRmNX9ohxS5IiECJy6\n1Z84hdB20BMZesU0YNr8CZUMJFiS3+XSFIOBV+TI+4LJt7UQfp+E/IsKxjVZ\ng+mwo+5SOdqpU4VIEM8SRMD43Z+j55GHdxLhWh43BX0C4BrtZ2oJjQjMz3Cu\nDdWcKTYSlnSAsUatA9GlV3TJ6iotnRbjRcA4uMn688S/tcfJhEk0unFR3yo5\n7N32GnXkZVlqtV60Ln9rAB9kP9pABQb9KhMVfq4YQxMdlvU+5TWy0sEvampU\nzFjJ3V0/2AI+tY13RvvIOBXQjK1kBxflmIlq4Enn9AjhAxRBTMT6IQKgE3aa\nMvlIvXgUp0SSIzoEp6hy+Tf2GBijrW6FDBCGSx7ff6+icV1buaCV64yyhTuY\nfLFv0Hv8qCE9BDOFotAuHjvjsriXs9faItkNzztms3NQiWhqlBEHkR3x8gKq\nHqLZ\r\n=3ACv\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID+3p4PdQw/+CQ97nEtFcwnZZ5w6l1KnZKxbLRetgztkAiEAuyf+hrUeOvcugbmuyRkuWzlMkxDKwEl/EVDeheuTu/w="}]}}, "2.6.1": {"name": "neo-async", "version": "2.6.1", "devDependencies": {"aigle": "^1.8.1", "async": "^2.6.0", "benchmark": "^2.1.1", "bluebird": "^3.5.1", "codecov.io": "^0.1.6", "fs-extra": "^4.0.2", "func-comparator": "^0.7.2", "gulp": "^3.9.1", "gulp-bump": "^2.8.0", "gulp-exit": "0.0.2", "gulp-git": "^2.4.2", "gulp-jscs": "^4.0.0", "gulp-mocha": "^4.2.0", "gulp-tag-version": "^1.3.0", "gulp-util": "^3.0.7", "husky": "^1.2.0", "istanbul": "^0.4.3", "jsdoc": "^3.5.5", "jshint": "^2.9.5", "lint-staged": "^8.1.0", "lodash": "^4.16.6", "minimist": "^1.2.0", "mocha": "^3.5.3", "mocha-parallel-executor": "^0.3.0", "mocha.parallel": "^0.15.3", "prettier": "^1.15.2", "require-dir": "^0.3.0", "run-sequence": "^1.2.2"}, "dist": {"integrity": "sha512-iyam8fBuCUpWeKPGpaNMetEocMt364qkCsfL9JuhjXX6dRnguRVOfk2GZaDpPjcOKiiXCPINZC1GczQ7iTq3Zw==", "shasum": "ac27ada66167fa8849a6addd837f6b189ad2081c", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-2.6.1.tgz", "fileCount": 119, "unpackedSize": 297150, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc2M7UCRA9TVsSAnZWagAAvroP/RzpgUPXW8VCZf6DWuQD\nrlh9NQraH0qvZUX5SVIBAnVePFFiu+PyVKIGj0LaS7b89QsfsC2Q17hpbOa/\ngJJ/Rpq9Lnv4kzTxl58UKqtEatDoFVtdwx3ElpXOtavSM0wI2hchUsUrQdxc\nv7Ea6JQTwmrwieZH0HllNQ8tctJ9LFWPGm2rsJidbIyAIkJ0KW9Keno18DvB\nUd0E4l+9oefLMGPzec3czbg8NWYCBPW9hJGAgzCmpGMRK/kb081ku/kFmDD/\n+jRtmvpACr/NgCia9sgHN6V2wHn23JQeIuSSPqAWWJxHpNWOgNkb3lPze8/Y\nqFBUftjgI539RC7DH1mF8mEnUqWA2lwhMzPrFteSMjdV0gBWf2DMAhSoEgEp\nJnM7+kztfW0ZCdBy3n0nguqJe3MHJqMWb9pgf0C8gMv/iqS4Mc5bC9H8bOFv\nxx8LtY+x52lLfEpGIP4Ig6AsFYMXkmF4USndaf3XFbUlIk9Kr+X9LgiL7/f4\nnbIrsppFRk/rrV7JR4uyf9pbttJzazz2R7otu8Fm3OncNDpMQDrSV7L48vA0\n4budD1y6lVgH7M50wSI30V9HWUR8ImX0++NRWuE9LsmBfPzUZX9nDUpdzRvE\nquXZV6ROn0WtbR+LLzJaGXxVBcdyhXh3mFRXbeLZ+XGluZJjuc2BbffTmbz8\n77mp\r\n=B3D2\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFGuPH3grkPj1wFm8MXPLCSbONdcrggSg2rWlyrWEUd0AiAsPp3CAkOj74UffBw92DJEl2bnpKyZZPMom+whnrOU5A=="}]}}, "2.6.2": {"name": "neo-async", "version": "2.6.2", "devDependencies": {"aigle": "^1.14.0", "async": "^2.6.0", "benchmark": "^2.1.1", "bluebird": "^3.5.1", "codecov.io": "^0.1.6", "fs-extra": "^4.0.2", "func-comparator": "^0.7.2", "gulp": "^4.0.2", "gulp-bump": "^2.8.0", "gulp-exit": "0.0.2", "gulp-git": "^2.4.2", "gulp-jscs": "^4.0.0", "gulp-mocha": "^4.2.0", "gulp-tag-version": "^1.3.0", "gulp-util": "^3.0.7", "husky": "^1.2.0", "istanbul": "^0.4.3", "jsdoc": "^3.5.5", "jshint": "^2.9.5", "lint-staged": "^8.1.0", "lodash": "^4.16.6", "minimist": "^1.2.0", "mocha": "^3.5.3", "mocha-parallel-executor": "^0.3.0", "mocha.parallel": "^0.15.3", "prettier": "^1.15.2", "require-dir": "^0.3.0"}, "dist": {"integrity": "sha512-Yd3UES5mWCSqR+qNT93S3UoYUkqAZ9lLg8a7g9rimsWmYGK8cVToA4/sF3RrshdyV3sAGMXVUmpMYOw+dLpOuw==", "shasum": "b4aafb93e3aeb2d8174ca53cf163ab7d7308305f", "tarball": "https://registry.npmjs.org/neo-async/-/neo-async-2.6.2.tgz", "fileCount": 119, "unpackedSize": 297636, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfB2C5CRA9TVsSAnZWagAAH4QP/0s8+ZnoI7kApwEBXN54\nQWpTQH8mH0AkEdBVoMer+utpIBomIMEzKTvUfkLzGqAbCgvUdVwIlA1OsWy4\nOX3h9gygkYCZuqhIPV4ne5eWcOYFz0xuiknoWlk7e+ex9FR4eEA5zhYUvg9u\n6KYuj5W0I4n40GJncOA5MEU7gTiyYYBEd7ocHXoEyUj2Meyi4yCBC6650f22\na8fngohJ4SyUC2RLfHCnPxLQ8GDqs6VfzEBUon6CqePSN7f4Ee8Y4fLoRJ7Q\nEZwk1w19kk9rfpINuJlb2a/cADuNokrnVvO3LnH77DLK/MyTDz62k/ok7nJl\n13JeL341jXXR7PQWPt3EuRQBHqLs8rx3pGVZqgVki4ldbMzNt6t39mmCmzDk\n1c02M0QFEtJDHJfw2PnaKQ89NZFMor5omz4oWhmeuGhdYf3iohoAinOcIa+w\nG94iwj1q9YawDoio84JtluG6OTibI5ABUc7VRjGpyt6b1Ou9T7Roj1SXwwJz\nDUsKfFtF7v5X1CKNrBOC8TppW5v5Amktaw6qr4VB9l4YIQTb8tvnJZ2v3I4q\n1RYtBNAmaoKB4K4/9T3rv4QrzSrSqfrylmwzwGSUrYKWwkiRyVjSw72iL9m4\n+079fkgB6FvIUHa2XLFzbdkYMZaK7s7UV7aeKPWNJE4eAln+XiN4svFEzC3/\nnNXI\r\n=0Oj7\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFvhSglvk5wdeHCDbyrHk3vaHp5xxeH+/BLudpp/xTYgAiBG9znMhm/i4TKl6hOHBdEQ8t5mUWCZEIA7tcOkAaaSCw=="}]}}}, "modified": "2022-06-21T00:04:10.222Z"}