{"name": "sshpk", "dist-tags": {"latest": "1.18.0"}, "versions": {"1.0.0": {"name": "sshpk", "version": "1.0.0", "dependencies": {"asn1": "^0.2.2", "assert-plus": "^0.1.5"}, "devDependencies": {"tap": "0.4.2", "benchmark": "^1.0.0"}, "dist": {"shasum": "a8328ea1f1ab7f24925d05d08b41d307d6e1cc82", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.0.0.tgz", "integrity": "sha512-+a9TI8MCVSOVVvYGsJCe1zM77dR7wKAQvbOkVSw5R4kNX4XDgBdYVA0trI7NKPWVirZTwWA82XFZgimgjxfXkQ==", "signatures": [{"sig": "MEQCIHo3hC6i4r1Si3mbu/pcgo/DxfNU2wEwehfM009RnW2tAiAVom+oX9rVjwnOWjNrBweDCSU99G1ShC6RvN41DSl0Zg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.1": {"name": "sshpk", "version": "1.0.1", "dependencies": {"asn1": "^0.2.2", "assert-plus": "^0.1.5"}, "devDependencies": {"tap": "0.4.2", "benchmark": "^1.0.0"}, "dist": {"shasum": "f082eaf45cf591f28025899076b711ae15492efe", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.0.1.tgz", "integrity": "sha512-VME/PREvDHZJubjNlQEJpINaX0D4M3LmgXhVu3RbHK+XljcLYpjmmceommhMIgfeh5QsOkUJzLn0UA82FvfD/w==", "signatures": [{"sig": "MEQCIDYgbXAdkmtADvGCcsVeSWpfyX/xVmwKCOSGzzWbubckAiAyGudJBakojTjqIpc9mxXSNWtPrZKtC8zGo37WS5a46A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.2": {"name": "sshpk", "version": "1.0.2", "dependencies": {"asn1": "^0.2.2", "assert-plus": "^0.1.5"}, "devDependencies": {"tape": "^3.5.0", "benchmark": "^1.0.0"}, "dist": {"shasum": "6c4ede38cbc33de7b97701b4439a1f92ff8dca9d", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.0.2.tgz", "integrity": "sha512-FdtqHysFvZOqJtb5Skkfd9aHvNFTABefGaTaLmcryGE0Lb/vqaqLq8JIS2ucNKYBbdtcAcWkG50VNaCPoX/YcA==", "signatures": [{"sig": "MEYCIQCuAp3mUvurqFRvS/1nE3nXwECSLmwCjqvcfWEJRjv/XgIhAI1oVqHcNxeU/oQXDgjvD5QMGX/g/+r0JOB65eNH/+Nu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}}, "1.0.3": {"name": "sshpk", "version": "1.0.3", "dependencies": {"asn1": "0.2.2", "assert-plus": "0.1.5"}, "devDependencies": {"tape": ">=3.5.0 <4.0.0", "benchmark": ">=1.0.0 <2.0.0"}, "dist": {"shasum": "6046f24117ed21399ba84aab69d4e2e308fc899f", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.0.3.tgz", "integrity": "sha512-eCgZLbJdY3f5BAdzAlFGLeCJBb4tw8YYt9wQcODSy88DhFyinG7uqpNO1+8gVYBvZvbp+4wv8QwvznnMtM5LHg==", "signatures": [{"sig": "MEUCIAKSuvaARdBpQMhOxWpcPvvKcuO3nyEwYXh+XGKVTyvBAiEAjWPxNjtw5SwyHxfOc7rWUz100C1IHXCdhINSEie95L4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}}, "1.0.4": {"name": "sshpk", "version": "1.0.4", "dependencies": {"asn1": "0.2.2", "assert-plus": "0.1.5"}, "devDependencies": {"tape": ">=3.5.0 <4.0.0", "benchmark": ">=1.0.0 <2.0.0"}, "dist": {"shasum": "584754ee1b57b9026caf2df0831b009011858f6b", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.0.4.tgz", "integrity": "sha512-bgdAfEw88FcX3cpCE8VaJoAtGBKxd135MYNjx94lckYuhVNuj/LOobix/Eh7XY85F/TCECnoae8t5GNs4d2glg==", "signatures": [{"sig": "MEYCIQDMDeLirkT0EiwfxeQ7fDvTbbxBbVE3SZugsGcSbo0BCAIhAO/gWRXxSZvqOf7x44RcqNVi7ZhP7yzNqo0umvVnrZYa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}}, "1.1.0": {"name": "sshpk", "version": "1.1.0", "dependencies": {"asn1": "0.2.2", "assert-plus": "0.1.5"}, "devDependencies": {"tape": ">=3.5.0 <4.0.0", "benchmark": ">=1.0.0 <2.0.0"}, "dist": {"shasum": "f1046ee820294ca95d737932e0e2369663a59513", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.1.0.tgz", "integrity": "sha512-s0hab9Q9rIZz/UOAABr+FH1w2OW/FaAHMxXO1n2TRWbQmwecqeDlPqZy6Qm2CZNi0ld+nay0MZPzRPq9plgYNg==", "signatures": [{"sig": "MEYCIQC0ydf+xMO2m8R85BxEbIedREX5u06aBZ4EWkRZWj67BwIhAJE+0HL0YB7NjCXSUq8572iLejnBO84LMp+Vi9+B23vx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}}, "1.2.0": {"name": "sshpk", "version": "1.2.0", "dependencies": {"asn1": "0.2.2", "assert-plus": "0.1.5"}, "devDependencies": {"tape": ">=3.5.0 <4.0.0", "benchmark": ">=1.0.0 <2.0.0"}, "dist": {"shasum": "e400ece7f6cbfe2da4db7335684198b1ced4057d", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.2.0.tgz", "integrity": "sha512-1wHmHoPyKyuLZ2S1e+y/GRPal/EFHhgl08JgA/9Jn8310woAxY6sGKXvbCG+yiSSGEm00m6dLe6WxKS3H1QOZg==", "signatures": [{"sig": "MEYCIQDdIm2k04cJcUiuD0KTs9oL8EUAF+X6UdjnnBUCDen23wIhAMhf+bbW/nhp/V/4+9pBuxTftizxhc81iCHj9NCS1OTt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}}, "1.2.1": {"name": "sshpk", "version": "1.2.1", "dependencies": {"asn1": "0.2.2", "assert-plus": "0.1.5"}, "devDependencies": {"tape": ">=3.5.0 <4.0.0", "benchmark": ">=1.0.0 <2.0.0"}, "dist": {"shasum": "d7f8272dc165afbfa3ba682086bd3daf26be3e83", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.2.1.tgz", "integrity": "sha512-7GK+Kz/4sEKokNtyPqqHVkupLNghCION+O+9I452lp/+VZ9/Ccc4Glg6AbACynmaTyuMWF0cYJfXbfWm1v2mSg==", "signatures": [{"sig": "MEUCIHea3NWm2JHFEVS4UH8ey4yvheJtuRxiArHUmBV1w6kMAiEAwX1qn96ZlZK1PlLzkTllTq9e8T1CWCWRYqrN2HCUPKk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}}, "1.3.0": {"name": "sshpk", "version": "1.3.0", "dependencies": {"asn1": ">=0.2.3 <0.3.0", "assert-plus": ">=0.1.5 <0.2.0", "big-integer": ">=1.6.0 <2.0.0"}, "optionalDependencies": {"big-integer": ">=1.6.0 <2.0.0"}, "devDependencies": {"tape": ">=3.5.0 <4.0.0", "benchmark": ">=1.0.0 <2.0.0"}, "dist": {"shasum": "fd3229a6e0971b2003e61927fa74b2814c696cfb", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.3.0.tgz", "integrity": "sha512-APpqRP7VrlRT3ptS+9bAY/l4KniVUyNHBUkGeGt0VsUNlqT0nszhQkqST0yoNgzt6kiYVzztoOrnRzWQPiH44Q==", "signatures": [{"sig": "MEYCIQDxIr9MA4/q2wHnlYQdOurAW4M/jwQZbxzc3JmSm1X2xQIhAP1JVrkrnEmFlaVf7QL0EpmKaFMjXiLVi6irGlko1p86", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}}, "1.4.0": {"name": "sshpk", "version": "1.4.0", "dependencies": {"asn1": ">=0.2.3 <0.3.0", "dashdash": ">=1.10.1 <2.0.0", "assert-plus": ">=0.1.5 <0.2.0", "big-integer": ">=1.6.0 <2.0.0"}, "optionalDependencies": {"big-integer": ">=1.6.0 <2.0.0"}, "devDependencies": {"tape": ">=3.5.0 <4.0.0", "benchmark": ">=1.0.0 <2.0.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv"}, "directories": {"bin": "./bin", "lib": "./lib"}, "dist": {"shasum": "4358666a18093d91cdbddb986e00545e119ee79b", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.4.0.tgz", "integrity": "sha512-K1+NhKQ0hpUFf4XnJzNtWBN+gX8fmZz9aWc4SbWU0IxF4FtvTJ5QcOm6iRSt98d/U0+TgEKVTzkD/fgiJL7uQw==", "signatures": [{"sig": "MEUCIQDb4pVZ3tR4wg6/K1+5W/eLKV1bDjshjCBsbNUdXHbcyQIgKgmqP7JSUeAuSMYLdxaX9Hmd/O6CNTAmfYNGUIQRQuA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}}, "1.4.1": {"name": "sshpk", "version": "1.4.1", "dependencies": {"asn1": ">=0.2.3 <0.3.0", "dashdash": ">=1.10.1 <2.0.0", "assert-plus": ">=0.1.5 <0.2.0", "big-integer": ">=1.6.0 <2.0.0"}, "optionalDependencies": {"big-integer": ">=1.6.0 <2.0.0"}, "devDependencies": {"tape": ">=3.5.0 <4.0.0", "benchmark": ">=1.0.0 <2.0.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv"}, "directories": {"bin": "./bin", "lib": "./lib"}, "dist": {"shasum": "f271f1533deba161bae23d46d40ddb4c7f28b395", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.4.1.tgz", "integrity": "sha512-etyGas8kDAKYDCrmAOYEqs+6Hlsp9EzcpUOCi/jDgW53c+DA6TSqv1ZzeuQDptCL1FBbsf8vNWAmCACl0yMKvA==", "signatures": [{"sig": "MEUCIDzLpOC4y74VOYU7CbrALmGDuFSxXsNMuKabWn+nQWU4AiEAttipw7NQqi5XpKdmD5L59Fs3XVd7Cde2N5KKygTKwPA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}}, "1.4.2": {"name": "sshpk", "version": "1.4.2", "dependencies": {"asn1": ">=0.2.3 <0.3.0", "dashdash": ">=1.10.1 <2.0.0", "assert-plus": ">=0.1.5 <0.2.0", "big-integer": ">=1.6.0 <2.0.0"}, "optionalDependencies": {"big-integer": ">=1.6.0 <2.0.0"}, "devDependencies": {"tape": ">=3.5.0 <4.0.0", "benchmark": ">=1.0.0 <2.0.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv"}, "directories": {"bin": "./bin", "lib": "./lib"}, "dist": {"shasum": "d06be5aa796f8a3bccdf7dcd6b9445177487012c", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.4.2.tgz", "integrity": "sha512-d19FZ7gfirLfoDn3aOMPusvxqRX/H09pwG6e9PvU7RRT4qxrEkhzEYy+2vy332n1oP8RRr025tSTY4z2EU0dfA==", "signatures": [{"sig": "MEUCIDA2q1kBmKNG/5vdmqc9uShvXAtg1ATG5wZl2Pz/TKKwAiEA7QOojarlwaUX4kcJwX2AGYQdVFpegj67f0VOd76FVk8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}}, "1.4.3": {"name": "sshpk", "version": "1.4.3", "dependencies": {"asn1": ">=0.2.3 <0.3.0", "dashdash": ">=1.10.1 <2.0.0", "assert-plus": ">=0.1.5 <0.2.0", "big-integer": ">=1.6.0 <2.0.0"}, "optionalDependencies": {"big-integer": ">=1.6.0 <2.0.0"}, "devDependencies": {"tape": ">=3.5.0 <4.0.0", "benchmark": ">=1.0.0 <2.0.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv"}, "directories": {"bin": "./bin", "lib": "./lib"}, "dist": {"shasum": "580177d5989653ad61b890af4ffb0e63dca6c5a1", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.4.3.tgz", "integrity": "sha512-enDhud100EEesQXT7BrJJKzlalpW4vcyEPGsGbPXqpleMfINCYY+SexIGKN/4YVSyyzBKW/VfMORP56OWmUYmw==", "signatures": [{"sig": "MEQCIHB27IQy7Yl/I/lgJfXtVX7DEhWbjoQH3Up4IgHhATCQAiBbMgwWf6qelKoNpsAauIztPCXaxvjt3uyyrREq4JH1JA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}}, "1.4.4": {"name": "sshpk", "version": "1.4.4", "dependencies": {"asn1": ">=0.2.3 <0.3.0", "dashdash": ">=1.10.1 <2.0.0", "assert-plus": ">=0.1.5 <0.2.0", "big-integer": ">=1.6.0 <2.0.0"}, "optionalDependencies": {"big-integer": ">=1.6.0 <2.0.0"}, "devDependencies": {"tape": ">=3.5.0 <4.0.0", "benchmark": ">=1.0.0 <2.0.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv"}, "directories": {"bin": "./bin", "lib": "./lib"}, "dist": {"shasum": "8da4da1bd64c48ce13fd9947144c917582462024", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.4.4.tgz", "integrity": "sha512-zNDiA87g7FZXS2e19l4bEiO2wbpJ+GiCEGjFnEvPjoJW2dy9uZ+fw2SI2zaJMFw98Zv9vbanp0igDL3c+vqo4w==", "signatures": [{"sig": "MEUCIQDiwj7U7sEDLkkktZl3IGNgY5zOoRtZqV36mfCuwEuA4QIgbnqSWAJLJKJOXHfld7eTTmftSc56KSiMM2qaaLAy8hw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}}, "1.4.5": {"name": "sshpk", "version": "1.4.5", "dependencies": {"asn1": ">=0.2.3 <0.3.0", "dashdash": ">=1.10.1 <2.0.0", "assert-plus": ">=0.1.5 <0.2.0", "big-integer": ">=1.6.0 <2.0.0"}, "optionalDependencies": {"big-integer": ">=1.6.0 <2.0.0"}, "devDependencies": {"tape": ">=3.5.0 <4.0.0", "benchmark": ">=1.0.0 <2.0.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv"}, "directories": {"bin": "./bin", "lib": "./lib"}, "dist": {"shasum": "b08d682ea1819fd50bad39c0bc1f08cd4d787883", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.4.5.tgz", "integrity": "sha512-1UlQxgIWGPhAdhc80Ol3X2C/NXrJHzJmo76kKrd3kwBnfMk5NZFoIvC6a2Xwpj92Bmf4ed7zC1jaPBiu2q2ZAw==", "signatures": [{"sig": "MEQCIA0yEtOljpJOpatMoJtMc6Q2psXJhOC1mriTixhXNt99AiAlw9aCNcI56x2oL3KreiW0sLUEz7u/KBL/3ZMqvi5FAg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}}, "1.4.6": {"name": "sshpk", "version": "1.4.6", "dependencies": {"asn1": ">=0.2.3 <0.3.0", "dashdash": ">=1.10.1 <2.0.0", "assert-plus": ">=0.1.5 <0.2.0", "big-integer": ">=1.6.0 <2.0.0"}, "optionalDependencies": {"big-integer": ">=1.6.0 <2.0.0"}, "devDependencies": {"tape": ">=3.5.0 <4.0.0", "benchmark": ">=1.0.0 <2.0.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv"}, "directories": {"bin": "./bin", "lib": "./lib"}, "dist": {"shasum": "aff7273e4797e60a83b031c94388bd00cf500c59", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.4.6.tgz", "integrity": "sha512-Jyrzoi1eNbkLJuwF7GNfLrIUONccL1Xhh6Mt2zw9nQpOUacKjR5zAXFcY+Wg6+M3Rx7+zwWmHpqLcwLNLQv4yQ==", "signatures": [{"sig": "MEUCIF5QCMytoA+fF8zFsUXn61AGaVVsljTH6lpIAm+HL8s2AiEApa642e5BLmgZvSUi1+Xvy1u1EdbzygEizhubM2N1gl4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}}, "1.4.7": {"name": "sshpk", "version": "1.4.7", "dependencies": {"asn1": ">=0.2.3 <0.3.0", "dashdash": ">=1.10.1 <2.0.0", "assert-plus": ">=0.1.5 <0.2.0", "big-integer": ">=1.6.0 <2.0.0"}, "optionalDependencies": {"big-integer": ">=1.6.0 <2.0.0"}, "devDependencies": {"tape": ">=3.5.0 <4.0.0", "benchmark": ">=1.0.0 <2.0.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv"}, "directories": {"bin": "./bin", "lib": "./lib"}, "dist": {"shasum": "62e3dbf98b065ca7950acd21cdb780b9d2080066", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.4.7.tgz", "integrity": "sha512-yFzBhX48lbX4WQMsajkHWTW3bIFbvVmatwHjVMg+0L3t4La/4VwhbqFk7JXcLIsIGV54xgR3GrQPji+jY7g0Aw==", "signatures": [{"sig": "MEUCIFvJ9muvCs5KjwqaSuwZZ9Oz6nO9FrDcUN4wDwgqjI7aAiEA90we3AcX8Wp1J7FbmBdo9k0ugS3NmEfVxBh9vIf15Yk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}}, "1.5.0": {"name": "sshpk", "version": "1.5.0", "dependencies": {"asn1": ">=0.2.3 <0.3.0", "jsbn": ">=0.0.0 <1.0.0", "dashdash": ">=1.10.1 <2.0.0", "jodid25519": ">=1.0.0 <2.0.0", "assert-plus": ">=0.1.5 <0.2.0"}, "optionalDependencies": {"jsbn": ">=0.0.0 <1.0.0", "jodid25519": ">=1.0.0 <2.0.0"}, "devDependencies": {"tape": ">=3.5.0 <4.0.0", "benchmark": ">=1.0.0 <2.0.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv"}, "directories": {"bin": "./bin", "lib": "./lib"}, "dist": {"shasum": "00c40d3b80d4fe7e9ac562a343ce111fafca4fe2", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.5.0.tgz", "integrity": "sha512-sXBs0Zv/CVc0yvolRfeh9M30qsbqDr5Vo5RmpkaVABW1R3w4kdosQCRiCU4LQqaRZFLH82GJajBANfdOOuyQ/A==", "signatures": [{"sig": "MEUCIQC5hNxrSs+kXIwxRFU3svZ1p/RJXS1/yADRP3UYWz8c8AIgHAnnFd/7Spf6uNjLx31tsfsL8/LAL5ZsEHQAdyi9Lz0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.5.1": {"name": "sshpk", "version": "1.5.1", "dependencies": {"asn1": ">=0.2.3 <0.3.0", "jsbn": ">=0.0.0 <1.0.0", "dashdash": ">=1.10.1 <2.0.0", "jodid25519": ">=1.0.0 <2.0.0", "assert-plus": ">=0.1.5 <0.2.0"}, "optionalDependencies": {"jsbn": ">=0.0.0 <1.0.0", "jodid25519": ">=1.0.0 <2.0.0"}, "devDependencies": {"tape": ">=3.5.0 <4.0.0", "benchmark": ">=1.0.0 <2.0.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv"}, "directories": {"bin": "./bin", "lib": "./lib"}, "dist": {"shasum": "85fe5c5e69b7318c3fa329d82abef0ba4228c4f9", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.5.1.tgz", "integrity": "sha512-CLZege1Vdn0zpTDU+IIAkg0gxeEBiW2oW6YE1h8Kl+dZOf3jhlU0iz7scpTMTsDyyj3pvFqWldLEjEoUlEsnYw==", "signatures": [{"sig": "MEUCIQCwZJWj1OFDmM5+YhYJby5Y0Pya4/E+FHctdjhmHROO+QIgFoJnAxBucUW5DzeeHQBbwpAcEeG6bCrwIZK51jb2Z24=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.6.0": {"name": "sshpk", "version": "1.6.0", "dependencies": {"asn1": ">=0.2.3 <0.3.0", "jsbn": ">=0.1.0 <0.2.0", "dashdash": ">=1.10.1 <2.0.0", "ecc-jsbn": ">=0.0.1 <1.0.0", "tweetnacl": ">=0.13.0 <1.0.0", "jodid25519": ">=1.0.0 <2.0.0", "assert-plus": ">=0.1.5 <0.2.0"}, "optionalDependencies": {"jsbn": ">=0.1.0 <0.2.0", "ecc-jsbn": ">=0.0.1 <1.0.0", "tweetnacl": ">=0.13.0 <1.0.0", "jodid25519": ">=1.0.0 <2.0.0"}, "devDependencies": {"tape": ">=3.5.0 <4.0.0", "sinon": ">=1.17.2 <2.0.0", "benchmark": ">=1.0.0 <2.0.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv"}, "directories": {"bin": "./bin", "lib": "./lib"}, "dist": {"shasum": "f9f9c76fd8e622d583b1ae83fe49aa7bec6e1d4a", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.6.0.tgz", "integrity": "sha512-bc6WjJfjvGWaAOsw8/VGr4K5RqtpsI0OnJ1XXUbRIQo8JapKuc+Wmcz5AaHiWG9A6ekk1fy7rFCzTHRxAUzNxw==", "signatures": [{"sig": "MEUCIBYDyrBIwHUVk1TumRjKSBQn9+fu8p8RxhgGyztK6PrUAiEArvxU5kxANN83CItI+Y9564Ffwe6s08Mz+gqD+pJHeQ4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.6.1": {"name": "sshpk", "version": "1.6.1", "dependencies": {"asn1": ">=0.2.3 <0.3.0", "jsbn": ">=0.1.0 <0.2.0", "dashdash": ">=1.10.1 <2.0.0", "ecc-jsbn": ">=0.0.1 <1.0.0", "tweetnacl": ">=0.13.0 <1.0.0", "jodid25519": ">=1.0.0 <2.0.0", "assert-plus": ">=0.1.5 <0.2.0"}, "optionalDependencies": {"jsbn": ">=0.1.0 <0.2.0", "ecc-jsbn": ">=0.0.1 <1.0.0", "tweetnacl": ">=0.13.0 <1.0.0", "jodid25519": ">=1.0.0 <2.0.0"}, "devDependencies": {"tape": ">=3.5.0 <4.0.0", "sinon": ">=1.17.2 <2.0.0", "benchmark": ">=1.0.0 <2.0.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv"}, "directories": {"bin": "./bin", "lib": "./lib"}, "dist": {"shasum": "3f1e6825feb00dd178f897409d6661756d3fefac", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.6.1.tgz", "integrity": "sha512-0iZWUpmXwwi5GjUQKjE4QKoa/SiuF5jnNw+UIRngZnXuQwJhdFAwOmIn3t7v756iutdx+vgLvml+JO31YZ7iHQ==", "signatures": [{"sig": "MEYCIQCqsTYx7I414qyngEzpJnKuGnP0YaVpqF1OEGOnT7VJSgIhAIiqWu0w4edBSJw+aRFO+nQrX5Sdvo1aYLO5myCgjs9g", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.6.2": {"name": "sshpk", "version": "1.6.2", "dependencies": {"asn1": ">=0.2.3 <0.3.0", "jsbn": ">=0.1.0 <0.2.0", "dashdash": ">=1.10.1 <2.0.0", "ecc-jsbn": ">=0.0.1 <1.0.0", "tweetnacl": ">=0.13.0 <1.0.0", "jodid25519": ">=1.0.0 <2.0.0", "assert-plus": ">=0.1.5 <0.2.0"}, "optionalDependencies": {"jsbn": ">=0.1.0 <0.2.0", "ecc-jsbn": ">=0.0.1 <1.0.0", "tweetnacl": ">=0.13.0 <1.0.0", "jodid25519": ">=1.0.0 <2.0.0"}, "devDependencies": {"tape": ">=3.5.0 <4.0.0", "sinon": ">=1.17.2 <2.0.0", "benchmark": ">=1.0.0 <2.0.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv"}, "directories": {"bin": "./bin", "lib": "./lib"}, "dist": {"shasum": "a8bbe7f5087739b71e1cecb43669e1ade663214c", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.6.2.tgz", "integrity": "sha512-yrtjJgkM0CTjlI0Z9ermaCLRJZUnlSu3HaBwe/IRGvzBBtq4X0/vPFI7xKmlZqso0OM5vk2ptcKEYhEzrwef0Q==", "signatures": [{"sig": "MEQCIHBzmkZC9b7U7Xds4IicX7WKK0EKA6/gALK15vKhf1ojAiAYJWKgaq8vNXK1wavlN6f+PgFTjvpgwsGrp+EPLchQdg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}}, "1.7.0": {"name": "sshpk", "version": "1.7.0", "dependencies": {"asn1": ">=0.2.3 <0.3.0", "jsbn": ">=0.1.0 <0.2.0", "dashdash": ">=1.10.1 <2.0.0", "ecc-jsbn": ">=0.0.1 <1.0.0", "tweetnacl": ">=0.13.0 <1.0.0", "jodid25519": ">=1.0.0 <2.0.0", "assert-plus": ">=0.2.0 <0.3.0"}, "optionalDependencies": {"jsbn": ">=0.1.0 <0.2.0", "ecc-jsbn": ">=0.0.1 <1.0.0", "tweetnacl": ">=0.13.0 <1.0.0", "jodid25519": ">=1.0.0 <2.0.0"}, "devDependencies": {"tape": ">=3.5.0 <4.0.0", "sinon": ">=1.17.2 <2.0.0", "benchmark": ">=1.0.0 <2.0.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv"}, "directories": {"bin": "./bin", "lib": "./lib"}, "dist": {"shasum": "c64ac26de1bdd19cc9ccbe78ba747292c6c70a7c", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.7.0.tgz", "integrity": "sha512-60oGUGLvQ4Q46D82flsCNA/daEkvTnVr3HlyxP0/1nkwCRZcUFwHWNyb0uBEIZMg1CWA1J4BlyjafgARImVN6g==", "signatures": [{"sig": "MEUCIHE9Hh+mE6m7cTXdmv4Do6aDMobaT1NR1G6V/Gn722lrAiEA4Ry5locN6WlOHClSqCz4NpNx59ZYECku5x6e/lgjloo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}}, "1.7.1": {"name": "sshpk", "version": "1.7.1", "dependencies": {"asn1": ">=0.2.3 <0.3.0", "jsbn": ">=0.1.0 <0.2.0", "dashdash": ">=1.10.1 <2.0.0", "ecc-jsbn": ">=0.0.1 <1.0.0", "tweetnacl": ">=0.13.0 <1.0.0", "jodid25519": ">=1.0.0 <2.0.0", "assert-plus": ">=0.2.0 <0.3.0"}, "optionalDependencies": {"jsbn": ">=0.1.0 <0.2.0", "ecc-jsbn": ">=0.0.1 <1.0.0", "tweetnacl": ">=0.13.0 <1.0.0", "jodid25519": ">=1.0.0 <2.0.0"}, "devDependencies": {"tape": ">=3.5.0 <4.0.0", "sinon": ">=1.17.2 <2.0.0", "benchmark": ">=1.0.0 <2.0.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv"}, "directories": {"bin": "./bin", "lib": "./lib"}, "dist": {"shasum": "565e386c42a77e6062fbd14c0472ff21cd53398c", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.7.1.tgz", "integrity": "sha512-5tF1bCTJM3L2EPeg25Njg91c231xKXrTthIOwFzRJo5DSdnaoC8SVTZvX4NcXsbu16LoszUuOyj+RzBa9E4law==", "signatures": [{"sig": "MEYCIQDY2UcmnjNTFyTzSYeV1TbKtx2RNjTpQieiJWnZgNOQmgIhAO/iPO0/ow0aeYLT8lL7YsjprQcaEPY+ysO+s2gjzQBw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}}, "1.7.2": {"name": "sshpk", "version": "1.7.2", "dependencies": {"asn1": ">=0.2.3 <0.3.0", "jsbn": ">=0.1.0 <0.2.0", "dashdash": ">=1.10.1 <2.0.0", "ecc-jsbn": ">=0.0.1 <1.0.0", "tweetnacl": ">=0.13.0 <1.0.0", "jodid25519": ">=1.0.0 <2.0.0", "assert-plus": ">=0.2.0 <0.3.0"}, "optionalDependencies": {"jsbn": ">=0.1.0 <0.2.0", "ecc-jsbn": ">=0.0.1 <1.0.0", "tweetnacl": ">=0.13.0 <1.0.0", "jodid25519": ">=1.0.0 <2.0.0"}, "devDependencies": {"tape": ">=3.5.0 <4.0.0", "temp": "0.8.2", "sinon": ">=1.17.2 <2.0.0", "benchmark": ">=1.0.0 <2.0.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv"}, "directories": {"bin": "./bin", "lib": "./lib"}, "dist": {"shasum": "e5eb43d0662bd201037327edb8b8f64656aca842", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.7.2.tgz", "integrity": "sha512-T2CUOimLP1rTzO588HpHZrLPpNHi2p7Dq2OC3QHBVIKLbjD6c+T+9E2VSbDAzxKnGnt9bx+Sllob78ch1FVNPA==", "signatures": [{"sig": "MEQCIE1Qcf4KEjGkpl4vT2JDx4xqapvTAb/eBq0bbeYonLNDAiB23UJsKGzcxabZoZ6YOpYHs3upO3PGlebL5kcn4J5o6w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}}, "1.7.3": {"name": "sshpk", "version": "1.7.3", "dependencies": {"asn1": ">=0.2.3 <0.3.0", "jsbn": ">=0.1.0 <0.2.0", "dashdash": ">=1.10.1 <2.0.0", "ecc-jsbn": ">=0.0.1 <1.0.0", "tweetnacl": ">=0.13.0 <1.0.0", "jodid25519": ">=1.0.0 <2.0.0", "assert-plus": ">=0.2.0 <0.3.0"}, "optionalDependencies": {"jsbn": ">=0.1.0 <0.2.0", "ecc-jsbn": ">=0.0.1 <1.0.0", "tweetnacl": ">=0.13.0 <1.0.0", "jodid25519": ">=1.0.0 <2.0.0"}, "devDependencies": {"tape": ">=3.5.0 <4.0.0", "temp": "0.8.2", "sinon": ">=1.17.2 <2.0.0", "benchmark": ">=1.0.0 <2.0.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "directories": {"bin": "./bin", "lib": "./lib", "man": "./man/man1"}, "dist": {"shasum": "caa8ef95e30765d856698b7025f9f211ab65962f", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.7.3.tgz", "integrity": "sha512-vFFte2Qp/1AG2zIvpn3sc2aJnewD6dnzj5jAsmC6XT0Wj6YPBLaCVVZ8kPBM+RnAIkUixY2LL/hp/BvEJdp0tQ==", "signatures": [{"sig": "MEYCIQDKBmXGIquvEvdcwRhAcZRiMVH4+EnUrnNcoVzDZ7hDZQIhAMbmGqKrq+M6LmgnWDED67frlbhGkRl5vlmUC8eClORl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}}, "1.7.4": {"name": "sshpk", "version": "1.7.4", "dependencies": {"asn1": ">=0.2.3 <0.3.0", "jsbn": ">=0.1.0 <0.2.0", "dashdash": ">=1.10.1 <2.0.0", "ecc-jsbn": ">=0.0.1 <1.0.0", "tweetnacl": ">=0.13.0 <1.0.0", "jodid25519": ">=1.0.0 <2.0.0", "assert-plus": ">=0.2.0 <0.3.0"}, "optionalDependencies": {"jsbn": ">=0.1.0 <0.2.0", "ecc-jsbn": ">=0.0.1 <1.0.0", "tweetnacl": ">=0.13.0 <1.0.0", "jodid25519": ">=1.0.0 <2.0.0"}, "devDependencies": {"tape": ">=3.5.0 <4.0.0", "temp": "0.8.2", "sinon": ">=1.17.2 <2.0.0", "benchmark": ">=1.0.0 <2.0.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "directories": {"bin": "./bin", "lib": "./lib", "man": "./man/man1"}, "dist": {"shasum": "ad7b47defca61c8415d964243b62b0ce60fbca38", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.7.4.tgz", "integrity": "sha512-p7+I1qlhKmgTIGmXu9DkQzjVig5uURH0xNWg/YU6O4YWsfCYoctnwbyrdgMcvzRmTBERMzOxVJifeaHlJLeLig==", "signatures": [{"sig": "MEQCICCMLv3576NQipDGpE+dy6WwiJVmpuJ+n1BWVvYEf9GaAiAeHQwr3UHpG4AAND8CbHayG2oYb06/EZl83SwbMpYTgQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}}, "1.8.0": {"name": "sshpk", "version": "1.8.0", "dependencies": {"asn1": "~0.2.3", "jsbn": "~0.1.0", "getpass": "^0.1.1", "dashdash": "^1.12.0", "ecc-jsbn": "~0.0.1", "tweetnacl": "~0.13.0", "jodid25519": "^1.0.0", "assert-plus": "^1.0.0"}, "optionalDependencies": {"jsbn": "~0.1.0", "ecc-jsbn": "~0.0.1", "tweetnacl": "~0.13.0", "jodid25519": "^1.0.0"}, "devDependencies": {"tape": "^3.5.0", "temp": "^0.8.2", "sinon": "^1.17.2", "benchmark": "^1.0.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "directories": {"bin": "./bin", "lib": "./lib", "man": "./man/man1"}, "dist": {"shasum": "b8f8820f8a21b0ded4e6459da42313317ff15bf6", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.8.0.tgz", "integrity": "sha512-0U/udGtPTPIioWrFpf9EASjltk3zAZVGbNpH/oVEgBYROPEKvodfZB24mZxdhRCudarNSLJq7FryyrEiED3Xkg==", "signatures": [{"sig": "MEUCIQDVffb1cf1liKbwqR7G2QjYeDsjPnrdLODRLCQAp9NHFAIgHnIUTWNHC/c3vD8XwSM8p3GI3En0BuZnjFtcTvydJYU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.8.1": {"name": "sshpk", "version": "1.8.1", "dependencies": {"asn1": "~0.2.3", "jsbn": "~0.1.0", "getpass": "^0.1.1", "dashdash": "^1.12.0", "ecc-jsbn": "~0.0.1", "tweetnacl": "~0.13.0", "jodid25519": "^1.0.0", "assert-plus": "^1.0.0"}, "optionalDependencies": {"jsbn": "~0.1.0", "ecc-jsbn": "~0.0.1", "tweetnacl": "~0.13.0", "jodid25519": "^1.0.0"}, "devDependencies": {"tape": "^3.5.0", "temp": "^0.8.2", "sinon": "^1.17.2", "benchmark": "^1.0.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "directories": {"bin": "./bin", "lib": "./lib", "man": "./man/man1"}, "dist": {"shasum": "6cb6cd073d27fc323207dd414614708f235a4b07", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.8.1.tgz", "integrity": "sha512-YzSug31Zaj/rE16yZ2OQeh41+Q3BTy3BSE9tjclA+t/LPkODgUG4Lgb0AyPLln7MJaDuhfY5Kn1wSmRoxNtPeQ==", "signatures": [{"sig": "MEQCIBpFrCSt5QhpgjnKsecD7lqUzNoY+S0Y/ZdqHuP6dJMsAiBh5o1dlngEE7FFd16eY4wH98zfkQ4dqg08Zi4RMfwdDQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.8.2": {"name": "sshpk", "version": "1.8.2", "dependencies": {"asn1": "~0.2.3", "jsbn": "~0.1.0", "getpass": "^0.1.1", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.13.0", "jodid25519": "^1.0.0", "assert-plus": "^1.0.0"}, "optionalDependencies": {"jsbn": "~0.1.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.13.0", "jodid25519": "^1.0.0"}, "devDependencies": {"tape": "^3.5.0", "temp": "^0.8.2", "sinon": "^1.17.2", "benchmark": "^1.0.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "directories": {"bin": "./bin", "lib": "./lib", "man": "./man/man1"}, "dist": {"shasum": "d217f6661c59ce7e042f49562a582e6a6d06b164", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.8.2.tgz", "integrity": "sha512-19JUh+gP15bgiT5LiFD5Hr1Q+eW0Mgrc+eVjNYM5OS4LfF4fyuRyXuBfPU0vKO1ZXAYnKiCj/uFmmPSlRyBI8w==", "signatures": [{"sig": "MEQCIFZKigoQv1Ff6cEDHNibxjSQEYCsgyYDtSzsTyGO8YGDAiAI4aAlm81Xyhw716mBb+OZQIygegUeblcz7fc92bjH+Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.8.3": {"name": "sshpk", "version": "1.8.3", "dependencies": {"asn1": "~0.2.3", "jsbn": "~0.1.0", "getpass": "^0.1.1", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.13.0", "jodid25519": "^1.0.0", "assert-plus": "^1.0.0"}, "optionalDependencies": {"jsbn": "~0.1.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.13.0", "jodid25519": "^1.0.0"}, "devDependencies": {"tape": "^3.5.0", "temp": "^0.8.2", "sinon": "^1.17.2", "benchmark": "^1.0.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "directories": {"bin": "./bin", "lib": "./lib", "man": "./man/man1"}, "dist": {"shasum": "890cc9d614dc5292e5cb1a543b03c9abaa5c374e", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.8.3.tgz", "integrity": "sha512-J8KGisqeY0NkCLaXSnmUkk9tIkIZ4g+PeB1hFQUuoAlsLGooNslBNZt9HSN7xgzRqisJ9xD0lpKNPxrT8n6OQg==", "signatures": [{"sig": "MEUCIEupbt/ubcYLV9NH0TMwyzepuJBajwM24HigxoIPtALMAiEAwWzaldYPnXurnNW65joPR7tHPIADctYcUI4mD/8p0A4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.9.0": {"name": "sshpk", "version": "1.9.0", "dependencies": {"asn1": "~0.2.3", "jsbn": "~0.1.0", "getpass": "^0.1.1", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.13.0", "jodid25519": "^1.0.0", "assert-plus": "^1.0.0"}, "optionalDependencies": {"jsbn": "~0.1.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.13.0", "jodid25519": "^1.0.0"}, "devDependencies": {"tape": "^3.5.0", "temp": "^0.8.2", "sinon": "^1.17.2", "benchmark": "^1.0.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "directories": {"bin": "./bin", "lib": "./lib", "man": "./man/man1"}, "dist": {"shasum": "b3ca15b9df95d36d8197ea2eb2123a70ef2349b7", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.9.0.tgz", "integrity": "sha512-u6/Gem+ESMUTEi2mYpeW7bUckz6S6xGkrzp6T4VloRP7+TS2M4kXlrA4kS1RGG0/Acxc8S4urGmQPD4JAtxFvA==", "signatures": [{"sig": "MEYCIQDOr2WZRfmyfuazywJDk7Fcn+LT5qiCbn96tf4jrMqSyAIhAPcTFGOw6l7Od9yicTML1zSjTqkoJQJ+n5Ctsb1DjdBn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.9.1": {"name": "sshpk", "version": "1.9.1", "dependencies": {"asn1": "~0.2.3", "jsbn": "~0.1.0", "getpass": "^0.1.1", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.13.0", "jodid25519": "^1.0.0", "assert-plus": "^1.0.0"}, "optionalDependencies": {"jsbn": "~0.1.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.13.0", "jodid25519": "^1.0.0"}, "devDependencies": {"tape": "^3.5.0", "temp": "^0.8.2", "sinon": "^1.17.2", "benchmark": "^1.0.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "directories": {"bin": "./bin", "lib": "./lib", "man": "./man/man1"}, "dist": {"shasum": "13a43cb36e05a87b97eb5939b5621e32a699ebd4", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.9.1.tgz", "integrity": "sha512-1wLb7YX+MQe60seAfMiYbtpG6+azOJghnjoxo3wRp4N6FvOuxes/emwb97ismPCzmc2yIR1nPVXEkwfcMEO07A==", "signatures": [{"sig": "MEUCIQCewoqdla0A0w9ADAb3GgdBz+GK8RM8AbinZ9urnjUBugIgBddciWF33/TSxSYEhbzrTTblrV9hwdmvQR52zr1ojyk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.9.2": {"name": "sshpk", "version": "1.9.2", "dependencies": {"asn1": "~0.2.3", "jsbn": "~0.1.0", "getpass": "^0.1.1", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.13.0", "jodid25519": "^1.0.0", "assert-plus": "^1.0.0"}, "optionalDependencies": {"jsbn": "~0.1.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.13.0", "jodid25519": "^1.0.0"}, "devDependencies": {"tape": "^3.5.0", "temp": "^0.8.2", "sinon": "^1.17.2", "benchmark": "^1.0.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "directories": {"bin": "./bin", "lib": "./lib", "man": "./man/man1"}, "dist": {"shasum": "3b41351bbad5c34ddf4bd8119937efee31a46765", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.9.2.tgz", "integrity": "sha512-ybqmfX7I4lILkWPMg1La2SC5B99iIDdGKmeuV+QBkOmkIqijX8gTQvmbYaEFwvZ7lNxjThI7XVx15SZlRwzR5Q==", "signatures": [{"sig": "MEUCIQC1E+fCLHGGH4Mowv8x4j5t0hbPKkvdz3wA2iXBgn4onAIgb82T+XxbyocMprJxO/Eg1XReD+p/Gd9ObGbCaGxLcag=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.10.0": {"name": "sshpk", "version": "1.10.0", "dependencies": {"asn1": "~0.2.3", "jsbn": "~0.1.0", "getpass": "^0.1.1", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.13.0", "jodid25519": "^1.0.0", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0"}, "optionalDependencies": {"jsbn": "~0.1.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.13.0", "jodid25519": "^1.0.0", "bcrypt-pbkdf": "^1.0.0"}, "devDependencies": {"tape": "^3.5.0", "temp": "^0.8.2", "sinon": "^1.17.2", "benchmark": "^1.0.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "directories": {"bin": "./bin", "lib": "./lib", "man": "./man/man1"}, "dist": {"shasum": "104d6ba2afb2ac099ab9567c0d193977f29c6dfa", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.10.0.tgz", "integrity": "sha512-G4fEgEQW0iRpmTsexYupcrttiidqcG0rdbfR2JzzI7s5znLbIRpo4WxV6QdSvviL0qqbBL+QIPeurfjsxMtqjg==", "signatures": [{"sig": "MEQCIDD7Fk7llaw0G0sonT+JvYH7zlq84Ff7SjTsr3SAkS6sAiAiIfTrU6Z83fJTgT40PV42mQQf8PmdSL0k/s/fxg5y9A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.10.1": {"name": "sshpk", "version": "1.10.1", "dependencies": {"asn1": "~0.2.3", "jsbn": "~0.1.0", "getpass": "^0.1.1", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.14.0", "jodid25519": "^1.0.0", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0"}, "optionalDependencies": {"jsbn": "~0.1.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.14.0", "jodid25519": "^1.0.0", "bcrypt-pbkdf": "^1.0.0"}, "devDependencies": {"tape": "^3.5.0", "temp": "^0.8.2", "sinon": "^1.17.2", "benchmark": "^1.0.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "directories": {"bin": "./bin", "lib": "./lib", "man": "./man/man1"}, "dist": {"shasum": "30e1a5d329244974a1af61511339d595af6638b0", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.10.1.tgz", "integrity": "sha512-ywhcxCSLw4u+wfJM4XDCtuyXmGEiJ3kHZcnaiehYztqaRf3hj9RE8yamh77ToBD59GxfrQZawktGV+vm8cFdPw==", "signatures": [{"sig": "MEUCIQCofWmR5Lg0NPms2HhCw8Jkkz4PLktACfTp6AIjerv0oQIgdkOv3qNVwjBVx0DB0TF+ks5W+bKQof11RFt1HPNjriE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.10.2": {"name": "sshpk", "version": "1.10.2", "dependencies": {"asn1": "~0.2.3", "jsbn": "~0.1.0", "getpass": "^0.1.1", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.14.0", "jodid25519": "^1.0.0", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0"}, "optionalDependencies": {"jsbn": "~0.1.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.14.0", "jodid25519": "^1.0.0", "bcrypt-pbkdf": "^1.0.0"}, "devDependencies": {"tape": "^3.5.0", "temp": "^0.8.2", "sinon": "^1.17.2", "benchmark": "^1.0.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "directories": {"bin": "./bin", "lib": "./lib", "man": "./man/man1"}, "dist": {"shasum": "d5a804ce22695515638e798dbe23273de070a5fa", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.10.2.tgz", "integrity": "sha512-KQh7n9HiKvK33VUUAew1GE7GC0kaPyvVQk+bulVMVJdLsvrMsjFpk44qQASup2gm4ZxAWQmqPZV1AzhCzyk1AA==", "signatures": [{"sig": "MEQCIDLnG5Z7+S0s9TFF5qiZ03C460XBPeas3MAruKcagefgAiAdTWlhQ2VGzjuC+N6hR3p08RKWwd2WmMtDJbd5deim1A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.11.0": {"name": "sshpk", "version": "1.11.0", "dependencies": {"asn1": "~0.2.3", "jsbn": "~0.1.0", "getpass": "^0.1.1", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.14.0", "jodid25519": "^1.0.0", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0"}, "optionalDependencies": {"jsbn": "~0.1.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.14.0", "jodid25519": "^1.0.0", "bcrypt-pbkdf": "^1.0.0"}, "devDependencies": {"tape": "^3.5.0", "temp": "^0.8.2", "sinon": "^1.17.2", "benchmark": "^1.0.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "directories": {"bin": "./bin", "lib": "./lib", "man": "./man/man1"}, "dist": {"shasum": "2d8d5ebb4a6fab28ffba37fa62a90f4a3ea59d77", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.11.0.tgz", "integrity": "sha512-xxv7jMGrKPHioDhw9zr2e4t2XwmuqfIP1mj4GT0/9A78LywlzYJD326y5IgiLssCMwF7hCSU4cqQlONkq0eZeg==", "signatures": [{"sig": "MEYCIQDfux1mF6JEfTDN2z8vlFwCdVVlQarJ74yFboB7n+j94gIhAN66RIljlZcoGXEyo6AWw30qF1X2bXgvtOXZZMQTHFPs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.13.0": {"name": "sshpk", "version": "1.13.0", "dependencies": {"asn1": "~0.2.3", "jsbn": "~0.1.0", "getpass": "^0.1.1", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.14.0", "jodid25519": "^1.0.0", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0"}, "optionalDependencies": {"jsbn": "~0.1.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.14.0", "jodid25519": "^1.0.0", "bcrypt-pbkdf": "^1.0.0"}, "devDependencies": {"tape": "^3.5.0", "temp": "^0.8.2", "sinon": "^1.17.2", "benchmark": "^1.0.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "directories": {"bin": "./bin", "lib": "./lib", "man": "./man/man1"}, "dist": {"shasum": "ff2a3e4fd04497555fed97b39a0fd82fafb3a33c", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.13.0.tgz", "integrity": "sha512-4pjoCmB+Vta6odJ3nrT2zf223+xW6mOJfYtHGX7n5CZUAVm6GhuHLbKeai3c0XoWCu6ZNyPo3Nf71CaUbgck2g==", "signatures": [{"sig": "MEUCIQC95OwIONegCq2GVsOqowX9ymrIeFz9yFtsB2noDEFhOwIgHVU5B15EeVSEJspQ5PlS/WoIW4iN1kAD4CrujSXUj1s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.13.1": {"name": "sshpk", "version": "1.13.1", "dependencies": {"asn1": "~0.2.3", "jsbn": "~0.1.0", "getpass": "^0.1.1", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.14.0", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0"}, "optionalDependencies": {"jsbn": "~0.1.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.14.0", "bcrypt-pbkdf": "^1.0.0"}, "devDependencies": {"tape": "^3.5.0", "temp": "^0.8.2", "sinon": "^1.17.2", "benchmark": "^1.0.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "directories": {"bin": "./bin", "lib": "./lib", "man": "./man/man1"}, "dist": {"shasum": "512df6da6287144316dc4c18fe1cf1d940739be3", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.13.1.tgz", "integrity": "sha512-oKPL4k+ld5d737ZPB4VM99bqNb3JH+ZkxwWXR1iSPtqYbZv81hkLD7v8gmclrgR7aA8uh8ar+q0IF7gHFHg7PQ==", "signatures": [{"sig": "MEUCIQDMsGQbhnkCQNvhXLKCAM8Iz8ulD4lVUGUh1cUR/aMW3AIgBhUmNFSt6PV6AylnAyF7pw7IEKMSVADyr+pqymxDM0M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.13.2": {"name": "sshpk", "version": "1.13.2", "dependencies": {"asn1": "~0.2.3", "jsbn": "~0.1.0", "getpass": "^0.1.1", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.14.0", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0"}, "optionalDependencies": {"jsbn": "~0.1.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.14.0", "bcrypt-pbkdf": "^1.0.0"}, "devDependencies": {"tape": "^3.5.0", "temp": "^0.8.2", "sinon": "^1.17.2", "benchmark": "^1.0.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "directories": {"bin": "./bin", "lib": "./lib", "man": "./man/man1"}, "dist": {"shasum": "856569df3ffa8464658a393984aed2a48655e014", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.13.2.tgz", "fileCount": 35, "integrity": "sha512-DNemA6WwC2HcQjDR3qp1Nb6R86+6aAN1UxaWxlZQuVLraNb8TOZXiXfcEktKYSEsPB1JOHunmS080Kc9nZivIQ==", "signatures": [{"sig": "MEQCIHmruVlLAFi17tJ+JVLgorkzDhmY6Z8G1U5mhpFia7jmAiBG8ywEbz6gZqN7aOOI3in9Su8ZPCk7VZZBRO0G2xaWaA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 208057}, "engines": {"node": ">=0.10.0"}}, "1.14.1": {"name": "sshpk", "version": "1.14.1", "dependencies": {"asn1": "~0.2.3", "jsbn": "~0.1.0", "getpass": "^0.1.1", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.14.0", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0"}, "optionalDependencies": {"jsbn": "~0.1.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.14.0", "bcrypt-pbkdf": "^1.0.0"}, "devDependencies": {"tape": "^3.5.0", "temp": "^0.8.2", "sinon": "^1.17.2", "benchmark": "^1.0.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "directories": {"bin": "./bin", "lib": "./lib", "man": "./man/man1"}, "dist": {"shasum": "130f5975eddad963f1d56f92b9ac6c51fa9f83eb", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.14.1.tgz", "fileCount": 35, "integrity": "sha512-/+d3fDmcbBSaYbLHQ/BU9Kt9ZMuyU9kZyG6Icbu/sMA2AN5NgfGuKSAJBl62I5NFsopfVwkufmj5cCRIEXq36g==", "signatures": [{"sig": "MEUCIQDSxmm7XbUzAwi7bVCbUprLoAxdspo9/d0RyyILD8oYqgIgX+4ISE3Erbqrc0xRs7YGTp2M3+np2sJtT2UUvskJN4Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 208057}, "engines": {"node": ">=0.10.0"}}, "1.14.2": {"name": "sshpk", "version": "1.14.2", "dependencies": {"asn1": "~0.2.3", "jsbn": "~0.1.0", "getpass": "^0.1.1", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.14.0", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0", "safer-buffer": "^2.0.2"}, "optionalDependencies": {"jsbn": "~0.1.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.14.0", "bcrypt-pbkdf": "^1.0.0"}, "devDependencies": {"tape": "^3.5.0", "temp": "^0.8.2", "sinon": "^1.17.2", "benchmark": "^1.0.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "directories": {"bin": "./bin", "lib": "./lib", "man": "./man/man1"}, "dist": {"shasum": "c6fc61648a3d9c4e764fd3fcdf4ea105e492ba98", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.14.2.tgz", "fileCount": 35, "integrity": "sha512-XIQCY8Ye6pY6rRNG+eCQiHyapz1vDY4OsMowlmy31arzqWPjC9phqZoVy+F/Oyz5xjsaDwgBpIMQmhj1kSJJOA==", "signatures": [{"sig": "MEYCIQCFCZFyMiSwPvjBaIvlp+rHojPbS+bxlq+ykH4qBclhxAIhAI9wQKRTVKxNQJC236DeBVUzMpdRTNVdZ2lAicft010N", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 209067, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbFtZyCRA9TVsSAnZWagAASjUQAJjjsdwBj1T7yJoSIYPj\n+0F1gZHNxlYUc713gd4TULwPLtMRpd8E/J/gSpNesHkWRk0wEK4ZXvU9BoTu\nj1guOxt2O5053rigJp7lGF2n4YHL41Wg6JImoB2aNSHwjN/ePo9VMZwq8/Gb\ndd06wHyYFyvbW6ntlV/UXKKcmyiGbjLq3ZEatmqgMwWL5h/Xvx2Vt0MAe4Pp\n7bsk7zy/1OUuz4IVXFnMHQ5+NS33wUCUAHz9JQZCzR2ZNPXx5j1ctH9We3XG\nybkKGMHoiDfaVRt//sJQEmtpgB5OBxU4UX912SsV3oTGsxkl+vT8CigVisE3\nsg0JIgx1JKxeTOJ9kF9cwuLOw5GoGp797n/btqSlSSQhF7ZbGMFk0FEvXIz/\n3ShYUrIRdmLgHMd2IbTolOkQXbsylBnuuGCTNGztvLpYCD8QCCRq2SywSWrg\nUY1Jtj2NdCtHyFR2rnsS2pyiymxHXxGEA7MebLNb7+VJ1Ti0PFC0gLMYHDyG\n5x1WPzXuVPv67Nhh2+1CGpEhj5Ln651Lj6iMduPrqpvFFnGROE0byot/FMvY\n479pqy2CN9v2XHxPyncKl/AhypLvzxvpzgThtl4E8bQoGyfIF1AvKnVB8D9Y\nAgRI3fYNNQXx73Gy1aAmASiwpoRQEFe0R3Yn3xEtobU/zMhGhDRfVBI9fZkX\nAMoW\r\n=/035\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=0.10.0"}}, "1.15.0": {"name": "sshpk", "version": "1.15.0", "dependencies": {"asn1": "~0.2.3", "jsbn": "~0.1.0", "getpass": "^0.1.1", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.14.0", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0", "safer-buffer": "^2.0.2"}, "devDependencies": {"tape": "^3.5.0", "temp": "^0.8.2", "sinon": "^1.17.2", "benchmark": "^1.0.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "directories": {"bin": "./bin", "lib": "./lib", "man": "./man/man1"}, "dist": {"shasum": "74e084946318c487a6c7c64796f864301ce35886", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.15.0.tgz", "fileCount": 35, "integrity": "sha512-fAUqwOzq7VlJ/pOgQWRJZmyMXGwXLgslaHYyJpNNg1uHoVFgW3Y8NuC8YBWaPV/9LltfgorM1Z9S2Q+DXwqxgQ==", "signatures": [{"sig": "MEYCIQC5mhyatJrE+ivquKcNJ2SaODUdF5CuJn/T/ujay+YLugIhAOn2Qnbs1RjkQKD08t1Fr69XcSJyNwYiMqmp1cUJdk2Q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 214130, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbv+P9CRA9TVsSAnZWagAAsQcP/1UXq7WNgDwO5el1p8bC\nFr5XZJ3uAtOkoJct5szuZK4hZ0UI/H4g6MkBOztesrKrM9JP5xASNoq/zDOX\nI6+dzRIRyna4YeVf5CtEXnMLdLLH+Wn2jT+7MPhR84CRJJjK6lKj7dKVIXAR\nku9EBHKQtBaddG94uiiFHbDTyhAMw5WhVG4JWujwBiWmAfjio3WIE8myAyrR\nVBaxjALEZTHJBGAK55OLq/JYr4MGeVCsmhsg6Uy4TUUBH5HDVRNzG5CHSNVX\nT/wHLs7Y7akab1e4HN6ts6UViq4z/kvaN3n7XRqqOY9klZ8Djmh5IVJD85lZ\n19jg7ze0FvC1AHvXlQSeirMHjQ/S8q1M4W6w1o4h6iuEkfyPvgOPtS/Ybnj8\nCD1U5svDku5HHkUml0EZtxiiWxRV+Z9yQ+buZISr8riPQf4ttc4IAGdhA0Y8\n/CE1yzI4qbiM2F1guBxSFB1n1UfgyXbcHd0MgivRjXgl2cCaejWKjEPI4U3T\neX8jRfWSVEqefDZt2JmJ0x8StpFTOQ4YFhjRP1DWlzttKaumC0gjkHlfc+r0\na1jDmhb3r4M46OGiflAfiUuRgalL1QIj8oe7Pjf0mZQL+YqxxJ3zHInbp6z5\nMXR9G14c6rlMBnZSH68bugySV0zpljtDK9MXTEibk7AhE7452T1NwP267WM/\ncgm5\r\n=FkRS\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=0.10.0"}}, "1.15.1": {"name": "sshpk", "version": "1.15.1", "dependencies": {"asn1": "~0.2.3", "jsbn": "~0.1.0", "getpass": "^0.1.1", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.14.0", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0", "safer-buffer": "^2.0.2"}, "devDependencies": {"tape": "^3.5.0", "temp": "^0.8.2", "sinon": "^1.17.2", "benchmark": "^1.0.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "directories": {"bin": "./bin", "lib": "./lib", "man": "./man/man1"}, "dist": {"shasum": "b79a089a732e346c6e0714830f36285cd38191a2", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.15.1.tgz", "fileCount": 35, "integrity": "sha512-mSdgNUaidk+dRU5MhYtN9zebdzF2iG0cNPWy8HG+W8y+fT1JnSkh0fzzpjOa0L7P8i1Rscz38t0h4gPcKz43xA==", "signatures": [{"sig": "MEQCIBcDpurZfzJRzO8i5DIh0k3vYqib/bznYHQsLbbcaig4AiB/OPi6Z+m96oxWB9C50pZnGVxvEi9yMJU0jlGiGJ//Ww==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 214292, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbv/A1CRA9TVsSAnZWagAA5gYP/2AnEPtbUm5vw1bvo0CG\nCiyRWL+Is/CrgQ8N2xWu1ZwQYmVBphqS0vnHcY9cM4SHWUIzlNIPm6I/cbUx\nH/003KgYIYDscKZz/HIHpUxlCMroDLO0/ZaRI4ICpApTsqqOZqtlixO6ONMW\nUB1wotJhgH6qUj6Oo9aMPlXk+UhF9oK8L8xlEX9NZEWiOH3q7glz9WEODV+3\n1/HylenpFRA8050NAevdg6GbfaEA5eNtUbwk7LLDKyp0O04PD75b9Rsgp+Z9\nWVdcmq3QTmnX2R7IDMCcLfRD7gpV7OmzOwcGZZow/c42CYm+MU9R3VKODARZ\nc/JXecahgd9hsKVKm06j4iNkk0susvtFG1LH6+LqakSzYm7NPXAYO8kZjhqM\nE1jXuGxt6h5hSMNTIyRVz3EzKTEKpLnaVl5bniwm5s1H5CIsxYjtUAJ2EB2k\nT4S474JyoPSQMV+H3FhH/Fr/Opv3iA3babWv1h49bs8DPw3hOM0UQzZMWH5t\nOIRvQ8/XtkdAscWOqj1nZ2mvnNT8JiuJP17V4oUybzGpNoyEM2b8Sp/D/Vpr\natwJXKTClyGE4OTVDW3Tp3+e/vM6TNlBNdxadtsVvBjuG1wVAP7vTVA7nNOL\nM8wkLHgw6ovu4Ylj5Z1mC9Sh81E4kp0r5oyhMayOij7L0rfq9Q9oAheKYzOs\ndrnu\r\n=eFBo\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=0.10.0"}}, "1.15.2": {"name": "sshpk", "version": "1.15.2", "dependencies": {"asn1": "~0.2.3", "jsbn": "~0.1.0", "getpass": "^0.1.1", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.14.0", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0", "safer-buffer": "^2.0.2"}, "devDependencies": {"tape": "^3.5.0", "temp": "^0.8.2", "sinon": "^1.17.2", "benchmark": "^1.0.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "directories": {"bin": "./bin", "lib": "./lib", "man": "./man/man1"}, "dist": {"shasum": "c946d6bd9b1a39d0e8635763f5242d6ed6dcb629", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.15.2.tgz", "fileCount": 35, "integrity": "sha512-Ra/OXQtuh0/enyl4ETZAfTaeksa6BXks5ZcjpSUNrjBr0DvrJKX+1fsKDPpT9TBXgHAFsa4510aNVgI8g/+SzA==", "signatures": [{"sig": "MEYCIQC3rbbXwe5WsHCHAWfOWFsyxlRBKSfK0zbtko1gThJZbgIhAKhkFSuafquiz5q6S8E3IU7n8Dz84f0V9OeQu/b5oVkT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 214293, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb18lUCRA9TVsSAnZWagAAGE0P/iE1eESfe+WQTpA37kse\n57qOXlCg3Wj70NwCI/Q6XqjWj34K99f2zafhDoccUNWYje4B3nHWMazkO7WY\nesnETDsQWt3TPF7TiB3mABQHvmu1C76so5cLx6uDiHwZIBKPaCJfZmjiFvOY\nyDT+fmhwRfQJSrjgfOJ9L5KxYmubkqXk2IGNdciiYEP4+EkiI73P/Csmy/7m\naJliAVkRE52iX+9k4cKRoJdNPBJeezYhwF14lQG4ysKxrujh+0Dz0dO6tuH/\nEHxkjJSAfquJskcNFruUVP+Yw5vW8jz4JXnIRbT3TfUCel2W/1/dkqY3YzN5\nCQ7zTsRgd+POWucVG4GZ2hfQWH0xHrpjrazEqATk8ninFwaoQm+Yv4WSXklq\nESCdR1SYffP28cWVKF6r3SHV0Mag7k90tWP5maclCBSJYR8fkwocie4ChVV+\n3lJozYiwU3IdFjUin+4g6JHcCy+KZkRIcTF54N04JIpCBuusdnDwfsmPLDo9\nkGu9CLksr62YVr+h6dmkELI5vYSIVafppWpugwq0avBzy3WLu78wKqQ4X8EY\nvPGQQrXtwcwisyfe2EvjoeUuHC7eV+szW2eo/TStYKfP5OE/W5E7OessodOv\nOXzmUH8KFmHJyrr+GbBPYWAF8ikNI6eeq3zxM5CAgFo5qLVgj6e+OtjtJoAV\njVq5\r\n=ij+M\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=0.10.0"}}, "1.16.0": {"name": "sshpk", "version": "1.16.0", "dependencies": {"asn1": "~0.2.3", "jsbn": "~0.1.0", "getpass": "^0.1.1", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.14.0", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0", "safer-buffer": "^2.0.2"}, "devDependencies": {"tape": "^3.5.0", "temp": "^0.8.2", "sinon": "^1.17.2", "benchmark": "^1.0.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "directories": {"bin": "./bin", "lib": "./lib", "man": "./man/man1"}, "dist": {"shasum": "1d4963a2fbffe58050aa9084ca20be81741c07de", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.16.0.tgz", "fileCount": 36, "integrity": "sha512-Zhev35/y7hRMcID/upReIvRse+I9SVhyVre/KTJSJQWMz3C3+G+HpO7m1wK/yckEtujKZ7dS4hkVxAnmHaIGVQ==", "signatures": [{"sig": "MEQCIDUJ0YnJdEfaLikyDrLz344d5Mxtn631PSsg6k7GftamAiBLoN3Yg/zBWoyROq/UWy5J489d7+r91MDqM5ZW+H0paA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 224787, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcHEepCRA9TVsSAnZWagAA2OkQAJSlbVdgQ/jlfALbl8NW\nEJA6sWX1GI6A8tPNbdAed4SvcACXotB8ECRXdRtLwHAHR0bOtF7VLJ2TpnIB\nAqME9pAjDyc/FThoRNSn0pnOgYSQhbSq8aHDHvEMnWuEQP8GestoD3WVGG4N\nMzxwYy03zLWoJtsLToooAD1wD0a9gqtUU5QNjLVOPbPozi2hdv2dIVkqFGON\nOULDFETXURHG7JxzvGiGrx9r6szoXd5ogEgzKfuIM4Ybdg62E5zdhgVSf/AP\n5vHosBQGwUgpYS8mlgAaQrKy33/bf+hf5RQDk/CMbMcWPe89w1uK15GbHzmE\naktB/0kLvn7Aa5McpwQet4wqssdPoPHGnxT7NBufFMRMEcwb6G7iab9bSlb9\nc6OnOhFz0AN6gFNMzI+vKyq9sh/YtHHCmt5pOgtU4fEDRpNDtT0kZpJKo0fr\ns5QVPXDkwlQfvCbg97FMfcc9jt0kW2p3mchzTWireb6Eu273+Ge49FRcoUVY\nUUAUI64MyRWcZzPf/BiYuLRmtLVrk0eKvmxq4KG+5YOgLRaL7kpC4TVz16eq\nLXVcHksejSZrnyEe7iSVvA4Rg45vn4amnPTUNocPduQ8Q2g0wC+3bAatKHcI\nL4eWqSMfrzPa6TTIMJLojyqt+P2GJC69Y+/I0Sv2QrUu0ASUVNUsjfoMy/SB\nIhiD\r\n=KY/A\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=0.10.0"}}, "1.16.1": {"name": "sshpk", "version": "1.16.1", "dependencies": {"asn1": "~0.2.3", "jsbn": "~0.1.0", "getpass": "^0.1.1", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.14.0", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0", "safer-buffer": "^2.0.2"}, "devDependencies": {"tape": "^3.5.0", "temp": "^0.8.2", "sinon": "^1.17.2", "benchmark": "^1.0.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "directories": {"bin": "./bin", "lib": "./lib", "man": "./man/man1"}, "dist": {"shasum": "fb661c0bef29b39db40769ee39fa70093d6f6877", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.16.1.tgz", "fileCount": 36, "integrity": "sha512-HXXqVUq7+pcKeLqqZj6mHFUMvXtOJt1uoUx09pFW6011inTMxqI8BA8PM95myrIyyKwdnzjdFjLiE6KBPVtJIg==", "signatures": [{"sig": "MEUCIBqeAViYL8NvIdnMv3FSeYai1KnjK44joWKx7U/5tCqvAiEAhU36u79rmOQnYlzKwYMEBBauiYKEIDSa80P5FxDOpKg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 225493, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcSN3gCRA9TVsSAnZWagAAXGUP/0r6539fiSwCFuS8XYqB\nXnM4k2f4KvlCovAhf/3Xrn/3713RqKNF5P2TSHlBcJVFO5F5YIQnG1qwNXwk\nuIK89IxnsQaGQBATMielB8zmt40P5tOFDVZs+aDc2mCjdBgmQ/UFeZCm99+a\nFFUnTAtGV5tY3Wk+BgUYRiRhdHAcbSR0/bMtLjzcyZiVxyUg3YNyRJMa8zVK\no5qmhHDADsIYxhU5zxNwVu07lUtOwK/uL74ZuTXvaF5PhcCQhy9+4vNkvfIO\n3g/lFOU+qR7hNYs9MYOlTY3gbMV0umDtn802NSiic9u/FFB9hqOREDv0Xljn\nIrHy3SCzEzDj83pVR25Hn6p7SKN1KSevVP7go61ZoDAyzY+4MMNPu+4cdopH\n52HPdLRmdfDIkdPRf8nS9w3eUnPG/+SD24yhCW4NbCAkSX2is1KEQqrcZDQg\n5817ko30vdNpG5UtBVIeYGlGUJoViYjgvM7/duvlW+9IT/XRotACNUBSrOT5\nF3j9+Mlx9J7hMIAk8yNzh6Y6HRf2J7ACNwU5ehn7XiUsROzjjcTM6zEaosjX\nCR/Qjru4UePVAKT2gPKcDxSAPlA1G6lSEl9xTqfpg4nzfTdEjTiDAZHC39or\nw05p174+vuXy2RgU4ebLZ6EFdeKL+l0KU1htszw04UatsaPiGMsAJH7sVyMI\nkA8N\r\n=aIg1\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=0.10.0"}}, "1.17.0": {"name": "sshpk", "version": "1.17.0", "dependencies": {"asn1": "~0.2.3", "jsbn": "~0.1.0", "getpass": "^0.1.1", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.14.0", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0", "safer-buffer": "^2.0.2"}, "devDependencies": {"tape": "^3.5.0", "temp": "^0.8.2", "sinon": "^1.17.2", "benchmark": "^1.0.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "directories": {"bin": "./bin", "lib": "./lib", "man": "./man/man1"}, "dist": {"shasum": "578082d92d4fe612b13007496e543fa0fbcbe4c5", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.17.0.tgz", "fileCount": 36, "integrity": "sha512-/9HIEs1ZXGhSPE8X6Ccm7Nam1z8KcoCqPdI7ecm1N33EzAetWahvQWVqLZtaZQ+IDKX4IyA2o0gBzqIMkAagHQ==", "signatures": [{"sig": "MEYCIQCREA9xUWnPeycgVlo3WGA9hzjFEwUypGHIO86hYRx4IgIhAI4GOGbflCUXlKFJWAeUHzsKZTnartAl9iOQH/LoMDsW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 230511, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2KSRCRA9TVsSAnZWagAAoCEQAKULF8esvxDrotZij/Bd\nogIQ70AWe4brcXAbFB8Xs2MZA4PfsEaBocCI1iEVT0oZfhUrMBPnqR+1GaiZ\nHZtq19rWjd6GMWZjGgkAr24Mg6EVahHblATpBVe5B4wvUrt8z5NVXIFn9dDK\nX2JFlapQT5+ENbqV7eyCmh3in3ZCwuqP4EezAVR7XSbl+8UHKmPj2KxPJKI6\nKsW1U0+lBoVHi7oRKXWcGDfziO5/aQQ7vtdCpRBYcIzheXolYoTdpjRn3EEo\n5aqNV/fxHviICLtR6GmVmKgSTPD9am+MV4qJZUrfxxDpZo4uDUEoNgPaOIUm\n2NrCmolZyWHXayRFQ4R07tSuEn/oyniudmtDQHjzYAYtsM2F3bycvzMogtEI\nYJXyJCgggOGkUGYpcyrLA3uyAue5xp2aITbgHlLzn737L38OJ9+38+cTpMrT\nbBrJhyfOWu+7kTyLHIOAWy1A7iClzZZncee9fUyYLVYJFEsGH5RrXAcZoMMc\nW6NQ01gyvazDk1wN9LI2RJnA9bdWVEDlzIAISNlkJDtOzLx2oZ+IS7mIvgvi\nq78+EC06bggxmoM62ec3yKui7+1a1JzsB3+hHWVDo5eE+QESs61c61/wD01P\nKI9Fu6JYmZW3Tgx4p23xXZLnD+Vo/1QXIHGkYG+7rNVAm2J2r1aMERuFsWeh\nqege\r\n=KU2y\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=0.10.0"}}, "1.18.0": {"name": "sshpk", "version": "1.18.0", "dependencies": {"asn1": "~0.2.3", "jsbn": "~0.1.0", "getpass": "^0.1.1", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "tweetnacl": "~0.14.0", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0", "safer-buffer": "^2.0.2"}, "devDependencies": {"tape": "^3.5.0", "temp": "^0.8.2", "sinon": "^1.17.2", "benchmark": "^1.0.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "directories": {"bin": "./bin", "lib": "./lib", "man": "./man/man1"}, "dist": {"shasum": "1663e55cddf4d688b86a46b77f0d5fe363aba028", "tarball": "https://registry.npmjs.org/sshpk/-/sshpk-1.18.0.tgz", "fileCount": 36, "integrity": "sha512-2p2KJZTSqQ/I3+HX42EpYOa2l3f8Erv8MWKsy2I9uf4wA7yFIkXRffYdsx86y6z4vHtV8u7g+pPlr8/4ouAxsQ==", "signatures": [{"sig": "MEMCH3KmsTHyjBLhb29KbohmsZYfyUQi9BELtOmfvnrL24ICIBPFOH9yEV++smfnirjMLTZu2nL05zA6bIxKAQ8kxNRH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 230846}, "engines": {"node": ">=0.10.0"}}}, "modified": "2025-02-07T15:28:20.204Z"}