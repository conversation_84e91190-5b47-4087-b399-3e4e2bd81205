{"_id": "fast-uri", "_rev": "27-872f8fe1866441882cd52cafb9ba3fff", "name": "fast-uri", "dist-tags": {"latest": "3.0.6"}, "versions": {"0.0.1": {"name": "fast-uri", "version": "0.0.1", "author": {"url": "https://github.com/zekth", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fast-uri@0.0.1", "maintainers": [{"name": "zekth", "email": "<EMAIL>"}], "tap": {"check-coverage": false}, "dist": {"shasum": "8ae168f4f9566f98b6fdc58924da4702f2a697eb", "tarball": "https://registry.npmjs.org/fast-uri/-/fast-uri-0.0.1.tgz", "fileCount": 22, "integrity": "sha512-XX/gBR9Xge9sDBWPcweAMhr0CWrEm8S+GP3fVSX//SrkaKh0Wrll0pvpvKKHJ4FG6CRkuQbVb5UomG1qICGCow==", "signatures": [{"sig": "MEUCIQCIaDrfyW08CkCo2FFDJWLgqcDxVGJhIja7LEJ76ZzaZQIgCmwIZYWNuvPAKwS2AkTfuR5DUI0Da7f+eLoynh3kie8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65155, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhtdH8CRA9TVsSAnZWagAAGs4QAJzeH0Kwuh0pqJ2vVpKF\nFbceAjLhse/jZMBuNh1EeN1jOS7eJUc72EWMh1W40liH4uNhw2YRVSc02Wo4\nHXcSgxAu4QYj3TS8uiVjwAE2jd7yicqPqHqpVsNs8h9p62S2O8g+i2+MBcdm\nyR5Hfg0R3lnvKlGj0IZ4COcmGjM7IprmdhqGuPnsJakYcJITImHJ3UG30AQX\nQauo4CulpbxKzbeZ1TCAzyCgJdQBIY55mVkQmtLeOnwXYiaTdxX7OtfGtxwk\nJ3SWwfReIEve2PC/CpjY9Z7h7sNB0CgDdhvixRQgqLxlwwZdVAC0zyYrICSi\nJ1zGPQCN/D94iKmkzXty/n5MuC96R60C0YVH/pfn+hl/rFxnow5uRdvM9Ykh\nijUgEtRH5FkweoDRRry417PCfifoo/Bax4lpzxcy74B32QNeqy1grTp3Axc9\nHWk85dqKAxb2Z0/FXjqzIZjpTm1+ffMPQ0PggfCPrsgrsSAQ2VRMLc+NndNm\nUfIX0qeqsP98TOeBYA3zsnuMUviHT8NNboRgZaVaEigPEZEs+FSagya+FFZH\nHZSweyUgTlB8lcw5WxXpKQve5W6O62EDXgDGtMk/EOTbTdv1nQx5+85oQm7T\nrXIf5jZBvZHowDbVXy+uqV0vBDe6tckSHTdtegDoPOL397Yf4iIq9N55E0t2\nEPiD\r\n=r1rA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "./index.d.ts", "gitHead": "7ec8e5d71cc464168e2831c7fc494388d27a283b", "scripts": {"test": "npm run test:lint && npm run test:unit && npm run test:typescript", "bench": "node benchmark.js", "test:ci": "npm run test:lint && npm run test:unit -- --cov --coverage-report=lcovonly && npm run test:typescript", "lint:fix": "standard --fix", "test:lint": "standard | snazzy", "test:unit": "tap -J test/*.test.js", "test:unit:dev": "tap -J test/*.test.js --coverage-report=html", "test:typescript": "tsd"}, "_npmUser": {"name": "zekth", "email": "<EMAIL>"}, "_npmVersion": "8.1.0", "description": "<div align=\"center\">", "directories": {}, "_nodeVersion": "16.12.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.0.10", "tsd": "^0.19.0", "snazzy": "^9.0.0", "uri-js": "^4.4.1", "standard": "^16.0.4", "benchmark": "^2.1.4", "coveralls": "^3.1.1", "typescript": "^4.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/fast-uri_0.0.1_1639305724331_0.8721382480239435", "host": "s3://npm-registry-packages"}}, "0.0.2": {"name": "fast-uri", "version": "0.0.2", "author": {"url": "https://github.com/zekth", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fast-uri@0.0.2", "maintainers": [{"name": "zekth", "email": "<EMAIL>"}], "homepage": "https://github.com/fastify/fast-uri", "bugs": {"url": "https://github.com/fastify/fast-uri/issues"}, "tap": {"check-coverage": false}, "dist": {"shasum": "3bf622e7f65db1ca4c068ea227e847f67556ea1a", "tarball": "https://registry.npmjs.org/fast-uri/-/fast-uri-0.0.2.tgz", "fileCount": 22, "integrity": "sha512-JmJ7NLpJjIVaC8P03wRHa6SY3NN5/k46iJ6ZRCVN3/crbueGrqqVIbeJ0lxQyzTNaFA+nWmazmtRwRPBuLNJ/Q==", "signatures": [{"sig": "MEQCIAm2XN8L3IY1gnjqIk12VKmCSd6PqyS4s5nlPyt7Bo9RAiBYxd6HBAlqD5zXCpXS+oqy432OirK3Fvkgx3jjoXSflA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66058, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6nX3CRA9TVsSAnZWagAABZcP/jMIY4EPahkcAUn2SXW4\n40SbmpWzHeNO4PK/2iHswST6yJywr4dK4EggWi1Ot4oia4ikjWeSLekIJsIV\niMfl3BbnoXk5yyFmD7qPxfpCyBIVWOtOZO8eHtj1GptxJhM4SynTxijZHqtm\nga4fkmjp4LguNFew44xM4EfATL/OWwy+tqjgJwCRNA2k3xygizN3dEOw/m2m\njHRYGowQmkVE73jdShDyoxSpc7dsW4/pxAyag4EcogwdM5aUVrdiGDDNhdfV\nB2rsWzdyM+EVMFNG0/V3zQbEUdge3A+dtD1KO+WEf7+RR/lc9VVd1z2zpojD\nKzgbfztFhbeQOG1CWEltuw8L9sN+ORuubP5vvPERFuNREgGI8XuLazks54iL\nA67FmyCQJDSiQZgG3X1mSyEXWAOZhs/2UzhvOK9yVSNpaBQL6lgFSb5YXDLP\nJ7nyXa74mzKdXlkou90MxF2z/R7So3ZbWXEZuxx2byu87uUlrtSOa0m4otKN\nVxalI6apMLO4y9zPhfOYlUEAqSE0lzdVR70fIGmP405XYQ1bq+/3sNlvfH45\nyPHB2XXpuys+xCpyNXJqrsg0QH+wDIUeOCNlVVpgmQvvSL7xfu6NiKd7RoEE\nZR1p+lPwsw6ZIJiJfFVgiraOaqvh1hTY09wj+NLprJ/r6P83UBOKF3rLxLQ7\n5LEE\r\n=7cmL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "type": "commonjs", "types": "index.d.ts", "gitHead": "25aa5527b617910f4bae4f41a8d6566ea23e6b8a", "scripts": {"test": "npm run test:lint && npm run test:unit && npm run test:typescript", "bench": "node benchmark.js", "test:ci": "npm run test:lint && npm run test:unit -- --cov --coverage-report=lcovonly && npm run test:typescript", "lint:fix": "standard --fix", "test:lint": "standard | snazzy", "test:unit": "tap -J test/*.test.js", "test:unit:dev": "tap -J test/*.test.js --coverage-report=html", "test:typescript": "tsd"}, "_npmUser": {"name": "zekth", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/fastify/fast-uri.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "Dependency free RFC 3986 URI toolbox", "directories": {}, "_nodeVersion": "16.12.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.0.10", "tsd": "^0.19.0", "snazzy": "^9.0.0", "uri-js": "^4.4.1", "standard": "^16.0.4", "benchmark": "^2.1.4", "coveralls": "^3.1.1", "typescript": "^4.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/fast-uri_0.0.2_1642755575310_0.9253269096183165", "host": "s3://npm-registry-packages"}}, "1.0.0": {"name": "fast-uri", "version": "1.0.0", "author": {"url": "https://github.com/zekth", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fast-uri@1.0.0", "maintainers": [{"name": "zekth", "email": "<EMAIL>"}], "homepage": "https://github.com/fastify/fast-uri", "bugs": {"url": "https://github.com/fastify/fast-uri/issues"}, "tap": {"check-coverage": false}, "dist": {"shasum": "aeb77c699b79d6277e337e4b3cc3907f18ef441e", "tarball": "https://registry.npmjs.org/fast-uri/-/fast-uri-1.0.0.tgz", "fileCount": 22, "integrity": "sha512-uAHuqh4OjEeKj2VEZ+f4lv9nu2dae2qV0pbOI2S1ZiIaQImlfDf5GfxDLc+f0v2VgIiTwvdMfssGw5vblK6RTQ==", "signatures": [{"sig": "MEQCIEP3UXUqiKX7XiY30YtlQ67H59WosYi8MVgyoKmvvr8sAiBXNz10C1tzPsPKBKeLvBZAU73BYAF2Egimg+RxIBzYpw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66058, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6upTCRA9TVsSAnZWagAApd0P/3A3MoE6txGTwR3axSWY\nek4QmPnIwieVL41yaEpDjGdPiJ6/FceStl5lmR5QBv3HI2j4MwCXTGVCVU3g\n40yzQZ677zoLeKFKNzoBjds1pbLpoK2TiT1saBqjjmd6kMbop/3YDgCmjsu9\ntCppyTtmq0Ac1ngEHr9c7injrpYppZBwZJGlUXlJo96Sb1fMrIpRsjkp1Q8w\nqrpkr7c8YOuBKkSPI1X+PQMlQTxqgHF0tEQehsR8rjPDLKzuBq3k9opPFxGZ\ny3FNM9JAOdEqGvXwKlBfbvQXxa2NQ4zE7bOp2Dv3NPEgM/GoUpjiXujQIvee\nV/GoQkVwFKHhbGrf1zvQPQEZ3kFIxd1GEVxHiconY7thePED2LryfHIPkIQK\nO4UUbLLBdWh+fGtC42CJm5EkhOeLX9tE2Jj4w30vJUSwigByeHjoggJVzwLN\nqJRXN42eQ3VUIfbU2YDDYn9y/UipqdHJUrN5jvE3X72q267jW6gjq36RtPio\nz7JR1cZnelp6BLWkQhtgLBMDm0qQnGUvre7prczJGOx8vcx30ik3iIpMNafD\nyIxfcZqUQvkhnK7qmhFBq6kqx7TaeJ2N4zdAwp8PLqxc/buxRYvBWrD4OXUq\n0nffLya1Ch3rgNlxX3ER0KTpOQwDyuj79jL2mEglFWJVp2crbGAfCAQaFwQN\n/EnQ\r\n=3ChO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "type": "commonjs", "types": "index.d.ts", "gitHead": "c1913a5c9aa54c735b0cc08badf1d002461ebb47", "scripts": {"test": "npm run test:lint && npm run test:unit && npm run test:typescript", "bench": "node benchmark.js", "test:ci": "npm run test:lint && npm run test:unit -- --cov --coverage-report=lcovonly && npm run test:typescript", "lint:fix": "standard --fix", "test:lint": "standard | snazzy", "test:unit": "tap -J test/*.test.js", "test:unit:dev": "tap -J test/*.test.js --coverage-report=html", "test:typescript": "tsd"}, "_npmUser": {"name": "zekth", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/fastify/fast-uri.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "Dependency free RFC 3986 URI toolbox", "directories": {}, "_nodeVersion": "16.12.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.0.10", "tsd": "^0.19.0", "snazzy": "^9.0.0", "uri-js": "^4.4.1", "standard": "^16.0.4", "benchmark": "^2.1.4", "coveralls": "^3.1.1", "typescript": "^4.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/fast-uri_1.0.0_1642785362918_0.483342223970711", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "fast-uri", "version": "1.0.1", "author": {"url": "https://github.com/zekth", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fast-uri@1.0.1", "maintainers": [{"name": "zekth", "email": "<EMAIL>"}], "homepage": "https://github.com/fastify/fast-uri", "bugs": {"url": "https://github.com/fastify/fast-uri/issues"}, "tap": {"check-coverage": false}, "dist": {"shasum": "dd637f093bccf17ebea58a70c178ee8a70b5aa45", "tarball": "https://registry.npmjs.org/fast-uri/-/fast-uri-1.0.1.tgz", "fileCount": 22, "integrity": "sha512-dbO/+ny6lX4tt7pvfPMTiHfQVR5igYKFa5BJ2a21TWuOgd2ySp5DYswsEGuMcJZLL3/eJ/MQJ5KNcXyNUvDt8w==", "signatures": [{"sig": "MEUCIQC2GmQgiwVXyPK8lXSWxe5xPT7/aK0HOW4m13DB5NYvJwIgXfKtH3VxrzoVJa0k0zr7KSGV0v2jYJTFRHx0Cw3h2t0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65978, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh/m5OCRA9TVsSAnZWagAAnEsP/j+dHd1FnydKVdYexzmD\ngTFhp2NMtGmsF1UTf8R2fh+lrpICHCiui1jxSq/zzQp50b0CFgdyuICiXSfy\nSX/hQi+LQJPfoOOV/T/qLkEHFDdwQMzxHieBEiMyo3Hlcn6qukinlOnNA86m\nV1K18WFza3GRlsJ9WwzxtpWr7x4YSqpbYf4DY+9UZe4d929wEl7zmo0kuL/m\nLJVts7NpYrKk8OnNGXCNhcjRK9XkSHLEQqvP51nvWuR9wiXacFv1qEv66Bsv\nnBgI4/ek+FpkGVllXYAhif1YNTAG3DRdZ7zRXI7hKRS6pq2LWpNk5tj8RyE5\nB/sPFi49jRAoj5LsrAPbOZxmwqtu6DnYQN5Yz7jDE69AEWhxFlG4G1OJx9iw\n0WUOi1cUJxlad4QFyjfdHjQEe15Qq2UBJuQ51jAgQzdx1cUKO7Rf9dyeTPA9\nIiJrmuNLzuZqJt4uTCmddqEAtFp2y24StErrNVK92wKBpH7kFCy6Eq77odf6\nRG+iDjJKmFw+iIWJT6MzdjpK1SJyqjjsJ36jk0e+zZobBbiUBuFR3GGg2b69\nll1jPHR5G0YxPUmNOZZHaghE9l/Eig5hFYKv/aN8mdMBxyoHMdmBn1P+PBZe\n0KHs/RmQZG1mLmkwlBf31wQjis3g4yhRPdxxHAbc0ocabMv6vOaE7V+52m6S\nmvhU\r\n=0eVb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "type": "commonjs", "types": "index.d.ts", "gitHead": "171af3fd5889f38edbf43eb364f1fc180bb35313", "scripts": {"test": "npm run test:lint && npm run test:unit && npm run test:typescript", "bench": "node benchmark.js", "test:ci": "npm run test:lint && npm run test:unit -- --cov --coverage-report=lcovonly && npm run test:typescript", "lint:fix": "standard --fix", "test:lint": "standard | snazzy", "test:unit": "tap -J test/*.test.js", "test:unit:dev": "tap -J test/*.test.js --coverage-report=html", "test:typescript": "tsd"}, "_npmUser": {"name": "zekth", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/fastify/fast-uri.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "Dependency free RFC 3986 URI toolbox", "directories": {}, "_nodeVersion": "16.12.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.0.10", "tsd": "^0.19.0", "snazzy": "^9.0.0", "uri-js": "^4.4.1", "standard": "^16.0.4", "benchmark": "^2.1.4", "coveralls": "^3.1.1", "typescript": "^4.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/fast-uri_1.0.1_1644064334581_0.6615352020760208", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "fast-uri", "version": "2.0.0", "author": {"url": "https://github.com/zekth", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fast-uri@2.0.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "zekth", "email": "<EMAIL>"}], "homepage": "https://github.com/fastify/fast-uri", "bugs": {"url": "https://github.com/fastify/fast-uri/issues"}, "tap": {"check-coverage": false}, "dist": {"shasum": "a0c1fcbfe769cb023f9d9808798103847de66036", "tarball": "https://registry.npmjs.org/fast-uri/-/fast-uri-2.0.0.tgz", "fileCount": 22, "integrity": "sha512-Mt/lDpFwcPcLv3G0Qd3WlIjP5AeCQo/X91TZ9G8Px/3GzkTADX5smwginXYz+iX8mSa5pBN98EObj05X39/JoA==", "signatures": [{"sig": "MEUCIQCBZ+EVd5LT0BCzalgfFuqrQBHJWkccYf5voCkuJIVt/wIgNiEMAKMl5JhILxlAtTYEjzCKQ+Es0ou10RexPfA/Lik=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 63766, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijgllACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp1jg/+OoIs7yD3ot/UFisU8lPYhrd9aGnifBtOkWDTDaOWu0JyQWZL\r\ni3Jb+fvimKGHZOk/Y+hcKPt2DPjdF0Rzhk0Wj9HGOFW3N9sa/QKlbTxF2XSI\r\ngl9+tGbwycdgVeFhx6nNFdZxAFJj57fPTCXuig9k9OcytqXD6jyX3a2n54pX\r\nyM1PmzcrT36sbmaXviqtiSVpNiIFTp1jc+mDCuxc9f80+ajfDTY3AFImXtO5\r\n2LiU0LWy5DUf7k6gH0j9yVe7rHsYe0alEmuWRWvmWKo32jm7hRnsXdZ4HTyy\r\npDMv+HbKOnCuWX8EDuHHC/TqcXg6L95cDnSG9EwNYpi6Yxr50n1X6ekDwEUk\r\nJ+zXzrBp9kxepSeqektEuwyOUod1y3Vd1E/HLjPk7YaE0hIAn7RzLwjjJ0Mg\r\nGwV9ovrV7xqwYdhLH6J2Xj6nE28YB3V8R+0SQB3SDSp8fBROD1vdIwtQSGz9\r\n6u0zWQwoQ7u7kcEIhTuHa3rcSxlTW1oqEPUaBZkUPumn7tnCCc99FOsMDKYT\r\nalzLjA44Ox+NoMRXpW0rJorD6w8bY8Ne/B7aFxxPvDTrd8jC86m0mPVTEaHz\r\n5eHvpobpL76ZaZVn5ubc4XcfBFLC40qe1nXDACQookDT+BnK505jzKgx8ufp\r\nMuRuADr7X+BsTiMGzRdIVeUDn+KU/gYyj60=\r\n=i449\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "type": "commonjs", "types": "index.d.ts", "gitHead": "e1387f63fd835872d4659f1ef81e4d8d546e04a5", "scripts": {"test": "npm run test:lint && npm run test:unit && npm run test:typescript", "bench": "node benchmark.js", "test:ci": "npm run test:lint && npm run test:unit -- --cov --coverage-report=lcovonly && npm run test:typescript", "lint:fix": "standard --fix", "test:lint": "standard | snazzy", "test:unit": "tap -J test/*.test.js", "test:unit:dev": "tap -J test/*.test.js --coverage-report=html", "test:typescript": "tsd"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/fastify/fast-uri.git", "type": "git"}, "_npmVersion": "8.5.5", "description": "Dependency free RFC 3986 URI toolbox", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.0", "tsd": "^0.20.0", "snazzy": "^9.0.0", "uri-js": "^4.4.1", "standard": "^17.0.0", "benchmark": "^2.1.4", "coveralls": "^3.1.1", "typescript": "^4.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/fast-uri_2.0.0_1653475685554_0.20139806336268928", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "fast-uri", "version": "2.1.0", "author": {"url": "https://github.com/zekth", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fast-uri@2.1.0", "maintainers": [{"name": "jsumners", "email": "<EMAIL>"}, {"name": "del<PERSON>or", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "zekth", "email": "<EMAIL>"}], "homepage": "https://github.com/fastify/fast-uri", "bugs": {"url": "https://github.com/fastify/fast-uri/issues"}, "tap": {"check-coverage": false}, "dist": {"shasum": "9279432d6b53675c90116b947ed2bbba582d6fb5", "tarball": "https://registry.npmjs.org/fast-uri/-/fast-uri-2.1.0.tgz", "fileCount": 22, "integrity": "sha512-qKRta6N7BWEFVlyonVY/V+BMLgFqktCUV0QjT259ekAIlbVrMaFnFLxJ4s/JPl4tou56S1BzPufI60bLe29fHA==", "signatures": [{"sig": "MEYCIQCiwZ9Sw4Nfc3PnH1NwgKCypdQIsuOqmgde0JuawWZV0AIhAPKWT+Vj3wMXzv543hsDE9VwU/v9yUrfQKhGkJhUeL82", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 63902, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio2qVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqznA/9Fmv/IoSnRQrmKhF7gNH8sU8F+DZc8n9L9dkgD97CX5L0d7v/\r\n5u38n7zj7WGWEEEISdycGtM5yIXzASBreHvmUYSUfNPmQVX5/Ea3bRkTYU/o\r\nN6RRdhW1kzDdvb1rkR1y3VFvjBCObG9ewjOGBV6GndR1VEDOww8xDS7ZOIXe\r\nMPycs2LtzUiJcND9SXeFlP6COaFY6kePg4vjfXTKFdt3S3Z4dDVilk5Haw0n\r\n38TLQZwwZklddr/KhcK+TIK+O9ic6B6nG3S+Q2Ju9myFaB3CO50HWx1jJNKc\r\nE7NUd71IaYN8KpO+ewH99PFrlWmM0y/X2z31JtmaMsHmm4DehpyPwSJgjs8F\r\nMX9jS4XulZkYRCK1405du89Qfs3vVImGhgqLGVCbD/Zi8908iJ5kA0KSk9a3\r\nHO9b9Mip26sR28OSFzqir8Eu/XLCS4gPc8nhfXRSbLK29N3zKR/1xWMsYFxm\r\n3Koi5DVsKoRniBmDtG8SyjqzG1mzlPlRo/O0NwTYDLlgxV3be1mLvRsG4nQY\r\n+r6xV6fywaM4pqNo2yY8Nni8F7Rbmyc4rTaKwcHW5O+GakuaJqDo31ES5Ebd\r\nEGg0RDegvld+1MbkTub2BdjvZgRZ8SIKKeHpI0yUhI9gRChKAnh2fRrwkzPY\r\nYDjRCx+eowX+l3TDH5MJNyJsayzdhhY9A6I=\r\n=kLMt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "type": "commonjs", "types": "index.d.ts", "gitHead": "b4bfd4ea22cd9c9b7f0dd2be1b71b31f292e69ce", "scripts": {"test": "npm run test:lint && npm run test:unit && npm run test:typescript", "bench": "node benchmark.js", "test:ci": "npm run test:lint && npm run test:unit -- --cov --coverage-report=lcovonly && npm run test:typescript", "lint:fix": "standard --fix", "test:lint": "standard | snazzy", "test:unit": "tap -J test/*.test.js", "test:unit:dev": "tap -J test/*.test.js --coverage-report=html", "test:typescript": "tsd"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/fastify/fast-uri.git", "type": "git"}, "_npmVersion": "8.12.1", "description": "Dependency free RFC 3986 URI toolbox", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.0", "tsd": "^0.20.0", "snazzy": "^9.0.0", "uri-js": "^4.4.1", "standard": "^17.0.0", "benchmark": "^2.1.4", "coveralls": "^3.1.1", "typescript": "^4.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/fast-uri_2.1.0_1654876821706_0.4342377897212166", "host": "s3://npm-registry-packages"}}, "2.2.0": {"name": "fast-uri", "version": "2.2.0", "author": {"url": "https://github.com/zekth", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fast-uri@2.2.0", "maintainers": [{"name": "jsumners", "email": "<EMAIL>"}, {"name": "del<PERSON>or", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "simon<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "eomm", "email": "<EMAIL>"}, {"name": "zekth", "email": "<EMAIL>"}, {"name": "rafael<PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/fastify/fast-uri", "bugs": {"url": "https://github.com/fastify/fast-uri/issues"}, "tap": {"check-coverage": false}, "dist": {"shasum": "519a0f849bef714aad10e9753d69d8f758f7445a", "tarball": "https://registry.npmjs.org/fast-uri/-/fast-uri-2.2.0.tgz", "fileCount": 21, "integrity": "sha512-cIusKBIt/R/oI6z/1nyfe2FvGKVTohVRfvkOhvx0nCEW+xf5NoCXjAHcWp93uOUBchzYcsvPlrapAdX1uW+YGg==", "signatures": [{"sig": "MEYCIQD7I5D4lx+4ng7XoQHji0F8OLPSJcqNfZp+7eDaSzd7ugIhAMNZWQkhKEVwlFhNoy4zC35GtWUzHFHixthk20yOTvmU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64872, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjjbmDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmolChAAoJpxckv8mcJiFw5NyAzfJ4gz39lgmMMtsW5IT7hcoTK+xlCl\r\ndDul68JTTa/VS4CMstkcwUgf0oG1E6TZJHWrS9PJ5Sq9TFlevkTxv3R1fPxh\r\nCA/eKeaY4VajnGDET058TkJoePYDWF/qKjGrt6WTzj0z7dreGYmoLruoA5Oq\r\nA5F/A0MpbSq6fv5J57Xph84wTIG4ZqWOrgrAsKIhgFaHHQKRA05QeBoMfflI\r\nucEQ94TLr9WozenJrahTu+spzcLNZ3zJzJcoRsX8ZYsXswZ+EVGBqO5l9jha\r\nWHzdoz2z/ELjkNve02veQ5zrXbsUJMMSZ1tP1WM1+HjhOkTMZqJYdhxedtMw\r\n7WYm3YUonRHelQWgn0ktWOBgZsRtwGf8XAbXt33OM7m9hJANDAhLI9aeRtni\r\nm+4qGt0tTqA9Yob8w+i8zPUDs+3NDCPxK4+FCY4S77uqLcrmklWCK+7DHs7A\r\nWBUVquNgYMsUIdvy4ernNWkA5aat0mdWmiYwUUmVUgb+Ux5Tgo5ilfne4dD3\r\nZdEt1W7B26rPkKAFRsF46y9gpU6nDMRyqo9WSNlc7JfjKM3t2QCB+r3bIGh/\r\nmWAGNdIvZKSDNEMTqkLtk1+PCIH5DGO1fNkQlWBDIny35MO/SDqwC1bgSveG\r\nEW19PXIAYWr7wP1Yqxj8vioaO7I/dWHxfpg=\r\n=8xTf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "type": "commonjs", "types": "types/index.d.ts", "gitHead": "2394025b8ab51f6962204b2cd38e2909b5e1bbe3", "scripts": {"test": "npm run test:lint && npm run test:unit && npm run test:typescript", "bench": "node benchmark.js", "test:ci": "npm run test:lint && npm run test:unit -- --cov --coverage-report=lcovonly && npm run test:typescript", "lint:fix": "standard --fix", "test:lint": "standard | snazzy", "test:unit": "tap -J test/*.test.js", "test:unit:dev": "tap -J test/*.test.js --coverage-report=html", "test:typescript": "tsd"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/fastify/fast-uri.git", "type": "git"}, "_npmVersion": "9.1.2", "description": "Dependency free RFC 3986 URI toolbox", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.0", "tsd": "^0.24.1", "snazzy": "^9.0.0", "uri-js": "^4.4.1", "standard": "^17.0.0", "benchmark": "^2.1.4", "coveralls": "^3.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/fast-uri_2.2.0_1670232451592_0.8528374786126331", "host": "s3://npm-registry-packages"}}, "2.3.0": {"name": "fast-uri", "version": "2.3.0", "author": {"url": "https://github.com/zekth", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fast-uri@2.3.0", "maintainers": [{"name": "eomm", "email": "<EMAIL>"}, {"name": "jsumners", "email": "<EMAIL>"}, {"name": "del<PERSON>or", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "gurgunday", "email": "<EMAIL>"}, {"name": "simon<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "zekth", "email": "<EMAIL>"}, {"name": "rafael<PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/fastify/fast-uri", "bugs": {"url": "https://github.com/fastify/fast-uri/issues"}, "tap": {"check-coverage": false}, "dist": {"shasum": "bdae493942483d299e7285dcb4627767d42e2793", "tarball": "https://registry.npmjs.org/fast-uri/-/fast-uri-2.3.0.tgz", "fileCount": 22, "integrity": "sha512-eel5UKGn369gGEWOqBShmFJWfq/xSJvsgDzgLYC845GneayWvXBf0lJCBn5qTABfewy1ZDPoaR5OZCP+kssfuw==", "signatures": [{"sig": "MEUCIE0Z1NpMnzJRcDXMbg9ETRkaEpbViuXj8leQNvdV5I5FAiEA50THSXxysL4xVOvlXwNkAFOCYMAjyAoyXBTaxHTYGww=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64395}, "main": "index.js", "type": "commonjs", "types": "types/index.d.ts", "gitHead": "22715af7358f8928d84c0ab67f2ac59da84e991e", "scripts": {"test": "npm run test:lint && npm run test:unit && npm run test:typescript", "bench": "node benchmark.js", "test:ci": "npm run test:lint && npm run test:unit -- --cov --coverage-report=lcovonly && npm run test:typescript", "lint:fix": "standard --fix", "test:lint": "standard | snazzy", "test:unit": "tap -J test/*.test.js", "test:unit:dev": "tap -J test/*.test.js --coverage-report=html", "test:typescript": "tsd"}, "_npmUser": {"name": "eomm", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/fastify/fast-uri.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "Dependency free RFC 3986 URI toolbox", "directories": {}, "_nodeVersion": "20.8.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.0", "tsd": "^0.29.0", "snazzy": "^9.0.0", "uri-js": "^4.4.1", "standard": "^17.0.0", "benchmark": "^2.1.4", "coveralls": "^3.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/fast-uri_2.3.0_1698694379507_0.8124008727102578", "host": "s3://npm-registry-packages"}}, "2.3.1": {"name": "fast-uri", "version": "2.3.1", "author": {"url": "https://github.com/zekth", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fast-uri@2.3.1", "maintainers": [{"name": "eomm", "email": "<EMAIL>"}, {"name": "jsumners", "email": "<EMAIL>"}, {"name": "del<PERSON>or", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "climba03003", "email": "<EMAIL>"}, {"name": "gurgunday", "email": "<EMAIL>"}, {"name": "simon<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "zekth", "email": "<EMAIL>"}, {"name": "rafael<PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/fastify/fast-uri", "bugs": {"url": "https://github.com/fastify/fast-uri/issues"}, "dist": {"shasum": "14af6294f8d5b7b10703351ca63590686251a6bf", "tarball": "https://registry.npmjs.org/fast-uri/-/fast-uri-2.3.1.tgz", "fileCount": 23, "integrity": "sha512-iC7SLdMJx2KGdBkhJ6UulbNfpeIGTMS3/OIJpPa1JkZu9DKVQmPtBBme9Esoa4XP2eLGaHBv4vzRqlolXKo9cg==", "signatures": [{"sig": "MEQCIDw9hrCHHvLDLGcGQTf4UFaXX0no14cKYe8qONIxbjEcAiAt66+tTrk+/C/dFEFRnAuRH3yjoUrZDM5u5LfOLoRTyQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64327}, "main": "index.js", "type": "commonjs", "types": "types/index.d.ts", "gitHead": "7ffca1e67b7d6d34cfbc3f91a1adc9a8c07728d5", "scripts": {"test": "npm run test:lint && npm run test:unit && npm run test:typescript", "bench": "node benchmark.js", "test:ci": "npm run test:lint && npm run test:unit -- --cov --coverage-report=lcovonly && npm run test:typescript", "lint:fix": "standard --fix", "test:lint": "standard | snazzy", "test:unit": "tap -J test/*.test.js", "test:unit:dev": "tap -J test/*.test.js --coverage-report=html", "test:typescript": "tsd"}, "_npmUser": {"name": "gurgunday", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/fastify/fast-uri.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Dependency free RFC 3986 URI toolbox", "directories": {}, "_nodeVersion": "20.14.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.0", "tsd": "^0.31.0", "snazzy": "^9.0.0", "uri-js": "^4.4.1", "standard": "^17.0.0", "benchmark": "^2.1.4", "coveralls": "^3.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/fast-uri_2.3.1_1717575233382_0.21714633452316878", "host": "s3://npm-registry-packages"}}, "2.4.0": {"name": "fast-uri", "version": "2.4.0", "author": {"url": "https://github.com/zekth", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fast-uri@2.4.0", "maintainers": [{"name": "eomm", "email": "<EMAIL>"}, {"name": "jsumners", "email": "<EMAIL>"}, {"name": "del<PERSON>or", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "climba03003", "email": "<EMAIL>"}, {"name": "gurgunday", "email": "<EMAIL>"}, {"name": "simon<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "zekth", "email": "<EMAIL>"}, {"name": "rafael<PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/fastify/fast-uri", "bugs": {"url": "https://github.com/fastify/fast-uri/issues"}, "dist": {"shasum": "67eae6fbbe9f25339d5d3f4c4234787b65d7d55e", "tarball": "https://registry.npmjs.org/fast-uri/-/fast-uri-2.4.0.tgz", "fileCount": 23, "integrity": "sha512-ypuAmmMKInk5q7XcepxlnUWDLWv4GFtaJqAzWKqn62IpQ3pejtr5dTVbt3vwqVaMKmkNR55sTT+CqUKIaT21BA==", "signatures": [{"sig": "MEUCIQCfJuTv2Elb2h4e8bmTZu/z4+DxeOlJypJ0iL5QX8ignAIgUh9g0KKZ0Ia5x9ZznbYDE0AhG9xXVmfNsVuw8ZUNW18=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64382}, "main": "index.js", "type": "commonjs", "types": "types/index.d.ts", "gitHead": "856e241efa1e042eced4938e7e59883729178174", "scripts": {"test": "npm run test:lint && npm run test:unit && npm run test:typescript", "bench": "node benchmark.js", "test:ci": "npm run test:lint && npm run test:unit -- --cov --coverage-report=lcovonly && npm run test:typescript", "lint:fix": "standard --fix", "test:lint": "standard | snazzy", "test:unit": "tap -J test/*.test.js", "test:unit:dev": "tap -J test/*.test.js --coverage-report=html", "test:typescript": "tsd"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/fastify/fast-uri.git", "type": "git"}, "_npmVersion": "10.5.2", "description": "Dependency free RFC 3986 URI toolbox", "directories": {}, "_nodeVersion": "20.13.1", "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.0", "tsd": "^0.31.0", "snazzy": "^9.0.0", "uri-js": "^4.4.1", "standard": "^17.0.0", "benchmark": "^2.1.4", "coveralls": "^3.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/fast-uri_2.4.0_1718022955950_0.1676096785197092", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "fast-uri", "version": "3.0.0", "author": {"url": "https://github.com/zekth", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fast-uri@3.0.0", "homepage": "https://github.com/fastify/fast-uri", "bugs": {"url": "https://github.com/fastify/fast-uri/issues"}, "dist": {"shasum": "f5ba3f3b26a06d54ebccde911b432391c63be251", "tarball": "https://registry.npmjs.org/fast-uri/-/fast-uri-3.0.0.tgz", "fileCount": 25, "integrity": "sha512-zv72gAcgHJjkq7h2IHzRB1Hg+9QIy9+IHmy2wBlmZnG1T7CfdfSjLXotI0vhq4T4Fqg31zkg+Dp2Dj8iFM6ULw==", "signatures": [{"sig": "MEYCIQCdoNWWl8rPkniFa8fUsRx3tL2G0IsrVd0azcJhxrG+wAIhALyct/5LuFSTAtgb3rof5cKL7L5TnDCQpUuUxXc7vWsF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 110398}, "main": "index.js", "type": "commonjs", "types": "types/index.d.ts", "gitHead": "5964558737eab37fffed66987be5b6a4d550f528", "scripts": {"lint": "standard | snazzy", "test": "npm run lint && npm run test:unit && npm run test:typescript", "bench": "node benchmark.js", "test:ci": "npm run lint && npm run test:unit -- --coverage-report=lcovonly && npm run test:typescript", "lint:fix": "standard --fix", "test:unit": "tap", "test:unit:dev": "npm run test:unit -- --coverage-report=html", "test:typescript": "tsd"}, "_npmUser": {"name": "jsumners", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/fastify/fast-uri.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Dependency free RFC 3986 URI toolbox", "directories": {}, "_nodeVersion": "20.14.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^18.7.2", "tsd": "^0.31.0", "snazzy": "^9.0.0", "uri-js": "^4.4.1", "standard": "^17.1.0", "benchmark": "^2.1.4", "coveralls": "^3.1.1", "@fastify/pre-commit": "^2.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fast-uri_3.0.0_1719158363367_0.6255606820749082", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "fast-uri", "version": "3.0.1", "author": {"url": "https://github.com/zekth", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fast-uri@3.0.1", "homepage": "https://github.com/fastify/fast-uri", "bugs": {"url": "https://github.com/fastify/fast-uri/issues"}, "dist": {"shasum": "cddd2eecfc83a71c1be2cc2ef2061331be8a7134", "tarball": "https://registry.npmjs.org/fast-uri/-/fast-uri-3.0.1.tgz", "fileCount": 26, "integrity": "sha512-MWipKbbYiYI0UC7cl8m/i/IWTqfC8YXsqjzybjddLsFjStroQzsHXkc73JutMvBiXmOvapk+axIl79ig5t55Bw==", "signatures": [{"sig": "MEQCIBOyEr5e7zsbHtnYXO4uBzgXaozwrsWICLMUYZ+rHM5BAiAHB02RDF9VbEQA6m1T/oATTkaogmkfh0tSoctbVpstfw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 111169}, "main": "index.js", "type": "commonjs", "types": "types/index.d.ts", "gitHead": "986abce97b2303c69011da82805285a59e5967de", "scripts": {"lint": "standard | snazzy", "test": "npm run lint && npm run test:unit && npm run test:typescript", "bench": "node benchmark.js", "test:ci": "npm run lint && npm run test:unit -- --coverage-report=lcovonly && npm run test:typescript", "lint:fix": "standard --fix", "test:unit": "tap", "test:unit:dev": "npm run test:unit -- --coverage-report=html", "test:typescript": "tsd"}, "_npmUser": {"name": "gurgunday", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/fastify/fast-uri.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Dependency free RFC 3986 URI toolbox", "directories": {}, "_nodeVersion": "20.14.0", "_hasShrinkwrap": false, "devDependencies": {"ajv": "^8.16.0", "tap": "^18.7.2", "tsd": "^0.31.0", "snazzy": "^9.0.0", "uri-js": "^4.4.1", "standard": "^17.1.0", "benchmark": "^2.1.4", "coveralls": "^3.1.1", "@fastify/pre-commit": "^2.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fast-uri_3.0.1_1719473881540_0.08969142661754814", "host": "s3://npm-registry-packages"}}, "3.0.2": {"name": "fast-uri", "version": "3.0.2", "author": {"url": "https://github.com/zekth", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fast-uri@3.0.2", "maintainers": [{"name": "eomm", "email": "<EMAIL>"}, {"name": "jsumners", "email": "<EMAIL>"}, {"name": "del<PERSON>or", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "climba03003", "email": "<EMAIL>"}, {"name": "gurgunday", "email": "<EMAIL>"}, {"name": "simon<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "zekth", "email": "<EMAIL>"}, {"name": "rafael<PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/fastify/fast-uri", "bugs": {"url": "https://github.com/fastify/fast-uri/issues"}, "dist": {"shasum": "d78b298cf70fd3b752fd951175a3da6a7b48f024", "tarball": "https://registry.npmjs.org/fast-uri/-/fast-uri-3.0.2.tgz", "fileCount": 25, "integrity": "sha512-GR6f0hD7XXyNJa25Tb9BuIdN0tdr+0BMi6/CJPH3wJO1JjNG3n/VsSw38AwRdKZABm8lGbPfakLRkYzx2V9row==", "signatures": [{"sig": "MEUCIQCBkEMlIhNt5F6TOYXk4ioiNAe2Q+SjKc+D8Z/w4yE/7AIgM/+FvZzHcQ9/xLbM793/efVF2jiEtfziWX2Z6OpbMyI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 107789}, "main": "index.js", "type": "commonjs", "types": "types/index.d.ts", "gitHead": "a81bfb8ca4298d0b857bbab48f15c2abc6a8232f", "scripts": {"lint": "standard | snazzy", "test": "npm run lint && npm run test:unit && npm run test:typescript", "bench": "node benchmark.js", "test:ci": "npm run lint && npm run test:unit -- --coverage-report=lcovonly && npm run test:typescript", "lint:fix": "standard --fix", "test:unit": "npx tape test/**/*.js", "test:unit:dev": "npm run test:unit -- --coverage-report=html", "test:typescript": "tsd"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/fastify/fast-uri.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Dependency free RFC 3986 URI toolbox", "directories": {}, "_nodeVersion": "20.17.0", "_hasShrinkwrap": false, "devDependencies": {"ajv": "^8.16.0", "tsd": "^0.31.0", "tape": "^5.8.1", "snazzy": "^9.0.0", "uri-js": "^4.4.1", "standard": "^17.1.0", "benchmark": "^2.1.4", "coveralls": "^3.1.1", "@fastify/pre-commit": "^2.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fast-uri_3.0.2_1727369294948_0.77161391620773", "host": "s3://npm-registry-packages"}}, "3.0.3": {"name": "fast-uri", "version": "3.0.3", "author": {"url": "https://github.com/zekth", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "fast-uri@3.0.3", "maintainers": [{"name": "eomm", "email": "<EMAIL>"}, {"name": "jsumners", "email": "<EMAIL>"}, {"name": "del<PERSON>or", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "climba03003", "email": "<EMAIL>"}, {"name": "gurgunday", "email": "<EMAIL>"}, {"name": "simon<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "zekth", "email": "<EMAIL>"}, {"name": "rafael<PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/fastify/fast-uri", "bugs": {"url": "https://github.com/fastify/fast-uri/issues"}, "dist": {"shasum": "892a1c91802d5d7860de728f18608a0573142241", "tarball": "https://registry.npmjs.org/fast-uri/-/fast-uri-3.0.3.tgz", "fileCount": 25, "integrity": "sha512-aLrHthzCjH5He4Z2H9YZ+v6Ujb9ocRuW6ZzkJQOrTxleEijANq4v1TsaPaVG1PZcuurEzrLcWRyYBYXD5cEiaw==", "signatures": [{"sig": "MEUCIH8+CKmNr+yvZKf7xDUIbmfnNYtP2abWs9FqPuhb06FNAiEAr9N3VC6YaN+l8J2kPB/ftSDEAMlQ8TJKl3QVjSTE1ug=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 107798}, "main": "index.js", "type": "commonjs", "types": "types/index.d.ts", "gitHead": "a79e2061bc3d3e8e6d06ee091be02d66d2a7f3e2", "scripts": {"lint": "standard | snazzy", "test": "npm run lint && npm run test:unit && npm run test:typescript", "bench": "node benchmark.js", "test:ci": "npm run lint && npm run test:unit -- --coverage-report=lcovonly && npm run test:typescript", "lint:fix": "standard --fix", "test:unit": "npx tape test/**/*.js", "test:unit:dev": "npm run test:unit -- --coverage-report=html", "test:typescript": "tsd"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/fastify/fast-uri.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Dependency free RFC 3986 URI toolbox", "directories": {}, "_nodeVersion": "20.18.0", "_hasShrinkwrap": false, "devDependencies": {"ajv": "^8.16.0", "tsd": "^0.31.0", "tape": "^5.8.1", "snazzy": "^9.0.0", "uri-js": "^4.4.1", "standard": "^17.1.0", "benchmark": "^2.1.4", "coveralls": "^3.1.1", "@fastify/pre-commit": "^2.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fast-uri_3.0.3_1729012630345_0.834769657916993", "host": "s3://npm-registry-packages"}}, "3.0.4": {"name": "fast-uri", "version": "3.0.4", "author": {"url": "https://github.com/zekth", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "fast-uri@3.0.4", "maintainers": [{"name": "jsumners", "email": "<EMAIL>"}, {"name": "del<PERSON>or", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "eomm", "email": "<EMAIL>"}, {"name": "climba03003", "email": "<EMAIL>"}, {"name": "zekth", "email": "<EMAIL>"}, {"name": "gurgunday", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simon<PERSON>", "email": "<EMAIL>"}, {"name": "fdawgs", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://heyhey.to/G", "name": "G<PERSON><PERSON><PERSON>n <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/fdawgs", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/fastify/fast-uri", "bugs": {"url": "https://github.com/fastify/fast-uri/issues"}, "dist": {"shasum": "bf2973f18465da231ef4b1e43a188c3bf580cf98", "tarball": "https://registry.npmjs.org/fast-uri/-/fast-uri-3.0.4.tgz", "fileCount": 26, "integrity": "sha512-G3iTQw1DizJQ5eEqj1CbFCWhq+pzum7qepkxU7rS1FGZDqjYKcrguo9XDRbV7EgPnn8CgaPigTq+NEjyioeYZQ==", "signatures": [{"sig": "MEUCIFAi6jQT2z2fe4YBrnHTLXH84uttR1e8ZxlDP9VuSANMAiEAmlmKXPljKM83Mu3QYJRhXPW2TgLgxxVBRrJgrREpdB8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108451}, "main": "index.js", "type": "commonjs", "types": "types/index.d.ts", "funding": [{"url": "https://github.com/sponsors/fastify", "type": "github"}, {"url": "https://opencollective.com/fastify", "type": "opencollective"}], "gitHead": "4f6922a4903d410d4a3d26e846ac4262ebb0957b", "scripts": {"lint": "eslint", "test": "npm run test:unit && npm run test:typescript", "bench": "node benchmark.js", "lint:fix": "eslint --fix", "test:unit": "npx tape test/**/*.js", "test:unit:dev": "npm run test:unit -- --coverage-report=html", "test:typescript": "tsd"}, "_npmUser": {"name": "fdawgs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/fastify/fast-uri.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Dependency-free RFC 3986 URI toolbox", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "devDependencies": {"ajv": "^8.16.0", "tsd": "^0.31.0", "tape": "^5.8.1", "eslint": "^9.17.0", "uri-js": "^4.4.1", "benchmark": "^2.1.4", "coveralls": "^3.1.1", "neostandard": "^0.12.0", "@fastify/pre-commit": "^2.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fast-uri_3.0.4_1736089727703_0.004487502014736133", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.5": {"name": "fast-uri", "version": "3.0.5", "author": {"url": "https://github.com/zekth", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "fast-uri@3.0.5", "maintainers": [{"name": "jsumners", "email": "<EMAIL>"}, {"name": "del<PERSON>or", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "eomm", "email": "<EMAIL>"}, {"name": "climba03003", "email": "<EMAIL>"}, {"name": "zekth", "email": "<EMAIL>"}, {"name": "gurgunday", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simon<PERSON>", "email": "<EMAIL>"}, {"name": "fdawgs", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://heyhey.to/G", "name": "G<PERSON><PERSON><PERSON>n <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/fdawgs", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/fastify/fast-uri", "bugs": {"url": "https://github.com/fastify/fast-uri/issues"}, "dist": {"shasum": "19f5f9691d0dab9b85861a7bb5d98fca961da9cd", "tarball": "https://registry.npmjs.org/fast-uri/-/fast-uri-3.0.5.tgz", "fileCount": 26, "integrity": "sha512-5JnBCWpFlMo0a3ciDy/JckMzzv1U9coZrIhedq+HXxxUfDTAiS0LA8OKVao4G9BxmCVck/jtA5r3KAtRWEyD8Q==", "signatures": [{"sig": "MEYCIQDN7TLkILG+9B/KSXlIdsuzom7uJhjxy1e1TTcNwUO78wIhAM4j5sTCbu8MrUA91Sm4lUpxb+jxB+Pux9pRv+CAKiPo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 109457}, "main": "index.js", "type": "commonjs", "types": "types/index.d.ts", "funding": [{"url": "https://github.com/sponsors/fastify", "type": "github"}, {"url": "https://opencollective.com/fastify", "type": "opencollective"}], "gitHead": "a0451454d05f37fefd8a7a2ce53a0ee49e063789", "scripts": {"lint": "eslint", "test": "npm run test:unit && npm run test:typescript", "bench": "node benchmark.js", "lint:fix": "eslint --fix", "test:unit": "tape test/**/*.js", "test:unit:dev": "npm run test:unit -- --coverage-report=html", "test:typescript": "tsd"}, "_npmUser": {"name": "fdawgs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/fastify/fast-uri.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Dependency-free RFC 3986 URI toolbox", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "devDependencies": {"ajv": "^8.16.0", "tsd": "^0.31.0", "tape": "^5.8.1", "eslint": "^9.17.0", "uri-js": "^4.4.1", "benchmark": "^2.1.4", "coveralls": "^3.1.1", "neostandard": "^0.12.0", "@fastify/pre-commit": "^2.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fast-uri_3.0.5_1736158170780_0.7611152611916181", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.6": {"name": "fast-uri", "description": "Dependency-free RFC 3986 URI toolbox", "version": "3.0.6", "main": "index.js", "type": "commonjs", "types": "types/index.d.ts", "license": "BSD-3-<PERSON><PERSON>", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/zekth"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "G<PERSON><PERSON><PERSON>n <PERSON>", "email": "<EMAIL>", "url": "https://heyhey.to/G"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/fdawgs"}], "repository": {"type": "git", "url": "git+https://github.com/fastify/fast-uri.git"}, "bugs": {"url": "https://github.com/fastify/fast-uri/issues"}, "homepage": "https://github.com/fastify/fast-uri", "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "scripts": {"bench": "node benchmark.js", "lint": "eslint", "lint:fix": "eslint --fix", "test": "npm run test:unit && npm run test:typescript", "test:unit": "tape test/**/*.js", "test:unit:dev": "npm run test:unit -- --coverage-report=html", "test:typescript": "tsd"}, "devDependencies": {"@fastify/pre-commit": "^2.1.0", "ajv": "^8.16.0", "benchmark": "^2.1.4", "coveralls": "^3.1.1", "eslint": "^9.17.0", "neostandard": "^0.12.0", "tape": "^5.8.1", "tsd": "^0.31.0", "uri-js": "^4.4.1"}, "_id": "fast-uri@3.0.6", "gitHead": "d40b400b0d13c3716ca5612cc61e380722f4dbd0", "_nodeVersion": "22.12.0", "_npmVersion": "10.9.0", "dist": {"integrity": "sha512-Atfo14OibSv5wAp4VWNsFYE1AchQRTv9cBGWET4pZWHzYshFSS9NQI6I57rdKn9croWVMbYFbLhJ+yJvmZIIHw==", "shasum": "88f130b77cfaea2378d56bf970dea21257a68748", "tarball": "https://registry.npmjs.org/fast-uri/-/fast-uri-3.0.6.tgz", "fileCount": 26, "unpackedSize": 109402, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDeqT0S5ftTVvf+XUGRkNorGlmiKgZ/4MR7JWKnPxiWrgIgNedrRYDKiH2vb4SzcgTvchhVoEQpcHNsHb8g5Gqet/0="}]}, "_npmUser": {"name": "gurgunday", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "jsumners", "email": "<EMAIL>"}, {"name": "del<PERSON>or", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "eomm", "email": "<EMAIL>"}, {"name": "climba03003", "email": "<EMAIL>"}, {"name": "zekth", "email": "<EMAIL>"}, {"name": "gurgunday", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simon<PERSON>", "email": "<EMAIL>"}, {"name": "fdawgs", "email": "<EMAIL>"}, {"name": "metcoder95", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/fast-uri_3.0.6_1737404574362_0.9588814401890091"}, "_hasShrinkwrap": false}}, "time": {"created": "2021-12-12T10:42:04.330Z", "modified": "2025-01-20T20:22:54.876Z", "0.0.1": "2021-12-12T10:42:04.514Z", "0.0.2": "2022-01-21T08:59:35.508Z", "1.0.0": "2022-01-21T17:16:03.121Z", "1.0.1": "2022-02-05T12:32:14.731Z", "2.0.0": "2022-05-25T10:48:05.736Z", "2.1.0": "2022-06-10T16:00:21.860Z", "2.2.0": "2022-12-05T09:27:31.809Z", "2.3.0": "2023-10-30T19:32:59.669Z", "2.3.1": "2024-06-05T08:13:53.592Z", "2.4.0": "2024-06-10T12:35:56.148Z", "3.0.0": "2024-06-23T15:59:23.534Z", "3.0.1": "2024-06-27T07:38:01.707Z", "3.0.2": "2024-09-26T16:48:15.209Z", "3.0.3": "2024-10-15T17:17:10.613Z", "3.0.4": "2025-01-05T15:08:48.037Z", "3.0.5": "2025-01-06T10:09:31.031Z", "3.0.6": "2025-01-20T20:22:54.617Z"}, "bugs": {"url": "https://github.com/fastify/fast-uri/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/zekth"}, "license": "BSD-3-<PERSON><PERSON>", "homepage": "https://github.com/fastify/fast-uri", "repository": {"type": "git", "url": "git+https://github.com/fastify/fast-uri.git"}, "description": "Dependency-free RFC 3986 URI toolbox", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "G<PERSON><PERSON><PERSON>n <PERSON>", "email": "<EMAIL>", "url": "https://heyhey.to/G"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/fdawgs"}], "maintainers": [{"name": "jsumners", "email": "<EMAIL>"}, {"name": "del<PERSON>or", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "eomm", "email": "<EMAIL>"}, {"name": "climba03003", "email": "<EMAIL>"}, {"name": "zekth", "email": "<EMAIL>"}, {"name": "gurgunday", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simon<PERSON>", "email": "<EMAIL>"}, {"name": "fdawgs", "email": "<EMAIL>"}, {"name": "metcoder95", "email": "<EMAIL>"}], "readme": "# fast-uri\n\n<div align=\"center\">\n\n[![NPM version](https://img.shields.io/npm/v/fast-uri.svg?style=flat)](https://www.npmjs.com/package/fast-uri)\n[![CI](https://github.com/fastify/fast-uri/actions/workflows/ci.yml/badge.svg?branch=main)](https://github.com/fastify/fast-uri/actions/workflows/ci.yml)\n[![neostandard javascript style](https://img.shields.io/badge/code_style-neostandard-brightgreen?style=flat)](https://github.com/neostandard/neostandard)\n\n</div>\n\nDependency-free RFC 3986 URI toolbox.\n\n## Usage\n\n## Options\n\nAll of the above functions can accept an additional options argument that is an object that can contain one or more of the following properties:\n\n*\t`scheme` (string)\n\tIndicates the scheme that the URI should be treated as, overriding the URI's normal scheme parsing behavior.\n\n*\t`reference` (string)\n\tIf set to `\"suffix\"`, it indicates that the URI is in the suffix format and the parser will use the option's `scheme` property to determine the URI's scheme.\n\n*\t`tolerant` (boolean, false)\n\tIf set to `true`, the parser will relax URI resolving rules.\n\n*\t`absolutePath` (boolean, false)\n\tIf set to `true`, the serializer will not resolve a relative `path` component.\n\n*\t`unicodeSupport` (boolean, false)\n\tIf set to `true`, the parser will unescape non-ASCII characters in the parsed output as per [RFC 3987](http://www.ietf.org/rfc/rfc3987.txt).\n\n*\t`domainHost` (boolean, false)\n\tIf set to `true`, the library will treat the `host` component as a domain name, and convert IDNs (International Domain Names) as per [RFC 5891](http://www.ietf.org/rfc/rfc5891.txt).\n\n### Parse\n\n```js\nconst uri = require('fast-uri')\nuri.parse('uri://user:<EMAIL>:123/one/two.three?q1=a1&q2=a2#body')\n// Output\n{\n  scheme: \"uri\",\n  userinfo: \"user:pass\",\n  host: \"example.com\",\n  port: 123,\n  path: \"/one/two.three\",\n  query: \"q1=a1&q2=a2\",\n  fragment: \"body\"\n}\n```\n\n### Serialize\n\n```js\nconst uri = require('fast-uri')\nuri.serialize({scheme: \"http\", host: \"example.com\", fragment: \"footer\"})\n// Output\n\"http://example.com/#footer\"\n\n```\n\n### Resolve\n\n```js\nconst uri = require('fast-uri')\nuri.resolve(\"uri://a/b/c/d?q\", \"../../g\")\n// Output\n\"uri://a/g\"\n```\n\n### Equal\n\n```js\nconst uri = require('fast-uri')\nuri.equal(\"example://a/b/c/%7Bfoo%7D\", \"eXAMPLE://a/./b/../b/%63/%7bfoo%7d\")\n// Output\ntrue\n```\n\n## Scheme supports\n\nfast-uri supports inserting custom [scheme](http://en.wikipedia.org/wiki/URI_scheme)-dependent processing rules. Currently, fast-uri has built-in support for the following schemes:\n\n*\thttp \\[[RFC 2616](http://www.ietf.org/rfc/rfc2616.txt)\\]\n*\thttps \\[[RFC 2818](http://www.ietf.org/rfc/rfc2818.txt)\\]\n*\tws \\[[RFC 6455](http://www.ietf.org/rfc/rfc6455.txt)\\]\n*\twss \\[[RFC 6455](http://www.ietf.org/rfc/rfc6455.txt)\\]\n*\turn \\[[RFC 2141](http://www.ietf.org/rfc/rfc2141.txt)\\]\n*\turn:uuid \\[[RFC 4122](http://www.ietf.org/rfc/rfc4122.txt)\\]\n\n\n## Benchmarks\n\n```\nfast-uri: parse domain x 1,306,864 ops/sec ±0.31% (100 runs sampled)\nurijs: parse domain x 483,001 ops/sec ±0.09% (99 runs sampled)\nWHATWG URL: parse domain x 862,461 ops/sec ±0.18% (97 runs sampled)\nfast-uri: parse IPv4 x 2,381,452 ops/sec ±0.26% (96 runs sampled)\nurijs: parse IPv4 x 384,705 ops/sec ±0.34% (99 runs sampled)\nWHATWG URL: parse IPv4 NOT SUPPORTED\nfast-uri: parse IPv6 x 923,519 ops/sec ±0.09% (100 runs sampled)\nurijs: parse IPv6 x 289,070 ops/sec ±0.07% (95 runs sampled)\nWHATWG URL: parse IPv6 NOT SUPPORTED\nfast-uri: parse URN x 2,596,395 ops/sec ±0.42% (98 runs sampled)\nurijs: parse URN x 1,152,412 ops/sec ±0.09% (97 runs sampled)\nWHATWG URL: parse URN x 1,183,307 ops/sec ±0.38% (100 runs sampled)\nfast-uri: parse URN uuid x 1,666,861 ops/sec ±0.10% (98 runs sampled)\nurijs: parse URN uuid x 852,724 ops/sec ±0.17% (95 runs sampled)\nWHATWG URL: parse URN uuid NOT SUPPORTED\nfast-uri: serialize uri x 1,741,499 ops/sec ±0.57% (95 runs sampled)\nurijs: serialize uri x 389,014 ops/sec ±0.28% (93 runs sampled)\nfast-uri: serialize IPv6 x 441,095 ops/sec ±0.37% (97 runs sampled)\nurijs: serialize IPv6 x 255,443 ops/sec ±0.58% (94 runs sampled)\nfast-uri: serialize ws x 1,448,667 ops/sec ±0.25% (97 runs sampled)\nurijs: serialize ws x 352,884 ops/sec ±0.08% (96 runs sampled)\nfast-uri: resolve x 340,084 ops/sec ±0.98% (98 runs sampled)\nurijs: resolve x 225,759 ops/sec ±0.37% (95 runs sampled)\n```\n\n## TODO\n\n- [ ] Support MailTo\n- [ ] Be 100% iso compatible with uri-js\n- [ ] Add browser test stack\n\n## License\n\nLicensed under [BSD-3-Clause](./LICENSE).\n", "readmeFilename": "README.md"}