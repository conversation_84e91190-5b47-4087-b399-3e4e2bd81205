{"_id": "lodash.isboolean", "_rev": "37-7fc3f543d9331322394b076e12410270", "name": "lodash.isboolean", "description": "The lodash method `_.isBoolean` exported as a module.", "dist-tags": {"latest": "3.0.3"}, "versions": {"2.0.0": {"name": "lodash.isboolean", "version": "2.0.0", "description": "The Lo-Dash function `_.isBoolean` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["customize", "functional", "lodash", "performance", "speed", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "_id": "lodash.isboolean@2.0.0", "dist": {"shasum": "080ad6d6a73c436f4cf0d14bb12dd3ebefa3a6a3", "tarball": "https://registry.npmjs.org/lodash.isboolean/-/lodash.isboolean-2.0.0.tgz", "integrity": "sha512-lRnSjeKA7Ualf+gmSpQyr1D7p/evtAKlc06hLt8cE5LAWGNaiWc9OU+1KStek7YhNlfrHd+bod9HIMowkFl/Ew==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICDhJwMN6N6jpkRzpRuejrUeaot7+2AjHua4vVnWgo7aAiA11LCLBdWWxhqB4kjQt1k0UeGNj0b++xUeuzIR5loF3Q=="}]}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}}, "2.1.0": {"name": "lodash.isboolean", "version": "2.1.0", "description": "The Lo-Dash function `_.isBoolean` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "performance", "speed", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "_id": "lodash.isboolean@2.1.0", "dist": {"shasum": "12fc96fcbe424a08f61716304c2b91f70c9db61c", "tarball": "https://registry.npmjs.org/lodash.isboolean/-/lodash.isboolean-2.1.0.tgz", "integrity": "sha512-CRpnvhF2Wu2ea+NlJp+xVBLqR+VSbCK791Yu7Iwe128WbahqRzBWNFSpnI681nLCeBRffE/vk2DclkEurn1HcQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHheFi7AKb5i7XofcZDnM5MlrDze9B4Qg0CIANHcwCZZAiEAjDYh0E5O1aIt+UhIbu/WwU1HdeMKoR5H7FqT9CgXV+c="}]}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}]}, "2.2.0": {"name": "lodash.isboolean", "version": "2.2.0", "description": "The Lo-Dash function `_.isBoolean` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "performance", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "_id": "lodash.isboolean@2.2.0", "dist": {"shasum": "dbfc682b9a0d63a191d99c98381758cdd87eb3d1", "tarball": "https://registry.npmjs.org/lodash.isboolean/-/lodash.isboolean-2.2.0.tgz", "integrity": "sha512-ZpjYaLbTszCdgW48UgGbVr75ZeFlM01NejAeHPdH31aB+KGAEefaFsMZmHHWY99LDUhzBm2JdzK7SJmumeDI6w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCq6Ug7I8cGxjI79zlnmWDjKXXS1dyw5CRU8vGA65j+oAIgNxYMyKnNsvOMNKFniZy+AbKT7wa8Hrf9U5nTXDvggD0="}]}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}]}, "2.2.1": {"name": "lodash.isboolean", "version": "2.2.1", "description": "The Lo-Dash function `_.isBoolean` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "_id": "lodash.isboolean@2.2.1", "dist": {"shasum": "1c9d800a4f3cca5918d90b2f7a21566aaa6f6a75", "tarball": "https://registry.npmjs.org/lodash.isboolean/-/lodash.isboolean-2.2.1.tgz", "integrity": "sha512-6sOIDjbaXWV50GmFgs2QmO4RD5DifRj/DOxwvlV61IdMPb4sy4ssXFPur+AbORvF/ndT5cAQ/RrChhJj/83hFw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGYhaxEaFIAd0LlT5Z4kdNzPknRf+ll9GaG9S9zlHQ1pAiEA4vfuo9RcxnZNGYcgzmkhkBWyhDx7mrHWgrkhJaV/CqA="}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}]}, "2.3.0": {"name": "lodash.isboolean", "version": "2.3.0", "description": "The Lo-Dash function `_.isBoolean` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "_id": "lodash.isboolean@2.3.0", "dist": {"shasum": "36eb4a5a8e0938dab526517867dfe4aa0b0acd4d", "tarball": "https://registry.npmjs.org/lodash.isboolean/-/lodash.isboolean-2.3.0.tgz", "integrity": "sha512-1buOhyfZ/urQFr2Wg6fqKpdfyQucqU5Lf87XWI6NnXqKrD17jWFYUyCjeTkNDsIeFasM/4yMWCbpLcLp+5JCZw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC+Xxz5CR1i/BjDnYkt4dJg8sjvSaoZ5Aw4/Zv8/x9a6AiEA1wW8FQ8zE3+c8Hd6xHAi2u9qKenyoUVrqciBKx5a1Ew="}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}]}, "2.4.0": {"name": "lodash.isboolean", "version": "2.4.0", "description": "The Lo-Dash function `_.isBoolean` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "_id": "lodash.isboolean@2.4.0", "dist": {"shasum": "139eb98c8d94c3a70dc6c3d8c6d182fb5f44b309", "tarball": "https://registry.npmjs.org/lodash.isboolean/-/lodash.isboolean-2.4.0.tgz", "integrity": "sha512-H8e1yOPzq17QYtGxx7e2cFnzfBD2Az8/JmiPyGDi4Atowi/gtrNplZJS6GZKxi/EL7YEIq46lWGVVUZTSStcdA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAvEuuri2EKFaIQESilq8z1K476CfEdzbVVztbssh+pbAiEA/Wz7mWcH7jb0zCSiHzTlCDpYB0247HB35bX7XDQ87fk="}]}, "_from": ".", "_npmVersion": "1.3.14", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}]}, "2.4.1": {"name": "lodash.isboolean", "version": "2.4.1", "description": "The Lo-Dash function `_.isBoolean` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "_id": "lodash.isboolean@2.4.1", "dist": {"shasum": "9756de9aa9f7a46659c05e43250a09e11dd1502c", "tarball": "https://registry.npmjs.org/lodash.isboolean/-/lodash.isboolean-2.4.1.tgz", "integrity": "sha512-BIgUtb0mbpiSGkGdLHlw60YPcx7PGK3hN3XMTfnFGokgi2Pf6rNJG/lH1uW4EQ0JgFrZVN9QBi4jM8IgYHMzyw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICacZdJfZjJKrxRb4JLnUfh1JNDgMIakZGpV8y6JDj5xAiEA1J+6xNxmWV5CvUh3IMUPDtpSNkIfMPI1Ax+kd1x5Osk="}]}, "_from": ".", "_npmVersion": "1.3.14", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}]}, "3.0.0": {"name": "lodash.isboolean", "version": "3.0.0", "description": "The modern build of lodash’s `_.isBoolean` as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "https://github.com/lodash/lodash"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isboolean@3.0.0", "_shasum": "9c695865f12211bad0c059ce0061456c90ea738a", "_from": ".", "_npmVersion": "2.3.0", "_nodeVersion": "0.10.35", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "dist": {"shasum": "9c695865f12211bad0c059ce0061456c90ea738a", "tarball": "https://registry.npmjs.org/lodash.isboolean/-/lodash.isboolean-3.0.0.tgz", "integrity": "sha512-iOqJTjmOPg3n46DvdIRc3hUxFr/hLtIyxXILT3o9/FHj+3R7vWOWdy4QsCS8W3GOGEuX9K1njg9OirLzJUfG0g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDLj6ySnbPv6CcVrhOOuI55NDVFz9YZXl7UP+OxM+8naAIhAPPq43L51aXG3KEd+YfvR4HFcJb3uq1v7HC1k3Jdyefw"}]}}, "3.0.1": {"name": "lodash.isboolean", "version": "3.0.1", "description": "The modern build of lodash’s `_.isBoolean` as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "https://github.com/lodash/lodash"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isboolean@3.0.1", "_shasum": "efcc72ba324743aed88a6188b5adaa58d9c319a4", "_from": ".", "_npmVersion": "2.7.3", "_nodeVersion": "0.12.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "dist": {"shasum": "efcc72ba324743aed88a6188b5adaa58d9c319a4", "tarball": "https://registry.npmjs.org/lodash.isboolean/-/lodash.isboolean-3.0.1.tgz", "integrity": "sha512-2yZzgA9HgEWAWWEcsn8qeM5l6Kh8LVBdrarbJSqrJdV1q8ErwIGlC8MNGdLaa5lugMtkCsObL60YFCsiMPWNeQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEK2O+IGQehAZV1dsJeYGe5/KZfCFPOJAmGk3pX/IN8OAiBsd3XPJ1LQEwLPpBhOALBShLN+w/pqY0sUs284zmO83g=="}]}}, "3.0.2": {"name": "lodash.isboolean", "version": "3.0.2", "description": "The lodash method `_.isBoolean` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util", "isboolean"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isboolean@3.0.2", "_shasum": "c16898a8189fe8ecf6c67febb2023ebe831af335", "_from": ".", "_npmVersion": "2.14.15", "_nodeVersion": "5.4.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "c16898a8189fe8ecf6c67febb2023ebe831af335", "tarball": "https://registry.npmjs.org/lodash.isboolean/-/lodash.isboolean-3.0.2.tgz", "integrity": "sha512-ujqUiwiii8k/2azePMeRivE+fKPG5na4O+C7+3xELZkU+O6SPTSqeniBu1oqBZwu+2s3dBVIwFOJytEp+lVw+g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGpsGJRoZAEtmW0ieb7yt7aq6EkeHCyCHs8veWYF8XeiAiEA2c26AOln6iRBbw4xh7qM9v0Uy6CBIA7thxjFu4Y89wE="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}]}, "3.0.3": {"name": "lodash.isboolean", "version": "3.0.3", "description": "The lodash method `_.isBoolean` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "isboolean"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isboolean@3.0.3", "_shasum": "6c2e171db2a257cd96802fd43b01b20d5f5870f6", "_from": ".", "_npmVersion": "2.14.15", "_nodeVersion": "5.4.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "6c2e171db2a257cd96802fd43b01b20d5f5870f6", "tarball": "https://registry.npmjs.org/lodash.isboolean/-/lodash.isboolean-3.0.3.tgz", "integrity": "sha512-Bz5mupy2SVbPHURB98VAcw+aHh4vRV5IPNhILUCsOzRmsTmSQ17jIuqopAentWoehktxGd9e/hbIXq980/1QJg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDTW2zqH3Q53UV1jAOEttC5dV0ChBDYTmqGULIniMOpCgIhAKUW3IzdIxdC1dYFEiVCqBWX7vB7i/NasDXWRhD+JxbW"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-9-west.internal.npmjs.com", "tmp": "tmp/lodash.isboolean-3.0.3.tgz_1454484499476_0.9152139471843839"}}}, "readme": "# lodash.isboolean v3.0.3\n\nThe [lodash](https://lodash.com/) method `_.isBoolean` exported as a [Node.js](https://nodejs.org/) module.\n\n## Installation\n\nUsing npm:\n```bash\n$ {sudo -H} npm i -g npm\n$ npm i --save lodash.isboolean\n```\n\nIn Node.js:\n```js\nvar isBoolean = require('lodash.isboolean');\n```\n\nSee the [documentation](https://lodash.com/docs#isBoolean) or [package source](https://github.com/lodash/lodash/blob/3.0.3-npm-packages/lodash.isboolean) for more details.\n", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "time": {"modified": "2022-06-19T13:35:48.954Z", "created": "2013-09-23T06:33:49.400Z", "2.0.0": "2013-09-23T07:36:56.644Z", "2.1.0": "2013-09-23T07:55:22.962Z", "2.2.0": "2013-09-29T22:09:24.606Z", "2.2.1": "2013-10-03T18:50:03.483Z", "2.3.0": "2013-11-11T16:47:25.896Z", "2.4.0": "2013-11-26T19:55:40.164Z", "2.4.1": "2013-12-03T17:13:57.703Z", "3.0.0": "2015-01-26T15:29:15.791Z", "3.0.1": "2015-03-25T23:35:18.792Z", "3.0.2": "2016-01-13T11:04:25.714Z", "3.0.3": "2016-02-03T07:28:20.290Z"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "homepage": "https://lodash.com/", "keywords": ["lodash-modularized", "isboolean"], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "license": "MIT", "readmeFilename": "README.md"}