{"_id": "email-verifier", "_rev": "13-577cfd7a40eda0122af21a8597235dc7", "name": "email-verifier", "description": "The best possible way to verify and validate an email address.", "dist-tags": {"latest": "0.4.1"}, "versions": {"0.0.1": {"name": "email-verifier", "version": "0.0.1", "description": "Checks if email address actually exists, instead of just validating it's format", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/Cyberuben/node-email-verifier.git"}, "keywords": ["email", "verify", "verification", "smtp"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/Cyberuben/node-email-verifier/issues"}, "homepage": "https://github.com/Cyberuben/node-email-verifier#readme", "gitHead": "aa73951847ba8d99e8fec0aa41cbfc203b50562b", "_id": "email-verifier@0.0.1", "_shasum": "95ae4fae1519af4f7adf5ca5cd377d3129079825", "_from": ".", "_npmVersion": "3.8.9", "_nodeVersion": "6.2.0", "_npmUser": {"name": "cyberuben", "email": "<EMAIL>"}, "dist": {"shasum": "95ae4fae1519af4f7adf5ca5cd377d3129079825", "tarball": "https://registry.npmjs.org/email-verifier/-/email-verifier-0.0.1.tgz", "integrity": "sha512-ER1bO+h9yZ3xKT+USdwOx7uBG8wj13RlAImjQUec2JaaKjVU5QMTq2j7sC+v8p2+zowLkEuI0cjDMBZr+25+Fw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFec363Y1We3B7sXp0sQADibTdL9nfXJ/b7gCO8xQQNHAiEApR+G1XWlEo8lTWsB75ehmHXhakeXneF5j2IP5KyM/Rc="}]}, "maintainers": [{"name": "cyberuben", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/email-verifier-0.0.1.tgz_1469043495073_0.13899336475878954"}, "directories": {}}, "0.1.0": {"name": "email-verifier", "version": "0.1.0", "description": "The best possible way to verify and validate an email address.", "main": "index.js", "scripts": {"test": "./node_modules/mocha/bin/mocha --no-timeouts"}, "repository": {"type": "git", "url": "git+https://github.com/rdegges/node-email-verifier.git"}, "keywords": ["email", "verify", "verification", "validation", "validate"], "author": {"name": "<PERSON>"}, "license": "Unlicense", "bugs": {"url": "https://github.com/rdegges/node-email-verifier/issues"}, "homepage": "https://github.com/rdegges/node-email-verifier#readme", "dependencies": {"backoff": "^2.5.0", "request": "^2.82.0"}, "devDependencies": {"mocha": "^3.5.3"}, "gitHead": "0bfa7a8eb95930c9b30a70e540dc46c98a296ce2", "_id": "email-verifier@0.1.0", "_npmVersion": "5.4.2", "_nodeVersion": "8.5.0", "_npmUser": {"name": "rdegges", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-rgxhPsAPKUm7LVVfmuMQfAhly6KgqTakMWW89LOESZ59sLNgubTrjWqoxm2Br8XousQpYC/qR2Rn9pK6ibrtyA==", "shasum": "ab335fe04991a40ce428e2c6107bdd50babd75dc", "tarball": "https://registry.npmjs.org/email-verifier/-/email-verifier-0.1.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDv0uZXW5H9G9G5yQYiLu3DaENcEiJJsqKFewryxdtLWQIhAP+jbU/GAJ60sgTDqx3wZOACHUz4nrWFe1nKXlyLEgh7"}]}, "maintainers": [{"email": "<EMAIL>", "name": "rdegges"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/email-verifier-0.1.0.tgz_1506888689462_0.5604808265343308"}, "directories": {}}, "0.2.0": {"name": "email-verifier", "version": "0.2.0", "description": "The best possible way to verify and validate an email address.", "main": "index.js", "scripts": {"test": "./node_modules/mocha/bin/mocha --no-timeouts"}, "repository": {"type": "git", "url": "git+https://github.com/whois-api-llc/node-email-verifier.git"}, "keywords": ["email", "verify", "verification", "validation", "validate"], "author": {"name": "<PERSON>"}, "license": "Unlicense", "bugs": {"url": "https://github.com/whois-api-llc/node-email-verifier/issues"}, "homepage": "https://github.com/whois-api-llc/node-email-verifier#readme", "dependencies": {"backoff": "^2.5.0", "request": "^2.82.0"}, "devDependencies": {"mocha": "^3.5.3"}, "gitHead": "b2a04ade442ac27ac5170c556f4cbcf3adb1371b", "_id": "email-verifier@0.2.0", "_npmVersion": "5.5.1", "_nodeVersion": "8.5.0", "_npmUser": {"name": "rdegges", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-+ALdkH5nQYCiyuUSzuuaUjc1mi8/zysMdnC95g19LZVJX/hx1p0Yg7vGr5vqUuvQh6nEMbQTvHQCtW27+d5N9Q==", "shasum": "70a320f5eb2a6011d7ad92cb99d8b8d2f3b9c39e", "tarball": "https://registry.npmjs.org/email-verifier/-/email-verifier-0.2.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDaQ5xsLOQfQbn4HE9drUdT0c5Bba80kBQ9Z5cMDhb0pwIgIZolGgvZWuyuh1ZfIPnt5/Tlbtz7t/YUw6NzkhZ4V18="}]}, "maintainers": [{"email": "<EMAIL>", "name": "rdegges"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/email-verifier-0.2.0.tgz_1508393035035_0.6805552942678332"}, "directories": {}}, "0.2.1": {"name": "email-verifier", "version": "0.2.1", "description": "The best possible way to verify and validate an email address.", "main": "index.js", "scripts": {"test": "./node_modules/mocha/bin/mocha --no-timeouts"}, "repository": {"type": "git", "url": "git+https://github.com/whois-api-llc/node-email-verifier.git"}, "keywords": ["email", "verify", "verification", "validation", "validate"], "author": {"name": "<PERSON>"}, "license": "Unlicense", "bugs": {"url": "https://github.com/whois-api-llc/node-email-verifier/issues"}, "homepage": "https://github.com/whois-api-llc/node-email-verifier#readme", "dependencies": {"backoff": "^2.5.0", "request": "^2.82.0"}, "devDependencies": {"mocha": "^3.5.3"}, "gitHead": "2bc3b9a4a897b57f75f9a71feb71914216c1c4e7", "_id": "email-verifier@0.2.1", "_npmVersion": "5.5.1", "_nodeVersion": "8.5.0", "_npmUser": {"name": "rdegges", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-n//XSFEtSwy4I80QLTOqp2FpVWd4EFbHBwURMTbFOJ8U1xSM9opqoxA3k54WtJnQIQ0THI77vRTXA/jXC4WeLg==", "shasum": "3caa9d85a87f21723b607b2ee0cb0666e6383ea4", "tarball": "https://registry.npmjs.org/email-verifier/-/email-verifier-0.2.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCx87/ZfIs0brA/NC5lcUVnWWmp4ZczaTnM6YSO5owaIQIhAL1zoGntsY3xRZJvgSQ4cYVj+5LyjynXpnuIC7gMGjSA"}]}, "maintainers": [{"email": "<EMAIL>", "name": "rdegges"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/email-verifier-0.2.1.tgz_1508393545842_0.6913344420026988"}, "directories": {}}, "0.3.0": {"name": "email-verifier", "version": "0.3.0", "description": "The best possible way to verify and validate an email address.", "main": "index.js", "scripts": {"test": "./node_modules/mocha/bin/mocha --no-timeouts"}, "repository": {"type": "git", "url": "git+https://github.com/whois-api-llc/node-email-verifier.git"}, "keywords": ["email", "verify", "verification", "validation", "validate"], "author": {"name": "<PERSON>"}, "license": "Unlicense", "bugs": {"url": "https://github.com/whois-api-llc/node-email-verifier/issues"}, "homepage": "https://github.com/whois-api-llc/node-email-verifier#readme", "dependencies": {"backoff": "^2.5.0", "request": "^2.82.0"}, "devDependencies": {"mocha": "^3.5.3"}, "gitHead": "04c6608688ff3c318f7caed35606f52ebdc8f570", "_id": "email-verifier@0.3.0", "_shasum": "e4f56d2f84263cfd8bb5b3208149798da3bad51a", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.9.0", "_npmUser": {"name": "rdegges", "email": "<EMAIL>"}, "dist": {"shasum": "e4f56d2f84263cfd8bb5b3208149798da3bad51a", "tarball": "https://registry.npmjs.org/email-verifier/-/email-verifier-0.3.0.tgz", "fileCount": 7, "unpackedSize": 34195, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa1DF9CRA9TVsSAnZWagAAeK8P/RZIN3fUI3BGV8DCYCKZ\nhkiQ80Iulb2e2I1Ye4WAYst0PZ3X0lczjcNXr1/Tp7HDeOzcE8n2PRvze1SF\n8MnGBFyG0M5ZN32Cxa+Plqf2RpK27GorGu6BxIGETNqJnhCWmSjD5DDKAQKz\nBksAPc3Zzf7x+G6QLtQ3NfjY25nychAcKP58XNMejz8Rur90agW55TWRj+4p\nuprgqXtWTukLLQa64imLf3CVUqcEvOiXO5Fb74e+Ovb97p75rd2cflv/yTga\nSupmSj09l9xykBfXzV7j20Sin3dCNtZ2F2ZO77kJwS6ZEIi8LRcsSMQ9aq+R\nOxKz2HsGjEI8KYkSchUP6R0c8PTEAYSsutZkn3Qe0iLo104DI61DQ6mUdY+t\n4zlYTAjkfTtaM7e4C6YuqvGTtzStSIq3pu4p6JRP3XfjhvZVNwEMJjhgbPKt\nWpDdXkunYwEOsgU+DCLuc7Ui0ioF1UvIGPaT2/01+RWZ/Mlro8R/YxVB59rW\nWMw7UCfGe6sQ7hO3wTdpoytwn90+/XMOUWlWxm+QyTsGctHBw9CzKm12ZCmp\nvR7BDXHsnyUxcWbu95dSFvn9Igk8o6k/7V/QPfN0U0Q+U1bVzXevzees1a6b\n5XjWLCCtTse9zZMtz5VLGOcp1WFO+Mj//yzJG7LJgIMDyyjfhKGn/JftGwnE\nx0tO\r\n=w1vD\r\n-----END PGP SIGNATURE-----\r\n", "integrity": "sha512-2CQtIVyM9c9sSbNd8LCkW7ntr24skJzv9gDzE3kbaIe5JvOzkOFXblf3OCMiy2XZzLCdp6BtKfK6w6Robgm0Kg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC5s8FsMvWGE7xAdOGZrkcaXwJ4hWd7mg0udoWwhGUshAiEAx+K4Pn6elbCIOT5xntKqmLAPSx7IZCXd6z2zQXS3HQ8="}]}, "maintainers": [{"email": "<EMAIL>", "name": "rdegges"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/email-verifier_0.3.0_1523855740555_0.2002476918144156"}, "_hasShrinkwrap": false}, "0.4.0": {"name": "email-verifier", "version": "0.4.0", "description": "The best possible way to verify and validate an email address.", "main": "index.js", "scripts": {"test": "./node_modules/mocha/bin/mocha --no-timeouts"}, "repository": {"type": "git", "url": "git+https://github.com/whois-api-llc/node-email-verifier.git"}, "keywords": ["email", "verify", "verification", "validation", "validate"], "author": {"name": "<PERSON>"}, "license": "Unlicense", "bugs": {"url": "https://github.com/whois-api-llc/node-email-verifier/issues"}, "homepage": "https://github.com/whois-api-llc/node-email-verifier#readme", "dependencies": {"backoff": "^2.5.0", "request": "^2.82.0"}, "devDependencies": {"mocha": "^3.5.3"}, "gitHead": "3c381b8422ea3074e79a017f38c0e76f952caeb2", "_id": "email-verifier@0.4.0", "_npmVersion": "6.0.0", "_nodeVersion": "9.2.0", "_npmUser": {"name": "rdegges", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-l6P4iItwG4jSkOWIq/zXdUk58Rpjv0Q45zuXmXapEXGcUSA600K8oH/RyNpoy/By0PhdhmvAxyz94f2IHxq6EA==", "shasum": "4c85599480558de3d0c856bbdaf4573f900a5fa7", "tarball": "https://registry.npmjs.org/email-verifier/-/email-verifier-0.4.0.tgz", "fileCount": 5, "unpackedSize": 12342, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa72N0CRA9TVsSAnZWagAATRcQAJWDg5S1luO4rYzvhAPD\nq/nf1TnToydi4REjLBTkFYc5PcPVWvWaolaUv1ojwDEGoyQ9ejmcaQxYznOa\nGZEUlsN25l4pdu7BboglOPprgRZe5stpY0MlmZ5HPP30pa3ILGCzbe38EdGb\n5VMZPo6GDxrJYEQy3okMeo3TJg+Xve094wEXY7VlOySuV72BZAOaShHLMu+u\n54bZYrnW669nNJGTG6EM/2VGR+6hZWUXPEqDItyPxJ44OUzfS8jZdTMNQ5Gk\n4YJ3wVkoUL/dq5RfOvrTE4+VTsdwv8VVYSz8QVMLdMMpxo/2UgEQE3ja03HH\nxQgnWugJhmMmYLnENJ7T0FpVV8s6BiN7f8+sgPGa9DkIawrIxmbMZ2tZWugl\niidlA4anl2UZoKuCISGuYkttG17N3udx25nKrVnalHEhjTLLhi7iqQgBxKvq\n72n+jIX/575PRws6otdLatDbW2Lj2LLA8z5lYfKtijmnAUBb+6QlGLOz9R1l\neTyJKQYbemtha/JEq8ggKsI9YIm2TXm9sOJBv+0FymZaEOv0veOQ20wbf7+C\n7cnVEFqvm4j3iVbGv9p1R3CPKIN9L6k1rLLIwz+TMAWUh3zqev2gy++RsRHX\nEbZFl+TgIsVC22rRvZlJ/KPC31oQevHnZrZkCP+83p7G7oVYPKh6qOqiEDmM\n0Zrx\r\n=Avxm\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFgjmnWZwMG+tzQmR+LgKoeCgw0N+n013zJ8XiMTIdsUAiEArnfjGM/j4wTlCP0zqAfVjrf2jYnBAhG5Elc3g/WiY0A="}]}, "maintainers": [{"email": "<EMAIL>", "name": "rdegges"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/email-verifier_0.4.0_1525638002830_0.748568038163661"}, "_hasShrinkwrap": false}, "0.4.1": {"name": "email-verifier", "version": "0.4.1", "description": "The best possible way to verify and validate an email address.", "main": "index.js", "scripts": {"test": "./node_modules/mocha/bin/mocha --no-timeouts"}, "repository": {"type": "git", "url": "git+https://github.com/whois-api-llc/node-email-verifier.git"}, "keywords": ["email", "verify", "verification", "validation", "validate"], "author": {"name": "<PERSON>"}, "license": "Unlicense", "bugs": {"url": "https://github.com/whois-api-llc/node-email-verifier/issues"}, "homepage": "https://github.com/whois-api-llc/node-email-verifier#readme", "dependencies": {"backoff": "^2.5.0", "request": "^2.82.0"}, "devDependencies": {"mocha": "^5.2.0"}, "gitHead": "07db15bae0484aa0400c1294a156f04ae8034091", "_id": "email-verifier@0.4.1", "_npmVersion": "6.4.1", "_nodeVersion": "8.14.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-WZAeqKgsGTCKfuvHimWTYF4Lix1sq7pUeDDAMLT1qrmo7iu8hgxGqZ+lTJn7SEL/RcYqXrjDL2U+NK3SWVkgFA==", "shasum": "925d81adf44ad9fa1246a1eb8db83a7c22835af3", "tarball": "https://registry.npmjs.org/email-verifier/-/email-verifier-0.4.1.tgz", "fileCount": 5, "unpackedSize": 12342, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBkFqCRA9TVsSAnZWagAAgDgP/3CVwxywqniN1uQZH5ux\nEkv0UiW+W9Y8aYDMECZ3lBy/6gddY86+Mr789+YKDHb3Mw2DsaVi1qku8TIM\nDIMble2b3uzwpBqQ3jmnw2r9XsrbQDWAnRbH7KtLEG6wKe80IkBN7ezHOd5e\n+PIMQDSuzjXxP+U2YtdxwUTN4/LA2RdRhjHAm4crNPyBa3mlakDm43g67RQh\n9QDcc1bDz212t4Q2JMIo4gRmA2Xy6mGyLWepBQfHxXfzaW6c2IrPdylxYS8L\n4S+apvX1kvrD36NviH+A0vuxVuvEYN95iHD5QWmjjAZN2S2wi80Ay4QBbR2v\nq0fYEOqim0RtS7BmKS8mAOsMCdqjOm45tevOjEI1NG14/MonNOcHbfOEwD0n\nN8RvTWwu88lXMIZukXH2jwXCbQd+nRojTxZFTOBx4NI/UukSze0/tJR0pRfC\nsSWY7EPzuGMQ3AiHe2vpJpkTgu2a/56xA3fSZtVLKg3iyWfvLMp3P3jhPfps\n15g6ogR0mX5TTjbNtYUFmyFPZN1F3Ewd3HYbD9ZMlp0HAWk0rAd/IzZ6rcZZ\n7HaF9QqNpJK4DHrFqCHHUWq9HTy8Gu/LLSGRHik7yOu0RI9fBQungf+yQPjf\nGyOv+wUFe0k36ctzdC6sxcwQKvsvxM2+XovHOUjghcFbBS62bNquHFL5r5Ui\nm+U7\r\n=yroe\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCQZofLT/w7YMm8TZGzPY+uHi8Pt28DE7Xz7gEfKU0yWQIhAMPkdYNfh354a/kVpiHAX/5zZVTNDqdtICfGC5q0pSMv"}]}, "maintainers": [{"email": "<EMAIL>", "name": "rdegges"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/email-verifier_0.4.1_1543913834219_0.6133649317949403"}, "_hasShrinkwrap": false}}, "readme": "# node-email-verifier\n\n[![NPM Version](https://img.shields.io/npm/v/email-verifier.svg?style=flat)](https://npmjs.org/package/email-verifier)\n[![NPM Downloads](http://img.shields.io/npm/dm/email-verifier.svg?style=flat)](https://npmjs.org/package/email-verifier)\n[![Build Status](https://img.shields.io/travis/whois-api-llc/node-email-verifier.svg?style=flat)](https://travis-ci.org/whois-api-llc/node-email-verifier)\n\n*The best possible way to verify and validate an email address in Node.*\n\n![Email Verifier Icon](https://github.com/whois-api-llc/node-email-verifier/raw/master/images/verify-email.png)\n\n\n## Meta\n\n- Author: <PERSON>\n- Email: <EMAIL>\n- Twitter: [@rdegges](https://twitter.com/rdegges)\n- Site: https://www.rdegges.com\n- Status: production ready\n\nIf you'd like to read an article about how this library works, please check out\nthis blog post:\nhttps://emailverification.whoisxmlapi.com/blog/how-to-verify-an-email-address-using-nodejs\n\n\n## Prerequisites\n\nTo use this library, you'll need to create a free WhoisAPI account:\nhttps://emailverification.whoisxmlapi.com/\n\nIf you haven't done this yet, please do so now.\n\n\n## Installation\n\nTo install `email-verifier` using [npm](https://www.npmjs.org/), simply run:\n\n```console\n$ npm install email-verifier\n```\n\nIn the root of your project directory.\n\n\n## Verification Methods\n\nEmail verification is a tricky thing to do properly. There are a number of\ndifferent ways to \"verify\" an email address, and not all of them may be\nappropriate for your use case.\n\nThis library is really flexible, and allows you to pick and choose what types of\nverification are done in a granular way. Here is a list of all the different\ntypes of email verification this library handles. This list includes all of the\nlatest and greatest checks, which are fully supported.\n\nAll checking mechanisms conform to best practices, and provide confident\nverification.\n\n**Syntax Checking**: This checks the email addresses and ensures that it\nconforms to IETF standards using a complete syntactical email validation engine.\n\n**Fake Email Pattern Detection**: This checks the email address against a\npowerful built-in fake email pattern detector algorithm. The fake email pattern\ndetector is capable of detecting thousands of fake emails automatically with\nvery high accuracy.\n\n**Typo/Curse Words Check**: This checks the email address against all known\ncommon typos for most email domains. This will also detect certain curse words\npresent in the email address. This is useful if you're building an application\nwhere profanity is something you want to filter.\n\n**Mail Server Existence Check**: This checks the availability of the email\naddress domain using DNS MX records.\n\n**Mail Existence Check**: This checks if the email address really exists and can\nreceive email via SMTP connections and email-sending emulation techniques.\n\n**Catch-all Domain Email Check**: This checks to see if the email domain will\nreceive all of the email messages addressed to that domain, even if their\naddresses do not exist in the mail server. This tells you whether or not you've\nbeen given a wildcard/catch-all address, or an individual mailbox.\n\n**Disposable Email Address Check**: This checks if the email is provided by a\nknown Disposable Email Address (DEA) provider such as Mailinator, 10MinuteMail,\nGuerrillaMail and about 2000 more.\n\n\n## Usage\n\nOnce you have `email-verifier` installed, you can use it to easily verify an\nemail address. Email verification performs a number of checks to ensure a given\nemail address is actually valid.\n\nThis library gives you access to all sorts of email verification data that you\ncan use in your application in any number of ways.\n\n```javascript\nconst Verifier = require(\"email-verifier\");\n\nlet verifier = new Verifier(\"your_email_verification_api_key\");\nverifier.verify(\"<EMAIL>\", (err, data) => {\n  if (err) throw err;\n  console.log(data);\n});\n```\n\nHere's the sort of data you might get back when running all checks at once:\n\n```json\n{\n  \"catchAll\": \"false\",\n  \"disposable\": \"false\",\n  \"dns\": \"OK\",\n  \"emailAddress\": \"<EMAIL>\",\n  \"free\": \"false\",\n  \"mxs\": [ \"mail.protonmail.ch\" ],\n  \"smtp\": \"OK\",\n  \"validFormat\": \"OK\"\n}\n```\n\nBy default, when verifying an email address all types of verification checks\nwill be performed. This can take a while (up to two seconds), and may not be\nideal for your use case.\n\nIf you prefer to only check certain types of email verification information, you\ncan pass in your preferred checks when creating the `Verifier` object:\n\n```javascript:\nconst Verifier = require(\"email-verifier\");\n\nlet verifier = new Verifier(\"your_email_verification_api_key\", {\n  checkCatchAll: false,\n  checkDisposable: false,\n  checkFree: false,\n  validateDNS: false,\n  validateSMTP: false\n});\n```\n\nIf, for some reason, you notice that a particular email's `validateSMTP` status\nis incorrect, this usually means that the person's email provider has recently\nchanged. The email verification API services caches SMTP checks for speed\npurposes, but you can force a cache refresh by specifying the optional\n`hardRefresh` option when making a query.\n\nFor instance:\n\n```javascript\nconst Verifier = require(\"email-verifier\");\n\nlet verifier = new Verifier(\"your_email_verification_api_key\");\nverifier.verify(\"<EMAIL>\", { hardRefresh: true }, (err, data) => {\n  if (err) throw err;\n  console.log(data);\n});\n```\n\nBy default, this library also handles retrying failed HTTP requests for you. For\ninstance: if the verification API service is currently down or having issues,\nyour request will be retried up to five consecutive times before failing.\n\nAgain: this can add more request time, and may not be what you want in all\ncases.\n\nIf you'd prefer to lower the amount of retries that this library will perform on\nyour behalf, you can pass in a `retries` option like so:\n\n```javascript\nconst Verifier = require(\"email-verifier\");\n\nlet verifier = new Verifier(\"your_email_verification_api_key\", {\n  retries: 2\n});\n```\n\n\n## Changelog\n\n0.4.0: *05-06-2018*\n\n- Adding support for the `hardRefresh` API option.\n\n0.3.0: *4-15-2018*\n\n- Updated API endpoint.\n- Switched from using username/password auth to API key auth.\n\n0.2.1: *10-18-2017*\n\n- Updating library to work with recent API changes.\n\n0.2.0: *10-18-2017*\n\n- Updating docs and repo path.\n\n0.1.0: *10-1-2017*\n\n- First release!\n", "maintainers": [{"email": "<EMAIL>", "name": "rdegges"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON>"}], "time": {"modified": "2022-06-16T09:26:14.986Z", "created": "2016-07-20T19:38:16.547Z", "0.0.1": "2016-07-20T19:38:16.547Z", "0.1.0": "2017-10-01T20:11:29.555Z", "0.2.0": "2017-10-19T06:03:55.132Z", "0.2.1": "2017-10-19T06:12:25.913Z", "0.3.0": "2018-04-16T05:15:40.687Z", "0.4.0": "2018-05-06T20:20:02.935Z", "0.4.1": "2018-12-04T08:57:14.372Z"}, "homepage": "https://github.com/whois-api-llc/node-email-verifier#readme", "keywords": ["email", "verify", "verification", "validation", "validate"], "repository": {"type": "git", "url": "git+https://github.com/whois-api-llc/node-email-verifier.git"}, "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/whois-api-llc/node-email-verifier/issues"}, "license": "Unlicense", "readmeFilename": "README.md"}