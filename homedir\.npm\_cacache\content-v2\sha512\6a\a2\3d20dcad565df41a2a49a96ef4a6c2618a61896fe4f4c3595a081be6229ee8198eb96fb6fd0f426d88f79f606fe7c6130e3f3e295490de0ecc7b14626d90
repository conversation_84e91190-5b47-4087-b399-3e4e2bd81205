{"name": "hasown", "dist-tags": {"latest": "2.0.2"}, "versions": {"1.0.1": {"name": "hasown", "version": "1.0.1", "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.4"}, "dist": {"shasum": "b64ff1570673ba06cc5d0183c0a4f0b5b1bd6459", "tarball": "https://registry.npmjs.org/hasown/-/hasown-1.0.1.tgz", "integrity": "sha512-My8IVgPaNw1TPrcOtLxG5N2BQJUr2YYI8a3ei3Njx4QIZ+WzEkvLQ4jySrcy6YNfq1JwHpyimb4p2Rw5IuE/SA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC2PWvzNlWQHInYuUclgwlqTxpUbJxAWKeX9B4eBbEVdAIgMiAZHo+4GgV/pDaqJvxagk93ulJYd5IJcFBleF46caM="}]}}, "2.0.0": {"name": "hasown", "version": "2.0.0", "dependencies": {"function-bind": "^1.1.2"}, "devDependencies": {"@ljharb/eslint-config": "^21.1.0", "@types/function-bind": "^1.1.9", "@types/mock-property": "^1.0.1", "@types/tape": "^5.6.3", "aud": "^2.0.3", "auto-changelog": "^2.4.0", "eslint": "=8.8.0", "evalmd": "^0.0.19", "in-publish": "^2.0.1", "mock-property": "^1.0.2", "npmignore": "^0.3.0", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.7.1", "typescript": "^5.3.0-dev.20231019"}, "dist": {"integrity": "sha512-vUptKVTpIJhcczKBbgnS+RtcuYMB8+oNzPK2/Hp3hanz8JmpATdmmgLgSaadVREkDm+e2giHwY3ZRkyjSIDDFA==", "shasum": "f4c513d454a57b7c7e1650778de226b11700546c", "tarball": "https://registry.npmjs.org/hasown/-/hasown-2.0.0.tgz", "fileCount": 11, "unpackedSize": 10837, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHEMS61n5pjHhGQuuWvV31D+pe/OiLmR6TMc3v1yh0kBAiEA8SQoSfqGFGGm6/ZJBXPQm6o8RaFI1/UzrWvqAd3xyAg="}]}, "engines": {"node": ">= 0.4"}}, "2.0.1": {"name": "hasown", "version": "2.0.1", "dependencies": {"function-bind": "^1.1.2"}, "devDependencies": {"@ljharb/eslint-config": "^21.1.0", "@types/function-bind": "^1.1.10", "@types/mock-property": "^1.0.2", "@types/tape": "^5.6.4", "aud": "^2.0.4", "auto-changelog": "^2.4.0", "eslint": "=8.8.0", "evalmd": "^0.0.19", "in-publish": "^2.0.1", "mock-property": "^1.0.3", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.7.4", "typescript": "next"}, "dist": {"integrity": "sha512-1/th4MHjnwncwXsIW6QMzlvYL9kG5e/CpVvLRZe4XPa8TOUNbCELqmvhDmnkNsAjwaG4+I8gJJL0JBvTTLO9qA==", "shasum": "26f48f039de2c0f8d3356c223fb8d50253519faa", "tarball": "https://registry.npmjs.org/hasown/-/hasown-2.0.1.tgz", "fileCount": 10, "unpackedSize": 11140, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCkyEeCGFLgef5pXaA5lBJ/2OgNan8SC1oifdZqTq5NkAIhAOm/oEE81UZRadLLQapZh8qy66V7ykHcRI6hjj3ruXvg"}]}, "engines": {"node": ">= 0.4"}}, "2.0.2": {"name": "hasown", "version": "2.0.2", "dependencies": {"function-bind": "^1.1.2"}, "devDependencies": {"@arethetypeswrong/cli": "^0.15.1", "@ljharb/eslint-config": "^21.1.0", "@ljharb/tsconfig": "^0.2.0", "@types/function-bind": "^1.1.10", "@types/mock-property": "^1.0.2", "@types/tape": "^5.6.4", "aud": "^2.0.4", "auto-changelog": "^2.4.0", "eslint": "=8.8.0", "evalmd": "^0.0.19", "in-publish": "^2.0.1", "mock-property": "^1.0.3", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.7.5", "typescript": "next"}, "dist": {"integrity": "sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==", "shasum": "003eaf91be7adc372e84ec59dc37252cedb80003", "tarball": "https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz", "fileCount": 10, "unpackedSize": 8765, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDwT1Hb/p8miJ/uXHCku9k+wzFABQD1ohA9jK2Wx7JrgwIgB1GtchHXm+XK4rsE3qomVCL/uhDlhqCYGaYgd7hCY5s="}]}, "engines": {"node": ">= 0.4"}}}, "modified": "2024-03-10T17:38:25.538Z"}