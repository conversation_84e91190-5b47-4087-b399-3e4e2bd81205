{"name": "magic-string", "dist-tags": {"latest": "0.30.17"}, "versions": {"0.1.0": {"name": "magic-string", "version": "0.1.0", "dependencies": {"vlq": "^0.1.0"}, "devDependencies": {"source-map": "^0.1.40"}, "dist": {"shasum": "ca0013859e534323a9dcbf07bfcb0fd92bb13f22", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.1.0.tgz", "integrity": "sha512-YiPF8PtEBP2UW4BQ4ppK4YuYwei/L7Y1Ql2CY7RYNJ69ThHrh/tI1WecD3QP0P4ifU6n5h9pWD/smsjXcFskzA==", "signatures": [{"sig": "MEUCIQC53eD1YcJXeHzCfGyuiXNFE/2M24WjSbCVkr/ZGA8U/wIgLhqtQCSz+D5mUchMiSBJSSFcrHCXM8FzydmvcIX8WEw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.1": {"name": "magic-string", "version": "0.1.1", "dependencies": {"vlq": "^0.1.0"}, "devDependencies": {"source-map": "^0.1.40"}, "dist": {"shasum": "958bfb152a10c5da049772de93cf9ef69588357a", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.1.1.tgz", "integrity": "sha512-Gv5kyMtRdty6UPBWFvm9o8QFEjBH4t4s73VwYCrMZLaAfxU+B0NbK2Tq1rmXL5caAfj1D4GY3YTW/SzTXcKNkA==", "signatures": [{"sig": "MEUCIQCeBH+5EYOczHwgFH01LAwO8xdewHYm17PkYD/ycJzB4QIgbhw1+jsVRJbkq3R+5com7ancmlBX4pk1sbstd+SsBmo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.3": {"name": "magic-string", "version": "0.1.3", "dependencies": {"vlq": "^0.1.0"}, "devDependencies": {"source-map": "^0.1.40"}, "dist": {"shasum": "9ea47e359623faec201d53aba19b19a553ba2936", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.1.3.tgz", "integrity": "sha512-d<PERSON><PERSON>ZcJSkxOGbxwwe/NDIHsp1SsvyPovR06iu/LDcX5PHZGk9UZm8FS90phnVXbgL4XL54AL/XSM3cD9BnM2fg==", "signatures": [{"sig": "MEYCIQCu9C+UoslwaS02x2Hx03k7X9eX0/8fSH/lTeAXUonuVQIhAKwTxbvxnGuqMTYL661fPVn8ieoMJH64Zx9o6BjS+05u", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.4": {"name": "magic-string", "version": "0.1.4", "dependencies": {"vlq": "^0.1.0"}, "devDependencies": {"source-map": "^0.1.40"}, "dist": {"shasum": "6c9d3cb0a432946352fde843ef6676466e66580a", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.1.4.tgz", "integrity": "sha512-oqSmSgG2EohQ2duL2/9fgEqzxq7tz8v6dWh9cESLlRgDY4yPvXErPgwuGFQVYGdFsEi8fVTAEuIZqHa0rUDKrw==", "signatures": [{"sig": "MEQCIEI51F0cvP97P0usjipsVBdRFYiNIUllRdJIhKjKGy1WAiBJMep6TIfbjhCsdHgNah+MCFe7CCKysCSVTpLCXFsi0w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.5": {"name": "magic-string", "version": "0.1.5", "dependencies": {"vlq": "^0.1.0"}, "devDependencies": {"gobble": "^0.6.10", "source-map": "^0.1.40"}, "dist": {"shasum": "72ae92918368033fd42c15e60940a8ad1fe557d1", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.1.5.tgz", "integrity": "sha512-qhkz6oDBkXfzfk+OHIwP+qkPA6AP8DcC/QpcLBxNAm7JUV2hROmbtzO5NaIrlVr++HQgqvErBffgyMll04Kouw==", "signatures": [{"sig": "MEYCIQDNmOHuYOWto30M90pHXuOCabMx49yPZqBVvMEBUuO7BgIhALOEmUZ9QfFB6rD1Ib0IRODifGb7t0JnQ6bHd19J45XJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.6": {"name": "magic-string", "version": "0.1.6", "dependencies": {"vlq": "^0.1.0"}, "devDependencies": {"gobble": "^0.6.10", "source-map": "^0.1.40"}, "dist": {"shasum": "925239a7635058832d40c270bef1026cdbd7be1a", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.1.6.tgz", "integrity": "sha512-+X/9kY/trs+rkv2sLDT5QpCHGqTRNELFttWksbV+0MoT6cKUJsF6Alm3fNtN7oG0dDoT+/yLp5YRddzfID/f5w==", "signatures": [{"sig": "MEYCIQCZh68Zu+Q6PRA95lcZKXPaWhEINzGMi7qqmL7sp5Xi3AIhAJ9Sc6IA5TUKmjKrdiBdSm40K4xnEUdGqWMzo9i10wAF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.7": {"name": "magic-string", "version": "0.1.7", "dependencies": {"vlq": "^0.1.0"}, "devDependencies": {"gobble": "^0.6.10", "source-map": "^0.1.40", "gobble-esperanto-bundle": "^0.1.0"}, "dist": {"shasum": "5cebf3aa48ac7743bfb4ca43de07c3ae08822a3a", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.1.7.tgz", "integrity": "sha512-t42SARfRF/hjAhl12H35j4bDxzoIOvndETzsBL71JqyALBn860b229pLQth/tQuJL/wNiPWPimx7HBC5Zqf/WA==", "signatures": [{"sig": "MEUCIAYwwBFawOCIt1LtK6cNV7dpRBCHHS2R4dAcP/nELL0+AiEAn4S3x/lTUKPnuOkRHLzeJOdg6Xr1Q3BjJo82XIRanDs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.8": {"name": "magic-string", "version": "0.1.8", "dependencies": {"vlq": "^0.1.0"}, "devDependencies": {"gobble": "^0.6.10", "source-map": "^0.1.40", "gobble-esperanto-bundle": "^0.1.0"}, "dist": {"shasum": "172e7cea3722e38bf8690cd778234bcb21147d13", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.1.8.tgz", "integrity": "sha512-i/ovvM5ICN1iwHrxOEdu28qCS10Uu8UL+kHvriaKQioLgAbgerTr+pw/CPDSEctpO1f72anBpOJPKk+Ea4gvcQ==", "signatures": [{"sig": "MEUCIQDrhDMfonQFhmy3l4RC3eaXUliqlbOy9AxXeJ3X84gNgQIgK7RE5idhxQvXJsqM1XEJb3+N3sqIya+rgUExNOGQp/A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.9": {"name": "magic-string", "version": "0.1.9", "dependencies": {"vlq": "^0.1.0"}, "devDependencies": {"gobble": "^0.6.10", "source-map": "^0.1.40", "gobble-esperanto-bundle": "^0.1.0"}, "dist": {"shasum": "b2a3f74097eb59c672230df495eef017e0bafa06", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.1.9.tgz", "integrity": "sha512-2rPiK41eA/kJY6fkQnkQNeWGGN/we3+LfxLMtkEY+cVE40pZ52AYuuaQdq79zod0jffdT4Vtzvr6JCrEKodwYQ==", "signatures": [{"sig": "MEUCICrAZOlMrB1EVdPsoe2214qJYv9xQVm0BqTC4PpX9nQKAiEA3iXKJXp1xzOQq/SESZSr7Au8scI1thasSnl1OBgE6AY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.10": {"name": "magic-string", "version": "0.1.10", "dependencies": {"vlq": "^0.1.0"}, "devDependencies": {"gobble": "^0.6.10", "source-map": "^0.1.40", "gobble-esperanto-bundle": "^0.1.0"}, "dist": {"shasum": "222833335ffdf36e5a08f2049b6ddb9ae5e43cc9", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.1.10.tgz", "integrity": "sha512-MjO3g6qUtFEh7jBp+MAek4sDUfx+FF6tKAhD2UIOC/jvWcVVIn+j1VYmB6fmXZyNogKBVaY5NkFT6GBFDAWf3Q==", "signatures": [{"sig": "MEUCIQDCJbOWbmAzPyoZ7NOGo8jysy2QNIhhQEp+pbBxF+WlPgIgZeBHncZcWIuqMOwu9J3/Mhq1qSJTA2m0KIiSTYe81zg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.2.0": {"name": "magic-string", "version": "0.2.0", "dependencies": {"vlq": "^0.1.0"}, "devDependencies": {"gobble": "^0.6.10", "source-map": "^0.1.40", "gobble-esperanto-bundle": "^0.1.0"}, "dist": {"shasum": "b4300ff015b933fd7011933903d66cc36aeddd0e", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.2.0.tgz", "integrity": "sha512-GTrjGJVoq6vdbvqIfKbGq3v69tJ/ZT5qHTzfVurjE+QwAsoeo79vwLaPovkHlgfQd7KXs4Keg/rl5wJQLCwvpw==", "signatures": [{"sig": "MEYCIQC9DyaZPOVbZyEGzPpk04lhNq/xyDbEkxVp706p2DtmZQIhAIF+eNtZOa19KgruQHs9tyH+a3pvWPgEFSGQP3Y5UJZ+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.2.1": {"name": "magic-string", "version": "0.2.1", "dependencies": {"vlq": "^0.1.0"}, "devDependencies": {"gobble": "^0.6.10", "source-map": "^0.1.40", "gobble-esperanto-bundle": "^0.1.0"}, "dist": {"shasum": "2116671c2b4b445a49e3344660064f60739e01bd", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.2.1.tgz", "integrity": "sha512-UL8FpYba6oj9GzGhP9yt7QKoJnk4SA50wUil/oT48ysc0EJlKi7YvvhjtqLqkR3iO8nGtYGFxlhRBHCQNXjtUQ==", "signatures": [{"sig": "MEUCIHIPMVDVf/t4sVA5oQ3R8jH3pK+WM2/VhZp8mCWhCGYCAiEAtPKM+D3FVWMVrgZgdujitWBrnxfAsjdg0dAzRfa82kw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.2.2": {"name": "magic-string", "version": "0.2.2", "dependencies": {"vlq": "^0.1.0"}, "devDependencies": {"gobble": "^0.6.10", "source-map": "^0.1.40", "gobble-esperanto-bundle": "^0.1.0"}, "dist": {"shasum": "0266f7ee3d9c45b45816d50af8c5b37f761a90e4", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.2.2.tgz", "integrity": "sha512-NziWhQcu+19enDrQl/Ac04iuPBX6lLAsUAEsmHtiHoXZsBi9dxs5yN5JJUj1hlHNCni+4ht+Zs1MsdlOC+ykxg==", "signatures": [{"sig": "MEUCID7aW+YhZLD28pufNEXA4HFJ/tBRWOJ5jLyBtn3zCqCMAiEA3MVf3mwlRsrlH7Seq4cnmG01cjG0l7K/AS639sECSXY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.2.3": {"name": "magic-string", "version": "0.2.3", "dependencies": {"vlq": "^0.1.0"}, "devDependencies": {"gobble": "^0.6.10", "source-map": "^0.1.40", "gobble-esperanto-bundle": "^0.1.0"}, "dist": {"shasum": "8288f6d0777aa4333f46cf6de696cf8d5e1d9809", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.2.3.tgz", "integrity": "sha512-i0YDH8zm1maXjC8WeZF5Ra2srG1Ay5+Xk8Xfgn6LJl8Fo0khCuY3OdktA3TUv34ZkX0JfZJOSenm2pP/KdZVhw==", "signatures": [{"sig": "MEYCIQCuCP6gpT74b1+NBC5Az78c5C9d1mqn3iE9eC64+uPxRwIhAPwRksqJoyoAle2IGHAALZIljR5DVo7fVc+Ssr95wi4+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.2.4": {"name": "magic-string", "version": "0.2.4", "dependencies": {"vlq": "^0.1.0"}, "devDependencies": {"gobble": "^0.6.10", "source-map": "^0.1.40", "gobble-esperanto-bundle": "^0.1.2"}, "dist": {"shasum": "9ed11dcdd1da89a5adb43b09191a0802528d3fd6", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.2.4.tgz", "integrity": "sha512-C4ClJ5pSLtxoR8B6RtClDFfatQMPTfzSDrSGHU4nJn40rIH5qRVeU4o5VkVEovg6dDeFGEJyM8BSVM5PRlCGYg==", "signatures": [{"sig": "MEQCICpoeGp57AITaLw0Iuqqf/GG1vRidddMvxq+vUOtvNv1AiAM4RSkjXTSenVJCW2ISbvpwsCRqx8jv09aEoPBvkjAdg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.2.5": {"name": "magic-string", "version": "0.2.5", "dependencies": {"vlq": "^0.1.0"}, "devDependencies": {"gobble": "^0.6.10", "source-map": "^0.1.40", "gobble-esperanto-bundle": "^0.1.2"}, "dist": {"shasum": "d4c3290eee67bfd3e5541c486c15b6f0326d4aed", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.2.5.tgz", "integrity": "sha512-8eNYyM+y4QiTkg8Uqh6lntQbjdFeGGc6TJ9nZsiuOMAoi0/uGO6K/NbTapEA+3cHW/b29ygXq1J0bx+FFsA4aw==", "signatures": [{"sig": "MEUCIGKBGCOaTdwEwS254vU2A1+EDFpoBJ4HgT2R2ibmGnpbAiEAu4P4Bz2Cb945YhoRZqnZjty4Sg6dl2yieq0zfeoBYZE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.2.6": {"name": "magic-string", "version": "0.2.6", "dependencies": {"vlq": "^0.1.0"}, "devDependencies": {"gobble": "^0.6.10", "source-map": "^0.1.40", "gobble-esperanto-bundle": "^0.1.3"}, "dist": {"shasum": "26ea8ac314c2cf28889333916d57e29929c11cbd", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.2.6.tgz", "integrity": "sha512-PniDsViCF+sf9xaXzJFk6upAEpnJHYU3bSwJ7Ws6KkARJq64uPMiQbGxXiUOOqy+2aKlusDtCvsQlBVYsjoraA==", "signatures": [{"sig": "MEYCIQCCSxfmnrdrY0HXohipbGGETKIm27gUcqH5aAiGVACwwgIhAPBRWCbzJleqvBuJNWX4nawWy5CSGUggiBFtBrZu1zZJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.2.7": {"name": "magic-string", "version": "0.2.7", "dependencies": {"vlq": "^0.1.0"}, "devDependencies": {"gobble": "^0.6.10", "source-map": "^0.1.40", "gobble-esperanto-bundle": "^0.1.3"}, "dist": {"shasum": "60f3fc3cf81b16496de2ee817218290b9abfbfb6", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.2.7.tgz", "integrity": "sha512-vlAx0jAVrAqowy37VK05j8BoPtE+d8ef9rd7jcXfeY64xCZMxLL2VjoeFImi+wLbKwwiGoQXMGMKzImMg+MmaA==", "signatures": [{"sig": "MEYCIQCIK9bqIvptBz4yG5EW2s5P1F7oNtEpZ8Gnzgmbhj9IaQIhAIRsalP6A1sokpV2VtsnSayvrJBqookqbmxTXMPkcsa4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.3.0": {"name": "magic-string", "version": "0.3.0", "dependencies": {"vlq": "^0.1.0"}, "devDependencies": {"gobble": "^0.6.10", "source-map": "^0.1.40", "gobble-esperanto-bundle": "^0.1.3"}, "dist": {"shasum": "5abc016a1af2e36b872d39e3eb2c5c52780d4924", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.3.0.tgz", "integrity": "sha512-aD6xIZvm3VBW7v7Sk3/67Iu4BwGHFLwVUltdHnJaTtDnKRrVCcwTyNjl48ss4kaXUKq9pdNrVl4qGxS3AVZB9A==", "signatures": [{"sig": "MEUCIQDLFcAAeJfWcGQ1DXiwLXg5ZhGOkqJtzPR+v9nhjNJDQwIgMblJqP5OgOwRvlqIp+KYOaVGebm6Ye+x/TDkX/rwKVk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.3.1": {"name": "magic-string", "version": "0.3.1", "dependencies": {"vlq": "^0.1.0"}, "devDependencies": {"gobble": "^0.6.10", "source-map": "^0.1.40", "gobble-esperanto-bundle": "^0.1.3"}, "dist": {"shasum": "c18b321a9de17210e1154e0660f95c73b1295886", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.3.1.tgz", "integrity": "sha512-utOIiw8YsBQxg2wiGUf9RuHnrnGeefuBUkHZdNz4rkJVdflBpw5Wyq6i2aBE/3DuacYAjqRmUP5xUaNjFU6bkQ==", "signatures": [{"sig": "MEUCIC+ksXmkgdTD4FwLgc4oXV3DzzDau3DOpDRNCvq+UsljAiEA395GLZy7EcTay0y1Mh6bnCIOAsgyYunUjxqxzP8NU98=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.4.0": {"name": "magic-string", "version": "0.4.0", "dependencies": {"vlq": "^0.2.0"}, "devDependencies": {"gobble": "^0.6.10", "resolve": "^1.1.0", "source-map": "^0.1.40", "es6-promise": "^2.0.1", "gobble-esperanto-bundle": "^0.1.4"}, "dist": {"shasum": "bfacc5481a5a8e61bd2260103baacd3494d01207", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.4.0.tgz", "integrity": "sha512-<PERSON>zey8PZiNWzLw2t9T2iSbtzznnThcNvUEffJM6uz6mSQ/+J+kgUHnXYBux64YXrqmQT7o89KgwqXzHUA3tNryQ==", "signatures": [{"sig": "MEQCIEPlRnVb9+Zyy5mPg5p0pI9VzCpimXm1Z90+4zHEYgvMAiBvuKM/rbcSfxBd7qRRRmaVpgEy6YHOZLxFQNp6Ylj3iA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.4.1": {"name": "magic-string", "version": "0.4.1", "dependencies": {"vlq": "^0.2.0"}, "devDependencies": {"gobble": "^0.6.10", "resolve": "^1.1.0", "source-map": "^0.1.40", "es6-promise": "^2.0.1", "gobble-esperanto-bundle": "^0.1.4"}, "dist": {"shasum": "016fab5ab4650edb8ed4a568822bf08e94043039", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.4.1.tgz", "integrity": "sha512-jjHd1nvwKZFBOkasdaQnxRN2LgGg0m8YuIPafj5MOc0nm6yarEzupBYyW9HDzh4tfZEOpjZZhc7V3q9y/HWuIA==", "signatures": [{"sig": "MEUCIQDV+TPCV8eOcLYbMA4ftCBTM1Jgnl72gIlND5o+AeTuqgIgB2Ne3wASRVvppvHwX5jrWj2tzmN2h5vPb17TXrOQs5s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.4.2": {"name": "magic-string", "version": "0.4.2", "dependencies": {"vlq": "^0.2.0"}, "devDependencies": {"gobble": "^0.6.10", "resolve": "^1.1.0", "source-map": "^0.1.40", "es6-promise": "^2.0.1", "gobble-esperanto-bundle": "^0.1.4"}, "dist": {"shasum": "6c01090f23cafe3e6d723a3619050f1003289f33", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.4.2.tgz", "integrity": "sha512-GI8GYq5kuGRzeVrkdQgQZOsvn5hQhkZ+KFV1dfU+PlVts1cryQY/lTqnuheMw5Pt1Vls44gZ63+thL7kJUkXww==", "signatures": [{"sig": "MEYCIQCsAHlcQmTwMfRTuykN3bMnBmK6fqv5fn9nvCIqcXnBjwIhAOQltseZxB9oyowi1JooXkoFOqeiow3errXlii+UNftk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.4.3": {"name": "magic-string", "version": "0.4.3", "dependencies": {"vlq": "^0.2.0"}, "devDependencies": {"mocha": "^2.1.0", "gobble": "^0.6.10", "resolve": "^1.1.0", "source-map": "^0.1.40", "es6-promise": "^2.0.1", "gobble-esperanto-bundle": "^0.1.4"}, "dist": {"shasum": "e2e942e6947e4580ac6dc76a6e6e2763e6cec87a", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.4.3.tgz", "integrity": "sha512-nUG65LNZAJbnbFMO9+LHLroH9/4w2UPCgYTl3uUBzuXgXP8pkV5VwKGFJmmfvK8mhsWuFigHCRYwg5Wx0QUXig==", "signatures": [{"sig": "MEQCIEOvIKKg4gY9azjvNDm+rZf0r95EV0EFTnugg62CBFqgAiBm7pU5Z/KDt14rVw9B7J9/DP3+BiOMKx++SqHYV6PcRA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.4.4": {"name": "magic-string", "version": "0.4.4", "dependencies": {"vlq": "^0.2.0"}, "devDependencies": {"mocha": "^2.1.0", "gobble": "^0.6.10", "resolve": "^1.1.0", "source-map": "^0.1.40", "es6-promise": "^2.0.1", "gobble-esperanto-bundle": "^0.1.4"}, "dist": {"shasum": "29af535a90ad03ee3acb933399f436ec9739f636", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.4.4.tgz", "integrity": "sha512-Zl2r0QcGIARB/LCYavUQPGeS9ju57ZCJpTPD142M7t1W48Iwyjwhb5Zf1hhF1Nn9rdFLo3c5Yg0DYjhQbZVBUQ==", "signatures": [{"sig": "MEQCIHLC/Fu/m4nAV1lEfvvmfOGEc+7nWIOLDi+Dgh5yrFDzAiAHMFLn7SrP/e104lItJgeBBqIH8qT9eHPPVqAVf3/nhA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.4.5": {"name": "magic-string", "version": "0.4.5", "dependencies": {"vlq": "^0.2.0"}, "devDependencies": {"mocha": "^2.1.0", "gobble": "^0.6.10", "resolve": "^1.1.0", "source-map": "^0.1.40", "es6-promise": "^2.0.1", "gobble-esperanto-bundle": "^0.1.4"}, "dist": {"shasum": "0d7fec1ebc5b7c17b47a8a82186319cc69121db6", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.4.5.tgz", "integrity": "sha512-DyfVXVINLT+kAEmZHLQChBAjNkz3akRseIKfIv6zfNf/Nf8rD2WxIQMhJrcWIJa/4GxqXXpJpG2LyP/jrndsVA==", "signatures": [{"sig": "MEQCICJBX+W9l3WextV9QkNrNM3C393YOw19zXNe+TUMqQjtAiBvEglOvMfscTxpu8AxXkTetle9TX7YSDSthgHqDboNaQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.4.6": {"name": "magic-string", "version": "0.4.6", "dependencies": {"vlq": "^0.2.0"}, "devDependencies": {"mocha": "^2.1.0", "gobble": "^0.6.10", "resolve": "^1.1.0", "source-map": "^0.1.40", "es6-promise": "^2.0.1", "gobble-esperanto-bundle": "^0.1.4"}, "dist": {"shasum": "7e01ac73c24ef34f724ab7893db64d217341b3bc", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.4.6.tgz", "integrity": "sha512-GENvfwtB1Vl2yfaJADo7f+dnmfzkhfmiDpowsb6ATToVmvxHKRflVlzPuzEjQarjSfROaVKwW92Q235IyVbqBA==", "signatures": [{"sig": "MEQCIAKgChroTuztKNB+sCU5t63pRCGwJP4ApVHhTTgH3NvIAiBUFkOOIA+SfnLSfXiwPEsUdyri/kT1CzDwIjePZOGN/w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.4.7": {"name": "magic-string", "version": "0.4.7", "dependencies": {"vlq": "^0.2.0"}, "devDependencies": {"mocha": "^2.1.0", "gobble": "^0.7.10", "resolve": "^1.1.0", "source-map": "^0.1.40", "es6-promise": "^2.0.1", "gobble-esperanto-bundle": "^0.1.7"}, "dist": {"shasum": "76dea11561e8ad1d3c118c50a1dda9c6e499e1e7", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.4.7.tgz", "integrity": "sha512-Lc0UqcKnZ26yzf1MpKezr0F7PhUX5OJK1g0bZwQX1M6jLmeizlbCZgj9rIsVhTnY+Y12Ei3JGdwshrwXfohVnQ==", "signatures": [{"sig": "MEUCIESdZ7EldhzhZWd47TBbZlhzVhWC3fG7NHcuY0Q4LrTDAiEAi2e8kRs5r78EaTOXY75tPcpJhpu3Y3K6ZM5pv/lBMdM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.4.8": {"name": "magic-string", "version": "0.4.8", "dependencies": {"vlq": "^0.2.0"}, "devDependencies": {"mocha": "^2.1.0", "gobble": "^0.7.10", "resolve": "^1.1.0", "source-map": "^0.1.40", "es6-promise": "^2.0.1", "gobble-esperanto-bundle": "^0.1.7"}, "dist": {"shasum": "d603df973271f97ebbaf15a25e87ff799bdb11f4", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.4.8.tgz", "integrity": "sha512-B+K+mG1r67wUbhX+iHYt2fRp2b5U+hYfq4XzW+Hut00QN7Zs6SaloBQUh3+DrTIMA2oAyC8JdG/fCxHim1OZzg==", "signatures": [{"sig": "MEUCIBKGc/f5kR7zD/LiDbAT45GsFd16BpYMks24HxfIjHU1AiEA83QaszrJGTSL8IhBpBHpqvQ3Yk63q4zceGAW3Hvg69c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.4.9": {"name": "magic-string", "version": "0.4.9", "dependencies": {"vlq": "^0.2.0"}, "devDependencies": {"mocha": "^2.1.0", "gobble": "^0.7.10", "resolve": "^1.1.0", "source-map": "^0.1.40", "es6-promise": "^2.0.1", "gobble-esperanto-bundle": "^0.1.7"}, "dist": {"shasum": "f46e9aaf5959cfbbc88f68a9969cd66d120e1a22", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.4.9.tgz", "integrity": "sha512-4+xFWOeaQsQHXHiA8B1xflbdOYNxYS1Ap7uU+F1LLgVoTdS6mQwFguOYBrqgdLydeAZGqNPcoq5HxTVKpnfRbA==", "signatures": [{"sig": "MEYCIQDDOFKZtUgwpvg4mv4XdZ03Lk7s5+2HFsJ4tOLyM+bdaQIhALyk345F4octAqLQVfDtXptwT9CbcigWD9k685vQVnc3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.5.0": {"name": "magic-string", "version": "0.5.0", "dependencies": {"vlq": "^0.2.0"}, "devDependencies": {"mocha": "^2.1.0", "gobble": "^0.10.1", "resolve": "^1.1.0", "source-map": "^0.1.40", "es6-promise": "^2.0.1", "gobble-babel": "^5.1.0", "gobble-esperanto-bundle": "^0.2.0"}, "dist": {"shasum": "5cc8991270b765a2a4aaaf667ad14bc7e94fbd7b", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.5.0.tgz", "integrity": "sha512-CQHS+LB82teHiw7Lupp+r1D9RuybUmmfWd/ZyrOBmcik8ByUE2YAkI+2nRdCuLD1b8OJx9RYfwlSSE6us/xF1w==", "signatures": [{"sig": "MEUCIQDkWDgA7rpiDqIbaAzBeDpnkpVVbcwhm13ushF4nWOqNQIgHjdTlkj8byFSznlHM8jaJwlfQE/YL/Gf0p3R/FXkK3g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.5.1": {"name": "magic-string", "version": "0.5.1", "dependencies": {"vlq": "^0.2.0"}, "devDependencies": {"mocha": "^2.1.0", "gobble": "^0.10.1", "resolve": "^1.1.0", "source-map": "^0.1.40", "es6-promise": "^2.0.1", "gobble-babel": "^5.1.0", "gobble-esperanto-bundle": "^0.2.0"}, "dist": {"shasum": "d8696879af84f46d0fb7e74ba13609e941d9e230", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.5.1.tgz", "integrity": "sha512-tVDVwxwb66qn9Wx6W31GuQSKkobBxlMMAH6XjW3tChbatjnV7yknVOXcz00aafPXvP+4w/g93OGhhtXGWZED7g==", "signatures": [{"sig": "MEYCIQD376nXsjv14pF+l+nal90WqdV6DLUaY0S37Sya0MkbzQIhALMPKO5ll20cI2t5VqtklxQnSTYn/KXgZUJGS6hyviE7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.5.3": {"name": "magic-string", "version": "0.5.3", "dependencies": {"vlq": "^0.2.0"}, "devDependencies": {"mocha": "^2.1.0", "gobble": "^0.10.1", "resolve": "^1.1.0", "source-map": "^0.1.40", "es6-promise": "^2.0.1", "gobble-babel": "^5.1.0", "gobble-esperanto-bundle": "^0.2.0"}, "dist": {"shasum": "c377f91b7a2680e2c3d566996cadd597a572479a", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.5.3.tgz", "integrity": "sha512-UILQsYS7Gt5AdgPnEWU4lcY/sKaKZLc87XVEl3kqRTqfI8OSwNMNWaDnrWZYGvdxws5BBCv7Ef/SeHIpzZ/dWQ==", "signatures": [{"sig": "MEQCIALD5vAttPdJezFcjRQGNgvNcH1jzNWkF4tJgUAxJHW0AiAHSnxOfq7NjzZcMd681emI/HDDPbW3IRUMYX+9ennNJQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.6.0": {"name": "magic-string", "version": "0.6.0", "dependencies": {"vlq": "^0.2.0"}, "devDependencies": {"mocha": "^2.1.0", "gobble": "^0.10.1", "resolve": "^1.1.0", "source-map": "^0.1.40", "es6-promise": "^2.0.1", "gobble-babel": "^5.1.0", "gobble-rollup": "^0.2.0"}, "dist": {"shasum": "f00e06174759bfd4dc82430e2fa2fe667c3f4864", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.6.0.tgz", "integrity": "sha512-x+p5NNrOVBZtt2jOGoJIjVofeh6dC23G9evrB9Y+mV3IUAkxFjZjIZKucdMw7LoqFvwQ05q0/5UeyKzX5xTNTQ==", "signatures": [{"sig": "MEUCIE/1Fbj1ZUqrshuZFHCyITEIqajBNk/CrDl/N5DAWo2NAiEAuKboP080RGPnCPKnrLTiH+IpXM1lJoUkgAwL4JANo5E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.6.1": {"name": "magic-string", "version": "0.6.1", "dependencies": {"vlq": "^0.2.0"}, "devDependencies": {"mocha": "^2.1.0", "gobble": "^0.10.1", "resolve": "^1.1.0", "source-map": "^0.1.40", "es6-promise": "^2.0.1", "gobble-babel": "^5.1.0", "gobble-rollup": "^0.2.0"}, "dist": {"shasum": "4dd285a695f607c2019570b6ca6df028ff2baf46", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.6.1.tgz", "integrity": "sha512-K6nkAkxVjVKboDrpC4McGhdSMifUNcv2IsQ6raLzvVcoZ9WXrVOZt73GSEXZX6ExaIjiP+hL/T7dCuDXu/GJSA==", "signatures": [{"sig": "MEQCID+RKSfMA7WoEi6SaT4FVyf3s5IVA1AEPWfHm+fjpv1hAiBL0YLlb/y6nDkGIjc/1GYidgVXfCb5ZcQIae/wjBRzgA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.6.2": {"name": "magic-string", "version": "0.6.2", "dependencies": {"vlq": "^0.2.0"}, "devDependencies": {"mocha": "^2.1.0", "gobble": "^0.10.1", "resolve": "^1.1.0", "source-map": "^0.1.40", "es6-promise": "^2.0.1", "gobble-babel": "^5.1.0", "gobble-rollup": "^0.2.0"}, "dist": {"shasum": "ea0783d29b284e3a9d17fdcdf76ad695fdfa458e", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.6.2.tgz", "integrity": "sha512-K3Z0XFPXHZy2KazshebTFXYoSDyjvNGj8/oVXahHAIhvMvgE//gLbpGCxvKC4rjnm+KNbufjGXGm0QGQeMYWAg==", "signatures": [{"sig": "MEUCIQDLrTw/v+NRXy+V3XdIMVTl6hT0I4EvYW5f9jKumPIvFgIgQrKZMSNYDd1KyRoBCoxFwMVhnjt0RMZUKX81jaPM1xY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.6.3": {"name": "magic-string", "version": "0.6.3", "dependencies": {"vlq": "^0.2.0"}, "devDependencies": {"mocha": "^2.1.0", "gobble": "^0.10.1", "resolve": "^1.1.0", "source-map": "^0.1.40", "es6-promise": "^2.0.1", "gobble-babel": "^5.1.0", "gobble-rollup": "^0.3.0"}, "dist": {"shasum": "be1a5d16cdab51d136f77911b83021a2010807cd", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.6.3.tgz", "integrity": "sha512-I+ix2CM0zQ4cYzwRy8nYuQc79+nvv4/QL3UREdz63vu+xVgX4fzKiF8o5DU/5hR/JrKuZT8PZ0mJFw/JYF3U5g==", "signatures": [{"sig": "MEUCICHRUwNaoAvOA49uSgCtEUmiGZ4AMzMLpzTKAooHcWb3AiEAizI2uDL6dJWB4GXavpDTxbLGa9OPmZn/0YQcRnCSaiA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.6.4": {"name": "magic-string", "version": "0.6.4", "dependencies": {"vlq": "^0.2.0"}, "devDependencies": {"mocha": "^2.1.0", "gobble": "^0.10.1", "resolve": "^1.1.0", "source-map": "^0.1.40", "es6-promise": "^2.0.1", "gobble-babel": "^5.1.0", "gobble-rollup": "^0.3.0"}, "dist": {"shasum": "f69208648d1477c3d23e5f1b47ffc3993c768450", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.6.4.tgz", "integrity": "sha512-yAxH+rTmp6UNsRBUmUZYnyPGQYNaL/TD15T79KB7R2s/dxeKYVKDokJZYWgP1ff9iWmieWD/tk6lUXe5sUbFrQ==", "signatures": [{"sig": "MEQCID7ILDuNNWgBaJ9SOCh0uitoZUIQLrBehlfS72thzcO6AiABLnYfpoRD1CVVb2UOz1y2LuZo87kxTW/ndwqUgfgpxQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.6.5": {"name": "magic-string", "version": "0.6.5", "dependencies": {"vlq": "^0.2.0"}, "devDependencies": {"mocha": "^2.1.0", "gobble": "^0.10.1", "resolve": "^1.1.0", "source-map": "^0.1.40", "es6-promise": "^2.0.1", "gobble-babel": "^5.1.0", "gobble-rollup": "^0.3.0"}, "dist": {"shasum": "07d513ffad5736bab56f4cdd2e2a93dcc90cc9a4", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.6.5.tgz", "integrity": "sha512-nVfaivBWGTBKB5tXK3BaT/ToV4KrEfROHVGO73YRljxjH22d34qX6PtWe+gOX26oI9l82soxQr0yYRb5YoJTng==", "signatures": [{"sig": "MEYCIQDPbjneb+3MVnWjc/jefB1gvjMmXfflI06XQlgX/khqhwIhAMiglCnJz90GLTDSNNV3n+cG2WNN0A5KMqKII7OOg7W6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.6.6": {"name": "magic-string", "version": "0.6.6", "dependencies": {"vlq": "^0.2.0"}, "devDependencies": {"mocha": "^2.1.0", "gobble": "^0.10.1", "resolve": "^1.1.0", "source-map": "^0.1.40", "es6-promise": "^2.0.1", "gobble-babel": "^5.1.0", "gobble-rollup": "^0.3.0"}, "dist": {"shasum": "35b5788604ec27f1774132deb3806d2369f8c36c", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.6.6.tgz", "integrity": "sha512-XkkIybiqFympvl/GIui42rB5Mza6EUkmOL799/2OnSCcdSs75shp3dLy66MEbnWRQ/G3fz2gSDwhq4DyYpCq4A==", "signatures": [{"sig": "MEYCIQCSwb2AxfCCg8AE0dkZhyGUgpWo5SLJy1amI21DC3favwIhAJA6DPbomqUEPKd2ECX2spdOae1LZbr9LuXM7faNOasJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.7.0": {"name": "magic-string", "version": "0.7.0", "dependencies": {"vlq": "^0.2.0"}, "devDependencies": {"mocha": "^2.1.0", "gobble": "^0.10.1", "resolve": "^1.1.0", "gobble-cli": "^0.4.2", "source-map": "^0.1.40", "es6-promise": "^2.0.1", "gobble-babel": "^5.1.0", "gobble-rollup": "^0.7.0"}, "dist": {"shasum": "7a244c8ab985d4b37a5ea36bbcdc2afc6b299e65", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.7.0.tgz", "integrity": "sha512-ZtCJ8dbIpTK2pXkg2Xm/vU0H6ldN8CGg6U/arwWHS1WNVslnMBRA2g9xdAZLmN7i1XcDsJvZhIK+5zhdc9mzfQ==", "signatures": [{"sig": "MEYCIQCB+MnCg3iNZlVGTH5j3ExojPm5C1tnuUo98JBAHffbmgIhAMX2JVxQ8lG551odYK1ISUZk9F6e4dZlQuhQEvEnvCii", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.8.0": {"name": "magic-string", "version": "0.8.0", "dependencies": {"vlq": "^0.2.0"}, "devDependencies": {"mocha": "^2.1.0", "eslint": "^1.5.1", "gobble": "^0.10.1", "resolve": "^1.1.0", "istanbul": "^0.4.0", "codecov.io": "^0.1.6", "gobble-cli": "^0.6.0", "source-map": "^0.5.1", "es6-promise": "^3.0.2", "remap-istanbul": "^0.3.0", "gobble-rollup-babel": "^0.6.1"}, "dist": {"shasum": "cf0c0636093b9a0fdc8ecdfbd0dff4d365c7c763", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.8.0.tgz", "integrity": "sha512-1KQAfZT/eoj4RSRWCT3tdlfrUMiFHWypzLyQ5osBwfnB7I3jSgNJNuhDEEeyTYvlV6o/u6VsQDwDEMWh9SMmWQ==", "signatures": [{"sig": "MEQCIGSkkRSa5iRdaKJVyVp291p9Q9oeyVxk0vQobfEwvYRsAiAHG66UMzx5pXhDdwqX4UIEO34Fb3pjqqnRIPCbnuKVLg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.9.0": {"name": "magic-string", "version": "0.9.0", "dependencies": {"vlq": "^0.2.0"}, "devDependencies": {"mocha": "^2.1.0", "eslint": "^1.5.1", "rollup": "^0.21.2", "resolve": "^1.1.0", "istanbul": "^0.4.0", "codecov.io": "^0.1.6", "source-map": "^0.5.1", "es6-promise": "^3.0.2", "remap-istanbul": "^0.4.0", "rollup-plugin-npm": "^1.1.0", "rollup-plugin-babel": "^2.2.0", "babel-preset-es2015-rollup": "^1.0.0"}, "dist": {"shasum": "eb27fe5c387b8b605b3465aaa62cf5cb29831e5a", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.9.0.tgz", "integrity": "sha512-L5B0pMHGLFWmM809qQ7gHKkMSd2y8eYRwQkAjnzzyR6TxfvBt3Z8pU9u4VdZ9U0b/KbkJFeKsNdLJ9zn3rPEXg==", "signatures": [{"sig": "MEUCIQC/SuCoialSPjg4LHNiT3s3Hjchfwth6nr4DtUP08JQVgIgGx8b709uNONquIFEy9Y/4naZ4g1Syszy56+IDjXf3R4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.9.1": {"name": "magic-string", "version": "0.9.1", "dependencies": {"vlq": "^0.2.0"}, "devDependencies": {"mocha": "^2.1.0", "eslint": "^1.10.3", "rollup": "^0.21.2", "resolve": "^1.1.0", "istanbul": "^0.4.0", "codecov.io": "^0.1.6", "source-map": "^0.5.3", "es6-promise": "^3.0.2", "remap-istanbul": "^0.5.1", "rollup-plugin-npm": "^1.1.0", "rollup-plugin-babel": "^2.2.0", "babel-preset-es2015-rollup": "^1.0.0"}, "dist": {"shasum": "bc3e2ada28be59c65cd28dac47ba6527b96c8fa8", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.9.1.tgz", "integrity": "sha512-B<PERSON>CHvzVPOQR6YFa81HPtLhM9C9FKhyRPId5vFNF73YbgOnUioaUVcug691bpJIxbgLFAcYjnKTNr4MoZN7CZAw==", "signatures": [{"sig": "MEQCIGqsCsKcpKI52Zd8HjhruOdotu0yGdl51mV/uXsH1pnaAiBli9OE0jQPf+ZOvjU6DgEAfrff3nV/Asj5AwlP24npLw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.10.0": {"name": "magic-string", "version": "0.10.0", "dependencies": {"vlq": "^0.2.0"}, "devDependencies": {"mocha": "^2.1.0", "eslint": "^1.10.3", "rollup": "^0.22.0", "resolve": "^1.1.0", "istanbul": "^0.4.0", "codecov.io": "^0.1.6", "source-map": "^0.5.3", "es6-promise": "^3.0.2", "console-group": "^0.1.2", "remap-istanbul": "^0.5.1", "rollup-plugin-npm": "^1.1.0", "source-map-support": "^0.4.0", "rollup-plugin-babel": "^2.2.0", "babel-preset-es2015-rollup": "^1.0.0"}, "dist": {"shasum": "91d9678bd6461448abfcd19376f8903a80b04187", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.10.0.tgz", "integrity": "sha512-sOHD7csdcHzkIhEgtV+TowoBppzLTYMoAYDtUxzI0uU39HzafOlXrfyp2pZiD62qVgoiI+2jnfH2PUqSVc9qQw==", "signatures": [{"sig": "MEUCIQC47NT14O8S+OA8/DiUHUMLxBFPpPFtbXuZRYHm+ls4uwIgcsJ243b2FdsjlXbcG8S0B6p5vTf2EDhObsaMNR5oYqA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.10.1": {"name": "magic-string", "version": "0.10.1", "dependencies": {"vlq": "^0.2.0"}, "devDependencies": {"mocha": "^2.1.0", "eslint": "^1.10.3", "rollup": "^0.22.0", "resolve": "^1.1.0", "istanbul": "^0.4.0", "codecov.io": "^0.1.6", "source-map": "^0.5.3", "es6-promise": "^3.0.2", "console-group": "^0.1.2", "remap-istanbul": "^0.5.1", "rollup-plugin-npm": "^1.1.0", "source-map-support": "^0.4.0", "rollup-plugin-babel": "^2.2.0", "babel-preset-es2015-rollup": "^1.0.0"}, "dist": {"shasum": "5a567c45518429316a8c84b804db7289654d75a0", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.10.1.tgz", "integrity": "sha512-PQOjD/ppYw2LA7fGb76a627F+5YsFY5TE6I8SXKI8XHkKIl4yIoLRQMZFLwNPgHivl2oqNy5TQzMrxuHvvoIJQ==", "signatures": [{"sig": "MEUCIC69GW012scA3NokiyJoOf5bsHWN7XIvlq/JFWoU4+xmAiEAxTaUDx91ilRZCBbOU7kfm5uP/8Fbiej6OgQOF3qruDk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.10.2": {"name": "magic-string", "version": "0.10.2", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^1.10.3", "rollup": "^0.24.0", "resolve": "^1.1.6", "istanbul": "^0.4.1", "codecov.io": "^0.1.6", "source-map": "^0.5.3", "es6-promise": "^3.0.2", "console-group": "^0.2.0", "remap-istanbul": "^0.5.1", "rollup-plugin-npm": "^1.2.0", "source-map-support": "^0.4.0", "rollup-plugin-babel": "^2.3.5", "babel-preset-es2015-rollup": "^1.0.0"}, "dist": {"shasum": "f25f1c3d9e484f0d8ad606d6c2faf404a3b6cf9d", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.10.2.tgz", "integrity": "sha512-Ah6T1JI6cPsMQ/7y2ZqLZ7ssNu9/oH95QvG9RTLbkRAb0OWskxFz1XP2IKzHmS8t91OzrPlMNsjab112Zd1vkg==", "signatures": [{"sig": "MEQCIGjgOz9uDcLHjH0Jtk/oOzgpgMeSADTAxphp+78shAQJAiBkLnHnxKgEl/gVp0tRF1xn2gl1/0rXIFL5eq38NXaMaQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.11.0-alpha": {"name": "magic-string", "version": "0.11.0-alpha", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^2.0.0", "rollup": "^0.25.0", "resolve": "^1.1.6", "istanbul": "^0.4.1", "codecov.io": "^0.1.6", "source-map": "^0.5.3", "es6-promise": "^3.0.2", "console-group": "^0.2.0", "remap-istanbul": "^0.5.1", "rollup-plugin-npm": "^1.2.0", "source-map-support": "^0.4.0", "rollup-plugin-babel": "^2.3.5", "babel-preset-es2015-rollup": "^1.0.0"}, "dist": {"shasum": "236458b2cf7cb1c0000ea526322873b03ad8f661", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.11.0-alpha.tgz", "integrity": "sha512-BNbpItywA0sO29jj5zZLMIjdI+ljB9P63eWJtznKQTHpffk7dXJ+FH3BNpwGKopDk8HgpKLnbBrO/LbTQdYryw==", "signatures": [{"sig": "MEQCICkzaGfaWRqjfciXMpFGgp9uTccMotCooVwwQWheXEyNAiA+QQzGUsDLm7qkNbWm6Cad9fWA/HXWrMxEZ1URRaniOQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.11.0": {"name": "magic-string", "version": "0.11.0", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^2.0.0", "rollup": "^0.25.0", "resolve": "^1.1.6", "istanbul": "^0.4.1", "codecov.io": "^0.1.6", "source-map": "^0.5.3", "es6-promise": "^3.0.2", "console-group": "^0.2.0", "remap-istanbul": "^0.5.1", "rollup-plugin-npm": "^1.2.0", "source-map-support": "^0.4.0", "rollup-plugin-babel": "^2.3.5", "babel-preset-es2015-rollup": "^1.0.0"}, "dist": {"shasum": "630bdcbbaf369aebf36b967c10a3d4c070b0b25c", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.11.0.tgz", "integrity": "sha512-qh5HizwfCljf9x+e2niYU0CaIgqxyF8cGvhcy4hhXjqe/KMYIachtbIKUj8i7HrVDPWtkz+GRN06NtciKdOo8w==", "signatures": [{"sig": "MEQCIETsCa7t6NL+Ly0+2O/PKybLDVqIBSlUehqpIOtw6QxqAiArUnR5T3sordjR5vjHw3+RgYaoAQLCxx8uJ25HUGC6aw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.11.1": {"name": "magic-string", "version": "0.11.1", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^2.0.0", "rollup": "^0.25.0", "resolve": "^1.1.6", "istanbul": "^0.4.1", "codecov.io": "^0.1.6", "source-map": "^0.5.3", "es6-promise": "^3.0.2", "console-group": "^0.2.0", "remap-istanbul": "^0.5.1", "source-map-support": "^0.4.0", "rollup-plugin-buble": "^0.4.0", "rollup-plugin-node-resolve": "^1.5.0"}, "dist": {"shasum": "921d8a65b6f0c7ee803fef4266e15ace2a33bf64", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.11.1.tgz", "integrity": "sha512-vM8qjvw+jooqNdVKJ5+Aq6H6XSgpVYBpbl2EZj6zvxl814ckUU9HCl9ecuDSsGgBASujJVbgB0ytb6vGEL4RoQ==", "signatures": [{"sig": "MEUCIQDhzIMnufjay5h490d2+FoTNrnfu3wkJAGBiAtVJ5UFdAIgKpL2+dUWQ/rZKej95X9a2zxJIubF352vZO1t66uUOao=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.11.2": {"name": "magic-string", "version": "0.11.2", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^2.0.0", "rollup": "^0.25.0", "resolve": "^1.1.6", "istanbul": "^0.4.1", "codecov.io": "^0.1.6", "source-map": "^0.5.3", "es6-promise": "^3.0.2", "console-group": "^0.2.0", "remap-istanbul": "^0.5.1", "source-map-support": "^0.4.0", "rollup-plugin-buble": "^0.4.0", "rollup-plugin-node-resolve": "^1.5.0"}, "dist": {"shasum": "28918f2c3325e6ba8234ed39503356cb02fb1316", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.11.2.tgz", "integrity": "sha512-ZiDAkdrHmKNLdSVknkj8LOramcS1UTW02ZZSg45xuWHrT0AHhhVBG6l/UlMkZSHhunW0FgM6tfFtflwgU3go1g==", "signatures": [{"sig": "MEUCIHfLU4AkEaYp8qlNyxdR6WtCEfVnrRKM0Gvq3GCRPIX5AiEA52Yhs1RYFhDvkqpKEjNjcRC438+HrmuZDPetq9ySR5c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.11.3": {"name": "magic-string", "version": "0.11.3", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^2.0.0", "rollup": "^0.25.0", "resolve": "^1.1.6", "istanbul": "^0.4.1", "codecov.io": "^0.1.6", "source-map": "^0.5.3", "es6-promise": "^3.0.2", "console-group": "^0.2.0", "remap-istanbul": "^0.5.1", "source-map-support": "^0.4.0", "rollup-plugin-buble": "^0.4.0", "rollup-plugin-node-resolve": "^1.5.0"}, "dist": {"shasum": "8d39a02fa078c4ec267a0eff6dbc0a5a27e7cc18", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.11.3.tgz", "integrity": "sha512-ZaBFmnsCJSOA0sobd3w3KAgGjJK74UBc6dPsbwJgslbNVM+pZzNOCIDmhhCARKEmrc7tK7q1L1lZpv+mkEbwBg==", "signatures": [{"sig": "MEUCIQCaIp7iTuAEzCCkCuux9zh1lRU7/IzPTfalnylIPSWSlAIgU14+tcl6CH03eJMlBE39fScwhUB3/WJI3phM/kYrUAE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.11.4": {"name": "magic-string", "version": "0.11.4", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^2.0.0", "rollup": "^0.25.0", "resolve": "^1.1.6", "istanbul": "^0.4.1", "codecov.io": "^0.1.6", "source-map": "^0.5.3", "es6-promise": "^3.0.2", "console-group": "^0.2.0", "remap-istanbul": "^0.5.1", "source-map-support": "^0.4.0", "rollup-plugin-buble": "^0.4.0", "rollup-plugin-node-resolve": "^1.5.0"}, "dist": {"shasum": "272646332541760c1a4dbf2ccbbd4083fa42f690", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.11.4.tgz", "integrity": "sha512-wRR5md5KnsIMe43bxw0k2fCTSmrn5c7npGzwOwkgXF/qjRxc9f+Bz7Aqg6wf0AU4TL6/oTkT5hsblEq5ZV+YHQ==", "signatures": [{"sig": "MEUCIEbxu56JUnQako2h4Upxvfm5sSn4lSLW4vTx5GhLLvNcAiEAu/NrgWxNmu3ulJWhe90sr99qs1p5LNlkNp5bRGc3/U0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.12.0": {"name": "magic-string", "version": "0.12.0", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^2.0.0", "rollup": "^0.26.0", "resolve": "^1.1.6", "istanbul": "^0.4.1", "codecov.io": "^0.1.6", "source-map": "^0.5.3", "es6-promise": "^3.0.2", "console-group": "^0.2.0", "remap-istanbul": "^0.6.1", "source-map-support": "^0.4.0", "rollup-plugin-buble": "^0.6.0", "rollup-plugin-node-resolve": "^1.5.0"}, "dist": {"shasum": "95acfbcdc052e0fb5c1d7056495b12f985b2c434", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.12.0.tgz", "integrity": "sha512-cZ+I5MK4WHDGFuCChD38XHR8+bipnKGBuNwuh9OO9CvrkZb98+sYsyY9AkYcsbrNCZVGHTf0Ujo2SJ8pfHO5Pg==", "signatures": [{"sig": "MEQCIBZR4aYXdINzDVrIxjfx/KbNPwbzlSoALMWqKby2XpUZAiBBQgmhtwiiC5PgO1jcTB44lN1YUdZ+s/ToVsqnpNBlwA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.12.1": {"name": "magic-string", "version": "0.12.1", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^2.0.0", "rollup": "^0.26.0", "resolve": "^1.1.6", "istanbul": "^0.4.1", "codecov.io": "^0.1.6", "source-map": "^0.5.3", "es6-promise": "^3.0.2", "console-group": "^0.2.0", "remap-istanbul": "^0.6.1", "source-map-support": "^0.4.0", "rollup-plugin-buble": "^0.6.0", "rollup-plugin-node-resolve": "^1.5.0"}, "dist": {"shasum": "bc41764d11c41ec5eca048747691f0ea06dd6855", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.12.1.tgz", "integrity": "sha512-40AfENvb/vRIIy95zoXA21Lm85ydr3/IGyGd+Rw5Sc0H8H2PIhcCjZ66P47+Ni8BPFnolqOwwVWzNPif6F+qvA==", "signatures": [{"sig": "MEQCIQCjtgbJNAPDY0/gY0qWwbbgA0EGeJLr6pxpaSnlGSGywQIfUCjjLPBiAbGnQ1Hhs/RA7K7wuuhKj7iwjrCQ5zZjQA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.13.0": {"name": "magic-string", "version": "0.13.0", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^2.0.0", "rollup": "^0.26.0", "resolve": "^1.1.6", "istanbul": "^0.4.1", "codecov.io": "^0.1.6", "source-map": "^0.5.3", "es6-promise": "^3.0.2", "console-group": "^0.2.0", "remap-istanbul": "^0.6.1", "source-map-support": "^0.4.0", "rollup-plugin-buble": "^0.7.0", "rollup-plugin-replace": "^1.1.0", "rollup-plugin-node-resolve": "^1.5.0"}, "dist": {"shasum": "eec260cff4918c1b5b09058271e7be5f8f6faed1", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.13.0.tgz", "integrity": "sha512-NP0/v3ExfMHRrHqHX3GvNKCFlPk0xQVIjeu1XOwj1+FC2FQ/GWXJFBHmtBCxTUQe8N2SNvdXNs8sJ/GJ5++JCw==", "signatures": [{"sig": "MEUCIQCgXPtpe1bmY53vFBQrhr5KzpFASY1u1Xdf/6VompMtsgIgBr5cmLJZUOAbATYfSW/9nt0NpbCL8v8BYVgCECUuJK4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.13.1": {"name": "magic-string", "version": "0.13.1", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^2.0.0", "rollup": "^0.26.0", "resolve": "^1.1.6", "istanbul": "^0.4.1", "codecov.io": "^0.1.6", "source-map": "^0.5.3", "es6-promise": "^3.0.2", "console-group": "^0.2.0", "remap-istanbul": "^0.6.1", "source-map-support": "^0.4.0", "rollup-plugin-buble": "^0.7.0", "rollup-plugin-replace": "^1.1.0", "rollup-plugin-node-resolve": "^1.5.0"}, "dist": {"shasum": "404b98c951619a774a2c92570c0d3b1e66779e3e", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.13.1.tgz", "integrity": "sha512-w6NUKQC5MLSdWjZSEKGbwItsa41Rk6YLMcglbaWz0NA1Zj8Q/1UDHBayQYXYOYJ1qiN0rqBgHR0fe2Fr9I6HYw==", "signatures": [{"sig": "MEYCIQDCzPM5yBC33LpolB4bTQJ1+Ya5MbnFurkdgrmKbbwcWQIhAIcodDihIV6obgS4F1OnuAOknfRWS9zu9ZKcZL8UHIvX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.14.0": {"name": "magic-string", "version": "0.14.0", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^2.0.0", "rollup": "^0.26.0", "resolve": "^1.1.6", "istanbul": "^0.4.1", "codecov.io": "^0.1.6", "source-map": "^0.5.3", "es6-promise": "^3.0.2", "console-group": "^0.2.0", "remap-istanbul": "^0.6.1", "source-map-support": "^0.4.0", "rollup-plugin-buble": "^0.7.0", "rollup-plugin-replace": "^1.1.0", "rollup-plugin-node-resolve": "^1.5.0"}, "dist": {"shasum": "57224aef1701caeed273b17a39a956e72b172462", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.14.0.tgz", "integrity": "sha512-ASteqiQbpCPx2uMF5NkmrIUlo3nsSDcPOo+O+F+pdPML/IS560BwrEljpzDFOR45eOME7UPTxgUQVPs6Lj2mTw==", "signatures": [{"sig": "MEYCIQDAOxO7ZA8UfLCywN5k+EqHMaLPgmnWgZhFxOwVnGrsWAIhANJNL11gVVa2rbcGjB3ITJrcKRtkZXf8IWdIdxhWZenK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.15.0": {"name": "magic-string", "version": "0.15.0", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^2.0.0", "rollup": "^0.26.0", "resolve": "^1.1.6", "istanbul": "^0.4.1", "codecov.io": "^0.1.6", "source-map": "^0.5.3", "es6-promise": "^3.0.2", "console-group": "^0.2.0", "remap-istanbul": "^0.6.1", "source-map-support": "^0.4.0", "rollup-plugin-buble": "^0.7.0", "rollup-plugin-replace": "^1.1.0", "rollup-plugin-node-resolve": "^1.5.0"}, "dist": {"shasum": "4ae8a04a4f70a335ff17854a4b1bc020ef064fbd", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.15.0.tgz", "integrity": "sha512-NFBH1Txkw12g2VEvKvXVUwYpa8SCJiwDOk61bARgALW7GRgngHL8GEMrZo1OEqt9kGjMPqBfuJ3Dr3lYiK/tzg==", "signatures": [{"sig": "MEUCIQDzBEgjZj0fag4mSjwmaGfB6LaaWboEzp4qLZWJ37lxUAIgRQneCCTXci03F4P463ZvKxbUlSQqwgvCAM9b31fL5R4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.15.1": {"name": "magic-string", "version": "0.15.1", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"mocha": "^2.5.3", "eslint": "^2.11.1", "rollup": "^0.29.0", "resolve": "^1.1.7", "istanbul": "^0.4.3", "codecov.io": "^0.1.6", "source-map": "^0.5.6", "console-group": "^0.2.1", "remap-istanbul": "^0.6.4", "source-map-support": "^0.4.0", "rollup-plugin-buble": "^0.10.0", "rollup-plugin-replace": "^1.1.0", "rollup-plugin-node-resolve": "^1.7.0"}, "dist": {"shasum": "8390de9ea037897309d95eb345bd55011e28f62a", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.15.1.tgz", "integrity": "sha512-fdsRmKHuncKCiXsnAgi7pO30DQxU1QWAGnPLdGSl5g52cFO/OPZnVOUhSukPWeUknq3rbZjIgqBujVTy/ZT9gw==", "signatures": [{"sig": "MEQCIEDiDRdrX3zZ0XKnVYmLx58Tynh3rlpRLWjyRPzF+1bXAiBMUugLKGXP9JKb1DQlAVYiK7x2XuNovBSVltMAb7Oidg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.15.2": {"name": "magic-string", "version": "0.15.2", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"mocha": "^2.5.3", "eslint": "^2.11.1", "rollup": "^0.31.0", "resolve": "^1.1.7", "istanbul": "^0.4.3", "codecov.io": "^0.1.6", "source-map": "^0.5.6", "console-group": "^0.2.1", "remap-istanbul": "^0.6.4", "source-map-support": "^0.4.0", "rollup-plugin-buble": "^0.10.0", "rollup-plugin-replace": "^1.1.0", "rollup-plugin-node-resolve": "^1.7.0"}, "dist": {"shasum": "0681d7388741bbc3addaa65060992624c6c09e9c", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.15.2.tgz", "integrity": "sha512-xkHb690SyIbvr1x6PiHoP2M2rTkIt9La8gZLjoXIjcm7CI0a+9V9JqQWVQPsBLgt9qzkgtCuYN+/Thqw6crg6w==", "signatures": [{"sig": "MEYCIQD/I7/ynlKoorDvcR+KzVGG56913w46y5KlIOW4krC/GAIhAKOSIm95ppRzjWmbXOOxGdI/0PsbUL7LyZKDRtCF30vi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.16.0": {"name": "magic-string", "version": "0.16.0", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"mocha": "^3.0.1", "eslint": "^2.11.1", "rollup": "^0.34.5", "resolve": "^1.1.7", "istanbul": "^0.4.3", "codecov.io": "^0.1.6", "source-map": "^0.5.6", "console-group": "^0.2.1", "remap-istanbul": "^0.6.4", "source-map-support": "^0.4.0", "rollup-plugin-buble": "^0.12.1", "rollup-plugin-replace": "^1.1.0", "rollup-plugin-node-resolve": "^2.0.0"}, "dist": {"shasum": "970ebb0da7193301285fb1aa650f39bdd81eb45a", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.16.0.tgz", "integrity": "sha512-c4BEos3y6G2qO0B9X7K0FVLOPT9uGrjYwYRLFmDqyl5YMboUviyecnXWp94fJTSMwPw2/sf+CEYt5AGpmklkkQ==", "signatures": [{"sig": "MEQCIBvKb7tKH9DRgE0NPDdA9j7khOUzCruoGz2XFMwiMXoDAiAj6LEbDfAxgjRWmO2tSAG0uqS2yye+CzN/f1N8p3uimg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.17.0": {"name": "magic-string", "version": "0.17.0", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"buble": "^0.14.0", "mocha": "^3.1.0", "eslint": "^3.7.1", "rollup": "^0.36.1", "resolve": "^1.1.7", "istanbul": "^0.4.5", "codecov.io": "^0.1.6", "source-map": "^0.5.6", "console-group": "^0.3.2", "remap-istanbul": "^0.6.4", "source-map-support": "^0.4.3", "rollup-plugin-buble": "^0.14.0", "rollup-plugin-replace": "^1.1.1", "rollup-plugin-node-resolve": "^2.0.0"}, "dist": {"shasum": "c1c2c2f3e30d2a568f055a96ea11ce03664fb772", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.17.0.tgz", "integrity": "sha512-u+C8RigrXIrV88cuOjJuMtiyFci6BiKgGNTkVmrVTEst9dSmkCGsbWtm3sdbssCe7fzn2WAoXWYCRJznus9fsg==", "signatures": [{"sig": "MEQCIFOz0CI+EDf3UPzhSgCBWdaiEGKPqBQ7m+f4f6h4RohDAiBsX3cKtFvhdYdbl0gt4UjK1s/8tDiYuCRttGoIYz5W5g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.18.0": {"name": "magic-string", "version": "0.18.0", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"buble": "^0.14.0", "mocha": "^3.1.0", "eslint": "^3.7.1", "rollup": "^0.36.1", "resolve": "^1.1.7", "istanbul": "^0.4.5", "codecov.io": "^0.1.6", "source-map": "^0.5.6", "console-group": "^0.3.2", "remap-istanbul": "^0.6.4", "source-map-support": "^0.4.3", "rollup-plugin-buble": "^0.14.0", "rollup-plugin-replace": "^1.1.1", "rollup-plugin-node-resolve": "^2.0.0"}, "dist": {"shasum": "c5066abc4cda5d868b85eaf7e7731a15fa7903d0", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.18.0.tgz", "integrity": "sha512-IDL/CDPXfLGbZ0IY9RI1BWVzb7nSL/nvgy1AQylncuWoaXUp5bzxGrulQZsAii+vn+MIzj9oB2MtmBP37R3y/w==", "signatures": [{"sig": "MEYCIQDUOFPfMFlqvtSh0TmgNAj3VTs038pCR7H6S+Qr2Z+qCwIhAOFRm5cMgVOw6ORnU3eGZCBRq+Uq4HPO134a1ig75Hap", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.19.0": {"name": "magic-string", "version": "0.19.0", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"buble": "^0.14.0", "mocha": "^3.1.0", "eslint": "^3.7.1", "rollup": "^0.36.1", "resolve": "^1.1.7", "istanbul": "^0.4.5", "codecov.io": "^0.1.6", "source-map": "^0.5.6", "console-group": "^0.3.2", "remap-istanbul": "^0.6.4", "source-map-support": "^0.4.3", "rollup-plugin-buble": "^0.14.0", "rollup-plugin-replace": "^1.1.1", "rollup-plugin-node-resolve": "^2.0.0"}, "dist": {"shasum": "198948217254e3e0b93080e01146b7c73b2a06b2", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.19.0.tgz", "integrity": "sha512-9UbmuWNz8mNNOmSnbwmvTa92AVjani9TqWUhBW35UVpl5W6KUu+9IM09Qs+xpbVvV74Q6TNXXfE25s0sx7Rj8w==", "signatures": [{"sig": "MEUCIQDDxQy4AMY4fPJ9hAj9Ss853adK9sA+eQfQvTH2VWTECgIgKsT0j1c131X8e7BzkFwAfz/jkBCZ8mvRetfyMq8/IKQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.19.1": {"name": "magic-string", "version": "0.19.1", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"buble": "^0.14.0", "mocha": "^3.1.0", "eslint": "^3.7.1", "rollup": "^0.36.1", "resolve": "^1.1.7", "istanbul": "^0.4.5", "codecov.io": "^0.1.6", "source-map": "^0.5.6", "console-group": "^0.3.2", "remap-istanbul": "^0.6.4", "source-map-support": "^0.4.3", "rollup-plugin-buble": "^0.14.0", "rollup-plugin-replace": "^1.1.1", "rollup-plugin-node-resolve": "^2.0.0"}, "dist": {"shasum": "14d768013caf2ec8fdea16a49af82fc377e75201", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.19.1.tgz", "integrity": "sha512-AJRZGyg/F3QJUsgz/0Kh7HR09VZ1Mu/Nfyou9WtlXAYyMErN4BvtAOqwsYpHwT+UWbP2QlGPPmHTCvZjk0zcAw==", "signatures": [{"sig": "MEUCIQCo1S+KxxsVcSEldR5ZJ7KXClUz2NAPaTZMzG5Aj/HdIgIgViKg/Kz9DxXrrUiKtcgv8w3X3/sJlk9RE0aBnpwwuR0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.20.0": {"name": "magic-string", "version": "0.20.0", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"buble": "^0.14.0", "mocha": "^3.1.0", "eslint": "^3.7.1", "rollup": "^0.36.1", "resolve": "^1.1.7", "istanbul": "^0.4.5", "codecov.io": "^0.1.6", "source-map": "^0.5.6", "console-group": "^0.3.2", "remap-istanbul": "^0.6.4", "source-map-support": "^0.4.3", "rollup-plugin-buble": "^0.14.0", "rollup-plugin-replace": "^1.1.1", "rollup-plugin-node-resolve": "^2.0.0"}, "dist": {"shasum": "255f07e0b1459dc00d74636367e9200cbb5d529d", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.20.0.tgz", "integrity": "sha512-hUtRcmCuD+j5eBzB42iSXw55hxrM1l72949VPangbxnas4RL5DhC/KQl6cGAa4XuceBlxqYOIhWKJLWhJ6ubsw==", "signatures": [{"sig": "MEUCIFCyju40BKKKbASjGIN24gnEpFZ9gNxTG78vcOrSpMDxAiEAp8Lp9lwd/wLAh7r67MCyqc24KW7ihyy5d84gEhusPMk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.21.0": {"name": "magic-string", "version": "0.21.0", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"buble": "^0.14.0", "mocha": "^3.1.0", "eslint": "^3.7.1", "rollup": "^0.36.1", "resolve": "^1.1.7", "istanbul": "^0.4.5", "codecov.io": "^0.1.6", "source-map": "^0.5.6", "console-group": "^0.3.2", "remap-istanbul": "^0.6.4", "source-map-support": "^0.4.3", "rollup-plugin-buble": "^0.14.0", "rollup-plugin-replace": "^1.1.1", "rollup-plugin-node-resolve": "^2.0.0"}, "dist": {"shasum": "a4ab14e93613630514e18c75b920e4f2225f5027", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.21.0.tgz", "integrity": "sha512-CvXZk4FnhCku0Bn4k6sSqSaBHtbcIuR+MziD3TQxaZM4ya3b/wYaTOt+PlrNTs99Qt5awP5n3IATTA10PZbcmw==", "signatures": [{"sig": "MEYCIQCfPmjXX4xSagq4EdTilFY9sJ4RErKUJo6eJLXXwHUF/wIhAJIDov7x3bhWsQIfg1VPr4OSv2tM5xhnjJwOiWWiJn2G", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.21.1": {"name": "magic-string", "version": "0.21.1", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"buble": "^0.14.0", "mocha": "^3.1.0", "eslint": "^3.7.1", "rollup": "^0.36.1", "resolve": "^1.1.7", "istanbul": "^0.4.5", "codecov.io": "^0.1.6", "source-map": "^0.5.6", "console-group": "^0.3.2", "remap-istanbul": "^0.6.4", "source-map-support": "^0.4.3", "rollup-plugin-buble": "^0.14.0", "rollup-plugin-replace": "^1.1.1", "rollup-plugin-node-resolve": "^2.0.0"}, "dist": {"shasum": "ec76b6b6e2e831543caa43cb7952852d10e6d917", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.21.1.tgz", "integrity": "sha512-K8OMr/1OKX5ElwzPkcmO/ADDsbkujOh+kvVeDB6vxrOi2j8m+3Tq+NBkBw2XoAtmRIeDHMTnurMVHWZiHNSpdQ==", "signatures": [{"sig": "MEQCIHCqdKquEOLi3fB1C/CaAla6soutiZJi/mzAvpqVDoiBAiBIYUKjvfqfSWf6/7waqgCBKA2cMTIczn+b433KmQ49xQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.21.2": {"name": "magic-string", "version": "0.21.2", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"buble": "^0.14.0", "mocha": "^3.1.0", "eslint": "^3.7.1", "rollup": "^0.36.1", "resolve": "^1.1.7", "istanbul": "^0.4.5", "codecov.io": "^0.1.6", "source-map": "^0.5.6", "console-group": "^0.3.2", "remap-istanbul": "^0.6.4", "source-map-support": "^0.4.3", "rollup-plugin-buble": "^0.14.0", "rollup-plugin-replace": "^1.1.1", "rollup-plugin-node-resolve": "^2.0.0"}, "dist": {"shasum": "b115a7a00eeba7d23e81b24e98fa2d58d2f881af", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.21.2.tgz", "integrity": "sha512-OakjFUn+DHu/1J4XRfhWW/ejjpZX11guU9tSRAKnn28M7pIpI66lQg2VnrXJcL4Ju3ldLK0uwi9xZ4YsC6KoAg==", "signatures": [{"sig": "MEYCIQDBsfd6+jqRBnViAqwbh0h/4HGZT+WG8jDzVN5+sgAaNAIhALT42OPnySeH1+XErTISGkA/to5uFwu2Bx9ifWCNZk5o", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.21.3": {"name": "magic-string", "version": "0.21.3", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"buble": "^0.14.0", "mocha": "^3.1.0", "eslint": "^3.7.1", "rollup": "^0.36.1", "resolve": "^1.1.7", "istanbul": "^0.4.5", "codecov.io": "^0.1.6", "source-map": "^0.5.6", "console-group": "^0.3.2", "remap-istanbul": "^0.6.4", "source-map-support": "^0.4.3", "rollup-plugin-buble": "^0.14.0", "rollup-plugin-replace": "^1.1.1", "rollup-plugin-node-resolve": "^2.0.0"}, "dist": {"shasum": "87e201009ebfde6f46dc5757305a70af71e31624", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.21.3.tgz", "integrity": "sha512-U66DN4L1NJsQTCaWbP9MTW+iydrqkYW8Is1QV+1tT44oZYfr0t1BUbtDew40YXrNOZSJAUYPZ8s4gd//2AZBUQ==", "signatures": [{"sig": "MEYCIQDdn4T+zELT2jNBGYLz3oR3V+4eSpmm/gRAGBlGtw6luQIhANIN0vCptyjThR9uF82nRwKM8L64qn9u+ucB2BJaOoJv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.22.0": {"name": "magic-string", "version": "0.22.0", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"buble": "^0.14.0", "mocha": "^3.1.0", "eslint": "^3.7.1", "rollup": "^0.36.1", "resolve": "^1.1.7", "istanbul": "^0.4.5", "codecov.io": "^0.1.6", "source-map": "^0.5.6", "console-group": "^0.3.2", "remap-istanbul": "^0.6.4", "source-map-support": "^0.4.3", "rollup-plugin-buble": "^0.14.0", "rollup-plugin-replace": "^1.1.1", "rollup-plugin-node-resolve": "^2.0.0"}, "dist": {"shasum": "d3a671d6a8c3c591dfeb82e75649cdec6b28445b", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.22.0.tgz", "integrity": "sha512-s8grA/KB1/ccqjlURVfalCENqx/BQxa/ovfYIy9di1k96YhFIp45vY+qDniWuYoBmKA+rfEtDyIVwEvHPlGRpA==", "signatures": [{"sig": "MEQCIDnuqrdChiBXVdr4ozQzpmwCjWpZWRmEs7tqIOgz0c+sAiBfEPxlU1qVsfJxFzrQJGmWDplRNxqE1h1U9/WrcTrxMA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.22.1": {"name": "magic-string", "version": "0.22.1", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"buble": "^0.14.0", "mocha": "^3.1.0", "eslint": "^3.7.1", "rollup": "^0.36.1", "resolve": "^1.1.7", "istanbul": "^0.4.5", "codecov.io": "^0.1.6", "source-map": "^0.5.6", "console-group": "^0.3.2", "remap-istanbul": "^0.6.4", "source-map-support": "^0.4.3", "rollup-plugin-buble": "^0.14.0", "rollup-plugin-replace": "^1.1.1", "rollup-plugin-node-resolve": "^2.0.0"}, "dist": {"shasum": "a1bda64dfd4ae6c63797a45a67ee473b1f8d0e0f", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.22.1.tgz", "integrity": "sha512-UG7eMVCbwYwrJ0wuA8yVykhTVWt0n6nnJ9UhWwRqJz4YXaEZctouKbO4mn82Leo8fw3LMCRXkXetU/tU18wfrQ==", "signatures": [{"sig": "MEYCIQCZWOthf2kDdJE2VJ58C2sAIb2mVv9MRaKWL6U/KLTaTQIhAOmZF4EVZ3j022cksABCfPLX+jt2JB4Zw820MHwEI13X", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.22.2": {"name": "magic-string", "version": "0.22.2", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"buble": "^0.14.0", "mocha": "^3.1.0", "eslint": "^3.7.1", "rollup": "^0.36.1", "resolve": "^1.1.7", "istanbul": "^0.4.5", "codecov.io": "^0.1.6", "source-map": "^0.5.6", "console-group": "^0.3.2", "remap-istanbul": "^0.6.4", "source-map-support": "^0.4.3", "rollup-plugin-buble": "^0.14.0", "rollup-plugin-replace": "^1.1.1", "rollup-plugin-node-resolve": "^2.0.0"}, "dist": {"shasum": "e0c54f1916d6264b34eb07634a5a1aacffded462", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.22.2.tgz", "integrity": "sha512-dcUSK1qjNRIe1u1UXfoCYyqhvmeTl39yJr6/emuRXXTdoiNX/Q4ZavOIEwpY6aoTiVBAoNaxBJfcdvmsmJRKzw==", "signatures": [{"sig": "MEUCIQDUHhoruNT4+2HuE23A5eQDgz6Vjwp0rQngqZ6HZ49L/QIgbcSWXwlh/8UwNv4v7okGRSf935pHZd9bO7ttLGCL3TQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.22.3": {"name": "magic-string", "version": "0.22.3", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"buble": "^0.14.0", "mocha": "^3.1.0", "eslint": "^3.7.1", "rollup": "^0.36.1", "resolve": "^1.1.7", "istanbul": "^0.4.5", "codecov.io": "^0.1.6", "source-map": "^0.5.6", "console-group": "^0.3.2", "remap-istanbul": "^0.6.4", "source-map-support": "^0.4.3", "rollup-plugin-buble": "^0.14.0", "rollup-plugin-replace": "^1.1.1", "rollup-plugin-node-resolve": "^2.0.0"}, "dist": {"shasum": "047989d99bfc7cbdefba1604adc8912551cd7ef1", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.22.3.tgz", "integrity": "sha512-pqnTW6coZiT8HvFKHoswarM6YTaYYN4imHlEtc+1LZC8hYdV+5sp0ZQqv7oEBhcDHx+c83Y1aYiUwPmoQlZmwQ==", "signatures": [{"sig": "MEUCIQDVFjBbuN2eewmW7CFc5OKVf0aObENe6nyhxeP16ZJ1VwIgZ+hgvH+3gXL9wiF+wrGeuVPfGvDoHzP6ZxO4YXQg3ag=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.22.4": {"name": "magic-string", "version": "0.22.4", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"buble": "^0.14.0", "mocha": "^3.1.0", "eslint": "^3.7.1", "rollup": "^0.36.1", "resolve": "^1.1.7", "istanbul": "^0.4.5", "codecov.io": "^0.1.6", "source-map": "^0.5.6", "console-group": "^0.3.2", "remap-istanbul": "^0.6.4", "source-map-support": "^0.4.3", "rollup-plugin-buble": "^0.14.0", "rollup-plugin-replace": "^1.1.1", "rollup-plugin-node-resolve": "^2.0.0"}, "dist": {"shasum": "31039b4e40366395618c1d6cf8193c53917475ff", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.22.4.tgz", "integrity": "sha512-kxBL06p6iO2qPBHsqGK2b3cRwiRGpnmSuVWNhwHcMX7qJOUr1HvricYP1LZOCdkQBUp0jiWg2d6WJwR3vYgByw==", "signatures": [{"sig": "MEUCIQDRWDbeeFmMDWXyJPu2/+PO7DKTwgZL/OgIcCTg37mkgAIgOOUTz8IR4eKVrCWcljRzGhSIkvEfTGjprovqGPs7CDI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.22.5": {"name": "magic-string", "version": "0.22.5", "dependencies": {"vlq": "^0.2.2"}, "devDependencies": {"buble": "^0.15.2", "mocha": "^3.5.0", "eslint": "^4.5.0", "rollup": "^0.48.0", "resolve": "^1.4.0", "istanbul": "^0.4.5", "codecov.io": "^0.1.6", "source-map": "^0.5.6", "console-group": "^0.3.3", "remap-istanbul": "^0.9.5", "source-map-support": "^0.4.16", "rollup-plugin-buble": "^0.15.0", "rollup-plugin-replace": "^1.1.1", "rollup-plugin-node-resolve": "^3.0.0"}, "dist": {"shasum": "8e9cf5afddf44385c1da5bc2a6a0dbd10b03657e", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.22.5.tgz", "fileCount": 10, "integrity": "sha512-oreip9rJZkzvA8Qzk9HFs8fZGF/u7H/gtrE8EN6RjKJ9kh2HlC+yQ2QezifqTZfGyiuAV0dRv5a+y/8gBb1m9w==", "signatures": [{"sig": "MEUCIQD4hUkqA42jDJjJ1SoiST7Un23EdcyaCT9Zm9kxPeXuVQIgGV7WXD3cqBK9TtErj7JHXDFW8vBx2IZN37gvWlCwhno=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 347442}}, "0.23.0": {"name": "magic-string", "version": "0.23.0", "dependencies": {"sourcemap-codec": "^1.4.1"}, "devDependencies": {"buble": "^0.15.2", "mocha": "^3.5.0", "eslint": "^4.5.0", "rollup": "^0.48.0", "resolve": "^1.4.0", "istanbul": "^0.4.5", "codecov.io": "^0.1.6", "source-map": "^0.5.6", "console-group": "^0.3.3", "remap-istanbul": "^0.9.5", "source-map-support": "^0.4.16", "rollup-plugin-buble": "^0.15.0", "rollup-plugin-replace": "^1.1.1", "rollup-plugin-node-resolve": "^3.0.0"}, "dist": {"shasum": "d8550fafb3bc34eb565b721f50aa6e44e6915c67", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.23.0.tgz", "fileCount": 10, "integrity": "sha512-4XPHnekXdHrQ8DQKMEW0COmjRzFsh04PXuGWWvg03p34WsIeON1eKYQUpbk5jpGJ+knEREiPyBEExefF+qdGNg==", "signatures": [{"sig": "MEUCIQCagMpMDwlAkaxVunFQApxWnPYAJyPQQGKq/hMzQ6cJNAIgXIMgaGd1P1cQ5rajoqGdBFD4730RQv0bgomc9jdNnTk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 361677}}, "0.23.1": {"name": "magic-string", "version": "0.23.1", "dependencies": {"sourcemap-codec": "^1.4.1"}, "devDependencies": {"buble": "^0.15.2", "mocha": "^3.5.0", "eslint": "^4.5.0", "rollup": "^0.48.0", "resolve": "^1.4.0", "istanbul": "^0.4.5", "codecov.io": "^0.1.6", "source-map": "^0.5.6", "console-group": "^0.3.3", "remap-istanbul": "^0.9.5", "source-map-support": "^0.4.16", "rollup-plugin-buble": "^0.15.0", "rollup-plugin-replace": "^1.1.1", "rollup-plugin-node-resolve": "^3.0.0"}, "dist": {"shasum": "2bfedb1bdb482fab323fb2a1564831b9be6beb21", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.23.1.tgz", "fileCount": 10, "integrity": "sha512-F5xClni8mDuOFUX73fg4n3Ist1psd5ZC8z9DaQiVPuwbPuEt6GhpUBZsYSYw3bdDEGbzlQASd6IH3L5HXkSvPw==", "signatures": [{"sig": "MEUCIDXv0pcwYQ4IhJvx9Zz7y3cv4eaYzCfP1zvv7w6pG9PVAiEA3RWAz14D/LTDrEendBGx75Oquwtge05yo2CIhvuApQg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 359870}}, "0.23.2": {"name": "magic-string", "version": "0.23.2", "dependencies": {"sourcemap-codec": "^1.4.1"}, "devDependencies": {"buble": "^0.15.2", "mocha": "^3.5.0", "eslint": "^4.5.0", "rollup": "^0.48.0", "resolve": "^1.4.0", "istanbul": "^0.4.5", "codecov.io": "^0.1.6", "source-map": "^0.5.6", "console-group": "^0.3.3", "remap-istanbul": "^0.9.5", "source-map-support": "^0.4.16", "rollup-plugin-buble": "^0.15.0", "rollup-plugin-replace": "^1.1.1", "rollup-plugin-node-resolve": "^3.0.0"}, "dist": {"shasum": "204d7c3ea36c7d940209fcc54c39b9f243f13369", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.23.2.tgz", "fileCount": 10, "integrity": "sha512-oIUZaAxbcxYIp4AyLafV6OVKoB3YouZs0UTCJ8mOKBHNyJgGDaMJ4TgA+VylJh6fx7EQCC52XkbURxxG9IoJXA==", "signatures": [{"sig": "MEUCIQDTfKwIOQqcKZ5I9mXrBAttz/ls3Sk6yoB5YvScsWSC6wIgAZGkbyyFnuy7xoxxtfG5kmF6pYs2/OJ6aj86fTxhGOw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 360905}}, "0.24.0": {"name": "magic-string", "version": "0.24.0", "dependencies": {"sourcemap-codec": "^1.4.1"}, "devDependencies": {"buble": "^0.19.3", "mocha": "^5.0.4", "eslint": "^4.19.0", "rollup": "^0.57.1", "prettier": "^1.11.1", "source-map": "^0.6.1", "source-map-support": "^0.5.4", "rollup-plugin-buble": "^0.19.2", "rollup-plugin-replace": "^2.0.0", "rollup-plugin-node-resolve": "^3.3.0"}, "dist": {"shasum": "1b396d26406188f1fa3730a68229562d36a1c2f2", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.24.0.tgz", "fileCount": 10, "integrity": "sha512-BhLyoHHzjhXGXuTHQW9odsmdkoBtBGorgElhMVlXP1fmM5xAk+Oe9wXwD6TAnm6Fjuq8ItQOePf2H1jLzor7CQ==", "signatures": [{"sig": "MEUCID8/3o54NnL48EC3IAjFzddJ/NRN01oocW9B305BAWhXAiEAtwYxOSOcNsnE6+BTPTNswrCm04ScdINgOj4gRZgJ6Mk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 343074}}, "0.24.1": {"name": "magic-string", "version": "0.24.1", "dependencies": {"sourcemap-codec": "^1.4.1"}, "devDependencies": {"buble": "^0.19.3", "mocha": "^5.0.4", "eslint": "^4.19.0", "rollup": "^0.57.1", "prettier": "^1.11.1", "source-map": "^0.6.1", "source-map-support": "^0.5.4", "rollup-plugin-buble": "^0.19.2", "rollup-plugin-replace": "^2.0.0", "rollup-plugin-node-resolve": "^3.3.0"}, "dist": {"shasum": "7e38e5f126cae9f15e71f0cf8e450818ca7d5a8f", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.24.1.tgz", "fileCount": 10, "integrity": "sha512-YBfNxbJiixMzxW40XqJEIldzHyh5f7CZKalo1uZffevyrPEX8Qgo9s0dmcORLHdV47UyvJg8/zD+6hQG3qvJrA==", "signatures": [{"sig": "MEQCIBcnyfrB3xkCP6Rl+dd+Y6G9E9YUqvv90sinfal6+iCMAiBN5sbamf+tQhaVrfxMkyntxOffY7s7EdCH6N9CqvZYkw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 355640, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+tRqCRA9TVsSAnZWagAAJ6YP/2J7ZbjtNLRKHvBIqGIQ\nxjeGz39L80tYl7Lcl6NvddSwqjTYudc29S8uRbguLsCZtN8z0GKWrXfhDNuv\nJeRvLKKIynH1T5wuXQiH2AYDUGgtIub2tMt/2bRADqSII4TpGXwdxLEKRAJQ\npVoXbqRuQWldXgMBiuFXmmAHwKmCao8h3GZN2+WlrXMDfE4xv7JQgfW1cFBu\naRsi34Y62sjkAEYhOFGDJ9gRlbG2evwmv7qVtUr02OelZ72mD9rZrdbFhES9\nvZnYiVp9FjYmfcevP3H++2UKutvNHg99GG4qWXhpMyT48PSl6OALqPEC+oCR\nLtjuazmLyzq0mFEQP2xBcrnf3Z6yQN5ErVC7Eeicb4vpgvWCEMYk3fEtPyyq\ndTFrU9AEObGBhylqprVD4ciw/1m3A+dkZ+exCLQeFiEM69+3nxU9hAjzQpZy\nLywN5bzeIDp/PTqwMJNTbLVviGSSpfo/cI3PPkohdHPHb49iCq8ZAVM/L2Sn\n71xYxjgVa8Qonj274DyFto4Kq5rgTZUloMEAzfk6tRyzPR+zyDnmi+5Ui4Q7\nTDmngHO7mJ4trwh9CLaQt+2I62F9c0zoqC+BedNWFZgPdWsiCQE1p11EwjRJ\nUCmOsAwSigtwoJxUB2+3Gl52piuiOEDScZ3KyFUrfwjZR5NoG9SEXwMiLQCf\nEf9D\r\n=dbIp\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.25.0": {"name": "magic-string", "version": "0.25.0", "dependencies": {"sourcemap-codec": "^1.4.1"}, "devDependencies": {"buble": "^0.19.3", "mocha": "^5.0.4", "eslint": "^4.19.0", "rollup": "^0.57.1", "prettier": "^1.11.1", "source-map": "^0.6.1", "source-map-support": "^0.5.4", "rollup-plugin-buble": "^0.19.2", "rollup-plugin-replace": "^2.0.0", "rollup-plugin-node-resolve": "^3.3.0"}, "dist": {"shasum": "1f3696f9931ff0a1ed4c132250529e19cad6759b", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.25.0.tgz", "fileCount": 10, "integrity": "sha512-Msbwa9oNYNPjwVh9ury5X2BHbTFWoirTlzuf4X+pIoSOQVKNRJHXTx1WmKYuXzRM4QZFv8dGXyZvhDMmWhGLPw==", "signatures": [{"sig": "MEYCIQCB2Cci/GThEm5qO5EH/+Agog98RnRXMewRh3BPvPvXGwIhAKM3ARJei81QJfM0Yr7JablUd9DYE3AxZ5EpTbO9EBIr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 359840, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbD8B7CRA9TVsSAnZWagAAyRIP/japlTUNiyQe959zsQ1z\nZ8ocLuC/925tenOwp0O/PSVayA3mKs7iYAt3cweRk4qC5ym3i4sU0b9/S2SH\nzKo/mGAU8mLuacAFPEeR1Gll65w0xiYV0rmSsuFqao2sGJM2K3Z3uYaLr6Qb\nlXwGERQEvK3mrGQSSHOc3JtSLw+AaO9mLWMyNU15q4Jo8rHJpDx5kemYk6Sq\nXuxgxRBKwrt2vmM0xCzSNdx9KsnqCm4yfyo2LkyYgH6STtN9a3AG3jE372uN\nVFNSlHKujm+0wDoSOW6xoDWqQOtYd/Ra67/7y1nJIWkQMvc+yoLH55w1Pkwb\noG6/gH2eNGtdajrReQh1G2kx7bwxU85QTpy+SBOwA6qukns4bhBgLAIB7n1W\naYD83yWXnWqccP5kYh2m7GllRcqczL9zGPSvQMU2OTR4Bgv22fwvDrDC2/dn\nEeI+4i+CAYwIotVkoUZmakXzNXvYBNlkmv/S2X5+H2RVA2Jt0Nf6bE8ac6/z\nk+NzEQslTuLEh9MxeTq+LSyfH/9n+KCI37GsnchPX1rFOJ/DI5UH0iypzu8Y\n/w0PVK88b3bKwhiUQ5RFggKobfTA8WbvFRnZv2tHfBOJhQOz/YZRd25VXwuA\nLOFxIZFDF5nvQFktYdoScL45vtUkdACeUuB7LWyhn3kbLGQYcCzilgwJkhlJ\no53b\r\n=Cvyr\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.25.1": {"name": "magic-string", "version": "0.25.1", "dependencies": {"sourcemap-codec": "^1.4.1"}, "devDependencies": {"buble": "^0.19.3", "mocha": "^5.0.4", "eslint": "^4.19.0", "rollup": "^0.57.1", "prettier": "^1.11.1", "source-map": "^0.6.1", "source-map-support": "^0.5.4", "rollup-plugin-buble": "^0.19.2", "rollup-plugin-replace": "^2.0.0", "rollup-plugin-node-resolve": "^3.3.0"}, "dist": {"shasum": "b1c248b399cd7485da0fe7385c2fc7011843266e", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.25.1.tgz", "fileCount": 10, "integrity": "sha512-sCuTz6pYom8Rlt4ISPFn6wuFodbKMIHUMv4Qko9P17dpxb7s52KJTmRuZZqHdGmLCK9AOcDare039nRIcfdkEg==", "signatures": [{"sig": "MEUCIQCXsJ73G8JNLwCF5vGbkGFrIqVlS9w2R1/qPAa6aiZf1AIgTlqZjCLaTtFjEXXKItpy1vww6N51bxp6NqPw2NI+Ykk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 360082, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJboWPjCRA9TVsSAnZWagAAJuEQAINm96lVOboXV0ZMezx8\nB9qPhdNlRbOGtuGGJZsisJJURR6JA6kLfzpRdLVC7XHWpsw+XI2WCSu0HRr6\n0ODRzSEUucdib1pZn/59Sb3HOkZk+x0tdCfbTOlV75VMGP0dKuXlNNmo8lh0\nKIgkq1S/1yYZlM0NFjogrKjpT1x2wwgGNb2uSIVrbyDytDheChQz9aMwxJ3X\neutbcM3ANwHzeF53WFw2teFqc040RRUEdVTMKi5Fit2hqmNff2Jr+VWOZmee\nns1y7C/Gji2G5bXe1I7ELtggbey8VcWXN9keS/QzxDh1oD6XnWUQebJlTYUX\nATWw3bVAp4u1xSF00QZERSanf9CCuANjvI/bNefB6HbB+9wToMc61cPH2H9A\n6fIPvtMYZFP2HBijVUikkWfrDJvxsGQbljExcTBjAc3gboCNh6PyTjItah1j\nMEJz6UISLsrHCePPmlkM1zt8BUt1tQzaCNrcU6e9YeNzFsBy7o4FCpNQ8wlt\n9xLykoz9DBFYPQFH+MzHZ4R2gB4Av27XsSseJM+hNwC8OLC7E727PBTZUO58\n0miLkfqpxEdPZGziiHUStNTQYuPPVYVgiFqwEun6jqDYJu2aqgLiCglc6j7J\nVlzp3ZuESKAhmq5ld6WZ3JsbH7hYmLhDaSP1DYFEBvgUW2uG26dY5XGuMW3T\n5iXK\r\n=I+v0\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.25.2": {"name": "magic-string", "version": "0.25.2", "dependencies": {"sourcemap-codec": "^1.4.4"}, "devDependencies": {"buble": "^0.19.6", "mocha": "^5.2.0", "eslint": "^5.13.0", "rollup": "^1.1.2", "prettier": "^1.16.4", "source-map": "^0.6.1", "source-map-support": "^0.5.10", "rollup-plugin-buble": "^0.19.6", "rollup-plugin-replace": "^2.1.0", "rollup-plugin-node-resolve": "^4.0.0"}, "dist": {"shasum": "139c3a729515ec55e96e69e82a11fe890a293ad9", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.25.2.tgz", "fileCount": 11, "integrity": "sha512-iLs9mPjh9IuTtRsqqhNGYcZXGei0Nh/A4xirrsqW7c+QhKVFL2vm7U09ru6cHRD22azaP/wMDgI+HCqbETMTtg==", "signatures": [{"sig": "MEYCIQCHBrByefI190fosP94XGo3AoP/xqA2GvyRRYx5fUl71gIhAND3qcrobTOP0jGXed0t6orIekrtNclDsv0l7Hu5kgsl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 360760, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcVydyCRA9TVsSAnZWagAA8u4P/jnxPT2/FFPiABpPOcLD\nUQKTlJ5oL+oFAprge8Zl1pp4XiXumCcqpgNHrJE1d0w/yXmyRLc18mUWOmtG\nvBIwVp9UsW3LOLvJxEEIK+QwVlq8D+ogOF8iB7P5Yh3aymxi+TaW12kaUiKF\nAL1RSHhQ0J0Rs3bGmesJ3OIlQYah9VMwxo14O/FXOBARtLoXOuKiko81Fkzt\n7DyFnJxt6vpJIDh83eDFxblp3PzE46cLnynsfYBbzPSmVgVXY7nBvA+6AS16\nmuSbGnBjV+szCNM9XnncPjhHQqsP9tTG5yR+pjQH4NG4nwgn/n3K2G0yvNJs\nL5Pemn/cxUV96uSFEgxTWLllqxyO9R6VOKHzeclhkwcS42eTARlTdk91FEIu\n6nk58NuH5I4VJ14jvLHrUuuhZFZgXLI25Lys9Z6mXW0nneKFgaDaLJd5TbVj\n8We5LQzCb4eSertYn6vqkGxUkXYBRqve5/Lk+lJjtTgINLtaHo0n5Y7E6emt\nxgjqgYFjN/F6+Z+f0+uZ/36fcI6oUCFfn+tE/iA7U2CZYF6Mbexp5Orp8mBS\n3UQ8o0fst0fu2JqmeM4GIFznzia60c6RZZo9UyMWmZg5xQisMdOiwo2kEPVK\nITsxkRls7df16o598SZSPd4BIzKeqaM8C4oE3o6LrgHp+ts2ON4+it0FF/TJ\nWdfr\r\n=uUna\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.25.3": {"name": "magic-string", "version": "0.25.3", "dependencies": {"sourcemap-codec": "^1.4.4"}, "devDependencies": {"mocha": "^5.2.0", "eslint": "^5.16.0", "rollup": "^1.16.3", "prettier": "^1.18.2", "source-map": "^0.6.1", "source-map-support": "^0.5.12", "rollup-plugin-buble": "^0.19.6", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-node-resolve": "^5.2.0"}, "dist": {"shasum": "34b8d2a2c7fec9d9bdf9929a3fd81d271ef35be9", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.25.3.tgz", "fileCount": 11, "integrity": "sha512-6QK0OpF/phMz0Q2AxILkX2mFhi7m+WMwTRg0LQKq/WBB0cDP4rYH3Wp4/d3OTXlrPLVJT/RFqj8tFeAR4nk8AA==", "signatures": [{"sig": "MEQCIFPx44smiMKIRs1FR1+pYrluzzcuNSVc8ArmCtRrPf5GAiAHdvJh3S1XUeg40pD0YZNa+FJn1fMk0+m20xYoEaXBLA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 360367, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdGe6GCRA9TVsSAnZWagAAAYIQAIqaDTd+2/aF/7DrbwuF\nXU5GBERFRr+OGTEAANcXK2GyVkWznVfVrmSquMCV1p0pNqMCC7S1i5a5G0Gx\nbh5xOWzHoMGbvPMBWAoUQYhZKlaj5pOlm6RO0i7IWf1+q10foAqwGiDYSfC1\nw2dmsJdzH+0oWCUpXNQJ/SlC9aaUFIi4yW2zTLjKWPMtsdwXgc+qXJZAvLs8\na+RwNUdMX7ETjuwd5Xog7pg2n6Y9J5jGhHmn37lOet8DSdmuBvc9zlpO6eS4\n/sowI057z4A3EtKcAfy0WovFWoJXGVIvHOBEnVE3u+mAkIava/cwGZwk6Kc+\nSCUgCNk0/aznHTzBCr4gxV9PrwDg3JB+xU1jAJlrHSdoPOmHeGKoNjbPyCeI\ndNyUWKwb4zggckKMyUg6LF6hMPgzsOgU+HVLp2MuXV0D1srT7Hgoaok8pt8Z\n4U3R33IJ2T15jNYH5WCWYqnAiDdWGhDbyLr5lsx9ZZFPvyLUcKHTY/OPgpOz\n10YSJFL7I553SY0zFHZCrQXVPn7fUXy8wEfveK19hwBnutBZ+BT3tut37Mak\nlbz7bCrUDhLdvQa9VdJszqhY5APsQvosxMcythsDy5Vf4/8MBB0Ngf8KdRN+\nDeqyVEafnjeCdEEws4lC95mYj0gpEkkFBcov9jvhNxPc/dl8nnYyNDgDmoBM\nZT9F\r\n=VUgc\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.25.4": {"name": "magic-string", "version": "0.25.4", "dependencies": {"sourcemap-codec": "^1.4.4"}, "devDependencies": {"mocha": "^5.2.0", "eslint": "^5.16.0", "rollup": "^1.16.3", "prettier": "^1.18.2", "source-map": "^0.6.1", "source-map-support": "^0.5.12", "rollup-plugin-buble": "^0.19.6", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-node-resolve": "^5.2.0"}, "dist": {"shasum": "325b8a0a79fc423db109b77fd5a19183b7ba5143", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.25.4.tgz", "fileCount": 11, "integrity": "sha512-oycWO9nEVAP2RVPbIoDoA4Y7LFIJ3xRYov93gAyJhZkET1tNuB0u7uWkZS2LpBWTJUWnmau/To8ECWRC+jKNfw==", "signatures": [{"sig": "MEUCIQDo61Xl34jVHfwlMc73qEDLphw1shNNu2XV4cUKToE8qgIgDEFwmsrjEC3HgGlbPtHpbFBJaU56+mfXny8hS2CUdr4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 362278, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdkhtgCRA9TVsSAnZWagAA7WAP/2PQu7y+9iS9m3XicGo1\nnPYnUMEn+ui83c5PJjbj4Kdi6MvLkpYPP50GG+8pNNc4mf315pbzViX+TJjZ\nxiHB0rfrqR2upBPr34UkTp2caly1O2pXslWtoI5LI7ED1MDyf2AkcxYsy/z0\nU+OfpwE4do4r82omLwFZeAQzC/AJAddtzL255kJV9dVsICjaSau1bxe35NPB\nJk36XtRFolUuI8aCsjded3ZW84F/mh0QPRjqG12oJ1NutQGClxu6MI6yIVve\nWoGrQ+scBtxW7ok7jlNvrbbGwXR7L4wTHmHDTvB5Vuri5yLZiknfbJjfoyBp\nnvFKw3EgDgFJDyJ5lb0O0DxU33Q0tQ/oNk3ORjOZmNuCSLVORr4mrqs7hknq\nHsDVMJk3d7WkqrK0bIR/yZ9CbriIKVVoqWhtw2QXle9WV72NKtf+TE+5/UEp\n8ZsAmBemIY0qRSYUoJj+/Y6sgQdRD5v0wqIpbJAsZZgcP+lodSGAJR7IGmay\ngIjjJDYrxzi3NVWsHuCm4oNb7RDU+XEui6ooAkiNaoj58wEBBTf3iVCpZVv4\nPbe9Br0BJnSYP08KgU3689cvJaHPVQrESaqJvvtC+memJ1Jj/cZuNp3kSwV4\nbByTdbuVomyaMUS4FBuWQP61+w4tmQJTEGMWEq8Y4KN0XjQiGNGZ53/7Eucn\n5//A\r\n=OhX1\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.25.5": {"name": "magic-string", "version": "0.25.5", "dependencies": {"sourcemap-codec": "^1.4.4"}, "devDependencies": {"mocha": "^5.2.0", "eslint": "^5.16.0", "rollup": "^1.16.3", "prettier": "^1.18.2", "source-map": "^0.6.1", "source-map-support": "^0.5.12", "rollup-plugin-buble": "^0.19.6", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-node-resolve": "^5.2.0"}, "dist": {"shasum": "694fa8c6b9a51d83cc4a72c5b6883a7cfa890e40", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.25.5.tgz", "fileCount": 11, "integrity": "sha512-vIO/BOm9odBHBAGwv0gZPLJeO9IpwliiIc0uPeAW93rrFMJ/R3M665IAEfOU/IW3kD4S9AtEn76lfTn1Yif+9A==", "signatures": [{"sig": "MEQCIHieGPAfIlhwp5+WrZGoL4qeLtAs3zoOI3fKxGzVPcn0AiA/tssOdIOcQn3nrS+tDL8ISjVw7hdwX/VvDP1fF+C5OQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 365344, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeDmjdCRA9TVsSAnZWagAAE0EP/1mXek2xeKgXKRmT0sLj\n8RGvzRCJoIbSFXE66r5vENypKCwKF1XxMWgKFfWbaR9/UWPqRTO/5hylDThv\nxh9B8MRCaG/3sXLM+1W7p/nt3RnMe+7Vvl22umeptBAM5UnRQ8B1u/fFKmum\nNXjp+UMmxhVziBNwNkLPM9l64Y8vVdQI1V3Z/QIVY5zf8pgFONrhyMjxjI/i\nFXeGMe6IZ1cPoM/jyQREjrKu68E8TzxjLnZHSIWvC5/0W9xUrYMP0KqoDIJc\n/pPudVMWYjtKLrRSQijQqdh5AYZdzecoKDOAbPRj53BvFaWneZ4DO/8tMKtc\n9PhFpY0AFNKP7Zwy6GofKDeiFvNGC1oHD0oA3QOGC3JdHQEuIZDqbMpG1Qzh\niMMgdY8DAJhQHDKSNEvs6STS02WL9Eac1+hiWtTY2+JPAby1fZX1lRJgXwkz\nOwb4qYHlx85rlC1lglZwg3au1q4lmwRYoZQekRdf60twavjk/19feiKVzMAh\nLUFn/wkejH6MtLF8d27RMUVSG8jZOKaK9ZslXM5qhOt1/qjlOKWwG0Ri9IVQ\nK+NYuTErqusKt5F5Gy1Iekl6c0JR8tVl5HuAwjRqzuqxHxtsgzKju+0NazzO\nMU0hWbFpOwkyVJ1k5QVyL07HJsbU3i7laB9l+Nb5wmoEwv4gHrj7ieZF9ck+\nSkm4\r\n=oP9g\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.25.6": {"name": "magic-string", "version": "0.25.6", "dependencies": {"sourcemap-codec": "^1.4.4"}, "devDependencies": {"mocha": "^5.2.0", "eslint": "^5.16.0", "rollup": "^1.16.3", "prettier": "^1.18.2", "source-map": "^0.6.1", "source-map-support": "^0.5.12", "rollup-plugin-buble": "^0.19.6", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-node-resolve": "^5.2.0"}, "dist": {"shasum": "5586387d1242f919c6d223579cc938bf1420795e", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.25.6.tgz", "fileCount": 11, "integrity": "sha512-3a5LOMSGoCTH5rbqobC2HuDNRtE2glHZ8J7pK+QZYppyWA36yuNpsX994rIY2nCuyP7CZYy7lQq/X2jygiZ89g==", "signatures": [{"sig": "MEYCIQCdwz4HIHkGxdy43lvJH1noIlnlaNEhObUjY0lQaonWuAIhAOeecvvEtOg4tomYexGlZvbkNgRQvOU2NmZ45TI6UtoM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 364972, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeFy6pCRA9TVsSAnZWagAAdKEP/jXP+k3/lY+SR29/EWZ2\nlbzcA/nh5EXaqbPkB09D8rcj9uCIKpiSOZy/c15K3i0/zeU0iPPPtO5QY6To\nsDWlrklCW1I6zXjnnxQSERZefH9rZkMp0w18TrYRjDML1ig62734GM4zz6T5\ng226mfafYLphfyxsYmZapJ/Ag8yU7k0cX4z3/zbDr+touXX89rqNHdUXumnX\nj7SVzp6TplK96dZeSg8WiPY5iX8nRRqFrt3w2rDBrFy10w8nc6/9/RNVEaQZ\niR254queuik8wM5XjzeDhZ/GxZKPtekiy1BsOE6TpDt28ryJ9pcMN3eJt5Tv\nMoxZ62YEtz7d1jI4aL0GR4zbKReo/VeIZUOEQpyOR+GOrEfUf/GqluxcgAL5\nyFG4ySCgmGq2Ui3GWLwkBO8WKTkPnfCs6L1dbgmW7aHxXNNd5d/cJBgx8mXM\n4sEfrklgO1qGjiiRX1e/maOhH0QHRspintn+vcp1gfmqx5EWX1CS4omTkey5\n6eJcAxF7tDgUKDep0X94bNK9KBmK/lZpjOphuSfjfjuldBDLozJBEUrSVVRB\nwaV97mICwkPO+rw3iB8R4C2q4sF3A4t42doWElPmhi7WHP7ETkEtwNPBCa0l\ncCz2+PLbdXVW6Zb4Cz3VRAa419bNzJNCUWMgyyrCAmVcHI+eL4nV5YTCSOUg\nPag4\r\n=Nou1\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.25.7": {"name": "magic-string", "version": "0.25.7", "dependencies": {"sourcemap-codec": "^1.4.4"}, "devDependencies": {"mocha": "^5.2.0", "eslint": "^5.16.0", "rollup": "^1.16.3", "prettier": "^1.18.2", "source-map": "^0.6.1", "source-map-support": "^0.5.12", "rollup-plugin-buble": "^0.19.6", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-node-resolve": "^5.2.0"}, "dist": {"shasum": "3f497d6fd34c669c6798dcb821f2ef31f5445051", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.25.7.tgz", "fileCount": 11, "integrity": "sha512-4CrMT5DOHTDk4HYDlzmwu4FVCcIYI8gauveasrdCu2IKIFOJ3f0v/8MDGJCDL9oD2ppz/Av1b0Nj345H9M+XIA==", "signatures": [{"sig": "MEYCIQDbn5cuNWnfL089im0WJqyrdXsGcW1w5/BsEsYD5AH9/wIhAKX0L45oMLfOoFB4MEdN0FUUPhgbAhtrJXWuIkuyBPSw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 364288, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeYkuXCRA9TVsSAnZWagAAOPoP/A8L30zQIZhJCVsrXHP2\nqs4UCE2fTfA14ZJxFkFgMfaObfTc1KbNbhKTFoGlkVyp9maVMPgzrOLyc1Uv\nL+uZYZ//I20cWVQvrwugPeGKpGLaSEzG6N0oOp/4hSwWHU1gu6sXPTV02giA\nq3jmmkj0TW1Gko/Y7Ui+9SPAdCjGXnbRp0cw+auao/jXjmmgzwib0ziOEqJn\nbLpgJSx11pvLkSzzPE1djaVVXvGW0jgoaztEGMt656yNYLrgQhZKeMZfVnC6\nVQl/mfI4sOYEwR6eB5yEDQ0AzIyEAZudby3ajEYe2OZIY90ESldJ/R20L2dQ\nXPhDgyCjcIlh7HaXNbR29XrpWkbeMKjahh8kQRwbqC0NGq2FHZpjgN4Q/zxU\na5bf4cAVcO1rEA2dQqlcT1shPYvRu5mQZmcnL78blKkolvTqOX2wV3mMmbsO\nn9WT4uITLzMd7fLhXkrubGFIzD836nNgw0+BFVT83SrnCv649uwl6vfU+f/p\nPajsU/QW48IVLMjIZc6dUhuij5cX6EF7AArUo5BAJgKp5RQYWk+bWjsiWu38\nRBWKsSEgzWw1jfmmPsvVt72A8yFnQUdE6jpXoI0tRw+o7nF7l4TJbOz9WSPh\nsHcCM0wHTSHmChIt30j8V+2katTVgRY/1dyNmfFqudcGvgTyzQh64egs1wnA\nDMKp\r\n=CNM1\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.25.8": {"name": "magic-string", "version": "0.25.8", "dependencies": {"sourcemap-codec": "^1.4.8"}, "devDependencies": {"bumpp": "^7.1.1", "mocha": "^9.2.1", "eslint": "^8.10.0", "rollup": "^2.69.0", "prettier": "^2.5.1", "source-map": "^0.6.1", "source-map-support": "^0.5.21", "@rollup/plugin-buble": "^0.21.3", "@rollup/plugin-replace": "^4.0.0", "conventional-changelog-cli": "^2.2.2", "@rollup/plugin-node-resolve": "^13.1.3"}, "dist": {"shasum": "9ee85aabefa5135cf8d80e96adc83b595ae2c5e0", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.25.8.tgz", "fileCount": 10, "integrity": "sha512-n9NlSgfkB2rPYjSd/EZDoQcsXzwYAv4CIB/vi3ZSvZ2Tjax5W5Ie1NMy4HG3PVdcL4bBMMR20Ng4UcISMzqRLw==", "signatures": [{"sig": "MEUCIQDEWr+136Ln28/u9kgfGi7VZl+cmvK+kU+TSu70R9yO2gIgVXO2xtdwRN196kbdO39ckhib3gw2oYyhDpbu9th05gM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 373471, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiH7ZmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqC3Q//Vc1ZlTpSZ0AeWofOgceoUSQ2v65cp/o7QWEc7mipputDqeoi\r\nXWZv0d/InZQH5kEWkUwEzESTqjlVgCvgerO2lD/H5ccU24ve2WqeErKqmxv+\r\nEWwibxDiglO3v8m8Ls1zDnIdtX5dtVtU7U1hVU2cIUVimZKw5KhhTuUau5ab\r\nKAS953Cp9x3DLFfmJF0Qk85uyrQ4p9ADNA5N6AssJOPks0UQ6oPE3h9f9Wl0\r\nDQ2feUfk8PCaMHav8Z8FyAari+EVY0CZzfTV6InljrB929IU0X0H1hG8xUC3\r\n/cg/YHQhp2wHfVJjL33YOwotdhMCQdOYUtYYUrcnnXYa5sJihCUtDlifcwtr\r\nQypmGxb2gVHBFqfi4dors1LxLLmhFghhdbCOP3srbwmcuWuQ4YCQoxFnD8Jf\r\neLc+sOJCtV+9szlZqLgpWQkCPbx8/MioDDs/64ykOe393IPKTnu/nGI/6CZS\r\nUH0Dgp6PwDMzyiaMKWxLbett0Gqq63qkrMf2dQJ+vjbSLtUIzL2IwycypyKK\r\nbU5WjHwXGW7N9/iyVX+6yVq4F0oVv7PG532lntaK4iEbKuqG9THH4+T0BbjD\r\nBaC7PQVrzMocfbcQe8yWCQbAitML/QbWMF4jZr3OgvyGfw2WgGUoE5l3aZlk\r\ncB95kYMnzjxDeB9d8p4HeWFBB5oQ7L2uooE=\r\n=LzKN\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.25.9": {"name": "magic-string", "version": "0.25.9", "dependencies": {"sourcemap-codec": "^1.4.8"}, "devDependencies": {"bumpp": "^7.1.1", "mocha": "^9.2.1", "eslint": "^7.32.0", "rollup": "^2.69.0", "prettier": "^2.5.1", "source-map": "^0.6.1", "source-map-support": "^0.5.21", "@rollup/plugin-buble": "^0.21.3", "@rollup/plugin-replace": "^4.0.0", "conventional-changelog-cli": "^2.2.2", "@rollup/plugin-node-resolve": "^13.1.3"}, "dist": {"shasum": "de7f9faf91ef8a1c91d02c2e5314c8277dbcdd1c", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.25.9.tgz", "fileCount": 10, "integrity": "sha512-RmF0AsMzgt25qzqqLc1+MbHmhdx0ojF2Fvs4XnOqz2ZOBXzzkEwc/dJQZCYHAn7v1jbVOjAZfK8msRn4BxO4VQ==", "signatures": [{"sig": "MEUCIQDV8jwBjaFd+UCnIJ2mVDguMYVlrR3OXkx6DiSS8W/f8wIgJXOLH/EUy9wYN0a6HdqJ8zH1CfWuDJ9Okiyb9ecS4Js=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 373056, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiIHOYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq7eg//b6qpgtc+FurMJQRYvhU9BQPkLx+ILEOPzmw5F6pik3p4vZfd\r\nnWm52DYJN60O0LOHqn4/oaccJoN9UgWu8O7XLWSYOJthVFFgpEjEHn/K7tFb\r\ntLSCAfH/z27pUJQMFVCG/I9Cqkm1KiwTtA4vcU0yLo/J3CUHOwzHhViw7Y1o\r\nQUl66CNGNJ1xS0mF/YW7I1MWbxlDAitYZWua+x+t+pZUmIZSim9eL4+QN2Wd\r\nEkZmd7qC8sUMU7jYwYUrjBBsYODyoHA4MT21AoEu2meOGEe799sxlxAB6ahs\r\nuTOKtzID+j9YrBi90nqWggIRBIPAHQKIFS3u6urub921A/Hoi7LYcdV06h9O\r\nwCq7qEi1ECPh8XjO4SNzu54FJxoXGQb1wAHzuVMJP2Wc2FU0SbcYi/zBTiwF\r\nJZNgGG/Z8Qsn8/He2LilwfwR6VZPloONEEcI7xpLufcxAQJdtUJDRgDc1AGg\r\nSPv25CPe4mU19Faojsybsu0xkcTtTAJ2DJeYpCa8SVh89G8FCZkKCDK49x6i\r\niuS3aKdH1TVtaHjVDA1siqRqG4iclwMVRPoLfPLhOAgBJ93sWW8HVtkssHrh\r\no9zASPgva3E5AWw2vkFKQljuK6SAqnIRh1iA+muBAI1GjTzX5EQViJ9c4goe\r\nonQO2QsrVQWLRQO601TqgWHTFJgO2xzQIOc=\r\n=bJY0\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.26.0": {"name": "magic-string", "version": "0.26.0", "dependencies": {"sourcemap-codec": "^1.4.8"}, "devDependencies": {"bumpp": "^7.1.1", "mocha": "^9.2.1", "eslint": "^8.10.0", "rollup": "^2.69.0", "prettier": "^2.5.1", "source-map": "^0.6.1", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^4.0.0", "conventional-changelog-cli": "^2.2.2", "@rollup/plugin-node-resolve": "^13.1.3"}, "dist": {"shasum": "3b7027a9b44c12987d8af177872765369d8d4da6", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.26.0.tgz", "fileCount": 10, "integrity": "sha512-TK1tGYBWlWzWabm3Lr3w1nb2GcN5ShN23jRIAsHOdc00ZffX90QPwQOALki3F+EnI40BoiZwlb4K2jMM5z3Xpg==", "signatures": [{"sig": "MEUCIQC4ZaF0qP96FLT3aYLvQFt4gAS4/JtXLRgFaSQRX+wjkAIgBdbLsoB4BzVZaZzdeASUj7TsSKsQvkC6EC4vcGqer/U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 381736, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiIHYcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrV8Q/+Npwrq1qmYRfgdjM8kVaJuTLKxxD1JdsVC3t/lYOhA4yNmIYY\r\nCcgzJ0dIP00fUFoM+E3DeMl/eq23OO9pOuF93d/bajPiCBIJy9WEPliahpoJ\r\n80ZUmb57JXLVNsEFuHUdf16vHdl8EyALDY7sboW+DmCWIvnJzybMZKOpLLgW\r\nUEjt1OCBPuFNPEHDeIWPRQST8XZ/tqBJt979tgg84VZV6L1Mv4SRudWhGbx2\r\nqYDLqB3jXBx9t3pKbPlAXtJRISaEjy47fXfk6mhr6rVVqvrHNgfKUw9d4qyj\r\nR1ipkbmdP2WmqJ62IwlPhJnR/uc7x2bwfiPSum+NP/DWSC6zpV0404oOYbBv\r\nZChZXg1olVvXPvd9+T1WSe0Q6BDgJV9J306goBBcAorDkQT4a6j3g6tloNTX\r\nqybvte93LC1zjLH2H0WjWcCYcKRrFWdG8tiKjSjKmcBbz3/OEKqaGviX2DAg\r\n43TxO2eodOaeVdQUOXZnBXtpalMquhmaQSFRIhhkMu3YbNBzgEytSswL48p6\r\n/Gu5+pSLA2fT94Pn6FaMA3BLCc5I+PG1IfGI2jmB37/fdR16jxfMenHhWFAy\r\n2AQF/GFvyujmoroEiCtlhJfWCuhnIvB0md0xChYbQsnyYoK/6SUt61tpdRZQ\r\n93Z3IEt83eco98evlm3e+boxOtmcQ3g5SDs=\r\n=SWte\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "0.26.1": {"name": "magic-string", "version": "0.26.1", "dependencies": {"sourcemap-codec": "^1.4.8"}, "devDependencies": {"bumpp": "^7.1.1", "mocha": "^9.2.1", "eslint": "^8.10.0", "rollup": "^2.69.0", "prettier": "^2.5.1", "source-map": "^0.6.1", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^4.0.0", "conventional-changelog-cli": "^2.2.2", "@rollup/plugin-node-resolve": "^13.1.3"}, "dist": {"shasum": "ba9b651354fa9512474199acecf9c6dbe93f97fd", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.26.1.tgz", "fileCount": 10, "integrity": "sha512-ndThHmvgtieXe8J/VGPjG+Apu7v7ItcD5mhEIvOscWjPF/ccOiLxHaSuCAS2G+3x4GKsAbT8u7zdyamupui8Tg==", "signatures": [{"sig": "MEUCIQCEV7jdAxaS44U4/Nq8rJYYy1aUlgKxk4wQNzKfL1o4FwIgCBeUg9W6gKTCTTgmGcHoPp/+Gww9BrbrlHbY65WI6t8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 382934, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiIIkiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrTGQ/+MIfu5fZnGArX4QNnl/o3nOCzmBLbWtcjUdktmfw1ZJLdwWlU\r\ncbwc9Ld2+kRtER/O+lE9ex0GHSFtVf5tCCZSBuwIA5S4czhsbB/dM8eoG3sE\r\nLeevVqfnvHSpPjELYqI3qaIK2epJJGC+T7lAygSlYz07uLGraNgn3hPE0da8\r\njYHvS9pb2528rrYW8ht4wFN2PLVC/9TpuT19d0lsotIJ68M6U8BXUOjir94U\r\nEjOGCQNidpso1G6G0xQdclqciL7IcZ3w8SvAmp/hGMsItsF9CQWvCHKliX35\r\n+RPErR49IQdX8VFa9tKdLF3huYPGzZGXtLC8LjHJQCn4d37XWA4DmPfoQWdu\r\nDBfyAEq9LgXJznuloowtWct/X9oQH/uEKvhUeyUZGf3vjXkF+5M84mdJejcF\r\nMEHtuRuDDBJJ+JNZFIzhgks+m9DPsS8EGjP6APiYE1OONvdHo2ACGeT/ZvQW\r\nyvukXVNF9bKbm6FLOlzDPV4NQWKGHSaNGsfOwwHryTJMn2yZdQvTsykmLAM9\r\n5giNvLkk2lx9/Im3zYmIonF1xpTBTLuXAclBliwTVNkc//XlnMdItsf/veRc\r\nTUnMhbBX5xhAFRz+O449Qh0uAfAKXodYD27lRHtSs1OC5hmZJ29TywzgXMa9\r\ndeCWPNtx8w2fSfR5lLnNsPTRrsorF2OwGHA=\r\n=XP1v\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "0.26.2": {"name": "magic-string", "version": "0.26.2", "dependencies": {"sourcemap-codec": "^1.4.8"}, "devDependencies": {"bumpp": "^7.1.1", "mocha": "^9.2.1", "eslint": "^8.10.0", "rollup": "^2.69.0", "prettier": "^2.5.1", "source-map": "^0.6.1", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^4.0.0", "conventional-changelog-cli": "^2.2.2", "@rollup/plugin-node-resolve": "^13.1.3"}, "dist": {"shasum": "5331700e4158cd6befda738bb6b0c7b93c0d4432", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.26.2.tgz", "fileCount": 10, "integrity": "sha512-NzzlXpclt5zAbmo6h6jNc8zl2gNRGHvmsZW4IvZhTC4W7k4OlLP+S5YLussa/r3ixNT66KOQfNORlXHSOy/X4A==", "signatures": [{"sig": "MEYCIQCPNCVhf8752ocVAgRNkTtHp9jDWOD3lQzpU6V/DAt92AIhANGlXzk1NVNel7l4vaPlncM+lCqzeEw9FkDWJ0w2SUov", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 383057, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJie3n6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmouYg/9GLTEyQmOZEZT1VNpONGIdy32iMtD04gNqzxyoh7q71dATgmT\r\ni2knvfXg5M/r9i9kcVI93V4/c/ATSAUMcUe6SfQgCX4VwJ+3lWPIY07F18fK\r\nj2Ox9vds1xIusT6OSvurI/N7qHLBZnG0GAiZfDq9UflKCr22XBqRCd5TPkYq\r\nE5as/TH+nBkspH9misUnzgKSpCrtzT8l1T+6ZL9uUDzbhiCDCU+YxDH9y2gH\r\nYXIfb75rHBgcDntJuQNmkEqvDijz1EpcUJ84TgRUEHL6QS//SNituBbSeqNV\r\nTs3B27glSFWnKOY3yatr2kQK+9eokr8Svptp6+Rpq3B8tkZEmkqN3hh7M9Ee\r\nEBQiQ10NQ1I3QS1/G+s65z+qxBbkvMjWRDuRQroNWpyYKqf2sSQA6lRxvwNP\r\n6eoBEabn5BlgkUU20Kx5D2P3PXjE58LN1bd7tY/7OXaVgqekI40ZQ3I66QiY\r\nzcvmtatKy6GYvuq2csy4u9rhh5yXNVJWDoHhDVSeQuoUfVqzJ/xcfxNjOWtw\r\nckU4Z3vbHOIk/JEhnupZDhZWheO1+D8rtn/7S3l8oRTKZAW7gidKNwF7gfHw\r\nt0qYCWr8tyXNtvVVpFAXVc4oaPlemetJE+nOLFQ5qAlQb+p1B9VIKtmDhOrV\r\n7Dol2Z9H8R8/v8M8vkwx89xsIeQsremT3jg=\r\n=gMoz\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "0.26.3": {"name": "magic-string", "version": "0.26.3", "dependencies": {"sourcemap-codec": "^1.4.8"}, "devDependencies": {"bumpp": "^8.2.1", "mocha": "^10.0.0", "eslint": "^8.23.0", "rollup": "^2.78.1", "prettier": "^2.7.1", "benchmark": "^2.1.4", "source-map-js": "^1.0.2", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^4.0.0", "conventional-changelog-cli": "^2.2.2", "@rollup/plugin-node-resolve": "^13.3.0"}, "dist": {"shasum": "25840b875140f7b4785ab06bddc384270b7dd452", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.26.3.tgz", "fileCount": 10, "integrity": "sha512-u1Po0NDyFcwdg2nzHT88wSK0+Rih0N1M+Ph1Sp08k8yvFFU3KR72wryS7e1qMPJypt99WB7fIFVCA92mQrMjrg==", "signatures": [{"sig": "MEQCIHTB8is4hnjQhynXeMUI+8d46VkXqlA6RMC60BNmfEB5AiAYAh0PsODlCX8JwYWiFXybLBUhIz3Wvu0+xlnHJbnHlQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 386332, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjDcE7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpymw/8CWBfEMz/Hi5N8XY7wyiIvzBibQmcJ9WsL4h2f+uZBOIoPrbr\r\nMpPvMJMPiqqvPW64ZV5bwi8oL6BmVDOId6z2534o1TMg1G5ejRkkRSLqG+eb\r\n5rK+ETrkpBNm5n5IOHrW+3pH4QxGgaOki9G3tJ9NweTbycXfXSz1INSYo0gD\r\nxzp/oC1wK3NL0N2kFO0RDy4zA1l/2dz1beU/7bq/tIROB/2OEOWboznuJIvU\r\n+a67QN8VkU7cGbq+3Tr3wUTAxSWhPfdY13j1ABVPlbfiJ/FuBvetXpv71JfG\r\ndL56kdH/fZRjWEZ1n8cHiHpaiU1s2AHYVSgIGMwA+KsHTlkRAVuL3H9cp3Ye\r\nBtRfow31NRAzyxN6qOcYovZtocJopbG/uMntG7WNu4XuNEWR3XJFxfofMN8h\r\nemgaC05fhQhgSAGJFPJZAqU0pmLocom1Tod5u0a4vht7KvKzWu4l8a3FGGek\r\nEufi4Ndf6s7MQu27b9kudFLOb/1QKhfaO3AL34//F0eTCqAxkK1REFlnJGca\r\nQk+AL5qoF59b5AJx3xv50Xhw07+7cbpmgMQx1VCksT94kgOywVqlgL2trQw+\r\n9ptYB8YlhsDmXwftZnslNFB7dEbiyKuSVOjGaxnL1mVv2BHHJMqv/q+UEBxu\r\nO8570A/iNFaJZmfxz6YWSMA1SRMDG/6BJg4=\r\n=G/1m\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "0.26.4": {"name": "magic-string", "version": "0.26.4", "dependencies": {"sourcemap-codec": "^1.4.8"}, "devDependencies": {"bumpp": "^8.2.1", "mocha": "^10.0.0", "eslint": "^8.23.1", "rollup": "^2.79.1", "prettier": "^2.7.1", "benchmark": "^2.1.4", "source-map-js": "^1.0.2", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^4.0.0", "conventional-changelog-cli": "^2.2.2", "@rollup/plugin-node-resolve": "^14.1.0"}, "dist": {"shasum": "3d057d3d0234c3b179aa3f421b33fe5d8a4044a8", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.26.4.tgz", "fileCount": 10, "integrity": "sha512-e5uXtVJ22aEpK9u1+eQf0fSxHeqwyV19K+uGnlROCxUhzwRip9tBsaMViK/0vC3viyPd5Gtucp3UmEp/Q2cPTQ==", "signatures": [{"sig": "MEYCIQCGARXdsodhGT0sSAYO/ZqFt6StA7hbdcmEhnRHsyXGBwIhAOmyswJ0/5ZPZhh6wmeNEZ7wF7oRiLwZKnHBnTvJS2ag", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 397173, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjLBtYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpx1Q/+KGhCE7Ha2OvprfxfHxJZ1gjntCSb5aUZRWjLk390AYd9wZlC\r\nKtNnvEVc0PJHSzVsWtmxakdqo5wU2aljH437jqsrLtBtHYf8gMbA/kibejJr\r\nyYzmPLqwKiP5z6s45Qsg7wYKjoYw2Eh9satMt0GJWUy01s0uQjtJ9bJhWlnn\r\ntDJE707u9rx5dIlOxrVXEKr/H8tfsEq6abtU7PN4LoiUwSqrKiw6A/e0yBw7\r\n7mH4gTfN/qZLdyM3rOLxdWkNJSrPsEdCsxppi3d8vjpZ1RfGCZ4rblLm2iti\r\n7H1idgwnjSRbln1awbSCU6BLFfvBCJeF+FC35fnfC3etbdHrO+QGKfxmxIod\r\nd7vpvTg+Gsb1x6a27GEM4WTjz4PbKj8mvk8dCHegHEdNN8ah1Oo8SeQCAJwc\r\nTzq1/cAw72GIDZwIUWVxFtuv7UNHbdnzuCznNqreUO1xQG7S3IgDPFj+/EMb\r\nlA8TH4T8xc96TWCkfL/MHN9/IzcxNYsh0k2f9yJc7yIOkOW0aSCRgnWmw/z1\r\nq7Hy4R2XTE2Kqmymo+yAL8dWDy+svBla1jNPtl+mZkORx4Jyg73gyptd8MhB\r\nEnDc6bJQuEnP7V395NXwQdNIpFVWTyYZCbg8WhypXr4iUCm2nnrS1mI5PXVR\r\nMtDuWLkKLWM7sPZUw/w2l0t0MG2L5fywtjc=\r\n=wogr\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "0.26.5": {"name": "magic-string", "version": "0.26.5", "dependencies": {"sourcemap-codec": "^1.4.8"}, "devDependencies": {"bumpp": "^8.2.1", "mocha": "^10.0.0", "eslint": "^8.23.1", "rollup": "^2.79.1", "prettier": "^2.7.1", "benchmark": "^2.1.4", "source-map-js": "^1.0.2", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^4.0.0", "conventional-changelog-cli": "^2.2.2", "@rollup/plugin-node-resolve": "^14.1.0"}, "dist": {"shasum": "3ceb5c60f546ba4e21e3865ab8de4d32bd8ed07f", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.26.5.tgz", "fileCount": 10, "integrity": "sha512-yXUIYOOQnEHKHOftp5shMWpB9ImfgfDJpapa38j/qMtTj5QHWucvxP4lUtuRmHT9vAzvtpHkWKXW9xBwimXeNg==", "signatures": [{"sig": "MEUCIBXtquBihnqTrcW4huNY2eus+4VmyLCp3/393/pXP2qNAiEAoChKSUQNczpuiKQdouDvQeBUVvQ83sEtRnMV9NaX3Go=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 397386, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNr1AACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoGDRAAlOLL+wYxLOH49WyXnjzVjjlSlejMCbUb/baMHvcdpPDoyqQH\r\nMZISvZzD8aKJXsN+04XHIYTXEgTAmmMrjIA3eEDPMVS2U4uNVB9mi8iB3D58\r\nAtgnr7WsuxWAfO49I7XbQFivkrPcwE53tEmSSlQ5KJ3JBCNK+YFWBXlnnmE/\r\nRr9npjUV5JN5429W4BuNEx1qU1UA0ezvcpzFmXMmHJjyTBkiO64V/95NQ5rY\r\n+yE6eFC92cZUmmsphCgog8ifvzbli/CitCVdVO+MCq71l6fDZdLju26PSO1d\r\nNTOMMYVTPF+KPXSeGPD4isw/S4q7x0IITc4DLOAMJEAz8qRmLErx1D73Vo+F\r\nJe9gssiBwXUmimRc/xuaIuHUOcLKTFO06IrI6DQ6trfmJ2rESyK0h4DilB55\r\ncGcY6c3SAvPtCmbzOM2ePSVdPP1v6rtss3kp1eNZIs/HR8E3sMuisop8h6NC\r\n5QjiAEBJYw/7JKYp22VzeZJZynzYLoaq/IF+A1spT8kMm9IKU+QiR5Z/TNBI\r\n3Wlf0twAl8CNUw3yo3rX3P1sLIfZFhR57b8e0SykDj8sqdZcQMZusCsea7JO\r\n9+r+lK1pLfrkvZcHEEnV+Q4KtQ0dlV3YGD2OqLt2qAHN9TrnNhamDaYCfjWk\r\n2gwRYF7M1k8jYUYUKxjdQ0XwLWYOhkvzC1A=\r\n=RecY\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "0.26.6": {"name": "magic-string", "version": "0.26.6", "dependencies": {"sourcemap-codec": "^1.4.8"}, "devDependencies": {"bumpp": "^8.2.1", "mocha": "^10.0.0", "eslint": "^8.23.1", "rollup": "^2.79.1", "prettier": "^2.7.1", "benchmark": "^2.1.4", "source-map-js": "^1.0.2", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^4.0.0", "conventional-changelog-cli": "^2.2.2", "@rollup/plugin-node-resolve": "^14.1.0"}, "dist": {"shasum": "b61e417c9f40b7b53bf7e73c0a803258e20d25ee", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.26.6.tgz", "fileCount": 10, "integrity": "sha512-6d+3bFybzyQFJYSoRsl9ZC0wheze8M1LrQC7tNMRqXR4izUTDOLMd9BtSuExK9iAukFh+s5K0WAhc/dlQ+HKYA==", "signatures": [{"sig": "MEYCIQCBqC4zx/T9Byeeledo4zjfag7lBxzKQsnZMc46KuA3bgIhAISSFLzypc9WUhDqpudk2MF5aZrFI98yg6W5aeEGn2Lz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 400813, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjPf/tACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr//RAAj2Zcq//mwCiaNU/6EXwbt4BWcMJsdt9AIAXrDr3UO925o1CV\r\neEYKzzmJUBrChVEPCuiF5TZTudUxIQDqTRuc+TuK3NQvfBO7DTc2FQ5OKnyr\r\nyXiBy3ugtYo21v+kwd2JExoYWKuxHhS20LSiRLt1r9uyybvUWUA69bZTvprL\r\nBQwmcqQ6yTNIsn/lr0zKnViyAS5knkCj+ob1N5dZYcdItbHykPPpad7i3Yh1\r\nWRKmRrXONqN4TDEcD5sc8XzqIh8vQcAr14O2dJbNGy88jy/RW3Il7q3MMQup\r\njeWaH4hc4v514pWE3N58OyutL/QWh4405Pl3WfyyloUn+rvpKKvg8WZljtEF\r\n7hYgZByRY0BMOwTjrFLwltcUTQ0J9LDmV600eActv2rctfRsBKWSopb+bZp4\r\n6b+/sFy9nu0W/tjvbHGIOcn/apMydreJtT3R7JWCGf0/int4BjHr27QNzf50\r\nMntMXmfhaq3VA9AK2ztGQ3SZ2ZG8+D1vSHyvnCZaAvqG/q9LjOrCyIW062wm\r\nnnOBD7+h9KT3h3UhttMWp6e5GcpjNHp3Jk3IddNY/0r2aRez4/5HuXl7GBkh\r\nJYJee1BicZ/bOnoWGdvQ0AfqSoeL57vWaSg0NTBxVtLm7NtYaZQBUgFGcoyI\r\nOtPsLC1MNFpkJg2B2Wsu4KGthY+oPN/7ox0=\r\n=AKK0\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "0.26.7": {"name": "magic-string", "version": "0.26.7", "dependencies": {"sourcemap-codec": "^1.4.8"}, "devDependencies": {"bumpp": "^8.2.1", "mocha": "^10.0.0", "eslint": "^8.23.1", "rollup": "^2.79.1", "prettier": "^2.7.1", "benchmark": "^2.1.4", "source-map-js": "^1.0.2", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^4.0.0", "conventional-changelog-cli": "^2.2.2", "@rollup/plugin-node-resolve": "^14.1.0"}, "dist": {"shasum": "caf7daf61b34e9982f8228c4527474dac8981d6f", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.26.7.tgz", "fileCount": 10, "integrity": "sha512-hX9XH3ziStPoPhJxLq1syWuZMxbDvGNbVchfrdCtanC7D13888bMFow61x8axrx+GfHLtVeAx2kxL7tTGRl+Ow==", "signatures": [{"sig": "MEUCIALott20PudieqzQBa+AqYRUn2XCyZ+rTCfTiyEVApK3AiEAm5EBo9gLEhU5zF3AE/+/YFQ75Zx8bbFMCdNMXOuSmQc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 400761, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQ0SCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrmEg//cqsN4bfXovGe+APFPmiIQeCCOhKAJULjqqzr97CoH5QNPgnb\r\nmg6hYxV5c/vTQaFZKoUOt4vMPNsfJVeJgAf7plOOkoaICGJc8OWsHvy82Yux\r\ngngDvoe4Fa3Tfi/8aqmY02Fz0KbOSGO5+4jiDSeOJg6k92TOR4m8N5587KCt\r\nJA2WyS4/BSz1FJvh7g/O6PtHGliVUOUqjMGI8xXE8/12g38AXDSy3c5iUIvH\r\n9Dm2rPFetvJTghK6X8Q0+FFqIpOk4TyoHRMogRjWIWbidQbMc4WnMroMKY7D\r\na9vTJtmgQJ6Ql4YdSxzWSJ8R5NQpN1hqmBarKziP6oS45RYsrErAaMXLvoJU\r\nVI5QifHNeRWXemThEQ+og5s+x71nBjrVYRmGGJ5syf3yxazEgrFJib5d16Vs\r\n+NP3bXIYg5wihhGwny2ApaU3dT8ylaassQ7MtJjoAhiHFcFd9qORiNEhp3dt\r\nNE2aPd2h5mCGSWmfPcEHGk+3sah//45oKwNViUUWPtCHiv+RHnJ+wcYnSsHN\r\n0L9DoD3r56hOG+DpzLrdvuYcOtQIM2/fiwEJGW5RQO5N2Qbcw3sas2r0lyeT\r\nSk7Uaep4tmO+1KawalMGS3p+C7T/IrnA4otCNbP6UCGrMEJCNB3sr7NwDCpQ\r\nAfoAWC02hdWSimky+AyMPwf2tV7bCCvQmpo=\r\n=IzHX\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "0.27.0": {"name": "magic-string", "version": "0.27.0", "dependencies": {"@jridgewell/sourcemap-codec": "^1.4.13"}, "devDependencies": {"bumpp": "^8.2.1", "mocha": "^10.0.0", "eslint": "^8.23.1", "rollup": "^2.79.1", "prettier": "^2.7.1", "benchmark": "^2.1.4", "source-map-js": "^1.0.2", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^4.0.0", "conventional-changelog-cli": "^2.2.2", "@rollup/plugin-node-resolve": "^14.1.0"}, "dist": {"shasum": "e4a3413b4bab6d98d2becffd48b4a257effdbbf3", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.27.0.tgz", "fileCount": 10, "integrity": "sha512-8UnnX2PeRAPZuN12svgR9j7M1uWMovg/CEnIwIG0LFkXSJJe4PdfUGiTGl8V9bsBHFUtfVINcSyYxd7q+kx9fA==", "signatures": [{"sig": "MEYCIQCjcPD+gH8KxSe8LEZ7pE7PsRYl5DRzrjPvqYNt2OjCLAIhAMCl//xjiz0YsV9MKPXvo1Gh2KXn5fOOjFkuy0LgZy85", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 404809, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJji8dTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqzhxAAooS7L+BRP9mpM6fdORX+H/eQ+JBs3cc/GB8RhnoRAU4jepOw\r\n/FKA7gATmzmZ/5CE5ei3CGJrIMVFr0J41qrLDXvlx11HpuUIi9KYia9B6tZ/\r\nrMUrBp3cHVT2WTkerdWizyjr5De/JXNtWRZeH2f9MHBumCT5swmZeLyyNTcQ\r\nN7kyrKqHnpwF00zBhN9nolw3Mt/Sqc5wWPJO6rzR64fYdwkjBh4Yxgx503YZ\r\n+RaRRu8kINIgNd9Vi7hgbRlPlqpVLyjfBF+EoLFv+FKIArg0pGqScMgWRxJ1\r\n1rZQyQm75tQSQ2pmjywmH4sQHvxtXjHi7aee7dUZIvd8cadf8sFnJK8hqHEg\r\n+yXpkjxxRqt7d5CQxs858aLtDWDiWh73GunvQuVLsC86yLjdEDA/BHfLDt/f\r\n6enCmY5WuoZx/65rgLvxCkmo673hb79RWFI6RIV/P6js3+qLRnp6PUcDAptt\r\njbz5tgAq5OuuSkyen/Meyd54MJl8ykmulJUjWjhP1UVKJ3X9u0MYHUW2i7NO\r\n0XDfRogyQwMo+yGfNU4Evpp6cz6WPP67o8uU4pIB0+z/alKlh9kC6dnBhon5\r\niK+a7YgLixyPSVZXuca9QwLYn2NsBcwKmLL9o8TXe4QPffAX+90VQmX0Jln6\r\n7TavgXPRHsXfvLi57SdoQX0E2LejqxC4G3Q=\r\n=6w74\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "0.28.0": {"name": "magic-string", "version": "0.28.0", "dependencies": {"@jridgewell/sourcemap-codec": "^1.4.13"}, "devDependencies": {"bumpp": "^8.2.1", "mocha": "^10.0.0", "eslint": "^8.23.1", "rollup": "^2.79.1", "publint": "^0.1.7", "prettier": "^2.7.1", "benchmark": "^2.1.4", "source-map-js": "^1.0.2", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^4.0.0", "conventional-changelog-cli": "^2.2.2", "@rollup/plugin-node-resolve": "^14.1.0"}, "dist": {"shasum": "b29c6129b82b9f2c077fffaa3f74cd563a938dcb", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.28.0.tgz", "fileCount": 10, "integrity": "sha512-B4A<PERSON>4hzryNcHPeIGfp0Xt+Ym5Tt/XdnSTIE6U5kyN7OGot6ZJs0xnlfhxWdjv9/61Cce2b4FRIvEOyGNX93xsQ==", "signatures": [{"sig": "MEYCIQDAaJ97U52ErFDJlKgaKkRY/5BYxUILkZfNhGgYB+DjbQIhAIeAlpbLqEQRkT5Dhntl9w1+biy7pJdFpAVTvkbzIpof", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 404863, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj57xSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp2hRAAilqw9u/mP0lzIoX0fqfpkeYrT9yX29slMrpGLZS7VkNM/KL7\r\nsXX8RfeaRKirlntsuMtJQ+S53qPBSOC3V+Mx19txmyY7o3EYkOonp2sgxhui\r\nWxr+gMWsRIK7gMWncZY+Vppjlk+N35/NzyvvVjxgF9GJcsdVp5QzJbXffRKk\r\nomugD1CJSO1deKKebRqrkxU+qCP2p+NrMkqPN1x8zeXxoIso4apkpw3GXSbK\r\nQVI1dfkuUbdpFdiqlqv2+zSkSWUPIDLdUIwYYKSiwmoXiuBTw9/EHFmV0gkt\r\nnQSoWoLu+5U0lis5NgqJDHq7oB2Snqf9vdf5hWgkL7jaf0ZKiRmCVBwKBpQf\r\nJwzuafYneFizFHvK2wohdxuhwXH+KVcznvJT37BgYnHIK8FVc75IGZERORtp\r\nNwLeraIQB50zWhr9eAAztiGsb2tIKPL8TIc4fZA6VrY2DyuHo89bqhEnWXnv\r\nf467sHvsCLsS3JIPlThNDEEzs6ETzqeuqwuiBdANrsFnslZCsypELnJGyP+1\r\nEqQlAxNF2LIY8dYLzjcz7dMPTEl/2a4nD6rnGbMWWMjUsouI2fEz9sTEN1iR\r\nFTL2r4+4g4DVAjhTrKUODcslB61nRwBRVrjlT34c54teJfg7PBIvbKZSKHDe\r\nJvlmod1z8f4/gjt2MYCnhjToD9mEnfPEKSs=\r\n=SCKL\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "0.29.0": {"name": "magic-string", "version": "0.29.0", "dependencies": {"@jridgewell/sourcemap-codec": "^1.4.13"}, "devDependencies": {"bumpp": "^8.2.1", "mocha": "^10.0.0", "eslint": "^8.23.1", "rollup": "^2.79.1", "publint": "^0.1.7", "prettier": "^2.7.1", "benchmark": "^2.1.4", "source-map-js": "^1.0.2", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^4.0.0", "conventional-changelog-cli": "^2.2.2", "@rollup/plugin-node-resolve": "^14.1.0"}, "dist": {"shasum": "f034f79f8c43dba4ae1730ffb5e8c4e084b16cf3", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.29.0.tgz", "fileCount": 10, "integrity": "sha512-WcfidHrDjMY+eLjlU+8OvwREqHwpgCeKVBUpQ3OhYYuvfaYCUgcbuBzappNzZvg/v8onU3oQj+BYpkOJe9Iw4Q==", "signatures": [{"sig": "MEQCIG4An8grRLG6JlgtE10x3B33sD7IEV3LgZ/Epk9Pd+VGAiBvWUKYgtLpdm4WDYFtVmPOE/D1jAShhERexFBc6g90lg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 406092, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj57yOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo0dA//TI3DuxHioYTavCv5KDtqpiftwCWVTGcpBSifacE7XkcRTRqD\r\ngaJTGghwt/1/aqRyuTrZbrWRSCrgs5c+GLaywfuWOKesSyfBtibrvaR4m6x3\r\n4NYX3oM1ipUjC56iU9Et1Yp+C+Xu/U9RWfmghs8SiEi8TzsLHbW05bWp2dWT\r\nMv4iNRcm4wogRQfBxe4uLoc8VaBmhGX7ONnH2Xre+54Af8TZ1l4cVg62NE+k\r\nVBuN7CNqybKVYlxzZuVG7vLJUsK0WwX7liSPdo8ZswiXUVHv/GWldKbhh2F1\r\ncd6CZ9ckUMhFBJuHxreDsbrpQi8Q+3ou4dcnCzE7MtbSMoy8j2Pgaa4vmAf0\r\ngVHwswGpNPpEbebcEHvZUkmVi7F7ITR7Gq/4Yh9k2nPu+BEJskgnYfsUqOcg\r\naT7YD2jbDdn8YiQJAx4OonGCtCfCZAJ0rEAtjz+l25sVssSpKp+Gevcgo0S3\r\nZRcBQ6gMKKEVy5KTx9756VbXi2IQab/1zVAjX1lLkLi4liGP5lVOz4Y/t5/o\r\nY1yJicehbPsZVuHzKKshZZpPscV5wtMwY4R5Gtggs5cRlfpSga3RYIzNrPn1\r\nQvDIii1ADXPcRwK4tXProuaojv2QwulcjbkoIWjCkztLLD6MRXy8Yg2rDcia\r\nJdEQCEa1CqwXp3t9SMVdYzwuARVYiGHQs50=\r\n=kHOt\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "0.30.0": {"name": "magic-string", "version": "0.30.0", "dependencies": {"@jridgewell/sourcemap-codec": "^1.4.13"}, "devDependencies": {"bumpp": "^8.2.1", "mocha": "^10.0.0", "eslint": "^8.23.1", "rollup": "^2.79.1", "publint": "^0.1.7", "prettier": "^2.7.1", "benchmark": "^2.1.4", "source-map-js": "^1.0.2", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^4.0.0", "conventional-changelog-cli": "^2.2.2", "@rollup/plugin-node-resolve": "^14.1.0"}, "dist": {"shasum": "fd58a4748c5c4547338a424e90fa5dd17f4de529", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.30.0.tgz", "fileCount": 10, "integrity": "sha512-LA+31JYDJLs82r2ScLrlz1GjSgu66ZV518eyWT+S8VhyQn/JL0u9MeBOvQMGYiPk1DBiSN9DDMOcXvigJZaViQ==", "signatures": [{"sig": "MEYCIQCoU8qJttPyOYandk8iofJcQph/2gY1/TDTEVp7SdLLAQIhAO0Ue7zM+eeu13MDE1UbFB0otO0Mi6M4uxqDgA69z3xW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 411167, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9fCUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpFGw//SO8COJDAx+E538M6PfdY3Da+fcD2pZDXUuYxRLOVIKpXLanZ\r\n4C5LPJNiFi/1cAVIJByxHG1muFektIz4ypsLRcQFa6nYAYjACP2/Yu1e7buL\r\nTLVkCSiGeEDl4QoB8ju+HxBJrhXKhAnDfXaQUK88CJiYGHCVQcp24Sv7epet\r\n+1k69TY0eFWICK+vHrEKYxOgBnHHiwGG6etAZEe21Sce+nDN7S/5lUnQ4OnZ\r\nhMqvc8xXQk4BcUa/64yLMMiU5RK1yubLxXjPLseDCkXnRdldAwN+FRp8us/O\r\n+AtiKlBX/YjN77mtosJeS0cXGDzZp98iOHsQgNwreOhJqCO7taOBN9PAnv3G\r\nzkkceZGyyi497RLt9bbWqg0PNDJu9qUBH1+6GRwgZBrDxfiBB0sqXLZo4kf5\r\n6/N+pEOUKXs/pvAvxhrdcifU2amrN+MwO+5DToyOTVMFmc2fxkdRIn9Skhe6\r\nX7ZSVL1XjUE0Lb2WOIjpixX+pdMt6eiGdnNnmD087YwGT5SBBt9B42tI0yUQ\r\n6n2bu/vqFyIBSHmxINnVH+QNVLKtcWwPncUke6XX3vbFeORWtGfOzb5j0CLm\r\nO5Qb0O5remO8ImAGPd08hxOy1a8ZUUzydrCq6fF36eKBQgndW0dRcBgmwpRO\r\npoiXlxvbOFkXV6+NB72tQkAKy7leE+YTTZk=\r\n=guN7\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "0.30.1": {"name": "magic-string", "version": "0.30.1", "dependencies": {"@jridgewell/sourcemap-codec": "^1.4.15"}, "devDependencies": {"bumpp": "^9.1.1", "mocha": "^10.2.0", "eslint": "^8.44.0", "rollup": "^2.79.1", "publint": "^0.1.15", "prettier": "^2.8.8", "benchmark": "^2.1.4", "source-map-js": "^1.0.2", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^4.0.0", "conventional-changelog-cli": "^3.0.0", "@rollup/plugin-node-resolve": "^14.1.0"}, "dist": {"shasum": "ce5cd4b0a81a5d032bd69aab4522299b2166284d", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.30.1.tgz", "fileCount": 11, "integrity": "sha512-mbVKXPmS0z0G4XqFDCTllmDQ6coZzn94aMlb0o/A4HEHJCKcanlDZwYJgwnkmgD3jyWhUgj9VsPrfd972yPffA==", "signatures": [{"sig": "MEUCIHFVolLHCVGMUXJrhCu++YanDouzIHiUXKCIrnwRjbQfAiEAosnFq5BNMKOehk/wEJQshLhmuF3bWwRjOUlfJ/qxsy4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 421073}, "engines": {"node": ">=12"}}, "0.30.2": {"name": "magic-string", "version": "0.30.2", "dependencies": {"@jridgewell/sourcemap-codec": "^1.4.15"}, "devDependencies": {"bumpp": "^9.1.1", "mocha": "^10.2.0", "eslint": "^8.45.0", "rollup": "^3.26.3", "publint": "^0.2.0", "prettier": "^3.0.0", "benchmark": "^2.1.4", "source-map-js": "^1.0.2", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^5.0.2", "conventional-changelog-cli": "^3.0.0", "@rollup/plugin-node-resolve": "^15.1.0"}, "dist": {"shasum": "dcf04aad3d0d1314bc743d076c50feb29b3c7aca", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.30.2.tgz", "fileCount": 11, "integrity": "sha512-lNZdu7pewtq/ZvWUp9Wpf/x7WzMTsR26TWV03BRZrXFsv+BI6dy8RAiKgm1uM/kyR0rCfUcqvOlXKG66KhIGug==", "signatures": [{"sig": "MEYCIQC7MTefzmO8jornICNLFsuTd3hiapvMlJpME21T+drn4wIhAOBDyQHDMutaSXzZxBN3o+xNQZfaZSSQaClTE+iGxCaU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 428014}, "engines": {"node": ">=12"}}, "0.30.3": {"name": "magic-string", "version": "0.30.3", "dependencies": {"@jridgewell/sourcemap-codec": "^1.4.15"}, "devDependencies": {"bumpp": "^9.2.0", "mocha": "^10.2.0", "eslint": "^8.47.0", "rollup": "^3.28.0", "publint": "^0.2.1", "prettier": "^3.0.2", "benchmark": "^2.1.4", "source-map-js": "^1.0.2", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^5.0.2", "conventional-changelog-cli": "^3.0.0", "@rollup/plugin-node-resolve": "^15.2.0"}, "dist": {"shasum": "403755dfd9d6b398dfa40635d52e96c5ac095b85", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.30.3.tgz", "fileCount": 11, "integrity": "sha512-B7xGbll2fG/VjP+SWg4sX3JynwIU0mjoTc6MPpKNuIvftk6u6vqhDnk1R80b8C2GBR6ywqy+1DcKBrevBg+bmw==", "signatures": [{"sig": "MEUCIB3xJN5T2wZRztCqjUKwVmclBTOVJ2XryR2OEm1yE+yQAiEA7pcaP3qfk4aHtrDFYUFRcU0r9lBzbsaaZT1XONd6AF0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 432379}, "engines": {"node": ">=12"}}, "0.30.4": {"name": "magic-string", "version": "0.30.4", "dependencies": {"@jridgewell/sourcemap-codec": "^1.4.15"}, "devDependencies": {"bumpp": "^9.2.0", "mocha": "^10.2.0", "eslint": "^8.47.0", "rollup": "^3.28.0", "publint": "^0.2.1", "prettier": "^3.0.2", "benchmark": "^2.1.4", "source-map-js": "^1.0.2", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^5.0.2", "conventional-changelog-cli": "^3.0.0", "@rollup/plugin-node-resolve": "^15.2.0"}, "dist": {"shasum": "c2c683265fc18dda49b56fc7318d33ca0332c98c", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.30.4.tgz", "fileCount": 11, "integrity": "sha512-Q/TKtsC5BPm0kGqgBIF9oXAs/xEf2vRKiIB4wCRQTJOQIByZ1d+NnUOotvJOvNpi5RNIgVOMC3pOuaP1ZTDlVg==", "signatures": [{"sig": "MEUCIQDzEpbZiHtU7Fue4f80KPMHy8mVtlYlEz8fpus2yuAWigIgFhqZIUegsirNVNl1WFh3CQNUSIDWYuzp/jNO0isRKl0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 438682}, "engines": {"node": ">=12"}}, "0.30.5": {"name": "magic-string", "version": "0.30.5", "dependencies": {"@jridgewell/sourcemap-codec": "^1.4.15"}, "devDependencies": {"bumpp": "^9.2.0", "mocha": "^10.2.0", "eslint": "^8.47.0", "rollup": "^3.28.0", "publint": "^0.2.1", "prettier": "^3.0.2", "benchmark": "^2.1.4", "source-map-js": "^1.0.2", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^5.0.2", "conventional-changelog-cli": "^3.0.0", "@rollup/plugin-node-resolve": "^15.2.0"}, "dist": {"shasum": "1994d980bd1c8835dc6e78db7cbd4ae4f24746f9", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.30.5.tgz", "fileCount": 11, "integrity": "sha512-7xlpfBaQaP/T6Vh8MO/EqXSW5En6INHEvEXQiuff7Gku0PWjU3uf6w/j9o7O+SpB5fOAkrI5HeoNgwjEO0pFsA==", "signatures": [{"sig": "MEQCIBNeH2/GUfAifwiuHGnPxykbSDlQx1ejs6H2wMzbaUFQAiATPlsMVBsjjQ8yxAvgpBeIH85TgwxiFbbhCKv7kHVWeQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 438918}, "engines": {"node": ">=12"}}, "0.30.6": {"name": "magic-string", "version": "0.30.6", "dependencies": {"@jridgewell/sourcemap-codec": "^1.4.15"}, "devDependencies": {"bumpp": "^9.3.0", "mocha": "^10.2.0", "eslint": "^8.56.0", "rollup": "^4.9.6", "publint": "^0.2.7", "prettier": "^3.2.4", "benchmark": "^2.1.4", "source-map-js": "^1.0.2", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^5.0.5", "conventional-changelog-cli": "^3.0.0", "@rollup/plugin-node-resolve": "^15.2.3"}, "dist": {"shasum": "996e21b42f944e45591a68f0905d6a740a12506c", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.30.6.tgz", "fileCount": 11, "integrity": "sha512-n62qCLbPjNjyo+owKtveQxZFZTBm+Ms6YoGD23Wew6Vw337PElFNifQpknPruVRQV57kVShPnLGo9vWxVhpPvA==", "signatures": [{"sig": "MEUCIAWPd+2zDK4k6ZwOao7lXKCK/fzN84Xwva7jknIIR5/gAiEAte6P/30kAUCTumlWfmXTMHn4nddVD+1nwz1Bnii2/aI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 439043}, "engines": {"node": ">=12"}}, "0.30.7": {"name": "magic-string", "version": "0.30.7", "dependencies": {"@jridgewell/sourcemap-codec": "^1.4.15"}, "devDependencies": {"bumpp": "^9.3.0", "mocha": "^10.2.0", "eslint": "^8.56.0", "rollup": "^3.28.0", "publint": "^0.2.7", "prettier": "^3.2.4", "benchmark": "^2.1.4", "source-map-js": "^1.0.2", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^5.0.5", "conventional-changelog-cli": "^3.0.0", "@rollup/plugin-node-resolve": "^15.2.3"}, "dist": {"shasum": "0cecd0527d473298679da95a2d7aeb8c64048505", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.30.7.tgz", "fileCount": 11, "integrity": "sha512-8vBuFF/I/+OSLRmdf2wwFCJCz+nSn0m6DPvGH1fS/KiQoSaR+sETbov0eIk9KhEKy8CYqIkIAnbohxT/4H0kuA==", "signatures": [{"sig": "MEYCIQDhL3HtpSJfoArfuvhylDNyMNcyJQNrxCrLKKdQ6y8kAAIhAOokSekEY5vrJB6AwUsCMhF+fx7z75U/r3NwwsUxPzd5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 447407}, "engines": {"node": ">=12"}}, "0.30.8": {"name": "magic-string", "version": "0.30.8", "dependencies": {"@jridgewell/sourcemap-codec": "^1.4.15"}, "devDependencies": {"bumpp": "^9.3.0", "mocha": "^10.2.0", "eslint": "^8.56.0", "rollup": "^3.28.0", "publint": "^0.2.7", "prettier": "^3.2.4", "benchmark": "^2.1.4", "source-map-js": "^1.0.2", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^5.0.5", "conventional-changelog-cli": "^3.0.0", "@rollup/plugin-node-resolve": "^15.2.3"}, "dist": {"shasum": "14e8624246d2bedba70d5462aa99ac9681844613", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.30.8.tgz", "fileCount": 11, "integrity": "sha512-ISQTe55T2ao7XtlAStud6qwYPZjE4GK1S/BeVPus4jrq6JuOnQ00YKQC581RWhR122W7msZV263KzVeLoqidyQ==", "signatures": [{"sig": "MEQCIEk0t9UUASK20ulRRLvhCXCHkKEs8GUgI4B4uUWP7mHcAiA/fXIaNKC2xmPecdiQyBQ3kw4oZOlATixXcQKK2yemxA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 449430}, "engines": {"node": ">=12"}}, "0.30.9": {"name": "magic-string", "version": "0.30.9", "dependencies": {"@jridgewell/sourcemap-codec": "^1.4.15"}, "devDependencies": {"bumpp": "^9.3.0", "mocha": "^10.2.0", "eslint": "^8.56.0", "rollup": "^3.28.0", "publint": "^0.2.7", "prettier": "^3.2.4", "benchmark": "^2.1.4", "source-map-js": "^1.0.2", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^5.0.5", "conventional-changelog-cli": "^3.0.0", "@rollup/plugin-node-resolve": "^15.2.3"}, "dist": {"shasum": "8927ae21bfdd856310e07a1bc8dd5e73cb6c251d", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.30.9.tgz", "fileCount": 11, "integrity": "sha512-S1+hd+dIrC8EZqKyT9DstTH/0Z+f76kmmvZnkfQVmOpDEF9iVgdYif3Q/pIWHmCoo59bQVGW0kVL3e2nl+9+Sw==", "signatures": [{"sig": "MEUCIHXHkxNSwSUDnOoV4GxPECk0tZVlakXze/6kmAZykaieAiEAoIQ7XinkN4NsbK1eqIjOK67hT5TKYwh0LYQcKlvgKD8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 452267}, "engines": {"node": ">=12"}}, "0.30.10": {"name": "magic-string", "version": "0.30.10", "dependencies": {"@jridgewell/sourcemap-codec": "^1.4.15"}, "devDependencies": {"bumpp": "^9.4.0", "mocha": "^10.4.0", "eslint": "^8.57.0", "rollup": "^3.29.4", "publint": "^0.2.7", "prettier": "^3.2.5", "benchmark": "^2.1.4", "source-map-js": "^1.2.0", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^5.0.5", "conventional-changelog-cli": "^3.0.0", "@rollup/plugin-node-resolve": "^15.2.3"}, "dist": {"shasum": "123d9c41a0cb5640c892b041d4cfb3bd0aa4b39e", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.30.10.tgz", "fileCount": 11, "integrity": "sha512-iIRwTIf0QKV3UAnYK4PU8uiEc4SRh5jX0mwpIwETPpHdhVM4f53RSwS/vXvN1JhGX+Cs7B8qIq3d6AH49O5fAQ==", "signatures": [{"sig": "MEYCIQDd4PQKR/hOBMHNbDcxlOJnIq66VytIofcnGxrktSXRhwIhAMQB65vKuoEv+s0yyKqnLn03CWDrNjj2YMQtbC5DlBEs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 451985}}, "0.30.11": {"name": "magic-string", "version": "0.30.11", "dependencies": {"@jridgewell/sourcemap-codec": "^1.5.0"}, "devDependencies": {"bumpp": "^9.4.1", "mocha": "^10.7.0", "eslint": "^8.57.0", "rollup": "^3.29.4", "publint": "^0.2.9", "prettier": "^3.3.3", "benchmark": "^2.1.4", "source-map-js": "^1.2.0", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^5.0.7", "conventional-changelog-cli": "^3.0.0", "@rollup/plugin-node-resolve": "^15.2.3"}, "dist": {"shasum": "301a6f93b3e8c2cb13ac1a7a673492c0dfd12954", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.30.11.tgz", "fileCount": 11, "integrity": "sha512-+Wri9p0QHMy+545hKww7YAu5NyzF8iomPL/RQazugQ9+Ez4Ic3mERMd8ZTX5rfK944j+560ZJi8iAwgak1Ac7A==", "signatures": [{"sig": "MEUCIQCIebgd0f6kDRQnNdchqWa45B3wH7To6Ox85TaSBhhGewIgVlgdxSxpAAa06+I+dVHsBXhAX1wI7RB3xKJGEADem3s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 463574}}, "0.30.12": {"name": "magic-string", "version": "0.30.12", "dependencies": {"@jridgewell/sourcemap-codec": "^1.5.0"}, "devDependencies": {"bumpp": "^9.4.1", "mocha": "^10.7.0", "eslint": "^8.57.0", "rollup": "^3.29.4", "publint": "^0.2.9", "prettier": "^3.3.3", "benchmark": "^2.1.4", "source-map-js": "^1.2.0", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^5.0.7", "conventional-changelog-cli": "^3.0.0", "@rollup/plugin-node-resolve": "^15.2.3"}, "dist": {"shasum": "9eb11c9d072b9bcb4940a5b2c2e1a217e4ee1a60", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.30.12.tgz", "fileCount": 11, "integrity": "sha512-Ea8I3sQMVXr8JhN4z+H/d8zwo+tYDgHE9+5G4Wnrwhs0gaK9fXTKx0Tw5Xwsd/bCPTTZNRAdpyzvoeORe9LYpw==", "signatures": [{"sig": "MEQCIBmz+cgC3wlLPfBwqVhTW6kh8jAESo/zz8qfEwJ3lCa+AiAd4dsbftDSKe2gYAMmHDkhizGBzgkbX9E0cUWcvGiYHw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 463745}}, "0.30.13": {"name": "magic-string", "version": "0.30.13", "dependencies": {"@jridgewell/sourcemap-codec": "^1.5.0"}, "devDependencies": {"bumpp": "^9.4.1", "mocha": "^10.7.0", "eslint": "^8.57.0", "rollup": "^3.29.4", "publint": "^0.2.9", "prettier": "^3.3.3", "benchmark": "^2.1.4", "source-map-js": "^1.2.0", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^5.0.7", "conventional-changelog-cli": "^3.0.0", "@rollup/plugin-node-resolve": "^15.2.3"}, "dist": {"shasum": "92438e3ff4946cf54f18247c981e5c161c46683c", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.30.13.tgz", "fileCount": 11, "integrity": "sha512-8rYBO+MsWkgjDSOvLomYnzhdwEG51olQ4zL5KXnNJWV5MNmrb4rTZdrtkhxjnD/QyZUqR/Z/XDsUs/4ej2nx0g==", "signatures": [{"sig": "MEYCIQD2G1egymw7T3FlUNtXOGZ+Rg1pjiGUxNYyUiEV3xaNHAIhAIfpG6dllFOxWCO92mmDUe8qEIkPOphk73VikziHcnQa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 464692}}, "0.30.14": {"name": "magic-string", "version": "0.30.14", "dependencies": {"@jridgewell/sourcemap-codec": "^1.5.0"}, "devDependencies": {"bumpp": "^9.4.1", "mocha": "^10.7.0", "eslint": "^8.57.0", "rollup": "^3.29.4", "publint": "^0.2.9", "prettier": "^3.3.3", "benchmark": "^2.1.4", "source-map-js": "^1.2.0", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^5.0.7", "conventional-changelog-cli": "^3.0.0", "@rollup/plugin-node-resolve": "^15.2.3"}, "dist": {"shasum": "e9bb29870b81cfc1ec3cc656552f5a7fcbf19077", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.30.14.tgz", "fileCount": 11, "integrity": "sha512-5c99P1WKTed11ZC0HMJOj6CDIue6F8ySu+bJL+85q1zBEIY8IklrJ1eiKC2NDRh3Ct3FcvmJPyQHb9erXMTJNw==", "signatures": [{"sig": "MEQCIHyX1smUmhBVl/YZmtjfI4cwPM9rRb3Es90O8lAYtlukAiBw1XW7dZa4HslLesQ1WOFZa0NbkXlScdHTXnbmbNjpIA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 464732}}, "0.30.15": {"name": "magic-string", "version": "0.30.15", "dependencies": {"@jridgewell/sourcemap-codec": "^1.5.0"}, "devDependencies": {"bumpp": "^9.4.1", "mocha": "^10.7.0", "eslint": "^8.57.0", "rollup": "^3.29.4", "publint": "^0.2.9", "prettier": "^3.3.3", "benchmark": "^2.1.4", "source-map-js": "^1.2.0", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^5.0.7", "conventional-changelog-cli": "^3.0.0", "@rollup/plugin-node-resolve": "^15.2.3"}, "dist": {"shasum": "d5474a2c4c5f35f041349edaba8a5cb02733ed3c", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.30.15.tgz", "fileCount": 11, "integrity": "sha512-zXeaYRgZ6ldS1RJJUrMrYgNJ4fdwnyI6tVqoiIhyCyv5IVTK9BU8Ic2l253GGETQHxI4HNUwhJ3fjDhKqEoaAw==", "signatures": [{"sig": "MEUCIErNx+lmxddK2ZSCpUzGag+v5Nc79Zi1yA8oCvtqN8knAiEAxtEMdm/idur5p3prxkvo0hz4sQJTioo2uOOhpxLR8HQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 464756}}, "0.30.16": {"name": "magic-string", "version": "0.30.16", "dependencies": {"@jridgewell/sourcemap-codec": "^1.5.0"}, "devDependencies": {"bumpp": "^9.9.1", "eslint": "^9.16.0", "rollup": "^3.29.5", "vitest": "^2.1.8", "publint": "^0.2.12", "prettier": "^3.4.2", "benchmark": "^2.1.4", "@eslint/js": "^9.16.0", "source-map-js": "^1.2.1", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^5.0.7", "conventional-changelog-cli": "^3.0.0", "@rollup/plugin-node-resolve": "^15.3.0"}, "dist": {"shasum": "225ab531bf824856ca4589ed3864249e51baf30f", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.30.16.tgz", "fileCount": 11, "integrity": "sha512-GOWufviMYkLKe5PYGHutkqYlntF0qDMlrZLObkQGbdmkcQAxwe7KDLlX8MKlLXQc8GwFqINYp29HpKw2t3iRoQ==", "signatures": [{"sig": "MEUCIQC5+yzbuyBuREMJ8LRuvEaG0+kLrJ1GWIArWctoVSF2TQIgehhieUioTwIQBuWdrGKrNt4QyejR/S2C/yHXJ5jSdpA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 467510}}, "0.30.17": {"name": "magic-string", "version": "0.30.17", "dependencies": {"@jridgewell/sourcemap-codec": "^1.5.0"}, "devDependencies": {"bumpp": "^9.9.1", "eslint": "^9.16.0", "rollup": "^3.29.5", "vitest": "^2.1.8", "publint": "^0.2.12", "prettier": "^3.4.2", "benchmark": "^2.1.4", "@eslint/js": "^9.16.0", "source-map-js": "^1.2.1", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^5.0.7", "conventional-changelog-cli": "^3.0.0", "@rollup/plugin-node-resolve": "^15.3.0"}, "dist": {"shasum": "450a449673d2460e5bbcfba9a61916a1714c7453", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.30.17.tgz", "fileCount": 11, "integrity": "sha512-sNPKHvyjVf7gyjwS4xGTaW/mCnF8wnjtifKBEhxfZ7E/S8tQ0rssrwGNn6q8JH/ohItJfSQp9mBtQYuTlH5QnA==", "signatures": [{"sig": "MEUCIA+WyMv4NUbNgZ55x5RZG05+IxR7YIcarznfqFcrFnLvAiEAyIPD3EhAcNC7kTB+TlguH03go3PCcEX92RpcxhbGKBc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 467464}}}, "modified": "2025-06-09T04:08:22.729Z"}