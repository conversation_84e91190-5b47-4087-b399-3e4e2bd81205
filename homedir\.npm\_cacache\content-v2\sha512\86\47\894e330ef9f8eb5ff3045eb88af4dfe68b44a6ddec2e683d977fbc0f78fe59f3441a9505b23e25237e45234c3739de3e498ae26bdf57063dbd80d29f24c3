{"_id": "depd", "_rev": "58-8221bb04850ae16cde9adbe7d43aa715", "name": "depd", "description": "Deprecate all the things", "dist-tags": {"latest": "2.0.0"}, "versions": {"0.0.0": {"name": "depd", "description": "Deprecate all the things", "version": "0.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git://github.com/dougwilson/nodejs-depd"}, "dependencies": {"supports-color": "0.2.0"}, "devDependencies": {"istanbul": "0.2.10", "mocha": "~1.20.1", "should": "~4.0.4"}, "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter spec --bail --require should test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --require should test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --require should test/"}, "bugs": {"url": "https://github.com/dougwilson/nodejs-depd/issues"}, "homepage": "https://github.com/dougwilson/nodejs-depd", "_id": "depd@0.0.0", "dist": {"shasum": "f0cdb7651bf4f7cad646b01f747afab94aedad0b", "tarball": "https://registry.npmjs.org/depd/-/depd-0.0.0.tgz", "integrity": "sha512-1ciGHqG24uXpj/iQtyefW0oVPEYb07RVy8G7mCdTrZyyEjKnuWwtE2Pbjc8J3hultewmXiMIEmnfzgc1yT7GjA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBEYTL8N8/ds8cMhWCMvqn7JafLT09bogy7qefLEJJ5IAiAHfZOtlbf/tuXU+UBJke4pwFyhRK8GkddxfZqSxbW93g=="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.0.1": {"name": "depd", "description": "Deprecate all the things", "version": "0.0.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "keywords": ["deprecate", "deprecated"], "repository": {"type": "git", "url": "git://github.com/dougwilson/nodejs-depd"}, "dependencies": {"supports-color": "0.2.0"}, "devDependencies": {"istanbul": "0.2.10", "mocha": "~1.20.1", "should": "~4.0.4"}, "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter spec --bail --require should test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --require should test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --require should test/"}, "bugs": {"url": "https://github.com/dougwilson/nodejs-depd/issues"}, "homepage": "https://github.com/dougwilson/nodejs-depd", "_id": "depd@0.0.1", "dist": {"shasum": "ff5cd1c93fa3d941539314c6f226da1b4d2ad9e2", "tarball": "https://registry.npmjs.org/depd/-/depd-0.0.1.tgz", "integrity": "sha512-SF/xQbZAkgSvqMQrnIFORSZHRWPoQ4yrdbA2AeRb3T65t132hR6SrGqy5E2MmRvdtGf9WV86bu1mzd3X1EQyQQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDK9PPaZmtI5X70AQjLpKvm1DYAVXzOYw7wJpc/2KM6RQIgUCCYwsURMbVeoOKHm3uLDi/KIYdajZtH1RGTqN44Guk="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.1.0": {"name": "depd", "description": "Deprecate all the things", "version": "0.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "keywords": ["deprecate", "deprecated"], "repository": {"type": "git", "url": "git://github.com/dougwilson/nodejs-depd"}, "dependencies": {"supports-color": "0.2.0"}, "devDependencies": {"istanbul": "0.2.10", "mocha": "~1.20.1", "should": "~4.0.4"}, "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter spec --bail --require should test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --require should test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --require should test/"}, "bugs": {"url": "https://github.com/dougwilson/nodejs-depd/issues"}, "homepage": "https://github.com/dougwilson/nodejs-depd", "_id": "depd@0.1.0", "dist": {"shasum": "16c10b0c8b3848a5cd4e29dc3ab6b9725b46d509", "tarball": "https://registry.npmjs.org/depd/-/depd-0.1.0.tgz", "integrity": "sha512-0mvlaZUeSARUeGXInMhLU6ffgHfbfs/ZafiwTTxRqDVvPHEme3HDEBrKFwnssOT7LfAjv/feliXlI6bD57J2Lg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD6kYc/DFfBYXSP1+lTkFg/Ib4InWJLfy0DIL7Es6yg2AIhAKSnoThdK0a7yohSKicnAZjkRFC6vDFFwv628RfEx/cf"}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.2.0": {"name": "depd", "description": "Deprecate all the things", "version": "0.2.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "keywords": ["deprecate", "deprecated"], "repository": {"type": "git", "url": "git://github.com/dougwilson/nodejs-depd"}, "devDependencies": {"istanbul": "0.2.10", "mocha": "~1.20.1", "should": "~4.0.4"}, "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter spec --bail --require should test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --require should test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --require should test/"}, "bugs": {"url": "https://github.com/dougwilson/nodejs-depd/issues"}, "homepage": "https://github.com/dougwilson/nodejs-depd", "_id": "depd@0.2.0", "dist": {"shasum": "cdae0ed2e0ec4e10455e71532b0085e903a9b453", "tarball": "https://registry.npmjs.org/depd/-/depd-0.2.0.tgz", "integrity": "sha512-d3psqdI3qTOyogwnAg0X6E9a3lyZHD3UYFTtE0zZBwVVOj+h1ZxnnOXn+u8Bx4Tsq5Te+ISzpo5Yx02lSp6jnQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID1FxpWG9FRmS42qUIm/5MTMNG3hKitMl+ZzyJzKiKXPAiEA5QBplyJPl2urApjGkNvPyD57dhYaAFdOEULN7qvoO7U="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.3.0": {"name": "depd", "description": "Deprecate all the things", "version": "0.3.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "keywords": ["deprecate", "deprecated"], "repository": {"type": "git", "url": "git://github.com/dougwilson/nodejs-depd"}, "devDependencies": {"istanbul": "0.2.10", "mocha": "~1.20.1", "should": "~4.0.4"}, "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter spec --bail --require should test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --require should test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --require should test/"}, "bugs": {"url": "https://github.com/dougwilson/nodejs-depd/issues"}, "homepage": "https://github.com/dougwilson/nodejs-depd", "_id": "depd@0.3.0", "dist": {"shasum": "11c9bc28e425325fbd8b38940beff69fa5326883", "tarball": "https://registry.npmjs.org/depd/-/depd-0.3.0.tgz", "integrity": "sha512-Uyx3FgdvEYlpA3W4lf37Ide++2qOsjLlJ7dap0tbM63j/BxTCcxmyIOO6PXbKbOuNSko+fsDHzzx1DUeo1+3fA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC+psZABSTz2KN/CZEYsqiyGPJzusVlVsZc4PaQ/2ZfiQIhAMyfYRsI8gVQsQblzB4uLm/o/3WwXFG0H7oQosztjLKI"}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.4.0": {"name": "depd", "description": "Deprecate all the things", "version": "0.4.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "keywords": ["deprecate", "deprecated"], "repository": {"type": "git", "url": "git://github.com/dougwilson/nodejs-depd"}, "devDependencies": {"istanbul": "0.3.0", "mocha": "~1.20.1", "should": "~4.0.4"}, "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter spec --bail --require should test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --require should test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --require should test/"}, "bugs": {"url": "https://github.com/dougwilson/nodejs-depd/issues"}, "homepage": "https://github.com/dougwilson/nodejs-depd", "_id": "depd@0.4.0", "dist": {"shasum": "708b0f636d3f4bf5eb593a88591da04d34858504", "tarball": "https://registry.npmjs.org/depd/-/depd-0.4.0.tgz", "integrity": "sha512-euoAcSQT+udDcjVV2sg3GGlz0P5Z7T0X0AX99XsY1ETBO+ymEVT7RuqgyWQrQd17cro5IMRLnPmSVA18H7T7wQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC0vyrBm5oQ1lHCb7ajFRFlnp5rJBoqkUZ7F5Qb5o6slwIgYUbCiKdjtQqLX3SpHDT5x4g0qlL3xXusTIVvwS0o1FI="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.4.1": {"name": "depd", "description": "Deprecate all the things", "version": "0.4.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "keywords": ["deprecate", "deprecated"], "repository": {"type": "git", "url": "git://github.com/dougwilson/nodejs-depd"}, "devDependencies": {"istanbul": "0.3.0", "mocha": "~1.20.1", "should": "~4.0.4"}, "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter spec --bail --require should test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --require should test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --require should test/"}, "bugs": {"url": "https://github.com/dougwilson/nodejs-depd/issues"}, "homepage": "https://github.com/dougwilson/nodejs-depd", "_id": "depd@0.4.1", "dist": {"shasum": "2939465411bd56deb66be29800eb28b1a6be7491", "tarball": "https://registry.npmjs.org/depd/-/depd-0.4.1.tgz", "integrity": "sha512-1OUmRU9uA6iJ8fcMA7+HBxhFbfsh3aaICFByJu904XDYqPWe4V706x6m/abvLwyLYterHFIuyzmUaWms62/6rQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGAAPFEN/Q5O2A0MZXHnqNX3ap8N/hXEqCel4Uh1Qmn9AiBpB89Zf7l/q/+hPsKSsFPnHwdPGqa5YBQX3BIMsWxerQ=="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.4.2": {"name": "depd", "description": "Deprecate all the things", "version": "0.4.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "keywords": ["deprecate", "deprecated"], "repository": {"type": "git", "url": "git://github.com/dougwilson/nodejs-depd"}, "devDependencies": {"istanbul": "0.3.0", "mocha": "~1.20.1", "should": "~4.0.4"}, "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter spec --bail --require should test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --require should test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --require should test/"}, "bugs": {"url": "https://github.com/dougwilson/nodejs-depd/issues"}, "homepage": "https://github.com/dougwilson/nodejs-depd", "_id": "depd@0.4.2", "dist": {"shasum": "a4bc8a0e4801770a66363daa6d35138f3e3b54dd", "tarball": "https://registry.npmjs.org/depd/-/depd-0.4.2.tgz", "integrity": "sha512-tG4S/hTtpA6stvb9Li65vWHrCblQ/oSN/UI90RKIA3wMk3N9lR1k/dCs8NKKNAy7UXD0+1/dUqhiaBuMatVNAQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDTxy5FeH6l5E8CpxHiMtkHzOcRTiJHXWnyf5cTyQG1nAiAfxeoo/wv88m6+cPCpH6Yq85FkADx0rQjkoJTmgWniEw=="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.4.3": {"name": "depd", "description": "Deprecate all the things", "version": "0.4.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "keywords": ["deprecate", "deprecated"], "repository": {"type": "git", "url": "git://github.com/dougwilson/nodejs-depd"}, "devDependencies": {"istanbul": "0.3.0", "mocha": "~1.20.1", "should": "~4.0.4"}, "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter spec --bail --require should test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --require should test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --require should test/"}, "bugs": {"url": "https://github.com/dougwilson/nodejs-depd/issues"}, "homepage": "https://github.com/dougwilson/nodejs-depd", "_id": "depd@0.4.3", "dist": {"shasum": "7c5118b16eb8cf123de65e98e45c8d868b44146e", "tarball": "https://registry.npmjs.org/depd/-/depd-0.4.3.tgz", "integrity": "sha512-2by597a9xfiwCrv0UnNQ8502V+VFxj3BIzn6hoO356gXH+kRDPWvavzTLP7dxNxG6s6wdyzao3OC4mNVvGCDXA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAXoClEVOmKK+skcI9Rn+LlHA69Lj6qPQjA/Vxjbmo3QAiEAv8NTQNU43qimPSAlRo19wOMr/UMx+roND4pKcKYGxOo="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.4.4": {"name": "depd", "description": "Deprecate all the things", "version": "0.4.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "keywords": ["deprecate", "deprecated"], "repository": {"type": "git", "url": "git://github.com/dougwilson/nodejs-depd"}, "devDependencies": {"istanbul": "0.3.0", "mocha": "~1.20.1", "should": "~4.0.4"}, "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter spec --bail --require should test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --require should test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --require should test/"}, "bugs": {"url": "https://github.com/dougwilson/nodejs-depd/issues"}, "homepage": "https://github.com/dougwilson/nodejs-depd", "_id": "depd@0.4.4", "dist": {"shasum": "07091fae75f97828d89b4a02a2d4778f0e7c0662", "tarball": "https://registry.npmjs.org/depd/-/depd-0.4.4.tgz", "integrity": "sha512-NoEUYrSLv7re4u8OtA4yp3ktNC0HaB4dDGPg89TO+suwiVYoBMG8MeSPVpq8RNX9llVyrhknE/pGccsfRMxbhA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHC9+JCCYmNsztQejFrLv8dXP73Jd5qoaRZuU8avo5jhAiEA6/jU4oaKbehHKfFU22StEK2/DgTOjl/xWUJtnDc0KHo="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.4.5": {"name": "depd", "description": "Deprecate all the things", "version": "0.4.5", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "keywords": ["deprecate", "deprecated"], "repository": {"type": "git", "url": "https://github.com/dougwilson/nodejs-depd"}, "devDependencies": {"benchmark": "1.0.0", "beautify-benchmark": "0.2.4", "istanbul": "0.3.2", "mocha": "~1.21.4", "should": "~4.0.4"}, "files": ["lib/", "History.md", "LICENSE", "index.js", "Readme.md"], "engines": {"node": ">= 0.6"}, "scripts": {"bench": "node benchmark/index.js", "test": "mocha --reporter spec --bail --require should test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --require should test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --require should test/"}, "gitHead": "e37a15044f7da76b94d8e0d46a6343feb168c82b", "bugs": {"url": "https://github.com/dougwilson/nodejs-depd/issues"}, "homepage": "https://github.com/dougwilson/nodejs-depd", "_id": "depd@0.4.5", "_shasum": "1a664b53388b4a6573e8ae67b5f767c693ca97f1", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "1a664b53388b4a6573e8ae67b5f767c693ca97f1", "tarball": "https://registry.npmjs.org/depd/-/depd-0.4.5.tgz", "integrity": "sha512-MyQx8POntp7sey9ghPezYB5gIKSbcce5pkoHdFmDYkiOcsE5f5yLLBzv8Qcs9Ll1hPgmEOfIae51n4Fa7l3zxw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDIivdksZEW/6N85Ir4wLys0Uj97HMA1y2RtgziTB6IXwIhAKgmcvzda5B6U11KknFVIsQe6lRluI7bHpIdk+Bm8+Q5"}]}, "directories": {}}, "1.0.0": {"name": "depd", "description": "Deprecate all the things", "version": "1.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "keywords": ["deprecate", "deprecated"], "repository": {"type": "git", "url": "https://github.com/dougwilson/nodejs-depd"}, "devDependencies": {"benchmark": "1.0.0", "beautify-benchmark": "0.2.4", "istanbul": "0.3.2", "mocha": "~1.21.4", "should": "~4.0.4"}, "files": ["lib/", "History.md", "LICENSE", "index.js", "Readme.md"], "engines": {"node": ">= 0.6"}, "scripts": {"bench": "node benchmark/index.js", "test": "mocha --reporter spec --bail --require should test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --require should test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --require should test/"}, "gitHead": "08b5a2182c8c1fdf7420e4ff8532bfd7e266a7b2", "bugs": {"url": "https://github.com/dougwilson/nodejs-depd/issues"}, "homepage": "https://github.com/dougwilson/nodejs-depd", "_id": "depd@1.0.0", "_shasum": "2fda0d00e98aae2845d4991ab1bf1f2a199073d5", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "2fda0d00e98aae2845d4991ab1bf1f2a199073d5", "tarball": "https://registry.npmjs.org/depd/-/depd-1.0.0.tgz", "integrity": "sha512-Ry9z2Ea+WtRc+D8lzx+ihjJ9FTZP4azT7TPRsZXi+wUfvWc8vKZYESAFf6UmqO1SK/eiKV2En/TgWRonMNF82A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG85mOiFTKDrUK+BIcecNCSoH2gVPtySC+a6n2BotvwPAiAbThl3URmH/CK2UNccFGvBHEj1AAmubeP7vmRv6EogEA=="}]}, "directories": {}}, "1.0.1": {"name": "depd", "description": "Deprecate all the things", "version": "1.0.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "keywords": ["deprecate", "deprecated"], "repository": {"type": "git", "url": "https://github.com/dougwilson/nodejs-depd"}, "devDependencies": {"benchmark": "1.0.0", "beautify-benchmark": "0.2.4", "istanbul": "0.3.5", "mocha": "~1.21.5"}, "files": ["lib/", "History.md", "LICENSE", "index.js", "Readme.md"], "engines": {"node": ">= 0.6"}, "scripts": {"bench": "node benchmark/index.js", "test": "mocha --reporter spec --bail test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --no-exit test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/"}, "gitHead": "769e0f8108463c35a6937a9d634ab19fee45100a", "bugs": {"url": "https://github.com/dougwilson/nodejs-depd/issues"}, "homepage": "https://github.com/dougwilson/nodejs-depd", "_id": "depd@1.0.1", "_shasum": "80aec64c9d6d97e65cc2a9caa93c0aa6abf73aaa", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "80aec64c9d6d97e65cc2a9caa93c0aa6abf73aaa", "tarball": "https://registry.npmjs.org/depd/-/depd-1.0.1.tgz", "integrity": "sha512-OEWAMbCkK9IWQ8pfTvHBhCSqHgR+sk5pbiYqq0FqfARG4Cy+cRsCbITx6wh5pcsmfBPiJAcbd98tfdz5fnBbag==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCjN6xHlm+WgFX2+8LOBoIVdERiwz01dpi6vXv9PjbrxAIhAMhEhNctLA1Bp4vXMNNmS7RK86g1KH/mQQle7kcrd7dO"}]}, "directories": {}}, "1.1.0": {"name": "depd", "description": "Deprecate all the things", "version": "1.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "keywords": ["deprecate", "deprecated"], "repository": {"type": "git", "url": "https://github.com/dougwilson/nodejs-depd"}, "browser": "lib/browser/index.js", "devDependencies": {"benchmark": "1.0.0", "beautify-benchmark": "0.2.4", "istanbul": "0.3.5", "mocha": "~1.21.5"}, "files": ["lib/", "History.md", "LICENSE", "index.js", "Readme.md"], "engines": {"node": ">= 0.6"}, "scripts": {"bench": "node benchmark/index.js", "test": "mocha --reporter spec --bail test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --no-exit test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/"}, "gitHead": "78c659de20283e3a6bee92bda455e6daff01686a", "bugs": {"url": "https://github.com/dougwilson/nodejs-depd/issues"}, "homepage": "https://github.com/dougwilson/nodejs-depd", "_id": "depd@1.1.0", "_shasum": "e1bd82c6aab6ced965b97b88b17ed3e528ca18c3", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "e1bd82c6aab6ced965b97b88b17ed3e528ca18c3", "tarball": "https://registry.npmjs.org/depd/-/depd-1.1.0.tgz", "integrity": "sha512-SN03SKT2SwhaAKUnRJ47Scnys7ZL2FuogA/6s9u5+58RAyqhsI2HBDZymMB0omazkYVBAwBHW9ONcjd4iZ8hDQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDHrC7Iz+sxbvH5eViY8YYWg9lGOTpxtbwluUt3c9lGYQIhAJ1zkQf4+R+XrfBhGnITmNkTQd9ix1cb2jYgjM4+HRhx"}]}, "directories": {}}, "1.1.1": {"name": "depd", "description": "Deprecate all the things", "version": "1.1.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "keywords": ["deprecate", "deprecated"], "repository": {"type": "git", "url": "git+https://github.com/dougwilson/nodejs-depd.git"}, "browser": "lib/browser/index.js", "devDependencies": {"benchmark": "2.1.4", "beautify-benchmark": "0.2.4", "eslint": "3.19.0", "eslint-config-standard": "7.1.0", "eslint-plugin-markdown": "1.0.0-beta.7", "eslint-plugin-promise": "3.5.0", "eslint-plugin-standard": "2.3.1", "istanbul": "0.4.5", "mocha": "~1.21.5"}, "files": ["lib/", "History.md", "LICENSE", "index.js", "Readme.md"], "engines": {"node": ">= 0.6"}, "scripts": {"bench": "node benchmark/index.js", "lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --no-exit test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/"}, "gitHead": "15c5604aaab7befd413506e86670168d7481043a", "bugs": {"url": "https://github.com/dougwilson/nodejs-depd/issues"}, "homepage": "https://github.com/dougwilson/nodejs-depd#readme", "_id": "depd@1.1.1", "_shasum": "5783b4e1c459f06fa5ca27f991f3d06e7a310359", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.11.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "5783b4e1c459f06fa5ca27f991f3d06e7a310359", "tarball": "https://registry.npmjs.org/depd/-/depd-1.1.1.tgz", "integrity": "sha512-Jlk9xvkTDGXwZiIDyoM7+3AsuvJVoyOpRupvEVy9nX3YO3/ieZxhlgh8GpLNZ8AY7HjO6y2YwpMSh1ejhu3uIw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD36eUazdTqqyyYmbO9AM9zroleabjsZ6OeR35wTM2SJQIhAM3G7bHnoI/Elb9yiBTGGccveL3HNceoAuN+B7IWyPN1"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/depd-1.1.1.tgz_1501197028677_0.8715836545452476"}, "directories": {}}, "1.1.2": {"name": "depd", "description": "Deprecate all the things", "version": "1.1.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "keywords": ["deprecate", "deprecated"], "repository": {"type": "git", "url": "git+https://github.com/dougwilson/nodejs-depd.git"}, "browser": "lib/browser/index.js", "devDependencies": {"benchmark": "2.1.4", "beautify-benchmark": "0.2.4", "eslint": "3.19.0", "eslint-config-standard": "7.1.0", "eslint-plugin-markdown": "1.0.0-beta.7", "eslint-plugin-promise": "3.6.0", "eslint-plugin-standard": "3.0.1", "istanbul": "0.4.5", "mocha": "~1.21.5"}, "files": ["lib/", "History.md", "LICENSE", "index.js", "Readme.md"], "engines": {"node": ">= 0.6"}, "scripts": {"bench": "node benchmark/index.js", "lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --no-exit test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/"}, "gitHead": "9a789740084d4f07a3a611432435ae4671f722ff", "bugs": {"url": "https://github.com/dougwilson/nodejs-depd/issues"}, "homepage": "https://github.com/dougwilson/nodejs-depd#readme", "_id": "depd@1.1.2", "_shasum": "9bcd52e14c097763e749b274c4346ed2e560b5a9", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.11.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "9bcd52e14c097763e749b274c4346ed2e560b5a9", "tarball": "https://registry.npmjs.org/depd/-/depd-1.1.2.tgz", "integrity": "sha512-7emPTl6Dpo6JRXOXjLRxck+FlLRX5847cLKEn00PLAgc3g2hTZZgr+e4c2v6QpSmLeFP3n5yUo7ft6avBK/5jQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGCtqwTbAuK6T72aHoiwzwqnndvYS3r7MoiMlINUdw8FAiAFKN20mlgs56r4Z0jKO3zM1nFJZMPiQLeWl8QRoZLCGw=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/depd-1.1.2.tgz_1515736023686_0.5012104702182114"}, "directories": {}}, "2.0.0": {"name": "depd", "description": "Deprecate all the things", "version": "2.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "keywords": ["deprecate", "deprecated"], "repository": {"type": "git", "url": "git+https://github.com/dougwilson/nodejs-depd.git"}, "browser": "lib/browser/index.js", "devDependencies": {"benchmark": "2.1.4", "beautify-benchmark": "0.2.4", "eslint": "5.7.0", "eslint-config-standard": "12.0.0", "eslint-plugin-import": "2.14.0", "eslint-plugin-markdown": "1.0.0-beta.7", "eslint-plugin-node": "7.0.1", "eslint-plugin-promise": "4.0.1", "eslint-plugin-standard": "4.0.0", "istanbul": "0.4.5", "mocha": "5.2.0", "safe-buffer": "5.1.2", "uid-safe": "2.1.5"}, "engines": {"node": ">= 0.8"}, "scripts": {"bench": "node benchmark/index.js", "lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail test/", "test-ci": "istanbul cover --print=none node_modules/mocha/bin/_mocha -- --reporter spec test/ && istanbul report lcovonly text-summary", "test-cov": "istanbul cover --print=none node_modules/mocha/bin/_mocha -- --reporter dot test/ && istanbul report lcov text-summary"}, "gitHead": "6d59c85d093092e65ec77033576417d743079fa0", "bugs": {"url": "https://github.com/dougwilson/nodejs-depd/issues"}, "homepage": "https://github.com/dougwilson/nodejs-depd#readme", "_id": "depd@2.0.0", "_npmVersion": "6.4.1", "_nodeVersion": "8.12.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw==", "shasum": "b696163cc757560d09cf22cc8fad1571b79e76df", "tarball": "https://registry.npmjs.org/depd/-/depd-2.0.0.tgz", "fileCount": 6, "unpackedSize": 27117, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb01R4CRA9TVsSAnZWagAArWcP/jPqhJPVwB4A7UlP7Z79\nKmkIJRegTXb8sBTkFdL8t5TDikX47CViz3qWSktGdJnwY4q/GnLHeOr/+eq6\nXVvkSKLrpOdmDBYbf7DxVepxbWOLVSCAshlnw6XPXhcqOKd2smn1CPA/hgRj\n26YvwMECmyIlcE5RgubUvN4qqOyMhGF7muWjCbqRq3qcGK7hXUqEnrlHxHsY\ndKCnzRw0HbfxYX2QynmyRL1Y9ynQuq2ucTDxuhQf78ElkHAsbSYaEks9unYG\nWGhU8zH4TGOcLrmJ6GsKhZk22JvnxCg3zwgqLwks2EFiG1iojPD4EkjZgcnJ\nt8meOu2nT8JCa6fNpPMWUpYO1aUKmhoOb5KGyMhSsNX/DND+ndSu0kOP9gU1\nCetsBqRTHHH3jUK/NmyAvzMO0X8mk/FxzI+NTqKQKt8r2/Y5IW4A6oDkaRkr\nPSu1s1DpT5mw9gHBA7K0YKuVr6HwQVnd/1io+OyUmNarjXm2lMaXucpZG3+Q\ncWpj4FyrD0vf+/bV9LKTXee3qAJ9UEGNvhJAKUrGTR8vtdM6uMxLnsBe42hR\nsRM2dJ8KnMujdw+lH+sgjtqcYP/G1Gz2cRtfbVWTrc/olODQPBpu2ycIY0nc\ncejka3bMbVHzYaY7+ho/9SfbE/6x9LRlmcHSEiPzUHRSpYY3pskktQDM0bBo\nm15/\r\n=BUpb\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDVfxAkrRUVlYI9lRCdJvtWEgg6Iyw4wlNnurobMd5RJgIgXVZGN3yvaeosftV2FAZ86fkhmMNp/QOLUhg0DyDnMJo="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/depd_2.0.0_1540576375784_0.14703351463172942"}, "_hasShrinkwrap": false}}, "readme": "# depd\n\n[![NPM Version][npm-version-image]][npm-url]\n[![NPM Downloads][npm-downloads-image]][npm-url]\n[![Node.js Version][node-image]][node-url]\n[![Linux Build][travis-image]][travis-url]\n[![Windows Build][appveyor-image]][appveyor-url]\n[![Coverage Status][coveralls-image]][coveralls-url]\n\nDeprecate all the things\n\n> With great modules comes great responsibility; mark things deprecated!\n\n## Install\n\nThis module is installed directly using `npm`:\n\n```sh\n$ npm install depd\n```\n\nThis module can also be bundled with systems like\n[Browserify](http://browserify.org/) or [webpack](https://webpack.github.io/),\nthough by default this module will alter it's API to no longer display or\ntrack deprecations.\n\n## API\n\n<!-- eslint-disable no-unused-vars -->\n\n```js\nvar deprecate = require('depd')('my-module')\n```\n\nThis library allows you to display deprecation messages to your users.\nThis library goes above and beyond with deprecation warnings by\nintrospection of the call stack (but only the bits that it is interested\nin).\n\nInstead of just warning on the first invocation of a deprecated\nfunction and never again, this module will warn on the first invocation\nof a deprecated function per unique call site, making it ideal to alert\nusers of all deprecated uses across the code base, rather than just\nwhatever happens to execute first.\n\nThe deprecation warnings from this module also include the file and line\ninformation for the call into the module that the deprecated function was\nin.\n\n**NOTE** this library has a similar interface to the `debug` module, and\nthis module uses the calling file to get the boundary for the call stacks,\nso you should always create a new `deprecate` object in each file and not\nwithin some central file.\n\n### depd(namespace)\n\nCreate a new deprecate function that uses the given namespace name in the\nmessages and will display the call site prior to the stack entering the\nfile this function was called from. It is highly suggested you use the\nname of your module as the namespace.\n\n### deprecate(message)\n\nCall this function from deprecated code to display a deprecation message.\nThis message will appear once per unique caller site. Caller site is the\nfirst call site in the stack in a different file from the caller of this\nfunction.\n\nIf the message is omitted, a message is generated for you based on the site\nof the `deprecate()` call and will display the name of the function called,\nsimilar to the name displayed in a stack trace.\n\n### deprecate.function(fn, message)\n\nCall this function to wrap a given function in a deprecation message on any\ncall to the function. An optional message can be supplied to provide a custom\nmessage.\n\n### deprecate.property(obj, prop, message)\n\nCall this function to wrap a given property on object in a deprecation message\non any accessing or setting of the property. An optional message can be supplied\nto provide a custom message.\n\nThe method must be called on the object where the property belongs (not\ninherited from the prototype).\n\nIf the property is a data descriptor, it will be converted to an accessor\ndescriptor in order to display the deprecation message.\n\n### process.on('deprecation', fn)\n\nThis module will allow easy capturing of deprecation errors by emitting the\nerrors as the type \"deprecation\" on the global `process`. If there are no\nlisteners for this type, the errors are written to STDERR as normal, but if\nthere are any listeners, nothing will be written to STDERR and instead only\nemitted. From there, you can write the errors in a different format or to a\nlogging source.\n\nThe error represents the deprecation and is emitted only once with the same\nrules as writing to STDERR. The error has the following properties:\n\n  - `message` - This is the message given by the library\n  - `name` - This is always `'DeprecationError'`\n  - `namespace` - This is the namespace the deprecation came from\n  - `stack` - This is the stack of the call to the deprecated thing\n\nExample `error.stack` output:\n\n```\nDeprecationError: my-cool-module deprecated oldfunction\n    at Object.<anonymous> ([eval]-wrapper:6:22)\n    at Module._compile (module.js:456:26)\n    at evalScript (node.js:532:25)\n    at startup (node.js:80:7)\n    at node.js:902:3\n```\n\n### process.env.NO_DEPRECATION\n\nAs a user of modules that are deprecated, the environment variable `NO_DEPRECATION`\nis provided as a quick solution to silencing deprecation warnings from being\noutput. The format of this is similar to that of `DEBUG`:\n\n```sh\n$ NO_DEPRECATION=my-module,othermod node app.js\n```\n\nThis will suppress deprecations from being output for \"my-module\" and \"othermod\".\nThe value is a list of comma-separated namespaces. To suppress every warning\nacross all namespaces, use the value `*` for a namespace.\n\nProviding the argument `--no-deprecation` to the `node` executable will suppress\nall deprecations (only available in Node.js 0.8 or higher).\n\n**NOTE** This will not suppress the deperecations given to any \"deprecation\"\nevent listeners, just the output to STDERR.\n\n### process.env.TRACE_DEPRECATION\n\nAs a user of modules that are deprecated, the environment variable `TRACE_DEPRECATION`\nis provided as a solution to getting more detailed location information in deprecation\nwarnings by including the entire stack trace. The format of this is the same as\n`NO_DEPRECATION`:\n\n```sh\n$ TRACE_DEPRECATION=my-module,othermod node app.js\n```\n\nThis will include stack traces for deprecations being output for \"my-module\" and\n\"othermod\". The value is a list of comma-separated namespaces. To trace every\nwarning across all namespaces, use the value `*` for a namespace.\n\nProviding the argument `--trace-deprecation` to the `node` executable will trace\nall deprecations (only available in Node.js 0.8 or higher).\n\n**NOTE** This will not trace the deperecations silenced by `NO_DEPRECATION`.\n\n## Display\n\n![message](files/message.png)\n\nWhen a user calls a function in your library that you mark deprecated, they\nwill see the following written to STDERR (in the given colors, similar colors\nand layout to the `debug` module):\n\n```\nbright cyan    bright yellow\n|              |          reset       cyan\n|              |          |           |\n▼              ▼          ▼           ▼\nmy-cool-module deprecated oldfunction [eval]-wrapper:6:22\n▲              ▲          ▲           ▲\n|              |          |           |\nnamespace      |          |           location of mycoolmod.oldfunction() call\n               |          deprecation message\n               the word \"deprecated\"\n```\n\nIf the user redirects their STDERR to a file or somewhere that does not support\ncolors, they see (similar layout to the `debug` module):\n\n```\nSun, 15 Jun 2014 05:21:37 GMT my-cool-module deprecated oldfunction at [eval]-wrapper:6:22\n▲                             ▲              ▲          ▲              ▲\n|                             |              |          |              |\ntimestamp of message          namespace      |          |             location of mycoolmod.oldfunction() call\n                                             |          deprecation message\n                                             the word \"deprecated\"\n```\n\n## Examples\n\n### Deprecating all calls to a function\n\nThis will display a deprecated message about \"oldfunction\" being deprecated\nfrom \"my-module\" on STDERR.\n\n```js\nvar deprecate = require('depd')('my-cool-module')\n\n// message automatically derived from function name\n// Object.oldfunction\nexports.oldfunction = deprecate.function(function oldfunction () {\n  // all calls to function are deprecated\n})\n\n// specific message\nexports.oldfunction = deprecate.function(function () {\n  // all calls to function are deprecated\n}, 'oldfunction')\n```\n\n### Conditionally deprecating a function call\n\nThis will display a deprecated message about \"weirdfunction\" being deprecated\nfrom \"my-module\" on STDERR when called with less than 2 arguments.\n\n```js\nvar deprecate = require('depd')('my-cool-module')\n\nexports.weirdfunction = function () {\n  if (arguments.length < 2) {\n    // calls with 0 or 1 args are deprecated\n    deprecate('weirdfunction args < 2')\n  }\n}\n```\n\nWhen calling `deprecate` as a function, the warning is counted per call site\nwithin your own module, so you can display different deprecations depending\non different situations and the users will still get all the warnings:\n\n```js\nvar deprecate = require('depd')('my-cool-module')\n\nexports.weirdfunction = function () {\n  if (arguments.length < 2) {\n    // calls with 0 or 1 args are deprecated\n    deprecate('weirdfunction args < 2')\n  } else if (typeof arguments[0] !== 'string') {\n    // calls with non-string first argument are deprecated\n    deprecate('weirdfunction non-string first arg')\n  }\n}\n```\n\n### Deprecating property access\n\nThis will display a deprecated message about \"oldprop\" being deprecated\nfrom \"my-module\" on STDERR when accessed. A deprecation will be displayed\nwhen setting the value and when getting the value.\n\n```js\nvar deprecate = require('depd')('my-cool-module')\n\nexports.oldprop = 'something'\n\n// message automatically derives from property name\ndeprecate.property(exports, 'oldprop')\n\n// explicit message\ndeprecate.property(exports, 'oldprop', 'oldprop >= 0.10')\n```\n\n## License\n\n[MIT](LICENSE)\n\n[appveyor-image]: https://badgen.net/appveyor/ci/dougwilson/nodejs-depd/master?label=windows\n[appveyor-url]: https://ci.appveyor.com/project/dougwilson/nodejs-depd\n[coveralls-image]: https://badgen.net/coveralls/c/github/dougwilson/nodejs-depd/master\n[coveralls-url]: https://coveralls.io/r/dougwilson/nodejs-depd?branch=master\n[node-image]: https://badgen.net/npm/node/depd\n[node-url]: https://nodejs.org/en/download/\n[npm-downloads-image]: https://badgen.net/npm/dm/depd\n[npm-url]: https://npmjs.org/package/depd\n[npm-version-image]: https://badgen.net/npm/v/depd\n[travis-image]: https://badgen.net/travis/dougwilson/nodejs-depd/master?label=linux\n[travis-url]: https://travis-ci.org/dougwilson/nodejs-depd\n", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2022-06-15T00:47:42.530Z", "created": "2014-06-15T05:36:40.048Z", "0.0.0": "2014-06-15T05:36:40.048Z", "0.0.1": "2014-06-15T06:45:56.017Z", "0.1.0": "2014-06-15T23:00:43.255Z", "0.2.0": "2014-06-16T03:10:42.166Z", "0.3.0": "2014-06-17T05:12:24.584Z", "0.4.0": "2014-07-20T00:44:09.734Z", "0.4.1": "2014-07-20T01:00:25.153Z", "0.4.2": "2014-07-20T01:50:35.874Z", "0.4.3": "2014-07-26T20:16:57.765Z", "0.4.4": "2014-07-27T16:58:38.606Z", "0.4.5": "2014-09-09T23:35:40.588Z", "1.0.0": "2014-09-18T06:42:35.900Z", "1.0.1": "2015-04-07T17:50:07.601Z", "1.1.0": "2015-09-14T15:59:06.622Z", "1.1.1": "2017-07-27T23:10:29.750Z", "1.1.2": "2018-01-12T05:47:03.858Z", "2.0.0": "2018-10-26T17:52:55.936Z"}, "homepage": "https://github.com/dougwilson/nodejs-depd#readme", "repository": {"type": "git", "url": "git+https://github.com/dougwilson/nodejs-depd.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/dougwilson/nodejs-depd/issues"}, "license": "MIT", "readmeFilename": "Readme.md", "keywords": ["deprecate", "deprecated"], "users": {"306766053": true, "zhangyaochun": true, "forivall": true, "bacra": true, "cshao": true, "simplyianm": true, "yasinaydin": true, "moimikey": true, "zhanghaili": true, "wangnan0610": true, "lgh06": true, "ahmed-dinar": true, "mojaray2k": true, "danielrhayes": true, "leonzhao": true, "programmer.severson": true, "abhisekp": true, "daizch": true, "eyson": true, "tedyhy": true, "zuojiang": true, "zhenguo.zhao": true, "xgheaven": true, "isayme": true}}