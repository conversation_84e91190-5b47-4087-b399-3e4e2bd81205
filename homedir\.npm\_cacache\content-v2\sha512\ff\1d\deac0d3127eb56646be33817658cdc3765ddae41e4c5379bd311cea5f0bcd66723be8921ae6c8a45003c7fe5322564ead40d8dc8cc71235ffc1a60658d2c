{"_id": "yuglify", "_rev": "19-b44fa20b3fd82cef84c77c04783dd28d", "name": "yuglify", "description": "cli wrapper for uglify and cssmin used by YUI", "dist-tags": {"latest": "2.0.0"}, "versions": {"0.0.1": {"name": "yuglify", "description": "cli wrapper for uglify and cssmin used by YUI", "version": "0.0.1", "dependencies": {"uglify-js": "~1.3.3", "cssmin": "~0.3.0", "nopt": "*"}, "devDependencies": {"yui-lint": "~0.1.1", "jshint": "~0.9.0", "vows": "*"}, "main": "./lib/index.js", "bin": {"yuglify": "./bin/yuglify"}, "scripts": {"pretest": "jshint --config ./node_modules/yui-lint/jshint.json ./lib/*.js ./bin/yuglify", "test": "vows --spec ./tests/*.js"}, "preferGlobal": "true", "bugs": {"url": "http://github.com/yui/yuglify/issues"}, "licenses": [{"type": "BSD", "url": "https://github.com/yui/yuglify/blob/master/LICENSE"}], "repository": {"type": "git", "url": "http://github.com/yui/yuglify.git"}, "_id": "yuglify@0.0.1", "dist": {"shasum": "1b10d64817b55eba5b18aee9a0414fdc8c8b942d", "tarball": "https://registry.npmjs.org/yuglify/-/yuglify-0.0.1.tgz", "integrity": "sha512-7ayjbItb3SBpog8Mdq+GwEgiC9rvm8T/CQ+ax73PIs45qrPyVwC8kTAoOopwOTUMYyKdso53h0ysJW46k7QMRg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCjT/VaCRwjeHQG0jSzpUGsDmyRH1QNvSlbDlNbfnhrxAIgJRmPkKRbAk07kvkmLFxmzlQaA4FFdRGhANehOw3dIQQ="}]}, "_npmVersion": "1.1.62", "_npmUser": {"name": "davglass", "email": "<EMAIL>"}, "maintainers": [{"name": "davglass", "email": "<EMAIL>"}]}, "0.1.0": {"name": "yuglify", "description": "cli wrapper for uglify and cssmin used by YUI", "version": "0.1.0", "dependencies": {"uglify-js": "~1.3.3", "ycssmin": "~1.0.0", "nopt": "*"}, "devDependencies": {"yui-lint": "~0.1.1", "jshint": "~0.9.0", "vows": "*"}, "main": "./lib/index.js", "bin": {"yuglify": "./bin/yuglify"}, "scripts": {"pretest": "jshint --config ./node_modules/yui-lint/jshint.json ./lib/*.js ./bin/yuglify", "test": "vows --spec ./tests/*.js"}, "preferGlobal": "true", "bugs": {"url": "http://github.com/yui/yuglify/issues"}, "licenses": [{"type": "BSD", "url": "https://github.com/yui/yuglify/blob/master/LICENSE"}], "repository": {"type": "git", "url": "http://github.com/yui/yuglify.git"}, "_id": "yuglify@0.1.0", "dist": {"shasum": "35f2061580eea889c91e9b25ae08ee32bf634027", "tarball": "https://registry.npmjs.org/yuglify/-/yuglify-0.1.0.tgz", "integrity": "sha512-mYkD7rCl6DWXwbYKLS1l/5YASQonZNFZEsY2FLXPeYREmsHZrZ23zo+MhR7EiDTN7RFBCwPrH6JjJinRDWlutw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHVejjKbfbWkcwsP0/E1VjcqVoRHSJMRbXgVQ0Qg2wt+AiB63PBVD3sqwbwsgRbeYyYsSTrLQuzvJ/hqvkPqZGgmAQ=="}]}, "_npmVersion": "1.1.62", "_npmUser": {"name": "davglass", "email": "<EMAIL>"}, "maintainers": [{"name": "davglass", "email": "<EMAIL>"}]}, "0.1.1": {"name": "yuglify", "description": "cli wrapper for uglify and cssmin used by YUI", "version": "0.1.1", "dependencies": {"uglify-js": "~1.3.3", "ycssmin": "~1.0.0", "nopt": "*"}, "devDependencies": {"yui-lint": "~0.1.1", "jshint": "~0.9.0", "vows": "*"}, "main": "./lib/index.js", "bin": {"yuglify": "./bin/yuglify"}, "scripts": {"pretest": "jshint --config ./node_modules/yui-lint/jshint.json ./lib/*.js ./bin/yuglify", "test": "vows --spec ./tests/*.js"}, "preferGlobal": "true", "bugs": {"url": "http://github.com/yui/yuglify/issues"}, "licenses": [{"type": "BSD", "url": "https://github.com/yui/yuglify/blob/master/LICENSE"}], "repository": {"type": "git", "url": "http://github.com/yui/yuglify.git"}, "_id": "yuglify@0.1.1", "dist": {"shasum": "ec3df33cc0cd3b70d6e365e1598d712cf1147324", "tarball": "https://registry.npmjs.org/yuglify/-/yuglify-0.1.1.tgz", "integrity": "sha512-HWmYCUunDGwq4O89AdM3k4M+rFSnKiHspCNtTlBWyTJXu4cVmDr8sni7Wrg7fIyagJXGI8qugBjGKpf8i97aDA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDhW+EmBibb1xs1beAsNjUqaWH6bNW+GnDpBZijLvn+IQIgIsl7czQcB2SWo8HTD4x4hPYA5SYxUvKM+wbHymrbZK0="}]}, "_npmVersion": "1.1.63", "_npmUser": {"name": "davglass", "email": "<EMAIL>"}, "maintainers": [{"name": "davglass", "email": "<EMAIL>"}]}, "0.1.2": {"name": "yuglify", "description": "cli wrapper for uglify and cssmin used by YUI", "version": "0.1.2", "dependencies": {"uglify-js": "~1.3.3", "ycssmin": "~1.0.0", "nopt": "*"}, "devDependencies": {"yui-lint": "~0.1.1", "jshint": "~0.9.0", "vows": "*"}, "main": "./lib/index.js", "bin": {"yuglify": "./bin/yuglify"}, "scripts": {"pretest": "jshint --config ./node_modules/yui-lint/jshint.json ./lib/*.js ./bin/yuglify", "test": "vows --spec ./tests/*.js"}, "preferGlobal": "true", "bugs": {"url": "http://github.com/yui/yuglify/issues"}, "licenses": [{"type": "BSD", "url": "https://github.com/yui/yuglify/blob/master/LICENSE"}], "repository": {"type": "git", "url": "http://github.com/yui/yuglify.git"}, "_id": "yuglify@0.1.2", "dist": {"shasum": "2ebae34120a82f97d9893397efcab6825a1fdd6f", "tarball": "https://registry.npmjs.org/yuglify/-/yuglify-0.1.2.tgz", "integrity": "sha512-2jWkng2VtGx3Qhse3fdU5IqerKjMQguYGRjePfxGikMh6EysSxjYjA6EMPL4OGiszAlZdhPWMeHAIYYmW1yX3A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDu7HbDtvjMzOwB+R1NiKRTb/zpYJ6Ri1mDlFstRv8RJAiALio7x21c2LohYarn/uD5EEV8AQC9x28bJbfH1xvHnOg=="}]}, "_npmVersion": "1.1.69", "_npmUser": {"name": "davglass", "email": "<EMAIL>"}, "maintainers": [{"name": "davglass", "email": "<EMAIL>"}]}, "0.1.3": {"name": "yuglify", "description": "cli wrapper for uglify and cssmin used by YUI", "version": "0.1.3", "dependencies": {"uglify-js": "~1.3.4", "ycssmin": "~1.0.1", "nopt": "~2.1.1"}, "devDependencies": {"yui-lint": "~0.1.1", "jshint": "~0.9.0", "vows": "*"}, "main": "./lib/index.js", "bin": {"yuglify": "./bin/yuglify"}, "scripts": {"pretest": "jshint --config ./node_modules/yui-lint/jshint.json ./lib/*.js ./bin/yuglify", "test": "vows --spec ./tests/*.js"}, "preferGlobal": "true", "bugs": {"url": "http://github.com/yui/yuglify/issues"}, "licenses": [{"type": "BSD", "url": "https://github.com/yui/yuglify/blob/master/LICENSE"}], "repository": {"type": "git", "url": "http://github.com/yui/yuglify.git"}, "_id": "yuglify@0.1.3", "dist": {"shasum": "11d095665b425c1e962141001c95daeb3f8cc9ed", "tarball": "https://registry.npmjs.org/yuglify/-/yuglify-0.1.3.tgz", "integrity": "sha512-qvN6FfM6R086/29ZbyRJr2am6+tBNnbeDsCR2bVUepsLgP92+fANDWP3oDd8uv2hPfz2Z7erxnt9miyVl7OKVA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD1yq0PJiOxsZRm7Q1/GfjBR7SN5yCqkgB1AW8egeALpAIgOJIBj/TmbhSHS9yHLgELOUBSNPZni7YHT53371rhv5o="}]}, "_from": ".", "_npmVersion": "1.2.15", "_npmUser": {"name": "davglass", "email": "<EMAIL>"}, "maintainers": [{"name": "davglass", "email": "<EMAIL>"}]}, "0.1.4": {"name": "yuglify", "description": "cli wrapper for uglify and cssmin used by YUI", "version": "0.1.4", "dependencies": {"uglify-js": "~1.3.4", "ycssmin": "~1.0.1", "nopt": "~2.1.1"}, "devDependencies": {"yui-lint": "~0.1.1", "jshint": "~0.9.0", "vows": "*"}, "main": "./lib/index.js", "bin": {"yuglify": "./bin/yuglify"}, "scripts": {"pretest": "jshint --config ./node_modules/yui-lint/jshint.json ./lib/*.js ./bin/yuglify", "test": "vows --spec ./tests/*.js"}, "preferGlobal": "true", "bugs": {"url": "http://github.com/yui/yuglify/issues"}, "licenses": [{"type": "BSD", "url": "https://github.com/yui/yuglify/blob/master/LICENSE"}], "repository": {"type": "git", "url": "http://github.com/yui/yuglify.git"}, "_id": "yuglify@0.1.4", "dist": {"shasum": "726d5e4af810f741ef21865f6f954c4078a3a45f", "tarball": "https://registry.npmjs.org/yuglify/-/yuglify-0.1.4.tgz", "integrity": "sha512-czTSueZ6TtBbWIG+wSoD2wNMnLgn0Fy1Ze8087KXdO2j6Dy7MUOdH5CQ9XglPGVbIJk8j24i4a/25G1HzTi/2A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICbxHmqD0GysqYSxXjHmDF7dekfXmw8kn2uEeqWGyVdDAiEAvVMONoMC+lOsPmIq0C+vdAUtwbsZPcyccv1xxS+P4i4="}]}, "_from": ".", "_npmVersion": "1.2.15", "_npmUser": {"name": "davglass", "email": "<EMAIL>"}, "maintainers": [{"name": "davglass", "email": "<EMAIL>"}]}, "1.0.0": {"name": "yuglify", "description": "cli wrapper for uglify and cssmin used by YUI", "version": "1.0.0", "dependencies": {"mocha": "^3.4.2", "nopt": "~2.1.1", "uglify-js": "^2.8.29", "ycssmin": "~1.0.1"}, "devDependencies": {"jshint": "^2.9.5", "vows": "*", "yui-lint": "^0.2.0"}, "main": "./lib/index.js", "bin": {"yuglify": "./bin/yuglify"}, "scripts": {"pretest": "jshint --config ./node_modules/yui-lint/jshint.json ./lib/*.js ./bin/yuglify", "test": "vows --spec ./tests/*.js && mocha mocha-tests --recursive"}, "preferGlobal": "true", "bugs": {"url": "http://github.com/yui/yuglify/issues"}, "licenses": [{"type": "BSD", "url": "https://github.com/yui/yuglify/blob/master/LICENSE"}], "repository": {"type": "git", "url": "git+ssh://**************/yui/yuglify.git"}, "gitHead": "376ba094c6d5f085e6b670a83710a90a0b470105", "homepage": "https://github.com/yui/yuglify#readme", "_id": "yuglify@1.0.0", "_npmVersion": "5.0.3", "_nodeVersion": "8.1.4", "_npmUser": {"name": "davglass", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-bOy+1Acy0GT5pv9laqTkvdNSMJdW1BZoOWZ6p01O85GwS3dSdZco1VSQOPHmF+W2C3sO8PcvBSRcs+lOy5xBjw==", "shasum": "3110f1bdd3afae1fecd06b7a77e08f2cbf6f632b", "tarball": "https://registry.npmjs.org/yuglify/-/yuglify-1.0.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCXkPMtsZFb4zlU2wrk0kH/obripx+i77Dty20z2NctmwIhAKZijACokwVr8ajHLQVfzieanlTNwZoWuhZarmI/phD4"}]}, "maintainers": [{"name": "davglass", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yuglify-1.0.0.tgz_1500060830365_0.4220444792881608"}}, "1.0.1": {"name": "yuglify", "description": "cli wrapper for uglify and cssmin used by YUI", "version": "1.0.1", "dependencies": {"nopt": "~2.1.1", "uglify-js": "^2.8.29", "ycssmin": "~1.0.1"}, "devDependencies": {"jshint": "^2.9.5", "mocha": "^3.4.2", "vows": "*", "yui-lint": "^0.2.0"}, "main": "./lib/index.js", "bin": {"yuglify": "./bin/yuglify"}, "scripts": {"pretest": "jshint --config ./node_modules/yui-lint/jshint.json ./lib/*.js ./bin/yuglify", "test": "vows --spec ./tests/*.js && mocha mocha-tests --recursive"}, "preferGlobal": "true", "bugs": {"url": "http://github.com/yui/yuglify/issues"}, "licenses": [{"type": "BSD", "url": "https://github.com/yui/yuglify/blob/master/LICENSE"}], "repository": {"type": "git", "url": "git+ssh://**************/yui/yuglify.git"}, "gitHead": "24200ba5a7307ba8eb824097cf3debc90ee4ed3f", "homepage": "https://github.com/yui/yuglify#readme", "_id": "yuglify@1.0.1", "_npmVersion": "5.0.3", "_nodeVersion": "8.1.4", "_npmUser": {"name": "davglass", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-uK50QI3MrMciW0dW9b0U/f5cNM7v4hiwWoUrrDtdF4MGOj9BnQPF/Z15jlyQcywNevGPd1hvcsWM7Hh3NojKEA==", "shasum": "dba003683b786542e0e3aaa6cbc2ca0afbea51c5", "tarball": "https://registry.npmjs.org/yuglify/-/yuglify-1.0.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC3Avto8Fj3/SjaizsrOi+0ez4TpPUr660mKjmJA9uNiQIgLxHMwo13M4Cxl8IxHERuh0AcOdT5TqEhYMMR92fjzyI="}]}, "maintainers": [{"name": "davglass", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yuglify-1.0.1.tgz_1500064331813_0.4147350685670972"}}, "2.0.0": {"name": "yuglify", "description": "cli wrapper for uglify and cssmin used by YUI", "version": "2.0.0", "dependencies": {"nopt": "~2.1.1", "uglify-js": "^3.1.4", "ycssmin": "~1.0.1"}, "devDependencies": {"jshint": "^2.9.5", "mocha": "^3.4.2", "vows": "*", "yui-lint": "^0.2.0"}, "main": "./lib/index.js", "bin": {"yuglify": "./bin/yuglify"}, "scripts": {"pretest": "jshint --config ./node_modules/yui-lint/jshint.json ./lib/*.js ./bin/yuglify", "test": "vows --spec ./tests/*.js && mocha mocha-tests --recursive"}, "preferGlobal": "true", "bugs": {"url": "http://github.com/yui/yuglify/issues"}, "licenses": [{"type": "BSD", "url": "https://github.com/yui/yuglify/blob/master/LICENSE"}], "repository": {"type": "git", "url": "git+ssh://**************/yui/yuglify.git"}, "gitHead": "7792e5884a0f84e2cce1e600545c0abd42c76a25", "homepage": "https://github.com/yui/yuglify#readme", "_id": "yuglify@2.0.0", "_shasum": "9d4e5addce20f41edf22486e2311b61d6a3f8d21", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.10.3", "_npmUser": {"name": "davglass", "email": "<EMAIL>"}, "dist": {"shasum": "9d4e5addce20f41edf22486e2311b61d6a3f8d21", "tarball": "https://registry.npmjs.org/yuglify/-/yuglify-2.0.0.tgz", "integrity": "sha512-H5bf7+JO9htUtORqDGYiFAeZjrAQXVU7l2ZX1EYDQFJENjex2pSixhmE6SMSnntzHMeraQhcIXPFdA3YF9ssMA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCrAJh9+/Arx4cVNutF/DzDJyNR3cVP3WvsrW0SmCJqqgIgQwZbX7x3munzvP5kHN1M+s15oP0v+ccjLCrYunvYess="}]}, "maintainers": [{"name": "davglass", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yuglify-2.0.0.tgz_1513887854914_0.8060420947149396"}}}, "readme": "yUglify\n=======\n\n`yuglify` is a wrapper around [UglifyJS](https://github.com/mishoo/UglifyJS) and [cssmin](https://github.com/jbleuzen/node-cssmin)\nwith the default YUI configurations on each of them.\n\n\nBuild Status\n------------\n\n[![Build Status](https://secure.travis-ci.org/yui/yuglify.png?branch=master)](http://travis-ci.org/yui/yuglify)\n\n\nCLI Usage\n----------\n\n    npm -g install yuglify\n\n`yuglify` has a very simple CLI interface to allow you to compress files from the command line.\n\n    yuglify ./lib/*.js #uses shell globbing, won't work on Windows\n\nThis will read all passed files and compress them (js or css) and write them back beside the original\nwith the name altered to `.min.js|css`.\n\nRequired\n--------\n\n    npm install yuglify\n\n\n```javascript\n\nvar yuglify = require('yuglify');\n\nyuglify.jsmin('<string of source', function(err, smashed) {\n    fs.writeFile('/path/to/file', smashed, 'utf8', function() {});\n});\n\nyuglify.cssmin('<string of source', function(err, smashed) {\n    fs.writeFile('/path/to/file', smashed, 'utf8', function() {});\n});\n\n```\n\nPurpose\n-------\n\nThis module is primarily designed to be used inside [shifter](http://yui.github.com/shifter/).\n\nWhy not use the default Uglify?\n-------------------------------\n\nWe need to support the `/*!` license comment blocks when minifying, so we added\na preprocessor to the code to pull them from the source, then place them back when\nthe minification is complete.\n\nWe also needed to make sure that the file ends in a clean line ending for our\ncombo servers. This way we ensure that other modules don't have to end with a\nsemi-colon and the combohandler doesn't concat them together in a bad way.\n\nWe've also added support to add a semi-colon if the last character of the\nminified source is either a `)` or a `}`.\n\nThe last thing this module does is provide the default config that we think\nis the most compatible with the way that YUI Compressor used to minify our\nfiles.\n\n```javascript\n{\n    mangle: true,\n    squeeze: true,\n    semicolon: false,\n    lift_vars: false,\n    mangle_toplevel: true,\n    no_mangle_functions: true,\n    max_line_length: 6000\n}\n```\n\nTesting\n-------\n\nCurrently, the tests for this module are just to make sure that they are exported properly.\nShifter's test suite validates that these compressors are working as expected. Soon, we'll\nmove them over to this repo too.\n", "maintainers": [{"name": "davglass", "email": "<EMAIL>"}], "time": {"modified": "2022-06-29T08:26:27.256Z", "created": "2012-10-10T13:36:32.201Z", "0.0.1": "2012-10-10T13:36:33.409Z", "0.1.0": "2012-10-15T16:00:06.349Z", "0.1.1": "2012-10-17T13:19:12.132Z", "0.1.2": "2013-01-04T14:41:13.831Z", "0.1.3": "2013-04-02T13:35:02.178Z", "0.1.4": "2013-04-02T16:05:34.716Z", "1.0.0": "2017-07-14T19:33:51.311Z", "1.0.1": "2017-07-14T20:32:12.798Z", "2.0.0": "2017-12-21T20:24:15.009Z"}, "repository": {"type": "git", "url": "git+ssh://**************/yui/yuglify.git"}, "homepage": "https://github.com/yui/yuglify#readme", "bugs": {"url": "http://github.com/yui/yuglify/issues"}, "readmeFilename": "README.md"}