{"name": "buffer-from", "dist-tags": {"latest": "1.1.2"}, "versions": {"0.1.0": {"name": "buffer-from", "version": "0.1.0", "devDependencies": {"standard": "^7.1.2"}, "dist": {"shasum": "a49dce54d048e955595d22e983ba5fe8aed5ffdd", "tarball": "https://registry.npmjs.org/buffer-from/-/buffer-from-0.1.0.tgz", "integrity": "sha512-VB11PczNxawoNdSfx9Wtp3e7u32ZvxtoTmBy3FBSF1AknhVjJIn6imh5IHn0/3jHGcjRxFDk9gkiemOtxfvKiQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHOTbZuHjJgi4b4Y0v7QTkDqdZRskrDXsVUjdV/wZ1oaAiAUhEup+ldUmX/NZ+OGfRLPiW2NuqmlsVpl/9S0Q5Qm2g=="}]}}, "0.1.1": {"name": "buffer-from", "version": "0.1.1", "dependencies": {"is-array-buffer-x": "^1.0.13"}, "devDependencies": {"standard": "^7.1.2"}, "dist": {"shasum": "57b18b1da0a19ec06f33837a5275a242351bd75e", "tarball": "https://registry.npmjs.org/buffer-from/-/buffer-from-0.1.1.tgz", "integrity": "sha512-ojL8pkJEJceHwDvyOXDlgJWLm2GruKWrykCPPfh1UBccsKsCd3QxUD9DinrU8DJsaSd/cug76qKYbiYcBFUNww==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCH91BBaXLWErR9u+lrQRYcQ5MYbELr/AuQHsYN5+RWqwIhANR5pzgHc5juVEqeMYnkNpPN+YmgC5VN2UILu71yXc1Y"}]}}, "0.1.2": {"name": "buffer-from", "version": "0.1.2", "devDependencies": {"standard": "^7.1.2"}, "dist": {"integrity": "sha512-RiWIenusJsmI2KcvqQABB83tLxCByE3upSP8QU3rJDMVFGPWLvPQJt/O1Su9moRWeH7d+Q2HYb68f6+v+tw2vg==", "shasum": "15f4b9bcef012044df31142c14333caf6e0260d0", "tarball": "https://registry.npmjs.org/buffer-from/-/buffer-from-0.1.2.tgz", "fileCount": 4, "unpackedSize": 4250, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC36wb1f4oIP0Fw03snth62xyHhNp4QhZWl+7lwtLXi1AiEArX4FrsGq89Wydp7vHqmow5mypBugX+JY7vswkX2sv6U="}]}}, "1.0.0": {"name": "buffer-from", "version": "1.0.0", "devDependencies": {"standard": "^7.1.2"}, "dist": {"integrity": "sha512-83apNb8KK0Se60UE1+4Ukbe3HbfELJ6UlI4ldtOGs7So4KD26orJM8hIY9lxdzP+UpItH1Yh/Y8GUvNFWFFRxA==", "shasum": "4cb8832d23612589b0406e9e2956c17f06fdf531", "tarball": "https://registry.npmjs.org/buffer-from/-/buffer-from-1.0.0.tgz", "fileCount": 4, "unpackedSize": 4250, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD/nmfoa7Q2oZmWH0dJsYeOWQn/y0vwN09yWftZ8kIeLAIgV9LEvesCXOPIAXgvVdtGJzOs9CH/30yxdQ0LXB+TC3Q="}]}}, "1.1.0": {"name": "buffer-from", "version": "1.1.0", "devDependencies": {"standard": "^7.1.2"}, "dist": {"integrity": "sha512-c5mRlguI/Pe2dSZmpER62rSCu0ryKmWddzRYsuXc50U2/g8jMOulc31VZMa4mYx31U5xsmSOpDCgH88Vl9cDGQ==", "shasum": "87fcaa3a298358e0ade6e442cfce840740d1ad04", "tarball": "https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.0.tgz", "fileCount": 3, "unpackedSize": 3888, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbDeRgCRA9TVsSAnZWagAAd9EP/2tgjGpg0D2foCpZEQKf\nCT1dXMK17+ogbS9ra2xGjuPclU3fkXMihwUINK7/xMoxxFo8fGOwMFPblOHM\nBfO8mEyUyWD8X2SNGwIC3thzobtiQFSiQIzGWag5G6TZxKcnhWXWDcRnGBeu\nfYF5N9K6TDItxL920LlzqJ3Y7glr4Wv7SDJyrzVBfEIGmfgM8aVad5MZNeEK\nv+Nzt/kd2jt5PvUoOoY826q03Vg14awD8vx9tJc6AoaFCOnOBnD/1uAqeg4l\nW404tmnqtfCBdSZj+/iv0xdjJSZqqInsiXK01+HNxorY7mSTc0Bdh0b8tnkA\njONz5g0k5a4S8Y1LYVfNg7MvmryxQFtdBCJj15eYVU5pYkR8AohCOkNF6vCH\nWPjS3FOfY3y6/ffbY32YhLnDAhIMk3leI9UIc2KvbQ0/bJgXHYRhrdU2Iumx\nmrx43cXbwTqLjngV23UISic6/5jJxyUbUYmjTL72j71bU61sZ/AWn8MwAXEY\nN56xPgL8MztdIcwHV+QqjekkP8YLiLutqAui42EtIxd8vZamypd2lOKiKEh6\nyJ641PVOvCKiIEq4F+9jqy0HtNnUIwZ5wOR+CMHwkaM4BkszVbviUA/9B0+K\n8dF13Js/z5YenP+8Atp4CUyAebmCrmJ5prFvFD+blLVoQSMjqz8KvHvHbNrh\nJ6rs\r\n=2hhN\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBTe6zGexIXMuWGkMiZbPTA0mzpKkDEsGrBDdX0XhWTnAiAno9YgbbuoBbvIAUztJ9agx+rUsrGgxC9T7seApUP1FA=="}]}}, "1.1.1": {"name": "buffer-from", "version": "1.1.1", "devDependencies": {"standard": "^7.1.2"}, "dist": {"integrity": "sha512-MQcXEUbCKtEo7bhqEs6560Hyd4XaovZlO/k9V3hjVUF/zwW7KBVdSK4gIt/bzwS9MbR5qob+F5jusZsb0YQK2A==", "shasum": "32713bc028f75c02fdb710d7c7bcec1f2c6070ef", "tarball": "https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.1.tgz", "fileCount": 4, "unpackedSize": 4966, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbYHUTCRA9TVsSAnZWagAAgoQP/1LXApc57d1uAc/0mnLq\nCI87dq7EtKS4lG7EjFY3yzxIZxlq1++vH0/sWe0V8je9sHhrm/AkSxlcplEV\nWELPjRwIY4h3LP6U5ppG7XAVGmbipkIAGhiRaJtqGfI/TgPRiB3QcrqanlRG\nBf4oDubBJ2vK8dh5Gd0AhAkkTOsIcf4b5uC64A5GUtMoPOrko+zyRYM8IrRo\n7kDspGfsk3YYxyB/L0APD4AIC8vYz2Gin2ca+zJBZH6cvfThhFWbSnCFfVuW\nxXcheune4boTojusOn5dm4LxzFyMgbeibDZTaq+Yod3RuuUVaSCXWNITcT6g\nIlP7wdbBFGx7Ik5hKEtAGtsCdWshzg9YxHR7g7CPe+0xIeb0zNeKBlxFF2jT\niPeiI+JGlMZuH2Qu+284T9WWom/j0NcbZuWmrV5E1GUimVJqtgqD3S3ivc4y\n6fS6OTIemRUmWwmS7farPxzTs34QkUp0vH/dhuvNeZKockTjLvlveHlOtC4m\nhGp5UdiwrsV7Xqqy/RHlACxPjexkDfhwi0FSeY799VkzbgrPm7Npwn8HwMDI\nv6qpaxPbruRJx65zp1J4DqWlhYENreSFOaXGY/t34GIQ1yNHXGg4Z2ltshPi\nb3lJuLEolQYK5GAcryVnAGusAisYQDfygT1BPxYsVT2ZWYmicpHs7UD+70ya\nfbJa\r\n=YgJu\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGOpkDuA6FlIl3GH4TNUaaSfuNzhyYhU8BlqyHab5Ns2AiB5Y0974Ito8JACG79Z3KLYKOMEeY70O9aPG9PmzEOOmA=="}]}}, "1.1.2": {"name": "buffer-from", "version": "1.1.2", "devDependencies": {"standard": "^12.0.1"}, "dist": {"integrity": "sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==", "shasum": "2b146a6fd72e80b4f55d255f35ed59a3a9a41bd5", "tarball": "https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.2.tgz", "fileCount": 4, "unpackedSize": 5047, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhAt/dCRA9TVsSAnZWagAAK3gP/3U4DZr6cDPvIUqEzn6Z\npqnUZD8mwnMtuFYU2LFuB1/biFBrLv0Mph2aXBh8k2nCjKoMuqy7AKGVkv7q\n1ieA/JAHWdEhDuo82I+Hi0qqzw9jpwp7l5mLFTdFX90HtA8BOvyWGDkdg3pL\nbFVP1dxBHu8LI3zrWnd2ruAYOoylSXNysJuXwaSqV6OToz/8ZRYrbyzZC/4m\noqxS6WdflQcs7jSLJkBZSHTV6rmCoDg3FbB5zLiJOUfj4oUM/JnlkN6cR54/\nhXVJo0s0+Rqb8KL7+tR3pj+sjJQqtzvPi21sMBbkg/btayJs9DUJSEp6h0/x\nfSgTBGVaPT5jbAM45e2KmkAI78eLlpwNUZzM34FFuYj8Zl1Fjnckdj7Fgadn\nJLpv283KG4oYY5yF16inPIaAh7hf74eNVx+KBjTs4jOt89CDFEmViANgV5+e\nLhS21CdMMkPIuA4xnB+Ya5jeC19Z03gKSN+t5vA2nFK2l0K7/JUMzF4jICUz\nr2q5kt3RiN2/EH3I4wYUnE6cuOKWmxF4IpB9rNU16ARV5Bxq4zp42JoDgGie\nbpaVM8YWvkMAj0eBXJ+pW5JSDdQnwpuiaPx9ry3ZfpYv9QkCWjr0qibSsXu+\n10CK9AbxTy+QRPNYkTn/te7WH2bc/V3l80ypNEnXJJICKWgOzgH4W4kG1fjH\nHMP0\r\n=2uut\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICwILuKQPZZQ6XxAN2DiGeVk/fpq0Ls5VaL1Sxgl4JGcAiAubelJ6nbraK71jpE/QRm7uePA3TtO4Yo/sXvSly8W7Q=="}]}}}, "modified": "2022-06-13T05:15:19.171Z"}