{"_id": "randombytes", "_rev": "31-71615784934f6a5e0035992b983ad7e5", "name": "randombytes", "description": "random bytes from browserify stand alone", "dist-tags": {"latest": "2.1.0"}, "versions": {"1.0.0": {"name": "randombytes", "version": "1.0.0", "description": "random bytes from browserify stand alone", "main": "index.js", "scripts": {"test": "node test.js | tspec"}, "repository": {"type": "git", "url": "**************:crypto-browserify/randombytes.git"}, "keywords": ["crypto", "random"], "author": "", "license": "MIT", "bugs": {"url": "https://github.com/crypto-browserify/randombytes/issues"}, "homepage": "https://github.com/crypto-browserify/randombytes", "browser": "randomBytes.js", "devDependencies": {"tap-spec": "^2.1.2", "tape": "^3.0.3"}, "gitHead": "9e99f197e01a17b47c88ae0e6eaf1a2199737c2a", "_id": "randombytes@1.0.0", "_shasum": "a52ff9f435cdecaea97cdeecb092303967dbec76", "_from": ".", "_npmVersion": "2.1.10", "_nodeVersion": "0.10.35", "_npmUser": {"name": "cwmma", "email": "<EMAIL>"}, "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}], "dist": {"shasum": "a52ff9f435cdecaea97cdeecb092303967dbec76", "tarball": "https://registry.npmjs.org/randombytes/-/randombytes-1.0.0.tgz", "integrity": "sha512-zCVUP2aYdX2KuY6G9c4gCspWaSvryGKJFUmElXc2sp7q5m5UbKWk5MUFYMSgdM/H0kvVdn/VWPkd9ajXFozM5A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDGD70DiVIMStaO+isuCafVjb5SHptAWHtY/Y5jdSlefwIhALGIAHdTrBZzISb7M21gZH/WuFpEBfHq28p9DvMrUBqF"}]}, "directories": {}}, "2.0.0": {"name": "randombytes", "version": "2.0.0", "description": "random bytes from browserify stand alone", "main": "index.js", "scripts": {"test": "node test.js | tspec"}, "repository": {"type": "git", "url": "**************:crypto-browserify/randombytes.git"}, "keywords": ["crypto", "random"], "author": "", "license": "MIT", "bugs": {"url": "https://github.com/crypto-browserify/randombytes/issues"}, "homepage": "https://github.com/crypto-browserify/randombytes", "browser": "browser.js", "devDependencies": {"tap-spec": "^2.1.2", "tape": "^3.0.3"}, "gitHead": "1d47bae0f725dcf40bfc5df148365b17b1d54827", "_id": "randombytes@2.0.0", "_shasum": "e59d471c66e3537f85e1ca2e2b0511e9cc2115b6", "_from": ".", "_npmVersion": "2.0.0", "_npmUser": {"name": "cwmma", "email": "<EMAIL>"}, "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "dcousens", "email": "<EMAIL>"}, {"name": "dominictarr", "email": "<EMAIL>"}, {"name": "jp", "email": "jp<PERSON><PERSON><EMAIL>"}], "dist": {"shasum": "e59d471c66e3537f85e1ca2e2b0511e9cc2115b6", "tarball": "https://registry.npmjs.org/randombytes/-/randombytes-2.0.0.tgz", "integrity": "sha512-yv0Q+KnDnkpTGfnvkx3ktHPQoMHGpicOqEFP/F7vLeco9uzDc7kK5X+UQehD28tt2IlvdiLEWN3wBVxVtyJBsg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIExEo+6ruaszMbne86baA96mObQicg5YCSPuLvpZHNXxAiEAyIiLS/IbMfMmu2nVckAudvX60b1M9w8RlA1xTpGiETI="}]}, "directories": {}}, "2.0.1": {"name": "randombytes", "version": "2.0.1", "description": "random bytes from browserify stand alone", "main": "index.js", "scripts": {"test": "node test.js | tspec"}, "repository": {"type": "git", "url": "**************:crypto-browserify/randombytes.git"}, "keywords": ["crypto", "random"], "author": "", "license": "MIT", "bugs": {"url": "https://github.com/crypto-browserify/randombytes/issues"}, "homepage": "https://github.com/crypto-browserify/randombytes", "browser": "browser.js", "devDependencies": {"tap-spec": "^2.1.2", "tape": "^3.0.3"}, "gitHead": "849c4a44af3275dd1fa5ae221e5e87304b43e165", "_id": "randombytes@2.0.1", "_shasum": "18f4a9ba0dd07bdb1580bc9156091fcf90eabc6f", "_from": ".", "_npmVersion": "2.2.0", "_nodeVersion": "0.10.35", "_npmUser": {"name": "dcousens", "email": "<EMAIL>"}, "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "dcousens", "email": "<EMAIL>"}, {"name": "dominictarr", "email": "<EMAIL>"}, {"name": "jp", "email": "jp<PERSON><PERSON><EMAIL>"}], "dist": {"shasum": "18f4a9ba0dd07bdb1580bc9156091fcf90eabc6f", "tarball": "https://registry.npmjs.org/randombytes/-/randombytes-2.0.1.tgz", "integrity": "sha512-siCt2duOdZbmvgk8IDL4U0SYXI8ypBEKWuor0qUpHBWAyOCrXQvSIYJ+VKuEpoX36moZ1pAu+mXkwUVAVssu6w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCtYb9NPPaiTej/z8VrnvVPoNed0WZV93Q3FgCHc23pvAIge4X3T7xUVGxrRVM0XtKGCgwLR9B9qOIMlq9v8M92hyw="}]}, "directories": {}}, "2.0.2": {"name": "randombytes", "version": "2.0.2", "description": "random bytes from browserify stand alone", "main": "index.js", "scripts": {"test": "standard && node test.js | tspec", "phantom": "zuul --phantom -- test.js", "local": "zuul --local --no-coverage -- test.js"}, "repository": {"type": "git", "url": "git+ssh://**************/crypto-browserify/randombytes.git"}, "keywords": ["crypto", "random"], "author": "", "license": "MIT", "bugs": {"url": "https://github.com/crypto-browserify/randombytes/issues"}, "homepage": "https://github.com/crypto-browserify/randombytes", "browser": "browser.js", "devDependencies": {"phantomjs": "^1.9.9", "standard": "^3.3.0", "tap-spec": "^2.1.2", "tape": "^3.0.3", "zuul": "^3.7.2"}, "gitHead": "9e49a0c722df7755376cfed04aeca651526540e5", "_id": "randombytes@2.0.2", "_shasum": "f5910f4bc57ea8eccc67e32e0b52adf0ae011e43", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.4.0", "_npmUser": {"name": "cwmma", "email": "<EMAIL>"}, "dist": {"shasum": "f5910f4bc57ea8eccc67e32e0b52adf0ae011e43", "tarball": "https://registry.npmjs.org/randombytes/-/randombytes-2.0.2.tgz", "integrity": "sha512-0ik5pRUAsd93cR0OjLJq+C7FU1bpuPDaJgfU8RiPNwTYm4pe6BVEzCGiY8tJG0FIJEdsbNDU3vnMF5OT9tbBTQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBdt2v02K91EO+Ns4ClyQyAVqXabfLhZwvDx96F5OTe2AiEAkfmO70Q9P+krumKzZVGsXQyPhi0KCWNedpxawgrLSwg="}]}, "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "dcousens", "email": "<EMAIL>"}, {"name": "dominictarr", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jp<PERSON><PERSON><EMAIL>"}, {"name": "indutny", "email": "<EMAIL>"}], "directories": {}}, "2.0.3": {"name": "randombytes", "version": "2.0.3", "description": "random bytes from browserify stand alone", "main": "index.js", "scripts": {"test": "standard && node test.js | tspec", "phantom": "zuul --phantom -- test.js", "local": "zuul --local --no-coverage -- test.js"}, "repository": {"type": "git", "url": "git+ssh://**************/crypto-browserify/randombytes.git"}, "keywords": ["crypto", "random"], "author": "", "license": "MIT", "bugs": {"url": "https://github.com/crypto-browserify/randombytes/issues"}, "homepage": "https://github.com/crypto-browserify/randombytes", "browser": "browser.js", "devDependencies": {"phantomjs": "^1.9.9", "standard": "^3.3.0", "tap-spec": "^2.1.2", "tape": "^3.0.3", "zuul": "^3.7.2"}, "gitHead": "f466c002a706ee11dadf3b6aa0a6e9bf1c37c82d", "_id": "randombytes@2.0.3", "_shasum": "674c99760901c3c4112771a31e521dc349cc09ec", "_from": ".", "_npmVersion": "3.6.0", "_nodeVersion": "5.5.0", "_npmUser": {"name": "cwmma", "email": "<EMAIL>"}, "dist": {"shasum": "674c99760901c3c4112771a31e521dc349cc09ec", "tarball": "https://registry.npmjs.org/randombytes/-/randombytes-2.0.3.tgz", "integrity": "sha512-lDVjxQQFoCG1jcrP06LNo2lbWp4QTShEXnhActFBwYuHprllQV6VUpwreApsYqCgD+N1mHoqJ/BI/4eV4R2GYg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDQTjZdSzCjaeaWyya1BQe5n+2O8wLfAhaHcIHDpmJ6ZwIgGCoYWue8HfKPYp5GumlEd+5SoL3h9VJkCsiv4Tu20mg="}]}, "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "dcousens", "email": "<EMAIL>"}, {"name": "dominictarr", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jp<PERSON><PERSON><EMAIL>"}, {"name": "indutny", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-13-west.internal.npmjs.com", "tmp": "tmp/randombytes-2.0.3.tgz_1456944700918_0.5908831511624157"}, "directories": {}}, "2.0.4": {"name": "randombytes", "version": "2.0.4", "description": "random bytes from browserify stand alone", "main": "index.js", "scripts": {"test": "standard && node test.js | tspec", "phantom": "zuul --phantom -- test.js", "local": "zuul --local --no-coverage -- test.js"}, "repository": {"type": "git", "url": "git+ssh://**************/crypto-browserify/randombytes.git"}, "keywords": ["crypto", "random"], "author": "", "license": "MIT", "bugs": {"url": "https://github.com/crypto-browserify/randombytes/issues"}, "homepage": "https://github.com/crypto-browserify/randombytes", "browser": "browser.js", "devDependencies": {"phantomjs": "^1.9.9", "standard": "^10.0.2", "tap-spec": "^2.1.2", "tape": "^4.6.3", "zuul": "^3.7.2"}, "dependencies": {"safe-buffer": "^5.0.1"}, "gitHead": "18447371fa78b23ee95b1561f28a67b21b0067e1", "_id": "randombytes@2.0.4", "_shasum": "9551df208422c8f80eb58e2326dd0b840ff22efd", "_from": ".", "_npmVersion": "4.6.1", "_nodeVersion": "7.10.0", "_npmUser": {"name": "dcousens", "email": "<EMAIL>"}, "dist": {"shasum": "9551df208422c8f80eb58e2326dd0b840ff22efd", "tarball": "https://registry.npmjs.org/randombytes/-/randombytes-2.0.4.tgz", "integrity": "sha512-91PiBYZc53fbddGd2m/NG8qS+EqjNUNTW4xy552xu+A30dfmHEiSfTUG1nsUxz+OKbHWb/f8iYwEQAcloCWR/Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGIqxN4z2tcYzK04X8mFK1MO8ALoUn+7rsTvNxS98egGAiEA2nBIzDLLYpoGjkm+hJiMWe/yUT1SyyKnCM4trPiYu94="}]}, "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "dcousens", "email": "<EMAIL>"}, {"name": "dominictarr", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jp<PERSON><PERSON><EMAIL>"}, {"name": "indutny", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/randombytes-2.0.4.tgz_1496377084603_0.3779000991489738"}, "directories": {}}, "2.0.5": {"name": "randombytes", "version": "2.0.5", "description": "random bytes from browserify stand alone", "main": "index.js", "scripts": {"test": "standard && node test.js | tspec", "phantom": "zuul --phantom -- test.js", "local": "zuul --local --no-coverage -- test.js"}, "repository": {"type": "git", "url": "git+ssh://**************/crypto-browserify/randombytes.git"}, "keywords": ["crypto", "random"], "author": "", "license": "MIT", "bugs": {"url": "https://github.com/crypto-browserify/randombytes/issues"}, "homepage": "https://github.com/crypto-browserify/randombytes", "browser": "browser.js", "devDependencies": {"phantomjs": "^1.9.9", "standard": "^10.0.2", "tap-spec": "^2.1.2", "tape": "^4.6.3", "zuul": "^3.7.2"}, "dependencies": {"safe-buffer": "^5.1.0"}, "gitHead": "9b470ac1419e5545a5265238ccc5db8a7f1f8644", "_id": "randombytes@2.0.5", "_npmVersion": "5.0.2", "_nodeVersion": "8.0.0", "_npmUser": {"name": "cwmma", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-8T7Zn1AhMsQ/HI1SjcCfT/t4ii3eAqco3yOcSzS4mozsOz69lHLsoMXmF9nZgnFanYscnSlUSgs8uZyKzpE6kg==", "shasum": "dc009a246b8d09a177b4b7a0ae77bc570f4b1b79", "tarball": "https://registry.npmjs.org/randombytes/-/randombytes-2.0.5.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDHt7nJnZht4tZVx9GEaTl9Lotk+NmH/qCqEfVUbcY1eQIgNdOjjqMRzY8L/TEe5fqLQF9QWoYNIvRCVh78NRGhfms="}]}, "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "dcousens", "email": "<EMAIL>"}, {"name": "dominictarr", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jp<PERSON><PERSON><EMAIL>"}, {"name": "indutny", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/randombytes-2.0.5.tgz_1496863837565_0.05516815301962197"}, "directories": {}}, "2.0.6": {"name": "randombytes", "version": "2.0.6", "description": "random bytes from browserify stand alone", "main": "index.js", "scripts": {"test": "standard && node test.js | tspec", "phantom": "zuul --phantom -- test.js", "local": "zuul --local --no-coverage -- test.js"}, "repository": {"type": "git", "url": "git+ssh://**************/crypto-browserify/randombytes.git"}, "keywords": ["crypto", "random"], "author": "", "license": "MIT", "bugs": {"url": "https://github.com/crypto-browserify/randombytes/issues"}, "homepage": "https://github.com/crypto-browserify/randombytes", "browser": "browser.js", "devDependencies": {"phantomjs": "^1.9.9", "standard": "^10.0.2", "tap-spec": "^2.1.2", "tape": "^4.6.3", "zuul": "^3.7.2"}, "dependencies": {"safe-buffer": "^5.1.0"}, "gitHead": "cc8b6aec2fc35ccb1aa782dcab18268886ef26e5", "_id": "randombytes@2.0.6", "_npmVersion": "5.6.0", "_nodeVersion": "8.6.0", "_npmUser": {"name": "cwmma", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-CIQ5OFxf4Jou6uOKe9t1AOgqpeU5fd70A8NPdHSGeYXqXsPe6peOwI0cUl88RWZ6sP1vPMV3avd/R6cZ5/sP1A==", "shasum": "d302c522948588848a8d300c932b44c24231da80", "tarball": "https://registry.npmjs.org/randombytes/-/randombytes-2.0.6.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDOetKW+TPkp5EoYY2Wn8TxwjQc3ZlA8Y3O2bAjW+rbFAiBX5aqjOMuOirpaeHH0zNuF/Cvkq2KH9PD9v15ZuQsOZg=="}]}, "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "dcousens", "email": "<EMAIL>"}, {"name": "dominictarr", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jp<PERSON><PERSON><EMAIL>"}, {"name": "indutny", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/randombytes-2.0.6.tgz_1515607807232_0.09801237541250885"}, "directories": {}}, "2.1.0": {"name": "randombytes", "version": "2.1.0", "description": "random bytes from browserify stand alone", "main": "index.js", "scripts": {"test": "standard && node test.js | tspec", "phantom": "zuul --phantom -- test.js", "local": "zuul --local --no-coverage -- test.js"}, "repository": {"type": "git", "url": "git+ssh://**************/crypto-browserify/randombytes.git"}, "keywords": ["crypto", "random"], "author": "", "license": "MIT", "bugs": {"url": "https://github.com/crypto-browserify/randombytes/issues"}, "homepage": "https://github.com/crypto-browserify/randombytes", "browser": "browser.js", "devDependencies": {"phantomjs": "^1.9.9", "standard": "^10.0.2", "tap-spec": "^2.1.2", "tape": "^4.6.3", "zuul": "^3.7.2"}, "dependencies": {"safe-buffer": "^5.1.0"}, "gitHead": "f18ded32b209f0d4c637608a11ae042ae96b4c2e", "_id": "randombytes@2.1.0", "_nodeVersion": "10.15.0", "_npmVersion": "6.7.0", "dist": {"integrity": "sha512-vYl3iOX+4CKUWuxGi9Ukhie6fsqXqS9FE2Zaic4tNFD2N2QQaXOMFbuKK4QmDHC0JO6B1Zp41J0LpT0oR68amQ==", "shasum": "df6f84372f0270dc65cdf6291349ab7a473d4f2a", "tarball": "https://registry.npmjs.org/randombytes/-/randombytes-2.1.0.tgz", "fileCount": 8, "unpackedSize": 6363, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcbAk3CRA9TVsSAnZWagAAt8UP/iMvszavkaNc5N5rQzve\n0EttekkWrg/MgVqJtFNjkCp2wKKtJIvSHCV+geoJh/qWbrawfgHSxUsiquSI\niKGAWM7Mh+xcB9pTpSz8eSUM1Z+RZwP8PXgRq0x5T06aQQAaUVDgCF+znQUl\nGCIB7ojLs9z4kZxQuo02TDEGKA5LCo3EjLWCP76TLD7ONYEMMsMihVcwKauy\nFvRN6UQebje/hr+7NLm/933g5hucBYnpoTwQ6K7lycVYXb4y1QPaFpddmqBm\ntCSPSWvz1IIcDt5ybLVibLxHNB49gJS0rf9w5sIKHE+ZmwKGT8cqArznsuBV\nhCT16R0yCdtERdYJjoPrF/x8sE+mM7o1+Qso1jNVCr8IgAgcVi86HsJgW+JJ\nItC/DHLx0ALN5fH4/GKfvUvLqERCwVCjM2r5MQGnHp1Yb45qA9jwp6gBMQaA\nUGUsvyioz7dUWRP7/9/tWQB27j8/hZQm2wK0Uw/m1zwSVbDx9jOWVzr7AKZz\nPjStzZhJxV5ejFeGrLeg7rjslkw9FSBUJXGxakpe5wkJZ9pH2qkVu2PfXeX8\n7XmJ+hBfcv8sq63wVTkw0ECfb9KPmREFqKNyI1iW52BC+Pbmp3Zh07ZQc/+D\ncs57tb+DvSiqp+tB4mT20H5G1kX+5CBZLfH6ALjsvHcnQfPndq27DqABiUhl\n+CDz\r\n=uHPq\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFveAiePwXHVrnQGLEo/SHPuQTAK1a5W44ZTQSqf2UCfAiArfrsD65j/H0iT51qIKB9lZWiqfHhWslTY1TiCXm+vzw=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "cwmma"}, {"email": "<EMAIL>", "name": "dcousens"}, {"email": "<EMAIL>", "name": "indutny"}, {"email": "jp<PERSON><PERSON><EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "_npmUser": {"name": "cwmma", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/randombytes_2.1.0_1550584118742_0.21304987976010614"}, "_hasShrinkwrap": false}}, "readme": "randombytes\n===\n\n[![Version](http://img.shields.io/npm/v/randombytes.svg)](https://www.npmjs.org/package/randombytes) [![Build Status](https://travis-ci.org/crypto-browserify/randombytes.svg?branch=master)](https://travis-ci.org/crypto-browserify/randombytes)\n\nrandombytes from node that works in the browser.  In node you just get crypto.randomBytes, but in the browser it uses .crypto/msCrypto.getRandomValues\n\n```js\nvar randomBytes = require('randombytes');\nrandomBytes(16);//get 16 random bytes\nrandomBytes(16, function (err, resp) {\n  // resp is 16 random bytes\n});\n```\n", "maintainers": [{"email": "<EMAIL>", "name": "dcousens"}, {"email": "<EMAIL>", "name": "lj<PERSON>b"}, {"email": "<EMAIL>", "name": "cwmma"}, {"email": "<EMAIL>", "name": "indutny"}, {"email": "jp<PERSON><PERSON><EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "time": {"modified": "2023-07-21T15:51:31.377Z", "created": "2015-01-15T14:33:50.140Z", "1.0.0": "2015-01-15T14:33:50.140Z", "2.0.0": "2015-01-18T16:41:00.977Z", "2.0.1": "2015-01-22T00:20:45.544Z", "2.0.2": "2016-01-13T13:16:41.439Z", "2.0.3": "2016-03-02T18:51:43.439Z", "2.0.4": "2017-06-02T04:18:04.688Z", "2.0.5": "2017-06-07T19:30:38.426Z", "2.0.6": "2018-01-10T18:10:08.246Z", "2.1.0": "2019-02-19T13:48:39.116Z"}, "homepage": "https://github.com/crypto-browserify/randombytes", "keywords": ["crypto", "random"], "repository": {"type": "git", "url": "git+ssh://**************/crypto-browserify/randombytes.git"}, "bugs": {"url": "https://github.com/crypto-browserify/randombytes/issues"}, "license": "MIT", "readmeFilename": "README.md", "users": {"jonabasque": true, "emilbay": true, "hugovila": true}}