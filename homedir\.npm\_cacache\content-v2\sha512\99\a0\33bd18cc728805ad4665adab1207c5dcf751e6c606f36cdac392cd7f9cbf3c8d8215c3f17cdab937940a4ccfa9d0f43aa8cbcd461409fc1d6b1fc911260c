{"_id": "glob-to-regexp", "_rev": "21-89df3edd1a9bcf5bc59fc3c3810c1e88", "name": "glob-to-regexp", "description": "Convert globs to regular expressions", "dist-tags": {"latest": "0.4.1"}, "versions": {"0.0.0": {"name": "glob-to-regexp", "version": "0.0.0", "description": "Convert globs to regular expressions", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "https://github.com/fitzgen/glob-to-regexp.git"}, "keywords": ["regexp", "glob", "regexps", "regular expressions", "regular expression", "wildcard"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "glob-to-regexp@0.0.0", "dist": {"shasum": "a6b928e9e088769c207f28c7cc4682f906508b80", "tarball": "https://registry.npmjs.org/glob-to-regexp/-/glob-to-regexp-0.0.0.tgz", "integrity": "sha512-SDoxSUllh9KCbwcBlDx23dPu7F495AWCQqDYzylWp1HweSxaR6oJtU87PhA8wEPyc2aQOHGVhx2dcnv1MRHyzg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDYIZdboqjZ9U7hr6587ycTNJQ+Wru3+frVQ6lDiS5skAiAYBaa3SarFf6+6UuhfMLqy1t8wHyAyE501g0tF57h9JA=="}]}, "_from": ".", "_npmVersion": "1.2.18", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.0.1": {"name": "glob-to-regexp", "version": "0.0.1", "description": "Convert globs to regular expressions", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "https://github.com/fitzgen/glob-to-regexp.git"}, "keywords": ["regexp", "glob", "regexps", "regular expressions", "regular expression", "wildcard"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "glob-to-regexp@0.0.1", "dist": {"shasum": "2a5f79f2ed3233d4ee9ea7b6412547000c3f9d75", "tarball": "https://registry.npmjs.org/glob-to-regexp/-/glob-to-regexp-0.0.1.tgz", "integrity": "sha512-tnrPbfZQfn2p1rU3rNULGuh0nrPawBYLgLHW2V/cNXaicCEvOuzPEvNDY3NoLYW3CiqjqKRCd+LtNHmSN/B2nw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD0sPpJx4SLb/3dPKyD6UrrNyW2llaSeTtE+WTdPnbVDwIgZNvqwwiix2rccInPw8mOK2OFNHf7U01Lx9EBsrxy6zg="}]}, "_from": ".", "_npmVersion": "1.2.18", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.0.2": {"name": "glob-to-regexp", "version": "0.0.2", "description": "Convert globs to regular expressions", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "https://github.com/fitzgen/glob-to-regexp.git"}, "keywords": ["regexp", "glob", "regexps", "regular expressions", "regular expression", "wildcard"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "bugs": {"url": "https://github.com/fitzgen/glob-to-regexp/issues"}, "homepage": "https://github.com/fitzgen/glob-to-regexp", "_id": "glob-to-regexp@0.0.2", "_shasum": "82cb3c797594b47890f180f015c1773601374b91", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "82cb3c797594b47890f180f015c1773601374b91", "tarball": "https://registry.npmjs.org/glob-to-regexp/-/glob-to-regexp-0.0.2.tgz", "integrity": "sha512-783TFbI/OMd26sdKJbwnGpV8febYYNrIf86CaLWmApoHOmOOGKS+5RYLYg55SF3TT7n1vY0KtOT+c9hAfoBKgQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGJ1lbD24n18+pUfupIatJH9RZ1IxD3FZmQHdckSOvm6AiAIcOyRl3LsIov8zm6ortzYXfEOXdiRNv7TX4pwRYXLsg=="}]}, "directories": {}}, "0.1.0": {"name": "glob-to-regexp", "version": "0.1.0", "description": "Convert globs to regular expressions", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "https://github.com/fitzgen/glob-to-regexp.git"}, "keywords": ["regexp", "glob", "regexps", "regular expressions", "regular expression", "wildcard"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "bugs": {"url": "https://github.com/fitzgen/glob-to-regexp/issues"}, "homepage": "https://github.com/fitzgen/glob-to-regexp", "_id": "glob-to-regexp@0.1.0", "_shasum": "e0369d426578fd456d47dc23b09de05c1da9ea5d", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "e0369d426578fd456d47dc23b09de05c1da9ea5d", "tarball": "https://registry.npmjs.org/glob-to-regexp/-/glob-to-regexp-0.1.0.tgz", "integrity": "sha512-zNKwUvfFs4IbHMLzBDl4v5YbFNs64e4yGkptl4DncCYwmhMQORQflvs7XsEv50+M5bJqbgjBqnV+zZ8vF490yQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD5U8pUywFx5cadTPSG9/RSQapHXI+dQnwdk+mFULfNHgIhAIHwym9VdKvPIwYmgPSu/luEU9rYwXyZ2HbhFN1SGi94"}]}, "directories": {}}, "0.2.0": {"name": "glob-to-regexp", "version": "0.2.0", "description": "Convert globs to regular expressions", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "https://github.com/fitzgen/glob-to-regexp.git"}, "keywords": ["regexp", "glob", "regexps", "regular expressions", "regular expression", "wildcard"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "gitHead": "8523ef26bc3b64975fde849a6bc140f842d609b6", "bugs": {"url": "https://github.com/fitzgen/glob-to-regexp/issues"}, "homepage": "https://github.com/fitzgen/glob-to-regexp", "_id": "glob-to-regexp@0.2.0", "_shasum": "e6dc13a4bcb7acdd49ea99727534ca4f385e26c2", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "5.11.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "e6dc13a4bcb7acdd49ea99727534ca4f385e26c2", "tarball": "https://registry.npmjs.org/glob-to-regexp/-/glob-to-regexp-0.2.0.tgz", "integrity": "sha512-KAVsabMPnNOs+qV7Jupz3Xqn4B3Ojngeyc5F5MFdfucofzCVdp0zg7bESajLuZaVbjPvecjUljunx123qbs6zg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDLWg4eNcyks0L7m0fvZha6Zmko6ulJnddwC8g6EdXqTAiEA2Wkpt7nN6t0PyPdzUGgHMzfWGEUsH8XSjr/W2bWxqus="}]}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/glob-to-regexp-0.2.0.tgz_1472320688765_0.26884225686080754"}, "directories": {}}, "0.3.0": {"name": "glob-to-regexp", "version": "0.3.0", "description": "Convert globs to regular expressions", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "https://github.com/fitzgen/glob-to-regexp.git"}, "keywords": ["regexp", "glob", "regexps", "regular expressions", "regular expression", "wildcard"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "gitHead": "60be6c56893e80ba8719c31bd919e9b8f84c0c1d", "bugs": {"url": "https://github.com/fitzgen/glob-to-regexp/issues"}, "homepage": "https://github.com/fitzgen/glob-to-regexp", "_id": "glob-to-regexp@0.3.0", "_shasum": "8c5a1494d2066c570cc3bfe4496175acc4d502ab", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "5.11.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "8c5a1494d2066c570cc3bfe4496175acc4d502ab", "tarball": "https://registry.npmjs.org/glob-to-regexp/-/glob-to-regexp-0.3.0.tgz", "integrity": "sha512-Iozmtbqv0noj0uDDqoL0zNq0VBEfK2YFoMAZoxJe4cwphvLR+JskfF30QhXHOR4m3KrE6NLRYw+U9MRXvifyig==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCYzLIwZc/XTMBMrDLPx4oyACUsOYEErwRSop/WVsvcdQIgXZTfb2Eok3K+Nhb4VdF3HhFwyGRvWHWwS1g5ib+X7qg="}]}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/glob-to-regexp-0.3.0.tgz_1472364336407_0.02303200075402856"}, "directories": {}}, "0.4.0": {"name": "glob-to-regexp", "version": "0.4.0", "description": "Convert globs to regular expressions", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "git+https://github.com/fitzgen/glob-to-regexp.git"}, "keywords": ["regexp", "glob", "regexps", "regular expressions", "regular expression", "wildcard"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "gitHead": "1d89832646e0c3b9a522627a3cbd68bf4baa0d43", "bugs": {"url": "https://github.com/fitzgen/glob-to-regexp/issues"}, "homepage": "https://github.com/fitzgen/glob-to-regexp#readme", "_id": "glob-to-regexp@0.4.0", "_npmVersion": "5.6.0", "_nodeVersion": "9.3.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-fyPCII4vn9Gvjq2U/oDAfP433aiE64cyP/CJjRJcpVGjqqNdioUYn9+r0cSzT1XPwmGAHuTT7iv+rQT8u/YHKQ==", "shasum": "49bd677b1671022bd10921c3788f23cdebf9c7e6", "tarball": "https://registry.npmjs.org/glob-to-regexp/-/glob-to-regexp-0.4.0.tgz", "fileCount": 5, "unpackedSize": 18091, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDTyoeQElpHMdgu07/aRJWg4nr7BHsnFv4ocCg1dFWQlAIgUcfm8aVjFKikCLQJ+w46Dr3y09MeTz+LPiDEYtK09/I="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/glob-to-regexp_0.4.0_1518208173718_0.2637942738688941"}, "_hasShrinkwrap": false}, "0.4.1": {"name": "glob-to-regexp", "version": "0.4.1", "description": "Convert globs to regular expressions", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "git+https://github.com/fitzgen/glob-to-regexp.git"}, "keywords": ["regexp", "glob", "regexps", "regular expressions", "regular expression", "wildcard"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "gitHead": "2abf65a834259c6504ed3b80e85f893f8cd99127", "bugs": {"url": "https://github.com/fitzgen/glob-to-regexp/issues"}, "homepage": "https://github.com/fitzgen/glob-to-regexp#readme", "_id": "glob-to-regexp@0.4.1", "_nodeVersion": "10.11.0", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-lkX1HJXwyMcprw/5YUZc2s7DrpAiHB21/V+E1rHUrVNokkvB6bqMzT0VfV6/86ZNabt1k14YOIaT7nDvOX3Iiw==", "shasum": "c75297087c851b9a578bd217dd59a92f59fe546e", "tarball": "https://registry.npmjs.org/glob-to-regexp/-/glob-to-regexp-0.4.1.tgz", "fileCount": 5, "unpackedSize": 18100, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcojopCRA9TVsSAnZWagAAtf8QAJ0L3tnfJ1CkNlCuQCv+\nwtg9fKgi3EGnQvofkmp0+se0mbb9ljxhgH6+Jl/F3HGAXv/ox1tcfkbJs2sm\n8NzcRTZRc7A904OIMhCEs6iR+E77fWXA3CunGcWw5/1gAoSZS7gnZ1m6RT+n\n/pKPOT8Df9swZcfHPlqrGhIsYbpHg8VMJACTpVGzegUT3xEYXNL9bqFcRoRM\n2iRhyI+LaOBNWfnPYfV46UCZv5i8P9RamqWD0gCi0UMwY2ACwq81Yg+QBQci\n0LwkGIW5aHj9T5xz2/hvnkqoCfbXu/LkFC1aRM27/E5K/TP/1FxU4J3iYOH2\nFPZmsvODIDCFsR4DMCZSBGfiYbBEzB5Alzxjk24lZqSf9TiMPeB2vcH8Eh9l\nK5IECx8gsOZ7MIV98kSRSb9kNXF7bkvp7bgW1/LczPbYlwwDfI8b09kzBrZ1\nhQuvy0w9IGeE6UFm7q1doA4lQcYY6n79WE442S6JXALqtxusf2lfWWFtO+/L\n+4iFKgnWKwMIzFD2jJyeFsHwjvKVt/eKqXxxAkfw/o22eodPKPXDRUx2UslD\n7B4RXP44rXoqPj663FiF40ztT2PT/DPWFeBl4NuZw04zs3wze1CQuf/KQDqh\nv+lrptO9epb4VXZtbHUPAzBwF+XzY5lRQg3C3sqwlU9bLwLNDwtKrWJkN8Zk\nynCk\r\n=X1W5\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAUk/tJ9GAdYH5qoKdhl7oT4o1/aGza5HHeT4kYMJ26gAiEA/uEy2GCtigCrPLfqzbNpwOJEr1eSmuvG0uQcCYU3EKY="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/glob-to-regexp_0.4.1_1554135593354_0.8493549171717911"}, "_hasShrinkwrap": false}}, "readme": "# Glob To Regular Expression\n\n[![Build Status](https://travis-ci.org/fitzgen/glob-to-regexp.png?branch=master)](https://travis-ci.org/fitzgen/glob-to-regexp)\n\nTurn a \\*-wildcard style glob (`\"*.min.js\"`) into a regular expression\n(`/^.*\\.min\\.js$/`)!\n\nTo match bash-like globs, eg. `?` for any single-character match, `[a-z]` for\ncharacter ranges, and `{*.html, *.js}` for multiple alternatives, call with\n`{ extended: true }`.\n\nTo obey [globstars `**`](https://github.com/isaacs/node-glob#glob-primer) rules set option `{globstar: true}`.\nNOTE: This changes the behavior of `*` when `globstar` is `true` as shown below:\nWhen `{globstar: true}`: `/foo/**` will match any string that starts with `/foo/`\nlike `/foo/index.htm`, `/foo/bar/baz.txt`, etc.  Also, `/foo/**/*.txt` will match\nany string that starts with `/foo/` and ends with `.txt` like `/foo/bar.txt`,\n`/foo/bar/baz.txt`, etc.\nWhereas `/foo/*` (single `*`, not a globstar) will match strings that start with\n`/foo/` like `/foo/index.htm`, `/foo/baz.txt` but will not match strings that\ncontain a `/` to the right like `/foo/bar/baz.txt`, `/foo/bar/baz/qux.dat`, etc.\n\nSet flags on the resulting `RegExp` object by adding the `flags` property to the option object, eg `{ flags: \"i\" }` for ignoring case.\n\n## Install\n\n    npm install glob-to-regexp\n\n## Usage\n```js\nvar globToRegExp = require('glob-to-regexp');\nvar re = globToRegExp(\"p*uck\");\nre.test(\"pot luck\"); // true\nre.test(\"pluck\"); // true\nre.test(\"puck\"); // true\n\nre = globToRegExp(\"*.min.js\");\nre.test(\"http://example.com/jquery.min.js\"); // true\nre.test(\"http://example.com/jquery.min.js.map\"); // false\n\nre = globToRegExp(\"*/www/*.js\");\nre.test(\"http://example.com/www/app.js\"); // true\nre.test(\"http://example.com/www/lib/factory-proxy-model-observer.js\"); // true\n\n// Extended globs\nre = globToRegExp(\"*/www/{*.js,*.html}\", { extended: true });\nre.test(\"http://example.com/www/app.js\"); // true\nre.test(\"http://example.com/www/index.html\"); // true\n```\n\n## License\n\nCopyright (c) 2013, Nick Fitzgerald\n\nAll rights reserved.\n\nRedistribution and use in source and binary forms, with or without modification,\nare permitted provided that the following conditions are met:\n\n* Redistributions of source code must retain the above copyright notice, this\n  list of conditions and the following disclaimer.\n\n* Redistributions in binary form must reproduce the above copyright notice, this\n  list of conditions and the following disclaimer in the documentation and/or\n  other materials provided with the distribution.\n\nTHIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\" AND\nANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\nWARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE\nDISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR\nANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES\n(INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;\nLOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON\nANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS\nSOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2022-06-18T12:54:52.833Z", "created": "2013-07-16T23:31:17.855Z", "0.0.0": "2013-07-16T23:31:18.936Z", "0.0.1": "2013-11-26T17:06:53.604Z", "0.0.2": "2015-01-30T21:44:24.713Z", "0.1.0": "2015-07-14T20:29:03.066Z", "0.2.0": "2016-08-27T17:58:11.118Z", "0.3.0": "2016-08-28T06:05:38.614Z", "0.4.0": "2018-02-09T20:29:33.774Z", "0.4.1": "2019-04-01T16:19:53.460Z"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/fitzgen/glob-to-regexp.git"}, "users": {"benmonro": true, "demsking": true, "jovinbm": true}, "homepage": "https://github.com/fitzgen/glob-to-regexp#readme", "keywords": ["regexp", "glob", "regexps", "regular expressions", "regular expression", "wildcard"], "bugs": {"url": "https://github.com/fitzgen/glob-to-regexp/issues"}, "license": "BSD-2-<PERSON><PERSON>", "readmeFilename": "README.md"}