{"name": "randombytes", "dist-tags": {"latest": "2.1.0"}, "versions": {"1.0.0": {"name": "randombytes", "version": "1.0.0", "devDependencies": {"tap-spec": "^2.1.2", "tape": "^3.0.3"}, "dist": {"shasum": "a52ff9f435cdecaea97cdeecb092303967dbec76", "tarball": "https://registry.npmjs.org/randombytes/-/randombytes-1.0.0.tgz", "integrity": "sha512-zCVUP2aYdX2KuY6G9c4gCspWaSvryGKJFUmElXc2sp7q5m5UbKWk5MUFYMSgdM/H0kvVdn/VWPkd9ajXFozM5A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDGD70DiVIMStaO+isuCafVjb5SHptAWHtY/Y5jdSlefwIhALGIAHdTrBZzISb7M21gZH/WuFpEBfHq28p9DvMrUBqF"}]}}, "2.0.0": {"name": "randombytes", "version": "2.0.0", "devDependencies": {"tap-spec": "^2.1.2", "tape": "^3.0.3"}, "dist": {"shasum": "e59d471c66e3537f85e1ca2e2b0511e9cc2115b6", "tarball": "https://registry.npmjs.org/randombytes/-/randombytes-2.0.0.tgz", "integrity": "sha512-yv0Q+KnDnkpTGfnvkx3ktHPQoMHGpicOqEFP/F7vLeco9uzDc7kK5X+UQehD28tt2IlvdiLEWN3wBVxVtyJBsg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIExEo+6ruaszMbne86baA96mObQicg5YCSPuLvpZHNXxAiEAyIiLS/IbMfMmu2nVckAudvX60b1M9w8RlA1xTpGiETI="}]}}, "2.0.1": {"name": "randombytes", "version": "2.0.1", "devDependencies": {"tap-spec": "^2.1.2", "tape": "^3.0.3"}, "dist": {"shasum": "18f4a9ba0dd07bdb1580bc9156091fcf90eabc6f", "tarball": "https://registry.npmjs.org/randombytes/-/randombytes-2.0.1.tgz", "integrity": "sha512-siCt2duOdZbmvgk8IDL4U0SYXI8ypBEKWuor0qUpHBWAyOCrXQvSIYJ+VKuEpoX36moZ1pAu+mXkwUVAVssu6w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCtYb9NPPaiTej/z8VrnvVPoNed0WZV93Q3FgCHc23pvAIge4X3T7xUVGxrRVM0XtKGCgwLR9B9qOIMlq9v8M92hyw="}]}}, "2.0.2": {"name": "randombytes", "version": "2.0.2", "devDependencies": {"phantomjs": "^1.9.9", "standard": "^3.3.0", "tap-spec": "^2.1.2", "tape": "^3.0.3", "zuul": "^3.7.2"}, "dist": {"shasum": "f5910f4bc57ea8eccc67e32e0b52adf0ae011e43", "tarball": "https://registry.npmjs.org/randombytes/-/randombytes-2.0.2.tgz", "integrity": "sha512-0ik5pRUAsd93cR0OjLJq+C7FU1bpuPDaJgfU8RiPNwTYm4pe6BVEzCGiY8tJG0FIJEdsbNDU3vnMF5OT9tbBTQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBdt2v02K91EO+Ns4ClyQyAVqXabfLhZwvDx96F5OTe2AiEAkfmO70Q9P+krumKzZVGsXQyPhi0KCWNedpxawgrLSwg="}]}}, "2.0.3": {"name": "randombytes", "version": "2.0.3", "devDependencies": {"phantomjs": "^1.9.9", "standard": "^3.3.0", "tap-spec": "^2.1.2", "tape": "^3.0.3", "zuul": "^3.7.2"}, "dist": {"shasum": "674c99760901c3c4112771a31e521dc349cc09ec", "tarball": "https://registry.npmjs.org/randombytes/-/randombytes-2.0.3.tgz", "integrity": "sha512-lDVjxQQFoCG1jcrP06LNo2lbWp4QTShEXnhActFBwYuHprllQV6VUpwreApsYqCgD+N1mHoqJ/BI/4eV4R2GYg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDQTjZdSzCjaeaWyya1BQe5n+2O8wLfAhaHcIHDpmJ6ZwIgGCoYWue8HfKPYp5GumlEd+5SoL3h9VJkCsiv4Tu20mg="}]}}, "2.0.4": {"name": "randombytes", "version": "2.0.4", "dependencies": {"safe-buffer": "^5.0.1"}, "devDependencies": {"phantomjs": "^1.9.9", "standard": "^10.0.2", "tap-spec": "^2.1.2", "tape": "^4.6.3", "zuul": "^3.7.2"}, "dist": {"shasum": "9551df208422c8f80eb58e2326dd0b840ff22efd", "tarball": "https://registry.npmjs.org/randombytes/-/randombytes-2.0.4.tgz", "integrity": "sha512-91PiBYZc53fbddGd2m/NG8qS+EqjNUNTW4xy552xu+A30dfmHEiSfTUG1nsUxz+OKbHWb/f8iYwEQAcloCWR/Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGIqxN4z2tcYzK04X8mFK1MO8ALoUn+7rsTvNxS98egGAiEA2nBIzDLLYpoGjkm+hJiMWe/yUT1SyyKnCM4trPiYu94="}]}}, "2.0.5": {"name": "randombytes", "version": "2.0.5", "dependencies": {"safe-buffer": "^5.1.0"}, "devDependencies": {"phantomjs": "^1.9.9", "standard": "^10.0.2", "tap-spec": "^2.1.2", "tape": "^4.6.3", "zuul": "^3.7.2"}, "dist": {"integrity": "sha512-8T7Zn1AhMsQ/HI1SjcCfT/t4ii3eAqco3yOcSzS4mozsOz69lHLsoMXmF9nZgnFanYscnSlUSgs8uZyKzpE6kg==", "shasum": "dc009a246b8d09a177b4b7a0ae77bc570f4b1b79", "tarball": "https://registry.npmjs.org/randombytes/-/randombytes-2.0.5.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDHt7nJnZht4tZVx9GEaTl9Lotk+NmH/qCqEfVUbcY1eQIgNdOjjqMRzY8L/TEe5fqLQF9QWoYNIvRCVh78NRGhfms="}]}}, "2.0.6": {"name": "randombytes", "version": "2.0.6", "dependencies": {"safe-buffer": "^5.1.0"}, "devDependencies": {"phantomjs": "^1.9.9", "standard": "^10.0.2", "tap-spec": "^2.1.2", "tape": "^4.6.3", "zuul": "^3.7.2"}, "dist": {"integrity": "sha512-CIQ5OFxf4Jou6uOKe9t1AOgqpeU5fd70A8NPdHSGeYXqXsPe6peOwI0cUl88RWZ6sP1vPMV3avd/R6cZ5/sP1A==", "shasum": "d302c522948588848a8d300c932b44c24231da80", "tarball": "https://registry.npmjs.org/randombytes/-/randombytes-2.0.6.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDOetKW+TPkp5EoYY2Wn8TxwjQc3ZlA8Y3O2bAjW+rbFAiBX5aqjOMuOirpaeHH0zNuF/Cvkq2KH9PD9v15ZuQsOZg=="}]}}, "2.1.0": {"name": "randombytes", "version": "2.1.0", "dependencies": {"safe-buffer": "^5.1.0"}, "devDependencies": {"phantomjs": "^1.9.9", "standard": "^10.0.2", "tap-spec": "^2.1.2", "tape": "^4.6.3", "zuul": "^3.7.2"}, "dist": {"integrity": "sha512-vYl3iOX+4CKUWuxGi9Ukhie6fsqXqS9FE2Zaic4tNFD2N2QQaXOMFbuKK4QmDHC0JO6B1Zp41J0LpT0oR68amQ==", "shasum": "df6f84372f0270dc65cdf6291349ab7a473d4f2a", "tarball": "https://registry.npmjs.org/randombytes/-/randombytes-2.1.0.tgz", "fileCount": 8, "unpackedSize": 6363, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcbAk3CRA9TVsSAnZWagAAt8UP/iMvszavkaNc5N5rQzve\n0EttekkWrg/MgVqJtFNjkCp2wKKtJIvSHCV+geoJh/qWbrawfgHSxUsiquSI\niKGAWM7Mh+xcB9pTpSz8eSUM1Z+RZwP8PXgRq0x5T06aQQAaUVDgCF+znQUl\nGCIB7ojLs9z4kZxQuo02TDEGKA5LCo3EjLWCP76TLD7ONYEMMsMihVcwKauy\nFvRN6UQebje/hr+7NLm/933g5hucBYnpoTwQ6K7lycVYXb4y1QPaFpddmqBm\ntCSPSWvz1IIcDt5ybLVibLxHNB49gJS0rf9w5sIKHE+ZmwKGT8cqArznsuBV\nhCT16R0yCdtERdYJjoPrF/x8sE+mM7o1+Qso1jNVCr8IgAgcVi86HsJgW+JJ\nItC/DHLx0ALN5fH4/GKfvUvLqERCwVCjM2r5MQGnHp1Yb45qA9jwp6gBMQaA\nUGUsvyioz7dUWRP7/9/tWQB27j8/hZQm2wK0Uw/m1zwSVbDx9jOWVzr7AKZz\nPjStzZhJxV5ejFeGrLeg7rjslkw9FSBUJXGxakpe5wkJZ9pH2qkVu2PfXeX8\n7XmJ+hBfcv8sq63wVTkw0ECfb9KPmREFqKNyI1iW52BC+Pbmp3Zh07ZQc/+D\ncs57tb+DvSiqp+tB4mT20H5G1kX+5CBZLfH6ALjsvHcnQfPndq27DqABiUhl\n+CDz\r\n=uHPq\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFveAiePwXHVrnQGLEo/SHPuQTAK1a5W44ZTQSqf2UCfAiArfrsD65j/H0iT51qIKB9lZWiqfHhWslTY1TiCXm+vzw=="}]}}}, "modified": "2023-07-21T15:51:31.377Z"}