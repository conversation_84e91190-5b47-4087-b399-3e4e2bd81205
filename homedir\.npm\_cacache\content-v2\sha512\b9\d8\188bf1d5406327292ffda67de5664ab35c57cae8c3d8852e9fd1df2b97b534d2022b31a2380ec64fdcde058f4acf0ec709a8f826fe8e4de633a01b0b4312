{"_id": "@rollup/rollup-win32-ia32-msvc", "_rev": "152-e9c39fea764be6bce5ce3ad1c0297d45", "name": "@rollup/rollup-win32-ia32-msvc", "dist-tags": {"beta": "4.33.0-0", "latest": "4.44.0"}, "versions": {"4.0.0-0": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.0.0-0", "keywords": ["modules", "bundler", "bundling", "es6", "optimizer"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.0.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "fdf4ca873eb9015d4019575f86b3f9cc3aa66fe1", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.0.0-0.tgz", "fileCount": 2, "integrity": "sha512-oQv3Lee39SGN7USos+7ZuWgGyEXJTSYCaXEuKOqjNO/gDbaeQTb0QFLjMCcxck8qu0NyrjC+b4tz7OSMCpDwww==", "signatures": [{"sig": "MEYCIQDL7D9Se1+eLvjLohL52ZHSMEAvXj3CSyAouXZiJIXCyAIhAOBPEuwIUmpY50zks4SbTf0+vMGPimU9LLmE0f7W9dmj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 727}, "main": "native/rollup.win32-ia32-msvc.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "580d17223962a0a359da88420001bbbc738e633a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Next-generation ES module bundler", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.0.0-0_1690831082099_0.1238362981257124", "host": "s3://npm-registry-packages"}}, "4.0.0-1": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.0.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.0.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "2878b5268311a32e46b837221fd6d00bc53b0312", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.0.0-1.tgz", "fileCount": 3, "integrity": "sha512-tk7/p17a2mNX9DH1NJ0AQZW7G3Z0OVc4zVoFcPHc7IlMBIx+/VIOfRE5gmY02wf3ZfbEzJx3SG+XLoqJyHobDg==", "signatures": [{"sig": "MEUCIQDevOmygHR/AKHQUd0XXIlJdaQIzA2eDJFwuLoK6JwXvAIgMKyJ7jZe0BZ1TweAqlTYE6sNbu8Ukpc042xnwxfSn+c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2359848}, "main": "rollup.win32-ia32-msvc.node", "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "gitHead": "d5b6ec3f77c860c048e2830353f5af4593ffaf20", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.0.0-1_1690865346602_0.35928921330078634", "host": "s3://npm-registry-packages"}}, "4.0.0-2": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.0.0-2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.0.0-2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "ce4dc3497a9b594492b148c4fc9c73fe140ce234", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.0.0-2.tgz", "fileCount": 3, "integrity": "sha512-nr3xRI/6RBJroCc/LULXsSlfNesTDneUqfhlmGvdO6tJ/8/3ewBQqFmA8SKNeWzyoZDMKVqwV74uxp43cRqJ2w==", "signatures": [{"sig": "MEUCIEfBYeHvsBB3mqT4TIzhOq4+scs+t9Pwn1z8mdW73aDOAiEA/HBuoDIirWZzxgLdQ/xO12uELgPNkiq5bmAYuOe6Ayw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2359848}, "main": "rollup.win32-ia32-msvc.node", "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "gitHead": "d62558dbc45912c9c4478dc761bb290738c3b968", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.0.0-2_1690888600402_0.7301053626820533", "host": "s3://npm-registry-packages"}}, "4.0.0-3": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.0.0-3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.0.0-3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "08c0433357653213fa369faf4af5c63e4f596623", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.0.0-3.tgz", "fileCount": 3, "integrity": "sha512-TPReZnRKSIgKd5li/4jx5tnAeC4ajg6/lHFlsoYu+lTxLC7ZHsM4Sfj2uSp4yZr7wojBdhJFQKThPg1Dsy/heA==", "signatures": [{"sig": "MEUCIBkAJ2SBI35q+uKoDHEd39VugXcvNjn4TLZ0uHGgKe+VAiEA07ZaL5ixZM0OfrkLXb5ZZ5QPbiLl84NU9i7B1qfhP5I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2388008}, "main": "rollup.win32-ia32-msvc.node", "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "gitHead": "d9deb724f026a6f3e429509fce2d920e75d6a1ae", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.0.0-3_1691137026467_0.6490736673922255", "host": "s3://npm-registry-packages"}}, "4.0.0-4": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.0.0-4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.0.0-4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "b820b78831a9a615138b08c7694b7b167757b4d3", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.0.0-4.tgz", "fileCount": 3, "integrity": "sha512-DI1HRQBYUDcbbZOArTQqAEVkYeBT00kxO9rsjR7c/pgIcDVhLLa4ihcr5KlBuz93qCdcnVADeh1W/wwq2Nj2DA==", "signatures": [{"sig": "MEUCIB0olyXtUzWeE8HCaSMCdOvFLeph2neJYGzzjcIPtJU9AiEA9cWwAXgw/LUJfTQhjdWhK1BDRhZJS0vaqqA2ciQsCBo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2387496}, "main": "rollup.win32-ia32-msvc.node", "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "gitHead": "c416e3eb3d2d6055d6567cac6e8747b992eec1de", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.0.0-4_1691149010974_0.45722867230441744", "host": "s3://npm-registry-packages"}}, "4.0.0-5": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.0.0-5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.0.0-5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "667e5e1c2aa8d3eaca6647bd006ae3b54194e824", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.0.0-5.tgz", "fileCount": 3, "integrity": "sha512-PC3e7i3eKaSP8MWiBsi0E1R735fVsK0j1SOeHpLeSgz9SlkDgo9UrHZWf3hAdFloAx4GCk4AdwN+LYmZmmLtpg==", "signatures": [{"sig": "MEQCIGC5Cr+jRFpRdqhZaD6izLzT1ngAhJssOczoRq9IPKnvAiAsBpqYowBMCH+UGcCXW0J3bopdhsDnlX51pyWgBrgy7w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2747547}, "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.win32-ia32-msvc.node"}}, "gitHead": "6284e58c1be160b656b9f2b44e8e2b1e5a93f9df", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.0.0-5_1692514618373_0.05793352417383302", "host": "s3://npm-registry-packages"}}, "4.0.0-6": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.0.0-6", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.0.0-6", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "adae94f821090147ba645a1ac5208c8eeaaf44d1", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.0.0-6.tgz", "fileCount": 3, "integrity": "sha512-6S<PERSON>ynj2ljxZDLNN3sAPi1Skc+sMTKB1qFpBJN4RMy26cvqt4tJJdT41zLc53Y22H0KYxQDyz1jv35dFZQjjM/g==", "signatures": [{"sig": "MEUCIGKHdcXPwc1T/JlhPQ4sD6wAhqtvslGgR4UUb+1u9761AiEA1OX1UK2m+d3D1RUzn6YUdwbIJHs7i2fb/yBnCaNfP50=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2747547}, "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.win32-ia32-msvc.node"}}, "gitHead": "39e7492a12eca9107c929d533c16608c9a0054be", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.0.0-6_1692517906278_0.9585237515485565", "host": "s3://npm-registry-packages"}}, "4.0.0-7": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.0.0-7", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.0.0-7", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "1cc1ca2b1c354148a428acdc1cca2f3abc70a9a6", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.0.0-7.tgz", "fileCount": 7, "integrity": "sha512-uH8oxcBCMjVA8eLC3tKwqsrW6jjOmxY8DYhtJtKSDyVjFbpIB+LGdRhKy733rQjxA/ir0StmKk0GsXc7YldrDQ==", "signatures": [{"sig": "MEUCIQDRm1E50I7Mx6WAEXdXfC1ho/JmKqMp+82pc0KNBglEugIgF+NxD3XCDqh4MpL6UuiRn5Wnavr3Kjam//z1SfPyJh4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8202469}, "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.win32-ia32-msvc.node"}}, "gitHead": "afaa754955a083970b389711127e368d6f4d235b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.0.0-7_1692527623398_0.2962533663700313", "host": "s3://npm-registry-packages"}}, "4.0.0-8": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.0.0-8", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.0.0-8", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "4e5998f467dd3a4f7b0e7b45355b4f7fb38c7e23", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.0.0-8.tgz", "fileCount": 7, "integrity": "sha512-+3k41/VHYqmFGHKgnalWqGejD/orXWQVMFVXDefmYsnpAQHjLRKLZnGmyoHmGceuMWglZgKGqrs08/HlHAIOLQ==", "signatures": [{"sig": "MEUCIAyRgucENCujdSMQO8DE2qg8XtC83+p6lvhYkk3vRPDCAiEAzltrVbD7dwsasu/1ABMqdzciMyOM9m99jAnbH90QUVc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8202469}, "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.win32-ia32-msvc.node"}}, "gitHead": "5bfa022de96252b5eaf0bdab90be6bcfefcccb57", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.0.0-8_1692530555187_0.8418965876698044", "host": "s3://npm-registry-packages"}}, "4.0.0-9": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.0.0-9", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.0.0-9", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "d7ce5e5ff0f849b4817e4bc6a049a7dbcf7089a7", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.0.0-9.tgz", "fileCount": 7, "integrity": "sha512-0LrI5uw+s/zTRMivELecO4ZlKSHROD8ELaC/0Fm7YEaD2hD4ua6s0tYNPIebekrRuJtudY9UrDErka1CMXlh7w==", "signatures": [{"sig": "MEYCIQDUXwHrO0jeRONXB3BvSWt/e7AmZN9KvBBVmwyicC7SygIhAJX8AqDVNGFjff0qvk3ZAId2pJkdSWFmyDCJ4gEQAN5S", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8202469}, "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.win32-ia32-msvc.node"}}, "gitHead": "e4d55671a81334ddc59fdbcd81ceabdb77d96974", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.0.0-9_1692541760709_0.17259986785117332", "host": "s3://npm-registry-packages"}}, "4.0.0-10": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.0.0-10", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.0.0-10", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "cc96685462e422c29f71d5486201c010e528076b", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.0.0-10.tgz", "fileCount": 7, "integrity": "sha512-SwogocYQLdX19OG3zI3ta64Ua28Lk4Hka13NlB26fwJVj0Z7Cy/ulYi9N+OApNgqWtEcDzGvG7mTNitFf3S/Aw==", "signatures": [{"sig": "MEQCIAElh4QX/ZnIBGq9YQlV1hd3jAJjDFXRkG95eBYCKIE6AiAeb4NesVjukF0s8jB5mjY/2Rzq/SKd0Q3vmVIh3FJ7JQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8198593}, "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.win32-ia32-msvc.node"}}, "gitHead": "2c7e3e32f5d56c60d92907a9ceacd338aa99ca82", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.0.0-10_1692631819392_0.7949539844638203", "host": "s3://npm-registry-packages"}}, "4.0.0-11": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.0.0-11", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.0.0-11", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "026c1b0c492fa6281eb201349434a947aa8f823a", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.0.0-11.tgz", "fileCount": 3, "integrity": "sha512-VAlsUuiEwoDMT1roTGsF3AgGvlQnt0MWgEWMMs4h3S6sZMf91blMqGqYlT80y6TxVw5UQgSLOXeqKjpMupCTyQ==", "signatures": [{"sig": "MEUCIERZi3Pmw0doxiOW62M236IXof6eU3/mp07aXWFu52u3AiEA+IQ+VUVdu7lLI9SnUZcv3HaTbvSgaJ3fXTx/4j+1+Dw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2748460}, "main": "./rollup.win32-ia32-msvc.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "3fc8b18da06fc76c386527cebadec4d8936b0f7a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.0.0-11_1692785768164_0.5903707279522816", "host": "s3://npm-registry-packages"}}, "4.0.0-12": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.0.0-12", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.0.0-12", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "180a332e41ea192df187e81dcb70c803def1d241", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.0.0-12.tgz", "fileCount": 3, "integrity": "sha512-RRBqt3SFcgkSYA5BAtRN9dFgSOtkRG5rrpcfLD6TiVPEr7rxHe5jSKnb3tUAAeTia2yumu4wXebwQoI3d+1j1Q==", "signatures": [{"sig": "MEYCIQDvuzl6YZO17W8H+THGD345z0d1bkyFDaJ7HhXCPdW0oQIhAMD6WqvR0jfEzMhuT+/5TJ3nb1/mu4P14FKKBlhGtXIQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2748460}, "main": "./rollup.win32-ia32-msvc.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "b6eec18d711348e3b177ef58dc2836cdf75e0432", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.0.0-12_1692801647239_0.8233185301749628", "host": "s3://npm-registry-packages"}}, "4.0.0-13": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.0.0-13", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.0.0-13", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "8a58f058d214c6c2718e2b87d67aa761f3e5f67f", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.0.0-13.tgz", "fileCount": 3, "integrity": "sha512-FGTLDE3dIA/j4/NUHtlWDN7SDGhI5jH+268uUcmDcwKtK5xyNVH57+BB+ZRK/XkiwZ0szIjh8arpRoV8LFd6mQ==", "signatures": [{"sig": "MEQCIHNEUq35H8vF/qQG3nqgK03UwmgRzhkA5rFZY7+V+lwsAiAwy0eMUxuXTYqufRpr4lZ1oAQSjIWTCDs//xc9cc9L5Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2757676}, "main": "./rollup.win32-ia32-msvc.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "07d3baeb218f6d1084e9d1b17a429ca84cb92561", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.0.0-13_1692892124715_0.47257572587676", "host": "s3://npm-registry-packages"}}, "4.0.0-14": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.0.0-14", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.0.0-14", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "4473fd315049470dfebc49a38a4a273c5d763a41", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.0.0-14.tgz", "fileCount": 3, "integrity": "sha512-k+MJtcptuyHFTqmoojTyZGKc50vJj1oVB9Q7DxXepzKfcDNFdJzkC7KuopLWPC85b4RZ5XV/Ns6g2Q5e+gIYbA==", "signatures": [{"sig": "MEUCIA1p3p9+k7PY9BGmPwg+cCbINZoNptfrGSNYzv2W46lOAiEAlHqbyh5XFPpEPddAsn9yn4zHBWHYOP/IOLmLriQhiJo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2715692}, "main": "./rollup.win32-ia32-msvc.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "ec2f8ec863d8d896aef0dd0097f2d73f59e8213a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.0.0-14_1694781274954_0.628127362006587", "host": "s3://npm-registry-packages"}}, "4.0.0-15": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.0.0-15", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.0.0-15", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "079a5eb8440b779d850e04f1daf76336932d800d", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.0.0-15.tgz", "fileCount": 3, "integrity": "sha512-rR4MfrNv6VIwM6cKmcel9AUUQGxJv7DRfR+IljJK5nKvuREnICraRSYxNQ3Gje1rafI3g5jfRs/H1S/iZHdC0w==", "signatures": [{"sig": "MEQCIHhB/29YIbiMTE4ryBVzL2E8hCi8BB4A16WE4Jf4W7TeAiAgWI/5WmyrYwejpUmtXouVweSH89iR2xEO/Q3N1rovEg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2715692}, "main": "./rollup.win32-ia32-msvc.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "6e6186636ebb169611373a0e430853eb3b6ce8e0", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.0.0-15_1694783221986_0.9248494152585254", "host": "s3://npm-registry-packages"}}, "4.0.0-16": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.0.0-16", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.0.0-16", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "55c42a48fe3f0622b082382d6fd4618bbff15a14", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.0.0-16.tgz", "fileCount": 3, "integrity": "sha512-aGfDBy1hd5PfnbDQIqDOG3DBSGPSDxbUW2QVDV4lU9E6zGjUQWZbvNTuyHYdET8DUPCdaaJBotVOwT6aXCkV7g==", "signatures": [{"sig": "MEUCIDZYnk2DzYJt6wsUhMWPXYvTv7fA+zHDr54yPkpKXHqUAiEAhMiIKp/Ytr0UrJxBxlPiQ3fpVCT/7JYH/Vb0HgsusgY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2715692}, "main": "./rollup.win32-ia32-msvc.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "fd025bcfab85bdecba183367d11c13a1f99c4f10", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.0.0-16_1694787446411_0.685744477356439", "host": "s3://npm-registry-packages"}}, "4.0.0-17": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.0.0-17", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.0.0-17", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "4de872d95f693e6b69850bbd5506b1181da9b85c", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.0.0-17.tgz", "fileCount": 3, "integrity": "sha512-KJ1wJHjttUgOoWk6lV2vi5Y2WojJevM4kdyv0OZc6mt7AkM/8Dh8TJestDbxQYEpR8ZCqkEsMqGZr2h2Jxug2w==", "signatures": [{"sig": "MEUCIQDoNnRjYANjCSqjS0JCSXJWpIvN9JKKKhUvklNN1e/7dQIgXrH4facQooFMQ7SlYG+IcGK/Z/NRVHu+iwjW1wr5yz0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2715692}, "main": "./rollup.win32-ia32-msvc.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "f7eb39f003eaa325451091faec04dd51d774ae3b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.0.0-17_1694789956670_0.670268905797565", "host": "s3://npm-registry-packages"}}, "4.0.0-18": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.0.0-18", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.0.0-18", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "81dbcf5f942a14eb9146b8943c2e31b8dbdb293b", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.0.0-18.tgz", "fileCount": 3, "integrity": "sha512-SIFPQ1N+DguKLpcWZDq+OIRO0XotFI+5+HJYb6SXAF8zU7VUhQKBkQLybrf9YQSgbWXv5O5iV1oQlktkmbAiwg==", "signatures": [{"sig": "MEUCIAuSvSzgMp7+UaF2lYpS9Bt9kWfJknFib5gBxwPtcwS6AiEAjxXJtOVGjVWn/M059mS1C6dWAU2BRlZQ6xAJg3E4HgY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2715692}, "main": "./rollup.win32-ia32-msvc.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "646171ff58e4f31127714ff8c78868c79b77d596", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.0.0-18_1694794249287_0.34497844539877476", "host": "s3://npm-registry-packages"}}, "4.0.0-19": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.0.0-19", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.0.0-19", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "84fc7795e1bcfa7f42fa8b3d513efc7e962df6ac", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.0.0-19.tgz", "fileCount": 3, "integrity": "sha512-jaNKkeKFAmid8cwhIZ2xTsWqAyI9u0IthIYtY6AWilGecjDhDqXKyuELd+ex2DHV7c3R9N0vRreyhmXEB0vftw==", "signatures": [{"sig": "MEYCIQDLyWAyCgO7jIkuP47HR7SdqJ/HWvDMGmtansph1Fb0CgIhAKsnmdlx4xWbd+gE7g2mihQXHZnBtNt8mslb2cVOPPcR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2715692}, "main": "./rollup.win32-ia32-msvc.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "25753ad04d73429f0d7b4d5dc85df09aeae78485", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.0.0-19_1694803867442_0.44442321722527756", "host": "s3://npm-registry-packages"}}, "4.0.0-20": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.0.0-20", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.0.0-20", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "cac26e55144fcb15f343d06f2b4ad0fe67c0bc36", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.0.0-20.tgz", "fileCount": 3, "integrity": "sha512-B6<PERSON>zZB4kHxze2REHeJR/H73SNsbsjdj0Lcjbpj3A1yo1q7jeqMvaL0aD4xhUXdE7Vgbh9IDgrakJ2cO044ckA==", "signatures": [{"sig": "MEUCICbjmTxb22WizZkV5hPcpu3ejT163/9pcobEyjt8NMXkAiEAoy5WDID/ma+2uUCZrlnGPEPaItdQu13enM38fxqgnqY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2705386}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "9d6dc574c6dca3d85e9eda512b09797a6d15462f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.0.0-20_1695535847102_0.7609065153864902", "host": "s3://npm-registry-packages"}}, "4.0.0-21": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.0.0-21", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.0.0-21", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "a24a9bd5f85bf43a43e1fa191537342fd14dfb92", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.0.0-21.tgz", "fileCount": 3, "integrity": "sha512-rspqpMYPruE5gFU+q6i2+UM7JI4j5EHNRHQPvHTghOYknH2Pc5DViHcUvHvtP8a+MdWtIanlw5/AnOZvh/PsJA==", "signatures": [{"sig": "MEUCIQDD3ejTnCX0OroCS3JQTUrqV9KgJrdqBYkS+5G4VrsKjQIgd3bJOi6iRGeOh/PPafQOmni61w+KHJy553NIZjQ3Mow=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2705386}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "fa868ad975b9ae6007ddc64b1a9e82766de6fa9e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.0.0-21_1695576153743_0.8388310119291951", "host": "s3://npm-registry-packages"}}, "4.0.0-22": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.0.0-22", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.0.0-22", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "2bf365f882048825396d894a79e8afc5d0154709", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.0.0-22.tgz", "fileCount": 3, "integrity": "sha512-pOTjUjjVqNUo5YWYGiScv93Wu79tgoJVjWIQ3nkEitX9xoUy/IPecUbbAxMg2JsIbcyJo6MOUIIUbpAyO7Othw==", "signatures": [{"sig": "MEYCIQCAcA9qH5DvHKv0WU3eHFHy5/73OWBgH8bWJLABLtL7BAIhAMhF2HVLJKTj9RgHCOJqcnfcsYP6tt4qpiPld6eRgmLA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2701290}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "38be49cf19099321f935c1ad5968e76fb30e0957", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.0.0-22_1695745062169_0.958987419408218", "host": "s3://npm-registry-packages"}}, "4.0.0-23": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.0.0-23", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.0.0-23", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "a8da755451e9575452242506d336c30ccf1c3739", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.0.0-23.tgz", "fileCount": 3, "integrity": "sha512-oPbbAitsulZ1czKOp7KTOdOVgMw9rFt1AC5+vKXCwEp+7rBFDuehX7dWEGyogsgMevW8oMfrQHBjxIj9tdKIkg==", "signatures": [{"sig": "MEUCIQDuDI+oc44MYq9Qma6YkId+TkWXGlcu/jYcLoYkyjQnqAIgTpdYc4je/BYU1x94RzpBe6dM5umN2ndOlTPoXZ01xYU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2701290}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "f1d93caae901c556ffb1e2f553428754038d65c1", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.0.0-23_1695759274419_0.476045368328452", "host": "s3://npm-registry-packages"}}, "4.0.0-24": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.0.0-24", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.0.0-24", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "5182616b6f46950007211171aa8762a5467a5649", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.0.0-24.tgz", "fileCount": 3, "integrity": "sha512-3gXFBlG5f18xbhVxKTM+zwciJPk097i3YswLI9cajVd4MAqMw5bGbuZkGOZOMnkzeIX0ELxovYWPbGDyUr+f5g==", "signatures": [{"sig": "MEQCIEHiPVdgidlYJtZwrPJVrjSiX2UtOKhLvz89/jDy5z8NAiAYg+cK+QCn9IOK06/2G3yM3AbM6bFzBVg/H0NsNOAlYA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2793450}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "ced077f2920c473c4c2ca31a8d72b259bec91f67", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.0.0-24_1696309976883_0.926615814306037", "host": "s3://npm-registry-packages"}}, "4.0.0-25": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.0.0-25", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.0.0-25", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "042625ef39232a7f458942e577e9d101b89943be", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.0.0-25.tgz", "fileCount": 3, "integrity": "sha512-/VfsZVBfohV1KGfGSgR/aznvG4T7FnALrocQWFq814msoRawMFkDgtTohRDSV7ih0fxJX8Zf/D+VIacXLmzp7Q==", "signatures": [{"sig": "MEQCIDVQL0QbYDLr1449CjDYMvcMPDV8wCZRBP9+2fBM80/VAiA40PNgQjRLQ/NI/dWllZiqVsMAgk0nOMJ5WO1cYEYZdQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2792426}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "1ac6bbc437c7ed0de3ad23e4e0904f00783e703d", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.0.0-25_1696515197751_0.8696373977014245", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.0.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.0.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "93f72239a043589fba7d06522180bc15835854e7", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.0.0.tgz", "fileCount": 3, "integrity": "sha512-1pl05L51RbVLnqZTEpbgG2RxeS7VLysF7vhU8v1EOAMqbLzko64r8+S2SxsNDKODsgusFqHO8rc3w+G9VUjodw==", "signatures": [{"sig": "MEQCIHBbw3C3XYD51b4CIVMGwwqi7Kohad57yhwO/okzPNr4AiA92VqBAOOu+wFXQ+i7eEfBEKmrWY6QLBa9IVHsdvcMHA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2792423}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "2f261358c62b4f9e62cb86bf99de8d4ff3668994", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.0.0_1696518891167_0.024984423533192857", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.0.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.0.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "5eb6fe4f04ac74d34701b53d67d00fc8d989a6f6", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.0.1.tgz", "fileCount": 3, "integrity": "sha512-WLHoLsYM5MFbChbLZFyCg0PwqncENl/UKECSYmlSW8X/LZ1KWhBBVrPcI491+mW9BrxdlR0yi/cGGSrC9dr/ig==", "signatures": [{"sig": "MEUCIFGz9oupN3WtfNqFrWO5TFOiXyeqfPG7TLMX0HIlnHWUAiEAgP/e0L2kha/+0xGclo8Qs6fpi4ZbaofpW0nvq/xD4Jc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2798567}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "fcab1f610fefb24621ce001dfb0831dd30e59ab3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.0.1_1696595814922_0.09669106478972589", "host": "s3://npm-registry-packages"}}, "4.0.2": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.0.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.0.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "da89fe49f5384235ccb846e93b2c316551f2474a", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.0.2.tgz", "fileCount": 3, "integrity": "sha512-3SKjj+tvnZ0oZq2BKB+fI+DqYI83VrRzk7eed8tJkxeZ4zxJZcLSE8YDQLYGq1tZAnAX+H076RHHB4gTZXsQzw==", "signatures": [{"sig": "MEUCIFyCPAA/RnZI7ws6ayqhiKhwAvDY/kyrjVDfswxFsdY5AiEAi+h1tyaEfeTTSAGpu4OSEOgWxQS0GoFZQagZXITShEk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2798567}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "3d9c833c4fcb666301967554bac7ab0a0a698efe", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.0.2_1696601934534_0.9808950844019964", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.1.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.1.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "295318a237267b2b7882ef1d4cbbbae88388b92f", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.1.0.tgz", "fileCount": 3, "integrity": "sha512-SQKgw9m7bCvrNu/Gy5bRBRqosM0T7AWMEL3f1ikBqmwsCjsXQBGvJrCYkvZ3Lp5UAkwU7EgSdP6KtnM3Z8PajA==", "signatures": [{"sig": "MEYCIQDQBcrA/oZC838Go4HAnlmqW+OCZ3NNQXUGPG9oc3LL3AIhAMVkdhLxT8KPKEjwS2YfrgECp+gn9quqObtZrsqp70dZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2847719}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "cb144b2be4262b3743b31983b26f7fa985be3ceb", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.1.0_1697262745954_0.9513242616595519", "host": "s3://npm-registry-packages"}}, "4.1.1": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.1.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.1.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "c25189bc45599183d24c3f95a412c8661513e547", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.1.1.tgz", "fileCount": 3, "integrity": "sha512-Vwwzacn8Wxcfu/zSQx722dFYvKsJO1do0YssCkByBVjwZPzt/k8aWpJ25pqOlqw3h7g8zxrFBpRazIo21QnZqw==", "signatures": [{"sig": "MEQCIC4TGZyyxuw1LobvOIW/OUd5/8H1g+oFTjqv6C0+pTZsAiAhPWdd3OXQ9pH/ohZgNGb3XSG27lcJOFi/JNzub1uVug==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2742759}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "d8b31a202a246758b8d67eefe77361a894d37005", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.1.1_1697351519864_0.34754396256265463", "host": "s3://npm-registry-packages"}}, "4.1.3": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.1.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.1.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "69c3384c1bbdc246585c392c56994a33ad2f9c4b", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.1.3.tgz", "fileCount": 3, "integrity": "sha512-JcnmTz5d2kcxKTjcPPt0p86IzhViYOj8f8qFWwyzgDpCHFLXAKv9C4uxnwTornHvWq31km7zZ8IBZui8LjX52w==", "signatures": [{"sig": "MEQCICvrQysk7gGIga72nWa0m0dmHIDn1sqHLEWOcsYSBeEqAiBcJbjhx8IKRaZPQEZp63SftPvztp9uIxol5ogLMqKCgA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2742759}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "c61a1507a88fc71be431550642b040da4b9422b0", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.1.3_1697392118650_0.6385094431474687", "host": "s3://npm-registry-packages"}}, "4.1.4": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.1.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.1.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "6aab05c9a60f952cf5a263ebca244aa225fbde63", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.1.4.tgz", "fileCount": 3, "integrity": "sha512-XG2mcRfFrJvYyYaQmvCIvgfkaGinfXrpkBuIbJrTl9SaIQ8HumheWTIwkNz2mktCKwZfXHQNpO7RgXLIGQ7HXA==", "signatures": [{"sig": "MEUCIQDdqNbbvz0EBYhZ8iTfF2RsWw4oL2QlcAKAs808TizddgIgdy7fOhPM2cIXLbEatz4iqPSL8nRA3U8M4J6CnVh4I30=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2707943}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "061a0387c8654222620f602471d66afd3c582048", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.1.4_1697430861488_0.9190061632160029", "host": "s3://npm-registry-packages"}}, "4.1.5": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.1.5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.1.5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "a54c21255306f21429abf2a5c8b05909fe27b0e1", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.1.5.tgz", "fileCount": 3, "integrity": "sha512-uVQyBREKX9ErofL8KAZ4iVlqzSZOXSIG+BOLYuz5FD+Cg6jh1eLIeUa3Q4SgX0QaTRFeeAgSNqCC+8kZrZBpSw==", "signatures": [{"sig": "MEUCIQCvJnfmuhvXiacydYBpuLs2XmQqwTBqD++6KZFYXWQJBwIgZ6C/jp4d54a2HR6a4mu530VB8pg/QVNzQD45WvrppGw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2717159}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "1cbb382b0dd3ab70541671c105f96eff283904ec", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.1.5_1698485024350_0.29912213906826945", "host": "s3://npm-registry-packages"}}, "4.1.6": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.1.6", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.1.6", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "96ee6898f35459d7c74c873a25db6cb7a5b80160", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.1.6.tgz", "fileCount": 3, "integrity": "sha512-Rz/WVQaDzc5O99Fm8H6X4J1YSjaDqXdzsPw5VAV4oJDSma1ghKf8Gvqiu8C4oph0B101T1ZOlLB1T+ZbM0qP1w==", "signatures": [{"sig": "MEUCIQCRmvG5TmotSjJbIX2DlQckWNsXf20eL0jdg2m7ru3ogAIgUC6iPTTnbT8lfUnNofEcx2lsswXlLQ7X3yJa+QJxz9Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2717159}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "5901e545697b36326110d89ed02964fdaffd9f6f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.1.6_1698731127487_0.5943385929559477", "host": "s3://npm-registry-packages"}}, "4.2.0": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.2.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.2.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "da08f1d2b0a6c58ca59aea026c2ffc5592ee8194", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.2.0.tgz", "fileCount": 3, "integrity": "sha512-1LZJ6zpl93SaPQvas618bMFarVwufWTaczH4ESAbFcwiC4OtznA6Ym+hFPyIGaJaGEB8uMWWac0uXGPXOg5FGA==", "signatures": [{"sig": "MEQCID/5NkA81KNSe+Dl3QSJXdOdlGb04o0ZVELPHnXTEQ8tAiBBHMynolu+9h/sv3AVLIKVmbkdhdXUAOBwASi74VwuXQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2732007}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "fbf806aceffd822d43e4603b664c54165c72cf36", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.2.0_1698739853641_0.7078989944685559", "host": "s3://npm-registry-packages"}}, "4.3.0": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.3.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.3.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "a0b1f79afde51e390a7725b7c15ab4e0df780aea", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.3.0.tgz", "fileCount": 3, "integrity": "sha512-DDZ5AH68JJ2ClQFEA1aNnfA7Ybqyeh0644rGbrLOdNehTmzfICHiWSn0OprzYi9HAshTPQvlwrM+bi2kuaIOjQ==", "signatures": [{"sig": "MEUCIGR/LyIo4/KH0kw0AGlKYUEQJPcwFOcoqe0uIBJFiZjHAiEA4tPq4hVGuvVfEBgSJWzFlHpnCVQZXeoalVn+KcYsaRw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2739175}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "937d9911376574c42f893e1cd14b55418c4f7b68", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.3.0_1699042396465_0.4435891419761484", "host": "s3://npm-registry-packages"}}, "4.3.1": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.3.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.3.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "4a1b97cce679efda4b1e4727999a8d2dd00b2b69", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.3.1.tgz", "fileCount": 3, "integrity": "sha512-/B5g1WqoXecmHyVsXsSGWfGE+QqiSIMk2I4+EOGcziXfZsUHoUbwXwaiAy5Sir/xUwdi9nEZDqj4jxwMchZPkQ==", "signatures": [{"sig": "MEYCIQC/NYUefxwCO3npAkMyYb0j7NgtAypQFtmTWdByjordNwIhAObDlQUQqCjl7NZeYgYUL0cH6iH4g4qLCwpzxBumDch/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2898407}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "52c55bb1e17154ae6d01fb40e0e4a3589bc20a8f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.3.1_1699689485512_0.5352764954894791", "host": "s3://npm-registry-packages"}}, "4.4.0": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.4.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.4.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "ee29090b93d69f84c3bc8da924c491ef6419b8da", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.4.0.tgz", "fileCount": 3, "integrity": "sha512-SoLQmJanozFow8o50ul2a3R+J7nk4pEhrp83PzTSXs5OzOmIZbPSp5kihtQ3f6ypo4MCbmh0V8Ev0bJIEp4Azw==", "signatures": [{"sig": "MEQCIH0Lb/XUP0tlcQ8jBaypL+3cYWClF+ZMUrZeXaQd8e3xAiBanFBZ3gQpsAy/KqUB/x6CHJv8gTUXZ2LAFKwMxNa5aw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2572263}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "53d636051ac60da9b302c4bd6b7eaaccb4871f4b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.4.0_1699775403716_0.5686817881355111", "host": "s3://npm-registry-packages"}}, "4.4.1": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.4.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.4.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "8bb9e8fbf0fdf96fe3bebcee23f5cfdbbd9a4a0a", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.4.1.tgz", "fileCount": 3, "integrity": "sha512-tLpKb1Elm9fM8c5w3nl4N1eLTP4bCqTYw9tqUBxX8/hsxqHO3dxc2qPbZ9PNkdK4tg4iLEYn0pOUnVByRd2CbA==", "signatures": [{"sig": "MEUCIQCl+ds0TzBB2f0V+r7luoTKL6T9/yRlQJjXW2hPrsH6IQIgZGyOAbkhYM/aHQEhIxXiS6g6ns0Jp6xp08BbzDY7Suw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2572263}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "01d8c9d1b68919c2c429427ae7e60f503a8bb5f4", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.4.1_1699939545485_0.6238485663736901", "host": "s3://npm-registry-packages"}}, "4.5.0": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.5.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.5.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "798614b191f9ce1dc58079d1dfbc234c71df9e0e", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.5.0.tgz", "fileCount": 3, "integrity": "sha512-Al6quztQUrHwcOoU2TuFblUQ5L+/AmPBXFR6dUvyo4nRj2yQRK0WIUaGMF/uwKulvRcXkpHe3k9A8Vf93VDktA==", "signatures": [{"sig": "MEQCIAdhwyyxme4p9Xt7DXH5xPXNSmau0zJjtu/NNXyh6CvjAiAC9cv1O2SiIdfqJeS6kmR5O3mGpNX4fqvC3+BFAWVIvg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2566119}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "86efc769f693516a29047c8d160c6d7287fb965d", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.5.0_1700286742351_0.4062896507979943", "host": "s3://npm-registry-packages"}}, "4.5.1": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.5.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.5.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "6f9e8b30a4d6b5c564bfe55cdf44a5b493139838", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.5.1.tgz", "fileCount": 3, "integrity": "sha512-zK9MRpC8946lQ9ypFn4gLpdwr5a01aQ/odiIJeL9EbgZDMgbZjjT/XzTqJvDfTmnE1kHdbG20sAeNlpc91/wbg==", "signatures": [{"sig": "MEYCIQDb8iWOvuzb6tRrduHLRpIDcod435CDfiII2SRSsygZHAIhAJj48b7Q2xusFa5rtUgBI0pQaZ0UJerwuAeNOi++Pu1G", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2566119}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "a083019c7f0c18a1c17260ab1239b12400984a88", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.5.1_1700597599717_0.6091890671661897", "host": "s3://npm-registry-packages"}}, "4.5.2": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.5.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.5.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "4b799c851864a10491a4755b5b8eadceae3e9889", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.5.2.tgz", "fileCount": 3, "integrity": "sha512-J5z<PERSON>3riR4AOyU/J3M/i4k/zZ8eP1yT+nTmAKztCXJtnI36jYH0eepvob22mAQ/kLwfsK2TB6dbyVY1F8c/0H5A==", "signatures": [{"sig": "MEUCIQDsy6D4EQxJMSWR+cemQQ2EWW6c/2VQAD59nNQ9c4G+8AIgPM8Ckiy0Iayw7ukZ0E/pniuURYpn+h3KdoIXq11g6D0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2591719}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "2e94641971195c1a4eb9e1a3fe6d73b9d04ffae0", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.5.2_1700807400266_0.0931035471686048", "host": "s3://npm-registry-packages"}}, "4.6.0": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.6.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.6.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "7d5fb96e9f0120451da1fece5c74d2bb373f8925", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.6.0.tgz", "fileCount": 3, "integrity": "sha512-rxfeE6K6s/Xl2HGeK6cO8SiQq3k/3BYpw7cfhW5Bk2euXNEpuzi2cc7llxx1si1QgwfjNtdRNTGqdBzGlFZGFw==", "signatures": [{"sig": "MEUCIQC5oZiHInwOSv8PU7hQu9xOYILTk9zi1sScPmOnX6iWsQIgUt/AtWO21N6sUnOJAWNiaEGiMEA/1rmIy9r8IWHcWP8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2591719}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "020774d0c7b1371865b20878e59dd3a6a45d3b31", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.6.0_1701005963386_0.35626557474273035", "host": "s3://npm-registry-packages"}}, "4.6.1": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.6.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.6.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "a95db026c640c8128bfd38546d85342f2329beaf", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.6.1.tgz", "fileCount": 3, "integrity": "sha512-YEeOjxRyEjqcWphH9dyLbzgkF8wZSKAKUkldRY6dgNR5oKs2LZazqGB41cWJ4Iqqcy9/zqYgmzBkRoVz3Q9MLw==", "signatures": [{"sig": "MEUCIHb6V7w424dDL4jOKDqilLc1l/b/WEhX599lDYXQjkNgAiEA0iuY8oZ1cp2AC6wjmZ07N/vXQK/GOvt0MAzWEgDNoR4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2591719}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "ded37aa8f95d5ba9786fa8903ef3424fd0549c73", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.6.1_1701321800478_0.20851474548314686", "host": "s3://npm-registry-packages"}}, "4.7.0": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.7.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.7.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "f9ca12cc03ebca191ff6b832785c5a5e1974cf55", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.7.0.tgz", "fileCount": 3, "integrity": "sha512-pmioUlttNh9GXF5x2CzNa7Z8kmRTyhEzzAC+2WOOapjewMbl+3tGuAnxbwc5JyG8Jsz2+hf/QD/n5VjimOZ63g==", "signatures": [{"sig": "MEYCIQCnVH1auX8yy9Dqr1mEoOhozZjSMdmn5KkAwHpsxGalaQIhAMWUE4hPc8wnGUOE6p0OLmDASBpcGNrEh+07N9xMibwd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2564583}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "098e29ca3e0643006870f9ed94710fd3004a9043", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.7.0_1702022292436_0.03347019544073726", "host": "s3://npm-registry-packages"}}, "4.8.0": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.8.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.8.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "794ef4058d04f97447e4434c083b1309b2be73c2", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.8.0.tgz", "fileCount": 3, "integrity": "sha512-p9E3PZlzurhlsN5h9g7zIP1DnqKXJe8ZUkFwAazqSvHuWfihlIISPxG9hCHCoA+dOOspL/c7ty1eeEVFTE0UTw==", "signatures": [{"sig": "MEUCIQDrgw/C4kq70cPAVJWBjcvYayfCwJxKApCqwUxECkU59gIgUu1zlNN/5Vb7qQ563TABhomP+blgXd9C0/O8uUA3QY0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2564583}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "62b648e1cc6a1f00260bb85aa2050097bb4afd2b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.8.0_1702275907680_0.9787103102500745", "host": "s3://npm-registry-packages"}}, "4.9.0": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.9.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.9.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "5c26b07f74f4054f3ecf202550100496ed2e73f3", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.9.0.tgz", "fileCount": 3, "integrity": "sha512-WE4pT2kTXQN2bAv40Uog0AsV7/s9nT9HBWXAou8+++MBCnY51QS02KYtm6dQxxosKi1VIz/wZIrTQO5UP2EW+Q==", "signatures": [{"sig": "MEUCIEfFB5O0qO7l53Vm5kbT8uQwJ4+1/HaeryA+v5QI4n4YAiEA24CsVTrz7U3i7UlfnX+3MIoH/dhmAsGPLF1LxoWqsqY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2564583}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "c5337ef28a71c796e768a9f0edb3d7259a93f1aa", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.9.0_1702459467606_0.041872732887568764", "host": "s3://npm-registry-packages"}}, "4.9.1": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.9.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.9.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "34156ebf8b4de3b20e6497260fe519a30263f8cf", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.9.1.tgz", "fileCount": 3, "integrity": "sha512-yE5c2j1lSWOH5jp+Q0qNL3Mdhr8WuqCNVjc6BxbVfS5cAS6zRmdiw7ktb8GNpDCEUJphILY6KACoFoRtKoqNQg==", "signatures": [{"sig": "MEQCIA0Zt1PRNp0mC9SCOAlrXv8LD4FaIWnAtoyUMr5l08FjAiBEYUWwbC3JQh8FVcjC41XwFTeI2aE6UxnHbArDcZ85jA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2590695}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "d56ac63dc0452820272a0d7536340277f7db68bf", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.9.1_1702794380995_0.0000826514876788309", "host": "s3://npm-registry-packages"}}, "4.9.2": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.9.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.9.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "657288cff10311f997d8dbd648590441760ae6d9", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.9.2.tgz", "fileCount": 3, "integrity": "sha512-VVzCB5yXR1QlfsH1Xw1zdzQ4Pxuzv+CPr5qpElpKhVxlxD3CRdfubAG9mJROl6/dmj5gVYDDWk8sC+j9BI9/kQ==", "signatures": [{"sig": "MEQCIAk+4cTS+P0XowaoKvHS9xG5bkCP8xLi58Qhh5KjJLHpAiAI1Ky8avBQizXHkBtO1zBBXYKKJuwUNNuZDjlJdHe//w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2594279}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "347a34745b2679c1192535db3c0f60889861d3ad", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.9.2_1703917416651_0.21912468697716547", "host": "s3://npm-registry-packages"}}, "4.9.3": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.9.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.9.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "4497f66f1e1e91c4e93561fe4587ce079fb2d80f", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.9.3.tgz", "fileCount": 3, "integrity": "sha512-wxomCHjBVKws+O4N1WLnniKCXu7vkLtdq9Fl9CN/EbwEldojvUrkoHE/fBLZzC7IT/x12Ut6d6cRs4dFvqJkMg==", "signatures": [{"sig": "MEUCIQDg7fI5LiNL5OibGWBgdZjEVrTBr4HXjiXir765oAowcwIgV3cLWwgaZ7y78zE8BO+NBq7Vbs4QwZYQK+l2VXM7Lk4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2605031}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "4ab3ad360457cd79f4ea852447d3ddca22da95d6", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.9.3_1704435656127_0.6155000689877153", "host": "s3://npm-registry-packages"}}, "4.9.4": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.9.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.9.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "71b6facad976db527863f698692c6964c0b6e10e", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.9.4.tgz", "fileCount": 3, "integrity": "sha512-z+JQ7JirDUHAsMecVydnBPWLwJjbppU+7LZjffGf+Jvrxq+dVjIE7By163Sc9DKc3ADSU50qPVw0KonBS+a+HQ==", "signatures": [{"sig": "MEQCIGtx3VawyzDODW/3LTgUQ2bA8iIacEostSrxSzyjtgt3AiBsgK70+NdZaxN5VerO1BwtXZRoOyr85loqvh3BuYKb/Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2605031}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "18372035f167ec104280e1e91ef795e4f7033f1e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.9.4_1704523149895_0.44801075351338526", "host": "s3://npm-registry-packages"}}, "4.9.5": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.9.5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.9.5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "7b73a145891c202fbcc08759248983667a035d85", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.9.5.tgz", "fileCount": 3, "integrity": "sha512-AiqiLkb9KSf7Lj/o1U3SEP9Zn+5NuVKgFdRIZkvd4N0+bYrTOovVd0+LmYCPQGbocT4kvFyK+LXCDiXPBF3fyA==", "signatures": [{"sig": "MEUCICRj4Grso8ZhPS7PXqdzMsV2zHe2M7xoty7KvKuaKtYNAiEAzKk4TKf+LoSHKFCGz8P5zPBHYXtVa7wWWYuppQJG1A0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2579431}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "7fa474cc5ed91c96a4ff80e286aa8534bc15834f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.9.5_1705040182894_0.005446066320881737", "host": "s3://npm-registry-packages"}}, "4.9.6": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.9.6", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.9.6", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "7d2251e1aa5e8a1e47c86891fe4547a939503461", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.9.6.tgz", "fileCount": 3, "integrity": "sha512-J9AFDq/xiRI58eR2NIDfyVmTYGyIZmRcvcAoJ48oDld/NTR8wyiPUu2X/v1navJ+N/FGg68LEbX3Ejd6l8B7MQ==", "signatures": [{"sig": "MEUCIBOMYkFqK5yCpRPvqZx1WpSrw1DF995xOQ1gvb7G//fQAiEA28it7H4YXKtugPBGXJVuuniwY8VaNz8+FQY5sbclyWA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2578407}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "ecb6b0a430098052781aa6ee04ec92ee70960321", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.9.6_1705816348354_0.023677246609962177", "host": "s3://npm-registry-packages"}}, "4.10.0": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.10.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.10.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "29d50292381311cc8d3623e73b427b7e2e40a653", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.10.0.tgz", "fileCount": 3, "integrity": "sha512-FV0Tpt84LPYDduIDcXvEC7HKtyXxdvhdAOvOeWMWbQNulxViH2O07QXkT/FffX4FqEI02jEbCJbr+YcuKdyyMg==", "signatures": [{"sig": "MEUCIQCrucGY2TZftOmbwXyK+WEgP5ecfMXzp8qVwydKQw5z4wIgY/lwT6sCDZHCRAJNnfv6uP11qmCTR4RLXwmFGXFjtq8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2638312}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "762420860765e8e46e24d48b38f5b98ca31735fa", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.10.0_1707544730838_0.09787271065273528", "host": "s3://npm-registry-packages"}}, "4.11.0": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.11.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.11.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "ff9281d8b189436b7c234a52e7e0b635631c8667", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.11.0.tgz", "fileCount": 3, "integrity": "sha512-xZVt6K70Gr3I7nUhug2dN6VRR1ibot3rXqXS3wo+8JP64t7djc3lBFyqO4GiVrhNaAIhUCJtwQ/20dr0h0thmQ==", "signatures": [{"sig": "MEUCIBhC1XENkPHqvucMVjUu+MFRZIHeS1gAi18zNfKBMQ+DAiEA0EVrc73rS0D+WcMO32gthrDu25W2PWiFVUm4Z22hxXM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2638312}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "90ad652b745c5fe7167d92b4ad671cc387577a99", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.11.0_1707977392854_0.07864569248869113", "host": "s3://npm-registry-packages"}}, "4.12.0": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.12.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.12.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "366ca62221d1689e3b55a03f4ae12ae9ba595d40", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.12.0.tgz", "fileCount": 3, "integrity": "sha512-fjtuvMWRGJn1oZacG8IPnzIV6GF2/XG+h71FKn76OYFqySXInJtseAqdprVTDTyqPxQOG9Exak5/E9Z3+EJ8ZA==", "signatures": [{"sig": "MEYCIQDZeATTx1DwjlGoNQdk9h2JiQNtg1hyUPT+Dh8uGugHWwIhAKRJziFgyEk+DdQUX6LIq5OfqKKylkzHFgmfQoWIaCSG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2640872}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "0146b84be33a8416b4df4b9382549a7ca19dd64a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.12.0_1708090352124_0.6833779337949057", "host": "s3://npm-registry-packages"}}, "4.12.1": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.12.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.12.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "0d252acd5af0274209c74374867ee8b949843d75", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.12.1.tgz", "fileCount": 3, "integrity": "sha512-LN+vnlZ9g0qlHGlS920GR4zFCqAwbv2lULrR29yGaWP9u7wF5L7GqWu9Ah6/kFZPXPUkpdZwd//TNR+9XC9hvA==", "signatures": [{"sig": "MEUCIGphuB5mkFpwafOYpJUVcA1dN/jt8sCO2eOTpAQBw8ylAiEAruJqqW3b62piq4u9CTLM6GcXpXCVje4I1CahG+55OxI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2626024}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "f44dac3170a671b0978afa3af43818617904f544", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.12.1_1709705028033_0.3780257525493673", "host": "s3://npm-registry-packages"}}, "4.13.0": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.13.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.13.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "a7505884f415662e088365b9218b2b03a88fc6f2", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.13.0.tgz", "fileCount": 3, "integrity": "sha512-P5/MqLdLSlqxbeuJ3YDeX37srC8mCflSyTrUsgbU1c/U9j6l2g2GiIdYaGD9QjdMQPMSgYm7hgg0551wHyIluw==", "signatures": [{"sig": "MEQCIHZc0/sprXZUX2LvxrboVUXXVCZdAjQjHvOden0ZwdI8AiBOdlpLGiSjHq5nbmrbqtun5JtaIbnJwa3+e5fwr0a1hw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2617832}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "1c8afed74bd81cd38ad0b373ea6b6ec382975013", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.13.0_1710221332800_0.06688079156200555", "host": "s3://npm-registry-packages"}}, "4.13.1-1": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.13.1-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.13.1-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "0182cc4df45037383f3ec67301af01f8dad46005", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.13.1-1.tgz", "fileCount": 3, "integrity": "sha512-ib0JRkCdIPAVIIt6p5+NcX75aWIW//Tv3nRkCWm4RtFRVgWmgp23uACyzvjhY9qJnORrYkae5+AFnQo6Dwml6g==", "signatures": [{"sig": "MEUCIQDA/A9R4CH8DQTDrf5VfOR5Vrttpa4YQ0/3VFfCYVrRBAIgCoUUdkyZK4DAPmn8xCdVjMZ2n3XKLFvt75dUo+v2iWg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2626538}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "84797d177bee161df233644292bc8f128b989cea", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.13.1-1_1711265974332_0.5020921641243894", "host": "s3://npm-registry-packages"}}, "4.13.1": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.13.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.13.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "d6545a29ac9dd8b39a9161b87924f13471eb992e", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.13.1.tgz", "fileCount": 3, "integrity": "sha512-+dI2jVPfM5A8zme8riEoNC7UKk0Lzc7jCj/U89cQIrOjrZTCWZl/+IXUeRT2rEZ5j25lnSA9G9H1Ob9azaF/KQ==", "signatures": [{"sig": "MEUCIESDVStFhbLUlu+05xEaRcmkS2dez3FrR4IOK8oaHLhIAiEAsoZ1KOKIkSqy01LLluOOgM/npsAxT+PRLoMFYLwt1JU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2626536}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "fffaedeaa1cf9c8f6efc93d53bb8a81738e0ce87", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.13.1_1711535273561_0.5638164998074384", "host": "s3://npm-registry-packages"}}, "4.13.2": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.13.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.13.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "bc1c5a4fbc4337d6cb15da80a4de95fd53ab3573", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.13.2.tgz", "fileCount": 3, "integrity": "sha512-2FSsE9aQ6OWD20E498NYKEQLneShWes0NGMPQwxWOdws35qQXH+FplabOSP5zEe1pVjurSDOGEVCE2agFwSEsw==", "signatures": [{"sig": "MEUCIHXPbYqw1aOrqFZcf4bTmKMWdGTensBcW0EMA0wEIo/7AiEAulDUmxoBXeAvdvQOJW4sn3KLJO2KhLjMtkmeJMErwQw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2626536}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "b379a592234416a2084918b0eea4c81865a1579f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.13.2_1711635229008_0.40128796348295404", "host": "s3://npm-registry-packages"}}, "4.14.0": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.14.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.14.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "d7701438daf964011fd7ca33e3f13f3ff5129e7b", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.14.0.tgz", "fileCount": 3, "integrity": "sha512-e/PBHxPdJ00O9p5Ui43+vixSgVf4NlLsmV6QneGERJ3lnjIua/kim6PRFe3iDueT1rQcgSkYP8ZBBXa/h4iPvw==", "signatures": [{"sig": "MEQCIGX6CVAxAtDup/RpGuEZpU7CO3NGdxnWZP4qK9JAdibZAiAVFoF/aNjDfjIqzpybwP3w763hljkJRVtLv/7ATk9iTg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2623464}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "5abe71bd5bae3423b4e2ee80207c871efde20253", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.14.0_1712121785197_0.5591467223881226", "host": "s3://npm-registry-packages"}}, "4.14.1": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.14.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.14.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "c1e0bc39e20e760f0a526ddf14ae0543af796605", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.14.1.tgz", "fileCount": 3, "integrity": "sha512-TdloItiGk+T0mTxKx7Hp279xy30LspMso+GzQvV2maYePMAWdmrzqSNZhUpPj3CGw12aGj57I026PgLCTu8CGg==", "signatures": [{"sig": "MEUCIEJ18qHr9YmJBtAx+bBzx6gcOu1F8JWTT79Tnyh2x6NvAiEA+vEshhdpq9ao4TOWbXoxt6vwaoqUNN8ynDz0VaAuVzQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2642920}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "0b665c31833525c923c0fc20f43ebfca748c6670", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.14.1_1712475349650_0.855945428752086", "host": "s3://npm-registry-packages"}}, "4.14.2": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.14.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.14.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "f27f9fb64b7e10b04121e0054d9145ee21589267", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.14.2.tgz", "fileCount": 3, "integrity": "sha512-djqpAjm/i8erWYF0K6UY4kRO3X5+T4TypIqw60Q8MTqSBaQNpNXDhxdjpZ3ikgb+wn99svA7jxcXpiyg9MUsdw==", "signatures": [{"sig": "MEYCIQDsEk5dCwHN3YLUc+iN3qedSG3j+K62uvuDg56Axl7lvwIhALnKl3BVr3xAYUkJYQt92m1KEW7oHyweOTIg4F9ZyQ1W", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2641384}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "7275328b41b29605142bfdf55d68cb54e895a20c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.14.2_1712903030180_0.8089729815440385", "host": "s3://npm-registry-packages"}}, "4.14.3": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.14.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.14.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "34727b5c7953c35fc6e1ae4f770ad3a2025f8e03", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.14.3.tgz", "fileCount": 3, "integrity": "sha512-T1l7y/bCeL/kUwh9OD4PQT4aM7Bq43vX05htPJJ46RTI4r5KNt6qJRzAfNfM+OYMNEVBWQzR2Gyk+FXLZfogGw==", "signatures": [{"sig": "MEYCIQDib8Gf5L3a7WsU+4V7B+m1p0V0QyipVlCqKzRyFO7NDQIhALm2C9VuqSUCgbNSTYZHiX0Sdspv5ObIwXwZ/9i0r8lJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2639848}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "e64f3d8d0cdc561f00d3efe503e3081f81889679", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.14.3_1713165523868_0.7690040027220617", "host": "s3://npm-registry-packages"}}, "4.15.0": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.15.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.15.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "acbd48f10093e6cd52f99ad004966433a49cb362", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.15.0.tgz", "fileCount": 3, "integrity": "sha512-5O49NykwSgX6iT2HgZ6cAoGHt6T/FqNMB5OqFOGxU/y1GyFSHquox1sK2OqApQc0ANxiHFQEMNDLNVCL7AUDnQ==", "signatures": [{"sig": "MEYCIQCzd5kzwRCkpP/qQ+QQ6FBeLfdeyZ8vcUdLf96mGVWz7QIhANSjr+nhkaPrNRUacM6lwOFmTKKWFqoa9Me4kJvHdfDi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2725352}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "e6e05cde31fc144228bb825c9d4ebba2f377075c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.15.0_1713591447601_0.6462324050089827", "host": "s3://npm-registry-packages"}}, "4.16.0": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.16.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.16.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "1d9c9477138236a7588ee601513508854f674c69", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.16.0.tgz", "fileCount": 3, "integrity": "sha512-7qLyKTL7Lf2g0B8bduETVAEI3WVUVwBRVcECojVevPNVAmi19IW1P2X+uMSwhmWNy36Q/qEvxXsfts1I8wpawg==", "signatures": [{"sig": "MEUCIAGjDb9ICfZSehuoZNqFq68sEaqmefC1//BXc20pf7HPAiEA3xytBWOKE+a2g4YKjJNHi8UYe6vIF2LvIe8+kKuVfOM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2725352}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "38fe70780cb7e374b47da99e3a3dca6b2a2170d2", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.16.0_1713674547131_0.9507077854295247", "host": "s3://npm-registry-packages"}}, "4.16.1": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.16.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.16.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "4e7b57e757c95da8e79092056d1b428617515668", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.16.1.tgz", "fileCount": 3, "integrity": "sha512-Y3M92DcVsT6LoP+wrKpoUWPaazaP1fzbNkp0a0ZSj5Y//+pQVfVe/tQdsYQQy7dwXR30ZfALUIc9PCh9Izir6w==", "signatures": [{"sig": "MEQCIBYlXP7FzrYocIYVFMnoQZEbs/gL/CkoWjNvLgRUOkkMAiAIHlsR/g+ABwoYr1YEONpcWF79rHOle+ZecS+xQTc+Mg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2725352}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "5d8019b901e98cc8895751a23e5edfc9135b1a35", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.16.1_1713724214880_0.7870136854170309", "host": "s3://npm-registry-packages"}}, "4.16.2": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.16.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.16.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "f17cc1db108f364bf6ef427f98844b5f742d31f0", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.16.2.tgz", "fileCount": 3, "integrity": "sha512-J/jCDKVMWp0Y2ELnTjpQFYUCUWv1Jr+LdFrJVZtdqGyjDo0PHPa7pCamjHvJel6zBFM3doFFqAr7cmXYWBAbfw==", "signatures": [{"sig": "MEQCIDLLJlZOfxa3UY6TWqH8DePWH4Ucq0CXYAFju8Ad0njJAiBQyEd+DJ3IA4iS/01Q735sA2pQ+uKm7reF41c/12KaCg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2725352}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "18839eb234f79adc44a591e355fd7b3243a4cd21", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.16.2_1713799172561_0.9970936251956268", "host": "s3://npm-registry-packages"}}, "4.16.3": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.16.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.16.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "39243a45f34452e3852a7924f074d4bbbdd0f1ec", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.16.3.tgz", "fileCount": 3, "integrity": "sha512-0NxVbLhBXmwANWWbgZY/RdSkeuHEgF+u8Dc0qBowUVBYsR2y2vwVGjKgUcj1wtu3jpjs057io5g9HAPr3Icqjg==", "signatures": [{"sig": "MEUCIDb5BZ3rqLbp+w8DY8w94PF4WIpa9EC3UsvUQAud/ViVAiEAqaarp5Jtmz1fGVx+0dJgNgVey21WVwJx7uRIMO7FSa0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2725352}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "b9a62fd4cf28538d7c3b268eb25e709b45d44cce", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.16.3_1713849166364_0.24753042411005177", "host": "s3://npm-registry-packages"}}, "4.16.4": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.16.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.16.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "aea0b7e492bd9ed46971cb80bc34f1eb14e07789", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.16.4.tgz", "fileCount": 3, "integrity": "sha512-9m/ZDrQsdo/c06uOlP3W9G2ENRVzgzbSXmXHT4hwVaDQhYcRpi9bgBT0FTG9OhESxwK0WjQxYOSfv40cU+T69w==", "signatures": [{"sig": "MEQCIBHX1TGnz0PqjB5VaOeGD2CQ9U2xIa61EKVemwU5nhyqAiBRrNaPUaHKk9eNhulXw6AOryiBRMSxN4NWJNMynkBdmg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2725352}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "1c404fa352b70007066e94ff4c1981f8046f8cef", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.16.4_1713878119150_0.13647067163053617", "host": "s3://npm-registry-packages"}}, "4.17.0": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.17.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.17.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "b9a2dd65241a78a434592b917430702b3c360fc1", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.17.0.tgz", "fileCount": 3, "integrity": "sha512-4WnSgaUiUmXILwFqREdOcqvSj6GD/7FrvSjhaDjmwakX9w4Z2F8JwiSP1AZZbuRkPqzi444UI5FPv33VKOWYFQ==", "signatures": [{"sig": "MEYCIQCGouVmXgut6mYbPMBfIe8kxl1kVJDIRyNN+kr2KvzRsgIhAOyPUu6QAdqfEQAshal4eQJjZ8pEGMlfkdgbW2ww9oRQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2718184}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "91352494fc722bcd5e8e922cd1497b34aec57a67", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.17.0_1714217404377_0.3766040296601012", "host": "s3://npm-registry-packages"}}, "4.17.1": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.17.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.17.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "f13b76ecfba6820867f23b805f6626e02c7300ec", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.17.1.tgz", "fileCount": 3, "integrity": "sha512-xjgkWUwlq7IbgJSIxvl516FJ2iuC/7ttjsAxSPpC9kkI5iQQFHKyEN5BjbhvJ/IXIZ3yIBcW5QDlWAyrA+TFag==", "signatures": [{"sig": "MEQCIAVSnveOYDRqGeBvTd3b3NtvYDIhQMvV7Ympiyyx2IGLAiBQxSeTqR7hOl04+LGaOQwB+avovaROxQJ68N5OsccSkg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2718184}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "dbf0a2e5d3c3eae09ac4d502646d0ecab63f40fd", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.17.1_1714366686729_0.1773923110532576", "host": "s3://npm-registry-packages"}}, "4.17.2": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.17.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.17.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "a2fbf8246ed0bb014f078ca34ae6b377a90cb411", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.17.2.tgz", "fileCount": 3, "integrity": "sha512-7II/QCSTAHuE5vdZaQEwJq2ZACkBpQDOmQsE6D6XUbnBHW8IAhm4eTufL6msLJorzrHDFv3CF8oCA/hSIRuZeQ==", "signatures": [{"sig": "MEUCIGDOKnwA7JkpRHIhLIEAmm++Yjbp5ymYMQujWooWitsPAiEAq2RXXO8fDk8c/+Va8A2vKjo8pOyCJ4tt1hhKBaRFrdk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2718184}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "5e955a1c2c5e080f80f20f650da9b44909d65d56", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.17.2_1714453261669_0.8518719076663996", "host": "s3://npm-registry-packages"}}, "4.18.0": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.18.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.18.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "14e0b404b1c25ebe6157a15edb9c46959ba74c54", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.18.0.tgz", "fileCount": 3, "integrity": "sha512-Txjh+IxBPbkUB9+SXZMpv+b/vnTEtFyfWZgJ6iyCmt2tdx0OF5WhFowLmnh8ENGNpfUlUZkdI//4IEmhwPieNg==", "signatures": [{"sig": "MEQCIBVROzoz2bESpKirVeD73mVGZw9pDLDRn//1fO+4L1WKAiArDo6zoq3OrGkPB/ZNUnyEXxJg+JSpiN3/NxXv9BrOvw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2734056}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "bb6f069ea3623b0297ef3895f2dcb98a2ca5ef58", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.18.0_1716354237981_0.8086797011421871", "host": "s3://npm-registry-packages"}}, "4.18.1": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.18.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.18.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "075b0713de627843a73b4cf0e087c56b53e9d780", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.18.1.tgz", "fileCount": 3, "integrity": "sha512-ELfEX1/+eGZYMaCIbK4jqLxO1gyTSOIlZr6pbC4SRYFaSIDVKOnZNMdoZ+ON0mrFDp4+H5MhwNC1H/AhE3zQLg==", "signatures": [{"sig": "MEUCICdEhqlS049boEVq6IGZ/dEX2WTVLBPanyIZPWtk2IvEAiEA9OcFJBmZm758y3OUwFNJ8HhahtSoTKLtEI5mpflLiSo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2640872}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "21f9a4949358b60801c948cd4777d7a39d9e6de0", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.18.1_1720452325700_0.44131377981921793", "host": "s3://npm-registry-packages"}}, "4.19.0": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.19.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.19.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "1ed93c9cdc84e185359797a686f4d1576afcea58", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.19.0.tgz", "fileCount": 3, "integrity": "sha512-xItlIAZZaiG/u0wooGzRsx11rokP4qyc/79LkAOdznGRAbOFc+SfEdfUOszG1odsHNgwippUJavag/+W/Etc6Q==", "signatures": [{"sig": "MEQCIFrai+J1zbrNJfHdCWH4F0PcZZprMBtAJD0WRnevpyWJAiBmZmrCTqOri8jhGua3NdiPAjTOHhozO9KHambqVG/aIQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2604008}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "28546b5821efcb72c2eb05f422d986524647a0e3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.19.0_1721454388020_0.36528699405784737", "host": "s3://npm-registry-packages"}}, "4.19.1": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.19.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.19.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "0cfe740063b35dcd5a62c4e243226631a846ce11", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.19.1.tgz", "fileCount": 3, "integrity": "sha512-LdxxcqRVSXi6k6JUrTah1rHuaupoeuiv38du8Mt4r4IPer3kwlTo+RuvfE8KzZ/tL6BhaPlzJ3835i6CxrFIRQ==", "signatures": [{"sig": "MEUCIHiN/5vFLaGFk8sW7DTHzc/O2QjfQ2z/Nw+MkICfmudPAiEAnDyNAWHNYwWPoYk18A5nB6gKcbmmz/tkv0qcAAeTZE8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2567656}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "8b967917c2923dc6a02ca1238261387aefa2cb2f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.19.1_1722056053874_0.10999147098945361", "host": "s3://npm-registry-packages"}}, "4.19.2": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.19.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.19.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "a716d862f6ac39d88bdb825e27f63aeb0387cd66", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.19.2.tgz", "fileCount": 3, "integrity": "sha512-Mda7iG4fOLHNsPqjWSjANvNZYoW034yxgrndof0DwCy0D3FvTjeNo+HGE6oGWgvcLZNLlcp0hLEFcRs+UGsMLg==", "signatures": [{"sig": "MEQCIH8qRwVz0tFytc7YUWzVhQLeD/jFyY3Scui1xDggg0KcAiBoaODl5D1qLn2yTSs1ZJpj4Ph3/ORuNDpE4Ab35Rz3qQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2567656}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "39955e55dbc12ec379a21efcf8fc21e55ec6ce3a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.19.2_1722501187105_0.9889706394224786", "host": "s3://npm-registry-packages"}}, "4.20.0": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.20.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.20.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "8bc8f77e02760aa664694b4286d6fbea7f1331c5", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.20.0.tgz", "fileCount": 3, "integrity": "sha512-GabekH3w4lgAJpVxkk7hUzUf2hICSQO0a/BLFA11/RMxQT92MabKAqyubzDZmMOC/hcJNlc+rrypzNzYl4Dx7A==", "signatures": [{"sig": "MEYCIQCBDalwRro8xTMy49Fv66lsAyAiLzL+vFaIS66DKkduggIhAJaE/KdT5E2+EK4Q+w79FqNfdKmr0eEZoVU5RQjRWRg+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2580968}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "df12edfea6e9c1a71bda1a01bed1ab787b7514d5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.20.0_1722660544608_0.3337333790957533", "host": "s3://npm-registry-packages"}}, "4.21.0": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.21.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.21.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "a18ad47a95c5f264defb60acdd8c27569f816fc1", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.21.0.tgz", "fileCount": 3, "integrity": "sha512-G9+TEqRnAA6nbpqyUqgTiopmnfgnMkR3kMukFBDsiyy23LZvUCpiUwjTRx6ezYCjJODXrh52rBR9oXvm+Fp5wg==", "signatures": [{"sig": "MEUCIQCChiJzwcNTh0QJA/jQqQzJRltiTURLsSGfLQz2tfztFgIgYTKp8kuiz1SbCtII1pb8+nQ454YlSzey4xNLoDulVio=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2479592}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "c4bb050938778bcbe7b3b3ea3419f7fa70d60f5b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.21.0_1723960546623_0.35658933847683016", "host": "s3://npm-registry-packages"}}, "4.21.1": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.21.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.21.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "199648b68271f7ab9d023f5c077725d51d12d466", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.21.1.tgz", "fileCount": 3, "integrity": "sha512-tNg+jJcKR3Uwe4L0/wY3Ro0H+u3nrb04+tcq1GSYzBEmKLeOQF2emk1whxlzNqb6MMrQ2JOcQEpuuiPLyRcSIw==", "signatures": [{"sig": "MEUCIFYm+haEwZIKELEjrMweyXi+vi/uf9RSSh9jFxYnKt25AiEA7IbZq7LRIbIjSkIa8coDUl2i6cZU8fcpAmjuHB48cOY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2499048}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "c33c6ceb7da712c3d14b67b81febf9303fbbd96c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.21.1_1724687667689_0.3160040502112056", "host": "s3://npm-registry-packages"}}, "4.21.2": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.21.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.21.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "aae2886beec3024203dbb5569db3a137bc385f8e", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.21.2.tgz", "fileCount": 3, "integrity": "sha512-5rA4vjlqgrpbFVVHX3qkrCo/fZTj1q0Xxpg+Z7yIo3J2AilW7t2+n6Q8Jrx+4MrYpAnjttTYF8rr7bP46BPzRw==", "signatures": [{"sig": "MEUCIEozOUAxw9A4CF+XNIo1WIS2per9y8R4QjNg6jrpHIf5AiEAjd94q8vp3B6HbzGq9NmlNrTFdlX+xoRtjJF3rusD6ZA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2491880}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "f83b3151e93253a45f5b8ccb9ccb2e04214bc490", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.21.2_1725001479336_0.4513865542642934", "host": "s3://npm-registry-packages"}}, "4.21.3": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.21.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.21.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "d50e2082e147e24d87fe34abbf6246525ec3845a", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.21.3.tgz", "fileCount": 3, "integrity": "sha512-nMIdKnfZfzn1Vsk+RuOvl43ONTZXoAPUUxgcU0tXooqg4YrAqzfKzVenqqk2g5efWh46/D28cKFrOzDSW28gTA==", "signatures": [{"sig": "MEQCIAHIdgpDSXUsuaFtCIDT5NE/v6dpbHZAXGtviEPmQi8QAiAVxYC2mANdt71xUzq8qMFqKIdiza9SvX/53tUrY1sU+A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2534376}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "9f5a735524a5c56ba61a8dc6989374917f5aceb1", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.21.3_1726124763673_0.6508968001216078", "host": "s3://npm-registry-packages"}}, "4.22.0": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.22.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.22.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "58daea1f1e65143c44c8f3311f30ff8eefa62bae", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.22.0.tgz", "fileCount": 3, "integrity": "sha512-+3qZ4rer7t/QsC5JwMpcvCVPRcJt1cJrYS/TMJZzXIJbxWFQEVhrIc26IhB+5Z9fT9umfVc+Es2mOZgl+7jdJQ==", "signatures": [{"sig": "MEYCIQC48Xs9wW/08ZZs2vNq9vHQqnRAO5O6m/9Ih/255/TXdAIhAIeRI7QKHPeOER31i6I3FWdwTk36h0dqza97/2O/6grS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2528744}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "5e7a3631a28a863ddb97a64189c3b76eec9983ca", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.22.0_1726721744812_0.9762108439645922", "host": "s3://npm-registry-packages"}}, "4.22.1": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.22.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.22.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "207b65d960ea06c86ef8f2bc554e4ed7c5c2d869", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.22.1.tgz", "fileCount": 3, "integrity": "sha512-yNEeuvH2b+susSgUCfpRelIRjB1CmErHyqA7KsQ/NCjY401rpChVqw5df/H5AUPCKNDqgBMbtrtl9F6z7N9LTg==", "signatures": [{"sig": "MEQCICFPa8cSshUryOlnxboemOy0wLMOVRozf39fD48djiUeAiBto6GKUoFHAPMGS1PaVtbc8oLC6eJJDhVjSeqrhadKkA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2530792}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "76e962daca5b7352bf199c28fa0a10ad4745c5e7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.22.1_1726820526881_0.21727018891902272", "host": "s3://npm-registry-packages"}}, "4.22.2": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.22.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.22.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "c3a8b081595026eab9fccfe581624cb31af0d6f8", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.22.2.tgz", "fileCount": 3, "integrity": "sha512-kTmX5uGs3WYOA+gYDgI6ITkZng9SP71FEMoHNkn+cnmb9Zuyyay8pf0oO5twtTwSjNGy1jlaWooTIr+Dw4tIbw==", "signatures": [{"sig": "MEQCIA+Fmr15jl4gbtQjQ3KnU/0ORdQJvKaisSr0Bs3J2VE+AiAywSXwI8Q+1POnaR24thPX0m0/c5qhgYFVbL9U/U8B0A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2530792}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "b86ffd776cfa906573d36c3f019316d02445d9ef", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.22.2_1726824838494_0.751725478184754", "host": "s3://npm-registry-packages"}}, "4.22.3-0": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.22.3-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.22.3-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "7c79e6261612676037580d284865c7d0aa2f812c", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.22.3-0.tgz", "fileCount": 3, "integrity": "sha512-YKrGGrXLOZHs/oYXpkH0eateF5yOjojUzxBfAvOEKpP8rdlbCf8AOtzg3fZb+8mDTe6JFGJgEBpg04AmcfSzLg==", "signatures": [{"sig": "MEQCICiRR9aiGcFt3gNQejMIvbOYoh+m98+Pi2P2zQ4OAIogAiBdge9k16921LtEPOfKjexOqsC7bdGNGpl5RasZMN5DLA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2530794}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "9e04b4849db9134473b84e4b94aa353ae4fd8754", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.22.3-0_1726843692594_0.024489886473433087", "host": "s3://npm-registry-packages"}}, "4.22.3": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.22.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.22.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "5a955f2fd54a39bafd6086860fc315a01a1c251a", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.22.3.tgz", "fileCount": 3, "integrity": "sha512-liDr0/niP4/7bhiZwrjl2nAARj7gwden3G2DdOJImMbYrjp2C2cYKxEPViOIs4ci6ugcan6TcakxfqJfcb9SJQ==", "signatures": [{"sig": "MEYCIQDnQoLhjstf21gjk0oFlwE61N7AWliQjfPRanwp50CNvwIhANvUel3h8YvXe625mFFkCxyDH/B+GigZ2/Ep/uT3Ls18", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2530792}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "e1cba8e84a0c01dd16580ba7a2536a988dfb4e18", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.22.3_1726895003741_0.18420172268374246", "host": "s3://npm-registry-packages"}}, "4.22.4": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.22.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.22.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "a6fc39a15db618040ec3c2a24c1e26cb5f4d7422", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.22.4.tgz", "fileCount": 3, "integrity": "sha512-SiWG/1TuUdPvYmzmYnmd3IEifzR61Tragkbx9D3+R8mzQqDBz8v+BvZNDlkiTtI9T15KYZhP0ehn3Dld4n9J5g==", "signatures": [{"sig": "MEUCIQCi/AS7Oxu+GeRHxddDalTsKrjiacH0bpnOKlWxsYxRYwIgPOOzUWTUVYRxtMl12HFNxDVy5vpHHJoInGPQ2mNqayg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2530792}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "79c0aba353ca84c0e22c3cfe9eee433ba83f3670", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.22.4_1726899094689_0.4278276289608909", "host": "s3://npm-registry-packages"}}, "4.22.5": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.22.5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.22.5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "0f987b134c6b3123c22842b33ba0c2b6fb78cc3b", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.22.5.tgz", "fileCount": 3, "integrity": "sha512-ElTYOh50InL8kzyUD6XsnPit7jYCKrphmddKAe1/Ytt74apOxDq5YEcbsiKs0fR3vff3jEneMM+3I7jbqaMyBg==", "signatures": [{"sig": "MEYCIQDKUktciWsrWo9l1CAROyVpfwvXTYo4C+IpdHiD9JWqYgIhAI5eI5meDpXysRnP/jTH7smU/q77O9A/rARa3voiu2Dq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2524136}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "bc7780c322e134492f40a76bf64afe561670425c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.22.5_1727437710580_0.08648448571613887", "host": "s3://npm-registry-packages"}}, "4.23.0": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.23.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.23.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "ebd6a789dd59c1a4e94ab055de0c37ab4ae43618", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.23.0.tgz", "fileCount": 3, "integrity": "sha512-0SPJk4cPZQhq9qA1UhIRumSE3+JJIBBjtlGl5PNC///BoaByckNZd53rOYD0glpTkYFBQSt7AkMeLVPfx65+BQ==", "signatures": [{"sig": "MEYCIQCgXjSOwk01IeK6QtW9FFxWVHVY1OS96UzwHHsSJTSA/wIhAIyjuWk33hoCpCg0sT8Zyg9MIW5R77DND5zeNB9XvZ+k", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2524136}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "ed98e0821e6ad064839f0af46ceca061adbe3f14", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.23.0_1727766621358_0.02016871185697089", "host": "s3://npm-registry-packages"}}, "4.24.0": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.24.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.24.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "653f5989a60658e17d7576a3996deb3902e342e2", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.24.0.tgz", "fileCount": 3, "integrity": "sha512-xrNcGDU0OxVcPTH/8n/ShH4UevZxKIO6HJFK0e15XItZP2UcaiLFd5kiX7hJnqCbSztUF8Qot+JWBC/QXRPYWQ==", "signatures": [{"sig": "MEYCIQDNI7hZMMuc6k/QpJ+vMChkqnvOMkvzQPaCUEk8Z/tf2wIhALj/mwAGaTVvWoIAbVJVlStADHYnEGKDCae/UnqOnWtG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2532328}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "d3c000f4fd453e39a354299f0cfaa6831f56d7d8", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.24.0_1727861855089_0.4138580834578234", "host": "s3://npm-registry-packages"}}, "4.24.1": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.24.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.24.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "f50a6188409d62990fa393a57cbccf4d19ccb618", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.24.1.tgz", "fileCount": 3, "integrity": "sha512-IW2axCCdiC+kgj5/50Mt5v8qG0LYaDichBGKXM4Oo2NaWStAs0oQp1dqVzCV1XOXNvNNDRFw0EaT+JMs6BX+WQ==", "signatures": [{"sig": "MEUCIC+e3BRjTV8/lG+J2nxC+cFXdd30ePqqDe9s/HYp2IKyAiEAsJLeYw2tpuuhEjTMoS8Lk4fOmaa43nSmAG5Y27Uel20=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2551272}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "88a54d892dacbb0efdbcade263a32d9df1a77b37", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.24.1_1730011398957_0.2113754100670644", "host": "s3://npm-registry-packages"}}, "4.24.2": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.24.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.24.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "a1e9d275cb16f6d5feb9c20aee7e897b1e193359", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.24.2.tgz", "fileCount": 3, "integrity": "sha512-Zh<PERSON>rakbqA1SCiJRMKSU64AZcYzlZ/9M5LaYil9QWxx9vLnkQ9Vnkve17Qn4SjlipqIIBFKjBES6Zxhnvh0EAEw==", "signatures": [{"sig": "MEYCIQCN+4nkRaXlS5+zKgpvOighzIxR+cyXCyKOtqeRdfM4jgIhAOYDYp+8EqfKKfIlx3Bc+C02xon86vG+rMPaPCAReeIl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2551272}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "32d0e7dae85121ac0850ec28576a10a6302f84a9", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.24.2_1730043625898_0.36943807410435814", "host": "s3://npm-registry-packages"}}, "4.25.0-0": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.25.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.25.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "99873ec45054758a4ff1ecd0c0e05d4db203270d", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.25.0-0.tgz", "fileCount": 3, "integrity": "sha512-J3ru<PERSON>4PHer/Y8kkquzcRuSc1MxZKU0egM6I6jnyEtG6ee98mECFt9zXAtYQQ+beIPqrvdQFej40dETC84nYKQ==", "signatures": [{"sig": "MEUCIQD6UlWtRZU+uwpf+EqN1I3xu0C5GVWYbSpYQcLGQqSOjwIgAtcp1RqA7zNLCkH7mpwoI4wzP+474logABg9HK+G1ZA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2551274}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "b7fcaba12e863db516f39de74c1eacfe5329a5c3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.25.0-0_1730182529263_0.013974232800702646", "host": "s3://npm-registry-packages"}}, "4.24.3": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.24.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.24.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "7ff146e53dc6e388b60329b7ec3335501d2b0f98", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.24.3.tgz", "fileCount": 3, "integrity": "sha512-9SjYp1sPyxJsPWuhOCX6F4jUMXGbVVd5obVpoVEi8ClZqo52ViZewA6eFz85y8ezuOA+uJMP5A5zo6Oz4S5rVQ==", "signatures": [{"sig": "MEUCIBKnDfcZYuytYNlLd7UciEtq47Q6Q+0VvG6DOyDueryuAiEAiq4thNXtc6/awLoxza6rjbmE2066fNb2ZFV4W8xX9Nk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2551272}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "69353a84d70294ecfcd5e1ab8e372e21e94c9f8e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.24.3_1730211266797_0.9147182012501396", "host": "s3://npm-registry-packages"}}, "4.24.4": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.24.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.24.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "a3fc8536d243fe161c796acb93eba43c250f311c", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.24.4.tgz", "fileCount": 3, "integrity": "sha512-V3nCe+eTt/W6UYNr/wGvO1fLpHUrnlirlypZfKCT1fG6hWfqhPgQV/K/mRBXBpxc0eKLIF18pIOFVPh0mqHjlg==", "signatures": [{"sig": "MEUCIQCCRLwbZiKrjuW7BbXr5VH+qqdIzpAsEd7UP8A+V75NhAIgcVB1KlydAGe45+3KturKJEtsPhxyU8aRKrSIxDycntc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2568680}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "cdf34ab5411aac6ac3f6cd21b10d2e58427e88ec", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.24.4_1730710046434_0.28465869770242147", "host": "s3://npm-registry-packages"}}, "4.25.0": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.25.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.25.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "5458eab1929827e4f805cefb90bd09ecf7eeed2b", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.25.0.tgz", "fileCount": 3, "integrity": "sha512-dRLjLsO3dNOfSN6tjyVlG+Msm4IiZnGkuZ7G5NmpzwF9oOc582FZG05+UdfTbz5Jd4buK/wMb6UeHFhG18+OEg==", "signatures": [{"sig": "MEUCIQCnMGyjcTGe8IuF+G34E9kwf9ZPfdoIyp3lOD9HW7QT8QIgbKExxZ+15rD/zlranpbTfjsbEgvir3TYd0UFBIYOuPQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2566120}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "42e587e0e37bc0661aa39fe7ad6f1d7fd33f825c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.25.0_1731141460677_0.12115350195585384", "host": "s3://npm-registry-packages"}}, "4.26.0": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.26.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.26.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "2eeabbc99342dafe04613a76c441be4ebcca49c3", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.26.0.tgz", "fileCount": 3, "integrity": "sha512-D4CxkazFKBfN1akAIY6ieyOqzoOoBV1OICxgUblWxff/pSjCA2khXlASUx7mK6W1oP4McqhgcCsu6QaLj3WMWg==", "signatures": [{"sig": "MEUCIBUssK7CqF3gob6jMBgZdTV5bnjImBJMUrafqPIsP6g+AiEAj66nd1KU+fzkXu2INxSxepuER7poVDOMtDB/ZUnTzaA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2566120}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "ae1d14b7855ff6568a6697d37271a5eb4d8e2d3e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.26.0_1731480317110_0.5630208077983494", "host": "s3://npm-registry-packages"}}, "4.27.0-0": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.27.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.27.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "c1418415e47f663135c68f1241cd8e1b7b752e31", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.27.0-0.tgz", "fileCount": 3, "integrity": "sha512-d55yX3cB+SRQVOvnVGAeyM6CGK0PIztl3C6NJpdy/CS8oLyKF5Ks/UFPKK8kG+L3laujaxkNW1MmIMiFq2NCEA==", "signatures": [{"sig": "MEUCIQD2mQRlndyu1C8gMrujlgXWI1rCzcGsSU4qfGPGqgMiDgIgdWa28g17ZgN49h1g0MgNsfjQKiLyvWtQySETtFTGzdM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2566122}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "5e6074f07843bcbcf26b916c557fdfd81d2adece", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.27.0-0_1731481410812_0.6787194909185761", "host": "s3://npm-registry-packages"}}, "4.27.0-1": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.27.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.27.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "025cf86e56e35430983bf301b3478afb821571b8", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.27.0-1.tgz", "fileCount": 3, "integrity": "sha512-DEFhJUuMYQBju2BNWY3e66V/PkcHfbsvODVE53Dn9L0XDDrRNA6W/F/Lp3hc7wdTBBhgvrBLEU4yKbvu4PGIpg==", "signatures": [{"sig": "MEYCIQCEKBgoZL3sNtS5QNVOvi1Z1by1k7KHu34eLkWqbBFB3QIhAK4LX0O/12P6yx2iK3IoGiwQHvOBJNnG5maAuW5MonMw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2566122}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "81f5021d7d7e2a488639dc036f2334995b3761fc", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.27.0-1_1731566008758_0.7749507574165047", "host": "s3://npm-registry-packages"}}, "4.27.0": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.27.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.27.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "2c57dd7ad63dcad45a9d0e5e9755f9182afbc17c", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.27.0.tgz", "fileCount": 3, "integrity": "sha512-LCqk4Xi3e4GzLqaq+QDM7gP5DtJ/RgWMzV3U2brwp/vEz9RTA5YBgIDP69xYfrTXexes6xPsOIquy79+kLifiA==", "signatures": [{"sig": "MEYCIQDKF/g2LcysiLsC5OpsQd8qNxCQL8lQWMc42w3JeZxicwIhAIYzanCI2Hc0tSkeB1KOmbzlv68hbeAdOisKgebT6QRy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2584040}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "c035068dfebeb959a35a8acf3ff008a249e2af73", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.27.0_1731667253306_0.11823432943052459", "host": "s3://npm-registry-packages"}}, "4.27.1-0": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.27.1-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.27.1-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "cd4dc08b95258c441d79cc4ce9084525a92023c8", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.27.1-0.tgz", "fileCount": 3, "integrity": "sha512-2Rn8kuNMRWwi5CLo0o75B7DU/bPzQxWg5Gqz+EECHBUNZJIh24CeuTo33uu9DRhTQZJJRkUib92m/Lr9DgRing==", "signatures": [{"sig": "MEQCIGk9eIZg5HkjimgI3JO9310OP2rWeCLuhTPf2gvR8Y+WAiAg4eGToS6QIXwpAJupqi+9Us+wmqlo75nLBtaK6SmHDQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2584042}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "a80f6a94d720224a44331d5a50745e9887619703", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.27.1-0_1731677306458_0.19451080879514793", "host": "s3://npm-registry-packages"}}, "4.27.1-1": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.27.1-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.27.1-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "22b341e5f36c3ec5fdb9238b9b91402aaaed7f08", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.27.1-1.tgz", "fileCount": 3, "integrity": "sha512-IU+S28I1e1msdW1LMM+ipdehHZpiHzhmo64StCBxLfasYmTdbyjO0LXczdSbmnZjHNt9A6U1+2x3lDVUBZy4Hg==", "signatures": [{"sig": "MEMCH26CtRxi8r1/aGhU05Tn24BUeyrKi6Wy+n9UBsUtdTQCIHij9C8dXb/kW3X23sS700nB6605iERUc/or+B/0u4f+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2584042}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "892ce0206dbf4fbf656b2f0563ef803c5e5a0016", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.27.1-1_1731685101777_0.6944999072237756", "host": "s3://npm-registry-packages"}}, "4.27.1": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.27.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.27.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "7d84dbc9fe5c0c5a6a1143bfd6fe2d96de5b184e", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.27.1.tgz", "fileCount": 3, "integrity": "sha512-rTQbl/dhpKbOb83i8AHAQm5FTeDmoKQqiNH2F3vlp5fySgT4t36wehWoCcrax7LRCmXc1LWrj66LEnrcXK/SRA==", "signatures": [{"sig": "MEUCIDxSwxM2VeprKwyDJ/Gfcrp7Q+KheEjdYOFRmfKEOEeoAiEAxraQvgGJs0nhsZSykFrymv1M00WZQPwJGFxA+LcDR6g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2584040}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "aaf38b725dd142b1da4190a91de8b04c006fead5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.27.1_1731686879655_0.23867096287233824", "host": "s3://npm-registry-packages"}}, "4.27.2": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.27.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.27.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "9294246ee7ab3021026fdb434f16d9f7d02a9c91", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.27.2.tgz", "fileCount": 3, "integrity": "sha512-RsnE6LQkUHlkC10RKngtHNLxb7scFykEbEwOFDjr3CeCMG+Rr+cKqlkKc2/wJ1u4u990urRHCbjz31x84PBrSQ==", "signatures": [{"sig": "MEYCIQD6EGWSsFyknaRLtqgW0YyLfjpn6Rdgsn5IpvzRQxpMDQIhAOCug1bI/OaWUAL+VEYF/oYiUmYkNbEin1UeDH/nAwGv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2584040}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "a503a4dd9982bf20fd38aeb171882a27828906ae", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.27.2_1731691222264_0.21606616068973206", "host": "s3://npm-registry-packages"}}, "4.27.3": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.27.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.27.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "54fcf9a13a98d3f0e4be6a4b6e28b9dca676502f", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.27.3.tgz", "fileCount": 3, "integrity": "sha512-ecE36ZBMLINqiTtSNQ1vzWc5pXLQHlf/oqGp/bSbi7iedcjcNb6QbCBNG73Euyy2C+l/fn8qKWEwxr+0SSfs3w==", "signatures": [{"sig": "MEUCIQDfj6B+o9UxtHQBx20GwkG/zFH4LGZdCt6MRh96krA66QIgJXaGB/ksKEFuLRVm7bym92as2dLD/9jGLl9xsHYlS7I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2584040}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "7c0b1f8810013b5a351a976df30a6a5da4fa164b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.27.3_1731947994274_0.10286958911105115", "host": "s3://npm-registry-packages"}}, "4.27.4": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.27.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.27.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "3457a3f44a84f51d8097c3606429e01f0d2d0ec2", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.27.4.tgz", "fileCount": 3, "integrity": "sha512-KtwEJOaHAVJlxV92rNYiG9JQwQAdhBlrjNRp7P9L8Cb4Rer3in+0A+IPhJC9y68WAi9H0sX4AiG2NTsVlmqJeQ==", "signatures": [{"sig": "MEYCIQDDhlhICm+gZfc/mDm7jeZtCvp14uFz4zG/W6VoU/w4AAIhAPL0rOQ2fOGMVclgy8KiXnC5Spgte95NAbj5Y12YpzF9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2571752}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "e805b546405a4e6cfccd3fe73e9f4df770023824", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.27.4_1732345237307_0.214211063666224", "host": "s3://npm-registry-packages"}}, "4.28.0": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.28.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.28.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "b64470f9ac79abb386829c56750b9a4711be3332", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.28.0.tgz", "fileCount": 3, "integrity": "sha512-kN/Vpip8emMLn/eOza+4JwqDZBL6MPNpkdaEsgUtW1NYN3DZvZqSQrbKzJcTL6hd8YNmFTn7XGWMwccOcJBL0A==", "signatures": [{"sig": "MEQCIDTL3yyxsfjStbmPgx1KjVTwmxzCY7erP2Zr6K9cAgp3AiBgWjZISaJMSJfRnJe3z3urR59JT0bMm2M6sa5lbobrhg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2569192}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "0595e433edec3608bfc0331d8f02912374e7f7f7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.28.0_1732972565766_0.984844316760646", "host": "s3://npm-registry-packages"}}, "4.28.1": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.28.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.28.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "06d1d60d5b9f718e8a6c4a43f82e3f9e3254587f", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.28.1.tgz", "fileCount": 3, "integrity": "sha512-ZkyTJ/9vkgrE/Rk9vhMXhf8l9D+eAhbAVbsGsXKy2ohmJaWg0LPQLnIxRdRp/bKyr8tXuPlXhIoGlEB5XpJnGA==", "signatures": [{"sig": "MEYCIQCEPy5153koqzwktbY0Unele5XD67uqaG7LhI8KJX0LCQIhAMPYM2hCXwGp+oxA7Byp0i6soI+bb2d+FpPoEYmK+Yw2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2570216}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "e60fb1c5d4e54ed5257495215eeda1bb43cf54ba", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.28.1_1733485514009_0.0403288962659174", "host": "s3://npm-registry-packages"}}, "4.29.0-0": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.29.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.29.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "e72ca67c7a1590dc8dcb3c2ce169024f4d24931f", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.29.0-0.tgz", "fileCount": 3, "integrity": "sha512-Y9CzvoXl4uCOcIhb6/oE26tkscTLjt+EuM/kHnGoL2tOpf4MV6WTs//HVBY6qeme3RHE5v6ahtWuNsHceZmzUQ==", "signatures": [{"sig": "MEUCIQC2fKP77zvJGvyorvaQ7IS+q9NRH2iWq4X2QCo+ThxIhwIgMg7Q6KZP8DeF5/929ondyfO58Y1EGS6x4bV21A9vpFw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2560490}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "879d03d68890f365f880e30c69b58377b8743407", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.29.0-0_1734331211978_0.2196157782248942", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.0-1": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.29.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.29.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "fd2590890707096533df3b54545f422f11cda8f6", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.29.0-1.tgz", "fileCount": 3, "integrity": "sha512-cpp8pmTeQugQjfJgILIkhlDHR91RpiA2B3i1A1A3bP7QQa2Bmvl3Y4g+L7BSJ19Z0OPMS1JHHwNUNY2ai6N8mA==", "signatures": [{"sig": "MEUCIFuF/KgVYwU5fmock2DKc+oFUBr949PuGBpdzLReCQ7pAiEAurvuDPQVAUf7NtGzSdYtHR92nCZNxnAoOLMy08yZpIQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2560490}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "fa5064084196636acd98263f95ffea59f8362e32", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.29.0-1_1734590268190_0.5315564659212966", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.0-2": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.29.0-2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.29.0-2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "29cb332a563d4506bdaf8c7976e8ed16c7279c18", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.29.0-2.tgz", "fileCount": 3, "integrity": "sha512-uaH2AArbAWyrQSs2P9ySJp/VfDr8fzajCuCFU4kEmGr1rOvJ0hbaIsgWhwcySmTy/B4c1xXtJjjWKzQG5/7TbQ==", "signatures": [{"sig": "MEUCIETMAMHwWSr3yaaUUhiT1Gv4azeUwR0HVb+yX7oUhYT0AiEAl+4e/YumbajQ+iGdCelj9T898w9aTNhK0AHdWQLc2MA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2560490}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "bbb7e7b1d4e208a923b0f18ceb8dd886838e1a01", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.29.0-2_1734677781448_0.5909413409537658", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.0": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.29.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.29.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "44f326ccac35b71a34060dc8df81114bce4c22bb", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.29.0.tgz", "fileCount": 3, "integrity": "sha512-4TQbLoAQVu9uE+cvh47JnjRZylXVdRCoOkRSVF2Rr2T0U1YwphGRjR0sHyRPEt95y3ETT4YFTTzQPq1O4bcjmw==", "signatures": [{"sig": "MEUCIHqEowOT7FKYoLiJ/0Wznzt6b2Nw2q43Bcj+R6CqbLOmAiEAra3M2t/GTSKsQkbXypTA8h9qtgX0pAtB/aOSV8vvurI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2577384}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "dadd4882c4984d7875af799ad56e506784d50e1c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.29.0_1734719863300_0.5311742736084513", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.1": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.29.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.29.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "353d14d6eee943004d129796e4feddd3aa260921", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.29.1.tgz", "fileCount": 3, "integrity": "sha512-rYRe5S0FcjlOBZQHgbTKNrqxCBUmgDJem/VQTCcTnA2KCabYSWQDrytOzX7avb79cAAweNmMUb/Zw18RNd4mng==", "signatures": [{"sig": "MEQCHw9N0zyYlT4DFNAr92FbroGyfI75DsTAiOUb01v1TU8CIQCxWhYzvSvtRC2vxJIsYercQdoIbmdVj6nRgYOFasul7w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2577384}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "5d3777803404c67ce14c62b8b05d6e26e46856f5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.29.1_1734765380878_0.3844302014530945", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.0-0": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.30.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.30.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "3f9a9bf6d437291cb8b3edc3965e836243d8d0d2", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.30.0-0.tgz", "fileCount": 3, "integrity": "sha512-CUdKgUVUWHJwIHTYcASNXjyRCeWKTgeNdFnDDKnO/mKyl2Wyo1RmMULh3akslcz7IbrtpOPm0JQt6e02ZOuKeg==", "signatures": [{"sig": "MEYCIQD2Mn+81HH3Nr8IUYAEXz3QTrkD+APQhE07YJbvXFUGxAIhAIUByJ00eXryptE/UhjqXFuVNbSLRERgsqmlUnDno9tO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2577386}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "2339f1d8384a8999645823f83f9042a9fc7b3bf7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.30.0-0_1734765452971_0.5480198694528311", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.0-1": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.30.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.30.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "cbd6984b60a07e919977d01650d26b88414cd43d", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.30.0-1.tgz", "fileCount": 3, "integrity": "sha512-DMAz5fzZi8jJvcf5eMQk1gnLNId3zZK1OJ8gApigzNPh+64PBPJLrCBYEt+E4rd5tsIhvxlxS/Uh+C83IyGxag==", "signatures": [{"sig": "MEUCIQDgxruN51tRE0YYmIj/zmzozIhx46iWs/SE9GuAFmprHAIgMWwSg8cBB+uCkVz8BKKANbpaBWyR0VY1MUFXQv90EaU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2561002}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "41ab39a6e4a5181e9be21e816dd6f11c57e1c52a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.30.0-1_1735541555683_0.16175383658372344", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.2": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.29.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.29.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "0b77e11129b04bb5b9bfff4b011d084a975190e0", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.29.2.tgz", "fileCount": 3, "integrity": "sha512-pW8kioj9H5f/UujdoX2atFlXNQ9aCfAxFRaa+mhczwcsusm6gGrSo4z0SLvqLF5LwFqFTjiLCCzGkNK/LE0utQ==", "signatures": [{"sig": "MEUCIBai6okmDFSDTbyRLOyeyZgRn/9PWZHgUqC3HCsoPVdWAiEA4Qyi/9945Unk/G+2AP9eOHSIdHDGvwDpzahbhGC6nCY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2561000}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "f5c349e5bb4cb40b0cc1a1b2a3fb5de415946406", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.29.2_1736078882225_0.7115945276939362", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.0": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.30.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.30.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "0321de1a0540dd402e8e523d90cbd9d16f1b9e96", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.30.0.tgz", "fileCount": 3, "integrity": "sha512-duzweyup5WELhcXx5H1jokpr13i3BV9b48FMiikYAwk/MT1LrMYYk2TzenBd0jj4ivQIt58JWSxc19y4SvLP4g==", "signatures": [{"sig": "MEQCIGEcxzgAgiuswijt5LDwcI31bB1vRQkrW136wUxToHOhAiAo2CgWTd8yxLwDMQwSO2tKhVQlJzvQwZufafR8Oket5w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2561000}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "958d5ebabd49297e9a4b78ad34ac0a0132305dea", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.30.0_1736145417045_0.28404379809500013", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.1": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.30.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.30.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "661c8b3e4cd60f51deaa39d153aac4566e748e5e", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.30.1.tgz", "fileCount": 3, "integrity": "sha512-pxHAU+Zv39hLUTdQQHUVHf4P+0C47y/ZloorHpzs2SXMRqeAWmGghzAhfOlzFHHwjvgokdFAhC4V+6kC1lRRfw==", "signatures": [{"sig": "MEYCIQCanzsEf5ayd7IRx+9KCFm1n0M8bsSGeRvKLOJ/NwcVQgIhAM6G9hqaBEdiwL7ItvA9nvk8BSD0yk9d/BEu3ocK53HZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2561000}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "94917087deb9103fbf605c68670ceb3e71a67bf7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.30.1_1736246170661_0.31139879979464236", "host": "s3://npm-registry-packages-npm-production"}}, "4.31.0-0": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.31.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.31.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "f2f7225011fe6fcd0c0830586db2aec317e7ba8a", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.31.0-0.tgz", "fileCount": 3, "integrity": "sha512-cWFzIDwRbKsU9QjnmIKmsuC/ntr216H30gyV9Jamarbksk+CkmvNWbv/HctTHl+HzDcxXqQ+Nz/zXaJ2dvyHcQ==", "signatures": [{"sig": "MEYCIQCmmYAqOf46vNjJO8KnWLEDs+Sa8HyWanw9doNHpG/piwIhAJ2rSFGKTgOa+Dn21+Rwy5ip7FxsgKR1xhF+3R8QI2zC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2571242}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "8c80d5f657f0777d14bd75d446fee3fa4b7639fc", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.31.0-0_1736834280192_0.2942477606809826", "host": "s3://npm-registry-packages-npm-production"}}, "4.31.0": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.31.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.31.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "d47effada68bcbfdccd30c4a788d42e4542ff4d3", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.31.0.tgz", "fileCount": 3, "integrity": "sha512-U1xZZXYkvdf5MIWmftU8wrM5PPXzyaY1nGCI4KI4BFfoZxHamsIe+BtnPLIvvPykvQWlVbqUXdLa4aJUuilwLQ==", "signatures": [{"sig": "MEYCIQChhRSo0bjb/MzT3vEQgIJDIJNSeCR+J7ZH6UL3zFJP3AIhANnyRCH8x+ndUcGR/57QEFDoaLedX95CEf+cW3WuU+2/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2564072}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "15c264d59e0768b7d283a7bb8ded0519d1b5199e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.31.0_1737291425385_0.7205382594665184", "host": "s3://npm-registry-packages-npm-production"}}, "4.32.0": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.32.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.32.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "8222ccfecffd63a6b0ddbe417d8d959e4f2b11b3", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.32.0.tgz", "fileCount": 3, "integrity": "sha512-/TG7WfrCAjeRNDvI4+0AAMoHxea/USWhAzf9PVDFHbcqrQ7hMMKp4jZIy4VEjk72AAfN5k4TiSMRXRKf/0akSw==", "signatures": [{"sig": "MEQCIH+E3Vyx71aHjOzGr8WyYS/ebdmI7IWvTbyjciFxsky/AiAMG2tEKN0J4bC/oHX1bSBMD94V41//rl3/jcO786Xu8A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2583528}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "2538304efdc05ecb7c52e6376d5777565139f075", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.32.0_1737707272587_0.709206899116118", "host": "s3://npm-registry-packages-npm-production"}}, "4.33.0-0": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.33.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.33.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "0596c589b1f923a1a20d74920f4a00bc170c90f7", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.33.0-0.tgz", "fileCount": 3, "integrity": "sha512-GZRryL7jOP0arY6o1DEGPR9Frm73cBYmdYU0WVVPlOC5+vderqFTbDgZtpACUcAlpNGEJmTZStwfVuP0Z6qQJA==", "signatures": [{"sig": "MEUCIQC5SDLxl6ilQ8AGTgc7MRkxn56VA9l5lb3OEOqwZairewIgNLFAlGuEmY/bnaEzQvbas9ckvpcyjK/qd/vf30GVFCE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2583530}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "f854e1988542d09f9691923eddd80888e92240d3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.33.0-0_1738053026531_0.825072482719591", "host": "s3://npm-registry-packages-npm-production"}}, "4.32.1": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.32.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.32.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "a1a394c705a0d2a974a124c4b471fc1cf851a56f", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.32.1.tgz", "fileCount": 3, "integrity": "sha512-Am9H+TGLomPGkBnaPWie4F3x+yQ2rr4Bk2jpwy+iV+Gel9jLAu/KqT8k3X4jxFPW6Zf8OMnehyutsd+eHoq1WQ==", "signatures": [{"sig": "MEYCIQDHBDN43DtlreoD6QT3LwiHh+L0pb1MCkFyCYy3mgNAeQIhALlFVfQuga8iStgXWUXzQSVOFFKKDBNYy0Hw/X5TEKHv", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2583528}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "abcf4febe11f3d313fae41ddca35fc60670b9ff8", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.32.1_1738053214521_0.04863006447806817", "host": "s3://npm-registry-packages-npm-production"}}, "4.33.0": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.33.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.33.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "55aeaa8c05a07785ab098deb0f6a50e9faf7a811", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.33.0.tgz", "fileCount": 3, "integrity": "sha512-9IhxrTkZzFlCx9+odRlDYPBCK7E/ThQQ1JulZYAuwTkoKalXT5Roy46+B2aOfstzIfygwsjmxfJE2Mvcgb1evQ==", "signatures": [{"sig": "MEYCIQDvH0A9NIUHGPvbqXreqwWFzhp2mHIyHi9JJ817Kh+5/QIhAKV27fFmuNW/tz3nIUfBAFFVxYvEIO5kzdOx/wuxp5O3", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2502632}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "494483e8df7b5d04796b30e37f54d7e96fa91a97", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.33.0_1738393938832_0.5871166807338288", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.0": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.34.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.34.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "283b3436a4c8a39e0f11d1d6935560ef37f1b5f8", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.34.0.tgz", "fileCount": 3, "integrity": "sha512-CV2aqhDDOsABKHKhNcs1SZFryffQf8vK2XrxP6lxC99ELZAdvsDgPklIBfd65R8R+qvOm1SmLaZ/Fdq961+m7A==", "signatures": [{"sig": "MEYCIQCDSeY1GaUzlDsPdfHo2xAH1yeZL+2NkfdJFRoSv0v3HQIhAPQgYipa2D7PxkBj9SCJKeqjaO7xKvt87+4EysDXYgsy", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2502632}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "979d62888dbe75f92e50fdd64246c737c52f5f1f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.34.0_1738399240721_0.38425357384710224", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.1": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.34.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.34.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "04e36410a55fa36278a627ed769a60576c882d1d", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.34.1.tgz", "fileCount": 3, "integrity": "sha512-i7aT5HdiZIcd7quhzvwQ2oAuX7zPYrYfkrd1QFfs28Po/i0q6kas/oRrzGlDhAEyug+1UfUtkWdmoVlLJj5x9Q==", "signatures": [{"sig": "MEYCIQDR3QYzCDPnPveWPzaJ1VEt0CTQjfqI0mPEEZJbVOgjKwIhAMLbUHMGYd+OJvqobk8k6sm1hfq+iEofpEpNdbw9zmCV", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2502632}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "0f20524ad9ecd166a900d43af93f05a3405d2a45", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.34.1_1738565911130_0.1922533600655547", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.2": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.34.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.34.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "cbba6c7c6cefdd632137bee4d47afb8ec9285bfe", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.34.2.tgz", "fileCount": 3, "integrity": "sha512-wt8OhpQUi6JuPFkm1wbVi1BByeag87LDFzeKSXzIdGcX4bMLqORTtKxLoCbV57BHYNSUSOKlSL4BYYUghainYA==", "signatures": [{"sig": "MEYCIQDkDceYLM4Wr+SCMkmrpgehbbqDw8ZkshryZDNvoVnr3wIhAJOZ0dc8JHIucAaIoojvDA0EcmHCvVdKsyBxaX8Kwf74", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2502632}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "615efa045779fae70c4fd5fe64fdb08a039c0442", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.34.2_1738656620559_0.7842684363327272", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.3": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.34.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.34.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "3fd1b93867442ecd3d2329b902b111853600cc6c", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.34.3.tgz", "fileCount": 3, "integrity": "sha512-K/V97GMbNa+Da9mGcZqmSl+DlJmWfHXTuI9V8oB2evGsQUtszCl67+OxWjBKpeOnYwox9Jpmt/J6VhpeRCYqow==", "signatures": [{"sig": "MEUCIGjCApzCU4crxdtMfwpviftr8VfoJb3C22evwhoRmrChAiEA9bqU9KOdq5cq7ETlWQY+1OeZk74ssEjyR+avFiQ6ygw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2502632}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "ac8b06a2b5406f694c38c416912cc2b18ba13355", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.34.3_1738747343710_0.4300332191330609", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.4": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.34.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.34.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "6b31c29ae32721f422d03bfaf7a865a464d9c85b", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.34.4.tgz", "fileCount": 3, "integrity": "sha512-5g7E2PHNK2uvoD5bASBD9aelm44nf1w4I5FEI7MPHLWcCSrR8JragXZWgKPXk5i2FU3JFfa6CGZLw2RrGBHs2Q==", "signatures": [{"sig": "MEQCIHqzHLI9vCvpmhblDyU+2KXxe//tUdllvzVxji6VLgggAiBQTGAiMIUc+R0/V02HMJJnfH+YUT48yf+p5raMhcJNEQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2502632}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "19312a762c3cda56a0f6dc80a0887a4499db2257", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.34.4_1738791090148_0.6191834315178328", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.5": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.34.5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.34.5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "7f1263e0ea24ee098952a89c42eb5a1a5345a32f", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.34.5.tgz", "fileCount": 3, "integrity": "sha512-3vncGhOJiAUR85fnAXJyvSp2GaDWYByIQmW68ZAr+e8kIxgvJ1VaZbfHD5BO5X6hwRQdY6Um/XfA3l5c2lV+OQ==", "signatures": [{"sig": "MEUCIQCQ2p0pxmS5hcKbRe4bDW5gs/M/D9KO4fQc6fxOc0FFQgIgc67mkGWZQ2uAG0BFsnD0D78rPCIZ2Tw3C2Uo0KDLqD8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2517480}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "3426b026e95319048dd5b703f2a0330c1c924e52", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.34.5_1738918400949_0.9361923102610161", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.6": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.34.6", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.34.6", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "16ca6bdadc9e054818b9c51f8dac82f6b8afab81", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.34.6.tgz", "fileCount": 3, "integrity": "sha512-oLHxuyywc6efdKVTxvc0135zPrRdtYVjtVD5GUm55I3ODxhU/PwkQFD97z16Xzxa1Fz0AEe4W/2hzRtd+IfpOA==", "signatures": [{"sig": "MEUCIHa2DzOOCpTylVC3fGevqF37DGDS1H9i8xJ07HQSfzL8AiEAyRsliQkTPlgauPc3h8roNHFXzArMRKpC+b5xH08ALwg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2500584}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "4b8745922d37d8325197d5a6613ffbf231163c7d", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.34.6_1738945945303_0.5504768190080036", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.7": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.34.7", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.34.7", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "90b39b977b14961a769be6ea61238e7fc668dd4d", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.34.7.tgz", "fileCount": 3, "integrity": "sha512-aeawEKYswsFu1LhDM9RIgToobquzdtSc4jSVqHV8uApz4FVvhFl/mKh92wc8WpFc6aYCothV/03UjY6y7yLgbg==", "signatures": [{"sig": "MEUCIQChQxEOyvuUihNbnr3sq3XPmcjAP33i7Ks72nSzu7L9WgIgCB+KjxJhz8YsG2Co3F1NXvVb7OLyrBgh7usFKBgnRBs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2529256}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "f9c52f80074e33f5b0799e8ca215e3bfac7d2755", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.34.7_1739526857096_0.9374683669480497", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.8": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.34.8", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.34.8", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "95cdbdff48fe6c948abcf6a1d500b2bd5ce33f62", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.34.8.tgz", "fileCount": 3, "integrity": "sha512-r3NRQrXkHr4uWy5TOjTpTYojR9XmF0j/RYgKCef+Ag46FWUTltm5ziticv8LdNsDMehjJ543x/+TJAek/xBA2w==", "signatures": [{"sig": "MEQCIB0Nfo9uIBKn7honEhX3HVCJ9Gr8QCC55REDNi7aB6gXAiAsbGVrwIdJEWS6bsgkyGespaeGp8nHzarcr/vihenKCQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2529256}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "8f667b7c15b176728449a4917cb29fe5ee3a1c0c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.34.8_1739773602821_0.7833696866875972", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.9": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.34.9", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.34.9", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "ecb9711ba2b6d2bf6ee51265abe057ab90913deb", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.34.9.tgz", "fileCount": 3, "integrity": "sha512-KB48mPtaoHy1AwDNkAJfHXvHp24H0ryZog28spEs0V48l3H1fr4i37tiyHsgKZJnCmvxsbATdZGBpbmxTE3a9w==", "signatures": [{"sig": "MEUCIBZz4luYt5iYDF/lGD42qJ91DGjSY1JmChTkzmjz1z+JAiEAsu4wW/VfCXWVeEYkKXT0edKddQ/tmtpJS6bieQASpOg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2646504}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "0ab9b9772e24dfe9ef08bfce3132e99a15b793f6", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.34.9_1740814375592_0.05425348735250002", "host": "s3://npm-registry-packages-npm-production"}}, "4.35.0": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.35.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.35.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "5b5a40e44a743ddc0e06b8e1b3982f856dc9ce0a", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.35.0.tgz", "fileCount": 3, "integrity": "sha512-2/lsgejMrtwQe44glq7AFFHLfJBPafpsTa6JvP2NGef/ifOa4KBoglVf7AKN7EV9o32evBPRqfg96fEHzWo5kw==", "signatures": [{"sig": "MEUCIE5O6D34BJpYPGDoHRfPUZe3JFNgpp3wHoJk2Na6mcUjAiEA5dQYO0LOYeQ1//itMOiLxCxanTZcV3XL6uJ/uv0G0Go=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2685416}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "70ef1cce7c740030cc2935b563d13950cc1511f5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.35.0_1741415105335_0.2601330131090045", "host": "s3://npm-registry-packages-npm-production"}}, "4.36.0": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.36.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.36.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "8e12739b9c43de8f0690b280c676af3de571cee0", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.36.0.tgz", "fileCount": 3, "integrity": "sha512-t+RY0JuRamIocMuQcfwYSOkmdX9dtkr1PbhKW42AMvaDQa+jOdpUYysroTF/nuPpAaQMWp7ye+ndlmmthieJrQ==", "signatures": [{"sig": "MEUCIHy5FXCVUys0GIb+Xd/RWAEho1BQW0uzUQzffTo92aWbAiEAyfeMjmxaP5OjlgA+XxNQGMS0/sWBDrXBP0GZg9fnPD8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2691560}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "ab7bfa8fe9c25e41cc62058fa2dcde6b321fd51d", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.36.0_1742200562792_0.040755476765853116", "host": "s3://npm-registry-packages-npm-production"}}, "4.37.0": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.37.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.37.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "891d90e3b5517f9d290bb416afdfe2ebfb12139e", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.37.0.tgz", "fileCount": 3, "integrity": "sha512-e3/1SFm1OjefWICB2Ucstg2dxYDkDTZGDYgwufcbsxTHyqQps1UQf33dFEChBNmeSsTOyrjw2JJq0zbG5GF6RA==", "signatures": [{"sig": "MEYCIQD0zAupLOfPz2JuGOhwnPijsX+Q6T5PDkD2fa3lNnIzcgIhAMr5ouMTJJPhaRZ/yqGaHJ9hHwneibjMjX9DK8paLQFt", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2693096}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "8b1c634d945dda9294cf579de68c4b223c618e7f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.37.0_1742741843889_0.5011595363973325", "host": "s3://npm-registry-packages-npm-production"}}, "4.38.0": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.38.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.38.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "6c02847c60fcc7a6d74e00a60f350d079558d84d", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.38.0.tgz", "fileCount": 3, "integrity": "sha512-mqu4PzTrlpNHHbu5qleGvXJoGgHpChBlrBx/mEhTPpnAL1ZAYFlvHD7rLK839LLKQzqEQMFJfGrrOHItN4ZQqA==", "signatures": [{"sig": "MEQCIGEvK+UnBJ5tp2wo8+hSucp7hVzsnL+eRrxfhLMw8IHjAiADYy7HV7yeyC+57ae4eMYa12/0JAReXT9jhga2vpbsew==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2696680}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "22b64bcc511dfc40ce463e3f662a928915908713", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.38.0_1743229766076_0.19119147361359223", "host": "s3://npm-registry-packages-npm-production"}}, "4.39.0": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.39.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.39.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "a44972d5cdd484dfd9cf3705a884bf0c2b7785a7", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.39.0.tgz", "fileCount": 3, "integrity": "sha512-6w9uMuza+LbLCVoNKL5FSLE7yvYkq9laSd09bwS0tMjkwXrmib/4KmoJcrKhLWHvw19mwU+33ndC69T7weNNjQ==", "signatures": [{"sig": "MEYCIQCjCVZ0DL05ZqXD4YKgcInxdwVSc9DTY0DGAVxHIeRyJgIhALEKFBuCnSdF1FOTP92g4yjmCAy8LAfuI4JKm3+FEME3", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2696680}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "5c001245779063abac3899aa9d25294ab003581b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.39.0_1743569390467_0.20246119738947677", "host": "s3://npm-registry-packages-npm-production"}}, "4.40.0": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.40.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.40.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "846e02c17044bd922f6f483a3b4d36aac6e2b921", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.40.0.tgz", "fileCount": 3, "integrity": "sha512-+m03kvI2f5syIqHXCZLPVYplP8pQch9JHyXKZ3AGMKlg8dCyr2PKHjwRLiW53LTrN/Nc3EqHOKxUxzoSPdKddA==", "signatures": [{"sig": "MEQCIGNg8evIT9dd+aJtQNALxypVqGARszq3xsNQR4k036b5AiBrn8BF4+4HJD0tXmPHb6WJUqykllnCh/Q3hN5xpaq9PA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2678760}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "1f2d579ccd4b39f223fed14ac7d031a6c848cd80", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.40.0_1744447193695_0.8030185971906987", "host": "s3://npm-registry-packages-npm-production"}}, "4.40.1": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.40.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.40.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "7749e1b65cb64fe6d41ad1ad9e970a0ccc8ac350", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.40.1.tgz", "fileCount": 3, "integrity": "sha512-DfcogW8N7Zg7llVEfpqWMZcaErKfsj9VvmfSyRjCyo4BI3wPEfrzTtJkZG6gKP/Z92wFm6rz2aDO7/JfiR/whA==", "signatures": [{"sig": "MEQCIB99yrEfDXqTD+eOzDp+IqM6mTKWX9C+K2VAcWBWmflvAiA/uv6ru+dFLIsllXCBEAP/ug0XKkFp3bNar8wPYAGtTw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2679784}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "1e6c40f49c428b7657fe3b9a2026f705acd39da1", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.40.1_1745814941889_0.16968632853022347", "host": "s3://npm-registry-packages-npm-production"}}, "4.40.2": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.40.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.40.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "df0991464a52a35506103fe18d29913bf8798a0c", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.40.2.tgz", "fileCount": 3, "integrity": "sha512-dt1llVSGEsGKvzeIO76HToiYPNPYPkmjhMHhP00T9S4rDern8P2ZWvWAQUEJ+R1UdMWJ/42i/QqJ2WV765GZcA==", "signatures": [{"sig": "MEUCIGl78h57f3coUBxA5wcWqb87XElyAGlVmwTwOtdWFrEqAiEAjCP1zCeUx92vyI0LBulF1kWvUbij0jyX/hXyZx22QDI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2672104}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "02da7efedcf373f0f819b78e3acbe50de05d9a5b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.40.2_1746516432544_0.38809086806779836", "host": "s3://npm-registry-packages-npm-production"}}, "4.41.0": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.41.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.41.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "e27ef5c40bbec49fac3d4e4b1618fbe4597b40e5", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.41.0.tgz", "fileCount": 3, "integrity": "sha512-tmazCrAsKzdkXssEc65zIE1oC6xPHwfy9d5Ta25SRCDOZS+I6RypVVShWALNuU9bxIfGA0aqrmzlzoM5wO5SPQ==", "signatures": [{"sig": "MEQCIAEfopQhAndWOWIuTyapSqMpyL/S1BUquMY5wnsHc0VYAiAOVOo3cBTjSStakju3RrS5udtjpbpKURROoKQoLb4KEA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2782184}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "0928185cd544907dab472754634ddf988452aae6", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.41.0_1747546428400_0.7848775504451495", "host": "s3://npm-registry-packages-npm-production"}}, "4.41.1": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.41.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.41.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "516c4b54f80587b4a390aaf4940b40870271d35d", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.41.1.tgz", "fileCount": 3, "integrity": "sha512-+psFT9+pIh2iuGsxFYYa/LhS5MFKmuivRsx9iPJWNSGbh2XVEjk90fmpUEjCnILPEPJnikAU6SFDiEUyOv90Pg==", "signatures": [{"sig": "MEQCIBILrGE0bjRAukZp4u2QsH4HCbeelCLCg3prVP03gOcRAiAcDeyzC0UE/PYzfh8agdNpMngDHaQPjiStfJtjqEFwgg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2858472}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "7c469dc4eb8e1cb6def9fdc04581fdfce9975da3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.41.1_1748067293235_0.9621831296955698", "host": "s3://npm-registry-packages-npm-production"}}, "4.41.2": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.41.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.41.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "60bcac53e064f95840f65ec0b69e06108833460e", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.41.2.tgz", "fileCount": 3, "integrity": "sha512-PFhMLaWu0lFziS+buQIJ+YGaifkABS2TGtmeuPRrPO8JKcG17sDSNj358otAP45gIzRWd4o9QM8R+DurDWJgTA==", "signatures": [{"sig": "MEUCIQCKCZTqygFMwbt0CkCCncomqBZCrYqlkLLuZpzmEQo5NAIgYhEQNhgJwyip8UfsmJvrERzcDIpCxE2BvhMf1VcsFwM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2862056}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "13b4669dbc21cb738551cd725d2a18c77b3cea11", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.41.2_1749210051650_0.32274521280089297", "host": "s3://npm-registry-packages-npm-production"}}, "4.42.0": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.42.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.42.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "0d80305a14fff372ea5e90cd35c63c6b8efbd143", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.42.0.tgz", "fileCount": 3, "integrity": "sha512-F+5J9pelstXKwRSDq92J0TEBXn2nfUrQGg+HK1+Tk7VOL09e0gBqUHugZv7SW4MGrYj41oNCUe3IKCDGVlis2g==", "signatures": [{"sig": "MEUCIFVAcUseQoYMVSo4rRiGqVvx68lykjFaAptJ0J82PV2cAiEAzK2o5QC1ZVxbyU/bC2KEdeRLvZqjFbbecZlQ8SJHliw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2862056}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "f76339428586620ff3e4c32fce48f923e7be7b05", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.42.0_1749221311915_0.711127814260966", "host": "s3://npm-registry-packages-npm-production"}}, "4.43.0": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.43.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-ia32-msvc@4.43.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["ia32"], "dist": {"shasum": "4af33168de2f65b97a8f36bd1d8d21cea34d3ccb", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.43.0.tgz", "fileCount": 3, "integrity": "sha512-fYCTEyzf8d+7diCw8b+asvWDCLMjsCEA8alvtAutqJOJp/wL5hs1rWSqJ1vkjgW0L2NB4bsYJrpKkiIPRR9dvw==", "signatures": [{"sig": "MEQCIB8fryJpX/0nm6xc1Tz129jLkojlAaj6A5EpxpDwO8CgAiAM2vBhmK7OTNw0IeXGeUclFxvGzNqmOcOEHOsSpbpHnA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2862056}, "main": "./rollup.win32-ia32-msvc.node", "gitHead": "72858cb1474b81c91902794ab7d28c79f34b8ca8", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-ia32-msvc_4.43.0_1749619380016_0.2848241553645863", "host": "s3://npm-registry-packages-npm-production"}}, "4.44.0": {"name": "@rollup/rollup-win32-ia32-msvc", "version": "4.44.0", "os": ["win32"], "cpu": ["ia32"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-ia32-msvc.node", "_id": "@rollup/rollup-win32-ia32-msvc@4.44.0", "gitHead": "fa4b2842c823f6a61f6b994a28b7fcb54419b6c6", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-3XJ0NQtMAXTWFW8FqZKcw3gOQwBtVWP/u8TpHP3CRPXD7Pd6s8lLdH3sHWh8vqKCyyiI8xW5ltJScQmBU9j7WA==", "shasum": "d9fb61d98eedfa52720b6ed9f31442b3ef4b839f", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.44.0.tgz", "fileCount": 3, "unpackedSize": 2856936, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDJKMrZdNr8udqoXmOVdwKIDOU6qj+tjFafnbrGwQZUvgIgAtGjYtsbBywiIRMoIkTqDirAcI/o9kUd992GgMuggIA="}]}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>", "actor": {"name": "lukastaegert", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-win32-ia32-msvc_4.44.0_1750314198106_0.8396135696655684"}, "_hasShrinkwrap": false}}, "time": {"created": "2023-07-31T19:18:02.031Z", "modified": "2025-06-19T06:23:18.597Z", "4.0.0-0": "2023-07-31T19:18:02.273Z", "4.0.0-1": "2023-08-01T04:49:06.748Z", "4.0.0-2": "2023-08-01T11:16:40.681Z", "4.0.0-3": "2023-08-04T08:17:06.728Z", "4.0.0-4": "2023-08-04T11:36:51.197Z", "4.0.0-5": "2023-08-20T06:56:58.537Z", "4.0.0-6": "2023-08-20T07:51:46.546Z", "4.0.0-7": "2023-08-20T10:33:43.740Z", "4.0.0-8": "2023-08-20T11:22:35.453Z", "4.0.0-9": "2023-08-20T14:29:21.101Z", "4.0.0-10": "2023-08-21T15:30:19.831Z", "4.0.0-11": "2023-08-23T10:16:08.414Z", "4.0.0-12": "2023-08-23T14:40:47.563Z", "4.0.0-13": "2023-08-24T15:48:44.957Z", "4.0.0-14": "2023-09-15T12:34:35.207Z", "4.0.0-15": "2023-09-15T13:07:02.286Z", "4.0.0-16": "2023-09-15T14:17:26.798Z", "4.0.0-17": "2023-09-15T14:59:16.913Z", "4.0.0-18": "2023-09-15T16:10:49.586Z", "4.0.0-19": "2023-09-15T18:51:07.748Z", "4.0.0-20": "2023-09-24T06:10:47.388Z", "4.0.0-21": "2023-09-24T17:22:34.038Z", "4.0.0-22": "2023-09-26T16:17:42.457Z", "4.0.0-23": "2023-09-26T20:14:34.741Z", "4.0.0-24": "2023-10-03T05:12:57.332Z", "4.0.0-25": "2023-10-05T14:13:18.054Z", "4.0.0": "2023-10-05T15:14:51.442Z", "4.0.1": "2023-10-06T12:36:55.239Z", "4.0.2": "2023-10-06T14:18:54.826Z", "4.1.0": "2023-10-14T05:52:26.191Z", "4.1.1": "2023-10-15T06:32:00.133Z", "4.1.3": "2023-10-15T17:48:38.897Z", "4.1.4": "2023-10-16T04:34:21.748Z", "4.1.5": "2023-10-28T09:23:44.619Z", "4.1.6": "2023-10-31T05:45:27.924Z", "4.2.0": "2023-10-31T08:10:53.941Z", "4.3.0": "2023-11-03T20:13:16.697Z", "4.3.1": "2023-11-11T07:58:05.766Z", "4.4.0": "2023-11-12T07:50:03.988Z", "4.4.1": "2023-11-14T05:25:45.771Z", "4.5.0": "2023-11-18T05:52:22.623Z", "4.5.1": "2023-11-21T20:13:19.996Z", "4.5.2": "2023-11-24T06:30:00.502Z", "4.6.0": "2023-11-26T13:39:23.643Z", "4.6.1": "2023-11-30T05:23:20.728Z", "4.7.0": "2023-12-08T07:58:12.747Z", "4.8.0": "2023-12-11T06:25:07.932Z", "4.9.0": "2023-12-13T09:24:28.164Z", "4.9.1": "2023-12-17T06:26:21.246Z", "4.9.2": "2023-12-30T06:23:36.871Z", "4.9.3": "2024-01-05T06:20:56.357Z", "4.9.4": "2024-01-06T06:39:10.116Z", "4.9.5": "2024-01-12T06:16:23.158Z", "4.9.6": "2024-01-21T05:52:28.579Z", "4.10.0": "2024-02-10T05:58:51.058Z", "4.11.0": "2024-02-15T06:09:53.072Z", "4.12.0": "2024-02-16T13:32:32.550Z", "4.12.1": "2024-03-06T06:03:48.228Z", "4.13.0": "2024-03-12T05:28:53.033Z", "4.13.1-1": "2024-03-24T07:39:34.554Z", "4.13.1": "2024-03-27T10:27:53.820Z", "4.13.2": "2024-03-28T14:13:49.357Z", "4.14.0": "2024-04-03T05:23:05.443Z", "4.14.1": "2024-04-07T07:35:49.923Z", "4.14.2": "2024-04-12T06:23:50.456Z", "4.14.3": "2024-04-15T07:18:44.106Z", "4.15.0": "2024-04-20T05:37:27.839Z", "4.16.0": "2024-04-21T04:42:27.357Z", "4.16.1": "2024-04-21T18:30:15.070Z", "4.16.2": "2024-04-22T15:19:32.762Z", "4.16.3": "2024-04-23T05:12:46.640Z", "4.16.4": "2024-04-23T13:15:19.398Z", "4.17.0": "2024-04-27T11:30:04.766Z", "4.17.1": "2024-04-29T04:58:06.951Z", "4.17.2": "2024-04-30T05:01:01.989Z", "4.18.0": "2024-05-22T05:03:58.316Z", "4.18.1": "2024-07-08T15:25:25.944Z", "4.19.0": "2024-07-20T05:46:28.233Z", "4.19.1": "2024-07-27T04:54:14.100Z", "4.19.2": "2024-08-01T08:33:07.328Z", "4.20.0": "2024-08-03T04:49:04.784Z", "4.21.0": "2024-08-18T05:55:46.833Z", "4.21.1": "2024-08-26T15:54:27.922Z", "4.21.2": "2024-08-30T07:04:39.588Z", "4.21.3": "2024-09-12T07:06:03.977Z", "4.22.0": "2024-09-19T04:55:45.083Z", "4.22.1": "2024-09-20T08:22:07.154Z", "4.22.2": "2024-09-20T09:33:58.710Z", "4.22.3-0": "2024-09-20T14:48:12.913Z", "4.22.3": "2024-09-21T05:03:23.997Z", "4.22.4": "2024-09-21T06:11:34.933Z", "4.22.5": "2024-09-27T11:48:30.796Z", "4.23.0": "2024-10-01T07:10:21.649Z", "4.24.0": "2024-10-02T09:37:35.319Z", "4.24.1": "2024-10-27T06:43:19.216Z", "4.24.2": "2024-10-27T15:40:26.150Z", "4.25.0-0": "2024-10-29T06:15:29.536Z", "4.24.3": "2024-10-29T14:14:27.164Z", "4.24.4": "2024-11-04T08:47:26.687Z", "4.25.0": "2024-11-09T08:37:40.946Z", "4.26.0": "2024-11-13T06:45:17.349Z", "4.27.0-0": "2024-11-13T07:03:31.076Z", "4.27.0-1": "2024-11-14T06:33:28.944Z", "4.27.0": "2024-11-15T10:40:53.615Z", "4.27.1-0": "2024-11-15T13:28:26.737Z", "4.27.1-1": "2024-11-15T15:38:22.165Z", "4.27.1": "2024-11-15T16:08:00.048Z", "4.27.2": "2024-11-15T17:20:22.577Z", "4.27.3": "2024-11-18T16:39:54.593Z", "4.27.4": "2024-11-23T07:00:37.640Z", "4.28.0": "2024-11-30T13:16:06.012Z", "4.28.1": "2024-12-06T11:45:14.235Z", "4.29.0-0": "2024-12-16T06:40:12.169Z", "4.29.0-1": "2024-12-19T06:37:48.361Z", "4.29.0-2": "2024-12-20T06:56:21.676Z", "4.29.0": "2024-12-20T18:37:43.631Z", "4.29.1": "2024-12-21T07:16:21.157Z", "4.30.0-0": "2024-12-21T07:17:33.196Z", "4.30.0-1": "2024-12-30T06:52:35.889Z", "4.29.2": "2025-01-05T12:08:02.481Z", "4.30.0": "2025-01-06T06:36:57.303Z", "4.30.1": "2025-01-07T10:36:10.871Z", "4.31.0-0": "2025-01-14T05:58:00.425Z", "4.31.0": "2025-01-19T12:57:05.640Z", "4.32.0": "2025-01-24T08:27:52.782Z", "4.33.0-0": "2025-01-28T08:30:26.746Z", "4.32.1": "2025-01-28T08:33:34.760Z", "4.33.0": "2025-02-01T07:12:19.060Z", "4.34.0": "2025-02-01T08:40:40.910Z", "4.34.1": "2025-02-03T06:58:31.354Z", "4.34.2": "2025-02-04T08:10:20.760Z", "4.34.3": "2025-02-05T09:22:24.033Z", "4.34.4": "2025-02-05T21:31:30.390Z", "4.34.5": "2025-02-07T08:53:21.201Z", "4.34.6": "2025-02-07T16:32:25.621Z", "4.34.7": "2025-02-14T09:54:17.343Z", "4.34.8": "2025-02-17T06:26:43.071Z", "4.34.9": "2025-03-01T07:32:55.842Z", "4.35.0": "2025-03-08T06:25:05.564Z", "4.36.0": "2025-03-17T08:36:03.038Z", "4.37.0": "2025-03-23T14:57:24.066Z", "4.38.0": "2025-03-29T06:29:26.459Z", "4.39.0": "2025-04-02T04:49:50.737Z", "4.40.0": "2025-04-12T08:39:53.880Z", "4.40.1": "2025-04-28T04:35:42.127Z", "4.40.2": "2025-05-06T07:27:12.759Z", "4.41.0": "2025-05-18T05:33:48.641Z", "4.41.1": "2025-05-24T06:14:53.508Z", "4.41.2": "2025-06-06T11:40:51.938Z", "4.42.0": "2025-06-06T14:48:32.151Z", "4.43.0": "2025-06-11T05:23:00.237Z", "4.44.0": "2025-06-19T06:23:18.328Z"}, "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "homepage": "https://rollupjs.org/", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "description": "Native bindings for Rollup", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "readme": "# `@rollup/rollup-win32-ia32-msvc`\n\nThis is the **i686-pc-windows-msvc** binary for `rollup`\n", "readmeFilename": "README.md"}