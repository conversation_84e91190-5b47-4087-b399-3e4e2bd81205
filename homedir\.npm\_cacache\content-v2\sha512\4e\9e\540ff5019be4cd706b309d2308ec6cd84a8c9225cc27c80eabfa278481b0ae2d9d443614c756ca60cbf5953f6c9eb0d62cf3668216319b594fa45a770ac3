{"name": "entities", "dist-tags": {"latest": "6.0.1"}, "versions": {"0.1.0": {"name": "entities", "version": "0.1.0", "dist": {"shasum": "d9c6c0a7b88c12273232bca16899cf79b7adea96", "tarball": "https://registry.npmjs.org/entities/-/entities-0.1.0.tgz", "integrity": "sha512-qqMy0afvL5jG55bC0mMZiNJJq30be2QOeLnEsuJgQaWCWtneUgPehvcSEUDrPbomOppLtasq1ax6kfnD83CTYg==", "signatures": [{"sig": "MEUCIHYM75LNSXJSz1p1M9s9E5GsMJT+Tgu/VuIB5CzW78P3AiEA9/1Tiv9zegpiIWVG4/MucWN+2Jq6KKS0CLm+0z4xJv4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.1.1": {"name": "entities", "version": "0.1.1", "dist": {"shasum": "8b96bcbaa22a9462746afe745dd4601d71377f03", "tarball": "https://registry.npmjs.org/entities/-/entities-0.1.1.tgz", "integrity": "sha512-kFvylj5u+4KN51wD7hmu9637zzMHrL40YZf7W4Cx2D9+u4bpwGHEEKGzUsSrOM65L2jC/S9u3Zfkp9pPG+JXBw==", "signatures": [{"sig": "MEUCIA0YDlpfpvnw0uxn/ZZ/Culik2z8c4M/2v0LKRDUkGyaAiEAlLMAL+qWY0JKBanbH/pQcDZKbpzh1FARFB2kcvc+pVg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.2.0": {"name": "entities", "version": "0.2.0", "dist": {"shasum": "02fc5c5a9a64b42d68926ae01ea7c972ef7d11d1", "tarball": "https://registry.npmjs.org/entities/-/entities-0.2.0.tgz", "integrity": "sha512-5CeMhmDPN8SjwfoRz1l/KBaax4fw8izwS7NmmvXu1I/jY428H15o4IaFlRvCGtyC41jaY5YDT6uTNK1GVeOx+Q==", "signatures": [{"sig": "MEQCIHWX6R6X/RNBVtDaXvN40Yz2o8yNSnBBWLX0knXbaZOKAiBv5WFLP//AqgtfWFyu/74WFGqexMEJf9864ctNw9eIhw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.2.1": {"name": "entities", "version": "0.2.1", "dist": {"shasum": "7aae886864887067f79f252a04c45309f4ac7980", "tarball": "https://registry.npmjs.org/entities/-/entities-0.2.1.tgz", "integrity": "sha512-2xus9PRl65xqZM1NgVZrabmXHETvF7Azsy+rXty/JB3VM6o58/RsUO9u4EuX7d0rTbxYn/ujXg5sV4ZvAKuZeg==", "signatures": [{"sig": "MEUCIGttMGYATRNv7Y7SvwEwPMHxvutSpLVfKN+fERmYHn1XAiEA8lRZztTWIED7kt54f3NEMjEBo6vDrD0+RUd/ULkkhjA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.2.2": {"name": "entities", "version": "0.2.2", "devDependencies": {"mocha": "~1.9.0"}, "directories": {"test": "test"}, "dist": {"shasum": "9488dc3f4e145d7df1e84bf45ee77e3c7295a650", "tarball": "https://registry.npmjs.org/entities/-/entities-0.2.2.tgz", "integrity": "sha512-PBZlxjUySTvFeA0p41eySa+YgwrlFUPJvYoo0f+MgXT6kHrwXNbDYHxh5SoK5XPAUpJoao1cooJ7XNfnFt108w==", "signatures": [{"sig": "MEQCICWucflyhzp1AvG1mqrJpl3Ugq+tZLiHsqXSQJGgkBInAiBYsq8B4kl7O3g/53dXXo+VMO1SPQuEiOu7IoCgI6Awng==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.3.0": {"name": "entities", "version": "0.3.0", "devDependencies": {"mocha": "~1.9.0"}, "directories": {"test": "test"}, "dist": {"shasum": "6ccead6010fee0c5a06f538d242792485cbfa256", "tarball": "https://registry.npmjs.org/entities/-/entities-0.3.0.tgz", "integrity": "sha512-i/9VWKXIE9Kq/8pH5cN8j5C+xTdJRgYcf8huIBhPoutmFJj15WFc60IzM4qN8raBfm6aIEivNXmgGq4lXjVxiA==", "signatures": [{"sig": "MEYCIQDGogNYJ6bha/GrJ0G6UDcaAvUKRJZZ3LOpz9+N4BduggIhAPZ3VppYdgFWb0yVnwbi+9nBVNPmJZnnq099uByT7lDh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.4.0": {"name": "entities", "version": "0.4.0", "devDependencies": {"mocha": "~1.9.0"}, "directories": {"test": "test"}, "dist": {"shasum": "c3c754554c142783df3f79785a146291fc35027a", "tarball": "https://registry.npmjs.org/entities/-/entities-0.4.0.tgz", "integrity": "sha512-nyiKBlT0ToMMnOnIO8VVR7o79wlKMOHLvEQkP8yXdla8iN2o2VLf+/zqSw147xw11vnd7hrQtb9h75hK2xE3lw==", "signatures": [{"sig": "MEUCIQDjgz7MyvqBpT+k7QDc9Jz8nSc5F0IjkmYVwTziXMDLQAIgGxw3Q1Mxk0YEndBut/kDLUWxfvNOdzS8vFkU2lNSsB4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.4.1": {"name": "entities", "version": "0.4.1", "devDependencies": {"mocha": "~1.9.0"}, "directories": {"test": "test"}, "dist": {"shasum": "3256cf3b4dcec71fc2ecc757f7180431590158e5", "tarball": "https://registry.npmjs.org/entities/-/entities-0.4.1.tgz", "integrity": "sha512-9Cia9mSESWUm2OprYfEY3E99s/T6+JTIL3BEYzQfWgnkG8ItELCmXCOuOtLaNMWqtobsRVYCKZHxmScDcOilyA==", "signatures": [{"sig": "MEYCIQDjOLW1Z/CjzuUNGJOAf0T+RP22wk7RhWdAT5udsGkMIgIhALGl8mUViv5tQQy4Dwok33s4HqFw4uR/wdPlT39YOVeD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.5.0": {"name": "entities", "version": "0.5.0", "devDependencies": {"mocha": "~1.9.0"}, "directories": {"test": "test"}, "dist": {"shasum": "f611cb5ae221050e0012c66979503fd7ae19cc49", "tarball": "https://registry.npmjs.org/entities/-/entities-0.5.0.tgz", "integrity": "sha512-T5XQtlzuW+PfeSsGp3uryfYQof820zYbnUnUDEkwUVIAfgYeixIN16c4jh8gs0SqJUTGLU0XD6QsvjEPbmdwzQ==", "signatures": [{"sig": "MEUCIQDzEY350lX8t2NHicPEtm2JeEC8B7f0h/CF7LDYFro+4QIgSh8WCPdmG3enm8MDPPTaQpe3mZJCNB4VUROKei4XSaQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.0": {"name": "entities", "version": "1.0.0", "devDependencies": {"mocha": "1", "jshint": "2", "istanbul": "*", "coveralls": "*", "mocha-lcov-reporter": "*"}, "directories": {"test": "test"}, "dist": {"shasum": "b2987aa3821347fcde642b24fdfc9e4fb712bf26", "tarball": "https://registry.npmjs.org/entities/-/entities-1.0.0.tgz", "integrity": "sha512-LbLqfXgJMmy81t+7c14mnulFHJ170cM6E+0vMXR9k/ZiZwgX8i5pNgjTCX3SO4VeUsFLV+8InixoretwU+MjBQ==", "signatures": [{"sig": "MEUCIQCWu5RpFugJxP4YFe2hFARyIGO16nxR6MzlMtfOdskt+gIgY2NgUz9IPpHB2UmdqAOL5UIHQ2aSdSlGCwEO4jeMTjc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.0": {"name": "entities", "version": "1.1.0", "devDependencies": {"mocha": "1", "jshint": "2", "istanbul": "*", "coveralls": "*", "mocha-lcov-reporter": "*"}, "directories": {"test": "test"}, "dist": {"shasum": "c8d70be28aae033e1f800e2c8b3bd153f911a72c", "tarball": "https://registry.npmjs.org/entities/-/entities-1.1.0.tgz", "integrity": "sha512-lab/Q2zvDNDyxGzCygUmPy5omhtftdGQpTsOs0feRpK4p2uJTHTTVhCRSuEEhQ5GywIe7EnAtSRMc/qlPoo6ag==", "signatures": [{"sig": "MEYCIQCF10/5Yt0pNu4Tf5MOa7Ia7jWQyMeAIbrqHzvHVdXEfgIhAP1DE0yg5QoLTnj6l9q6tECGWd9s3cKJopgjvfbJz6Ng", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.1": {"name": "entities", "version": "1.1.1", "devDependencies": {"mocha": "1", "jshint": "2", "istanbul": "*", "coveralls": "*", "mocha-lcov-reporter": "*"}, "directories": {"test": "test"}, "dist": {"shasum": "6e5c2d0a5621b5dadaecef80b90edfb5cd7772f0", "tarball": "https://registry.npmjs.org/entities/-/entities-1.1.1.tgz", "integrity": "sha512-bWStsIg5+vWzgtKoA/pkAIJzRAeUatnImqcK4CyHd2KoNq3fCg/tHDoE/7e81yRq9Xg8XUSvAcsSaMCp4a3HuQ==", "signatures": [{"sig": "MEUCIQC0bmNAjLw0SKivNIYZJ6nDmN1186dznk8qBJCVPWyXqgIgLjND5muRzkjSZ1AUk37PFUHrLwL5WllnkKC1IuNmxBA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.2": {"name": "entities", "version": "1.1.2", "devDependencies": {"mocha": "^5.0.1", "jshint": "2", "istanbul": "*", "coveralls": "*", "mocha-lcov-reporter": "*"}, "directories": {"test": "test"}, "dist": {"shasum": "bdfa735299664dfafd34529ed4f8522a275fea56", "tarball": "https://registry.npmjs.org/entities/-/entities-1.1.2.tgz", "fileCount": 14, "integrity": "sha512-f2LZMYl1Fzu7YSBKg+RoROelpOaNrcGmE9AZubeDfrCEia483oW4MI4VyFd5VNHIgQ/7qm1I0wUHK1eJnn2y2w==", "signatures": [{"sig": "MEUCIQDkYMxUq0BadpKofz1aUZ+TDuoneScsgeg6MhWi53YNoAIgVwp/gWELwjKH1UajdWkDn32I3aiTedVyTmzjmWOuU2Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57359, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbzP03CRA9TVsSAnZWagAAJBQP+wca0lyuDaPXh22c1FZ1\nCMsY1wnU6lg71osycf2M4dpV/Q4Mmd8n4kvAdsOG12EK0UrSfaJnC2xRiTcP\niC+rp9XO+AP3e+8V8EbnJhJHkeHEJBs9JMi5nOCehKNH1EUM/ZmCZ+WIDfii\nUVQ8WIoBmuvEdLydKqO9yQdv5KjZsfZyC0mi3MtcDPfJ2zTngfJ5OgaAkKzM\nJnjGjUvGbBBwwEm3eP7aJNGWMFRzVjOiFN6o5BcIbFBZ0dSIjZ9Fwlbj95yl\nWpCJoS/ayzfp+TJGySWkbFhGzXtrvn0vlky/7rfh7b4fZ5fcogdsWoIqtwO2\nJgcub34OdC+atMAX0bwkixV8qbtFfKB6wyWiE04xvBeop/RR9k8xgvUR/T+h\n/8w1PnuuFZLrqec6rkt+NqaYA4GBOl3FhzHy9vyW9Hjvsa3ZnfAxxaLm8xcH\nbCTtHprwhI/nYq6HEPqXd6XgRJ5v9s7uzasSX8PncV6H3TLF4dBfiWc9pt/e\n1T+L5BV+kYBImbefy0OjnmcryFZk2YqbBkyglC27J6DeGkZjq4vYQ8rqS2ee\nm69Q21fYmP+LkF5K0cdCo4hEFZw2smOMFjmP5JpOSZmKWfjJjrmRYTxQDSSx\nh2ptVmuE7BFC8/05KQ4miVVBTmHOUieC0nKvVNCQhs9USShr5ighrrrlCDZj\n6yp8\r\n=2vNQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0": {"name": "entities", "version": "2.0.0", "devDependencies": {"nyc": "^14.1.1", "mocha": "^6.1.4", "eslint": "^6.0.1", "ts-node": "^8.3.0", "prettier": "^1.18.2", "coveralls": "*", "typescript": "^3.5.3", "@types/node": "^12.6.8", "@types/mocha": "^5.2.7", "source-map-support": "^0.5.12", "mocha-lcov-reporter": "*", "eslint-config-prettier": "^6.0.0", "@typescript-eslint/parser": "^1.12.0", "@typescript-eslint/eslint-plugin": "^1.12.0"}, "directories": {"test": "test"}, "dist": {"shasum": "68d6084cab1b079767540d80e56a39b423e4abf4", "tarball": "https://registry.npmjs.org/entities/-/entities-2.0.0.tgz", "fileCount": 31, "integrity": "sha512-D9f7V0JSRwIxlRI2mjMqufDrRDnx8p+eEOz7aUM9SuvF8gsBzra0/6tbjl1m8eQHrZlYj6PxqE00hZ1SAIKPLw==", "signatures": [{"sig": "MEYCIQDLLpOBWmmYYZ8boTn+iEZl4O6z98iRwqEVeFQxfdBFxQIhAJoqd5ihW0WtOMGKzx0vUlHnCip8w3bTKnM2JhJInQN2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70181, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdL6PYCRA9TVsSAnZWagAAlS0P/3QZiV03MM5QV9G5Ufv/\nsQjJgBO/LurR0hVpm2KrIHR+3XIJbY+7CqeFRP/75SBZU3QvvZg5CagRs0vp\ngC0QgNYDjt/aTFlR6KaYaCXEjIPDrqsRU3tmgYyUYkv/dnJbiKsXLdNMwaYF\nPTVEzScTjqCcNKzW6GLXukW94/RNMICb2NdQmndi/9ulp+E9ldCXwbBfNgqv\nstEPhOjFeGyqm1yZOI47Va/vU/m1xmj6Q+9UDeLqkXRwuhWTVx6WseInW+Dk\nc1DbfF96kfYAUvUlNQTH/p2rYrSsloJf92PucRlmIhc7bY8CSznHOkC6U9KG\n1Rr+MSmqBcX9Ips6+qVQWAe6730BdXD6Unupe4e71PmLqnxvqBoR7r3XxMvu\nAAJIkrd1KzzjEgu0ol7krCRgiQTXI7xMXHh7Ie3PuCwW6+pYtrpJ6jsrQXWR\nFPUVXxaVtS/qUvgig18iqAZuY7zmzQhcqAVv2/TZPWBx30dopJ8gqBwrqcJQ\nUZrSIYCpuWu+j5BQ16CV+2ks9l74owRaFOL6CV9ZtMbFqtk2ZdGxrkR5Q3vf\n7jmsywAnygBBqJeo0teFee4PJxlc60gRFshTAtmZfhaX97vLN44rtatgTczr\nJ/rdJXvBbelQ03sV42/V3+xWYHW9rEsaA3MUAYWCP8zAYA/1LxD/bMdlz7wI\nhr4I\r\n=4rPz\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.1": {"name": "entities", "version": "2.0.1", "devDependencies": {"jest": "^26.0.1", "eslint": "^7.0.0", "ts-jest": "^25.3.1", "prettier": "^2.0.5", "coveralls": "*", "typescript": "^3.5.3", "@types/jest": "^25.1.4", "@types/node": "^13.13.4", "eslint-config-prettier": "^6.0.0", "@typescript-eslint/parser": "^2.31.0", "@typescript-eslint/eslint-plugin": "^2.31.0"}, "dist": {"shasum": "743a5783cdb50315e9cfd42a2f86c66f55bc4349", "tarball": "https://registry.npmjs.org/entities/-/entities-2.0.1.tgz", "fileCount": 19, "integrity": "sha512-OMkgoczBGrDmlphOCL5LwlMJosZuysXb4qiz6LEI4rkvX7y1lDWrl4PSWT68d70muq40ztOeaZ33v4lR/WkV0g==", "signatures": [{"sig": "MEUCIBL9OwA3J7pXER3yixpZIVBMnT9CTzEg9RAg9+T2gp5fAiEAt+jVagOKrsUv00xMuSU5MEDw+ZYxUq6ue+STgvgueTk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62718, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJetyhNCRA9TVsSAnZWagAAhpAP/AhVL3JA3dvMftOd+HKj\nM6J4B3a9Hcc4L/t37zwOx4CaNyCMUVJ9xUwmXFYfVhtGAHe8yCyMmPDEUF37\nV+ps0/rUzN3XrcclMaFs1sBqp9BXbeEhpKRxyKwIrpsAHLVKK0QfZY1YGX+7\nPh7mKR4YKtARFjNwvCkWaN9KG6/AZLX25eL48gtyvt7IOerZi7HpiIru58ol\ncIkgCJaWCrPAf3kg9qaSSaRKpsVkH+wlwo9BsHTarjK95S1AZv8jSW0RGkjW\nVWl+7ej5wxfF7amV0tfONLpaufnb+YN8JGzmUscC3h6Xf3yNCXFxz/Qiy4JZ\na0SeWvqFCDVQaRB/Ejt4tVRx6r2ehTCxOQL2bO4q8ER+rWH5a+3y+M52XebQ\nOtkn3bQGE+lX/+sPelZ3otgPLwp+pe0YfVqAtT8U6maPkivfggWnkmAyOHob\nDqATuPzZKoxwvMonCjdnDlZ125SxTBk/H4/nB4aJ//c9r9l0RdGfHgQXELBL\nL45GmIrgG/P/rvDFRIorkrneKvfq98mKZl8VvViV8IvgKuuYm5agJwnjhezf\nH3Lrwkmjvt10DUUlL0L2iA3Y6HYiqB4bDdfQZ71ZiqMk9R9XfJy5LVYsAyFj\nNI1S4GCR4c6+8IaPHRFB0BpIl2IapBCF0x5joHEaI0G96JCmQrGYqw7+TymK\n9YPK\r\n=pHBo\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.2": {"name": "entities", "version": "2.0.2", "devDependencies": {"jest": "^26.0.1", "eslint": "^7.0.0", "ts-jest": "^25.3.1", "prettier": "^2.0.5", "coveralls": "*", "typescript": "^3.5.3", "@types/jest": "^25.1.4", "@types/node": "^13.13.4", "eslint-config-prettier": "^6.0.0", "@typescript-eslint/parser": "^2.31.0", "@typescript-eslint/eslint-plugin": "^2.31.0"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "ac74db0bba8d33808bbf36809c3a5c3683531436", "tarball": "https://registry.npmjs.org/entities/-/entities-2.0.2.tgz", "fileCount": 19, "integrity": "sha512-dmD3AvJQBUjKpcNkoqr+x+IF0SdRtPz9Vk0uTy4yWqga9ibB6s4v++QFWNohjiUGoMlF552ZvNyXDxz5iW0qmw==", "signatures": [{"sig": "MEYCIQCaw4e8yMOsGVqU40pVaX4HT+xy5ftBQWM07uu9JkUcSgIhAMBa69Y34VdVmaup8eyFvCW9wWAsS0tBn+cpjmuJdFcj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62799, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJetyj2CRA9TVsSAnZWagAA7uoP/3s/JT3hr+9UWPCbIFvG\nmYbAy7govYWod8YHnYJen+A5Ij7EHgdgUCyAStx7bcWqGQrMySjtd8MBSU4l\n6iYMn/A7RGgAgTn4j1Lhc5PQwL6fPIpLjVAdRQmX/fNpYGnUm+ssp/pN0wcb\nligbzUvSj+2vnYhj5JdD77xEOPuDSFAe/IHaFNAyU7VCJ7AYn5pKXCGYhhhw\nF+VftzRkyFOuywUd7cdgBdqZ94S7px4AVFPj3lywGTQE/ODllP8UlC2FlRs5\nYncpoGm441278a4xHbYlDf0JOKFqG1psaAZWdVWJNp/7SlH7Js4akiucISXt\nDxC6xu4ci4Y6rpdfc8AvAiZOulFXgtNcCYZRFKnkXjmMRQzOvZnhdxnveVcC\nXVteURInPz7V2iIPoy+K7C//bg1/gwB+kUTbCYWmDRTFggOQtXRoZRHmyeOX\nceIztMV7tccPP1Tlfh0ZyIvpsLpqvnPoy3kvsZjdLWtYY+VOwmaqtXY9t9gI\npa3ryvPJKGTg1TqDY1aqiFwt7tI07D5KcSsYfUMBojqd2XlQbVEkjOCZNgFF\nfxcR6wnNE0MAMDw6un1PQsFYtpeb9fkkWDHPc4PpJlwHOD5dUpfkor9g3UCi\n36NCrIKbqNCOPNhoglHBwispGlVXtyR0orOmB9rW1l3/ot7NQPziJj3lTx9J\njNa2\r\n=+ozc\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.3": {"name": "entities", "version": "2.0.3", "devDependencies": {"jest": "^26.0.1", "eslint": "^7.0.0", "ts-jest": "^26.1.0", "prettier": "^2.0.5", "coveralls": "*", "typescript": "^3.5.3", "@types/jest": "^25.1.4", "@types/node": "^14.0.6", "eslint-config-prettier": "^6.0.0", "@typescript-eslint/parser": "^2.31.0", "@typescript-eslint/eslint-plugin": "^2.31.0"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "5c487e5742ab93c15abb5da22759b8590ec03b7f", "tarball": "https://registry.npmjs.org/entities/-/entities-2.0.3.tgz", "fileCount": 19, "integrity": "sha512-MyoZ0jgnLvB2X3Lg5HqpFmn1kybDiIfEQmKzTb5apr51Rb+T3KdmMiqa70T+bhGnyv7bQ6WMj2QMHpGMmlrUYQ==", "signatures": [{"sig": "MEQCIGv4kHn51uYwZv5c2+C7W2LidQjxc0YNevkmHJPklC1fAiBG7WXSeX1xs9f7Fp4RDNVpc/WJluhn55ivSOjKMIHGxg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64191, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe2DUPCRA9TVsSAnZWagAAqNkP/04wDZmSYLa/7iXCVzey\n83rQNxUO12XPlqtHIUc9/GJ2650yyDOzVTDfQKAi4vvs/Z+U0QEbMCAg3YoU\n8nMrT2vh+7Frxfmm7uZyvpjsOzFGUGG04KaiqCFLm7gmqV6FGklqLAZNuQiC\nA+NJEHDy5/uEwovzFmBezeDu9PneuNbtdsl6LXI25Opz+IFVXV8vG0HCGG0+\nRvFWthhUs7K59W3VHTWCp/667veRI1zO2ON/rsClYiYH85D68TVRAhzP1+it\nrSxJsSOK8cwHdXVt2AvKIwGMFsfyYxojMJcn4aEH24tFCHv9LsmfA6/Y2aop\nW6elphE4VCO11vzjnJ5aaUQ99+kd8eig245O3EDy8e+cblwX98aNTA7uSYkC\nQJCoRSuJvLmv0zc9VNH5Z8m0UsXq3axVt+WtrK2lCQd0VqYaACXkgNYndAH0\nMJAwSPgZeWh/OS6SF3L9uDu8mVTwCYfEq5diWfBRDW/jkEcfOjIm2QsLPVFs\nupgkIGmxIIFfY/zi5n694lGQMYj3HKElv6iemxFfko7ZT5zE+deuahTTFL7W\n96808FO55dZjOyhpO4WYnwNeNbuu1xaCf3JIKZHzDt873n5iq+zdJTwrF3up\nFeFdE8BtfreQcqJZlJtX3lA3qaff6w+wd7A9YoemKQXt7eDOy2UCJ0KohGaK\nomSB\r\n=ViHj\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.1.0": {"name": "entities", "version": "2.1.0", "devDependencies": {"jest": "^26.5.3", "eslint": "^7.11.0", "ts-jest": "^26.1.0", "prettier": "^2.0.5", "coveralls": "*", "typescript": "^4.0.2", "@types/jest": "^26.0.0", "@types/node": "^14.11.8", "eslint-plugin-node": "^11.1.0", "eslint-config-prettier": "^6.0.0", "@typescript-eslint/parser": "^4.4.1", "@typescript-eslint/eslint-plugin": "^4.4.1"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "992d3129cf7df6870b96c57858c249a120f8b8b5", "tarball": "https://registry.npmjs.org/entities/-/entities-2.1.0.tgz", "fileCount": 19, "integrity": "sha512-hCx1oky9PFrJ611mf0ifBLBRW8lUUVRlFolb5gWRfIELabBlbp9xZvrqZLZAs+NxFnbfQoeGd8wDkygjg7U85w==", "signatures": [{"sig": "MEYCIQDtb9+v+G329EUTt+l/sDMU/Tp5eRc8dV3UZC0e1J4E/QIhAOWQNvuJmaFVsUGTaGIyviu0dF7cPbWrqs2QXOuFz9TN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52713, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfikteCRA9TVsSAnZWagAARmEP/RPlbdRDBQxJZKcif+3O\nMtw2yYgbveN8c1dG9O2wjGKAVYvhvSH2t4iNXbWOV8VhJfVmq8879sx69/42\nyMDnuoMhLSj1HM2FW+31zI723FQ+oXb7hv8FlScW09REj6Do2PdnFmsqEOxf\nMY8D/WfAyPi0KERfA5g5zyFdNtDxpqTC+h2Oc1pO/jtgAqw1C377qzuBEGRD\nNsvIv3D/ZTO+H1oOctrljulaU4mcd4Dm0R3CcHDguhHJpEKEhHKvc8gYZJZR\nLadDdE+bAIf1cFBTYyd12F3eT0OqgPdxaqe9N3AlYzGZ7s7lRcXF12krUv6U\n7qynm+4cvhROQjQbhDzwH0zWCLfoJAg4SIVURUEm0p5EU9B4xzUoZ+qKMuTa\nYYNAVmZPFBSHmKQF5qqcqEZ0O/ncWUsjbSKt0d9j1A7PxqRcm1vKM3iUb1de\nOtZleDqomnethLnZpLYapCaR/TebEmHsK9ZCHEenBIPHeY8NBOnPDg7LIjai\nlu7s5q7id1uMB1VHO84cpKBbPFCB8WrRgLfrXRSBnFAzY4+thmT4BGF39kDR\nO4ymKQC8PmCxjKMpXOCBJrFTnrMvoZNoOb2W4h7g3YPyT7k63i0prUaFHGQl\nyDboxEf04K1274naXLanRrlbOvBuRYyO8M8hUVIKVFwGbV0bfnOLwrth4dxT\nTZzA\r\n=O9rl\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": "https://github.com/fb55/entities?sponsor=1"}, "2.2.0": {"name": "entities", "version": "2.2.0", "devDependencies": {"jest": "^26.5.3", "eslint": "^7.11.0", "ts-jest": "^26.1.0", "prettier": "^2.0.5", "coveralls": "*", "typescript": "^4.0.2", "@types/jest": "^26.0.0", "@types/node": "^14.11.8", "eslint-plugin-node": "^11.1.0", "eslint-config-prettier": "^7.0.0", "@typescript-eslint/parser": "^4.4.1", "@typescript-eslint/eslint-plugin": "^4.4.1"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "098dc90ebb83d8dffa089d55256b351d34c4da55", "tarball": "https://registry.npmjs.org/entities/-/entities-2.2.0.tgz", "fileCount": 19, "integrity": "sha512-p92if5Nz619I0w+akJrLZH0MX0Pb5DX39XOwQTtXSdQQOaYH03S1uIQp4mhOZtAXrxq4ViO67YTiLBo2638o9A==", "signatures": [{"sig": "MEUCIQCRVzshRyvzaMCvzr7QbONg9wCeRT8vbWg3yvZdClcX2gIgOVAYkDMgZXH3m/2KXT6hUe+WiMRhFSDC9DhI7vWEKPA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57673, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgDhPBCRA9TVsSAnZWagAANYUP/RqNQ3QQZ5LUS46QZFOv\nzeqAg0GMqAo1tw+ja1IxmMJOitBhPb86PWbcxe4iV2COY3Om5+66mgt3CET7\ntwua6jbh0xOftJaW27OZG0wmVWobNF/cgARvIQYGtLu9GSKOPveAASF5gyG3\nF7G1xXOLDhZHQAaw02/mzKo748gv+9LCAYqzc+hhnwaY8XWS42znqopdxuRo\n9GiPWWnOad3KSnzt+77lxnLxC0ZkaMTMEnVTqAmM5vPlQmryd0SJMbu5MjNg\n66bQq3HFBWYsoQdvh1mfGC8E2r/Vcl5FoNW9dqI18a+L195zaebrPEjiRvds\nbJPGGsWh9vMh9IXLRCqJOymHoAgqPo9tU0wE3lECwDyxT+nt1lGJdHUdCj2Z\nHF+eG7SIgbvtSW2dxL5RDBokGHFSamgIauGiIP3DXJI+I7+6wfSlWgvOvvGw\nvx4lZPRqkNhS/VjnqGE8MSIr6wPxsb6eTYo8OL7ViTISpNUgfnoTuf05yvN1\nWNwc/jI9TtYqdFUT4N1GwPWopUv0sB7Uc5TZlKAHqxH348pXTb3cKcAjp6IO\nPs9HHSfVHeXLsNugq/gbXh4qbHkGaD5wxZ2QVRCoX3lfOUdXFxR69282Hi8r\nVSThGNzlcJOUR7kjmikPGQ9+PZ+dzPCcOVqvIglFjsydaI3ppvElpRtI5/5w\nJjpN\r\n=mgco\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": "https://github.com/fb55/entities?sponsor=1"}, "3.0.0": {"name": "entities", "version": "3.0.0", "devDependencies": {"jest": "^27.0.3", "eslint": "^7.32.0", "ts-jest": "^27.0.4", "prettier": "^2.0.5", "typescript": "^4.0.2", "@types/jest": "^26.0.24", "@types/node": "^16.4.13", "eslint-plugin-node": "^11.1.0", "eslint-config-prettier": "^8.1.0", "@typescript-eslint/parser": "^4.29.0", "@typescript-eslint/eslint-plugin": "^4.29.0"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "59c1762aaa20a2482b9b0a0dd4f447c9c13cf0d5", "tarball": "https://registry.npmjs.org/entities/-/entities-3.0.0.tgz", "fileCount": 27, "integrity": "sha512-BCTCcmWItCjI/bwic3Y2o4mt2P7cSHQpgkRh/jHzhE0fAgAxNjx5WvfF3ZCQ1DWACrk+VRY/YOtWu9ap962zUQ==", "signatures": [{"sig": "MEUCID5wz4aJaO1VdVP7BNWpKsERpGCsK7UKarpOK5dGHgFdAiEA+god233/+xcG//qtr1aJNVIYPjlrCeLjRQIcQIORu/g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 172163, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhDwYNCRA9TVsSAnZWagAAU5QP/1srUJvwEKgvPZO4m1KU\nM2zYPMJfQ/eVXj+ovOO8mYVu1CVKX6LdN0bnWvYGPY6kT6AhFpVq4xpcChMN\n/bu3Z8+Z/dbovoQ4eLsMhCMOcYSBNEtvWFAPSERchhM7WJckl70qmzD/N7vr\nlhReP+Aflrqr2B8arQ2XSI8ck69XxkIAyljMj19FO8/6cFKWFEzHvO0IlwI5\nrazb+Vte6GllEn5k8sb5WcXJGzck5uQJEGAVSEimD2usKTD83YJ7YgeBD6zU\n1Ih5ezqKn+yJhg615kp/VgwNEAhDqpmc5Wru1urVxQOxnxkACRM1FVAd/aUy\nDlTBWURNr0ScNVNCAcEwjb93UJcQHu46AG8/0vLJC3CJ1DzuhRUQnK0yV+b+\nOTKuszrm0U1oEsNmobuNAbB1ZZhfUBNZjROQoWJk2eQNEWCwvYfdJvy8OLRB\nNrS59QjNLTl326GahM842ZHiKxK1iD3sduAt+I009l0aK8gsocoh1YSVIbP0\no8ndWq9yrBUv6JnuOyC7d07WTqCSwEznJT8GZZ3DfgfLj1Ddi4U9Eu5QOdy7\naatqoCK7xqATOYs11udWumOA33PmCjqw94O0z3a/FQTfX40zkx7g7LMPFSI+\nNVFB+x+pwb2+hhqKhLViy8FyWluk8Zwn2d08fKwkyxc1B4kndXkwjCSxqBaa\nwTtK\r\n=ZSbd\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=0.12"}, "funding": "https://github.com/fb55/entities?sponsor=1"}, "3.0.1": {"name": "entities", "version": "3.0.1", "devDependencies": {"jest": "^27.0.3", "eslint": "^7.32.0", "ts-jest": "^27.0.4", "prettier": "^2.0.5", "typescript": "^4.0.2", "@types/jest": "^26.0.24", "@types/node": "^16.4.13", "eslint-plugin-node": "^11.1.0", "eslint-config-prettier": "^8.1.0", "@typescript-eslint/parser": "^4.29.0", "@typescript-eslint/eslint-plugin": "^4.29.0"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "2b887ca62585e96db3903482d336c1006c3001d4", "tarball": "https://registry.npmjs.org/entities/-/entities-3.0.1.tgz", "fileCount": 27, "integrity": "sha512-WiyBqoomrwMdFG1e0kqvASYfnlb0lp8M5o5Fw2OFq1hNZxxcNk8Ik0Xm7LxzBhuidnZB/UtBqVCgUz3kBOP51Q==", "signatures": [{"sig": "MEUCIFTFGIPOxaBEgqkWByMDEhV4CmoVqhq9hskxx4gIhGt4AiEAwkP858qPx99J1AWPWLWfmPjI82QGEygHQxklXZJ71fU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 171119, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhEQ/LCRA9TVsSAnZWagAAMUQP/j/UehYer5YzwMMBWK7o\n+GPEjkkfRcB/wIZmXQYd9PR123N55DZCeLfhKn3rNfh5pG0zzi2B4Q0bB3E1\nSzApscepsjV8UjOfjaMZgYJtdhQntlGfDO5vSMu2dTGiUnOadtRs5UowBm9J\nwnuGdM6G8m/QoV8vRb2i2kd3+GlQisTXhzKc4LjbljESDgwDKH1NZyVsucJT\nku0Zok04WNK3/Z4drVeUVmN5FUSlHhm7nHtX7OOClk3fPxB3RXhu9285fsKE\nPnvy3xhx+8yAtjd53K2PZ3t/u4DmG4llsDwGg3dpVlxg2qycCmVXNcfwzbNl\ni05nv2IYzr9ELOcOOI3onSg/w2XxwXp//VV7DkHaCeGHXsBthuQXotDylgYn\n7j0krUw1p271cS4vP+6LtpYsJ42hbRK8KiAbRWrE+b+Bdr98Hg682yPdxhyT\nBxd2JI2cvbqKg3YE89fUcMPofoVZP5/SI23cvstxFT1xcmJPQUqi4IwxTQNt\n2JsyNL7Vto4VGR6p4TrrB0lF9GtMzs/fCrofZYmersQiYeyk2CxuoFZyukfT\nIv3WhpCNT7z4FidqIeggzELA3bIwj+EKroPDHefKdrBJDZQQlSRlltSUHtUf\n9gbKpOaXAelprOG6jGMWx2BFnePkCPaj49Zyw8bqy3Sf1KvS1Ay33FYG5J6O\nwJjQ\r\n=sNUJ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=0.12"}, "funding": "https://github.com/fb55/entities?sponsor=1"}, "4.0.0": {"name": "entities", "version": "4.0.0", "devDependencies": {"jest": "^27.5.1", "eslint": "^8.12.0", "ts-jest": "^27.1.4", "typedoc": "^0.22.13", "prettier": "^2.6.1", "typescript": "^4.6.3", "@types/jest": "^27.4.1", "@types/node": "^17.0.23", "eslint-plugin-node": "^11.1.0", "eslint-config-prettier": "^8.5.0", "@typescript-eslint/parser": "^5.17.0", "@typescript-eslint/eslint-plugin": "^5.17.0"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "178200738f774678b4e4621d82959851b0e67ab4", "tarball": "https://registry.npmjs.org/entities/-/entities-4.0.0.tgz", "fileCount": 62, "integrity": "sha512-4v2Nrbg5YsidL0sfsj05GSI86KCn2IJX3TlUq11aDUIq0O5iICDGI/WwVUS7hKPBhfoKr9E4e2Jx8A7rDbpg/w==", "signatures": [{"sig": "MEUCIQD2ot+4ruZ2d/yZcYm6iBmUA0UqAvgReKPfpS6jiQfuPQIgYk+TWpXYzDH/CxRkBxqXsT4XF1WhuPJHdMLqgvAY8NQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 616366, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiR2xaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo7gQ//SyCnDfoUo2k1mzZc/26B807jOjOZj2YSKyWDUW2mIDPEHBuW\r\nG2pCzOolCSPk546VFRXkEgxKPGrMT3hS+VocCi5qo71fW2Bt+Xh/wvdoAkvg\r\nQfji6ZcysFaysVf8uUsgnMUstB0nxPTt8Ntwb+QEmOZMm7guAFk56rdJi4cu\r\nWVIFQg6omeSJj0N8AftP4iHgIpW4X9iSyyINudiuONOGTLbMYmDASJhdKLm+\r\nCxRP0tmeN+8RvlAADzkPVCdEWw6PBmkuuEzpwf6fRn4YQ1RzGvdadhVd52Tp\r\nhSLz3Q7cg72ZT5pb4SgCSd51aZp1io2MSdKaOulln17tv4alJz72qKcQSvjU\r\nbplMW60JvcskoFiuvSQ/ECsRwrnmxzRI5AE1gO7+hVfaBb+/A4RTG/+sqoFT\r\n/OxCYb+adBEE0MhZIUfZwVcNbAOBS6zgm0AAYSZBqxdm1mj1aHDg8F33nJqh\r\nBAcRKkjVmY+r8wR7yM1D5WRQWergPh1FUtVAhQLjGPQwr4+iVYvxiK3sM4JU\r\njkYNzE8m7rJeGqkrNZw3R6rOtZP9bAQ1LPBfFYv1w2Mp7QI8Qa9txuq5Luei\r\ng2L8oapnNVkdkpzI85vQxazaEjxufyLrjzOZJLmLCq4nurAv9Wd9L47kP5h6\r\nVlrdXMVVR47X76ofJCVCWycIx9Rpss7xbDo=\r\n=0B3+\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=0.12"}, "funding": "https://github.com/fb55/entities?sponsor=1"}, "4.1.0": {"name": "entities", "version": "4.1.0", "devDependencies": {"jest": "^27.5.1", "eslint": "^8.12.0", "ts-jest": "^27.1.4", "typedoc": "^0.22.13", "prettier": "^2.6.1", "typescript": "^4.6.3", "@types/jest": "^27.4.1", "@types/node": "^17.0.23", "eslint-plugin-node": "^11.1.0", "eslint-config-prettier": "^8.5.0", "@typescript-eslint/parser": "^5.17.0", "@typescript-eslint/eslint-plugin": "^5.17.0"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "528b16f186a78b390566b7c9b622a2fe625e5717", "tarball": "https://registry.npmjs.org/entities/-/entities-4.1.0.tgz", "fileCount": 68, "integrity": "sha512-8oyVJDboaJcbzf4tS3eneZS+3bQd/2SiwcTJ45vI5FtwJbRvC6d0eZPEiFu6Ca+jMJFYMSnGwsNF+szPAOjksg==", "signatures": [{"sig": "MEYCIQDymED9Yu4NaTPeuPtQmRSlnVNqPtXl/tgvEyWtcp/97gIhAKjerTDMw40zM4Mc19Bu3OdJgVp/1ZSzJo0O+YXrN5WL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 724834, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiSEilACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr3hA//cYPaZaVxnFRQnm1Tdnl6T+ME7Bp8hr4IWK39joXOkAP+u5qP\r\nWjsuXcSgGoWnXcpthumxai0M2gKjnsX3C4TpHYquw+oTMZMW/ROOR1lBzBCH\r\nCbz2kbAAp9ZpOPN6xQFMJb7ot8yn7TpwJkzujjsdtqzIGXCjk0cpirPai7K1\r\n3gmqdboXbAEkRS4JTnta85xOqrcGxh3gO2HDn612Ejf0xvB017+mH/8/RmWI\r\ndSiJ+4l6CaFXaa+HGReISwCwmYC69jjvGTF3zlq0MmL/jKX7mZWR6ox9pf0Y\r\nlCB0NJ8KX1PmTW3DAf/4ShnGIF6noo6xY1XgxaF8y2QC7IKyE5HQWEuxS+8P\r\nrhxJJVI0j36uUOnZBo/NZFDokiuASVIfFYc2QCi5/nNAOdsrHHShVupxAJdQ\r\nXUYCCBBKVHQIyoJGHdkh7+B1lrTvKYdWHUs+jbaOeAXEW7RkjGz0sXamNvTH\r\nysUfGItI77eMCqISEOPr6lOJ9MrJpa7/RvHq9Uko+8RbWx3ky8GkYrgyHOgT\r\nd9vTKm4dwmuolsUQKNFjaLqniH92l+lizm2Yqn4v+hUPcLOOOUjJ9zAX3Zew\r\nxiH0pH/nPYDReHMZB61lzonahJF6lylbxR4ILtJOTFl1wBsgH+xd/mHJZB1Q\r\nPUHgKQYdUY77+kUFN4Ij2trjZY8ES/Wlrfw=\r\n=Ow1q\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=0.12"}, "funding": "https://github.com/fb55/entities?sponsor=1"}, "4.1.1": {"name": "entities", "version": "4.1.1", "devDependencies": {"jest": "^27.5.1", "eslint": "^8.12.0", "ts-jest": "^27.1.4", "typedoc": "^0.22.13", "prettier": "^2.6.1", "typescript": "^4.6.3", "@types/jest": "^27.4.1", "@types/node": "^17.0.23", "eslint-plugin-node": "^11.1.0", "eslint-config-prettier": "^8.5.0", "@typescript-eslint/parser": "^5.17.0", "@typescript-eslint/eslint-plugin": "^5.17.0"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "fc6ad5c3b77898165a143356db12d2f62eb1feb4", "tarball": "https://registry.npmjs.org/entities/-/entities-4.1.1.tgz", "fileCount": 68, "integrity": "sha512-AxszXDqnHj5aVzjBpofDDfXX9zC8gugYwJxEYDdA52d6dqoxPKfNDBFxZyIZrkaqUtNy/ip/knBm6mRJed7p1A==", "signatures": [{"sig": "MEYCIQDjXoA9EQPGRIab4TOQ3uPnYAR64ZhdYp2fxKdtazkI+QIhAMzyzKWuapa1SBhRlorMvIxxr6fsNZuM/hTZtGOv99Xa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 724845, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiSMqrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqjvRAAmcVZhTrtycC1/sZ2soWyEmP+nYkD1goQ6dl3cLDXyzKw4ISp\r\nhHKlhpkgIJSa6iRBeIwlDwKP8JNBGABjhuSVUemkn2pMB7VxnvRQaRpkQUGd\r\nNawmponkBGNXYlKiHjAYYmcrdcE0TZ2uv0Ljmr5KVjjmwQ1nprUThw6Q/5ep\r\nppOJX0Nb+G4SqJKL/SiScfq/TG0V2zcFuNnBq+IMGeDwUVyj2X3/Ls0+NY3J\r\nZyjSbV0wpLjMQO7JdqiJl6izW6Zp7huQIDtluLUjBi4VaslYkywo1H6e8Z5e\r\nsnfXjqtTr2tqz7YIz2YT4JHKydcgo9eCdIo8JU6E1J6HQdPIgdH6NKLvt6sg\r\nz2DNuU0mfDm5NZUVXylOVkMbnYq2jtf21U5wlrC6jjVmFICj9uexzzNbZfti\r\nnCVOLzYXhAqtToBeoEdnsjOhwnTznYDjvgRCE7KR/2mvGxTVVv/pBF6Xq4r7\r\nZeMF7DrpVm8t+7MrVjthlu2WVVk75HV7Ztuwi9jVijXEai8SY2SR6k02iTv4\r\nqEGcTpeyav9u1XA/NfmZinpyaU8pYDKtWgKkWDM16p26FzFBhRlPT/z0FJ6/\r\nA162AVpEuoJb+BbG21FpKZmYJIgXxCoXiJ3av4PM30Lyl0XOR+KUoBx6UBlK\r\nk7XQEexrVFzEh5K+igX3Qx3a4oyMYJeA4x8=\r\n=epZJ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=0.12"}, "funding": "https://github.com/fb55/entities?sponsor=1"}, "4.2.0": {"name": "entities", "version": "4.2.0", "devDependencies": {"jest": "^27.5.1", "eslint": "^8.12.0", "ts-jest": "^27.1.4", "typedoc": "^0.22.13", "prettier": "^2.6.2", "typescript": "^4.6.3", "@types/jest": "^27.4.1", "@types/node": "^17.0.23", "eslint-plugin-node": "^11.1.0", "eslint-config-prettier": "^8.5.0", "@typescript-eslint/parser": "^5.18.0", "@typescript-eslint/eslint-plugin": "^5.18.0"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "e66570a4f577b427fa416703c29f833c67b0b79a", "tarball": "https://registry.npmjs.org/entities/-/entities-4.2.0.tgz", "fileCount": 68, "integrity": "sha512-wEJa03bJgqEwPnkUqYdgmcfUXfm6+4hePQhntIvRy/1/+C4dFuhYHsgKBRjbQ6OWBh42P+VhAoCDO77DUh0e/Q==", "signatures": [{"sig": "MEYCIQDqU0dIEB4kXIEy6SraKIH63qQBDt2iPeitqpBqLG4PXgIhAMa+xbqHl45Sg5EGjfiec3bIxDbxhbQO9FSXMuvz2BM2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 726088, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiTsllACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpnpA//fHIDQvvYPDdWL8hC7bezLyXZTj1fnK8AMBZ4/zoaGcU2Ep3o\r\nALHPzbro46VyONgJTUoTe1SsF93wSKEL90+wji796FdeK+otE4LZC1A4QcIJ\r\nFcgU1d6QfnVdNAjI26EgQuPRgZAiLWZdeT+dPKjfloVQFmZJJQTZkvy1eW+O\r\nhWKawNHvzgHLPacd4tzac7sdS6EBjzDMihLU3+BngFijLGOO7veXVoBtzUZu\r\n0dRc3CGmfPfhR/8TAIxNM7MxF/iY/7gcXR0uRsrevs9XyybEFigOLrh0ZxAx\r\n4hfdrrx8vGoksKKDNU8CNBqmx9Saoq/FpwMox9lv6/EFOSkAxPoCj/EPCqdQ\r\nIa/JMMCBDIGDh8peGjVN3cCvhusMBSLlX08F+sRz1ULSwg7Is7i7gzValCOn\r\nqYbK077Fo07MLS2B/K8E2Is8HY/rboMMr+QWxUilM4oTUnFgxDCiheldEYtr\r\nnV3SHVATAlH4AUrgBcgm0a5s6+I3ImESX+xtbM3DB9GttsI6gr5PXgfqTnOV\r\nBE04w1c5KZCO2x7ZYjpOtDCkUTMlZWkw5iJNkRDhL0uw39pdtx7JE+QQiTe9\r\n0/Y3wMcEhlk3YgGQkIsGu71xVB0eSl7VL0T6eF7RKx5po9aLFZ3RXtJI7ZDR\r\nc0PCBbrvBri4Dq/imklxcFREWaUDd4YnQPo=\r\n=K9tJ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=0.12"}, "funding": "https://github.com/fb55/entities?sponsor=1"}, "4.3.0": {"name": "entities", "version": "4.3.0", "devDependencies": {"jest": "^27.5.1", "eslint": "^8.12.0", "ts-jest": "^27.1.4", "typedoc": "^0.22.14", "prettier": "^2.6.2", "typescript": "^4.6.3", "@types/jest": "^27.4.1", "@types/node": "^17.0.23", "eslint-plugin-node": "^11.1.0", "eslint-config-prettier": "^8.5.0", "@typescript-eslint/parser": "^5.18.0", "@typescript-eslint/eslint-plugin": "^5.18.0"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "62915f08d67353bb4eb67e3d62641a4059aec656", "tarball": "https://registry.npmjs.org/entities/-/entities-4.3.0.tgz", "fileCount": 68, "integrity": "sha512-/iP1rZrSEJ0DTlPiX+jbzlA3eVkY/e8L8SozroF395fIqE3TYF/Nz7YOMAawta+vLmyJ/hkGNNPcSbMADCCXbg==", "signatures": [{"sig": "MEYCIQCdz9/qMCh2K7/O8ThtZMGkpbeCorhT5YUrew4QsoNyvAIhAJw19+xi/Xv+eaAzooRq4JK4+SpeLCn33WU3SIe6XSbk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 730095, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiUtwYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr6mg//UZk+o1/njS6WlDCwKijxA+NHdA2oE/lbTyfyeGZBIo68FulV\r\ngdi1URA7Atzlf5MXmyiqBIkeK1VYPM3zE/B1wvnvldP+xWPAH4Jn9xtkEBvX\r\n9+NRP9jrVznez/g4wv9Ap4fMgzKruCNXy41JQKDqtZoPUaFrL6TNH7ZyggE0\r\njWi1RNpsQuzn6pL6fBdRnyQFFmIIYWCjfFJeYLaSx1Y91qpgLK1x5cxD2u/J\r\nBgmv6VA/zPDZ0yhtDJlycPruHcW7r116RlI89BwTZVFezMm+kqoU23Xhb8ak\r\n9ZAoSgUDXgzygfAepm38v4mRbrzshIM9LS9KSk3Ot/PiZyPNKKyCOH6Fmq6u\r\nx9AlKdirE7l91r/I5AR7PCSQ7f3P6eIZ6PfLjlf8sMb702pAFVxQYxEVWZxF\r\nBYHS9gLne5fsW+MPumj/2YmKQ+EYLbKdRkPXLBKH2R9ry1vRUdIZcT/CYcpq\r\nti5gLOB5FMW/m1a81C/QAIimnwKfo6wfavio3ZNcAZ25BuJ6qLDfC+6vl/z+\r\nlN0Ag0nEYZ2l8xWECo0dmUmHV7l6nJa77i3qJ3gm8QQFwPgMTXt+7xUms94h\r\nVjvfiC+dv4H6ae3IFoPvYYi6UZgapRa+Y9iwr+BtuwJKxcH/CJ1YtEk7C75e\r\nQyiQzLl3bwjm2RTh/2+tetFQkVRYuct0m7g=\r\n=uRA9\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=0.12"}, "funding": "https://github.com/fb55/entities?sponsor=1"}, "4.3.1": {"name": "entities", "version": "4.3.1", "devDependencies": {"jest": "^28.1.2", "eslint": "^8.18.0", "ts-jest": "^28.0.5", "typedoc": "^0.23.2", "prettier": "^2.7.1", "typescript": "^4.7.4", "@types/jest": "^28.1.4", "@types/node": "^18.0.0", "eslint-plugin-node": "^11.1.0", "eslint-config-prettier": "^8.5.0", "@typescript-eslint/parser": "^5.30.0", "@typescript-eslint/eslint-plugin": "^5.30.0"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "c34062a94c865c322f9d67b4384e4169bcede6a4", "tarball": "https://registry.npmjs.org/entities/-/entities-4.3.1.tgz", "fileCount": 68, "integrity": "sha512-o4q/dYJlmyjP2zfnaWDUC6A3BQFmVTX+tZPezK7k0GLSU9QYCauscf5Y+qcEPzKL+EixVouYDgLQK5H9GrLpkg==", "signatures": [{"sig": "MEUCIQCYDuDOLARk5IjZr012W1G/TaLCvojpDaw7YvpMK9EhsQIgPBhsUCAU19TVq25QCbPlmybGqFkGpDyY91pEIGxk3dg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 732832, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiv0CYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpFlQ/+LU2bL6QE6T0FScz57/ut9mSpRTnaO7zlKghI4LoCpG1AQr0c\r\nTd0XQomJPupMG06qpL+FxJKUBqLNypDek8xP5LxVmHZJHjZ2um/LIWK/6qiR\r\nV3/HrDKXn8seQ/Wh2jXgoSFFH6/Qx2uS4s/9NkhCZllLvdaS02JfeT33zh9s\r\nbUqJXyuHSyDJKs/PLePRfwrU/MM4TbFp6TLG3mjMG8P2Fm8s0lS3J8uxYrLG\r\ntY6N/4SgO/qDOZ82MeavF6OXBnQkWM/HVU3iwvmw0okKelxgrU5QYtmE/dE6\r\nkeqwXNyp9xoU+nX+DKfC0Juiz1e3eQyX9h44MZmGmlT2L3FUC7eDmcteTHUO\r\nBGqgJUQkYaB9bVpvzGGLb+IXEaatoG+CkSMm07dQbeV+YrI6kDsjGjEfd4Pl\r\nI0KbMWwXtTZ4rnoSzh59q+uVYtrKR0xF4MDLaokWmjCT6AmdRpSsCQVT22tt\r\nLATRRN9nyBTRLZ0GFn/KICmHTW88HcapopzCs/pdm/ZfdWHxnxFDf0NSmO9d\r\nl/PmM4Qx5mJsk0z08rhuLs9txPjYh81irPdCJNNDl1oKMxqz39DGBg2OBZsf\r\nK/5czMySkBFvfC7C/tWdpl34pBkcWV5pxnZrvHeBYg19uevYDO5KHAkVxrUT\r\nda/bJ++e7U5WNPlsTcWOqs3ERbMPm29tDDo=\r\n=nTRR\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=0.12"}, "funding": "https://github.com/fb55/entities?sponsor=1"}, "4.4.0": {"name": "entities", "version": "4.4.0", "devDependencies": {"jest": "^28.1.3", "eslint": "^8.23.0", "ts-jest": "^28.0.8", "typedoc": "^0.23.12", "prettier": "^2.7.1", "typescript": "^4.8.2", "@types/jest": "^28.1.8", "@types/node": "^18.7.14", "eslint-plugin-node": "^11.1.0", "eslint-config-prettier": "^8.5.0", "@typescript-eslint/parser": "^5.36.1", "@typescript-eslint/eslint-plugin": "^5.36.1"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "97bdaba170339446495e653cfd2db78962900174", "tarball": "https://registry.npmjs.org/entities/-/entities-4.4.0.tgz", "fileCount": 68, "integrity": "sha512-oYp7156SP8LkeGD0GF85ad1X9Ai79WtRsZ2gxJqtBuzH+98YUV6jkHEKlZkMbcrjJjIVJNIDP/3WL9wQkoPbWA==", "signatures": [{"sig": "MEUCID8RzH4C3PHQgf+t7qbIx6eXFl09i/gNxvDU7IcjZHnoAiEA4PoxGaqJbpr8CR/YVHQKXSnEiV6gwarkDoi3A4NhWug=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 353316, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjEHYNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrHrg/9H/MwpVlZ7gpwoIGl0qgrMW1BSHY+64XwXV5l/MkCgCsw9cca\r\n6lEPG/4UYRTlHCfCOev3QQ6kJGTjAqTwnM6FgAh/3nXaCI5GU7GkLeRq5izL\r\nbuYlKjHwv9uV5ntpyYm/J1SUpnuCAm0E+YqVCqYAOpMCrjml6/GaO77cEpk4\r\nsknZEQkwK59EUMWXvkm/YTqH9Gkr9AMIa5xwI1CQduwmhIP2SYj/zbIuCre0\r\nnsmKFA+HrsEBAr3oK533utbHdVHIn833imjZ5tNqca6Fi9xMt7SmHI89VsEB\r\nXqV9cdhWlJOL2EXvLOCjjVTKKWyIdn//Hvwn3XH6V14fjDmXUUbMDzyy1ZVO\r\nFqrMJGp3R5Qlv1vvvTO8aEFvu/w2dpgc+/fSKPXOWdb5/MGo/vWwzjDd3vC6\r\ndQVmvhC1hsAeeqUGk7mbw27s8tQSzF1XNdVfFUK78pRCld8HVlQmVrISNgAI\r\nbTB2x5FZ5vqjyqCVC9OI9SPuSVJipMAGmEsaRJJUm/GjWmkgqvyv8Zxst/VK\r\ndTLkP63LhdsV8XmWkRioThS6SwbfOjSbfdm0Ls5zrcAjHO2OU1ammw+Gylfp\r\n+IW5KvLBn8Gi83eqPLQGVjo4le7+GbtXodY+C1SxEkcgyP7KM61G8LafDfXq\r\no5nS9AO2kcLBCO/y3VSvpF/xKW7GDTKc3vI=\r\n=dubq\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=0.12"}, "funding": "https://github.com/fb55/entities?sponsor=1"}, "4.5.0": {"name": "entities", "version": "4.5.0", "devDependencies": {"jest": "^28.1.3", "eslint": "^8.38.0", "ts-jest": "^28.0.8", "typedoc": "^0.24.1", "prettier": "^2.8.7", "typescript": "^5.0.4", "@types/jest": "^28.1.8", "@types/node": "^18.15.11", "eslint-plugin-node": "^11.1.0", "eslint-config-prettier": "^8.8.0", "@typescript-eslint/parser": "^5.58.0", "@typescript-eslint/eslint-plugin": "^5.58.0"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "5d268ea5e7113ec74c4d033b79ea5a35a488fb48", "tarball": "https://registry.npmjs.org/entities/-/entities-4.5.0.tgz", "fileCount": 68, "integrity": "sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw==", "signatures": [{"sig": "MEUCIFnDuAfvOEAP+8GFqwPx1P0ODfSteOsQwx1FXj4Hell1AiEAxPf8dru+/3BMiBsS4fAxNQprQF3RS8qteSY1KDlj928=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 412892, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkOEKkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoFVA/+NeBYybvDjHNs6XnWGm1rPER3YLHIGzNCBw9iC8Ts7hC7wM43\r\nKRo7fqLeNqHQ17l1NvGoY/Nad0P+dMMjnI86JteeWj5XaO4U5ZVQa89HxWT4\r\nf41ozfQmbguBcwHrIrXuXw0UpvIhwe5E1QHPXMTuGhkqSklwCAX8NAW0Rxpq\r\n2tSPKR+PR7TB8U+/QG6gw6MkCelLeAGbXI9c6PYgrdR4WEZJdvBohtQl+xpx\r\n/tUzQ4Nv0VQxMfOzjsC/z4rRK3pR72s+GBT5e8zqVqXSqFha44pWGpSpdOiI\r\nkqKKCwFTLND8u3v6gwwS3mEChh9i70m4aI36jARIFidTTEaBgKk/lVlsMeua\r\nJG5WDxFfcmv3DeezxkIYJ1RgPq5ZWqylzxOay7zjq/DEFeTn0WUbJCciPELp\r\nSc/6XExhcpcdJ6dHkLdUWnvid6yqmc1fPcUy2TbIVXmmeTkmEULek+UJy/Sh\r\n1gJi/HOlk61F8WyvHGDNns0DqZmgMb7sbmXgRHMsgkBq0g+u0O2Jy1ElVapC\r\nMQN3IgeBUFrxL8Fjm4ym3ETJamE7FIqwVZxxAABMsuApJNb82LiRiDEqtYDA\r\ntBGcOd8OR7YK4O6Fa2UnEmx8xKXaipl17ADinGuBJ+vI3Krv9N+BHlbUZf8R\r\nkZvsiilF0BXTeD4IYczbO9NOQMKhufQSlM8=\r\n=NiHs\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=0.12"}, "funding": "https://github.com/fb55/entities?sponsor=1"}, "5.0.0": {"name": "entities", "version": "5.0.0", "devDependencies": {"tsx": "^4.15.7", "tshy": "^1.16.1", "eslint": "^8.57.0", "vitest": "^1.6.0", "typedoc": "^0.26.2", "prettier": "^3.3.2", "typescript": "^5.5.2", "@types/node": "^20.14.8", "eslint-plugin-n": "^17.9.0", "@vitest/coverage-v8": "^1.6.0", "eslint-plugin-unicorn": "^54.0.0", "eslint-config-prettier": "^9.1.0", "@typescript-eslint/parser": "^7.14.1", "@typescript-eslint/eslint-plugin": "^7.14.1"}, "dist": {"shasum": "b2ab51fe40d995817979ec79dd621154c3c0f62b", "tarball": "https://registry.npmjs.org/entities/-/entities-5.0.0.tgz", "fileCount": 82, "integrity": "sha512-BeJFvFRJddxobhvEdm5GqHzRV/X+ACeuw0/BuuxsCh1EUZcAIz8+kYmBp/LrQuloy6K1f3a0M7+IhmZ7QnkISA==", "signatures": [{"sig": "MEUCIQCn1K6+kbCshcPnkX+Lru9mmbzmws3mGlSI1ZbBidytGwIgPo/YAJ1dY4cIxRCPLChjgTwC2Is18UAAE/qq2ySF1TI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 538592}, "engines": {"node": ">=0.12"}, "funding": "https://github.com/fb55/entities?sponsor=1"}, "6.0.0": {"name": "entities", "version": "6.0.0", "devDependencies": {"tsx": "^4.19.2", "tshy": "^3.0.2", "eslint": "^8.57.1", "vitest": "^2.0.2", "typedoc": "^0.27.5", "prettier": "^3.4.2", "typescript": "^5.7.2", "@types/node": "^22.10.2", "eslint-plugin-n": "^17.15.1", "@vitest/coverage-v8": "^2.1.8", "eslint-plugin-unicorn": "^56.0.1", "eslint-config-prettier": "^9.1.0", "@typescript-eslint/parser": "^8.18.0", "@typescript-eslint/eslint-plugin": "^8.18.1"}, "dist": {"shasum": "09c9e29cb79b0a6459a9b9db9efb418ac5bb8e51", "tarball": "https://registry.npmjs.org/entities/-/entities-6.0.0.tgz", "fileCount": 84, "integrity": "sha512-aKstq2TDOndCn4diEyp9Uq/Flu2i1GlLkc6XIDQSDMuaFE3OPW5OphLCyQ5SpSJZTb4reN+kTcYru5yIfXoRPw==", "signatures": [{"sig": "MEUCIQDod4gueMg/jAaprRgSnW4OET+kaRnmavz+mEhXF7e/swIgaSeKclnJONUjvNxax4+cu/bLvZA122zkvgWYUfNlDyg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 540419}, "engines": {"node": ">=0.12"}, "funding": "https://github.com/fb55/entities?sponsor=1"}, "6.0.1": {"name": "entities", "version": "6.0.1", "devDependencies": {"@types/node": "^22.15.30", "@typescript-eslint/eslint-plugin": "^8.33.1", "@typescript-eslint/parser": "^8.33.1", "@vitest/coverage-v8": "^2.1.8", "eslint": "^8.57.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-n": "^17.19.0", "eslint-plugin-unicorn": "^56.0.1", "prettier": "^3.5.3", "tshy": "^3.0.2", "tsx": "^4.19.4", "typedoc": "^0.28.5", "typescript": "^5.8.3", "vitest": "^2.0.2"}, "dist": {"integrity": "sha512-aN97NXWF6AWBTahfVOIrB/NShkzi5H7F9r1s9mD3cDj4Ko5f2qhhVoYMibXF7GlLveb/D2ioWay8lxI97Ven3g==", "shasum": "c28c34a43379ca7f61d074130b2f5f7020a30694", "tarball": "https://registry.npmjs.org/entities/-/entities-6.0.1.tgz", "fileCount": 86, "unpackedSize": 540551, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQC3VvSO19lPnaq+4Txc7DJo5/IKsbFMacfN/ZIevM14ewIgONXumuj31t3ttXPY9aEy1yrsivfof3R8xPA84sfXIMI="}]}, "engines": {"node": ">=0.12"}, "funding": "https://github.com/fb55/entities?sponsor=1"}}, "modified": "2025-06-08T19:31:24.268Z"}