{"_id": "cheerio", "_rev": "888-a12f2f20fa47a3add4c4aca72dc1226f", "name": "cheerio", "dist-tags": {"latest": "1.1.0"}, "versions": {"0.0.1": {"name": "cheerio", "version": "0.0.1", "author": {"url": "mattmueller.me", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio@0.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "0c52df43cdade2999a735009595061c53c7f9cf0", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.0.1.tgz", "integrity": "sha512-VmsJUQMJWgMKXFGPR6fjBMOp/JxhyjYhB+EiV3/2zKtaCrBRXRQCiSIgs8EeCfEI9qKP+Ws1EKJwzQMMu1hpBQ==", "signatures": [{"sig": "MEUCIGGgTA/wxxgPBdHqIU7YJe/0wl/ZrtQO91FKpb2zqy/6AiEAlr8fs9a4oqWB9Nj7T2td60ofj2b7unN1HXEoeglb22I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.4.11"}, "scripts": {}, "repository": {"url": ""}, "_npmVersion": "1.0.25", "description": "jQuery-like wrapper for soupselect and node-htmlparser", "directories": {}, "_nodeVersion": "v0.4.11", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/cheerio/0.0.1/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"htmlparser": "1.x", "soupselect": "0.2.x"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.0.2": {"name": "cheerio", "version": "0.0.2", "author": {"url": "mattmueller.me", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio@0.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "ac7d42190f1c8f47ba67065eef95d4e73a5ace6f", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.0.2.tgz", "integrity": "sha512-xer2SxAni12j0zBgyrJOVo7ct7s4Z1QobSYxSjNmSGwPPy61gL0s+v/9a3D6u+GUNvjHuKFI/Wu/Oap5oSIZJw==", "signatures": [{"sig": "MEUCIETEetNUgqpWoP3GmYHd62a73UR1l2FYkXYd0A6J+hqCAiEA809BhntZaF+znG84BE+ckZpI7r6SK73mGRNJ+kjjVLM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.4.11"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "repository": {"url": ""}, "_npmVersion": "1.0.103", "description": "jQuery-like wrapper for soupselect and node-htmlparser", "directories": {}, "_nodeVersion": "v0.4.11", "dependencies": {"htmlparser": "1.x", "soupselect": "0.2.x"}, "_defaultsLoaded": true, "devDependencies": {"vows": "0.5.x", "coffee-script": "1.1.x", "diff_match_patch": "0.1.x"}, "_engineSupported": true}, "0.0.3": {"name": "cheerio", "version": "0.0.3", "author": {"url": "mattmueller.me", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio@0.0.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "b8ea52580cffa209bb9132eb6721993688e46f1e", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.0.3.tgz", "integrity": "sha512-jQsrvYJ8MzEDveqK1Qy+upddBua3qgblh8bJ6H2qZlq/sZkIE4SPyRCXcLl7UgS9QepEkIhHNLvuGKqlyMakiw==", "signatures": [{"sig": "MEYCIQC1h1GIGAAIooUF9v6ezqdtj47cDZBz/ajJMHmHDXsCfgIhAPDkcZOGxEbRnvrv3lQQUQHtX3oPtBOZ6LV/m7EjNjiy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.4.11"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "repository": {"url": ""}, "_npmVersion": "1.0.103", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "_nodeVersion": "v0.4.11", "dependencies": {"htmlparser": "1.x", "soupselect": "0.2.x"}, "_defaultsLoaded": true, "devDependencies": {"vows": "0.5.x", "coffee-script": "1.1.x", "diff_match_patch": "0.1.x"}, "_engineSupported": true}, "0.0.4": {"name": "cheerio", "version": "0.0.4", "author": {"url": "mattmueller.me", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio@0.0.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "b3816949f6151356a84913f67dff8d066d279120", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.0.4.tgz", "integrity": "sha512-IL6P72i0c3WTB30gFFf1rGeTaro4ylosRLNYQPpeDiyJ4421bNkiI1byijHhsMXJl5pQvDwjZ9efUnAWnthdeQ==", "signatures": [{"sig": "MEUCIQCLyOr98D2DWTxxumOYb8DQwJnib1VyNz04wfOg/+ERDQIgdt7fA/q6GHJDe5/NHYshWa3cwbngzNlMvxgHv+b3jU0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.4.11"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/MatthewMueller/cheerio.git", "type": "git"}, "_npmVersion": "1.0.103", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "_nodeVersion": "v0.4.11", "dependencies": {"htmlparser": "1.x", "soupselect": "0.2.x"}, "_defaultsLoaded": true, "devDependencies": {"vows": "0.5.x", "coffee-script": "1.1.x", "diff_match_patch": "0.1.x"}, "_engineSupported": true}, "0.1.1": {"name": "cheerio", "version": "0.1.1", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper"], "author": {"url": "mattmueller.me", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio@0.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "9aad92f1a92b80f67bee683ddacf1815a1201d06", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.1.1.tgz", "integrity": "sha512-jdLtpjOoBIkZAYIWDUt2mWmzEY0vK17E9jwnYSthhFzKaGktb44MGUlJOw0f2/ll+vL/VAaXPxLJcdKI8+opKA==", "signatures": [{"sig": "MEYCIQCqq7jznUvxRsYJsC4k74yVi4SJC87dJAa8Pr+xcEZdNgIhAO6aNf+Ic8g8imX+I+TWvbi8UyQv0hVWQtOm2Ct2hd1F", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.4.11"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/MatthewMueller/cheerio.git", "type": "git"}, "_npmVersion": "1.0.103", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "_nodeVersion": "v0.4.11", "dependencies": {"htmlparser": "1.x", "soupselect": "0.2.x", "underscore": "1.x"}, "_defaultsLoaded": true, "devDependencies": {"vows": "0.5.x", "coffee-script": "1.1.x", "diff_match_patch": "0.1.x"}, "_engineSupported": true}, "0.1.2": {"name": "cheerio", "version": "0.1.2", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper"], "author": {"url": "mattmueller.me", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio@0.1.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "d0bd301ee5360ee69d87a525fd6751c217fde6d7", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.1.2.tgz", "integrity": "sha512-3480Swhh2a85EeQJ7FYwk9019bChd4/ZiNAua9Vhw++d6zq8qf0atpRuPKjZQbjG4n5SWuY2WcxgN7fLNVbIQA==", "signatures": [{"sig": "MEYCIQD0JWAhnU0o556+I663EEzQIEeKeZqrw3Rp/1de+e9u6gIhAKlkWqFPEFnm2HdbiCHn0pmff4iRBMqkx2+WyYScFvOW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.4.11"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/MatthewMueller/cheerio.git", "type": "git"}, "_npmVersion": "1.0.103", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "_nodeVersion": "v0.4.11", "dependencies": {"htmlparser": "1.x", "soupselect": "0.2.x", "underscore": "1.x"}, "_defaultsLoaded": true, "devDependencies": {"vows": "0.5.x", "coffee-script": "1.1.x", "diff_match_patch": "0.1.x"}, "_engineSupported": true}, "0.1.3": {"name": "cheerio", "version": "0.1.3", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper"], "author": {"url": "mattmueller.me", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio@0.1.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "242ad274ada4780aca138c2a83944d1c6fb4b8eb", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.1.3.tgz", "integrity": "sha512-pZEi9VLIT1hW1n/E/6U1zpwfG2lCfgUqeaIqwzjv93PFtnKB56war/DZe4tlA/VtpltghSUGOYZsNh8UbTh0vg==", "signatures": [{"sig": "MEUCIAlH32gMPVRi0zLSs6b8SH2llmE/qVs5MpUqfVy8K4rlAiEAgtZaRnfEQqG98zwlPrvbIzN7P6xZuamoMzXizoviJ2M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.4.11"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/MatthewMueller/cheerio.git", "type": "git"}, "_npmVersion": "1.0.103", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "_nodeVersion": "v0.4.11", "dependencies": {"htmlparser": "1.x", "soupselect": "0.2.x", "underscore": "1.x"}, "_defaultsLoaded": true, "devDependencies": {"vows": "0.5.x", "coffee-script": "1.1.x", "diff_match_patch": "0.1.x"}, "_engineSupported": true}, "0.1.4": {"name": "cheerio", "version": "0.1.4", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper"], "author": {"url": "mattmueller.me", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio@0.1.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "1aa6c969f46c5ace7e47fe76ce4fed5b5108369f", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.1.4.tgz", "integrity": "sha512-5MmuI+6Z+tQ4dO5QSFbVvl8v7MJAJ9Z6E/JqFgB8BkHjyLZTS+Z5hSZK8i5rSXPgkTm4MarVBVo3+M7ndeUXig==", "signatures": [{"sig": "MEUCIQDC67LORTbwFAMrnLWBWKse+drcj9k/lLmTP3mzpJeh+wIgSd5cT1nF+C+EZ5rM2jl0DMtStE2I6mSOu1uDfYbwh1I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.4.11"}, "scripts": {"test": "vows tests/test.cheerio.coffee --spec", "prepublish": "coffee -o lib/ src/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/MatthewMueller/cheerio.git", "type": "git"}, "_npmVersion": "1.0.103", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "_nodeVersion": "v0.4.11", "dependencies": {"htmlparser": "1.x", "soupselect": "0.2.x", "underscore": "1.x"}, "_defaultsLoaded": true, "devDependencies": {"vows": "0.5.x", "coffee-script": "1.1.x", "diff_match_patch": "0.1.x"}, "_engineSupported": true}, "0.1.5": {"name": "cheerio", "version": "0.1.5", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper"], "author": {"url": "mattmueller.me", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio@0.1.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "34f701bf8f6194978a3afafdd1de8240e5e200f9", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.1.5.tgz", "integrity": "sha512-vRdlCRN3tG6GwmOhl0nMUhUMep4PLaZmXF0Yb2eJP9daK5cULOtpOHd5J1gEEBEaXj4sV27dYMr3xnFsHectGw==", "signatures": [{"sig": "MEUCIQDVwYJvZw8feCgn+GD3nF17SAyI4cQV0HQRFlH/9ovvZgIgOlyDsXlBnSRioGrFpP8AonJY03Cm4eWd88oin/hq6L0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.4.11"}, "scripts": {"test": "coffee -o lib/ src/ && vows tests/test.cheerio.coffee --spec", "prepublish": "coffee -o lib/ src/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/MatthewMueller/cheerio.git", "type": "git"}, "_npmVersion": "1.0.103", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "_nodeVersion": "v0.4.11", "dependencies": {"htmlparser": "1.x", "soupselect": "0.2.x", "underscore": "1.x"}, "_defaultsLoaded": true, "devDependencies": {"vows": "0.5.x", "should": "0.x", "coffee-script": "1.1.x", "diff_match_patch": "0.1.x"}, "_engineSupported": true}, "0.2.0": {"name": "cheerio", "version": "0.2.0", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper"], "author": {"url": "mattmueller.me", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio@0.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "b3a0a90a0e7a4922bdb105a17b3b04a412c2e6e3", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.2.0.tgz", "integrity": "sha512-NkLNsH3T4iKb290XPsK5hC/mbDNqcVWXI8eWtzTshwWpLfIw05a3ztzymUXVygLsJ4W5olp1AMXQZLBp3+rn0w==", "signatures": [{"sig": "MEUCIQC/JHIR1NN7Qo/bI/Hb4s1COnmg5Z/MM/rCGm3ydpnKoAIgEinB9Lcwdb0Ldp4alPcQwF6RDFu25B7Yv81+4lZjn6w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.4.11"}, "scripts": {"test": "coffee -o lib/ src/ && vows tests/test.cheerio.coffee --spec", "prepublish": "coffee -o lib/ src/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/MatthewMueller/cheerio.git", "type": "git"}, "_npmVersion": "1.0.103", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "_nodeVersion": "v0.4.11", "dependencies": {"htmlparser": "1.x", "soupselect": "0.2.x", "underscore": "1.x"}, "_defaultsLoaded": true, "devDependencies": {"vows": "0.5.x", "should": "0.x", "coffee-script": "1.1.x", "diff_match_patch": "0.1.x"}, "_engineSupported": true}, "0.2.1": {"name": "cheerio", "version": "0.2.1", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper"], "author": {"url": "mattmueller.me", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio@0.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "595d089c5b65d0b77d983fb59ce4239127f81200", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.2.1.tgz", "integrity": "sha512-xQjPGE1t14HbXVaCaP7fUICCEVXAjzdbzhj+l5fPzkm9E7GkKAYFXjOI0az++hSkHpQaWY3RIlwZpRnvzrPs0w==", "signatures": [{"sig": "MEQCIFOFk9y9mrupvKPeLZXjwK4XQ6JO/Zq2fhFN7OuqNp50AiA6kLrt/6ZNDFriw4Xt0dz6/R8iFm7VHceQj9KhVK/pxw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.4.11"}, "scripts": {"test": "coffee -o lib/ src/ && vows tests/test.cheerio.coffee --spec", "prepublish": "coffee -o lib/ src/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/MatthewMueller/cheerio.git", "type": "git"}, "_npmVersion": "1.0.103", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "_nodeVersion": "v0.4.11", "dependencies": {"htmlparser": "1.x", "soupselect": "0.2.x", "underscore": "1.x"}, "_defaultsLoaded": true, "devDependencies": {"vows": "0.5.x", "should": "0.x", "coffee-script": "1.1.x", "diff_match_patch": "0.1.x"}, "_engineSupported": true}, "0.2.2": {"name": "cheerio", "version": "0.2.2", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper"], "author": {"url": "mattmueller.me", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio@0.2.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "e89bc7fc1a18737614ce29d1a78f9f8afc91c3e6", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.2.2.tgz", "integrity": "sha512-fT9th1a9WNwtIX7Gcv9pNX24j2GvngOh8p8xilacDvnz/XyiwFKjzZ4UHYo2M236i/nCRKf+I14hCXVUo0yHVw==", "signatures": [{"sig": "MEYCIQDjpl8va8+QiKZ9eSMj+1Q6H8hIogzzkuCEuDX/Ct80wwIhANB5UU7y6vw7qg1KmKYSWTiClvvxhMuXVTBeAatiwZ8x", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.4.11"}, "scripts": {"test": "coffee -o lib/ src/ && vows tests/test.cheerio.coffee --spec", "prepublish": "coffee -o lib/ src/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/MatthewMueller/cheerio.git", "type": "git"}, "_npmVersion": "1.0.104", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "_nodeVersion": "v0.5.10", "dependencies": {"htmlparser": "1.x", "soupselect": "0.2.x", "underscore": "1.x"}, "_defaultsLoaded": true, "devDependencies": {"vows": "0.5.x", "should": "0.x", "coffee-script": "1.1.x", "diff_match_patch": "0.1.x"}, "_engineSupported": true}, "0.3.0": {"name": "cheerio", "version": "0.3.0", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper"], "author": {"url": "mattmueller.me", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio@0.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "b56cc0f281c08134f22868ce7cf8fc164c49274c", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.3.0.tgz", "integrity": "sha512-Pt21UMGzi7gtu3N/hgEeVpiq53jVVgum4YlyfMO5u2YH1upM7WJEHWVumI0awn1JzFg7tELmF4Ff/2YcBwvizw==", "signatures": [{"sig": "MEUCIDLelXW/V1qKq0go1OQKwT6scHuzrnIVtByaCrBh+aHYAiEA9o7OZEqMYiYiTeD847YBwG9HK9L8/SxCk0ExYowrV48=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.4.11"}, "scripts": {"test": "coffee -o lib/ src/ && vows tests/test.cheerio.coffee --spec", "prepublish": "coffee -o lib/ src/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/MatthewMueller/cheerio.git", "type": "git"}, "_npmVersion": "1.0.104", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "_nodeVersion": "v0.4.11", "dependencies": {"soupselect": "0.2.x", "underscore": "1.x", "htmlparser2": "1.x"}, "_defaultsLoaded": true, "devDependencies": {"vows": "0.5.x", "should": "0.x", "request": "2.x", "coffee-script": "1.1.x", "diff_match_patch": "0.1.x"}, "_engineSupported": true}, "0.3.1": {"name": "cheerio", "version": "0.3.1", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper"], "author": {"url": "mattmueller.me", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio@0.3.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "9211ad8c25752a7ae789c183d4af186ee39ef804", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.3.1.tgz", "integrity": "sha512-gSB6HZvh9qJqfX3ETqaOvYXL4VYjhvUIZfPnFFoyaHzDPz1VVojODVMzYhw6ORUZANYllVxE3OQGu4HIJFuBKg==", "signatures": [{"sig": "MEYCIQCGUynQYwEWmfDr5AoNhQkb8DnPfeqvEqWWZQwrF3oO3AIhAIq+u0jzjHVBofhRj3NTwnZRlqILJF+8h+KRW1wJoTMq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.4.11"}, "scripts": {"test": "coffee -o lib/ src/ && vows tests/test.cheerio.coffee --spec", "prepublish": "coffee -o lib/ src/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/MatthewMueller/cheerio.git", "type": "git"}, "_npmVersion": "1.0.104", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "_nodeVersion": "v0.4.11", "dependencies": {"underscore": "1.x", "htmlparser2": "1.x", "cheerio-soupselect": "0.0.x"}, "_defaultsLoaded": true, "devDependencies": {"vows": "0.5.x", "should": "0.x", "request": "2.x", "coffee-script": "1.1.x", "diff_match_patch": "0.1.x"}, "_engineSupported": true}, "0.3.2": {"name": "cheerio", "version": "0.3.2", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper"], "author": {"url": "mattmueller.me", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio@0.3.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "a831215d3d204d8016f9d41a8fcddea1f7d9d3c8", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.3.2.tgz", "integrity": "sha512-C86lfS9sZJq/VFlCmXZM0Qop1VuHGNgxy2dXeqhjfewME/mTfBYjHh2eUmExjq/CkFJdVienwu0QvMZksU1D6A==", "signatures": [{"sig": "MEUCIEURewYq9uPOzb1gvNZzPwa22tJfHgk6kdRNqe6/VMR3AiEA6XtABWtNlrWQlBTdZkWzTyN6NR1Dxq3LVIPsm3V0poE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.4.11"}, "scripts": {"test": "coffee -o lib/ src/ && vows tests/test.cheerio.coffee --spec", "prepublish": "coffee -o lib/ src/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/MatthewMueller/cheerio.git", "type": "git"}, "_npmVersion": "1.0.104", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "_nodeVersion": "v0.4.9-pre", "dependencies": {"underscore": "1.x", "htmlparser2": "1.x", "cheerio-soupselect": "0.0.x"}, "_defaultsLoaded": true, "devDependencies": {"vows": "0.5.x", "should": "0.x", "request": "2.x", "coffee-script": "1.1.x", "diff_match_patch": "0.1.x"}, "_engineSupported": false}, "0.4.0": {"name": "cheerio", "version": "0.4.0", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper"], "author": {"url": "mattmueller.me", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio@0.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "ff21f86f676dc1dddcd3328697a4d328751885c6", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.4.0.tgz", "integrity": "sha512-311UtAokK4VnPLygVwMSTViLBDNjIQBmc77Al3NIa0K8qHORS+qh2VmbOZxrder4xL7QcAcLoiREjXY0at5YDA==", "signatures": [{"sig": "MEYCIQD2mFkz8uUt8IVs8LEyb3YwuTLIFuMSV+STrU/SaZ9AaAIhALbAOaT/y2dJj4Mi5y5ImkosJRBD07Th3CNzcPGmF3JG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.4.7"}, "scripts": {"test": "coffee -o lib/ src/ && vows tests/test.cheerio.coffee --spec", "prepublish": "coffee -o lib/ src/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/MatthewMueller/cheerio.git", "type": "git"}, "_npmVersion": "1.0.103", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "_nodeVersion": "v0.4.11", "dependencies": {"underscore": "1.x", "htmlparser2": "2.x", "cheerio-soupselect": "0.0.x"}, "_defaultsLoaded": true, "devDependencies": {"mocha": "0.x", "should": "*", "coffee-script": "1.1.x"}, "_engineSupported": true}, "0.4.1": {"name": "cheerio", "version": "0.4.1", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper"], "author": {"url": "mattmueller.me", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio@0.4.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "13e8d9e1283e75174ce984c686b86905b9221799", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.4.1.tgz", "integrity": "sha512-/15mBhctdwEG8Za0AawyVdiuwv1edXY0ZHm5Ah6SX6BqwbrJ+8rSoPapxXCGZDl9P2nJYnlkDwx6uS6OJhfU1A==", "signatures": [{"sig": "MEQCIDHEo+ipMiCQYHLuz8N46KQTzp1PQA4RamcyuQKFMq8CAiBKT2jD8zfWhOf7CLTk8vFCaP9/Dw0lQ8zs1k69gKLqZg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.4.7"}, "scripts": {"test": "coffee -o lib/ src/ && vows tests/test.cheerio.coffee --spec", "prepublish": "coffee -o lib/ src/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/MatthewMueller/cheerio.git", "type": "git"}, "_npmVersion": "1.0.103", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "_nodeVersion": "v0.4.11", "dependencies": {"underscore": "1.x", "htmlparser2": "2.x", "cheerio-soupselect": "0.0.x"}, "_defaultsLoaded": true, "devDependencies": {"mocha": "0.x", "should": "*"}, "_engineSupported": true}, "0.4.2": {"name": "cheerio", "version": "0.4.2", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper"], "author": {"url": "mattmueller.me", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio@0.4.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "89168636fd83a4acc71178722a085e4857c3eac7", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.4.2.tgz", "integrity": "sha512-9JiGOC3rAv8MF8GiPUbHrtfp/IuYjBSn+1SH9h/WCGT4tNUBTkL1n+26V4QFZriJFEYGbKPJ6AeykPt4DJO6pg==", "signatures": [{"sig": "MEUCIQCuWHbMB4XDZGN0As2Gxg/0qfcLgtvTar0zfIAYwgC4RgIgESpn+w5abThZ+amrBRNYFI77VxsKAQRGqbVNOI6z+AY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.4.7"}, "scripts": {"test": "coffee -o lib/ src/ && vows tests/test.cheerio.coffee --spec", "prepublish": "coffee -o lib/ src/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/MatthewMueller/cheerio.git", "type": "git"}, "_npmVersion": "1.0.103", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "_nodeVersion": "v0.4.11", "dependencies": {"underscore": "1.x", "htmlparser2": "2.x", "cheerio-soupselect": "*"}, "_defaultsLoaded": true, "devDependencies": {"mocha": "0.x", "should": "*"}, "_engineSupported": true}, "0.5.0": {"name": "cheerio", "version": "0.5.0", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper"], "author": {"url": "mattmueller.me", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio@0.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "b292d09676a3e99d2173233cb316b22428b8bf43", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.5.0.tgz", "integrity": "sha512-dhF9whQQec7h2V1mMgE8BhA+fqNZcEqlctLVEJ6DIJzdKEHZV2C2Q5r5KztvvPIUla+j2OJY8LlnJlv1/M/9vg==", "signatures": [{"sig": "MEUCIFkG58PPfY0QwKb6JwO4N9HMoAfRlka/o2nSvZ0D0SVZAiEA2kAnVBSUDrUJyHMmEptb0RUjBj0aZiSlwHxo9/IEHFk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.4.7"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/MatthewMueller/cheerio.git", "type": "git"}, "_npmVersion": "1.0.103", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "_nodeVersion": "v0.4.11", "dependencies": {"underscore": "1.x", "htmlparser2": "2.x", "cheerio-soupselect": "*"}, "_defaultsLoaded": true, "devDependencies": {"mocha": "0.x", "should": "*", "coffee-script": "*"}, "_engineSupported": true}, "0.5.1": {"name": "cheerio", "version": "0.5.1", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper"], "author": {"url": "mattmueller.me", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio@0.5.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "09832bdf5956ae4df9c29aea68a7585b76f6ca16", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.5.1.tgz", "integrity": "sha512-VJ8gv/16+pX5rOShb/OGFVpb1lk7dkqkiQCNrh3pzA6xvURMYlT36w4dRedlh9Rpd1SIL8/XKvOLGUDSzZkHJQ==", "signatures": [{"sig": "MEQCIHjCYyOk0qCVXjUBBBkdhufDhjcfC5dxHEAlqTtcIkTHAiBQ7vRrSi3a7ArQ0WxZSbLNCK5o0CB0YAoEViERZeboTA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.4.7"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/MatthewMueller/cheerio.git", "type": "git"}, "_npmVersion": "1.0.103", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "_nodeVersion": "v0.4.11", "dependencies": {"underscore": "1.x", "htmlparser2": "2.x", "cheerio-soupselect": "*"}, "_defaultsLoaded": true, "devDependencies": {"mocha": "0.x", "should": "*", "coffee-script": "*"}, "_engineSupported": true}, "0.5.2": {"name": "cheerio", "version": "0.5.2", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper"], "author": {"url": "mattmueller.me", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio@0.5.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "aa45ee563f0cbbe7982f69491d2a7f190a5d48a7", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.5.2.tgz", "integrity": "sha512-+bnXtBNbmYWx65bXFLaxriUwBg9MB4pZ8Gy+9csPEM9fVNdAV7yq8FCWxHP4OorlXE66mWYEYwPkEgatIE41Yg==", "signatures": [{"sig": "MEUCIC4QQon3HY7cQojwL3JFsX63QkfD0f3Rq40Mhc1pvLcTAiEA/uaH86WTquosEXqckbRwBG9H5G+v3BZCVVGulRsDGH4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.4.7"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/MatthewMueller/cheerio.git", "type": "git"}, "_npmVersion": "1.0.103", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "_nodeVersion": "v0.4.11", "dependencies": {"underscore": "1.x", "htmlparser2": "2.x", "cheerio-soupselect": "*"}, "_defaultsLoaded": true, "devDependencies": {"mocha": "0.x", "should": "*", "coffee-script": "*"}, "_engineSupported": true}, "0.6.0": {"name": "cheerio", "version": "0.6.0", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper"], "author": {"url": "mattmueller.me", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio@0.6.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "b763cf4e2f6cb600f419b9d3b540dc789178e049", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.6.0.tgz", "integrity": "sha512-3g+OpbIJ7qwREcedKr54wRs4sQpGLPe72PzLTCIJODprquJQf+2IIZC1PzG2GyYThtFnazSuN3nYzahMzZ3e/g==", "signatures": [{"sig": "MEUCIQDTPZv5jVWvCfQah4ATUqfWvdq6Ia6dH4rJ2HVqBke8WwIgbG8tBHSBwNpfnjK0Tk3yqu+qU4Irz60JeGiIQBY/ykc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.4.7"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/MatthewMueller/cheerio.git", "type": "git"}, "_npmVersion": "1.0.103", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "_nodeVersion": "v0.4.11", "dependencies": {"underscore": "1.x", "htmlparser2": "2.0.1 - 2.2.2", "cheerio-soupselect": "*"}, "_defaultsLoaded": true, "devDependencies": {"mocha": "0.x", "should": "*", "coffee-script": "*"}, "_engineSupported": true}, "0.6.1": {"name": "cheerio", "version": "0.6.1", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper"], "author": {"url": "mattmueller.me", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio@0.6.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "65f4daabb89597186b51853f1a2ff841939f75f9", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.6.1.tgz", "integrity": "sha512-IdwVS+LRVZECYeh/8Wgly7mMTROp3yFS+BMrrHsoFyH65zXReYhY86SbE0abq/BxPgDob01wC6H/YH/tteSKzg==", "signatures": [{"sig": "MEUCIErjRjcv9dZU7Kt8fexb2vub2K4qXJA+LnPUillFojQaAiEAxhKNuvGiSnwrNVF/elaV6xwqbBbons31opsxJtx9Yxc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.4.7"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/MatthewMueller/cheerio.git", "type": "git"}, "_npmVersion": "1.0.103", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "_nodeVersion": "v0.4.11", "dependencies": {"underscore": "1.x", "htmlparser2": "2.0.1 - 2.2.2", "cheerio-soupselect": "*"}, "_defaultsLoaded": true, "devDependencies": {"mocha": "0.x", "should": "*", "coffee-script": "*"}, "_engineSupported": true}, "0.6.2": {"name": "cheerio", "version": "0.6.2", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper"], "author": {"url": "mattmueller.me", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio@0.6.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "9e6388f8b291b933733f2751ce3fdb87de81b099", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.6.2.tgz", "integrity": "sha512-GCWrnUnok864se9r1oPHbrFkIO6HZvpBgUpG8EvLMTWxmq+2t12Vkxg3kystHc9WnrlhtqOh4WDJmTUMPEkPXw==", "signatures": [{"sig": "MEUCIQCAT5nk5yvqA/tTMt0x8nlj0uCf2KEm76y142Cqt0kv1gIgDGrPqqA3gXfj6UeflsvjzQGP4iowj4Td8FIHaANdvEY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.4.7"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/MatthewMueller/cheerio.git", "type": "git"}, "_npmVersion": "1.0.103", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "_nodeVersion": "v0.4.11", "dependencies": {"underscore": "1.x", "htmlparser2": "2.0.1 - 2.2.2", "cheerio-soupselect": "*"}, "_defaultsLoaded": true, "devDependencies": {"mocha": "0.x", "should": "*", "coffee-script": "*"}, "_engineSupported": true}, "0.7.0": {"name": "cheerio", "version": "0.7.0", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper"], "author": {"url": "mattmueller.me", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio@0.7.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "b98cb806b693396411a450f58b39240abe9833dc", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.7.0.tgz", "integrity": "sha512-f1rtQxWeXugeR3/aZIWXHXgLJ+8THXBgAwPQnbTsWTmgiKzSQxl49ci5TXxRNTCudxb4Y046oPUH0y1v6KXsTg==", "signatures": [{"sig": "MEYCIQCwqF/2qEeSG1DsQmPE0nR+D0SM6bn/m/6klDuQqA8TjwIhAKav4hd2sj33Ob8YGbAyIH3XebgDe2LfsfTu/qRHtfmn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.4.7"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/MatthewMueller/cheerio.git", "type": "git"}, "_npmVersion": "1.1.15", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "_nodeVersion": "v0.7.7", "dependencies": {"underscore": "*", "htmlparser2": "2.0.1 - 2.2.2", "cheerio-soupselect": "*"}, "_defaultsLoaded": true, "devDependencies": {"mocha": "*", "expect.js": "*", "coffee-script": "*"}, "_engineSupported": true, "optionalDependencies": {}}, "0.8.0": {"name": "cheerio", "version": "0.8.0", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper"], "author": {"url": "mattmueller.me", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio@0.8.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "2f2222913a40555f0c353a82bd668db7cd1c6d22", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.8.0.tgz", "integrity": "sha512-WeG6uDXVoTo7kCczQ5GXOpM55YgS0Pib2uAnsiCu6tVb3ULtnSrCQNoQDi6z6iVQ8mTH5Y9wUsaa5mU5/gmnfw==", "signatures": [{"sig": "MEYCIQCM6vhlR7sRIY0agp66ktggwK1RsSp1kz6ly/gND13b+gIhAICrCQkEXIbTQCjGT+t71l0wUAtuuWOm2kK9iDogFbER", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.6"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/MatthewMueller/cheerio.git", "type": "git"}, "_npmVersion": "1.1.16", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "_nodeVersion": "v0.6.6", "dependencies": {"underscore": "*", "htmlparser2": "2.x", "cheerio-select": "*"}, "_defaultsLoaded": true, "devDependencies": {"mocha": "*", "expect.js": "*", "coffee-script": "*"}, "_engineSupported": true, "optionalDependencies": {}}, "0.8.1": {"name": "cheerio", "version": "0.8.1", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper"], "author": {"url": "mattmueller.me", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio@0.8.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "7e900d93fd45296c3529430c0257b147a37f9dce", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.8.1.tgz", "integrity": "sha512-S+nnbnXjT49ny1gQ1AUfr13L4hDfC2eTuILms0CnAw9ifyT2ig721mlBxrSs+8hiEb+Bk7orAXj4epnwskZs4w==", "signatures": [{"sig": "MEUCIBG0wS+CZr7hmt5isf3d2ZhRH9R54kPXT7hxe9A4lSkTAiEAvOZ+JJaByCLLA1NjL4TtxoBPr0vZkjwekXlTkW1Xb50=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.6"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/MatthewMueller/cheerio.git", "type": "git"}, "_npmVersion": "1.1.16", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "_nodeVersion": "v0.6.6", "dependencies": {"underscore": "*", "htmlparser2": "2.x", "cheerio-select": "*"}, "_defaultsLoaded": true, "devDependencies": {"mocha": "*", "expect.js": "*", "coffee-script": "*"}, "_engineSupported": true, "optionalDependencies": {}}, "0.8.2": {"name": "cheerio", "version": "0.8.2", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper"], "author": {"url": "mattmueller.me", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio@0.8.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "fbc40a14fb1ed162fa2b5bc58ff8344205063ba9", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.8.2.tgz", "integrity": "sha512-B8en4/wR8B573C5JTZj4NndAX+DX7/uJO5mdfPjyPXVo/snffyq7wS2OI1RPdJm13wdH92ggKUpgbgcGNKaRKA==", "signatures": [{"sig": "MEUCIAXajNKAvOXFyGzfBIJr6d+ARm8YcUmH2eeBCO/TL+CxAiEA5rIzTV0ruqm1XGitSctikxwAtVCvyNNzGRWFL000NE0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.6"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/MatthewMueller/cheerio.git", "type": "git"}, "_npmVersion": "1.1.4", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "_nodeVersion": "v0.6.8", "dependencies": {"entities": "1.x", "underscore": "*", "htmlparser2": "2.x", "cheerio-select": "*"}, "_defaultsLoaded": true, "devDependencies": {"mocha": "*", "expect.js": "*", "coffee-script": "*"}, "_engineSupported": true, "optionalDependencies": {}}, "0.8.3": {"name": "cheerio", "version": "0.8.3", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper"], "author": {"url": "mattmueller.me", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio@0.8.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "94bca93b260024c3dad12d473195a2c8b7cb2467", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.8.3.tgz", "integrity": "sha512-GCVD8ocmDClqEPOC3l752iBt1iIC169RZn+9HZCECbFcVbRUdjDM6HklvEemJ5JERTc3xTRNBb0OoKriG+uK1w==", "signatures": [{"sig": "MEUCIHOpaDeFv+lWnyToNaAS/wxDRCrAg7R6XzJWNO3lEEsrAiEA2uTK7Wdx3z/ZDpUCCzx8ukSUp6kb1cKzGt87YHVnnTo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.6"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/MatthewMueller/cheerio.git", "type": "git"}, "_npmVersion": "1.1.16", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "_nodeVersion": "v0.6.6", "dependencies": {"entities": "0.x", "underscore": "*", "htmlparser2": "2.x", "cheerio-select": "*"}, "_defaultsLoaded": true, "devDependencies": {"mocha": "*", "expect.js": "*", "coffee-script": "*"}, "_engineSupported": true, "optionalDependencies": {}}, "0.9.0": {"name": "cheerio", "version": "0.9.0", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper"], "author": {"url": "mattmueller.me", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio@0.9.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "00bc8f711090de9c2e510e3e40ea59773d5b83d5", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.9.0.tgz", "integrity": "sha512-TvrsLDKqeYrIq01CV3BzZYZ2PHGOJVtKyUxIaysNMvp8hc8HgV8bxTVpYhIRs/kj3WtWNHTdkkVedoRMJon7lw==", "signatures": [{"sig": "MEYCIQDxNUzceuf7uQN+5Eo0EOmNrlKvflZR0ijHrv2uaiz6sAIhAPyJVYTpHAA9H666OV9s12Cmp/PaIPLPI+6e61XCamGq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.6"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/MatthewMueller/cheerio.git", "type": "git"}, "_npmVersion": "1.1.4", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "_nodeVersion": "v0.6.8", "dependencies": {"entities": "0.x", "underscore": "*", "htmlparser2": "2.x", "cheerio-select": "*"}, "_defaultsLoaded": true, "devDependencies": {"mocha": "*", "expect.js": "*"}, "_engineSupported": true, "optionalDependencies": {}}, "0.9.1": {"name": "cheerio", "version": "0.9.1", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper"], "author": {"url": "mattmueller.me", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio@0.9.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "57ead39e9e0c23ecc92dc18e55eec8ceb0cb241a", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.9.1.tgz", "integrity": "sha512-HE37dJfZ7C/01LtHDY9n9wwKhaHugmzJsVSP82rFOg88ro0fJe+JmDiJMl1Oqr+CPNyHCXA0FK6BYoTdYrkiPA==", "signatures": [{"sig": "MEQCIGRCGx9GXi9PM7wISOX7UpOsNgDq6cSmMquh2dcA1Ts+AiAaVWkURVmolHE58IKWHT/v1AZlEp/V2bP74FpwImsK3Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.6"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/MatthewMueller/cheerio.git", "type": "git"}, "_npmVersion": "1.1.4", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "_nodeVersion": "v0.6.8", "dependencies": {"entities": "0.x", "underscore": "*", "htmlparser2": "2.x", "cheerio-select": "*"}, "_defaultsLoaded": true, "devDependencies": {"mocha": "*", "expect.js": "*"}, "_engineSupported": true, "optionalDependencies": {}}, "0.9.2": {"name": "cheerio", "version": "0.9.2", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper"], "author": {"url": "mattmueller.me", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio@0.9.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "8cabe74aecc245dbafe03470e2decad87d133923", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.9.2.tgz", "integrity": "sha512-uFsXmFJsogiuMZ5Chv9ufGVx0BzF1fow0MQVGwHwhYCJ764pmWTQt/flOuykOIMiZrdESs+gqP7oVJw5w0jBmw==", "signatures": [{"sig": "MEQCIGXF+in54xeVqezSxE375X5lztXAog36gbCHbbAuZ63aAiB+tP+XfuCqqIxbp/ATAF7n1+Ql3Yv2bLPCj0JvUtQjxA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.6"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/MatthewMueller/cheerio.git", "type": "git"}, "_npmVersion": "1.1.4", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "_nodeVersion": "v0.6.8", "dependencies": {"entities": "0.x", "underscore": "*", "htmlparser2": "2.x", "cheerio-select": "*"}, "_defaultsLoaded": true, "devDependencies": {"mocha": "*", "expect.js": "*"}, "_engineSupported": true, "optionalDependencies": {}}, "0.10.0": {"name": "cheerio", "version": "0.10.0", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper"], "author": {"url": "mattmueller.me", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio@0.10.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "01d102adc737758d94aaa408c05788550e6eefcb", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.10.0.tgz", "integrity": "sha512-Q5/vJ7xOEZ63fntI8vrye+Orl4AZ2ALQqgCpcrFAnk0ShbDUaxCYh6L1jkZ1n3zOyg0RYyWexeLtGDo2Wb+dhA==", "signatures": [{"sig": "MEYCIQC8Jkgr0tf9bumzO0SXHGfOCmokq/8DeEUio5fOikmulwIhAOvsHCzW4WnYG75lFqOKflmMzBZA/f+jXMJdM54kWeMr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.6"}, "scripts": {"test": "make test"}, "repository": {"url": "git://github.com/MatthewMueller/cheerio.git", "type": "git"}, "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "dependencies": {"entities": "0.x", "underscore": "*", "htmlparser2": "2.x", "cheerio-select": "*"}, "devDependencies": {"mocha": "*", "expect.js": "*"}}, "0.10.1": {"name": "cheerio", "version": "0.10.1", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper"], "author": {"url": "mattmueller.me", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio@0.10.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "465895fed38234617c9549a60d4c2296d38fba3d", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.10.1.tgz", "integrity": "sha512-9AimNi1Co0zTVXtqqMmNNTi8vrcxXqkXykou1CIIxQQQ5P4e3InW22r2poIBUBvz7kgpCFqiKbpckAiGhh+g6g==", "signatures": [{"sig": "MEUCIDq5Pufo/qtx8qZkonTFRzVeHB1GHagsNS3T/kj5XXKRAiEAyKUeEmaei5f6bw4gBY13f7MwagQY2CVW/NmQBZeYRbk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.6"}, "scripts": {"test": "make test"}, "repository": {"url": "git://github.com/MatthewMueller/cheerio.git", "type": "git"}, "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "dependencies": {"entities": "0.x", "underscore": "*", "htmlparser2": "2.x", "cheerio-select": "*"}, "devDependencies": {"mocha": "*", "expect.js": "*"}}, "0.10.2": {"name": "cheerio", "version": "0.10.2", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper"], "author": {"url": "mattmueller.me", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio@0.10.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "933c9f5d4c4ccce73b58dca8caef51db523be479", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.10.2.tgz", "integrity": "sha512-SO<PERSON>ip15khZBrB9QieIzL6AAJ3PSAU+kXhq7l9YWf0drXwDjrK5E7Gqtyn74AocoC0OtcbnVyaXktz7Owj1nWsw==", "signatures": [{"sig": "MEUCIE9uA35EPEL2VYkisxbyf34DsINAUJnjvCFdKCNNix4PAiEAyZ2lH8eGp3L4YoYaRCD/XCyyxO8pSctJ/Zdhea3GeGI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.6"}, "scripts": {"test": "make test"}, "repository": {"url": "git://github.com/MatthewMueller/cheerio.git", "type": "git"}, "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "dependencies": {"entities": "0.x", "underscore": "*", "htmlparser2": "2.x", "cheerio-select": "*"}, "devDependencies": {"mocha": "*", "expect.js": "*"}}, "0.10.3": {"name": "cheerio", "version": "0.10.3", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper"], "author": {"url": "mattmueller.me", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio@0.10.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "50103d42e30ef4a1bf552001d684ecee70827b67", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.10.3.tgz", "integrity": "sha512-z18iEDVDRpNeLPCGDmDRlex8iLpYxV5KyQz3JHGpzdVX1tSRvtBfwWELW65Ae/FIOGO/YKxKERhz/0n5RI0m7A==", "signatures": [{"sig": "MEYCIQDbfkWViHPJS4l02ri36IU5CbI15rCPW3ti6S+/dlTnfwIhAOlFZxlZYYZoiMPKQrpS4wxgMw4XSqeQtMwlSFZysLis", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.6"}, "scripts": {"test": "make test"}, "repository": {"url": "git://github.com/MatthewMueller/cheerio.git", "type": "git"}, "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "dependencies": {"entities": "0.x", "underscore": "*", "htmlparser2": "2.x", "cheerio-select": "*"}, "devDependencies": {"mocha": "*", "expect.js": "*"}}, "0.10.4": {"name": "cheerio", "version": "0.10.4", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper"], "author": {"url": "mattmueller.me", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio@0.10.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "70fc0684142aaf890fbacddd6ad761b6f37301d6", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.10.4.tgz", "integrity": "sha512-kgUcTFFNFnsoO03LgKnE7x8msboLkOOQ2ol54coZfEQ18MAs1CztT3PGECfOLCzF8yD2AL2Vw26DfdK3Xq+y3Q==", "signatures": [{"sig": "MEUCIAv4SizJuRmFlJltNCDpiPGp6e5QPLRpVpNxm7GQuaHpAiEAlkp96hOlihrsTJg8pX9/T5VMVHTsjmXQd/xhWzu8TZI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.6"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/MatthewMueller/cheerio.git", "type": "git"}, "_npmVersion": "1.1.65", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "dependencies": {"entities": "0.x", "underscore": "*", "htmlparser2": "2.x", "cheerio-select": "*"}, "devDependencies": {"mocha": "*", "expect.js": "*"}}, "0.10.5": {"name": "cheerio", "version": "0.10.5", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper"], "author": {"url": "mattmueller.me", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio@0.10.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "cc67d29453c8837c40c910b4a4332316201b586d", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.10.5.tgz", "integrity": "sha512-2hEK5VlyAHlw1p2Wjlqfd/J/dX8VuFYDcx89OwtDDDbbUSqlm2+B4XOvM8xk7GvYLtA7WkAzcj+DnXb0JBonDQ==", "signatures": [{"sig": "MEYCIQCGAlb/oVdz6tM8QOujf3oj3Qx2YzgShIrUUt0KxHjwzgIhAISlUX8V9FnhFyxNjrjjuyJdU7yCxYMwFfpui3O2Y5oU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.6"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/MatthewMueller/cheerio.git", "type": "git"}, "_npmVersion": "1.1.65", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "dependencies": {"entities": "0.x", "underscore": "~1.4", "htmlparser2": "2.x", "cheerio-select": "*"}, "devDependencies": {"mocha": "*", "expect.js": "*"}}, "0.10.6": {"name": "cheerio", "version": "0.10.6", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper"], "author": {"url": "mattmueller.me", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio@0.10.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "4238fb7a83327e6df54422232f4635e6c4dbc22d", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.10.6.tgz", "integrity": "sha512-ajgPa7TgPy199hr0psoSFpHmRbU9FGvfFUOMMXbwqDvvk9dTVEtldeuDvdm8UVCwfjD4yquumeGpCDVGUoubaA==", "signatures": [{"sig": "MEUCIQCQ8mEGqyb2cgkT6tiQo8v/fhSjkafQhGBjCN5mwv/VKwIgQhG5AS9nP2iJZPzzzNBZXlYqjVEqp/0n6bbxdLEwCqA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.6"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/MatthewMueller/cheerio.git", "type": "git"}, "_npmVersion": "1.1.65", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "dependencies": {"entities": "0.x", "underscore": "~1.4", "htmlparser2": "2.x", "cheerio-select": "*"}, "devDependencies": {"mocha": "*", "expect.js": "*"}}, "0.10.7": {"name": "cheerio", "version": "0.10.7", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper"], "author": {"url": "mattmueller.me", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio@0.10.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "fe10dcadf629354fb1b9568d9cc0695634fbc41a", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.10.7.tgz", "integrity": "sha512-RKGXGtktNqsYQVW248wDlOhbADrv92ySS7Dk8qbgeylhcxY+AxX1gWWIR6kqVN9oL9fhqx27Dn1zPDuKWrpUUw==", "signatures": [{"sig": "MEQCIAlJ5FL5yA8wxjyUS0qWT4N6FkuQ/UMckX51XrW2eANdAiBnzF/Q1XVCEGMiaNjSW7XNR2/vphPqb/A6Yvbe+WyTfQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.6"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/MatthewMueller/cheerio.git", "type": "git"}, "_npmVersion": "1.1.65", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "dependencies": {"entities": "0.x", "underscore": "~1.4", "htmlparser2": "2.x", "cheerio-select": "*"}, "devDependencies": {"mocha": "*", "expect.js": "*"}}, "0.10.8": {"name": "cheerio", "version": "0.10.8", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper"], "author": {"url": "mattmueller.me", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio@0.10.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "ece5ad0c8baa9b9adc87394bbdb1c68bc4552ba0", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.10.8.tgz", "integrity": "sha512-/rDdcRIPVkXblUMxhRe9r/sANqG5T7App+V3LJby4GkN+0PrWmFwpE1k4FfbFpiJAOAy7vxUzi4sNX5in0nL2w==", "signatures": [{"sig": "MEUCIQDH4P5ZItc6RqjTFkjyB4t2+lm+Wf80GmXtJgNZoKxruwIgUMEQNoWvcQJEC4AwNSKARkwwpb8lMubvIxcPUJe6YQ0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "engines": {"node": ">= 0.6"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/MatthewMueller/cheerio.git", "type": "git"}, "_npmVersion": "1.2.12", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "dependencies": {"entities": "0.x", "underscore": "~1.4", "htmlparser2": "2.x", "cheerio-select": "*"}, "devDependencies": {"mocha": "*", "expect.js": "*"}}, "0.11.0": {"name": "cheerio", "version": "0.11.0", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper"], "author": {"url": "mattmueller.me", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio@0.11.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "62549889403ebc7a608e3fba64c1822117e76b51", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.11.0.tgz", "integrity": "sha512-D115H8ceXoRjpkqd7badz2miKtrfuLSv9RFi2fheMmjg41OkumZaqEZGnvifZOOQwv/jK98ww1r6O5gG2s8nbg==", "signatures": [{"sig": "MEYCIQDJ6t6wvJAyzHcbp7QbuCy9Fu4LC8hBrarQ1zroQwMUpAIhAPVOEUBe6jPgGnD3K9mcO+WaXhQ3cuQ2OYD84JY2s/c6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.6"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/MatthewMueller/cheerio.git", "type": "git"}, "_npmVersion": "1.1.71", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "dependencies": {"entities": "0.x", "underscore": "~1.4", "htmlparser2": "2.x", "cheerio-select": "*"}, "devDependencies": {"mocha": "*", "expect.js": "*"}}, "0.12.0": {"name": "cheerio", "version": "0.12.0", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper"], "author": {"url": "mattmueller.me", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio@0.12.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "a4ce0cdc140df45b5bd6856345cbd7192369ddfc", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.12.0.tgz", "integrity": "sha512-xKgh+D09J4kaZS33eAyADzI740hqtyn2Cqe27sFX1C/U2ysLR4ZfqHL2GmSyhW1bph0XiNZmiLvBVQ3Egl9I/g==", "signatures": [{"sig": "MEUCIQDIai+1KR9/n7koknG2DUjRPGG7WOZ4JQhpg3YlxjGCXwIgPUswlvr/XqZvg+QAcCszUGKMe+uFfYB+wl1WPbq21/Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.6"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/MatthewMueller/cheerio.git", "type": "git"}, "_npmVersion": "1.1.71", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "dependencies": {"entities": "0.x", "underscore": "~1.4", "htmlparser2": "3.1.1", "cheerio-select": "*"}, "devDependencies": {"mocha": "*", "expect.js": "*"}}, "0.12.1": {"name": "cheerio", "version": "0.12.1", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper"], "author": {"url": "mattmueller.me", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio@0.12.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "bugs": {"url": "https://github.com/MatthewMueller/cheerio/issues"}, "dist": {"shasum": "82cf2b7e9a260f216142cf3c41e94289a3ea4aa3", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.12.1.tgz", "integrity": "sha512-Xx/1AjtKXtH0rVvvJyNACep91tRugsnBZHkoPPlmZJ7VlfNak3ZJVMiPud2Yp/PUN9aA+VMtqS7kqb+OTueW8Q==", "signatures": [{"sig": "MEUCIAsW3BGI4VgolbquUgHNYp6FGTD5q4IV+xbopg9ZtjIQAiEAiSdYyz58awLIUTKdv1vdVK+maBuRWWuRe8Bc+y/BohA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "engines": {"node": ">= 0.6"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/MatthewMueller/cheerio.git", "type": "git"}, "_npmVersion": "1.3.2", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "dependencies": {"entities": "0.x", "underscore": "~1.4", "htmlparser2": "3.1.4", "cheerio-select": "*"}, "devDependencies": {"mocha": "*", "expect.js": "*"}}, "0.12.2": {"name": "cheerio", "version": "0.12.2", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper"], "author": {"url": "mattmueller.me", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio@0.12.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "bugs": {"url": "https://github.com/MatthewMueller/cheerio/issues"}, "dist": {"shasum": "d9908e29679e6d1b501c2cfe0e4ada330ea278c7", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.12.2.tgz", "integrity": "sha512-hG53NQAjZ3V7wV7x6jeaQ91Fm15ZxYVVIbaXpOF18w79kVubCYU+i4TdMwKUnaeWupMUYj33x0srpLpDxMEi5A==", "signatures": [{"sig": "MEQCIEyHDJjB+SE0WMngekgzyGoWFtMPqj87j3Ql9wiFzYhvAiAiLsJZRhof0Hy7OQsYkbpSM92K0m0JY19OBcnFL+MuFg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "engines": {"node": ">= 0.6"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/MatthewMueller/cheerio.git", "type": "git"}, "_npmVersion": "1.3.2", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "dependencies": {"entities": "0.x", "underscore": "~1.4", "htmlparser2": "3.1.4", "cheerio-select": "*"}, "devDependencies": {"mocha": "*", "expect.js": "*"}}, "0.12.3": {"name": "cheerio", "version": "0.12.3", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper"], "author": {"url": "mattmueller.me", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio@0.12.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "bugs": {"url": "https://github.com/MatthewMueller/cheerio/issues"}, "dist": {"shasum": "8eb05ace0a3fc72d9d9ce0b5d364fe8bb565d7fa", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.12.3.tgz", "integrity": "sha512-R/JfGDYy2Q7vvwojeyozDf1uDIb1WCwAqs7pD52qR4diN8Vp6ezjPpp1XFsnSSOFLCwXdm5ooj2NEYqylU/8Gw==", "signatures": [{"sig": "MEYCIQDnWogLH6DjWyr7QgBp0WjOT6CxoYhXt8qAC132qqJAFgIhAMebdHYDj1l046SYxjhKXE5RNj0m13JSrtsr114SZ0XV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "engines": {"node": ">= 0.6"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/MatthewMueller/cheerio.git", "type": "git"}, "_npmVersion": "1.3.2", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "dependencies": {"entities": "0.x", "underscore": "~1.4", "htmlparser2": "3.1.4", "cheerio-select": "*"}, "devDependencies": {"mocha": "*", "expect.js": "*"}}, "0.12.4": {"name": "cheerio", "version": "0.12.4", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper"], "author": {"url": "mattmueller.me", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio@0.12.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/MatthewMueller/cheerio", "bugs": {"url": "https://github.com/MatthewMueller/cheerio/issues"}, "dist": {"shasum": "c199626e9e1eb0d4233a91a4793e7f8aaa69a18b", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.12.4.tgz", "integrity": "sha512-XZStQ+OVs2AcUeMdpBUI51tGN0F5Bmu0EQ+5PC8RI4yd+MD0DrgIgP5ejuK8j5TRtVinhZbQrwbCIU07EN7exQ==", "signatures": [{"sig": "MEYCIQDggnekQbxuwrtqsyBeEkR6kf2YcBkmghBKDaEBuHIWNgIhAKjGMl5FjxbGd5U1/AUQXgbDOvw1Lojhi113gi7DJAKr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "engines": {"node": ">= 0.6"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/MatthewMueller/cheerio.git", "type": "git"}, "_npmVersion": "1.3.13", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "dependencies": {"entities": "0.x", "underscore": "~1.4", "htmlparser2": "3.1.4", "cheerio-select": "*"}, "devDependencies": {"mocha": "*", "jshint": "~2.3.0", "expect.js": "*"}}, "0.13.0": {"name": "cheerio", "version": "0.13.0", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper"], "author": {"url": "mattmueller.me", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio@0.13.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/MatthewMueller/cheerio", "bugs": {"url": "https://github.com/MatthewMueller/cheerio/issues"}, "dist": {"shasum": "44f5112044e0e0148300dd16bf8bbd7755ce65f1", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.13.0.tgz", "integrity": "sha512-W9TnvGuZmuoeI00l71d3nosQZLLwJTNSYRs2tlXJaNN05gOnMoS0ej7d07yC6WVXo7jDYAXacvdaRmLnaQTxLg==", "signatures": [{"sig": "MEUCIQCqV8ZThNBDd3Z5fPmMXLXKmumPdBUr3sRQjfVJlHphTAIgEPC8IKBEeWsl82xGqUnz6guaaIpJjnDzBKdwz+G7cB4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "engines": {"node": ">= 0.6"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/MatthewMueller/cheerio.git", "type": "git"}, "_npmVersion": "1.3.14", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "dependencies": {"entities": "0.x", "CSSselect": "~0.4.0", "underscore": "~1.4", "htmlparser2": "~3.4.0"}, "devDependencies": {"jsdom": "~0.8.9", "mocha": "*", "jshint": "~2.3.0", "benchmark": "~1.0.0", "expect.js": "*"}}, "0.13.1": {"name": "cheerio", "version": "0.13.1", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper"], "author": {"url": "mattmueller.me", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio@0.13.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/MatthewMueller/cheerio", "bugs": {"url": "https://github.com/MatthewMueller/cheerio/issues"}, "dist": {"shasum": "48af1134561b3527f83d9156c4f9a8ebd82b06ec", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.13.1.tgz", "integrity": "sha512-rf91mJeJg8CiTithSnWkxLzU7tlxwEhV2ABumqaYqa/p5yyojI6U4CrilM+UtVWOTtjNE2Y3zEkYMuZdjWJ76g==", "signatures": [{"sig": "MEQCIHdQEY355M81Ww78z3fstJphtJYJsMisnrqqfdI1JPxTAiBvpWgPAq7V/Ju3FHhk1u2cLP/osX8Bs6txtSRmjK6/vw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "engines": {"node": ">= 0.6"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/MatthewMueller/cheerio.git", "type": "git"}, "_npmVersion": "1.3.14", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "dependencies": {"entities": "0.x", "CSSselect": "~0.4.0", "underscore": "~1.5", "htmlparser2": "~3.4.0"}, "devDependencies": {"jsdom": "~0.8.9", "mocha": "*", "jshint": "~2.3.0", "benchmark": "~1.0.0", "expect.js": "*"}}, "0.14.0": {"name": "cheerio", "version": "0.14.0", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper", "parser", "html"], "author": {"url": "mattmueller.me", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio@0.14.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/MatthewMueller/cheerio", "bugs": {"url": "https://github.com/MatthewMueller/cheerio/issues"}, "dist": {"shasum": "209648d501846de95cdca6440f389a7f5c29dc8f", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.14.0.tgz", "integrity": "sha512-dhy1vwNo4ECjX1m8+kgJiAsgorlS197zALuleJ0bhIxhGfHXE8SQUPpKHYSBPtXhYZBPgZ4RCOAUMLjF6kG/ug==", "signatures": [{"sig": "MEYCIQCiNlFR4my3mLS/1tMvqeaxnNhfHr9twTwmS4/gyjVRtQIhAPIm9Yq+K5bXi05kok2Tj29lu350PDUZ6yQnAyb+Joc5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "engines": {"node": ">= 0.6"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/MatthewMueller/cheerio.git", "type": "git"}, "_npmVersion": "1.4.6", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "dependencies": {"lodash": "~2.4.1", "entities": "~1.0.0", "CSSselect": "~0.4.0", "htmlparser2": "~3.7.0"}, "devDependencies": {"jsdom": "~0.8.9", "mocha": "*", "jshint": "~2.3.0", "benchmark": "~1.0.0", "expect.js": "~0.3.1"}}, "0.15.0": {"name": "cheerio", "version": "0.15.0", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper", "parser", "html"], "author": {"url": "mat.io", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio@0.15.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/MatthewMueller/cheerio", "bugs": {"url": "https://github.com/MatthewMueller/cheerio/issues"}, "dist": {"shasum": "8775ec3ab16f4c66195b9cc6797e0c82b51e6b34", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.15.0.tgz", "integrity": "sha512-dQzfM7SxwOFzzjFJNhb11mSvAr3MLDWY3mB55uJWmycJiFzHzlzr7DnEcNLqVhGoi1XthuaHoo/iAe6wi4LiFg==", "signatures": [{"sig": "MEYCIQDrOsq7WxTNzWDmVpOeHZuaioeUOHxwWFhKgsy9Rq39XAIhALhkXPlKdr5mPc/MQTOsJPR30Jog+tLUesbmoqSujB5g", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "engines": {"node": ">= 0.6"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/MatthewMueller/cheerio.git", "type": "git"}, "_npmVersion": "1.4.6", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "dependencies": {"lodash": "~2.4.1", "entities": "~1.0.0", "CSSselect": "~0.4.0", "htmlparser2": "~3.7.0"}, "devDependencies": {"jsdom": "~0.8.9", "mocha": "*", "jshint": "~2.3.0", "benchmark": "~1.0.0", "expect.js": "~0.3.1"}}, "0.16.0": {"name": "cheerio", "version": "0.16.0", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper", "parser", "html"], "author": {"url": "mat.io", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio@0.16.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "david<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/MatthewMueller/cheerio", "bugs": {"url": "https://github.com/MatthewMueller/cheerio/issues"}, "dist": {"shasum": "780ae472692ea85e1e0fe332e806bdba98c16d0a", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.16.0.tgz", "integrity": "sha512-GB+YYcb/2s1HpNXThiHyl1PO5evlkX+avFzggq9/4JZGLGbtMT5FE9GUFjxH+5nObb4Lfu72hAH4lqGljog0Mw==", "signatures": [{"sig": "MEYCIQCpIdZSmwyORfce3L5nErUJYZV0Mn3gKGnQsMZL9BKNggIhANpdM3Q5GSsGxxHcsbutjbobMunB7hLzMTXUDOJk3zVS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "engines": {"node": ">= 0.6"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/MatthewMueller/cheerio.git", "type": "git"}, "_npmVersion": "1.3.25", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "dependencies": {"lodash": "~2.4.1", "entities": "~1.1.1", "CSSselect": "~0.4.0", "htmlparser2": "~3.7.0"}, "devDependencies": {"xyz": "~0.3.0", "jsdom": "~0.8.9", "mocha": "*", "jshint": "~2.3.0", "benchmark": "~1.0.0", "expect.js": "~0.3.1"}}, "0.17.0": {"name": "cheerio", "version": "0.17.0", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper", "parser", "html"], "author": {"url": "mat.io", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio@0.17.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "david<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/MatthewMueller/cheerio", "bugs": {"url": "https://github.com/MatthewMueller/cheerio/issues"}, "dist": {"shasum": "fa5ae42cc60121133d296d0b46d983215f7268ea", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.17.0.tgz", "integrity": "sha512-pakQLw14btS8T4+Q3dgFbe7x447hjsQeEhMJ2kHg8YrOLSmKS0xGl9REnz/eic7dsxS0S9kMRbZcsqrlMAS0dQ==", "signatures": [{"sig": "MEUCIDtElNCSsJy4MplzAWdwSpTBdgbcemVmmC/bnWe7vptZAiEAhFyrWaaCggDva+IrRSwKPAKPQDQP0bfv5IXrNRKnw+E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_shasum": "fa5ae42cc60121133d296d0b46d983215f7268ea", "engines": {"node": ">= 0.6"}, "gitHead": "3210b2287a9fd3b1ec0bcc656092b12c08c2916c", "scripts": {"test": "make test"}, "_npmUser": {"name": "david<PERSON><PERSON>s", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/MatthewMueller/cheerio.git", "type": "git"}, "_npmVersion": "1.4.14", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "dependencies": {"lodash": "~2.4.1", "entities": "~1.1.1", "CSSselect": "~0.4.0", "htmlparser2": "~3.7.2", "dom-serializer": "~0.0.0"}, "devDependencies": {"xyz": "~0.3.0", "jsdom": "~0.8.9", "mocha": "*", "jshint": "~2.3.0", "benchmark": "~1.0.0", "expect.js": "~0.3.1"}}, "0.18.0": {"name": "cheerio", "version": "0.18.0", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper", "parser", "html"], "author": {"url": "mat.io", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio@0.18.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "david<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "jugglinmike", "email": "<EMAIL>"}], "homepage": "https://github.com/cheeriojs/cheerio", "bugs": {"url": "https://github.com/cheeriojs/cheerio/issues"}, "dist": {"shasum": "4e1c06377e725b740e996e0dfec353863de677fa", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.18.0.tgz", "integrity": "sha512-SDCO2EGvIjjPxZiXT8d4NNOr1+sHTNFHp3bFprNYqI6YdUiZfQ5/r8z8YXQhDgplC+bpfT8812624t7iqL0Bqw==", "signatures": [{"sig": "MEQCICH4zzCMmoThhRzm7w7R7annWILd6efxhGU0qihE2MPGAiB7YbS3vBuM6yvz5zs6N8zU4jDZ/BXoxWMH/oJQwKb3DQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_shasum": "4e1c06377e725b740e996e0dfec353863de677fa", "engines": {"node": ">= 0.6"}, "gitHead": "c4f52db9d0e2011a968ba097c85f434f3a05b7f0", "scripts": {"test": "make test"}, "_npmUser": {"name": "jugglinmike", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/cheeriojs/cheerio.git", "type": "git"}, "_npmVersion": "2.1.3", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "_nodeVersion": "0.10.31", "dependencies": {"lodash": "~2.4.1", "entities": "~1.1.1", "CSSselect": "~0.4.0", "htmlparser2": "~3.8.1", "dom-serializer": "~0.0.0"}, "devDependencies": {"xyz": "~0.4.0", "jsdom": "~0.8.9", "mocha": "*", "jshint": "~2.5.1", "istanbul": "~0.2", "benchmark": "~1.0.0", "coveralls": "~2.10", "expect.js": "~0.3.1"}}, "0.19.0": {"name": "cheerio", "version": "0.19.0", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper", "parser", "html"], "author": {"url": "mat.io", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "license": "MIT", "_id": "cheerio@0.19.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "david<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "jugglinmike", "email": "<EMAIL>"}, {"name": "feedic", "email": "<EMAIL>"}], "homepage": "https://github.com/cheeriojs/cheerio", "bugs": {"url": "https://github.com/cheeriojs/cheerio/issues"}, "dist": {"shasum": "772e7015f2ee29965096d71ea4175b75ab354925", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.19.0.tgz", "integrity": "sha512-Fwcm3zkR37STnPC8FepSHeSYJM5Rd596TZOcfDUdojR4Q735aK1Xn+M+ISagNneuCwMjK28w4kX+ETILGNT/UQ==", "signatures": [{"sig": "MEYCIQCevDHE/FoqEFh47CVlp4umHUsw7b/SSmOox6f8PJrQEQIhAOjkSx0/7874FooCXfbON/TbZ9e3No0AegtVIAg/PZyZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_shasum": "772e7015f2ee29965096d71ea4175b75ab354925", "engines": {"node": ">= 0.6"}, "gitHead": "9e3746d391c47a09ad5b130d770c747a0d673869", "scripts": {"test": "make test"}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/cheeriojs/cheerio.git", "type": "git"}, "_npmVersion": "2.7.1", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "_nodeVersion": "1.5.1", "dependencies": {"lodash": "^3.2.0", "entities": "~1.1.1", "css-select": "~1.0.0", "htmlparser2": "~3.8.1", "dom-serializer": "~0.1.0"}, "devDependencies": {"xyz": "~0.5.0", "jsdom": "~0.8.9", "mocha": "*", "jshint": "~2.5.1", "istanbul": "~0.2", "benchmark": "~1.0.0", "coveralls": "~2.10", "expect.js": "~0.3.1"}}, "0.20.0": {"name": "cheerio", "version": "0.20.0", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper", "parser", "html"], "author": {"url": "mat.io", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "license": "MIT", "_id": "cheerio@0.20.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "david<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "jugglinmike", "email": "<EMAIL>"}, {"name": "feedic", "email": "<EMAIL>"}], "homepage": "https://github.com/cheeriojs/cheerio#readme", "bugs": {"url": "https://github.com/cheeriojs/cheerio/issues"}, "dist": {"shasum": "5c710f2bab95653272842ba01c6ea61b3545ec35", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.20.0.tgz", "integrity": "sha512-e5jCTzJc28MWkrLLjB1mu3ks7rDQJLC5y/JMdQkOAEX/dmJk62rC6Xae1yvOO4xyCxLpzcth3jIZ7nypmjQ/0w==", "signatures": [{"sig": "MEQCIBKxTkax3d3T3G/YVlfDT1NMht0B9FeYchqJM5d6cCEwAiBuZ2js/4PkGr7W/ZD57KDHq8GQwLagpK9PnQT5GQh+Og==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "files": ["index.js", "lib"], "_shasum": "5c710f2bab95653272842ba01c6ea61b3545ec35", "engines": {"node": ">= 0.6"}, "gitHead": "c3ec1cd7bff41da0033bdc45375d77844f0f81c0", "scripts": {"test": "make test"}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/cheeriojs/cheerio.git", "type": "git"}, "_npmVersion": "3.6.0", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {"jsdom": "^7.0.2", "lodash": "^4.1.0", "entities": "~1.1.1", "css-select": "~1.2.0", "htmlparser2": "~3.8.1", "dom-serializer": "~0.1.0"}, "devDependencies": {"xyz": "~0.5.0", "mocha": "*", "jshint": "~2.5.1", "istanbul": "~0.2", "benchmark": "~1.0.0", "coveralls": "~2.10", "expect.js": "~0.3.1"}, "optionalDependencies": {"jsdom": "^7.0.2"}}, "0.22.0": {"name": "cheerio", "version": "0.22.0", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper", "parser", "html"], "author": {"url": "mat.io", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "license": "MIT", "_id": "cheerio@0.22.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "david<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "jugglinmike", "email": "<EMAIL>"}, {"name": "feedic", "email": "<EMAIL>"}], "homepage": "https://github.com/cheeriojs/cheerio#readme", "bugs": {"url": "https://github.com/cheeriojs/cheerio/issues"}, "dist": {"shasum": "a9baa860a3f9b595a6b81b1a86873121ed3a269e", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-0.22.0.tgz", "integrity": "sha512-8/MzidM6G/TgRelkzDG13y3Y9LxBjCb+8yOEZ9+wwq5gVF2w2pV0wmHvjfT0RvuxGyR7UEuK36r+yYMbT4uKgA==", "signatures": [{"sig": "MEQCIENUPAs6XBNXV5qZwLNq1gw8XfrNTk8zRofZM7Oijk65AiAHifvv+WbJnlxvp3E8ko8ZIeuRtsoC/62/KqHo2NAN5Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "files": ["index.js", "lib"], "_shasum": "a9baa860a3f9b595a6b81b1a86873121ed3a269e", "engines": {"node": ">= 0.6"}, "gitHead": "35c4917205dca9d08139c95419e2626c0689e38a", "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/cheeriojs/cheerio.git", "type": "git"}, "_npmVersion": "3.10.6", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "_nodeVersion": "6.2.2", "dependencies": {"entities": "~1.1.1", "css-select": "~1.2.0", "lodash.map": "^4.4.0", "htmlparser2": "^3.9.1", "lodash.bind": "^4.1.4", "lodash.pick": "^4.2.1", "lodash.some": "^4.4.0", "lodash.merge": "^4.4.0", "lodash.filter": "^4.4.0", "lodash.reduce": "^4.4.0", "lodash.reject": "^4.4.0", "dom-serializer": "~0.1.0", "lodash.flatten": "^4.2.0", "lodash.foreach": "^4.3.0", "lodash.assignin": "^4.0.9", "lodash.defaults": "^4.0.1"}, "devDependencies": {"xyz": "~0.5.0", "jsdom": "^9.2.1", "mocha": "^2.5.3", "jquery": "^3.0.0", "jshint": "^2.9.2", "istanbul": "^0.4.3", "benchmark": "^2.1.0", "coveralls": "^2.11.9", "expect.js": "~0.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/cheerio-0.22.0.tgz_1471954900169_0.12557715992443264", "host": "packages-12-west.internal.npmjs.com"}}, "1.0.0-rc.1": {"name": "cheerio", "version": "1.0.0-rc.1", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper", "parser", "html"], "author": {"url": "mat.io", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "license": "MIT", "_id": "cheerio@1.0.0-rc.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "david<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "jugglinmike", "email": "<EMAIL>"}, {"name": "feedic", "email": "<EMAIL>"}], "homepage": "https://github.com/cheeriojs/cheerio#readme", "bugs": {"url": "https://github.com/cheeriojs/cheerio/issues"}, "dist": {"shasum": "2af37339eab713ef6b72cde98cefa672b87641fe", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-1.0.0-rc.1.tgz", "integrity": "sha512-f9fNo3JP239BmXoZM2afbybS8CSm9fPyrTSH1UbQCQaaMeL0bRfbpAvYMbKOvy0y9tSho/coEdwBvYWx8hemDg==", "signatures": [{"sig": "MEUCIDjeakvoUNJsjudaO2I/XHHQypbEh+s3Uf5ZYG85T0PoAiEA97Lr8e/ulfjrYjxwtdA9zJij3VTPPFIaiBohc1DE1VQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "files": ["index.js", "lib"], "_shasum": "2af37339eab713ef6b72cde98cefa672b87641fe", "engines": {"node": ">= 0.6"}, "gitHead": "f21ffef971826d1ba64ccbdf96adbc44964d30c5", "scripts": {"test": "make test"}, "_npmUser": {"name": "jugglinmike", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/cheeriojs/cheerio.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "_nodeVersion": "6.10.3", "dependencies": {"lodash": "^4.15.0", "parse5": "^3.0.1", "entities": "~1.1.1", "css-select": "~1.2.0", "htmlparser2": "^3.9.1", "dom-serializer": "~0.1.0"}, "devDependencies": {"xyz": "~1.1.0", "jsdom": "^9.2.1", "mocha": "^3.1.2", "jquery": "^3.0.0", "jshint": "^2.9.2", "istanbul": "^0.4.3", "benchmark": "^2.1.0", "coveralls": "^2.11.9", "expect.js": "~0.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/cheerio-1.0.0-rc.1.tgz_1495918964541_0.5715961558744311", "host": "s3://npm-registry-packages"}}, "1.0.0-rc.2": {"name": "cheerio", "version": "1.0.0-rc.2", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper", "parser", "html"], "author": {"url": "mat.io", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "license": "MIT", "_id": "cheerio@1.0.0-rc.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "david<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "jugglinmike", "email": "<EMAIL>"}, {"name": "feedic", "email": "<EMAIL>"}], "homepage": "https://github.com/cheeriojs/cheerio#readme", "bugs": {"url": "https://github.com/cheeriojs/cheerio/issues"}, "dist": {"shasum": "4b9f53a81b27e4d5dac31c0ffd0cfa03cc6830db", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-1.0.0-rc.2.tgz", "integrity": "sha512-9LDHQy1jHc/eXMzPN6/oah9Qba4CjdKECC7YYEE/2zge/tsGwt19NQp5NFdfd5Lx6TZlyC5SXNQkG41P9r6XDg==", "signatures": [{"sig": "MEUCIQDaxqVZKMdE4QdVGgNS34fG1MNCd8fNSGDe3Mtdbm5DYQIgczH2oBExdl9Vxghpp+aup98Vgi8iNKvCSKv/yVyiBF0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "files": ["index.js", "lib"], "_shasum": "4b9f53a81b27e4d5dac31c0ffd0cfa03cc6830db", "engines": {"node": ">= 0.6"}, "gitHead": "48eae25c93702a29b8cd0d09c4a2dce2f912d1f4", "scripts": {"test": "make test"}, "_npmUser": {"name": "jugglinmike", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/cheeriojs/cheerio.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "_nodeVersion": "6.10.3", "dependencies": {"lodash": "^4.15.0", "parse5": "^3.0.1", "entities": "~1.1.1", "css-select": "~1.2.0", "htmlparser2": "^3.9.1", "dom-serializer": "~0.1.0"}, "devDependencies": {"xyz": "~1.1.0", "jsdom": "^9.2.1", "mocha": "^3.1.2", "jquery": "^3.0.0", "jshint": "^2.9.2", "istanbul": "^0.4.3", "benchmark": "^2.1.0", "coveralls": "^2.11.9", "expect.js": "~0.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/cheerio-1.0.0-rc.2.tgz_1499017014157_0.4666579710319638", "host": "s3://npm-registry-packages"}}, "1.0.0-rc.3": {"name": "cheerio", "version": "1.0.0-rc.3", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper", "parser", "html"], "author": {"url": "mat.io", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "license": "MIT", "_id": "cheerio@1.0.0-rc.3", "maintainers": [{"name": "david<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "feedic", "email": "<EMAIL>"}, {"name": "jugglinmike", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/cheeriojs/cheerio#readme", "bugs": {"url": "https://github.com/cheeriojs/cheerio/issues"}, "dist": {"shasum": "094636d425b2e9c0f4eb91a46c05630c9a1a8bf6", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-1.0.0-rc.3.tgz", "fileCount": 15, "integrity": "sha512-0td5ijfUPuubwLUu0OBoe98gZj8C/AA+RW3v67GPlGOrvxWjZmBXiBCRU+I8VEiNyJzjth40POfHiz2RB3gImA==", "signatures": [{"sig": "MEUCIQDXBQ+eqYef4jlxyA0WA4u99GhTTcie4GaQMTEzPI2qPwIgLKdmWAc3KWG0kHHnqmRQdnxszgNkCu/TGs+9g4UT4Ks=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 111926, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcqSS5CRA9TVsSAnZWagAA7F8P/0wh2IiLcqvx5qd6H4xY\nv/2db56ZXgndPGHe8EnrJZPmxZADBMFI4Kg+L4mV1T57si/f3UGlcp3qscU2\nlzL4yI4GuIxnGWJCz3GdUqfv/KiiT2b6T7E3Ax92BtSTwLK7bdko27MTeo6m\nHOUIIY+oLQGN5P0/UZvcE6u6V+pyMLhbT9xClTOxZGmYAIW8T8oL6Kc+hq6b\ngkvi5ilOJAt+NCoaV7Ml8dDILd8flRN/rccY7/1+b+yaDqIrFjpBgFfBa3LV\nrYvdsRyX21/Os2PJ1ZjH2Yf5vg0YXCDPe6rbBm2lZgLecte3zbNr75tG6QFZ\nq71zivspB1GyGx0K7pdYpoRvwLQBs6WSORJLd5WunpAiy2iWiJ2Mcx7yUJul\n00OrqB0FZvRKx/dE0fDm8sgvoVYSgh3SALK4Q6J4TrALqJWRh6KeDEDDPc1M\n9YJdXb8mR4GIkhGrB04XI2hKDZRHyRaV0bRayKHO001ZlmGSXQp9OoN1EM3V\n3fbacRMIS7QQ/Ua2q9NIVTLADsi0YljT0uG0xMyFNgguJFyR5sOAM+zaOpwj\nis80d/4aiwvEu4m7/Ie8wN5OTgv6g0l/j/B445ELIDY83RyFi7nYfiERgJ5v\n7qsQVpnkdDYiZoWf7I3RXHnLgR7hGORodUTtkocNbcUzYqlHI0p02i80lhmy\ncb9y\r\n=UxM8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "engines": {"node": ">= 0.6"}, "gitHead": "e7c035fd3aedbe449e37d2b1c59fb619375a493a", "scripts": {"test": "make test"}, "_npmUser": {"name": "jugglinmike", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/cheeriojs/cheerio.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "_nodeVersion": "10.15.3", "dependencies": {"lodash": "^4.15.0", "parse5": "^3.0.1", "entities": "~1.1.1", "css-select": "~1.2.0", "htmlparser2": "^3.9.1", "dom-serializer": "~0.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"xyz": "~1.1.0", "jsdom": "^9.2.1", "mocha": "^3.1.2", "jquery": "^3.0.0", "jshint": "^2.9.2", "istanbul": "^0.4.3", "benchmark": "^2.1.0", "coveralls": "^2.11.9", "expect.js": "~0.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/cheerio_1.0.0-rc.3_1554588855915_0.13828001641261256", "host": "s3://npm-registry-packages"}}, "1.0.0-rc.4": {"name": "cheerio", "version": "1.0.0-rc.4", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper", "parser", "html"], "author": {"url": "mat.io", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "license": "MIT", "_id": "cheerio@1.0.0-rc.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "david<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "jugglinmike", "email": "<EMAIL>"}, {"name": "feedic", "email": "<EMAIL>"}], "homepage": "https://github.com/cheeriojs/cheerio#readme", "bugs": {"url": "https://github.com/cheeriojs/cheerio/issues"}, "dist": {"shasum": "a0e60d686400a3017bb2effeca503ce6b4bde83d", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-1.0.0-rc.4.tgz", "fileCount": 16, "integrity": "sha512-cpDpxhXN2XTyN7Zq28jSjjRk/mi6j0PDDmhEdwGN7di0WqHzp8/y+XWyU1KrZ2MZgrB7AwSj7d8AQ5Vi64dBMw==", "signatures": [{"sig": "MEQCICxAxQgh5IRmqAhVmSS9Kqgv4S5MCRKT75aX1KztPizmAiAYTUzqk9GYVjMK+BHABW8MM5VuhOCPL5VtUNWXxGbsww==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 142551, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf4PkmCRA9TVsSAnZWagAAECoP/06fx/2IptZYRcZOZFkv\nq7RS6UdykcIqt+XSOEW88k9YdY6YixGTrjgNsFD6VZsIBEDHetpaPOt38Aa3\nBOMK6tXc8ECoXwkBlQK7TFN1Ht8gYMv+4XiNoBYIPltJXHodgB3TSr7V1Nuf\nSLwJ5h4/k/9EeB+bJt1nKhZN6DAiXuiPYRE0on1s6V6UHntWX3GpJrX+k8jp\n4/5x5w/epTSj/QT7EQmir7QOH2HICDCLvSIP/SsL3Z2AkgLZu0HlVnB5ougQ\nj+1NG1Pf0tmt6wqBK1eOV8eGjxlJrOcvCwgjlzr7k/7du+4eN9xuEgnLNP6F\nR15+MEmM2x5P8X9QMWxDUrpSlfCQWAAr9JdINdV/2WvMVSiOkiHAkZcvaedq\nMHL7IgMnTsZ1Ushtjk8FClP6TtlvGEL8ay8MywUfpcEFUIqTEU7wvGT6ChrG\nbNuWgBUQDDux+gubTQNTKPVyJfpzXMy6x1SypCg0wRV8Xbcg6ysvLq2mqOyX\nxrUp6V6JCBd28491+gG585Nf1uixTRH+4eTrHmJ7vtvGGzLHs68MK3i/c20V\nsS9j7QTz+okKenTCfb3PKdZg1JoNGNLCZbnktHVei05A5vlGvVN2o9Is2Txd\n/7vHvEKTmLS3y/QQ/KGprn4IJFwk1GLXsZBiH+79CHUuhOYbij4OPggBXYYI\n/Sfk\r\n=rMiR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "types": "types/index.d.ts", "engines": {"node": ">= 0.12"}, "gitHead": "52066f3c6943d352b41fc3d5f972a7f8ecad84db", "scripts": {"lint": "npm run lint:es && npm run lint:prettier", "test": "npm run lint && npm run test:mocha && npm run test:types", "format": "npm run format:es && npm run format:prettier", "lint:es": "eslint --ignore-path .prettierignore .", "format:es": "npm run lint:es -- --fix", "build:docs": "jsdoc --configure jsdoc-config.json", "pre-commit": "lint-staged", "test:mocha": "mocha --recursive --reporter dot --parallel", "test:types": "tsd", "lint:prettier": "npm run format:prettier:raw -- --check", "format:prettier": "npm run format:prettier:raw -- --write", "format:prettier:raw": "prettier '**/*.{js,ts,md,json,yml}' --ignore-path .prettierignore"}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "prettier": {"tabWidth": 2, "singleQuote": true}, "repository": {"url": "git://github.com/cheeriojs/cheerio.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "lint-staged": {"*.js": ["prettier --write", "npm run test:lint -- --fix"], "*.{json,md,ts,yml}": ["prettier --write"]}, "_nodeVersion": "15.4.0", "dependencies": {"parse5": "^6.0.0", "entities": "~2.1.0", "domhandler": "^4.0.0", "htmlparser2": "^6.0.0", "cheerio-select": "github:cheeriojs/cheerio-select", "dom-serializer": "~1.2.0", "parse5-htmlparser2-tree-adapter": "^6.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.1", "tsd": "^0.14.0", "xyz": "~4.0.0", "husky": "^4.2.5", "jsdoc": "^3.6.6", "jsdom": "^16.2.2", "mocha": "^8.1.1", "eslint": "^7.10.0", "jquery": "^3.0.0", "prettier": "^2.1.1", "benchmark": "^2.1.4", "coveralls": "^3.0.2", "expect.js": "~0.3.1", "@types/node": "^14.14.10", "lint-staged": "^10.2.2", "eslint-plugin-jsdoc": "^30.6.2", "eslint-config-prettier": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cheerio_1.0.0-rc.4_1608579365580_0.20162319011742458", "host": "s3://npm-registry-packages"}}, "1.0.0-rc.5": {"name": "cheerio", "version": "1.0.0-rc.5", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper", "parser", "html"], "author": {"url": "mat.io", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "license": "MIT", "_id": "cheerio@1.0.0-rc.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "david<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "jugglinmike", "email": "<EMAIL>"}, {"name": "feedic", "email": "<EMAIL>"}], "homepage": "https://github.com/cheeriojs/cheerio#readme", "bugs": {"url": "https://github.com/cheeriojs/cheerio/issues"}, "dist": {"shasum": "88907e1828674e8f9fee375188b27dadd4f0fa2f", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-1.0.0-rc.5.tgz", "fileCount": 16, "integrity": "sha512-yoqps/VCaZgN4pfXtenwHROTp8NG6/Hlt4Jpz2FEP0ZJQ+ZUkVDd0hAPDNKhj3nakpfPt/CNs57yEtxD1bXQiw==", "signatures": [{"sig": "MEQCIGUyR5woEt5lue+STkwTxMkfxv5pzI6JPxDyOSlAOAcTAiB5ScPhmbOGRdld1vJADiSrxKnS5i+xSrhed+UebEh4cg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 142538, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf4Q41CRA9TVsSAnZWagAAGF0P/RPBv1/aiXIx/KPqktiE\npvCqLWW+AVC8CGjbyIFZDPv82uNQ683ohe/NhmcBD1csJ4W3h3ol6jqyaZzd\n9WUpsVHglC92WrkPtRYI0gwK3Csx0gpItQ0E+XIJY27GoZLCe7fsVwXessHL\nNRVsxmig2cjxWKKkhgFTz0OVYinOMNIH0Fou1SiAk0W1UkD4mhJuL83qivoY\nVCyA/KW1uucMjzh15MhQUqAr58wWpatdescLLz/1/yHKBtZrzqmDy9BxQUOx\nASVW/yKH+mkaopFZw1pytM0T5SiTxwFX77mRYtDFMZv9hkqwfpyLHoCbhZrI\nr+Rbyc6ZCGGIxJ9AYL+6gBHoImtDgMRmGpD1lQKZKg5SdhS/VsGpRKjbnvwM\nfV4xGZHT/q/0tHMEdWjWsDmR2TkSWV6Qjw4Ik1sqmnCVNwU+mOGXfAjjrel7\nHjJUbwKUosolEh4Djx4TkBN4hHq63vl0ktJnviAkMM+scxVEewqZBTbXNQ3N\nAfE/c+DyQEmPOX7W9a8SFxtaYQfDOLny9K5ihwUBHgIwBrTBvIzV8Gi+uarL\nwgwhf9YD730TWCLkzf7a806qSclS+lMXGcT1uGPtFt93McloSheFc4w/9nbP\nTlG5/cG9cTgglnTyHn9OkwgILKyjcqbj0UO7yDk3ZcAvYvwRFYo/NMzABya6\nJRtU\r\n=gQZu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "types": "types/index.d.ts", "engines": {"node": ">= 0.12"}, "gitHead": "f386975b627c870e88eac54e32cb593ce5300e99", "scripts": {"lint": "npm run lint:es && npm run lint:prettier", "test": "npm run lint && npm run test:mocha && npm run test:types", "format": "npm run format:es && npm run format:prettier", "lint:es": "eslint --ignore-path .prettierignore .", "format:es": "npm run lint:es -- --fix", "build:docs": "jsdoc --configure jsdoc-config.json", "pre-commit": "lint-staged", "test:mocha": "mocha --recursive --reporter dot --parallel", "test:types": "tsd", "lint:prettier": "npm run format:prettier:raw -- --check", "format:prettier": "npm run format:prettier:raw -- --write", "format:prettier:raw": "prettier '**/*.{js,ts,md,json,yml}' --ignore-path .prettierignore"}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "prettier": {"tabWidth": 2, "singleQuote": true}, "repository": {"url": "git://github.com/cheeriojs/cheerio.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "lint-staged": {"*.js": ["prettier --write", "npm run test:lint -- --fix"], "*.{json,md,ts,yml}": ["prettier --write"]}, "_nodeVersion": "15.4.0", "dependencies": {"parse5": "^6.0.0", "entities": "~2.1.0", "domhandler": "^4.0.0", "htmlparser2": "^6.0.0", "dom-serializer": "~1.2.0", "cheerio-select-tmp": "^0.1.0", "parse5-htmlparser2-tree-adapter": "^6.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.1", "tsd": "^0.14.0", "xyz": "~4.0.0", "husky": "^4.2.5", "jsdoc": "^3.6.6", "jsdom": "^16.2.2", "mocha": "^8.1.1", "eslint": "^7.10.0", "jquery": "^3.0.0", "prettier": "^2.1.1", "benchmark": "^2.1.4", "coveralls": "^3.0.2", "expect.js": "~0.3.1", "@types/node": "^14.14.10", "lint-staged": "^10.2.2", "eslint-plugin-jsdoc": "^30.6.2", "eslint-config-prettier": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cheerio_1.0.0-rc.5_1608584756893_0.4150975989223866", "host": "s3://npm-registry-packages"}}, "1.0.0-rc.6": {"name": "cheerio", "version": "1.0.0-rc.6", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper", "parser", "html"], "author": {"url": "mat.io", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "license": "MIT", "_id": "cheerio@1.0.0-rc.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "david<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "jugglinmike", "email": "<EMAIL>"}, {"name": "feedic", "email": "<EMAIL>"}], "homepage": "https://cheerio.js.org/", "bugs": {"url": "https://github.com/cheeriojs/cheerio/issues"}, "dist": {"shasum": "a5ae81ab483aeefa1280c325543c601145506240", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-1.0.0-rc.6.tgz", "fileCount": 19, "integrity": "sha512-hjx1XE1M/D5pAtMgvWwE21QClmAEeGHOIDfycgmndisdNgI6PE1cGRQkMGBcsbUbmEQyWu5PJLUcAOjtQS8DWw==", "signatures": [{"sig": "MEYCIQDqSLRQTJFwGs31xJLz+DtzCeVPDFO2cJkLxML3Rr5YiwIhAPVAFORqyKCIugU94PXnqOUvNeKOqJmBby5zeI+yMH+5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 153959, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgb3MxCRA9TVsSAnZWagAAgEAQAJVjemcWchEkQbgm9DYF\n2OaJMEwcpArkgDltNzxsLaT3bNE+F8ZQAzaW8INyEOw+H7/s0VdSm0MGfGOo\nmft+LqlNgGTE6SZh/j4pvPKflr2LjgOOZzM61svinyE9Jh1282cKm/U9rKgV\nSLUrQLoo/0EM4/ZbfeDjUUiXVwaXhmQ8GSc4dTEBD87x31YsBnH6Fi4ZdNho\n8DQEQ/P93Hr1shPzJDLmHRHJ0I2xM8tVbzqX5dSaBIYOxZDxUa6NMLZhaZAc\nJZBRnR1x3S5wdVb8zK3VTMjGRHZEov38eOy14QmaWXDRk+XedJh5Kjqxeesa\nccyvXxFsbM/Lzgr/rHLZwtveec8mtKEkRxGGOHHahtEC+2CIBdGjuCm079Cp\nKxVsnvv13YgmfECNlksiKN5uTkHc9u+WCS7SdvVIMLsx+UQTMemp8R4tfc1H\n0mJlBYWttwRduduIWVPYFZVTNZsj4us7ECMR30E0IyQcu2+YxTr0i+PR+qrv\nBOJe9n8u9Zn4iWYcQtRpxBT5qap8OrQ7m60QFP94NPbFfs/ZjINQmUxUXaB5\ngPn59Jq5rKzWIpHBvwLwWEAd8inm4PgbK4iac17ClfCmjRmJUinPij674RxV\npLFbgzrurWqyCvZbjt9rELqZ9Q+HJzfOUeGoObfd4GoewhbKYugm0W6cSP2d\nn7Y5\r\n=GuRI\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testMatch": ["<rootDir>/test/**/*.js"], "testEnvironment": "node", "testPathIgnorePatterns": ["/__fixtures__/"]}, "main": "./index.js", "types": "types/index.d.ts", "engines": {"node": ">= 0.12"}, "gitHead": "0c787f656a2cd44ca36a1ab70dccc7c8d98340de", "scripts": {"lint": "npm run lint:es && npm run lint:prettier", "test": "npm run lint && npm run test:jest && npm run test:types", "bench": "npm run benchmark", "format": "npm run format:es && npm run format:prettier", "lint:es": "eslint --ignore-path .gitignore .", "benchmark": "node benchmark/benchmark.js --regex \"^(?!.*highmem)\"", "format:es": "npm run lint:es -- --fix", "test:jest": "jest", "build:docs": "jsdoc --configure jsdoc-config.json", "pre-commit": "lint-staged", "test:types": "tsd", "lint:prettier": "npm run format:prettier:raw -- --check", "test:jest:cov": "npm run test:jest -- --coverage", "format:prettier": "npm run format:prettier:raw -- --write", "format:prettier:raw": "prettier \"**/*.{js,ts,md,json,yml}\" --ignore-path .gitignore"}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "prettier": {"tabWidth": 2, "singleQuote": true}, "repository": {"url": "git://github.com/cheeriojs/cheerio.git", "type": "git"}, "_npmVersion": "7.9.0", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "lint-staged": {"*.js": ["prettier --write", "npm run test:lint -- --fix"], "*.{json,md,ts,yml}": ["prettier --write"]}, "_nodeVersion": "15.11.0", "dependencies": {"parse5": "^6.0.1", "domhandler": "^4.1.0", "htmlparser2": "^6.1.0", "cheerio-select": "^1.3.0", "dom-serializer": "^1.3.1", "parse5-htmlparser2-tree-adapter": "^6.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"tsd": "^0.14.0", "jest": "^26.6.3", "husky": "^4.3.8", "jsdoc": "^3.6.6", "jsdom": "^16.5.2", "eslint": "^7.23.0", "jquery": "^3.6.0", "prettier": "^2.2.1", "benchmark": "^2.1.4", "@types/node": "^14.14.37", "lint-staged": "^10.5.4", "clean-jsdoc-theme": "^3.1.2", "eslint-plugin-jest": "^24.3.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^32.3.0", "prettier-plugin-jsdoc": "0.3.14", "eslint-config-prettier": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/cheerio_1.0.0-rc.6_1617916720540_0.2675324082455899", "host": "s3://npm-registry-packages"}}, "1.0.0-rc.7": {"name": "cheerio", "version": "1.0.0-rc.7", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper", "parser", "html"], "author": {"url": "mat.io", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "license": "MIT", "_id": "cheerio@1.0.0-rc.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "david<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "jugglinmike", "email": "<EMAIL>"}, {"name": "feedic", "email": "<EMAIL>"}], "homepage": "https://cheerio.js.org/", "bugs": {"url": "https://github.com/cheeriojs/cheerio/issues"}, "dist": {"shasum": "392dec11e03da30c495149081de1a8b8e669bea1", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-1.0.0-rc.7.tgz", "fileCount": 4, "integrity": "sha512-LiS5ueTHfMKDpcCEJYsMIQmokQj/7McI9cvIsX7Spn7339oqA7dksNug1ceo8UTZUQovEjbNsNZ/OOdoq0wfpQ==", "signatures": [{"sig": "MEYCIQDBsjfpTg4e2ZfdjUTmxzpP1p10Oaf0VpUWG4wA+AbdQgIhAMSj4/VXXiYOL6/DX02wm3TqCjSp8lFiLhSQcu598eX3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41956, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJglAV9CRA9TVsSAnZWagAAzU8P/389mnIpjYGAgWAEbPbK\nqchZ6WHJWNHQsaLpY4UsQuyBuU9YsslNORunZTy9C+UaYnAMrhd3juejVPH1\n0qrobGRAeekrwh2JoNTWHsdAbLVUjKcIlQcSCW+11EApWB30xONbDXhjN2nk\nxLfD/Mvvwq7b/Km6Oy8WuDplHceSvD5WHRr4hgbL7ZXUJdWcPUuVloPqzBTo\nkjGgdjN+UxHQegQF+GD1I94mSaKblBku6qH6B9pp2y38+g+7h4H5IeOhrWzI\ngz6gSLcXCCHNFtjl48eCgp2SoK4b6sbUUPvIE0MUwElzRUD6QRFRx3m0mTik\ntCofBot33nhLmQDY0rzphZLsbBFUYGjysULlLtiqkTqTSQgDXoAcMNzEg/NN\npCxMKVCx3+KMLY6jKzwRyOrQ9QMK7yHbNjjcYyDTihWDNPihemT0+EL9F6rp\nxtRbHYtf2/8CGutUTK/3lbu47KaTOrhVX0uEVIjsxzzBlenqhbY67y9xnR97\nQyy57t7JGOwaIctnIjG4/ArdWv8YFssZReVfil3/um1hE9CsdzVcq8A5vUkp\nwT+jeKR/457dsV9MsYFfleWLtEuRI4/wI35mSByPkIS2J3Wbk94tFG1RJ/RE\nDymx+jbN0HqjevB0wtaHx4JLjVrPUH19/HqKvymL4GmySydXfEMwhQOHeQkb\nhyEW\r\n=ACSb\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "testPathIgnorePatterns": ["/__fixtures__/"]}, "main": "lib/index.js", "types": "lib/index.d.ts", "engines": {"node": ">= 6"}, "funding": "https://github.com/cheeriojs/cheerio?sponsor=1", "gitHead": "819f9b2140d0148059d458d0135bc9bdfec8c81b", "scripts": {"lint": "npm run lint:es && npm run lint:prettier", "test": "npm run lint && npm run test:jest", "bench": "npm run benchmark", "format": "npm run format:es && npm run format:prettier", "lint:es": "eslint --ignore-path .gitignore .", "benchmark": "ts-node benchmark/benchmark.ts --regex \"^(?!.*highmem)\"", "format:es": "npm run lint:es -- --fix", "test:jest": "jest", "build:docs": "typedoc --hideGenerator src/index.ts", "pre-commit": "lint-staged", "lint:prettier": "npm run format:prettier:raw -- --check", "test:jest:cov": "npm run test:jest -- --coverage", "format:prettier": "npm run format:prettier:raw -- --write", "update-sponsors": "ts-node scripts/fetch-sponsors.ts", "format:prettier:raw": "prettier \"**/*.{js,ts,md,json,yml}\" --ignore-path .gitignore"}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "prettier": {"tsdoc": true, "tabWidth": 2, "singleQuote": true}, "repository": {"url": "git://github.com/cheeriojs/cheerio.git", "type": "git"}, "_npmVersion": "7.10.0", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "lint-staged": {"*.js": ["prettier --write", "npm run test:lint -- --fix"], "*.{json,md,ts,yml}": ["prettier --write"]}, "_nodeVersion": "16.0.0", "dependencies": {"tslib": "^2.2.0", "parse5": "^6.0.1", "domhandler": "^4.2.0", "htmlparser2": "^6.1.0", "cheerio-select": "^1.4.0", "dom-serializer": "^1.3.1", "parse5-htmlparser2-tree-adapter": "^6.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.6.3", "husky": "^4.3.8", "jsdom": "^16.5.3", "eslint": "^7.25.0", "jquery": "^3.6.0", "ts-jest": "^26.5.6", "ts-node": "^9.1.1", "typedoc": "^0.20.36", "prettier": "^2.2.1", "benchmark": "^2.1.4", "node-fetch": "^2.6.1", "typescript": "^4.2.4", "@types/jest": "^26.0.23", "@types/node": "^15.0.2", "lint-staged": "^10.5.4", "@types/jsdom": "^16.2.10", "@types/parse5": "^6.0.0", "@octokit/graphql": "^4.6.1", "@types/benchmark": "^2.1.0", "@types/node-fetch": "^2.5.10", "eslint-plugin-jest": "^24.3.6", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^33.1.0", "prettier-plugin-jsdoc": "0.3.22", "eslint-config-prettier": "^8.3.0", "@typescript-eslint/parser": "^4.22.1", "@typescript-eslint/eslint-plugin": "^4.22.1", "@types/parse5-htmlparser2-tree-adapter": "^6.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cheerio_1.0.0-rc.7_1620313469149_0.2067674536478592", "host": "s3://npm-registry-packages"}}, "1.0.0-rc.8": {"name": "cheerio", "version": "1.0.0-rc.8", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper", "parser", "html"], "author": {"url": "mat.io", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "license": "MIT", "_id": "cheerio@1.0.0-rc.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "david<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "jugglinmike", "email": "<EMAIL>"}, {"name": "feedic", "email": "<EMAIL>"}], "homepage": "https://cheerio.js.org/", "bugs": {"url": "https://github.com/cheeriojs/cheerio/issues"}, "dist": {"shasum": "1a2e4875d10aa49b9e70e722c1ac8b61e3914bd3", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-1.0.0-rc.8.tgz", "fileCount": 50, "integrity": "sha512-w188MtEs/byz3Xh1DHifokHdAEkd/FVIawxovzHKIb8O7eim2M294X+rUd7RhiomPdISDSiq2oU1R4ImcR/aug==", "signatures": [{"sig": "MEQCIDuc/+zlSVzjsO5Wc09E5E/1tKWJvyApzzCJCYp6i1cvAiB/0B8+FU08HQJGdTIxQD6f07rQu1eQA7jF3bAM6WVgTg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 237433, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJglAr0CRA9TVsSAnZWagAA0GAP/0ZQL1n4KawNBFyDbXa/\nkwiEj19LQFte8O2P1oUPs3Rk3yBn+F/UOw30A+j+UV/f7vnAt7e2HzbXduTA\nyyGsv1HDIH6pl8iX5qpiHF2mAi4z53PtW3sgDRPjM2GGc3g2/On/Y4oHq68b\nj50SBT0ugHz/u0j2H+t064Qkjwu3fw5AxSQgodLfisGEf1xjG6lMPnkd3qlX\n/zDdAsBTjLTc4czPKywKd3CJVUkuy0f6QiufCKZYFNsW3yI8iYx2N89hCdsh\n/Fu1G09GEW1S4daCkwpjoM39IHMsnpccFotgauJDg8TFyn6ombUFJTYhmSYV\nIdJQIJkMOejq2sOvXybqOsi68nIqzBeIGkoYMmj+NntCVPo/Q6IGpOT0w6Ql\nn3Jh8NdPMl5AShqu5rpadgAgbuuriQM4eJStFwQlfIP5RtcxPv66xWduMlEG\nI66prDWqqQUZyZ0KU257jjomzvchv5imQy3q8VCVcqxkc8nemR1PG5gYnZn2\naASJa+mY/+GmX5gp2+wAQ9tR4+3+iuVOyyEcE+5JG+qiwK5AcDMvL7/sTIdW\nU1McBUuiOrra6wuvOIqNbQPCzCHuQwxhmhci2ozS3LvoblIrDTzr3gg2YuI2\nS+fSg459QmsWDpU6wt8dB7HclBI+kuKVlT1JPUjZBHcYluRklbvsODyzTMG+\nbcX2\r\n=MUvY\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "testPathIgnorePatterns": ["/__fixtures__/"]}, "main": "lib/index.js", "types": "lib/index.d.ts", "engines": {"node": ">= 6"}, "funding": "https://github.com/cheeriojs/cheerio?sponsor=1", "gitHead": "649b6cfb7bf31b2e87fd059cd3f82190da6a5a7c", "scripts": {"lint": "npm run lint:es && npm run lint:prettier", "test": "npm run lint && npm run test:jest", "bench": "npm run benchmark", "build": "tsc", "format": "npm run format:es && npm run format:prettier", "lint:es": "eslint --ignore-path .gitignore .", "benchmark": "ts-node benchmark/benchmark.ts --regex \"^(?!.*highmem)\"", "format:es": "npm run lint:es -- --fix", "test:jest": "jest", "build:docs": "typedoc --hideGenerator src/index.ts", "pre-commit": "lint-staged", "lint:prettier": "npm run format:prettier:raw -- --check", "test:jest:cov": "npm run test:jest -- --coverage", "prepublishOnly": "npm run build", "format:prettier": "npm run format:prettier:raw -- --write", "update-sponsors": "ts-node scripts/fetch-sponsors.ts", "format:prettier:raw": "prettier \"**/*.{js,ts,md,json,yml}\" --ignore-path .gitignore"}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "prettier": {"tsdoc": true, "tabWidth": 2, "singleQuote": true}, "repository": {"url": "git://github.com/cheeriojs/cheerio.git", "type": "git"}, "_npmVersion": "7.10.0", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "lint-staged": {"*.js": ["prettier --write", "npm run test:lint -- --fix"], "*.{json,md,ts,yml}": ["prettier --write"]}, "_nodeVersion": "16.0.0", "dependencies": {"tslib": "^2.2.0", "parse5": "^6.0.1", "domhandler": "^4.2.0", "htmlparser2": "^6.1.0", "cheerio-select": "^1.4.0", "dom-serializer": "^1.3.1", "parse5-htmlparser2-tree-adapter": "^6.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.6.3", "husky": "^4.3.8", "jsdom": "^16.5.3", "eslint": "^7.25.0", "jquery": "^3.6.0", "ts-jest": "^26.5.6", "ts-node": "^9.1.1", "typedoc": "^0.20.36", "prettier": "^2.2.1", "benchmark": "^2.1.4", "node-fetch": "^2.6.1", "typescript": "^4.2.4", "@types/jest": "^26.0.23", "@types/node": "^15.0.2", "lint-staged": "^10.5.4", "@types/jsdom": "^16.2.10", "@types/parse5": "^6.0.0", "@octokit/graphql": "^4.6.1", "@types/benchmark": "^2.1.0", "@types/node-fetch": "^2.5.10", "eslint-plugin-jest": "^24.3.6", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^33.1.0", "prettier-plugin-jsdoc": "0.3.22", "eslint-config-prettier": "^8.3.0", "@typescript-eslint/parser": "^4.22.1", "@typescript-eslint/eslint-plugin": "^4.22.1", "@types/parse5-htmlparser2-tree-adapter": "^6.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cheerio_1.0.0-rc.8_1620314867918_0.9776413631429546", "host": "s3://npm-registry-packages"}}, "1.0.0-rc.9": {"name": "cheerio", "version": "1.0.0-rc.9", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper", "parser", "html"], "author": {"url": "mat.io", "name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "license": "MIT", "_id": "cheerio@1.0.0-rc.9", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "david<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "jugglinmike", "email": "<EMAIL>"}, {"name": "feedic", "email": "<EMAIL>"}], "homepage": "https://cheerio.js.org/", "bugs": {"url": "https://github.com/cheeriojs/cheerio/issues"}, "dist": {"shasum": "a3ae6b7ce7af80675302ff836f628e7cb786a67f", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-1.0.0-rc.9.tgz", "fileCount": 49, "integrity": "sha512-QF6XVdrLONO6DXRF5iaolY+odmhj2CLj+xzNod7INPWMi/x9X4SOylH0S/vaPpX+AUU6t04s34SQNh7DbkuCng==", "signatures": [{"sig": "MEUCIHaR8xZ2b3W2wXX6X/4WBelpfaF/5UvInBpN0lNIZaY0AiEAwi7KjanI6WcgWIEsbmE/25DDhJs9secIBeRpc9OXaes=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 233436, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJglBHVCRA9TVsSAnZWagAAvrgQAIw8eewgCUuPe0VvG5k5\nRRqe7hq2dK5mvkXeIggMKQwupYGkftB8EKN25RCfFKnF5fyB21xCz39cY3zN\nHo3TKR4oP6k5XEDTOjCN27reRaifT4h8PNWlSls3wz771CEk1D/I3YQ557Mr\nCeHycylhY7NqXXKwW5YZ1MYb6WspLm24qdK6f0rMinCFkREK4sBbRrCv2Wjn\nl7TjhEmF+q6kKFI5P5tISpat9y5HdlzrRAtH/D5xfgF+RgUU7DSEjzSYcMyx\nS2KGxxU2vCc2zep6UONTNfYJPuvq06BXVztyMNJm0SQ28cRFuZglwMZktzuh\nMef9ht8Te6IUQtZ15bGb18Cle08HnK+gwQENP6d53GxG876hvaJ8cURAQHnY\n++LBDUoX+JADtTh+A+MG4/lZgUFvGoavvCKH5DfP/moUdU2pU/M4oO+TJBGb\n5RUyYeKdIbxdhNN4DHCwUp3wXS3w4PfeN9bzY+NoEqbIx1BilbIa2uZ+Xx/K\n3bDwWTQQ4zw+WJHQmwUqQtcLTR6n+seO8vIdUxyPyXIRTzUWIfS8ZUSOb5Js\nVPUjISPyAbGCVKR6Vz6gWtFFL3kCHfNDO+zUy3RGhQF/lLZy90ahDuPR5Hl9\nrsitHCcxoqB/QMOXqMF5glivQOEV+ymtyRDaT+P/uWy3WdMws2P+VxwmouoG\noIQP\r\n=5on1\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "testPathIgnorePatterns": ["/__fixtures__/"]}, "main": "lib/index.js", "types": "lib/index.d.ts", "engines": {"node": ">= 6"}, "funding": "https://github.com/cheeriojs/cheerio?sponsor=1", "gitHead": "95759d64886d2f4f944daef1d8b706e75aa0154b", "scripts": {"lint": "npm run lint:es && npm run lint:prettier", "test": "npm run lint && npm run test:jest", "bench": "npm run benchmark", "build": "tsc", "format": "npm run format:es && npm run format:prettier", "lint:es": "eslint --ignore-path .gitignore .", "benchmark": "ts-node benchmark/benchmark.ts --regex \"^(?!.*highmem)\"", "format:es": "npm run lint:es -- --fix", "test:jest": "jest", "build:docs": "typedoc --hideGenerator src/index.ts", "pre-commit": "lint-staged", "lint:prettier": "npm run format:prettier:raw -- --check", "test:jest:cov": "npm run test:jest -- --coverage", "prepublishOnly": "npm run build", "format:prettier": "npm run format:prettier:raw -- --write", "update-sponsors": "ts-node scripts/fetch-sponsors.ts", "format:prettier:raw": "prettier \"**/*.{js,ts,md,json,yml}\" --ignore-path .gitignore"}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "prettier": {"tsdoc": true, "tabWidth": 2, "singleQuote": true}, "repository": {"url": "git://github.com/cheeriojs/cheerio.git", "type": "git"}, "_npmVersion": "7.10.0", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "lint-staged": {"*.js": ["prettier --write", "npm run test:lint -- --fix"], "*.{json,md,ts,yml}": ["prettier --write"]}, "_nodeVersion": "16.0.0", "dependencies": {"tslib": "^2.2.0", "parse5": "^6.0.1", "domhandler": "^4.2.0", "htmlparser2": "^6.1.0", "cheerio-select": "^1.4.0", "dom-serializer": "^1.3.1", "parse5-htmlparser2-tree-adapter": "^6.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.6.3", "husky": "^4.3.8", "jsdom": "^16.5.3", "eslint": "^7.25.0", "jquery": "^3.6.0", "ts-jest": "^26.5.6", "ts-node": "^9.1.1", "typedoc": "^0.20.36", "prettier": "^2.2.1", "benchmark": "^2.1.4", "node-fetch": "^2.6.1", "typescript": "^4.2.4", "@types/jest": "^26.0.23", "@types/node": "^15.0.2", "lint-staged": "^10.5.4", "@types/jsdom": "^16.2.10", "@types/parse5": "^6.0.0", "@octokit/graphql": "^4.6.1", "@types/benchmark": "^2.1.0", "@types/node-fetch": "^2.5.10", "eslint-plugin-jest": "^24.3.6", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^33.1.0", "prettier-plugin-jsdoc": "0.3.22", "eslint-config-prettier": "^8.3.0", "@typescript-eslint/parser": "^4.22.1", "@typescript-eslint/eslint-plugin": "^4.22.1", "@types/parse5-htmlparser2-tree-adapter": "^6.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cheerio_1.0.0-rc.9_1620316629455_0.8924758542915057", "host": "s3://npm-registry-packages"}}, "1.0.0-rc.10": {"name": "cheerio", "version": "1.0.0-rc.10", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper", "parser", "html"], "author": {"name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "license": "MIT", "_id": "cheerio@1.0.0-rc.10", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "david<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "jugglinmike", "email": "<EMAIL>"}, {"name": "feedic", "email": "<EMAIL>"}], "homepage": "https://cheerio.js.org/", "bugs": {"url": "https://github.com/cheeriojs/cheerio/issues"}, "dist": {"shasum": "2ba3dcdfcc26e7956fc1f440e61d51c643379f3e", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-1.0.0-rc.10.tgz", "fileCount": 48, "integrity": "sha512-g0J0q/O6mW8z5zxQ3A8E8J1hUgp4SMOvEoW/x84OwyHKe/Zccz83PVT4y5Crcr530FV6NgmKI1qvGTKVl9XXVw==", "signatures": [{"sig": "MEQCIHO1bypYxU1Fyor88oSUJieGfbcBryf9zDFd6XKRl6lTAiBJSKkTbbZ4c/i/JZoeM3t3XgUiD1UpESZLB0vTz17VjA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 204054, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgv4//CRA9TVsSAnZWagAADckP+wdy8Z4c71rWW2cQ1FxQ\nfN+adjGX2LS2hbB/xbPwIWkdZ5I/k9oxDKOGB90/LrtfiLgoCJJPnHjDrgum\nRb48ACBTXxq0OIC/toH40KoAsnAHPzl4CVXKtXJQQwFCNfet+UQWsrK5uXVg\n23WgFFnCawbHC0KSjTuMmKZEcm4/8VmWRGXaDwTVyxNwwZAK33Up/vITQ+Yo\nkAZgR3NxWgxqSbRxNIla2bVuQ/wS2hcXYe66ZP08vGUhbKL4FMshV12IZ2+R\n6km6MFslnneJxJ0/woOorUP7pz27NUtIO5sGUkYmWKKl1a8zgeuAsitY/Ly+\nY0I6ge+x/FLUB4I1WbZd2PQrikYHSpwxrfJDYMKeXUpc2MNfGuzTB2aLtGJ4\nKEMS2+Fa0wF9xZqk/JRbN4hIShFDRIUkMBDjloi1G0zYhOkYpaAXE44Pnxm9\nH9xXIM0B9vSWbM860ynW9eeqZOQz+Tm9n8OYYQA59ndznNAuSm+/anfPJxFM\n4vEYkOT/0/IEJl6sOlwT1Yg96QhkVfPNioXlbAQguEwZSQxRWTJuoBrRffCv\nLP0i/Mpsyz4luJgsMnaVVVibbNLvh5ugMA1jyNIBrR2ahsAMorIvcgQMqVKM\nOSRzNXIYlOrdzJjDLOsUQa5QJniBJ6Cz+llzRn8xXhesnljeD0mVgwHDF2ii\n31l6\r\n=jOxW\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "testPathIgnorePatterns": ["/__fixtures__/"]}, "main": "lib/index.js", "types": "lib/index.d.ts", "engines": {"node": ">= 6"}, "funding": "https://github.com/cheeriojs/cheerio?sponsor=1", "gitHead": "6a6847a06f6e3c7a14dee60acb37fc1139d72227", "scripts": {"lint": "npm run lint:es && npm run lint:prettier", "test": "npm run lint && npm run test:jest", "bench": "npm run benchmark", "build": "tsc", "format": "npm run format:es && npm run format:prettier", "lint:es": "eslint --ignore-path .gitignore .", "benchmark": "ts-node benchmark/benchmark.ts --regex \"^(?!.*highmem)\"", "format:es": "npm run lint:es -- --fix", "test:jest": "jest", "build:docs": "typedoc --hideGenerator src/index.ts", "pre-commit": "lint-staged", "lint:prettier": "npm run format:prettier:raw -- --check", "test:jest:cov": "npm run test:jest -- --coverage", "prepublishOnly": "npm run build", "format:prettier": "npm run format:prettier:raw -- --write", "update-sponsors": "ts-node scripts/fetch-sponsors.ts", "format:prettier:raw": "prettier \"**/*.{js,ts,md,json,yml}\" --ignore-path .gitignore"}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "prettier": {"tsdoc": true, "tabWidth": 2, "singleQuote": true}, "repository": {"url": "git://github.com/cheeriojs/cheerio.git", "type": "git"}, "_npmVersion": "7.13.0", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "lint-staged": {"*.js": ["prettier --write", "npm run test:lint -- --fix"], "*.{json,md,ts,yml}": ["prettier --write"]}, "_nodeVersion": "16.2.0", "dependencies": {"tslib": "^2.2.0", "parse5": "^6.0.1", "domhandler": "^4.2.0", "htmlparser2": "^6.1.0", "cheerio-select": "^1.5.0", "dom-serializer": "^1.3.2", "parse5-htmlparser2-tree-adapter": "^6.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^27.0.4", "husky": "^4.3.8", "jsdom": "^16.6.0", "eslint": "^7.28.0", "jquery": "^3.6.0", "ts-jest": "^27.0.3", "ts-node": "^10.0.0", "typedoc": "^0.20.36", "prettier": "^2.3.1", "benchmark": "^2.1.4", "node-fetch": "^2.6.1", "typescript": "^4.2.4", "@types/jest": "^26.0.23", "@types/node": "^15.12.1", "lint-staged": "^11.0.0", "@types/jsdom": "^16.2.10", "@types/parse5": "^6.0.0", "@octokit/graphql": "^4.6.2", "@types/benchmark": "^2.1.0", "@types/node-fetch": "^2.5.10", "eslint-plugin-jest": "^24.3.6", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^35.1.3", "prettier-plugin-jsdoc": "0.3.22", "eslint-config-prettier": "^8.3.0", "@typescript-eslint/parser": "^4.26.0", "@typescript-eslint/eslint-plugin": "^4.26.0", "@types/parse5-htmlparser2-tree-adapter": "^6.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cheerio_1.0.0-rc.10_1623166975230_0.3268714267694157", "host": "s3://npm-registry-packages"}}, "1.0.0-rc.11": {"name": "cheerio", "version": "1.0.0-rc.11", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper", "parser", "html"], "author": {"name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "license": "MIT", "_id": "cheerio@1.0.0-rc.11", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "david<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "jugglinmike", "email": "<EMAIL>"}, {"name": "feedic", "email": "<EMAIL>"}], "homepage": "https://cheerio.js.org/", "bugs": {"url": "https://github.com/cheeriojs/cheerio/issues"}, "dist": {"shasum": "1be84be1a126958366bcc57a11648cd9b30a60c2", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-1.0.0-rc.11.tgz", "fileCount": 124, "integrity": "sha512-bQwNaDIBKID5ts/DsdhxrjqFXYfLw4ste+wMKqWA8DyKcS4qwsPP4Bk8ZNaTJjvpiX/qW3BT4sU7d6Bh5i+dag==", "signatures": [{"sig": "MEYCIQCsVAf1wwlFQxoHLZGUMJRatYZ2FZZj1P4Kk7jMFcAMgQIhAK8Wf3ZdUB+L4rkH9qbwGfm2yXACa9UeO2NW4OsFKUBg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 557627, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih7LSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqZSg/9EDNCuj3jpfUNHxIcT6PXgZvVwImztqjdve2Iq0xJxC3yPxxV\r\n6u6hG5JG+9eWveQ4KM6dMwHFO8Ss5BwTKmYaQWIfLvlJVkGbfZ2Kb4VqRzzg\r\nsrWlj2f+yKSBctXOmPwsxANEkSQq1qlHwdM4fgSxGTa+s9lX+ja5njNgAuB3\r\nbXgqefdfczUdEp2W6EVaBV0Y7NEyT2CjY3I4fFsHGQWaxDd346ChpWpWBCKW\r\nJ9fHYd+3PTlXy9KHw4kVFBUslsvXmPZXNs5upt6FjhMyK0hxQDdiqbyvrQxq\r\nYRArmjsOhzoeA8kKDLa4zREpQsCEWmuauVQByVsJyBQ7MQlQZgwEdRB460Ga\r\nUgo06ZTBe5YK0mXhZ2e+Bi8GC8Ga1G8YuEgd+IofsvNakIB14tDGWWxpOjMq\r\nGfOilCjyFLE4vNzF9k2Xqxhc8TcanDCM5Po04R8TgxpZU36g/heYGmxhqv+W\r\nlfDbyF6XZXzLm9GScPks8uvXPUpVHR7pghMXsdoxTcVSDzi+VhpghGcy4kXn\r\nuq/EUYbqRvdOYghgba4Epo6KJ54BW1sSqBmldQiljVq+MT0LKdyGGxO9cql8\r\nSHU4gmirCZuDxMmU5QkUDvWdLbgEe1qpdWWdEc8VxmUIja1dN2JO72datfiD\r\nps52gK4mz6SOnwIMGD1m789ZTIBDgrl1vxU=\r\n=ioP/\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "coverageProvider": "v8", "moduleNameMapper": {"^(.*)\\.js$": "$1"}, "testPathIgnorePatterns": ["/__fixtures__/"]}, "main": "lib/index.js", "types": "lib/index.d.ts", "module": "lib/esm/index.js", "engines": {"node": ">= 6"}, "exports": {".": {"import": "./lib/esm/index.js", "require": "./lib/index.js"}, "./lib/slim": {"import": "./lib/esm/slim.js", "require": "./lib/slim.js"}}, "funding": "https://github.com/cheeriojs/cheerio?sponsor=1", "gitHead": "7b260a2233b112375dbeb261013e142cbe48d81d", "scripts": {"lint": "npm run lint:es && npm run lint:prettier", "test": "npm run lint && npm run test:jest", "bench": "npm run benchmark", "build": "npm run build:cjs && npm run build:esm", "format": "npm run format:es && npm run format:prettier", "lint:es": "eslint --ignore-path .gitignore .", "prepare": "husky install", "benchmark": "npm run build:cjs && ts-node benchmark/benchmark.ts --regex \"^(?!.*highmem)\"", "build:cjs": "tsc --sourceRoot https://raw.githubusercontent.com/cheeriojs/cheerio/$(git rev-parse HEAD)/src/", "build:esm": "npm run build:cjs -- --module esnext --target es2019 --outDir lib/esm && echo '{\"type\":\"module\"}' > lib/esm/package.json", "format:es": "npm run lint:es -- --fix", "test:jest": "jest", "build:docs": "typedoc --hideGenerator src/index.ts", "lint:prettier": "npm run format:prettier:raw -- --check", "test:jest:cov": "npm run test:jest -- --coverage", "prepublishOnly": "npm run build", "format:prettier": "npm run format:prettier:raw -- --write", "update-sponsors": "ts-node scripts/fetch-sponsors.ts", "format:prettier:raw": "prettier \"**/*.{{m,c,}js,ts,md,json,yml}\" --ignore-path .gitignore"}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "prettier": {"tsdoc": true, "tabWidth": 2, "singleQuote": true}, "repository": {"url": "git://github.com/cheeriojs/cheerio.git", "type": "git"}, "_npmVersion": "8.6.0", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "lint-staged": {"*.js": ["prettier --write", "npm run test:lint -- --fix"], "*.{json,md,ts,yml}": ["prettier --write"]}, "_nodeVersion": "18.0.0", "dependencies": {"tslib": "^2.4.0", "parse5": "^7.0.0", "domutils": "^3.0.1", "domhandler": "^5.0.3", "htmlparser2": "^8.0.1", "cheerio-select": "^2.1.0", "dom-serializer": "^2.0.0", "parse5-htmlparser2-tree-adapter": "^7.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^27.5.1", "husky": "^8.0.1", "jsdom": "^19.0.0", "eslint": "^8.15.0", "jquery": "^3.6.0", "undici": "^5.2.0", "ts-jest": "^27.1.4", "ts-node": "^10.7.0", "typedoc": "^0.22.15", "prettier": "^2.6.2", "benchmark": "^2.1.4", "typescript": "^4.6.4", "@types/jest": "^27.5.0", "@types/node": "^17.0.35", "lint-staged": "^12.4.1", "@octokit/graphql": "^4.8.0", "@types/benchmark": "^2.1.1", "eslint-plugin-jest": "^26.2.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^39.3.0", "prettier-plugin-jsdoc": "0.3.38", "eslint-config-prettier": "^8.5.0", "@typescript-eslint/parser": "^5.25.0", "@typescript-eslint/eslint-plugin": "^5.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/cheerio_1.0.0-rc.11_1653060305831_0.8197741790986095", "host": "s3://npm-registry-packages"}}, "1.0.0-rc.12": {"name": "cheerio", "version": "1.0.0-rc.12", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper", "parser", "html"], "author": {"name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "license": "MIT", "_id": "cheerio@1.0.0-rc.12", "maintainers": [{"name": "jugglinmike", "email": "<EMAIL>"}, {"name": "feedic", "email": "<EMAIL>"}], "homepage": "https://cheerio.js.org/", "bugs": {"url": "https://github.com/cheeriojs/cheerio/issues"}, "dist": {"shasum": "788bf7466506b1c6bf5fae51d24a2c4d62e47683", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-1.0.0-rc.12.tgz", "fileCount": 124, "integrity": "sha512-VqR8m68vM46BNnuZ5NtnGBKIE/DfN0cRIzg9n40EIq9NOv90ayxLBXA8fXC5gquFRGJSTRqBq25Jt2ECLR431Q==", "signatures": [{"sig": "MEUCID4jbToCGdF58tiKU34msLoglolVURuUehaKp+1UcAlNAiEAos7FwrWEirGFQ0RlxLGqPxpi26rL8c3NnHkBYXEjaBc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 557840, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiuGBCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqXchAAnUFbQwvJgn5NhYzkRZCxu0FOWpoptI4la4mR5eYGzSaGb06C\r\nHj9VMWPEcn+gNwSO6ujKduasJ7Fhphg/fG0UVSDBNEMVtYKoPOsMWZaIhSka\r\n8CmOKzi2jWvanWac9FzjzERhCvQoKL9UN9i7Osfq+9vLMqCNHB7aNyNJEkr3\r\nNMBV+RoIn3l/HSjdP/nw2tohhqSnQqLu7hGnN8ktc8YUqwlnDKtVLVWhR0MB\r\nRUReRLNohW1+PzTscYvPMNS7jU+lZ/iZXhM6VweJRbcXLaUQU2vAqm3mqskh\r\nDR/A24Ikr+Jyn7OivAt1Lp6LJl+nU/joljsHpOSQXTdNTfLM/n7PqLXJuApe\r\nddd3Z4b3hOpDTeEGwgPnqMnHvo4EM6pJWFt9OVUlFgjAgMyvNdXSPh3xSvKj\r\n/Uv6zNiso1MB4n5faAB2qNAuVLdmNlCiegfvyDSQDnew5O2U3ypqWgO3/NXD\r\nPL7mbsmMRmjczJYoYBXM3bcdTIp6VG2n7QkcvvN0j7OZ94i0j4FItSkVAxDr\r\nylMOTgnOL2Q7+m5QkNFhwVR12LGc4cHlYZTW7XqoGAgI3T6Sl7tlof4Wv2jd\r\nqfLF41uqH8Axo608HF9kEeRbMwWp2FYbF5exWO7sn/6/D6jgph61onJwVAYX\r\nsZRE4e3SxjvePdgw4AilGjCsIEjZBKrjyYM=\r\n=ncrQ\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "coverageProvider": "v8", "moduleNameMapper": {"^(.*)\\.js$": ["$1.js", "$1"]}, "testPathIgnorePatterns": ["/__fixtures__/"]}, "main": "lib/index.js", "types": "lib/index.d.ts", "module": "lib/esm/index.js", "engines": {"node": ">= 6"}, "exports": {".": {"import": "./lib/esm/index.js", "require": "./lib/index.js"}, "./lib/slim": {"import": "./lib/esm/slim.js", "require": "./lib/slim.js"}, "./lib/utils": {"import": "./lib/esm/utils.js", "require": "./lib/utils.js"}}, "funding": "https://github.com/cheeriojs/cheerio?sponsor=1", "gitHead": "d1cbc66d53392ce8bf6cd0068f675836372d2bf3", "scripts": {"lint": "npm run lint:es && npm run lint:prettier", "test": "npm run lint && npm run test:jest", "bench": "npm run benchmark", "build": "npm run build:cjs && npm run build:esm", "format": "npm run format:es && npm run format:prettier", "lint:es": "eslint --ignore-path .gitignore .", "prepare": "husky install", "benchmark": "npm run build:cjs && ts-node benchmark/benchmark.ts --regex \"^(?!.*highmem)\"", "build:cjs": "tsc --sourceRoot https://raw.githubusercontent.com/cheeriojs/cheerio/$(git rev-parse HEAD)/src/", "build:esm": "npm run build:cjs -- --module esnext --target es2019 --outDir lib/esm && echo '{\"type\":\"module\"}' > lib/esm/package.json", "format:es": "npm run lint:es -- --fix", "test:jest": "jest", "build:docs": "typedoc --hideGenerator src/index.ts", "lint:prettier": "npm run format:prettier:raw -- --check", "test:jest:cov": "npm run test:jest -- --coverage", "prepublishOnly": "npm run build", "format:prettier": "npm run format:prettier:raw -- --write", "update-sponsors": "ts-node scripts/fetch-sponsors.ts", "format:prettier:raw": "prettier \"**/*.{{m,c,}js,ts,md,json,yml}\" --ignore-path .gitignore"}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "prettier": {"tsdoc": true, "tabWidth": 2, "singleQuote": true}, "repository": {"url": "git://github.com/cheeriojs/cheerio.git", "type": "git"}, "_npmVersion": "8.12.1", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "directories": {}, "lint-staged": {"*.js": ["prettier --write", "npm run test:lint -- --fix"], "*.{json,md,ts,yml}": ["prettier --write"]}, "_nodeVersion": "18.4.0", "dependencies": {"parse5": "^7.0.0", "domutils": "^3.0.1", "domhandler": "^5.0.3", "htmlparser2": "^8.0.1", "cheerio-select": "^2.1.0", "dom-serializer": "^2.0.0", "parse5-htmlparser2-tree-adapter": "^7.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^28.1.1", "husky": "^8.0.1", "jsdom": "^20.0.0", "eslint": "^8.18.0", "jquery": "^3.6.0", "undici": "^5.5.1", "ts-jest": "^28.0.5", "ts-node": "^10.8.1", "typedoc": "^0.22.17", "prettier": "^2.7.1", "benchmark": "^2.1.4", "typescript": "^4.7.4", "@types/jest": "^28.1.3", "@types/node": "^18.0.0", "lint-staged": "^13.0.2", "@octokit/graphql": "^4.8.0", "@types/benchmark": "^2.1.1", "eslint-plugin-jest": "^26.5.3", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^39.3.3", "prettier-plugin-jsdoc": "0.3.38", "eslint-config-prettier": "^8.5.0", "@typescript-eslint/parser": "^5.29.0", "@typescript-eslint/eslint-plugin": "^5.29.0"}, "_npmOperationalInternal": {"tmp": "tmp/cheerio_1.0.0-rc.12_1656250434099_0.025500869652552938", "host": "s3://npm-registry-packages"}}, "1.0.0": {"name": "cheerio", "version": "1.0.0", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper", "parser", "dom", "xml", "html"], "author": {"name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "license": "MIT", "_id": "cheerio@1.0.0", "maintainers": [{"name": "jugglinmike", "email": "<EMAIL>"}, {"name": "feedic", "email": "<EMAIL>"}], "homepage": "https://cheerio.js.org/", "bugs": {"url": "https://github.com/cheeriojs/cheerio/issues"}, "dist": {"shasum": "1ede4895a82f26e8af71009f961a9b8cb60d6a81", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-1.0.0.tgz", "fileCount": 243, "integrity": "sha512-quS9HgjQpdaXOvsZz82Oz7uxtXiy6UIsIQcpBj7HRw2M63Skasm9qlDocAM7jNuaxdhpPU7c4kJN+gA5MCu4ww==", "signatures": [{"sig": "MEYCIQDgJB9DsthPETRSBahO6YXCJ5KBT9CQyWZKJQJcuh0a3wIhANdMqfbbOSVOOl07wVMzZ1cWzEtHjM1LqCm0S3SRIxbd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1252107}, "main": "./dist/commonjs/index.js", "tshy": {"exclude": ["**/*.spec.ts", "**/__fixtures__/*", "**/__tests__/*", "**/__snapshots__/*"], "exports": {".": "./src/index.ts", "./slim": "./src/slim.ts", "./utils": "./src/utils.ts", "./package.json": "./package.json"}, "esmDialects": ["browser"]}, "type": "module", "types": "./dist/commonjs/index.d.ts", "module": "./dist/esm/index.js", "engines": {"node": ">=18.17"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "browser": {"types": "./dist/browser/index.d.ts", "default": "./dist/browser/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./slim": {"import": {"types": "./dist/esm/slim.d.ts", "default": "./dist/esm/slim.js"}, "browser": {"types": "./dist/browser/slim.d.ts", "default": "./dist/browser/slim.js"}, "require": {"types": "./dist/commonjs/slim.d.ts", "default": "./dist/commonjs/slim.js"}}, "./utils": {"import": {"types": "./dist/esm/utils.d.ts", "default": "./dist/esm/utils.js"}, "browser": {"types": "./dist/browser/utils.d.ts", "default": "./dist/browser/utils.js"}, "require": {"types": "./dist/commonjs/utils.d.ts", "default": "./dist/commonjs/utils.js"}}, "./package.json": "./package.json"}, "funding": "https://github.com/cheeriojs/cheerio?sponsor=1", "gitHead": "50b5d5ce49f7863ed70c82dad2652bc8cb147859", "scripts": {"lint": "npm run lint:es && npm run lint:prettier && npm run lint:ts", "test": "npm run lint && npm run test:vi", "build": "tshy", "format": "npm run format:es && npm run format:prettier", "lint:es": "eslint --report-unused-disable-directives --ignore-path .gitignore .", "lint:ts": "tsc --noEmit", "prepare": "husky", "test:vi": "vitest run", "benchmark": "node --import=tsx benchmark/benchmark.ts", "format:es": "npm run lint:es -- --fix", "lint:prettier": "npm run format:prettier:raw -- --check", "prepublishOnly": "npm run build", "format:prettier": "npm run format:prettier:raw -- --write", "update-sponsors": "tsx scripts/fetch-sponsors.mts", "format:prettier:raw": "prettier \"**/*.{{m,c,}{j,t}s{x,},md{x,},json,y{a,}ml}\" --ignore-path .gitignore"}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "prettier": {"tsdoc": true, "plugins": ["./node_modules/prettier-plugin-jsdoc/dist/index.js"], "tabWidth": 2, "proseWrap": "always", "singleQuote": true}, "repository": {"url": "git://github.com/cheeriojs/cheerio.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "The fast, flexible & elegant library for parsing and manipulating HTML and XML.", "directories": {}, "lint-staged": {"*.js": ["prettier --write", "npm run lint:es -- --fix"], "*.{json,md,ts,yml}": ["prettier --write"]}, "_nodeVersion": "22.6.0", "dependencies": {"parse5": "^7.1.2", "undici": "^6.19.5", "domutils": "^3.1.0", "domhandler": "^5.0.3", "htmlparser2": "^9.1.0", "cheerio-select": "^2.1.0", "dom-serializer": "^2.0.0", "whatwg-mimetype": "^4.0.0", "encoding-sniffer": "^0.2.0", "parse5-parser-stream": "^7.1.2", "parse5-htmlparser2-tree-adapter": "^7.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tsx": "^4.17.0", "tshy": "^3.0.2", "husky": "^9.1.4", "jsdom": "^24.1.1", "eslint": "^8.57.0", "jquery": "^3.7.1", "vitest": "^2.0.5", "prettier": "^3.3.3", "tinybench": "^2.9.0", "typescript": "^5.5.4", "@types/node": "^22.1.0", "lint-staged": "^15.2.8", "@types/jsdom": "^21.1.7", "@imgix/js-core": "^3.8.0", "eslint-plugin-n": "^16.6.2", "@octokit/graphql": "^8.1.1", "@vitest/coverage-v8": "^2.0.5", "eslint-plugin-jsdoc": "^50.0.0", "eslint-plugin-vitest": "^0.5.4", "eslint-plugin-unicorn": "^55.0.0", "prettier-plugin-jsdoc": "^1.3.0", "@types/whatwg-mimetype": "^3.0.2", "eslint-config-prettier": "^9.1.0", "@typescript-eslint/parser": "^8.0.1", "eslint-plugin-expect-type": "^0.4.0", "@typescript-eslint/eslint-plugin": "^8.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/cheerio_1.0.0_1723230101445_0.4563454789207071", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "cheerio", "version": "1.1.0", "description": "The fast, flexible & elegant library for parsing and manipulating HTML and XML.", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper", "parser", "dom", "xml", "html"], "homepage": "https://cheerio.js.org/", "bugs": {"url": "https://github.com/cheeriojs/cheerio/issues"}, "repository": {"type": "git", "url": "git://github.com/cheeriojs/cheerio.git"}, "funding": "https://github.com/cheeriojs/cheerio?sponsor=1", "license": "MIT", "author": {"name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "jugglinmike", "email": "<EMAIL>"}, {"name": "feedic", "email": "<EMAIL>"}], "type": "module", "exports": {".": {"browser": {"types": "./dist/browser/index.d.ts", "default": "./dist/browser/index.js"}, "import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./slim": {"browser": {"types": "./dist/browser/slim.d.ts", "default": "./dist/browser/slim.js"}, "import": {"types": "./dist/esm/slim.d.ts", "default": "./dist/esm/slim.js"}, "require": {"types": "./dist/commonjs/slim.d.ts", "default": "./dist/commonjs/slim.js"}}, "./utils": {"browser": {"types": "./dist/browser/utils.d.ts", "default": "./dist/browser/utils.js"}, "import": {"types": "./dist/esm/utils.d.ts", "default": "./dist/esm/utils.js"}, "require": {"types": "./dist/commonjs/utils.d.ts", "default": "./dist/commonjs/utils.js"}}, "./package.json": "./package.json"}, "main": "./dist/commonjs/index.js", "module": "./dist/esm/index.js", "browser": "./dist/browser/index.js", "types": "./dist/commonjs/index.d.ts", "scripts": {"benchmark": "node --import=tsx benchmark/benchmark.ts", "build": "tshy", "format": "npm run format:es && npm run format:prettier", "format:es": "eslint . --fix", "format:prettier": "npm run format:prettier:raw -- --write", "format:prettier:raw": "prettier \"**/*.{{m,c,}{j,t}s{x,},md{x,},json,y{a,}ml}\" --ignore-path .gitignore", "lint": "npm run lint:es && npm run lint:prettier && npm run lint:ts", "lint:es": "eslint .", "lint:prettier": "npm run format:prettier:raw -- --check", "lint:ts": "tsc --noEmit", "prepare": "husky", "prepublishOnly": "npm run build", "test": "npm run lint && npm run test:vi", "test:vi": "vitest run", "update-sponsors": "tsx scripts/fetch-sponsors.mts"}, "lint-staged": {"*.js": ["prettier --write", "eslint --fix"], "*.{json,md,ts,yml}": ["prettier --write"]}, "prettier": {"plugins": ["./node_modules/prettier-plugin-jsdoc/dist/index.js"], "proseWrap": "always", "singleQuote": true, "tabWidth": 2, "tsdoc": true}, "dependencies": {"cheerio-select": "^2.1.0", "dom-serializer": "^2.0.0", "domhandler": "^5.0.3", "domutils": "^3.2.2", "encoding-sniffer": "^0.2.0", "htmlparser2": "^10.0.0", "parse5": "^7.3.0", "parse5-htmlparser2-tree-adapter": "^7.1.0", "parse5-parser-stream": "^7.1.2", "undici": "^7.10.0", "whatwg-mimetype": "^4.0.0"}, "devDependencies": {"@eslint/compat": "^1.2.9", "@eslint/js": "^9.28.0", "@imgix/js-core": "^3.8.0", "@octokit/graphql": "^9.0.1", "@types/jsdom": "^21.1.7", "@types/node": "^22.15.30", "@types/whatwg-mimetype": "^3.0.2", "@vitest/coverage-v8": "^3.2.2", "@vitest/eslint-plugin": "^1.2.1", "eslint": "^9.28.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-jsdoc": "^50.7.1", "eslint-plugin-n": "^17.19.0", "eslint-plugin-unicorn": "^59.0.1", "globals": "^16.2.0", "husky": "^9.1.7", "jquery": "^3.7.1", "jsdom": "^26.1.0", "lint-staged": "^16.1.0", "prettier": "^3.5.3", "prettier-plugin-jsdoc": "^1.3.2", "tinybench": "^4.0.1", "tshy": "^3.0.2", "tsx": "^4.19.4", "typescript": "^5.8.3", "typescript-eslint": "^8.33.1", "vitest": "^3.1.4"}, "engines": {"node": ">=18.17"}, "tshy": {"esmDialects": ["browser"], "exports": {".": "./src/index.ts", "./slim": "./src/slim.ts", "./utils": "./src/utils.ts", "./package.json": "./package.json"}, "exclude": ["**/*.spec.ts", "**/__fixtures__/*", "**/__tests__/*", "**/__snapshots__/*"]}, "_id": "cheerio@1.1.0", "gitHead": "5971683c03df5123178b55186435a7e96d865f13", "_nodeVersion": "24.1.0", "_npmVersion": "11.3.0", "dist": {"integrity": "sha512-+0hMx9eYhJvWbgpKV9hN7jg0JcwydpopZE4hgi+KvQtByZXPp04NiCWU0LzcAbP63abZckIHkTQaXVF52mX3xQ==", "shasum": "87b9bec6dd3696e405ea79da7d2749d8308b0953", "tarball": "https://registry.npmjs.org/cheerio/-/cheerio-1.1.0.tgz", "fileCount": 243, "unpackedSize": 1264460, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQC90c3SvlPO/KFoFLSf/iHxb4pfse1hGkod+9C+7RwRogIhAMv/KgFqhNBtw6sN0yBTgYMwzpv4x1gZPdyfuwUbox8m"}]}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/cheerio_1.1.0_1749412411695_0.5220611294923516"}, "_hasShrinkwrap": false}}, "time": {"created": "2011-10-08T07:28:26.299Z", "modified": "2025-06-08T19:53:32.090Z", "0.0.1": "2011-10-08T07:28:34.195Z", "0.0.2": "2011-11-01T03:23:31.758Z", "0.0.3": "2011-11-01T03:24:37.466Z", "0.0.4": "2011-11-01T03:27:34.918Z", "0.1.1": "2011-11-01T04:41:36.490Z", "0.1.2": "2011-11-01T04:48:23.487Z", "0.1.3": "2011-11-01T05:15:23.908Z", "0.1.4": "2011-11-01T05:28:03.164Z", "0.1.5": "2011-11-01T06:00:52.465Z", "0.2.0": "2011-11-01T06:08:39.155Z", "0.2.1": "2011-11-06T06:56:10.622Z", "0.2.2": "2011-11-10T04:57:58.140Z", "0.3.0": "2011-11-20T05:34:41.736Z", "0.3.1": "2011-11-26T04:56:06.446Z", "0.3.2": "2011-12-02T03:36:41.948Z", "0.4.0": "2011-12-19T08:59:49.994Z", "0.4.1": "2011-12-19T09:17:23.883Z", "0.4.2": "2012-01-17T04:15:16.676Z", "0.5.0": "2012-02-05T07:04:53.133Z", "0.5.1": "2012-02-05T09:01:19.447Z", "0.5.2": "2012-02-05T10:00:57.685Z", "0.6.0": "2012-02-07T08:16:03.489Z", "0.6.1": "2012-02-12T23:04:11.898Z", "0.6.2": "2012-02-13T02:49:34.778Z", "0.7.0": "2012-04-09T03:14:43.627Z", "0.8.0": "2012-05-28T02:06:50.120Z", "0.8.1": "2012-06-03T00:46:17.677Z", "0.8.2": "2012-06-12T01:33:47.872Z", "0.8.3": "2012-06-12T03:18:10.296Z", "0.9.0": "2012-07-24T16:29:13.808Z", "0.9.1": "2012-08-03T23:45:31.974Z", "0.9.2": "2012-08-10T21:34:13.228Z", "0.10.0": "2012-09-24T07:13:07.669Z", "0.10.1": "2012-10-03T17:42:48.351Z", "0.10.2": "2012-11-17T22:31:40.954Z", "0.10.3": "2012-11-18T21:43:33.025Z", "0.10.4": "2012-12-17T05:55:17.709Z", "0.10.5": "2012-12-18T17:47:58.495Z", "0.10.6": "2013-01-29T21:22:54.884Z", "0.10.7": "2013-02-11T03:12:08.331Z", "0.10.8": "2013-03-11T23:48:11.820Z", "0.11.0": "2013-04-22T22:42:27.721Z", "0.12.0": "2013-06-09T17:59:09.141Z", "0.12.1": "2013-07-31T06:58:53.388Z", "0.12.2": "2013-09-04T16:01:06.899Z", "0.12.3": "2013-10-05T01:43:49.342Z", "0.12.4": "2013-11-12T16:45:43.198Z", "0.13.0": "2013-12-30T21:59:53.305Z", "0.13.1": "2014-01-07T14:46:19.228Z", "0.14.0": "2014-04-01T14:47:26.497Z", "0.15.0": "2014-04-08T16:59:15.549Z", "0.16.0": "2014-05-09T01:28:44.313Z", "0.17.0": "2014-06-10T22:35:55.166Z", "0.18.0": "2014-11-06T19:01:31.574Z", "0.19.0": "2015-03-21T11:17:36.355Z", "0.20.0": "2016-02-01T11:06:37.919Z", "0.21.0": "2016-06-12T05:34:11.519Z", "0.22.0": "2016-08-23T12:21:40.447Z", "1.0.0-rc.1": "2017-05-27T21:02:45.789Z", "1.0.0-rc.2": "2017-07-02T17:36:55.384Z", "1.0.0-rc.3": "2019-04-06T22:14:16.077Z", "1.0.0-rc.4": "2020-12-21T19:36:05.900Z", "1.0.0-rc.5": "2020-12-21T21:05:57.054Z", "1.0.0-rc.6": "2021-04-08T21:18:40.714Z", "1.0.0-rc.7": "2021-05-06T15:04:29.274Z", "1.0.0-rc.8": "2021-05-06T15:27:48.073Z", "1.0.0-rc.9": "2021-05-06T15:57:09.579Z", "1.0.0-rc.10": "2021-06-08T15:42:55.467Z", "1.0.0-rc.11": "2022-05-20T15:25:06.218Z", "1.0.0-rc.12": "2022-06-26T13:33:54.305Z", "1.0.0": "2024-08-09T19:01:41.740Z", "1.1.0": "2025-06-08T19:53:31.913Z"}, "bugs": {"url": "https://github.com/cheeriojs/cheerio/issues"}, "author": {"name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "license": "MIT", "homepage": "https://cheerio.js.org/", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper", "parser", "dom", "xml", "html"], "repository": {"type": "git", "url": "git://github.com/cheeriojs/cheerio.git"}, "description": "The fast, flexible & elegant library for parsing and manipulating HTML and XML.", "maintainers": [{"name": "jugglinmike", "email": "<EMAIL>"}, {"name": "feedic", "email": "<EMAIL>"}], "readme": "<h1 align=\"center\">cheerio</h1>\n\n<h5 align=\"center\">The fast, flexible, and elegant library for parsing and manipulating HTML and XML.</h5>\n\n<div align=\"center\">\n  <a href=\"https://github.com/cheeriojs/cheerio/actions/workflows/ci.yml\">\n    <img src=\"https://github.com/cheeriojs/cheerio/actions/workflows/ci.yml/badge.svg\" alt=\"Build Status\">\n  </a>\n  <a href=\"https://coveralls.io/github/cheeriojs/cheerio\">\n    <img src=\"https://img.shields.io/coveralls/github/cheeriojs/cheerio/main\" alt=\"Coverage\">\n  </a>\n  <a href=\"#backers\">\n    <img src=\"https://img.shields.io/opencollective/backers/cheerio\" alt=\"OpenCollective backers\">\n  </a>\n  <a href=\"#sponsors\">\n    <img src=\"https://img.shields.io/opencollective/sponsors/cheerio\" alt=\"OpenCollective sponsors\">\n  </a>\n</div>\n\n<br>\n\n[中文文档 (Chinese Readme)](https://github.com/cheeriojs/cheerio/wiki/Chinese-README)\n\n```js\nimport * as cheerio from 'cheerio';\nconst $ = cheerio.load('<h2 class=\"title\">Hello world</h2>');\n\n$('h2.title').text('Hello there!');\n$('h2').addClass('welcome');\n\n$.html();\n//=> <html><head></head><body><h2 class=\"title welcome\">Hello there!</h2></body></html>\n```\n\n## Installation\n\nInstall Cheerio using a package manager like npm, yarn, or bun.\n\n```bash\nnpm install cheerio\n# or\nbun add cheerio\n```\n\n## Features\n\n**&#10084; Proven syntax:** Cheerio implements a subset of core jQuery. Cheerio\nremoves all the DOM inconsistencies and browser cruft from the jQuery library,\nrevealing its truly gorgeous API.\n\n**&#991; Blazingly fast:** Cheerio works with a very simple, consistent DOM\nmodel. As a result parsing, manipulating, and rendering are incredibly\nefficient.\n\n**&#10049; Incredibly flexible:** Cheerio wraps around\n[parse5](https://github.com/inikulin/parse5) for parsing HTML and can optionally\nuse the forgiving [htmlparser2](https://github.com/fb55/htmlparser2/). Cheerio\ncan parse nearly any HTML or XML document. Cheerio works in both browser and\nserver environments.\n\n## API\n\n### Loading\n\nFirst you need to load in the HTML. This step in jQuery is implicit, since\njQuery operates on the one, baked-in DOM. With Cheerio, we need to pass in the\nHTML document.\n\n```js\n// ESM or TypeScript:\nimport * as cheerio from 'cheerio';\n\n// In other environments:\nconst cheerio = require('cheerio');\n\nconst $ = cheerio.load('<ul id=\"fruits\">...</ul>');\n\n$.html();\n//=> <html><head></head><body><ul id=\"fruits\">...</ul></body></html>\n```\n\n### Selectors\n\nOnce you've loaded the HTML, you can use jQuery-style selectors to find elements\nwithin the document.\n\n#### \\$( selector, [context], [root] )\n\n`selector` searches within the `context` scope which searches within the `root`\nscope. `selector` and `context` can be a string expression, DOM Element, array\nof DOM elements, or cheerio object. `root`, if provided, is typically the HTML\ndocument string.\n\nThis selector method is the starting point for traversing and manipulating the\ndocument. Like in jQuery, it's the primary method for selecting elements in the\ndocument.\n\n```js\n$('.apple', '#fruits').text();\n//=> Apple\n\n$('ul .pear').attr('class');\n//=> pear\n\n$('li[class=orange]').html();\n//=> Orange\n```\n\n### Rendering\n\nWhen you're ready to render the document, you can call the `html` method on the\n\"root\" selection:\n\n```js\n$.root().html();\n//=>  <html>\n//      <head></head>\n//      <body>\n//        <ul id=\"fruits\">\n//          <li class=\"apple\">Apple</li>\n//          <li class=\"orange\">Orange</li>\n//          <li class=\"pear\">Pear</li>\n//        </ul>\n//      </body>\n//    </html>\n```\n\nIf you want to render the\n[`outerHTML`](https://developer.mozilla.org/en-US/docs/Web/API/Element/outerHTML)\nof a selection, you can use the `outerHTML` prop:\n\n```js\n$('.pear').prop('outerHTML');\n//=> <li class=\"pear\">Pear</li>\n```\n\nYou may also render the text content of a Cheerio object using the `text`\nmethod:\n\n```js\nconst $ = cheerio.load('This is <em>content</em>.');\n$('body').text();\n//=> This is content.\n```\n\n### The \"DOM Node\" object\n\nCheerio collections are made up of objects that bear some resemblance to\n[browser-based DOM nodes](https://developer.mozilla.org/en-US/docs/Web/API/Node).\nYou can expect them to define the following properties:\n\n- `tagName`\n- `parentNode`\n- `previousSibling`\n- `nextSibling`\n- `nodeValue`\n- `firstChild`\n- `childNodes`\n- `lastChild`\n\n## Screencasts\n\n[https://vimeo.com/31950192](https://vimeo.com/31950192)\n\n> This video tutorial is a follow-up to Nettut's \"How to Scrape Web Pages with\n> Node.js and jQuery\", using cheerio instead of JSDOM + jQuery. This video shows\n> how easy it is to use cheerio and how much faster cheerio is than JSDOM +\n> jQuery.\n\n## Cheerio in the real world\n\nAre you using cheerio in production? Add it to the\n[wiki](https://github.com/cheeriojs/cheerio/wiki/Cheerio-in-Production)!\n\n## Sponsors\n\nDoes your company use Cheerio in production? Please consider\n[sponsoring this project](https://github.com/cheeriojs/cheerio?sponsor=1)! Your\nhelp will allow maintainers to dedicate more time and resources to its\ndevelopment and support.\n\n**Headlining Sponsors**\n\n<!-- BEGIN SPONSORS: headliner -->\n\n<a href=\"https://tidelift.com/subscription/pkg/npm-cheerio\" target=\"_blank\" rel=\"noopener noreferrer\">\n            <img height=\"128px\" width=\"128px\" src=\"https://humble.imgix.net/https%3A%2F%2Fgithub.com%2Ftidelift.png?ixlib=js-3.8.0&w=128&h=128&fit=fillmax&fill=solid&s=0713e6ee5c7ab01e7559df695c1e8cd9\" title=\"Tidelift\" alt=\"Tidelift\"></img>\n          </a>\n<a href=\"https://github.com/\" target=\"_blank\" rel=\"noopener noreferrer\">\n            <img height=\"128px\" width=\"128px\" src=\"https://humble.imgix.net/https%3A%2F%2Fgithub.com%2Fgithub.png?ixlib=js-3.8.0&w=128&h=128&fit=fillmax&fill=solid&s=a1e87ca289de84eb32ea85432cf8ad11\" title=\"Github\" alt=\"Github\"></img>\n          </a>\n<a href=\"https://www.airbnb.com/\" target=\"_blank\" rel=\"noopener noreferrer\">\n            <img height=\"128px\" width=\"128px\" src=\"https://humble.imgix.net/https%3A%2F%2Fgithub.com%2Fairbnb.png?ixlib=js-3.8.0&w=128&h=128&fit=fillmax&fill=solid&s=384cad45e10faea516202ad10801f895\" title=\"AirBnB\" alt=\"AirBnB\"></img>\n          </a>\n\n<!-- END SPONSORS -->\n\n**Other Sponsors**\n\n<!-- BEGIN SPONSORS: sponsor -->\n\n<a href=\"https://betking.com.ua/games/all-slots/\" target=\"_blank\" rel=\"noopener noreferrer\">\n            <img height=\"64px\" width=\"64px\" src=\"https://humble.imgix.net/https%3A%2F%2Fimages.opencollective.com%2Figrovye-avtomaty-ua%2F96bfde3%2Flogo.png?ixlib=js-3.8.0&w=64&h=64&fit=fillmax&fill=solid&s=07091c88a0b859ecaa81ef10fadf3075\" title=\"Ігрові автомати\" alt=\"Ігрові автомати\"></img>\n          </a>\n<a href=\"https://onlinecasinosspelen.com\" target=\"_blank\" rel=\"noopener noreferrer\">\n            <img height=\"64px\" width=\"64px\" src=\"https://humble.imgix.net/https%3A%2F%2Fimages.opencollective.com%2Fonlinecasinosspelen%2F4ce3830%2Flogo.png?ixlib=js-3.8.0&w=64&h=64&fit=fillmax&fill=solid&s=60e5dd9f3993a754d0e5d47a43ff7462\" title=\"OnlineCasinosSpelen\" alt=\"OnlineCasinosSpelen\"></img>\n          </a>\n<a href=\"https://casinoZonderregistratie.net/\" target=\"_blank\" rel=\"noopener noreferrer\">\n            <img height=\"64px\" width=\"64px\" src=\"https://humble.imgix.net/https%3A%2F%2Fimages.opencollective.com%2Fczrnet%2F24e6252%2Flogo.png?ixlib=js-3.8.0&w=64&h=64&fit=fillmax&fill=solid&s=d9b81b3c39bca4d3a8f279e79c5eec8d\" title=\"CasinoZonderRegistratie.net\" alt=\"CasinoZonderRegistratie.net\"></img>\n          </a>\n<a href=\"https://Nieuwe-Casinos.net\" target=\"_blank\" rel=\"noopener noreferrer\">\n            <img height=\"64px\" width=\"64px\" src=\"https://humble.imgix.net/https%3A%2F%2Fimages.opencollective.com%2Fnieuwecasinos%2Fee150d6%2Flogo.png?ixlib=js-3.8.0&w=64&h=64&fit=fillmax&fill=solid&s=c2663f8b5dcfc983ef5641028d7b430b\" title=\"Nieuwe-Casinos.net\" alt=\"Nieuwe-Casinos.net\"></img>\n          </a>\n\n<!-- END SPONSORS -->\n\n## Backers\n\n[Become a backer](https://github.com/cheeriojs/cheerio?sponsor=1) to show your\nsupport for Cheerio and help us maintain and improve this open source project.\n\n<!-- BEGIN SPONSORS: backer -->\n\n<a href=\"https://kafidoff.com\" target=\"_blank\" rel=\"noopener noreferrer\">\n            <img height=\"64px\" width=\"64px\" src=\"https://humble.imgix.net/https%3A%2F%2Fimages.opencollective.com%2Fkafidoff-vasy%2Fd7ff85c%2Favatar.png?ixlib=js-3.8.0&w=64&h=64&fit=fillmax&fill=solid&s=a41c66c2f9b1d3a7a241e425e7aa2d09\" title=\"Vasy Kafidoff\" alt=\"Vasy Kafidoff\"></img>\n          </a>\n<a href=\"https://medium.com/norch\" target=\"_blank\" rel=\"noopener noreferrer\">\n            <img height=\"48px\" width=\"48px\" src=\"https://humble.imgix.net/https%3A%2F%2Fimages.opencollective.com%2Fespenklem%2F7e8cd40%2Favatar.png?ixlib=js-3.8.0&w=48&h=48&fit=fillmax&fill=solid&s=f727bc0f59d1738188ec8e4499123149\" title=\"Espen Klem\" alt=\"Espen Klem\"></img>\n          </a>\n\n<!-- END SPONSORS -->\n\n## License\n\nMIT\n", "readmeFilename": "Readme.md", "users": {"6174": true, "285858315": true, "apk": true, "jwv": true, "nex": true, "nxc": true, "pid": true, "vbm": true, "andr": true, "bdfu": true, "binq": true, "cath": true, "d3ck": true, "dahe": true, "dwqs": true, "etiv": true, "ferx": true, "fill": true, "hain": true, "huyz": true, "inca": true, "ions": true, "j.su": true, "jtrh": true, "kael": true, "naij": true, "neo1": true, "nuer": true, "qmmr": true, "shkd": true, "spad": true, "stri": true, "susu": true, "wfsm": true, "wzbg": true, "xufz": true, "xumx": true, "zeke": true, "8code": true, "andyd": true, "arsnl": true, "ashco": true, "azder": true, "coppy": true, "dchem": true, "dlion": true, "dralc": true, "ealen": true, "eklem": true, "flozz": true, "fm-96": true, "h4des": true, "jacks": true, "jmm23": true, "jokja": true, "jruif": true, "junos": true, "jxson": true, "kikna": true, "lbeff": true, "lvwei": true, "mebmc": true, "menor": true, "nak2k": true, "nickl": true, "nmadd": true, "ntl88": true, "phris": true, "pyrou": true, "renz0": true, "roxnz": true, "scl33": true, "seldo": true, "shide": true, "simme": true, "sopov": true, "stany": true, "stona": true, "suddi": true, "timdp": true, "xalbi": true, "yrocq": true, "zfkun": true, "a_dent": true, "adamlu": true, "adonai": true, "ahme-t": true, "ajduke": true, "alanev": true, "alek-s": true, "antoor": true, "apopek": true, "appnus": true, "arttse": true, "bojand": true, "bugaga": true, "buster": true, "bvaccc": true, "chosan": true, "chrisx": true, "clisun": true, "cthree": true, "d-band": true, "d1soft": true, "dmikam": true, "dna2go": true, "dpobel": true, "duxing": true, "ecelis": true, "edin-m": true, "emsure": true, "evan2x": true, "fgmnts": true, "foxted": true, "ggomma": true, "glebec": true, "hiwanz": true, "hkongm": true, "ibruce": true, "irfan3": true, "itskdk": true, "jolg42": true, "joliva": true, "jorycn": true, "kabomi": true, "kericw": true, "kevinh": true, "knoja4": true, "kungkk": true, "leesei": true, "legacy": true, "lenine": true, "leohxj": true, "levani": true, "lionft": true, "m0dred": true, "mahpah": true, "maxidr": true, "maziar": true, "mihaiv": true, "mimmo1": true, "minowu": true, "mmatto": true, "monjer": true, "moueza": true, "mrbgit": true, "mrgabo": true, "nanook": true, "neckro": true, "noyobo": true, "nuwaio": true, "oheard": true, "petrus": true, "phdong": true, "phuego": true, "psibal": true, "pstoev": true, "royliu": true, "shriek": true, "sloanb": true, "sm0ck1": true, "tedyhy": true, "tomekf": true, "tscole": true, "tylerm": true, "visnup": true, "vutran": true, "webbot": true, "wh8766": true, "xtiawy": true, "yeming": true, "yichan": true, "yinfxs": true, "yl2014": true, "yong_a": true, "yorusi": true, "yuch4n": true, "zambon": true, "zanner": true, "zaptun": true, "zechau": true, "zhoutk": true, "zolern": true, "2xcvbnm": true, "alectic": true, "alexreg": true, "algonzo": true, "alphatr": true, "antanst": true, "asaupup": true, "aselzer": true, "asj1992": true, "aslezak": true, "astesio": true, "azevedo": true, "cashsun": true, "chengsu": true, "dac2205": true, "demoive": true, "denmerc": true, "dimayak": true, "dino_su": true, "dkannan": true, "drewigg": true, "eterna2": true, "ferrari": true, "flyslow": true, "fytriht": true, "ghe1219": true, "guim0ve": true, "gztomas": true, "hemanth": true, "honpery": true, "itonyyo": true, "jalcine": true, "jaxcode": true, "jcottam": true, "jerrywu": true, "jovinbm": true, "kaashin": true, "kparkov": true, "lachriz": true, "laoshaw": true, "lezyeoh": true, "lijunle": true, "lo1tuma": true, "mamalat": true, "movever": true, "nilz3ro": true, "nohomey": true, "ohcoder": true, "parroit": true, "pdedkov": true, "perrywu": true, "pitzzae": true, "preco21": true, "rebolon": true, "restuta": true, "robixxu": true, "rolandp": true, "snazzah": true, "soladmd": true, "subchen": true, "syaning": true, "tangchr": true, "tapsboy": true, "tianmin": true, "tomchao": true, "tsxuehu": true, "vghhkjh": true, "wesleyr": true, "wjp5826": true, "x0000ff": true, "xngiser": true, "yokubee": true, "1cr18ni9": true, "2dxgujun": true, "abhisekp": true, "abraheom": true, "amkaradi": true, "andruxin": true, "antouank": true, "artjacob": true, "austencm": true, "awaterma": true, "balduran": true, "blessdyb": true, "buru1020": true, "choi4450": true, "churnpoh": true, "clholzin": true, "coalesce": true, "damianof": true, "dgarlitt": true, "drveresh": true, "esundahl": true, "eugen814": true, "fabiancz": true, "fabioper": true, "fangleen": true, "foxforce": true, "gavatron": true, "gchudnov": true, "grreenzz": true, "gurunate": true, "gvr37leo": true, "henrymcc": true, "hex20dec": true, "hexagon6": true, "hiztmine": true, "htavarez": true, "hurerera": true, "ifeature": true, "iliaxone": true, "jackvial": true, "jlucktay": true, "joeyespo": true, "jon_shen": true, "kenlimmj": true, "kevinwan": true, "krabello": true, "lamansky": true, "leejefon": true, "lenville": true, "leodutra": true, "leonning": true, "lianhr12": true, "limingv5": true, "losymear": true, "makediff": true, "manishrc": true, "manxisuo": true, "markov00": true, "marsking": true, "mayq0422": true, "mhaidarh": true, "mkomigor": true, "moimikey": true, "pddivine": true, "phette23": true, "porkbits": true, "pruettti": true, "qddegtya": true, "qingying": true, "rachkoud": true, "risyasin": true, "rochejul": true, "scaffrey": true, "shacoxss": true, "shahzaib": true, "sloutrel": true, "sobralia": true, "softwind": true, "ssljivic": true, "sujith3g": true, "tdmalone": true, "tmcguire": true, "tommyzzm": true, "tza17313": true, "vamakoda": true, "vsakaria": true, "waitfish": true, "waldrupm": true, "wattanar": true, "wujianfu": true, "xmalinov": true, "yangfan1": true, "yashprit": true, "youngluo": true, "zuojiang": true, "actionklo": true, "alexreg90": true, "alimaster": true, "antixrist": true, "appastair": true, "awhmandan": true, "babenzele": true, "binarymax": true, "bluechili": true, "bpmccurdy": true, "bransorem": true, "cascadejs": true, "cilindrox": true, "cuidapeng": true, "davidrlee": true, "ddkothari": true, "devonning": true, "djensen47": true, "dr-benton": true, "erincinci": true, "fgribreau": true, "finnpauls": true, "flying-dr": true, "gavinning": true, "grabantot": true, "guiambros": true, "guojiahao": true, "heartnett": true, "herlon214": true, "huigezong": true, "igorissen": true, "jason0518": true, "jbhurruth": true, "jerkovicl": true, "jesusgoku": true, "jinglf000": true, "jirqoadai": true, "jminnamon": true, "joaocunha": true, "jtittsler": true, "juzhiyuan": true, "karbunkul": true, "kkk123321": true, "kulakowka": true, "largepuma": true, "larrychen": true, "lichenhao": true, "liweifeng": true, "maalthous": true, "madalozzo": true, "magemagic": true, "mark12433": true, "mastayoda": true, "max_devjs": true, "mikemimik": true, "nachbar90": true, "nancenick": true, "nice_body": true, "ninozhang": true, "nmccready": true, "noisypixy": true, "not-found": true, "pcooney10": true, "pingjiang": true, "porta8080": true, "ramzesucr": true, "riocampos": true, "rolldance": true, "rylan_yan": true, "shakakira": true, "shwaydogg": true, "stanzheng": true, "steel1990": true, "sternelee": true, "sunkeyhub": true, "superhans": true, "troygizzi": true, "txmcy1993": true, "ukrbublik": true, "vmichalak": true, "vonmauser": true, "vprasanth": true, "yang.shao": true, "yvanscher": true, "abdihaikal": true, "afelicioni": true, "alanerzhao": true, "alexmercer": true, "andriecool": true, "aquiandres": true, "avivharuzi": true, "beaulebens": true, "binginsist": true, "bplok20010": true, "bradcozine": true, "byossarian": true, "cfleschhut": true, "charmander": true, "chiaychang": true, "chirag8642": true, "davidkassa": true, "dccunni171": true, "directctrl": true, "ericlondon": true, "farskipper": true, "forestlake": true, "garrickajo": true, "gavinengel": true, "gerst20051": true, "gingersami": true, "guzhongren": true, "hentai_mew": true, "isaacvitor": true, "ivanempire": true, "jalmenarez": true, "jessaustin": true, "jswartwood": true, "jussipekka": true, "justinshea": true, "kingtrocki": true, "legiahoang": true, "leonardorb": true, "lijinghust": true, "lius971125": true, "magicarbon": true, "manikantag": true, "mark24code": true, "meganekick": true, "nate-river": true, "nicomf1982": true, "paulbremer": true, "pillar0514": true, "pmbenjamin": true, "quocnguyen": true, "rhinodavid": true, "rocket0191": true, "samhou1988": true, "sbruchmann": true, "seangenabe": true, "shadowlong": true, "shangri_la": true, "shuoshubao": true, "sonhuytran": true, "stephenhuh": true, "sushant711": true, "svmatthews": true, "tarkeshwar": true, "tomjamescn": true, "uxabdullah": true, "winfredzhu": true, "xieranmaya": true, "yasinaydin": true, "zousandian": true, "adityabakle": true, "balazserdos": true, "blaidd_drwg": true, "blakeembrey": true, "bracketdash": true, "chown_chmod": true, "chunhei2008": true, "davidazullo": true, "davidnyhuis": true, "diogocapela": true, "erk_war_han": true, "eserozvataf": true, "fengmiaosen": true, "flumpus-dev": true, "frknbasaran": true, "gonprazeres": true, "hal9zillion": true, "hortinstein": true, "icerainnuaa": true, "jackchi1981": true, "jamesbedont": true, "jasonbourne": true, "karlbateman": true, "khaledkaram": true, "kodekracker": true, "linfeng9008": true, "lukvonstrom": true, "mattattaque": true, "mr.raindrop": true, "nskondratev": true, "ouroboros99": true, "rubenjose75": true, "sammyteahan": true, "schwartzman": true, "sessionbean": true, "silentcloud": true, "tunnckocore": true, "volkanongun": true, "wangnan0610": true, "windmillboy": true, "zhaojunlike": true, "bhaveshgohel": true, "bianlongting": true, "boustanihani": true, "chocolateboy": true, "dpjayasekara": true, "ghalvatzakis": true, "hanghanghang": true, "happy-coding": true, "henryheleine": true, "herrbischoff": true, "imlinhanchao": true, "ivan.marquez": true, "jaimegvalero": true, "jakedemonaco": true, "joshhartigan": true, "matiasmarani": true, "musically-ut": true, "nanaotakashi": true, "natterstefan": true, "okumurakengo": true, "oussoulessou": true, "raphaelcockx": true, "salvatorelab": true, "samhwang1990": true, "shaomingquan": true, "sinisterstuf": true, "stevenvachon": true, "superchenney": true, "toby_reynold": true, "wesleylhandy": true, "yonigoldberg": true, "zhanghao-web": true, "zhenguo.zhao": true, "chinawolf_wyp": true, "crazyjingling": true, "dewang-mistry": true, "dongguangming": true, "gamersdelight": true, "hibrahimsafak": true, "humantriangle": true, "ivan403704409": true, "jasonwang1888": true, "jian263994241": true, "liangtongzhuo": true, "manojkhannakm": true, "nimblemachine": true, "parkerproject": true, "philippwiddra": true, "sakthiinfotec": true, "serge-nikitin": true, "shen-weizhong": true, "stewartatkins": true, "stone_breaker": true, "vitali.doudko": true, "avinashkoyyana": true, "bradleybossard": true, "eirikbirkeland": true, "flaviomarcioti": true, "gggauravgandhi": true, "jeffersonsouza": true, "leonardothibes": true, "shanewholloway": true, "spencermathews": true, "thebearingedge": true, "universemaster": true, "akashdeep-singh": true, "alexbaumgertner": true, "brandonpapworth": true, "chrisscastaneda": true, "icodeforcookies": true, "pensierinmusica": true, "subinvarghesein": true, "urbantumbleweed": true, "yoshimaa-tricot": true, "lorenzo.disidoro": true, "sammy_winchester": true, "programmingpearls": true}}