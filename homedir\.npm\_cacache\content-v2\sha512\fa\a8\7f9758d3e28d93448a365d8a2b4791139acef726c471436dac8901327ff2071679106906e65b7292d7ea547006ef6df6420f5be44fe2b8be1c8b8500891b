{"_id": "escape-html", "_rev": "59-d17e704abfaa742e0f309504cf3e6e35", "name": "escape-html", "description": "Escape string for use in HTML", "dist-tags": {"latest": "1.0.3"}, "versions": {"0.0.1": {"name": "escape-html", "description": "Escape HTML entities", "version": "0.0.1", "keywords": ["escape", "html", "utility"], "dependencies": {}, "main": "index.js", "_id": "escape-html@0.0.1", "dist": {"shasum": "160c25d8af49f4a7c140700697a92d1c218b901e", "tarball": "https://registry.npmjs.org/escape-html/-/escape-html-0.0.1.tgz", "integrity": "sha512-n7MoTJIwtDy8dnBJgpwz0TXwzGq0pmO/vlagjiJafcWPj3cAHFVB6qKn8QsCtTEOcls7DDA/1Rxx6d27928osw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCIrQ2uVgKY2m+bfU9LBMyxF/eEvCPiRJxFG74xPaCgEQIgVVenUWttcV7gvl8k4zPV2Gt8jVE57sZiJzwocyJWuL0="}]}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "1.0.0": {"name": "escape-html", "description": "Escape HTML entities", "version": "1.0.0", "keywords": ["escape", "html", "utility"], "dependencies": {}, "main": "index.js", "component": {"scripts": {"escape-html/index.js": "index.js"}}, "_id": "escape-html@1.0.0", "dist": {"shasum": "fedcd79564444ddaf2bd85b22c9961b3a3a38bf5", "tarball": "https://registry.npmjs.org/escape-html/-/escape-html-1.0.0.tgz", "integrity": "sha512-vb7Ns+9QhiE03jZbHW6ZAyT8N9kMR2genHRnoe8nUH2lOJ4rP46j8eOhC1j8aa9owREaS9qPy+BhkHnbjKhT0A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAlJkwdcYGPfS34hAwiMLdQakI2Vm8RZh6dVsPaV7IL/AiEAiqvBJxEst394qSYu40tn6DmNvCf7Nh8EdOZ6/LowFyU="}]}, "_from": ".", "_npmVersion": "1.2.14", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "1.0.1": {"name": "escape-html", "description": "Escape HTML entities", "version": "1.0.1", "keywords": ["escape", "html", "utility"], "dependencies": {}, "main": "index.js", "component": {"scripts": {"escape-html/index.js": "index.js"}}, "repository": {"type": "git", "url": "https://github.com/component/escape-html.git"}, "bugs": {"url": "https://github.com/component/escape-html/issues"}, "homepage": "https://github.com/component/escape-html", "_id": "escape-html@1.0.1", "dist": {"shasum": "181a286ead397a39a92857cfb1d43052e356bff0", "tarball": "https://registry.npmjs.org/escape-html/-/escape-html-1.0.1.tgz", "integrity": "sha512-z6kAnok8fqVTra7Yu77dZF2Y6ETJlxH58wN38wNyuNQLm8xXdKnfNrlSmfXsTePWP03rRVUKHubtUwanwUi7+g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE0LavUKEQNdYNH1wTAIBpiaSDJi/jPlUjFBGWytkRKcAiEAj6vJV8wABSQHozwoOz8kXKqAzZFaBO+u8WqgkDEtd/E="}]}, "_from": ".", "_npmVersion": "1.3.15", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "1.0.2": {"name": "escape-html", "description": "Escape HTML entities", "version": "1.0.2", "license": "MIT", "keywords": ["escape", "html", "utility"], "repository": {"type": "git", "url": "https://github.com/component/escape-html"}, "files": ["LICENSE", "Readme.md", "index.js"], "gitHead": "2477a23ae56f75e0a5622a20b5b55da00de3a23b", "bugs": {"url": "https://github.com/component/escape-html/issues"}, "homepage": "https://github.com/component/escape-html", "_id": "escape-html@1.0.2", "scripts": {}, "_shasum": "d77d32fa98e38c2f41ae85e9278e0e0e6ba1022c", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "d77d32fa98e38c2f41ae85e9278e0e0e6ba1022c", "tarball": "https://registry.npmjs.org/escape-html/-/escape-html-1.0.2.tgz", "integrity": "sha512-<PERSON><PERSON><PERSON>yCRC4liskWVAfkmosNWfG0eHQxI0W+Ko7k3cZaYVMfgt05dwZ68vw6S/TZM1BPvuTv3kq6CRCb7WWtBUVA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGZh1+DmuzF1itd9KlfFvrQ+uyDJZgMHRnDU28wxt9AsAiEA0mlM2OmIOHO/4ZsHyAiKlZgPE4HZE3EtzbfHlJO26Zk="}]}, "directories": {}}, "1.0.3": {"name": "escape-html", "description": "Escape string for use in HTML", "version": "1.0.3", "license": "MIT", "keywords": ["escape", "html", "utility"], "repository": {"type": "git", "url": "https://github.com/component/escape-html"}, "devDependencies": {"benchmark": "1.0.0", "beautify-benchmark": "0.2.4"}, "files": ["LICENSE", "Readme.md", "index.js"], "scripts": {"bench": "node benchmark/index.js"}, "gitHead": "7ac2ea3977fcac3d4c5be8d2a037812820c65f28", "bugs": {"url": "https://github.com/component/escape-html/issues"}, "homepage": "https://github.com/component/escape-html", "_id": "escape-html@1.0.3", "_shasum": "0258eae4d3d0c0974de1c169188ef0051d1d1988", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "0258eae4d3d0c0974de1c169188ef0051d1d1988", "tarball": "https://registry.npmjs.org/escape-html/-/escape-html-1.0.3.tgz", "integrity": "sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCTGg/6nY8LmCbKojGALSVEsZ/nLzu4iibasvRSRY18LgIhAMh/99YA/q2JoZwOY9Hei0TEv8FxjxaHcSJ3ejfvELXk"}]}, "directories": {}}}, "readme": "\n# escape-html\n\n  Escape string for use in HTML\n\n## Example\n\n```js\nvar escape = require('escape-html');\nvar html = escape('foo & bar');\n// -> foo &amp; bar\n```\n\n## Benchmark\n\n```\n$ npm run-script bench\n\n> escape-html@1.0.3 bench nodejs-escape-html\n> node benchmark/index.js\n\n\n  http_parser@1.0\n  node@0.10.33\n  v8@********\n  ares@1.9.0-DEV\n  uv@0.10.29\n  zlib@1.2.3\n  modules@11\n  openssl@1.0.1j\n\n  1 test completed.\n  2 tests completed.\n  3 tests completed.\n\n  no special characters    x 19,435,271 ops/sec ±0.85% (187 runs sampled)\n  single special character x  6,132,421 ops/sec ±0.67% (194 runs sampled)\n  many special characters  x  3,175,826 ops/sec ±0.65% (193 runs sampled)\n```\n\n## License\n\n  MIT", "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}], "time": {"modified": "2023-11-16T17:38:16.493Z", "created": "2012-08-20T22:54:26.303Z", "0.0.1": "2012-08-20T22:54:27.744Z", "1.0.0": "2013-05-30T15:28:48.920Z", "1.0.1": "2013-12-20T22:58:20.934Z", "1.0.2": "2015-06-06T20:25:02.222Z", "1.0.3": "2015-09-01T04:47:22.713Z"}, "users": {"omgbbqhax": true, "goodseller": true, "simplyianm": true, "vicjohnson1213": true, "itonyyo": true, "jcottam": true, "joris-van-der-wel": true, "santihbc": true, "radicek": true, "nickeltobias": true, "erikj": true, "tobiasnickel": true, "kankungyip": true, "onufrienko": true, "wangnan0610": true, "sunkeyhub": true, "ungurys": true, "abuelwafa": true, "antixrist": true, "mojaray2k": true, "stretchgz": true, "kodekracker": true, "shiruken": true, "rocket0191": true, "ahmed-dinar": true, "bplok20010": true, "drewigg": true, "isayme": true, "bobjohnson23": true, "rochejul": true, "btd": true, "jhx4mp": true, "martinandersen3d": true, "jmsherry": true, "monjer": true, "shivayl": true, "zuojiang": true, "flumpus-dev": true}, "repository": {"type": "git", "url": "https://github.com/component/escape-html"}, "homepage": "https://github.com/component/escape-html", "keywords": ["escape", "html", "utility"], "bugs": {"url": "https://github.com/component/escape-html/issues"}, "readmeFilename": "Readme.md", "license": "MIT"}