{"name": "commander", "dist-tags": {"2_x": "2.20.3", "next": "13.0.0-0", "latest": "14.0.0"}, "versions": {"0.0.1": {"name": "commander", "version": "0.0.1", "devDependencies": {"should": ">= 0.0.1"}, "dist": {"shasum": "4d4128672182d377fa53618d31282a985eeb0298", "tarball": "https://registry.npmjs.org/commander/-/commander-0.0.1.tgz", "integrity": "sha512-tCOJMg2UJ7N/ai7PbIb09Ae5gwURekJ0MPmgu0u8n1Ur1I9dX8ids/QWNw3B281xTGKuSi8zINkxEDp7TjM7xg==", "signatures": [{"sig": "MEUCIGzAgD29K4V+7Zbk/4+4T+MYkiC1f2xhdJ4H1tNVjhmpAiEAuMdFu1Dhd9x0EygujXNQtxGMu3XzirZWwZ7DbPIcs+c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "0.4.x"}}, "0.0.3": {"name": "commander", "version": "0.0.3", "devDependencies": {"should": ">= 0.0.1"}, "dist": {"shasum": "9feeaa41be6cd27a5682218cb986773e25b49525", "tarball": "https://registry.npmjs.org/commander/-/commander-0.0.3.tgz", "integrity": "sha512-AG38Y14Bs0PgWBao5p/7c9fM0k5lG6uH00KBiX7zqaSiyR74wyh8vSkR6BqryPrLZ14FBvlHCcQmjYWqN+05qw==", "signatures": [{"sig": "MEQCIE7lQBger02i/TP2JJv3H29uEIzhB8cMW+HNDgxdiJcYAiBa3zN5Ypv6WHH0sd9lN7vI4sWCSH9d+GN4OJmz0hfGlQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "0.4.x"}}, "0.0.4": {"name": "commander", "version": "0.0.4", "devDependencies": {"should": ">= 0.0.1"}, "dist": {"shasum": "72206c96453f4475c0a6e0f041707b217bef8331", "tarball": "https://registry.npmjs.org/commander/-/commander-0.0.4.tgz", "integrity": "sha512-oEmRSRK81PzVDy8BNNXfxv/QzOf+fGKeJ9Jq7Lzxljsx4QH7wEQQA92ZOe2C8GcLNRW8c3wVRcVJDRVRFpQMVA==", "signatures": [{"sig": "MEQCIGl1Mf3LQS5FrJNlVVFYJX8gCJ054/3FlZGt0KWHrkcqAiBPLvLSHZFmZdPavSLMfW3YzkeOLK6Q+H9M5JTRyY6T+g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "0.4.x"}}, "0.0.5": {"name": "commander", "version": "0.0.5", "devDependencies": {"should": ">= 0.0.1"}, "dist": {"shasum": "7824fe04d5357f6dba0045fba86fffcfc843ebfd", "tarball": "https://registry.npmjs.org/commander/-/commander-0.0.5.tgz", "integrity": "sha512-Uet84SNbwy+qsSU+hDhExwhWoAn65y9W1+w0n9aOTPuMJcVwcH8RpGU7Pa9uiZqK2d/Kf50SW3drsMNtNWnLqA==", "signatures": [{"sig": "MEQCIAHfxT5VyvMjNU24ijiY/18Ip70CYCyWJyqg3amZ+HVLAiBowFwal56IUprnfiuSyQzHrW7gt2X6/VkS6W1jKoYJaA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "0.4.x"}}, "0.1.0": {"name": "commander", "version": "0.1.0", "devDependencies": {"should": ">= 0.0.1"}, "dist": {"shasum": "4f1b767116853b659106f9cf5897c8bac2c189b2", "tarball": "https://registry.npmjs.org/commander/-/commander-0.1.0.tgz", "integrity": "sha512-Co5D0DJGIK+I2RHwGIWOli42dNU3QakxwZAROH1AN0lD32HguSbgrlrfQFxe5fSM6RK3rBQdgsEWMwQIDIjPpA==", "signatures": [{"sig": "MEYCIQCltoDPf4N7EDWDqy8UBP9h9eWmfsAlyV2gTOiGJo7kWgIhALV7iRcn5Lt9T0vrw3hxtvZzFN2tT7DlQdWeWRFgGCVw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "0.4.x"}}, "0.2.0": {"name": "commander", "version": "0.2.0", "devDependencies": {"should": ">= 0.0.1"}, "dist": {"shasum": "61d495ef9c5d9d4ab0a9d168674822ae07e961cc", "tarball": "https://registry.npmjs.org/commander/-/commander-0.2.0.tgz", "integrity": "sha512-tXycDJ1VgN+9A0DVf3iQvbnZzEf+BCY+ls/nlXQmdEmvnkppjB2pCu+iX1SiCPpRdKXKdrq4kbgnPGMqRJSnQw==", "signatures": [{"sig": "MEUCIF+wOdvB5Rllv4qsudzalN18VDCq9CBqoXvi+PUR6EkGAiEA0AfIUmfSVEnk+pIaWzuUFkoFNKvbRLHiZPWOhBzBl/Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "0.4.x"}}, "0.2.1": {"name": "commander", "version": "0.2.1", "devDependencies": {"should": ">= 0.0.1"}, "dist": {"shasum": "32ca3c217ac340082bd70e1326b5bbd41fbc6cd1", "tarball": "https://registry.npmjs.org/commander/-/commander-0.2.1.tgz", "integrity": "sha512-X0rKovMH5Z1nJ1tHvoC5/IR9LOa02F3wLp/VSCqE3JOoruENwoz+gh1hAmieiAdlaEooi//eRCQnOKI1JGva9Q==", "signatures": [{"sig": "MEUCIE+SBRkwrXk1gx5vXewAtZ1sqUsR0gfbBSlZ01Pxd7RXAiEAt4/e2wjR7JxuzOkgA01almlHRIhC8jZTi4fGwqLxEII=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.x < 0.7.0"}}, "0.3.0": {"name": "commander", "version": "0.3.0", "devDependencies": {"should": ">= 0.0.1"}, "dist": {"shasum": "02cafd95f625df941eb0697b6bb540127c4778a7", "tarball": "https://registry.npmjs.org/commander/-/commander-0.3.0.tgz", "integrity": "sha512-PI7k+/YsVGSyqHefMZFaV/YktcBPuEtk9sJhGpd4usOn7Nm3rItEWx7fYns9Adu2JCtkFQk8VBpEzV0fPK7asQ==", "signatures": [{"sig": "MEUCIQCOBX1pxYtKEEiv2pvdXciHphEur6oH+mDvqXzGgOoH1QIgS6gYJyuIQES4UChC65siWXRWyLc2I+wJg1moZUIwdg0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.x < 0.7.0"}}, "0.3.1": {"name": "commander", "version": "0.3.1", "devDependencies": {"should": ">= 0.0.1"}, "dist": {"shasum": "a70cc95038d614937abf9349b0b94f5491bcd8eb", "tarball": "https://registry.npmjs.org/commander/-/commander-0.3.1.tgz", "integrity": "sha512-WH6vNSOAx1eq6wPKK2gxnf3bZ/A89cinni53VfAhhby7EntNP3eyAStVwzF0/jVEqO7/TZ0+ORZyspLZHPzalA==", "signatures": [{"sig": "MEYCIQDl3ej6HqA7Ecf9bU6h4gsYxmADOVpAJrM3lNsh71f+XwIhAI2cgsw3AR4g3uV4tgkhVP179WORTKGB7rUpvnZbSTCz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.x < 0.7.0"}}, "0.3.2": {"name": "commander", "version": "0.3.2", "devDependencies": {"should": ">= 0.0.1"}, "dist": {"shasum": "8a98a6b590d2abab04892739da8f8577da964961", "tarball": "https://registry.npmjs.org/commander/-/commander-0.3.2.tgz", "integrity": "sha512-wCUu0gLg2JakdzAOPenfK1TIOot7sMjfnCe8A6St7F0RTaMytPrP1ZDn6dfVu0xZIsshGwNZj8RJlsRvMPMAmQ==", "signatures": [{"sig": "MEQCIBlt1i92agTzdv6pqmIsqW2zYMWICVm1pAlxK5e4FbfTAiBpd1TA/NwzV/7XPiBQ5BNUsNDDRy0JuENllH+nnfrjWw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.x < 0.7.0"}}, "0.3.3": {"name": "commander", "version": "0.3.3", "devDependencies": {"should": ">= 0.0.1"}, "dist": {"shasum": "388a4097f857e9299c26415352b54d0706b06a2c", "tarball": "https://registry.npmjs.org/commander/-/commander-0.3.3.tgz", "integrity": "sha512-xDvGtjXnFJnbD74oS0G4bb/Skbuf1SVt33qKXhxXX0eubIsbk/iNzF484OyAIXM74lg1VcnE8eqcHu6mo3MIJQ==", "signatures": [{"sig": "MEYCIQCa2YH0kWKnwuDMc1iDmVKefmNUU0aV5r8xsHQOEVbtKAIhANzT1asskhJ3Gl5kG4xO0UV2wYj8ZB2pzJlC2gkG54Hq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.x < 0.7.0"}}, "0.4.0": {"name": "commander", "version": "0.4.0", "devDependencies": {"should": ">= 0.0.1"}, "dist": {"shasum": "85f193a56264f4959401bdbbce0bb09e5a8764cf", "tarball": "https://registry.npmjs.org/commander/-/commander-0.4.0.tgz", "integrity": "sha512-M3OgolicFDe3+ttllzBqekTBHNJeuljdx4aH5AczHCZYFaCA/zyO4TyHYC/9yjuBNM67MFtrEGKl1oyJPLrvrg==", "signatures": [{"sig": "MEYCIQDw4QT48TQtQd+qmVtgqbLKuQgUvuXGFDXGARBQ42vEPwIhAIgJtnry1zo9JU3ZJAYhgbW129UD8sWapvA9Yig8gaFD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.x < 0.7.0"}}, "0.4.1": {"name": "commander", "version": "0.4.1", "devDependencies": {"should": ">= 0.0.1"}, "dist": {"shasum": "85c30d8e80fb57de9a95ae9bd5084021abc1dfdf", "tarball": "https://registry.npmjs.org/commander/-/commander-0.4.1.tgz", "integrity": "sha512-aNQ4eVztznBD1VizefwNwII4wCft/LbvLSXeAi8uvz12lHDA7J2ZCytxPmmE0hgqXxuv/SbdDWO9SrrKuu23Jw==", "signatures": [{"sig": "MEUCIAi3yV/utZHTNWn5WIVsd6wdCm2BcP41dB/0JeTWblP5AiEAt9yxniB+Bdkn+SNspQ/Q+ndWhYvPYuMurUbb8qd6trk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.x < 0.7.0"}}, "0.4.2": {"name": "commander", "version": "0.4.2", "devDependencies": {"should": ">= 0.0.1"}, "dist": {"shasum": "f1872070e42d271a2a1c419981628628716ce01c", "tarball": "https://registry.npmjs.org/commander/-/commander-0.4.2.tgz", "integrity": "sha512-tJhzOn6s7lwgMtAYWifuRPwSsINVvtm+4dN4xEMbHpIeTXjeL4pKefUGpYXfHx1Z5jq3HS2wbnJpttmkwVAW2A==", "signatures": [{"sig": "MEUCIHy+YPwOCPWT/VzDHmTs+TjAITYuko25Rnmf9SCYuGooAiEA1Ak3udBVwb0lrEgBq51hbMWUXd/3yOF/i91pbw514Yc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.x < 0.7.0"}}, "0.4.3": {"name": "commander", "version": "0.4.3", "devDependencies": {"should": ">= 0.0.1"}, "dist": {"shasum": "1f9c45f5c2d314c4bc9f9a3dd5b883261fbac8fc", "tarball": "https://registry.npmjs.org/commander/-/commander-0.4.3.tgz", "integrity": "sha512-qakepITbqSBLqFR7G8cTiN3c+cUuZCtuGYiPddr+K1SZh4y2i+UII8uRaX39WjuRUAP5sP8oO4RokJ1WHjfrGw==", "signatures": [{"sig": "MEUCIQCEYE7UFWVjX0KYigcMmpKYGtFT+9F4pst+u8LER3xp1AIgHZCsL7vThs6HGPuaqOgM9kI9pK2zIoD/a+dibUjNBCg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.x < 0.7.0"}}, "0.5.0": {"name": "commander", "version": "0.5.0", "devDependencies": {"should": ">= 0.0.1"}, "dist": {"shasum": "8fe03c71e444891dbda97c7dfbb108a33a05eaf3", "tarball": "https://registry.npmjs.org/commander/-/commander-0.5.0.tgz", "integrity": "sha512-+3agkFmJqFgJWwcPJtADylbCFa8x8RkJ5OQly1Fg6bTZoLQcpL9GcLUhJ77pvqENav5C04b5eQ+kMHBv//y9Vw==", "signatures": [{"sig": "MEUCIQCx46w6+2p36L7EmkE0K3JPz43SFqetUgnk3o7WhPsu7QIgdCbqlvSLa/6/wrHku37hAlhDi5OwA0GJn08+sj51Zy4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.x < 0.7.0"}}, "0.5.1": {"name": "commander", "version": "0.5.1", "devDependencies": {"should": ">= 0.0.1"}, "dist": {"shasum": "08477afb326d1adf9d4ee73af7170c70caa14f95", "tarball": "https://registry.npmjs.org/commander/-/commander-0.5.1.tgz", "integrity": "sha512-oRF4uekGyV5otUrGRrvUUTNSZQ7R8own3s8pwAMHt6au/DF6o7CDVDoI7jl+tPzHdJS2tiAbLKqZwOj5YA5+wg==", "signatures": [{"sig": "MEYCIQCBPztQg4bgtBT7RWSphuJNgHsZ1MuBqBk5M/5fkHm9kAIhAOwzTDwoQBvCq/N2Q3rLVn8vkriPKzwTVrrYQ0qh5PvI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.x < 0.7.0"}}, "0.6.0": {"name": "commander", "version": "0.6.0", "devDependencies": {"should": ">= 0.0.1"}, "dist": {"shasum": "778f617d8a485268b0e06c02576d5a349aa25a9d", "tarball": "https://registry.npmjs.org/commander/-/commander-0.6.0.tgz", "integrity": "sha512-Dk8oKrZqmQsosZ0OmnO3cOpHY4t22WfNnUA+wbLhkk+TQtbgbLx4D0ImkhwVXZikGJsmOzb5ZymtszUMU1+hNg==", "signatures": [{"sig": "MEUCIQC15uOW+Z//ySvEc9lTyUpjjqwk4EKH4UFd+PXwzWdpfgIgAaC1DK8wlDX0rUHrMiWlH0MZGiuA4nhaCK2xyLIOdYE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.x < 0.7.0"}}, "0.6.1": {"name": "commander", "version": "0.6.1", "devDependencies": {"should": ">= 0.0.1"}, "dist": {"shasum": "fa68a14f6a945d54dbbe50d8cdb3320e9e3b1a06", "tarball": "https://registry.npmjs.org/commander/-/commander-0.6.1.tgz", "integrity": "sha512-0fLycpl1UMTGX257hRsu/arL/cUbcvQM4zMKwvLvzXtfdezIV4yotPS2dYtknF+NmEfWSoCEF6+hj9XLm/6hEw==", "signatures": [{"sig": "MEYCIQDMySj/ZB9OsSCc1J+g8qMePktWD6Xt1SvvgXNGCZ2Y3wIhANHeUxwjwc0r6hUK0s/TSla+He+z+Y2vjSulFOQ1XIa2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.x"}}, "0.5.2": {"name": "commander", "version": "0.5.2", "devDependencies": {"should": ">= 0.0.1"}, "dist": {"shasum": "f270326709a115a126cfed5623852439b8e4a3b5", "tarball": "https://registry.npmjs.org/commander/-/commander-0.5.2.tgz", "integrity": "sha512-/IKo89++b1UhClEhWvKk00gKgw6iwvwD8TOPTqqN9AyvjgPCnf9OrjnDNY3dPDOj+K+OhN9SRjYQH0AfX0bROw==", "signatures": [{"sig": "MEUCIQC/SXfP61fjPzpKIMOQVDj2X1lolGDBYG+P4To4eeAkRwIgR5wgONaSyb71s085st8/qCadUvthi9dmgPCmhqyAGTM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.x"}}, "1.0.0": {"name": "commander", "version": "1.0.0", "devDependencies": {"should": ">= 0.0.1"}, "dist": {"shasum": "5e6a88e7070ff5908836ead19169548c30f90bcd", "tarball": "https://registry.npmjs.org/commander/-/commander-1.0.0.tgz", "integrity": "sha512-ypAKENwAvjA+utibuxSPeduXV/tIX73+9IyWMkFNnbxiJTeY2xdcM8C2KZo3KEGlDnO5tSm2BVZ65QfuRcR8DQ==", "signatures": [{"sig": "MEUCIQCHaF49gjzKU1z+PJFCNqRUnzfMFVFu0/Rr3/wqz+ZEQAIgWopGzE4jaxZ0HxlV25Nh4Woe2emd2APMQUz18aN6OI4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.x"}}, "1.0.1": {"name": "commander", "version": "1.0.1", "devDependencies": {"should": ">= 0.0.1"}, "dist": {"shasum": "e2c18dc9b8f7ce51185b248271890b1af62cceaf", "tarball": "https://registry.npmjs.org/commander/-/commander-1.0.1.tgz", "integrity": "sha512-3ObOU/JSXO2XoVNLoBkeh0mMB9UHFCnZLfR5by6rrPbY5mewLT2+QCZG7ZMJyXgBmhuvevgIzu4j6CbSe0F02A==", "signatures": [{"sig": "MEUCIHMlqR1xbFaKa4fJiPMXP9mHD0SIsEHoRcRxbz+8eq9gAiEAjJ32wIgJwbkOK3QCrR9jy9PhhNrVAxe721JDDZRpkm8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.4.x"}}, "1.0.2": {"name": "commander", "version": "1.0.2", "dependencies": {"keypress": "0.1.x"}, "devDependencies": {"should": ">= 0.0.1"}, "dist": {"shasum": "b9443ef3a966fb3a77d62f2d92dc5a06f1516116", "tarball": "https://registry.npmjs.org/commander/-/commander-1.0.2.tgz", "integrity": "sha512-QWuZAG0YliuK5KKerIxHFeMd0Oc5uxQ5fOME1QPkBbZgm+io5uKe0WvP31xAWQm+Fse9Jpo6PzpTvAv7fGBinA==", "signatures": [{"sig": "MEYCIQD4qCyHi6Lya8kU20d2PLfEMMusY0zB6ANcHbgoLQaLIAIhAJi6ohcfdDX+xCutA/Tio9wbveP18CbXuLrQ5F4APUqh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6.x"}}, "1.0.3": {"name": "commander", "version": "1.0.3", "dependencies": {"keypress": "0.1.x"}, "devDependencies": {"should": ">= 0.0.1"}, "dist": {"shasum": "037451a770f85c2fbb760e2911757fd79a366e2a", "tarball": "https://registry.npmjs.org/commander/-/commander-1.0.3.tgz", "integrity": "sha512-nRE7Lp7Y7rBdpvV/XsrltPpKUqjgLh+2ZYpUkzh0kkZm1UZnBlNC5gAZMEY87PMeJ/aLaAnEzYTT/YF8XHaaGQ==", "signatures": [{"sig": "MEUCIH+PVNyPsS+EwIYEVLzO3E+f9hEO4Ipkqyp7pRmjZC1ZAiEAiXaZvEi4bCvOMhv401mOmBP7CGZWeJGrts1esItZyBM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6.x"}}, "1.0.4": {"name": "commander", "version": "1.0.4", "dependencies": {"keypress": "0.1.x"}, "devDependencies": {"should": ">= 0.0.1"}, "dist": {"shasum": "5edeb1aee23c4fb541a6b70d692abef19669a2d3", "tarball": "https://registry.npmjs.org/commander/-/commander-1.0.4.tgz", "integrity": "sha512-Xz0JOF7NqSubDnWmw7qvX1FuIpCsV62ci/gkpa2NFlm+roeMniBtbxK8QePjs762ZGsuhKaGgcb83eaBiSJ16A==", "signatures": [{"sig": "MEYCIQD6FouWG4kemFayjI+TOgAat3QpbrUywN2UANvbB3au7gIhANbtGBWlam3E2+PIorkMJwCsZCuh+xvTIkjrCBN6rZa8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6.x"}}, "1.0.5": {"name": "commander", "version": "1.0.5", "dependencies": {"keypress": "0.1.x"}, "devDependencies": {"should": ">= 0.0.1"}, "dist": {"shasum": "457295bb976e388e9dd0db52de4333e249f3d88c", "tarball": "https://registry.npmjs.org/commander/-/commander-1.0.5.tgz", "integrity": "sha512-Iil6cZ1vitahfQSTrGO3L4v3dtvnfyGpKkXN+aJV9uR24JYxhM9bUfBLat65nU7cIXzOcnkjGtfdCuqaO1caIQ==", "signatures": [{"sig": "MEUCIBdSiTvLb6sQpvUSek12wLgAtkHlrGe8+LfqBN1M+L9bAiEAr4stJ51UykZlnvpgsldu2sDeRiVVn9ynnxm280OUIG0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6.x"}}, "1.1.0": {"name": "commander", "version": "1.1.0", "dependencies": {"keypress": "0.1.x"}, "devDependencies": {"should": ">= 0.0.1"}, "dist": {"shasum": "88ab74780346d69a112d2efd30f2f4132624af67", "tarball": "https://registry.npmjs.org/commander/-/commander-1.1.0.tgz", "integrity": "sha512-R2HfyKOSPaYYmVcSSALM306VgeflxlmunyCibHIEPif4WGVA74+GRLygh7fadAPyGqR1aNz0ECDJthxu6TFTeg==", "signatures": [{"sig": "MEYCIQDGGyOhTseyIY5U2EiUMIMOM82WLHIvmCVktRhxU7YeugIhAMS4gk9wdAKchZShCQ/9rpIndI93jJeqCC1aFPLA7Exb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6.x"}}, "1.1.1": {"name": "commander", "version": "1.1.1", "dependencies": {"keypress": "0.1.x"}, "devDependencies": {"should": ">= 0.0.1"}, "dist": {"shasum": "50d1651868ae60eccff0a2d9f34595376bc6b041", "tarball": "https://registry.npmjs.org/commander/-/commander-1.1.1.tgz", "integrity": "sha512-71Rod2AhcH3JhkBikVpNd0pA+fWsmAaVoti6OR38T76chA7vE3pSerS0Jor4wDw+tOueD2zLVvFOw5H0Rcj7rA==", "signatures": [{"sig": "MEQCICVLzbdrkLYBmbOvLighz535ECbp65ULO9cDSYj5LE1yAiBfINdCoSNSzcHH9yTzhpPCkWLVDzSaiVcel4QWpe2QkQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6.x"}}, "1.2.0": {"name": "commander", "version": "1.2.0", "dependencies": {"keypress": "0.1.x"}, "devDependencies": {"should": ">= 0.0.1"}, "dist": {"shasum": "fd5713bfa153c7d6cc599378a5ab4c45c535029e", "tarball": "https://registry.npmjs.org/commander/-/commander-1.2.0.tgz", "integrity": "sha512-4AzfHvT/zLkvp+LVOHxZ02sHTuNrtoTnu8qEoJbpcL3nTnFhoNmAml1UV+96k9y5Tgz7jIjsom546WIi2iif0g==", "signatures": [{"sig": "MEUCIFE2Z8Ht81n0/PAd02vfF/f9wNLd4aZvAdkyJDPUm361AiEAiP02We2PpXuoqJDxneLWn8gbteiXPDsvrEE7AmM+/oc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6.x"}}, "1.3.0": {"name": "commander", "version": "1.3.0", "dependencies": {"keypress": "0.1.x"}, "devDependencies": {"should": ">= 0.0.1"}, "dist": {"shasum": "01e9f594426491a8baa85ebece3366685e0a031e", "tarball": "https://registry.npmjs.org/commander/-/commander-1.3.0.tgz", "integrity": "sha512-ndfuM4otja+nsHzAv8uZ7a/qD5tdV7prBCc0M8ipK7fYNO7GOOd3IkBHTHJWOP61aQ/79PCqfgm9wagqI4RBFA==", "signatures": [{"sig": "MEUCIDnrlL/vFCR/qS/acvjBSwYcdpULM8vKvJ1OfzfK2f75AiEA2IBccO5vBNd0SZBI5ITD5U+qOiOfasyiul/KM8XN9DU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6.x"}}, "1.3.1": {"name": "commander", "version": "1.3.1", "dependencies": {"keypress": "0.1.x"}, "devDependencies": {"should": ">= 0.0.1"}, "dist": {"shasum": "02443e02db96f4b32b674225451abb6e9510000e", "tarball": "https://registry.npmjs.org/commander/-/commander-1.3.1.tgz", "integrity": "sha512-tpQAw1M5L/v+kutCmx0ceB6WOLeJR+KZyj+g1tLvJUsA5ilaMqqM+90oIndVIGQnpB/IwcMmc1ov22KL08Cqhw==", "signatures": [{"sig": "MEUCICjR8zWasLb68sbJbJAkw0F5QkStBXFMrpjl1yZYTLVtAiEAsUNjRmC+ya3pB9+oZrAYBL9/weqzSAXHefTxu1aAV4c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6.x"}}, "1.3.2": {"name": "commander", "version": "1.3.2", "dependencies": {"keypress": "0.1.x"}, "devDependencies": {"should": ">= 0.0.1"}, "dist": {"shasum": "8a8f30ec670a6fdd64af52f1914b907d79ead5b5", "tarball": "https://registry.npmjs.org/commander/-/commander-1.3.2.tgz", "integrity": "sha512-uoVVA5dchmxZeTMv2Qsd0vhn/RebJYsWo4all1qtrUL3BBhQFn4AQDF4PL+ZvOeK7gczXKEZaSCyMDMwFBlpBg==", "signatures": [{"sig": "MEYCIQD7dBM2NL5oir2YzuXlqAa6L7Wvs/rUVraJIxI7BPuAnAIhAIq+WRJSEx/rxzLyti/xYyQk1HOKIRKJ6jR/FbYTc4tY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6.x"}}, "2.0.0": {"name": "commander", "version": "2.0.0", "devDependencies": {"should": ">= 0.0.1"}, "dist": {"shasum": "d1b86f901f8b64bd941bdeadaf924530393be928", "tarball": "https://registry.npmjs.org/commander/-/commander-2.0.0.tgz", "integrity": "sha512-qebjpyeaA/nJ4w3EO2cV2++/zEkccPnjWogzA2rff+Lk8ILI75vULeTmyd4wPxWdKwtP3J+G39IXVZadh0UHyw==", "signatures": [{"sig": "MEUCIGaGEY7sLxxXtBt2YKSZ8HUrM54h2Btkv9rKOd2SByWwAiEAsSzmCZAcLvCPFUhiZf4ag0RdspfUlrWJ+VOL+wI8dio=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6.x"}}, "2.1.0": {"name": "commander", "version": "2.1.0", "devDependencies": {"should": ">= 0.0.1"}, "dist": {"shasum": "d121bbae860d9992a3d517ba96f56588e47c6781", "tarball": "https://registry.npmjs.org/commander/-/commander-2.1.0.tgz", "integrity": "sha512-J2wnb6TKniXNOtoHS8TSrG9IOQluPrsmyAJ8oCUJOBmv+uLBCyPYAZkD2jFvw2DCzIXNnISIM01NIvr35TkBMQ==", "signatures": [{"sig": "MEUCIAGXiOta6KKW+i+JT1vilv6xTV6tjb9iPLglZ32dn+EtAiEAxmk2Vj5BHO2SaCzABW2MTzTxl0/DNllWyyyWboW2x28=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6.x"}}, "2.2.0": {"name": "commander", "version": "2.2.0", "devDependencies": {"should": ">= 0.0.1"}, "dist": {"shasum": "175ad4b9317f3ff615f201c1e57224f55a3e91df", "tarball": "https://registry.npmjs.org/commander/-/commander-2.2.0.tgz", "integrity": "sha512-U6hBkeIsoeE81B+yas9uVF4YYVcVoBCwb1e314VPyvVQubFwvnTAuc1oUQ6VuMPYUS4Rf1gzr0wTVLvs4sb5Pw==", "signatures": [{"sig": "MEQCIEbJjC8rZ6UE/xmPmg83eLVpHg10sZO2XGwgSqt8dp/NAiAs6UAfpT88b+bG9dh2sga6P984/jvahjSuZy6FjamLKw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6.x"}}, "2.3.0": {"name": "commander", "version": "2.3.0", "devDependencies": {"should": ">= 0.0.1"}, "dist": {"shasum": "fd430e889832ec353b9acd1de217c11cb3eef873", "tarball": "https://registry.npmjs.org/commander/-/commander-2.3.0.tgz", "integrity": "sha512-CD452fnk0jQyk3NfnK+KkR/hUPoHt5pVaKHogtyyv3N0U4QfAal9W0/rXLOg/vVZgQKa7jdtXypKs1YAip11uQ==", "signatures": [{"sig": "MEUCIGIltvNKhTdXE5ZSPP5Ey1lYq+M66cIIuqBxvbEIfD7+AiEAl7pVdIOdU5Qa6AR0gY5YtTt2ZekaXTfpSF43kCWlT2Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6.x"}}, "2.4.0": {"name": "commander", "version": "2.4.0", "devDependencies": {"should": ">= 0.0.1"}, "dist": {"shasum": "fad884ce8f09509b10a5ec931332cb97786e2fd6", "tarball": "https://registry.npmjs.org/commander/-/commander-2.4.0.tgz", "integrity": "sha512-TK/kiMz3F635LzTAaPIKzoBmnRiKX5/0zlzc4DytFvItRvXXcjCxfw1QXa9u4S+eeb+jKCaGlKSFcV+j3cRA7g==", "signatures": [{"sig": "MEYCIQC6lq8m91qW5vF2nH7m9SlE17WX5DPKWVhAE131IihX2gIhAOTqQgadZwVXN8GypaDXSpqoZD/tefCbd8E8R+gE7D0+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6.x"}}, "2.5.0": {"name": "commander", "version": "2.5.0", "devDependencies": {"should": ">= 0.0.1"}, "dist": {"shasum": "d777b6a4d847d423e5d475da864294ac1ff5aa9d", "tarball": "https://registry.npmjs.org/commander/-/commander-2.5.0.tgz", "integrity": "sha512-OWTkyOHhPiThmSv9jA4mIx9Bf27sG8MZb/c7FR5nuOEuQzCIzIaUAIopbDODfg+CBem64FtiYj8t+G+3NGuQZw==", "signatures": [{"sig": "MEYCIQCJhdDL6/IbbjGrk9Y/sfkViipZQYE8iM1R4GWbQXFaxQIhALoUobJHOr0W2+LR0bUJjuVonaShCULOR0wPMuI9UZSL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6.x"}}, "2.5.1": {"name": "commander", "version": "2.5.1", "devDependencies": {"should": ">= 0.0.1"}, "dist": {"shasum": "23c61f6e47be143cc02e7ad4bb1c47f5cd5a2883", "tarball": "https://registry.npmjs.org/commander/-/commander-2.5.1.tgz", "integrity": "sha512-1eyhrbxuz9laj/ZA8OlTxfmBy7Xy99Fw1aubQpL2swXikVEZIaJzjwDmbGKoVOdsU2pMg9sNmoEa93mhKsdlbw==", "signatures": [{"sig": "MEYCIQDyEukhFzEYgazRPd1QmRmnCoC33o0NCVIP+2wPIncjpQIhAIC8jLFMm99/G1KnJkRlMpvtmx2xn9DFrJpvWWifdW5l", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6.x"}}, "2.6.0": {"name": "commander", "version": "2.6.0", "devDependencies": {"should": ">= 0.0.1"}, "dist": {"shasum": "9df7e52fb2a0cb0fb89058ee80c3104225f37e1d", "tarball": "https://registry.npmjs.org/commander/-/commander-2.6.0.tgz", "integrity": "sha512-PhbTMT+ilDXZKqH8xbvuUY2ZEQNef0Q7DKxgoEKb4ccytsdvVVJmYqR0sGbi96nxU6oGrwEIQnclpK2NBZuQlg==", "signatures": [{"sig": "MEQCIDCGoA2tUfZIea/I/MZBlWbPyCTiyK6c5wuZkoaMTMvJAiA+QPn2Lmb6+ReKe9Oeb5Xuej4pnXPE8dGSDDUqRZepkQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6.x"}}, "2.7.0": {"name": "commander", "version": "2.7.0", "dependencies": {"graceful-readlink": ">= 1.0.0"}, "devDependencies": {"should": ">= 0.0.1"}, "dist": {"shasum": "f3d8e36f6fcb32e663cabb70689a59ea847433b1", "tarball": "https://registry.npmjs.org/commander/-/commander-2.7.0.tgz", "integrity": "sha512-oiMhn3QQ6ySav1b5daL0mPYfFhqlGrlrwC9nPzwSWBiRtu4O2Ucx390mFe1H4hCxccwV7RgrYkk2pGIaXRttAA==", "signatures": [{"sig": "MEUCIQCbatr+I0zc0Dn+0NPTQSdP2njO2UhAKnnN/6bpMm54vwIgGqgjw0XRP8FqSIU/IkBPbvyvmPIAyDF+SZZuYWirgFg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6.x"}}, "2.7.1": {"name": "commander", "version": "2.7.1", "dependencies": {"graceful-readlink": ">= 1.0.0"}, "devDependencies": {"should": ">= 0.0.1"}, "dist": {"shasum": "5d419a2bbed2c32ee3e4dca9bb45ab83ecc3065a", "tarball": "https://registry.npmjs.org/commander/-/commander-2.7.1.tgz", "integrity": "sha512-5qK/Wsc2fnRCiizV1JlHavWrSGAXQI7AusK423F8zJLwIGq8lmtO5GmO8PVMrtDUJMwTXOFBzSN6OCRD8CEMWw==", "signatures": [{"sig": "MEUCIGNVl4rt/BzpIHmUeYp6lNaV9OLCQs9eckBEjXPr+qhcAiEA4MEUWNGb9mPG75Q2XYius5H+Fuw/c0D0SLgpEYBKhuE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6.x"}}, "2.8.0": {"name": "commander", "version": "2.8.0", "dependencies": {"graceful-readlink": ">= 1.0.0"}, "devDependencies": {"sinon": ">= 1.13.0", "should": ">= 0.0.1"}, "dist": {"shasum": "117c42659a72338e3364877df20852344095dc11", "tarball": "https://registry.npmjs.org/commander/-/commander-2.8.0.tgz", "integrity": "sha512-cwCeOWJKgop/zBQ+L0iRuerHLaGtToAJ7F19k9E5d75tUwANc26J/yIwmzSftGo1oMRbRuK6h7h1SLTSU+wJ5w==", "signatures": [{"sig": "MEQCIB9p2VPzTWoSTBpCecNoLpvC8fYjOfKRq7kdKBSiSK/PAiAnDfReQEB9pxRAqQJQnV5q4rmN6ipaQ+tgo6THWjQWWw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6.x"}}, "2.8.1": {"name": "commander", "version": "2.8.1", "dependencies": {"graceful-readlink": ">= 1.0.0"}, "devDependencies": {"sinon": ">= 1.14.1", "should": ">= 0.0.1"}, "dist": {"shasum": "06be367febfda0c330aa1e2a072d3dc9762425d4", "tarball": "https://registry.npmjs.org/commander/-/commander-2.8.1.tgz", "integrity": "sha512-+pJLBFVk+9ZZdlAOB5WuIElVPPth47hILFkmGym57aq8kwxsowvByvB0DHs1vQAhyMZzdcpTtF0VDKGkSDR4ZQ==", "signatures": [{"sig": "MEYCIQCJXrJSl227y8cgT2Ozzq4PTCu08kuQPvJzV9KnBsJ39AIhALr2Ubz1gXUz5dI+Ab4jdSx2S20G8X6FsJo2kn8S8UeO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6.x"}}, "2.9.0": {"name": "commander", "version": "2.9.0", "dependencies": {"graceful-readlink": ">= 1.0.0"}, "devDependencies": {"sinon": ">=1.17.1", "should": ">= 0.0.1"}, "dist": {"shasum": "9c99094176e12240cb22d6c5146098400fe0f7d4", "tarball": "https://registry.npmjs.org/commander/-/commander-2.9.0.tgz", "integrity": "sha512-bmkUukX8wAOjHdN26xj5c4ctEV22TQ7dQYhSmuckKhToXrkUn0iIaolHdIxYYqD55nhpSPA9zPQ1yP57GdXP2A==", "signatures": [{"sig": "MEYCIQDYupOTtVbmZEwIy3Wac7LgNqK5nnVlLyw6Ks/QxHK4KwIhAMcbIu0L9YM+ipy0oufE50poN4Or11qW+bWHNgLRQKKC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6.x"}}, "2.10.0": {"name": "commander", "version": "2.10.0", "dependencies": {"graceful-readlink": ">= 1.0.0"}, "devDependencies": {"sinon": ">=1.17.1", "should": ">= 0.0.1 <9.0.0"}, "dist": {"shasum": "e1f5d3245de246d1a5ca04702fa1ad1bd7e405fe", "tarball": "https://registry.npmjs.org/commander/-/commander-2.10.0.tgz", "integrity": "sha512-q/r9trjmuikWDRJNTBHAVnWhuU6w+z80KgBq7j9YDclik5E7X4xi0KnlZBNFA1zOQ+SH/vHMWd2mC9QTOz7GpA==", "signatures": [{"sig": "MEQCIFSW3bgO12KwSYywh+z2c4J8hbaroheeI3GuO7PduGmKAiB7MX7ovG4D5pDXxAezlJL2fdIj6DASzNalwLGbyq84ow==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6.x"}}, "2.11.0": {"name": "commander", "version": "2.11.0", "devDependencies": {"sinon": "^2.3.5", "should": "^11.2.1"}, "dist": {"shasum": "157152fd1e7a6c8d98a5b715cf376df928004563", "tarball": "https://registry.npmjs.org/commander/-/commander-2.11.0.tgz", "integrity": "sha512-b0553uYA5YAEGgyYIGYROzKQ7X5RAqedkfjiZxwi0kL1g3bOaBNNZfYkzt/CL0umgD5wc9Jec2FbB98CjkMRvQ==", "signatures": [{"sig": "MEYCIQCxqdSlCyLiYmSJxsu6oGK3QLXfRzOvJK12IA2mpc+UQQIhAO+JHieh4ntdnqCq+pTSJ7kdCSg2OtZ1VZIBYtC3i5CI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.12.0": {"name": "commander", "version": "2.12.0", "dependencies": {"@types/node": "^7.0.48"}, "devDependencies": {"sinon": "^2.3.5", "should": "^11.2.1", "typescript": "^2.6.1"}, "dist": {"shasum": "2f13615c39c687a77926aa68ef25c099db1e72fb", "tarball": "https://registry.npmjs.org/commander/-/commander-2.12.0.tgz", "integrity": "sha512-0FAmW4svUhnHJzjJHrg0vHi8+3Wp5mqvZTOui03Tc0515CToaw1BD7WC8ROcY08UnTJJOr4essVYvXBSPYeV2w==", "signatures": [{"sig": "MEUCIAGaJCZfc87GzQwB7O3BzfN2yBCS6KFll4stUTUPG2hNAiEA1HzSbjGkcXO6moVpwBrmW6ofNfg+6jb+Kew8vNi+nQ8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.12.1": {"name": "commander", "version": "2.12.1", "devDependencies": {"sinon": "^2.4.1", "should": "^11.2.1", "typescript": "^2.6.1", "@types/node": "^7.0.48"}, "dist": {"shasum": "468635c4168d06145b9323356d1da84d14ac4a7a", "tarball": "https://registry.npmjs.org/commander/-/commander-2.12.1.tgz", "integrity": "sha512-PCNLExLlI5HiPdaJs4pMXwOTHkSCpNQ1QJH9ykZLKtKEyKu3p9HgmH5l97vM8c0IUz6d54l+xEu2GG9yuYrFzA==", "signatures": [{"sig": "MEQCIBk9cjIYR7V3KUzEOq8UYbYiFhLwd39Nh/v4oRnbWYiEAiBr9UgcL82AZeG7VczEbtcDIOhGFor6c1fKOeUHK8rXsw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.12.2": {"name": "commander", "version": "2.12.2", "devDependencies": {"sinon": "^2.4.1", "should": "^11.2.1", "typescript": "^2.6.2", "@types/node": "^7.0.48"}, "dist": {"shasum": "0f5946c427ed9ec0d91a46bb9def53e54650e555", "tarball": "https://registry.npmjs.org/commander/-/commander-2.12.2.tgz", "integrity": "sha512-BFnaq5ZOGcDN7FlrtBT4xxkgIToalIIxwjxLWVJ8bGTpe1LroqMiqQXdA7ygc7CRvaYS+9zfPGFnJqFSayx+AA==", "signatures": [{"sig": "MEYCIQCZIhfd7Xjw4kYrFRAvABDydO6oARwHdrntQItUvH4tiQIhAKTX4s0WbTP8pXdwKFk62oq/Y36o7QCH3Ops8iMQX2aV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.13.0": {"name": "commander", "version": "2.13.0", "devDependencies": {"sinon": "^2.4.1", "should": "^11.2.1", "typescript": "^2.6.2", "@types/node": "^7.0.48"}, "dist": {"shasum": "6964bca67685df7c1f1430c584f07d7597885b9c", "tarball": "https://registry.npmjs.org/commander/-/commander-2.13.0.tgz", "integrity": "sha512-MVuS359B+YzaWqjCL/c+22gfryv+mCBPHAv3zyVI2GN8EY6IRP8VwtasXn8jyyhvvq84R4ImN1OKRtcbIasjYA==", "signatures": [{"sig": "MEUCICJ11+5N4BEtFBphGC8BftaF2IEYHLz0D8FkTO2Xm/GOAiEA1RR/WIpdcq4FeorFTuby36+DsOGsjXUsGnWGdfFAz+Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.14.0": {"name": "commander", "version": "2.14.0", "devDependencies": {"sinon": "^2.4.1", "eslint": "^3.19.0", "should": "^11.2.1", "standard": "^10.0.3", "typescript": "^2.7.1", "@types/node": "^7.0.52"}, "dist": {"shasum": "7b25325963e6aace20d3a9285b09379b0c2208b5", "tarball": "https://registry.npmjs.org/commander/-/commander-2.14.0.tgz", "integrity": "sha512-okPpdvdJr6mUGi2XzupC+irQxzwGLVaBzacFC14hjLv8NColXEsxsU+QaeuSSXpQUak5g2K0vQ7WjA1e8svczg==", "signatures": [{"sig": "MEYCIQCNOKtsUMm+0OC26EpAOVQJV7lw+A0Mfjx+MQWIS55/OAIhALz2zGKRMCbrCeumzTAkR31J4S7jhi7IH6S0NwvlkZbq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.14.1": {"name": "commander", "version": "2.14.1", "devDependencies": {"sinon": "^2.4.1", "eslint": "^3.19.0", "should": "^11.2.1", "standard": "^10.0.3", "typescript": "^2.7.1", "@types/node": "^7.0.52"}, "dist": {"shasum": "2235123e37af8ca3c65df45b026dbd357b01b9aa", "tarball": "https://registry.npmjs.org/commander/-/commander-2.14.1.tgz", "fileCount": 6, "integrity": "sha512-+YR16o3rK53SmWHU3rEM3tPAh2rwb1yPcQX5irVn7mb0gXbwuCCrnkbV5+PBfETdfg1vui07nM6PCG1zndcjQw==", "signatures": [{"sig": "MEUCIGv0LqMLRutJqlMQSqy2Z8tCnLh3JWR+k0FYqXt2b/RWAiEA7OLkq2XI8Qit/97G/Bhu5EtXfMJmNU+WTwm4AwKwzm8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58015}}, "2.15.0": {"name": "commander", "version": "2.15.0", "devDependencies": {"sinon": "^2.4.1", "eslint": "^3.19.0", "should": "^11.2.1", "standard": "^10.0.3", "typescript": "^2.7.2", "@types/node": "^7.0.55"}, "dist": {"shasum": "ad2a23a1c3b036e392469b8012cec6b33b4c1322", "tarball": "https://registry.npmjs.org/commander/-/commander-2.15.0.tgz", "fileCount": 6, "integrity": "sha512-7B1ilBwtYSbetCgTY1NJFg+gVpestg0fdA1MhC1Vs4ssyfSXnCAjFr+QcQM9/RedXC0EaUx1sG8Smgw2VfgKEg==", "signatures": [{"sig": "MEUCIQDTg/dq1YxxVio2+IsYyUqnmnryX5fN9RQWP+Ca9DsduwIgBVWjgpuwZQGjzEhCFpQc1rAUjCxWpq/ItEqSEgiQuBo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59781}}, "2.15.1": {"name": "commander", "version": "2.15.1", "devDependencies": {"sinon": "^2.4.1", "eslint": "^3.19.0", "should": "^11.2.1", "standard": "^10.0.3", "typescript": "^2.7.2", "@types/node": "^7.0.55"}, "dist": {"shasum": "df46e867d0fc2aec66a34662b406a9ccafff5b0f", "tarball": "https://registry.npmjs.org/commander/-/commander-2.15.1.tgz", "fileCount": 6, "integrity": "sha512-VlfT9F3V0v+jr4yxPc5gg9s62/fIVWsd2Bk2iD435um1NlGMYdVCq+MjcXnhYq2icNOizHr1kK+5TI6H0Hy0ag==", "signatures": [{"sig": "MEYCIQCVzQTQOJSWLqfWMwy/+3ADNmkVcpY30cBG0lxHeX+cDwIhAMwurGXChhtOtwsmb5mA7mQABW/x+daGiFwiUPRBKisA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59781}}, "2.16.0": {"name": "commander", "version": "2.16.0", "devDependencies": {"sinon": "^2.4.1", "eslint": "^4.19.1", "should": "^11.2.1", "standard": "^10.0.3", "typescript": "^2.9.2", "@types/node": "^7.0.66"}, "dist": {"shasum": "f16390593996ceb4f3eeb020b31d78528f7f8a50", "tarball": "https://registry.npmjs.org/commander/-/commander-2.16.0.tgz", "fileCount": 6, "integrity": "sha512-sVXqklSaotK9at437sFlFpyOcJonxe0yST/AG9DkQKUdIE6IqGIMv4SfAQSKaJbSdVEJYItASCrBiVQHq1HQew==", "signatures": [{"sig": "MEUCIQDq2mixK5WslstzP3Nb+QjIbd5DURJV8Msvwd3jVUQ60QIgGWJkqHrw7Ez/6S32VXJP96FN9YV6LbfN76MAQ+Zczfc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 60770, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbNf6TCRA9TVsSAnZWagAAeOcP/1GvW++kyIoNTLX/kcWn\n12krx6uUADcgK+gqzipj8GNP2xWVfXO95CiKB99d9bufa/okMMuDVYnUMCjN\ne0Z1ytDg6ZEAG2aQd5eTxUMLGBAkvQXTq3iRmuFwS4syW1RpFir2wXJST+dp\nMukBMYzaMM9Jl+DcLZ2TJqgkmI0nqW1Da/ItMB4wVmSJ21FShHQuCQg2VW1h\nR7d1dDXCUt5uwkmIPxqE7v/o4iGWNlLludzbdqKZs/uddTITq983ASwhxGXG\nFjvAPR5jDwOpalIGgy28blTyV+mHM4XK1QekMxW1eAJuUkD6YSXAIets/v3t\nWlY4Lj3Jq3Q9jWp08iwLiSJjv7MtfMlMZH84zJrqTo5XxVvbl2z0u2DM+Wq+\n93mkWoVVLJpC5m5n7GoCG2rTFrxak7hkOcqA4yAB8IEPbDqRmncYrWwrgTcm\ndWwVUlXe0Ij/ZYOxjZETZls2Ed78MNh0rLgZGo4m87z7wGV2lmDDfdsvwReQ\nw8MR4b0jglgfPQ/bFgo0qXCWOLrfCu4ATPS8DWF7ioAus8H5gNhyrji4ALyJ\ndePUf/IJ7hHj6B9AKQ86RIM8pU4ma2fsSG8EkHZ/7GZtcdivNqAsXY44JPX4\np5Ux6AaC88RqeCcIG+aK7LGR0F1E4phVcyKDhtYXryW3oz+XE+D7lT6GNrw3\nxApX\r\n=gqzQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.17.0": {"name": "commander", "version": "2.17.0", "devDependencies": {"sinon": "^6.1.4", "eslint": "^5.2.0", "should": "^13.2.3", "standard": "^11.0.1", "typescript": "^2.9.2", "@types/node": "^10.5.5"}, "dist": {"shasum": "9d07b25e2a6f198b76d8b756a0e8a9604a6a1a60", "tarball": "https://registry.npmjs.org/commander/-/commander-2.17.0.tgz", "fileCount": 6, "integrity": "sha512-477o1hdVORiFlZxw8wgsXYCef3lh0zl/OV0FTftqiDxJSWw6dPQ2ipS4k20J2qBcsmsmLKSyr2iFrf9e3JGi4w==", "signatures": [{"sig": "MEQCIDec3zI0RitVz7HQJjfAWIeUzxMq8A2Kgq+nGLoieNjFAiAjvzm7YyPJWmlq/r72VzIAcpFJ0kHertC4LecY8vr6WQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61027, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZO69CRA9TVsSAnZWagAAPs4QAIHuGLlmIFZIvEdLxDd3\nh349JSxqLuDUfbGRvUD/av80KRl0woSw9gP8MypoS6MNjSrBF1de36uoa+/4\nEvmt71OEWnx5TGUPAwFv3/RxL/rQehFq9v86WpRY+PcRuOue7qLI7v2azRMa\nbJ1zc3KLaJzUnO3B/n28RQww2EK0XH2iDI1XQKM3zwEgfOGJL4pHTWP2af9Y\nHHLX1+n8xBN5/HazRAZabAxMCVxnYf8Yre6PHVuq3kWFV0nvUQS7Sfj8tnvr\nAGGFvto5M062n+Ze0a5gpw43VZ3D0GznidlehiYWUoleLe6d/0vcvotMs93U\nkRtLloc6wMtQiwuvHUuS4JaExSB1rSQRXw/GISrIlODDwbNwHgcfBRYdpfY4\naopWzIz8CWVgQBMJ83EDMHyCEZYQpIEpXgmOD/K1etvfjLWeBoLmV7UFRjZY\nQ5Js8u73VGh15Yvz3QBMLIKQE0xh+HeQzNjCR0WDocGhHp4jWjomSHac/J3B\naMCqqKkxT/qHMuZ8z8cWBZZ/bhjbrDIb1jBUDL5eenCG/fWrd7CJNeJmKnO/\nifLs8GdtAn+U34otn5548cw6w98M3Wi7t4f/lcP2tAgwoNBnKlkLp7nT/Mri\ndbta24L4uNUfVI4gQgWS0GzSi70Br/LP7U8LVIOiGFKEOP/tzNeVxlGv7iUq\nx50C\r\n=bZ8y\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.17.1": {"name": "commander", "version": "2.17.1", "devDependencies": {"sinon": "^6.1.4", "eslint": "^5.3.0", "should": "^13.2.3", "standard": "^11.0.1", "typescript": "^2.9.2", "@types/node": "^10.5.7"}, "dist": {"shasum": "bd77ab7de6de94205ceacc72f1716d29f20a77bf", "tarball": "https://registry.npmjs.org/commander/-/commander-2.17.1.tgz", "fileCount": 6, "integrity": "sha512-wPMUt6FnH2yzG95SA6mzjQOEKUU3aLaDEmzs1ti+1E9h+CsrZghRlqEM/EJ4KscsQVG8uNN4uVreUeT8+drlgg==", "signatures": [{"sig": "MEQCIBJfoMf91v3Lk2MPJhn90TS2mBBALaNroH2FX8A/p4fiAiBkFALr9q441VMD1e7FPeqwHx/trotcDrz0+M32cSZ1KA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61159, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbaYBDCRA9TVsSAnZWagAAl2QP/327vj1GeSPmzfzN1vJW\nS6v21X14g4oQ+5+r+EcZ94Q5fgcloOJcR05Ad6yD50uzlHvGjux9dqe4prBg\nkp3jIt58NHz1c6oXmZ5SuVILrIOv3J2xrxoH9OC2ZBwBSejp1iZsEGLOByhR\ns2sV/zjgfrD9FCHT0ZMyRixkp9wFBw5ncfzMnFvu6L952t41e1dgnoJutXQT\ny3F4/f2H9nUSvw5llrkVyhZ2iZZBuZxZzNEgBKxpOwdhHQsnuWpTQ5PUssAL\nnRLc2Kk6Txi5bdvEYziH1yzHq42EPYW9LHWpGiR2SXl+Elbdkcc2sFHU8qd+\nITA2k6GEMo0Evpv9Wt3CACs0OoyShnuNz8J03WYh9Pda8dvZQ2h/NHNzYaGB\nUwMmwiXUL2b8iY490eZQCn4vYvKnGBkiTHXeGVay+BwqGjpbE9NdhSou0OLJ\nJs/wPPOXDJLNELLxs+R4jlk2tn9flYsC1bvz3y9tuQE7hQtYLM5+KXKUucE7\n5u/wbfSHICgLiAbU1ko0E6EnkGxGojeleM4/EYyW8c1sO5Owx4qQRuBEUOUN\n/FREwjY/8/MLI5F5bYhoRhhRQmdgUjRVYlQolj0DG4d0QZBBobsaGcCnw9gz\nroYMYAW1WtEcYhmE807vv2n1nEiUVjePqt5Aunpuf4/heqzRC7jQHtsEDWOX\nzE5d\r\n=rq5H\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.18.0": {"name": "commander", "version": "2.18.0", "devDependencies": {"sinon": "^6.2.0", "eslint": "^5.5.0", "should": "^13.2.3", "ts-node": "^7.0.1", "standard": "^12.0.1", "typescript": "^2.9.2", "@types/node": "^10.9.4"}, "dist": {"shasum": "2bf063ddee7c7891176981a2cc798e5754bc6970", "tarball": "https://registry.npmjs.org/commander/-/commander-2.18.0.tgz", "fileCount": 6, "integrity": "sha512-6CYPa+JP2ftfRU2qkDK+UTVeQYosOg/2GbcjIcKPHfinyOLPVGXu/ovN86RP49Re5ndJK1N0kuiidFFuepc4ZQ==", "signatures": [{"sig": "MEUCIQDGoYSS6pmqtUrRd8HaBJxXV5YW0m0skn9Pbaid+CT9KwIgHHu30AUcan7UlWNA1KR/10QGVa4dpzcm+xYQ4SkvK7M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61367, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbklKICRA9TVsSAnZWagAA3UsP/2wOSXhOIU8UZC7MzbKq\nO1eUAOk5lidElUWiWBAnMv53DD98JGThTERjePa5L9bndPOKeJ/bI3mW4inB\n3Do+gpeXgEP2bTgVUPgZy2kJQhF4Is67p6D9cg/C5zAocyhKq0+m93Q7PKbb\nfpK69lD7T9q88h5+wvwjUCyJbF+tsdDJkgVI0jQEyimXqtPbM1Ici8jrAl/h\nkZakZ/IhwsbAaksX0nKG2Da27KWzAF7VrqkNyHSmhgBHGDG2De3mfOstHo0C\nJYx/WTAlZvPLECzMpF+Q6+tDLAj+XyoXuwYMGSHMjbxg4IF1h1qZMBfxx1yM\nOdpc1aSGb0+NAWqm1ksX7tviAk8laR1VRE7y7EciUN7FTZUYTgNZDQ/r6Qut\nLDuiUEP6NYIRmBJqxH4jbrU8zsaBJw9tutPTGkNWp1LajOq79BR2OGUGgHlI\n4WYRZMbiBmntPxJ5hjKsaz1CY8s3Cxk7y9oDAYGWH57+ddBAxKRY726yG6an\n7i5NbfBqoFe1HNUQQ8qpTLp1Hf/Plt02dIMevscye0oaofH9UBTDd38AmKc3\npYLB1zgF2SQmdpYS2DZkadUSmHtQ2Wwd5uRSBlSVkKklFAZLQVbfsKpLK23c\n6XnXrgplszdTA/fIqxsZR59n9SRaasITF+QTxBNpeg5B9HS6z6nWPIC4ravz\nRXw6\r\n=q4UK\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.19.0": {"name": "commander", "version": "2.19.0", "devDependencies": {"sinon": "^6.3.4", "eslint": "^5.6.1", "should": "^13.2.3", "ts-node": "^7.0.1", "standard": "^12.0.1", "typescript": "^2.9.2", "@types/node": "^10.11.3"}, "dist": {"shasum": "f6198aa84e5b83c46054b94ddedbfed5ee9ff12a", "tarball": "https://registry.npmjs.org/commander/-/commander-2.19.0.tgz", "fileCount": 6, "integrity": "sha512-6tvAOO+D6OENvRAh524Dh9jcfKTYDQAqvqezbCW82xj5X0pSrcpxtvRKHLG0yBY6SD7PSDrJaj+0AiOcKVd1Xg==", "signatures": [{"sig": "MEYCIQDUq51T5Rmhp0/+4T9ufdoOu6hB4Ltc7II8jECcKCIqJgIhAPMVwdgLm91eAn7L1omeB21/AesSBN14hXFZ1DDLfD+N", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61383, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbu9FSCRA9TVsSAnZWagAAc5YQAIADsKVX+3i2QDdUwZzo\nMltfEFOMP1vIiiD0jgsOe6RWO0AwEIBds7dFc4bV4oRoOraZhE5gMAPGd1Y4\n7jekExbT/VYWZYJg+Pg2PppiD5W2PZRS+0LfGiujcyRlpFIsKKc7dUOEQ4Ty\n+htXGTetO5qeYH10dso+9Rel8dsIAKBMQycKS5cuEk4SQC7VjwalM2KJUeC/\noHRzLUtfw64zGGFoshAOWCdsuuMpKvjOiO70mZbmNF0h4Cqd2gZlzdA/XiFi\nM4//gqKcl2hAa6S2Y6cd6vb4P1azv5S2Go2tARL1OBhuI6ooofhAfmfClDl0\n7lfkGwtKkc/c5RgQLAyaBiT73WsAy7bSFW9HPDZNJvO7y4JdI4ZczSi5JeeQ\nt2/Zw5pnOWrIzygMAvhiXC2c8BC8BQ+8CuxuvCkPzonr4F56fC54JR0ANHWa\nRkwvQVWI3C5T1YX21ZzgsvaXaY460sXEksT89stxLfzN0FfFxHnwjRn3crX8\n6RltbIevb2gT8QyGL1WmwMdp94IkBpw+7LfLrEl5o95v8IAJr6k822A/bFiw\nOYAGTJ9OgtEuJd7N8a1kCEkF7u54Yd9YO9vfPkO6SnEMiENW96wuzFBpv3K/\n/M0+4X8VDPXjEsS8wfRhUYYawhhDcE7/I9nnbngbA66V5Ci3d27ASkx0i6XX\nZ798\r\n=7lGM\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.20.0": {"name": "commander", "version": "2.20.0", "devDependencies": {"sinon": "^6.3.4", "eslint": "^5.6.1", "should": "^13.2.3", "ts-node": "^7.0.1", "standard": "^12.0.1", "typescript": "^2.9.2", "@types/node": "^10.11.3"}, "dist": {"shasum": "d58bb2b5c1ee8f87b0d340027e9e94e222c5a422", "tarball": "https://registry.npmjs.org/commander/-/commander-2.20.0.tgz", "fileCount": 6, "integrity": "sha512-7j2y+40w61zy6YC2iRNpUe/NwhNyoXrYpHMrSunaMG64nRnaf96zO/KMQR4OyN/UnE5KLyEBnKHd4aG3rskjpQ==", "signatures": [{"sig": "MEUCIQCc/z5c/aj49ttTzeEpU/X07nS7ynhIStYA9HSa3a9CuQIgMiS5h6QVmXwMXk1dEL6nkq0EfA0Op41lGxCMhcEQrRk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62223, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcpAE6CRA9TVsSAnZWagAARXcP/1Dlmv/NEiARtvdTN0Fo\nZoBKmjwWrgyT5oLE1efAg47RdO1zDQLWD2y5pUfW3RgdEPek6cmPTwjnijbj\nF3fuYCChl9ipkbkQMPhfN7eIv13pz8muVXvo9Rt1HF7rQ9jm25+lorzaOHL4\nn/ew0TNvmjzvxEi3cm9hFTYA2QMvGo6GnFh/7Tp1BZcVM7xC6eJRB3sD89vo\npv3CZ0R/JhZkLiKJ1eljTnE7Ch1YL739tUZDFGmkwtko0WPiiKmKuLseTokb\nKlvc6q6Qmy1tAzf8v0gCET/KSUzRXByd1XXaONQX7WNQRBjF4uit1GGxaMVB\ngTNR6lcYPqoLQ+9td4xnxYLKYN3xIKRvzex59PKS79j6D8PW5SwfHJG6mIDm\n3HWkZRHT8MRQcF792ZcwvaeOe69W2fty+7tu4FQxmSQ6ajgSem/mymkePOre\nkBIUx6U3pRq+qo8SuC0xHJaHehUaErhz76VYuJOut9DVRul9QNlD9c/e6Bls\nQ3zgSlrrzGoHddAB9DG+vQaDmvF7twV+oWYfzDxNwxNlOATjR0cXod3230EW\nEzs7vDpx9UrJBhqKAOxga/oYWzqx2GvC5vuXZcnMnNQ+3xejO08gBPWzNEEc\nMIGsO+cU9Kyd+9UJWQRBE7kH/6j5Bh54qyxNskfc+hpJ3UyBgKUJBsUWm5um\nj7Uc\r\n=zhg8\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.0.0-0": {"name": "commander", "version": "3.0.0-0", "devDependencies": {"sinon": "^7.3.2", "eslint": "^6.0.1", "should": "^13.2.3", "ts-node": "^8.3.0", "standard": "^13.0.1", "typescript": "^3.5.3", "@types/node": "^12.6.2"}, "dist": {"shasum": "c7555e26ab809878560e8a3533220a4d37996b27", "tarball": "https://registry.npmjs.org/commander/-/commander-3.0.0-0.tgz", "fileCount": 6, "integrity": "sha512-b/YlgJi4Nn53KWvxsOU9Mz1Lr+ZK4tMUyDOk2IvB+Hki5eefYZusNOvLls91HeI2oWLyxJ3KMEU9QeEaRtTPFQ==", "signatures": [{"sig": "MEUCIQCEqHSHIg6yulNLsPOU4ZJfhqwikZheVXkZsMKwAytdIQIgbtr8tGlDXHm5OtMAsoa6+vqry7N1ZIznGo+C0ldGUF4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73303, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdO7kUCRA9TVsSAnZWagAArrwP/j8rZpV/bcExP07qqGe+\nX9ZFwooqq8/9XLIAumCnaMO8vAbqJBk7aphBLUkXorhS4Sqh3Rhc1wdErgeP\n05yN2jd/GOzzPAQqqSOzIsP4jFTFyqb2wk3LJJLtf1VtDGHPh89mon4A2mP/\n4CjqMaSOfIzZEeUCksToOpk+pxlmodALSt+NBMh76KiDKWNpqS1slnZ9VZ5V\njIV58Z2AVBdE7PmaJLo7tL/WMG8rWWXnSLyNU06GGJICVC7PTr2Kdex+MYRB\n+px8ehvq6tqbSW2AdVhyk+QpKTRdbTLDxeukGPm5m2fO84jGe/Y1twCpIrdM\n3cNx8Smnqy825feY4RmMTCulcULK9WKPuBp9PBANtYPQqnoRD9hQq3b7lN6r\nz/ZxkuzBY8prgwppniGguZ3zCrA2lNzL3pHWUXhZ9JNvijTwiR9OPev4F2NE\nTty5YHv/mFAQ+kAOIOtGSHj4lOYLwtR/PJaSiq1uCk2np1KJSREFCyTCxFgV\nQPtPFp7XR7V94Uc7qrm3I+0n579FecbMLWQfrXNSZfFT0juEZyp7aSXJQ+QA\nGGZVeP8QBUZRyhZkxFvI50jXqq3nI+tNXReP24TG++RbcY0L3SltORDC1bFP\nKy9FweZbcpAogQRAzBDsdWZ6oTJYjbDL0VtPCoIG2MhUXQJvjWNnxy6f//di\nm3bZ\r\n=9Csb\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.0.0": {"name": "commander", "version": "3.0.0", "devDependencies": {"sinon": "^7.3.2", "eslint": "^6.0.1", "should": "^13.2.3", "ts-node": "^8.3.0", "standard": "^13.0.1", "typescript": "^3.5.3", "@types/node": "^12.6.2"}, "dist": {"shasum": "0641ea00838c7a964627f04cddc336a2deddd60a", "tarball": "https://registry.npmjs.org/commander/-/commander-3.0.0.tgz", "fileCount": 6, "integrity": "sha512-pl3QrGOBa9RZaslQiqnnKX2J068wcQw7j9AIaBQ9/JEp5RY6je4jKTImg0Bd+rpoONSe7GUFSgkxLeo17m3Pow==", "signatures": [{"sig": "MEUCIDvLESlJv46u5FjCuNhRFJz25idUicXNldtXUzIn99sCAiEAwPYy9DrfW1iMLNSHT20A0wZpHMqPRGasIAzVVOY45ko=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75196, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdTMJ8CRA9TVsSAnZWagAAC+UP/RjuPatKnwcfPeI7gZQd\nVDRsyzHJGmSlxcpsejCctPu/AuRGZ6kxzcKcSNE4m+l+1GyRkT45DJsf0tL6\nWWB3muf7lHWWiprf6bMaC39BBvr5LDXbHfHFk+MSc7CVhFK9Ekl1KSD6MxPs\nanKzcnrchestrWTdzZGV/Sw54gtcGXoOmHMrxQJqaw+kPB9Vt+55q3+SCVdL\nUTIoIVFdKLzavEBdeRzPGV2j48zFU3dmOaEVcHXZgGFZweAKUMVqCGDYpfBe\nU0cIAbQNCRbLvE4g91cccqplSq+6OZRVcMjY7xxDjEs/gXIAEdR8x7xu6QpI\ne5v3JM+hm3n+hzV0Gm5803zCOXnopu9f++pHXXWFJ59Ysu3Emjca3eBDEcDX\nT+FFLcAQV5/PEE/3pbW3UGoHCwUKlaABYORSzD/fzjutFpehR6JGVzkFhGCt\nVkoaOQy2YBP5AltfHMF+nl+tOc8HL10XPJicll7/MDfX33Jsq/R9Nc1qBSeb\n+5YdQ3Zb56s1mHWPcbUKfksUoCuK3xCPQTlcXOgKrQ9Hd/4TnULbiYnSz8rY\nJVh3KxKk0L3nQWOv1JhgtltuRF634bqHQpEBTgIvXyJxf+0Jt3kDWZQcoq/Q\n4FfhbgCks442wkYf13rZzkCKcCELYUTayeLdV8GXSlXkykbKCm3stPzZPwGF\nb1lJ\r\n=YAJ3\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.0.1": {"name": "commander", "version": "3.0.1", "devDependencies": {"sinon": "^7.4.1", "eslint": "^6.1.0", "should": "^13.2.3", "ts-node": "^8.3.0", "standard": "^13.1.0", "typescript": "^3.5.3", "@types/node": "^12.7.2"}, "dist": {"shasum": "4595aec3530525e671fb6f85fb173df8ff8bf57a", "tarball": "https://registry.npmjs.org/commander/-/commander-3.0.1.tgz", "fileCount": 6, "integrity": "sha512-UNgvDd+csKdc9GD4zjtkHKQbT8Aspt2jCBqNSPp53vAS0L1tS9sXB2TCEOPHJ7kt9bN/niWkYj8T3RQSoMXdSQ==", "signatures": [{"sig": "MEQCIA/CaiG6uvCXdd6D6qnU2WTlhCGiZNnAiKfS9ca5+B+JAiBAvKiGyKNSH2Rqh9ZlHJ3Z880gKc/2k5wpHTf//P5t6Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77846, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdaOSLCRA9TVsSAnZWagAAqh0P/291MbV2QUq2ft6zXOFI\nNZZ32+k1GVp7z4U7ho4LqeVlfb+Pl/VKCkuxt6Y7wZrDJIIAdfxtOn1BIgz0\nrN//DDXGBepnDpbTwtSGpoBktHKV35vSAtRQde2yqmSdGEevRCyJLcwFgPUo\nNObsXgzzFa6Q5mByP+e5qoxYcMP6aieVw9PZJO7/L/bp3CrA7I0bAQfoUAAh\naHlFFbFiMbfu7+sS5ldoINa02pzV7wqPd7Pcnhk1FAbC+kl4wYUHzNix3mUC\nJ0g0mJuUXb0tvTOWd5I1wQ1S4IzQpcYo0Bcn58Hq3sApeIeiBrA4VA7ECAxG\nqu9uY/aJv0pfJToBYR35ZbqX52P++QHwqjq/mLGBzKxLpzPEBCdgUD8/Dxjf\n7FwXlF0qr+7PCv1j4rbOi1lwvHpCJvugeLzrRq9erQSLIL2O1zzJ1Bhjbp8S\nOY14zCLarxLOj8tND2qahJZoiJtBMFIrr9ZFiGbTJ+ybPGQm4JfK5afXTTNx\nGD7Jnmyu6EXrk+pmp+iJLuWgdgs7YHYbEDjkwr9LOSk32hZrKsbvgPCdOZQd\nKFx9BHcrjv+BvRyxolY0CkRi8j3qPZ/8ImNm7Ukus/6z4gYUw4lmdGB+CZVj\nubzAplB3/1old2GBA/eZTDIYRYDcB8su/cFoaCFcvHLRIyIWBJZXFgUMFFRJ\ngrYU\r\n=O9oM\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.0.2": {"name": "commander", "version": "3.0.2", "devDependencies": {"sinon": "^7.5.0", "eslint": "^6.4.0", "should": "^13.2.3", "ts-node": "^8.4.1", "standard": "^13.1.0", "typescript": "^3.6.3", "@types/node": "^12.7.8"}, "dist": {"shasum": "6837c3fb677ad9933d1cfba42dd14d5117d6b39e", "tarball": "https://registry.npmjs.org/commander/-/commander-3.0.2.tgz", "fileCount": 6, "integrity": "sha512-Gar0ASD4BDyKC4hl4DwHqDrmvjoxWKZigVnAbn5H1owvm4CxCPdb0HQDehwNYMJpla5+M2tPmPARzhtYuwpHow==", "signatures": [{"sig": "MEUCIQDjOxrLLfp++1TsWeeYqkyROURlbV/16ebDhb/xUDFAzQIgeG1nVSy95uOk+38jJIbsaM4zukFoobyL1dLp/aboTv8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77942, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdjIB/CRA9TVsSAnZWagAA44IP/iYCjdRvcOZL6HI3Nm0i\nh0w6TzknRyKPDSySwajq4umoQERahfi4xVCj481SAoDoNqvtoDw0lIPBZWfM\ngrF50DW8ykRbtyl1LRQZQJiuHu0B+3FNnjh8xOQ+E9LXrQmj2h0OU+G9N8EM\nqO5zmmsZU+RfQGzZc3j6TIRxLnW+xQNUPuL6MZRp5Gap0bDnVp/sJn0XQcju\n/FTVfjLvzUTC4SMhRX7lExfPXy7ylqNwOFSBtM8mIIlwd2N2Yxq9dQcFQJFC\nlV0Rx4ZdsR1icWBXsY7iWX3whUsx79QBNKMr1RgNL2GZ5yFd8sg7bN5hcx9O\nDeo9KYJElgQ9lKeNYBnMIwQQxTDER3VjHlJ4VL1bJ1bj5ISnrA1EG0twLnpm\nb5E+HbImrHFgK8dVZFnz8QiAGyi/EE0kLEj2hb1EWaeuUle0CrSOcU9lJIIE\nX387cGPrQJT1Qo4k4nQ3W5EFgb0NEWs5OctqkFYE6uta86Q08yiTB1PRGYeV\nIpq79QOmUZO8UoNFn+C03HRW9fKfnsBfh18YitucN8w0OC8ELTmj1YAJ7ykq\ndeoEsXCYuu6ahIXnK4EveJShiyFMxNfAKgWJJw+jEtkRRxQBUb7Qp4YVpYxf\n05/4ub0MTdtAy1fd1AyN6DKM3MLIG8q5nmcKUe+LRumJ7PW5uPX+KFZzLtdX\njMqr\r\n=ORqI\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.20.1": {"name": "commander", "version": "2.20.1", "devDependencies": {"sinon": "^7.5.0", "eslint": "^6.4.0", "should": "^13.2.3", "ts-node": "^8.4.1", "standard": "^14.3.1", "typescript": "^3.6.3", "@types/node": "^12.7.8"}, "dist": {"shasum": "3863ce3ca92d0831dcf2a102f5fb4b5926afd0f9", "tarball": "https://registry.npmjs.org/commander/-/commander-2.20.1.tgz", "fileCount": 6, "integrity": "sha512-cCuLsMhJeWQ/ZpsFTbE765kvVfoeSddc4nU3up4fV+fDBcfUXnbITJ+JzhkdjzOqhURjZgujxaioam4RM9yGUg==", "signatures": [{"sig": "MEUCIB0Lt5PS+XkAGcnBcMmbZq3FLFZB+pMkPZGW+QEGM+E9AiEAi7OuLEzTCA73eNSaaJNo66DSsohuzsCwjnlNL1qPEv4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62298, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdj9hICRA9TVsSAnZWagAA158P/itguvXbWFYz25Aj9NL7\nwE3zXiaP2L6Va4JsYkm7ZlCHJ1DmrboYBvXkYFsQI29bOtIjFN5lAjgcm7b1\nh52EygFKxuzTxHbyArV59d/o9POYgosINU7/QC9Qd3Vtzi1jwAKxVoT032Uz\nELrY80U4YTJwy+rx7xJPSy1p+rCeCHpF74NdsYBNvQtrSUa3FUmq2lOLC5WV\nUV2XsOSwCG1j2GKnNnAnBy8AwRP+OWQ5SUekSAK3KXiyFylsH5hJWKbPWPQJ\ny4MAYE3wYwvx2rJJ+MUqGT/DGU6exvXrrvWIzT2QicERaFo4FFgdpp0eIapi\n6Bk3wOt/lL+v4KUNOyIdv6aixLKgO1vjxAWuj/whPEO4bIpTjn4gcPsFqDac\n8nIW6cMtKtHy6YtyjjtOWjyGkdH62fKd4nCs6uagz+Qq1TjyMfhDIKmJBL0Y\ngS7WHMsk95emKJPpXxj9H37KRcOBJEcHODlBwL8sutND5bem+CWMTDBRXm4A\nmnYS8FmMKO7Wc40I1FFPzLT7noRbN5yQvdJ6W1Fh1lqb7Al4UY8rc9S04Mgn\n+khJKLmrr9YqEYfziXWZ/KoEW86M7K11spTUdLoSPTxpubpjXz8S5fZNi22u\nURzwkK4FA0V17Sy+op1+uwnpuOlR9MNE/lzp8wEl+PHD+bLkx0hm7H+F0iA0\nK9wA\r\n=VsbK\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.0.0-0": {"name": "commander", "version": "4.0.0-0", "devDependencies": {"jest": "^24.8.0", "eslint": "^6.4.0", "ts-node": "^8.4.1", "standard": "^14.3.1", "typescript": "^3.6.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.5", "eslint-plugin-jest": "^22.17.0"}, "dist": {"shasum": "f6bae309054b2e6c870189dcc2f51555c1a4c377", "tarball": "https://registry.npmjs.org/commander/-/commander-4.0.0-0.tgz", "fileCount": 6, "integrity": "sha512-jARQuTKQXYqAbHK/WJqYHbsnHzKZg0TQVw/31gg7gr4JWFJAoB5KFJS13FpF9ewiB7LhWA+0eT0ejt1IgvCWlw==", "signatures": [{"sig": "MEYCIQDg5OXJ1vM7cEYk2Icv5j158Ps8ObSz6BpCM+zz9zP+6QIhAL37vPWFMBpI9MCXug7K3X90zQ/YF4rRAevCsiIdjKxm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85025, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdkxDRCRA9TVsSAnZWagAAXZwP/A4AfrK1oLTL2QuN86bG\ntlHd8VrVqq0KwPCtuGIB3RBMWcKyXe0FbUToIPNeBqH7r93tYP4k4Fjk2oe0\nx1MY2rbpS7TvcMN12aCqQC3/wXZHybD2eiLSHqiUKpqyTDO0iZOKqCyxACx8\nW4gbaCo+4JAfEB/mNwKikKpVTL0rtFgsKo9wMXvLintndk6Rlbol+0QeAICu\nAKUtimtZxi1AeXs2rXOiNEAGsTk2FQgn4wI+payX233v4BIsSNE/7Tab1jlS\niH/ItqVKwaHExTegoxXseOk8TFJ1fW9uxdxa8R+hgPO5ujg7RCWpumkRVVre\nM2S7dP7Xd3sjSoY27Zb0oNPE+6JVbEfwzHacSEc7OMQKB3g4nraQWE5i8VAz\nISiVpakYLiUKISg4lpDWN8YYyAQKZu3lvFW8O25Clji8QevNSm1nqheDj0FQ\njQnMGf6Dk5G38zRNeuS6qYjFCnINK8IxATDi2WfhWMRDkUcDdDNF4aaKc25f\nWLnoL6mV5Zd+l8eEEPR6ZIRWzEsN9VBaW9MI/zFTzHmQ99w6lhEPogh/aguh\nuHMqP5hBiEUI8St6MYEWI67qaimgYrxunXvVPthBUt2ffNlUtzpCVoUA3bQz\n70mIMIrnQSB+Cj1WaHL3PiH95LdgIpL+r3xwSMWzibqiI5oyrzg97ajrAtwF\nV3PU\r\n=YPfl\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.0.0-1": {"name": "commander", "version": "4.0.0-1", "devDependencies": {"jest": "^24.8.0", "eslint": "^6.4.0", "ts-node": "^8.4.1", "standard": "^14.3.1", "typescript": "^3.6.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.5", "eslint-plugin-jest": "^22.17.0"}, "dist": {"shasum": "cc00f6d884cc492aac3fe3b32ff36e5f46cd67d4", "tarball": "https://registry.npmjs.org/commander/-/commander-4.0.0-1.tgz", "fileCount": 6, "integrity": "sha512-6UCnFDyJnZfk4ZugvEhl8hYtlViGJNVki5J+3x/zNd8nLDpBcDHZShvBYWKIIktsiBUuRDttP/qG1yF+bhhVnQ==", "signatures": [{"sig": "MEUCIAIri2bgPp82yFKu1clSMz4STv/V/aUNLI911UuyMxIdAiEAqBQUfUyHuEtWdBAsgANiot60P2wSjEjQxDiBqBl9ezw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88892, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdm+YYCRA9TVsSAnZWagAAR9oP/AscfhK6wrWOAQirqHnq\nsgF0JCgHlpEPW9+tmvu7vL4kxni7K1wlCbur8y+dvyiBYnnTsWQZKDLrm/F4\nnA8TEK2HprGJ4mBsUHihfy+BbdT+OLxoez8IQCOC92ghk/yx3Xoxhqg+ik4I\neXV+S70BFBc/EnBN+yJDwn3gvQ6YGpeGPKR4+dv5vY8cx1zG4RR5CPx/pPOl\nOu86quAB90L0tVu+KXqTEGfx8rbyVKtrThhzgHUVOtAiZP3Wvpwv8Zct194O\neaDdQKfLOhSd2we2XLCCD/C8mTxi7byXb4XbW8MV2+pPrelUIkLhapssXA2n\nk388Qr3xud/2v0V7FPiTGd45Pr1iuo+a0cMvmFbWsn1buFVuDyhQLo3duz72\nyf+WIYmAxgIb+ucQ/d420boD3h/zTxMZm7V2378mOFWj0HXFRORSzHgO2RPD\nlaahbwzvfjfXdljJKZljjkAppQpdCWsUkyj1a+NJ8EbgkV2jx3qZAAnTuar9\nmckqw0KH6dmdTbIizNQsc2I3fL/KJpE42VKPOmN5i8Cd1XaMozJnHjSbFdGw\nUuu/+jCl6ADxSeGeIyPQNvD6jqHehtL5n0881e9xFv/lBAwyDFkwVkbz9XE2\nbiLFqQI8CUn5UDRncvpO9V2Du5tQKiodXpS5OIMHCxyn7vOGsueyy0iUTSYI\nmRGp\r\n=H2sA\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.20.3": {"name": "commander", "version": "2.20.3", "devDependencies": {"sinon": "^7.5.0", "eslint": "^6.4.0", "should": "^13.2.3", "ts-node": "^8.4.1", "standard": "^14.3.1", "typescript": "^3.6.3", "@types/node": "^12.7.8"}, "dist": {"shasum": "fd485e84c03eb4881c20722ba48035e8531aeb33", "tarball": "https://registry.npmjs.org/commander/-/commander-2.20.3.tgz", "fileCount": 6, "integrity": "sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==", "signatures": [{"sig": "MEYCIQDi8P97IdM/HCU/5iN1cUMTYac7x2dNO9uJn3grTlBU5wIhAJTnisAKEKNxXfo45VISgVzZa+3JpFUl+TaprmdT3PYp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62442, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdoBXICRA9TVsSAnZWagAAxZoQAJuUZ2avRahPhSmn15Vt\nRrnyt/EzaZaR9jGpwPpdSVKEyEypyKO21zsrFKg+3TjJGLQVb6gd6eXvY3Rs\nTG1JeNQKYyqcrvH+q94ZIDTMT4tzUv50MVBuSdVCAQB2liHKzcay2aMbd0bW\nFbb56rVEfixabLoYJGhpsHAamzAD51CpxwjpeKuVIYe2LDTxcDOAsMvrjxwK\nB1vsuQe4EqcuMWE0a7jtGJTHqxxqQxcvZLloxgO+kr6TGMZQ1oMtwhRUayNO\nPbN9R97nhOoV9J9AZu28O98yJXiz6p7TQ11JeN/mWF8F4LT1N5Nv227OHP/a\nVh6EtM6YPMLQU6lQTWUVa9BF6mqCMREBY9QRmh8wfRhrrCfD9oOez1qmGsx6\nnz6zqiqabuy8KlULmhyXA2ixHcKgEimlQOVDGJLy4EJIv84o+GCdVJNiP9qd\nrB7qTVbiNm9TiY22OehPADtx9QhcavZuju6xftjXllvjKN3lJFx+jbyenfR5\nBonTnPuqx4+dmUyFnmfHSM7pHO7NtzpLuEx7N/SGDtUfI6KPRBSEt7DpoaFu\nF7lABbtehgMpcR/gCOMez7BXcNkigDWHQeRqaJ+iedXrd3YLvJMQoaFgUmDU\nMyS3zLW3srq8EsazcsdlmQpVb8lRJjKnki6Bj93v0caKaT+4aAjpMtn2xyCK\nPqC9\r\n=91C8\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.0.0": {"name": "commander", "version": "4.0.0", "devDependencies": {"jest": "^24.8.0", "eslint": "^6.4.0", "ts-node": "^8.4.1", "standard": "^14.3.1", "typescript": "^3.6.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.5", "eslint-plugin-jest": "^22.17.0"}, "dist": {"shasum": "e782b6afe6a0f1b1408be59429919e1305160e3f", "tarball": "https://registry.npmjs.org/commander/-/commander-4.0.0.tgz", "fileCount": 6, "integrity": "sha512-SEa2abMBTZuEjLVYpNrAFoRgxPwG4rXP3+SGY6CM/HZGeDzIA7Pzp+7H3AHDukKEpyy2SoSGGPShKqqfH9T9AQ==", "signatures": [{"sig": "MEYCIQCKtB/nu1sVQLzj7lLMXgRk+Rn8EB+kKPJg9hveuqPlhAIhAMI6OKMu+BDEGCQfB5WUsyldjlewGe7ihxwUAl6nma+m", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89230, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdvARDCRA9TVsSAnZWagAAStIP/1aYZBwrAfiGXxnQxy5l\n4GrF1OC2pML0nVRqyMDfnd94hn+1q5lyDoLuoEr7dr3HwacDy4p1hKDQTdSw\niRCqc3Zp8ktKF1y3ZpUFFnMWvq+kaPv5U1TSVwtlFKt/ywuThpjh28g7MRVR\n4aNONkGhlPyyMfKd2N9AunC3sx7Ykz6mQlLvyGlryz4m8VcQU3rsBmcsIdzu\nPHUUCHQUeqmz9z1czJir/4NT+IUnmbGMfyw+auHrMzQmoI5tKMJ4TJfGWAxY\nsKeF614GAcIWi4CuZDtj48KVSIrsrLoGLNGuzBS52+w/Z0RGXYapYiSKWf1Z\nrP5MTl4SxaeiUlB2gzTsV0Y3iNyMnn01hLWJMCOACzGR9e4llCO2CMgxYE8O\n9fK/qeN+NW/HFnJ9pR8ReiOoCUVO29BYroh2/yzabBEIBD5Ie0VRNoDNiFKy\nFEGeOI4MzzutFTzlZ+mvS9S/sKqAaaFEo0IR5OuDR/1Kb+1DZwsiFFI70JwW\nqRMqbV+ucar2BcL4T1Nf+PTj0F/M4jVpqq4mekv/wuzZWk3h8oi6lSlInDXv\nFS4jABQXWLlPFLd7jynloPmXj8BU7H/oZEWMJX9PO+hbJ2Cx2ReGnsrxhVUo\np+gnPb6eqo7bZG8DRf2U3X/cm+C97rqWWG4fsyrCo80WgySKV38XPIT1qQAe\nRx3P\r\n=wiE+\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 6"}}, "4.0.1": {"name": "commander", "version": "4.0.1", "devDependencies": {"jest": "^24.8.0", "eslint": "^6.4.0", "ts-node": "^8.4.1", "standard": "^14.3.1", "typescript": "^3.6.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.5", "eslint-plugin-jest": "^22.17.0"}, "dist": {"shasum": "b67622721785993182e807f4883633e6401ba53c", "tarball": "https://registry.npmjs.org/commander/-/commander-4.0.1.tgz", "fileCount": 6, "integrity": "sha512-IPF4ouhCP+qdlcmCedhxX4xiGBPyigb8v5NeUp+0LyhwLgxMqyp3S0vl7TAPfS/hiP7FC3caI/PB9lTmP8r1NA==", "signatures": [{"sig": "MEYCIQCVLRb/IYRLG5vlyKfCEWwlLK7hYEvQOPCWX3VqvwyIggIhALcBY5KKXmuiyVhAxTB4I7lbw59XeJ8BAeIdYCYhbyJs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90256, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdySdqCRA9TVsSAnZWagAApccP/R6EvOeW1V7wNBqAEmU2\nIm0OW6aOuLM/b0qRTzQPuVNeIsu4l3Y/usfqZl5Hk9RRew3xAfHEJDT8sj4L\nJdRIUP5+MEe531awH5WQpLspY8VBHYsNuW13DqZyc3e/axLOlq6vC+yWVO7i\nlUmowznWvG2/X8YGJYI45lihfaavbJ5v7H8Z+mXTgBXnXQzjqH40T+ck/6L2\ntrjrfUFtDyTIMDtLDu17dTEF3vklL7TMf7w+BhajjLrCW+Kq72PSOCQPx8CP\nRpoSY5Qo8/fKzhUkpgQ0qh8M+x8NOvPdkxfzw1hV4v3mvMK/kEBKXkaBdw7y\nsRDJnU60LlPR8eGpYXMdMEzWFicOE+IK3ySxYEAWVdJCDoV/tB7dTlVXOgIn\nVKLYL/Z7adnv27GeFOO6aNhA/xyflsjstS5so7M5Ay6eyJ8R1/cUDfxjf7vz\nrJP4sPixDN+lGdVb7EGAWaMlbTEOdI7cmGE8Vf2HPgGLoREYBhkDztkFex9q\n7TngRMaUo6umxTX66LDj3NieDos3LksfOLlu+PmZU8UlHM5dMyV4PEzxj7Uw\nTlaq9kiX6Ebtn3ey7J9i/YpiIWoCeN5CTUpjiBClFKgXkgsWLBasfMWXOrdD\n0zpawMfj7hBtn5n7srEb162UD5dI0OhmkW9fsvik8S0gG5fpttVr8rQuYGzV\nJCcl\r\n=Jqbu\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 6"}}, "4.1.0": {"name": "commander", "version": "4.1.0", "devDependencies": {"jest": "^24.8.0", "eslint": "^6.7.0", "standard": "^14.3.1", "typescript": "^3.7.2", "@types/jest": "^24.0.23", "@types/node": "^12.12.11", "eslint-plugin-jest": "^22.21.0"}, "dist": {"shasum": "545983a0603fe425bc672d66c9e3c89c42121a83", "tarball": "https://registry.npmjs.org/commander/-/commander-4.1.0.tgz", "fileCount": 6, "integrity": "sha512-NIQrwvv9V39FHgGFm36+U9SMQzbiHvU79k+iADraJTpmrFFfx7Ds0IvDoAdZsDrknlkRk14OYoWXb57uTh7/sw==", "signatures": [{"sig": "MEQCIHQoGWeZlFh6isUDEas5C8c+Sos07LYvYjLt4cOOlP13AiAK6pFWn6Je0DwTmMJVksEL7/IMT5KUPy5nCFNZ+3PDBQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 92906, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeEvsECRA9TVsSAnZWagAAVaAP/jvX6NMVBFylf2XG5Yfe\nPeMK9l/TIcVNvrEgzXh2blZPtAuYHHdKIepwZPf7oVTshy0bSvoMPw/oSv6z\npinA2XDgYey322hLsBkrmv9ln/rwOmHi38Mo+WBU2Br167s27//rqoNdV4Pv\nfcc1/9CIMNZ7C78hT/Qxbk6r8bs3HSGvQe/vxmB1pm9kdivdy2PNBd/C1rXZ\nvdiGHWpuvO/C/p0DJjzu+nbJLfOM2UJ/cK1tMK1cXJbI6xEsuDW8izFOHFCr\n2ywH2KUVDnJK7dv52mHv8mKTvErUBfeSVUg4632LVR4BTbf6N3tYZxix+Udx\nuUSOMuYxEqaFYdmuT1BUO38mvmB2iW3vFcZTEcdkLXuJ0uPaVy3GmOtJaodu\nAE92QxAnyj30kd7Bp9T3LB8GDJaD0gJWx0x71WqpDqQ6yCsHi7W6hm0zzXc4\nTPTXCUKA1/QC7gErv9ZXjIoZ8IiogX/JhNoLR0rPhNR70x0brCG3xOrhac8m\nLmZq5z9Q2zTuxw4r4OhAvXmrPtBKoiZTVl/Nwep9WypUVkZdK1+FqkzNVpeF\nfSkAeGSVEH4EY1q8k4Z7kiL/9qvi/jcJZewFIiCnJXnQ6luupCnPDxEbxYmg\nA6aRl1acSacEhLfEZITjfvGot5iRaMLHssIFgQHACvWcmnMSW9xSFtEThFfU\nQelL\r\n=IVoz\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 6"}}, "5.0.0-0": {"name": "commander", "version": "5.0.0-0", "devDependencies": {"jest": "^24.8.0", "eslint": "^6.7.0", "standard": "^14.3.1", "typescript": "^3.7.2", "@types/jest": "^24.0.23", "@types/node": "^12.12.11", "eslint-plugin-jest": "^22.21.0"}, "dist": {"shasum": "22c4588cb45740cbaa27bf8163f2327a2c126a75", "tarball": "https://registry.npmjs.org/commander/-/commander-5.0.0-0.tgz", "fileCount": 6, "integrity": "sha512-LLP9hLsnl19X3DTYAZ72b5ZFxsaDNw31FZPxaJHefhat7BSjA4O7L3xFy8AuOBTt6AfOZlNYsEgzYJBE/KRV2A==", "signatures": [{"sig": "MEUCIEIXO3SwYIkkskwa95HkcH1LdMG0cNN2wUzHFKNpv6ETAiEA4Vc727ASRHZbbm8T2v5jAF42MaQG0h8aoGjYgU+V2fo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 101487, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeNPQoCRA9TVsSAnZWagAAzBwP+gLLq8hnVoqCroK9VK/2\nygLOkf3phbegwQbjQf6As/yoIplt+6kWuFwpZRj61bKiUvd7sH8qcSepv5Hd\nGSINQtbP9hF6FMaOqW5YXzMGPIe1qz8nmC5pIM2ep33Rae/Q2b/1lRJKt0ML\nNM7be/jeOKbZeAQhO4DcbzFHuV+5GtDOjEoJBRLSswOBTUN0pOmUzgZOEUJJ\nVa3ghhtQCelE5ykGB4VtF6TKlJbcX8vE/YWNSLz7ALZt91Qi5cxn/fw9WACF\n08uS+BULnhBUvhaopyfx+3V2B69KQ40nhZ7DK7cBIGFoFMnMnoj44xCnuCx4\n0mZyqaEHVnDGZ0e0MYRfmlutv77wRIfcbRY4PCxjPvRyLknHJ/xIkD2nZx+E\nVaS8ww+Zvbscdo1/xjydktibpO3QtWfPWKqgzlnyvCjEWOC2Ixxof+mTJR2F\n5pnDA4dZlV/ML0HSqFhuHPsmcD+uyYQI6enXsI1/quKDtWIVjH0qUcRp2JFA\nrORoZeXtrMVZU4/zy2KYm/dLrzDCh5cvTrXAFgPM0d8O5TR2iBZ5wAfOEmjC\nbWry0p8o4IW6L9ee3q7/VdYRiqO1LWr8z2iC4MFUmla7QLOz9Q7DRW2Ga5NK\nU0SiXUTpyju/MlgoLZ+oj0npEC88wKYP2lZct7Mf4p2xJ6doZ6AWwPpcYWmn\n3L/5\r\n=4M9R\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 6"}}, "4.1.1": {"name": "commander", "version": "4.1.1", "devDependencies": {"jest": "^24.8.0", "eslint": "^6.7.0", "standard": "^14.3.1", "typescript": "^3.7.2", "@types/jest": "^24.0.23", "@types/node": "^12.12.11", "eslint-plugin-jest": "^22.21.0"}, "dist": {"shasum": "9fd602bd936294e9e9ef46a3f4d6964044b18068", "tarball": "https://registry.npmjs.org/commander/-/commander-4.1.1.tgz", "fileCount": 6, "integrity": "sha512-NOKm8xhkzAjzFx8B2v5OAHT+u5pRQc2UCa2Vq9jYL/31o2wi9mxBA7LIFs3sV5VSC49z6pEhfbMULvShKj26WA==", "signatures": [{"sig": "MEYCIQCLxbbWdsLFDvxCF0Ne11CQnzsD97XGapRT63Z+PAkC8QIhAKzQqDOO3VYVVKFzP/ClCL1lwC/PZAntTzJjZJUKvqSS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 93231, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeN7qdCRA9TVsSAnZWagAA1YUP/Au6/s5G11d8OQETCs5N\nqQwXL1HldSNz8VBxRfCCqXF34p4Tlg8MSUxuf265a3tHAQM+cIt/9IFXPt7T\n2JFgKkenOGltg9VD9QX/uerDQBpzWpGxIFPhhw5CjZyJljIzoN/Dcffyrena\nu5O7qTVZyqo3HAqRDBeso+r0bfg2i7st7I0gA7+Dfgxw6DNyZd30qFVZ+9i2\nnNRAsh/cseVTN85OBkb3aLUL5q1fVVR9MF/A4AJrC8zAVDyS43L+tNorfuF2\n8jkInsLhjRH3Tqy9pTAjRVZnuTUapuAN1I9M+Q4BPPWpvcs3gouxG8WmED90\nmsCdrZzzFlqL9IdsY56PoPpQ2RQba9UtovOs5u5NCWNPRR91nMD5j/dN2J8K\nONVQtqXBriGE8T7d8OBozmv6vARWbQjX7FRVhI0+NruCmapRYJqJWUgyuYCF\nBvrg64U0aJLasO0/Uca1wbxDSRn7wvzbfX/A3tuO/URuvLYMdlwAUCvNmZzN\nUJI8EOkJDJBBMFQAa+awrKa1E3/xG1MjY89b5aKZb1G1/+QXfYwVqxJ7ovU/\nC8uiD0diRHx6778UafnFBoIZtWVpYPJvO0VWQfWjXPNvYnu/a3KLmn9F2iuk\nEAUhTqqJpwFen/pB1avLZ0lYWZ/PRew7HicENZBoJm2PW2lNyEBlEHHMsr6K\nSh1o\r\n=zQzX\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 6"}}, "5.0.0-1": {"name": "commander", "version": "5.0.0-1", "devDependencies": {"jest": "^25.1.0", "eslint": "^6.8.0", "standard": "^14.3.1", "typescript": "^3.7.5", "@types/jest": "^25.1.1", "@types/node": "^12.12.26", "eslint-plugin-jest": "^23.6.0"}, "dist": {"shasum": "7af5ad7324cfdbd6ac23b71d54b96336646a9d25", "tarball": "https://registry.npmjs.org/commander/-/commander-5.0.0-1.tgz", "fileCount": 6, "integrity": "sha512-cV808agy7Q4lIV8YV7zAmMa929TuQVbEIEtZ3P73T7WyWlbS6QqABihySoS4wKoFv08psv+DhagmVn3YZQREAg==", "signatures": [{"sig": "MEUCIQCXLj4CtK24AX0+X8RD68kjpZki/VHF5JK67rg6QWLILQIgFfnZ0pSLOc6GRafsN49pVpUOqh/J9EPWE43y5EFExj4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 99685, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJePx7bCRA9TVsSAnZWagAApL0P/12hiNxfOsVOZSyyUHrJ\niKDmvX+nGVCY5vBtCGeP0ZMnlRZ2dfGXIzmsV1dQ3BatSwho0Q4H+rh0RfM6\nMteIkcf0ufCYpltAKj9dvxbDgktaUR0yZ0R/AnF99NzTl9UyyQjmsjEj8kBn\n6oYtunBTAu6tC54kxjUHBG7XwxQ9RdbIWNUtncmTqbHNDhTIQrnDWYwSEjd3\nEhxzGzNSq5MYeX8+7/cRCQo4ZIEN/hD86P8NjJaY/5lHSKlfkZaJDtvHGrE2\nc4gaWqAYHujripcW68zCZCaQi9JjZ+QfN9kxjAsN22raD1K64GvRHsiwZvvP\nlrDgkFKuYa20GFiexfDi2rx3xaxmBmPqRDahB8vqYTdr2Of8YyQhs1FmT9e2\ne7l0sZpG3GM5q9+SNPYjNf8NGfqPTKUPfgBQeG7K53uLHKrDKJpeLZS/5uMU\nw/UbEHw9tCvCdtEOCpT0ok8+imNwnDxaM6XaGP0RTS6nQcEETrll/CMtfDh1\nc+iP1ndKH6D2CKr7pQWkZYYxVWMN5WtI+AQBy33JUIE/+VCQMszlbccJ/Muq\n6FZ0Ur0Xutav6XE37ltToX379Sb9gPfBO1EbCL6j5keK33aO0RIHc5oTo4jh\nIuuEjzD9XFFVTp6ikMd96TRJuXrWpT0yX0l+VM9xBq5m0RtnwLxv29TVZqfI\nzl32\r\n=clQG\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 6"}}, "5.0.0-2": {"name": "commander", "version": "5.0.0-2", "devDependencies": {"jest": "^25.1.0", "eslint": "^6.8.0", "standard": "^14.3.1", "typescript": "^3.7.5", "@types/jest": "^25.1.1", "@types/node": "^12.12.26", "eslint-plugin-jest": "^23.6.0"}, "dist": {"shasum": "4550bdd78a7616b7ed0c3e2a0f4ac83a2c641333", "tarball": "https://registry.npmjs.org/commander/-/commander-5.0.0-2.tgz", "fileCount": 6, "integrity": "sha512-9oKoqJisncoX2ppeXigKyRiFtLZreopWKdOCP9GeGESMAHETXgy3k7zJPuh+R+nm4LpDeqlPAz9kAUaRnLSlvQ==", "signatures": [{"sig": "MEUCIQCtWP67IOz+6fiGTxmo2ToZ2vo4xxp6em3uBAUmRSCslwIgYq22TQ1cy0+RKI9et4Od3Q1PAIeUO8E75hnPKLo42tw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 100325, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeQnm1CRA9TVsSAnZWagAAt2cP/3c4tuHFMMxp18wLUGp1\nYGG8m2f7CKGdUL2UvnyIgZR64nmYckoNE/nH4lljfnoN+rWtubKQ/hWGExv3\nRbshBa/9RbLetVQmqxjMHbkfncRKqJZpjQLXnlWM9Ux43O9AT4EtPoaq96QI\nzHd7VnsCDCKvZacRcCBZFP3lNzH2iUFKDPuWAVzDzsglVb3EflKX2zUFz6NV\nKctCP8DU5iCzH/q9P0jqdj/aYW3byQItY640D6kBVvB6jceY8NeColU/MPvK\nFzQ3XKG0A1qtenW4jOaaEfG89Kod5ubZnk6uqz6HCUEMPo4X0Gvog4Hvn4b/\nFMS6DOxrAXLt4oIBHBNNytqV/JzJu0m6Qrq8Qfz/lrDgb9602eya3zBV7ihT\nDiRmF99T7NDPhY07SW9RxrvB7E0+ar9oxGA6jgDk2p7Kclqoj8LPskf/po3v\nkAuMWnq+GlIQk6H96w2ndAzH38GTuTrKdcyJF3jtROrtZcAIwfQPeYBo0UFJ\nzDJjpTBWioxvM0u4dRJq2gAYxlNOU2cwkCidvr7LPD6/ZhRtIDLcfHd61fq8\naliVmRG/suVoUYymTlCUZfMdQ+Xoe3T07LFZT8j0qEpjSIYPEpm4zj7IjsBO\n+D3vN9/nmi+0FCSJw6kmWoOvckcB+Ei7/kJMF64z/njZ3MWj8cBY4k9Zuycz\nJknR\r\n=ARMB\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 6"}}, "5.0.0-3": {"name": "commander", "version": "5.0.0-3", "devDependencies": {"jest": "^25.1.0", "eslint": "^6.8.0", "standard": "^14.3.1", "typescript": "^3.7.5", "@types/jest": "^25.1.1", "@types/node": "^12.12.26", "eslint-plugin-jest": "^23.6.0"}, "dist": {"shasum": "4bd12705959837cfaeb679799580e81c16593423", "tarball": "https://registry.npmjs.org/commander/-/commander-5.0.0-3.tgz", "fileCount": 6, "integrity": "sha512-8lfJT6SUoBSqxsXxICEeSB6+/YeVUDshV4jfrDneRcrC3H/bZ9YossoD5KCtNJlmKFUsWFZJg1b9MmEvLD3BCA==", "signatures": [{"sig": "MEUCIQCYIEj7LTGFqBGpHxIHym8rdviSGxJS0FO0eI0eENvj8QIgQaAFW1kkD04sKtdeVueopcuXObbKXC5turgD686YeGY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 101041, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeTjcHCRA9TVsSAnZWagAA7p0P/iy4uQGHCrxy9Y7dvdFY\nRdbfGBWc9SKXQLxod66Y8RUqtt7ae1vtYuGcz2Wfht98FxCyq0JYtnXk350P\nkL9ztWITaKVS8lGu01lioybn2wTHzSJRCKfV0Ujc58DIQzAUXn1C/lBUDcUM\nKFPFZ44p/9cQhQz+ZsoUJQ6MeblRHiIp+pOixfAintyj+bM3uIPTGJtCzhby\nVJrLmF2+T1l2tg47OWxzPyXyWJl9pQV/f+m/6ZqEM1uTToeIyVbRqjO1M8/p\nK9xdnrEY7riP6KwAUGxQ3WLT+TMIl6VBEkYzai2Ynk/TaLC4jARFfsHkvypE\nrWVujnmegVBZrxjvFoyh3k4jirbNnI1zZ/sfty+cmbM3kLdZdu84I9T30REB\ncHMiHrN9q9yYkvu5H8Zq7eW/JdSZMvGSFhopQzYgrOlrY1DdfYgMgFVhKqCZ\n9I06UK9etk1K94FfSPZTOmkLEMTpHNGs2JCecV8d91+JASpIDlwiNUcHEo0U\ncOHo/NKQPFwDhSs7sxuVU/5pGgiwfYLgwKPnmwC0PcqKskUXoB/kRNsZIM0m\nzCbiDOzqA723Ky7Jabgs3zt/bhh6O8GR8dyezCp3OWknGDZaFhy6aGQbdSXC\nKxL0n4m5C/krMreJIF8amdNJjT3ybORsJKDf3Ua9c8Z3WvZBiMKBuNBSK1ZT\ntLIq\r\n=fSGd\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 6"}}, "5.0.0-4": {"name": "commander", "version": "5.0.0-4", "devDependencies": {"jest": "^25.1.0", "eslint": "^6.8.0", "standard": "^14.3.1", "typescript": "^3.7.5", "@types/jest": "^25.1.1", "@types/node": "^12.12.26", "eslint-plugin-jest": "^23.6.0"}, "dist": {"shasum": "3960a6d58cfe1a67537dbcf993f63f7f883daf32", "tarball": "https://registry.npmjs.org/commander/-/commander-5.0.0-4.tgz", "fileCount": 6, "integrity": "sha512-5ouZPC4TTJaxyjVoLbFP9M08CFmiMewAl/Vg044S5hv/oVtP3sUDPOl73XY75l2k8njoplxogL34oaaFayuS+A==", "signatures": [{"sig": "MEUCIQCpXf/tBdd8qh4klu5Vb8Oxjt5NzFiZ8cye88dFWZsciAIgJTOFLu5V993kbuUCLopE9mky1AyDZLM6vHKs8RYyOFE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102868, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeXeyoCRA9TVsSAnZWagAA2ekP+wWZzsz3MfTPGoXM7g0h\n5E3ES6Vnbr8UH5DPbpiBXvdzIdWhDClKZzzfBTPgtcZcYO5qW1fVn09lNE/S\n8lJwxU5k4ijhpzPE/lJ1rEfi19QTJi10c0k65j1K/GSzPdvF+TvuaJlHCXkQ\n1s3uXRuGCJVsMgvkFjknjhtZk4EWf0uqxWghVCu8t5xpEVec3rNO3Vk0EA0J\ndyEw7oF7quKdt31f7P5LJqAUR91+Z3eWuLti1dSkZByf8PMx2h1NA76rQHMJ\n5AdRGRuqvXJyePAES3MCrmsBmiZrKG4LUpC7A9+YX9ArMB9VBGV0Urmu/XqL\nrRQmCiyaMpd61OLOQARwisQI6xvYXWnz1i1XsEYWgSiv1cU/I1PLiFgLg6g5\nHv5lRenitGMQaHlheErPTYHtGprzp3luGjfm3inLQjk9Cuf8tUbI1XSJvl4R\nA/0sbKaowDcMn9ihKHwmwleEm9Rb0eX9fYQSitZsCMHnswwiPrGvAJwXcVp7\niAEMbV+O53cz/jtdBNn22hUlGpLMPtwlvgz8XsR/TJZS3H3xbCPhUJ8uvZv6\n6d0IU2Uj2wjS8urSw2XAAlVilo/Yh7WVG68o98uZpqAPXn/cEfbD+kKDvYMp\nzTNYReG1zy4z1mbipZ0rGo0Cx/iBhpDIKNONmvoaCmEfppuU8OzKCU8ir9Er\nyZDH\r\n=nXND\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 6"}}, "5.0.0": {"name": "commander", "version": "5.0.0", "devDependencies": {"jest": "^25.1.0", "eslint": "^6.8.0", "standard": "^14.3.1", "typescript": "^3.7.5", "@types/jest": "^25.1.4", "@types/node": "^12.12.26", "eslint-plugin-jest": "^23.8.2", "@typescript-eslint/eslint-plugin": "^2.23.0", "eslint-config-standard-with-typescript": "^14.0.0"}, "dist": {"shasum": "dbf1909b49e5044f8fdaf0adc809f0c0722bdfd0", "tarball": "https://registry.npmjs.org/commander/-/commander-5.0.0.tgz", "fileCount": 6, "integrity": "sha512-JrDGPAKjMGSP1G0DUoaceEJ3DZgAfr/q6X7FVk4+U5KxUSKviYGM2k6zWkfyyBHy5rAtzgYJFa1ro2O9PtoxwQ==", "signatures": [{"sig": "MEYCIQDxmxPpv3vFDxpNEVv2z62gMzWfbd7qGdgH/twpS6IArAIhAM3YN20gjeU8uoX5pq82Zthx/+DNoMMy7OavKSq5vFSh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103192, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJebC8OCRA9TVsSAnZWagAA2tIP+wWxCWJU32qgy6hq7y3D\nhPASN47V1YIbKcxEHrMrBwf4pgbI/gwdw9YcnTQ/mn+7nd2+ULyGYf3vP58i\nBhTtJF6BwOPj1tpyroQOICHOMm7VEzhsWOgtAqP6ItaSTIwvrqCDmPEY2iNP\nFwfKFkO2IPD6V+2yEYuWljY/RrlgWrqfeswu1rZxXAg98rwGR9AV8V9fq4rX\nGCcBBlMcZ9tOwGcZFE0VGq+lTHBrBUDICTnv77CzyABkfEbJgyTryRWw7wPb\nTW5JuHlob2NIAsUS+TFK17ixDZeU8opItU+WLm5/31OBWgMoSlYnLsDTO0Sa\nAemAazg5ZAjYDYTXHbxJdGfLOZzQvLKOR1WNLZVNThcdJ0MuHDKOFniCA0Pj\nBIop1IjRiMTOeA0K48SVm03gLpq2dz8AVLf+ikxOoP1jYDya5vezABKDU7qt\nih6d0vb7pdbAsGh6/5mY+dWnXGlp+shTVbjaMBEhiXh5GCL/qfAQQv73gASm\npPAL7pYEg1s/Moo8422Xqx3wSFjj4oXffy+u3/Gy6g6Z4rNfdO2zK7M4daCi\n9k25IQ7ynZaq+SsZcC31Vc/UVbvYaK2FLjH4fofKIgqb4hw1RlEVu+njrLiB\nP0y6xSswgzy1VRiC4Tyn7OhEedC+iKBcSnDr/Zbo3EAnm0EEe7f3GDg6Xd+D\nPZQB\r\n=x8qh\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 6"}}, "5.1.0": {"name": "commander", "version": "5.1.0", "devDependencies": {"jest": "^25.4.0", "eslint": "^6.8.0", "standard": "^14.3.3", "typescript": "^3.7.5", "@types/jest": "^25.2.1", "@types/node": "^12.12.36", "eslint-plugin-jest": "^23.8.2", "@typescript-eslint/eslint-plugin": "^2.29.0", "eslint-config-standard-with-typescript": "^15.0.1"}, "dist": {"shasum": "46abbd1652f8e059bddaef99bbdcb2ad9cf179ae", "tarball": "https://registry.npmjs.org/commander/-/commander-5.1.0.tgz", "fileCount": 6, "integrity": "sha512-P0CysNDQ7rtVw4QIQtm+MRxV66vKFSvlsQvGYXZWR3qFU0jlMKHZZZgw8e+8DSah4UDKMqnknRDQz+xuQXQ/Zg==", "signatures": [{"sig": "MEUCIB1FQcMp2VtJHCVeVAZ73481wG33i9MfSftnJTeg3qJPAiEAsGouRfQtcUo7IFKlmqEB7esXCuIBWwCNDWPtJ4EBumU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106839, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeo6CCCRA9TVsSAnZWagAAEOkP/1vbabWlRUu3mPVGI6zS\nL58zA0x6phEnUHkY5aTS3+0STAzOhT6bDB7sptNxT2WmTkyoP/U20czuOzda\n5eCeQH2FDhvj9vYPnmE6mU/DejL5JyBQie8UsJS6k3yzmrVNcxcAWu1xNkLu\nvXtfC7gp16TpUBLagTGROhJ/+TvtLTgz4/4p2oB3aI4pr26MXd2UvUfOTnwn\nB0rzbnfkCIovf3kmSiS29R7mWvo8WgHHQ9NKkasNJ0iDYLFaiqNueNeL4bBP\nB/3rS5m45p7JpAYq+uPAYdd/Jsl5EWsF72v/gVXioodDS0uUA4INx05DzWFT\nSyO/teSqVIpHH8/OBWb8eF5qSN29uVOrH86PBgKgTWkIQ1MfpEQfRZWI4Rk0\ngQlLSX8HuLmPCz8gT4R5lI6ccO/L4izsArnPsM5y/WlpAlzYhw2E1QBq+Nrc\nu00tc5EkgBL7L6cN0ceW2yYL+z3zITXcZC4MbLwdpumh7wBn0mTDI6eDzAdm\nNNljdDi4Z0btHT/z2X3stkLbC57ppY5RRzThloLMLLv9pEgznoJ1sxhxX8cd\nzpMpkuM+JPPyBgTuNe/X5AYfi8PN7T/WgrAnUahP5I0+iXhwx0j3PDDOcwty\nJM9j93JS8qsQ5iY6I3o4wSoDXyCzeDjryMUv3Q2jeRmSSCoFpKc9hwo5otD1\nAcU1\r\n=6wNq\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 6"}}, "6.0.0-0": {"name": "commander", "version": "6.0.0-0", "devDependencies": {"jest": "^26.0.1", "eslint": "^6.8.0", "standard": "^14.3.3", "typescript": "^3.7.5", "@types/jest": "^25.2.1", "@types/node": "^12.12.38", "eslint-plugin-jest": "^23.10.0", "@typescript-eslint/eslint-plugin": "^2.31.0", "eslint-config-standard-with-typescript": "^16.0.0"}, "dist": {"shasum": "9916b52863f367a878732bf88c06782ec656bed6", "tarball": "https://registry.npmjs.org/commander/-/commander-6.0.0-0.tgz", "fileCount": 6, "integrity": "sha512-KKrRiIPOMyRoCI7QrFwZIsRjfJzRqBswzAYWxKo0dXCmktq0Qz04gSHTx/c6oRLhC/BTYZFeWv6rx2jJl9wr8Q==", "signatures": [{"sig": "MEUCIE/5X+oCEhqIJ9XzaGFKGdtk3CMpej1R5VLBnEKVS0wzAiEArXzSGXIj8TC+L1Ri1pZ9wV43WDySkSCqGgb3mIUMLSI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 111420, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe7abACRA9TVsSAnZWagAAVnUQAIyaPrFTsn3I85j3GU0t\nEaS+OIIDj+1RqIGOpqgJVhhhOjjiotBR95amnm4rrfgbRoXF6/1eF3995ft1\nQfkdyJJQBphN/RqQ5QdKGIX0NUljK0Gl/TDgxrQT3esL0UocmMzCPPOqKwoy\nKwbU6krqVlIFxj9OsA6GVISBUZ23r5xSMNvGJSWptfji5WgoNpD7drKB1Efb\nJRGGkQ59Ifajen3TtAlfp6x3PvTie8ajPZ8JgA+baaA0hWP0eeORTG7UhkC4\n7D7cUhMyInkv3/ndMin5BJHBS+6wpyAxI1EkNHoJx/6Qicbon76nFfy/2UMo\njUXmIcQ3fUfoqzip82/NNrQiAHggQp0yCbpaIr6yRU4ZQUtFg/cSZPIor2w4\nGnxiFehjzqGMI7HeSxqjcWWOMHe0I55X5N8YOguvjV4TiN8yYbOeN6fIB65D\nwNJG1q9Z+YrTc6BHL/PuaQPsOFkQwjoQbnleKEk98vCLduGocDxm/JOFI/OI\nRsp9mmrJixAFd+9O+n62KWPKWHnVoXGAVqPavsGedDvlYAeJfrFOKutv20D2\ni8pM+/CfyjD9D0A4dpbyLJWZphaeAQWtNRZ3YU9nSpAkGLJqx3AAcbtSv9e9\nMyujbN+nnqWcwwpXUTjmg+t1/HH1F3T9r3FGer7RdY4Q7olAqhPP+AUZVtQ8\nlBVz\r\n=pBhB\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 6"}}, "6.0.0": {"name": "commander", "version": "6.0.0", "devDependencies": {"jest": "^26.1.0", "eslint": "^6.8.0", "standard": "^14.3.4", "typescript": "^3.9.7", "@types/jest": "^26.0.5", "@types/node": "^14.0.23", "eslint-plugin-jest": "^23.18.0", "@typescript-eslint/eslint-plugin": "^2.34.0", "eslint-config-standard-with-typescript": "^16.0.0"}, "dist": {"shasum": "2b270da94f8fb9014455312f829a1129dbf8887e", "tarball": "https://registry.npmjs.org/commander/-/commander-6.0.0.tgz", "fileCount": 6, "integrity": "sha512-s7EA+hDtTYNhuXkTlhqew4txMZVdszBmKWSPEMxGr8ru8JXR7bLUFIAtPhcSuFdJQ0ILMxnJi8GkQL0yvDy/YA==", "signatures": [{"sig": "MEYCIQCVJ7BYua1ulrJbWcCspNQJw1m0YmEOrTLdZbSgqRJjOgIhALLRyb4Rdd21cJyaMoGnYRdxAvS1DtRKIN6JqxD5BHna", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108036, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfFFI8CRA9TVsSAnZWagAAKqIQAKRqf1kM12djMkODES39\n6nX6MN2G3L4FxOy4F6a8sgjisU3y7fyOrGb8PkbEaBw70YLwmA61sK7F8OWS\nm52LfVjrKRO/XksTmK0xnzYDyy8My14Lr7S9rnlswQ2lcjNqoglCnpOwiGZ8\n2UNB+cp7+da23elmx8+ldTYrqyec2c0VmL27YurQavKmvruJ98AZgZkn0BvK\ns/SH0sQRHLrzF0Ye/5oh+H8AdLqKVWe6tjCzcSpPRRKjk/upBovNqO2l10tJ\nefBpMe3z//f6X8hBLhCuR+xktOUBIeHMYd3ND9m8qAqlzrKXlU5mrvKD7pSw\nHVv7sm8SXdpkxxJJjVAJRABid6Bcorge8+rKVZBhZBsColSzsSsftcAvqc3Y\nUaRDb7jgYlKLFYEgqpIN/rY7RqZxpLKaFTJp1N/jYiznXZAGpt8FsfHUSyrM\nciNjrC4UICbzD6MwaKj/ZX7Ft9/qA3f7aQhSU1N/zYeEuTmymNLNESFIJbdT\nnbHN5ykgZoLeyMv2OxPdTfjzQ9x2cg97vm/+u7Oqvx6EQMq4VSErKWmamNKh\nOcQFBHvovabHXAcDJjxcBnQTY4w2wzrwn3UKzO6YMbqxJX+P1+jHapxWKCoD\npgI/KPoUOrdmusPsDVx3uKODpj7EyyvRyy75BWDybIoq/o82c042EmPNm4GP\nLw1J\r\n=P2lW\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 6"}}, "6.1.0": {"name": "commander", "version": "6.1.0", "devDependencies": {"jest": "^26.1.0", "eslint": "^6.8.0", "standard": "^14.3.4", "typescript": "^3.9.7", "@types/jest": "^26.0.5", "@types/node": "^14.0.23", "eslint-plugin-jest": "^23.18.0", "@typescript-eslint/eslint-plugin": "^2.34.0", "eslint-config-standard-with-typescript": "^16.0.0"}, "dist": {"shasum": "f8d722b78103141006b66f4c7ba1e97315ba75bc", "tarball": "https://registry.npmjs.org/commander/-/commander-6.1.0.tgz", "fileCount": 6, "integrity": "sha512-wl7PNrYWd2y5mp1OK/LhTlv8Ff4kQJQRXXAvF+uU/TPNiVJUxZLRYGj/B0y/lPGAVcSbJqH2Za/cvHmrPMC8mA==", "signatures": [{"sig": "MEQCIGypzeQz/k7rIICljickaOCyDIiitBb4HsDVCz0pdWV1AiARUjUiBgKDh2Wgsg4Yq4D0MANnALWfBzLoPxW/YnyJgg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 111233, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfSFxuCRA9TVsSAnZWagAAkgsP/R80bYbMXPlaEGRMFel4\ndxus1GcGr2hM78XU4uA1bXP7miANDwiMJVEWU9e3IGqlaP4olKE1zXL00JRM\n3XU9lMgE6RaEiM5cCuQGwZ9nqidDZgT26yHqz16mIKTJqVGFOkx7PKC2RA+o\n5hzgpqi7AHjzgz+T2b5U/1isILdFGCllG9oRML3DxQ3MuRzh2ZQUJ7bjwKtr\n/hV06Ka+KXzkNs0yafe2pdnXoZzKQdoIIdyxEnXIroAUcbPRjxLi/igfePgn\niU1rM1lKwr89jglROOmawam+a6SqB6649S+zj0PkTzf+/n+2u4mh5ze25q8b\nbm1AoQhgBF64uFP2DoXt2I032yBvMbC55VfurgkJiECW61MB6w03Yhox9cU9\nSn9i2h+Z8YNSmuJa+lnlXKKafF9gzFx8p8F+5hYYkETzuqqiwqODLJ7my7i+\nNhCtLL1ktnm4xn39Rx7VrqS4i/6m6+8VfPKzinQMXeHXpTrankNKP+Im+74X\ncqgrB4buNQxzrI30kbj4Q4Ixn2n4LPytTeZ0pqNECdpN5a/JIri9mJFzoOee\nZu7wT7zSZxvGLdXJAkzcdAwmQb7Ym1b2mOeAKbVHwhxN/z0YkkLx+ehMkjDX\nizihTyANkKw218VqYap7TzUQ7b/5g9+/snlF5d6EvLuGUaa72QE4xpZDgmJt\nyV84\r\n=FpLQ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 6"}}, "6.2.0": {"name": "commander", "version": "6.2.0", "devDependencies": {"jest": "^26.6.0", "eslint": "^7.11.0", "standard": "^15.0.0", "typescript": "^4.0.3", "@types/jest": "^26.0.15", "@types/node": "^14.14.2", "eslint-plugin-jest": "^24.1.0", "@typescript-eslint/eslint-plugin": "^4.5.0", "eslint-config-standard-with-typescript": "^19.0.1"}, "dist": {"shasum": "b990bfb8ac030aedc6d11bc04d1488ffef56db75", "tarball": "https://registry.npmjs.org/commander/-/commander-6.2.0.tgz", "fileCount": 6, "integrity": "sha512-zP4jEKbe8SHzKJYQmq8Y9gYjtO/POJLgIdKgV7B9qNmABVFVc+ctqSX6iXh4mCpJfRBOabiZ2YKPg8ciDw6C+Q==", "signatures": [{"sig": "MEUCIQCfyUbONjxqsb0vWknV1phLk8bmh6C1xxcqdt8+zle4kwIgKwKDBLBHJugTzayUPz//D44KVcM9XP66Uq9X3wEmhLw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 113236, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJflOUFCRA9TVsSAnZWagAATIEQAIXPfPigdRxIl67hNDaP\noegoXv5FxcWLBf4Rh+h2Xr+3nJQAbPZ7aafvr4d4lljwaEc1+6S1ybb46mH5\nYYwKpE9hwuomHLOxhp1FMgt871kJlLJ13MbJMeLYLNQK9jsJZaEqbzu7L6X1\nOFCbLX2rSmVKTCLMPOT0jAqytyr5LoG4T8DA6Xm6l55XIhwXjCpGu4MVNUJn\nshzBeTcteNdAhXcb+JBNj95ih9TtBC3sznawtYE1aWKV+OiBIG6jaMKtNYar\nNkdJpefEl8DKbeQoHVE/ImxDzTPgBoocEqnp3bSTZ+CCjJ4gqk32Bp7e7Tx8\n0jicPAgs1d3OoPz21FxwRexlFvZzBTrY3BpNp5CBzG19T+8GR5zb05GYs4tB\nqFMd6TTyNta/SNFZnZ8ZeW3M7a4JpkEWCzdhf3HvUZ0qIqyLw46PTPeXg/2H\ns/o895CUOEnj2kT3+xiW9TKZPFHnE+8MC5Nnt11P9IF5Ael58feh4cxSZHhM\nwrrFjqURhP1e0FdajOe/4ynOR3uT8elT3906vs5v74f+wGEI9Sc8HM1+znNb\nYvKY5kEPBGeciBQYWPHxEI409Ynmvdx4T0lEmXE9jlX0y+kFRsiu/0k91p1k\nmSQ7H6kaeHETNjIMiILRRGUbsAOQLlOmKi/32vi8Vz/RmIo6xI/bSD8CeNDX\nupNZ\r\n=01r0\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 6"}}, "7.0.0-0": {"name": "commander", "version": "7.0.0-0", "devDependencies": {"jest": "^26.6.0", "eslint": "^7.11.0", "standard": "^15.0.0", "typescript": "^4.0.3", "@types/jest": "^26.0.15", "@types/node": "^14.14.2", "eslint-plugin-jest": "^24.1.0", "@typescript-eslint/eslint-plugin": "^4.5.0", "eslint-config-standard-with-typescript": "^19.0.1"}, "dist": {"shasum": "b4f780553cbd4b0f3dfe49c7a1d13b088353d474", "tarball": "https://registry.npmjs.org/commander/-/commander-7.0.0-0.tgz", "fileCount": 6, "integrity": "sha512-PJ04PNZn5mXaCHQZGW8WqBvkIgMKwtGPdmL3X3EpJuKZF3hggmrwff9rCUCEn2tewnrbs3jvcJW/F11MJ8I6QA==", "signatures": [{"sig": "MEUCIQDIHnL7UiLynfNMLNorl4a9429nv/lpmci3rH9odlNIdwIgKLpAgBprhzHyf2383v0CsGfqr6wNm3LP2lKNSrGD8G0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 130314, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJflQPKCRA9TVsSAnZWagAANaAP/2RB3Iuez4YlPglFVNJ2\n1LVDIHo67A8S+MbNHEKsYu9v9ybHoLYMndwpLdiGF03L7xFYaFleoBFJEThN\nFz1l9cM+nOw0BYclJnQqwbnes2Vol+0FVWx1gEKrw4SnYosfadFPqEC4aqFI\nM2r6BRuRi/D9Ypf3JYQlPrStHjzsrhIoqRP5FhDJAUR22ouHU+NMJV52tQYr\nHbjI4LR1AhUwTfy1fkiMF1yXIxUOiPpZJTc17rf0x7iGRvcd2dno7U4YB5C/\nLMxr+6pcPp3l0RWUQbvYjZKlkTJa/C2dKly2cr3XZHCJrQ064rcMLI0mg3aY\nRkd36+aMuy9KBe9keAUf/Y7ZW5w8vhyYZbbs002z/YGw8riNuD4lHpeTAcAP\nUWGJ/mM4fB8E7udaclU33FTfuzKmGWLNeGyjv323IB2hiSb01ONeMn78IQTK\nX5glMXxq7Y9y3PKGy4tJ85kj1sKH6w3gMkpxW2Q3D8gri220mNmDPSGWukNa\n3Q3Vk0IAu4WAsZRkXQSlSMdP3kU0pBFFb4wVdy/pfaGrCTzEVVtkoswxzrIz\n85PhDuALPQh5vr5806k8C//iEH177AlrT5PkOm8YiCLpyorykCuB7Pg9FKDJ\nhDVnR3VKO6J8Y9k76ImuzSwP+DjypA9XcC1J8jKlWaYyx99lBzSKtaD7n+Fm\naRU3\r\n=h+dH\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 6"}}, "7.0.0-1": {"name": "commander", "version": "7.0.0-1", "devDependencies": {"jest": "^26.6.0", "eslint": "^7.11.0", "standard": "^15.0.0", "typescript": "^4.0.3", "@types/jest": "^26.0.15", "@types/node": "^14.14.2", "eslint-plugin-jest": "^24.1.0", "@typescript-eslint/eslint-plugin": "^4.5.0", "eslint-config-standard-with-typescript": "^19.0.1"}, "dist": {"shasum": "c55188136d80d1a0df19ae21c3317fa0bc21c6a0", "tarball": "https://registry.npmjs.org/commander/-/commander-7.0.0-1.tgz", "fileCount": 6, "integrity": "sha512-5yh6ihhjDbdt8nchmjJINF5ttoczMFPEdSn9VBK3v+Yg+SsozGTdonkLf6GFOzKu8o2PwVjYorARioDLgbd3oA==", "signatures": [{"sig": "MEYCIQC3bRvuoluiLyGrMeqlj9gX4A7utjnR/6ShYalAaZL5QgIhAN9pMXuiLfEB31XPIAg2edZ2akQC56xNkNGOw9zS+ehq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 131978, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfuYb0CRA9TVsSAnZWagAAvlAP/3o0pXvUtjuc/njElgbT\nLQAbnE0Y8HKHg/kZXKjfvyY3xoBR4XT6THRR9G/ERXmEZkPASewilXa9PbMS\nw5InwSp4KqvUBzTWLXzRp9xjHWf5qJqMF6/2RaiyP0CmlYdaRGg44xsN3Q7r\n47w23Gwj3KkZRF95FDVK1q9vIt/SmLXetZkysdqeulPDNVJwSGXunxjuTgJl\nBVaEb2XR9hPvA6xyg4Jf7EPpUahwpN9UeCdnsPS5dKNu454eAgw0XWvgX2e+\nuZ8DJ6TVuENj2+P35d4fVOS/jw5BReWCg4xOQQqGtgZ8tleXjXp8TYE2ZMSx\nVxcxemz9t3BJzlTbLTECXs5NSJ+BVayROVNy/oZzsk4bg4slBLw3stn1VoYP\nSXC+gVybZcsD1k09qyEFokdJQqy3iX0T0YLWndaoaLlNSKwW6NerzfB+ZUN7\n9oqhhNFLEpu3FS/ezeCzjo5NLpbDsBrsOyrz8xl6kWzO24P5TYm/mzoftwER\nEMbQ2yk+iLU0VbM8+eY8ZZkB9096+eQZBuBFDWcM6u3DP79+bvQ8/WGOE5sk\nPnDxTzVZWogPWh/EfnkvoU4/+N5xF3KpU8g+HzKd65ZJzH9C56s3afklBvVi\nqDN+IzeJoGBq/DYLjHOrOYoMU4sGjlBDGNRjEzlM5ar6UD7Kkb0GdxEBb33j\nLciU\r\n=AYUy\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 6"}}, "6.2.1": {"name": "commander", "version": "6.2.1", "devDependencies": {"jest": "^26.6.0", "eslint": "^7.11.0", "standard": "^15.0.0", "typescript": "^4.0.3", "@types/jest": "^26.0.15", "@types/node": "^14.14.2", "eslint-plugin-jest": "^24.1.0", "@typescript-eslint/eslint-plugin": "^4.5.0", "eslint-config-standard-with-typescript": "^19.0.1"}, "dist": {"shasum": "0792eb682dfbc325999bb2b84fddddba110ac73c", "tarball": "https://registry.npmjs.org/commander/-/commander-6.2.1.tgz", "fileCount": 6, "integrity": "sha512-U7VdrJFnJgo4xjrHpTzu0yrHPGImdsmD95ZlgYSEajAn2JKzDhDTPG9kBTefmObL2w/ngeZnilk+OV9CG3d7UA==", "signatures": [{"sig": "MEQCIFGpc0sdG2iBTOgN4bknTSpG5p9ZUtAKMn0XgEd8Mbs+AiBEpET+wpBLUiOIKLMaXwf92zXcCi8zpiVMFb+zkYG1MQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 113457, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf1rYWCRA9TVsSAnZWagAAvYUP/ig9f6oDfuxRTtZ8wOOe\niV22RcHDYTrmrrlB1HVlPUhlcr096FPIYuMpQnQFB3d42tjVSCTdOCTnQAi4\nnjedQscYVg+FiA43AgMRvSktmnPEtQfGBLQB7fQLEhMicHbd0+pJwn5dQkah\nqui3Uyk3m5JxPR8garPFHF2Zv95m7pS26zGPKWASnTGJ5qsQtRAZ/77vZBa2\n51aOhmt4976UMBjDhD1Bfpz+8a3/i75ktP9Vnyqtsw9lE8N6t7meaICBu1nD\nPEli9AHxDHYY4eLnzqMA5I2wXA/QT8tyhh2+khYhnbVTMzj9FNsCroRwwfpV\n/9FHxW0ilnS6J51SPVp7uV9Idzo+EXkf3a45TpMNsJ7o3BrZVqEDGEe94o5E\nCRYjt+ykyJpv5Yh4KfV0lrNBr2BLl6ZvSITsCbxWthjJNNCs1eyoMXpEXb2/\nqKUFvVCdbfMO3OW+AT6ThDsed+hv50YMBeG05KrLpUjdwFq9n2Wwy2Qdu7h2\nk919SyMpuG/GMNjuUnyf2G0O2eYl3Iw5+sgR5gDJIv16jDn/u6kP7EmNl7iA\n6JCjLN1xm2MgPi0/SeJJRfvT74y2qpIOZ7hyf9TeFgZbgeqhyIVRsq4X6vcq\nIH3ppyZQgvHcgt5naNEIYvqUWzpdFfeKB3a0e4V2MHc5Z9YjkAA+f6BbBBMv\n6hYv\r\n=QUiO\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 6"}}, "7.0.0-2": {"name": "commander", "version": "7.0.0-2", "devDependencies": {"jest": "^26.6.3", "eslint": "^7.15.0", "standard": "^16.0.3", "typescript": "^4.1.2", "@types/jest": "^26.0.16", "@types/node": "^14.14.10", "eslint-plugin-jest": "^24.1.3", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-promise": "^4.2.1", "eslint-config-standard": "^16.0.2", "eslint-plugin-standard": "^4.1.0", "@typescript-eslint/eslint-plugin": "^4.9.0", "eslint-config-standard-with-typescript": "^19.0.1"}, "dist": {"shasum": "5df69e0962ae9780eb5692f55b2eb650de8f6b02", "tarball": "https://registry.npmjs.org/commander/-/commander-7.0.0-2.tgz", "fileCount": 6, "integrity": "sha512-ezxiUopEH3fSd/sI0UTlhwR8EXjrgMOCphZGTMqhrjJvQNsrbU2E5KOxiUTPaZDxhYDFZnBe/GDSpcI326MTAw==", "signatures": [{"sig": "MEUCIQC0Q1lj6Kan1D5J/rdlJ+bFoc64HbYyXZ5EKvwcqfCAGAIgbu8ozkdgYJbVD9NwbHgjCl3iVPi2vYodeeG56ynWHew=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 136384, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf1xjeCRA9TVsSAnZWagAA4asP/iClE0JY9NnFa6aSO7Al\nxjvpaGghePxjOB2vRP+1gCQzcinf2SXeLbSxGzaKpSxHHuyFMCZBjhqvz+YE\nQEDJrP7r9stYIfigiLHpeZGnH03OU1zD3opTNumjEC3himziTjdBSaS+SvrV\nP9x68e4u9E6fj9A4cJChh+exbX8nxhciNlteDY9LDRHcVfXECkW24+g60O8i\nAd3sLMGE9BLSPTxENcJFllZWtvLoR8Jo/xrFWi9QbpZ3kMf8YITdZ25K+7qJ\nu3vdiLOICP/WatQ3pkAQKeuwt/jOLm1XJMkfJV+GnfHPATbqP/c529RLrJee\n11188PB1nZjyNlz6lKh10WLfpe5cqdDp+fylIrxJ5zW635xEG0gVpRYYJXNB\nNNTRMtdb9561UQDWQh7tqWjo8RZQcWHUyqlRJu4mprcURVSPSCCR2ZsPENyM\nT8Dll8V5Ve8ctEaVzWeqeft8f62VlvaeCG9N1xPtmsRFP84FpGPIqvq7Q/7F\nwA7hhl/sHrjzTJyUoBnNUxFlreMEWIsrA6428VOFF/ibadM+nrwIYoMK/HKK\nyLWwKNZAX5hmPVBu69YiJVhqT5E9EjcJdJ2aheyUqnSy0bDpEHEJM+dWXqBO\nFhBEMGrfIN1ov8mSx976OxmTgV8onX2KbNtPDqdKKO2eNMbAH30PBGT9rAeK\n1+pN\r\n=5qeH\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}}, "7.0.0": {"name": "commander", "version": "7.0.0", "devDependencies": {"jest": "^26.6.3", "eslint": "^7.17.0", "standard": "^16.0.3", "typescript": "^4.1.2", "@types/jest": "^26.0.20", "@types/node": "^14.14.20", "eslint-plugin-jest": "^24.1.3", "eslint-config-standard": "^16.0.2", "@typescript-eslint/parser": "^4.12.0", "@typescript-eslint/eslint-plugin": "^4.12.0"}, "dist": {"shasum": "3e2bbfd8bb6724760980988fb5b22b7ee6b71ab2", "tarball": "https://registry.npmjs.org/commander/-/commander-7.0.0.tgz", "fileCount": 6, "integrity": "sha512-ovx/7NkTrnPuIV8sqk/GjUIIM1+iUQeqA3ye2VNpq9sVoiZsooObWlQy+OPWGI17GDaEoybuAGJm6U8yC077BA==", "signatures": [{"sig": "MEYCIQDZzPLrCSqglA2MMIXAJgTBpuzEAd27SS5X3U7i4nhWtwIhAMPaKjMoORc2kREPyV8ccq6iRKUIovJunwWDgjVRsIQ3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 141730, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgAWSlCRA9TVsSAnZWagAA6bIP/jKECd4+HOOBFE8gL3q7\neTibJKKsTe9MT5h5DXjNaayZm19M7Ak4kieI3/zMtsOfbGLK00KxbIyuhQUJ\nMJ46e9xQ2IuylI8xen4WVu+QObQO+C1377pfEPBD16dhnN8onb5qBlicOpVu\n8SNgEJETQ7uICP0p+Sajvn4F5dIQZujKT30xU6xVhJF90Zh3Nh0GeNjp5CBn\n8Nrrr3BOy8/stzPKopY9sL5BekkbUyjRXWLJEe/eN3i35XRnrH8Kb0SZXHz3\nMDP2dkKzOsNZrZXL0jha9xyxtaS5KyBfwMK9uVkp56PACY+YNWhhxw6u3JYp\noyKHBrpvhNGHeZ6+3MT5Vm1Ypeju/BtW6pHK5v+wdNgukcH2M3iY9bw+UGhO\nJ5/2ONrpin+iHZHX8dCwErH0utgO4QYX+o8iFnoQw0P32u9vnkz7uhROR0NF\nUuP0xYU9+W36xVPRsB8uKXDfqT7kniBdTziXhPTuF8VyaRXDiViVfTDUiczh\nPnBqaRGTGVUqVs9Dmkk3FEsDXf1nHiWcgKDwiTR7MdwcELPIEb1UFhgtJNQP\nAGktBMiajSbJ2rqx4w641L7rnWLqsFoN6h7jj7yrC6NrzzxzcRWlbgbvO8Ei\nr2PsgrVkje3B9ujaenaI1mkaK6bsS0Byj47xWXriXd/ewSHkL3NElVuwdiEq\nvqd2\r\n=8edf\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}}, "7.1.0": {"name": "commander", "version": "7.1.0", "devDependencies": {"tsd": "^0.14.0", "jest": "^26.6.3", "eslint": "^7.17.0", "standard": "^16.0.3", "typescript": "^4.1.2", "@types/jest": "^26.0.20", "@types/node": "^14.14.20", "eslint-plugin-jest": "^24.1.3", "eslint-config-standard": "^16.0.2", "@typescript-eslint/parser": "^4.12.0", "@typescript-eslint/eslint-plugin": "^4.12.0"}, "dist": {"shasum": "f2eaecf131f10e36e07d894698226e36ae0eb5ff", "tarball": "https://registry.npmjs.org/commander/-/commander-7.1.0.tgz", "fileCount": 7, "integrity": "sha512-pRxBna3MJe6HKnBGsDyMv8ETbptw3axEdYHoqNh7gu5oDcew8fs0xnivZGm06Ogk8zGAJ9VX+OPEr2GXEQK4dg==", "signatures": [{"sig": "MEUCIQDedEMG8CddTIoczuxvjC6DZr+JY1xo2tVWms1LPptRIQIgTP+LfNHQR9ZcQ91LDhGJxnuOM4anJ8EI5Cdwh9V6zGQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 143520, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgKiG7CRA9TVsSAnZWagAAF2MP/3lJPrZx51HV9ixvKakr\nMbu88wHvSmbvpQT+C6rULEm7cqeNd2Z5zif2++TckXvVvbsmOwD5Vjg5Miw4\nLsR1JnufHrgNDSSL3dXiksYjFHTijgitDGIXD3CcAKOSBLoSbOua4nkGj70u\nG0InzexV3QqpXNgguHRfTzuWAKw21ZMfMxK1Xy3/WuoNTha2agTKjury7L/z\n6QYR7CH1NOW1tlWM0M6EUxVH29IicgN2lPCPZ07B5w8A4sWleV18UtncsSkn\nC6+3jwz/SnrJXbipl0ngOUsNgbhNEEa4nYen/NraTjP6D595wry/3hCMTCgX\nCyZTWvb3HYuFO2uJyuTqCRI5X9kEafkaiUAzNR0n5+mATGy8YTTR6NPJTb5t\noAJcaIdIhTKksTmkczpgcpQxdXWkrZohS+e+BZsNIzrIFlkZ+R/99qcp+4zg\nVg9kKYknxzJzUK/lTmf3F79RPa/nNCcpdP+S1mlqlAowz/8mxnFMwaJVaPLd\nChQghCu6bUtVpSJ3lY+skH8Nrt2PJ7NoZT/Kg3VdLLMctsF7RFoheRZ1t4hz\najZwlCtqt7A1E/gMdhBBZ9buiY/kAB5lWbE7aXAX5bQsEPVeH5QRV11KImRK\nxohv4ykTRXXrVuOoTpT4PdwJwdL7/r0fXqymuA80izOkTJx8eCc0obZWnCYQ\nliPe\r\n=E36w\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}}, "7.2.0": {"name": "commander", "version": "7.2.0", "devDependencies": {"tsd": "^0.14.0", "jest": "^26.6.3", "eslint": "^7.17.0", "ts-jest": "^26.5.1", "standard": "^16.0.3", "typescript": "^4.1.2", "@types/jest": "^26.0.20", "@types/node": "^14.14.20", "eslint-plugin-jest": "^24.1.3", "eslint-config-standard": "^16.0.2", "@typescript-eslint/parser": "^4.12.0", "@typescript-eslint/eslint-plugin": "^4.12.0"}, "dist": {"shasum": "a36cb57d0b501ce108e4d20559a150a391d97ab7", "tarball": "https://registry.npmjs.org/commander/-/commander-7.2.0.tgz", "fileCount": 8, "integrity": "sha512-QrWXB+ZQSVPmIWIhtEO9H+gwHaMGYiF5ChvoJ+K9ZGHG/sVsa6yiesAD1GC/x46sET00Xlwo1u49RVVVzvcSkw==", "signatures": [{"sig": "MEQCIBflDnZJYcPmn5yXDFWwb4LBZUuBOgEJi9vXWkkVGnvDAiA+SOYBJIcvrnLcyhTsa0RQlj+iRp3vQBT+kTFM4ZDDig==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 144710, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgV8EACRA9TVsSAnZWagAA8+wP/07OyprjrjtHW7TDf7ek\naIJdj4jqxdT3v9io7Xv1gp4c5GodhB4ACE4GI8XcF0T0ARZIeFTFnUlYBhz4\nfkTf4YOn5gbSKG7nhpDKKm3LEHpOSstnjsX43DCR/eZfQMzRxsmYpj+jL5Rp\nrS7ATBCiG+H9ULsL79Yb5EiB6txsUX7vkmBgjsfZK8h0/qtBU9yWIgtjokas\nrWsUD1PnMHUYzUoCsJzrE2Lh9tVud7Jq1ujTeu9roiwEmvf9Vu6yHv81b3KY\nwFmt7hkb7oa2AvhWIwZxsEF08AmaBmsbO6Y3GK1FojAiKWZW3f8ixnws92fA\nlyoVA1QP6921UZ8zUOODFYpjztKwlBrx0t/6AtUGAkQDsI/v71p6aeulWhYZ\nldz0O3wduc0LYgjn0/M2VKP5bKLJdK9/MdAK0YHLFZfJ9hirVNK9JHO/8UhB\nwrWLLMmPVFSa6M55ibVdU+W2TDvWE40gt3VGEC39Blu5VKCpK8K/v30iv8sm\nAtDl9bFogJvcjryYwSdHN+LaxMee5MnUEvE8xRyNJaHFoKMeVZ2b4LTY8Ges\n/4pXzZ17u1lNnwkLSKs1pLajGynZbi8Dcp1v+y7AgfqShecaLWvdlwO+o35L\nf9eZcxl6QWf0VWFuvVPGP/xOHt/Pz5qd66IoegXCpOsPgtnmAAikf5KHcp9l\nYIid\r\n=Lt+W\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}}, "8.0.0-0": {"name": "commander", "version": "8.0.0-0", "devDependencies": {"tsd": "^0.15.1", "jest": "^26.6.3", "eslint": "^7.26.0", "ts-jest": "^26.5.6", "standard": "^16.0.3", "typescript": "^4.2.4", "@types/jest": "^26.0.23", "@types/node": "^14.17.0", "eslint-plugin-jest": "^24.3.6", "eslint-config-standard": "^16.0.2", "@typescript-eslint/parser": "^4.24.0", "@typescript-eslint/eslint-plugin": "^4.24.0"}, "dist": {"shasum": "581657335fd53b1bbc3978341f66d7dea8cec239", "tarball": "https://registry.npmjs.org/commander/-/commander-8.0.0-0.tgz", "fileCount": 8, "integrity": "sha512-RChUqiJ5hzjkLJ6V5Gcx349TFXiB4FLNgZKRHEhKAe37gMsgByfsUi16b7IuYFYm4gN7aFibfhuIwyKd30v0Jw==", "signatures": [{"sig": "MEUCIQDRSEa/SXDctqdW6heLxwGEWIVeVX4AVxtUkIJ+0hIe/AIgClDagsfT2RY+C4hyIEcefLyCkPPyjrryQ/jPCtsvniE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 150807, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgqZQ4CRA9TVsSAnZWagAAjLcP/jF18uFOobuHGv5McMy/\nK0qcwWW7fDOJi2yrkWI3VFder5wYyMhHS+t0CUIuK/MLus41cJe0OFAL+3qz\nLDIY3H2QLcPjvnrzY3eDxxNY6baW3QvQpfsQrCRo9iVmF3zimJnHQpTFzpZe\nVfhJvcomTgtxGxlnDtd0XLyvD4VMe2QLgYK0JwnMmZQFRZbk7Tn/nGNGGYNb\nm5DTN2vXKTnvziT95l3CFI4rUlK0R4L4ExI/fWmo528ryKR4sVCYw+9Do5A7\nR748SCdQcBpoYn0VQLYFEbHCL+ocLF3vCuAssUmT/di5mEbHsMu0kTznMg4j\ncidZiODhG/OY6yoqrHko5PF0Uigv4UJHRbjG9kQM7Wd5PL5mHDD1qGJ5bVmd\nUFd5oOoM9o1OfbFgo5+DUP28Wd33IXAo3X5cpPtAheY1nyfxoECkg8TlHsmx\nOyAU0xFTIhxVewo+WTKXnqAf5M8+/z3emYyjsGtjP9IYgsyO2QC38C1iBd3j\nRajySx6ryMlIVNsuZPb8wXRvUl5ZMqhfO1/egI0fEnSYJ5rdNH57awu+9EZH\nsoHiLHcIEh5PP/TWHn5puVqZ5BAjphi4SAW4tgjSBYIztccFk+JVLt0TT3or\nu4uS7A3bD3KrFP1MA6nxkDpNCExfF4/bqHciwMqpzQz6drI4qM+sIkBQOcCH\nOozP\r\n=gU0F\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 12"}}, "8.0.0-1": {"name": "commander", "version": "8.0.0-1", "devDependencies": {"tsd": "^0.16.0", "jest": "^27.0.1", "eslint": "^7.27.0", "ts-jest": "^27.0.1", "standard": "^16.0.3", "typescript": "^4.3.2", "@types/jest": "^26.0.23", "@types/node": "^14.17.1", "eslint-plugin-jest": "^24.3.6", "eslint-config-standard": "^16.0.3", "@typescript-eslint/parser": "^4.25.0", "@typescript-eslint/eslint-plugin": "^4.25.0"}, "dist": {"shasum": "beb815df52cc9d0e1afcfff93b0f94b79a62a8ba", "tarball": "https://registry.npmjs.org/commander/-/commander-8.0.0-1.tgz", "fileCount": 13, "integrity": "sha512-tSrRWF7x0QOO7bjPJIWxOmFtrqcUGWkyp4zANM8ZholgD2gtw9zRNtM5RtbQyOQAu/qp01tnvyFmP8KrlXokHQ==", "signatures": [{"sig": "MEUCIQCmJvqZDOONkS/vyQPIltQYo7XoLOk0W6XieDSLda2qiAIgF+C3kG+d+c7pQiDVDH0JYXAwCIh1M2PlaHHn1bVINMo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 154621, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgtJCTCRA9TVsSAnZWagAAdu8P/0NtcPdmNQr8w53gSQbd\nonUl25G8i89kRNIqJCIF50SKl1Y6rSYwve5yvoBcjgV4SbO3TJPBiVA+0mur\nrOCK7vsckxsamxlAV2rFQA/6+0rwxn2fmrVkSfGqEdjAsvzUW7pBCnnCnPkB\n01xQBdYu8fddsGUZmzscnwUSwXQSk8eBNFdIiBU1gKko8F+rHxdiTALw5DUO\nIAJP7z/hTJtZ3bfYrAil6p2E24v/KnALQznQiVNpf9U85N4rRxN4RPcFSIOA\n+1ce6tmu4Mo4tq/KMLGPBXztPMsC1MU/zpJYqRQz4I0S9kxiO4e2R8IfhtAd\nCkWqCI5+LBJSHtK5gP4+1nd6DlznipyOjlCMOa1AvOQddRaGx4dFKXTMQRXJ\nGunSutktE5bxxT/OI6hTbKHPjjtgF635ecM/1PQ1E8R0I35xUdDfx5mZMWGs\nk0cLEig/rDYnghpufnx4MfcZUeE3EFnk/OXo3UvH2QMakGl0lZW0cCQpE8ws\n8qBrkqcStjKepqjeBj8LigbpgN/1oZDsCDQQB8X54xhTqseTk2fZjT8jfDZN\nucC8UrEqB/Gd7kgSafPU14bOkcnZQgbaWKXzT2vKAynd1BeRwtIXL+F0hj7+\nHCZdBdybZtIczAumUTNg2uRLnZJk42hhnQ6queaYjnAfa7/cIdl32OfJ0ALM\nxbUz\r\n=6HJ7\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 12"}}, "8.0.0-2": {"name": "commander", "version": "8.0.0-2", "devDependencies": {"tsd": "^0.16.0", "jest": "^27.0.1", "eslint": "^7.27.0", "ts-jest": "^27.0.1", "standard": "^16.0.3", "typescript": "^4.3.2", "@types/jest": "^26.0.23", "@types/node": "^14.17.1", "eslint-plugin-jest": "^24.3.6", "eslint-config-standard": "^16.0.3", "@typescript-eslint/parser": "^4.25.0", "@typescript-eslint/eslint-plugin": "^4.25.0"}, "dist": {"shasum": "b661866c4fe85f254f94e360eecf0b631792fba7", "tarball": "https://registry.npmjs.org/commander/-/commander-8.0.0-2.tgz", "fileCount": 12, "integrity": "sha512-Eo8<PERSON><PERSON>chZJS3CORoBTljR385shpHd00uYXBQPRCbtEy4P2f4VpwvV5+AKYAhc7QLxVj3I/cR6nHq0MaMfnPuVbw==", "signatures": [{"sig": "MEQCIE9jTfM3Q5xSMucFitHv+36JTJYnz6gLnVRDuMK3ZDtkAiA/WDiMXZST1L9HCH0OqEN1A/WTgmeFRL5n47LJi9AlyA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 142662, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgvFDKCRA9TVsSAnZWagAAZ5oQAJNNDQO+Dr5AgWuixCrI\nRu/FimD6JbTW/r6Ay+D8cDiHW0ihCfaxKEaqSBlxQyIRV5Q/2PfY82UstWMp\nPrKVQ2CSzjTq1SjkVNBPOXrnuOQcaA9M+zL0AgqZXfrorALkuewEc5n0pzBZ\nBaET6CUGvTrhI2JtIxGLGluCM36pbVWUDfIhDkWjdciCHY9m96JwrDnQv3lP\nY4pATulifZf9JAapfI8ssy1mcllq9ZpCwFcbUHXvMPPox7/VRjZdkyky5l41\n4nC6em7yAGVybU92K8Pl4T5caklfUPlpy6hetev025JqBo5Q7aVLcujDtkrA\nvsIFM89I/0l3d+kEBjj896n+ZbSUOjArvVrGRYCpKfuDwmMLZm3TbCIrBsRq\ngPfntAlbKj6BX6ig2BG8Z1TIHdqaDWKPjs5iXHNNV7wc33NShy8e3+z63nmB\nCZLVtPw3EPB2gshHcMgyAiyWXfKqIsmIwT2z8P96qiqokN9Y0Lp0oREk+aTg\nISbzqmak7JORpmjTjZnZ7vQvK2jujV6IPAv2TpVxOWTY8HQJZi7lZOyrDkbl\nJS4UViOfEJb/kCOL8qMi/p44fr+I475pILTPWrwTQ+oJ5Fj6rCJCH7mk7pte\nddrJmpuJOk6sm5rhCaUJrSoZr0w/quyJJFPIvtWE2M2z4/bhOVFvn3KSHHJE\nd5Q+\r\n=GEhw\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 12"}}, "8.0.0": {"name": "commander", "version": "8.0.0", "devDependencies": {"tsd": "^0.17.0", "jest": "^27.0.4", "eslint": "^7.29.0", "ts-jest": "^27.0.3", "standard": "^16.0.3", "typescript": "^4.3.4", "@types/jest": "^26.0.23", "@types/node": "^14.17.3", "eslint-plugin-jest": "^24.3.6", "eslint-config-standard": "^16.0.3", "@typescript-eslint/parser": "^4.27.0", "@typescript-eslint/eslint-plugin": "^4.27.0"}, "dist": {"shasum": "1da2139548caef59bd23e66d18908dfb54b02258", "tarball": "https://registry.npmjs.org/commander/-/commander-8.0.0.tgz", "fileCount": 12, "integrity": "sha512-Xvf85aAtu6v22+E5hfVoLHqyul/jyxh91zvqk/ioJTQuJR7Z78n7H558vMPKanPSRgIEeZemT92I2g9Y8LPbSQ==", "signatures": [{"sig": "MEQCIHcKA69zrYUV4bnOXkidiiE5MigBOHwIb1AOzIXjrT4aAiAunx3RJuJw80pxhM+6A19A76tq+CC1do0m65TtOtKIhQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 142660, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg1ZKwCRA9TVsSAnZWagAAt+MP/j0fXBaAOV40TEeZwDP/\nO3adO1jhSYPTP5pySjHfo5plUw5Ynvd4MzWXwvl3by9z1oqNB2MeODH7S28c\nBXqy1xI0K5iI90jSu8/ObQS47UGpTIOmgZn1643h1C+yI0Ha279nuloj8BkT\nDOCXNXIOYq2yxy62SoziXiNpjLcjtouv7X85cqF7K2gOSBbHXQV6f9+6L5JN\nZxgP1+UOtwpT0i6BL1ux5fHhnaOm71Wwo8O98mlWfqZi+JOEpSiVPMYXN782\ni2DcVAEEMCv+HCFrhbAA/bGGAqv4bnxqsQ/V1P9NCA50ZjIS5xzidCA5rvdQ\nokdsxK7+yF4DSJUHM92mRdFl34RNQ+8Sug/Vg+/ssGXo3CJaC2eK6+cFBI6w\nd66Fpuqs0E6nuNu9vWEpOhZ70EOoD2LW5f8ED3fsiLe8P7+R3Ks2AfzdkHQi\nlrKHKHNcEk1ff+E3yB3R0Ont6DD3Jhe7v8euYMc5JLWRd/dbd/ZUuQIEtTVO\ndsqwMyQKTg+JPhZV4x8lTuwX0ay01kKlK0Ut/2mXwWRu5xeXIVI7hQibA/jP\nrAe3p2wtN/RIK7F8cPeE6oXXKOunYgN65zkTWZGyYxjyGvhTHnGd+E/0oNiz\nWk95Li4UsUSOhHmqoqJ/v22AnZtZQMPTa3o2qzZI5pnNvYGqU710r0LdUXoM\npOPS\r\n=jWYQ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 12"}}, "8.1.0": {"name": "commander", "version": "8.1.0", "devDependencies": {"tsd": "^0.17.0", "jest": "^27.0.4", "eslint": "^7.29.0", "ts-jest": "^27.0.3", "standard": "^16.0.3", "typescript": "^4.3.4", "@types/jest": "^26.0.23", "@types/node": "^14.17.3", "eslint-plugin-jest": "^24.3.6", "eslint-config-standard": "^16.0.3", "@typescript-eslint/parser": "^4.27.0", "@typescript-eslint/eslint-plugin": "^4.27.0"}, "dist": {"shasum": "db36e3e66edf24ff591d639862c6ab2c52664362", "tarball": "https://registry.npmjs.org/commander/-/commander-8.1.0.tgz", "fileCount": 12, "integrity": "sha512-mf45ldcuHSYShkplHHGKWb4TrmwQadxOn7v4WuhDJy0ZVoY5JFajaRDKD0PNe5qXzBX0rhovjTnP6Kz9LETcuA==", "signatures": [{"sig": "MEUCIEl7B2A5bY36X258Csd0Wun2OqtCJoIv117te9edJCcsAiEAqSvBdDRQWMTjcb9iWpb3PyW8WaSulod/y+iIpcizHFM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 143321, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg/4PmCRA9TVsSAnZWagAA4zYQAJRrf/GUVlaSo/tFUZ1i\nXp5dIJKCj6xE2hzFvi7MEzpzbX9Yx7uAiN4TQ5Xx/MvyXayvo2bo9pTcUrIs\n9CAnqGmQh1CAHJnSxt+bzZAQ/1c+MG02TFrM3W4zMbgcjyEz24sJhh8nhlxI\n0AmhQqtNqVOwgvDdisv66uDD/e0Shc+x4iroouFQJv7+tZGBV/JU0HsZvpgJ\nVreTI+8KldNmbwEe7VGhgc9Y5Gl8DEzPVTisi1B0hJdueQJFunO6mInzBLZI\n6eA8yIQ6krSogha1hCHifcHU8qkZ6BBQUqLKG1O+lVIMhichumwhRKofBU7k\nWKK/fSLhTbfSZusAksAXoyWyz+2dgh4wFRMcNiADYCBymxaq2a75GcH1pdIQ\ndw30Sqb2Ho5SJAvX5cHpLIEjH7kC2QoLdtovgvxLNg9jKtFSloYZy6uZl9Sd\ny2P7Q2OZndZpq/ZwijLA2sXgOB/EoRPPPihDPOVT+K1OIwMor+fdRzJz7ZFj\ndDieqWCYHt3Xjj4bRRLX7l0sDiS5V5/xxl2YWr5ocI7JGf92jGnRGAxdoRxh\n4r/t57LuJ2Sp6GV3F+QC1Ek2OfIVfz/jL+ZModdh0oW2XvfXh4n61iwZa83H\n92R4q9HaBSBhLRQl0cz9lf9hmrs3QmARmAqRrVPlNfCegIgTXKoGY3jAFTAn\nYS5V\r\n=JRn7\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 12"}}, "8.2.0": {"name": "commander", "version": "8.2.0", "devDependencies": {"tsd": "^0.17.0", "jest": "^27.0.4", "eslint": "^7.29.0", "ts-jest": "^27.0.3", "standard": "^16.0.3", "typescript": "^4.3.4", "@types/jest": "^26.0.23", "@types/node": "^14.17.3", "eslint-plugin-jest": "^24.3.6", "eslint-config-standard": "^16.0.3", "@typescript-eslint/parser": "^4.27.0", "@typescript-eslint/eslint-plugin": "^4.27.0"}, "dist": {"shasum": "37fe2bde301d87d47a53adeff8b5915db1381ca8", "tarball": "https://registry.npmjs.org/commander/-/commander-8.2.0.tgz", "fileCount": 13, "integrity": "sha512-LLKxDvHeL91/8MIyTAD5BFMNtoIwztGPMiM/7Bl8rIPmHCZXRxmSWr91h57dpOpnQ6jIUqEWdXE/uBYMfiVZDA==", "signatures": [{"sig": "MEQCICbMQKxMlq0M38M7+5p1kMbmsua7qRznYK2gcIQRopWeAiAUru7//z5x13mAwnO8Aj5dmQgiGtJXBYhN0ltBi1VlXA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 150526, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhOwKQCRA9TVsSAnZWagAArDsP/0SuFT++uruPwyIyLFCL\nEOKcvGK7Pat8Ze4oAPZO+MfIdjOI6kEDkootxu2IphUxt4ZA3Jfs8E/1ATQk\neL44YdewOj5Atqke3yIjcAkobWdDYdNxYMVh6PuT2QvpYHuSqNBQ8p0c+qh9\nUpLIDv2ZOshX2kaZTiCGVxp8TrilxJLSwFclpbF9lyWekJVhR9ly+ViDMj1K\nFlgG2uC31A51AjGTcKujLEXN2mzyIqDxngYxfB8Xw1nM0zl41cSEjK/Vmsd2\nN7B1UDApsb7UfCwPp9PGuhMZoJwOWqOHT7qjnkHIk/n5QovBu57hP+JeeKMG\nUtLdDTPmmifErOYxyD2WUkcbDUH7/Ai7Ofa2E5Ubi3Hww3gIw7iFZ+1ZxMT4\nQcIFg9LH6mv7iuAEJcvvBJoAZPtdwMubEWYUEDolbQA3YDDNloLRVTf4nQO6\n5CPsbTshU641VQNTtqhCOZP8LYHwiW9c/GuHsXiVEv4sGB9oY5lLPfIxUNLS\nwM/OWn5VDJD4LJHzB+yVG3VNBnB9HzmWe7V+tojaknmBu6NsASmlJBaa7IxX\nm3v0PAmfwaZFYv7vfbJfSotchOY45xQ+FasjPONG/gyBJNrPifHTZDzwgrXB\n2l6nWGfjCZAnxo3a3MCdLeKygD3EhEB/nFq1/4wNz1JNBdnUMwaRibXuteTb\n6P5q\r\n=ig11\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 12"}}, "8.3.0": {"name": "commander", "version": "8.3.0", "devDependencies": {"tsd": "^0.17.0", "jest": "^27.0.4", "eslint": "^7.29.0", "ts-jest": "^27.0.3", "standard": "^16.0.3", "typescript": "^4.3.4", "@types/jest": "^26.0.23", "@types/node": "^14.17.3", "eslint-plugin-jest": "^24.3.6", "eslint-config-standard": "^16.0.3", "@typescript-eslint/parser": "^4.27.0", "@typescript-eslint/eslint-plugin": "^4.27.0"}, "dist": {"shasum": "4837ea1b2da67b9c616a67afbb0fafee567bca66", "tarball": "https://registry.npmjs.org/commander/-/commander-8.3.0.tgz", "fileCount": 13, "integrity": "sha512-OkTL9umf+He2DZkUq8f8J9of7yL6RJKI24dVITBmNfZBmri9zYZQrKkuXiKhyfPSu8tUhnVBB1iKXevvnlR4Ww==", "signatures": [{"sig": "MEYCIQCTvkYjMZlMkau8hM4o9CT1rJmed2t13Q6eXSEo+sGsoQIhAPj0DACacRAViqyCS0davf0Biah/5ZIUZ7l40DkPhQl0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 151267}, "engines": {"node": ">= 12"}}, "9.0.0-0": {"name": "commander", "version": "9.0.0-0", "devDependencies": {"tsd": "^0.17.0", "jest": "^27.0.4", "eslint": "^7.29.0", "ts-jest": "^27.0.3", "standard": "^16.0.3", "typescript": "^4.3.4", "@types/jest": "^27.0.3", "@types/node": "^16.11.15", "eslint-plugin-jest": "^24.3.6", "eslint-config-standard": "^16.0.3", "@typescript-eslint/parser": "^4.27.0", "@typescript-eslint/eslint-plugin": "^4.27.0"}, "dist": {"shasum": "4709bd066c9b2477ded951f68bc381a9e2b1819f", "tarball": "https://registry.npmjs.org/commander/-/commander-9.0.0-0.tgz", "fileCount": 13, "integrity": "sha512-t74Nk7fzdtI4N2lxWUKAUdSmCZlHINFYKgARvo+gXHjHY0MUyTElpiqcJKimiSvZg2cMzjDdTwwr8Qhja1AcEA==", "signatures": [{"sig": "MEUCICxDuN3SpVGm4Q8TMl7FJjy+Wte/FGSy3y7mCFQxwQSNAiEA3waYO8mAYBmmoPT9zPjv1iw2dr68bPPvrVt5k+O+Zb8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 156195, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhwruSCRA9TVsSAnZWagAAF04P/j302qtQ+HhQKc38KT4a\ndVn7w6wPQRC7apmpMucVVL/CyEhW9f8XthvP+i55yDnhXdMXNPtl3UcBTc/7\nSW4QwJbkIL7dlyvEYX0mf92JtEJcdtXJlaZEI+vjHssUrHOfyXX0OZ/rNZBi\n+cw/hKfvZfjBU3NhXuHmpgkEgllYbe4B4MEQy8+r+4NR/K7MlFk41bclOt9b\nth1h81viMumcQrTaO2M8Uvnk4s2NRi/wahCn5wsnYdGwgP0hmaWMEEKxXgUr\ndYc8hJ71SJ1NyXxD/k75UNrHW/kUugb2IpRYFY9SQiAlJ7DyKWO+wWQr4hs5\nO2/Lpzt8YgjrA0Wyo8wPHxm1k9DbiDk8i8MEw3DBVl87Y72Rx66cUesQcsHq\nwfTStK7qYbR6OHmpWHZpSdig2FveMuRXz3SKAH6UNvPOwB8XZJDtbeHCk7jR\nYSnln3Sx5+bKi08qM8e0bnHY3aePG0IwTDDxtsor3JWXVHUCHaiDGrozq/ox\npS19vu8+vTcMkj89KWw++5kVVNEulQkvuIenwyzJ9AOA8Z1fpoVOMvppyn4h\ndVOpFMrPdadCTtmQIEJQ6NEqWgWwPeA/E4T6vF2xCLam2w/zOpdGN59vSqpH\nFFpU84m1LobbP2e/oNk+rld1LMLGm4dL8wjXwq97KBAZ81YteCIke3BwOhdn\nyTgT\r\n=SSF5\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.20.0 || >=14"}}, "9.0.0-1": {"name": "commander", "version": "9.0.0-1", "devDependencies": {"tsd": "^0.17.0", "jest": "^27.0.4", "eslint": "^7.29.0", "ts-jest": "^27.0.3", "typescript": "^4.3.4", "@types/jest": "^27.0.3", "@types/node": "^16.11.15", "eslint-plugin-jest": "^24.3.6", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.25.3", "eslint-plugin-promise": "^5.2.0", "eslint-config-standard": "^16.0.3", "@typescript-eslint/parser": "^4.27.0", "@typescript-eslint/eslint-plugin": "^4.33.0", "eslint-config-standard-with-typescript": "^21.0.1"}, "dist": {"shasum": "4e462b5c7d1ec4df59acbf12439ffed46a294d77", "tarball": "https://registry.npmjs.org/commander/-/commander-9.0.0-1.tgz", "fileCount": 13, "integrity": "sha512-arL8xbMrDr6BfDbgS3RHICRBc7hKrLuMZVWyWswvpVYd8jLa4cv3AnEb/DnK8NenvTEJF5NgPa8/K9oiY94ezw==", "signatures": [{"sig": "MEQCIBGWcoycjWPRiPOpsRzSB/IWkvEClTWaJlMsNNFvBNPPAiAKXPhznjNuCu661vW2WfvzXzuc3MLWJ6J0joapLqiPFw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 159103, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4d4OCRA9TVsSAnZWagAApP8P/0MS7jQg5JOwabHTMyzb\njBTpLLnIsCSF8iqsPTLOF18Fj+SrCWnhZej6TsZpQprHZc/3+jqhfC3X+LWN\nUU5WVRFbFW658gkt2SYgfRZQmaBxHKGp8Ns8amqs682b99oqshIm0q3EZLcH\n73EviGJ7acnrIRuFf6FDXy7B11ZtPwDEiWiCKVR5ZJhhnF6+elLisWWh2MFH\n9odViCp51iecvq1VZEout4Avsyhl1FadQMu/sS3vGTYwrApZLDS+WIblRtlC\nuWCT31lvn6x5/cenb94rqWCIL19/KZgQ6ydxQ2qHlYrWxbfTgyn29leoPN/2\n43G1KPoMsi35Sm1iQF4XG5SPTVXZrQkhAHsTJeHIv9B3LYgl/j0kQvd9MtA7\ntW69LNGapOzk5CEqxSo3ZfkVOcFO9vtupG8dKY2QNOcRtgWmGbIiUwbNQ72w\nZR5qUUYAZMMJGigVrViV6JG8VfBhyRkLMnJ1UWyT18+O33m3ph57TVJbDh5/\nsZe/K2BJkaG+jT9v3sQpQpwvRMU+NzT9tWCMRybY9FMZLZ2nADnud3Q4MSxS\nosTqX1lUzguxVoBRXZsLDmoM58CrzAatjGhQlP2cLysNtm1g5nS/QWL4wTAt\nDQWb3YLtL8lEw05J/qi5Ox0/nkctXQ86VNWEYraKSlshvY/i5byYJjNKdV+2\nl0WW\r\n=zj+U\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.20.0 || >=14"}}, "9.0.0": {"name": "commander", "version": "9.0.0", "devDependencies": {"tsd": "^0.17.0", "jest": "^27.0.4", "eslint": "^7.29.0", "ts-jest": "^27.0.3", "typescript": "^4.3.4", "@types/jest": "^27.0.3", "@types/node": "^16.11.15", "eslint-plugin-jest": "^24.3.6", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.25.3", "eslint-plugin-promise": "^5.2.0", "eslint-config-standard": "^16.0.3", "@typescript-eslint/parser": "^4.27.0", "@typescript-eslint/eslint-plugin": "^4.33.0", "eslint-config-standard-with-typescript": "^21.0.1"}, "dist": {"shasum": "86d58f24ee98126568936bd1d3574e0308a99a40", "tarball": "https://registry.npmjs.org/commander/-/commander-9.0.0.tgz", "fileCount": 13, "integrity": "sha512-JJfP2saEKbQqvW+FI93OYUB4ByV5cizMpFMiiJI8xDbBvQvSkIk0VvQdn1CZ8mqAO8Loq2h0gYTYtDFUZUeERw==", "signatures": [{"sig": "MEUCIFMXh1L+6s1reN9h9hP+WQ5oZmSa/dPk1Cq3gx3DS8AzAiEAiDdbbzeELRAaIhWdUWKBgtEduAbakvNyKvX5y/hz9Ao=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 159103, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh9IqwCRA9TVsSAnZWagAAKfEP/1+5z7NTXJfEaIXNH3dY\nesLyZFR8SP+Tmm9w4W8howA3lQSJJRGU6T5LywUI5x4yc6HLornpk5nFJnIN\n9B1PTjBlgRl003psudg7EuCZMSbJWL211pwxKqa0kmj5l87S34PRe6wGGaEQ\nBiDTxgt0IUu+a36yA/L06VLwpZeO2kio8z6mPZCEcQwfDCKjO3Y35jr78N3S\nliO/pE4t76pDRqIma8nrrY9nx0mYzeI2GeX+XoqNYLAX4NbBRzsc3YKF5V8Q\nMAvlzDSqTmTwNs8a3rrvigZoTrXqLHkcwlnZz3tweF/xy+WD8wC7KhxjRvve\nbImAVhN8VcVHsL7J2Q6CrBv/mG8yAcmAyTo81cGei/GZJsIQMm4XZ7DFVheY\n57l7Kw0J8xFQPH9MVGW3Mot5a89Fc2/3IqndoIqbo9DwWWdvzLBxzdSA2MBa\nPThFf+5VcrXe71VQCRBWfcvOJ0mUYnwuUKZgPfmMNrR+SgqjaxSKO3tvNodK\nmkj5q8Dq6u2W2O+mGOMlfyytASH6/0LCFA2ZxEu5lBJnBbhb6066qlPpjT+W\nw5or2WTJNSd54WJocIesBZs5ZRXsZWG/eTeDHHQ6MvmzauR5+iYXf9QRYPJt\n7M5TyL3lmX2wGDiovIXZfAlzFRDxq6k/7UxtNdp1UXEgIDxqR46uHGC26nJG\nwj41\r\n=hbI2\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.20.0 || >=14"}}, "9.1.0": {"name": "commander", "version": "9.1.0", "devDependencies": {"tsd": "^0.17.0", "jest": "^27.0.4", "eslint": "^7.29.0", "ts-jest": "^27.0.3", "typescript": "^4.3.4", "@types/jest": "^27.0.3", "@types/node": "^16.11.15", "eslint-plugin-jest": "^24.3.6", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.25.3", "eslint-plugin-promise": "^5.2.0", "eslint-config-standard": "^16.0.3", "@typescript-eslint/parser": "^4.27.0", "@typescript-eslint/eslint-plugin": "^4.33.0", "eslint-config-standard-with-typescript": "^21.0.1"}, "dist": {"shasum": "a6b263b2327f2e188c6402c42623327909f2dbec", "tarball": "https://registry.npmjs.org/commander/-/commander-9.1.0.tgz", "fileCount": 13, "integrity": "sha512-i0/MaqBtdbnJ4XQs4Pmyb+oFQl+q0lsAmokVUH92SlSw4fkeAcG3bVon+Qt7hmtF+u3Het6o4VgrcY3qAoEB6w==", "signatures": [{"sig": "MEYCIQDE9y4pIADOIH0JgMymKd82AcrHxQh09VZqqcfonZQaagIhAO2eyxHuUIMPvNekK2O+jG/YvoflHWT2WMcrQOnX7W7y", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 162734, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiNB8FACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqyJA/9FLJGLVapbl69UKld2WW74UouICXSC0/3C/99vr8MxMG7qASr\r\n1ELuhEfpZC2cgcgfdFedGUNIRqlK7ldE6lScmDtx7n06HtNJof/wFh6J2mGq\r\nhLJDPOms0v+jin3F6vOfEwdM4+CpxiQcm8yVw0bSCV+kFXYZkAmBgihFyhjC\r\n/eLMsI5OcGLjNdh+pPQIgv0BG9snZXTZxh9v0nt5LRNQUaCe41uUEYxru65k\r\nrPG+1/0Sg/4HtP8+5IVySxv4be6hQOhOJnusu8IdlSbMmqUUghHNe/pnaWcb\r\nXX5I9tKv1SbAFEsj+lMWZhkl+GU2pJgFYyxHPsRW1p216sqoE5mmNMvymNA8\r\nmkBlgsh5LdFq0yQPQYf1NuAbsLO2jXePzkhJW16cfA49X37C87YLnhhMfBFa\r\npADsvuAWKSynoxVDTwSXtdnmqJ3zpeneQX4JRDNqxYP7skSaowgAkTLFEXcV\r\nBPyg4PRd3p6MBNk94LHF+8L8f/3vmAPvbvClrqI/TIgjGhBrgWoEphQG5AwD\r\nm3moX/gdxQv17p3DMkKWEr7k1S34vcFc/69YfNlXRyWsBGwD4d3hmE+7eAn2\r\n842Id1dV36c/uuYx8i03EPUz3l8TXHTzYlZkXZZxA2wEnJkugmKlgip+WjmI\r\noHotsPgtFijECaAJnwGzzpnCoYSGRXyc6kU=\r\n=HqSt\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.20.0 || >=14"}}, "9.2.0": {"name": "commander", "version": "9.2.0", "devDependencies": {"tsd": "^0.17.0", "jest": "^27.0.4", "eslint": "^7.29.0", "ts-jest": "^27.0.3", "typescript": "^4.3.4", "@types/jest": "^27.0.3", "@types/node": "^16.11.15", "eslint-plugin-jest": "^24.3.6", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.25.3", "eslint-plugin-promise": "^5.2.0", "eslint-config-standard": "^16.0.3", "@typescript-eslint/parser": "^4.27.0", "@typescript-eslint/eslint-plugin": "^4.33.0", "eslint-config-standard-with-typescript": "^21.0.1"}, "dist": {"shasum": "6e21014b2ed90d8b7c9647230d8b7a94a4a419a9", "tarball": "https://registry.npmjs.org/commander/-/commander-9.2.0.tgz", "fileCount": 13, "integrity": "sha512-e2i4wANQiSXgnrBlIatyHtP1odfUp0BbV5Y5nEGbxtIrStkEOAAzCUirvLBNXHLr7kwLvJl6V+4V3XV9x7Wd9w==", "signatures": [{"sig": "MEUCIQCIBIQ/rLBbQNE86QY3zNbwqpqy3QYTlM8ck9xYLxv0mwIgX/aJgq/15aQGXrGeioFgCERDQphjPnPt21DQadBZkCQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 163823, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWSo0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqZrw/+OV277VrhZFxWfjn5vyTuOD8427DBYfvux9Y6n9AUDH8kCTyc\r\n20TvYkuhU1Tk8JoFgP8IiRrhvKc/a2du3BVlgyshdqL8aNCBhGhGpj/DK8H9\r\nZn6jqAuOWR1ARKBS3iTJNLTG4nY5mdqDR0YVCCRGmuFTiiVjq6zA8ue3C8W7\r\ntLZmjnOKSSTfQUdGfiwGRnUdRAFYH3IRX1DI+4FWDynZAe+TDujTByW+ZL/7\r\nZmvOai8gu3DgFQWigQn75bu+MD8qWZrnc5s+oSIN31KFlPdsDpKqnPh8MP0/\r\n3DDq4rcy1Pf6PXifrXTc2HhEFxxs8ZIvOqxmbfG9VQxGfmkW+whe6p8fn3Eh\r\nc+dHMhSqicBP3V3O15sZJLNSsPDevahcJPUunnB09Q4W6C8DpFVvaTUNIrWa\r\nQFIal/dEB5BppGmjs6w9rx/lfrV94U5qXfUbicyHingryWuwXS3A+smZgtXS\r\nccZRxb34T/Omr+WHo288BOhu2V8Nzwhy1xfo7wY7F8Qc+hsGfyi83W+Wq/YQ\r\n1Hsdk22jC+Z4x/INwAFg3SLCAm4Vh2ntzchkClzvZ9/KPOp1qzYixzUbxW/G\r\nECNm94AFORwaOIAvshk56klWPR0RX7odePpPcZJ83vVc2M1e1NYL7pZ++3+f\r\n8XnNmAP9JXG4UvUiuTOe+wKMaFxtBOmykGs=\r\n=cxAB\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.20.0 || >=14"}}, "9.3.0": {"name": "commander", "version": "9.3.0", "devDependencies": {"tsd": "^0.17.0", "jest": "^27.0.4", "eslint": "^7.29.0", "ts-jest": "^27.0.3", "typescript": "^4.3.4", "@types/jest": "^27.0.3", "@types/node": "^16.11.15", "eslint-plugin-jest": "^24.3.6", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.25.3", "eslint-plugin-promise": "^5.2.0", "eslint-config-standard": "^16.0.3", "@typescript-eslint/parser": "^4.27.0", "@typescript-eslint/eslint-plugin": "^4.33.0", "eslint-config-standard-with-typescript": "^21.0.1"}, "dist": {"shasum": "f619114a5a2d2054e0d9ff1b31d5ccf89255e26b", "tarball": "https://registry.npmjs.org/commander/-/commander-9.3.0.tgz", "fileCount": 13, "integrity": "sha512-hv95iU5uXPbK83mjrJKuZyFM/LBAoCV/XhVGkS5Je6tl7sxr6A0ITMw5WoRV46/UaJ46Nllm3Xt7IaJhXTIkzw==", "signatures": [{"sig": "MEYCIQCqt1oj43rokg2mwu0zJGmult9cP2uHSoNKq0GhMtABvQIhAINfWX2eomVqVK3E44sKduBoE0Cv1TG75z6L6nZOSfQ5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 168750, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJikY0qACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoafxAAmu5ns5xh1iR4rCjN7Y67Ih2lNFdiYc9a8X2HMShNPb6gFM/F\r\np0RasV9IdPLOnHupbgfCUJLnTxPNwtfT5nOSt1iv7c7cpi8tlnIlEXbBe7h8\r\ntz795H5UPlwq7GvORSnX+K85uFlA1KY+nogsceSAkfrjc1UUjTPSHz+1os2W\r\nMUid8aknf7dQkWxf2R5PErDtTzMAfrqrrdbfcLRLratknK0h9AmFYNlaPlzL\r\nENn3SHi8G/ta915Xzq0Io7eNmnq0gr5mpOOKt6H9c7lautVGQB20jyDeDHgx\r\nIRRHFa2gUqR2hgctkKZsU4XXhGcTakNvunPP6D1S5jQ8cev14VEhkuxV+dsF\r\n6XtSkslxNrEJz/bT5M8b0zb6iMfgJZwVIz2ydDClR1cLoMo3y1JNYXCGf5A1\r\nRVGzcQ8Y60aC1U/uNlgNaurBbXDDztpluQtQb6hpxF+nUfVRgWJf5UanjsT3\r\nYrqvoQOAT6sPUOcMoFwyyOa8G2B9Rc66lb4wKSiO7Ioa5lZ2B83vE0qqPsfp\r\nsgYmVo8JQfsRmj94IHGVhAAhHXtSR+xmhuCAnK17W480/hjgOSjs1IIdH1Tq\r\nck/N9IZNjbKc4pvRA2QAzeoovp6ghlQuwNBbcm996OE5H0z+QwbFtWAECeIj\r\ngLA4hzvU4jqVKd+CQfvR+y1XnawHJ1GMlIY=\r\n=K7oX\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.20.0 || >=14"}}, "9.4.0": {"name": "commander", "version": "9.4.0", "devDependencies": {"tsd": "^0.22.0", "jest": "^28.1.2", "eslint": "^8.19.0", "ts-jest": "^28.0.5", "typescript": "^4.7.4", "@types/jest": "^28.1.4", "@types/node": "^16.11.15", "eslint-plugin-n": "^15.2.4", "eslint-plugin-jest": "^26.5.3", "eslint-plugin-import": "^2.25.3", "eslint-plugin-promise": "^6.0.0", "eslint-config-standard": "^17.0.0", "@typescript-eslint/parser": "^5.30.6", "@typescript-eslint/eslint-plugin": "^5.30.6", "eslint-config-standard-with-typescript": "^22.0.0"}, "dist": {"shasum": "bc4a40918fefe52e22450c111ecd6b7acce6f11c", "tarball": "https://registry.npmjs.org/commander/-/commander-9.4.0.tgz", "fileCount": 13, "integrity": "sha512-sRPT+umqkz90UA8M1yqYfnHlZA7fF6nSphDtxeywPZ49ysjxDQybzk13CL+mXekDRG92skbcqCLVovuCusNmFw==", "signatures": [{"sig": "MEUCIEf6QmdeSaLf3+an3+gq8ir/VGHLuSeUIV8B9B28gmOmAiEA94Oc5SeRiK8vE5Pqx8oLeVR/gdz4zDiAU8vExhMIk5Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 169596, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi0QcJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoxMw/+JuPMPhOqYOKFnGQQzEVJuQd49jAyjkSiuCk6aKmzXqWsJQfT\r\noxoSoroqsA/JcAAlohpL2kfgFbz+1p6e71R7YbgicU7aQFDExsm017MYGqtv\r\nnd10qdbwmSGIsbtY1I3h+ydnnxmMY5pu4tznTF1PBJa6WtU9/Z0uzNPIX7a2\r\njDuMA5mpufKU89qBF8a3g9h1xUpXJ1ye4AbkkOwuYNQazSGGkjqt+7pRTcex\r\nPH6Df+i3CvUx3X/lGvhwe3NjLDHfVzyaePxYz8ill23tcFGDbo/PNpRNAmon\r\nv3m7O4QGviD/Tth37FSm/MBBoiKZzXco+JPkFcRQ2w673ydJdpZe4a/KGRKg\r\n75AqTN7+Jpx6GMiTpk2PziWYZEYE2bGwXKPxSKA00fXu0VzjTpVYBy7wN9CK\r\n9OjrWG+btaqRwWerf3nqFD8MG3MwVVLt0qugms6AhwndDapR9Ml4D3pydo+G\r\n/JNmUwwgyWMTSgYWpnXZCjP1ryMqZvzo0YRRsrobrLJRz+fkqv9ktzaa0J+l\r\n/ARQjc9GAcRAacISWWbxlADlyTHCohe6yRYSN62rYhjgAWbXTqlB+HlOu1cx\r\nVTVMPEabquUsqSepvBqTnibgtBdSpZBS1nIAGarJfLvzt2/JKf9qNDAlVoHh\r\nmSZvBfheG7qjKf1X8p5lNrfEK10Q4h30tzY=\r\n=EsY2\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.20.0 || >=14"}}, "9.4.1": {"name": "commander", "version": "9.4.1", "devDependencies": {"tsd": "^0.22.0", "jest": "^28.1.2", "eslint": "^8.19.0", "ts-jest": "^28.0.5", "typescript": "^4.7.4", "@types/jest": "^28.1.4", "@types/node": "^16.11.15", "eslint-plugin-n": "^15.2.4", "eslint-plugin-jest": "^26.5.3", "eslint-plugin-import": "^2.25.3", "eslint-plugin-promise": "^6.0.0", "eslint-config-standard": "^17.0.0", "@typescript-eslint/parser": "^5.30.6", "@typescript-eslint/eslint-plugin": "^5.30.6", "eslint-config-standard-with-typescript": "^22.0.0"}, "dist": {"shasum": "d1dd8f2ce6faf93147295c0df13c7c21141cfbdd", "tarball": "https://registry.npmjs.org/commander/-/commander-9.4.1.tgz", "fileCount": 13, "integrity": "sha512-5EEkTNyHNGFPD2H+c/dXXfQZYa/scCKasxWcXJaWnNJ99pnQN9Vnmqow+p+PlFPE63Q6mThaZws1T+HxfpgtPw==", "signatures": [{"sig": "MEUCIFRS4i402M/vySu0tExO8LjawFKsijSAt1xdtUvEgITWAiEA9RwfQsP2b0dcfSH87hjQvcujZy5U6eSZ/PkDYbY8Klg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 170255, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNpqEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrDdQ/+NIlmWtECOBd8wkHULQPIGG+b9CXZENS0Yk0wNlrbIiB5BP2q\r\nLJlQwb2fDVhMvlmwigF+x2vvgyaubKkKohuc2W/CT7YldynFHQWXLHpZR3QD\r\n60uzVnFKzY5ra8uyWzMgyay0B3gYkhOT0qjVjpSvwUmgBp3XfH3LWezAs6u+\r\nQEH/EACjebIktNQNvqcY2KWvhAiLLWhynRJcN6ONMTlqnT4kTeUj7dIm8bJZ\r\nF3jipDSFr7Y50l9tNQJyBR7Ca9WNx/LINq5VfevjMqP00HMRhKcFMCsQdxBA\r\noE3HPSjZ3Mgf/XALE6rZFlFGmCRdw4Qtu1dSUOd9dB3TsX93bSppiVKWY2ax\r\nAX+8PHC5uvxAAg3rAuzwbe0sCk+LspCh3zbWpAArbQ6h1+uJceEzg+s0mruz\r\nH+YL2SOM0Kzkg1+l4sxvP9TpgLXHxyOBh23u5I1e7/V3TK9Vrxr0THAkFRIX\r\nbUN6w8waatr6vPrxjva09lkV2tHSW9vptCbQ+dHVbCY/KgSGry4OYJxVF3gU\r\nfD8+vTdfU9RIndqTbdLlhXQUJAWGPYKrd5segA/2k8WqKf8Uuh8AFUZTrNVj\r\n4ezYbXQBsIWTrVp720ssxEbF0OSuD/wjSZInPTevsB92Yk3znnw8Sx0JaKZL\r\nLEzRtc+Vz3XF9Cicp0qo1YEDb369BDumWmg=\r\n=mrwF\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.20.0 || >=14"}}, "9.5.0": {"name": "commander", "version": "9.5.0", "devDependencies": {"tsd": "^0.22.0", "jest": "^28.1.2", "eslint": "^8.19.0", "ts-jest": "^28.0.5", "typescript": "^4.7.4", "@types/jest": "^28.1.4", "@types/node": "^16.11.15", "eslint-plugin-n": "^15.2.4", "eslint-plugin-jest": "^26.5.3", "eslint-plugin-import": "^2.25.3", "eslint-plugin-promise": "^6.0.0", "eslint-config-standard": "^17.0.0", "@typescript-eslint/parser": "^5.30.6", "@typescript-eslint/eslint-plugin": "^5.30.6", "eslint-config-standard-with-typescript": "^22.0.0"}, "dist": {"shasum": "bc08d1eb5cedf7ccb797a96199d41c7bc3e60d30", "tarball": "https://registry.npmjs.org/commander/-/commander-9.5.0.tgz", "fileCount": 13, "integrity": "sha512-KRs7WVDKg86PWiuAqhDrAQnTXZKraVcCc6vFdL14qrZ/DcWwuRo7VoiYXalXO7S5GKpqYiVEwCbgFDfxNHKJBQ==", "signatures": [{"sig": "MEQCIBbtDgW9BNWrOvjCqr6RFDuuBT/c9591XUP9+8IbN/viAiBR4pOg7rHwAQo5RR0HtkCtZh6SbG/jrC8kXFIrXxcgzA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 173252, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjuRmNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpWAg//S9Z4RBLYQxfpiLDjtG3eewGccT3kkpJ9bivcWhV+uH9SAwhN\r\ns6iMRF83LUuMfqu+2aC3QOGrXynxnBscTn1wId61AkCiOAYvsZsr8mTH3PC3\r\neMtGZt4eQFnI8Vb+4zyEWp9gPok2MmdlZh5AKe7rYOmYJolG6g84gDwcbUBJ\r\ny3hFKq3P2UMMCw7ims2QAo1a5AK+8w1pzHjMdfb6TSq+dBkqMQnTkKFrUE3u\r\nRQWjbiG2J5KuIzJ0ZrhBCPKgH9oRngBAHvL3miuJa+kUOEpOGzNWySqe28ps\r\nGvKQkpwpnzipnl570vgdgJVBbJqVgj+V1CQK0mA1FUNEor/n2CtTi45l4Aew\r\nesipEpBRb1hVI6leJMrLAb6+pQ6S3mEL4wnzyroIxm2/O2nt6O3NJPbQe+DN\r\n8zZdnncFOrh1GeIUnIhAK4dOr1cMX120J7XBysWN99G+qLK2NVmdqeUsg+nJ\r\ni5hSwGx4tH3OXxR1UY9oYBj2mwlNNmeyuLBNnxnH0NZZAP4jEUmni4ajTLyD\r\n+MK49K2vzZS04HLdQBzCTMIPNxX7dyIML2PSL++or82q4hogF6SRaHUfPtDA\r\n/Sh5MMLnWJQTCcYdZ5ZOHCTF6+tKbu9hTFbTPKpmLou88F4+rAGvVoTNIeke\r\n6bSZwmVDpqEu/PDaHOXID9PAKg3X3VYG+Lw=\r\n=SBts\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.20.0 || >=14"}}, "10.0.0": {"name": "commander", "version": "10.0.0", "devDependencies": {"tsd": "^0.25.0", "jest": "^29.3.1", "eslint": "^8.30.0", "ts-jest": "^29.0.3", "typescript": "^4.9.4", "@types/jest": "^29.2.4", "@types/node": "^18.11.18", "eslint-plugin-n": "^15.6.0", "eslint-plugin-jest": "^27.1.7", "eslint-plugin-import": "^2.26.0", "eslint-plugin-promise": "^6.1.1", "eslint-config-standard": "^17.0.0", "@typescript-eslint/parser": "^5.47.1", "@typescript-eslint/eslint-plugin": "^5.47.1", "eslint-config-standard-with-typescript": "^24.0.0"}, "dist": {"shasum": "71797971162cd3cf65f0b9d24eb28f8d303acdf1", "tarball": "https://registry.npmjs.org/commander/-/commander-10.0.0.tgz", "fileCount": 13, "integrity": "sha512-zS5PnTI22FIRM6ylNW8G4Ap0IEOyk62fhLSD0+uHRT9McRCLGpkVNvao4bjimpK/GShynyQkFFxHhwMcETmduA==", "signatures": [{"sig": "MEYCIQC20DufiOmKMzBPAy9TozrTyfB6OkqptSLk5/46uiMDjQIhAL1lPCbcKxFKOB7z4WlN6BMY9f4o8ZOH4aW1oRGBIbEh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 173653, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjwgslACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp2Ew/+InYpoqkMJez7x/YUHx3xaoS203GkJL3of04oArkjd3fW1Y2U\r\nILrIAbX6qmHH4dq7OpFaSSZHPYwQkk3AtELMwpZ/0Q0zO3NNmB2QKwAoP3QF\r\no3CXxvjU3u4vIlSEBXiiO2zMuD2bJ5CsP7c7syIsUrv/q47yUDb/GWk0k0sP\r\nTzXEN8SjfTX0blkQ2d4GmDMICyA4lj/Td6VMIgPoBujdYb+TBKo1DfwZBdaz\r\nWq2Tb7YrnAALHR/dAluQT8k8kRsale30ob9YHT/EOII+eJU5VA62MwNlBp2R\r\n7UlUC4ryMAte/E676zo6Ua0d0SxTwJTrE/CB1ms1605xxg1+fVvXBgedAF2Y\r\nUyeRAR3ZFnNqXE1X+KH6P76f2FWlYS1/J94ARPeT6cpT+z4rRXvv+Yg4c1HD\r\nKMdw4GfXHFg1syIOiEC2bbd3ge5OCa3EbpEJOzEqhzFR1AkTaXS9IjmfPe8t\r\nJPsRZLkcXCfhEbhaNKhItTR7UUhvoVl9aTiKyByYFLMulAEo1dxOY4pl0B28\r\nP6J2vqCyMwb/kKmLNtPsF9+n6/ArNIbNcBZgiDBTH0oukL5Zxxq9Q2iTIvaF\r\nsU/xSjP5CW+v3WoqIFwzVSa83uWV66eqPHpGOXxiyPcXRctTTmPQwP8v9J9A\r\n6M//sPEf3L8coY88JTAOcRSMd125BrmMGtY=\r\n=dMOf\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=14"}}, "10.0.1": {"name": "commander", "version": "10.0.1", "devDependencies": {"tsd": "^0.25.0", "jest": "^29.3.1", "eslint": "^8.30.0", "ts-jest": "^29.0.3", "typescript": "^4.9.4", "@types/jest": "^29.2.4", "@types/node": "^18.11.18", "eslint-plugin-n": "^15.6.0", "eslint-plugin-jest": "^27.1.7", "eslint-plugin-import": "^2.26.0", "eslint-plugin-promise": "^6.1.1", "eslint-config-standard": "^17.0.0", "@typescript-eslint/parser": "^5.47.1", "@typescript-eslint/eslint-plugin": "^5.47.1", "eslint-config-standard-with-typescript": "^24.0.0"}, "dist": {"shasum": "881ee46b4f77d1c1dccc5823433aa39b022cbe06", "tarball": "https://registry.npmjs.org/commander/-/commander-10.0.1.tgz", "fileCount": 13, "integrity": "sha512-y4Mg2tXshplEbSGzx7amzPwKKOCGuoSRP/CjEdwwk0FOGlUbq6lKuoyDZTNZkmxHdJtp54hdfY/JUrdL7Xfdug==", "signatures": [{"sig": "MEUCIAdfY3b4EC42gKHGu+VwkrhYwtZc39I3Gew/XLCd9cmmAiEAgVdaQ7bsLSjZ6XPBVuJves9Tou2c5mm4lYu6l5bLI7g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 174283, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkOi/EACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqghRAAjGC4IvMW2ZN86qlSobS47HdFySSatonSysNxhXIzyqqIAg5r\r\npO3ALz2CGUI8fpUTUzpZUfa2fuW7oDxbJAAilqLtAeBKfPEq9LsNOE98UBJz\r\nPh9v9S7AAC96m90KOz30EIoKQVhgVLJ+AYSe2fBzikgjacmVVOJ9vN4luAVr\r\nxL5svvG6+85Nmr55aY9wV9HVnXejZTdLZ2Z5iXo0CXKvhERr6PCHF9QEZ8Hp\r\n/OPQzItFekxQBA4cM8T8dNiTqAz2iQCd1m04zg9WvHwcFRblEFkXTj2Dgz/Q\r\n78WHapsCcdL1qkYNB6nowTkHNmAZjTFXv5ve0G7lU/h/PhGaOwg477DosaJg\r\n882yfhO1u9uEyCx4yi8jjl1bZt01ZgRaLkOFp874CaTWer15h0nVKngvNksk\r\nMnyUb0brlNuPlMs6qyNI0UpGFWi/IXeE79agfGvh2W697LXb2cNJmAPyGszE\r\nv3aax5xCG/uvtnkOZUgPk8m1mikXpaSYEN1zucVgbE0SNkvRdVnOKQOa4Ldi\r\nGgD+9znAHrxBbBcwO/XadZkNt4pigFQ7WFJSFkUsGglcATt2zr2snpQ2EXHx\r\nIKUs8uNfxwkdMBzSHiBNbsD6VVpynv6OxNe08t/2grNwsoGrlwpq1vAxaxDa\r\nFDXw/9ABkJ7L0fq6PibWQxIdqActL4UuylA=\r\n=y0So\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=14"}}, "11.0.0": {"name": "commander", "version": "11.0.0", "devDependencies": {"tsd": "^0.28.1", "jest": "^29.3.1", "eslint": "^8.30.0", "ts-jest": "^29.0.3", "typescript": "^5.0.4", "@types/jest": "^29.2.4", "@types/node": "^20.2.5", "eslint-plugin-n": "^15.6.0", "eslint-plugin-jest": "^27.1.7", "eslint-plugin-import": "^2.26.0", "eslint-plugin-promise": "^6.1.1", "eslint-config-standard": "^17.0.0", "@typescript-eslint/parser": "^5.47.1", "@typescript-eslint/eslint-plugin": "^5.47.1", "eslint-config-standard-with-typescript": "^33.0.0"}, "dist": {"shasum": "43e19c25dbedc8256203538e8d7e9346877a6f67", "tarball": "https://registry.npmjs.org/commander/-/commander-11.0.0.tgz", "fileCount": 14, "integrity": "sha512-9HMlXtt/BNoYr8ooyjjNRdIilOTkVJXB+GhxMTtOKwk0R4j4lS4NpjuqmRxroBfnfTSHQIHQB7wryHhXarNjmQ==", "signatures": [{"sig": "MEUCIQCnL/Y6uyE9c40ZCWmhPrH1cK74I1hKigKXY8Dx7SgMXgIgc1aQbAyRDJ48vZXIOjfoF66Awtb9r0pX40JNLbGShlQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 175557}, "engines": {"node": ">=16"}}, "11.1.0": {"name": "commander", "version": "11.1.0", "devDependencies": {"tsd": "^0.28.1", "jest": "^29.3.1", "eslint": "^8.30.0", "ts-jest": "^29.0.3", "typescript": "^5.0.4", "@types/jest": "^29.2.4", "@types/node": "^20.2.5", "eslint-plugin-n": "^15.6.0", "eslint-plugin-jest": "^27.1.7", "eslint-plugin-import": "^2.26.0", "eslint-plugin-promise": "^6.1.1", "eslint-config-standard": "^17.0.0", "@typescript-eslint/parser": "^5.47.1", "@typescript-eslint/eslint-plugin": "^5.47.1", "eslint-config-standard-with-typescript": "^33.0.0"}, "dist": {"shasum": "62fdce76006a68e5c1ab3314dc92e800eb83d906", "tarball": "https://registry.npmjs.org/commander/-/commander-11.1.0.tgz", "fileCount": 14, "integrity": "sha512-yPVavfyCcRhmorC7rWlkHn15b4wDVgVmBA7kV4QVBsF7kv/9TKJAbAXVTxvTnwP8HHKjRCJDClKbciiYS7p0DQ==", "signatures": [{"sig": "MEQCIG4OXx0HEJTNxkBjrp8POwBJUFrpuZiWKJAOYJujZTTKAiAuz9dTVRR7iBlcXkZRVNYjCzOHIXwsSjYaSqg6j2KbAA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 176667}, "engines": {"node": ">=16"}}, "12.0.0-0": {"name": "commander", "version": "12.0.0-0", "devDependencies": {"tsd": "^0.29.0", "jest": "^29.3.1", "eslint": "^8.30.0", "ts-jest": "^29.0.3", "typescript": "^5.0.4", "@types/jest": "^29.2.4", "@types/node": "^20.2.5", "eslint-plugin-n": "^16.2.0", "eslint-plugin-jest": "^27.1.7", "eslint-plugin-import": "^2.26.0", "eslint-plugin-promise": "^6.1.1", "eslint-config-standard": "^17.0.0", "@typescript-eslint/parser": "^6.7.5", "@typescript-eslint/eslint-plugin": "^6.7.5", "eslint-config-standard-with-typescript": "^39.1.1"}, "dist": {"shasum": "5b82ae6a6d2433b4035bfc71157f76569840aca7", "tarball": "https://registry.npmjs.org/commander/-/commander-12.0.0-0.tgz", "fileCount": 14, "integrity": "sha512-UsCJj6ASq58nS+xEF4P/eCKYaBC938xOTQMJnTEdUOn0EZ63zHjr4ewwbf++T/w7kdpY57dW2Ff4Jsk2xL+aUg==", "signatures": [{"sig": "MEYCIQDlZ8WXjC+8tqyrZG8O/tB9MfklxGn4GPPq8EBsk2RvQAIhAJ9dQdqxHosxZ3UyAsW6EpS7scDZHzLoC/NmnLj1I2AZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 178431}, "engines": {"node": ">=18"}}, "12.0.0-1": {"name": "commander", "version": "12.0.0-1", "devDependencies": {"tsd": "^0.29.0", "jest": "^29.3.1", "eslint": "^8.30.0", "ts-jest": "^29.0.3", "typescript": "^5.0.4", "@types/jest": "^29.2.4", "@types/node": "^20.2.5", "eslint-plugin-n": "^16.2.0", "eslint-plugin-jest": "^27.1.7", "eslint-plugin-import": "^2.26.0", "eslint-plugin-promise": "^6.1.1", "eslint-config-standard": "^17.0.0", "@typescript-eslint/parser": "^6.7.5", "@typescript-eslint/eslint-plugin": "^6.7.5", "eslint-config-standard-with-typescript": "^40.0.0"}, "dist": {"shasum": "8f23171fad23c9238e8e3ec014eb589efdf172c1", "tarball": "https://registry.npmjs.org/commander/-/commander-12.0.0-1.tgz", "fileCount": 14, "integrity": "sha512-43nKW58vMv4OVD6Xm+KIlF9lx9hpbwaA0A3bSz6kpsMzkxBOhkPv/wsrU/ZC4Is5xaKqDKF5CBRcM/h4xkz47Q==", "signatures": [{"sig": "MEUCIQChzU9cD1CyuG74UEtTDRfORwFaNWBvjc1ViVQ8Cf7TmAIgOtM+6h1i1L2MUm5Y9rAk7KdZQwhfGvgZCDL6ZuAAY2A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 181044}, "engines": {"node": ">=18"}}, "12.0.0": {"name": "commander", "version": "12.0.0", "devDependencies": {"tsd": "^0.30.4", "jest": "^29.3.1", "eslint": "^8.30.0", "ts-jest": "^29.0.3", "typescript": "^5.0.4", "@types/jest": "^29.2.4", "@types/node": "^20.2.5", "eslint-plugin-n": "^16.2.0", "eslint-plugin-jest": "^27.1.7", "eslint-plugin-import": "^2.26.0", "eslint-plugin-promise": "^6.1.1", "eslint-config-standard": "^17.0.0", "@typescript-eslint/parser": "^6.7.5", "@typescript-eslint/eslint-plugin": "^6.7.5", "eslint-config-standard-with-typescript": "^40.0.0"}, "dist": {"shasum": "b929db6df8546080adfd004ab215ed48cf6f2592", "tarball": "https://registry.npmjs.org/commander/-/commander-12.0.0.tgz", "fileCount": 14, "integrity": "sha512-MwVNWlYjDTtOjX5PiD7o5pK0UrFU/OYgcJfjjK4RaHZETNtjJqrZa9Y9ds88+A+f+d5lv+561eZ+yCKoS3gbAA==", "signatures": [{"sig": "MEYCIQCqMcOJ/77v7Uz/pTyod3HlhUtEaNzX7jUj1/9ZJWNKJwIhALSSkM5pUUoYdfAocjnVTK5Sw5PqEZOyUTJ57l1k/c1Y", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 181031}, "engines": {"node": ">=18"}}, "12.1.0": {"name": "commander", "version": "12.1.0", "devDependencies": {"tsd": "^0.31.0", "jest": "^29.3.1", "eslint": "^8.30.0", "globals": "^13.24.0", "ts-jest": "^29.0.3", "prettier": "^3.2.5", "@eslint/js": "^8.56.0", "typescript": "^5.0.4", "@types/jest": "^29.2.4", "@types/node": "^20.2.5", "typescript-eslint": "^7.0.1", "eslint-plugin-jest": "^28.3.0", "eslint-plugin-jsdoc": "^48.1.0", "prettier-plugin-jsdoc": "^1.3.0", "eslint-config-prettier": "^9.1.0"}, "dist": {"shasum": "01423b36f501259fdaac4d0e4d60c96c991585d3", "tarball": "https://registry.npmjs.org/commander/-/commander-12.1.0.tgz", "fileCount": 14, "integrity": "sha512-Vw8qHK3bZM9y/P10u3Vib8o/DdkvA2OtPtZvD871QKjy74Wj1WSKFILMPRPSdUSx5RFK1arlJzEtA4PkFgnbuA==", "signatures": [{"sig": "MEYCIQDGfZX/89afRyWbnvCN6Ti/61zQGiZDewN03eez9Ni/fgIhAMk8A/9F2Tbtcna/SjHl/aJn7Uc68PQxZMzK36OzHsQx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 186398}, "engines": {"node": ">=18"}}, "13.0.0-0": {"name": "commander", "version": "13.0.0-0", "devDependencies": {"tsd": "^0.31.0", "jest": "^29.3.1", "eslint": "^8.57.1", "globals": "^15.7.0", "ts-jest": "^29.0.3", "prettier": "^3.2.5", "@eslint/js": "^9.4.0", "typescript": "^5.0.4", "@types/jest": "^29.2.4", "@types/node": "^22.7.4", "typescript-eslint": "^8.12.2", "eslint-plugin-jest": "^28.3.0", "eslint-config-prettier": "^9.1.0"}, "dist": {"shasum": "1ec4503b69359ec74ea31d9eff53491f07d9dce6", "tarball": "https://registry.npmjs.org/commander/-/commander-13.0.0-0.tgz", "fileCount": 14, "integrity": "sha512-3CshrHCF8M4mJhtBruqZvYzMoRplFO1NmsIzF6uaIxZpzvW+ZI6xU/fx5LmprLNYB5sH5F2CnjTkXflGfkYgkQ==", "signatures": [{"sig": "MEYCIQCcbokr333aCVfqogUYtd+ft2aycM3Y2/UHH6OFjrDEPwIhAKQ/J0o+wx+WRW/zbjnZ+uJNbXx9NxAD2odxDzn5JBGT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 196088}, "engines": {"node": ">=18"}}, "13.0.0": {"name": "commander", "version": "13.0.0", "devDependencies": {"tsd": "^0.31.0", "jest": "^29.3.1", "eslint": "^9.17.0", "globals": "^15.7.0", "ts-jest": "^29.0.3", "prettier": "^3.2.5", "@eslint/js": "^9.4.0", "typescript": "^5.0.4", "@types/jest": "^29.2.4", "@types/node": "^22.7.4", "typescript-eslint": "^8.12.2", "eslint-plugin-jest": "^28.3.0", "eslint-config-prettier": "^9.1.0"}, "dist": {"shasum": "1b161f60ee3ceb8074583a0f95359a4f8701845c", "tarball": "https://registry.npmjs.org/commander/-/commander-13.0.0.tgz", "fileCount": 14, "integrity": "sha512-oPYleIY8wmTVzkvQq10AEok6YcTC4sRUBl8F9gVuwchGVUCTbl/vhLTaQqutuuySYOsu8YTgV+OxKc/8Yvx+mQ==", "signatures": [{"sig": "MEQCIAfq9DCLBTnsNln8wX2GrEEQgV2iyqyj5bffmTcjNmYoAiAGuHeloTv2kgjXmKaG7h7qnJGSVg3IDLdhbCdtQ7fowg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 199411}, "engines": {"node": ">=18"}}, "13.1.0": {"name": "commander", "version": "13.1.0", "devDependencies": {"tsd": "^0.31.0", "jest": "^29.3.1", "eslint": "^9.17.0", "globals": "^15.7.0", "ts-jest": "^29.0.3", "prettier": "^3.2.5", "@eslint/js": "^9.4.0", "typescript": "^5.0.4", "@types/jest": "^29.2.4", "@types/node": "^22.7.4", "typescript-eslint": "^8.12.2", "eslint-plugin-jest": "^28.3.0", "eslint-config-prettier": "^9.1.0"}, "dist": {"shasum": "776167db68c78f38dcce1f9b8d7b8b9a488abf46", "tarball": "https://registry.npmjs.org/commander/-/commander-13.1.0.tgz", "fileCount": 14, "integrity": "sha512-/rFeCpNJQbhSZjGVwO9RFV3xPqbnERS8MmIQzCtD/zl6gpJuV/bMLuN92oG3F7d8oDEHHRrujSXNUr8fpjntKw==", "signatures": [{"sig": "MEYCIQDmyGTBaLFQN4/SExNA2Zz8YhU4S4D3LUPYXaJePOktPQIhALa2IBUy9wXlnRmenlSlaWTxtSvKa1gmUTzhiPl6T6F7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 200514}, "engines": {"node": ">=18"}}, "14.0.0": {"name": "commander", "version": "14.0.0", "devDependencies": {"tsd": "^0.31.0", "jest": "^29.3.1", "eslint": "^9.17.0", "globals": "^16.0.0", "ts-jest": "^29.0.3", "prettier": "^3.2.5", "@eslint/js": "^9.4.0", "typescript": "^5.0.4", "@types/jest": "^29.2.4", "@types/node": "^22.7.4", "typescript-eslint": "^8.12.2", "eslint-plugin-jest": "^28.3.0", "eslint-config-prettier": "^10.0.1"}, "dist": {"shasum": "f244fc74a92343514e56229f16ef5c5e22ced5e9", "tarball": "https://registry.npmjs.org/commander/-/commander-14.0.0.tgz", "fileCount": 14, "integrity": "sha512-2uM9rYjPvyq39NwLRqaiLtWHyDC1FvryJDa2ATTVims5YAS4PupsEQsDvP14FqhFr0P49CYDugi59xaxJlTXRA==", "signatures": [{"sig": "MEYCIQCOh07iotg66yGE6l7Hlxgj0e/8T7YE7NRh+Ci3rLppowIhAJVbXCIGb8ayS3pHbEmamarp0JGNw15bbJd6kjmoUXvR", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 208171}, "engines": {"node": ">=20"}}}, "modified": "2025-05-18T23:00:59.689Z"}