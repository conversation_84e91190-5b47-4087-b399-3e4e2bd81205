{"_id": "merge-stream", "_rev": "100-82eb1fc5be7bd0ca5a58caf232aa7609", "name": "merge-stream", "dist-tags": {"latest": "2.0.0"}, "versions": {"0.0.0": {"name": "merge-stream", "version": "0.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "merge-stream@0.0.0", "maintainers": [{"name": "grncdr", "email": "<EMAIL>"}], "dist": {"shasum": "aad0909f8acafdb260b2f03586ee9ead4710d170", "tarball": "https://registry.npmjs.org/merge-stream/-/merge-stream-0.0.0.tgz", "integrity": "sha512-AfPlS1qE35NgI66+xE5OMqDq3Rsdp3XcBqPhtVJ3w5WU2PJ8GKEjfyS/6gkLSAYMSevV2KEG/FPgkIcCotvuBQ==", "signatures": [{"sig": "MEUCIQCc5olWODjPpz324mCAxuLoqs6ieLYEIXBXpc56UNAf+AIgBfNHbySoq4lynWAd1cMIjGSWvJzOXGUQKGg8HUuG7kM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": "", "description": "Create a stream that emits events from multiple other streams", "directories": {}}, "0.0.1": {"name": "merge-stream", "version": "0.0.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "merge-stream@0.0.1", "maintainers": [{"name": "grncdr", "email": "<EMAIL>"}], "homepage": "https://github.com/grncdr/merge-stream", "bugs": {"url": "https://github.com/grncdr/merge-stream/issues"}, "dist": {"shasum": "8e183240e30ce8b1c8fa9103ccbf45e83858e993", "tarball": "https://registry.npmjs.org/merge-stream/-/merge-stream-0.0.1.tgz", "integrity": "sha512-AD2XF88HAWezOuGcJmAlGX+eRq6cvB1dQw4sdaYF4Ge0gfdZXdUihlWEGXb+lA6OOihq952kOCFXcqnVN261bQ==", "signatures": [{"sig": "MEYCIQDeuGSYqNFAE1ZM9Tqi37lakg/hP9QmQlM/FASbnYwnZgIhAPO55LOB2AqPBmf7A0PRfQvvMDOVfjpYpMdhQt1jgKJU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "8e183240e30ce8b1c8fa9103ccbf45e83858e993", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "grncdr", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/grncdr/merge-stream", "type": "git"}, "_npmVersion": "1.4.8", "description": "Create a stream that emits events from multiple other streams", "directories": {}}, "0.1.1": {"name": "merge-stream", "version": "0.1.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "merge-stream@0.1.1", "maintainers": [{"name": "grncdr", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/grncdr/merge-stream/issues"}, "dist": {"shasum": "770ac82d20576fc04675b0cc4a2a54cebe899412", "tarball": "https://registry.npmjs.org/merge-stream/-/merge-stream-0.1.1.tgz", "integrity": "sha512-/ddd9sEk6Uu4d5MbUldYKlqTQrznRoCSRIAfL2V6UNL2XNOXYY5enUuuDB2v5GOMU3CSlB5XSq0yGH9r0+eGmQ==", "signatures": [{"sig": "MEUCICesD8C35hFN+ThezgCJR5NDxnm2iSgAxXhn8ZxgtdsGAiEAnQecBV7EA2TlrKP/lmjQKnpuf34AoEUj2j+pK6UYoCo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "grncdr", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/grncdr/merge-stream", "type": "git"}, "_npmVersion": "1.3.11", "description": "Create a stream that emits events from multiple other streams", "directories": {}}, "0.1.2": {"name": "merge-stream", "version": "0.1.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "merge-stream@0.1.2", "maintainers": [{"name": "grncdr", "email": "<EMAIL>"}], "homepage": "https://github.com/grncdr/merge-stream", "bugs": {"url": "https://github.com/grncdr/merge-stream/issues"}, "dist": {"shasum": "7052b73a9f7e4b42fe4a3d1fb3f1cb4956b159bc", "tarball": "https://registry.npmjs.org/merge-stream/-/merge-stream-0.1.2.tgz", "integrity": "sha512-yag7psVeiuvn/CDCkZzE7WB9ArJTJC8tN5MNVE2DuocGiHw9mdSLmQVcTEKp8kGhDxnnHsGEqlM4Ev3NQh9X5w==", "signatures": [{"sig": "MEUCIAlzi/7oA2M+9QpSc7+GBhnjfRCdn1NMQUXyZ969jFElAiEAgq1pBCEoHK7tBGro5TzeO1U/gzsSl9hDk/W+R/5hsuU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "7052b73a9f7e4b42fe4a3d1fb3f1cb4956b159bc", "gitHead": "cb43f6cd39189d13a8058e0bf3aabfe4de559a4b", "scripts": {"test": "istanbul cover test.js && istanbul check-cover --statements 100 --branches 100"}, "_npmUser": {"name": "grncdr", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/grncdr/merge-stream", "type": "git"}, "_npmVersion": "1.4.13", "description": "Create a stream that emits events from multiple other streams", "directories": {}, "dependencies": {"through2": "^0.5.1"}, "devDependencies": {"from2": "0.0.0", "istanbul": "^0.2.12"}}, "0.1.3": {"name": "merge-stream", "version": "0.1.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "merge-stream@0.1.3", "maintainers": [{"name": "grncdr", "email": "<EMAIL>"}], "homepage": "https://github.com/grncdr/merge-stream", "bugs": {"url": "https://github.com/grncdr/merge-stream/issues"}, "dist": {"shasum": "2953a3747eea4ecb3fc17c4a37a3b346ce8f08c9", "tarball": "https://registry.npmjs.org/merge-stream/-/merge-stream-0.1.3.tgz", "integrity": "sha512-VCDsFoegQFrIhFEOGpXTlI1PH3nKzuBTJw2txleer2A7GsArHWzgTiV+fUlp1GGlD3S8VOSzD+IaxQl6ywQAqg==", "signatures": [{"sig": "MEQCIEsc8iJOKJqt6evdUzxkwe9RzWqCQ7FBGyhRSFZGawXKAiBscZ/SdW5rnKufvM1Z0gbVHcYtpDt8WKoOZMv4hGbp9A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "2953a3747eea4ecb3fc17c4a37a3b346ce8f08c9", "gitHead": "3ece63bd29a7b495567bc846630566edecbd4f58", "scripts": {"test": "istanbul cover test.js && istanbul check-cover --statements 100 --branches 100"}, "_npmUser": {"name": "grncdr", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/grncdr/merge-stream", "type": "git"}, "_npmVersion": "1.4.13", "description": "Create a stream that emits events from multiple other streams", "directories": {}, "dependencies": {"through2": "^0.5.1"}, "devDependencies": {"from2": "0.0.0", "istanbul": "^0.2.12"}}, "0.1.4": {"name": "merge-stream", "version": "0.1.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "merge-stream@0.1.4", "maintainers": [{"name": "grncdr", "email": "<EMAIL>"}], "homepage": "https://github.com/grncdr/merge-stream", "bugs": {"url": "https://github.com/grncdr/merge-stream/issues"}, "dist": {"shasum": "cbaee98687502de73c8c66d784646133b0a98cac", "tarball": "https://registry.npmjs.org/merge-stream/-/merge-stream-0.1.4.tgz", "integrity": "sha512-d5lTkPdpc9v+QaZcgCMLMoxBrlS32oIPYjyklv15Bl+e9kG0537U66awkt3jZSPRzdtCSzwaM9x6ZKTW7B9BzQ==", "signatures": [{"sig": "MEUCIQCa5+HDfWK2HTewhrOEQSm4pxn9vynswU2Olf+mndb7vwIgUEjUO1Ozxf/5ObaXfZdorP0a8musNr4v9VS1kx24+qc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "cbaee98687502de73c8c66d784646133b0a98cac", "gitHead": "825412652c0b4251d0813ff76628e7527822efd0", "scripts": {"test": "istanbul cover test.js && istanbul check-cover --statements 100 --branches 100"}, "_npmUser": {"name": "grncdr", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/grncdr/merge-stream", "type": "git"}, "_npmVersion": "1.4.13", "description": "Create a stream that emits events from multiple other streams", "directories": {}, "dependencies": {"through2": "^0.5.1"}, "devDependencies": {"from2": "0.0.0", "istanbul": "^0.2.12"}}, "0.1.5": {"name": "merge-stream", "version": "0.1.5", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "merge-stream@0.1.5", "maintainers": [{"name": "grncdr", "email": "<EMAIL>"}], "homepage": "https://github.com/grncdr/merge-stream", "bugs": {"url": "https://github.com/grncdr/merge-stream/issues"}, "dist": {"shasum": "e6820f7addbaee003f48ca4a58c7c5a253d5e05c", "tarball": "https://registry.npmjs.org/merge-stream/-/merge-stream-0.1.5.tgz", "integrity": "sha512-K9O0uLBbnDuxn9n1JS2gnq7o67dWGRT8qupWbPdQ0nJGLa0wv3p4ZOvEq/lbSbqRrgDxVrNkimifN2mgb1ArEQ==", "signatures": [{"sig": "MEQCICQH069uwqOZvgwWk0EATuvkWKsm4FjcMj5L5txWGfN4AiAFGEMrKBI7rCqoVh0+6GlNMs2zxL1uCHQomMVDxSCtPQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "e6820f7addbaee003f48ca4a58c7c5a253d5e05c", "gitHead": "39e073fe11753da1e43b3682bb486e3c2b0dd177", "scripts": {"test": "istanbul cover test.js && istanbul check-cover --statements 100 --branches 100"}, "_npmUser": {"name": "grncdr", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/grncdr/merge-stream", "type": "git"}, "_npmVersion": "1.4.13", "description": "Create a stream that emits events from multiple other streams", "directories": {}, "dependencies": {"through2": "^0.5.1"}, "devDependencies": {"from2": "0.0.0", "istanbul": "^0.2.12"}}, "0.1.6": {"name": "merge-stream", "version": "0.1.6", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "merge-stream@0.1.6", "maintainers": [{"name": "grncdr", "email": "<EMAIL>"}], "homepage": "https://github.com/grncdr/merge-stream", "bugs": {"url": "https://github.com/grncdr/merge-stream/issues"}, "dist": {"shasum": "5c81b612d8816509c9ae101f429cfc31f3191df7", "tarball": "https://registry.npmjs.org/merge-stream/-/merge-stream-0.1.6.tgz", "integrity": "sha512-3T+p5oSiIsKR8pXwyJwpZybfO0EwyCGB8hmk17aK5xylvJg6z6+59QZbZDHnaKwT8m7zc00uUXzJwU+QmkPVwQ==", "signatures": [{"sig": "MEUCIQCdRtHMFBxcjN/dzjPSwP6lgIs+ZqRrX+CrahwzvJdBLAIgKSpdzxiOvAm61FkzwK900/WeFDDcZXp3g3mNJG3Yhko=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js"], "_shasum": "5c81b612d8816509c9ae101f429cfc31f3191df7", "gitHead": "d56167b2d1793eee164abd852992bab10da97be0", "scripts": {"test": "istanbul cover test.js && istanbul check-cover --statements 100 --branches 100"}, "_npmUser": {"name": "grncdr", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/grncdr/merge-stream", "type": "git"}, "_npmVersion": "1.4.28", "description": "Create a stream that emits events from multiple other streams", "directories": {}, "dependencies": {"through2": "^0.6.1"}, "devDependencies": {"from2": "^1.2.0", "istanbul": "^0.3.2"}}, "0.1.7": {"name": "merge-stream", "version": "0.1.7", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "merge-stream@0.1.7", "maintainers": [{"name": "grncdr", "email": "<EMAIL>"}], "homepage": "https://github.com/grncdr/merge-stream", "bugs": {"url": "https://github.com/grncdr/merge-stream/issues"}, "dist": {"shasum": "31debae13f9737ba17f7b4fbe33c16574c86fc79", "tarball": "https://registry.npmjs.org/merge-stream/-/merge-stream-0.1.7.tgz", "integrity": "sha512-ZZUWARNQ3f0H/wb6NCt2HQZifZqcSYWnyC7p+EKw6Pnumx0PA64xSPuSxpXWKnQfiAItlLIvyyzTE9R4z2BEEw==", "signatures": [{"sig": "MEQCICcWOpAZAuCi1pAOKLoWeTMUibm5aH6UojC6nHte06gpAiBcYhGU2gqrgMpLpWr72gUHEwb51Zfjlwj99NzuVqWDnQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js"], "_shasum": "31debae13f9737ba17f7b4fbe33c16574c86fc79", "gitHead": "be442ce5bd68015390e86f4545f4c070c4e9d942", "scripts": {"test": "istanbul cover test.js && istanbul check-cover --statements 100 --branches 100"}, "_npmUser": {"name": "grncdr", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/grncdr/merge-stream", "type": "git"}, "_npmVersion": "1.4.28", "description": "Create a stream that emits events from multiple other streams", "directories": {}, "dependencies": {"through2": "^0.6.1"}, "devDependencies": {"from2": "^1.2.0", "istanbul": "^0.3.2"}}, "0.1.8": {"name": "merge-stream", "version": "0.1.8", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "merge-stream@0.1.8", "maintainers": [{"name": "grncdr", "email": "<EMAIL>"}], "homepage": "https://github.com/grncdr/merge-stream", "bugs": {"url": "https://github.com/grncdr/merge-stream/issues"}, "dist": {"shasum": "48a07b3b4a121d74a3edbfdcdb4b08adbf0240b1", "tarball": "https://registry.npmjs.org/merge-stream/-/merge-stream-0.1.8.tgz", "integrity": "sha512-ivGsLZth/AkvevAzPlRLSie8Q3GdyH/5xUYgn+ItAJYslT0NsKd2cxx0bAjmqoY5swX0NoWJjvkDkfpaVZx9lw==", "signatures": [{"sig": "MEQCID+vRHgvkFk7gsrqysN+ZRPiUPqLv9hhdSpZIC7KSr3gAiAo3VlE691SAHJKMV64w95jQ/cTQvFn4a/k+aKg6aCNyg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js"], "_shasum": "48a07b3b4a121d74a3edbfdcdb4b08adbf0240b1", "gitHead": "b6892439437a82700d278902f357be95258c2a52", "scripts": {"test": "istanbul cover test.js && istanbul check-cover --statements 100 --branches 100"}, "_npmUser": {"name": "grncdr", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/grncdr/merge-stream", "type": "git"}, "_npmVersion": "1.4.28", "description": "Create a stream that emits events from multiple other streams", "directories": {}, "dependencies": {"through2": "^0.6.1"}, "devDependencies": {"from2": "^1.2.0", "istanbul": "^0.3.2"}}, "1.0.0": {"name": "merge-stream", "version": "1.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "merge-stream@1.0.0", "maintainers": [{"name": "grncdr", "email": "<EMAIL>"}, {"name": "shinnn", "email": "<EMAIL>"}], "homepage": "https://github.com/grncdr/merge-stream#readme", "bugs": {"url": "https://github.com/grncdr/merge-stream/issues"}, "dist": {"shasum": "9cfd156fef35421e2b5403ce11dc6eb1962b026e", "tarball": "https://registry.npmjs.org/merge-stream/-/merge-stream-1.0.0.tgz", "integrity": "sha512-f08gVCWi/O8wGsJbFJTsq1PGzoA2vWvnNsDF9eColi2ieV4Q3F0Sjfra8LmvxJIgr6nHfDL72wty2JALO+Fg/Q==", "signatures": [{"sig": "MEQCIDtxmPBExUHGhBFYjXWC0nla4cok/eneD86oXXv4OvzQAiALV50UzLEuO9u+adkxL8lYzLQ1l0aZUxjxm/Zxpwm43A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js"], "_shasum": "9cfd156fef35421e2b5403ce11dc6eb1962b026e", "gitHead": "e973cf43ef0edda5d4e3b08b07040d4039822734", "scripts": {"test": "istanbul cover test.js && istanbul check-cover --statements 100 --branches 100"}, "_npmUser": {"name": "shinnn", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/grncdr/merge-stream.git", "type": "git"}, "_npmVersion": "2.13.1", "description": "Create a stream that emits events from multiple other streams", "directories": {}, "_nodeVersion": "2.4.0", "dependencies": {"readable-stream": "^2.0.1"}, "devDependencies": {"from2": "^2.0.3", "istanbul": "^0.3.2"}}, "1.0.1": {"name": "merge-stream", "version": "1.0.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "merge-stream@1.0.1", "maintainers": [{"name": "grncdr", "email": "<EMAIL>"}, {"name": "shinnn", "email": "<EMAIL>"}], "homepage": "https://github.com/grncdr/merge-stream#readme", "bugs": {"url": "https://github.com/grncdr/merge-stream/issues"}, "dist": {"shasum": "4041202d508a342ba00174008df0c251b8c135e1", "tarball": "https://registry.npmjs.org/merge-stream/-/merge-stream-1.0.1.tgz", "integrity": "sha512-e6RM36aegd4f+r8BZCcYXlO2P3H6xbUM6ktL2Xmf45GAOit9bI4z6/3VU7JwllVO1L7u0UDSg/EhzQ5lmMLolA==", "signatures": [{"sig": "MEUCIQD9hhaZBc7Z7jQuasDzYYOEwKaCdvjzSlzCml3r5nSiEQIgEpwX3O+3quShQ/NXeNY2r2lcWTYeDEwNi+XaFFbeHoI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "4041202d508a342ba00174008df0c251b8c135e1", "gitHead": "1b33da64b219dcc8bf33645953c32a6fd9e3b36d", "scripts": {"test": "istanbul cover test.js && istanbul check-cover --statements 100 --branches 100"}, "_npmUser": {"name": "shinnn", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/grncdr/merge-stream.git", "type": "git"}, "_npmVersion": "4.0.3", "description": "Create a stream that emits events from multiple other streams", "directories": {}, "_nodeVersion": "7.1.0", "dependencies": {"readable-stream": "^2.0.1"}, "devDependencies": {"from2": "^2.0.3", "istanbul": "^0.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/merge-stream-1.0.1.tgz_1480049995559_0.005447877338156104", "host": "packages-18-east.internal.npmjs.com"}}, "2.0.0": {"name": "merge-stream", "version": "2.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "merge-stream@2.0.0", "maintainers": [{"name": "grncdr", "email": "<EMAIL>"}, {"name": "shinnn", "email": "<EMAIL>"}, {"name": "ste<PERSON><PERSON><PERSON>", "email": "maoch<PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/grncdr/merge-stream#readme", "bugs": {"url": "https://github.com/grncdr/merge-stream/issues"}, "dist": {"shasum": "52823629a14dd00c9770fb6ad47dc6310f2c1f60", "tarball": "https://registry.npmjs.org/merge-stream/-/merge-stream-2.0.0.tgz", "fileCount": 4, "integrity": "sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==", "signatures": [{"sig": "MEUCIHkSj6eJZ/Q+s1rKnvS56xWMntshRErtO1r20DqivKWoAiEAsEeWHE5fY8tciskCzhMSM3Tg/m/SFmmtPBalQbJ58bY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4306, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc5qHuCRA9TVsSAnZWagAA1nEP+wQO4CG5vJw/N/G6IqhF\nLo/u8UlHZu1XDB+swsNMk7cb1EN/IlFFyxYu8Uc4dR64pXf2rJ0cencSmVrd\njOCUouSyIX7FovZnkNP6jSjtO8J8n/jwJ1PETg5IgKXh1rK5jj5f1Ho4oUEP\nwtdhwfR6f7uIFR+axsDCjrxkxgg8uK7ecEGsAs3yqC+oUw+fnsxVQXwjMICU\nr/wgB0Cu2LmAn0vioKWCq1hcFdPYeJGeKMuHPftwLxCht/2y/CjFWHUR/11K\n3kO6HWy8UFDxYsRL9qATN+3zCL1f+Ez20Do3VnQNlCf2m05zYcGRz+tRAF+n\noAS8c47qrbnEOPS9Ht7wkjaurkMGys5B5hZAqboCmlvDp2iweex1EzBYqLR2\nDvWbsAG/3TkA1CYA/I6gTsqC55JEGh+KLhJ4bzh6wEnB2evFNmx6sS4i7u4y\nGY81BV6TIrh6wa/QB6eEDjvSfFJMdnxFttEOZZj5FvuxWoE23L+EZxftGFE7\n6IGLUsjaVj7nH9kKqilhKPc6+eKukJTuL9wJ8xzQ7HhoN4zSzF8ZssL/k/13\nh81fYGltdVUHigNb+QKMWpZmzhAZCBmz9+VSguOwEqI9kKnXP0cTtViDZL2E\nhyrxVaBYapoJOoAiuv/FAVOWq+kEjG5GsiUU10zHK0Iqr4QEN2ZKmDZ3hYr0\nHafY\r\n=VgeY\r\n-----END PGP SIGNATURE-----\r\n"}, "gitHead": "cf3b957fe7937f09c0c95f714b6076436f8516b3", "scripts": {"test": "istanbul cover test.js && istanbul check-cover --statements 100 --branches 100"}, "_npmUser": {"name": "ste<PERSON><PERSON><PERSON>", "email": "maoch<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/grncdr/merge-stream.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Create a stream that emits events from multiple other streams", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"from2": "^2.0.3", "istanbul": "^0.4.5"}, "_npmOperationalInternal": {"tmp": "tmp/merge-stream_2.0.0_1558618605777_0.657983797951093", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2013-06-10T03:31:48.851Z", "modified": "2024-10-25T14:48:19.149Z", "0.0.0": "2013-06-10T03:31:50.008Z", "0.1.0": "2013-06-11T04:40:42.038Z", "0.0.1": "2014-05-19T08:56:09.235Z", "0.1.1": "2014-05-20T06:54:55.404Z", "0.1.2": "2014-06-26T21:22:58.499Z", "0.1.3": "2014-07-06T17:52:09.427Z", "0.1.4": "2014-07-06T17:53:03.173Z", "0.1.5": "2014-07-06T17:53:30.302Z", "0.1.6": "2014-09-24T23:58:11.643Z", "0.1.7": "2015-01-10T16:40:44.587Z", "0.1.8": "2015-06-30T21:04:54.555Z", "1.0.0": "2015-08-10T08:21:53.649Z", "1.0.1": "2016-11-25T04:59:57.218Z", "2.0.0": "2019-05-23T13:36:45.932Z"}, "bugs": {"url": "https://github.com/grncdr/merge-stream/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/grncdr/merge-stream#readme", "repository": {"url": "git+https://github.com/grncdr/merge-stream.git", "type": "git"}, "description": "Create a stream that emits events from multiple other streams", "maintainers": [{"name": "grncdr", "email": "<EMAIL>"}, {"name": "shinnn", "email": "<EMAIL>"}, {"name": "ste<PERSON><PERSON><PERSON>", "email": "maoch<PERSON><PERSON>@gmail.com"}], "readme": "# merge-stream\n\nMerge (interleave) a bunch of streams.\n\n[![build status](https://secure.travis-ci.org/grncdr/merge-stream.svg?branch=master)](http://travis-ci.org/grncdr/merge-stream)\n\n## Synopsis\n\n```javascript\nvar stream1 = new Stream();\nvar stream2 = new Stream();\n\nvar merged = mergeStream(stream1, stream2);\n\nvar stream3 = new Stream();\nmerged.add(stream3);\nmerged.isEmpty();\n//=> false\n```\n\n## Description\n\nThis is adapted from [event-stream](https://github.com/dominictarr/event-stream) separated into a new module, using Streams3.\n\n## API\n\n### `mergeStream`\n\nType: `function`\n\nMerges an arbitrary number of streams. Returns a merged stream.\n\n#### `merged.add`\n\nA method to dynamically add more sources to the stream. The argument supplied to `add` can be either a source or an array of sources.\n\n#### `merged.isEmpty`\n\nA method that tells you if the merged stream is empty.\n\nWhen a stream is \"empty\" (aka. no sources were added), it could not be returned to a gulp task.\n\nSo, we could do something like this:\n\n```js\nstream = require('merge-stream')();\n// Something like a loop to add some streams to the merge stream\n// stream.add(streamA);\n// stream.add(streamB);\nreturn stream.isEmpty() ? null : stream;\n```\n\n## Gulp example\n\nAn example use case for **merge-stream** is to combine parts of a task in a project's **gulpfile.js** like this:\n\n```js\nconst gulp =          require('gulp');\nconst htmlValidator = require('gulp-w3c-html-validator');\nconst jsHint =        require('gulp-jshint');\nconst mergeStream =   require('merge-stream');\n\nfunction lint() {\n  return mergeStream(\n    gulp.src('src/*.html')\n      .pipe(htmlValidator())\n      .pipe(htmlValidator.reporter()),\n    gulp.src('src/*.js')\n      .pipe(jsHint())\n      .pipe(jsHint.reporter())\n  );\n}\ngulp.task('lint', lint);\n```\n\n## License\n\nMIT\n", "readmeFilename": "README.md", "users": {"lmhs": true, "bsara": true, "ccd3v": true, "denji": true, "etsit": true, "h4des": true, "kirov": true, "muroc": true, "qafir": true, "ryanj": true, "monjer": true, "omgben": true, "pandao": true, "qoozoo": true, "rbelow": true, "wvlvik": true, "akodevs": true, "drewigg": true, "hallaji": true, "ijunlin": true, "itonyyo": true, "mattweb": true, "maxwang": true, "rokt33r": true, "ungurys": true, "dgarlitt": true, "ham2yagi": true, "magicboy": true, "maxogden": true, "npmrud5g": true, "otravers": true, "abuelwafa": true, "bmpvieira": true, "fadihania": true, "larrychen": true, "yodairish": true, "abdihaikal": true, "mariusz-eu": true, "nerdybeast": true, "seangenabe": true, "xenohunter": true, "davidnyhuis": true, "ericwbailey": true, "evanj0hnson": true, "illuminator": true, "wangnan0610": true, "ghostcode521": true, "alexvcasillas": true, "donggw2030521": true, "miadzadfallah": true, "tobiasalthoff": true, "yongfei_iflix": true, "classicoldsong": true, "laravelfanatic": true, "outboundexplorer": true}}