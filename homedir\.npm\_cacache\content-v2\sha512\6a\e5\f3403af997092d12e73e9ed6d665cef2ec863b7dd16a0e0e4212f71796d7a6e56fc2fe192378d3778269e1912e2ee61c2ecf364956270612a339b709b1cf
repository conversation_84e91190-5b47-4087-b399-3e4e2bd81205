{"name": "balanced-match", "dist-tags": {"latest": "3.0.1"}, "versions": {"0.0.0": {"name": "balanced-match", "version": "0.0.0", "devDependencies": {"tape": "~1.1.1"}, "dist": {"shasum": "86efc32ae583496c1c1fbb51cd648de0363ebb03", "tarball": "https://registry.npmjs.org/balanced-match/-/balanced-match-0.0.0.tgz", "integrity": "sha512-daYFGv8RHJKIcx7l5jAzeS86+pMEgTAcbF7Q89qnrgRVI1GEDkuGABNGzkcWYrUwUZJ4+uUf8hF4n3SZMIPVOQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHVHvgnAs1bDFoDI6+/pz7mkkZXVfz8thHg0hQ7Y9K/8AiBABPKzjsOSHdHyBJ6tGAMBNjzJTn1XASzfvfo3CnZfAQ=="}]}}, "0.0.1": {"name": "balanced-match", "version": "0.0.1", "devDependencies": {"tape": "~1.1.1"}, "dist": {"shasum": "2c408589c3288fc8a152c535ed853f77763899ae", "tarball": "https://registry.npmjs.org/balanced-match/-/balanced-match-0.0.1.tgz", "integrity": "sha512-obnFpTIt83MxrUxnHfs4npfChWAw0YcBQui+hI1awrVPzIqpKKkQ7KTunVRKAfauTptPQXZohaPs1hf38HJ05A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCO2l/CrUtV26QU2sOMNhCk02ZePiXNQy7szGHJTneWmgIgNhB9Yc4EEGiKzMSbdGAEskqxf6DeIZLLX63/pzCdMrE="}]}}, "0.1.0": {"name": "balanced-match", "version": "0.1.0", "devDependencies": {"tape": "~1.1.1"}, "dist": {"shasum": "b504bd05869b39259dd0c5efc35d843176dccc4a", "tarball": "https://registry.npmjs.org/balanced-match/-/balanced-match-0.1.0.tgz", "integrity": "sha512-4xb6XqAEo3Z+5pEDJz33R8BZXI8FRJU+cDNLdKgDpmnz+pKKRVYLpdv+VvUAC7yUhBMj4izmyt19eCGv1QGV7A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIADDYF41QH1NuQ+/2uuuSzZelNXhFB1Tqi2YjQq7OuYaAiEA2BMkJ/3Tbk/knnCvb/33vauA8Rw/9xhG5PA90ipzB/U="}]}}, "0.2.0": {"name": "balanced-match", "version": "0.2.0", "devDependencies": {"tape": "~1.1.1"}, "dist": {"shasum": "38f6730c03aab6d5edbb52bd934885e756d71674", "tarball": "https://registry.npmjs.org/balanced-match/-/balanced-match-0.2.0.tgz", "integrity": "sha512-kuRgl0wyQa2pmUzVVyVQp0E04p//9u7J6Hi0Hd7fpF2Le1waUYUPmOcp6ITXNBYtBfzu9zw+aTG5eLLfYWHd1A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDorE0C4ozrLlU3/RXjoBGDnTQ1vGfaj6q66FYyGhfNsAiAWgloiwUeWMBJxB1SfnfDam7lkrmul37OR/Jb9PSXVQQ=="}]}}, "0.2.1": {"name": "balanced-match", "version": "0.2.1", "devDependencies": {"tape": "~1.1.1"}, "dist": {"shasum": "7bc658b4bed61eee424ad74f75f5c3e2c4df3cc7", "tarball": "https://registry.npmjs.org/balanced-match/-/balanced-match-0.2.1.tgz", "integrity": "sha512-euSOvfze1jPOf85KQOmZ2UcWDJ/dUJukTJdj4o9ZZLyjl7IjdIyE4fAQRSuGrxAjB9nvvvrl4N3bPtRq+W+SyQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBJ3wjKXLgAZjSqy9mOktUcOqNoQh8JSEPhMyfNsbo5hAiEA8V3Y/Vugo26oLm+5dp6W9C4PB4wKNnoPMiESz7Lj3yg="}]}}, "0.3.0": {"name": "balanced-match", "version": "0.3.0", "devDependencies": {"tape": "~4.2.2"}, "dist": {"shasum": "a91cdd1ebef1a86659e70ff4def01625fc2d6756", "tarball": "https://registry.npmjs.org/balanced-match/-/balanced-match-0.3.0.tgz", "integrity": "sha512-bgB9RrUMd3G7drkg5+Gv+dMZTUSFbfrrp61qsQGlTdCdIPqdzF9UG2G5Ndlg6zR3ArNeGGXMIYSYFZRRtZaT9Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCFVZKtSpYwgtaTT2Kqf1h7zkwzrSJagLcLLzTDM+RwSQIgPQSY7OnNwUniyQQvJ0f8cHvHD6YlDcpo6cvfszyv234="}]}}, "0.4.0": {"name": "balanced-match", "version": "0.4.0", "devDependencies": {"tape": "~4.5.0"}, "dist": {"shasum": "84818b70e91d9ac8b4d77df20e9239e80c025089", "tarball": "https://registry.npmjs.org/balanced-match/-/balanced-match-0.4.0.tgz", "integrity": "sha512-0fxU/CUKHz4ojATahMymHO3MC7xccEcNISC+fNroLYitQjVUP3rEAwV8lsviJMjTlrLza4cH/TCH9kBHvSDf1Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIARCRZyc0v8nLUsZoBLVE8r0ayj+33hDa41xwLM8c7m/AiBnlZGwQD0ZJGpLfKojVsbLVxsG7CKYEgLd/AL1D791KQ=="}]}}, "0.4.1": {"name": "balanced-match", "version": "0.4.1", "devDependencies": {"tape": "~4.5.0"}, "dist": {"shasum": "19053e2e0748eadb379da6c09d455cf5e1039335", "tarball": "https://registry.npmjs.org/balanced-match/-/balanced-match-0.4.1.tgz", "integrity": "sha512-vgW4YcTHFsmsL5q8x0ovPQfwzEdFCoQXv6HBse+E46uZNwA+lE5+V1G9ap3IaUz0oM9JPFiJ8tnDZjqdReFSqA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAytkJMWi5v5v1dWj3sCpqNsfuHH8wnB0+ogc3MURhfqAiEAn3aM52dUjjGxYPD6DhQHUkMuJlqw/RQRUZbTpgvycUk="}]}}, "0.4.2": {"name": "balanced-match", "version": "0.4.2", "devDependencies": {"tape": "^4.6.0"}, "dist": {"shasum": "cb3f3e3c732dc0f01ee70b403f302e61d7709838", "tarball": "https://registry.npmjs.org/balanced-match/-/balanced-match-0.4.2.tgz", "integrity": "sha512-STw03mQKnGUYtoNjmowo4F2cRmIIxYEGiMsjjwla/u5P1lxadj/05WkNaFjNiKTgJkj8KiXbgAiRTmcQRwQNtg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDwR6gkoRTPsOQQNI/+S71bhdZoeEMHWYyKDMsSzVwixAIhAIllfa3v0fyWYS51UxB+4wbQk2LCtxJhWSjseBejXl9z"}]}}, "1.0.0": {"name": "balanced-match", "version": "1.0.0", "devDependencies": {"matcha": "^0.7.0", "tape": "^4.6.0"}, "dist": {"shasum": "89b4d199ab2bee49de164ea02b89ce462d71b767", "tarball": "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.0.tgz", "integrity": "sha512-9Y0g0Q8rmSt+H33DfKv7FOc3v+iRI+o1lbzt8jGcIosYW37IIW/2XVYq5NPdmaD5NQ59Nk26Kl/vZbwW9Fr8vg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDN5U38zzaYjzNgiGzGDWu9nnWtcbrB6JezTyfWwriLJAiBjOrytimT7VRffO2Y/7LWXIOmsJFjo5toVuTAXyucXZg=="}]}}, "1.0.1": {"name": "balanced-match", "version": "1.0.1", "devDependencies": {"@c4312/matcha": "^1.3.1", "np": "^7.4.0", "prettier-standard": "^16.4.1", "standard": "^16.0.3", "tape": "^4.6.0"}, "dist": {"integrity": "sha512-qyTw2VPYRg31SlVU5WDdvCSyMTJ3YSP4Kz2CidWZFPFawCiHJdCyKyZeXIGMJ5ebMQYXEI56kDR8tcnDkbZstg==", "shasum": "4f46cf3183a02a6a25875cf9a4240c15291cf464", "tarball": "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.1.tgz", "fileCount": 5, "unpackedSize": 7083, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgbBCwCRA9TVsSAnZWagAAyIwP/3NssbM+7PI+JjP3izQc\nc6+ePWLbBz5smmilqFyHnv8z2Ouv5PBBO6EVyyRX80DPy7KPPFUFXNOS00Vw\n8yHZ+EyaWzamt6yVDRNxx2DGf8jDzB1Axh8NwkIQKfnwsBxt/wVJFojLo6Rn\nuGOXhy2n5nbZ1JavWL8aquTx/6maPoyEu3omopwrDEhxcAmz50czBRPb8sPH\n+fQYl9SgkJdMUDAUNr65pj77v+gR4glViT838GWsoa32f/Wt/e8Na034+IeU\nzSwnEmA0cvGj2/ubkiAifPIshIXDXcEm0aSRn5lrCzmInGKtD124F5vinY3d\nXZ7CD1YGv2zQ703HZLVhAugd2/4l1Ac3Uf8bGSOFc4ipzwYXUOH8OUlIWKDU\nQ/ktMaueuBENMU4cs/ys3th5qZQFmv0vT8L8VAC1ybJ+tDF80bvvNnIlhwgb\nj2elsnB4uj5DjvDq/hjRHLODAXSJWnikm9gDtRHMcIOy6tJI39UIRfG6br0K\n6MqtN7TE4UkkkPaUEEKPv53fhABsCkkhWVKd2oW04i+hn53Iu7FZm2Pgx5yu\nC82gcej2byggT97RO9PtiNbCtkvJXm52I5dg6lIJ2/Xsqb16O///kgmD7n8/\n8u/9HNauhKbhg0wGuZJgcy0hBbOYFSJOfM+S26UHADWiPcEkJ4fc7nlqG9vv\nmMTG\r\n=ftTQ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDlcpMP2h5zkcX0B8nr6vV/qhflEMPCULRG0/JGC4+B3AiAQ6pXxP1MfFPEU2M5/jichFV38qfuD5MdVDPrp4Wu9eA=="}]}, "deprecated": "this package has been deprecated"}, "1.0.2": {"name": "balanced-match", "version": "1.0.2", "devDependencies": {"matcha": "^0.7.0", "tape": "^4.6.0"}, "dist": {"integrity": "sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==", "shasum": "e83e3a7e3f300b34cb9d87f615fa0cbf357690ee", "tarball": "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz", "fileCount": 5, "unpackedSize": 6939, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgbFk9CRA9TVsSAnZWagAAZCkP/2oCPlLyH1O+2fxJepxC\nP64dIPH4FmdtcuRV6m9JSSnNayjLyl7KZSkzngJveJAVMwBH2oSO40HVruAc\njNGdawU0sm41Tvkxm0K9AhiT5pfqBHv6KBj/sR5+2iF56zAM7pxrc8eTsgj9\nHBAYq5ZoePKf+Kki77ilWwK1Z7VXekk3KNgPd4jsbZ58JGL2dLVmqJcOPAfx\nTRECI9NV5oyHl+EsOGnMnAB8Z7GvNH+/sVo5lWZkldStJDjlj3mZq9fxMo5I\nw/2pmVPI8dvYYA6r3mp55YYDyvWA49CoRgTHXqEy4tpHmmdTAdB2Je+3j/n0\nvbJm74Ab6CnZnwa9Oaowz+VcKkcczXICTxPj0D+ddvVksD+6VpnAz79Jyia5\nqApDNXnYv+8bdnMwhnA2tQ0vz10HANuZ1xfpXE9Yy4Py/1LsTvExovYsie1G\n9RQ1GkIpGwwyOuzbDqHtrRjduAy35VNtIw2nQTCRLz87w/7DV+RbTvaT1Fp7\nb4WQN9z6BoX0Bl/Qi8PXTDN5J8M83MsRThoYm20M0nAVeGbxrfHTMJoXvxF9\ntlHuV3E7W7x3lvG0za7wLn9p76uOzxDX8Osr5POJ/GpEVciz0PWcbHQHFHUm\nxB+x3O0C9eAdKW/9/7/YA9zMqdqcMuwg6f26neIYIk10oZQyRriBoV6OZtIy\ntw1n\r\n=eH8s\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHRQpAKwqTgs0SDP5KcV7MzsuTPMEkHeNqJFBOy5hYMwAiB/QgzhE/4zo/h6mn5Sl6u4YP0UZKqPYCZe5GhyLntdKA=="}]}}, "2.0.0": {"name": "balanced-match", "version": "2.0.0", "devDependencies": {"@c4312/matcha": "^1.3.1", "np": "^7.4.0", "prettier-standard": "^16.4.1", "standard": "^16.0.3", "tape": "^4.6.0"}, "dist": {"integrity": "sha512-1ugUSr8BHXRnK23KfuYS+gVMC3LB8QGH9W1iGtDPsNWoQbgtXSExkBu2aDR4epiGWZOjZsj6lDl/N/AqqTC3UA==", "shasum": "dc70f920d78db8b858535795867bf48f820633d9", "tarball": "https://registry.npmjs.org/balanced-match/-/balanced-match-2.0.0.tgz", "fileCount": 5, "unpackedSize": 7083, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgbFnBCRA9TVsSAnZWagAAnBUQAIwSae9EWp8mawlco66Y\nsYcwEHdv5Cc7shxnCSIeYYGgowelCQgirX5QrJHKmPEj10UfrJJvCnHu4uMC\nvyztZIDLxtg3xWMaTObZfVRCO23S90Po81YDJBvOtrRciRGqQmZ+HWmuRYDu\nI7rtvXMK/yc31dnkOjTPBd6FjufQRfH+OyS1cPJP5/ZyXxZsiNi28jIDe/1R\nKETSdx279AtQo+vUL6uK+OnKF9Rxo8GXeabM+4dRezqWtYW1B2RugEKuhSk5\nlwXOrjJEioG+TaIozgXY8X/0hiyRW6mCisMtFE3aYxhgp/WxPwlyNV6k+dtz\nqsnrwPLlZyVg0IX16MbHXJBbr0yvynSbN2t1eUZ1kX36wquzuIMDk6H/1XNY\nhhAydNkpFGICPedeLkFVvVFjpx+zeVryhMj3sq+P5FYdIDcHkhxFDX8s3cfp\ntIrtY7Y59hMsdDnIUwp4qqOvxG7DuuEFprWG38BIVCa0hE3yA+vQ5+ACUmBo\no6DM/RUgXwuqFghoYRX00fxKSedVIWfX8f6nPyG0WhN5svfdPlC/0qayvE0r\nGllsfW6la8n1yVN8jey9we2x0OdLutG4rYB5gEzl91DoLJjP9TopCdNnZhhC\nuM3d6jLW/BVRgPTU7z6YBCXiESw0lntphDMotVZBPeXz2CTqGTNbE4vKAJq4\nROwK\r\n=8h9J\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCwPXtClY2xRtpUhfN8Otf+E02dH+DO55UcSuJ0vi+LrAIhALiaQS2V+1k2zJKf/lBKrxIRH8shIVFuYbpjfo7ABZ38"}]}}, "3.0.0": {"name": "balanced-match", "version": "3.0.0", "devDependencies": {"@c4312/matcha": "^1.3.1", "np": "^8.0.4", "standard": "^17.1.0", "test": "^3.3.0"}, "dist": {"integrity": "sha512-roy6f9Ri49dpBe1EUBikUsqhJfEVlW+oLV7JFwGm17PdkZ81xVreEYNEIsytl9NQ6fvvvJRXHyVe60O5ve6i1w==", "shasum": "c47006ef8f61f4c7ffbecbd69b2fe9c56fb8773c", "tarball": "https://registry.npmjs.org/balanced-match/-/balanced-match-3.0.0.tgz", "fileCount": 4, "unpackedSize": 7127, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHfh6+e4G478Rijxgz6qRQLhcQiHzmBYVuJ1mzlE6FC7AiB5VfbD/aHfrYbKC3EUC85l/DO4yGx4JK96abS4fwZ5bA=="}]}, "engines": {"node": ">= 16"}}, "3.0.1": {"name": "balanced-match", "version": "3.0.1", "devDependencies": {"@c4312/matcha": "^1.3.1", "np": "^8.0.4", "standard": "^17.1.0", "test": "^3.3.0"}, "dist": {"integrity": "sha512-vjtV3hiLqYDNRoiAv0zC4QaGAMPomEoq83PRmYIofPswwZurCeWR5LByXm7SyoL0Zh5+2z0+HC7jG8gSZJUh0w==", "shasum": "e854b098724b15076384266497392a271f4a26a0", "tarball": "https://registry.npmjs.org/balanced-match/-/balanced-match-3.0.1.tgz", "fileCount": 5, "unpackedSize": 12334, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEHsKepAWqy0XBNt9lRc2IKfkNV2LfAzNNev+dVSGip1AiEAvZJxo1yLwJNtvZRe+9qcUinlJ6fC6btDmG+KvqyP4+g="}]}, "engines": {"node": ">= 16"}}}, "modified": "2023-10-07T13:34:03.844Z"}