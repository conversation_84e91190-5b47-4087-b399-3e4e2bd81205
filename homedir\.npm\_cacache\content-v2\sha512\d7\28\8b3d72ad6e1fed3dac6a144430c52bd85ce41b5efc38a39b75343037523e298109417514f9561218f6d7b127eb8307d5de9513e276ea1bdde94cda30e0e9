{"_id": "har-validator", "_rev": "76-d62a626346ce15241a8a5355e59999e7", "name": "har-validator", "description": "Extremely fast HTTP Archive (HAR) validator using JSON Schema", "dist-tags": {"latest": "5.1.5"}, "versions": {"1.0.0": {"version": "1.0.0", "name": "har-validator", "description": "HTTP Archive (HAR) validator using JSON Schema", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "license": "MIT", "main": "./src/index.js", "bin": {"har-validator": "./bin/har-validator"}, "engines": {"node": "0.10.x", "npm": "1.4.x"}, "repository": {"type": "git", "url": "https://github.com/ahmadnassri/har-validator.git"}, "bugs": {"url": "https://github.com/ahmadnassri/har-validator/issues"}, "keywords": ["HAR", "HTTP", "Archive"], "scripts": {"test": "mocha -R spec"}, "devDependencies": {"mocha": "^2.1.0", "should": "^4.6.1"}, "dependencies": {"chalk": "^0.5.1", "commander": "^2.6.0", "is-my-json-valid": "^2.4.0"}, "gitHead": "5fbbe36e44eb9596b824bb0d329abe98f7c7a1f6", "homepage": "https://github.com/ahmadnassri/har-validator", "_id": "har-validator@1.0.0", "_shasum": "120380a05cc6a46f5a7b45ea96dab7348ad62649", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "120380a05cc6a46f5a7b45ea96dab7348ad62649", "tarball": "https://registry.npmjs.org/har-validator/-/har-validator-1.0.0.tgz", "integrity": "sha512-v/HeU1Ew/cp5qJ9LRWKiTCwEEoJ5hOTIL45l4gNWXoCUsMOvHrVYAiauQo6ACbNXMMJ50fw6OaPC1UoLsVNWyw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDlDAEI34JoRp5rdS0vbDLuTrPZ9SbjwSakZYJApnX9WgIgY0XHkTpxx6bUeBeMchk9FMH0CAI9eksmTcXGyGG57WQ="}]}, "directories": {}, "deprecated": "this library is no longer supported"}, "1.0.1": {"version": "1.0.1", "name": "har-validator", "description": "HTTP Archive (HAR) validator using JSON Schema", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "license": "MIT", "main": "./src/index.js", "bin": {"har-validator": "./bin/har-validator"}, "engines": {"node": "0.10.x", "npm": "1.4.x"}, "repository": {"type": "git", "url": "https://github.com/ahmadnassri/har-validator.git"}, "bugs": {"url": "https://github.com/ahmadnassri/har-validator/issues"}, "keywords": ["HAR", "HTTP", "Archive"], "scripts": {"test": "mocha -R spec"}, "devDependencies": {"mocha": "^2.1.0", "should": "^5.0.1"}, "dependencies": {"chalk": "^1.0.0", "commander": "^2.6.0", "is-my-json-valid": "^2.9.4"}, "gitHead": "c0848dbc85a1eb9da643e4fa3d4789c93d0917bb", "homepage": "https://github.com/ahmadnassri/har-validator", "_id": "har-validator@1.0.1", "_shasum": "b74e5aa074998ea95f196cccd608fd98f465b298", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "b74e5aa074998ea95f196cccd608fd98f465b298", "tarball": "https://registry.npmjs.org/har-validator/-/har-validator-1.0.1.tgz", "integrity": "sha512-Nkb2uso9UcRLXyYFy+SQERxi9APgOIKBRfE+2nPAFPmlBVCryGDdGVyap+r4Hlk0CnGCSxPajYFtEBbcKzHwtQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGIF77dQlGKGWxm1v93ov5Ifj35F6RYFeSKnsT+yVqqTAiBKavu8Y0rGW54Pn6Fv4jd+Xpu+NeIVQ+pxnTN3FM7KwQ=="}]}, "directories": {}, "deprecated": "this library is no longer supported"}, "1.0.2": {"version": "1.0.2", "name": "har-validator", "description": "Extremely fast HTTP Archive (HAR) validator using JSON Schema", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "license": "MIT", "main": "./src/index.js", "bin": {"har-validator": "./bin/har-validator"}, "engines": {"node": "0.10.x", "npm": "1.4.x"}, "repository": {"type": "git", "url": "https://github.com/ahmadnassri/har-validator.git"}, "bugs": {"url": "https://github.com/ahmadnassri/har-validator/issues"}, "keywords": ["HAR", "HTTP", "Archive"], "scripts": {"test": "mocha -R spec"}, "devDependencies": {"mocha": "^2.1.0", "should": "^5.0.1"}, "dependencies": {"chalk": "^1.0.0", "commander": "^2.6.0", "is-my-json-valid": "^2.9.4"}, "gitHead": "3b3245e983d516f0faebc9420b94dbc2331ea95c", "homepage": "https://github.com/ahmadnassri/har-validator", "_id": "har-validator@1.0.2", "_shasum": "a00f08e53b1031489208cd95d4bb5d6e02721fa7", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "a00f08e53b1031489208cd95d4bb5d6e02721fa7", "tarball": "https://registry.npmjs.org/har-validator/-/har-validator-1.0.2.tgz", "integrity": "sha512-Awbf6OCsQuOp/BMOEKCfVOdmpUoAWxtoPrqyz9HA0j7EzkAXTZdxRgP0zp8AUW4jWvCpe+NGDHgnBc6tLPS5/w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF80tfDrgMtan9Lk9INGwtS3I+8jYkpICB8GAXrzLICUAiEArwmgTGUMUyKCmPCVv9Z6Yyr24dg2QvIPJ2H7cXFEtLQ="}]}, "directories": {}, "deprecated": "this library is no longer supported"}, "1.1.0": {"version": "1.1.0", "name": "har-validator", "description": "Extremely fast HTTP Archive (HAR) validator using JSON Schema", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "license": "MIT", "main": "./src/index.js", "bin": {"har-validator": "./bin/har-validator"}, "engines": {"node": "0.10.x", "npm": "1.4.x"}, "repository": {"type": "git", "url": "https://github.com/ahmadnassri/har-validator.git"}, "bugs": {"url": "https://github.com/ahmadnassri/har-validator/issues"}, "keywords": ["HAR", "HTTP", "Archive"], "scripts": {"test": "mocha -R spec"}, "devDependencies": {"mocha": "^2.1.0", "should": "^5.0.1"}, "dependencies": {"chalk": "^1.0.0", "commander": "^2.6.0", "is-my-json-valid": "^2.9.4"}, "gitHead": "1673e213c2ae6a9c3a92b40240856d76edceb5f4", "homepage": "https://github.com/ahmadnassri/har-validator", "_id": "har-validator@1.1.0", "_shasum": "6fdf52a744d9cb50075da281f0853cf7e3ad6d12", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "6fdf52a744d9cb50075da281f0853cf7e3ad6d12", "tarball": "https://registry.npmjs.org/har-validator/-/har-validator-1.1.0.tgz", "integrity": "sha512-DSOYoFfNB+qqeunTOApRqQRAGURWEe4h+FxWfUQTq3s5WDjJvCJfn0s+JZbmC9cnqPsXOpbInEDQvX8KuZahHQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDDBFDeuKpMRX0RqyjRSO5RlXKFPVLrph98rpF4x7g2IAIgW/qL/ToXHlxZipstY7MiobqpRiXKnPvtA9EVQWGQzuc="}]}, "directories": {}, "deprecated": "this library is no longer supported"}, "1.1.1": {"version": "1.1.1", "name": "har-validator", "description": "Extremely fast HTTP Archive (HAR) validator using JSON Schema", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "license": "MIT", "main": "./src/index.js", "bin": {"har-validator": "./bin/har-validator"}, "engines": {"node": "0.10.x", "npm": "1.4.x"}, "repository": {"type": "git", "url": "https://github.com/ahmadnassri/har-validator.git"}, "bugs": {"url": "https://github.com/ahmadnassri/har-validator/issues"}, "keywords": ["HAR", "HTTP", "Archive"], "scripts": {"test": "mocha -R spec"}, "devDependencies": {"mocha": "^2.1.0", "should": "^5.0.1"}, "dependencies": {"chalk": "^1.0.0", "commander": "^2.6.0", "is-my-json-valid": "^2.9.4"}, "gitHead": "ee84ebca4b77c2383b980eb6fb745b97ce5645eb", "homepage": "https://github.com/ahmadnassri/har-validator", "_id": "har-validator@1.1.1", "_shasum": "dfb2af18db77cafd29a260862f151271b04fd0a9", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "dfb2af18db77cafd29a260862f151271b04fd0a9", "tarball": "https://registry.npmjs.org/har-validator/-/har-validator-1.1.1.tgz", "integrity": "sha512-mlMKfafq1FT7LwLGG7kvu5jxsR7/zpoG2kAOIfjE1t8LLajAb5gK9qdXzVLc4yWV2vaS4AAcXCeJd13eaIhZ/A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBT/7Oip59ktYoI9t58h2F2yKcUDhommCRu4sKKotkkKAiBJ1bAQqXjw1dCh6r7RwUw3q86aVHtX80t1gW/8nSO8IA=="}]}, "directories": {}, "deprecated": "this library is no longer supported"}, "1.1.2": {"version": "1.1.2", "name": "har-validator", "description": "Extremely fast HTTP Archive (HAR) validator using JSON Schema", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "license": "MIT", "main": "./src/index.js", "bin": {"har-validator": "./bin/har-validator"}, "engines": {"node": "0.10.x", "npm": "1.4.x"}, "repository": {"type": "git", "url": "https://github.com/ahmadnassri/har-validator.git"}, "bugs": {"url": "https://github.com/ahmadnassri/har-validator/issues"}, "keywords": ["HAR", "HTTP", "Archive"], "scripts": {"test": "mocha -R spec"}, "devDependencies": {"mocha": "^2.1.0", "should": "^5.0.1"}, "dependencies": {"chalk": "^1.0.0", "commander": "^2.6.0", "is-my-json-valid": "^2.9.4"}, "gitHead": "e3fb33075a2b04a88396eb737dd687ba8d2362df", "homepage": "https://github.com/ahmadnassri/har-validator", "_id": "har-validator@1.1.2", "_shasum": "13edd735b4566eb0d00a8468d665090e54ae2153", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "13edd735b4566eb0d00a8468d665090e54ae2153", "tarball": "https://registry.npmjs.org/har-validator/-/har-validator-1.1.2.tgz", "integrity": "sha512-qzUWHSatjQ1V32ZQ4rFd82/f4hBoWqVTvs+k/i38WSW/1McYF4Bu7d6GBdxOscSxA/BIl5ulpp8AqTs8hkFOjg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCeF7iqOzuts9uIQ3dbUKenev+bH2aoqFYTTj2ypMHPFgIhAK8GS9andMaW+GyR65V+Y5rQ+bdNDQMNee0xN8VKYA6d"}]}, "directories": {}, "deprecated": "this library is no longer supported"}, "1.1.3": {"version": "1.1.3", "name": "har-validator", "description": "Extremely fast HTTP Archive (HAR) validator using JSON Schema", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "homepage": "https://github.com/ahmadnassri/har-validator", "license": "MIT", "main": "./src/index.js", "bin": {"har-validator": "./bin/har-validator"}, "keywords": ["har", "http", "archive", "validate", "validator"], "engines": {"node": ">=0.10"}, "files": ["bin", "src"], "repository": {"type": "git", "url": "https://github.com/ahmadnassri/har-validator"}, "bugs": {"url": "https://github.com/ahmadnassri/har-validator/issues"}, "scripts": {"test": "standard && mocha --reporter spec", "coverage": "istanbul cover ./node_modules/mocha/bin/_mocha", "codeclimate": "codeclimate < coverage/lcov.info"}, "devDependencies": {"codeclimate-test-reporter": "0.0.4", "istanbul": "^0.3.8", "mocha": "^2.2.1", "should": "^5.2.0", "standard": "^3.2.0"}, "dependencies": {"async": "^0.9.0", "bluebird": "^2.9.14", "chalk": "^1.0.0", "commander": "^2.7.1", "debug": "^2.1.3", "is-my-json-valid": "^2.10.0", "require-directory": "^2.1.0"}, "gitHead": "3469de9a77928b1f7bdb424794780fede65ec1e4", "_id": "har-validator@1.1.3", "_shasum": "9fac87db94b308758f506bcfcbaf104df655e616", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "9fac87db94b308758f506bcfcbaf104df655e616", "tarball": "https://registry.npmjs.org/har-validator/-/har-validator-1.1.3.tgz", "integrity": "sha512-ppXIj+3ntqSoFlkc92NgKGFVMh+I1BnQP413BCCZ9lBFDy45yyAPSCh9ZtUOhwG2/M8A0Qzif/Dk3R19Y0qTsw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD1N87pRavMTw75Unuq7X25pZpxrcR7QSLg4DL/C3EymwIhAM4hQUIBTjOoH6GYwe/k3/MwRbhfmgjYOA6nhME7azq4"}]}, "directories": {}, "deprecated": "this library is no longer supported"}, "1.2.0": {"version": "1.2.0", "name": "har-validator", "description": "Extremely fast HTTP Archive (HAR) validator using JSON Schema", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "homepage": "https://github.com/ahmadnassri/har-validator", "license": "MIT", "main": "./src/index.js", "bin": {"har-validator": "./bin/har-validator"}, "keywords": ["har", "http", "archive", "validate", "validator"], "engines": {"node": ">=0.10"}, "files": ["bin", "src"], "repository": {"type": "git", "url": "https://github.com/ahmadnassri/har-validator"}, "bugs": {"url": "https://github.com/ahmadnassri/har-validator/issues"}, "scripts": {"test": "standard && mocha --reporter spec", "coverage": "istanbul cover ./node_modules/mocha/bin/_mocha", "codeclimate": "codeclimate < coverage/lcov.info"}, "devDependencies": {"codeclimate-test-reporter": "0.0.4", "istanbul": "^0.3.8", "mocha": "^2.2.1", "should": "^5.2.0", "standard": "^3.2.0"}, "dependencies": {"async": "^0.9.0", "bluebird": "^2.9.14", "chalk": "^1.0.0", "commander": "^2.7.1", "debug": "^2.1.3", "is-my-json-valid": "^2.10.0", "require-directory": "^2.1.0"}, "gitHead": "fd078bc2b8bbc172047348b57d96fe22769da233", "_id": "har-validator@1.2.0", "_shasum": "eb23f88bf3071be117605470f008896c9e01cf70", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "eb23f88bf3071be117605470f008896c9e01cf70", "tarball": "https://registry.npmjs.org/har-validator/-/har-validator-1.2.0.tgz", "integrity": "sha512-x18Zx30FqS24iSq98YtwcMPmfl6Flw1bZqJGJIF6SvScoPzJSo2FtFKbduls0hafyLwN2O+GekF2Cns+VMBMyA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHBiY2NACWl0Y0wrCKUOsqA5bzEnkKNs7wRuZTs5qKAvAiEAofH9srw+PQDkq1qsYxrPyEaW9u6ilG9+L5P25XfPf28="}]}, "directories": {}, "deprecated": "this library is no longer supported"}, "1.3.0": {"version": "1.3.0", "name": "har-validator", "description": "Extremely fast HTTP Archive (HAR) validator using JSON Schema", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "homepage": "https://github.com/ahmadnassri/har-validator", "license": "MIT", "main": "./src/index.js", "bin": {"har-validator": "./bin/har-validator"}, "keywords": ["har", "http", "archive", "validate", "validator"], "engines": {"node": ">=0.10"}, "files": ["bin", "src"], "repository": {"type": "git", "url": "https://github.com/ahmadnassri/har-validator"}, "bugs": {"url": "https://github.com/ahmadnassri/har-validator/issues"}, "scripts": {"test": "standard && mocha --reporter spec", "coverage": "istanbul cover ./node_modules/mocha/bin/_mocha", "codeclimate": "codeclimate < coverage/lcov.info"}, "devDependencies": {"codeclimate-test-reporter": "0.0.4", "istanbul": "^0.3.8", "mocha": "^2.2.1", "should": "^5.2.0", "standard": "^3.2.0"}, "dependencies": {"async": "^0.9.0", "bluebird": "^2.9.14", "chalk": "^1.0.0", "commander": "^2.7.1", "debug": "^2.1.3", "is-my-json-valid": "^2.10.0", "require-directory": "^2.1.0"}, "gitHead": "1f8413fac2158edfb4168b7c2a0de68efc8c507d", "_id": "har-validator@1.3.0", "_shasum": "fc27a227148b41e554009401e95b687de7325050", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "fc27a227148b41e554009401e95b687de7325050", "tarball": "https://registry.npmjs.org/har-validator/-/har-validator-1.3.0.tgz", "integrity": "sha512-bDNS7uCGqdfGXiNwuktbVxw2Vb21ziicNYaycEzkXqo5CO77n6bcW2TJ//6ALo+yv8FSw0Jxi1SyTyovf8jVZA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFDDA3FzPLPCb83VdPNKXez0NiBCaQhUrCMwkgAZVW60AiEAoAe8gsYoPQ3lV08l+N76iDTP4sgHulba6V6qzXhFdPM="}]}, "directories": {}, "deprecated": "this library is no longer supported"}, "1.3.1": {"version": "1.3.1", "name": "har-validator", "description": "Extremely fast HTTP Archive (HAR) validator using JSON Schema", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "homepage": "https://github.com/ahmadnassri/har-validator", "license": "MIT", "main": "./src/index.js", "bin": {"har-validator": "./bin/har-validator"}, "keywords": ["har", "http", "archive", "validate", "validator"], "engines": {"node": ">=0.10"}, "files": ["bin", "src"], "repository": {"type": "git", "url": "https://github.com/ahmadnassri/har-validator"}, "bugs": {"url": "https://github.com/ahmadnassri/har-validator/issues"}, "scripts": {"test": "standard && mocha --reporter spec", "coverage": "istanbul cover ./node_modules/mocha/bin/_mocha", "codeclimate": "codeclimate < coverage/lcov.info"}, "devDependencies": {"codeclimate-test-reporter": "0.0.4", "istanbul": "^0.3.8", "mocha": "^2.2.1", "should": "^5.2.0", "standard": "^3.2.0"}, "dependencies": {"async": "^0.9.0", "bluebird": "^2.9.14", "chalk": "^1.0.0", "commander": "^2.7.1", "debug": "^2.1.3", "is-my-json-valid": "^2.10.0", "require-directory": "^2.1.0"}, "gitHead": "8427086157865b2996c47e510dda2b362bf2bb49", "_id": "har-validator@1.3.1", "_shasum": "99d58aa141025a450a314f01508beb9c9043dc5f", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "99d58aa141025a450a314f01508beb9c9043dc5f", "tarball": "https://registry.npmjs.org/har-validator/-/har-validator-1.3.1.tgz", "integrity": "sha512-ngZBgRobqe6F2oPsu8JlS9uTWuKd5pbWhBO0llau+jt5PA6rYaqMb/I4Z3fHiVABGg4VBOrjfoxOrDyBZYdJig==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAFabGlADB1NC0OyG28B2KlWQM5E/BnDY50SgaVzT3iAAiAJzwt4fHuc21tuRcWaIVwpZmpUKjPx464hhICv12drhQ=="}]}, "directories": {}, "deprecated": "this library is no longer supported"}, "1.4.0": {"version": "1.4.0", "name": "har-validator", "description": "Extremely fast HTTP Archive (HAR) validator using JSON Schema", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "homepage": "https://github.com/ahmadnassri/har-validator", "license": "MIT", "main": "./src/index.js", "bin": {"har-validator": "./bin/har-validator"}, "keywords": ["har", "http", "archive", "validate", "validator"], "engines": {"node": ">=0.10"}, "files": ["bin", "src"], "repository": {"type": "git", "url": "https://github.com/ahmadnassri/har-validator"}, "bugs": {"url": "https://github.com/ahmadnassri/har-validator/issues"}, "scripts": {"test": "standard && mocha --reporter spec", "coverage": "istanbul cover ./node_modules/mocha/bin/_mocha", "codeclimate": "codeclimate < coverage/lcov.info"}, "devDependencies": {"codeclimate-test-reporter": "0.0.4", "istanbul": "^0.3.8", "mocha": "^2.2.1", "should": "^5.2.0", "standard": "^3.2.0"}, "dependencies": {"async": "^0.9.0", "bluebird": "^2.9.14", "chalk": "^1.0.0", "commander": "^2.7.1", "debug": "^2.1.3", "is-my-json-valid": "^2.10.0", "require-directory": "^2.1.0"}, "gitHead": "6d7268e7bb929aaa98d1c919dcba231ea5e81084", "_id": "har-validator@1.4.0", "_shasum": "845924893a05602a9791c319f81d628948b1b2af", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "845924893a05602a9791c319f81d628948b1b2af", "tarball": "https://registry.npmjs.org/har-validator/-/har-validator-1.4.0.tgz", "integrity": "sha512-oiluFKiKHBBBDfJNMotzYW+/dcwxXY2TEmBCwTg7EQjQGTawJrULHN1dyo6u+xuCs1vxahrkEGja4Yd0OeZ3/g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCZa3HRihiumKSlKn+aCtmaci46q9X031u03V41c48hGwIhANH8Qq+tmjjIpgJKm/+IMBX7cG4jolXP2fv6twe6tlOG"}]}, "directories": {}, "deprecated": "this library is no longer supported"}, "1.5.0": {"version": "1.5.0", "name": "har-validator", "description": "Extremely fast HTTP Archive (HAR) validator using JSON Schema", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "homepage": "https://github.com/ahmadnassri/har-validator", "license": "MIT", "main": "./src/index.js", "bin": {"har-validator": "./bin/har-validator"}, "keywords": ["har", "http", "archive", "validate", "validator"], "engines": {"node": ">=0.10"}, "files": ["bin", "src"], "repository": {"type": "git", "url": "https://github.com/ahmadnassri/har-validator"}, "bugs": {"url": "https://github.com/ahmadnassri/har-validator/issues"}, "scripts": {"test": "standard && mocha --reporter spec", "coverage": "istanbul cover ./node_modules/mocha/bin/_mocha", "codeclimate": "codeclimate < coverage/lcov.info"}, "devDependencies": {"codeclimate-test-reporter": "0.0.4", "istanbul": "^0.3.8", "mocha": "^2.2.1", "should": "^5.2.0", "standard": "^3.2.0"}, "dependencies": {"async": "^0.9.0", "bluebird": "^2.9.14", "chalk": "^1.0.0", "commander": "^2.7.1", "debug": "^2.1.3", "is-my-json-valid": "^2.10.0", "require-directory": "^2.1.0"}, "gitHead": "5f76772c4d254610f980da7016fd706bfbb5e2d2", "_id": "har-validator@1.5.0", "_shasum": "0d79dfe456fb7297497400b69de7b3a99223a6fe", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "0d79dfe456fb7297497400b69de7b3a99223a6fe", "tarball": "https://registry.npmjs.org/har-validator/-/har-validator-1.5.0.tgz", "integrity": "sha512-9eO+pLS9HyjWp9qlHX4Lz8mejR4EcZXS/8/mXfcxuk/bzJ+QrWj4Xg+UkgsT7rWmaSTjcf/NqXUdt7bIhjF/TQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDlCEM+ysHap8KegmWS/+hjmgPmM8Ex/251FvZA278ynwIgR/s+1A+zdwQ9Ioc9c/GDmt2iWiI50PqCR437k6nEG+I="}]}, "directories": {}, "deprecated": "this library is no longer supported"}, "1.5.1": {"version": "1.5.1", "name": "har-validator", "description": "Extremely fast HTTP Archive (HAR) validator using JSON Schema", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "homepage": "https://github.com/ahmadnassri/har-validator", "license": "MIT", "main": "./src/index.js", "bin": {"har-validator": "./bin/har-validator"}, "keywords": ["har", "http", "archive", "validate", "validator"], "engines": {"node": ">=0.10"}, "files": ["bin", "src"], "repository": {"type": "git", "url": "https://github.com/ahmadnassri/har-validator"}, "bugs": {"url": "https://github.com/ahmadnassri/har-validator/issues"}, "scripts": {"test": "standard && mocha --reporter spec", "coverage": "istanbul cover ./node_modules/mocha/bin/_mocha", "codeclimate": "codeclimate < coverage/lcov.info"}, "devDependencies": {"codeclimate-test-reporter": "0.0.4", "istanbul": "^0.3.8", "mocha": "^2.2.1", "require-directory": "^2.1.0", "should": "^5.2.0", "standard": "^3.2.0"}, "dependencies": {"bluebird": "^2.9.14", "chalk": "^1.0.0", "commander": "^2.7.1", "is-my-json-valid": "^2.10.0"}, "gitHead": "f26e756a95b405f15fad4b923f48648c53a3968e", "_id": "har-validator@1.5.1", "_shasum": "5c28a7574123c5401b33ba3aa5932ff51880502b", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "5c28a7574123c5401b33ba3aa5932ff51880502b", "tarball": "https://registry.npmjs.org/har-validator/-/har-validator-1.5.1.tgz", "integrity": "sha512-dPF40HJ1ceUP4/+ERqglIWHVHMsiU/U340ysOPovH5ElFugPVR1cWV9f2rZm2IAOPcmG7JygOuhbkWWFN7HFIA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICtW+mao3kyPDh7rlBNYDqMKcpZaTUPGf3BGRQRVa9ARAiB0taLZbAVARNjsePns03rlvpyzfywSpbBATj8IlVJjhg=="}]}, "directories": {}, "deprecated": "this library is no longer supported"}, "1.6.0": {"version": "1.6.0", "name": "har-validator", "description": "Extremely fast HTTP Archive (HAR) validator using JSON Schema", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "homepage": "https://github.com/ahmadnassri/har-validator", "license": "MIT", "main": "./src/index.js", "bin": {"har-validator": "./bin/har-validator"}, "keywords": ["har", "http", "archive", "validate", "validator"], "engines": {"node": ">=0.10"}, "files": ["bin", "src"], "repository": {"type": "git", "url": "https://github.com/ahmadnassri/har-validator"}, "bugs": {"url": "https://github.com/ahmadnassri/har-validator/issues"}, "scripts": {"test": "standard && mocha --reporter spec", "coverage": "istanbul cover ./node_modules/mocha/bin/_mocha", "codeclimate": "codeclimate < coverage/lcov.info"}, "devDependencies": {"codeclimate-test-reporter": "0.0.4", "istanbul": "^0.3.11", "mocha": "^2.2.1", "require-directory": "^2.1.0", "should": "^5.2.0", "standard": "^3.3.0"}, "dependencies": {"bluebird": "^2.9.21", "chalk": "^1.0.0", "commander": "^2.7.1", "is-my-json-valid": "^2.10.0"}, "gitHead": "036e96ea46f511b1a84cc3e0c6022f33ecc0758f", "_id": "har-validator@1.6.0", "_shasum": "f327e8d4a1c698e0965300330965f1f4ffb68d0a", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "f327e8d4a1c698e0965300330965f1f4ffb68d0a", "tarball": "https://registry.npmjs.org/har-validator/-/har-validator-1.6.0.tgz", "integrity": "sha512-Qp+2/oZN48bM/svqAJ6aLZ6ucxSYd2rUtTufH8nbwZ3XeJ6UpB8tHjmR7i99nwKCqQHQti/sS2ueEvuWZUywfw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDp1xuDlGAAbE6hVEaSa6KBnK+qCu4dKklCeRg+3atg/wIgEUBt4D2s2RCpIK3GiKsPXXajM1SVvXsKJjpGYOtrYzw="}]}, "directories": {}, "deprecated": "this library is no longer supported"}, "1.6.1": {"version": "1.6.1", "name": "har-validator", "description": "Extremely fast HTTP Archive (HAR) validator using JSON Schema", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "homepage": "https://github.com/ahmadnassri/har-validator", "license": "MIT", "main": "./src/index.js", "bin": {"har-validator": "./bin/har-validator"}, "keywords": ["har", "http", "archive", "validate", "validator"], "engines": {"node": ">=0.10"}, "files": ["bin", "src"], "repository": {"type": "git", "url": "https://github.com/ahmadnassri/har-validator"}, "bugs": {"url": "https://github.com/ahmadnassri/har-validator/issues"}, "scripts": {"test": "standard && mocha --reporter spec", "coverage": "istanbul cover ./node_modules/mocha/bin/_mocha", "codeclimate": "codeclimate < coverage/lcov.info"}, "devDependencies": {"codeclimate-test-reporter": "0.0.4", "istanbul": "^0.3.11", "mocha": "^2.2.1", "require-directory": "^2.1.0", "should": "^5.2.0", "standard": "^3.3.0"}, "dependencies": {"bluebird": "^2.9.21", "chalk": "^1.0.0", "commander": "^2.7.1", "is-my-json-valid": "^2.10.0"}, "gitHead": "aa68c8efacea7437bb545d816a14e20db444fde5", "_id": "har-validator@1.6.1", "_shasum": "baef452cde645eff7d26562e8e749d7fd000b7fd", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "baef452cde645eff7d26562e8e749d7fd000b7fd", "tarball": "https://registry.npmjs.org/har-validator/-/har-validator-1.6.1.tgz", "integrity": "sha512-s7hmegRC4esORegG2JrYeb+5Y+K3EpFw9fBlyJXdJxGyeOPVxxWEcevvunDZhYqLDx2UAPa2ElFBK+dwsDsTmw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFGfd9jQjXMsTGhWoXJxZfW0WSf6b8Z5NNW6Vw6Yd1EWAiBopVRn+MIu4tqTR3j0W6Tu2yeA+w2AeSH1oYBEU0xiCw=="}]}, "directories": {}, "deprecated": "this library is no longer supported"}, "1.7.0": {"version": "1.7.0", "name": "har-validator", "description": "Extremely fast HTTP Archive (HAR) validator using JSON Schema", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "homepage": "https://github.com/ahmadnassri/har-validator", "license": "MIT", "main": "./src/index.js", "bin": {"har-validator": "./bin/har-validator"}, "keywords": ["har", "http", "archive", "validate", "validator"], "engines": {"node": ">=0.10"}, "files": ["bin", "src"], "repository": {"type": "git", "url": "https://github.com/ahmadnassri/har-validator"}, "bugs": {"url": "https://github.com/ahmadnassri/har-validator/issues"}, "scripts": {"test": "standard && mocha --reporter spec", "coverage": "istanbul cover ./node_modules/mocha/bin/_mocha", "codeclimate": "codeclimate < coverage/lcov.info"}, "devDependencies": {"codeclimate-test-reporter": "0.0.4", "istanbul": "^0.3.13", "mocha": "^2.2.4", "require-directory": "^2.1.0", "should": "^6.0.1", "standard": "^3.7.2"}, "dependencies": {"bluebird": "^2.9.25", "chalk": "^1.0.0", "commander": "^2.8.1", "is-my-json-valid": "^2.10.1"}, "gitHead": "74aab6b4994681cdb961db88f5129c5096eaf06a", "_id": "har-validator@1.7.0", "_shasum": "563f8c58edca6410e2e408b0e540161da580dc46", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "563f8c58edca6410e2e408b0e540161da580dc46", "tarball": "https://registry.npmjs.org/har-validator/-/har-validator-1.7.0.tgz", "integrity": "sha512-nt/jVmQNMvtNkOG2Kr7+85EHwbb9EttRc9jxibRggmmg9p7enyUlxm108s91NB6v26yzlubsbVUqAeTMTblJfg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGrRsUlW8mj97FCel5c0/BEoof++SCjHEyeuZoCN0zdgAiEAtiX/bDb68lXom5xG9FrHsLt5/JWgXkMuFyAbT1oON/A="}]}, "directories": {}, "deprecated": "this library is no longer supported"}, "1.7.1": {"version": "1.7.1", "name": "har-validator", "description": "Extremely fast HTTP Archive (HAR) validator using JSON Schema", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "homepage": "https://github.com/ahmadnassri/har-validator", "repository": {"type": "git", "url": "https://github.com/ahmadnassri/har-validator"}, "license": "MIT", "main": "./src/index.js", "bin": {"har-validator": "./bin/har-validator"}, "keywords": ["har", "http", "archive", "validate", "validator"], "engines": {"node": ">=0.10"}, "files": ["bin", "src"], "bugs": {"url": "https://github.com/ahmadnassri/har-validator/issues"}, "scripts": {"test": "standard && echint && mocha --reporter spec", "coverage": "istanbul cover ./node_modules/mocha/bin/_mocha", "codeclimate": "codeclimate < coverage/lcov.info"}, "echint": {"ignore": ["coverage/**"]}, "devDependencies": {"codeclimate-test-reporter": "0.0.4", "echint": "^1.1.0", "istanbul": "^0.3.14", "mocha": "^2.2.5", "require-directory": "^2.1.0", "should": "^6.0.3", "standard": "^3.11.1"}, "dependencies": {"bluebird": "^2.9.26", "chalk": "^1.0.0", "commander": "^2.8.1", "is-my-json-valid": "^2.12.0"}, "gitHead": "328d7f2f37affcc4fca1db13da68f2be817ad31c", "_id": "har-validator@1.7.1", "_shasum": "8ec8952f8287d21b451ba3e36f27ed8d997d8a95", "_from": ".", "_npmVersion": "2.7.4", "_nodeVersion": "0.12.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "8ec8952f8287d21b451ba3e36f27ed8d997d8a95", "tarball": "https://registry.npmjs.org/har-validator/-/har-validator-1.7.1.tgz", "integrity": "sha512-mGPgUT0++wIXf7aXhCNU6yuP8HJ2ESKRIerB92sYBEaUX8aksu2d813dNm0ZT2wXOsG4E+gKULmoaaySJjmm1g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDTE3eoCSovh8DH3s0B3MDfjaX1gUQyDi1u2Qs+ENJJpAiAwrs0Gww15lY+hgrnfygjkiWKF8Xvx3eSmEy2grKcpQg=="}]}, "directories": {}, "deprecated": "this library is no longer supported"}, "1.8.0": {"version": "1.8.0", "name": "har-validator", "description": "Extremely fast HTTP Archive (HAR) validator using JSON Schema", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "homepage": "https://github.com/ahmadnassri/har-validator", "repository": {"type": "git", "url": "git+https://github.com/ahmadnassri/har-validator.git"}, "license": "ISC", "main": "lib/index", "bin": {"har-validator": "bin/har-validator"}, "keywords": ["har", "http", "archive", "validate", "validator"], "engines": {"node": ">=0.10"}, "files": ["bin", "lib"], "bugs": {"url": "https://github.com/ahmadnassri/har-validator/issues"}, "scripts": {"pretest": "standard && echint", "test": "mocha", "posttest": "npm run coverage", "coverage": "istanbul cover --dir coverage _mocha -- -R dot", "codeclimate": "codeclimate < coverage/lcov.info"}, "echint": {"ignore": ["coverage/**"]}, "devDependencies": {"codeclimate-test-reporter": "0.0.4", "echint": "^1.3.0", "istanbul": "^0.3.15", "mocha": "^2.2.5", "require-directory": "^2.1.1", "should": "^7.0.1", "standard": "^4.3.1"}, "dependencies": {"bluebird": "^2.9.30", "chalk": "^1.0.0", "commander": "^2.8.1", "is-my-json-valid": "^2.12.0"}, "gitHead": "8fd21c30edb23a1fed2d50b934d055d1be3dd7c9", "_id": "har-validator@1.8.0", "_shasum": "d83842b0eb4c435960aeb108a067a3aa94c0eeb2", "_from": ".", "_npmVersion": "2.11.2", "_nodeVersion": "0.12.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "d83842b0eb4c435960aeb108a067a3aa94c0eeb2", "tarball": "https://registry.npmjs.org/har-validator/-/har-validator-1.8.0.tgz", "integrity": "sha512-0+M2lRG5aXVEFwZZ2tUeRVBZT5AxViug9y94qquvQaHHVoL9ydL86aJvI3K28rwoD+DL15DzAgWtPCXNhdTKAQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAkFQeegQqWolnCwMMFl1XHWXct3mZXWT5GsO4OhyCgBAiB/sluuienbxHPnkFH+k9MomqlKaN8Lnkcd2+hhdfx2yw=="}]}, "directories": {}, "deprecated": "this library is no longer supported"}, "2.0.0": {"version": "2.0.0", "name": "har-validator", "description": "Extremely fast HTTP Archive (HAR) validator using JSON Schema", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "homepage": "https://github.com/ahmadnassri/har-validator", "repository": {"type": "git", "url": "git+https://github.com/ahmadnassri/har-validator.git"}, "license": "ISC", "main": "lib/index", "bin": {"har-validator": "bin/har-validator"}, "keywords": ["har", "http", "archive", "validate", "validator"], "engines": {"node": ">=0.10"}, "files": ["bin", "lib"], "bugs": {"url": "https://github.com/ahmadnassri/har-validator/issues"}, "scripts": {"pretest": "standard && echint", "test": "mocha", "posttest": "npm run coverage", "coverage": "istanbul cover --dir coverage _mocha -- -R dot", "codeclimate": "codeclimate < coverage/lcov.info"}, "echint": {"ignore": ["coverage/**"]}, "devDependencies": {"codeclimate-test-reporter": "0.1.1", "echint": "^1.5.0", "istanbul": "^0.3.21", "mocha": "^2.3.3", "require-directory": "^2.1.1", "should": "^7.1.0", "should-promised": "^0.3.1", "standard": "^5.3.1"}, "dependencies": {"chalk": "^1.1.1", "commander": "^2.8.1", "is-my-json-valid": "^2.12.2", "pinkie-promise": "^1.0.0"}, "gitHead": "b4ad33d7e4e34b8e78b465dcd89a2609c14cc64e", "_id": "har-validator@2.0.0", "_shasum": "58ce9953f0e4ea4d793a16be3a039776915f296f", "_from": ".", "_npmVersion": "2.14.4", "_nodeVersion": "4.1.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "58ce9953f0e4ea4d793a16be3a039776915f296f", "tarball": "https://registry.npmjs.org/har-validator/-/har-validator-2.0.0.tgz", "integrity": "sha512-AxleA5J2I3a+0grOnMmHqvM06zL6quXATZDGmNQUXuFkk7RPdkxQO3MrDHJcFmD6dGvS2IhZJKge68adDGKf9g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDcDdAV4iZYwVGAlytH3RAYBr3Ix09z7dfKUuGwSOoFSAIgDzJKx3UhEyKpLAalehnBYEoYCENj8dW+baOHcMOSLI8="}]}, "directories": {}, "deprecated": "this library is no longer supported"}, "2.0.1": {"version": "2.0.1", "name": "har-validator", "description": "Extremely fast HTTP Archive (HAR) validator using JSON Schema", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "homepage": "https://github.com/ahmadnassri/har-validator", "repository": {"type": "git", "url": "git+https://github.com/ahmadnassri/har-validator.git"}, "license": "ISC", "main": "lib/index", "bin": {"har-validator": "bin/har-validator"}, "keywords": ["har", "http", "archive", "validate", "validator"], "engines": {"node": ">=0.10"}, "files": ["bin", "lib"], "bugs": {"url": "https://github.com/ahmadnassri/har-validator/issues"}, "scripts": {"pretest": "standard && echint", "test": "mocha", "posttest": "npm run coverage", "coverage": "istanbul cover --dir coverage _mocha -- -R dot", "codeclimate": "codeclimate < coverage/lcov.info"}, "echint": {"ignore": ["coverage/**"]}, "devDependencies": {"codeclimate-test-reporter": "0.1.1", "echint": "^1.5.0", "istanbul": "^0.3.21", "mocha": "^2.3.3", "require-directory": "^2.1.1", "should": "^7.1.0", "should-promised": "^0.3.1", "standard": "^5.3.1"}, "dependencies": {"chalk": "^1.1.1", "commander": "^2.8.1", "is-my-json-valid": "^2.12.2", "pinkie-promise": "^1.0.0"}, "gitHead": "95861d4f049475220a25352735dfd9937197bc18", "_id": "har-validator@2.0.1", "_shasum": "042659a007bb8b5b6351cc927df0ee87073c8879", "_from": ".", "_npmVersion": "2.14.4", "_nodeVersion": "4.1.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "042659a007bb8b5b6351cc927df0ee87073c8879", "tarball": "https://registry.npmjs.org/har-validator/-/har-validator-2.0.1.tgz", "integrity": "sha512-YRyorgLgrGx4jdgua+U991TQFGT61v47vCZl3pnbFxJNRs9XJg7xQgzwPN+XTlaZwIKMNdG5Vo032AultcF0Ng==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHkSCD9wdtyvzIbdmlWperqD7WeXasJzn7TVnAChRW7jAiAoN0PCKiiXZzWa8lYjT/b8eAXR1lV/wUltyXzsFv4yFQ=="}]}, "directories": {}, "deprecated": "this library is no longer supported"}, "2.0.2": {"version": "2.0.2", "name": "har-validator", "description": "Extremely fast HTTP Archive (HAR) validator using JSON Schema", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "homepage": "https://github.com/ahmadnassri/har-validator", "repository": {"type": "git", "url": "git+https://github.com/ahmadnassri/har-validator.git"}, "license": "ISC", "main": "lib/index", "bin": {"har-validator": "bin/har-validator"}, "keywords": ["har", "http", "archive", "validate", "validator"], "engines": {"node": ">=0.10"}, "files": ["bin", "lib"], "bugs": {"url": "https://github.com/ahmadnassri/har-validator/issues"}, "scripts": {"pretest": "standard && echint", "test": "mocha", "posttest": "npm run coverage", "coverage": "istanbul cover --dir coverage _mocha -- -R dot", "codeclimate": "codeclimate < coverage/lcov.info"}, "echint": {"ignore": ["coverage/**"]}, "devDependencies": {"codeclimate-test-reporter": "0.1.1", "echint": "^1.5.0", "istanbul": "^0.3.21", "mocha": "^2.3.3", "require-directory": "^2.1.1", "should": "^7.1.0", "should-promised": "^0.3.1", "standard": "^5.3.1"}, "dependencies": {"chalk": "^1.1.1", "commander": "^2.8.1", "is-my-json-valid": "^2.12.2", "pinkie-promise": "^1.0.0"}, "gitHead": "46efd17253a81fb70614e904d9e766aa4de4f394", "_id": "har-validator@2.0.2", "_shasum": "233d0fa887b98a4f345969f811a2eec70d97aed7", "_from": ".", "_npmVersion": "2.14.4", "_nodeVersion": "4.1.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "233d0fa887b98a4f345969f811a2eec70d97aed7", "tarball": "https://registry.npmjs.org/har-validator/-/har-validator-2.0.2.tgz", "integrity": "sha512-u5G5hjzC6R0x1pMeJae2awpNbwN3CVsZRWwZGO0eS/cJit3RdRGq3en/Cd9f9xFg5hx09SvBQRAHViEdG7lLIA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDbiNRWoOizezChJHtUhmvV+liimDI3BB1lhk/N9+d6QwIhAKnJGvfDN9iYUL6Lo1j6hBFcHHL8zGQ+1CUUuM+AlAgt"}]}, "directories": {}, "deprecated": "this library is no longer supported"}, "2.0.3": {"version": "2.0.3", "name": "har-validator", "description": "Extremely fast HTTP Archive (HAR) validator using JSON Schema", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "homepage": "https://github.com/ahmadnassri/har-validator", "repository": {"type": "git", "url": "git+https://github.com/ahmadnassri/har-validator.git"}, "license": "ISC", "main": "lib/index", "bin": {"har-validator": "bin/har-validator"}, "keywords": ["har", "http", "archive", "validate", "validator"], "engines": {"node": ">=0.10"}, "files": ["bin", "lib"], "bugs": {"url": "https://github.com/ahmadnassri/har-validator/issues"}, "scripts": {"pretest": "standard && echint", "test": "mocha", "posttest": "npm run coverage", "coverage": "istanbul cover --dir coverage _mocha -- -R dot", "codeclimate": "codeclimate < coverage/lcov.info"}, "echint": {"ignore": ["coverage/**"]}, "devDependencies": {"codeclimate-test-reporter": "0.1.1", "echint": "^1.5.0", "istanbul": "^0.4.0", "mocha": "^2.3.4", "require-directory": "^2.1.1", "should": "^7.1.1", "should-promised": "^0.3.1", "standard": "^5.4.1"}, "dependencies": {"chalk": "^1.1.1", "commander": "^2.9.0", "is-my-json-valid": "^2.12.3", "pinkie-promise": "^2.0.0"}, "gitHead": "a71163c62b8786a41d503248fb60893598f3c26f", "_id": "har-validator@2.0.3", "_shasum": "5a9e12564a571cf0b81ef93c2157bd1617168883", "_from": ".", "_npmVersion": "2.14.4", "_nodeVersion": "4.1.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "5a9e12564a571cf0b81ef93c2157bd1617168883", "tarball": "https://registry.npmjs.org/har-validator/-/har-validator-2.0.3.tgz", "integrity": "sha512-SujSwVMZD3hGLnB8lnwH0eW029etWNfbNBWW/8qlmGbXVU3J+b2XiP5hztmIKQ5fw+iGwRwFDIfn2o4ydmROXA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCf3BQplIkmvMyE4SODfjdD+R23eAyYuYg6qrh3pIJqtgIgWVirJgz5t+Hj5Vk+8x7PPMouZy/ne5Jp0UKyHcCJScM="}]}, "directories": {}, "deprecated": "this library is no longer supported"}, "2.0.4": {"version": "2.0.4", "name": "har-validator", "description": "Extremely fast HTTP Archive (HAR) validator using JSON Schema", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "homepage": "https://github.com/ahmadnassri/har-validator", "repository": {"type": "git", "url": "git+https://github.com/ahmadnassri/har-validator.git"}, "license": "ISC", "main": "lib/index", "bin": {"har-validator": "bin/har-validator"}, "keywords": ["har", "http", "archive", "validate", "validator"], "engines": {"node": ">=0.10"}, "files": ["bin", "lib"], "bugs": {"url": "https://github.com/ahmadnassri/har-validator/issues"}, "scripts": {"pretest": "standard && echint", "test": "mocha", "posttest": "npm run coverage", "coverage": "istanbul cover --dir coverage _mocha -- -R dot", "codeclimate": "codeclimate < coverage/lcov.info"}, "echint": {"ignore": ["coverage/**"]}, "devDependencies": {"codeclimate-test-reporter": "0.1.1", "echint": "^1.5.0", "istanbul": "^0.4.0", "mocha": "^2.3.4", "require-directory": "^2.1.1", "should": "^7.1.1", "should-promised": "^0.3.1", "standard": "^5.4.1"}, "dependencies": {"chalk": "^1.1.1", "commander": "^2.9.0", "is-my-json-valid": "^2.12.3", "pinkie-promise": "^2.0.0"}, "gitHead": "e18706ca70b93d3874ebc2f7203ec749b36771e8", "_id": "har-validator@2.0.4", "_shasum": "aab19d1d21b88e87b6785c49bbb14e09fc5615b3", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.4.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "aab19d1d21b88e87b6785c49bbb14e09fc5615b3", "tarball": "https://registry.npmjs.org/har-validator/-/har-validator-2.0.4.tgz", "integrity": "sha512-LrU4mhvB5bkp9YNmzY9cLv5ZpiauRaJL5uA0BNkFn2yPYRr/ITgMykhwK+SjTQh+Iavg3z5RvlNvpSJeRBcnMA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHhcBwkO7gCQOq7BoD9OrkAZzz2OhRJRerw68Ns0ZWsLAiEAhT3LGrV8ZB+4Y0UhJqN23HcXviaIbQJPhiVOb2wxsJA="}]}, "directories": {}, "deprecated": "this library is no longer supported"}, "2.0.5": {"version": "2.0.5", "name": "har-validator", "description": "Extremely fast HTTP Archive (HAR) validator using JSON Schema", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "homepage": "https://github.com/ahmadnassri/har-validator", "repository": {"type": "git", "url": "git+https://github.com/ahmadnassri/har-validator.git"}, "license": "ISC", "main": "lib/index", "bin": {"har-validator": "bin/har-validator"}, "keywords": ["har", "http", "archive", "validate", "validator"], "engines": {"node": ">=0.10"}, "files": ["bin", "lib"], "bugs": {"url": "https://github.com/ahmadnassri/har-validator/issues"}, "scripts": {"pretest": "standard && echint", "test": "mocha", "posttest": "npm run coverage", "coverage": "istanbul cover --dir coverage _mocha -- -R dot", "codeclimate": "codeclimate < coverage/lcov.info"}, "echint": {"ignore": ["coverage/**"]}, "devDependencies": {"codeclimate-test-reporter": "0.1.1", "echint": "^1.5.0", "istanbul": "^0.4.2", "mocha": "^2.3.4", "require-directory": "^2.1.1", "should": "^8.1.1", "standard": "^5.4.1"}, "dependencies": {"chalk": "^1.1.1", "commander": "^2.9.0", "is-my-json-valid": "^2.12.3", "pinkie-promise": "^2.0.0"}, "gitHead": "1382234c6fbdeba8f7821f6a1f700e0911ac6aea", "_id": "har-validator@2.0.5", "_shasum": "a3a51876594b6ede76bb63eb1b311c246b18a9bb", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.4.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "a3a51876594b6ede76bb63eb1b311c246b18a9bb", "tarball": "https://registry.npmjs.org/har-validator/-/har-validator-2.0.5.tgz", "integrity": "sha512-SGDHpROSEEfJkDnK35KBemmhJebD0gb69fOaYJ963RFI4+sMILkNdULk9dhErwAFzqmSHDXO0cBH8AjCZ4pvyA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCiPL6gpFuUiLrMrW9WXc0CSggC8+EPuMHGLhDXhaB0kgIhANQLkqIFtk/ZDy7MMg2qA9sv3+W1jkF9GPy4Im432KJr"}]}, "directories": {}, "deprecated": "this library is no longer supported"}, "2.0.6": {"version": "2.0.6", "name": "har-validator", "description": "Extremely fast HTTP Archive (HAR) validator using JSON Schema", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "homepage": "https://github.com/ahmadnassri/har-validator", "repository": {"type": "git", "url": "https://github.com/ahmadnassri/har-validator"}, "license": "ISC", "main": "lib/index", "bin": {"har-validator": "bin/har-validator"}, "keywords": ["har", "http", "archive", "validate", "validator"], "engines": {"node": ">=0.10"}, "files": ["bin", "lib"], "bugs": {"url": "https://github.com/ahmadnassri/har-validator/issues"}, "scripts": {"pretest": "standard && echint", "test": "mocha", "posttest": "npm run coverage", "coverage": "istanbul cover --dir coverage _mocha -- -R dot", "codeclimate": "codeclimate < coverage/lcov.info"}, "echint": {"ignore": ["coverage/**"]}, "devDependencies": {"codeclimate-test-reporter": "0.2.1", "echint": "^1.5.1", "istanbul": "^0.4.2", "mocha": "^2.3.4", "require-directory": "^2.1.1", "should": "^8.1.1", "standard": "^5.4.1"}, "dependencies": {"chalk": "^1.1.1", "commander": "^2.9.0", "is-my-json-valid": "^2.12.4", "pinkie-promise": "^2.0.0"}, "gitHead": "92ccddad2e5d13e6e32c764e06c347d67805b211", "_id": "har-validator@2.0.6", "_shasum": "cdcbc08188265ad119b6a5a7c8ab70eecfb5d27d", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.2.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "cdcbc08188265ad119b6a5a7c8ab70eecfb5d27d", "tarball": "https://registry.npmjs.org/har-validator/-/har-validator-2.0.6.tgz", "integrity": "sha512-P6tFV+wCcUL3nbyTDAvveDySfbhy0XkDtAIfZP6HITjM2WUsiPna/Eg1Yy93SFXvahqoX+kt0n+6xlXKDXYowA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDiZGpYmF2YYLQSRznLCSGxNr+IeEDkcPN120sdTRskWwIhAMKmH9cGxF5OH06yf/bfnP7ZS9KBu1JUKW8OMH6B2Js1"}]}, "directories": {}, "deprecated": "this library is no longer supported"}, "2.1.0": {"name": "har-validator", "description": "unnamed package", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "homepage": "https://github.com/ahmadnassri/har-validator", "repository": {"type": "git", "url": "git+https://github.com/ahmadnassri/har-validator.git"}, "license": "ISC", "main": "lib/index.js", "bin": {"har-validator": "lib/bin.js"}, "keywords": ["har", "http", "archive", "validate", "validator"], "engines": {"node": ">=0.10"}, "files": ["schemas", "lib", "src"], "bugs": {"url": "https://github.com/ahmadnassri/har-validator/issues"}, "scripts": {"compile": "babel -q src --out-dir lib", "test": "tap test/*.js --node-arg=--require --node-arg=babel-register --node-arg=--require --node-arg=babel-polyfill | tap-mocha-reporter spec", "pretest": "standard && echint", "coverage": "tap test/*.js --coverage --nyc-arg=--require --nyc-arg=babel-register --nyc-arg=--require --nyc-arg=babel-polyfill", "codeclimate": "nyc report --reporter=text-lcov | codeclimate-test-reporter", "prepublish": "npm run compile", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "babel": {"presets": ["es2015"], "plugins": ["transform-export-extensions", "transform-object-rest-spread"]}, "standard": {"ignore": ["lib/**", "src/index.js", "test/fixtures/**"]}, "echint": {"ignore": ["lib/**"]}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "devDependencies": {"babel-cli": "^6.6.5", "babel-plugin-transform-export-extensions": "^6.5.0", "babel-plugin-transform-object-rest-spread": "^6.6.5", "babel-polyfill": "^6.6.1", "babel-preset-es2015": "^6.6.0", "babel-register": "^6.6.5", "codeclimate-test-reporter": "^0.3.1", "cz-conventional-changelog": "^1.1.5", "echint": "^1.5.3", "nyc": "^6.0.0", "semantic-release": "^6.2.0", "standard": "^6.0.7", "tap": "^5.7.0", "tap-mocha-reporter": "0.0.24"}, "dependencies": {"chalk": "^1.1.1", "commander": "^2.9.0", "is-my-json-valid": "^2.13.1"}, "version": "2.1.0", "_id": "har-validator@2.1.0", "gitHead": "3643416452884e3cba2e3a91376668c6b0f92dc8", "_shasum": "ad74bfa091493caeaa4ea686bcfdee05e012b67a", "_from": ".", "_npmVersion": "2.14.12", "_nodeVersion": "4.3.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "ad74bfa091493caeaa4ea686bcfdee05e012b67a", "tarball": "https://registry.npmjs.org/har-validator/-/har-validator-2.1.0.tgz", "integrity": "sha512-1JljtzeOh35fPuUW2i6PGkJ30oC1Aj5eUg9DxwT8T2ce5ilVMCi7lpOhFEVAV/FQqwB6Hoh6YM6fNBJJqgxK5A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCZ2jJOg+Jv0RYS3kT3R1AqRkf74gzgF9A4its/EEJ1KQIhALFcxbXSJBJlGMb72LkUgmfXKBtvdNV/DdE4vYLGonU/"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-13-west.internal.npmjs.com", "tmp": "tmp/har-validator-2.1.0.tgz_1457397160699_0.41516683134250343"}, "directories": {}, "deprecated": "this library is no longer supported"}, "2.1.1": {"name": "har-validator", "description": "unnamed package", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "homepage": "https://github.com/ahmadnassri/har-validator", "repository": {"type": "git", "url": "git+https://github.com/ahmadnassri/har-validator.git"}, "license": "ISC", "main": "lib/index.js", "bin": {"har-validator": "lib/bin.js"}, "keywords": ["har", "http", "archive", "validate", "validator"], "engines": {"node": ">=0.10"}, "files": ["schemas", "lib", "src"], "bugs": {"url": "https://github.com/ahmadnassri/har-validator/issues"}, "scripts": {"compile": "babel -q src --out-dir lib", "test": "tap test/*.js --node-arg=--require --node-arg=babel-register --node-arg=--require --node-arg=babel-polyfill | tap-mocha-reporter spec", "pretest": "standard && echint", "coverage": "tap test/*.js --coverage --nyc-arg=--require --nyc-arg=babel-register --nyc-arg=--require --nyc-arg=babel-polyfill", "codeclimate": "nyc report --reporter=text-lcov | codeclimate-test-reporter", "prepublish": "npm run compile", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "babel": {"presets": ["es2015"], "plugins": ["transform-export-extensions", "transform-object-rest-spread"]}, "standard": {"ignore": ["lib/**", "src/index.js", "test/fixtures/**"]}, "echint": {"ignore": ["lib/**"]}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "devDependencies": {"babel-cli": "^6.6.5", "babel-plugin-transform-export-extensions": "^6.5.0", "babel-plugin-transform-object-rest-spread": "^6.6.5", "babel-polyfill": "^6.6.1", "babel-preset-es2015": "^6.6.0", "babel-register": "^6.6.5", "codeclimate-test-reporter": "^0.3.1", "cz-conventional-changelog": "^1.1.5", "echint": "^1.5.3", "nyc": "^6.0.0", "semantic-release": "^6.2.0", "standard": "^6.0.7", "tap": "^5.7.0", "tap-mocha-reporter": "0.0.24"}, "dependencies": {"chalk": "^1.1.1", "commander": "^2.9.0", "is-my-json-valid": "^2.13.1"}, "version": "2.1.1", "_id": "har-validator@2.1.1", "gitHead": "f739d4d11b304c6e824bb745b6919e725f34e2ee", "_shasum": "20b1a6eac74c52ac11f9aa17e1636421572f90b8", "_from": ".", "_npmVersion": "2.14.12", "_nodeVersion": "4.3.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "20b1a6eac74c52ac11f9aa17e1636421572f90b8", "tarball": "https://registry.npmjs.org/har-validator/-/har-validator-2.1.1.tgz", "integrity": "sha512-DJQ6Hf6qZc4GZNARqksLrKLRg3QV0MuQELsACqANwBQ7LvH3UnHLjEh3kn6MqAqETtq/6dEWvwRbcCWD1pCF8g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQChBlgMTm8Bq1/qWUGs0e/sHYZj7XSyHo+QqKIYVFLy5gIgKCc1ypKA1d92T5gnyrNIUB6pojrRNXUbZ+eBoFqvQr0="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/har-validator-2.1.1.tgz_1457397795987_0.7874950913246721"}, "directories": {}, "deprecated": "this library is no longer supported"}, "2.1.2": {"name": "har-validator", "description": "unnamed package", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "homepage": "https://github.com/ahmadnassri/har-validator", "repository": {"type": "git", "url": "git+https://github.com/ahmadnassri/har-validator.git"}, "license": "ISC", "main": "lib/promise.js", "bin": {"har-validator": "lib/bin.js"}, "keywords": ["har", "cli", "http", "archive", "validate", "validator"], "engines": {"node": ">=0.10"}, "files": ["schemas", "lib", "src"], "bugs": {"url": "https://github.com/ahmadnassri/har-validator/issues"}, "scripts": {"compile": "babel -q src --out-dir lib", "test": "tap test/*.js --node-arg=--require --node-arg=babel-register --node-arg=--require --node-arg=babel-polyfill | tap-mocha-reporter spec", "pretest": "standard && echint", "coverage": "tap test/*.js --coverage --nyc-arg=--require --nyc-arg=babel-register --nyc-arg=--require --nyc-arg=babel-polyfill", "codeclimate": "nyc report --reporter=text-lcov | codeclimate-test-reporter", "prepublish": "npm run compile", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "babel": {"presets": ["es2015"], "plugins": ["transform-export-extensions"]}, "standard": {"ignore": ["lib/**", "test/fixtures/**"]}, "echint": {"ignore": ["lib/**"]}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "devDependencies": {"babel-cli": "^6.6.5", "babel-plugin-transform-export-extensions": "^6.5.0", "babel-polyfill": "^6.7.4", "babel-preset-es2015": "^6.6.0", "babel-register": "^6.7.2", "codeclimate-test-reporter": "^0.3.1", "cz-conventional-changelog": "^1.1.5", "echint": "^1.5.3", "nyc": "^6.2.1", "semantic-release": "^6.2.0", "standard": "^6.0.8", "tap": "^5.7.0", "tap-mocha-reporter": "0.0.24"}, "dependencies": {"chalk": "^1.1.3", "commander": "^2.9.0", "is-my-json-valid": "^2.13.1"}, "version": "2.1.2", "gitHead": "4142354a10ddcf77352eb3901bc8978237990858", "_id": "har-validator@2.1.2", "_shasum": "f9475bacaef2f9fff2d17ff40c775aeadc8e3b3f", "_from": ".", "_npmVersion": "2.15.1", "_nodeVersion": "4.4.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "f9475bacaef2f9fff2d17ff40c775aeadc8e3b3f", "tarball": "https://registry.npmjs.org/har-validator/-/har-validator-2.1.2.tgz", "integrity": "sha512-BkrqE5mz/qVhf7G61mgTU+vvPRosEVhzgvfbg0bzjOgb+B4czBaH8UVw5vpkBL//TX7enA4EYH67u7022mJDNw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCJPxRWwhNJBSkJEXrd1yATzzG3LriBrm7x5LGxxHVHfgIgbZHS9bohZkXh6ALS2yuAMZBa9FW9wKcwKJ9WAqWKXYc="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/har-validator-2.1.2.tgz_1460779536990_0.46244929172098637"}, "directories": {}, "deprecated": "this library is no longer supported"}, "2.1.3": {"name": "har-validator", "description": "Extremely fast HTTP Archive (HAR) validator using JSON Schema", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "homepage": "https://github.com/ahmadnassri/har-validator", "repository": {"type": "git", "url": "git+https://github.com/ahmadnassri/har-validator.git"}, "license": "ISC", "main": "lib/promise.js", "bin": {"har-validator": "lib/bin.js"}, "keywords": ["har", "cli", "http", "archive", "validate", "validator"], "engines": {"node": ">=0.10"}, "files": ["schemas", "lib", "src"], "bugs": {"url": "https://github.com/ahmadnassri/har-validator/issues"}, "scripts": {"compile": "babel -q src --out-dir lib", "test": "tap test/*.js --node-arg=--require --node-arg=babel-register --node-arg=--require --node-arg=babel-polyfill | tap-mocha-reporter spec", "pretest": "standard && echint", "coverage": "tap test/*.js --coverage --nyc-arg=--require --nyc-arg=babel-register --nyc-arg=--require --nyc-arg=babel-polyfill", "codeclimate": "nyc report --reporter=text-lcov | codeclimate-test-reporter", "prepublish": "npm run compile", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "babel": {"presets": ["es2015"], "plugins": ["transform-export-extensions"]}, "standard": {"ignore": ["lib/**", "test/fixtures/**"]}, "echint": {"ignore": ["lib/**"]}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "devDependencies": {"babel-cli": "^6.6.5", "babel-plugin-transform-export-extensions": "^6.5.0", "babel-polyfill": "^6.7.4", "babel-preset-es2015": "^6.6.0", "babel-register": "^6.7.2", "codeclimate-test-reporter": "^0.3.1", "cz-conventional-changelog": "^1.1.5", "echint": "^1.5.3", "nyc": "^6.2.1", "semantic-release": "^6.2.0", "standard": "^6.0.8", "tap": "^5.7.0", "tap-mocha-reporter": "0.0.24"}, "dependencies": {"chalk": "^1.1.3", "commander": "^2.9.0", "is-my-json-valid": "^2.13.1"}, "version": "2.1.3", "gitHead": "9cbdb120b94bdfa1a74f3365b3ba21c4834ee68d", "_id": "har-validator@2.1.3", "_shasum": "68ff49c85cfbc2b34fb42f6480a9796b6441dcf3", "_from": ".", "_npmVersion": "2.15.1", "_nodeVersion": "4.4.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "68ff49c85cfbc2b34fb42f6480a9796b6441dcf3", "tarball": "https://registry.npmjs.org/har-validator/-/har-validator-2.1.3.tgz", "integrity": "sha512-ZTA/Qr85NGzNXqXL0rAQkTkXPyQx+KjfzJLxP2Vrg3v4cUjRSaKV8Wj/NWLgGO1dzQ2Hwdy8Zhcx7p+3iYTQLQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDfSPAi1WgeCUZ4olu+T6BDaSzani8bcziDZPtUbdfldAIhAIUF8EZhFCMCRcWMOF5QujTvi8hNKiViYi/5IbPSeTiz"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/har-validator-2.1.3.tgz_1460780120240_0.6595741915516555"}, "directories": {}, "deprecated": "this library is no longer supported"}, "3.0.0": {"version": "3.0.0", "name": "har-validator", "description": "Extremely fast HTTP Archive (HAR) validator using JSON Schema", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "homepage": "https://github.com/ahmadnassri/har-validator", "repository": {"type": "git", "url": "git+https://github.com/ahmadnassri/har-validator.git"}, "license": "ISC", "main": "lib/promise.js", "bin": {"har-validator": "lib/bin.js"}, "keywords": ["har", "cli", "http", "archive", "validate", "validator"], "engines": {"node": ">=4"}, "files": ["schemas", "lib", "src"], "bugs": {"url": "https://github.com/ahmadnassri/har-validator/issues"}, "scripts": {"compile": "babel -q src", "test": "BABEL_ENV=test tap test/*.js --reporter spec --node-arg=--require --node-arg=babel-register", "pretest": "snazzy && echint", "coverage": "BABEL_ENV=test tap test/*.js --reporter silent --coverage --nyc-arg=--require --nyc-arg=babel-register", "codeclimate": "BABEL_ENV=test tap --coverage-report=text-lcov | codeclimate-test-reporter", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "standard": {"ignore": ["lib/**"]}, "echint": {"ignore": ["lib/**"]}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "devDependencies": {"babel-cli": "^6.18.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-preset-env": "0.0.9", "babel-register": "^6.18.0", "codeclimate-test-reporter": "^0.4.0", "cz-conventional-changelog": "^1.2.0", "echint": "^2.0.0", "semantic-release": "^6.3.2", "snazzy": "^5.0.0", "tap": "^8.0.1"}, "dependencies": {"chalk": "^1.1.3", "commander": "^2.9.0", "is-my-json-valid": "^2.15.0"}, "gitHead": "e9512ab4f75aae816ef22c14ec2c0063b2df9fb3", "_id": "har-validator@3.0.0", "_shasum": "a76fc868c7301e34ebde2585b13d660c14e2f4d7", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.6.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "a76fc868c7301e34ebde2585b13d660c14e2f4d7", "tarball": "https://registry.npmjs.org/har-validator/-/har-validator-3.0.0.tgz", "integrity": "sha512-l5I8kcY5HAvzQurPSoY6ZtyfEHq0fxNY3cE4Xe3/kzGsU+lzY/0QFwT+SSP6VkbbNQDocYLre72aMo/+nrtaJA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBjYSHbSLNFIcqykJYAhJD305V00p7gXSBWRFDbOupA2AiEAhdyB8uyFQprnkqHchGSR/QbXYUM2Bv6dqfg114NFwj0="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/har-validator-3.0.0.tgz_1480721428684_0.0023917395155876875"}, "directories": {}, "deprecated": "this library is no longer supported"}, "3.1.0": {"version": "3.1.0", "name": "har-validator", "description": "Extremely fast HTTP Archive (HAR) validator using JSON Schema", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "homepage": "https://github.com/ahmadnassri/har-validator", "repository": {"type": "git", "url": "git+https://github.com/ahmadnassri/har-validator.git"}, "license": "ISC", "main": "lib/promise.js", "bin": {"har-validator": "lib/bin.js"}, "keywords": ["har", "cli", "http", "archive", "validate", "validator"], "engines": {"node": ">=4"}, "files": ["schemas", "lib", "src"], "bugs": {"url": "https://github.com/ahmadnassri/har-validator/issues"}, "scripts": {"compile": "babel -q src", "test": "BABEL_ENV=test tap test/*.js --reporter spec --node-arg=--require --node-arg=babel-register", "pretest": "snazzy && echint", "coverage": "BABEL_ENV=test tap test/*.js --reporter silent --coverage --nyc-arg=--require --nyc-arg=babel-register", "codeclimate": "BABEL_ENV=test tap --coverage-report=text-lcov | codeclimate-test-reporter", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "standard": {"ignore": ["lib/**"]}, "echint": {"ignore": ["lib/**"]}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "devDependencies": {"babel-cli": "^6.18.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-preset-env": "0.0.9", "babel-register": "^6.18.0", "codeclimate-test-reporter": "^0.4.0", "cz-conventional-changelog": "^1.2.0", "echint": "^2.0.0", "semantic-release": "^6.3.2", "snazzy": "^5.0.0", "tap": "^8.0.1"}, "dependencies": {"chalk": "^1.1.3", "commander": "^2.9.0", "is-my-json-valid": "^2.15.0"}, "gitHead": "ddba552bcf226d77546c6586b2e13b6e9a849e8c", "_id": "har-validator@3.1.0", "_shasum": "12f5011218b719d6d4a17f8f6c91e7d4af64741c", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.6.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "12f5011218b719d6d4a17f8f6c91e7d4af64741c", "tarball": "https://registry.npmjs.org/har-validator/-/har-validator-3.1.0.tgz", "integrity": "sha512-dy3oZxaLOV1QJuZbyufag0AKlLp3kG+n9L6iLD/ekN3JYGvWnRS5EY0TNu9uYzX/DHYrhfSXMzoa9ag2Hf/8Hw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDTwKW9i9cxXLkdgFze84bC2cbaNn1mNatbFolnNbU3ZQIgFbR0ESLZTV60DLBB3ODjeIeZ/6p7Ef/tm3dMp45X6Uk="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/har-validator-3.1.0.tgz_1480793085863_0.5526188455987722"}, "directories": {}, "deprecated": "this library is no longer supported"}, "3.2.0": {"version": "3.2.0", "name": "har-validator", "description": "Extremely fast HTTP Archive (HAR) validator using JSON Schema", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "homepage": "https://github.com/ahmadnassri/har-validator", "repository": {"type": "git", "url": "git+https://github.com/ahmadnassri/har-validator.git"}, "license": "ISC", "main": "lib/node4/promise.js", "bin": {"har-validator": "lib/node4/bin.js"}, "keywords": ["har", "cli", "http", "archive", "validate", "validator"], "engines": {"node": ">=4"}, "files": ["schemas", "lib", "src"], "bugs": {"url": "https://github.com/ahmadnassri/har-validator/issues"}, "scripts": {"compile": "babel -q src", "test": "BABEL_ENV=test tap test/*.js --reporter spec --node-arg=--require --node-arg=babel-register", "pretest": "snazzy && echint", "coverage": "BABEL_ENV=test tap test/*.js --reporter silent --coverage --nyc-arg=--require --nyc-arg=babel-register", "codeclimate": "BABEL_ENV=test tap --coverage-report=text-lcov | codeclimate-test-reporter", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "standard": {"ignore": ["lib/**"]}, "echint": {"ignore": ["lib/**"]}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "devDependencies": {"babel-cli": "^6.18.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-preset-env": "0.0.9", "babel-register": "^6.18.0", "codeclimate-test-reporter": "^0.4.0", "cz-conventional-changelog": "^1.2.0", "echint": "^2.0.0", "semantic-release": "^6.3.2", "snazzy": "^5.0.0", "tap": "^8.0.1"}, "dependencies": {"chalk": "^1.1.3", "commander": "^2.9.0", "is-my-json-valid": "^2.15.0"}, "gitHead": "585809756100e4a41849cd8b4ee36e7dcab8bec2", "_id": "har-validator@3.2.0", "_shasum": "65b5373f33e361b265e12b252b7c6e1be912c8f5", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.6.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "65b5373f33e361b265e12b252b7c6e1be912c8f5", "tarball": "https://registry.npmjs.org/har-validator/-/har-validator-3.2.0.tgz", "integrity": "sha512-/ahMoR1CIcCOdomybjKqY22KgnV4yvZV4P9/dqoSpcuqyGyc3k3VTjQyLTVtkv+tXCtTl5mVA4PZsFLTmlorEw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCAT54ZKpGpFTvvF2q59hAhoDN27ZainPmErYUqxuZBnQIgKBidWSwnBhPnIfISQXZGkXLmG4zY2MFYLQIIwYVa39c="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/har-validator-3.2.0.tgz_1480793470577_0.4003969542682171"}, "directories": {}, "deprecated": "this library is no longer supported"}, "3.3.0": {"version": "3.3.0", "name": "har-validator", "description": "Extremely fast HTTP Archive (HAR) validator using JSON Schema", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "homepage": "https://github.com/ahmadnassri/har-validator", "repository": {"type": "git", "url": "git+https://github.com/ahmadnassri/har-validator.git"}, "license": "ISC", "main": "lib/node4/promise.js", "bin": {"har-validator": "lib/node4/bin.js"}, "keywords": ["har", "cli", "ajv", "http", "archive", "validate", "validator"], "engines": {"node": ">=4"}, "files": ["schemas", "lib", "src"], "bugs": {"url": "https://github.com/ahmadnassri/har-validator/issues"}, "scripts": {"compile": "babel -q src", "test": "BABEL_ENV=test tap test --reporter spec --node-arg=--require --node-arg=babel-register", "test-one": "BABEL_ENV=test tap --reporter spec --node-arg=--require --node-arg=babel-register", "test-cli": "BABEL_ENV=test babel-node src/bin.js", "pretest": "snazzy && echint", "coverage": "BABEL_ENV=test tap test --reporter silent --coverage --nyc-arg=--require --nyc-arg=babel-register", "codeclimate": "BABEL_ENV=test tap --coverage-report=text-lcov | codeclimate-test-reporter", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "standard": {"ignore": ["lib/**"]}, "echint": {"ignore": ["lib/**"]}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "devDependencies": {"babel-cli": "^6.18.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-preset-env": "0.0.9", "babel-register": "^6.18.0", "codeclimate-test-reporter": "^0.4.0", "cz-conventional-changelog": "^1.2.0", "echint": "^2.0.0", "semantic-release": "^6.3.2", "snazzy": "^5.0.0", "tap": "^8.0.1"}, "dependencies": {"ajv": "^4.9.0", "chalk": "^1.1.3", "commander": "^2.9.0", "har-schema": "^0.1.0"}, "gitHead": "89b648644a0c8508434f7096a36b8d1b288d33c7", "_id": "har-validator@3.3.0", "_shasum": "61d77ac71ab25c5740e5ad7ae3147cf17f820849", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.6.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "61d77ac71ab25c5740e5ad7ae3147cf17f820849", "tarball": "https://registry.npmjs.org/har-validator/-/har-validator-3.3.0.tgz", "integrity": "sha512-LnijoPOuNRdjCTG236E3PuwaJwqVcsx0bo7Pt3+juE/bE0EeO1bZoMxWDGNrP98Du+dS++RA5dW+8Qo0Qa5I5Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC9/g77u4iI4+ZdgoeWsSJNGKZuX4oOXse/twvawheflgIhANmk8ccm78g50GepOdPi2rSfQF+LyPNaOMI0f77VDsnI"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/har-validator-3.3.0.tgz_1480802544484_0.12833484495058656"}, "directories": {}, "deprecated": "this library is no longer supported"}, "3.3.1": {"version": "3.3.1", "name": "har-validator", "description": "Extremely fast HTTP Archive (HAR) validator using JSON Schema", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "homepage": "https://github.com/ahmadnassri/har-validator", "repository": {"type": "git", "url": "git+https://github.com/ahmadnassri/har-validator.git"}, "license": "ISC", "main": "lib/node4/promise.js", "bin": {"har-validator": "lib/node4/bin.js"}, "keywords": ["har", "cli", "ajv", "http", "archive", "validate", "validator"], "engines": {"node": ">=4"}, "files": ["lib", "src"], "bugs": {"url": "https://github.com/ahmadnassri/har-validator/issues"}, "scripts": {"compile": "babel -q src", "test": "BABEL_ENV=test tap test --reporter spec --node-arg=--require --node-arg=babel-register", "test-one": "BABEL_ENV=test tap --reporter spec --node-arg=--require --node-arg=babel-register", "test-cli": "BABEL_ENV=test babel-node src/bin.js", "pretest": "snazzy && echint", "coverage": "BABEL_ENV=test tap test --reporter silent --coverage --nyc-arg=--require --nyc-arg=babel-register", "codeclimate": "BABEL_ENV=test tap --coverage-report=text-lcov | codeclimate-test-reporter", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "standard": {"ignore": ["lib/**"]}, "echint": {"ignore": ["lib/**"]}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "devDependencies": {"babel-cli": "^6.18.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-preset-env": "0.0.9", "babel-register": "^6.18.0", "codeclimate-test-reporter": "^0.4.0", "cz-conventional-changelog": "^1.2.0", "echint": "^2.0.0", "semantic-release": "^6.3.2", "snazzy": "^5.0.0", "tap": "^8.0.1"}, "dependencies": {"ajv": "^4.9.0", "chalk": "^1.1.3", "commander": "^2.9.0", "har-schema": "^0.1.0"}, "gitHead": "ed15905a90046a26faafd24f68d18e1ec6a50c2b", "_id": "har-validator@3.3.1", "_shasum": "514cd19910b65a6cc09bd40f46097fbe2e6be062", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.6.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "514cd19910b65a6cc09bd40f46097fbe2e6be062", "tarball": "https://registry.npmjs.org/har-validator/-/har-validator-3.3.1.tgz", "integrity": "sha512-w0<PERSON>eaunBhKDy7kWoOE4WMIWYTJ5sRtiJOLNMpySw9QxPD6wRdKXQDaCXjsmRxYjsFw9M2kOwUW2qqevLDL7vxA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDm0Nuyw0R1tFbVZtbrj7fLBrjXG2/qd4m1/oHpEAzdMgIhAMD8mDktyb0RM/Nc5pt0aTKl1SgpXUrUL0xB9xetbGB9"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/har-validator-3.3.1.tgz_1480805589502_0.05432287883013487"}, "directories": {}, "deprecated": "this library is no longer supported"}, "3.4.0": {"version": "3.4.0", "name": "har-validator", "description": "Extremely fast HTTP Archive (HAR) validator using JSON Schema", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "homepage": "https://github.com/ahmadnassri/har-validator", "repository": {"type": "git", "url": "git+https://github.com/ahmadnassri/har-validator.git"}, "license": "ISC", "main": "lib/node4/promise.js", "keywords": ["har", "cli", "ajv", "http", "archive", "validate", "validator"], "engines": {"node": ">=4"}, "files": ["lib", "src"], "bugs": {"url": "https://github.com/ahmadnassri/har-validator/issues"}, "scripts": {"compile": "babel -q src", "test": "BABEL_ENV=test tap test --reporter spec --node-arg=--require --node-arg=babel-register", "test-one": "BABEL_ENV=test tap --reporter spec --node-arg=--require --node-arg=babel-register", "pretest": "snazzy && echint", "coverage": "BABEL_ENV=test tap test --reporter silent --coverage --nyc-arg=--require --nyc-arg=babel-register", "codeclimate": "BABEL_ENV=test tap --coverage-report=text-lcov | codeclimate-test-reporter", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "standard": {"ignore": ["lib/**"]}, "echint": {"ignore": ["lib/**"]}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "devDependencies": {"babel-cli": "^6.18.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-preset-env": "0.0.9", "babel-register": "^6.18.0", "codeclimate-test-reporter": "^0.4.0", "cz-conventional-changelog": "^1.2.0", "echint": "^2.0.0", "semantic-release": "^6.3.2", "snazzy": "^5.0.0", "tap": "^8.0.1"}, "dependencies": {"ajv": "^4.9.0", "har-schema": "^0.1.0"}, "gitHead": "9b372bdac2d67819cd693bc6dc3f775a3531e3e0", "_id": "har-validator@3.4.0", "_shasum": "d3e64d083232c98e46c70cfa5a2d7d9cd72d45d5", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.6.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "d3e64d083232c98e46c70cfa5a2d7d9cd72d45d5", "tarball": "https://registry.npmjs.org/har-validator/-/har-validator-3.4.0.tgz", "integrity": "sha512-AY6eKICOTgB+LagnXgS7TVMX25Kb/6A9JH+iugnZAx3hC9z8IG8f+S+TS8Va++svNXwStDNlYDerylbDf4lwtg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH06cNCQm2JXReK/4qOCmsRv/zAabdfCG8imEMFEEKYRAiA1PA8Z3RO+Nlydq85mSa19ofOSKI2piY5VtHpJ5fBj2g=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/har-validator-3.4.0.tgz_1480811996569_0.28792823222465813"}, "directories": {}, "deprecated": "this library is no longer supported"}, "4.0.0": {"version": "4.0.0", "name": "har-validator", "description": "Extremely fast HTTP Archive (HAR) validator using JSON Schema", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "homepage": "https://github.com/ahmadnassri/har-validator", "repository": {"type": "git", "url": "git+https://github.com/ahmadnassri/har-validator.git"}, "license": "ISC", "main": "lib/node4/promise.js", "keywords": ["har", "cli", "ajv", "http", "archive", "validate", "validator"], "engines": {"node": ">=4"}, "files": ["lib", "src"], "bugs": {"url": "https://github.com/ahmadnassri/har-validator/issues"}, "scripts": {"compile": "babel -q src", "test": "BABEL_ENV=test tap test --reporter spec --node-arg=--require --node-arg=babel-register", "test-one": "BABEL_ENV=test tap --reporter spec --node-arg=--require --node-arg=babel-register", "pretest": "snazzy && echint", "coverage": "BABEL_ENV=test tap test --reporter silent --coverage --nyc-arg=--require --nyc-arg=babel-register", "codeclimate": "BABEL_ENV=test tap --coverage-report=text-lcov | codeclimate-test-reporter", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "standard": {"ignore": ["lib/**"]}, "echint": {"ignore": ["lib/**"]}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "devDependencies": {"babel-cli": "^6.18.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-preset-env": "0.0.9", "babel-register": "^6.18.0", "codeclimate-test-reporter": "^0.4.0", "cz-conventional-changelog": "^1.2.0", "echint": "^2.0.0", "semantic-release": "^6.3.2", "snazzy": "^5.0.0", "tap": "^8.0.1"}, "dependencies": {"ajv": "^4.9.0", "har-schema": "^0.1.0"}, "gitHead": "1106bc5b47dd442e9cff9d98d48327dd909e8cc0", "_id": "har-validator@4.0.0", "_shasum": "57c395eb8a8a77faa3898957cc3b99ecb0561239", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.6.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "57c395eb8a8a77faa3898957cc3b99ecb0561239", "tarball": "https://registry.npmjs.org/har-validator/-/har-validator-4.0.0.tgz", "integrity": "sha512-2esSY/9Whafcxdr8OxPWnzYmoDvblfXX82zSICrhzZL7jXXbJxwka3tsqqDDI8IX1kcDjdlT2QzfaR60Jq2c8A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCauJoD0fZshqxeDlVmtuBcZrRBJxtLnSj8R0jg0Nh6EAIgBICkxWHtju5o8gXhzPNXYkUyaJGlzQ+Kb765qyb6AE8="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/har-validator-4.0.0.tgz_1480812398387_0.8863940783776343"}, "directories": {}, "deprecated": "this library is no longer supported"}, "4.0.1": {"version": "4.0.1", "name": "har-validator", "description": "Extremely fast HTTP Archive (HAR) validator using JSON Schema", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "homepage": "https://github.com/ahmadnassri/har-validator", "repository": {"type": "git", "url": "git+https://github.com/ahmadnassri/har-validator.git"}, "license": "ISC", "main": "lib/node4/promise.js", "keywords": ["har", "cli", "ajv", "http", "archive", "validate", "validator"], "engines": {"node": ">=4"}, "files": ["lib", "src"], "bugs": {"url": "https://github.com/ahmadnassri/har-validator/issues"}, "scripts": {"compile": "babel -q src", "test": "BABEL_ENV=test tap test --reporter spec --node-arg=--require --node-arg=babel-register", "test-one": "BABEL_ENV=test tap --reporter spec --node-arg=--require --node-arg=babel-register", "pretest": "snazzy && echint", "coverage": "BABEL_ENV=test tap test --reporter silent --coverage --nyc-arg=--require --nyc-arg=babel-register", "codeclimate": "BABEL_ENV=test tap --coverage-report=text-lcov | codeclimate-test-reporter", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "standard": {"ignore": ["lib/**"]}, "echint": {"ignore": ["lib/**"]}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "devDependencies": {"babel-cli": "^6.18.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-preset-env": "0.0.9", "babel-register": "^6.18.0", "codeclimate-test-reporter": "^0.4.0", "cz-conventional-changelog": "^1.2.0", "echint": "^2.0.0", "semantic-release": "^6.3.2", "snazzy": "^5.0.0", "tap": "^8.0.1"}, "dependencies": {"ajv": "^4.9.0", "har-schema": "^0.2.0"}, "gitHead": "4112131a22f4e77d287f05b83d4b8a58c1cbbb95", "_id": "har-validator@4.0.1", "_shasum": "4ff0e2e68c86ef111772c9d44475898c52227b0a", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.6.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "4ff0e2e68c86ef111772c9d44475898c52227b0a", "tarball": "https://registry.npmjs.org/har-validator/-/har-validator-4.0.1.tgz", "integrity": "sha512-IouO+OCJ6dYELvAwZkNliPXnHDJxQBL6A46f2EY1mMrkAWvPKnrpONQUGpkxCgFDnt6XkKyc6x2tbHx/Dr4k1w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDzY/F6rCVfdH/1E4F4AfP0H9MUvlg8f2OopNgpW4UZAwIgP6lLCaNOc+hSGUwwTxLbJ5h5k6jwhaQJzE3Y+PzK25w="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/har-validator-4.0.1.tgz_1480814460754_0.3203337125014514"}, "directories": {}, "deprecated": "this library is no longer supported"}, "4.0.2": {"version": "4.0.2", "name": "har-validator", "description": "Extremely fast HTTP Archive (HAR) validator using JSON Schema", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "homepage": "https://github.com/ahmadnassri/har-validator", "repository": {"type": "git", "url": "git+https://github.com/ahmadnassri/har-validator.git"}, "license": "ISC", "main": "lib/node4/promise.js", "keywords": ["har", "cli", "ajv", "http", "archive", "validate", "validator"], "engines": {"node": ">=4"}, "files": ["lib", "src"], "bugs": {"url": "https://github.com/ahmadnassri/har-validator/issues"}, "scripts": {"compile": "babel -q src", "test": "BABEL_ENV=test tap test --reporter spec --node-arg=--require --node-arg=babel-register", "test-one": "BABEL_ENV=test tap --reporter spec --node-arg=--require --node-arg=babel-register", "pretest": "snazzy && echint", "coverage": "BABEL_ENV=test tap test --reporter silent --coverage --nyc-arg=--require --nyc-arg=babel-register", "codeclimate": "BABEL_ENV=test tap --coverage-report=text-lcov | codeclimate-test-reporter", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "standard": {"ignore": ["lib/**"]}, "echint": {"ignore": ["lib/**"]}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "devDependencies": {"babel-cli": "^6.18.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-preset-env": "0.0.9", "babel-register": "^6.18.0", "codeclimate-test-reporter": "^0.4.0", "cz-conventional-changelog": "^1.2.0", "echint": "^2.1.0", "semantic-release": "^6.3.2", "snazzy": "^5.0.0", "tap": "^8.0.1"}, "dependencies": {"ajv": "^4.9.1", "har-schema": "^1.0.0"}, "gitHead": "062cc57a6d79816d2c06e6b23934140e100c9179", "_id": "har-validator@4.0.2", "_shasum": "4331710dc99e7109eac69fe130e9c118a3206cfa", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.6.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "4331710dc99e7109eac69fe130e9c118a3206cfa", "tarball": "https://registry.npmjs.org/har-validator/-/har-validator-4.0.2.tgz", "integrity": "sha512-rXWxq/C9ym5Sq0MTGuftUXjigo7W16k1kI1qnicCGsS/3ECZd3niwPHi24znQIaLljOx0cNPBxR84c7eP6UZpg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDug75TAmi7FoUdVIyDfG4nSw7OrOTFuGxjGofumthy5AiEArVLuG62jHPO6TFjS2JdRGhPnzhTJzCOyzoWjNw/VMFw="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/har-validator-4.0.2.tgz_1480829892578_0.04349543456919491"}, "directories": {}, "deprecated": "this library is no longer supported"}, "4.0.3": {"version": "4.0.3", "name": "har-validator", "description": "Extremely fast HTTP Archive (HAR) validator using JSON Schema", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "homepage": "https://github.com/ahmadnassri/har-validator", "repository": {"type": "git", "url": "git+https://github.com/ahmadnassri/har-validator.git"}, "license": "ISC", "main": "lib/node4/promise.js", "keywords": ["har", "cli", "ajv", "http", "archive", "validate", "validator"], "engines": {"node": ">=4"}, "files": ["lib", "src"], "bugs": {"url": "https://github.com/ahmadnassri/har-validator/issues"}, "scripts": {"compile": "babel -q src", "test": "BABEL_ENV=test tap test --reporter spec --node-arg=--require --node-arg=babel-register", "test-one": "BABEL_ENV=test tap --reporter spec --node-arg=--require --node-arg=babel-register", "pretest": "snazzy && echint", "coverage": "BABEL_ENV=test tap test --reporter silent --coverage --nyc-arg=--require --nyc-arg=babel-register", "codeclimate": "BABEL_ENV=test tap --coverage-report=text-lcov | codeclimate-test-reporter", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "standard": {"ignore": ["lib/**"]}, "echint": {"ignore": ["lib/**"]}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "devDependencies": {"babel-cli": "^6.18.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-preset-env": "0.0.9", "babel-register": "^6.18.0", "codeclimate-test-reporter": "^0.4.0", "cz-conventional-changelog": "^1.2.0", "echint": "^2.1.0", "semantic-release": "^6.3.2", "snazzy": "^5.0.0", "tap": "^8.0.1"}, "dependencies": {"ajv": "^4.9.1", "har-schema": "^1.0.1"}, "gitHead": "30f242427b0b1d6369213e3ed425dc74db28fbdc", "_id": "har-validator@4.0.3", "_shasum": "f9ff23eb88543d97dc5f0de7a6c4a26b84c08c92", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.6.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "f9ff23eb88543d97dc5f0de7a6c4a26b84c08c92", "tarball": "https://registry.npmjs.org/har-validator/-/har-validator-4.0.3.tgz", "integrity": "sha512-4GhIFv9sl1F6lUUIkbjJEY+mo2z6w36dPkNzf6qdS+B2Sm79NeBxxoFSSC8KXt3hBv3EzvDGXN4rlHE5vaJIiQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDMweV1K8iyE71tEHH/OybN50SiDhDdl9XWL8Dcs/zoCwIgRQMYNt5x2zIYQY8TPXdv+I92aCYTLySmneeebcKT0OM="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/har-validator-4.0.3.tgz_1480830990474_0.8110444666817784"}, "directories": {}, "deprecated": "this library is no longer supported"}, "4.0.4": {"version": "4.0.4", "name": "har-validator", "description": "Extremely fast HTTP Archive (HAR) validator using JSON Schema", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "homepage": "https://github.com/ahmadnassri/har-validator", "repository": {"type": "git", "url": "git+https://github.com/ahmadnassri/har-validator.git"}, "license": "ISC", "main": "lib/node4/promise.js", "keywords": ["har", "cli", "ajv", "http", "archive", "validate", "validator"], "engines": {"node": ">=4"}, "files": ["lib", "src"], "bugs": {"url": "https://github.com/ahmadnassri/har-validator/issues"}, "scripts": {"compile": "babel -q src", "test": "BABEL_ENV=test tap test --reporter spec --node-arg=--require --node-arg=babel-register", "test-one": "BABEL_ENV=test tap --reporter spec --node-arg=--require --node-arg=babel-register", "pretest": "snazzy && echint", "coverage": "BABEL_ENV=test tap test --reporter silent --coverage --nyc-arg=--require --nyc-arg=babel-register", "codeclimate": "BABEL_ENV=test tap --coverage-report=text-lcov | codeclimate-test-reporter", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "standard": {"ignore": ["lib/**"]}, "echint": {"ignore": ["lib/**"]}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "devDependencies": {"babel-cli": "^6.18.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-preset-env": "0.0.9", "babel-register": "^6.18.0", "codeclimate-test-reporter": "^0.4.0", "cz-conventional-changelog": "^1.2.0", "echint": "^2.1.0", "semantic-release": "^6.3.2", "snazzy": "^5.0.0", "tap": "^8.0.1"}, "dependencies": {"ajv": "^4.9.1", "har-schema": "^1.0.2"}, "gitHead": "28ae32c3efcd35feffbfbbdd587bce49bd395173", "_id": "har-validator@4.0.4", "_shasum": "b41433175d647f28d9089adb56eb4fbd09167d80", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.6.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "b41433175d647f28d9089adb56eb4fbd09167d80", "tarball": "https://registry.npmjs.org/har-validator/-/har-validator-4.0.4.tgz", "integrity": "sha512-IQjJ4mL+lqY2eOL++ouJp2v9JHBM5XLdpwmLpazfMBaf8180x++2lSzJ2SzI7X444BG/KMdtHi15ripNec3YRw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDFWIC3OcxIIb8hxl0bPtkGS4VFrtEM0yEapIiVNrf0pQIgEO6bSW2t8hFQmPsOH26T0h2uPggNN3Gl44WEGMEiqpM="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/har-validator-4.0.4.tgz_1480834464587_0.3108202340081334"}, "directories": {}, "deprecated": "this library is no longer supported"}, "4.1.0": {"version": "4.1.0", "name": "har-validator", "description": "Extremely fast HTTP Archive (HAR) validator using JSON Schema", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "homepage": "https://github.com/ahmadnassri/har-validator", "repository": {"type": "git", "url": "git+https://github.com/ahmadnassri/har-validator.git"}, "license": "ISC", "main": "lib/node4/promise.js", "keywords": ["har", "cli", "ajv", "http", "archive", "validate", "validator"], "engines": {"node": ">=4"}, "files": ["lib", "src"], "bugs": {"url": "https://github.com/ahmadnassri/har-validator/issues"}, "scripts": {"compile": "babel -q src", "test": "BABEL_ENV=test tap test --reporter spec --node-arg=--require --node-arg=babel-register", "test-one": "BABEL_ENV=test tap --reporter spec --node-arg=--require --node-arg=babel-register", "pretest": "snazzy && echint", "coverage": "BABEL_ENV=test tap test --reporter silent --coverage --nyc-arg=--require --nyc-arg=babel-register", "codeclimate": "BABEL_ENV=test tap --coverage-report=text-lcov | codeclimate-test-reporter", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "standard": {"ignore": ["lib/**"]}, "echint": {"ignore": ["lib/**"]}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "devDependencies": {"babel-cli": "^6.18.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-preset-env": "0.0.9", "babel-register": "^6.18.0", "codeclimate-test-reporter": "^0.4.0", "cz-conventional-changelog": "^1.2.0", "echint": "^2.1.0", "semantic-release": "^6.3.2", "snazzy": "^5.0.0", "tap": "^8.0.1"}, "dependencies": {"ajv": "^4.9.1", "har-schema": "^1.0.4"}, "gitHead": "ccb64add42d7452c60721bfd29eb319eefaf9c1f", "_id": "har-validator@4.1.0", "_shasum": "86e4f56f0cae4ccd72c276d772a527f5d606903c", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.6.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "86e4f56f0cae4ccd72c276d772a527f5d606903c", "tarball": "https://registry.npmjs.org/har-validator/-/har-validator-4.1.0.tgz", "integrity": "sha512-GHazvamln14NAbFvJakNnHElErfu0O4cBvfVkg/DTk4Xx3p2NdfmDSXHWzNtiC3nJrJyZQaqZvNmOELRdKfZWA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHCh+wFKC16TFBfPnPGRdDOqr2OVr4HdvNfi8dkMC0bcAiEAkHgsbjB0MfqWsBD0u83JHMFo5nlMiaVrm4wTDUj5E3k="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/har-validator-4.1.0.tgz_1480839862492_0.274944152450189"}, "directories": {}, "deprecated": "this library is no longer supported"}, "4.1.1": {"version": "4.1.1", "name": "har-validator", "description": "Extremely fast HTTP Archive (HAR) validator using JSON Schema", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "homepage": "https://github.com/ahmadnassri/har-validator", "repository": {"type": "git", "url": "git+https://github.com/ahmadnassri/har-validator.git"}, "license": "ISC", "main": "lib/node4/promise.js", "keywords": ["har", "cli", "ajv", "http", "archive", "validate", "validator"], "engines": {"node": ">=4"}, "files": ["lib", "src"], "bugs": {"url": "https://github.com/ahmadnassri/har-validator/issues"}, "scripts": {"compile": "babel -q src", "test": "BABEL_ENV=test tap test --reporter spec --node-arg=--require --node-arg=babel-register", "test-one": "BABEL_ENV=test tap --reporter spec --node-arg=--require --node-arg=babel-register", "pretest": "snazzy && echint", "coverage": "BABEL_ENV=test tap test --reporter silent --coverage --nyc-arg=--require --nyc-arg=babel-register", "codeclimate": "BABEL_ENV=test tap --coverage-report=text-lcov | codeclimate-test-reporter", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "standard": {"ignore": ["lib/**"]}, "echint": {"ignore": ["lib/**"]}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "devDependencies": {"babel-cli": "^6.18.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-preset-env": "0.0.9", "babel-register": "^6.18.0", "codeclimate-test-reporter": "^0.4.0", "cz-conventional-changelog": "^1.2.0", "echint": "^2.1.0", "semantic-release": "^6.3.2", "snazzy": "^5.0.0", "tap": "^8.0.1"}, "dependencies": {"ajv": "^4.9.1", "har-schema": "^1.0.5"}, "gitHead": "d9fcff4bc0f6225300cb1535590a89fa49a46e0e", "_id": "har-validator@4.1.1", "_shasum": "8df876e1ecfd925259f5ee5a1d0585c12b7f6629", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.6.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "8df876e1ecfd925259f5ee5a1d0585c12b7f6629", "tarball": "https://registry.npmjs.org/har-validator/-/har-validator-4.1.1.tgz", "integrity": "sha512-phv9SpSBi+k1I+lgSFqcv1tkRjCNkXDl9ur8ycmDwRbZQx2qznky4Sf/aLwcS7xT4G8/vxbtTVnFtNxdP8uMVw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCR/gNxXtUKMbFkl0nCfgzjC8NpxaCLGO40q43FzX66ygIhAL4sCOiq6iCY7g2obNPTKXGZlY+9aFQBmkcedXBVORwA"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/har-validator-4.1.1.tgz_1480880035555_0.45075810770504177"}, "directories": {}, "deprecated": "this library is no longer supported"}, "4.1.2": {"version": "4.1.2", "name": "har-validator", "description": "Extremely fast HTTP Archive (HAR) validator using JSON Schema", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "homepage": "https://github.com/ahmadnassri/har-validator", "repository": {"type": "git", "url": "git+https://github.com/ahmadnassri/har-validator.git"}, "license": "ISC", "main": "lib/node4/promise.js", "keywords": ["har", "cli", "ajv", "http", "archive", "validate", "validator"], "engines": {"node": ">=4"}, "files": ["lib", "src"], "bugs": {"url": "https://github.com/ahmadnassri/har-validator/issues"}, "scripts": {"compile": "babel -q src", "test": "BABEL_ENV=test tap test --reporter spec --node-arg=--require --node-arg=babel-register", "test-one": "BABEL_ENV=test tap --reporter spec --node-arg=--require --node-arg=babel-register", "pretest": "snazzy && echint", "coverage": "BABEL_ENV=test tap test --reporter silent --coverage --nyc-arg=--require --nyc-arg=babel-register", "codeclimate": "BABEL_ENV=test tap --coverage-report=text-lcov | codeclimate-test-reporter", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "standard": {"ignore": ["lib/**"]}, "echint": {"ignore": ["lib/**"]}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "devDependencies": {"babel-cli": "^6.18.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-preset-env": "0.0.9", "babel-register": "^6.18.0", "codeclimate-test-reporter": "^0.4.0", "cz-conventional-changelog": "^1.2.0", "echint": "^2.1.0", "semantic-release": "^6.3.2", "snazzy": "^5.0.0", "tap": "^8.0.1"}, "dependencies": {"ajv": "^4.9.1", "har-schema": "^1.0.5"}, "gitHead": "aaea445a6ea339e1a125b411beb8c1a585f2142b", "_id": "har-validator@4.1.2", "_shasum": "3dabdba74f3904482f5774b62de3aba394b92427", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.6.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "3dabdba74f3904482f5774b62de3aba394b92427", "tarball": "https://registry.npmjs.org/har-validator/-/har-validator-4.1.2.tgz", "integrity": "sha512-rDLOgk0hkxoOW0U0gej9UIrJhc3/RxbLJHozb3ceRjt65QtbJKwfFtoePFG+Cfwm7h4JRimXDmZCXga3TZMwng==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEGsOGBFmserPpTi5DkqeRWNeA20VUZDep1w0gQtb7AFAiBSqfLakuxyd7+jBJLSOfBx3BHTZZDbqJMnKetM/HGBgA=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/har-validator-4.1.2.tgz_1480881364452_0.5804192230571061"}, "directories": {}, "deprecated": "this library is no longer supported"}, "4.2.0": {"version": "4.2.0", "name": "har-validator", "description": "Extremely fast HTTP Archive (HAR) validator using JSON Schema", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "homepage": "https://github.com/ahmadnassri/har-validator", "repository": {"type": "git", "url": "git+https://github.com/ahmadnassri/har-validator.git"}, "license": "ISC", "main": "lib/node4/promise.js", "keywords": ["har", "cli", "ajv", "http", "archive", "validate", "validator"], "engines": {"node": ">=4"}, "files": ["lib", "src"], "bugs": {"url": "https://github.com/ahmadnassri/har-validator/issues"}, "scripts": {"compile": "babel -q src", "test": "BABEL_ENV=test tap test --reporter spec --node-arg=--require --node-arg=babel-register", "test-one": "BABEL_ENV=test tap --reporter spec --node-arg=--require --node-arg=babel-register", "pretest": "snazzy && echint", "coverage": "BABEL_ENV=test tap test --reporter silent --coverage --nyc-arg=--require --nyc-arg=babel-register", "codeclimate": "BABEL_ENV=test tap --coverage-report=text-lcov | codeclimate-test-reporter", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "standard": {"ignore": ["lib/**"]}, "echint": {"ignore": ["lib/**"]}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "devDependencies": {"babel-cli": "^6.18.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-preset-env": "0.0.9", "babel-register": "^6.18.0", "codeclimate-test-reporter": "^0.4.0", "cz-conventional-changelog": "^1.2.0", "echint": "^2.1.0", "semantic-release": "^6.3.2", "snazzy": "^5.0.0", "tap": "^8.0.1"}, "dependencies": {"ajv": "^4.9.1", "har-schema": "^1.0.5"}, "gitHead": "b38c2d7d1286fb17788cac6447bcafa6b2cf72e1", "_id": "har-validator@4.2.0", "_shasum": "c2efa9f6c50fee92ef033cf30b796a2c5b660cd7", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.6.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "c2efa9f6c50fee92ef033cf30b796a2c5b660cd7", "tarball": "https://registry.npmjs.org/har-validator/-/har-validator-4.2.0.tgz", "integrity": "sha512-CfHUR0KotNE6hSHoVdljZR0WqDvxcjm7AiblsQgAJppIpUH2W/Hc8uoX2RbyLWdvOiVmgXgukWavehUNPwZQZw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH0/d+K8aIP7wlhFkflcM2aaNRFvsr2rps+jIli2bJCzAiABHriZkA49p4TYzQggahwBWn6NEdmFICt0PsbyQNQ/vQ=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/har-validator-4.2.0.tgz_1480882330276_0.14189945277757943"}, "directories": {}, "deprecated": "this library is no longer supported"}, "4.2.1": {"version": "4.2.1", "name": "har-validator", "description": "Extremely fast HTTP Archive (HAR) validator using JSON Schema", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "homepage": "https://github.com/ahmadnassri/har-validator", "repository": {"type": "git", "url": "git+https://github.com/ahmadnassri/har-validator.git"}, "license": "ISC", "main": "lib/node4/promise.js", "keywords": ["har", "cli", "ajv", "http", "archive", "validate", "validator"], "engines": {"node": ">=4"}, "files": ["lib", "src"], "bugs": {"url": "https://github.com/ahmadnassri/har-validator/issues"}, "scripts": {"compile": "babel -q src", "test": "BABEL_ENV=test tap test --reporter spec --node-arg=--require --node-arg=babel-register", "test-one": "BABEL_ENV=test tap --reporter spec --node-arg=--require --node-arg=babel-register", "pretest": "snazzy && echint", "coverage": "BABEL_ENV=test tap test --reporter silent --coverage --nyc-arg=--require --nyc-arg=babel-register", "codeclimate": "BABEL_ENV=test tap --coverage-report=text-lcov | codeclimate-test-reporter", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "standard": {"ignore": ["lib/**"]}, "echint": {"ignore": ["lib/**"]}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "devDependencies": {"babel-cli": "^6.18.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-preset-env": "1.1.10", "babel-register": "^6.18.0", "codeclimate-test-reporter": "^0.4.0", "cz-conventional-changelog": "^1.2.0", "echint": "^4.0.1", "semantic-release": "^6.3.2", "snazzy": "^6.0.0", "tap": "^10.0.0"}, "dependencies": {"ajv": "^4.9.1", "har-schema": "^1.0.5"}, "gitHead": "06cb69e2da150de1643bfe511f0374f23b7a5b11", "_id": "har-validator@4.2.1", "_shasum": "33481d0f1bbff600dd203d75812a6a5fba002e2a", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.8.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "33481d0f1bbff600dd203d75812a6a5fba002e2a", "tarball": "https://registry.npmjs.org/har-validator/-/har-validator-4.2.1.tgz", "integrity": "sha512-5Gbp6RAftMYYV3UEI4c4Vv3+a4dQ7taVyvHt+/L6kRt+f4HX1GweAk5UDWN0SvdVnRBzGQ6OG89pGaD9uSFnVw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAEbZ4i6JlJHHHJiWfDJPjNWyx3BF676g01QtQ1Y0iGuAiBy8Ncho8vTXhaRhMx7ngoXaPGLSyiQhW28pOv1FEniAg=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/har-validator-4.2.1.tgz_1488636538686_0.5101928301155567"}, "directories": {}, "deprecated": "this library is no longer supported"}, "5.0.0": {"version": "5.0.0", "name": "har-validator", "description": "Extremely fast HTTP Archive (HAR) validator using JSON Schema", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "homepage": "https://github.com/ahmadnassri/har-validator", "repository": {"type": "git", "url": "git+https://github.com/ahmadnassri/har-validator.git"}, "license": "ISC", "main": "lib/promise.js", "keywords": ["har", "cli", "ajv", "http", "archive", "validate", "validator"], "engines": {"node": ">=4"}, "files": ["lib"], "bugs": {"url": "https://github.com/ahmadnassri/har-validator/issues"}, "scripts": {"lint": "standard && echint", "pretest": "npm run lint", "test": "tap test", "coverage": "tap test --reporter silent --coverage"}, "devDependencies": {"echint": "^4.0.1", "standard": "^9.0.1", "tap": "^10.3.0"}, "dependencies": {"@ahmadnassri/error": "^1.1.0", "ajv": "^4.9.1", "har-schema": "^1.0.5"}, "gitHead": "ea17fb13250fbe90c21371facafd23c9375cfefb", "_id": "har-validator@5.0.0", "_shasum": "ce709dfe2974340e9cf154f13ec6249bda712c55", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.7.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "ce709dfe2974340e9cf154f13ec6249bda712c55", "tarball": "https://registry.npmjs.org/har-validator/-/har-validator-5.0.0.tgz", "integrity": "sha512-KyUIWi8lNWssIsBR2tzCJTOMuWH/ZPUNX1yrDrERts+maYr6GdtML+zSRNUQHGokOT4wsuVV/MqQzs4rKcUQ/w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCi5wIErHG+092rrF+Wup42KaJGzICQj7DVL8L81vjoYwIgXH+sR9R6FtlibJhI1t/ybQVLpmZNYQCePGLGA4m7UVw="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/har-validator-5.0.0.tgz_1489524601965_0.05852990713901818"}, "directories": {}, "deprecated": "this library is no longer supported"}, "5.0.1": {"version": "5.0.1", "name": "har-validator", "description": "Extremely fast HTTP Archive (HAR) validator using JSON Schema", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "homepage": "https://github.com/ahmadnassri/har-validator", "repository": {"type": "git", "url": "git+https://github.com/ahmadnassri/har-validator.git"}, "license": "ISC", "main": "lib/promise.js", "keywords": ["har", "cli", "ajv", "http", "archive", "validate", "validator"], "engines": {"node": ">=4"}, "files": ["lib"], "bugs": {"url": "https://github.com/ahmadnassri/har-validator/issues"}, "scripts": {"lint": "standard && echint", "pretest": "npm run lint", "test": "tap test", "coverage": "tap test --reporter silent --coverage"}, "devDependencies": {"echint": "^4.0.1", "standard": "^9.0.1", "tap": "^10.3.0"}, "dependencies": {"ajv": "^4.9.1", "har-schema": "^1.0.5"}, "gitHead": "3ec971e1c47f889958971bf91c4327da124923a9", "_id": "har-validator@5.0.1", "_shasum": "17394849aab560bc9838a325b07eeba328a6ac0b", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.7.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "17394849aab560bc9838a325b07eeba328a6ac0b", "tarball": "https://registry.npmjs.org/har-validator/-/har-validator-5.0.1.tgz", "integrity": "sha512-d0bhWhleNAD/WDnvMNbLVian+PulH4JIzZmcxKYXQ+btm+TWxBpuR1RJJJG8z1GB2KdRVQLFwTufCwUYrO9uNQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCY3a8rc2Lkws4zp1ZlburqwS1iH1ylnR7CDVvTgvoFowIgW0+YWa+FjVXFctgfXJCrb1mGC8zISXz4x/CXoz1VeWE="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/har-validator-5.0.1.tgz_1489525993907_0.10738185956142843"}, "directories": {}, "deprecated": "this library is no longer supported"}, "5.0.2": {"version": "5.0.2", "name": "har-validator", "description": "Extremely fast HTTP Archive (HAR) validator using JSON Schema", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "homepage": "https://github.com/ahmadnassri/har-validator", "repository": {"type": "git", "url": "git+https://github.com/ahmadnassri/har-validator.git"}, "license": "ISC", "main": "lib/promise.js", "keywords": ["har", "cli", "ajv", "http", "archive", "validate", "validator"], "engines": {"node": ">=4"}, "files": ["lib"], "bugs": {"url": "https://github.com/ahmadnassri/har-validator/issues"}, "scripts": {"lint": "standard && echint", "pretest": "npm run lint", "test": "tap test", "coverage": "tap test --reporter silent --coverage"}, "devDependencies": {"echint": "^4.0.1", "standard": "^9.0.1", "tap": "^10.3.0"}, "dependencies": {"ajv": "^4.9.1", "har-schema": "^1.0.5"}, "gitHead": "be27f68b78ae2d6ae66c419d15df99b99f0a8eaf", "_id": "har-validator@5.0.2", "_shasum": "25a23c7a67465531ea9f3d3f8aebeca7d6914b5f", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.7.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "25a23c7a67465531ea9f3d3f8aebeca7d6914b5f", "tarball": "https://registry.npmjs.org/har-validator/-/har-validator-5.0.2.tgz", "integrity": "sha512-nsJwpOuPKB8qf/ZZF+ZvY3VBHaDuFNgHn4XLvDpvCC4eC9VcniYYRGC6GI5havo8c5a76qTJ5kUDJ2I5HaBxlQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICVFImMysrjekGEFE33ZfUadjQIB3rMSpYsasdpyW2ieAiAKEpBtSjoDIesA9Onl+J7gJDDjb5OZkUL4JAQG1G1fzA=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/har-validator-5.0.2.tgz_1489527271584_0.08811150095425546"}, "directories": {}, "deprecated": "this library is no longer supported"}, "5.0.3": {"version": "5.0.3", "name": "har-validator", "description": "Extremely fast HTTP Archive (HAR) validator using JSON Schema", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "homepage": "https://github.com/ahmadnassri/har-validator", "repository": {"type": "git", "url": "git+https://github.com/ahmadnassri/har-validator.git"}, "license": "ISC", "main": "lib/promise.js", "keywords": ["har", "cli", "ajv", "http", "archive", "validate", "validator"], "engines": {"node": ">=4"}, "files": ["lib"], "bugs": {"url": "https://github.com/ahmadnassri/har-validator/issues"}, "scripts": {"lint": "standard && echint", "pretest": "npm run lint", "test": "tap test", "coverage": "tap test --reporter silent --coverage"}, "devDependencies": {"echint": "^4.0.1", "standard": "^10.0.2", "tap": "^10.3.2"}, "dependencies": {"ajv": "^5.1.0", "har-schema": "^2.0.0"}, "gitHead": "254112b780849ab773dcf0245db547154fe7e248", "_id": "har-validator@5.0.3", "_shasum": "ba402c266194f15956ef15e0fcf242993f6a7dfd", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.10.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "ba402c266194f15956ef15e0fcf242993f6a7dfd", "tarball": "https://registry.npmjs.org/har-validator/-/har-validator-5.0.3.tgz", "integrity": "sha512-r7LZkP7Z6WMxj5zARzB9dSpIKu/sp0NfHIgtj6kmQXhEArNctjB5FEv/L2XfLdWqIocPT2QVt0LFOlEUioTBtQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCLw50TtgCaZDfANyuxDuy26ecponLZrF5WRIH05vuJzgIgUPu+VrYjXJwXVP2ebxaSjs+zlKfUaQhyqYgmntyU4VE="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/har-validator-5.0.3.tgz_1494776767065_0.45195462461560965"}, "directories": {}, "deprecated": "this library is no longer supported"}, "5.1.0": {"version": "5.1.0", "name": "har-validator", "description": "Extremely fast HTTP Archive (HAR) validator using JSON Schema", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "homepage": "https://github.com/ahmadnassri/har-validator", "repository": {"type": "git", "url": "git+https://github.com/ahmadnassri/har-validator.git"}, "license": "ISC", "main": "lib/promise.js", "keywords": ["har", "cli", "ajv", "http", "archive", "validate", "validator"], "engines": {"node": ">=4"}, "files": ["lib"], "bugs": {"url": "https://github.com/ahmadnassri/har-validator/issues"}, "scripts": {"lint": "standard && echint", "pretest": "npm run lint", "test": "tap test", "coverage": "tap test --reporter silent --coverage"}, "devDependencies": {"echint": "^4.0.1", "standard": "^10.0.3", "tap": "^10.7.2"}, "dependencies": {"ajv": "^5.3.0", "har-schema": "^2.0.0"}, "gitHead": "33e6dbdd7d644a1f0b419b0fa277b1bb491e0331", "_id": "har-validator@5.1.0", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-+qnmNjI4OfH2ipQ9VQOw23bBd/ibtfbVdK2fYbY4acTDqKTW/YDp9McimZdDbG8iV9fZizUqQMD5xvriB146TA==", "shasum": "44657f5688a22cfd4b72486e81b3a3fb11742c29", "tarball": "https://registry.npmjs.org/har-validator/-/har-validator-5.1.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICfgGgw2ilVBuJRC0QdOArAhbh09cj/S0cf/G1XYzJLcAiA6u0Teogh4YLrmOFqwyLJGmhlhNqp7kWjhQYlTrwJwgA=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/har-validator-5.1.0.tgz_1509688503296_0.303169913822785"}, "directories": {}, "deprecated": "this library is no longer supported"}, "5.1.3": {"version": "5.1.3", "name": "har-validator", "description": "Extremely fast HTTP Archive (HAR) validator using JSON Schema", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "homepage": "https://github.com/ahmadnassri/node-har-validator", "repository": {"type": "git", "url": "git+https://github.com/ahmadnassri/node-har-validator.git"}, "license": "MIT", "main": "lib/promise.js", "keywords": ["har", "cli", "ajv", "http", "archive", "validate", "validator"], "engines": {"node": ">=6"}, "bugs": {"url": "https://github.com/ahmadnassri/node-har-validator/issues"}, "scripts": {"lint:deps": "npx updated", "lint:ec": "npx editorconfig-checker .", "lint:js": "npx eslint .", "lint:md": "npx remark --quiet --frail .", "lint": "npx run-p lint:*", "open:coverage": "opener coverage/lcov-report/index.html", "test": "tap test --coverage-report=lcov --no-browser"}, "devDependencies": {"tap": "^12.0.1"}, "dependencies": {"ajv": "^6.5.5", "har-schema": "^2.0.0"}, "gitHead": "a38c0672cd3b202bd52534ee7da83b74003eb472", "_id": "har-validator@5.1.3", "_npmVersion": "6.4.1", "_nodeVersion": "10.12.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-sNvOCzEQNr/qrvJgc3UG/kD4QtlHycrzwS+6mfTrrSq97BvaYcPZZI1ZSqGSPR73Cxn4LKTD4PttRwfU7jWq5g==", "shasum": "1ef89ebd3e4996557675eed9893110dc350fa080", "tarball": "https://registry.npmjs.org/har-validator/-/har-validator-5.1.3.tgz", "fileCount": 6, "unpackedSize": 8226, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb53VxCRA9TVsSAnZWagAAxgcP/1BGWYGIW64yLNAW/WHN\nOSYoh6XrgKCrO61n8lQn2QwRNDkMVSynpPqBsdARLMT/jWh4H7Dobgfr1sjH\nmcw8zl+qVYIktLBmJh0wZ8Nyw<PERSON>yev+Uo2ykRFNEhJJpPMEDyvqOTDxzhYJzd\nDeUjV5TllJ5KACWkpL+vTl5i9wIO1NvUAa0CGTBMRS0FiZTZDYqFAsi9Elnu\nOIPpk0J1DUUWN3ZM2OB/T6R0Oz12GdYXWU84rqb8TgJFlM0LQiKSZ2pnpVc+\nNH5hajBY8tITbel8S4FS3CrUGUzCBUIN2+bRddLiRSaLRQqYXW1tEvWU+IXS\nTv5VTChKnpPIzIclJPhrxpv7CdlCVNXjnFLrLrVeFprJXsDmU4gfwIdqD6bO\neSOnUrh9HVJqYgqMhhSOmoZHZTwM7EYLfLmmo6Ai+4jFEN7gZObr1MEdrIbI\ntqhBH4IMvmXqqpplDNAFkI4HvcG/lujIeKkgFgnvPwgZssE848Uh+nx2Su9M\ngYQnci6fbaPLO+qL5FIR+2Xlzp8rNs9QPsqxukQUcake2XoQ0Est6XCzojFi\nwMuNjpltWX9prRpttiJ/k+XLO0uvYt2AGl+4U4Q7RWquospACW9XDIIsL3Af\nHUdilD8jf2cQEmfZTHJ1Qr809sdKki4xdBYgsne08DnnuBjdfiAFh8pQp1II\nlF7b\r\n=cKta\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD+FnO3KtBVp0gegZvy01U49O7EUrLr/BJJZeRsMOfMKwIhALL0FOClwt7O8KUBsnTa/+3jSY8N7soccfZD6gOs3Ym8"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/har-validator_5.1.3_1541895536289_0.3289158818028399"}, "_hasShrinkwrap": false, "deprecated": "this library is no longer supported"}, "5.1.4": {"version": "5.1.4", "name": "har-validator", "description": "Extremely fast HTTP Archive (HAR) validator using JSON Schema", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "homepage": "https://github.com/ahmadnassri/node-har-validator", "repository": {"type": "git", "url": "git+https://github.com/ahmadnassri/node-har-validator.git"}, "license": "MIT", "main": "lib/promise.js", "keywords": ["har", "cli", "ajv", "http", "archive", "validate", "validator"], "engines": {"node": ">=6"}, "bugs": {"url": "https://github.com/ahmadnassri/node-har-validator/issues"}, "scripts": {"lint": "npx run-p lint:*", "test": "tap test --no-coverage", "test:coverage": "tap test --coverage-report=lcov --no-browser"}, "devDependencies": {"tap": "^14.10.8"}, "dependencies": {"ajv": "^6.12.3", "har-schema": "^2.0.0"}, "gitHead": "01c5f2a8309b30cab9f71664e42176b8ed301be2", "_id": "har-validator@5.1.4", "_nodeVersion": "12.18.3", "_npmVersion": "6.14.7", "dist": {"integrity": "sha512-NdlLHC9WZRlxP1Hqzzl3wLR5oI8mDuISvHCNu4vfPadiXdnL71TBs0AwXNVmanxdyItmv81aYl3IKBCiuYZSfA==", "shasum": "8bfb8fcca072589d492eb2d947df22fd35b18fbf", "tarball": "https://registry.npmjs.org/har-validator/-/har-validator-5.1.4.tgz", "fileCount": 6, "unpackedSize": 8216, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfIkwzCRA9TVsSAnZWagAAwQsP/03lwolRc9GDC6XyQNxz\nD1/pISkgO66ge7QednqnBrQMwnFwcLFI2+IlqRfx8QxvOTeR797WFhGDL06D\nmuXYsZndIw5kuUjSsxGR/KQSdnuTGwzvFhLToWwmbsIMk0ko3M1y0wXJPYWy\nbyIYXWW5y6x13lcK1PzoAQh2YAtOquXTJQFQfS0XusjzPrtN9AxxPs0GZh+Y\nppNeYY+D9oSWxN3g3yR7re5lNLil9dKY975zWAX00HYTR19hbgZBkl8vzvPX\nnZoi9hc1X93HGawtGJYse6f3ebRU1ulXucafMoDXjnrX976qbvSVw8YLeRj/\nqonk1g1W1G4lrl/xZ9TU70F0TZEK52oeiryNAYHnUboDfZQQlgsr57Km1RId\nSIklRbkSvAYVIO4NdLpIDmLhOpiqJYttL+hghPvjsCjVxxOkNH3zNvbD5VBM\n4WRWQPoqLE4jKAQAKf43Lorh9lH7mQdanIG2kxfenNn7ZKzbnInASXeZ+tt4\nUVtNtLyWeW1eZnkLypZxN1JGRhsDIxZTGNSKB7kxbkJ1zMdx8hjHR0GD1SXb\nQgHWH75cbn1zgnXLrrMslBBXFikZ+m1Oc03+H5mXekd5PdQ2/JC1Cf8otaiK\nYx+0xTP7vKhtG7YWl/QYdpLQ9nRfqa8NoBby+rwjsFrXrlhluaKBEAnnsUIx\nqlLj\r\n=6xtG\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICOsfEedvmk0/Sd17GKLwID/AQ9criW6OGN0NQ7rYOLIAiEAuKvmUIavRkt5e0J+n0dspelNxJX+o9xsfRR6hM0TZH0="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/har-validator_5.1.4_1596083250831_0.8808725066506475"}, "_hasShrinkwrap": false, "deprecated": "this library is no longer supported"}, "5.1.5": {"version": "5.1.5", "name": "har-validator", "description": "Extremely fast HTTP Archive (HAR) validator using JSON Schema", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "homepage": "https://github.com/ahmadnassri/node-har-validator", "repository": {"type": "git", "url": "git+https://github.com/ahmadnassri/node-har-validator.git"}, "license": "MIT", "main": "lib/promise.js", "keywords": ["har", "cli", "ajv", "http", "archive", "validate", "validator"], "engines": {"node": ">=6"}, "bugs": {"url": "https://github.com/ahmadnassri/node-har-validator/issues"}, "scripts": {"lint": "npx run-p lint:*", "test": "tap test --no-coverage", "test:coverage": "tap test --coverage-report=lcov --no-browser"}, "devDependencies": {"tap": "^14.10.8"}, "dependencies": {"ajv": "^6.12.3", "har-schema": "^2.0.0"}, "gitHead": "b77cdcbaf3443af59bc7dc8ef2cffb437201134e", "_id": "har-validator@5.1.5", "_nodeVersion": "12.18.3", "_npmVersion": "6.14.7", "dist": {"integrity": "sha512-nmT2T0lljbxdQZfspsno9hgrG3Uir6Ks5afism62poxqBM6sDnMEuPmzTq8XN0OEwqKLLdh1jQI3qyE66Nzb3w==", "shasum": "1f0803b9f8cb20c0fa13822df1ecddb36bde1efd", "tarball": "https://registry.npmjs.org/har-validator/-/har-validator-5.1.5.tgz", "fileCount": 6, "unpackedSize": 8220, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfIk+5CRA9TVsSAnZWagAA1uQP/AkJKwh/PLlwGTa8LqSE\nBEWHOJ4VrA2l+VHRxXwrew+notdLhbXqtveFOONbXfE0AjhO+26b26urs/Ob\na3wGFw0RrYzdETqZcSaWWtoASbHxVWEJvaNVXYl5hyCdhajxOKWY7mjc0cxJ\nH4Xv3Z+b46d1qcMPZIe69rQlfAuvE3qB83ZPx0tinN79hx3KSb5st+FxZNHF\n6URW9Lqa6MAhvsOqq5f+JCl5rJhneRsIewlP7EO6xasu9a58/mbbnlrv3xkr\nHKxRsKLHAvLyAewuCq4xwp6Z+RpUEOe0PLZbkXZ+d5wXeU4qoHqFbivX81Qg\nNMRuyHtE8lFOIoTX31VesjcINtP8hcTz3Ly06rs8aRAs23hcQDUkIvB0abgJ\n2iqXleCA0zrrz3DAHQCcqizSYvQdCVOB1ixcfmLm/T///US2rXymMhKy3UPE\njoA2UdkKd2Np8UgvW7FEY27BUZSewk8iSyHFVjXCmIVlNRXhtJ2qHakH/Iok\n2HSWUMcaqy74POJEwW+gqyU/dXx5RrqFtkr/wshko1SRxnoJ1YaW7zD+hvaq\nPrm7PIDHgjKyUI5Iz/fjChCU7aoahAH/vkCZqwseqsU34ZQsWa0oRGqCa9Zd\n/7WVCYf+6H2abd+PquG99hnR2/tjJc8u0B+yoKzVIe/7HiQ4cJ6SLUxNNRPV\nKOXz\r\n=VNMH\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDB1C/0oVCB8/gXAchQ30dDEmw8WdA275qZXTQASQqeugIhANqNyf2N2D93lZqS4NomSg3t/IkCxKia7ZaFXDcvKZQk"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/har-validator_5.1.5_1596084153522_0.5429455618188734"}, "_hasShrinkwrap": false, "deprecated": "this library is no longer supported"}}, "readme": "# HAR Validator\n\n[![license][license-img]][license-url]\n[![version][npm-img]][npm-url]\n[![super linter][super-linter-img]][super-linter-url]\n[![test][test-img]][test-url]\n[![release][release-img]][release-url]\n\n[license-url]: LICENSE\n[license-img]: https://badgen.net/github/license/ahmadnassri/node-har-validator\n\n[npm-url]: https://www.npmjs.com/package/har-validator\n[npm-img]: https://badgen.net/npm/v/har-validator\n\n[super-linter-url]: https://github.com/ahmadnassri/node-har-validator/actions?query=workflow%3Asuper-linter\n[super-linter-img]: https://github.com/ahmadnassri/node-har-validator/workflows/super-linter/badge.svg\n\n[test-url]: https://github.com/ahmadnassri/node-har-validator/actions?query=workflow%3Atest\n[test-img]: https://github.com/ahmadnassri/node-har-validator/workflows/test/badge.svg\n\n[release-url]: https://github.com/ahmadnassri/node-har-validator/actions?query=workflow%3Arelease\n[release-img]: https://github.com/ahmadnassri/node-har-validator/workflows/release/badge.svg\n\n> Extremely fast HTTP Archive ([HAR](https://github.com/ahmadnassri/har-spec/blob/master/versions/1.2.md)) validator using JSON Schema.\n\n## Install\n\n```bash\nnpm install har-validator\n```\n\n## CLI Usage\n\nPlease refer to [`har-cli`](https://github.com/ahmadnassri/har-cli) for more info.\n\n## API\n\n**Note**: as of [`v2.0.0`](https://github.com/ahmadnassri/node-har-validator/releases/tag/v2.0.0) this module defaults to Promise based API.\n_For backward compatibility with `v1.x` an [async/callback API](docs/async.md) is also provided_\n\n- [async API](docs/async.md)\n- [callback API](docs/async.md)\n- [Promise API](docs/promise.md) _(default)_\n", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2022-06-18T19:19:46.633Z", "created": "2015-02-10T09:34:51.208Z", "1.0.0": "2015-02-10T09:34:51.208Z", "1.0.1": "2015-03-04T21:10:19.824Z", "1.0.2": "2015-03-04T21:41:18.650Z", "1.1.0": "2015-03-05T17:09:47.634Z", "1.1.1": "2015-03-05T17:19:39.368Z", "1.1.2": "2015-03-05T17:22:43.677Z", "1.1.3": "2015-03-21T07:05:36.574Z", "1.2.0": "2015-03-21T08:09:56.553Z", "1.3.0": "2015-03-22T05:45:54.179Z", "1.3.1": "2015-03-22T05:56:34.528Z", "1.4.0": "2015-03-22T05:59:49.920Z", "1.5.0": "2015-03-30T03:31:22.173Z", "1.5.1": "2015-03-30T03:48:16.233Z", "1.6.0": "2015-04-02T06:31:38.556Z", "1.6.1": "2015-04-02T07:48:31.380Z", "1.7.0": "2015-04-30T04:01:06.241Z", "1.7.1": "2015-05-27T09:16:55.401Z", "1.8.0": "2015-06-22T13:59:06.322Z", "2.0.0": "2015-10-03T20:36:43.750Z", "2.0.1": "2015-10-03T20:56:34.699Z", "2.0.2": "2015-10-03T22:58:44.305Z", "2.0.3": "2015-11-24T13:59:09.905Z", "2.0.4": "2016-01-14T06:50:03.933Z", "2.0.5": "2016-01-14T06:59:23.497Z", "2.0.6": "2016-01-19T23:18:30.950Z", "2.1.0": "2016-03-08T00:32:44.494Z", "2.1.1": "2016-03-08T00:43:19.796Z", "2.1.2": "2016-04-16T04:05:39.404Z", "2.1.3": "2016-04-16T04:15:21.260Z", "3.0.0": "2016-12-02T23:30:29.307Z", "3.1.0": "2016-12-03T19:24:47.829Z", "3.2.0": "2016-12-03T19:31:11.165Z", "3.3.0": "2016-12-03T22:02:25.049Z", "3.3.1": "2016-12-03T22:53:10.083Z", "3.4.0": "2016-12-04T00:39:58.691Z", "4.0.0": "2016-12-04T00:46:38.983Z", "4.0.1": "2016-12-04T01:21:02.883Z", "4.0.2": "2016-12-04T05:38:13.182Z", "4.0.3": "2016-12-04T05:56:32.120Z", "4.0.4": "2016-12-04T06:54:25.245Z", "4.1.0": "2016-12-04T08:24:24.491Z", "4.1.1": "2016-12-04T19:33:57.708Z", "4.1.2": "2016-12-04T19:56:05.064Z", "4.2.0": "2016-12-04T20:12:12.333Z", "4.2.1": "2017-03-04T14:09:00.472Z", "5.0.0": "2017-03-14T20:50:02.584Z", "5.0.1": "2017-03-14T21:13:14.475Z", "5.0.2": "2017-03-14T21:34:33.483Z", "5.0.3": "2017-05-14T15:46:08.016Z", "5.1.0": "2017-11-03T05:55:04.210Z", "5.1.1": "2018-11-07T02:02:25.938Z", "5.1.2": "2018-11-07T02:04:55.963Z", "5.1.3": "2018-11-11T00:18:56.399Z", "5.1.4": "2020-07-30T04:27:30.951Z", "5.1.5": "2020-07-30T04:42:33.615Z"}, "homepage": "https://github.com/ahmadnassri/node-har-validator", "keywords": ["har", "cli", "ajv", "http", "archive", "validate", "validator"], "repository": {"type": "git", "url": "git+https://github.com/ahmadnassri/node-har-validator.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "bugs": {"url": "https://github.com/ahmadnassri/node-har-validator/issues"}, "license": "MIT", "readmeFilename": "README.md", "users": {"ahmadnassri": true, "neodon1014": true, "mojaray2k": true}}