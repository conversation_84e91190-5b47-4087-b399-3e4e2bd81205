{"_id": "undefsafe", "_rev": "19-beb54c4ca1060dcf0ad8c24b5af1cb44", "name": "undefsafe", "description": "Undefined safe way of extracting object properties", "dist-tags": {"latest": "2.0.5"}, "versions": {"0.0.1": {"name": "undefsafe", "version": "0.0.1", "description": "Undefined safe way of extracting object properties", "main": "lib/undefsafe.js", "directories": {"test": "test"}, "scripts": {"test": "node_modules/mocha/bin/_mocha --ui bdd test/**/*.test.js"}, "keywords": ["undefined"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT / http://rem.mit-license.org", "devDependencies": {"mocha": "~1.16.2"}, "_id": "undefsafe@0.0.1", "dist": {"shasum": "774beb0d2df44f09d4a68fb36e9cf5f995834440", "tarball": "https://registry.npmjs.org/undefsafe/-/undefsafe-0.0.1.tgz", "integrity": "sha512-bA41LtX98CkYpFSydSoJykuaycXN4wdICI9Z3f1+XsZj9XJ29zv98tDm7vik99oGV4q75ljbtF4XKfBMO4U2xw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCTTzzjU1O6RssIDxl/+vnIUo/UnHCPKIuhGn76K8VotQIhAL5aJrIQVkyNJ4N5vJ8e+T9fESyz7UTr8cEsOdJl/Dbd"}]}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "remy", "email": "<EMAIL>"}, "maintainers": [{"name": "remy", "email": "<EMAIL>"}]}, "0.0.2": {"name": "undefsafe", "version": "0.0.2", "description": "Undefined safe way of extracting object properties", "main": "lib/undefsafe.js", "directories": {"test": "test"}, "scripts": {"test": "node_modules/mocha/bin/_mocha --ui bdd test/**/*.test.js"}, "repository": {"type": "git", "url": "git://github.com/remy/undefsafe.git"}, "keywords": ["undefined"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT / http://rem.mit-license.org", "devDependencies": {"mocha": "~1.16.2"}, "bugs": {"url": "https://github.com/remy/undefsafe/issues"}, "homepage": "https://github.com/remy/undefsafe", "_id": "undefsafe@0.0.2", "dist": {"shasum": "ef37873e9962724f523861c051cd381067711319", "tarball": "https://registry.npmjs.org/undefsafe/-/undefsafe-0.0.2.tgz", "integrity": "sha512-OWKnlTuLY0GM2+bCjrKJZ/GFJvNE75mQYVLvYeroxN1auzAMAl/d8JYVLXXx2AO0r2Wc/Njro566+4eTbUIlCw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC+F+T7cK8CPLZXTUFd/XE0fxEeNjIU7Cw2/7awDh/BZgIhAOQfyhZxhIqy4nbGguLIzRvF/h05UX5ibQQvElqpSxie"}]}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "remy", "email": "<EMAIL>"}, "maintainers": [{"name": "remy", "email": "<EMAIL>"}]}, "0.0.3": {"name": "undefsafe", "version": "0.0.3", "description": "Undefined safe way of extracting object properties", "main": "lib/undefsafe.js", "directories": {"test": "test"}, "scripts": {"test": "node_modules/mocha/bin/_mocha --ui bdd test/**/*.test.js"}, "repository": {"type": "git", "url": "git://github.com/remy/undefsafe.git"}, "keywords": ["undefined"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT / http://rem.mit-license.org", "devDependencies": {"mocha": "~1.16.2"}, "gitHead": "435db2701b5ddccc0d575c669df8bdc96918889b", "bugs": {"url": "https://github.com/remy/undefsafe/issues"}, "homepage": "https://github.com/remy/undefsafe", "_id": "undefsafe@0.0.3", "_shasum": "ecca3a03e56b9af17385baac812ac83b994a962f", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "remy", "email": "<EMAIL>"}, "maintainers": [{"name": "remy", "email": "<EMAIL>"}], "dist": {"shasum": "ecca3a03e56b9af17385baac812ac83b994a962f", "tarball": "https://registry.npmjs.org/undefsafe/-/undefsafe-0.0.3.tgz", "integrity": "sha512-mISmZCZVGRI3y3dxtKcOhlfK46hFVleOUTVTRT8BLByUJ3ioLlQ/Nkda54ndIYBJ53Hga3T/FazHXxUw7Pbtew==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFJs3xyNNFLkwABsIsU4HG2YOSnXbQiFVdp4YK8pV46wAiEAvwNvR0R8Au80AJr3cHgdj7gywwBj0BbaWo/2kcafHMI="}]}}, "1.0.0": {"name": "undefsafe", "version": "1.0.0", "description": "Undefined safe way of extracting object properties", "main": "lib/undefsafe.js", "tonicExampleFilename": "example.js", "directories": {"test": "test"}, "scripts": {"test": "node_modules/mocha/bin/_mocha --ui bdd test/**/*.test.js"}, "repository": {"type": "git", "url": "git://github.com/remy/undefsafe.git"}, "keywords": ["undefined"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT / http://rem.mit-license.org", "devDependencies": {"mocha": "~1.16.2"}, "gitHead": "951081b1557c100545fc2e598e42c736b6a6d1fd", "bugs": {"url": "https://github.com/remy/undefsafe/issues"}, "homepage": "https://github.com/remy/undefsafe#readme", "_id": "undefsafe@1.0.0", "_shasum": "1394c2524040f3e88031b04e7f6ba6a25e0d8065", "_from": ".", "_npmVersion": "2.14.12", "_nodeVersion": "4.2.4", "_npmUser": {"name": "remy", "email": "<EMAIL>"}, "dist": {"shasum": "1394c2524040f3e88031b04e7f6ba6a25e0d8065", "tarball": "https://registry.npmjs.org/undefsafe/-/undefsafe-1.0.0.tgz", "integrity": "sha512-7V+6qeue57hcWpKYbKfwopO6ub2aYNft4jUv2dfocBDjs+PTXEGiu1wkLDtQMMSZIDAw1uZHQtGsgBr6/Sncbw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCL9qB2/RJIrzqUQN82SRMEEeFTkmEiDlU2i1Bg4+MjVwIgSx5GiftPAuZFoV1lf9ctblYsoC38VzRhzx7BoC/5+cA="}]}, "maintainers": [{"name": "remy", "email": "<EMAIL>"}]}, "1.1.0": {"name": "undefsafe", "description": "Undefined safe way of extracting object properties", "main": "lib/undefsafe.js", "tonicExampleFilename": "example.js", "directories": {"test": "test"}, "scripts": {"test": "tap test/**/*.test.js -R spec", "cover": "tap test/*.test.js --cov --coverage-report=lcov", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "repository": {"type": "git", "url": "git+https://github.com/remy/undefsafe.git"}, "keywords": ["undefined"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"semantic-release": "^4.3.5", "tap": "^5.7.1"}, "version": "1.1.0", "gitHead": "aab63c28b2610d4878fa4b74aea6b87d4bca207c", "bugs": {"url": "https://github.com/remy/undefsafe/issues"}, "homepage": "https://github.com/remy/undefsafe#readme", "_id": "undefsafe@1.1.0", "_shasum": "4f74b1967cb775b096db99dc096a86ec1d28acbd", "_from": ".", "_npmVersion": "2.15.3", "_nodeVersion": "4.4.3", "_npmUser": {"name": "remy", "email": "<EMAIL>"}, "dist": {"shasum": "4f74b1967cb775b096db99dc096a86ec1d28acbd", "tarball": "https://registry.npmjs.org/undefsafe/-/undefsafe-1.1.0.tgz", "integrity": "sha512-5QI1FQcZdC9cfU6VeCKdo+Lz5S5p3ZmjJ6JoAG3cJ69G9wLG/MvT1S7K1DAS8dgoVTcynsKV1OKK0D4Ikez/tQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC025WykBKO0gNlFpjVHXuhp6rCwVm0aPfG3F2B0E8XTAIhAOoPzWrJp6dmBzZ+jbbdYz6g/SbhhdMb+wcsLj1gMAi9"}]}, "maintainers": [{"name": "remy", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/undefsafe-1.1.0.tgz_1460581064225_0.9182229312136769"}}, "1.2.0": {"name": "undefsafe", "description": "Undefined safe way of extracting object properties", "main": "lib/undefsafe.js", "tonicExampleFilename": "example.js", "directories": {"test": "test"}, "scripts": {"test": "tap test/**/*.test.js -R spec", "cover": "tap test/*.test.js --cov --coverage-report=lcov", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "repository": {"type": "git", "url": "git+https://github.com/remy/undefsafe.git"}, "keywords": ["undefined"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"semantic-release": "^4.3.5", "tap": "^5.7.1", "tap-only": "0.0.5"}, "dependencies": {"debug": "^2.2.0"}, "version": "1.2.0", "gitHead": "a8ec59cef4fbafd0afe8fe4803fc7701ef5fe146", "bugs": {"url": "https://github.com/remy/undefsafe/issues"}, "homepage": "https://github.com/remy/undefsafe#readme", "_id": "undefsafe@1.2.0", "_shasum": "e6888286cbb15340cabe20118df9b8db531351a4", "_from": ".", "_npmVersion": "2.15.10", "_nodeVersion": "4.5.0", "_npmUser": {"name": "remy", "email": "<EMAIL>"}, "dist": {"shasum": "e6888286cbb15340cabe20118df9b8db531351a4", "tarball": "https://registry.npmjs.org/undefsafe/-/undefsafe-1.2.0.tgz", "integrity": "sha512-Mu1zfGEhacsRTgoZDDoG0qd2jFlCDfbu9QlpAWy0kJ03I4YPUL8sQU2pSCCG+xzD3zNbCPtFr2toU3KT8zIEQg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEgu+usFu7GOuxb9/XKcw9Z/GBHQPyBNEwWPnJn8l3moAiB+MMPU5bF/k1FcC/fHNHJbWF4sA4FHzh5IzRhcfgp4PQ=="}]}, "maintainers": [{"name": "remy", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/undefsafe-1.2.0.tgz_1471977754816_0.644012144068256"}}, "1.3.0": {"name": "undefsafe", "description": "Undefined safe way of extracting object properties", "main": "lib/undefsafe.js", "tonicExampleFilename": "example.js", "directories": {"test": "test"}, "scripts": {"test": "tap test/**/*.test.js -R spec", "cover": "tap test/*.test.js --cov --coverage-report=lcov", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "repository": {"type": "git", "url": "git+https://github.com/remy/undefsafe.git"}, "keywords": ["undefined"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"semantic-release": "^4.3.5", "tap": "^5.7.1", "tap-only": "0.0.5"}, "dependencies": {"debug": "^2.2.0"}, "version": "1.3.0", "gitHead": "f6a73696fa00d6aa73c0fb0557725b94a266e57d", "bugs": {"url": "https://github.com/remy/undefsafe/issues"}, "homepage": "https://github.com/remy/undefsafe#readme", "_id": "undefsafe@1.3.0", "_shasum": "fecdc37e3970986f88c6b885178b3a433faac0de", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.7.3", "_npmUser": {"name": "remy", "email": "<EMAIL>"}, "dist": {"shasum": "fecdc37e3970986f88c6b885178b3a433faac0de", "tarball": "https://registry.npmjs.org/undefsafe/-/undefsafe-1.3.0.tgz", "integrity": "sha512-s8Cqc/MoZwnYP04dbP0DcFQdWkvDWSP8IiswiCgPp/NQp13I33bWCE8U42VpqVGKfJCbKvrciY9uuiwOEO31iw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFa8AC7kmnctDSW3sPFnwpQWxxFlYtquejCLBTYA4LMwAiAJiyMUGy4NkPupPuMwmxSm1is49X5EtKC6lNViCuWlOg=="}]}, "maintainers": [{"name": "remy", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/undefsafe-1.3.0.tgz_1486571184904_0.12533880630508065"}}, "1.3.1": {"name": "undefsafe", "description": "Undefined safe way of extracting object properties", "main": "lib/undefsafe.js", "tonicExampleFilename": "example.js", "directories": {"test": "test"}, "scripts": {"test": "tap test/**/*.test.js -R spec", "cover": "tap test/*.test.js --cov --coverage-report=lcov", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "repository": {"type": "git", "url": "git+https://github.com/remy/undefsafe.git"}, "keywords": ["undefined"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"semantic-release": "^4.3.5", "tap": "^5.7.1", "tap-only": "0.0.5"}, "dependencies": {"debug": "^2.2.0"}, "version": "1.3.1", "gitHead": "9c7867e6dc75ee8842b4bbfdfeba9e3ccf3bc616", "bugs": {"url": "https://github.com/remy/undefsafe/issues"}, "homepage": "https://github.com/remy/undefsafe#readme", "_id": "undefsafe@1.3.1", "_shasum": "168853130e15d57a5842a294bd0f203f1dd734ac", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.7.3", "_npmUser": {"name": "remy", "email": "<EMAIL>"}, "dist": {"shasum": "168853130e15d57a5842a294bd0f203f1dd734ac", "tarball": "https://registry.npmjs.org/undefsafe/-/undefsafe-1.3.1.tgz", "integrity": "sha512-HQ9x/t39f+rK7TcACYxqc6/LjrpXgbb15njOeJa3LyZCniaH3ueRyne22Ys4U5hBPrTsRdh4RuoOVUk1+CRF9w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCoOBbOfZFTa1jAGbF+wB3Dxer+R5wmn6aBBSB211gDLgIhAINZlVJIVypNR0mNufkG/TQzRuOMnxahoIt1h+EU4qYD"}]}, "maintainers": [{"name": "remy", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/undefsafe-1.3.1.tgz_1486572957907_0.8126757687423378"}}, "2.0.0": {"name": "undefsafe", "description": "Undefined safe way of extracting object properties", "main": "lib/undefsafe.js", "tonicExampleFilename": "example.js", "directories": {"test": "test"}, "scripts": {"test": "tap test/**/*.test.js -R spec", "cover": "tap test/*.test.js --cov --coverage-report=lcov", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "repository": {"type": "git", "url": "git+https://github.com/remy/undefsafe.git"}, "keywords": ["undefined"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"semantic-release": "^4.3.5", "tap": "^5.7.1", "tap-only": "0.0.5"}, "dependencies": {"debug": "^2.2.0"}, "version": "2.0.0", "gitHead": "2d38e723b7a10a9d6944678bef056a5b1b132f6d", "bugs": {"url": "https://github.com/remy/undefsafe/issues"}, "homepage": "https://github.com/remy/undefsafe#readme", "_id": "undefsafe@2.0.0", "_shasum": "b4e83bd3d50587e1f736c61dd2d4ec24b7e495b4", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.7.3", "_npmUser": {"name": "remy", "email": "<EMAIL>"}, "dist": {"shasum": "b4e83bd3d50587e1f736c61dd2d4ec24b7e495b4", "tarball": "https://registry.npmjs.org/undefsafe/-/undefsafe-2.0.0.tgz", "integrity": "sha512-TMbUfE45G5+e9jUlXqsBh1mUqIMpKi8Mj3RFiQHJSwqTOGjBdQEVSSVbb24NXcP9hhTTyETG108qo+5SK8hCTQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDZ8t/+/FEcq/RM1WwKrBTMQmWbW6dIaVl4plLBFxdXJQIhAL8J1eRF87sStAOhHtOiIO3M1qjy0wh8JiN3Urle1XmC"}]}, "maintainers": [{"name": "remy", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/undefsafe-2.0.0.tgz_1486909862847_0.5750529782380909"}}, "2.0.1": {"name": "undefsafe", "description": "Undefined safe way of extracting object properties", "main": "lib/undefsafe.js", "tonicExampleFilename": "example.js", "directories": {"test": "test"}, "scripts": {"test": "tap test/**/*.test.js -R spec", "cover": "tap test/*.test.js --cov --coverage-report=lcov", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "repository": {"type": "git", "url": "git+https://github.com/remy/undefsafe.git"}, "keywords": ["undefined"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"semantic-release": "^4.3.5", "tap": "^5.7.1", "tap-only": "0.0.5"}, "dependencies": {"debug": "^2.2.0"}, "version": "2.0.1", "gitHead": "29c8d32ee4e8780b81b6964a54e2b049194f4670", "bugs": {"url": "https://github.com/remy/undefsafe/issues"}, "homepage": "https://github.com/remy/undefsafe#readme", "_id": "undefsafe@2.0.1", "_shasum": "03b2f2a16c94556e14b2edef326cd66aaf82707a", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.7.3", "_npmUser": {"name": "remy", "email": "<EMAIL>"}, "dist": {"shasum": "03b2f2a16c94556e14b2edef326cd66aaf82707a", "tarball": "https://registry.npmjs.org/undefsafe/-/undefsafe-2.0.1.tgz", "integrity": "sha512-B+RNYGMqBr/BEbcfu5IX7zGRspWI5W7C1Y28MUDdkOS675Inhus3Sfvh0YJIfqOuZHVZ1N3r2HAgx8321cpfyw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCPBa4YvOORCbG+xkKcZ/kfsg3ghUlZnxlewMOiPkclTQIhALWNYozk8emKiROWodewruqTXTsVLtD+iY0djQI2lWcp"}]}, "maintainers": [{"name": "remy", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/undefsafe-2.0.1.tgz_1487686936366_0.436940515646711"}}, "2.0.2": {"name": "undefsafe", "description": "Undefined safe way of extracting object properties", "main": "lib/undefsafe.js", "tonicExampleFilename": "example.js", "directories": {"test": "test"}, "scripts": {"test": "tap test/**/*.test.js -R spec", "cover": "tap test/*.test.js --cov --coverage-report=lcov", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "repository": {"type": "git", "url": "git+https://github.com/remy/undefsafe.git"}, "keywords": ["undefined"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"semantic-release": "^4.3.5", "tap": "^5.7.1", "tap-only": "0.0.5"}, "dependencies": {"debug": "^2.2.0"}, "version": "2.0.2", "gitHead": "e4180bac3c9d4571dc542eca7d53b4f14c8603fe", "bugs": {"url": "https://github.com/remy/undefsafe/issues"}, "homepage": "https://github.com/remy/undefsafe#readme", "_id": "undefsafe@2.0.2", "_shasum": "225f6b9e0337663e0d8e7cfd686fc2836ccace76", "_from": ".", "_npmVersion": "2.15.12", "_nodeVersion": "4.8.7", "_npmUser": {"name": "remy", "email": "<EMAIL>"}, "dist": {"shasum": "225f6b9e0337663e0d8e7cfd686fc2836ccace76", "tarball": "https://registry.npmjs.org/undefsafe/-/undefsafe-2.0.2.tgz", "fileCount": 9, "unpackedSize": 6895, "integrity": "sha512-uIrN/fvxlTSlhHPrsbsqiaUNHx1ze1OkgM+vbWO4gFrS5J5RzD7+rVlbeGjYD3VXVaUaCUfbkHVftUUUB7iKSQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEBZFWCrfECgoA+Tz1jiKvEwlPyQwDkurO2do5M9pZY6AiEAhrKxva98z1fgAA1rGLB2GMV3igmzx1XqNVaLMLxdlIw="}]}, "maintainers": [{"name": "remy", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/undefsafe_2.0.2_1518681535273_0.5962650788047354"}, "_hasShrinkwrap": false}, "2.0.3": {"name": "undefsafe", "description": "Undefined safe way of extracting object properties", "main": "lib/undefsafe.js", "tonicExampleFilename": "example.js", "directories": {"test": "test"}, "scripts": {"test": "tap test/**/*.test.js -R spec", "cover": "tap test/*.test.js --cov --coverage-report=lcov", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "prettier": {"trailingComma": "none", "singleQuote": true}, "repository": {"type": "git", "url": "git+https://github.com/remy/undefsafe.git"}, "keywords": ["undefined"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"semantic-release": "^4.3.5", "tap": "^5.7.1", "tap-only": "0.0.5"}, "dependencies": {"debug": "^2.2.0"}, "version": "2.0.3", "gitHead": "f272681b3a50e2c4cbb6a8533795e1453382c822", "bugs": {"url": "https://github.com/remy/undefsafe/issues"}, "homepage": "https://github.com/remy/undefsafe#readme", "_id": "undefsafe@2.0.3", "_shasum": "6b166e7094ad46313b2202da7ecc2cd7cc6e7aae", "_from": ".", "_npmVersion": "2.15.12", "_nodeVersion": "4.9.1", "_npmUser": {"name": "remy", "email": "<EMAIL>"}, "dist": {"shasum": "6b166e7094ad46313b2202da7ecc2cd7cc6e7aae", "tarball": "https://registry.npmjs.org/undefsafe/-/undefsafe-2.0.3.tgz", "integrity": "sha512-nrXZwwXrD/T/JXeygJqdCO6NZZ1L66HrxM/Z7mIq2oPanoN0F1nLx3lwJMu6AwJY69hdixaFQOuoYsMjE5/C2A==", "fileCount": 9, "unpackedSize": 7135, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeSuHbCRA9TVsSAnZWagAAV9QP/jMvGaKPHBVUx6a4uwIb\nMO659i0Ay08V8pZymM5NDJiH+YQG+k7AkBbUXA/OzNuR4eiwfVE3ndiHnC3T\nTHZeUjsuFspzP+hTdTv393FVMtpgJdjPUsrxy5ejNCXGMvVg5KshgtiuWbDm\n6NfajFJTtSNimUhcM7m001Z+XJzThzKipYUSRh4OBzff83pa0lozNzaiA0kb\nQjfkw6jK8Pr6pwN0cCCWNJQmXCIWfkUwZUGfw9Wvw4gaGVXY/kWe7/vWK+zl\nAlDaYWcFMkvvsdqXDE1lfdfARjQABSfiiPoWHT7ts3UxVG/QCY4dnQrKf3Xp\n0jah5EIiFvqDDvnIqzeqpffIBf6GPBfOL8JruLtDzEvanZWh6I+0PJREEEF8\nqVV0/jKR9IVqEe/TozIJw9yrIFMXYRj/MCwDM+SrvqWZT1tFdOaEm5K3EsDp\nGp/0F1DXWsLbePq7zyM78ZTSSuuhbO3NnfzVFCO9ndcbvRBuieoGZWYX6TtT\nbbJACxRXJSICXQeyCIdnLnthfKDi2+GADiuiPo/BqkXp+PYTMElsu9h1kjML\nNzx7UEMNIndruFtkfobUNctJL4cNd+Q4WXbIhmMsJXuvLWKmQ3HQuRZAi0GO\nD7F8bkAM/5PoNrV2KwyyicfDjcoE5nsP5AgAWIGOsryA6K5ctnVOA2rm2tmH\nOZyh\r\n=m2Z+\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEcHs2XY6ytWenv+4mAB5zQmn+GaJ8y2oFuSjGXcjr6WAiEApHZPdhmudSjuR8rCBk6ZKa8ts+56wkxDq8j4dAmff+0="}]}, "maintainers": [{"name": "remy", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/undefsafe_2.0.3_1581965786600_0.9598020755232821"}, "_hasShrinkwrap": false}, "2.0.5": {"name": "undefsafe", "description": "Undefined safe way of extracting object properties", "main": "lib/undefsafe.js", "tonicExampleFilename": "example.js", "directories": {"test": "test"}, "scripts": {"test": "tap test/**/*.test.js -R spec", "cover": "tap test/*.test.js --cov --coverage-report=lcov", "semantic-release": "semantic-release"}, "prettier": {"trailingComma": "none", "singleQuote": true}, "repository": {"type": "git", "url": "git+https://github.com/remy/undefsafe.git"}, "keywords": ["undefined"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"semantic-release": "^18.0.0", "tap": "^5.7.1", "tap-only": "0.0.5"}, "dependencies": {}, "version": "2.0.5", "gitHead": "5d3fa3460e6716ed85dd3d41f4973fcd0896493d", "bugs": {"url": "https://github.com/remy/undefsafe/issues"}, "homepage": "https://github.com/remy/undefsafe#readme", "_id": "undefsafe@2.0.5", "_nodeVersion": "16.11.1", "_npmVersion": "7.24.2", "dist": {"integrity": "sha512-WxONCrssBM8TSPRqN5EmsjVrsv4A8X12J4ArBiiayv3DyyG3ZlIg6yysuuSYdZsVz3TKcTg2fd//Ujd4CHV1iA==", "shasum": "38733b9327bdcd226db889fb723a6efd162e6e2c", "tarball": "https://registry.npmjs.org/undefsafe/-/undefsafe-2.0.5.tgz", "fileCount": 9, "unpackedSize": 7600, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2+6vCRA9TVsSAnZWagAAKrkP/iYivPkem3qVrjMEJYHT\n6Bll/ueS8Ye+9zSNArBIcRhLrqUyDd1o1T5dnCB94CFn6XJF5YvXMnP9zmp2\nXokZzwnonwZNyb1vWIkVX2jLQ6pDYmQxX3IyaQ5xxR6/2pFzh3cSxyE1FHR1\nhBqpeXhgN19UFLarcAYkUP2IBvMK26nR9Tfxfg0lQFqyy1L0tmaR2OXGlv2r\nhY2R57FCFxVOwce+8V5yesVxLtvZqmTXwjURnATflpcdYbzUt61vu9hU1v7N\n+ulovQxjSxk2mxvxcEGrBEZ1eXJkEgZLLN3jG6I5vmTyusmKVEc3cLWFbsJ4\nkwpYtFCmSfVrQ62XdAVlj3DLSF39MxfW4xeVVKNdwhKc7CZNrLDtLpJrKTCL\nY5Zr0r7Oso9vWVvOmNEwhjq6tNEGpDUvSdjBUcgY4Sbgo6e9sWBXDu7Yfa0K\nb3APvAvEqPk2wn6TIOk9pPVIbn/1aXSI/sGF3PSyk5zz+qNQGLfkWQ6j9NJh\nA4m2S/IqFOWq3KLHkIWvtp5tC78u119k0ZfZ3yTxYTOUUjVYZ9He/5E26eSs\naZYo0Tn63R+Kf/UUibZK2jdeTQSN66oqKlfn7JCfgfyXZ0d1ABdzwH0F18ao\n4+QV8Fhxe4UWWi+yuqjUZ4V3t2uDmJq8r5WBJl3ELQxVo4U7NKqbL5TZa9je\nMfou\r\n=hTxf\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFhEQGust6EeBjz3YZDniUTqlUR/39Xeb5dM9aWL7fRYAiEAusGm6BuD/pAb8s72mRI6UPlEAC8RjYil6lsW0JTH6qc="}]}, "_npmUser": {"name": "remy", "email": "<EMAIL>"}, "maintainers": [{"name": "remy", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/undefsafe_2.0.5_1634463344310_0.6497103128637345"}, "_hasShrinkwrap": false}}, "readme": "# undefsafe\n\nSimple *function* for retrieving deep object properties without getting \"Cannot read property 'X' of undefined\"\n\nCan also be used to safely set deep values.\n\n## Usage\n\n```js\nvar object = {\n  a: {\n    b: {\n      c: 1,\n      d: [1,2,3],\n      e: 'remy'\n    }\n  }\n};\n\nconsole.log(undefsafe(object, 'a.b.e')); // \"remy\"\nconsole.log(undefsafe(object, 'a.b.not.found')); // undefined\n```\n\nDemo: [https://jsbin.com/eroqame/3/edit?js,console](https://jsbin.com/eroqame/3/edit?js,console)\n\n## Setting\n\n```js\nvar object = {\n  a: {\n    b: [1,2,3]\n  }\n};\n\n// modified object\nvar res = undefsafe(object, 'a.b.0', 10);\n\nconsole.log(object); // { a: { b: [10, 2, 3] } }\nconsole.log(res); // 1 - previous value\n```\n\n## Star rules in paths\n\nAs of 1.2.0, `undefsafe` supports a `*` in the path if you want to search all of the properties (or array elements) for a particular element.\n\nThe function will only return a single result, either the 3rd argument validation value, or the first positive match. For example, the following github data:\n\n```js\nconst githubData = {\n        commits: [{\n          modified: [\n            \"one\",\n            \"two\"\n          ]\n        }, /* ... */ ]\n      };\n\n// first modified file found in the first commit\nconsole.log(undefsafe(githubData, 'commits.*.modified.0'));\n\n// returns `two` or undefined if not found\nconsole.log(undefsafe(githubData, 'commits.*.modified.*', 'two'));\n```\n", "maintainers": [{"name": "remy", "email": "<EMAIL>"}], "time": {"modified": "2022-06-28T04:02:11.972Z", "created": "2014-01-08T14:40:07.358Z", "0.0.1": "2014-01-08T14:40:07.358Z", "0.0.2": "2014-01-14T17:39:43.502Z", "0.0.3": "2015-04-13T14:22:50.641Z", "1.0.0": "2016-01-29T16:44:06.065Z", "1.1.0": "2016-04-13T20:57:46.585Z", "1.2.0": "2016-08-23T18:42:36.599Z", "1.3.0": "2017-02-08T16:26:26.666Z", "1.3.1": "2017-02-08T16:55:58.475Z", "2.0.0": "2017-02-12T14:31:03.511Z", "2.0.1": "2017-02-21T14:22:18.204Z", "2.0.2": "2018-02-15T07:58:55.955Z", "2.0.3": "2020-02-17T18:56:26.726Z", "2.0.5": "2021-10-17T09:35:44.465Z"}, "homepage": "https://github.com/remy/undefsafe#readme", "keywords": ["undefined"], "repository": {"type": "git", "url": "git+https://github.com/remy/undefsafe.git"}, "author": {"name": "<PERSON><PERSON>"}, "bugs": {"url": "https://github.com/remy/undefsafe/issues"}, "license": "MIT", "readmeFilename": "README.md", "users": {"nickeltobias": true}}