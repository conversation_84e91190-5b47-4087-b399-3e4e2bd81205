{"name": "json-schema-traverse", "dist-tags": {"latest": "1.0.0"}, "versions": {"0.0.1": {"name": "json-schema-traverse", "version": "0.0.1", "dist": {"shasum": "d241e25481ad2fe8a1fdef61240d727fdee61283", "tarball": "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.0.1.tgz", "integrity": "sha512-P3xS/mSXzflYybh30MoI1fF1nFJuyVnNKlluipufn3Jeg0V2bMxsQ4GzF+L7DYud3OSH0QLR7wxgETrF+i2L3A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICcHoqEB2KrJcOWJjZB3/3+1KUeg9DRhkXoLv3rrj4W8AiEAv51Bi28kYL/xuhv38ir35R2q3cY4aLNIi8EBQPUjUS8="}]}}, "0.1.0": {"name": "json-schema-traverse", "version": "0.1.0", "devDependencies": {"coveralls": "^2.13.1", "eslint": "^3.19.0", "mocha": "^3.4.2", "nyc": "^11.0.2", "pre-commit": "^1.2.2"}, "dist": {"integrity": "sha512-1MtXJHDOlTbfMgXZ2mL1DvViXyph6ISlzKIk5On6KJbcMrmdOmwTsWkFDhy+9CJsstbuKlQHohAM3UIDNzQQQA==", "shasum": "d88e207b83fd8b71f09f5b0c11734de5a0a2d203", "tarball": "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.1.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDYvoTSzjq4AXufqip4HqomwT98HXIEZIHep8LTM/MlcAIgFiTIx13nnu8bxYpt9SbfWJeW5I80ag9VkIsXMNt/U3w="}]}}, "0.2.0": {"name": "json-schema-traverse", "version": "0.2.0", "devDependencies": {"coveralls": "^2.13.1", "eslint": "^3.19.0", "mocha": "^3.4.2", "nyc": "^11.0.2", "pre-commit": "^1.2.2"}, "dist": {"shasum": "486926926c876f74dd0f7d696ad072118c75d03c", "tarball": "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.2.0.tgz", "integrity": "sha512-W8OfVxI/WDsrJXsDzCRfcH4UXFaxC+W/MuWB38urdbLZAkgxV/Qi+YeC6tTmx6+n9sbFXp36oe95Fn3FFNyMRw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGDTJg4X0USb8FZj84XtVy+05viKTY1xkEW1zuBrydrrAiB1v6ydur38P+xh+R3eeWji2yU5p0ogZZX3Di8qNoP+Pg=="}]}}, "0.3.0": {"name": "json-schema-traverse", "version": "0.3.0", "devDependencies": {"coveralls": "^2.13.1", "eslint": "^3.19.0", "mocha": "^3.4.2", "nyc": "^11.0.2", "pre-commit": "^1.2.2"}, "dist": {"shasum": "0016c0b1ca1efe46d44d37541bcdfc19dcfae0db", "tarball": "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.3.0.tgz", "integrity": "sha512-rAzOwA4cQfuDsZtnNbop0anQyosa+EzNt6bafT8lDMQA+xFmlVnhG+Uc6Eri/pOI6nFtA5Nak9ORyvbCZ4KFNQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCPhCdw3Nvs9Gid+yGpm4jif6WGagbu0JZPOWzJ9bgvvwIgK4fIwQ6MVtrJQor+dCvZ0NxCXe9/xL2BBlDxgU+wWn8="}]}}, "0.3.1": {"name": "json-schema-traverse", "version": "0.3.1", "devDependencies": {"coveralls": "^2.13.1", "eslint": "^3.19.0", "mocha": "^3.4.2", "nyc": "^11.0.2", "pre-commit": "^1.2.2"}, "dist": {"shasum": "349a6d44c53a51de89b40805c5d5e59b417d3340", "tarball": "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.3.1.tgz", "integrity": "sha512-4JD/Ivzg7PoW8NzdrBSr3UFwC9mHgvI7Z6z3QGBsSHgKaRTUDmyZAAKJo2UbG1kUVfS9WS8bi36N49U1xw43DA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEEUvq6T+SmKN/KLhRDONA5sJia05n3oYTphNkVZZl+UAiEA7ITFoYFGXSzOvfcziBq7b/81vfczQk8HS+n0k+rgWzo="}]}}, "0.4.0": {"name": "json-schema-traverse", "version": "0.4.0", "devDependencies": {"coveralls": "^2.13.1", "eslint": "^3.19.0", "mocha": "^3.4.2", "nyc": "^11.0.2", "pre-commit": "^1.2.2"}, "dist": {"integrity": "sha512-1YoNcmKmUIOyoF4DdbjA4qeZz9sxGJ3O0OAZI7J0b6K9A7jiE/24azJPEE/4ZDZbXsDU6biRoNcC8QWwCKegkA==", "shasum": "e8467bf83020ba9d2a47b2e6b82e1ac056b0ad86", "tarball": "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.4.0.tgz", "fileCount": 9, "unpackedSize": 19547, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa8fygCRA9TVsSAnZWagAAdn8P/09M6Zwu8Q608VPdBomC\n34JdJK+UNPU/k0NCO0aC84D31U4M/fsT2zVAWF8XcQ4X+9qFYJyKwRyvA+b0\ni2hxPdlBife/3aJKxFugOGlHQv0QE46H9BI1Yzr/auKlc/XtBPbK5NgBxQua\ngpOZKY92+jRE42eZaQ5AL1K+W6pzkE5/zOfDCeI5CHJos7/EAdQdsS/5Ferm\nJV/i+A2Z1VbdFtZYAiqgvdldqt4gP6TA77uoep1aLPtf/Zvm3T7qXk7hencf\noo6AilfwwTUOWvIsy2ph4qaq1zwtM1PQ8/27/YQx0notvsegRfEJvh/8rtQD\nuOk/6nWtiG9Ls/Tbh/ZqwQBikcuFsU5EA+Zn/ZSnl33EhXSahSHP7l2BQA/Q\nx7jDqts1ATpwQvX9Zz09h3s7RsHtTNftIrahUGqJ5owBw8vi48RAO2pcgxqr\neLdCJkzUf/ECyQvoroxV7Ek57V2It61QlNankKGEB3rHlH2O1aCldTjtSsqB\njv9F9dCCufHMp7LwWibaiZseLqdOn00NSY2GvOnmrjM8sU3OBIq/bwNt4CyI\nX1SI866T56JMBCT6B9KCDMdysfEKtqtho2vXqtjWbRY7LVXiycHn+HR9x5v8\nj+2+c4lYY1t5vMjOdh6Om0+eUGK1C04cERu5yIvKNbaOtrxOxNo/wzamly5a\nkEpm\r\n=ZBqZ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFIhICbuOP7yZkZHWsL7vBkiNhAHTUZE0fTGPF8yYtrtAiA3VEMyl+zpOLpWbvLHqA2ZnkOHRCSCFTAq8OitS+MRGg=="}]}}, "0.4.1": {"name": "json-schema-traverse", "version": "0.4.1", "devDependencies": {"coveralls": "^2.13.1", "eslint": "^3.19.0", "mocha": "^3.4.2", "nyc": "^11.0.2", "pre-commit": "^1.2.2"}, "dist": {"integrity": "sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==", "shasum": "69f6a87d9513ab8bb8fe63bdb0979c448e684660", "tarball": "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz", "fileCount": 9, "unpackedSize": 19564, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbHOReCRA9TVsSAnZWagAAVWUP/0MJPYc1bhxL+UjZWR+N\n0w1lIESRrtuAzcNFPDBfPvd8bBr3gxJlaLHMkpvzyEZGN69bgQP2hsp6I+Mg\nxvZz/mSOnK5Yj9/VTytxEjlwqW0GDK24x08GXNAZenNidRht1iZvBjugZbJa\n0hxF0z2Xjkn9VXgtOI0QiBLj9t4mutLrte3kFFDNpLTzoRx5r08JeA3L4k4I\na/Svzav/z4c8AsUFaeY/eAtGPm9KqwLzX0eLWQ7ueYPR7YgtY7WUWuSSh8+f\nuk9UJrG0OVS4GPl8YvwMvjf3Di9nUDwoQem9QnsIJCaXJ+Gbknv8B/Lit/9a\nzmjaNJDDNOF8t2iUfwUZWarFBGxYsZBcoECDPrU/tGMj/8IfHXMNHVWraqAp\nfZ9pvYbJ7s8DTlpJ8pkuGUDPrZIX4POWYcsuYVjUUpRwV73HnCIIAJibctmm\nOf1HzG0WaF5OE0ZU06UcGD6SUiZTgtyqaA0jU9vExTYhYSobaWqGKxA1PspG\nMATdwowCSsHWrUx+jXpzuJnEAyDBLW0iVh+rjBmWWvZHS+fub3rE/yT5FOPI\nk1y4qSwsF2I8T8R+Moo1OewFQzUBbaCjdpnOsBluUWmSvkRiwr/lcwZznt51\nBCTVCgPAGPQhcHbHtM/cK8tqpGEXeFTKtHZ5fHCGsL49sLtGPTj3b948t4z/\n1BiZ\r\n=77vn\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBDbOwAHkjLehseSNqU5TdSlk74ifhLpOlyMdfnCn3piAiEA0+22VOsxdSfplXb4J6DDiQvQd4rDYLqynJknCRcTsBE="}]}}, "0.5.0": {"name": "json-schema-traverse", "version": "0.5.0", "devDependencies": {"coveralls": "^3.0.1", "eslint": "^7.3.1", "mocha": "^8.0.1", "nyc": "^15.0.0", "pre-commit": "^1.2.2"}, "dist": {"integrity": "sha512-x+TRJIQFskrNnFKE2Viz9FCSjK1vIh+H/uaBiOYszh/IcZmAFneQ35H4osWDJp1NPXccuV2I0RMXmi2ZS6Kqcg==", "shasum": "1069a6097f9e9a567bfc6cc215e85f606ebf6480", "tarball": "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.5.0.tgz", "fileCount": 11, "unpackedSize": 21098, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfU+ywCRA9TVsSAnZWagAAo4MQAJhqEwwNhHZFLPcknIez\n/fD01Vms7E0C65XAiKct2X2Pkiqpv9LqMk3cBBaPXDnVUMZeebjIjXacuN6z\nuuyIAQTjxDPZez3j2qd91lFPMSW5QXuWDK0xpnUqCINkZfrAH0g2ctZY81vW\nGVY5lutIYk6uQStlsSet/iVDWdJX4C81lmDZzew7EnNcTUi0O3TQtghOSuVG\nslnT7qpdweY+z+mNzrBwkFeMg6il5YygkHmmYz18HssNncV6+PoVdFn+XJK+\nZZ/QGXjS9IcBh+/iCgz0+2Az4bGr6UVcf2HleKBMIfjOQduLQ0zMiBy+lDMx\nhxyLmtevrqefSdkeVX7Y8tAIAhHr8Se7WMeNHjMbGQe2T+dB7PUh18zLSO8+\nQZqtOsNprGm23n5avkLORrxA7PnqGVq8BBPY3pisPv19P8ljJYbRtSrsH2Vz\nMoMUupSVaLNWeFwiUA/64WkXPVMFcxNjTQK6XDUL30JwGuE6PN/p1FGIYPXv\nByCLsZ+sumBDYF8V0jIb1AfERA143Nvgl2Pz6Ye27yAKw1hdR+d+5MTMxUWu\nmPSkSNAni33H9VMybozn3nulLHIwb+YyGJieR/iLQvoyemduoMAhwtCjK0fA\nnfRehA0Qp2hefE0AfyUb6IcPV3ROZddrDGvMgpJ5CbMNeaDoXy2T09vG/a/7\ntDlM\r\n=6n/B\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHaYYtSpbSyhEv3zHtQ/JRHfNMkXDufaaoXD4+ivt36BAiEAnCnCZR8CFN2gs5RehRrsmfjwXkBtC0cA6XgRhWgvMmA="}]}}, "1.0.0": {"name": "json-schema-traverse", "version": "1.0.0", "devDependencies": {"eslint": "^7.3.1", "mocha": "^8.0.1", "nyc": "^15.0.0", "pre-commit": "^1.2.2"}, "dist": {"integrity": "sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug==", "shasum": "ae7bcb3656ab77a73ba5c49bf654f38e6b6860e2", "tarball": "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz", "fileCount": 12, "unpackedSize": 22220, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf1fN3CRA9TVsSAnZWagAAOvgP/RW4IyNaG+9p/oIa6WhS\nu0try6lP7Hym+dFd2utH5S19/K2IPoFXP7CFvq1blPVc1adUBBRdfdz7SJ9J\nVJr0Hdzpvrzhkzfod9SyGcjmwOIZDGmI7mhwxTuqYCN1kOWpAY0gnzfV+n5p\nZxDGKLieCqCzmKc1TsSb/aUtWndUmcs8UsUZI5mjqSTajcqRSX11Fillmor8\nZVFzyKWhwEmDFRQlFRR3cTEbytvs55sqTdpWxaZDh7g8UrxdhUQIoI0SnQFO\n3pWaMCf+fM9SGY2JUlMLhr6n3ui5OBqkgjfw5AUtld405fyNbPQlJu7QxATo\nMU7npmYHcoinMOpciVD/tLNxD+OQKyOZ/NlL+XXBdCWQhLIuF+Rnb+raUiSC\nw8q8W5Vba/JN9/pHs8x+3P1eZFIM+cxMQk9RYFuEqtHkGhDiVthpyMsK5h0K\n2Nu7nLBVsnEEid9UggwPep2ua1JO5YETsAtwwo0RFuhRhs3I37eKloiI+NmJ\nCBASUczFiT2vE5ySGPHWMteTjL/5uUU+a+IZhqA/o2Wsmnw4Kxln5npF0kjg\njAQnn/t7QQcWhNGqsFnKEg2rv6cUgj9MoZdxHsyyyCnZ4qEBLByv83zOpigR\nfyxhFwV8uGKg1IZmeNQn+Kj/pBpifoUSbR4LGr54MVSuUrkjIdBv/sCLVC3c\n3vaz\r\n=T5IW\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCNOIFNJRe2jcKCXH4ICrcSvZnowa7w+vu8ftVn1ma0LAIgVov161AURGFLhlj5B6eSkke5RxOlmvY3avjHP8IvUTo="}]}}}, "modified": "2023-06-22T16:32:38.879Z"}