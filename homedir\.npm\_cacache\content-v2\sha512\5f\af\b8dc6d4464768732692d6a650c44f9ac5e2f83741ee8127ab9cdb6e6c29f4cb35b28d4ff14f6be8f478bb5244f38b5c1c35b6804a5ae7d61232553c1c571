{"_id": "lightningcss-linux-arm-gnueabihf", "_rev": "37-850a9e43388a4d784a9d31ca27f0d0ca", "name": "lightningcss-linux-arm-gnueabihf", "dist-tags": {"latest": "1.30.1"}, "versions": {"1.14.0": {"name": "lightningcss-linux-arm-gnueabihf", "version": "1.14.0", "license": "MPL-2.0", "_id": "lightningcss-linux-arm-gnueabihf@1.14.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "82ab47c611342adcb617e28fb9599486aa5ee913", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm-gnueabihf/-/lightningcss-linux-arm-gnueabihf-1.14.0.tgz", "fileCount": 3, "integrity": "sha512-8cH/qwZnoU3zIruVNWGLBDVm6f1w/ZC3eMDkFOTEF4FaW3jCpxEg/BH5r04lNUM/SO6Zu2C+vgJaEKdUm58dQg==", "signatures": [{"sig": "MEUCICbAPTPXHepCM7tEVPGhSDiHxXzK90JO5S2sgPYrUAMUAiEA90G00uw40L5wLcIj8zopPAI5qrdHn04I+I97kP+vREU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3156802, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjGXtKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqCVg/+JO2vdTa0On2vOZaMFIn1zMvTq9hQCCnclYqQcIUet1oV7ML1\r\nYZTgvC+MVPTHKsbtAvlFcwFLcX+/Qz6QIKbdZXu19LJwCdLkJthxEfn9oRwC\r\nM5Fd0/WLVGrH9/SSA0ftAWXjiXloyO6tsLn558e6Yk9ixDK4ydIJQcNXVI70\r\nhTCCWJLO06BcogFdmw0v2w3XYVJHzHRu6d+HV18Z13xS1A+5wCgW9MWxh/Xm\r\nrrl2rL93jbezoVd+B0yf7KxBNTP77r6TVelCU7yIqvDbmX+RV5QhGf1zEptp\r\n1OMlTD/K0h+2aPBsJZdP0wDOcDd1HDIP6oZQnJdLB6c4PI/7IuoKku0XBuEc\r\n+603ZGbqZ1KMdIuO07Gh4ZXTISkprYYVUFePrLi4nv6gL7gfMO2mZBVAt6lh\r\n8n93qrCTQesBLpl1Z2cPzZbcKvVLD7LG7mHtuy30AZpFA0sElDi/3SG+hEZg\r\nfiYaItORI0mmAxe0HEOaMeVG9+0MrutCXyhfHFqHD1OXLioyFo80VSPQwbUW\r\nXTOmewuvk88uZfWYSssLZceSt+wwBvN2Lb6sMWt2zteiKziOKHiRsvxUc0hb\r\neRRBGDrCSSI6XSyfW0la+a9O41Z/kSkBrbF04JvBFqOeZCLRRRxgcsDn06ig\r\nu5baX/D/6itWDZAtMkFWypd6WLUTfc9alPY=\r\n=Q5Xo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lightningcss.linux-arm-gnueabihf.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "2d17e0e1ed1482525985ef4d30809b4c06d76944", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "_nodeVersion": "16.17.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm-gnueabihf_1.14.0_1662614346288_0.476151286119348", "host": "s3://npm-registry-packages"}}, "1.15.0": {"name": "lightningcss-linux-arm-gnueabihf", "version": "1.15.0", "license": "MPL-2.0", "_id": "lightningcss-linux-arm-gnueabihf@1.15.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "f916ba49bf9feb47d522cee8fa3c9026436fa7a6", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm-gnueabihf/-/lightningcss-linux-arm-gnueabihf-1.15.0.tgz", "fileCount": 3, "integrity": "sha512-mK/cI5FNgc1FIgeqpEVpnxXW83DAKewmGrlhvtQfwNEAnOIumltltr0MMjjTURF0usPhZaiGJk/r50xVDrU4FQ==", "signatures": [{"sig": "MEYCIQD6MUsMZMLB1VjQ7hT3N5uXO8dBk4wKT9wsBCRRaTBPFgIhALXj2BB+pwVmqwVZgFqDo+T1CnKOUfc04d9NyJ0MRLY+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3165041, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjIqcaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmryrg/+NZVOhj6JBX4nQ78oJs4pONg3hIMT1swexQzpRRwFXHOynS/N\r\nt/K7H2I6CS6LeR2859JDTeQDXQ6AXksdp9RqAqKINBpB6+tQkrCQAXtWTEdb\r\n2WRw8VO5EIcirjA0yzb0TiyDD6J6NTPNW8RKo25J0HNKHQrRB0pD6Ae/mort\r\nvEg5q1V5gBwvKvQiw8lEcbrUmkdNRUnv1qU6XDflxAdhHj8mvc9IdM+crrb3\r\n6bUG7ea4TWpmUDeruNDyjsi8GNEdPvXsLh37I1KvDdr5An/qj0vaD+iR7fzq\r\n5NVimn8fqd53hXMsbUrQNvQtjWV1sGLWqRAQ1pzyhj/Sqh3eoiM9JelM8U5M\r\nnLm3sb5kDds9vh8qCBR23M8cA553vCzq6B3Uy9qeRwtZv8vjAyDqObuzIIe7\r\nwdIs/jDbj4wJvLb1Ui2pS5UCXlwRYJYIAPo+EpQaTO6ZSBY5LgO//yzryOq9\r\n7UfVDHz4xapSpuFyTeKo/TZL7DIvF5Lod0YdvNWHmogSfto65Oqcl0TKKuzv\r\nKMXrAP+y/TzLX/AyGOIEEqiUCoghJlbLNan+tlsoA8DTAYTsy9tmvwVwheNi\r\npyZrh3HSxdS/eUDJG06XppHHL6qWjxfCyo9fPOHh90W32niBGwGMUPngfQbU\r\nSKDO6T+b34Dp0PLqJKyBnU0W1VNmzdNd+2Y=\r\n=X1KH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lightningcss.linux-arm-gnueabihf.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "1a23835a0d72afc06ab5f98cbbee5fb03ae09074", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "_nodeVersion": "16.17.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm-gnueabihf_1.15.0_1663215385885_0.8293935742618961", "host": "s3://npm-registry-packages"}}, "1.15.1": {"name": "lightningcss-linux-arm-gnueabihf", "version": "1.15.1", "license": "MPL-2.0", "_id": "lightningcss-linux-arm-gnueabihf@1.15.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "2d14a9115769bf63cbeb2e0676ba4fb3b69bec2c", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm-gnueabihf/-/lightningcss-linux-arm-gnueabihf-1.15.1.tgz", "fileCount": 3, "integrity": "sha512-8FijfM4HGJPCQPB9nAaTjdOE2PGQYE66t1wvV+SR915dEePn4yyRdCBJitlas5B6aTAE2AMwEuEl1i/pVDmkGw==", "signatures": [{"sig": "MEYCIQC7c+V84IEYEhpBjiWCD9gwJToB/HgIaoSiH60dWLL8wwIhAPv2Yi7Z+ci2g3k8nRHdQY3/hCDSkYRnVQRrXD/nC5bw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3165041, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjI/I8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrwfQ//ZyRX4lSz+ph1IS0o7xqKoMj5vv5aGK4qPb5F0Rmvihtlbwyf\r\nU0D73lDXJo5FK43Gqv3DOuhCpwyicpuXkpaIR09vZxbbPVpR3umKx+gpngjk\r\n5UHOUEU/yUIHRRhuxebKZnu4jq8Xd6OhIym2kCX55aZcmnSnJ7ci/SMcYA7d\r\n/xiNk+P6u9g+llDoSzjrhQR8D58SzMSEQj4nOoLnzSfdfiO5FgkmcaBLikuY\r\nSWQC11bcImhWHJvWiN3HI2cpBiPhm1IgUE8xVYYvP3NxlQkYqOswKwQ+rvl3\r\nKH8SJMhyuPojKgEt8UxrL99TsbSdDw0DCCixV0WJ1M9Gjy5tnjWQs8vVs+eB\r\nB9evH5O6y/wu1WW8xCegLNLCfstXSqh0lUB2nUcCKApkiL+k/97GHgVrRtel\r\ngXFTn3dQx8eKISk8mSLlhKhKEVsQPfgmAX0XPB41UfadUX3f2JecBSESZ2Ns\r\nNBmanEvi3JOskg8kcJZFdbrxA6qV80T6oQQQhTmtN5Ku5W3kS7JJEgYALpnt\r\nMO2xVScT/OdaJgQviGDDhEq+IqkEwjgDGipR8uagrg7hmUkB0ts6J7C+4dsJ\r\nPmSuCmbeuZDGq8PfkJe4JjYQ+ZatBA71GiVSfApMaGo1YgzMngVpnsYqlMrG\r\nX1M1swfsKfart/5ScJ8nGsR9Rer1G0wzgF8=\r\n=tIeu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lightningcss.linux-arm-gnueabihf.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "eb66ece2f9b47bf4ef60848736dde697ec80a319", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "_nodeVersion": "16.17.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm-gnueabihf_1.15.1_1663300156320_0.06457885083939807", "host": "s3://npm-registry-packages"}}, "1.16.0": {"name": "lightningcss-linux-arm-gnueabihf", "version": "1.16.0", "license": "MPL-2.0", "_id": "lightningcss-linux-arm-gnueabihf@1.16.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "5da29f465dd44d98434b00b424cc4b445ea49eff", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm-gnueabihf/-/lightningcss-linux-arm-gnueabihf-1.16.0.tgz", "fileCount": 3, "integrity": "sha512-oSwEbvXUPr//H/ainBRJXTxHerlheee/KgkTTmAQWiVnt8HV+bRohTBWWPBy5ZArgiGLwj7ogv45istgljPN2Q==", "signatures": [{"sig": "MEYCIQCMzgjGB4KolpekZrtybszMQlQ6ZFQ4gRW7Ij9PYX8e7wIhAM8x+eGmZkN0nxZz+t9X7x1todrPJw35y2C2Lhux40SZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4008817, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjKTIbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmphwA/6A5wKCIoD4YNSz4lPK726bX8pEaJS6oIJxKqxuT+VPWao9hgw\r\nPE78SxcCxmYo6dDym6VZWFX/2T5BTZWi0iTVZ1+zVkGlSNYpCBw5PgyU0vqc\r\nLLHvFp1W4Ne5DcGTmZhV8ppF8gziS4lg0pGMUeaIOIXPv/QhWlnF4FEESgDP\r\nL3k18uHv88XI5xMCU8C2EyW7ovgqVB16uggJzH53ixIft7ji/pY5qe7Fa2qN\r\ns+pZWb9pXiJCAI9iPPtl/y3kwbuwHq7887oVhZNsnOAtYdm0PLhCgJKh+hFY\r\nd9u6ITO7wywVaycyGf5r9brAjR/2cbvmDx633ndk71cWs/GCao8kqBIhmiJ1\r\nGoChysewDBAj45/OGEAUTlh1rx1fmxAOlXykbSXy34HZ1afchvM3tItF/Y81\r\nWUH3UiaK0GgTPGIoy8onvgGyX5dDvX3P8eVSarJwW/8Zm3zxyOb+X27lntKs\r\nggVI/T1e+IrQXBsAif7Ytz/UznfFnHn3CgeJ+fgIEaMgnhxjMDaaCb0DYT+m\r\nsWjbY4Iap6mkj92f9kgzEq7UbhHAccRnUqIwt2QkDZNOOk4mkhQmAs3FqEs8\r\nGEdiK3HfatpiVXI/gKhZcyM1sN2OhSAPGgJS7oA7eRxvnM3ITZlE9GzavGTo\r\nk3YGiz1WT9stYE5Fv8YsFTwe/zaTKbmrTOk=\r\n=MLUR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lightningcss.linux-arm-gnueabihf.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "200120e24aa31449703707db14ffef1bf40a7fea", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "_nodeVersion": "16.17.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm-gnueabihf_1.16.0_1663644187185_0.6853114960285314", "host": "s3://npm-registry-packages"}}, "1.16.1": {"name": "lightningcss-linux-arm-gnueabihf", "version": "1.16.1", "license": "MPL-2.0", "_id": "lightningcss-linux-arm-gnueabihf@1.16.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "9edacb0d9bd18fa1830a9d9f9ba00bdc9f8dabc9", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm-gnueabihf/-/lightningcss-linux-arm-gnueabihf-1.16.1.tgz", "fileCount": 3, "integrity": "sha512-0AJC52l40VbrzkMJz6qRvlqVVGykkR2MgRS4bLjVC2ab0H0I/n4p6uPZXGvNIt5gw1PedeND/hq+BghNdgfuPQ==", "signatures": [{"sig": "MEQCID9qkJ9gkmq5eT0TWBaW6lAV/yQrfvoW7RKCv0svOrFpAiAY48CC/A0gWiah7zv+UIJVxiR0cZxE1bJi6WEDmkJzyw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3865417, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjZ/aYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo1TQ/6A54h+5t1GaM3oDXv4NAUdH33Z/1YDpzTtRxLMUPNDdu2DId2\r\nvUL0M9A875lyfRQ40auyhX8wfEY/YlPjQbuDttKE+jl2OPTptZzpiT3w8T61\r\nmyqOxc5Ku0XQNtNn5oXFlciy1cjyab4+waj5x8gZW4GLRi1uvRrL5zi3jDry\r\nTdI8XBRhb46cmW0oWWGi3erQzUcUGpBTXOpJ9MeLSFj3gPmuG3kvAALuahvZ\r\nDJY56tB+oG1FRN1kmx66M5jbmPpUyJiNCJPZuyvC2Naothmy/U2hKMaGf9tB\r\nAr8yuQItQ1vQBhZdXVu1LNVwkcYn/15HCHgWbognMgkbVGm44Hz7NBQxDfPX\r\nzP3vtSLywWWj3bdYklIrb97BA28aIl3y0fGYShcyF9+BxWY7j8qotCyUVv90\r\nVoCjbqpdM2OoVaFunuoD/AKh+IXbje4v+Ehznmi45hfk/VVULlzS1tEWXHfb\r\nlDOXBhvMQfkuYsOCPaGa9QsWDoPnGnobMZ6XkSgdOuQid3q/mFhBUfsszXTt\r\nxnmSKbB2e6FBWibJQSeqWn4yRvyqEQdWUdLYjtvJJizykI1Cc1Xw9pubA6ZB\r\ngDcRQdzM2lg8LnCYagkZQE+RrbPeo/N6pG2aCBjzb/xzw8W7LZY9fByR8qVP\r\nWoA9PetzJ2n5LmC0tdA456TU7jW0TyNbVjM=\r\n=dBLC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lightningcss.linux-arm-gnueabihf.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "87e808fd771374ae705886351ee62c4fac3d3630", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "_nodeVersion": "16.18.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm-gnueabihf_1.16.1_1667757720450_0.28004892072293863", "host": "s3://npm-registry-packages"}}, "1.17.0": {"name": "lightningcss-linux-arm-gnueabihf", "version": "1.17.0", "license": "MPL-2.0", "_id": "lightningcss-linux-arm-gnueabihf@1.17.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "3d84b9ca8651dd1223ea7aec9ec4b5c889a57e25", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm-gnueabihf/-/lightningcss-linux-arm-gnueabihf-1.17.0.tgz", "fileCount": 3, "integrity": "sha512-bvACFOvKv3Ls8d5OPG0sCCKV44/cr2UMDceWc4qXHQ3cKoeyduuqitYF9Ez6U7OmyI5B2MEXB8ttyRdr1oL/pg==", "signatures": [{"sig": "MEQCIDCFT8r0N1wJ3HR2tDh+dqmCQzKgzP2YhvxKbFgFfTuoAiBsgWq/8zx/WZZwQ4lT8ibf+fprXkqa3A9V1dE51TcMCw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4901709, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjhjevACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrm6A//c8CWm1AFCeV01qoC4Xg8c6sbnLU2J8qga608zHO6VUbRc40a\r\nIcQf2f5zizH9UJPv4g1cLNMPbz6fyKbdxV/LlsG8Z6Jr/Lsa7h00kPqpQ1mX\r\nXvk/cj+11BtcPLikl3RUu1vZKRGq5stR1ay5JxhC/HVkUl0tH1C2Pov6SMPK\r\n65rYontInEKQHUYHCakuX0AsC8X+OxkD5jHfEkOnVvOV8aV8Zeq0gDCaHFtV\r\nQYlBG8pCLSeb/clReI12eAJwKoXZv3B9EoNqfbMqHm8K+JeoZb+lKXt2jN3D\r\n1+kVbz+bgRYF0ilwjic8G3BkhEbQTZz1OWTUNgZNo5LD09DW6G3z6JP9kjzu\r\nbeJVsozYRZzi13xPFM7EBq6FO+/YlE4bKK1pTLPxC7cCuWe8bQXjKx21ykIH\r\nSL03tozKTB8rSz1aZhgnGs4zksvcikZyFKUKS5l/dvBEGdVk54VwzlIq8G8P\r\n1VeQuvpK3mwUdUzz0yW5AkhAvHo6zDl6x6LklU1MjtMDXLxxRTxFNsZRTspb\r\n6C8XL3AmcDKEz3oTffZ0Wz7D1O4rsEnPWjvQxYBkmsGQNkszovlsj0jCRzZe\r\n0dyxSDmjcVeVZMwSZeOC0SU6PZfytlzyOxhXhmcooSRADiFDLwNdUuziYn3X\r\nuu/hzkIAlfDGYu13PfVf2kejXmXFj0yQmz8=\r\n=e+q3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lightningcss.linux-arm-gnueabihf.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "d4853b204cc80b051d29be324e2d16f621e76336", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "_nodeVersion": "16.18.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm-gnueabihf_1.17.0_1669740462845_0.35763075074836315", "host": "s3://npm-registry-packages"}}, "1.17.1": {"name": "lightningcss-linux-arm-gnueabihf", "version": "1.17.1", "license": "MPL-2.0", "_id": "lightningcss-linux-arm-gnueabihf@1.17.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "9ba7ffd5be686210b88ec28bb495bf9593698678", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm-gnueabihf/-/lightningcss-linux-arm-gnueabihf-1.17.1.tgz", "fileCount": 3, "integrity": "sha512-alUZumuznB6K/9yZ0zuZkODXUm8uRnvs9t0CL46CXN16Y2h4gOx5ahUCMlelUb7inZEsgJIoepgLsJzBUrSsBw==", "signatures": [{"sig": "MEQCIFuaOZig0KOMjcLhY+0rdXUQ/5MqEaFt4OJr7iBZ6tC4AiAUk47lM8l8E+8rT9ClHlkTiOA8HH5kXhy5xSo7J47oSQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4901709, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjh5M2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo0yg/+JmsGlRa5fIQh0JOKjjST8cAz7aq8btvRHaPwEpzI4q1BqIWt\r\nyn3e6fGTCioxGK6lX5AS2oBstEsfZxrNanHu5e4EIyj48SIVLHE50728qCin\r\noXd/LbZOxYSQBEuJG7vuJ3+V9cmZjNytyJY9JTtyPmgF8p4d3QtV0jkvx4Q5\r\ntan13G5aPvGN01Q03u3PgQ7FgVPfMML9VbudMi70+e2E+wbnocEmMv16b+cJ\r\n<PERSON>hcyVYzAsduLEUbvInp8jQ8T+q0kJS2Ay6UnzfdOOecECmhx/DkDwndvMkUZ\r\nTFviwSiR/60iBLgzLBR/7Rkv1BOg3z8bw7Sw+82OivSXskxfB5Ep3/8m485F\r\nv0+hq493EvoShnVfQT98H/IsjNNyEJ3lNsOhZVn3mbxF+HxSfMxz9UJPWTvi\r\n4Ah1ADjLytrzokVIHmLSi6xzDvV/stbB6IGmg96VnFRkAqikUhwyphc2NYad\r\nCTHtkYRrBTV7zwn2ohybtc8EFZ8lCyo2EBcID3Q/mgspQam+5ln7BjTm9BLO\r\nfB2yNznyTZK0b0xm4lqlcUSXz4NJRQsFaP4/mnuG3Or9tVunkMhCPXQNOIrq\r\n7in37b1fKpbEupXl4txeGOd/2PB6jQOdPqE8NmVjQdx22FUizgiGc9l5jnPH\r\naegIao7KyydvKAwnlA2BSPbcpgeKzCLTiIU=\r\n=P9xn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lightningcss.linux-arm-gnueabihf.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "27cb9c2a382fd8d9702de8def9dd18db54280c43", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "_nodeVersion": "16.18.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm-gnueabihf_1.17.1_1669829430435_0.381569512618388", "host": "s3://npm-registry-packages"}}, "1.18.0": {"name": "lightningcss-linux-arm-gnueabihf", "version": "1.18.0", "license": "MPL-2.0", "_id": "lightningcss-linux-arm-gnueabihf@1.18.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "23ca85e05dc4def9b4975aef307554ef292b56cd", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm-gnueabihf/-/lightningcss-linux-arm-gnueabihf-1.18.0.tgz", "fileCount": 3, "integrity": "sha512-S+25JjI6601HiAVoTDXW6SqH+E94a+FHA7WQqseyNHunOgVWKcAkNEc2LJvVxgwTq6z41sDIb9/M3Z9wa9lk4A==", "signatures": [{"sig": "MEUCIBEGuCW/cA/G3NVii6HO4R4eF4PZ50HQRbtWcOBA+TEaAiEAlhELNlzivlYQbl/BKNQ2D+fbSVj8uq2E+nF7Hf7/u8I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8287254, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjtbCdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpUCg/7BGwzo/g0yyLxWspypQPXxz/SP7lyqGnyO72F/dB+NUmJrZMK\r\nAsbrFHy1egkr7p5kCv1Z6f9UmLr9GPJLTTXRO/LVWK1rJ1B496si0KD0p6ls\r\nPm8FzpMTeqzL8wkrQ4D1euF1Yta/CrqzqYm/H7GAPoPSpYQQcoeqQ/icTOdS\r\nN4d5XWh4Lg2Zt6xXv7r1YfGN/HuaMuf2dvpiXxBVxPMUAWuoul1tgGc2zvVj\r\n31J2vBOnsKCXig7YFnELFwrRi8wxjvTtOAipQpjwRTxls8naFueVe1pyliuf\r\nW5rM5mAI6Pm4V9otxX7THsz4Qax/r5n9Kaa/4tcjNudIUGw/BTdufV7pPwFu\r\nMiG9y5KAS7qF/zRq3Sf7wk7y4W9/uQ3ns2cwCeV1WhbRGG0lmk7pm3LskUgx\r\nIZOPoBV4r7skAtrqMpjZdrmFnCoI/rWf8qf9aYDARuEed8h4taZ77UbpE4I0\r\npAke8NMnFlgzPLVQAbxUCuARPVPbqoqHm7kHkWU/DHFHyggF0esu123NqgUj\r\nx3ehGSKJcexDuV0uDcEMHCHLdQK7C5t5RDTRCYBpaz2fnOLdTa0gyeOmunVC\r\nhuU/+4k1GQV6h2jzsItbKR4CfZPDNcQfMmFPuU0SPqRK4wZVlPQQqv4bY/V0\r\nH4jp0A8y0NpDxCWaS0tzwKHHTZweRI7uBhs=\r\n=9cLS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lightningcss.linux-arm-gnueabihf.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "6fb77292c9ef4320fa85a1e26fec3fbdbae3cf8a", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "16.18.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm-gnueabihf_1.18.0_1672851613551_0.46133522989225795", "host": "s3://npm-registry-packages"}}, "1.19.0": {"name": "lightningcss-linux-arm-gnueabihf", "version": "1.19.0", "license": "MPL-2.0", "_id": "lightningcss-linux-arm-gnueabihf@1.19.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "0f921dc45f2e5c3aea70fab98844ac0e5f2f81be", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm-gnueabihf/-/lightningcss-linux-arm-gnueabihf-1.19.0.tgz", "fileCount": 4, "integrity": "sha512-P15VXY5682mTXaiDtbnLYQflc8BYb774j2R84FgDLJTN6Qp0ZjWEFyN1SPqyfTj2B2TFjRHRUvQSSZ7qN4Weig==", "signatures": [{"sig": "MEUCIQD31Vr7Ju+SbysI+RlgZp0Z2pgVOJ99BewO+heavEF29AIgQIrIcNLQTiz/1VyMyaLwnfvhTYiwS2bfSl6/0a36KhU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7793570, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj6l5KACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmow3RAAiUSiU3a7e6mdG3GiFDfgERzyn/ygtL2N3ROlHlYQ9vX8N5o5\r\njZm3HgaOEreWnmNLG7w1bnEitmvK8mbtbzzwRtnYTy6l90LoKCZCwz3tkQs+\r\nlFlOMe6QKEvatkT6Q+fR5IVFhp9HbB8pu3HO1gkkXF/E3ETqMFh52TqqpyVK\r\nPrK1mgAP3XNZuNMql+NYAOl2930GMWziPYjI3EsiU8vEzkB1anuCVkLLzrTS\r\nYFwYBJ4wktbNYgukouMbNxj6O208yT1br/VHuWsiW7bpVEURZ6UmDcZWKfwd\r\nku865u8tnUeq+/t7UKEGrXVzGSdg4nwSWkMPfvF249jCYqLrS8Yt86kJmdVt\r\nNGCR5TUWGrRoZH50SHVs85P1ONhHwEKvFYcKJOMN5/ht9axX9nAWcqyB8oMN\r\nRl/HAkHwbQDq3ztTIRml3weQkDJT2rUjmjPWGF2rySzApH51AokfH0qv/ROV\r\nnceFpRqrkpaY0MkQ9ulxecjumwIYO6vVFvPIgKC6hWheywtn7Vs0MZs0EXy/\r\nFVZ3Vh29d1FWWHEI4hB2fluuCYUwl4fUzZiZi9nBt6Ygg+qlBtcLg6gwJ5uQ\r\nzo3I2y2T8FQGXftgBexTQplcRYYObJq+P7JBsumzWXGTo/0WwhtOzgCsgF00\r\nwSXC0gpzgFZaFaB226Fq2X5R/AO06OsOpok=\r\n=agL2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lightningcss.linux-arm-gnueabihf.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "05c23f1269321d4b072f6264a5c9cb6edd8a02bb", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "16.19.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm-gnueabihf_1.19.0_1676303946324_0.16744255416391907", "host": "s3://npm-registry-packages"}}, "1.20.0": {"name": "lightningcss-linux-arm-gnueabihf", "version": "1.20.0", "license": "MPL-2.0", "_id": "lightningcss-linux-arm-gnueabihf@1.20.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "3e999a07aa77c10a06c9dda9b507c7a737f8521f", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm-gnueabihf/-/lightningcss-linux-arm-gnueabihf-1.20.0.tgz", "fileCount": 4, "integrity": "sha512-/m+NDO1O6JCv7R9F0XWlXcintQHx4MPNU+kt8jZJO07LLdGwCfvjN31GVcwVPlStnnx/cU8uTTmax6g/Qu/whg==", "signatures": [{"sig": "MEUCIQDChOEi+agYsiCaWaID9kqm886uktwyxP3crvPKkKNIcQIgIlF6dKZf+FyUSBmGxHelP2xElj8HACDz2d65QXlP8dg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8030286, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkQGQ9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr3qQ//cOy4mNqRYaEJz66IRPGvzBtXExggyBG5OmNQY/JcM/LAXa9e\r\nBSdd2UXZuc13W14pDNEMeA7rlkTOrlxY/TcAstszUtb3oeguVL+JV+sqieIX\r\nPUh2ff5GJ5c3cNY8YOLCiTuVduOjT+FUfTE39eh5F5bTzus/CZ3UPv4tCinO\r\nZb/C06QUK+OS17HcrDAO3Cm+vyGbBAgTfdK90dGjwVxhLzmOZHjBx7FT6Ny/\r\nepjHOQiPg3O5kbrLztfsHFkMbujVPG6oRhIgSAx1hZyDWHFdwiscZFD91ohH\r\nHNmR0E5oHTcK5QKRu9ARd9ynQUBGxnu8Uw8ONizfGTFzD+jOuMJUQ0ZEW5VH\r\nEGhhZyzkd2q/YhBM6tiwkgnHfwLgsZ+YZEc2wKZR0cmz+ffjCqYHGk4ZG/JD\r\neyfpUR7dyvQd7THb66WRYP8wUvp1zuVdNXSlu9MEFyJzLd29pMQh1nSsnwSD\r\nZdeVotSJ2FKTWd0mlwuCkz8R7trcJAZOcZjIVY/RzMA698vxzGMyRVXNdTdM\r\nMexC68GebuHTXTY6XFybkEUYwxCPcLG9QNqDpW/UdLYrHYrdAgHMnPJPCidz\r\nYfs0H0NpHWsU7EWJUc+K2P2gwsGFFikZeU5cBBQ2MN51BaUXjhefLjGREnEs\r\n0ySt4YjGydAqONk3kdJg2R8lxE/4e7JJiyg=\r\n=tEP3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lightningcss.linux-arm-gnueabihf.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "4028d4d9026750b31b813ecb4ca5387dba8dd396", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.15.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm-gnueabihf_1.20.0_1681941565205_0.3440156531920482", "host": "s3://npm-registry-packages"}}, "1.21.0": {"name": "lightningcss-linux-arm-gnueabihf", "version": "1.21.0", "license": "MPL-2.0", "_id": "lightningcss-linux-arm-gnueabihf@1.21.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "bb74da249368006d822cd1a9831c21c38fe2e498", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm-gnueabihf/-/lightningcss-linux-arm-gnueabihf-1.21.0.tgz", "fileCount": 4, "integrity": "sha512-rk1cr+C2IA1QHvh0QJAPXsQ2vrwCksms7fgfaw43RIERBWa6EEM5p0/1CWhdZ5zrl9veUdY6NRaNGRJjJL0iLw==", "signatures": [{"sig": "MEYCIQC1XiO6gcdtrzzQhhZ0e4UPOYJ0duvvDf7PEwMxAonEywIhAKXbmKmnoV9Tdlnam5N+qAJBHujYgpPPAmHryJzGNEUe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8255182}, "main": "lightningcss.linux-arm-gnueabihf.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "11f4179f81041e14a04536d3bb0618f06de4e454", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.16.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm-gnueabihf_1.21.0_1686117492993_0.9001217719376269", "host": "s3://npm-registry-packages"}}, "1.21.1": {"name": "lightningcss-linux-arm-gnueabihf", "version": "1.21.1", "license": "MPL-2.0", "_id": "lightningcss-linux-arm-gnueabihf@1.21.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "7581480a3ad4a5f5b80e7a1b1db787ec086267e0", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm-gnueabihf/-/lightningcss-linux-arm-gnueabihf-1.21.1.tgz", "fileCount": 4, "integrity": "sha512-Ak12ti7D4Q9Tk3tX9fktCJVe+spP12/dOcebw67DBeZ3EQ4meIGTkFpl2ryZK8Z7kbIJNUsscVsz3zXW21/25A==", "signatures": [{"sig": "MEYCIQCA4Lh4aXlTAtyml7f+J75dn+y4oXUo9eblvUlhSCEkMAIhANo7tGYJHE0tkP4818MMO2erzmwkQ83MFW2buse5TPeD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8259518}, "main": "lightningcss.linux-arm-gnueabihf.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "a36c105af3bb83370f64e28a8cf98b7dea52a6e0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.16.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm-gnueabihf_1.21.1_1687660729579_0.3706926196425733", "host": "s3://npm-registry-packages"}}, "1.21.2": {"name": "lightningcss-linux-arm-gnueabihf", "version": "1.21.2", "license": "MPL-2.0", "_id": "lightningcss-linux-arm-gnueabihf@1.21.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "73285ccbd329039e3eac0510289743ee932e859f", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm-gnueabihf/-/lightningcss-linux-arm-gnueabihf-1.21.2.tgz", "fileCount": 4, "integrity": "sha512-83KqiPvvOuAocrUyuV+V3peLe5cfBZRHV312vSJq/OGluHzeg2meb36q73q1gIcz/qX9RdAcx6GlhZ/vwbGpnQ==", "signatures": [{"sig": "MEYCIQC1QWYVAixY9gLx1RJmVomVoKzrq3mVdC5Uc7TmgzKdLQIhAPIoJCVbBXjOGiO6hfEkwR+agBBPB8jYVE+kvWAPSN8i", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8267934}, "main": "lightningcss.linux-arm-gnueabihf.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "e4963a58a813e02b47a7dd0320defcf0c4182512", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.16.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm-gnueabihf_1.21.2_1688266093031_0.9956825254797643", "host": "s3://npm-registry-packages"}}, "1.21.3": {"name": "lightningcss-linux-arm-gnueabihf", "version": "1.21.3", "license": "MPL-2.0", "_id": "lightningcss-linux-arm-gnueabihf@1.21.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "b08dfc023e8bb9208dc097ee9a3a73b382c0aeb7", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm-gnueabihf/-/lightningcss-linux-arm-gnueabihf-1.21.3.tgz", "fileCount": 4, "integrity": "sha512-8CjgX7vXF8ue1ran+4Gs1Bs0URIklvuBUsLPBUwFbLZq+lvDd+kmfTWMRNYUpZ9CyVzsInzmLBvWskaRLrR/wA==", "signatures": [{"sig": "MEYCIQC19IaHuEHNpB6aL28drpyC1BOXd1eIqkt6O1UhCI6bYgIhAPgURq5xWzR/Vwm2Z4oxaCMDh1yxYPkEzz5/1KPrPcyx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8267934}, "main": "lightningcss.linux-arm-gnueabihf.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "83bab71e80781b4d2b128f5938099b2f0c8239bd", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.16.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm-gnueabihf_1.21.3_1688397345756_0.14618793774407468", "host": "s3://npm-registry-packages"}}, "1.21.4": {"name": "lightningcss-linux-arm-gnueabihf", "version": "1.21.4", "license": "MPL-2.0", "_id": "lightningcss-linux-arm-gnueabihf@1.21.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "2fd1df003d20b895c4333440474bd7dae1b1a311", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm-gnueabihf/-/lightningcss-linux-arm-gnueabihf-1.21.4.tgz", "fileCount": 4, "integrity": "sha512-fUYdxqr/Zmk2lbRKRP6HqUFxVPp12A+mU5v8DVgiwnbTmJn5vRvNUUPKEA+trCvyZ+pbSM4ikOS1OsPz8zeKyg==", "signatures": [{"sig": "MEUCIQDABYgXiP1J/OqashparfUvcUrgfzs9tYxEQPKwFen4QAIgO6FGV1EOO5DTtq1b1lCHcH7uObwedqhTJbgkRRgd6BU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8267934}, "main": "lightningcss.linux-arm-gnueabihf.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "bd517fde245dcce75a4b2eb21a25b147d54eb64a", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.16.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm-gnueabihf_1.21.4_1688441866510_0.7609256723070335", "host": "s3://npm-registry-packages"}}, "1.21.5": {"name": "lightningcss-linux-arm-gnueabihf", "version": "1.21.5", "license": "MPL-2.0", "_id": "lightningcss-linux-arm-gnueabihf@1.21.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "dcc78522f5297d18fbbe142a509bee3305f90907", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm-gnueabihf/-/lightningcss-linux-arm-gnueabihf-1.21.5.tgz", "fileCount": 4, "integrity": "sha512-xN6+5/JsMrbZHL1lPl+MiNJ3Xza12ueBKPepiyDCFQzlhFRTj7D0LG+cfNTzPBTO8KcYQynLpl1iBB8LGp3Xtw==", "signatures": [{"sig": "MEYCIQCy02UXGUJjHTOpkAs5J3uLaxmv5TkGIgFv7/5q41nGxQIhAM7uuSJMN3muiy4n5iOJm9iuaGeCI1v2Uqd0bt9Kf0Ku", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8267662}, "main": "lightningcss.linux-arm-gnueabihf.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "d44a73c3c000ccebbe8355b7cf9272236e573b4d", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.16.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm-gnueabihf_1.21.5_1688531412146_0.30386787243937174", "host": "s3://npm-registry-packages"}}, "1.21.6": {"name": "lightningcss-linux-arm-gnueabihf", "version": "1.21.6", "license": "MPL-2.0", "_id": "lightningcss-linux-arm-gnueabihf@1.21.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "b07f232da044a693f4a5b058dfbaec7d05669aa3", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm-gnueabihf/-/lightningcss-linux-arm-gnueabihf-1.21.6.tgz", "fileCount": 4, "integrity": "sha512-56JTC6YbmliaegbvJD1188z1jPC9CcIdiYO8sjSDLkxif3dhB+8YDOn5Wob7FW5rg6v0Qc2/7+ogNs/s73Na8Q==", "signatures": [{"sig": "MEUCIECtzU/XNqK7aiZTXJlZkhsp6Peg7uYVpap6CEsGVmI4AiEAj0cJyxNSeseDJLEyisVI9sHUS1NffDfBqriwdBOEDEk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8275822}, "main": "lightningcss.linux-arm-gnueabihf.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "f485eb51d13e677a674cb10e170c71138446b71d", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.17.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm-gnueabihf_1.21.6_1692511445509_0.5750421624940953", "host": "s3://npm-registry-packages"}}, "1.21.7": {"name": "lightningcss-linux-arm-gnueabihf", "version": "1.21.7", "license": "MPL-2.0", "_id": "lightningcss-linux-arm-gnueabihf@1.21.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "c4b30de126f3dfd4b9689b7f91d7181c65abe50c", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm-gnueabihf/-/lightningcss-linux-arm-gnueabihf-1.21.7.tgz", "fileCount": 4, "integrity": "sha512-biSRUDZNx7vubWP1jArw/qqfZKPGpkV/qzunasZzxmqijbZ43sW9faDQYxWNcxPWljJJdF/qs6qcurYFovWtrQ==", "signatures": [{"sig": "MEQCIELrvsnLr8iZX0yjk4sGoCCzTdzNQwMfQF5DGVGDoEvEAiAV1Ayk4qGJoWhaZ/qxSLLX872uZWTsCE7iV8+KLS2fYw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8275822}, "main": "lightningcss.linux-arm-gnueabihf.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "393013928888d47ec7684d52ed79f758d371bd7b", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.17.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm-gnueabihf_1.21.7_1692555044743_0.028246626729734547", "host": "s3://npm-registry-packages"}}, "1.21.8": {"name": "lightningcss-linux-arm-gnueabihf", "version": "1.21.8", "license": "MPL-2.0", "_id": "lightningcss-linux-arm-gnueabihf@1.21.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "523366a683d3545d3a36c133079ff6af0a3d95c0", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm-gnueabihf/-/lightningcss-linux-arm-gnueabihf-1.21.8.tgz", "fileCount": 4, "integrity": "sha512-9PMbqh8n/Xq0F4/j2NR/hHM2HRDiFXFSF0iOvV67pNWKJkHIO6mR8jBw/88Aro5Ye/ILsX5OuWsxIVJDFv0NXA==", "signatures": [{"sig": "MEUCIQCYVAl0VAKYGqPKW3KyGiHS72EKaWgD8Cgt+I4Q2ct1dwIgFL3O+gwGNYpRmyhLPCQIiztPXl19630dPhUDj3eyLxY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8277246}, "main": "lightningcss.linux-arm-gnueabihf.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "81cb0daee30dad924b14af7fbc739ddfe31d7f9e", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.17.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm-gnueabihf_1.21.8_1694407626760_0.0052173826486563435", "host": "s3://npm-registry-packages"}}, "1.22.0": {"name": "lightningcss-linux-arm-gnueabihf", "version": "1.22.0", "license": "MPL-2.0", "_id": "lightningcss-linux-arm-gnueabihf@1.22.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "1c4287ec7268dcee6d9dcccb3d0810ecdcd35b74", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm-gnueabihf/-/lightningcss-linux-arm-gnueabihf-1.22.0.tgz", "fileCount": 4, "integrity": "sha512-epQGvXIjOuxrZpMpMnRjK54ZqzhiHhCPLtHvw2fb6NeK2kK9YtF0wqmeTBiQ1AkbWfnnXGTstYaFNiadNK+StQ==", "signatures": [{"sig": "MEYCIQCiNhu4yjq7v7l0xbW1harx361tU0SlyAMvanOMHRUsMQIhAIagmtyOAHaM/lBzCS/WXB+7O/9cQQVGlSV1kUMRCLbB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8268134}, "main": "lightningcss.linux-arm-gnueabihf.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "7ff93ca5c69ba9df415e1e2319d275e2cec249d7", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.17.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm-gnueabihf_1.22.0_1694990937239_0.11295427816523396", "host": "s3://npm-registry-packages"}}, "1.22.1": {"name": "lightningcss-linux-arm-gnueabihf", "version": "1.22.1", "license": "MPL-2.0", "_id": "lightningcss-linux-arm-gnueabihf@1.22.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "134cf9b41abd44ec53d8bae02c9f6e4f257eb617", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm-gnueabihf/-/lightningcss-linux-arm-gnueabihf-1.22.1.tgz", "fileCount": 4, "integrity": "sha512-6rub98tYGfE5I5j0BP8t/2d4BZyu1S7Iz9vUkm0H26snAFHYxLfj3RbQn0xHHIePSetjLnhcg3QlfwUAkD/FYg==", "signatures": [{"sig": "MEUCIBAnNbs1nIcI04mYYZ/2wDHXyzvv1tll0LTJKgX4pSyFAiEAmYSfqvIX/yj+2UyBRq8eD0lJGFXpVEoEbKwSn2GNWzU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8265798}, "main": "lightningcss.linux-arm-gnueabihf.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "4994306f8f8d98a2b37433fced50efdacbbab901", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.18.2", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm-gnueabihf_1.22.1_1699395808459_0.6865407303328392", "host": "s3://npm-registry-packages"}}, "1.23.0": {"name": "lightningcss-linux-arm-gnueabihf", "version": "1.23.0", "license": "MPL-2.0", "_id": "lightningcss-linux-arm-gnueabihf@1.23.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "040e9718c9a9dc088322da33983a894564ffcb10", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm-gnueabihf/-/lightningcss-linux-arm-gnueabihf-1.23.0.tgz", "fileCount": 4, "integrity": "sha512-fBamf/bULvmWft9uuX+bZske236pUZEoUlaHNBjnueaCTJ/xd8eXgb0cEc7S5o0Nn6kxlauMBnqJpF70Bgq3zg==", "signatures": [{"sig": "MEYCIQDdkS9VA9KPZtftUTb5wehwx4QU4mI9LoiO4qFkGsFp9wIhAI7vtNgGJVfiVK77mnTHlQv56emJbVgvT5vKGHVA6AxB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8307086}, "main": "lightningcss.linux-arm-gnueabihf.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "b47f496a86075adcb6719aee3a8867e749a880b9", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.19.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm-gnueabihf_1.23.0_1705276071922_0.1405880913693216", "host": "s3://npm-registry-packages"}}, "1.24.0": {"name": "lightningcss-linux-arm-gnueabihf", "version": "1.24.0", "license": "MPL-2.0", "_id": "lightningcss-linux-arm-gnueabihf@1.24.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "b59678c2f9e29176740193a54b62cd2c28112e13", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm-gnueabihf/-/lightningcss-linux-arm-gnueabihf-1.24.0.tgz", "fileCount": 4, "integrity": "sha512-N55K6JqzMx7C0hYUu1YmWqhkHwzEJlkQRMA6phY65noO0I1LOAvP4wBIoFWrzRE+O6zL0RmXJ2xppqyTbk3sYw==", "signatures": [{"sig": "MEUCIBTE9c+w6opgvqLlC3yXqS+5mBjbBjE1eVaw5V4mx1YVAiEArTy9F/SJRL5s4+jv4bY+64KUtbLrGy5BjeNmvHYzVmE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8177166}, "main": "lightningcss.linux-arm-gnueabihf.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "30b9d79b3d277dfb90d90d78cd9182ac755ad6cf", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.19.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm-gnueabihf_1.24.0_1708648886992_0.35967370544448274", "host": "s3://npm-registry-packages"}}, "1.24.1": {"name": "lightningcss-linux-arm-gnueabihf", "version": "1.24.1", "license": "MPL-2.0", "_id": "lightningcss-linux-arm-gnueabihf@1.24.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "ba41556f4422a6a889553ad897898a314386153e", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm-gnueabihf/-/lightningcss-linux-arm-gnueabihf-1.24.1.tgz", "fileCount": 4, "integrity": "sha512-NLQLnBQW/0sSg74qLNI8F8QKQXkNg4/ukSTa+XhtkO7v3BnK19TS1MfCbDHt+TTdSgNEBv0tubRuapcKho2EWw==", "signatures": [{"sig": "MEUCIQC5Hc6tAXjTUtlMIosh/GuTSOMfEGHp4AkR3qGW6slwKwIgbOUfMFxszVQyFTvJlakJGlsfV7sqG8UqhHY3DZyd+JU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7966738}, "main": "lightningcss.linux-arm-gnueabihf.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "baa1a2b7fa52eeb3827f8edcc2e14de33cd69ad0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.19.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm-gnueabihf_1.24.1_1710476576538_0.28903800022120274", "host": "s3://npm-registry-packages"}}, "1.25.0": {"name": "lightningcss-linux-arm-gnueabihf", "version": "1.25.0", "license": "MPL-2.0", "_id": "lightningcss-linux-arm-gnueabihf@1.25.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "27ed80de74c29c45ccdfc07ea9db994bdff7f9f6", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm-gnueabihf/-/lightningcss-linux-arm-gnueabihf-1.25.0.tgz", "fileCount": 4, "integrity": "sha512-7KSVcjci9apHxUKNjiLKXn8hVQJqCtwFg5YNvTeKi/BM91A9lQTuO57RpmpPbRIb20Qm8vR7fZtL1iL5Yo3j9A==", "signatures": [{"sig": "MEYCIQCt0mWD5Lspr5Z09fHyd5E/FYWLpOAtaGBv9N8n6hy2xgIhAJWBgfbMaI5ySEXlC1Uud9IlTtv39YvODZ55NU7vnRer", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8100346}, "main": "lightningcss.linux-arm-gnueabihf.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "81d21b9f5201c6eb8365fbb4fa81cbd947c3474f", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.20.2", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm-gnueabihf_1.25.0_1715973754190_0.1474773787675856", "host": "s3://npm-registry-packages"}}, "1.25.1": {"name": "lightningcss-linux-arm-gnueabihf", "version": "1.25.1", "license": "MPL-2.0", "_id": "lightningcss-linux-arm-gnueabihf@1.25.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "b5395b55fb8a4cea87e2723c5c61a5124a0d4c42", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm-gnueabihf/-/lightningcss-linux-arm-gnueabihf-1.25.1.tgz", "fileCount": 4, "integrity": "sha512-tWyMgHFlHlp1e5iW3EpqvH5MvsgoN7ZkylBbG2R2LWxnvH3FuWCJOhtGcYx9Ks0Kv0eZOBud789odkYLhyf1ng==", "signatures": [{"sig": "MEUCIHDMtF14mghL9CEed7ESG9YBl6SvsRqlVNzQ2XiznNnPAiEAs2n62YPo9EKUc97M+P9M70j1kPc/L2am1dF7y6My3gQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8110458}, "main": "lightningcss.linux-arm-gnueabihf.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "fa6c015317e5cca29fa8a09b12d09ac53dcfbc77", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.20.2", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm-gnueabihf_1.25.1_1716617176432_0.6706143247635914", "host": "s3://npm-registry-packages"}}, "1.26.0": {"name": "lightningcss-linux-arm-gnueabihf", "version": "1.26.0", "license": "MPL-2.0", "_id": "lightningcss-linux-arm-gnueabihf@1.26.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "c14fe3aaf152b0318367030734d3e08fdf6add86", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm-gnueabihf/-/lightningcss-linux-arm-gnueabihf-1.26.0.tgz", "fileCount": 4, "integrity": "sha512-Aag9kqXqkyPSW+dXMgyWk66C984Nay2pY8Nws+67gHlDzV3cWh7TvFlzuaTaVFMVqdDTzN484LSK3u39zFBnzg==", "signatures": [{"sig": "MEUCIHsAVqm+WW8oVS380wfZ7yO1QXC3/ZLFrDJBYupgWYXyAiEAsMCgpCTZ0csfJOjsbWATAsGJ6NOeTwPZny6eAylGuE8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8049890}, "main": "lightningcss.linux-arm-gnueabihf.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "0bcd896e81a8a2c5d847f626a37e2cffea79e2a0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.20.4", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm-gnueabihf_1.26.0_1722958365226_0.06319535140437837", "host": "s3://npm-registry-packages"}}, "1.27.0": {"name": "lightningcss-linux-arm-gnueabihf", "version": "1.27.0", "license": "MPL-2.0", "_id": "lightningcss-linux-arm-gnueabihf@1.27.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "c7c16432a571ec877bf734fe500e4a43d48c2814", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm-gnueabihf/-/lightningcss-linux-arm-gnueabihf-1.27.0.tgz", "fileCount": 4, "integrity": "sha512-MUMRmtdRkOkd5z3h986HOuNBD1c2lq2BSQA1Jg88d9I7bmPGx08bwGcnB75dvr17CwxjxD6XPi3Qh8ArmKFqCA==", "signatures": [{"sig": "MEUCIQDZebXrhRJDKz7ILW1OzJZIRxq/JhGGnBR3vTIdmZTvMAIgbbHZKDPruO06twB4Vrij6KsfxqqV2pf4ojXVwqcwCR8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8057594}, "main": "lightningcss.linux-arm-gnueabihf.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "eb49015cf887ae720b80a2856ccbdf61bf940ef1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.20.4", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm-gnueabihf_1.27.0_1726023639286_0.898711693244939", "host": "s3://npm-registry-packages"}}, "1.28.0": {"name": "lightningcss-linux-arm-gnueabihf", "version": "1.28.0", "license": "MPL-2.0", "_id": "lightningcss-linux-arm-gnueabihf@1.28.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "7c076147f28be53c7bb978af3dc2ecd10bdc6093", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm-gnueabihf/-/lightningcss-linux-arm-gnueabihf-1.28.0.tgz", "fileCount": 4, "integrity": "sha512-c/dTKmu0ytEpbvDX+XUPpgeIxlZ6dhDKF8iutquCCiao+qsWBgbTfwEuyG/bIdXkYQLrA+NfFYS1WnxbpKHkyA==", "signatures": [{"sig": "MEYCIQCkhyedCVjqjAxqi07VOWzmsgOSivKyEQB359MucgN4fwIhAINHxLgRJKUefIWqk3kz9w2B2oUrBcICs8kVvwdsx9sU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8069607}, "main": "lightningcss.linux-arm-gnueabihf.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "8a67583105757e4a25378d65d243b87a345b2c2d", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"caniuse-lite": "^1.0.30001677", "lightningcss": "link:."}, "_nodeVersion": "18.20.4", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm-gnueabihf_1.28.0_1730668657987_0.23707122469843767", "host": "s3://npm-registry-packages"}}, "1.28.1": {"name": "lightningcss-linux-arm-gnueabihf", "version": "1.28.1", "license": "MPL-2.0", "_id": "lightningcss-linux-arm-gnueabihf@1.28.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "726dfdb2db6ba3a7bb2169e5724d826cb585a76d", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm-gnueabihf/-/lightningcss-linux-arm-gnueabihf-1.28.1.tgz", "fileCount": 4, "integrity": "sha512-p61kXwvhUDLLzkWHjzSFfUBW/F0iy3jr3CWi3k8SKULtJEsJXTI9DqRm9EixxMSe2AMBQBt4auTYiQL4B1N51A==", "signatures": [{"sig": "MEYCIQCQKN6R8hpy6MoIYTjy0WQP45jR31bYlD3xjeuGurENyAIhAOjVyPtCrTCEVi+gec/K+WHx9bWJ3qcWO2fjo4ZE00QH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8070247}, "main": "lightningcss.linux-arm-gnueabihf.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "a3390fd4140ca87f5035595d22bc9357cf72177e", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"caniuse-lite": "^1.0.30001677", "lightningcss": "link:."}, "_nodeVersion": "18.20.4", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm-gnueabihf_1.28.1_1730674696098_0.03462494461219512", "host": "s3://npm-registry-packages"}}, "1.28.2": {"name": "lightningcss-linux-arm-gnueabihf", "version": "1.28.2", "license": "MPL-2.0", "_id": "lightningcss-linux-arm-gnueabihf@1.28.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "c32595127b565690d854c9ff641831e4ad739ee1", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm-gnueabihf/-/lightningcss-linux-arm-gnueabihf-1.28.2.tgz", "fileCount": 4, "integrity": "sha512-DKMzpICBEKnL53X14rF7hFDu8KKALUJtcKdFUCW5YOlGSiwRSgVoRjM97wUm/E0NMPkzrTi/rxfvt7ruNK8meg==", "signatures": [{"sig": "MEUCIHqUQiuiyCZIuMGdJbOaP8jYToBDG4XEmS3plYaO/pPNAiEAtm7rfBzk0p14BmkdpFNfmsAzqfpC3ckb4vjtk5GR+lc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8067815}, "main": "lightningcss.linux-arm-gnueabihf.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "9b2e8bbe732d7c101272ddab03ac21b88bf55c4a", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"caniuse-lite": "^1.0.30001677", "lightningcss": "link:."}, "_nodeVersion": "18.20.5", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm-gnueabihf_1.28.2_1732512286307_0.6539660649363745", "host": "s3://npm-registry-packages"}}, "1.29.0": {"name": "lightningcss-linux-arm-gnueabihf", "version": "1.29.0", "license": "MPL-2.0", "_id": "lightningcss-linux-arm-gnueabihf@1.29.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "f26b184eb7bb94e68044615332a04ded473f9a00", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm-gnueabihf/-/lightningcss-linux-arm-gnueabihf-1.29.0.tgz", "fileCount": 4, "integrity": "sha512-KvIyB4ucvLI4169RScKD/e50y6dQE2wxsaNZOW9ONHkaf1BxJ805cye/N5SBQmnKkQHNjAt2dkTvZzjWD6Xq+w==", "signatures": [{"sig": "MEQCIAf0eRONbzgtnqgNVcb+NVO8jD+7l+zMIFDdM6pwDAyUAiAjfkfKr2UDUxEllJzEda/hyIf95OmVmXBsh2UHmDQWxg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7890839}, "main": "lightningcss.linux-arm-gnueabihf.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "222fa9f7ad8b4004afd7a30c2f99cab3c8621191", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"caniuse-lite": "^1.0.30001677", "lightningcss": "link:."}, "_nodeVersion": "20.18.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm-gnueabihf_1.29.0_1736401658275_0.3360477377642648", "host": "s3://npm-registry-packages-npm-production"}}, "1.29.1": {"name": "lightningcss-linux-arm-gnueabihf", "version": "1.29.1", "license": "MPL-2.0", "_id": "lightningcss-linux-arm-gnueabihf@1.29.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "b80e9c4dd75652bec451ffd4d5779492a01791ff", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm-gnueabihf/-/lightningcss-linux-arm-gnueabihf-1.29.1.tgz", "fileCount": 4, "integrity": "sha512-sD32pFvlR0kDlqsOZmYqH/68SqUMPNj+0pucGxToXZi4XZgZmqeX/NkxNKCPsswAXU3UeYgDSpGhu05eAufjDg==", "signatures": [{"sig": "MEUCIQDXFTxGkBxz6n7AS/X4WIhfYmS/RbRaEYyYW/ZBmfDuYgIgA5qoD74y1i3mfkm6GlP2GZT6xZ2SY68F9GIG4k/X36c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7891975}, "main": "lightningcss.linux-arm-gnueabihf.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "b10f9baf8878411bf2b09dfe8d64ba09ef7a4eac", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"caniuse-lite": "^1.0.30001677", "lightningcss": "link:."}, "_nodeVersion": "20.18.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm-gnueabihf_1.29.1_1736445753239_0.5592833264046218", "host": "s3://npm-registry-packages-npm-production"}}, "1.29.2": {"name": "lightningcss-linux-arm-gnueabihf", "version": "1.29.2", "license": "MPL-2.0", "_id": "lightningcss-linux-arm-gnueabihf@1.29.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "5c60bbf92b39d7ed51e363f7b98a7111bf5914a1", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm-gnueabihf/-/lightningcss-linux-arm-gnueabihf-1.29.2.tgz", "fileCount": 4, "integrity": "sha512-IRUrOrAF2Z+KExdExe3Rz7NSTuuJ2HvCGlMKoquK5pjvo2JY4Rybr+NrKnq0U0hZnx5AnGsuFHjGnNT14w26sg==", "signatures": [{"sig": "MEUCIFaNFmFYh/Wi5iv8JFgP9Gbo/EX0eVuH5sUmjuckUD4oAiEA3cpnfZgodqctRolnItFpR0GaxFZ7GIF1AycWLTQ3MD0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 7894162}, "main": "lightningcss.linux-arm-gnueabihf.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "f2303df29448a55c5f4f284f747eb53760769fe4", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "20.18.3", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm-gnueabihf_1.29.2_1741242112902_0.9445706359050214", "host": "s3://npm-registry-packages-npm-production"}}, "1.29.3": {"name": "lightningcss-linux-arm-gnueabihf", "version": "1.29.3", "license": "MPL-2.0", "_id": "lightningcss-linux-arm-gnueabihf@1.29.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "012e42661f26a4b61fffa1c61ee4022a5179aa5a", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm-gnueabihf/-/lightningcss-linux-arm-gnueabihf-1.29.3.tgz", "fileCount": 4, "integrity": "sha512-UhgZ/XVNfXQVEJrMIWeK1Laj8KbhjbIz7F4znUk7G4zeGw7TRoJxhb66uWrEsonn1+O45w//0i0Fu0wIovYdYg==", "signatures": [{"sig": "MEUCIQD9veWEIeFZrxdAT9NUcdMpHYSVaF/tN4zE6fgqGu/cGAIgLosyGWdcBVy4Ie+rl1DVC+AX1hdnm8kiuZkPEIazLf0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 7897530}, "main": "lightningcss.linux-arm-gnueabihf.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "80eb8617c5f8f5519ed85bd3eb6d8953f4d32493", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "20.18.3", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm-gnueabihf_1.29.3_1741974589324_0.335934028341335", "host": "s3://npm-registry-packages-npm-production"}}, "1.30.0": {"name": "lightningcss-linux-arm-gnueabihf", "version": "1.30.0", "license": "MPL-2.0", "_id": "lightningcss-linux-arm-gnueabihf@1.30.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "f3ebc3f3a8937db19d3cb1ea4a5c34651ca57059", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm-gnueabihf/-/lightningcss-linux-arm-gnueabihf-1.30.0.tgz", "fileCount": 4, "integrity": "sha512-3B5val/f61unLgfZHEfkZGzunlyyL76l8xRoxFx+G0uwxK7rvaFcnkyf6k4Zto2STVj05FsLs+aTZoTqslPaug==", "signatures": [{"sig": "MEUCIQCVMciWwpp8r+QUbIHjrVdfjrRjM+xg0t8Tv8pDAxmpEAIgGAgl8YSdtoDhhFXW4ZWz1izfn23dQCbKeSLcxWNhZA4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 7943274}, "main": "lightningcss.linux-arm-gnueabihf.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "474c67c046b4a54217166057296fa4ca28764858", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "20.19.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm-gnueabihf_1.30.0_1746945547829_0.09036336071420714", "host": "s3://npm-registry-packages-npm-production"}}, "1.30.1": {"name": "lightningcss-linux-arm-gnueabihf", "version": "1.30.1", "license": "MPL-2.0", "description": "A CSS parser, transformer, and minifier written in Rust", "main": "lightningcss.linux-arm-gnueabihf.node", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "git+https://github.com/parcel-bundler/lightningcss.git"}, "engines": {"node": ">= 12.0.0"}, "resolutions": {"lightningcss": "link:."}, "os": ["linux"], "cpu": ["arm"], "_id": "lightningcss-linux-arm-gnueabihf@1.30.1", "gitHead": "f2dc67c4d3fe92f26693c02366db1e60cae0db27", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-MjxUShl1v8pit+6D/zSPq9S9dQ2NPFSQwGvxBCYaBYLPlCWuPh9/t1MRS8iUaR8i+a6w7aps+B4N0S1TYP/R+Q==", "shasum": "1f5ecca6095528ddb649f9304ba2560c72474908", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm-gnueabihf/-/lightningcss-linux-arm-gnueabihf-1.30.1.tgz", "fileCount": 4, "unpackedSize": 7943274, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIBZEgwHAhGW4TYKCGRswCVpERJEHzW7agRHPzmUqUgumAiEAlxZmpmNmUv8uu5NTQ/DFfiAxoobmRHsGf22Vs84LeHs="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/lightningcss-linux-arm-gnueabihf_1.30.1_1747193918199_0.8470997428320746"}, "_hasShrinkwrap": false}}, "time": {"created": "2022-09-08T05:19:06.229Z", "modified": "2025-05-14T03:38:38.623Z", "1.14.0": "2022-09-08T05:19:06.622Z", "1.15.0": "2022-09-15T04:16:26.123Z", "1.15.1": "2022-09-16T03:49:16.503Z", "1.16.0": "2022-09-20T03:23:07.441Z", "1.16.1": "2022-11-06T18:02:00.736Z", "1.17.0": "2022-11-29T16:47:43.157Z", "1.17.1": "2022-11-30T17:30:30.694Z", "1.18.0": "2023-01-04T17:00:13.812Z", "1.19.0": "2023-02-13T15:59:06.498Z", "1.20.0": "2023-04-19T21:59:25.520Z", "1.21.0": "2023-06-07T05:58:13.235Z", "1.21.1": "2023-06-25T02:38:49.848Z", "1.21.2": "2023-07-02T02:48:13.339Z", "1.21.3": "2023-07-03T15:15:46.105Z", "1.21.4": "2023-07-04T03:37:46.812Z", "1.21.5": "2023-07-05T04:30:12.416Z", "1.21.6": "2023-08-20T06:04:05.867Z", "1.21.7": "2023-08-20T18:10:45.011Z", "1.21.8": "2023-09-11T04:47:06.983Z", "1.22.0": "2023-09-17T22:48:57.536Z", "1.22.1": "2023-11-07T22:23:28.865Z", "1.23.0": "2024-01-14T23:47:52.164Z", "1.24.0": "2024-02-23T00:41:27.335Z", "1.24.1": "2024-03-15T04:22:56.882Z", "1.25.0": "2024-05-17T19:22:34.411Z", "1.25.1": "2024-05-25T06:06:16.729Z", "1.26.0": "2024-08-06T15:32:45.537Z", "1.27.0": "2024-09-11T03:00:39.640Z", "1.28.0": "2024-11-03T21:17:38.281Z", "1.28.1": "2024-11-03T22:58:16.389Z", "1.28.2": "2024-11-25T05:24:46.558Z", "1.29.0": "2025-01-09T05:47:38.517Z", "1.29.1": "2025-01-09T18:02:33.728Z", "1.29.2": "2025-03-06T06:21:53.179Z", "1.29.3": "2025-03-14T17:49:49.595Z", "1.30.0": "2025-05-11T06:39:08.126Z", "1.30.1": "2025-05-14T03:38:38.467Z"}, "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "license": "MPL-2.0", "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "repository": {"type": "git", "url": "git+https://github.com/parcel-bundler/lightningcss.git"}, "description": "A CSS parser, transformer, and minifier written in Rust", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "This is the armv7-unknown-linux-gnueabihf build of lightningcss. See https://github.com/parcel-bundler/lightningcss for details.", "readmeFilename": "README.md"}