{"_id": "caseless", "_rev": "35-c404bc35acd6d853bdc3e8cdcde15c70", "name": "caseless", "description": "Caseless object set/get/has, very useful when working with HTTP headers.", "dist-tags": {"latest": "0.12.0"}, "versions": {"0.1.0": {"name": "caseless", "version": "0.1.0", "description": "Caseless object set/get/has, very useful when working with HTTP headers.", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "https://github.com/mikeal/caseless"}, "keywords": ["headers", "http", "caseless"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD", "bugs": {"url": "https://github.com/mikeal/caseless/issues"}, "_id": "caseless@0.1.0", "dist": {"shasum": "eb7ab59f1fb317b4a1620b4093056d18aa548d84", "tarball": "https://registry.npmjs.org/caseless/-/caseless-0.1.0.tgz", "integrity": "sha512-DzVSOJnClBmEPZ8YyrlG/sZyJbJ2SLca4NMvRdfNUQorCZSDhvi5ezU3IiylgIqo20BgimTqVmxu99QTtf/4RQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCuplybMw1yweevQ4M6F1Bahrk6pCTiu6LMnE7kmptGDQIhAI4ihfGhM6Rh1afTV/sqMiUMocqdtqWYc+V2Fu2Fut2B"}]}, "_from": ".", "_npmVersion": "1.3.2", "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "directories": {}}, "0.2.0": {"name": "caseless", "version": "0.2.0", "description": "Caseless object set/get/has, very useful when working with HTTP headers.", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "https://github.com/mikeal/caseless"}, "keywords": ["headers", "http", "caseless"], "test": "node test.js", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD", "bugs": {"url": "https://github.com/mikeal/caseless/issues"}, "devDependencies": {"tape": "^2.10.2"}, "homepage": "https://github.com/mikeal/caseless", "_id": "caseless@0.2.0", "dist": {"shasum": "f8da680d08353983334b69af1063697bae982c0c", "tarball": "https://registry.npmjs.org/caseless/-/caseless-0.2.0.tgz", "integrity": "sha512-1cqcQ6TBUrO9vq79AezydqhHRbrKFLYwnyvjzeSqQ+9iuAknzBgS8aOKdhDs/5OuveEO3A2mFEONdR11zcvmZw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCiXX/e0FggyAgyDUiRXQ5/9SJSPP/CrnWZx0ypw82uMwIgAuCrz3VKjxW08eOGIHAyKn76h6MHMeJSPUZkNogkt6M="}]}, "_from": ".", "_npmVersion": "1.4.4", "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "directories": {}}, "0.3.0": {"name": "caseless", "version": "0.3.0", "description": "Caseless object set/get/has, very useful when working with HTTP headers.", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "https://github.com/mikeal/caseless"}, "keywords": ["headers", "http", "caseless"], "test": "node test.js", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD", "bugs": {"url": "https://github.com/mikeal/caseless/issues"}, "devDependencies": {"tape": "^2.10.2"}, "homepage": "https://github.com/mikeal/caseless", "_id": "caseless@0.3.0", "dist": {"shasum": "534e97916387d3b706b64fdebbac46438450934f", "tarball": "https://registry.npmjs.org/caseless/-/caseless-0.3.0.tgz", "integrity": "sha512-Z0AY93yggJSRqqgYSdRUX1srMYHX2c3be+ZTrQ3KRAT9uAoVfPea9nqF6ESDRzd8j8L1dHMtRK6ME0TEZ6yRYw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFo1/eCgTMbqNec4ZgKZoz/5K7d8tSDuZ+svHmnrndUdAiBEMqt9gD4DxBHhO3LzEJ4ZcdiA9A231b9xX5mUYJ2lPg=="}]}, "_from": ".", "_npmVersion": "1.4.4", "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "directories": {}}, "0.4.0": {"name": "caseless", "version": "0.4.0", "description": "Caseless object set/get/has, very useful when working with HTTP headers.", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "https://github.com/mikeal/caseless"}, "keywords": ["headers", "http", "caseless"], "test": "node test.js", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD", "bugs": {"url": "https://github.com/mikeal/caseless/issues"}, "devDependencies": {"tape": "^2.10.2"}, "homepage": "https://github.com/mikeal/caseless", "_id": "caseless@0.4.0", "_shasum": "0edc5225a0f08483f8979dcc58bdf4881ff50d5c", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "dist": {"shasum": "0edc5225a0f08483f8979dcc58bdf4881ff50d5c", "tarball": "https://registry.npmjs.org/caseless/-/caseless-0.4.0.tgz", "integrity": "sha512-u//wDpE06Cvw7PU3OVNaJu+q0DUole8XTFDA+4EjP1ljWmtQ6kFrzLtXviOBjjoe7nZUkyMfgc9vzzFIyrF6sQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDFEBmfmNYcwdBIiyWlUziwPMRo/+7Ub3P3iTJlfrsmYgIgG/We3UGj5VVwjgRzr+QP+3f2c5R0I+CAU6SUSgxHpuI="}]}, "directories": {}}, "0.5.0": {"name": "caseless", "version": "0.5.0", "description": "Caseless object set/get/has, very useful when working with HTTP headers.", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "https://github.com/mikeal/caseless"}, "keywords": ["headers", "http", "caseless"], "test": "node test.js", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD", "bugs": {"url": "https://github.com/mikeal/caseless/issues"}, "devDependencies": {"tape": "^2.10.2"}, "homepage": "https://github.com/mikeal/caseless", "_id": "caseless@0.5.0", "_shasum": "2b77c9c988f22f99e179900d3119a1092ed21ce9", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "dist": {"shasum": "2b77c9c988f22f99e179900d3119a1092ed21ce9", "tarball": "https://registry.npmjs.org/caseless/-/caseless-0.5.0.tgz", "integrity": "sha512-Ik/5ZTJ3MVcdGobxvHDY/fKzjHkyTFvFd3/s/SFw/iFJrz7CsQ232P+TNsDG3+ze4MRShL/u7eEuWrX4HkiPAA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAI7OvkNNqroTejgVhFjLUzbhXP+tv1/sfvg0n5jyO+cAiA0dhHCWzROyT7/ctyoJxVrD6Zyc3kqJRozuygDil3J9g=="}]}, "directories": {}}, "0.6.0": {"name": "caseless", "version": "0.6.0", "description": "Caseless object set/get/has, very useful when working with HTTP headers.", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "https://github.com/mikeal/caseless"}, "keywords": ["headers", "http", "caseless"], "test": "node test.js", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD", "bugs": {"url": "https://github.com/mikeal/caseless/issues"}, "devDependencies": {"tape": "^2.10.2"}, "homepage": "https://github.com/mikeal/caseless", "_id": "caseless@0.6.0", "_shasum": "8167c1ab8397fb5bb95f96d28e5a81c50f247ac4", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "dist": {"shasum": "8167c1ab8397fb5bb95f96d28e5a81c50f247ac4", "tarball": "https://registry.npmjs.org/caseless/-/caseless-0.6.0.tgz", "integrity": "sha512-/X9C8oGbZJ95LwJyK4XvN9GSBgw/rqBnUg6mejGhf/GNfJukt5tzOXP+CJicXdWSqAX0ETaufLDxXuN2m4/mDg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCCrUBu1Kua3wu/gjDunHFhO4rDtbXiqwxbvg+zuWewewIgHwCdEMijMpeeinM9Pu8rdeli5dgal2aQo5fkFaDrvD0="}]}, "directories": {}}, "0.7.0": {"name": "caseless", "version": "0.7.0", "description": "Caseless object set/get/has, very useful when working with HTTP headers.", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "https://github.com/mikeal/caseless"}, "keywords": ["headers", "http", "caseless"], "test": "node test.js", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD", "bugs": {"url": "https://github.com/mikeal/caseless/issues"}, "devDependencies": {"tape": "^2.10.2"}, "gitHead": "9359e958f196e1aaa2180b551151b2c25826b8f5", "homepage": "https://github.com/mikeal/caseless", "_id": "caseless@0.7.0", "_shasum": "cbd705ae6229158bb0bc971bf7d7a04bdbd51ff8", "_from": ".", "_npmVersion": "2.0.0", "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "dist": {"shasum": "cbd705ae6229158bb0bc971bf7d7a04bdbd51ff8", "tarball": "https://registry.npmjs.org/caseless/-/caseless-0.7.0.tgz", "integrity": "sha512-Iw5tqaVVKeum+J4kBskttLYudGFgKyKezspzwnUfkv/RQHt+gFmQ/59hqVI53hpkjq7MlfFV17v7NvVmMWWcrw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAX2HGEtO2/Y+Nly7pUSPwEQF7Z6Mz6AcbXq1RtPit4TAiAvX+775GcWn+Q24LY9kIjQDJlDL2VwhQe12Xnab60DXg=="}]}, "directories": {}}, "0.8.0": {"name": "caseless", "version": "0.8.0", "description": "Caseless object set/get/has, very useful when working with HTTP headers.", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "https://github.com/mikeal/caseless"}, "keywords": ["headers", "http", "caseless"], "test": "node test.js", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD", "bugs": {"url": "https://github.com/mikeal/caseless/issues"}, "devDependencies": {"tape": "^2.10.2"}, "gitHead": "1bfbf01d4481c057738a64ba284749222a944176", "homepage": "https://github.com/mikeal/caseless", "_id": "caseless@0.8.0", "_shasum": "5bca2881d41437f54b2407ebe34888c7b9ad4f7d", "_from": ".", "_npmVersion": "2.0.0", "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "dist": {"shasum": "5bca2881d41437f54b2407ebe34888c7b9ad4f7d", "tarball": "https://registry.npmjs.org/caseless/-/caseless-0.8.0.tgz", "integrity": "sha512-RtOAnto0D6IIVC+dU+vHyH0tXs6BfZ/s0kaaT5+6loiwoi9O3+J5iASBkliQHrd8GSRNGERS7f8pgaRc895bAg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAGQFGWwYWw0zEKvoLnzKmb1rprLqLKQwNSseVzUtAWmAiBMrQ2lGnDpIxOcmNkJ4owqK9ytwe6CjWCPBQCeLGJNhA=="}]}, "directories": {}}, "0.9.0": {"name": "caseless", "version": "0.9.0", "description": "Caseless object set/get/has, very useful when working with HTTP headers.", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "https://github.com/mikeal/caseless"}, "keywords": ["headers", "http", "caseless"], "test": "node test.js", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD", "bugs": {"url": "https://github.com/mikeal/caseless/issues"}, "devDependencies": {"tape": "^2.10.2"}, "gitHead": "5ff0ccebbbf14dad5dc91def1f274887801db3e3", "homepage": "https://github.com/mikeal/caseless", "_id": "caseless@0.9.0", "_shasum": "b7b65ce6bf1413886539cfd533f0b30effa9cf88", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "nylen", "email": "<EMAIL>"}, "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}], "dist": {"shasum": "b7b65ce6bf1413886539cfd533f0b30effa9cf88", "tarball": "https://registry.npmjs.org/caseless/-/caseless-0.9.0.tgz", "integrity": "sha512-6msL6rlJApxKoPTh2QkZF+pn7/4fqQZAJb8s5noLh/GQxFGnGYfvFaz0JGNFOip/JBM3oP3RjCdwyc4uDXWJwQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDfs36yxvITtal3Ixu01fbOn3qOAQy2bD5aFPKNrmzjoAiA5+USIsV53AoQGkutaeUJCu6bugvjhQUZe6BCbLGc+8g=="}]}, "directories": {}}, "0.10.0": {"name": "caseless", "version": "0.10.0", "description": "Caseless object set/get/has, very useful when working with HTTP headers.", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "https://github.com/mikeal/caseless"}, "keywords": ["headers", "http", "caseless"], "test": "node test.js", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD", "bugs": {"url": "https://github.com/mikeal/caseless/issues"}, "devDependencies": {"tape": "^2.10.2"}, "gitHead": "9c85efef6c37d48923b79f3f282441414dd691c9", "homepage": "https://github.com/mikeal/caseless", "_id": "caseless@0.10.0", "_shasum": "ed6b2719adcd1fd18f58dc081c0f1a5b43963909", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "nylen", "email": "<EMAIL>"}, "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}], "dist": {"shasum": "ed6b2719adcd1fd18f58dc081c0f1a5b43963909", "tarball": "https://registry.npmjs.org/caseless/-/caseless-0.10.0.tgz", "integrity": "sha512-1meZDjkziCX2rIL72FnjndyP96yCOfKhkmzr8Umi0xOw493PlVoOgfOmjScp9YaFJU1Acpwe9zeHBMVH5p4xPA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIARaXxxUnlWqSdmI0qLlBu7ezVc2xA05zV4ByVf5AT0HAiEA68qN193zgVrqKMuxdVMKKT3F4AvSWpIb2wJDjdC6uvQ="}]}, "directories": {}}, "0.11.0": {"name": "caseless", "version": "0.11.0", "description": "Caseless object set/get/has, very useful when working with HTTP headers.", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "git+https://github.com/mikeal/caseless.git"}, "keywords": ["headers", "http", "caseless"], "test": "node test.js", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/mikeal/caseless/issues"}, "devDependencies": {"tape": "^2.10.2"}, "gitHead": "c578232a02cc2b46b6da8851caf57fdbfac89ff5", "homepage": "https://github.com/mikeal/caseless#readme", "_id": "caseless@0.11.0", "_shasum": "715b96ea9841593cc33067923f5ec60ebda4f7d7", "_from": ".", "_npmVersion": "2.8.3", "_nodeVersion": "1.8.1", "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "715b96ea9841593cc33067923f5ec60ebda4f7d7", "tarball": "https://registry.npmjs.org/caseless/-/caseless-0.11.0.tgz", "integrity": "sha512-ODLXH644w9C2fMPAm7bMDQ3GRvipZWZfKc+8As6hIadRIelE0n0xZuN38NS6kiK3KPEVrpymmQD8bvncAHWQkQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICfzl5F7m+qsN4BTHw4c2ZQ5n+YFWSMFJD65BkxJEQZvAiB/JtczBYO2QoJcX+G7uzLLmvDdj8UMod0pvcTO9b1oBw=="}]}, "directories": {}}, "0.12.0": {"name": "caseless", "version": "0.12.0", "description": "Caseless object set/get/has, very useful when working with HTTP headers.", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "git+https://github.com/mikeal/caseless.git"}, "keywords": ["headers", "http", "caseless"], "test": "node test.js", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/mikeal/caseless/issues"}, "devDependencies": {"tape": "^2.10.2"}, "gitHead": "af91df7878a8b53cf3dc2e9a086dc57ba8301649", "homepage": "https://github.com/mikeal/caseless#readme", "_id": "caseless@0.12.0", "_shasum": "1b681c21ff84033c826543090689420d187151dc", "_from": ".", "_npmVersion": "3.10.9", "_nodeVersion": "6.9.2", "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "1b681c21ff84033c826543090689420d187151dc", "tarball": "https://registry.npmjs.org/caseless/-/caseless-0.12.0.tgz", "integrity": "sha512-4tYFyifaFfGacoiObjJegolkwSU4xQNGbVgUiNYVUxbQ2x2lUsFvY4hVgVzGiIe6WLOPqycWXA40l+PWsxthUw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCsjYgqrNe9Nn0Tn/eyBBeG5iEi88Id1jA+jhKPbqfYSAIgYdTIeevn+65cRkzxFT4ctRccH+yoxnKg0L6zufBp9Jg="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/caseless-0.12.0.tgz_1485466648253_0.3714302028529346"}, "directories": {}}}, "readme": "## Caseless -- wrap an object to set and get property with caseless semantics but also preserve caseing.\n\nThis library is incredibly useful when working with HTTP headers. It allows you to get/set/check for headers in a caseless manner while also preserving the caseing of headers the first time they are set.\n\n## Usage\n\n```javascript\nvar headers = {}\n  , c = caseless(headers)\n  ;\nc.set('a-Header', 'asdf')\nc.get('a-header') === 'asdf'\n```\n\n## has(key)\n\nHas takes a name and if it finds a matching header will return that header name with the preserved caseing it was set with.\n\n```javascript\nc.has('a-header') === 'a-Header'\n```\n\n## set(key, value[, clobber=true])\n\nSet is fairly straight forward except that if the header exists and clobber is disabled it will add `','+value` to the existing header.\n\n```javascript\nc.set('a-Header', 'fdas')\nc.set('a-HEADER', 'more', false)\nc.get('a-header') === 'fdsa,more'\n```\n\n## swap(key)\n\nSwaps the casing of a header with the new one that is passed in.\n\n```javascript\nvar headers = {}\n  , c = caseless(headers)\n  ;\nc.set('a-Header', 'fdas')\nc.swap('a-HEADER')\nc.has('a-header') === 'a-HEADER'\nheaders === {'a-HEADER': 'fdas'}\n```\n", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}], "time": {"modified": "2024-05-06T13:40:15.812Z", "created": "2013-09-13T08:24:37.793Z", "0.1.0": "2013-09-13T08:24:41.536Z", "0.2.0": "2014-03-19T00:24:45.260Z", "0.3.0": "2014-03-19T00:32:45.686Z", "0.4.0": "2014-07-24T02:20:38.463Z", "0.5.0": "2014-08-11T22:09:39.169Z", "0.6.0": "2014-08-11T22:11:11.459Z", "0.7.0": "2014-10-29T19:02:25.783Z", "0.8.0": "2014-11-21T01:43:50.954Z", "0.9.0": "2015-01-12T13:16:27.155Z", "0.10.0": "2015-03-13T16:44:33.260Z", "0.11.0": "2015-06-25T21:39:22.129Z", "0.12.0": "2017-01-26T21:37:30.100Z"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/mikeal/caseless.git"}, "homepage": "https://github.com/mikeal/caseless#readme", "keywords": ["headers", "http", "caseless"], "bugs": {"url": "https://github.com/mikeal/caseless/issues"}, "license": "Apache-2.0", "readmeFilename": "README.md", "users": {"sprjrx": true, "chrisdickinson": true, "wangnan0610": true, "akiva": true, "ganeshkbhat": true, "thevikingcoder": true}}