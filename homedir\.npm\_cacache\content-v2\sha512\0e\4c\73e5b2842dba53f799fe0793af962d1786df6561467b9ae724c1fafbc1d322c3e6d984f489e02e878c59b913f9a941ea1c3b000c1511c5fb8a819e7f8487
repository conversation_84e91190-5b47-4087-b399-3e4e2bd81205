{"_id": "@xtuc/ieee754", "_rev": "1-eccdc5a77ecdb411d558f6d1ea08b9a1", "name": "@xtuc/ieee754", "dist-tags": {"latest": "1.2.0"}, "versions": {"1.2.0": {"name": "@xtuc/ieee754", "description": "Read/write IEEE754 floating point numbers from/to a Buffer or array-like object", "version": "1.2.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "devDependencies": {"airtap": "0.0.7", "standard": "*", "tape": "^4.0.0", "@babel/cli": "^7.0.0-beta.54", "@babel/core": "^7.0.0-beta.54", "@babel/plugin-transform-modules-commonjs": "^7.0.0-beta.54"}, "keywords": ["IEEE 754", "buffer", "convert", "floating point", "ieee754"], "license": "BSD-3-<PERSON><PERSON>", "main": "dist/index.cjs.js", "module": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/ieee754.git"}, "scripts": {"test": "standard && npm run test-node && npm run test-browser", "test-browser": "airtap -- test/*.js", "test-browser-local": "airtap --local -- test/*.js", "test-node": "tape test/*.js"}, "prepublish": "babel --plugins @babel/plugin-transform-modules-commonjs index.js -o dist/index.cjs.js", "gitHead": "25da4f98d46eb47072792e23adfd92d02c16c6d7", "bugs": {"url": "https://github.com/feross/ieee754/issues"}, "homepage": "https://github.com/feross/ieee754#readme", "_id": "@xtuc/ieee754@1.2.0", "_npmVersion": "5.5.1", "_nodeVersion": "9.3.0", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-DX8nKgqcGwsc0eJSqYt5lwP4DH5FlHnmuWWBRy7X0NcaGR0ZtuyeESgMwTYVEtxmsNGY+qit4QYT/MIYTOTPeA==", "shasum": "eef014a3145ae477a1cbc00cd1e552336dceb790", "tarball": "https://registry.npmjs.org/@xtuc/ieee754/-/ieee754-1.2.0.tgz", "fileCount": 6, "unpackedSize": 8575, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbULgFCRA9TVsSAnZWagAAQ6EP/0DW0e9dCD8Uwlo0JKN2\ngcsushPj1QAdnlsL2jIyWDs0h6WKRVEct2Bx28Go9LC3MYLd8RjxhhnrsQ5N\nYQcgqgJ1peuN2IvE+veSd098RryELJJ1T82r6TgOqkg133l/K5P6Rjqkl8Sa\n9Qxu5E5egW6e2d7dNSUnl4Po/NxogwWmCzxHR3hmWYvLug64tzJpGp8mYtsp\n4lCXAMIHrHEb8FS667QQXv8iTCNHNUXqiulyejqlDwuREGWsCNP+owvAU9zc\nJOH0ULQiUAs2bMBok/Zeis7Ro8fuUKx0PhfRshcu3Bwt3cvtb36Ryl/SwGoG\ndE5P3Q6oFGbQgMUqcSe0m/4lTHnD3/VJqDxVYt9pV+38RH8oF0lF4sQU76kw\nCjr9OtuTHKh4w58up6Hywn41bHDiad+3V5aQMOjMhQyoeeiFej6YmTKbzShj\n1AveVvs/2PQXICiPMiiWgmMd2lW8UEfP9jyWIb0yIMYnT0nyE4oBb5np2WA6\nhcWI17VaB5TBpltw0i7ClplLbGBXViT6lmxcxhQ+SmkD7lkUDWYXmCP+a0MB\nXWT/X6VVrhvT8Fit3Ii/x7+Fhl/q8hlMDsbrzilNs9PDGxujDpxuQGkA00Gl\nNC3PgVDAAIFpv35EpQJoxuseKLihErLnFa2eyTnSMVZMeNWjLCOuLAddt+Ab\n8yPF\r\n=mxnY\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC443hI6c8XxzN84UO773+n8YiIU0LpHSPL3r+5zPUNGgIhAMhGOUhe6/vMQn4q7Ds/DVRNPP8sd+7ofLEArkEUvofL"}]}, "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ieee754_1.2.0_1532016645902_0.15972107691013826"}, "_hasShrinkwrap": false}}, "time": {"created": "2018-07-19T16:10:45.654Z", "1.2.0": "2018-07-19T16:10:45.976Z", "modified": "2022-04-11T10:47:48.912Z"}, "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "description": "Read/write IEEE754 floating point numbers from/to a Buffer or array-like object", "homepage": "https://github.com/feross/ieee754#readme", "keywords": ["IEEE 754", "buffer", "convert", "floating point", "ieee754"], "repository": {"type": "git", "url": "git://github.com/feross/ieee754.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/ieee754/issues"}, "license": "BSD-3-<PERSON><PERSON>", "readme": "# ieee754 [![travis][travis-image]][travis-url] [![npm][npm-image]][npm-url] [![downloads][downloads-image]][downloads-url] [![javascript style guide][standard-image]][standard-url]\n\n[travis-image]: https://img.shields.io/travis/feross/ieee754/master.svg\n[travis-url]: https://travis-ci.org/feross/ieee754\n[npm-image]: https://img.shields.io/npm/v/ieee754.svg\n[npm-url]: https://npmjs.org/package/ieee754\n[downloads-image]: https://img.shields.io/npm/dm/ieee754.svg\n[downloads-url]: https://npmjs.org/package/ieee754\n[standard-image]: https://img.shields.io/badge/code_style-standard-brightgreen.svg\n[standard-url]: https://standardjs.com\n\n[![saucelabs][saucelabs-image]][saucelabs-url]\n\n[saucelabs-image]: https://saucelabs.com/browser-matrix/ieee754.svg\n[saucelabs-url]: https://saucelabs.com/u/ieee754\n\n### Read/write IEEE754 floating point numbers from/to a Buffer or array-like object.\n\n## install\n\n```\nnpm install ieee754\n```\n\n## methods\n\n`var ieee754 = require('ieee754')`\n\nThe `ieee754` object has the following functions:\n\n```\nieee754.read = function (buffer, offset, isLE, mLen, nBytes)\nieee754.write = function (buffer, value, offset, isLE, mLen, nBytes)\n```\n\nThe arguments mean the following:\n\n- buffer = the buffer\n- offset = offset into the buffer\n- value = value to set (only for `write`)\n- isLe = is little endian?\n- mLen = mantissa length\n- nBytes = number of bytes\n\n## what is ieee754?\n\nThe IEEE Standard for Floating-Point Arithmetic (IEEE 754) is a technical standard for floating-point computation. [Read more](http://en.wikipedia.org/wiki/IEEE_floating_point).\n\n## license\n\nBSD 3 Clause. Copyright (c) 2008, Fair Oaks Labs, Inc.\n", "readmeFilename": "README.md"}