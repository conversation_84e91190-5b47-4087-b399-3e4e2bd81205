{"_id": "rechoir", "_rev": "32-5b0d14c078909031fd66e77485e0b89b", "name": "rechoir", "description": "Prepare a node environment to require files with different extensions.", "dist-tags": {"latest": "0.8.0"}, "versions": {"0.1.0": {"name": "rechoir", "description": "Require any supported file as a node module.", "version": "0.1.0", "homepage": "https://github.com/tkellen/node-rechoir", "author": {"name": "<PERSON>", "url": "http://goingslowly.com/"}, "repository": {"type": "git", "url": "git://github.com/tkellen/node-rechoir.git"}, "bugs": {"url": "https://github.com/tkellen/node-rechoir/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/tkellen/node-rechoir/blob/master/LICENSE"}], "main": "index.js", "engines": {"node": ">= 0.10"}, "scripts": {"test": "mocha -R spec test/index.js"}, "dependencies": {"resolve": "^0.6.1", "interpret": "^0.2.0"}, "devDependencies": {"mocha": "^1.17.1", "chai": "^1.9.0", "coco": "^0.9.1", "coffee-script": "^1.7.1", "require-csv": "0.0.1", "iced-coffee-script": "^1.7.1-b", "require-ini": "0.0.1", "toml-require": "^1.0.1", "require-xml": "0.0.1", "require-yaml": "0.0.1", "LiveScript": "^1.2.0"}, "keywords": ["require", "coco", "coffee-script", "csv", "iced-coffee-script", "ini", "livescript", "toml", "xml", "yaml", "yml"], "_id": "rechoir@0.1.0", "dist": {"shasum": "107c8d54e0479748f9e85fa6b976d77bd80faf6b", "tarball": "https://registry.npmjs.org/rechoir/-/rechoir-0.1.0.tgz", "integrity": "sha512-MVIOEdGZ6b4USVyq7oBH2rFXb9bE6fqYOsGiHQlE/PuNToMaShM6T0bTG1gQ0NkYX10CssxM6rlWDQhOYRtLlA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCF2esmxfIAVlGO8EVIAEtZoyRDL15w4q9emNDGrvit4AIgKh9uGzaL+1GbkLnBIVA9XPrcGhzrDTjQ049XXc6araI="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "tkellen", "email": "<EMAIL>"}, "maintainers": [{"name": "tkellen", "email": "<EMAIL>"}], "directories": {}}, "0.2.0": {"name": "rechoir", "description": "Require any supported file as a node module.", "version": "0.2.0", "homepage": "https://github.com/tkellen/node-rechoir", "author": {"name": "<PERSON>", "url": "http://goingslowly.com/"}, "repository": {"type": "git", "url": "git://github.com/tkellen/node-rechoir.git"}, "bugs": {"url": "https://github.com/tkellen/node-rechoir/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/tkellen/node-rechoir/blob/master/LICENSE"}], "main": "index.js", "engines": {"node": ">= 0.10"}, "scripts": {"test": "mocha -R spec test/index.js"}, "dependencies": {"resolve": "^0.6.1", "interpret": "^0.3.0"}, "devDependencies": {"mocha": "^1.17.1", "chai": "^1.9.0", "coco": "^0.9.1", "coffee-script": "^1.7.1", "require-csv": "0.0.1", "iced-coffee-script": "^1.7.1-b", "require-ini": "0.0.1", "toml-require": "^1.0.1", "require-xml": "0.0.1", "require-yaml": "0.0.1", "LiveScript": "^1.2.0"}, "keywords": ["require", "coco", "coffee-script", "csv", "iced-coffee-script", "ini", "livescript", "toml", "xml", "yaml", "yml"], "_id": "rechoir@0.2.0", "dist": {"shasum": "673b56ff87558922a2d4309b70996e8ceb51ee19", "tarball": "https://registry.npmjs.org/rechoir/-/rechoir-0.2.0.tgz", "integrity": "sha512-vhMjCzLqyPzgWXAK9YCqT0U49KesbDw9NfGeYmUVaZvnw/sApyPn7nxx8s7a37c8QNzAbMtkv2qqNDVKsXxUzA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF/risat7iY6wxhROUZxPtcbIcbBYHrHqNnICRIKSfYgAiBjn2dmfiQgYAONCKu3HhFJyxt0PkC9KSkGFj89FI4u/Q=="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "tkellen", "email": "<EMAIL>"}, "maintainers": [{"name": "tkellen", "email": "<EMAIL>"}], "directories": {}}, "0.2.1": {"name": "rechoir", "description": "Require any supported file as a node module.", "version": "0.2.1", "homepage": "https://github.com/tkellen/node-rechoir", "author": {"name": "<PERSON>", "url": "http://goingslowly.com/"}, "repository": {"type": "git", "url": "git://github.com/tkellen/node-rechoir.git"}, "bugs": {"url": "https://github.com/tkellen/node-rechoir/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/tkellen/node-rechoir/blob/master/LICENSE"}], "main": "index.js", "engines": {"node": ">= 0.10"}, "scripts": {"test": "mocha -R spec test/index.js"}, "dependencies": {"resolve": "^0.6.1", "interpret": "^0.3.0"}, "devDependencies": {"mocha": "^1.17.1", "chai": "^1.9.0", "coco": "^0.9.1", "coffee-script": "^1.7.1", "require-csv": "0.0.1", "iced-coffee-script": "^1.7.1-b", "require-ini": "0.0.1", "toml-require": "^1.0.1", "require-xml": "0.0.1", "require-yaml": "0.0.1", "LiveScript": "^1.2.0"}, "keywords": ["require", "coco", "coffee-script", "csv", "iced-coffee-script", "ini", "livescript", "toml", "xml", "yaml", "yml"], "_id": "rechoir@0.2.1", "dist": {"shasum": "7047685cc660b676f9038f3c5b0c37bb6b82d531", "tarball": "https://registry.npmjs.org/rechoir/-/rechoir-0.2.1.tgz", "integrity": "sha512-eFhsT22oKCB14/30mbyiGd8CCBPSu3nycNz3hYa9Ug/QCBwD17UizHUuNPQx18SmXauvqQ9+QAlpAt92vTPz2Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDFKDvwbjObUqpnv+UQk5Fx8mf8W+T4bqFW8LmARFrcaQIgCaGIHQ8jH6guVzqimo2fOcIvHhs/Rc5R+R25WIiVh3o="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "tkellen", "email": "<EMAIL>"}, "maintainers": [{"name": "tkellen", "email": "<EMAIL>"}], "directories": {}}, "0.2.2": {"name": "rechoir", "description": "Require any supported file as a node module.", "version": "0.2.2", "homepage": "https://github.com/tkellen/node-rechoir", "author": {"name": "<PERSON>", "url": "http://goingslowly.com/"}, "repository": {"type": "git", "url": "git://github.com/tkellen/node-rechoir.git"}, "bugs": {"url": "https://github.com/tkellen/node-rechoir/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/tkellen/node-rechoir/blob/master/LICENSE"}], "main": "index.js", "engines": {"node": ">= 0.10"}, "scripts": {"test": "mocha -R spec test/index.js"}, "dependencies": {"resolve": "^0.6.1", "interpret": "^0.3.0"}, "devDependencies": {"LiveScript": "^1.2.0", "chai": "^1.9.0", "coco": "^0.9.1", "coffee-script": "^1.7.1", "iced-coffee-script": "^1.7.1-b", "json5": "^0.4.0", "mocha": "^1.17.1", "node-jsx": "^0.10.0", "require-csv": "0.0.1", "require-ini": "0.0.1", "require-xml": "0.0.1", "require-yaml": "0.0.1", "toml-require": "^1.0.1"}, "keywords": ["require", "coco", "coffee-script", "csv", "iced-coffee-script", "ini", "livescript", "toml", "xml", "yaml", "yml"], "gitHead": "bd11a5e7bfbcd4a38237fec3c900cc728048dd36", "_id": "rechoir@0.2.2", "_shasum": "0f9a1a09ac410381f5ae953b70b37149bfd06c4d", "_from": ".", "_npmVersion": "2.0.0", "_npmUser": {"name": "tkellen", "email": "<EMAIL>"}, "maintainers": [{"name": "tkellen", "email": "<EMAIL>"}], "dist": {"shasum": "0f9a1a09ac410381f5ae953b70b37149bfd06c4d", "tarball": "https://registry.npmjs.org/rechoir/-/rechoir-0.2.2.tgz", "integrity": "sha512-LXfROFzT+u0n06OCQypwtwq+CaSmczvjHJ4Te5sZdswnTEOtnY8jbTH2vJQ68TkxGPWDy9KnkU+t7mKDRwdyjA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFWQaHtaLPyW9FQjzimeYF5J54CovS1cTBzKLvgm7SR5AiEA7WPNkIQlaJJKdVdR2hi52L/h76SK5l5bBXOyIP+IBQA="}]}, "directories": {}}, "0.3.0": {"name": "rechoir", "description": "Require any supported file as a node module.", "version": "0.3.0", "homepage": "https://github.com/tkellen/node-rechoir", "author": {"name": "<PERSON>", "url": "http://goingslowly.com/"}, "repository": {"type": "git", "url": "git://github.com/tkellen/node-rechoir.git"}, "bugs": {"url": "https://github.com/tkellen/node-rechoir/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/tkellen/node-rechoir/blob/master/LICENSE"}], "main": "index.js", "engines": {"node": ">= 0.10"}, "scripts": {"test": "mocha -R spec test/index.js"}, "dependencies": {"resolve": "^0.6.1", "interpret": "^0.4.0"}, "devDependencies": {"6to5": "^2.9.4", "LiveScript": "^1.2.0", "chai": "^1.9.0", "coco": "^0.9.1", "coffee-script": "^1.7.1", "iced-coffee-script": "^1.7.1-b", "json5": "^0.4.0", "mocha": "^1.17.1", "node-jsx": "^0.10.0", "require-csv": "0.0.1", "require-ini": "0.0.1", "require-xml": "0.0.1", "require-yaml": "0.0.1", "toml-require": "^1.0.1"}, "keywords": ["require", "cjsx", "co", "coco", "coffee-script", "coffee", "coffee.md", "csv", "es", "es6", "iced", "iced.md", "iced-coffee-script", "ini", "js", "json", "json5", "jsx", "react", "litcoffee", "liticed", "ls", "livescript", "toml", "ts", "typescript", "xml", "yaml", "yml"], "gitHead": "6a6efd2e642c3ff48b05af12429821b6685b80a4", "_id": "rechoir@0.3.0", "_shasum": "db1ee6f8f27596a3720aa9d83b9a7aa74c99d4a3", "_from": ".", "_npmVersion": "2.1.17", "_nodeVersion": "0.10.35", "_npmUser": {"name": "phated", "email": "<EMAIL>"}, "maintainers": [{"name": "tkellen", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "dist": {"shasum": "db1ee6f8f27596a3720aa9d83b9a7aa74c99d4a3", "tarball": "https://registry.npmjs.org/rechoir/-/rechoir-0.3.0.tgz", "integrity": "sha512-oppyLr2ChpILyPFy7rA5QpqVtDIUJkQdSnZX82yQjEBXXd14WDpp7r5EZhBjaytEyNWqDcEeK/4unirfIgvXdw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCuM7B4h0T5wmPchi7lTS+1KVp+2yFDOjcHLBBB5kJIVwIgXSMYpibZFDhQFmkpuIhxFUo0iYpuKBLl/NqeuPtxjwY="}]}, "directories": {}}, "0.4.0": {"name": "rechoir", "description": "Require any supported file as a node module.", "version": "0.4.0", "homepage": "https://github.com/tkellen/node-rechoir", "author": {"name": "<PERSON>", "url": "http://goingslowly.com/"}, "repository": {"type": "git", "url": "git://github.com/tkellen/node-rechoir.git"}, "bugs": {"url": "https://github.com/tkellen/node-rechoir/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/tkellen/node-rechoir/blob/master/LICENSE"}], "main": "index.js", "engines": {"node": ">= 0.10"}, "scripts": {"test": "mocha -R spec test/index.js"}, "dependencies": {"resolve": "^0.6.1", "interpret": "^0.5.0"}, "devDependencies": {"babel": "^4.1.1", "LiveScript": "^1.2.0", "chai": "^1.9.0", "coco": "^0.9.1", "coffee-script": "^1.7.1", "iced-coffee-script": "^1.7.1-b", "json5": "^0.4.0", "mocha": "^1.17.1", "node-jsx": "^0.10.0", "require-csv": "0.0.1", "require-ini": "0.0.1", "require-xml": "0.0.1", "require-yaml": "0.0.1", "toml-require": "^1.0.1", "typescript-register": "^1.0.6"}, "keywords": ["require", "cjsx", "co", "coco", "coffee-script", "coffee", "coffee.md", "csv", "es", "es6", "iced", "iced.md", "iced-coffee-script", "ini", "js", "json", "json5", "jsx", "react", "litcoffee", "liticed", "ls", "livescript", "toml", "ts", "typescript", "xml", "yaml", "yml"], "gitHead": "ed8d97686ec743ebc4cb9fe03335011162276d72", "_id": "rechoir@0.4.0", "_shasum": "7445ba6f5b0e3fc8af8c65884bc847afb5b023b6", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "phated", "email": "<EMAIL>"}, "maintainers": [{"name": "tkellen", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "dist": {"shasum": "7445ba6f5b0e3fc8af8c65884bc847afb5b023b6", "tarball": "https://registry.npmjs.org/rechoir/-/rechoir-0.4.0.tgz", "integrity": "sha512-kwt9+3mo/aWkB41MayNoNFMO+DGNIf8VC5EvvdfnoagfDMBMIZgICpfA3NwjruJqDCbMyuDRgtLfiz3+IrPxVQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGlv7MvYu25jVvFSjK/k+LaeowqQmgT7tHrbYUplOhGGAiEA7SZEETYHf3vfL3dURuXnwXE170hebqwNN9zObYdPWtU="}]}, "directories": {}}, "0.5.0": {"name": "rechoir", "description": "Require any supported file as a node module.", "version": "0.5.0", "homepage": "https://github.com/tkellen/node-rechoir", "author": {"name": "<PERSON>", "url": "http://goingslowly.com/"}, "repository": {"type": "git", "url": "git://github.com/tkellen/node-rechoir.git"}, "bugs": {"url": "https://github.com/tkellen/node-rechoir/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/tkellen/node-rechoir/blob/master/LICENSE"}], "main": "index.js", "engines": {"node": ">= 0.10"}, "scripts": {"test": "mocha -R spec test/index.js"}, "dependencies": {"resolve": "^1.1.6"}, "devDependencies": {"babel": "^5.4.3", "chai": "^2.3.0", "coco": "^0.9.1", "coffee-script": "^1.9.2", "iced-coffee-script": "^1.8.0-d", "interpret": "^0.6.1", "json5": "^0.4.0", "livescript": "^1.4.0", "mocha": "^2.2.5", "node-jsx": "^0.13.3", "require-csv": "0.0.1", "require-ini": "0.0.1", "require-uncached": "^1.0.2", "require-xml": "0.0.1", "require-yaml": "0.0.1", "rimraf": "^2.3.4", "semver": "^4.3.4", "sinon": "^1.14.1", "toml-require": "^1.0.1", "typescript-register": "^1.1.0"}, "keywords": ["require", "cjsx", "co", "coco", "coffee-script", "coffee", "coffee.md", "csv", "es", "es6", "iced", "iced.md", "iced-coffee-script", "ini", "js", "json", "json5", "jsx", "react", "litcoffee", "liticed", "ls", "livescript", "toml", "ts", "typescript", "xml", "yaml", "yml"], "gitHead": "6c70b24ad9479febf963f539c92f2e3da99d17c9", "_id": "rechoir@0.5.0", "_shasum": "aa7f34454ad246ec2ce6bb986b983f1d9647adfe", "_from": ".", "_npmVersion": "2.7.4", "_nodeVersion": "0.12.1", "_npmUser": {"name": "tkellen", "email": "<EMAIL>"}, "maintainers": [{"name": "tkellen", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "dist": {"shasum": "aa7f34454ad246ec2ce6bb986b983f1d9647adfe", "tarball": "https://registry.npmjs.org/rechoir/-/rechoir-0.5.0.tgz", "integrity": "sha512-q3U14QZKKmZ5450v0giRBTm7UV0vjUkiEfXwTaZTz+V3OmDgIrn5JJ12De9vzpsZh8HGNOrGrADFPAfh91EelQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCDbO+Fu+0kKEA0FUPoOEEEUElVIct2xNw7RW0IRbKmhAIgcK+p8ouXuXQu2js2LlfYcDCRGA2mwfJhyFaQ58fWhfY="}]}, "directories": {}}, "0.6.0": {"name": "rechoir", "description": "Require any supported file as a node module.", "version": "0.6.0", "homepage": "https://github.com/tkellen/node-rechoir", "author": {"name": "<PERSON>", "url": "http://goingslowly.com/"}, "repository": {"type": "git", "url": "git://github.com/tkellen/node-rechoir.git"}, "bugs": {"url": "https://github.com/tkellen/node-rechoir/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/tkellen/node-rechoir/blob/master/LICENSE"}], "main": "index.js", "engines": {"node": ">= 0.10"}, "scripts": {"test": "mocha -R spec test/index.js"}, "dependencies": {"resolve": "^1.1.6"}, "devDependencies": {"babel": "^5.4.3", "chai": "^2.3.0", "coco": "^0.9.1", "coffee-script": "^1.9.2", "iced-coffee-script": "^1.8.0-d", "interpret": "^0.6.1", "json5": "^0.4.0", "livescript": "^1.4.0", "mocha": "^2.2.5", "node-jsx": "^0.13.3", "require-csv": "0.0.1", "require-ini": "0.0.1", "require-uncached": "^1.0.2", "require-xml": "0.0.1", "require-yaml": "0.0.1", "rimraf": "^2.3.4", "semver": "^4.3.4", "sinon": "^1.14.1", "toml-require": "^1.0.1", "typescript-register": "^1.1.0"}, "keywords": ["require", "cjsx", "co", "coco", "coffee-script", "coffee", "coffee.md", "csv", "es", "es6", "iced", "iced.md", "iced-coffee-script", "ini", "js", "json", "json5", "jsx", "react", "litcoffee", "liticed", "ls", "livescript", "toml", "ts", "typescript", "xml", "yaml", "yml"], "gitHead": "eb84cf7a7ac3312aafebaebde87d382ffea63559", "_id": "rechoir@0.6.0", "_shasum": "cc3f3dd4718636a7f580ef7f202b24bee3a08376", "_from": ".", "_npmVersion": "2.7.4", "_nodeVersion": "0.12.1", "_npmUser": {"name": "tkellen", "email": "<EMAIL>"}, "maintainers": [{"name": "tkellen", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "dist": {"shasum": "cc3f3dd4718636a7f580ef7f202b24bee3a08376", "tarball": "https://registry.npmjs.org/rechoir/-/rechoir-0.6.0.tgz", "integrity": "sha512-9UfomdwOAUYd0k6y/1hL8m16/2fjASoDFVs73Cm/q3SY/mQy6cSS2Rhnl/6Q8kb+3N8z4VL8jItQ28iuSt7fgg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDBihSYyJjmjbhPTOw3anfaYa8loRYWO8uJI1vHu5RhewIhAOU6apEgyN0Xw0mQpdxd9NipbsxglpST09PPufizl8Lz"}]}, "directories": {}}, "0.6.1": {"name": "rechoir", "description": "Require any supported file as a node module.", "version": "0.6.1", "homepage": "https://github.com/tkellen/node-rechoir", "author": {"name": "<PERSON>", "url": "http://goingslowly.com/"}, "repository": {"type": "git", "url": "git://github.com/tkellen/node-rechoir.git"}, "bugs": {"url": "https://github.com/tkellen/node-rechoir/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/tkellen/node-rechoir/blob/master/LICENSE"}], "main": "index.js", "engines": {"node": ">= 0.10"}, "scripts": {"test": "mocha -R spec test/index.js"}, "dependencies": {"resolve": "^1.1.6"}, "devDependencies": {"babel": "^5.4.3", "chai": "^2.3.0", "coco": "^0.9.1", "coffee-script": "^1.9.2", "iced-coffee-script": "^1.8.0-d", "interpret": "^0.6.1", "json5": "^0.4.0", "livescript": "^1.4.0", "mocha": "^2.2.5", "node-jsx": "^0.13.3", "require-csv": "0.0.1", "require-ini": "0.0.1", "require-uncached": "^1.0.2", "require-xml": "0.0.1", "require-yaml": "0.0.1", "rimraf": "^2.3.4", "semver": "^4.3.4", "sinon": "^1.14.1", "toml-require": "^1.0.1", "typescript-register": "^1.1.0"}, "keywords": ["require", "cjsx", "co", "coco", "coffee-script", "coffee", "coffee.md", "csv", "es", "es6", "iced", "iced.md", "iced-coffee-script", "ini", "js", "json", "json5", "jsx", "react", "litcoffee", "liticed", "ls", "livescript", "toml", "ts", "typescript", "xml", "yaml", "yml"], "gitHead": "e3fd6590e34bcfde9d5c3a5a54c40f9d5353ae91", "_id": "rechoir@0.6.1", "_shasum": "462beb9fe212d568a0d1282447b09de364e5f435", "_from": ".", "_npmVersion": "2.7.4", "_nodeVersion": "0.12.1", "_npmUser": {"name": "tkellen", "email": "<EMAIL>"}, "maintainers": [{"name": "tkellen", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "dist": {"shasum": "462beb9fe212d568a0d1282447b09de364e5f435", "tarball": "https://registry.npmjs.org/rechoir/-/rechoir-0.6.1.tgz", "integrity": "sha512-7/v0lyIPoo0zEQL5c5wKlbhPvAOlDzvs2yvxnI9eevI6x0fbbxrQu5DZnzBWuydU6vbZTc3edVzZoJTXnhBXaw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAd2N08Q9P3SGWQ3+pwovzFN0LajE/OJAlROqsMoZAqNAiEAvsQ2VKlUjjD8nR4iDWYxEQbKuBZNkoChAOicLdXeRxk="}]}, "directories": {}}, "0.6.2": {"name": "rechoir", "description": "Require any supported file as a node module.", "version": "0.6.2", "homepage": "https://github.com/tkellen/node-rechoir", "author": {"name": "<PERSON>", "url": "http://goingslowly.com/"}, "repository": {"type": "git", "url": "git://github.com/tkellen/node-rechoir.git"}, "bugs": {"url": "https://github.com/tkellen/node-rechoir/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/tkellen/node-rechoir/blob/master/LICENSE"}], "main": "index.js", "engines": {"node": ">= 0.10"}, "scripts": {"test": "mocha -R spec test/index.js"}, "dependencies": {"resolve": "^1.1.6"}, "devDependencies": {"babel": "^5.4.3", "chai": "^2.3.0", "coco": "^0.9.1", "coffee-script": "^1.9.2", "earlgrey": "0.0.9", "iced-coffee-script": "^1.8.0-d", "interpret": "^0.6.1", "json5": "^0.4.0", "livescript": "^1.4.0", "mocha": "^2.2.5", "node-jsx": "^0.13.3", "require-csv": "0.0.1", "require-ini": "0.0.1", "require-uncached": "^1.0.2", "require-xml": "0.0.1", "require-yaml": "0.0.1", "rimraf": "^2.3.4", "semver": "^4.3.4", "sinon": "^1.14.1", "toml-require": "^1.0.1", "typescript-register": "^1.1.0"}, "keywords": ["require", "cjsx", "co", "coco", "coffee-script", "coffee", "coffee.md", "csv", "<PERSON><PERSON><PERSON>", "es", "es6", "iced", "iced.md", "iced-coffee-script", "ini", "js", "json", "json5", "jsx", "react", "litcoffee", "liticed", "ls", "livescript", "toml", "ts", "typescript", "xml", "yaml", "yml"], "gitHead": "1aafd85aac487171be71891b916c9136c620ac0e", "_id": "rechoir@0.6.2", "_shasum": "85204b54dba82d5742e28c96756ef43af50e3384", "_from": ".", "_npmVersion": "2.7.4", "_nodeVersion": "0.12.4", "_npmUser": {"name": "tkellen", "email": "<EMAIL>"}, "maintainers": [{"name": "tkellen", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "dist": {"shasum": "85204b54dba82d5742e28c96756ef43af50e3384", "tarball": "https://registry.npmjs.org/rechoir/-/rechoir-0.6.2.tgz", "integrity": "sha512-HFM8rkZ+i3zrV+4LQjwQ0W+ez98pApMGM3HUrN04j3CqzPOzl9nmP15Y8YXNm8QHGv/eacOVEjqhmWpkRV0NAw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCOy3x4667cKBHl2LvSZnAKE3S/GfaSn4YwYvSsVf5vmwIhAI/nUwNifyygMhhRRAkPYmrSEm8yGetjJAJIw8d4o71p"}]}, "directories": {}}, "0.7.0": {"name": "rechoir", "version": "0.7.0", "description": "Prepare a node environment to require files with different extensions.", "author": {"name": "Gulp Team", "email": "<EMAIL>", "url": "http://gulpjs.com/"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://goingslowly.com/"}], "repository": {"type": "git", "url": "git+https://github.com/gulpjs/rechoir.git"}, "license": "MIT", "engines": {"node": ">= 0.10"}, "main": "index.js", "scripts": {"lint": "eslint .", "pretest": "rm -rf tmp/ && npm run lint", "test": "mocha --async-only", "cover": "istanbul cover _mocha --report lcovonly", "coveralls": "npm run cover && istanbul-coveralls"}, "dependencies": {"resolve": "^1.9.0"}, "devDependencies": {"eslint": "^2.13.0", "eslint-config-gulp": "^3.0.1", "expect": "^1.20.2", "istanbul": "^0.4.3", "istanbul-coveralls": "^1.0.3", "mocha": "^3.5.3"}, "keywords": ["require", "loader", "extension", "extensions", "prepare"], "gitHead": "fdabf8d47ed3063170dba9a3f69a539fccc8fb72", "bugs": {"url": "https://github.com/gulpjs/rechoir/issues"}, "homepage": "https://github.com/gulpjs/rechoir#readme", "_id": "rechoir@0.7.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.1", "_npmUser": {"name": "phated", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-ADsDEH2bvbjltXEP+hTIAmeFekTFK0V2BTxMkok6qILyAJEXV0AFfoWcAq4yfll5VdIMd/RVXq0lR+wQi5ZU3Q==", "shasum": "32650fd52c21ab252aa5d65b19310441c7e03aca", "tarball": "https://registry.npmjs.org/rechoir/-/rechoir-0.7.0.tgz", "fileCount": 7, "unpackedSize": 7911, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcL8b+CRA9TVsSAnZWagAA9KkP/jXej4OXTvCT5tobuOlT\nJWIwu/ybmiXA+0e8gKF9+hrKYKBbwzb2I8k0CL4n7tmgTDmhGcgBEHNpzkMu\nEbX4E2k9DgBkrkH09IlK6knBeGV12U03tSYN4TpiOUAcPnmsdSTXdRQCZgHJ\npAFRG4432ufk9VP9WKCiov69gys5UwynKDI5Bq8Yt9cP3TCVnhm5Swj3mdEh\nJahlKULuW4zsly0WDE0ZxxEYOEaFxcwtR3J4Z+K5TlFZpL+R+PG/OzYLqV9b\nnrP2ZUm2F3gHVXoQEFmFvtVo7hIgM06U+/Buzwx45iX1C4czC11PRtzneK1Y\nMgShfTLWp+/baye5m6EkpcS2iWHx9NomiZaH84IpHfOiCwoVbZC87gBCj2Qv\n5YKtV4a62L93C9s4+MyhJADqspVzoXCvO+vjBUpM7EJu6x5ar2SZghjfff6I\ntK65FwQahk2768IFC8jvtqxThHmPaIGIh8PzO3JjUy91h78i4Fd9DCsD80Fv\nyPI7p1YSqvsdDnShjmckVt2DO5KIEuctTGJAydQ5JViJ+S1pWYM+MCtjrBJW\nff00HzHm5d35zhJj9Y1LVQSVUE3j2Gd+kmIdcI/sTu9RowGl+pTUHCn2y2Xq\n0sPzH6LWWRwYmAOAZ9SAP5kdECJPAujGtj76++dGKpzojGoTRGtwj05adFfY\nlXAz\r\n=dNAG\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDRYQFP0Wmz93RcC2jlE0obPDcZc7ANiAfocjdeZXJS9QIgTd0Tkuwx20L0dSryxuRDc2umCNLO7XCAzItT4TzPThc="}]}, "maintainers": [{"name": "phated", "email": "<EMAIL>"}, {"name": "tkellen", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rechoir_0.7.0_1546635005822_0.3529092046940494"}, "_hasShrinkwrap": false}, "0.7.1": {"name": "rechoir", "version": "0.7.1", "description": "Prepare a node environment to require files with different extensions.", "author": {"name": "Gulp Team", "email": "<EMAIL>", "url": "http://gulpjs.com/"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://goingslowly.com/"}], "repository": {"type": "git", "url": "git+https://github.com/gulpjs/rechoir.git"}, "license": "MIT", "engines": {"node": ">= 0.10"}, "main": "index.js", "scripts": {"lint": "eslint .", "pretest": "rm -rf tmp/ && npm run lint", "test": "mocha --async-only test test/lib", "cover": "istanbul cover _mocha --report lcovonly test test/lib", "coveralls": "npm run cover && istanbul-coveralls"}, "dependencies": {"resolve": "^1.9.0"}, "devDependencies": {"eslint": "^2.13.0", "eslint-config-gulp": "^3.0.1", "expect": "^1.20.2", "istanbul": "^0.4.3", "istanbul-coveralls": "^1.0.3", "mocha": "^3.5.3"}, "keywords": ["require", "loader", "extension", "extensions", "prepare"], "gitHead": "9351bb3e695c77253ee539912ca02d3e94a9d0ad", "bugs": {"url": "https://github.com/gulpjs/rechoir/issues"}, "homepage": "https://github.com/gulpjs/rechoir#readme", "_id": "rechoir@0.7.1", "_nodeVersion": "14.15.4", "_npmVersion": "6.14.10", "dist": {"integrity": "sha512-/njmZ8s1wVeR6pjTZ+0nCnv8SpZNRMT2D1RLOJQESlYFDBvwpTA4KWJpZ+sBJ4+vhjILRcK7JIFdGCdxEAAitg==", "shasum": "9478a96a1ca135b5e88fc027f03ee92d6c645686", "tarball": "https://registry.npmjs.org/rechoir/-/rechoir-0.7.1.tgz", "fileCount": 7, "unpackedSize": 8936, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg9mktCRA9TVsSAnZWagAAtFgP+gLgv667+e02xlFoqOV2\nVDaA5CeUcaCufirmkZnjBwWbAJzQP6a8TkHCbb/Vww0V9kjFSCOLK2jLRvmt\n9qf/G7qMq6f6UPJucgJ7+F3KaXQQ4mo9DsmXNhyPC/+CTltjdeic+6opBCh4\nzz4Ac//lnkWebRMg18v4FhLUBbzx7XgNiUYy+19YBM+CZyQkLJlIPm56lcm1\njqU+bwEnaSqolWcl5ZOctAQ/OvwRGaoOw6O1Qv9cEmW5s9ebeCH/dqbUV1fM\nUmJ8uLiObWt2ro9WwM3Y+9VZ3QTj8idlXmEi2bfoxA2Bs3IIG4YZnMjCdT9k\n2AtkAZ7J72rIuB2e4O+6tiJPk78tH/Vv4uiWmwQGwRAVLNX2OytYSacWR9p9\nlaU7pC+uNaLectme1fTCImrB9TF3knPazRF9ECcnrt8abSVbTAeyI+aySag3\ndKhesbQXD7hF2fS55Qo8xkJheo5F9KeNEvNPWDC4Xb5UVBZ76f4BnZZJEzjk\nKWAXs885GdwGQknUN6HdywrRI0/NbspsbOT/2kxqgu8RcDR6uqiSSnLpB23j\nr2nq9JY20ryagce8KURTSzK8ZEJ4rQ96vvqKVM4lFTsBNmRHfBAWdqN7rxD/\nK4OPOO375lEnTrWlBb5cpYuKthF+ZhbIwfxOelR7/C4eXbQR5KoqmSlgF0cC\nxHQj\r\n=UZa4\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCnNXQJ6AZ77blkxxwtT29FPk7xKayg8Xua396mtPTDbgIgD8kdYhYrBi3OOfCZqjR4LqUBENi4dgUdrzEBGaVC7fQ="}]}, "_npmUser": {"name": "phated", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sttk", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "contra", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rechoir_0.7.1_1626761517213_0.410364137393306"}, "_hasShrinkwrap": false}, "0.8.0": {"name": "rechoir", "version": "0.8.0", "description": "Prepare a node environment to require files with different extensions.", "author": {"name": "Gulp Team", "email": "<EMAIL>", "url": "http://gulpjs.com/"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://goingslowly.com/"}], "repository": {"type": "git", "url": "git+https://github.com/gulpjs/rechoir.git"}, "license": "MIT", "engines": {"node": ">= 10.13.0"}, "main": "index.js", "scripts": {"lint": "eslint .", "pretest": "rm -rf tmp/ && npm run lint", "test": "nyc mocha --async-only"}, "dependencies": {"resolve": "^1.20.0"}, "devDependencies": {"eslint": "^7.21.0", "eslint-config-gulp": "^5.0.1", "expect": "^27.0.0", "mocha": "^8.3.0", "nyc": "^15.1.0"}, "nyc": {"reporter": ["lcov", "text-summary"]}, "prettier": {"singleQuote": true}, "keywords": ["require", "loader", "extension", "extensions", "prepare"], "gitHead": "1880cbb697c5027855a60c169202a2d8a03ce739", "bugs": {"url": "https://github.com/gulpjs/rechoir/issues"}, "homepage": "https://github.com/gulpjs/rechoir#readme", "_id": "rechoir@0.8.0", "_nodeVersion": "14.15.4", "_npmVersion": "6.14.10", "dist": {"integrity": "sha512-/vxpCXddiX8NGfGO/mTafwjq4aFa/71pvamip0++IQk3zG8cbCj0fifNPrjjF1XMXUne91jL9OoxmdykoEtifQ==", "shasum": "49f866e0d32146142da3ad8f0eff352b3215ff22", "tarball": "https://registry.npmjs.org/rechoir/-/rechoir-0.8.0.tgz", "fileCount": 8, "unpackedSize": 8639, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh25bvCRA9TVsSAnZWagAAoB4P/0XNYtZ5Kshx3wUPJjCq\nZ5LxJrLGP3j37WDRraccdcMswf9h3jxWpld/q4xWzyVU1PkFA+JAmlGxY9Dg\nbeyKLYFnEqzqh+D793iL202dkmcMyYF2sRg3iuLscq20swP+i1KT/l9Qhojm\noQ4RGC259N2qa/hdHbJ0dMvzCykpkOZB5E4lgx5COdCxnV+qcJby4j2JPZeC\naaBS307koFTPkndg29Sz/EtmFGCr855Z86BagGaLiR5vtnIftLeY+MGgMwpo\nmjYeJMLhR/5bpA9WChdcDrTsEcoCRAbQFVG2miMJGAQLK0iEKi6AoISeyF+N\nUuxGFpy71thS+GJpBOBFrEN45ZzotYfxr0rhD474hpMkvEgTCcnP+xZ7T18E\nTi/8yhjrQBEB2ghMCthlDpsfkpvTnNKwqWBsNEnCvQEnH72IpAuOHdJlcSWn\nBLkQ+ndcM76+k+Jm1k+OFFvNSyvlGO4q5GrDd4nirFfREwftijx0zbzp4qpk\naeqFj9dLkNROFztlsMP+AUfdYby61pLTJE6WVgyd5jWppa2GXVfvI341GgYu\ndGpvGveLZnjswQUcBTf7Uvox2RH0IB6boAitwrNlNT7q6A/voWmyYphgO9zB\nyA8ALvikIaJj652GvadjhhCDD0Wevrx+lbS4rCi7bW+Hr3snQM97aFRvSV9v\nHn07\r\n=p+0N\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDqhH6REuG+9mcpZCn1feoOlSglb9FN1/QTRyIvEg7swAIhAOcTDWfDCrnPxyCmGmPOZwHJUOg3qreCGyc2OfOjAlSR"}]}, "_npmUser": {"name": "phated", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sttk", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "contra", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rechoir_0.8.0_1627101564926_0.20828219250262103"}, "_hasShrinkwrap": false}}, "readme": "<p align=\"center\">\n  <a href=\"http://gulpjs.com\">\n    <img height=\"257\" width=\"114\" src=\"https://raw.githubusercontent.com/gulpjs/artwork/master/gulp-2x.png\">\n  </a>\n</p>\n\n# rechoir\n\n[![NPM version][npm-image]][npm-url] [![Downloads][downloads-image]][npm-url] [![Build Status][ci-image]][ci-url] [![Coveralls Status][coveralls-image]][coveralls-url]\n\nPrepare a node environment to require files with different extensions.\n\n## What is it?\n\nThis module, in conjunction with [interpret]-like objects, can register any filetype the npm ecosystem has a module loader for. This library is a dependency of [liftoff].\n\n**Note:** While `rechoir` will automatically load and register transpilers like `coffee-script`, you must provide a local installation. The transpilers are **not** bundled with this module.\n\n## Usage\n\n```js\nconst config = require('interpret').extensions;\nconst rechoir = require('rechoir');\nrechoir.prepare(config, './test/fixtures/test.coffee');\nrechoir.prepare(config, './test/fixtures/test.csv');\nrechoir.prepare(config, './test/fixtures/test.toml');\n\nconsole.log(require('./test/fixtures/test.coffee'));\nconsole.log(require('./test/fixtures/test.csv'));\nconsole.log(require('./test/fixtures/test.toml'));\n```\n\n## API\n\n### `prepare(config, filepath, [cwd], [noThrow])`\n\nLook for a module loader associated with the provided file and attempt require it. If necessary, run any setup required to inject it into [require.extensions].\n\n`config` An [interpret]-like configuration object.\n\n`filepath` A file whose type you'd like to register a module loader for.\n\n`cwd` An optional path to start searching for the module required to load the requested file. Defaults to the directory of `filepath`.\n\n`noThrow` An optional boolean indicating if the method should avoid throwing.\n\nIf calling this method is successful (e.g. it doesn't throw), you can now require files of the type you requested natively.\n\nAn error with a `failures` property will be thrown if the module loader(s) configured for a given extension cannot be registered.\n\nIf a loader is already registered, this will simply return `true`.\n\n## License\n\nMIT\n\n<!-- prettier-ignore-start -->\n[downloads-image]: https://img.shields.io/npm/dm/rechoir.svg?style=flat-square\n[npm-url]: https://www.npmjs.com/package/rechoir\n[npm-image]: https://img.shields.io/npm/v/rechoir.svg?style=flat-square\n\n[ci-url]: https://github.com/gulpjs/rechoir/actions?query=workflow:dev\n[ci-image]: https://img.shields.io/github/workflow/status/gulpjs/rechoir/dev?style=flat-square\n\n[coveralls-url]: https://coveralls.io/r/gulpjs/rechoir\n[coveralls-image]: https://img.shields.io/coveralls/gulpjs/rechoir/master.svg\n<!-- prettier-ignore-end -->\n\n<!-- prettier-ignore-start -->\n[interpret]: https://github.com/gulpjs/interpret\n[require.extensions]: https://nodejs.org/api/modules.html#modules_require_extensions\n[liftoff]: https://github.com/js-cli/js-liftoff\n<!-- prettier-ignore-end -->\n", "maintainers": [{"email": "<EMAIL>", "name": "phated"}], "time": {"modified": "2024-04-04T23:53:42.146Z", "created": "2014-03-20T18:34:03.768Z", "0.1.0": "2014-03-20T18:34:03.768Z", "0.2.0": "2014-03-20T19:02:34.285Z", "0.2.1": "2014-03-20T19:20:12.910Z", "0.2.2": "2014-12-17T17:46:23.421Z", "0.3.0": "2015-01-10T20:03:20.460Z", "0.4.0": "2015-02-17T23:39:27.450Z", "0.5.0": "2015-05-20T23:37:42.656Z", "0.6.0": "2015-05-21T00:34:47.905Z", "0.6.1": "2015-05-22T20:40:08.943Z", "0.6.2": "2015-07-22T14:42:26.370Z", "0.7.0": "2019-01-04T20:50:05.912Z", "0.7.1": "2021-07-20T06:11:57.357Z", "0.8.0": "2021-07-24T04:39:25.084Z"}, "homepage": "https://github.com/gulpjs/rechoir#readme", "keywords": ["require", "loader", "extension", "extensions", "prepare"], "repository": {"type": "git", "url": "git+https://github.com/gulpjs/rechoir.git"}, "author": {"name": "Gulp Team", "email": "<EMAIL>", "url": "http://gulpjs.com/"}, "bugs": {"url": "https://github.com/gulpjs/rechoir/issues"}, "readmeFilename": "README.md", "users": {"shanewholloway": true, "flumpus-dev": true}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://goingslowly.com/"}], "license": "MIT"}