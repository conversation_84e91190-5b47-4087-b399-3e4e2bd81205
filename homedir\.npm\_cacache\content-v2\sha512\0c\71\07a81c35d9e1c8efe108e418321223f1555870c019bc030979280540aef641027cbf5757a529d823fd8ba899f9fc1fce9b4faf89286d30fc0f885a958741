{"_id": "json-stringify-safe", "_rev": "76-e83ee78a5e7769c599025c291f4386d5", "name": "json-stringify-safe", "description": "Like JSON.stringify, but doesn't blow up on circular refs.", "dist-tags": {"latest": "5.0.1"}, "versions": {"2.0.0": {"name": "json-stringify-safe", "version": "2.0.0", "description": "Like JSON.stringify, but doesn't blow up on circular refs", "main": "stringify.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/json-stringify-safe"}, "keywords": ["json", "stringify", "circular", "safe"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "license": "BSD", "_id": "json-stringify-safe@2.0.0", "dist": {"shasum": "3f5e856825d4ffb9deea1156f79c47adab7f9c44", "tarball": "https://registry.npmjs.org/json-stringify-safe/-/json-stringify-safe-2.0.0.tgz", "integrity": "sha512-2wwGzZ1gJ6X2Um441XxAFT8NbjCkcbIo1akA8UDKm27YA8ys8g/lUOC7A3axJ3FMR0MJ+URGdqtZV3WyoxrpIw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGaVTCmtfhjFbPlfyq9uRakDAgvcwgxK3BPJ4y27aK3uAiEAlFL8UKcJTZ0fNCyBkxFbfuQ+VwU4tbosUeByJVtCPTI="}]}, "_from": ".", "_npmVersion": "1.2.12", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}}, "3.0.0": {"name": "json-stringify-safe", "version": "3.0.0", "description": "Like JSON.stringify, but doesn't blow up on circular refs", "main": "stringify.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/json-stringify-safe"}, "keywords": ["json", "stringify", "circular", "safe"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "license": "BSD", "_id": "json-stringify-safe@3.0.0", "dist": {"shasum": "9db7b0e530c7f289c5e8c8432af191c2ff75a5b3", "tarball": "https://registry.npmjs.org/json-stringify-safe/-/json-stringify-safe-3.0.0.tgz", "integrity": "sha512-VSSuxEAawKLYlCabQOR7YDijQ69zPqQBOriUuCgNhlAqtU7RPr41gPpaSs6WkEu+ZOtUequpXWbI51CS+Z/gMQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDfLZGodA6RU1McwpKnrUhWF3AFwUA28CBJ1dtG2w3OGQIhAJ1DyPEWFOALItmQ30EosfBsc1AwXqmDHOCSeUZm7OAA"}]}, "_from": ".", "_npmVersion": "1.2.12", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}}, "4.0.0": {"name": "json-stringify-safe", "version": "4.0.0", "description": "Like JSON.stringify, but doesn't blow up on circular refs", "main": "stringify.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/json-stringify-safe"}, "keywords": ["json", "stringify", "circular", "safe"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "license": "BSD", "_id": "json-stringify-safe@4.0.0", "dist": {"shasum": "77c271aaea54302e68efeaccb56abbf06a9b1a54", "tarball": "https://registry.npmjs.org/json-stringify-safe/-/json-stringify-safe-4.0.0.tgz", "integrity": "sha512-qzEpz1SDUb9xvA+LDOkNgjekdV7tuC7zDQf14sqMBtujh8kVbQhF11VWm4DeR99yFNjVSjTTfKa40c9ZQOtwXA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIANlsEFOpCxi10v1VYQpYZr3BmLtY0hk7OaiRCVFJRr7AiEAoGpAHXpARt5H/y2jhyuKnPfrENXAAVfSQg+aYo/vgIo="}]}, "_from": ".", "_npmVersion": "1.2.14", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}}, "5.0.0": {"name": "json-stringify-safe", "version": "5.0.0", "description": "Like JSON.stringify, but doesn't blow up on circular refs", "main": "stringify.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/json-stringify-safe"}, "keywords": ["json", "stringify", "circular", "safe"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "license": "BSD", "bugs": {"url": "https://github.com/isaacs/json-stringify-safe/issues"}, "_id": "json-stringify-safe@5.0.0", "dist": {"shasum": "4c1f228b5050837eba9d21f50c2e6e320624566e", "tarball": "https://registry.npmjs.org/json-stringify-safe/-/json-stringify-safe-5.0.0.tgz", "integrity": "sha512-MNUPdXfDS1pgJNcvJ5wzKZZlr5NDEP344o5s3TwSKAjRxKAq93QFriEmZ5BDbYOdTlyRsylFPZ8r2bibRMB/nQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCSQxwtgJLiotPptrvMa5zIFznCcIlVRbBmbbBTCuIKQAIhAPhv/DjQlILtxqkC+j+ziwjkx/cIS9+jeuPVMVikrujz"}]}, "_from": ".", "_npmVersion": "1.3.6", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}}, "5.0.1": {"name": "json-stringify-safe", "version": "5.0.1", "description": "Like JSON.stringify, but doesn't blow up on circular refs.", "keywords": ["json", "stringify", "circular", "safe"], "homepage": "https://github.com/isaacs/json-stringify-safe", "bugs": {"url": "https://github.com/isaacs/json-stringify-safe/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://themoll.com"}], "license": "ISC", "repository": {"type": "git", "url": "git://github.com/isaacs/json-stringify-safe.git"}, "main": "stringify.js", "scripts": {"test": "node test.js"}, "devDependencies": {"mocha": ">= 2.1.0 < 3", "must": ">= 0.12 < 0.13", "sinon": ">= 1.12.2 < 2"}, "gitHead": "3890dceab3ad14f8701e38ca74f38276abc76de5", "_id": "json-stringify-safe@5.0.1", "_shasum": "1296a2d58fd45f19a0f6ce01d65701e2c735b6eb", "_from": ".", "_npmVersion": "2.10.0", "_nodeVersion": "2.0.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "1296a2d58fd45f19a0f6ce01d65701e2c735b6eb", "tarball": "https://registry.npmjs.org/json-stringify-safe/-/json-stringify-safe-5.0.1.tgz", "integrity": "sha512-ZClg6AaYvamvYEE82d3Iyd3vSSIjQ+odgjaTzRuO3s7toCdFKczob2i0zCh7JE8kWn17yvAWhUVxvqGwUalsRA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAZqSTtSFmiVTuyzmerhv0esiG/S1Gpn+XTqthnqQBhgAiEAzTZ4VK8kw70vebmigWDEUKPI/OJ5SGzlyjMGbdeKXlM="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "moll", "email": "<EMAIL>"}], "directories": {}}}, "readme": "# json-stringify-safe\n\nLike JSON.stringify, but doesn't throw on circular references.\n\n## Usage\n\nTakes the same arguments as `JSON.stringify`.\n\n```javascript\nvar stringify = require('json-stringify-safe');\nvar circularObj = {};\ncircularObj.circularRef = circularObj;\ncircularObj.list = [ circularObj, circularObj ];\nconsole.log(stringify(circularObj, null, 2));\n```\n\nOutput:\n\n```json\n{\n  \"circularRef\": \"[Circular]\",\n  \"list\": [\n    \"[Circular]\",\n    \"[Circular]\"\n  ]\n}\n```\n\n## Details\n\n```\nstringify(obj, serializer, indent, decycler)\n```\n\nThe first three arguments are the same as to JSON.stringify.  The last\nis an argument that's only used when the object has been seen already.\n\nThe default `decycler` function returns the string `'[Circular]'`.\nIf, for example, you pass in `function(k,v){}` (return nothing) then it\nwill prune cycles.  If you pass in `function(k,v){ return {foo: 'bar'}}`,\nthen cyclical objects will always be represented as `{\"foo\":\"bar\"}` in\nthe result.\n\n```\nstringify.getSerialize(serializer, decycler)\n```\n\nReturns a serializer that can be used elsewhere.  This is the actual\nfunction that's passed to JSON.stringify.\n\n**Note** that the function returned from `getSerialize` is stateful for now, so\ndo **not** use it more than once.\n", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "moll", "email": "<EMAIL>"}], "time": {"modified": "2022-06-19T06:27:33.261Z", "created": "2013-02-19T19:01:58.271Z", "2.0.0": "2013-02-19T19:02:04.205Z", "3.0.0": "2013-02-19T19:22:41.788Z", "4.0.0": "2013-03-13T06:31:31.741Z", "5.0.0": "2013-08-02T20:28:17.007Z", "5.0.1": "2015-05-19T01:42:09.719Z"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "repository": {"type": "git", "url": "git://github.com/isaacs/json-stringify-safe.git"}, "users": {"dbrockman": true, "dubban": true, "fotomut": true, "boustanihani": true, "thethomaseffect": true, "oceanswave": true, "morlay": true, "mastayoda": true, "warapitiya": true, "kurt.pattyn": true, "koulmomo": true, "nickeltobias": true, "tobiasnickel": true, "calmwinds": true, "nukisman": true, "jsumners": true, "srokap": true, "hasithaishere": true, "t2ym": true, "troygizzi": true, "deadratfink": true, "magicmind": true, "mojaray2k": true, "dzhou777": true, "froguard": true, "wukaidong": true, "456wyc": true, "anoubis": true, "lunaroyster": true, "alexxnica": true, "ivan.sh": true, "kontrax": true, "shuoshubao": true, "zhenguo.zhao": true, "d3ck": true}, "keywords": ["json", "stringify", "circular", "safe"], "bugs": {"url": "https://github.com/isaacs/json-stringify-safe/issues"}, "license": "ISC", "readmeFilename": "README.md", "homepage": "https://github.com/isaacs/json-stringify-safe", "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://themoll.com"}]}