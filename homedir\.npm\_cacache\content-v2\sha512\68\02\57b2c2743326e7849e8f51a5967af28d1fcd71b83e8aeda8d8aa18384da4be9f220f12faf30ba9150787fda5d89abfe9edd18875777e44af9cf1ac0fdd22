{"_id": "@esbuild/netbsd-x64", "_rev": "95-1ab95bfc03ab0b9cc28e9e9990ed9ab5", "name": "@esbuild/netbsd-x64", "dist-tags": {"latest": "0.25.5"}, "versions": {"0.15.18": {"name": "@esbuild/netbsd-x64", "version": "0.15.18", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.15.18", "maintainers": [{"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "2b3a4a442d4c8521cecfafac6edd7bc7237c2b4d", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.15.18.tgz", "fileCount": 3, "integrity": "sha512-lEhMB2GW1gfniLXm83Bqyc0hj0fgE97ObGThzEoeUW9UDpb8uswUxrZ0eVMDgILvv5w3+jxrdkOL4HmQcAbuTw==", "signatures": [{"sig": "MEUCIEeKvKKW9pj/fGgSQwTY8g9Ci3jsvIFQWf2m2KSjSPjwAiEAzFaS8EdjySxsLsxumPowCSBIFTzCtUnPs6RUwmWcGJo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8598211, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjjoUMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpKkw/9EDzFQ3fTKqjGZHCl+gcXZ2408UrxrYwjn7ll0eSxgxRhZJxY\r\nVeO6AYvANwi3XLlcdrtWDDOs0rI55xA2Qm+aMeKGj7MvmXk2CgGvxvWmmvrw\r\nYjbvYM3O4lDvJYwDEa4TkDSDtFxhYLQkgpbfRThL5vX1on4AD48ZpeGk84wn\r\nvEgV43qQst78aYY4xrRpWlWXu2dGn45DstustHneGneZ2QGJnQgZA6vgouZX\r\nQxAXlW1O4F3R7QEi2dTdE85gd629rmauepEjqaiWsR4P3JIb9b0h/03U6uDp\r\ncJ8NZs4g6zwHzAjwc3vmTnI1PbaL0NpbAb7D/DhzklqpHPDiaQCWDv+omvAP\r\nKNLis4FvxySTLFb3ThH6iZkbeeZJMnjYg3Wz1rDTwPlbjQstNlHQ+BnXwIvv\r\nYL+8JZjeTO7ka9bFO8od6FBBuE++kStJq72RK1tEHC9DgthRQtUccuUNC3r6\r\nlgu1tc3jGo/11UqUWiplYcoYHuHHs0tQLMx5QSZFjuBx1XOQ09C1U9wGq9/6\r\nn3QE+A9+uie3DVDZR1OQmTJfUGOrzEPnc9SJaZNd3AcX9AtnizWJ7gZ0m8w1\r\n0BXuzNxyK1fq5PMEgAZJPfPudh1VAUDwqlD4/PATrjUGMs3A2YnkC6O+jxue\r\nYgbwgxknRmXOYHhp3EzduFIuiUxeAFgh4wc=\r\n=GweB\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "07e607164c880e03e13f86aa50a58cd6d44ec084", "_npmUser": {"name": "esbuild", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.15.18_1670284555799_0.473979150214207", "host": "s3://npm-registry-packages"}}, "0.16.0": {"name": "@esbuild/netbsd-x64", "version": "0.16.0", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.16.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "6bd7db294b8b93552e1f2acb87cbdc4723b0b36a", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.16.0.tgz", "fileCount": 3, "integrity": "sha512-UPMJEmi0rJTczhRPHRHSDcihSrERqgsD/xK+YjONneTLJ2fSzclUpKgZtIOFA61NGTh3GA8nOdoCSdWTV59vBg==", "signatures": [{"sig": "MEUCIQCt9BNUnWoHoGOh7/JtOWA0xjW0Zt+onifhlMId/70ysgIgep0DeQ8wOIPSEQsAzmwTlZOpz/QTRwnyOvKixy4rZnE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8602306, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkA6UACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq2OBAAmaTT14WjWCPvQ5h2UND41+74tMhHSu2jblXG5JcezgdXtJB3\r\n7kHPQEiEX8BZrtU/Pj0Rp4mg7YR6Gic1It6Pu1iwB8YddIKll/hIyTh0IAX0\r\nNSHTPJcWSiHZRZ5xTDka6F5oZmA2wYSz6Gva4vvf1KMdiDvTwNgKKuLiCKsD\r\nndwOSnXLhstSVQhWWWa4PsRcENmUx6wuXm2FipQMdGn2K83u6DAcAsA7bLoT\r\ntw5fu7z6NDXw0b3NHo1HbiHoaNjdf1AqOz6JEeSN8ImAXBcttQoDnOVFqwFH\r\ndZ3cP5s8L71wIViNCQZ7fRTU+Ey+5OoIhjfmLkCoyJJVjiDG11XP6sCP8pIQ\r\nAWAgdWZnTrLjI3N9ShuaPWfMecYoq/CGGxoBvGTOXw6FwyuYBQWq3db/F14g\r\nECM1SdAcxRhlt1voaski9OcbV4ePrpslK9bsPzNEf34/rZHVaw/tnr8XCVeH\r\n5OEkI+4TamBpn0ucyKssqzauzpoyhCT/D/Y8PuqHIOG9iwkUZk3f4O9cC11b\r\nNUl5qbdj0RMPZjf4ULMxzg0s6sE24apIVKLb/qY2z3azTg6mIcgmSLPCmxo2\r\nVEI4MV7lQ7RcEWJ39r3Zu7ow92x/hdCMwvWhD6lHSgzeM8TdhpA33FIOnyQ9\r\nBUd/TguaveO9nc10De2YQ5mP1Wg2NcWnw4I=\r\n=/ENX\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "6c8d15d404874fd939d7f4062cc6a660dffdabcb", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.16.0_1670385300512_0.22184427409280572", "host": "s3://npm-registry-packages"}}, "0.16.1": {"name": "@esbuild/netbsd-x64", "version": "0.16.1", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.16.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "77ae84d4674d6c315a4b652ccbb27ce8e9484dcd", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.16.1.tgz", "fileCount": 3, "integrity": "sha512-OkDgqg+drkSEvNOAEPUQrv3g7OlE0hMsLe7on5+GePZvjgQepQ7fQ8T6RGj2nEMGa5Am2Q3jWEVx5lq6bsFpRw==", "signatures": [{"sig": "MEUCIGl5UIAAdYjhvr78Yvirjd8d5JdwG4ILdK+HZG9jRgySAiEAtltOQzDa7Tf3PgFoskbcctOtV9UXeGASS+kKVzoXG4Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8602306, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkBsJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrdMxAAiEskVc64zTYuUeUdhQJpnkHF1UeCfdV3mb9d4HN0nf0j0P1r\r\nR9DutrLTnOunIhlIqDOka8SSJQ7+cnBVquvljuDT0/S7g8/9JXYfGQEwTFyN\r\nvAjzuo2X3LjUABl3Gd33kPAQqMwapYoZKgqMFz+cQBu8OwTRVyy8Ra/nigs5\r\nQf1RweR5A/MOhmCJA/VpkJ3UuTqx6JglaxKKqHtFr9ICuV70mGCa4fu5IxS8\r\n6BwFrHNYVWGvJ07z16Yb/Y1igj43jYzheUgJByF7zSiFaWcbWf+aQJK342M/\r\nmUeZi9WfWKCnZG/kyQHi0o7OnAzlNpvBkZfGCcoU3fx1uSoEXYb2uYcJqzPf\r\nRSNSulltV4lEjrt5mK8bDHdtj2TP9Gh3jzWt/BjPQkQNfcCu2KEOOu3AbHsh\r\n+QOaxmppu83h98/msTBpxKDaBumnqxgCAsoIbkdQKedYtdFUqeW4pIUreiJ5\r\n9TAWM19U/9mazQoQHCAHj5LhXYxtcEwfPwNcHgkrWEFEa2HSoscKjdzj7w4j\r\nnBS8wCKTYTJknrcd3m4FSyTHpgcLkyqeyYtnjGscnKWZkn+zf8dQE9qV63vZ\r\n4EX4+j5vTw45ykcQZ845/UOIMls/MsMpvrF14YbExQ2otlXRySuPLL5EpQT9\r\nJkw1FqKADwXvUPIl6VsNGiMy00WhiIorYTY=\r\n=Tkv1\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "3b62a3680cdd1c9d76bed3e2e60841e371670c35", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.16.1_1670388488907_0.4311798300980971", "host": "s3://npm-registry-packages"}}, "0.16.2": {"name": "@esbuild/netbsd-x64", "version": "0.16.2", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.16.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "fa5c5ad79bbadfbd741423ee4ab22d2b0c55143f", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.16.2.tgz", "fileCount": 3, "integrity": "sha512-oBH2Aj4fL9FLlkIi2wYGckydKHVKmYrqiqt91i6kFE1mF7B05YYttrlOHAf3JzWIJQWyvzvsmoA/XFPf1sTgBw==", "signatures": [{"sig": "MEUCIQCKSFO7nBLcif1csZ3UWdacPAYMvy2txe9kjMmzc5dl+gIgNBlx3HQHGwWFtY9EAnG4IWpH5gpsU+9NSTOY7x4Wlrw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8610498, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkYtfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrdVg//XV/QBLwAJ64nC/a+G3mcv6P+y0poTc5TekP76gRstc4EUFJ1\r\nlnHFEzWVin9ZWEhLRLuSbEVSi9zsw/qLFBF17wa63X6yj1x4z1aU2X5/JzWW\r\nKn5hqaZq/IyVm8NBCS6XJgSWGtBSJOUDOH6B5SvYoyU8QqfC+op9IoYYOh9o\r\ndaLqkIRbZorH0lxb/EWtdRcq5RzgBqBjOY0iLWYQi/+dktpWNduDXQI75fJQ\r\nouPb0CxqTiycAegApUd35ddOxcrFeDhoMyJitlsx56bEl+gqXNvsW7wuY2IG\r\nhFEWi+wOxBmOwj+cCWLrfzvxyEURf/tWCovtKwGpG1RLeEw7aj5KZYFu2ZjD\r\ngVkKWmqyXLZ0kZsWIfRufdBUASK/YbFJS6DCuANjwNYCqfTD2KeB5DWxQnuo\r\nSkZMpUvIe8TKq8ezi0hs9+4xK2PLPOVS2A9X2NZnLwgQfWqWxED4aiRyCHqi\r\n38WE8tIoR2WAay0idSxfBprJnghAtHJ8SCBJid/StCiUn36EV1FOF9heJOYi\r\nCWzv93RkKpCMOQ18yEGyAXzzdlqcKqEG85tFAJP2eEMnipjuru5cD7RymEY0\r\n286NSpY/tFCzo6i7zrJhMDil6r/XfmyNwUjgzMK0Z+UILBoW8ASW0dvAF7wy\r\nhLBQ76kFRXj/BSL2QMA0ZXHR37wVlTby/As=\r\n=zFz1\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "0829d74c0b3a913c0cf6d3f59902871bf63e0d16", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.16.2_1670482783248_0.6347413850174921", "host": "s3://npm-registry-packages"}}, "0.16.3": {"name": "@esbuild/netbsd-x64", "version": "0.16.3", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.16.3", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "e0270569567f1530b8dbe6d11d5b4930b9cc71ae", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.16.3.tgz", "fileCount": 3, "integrity": "sha512-AzbsJqiHEq1I/tUvOfAzCY15h4/7Ivp3ff/o1GpP16n48JMNAtbW0qui2WCgoIZArEHD0SUQ95gvR0oSO7ZbdA==", "signatures": [{"sig": "MEYCIQDyhPkTG8gkOs4H1XPuP71EXkr42A1qqewN41oVDcl1sQIhAOy0ieRuIuSKI9cCy1mI0D+WTSbXnrdHJ8gI5OhNReug", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8610498, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkkVEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr4gQ/+PQs71emBFFoGui55rMzRj9hnH/h6UVsJ66tzTk14Bt+j4r2X\r\njYWK8ksuDm6nOb6WosAd3OCChIj4+aUdmPEc3UoCL63FGsku4TUgDsrU1VdP\r\n0kfaiV0mEL0O6c1uIWyHqxOQhyx0FIjpc2OMqnJt9AX65O973bkNEcciviRk\r\nTPg8cciWXdGJ6BrE5rrAYsRyK6gQiXyam+PM5ocokAdv9tWKWN2Zq3nbK4st\r\no071ALTJRaYVtOnILnKsrKZoF1FV0N2C+JbI6AzqyjwQZ3MWuDMMXt1zgLET\r\naHVkIWPM3Eo6dVZkZ5tzNFghvxUSwbSUCDBgAtA/Al1IKmM2J7eZzkGFm7ON\r\npeCo3yofSHf8zQevgDGtOdjq4ma18mO2do03Q/RemAiYUu8CFCqwlyCpAZ7w\r\nrXp7CqeFNm+spdKkwKq90+T+K0RBjs7X29L9pg5/tHJBKNDANogiucdO3gXt\r\nY0/AKieaN3mQae7rJK0jl+Yio7ojG36VYG6GQekLH1Zdotf09xXLyXtlR6yY\r\nUrXlT2eI9pkhfXyvOHuvhj+kJFDenz5WfUeF5at3Wr2v4+hlXDbpEQhRWUjR\r\nxwW03OiIkE/nfB9rX4vyXmSTfapD7qCdHKDvDDdi5SlT38ofq0w5aE8cPpEQ\r\noXfXmpdUAfd+WAgPLM/RcyWproeF57Gh/wA=\r\n=Lqhn\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "8ef19fefc9bfdd28fab95dec3783d3f100f25e3e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.16.3_1670530371774_0.62362699210764", "host": "s3://npm-registry-packages"}}, "0.16.4": {"name": "@esbuild/netbsd-x64", "version": "0.16.4", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.16.4", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "b59ecb49087119c575c0f64d7e66001d52799e24", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.16.4.tgz", "fileCount": 3, "integrity": "sha512-LHSJLit8jCObEQNYkgsDYBh2JrJT53oJO2HVdkSYLa6+zuLJh0lAr06brXIkljrlI+N7NNW1IAXGn/6IZPi3YQ==", "signatures": [{"sig": "MEYCIQCrPHoPXk9xC9mKmYvY1WA0e8wIDQioeNI3ywjffrdozwIhAKmo6YNuJk6DlOmUu/BbFjF/kuKkxBxNAPQPy9iRobav", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8614594, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjlAIBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqs9w//Rz1ZF5W0YMvf+1iVZeeupG3P/eItpCYXz8KYp5KNSXzPy9KO\r\nZM1++7oQGvVVeVNNj780rnF4+ITM/P25zXUiROqgQXn39F0sWZAV04pCWFv8\r\nFFhD63h7ha2Je7RD3Vr+iWXvcFY8fpHk9fpQ48ZmTjeVdcJjAcg9bSATSZI5\r\ncTukc9OQCd1W9sD+e3k/H5j9mE0w+4OUGkPfHnI20ZHXzTsfIn9JI3oCj0nf\r\neCC8L58OXyMAEWtU9HMFVThyWmLjUk94TFeSTZPL4jv+I2gAqpp1KQ5wyy6x\r\nYwhUJ375wxqkasspDe3EU8DEcrv9jVS3VlXRUC4AM3UepexS/DCdqzvpPw5r\r\ngR36NRXQ+fdnO8G3axXLQvA/h+kJNtYQB0oay1PnAJ0ZdPVZGYp65B5xisCR\r\nqyi43U1TGLVldOfd7NAzTfh/O9kO2zCh0vDH3VQlPpslijq3KV62MK2O9Q4i\r\nipoWES8rasAcIFq0n1J22y28AHfXo5Pr5FKdtBJ4SeFcxaFRiSKrdKdQ2bdC\r\nV46gTPG9xAEibwWKAQaJdjLc66MWZX5tBWDF/SQLp0A79cfb8kYLUkajS7x5\r\nCzBP/o/O0lFESF2Hio9KfgfvTtWAudw0IbcyrgW+GvJfYd0+2DIygi5cNXs5\r\nFl0JrNP+fUJ1BYKWhJ6jSh5119bFhxhpbbE=\r\n=1TYY\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "643af8ed12345b3a249f1d4c7643c261d95c098c", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.16.4_1670644225191_0.34809708119770444", "host": "s3://npm-registry-packages"}}, "0.16.5": {"name": "@esbuild/netbsd-x64", "version": "0.16.5", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.16.5", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "ab0ffb19dc14c0efc2317372e4e4782fdb8cbd9c", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.16.5.tgz", "fileCount": 3, "integrity": "sha512-ZhfELxpZLXg7OidX9MrjgQNhjhYx3GXm59EAQVZds8GTyOOPj+Hg7ttKenlXoV8PZVkoCm0dgoWXzhasZJGfWw==", "signatures": [{"sig": "MEYCIQDIPjclDdydK6LetuP/NaKW1wlP8HX+9BA7JKhG4u7/ogIhAOzHSnbntXMsE3bosQRrTUB0h1Jppce9ltDH84BJLnki", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8626882, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjmLq5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq+Hw/+MMrKe0Kp9gHjreepyYJKuqcg8ehAIv/hriHAodgvIxTLKdZ1\r\nXGEtvJ0aMiRIh05zCnUmbmhqM/1sUJkis0ReMY1aTh36ZSagoj+MspGmioDx\r\nGop+NrUvKV6RcnBSKygh2Un2ag+eMPyPn0NoGcyZ0yD8+SWmYKdF7erH78oz\r\no2px8GYeFwoedCc2lsIX4z8Xm1VHwRVaOuRt4wFlCU1uwz4VFaW7mVKSA0ug\r\nv6j/kQopQMZSTgf09rMtKO3frqrBct7ONIuoT//SPEqWCW4Jw2mC/KeEdDc7\r\n9X6z6IVhkoruvGra+Uycxx7RcHZqC6h8HTXE79YVmfWh7YA344gO5tvd1sNB\r\nDiyF7nn+V8jAS0pWqWwlpFDZN7d7FfzXZSMEfirz4eWrIiNOwr6racZ1OW/l\r\nXoQMpo0zOf7sTkUmKdqzRHtaj+QwBzmBxpEwR/AwNc/uMUVgMJC9bbk8MWpZ\r\n8h6lhx7FiS8GrrL5T50GSu2/2jLo+rTPo/wf3L0e7ihDUgMP8Q1X7Jl+limA\r\n/lpy8K1vxujUJ5w1QjnizwdX4kcfS4fItbIh0wQbnMycAe5eEBhrpWOpDfds\r\nLOlYPWCcH3VD9gN6XiZ/Csv+drAups0HCJFRuEC+mquQ7gh39Y9mRHHsSugk\r\nfNucBa7TMUiUWdex/vsnnIBQaPn1wAs+2mo=\r\n=E4Zv\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "bb9639c3e1f57a3fdfaadf073a35d87020253f70", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.16.5_1670953657514_0.9936072489363641", "host": "s3://npm-registry-packages"}}, "0.16.6": {"name": "@esbuild/netbsd-x64", "version": "0.16.6", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.16.6", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "2227e9483c5a861bbecd740e525cd9b17dc01c61", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.16.6.tgz", "fileCount": 3, "integrity": "sha512-q5tKkYilkgNLtp6szs/yXAHJJ4OEjoTRlHHPJtVyDj6AZsdDynrkoFUV98D+CncB9Im5CIRnPmJErb6EDvIR0Q==", "signatures": [{"sig": "MEUCICAEpOOrESvh8Gv+FjC3UmX+5oROr6gBKPUFJEKr41iWAiEA58GYTTEejvgkWjpWY3GntoMzj9ufvGyGRuTx1Sm/NVE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8630978, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjmV2/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmra9Q//TiMJ1xWgLd/b8c+ZARWdVZ1yoYrLEN3tNhoVB/PQ1qwym93A\r\n2VQSNxBCmsX00Q8tOOZW89etymNqkEekP/f4+ciDYsKU3gmeBd2nQJ56hRV3\r\nJHb2Z8KT1Ff4xysKHneHPuhz6FiEHQH//WTlwVDZAnuxqI23c1qcpZ0aViG4\r\nRuRuTIjyhxXP70WkVHeTSMSEphfZYsU5efIS5pVNI+o2sF/UWVeio71Bj135\r\nnmJNejjeXDpXjMyK1qyMZIH1a+oxTvBCmWR/7HzOe8NlESFqFf1PxcdEpr6c\r\no4QooGDvnas0dZEmvuwSrLZ9AamxNsBKER3eXmhiI052PaFrPE+LGhdBYAgI\r\np+YsS4iZN49+ht7QSfscYKIzeDdXtyS9UGRpmAochqVc9HtqJGwHz/aqtGL/\r\neiaZnRzvcQk2VpS9i0zWldyTL3FjsdE3/TGZdK3ORMmqdEd9a2JYXslPaGCu\r\nOiPGWpLbIZ77ENKSrQVey0emrVCkG5wkFbzTNB6EWzLf58vZFDxGJvUDbHIo\r\nFPubZ9Qe8CaDOfsEe/3oyHnh/HyTTecfLE/ndd4HePiqc05rU0oHdMwFRr4V\r\naKy7d9Z+GUeigb8KCjrZ8IrP+ArajrboQBT1sV2OA0n43opXgDWxi8zhz7qa\r\nH0X7SDKcng57E46Nwfs1yYxiar4nNpVBcK8=\r\n=a1Dy\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "ee8e0ddc78114b73836ee1c520d255fd28c1ab1a", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.16.6_1670995391116_0.03019789109303761", "host": "s3://npm-registry-packages"}}, "0.16.7": {"name": "@esbuild/netbsd-x64", "version": "0.16.7", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.16.7", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "0fd59fea5e6b94ee82e81b3b389e561efe77b347", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.16.7.tgz", "fileCount": 3, "integrity": "sha512-NUsYbq3B+JdNKn8SXkItFvdes9qTwEoS3aLALtiWciW/ystiCKM20Fgv9XQBOXfhUHyh5CLEeZDXzLOrwBXuCQ==", "signatures": [{"sig": "MEUCIGn84zBGnI7mLXJcMs8XIkBjBazrulHn5ySDCKEAclbGAiEAtsvoufR4D/m7ZtEN4m48/ZlcuUtleDUnX+eNvRsNtwk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8639170, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjmlJcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpAng//VRflv/ovYq88EGoKMufaczaLVR52EMoRX9Zr2ca8RWf93OF1\r\nET3M0SYXu0fZnXcwHlKpIidbVMipy11bGvI1d31AfBIKJe7Rcxwp1T3mMTGk\r\nB/ddR4DqP3zK5GjibkWTpZxW/EyihqvvjVdp/i3UrpOb4uF5xz5cEn4LbIfl\r\n6oVRnP/R7awMpSUjnKC680emCkR4i9Jy8bB3JIb3pQnZjAUnVi8IBtQ03lAw\r\nsU21c2WEVPbU1RFVDVzXD+X4ZxOZGZsRrSF3xIWpgPmFxq+tmcAUsKSJA7Rn\r\n1Cj7vLvV0sjVxwHHe2//EtIh19hNa8GHRIbp1ifDlvekjsL97mAeCdKeKZ0O\r\noDLTfyzh6njA+ymXxSgpxQgODp7T7Fp88lX4Bc9JgaFeBKGyUpK8sdGoCm7x\r\nPwm3rhk1GqxxTLZiTFy/p4Vd99ckjGk/ewWhj9hR2gF5QpGohrRpZxlajhUe\r\nh6rOX97FxCn/VHZNJMzMLg2xhAyYjLnpVt98B4Cs/qCiEao9QEv8jCm/DCXn\r\nlHMdz6icziBh3Ztba8ugQ1M6piQucvVf+UegGcHZiOGVM+DdKbaJv/AmvTRI\r\nv5rBcXg9JMig1ZtXS/JeKHYfMpafG0PAJGIM13kxJjZz+VCbow0/WKEPgsQa\r\nUl+2n98DlSulQ+/HQl18XgutWQ1Vj1bJKu8=\r\n=13TR\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "be16d813dfaca257af7ba99e458f54d1abdc31a4", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.16.7_1671058012593_0.6406469584371968", "host": "s3://npm-registry-packages"}}, "0.16.8": {"name": "@esbuild/netbsd-x64", "version": "0.16.8", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.16.8", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "c301af71797751766689460c2bd4516ead698c0e", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.16.8.tgz", "fileCount": 3, "integrity": "sha512-wtENU7TOrnEbUes9aQuNe5PeBM4cTK5dn1W7v6XCr1LatJxAOn6Jn8yDGRsa2uKeEbAS5HeYx7uBAbTBd98OXQ==", "signatures": [{"sig": "MEQCIHEDDuPHXErVDxoBJQ+SpZvfSB1EdbKuo+xehINj8feoAiAQdkgG+OmuURGvU8yZr4gMrwxfvnTPmwO++S+ARaYeeQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8647362, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjnQF7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmouyQ//Yt/mkoZJbaDZkVOVI6ayTxdv9YA6aDG+gHJ72bD7Uq+Hoqna\r\nx0JxbHa4sKQvCvvdusY2XsC2KUes4nVpsLOffMswICmBsDoziEz638B/A9ZL\r\nUVmXcjiTjFhX3xxDc3LVAyYVKSzxsf9nkgnqTCkY3KRzxzM9cVVLq53dgKvn\r\nyO2K/N7sifGyt+cND6lgNtyvYL9Pr1LOtuyqRv/wj2RS+ckyOv/Boala4UjA\r\n87w877qqV/sHBeqtkJGgCB0LHK/cfx54zRdXGFrdLJyczn3WS0j0lfgb83HD\r\ngfB2cOiFHbpEWjDnrUwMVjBd1OdBmIsiDPLxhUSSzkdUwGMzxJHkOcdggNb6\r\nCxlQpLEoQ74EzDD66+F8pvrW6M2OQIzoctnXgOCbiWnmJNufuTIftXol9sOJ\r\nCUCxJ5fxtlBSSxbBCairtjJLpB1CYTgtETqCgb6rCdSgWcT0ZgIrz2aSdbr6\r\n04y0E7ulNdBLC2PG/oChSXag+W0/gbgKgYKMuYsqiEqW6zl8GybiMUru0Rxa\r\n/SNycAKif6VYC+uJ9BCbjcldPLn6E/Epm6w1796VEg692yhK9YMxgmSzkqw0\r\nGwx2c6UKDT5qHuWMEJoo7QSEzKVIPn7OMmuRyw0UtULjchDLuuh57+cZxVkM\r\nfBaFUKz2upEnOK5IfY2r4ucv1RjtU7cchlc=\r\n=Gf5t\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "0ddb995d7d9bbcefa8e74c5a29c700111427bf18", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.16.8_1671233914814_0.16465037153213835", "host": "s3://npm-registry-packages"}}, "0.16.9": {"name": "@esbuild/netbsd-x64", "version": "0.16.9", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.16.9", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "22ed58e404ebeb2475b821bc4e25f1027eb0c912", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.16.9.tgz", "fileCount": 3, "integrity": "sha512-d0WnpgJ+FTiMZXEQ1NOv9+0gvEhttbgKEvVqWWAtl1u9AvlspKXbodKHzQ5MLP6YV1y52Xp+p8FMYqj8ykTahg==", "signatures": [{"sig": "MEQCIFTUA3TLXJf9OGsozbY1secP+Su5pE6TiInPovrtovGgAiAmEeJ8AkefDsz/vRbYdiIypKLnS1l7+YHMBDFWp8vbdQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8651458, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjnpeTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrbHQ/+IDt+zf355AF6nytk5ZSxAWsmSaUZ/8SYD/iVYUGiglXFh5qQ\r\nHqyllF/bQ//98L5WZddf6g+mcpeLtXr7a47s2qTsum77L4zn2LJXsIosFGqV\r\nNjRczxOCYx+aspPFVMVIwtN1wvuXdasKAxIEFA55+GNed2PvGelUl4QPKyYX\r\nQUffy7x+6JRUWjsezyRVsUh992niR7ggoW4V/CE+82vgF2VlTV12IO04qE88\r\nRG1c6gkAF79vHikv4aCTMyZbwV7ZoPKVsDh/T5RCR463MkiQSKdVFVCnCA03\r\nE9MnW8YAodGvV4jsMrbt8XEFqn2D0HN78aj7q3O/5NoCa67tv/8O1mCzmLtY\r\niHkPAqU/QMRlCayRIVrHkyakKAUhaAroNTHmbNKJeU937uiqMNrOlIkwRu+A\r\ndunwxWHvnRK1B2IilKCIKtSJIshqXu9YsrjqCM+QGXgksqp7tOoH9REOP1R5\r\nIiEI/gNk6SiGcxZbgTKpN3S1xqUjiuZzlIqAj05VH10B7ygn/HvMwOpmoobc\r\ni8qm7VfsGk0X7E/OTKq+G0+/CgJU3Q26YaqEU22A2u4XDLtEjx7R39HVl063\r\nHaefsOdXcCy2wIUlBbWhXiIGZZ7L5XbH7AGIINPYBJqJOEq4KkkmTB28MIsA\r\nl3epGIGjuMyAoe7BB/CyW35sf8SdYtD+6oE=\r\n=U8TR\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "29ae56a2ca081ed980ac9c73fcced1fdbc479f90", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.16.9_1671337875649_0.5319383881426347", "host": "s3://npm-registry-packages"}}, "0.16.10": {"name": "@esbuild/netbsd-x64", "version": "0.16.10", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.16.10", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "ebac59e3986834af04bbafcee7b0c1f31cd477c6", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.16.10.tgz", "fileCount": 3, "integrity": "sha512-JeZXCX3viSA9j4HqSoygjssdqYdfHd6yCFWyfSekLbz4Ef+D2EjvsN02ZQPwYl5a5gg/ehdHgegHhlfOFP0HCA==", "signatures": [{"sig": "MEUCIEWPmEdV6i+muJeiQ8fGax53q3YUFQN0Eaf8doaDl7jAAiEAhx6J7S4xPTlvmszKszXv4BpCicUPcEdYXPb8y2D0oro=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8663747, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjoPMoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpqLw/8CDV62Nu5oRuBzS84kMZoy+XxEHdRlPhkpHc2sjUdo62SJXEe\r\n+3VLRL+1WIMX+z66OR2EaOeeOyODhM2J1xkvMre1nW9mEOBkIqP2PMTMz2gl\r\nf4JRYmdc8Kzsof3YnxaxBxfmecUCKqY7VMFZAjsGOwErecbjIZSmEf3vIPUL\r\nAmL9OgA7O+mJSf6Da5hFrthx2qW3SHtuDEZjxLLoh4406TOGW/MRa0TAkFDh\r\nE5mw7uLJOosj20LJ1jneRc/6CAwwdVGa7TsrJ+UEq4I1qlC3hXj7kyBVNwwM\r\ntNzUpQLnIA3p/g3yDIKTrcH+mA08FFkWvSwW+yJ1ujWDf8DIPKVazVQKv+JO\r\nGhuYS0RshZjBidCB4vPXOZseA/JSa9xuO8GrPpmTaYk3C87dvypw2aN/88bQ\r\nzAOs8dRhCb/wTQ4apg+eA2XsgwQWYDZs6bi5AyIIdUpsgFx4KrCDHW7ZtadG\r\nt9sN7AO01b3EwletnWDldyYd9oRNB2eqnLLiPaPnJCFJ5OT4M6++Q61EEjJE\r\nzTLS6ot5rBHg7w8wnj9pokJil7MsqMjMgngVBr4VKeCx7sdzRD1j99RKAV+x\r\nPlR14xF3tRZuswxOOc3BOjSH1FH+s64xvccn2FHD2i0nb66jlPf7dAjDlFdN\r\nUOUUMX99jBRK+uiqwEOITt3P+FWyOPsz2IM=\r\n=1Blg\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "0fea6aea59845d1c0bef9dc16dfff636c3f721d4", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.16.10_1671492391765_0.5543665191312188", "host": "s3://npm-registry-packages"}}, "0.16.11": {"name": "@esbuild/netbsd-x64", "version": "0.16.11", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.16.11", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "05a7483d9f267e8e0a8a2b3fe56ee6af6ba6cfd0", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.16.11.tgz", "fileCount": 3, "integrity": "sha512-PiljZi6QZ3Pz0pL8rfJqPln8F/a3mEJwh2vhBb1kDYLniLufo9/7AInb/ZyhvgR7FxUQluUYyz64owPomgaLJA==", "signatures": [{"sig": "MEUCIQCwIE/FKvPKkawcSSKZnhNEHTP819b9xrg4S8Fy7FCrYQIgJoEuJgh3SuQca8SsSg3qUzXrKVRchaqjz8ejtVXrD60=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8663747, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjqky3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmotKA/+MLWpnLJTd2OQSZngo15ue+tP9Vbt5t3/zKDHkgFC9C3MUaI7\r\n7A/oUsZdcKdiQ6luWWxULwRK1X7muBwKasKWhfOM0R0GZaDrWV91uoe+cVGo\r\nV+WcT4VZiI2w2KL7gBAYlqjv89E6J9DywTAGzGvgG42SEZpIGp1+PPHkgfdp\r\niSNZ1sbf55uZrjsHF2dlnBHJ7V1rmQlOKwPmDqfB6b/GEGxp93EHbMvqhsG1\r\n4fBuhf/D0EG7fVojSkv/GVueiGY86jwjJFNvsK/FU5H38yj0lPceMBNA+02D\r\n1Dz+qBfM9nGDsq18hEDP6tcu69hPH1z9MicbexhvqKY5wYfjYdlWhD/o0ptw\r\nZX+KwHIeGNvjlLUdQBmsufdSFdvqcAN0u1DEj9M70r1vWR7dfAjWGXCPK4w9\r\n+/C1eA1zPPl6lX5R3SehROa8KIYGLMCOoJ4PDGPBWYEXEb9th8VmH6MSBuOi\r\n0w/kAjxiIJMjAUgLNZnx8sN4keQp3H9nqIXxf3a7dRZlM6yHhSRJW/CqeabZ\r\n9mU3X+qFahQHiX7yGAr7mLl5wXw3sXKvDmdZ1VRtHNnfjR2YtPYL97jxfV8L\r\nzWd0LfopFXfkBlJoGlAj54WdJFG5i3kpJrzO2jua9fbWC6xUj6P2bpyT4/73\r\nYTD9NusS3bWsl95dioluFSThjc6S6dkP1d4=\r\n=Jd5+\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "17555921cbe672f6327f49a2436df1a69124623b", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.16.11_1672105143513_0.9154555856330338", "host": "s3://npm-registry-packages"}}, "0.16.12": {"name": "@esbuild/netbsd-x64", "version": "0.16.12", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.16.12", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "6d4b9de7dc3ac99bf04653fe640b3be63c57b1aa", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.16.12.tgz", "fileCount": 3, "integrity": "sha512-z2+kUxmOqBS+6SRVd57iOLIHE8oGOoEnGVAmwjm2aENSP35HPS+5cK+FL1l+rhrsJOFIPrNHqDUNechpuG96Sg==", "signatures": [{"sig": "MEUCIGzGYoJqNuNa8dOhagq86LOwAukc1d/UXk9mtH1yw2r4AiEA04KlPZIsnCNDn2ISKMLLCnaJsCCMJTvDFb2h2FT1n30=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8663747, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjq6Q5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp7Pw//QBOs44okA/I0xsUKGwFvdqlAVTSGwTagO1PIjCF2Dq6vrfq3\r\ncM9ly166fQjBIgTfFN94QJ6ygsTbQIj47IRH4P3kajDi8QmwNQ7C3PcDVYQ1\r\ncBXBIBmUHx7ervGFvyyWLM5IYvbrROd5DfcPrVTRtbkRVhp2Tu/HYMXDe98w\r\n+cwqTuG+Tqo2M0fF0Fzv/ALFrXQV40Ok4Oo97MDvzf13EtPk3P8MzzOCQnMo\r\nfgvJUb3PJaZPZaHt13BR2eIkiEI7ky6D9UEmRzlzOe8qZPBuk+DfACCzbXfh\r\nvhjQOEC9iLo0ppfBL4cOwA2kWG7AnASBONQzozhqPuyie2VNxnGuBpL0chtE\r\nU5+mgyZwJfa/T3PbnUu7nvjJLlhFtQX1wpdTV6E6oXbBhcBBBiQV9LMJU1Jm\r\nlhLmXZqjm1moSFqjFl4L+pzPX4WclbW8S8ve3NPjSgCsZfkMbLWaloGSyrIx\r\ndavBFzstqdvwQvq19icPsUPDOmdlDeIP09LhHKxcDVkyCm6Ggyw0jAdj98/b\r\n01Gx5a6d+osVMGifJ/OnHzzxBbKK7tbq17HV6YBoSaTg5Z4ZD/Q0rchZXwyQ\r\ni3g449SD1KxGZ/OXQPgpYNOgn9Kbk21uRyyo3Z/qTivGPzhFwPyict60d+s3\r\n/PZQQS/1lUEi3qgB1UUC1YT7HjfVIfF2a4c=\r\n=F9PG\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "ddda86edaae10abdc759601da6198b33e61c1220", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.16.12_1672193080907_0.8784758985083676", "host": "s3://npm-registry-packages"}}, "0.16.13": {"name": "@esbuild/netbsd-x64", "version": "0.16.13", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.16.13", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "8cba08074263862138cc5c63ad7f9640fe3faa69", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.16.13.tgz", "fileCount": 3, "integrity": "sha512-2c8JWgfUMlQHTdaR5X3xNMwqOyad8kgeCupuVkdm3QkUOzGREjlTETQsK6oHifocYzDCo9FeKcUwsK356SdR+g==", "signatures": [{"sig": "MEUCIQCgBUawGwpCkP5n0AoVPelInOSsPTf3x1MhdZmXOST9LgIgAsg4y2SEb7V952M+SIJDkvFSsgvjQJO4t5nO5GhyQF8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8667843, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjs2FNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq53Q/9FDxFDBzQmJVMlZ1YkLtEvIebOYKu9XF3zarOji719pM7Ezc7\r\neuF8cl5n9fz/RvmOp6kysAQkmzEsXg9/BC4i4qiWskS1UA6dBKTHX8In/py+\r\ncff1whyu0vxLVHBMZJNvWhTilOrRvb+PIAONx0IO6y/I7pB4/zUP5gshs/e+\r\nat2MmjqzgrB1jnJgY52x3zo7iKnlTINBdr8ZDgwYt+XHdtTovXCCPY5nBglM\r\ndCfsUq0gco9J1+odSNugSM+svBReYZH3uamp6zomscKXQhbAf7W+2ZldNyhz\r\ne69V1y0a2VxcSej8jowT6O5RFKAdDDPQjFORpRHYpaLiT3PGKY+/o2S9JVku\r\n8xmVaOvTU4Uj/HOpfneDFHpEGPiIlLfJ9lgeV0965OZpvKn+uj5FE38JmxmF\r\nlcCqgf+DDkaJh3p3zE7xQAi7UshFO3UBF5qiqcjYJM01oT2PJNcv8BjWTfBI\r\ny9oeZ4maUrof6uE7pNHuK2CME1H17YrfJQBJnxF9S/o2W2O33FI7/77i6oHg\r\nOJ0SF40naAsq+sBqJozgWUe/06ykSOH5iyYyTZ2aIXPFseHQFK2m8Rwbt+wm\r\nTCYnxDPsuOZSIkAKx8qlRwP5Vm63yGMrDixfF9LwEC+GGqiTH5JwZ+A6tevt\r\n5sUoIQl9M3SrECS5m70a68RRwyQx4wEn5AM=\r\n=ZGSW\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "0db0b46399de81fb29f6fcb65dfb5fad3638b6d8", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.16.13_1672700237072_0.8855982278163066", "host": "s3://npm-registry-packages"}}, "0.16.14": {"name": "@esbuild/netbsd-x64", "version": "0.16.14", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.16.14", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "53dcfb5131376feff0911adff7f01b4821706cf6", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.16.14.tgz", "fileCount": 3, "integrity": "sha512-U06pfx8P5CqyoPNfqIJmnf+5/r4mJ1S62G4zE6eOjS59naQcxi6GnscUCPH3b+hRG0qdKoGX49RAyiqW+M9aSw==", "signatures": [{"sig": "MEUCIDnJUT2DkWGk9e5K66Svnz4OJkERy6zu5Oy1we5w96C2AiEA2Divf6vG71LitKg5MRkbAijE5CpkW+gwnZlh3YqnFoA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8704707, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjtd3HACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpqhA//YrTVNKVvTpqmaQszwwDyz30cXYjdsISLAlbNTJJH3FNsbWXf\r\nNUtMu5EZFK4td/Lbk0Q40dRMsF44+M68eGo2vPcGuxbL/PwbGSTx9nyFv7Rz\r\nixSKeehhTam7XiarvO4/GPdH8HZZ12iteFHDGKkML519V8/p6h1MPMN/rI0I\r\niLV4QCseqJQLOQ72FH4yzIUv7BOW8xGFDhvBw6tH7eUol2AgdZGZldB2kQpM\r\nZUe878Y8BPDbxtJsocb/KLAa4KvUm7AtnGwht6mMj826gEQBD/Xssxvh6kh2\r\n9VQPWcJRTxg8IDSxqNrdrWUWHG1gzazsE9r7Fd/MbVVgYHm2UhN01gw0exNT\r\nTtt16Yu2EuzHSwaDFt5pdARh5z9t/DG6C5LHHXC4R2KMOcnVmsaHr8aw70oa\r\nZoT05ZXid29viCQmyAW9EpSkeNVXR5pCqtXNdk3s1hKC59G0i+pvCJEVvbIK\r\nEf7ZL4S/tDFD5Q3og1zPH4L8HjZbDlCaeOasMeJ79BUDww7jvZn5wGBboDbj\r\nRYrK+r+1SBkG3KE5FnE/1JudYSVNtu3cr1YcHOZtsY80tuEsQ13xgcLeyzZo\r\ntSZ25eQ6UntRbYsrB6z/Omb2bEhhaPsd0q9n+1zm83MNDx/okZetUdDNYFEO\r\n/4dPWl8VaagCSUKHrMELUMI/5YpRWRtuoOA=\r\n=LIPA\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "93328af7dcf842f750c3e782bd83997e4f817e8d", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.16.14_1672863174885_0.7243629593546375", "host": "s3://npm-registry-packages"}}, "0.16.15": {"name": "@esbuild/netbsd-x64", "version": "0.16.15", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.16.15", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "cba5674608c197bee9d25451ae458ab76a770a45", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.16.15.tgz", "fileCount": 3, "integrity": "sha512-0k0Nxi6DOJmTnLtKD/0rlyqOPpcqONXY53vpkoAsue8CfyhNPWtwzba1ICFNCfCY1dqL3Ho/xEzujJhmdXq1rg==", "signatures": [{"sig": "MEUCIG+5zl1T87y568PsMaArj/sKViVqDtKo1ohlDgbZJlTaAiEAvsfqQ/1bf1/r2Ez1TxpLuYjcwvr0BoHFh0eMdCiIrPs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8704707, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjuPKsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrpPxAAnCyaArZYqQcuViHLZBOoxkdxnmV2SneZkGA7YNFfvi3M/sQK\r\n0y8GhrPBemWFmnYsFlQZn5vCpx4qqHTQ7U1iLPRWdS2/2QEM7Ed6H3jzdKN2\r\noMbY5+i39Fk30Q9AnzidAiZqhgBWm0LTrZAtIac9V7jP8jwteKaUdt0gFqTF\r\nYopaxpXb9zdM+JxvnwYvozcaxEhpew3h9t0PhVkkx4ygdrI1YQKvVYB5/u3s\r\nu0hR7EbFJTIWN77S1/xR4Jl2wwNP4SwOEZf4gxgZbxbGHV1kvM42zyRghK4P\r\nkua15KprNdOkc7xuAuJyxPpi/96ivpjRNR4FGH65a7eVmGYdGPets3tMFgIi\r\n9NJ2P0drlnRlSSI8hyfXACkF7RzlivwgJoauL95S2mEOHcyTc50SZUq0HGS1\r\noWq44Zirl1/A/54IlIW57P3V8nTeBkk+7+2Pe2npkUUfFzNHTzmWuwPS25T7\r\nX3ZFDL6J73Wh9jo3qn+jlGkV+XI/gt+tTfA9n0bIVAUrOMiIPm2K9V6i8qSu\r\nEwP8s6WmmxDUaa9+KTrU97WkdWl7c1Ke5frvT8eQ1JMITeGy3oKGBjqufKVU\r\nPItIIGVPiTMNjhRwDCO1t5Lw00ipUTqjZsgvZuvt2N3C5SJ2OrtMWQUl2e1n\r\nnWXvQpVE6iBGiGSQEZG3317rCCd/bzobFc8=\r\n=vouj\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "33a515951c626e56addc1dd4c6561a1514559fd0", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.16.15_1673065131958_0.6945308753335164", "host": "s3://npm-registry-packages"}}, "0.16.16": {"name": "@esbuild/netbsd-x64", "version": "0.16.16", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.16.16", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "e2a0ee181fbbe834174d26e24ce1b258232bb3eb", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.16.16.tgz", "fileCount": 3, "integrity": "sha512-xwjGJB5wwDEujLaJIrSMRqWkbigALpBNcsF9SqszoNKc+wY4kPTdKrSxiY5ik3IatojePP+WV108MvF6q6np4w==", "signatures": [{"sig": "MEYCIQCWxre/8tGWViqBgzZiGCZ3thz3b6EZTB3W01PNu8Y/SwIhAL+iEssgk8z0DqHoDsU1jU4xE3UTflR5Q1s2FbDoQhRL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8704707, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJju0clACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqMgw//Synec9cN0Z0MzfM76EXrODrmq6D38/Khc2CnZn44oLl9/Z0v\r\nfMmK7b/MHNwOVWyqaTF+pmbVYDETDTZ4OqEyIld2BQ0IRggl2IFI9TF8IjTu\r\nIuOSMfXvb6QD9xkyogPMZcVc013KRmQd5Lt+aoA+lZF0xaucu1N6eH8B8IZS\r\nWTJ1sp4lJdn4h5IB/tL7zDY95T/MF6gOCyAV0lbtSqzsh/i3Auckn0Fp1nvf\r\nHXoTuP6FLmgARKbB2D1VNunZ/+WW55BGpAhV8692vqSrBRsy/KxHqMQNGfXw\r\nFrQItTg2ehFiC+lzevQ7f/fG3gK6PqrtOqIblk2yMzAk1F7wia6UvIsHyr8v\r\nXkWMaI/SbZe0Lfbqjp5/5izpE34ElHqNjADKUYAPxzBMq7ubpc9+LDQUSebi\r\n3SacHyJjI9dJwu2EY+oBE+9Ra5hzwXWOyADEoOn2Yn7WCCRwZIaEX7dlHn9O\r\n5BFaVkMqNz/A7mRvEelK2ng681EGbpBzt5sPue7oNhy7LDnwrHAe1v3DBSb9\r\nrrfHvNkVml/5vkMfs7kuydaIzMF27ScOCqo9i/CKRlFjYvMDdxfLvrLN7spu\r\nNI51b33ZOvdo2QZt5INMILvuXXg3YxnoEJw5tg75tZeYfk3vCBKjh615rPqJ\r\nvhvmqt1vmsVrjQZymBDxF0BoM1fxDJ356RU=\r\n=Cm64\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "edede3c49ad6adddc6ea5b3c78c6ea7507e03020", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.16.16_1673217829068_0.26607365712136977", "host": "s3://npm-registry-packages"}}, "0.16.17": {"name": "@esbuild/netbsd-x64", "version": "0.16.17", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.16.17", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "7d4f4041e30c5c07dd24ffa295c73f06038ec775", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.16.17.tgz", "fileCount": 3, "integrity": "sha512-/PzmzD/zyAeTUsduZa32bn0ORug+Jd1EGGAUJvqfeixoEISYpGnAezN6lnJoskauoai0Jrs+XSyvDhppCPoKOA==", "signatures": [{"sig": "MEYCIQCrEMA26OQlsH68VIH9nMTZlcUEsUugC44tlsYkUWyBzwIhAKy4J86g0fv1mAb/HZZwqMM6uacGMN4SGyDFHAXUm5vA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8725187, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjvzDjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoseA//UP+qBbc+zHb7wSApWTKLCBZj+Y+4FeDo9eryicm4oIi18ETB\r\nmt9vH20HOjOy3sXXtL7VSRMeGsF2e8ftlTAY7ZuTnzsNgX3ETET/9T7GKvOe\r\nBuGSYfIE6sOtY585oyVRYEPlTTDE4Z5jNMSLg2yxJ2rLClC6Hwt+KdSHBhrM\r\nD87ciQ9px6GLpgAAR5jaIO8kaGgXdtvnOFWDc3Aux4ucpB5CFXd2Um5LydR+\r\nKwz2cByNs5yXq5RrTqj+NzZnc7XOU2nl18iNkLFzWnax7aQldXx2hcIxEREv\r\n5YnIO5eoZSNd1nxcH653cfT/1BuTq5x3X8Ocfxx7yX7y7ZMtQnf2cX/Yv7Sj\r\n2xIsc3fBZnNmTw0rZ922gj/zKwOwughmXRZIJE7mSgIcpKOaaQ9g4VrwKI79\r\nS7pzSPjSyuU9SGeaQ6WarLFSbjzA0Cxn/fLKjlK0grxwaW4AsG5v1BVTreto\r\nKyNniLPbnfWcAiVSYNt2gvELPR9imIcaM3Caqa9UxT1MZVDd03Uj+1pPSg/8\r\noyncfVUPq8ir1r10eQ1+98wwP7LwnuPcyl90GPCLK7YAB6PK9G0jVCoRuPYR\r\n9sLuJ3nSTX7A6C4JZExcgZe2QnFoxEAPI62J1zPN6wDBLHxsAgSC2Pms++PL\r\n4SkNwRFqMPpIqTduu+3fux95DHNp+L0WFVI=\r\n=EeOv\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "d751dfb82002d332aa4dbfa89c74d25203d28123", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.16.17_1673474274735_0.36208161994192856", "host": "s3://npm-registry-packages"}}, "0.17.0": {"name": "@esbuild/netbsd-x64", "version": "0.17.0", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.17.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "46e770aa6a14dad73d2cdf6a9521585eca1005ef", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.17.0.tgz", "fileCount": 3, "integrity": "sha512-V6xXsv71b8vwFCW/ky82Rs//SbyA+ORty6A7Mzkg33/4NbYZ/1Vcbk7qAN5oi0i/gS4Q0+7dYT7NqaiVZ7+Xjw==", "signatures": [{"sig": "MEQCIClROIEBXxYl7+oExscAH3YNQkZCuhYyniFUNwz6i6qVAiBT0h1rUbvxwk/Zk2iV62Gq/9tEal2Lmhpc1GY/3CmOCQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8975042, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjwjCkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoUPA/9F8V4TxVVYNn39gBJMYEg86X5TugsCz2HCvlxmjqGKs3KARZk\r\n8u/+FdlCxbmx/Kp1BQrHpvi/HhwP7OS/kIXvU5k3utPcSIq3g6eo6flSeXji\r\nSI6eriZzsgpbR30Cz6cGxVoSzyzrOn1jTFmS486ER26FpCQPOvOx/T3T+vRr\r\nZONjfFOjAvMZWGMjs3tUm3UjP/ahqPg2oUGx3oCRwAJlIoSSoV3MqIdl6F7f\r\nc0Zg32bLtwHwjsE34lsIKyR+wJ730SPFEvZklnMYa1VSvQ0TlnVuxgb4lmG5\r\nXBLbySRxsK63rdrl0cY0gaJzavXbo2jRFOLPzECmb7YRwNSphluLGII+LMQP\r\n7AOGlm9tBc2a1dCwXwlHznm4GOCWAPJXpFDvCSvBhTwVzhJVmwpglWk2n2FL\r\nXkrG5ToqE2+4ZDVtG6+Vr9yAna1NM/tJ8IDHAXcT/n7+kB8Jx+xaPg8uZN5V\r\nGa1lDizJRx1N/+B0cdDezFVpnoMBPX140qnst9cpeB0ghj6GZg5vnUFdqPSX\r\n/et+VDaI8OPdGvi6QrHVspISvqByugVIS5S8O3QeqOjLuEWXqZ+VbTdsc7qJ\r\nbAddnHek/lSg1O4m5sy4Kzaeek3gHHRwpVt63jjZgQsNPTyd1N7BoN76zAOW\r\n9ifpVkWbKGjF7gzoM3pnZ4OsZS6E/El++nA=\r\n=bhTW\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "2a18b3d6ab3d1003afbcfcb4a6ffca61e04fcb57", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.17.0_1673670820349_0.6253214155093862", "host": "s3://npm-registry-packages"}}, "0.17.1": {"name": "@esbuild/netbsd-x64", "version": "0.17.1", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.17.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "345a1e1154e7ebea47e53e779ede5f5d098d0d32", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.17.1.tgz", "fileCount": 3, "integrity": "sha512-TrYeatKXA6XIpJ+Lo1qBiYRokSB3EE6b/KxDk7NeD47Zx5k7QrwCEMTlFJGcy2eUlRnbDDMqY4LaRO/GniU8RA==", "signatures": [{"sig": "MEUCICh/4PpVMCu17y3yONuSCKjIyY8qpFoEk02+6agDpMGfAiEAnXiB+5xFmrhB+Udm7BcShxSYxuNB6fNwv8CQA+E+kU4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8983234, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxZHtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpOkQ//RBQE7Rah0RA6NL3esd1Yh94UmpkbV8+XCJXOzBxSCfzikKnT\r\nNpA58cX5e/TgtilI+9c26x6C5j5Wp3ezWdMNZBkPcqXn/xaszjEKUMmaxyJE\r\nxeQtVkAwKwBZcaYMjAiaqpc8+WdRikqkgvJK30JrlkUXvgt5RyQ/QmaKQ+vG\r\ndmzhiQVSnUmdrd79IesXPSxGh9T/jDgXurba+Grke0f3jBmeRP0ip+aCdGxR\r\n2gla82BCjPCYab9Cj/CvIynb2dKcL0U0hF2NhH0EIZDFiLAL8OVppej3DYXq\r\nkzuB9R1FgiCatVml1Zx17/58giI6nMNxe7PmYUObJxADiFEIxYohr0xjDmX8\r\nogyzQxp20OqrHBPnPrFx0LmpWqv4mQouXcvwqUpTBjJRbe28X4d3SOcCyXvL\r\nnfMNVyDYXgkQe4wowjfHgPxys8Rxfc25Rzu9BWZdMQKQ/Tt+fZtPg5MXjRrk\r\nu2UAPXgGjuVbBMQoh7i23NHgfUl5GhdimS7f/b+PibZ1BgdYzgTJAnyC+G0o\r\nWbcBovkTdNA2Rb5G311HkTqs5hYblALmzGe4XglfuCSjoJdnEHJ/0OnW8TnZ\r\nAPvi2k44nB3CsnlDAFAyK3cNHJMzcwhtvOjidyCuRDEOpHkdXq0rnc8Jyczn\r\ntcgEz5oCjaB+CSP3N5x7Vv5EvVdvcwagRfc=\r\n=iEx6\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "002ef9522a4103132cefb075aee3e09b0e4fa3f1", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.17.1_1673892332954_0.4712270948034487", "host": "s3://npm-registry-packages"}}, "0.17.2": {"name": "@esbuild/netbsd-x64", "version": "0.17.2", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.17.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "be8afb6d91827ecb8a8f42a43c63b528bbdd9c53", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.17.2.tgz", "fileCount": 3, "integrity": "sha512-Hagbdq4EpiG9XXJY6Ozfrl2RN5jkXZXd6BD39f43tWz0d8yyOrRZlofM1eA6JYQbdv6c8BUsUOcgopavIqwx4Q==", "signatures": [{"sig": "MEQCIHmb+AvLYwbDWK50xhuE/TRqB8x+tzFD9pDNwE/tyA/rAiBDPkV+o1gfCltiS3Rfe9UqLmdPZ38xdkzsCaXH23PY1g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8983234, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxkKsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpg9A//UGQZXmVZ6FzDqk2bnnYVRXyWTsgC5RSq3H4KulVHbNRkBHjz\r\nCOEFNPLt3OWw8lceiyJBZjUPAxy+yK7SweCXZlfVtcw6auwx8lmn2yjdm7WO\r\nppH+17McVfGwsi5Dzvygixg98ZIbAgqldzfQ3I8Lv1InIP9fOSqH7LD6rTn9\r\n3G4H53GlJuGF3IOiJDkjTZV2iqHgRFGPz4Ozn+hsSSwmPnB5D4Ea0umKgG/Q\r\naXQb1qCqBxIPa4T36yLQ6T+SRw7hKR1tbkiQYxTrpS8rJuwrYR0LXfNq/Lp6\r\ntolTKIZX6U24JWQSNKCitKpRt761WAcYq5dZBPId1hskLgfHjozv5WSainmA\r\nSrjqYCQFfvAJiX6wRfzWNqebzyppR7uTqf6hlAgNLtF/i1LxhLW+4Vpjh3l2\r\nF2bM8Xy7n8voGpLnyxxfW8cxqzbewlEXKGkNEnBgS1MylbYBZbeKZDW+ojz0\r\ny8FyWUU//Ius3hmC0cYyS9aPawmfMamvyg5k8eIMwj0PnAAd1Hs1yCupK7rl\r\nmCvxO/9n9NZZB1stQM+sbklDDse8Sgn+hzSiDHHzFWnQt3H/QpzpPKm4QDXx\r\ns/9/njNVtkn1vtSB+8R/vRYdolrHGf74C5dqSE+0aXEHCJCKOuWGd38k6ceV\r\nbLRYESFNtdwzF/410aX1gq/0Jg3jddMVEUE=\r\n=7hEf\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "a98870a2fa9f7af7024be24cb6833e638aa71da3", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.17.2_1673937580394_0.29577674644741436", "host": "s3://npm-registry-packages"}}, "0.17.3": {"name": "@esbuild/netbsd-x64", "version": "0.17.3", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.17.3", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "b6ae9948b03e4c95dc581c68358fb61d9d12a625", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.17.3.tgz", "fileCount": 3, "integrity": "sha512-4+rR/WHOxIVh53UIQIICryjdoKdHsFZFD4zLSonJ9RRw7bhKzVyXbnRPsWSfwybYqw9sB7ots/SYyufL1mBpEg==", "signatures": [{"sig": "MEUCIQDKjaKjJ4AMLb8dAXJkVMKoQWQVS/GuBGrxwnBVjIrS5QIgcvaiKsOKD5N8U6/bZfItIp07rOsqt4eur0TEotZm36g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8983234, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjyEUSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpSJg//bads/X85AXL9nj8KykmL92/0nEoBX7+IQXFy5tuIh0pRYDOm\r\nSrOFJ3CHjZ8E5reCjGWzzLMhgOcLZk8+1C5s92zc0pmbydybPOBKwWHkIHjG\r\npa9duP08Jsnrx5It+neRmcKVQ5fc9L31tkMmReoadCX5Noid7MuFJpYaEnvR\r\ngv9N50SjWC4WVZes7BknDozGTtntRhRjKV8le/jA4Uy1PDh6gUkUza7mWeMS\r\naEiv4CeP1tVRPU2g/UueuNr5YqHClmCHZ7q+yTeNJaMEQxd/xfTe8EAingjh\r\nQPo28Tke6UcZ2EmG2yIF9qlmAQap5KY/+n7zSZscM4ZVDs5Q/wOIMGPTAoaE\r\noY2pt1YkXeeacWFxZ93TzCFKLjjK9vSUyABnXnvFgqL1z15fP0fc3NScGncE\r\nyTI7fYxa9aqSZ9kkHdYtff9jpmNo5NraUGUdRuix/mi+57AwX25w/VHKCefg\r\nQs4GImoimEDKdyjczQMahUaeu/hbKTVlcA0xODKvBy2kgfW4o0fQQFST+K7+\r\nU3wSnr055fk8JekMmFa+4WwUgPgrxXMzZ8Lsh5j1GDCIRx0ECqf9UzHLJdWS\r\namkUm/FJyyEecm6s8yD2oD4Ggb3lAtD3uumBFER8EhQtKiz682Sjgl9BmvmY\r\n9gNWs5/eG0P1XtCoE4itiFFEX+PX7tJ3P44=\r\n=rMoc\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "acc5becf4d4e81473761091fc340efe16325da4a", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.17.3_1674069265774_0.40187516328266004", "host": "s3://npm-registry-packages"}}, "0.17.4": {"name": "@esbuild/netbsd-x64", "version": "0.17.4", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.17.4", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "e4d5d8022f8eddbd7d9899d58265915444f46f3b", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.17.4.tgz", "fileCount": 3, "integrity": "sha512-zD1TKYX9553OiLS/qkXPMlWoELYkH/VkzRYNKEU+GwFiqkq0SuxsKnsCg5UCdxN3cqd+1KZ8SS3R+WG/Hxy2jQ==", "signatures": [{"sig": "MEYCIQDPubZaREnjO0NgnkZA/83XrcRnmwIoNkuXqJAwTPxp2gIhAKLd450h9sq0/XgOmaUXgsSVR9CS+zoO9I4mEPYa75se", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8991426, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjzNQSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoSpA/+L01Bm4zdxUEZ582vF4s/H0b5jaElZmkMMfd/+Hts89OWZR1/\r\nc7Wb8xVInjJczqOZe0QPHtL8bRW2tGM1XErNtp0QkNkiNIOFLgyxOpel35B8\r\n7z935iAQmKaIeudO+zuVoE3Q7nKlnSKznrPNx/Rg+jntPhqCmEc+Hh2j5RRy\r\nPuJ0S8XR3RShGJ3sfYpiCjQ+j4NhNeUR4A0V3rei1NGXIxhYBuzBP1qtORQe\r\ntnLrUp/U89nTD3QsrgWviAixxU0rnstGzAZCJETeLztXwhJ+pm5+xRalhu1Y\r\nhb+uQerylSw2FErVXjtqV6szbF0gUDGGGDSbW6U/kWriSruTbDcCqPAK66Pc\r\n5IY7UXX7vA+B3/Ico67DXhTrwlfvkfdq39YDDlmVDWVNmjO+lQuNH2s4Dcyr\r\nQav6XwQr0r9sHGh5y57kj9pQO1O7ljav04sMKsFSwZ3YnDx8A73Sb/6LMbkY\r\nWHuc0e6A6i2tktQH2ukJvHnoqy8TOKufKhEkd22yBON8RfEZLRoHf6F1NM4p\r\nVngQUUbOqFWPm/YQmIYAs+hVg0yNSNAUf9HbLBuwU7hclascH6YpKgcQLrlL\r\nlRTZt6a+KbOLplbAmDUMn2yfj074RObcgmMHhkf2jroB50+e6AZXh3qWDaTC\r\nL67odFoBwIVtiPMoUJOe1B1L8rF0XTMvirU=\r\n=Y64P\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "3c83a84d01e22664923b543998b5c03c0c5d8654", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.17.4_1674368018012_0.13532172889109506", "host": "s3://npm-registry-packages"}}, "0.17.5": {"name": "@esbuild/netbsd-x64", "version": "0.17.5", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.17.5", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "47bb187b86aad9622051cb80c27e439b7d9e3a9a", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.17.5.tgz", "fileCount": 3, "integrity": "sha512-cigBpdiSx/vPy7doUyImsQQBnBjV5f1M99ZUlaJckDAJjgXWl6y9W17FIfJTy8TxosEF6MXq+fpLsitMGts2nA==", "signatures": [{"sig": "MEQCIC/z3+iE6MH/7Ie2rVd4XvDTtkXqG9AnsKrLehZrW6m/AiAI9pz06M0aDjBz7/93aBUWuJVe9qrze29maQeS/9OFYA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8999618, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj0/3aACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpUahAAkooixjaREeyFtJcfcgN5hW2k2CCJVIywRvUJPv/POuZPBF/L\r\n/gnIhs2Yxg+9+bOPq45BLuQ4TcOs72Tdwljv7dWmAh+SbfVd7dtncyQAm/za\r\nD0iN6xZrsJbTni0/Jjj7mGiOAaAFCVHOFrOrBDHjZAZAxWOtYzVEzE4c+Yxm\r\nmJt+Za7cdLZz5z7iONkLIJpsK7qvZabnti8LvvY1xed8k7zeJb6KSXrS9/O7\r\nIFfXnxMsuFyaQYehcQCTfKJk1Rx5nI3UMlyAbyX8vwYaFkOA826zMcgqjY/7\r\nJDOjN7VT/4CKMKkbQJ3J3hPXngdnnpB7Hw7v18tsfKN4M+ks3D1iHL+oRelZ\r\nymPLsWx0VF57RPDKrS0anbVreox/Y3h2IH69tMwh9UFkzJW471MujQSTvXWi\r\n2pIFjo7OlKjYvGHDo9dqABDL/PJ0d+vdv5CT9TGt35p2d86Oo8nf3KA34z6r\r\n4kCctK1V1mvJcMgemhT6xKbsjjn+O7kWBdAzy+5Swj2muFetO10WLG+cV8NJ\r\nGSgwoUj5YS52Ok4EtAkH04u4KUE4ehWOvvyDnOhZRyZUWWrWecq38s+QvmBO\r\nmJzcggnOtvlnFPKgh89cmGywNeUlWTSLOXyq9/ePpnhm5XZn/I0awrImRGNa\r\n2VnrmX7GQ1IU28EriXg/l+//Ns6gI1X7Up4=\r\n=kDPV\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "a8b660d85a0a57087a0f188857519f194f52b84c", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.17.5_1674837466498_0.23520569463048258", "host": "s3://npm-registry-packages"}}, "0.17.6": {"name": "@esbuild/netbsd-x64", "version": "0.17.6", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.17.6", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "e84d6b6fdde0261602c1e56edbb9e2cb07c211b9", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.17.6.tgz", "fileCount": 3, "integrity": "sha512-EanJqcU/4uZIBreTrnbnre2DXgXSa+Gjap7ifRfllpmyAU7YMvaXmljdArptTHmjrkkKm9BK6GH5D5Yo+p6y5A==", "signatures": [{"sig": "MEUCIHJUPtNZ7EIERrPuZwUMrsdFwTFCkZT8b9y7B2DH0GbvAiEAjs3poDIw6p2F391ULZV6iyMQJzcjSyW3JzRjpa95K9E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8970946, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4TI9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqcUg/8CtLd1NJhaIShCG3gxrkj4CSxZ9v6obwJZmwDKhtSn/FkfP7G\r\n0rqETIDRQOz//JHiHgxlxfJ4BbIcciYf6zfRUCmWsQ1dIwyd8DCLdiVJroHu\r\ngmziu6YitnbyM0Q/fcoe+5+SOe1XC+fGzph+Qx/r3Qvvr1lv5TDhrw3ywef6\r\nbDMvz5quRGmCY6k0+NpgpiXx0OIZ4aKq75EVico6lYhyW3r5X22jtbJha3yR\r\nIt7xd3OUnFk0Mt2wSKt5B7wyqsGbb8t5PbhJotM1kAqrDaoVXkHTbZ9Hzh6H\r\nNQpYJwUHUv/UtWWBDbX/dUzByjpPwrf6SCio7p7i1rRRUXGFBiV7bOEQ6s6M\r\ngVSUw9Cc6KmSpqnWdCScFaxL/0TRqmMNL+IdkuDEdZRuB4p6faUKsmJ6lhGV\r\nE4BunH2kmXdo09k91NgVTeHzdakLEs/Rf0JKYr7LR2LHSjqrmIXpNLlsOUsn\r\npTHkaq+hdPNlX/WkCccSdm6wfKkEBHP7U0DiPFSE7xOyj5frG2yrDvt/V9nQ\r\nyavfxnx0Js4E3uLJsXVlApqeRxB1Mcya9b2e43t1Mz9j6+1pTaB4TqjerK8Z\r\nlcEcHnhlPrFNc8FXlIsYHSfjh6dRkaQToZclfroD6Svj9vM9dZ52SwqTZrPR\r\nrgSvwOiwYs9M3hJOK1dUJ/IbrMexIC6IpNo=\r\n=wS1J\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "e1143a75dd5e7d9fb8591096edfa123f6eedbe44", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.17.6_1675702844880_0.7114471164026357", "host": "s3://npm-registry-packages"}}, "0.17.7": {"name": "@esbuild/netbsd-x64", "version": "0.17.7", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.17.7", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "6aa81873c6e08aa419378e07c8d3eed5aa77bf25", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.17.7.tgz", "fileCount": 3, "integrity": "sha512-//VShPN4hgbmkDjYNCZermIhj8ORqoPNmAnwSPqPtBB0xOpHrXMlJhsqLNsgoBm0zi/5tmy//WyL6g81Uq2c6Q==", "signatures": [{"sig": "MEUCIQChBOd/lR5TUfdI5M2I8Yo21yscfUSz/dx3G4er56e7mQIgJJ7tRf7OSM1JvtlcsjRTG7cKo0n6u0UXpDwK9OSkgrQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8970946, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj5XMgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmopLxAAnFIQRzk0HOctvg0Y08gViHP5MRhx+LoToLU6g6NlJLV66bBt\r\ndfFvkDJ9T7CAmS31PpechaMwYgo8f+BBmjjwzfOU1oAXYYX2v/QNM5/IBS2x\r\niFxRLPYv63d/EIWWeQLbbkYq15/XbLxYD7By8PHyxRCWTPFA5jievOx/Df0R\r\n/bY6Ypkc+B9BC4l95caOmU8Hr7eVI4NvxulPwxAIzvUhlolWXxkBORthBsr7\r\nHcmE3XYtGWEgx+1ijLCMfVLVTjBZIh334IPEy35WLsyxIVPvp40a/1o71XJR\r\nCQ0NLzqNKoVt2RA5l0bKZmqBd/26nMQJDjY3It8ccBcV9W0M7unpBMlrdH3Q\r\nNcdyF5ulzbzx+c0HTyqOZ4WB35uI2TeBnh5TwKecL6IPn8Xm8EYRF8Fk/xoU\r\ngTTWll9EnxmQyFJaLinE+ifwCqwxOosCpyYbLlwv6b6qD+iC+XxS6OtxtXKd\r\nlAQ7RwjfznW688p/8uNHJV9ftWPYrZAu7kAcX5Q9OQXCrAQBrjE26byeBJaJ\r\nRIq+wuXOBdLIUMFgcpgKu82/EzQI8TQ4+/zq+wZoaIphLq152zFVIaaj+L5O\r\n/jfqOkYzJHYVzmddumjxisdGoC4ZJ6r54b9gjzU2h1t4r8CCTPjguzUoS1XG\r\nA1ibT8BC1TajMxxXnDb5fWL0Pua9X8iQ+v0=\r\n=8npr\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "e345b13687bc3ac86f18f4a266a162653544ad31", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.17.7_1675981600691_0.28927096198590996", "host": "s3://npm-registry-packages"}}, "0.17.8": {"name": "@esbuild/netbsd-x64", "version": "0.17.8", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.17.8", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "b436d767e1b21852f9ed212e2bb57f77203b0ae2", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.17.8.tgz", "fileCount": 3, "integrity": "sha512-eKUYcWaWTaYr9zbj8GertdVtlt1DTS1gNBWov+iQfWuWyuu59YN6gSEJvFzC5ESJ4kMcKR0uqWThKUn5o8We6Q==", "signatures": [{"sig": "MEQCICM+xuiOSbSk55uIOrJbpZeQmEFzGEgo0znKAHBbZmMFAiBxO5C792WGhrLwu7KzNDiGSWVfVmoKWyyzXNpOILpvtA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8975042, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj6do6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpkkg//eJaM8d/CsIZHzLlDR6CpdksdDfVNe9lh07gHHTNVbGN+kMTB\r\n89479Qu4rf0ndYwPo2WLtVnpiP9Ad5KaoEztvfkwxhKMDf55RGIt4FOhddij\r\nPHFQKKmnSe59A1wisdHTePsroZHqwLCpNELdhMw5ymbxtMrRu+8BcRqVNuE3\r\nEbdpYi5L7oVlddxFuM6MFm8QJkubc0UNLkVgelP0fF0QGCJhETslIbniuyqJ\r\nLN9B1tf2wE2LZPrX7fH2Y<PERSON>pAhPRtZ64QkFbn2D8zpIZAmHmqczxgDThpHrRJ\r\ninLbCdJm5m8EJkKImABUppNeqMM9ZO28s/Rie66HGWhdCuCVxR8sgORlfIJl\r\naC3IB0Ln+nJSzatChJUGMeRD0mgjeP6InUfTrQZ9TUpu12Sjwge0/KgiE63e\r\npS3OyKywwCDrYarBOBZtl+YRX0Jx8noDcqvwEiDedF71+FBPY8LbUpLUuqec\r\njvEnz4W892m8RrE2j8xi7EH/d0NoxxnU9VgFeyqLKzRWUKW9HMTWqeGjgdeR\r\nkSed1daOAjPoJveSDd0/jHh7cGOSSJsja0GIwUJNuy7NZL8I4tWUUgYZI5wD\r\njQWneiP85WC3aAL64XZF+PTz/Yh5HoVO3cCkJcTPJ3VU9ICbM+weA7FPSTZe\r\nsg/MT6SM31Tmy4BsQL0Qgb6khfSFWmTP5hA=\r\n=Ur0B\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "5e0b1cd2ce6297cf31b132e413134ffe2576c668", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.17.8_1676270138520_0.16029875577339414", "host": "s3://npm-registry-packages"}}, "0.17.9": {"name": "@esbuild/netbsd-x64", "version": "0.17.9", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.17.9", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "50b1fc5a6e0d13c8ff2b29a154cd7770194489d9", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.17.9.tgz", "fileCount": 3, "integrity": "sha512-BRoQyPJ7aiQ7USFCtGmmrYTbRDa9muZAwoYchfqspd+ef8n2kKcXGQ0K2OqcLEqNFOwhLpAY4y4YAl22FbP+BA==", "signatures": [{"sig": "MEYCIQDZUUPL++vvgbfF/jEsQb7Z5kjRSkk7fNZpC0PivK5jYQIhAMwKUC1TONShhsbu3qErXjlf5Ger/8H34MIyQ1Y1liyZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8979138, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj8mAuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqRqA/+LnBxcKCXtnmSo90REaol1lMPk94XweSkd+LnSiLEEbfbF/qM\r\nlIlRGVAAR8P70TTlS3suRXpJlRlGaN44pG/cpANNGPwGMNl7RplIgGZ2BmUA\r\nXLCrfbQmgyJVojiYK3eTq4DBOb2qMlU0d+k/07c3oQVN8/hmTWxHWgVOU6YU\r\nTQElR+MCZ/AoQCfVbLLc1pIWoGTrc1jm1N7EVWk9px3LjwQgLR/YC9o1BI5T\r\n+7AC0AWpIBNrAvI2aEdF191N+a0lg2m2vtM0rusKzH5vQYhr3/fSuJiyOCvJ\r\nQ4ntQnhPzDQ8vJquyJwB/Fb6eXnuYxmIUdeCfiEZjTmM0lAAHKJu8nyWm4pw\r\nYq1gNZsHl60PKUzC8yPtyUFOo2vWD/AnS+YOcZ0ByLQY1TQ+IJnQvcVfMnM5\r\nPrDrolJ5tzZbd0NT4a2hbWL6qgyk2J7HavgEM8RhtfY97eFx28xf6NTveXTp\r\nF3LwmRZVbevp0gOCsTvoc+cL9N+oJjmejJra0fcnHQ2jMmEX57uYj2T/PsGF\r\nLmuksrmuk/BZHgGbg7ulOCSsyJQ9qJOuQ8YcjTn0sBw9ir7bsXK1qtHiYw5N\r\nFpAIABgnEeGjEBKlJGbMYm6WDnGyE6EGRymsO58XfYSBbwh3by2IFFtBwDfL\r\nKwSOC+C0/Ks04l5pOV6AZrreuBNrlHjk9fg=\r\n=bQ75\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "3765e880ed0a55b8e62bfa17b7e004e656eaf914", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.17.9_1676828717951_0.7546713024940563", "host": "s3://npm-registry-packages"}}, "0.17.10": {"name": "@esbuild/netbsd-x64", "version": "0.17.10", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.17.10", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "2d24727ddc2305619685bf237a46d6087a02ee9a", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.17.10.tgz", "fileCount": 3, "integrity": "sha512-lOMkailn4Ok9Vbp/q7uJfgicpDTbZFlXlnKT2DqC8uBijmm5oGtXAJy2ZZVo5hX7IOVXikV9LpCMj2U8cTguWA==", "signatures": [{"sig": "MEUCIFUxFLhi0hfhnvZNlQeSaOxkM6Zke3VHVZMwN02jzNQQAiEAsvqDpK3BBW1c0Jz+jcaMKuBOE9nRfpXLXNEycDrt+bg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8979139, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj87PuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpDxQ//e6JcYHmOG7LU3hy0VFdnv2kGIZsd6+ZcL5fbRH1BLfMoUkri\r\ncmX4C4Jy0ocZaCpsYXfdILLUO1IqwZ5yMHrx51eD0qLEmlUToG8mwWvHRjsp\r\n2l7nexyy7kNiCWNCvcM1wGPiXHizKveK19LQXoL9qufnSkLB8svdLnFE2L1m\r\nrZjiOrpr3D3Z+5x0piuEnbNKSJHSzNvne5M7BT8kVACLXGt5RXY8339Pl6Fl\r\nmQXOSBz02GOdA72TrnaXbYgaZX9DqjkglN+iH8dKPjEsWs0OBCCEDIJ37Ifz\r\n6dIRVBsyiLpd2uKTwzr5pCsXlEiKgClr/Aw4PEdwjFwrG5r1gBRDWvFeq5wk\r\noqXL4j15qOiNuy/tDwS4UVnMr6PdgkNy/4p1/jjVovA8h5odSbVGk9614KOY\r\n45FUxQGKUjfikb0ME10a3xomhDZTfDuaRGZCfm08BrLTAwz3bI3AFypDcVII\r\nXM28RaFPb/N8xCcSRTPhf3HGeMo7LDQvt/imeDMjzl5EF3GkYvDnzCMOOCwA\r\nIKPQM/riHwO4t7wcOSKmna5ijXGcJbbfpRZyC95p2WC/+hZTR5v8k1NiTXbp\r\nlMx7axf9Lza+0Wo8QIbuPNWvBe8NDdSW0iqhFG1U0zgaX7pPhzX55BAKgAsa\r\nCeuWmmYFyvevvZrNjkj+qlzObOwa7lDFt3c=\r\n=u7Zv\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "996d400a7ab25b67b80122e2d4a8515575918e79", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.17.10_1676915694307_0.9986391603234341", "host": "s3://npm-registry-packages"}}, "0.17.11": {"name": "@esbuild/netbsd-x64", "version": "0.17.11", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.17.11", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "b589239fe7d9b16ee03c5e191f3f5b640f1518a1", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.17.11.tgz", "fileCount": 3, "integrity": "sha512-4WaAhuz5f91h3/g43VBGdto1Q+X7VEZfpcWGtOFXnggEuLvjV+cP6DyLRU15IjiU9fKLLk41OoJfBFN5DhPvag==", "signatures": [{"sig": "MEUCICduy2sulLyczoL6dW7FZFLKpY0Fdq4onICIITZC1p90AiEA/sdeu1wt2/1LBAi5zabyKibqTEpfga/YWr9x159DvOA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8987331, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkAndJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqkcg/+PWndxK615IQJ+uwOw4E8uEKwRqlD326KNzApSMthpBUUliTQ\r\nL0C5B9n7desT8TWfAZlP54zdE5pKXj5vfNK08X9dmu6ZPXNyVskxGJCnWL08\r\nfHfs+UTWJNkIfzvfKkNxys7N689ZghpFYOD7iO6g3wgmgYTWzHbc3AR4DTSb\r\nkGr1n7K9lMFnXXV+bI/lmThJb/MQAcAllewxVojjPDK4Lelqdleh9q4Etofi\r\naswmSqV9Mwb9vD7SkHvIXjRUWjeleR8ETV5kPBfgoMPBmvMzAgzw65HjMfvO\r\nrWBTP1dqdFrw7zkrEJlOi3CVJcKwQyLc6/SIqDer25cVq5VZKs+rWO64zTct\r\niM1gyOEhqFChz9/h3HeO4Spf7eVTVxddu8pna68Zqj2KfbTOQxMsie8EPLXq\r\nCtGaw/G6/SoySxvD2Jzp77VsbhLan47XdEOElGRUUn3qh9N3Uvva3ykIUf5e\r\ndtQAs8njfk5v9O6DmxRi0w6Pjohjq3O6s4woqX1Pa3j9T9TsJdR2oFYbbXWK\r\n0odeaX4YZmIasQmnNPPDHxAQrTwBYPmcxfUg7va4+51DpmunOO/dgaitgsV4\r\nH6t399NWM5UWub2IEEmdggzoE7g9cvWV899Xn25j6y48mL5mkPdHZ2GBeuQZ\r\nOLPgzJgP1EFZSe1yxDS/1jJlHTPFuCNCcU0=\r\n=KrU9\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "38cfd53020d9e0454bae0956e549546c55a66aed", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.17.11_1677883209316_0.31506003888733103", "host": "s3://npm-registry-packages"}}, "0.17.12": {"name": "@esbuild/netbsd-x64", "version": "0.17.12", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.17.12", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "c7c3b3017a4b938c76c35f66af529baf62eac527", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.17.12.tgz", "fileCount": 3, "integrity": "sha512-DNdoRg8JX+gGsbqt2gPgkgb00mqOgOO27KnrWZtdABl6yWTST30aibGJ6geBq3WM2TIeW6COs5AScnC7GwtGPg==", "signatures": [{"sig": "MEQCIBFd4NIXGb121kQwchmahlyKCWR774Y+OIqLWux/oIxJAiAWODjP+PogrNt5ZPDYIQtw/hhAroycDSLd7W0m1Ca9FA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9020099, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkFAWzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqaZg/9HROgwjqGsrBe7UbZUcJJfh4My1NPH0bw91gtFqOMVBtIxF1E\r\n/BkEAvbJvl9FvsZXeN6ZeIUrjJ/b/k0nP6jbVcUeXvj35r53921pw4X3of2B\r\nxxcE5ZDiwu0Wofb1Q1cShpJO9KniHJ6IH/7YP3fC9edUWr5NK+aOW47JG+Xz\r\noMJhmrDi6SNos9REGNjDeTl8xav/S3uZma+pEEl2xndeAG88eyOPZgnI8xHI\r\nNxaaCza6elCj6wX2ruu5aDSlDqTcFf01FVsYO1WZ9h9utiRsaa4ceGhD4lU9\r\nC07gurf9GI5tGp3RDggOO/g3xcxmPv7Vy6Q8bDF0dScP4kl7BpX76p81HAdD\r\nbyJhzosnv1ZDH9oiasLrCxf14Y08r8MV4akKIz8AAekx9r9A71HSgUdAKTct\r\nE3o3OjdFJiehQRpJ8grvFvwCf/r+idasv2xLW8MGNNkWbAk+6pfQ/RZddhCk\r\nOhZwO50C8Wmc+JQSHp+uH82FHoSFQYXqT7Chu70wcWd3TuMC2YBvrBc3E7t8\r\nYyoCvclapQUtpwG4vFOxOGuc1XHovGSsnFKRAH3UdbT01THpv3I/0/cSvVkL\r\nmdPqYHgz04Ni28kjpcDG7w4SJtxkBTNY3GbKJhom0Akk8Hg8koY5KfnuyUtw\r\n0e6Rbu82ZzXlcpbi7mlTx1brcqDA3yAhwHg=\r\n=kMWz\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "495216dbea685cd0e10172a866eeb8ca4764a0bf", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.17.12_1679033778887_0.7790620897231455", "host": "s3://npm-registry-packages"}}, "0.17.13": {"name": "@esbuild/netbsd-x64", "version": "0.17.13", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.17.13", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "5db5d517bd6395e63452a59cd4b00c12eb584c3a", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.17.13.tgz", "fileCount": 3, "integrity": "sha512-F8PXDeT+3eQpPjf4bmNJapPLu0SKKlWRGPQvBQqVS+YDGoMKnyyYp2UENLFMV8zT7kS39zKxZRZvUL3fMz/7Ww==", "signatures": [{"sig": "MEQCIEC5CR1x4aVxz5u06VzlJJvjZJ10ydLKoCEnqdMkC5waAiBG8rU/5m+fqiYF0oG5aR+pTN4hiLqtOpTc17dHh41TDQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9024195, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkHfKAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpVPA/9EfI22PoY17RPNSD1s8PN9M5dEojnIJTtKH5qGY5ySmDLQQD7\r\nQoTzs/hQ4BDCcrZ91psjfyNJrvaUYEES8/cVRX8AQ/ZxdGVutMxwJEyLTKny\r\na9UZcCew3BnnWU/Yi8WKX5MfnSjTmQoFQTFDwXP9nVfPnMkBcj0bUUQOunGY\r\nmCvFIoW/8Xel63mdsgGRN6+C7MXYgp+bBvV9x5DfJANAsWpoo3Lzjpzy5XbB\r\n/NZNx2dCd8ExMGw9Gp+4eWgRdYzPmado9DTUSriKJmy7lYcVLfgtjMXTpoWT\r\n0w7GJEa9r8T5ytgQkOE32IYt5CzaEebU0iuuRvgfCH5Rp6MQs5GkehSZB28R\r\naH1A6cWeSZhl9hp/KC6kE7pxKRqqErULNw5EwoV51sHaoxnPeWAgSiGdaJMJ\r\nFVyQmUvd2sUB6UPCgTvpLUqdEtCwKRxp3N67PrkjwgopIyR2MQf2U/pE3P3+\r\n9WObJZlWQ2Cs8EhUMqVckWI2wQlt/wIKvwnCSZAdbk7r014WOEUkQ+pRmygT\r\nG19BPJJ6B2v2bd59qxt8n+WEw672GyQOoS48dDwPzCwggQDu4aId1ksr5CG6\r\ntQ5Wwi+rmrqLM8+jbUU2We41IVGEFsvRxLIW+4gCjYsESak/MAo1F8ip6Nrh\r\nzsilMyuKJyygvVpYm/afZ5J3jD54m6bQPKQ=\r\n=yKiJ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "8dbb85531a9971058f4d8739cb52e98fa43b40a1", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.17.13_1679684223801_0.9644533124713681", "host": "s3://npm-registry-packages"}}, "0.17.14": {"name": "@esbuild/netbsd-x64", "version": "0.17.14", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.17.14", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "5fa5255a64e9bf3947c1b3bef5e458b50b211994", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.17.14.tgz", "fileCount": 3, "integrity": "sha512-nVwpqvb3yyXztxIT2+VsxJhB5GCgzPdk1n0HHSnchRAcxqKO6ghXwHhJnr0j/B+5FSyEqSxF4q03rbA2fKXtUQ==", "signatures": [{"sig": "MEQCIDkH2E3d8UD4Tpou2qlMRHcfYYj5BDyhlTWwyMnFeImaAiAfelSeDAvjjpkm7EgXYv5OcecIoYV8OuKVfPVQoIrbEQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9061059, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkH7JMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr1Ag/5AJ3Cq8gh8U9twBzbEs44clUC5XWwd+ov3Nt5FsmpEZHbl0C5\r\n8afarUx87xVwZvqoDkEd2jypop+pHEf1XfuO4YHobjGOBUMNrBT+QhuxzGY7\r\nL1uYcr9M1HDMTKuAPwJ4ccp9ySm2GRNWAq1LVu5YuT6c8ZZJxoRN7NXTIUB9\r\nt8Tm+Wo37yFh36tmYAPVJ77ozUslnoG48DrOcw+yEpMoppc9gbli0qLPA6R4\r\nOLI8AAYEAhkH/kOfL/lVuwXjWV2dBviubO0drlwYFRE6dFYaomLemWyrAcsA\r\nai2vkxqSRJqwE0PRGVypJKTrm1rJa4HDLyZ2Yy+CxNW5SHJLAfMhyp6ywBSF\r\nGUPv/o8aWWbzGa+bGUwDBhOxz/YcHW8NJcPcJKDU7kKIA25ZoMgI3GwgtY8v\r\nMCpJDYfwSUYsWAs4bqqo5S/CZHDiMWN7n0/sOhSdIKNhd6uyrt25K1ykeYvW\r\nClrbasrJCjMyFxz3LYO8o30xqaq6IlZx0LRaAs17v4wIiTqfJ4xzheD5QSTc\r\nA+uZsidTiRO68XP9xnHC4alu6YrUoejIo8pmhmj792PZOxufdTBo9e7kGa7S\r\nCg4V86fjRgcfVwzWsZmlB5hcnAjMaeWDb6a0HiiyyvrS+n4bb7MADiiivqyD\r\nHjzXQpMl/6sXBbEpa6yTTAMYr4uIH8uh7ZQ=\r\n=AufG\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "b2b897870564a6b8e8bc802a140c55bf602de31b", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.17.14_1679798859892_0.806318634470278", "host": "s3://npm-registry-packages"}}, "0.17.15": {"name": "@esbuild/netbsd-x64", "version": "0.17.15", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.17.15", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "8da299b3ac6875836ca8cdc1925826498069ac65", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.17.15.tgz", "fileCount": 3, "integrity": "sha512-R6fKjtUysYGym6uXf6qyNephVUQAGtf3n2RCsOST/neIwPqRWcnc3ogcielOd6pT+J0RDR1RGcy0ZY7d3uHVLA==", "signatures": [{"sig": "MEYCIQDJZCDWy1WbRK9OgKfl4eQ+LUM0znCu3/eos1kWf/AoDwIhAPLNpxnfKcMxOBF4AWu56QwCOO0RsHu+q1YKc7wXtqt+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9061059, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkKK+oACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpvrA//a0arOQPpRVLd/k/bJHgAd1Jf1LQq7QmaiczwifpQziluJJO1\r\n2y3NkUBQCTq9rCi5HtNUOu13MhgIm0b5aGTzIq1pQI0WbEJss/NuZdLL8c7k\r\nzp2JgUEZ0BiGYTrLBRfYnsLTngA3dNdCfy2yGjaZHWvfKbFNItTVsrexQcML\r\nISFNQqfzAuROo19buWsRBMmqWuhM7CCmDO0B8xjUjAhnNFMtDz2xrxDbN8rf\r\nRpmj0cXtkeEJ2bPKjw1BsLcOj6oHgqcvsIhUBcGe58Jk/7OwaDsSoZ822Ron\r\nmanIcA1EJ2U9DEw6roqZObo3sOzWE1+yRjipjdXwwXWaaFdWwGgL0xoODcyj\r\nGkFjPr/fOmF/DFX7FO8AtWCmzsMBzFkdd7nG2GbbE2schYXIkOtifCOHNDbX\r\nR6iwjjJBHRBkX5tdmKwEsshuyOcu72k1APxdpc0ipLPBxn2ge8VcVvgBmELp\r\nbfbu7tpefnOvN5JcKr491TcFbY4lfRQpBVgjFEXASyGf4VDaE0SGREV2uutm\r\ngrRwjDoqqlMdVNyuHZarHlcqOR5T8wHO9CbKcsb07Wj0ezOaMOZzT1m1DG/a\r\nqvjZ7g8G/LSQjIB5IQ9O1qgTqiCyezjDYrmbfwloPoW5tV9MhJHoCMjMlvBb\r\nHtQln2Q3LRf23aHujJvJ9wvpX38nXM2yHjI=\r\n=OshV\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "ef912892181e27386205d110c622c55c4c1df856", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.17.15_1680388008234_0.5986355433528423", "host": "s3://npm-registry-packages"}}, "0.17.16": {"name": "@esbuild/netbsd-x64", "version": "0.17.16", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.17.16", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "ddef985aed37cc81908d2573b66c0299dbc49037", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.17.16.tgz", "fileCount": 3, "integrity": "sha512-n8zK1YRDGLRZfVcswcDMDM0j2xKYLNXqei217a4GyBxHIuPMGrrVuJ+Ijfpr0Kufcm7C1k/qaIrGy6eG7wvgmA==", "signatures": [{"sig": "MEQCID2qgIp3cmnLJYlNewl2IpSTYaG/JIwu7erZHM3wZCK1AiA67YchY44oK3NzaWsCU1gpsikm1xqyl06Z/h30MwpsyQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9065155, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkM5HyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpLARAAglMI5mICTBHDECBqM6lRQZshFWmf8374PMmiaXBZsFHmCGrC\r\norKLSxtX8UsagOaTWOnPhvGgsO8g242gOhqP0rUAWvAZgEknfOgmLcJlEKSc\r\nNBaIJzgU5fAec3GQwPQETpv8B3oOAvWLa8ryrxWVwasYDF//IT3zdJs1XJPQ\r\nuvJoVKPGdPPBwNKDohZmxXkuKsEZoD//iN6UoAvlWB15vlxi495khSYAIHrU\r\nuUSA6tq9Lc+dcKj0vaja98HWaoYtHJbmWnNhG9K9dFbWdtEhvrPeWzpOvWhc\r\nUIKNQc1lev136Rbox0LuaL2RqAT3skgKaOZvxxqrkLqTZswhtgjFXzcBJZcF\r\na2RxdTlsUNMHcET7BzI96OVy3SkLklqg/ir7ubw0e5tI3K2cLDhqLBKnqV4o\r\nfx6CSEkDLOn1VkddVgr45qMKwkBD9e4f/5ZK1xdS7MI923vvX1LqVCvm15a1\r\nRLgf2qcIfnazfILpgzUORq1+U7Bl1v9SriHF40YijGvbZPDH5hsgj8xZdLXX\r\njXWxd+jdFdl8jfHbWtzDgfJlpzeZdj+7mMLbJpnQBWDwYof/lMUxxf1zJue8\r\n2+8T+MbZqAZPMJDytKMoYw2Ot9p0P3df0/SAgUjpOFAfi+3Evyqpnupm/XLt\r\nWN6+GgF6+x6XmSPVq8nkFcYcNZCh/ZCmoWs=\r\n=SFbg\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "f0704baa38ef9a808b7e34cac75aa4ac285bb087", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.17.16_1681101298028_0.6848692801385561", "host": "s3://npm-registry-packages"}}, "0.17.17": {"name": "@esbuild/netbsd-x64", "version": "0.17.17", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.17.17", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "1b5dcfbc4bfba80e67a11e9148de836af5b58b6c", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.17.17.tgz", "fileCount": 3, "integrity": "sha512-RNLCDmLP5kCWAJR+ItLM3cHxzXRTe4N00TQyQiimq+lyqVqZWGPAvcyfUBM0isE79eEZhIuGN09rAz8EL5KdLA==", "signatures": [{"sig": "MEUCIAQVgY2VqYy7LwrWWhEaKDESW3WM7zmnpLXmBakngrbVAiEAt6ypAWmVEhAa8nzokIJQOzU5D8EOpbZf1/cFBqHkh10=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9069251, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkPGdZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqNKBAAnR5arXb0jWewddfsiiHBq/2dWPvsX6V+wTrT9vBn06IjsCd0\r\ncNVxoI2rxKVdKrIW7aAoePdXDYQGUyu54ZjSJz9qNv1gRlQGDRZfS88J6VjZ\r\nRudWa54oU734weDX8x5WwubHfNvLf1f1DNdfNSrXPVbcfT8zf2Fmpq5J1jU2\r\n1eT+PuHp+aNqWx4qLcit32YLkoD1YIY/jLMLlbcGLi4eeZkNxBl+9r66cHBP\r\n4DVyx2FIsaRJm5W8KR7MEz/kHtiFosQ+HybTQS9Xzopz8p8SNkrNk+8rrhpS\r\nDXzy00RK/f2TSGVBAjrFUfv8i6OGZ0wjgoP2kdAduZ3LXIAKNpgInH8bS834\r\nX1dszfDFS0skpD1W8IfcrffvtYx/Z9171hC5HBKqBtVK5/v+QqL6LzYJeTTl\r\nYmLpIudkjuMq2bpnhdmxnt0LgFqax/0YyfOAtLTL3cTVRGyaNwLW0N9TriSm\r\nG5/LzCu78iLxQRuTph8RuGpi9jGFP33Aeh+0aqc425jvW6E4X6EmI6kj0Eez\r\n+9w7vfPlYmdZgTf8bR7YY7T2MNjZZuhwyUez+68hwNdIMmUl6qzAQqVk9nxv\r\nka8RsfpVFhG9Oy/LQUehjVlxhPhlEZeXUvUgQ8ch0NQMhiA5i5cKjFuzkr7K\r\ntWdOtQWsQ8umkR/Tgypz6hrXRRa781B1Yfs=\r\n=fFpq\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "0776a4be2bb80980482b123a9a05dbf55cc35683", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.17.17_1681680217281_0.30796688833218555", "host": "s3://npm-registry-packages"}}, "0.17.18": {"name": "@esbuild/netbsd-x64", "version": "0.17.18", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.17.18", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "789a203d3115a52633ff6504f8cbf757f15e703b", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.17.18.tgz", "fileCount": 3, "integrity": "sha512-95IRY7mI2yrkLlTLb1gpDxdC5WLC5mZDi+kA9dmM5XAGxCME0F8i4bYH4jZreaJ6lIZ0B8hTrweqG1fUyW7jbg==", "signatures": [{"sig": "MEQCIBsaJGvcno0DcUNAiHxeal+dB57/TYJY9w8zhO6OS9gfAiA4XiAJBcV6knKEq4Jowvf4ZMjA57DgGEEE6zn0kf5AdQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9073347, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkREZzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrt8BAAnyN6HhcfypH1rmQBT5LtOjkgVqsToNaf/wRxxgwidjIKvsaJ\r\nb9rRbRiJeh/DKXiCVsTA4W6ITLE0ZD/ErEmqdZ1I9ewFsuZWPHga44vKvsK8\r\ncfsIH44OAbWZEN3bW7VLqFkvmopALJdRGFlSyKGamdqb9/oUFEYfP+/i2dn5\r\nSIS+Yvu4gvm0k7R71DoOxmU/+6oVRejS2DtVnox0R67d2khJqFXdWz35Lcbg\r\nsgMVMacqjDqGzxxeaAtMonUU382+xm5yL94d0OIvk+30r9/X9bVhy0xgzWGH\r\niU63Q/Nkh88DgmvrkhIBrwJxPvKn0ZK5V9A/Ul5ovBsKz5eH0dAhdwwQKLUH\r\nky8cb+Y5+a/x3DzruPrcxDyd230ysXKdnMcu1edHvR5oJWHYiVvn3/kEhBRz\r\n0aeda4gCdPWp6i6FqfaWjqOkaMLW8EaUTsSdOUPIWYKeALSQ4L0HbgGCqh/W\r\ncCE7RKNAoubduimdJx8ukeXpxq3sbhJzcPWmZSQYN4tvwoLDqy8lSf0fhcUQ\r\n+p47x94IHCRtUTMqQbIi7oln9aK6g11Xbbk6mHhJcAVHFfXYCD9AnI5AaNJh\r\n5+PiDxfgSUJGJLFXWxf/afVPd8YrWCG1sALKh7B/e1tjkJEd+89pmP85pskW\r\nY7v7YpJaWd0HqQbUQVM2ClIe2X+ZIN18cf8=\r\n=27KG\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "ee646b4ed8d3b9567e1a0ce2e478241b68a2a2e4", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.17.18_1682196082934_0.2967696360722023", "host": "s3://npm-registry-packages"}}, "0.17.19": {"name": "@esbuild/netbsd-x64", "version": "0.17.19", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.17.19", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "c29fb2453c6b7ddef9a35e2c18b37bda1ae5c462", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.17.19.tgz", "fileCount": 3, "integrity": "sha512-Cw<PERSON>q42rXCR8TYIjIfpXCbRX0rp1jo6cPIUPSaWwzbVI4aOfX96OXY8M6KNmtPcg7QjYeDmN+DD0Wp3LaBOLf4Q==", "signatures": [{"sig": "MEUCIQC0god2IegUO9kmXj50HhMMMpk/C9EfxKXoyWXcYNwsgAIgDEtgD6eksSbi/NsKQqEFuw7xA6WpZkhaod9pwWKrFdg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9081539}, "engines": {"node": ">=12"}, "gitHead": "d47ab43980c457db27d2671ab618cd5c40a618a7", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.17.19_1683936387066_0.4490895532970791", "host": "s3://npm-registry-packages"}}, "0.18.0": {"name": "@esbuild/netbsd-x64", "version": "0.18.0", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.18.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "a9c72773bf301467e74a05c16251780d2fec4ef3", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.18.0.tgz", "fileCount": 3, "integrity": "sha512-BEfJrZsZ/gMtpS2vC+2YoFGxmfLKiYQvj8lZrBfjKzQrwyMpH53CzQJj9ypOx9ldjM/MVxf9i9wi/rS4BWV7WA==", "signatures": [{"sig": "MEQCIA6+M3ztH6wOzb7Sg8VSY2fXUuvRR4dy/Vgt4liHSzyiAiAQWkEN5vyY8Spr6ZSJAfYH2tTxROCyhRoYZHWc/42chg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9085634}, "engines": {"node": ">=12"}, "gitHead": "4dda49d4ea86afcbe715bdca5e8f4b13659e0c2f", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.18.0_1686345858132_0.6612932917199583", "host": "s3://npm-registry-packages"}}, "0.18.1": {"name": "@esbuild/netbsd-x64", "version": "0.18.1", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.18.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "9c42d1ab35b7cb6f9632dff3a1274d709c29cf72", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.18.1.tgz", "fileCount": 3, "integrity": "sha512-eFL7sxibN8wKuwrRf3HRkcjELALlfl/TavJ8P4J+BJfnkXOITF7zx4tTmGkzK8OwAR9snT2kEfp1Ictc80atGw==", "signatures": [{"sig": "MEQCICNXMt0Y9eGoxaoFuRcu7H/QDrVrq9t1PRMRXAdIuf0IAiAgs2xy/Gx/ogljw5aocF37oDY47UBoAqIhZHWlZ6pYXw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9089730}, "engines": {"node": ">=12"}, "gitHead": "3aa3ec2da489dad64d90aa965c9782984defc904", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.18.1_1686545502260_0.5966735302967114", "host": "s3://npm-registry-packages"}}, "0.18.2": {"name": "@esbuild/netbsd-x64", "version": "0.18.2", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.18.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "e26afa20e65c0c1dd7215caf03368c88881f8eef", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.18.2.tgz", "fileCount": 3, "integrity": "sha512-usNjpKFf83X4o60gdMD47NCblaSZ6DARf31/FyCzxOgnF80mJ+RhDs9RTqgyfH8KyduO5mjgInw9+ct286ayYA==", "signatures": [{"sig": "MEYCIQCe62jpd239xGQ2PfQFkYWTHBI/mE00Xho2nJHFYVWZDQIhAN56hC0k7RL/TR4+FFgdgE8765yqjlwKSpIJ1si3N8dN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9097922}, "engines": {"node": ">=12"}, "gitHead": "a7a909605b1387b5c74d1bd0217af3fb4a843461", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.18.2_1686624027651_0.9473341997723823", "host": "s3://npm-registry-packages"}}, "0.18.3": {"name": "@esbuild/netbsd-x64", "version": "0.18.3", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.18.3", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "878fd523afe75e7352edf6d6f258d7bbf7c5db1a", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.18.3.tgz", "fileCount": 3, "integrity": "sha512-piZ2oBoaq58pKZvhgdV6PemlL30Uhd9GmmOkIGZYgChwNcyVSSl6iMEJxMzU7x44Lk9q+hJ6a343M/iVEMEvxA==", "signatures": [{"sig": "MEUCIQDEsQ/flpCmYRAbIlV4Q/ftKCmSMYwJPMFrK8zodtVIKwIgNUMAGp7MtJMz6G4Tta6bsffzs7pMWdqoDvDg7bg1P20=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9097922}, "engines": {"node": ">=12"}, "gitHead": "9224cce93632b0fc6db8767676211fb44ac642a7", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.18.3_1686831650914_0.5150698319502374", "host": "s3://npm-registry-packages"}}, "0.18.4": {"name": "@esbuild/netbsd-x64", "version": "0.18.4", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.18.4", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "fc511ecd4b46b1b38410f635dd3e0db500a8b325", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.18.4.tgz", "fileCount": 3, "integrity": "sha512-AaShPmN9c6w1mKRpliKFlaWcSkpBT4KOlk93UfFgeI3F3cbjzdDKGsbKnOZozmYbE1izZKLmNJiW0sFM+A5JPA==", "signatures": [{"sig": "MEYCIQCoX1EGKFMFB3ygJpIcZzEbBO9yDhWipJeW3IMCyMGCjgIhAOHMkrsUKdSly+1Tg38hLMMJszdfrNu0A6WqRMsIcH0z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9102018}, "engines": {"node": ">=12"}, "gitHead": "bfc5a0fe07b6f6855ff3ff11a91894066378c5dd", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.18.4_1686929895321_0.4769578627793909", "host": "s3://npm-registry-packages"}}, "0.18.5": {"name": "@esbuild/netbsd-x64", "version": "0.18.5", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.18.5", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "baa039f38c7c3b9f5bfe2687e972597d3f32b048", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.18.5.tgz", "fileCount": 3, "integrity": "sha512-MABnKzjMcXjO0NEYyexOhqjcrgM6dE8BXnm+lctm2x2aPpYg5iL0Ew3aABSTZyp9dS3Z4VzFu5PPoOYEw8akTQ==", "signatures": [{"sig": "MEUCIQDDJc0lxbHHN+6XVl/v6gQ6NduJTHKiUN8Wszx/KaQd3QIgAgAvhoNScAKPP+Eot8FjBgpKfJeCMg17EBJnaILNsU8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9126594}, "engines": {"node": ">=12"}, "gitHead": "931be1b9b2312609c5214812671b0d2a21cfe92b", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.18.5_1687222356033_0.41215794440055764", "host": "s3://npm-registry-packages"}}, "0.18.6": {"name": "@esbuild/netbsd-x64", "version": "0.18.6", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.18.6", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "5b7e69689f5d1047504dedfc32f3bb9d36df5f1f", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.18.6.tgz", "fileCount": 3, "integrity": "sha512-3hgZlp7NqIM5lNG3fpdhBI5rUnPmdahraSmwAi+YX/bp7iZ7mpTv2NkypGs/XngdMtpzljICxnUG3uPfqLFd3w==", "signatures": [{"sig": "MEUCIQDZPRBRgM9atmnKEk7AMpHKtN24Kx82ZmuWDvtAeUlNmQIgfsexUpTlZ+tChTBai6jSnNeotovMu0K0nXvhyt4HjbE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9130690}, "engines": {"node": ">=12"}, "gitHead": "f0b5803694c3d74c1d84851a518e3e25916ec005", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.18.6_1687303490807_0.4349535897037544", "host": "s3://npm-registry-packages"}}, "0.18.7": {"name": "@esbuild/netbsd-x64", "version": "0.18.7", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.18.7", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "deb2956dc3e1b16eb451afb1701fa2b2e458fcd8", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.18.7.tgz", "fileCount": 3, "integrity": "sha512-f82sUnrzdoW4MiiCDn1G3RSsPH8+no+okDhkHgGTGa+5F5wZCyxdXxzR6ctsiimvyPn9FIu9Zs+MesVsBRwyTw==", "signatures": [{"sig": "MEUCIQDjKBPD2akX9qxjFQoolsak92PSfBdmJpBjfmYOdjLXigIgBCUsO35mB41R1r/HEApun/lHC246frS7gn+T9bAnl1I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9175746}, "engines": {"node": ">=12"}, "gitHead": "adb8d19b56d2ae2d65128305c875b577476fac93", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.18.7_1687574783140_0.11587520495370618", "host": "s3://npm-registry-packages"}}, "0.18.8": {"name": "@esbuild/netbsd-x64", "version": "0.18.8", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.18.8", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "94da2b20da94af77ee3287502b69a1a75c8c3392", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.18.8.tgz", "fileCount": 3, "integrity": "sha512-/Aqh6SoP6UpLrgdfgFzi1Von4D5OhnJEYZNdkZA0AREuwSBcZh6X5eUsSCiEszJaeOt/oOZOvSwNR7i2VjmDnA==", "signatures": [{"sig": "MEYCIQCUPZE8eRLihTVJNRW9W8z2baoMAtLa+9n4+2JutPJ7zwIhAJQw/WkYQLiJZuG4J1iO1xgs/WO/blzz+4GMoEMGJfXb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9175746}, "engines": {"node": ">=12"}, "gitHead": "9b233a4f670a73173dcc7e83ebf7648d0007b082", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.18.8_1687663150579_0.5778921414108473", "host": "s3://npm-registry-packages"}}, "0.18.9": {"name": "@esbuild/netbsd-x64", "version": "0.18.9", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.18.9", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "0e67492dd5197bc6227234563465a66789ac5dde", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.18.9.tgz", "fileCount": 3, "integrity": "sha512-j/GgOjKNUPd54isC/RBYlS6CREbulnMWAJEIKTnPM0QnY0pEGfMHkFh73bsmZdovp/97zRty0NdePRk4dTP/cw==", "signatures": [{"sig": "MEUCIQC239IGus9IPVnhU3RapOaPHRn3DpNITVo2yphZ74AjsAIgWfWUINfOcgYh7fw1eAia7QvRqL+ru9a+VdQGQqOE6FU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9192130}, "engines": {"node": ">=12"}, "gitHead": "d568ff038d012a7894a9d4334b75f8559bf2532e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.18.9_1687757270716_0.37030403094305764", "host": "s3://npm-registry-packages"}}, "0.18.10": {"name": "@esbuild/netbsd-x64", "version": "0.18.10", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.18.10", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "436c8c8590eb7638e9d811ce5aae5e44b7dd2e45", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.18.10.tgz", "fileCount": 3, "integrity": "sha512-pQ9QqxEPI3cVRZyUtCoZxhZK3If+7RzR8L2yz2+TDzdygofIPOJFaAPkEJ5rYIbUO101RaiYxfdOBahYexLk5A==", "signatures": [{"sig": "MEQCIAR/lvfIE6jI8EYD6C906Ozr+PuA5LQVC6IZ8zehcUE0AiAJ69iyl250l/8mR3YzDeLDDtdv7hCQSoWaBQJ8ybE8zg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9192131}, "engines": {"node": ">=12"}, "gitHead": "cdb6c7ce3f9419e51bd855fd61d07d2c615fb30e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.18.10_1687814425585_0.14655825181402848", "host": "s3://npm-registry-packages"}}, "0.18.11": {"name": "@esbuild/netbsd-x64", "version": "0.18.11", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.18.11", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "a7b2f991b8293748a7be42eac1c4325faf0c7cca", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.18.11.tgz", "fileCount": 3, "integrity": "sha512-aSjMHj/F7BuS1CptSXNg6S3M4F3bLp5wfFPIJM+Km2NfIVfFKhdmfHF9frhiCLIGVzDziggqWll0B+9AUbud/Q==", "signatures": [{"sig": "MEQCIEm0mg8o2roquHwu0SC+VJ4Rwu2djcQ09hXz08qhnLv1AiAHq6/UlscM5BR7ZJCrw1h5HOIOheugdJwTHjtuYy8wWw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9192131}, "engines": {"node": ">=12"}, "gitHead": "2703f90d47fd96f425fedcfd2a5c318a43b04d45", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.18.11_1688191430371_0.8063593821727948", "host": "s3://npm-registry-packages"}}, "0.18.12": {"name": "@esbuild/netbsd-x64", "version": "0.18.12", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.18.12", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "8e027526e556c3e909b55bb3b1839013ff9d9786", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.18.12.tgz", "fileCount": 3, "integrity": "sha512-55FzVCAiwE9FK8wWeCRuvjazNRJ1QqLCYGZVB6E8RuQuTeStSwotpSW4xoRGwp3a1wUsaVCdYcj5LGCASVJmMg==", "signatures": [{"sig": "MEQCIB6njirCzYE8rWfmtV+Dl+Dj1p50lG8jVzy+a/7kr5TyAiBUi15+OEKrQzgS54Msa45i+35gCqcBtL/YZslM3woSyg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9196227}, "engines": {"node": ">=12"}, "gitHead": "d196e4c4898fc46dc553124e28b1b29829ef7f7d", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.18.12_1689212042989_0.1864229820240233", "host": "s3://npm-registry-packages"}}, "0.18.13": {"name": "@esbuild/netbsd-x64", "version": "0.18.13", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.18.13", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "67954292195ecbdae33ab09a9ae6a7f566e49d04", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.18.13.tgz", "fileCount": 3, "integrity": "sha512-I6zs10TZeaHDYoGxENuksxE1sxqZpCp+agYeW039yqFwh3MgVvdmXL5NMveImOC6AtpLvE4xG5ujVic4NWFIDQ==", "signatures": [{"sig": "MEYCIQCKhjncl/ZgoEJA1KrgRFwH8xVzhB3kXEO6X35P3PLzsAIhAJvMlqqu0F1xSjVtHhZn1DdaUVvTT5VACHiHb8NMxyUH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9196227}, "engines": {"node": ">=12"}, "gitHead": "12a8a25b4ca8b650d7c96046b8a3e76491a119f5", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.18.13_1689388632258_0.9335951582165434", "host": "s3://npm-registry-packages"}}, "0.18.14": {"name": "@esbuild/netbsd-x64", "version": "0.18.14", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.18.14", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "4677bf88b489d5ffe1b5f0abbb85812996455479", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.18.14.tgz", "fileCount": 3, "integrity": "sha512-stvCcjyCQR2lMTroqNhAbvROqRjxPEq0oQ380YdXxA81TaRJEucH/PzJ/qsEtsHgXlWFW6Ryr/X15vxQiyRXVg==", "signatures": [{"sig": "MEQCIFKonwte7sogkd4nhwV4Ki7CBz08G4jvYqID2xHpbVYOAiBYXJG2iXRNNqFfXlq7x6QuhhhNov382UCUwoVLMoSA8Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9233091}, "engines": {"node": ">=12"}, "gitHead": "af0fe32eaea8112de45e17cb8d0ad487b2123132", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.18.14_1689656415550_0.17395188022334862", "host": "s3://npm-registry-packages"}}, "0.18.15": {"name": "@esbuild/netbsd-x64", "version": "0.18.15", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.18.15", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "08b5ccaf027c7e2174b9a19c29bebfe59dce1cfb", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.18.15.tgz", "fileCount": 3, "integrity": "sha512-djST6s+jQiwxMIVQ5rlt24JFIAr4uwUnzceuFL7BQT4CbrRtqBPueS4GjXSiIpmwVri1Icj/9pFRJ7/aScvT+A==", "signatures": [{"sig": "MEYCIQCpNgOZtZOLn7p1qqaZJI7jJvdudMn9UTgPd/bS+RwU9wIhAP7vZV7I9bwnfXE+d/9WBgt9aaZfFG6WI+iOOv5+PvH4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9241283}, "engines": {"node": ">=12"}, "gitHead": "daf64732be2e05d1258023b5b7d5389e08e291a9", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.18.15_1689857593697_0.7373592020447084", "host": "s3://npm-registry-packages"}}, "0.18.16": {"name": "@esbuild/netbsd-x64", "version": "0.18.16", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.18.16", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "6514a86bd07744f3100d2813ea2fb6520d53e72e", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.18.16.tgz", "fileCount": 3, "integrity": "sha512-uVAgpimx9Ffw3xowtg/7qQPwHFx94yCje+DoBx+LNm2ePDpQXHrzE+Sb0Si2VBObYz+LcRps15cq+95YM7gkUw==", "signatures": [{"sig": "MEYCIQDnw5lyuE6Tsf1k6/o4/Xo+Atza6LEWv/skX3WwymxlNQIhAN1f/TZTSqxaa1kriqFhZv6/NUBSAJGEbyc9PGZbhcO8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9245379}, "engines": {"node": ">=12"}, "gitHead": "22920366954b4d18aed77dfc2b5961f339d4e318", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.18.16_1690087681260_0.6536830514372651", "host": "s3://npm-registry-packages"}}, "0.18.17": {"name": "@esbuild/netbsd-x64", "version": "0.18.17", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.18.17", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "6bb89668c0e093c5a575ded08e1d308bd7fd63e7", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.18.17.tgz", "fileCount": 3, "integrity": "sha512-/jGlhWR7Sj9JPZHzXyyMZ1RFMkNPjC6QIAan0sDOtIo2TYk3tZn5UDrkE0XgsTQCxWTTOcMPf9p6Rh2hXtl5TQ==", "signatures": [{"sig": "MEUCIQCNBf+w5ZkzhZi3hOllaOlITcprqQCbwkXIMzrOUEF3AwIgZG8iZC35+A9K5ym8Bg3aCJilwriXwsqkNos3vreBlbI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9265859}, "engines": {"node": ">=12"}, "gitHead": "1771c7109f7f5d17d96543d6b17a6ab12d9d38d0", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.18.17_1690335650498_0.7180843228358997", "host": "s3://npm-registry-packages"}}, "0.18.18": {"name": "@esbuild/netbsd-x64", "version": "0.18.18", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.18.18", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "99b6125868c5ba8f0131bacc3f2bd05918245f45", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.18.18.tgz", "fileCount": 3, "integrity": "sha512-8Otf05Vx5sZjLLDulgr5QS5lsWXMplKZEyHMArH9/S4olLlhzmdhQBPhzhJTNwaL2FJNdWcUPNGAcoD5zDTfUA==", "signatures": [{"sig": "MEYCIQCuCkx4UiJlFPxq8nGBs525y3ldzTiPVfI+OzQssRxf+gIhAPh4cvuF9/6wyza/08zli+1j3CtHNFAdZPr9lKBKHflG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9282243}, "engines": {"node": ">=12"}, "gitHead": "e8e43ad19359f0b29d84607c89c6aa95a4d1637d", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.18.18_1691255185791_0.8874358436492604", "host": "s3://npm-registry-packages"}}, "0.18.19": {"name": "@esbuild/netbsd-x64", "version": "0.18.19", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.18.19", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "8fd667c535db0a5b346afa2d74ff1fb53477427f", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.18.19.tgz", "fileCount": 3, "integrity": "sha512-3tt3SOS8L3D54R8oER41UdDshlBIAjYhdWRPiZCTZ1E41+shIZBpTjaW5UaN/jD1ENE/Ok5lkeqhoNMbxstyxw==", "signatures": [{"sig": "MEUCIBxuoxIkWt42oQNCWhdQzKYxS2T+PXGbfXyaOH59yc/CAiEAt+y5+7kob9oBbRKbfgCje31Gb/tvG+kTuIMV2EHFDoY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9315011}, "engines": {"node": ">=12"}, "gitHead": "e08ee8990905f24b987a7ddffde89e20cbf3cf6a", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.18.19_1691376674756_0.16153835524909388", "host": "s3://npm-registry-packages"}}, "0.18.20": {"name": "@esbuild/netbsd-x64", "version": "0.18.20", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.18.20", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "30e8cd8a3dded63975e2df2438ca109601ebe0d1", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.18.20.tgz", "fileCount": 3, "integrity": "sha512-iO1c++VP6xUBUmltHZoMtCUdPlnPGdBom6IrO4gyKPFFVBKioIImVooR5I83nTew5UOYrk3gIJhbZh8X44y06A==", "signatures": [{"sig": "MEUCIEQzwbLTunHelHsr18JJzDTMH+H5z0laB+2AJkAG1RipAiEAg8ZAE7Zk/2Qd7oyGOnTO7df0mTFkRJkQAj4vWIR+vhI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9327299}, "engines": {"node": ">=12"}, "gitHead": "22f0818cf81024b63752d815c51fe737612b43ec", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.18.20_1691468093823_0.944293512766677", "host": "s3://npm-registry-packages"}}, "0.19.0": {"name": "@esbuild/netbsd-x64", "version": "0.19.0", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.19.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "2e6e8d869b58aea34bab9c0c47f15ae1bda29a90", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.19.0.tgz", "fileCount": 3, "integrity": "sha512-AMQAp/5oENgDOvVhvOlbhVe1pWii7oFAMRHlmTjSEMcpjTpIHtFXhv9uAFgUERHm3eYtNvS9Vf+gT55cwuI6Aw==", "signatures": [{"sig": "MEUCIQCcQT/pXUhdpjp1dvnnCjbXqTbYsaEpvHRT8TujzARlMQIgPZywvcUwiYF+ub82mAUZdsa2rqoT2+Oc+ggrU7CmSaM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9380546}, "engines": {"node": ">=12"}, "gitHead": "c337498cdad8cac87517ec49c923441b2dc67bf2", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.19.0_1691509927072_0.32437449522057404", "host": "s3://npm-registry-packages"}}, "0.19.1": {"name": "@esbuild/netbsd-x64", "version": "0.19.1", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.19.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "c1f948931b14b96cf958c783c7dbec0409782a0c", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.19.1.tgz", "fileCount": 3, "integrity": "sha512-4OrGMPorHCq9h52VLtyyyAmPjC2ZlANx54VDYyCrqXUOi+k0qxnPKXKKprVES67w2mE7TZJx9qZmT+jHeiZbHQ==", "signatures": [{"sig": "MEUCIBo0JR4kpM7fCK2Xw+vTg8g/HYn2i8d/O54tzfsjdCfwAiEAqEXhMaSb+xyYY3N68EKOb4hB94vWRGeu1kYGcukkLOQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9392834}, "engines": {"node": ">=12"}, "gitHead": "49801f761347d53bd1f6a88767bb79e257f9fbb9", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.19.1_1691769446712_0.17515568521793523", "host": "s3://npm-registry-packages"}}, "0.19.2": {"name": "@esbuild/netbsd-x64", "version": "0.19.2", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.19.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "bbfd7cf9ab236a23ee3a41b26f0628c57623d92a", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.19.2.tgz", "fileCount": 3, "integrity": "sha512-WNa5zZk1XpTTwMDompZmvQLHszDDDN7lYjEHCUmAGB83Bgs20EMs7ICD+oKeT6xt4phV4NDdSi/8OfjPbSbZfQ==", "signatures": [{"sig": "MEYCIQCzyTK4KpoKD02Vt253/h1PfkUZ+yBIWrbti1gyl8rE0QIhAL3PqBzuJHz0IU+0HDj1AnbCQ0FYa2Gx8dEg6WsFsT7N", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9388738}, "engines": {"node": ">=12"}, "gitHead": "09a100124e9daef9e0be57d21cc7729c6f3516e7", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.19.2_1691978296565_0.6920104498681028", "host": "s3://npm-registry-packages"}}, "0.19.3": {"name": "@esbuild/netbsd-x64", "version": "0.19.3", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.19.3", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "e9b046934996991f46b8c1cadac815aa45f84fd4", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.19.3.tgz", "fileCount": 3, "integrity": "sha512-o1jLNe4uzQv2DKXMlmEzf66Wd8MoIhLNO2nlQBHLtWyh2MitDG7sMpfCO3NTcoTMuqHjfufgUQDFRI5C+xsXQw==", "signatures": [{"sig": "MEUCIQD/Xk94hwf/Z8S3zLZXghv3msE7ftGwNYyFtcnmRiDAMAIgAc1P3CvJCfuiRVfr6/qLkGbB84cytzCmSgTWw4m9PV0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9401026}, "engines": {"node": ">=12"}, "gitHead": "673ad10ff752486aa90749b63ebeb952c29106a1", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.19.3_1694653938903_0.9506509237796681", "host": "s3://npm-registry-packages"}}, "0.19.4": {"name": "@esbuild/netbsd-x64", "version": "0.19.4", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.19.4", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "7889744ca4d60f1538d62382b95e90a49687cef2", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.19.4.tgz", "fileCount": 3, "integrity": "sha512-9+Wxx1i5N/CYo505CTT7T+ix4lVzEdz0uCoYGxM5JDVlP2YdDC1Bdz+Khv6IbqmisT0Si928eAxbmGkcbiuM/A==", "signatures": [{"sig": "MEYCIQDtXKEXT2Ez8WXbTYTY0+QhGr8SLK6w7aSGXKw83GUvkQIhAPgWtEbLZmynmyAY5ds+ubnFCPyJ/ADUjMA0yfo7DoHM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9401026}, "engines": {"node": ">=12"}, "gitHead": "a75b16ec09e76a050cea8ad43588172dc297784d", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.19.4_1695865606600_0.7868434077860149", "host": "s3://npm-registry-packages"}}, "0.19.5": {"name": "@esbuild/netbsd-x64", "version": "0.19.5", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.19.5", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "3b5c1fb068f26bfc681d31f682adf1bea4ef0702", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.19.5.tgz", "fileCount": 3, "integrity": "sha512-kL2l+xScnAy/E/3119OggX8SrWyBEcqAh8aOY1gr4gPvw76la2GlD4Ymf832UCVbmuWeTf2adkZDK+h0Z/fB4g==", "signatures": [{"sig": "MEUCIQDmSl4RIfWYThvWNC1tyfBSN5bG7IMdFTIYdfDtA8DNVQIgefAEkzt9kupzxpZPhP4pxiRdQQphCuEIEu6z1q9aEU4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9409218}, "engines": {"node": ">=12"}, "gitHead": "a7fcc43fdb6b6edc58f781fe96328f4867f4b33e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.19.5_1697519429320_0.33120531580533785", "host": "s3://npm-registry-packages"}}, "0.19.6": {"name": "@esbuild/netbsd-x64", "version": "0.19.6", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.19.6", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "6108d7270599ee37cd57bb14e4516a83541885d5", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.19.6.tgz", "fileCount": 3, "integrity": "sha512-EpWiLX0fzvZn1wxtLxZrEW+oQED9Pwpnh+w4Ffv8ZLuMhUoqR9q9rL4+qHW8F4Mg5oQEKxAoT0G+8JYNqCiR6g==", "signatures": [{"sig": "MEYCIQDIVnKE2XzA980NQLjv246qXSbzV4v7NiCXf65o4bl5JgIhAIDJWJkoPifsb+PLlcdramSHwPspDexlYfItq39LpBFT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9429698}, "engines": {"node": ">=12"}, "gitHead": "6073a3a9a02909d54cedbaf9c06f5fa501f9f337", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.19.6_1700377890173_0.5554829998810253", "host": "s3://npm-registry-packages"}}, "0.19.7": {"name": "@esbuild/netbsd-x64", "version": "0.19.7", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.19.7", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "98898ba8800374c9df9bb182ca4f69fcecaf4411", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.19.7.tgz", "fileCount": 3, "integrity": "sha512-/yfjlsYmT1O3cum3J6cmGG16Fd5tqKMcg5D+sBYLaOQExheAJhqr8xOAEIuLo8JYkevmjM5zFD9rVs3VBcsjtQ==", "signatures": [{"sig": "MEUCIEAbJiMoasl2uVJ/xJsh+rtb+PlUuhAW7ve05mZSOA+RAiEA4ZSSN9yqUo/8lzHp66epYC6aw01jzeTTVJGeqgjaqUY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9466562}, "engines": {"node": ">=12"}, "gitHead": "a7773b340bb216d053df91b7479b5aa2a152b0de", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.19.7_1700528451607_0.9979461386175155", "host": "s3://npm-registry-packages"}}, "0.19.8": {"name": "@esbuild/netbsd-x64", "version": "0.19.8", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.19.8", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "c1ec0e24ea82313cb1c7bae176bd5acd5bde7137", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.19.8.tgz", "fileCount": 3, "integrity": "sha512-hvWVo2VsXz/8NVt1UhLzxwAfo5sioj92uo0bCfLibB0xlOmimU/DeAEsQILlBQvkhrGjamP0/el5HU76HAitGw==", "signatures": [{"sig": "MEUCIQDuN0KusE0gA1NKMokVNyLXo85R+KGePGXU6PJRLcnWDwIgKaa4TgIEl5LEriYbxCjNcB4hstzyNwQP5U0z8w33Des=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9466562}, "engines": {"node": ">=12"}, "gitHead": "e97bd6706c7aaddb3770ae31b164d7ccaec8056c", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.19.8_1701040072306_0.2993474746123934", "host": "s3://npm-registry-packages"}}, "0.19.9": {"name": "@esbuild/netbsd-x64", "version": "0.19.9", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.19.9", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "0cbca65e9ef4d3fc41502d3e055e6f49479a8f18", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.19.9.tgz", "fileCount": 3, "integrity": "sha512-GThgZPAwOBOsheA2RUlW5UeroRfESwMq/guy8uEe3wJlAOjpOXuSevLRd70NZ37ZrpO6RHGHgEHvPg1h3S1Jug==", "signatures": [{"sig": "MEUCICk+XzCY7aUTkFUapHAsU5m2ENhY/dZdmO5iPUZP+NrNAiEAsbVBJj3PDrMf3ghbzseYpgE6o2DXQAmsq9FbL/G5kik=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9577154}, "engines": {"node": ">=12"}, "gitHead": "9edc9d44c3d0480c27f68a71365f18e688b6184a", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.19.9_1702184961861_0.5715278146664391", "host": "s3://npm-registry-packages"}}, "0.19.10": {"name": "@esbuild/netbsd-x64", "version": "0.19.10", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.19.10", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "fd473a5ae261b43eab6dad4dbd5a3155906e6c91", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.19.10.tgz", "fileCount": 3, "integrity": "sha512-ACbZ0vXy9zksNArWlk2c38NdKg25+L9pr/mVaj9SUq6lHZu/35nx2xnQVRGLrC1KKQqJKRIB0q8GspiHI3J80Q==", "signatures": [{"sig": "MEQCIFK7+a80/gG40MEo8z3sM46fJAZEw5i6PFjDmYQ2Hps5AiAzf/IPRYpeK2MmJKQM7LzP2aohkHV/sn3MSFRLuBVXYQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9585347}, "engines": {"node": ">=12"}, "gitHead": "55e1127a49db0c26f1abd97f1b180bbc728aa95a", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.19.10_1702945285528_0.9460408024852034", "host": "s3://npm-registry-packages"}}, "0.19.11": {"name": "@esbuild/netbsd-x64", "version": "0.19.11", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.19.11", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "d633c09492a1721377f3bccedb2d821b911e813d", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.19.11.tgz", "fileCount": 3, "integrity": "sha512-13jvrQZJc3P230OhU8xgwUnDeuC/9egsjTkXN49b3GcS5BKvJqZn86aGM8W9pd14Kd+u7HuFBMVtrNGhh6fHEQ==", "signatures": [{"sig": "MEUCIQC72xscz12roKwtKZomkUT4shdZjR1pMlxSLkHsPm9KVQIgLetmkve+ZFo5iQWaKhZin1ZyJAYD6MDeXGjUmXM81ZA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9585347}, "engines": {"node": ">=12"}, "gitHead": "6ee82255bdfdffef2de60827e9d35a425a7cbff6", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.19.11_1703881906722_0.4495456297745253", "host": "s3://npm-registry-packages"}}, "0.19.12": {"name": "@esbuild/netbsd-x64", "version": "0.19.12", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.19.12", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "29da566a75324e0d0dd7e47519ba2f7ef168657b", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.19.12.tgz", "fileCount": 3, "integrity": "sha512-3ltjQ7n1owJgFbuC61Oj++XhtzmymoCihNFgT84UAmJnxJfm4sYCiSLTXZtE00VWYpPMYc+ZQmB6xbSdVh0JWA==", "signatures": [{"sig": "MEYCIQD0vCB3jJ3jpqjzAoQDPvbTMp6dbOHPbknPP62xTeIxGwIhALro1CtKBJNvTcEmwND9eFfqhUhHFbawPWVChBfaT6/R", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9585347}, "engines": {"node": ">=12"}, "gitHead": "d7fd1ad35715cda76eb33343b7c07b275e402a2e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.19.12_1706031616061_0.04928216683860831", "host": "s3://npm-registry-packages"}}, "0.20.0": {"name": "@esbuild/netbsd-x64", "version": "0.20.0", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.20.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "6cc778567f1513da6e08060e0aeb41f82eb0f53c", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.20.0.tgz", "fileCount": 3, "integrity": "sha512-lCT675rTN1v8Fo+RGrE5KjSnfY0x9Og4RN7t7lVrN3vMSjy34/+3na0q7RIfWDAj0e0rCh0OL+P88lu3Rt21MQ==", "signatures": [{"sig": "MEQCIAWpuNKBxYGtz8+jqPPECDKs3sQjPq+hgUGgV81HBFUeAiBS42G/c6QfVucPXlETq1VIiH+meE80J0dqfL0bAvlWaQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9585390}, "engines": {"node": ">=12"}, "gitHead": "2af5ccf478812d2d7226ad4435d46fbbb3419a8c", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.20.0_1706374166965_0.2338099801606499", "host": "s3://npm-registry-packages"}}, "0.20.1": {"name": "@esbuild/netbsd-x64", "version": "0.20.1", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.20.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "3148e48406cd0d4f7ba1e0bf3f4d77d548c98407", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.20.1.tgz", "fileCount": 3, "integrity": "sha512-4fL68JdrLV2nVW2AaWZBv3XEm3Ae3NZn/7qy2KGAt3dexAgSVT+Hc97JKSZnqezgMlv9x6KV0ZkZY7UO5cNLCg==", "signatures": [{"sig": "MEUCIBUxDD0rDej/WVzhhGVfU0KO80hzzruPQ6K7pw8sTll/AiEA05jfOG2XEaCoswUZurtRFazIENHMy33L9xoa/32KSw4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9597678}, "engines": {"node": ">=12"}, "gitHead": "9f9e4f85e6e28a58727531458663afd157b8b415", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.20.1_1708324677633_0.841378189292227", "host": "s3://npm-registry-packages"}}, "0.20.2": {"name": "@esbuild/netbsd-x64", "version": "0.20.2", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.20.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "e771c8eb0e0f6e1877ffd4220036b98aed5915e6", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.20.2.tgz", "fileCount": 3, "integrity": "sha512-K8/DhBxcVQkzYc43yJXDSyjlFeHQJBiowJ0uVL6Tor3jGQfSGHNNJcWxNbOI8v5k82prYqzPuwkzHt3J1T1iZQ==", "signatures": [{"sig": "MEQCIA7WAgyI0O5cF+r81rflJW5Y91jWHJuoSH8Hqo2Zg/1FAiAZOsCAxjLBSbNP3rzjcLKz5Vm1N8tsT3VKPRdMtafFTQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9601774}, "engines": {"node": ">=12"}, "gitHead": "617eddaa32b7649ad23ddd15257816df3f0f544c", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.20.2_1710445778444_0.18456083166658654", "host": "s3://npm-registry-packages"}}, "0.21.0": {"name": "@esbuild/netbsd-x64", "version": "0.21.0", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.21.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "6bcab3069361aa3f4d2b2179db46c4bd46012d3f", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.21.0.tgz", "fileCount": 3, "integrity": "sha512-AwkJoff9D5Px7+lHafSSgDK3JreyeyPtwTsOfxhlk5NZ+bMGlvSfHkA6DKv9vD0gmGrBPTMv/uIePkNaVsDq7w==", "signatures": [{"sig": "MEQCIBH4NhVya3B7HCDWZoHZi/UuvOjCdyXsd14LkgnahJHmAiBHiY1CJmLCPrTd5tgW12ihKnAoid/cd/4YDQjJW6i0CA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9683694}, "engines": {"node": ">=12"}, "gitHead": "c6da2c3aa2b1321be3fdacd1e53566c5f24ee702", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.21.0_1715050341474_0.21621779134139807", "host": "s3://npm-registry-packages"}}, "0.21.1": {"name": "@esbuild/netbsd-x64", "version": "0.21.1", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.21.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "e298f854e8999563f2e4668bd542678c46be4b53", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.21.1.tgz", "fileCount": 3, "integrity": "sha512-CPWs0HTFe5woTJN5eKPvgraUoRHrCtzlYIAv9wBC+FAyagBSaf+UdZrjwYyTGnwPGkThV4OCI7XibZOnPvONVw==", "signatures": [{"sig": "MEUCICqeMzZdo1BuRKLPSzW5jZRGaO2bZc/6jfwdmos4rLXtAiEA8qsxotoigs8WEVmT61HhYXPjE46s11fuXBTuBygVskQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9679598}, "engines": {"node": ">=12"}, "gitHead": "e87639417e47ba5db160f105785dc10bde0999cf", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.21.1_1715100893924_0.21360474390866524", "host": "s3://npm-registry-packages"}}, "0.21.2": {"name": "@esbuild/netbsd-x64", "version": "0.21.2", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.21.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "e1dde3694f5f8fbf2f7696d021c026e601579167", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.21.2.tgz", "fileCount": 3, "integrity": "sha512-IeFMfGFSQfIj1d4XU+6lkbFzMR+mFELUUVYrZ+jvWzG4NGvs6o53ReEHLHpYkjRbdEjJy2W3lTekTxrFHW7YJg==", "signatures": [{"sig": "MEUCIBDO2DCkFU2gnNwY0TRpaJo29mMEoxSf237LkQp3A+d0AiEA2J1NpVlpNeFB/w71UbqdonjMGreMj0h6Xu3gESoYpsk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9683694}, "engines": {"node": ">=12"}, "gitHead": "b24180e4fbd07504e91cb922948870d5467072e0", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.21.2_1715545971540_0.31996827236543623", "host": "s3://npm-registry-packages"}}, "0.21.3": {"name": "@esbuild/netbsd-x64", "version": "0.21.3", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.21.3", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "309d8c323632e9c70ee92cf5414fa65b5eb7e00e", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.21.3.tgz", "fileCount": 3, "integrity": "sha512-uTgCwsvQ5+vCQnqM//EfDSuomo2LhdWhFPS8VL8xKf+PKTCrcT/2kPPoWMTs22aB63MLdGMJiE3f1PHvCDmUOw==", "signatures": [{"sig": "MEUCIBImzS8pRTeX60woFhWyrK98tSTpVSWinbiPJyEa3HYZAiEAvzi/BG/30bGUGMqPZ/aqtjod5Mbr1BE3AbRMFHUIoTI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9679598}, "engines": {"node": ">=12"}, "gitHead": "efa3dd2d8e895f7f9a9bef0d588560bbae7d776e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.21.3_1715806345280_0.40751460014517527", "host": "s3://npm-registry-packages"}}, "0.21.4": {"name": "@esbuild/netbsd-x64", "version": "0.21.4", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.21.4", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "89b97d823e1cc4bf8c4e5dc8f76c8d6ceb1c87f3", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.21.4.tgz", "fileCount": 3, "integrity": "sha512-Awn38oSXxsPMQxaV0Ipb7W/gxZtk5Tx3+W+rAPdZkyEhQ6968r9NvtkjhnhbEgWXYbgV+JEONJ6PcdBS+nlcpA==", "signatures": [{"sig": "MEUCIGEv2vctjcIjzsbGzadxIQUIF9Gi//NNTrR3DUrSVJ/6AiEA+STk3rjPXI1igxHRrgMNSEBJfqeKbM3AD0We/8dY8Js=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9683694}, "engines": {"node": ">=12"}, "gitHead": "67cbf87a4909d87a902ca8c3b69ab5330defab0a", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.21.4_1716603046991_0.7853802759742023", "host": "s3://npm-registry-packages"}}, "0.21.5": {"name": "@esbuild/netbsd-x64", "version": "0.21.5", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.21.5", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "bbe430f60d378ecb88decb219c602667387a6047", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.21.5.tgz", "fileCount": 3, "integrity": "sha512-Woi2MXzXjMULccIwMnLciyZH4nCIMpWQAs049KEeMvOcNADVxo0UBIQPfSmxB3CWKedngg7sWZdLvLczpe0tLg==", "signatures": [{"sig": "MEQCIBeXMaTmfjnFYqVhQy5rBfCOhCniHQEp1m3D1uss591HAiAprVC+xdGlO6GljuThPGrlr3zdd1OKRnqkGBKGYhichA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9691886}, "engines": {"node": ">=12"}, "gitHead": "fc37c2fa9de2ad77476a6d4a8f1516196b90187e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.21.5_1717967812340_0.7746355149291662", "host": "s3://npm-registry-packages"}}, "0.22.0": {"name": "@esbuild/netbsd-x64", "version": "0.22.0", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.22.0", "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "bc3f51c41eaab89cf5fdb09d0c633affb39cb1a1", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.22.0.tgz", "fileCount": 3, "integrity": "sha512-11PoCoHXo4HFNbLsXuMB6bpMPWGDiw7xETji6COdJss4SQZLvcgNoeSqWtATRm10Jj1uEHiaIk4N0PiN6x4Fcg==", "signatures": [{"sig": "MEUCIQC8xRZNxT6TqS0ESehsMfiWe9uQW16yXNS2ouzK4HK9rgIgEBpErHWaByXf04lCMHZFf8Yk6jExwEfoSxR1CZyd36Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9946009}, "engines": {"node": ">=18"}, "gitHead": "80c6e6ea094a71691ab1644ab61494cc67729365", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.22.0_1719779863648_0.0887883490673107", "host": "s3://npm-registry-packages"}}, "0.23.0": {"name": "@esbuild/netbsd-x64", "version": "0.23.0", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.23.0", "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "ba70db0114380d5f6cfb9003f1d378ce989cd65c", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.23.0.tgz", "fileCount": 3, "integrity": "sha512-cRK+YDem7lFTs2Q5nEv/HHc4LnrfBCbH5+JHu6wm2eP+d8OZNoSMYgPZJq78vqQ9g+9+nMuIsAO7skzphRXHyw==", "signatures": [{"sig": "MEUCIQCls1sDysbtncwieSNot7uWWUFwH9Ne0xfneibgVYviuQIgdJO5sEc5NzZzgtpnMv63XQ+iR0FEfEvr2L1WFZJAidE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9946009}, "engines": {"node": ">=18"}, "gitHead": "9d506806bdd963b02b3d6edf45e717e03dcba785", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.23.0_1719891220058_0.8629629406857644", "host": "s3://npm-registry-packages"}}, "0.23.1": {"name": "@esbuild/netbsd-x64", "version": "0.23.1", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.23.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "44e743c9778d57a8ace4b72f3c6b839a3b74a653", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.23.1.tgz", "fileCount": 3, "integrity": "sha512-aevEkCNu7KlPRpYLjwmdcuNz6bDFiE7Z8XC4CPqExjTvrHugh28QzUXVOZtiYghciKUacNktqxdpymplil1beA==", "signatures": [{"sig": "MEUCIQCtK9yY0uR830Di4AufvG3h8uu5NFHrHSXyIPi9qMseeAIgRBqZeMgRoQpqq3PAycjXInVb4+3bLwl8OwcLRPhYGF0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9950105}, "engines": {"node": ">=18"}, "gitHead": "332727499e62315cff4ecaff9fa8b86336555e46", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.23.1_1723846390807_0.48369933200235193", "host": "s3://npm-registry-packages"}}, "0.24.0": {"name": "@esbuild/netbsd-x64", "version": "0.24.0", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.24.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "1ae73d23cc044a0ebd4f198334416fb26c31366c", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.24.0.tgz", "fileCount": 3, "integrity": "sha512-hjQ0R/ulkO8fCYFsG0FZoH+pWgTTDreqpqY7UnQntnaKv95uP5iW3+dChxnx7C3trQQU40S+OgWhUVwCjVFLvg==", "signatures": [{"sig": "MEUCIEuO/fcMkaq61YReON5sQPGaJIxjfzQAFsgvhjoLDQraAiEAih5pzy0IDfo7DTiUEjzxOPk8k5BQtiURx6+uuz6ibuU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10138521}, "engines": {"node": ">=18"}, "gitHead": "d34e79e2a998c21bb71d57b92b0017ca11756912", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.24.0_1726970779180_0.05023159652120657", "host": "s3://npm-registry-packages"}}, "0.24.1": {"name": "@esbuild/netbsd-x64", "version": "0.24.1", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.24.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "b76ca03f77bdf9716216f486b412acf6a16af555", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.24.1.tgz", "fileCount": 3, "integrity": "sha512-OdjqCVKtJuxRk9gPit/iI4/wSCGOnCcuPkgkT8Pt+0vM63QUOBd5oNX2umXUBr4bYTpSCL/aGxrAb3qENcqA4g==", "signatures": [{"sig": "MEUCIEo9O+M0oP8Uy7uQUh3AItSP7MhrG6VJ1lHIY3JdieTDAiEA3RZxJKqHibM7OxqoMvt/g7TqlXCLDU6bfJ9PnaVSm88=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10146713}, "engines": {"node": ">=18"}, "gitHead": "de9598f42dc3ffc395e3fd3672a4804f6b4e5c09", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.24.1_1734673256215_0.5704421373306092", "host": "s3://npm-registry-packages-npm-production"}}, "0.24.2": {"name": "@esbuild/netbsd-x64", "version": "0.24.2", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.24.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "7a3a97d77abfd11765a72f1c6f9b18f5396bcc40", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.24.2.tgz", "fileCount": 3, "integrity": "sha512-VefFaQUc4FMmJuAxmIHgUmfNiLXY438XrL4GDNV1Y1H/RW3qow68xTwjZKfj/+Plp9NANmzbH5R40Meudu8mmw==", "signatures": [{"sig": "MEYCIQDntnRUu1XA4hiAeuvltTFBJxuAO5BDEhuDjGvsRstiLQIhAISRpva5BFiv46m7VZVmuewprQKJdQQDCMD/tKN3rYPf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10146713}, "engines": {"node": ">=18"}, "gitHead": "745abd9f0c06f73ca40fbe198546a9bc36c23b81", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.24.2_1734717372046_0.050444013700436496", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.0": {"name": "@esbuild/netbsd-x64", "version": "0.25.0", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.25.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "414677cef66d16c5a4d210751eb2881bb9c1b62b", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.25.0.tgz", "fileCount": 3, "integrity": "sha512-jl+qisSB5jk01N5f7sPCsBENCOlPiS/xptD5yxOx2oqQfyourJwIKLRA2yqWdifj3owQZCL2sn6o08dBzZGQzA==", "signatures": [{"sig": "MEQCIFDw7ieiwWKsg0A7E8+7Ajj412D1WHmdXesN2l7XxPr3AiBhvKFkfAo2iY6NCvlMUoWHRn9Mu2d7tVn8nj7/bUPXyg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10199961}, "engines": {"node": ">=18"}, "gitHead": "e9174d671b1882758cd32ac5e146200f5bee3e45", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.25.0_1738983732419_0.30261873353967217", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.1": {"name": "@esbuild/netbsd-x64", "version": "0.25.1", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.25.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "be663893931a4bb3f3a009c5cc24fa9681cc71c0", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.25.1.tgz", "fileCount": 3, "integrity": "sha512-X53z6uXip6KFXBQ+Krbx25XHV/NCbzryM6ehOAeAil7X7oa4XIq+394PWGnwaSQ2WRA0KI6PUO6hTO5zeF5ijA==", "signatures": [{"sig": "MEUCIQDnaYLRjDutCP7oJIMIR5p4Y/TMcSvEDPfh0LeVFE9BcQIgfdYmYHA/UfniPC/T6IdWGSZOf8SfiITCYdiMcA9fusg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10208153}, "engines": {"node": ">=18"}, "gitHead": "6bfc1c13b4d986b86e8bc2035f00c337b0c1d007", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.14.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.25.1_1741578322127_0.9155531056449644", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.2": {"name": "@esbuild/netbsd-x64", "version": "0.25.2", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.25.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "dbbe7521fd6d7352f34328d676af923fc0f8a78f", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.25.2.tgz", "fileCount": 3, "integrity": "sha512-voZT9Z+tpOxrvfKFyfDYPc4DO4rk06qamv1a/fkuzHpiVBMOhpjK+vBmWM8J1eiB3OLSMFYNaOaBNLXGChf5tg==", "signatures": [{"sig": "MEQCIHUWc3Bo1fySrZ2sioSnJ5IQ/GwJcx7F/YNBJHThpZ++AiBeA5z764QOyH8E0/Upn5JIv+TAKcBLLlNEoasA8WBmiQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10212249}, "engines": {"node": ">=18"}, "gitHead": "4475787eef4c4923b92b9fa37ebba1c88b9e1d9b", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.14.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.25.2_1743355972599_0.39078745216783006", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.3": {"name": "@esbuild/netbsd-x64", "version": "0.25.3", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.25.3", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "12f6856f8c54c2d7d0a8a64a9711c01a743878d5", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.25.3.tgz", "fileCount": 3, "integrity": "sha512-i5Hm68HXHdgv8wkrt+10Bc50zM0/eonPb/a/OFVfB6Qvpiirco5gBA5bz7S2SHuU+Y4LWn/zehzNX14Sp4r27g==", "signatures": [{"sig": "MEUCIFOpA595l1hQe6OYB9zL0nR5iEF0zXhfMu9YCoLPctQgAiEAjVfWsMcE4ZGpLT+Ftniz5Z2pFuZXXOkBSylGtukTi50=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10220441}, "engines": {"node": ">=18"}, "gitHead": "677910b073194b64d5ae01aefd7a7465bbf5b27b", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.14.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.25.3_1745380555276_0.463243323755397", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.4": {"name": "@esbuild/netbsd-x64", "version": "0.25.4", "license": "MIT", "_id": "@esbuild/netbsd-x64@0.25.4", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["x64"], "dist": {"shasum": "ec401fb0b1ed0ac01d978564c5fc8634ed1dc2ed", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.25.4.tgz", "fileCount": 3, "integrity": "sha512-XAg8pIQn5CzhOB8odIcAm42QsOfa98SBeKUdo4xa8OvX8LbMZqEtgeWE9P/Wxt7MlG2QqvjGths+nq48TrUiKw==", "signatures": [{"sig": "MEQCIAx0HgwRdqcqUheV5dJSi/3FUl18X8POBCVTBCVnjczeAiBp/ZfouZKfQDkOiTcLcdNLZvL/5LG5iiKQUVaMRhZTwg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10224537}, "engines": {"node": ">=18"}, "gitHead": "218d29e9da018d60cf87b8fb496bb8167936ff54", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.14.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/netbsd-x64_0.25.4_1746491438752_0.7780581022919566", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.5": {"name": "@esbuild/netbsd-x64", "version": "0.25.5", "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "engines": {"node": ">=18"}, "os": ["netbsd"], "cpu": ["x64"], "_id": "@esbuild/netbsd-x64@0.25.5", "gitHead": "ea453bf687c8e5cf3c5f11aae372c5ca33be0c98", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-WOb5fKrvVTRMfWFNCroYWWklbnXH0Q5rZppjq0vQIdlsQKuw6mdSihwSo4RV/YdQ5UCKKvBy7/0ZZYLBZKIbwQ==", "shasum": "a0206f6314ce7dc8713b7732703d0f58de1d1e79", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.25.5.tgz", "fileCount": 3, "unpackedSize": 10224537, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIF1jddpPZAsO5D2U9naVcF/NPm4Q9hCjEyZxMsagJY3jAiAV9ZUQuFfKzJvj5vcsRPyYuLE/OMQO2YTz6/ESBJMCew=="}]}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/netbsd-x64_0.25.5_1748315561984_0.7102973245276094"}, "_hasShrinkwrap": false}}, "time": {"created": "2022-12-05T23:55:55.712Z", "modified": "2025-05-27T03:12:42.427Z", "0.15.18": "2022-12-05T23:55:56.087Z", "0.16.0": "2022-12-07T03:55:00.754Z", "0.16.1": "2022-12-07T04:48:09.248Z", "0.16.2": "2022-12-08T06:59:43.497Z", "0.16.3": "2022-12-08T20:12:52.011Z", "0.16.4": "2022-12-10T03:50:25.452Z", "0.16.5": "2022-12-13T17:47:37.853Z", "0.16.6": "2022-12-14T05:23:11.417Z", "0.16.7": "2022-12-14T22:46:52.873Z", "0.16.8": "2022-12-16T23:38:35.102Z", "0.16.9": "2022-12-18T04:31:15.906Z", "0.16.10": "2022-12-19T23:26:32.078Z", "0.16.11": "2022-12-27T01:39:03.801Z", "0.16.12": "2022-12-28T02:04:41.174Z", "0.16.13": "2023-01-02T22:57:17.303Z", "0.16.14": "2023-01-04T20:12:55.181Z", "0.16.15": "2023-01-07T04:18:52.290Z", "0.16.16": "2023-01-08T22:43:49.315Z", "0.16.17": "2023-01-11T21:57:55.033Z", "0.17.0": "2023-01-14T04:33:40.643Z", "0.17.1": "2023-01-16T18:05:33.261Z", "0.17.2": "2023-01-17T06:39:40.662Z", "0.17.3": "2023-01-18T19:14:26.042Z", "0.17.4": "2023-01-22T06:13:38.322Z", "0.17.5": "2023-01-27T16:37:46.696Z", "0.17.6": "2023-02-06T17:00:45.217Z", "0.17.7": "2023-02-09T22:26:40.948Z", "0.17.8": "2023-02-13T06:35:38.788Z", "0.17.9": "2023-02-19T17:45:18.231Z", "0.17.10": "2023-02-20T17:54:54.528Z", "0.17.11": "2023-03-03T22:40:09.588Z", "0.17.12": "2023-03-17T06:16:19.303Z", "0.17.13": "2023-03-24T18:57:04.121Z", "0.17.14": "2023-03-26T02:47:40.191Z", "0.17.15": "2023-04-01T22:26:48.528Z", "0.17.16": "2023-04-10T04:34:58.313Z", "0.17.17": "2023-04-16T21:23:37.524Z", "0.17.18": "2023-04-22T20:41:23.191Z", "0.17.19": "2023-05-13T00:06:27.361Z", "0.18.0": "2023-06-09T21:24:18.330Z", "0.18.1": "2023-06-12T04:51:42.572Z", "0.18.2": "2023-06-13T02:40:27.893Z", "0.18.3": "2023-06-15T12:20:51.233Z", "0.18.4": "2023-06-16T15:38:15.602Z", "0.18.5": "2023-06-20T00:52:36.312Z", "0.18.6": "2023-06-20T23:24:51.092Z", "0.18.7": "2023-06-24T02:46:23.346Z", "0.18.8": "2023-06-25T03:19:10.831Z", "0.18.9": "2023-06-26T05:27:51.021Z", "0.18.10": "2023-06-26T21:20:25.963Z", "0.18.11": "2023-07-01T06:03:50.653Z", "0.18.12": "2023-07-13T01:34:03.299Z", "0.18.13": "2023-07-15T02:37:12.559Z", "0.18.14": "2023-07-18T05:00:15.803Z", "0.18.15": "2023-07-20T12:53:13.941Z", "0.18.16": "2023-07-23T04:48:01.535Z", "0.18.17": "2023-07-26T01:40:50.766Z", "0.18.18": "2023-08-05T17:06:26.026Z", "0.18.19": "2023-08-07T02:51:15.049Z", "0.18.20": "2023-08-08T04:14:54.144Z", "0.19.0": "2023-08-08T15:52:07.379Z", "0.19.1": "2023-08-11T15:57:27.004Z", "0.19.2": "2023-08-14T01:58:16.898Z", "0.19.3": "2023-09-14T01:12:19.222Z", "0.19.4": "2023-09-28T01:46:46.885Z", "0.19.5": "2023-10-17T05:10:29.643Z", "0.19.6": "2023-11-19T07:11:30.434Z", "0.19.7": "2023-11-21T01:00:51.914Z", "0.19.8": "2023-11-26T23:07:52.567Z", "0.19.9": "2023-12-10T05:09:22.129Z", "0.19.10": "2023-12-19T00:21:25.800Z", "0.19.11": "2023-12-29T20:31:47.001Z", "0.19.12": "2024-01-23T17:40:16.337Z", "0.20.0": "2024-01-27T16:49:27.277Z", "0.20.1": "2024-02-19T06:37:57.901Z", "0.20.2": "2024-03-14T19:49:38.714Z", "0.21.0": "2024-05-07T02:52:21.798Z", "0.21.1": "2024-05-07T16:54:54.161Z", "0.21.2": "2024-05-12T20:32:51.771Z", "0.21.3": "2024-05-15T20:52:25.526Z", "0.21.4": "2024-05-25T02:10:47.238Z", "0.21.5": "2024-06-09T21:16:52.583Z", "0.22.0": "2024-06-30T20:37:43.904Z", "0.23.0": "2024-07-02T03:33:40.297Z", "0.23.1": "2024-08-16T22:13:11.332Z", "0.24.0": "2024-09-22T02:06:19.487Z", "0.24.1": "2024-12-20T05:40:56.440Z", "0.24.2": "2024-12-20T17:56:12.289Z", "0.25.0": "2025-02-08T03:02:12.691Z", "0.25.1": "2025-03-10T03:45:22.334Z", "0.25.2": "2025-03-30T17:32:52.886Z", "0.25.3": "2025-04-23T03:55:55.545Z", "0.25.4": "2025-05-06T00:30:39.059Z", "0.25.5": "2025-05-27T03:12:42.236Z"}, "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "license": "MIT", "homepage": "https://github.com/evanw/esbuild#readme", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "description": "The NetBSD AMD64 binary for esbuild, a JavaScript bundler.", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "readme": "# esbuild\n\nThis is the NetBSD AMD64 binary for esbuild, a JavaScript bundler and minifier. See https://github.com/evanw/esbuild for details.\n\n⚠️ Note: NetBSD is not one of [Node's supported platforms](https://nodejs.org/api/process.html#process_process_platform), so installing esbuild may or may not work on NetBSD depending on how Node has been patched. This is not a problem with esbuild. ⚠️\n", "readmeFilename": "README.md"}