{"_id": "@discoveryjs/json-ext", "_rev": "20-6d0e8f421332393b5d3e1bc30da2ed93", "name": "@discoveryjs/json-ext", "dist-tags": {"latest": "0.6.3"}, "versions": {"0.1.0": {"name": "@discoveryjs/json-ext", "version": "0.1.0", "keywords": ["json", "utils", "stream", "async", "promise", "stringify", "info"], "author": {"url": "https://github.com/lahmatiy", "name": "<PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}, "license": "MIT", "_id": "@discoveryjs/json-ext@0.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/discoveryjs/json-ext#readme", "bugs": {"url": "https://github.com/discoveryjs/json-ext/issues"}, "dist": {"shasum": "58212ca5478669a5a50b5fa7a80ef9af86bd6092", "tarball": "https://registry.npmjs.org/@discoveryjs/json-ext/-/json-ext-0.1.0.tgz", "fileCount": 8, "integrity": "sha512-eAioVEGXRlMAXDI+yk9FmVE6+HsLzlafrx6gEk1MlYAMsUscx+2Lva/IP5wQ82K1Hmt15E0Uq0+/7ytD5SQpEw==", "signatures": [{"sig": "MEUCIGzru3Yb1t7QBSZp2PJqp2lH+7LGyVqzdQakcASXA63aAiEAjd0RV3LH/FaHiLQIoXvz0rO1/cWzccI2g4J3udZuLFQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24211, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfV1y5CRA9TVsSAnZWagAAJlQQAJpFmCurNYZsSHG8e7a4\nz0vpJEkNjsnoA7+2wVHEdnvUsxaIsHwrnG6teblvwDqS8QbSrm0AITPsGtCF\n5zFBWPDZqhYU3A8JMjvJKmjZJj7lFRQNoHirUWljfsVak7OpqRZBO2yzSRL1\nsiu9gnFAYPOBnhJsnvJMBV1VZ/VFqGPmkTKbgXzI/LXfxg0SkWNLjq5BLUr4\nte4ToJyMKeiBtUIzYyb1zZRnjd796ac/Up0r/po70gvZ1L2aEp/DgVFtsSK5\nBAIB94dJuaXUJU7f4/tFtlECsgT59XeW0t4WDSrDDk/geEHFtIzRbYQ106rg\nrXrE2K687QPGRZB4bHsuGBRyGkBmOKa0FRSfAwoNNdBPYtuCdpyzp1ckjBWT\nw/dL2P5rA1QBMchzjaey/6MOUEWV28SOUnRwvAlJFm0EQynS4TosRWFyn8td\nxJNtgTlc4PmkbLg+iDOUtH1HhQ8Lu5q3npSDtfoH7nVvWp4kXm+FMKEvOIwx\nsZ7Djmyv/tPYGpYbXeZLa4JtWcuw5OqpRQz8YSHIklt6ILNGFyAtY6O6uNdc\nkf71ucPnqoQb/XIAwBdNdkI6bFDr4f/6TorXmtUZLgw/6g2E4yEwyYv/Ncjw\nRQKPEXIsbbq83b/q8na6p5qq8RrrGRHL/mhOQ202E6k8KBsfBlOXG6MOM2/j\nVEMY\r\n=n/Bd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index", "engines": {"node": ">=12.0.0"}, "gitHead": "afb917b9da63eafce1bb6f3c26b9a398e5d551a5", "scripts": {"lint": "eslint src test", "test": "mocha --reporter progress", "travis": "nyc npm run lint-and-test && npm run coveralls", "coverage": "nyc npm test", "coveralls": "nyc report --reporter=text-lcov | coveralls", "lint-and-test": "npm run lint && npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/discoveryjs/json-ext.git", "type": "git"}, "_npmVersion": "6.14.7", "description": "A set of utilities that extend the use of JSON", "directories": {}, "_nodeVersion": "14.9.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "mocha": "^8.1.1", "eslint": "^7.6.0", "coveralls": "^3.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/json-ext_0.1.0_1599560889285_0.9834944447673313", "host": "s3://npm-registry-packages"}}, "0.1.1": {"name": "@discoveryjs/json-ext", "version": "0.1.1", "keywords": ["json", "utils", "stream", "async", "promise", "stringify", "info"], "author": {"url": "https://github.com/lahmatiy", "name": "<PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}, "license": "MIT", "_id": "@discoveryjs/json-ext@0.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/discoveryjs/json-ext#readme", "bugs": {"url": "https://github.com/discoveryjs/json-ext/issues"}, "dist": {"shasum": "7d36c499032406e3799f1c3f54f2f9b3f4272026", "tarball": "https://registry.npmjs.org/@discoveryjs/json-ext/-/json-ext-0.1.1.tgz", "fileCount": 8, "integrity": "sha512-T5TdVRPth7Vew3oF649Hf1OkLFTvAvRpCq5vRJehKWOHSWqeOQyAUyUzvDEmql/IgfKyafOi6eB1sg6qXGkj/w==", "signatures": [{"sig": "MEYCIQCMuqSJRSH0HA3udCMT/yaoR5u9eQPUUSHOHdLipYB9egIhAKY0kQG+3LWIs/AvDWVlya3RPAdmxF341yxFKgAaGcDP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24303, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfV6zlCRA9TVsSAnZWagAAqdUP/2yJ0JfgdmgBvbsv3/IX\n8jSYq3H7UlJ5WdEmj7/ERkMbi2UzIrvyvb9UZsEPjQBQtCYaq9egPrUTizmj\nAa7HT+TE89r8F9b3oxWE2mvVtXnQQiv2vSSRC8kt7p9PvMuvodhmL24MQLg2\nG9GOYI090tfZ/9woTX5GGUS9izlNkFTEYxTRxat+w7eDlW6GnXKX8eznf4fe\nwq2g9WcVFTJ99cZZ2lrnxZYoYBNceKrOdqy9XKUg7Ac/+5q1nsTDOZikO+4I\nlcEhsJnylSSaNM/1GrvPFu/F4y1jObv5QSVw26iH27iY0EQ45Vo/l2aKN2vQ\nUJ0H+7U2vDebsXwiELkz1dhaOvFQFWBEVgVZcxgp1f1QuomWAYiGX4o4ER8C\nBEtf5YzG/PvJ31t9BvicgYmTTCWnDZEfEkMmp5LE0bA9bQuDhQwErUHaxr/D\n4jjC8TP1alkJgFkD8uRs70KwWYgE1Ra8v1dw4q+osbbSnE3c1YlY7757MXi2\nlU3I47BURTAqFFVOZejZyiVk5i6bEatHq5nop2qRVVCfDUkOA46QiaezNelg\nCWCav6YR7tGhmu4oQNH1alQfv6muc3SaXs7+yi8kUhuNqw0Ijm+pkUQVd9aT\n4HiZ1x03rDECue6kWbIdX2e1S/ySjH4Dnr8GYiFDy3nl1oyvvkTG4MPyUpOH\nwIhF\r\n=D8xg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/index", "engines": {"node": ">=12.0.0"}, "gitHead": "71140a057fc45576f6bd0a5114fe04eaa8700776", "scripts": {"lint": "eslint src test", "test": "mocha --reporter progress", "travis": "nyc npm run lint-and-test && npm run coveralls", "coverage": "nyc npm test", "coveralls": "nyc report --reporter=text-lcov | coveralls", "lint-and-test": "npm run lint && npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/discoveryjs/json-ext.git", "type": "git"}, "_npmVersion": "6.14.7", "description": "A set of utilities that extend the use of JSON", "directories": {}, "_nodeVersion": "14.9.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "mocha": "^8.1.1", "eslint": "^7.6.0", "coveralls": "^3.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/json-ext_0.1.1_1599581413133_0.7627358891071205", "host": "s3://npm-registry-packages"}}, "0.2.0": {"name": "@discoveryjs/json-ext", "version": "0.2.0", "keywords": ["json", "utils", "stream", "async", "promise", "stringify", "info"], "author": {"url": "https://github.com/lahmatiy", "name": "<PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}, "license": "MIT", "_id": "@discoveryjs/json-ext@0.2.0", "maintainers": [{"name": "exdis", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/discoveryjs/json-ext#readme", "bugs": {"url": "https://github.com/discoveryjs/json-ext/issues"}, "dist": {"shasum": "8596902f4339526cfd94b509b0735e5dc0edace3", "tarball": "https://registry.npmjs.org/@discoveryjs/json-ext/-/json-ext-0.2.0.tgz", "fileCount": 8, "integrity": "sha512-nAH9XTVYpEPXmrO1QnX59LNLVyx+q8VCZ9+rnM1gKop7zAEq92VMCEknvpIv0EdUdVswo1h9uB2B1xhvbbZjgg==", "signatures": [{"sig": "MEYCIQC9pH4fxeX20AlSQA1xw/s21spbZLq6Jvpv9a1d7uMQyAIhAIuzPims0n25VpyW4i/ItMdaeFscJuG+ZXsmfQzG1juL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24703, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfcdhmCRA9TVsSAnZWagAAes0P/2pEoZygPA+VsxiIIgCe\nvFZMxSkwmB5rAILM3oAsTBAgplTkGhY94DN6KWwiFQAoEB/2Bp54U6li96Lb\nomRXtCclYAsdTtS6UM2VrDc6Vb4OFD6MmfH1y2M5o5fDaaf14/K7BrojMaKM\n6LUCP11BE7kShdZ/p+va6XDr/sp2q8tB8Vti5mZCUmuYUTORG9KLmoDG7LDZ\nYVMtvLeZmOHYFq+8G9BA+BpttrsAu117r0LNQzQ7sY2u2aVid4F67fp6t3f6\nvEx0w3uX0ele9E5I05gnjeOT/YWclxsv6+VVmTsqht/ejA9NY3JopaVXtdXH\nSEG8UxEsLajQmPFD0TskhamcXEDsKsHEr3qCAJKbPJ9PSYt5TPe31Jgp5tgm\nnlqn1T8KiVrVMaEBQmgg+JQqkaqC3vhVGd1bTMClc3OwDVz3WvCg6b/IV38p\nQ4PWcLxhOZJnVylgOFAEytZtNmWeo1YbAHnkod7zKI/fFsffpAQCtmjsEMMS\n3NZkRNirtooW/8cPEI55JjSs1kJUrzlO8dZILJpIbwBOr24qvSWr+ffcoJ7v\nEuAwmLjK4KSDFVfy4QYi1cS9A0OQG9iLi36YtE10i6sGC9kWfH03Blbw/YVY\nWxmZwjalH6IyX3OdNjJ4uRzeQc7avQTsgwQ2xpvVaNBfAOWzx5vOdRpBEYJj\nZXJf\r\n=swzT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/index", "engines": {"node": ">=12.0.0"}, "gitHead": "401a2a1731687ee53e83c40525515b4485b04f31", "scripts": {"lint": "eslint src test", "test": "mocha --reporter progress", "build": "rollup --config", "travis": "nyc npm run lint-and-test && npm run coveralls", "coverage": "nyc npm test", "coveralls": "nyc report --reporter=text-lcov | coveralls", "lint-and-test": "npm run lint && npm test", "build-and-test": "npm run build && npm run test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/discoveryjs/json-ext.git", "type": "git"}, "_npmVersion": "6.14.7", "description": "A set of utilities that extend the use of JSON", "directories": {}, "_nodeVersion": "14.9.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "mocha": "^8.1.1", "eslint": "^7.6.0", "rollup": "^2.28.2", "coveralls": "^3.1.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-commonjs": "^15.1.0", "@rollup/plugin-node-resolve": "^9.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/json-ext_0.2.0_1601296485812_0.8763217546070752", "host": "s3://npm-registry-packages"}}, "0.3.0": {"name": "@discoveryjs/json-ext", "version": "0.3.0", "keywords": ["json", "utils", "stream", "async", "promise", "stringify", "info"], "author": {"url": "https://github.com/lahmatiy", "name": "<PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}, "license": "MIT", "_id": "@discoveryjs/json-ext@0.3.0", "maintainers": [{"name": "exdis", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/discoveryjs/json-ext#readme", "bugs": {"url": "https://github.com/discoveryjs/json-ext/issues"}, "dist": {"shasum": "d0943856e54e5859268e67b2d7f3755077e8a948", "tarball": "https://registry.npmjs.org/@discoveryjs/json-ext/-/json-ext-0.3.0.tgz", "fileCount": 10, "integrity": "sha512-hT6NV1B7CzT2s/XujkifKIFkcXzkT++p6jiJup5xaJol53yU34q16Ovpva0FeRIfHs60KnU+M3GecNZUW+ed6Q==", "signatures": [{"sig": "MEQCIHI6wLrbsyaNHLF98ynEDrAsXi3CbuYQ22+LoPobCXhjAiAp8gNiIQy1WT0xTxCqWJXrbZZ6EqV/p0tje/nULymOTg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40381, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfceKOCRA9TVsSAnZWagAAxxkP/jWO+PXbWmaCDEuDDKYR\n8yLq9H64s0QqtBq3sBhY8CvL6Kq6c3b3Ox0vHXbshvIlT0UOVich3bDLE49E\neoukplilIPQjQuxkU9IrbZnHpHVVPhrEZPmjePYszZx+rfW3HsdB3hGlQaJa\nTmINVpWxDDyFhvYTV6i+c2cI9VECATpSqlm2h2Sm9ELrKuBPk25lB2B1Rjug\nDa3Lwuo3nX6YErwhy1zXRR/wXUD1waRnDhIQTN4mD3Bysfo9zDB19LGZx/2i\ngu9rxRMKc4D6BJLnNyxY/XsJkcNjRHUL3jchAZCKNdIKtKJY881XvLywVq+X\nAayKLbu7CktXjnox0DIp3HU3AzIIT0q6GF8oKTCuXb8jji1SRTI/wu6fZnma\n/sCdclV/VrkQjYOLAAgBARvrNbcRw2eNpWO92uddbZkiU/JLKINu28TMoDx/\nxTCaV+GUFqo2jKfL38tVgSWT9nT0+3Hf60CJqYekuD0mu7XixWfFnph68OWR\nq5fl/0HD+gOdDuYghX7z4J8vZkIOuJR9rQkQgl8tZWPcL6hMOtlAt9lMDsVO\nuWc7CW4Jvc0iKQSqQGJrfT+u919MFfjUQBEhsWnuKNj/oa/1pRLCmNyEDm8j\ncH2+SCLQOqse8beaf0RXIHm1Q7IsjtEJIt3oZsCwOSirXK9UXstHZiN8Qht1\nXnsl\r\n=4ik/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/index", "engines": {"node": ">=12.0.0"}, "gitHead": "b7cd5982f1653821588684bea0a09432eba30f23", "scripts": {"lint": "eslint src test", "test": "mocha --reporter progress", "build": "rollup --config", "travis": "nyc npm run lint-and-test && npm run coveralls", "coverage": "nyc npm test", "test:all": "npm run test:src && npm run test:dist", "test:src": "npm test", "coveralls": "nyc report --reporter=text-lcov | coveralls", "test:dist": "MODE=dist npm test && MODE=dist-min npm test", "lint-and-test": "npm run lint && npm test", "build-and-test": "npm run build && npm run test:dist", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/discoveryjs/json-ext.git", "type": "git"}, "_npmVersion": "6.14.7", "description": "A set of utilities that extend the use of JSON", "directories": {}, "_nodeVersion": "14.9.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "chalk": "^4.1.0", "mocha": "^8.1.1", "eslint": "^7.6.0", "rollup": "^2.28.2", "coveralls": "^3.1.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-commonjs": "^15.1.0", "@rollup/plugin-node-resolve": "^9.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/json-ext_0.3.0_1601299085771_0.009405064097204097", "host": "s3://npm-registry-packages"}}, "0.3.1": {"name": "@discoveryjs/json-ext", "version": "0.3.1", "keywords": ["json", "utils", "stream", "async", "promise", "stringify", "info"], "author": {"url": "https://github.com/lahmatiy", "name": "<PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}, "license": "MIT", "_id": "@discoveryjs/json-ext@0.3.1", "maintainers": [{"name": "exdis", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/discoveryjs/json-ext#readme", "bugs": {"url": "https://github.com/discoveryjs/json-ext/issues"}, "dist": {"shasum": "7b33ccbef07506fe60d2ed7c9d70c8901ef01083", "tarball": "https://registry.npmjs.org/@discoveryjs/json-ext/-/json-ext-0.3.1.tgz", "fileCount": 10, "integrity": "sha512-Mm1lAQBGmvEc6FmFI+Jqg3ox4c6qlKJEWfvr89hwzytLOLJEp0ehnZVqZRKTggmQUhvvlQUI/1odM5viEW9TAQ==", "signatures": [{"sig": "MEUCIQDzJE/vT4RF52DLxDdlDBfKA47kKxAbwE9aUsSoUbmLSwIgMOBJr2uO5TCou0czwyioINU4CoiR/2pkOpaKOD1NUnQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43001, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJflsu/CRA9TVsSAnZWagAAtUUP/i53qKw1BfNhZg2cU5wa\n/fenY9HNUGTgIqAEjp5Tx9OdKSpLaTDCbgplhpcsBtCEuHzulMTZyhLnF92z\nR+iaD+z+nTNpUE3ddhTp3JoDOCxjODelFghVplx7N8ZSkUAYQ4KrNV6e8syl\nD7/JYrLc6zQcCrGaYk2EDMrx5kF7uDxAboHFpDx1xDPvDMH80uyxKClcpUo4\nTe/U/wbtVPmIC65/wKVt+rQP3OZZ1WeE10ZbrwAmJPJUmI/IFMxFacEFB8UW\ngTTW5triAaC4tVq1JZNrm6c5ia9gVB0GjcUKh+f8urIErQQ3fx5zZEfgnGzB\nE0PUwBGNkbMr0vkIa7Z/iVYZtVPwUbsUtDPcxrzu2Gbfj4CcVAb3CBx8GDg8\n1TsYn1DAh4391cfj+Q+kcQfZg0ScztEjrDx148BH9158eS6gm6T0wkliIKhY\n0YtB1s8NyyvK1WlIjcWGMHaVEMFpbbBsd7vIiSuTywQYyOOcwcnZURH8CqYd\ndyG2ShS0T56oErtz+xk/HLxvbUaZvo7SGDHN/hYELrIwB37kxgflQPJ3wRWE\nvQgguuEysnJP418+h+h/X9bn4h5pN5KQAf8jlr5swGCnAfWDEsAlHmjqJ3WV\nQt83D2vprkrS4RrgyBPqNsCXbWaI4tGFxMK3NnldWVOKnAEGH5mBqz8rUxdN\np9WU\r\n=M2wa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/index", "browser": {"./src/stringify-stream.js": "./browser-method-is-not-supported.js"}, "engines": {"node": ">=12.0.0"}, "gitHead": "bf83e68e36f6ced3fb8c5834b7ceb29761fedc59", "scripts": {"lint": "eslint src test", "test": "mocha --reporter progress", "build": "rollup --config", "travis": "nyc npm run lint-and-test && npm run coveralls", "coverage": "nyc npm test", "test:all": "npm run test:src && npm run test:dist", "test:src": "npm test", "coveralls": "nyc report --reporter=text-lcov | coveralls", "test:dist": "MODE=dist npm test && MODE=dist-min npm test", "lint-and-test": "npm run lint && npm test", "build-and-test": "npm run build && npm run test:dist", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/discoveryjs/json-ext.git", "type": "git"}, "_npmVersion": "6.14.7", "description": "A set of utilities that extend the use of JSON", "directories": {}, "_nodeVersion": "14.9.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "chalk": "^4.1.0", "mocha": "^8.1.1", "eslint": "^7.6.0", "rollup": "^2.28.2", "coveralls": "^3.1.0", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-commonjs": "^15.1.0", "@rollup/plugin-node-resolve": "^9.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/json-ext_0.3.1_1603718078744_0.6945715012920795", "host": "s3://npm-registry-packages"}}, "0.3.2": {"name": "@discoveryjs/json-ext", "version": "0.3.2", "keywords": ["json", "utils", "stream", "async", "promise", "stringify", "info"], "author": {"url": "https://github.com/lahmatiy", "name": "<PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}, "license": "MIT", "_id": "@discoveryjs/json-ext@0.3.2", "maintainers": [{"name": "exdis", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/discoveryjs/json-ext#readme", "bugs": {"url": "https://github.com/discoveryjs/json-ext/issues"}, "dist": {"shasum": "80ff80f3b221890fd125f185da1d2642e73abfa1", "tarball": "https://registry.npmjs.org/@discoveryjs/json-ext/-/json-ext-0.3.2.tgz", "fileCount": 11, "integrity": "sha512-C7G6IE8/8tVrLhXxNIh7Jwt2qIDXGUlwMNU4lSRzcwfcxM4Vb5yOnfR+EtoC1ci+UBSJE1NfbY4O0VcsnIhlhQ==", "signatures": [{"sig": "MEUCIQCvemX38h4zaaVcQiffKs0Zrxc/FB3TvvcXTLt8u/fZQgIgZ9/9nyAIFpNVuDlhNV6twVdXI7PoK45/QEhmXmopMT4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43148, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfltJSCRA9TVsSAnZWagAAqbEP/Rx89UfFrIyuRwmWdphg\nZOdrOG2oyAb2l/QHNulpwqKcp5NFxCQ7Mf5UvqWqZfTpAXuXPbjZSxuvLXMv\naXALMHUpe6W1rQAmgfwM5/JfEgDkyHjAXXCdnbvwD4I3MChmzxQPY2v9sHXI\n0ovOc1MXnfhnwBSbdbGFnbLrRIJ5juXpw2Md8c3dqKuslIzEZq1Jeze/j6G0\naVbjrmUuJTJAHKdCTOjshHc/w0Dr9dwYhkQCbKYOBi2F7HzMV2OikqCDSU+N\nU4VoLjWS4OfAlqO4L+ey2Smh/h6UA2oBzmztvhZOmnaJ+bc90xCzdp7UzQew\npIWOXNObUzpbtDEEvEzXgD4BluGBWMoSKa2VZ98bkEAMdRSb3ZbQgwJ5w9MU\nyLdNZoJ0tg7SeFV/gOu5cj+MesQFRm/q3+VrfDQVgZGmO1ZB++m7DSpLV+t2\nksa6UjdjiaEp4miJ3mIxE+FyfQZDaV/dDeGTViK8a3r6LkOR6LnizxtYGGA+\n/UGtDqnHntEUmtGgB8l++97S6AeQtvYhBNTtUrLdkodGaPaUbgCPOTCsYR06\nPe1DXY/YOUHP7teNZHYJZCmbjZtjLEHiVti1yompENPj5+LU8ZgYaXPndKqi\nmcoHJpSwbo+9R3WrIdVNMwKNpLzeAQoOtQrjNybKUTIeksp6aemlF2JBOc0t\nOj/T\r\n=GWoi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/index", "browser": {"./src/stringify-stream.js": "./src/browser-method-is-not-supported.js"}, "engines": {"node": ">=12.0.0"}, "gitHead": "a899d78e2aaa136ddf194a4ca5bf6d9b15041f50", "scripts": {"lint": "eslint src test", "test": "mocha --reporter progress", "build": "rollup --config", "travis": "nyc npm run lint-and-test && npm run coveralls", "coverage": "nyc npm test", "test:all": "npm run test:src && npm run test:dist", "test:src": "npm test", "coveralls": "nyc report --reporter=text-lcov | coveralls", "test:dist": "MODE=dist npm test && MODE=dist-min npm test", "lint-and-test": "npm run lint && npm test", "build-and-test": "npm run build && npm run test:dist", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/discoveryjs/json-ext.git", "type": "git"}, "_npmVersion": "6.14.7", "description": "A set of utilities that extend the use of JSON", "directories": {}, "_nodeVersion": "14.9.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "chalk": "^4.1.0", "mocha": "^8.1.1", "eslint": "^7.6.0", "rollup": "^2.28.2", "coveralls": "^3.1.0", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-commonjs": "^15.1.0", "@rollup/plugin-node-resolve": "^9.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/json-ext_0.3.2_1603719762168_0.9084722674572594", "host": "s3://npm-registry-packages"}}, "0.4.0": {"name": "@discoveryjs/json-ext", "version": "0.4.0", "keywords": ["json", "utils", "stream", "async", "promise", "stringify", "info"], "author": {"url": "https://github.com/lahmatiy", "name": "<PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}, "license": "MIT", "_id": "@discoveryjs/json-ext@0.4.0", "maintainers": [{"name": "exdis", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/discoveryjs/json-ext#readme", "bugs": {"url": "https://github.com/discoveryjs/json-ext/issues"}, "dist": {"shasum": "f88244572b887d8379b7015f88e58f68e13806c2", "tarball": "https://registry.npmjs.org/@discoveryjs/json-ext/-/json-ext-0.4.0.tgz", "fileCount": 12, "integrity": "sha512-duX5JTqLWZYpcAxWmDGXX4nm3LDaVCgl/yMSKNXtpzbM5KAm5yQxvtFByt1+SJQDies/lJBK0sIFtqAyja0cuQ==", "signatures": [{"sig": "MEYCIQCFPugkaABppmpnubRGjiL2jvdznfFLdTrnnIOVjOShiQIhAO95OCvbK0pmHO0fld6AIGQIcxFZTqhRjH7O8OSMzvuO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76490, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfyr2BCRA9TVsSAnZWagAAnu4P/jzM5jI1X/oZHOkYobKE\ncHjA5tYQ/hSim44oau6vVaWroAhAyN8U/jFUHvPjNlaVXHjdnqw/yQN14gzB\nAeeymIFMOuEf30nmSfcejTGTB/pePp3Dcv0dmw8ozU13KC0SFz1o8UGExXDp\ngZUIfLeYngs6zcpX5mzZ4bYFDM56nnnsP0Oq9RR7OBRc5GIW25/kCFZ9WNYM\nsthEpZtvms3lqJEwoGmLfnpe/xBRNaGcnL8fs/lEkHdZvgRDyEOgHM3TZSmr\npYI7D0fuqIJjR0xBrq8XdTHFa8pxAtZuiI5qk+Pc47vTWYngX3NPMfrONkSl\nqKWcNww3Q7YpCvNnW4j5aNhtS6BP1wVaUmfOaozBXJIplW+l7Y/Dr93DjvSO\n0Y43+ihyg84z11Ab3PCq6UE9v8jfrpF9roG5JHQw0P18exte/b0Me6lileN7\ntnBgfESZovxj0+bQG4QIcHoEYfW2GUeeaH1C9Y1uz0cSEb0SUhBTGNIJxAdV\nDDExFQ8hT0ciJSv6zMrW8cbOhcQmjT/lJeIuzA0mIjg7KhhPcudAeH3pwKXE\n4Uwc87+GqE8CPjX9iisp+RzoR1fav1GNdheyuIAi6g/RGW8sXb52ZSdOFnEX\nc/fT+KEvnDbr2luWn0O9JB6eO1GbVztALvz4QePA5Bo95VzI8MhTaDXIy7QZ\ncW5O\r\n=SyMd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/index", "browser": {"./src/stringify-stream.js": "./src/browser-method-is-not-supported.js"}, "engines": {"node": ">=12.0.0"}, "gitHead": "fa11f97956e0a3a5d9d1f93be813b243ff41e39f", "scripts": {"lint": "eslint src test", "test": "mocha --reporter progress", "build": "rollup --config", "travis": "nyc npm run lint-and-test && npm run coveralls", "coverage": "nyc npm test", "test:all": "npm run test:src && npm run test:dist", "test:src": "npm test", "coveralls": "nyc report --reporter=text-lcov | coveralls", "test:dist": "MODE=dist npm test && MODE=dist-min npm test", "lint-and-test": "npm run lint && npm test", "build-and-test": "npm run build && npm run test:dist", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/discoveryjs/json-ext.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "A set of utilities that extend the use of JSON", "directories": {}, "_nodeVersion": "15.3.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "chalk": "^4.1.0", "mocha": "^8.1.1", "eslint": "^7.6.0", "rollup": "^2.28.2", "coveralls": "^3.1.0", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-commonjs": "^15.1.0", "@rollup/plugin-node-resolve": "^9.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/json-ext_0.4.0_1607122304432_0.6432348233936858", "host": "s3://npm-registry-packages"}}, "0.5.0": {"name": "@discoveryjs/json-ext", "version": "0.5.0", "keywords": ["json", "utils", "stream", "async", "promise", "stringify", "info"], "author": {"url": "https://github.com/lahmatiy", "name": "<PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}, "license": "MIT", "_id": "@discoveryjs/json-ext@0.5.0", "maintainers": [{"name": "exdis", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/discoveryjs/json-ext#readme", "bugs": {"url": "https://github.com/discoveryjs/json-ext/issues"}, "dist": {"shasum": "d6cf8951ceb673db41861d544cef2f2e07ebcb4d", "tarball": "https://registry.npmjs.org/@discoveryjs/json-ext/-/json-ext-0.5.0.tgz", "fileCount": 14, "integrity": "sha512-gX9Hx+2BMP5+yXfr4Agb+iBd9YiI729x38wbhfvRSkxQqfXnJUNy1nnyJWetYCvxnL1caWd1HqJnbQwVrgvgeA==", "signatures": [{"sig": "MEUCIQChsG6ZEDoKm5jCufNNIaLCqz9mho8Tted7+goP4Fn3HgIgP3yjCzELf9OtX9GovjiTOfjQ1e4Pxp9HdvC/GTe37Dc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77498, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfzA60CRA9TVsSAnZWagAA5vsQAIljXo2d9mi/p82SMtIE\n3ZgomVS4f5S9pvxIwqQSKhEBGFZFrvYmMi/ju3EFBYqRBtcvIog8Ov5fsovs\n7PPG2McvJcTf5NwpJyaOIPr0ELPpZOfjdx9fEqjAwrg65g/nRzIA7oxG1FL6\ndjcLAs0x16DtrIiVfPtt0EVrtZjdABGLhPAGuBFqxioKsGX2VDeEaSsKxJxj\nhlPNCjHLBtJkWKYIWw5vUoNrrrnd3mYBaC7Lh01J3ARtebxM3b0gUO/hLj28\nliHjyBgNooAOdKGWAruvzgE5agpU5kZ8Vqje4PT1jM5m3I5f/N2JZdpy3X8V\nezyq8/PpJNt88p1QOikSGcPLQV/iuv2elTj04ALe09XjwmwKxQTwSPcLckBN\nkhymUj4hLp5GNE8OduwtRG5m6HG4QMWsdOhBzwfvMzWmPo98ltHSI342bVUe\nEmLINhgV/765/S8UW/v1leGn/uNW6ha/IFBp5/6e+L0HzNZBqgyXBrowYa+7\nma8Qx6mZL0sAy76EHH/pCIHJhQMtVyBACaHGN5LrtwDgfGi/ugX4Wo6WqC7Q\nUpiHt08A7wg7KLpwk7FBFLxx3QiN6EfcJpqL5qXJ/jgbB4Hvet+nH1g3KOwN\nHVKSILcf8AwKFhQ74EFmQItjx8D/tAygRLhAsD0YJRXmyuvX5S0UyX5051Cc\neJMg\r\n=dYqG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/index", "browser": {"./src/text-decoder.js": "./src/text-decoder-browser.js", "./src/stringify-stream.js": "./src/stringify-stream-browser.js"}, "engines": {"node": ">=10.0.0"}, "gitHead": "0f7c96dd8d726eac751b0d141e61801448cae894", "scripts": {"lint": "eslint src test", "test": "mocha --reporter progress", "build": "rollup --config", "travis": "nyc npm run lint-and-test && npm run coveralls", "coverage": "nyc npm test", "test:all": "npm run test:src && npm run test:dist", "test:src": "npm test", "coveralls": "nyc report --reporter=text-lcov | coveralls", "test:dist": "MODE=dist npm test && MODE=dist-min npm test", "lint-and-test": "npm run lint && npm test", "build-and-test": "npm run build && npm run test:dist", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/discoveryjs/json-ext.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "A set of utilities that extend the use of JSON", "directories": {}, "_nodeVersion": "15.3.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "chalk": "^4.1.0", "mocha": "^8.1.1", "eslint": "^7.6.0", "rollup": "^2.28.2", "coveralls": "^3.1.0", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-commonjs": "^15.1.0", "@rollup/plugin-node-resolve": "^9.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/json-ext_0.5.0_1607208628075_0.5281334656205079", "host": "s3://npm-registry-packages"}}, "0.5.1": {"name": "@discoveryjs/json-ext", "version": "0.5.1", "keywords": ["json", "utils", "stream", "async", "promise", "stringify", "info"], "author": {"url": "https://github.com/lahmatiy", "name": "<PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}, "license": "MIT", "_id": "@discoveryjs/json-ext@0.5.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "exdis", "email": "<EMAIL>"}], "homepage": "https://github.com/discoveryjs/json-ext#readme", "bugs": {"url": "https://github.com/discoveryjs/json-ext/issues"}, "dist": {"shasum": "cf87081ac9b0f3eb3b5740415b50b7966bac8fc5", "tarball": "https://registry.npmjs.org/@discoveryjs/json-ext/-/json-ext-0.5.1.tgz", "fileCount": 14, "integrity": "sha512-Oee4NT60Lxe90m7VTYBU4UbABNaz0N4Q3G62CPB+6mGE4KuLMsTACmH8q3PH5u9pSZCuOdE9JClJ9vBqsp6DQg==", "signatures": [{"sig": "MEUCIQCMMeeoeSttbh6ZH2lhxfDiXHucGwJ2BFo3uNRBiLzotQIgVG5rJoTItLCUGwTmTud51LM8eQ8DYX8rnsMBRiRCrMI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77757, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf3R9fCRA9TVsSAnZWagAAww0P/i+CvkWyLfHPg6bv1Axx\n5F+W0iMW1SpzNholHgmtQrrYprbrNW3FOqvtuFs3wWxzn4W3ObVJlvx/6w0b\nCSxN0bmw9a4+Jxu3SBTr0Ie9KS80gL//M3/5gd/CHRI+HKuZ2rWbBGgaQxCR\nKRJS7Boz2f8cwx4kvw+bX2ueu7E6Ed3U75Ph4H04v7zbWILNaTthJuPZUKd8\n+ydrQnf9yApjIffXVDdzjGZcOP4a3mbC3QkqAEDBtm79UY0WiIs3OX7A0b5l\nLbry1wnUKbLAD6Kxp3jUDi2aCPofMI5t8CxB+I4ZhrUu18dGe3gjY7MRIYk/\n70EKI3DUrQeJ+hHOqzzyrc7B1cULYSTgBColFKUU0B6LyNCntpBP8Vk0gYbG\nk0w0l2NGE/xIHYiYQbpf+uMHPEYd/S4t0ZfSzlyIHpBRmAyQ4gv0LMEiI9HX\nLpxo8jVNqxHhi5VAkwg9AwDcT+SEAiYpilQ09EZToG9ZvezR1mRZ4fF1dMIu\n0zI9CTFuSSi3FhEjkYzzenXIDfMr7PBr1PadhDzCcmPquehZY7QFE3mflhmU\nXHfrSFeYXPPEnspUwmGtDzaI9ownrWSr8A0uCQbqHdrFyl2jivLaA+H7bn7g\n4LvZkajKA9F99g/1LsjTzJq9JHbmSYc487ts7qxRge3rLm1fdj5UU5BwQ+g0\nmgTX\r\n=7hoQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/index", "browser": {"./src/text-decoder.js": "./src/text-decoder-browser.js", "./src/stringify-stream.js": "./src/stringify-stream-browser.js"}, "engines": {"node": ">=10.0.0"}, "gitHead": "ae99132df4c62c0febe4839c0b1648cbef99a2ab", "scripts": {"lint": "eslint src test", "test": "mocha --reporter progress", "build": "rollup --config", "travis": "nyc npm run lint-and-test && npm run coveralls", "coverage": "nyc npm test", "test:all": "npm run test:src && npm run test:dist", "test:src": "npm test", "coveralls": "nyc report --reporter=text-lcov | coveralls", "test:dist": "MODE=dist npm test && MODE=dist-min npm test", "lint-and-test": "npm run lint && npm test", "build-and-test": "npm run build && npm run test:dist", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/discoveryjs/json-ext.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "A set of utilities that extend the use of JSON", "directories": {}, "_nodeVersion": "12.20.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "chalk": "^4.1.0", "mocha": "^8.1.1", "eslint": "^7.6.0", "rollup": "^2.28.2", "coveralls": "^3.1.0", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-commonjs": "^15.1.0", "@rollup/plugin-node-resolve": "^9.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/json-ext_0.5.1_1608327007078_0.11517650278064306", "host": "s3://npm-registry-packages"}}, "0.5.2": {"name": "@discoveryjs/json-ext", "version": "0.5.2", "keywords": ["json", "utils", "stream", "async", "promise", "stringify", "info"], "author": {"url": "https://github.com/lahmatiy", "name": "<PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}, "license": "MIT", "_id": "@discoveryjs/json-ext@0.5.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "exdis", "email": "<EMAIL>"}], "homepage": "https://github.com/discoveryjs/json-ext#readme", "bugs": {"url": "https://github.com/discoveryjs/json-ext/issues"}, "dist": {"shasum": "8f03a22a04de437254e8ce8cc84ba39689288752", "tarball": "https://registry.npmjs.org/@discoveryjs/json-ext/-/json-ext-0.5.2.tgz", "fileCount": 14, "integrity": "sha512-HyYEUDeIj5rRQU2Hk5HTB2uHsbRQpF70nvMhVzi+VJR0X+xNEhjPui4/kBf3VeH/wqD28PT4sVOm8qqLjBrSZg==", "signatures": [{"sig": "MEQCIDG+Vb6kc5EJZUuBDvjyAR493GdoiqPUBeKNsDDUjqQ1AiAbTfRH6Y+hXDxRdzN7AliuKgBw/8+5aztXIhfpB2LXWw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78721, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf50tqCRA9TVsSAnZWagAAXi8P/ibeFAQdGjiYWcqqkBs+\n2dQHlAsDN63fDkc5zus5J8zF3YvRgQ/FhREzjwcMsDeETwuN4rmK1oV8rtUY\nf1g6gCgzdTWYGU34n5QjxUC3+p6NRlfN9PVAkk5qiIxYbOHQ6sP4joaPXE2C\nOEAb5IeHMAQ8hUsgmwHiANzvu1jVt9s32dmSQZ76g02g8M23FUTU6QbWdt0T\nCXlK8ANqwjnPpgfrbu3SxPQmrwp4iZv9I6xrvVbmEWGRxLFcvDx8uo/369k8\n2H0ec7l0my5MxAHRJR+2BzI8jP1fesqlVIxjioN9vkOHYqVv1x0eQ4aaPO+y\nsFAHLyWSVsXwdZI1an2g6l54zYwnuz7NEBQR52V4fFNn0DxM/Bxo0nGQo5Cm\nGNiaj/ppY1FCNlHJ2XMVVEcuKlZljr47IxpAQ3/tNBnMwcXLuH5hzR3scR7D\nryf3nj507r0NxgLkmtplamc3NaUXyFfGldbmlwC++DyJWZc6zKpXMxBHjFLt\nTEghKCxuqjk9VXKsICkQ0p6BcUF8VIHCxqHpfdslgwXPRCy8msuyq79lDu50\nsdTB3gV5h6SPCOTAqeah0Gp0TcSI3xJnHHPkJO7Zupgz/CnpJ9/Oa9JRTlkJ\n1jxhYzeIRQnsEFt/jynRHS7LnT0Rzkwe9BfcfQxswqRNEV3WmjA4cs5XRqdL\nBzUS\r\n=jmMa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/index", "browser": {"./src/text-decoder.js": "./src/text-decoder-browser.js", "./src/stringify-stream.js": "./src/stringify-stream-browser.js"}, "engines": {"node": ">=10.0.0"}, "gitHead": "26a08f0fa6a52020bfe381ccff70aa6d3e28fe47", "scripts": {"lint": "eslint src test", "test": "mocha --reporter progress", "build": "rollup --config", "travis": "nyc npm run lint-and-test && npm run coveralls", "coverage": "nyc npm test", "test:all": "npm run test:src && npm run test:dist", "test:src": "npm test", "coveralls": "nyc report --reporter=text-lcov | coveralls", "test:dist": "MODE=dist npm test && MODE=dist-min npm test", "lint-and-test": "npm run lint && npm test", "build-and-test": "npm run build && npm run test:dist", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/discoveryjs/json-ext.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "A set of utilities that extend the use of JSON", "directories": {}, "_nodeVersion": "12.20.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "chalk": "^4.1.0", "mocha": "^8.1.1", "eslint": "^7.6.0", "rollup": "^2.28.2", "coveralls": "^3.1.0", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-commonjs": "^15.1.0", "@rollup/plugin-node-resolve": "^9.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/json-ext_0.5.2_1608993642423_0.008773222405023429", "host": "s3://npm-registry-packages"}}, "0.5.3": {"name": "@discoveryjs/json-ext", "version": "0.5.3", "keywords": ["json", "utils", "stream", "async", "promise", "stringify", "info"], "author": {"url": "https://github.com/lahmatiy", "name": "<PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}, "license": "MIT", "_id": "@discoveryjs/json-ext@0.5.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "exdis", "email": "<EMAIL>"}], "homepage": "https://github.com/discoveryjs/json-ext#readme", "bugs": {"url": "https://github.com/discoveryjs/json-ext/issues"}, "dist": {"shasum": "90420f9f9c6d3987f176a19a7d8e764271a2f55d", "tarball": "https://registry.npmjs.org/@discoveryjs/json-ext/-/json-ext-0.5.3.tgz", "fileCount": 14, "integrity": "sha512-Fxt+AfXgjMoin2maPIYzFZnQjAXjAL0PHscM5pRTtatFqB+vZxAM9tLp2Optnuw3QOQC40jTNeGYFOMvyf7v9g==", "signatures": [{"sig": "MEYCIQCQM5idK4xqsRJrJj2cU/IBocPRe5wgjt5sw1z+YM2+SgIhANUr2+AY2tQSqqH408G41AMaBaZZfUHA5G6vE739euUG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83398, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgnZgfCRA9TVsSAnZWagAAcv8P/0shVx889KYcO9apy14q\nbAtBuYEuxcQ8xXqKXqp7KPiwBDdb/TyemElKzMGsXoLO3lTeR0TJqZSfFHUd\nib3aSzzzgLAfPxViMprnbVSzMo2UEkHP1CSwR5NHje8Fb1d3abaXGfXQPBYk\nHA/aV1DLv/yGS5mytAV3JCE1+ehlLyKFOweLNO+Z9fwtL1njwI6D/vt/9zP6\nIBbdtQrHHkVOIJ0EYf+NPjNf/5pdtu7tnIo9kWp9gkw5vkgcQ0TnptXZ88dx\nRocTy23Q414tqxlFxyGesU91dmGYc3Fyyfy+VPVRYUBCbBnkgU6xJZU6+oIS\nImif+tKIlv/eHoBxFX5NvHQdgfP49NHxOYDygn83m0NUbTUmyMhKYJdW4uYF\nMiv2rtXXQjXI5Eo1svtNFSBdbQAGSO/7x1vuhv9got0yoq5RtwuEmWYueGe7\nNI2+y6+hlud5Y40nyFpzSJ5/fn+RywYSqISpxInvANmHOqjdu0u+TX/nkzWK\nMTG2a154EvmlvGS9z6h3AQQnd95lU2Hl5MGLoXf3x2g4ju90/jXVbWWDEJUu\nk4PT4fYKlEYCwgPNzfgfemn4Mkuuv1KZcaaqy/a41kyAvufqKztkGTESX+47\n4T9cqjuP6QxWIGqDjVgQ8jY+xaPoe7caaSIYAQPG5x8oz7DhNHfVoXvXR7ni\nQ9BW\r\n=66lR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/index", "browser": {"./src/text-decoder.js": "./src/text-decoder-browser.js", "./src/stringify-stream.js": "./src/stringify-stream-browser.js"}, "engines": {"node": ">=10.0.0"}, "gitHead": "705ff9155152f82a460e5db07edfbaec0efcee87", "scripts": {"lint": "eslint src test", "test": "mocha --reporter progress", "build": "rollup --config", "travis": "nyc npm run lint-and-test && npm run build-and-test && npm run coveralls", "coverage": "nyc npm test", "test:all": "npm run test:src && npm run test:dist", "test:src": "npm test", "coveralls": "nyc report --reporter=text-lcov | coveralls", "test:dist": "cross-env MODE=dist npm test && cross-env MODE=dist-min npm test", "lint-and-test": "npm run lint && npm test", "build-and-test": "npm run build && npm run test:dist", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/discoveryjs/json-ext.git", "type": "git"}, "_npmVersion": "6.14.13", "description": "A set of utilities that extend the use of JSON", "directories": {}, "_nodeVersion": "16.1.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "chalk": "^4.1.0", "mocha": "^8.1.1", "eslint": "^7.6.0", "rollup": "^2.28.2", "coveralls": "^3.1.0", "cross-env": "^7.0.3", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-commonjs": "^15.1.0", "@rollup/plugin-node-resolve": "^9.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/json-ext_0.5.3_1620940831000_0.318175053419018", "host": "s3://npm-registry-packages"}}, "0.5.4": {"name": "@discoveryjs/json-ext", "version": "0.5.4", "keywords": ["json", "utils", "stream", "async", "promise", "stringify", "info"], "author": {"url": "https://github.com/lahmatiy", "name": "<PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}, "license": "MIT", "_id": "@discoveryjs/json-ext@0.5.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "exdis", "email": "<EMAIL>"}], "homepage": "https://github.com/discoveryjs/json-ext#readme", "bugs": {"url": "https://github.com/discoveryjs/json-ext/issues"}, "dist": {"shasum": "d8f0f394dec7be40b5113cd78088339c42ee811f", "tarball": "https://registry.npmjs.org/@discoveryjs/json-ext/-/json-ext-0.5.4.tgz", "fileCount": 14, "integrity": "sha512-8szzeplTi6qw+dbzBB/I3T5TzU9GM7AxzQkiiWdakKsCnaMlMLUMElfEhqT1S3PfdSjHBwzPUTcfWyFHdf9FaQ==", "signatures": [{"sig": "MEUCIQDCYUSSM3XZpJzpgZP+KSF2yDLzGBX3B3HHWSRX4nulLQIgBdc0cYos/zoBy6cciHPOCxmDKzd6X4c//62Q5799lVs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83543, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQLPwCRA9TVsSAnZWagAAN/sQAI8rpdZ2Dol+wSnLD1ZT\n7VmBqELatqZ7nozDCbE+zLQ1As0Ru/AkggYUXP0DRAgdXdc8koKOlXL0PvRq\nhKxj4Ir4764WwfQq2QwQtM9B/Dl3uXIKIm+gzrKllj+5zaBBo1uZW33vLqmG\nQrdsGElfSKWOVEIixxfSmceMjucZE7/KT+iR+0jg9OgK5dUcJ4KOuckQMnGo\nV/CVslTTGNc003C8GXuhDDVG8TY7GkXwO2LISatVKWzw0LydmVgYw8TWJ4HP\n4MhmaBxn6wHk9QRDrCnagMW7Rr8BAEA0UBQx3wQxizxyEHCfXKOFzhs5qZT9\nbSXvQpBPZ3LlI1xXnlC1FtpRRSXrvcGsdEtkQ/1vZnDOcCczsGRI2P9oVh8H\naY8bc2cuz/ETir5NTC7JXIniLFIG69IHiQZkjYpmbQvMaHiVBk9/r3VXHqWS\n28WR5B/U3qkEzZVXqnoymSbkuPnuIDbupX4rHQuvIgU9Z+axte2rOVhjOv32\ngQM4jpRWaZd9eTaei6Wvv1w9U4L783ueUbh3XObLMvg7U56Ki4yzeu2MkINZ\n7dpDM9TSHwWq31m55/NRRhnqRY2KZ7NdwHHz94Sd4JL/edgM/e/AF6ckUPn8\n+b2IVQ2a7iOb1Bxe3ofIKc2gIZG6A+C3aMnmKaYKtrqr0UUga68INT6sdUF5\nxg8E\r\n=/gKT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/index", "types": "./index.d.ts", "browser": {"./src/text-decoder.js": "./src/text-decoder-browser.js", "./src/stringify-stream.js": "./src/stringify-stream-browser.js"}, "engines": {"node": ">=10.0.0"}, "gitHead": "fb77e4626aac7b58aca6a15f6ba876ce1c7ae88e", "scripts": {"lint": "eslint src test", "test": "mocha --reporter progress", "build": "rollup --config", "travis": "nyc npm run lint-and-test && npm run build-and-test && npm run coveralls", "coverage": "nyc npm test", "test:all": "npm run test:src && npm run test:dist", "test:src": "npm test", "coveralls": "nyc report --reporter=text-lcov | coveralls", "test:dist": "cross-env MODE=dist npm test && cross-env MODE=dist-min npm test", "lint-and-test": "npm run lint && npm test", "build-and-test": "npm run build && npm run test:dist", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/discoveryjs/json-ext.git", "type": "git"}, "_npmVersion": "6.14.13", "description": "A set of utilities that extend the use of JSON", "directories": {}, "_nodeVersion": "12.22.3", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "chalk": "^4.1.0", "mocha": "^8.1.1", "eslint": "^7.6.0", "rollup": "^2.28.2", "coveralls": "^3.1.0", "cross-env": "^7.0.3", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-commonjs": "^15.1.0", "@rollup/plugin-node-resolve": "^9.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/json-ext_0.5.4_1631630320547_0.7234853518014437", "host": "s3://npm-registry-packages"}}, "0.5.5": {"name": "@discoveryjs/json-ext", "version": "0.5.5", "keywords": ["json", "utils", "stream", "async", "promise", "stringify", "info"], "author": {"url": "https://github.com/lahmatiy", "name": "<PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}, "license": "MIT", "_id": "@discoveryjs/json-ext@0.5.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "exdis", "email": "<EMAIL>"}], "homepage": "https://github.com/discoveryjs/json-ext#readme", "bugs": {"url": "https://github.com/discoveryjs/json-ext/issues"}, "dist": {"shasum": "9283c9ce5b289a3c4f61c12757469e59377f81f3", "tarball": "https://registry.npmjs.org/@discoveryjs/json-ext/-/json-ext-0.5.5.tgz", "fileCount": 15, "integrity": "sha512-6nFkfkmSeV/rqSaS4oWHgmpnYw194f6hmWF5is6b0J1naJZoiD0NTc9AiUwPHvWsowkjuHErCZT1wa0jg+BLIA==", "signatures": [{"sig": "MEUCIEpw2p05KxO7Tg2ot4LStfNrJEgn8Hj5n3pZtaxBiDMNAiEAgle2HopcxbeIAfOlq12oLrO23HACkIcC/aX/dvlziNk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84584, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQS+sCRA9TVsSAnZWagAAoeMP/j+tNUKXy20XWW4Hg0oU\nTAmdRL5vCU4A0jShbIV45j7GLt3tO8JKPgS1jMzIZEVL2W0gyoE8CVITrOqG\nC0IdQSFGH49QZl4xEi7gfC8xY4QwaHGN0ySrnYJ+BwT3eI+BvQBeaMceL0NA\nqVKT6to+4gT3vH+tDuIU+H9KLIG5aMpN1VfTj+J/SICOeNOw+wszHPRwKpmH\nzmfxGCYLyXX5lyOyTm6M4M2Iz6oXx0UAoeJSy9tPILbacfaFkLSr3qGHoRDh\nAQxbcfITA1luqiBiFbcLeblcsoOFEF7toWJ2lP0BXODTuIv9uVYulHLbRi0L\nGY0810C/5muc/uh4jAa4EDQptZYQuZyWgsTKgP7NzI662/K1IRdqCJpq6sI0\nkyVDTwmjVSoyoZruyqQTmqSrUSPC9QMIx1edsSTGHd0TYdW3LOALpeTpBPdA\nMj0Fi04UbwN8qKH716MFdIxmuwJubcGpNRs92baME91ZGCgheOqFwgvNhDSf\nCWzk0V7omZXsEOoMKm2WpDdGOLIWcnPijV4AqJTthfpBA3LflOHRF4b6Znz4\nFwxs2gNtVze8tl3AZXc7EHySWjKhQop5sLvvlmQ/DexJZYjp4cgJKethCaIu\nfiUpAUkIfV7xJ/R9oAcOHZfuG6Sw5GW3/O782NAS07Ku52RdzCRDExKohKZM\n3VyR\r\n=82DZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/index", "types": "./index.d.ts", "browser": {"./src/text-decoder.js": "./src/text-decoder-browser.js", "./src/stringify-stream.js": "./src/stringify-stream-browser.js"}, "engines": {"node": ">=10.0.0"}, "gitHead": "6ccfaf5b6d95ab74a5502ec5f8730c11f480b374", "scripts": {"lint": "eslint src test", "test": "mocha --reporter progress", "build": "rollup --config", "travis": "nyc npm run lint-and-test && npm run build-and-test && npm run coveralls", "coverage": "nyc npm test", "test:all": "npm run test:src && npm run test:dist", "test:src": "npm test", "coveralls": "nyc report --reporter=text-lcov | coveralls", "test:dist": "cross-env MODE=dist npm test && cross-env MODE=dist-min npm test", "lint-and-test": "npm run lint && npm test", "build-and-test": "npm run build && npm run test:dist", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/discoveryjs/json-ext.git", "type": "git"}, "_npmVersion": "6.14.13", "description": "A set of utilities that extend the use of JSON", "directories": {}, "_nodeVersion": "12.22.3", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "chalk": "^4.1.0", "mocha": "^8.1.1", "eslint": "^7.6.0", "rollup": "^2.28.2", "coveralls": "^3.1.0", "cross-env": "^7.0.3", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-commonjs": "^15.1.0", "@rollup/plugin-node-resolve": "^9.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/json-ext_0.5.5_1631661996142_0.6007107464481163", "host": "s3://npm-registry-packages"}}, "0.5.6": {"name": "@discoveryjs/json-ext", "version": "0.5.6", "keywords": ["json", "utils", "stream", "async", "promise", "stringify", "info"], "author": {"url": "https://github.com/lahmatiy", "name": "<PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}, "license": "MIT", "_id": "@discoveryjs/json-ext@0.5.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "exdis", "email": "<EMAIL>"}], "homepage": "https://github.com/discoveryjs/json-ext#readme", "bugs": {"url": "https://github.com/discoveryjs/json-ext/issues"}, "dist": {"shasum": "d5e0706cf8c6acd8c6032f8d54070af261bbbb2f", "tarball": "https://registry.npmjs.org/@discoveryjs/json-ext/-/json-ext-0.5.6.tgz", "fileCount": 14, "integrity": "sha512-ws57AidsDvREKrZKYffXddNkyaF14iHNHm8VQnZH6t99E8gczjNN0GpvcGny0imC80yQ0tHz1xVUKk/KFQSUyA==", "signatures": [{"sig": "MEUCIQD2XuT09XthvJPHi9nJJbYKTyXf5Q6yYH8MtSnMEpLfdwIgMxCkZB7QQwCfDgbHJ/neClOkZcqz1SxHKr5xemopOGM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83294, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpp+8CRA9TVsSAnZWagAAtuIP/3tnkcjXUk3JlQamZa8H\n9wmm3UWBCyBydbMZNAZqv6rfTmXF/DIqz1b3Hs1Ll0Laf8X+NmBqAeZmlmzs\n5udSilWBUkSl9t7XaDeaFRdpSyKj1cl4vbkWcOqawqJ8J4t9x8/VxgAEUwH7\nBbMY7pkzRfHmY4c3A0I+pjwvhOgN1LK4I7yebx6G2PuWMm0i/e+C2k5jFmCq\nQrgtS955h0cwdEpG17EIC0QsXbfn1mPPozUKmOqmniXQFRY+UfoXtBBY3GeQ\nScorPtDkjM+TCwFPa+37fUIT3Ml5NVHUgu2OtOMqBpOO17oLd9r05jlu5hhI\nQhgnwcjZ4P3Bi5i6WPXqm6KdTJ1HdADw57+SpN301nnBu2+hFPROwguFNkQi\nag0UGOYpVioVZotLH4NdTYXxmrUzUKuo8t0aA5N4dhmG8b1v45zhDr1qYHXU\nymrmn4o0CFTrQOHxzuspJDnrvVwgQ+J3tFnAFHrClNYARCzyGnWdopqCxNPY\nJkj4EDo6B9kYFOnq5atb6UwV/NqLgSzZDr/rR3QjSVChYenQbTC245bsksqL\nrAGIqmaT2c72WtavCbz3QNetaxpgGsZKpEuNjrUpgeRzeQsMvg6CY4fy1PMw\n+30IjhyOSxjmPyZjndlYVGrWEBzTzNpNdaZWEcMmclERYFuljDYLpbsQR3Mq\nqFmk\r\n=lnm8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/index", "types": "./index.d.ts", "browser": {"./src/text-decoder.js": "./src/text-decoder-browser.js", "./src/stringify-stream.js": "./src/stringify-stream-browser.js"}, "engines": {"node": ">=10.0.0"}, "gitHead": "baf1f9567e098d7d03fc0f07b335560e53aa166a", "scripts": {"lint": "eslint src test", "test": "mocha --reporter progress", "build": "rollup --config", "travis": "nyc npm run lint-and-test && npm run build-and-test && npm run coveralls", "coverage": "nyc npm test", "test:all": "npm run test:src && npm run test:dist", "test:src": "npm test", "coveralls": "nyc report --reporter=text-lcov | coveralls", "test:dist": "cross-env MODE=dist npm test && cross-env MODE=dist-min npm test", "lint-and-test": "npm run lint && npm test", "build-and-test": "npm run build && npm run test:dist", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/discoveryjs/json-ext.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A set of utilities that extend the use of JSON", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "chalk": "^4.1.0", "mocha": "^8.1.1", "eslint": "^7.6.0", "rollup": "^2.28.2", "coveralls": "^3.1.0", "cross-env": "^7.0.3", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-commonjs": "^15.1.0", "@rollup/plugin-node-resolve": "^9.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/json-ext_0.5.6_1638309820062_0.5492637512489906", "host": "s3://npm-registry-packages"}}, "0.5.7": {"name": "@discoveryjs/json-ext", "version": "0.5.7", "keywords": ["json", "utils", "stream", "async", "promise", "stringify", "info"], "author": {"url": "https://github.com/lahmatiy", "name": "<PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}, "license": "MIT", "_id": "@discoveryjs/json-ext@0.5.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "exdis", "email": "<EMAIL>"}], "homepage": "https://github.com/discoveryjs/json-ext#readme", "bugs": {"url": "https://github.com/discoveryjs/json-ext/issues"}, "dist": {"shasum": "1d572bfbbe14b7704e0ba0f39b74815b84870d70", "tarball": "https://registry.npmjs.org/@discoveryjs/json-ext/-/json-ext-0.5.7.tgz", "fileCount": 16, "integrity": "sha512-dBVuXR082gk3jsFp7Rd/JI4kytwGHecnCoTtXFb7DB6CNHp4rg5k1bhg0nWdLGLnOV71lmDzGQaLMy8iPLY0pw==", "signatures": [{"sig": "MEYCIQCMRh+QfcpLEvtci2j1eZXmUATBfCPbBCeZ1QdruQafIAIhAPAAJmIblhWs/z6xwqlinepC+8c+F8tIndCTJL7taJCq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81111, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiKOPIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo8tQ//chYdflfnb6VX6LE0vffZe+yWqCtDGVUuEx3qSej4DQgK8deR\r\n0k+6U+8sIvLBkv9MWZ4wuloD/zSi9ZtwKhV/WMQyMxHQiR0K9RC6ni7MidB1\r\nW+Dxy9qXkkCLU8PS/UY7MZ9QhP/pdkPdVPsVzW++AfDYpE+W4GR/JyYUZujY\r\nz5UQTfptOvUZ9SKq5E+bMWlQQXrPsUKkbDnt9PckFb+FKCMklPE/9BW/6u19\r\nzFMtpIdmQVZAG6X8QCoWlzEKLxcVhgtMytfoWp/DTLDtQh3A81W/GJi5izvC\r\nKtDdilQJm+4E/+oqlLrNPm14BluCs3uRk22Cn/0C2R6a0Y32rYPOIU/WMV89\r\n2t0OPgMUuIyQtOL4uJ34yqUNxBekiEJSGmtDkJU8xOyCGDPDpMNNYIGjudXZ\r\nKKTikWoFbzCkpmD4iZFQiBqnOehZJOehOj8MSK4Td9pRl5I5FDiX/cIoT97N\r\n/ndkbvskuAvGPYCH9/D9caqaBiWIWgvTNfUe73NeskJYERdiaT4/1ZxcuPZK\r\nhgJ5dkZOYC+6LLYSKhJjbvqSSGszumxGaWuGuxVxzL0X19hs/aQDDOe/uFEl\r\nKSNYOUMi+lSZ6NGjiVgkp2Au/ZMLh/REm4yYJoSZnx+ImdvBGV9j9sBzKzfg\r\nN3r/ILAbAVjAz2FbHtwKjLIdcRozCEC3Fsg=\r\n=eWf+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/index", "types": "./index.d.ts", "browser": {"./src/version.js": "./dist/version.js", "./src/text-decoder.js": "./src/text-decoder-browser.js", "./src/stringify-stream.js": "./src/stringify-stream-browser.js"}, "engines": {"node": ">=10.0.0"}, "gitHead": "38b72d411627b57ab8c91f60dc6ae6205fbfcaca", "scripts": {"lint": "eslint src test", "test": "mocha --reporter progress", "build": "rollup --config", "coverage": "c8 --reporter=lcovonly npm test", "test:all": "npm run test:src && npm run test:dist", "test:src": "npm test", "test:dist": "cross-env MODE=dist npm test && cross-env MODE=dist-min npm test", "lint-and-test": "npm run lint && npm test", "build-and-test": "npm run build && npm run test:dist", "prepublishOnly": "npm run lint && npm test && npm run build-and-test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/discoveryjs/json-ext.git", "type": "git"}, "_npmVersion": "8.3.1", "description": "A set of utilities that extend the use of JSON", "directories": {}, "_nodeVersion": "16.14.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.10.0", "chalk": "^4.1.0", "mocha": "^8.4.0", "eslint": "^8.10.0", "rollup": "^2.28.2", "cross-env": "^7.0.3", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-commonjs": "^15.1.0", "@rollup/plugin-node-resolve": "^9.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/json-ext_0.5.7_1646846919966_0.587183615906542", "host": "s3://npm-registry-packages"}}, "0.6.0": {"name": "@discoveryjs/json-ext", "version": "0.6.0", "keywords": ["json", "utils", "stream", "async", "promise", "stringify", "info"], "author": {"url": "https://github.com/lahmatiy", "name": "<PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}, "license": "MIT", "_id": "@discoveryjs/json-ext@0.6.0", "homepage": "https://github.com/discoveryjs/json-ext#readme", "bugs": {"url": "https://github.com/discoveryjs/json-ext/issues"}, "dist": {"shasum": "323395f46f8c9a10107be60574c0f8ff8d802220", "tarball": "https://registry.npmjs.org/@discoveryjs/json-ext/-/json-ext-0.6.0.tgz", "fileCount": 20, "integrity": "sha512-ggk8A6Y4RxpOPaCER/yl1sYtcZ1JfFBdHR6it/e2IolmV3VXSaFov2hqmWUdh9dXgpMprSg3xUvkGJDfR5sc/w==", "signatures": [{"sig": "MEUCIDwX3ecPaUWJN7EqxGCv6c4JdfYi+/C7aei0yxLL6z4nAiEA8yT7waUVLt7hejPkqbYu8rCcGFw93AXRROkCV+yZrD8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 142960}, "main": "./cjs/index.cjs", "type": "module", "types": "./index.d.ts", "module": "./src/index.js", "engines": {"node": ">=14.17.0"}, "exports": {".": {"types": "./index.d.ts", "import": "./src/index.js", "require": "./cjs/index.cjs"}, "./dist/*": "./dist/*", "./package.json": "./package.json"}, "gitHead": "690323d3a73f88dd1842f6922c8fb337650e92d6", "scripts": {"lint": "eslint src", "test": "npm run test:src", "start": "node server.js", "bundle": "node scripts/bundle.js", "coverage": "c8 --reporter=lcovonly npm test", "test:all": "npm run test:src && npm run test:cjs && npm run test:dist && npm run test:e2e", "test:cjs": "mocha --reporter progress cjs/*.test.cjs", "test:e2e": "mocha --reporter progress test-e2e", "test:src": "mocha --reporter progress src/*.test.js", "test:deno": "node scripts/deno-adapt-test.js && mocha --reporter progress deno-tests/*.test.js", "test:dist": "mocha --reporter progress dist/test", "transpile": "node scripts/transpile.cjs", "lint-and-test": "npm run lint && npm test", "prepublishOnly": "npm run lint && npm run bundle && npm run transpile && npm run test:all", "bundle-and-test": "npm run bundle && npm run test:dist"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/discoveryjs/json-ext.git", "type": "git"}, "_npmVersion": "10.8.1", "description": "A set of utilities that extend the use of JSON", "directories": {}, "_nodeVersion": "22.3.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.10.0", "chalk": "^4.1.0", "mocha": "^9.2.2", "eslint": "^8.57.0", "rollup": "^2.67.3", "esbuild": "^0.21.5"}, "_npmOperationalInternal": {"tmp": "tmp/json-ext_0.6.0_1719923584799_0.3165879186246028", "host": "s3://npm-registry-packages"}}, "0.6.1": {"name": "@discoveryjs/json-ext", "version": "0.6.1", "keywords": ["json", "utils", "stream", "async", "promise", "stringify", "info"], "author": {"url": "https://github.com/lahmatiy", "name": "<PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}, "license": "MIT", "_id": "@discoveryjs/json-ext@0.6.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "exdis", "email": "<EMAIL>"}], "homepage": "https://github.com/discoveryjs/json-ext#readme", "bugs": {"url": "https://github.com/discoveryjs/json-ext/issues"}, "dist": {"shasum": "593da7a17a31a72a874e313677183334a49b01c9", "tarball": "https://registry.npmjs.org/@discoveryjs/json-ext/-/json-ext-0.6.1.tgz", "fileCount": 20, "integrity": "sha512-boghen8F0Q8D+0/Q1/1r6DUEieUJ8w2a1gIknExMSHBsJFOr2+0KUfHiVYBvucPwl3+RU5PFBK833FjFCh3BhA==", "signatures": [{"sig": "MEUCICnwFSVTXzPpvxPSzOboGEZRyT6G1lwUKkSyyMeDNJT7AiEA6K3adZoPta18cGbltYm3yhx3LduTiH4qez6nGqqCv58=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 146004}, "main": "./cjs/index.cjs", "type": "module", "types": "./index.d.ts", "module": "./src/index.js", "engines": {"node": ">=14.17.0"}, "exports": {".": {"types": "./index.d.ts", "import": "./src/index.js", "require": "./cjs/index.cjs"}, "./dist/*": "./dist/*", "./package.json": "./package.json"}, "gitHead": "104ad9cdd49d4c68e31a843284d472cac03972ec", "scripts": {"lint": "eslint src", "test": "npm run test:src", "bundle": "node scripts/bundle.js", "coverage": "c8 --reporter=lcovonly npm test", "test:all": "npm run test:src && npm run test:cjs && npm run test:dist && npm run test:e2e", "test:cjs": "mocha --reporter progress cjs/*.test.cjs", "test:e2e": "mocha --reporter progress test-e2e", "test:src": "mocha --reporter progress src/*.test.js", "test:deno": "node scripts/deno-adapt-test.js && mocha --reporter progress deno-tests/*.test.js", "test:dist": "mocha --reporter progress dist/test", "transpile": "node scripts/transpile.cjs", "lint-and-test": "npm run lint && npm test", "prepublishOnly": "npm run lint && npm run bundle && npm run transpile && npm run test:all", "bundle-and-test": "npm run bundle && npm run test:dist"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/discoveryjs/json-ext.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A set of utilities that extend the use of JSON", "directories": {}, "_nodeVersion": "22.5.1", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.10.0", "chalk": "^4.1.0", "mocha": "^9.2.2", "eslint": "^8.57.0", "rollup": "^2.67.3", "esbuild": "^0.21.5"}, "_npmOperationalInternal": {"tmp": "tmp/json-ext_0.6.1_1722983302479_0.007517055313263521", "host": "s3://npm-registry-packages"}}, "0.6.2": {"name": "@discoveryjs/json-ext", "version": "0.6.2", "keywords": ["json", "utils", "stream", "async", "promise", "stringify", "info"], "author": {"url": "https://github.com/lahmatiy", "name": "<PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}, "license": "MIT", "_id": "@discoveryjs/json-ext@0.6.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "exdis", "email": "<EMAIL>"}], "homepage": "https://github.com/discoveryjs/json-ext#readme", "bugs": {"url": "https://github.com/discoveryjs/json-ext/issues"}, "dist": {"shasum": "b3bd3373bca66496ad62f2aff992d070e861d79b", "tarball": "https://registry.npmjs.org/@discoveryjs/json-ext/-/json-ext-0.6.2.tgz", "fileCount": 20, "integrity": "sha512-GHZT40sAqBY7qdKaD7XtaohbX00VDfWjX7A6d0c/dc9bR/2h5I51cVh+TbNKCytBkfV+L+n0bR7OZWNt5r4/CQ==", "signatures": [{"sig": "MEUCIB9Lxro1xU/qxqTKeX5iiawdn1/5HYyBthDYs/mKM1z7AiEApHH0O+wxslb9/YR3S801ZSbLeADyEvyuCG6sdK4juc0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 147958}, "main": "./cjs/index.cjs", "type": "module", "types": "./index.d.ts", "module": "./src/index.js", "engines": {"node": ">=14.17.0"}, "exports": {".": {"import": "./src/index.js", "require": "./cjs/index.cjs"}, "./dist/*": "./dist/*", "./package.json": "./package.json"}, "gitHead": "6614e754520c2ab73ad3904f30311f04ad4bf7b9", "scripts": {"lint": "eslint src", "test": "npm run test:src", "bundle": "node scripts/bundle.js", "coverage": "c8 --reporter=lcovonly npm test", "test:all": "npm run test:src && npm run test:cjs && npm run test:dist && npm run test:e2e", "test:cjs": "mocha --reporter progress cjs/*.test.cjs", "test:e2e": "mocha --reporter progress test-e2e", "test:src": "mocha --reporter progress src/*.test.js", "test:deno": "node scripts/deno-adapt-test.js && mocha --reporter progress deno-tests/*.test.js", "test:dist": "mocha --reporter progress dist/test", "transpile": "node scripts/transpile.cjs", "lint-and-test": "npm run lint && npm test", "prepublishOnly": "npm run lint && npm run bundle && npm run transpile && npm run test:all", "bundle-and-test": "npm run bundle && npm run test:dist"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/discoveryjs/json-ext.git", "type": "git"}, "_npmVersion": "10.8.3", "description": "A set of utilities that extend the use of JSON", "directories": {}, "_nodeVersion": "22.9.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.10.0", "chalk": "^4.1.0", "mocha": "^9.2.2", "eslint": "^8.57.0", "rollup": "^2.79.2", "esbuild": "^0.24.0"}, "_npmOperationalInternal": {"tmp": "tmp/json-ext_0.6.2_1729216672527_0.22147748963376368", "host": "s3://npm-registry-packages"}}, "0.6.3": {"name": "@discoveryjs/json-ext", "version": "0.6.3", "description": "A set of utilities that extend the use of JSON", "keywords": ["json", "utils", "stream", "async", "promise", "stringify", "info"], "author": {"name": "<PERSON>", "email": "rd<PERSON><PERSON>@gmail.com", "url": "https://github.com/lahmatiy"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/discoveryjs/json-ext.git"}, "engines": {"node": ">=14.17.0"}, "type": "module", "main": "./cjs/index.cjs", "module": "./src/index.js", "types": "./index.d.ts", "exports": {".": {"types": "./index.d.ts", "require": "./cjs/index.cjs", "import": "./src/index.js"}, "./dist/*": "./dist/*", "./package.json": "./package.json"}, "scripts": {"test": "npm run test:src", "lint": "eslint src", "lint-and-test": "npm run lint && npm test", "bundle": "node scripts/bundle.js", "transpile": "node scripts/transpile.cjs", "test:all": "npm run test:src && npm run test:cjs && npm run test:dist && npm run test:e2e", "test:src": "mocha --reporter progress src/*.test.js", "test:cjs": "mocha --reporter progress cjs/*.test.cjs", "test:e2e": "mocha --reporter progress test-e2e", "test:dist": "mocha --reporter progress dist/test", "test:deno": "node scripts/deno-adapt-test.js && mocha --reporter progress deno-tests/*.test.js", "bundle-and-test": "npm run bundle && npm run test:dist", "coverage": "c8 --reporter=lcovonly npm test", "prepublishOnly": "npm run lint && npm run bundle && npm run transpile && npm run test:all"}, "devDependencies": {"c8": "^7.10.0", "chalk": "^4.1.0", "esbuild": "^0.24.0", "eslint": "^8.57.0", "mocha": "^9.2.2", "rollup": "^2.79.2"}, "_id": "@discoveryjs/json-ext@0.6.3", "gitHead": "570860b81f214321eb1860de00db9866510173f0", "bugs": {"url": "https://github.com/discoveryjs/json-ext/issues"}, "homepage": "https://github.com/discoveryjs/json-ext#readme", "_nodeVersion": "22.9.0", "_npmVersion": "10.8.3", "dist": {"integrity": "sha512-4B4OijXeVNOPZlYA2oEwWOTkzyltLao+xbotHQeqN++Rv27Y6s818+n2Qkp8q+Fxhn0t/5lA5X1Mxktud8eayQ==", "shasum": "f13c7c205915eb91ae54c557f5e92bddd8be0e83", "tarball": "https://registry.npmjs.org/@discoveryjs/json-ext/-/json-ext-0.6.3.tgz", "fileCount": 20, "unpackedSize": 147995, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCSQOOh3e56j6KLZ+rZpw5j5/WRjH+ysunX/NtK3qELwgIga4cwkDWfmRtS+7v6XqVZs7/kSzMNpXY4AuELyCGUtHY="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "exdis", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/json-ext_0.6.3_1729805002499_0.5186237590679321"}, "_hasShrinkwrap": false}}, "time": {"created": "2020-09-08T10:28:09.123Z", "modified": "2024-10-24T21:23:22.958Z", "0.1.0": "2020-09-08T10:28:09.409Z", "0.1.1": "2020-09-08T16:10:13.231Z", "0.2.0": "2020-09-28T12:34:45.967Z", "0.3.0": "2020-09-28T13:18:05.909Z", "0.3.1": "2020-10-26T13:14:38.894Z", "0.3.2": "2020-10-26T13:42:42.490Z", "0.4.0": "2020-12-04T22:51:44.556Z", "0.5.0": "2020-12-05T22:50:28.304Z", "0.5.1": "2020-12-18T21:30:07.215Z", "0.5.2": "2020-12-26T14:40:42.615Z", "0.5.3": "2021-05-13T21:20:31.220Z", "0.5.4": "2021-09-14T14:38:40.682Z", "0.5.5": "2021-09-14T23:26:36.304Z", "0.5.6": "2021-11-30T22:03:40.220Z", "0.5.7": "2022-03-09T17:28:40.118Z", "0.6.0": "2024-07-02T12:33:04.994Z", "0.6.1": "2024-08-06T22:28:22.658Z", "0.6.2": "2024-10-18T01:57:52.743Z", "0.6.3": "2024-10-24T21:23:22.715Z"}, "bugs": {"url": "https://github.com/discoveryjs/json-ext/issues"}, "author": {"name": "<PERSON>", "email": "rd<PERSON><PERSON>@gmail.com", "url": "https://github.com/lahmatiy"}, "license": "MIT", "homepage": "https://github.com/discoveryjs/json-ext#readme", "keywords": ["json", "utils", "stream", "async", "promise", "stringify", "info"], "repository": {"type": "git", "url": "git+https://github.com/discoveryjs/json-ext.git"}, "description": "A set of utilities that extend the use of JSON", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "exdis", "email": "<EMAIL>"}], "readme": "# json-ext\n\n[![NPM version](https://img.shields.io/npm/v/@discoveryjs/json-ext.svg)](https://www.npmjs.com/package/@discoveryjs/json-ext)\n[![Build Status](https://github.com/discoveryjs/json-ext/actions/workflows/ci.yml/badge.svg)](https://github.com/discoveryjs/json-ext/actions/workflows/ci.yml)\n[![Coverage Status](https://coveralls.io/repos/github/discoveryjs/json-ext/badge.svg?branch=master)](https://coveralls.io/github/discoveryjs/json-ext)\n[![NPM Downloads](https://img.shields.io/npm/dm/@discoveryjs/json-ext.svg)](https://www.npmjs.com/package/@discoveryjs/json-ext)\n\nA set of utilities designed to extend JSON's capabilities, especially for handling large JSON data (over 100MB) efficiently:\n\n- [parseChunked()](#parsechunked) – Parses JSON incrementally; similar to [`JSON.parse()`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON/parse), but processing JSON data in chunks.\n- [stringifyChunked()](#stringifychunked) – Converts JavaScript objects to JSON incrementally; similar to [`JSON.stringify()`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON/stringify), but returns a generator that yields JSON strings in parts.\n- [stringifyInfo()](#stringifyinfo) – Estimates the size of the `JSON.stringify()` result and identifies circular references without generating the JSON.\n- [parseFromWebStream()](#parsefromwebstream) – A helper function to parse JSON chunks directly from a Web Stream.\n- [createStringifyWebStream()](#createstringifywebstream) – A helper function to generate JSON data as a Web Stream.\n\n### Key Features\n\n- Optimized to handle large JSON data with minimal resource usage (see [benchmarks](./benchmarks/README.md))\n- Works seamlessly with browsers, Node.js, Deno, and Bun\n- Supports both Node.js and Web streams\n- Available in both ESM and CommonJS\n- TypeScript typings included\n- No external dependencies\n- Compact size: 9.4Kb (minified), 3.8Kb (min+gzip)\n\n### Why json-ext?\n\n- **Handles large JSON files**: Overcomes the limitations of V8 for strings larger than ~500MB, enabling the processing of huge JSON data.\n- **Prevents main thread blocking**: Distributes parsing and stringifying over time, ensuring the main thread remains responsive during heavy JSON operations.\n- **Reduces memory usage**: Traditional `JSON.parse()` and `JSON.stringify()` require loading entire data into memory, leading to high memory consumption and increased garbage collection pressure. `parseChunked()` and `stringifyChunked()` process data incrementally, optimizing memory usage.\n- **Size estimation**: `stringifyInfo()` allows estimating the size of resulting JSON before generating it, enabling better decision-making for JSON generation strategies.\n\n## Install\n\n```bash\nnpm install @discoveryjs/json-ext\n```\n\n## API\n\n### parseChunked()\n\nFunctions like [`JSON.parse()`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON/parse), iterating over chunks to reconstruct the result object, and returns a [Promise](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise).\n\n> Note: `reviver` parameter is not supported yet.\n\n```ts\nfunction parseChunked(input: Iterable<Chunk> | AsyncIterable<Chunk>): Promise<any>;\nfunction parseChunked(input: () => (Iterable<Chunk> | AsyncIterable<Chunk>)): Promise<any>;\n\ntype Chunk = string | Buffer | Uint8Array;\n```\n\n[Benchmark](https://github.com/discoveryjs/json-ext/tree/master/benchmarks#parse-chunked)\n\nUsage:\n\n```js\nimport { parseChunked } from '@discoveryjs/json-ext';\n\nconst data = await parseChunked(chunkEmitter);\n```\n\nParameter `chunkEmitter` can be an iterable or async iterable that iterates over chunks, or a function returning such a value. A chunk can be a `string`, `Uint8Array`, or Node.js `Buffer`.\n\nExamples:\n\n- Generator:\n    ```js\n    parseChunked(function*() {\n        yield '{ \"hello\":';\n        yield Buffer.from(' \"wor'); // Node.js only\n        yield new TextEncoder().encode('ld\" }'); // returns Uint8Array\n    });\n    ```\n- Async generator:\n    ```js\n    parseChunked(async function*() {\n        for await (const chunk of someAsyncSource) {\n            yield chunk;\n        }\n    });\n    ```\n- Array:\n    ```js\n    parseChunked(['{ \"hello\":', ' \"world\"}'])\n    ```\n- Function returning iterable:\n    ```js\n    parseChunked(() => ['{ \"hello\":', ' \"world\"}'])\n    ```\n- Node.js [`Readable`](https://nodejs.org/dist/latest-v14.x/docs/api/stream.html#stream_readable_streams) stream:\n    ```js\n    import fs from 'node:fs';\n\n    parseChunked(fs.createReadStream('path/to/file.json'))\n    ```\n- Web stream (e.g., using [fetch()](https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API)):\n    > Note: Iterability for Web streams was added later in the Web platform, not all environments support it. Consider using `parseFromWebStream()` for broader compatibility.\n    ```js\n    const response = await fetch('https://example.com/data.json');\n    const data = await parseChunked(response.body); // body is ReadableStream\n    ```\n\n### stringifyChunked()\n\nFunctions like [`JSON.stringify()`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON/stringify), but returns a generator yielding strings instead of a single string.\n\n> Note: Returns `\"null\"` when `JSON.stringify()` returns `undefined` (since a chunk cannot be `undefined`).\n\n```ts\nfunction stringifyChunked(value: any, replacer?: Replacer, space?: Space): Generator<string, void, unknown>;\nfunction stringifyChunked(value: any, options: StringifyOptions): Generator<string, void, unknown>;\n\ntype Replacer =\n    | ((this: any, key: string, value: any) => any)\n    | (string | number)[]\n    | null;\ntype Space = string | number | null;\ntype StringifyOptions = {\n    replacer?: Replacer;\n    space?: Space;\n    highWaterMark?: number;\n};\n```\n\n[Benchmark](https://github.com/discoveryjs/json-ext/tree/master/benchmarks#stream-stringifying)\n\nUsage:\n\n- Getting an array of chunks:\n    ```js\n    const chunks = [...stringifyChunked(data)];\n    ```\n- Iterating over chunks:\n    ```js\n    for (const chunk of stringifyChunked(data)) {\n        console.log(chunk);\n    }\n    ```\n- Specifying the minimum size of a chunk with `highWaterMark` option:\n    ```js\n    const data = [1, \"hello world\", 42];\n\n    console.log([...stringifyChunked(data)]); // default 16kB\n    // ['[1,\"hello world\",42]']\n\n    console.log([...stringifyChunked(data, { highWaterMark: 16 })]);\n    // ['[1,\"hello world\"', ',42]']\n\n    console.log([...stringifyChunked(data, { highWaterMark: 1 })]);\n    // ['[1', ',\"hello world\"', ',42', ']']\n    ```\n- Streaming into a stream with a `Promise` (modern Node.js):\n    ```js\n    import { pipeline } from 'node:stream/promises';\n    import fs from 'node:fs';\n\n    await pipeline(\n        stringifyChunked(data),\n        fs.createWriteStream('path/to/file.json')\n    );\n    ```\n- Wrapping into a `Promise` streaming into a stream (legacy Node.js):\n    ```js\n    import { Readable } from 'node:stream';\n\n    new Promise((resolve, reject) => {\n        Readable.from(stringifyChunked(data))\n            .on('error', reject)\n            .pipe(stream)\n            .on('error', reject)\n            .on('finish', resolve);\n    });\n    ```\n- Writing into a file synchronously:\n    > Note: Slower than `JSON.stringify()` but uses much less heap space and has no limitation on string length\n    ```js\n    import fs from 'node:fs';\n\n    const fd = fs.openSync('output.json', 'w');\n\n    for (const chunk of stringifyChunked(data)) {\n        fs.writeFileSync(fd, chunk);\n    }\n\n    fs.closeSync(fd);\n    ```\n- Using with fetch (JSON streaming):\n    > Note: This feature has limited support in browsers, see [Streaming requests with the fetch API](https://developer.chrome.com/docs/capabilities/web-apis/fetch-streaming-requests)\n\n    > Note: `ReadableStream.from()` has limited [support in browsers](https://developer.mozilla.org/en-US/docs/Web/API/ReadableStream/from_static), use [`createStringifyWebStream()`](#createstringifywebstream) instead.\n    ```js\n    fetch('http://example.com', {\n        method: 'POST',\n        duplex: 'half',\n        body: ReadableStream.from(stringifyChunked(data))\n    });\n    ```\n- Wrapping into `ReadableStream`:\n    > Note: Use `ReadableStream.from()` or [`createStringifyWebStream()`](#createstringifywebstream) when no extra logic is needed\n    ```js\n    new ReadableStream({\n        start() {\n            this.generator = stringifyChunked(data);\n        },\n        pull(controller) {\n            const { value, done } = this.generator.next();\n\n            if (done) {\n                controller.close();\n            } else {\n                controller.enqueue(value);\n            }\n        },\n        cancel() {\n            this.generator = null;\n        }\n    });\n    ```\n\n### stringifyInfo()\n\n```ts\nexport function stringifyInfo(value: any, replacer?: Replacer, space?: Space): StringifyInfoResult;\nexport function stringifyInfo(value: any, options?: StringifyInfoOptions): StringifyInfoResult;\n\ntype StringifyInfoOptions = {\n    replacer?: Replacer;\n    space?: Space;\n    continueOnCircular?: boolean;\n}\ntype StringifyInfoResult = {\n    bytes: number;      // size of JSON in bytes\n    spaceBytes: number; // size of white spaces in bytes (when space option used)\n    circular: object[]; // list of circular references\n};\n```\n\nFunctions like [`JSON.stringify()`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON/stringify), but returns an object with the expected overall size of the stringify operation and a list of circular references.\n\nExample:\n\n```js\nimport { stringifyInfo } from '@discoveryjs/json-ext';\n\nconsole.log(stringifyInfo({ test: true }, null, 4));\n// {\n//   bytes: 20,     // Buffer.byteLength('{\\n    \"test\": true\\n}')\n//   spaceBytes: 7,\n//   circular: []    \n// }\n```\n\n#### Options\n\n##### continueOnCircular\n\nType: `Boolean`  \nDefault: `false`\n\nDetermines whether to continue collecting info for a value when a circular reference is found. Setting this option to `true` allows finding all circular references.\n\n### parseFromWebStream()\n\nA helper function to consume JSON from a Web Stream. You can use `parseChunked(stream)` instead, but `@@asyncIterator` on `ReadableStream` has limited support in browsers (see [ReadableStream](https://developer.mozilla.org/en-US/docs/Web/API/ReadableStream) compatibility table).\n\n```js\nimport { parseFromWebStream } from '@discoveryjs/json-ext';\n\nconst data = await parseFromWebStream(readableStream);\n// equivalent to (when ReadableStream[@@asyncIterator] is supported):\n// await parseChunked(readableStream);\n```\n\n### createStringifyWebStream()\n\nA helper function to convert `stringifyChunked()` into a `ReadableStream` (Web Stream). You can use `ReadableStream.from()` instead, but this method has limited support in browsers (see [ReadableStream.from()](https://developer.mozilla.org/en-US/docs/Web/API/ReadableStream/from_static) compatibility table).\n\n```js\nimport { createStringifyWebStream } from '@discoveryjs/json-ext';\n\ncreateStringifyWebStream({ test: true });\n// equivalent to (when ReadableStream.from() is supported):\n// ReadableStream.from(stringifyChunked({ test: true }))\n```\n\n## License\n\nMIT\n", "readmeFilename": "README.md"}