{"_id": "fast-json-stable-stringify", "_rev": "6-3d5c9fc4f539013e48c0a8a8d9884f00", "name": "fast-json-stable-stringify", "description": "deterministic `JSON.stringify()` - a faster version of substack's json-stable-strigify without jsonify", "dist-tags": {"latest": "2.1.0"}, "versions": {"1.0.2": {"name": "fast-json-stable-stringify", "version": "1.0.2", "description": "deterministic JSON.stringify() to get deterministic hashes from stringified results", "main": "index.js", "devDependencies": {"tape": "~1.0.4"}, "scripts": {"test": "tape test/*.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "ff/5", "ff/latest", "chrome/15", "chrome/latest", "safari/latest", "opera/latest"]}, "repository": {"type": "git", "url": "git://github.com/epoberezkin/fast-json-stable-stringify.git"}, "homepage": "https://github.com/epoberezkin/fast-json-stable-stringify", "keywords": ["json", "stringify", "deterministic", "hash", "stable"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "gitHead": "9c1b94db1d3696918fa7f2f1845f8d0cc658b483", "bugs": {"url": "https://github.com/epoberezkin/fast-json-stable-stringify/issues"}, "_id": "fast-json-stable-stringify@1.0.2", "_npmVersion": "5.4.2", "_nodeVersion": "8.7.0", "_npmUser": {"name": "esp", "email": "<EMAIL>"}, "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "dist": {"integrity": "sha512-VyS3a5AWnBZ9G3p9aA/EeB6V9QP+v0qObIFguuV0EbZcUiDTwv57Rofoz3FptA3/xtlFQXYpE/6SCrGl03mNcg==", "shasum": "a5d6c355312bf4570d3368ceae891c56bdc0373f", "tarball": "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-1.0.2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGFPgofDclpS+Aj+NSTeEh+4bHXYaqn/cYskIYrtCPfrAiAhO0aXLN1d7Pxy5ichj/Fe+lz5tV0I+YnbN0LCB4dVHQ=="}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fast-json-stable-stringify-1.0.2.tgz_1508836898860_0.7807034021243453"}, "directories": {}}, "2.0.0": {"name": "fast-json-stable-stringify", "version": "2.0.0", "description": "deterministic `JSON.stringify()` - a faster version of substack's json-stable-strigify without jsonify", "main": "index.js", "devDependencies": {"benchmark": "^2.1.4", "coveralls": "^3.0.0", "eslint": "^4.9.0", "fast-stable-stringify": "latest", "faster-stable-stringify": "latest", "json-stable-stringify": "latest", "nyc": "^11.2.1", "pre-commit": "^1.2.2", "tape": "~1.0.4"}, "scripts": {"eslint": "eslint index.js test", "test-spec": "tape test/*.js", "test": "npm run eslint && nyc npm run test-spec"}, "repository": {"type": "git", "url": "git://github.com/epoberezkin/fast-json-stable-stringify.git"}, "homepage": "https://github.com/epoberezkin/fast-json-stable-stringify", "keywords": ["json", "stringify", "deterministic", "hash", "stable"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "nyc": {"exclude": ["test", "node_modules"], "reporter": ["lcov", "text-summary"]}, "gitHead": "43c016ad43684fb3dff2f94e6bc25cd39fdf7f3b", "bugs": {"url": "https://github.com/epoberezkin/fast-json-stable-stringify/issues"}, "_id": "fast-json-stable-stringify@2.0.0", "_shasum": "d5142c0caee6b1189f87d3a76111064f86c8bbf2", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.10.0", "_npmUser": {"name": "esp", "email": "<EMAIL>"}, "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "dist": {"shasum": "d5142c0caee6b1189f87d3a76111064f86c8bbf2", "tarball": "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.0.0.tgz", "integrity": "sha512-eIgZvM9C3P05kg0qxfqaVU6Tma4QedCPIByQOcemV0vju8ot3cS2DpHi4m2G2JvbSMI152rjfLX0p1pkSdyPlQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBtCQ/tlRgX6yJgrfcz1G6lMuFo8R+SGWmtupZLHATEqAiEAg8rvVe4jCHfmwll3At1KkoNOXzwKQSdgcIarR0OMZak="}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fast-json-stable-stringify-2.0.0.tgz_1508867040972_0.377122831530869"}, "directories": {}}, "2.1.0": {"name": "fast-json-stable-stringify", "version": "2.1.0", "description": "deterministic `JSON.stringify()` - a faster version of substack's json-stable-strigify without jsonify", "main": "index.js", "types": "index.d.ts", "dependencies": {}, "devDependencies": {"benchmark": "^2.1.4", "coveralls": "^3.0.0", "eslint": "^6.7.0", "fast-stable-stringify": "latest", "faster-stable-stringify": "latest", "json-stable-stringify": "latest", "nyc": "^14.1.0", "pre-commit": "^1.2.2", "tape": "^4.11.0"}, "scripts": {"eslint": "eslint index.js test", "test-spec": "tape test/*.js", "test": "npm run eslint && nyc npm run test-spec"}, "repository": {"type": "git", "url": "git://github.com/epoberezkin/fast-json-stable-stringify.git"}, "homepage": "https://github.com/epoberezkin/fast-json-stable-stringify", "keywords": ["json", "stringify", "deterministic", "hash", "stable"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "nyc": {"exclude": ["test", "node_modules"], "reporter": ["lcov", "text-summary"]}, "gitHead": "b3ab8bdfb91cb182c93475c2c3518d6224672bb4", "bugs": {"url": "https://github.com/epoberezkin/fast-json-stable-stringify/issues"}, "_id": "fast-json-stable-stringify@2.1.0", "_nodeVersion": "12.13.1", "_npmVersion": "6.12.1", "dist": {"integrity": "sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==", "shasum": "874bf69c6f404c2b5d99c481341399fd55892633", "tarball": "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz", "fileCount": 18, "unpackedSize": 16959, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd9Qs1CRA9TVsSAnZWagAAltoP/jWcQFQ/Edn5/PAwd8pb\nGVyKQiGL3MORCYwAEwg21khJmXlcMhk4yb+yP5w9f4XbBwltA7d6ToAUOG25\nhErqa7HFHkI3QnqHCD4m5WERuG0QAOoGJBdguNn9xhkZdo4ag4An8FCDAVPI\naY7vctnN5NBhFyA0vseJlWKc42fwzAFjqgGqZ5Ua2736vhqZAUgvTrvTlS5+\nF+L2SJa8n462W9EKS+SUJq1RohSKsHhVKhoiVZDntQDbttsINSKeiwHTf7kp\nNrfNZesdlhs0OnFyKysLud3HYd5SpvOMq4/20G6Cn+Z31pmvakrfkpvlb5EX\n0dUpOTeKmWq+GrF3r2xDOx1szitQQa+D/DWOZFFsoXUAwv4d2fnUYMJVOjHS\ntWmGzVMoCEW62uEmIPCfCf9fuhHN52Tly0v28MnbmdWoB4R0mAFqDhKsMSSJ\nJ7LUPWjZ4n7d8oiZeJZebT6MfSJr1i1ROm9tLbQCt4j18pGKDVJw1KnlcoTc\nz/Q4hZgoIkskc3hV9ferIl/jAdeQcfM3DgekKqr96Y6WGcMlobzyhzXbd9Aq\nRgfS+345S8K/fnDBM5c4ZDgRnDHklm34IZq1ZtEadeiWsQ+5qpPJ+1qhmzPb\n7aZ8PUEiCLj9fxV349tYOXMjMG/Lw7vy+IM02BZvz8lgxrc3XPXR7xtg4pZv\nThLg\r\n=nreR\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGo7BmXw/CHrFXGFD4y35lCwvvA1K9sq/6xMNq0ZBgUNAiB9E8pFb5lsCxox44yHnz6uHUG3edXxKpVUGumEW8d+VQ=="}]}, "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "_npmUser": {"name": "esp", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fast-json-stable-stringify_2.1.0_1576340277444_0.8261047926724743"}, "_hasShrinkwrap": false}}, "readme": "# fast-json-stable-stringify\n\nDeterministic `JSON.stringify()` - a faster version of [@substack](https://github.com/substack)'s json-stable-strigify without [jsonify](https://github.com/substack/jsonify).\n\nYou can also pass in a custom comparison function.\n\n[![Build Status](https://travis-ci.org/epoberezkin/fast-json-stable-stringify.svg?branch=master)](https://travis-ci.org/epoberezkin/fast-json-stable-stringify)\n[![Coverage Status](https://coveralls.io/repos/github/epoberezkin/fast-json-stable-stringify/badge.svg?branch=master)](https://coveralls.io/github/epoberezkin/fast-json-stable-stringify?branch=master)\n\n# example\n\n``` js\nvar stringify = require('fast-json-stable-stringify');\nvar obj = { c: 8, b: [{z:6,y:5,x:4},7], a: 3 };\nconsole.log(stringify(obj));\n```\n\noutput:\n\n```\n{\"a\":3,\"b\":[{\"x\":4,\"y\":5,\"z\":6},7],\"c\":8}\n```\n\n\n# methods\n\n``` js\nvar stringify = require('fast-json-stable-stringify')\n```\n\n## var str = stringify(obj, opts)\n\nReturn a deterministic stringified string `str` from the object `obj`.\n\n\n## options\n\n### cmp\n\nIf `opts` is given, you can supply an `opts.cmp` to have a custom comparison\nfunction for object keys. Your function `opts.cmp` is called with these\nparameters:\n\n``` js\nopts.cmp({ key: akey, value: avalue }, { key: bkey, value: bvalue })\n```\n\nFor example, to sort on the object key names in reverse order you could write:\n\n``` js\nvar stringify = require('fast-json-stable-stringify');\n\nvar obj = { c: 8, b: [{z:6,y:5,x:4},7], a: 3 };\nvar s = stringify(obj, function (a, b) {\n    return a.key < b.key ? 1 : -1;\n});\nconsole.log(s);\n```\n\nwhich results in the output string:\n\n```\n{\"c\":8,\"b\":[{\"z\":6,\"y\":5,\"x\":4},7],\"a\":3}\n```\n\nOr if you wanted to sort on the object values in reverse order, you could write:\n\n```\nvar stringify = require('fast-json-stable-stringify');\n\nvar obj = { d: 6, c: 5, b: [{z:3,y:2,x:1},9], a: 10 };\nvar s = stringify(obj, function (a, b) {\n    return a.value < b.value ? 1 : -1;\n});\nconsole.log(s);\n```\n\nwhich outputs:\n\n```\n{\"d\":6,\"c\":5,\"b\":[{\"z\":3,\"y\":2,\"x\":1},9],\"a\":10}\n```\n\n### cycles\n\nPass `true` in `opts.cycles` to stringify circular property as `__cycle__` - the result will not be a valid JSON string in this case.\n\nTypeError will be thrown in case of circular object without this option.\n\n\n# install\n\nWith [npm](https://npmjs.org) do:\n\n```\nnpm install fast-json-stable-stringify\n```\n\n\n# benchmark\n\nTo run benchmark (requires Node.js 6+):\n```\nnode benchmark\n```\n\nResults:\n```\nfast-json-stable-stringify x 17,189 ops/sec ±1.43% (83 runs sampled)\njson-stable-stringify x 13,634 ops/sec ±1.39% (85 runs sampled)\nfast-stable-stringify x 20,212 ops/sec ±1.20% (84 runs sampled)\nfaster-stable-stringify x 15,549 ops/sec ±1.12% (84 runs sampled)\nThe fastest is fast-stable-stringify\n```\n\n\n## Enterprise support\n\nfast-json-stable-stringify package is a part of [Tidelift enterprise subscription](https://tidelift.com/subscription/pkg/npm-fast-json-stable-stringify?utm_source=npm-fast-json-stable-stringify&utm_medium=referral&utm_campaign=enterprise&utm_term=repo) - it provides a centralised commercial support to open-source software users, in addition to the support provided by software maintainers.\n\n\n## Security contact\n\nTo report a security vulnerability, please use the\n[Tidelift security contact](https://tidelift.com/security).\nTidelift will coordinate the fix and disclosure. Please do NOT report security vulnerability via GitHub issues.\n\n\n# license\n\n[MIT](https://github.com/epoberezkin/fast-json-stable-stringify/blob/master/LICENSE)\n", "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "time": {"modified": "2023-06-22T16:31:53.694Z", "created": "2017-10-24T09:21:39.783Z", "1.0.2": "2017-10-24T09:21:39.783Z", "2.0.0": "2017-10-24T17:44:01.875Z", "2.1.0": "2019-12-14T16:17:57.583Z"}, "homepage": "https://github.com/epoberezkin/fast-json-stable-stringify", "keywords": ["json", "stringify", "deterministic", "hash", "stable"], "repository": {"type": "git", "url": "git://github.com/epoberezkin/fast-json-stable-stringify.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "bugs": {"url": "https://github.com/epoberezkin/fast-json-stable-stringify/issues"}, "license": "MIT", "readmeFilename": "README.md", "users": {"flumpus-dev": true}}