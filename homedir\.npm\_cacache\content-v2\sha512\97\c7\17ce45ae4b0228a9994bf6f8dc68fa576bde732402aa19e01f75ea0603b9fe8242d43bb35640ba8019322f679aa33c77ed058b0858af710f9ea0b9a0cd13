{"_id": "@rollup/rollup-win32-x64-msvc", "_rev": "152-840c759920fd8dae83b1d9d04acf1ea9", "name": "@rollup/rollup-win32-x64-msvc", "dist-tags": {"beta": "4.33.0-0", "latest": "4.44.0"}, "versions": {"4.0.0-0": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.0.0-0", "keywords": ["modules", "bundler", "bundling", "es6", "optimizer"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.0.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "dfab2205dc5528930493ffe5111b51162422b282", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.0.0-0.tgz", "fileCount": 2, "integrity": "sha512-cjEEbcQnBpISJFXHQDcEG8nSTg/W+/o8RuuHzKj0Th4QB4+tp7HKGn73QcfL097SKYfzHS/R3PSWFCRlz1GJ0g==", "signatures": [{"sig": "MEQCIHULK9SzE7LtLd+sorjlHlrPceJu6SFWspKXaUkFB3eBAiAE/mEZ/VvxAvRSZPXXkNVsnC9UV/zGADeHeBE0246q1Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 724}, "main": "native/rollup.win32-x64-msvc.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "580d17223962a0a359da88420001bbbc738e633a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Next-generation ES module bundler", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.0.0-0_1690831065027_0.2767806210092385", "host": "s3://npm-registry-packages"}}, "4.0.0-1": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.0.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.0.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "d4ef930fa138d7104dbb33490573b825c69e04f4", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.0.0-1.tgz", "fileCount": 3, "integrity": "sha512-IpRHi/VQEfezuMDTkJYRGxCYQJJ9U1omuQculMkzN+JOOZLmSfYVsBB2AQtp47cLIvuW2ks3f9CFkmlYbTXRhw==", "signatures": [{"sig": "MEQCICXX0GLRxkpr3KHnckt5Ig+3R/i6TqK3HV5oFqoGmuX2AiBnM3ymNzweaH1ey4iwU8B746tHj4upnLKfJRVbZqVBrQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2826277}, "main": "rollup.win32-x64-msvc.node", "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "gitHead": "d5b6ec3f77c860c048e2830353f5af4593ffaf20", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.0.0-1_1690865350928_0.25354647813273123", "host": "s3://npm-registry-packages"}}, "4.0.0-2": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.0.0-2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.0.0-2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "25fe918687fcb3ca59041def3715b39da470e050", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.0.0-2.tgz", "fileCount": 3, "integrity": "sha512-4XCTI/t4hHSp/FEqHpX2bzfTIBYZNdNhFxcE7qT5yhUkE1vFq1/pDLU6WmIMI5mG8QfHXjTB8pJT6dv11a0SCw==", "signatures": [{"sig": "MEUCIQCBeU+C7+urAdwb69TcLG9RyjWLjnTGW3dUUanHIEKb5AIgMwme50MQ45E1ZW8nEjF0wxiYH8oF6LCuTOLVnMqQNDc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2826277}, "main": "rollup.win32-x64-msvc.node", "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "gitHead": "d62558dbc45912c9c4478dc761bb290738c3b968", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.0.0-2_1690888605806_0.47531000945906476", "host": "s3://npm-registry-packages"}}, "4.0.0-3": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.0.0-3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.0.0-3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "47beeb13e86e90e0e3e6b870b33e0a14fb34310e", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.0.0-3.tgz", "fileCount": 3, "integrity": "sha512-7FtWS5R7RpkCgdTF0DI1U1R67ueZJjV4IdoSCfrUOe95GBc6xHDyKYv9IZpdQaXJfLWFglYYic8lS6TEqtW3aw==", "signatures": [{"sig": "MEQCIG6rVM1E1K+Rsfj25OlBUO1RpkWzizIi5dQCEmYtHVlxAiBlbQun1/hy3N9poE6+htyGHyzEVF6/Fqxe1Hh3qq1bRA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2839077}, "main": "rollup.win32-x64-msvc.node", "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "gitHead": "d9deb724f026a6f3e429509fce2d920e75d6a1ae", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.0.0-3_1691137031497_0.6662007589741641", "host": "s3://npm-registry-packages"}}, "4.0.0-4": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.0.0-4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.0.0-4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "cb970152c93461cc1f5ec4f24a11faa082747915", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.0.0-4.tgz", "fileCount": 3, "integrity": "sha512-6gUQmOrfzAeanpB0SCj7r7avNu352xViG3FP5QajIswkvl/J51JGDWUgQPD2CCi55O0aDf9nmm8sdrqFg+5CjA==", "signatures": [{"sig": "MEQCIEknTLo4tKim3qjj58FJDSa6JK8D/+ZA6U9Xhw1Rre6RAiAyH7+c89J0pehSiuIArSFMcdHUtrfHVYhUxw7GqPK7IQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2839589}, "main": "rollup.win32-x64-msvc.node", "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "gitHead": "c416e3eb3d2d6055d6567cac6e8747b992eec1de", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.0.0-4_1691149016899_0.06691605022771885", "host": "s3://npm-registry-packages"}}, "4.0.0-5": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.0.0-5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.0.0-5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "f287bbb91d53a1a4edd63ae32c4b7f8e7bded1a3", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.0.0-5.tgz", "fileCount": 3, "integrity": "sha512-byeCUVxXj4IK7OD/zw8F6AspAs3bIoWJf5IdSc52GEF5NTuVHWLnmYEHt/wT5u98LLLwihmA6WxmypjxrpotCg==", "signatures": [{"sig": "MEUCIC5OgG0XvplluV2tMaRqY2jjNbsmZFeQ0BDihiarHp0gAiEAgeqZXWhcCwX5YB/PP5MhEyTGHXo6RnlnLsM2pr9xsCM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3391128}, "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.win32-x64-msvc.node"}}, "gitHead": "6284e58c1be160b656b9f2b44e8e2b1e5a93f9df", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.0.0-5_1692514623741_0.04183002424819149", "host": "s3://npm-registry-packages"}}, "4.0.0-6": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.0.0-6", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.0.0-6", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "996515ea25bc3534194464fda6c4e9345348f2f1", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.0.0-6.tgz", "fileCount": 3, "integrity": "sha512-LJtcXkcOzPwYGslevlFLP0EGJTFtuY0vEmorJ518a8G58kjiUOzjPScgiewcdQTqKgTA+KQtQ51VJNBj768fjA==", "signatures": [{"sig": "MEQCIAE3v15I6p38IVxspx4Qm7uFpiLWoQ5vFxlr4vdoaThyAiBfg1T7l9Xh+0bMkD6/dVpOjN0+O91kAX2rb275LnLF/w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3391128}, "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.win32-x64-msvc.node"}}, "gitHead": "39e7492a12eca9107c929d533c16608c9a0054be", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.0.0-6_1692517911859_0.18478800390812267", "host": "s3://npm-registry-packages"}}, "4.0.0-7": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.0.0-7", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.0.0-7", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "929a3538a934e01900a2872c86283c234e4305d7", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.0.0-7.tgz", "fileCount": 7, "integrity": "sha512-OGCEAk9eizX3b/DDQIcLtWbu80xtqkJwaNXlh6QCR2TO8jW9U9+t5QeN9kAzCXL9jVLffmrEaXr91dIgMVKPeg==", "signatures": [{"sig": "MEYCIQC4mvROEVxa4uPFRLeyOqdXnUvQzqGJsxyzYEVzFNd4CgIhAORE9kKNHIPGLKle4K3+6ymmPnF2lHnBmYi+1ko8d8x9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8846050}, "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.win32-x64-msvc.node"}}, "gitHead": "afaa754955a083970b389711127e368d6f4d235b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.0.0-7_1692527633782_0.13077867925237663", "host": "s3://npm-registry-packages"}}, "4.0.0-8": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.0.0-8", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.0.0-8", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "20e6935df9ac25b6f8778e2e80c4b902d21a1a15", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.0.0-8.tgz", "fileCount": 7, "integrity": "sha512-+uUElygyZkTMcWO0aQ5ABQustjtAuZ3LWl5JPRArMAPuD4DReWHUZ58I2n2Ot2QjJLMZz5J4oMKSOb2jaJoPfA==", "signatures": [{"sig": "MEYCIQD2YMl0PA5qjmvZZYMdpWCGfAyX0my6C/r+vDtMX+u9vwIhALZfe4d+oHvFPXPTBq2hpoZMBtBTCe86LNOzGzwZJcVT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8846050}, "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.win32-x64-msvc.node"}}, "gitHead": "5bfa022de96252b5eaf0bdab90be6bcfefcccb57", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.0.0-8_1692530565650_0.7677193435570551", "host": "s3://npm-registry-packages"}}, "4.0.0-9": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.0.0-9", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.0.0-9", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "f6679457ac4d5ec7802927e3d11ab0004370a9f6", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.0.0-9.tgz", "fileCount": 7, "integrity": "sha512-vIv0sckZNU16XeV3+m040I9zJd4UeiIofoSpTRBuxLLUdS+TLQLZq1knM7twxQ5OkYzGYIT7xWjmBmjxWujcuw==", "signatures": [{"sig": "MEUCIQDOsD4rYSfgPQQDW9m1ZGs+eYopFqGbhAN+wUhIlCuieQIgXiyJ+Bd+/jxgGTxPUdnU2b70a4PK4pk+NcRxMmhX9/c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8846050}, "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.win32-x64-msvc.node"}}, "gitHead": "e4d55671a81334ddc59fdbcd81ceabdb77d96974", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.0.0-9_1692541771200_0.8585564111893598", "host": "s3://npm-registry-packages"}}, "4.0.0-10": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.0.0-10", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.0.0-10", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "b36103818cc91996cfea2559fa58644daf5262ea", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.0.0-10.tgz", "fileCount": 7, "integrity": "sha512-0D50mD6Ite1HV9lVzQp9yS+MW9a2Ub32kufrMochLQ6f/D4epugVFo3dyNUT3gkrCq8MuPWUIy1Vhn2pcDF5tw==", "signatures": [{"sig": "MEUCIEOzKXzhyHxTAaEwLPckO82cIL7ldeQSTsKnB3vkrYZhAiEAjI4NAzY5Y/UcOLnhwcSY0YYThPECJeJ+vp9OQtbgrts=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8871870}, "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.win32-x64-msvc.node"}}, "gitHead": "2c7e3e32f5d56c60d92907a9ceacd338aa99ca82", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.0.0-10_1692631829715_0.43803008247276765", "host": "s3://npm-registry-packages"}}, "4.0.0-11": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.0.0-11", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.0.0-11", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "f386ca97e204724c2505840de2e3867f2657efbc", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.0.0-11.tgz", "fileCount": 3, "integrity": "sha512-NOPAagYDRESR6fudlzzYpkxnjDVUjfZp4VPpyNjnC6HuSH4JjBFepNwayX+HhZnZnSkPrM1CS9cBwOan8HeNmg==", "signatures": [{"sig": "MEUCIHOAcy3fySOm8IgWmJlp1kwThUV3IbpOPP6gzMBuYbRYAiEA19SbDz4qQfe8BE4mKXdaNeANBNlKMTo4CHNsS3XijJg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3428393}, "main": "./rollup.win32-x64-msvc.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "3fc8b18da06fc76c386527cebadec4d8936b0f7a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.0.0-11_1692785774214_0.22782526830968797", "host": "s3://npm-registry-packages"}}, "4.0.0-12": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.0.0-12", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.0.0-12", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "d5c9d6f71b577fb68a61b42158253845300e8969", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.0.0-12.tgz", "fileCount": 3, "integrity": "sha512-igu0p9TqXA3A5SeaUGKTEZKGZQLA+8tbcoXPOH2cQ8S6xRq1Y+Ht/4Y9ygP9fJfPESFJgo3Vl82NwT/kxEP1UQ==", "signatures": [{"sig": "MEUCIA63yK2DeqO1/CC00hJUHAB1sjE3mQ82RHfkbcJ3FxqRAiEAkLkvYp/c10TgP2j0IdpGSczISCFBeGbLZnNW6bO5uXE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3428393}, "main": "./rollup.win32-x64-msvc.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "b6eec18d711348e3b177ef58dc2836cdf75e0432", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.0.0-12_1692801660587_0.25062560795908007", "host": "s3://npm-registry-packages"}}, "4.0.0-13": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.0.0-13", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.0.0-13", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "fb0360461ae335e27a21abc11be31562ebf9e7a1", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.0.0-13.tgz", "fileCount": 3, "integrity": "sha512-pJzxRxm4BS++bNDOkqF5uqmkdHX6PYPPHLKOY5Q1iEQddPrQhgB03ZXFIIXL2u0k02vnmqe5TcDI4f+8q0XCGQ==", "signatures": [{"sig": "MEUCIF1haVeN43xgoX13HQgB0gcajmjYBA4VnbKxko6PJzXkAiEAphsMf228W4tnIt3o6YmaFQJJbSkxdQnjCtWtLwNMVlg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3431977}, "main": "./rollup.win32-x64-msvc.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "07d3baeb218f6d1084e9d1b17a429ca84cb92561", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.0.0-13_1692892130175_0.563971432542723", "host": "s3://npm-registry-packages"}}, "4.0.0-14": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.0.0-14", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.0.0-14", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "144e22218628a2a3e586b8dcffa1f60fe53d4b9d", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.0.0-14.tgz", "fileCount": 3, "integrity": "sha512-nFrOtiJDfQCo2HLAEYmJi+jSm0gOyJliJCJvtS5oeZjrwToc1zuX3sdK3000E/OJP4QUr1w+I3PCSFJvMNR8Zg==", "signatures": [{"sig": "MEYCIQCBQTmjQAGGF5CqeF0zpEjc5l3Xhf+cncdFxlUIt5ottwIhAP3jOtadsUkg9lkh/AD0r0/xoIYBKGTMU9/ihAlD2/wU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3416617}, "main": "./rollup.win32-x64-msvc.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "ec2f8ec863d8d896aef0dd0097f2d73f59e8213a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.0.0-14_1694781280539_0.9453651130700871", "host": "s3://npm-registry-packages"}}, "4.0.0-15": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.0.0-15", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.0.0-15", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "bb05d9d1b6fd61eeebb61ab62897de915850c852", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.0.0-15.tgz", "fileCount": 3, "integrity": "sha512-ZVtePbamhThqX8AZIz3N8l2uicGMEYIVTOgt8LwQdEk7zBeF2ahjQuGbLEYpV+H9XAyPRjn6EeMvdWEr8YcFjQ==", "signatures": [{"sig": "MEQCICJ3MlNCgB6o+eAZrawLwlDiybo4PxK20ufPHcYMpK6MAiAl8oTwIt6CMjdEj/Jxn1cGDVzOFa4b2QexaARz7BXlrQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3416617}, "main": "./rollup.win32-x64-msvc.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "6e6186636ebb169611373a0e430853eb3b6ce8e0", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.0.0-15_1694783227450_0.7428162210987499", "host": "s3://npm-registry-packages"}}, "4.0.0-16": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.0.0-16", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.0.0-16", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "18b7c80afd0ef8ff501f98a46d3b0c263037d6a8", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.0.0-16.tgz", "fileCount": 3, "integrity": "sha512-EaT7F476dPUZszUYBcJuBiRcdo4YkOi28ycE+Y/pBKTD9tgSnaTmwFH9LdyB5jhWeMzdcu21aGgBKvQxKIWNIg==", "signatures": [{"sig": "MEUCIQCRhTNZ0vu5HeyzyGwFvtx0bHugudtALHbhjmkbhF97QQIgTK0FCGa/KSM7a3H1AKy7kalGD2R+/vaXPKM0e4QvXMQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3416617}, "main": "./rollup.win32-x64-msvc.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "fd025bcfab85bdecba183367d11c13a1f99c4f10", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.0.0-16_1694787453017_0.26783311980218594", "host": "s3://npm-registry-packages"}}, "4.0.0-17": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.0.0-17", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.0.0-17", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "49b9ff55d7f76bc64a00b5bbbe6e58fad012eafc", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.0.0-17.tgz", "fileCount": 3, "integrity": "sha512-DyaBDA6vHHqE5+drExLlM8MYN9w7G0XfJPo3iJTm7KMHP3UFMOGcWtcHeetIKq6QmJhF7GdvfEoVqTKhgppXLw==", "signatures": [{"sig": "MEUCIEvfYwz2RxtFpZeMM91uhfh+phXSforo/bZxl3B/AC/4AiEA0hF8QTQ2sf+8ZMsfW9pCJSzuWMqut72ejDzDep/t87A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3416617}, "main": "./rollup.win32-x64-msvc.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "f7eb39f003eaa325451091faec04dd51d774ae3b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.0.0-17_1694789962932_0.00836583061518481", "host": "s3://npm-registry-packages"}}, "4.0.0-18": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.0.0-18", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.0.0-18", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "896559e34e4ffb1fa6ea7d68b151dcef97ce619a", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.0.0-18.tgz", "fileCount": 3, "integrity": "sha512-PdarfxvBylhv9AfapYczc+ITGhHbhxkLNKrE7z16/dNKSUThtWLJTWqYfCUrMQHJg+aAc9YKGW6bSXQe9Pd5wg==", "signatures": [{"sig": "MEQCICA+UwI4dTzoRI8dW0oLtr/T5A4W8w7B5Yi0oTaJ7b+TAiBaqYDR5ZaEEunJLvtWt+XhfD/0HxVZxuMOu5d6q84nJA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3416617}, "main": "./rollup.win32-x64-msvc.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "646171ff58e4f31127714ff8c78868c79b77d596", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.0.0-18_1694794262604_0.446497622318649", "host": "s3://npm-registry-packages"}}, "4.0.0-19": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.0.0-19", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.0.0-19", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "e35e448d0d995248fde4b0e9cf3f8ac846c30736", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.0.0-19.tgz", "fileCount": 3, "integrity": "sha512-A3vR3MX87vt0D/eeAcPO96an/uzH0pMw5pK4H8t7Ip8QJyr9iGpEU62oNvjQorNNc78ag0fTakjsJFquYxAeGg==", "signatures": [{"sig": "MEUCIQDvzZ2SoaBwjbInGociltCuAdrSer/QapwE239Tv34JCAIgEdzcUNMn5IM2IB36VBBXPZ3+bzKP/+qOS3DHEIOSSPg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3416617}, "main": "./rollup.win32-x64-msvc.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "25753ad04d73429f0d7b4d5dc85df09aeae78485", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.0.0-19_1694803873085_0.012665989889933726", "host": "s3://npm-registry-packages"}}, "4.0.0-20": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.0.0-20", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.0.0-20", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "f1a5bb7d503d7f34f498de71dff8a6c12e96836f", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.0.0-20.tgz", "fileCount": 3, "integrity": "sha512-MnkeXzyktuN2uNBw1/ROySs6QY3c+asMvsEEwJP0jTSy+AG2VwN2FDlW2KkMC2tbOVBYnNL+wjIV5fNupgpGBg==", "signatures": [{"sig": "MEUCIQDubv7Wdb+6MMoBs3H+m8CnnXjWhT+THfddrHGcoFwu4AIgMb973m1hX7HlBl0YRr+c9ff7LVvzkSyQgCxh848oAzU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3401191}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "9d6dc574c6dca3d85e9eda512b09797a6d15462f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.0.0-20_1695535852600_0.8807957198226852", "host": "s3://npm-registry-packages"}}, "4.0.0-21": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.0.0-21", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.0.0-21", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "c3ee86e7b827fdb7f6b1b53ea45cefbb07a8336f", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.0.0-21.tgz", "fileCount": 3, "integrity": "sha512-bADfq3xjkOY8MdcXkNpinK4Apsk0QAEf/stthyhCLSwZ5gz26sKpNPbMkx8ck3rWUpNyNvRoW4C6tF4Okhg0Aw==", "signatures": [{"sig": "MEYCIQCBVnFyRzlacu8+B4vHOMRKwjBUHn5BH9HOCchYEV6oqgIhAK3+BEz3jstW9Rxa7cM/wBW0GG8q+aGvYC1Cz7DVjpaR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3401191}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "fa868ad975b9ae6007ddc64b1a9e82766de6fa9e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.0.0-21_1695576159548_0.4230302809784925", "host": "s3://npm-registry-packages"}}, "4.0.0-22": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.0.0-22", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.0.0-22", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "feda4cfafeedbcd6a3d4e77ba377d8b0fbff18d2", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.0.0-22.tgz", "fileCount": 3, "integrity": "sha512-s8cuhNg47UxLkSKiEv0CkskKGDt1SBp3tCBg+OvtIaCLTCFxUxE3CYvX7ywbtwwz02iLAwYRMbsYdxFaAbcsgg==", "signatures": [{"sig": "MEUCIF41UY/HR1z6db1ATy6wWP0+9OgqIeOb+w6PZ7bpUSYqAiEA/j10SWcv//ENMFmgpMjmMoprr6SxqLYDx0JIM2JF0SQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3399143}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "38be49cf19099321f935c1ad5968e76fb30e0957", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.0.0-22_1695745071389_0.3251957816072242", "host": "s3://npm-registry-packages"}}, "4.0.0-23": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.0.0-23", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.0.0-23", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "91244887d3da3e32fc1ec859b621b9d69fe5e9b3", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.0.0-23.tgz", "fileCount": 3, "integrity": "sha512-O4vk4bBXS5Hnla8Mmx1rP+X1SerP0Obl4HqgeluKqon1b8l6kHvS4sXSQuSMw7+VH4NFOEqfV7ES6cObwhSjSA==", "signatures": [{"sig": "MEUCIQChoeoz7/WVF5PDbFrWjC3B/HhEKh8daodAk1dv6e+6cQIgTi25ls3N/8w+1cgpezsCqwsjSblOlExsTD9budHbcTk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3399143}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "f1d93caae901c556ffb1e2f553428754038d65c1", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.0.0-23_1695759282127_0.973340094408045", "host": "s3://npm-registry-packages"}}, "4.0.0-24": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.0.0-24", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.0.0-24", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "a39f601e48e9ec6cbe63c8f12757964151000ab9", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.0.0-24.tgz", "fileCount": 3, "integrity": "sha512-41+QkzRaKEZwmA14Fa2DI0QKN5hkcN/orA2KOg5vJAtvwSfB1uQTUmf6T4SGZLw/8In2TEmViB9tDVlbnXmH1A==", "signatures": [{"sig": "MEQCIGUMSay09INuKBFztSBGpkESUZblVnV0vbJ32uWAr3/TAiBiVDnxoM6k8bYZZlVaJtA1XuqwX+5cHKWb1Es0uMwAag==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3463143}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "ced077f2920c473c4c2ca31a8d72b259bec91f67", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.0.0-24_1696309983236_0.615650008831266", "host": "s3://npm-registry-packages"}}, "4.0.0-25": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.0.0-25", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.0.0-25", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "430ef7ad2bfc43c9ea6a514fce48564bcd408896", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.0.0-25.tgz", "fileCount": 3, "integrity": "sha512-bbhPfQDPtuzr8ahCZPKNw2fCg1jebf347AuzBHOLXE1VvyZ331GriNdOwO3ndoHOG+zW4z99PuzgAiGXCbxAqw==", "signatures": [{"sig": "MEQCIDOor0E490mwQsnxzzMYaddgkCjdtakRAeiiik0+v8znAiAphs+D1b90Gyi3gUQqvVi0p5PJ8pXm8GqoCk85JD4Xyg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3443175}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "1ac6bbc437c7ed0de3ad23e4e0904f00783e703d", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.0.0-25_1696515212504_0.6565313401365669", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.0.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.0.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "1b6d1a5cd83ae93bc35b601959322d3a80ef05c9", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.0.0.tgz", "fileCount": 3, "integrity": "sha512-GDi4TkL95/J0ven1wt+q2cfdg1k9UEIQiF58lSC36KUdA0xtlqgLPEDlNAhu6NTXJ491eiZ71lQbLu1D7hlz9w==", "signatures": [{"sig": "MEYCIQCxpF9z7JKsNHmRg5xOZ4xG/drcTg4L49eAu+3n9jZBFwIhANOfT8UC6KvMMSU3ZM48sPb11ECX9JYcMoRrOOLa0nf5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3443172}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "2f261358c62b4f9e62cb86bf99de8d4ff3668994", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.0.0_1696518910729_0.35744972206056835", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.0.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.0.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "9542af771ae2a8c37992de42bf0ca0d9f403982e", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.0.1.tgz", "fileCount": 3, "integrity": "sha512-ysfyyP59n4VUdn3GTfDHg8szzf4Gg01OY6n5DT3hC8jVyyq2pesj8Mq+3yiIGsnnA9uKO6kyWdOGGAy1m6ttAg==", "signatures": [{"sig": "MEQCICp0Y92NVYiCWFR5jqdlfzXjvjchXKXG7A/R+lzPf4laAiA5Wlc4hWECQ4Oijwwaif7twe5PbLBlVB/GBD87YxioRw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3415012}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "fcab1f610fefb24621ce001dfb0831dd30e59ab3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.0.1_1696595821207_0.35620907014350767", "host": "s3://npm-registry-packages"}}, "4.0.2": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.0.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.0.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "4c3ba41643cc1c83ddcaf9d864bf16f612f909cb", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.0.2.tgz", "fileCount": 3, "integrity": "sha512-MBdJIOxRauKkry7t2q+rTHa3aWjVez2eioWg+etRVS3dE4tChhmt5oqZYr48R6bPmcwEhxQr96gVRfeQrLbqng==", "signatures": [{"sig": "MEYCIQD6O+fueVDrffeTdkiJdwtdggUMxsq/e81TjtwNpDaC8QIhANBWSGJjRvEd4OYakD0qaVAYerDvVTi0Lc7gaZghw0dh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3415012}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "3d9c833c4fcb666301967554bac7ab0a0a698efe", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.0.2_1696601941367_0.45448668664966263", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.1.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.1.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "cf971aeb03123b734d04cc596ca16148afb71b9e", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.1.0.tgz", "fileCount": 3, "integrity": "sha512-ZKUpObly8HGI1QXM1AOxEiYolMByH2ekxaeROqRqZnEIWrBJJ/HjNFIY+Q+ujJymSkjj+ArDRTryHX3M7KZGnQ==", "signatures": [{"sig": "MEYCIQD20YMX6y8MbYpeP8raV2sIt7xVy2JF3KTPX+bCxFtvcwIhAM4eu0P2oPqdpMJMFvMlEY8RyWt8gHoZVo9XvnI7e3Yo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3533796}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "cb144b2be4262b3743b31983b26f7fa985be3ceb", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.1.0_1697262751603_0.2504516125574867", "host": "s3://npm-registry-packages"}}, "4.1.1": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.1.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.1.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "3b3717fe537e2ff0c30ef238010d2284b3a7ba7f", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.1.1.tgz", "fileCount": 3, "integrity": "sha512-KiJhHBF211AWQ9l4K8YcPtDqa3lI1RLpySLef634Z4vjbU5v57+y1EuplUzdNYos/aoVrjbaVRAN1GU552YPHA==", "signatures": [{"sig": "MEUCIQCJG2CUhsa+k37R4NWJirpkiyk0aeNhlk8ZcAx+hVakjQIgYgTgNkfIKmDGaLNhYVffiZkt3K/Fpdx0EscBuTzsMuc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3490788}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "d8b31a202a246758b8d67eefe77361a894d37005", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.1.1_1697351525777_0.8282250976769421", "host": "s3://npm-registry-packages"}}, "4.1.3": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.1.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.1.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "8926694478f63da219486f8b6b29c1398583b32a", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.1.3.tgz", "fileCount": 3, "integrity": "sha512-B9oi0JSMvSOsjB0Rve1WLFFEv+8uRvok+N3XWtxzSi/jjiVeM6wbfFgnHzZ2LA7FG9tJs0V7aHLD+73Q8VUIvg==", "signatures": [{"sig": "MEQCIC+eUxzJOh/dcrPUOh1SFk62U1MqAWaZ+0cd8f3skvSGAiA9/C3eLE726fTyHw1PsZD8ttHzqWkUH4NcjjGROCs1Wg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3490788}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "c61a1507a88fc71be431550642b040da4b9422b0", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.1.3_1697392123996_0.5236195167226478", "host": "s3://npm-registry-packages"}}, "4.1.4": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.1.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.1.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "482022c71466e653aa6e1afc7a8323298743609b", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.1.4.tgz", "fileCount": 3, "integrity": "sha512-ANFqWYPwkhIqPmXw8vm0GpBEHiPpqcm99jiiAp71DbCSqLDhrtr019C5vhD0Bw4My+LmMvciZq6IsWHqQpl2ZQ==", "signatures": [{"sig": "MEUCIQDffJB3QtLUItIzGbMWg0Li+/gY9x6cnJlnwymCVSIfPgIgOS77ASrCwo8g+NB3iIIHUdr4/gjK9qr35MTfNJYqpiU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3474404}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "061a0387c8654222620f602471d66afd3c582048", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.1.4_1697430867245_0.9608538006183662", "host": "s3://npm-registry-packages"}}, "4.1.5": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.1.5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.1.5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "121eb95664a46afbc58d4fe2226b9dd9f8dcc59b", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.1.5.tgz", "fileCount": 3, "integrity": "sha512-FQ5qYqRJ2vUBSom3Fos8o/6UvAMOvlus4+HGCAifH1TagbbwVnVVe0o01J1V52EWnQ8kmfpJDJ0FMrfM5yzcSA==", "signatures": [{"sig": "MEUCIGurK0fdYI6zs/Di3z4ag/5k4Ae0cYnGRuR98acR3gHDAiEAsCCiPM9Q0a92ilTFico4+FobSR6ggvUB+4tKgcO+2Fo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3457508}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "1cbb382b0dd3ab70541671c105f96eff283904ec", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.1.5_1698485030184_0.6754148755560747", "host": "s3://npm-registry-packages"}}, "4.1.6": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.1.6", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.1.6", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "668df86a4b9e3fe33d1a8de9db323ba0b0a0d497", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.1.6.tgz", "fileCount": 3, "integrity": "sha512-rsrzsMxZJAMPQSd+0oXmgbbu42wj9aPI9OEQvJmCaqT9dV1TEGEHN9HFvg0OUrc+XzhV6sxZCoZjOO3uhecrqQ==", "signatures": [{"sig": "MEQCIBkGnCB/rhTDDQxd/o2qHbgls8bmH4mbCJkpucm4uDerAiAwTd5fb6GUu2ftyP4y5x50xzeV9YpfVKXdxJL2wM36sA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3457508}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "5901e545697b36326110d89ed02964fdaffd9f6f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.1.6_1698731133487_0.4958966732216503", "host": "s3://npm-registry-packages"}}, "4.2.0": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.2.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.2.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "c01c43de5a3e786b603b37555961870fa5db2e1c", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.2.0.tgz", "fileCount": 3, "integrity": "sha512-dgQfFdHCNg08nM5zBmqxqc9vrm0DVzhWotpavbPa0j4//MAOKZEB75yGAfzQE9fUJ+4pvM1239Y4IhL8f6sSog==", "signatures": [{"sig": "MEQCIDgqIqHYBrht3y99zJlgWhelTIV9zpEvPDyuhuOmXDvuAiBxvbxM+Yc4CGKZg9yZUKirzLsjf/KHHBAGMr1URfmw5Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3474916}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "fbf806aceffd822d43e4603b664c54165c72cf36", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.2.0_1698739860328_0.5347188359654358", "host": "s3://npm-registry-packages"}}, "4.3.0": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.3.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.3.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "0b9bcc159b93c911efb5a2c39ec5d70dd0a589dc", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.3.0.tgz", "fileCount": 3, "integrity": "sha512-dMvGV8p92GQ8jhNlGIKpyhVZPzJlT258pPrM5q2F8lKcc9Iv9BbfdnhX1OfinYWnb9ms5zLw6MlaMnqLfUkKnQ==", "signatures": [{"sig": "MEUCIC96HXQ8RU10XctYaB/DGDfv3ftICagKIKhimMG7/F7dAiEA5YXHmvIwhhPO4UowgB0Tfzl+xfio3tP9MbF4VC4cUjI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3508196}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "937d9911376574c42f893e1cd14b55418c4f7b68", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.3.0_1699042403718_0.14289509991526828", "host": "s3://npm-registry-packages"}}, "4.3.1": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.3.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.3.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "f9bd263c30cc3bacabd8eabe8c767c964732d10e", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.3.1.tgz", "fileCount": 3, "integrity": "sha512-2cRSO5SflYT21SKh1G+2zchLUotL2g7/jhYxbeFpJ8gfVU6CMd2YiIfN++Rs8kzTsuwaTqrE8CAK8GORqoVOeQ==", "signatures": [{"sig": "MEQCIFnyjVSgrSUBxlfH3Bzx+73VmtlqTDw27eDhf4YW1AMoAiBz10kV9bknp4bhBgZxcP+2nLG4WRjyzewTYhlfEFm1qg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3640292}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "52c55bb1e17154ae6d01fb40e0e4a3589bc20a8f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.3.1_1699689490391_0.6428284397029076", "host": "s3://npm-registry-packages"}}, "4.4.0": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.4.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.4.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "a74d4bb9cde9992688d93db7d94d95db1c2c1d8d", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.4.0.tgz", "fileCount": 3, "integrity": "sha512-Zaz6itfQ5sQF5Cia49YDW1ZTr+YfIKzTSb9npLyvQn346n7ulRDOv2J7GnL0zcOJ3cqW7HzG/ZisyO6fH43J9g==", "signatures": [{"sig": "MEUCIDA4xb/e5uyxSChJpMTYkDytLUQ0jalzqM7gX9AZmgWoAiEA0ZNW0IJ5oUd1KBDqoQPXaAGGm2MrlV5rS7lGorXlaUA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3198948}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "53d636051ac60da9b302c4bd6b7eaaccb4871f4b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.4.0_1699775408563_0.33559070459303575", "host": "s3://npm-registry-packages"}}, "4.4.1": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.4.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.4.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "8311b77e6cce322865ba12ada8c3779369610d18", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.4.1.tgz", "fileCount": 3, "integrity": "sha512-eAhItDX9yQtZVM3yvXS/VR3qPqcnXvnLyx1pLXl4JzyNMBNO3KC986t/iAg2zcMzpAp9JSvxB5VZGnBiNoA98w==", "signatures": [{"sig": "MEQCIHOGTTqvMGXicGK0649tuSCfenj9H+JO0RJj0vhJT+YXAiBL7p5nlAhM6YFtCX8bhBf0MhkrNumgRWmXQnaPZB6JyQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3198948}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "01d8c9d1b68919c2c429427ae7e60f503a8bb5f4", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.4.1_1699939559243_0.4534291537401278", "host": "s3://npm-registry-packages"}}, "4.5.0": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.5.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.5.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "748970e066839e33ed8c935061e370c4ab050517", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.5.0.tgz", "fileCount": 3, "integrity": "sha512-8kdW+brNhI/NzJ4fxDufuJUjepzINqJKLGHuxyAtpPG9bMbn8P5mtaCcbOm0EzLJ+atg+kF9dwg8jpclkVqx5w==", "signatures": [{"sig": "MEQCIFzE8ngMU7dE143sJhXTDhdmuwo2xUxP7qKHUE7G0jIiAiBd1fcpOKpwYm76FSgLRm/7sQ7uvAEUT/2Z2HllVz2P6A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3219940}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "86efc769f693516a29047c8d160c6d7287fb965d", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.5.0_1700286747149_0.8787470290432582", "host": "s3://npm-registry-packages"}}, "4.5.1": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.5.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.5.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "0e79117bacb5817ff9a88ab19cb59df839638d6d", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.5.1.tgz", "fileCount": 3, "integrity": "sha512-5I3Nz4Sb9TYOtkRwlH0ow+BhMH2vnh38tZ4J4mggE48M/YyJyp/0sPSxhw1UeS1+oBgQ8q7maFtSeKpeRJu41Q==", "signatures": [{"sig": "MEYCIQCwE7VhUB10wx34jXy5p+Pyq9IVC98PrZvxbcFSw9aZaAIhANj9xiSltulvTbMqwBHOagQfeh82wHkSbdwTs4Gk/Cci", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3220452}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "a083019c7f0c18a1c17260ab1239b12400984a88", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.5.1_1700597605215_0.13081501095950454", "host": "s3://npm-registry-packages"}}, "4.5.2": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.5.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.5.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "7e59216d929a6b444304000be40c32d2d127fe4f", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.5.2.tgz", "fileCount": 3, "integrity": "sha512-pL0RXRHuuGLhvs7ayX/SAHph1hrDPXOM5anyYUQXWJEENxw3nfHkzv8FfVlEVcLyKPAEgDRkd6RKZq2SMqS/yg==", "signatures": [{"sig": "MEUCIAsKh+ATgx0589qFdL633k1OAYZfIoIDLIIqpHAKIaOIAiEAqoS++oRF1+MDvtkWaqYwECBrqcFgFH59PtszJ+gwPwY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3245028}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "2e94641971195c1a4eb9e1a3fe6d73b9d04ffae0", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.5.2_1700807406264_0.4040247609783858", "host": "s3://npm-registry-packages"}}, "4.6.0": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.6.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.6.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "15841505c7ec1648020941d04ca0210f88c59e3a", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.6.0.tgz", "fileCount": 3, "integrity": "sha512-QqmCsydHS172Y0Kc13bkMXvipbJSvzeglBncJG3LsYJSiPlxYACz7MmJBs4A8l1oU+jfhYEIC/+AUSlvjmiX/g==", "signatures": [{"sig": "MEQCIBNDsLhQal3nZ4sqnbYyhhZ+sIn/Oja+/GvB1LBffXenAiBPEWORShFT6UcB+bocgPi1YZG7RqCLKShC4QHjZGho5Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3245028}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "020774d0c7b1371865b20878e59dd3a6a45d3b31", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.6.0_1701005969030_0.8962638416191056", "host": "s3://npm-registry-packages"}}, "4.6.1": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.6.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.6.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "45785b5caf83200a34a9867ba50d69560880c120", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.6.1.tgz", "fileCount": 3, "integrity": "sha512-0zfTlFAIhgz8V2G8STq8toAjsYYA6eci1hnXuyOTUFnymrtJwnS6uGKiv3v5UrPZkBlamLvrLV2iiaeqCKzb0A==", "signatures": [{"sig": "MEQCIGFa1NAAOKV8QQR4UBzI/EN4uF4IS2dzV5rXfTVnlzemAiBilhWHqU87AnJx1PAV5DyQWnzWYgpxFHRgSgjwunTYAw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3245028}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "ded37aa8f95d5ba9786fa8903ef3424fd0549c73", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.6.1_1701321805940_0.2047066929858159", "host": "s3://npm-registry-packages"}}, "4.7.0": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.7.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.7.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "1ad18d12c21d09a12d88c904647f1ea64123c32e", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.7.0.tgz", "fileCount": 3, "integrity": "sha512-SeZzC2QhhdBQUm3U0c8+c/P6UlRyBcLL2Xp5KX7z46WXZxzR8RJSIWL9wSUeBTgxog5LTPJuPj0WOT9lvrtP7Q==", "signatures": [{"sig": "MEQCIFzdTK5VEBYX/5l3POqmzEhQqeKeksPxNWVhPnz/ba+VAiAre+crMs9dnNTaf7fI9oBCbvNaiV6h0mVl0LpoXCKn/g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3220452}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "098e29ca3e0643006870f9ed94710fd3004a9043", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.7.0_1702022299893_0.32037665505355384", "host": "s3://npm-registry-packages"}}, "4.8.0": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.8.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.8.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "05057436705f0be9203c30612a48225ec70af741", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.8.0.tgz", "fileCount": 3, "integrity": "sha512-kb4/auKXkYKqlUYTE8s40FcJIj5soOyRLHKd4ugR0dCq0G2EfcF54eYcfQiGkHzjidZ40daB4ulsFdtqNKZtBg==", "signatures": [{"sig": "MEMCIDLrqovy3Teq2FwwOwRNvP0j/TrD+ZsFvI/V1HM2TXkpAh8FCOjwIcmGzJUO575BE8c+/aeo/x5xToIT5akTGKYt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3220452}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "62b648e1cc6a1f00260bb85aa2050097bb4afd2b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.8.0_1702275914215_0.38337654407439836", "host": "s3://npm-registry-packages"}}, "4.9.0": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.9.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.9.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "4ea610e0c40a07a8afa2977cbf80507f41c2271c", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.9.0.tgz", "fileCount": 3, "integrity": "sha512-aPP5Q5AqNGuT0tnuEkK/g4mnt3ZhheiXrDIiSVIHN9mcN21OyXDVbEMqmXPE7e2OplNLDkcvV+ZoGJa2ZImFgw==", "signatures": [{"sig": "MEUCIQDe77TSHvR4uEzdRTxlXly5DudVcUusb+a6brtfZ8Iz/wIgDPMw1/k3IePbss8U01keKE7TuW5vsIJYQNekYkpeXSQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3220452}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "c5337ef28a71c796e768a9f0edb3d7259a93f1aa", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.9.0_1702459475113_0.5033834473931542", "host": "s3://npm-registry-packages"}}, "4.9.1": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.9.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.9.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "d146db7a5949e10837b323ce933ed882ac878262", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.9.1.tgz", "fileCount": 3, "integrity": "sha512-PyJsSsafjmIhVgaI1Zdj7m8BB8mMckFah/xbpplObyHfiXzKcI5UOUXRyOdHW7nz4DpMCuzLnF7v5IWHenCwYA==", "signatures": [{"sig": "MEYCIQDVpopuev5LVeS2BEXNdH5NgqZ3VgO2Sq26X+eqPAdcFwIhAPYdwPcylmnHB7KZ3wXz5H9KuZE5+qgrXe6wM9ABOolA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3236324}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "d56ac63dc0452820272a0d7536340277f7db68bf", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.9.1_1702794387331_0.32460770732146105", "host": "s3://npm-registry-packages"}}, "4.9.2": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.9.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.9.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "830f3a3fba67f6216a5884368431918029045afe", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.9.2.tgz", "fileCount": 3, "integrity": "sha512-SYRedJi+mweatroB+6TTnJYLts0L0bosg531xnQWtklOI6dezEagx4Q0qDyvRdK+qgdA3YZpjjGuPFtxBmddBA==", "signatures": [{"sig": "MEUCIDVcgFQgQghE3SSWNefPcWqR6e13fKtABGglB/5qEA2yAiEAwNrjSWZRLE43szNxV/zzhD3nYqFg+v0zEIl0MOnnl/k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3215332}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "347a34745b2679c1192535db3c0f60889861d3ad", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.9.2_1703917422788_0.6325499774847414", "host": "s3://npm-registry-packages"}}, "4.9.3": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.9.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.9.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "0c4016b0cdb54d40a87b0df1cda14685cd4b94cd", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.9.3.tgz", "fileCount": 3, "integrity": "sha512-1Qf/qk/iEtx0aOi+AQQt5PBoW0mFngsm7bPuxHClC/hWh2hHBktR6ktSfUg5b5rC9v8hTwNmHE7lBWXkgqluUQ==", "signatures": [{"sig": "MEUCIHchOUmcN5klhQEzi/Eb4Fy58SY53Hofj+pDwq8HdO40AiEAhfldwbocI8jK66jJRD3EFguANPZ7ie3K7xr03RHGENI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3213796}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "4ab3ad360457cd79f4ea852447d3ddca22da95d6", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.9.3_1704435662873_0.7850361306540521", "host": "s3://npm-registry-packages"}}, "4.9.4": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.9.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.9.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "16295ccae354707c9bc6842906bdeaad4f3ba7a5", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.9.4.tgz", "fileCount": 3, "integrity": "sha512-LfdGXCV9rdEify1oxlN9eamvDSjv9md9ZVMAbNHA87xqIfFCxImxan9qZ8+Un54iK2nnqPlbnSi4R54ONtbWBw==", "signatures": [{"sig": "MEYCIQCxOYRiBGjB+RSDLg8JPXPyMCuFKFPK+UQKzMCCiQKf3QIhAMMc+vKJva3j3/o23eWiB/gA/dUDFpoZegfRf/n3+O4g", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3213796}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "18372035f167ec104280e1e91ef795e4f7033f1e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.9.4_1704523156646_0.35442931298898683", "host": "s3://npm-registry-packages"}}, "4.9.5": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.9.5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.9.5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "10491ccf4f63c814d4149e0316541476ea603602", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.9.5.tgz", "fileCount": 3, "integrity": "sha512-1q+mykKE3Vot1kaFJIDoUFv5TuW+QQVaf2FmTT9krg86pQrGStOSJJ0Zil7CFagyxDuouTepzt5Y5TVzyajOdQ==", "signatures": [{"sig": "MEUCIBbS97R48N9z8KNoiEfBxHWD1CG7dGOWeNOUvIMJLR4oAiEAx/Pb9/yuqjZ2u3dYhzeItT+9uVVEz3WCRMKUztphSBI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3352036}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "7fa474cc5ed91c96a4ff80e286aa8534bc15834f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.9.5_1705040189286_0.09810176417101402", "host": "s3://npm-registry-packages"}}, "4.9.6": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.9.6", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.9.6", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "2c1fb69e02a3f1506f52698cfdc3a8b6386df9a6", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.9.6.tgz", "fileCount": 3, "integrity": "sha512-jqzNLhNDvIZOrt69Ce4UjGRpXJBzhUBzawMwnaDAwyHriki3XollsewxWzOzz+4yOFDkuJHtTsZFwMxhYJWmLQ==", "signatures": [{"sig": "MEUCIQDgofJqbrPQEk4dJjH4FoZZkS28A05Nx+iE0J1ac9tgNwIgOhr8Yc2v9yrU9WcPXRMp4B5xJYlBtTIt369XJL5+QB8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3395556}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "ecb6b0a430098052781aa6ee04ec92ee70960321", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.9.6_1705816354875_0.7314373082122512", "host": "s3://npm-registry-packages"}}, "4.10.0": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.10.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.10.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "4eedd01af3a82c1acb0fe6d837ebf339c4cbf839", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.10.0.tgz", "fileCount": 3, "integrity": "sha512-OZoJd+o5TaTSQeFFQ6WjFCiltiYVjIdsXxwu/XZ8qRpsvMQr4UsVrE5UyT9RIvsnuF47DqkJKhhVZ2Q9YW9IpQ==", "signatures": [{"sig": "MEUCIQCpXssjWUl/jexUAhozAO386N3co2sjGrPGSo3AoENCwAIgDfWREiQjM1YCMARjPLeAVSOVvErB3/37PBO2FAKJPGM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3464677}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "762420860765e8e46e24d48b38f5b98ca31735fa", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.10.0_1707544739061_0.5892350467394347", "host": "s3://npm-registry-packages"}}, "4.11.0": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.11.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.11.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "a59158f78d07cd3ac15092ffca2f0319f3bb69f6", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.11.0.tgz", "fileCount": 3, "integrity": "sha512-f3I7h9oTg79UitEco9/2bzwdciYkWr8pITs3meSDSlr1TdvQ7IxkQaaYN2YqZXX5uZhiYL+VuYDmHwNzhx+HOg==", "signatures": [{"sig": "MEUCIEaDdRmy8+7A4RhNUKGtUCKmuYP6xwyCZ/OjRGgjmlb8AiEAm5/bEgsnQ02Y5TjIgshkaTLnvLYQkotnOxqkfIGDmz8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3464677}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "90ad652b745c5fe7167d92b4ad671cc387577a99", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.11.0_1707977404120_0.24206985371735223", "host": "s3://npm-registry-packages"}}, "4.12.0": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.12.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.12.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "9ffdf9ed133a7464f4ae187eb9e1294413fab235", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.12.0.tgz", "fileCount": 3, "integrity": "sha512-ZYmr5mS2wd4Dew/JjT0Fqi2NPB/ZhZ2VvPp7SmvPZb4Y1CG/LRcS6tcRo2cYU7zLK5A7cdbhWnnWmUjoI4qapg==", "signatures": [{"sig": "MEYCIQC6d/l0zHjkKKGQe342A/+LBeTJJFfEWHJ2bTvynrsDygIhAP6lmssQ2mSls2ndl+xco0+x/0dZF32Mol5TrrtY0iun", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3443685}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "0146b84be33a8416b4df4b9382549a7ca19dd64a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.12.0_1708090375584_0.4513317038865574", "host": "s3://npm-registry-packages"}}, "4.12.1": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.12.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.12.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "cd8d175e001c212d5ac71c7827ef1d5c5e14494c", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.12.1.tgz", "fileCount": 3, "integrity": "sha512-n+vkrSyphvmU0qkQ6QBNXCGr2mKjhP08mPRM/Xp5Ck2FV4NrHU+y6axzDeixUrCBHVUS51TZhjqrKBBsHLKb2Q==", "signatures": [{"sig": "MEUCIQCNDn1uXDT5t1hSxVGLOnGSp5oaYSSO6x1iNCyF6dp4CgIgTiTGBtM8W4yNEQZ6B1iVeF8fItii6by2WFveNfb8Eq0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3466213}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "f44dac3170a671b0978afa3af43818617904f544", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.12.1_1709705038729_0.33990403966779525", "host": "s3://npm-registry-packages"}}, "4.13.0": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.13.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.13.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "6abd79db7ff8d01a58865ba20a63cfd23d9e2a10", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.13.0.tgz", "fileCount": 3, "integrity": "sha512-UKXUQNbO3DOhzLRwHSpa0HnhhCgNODvfoPWv2FCXme8N/ANFfhIPMGuOT+QuKd16+B5yxZ0HdpNlqPvTMS1qfw==", "signatures": [{"sig": "MEUCIQCKK9lG392ARx0czt9G7BElmvz7R0SieGp4S+UTeOHLAAIgBMlSF1OajCNpSU1BjQp/GiMhPL3G9gn43+TOM6HqTFY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3468261}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "1c8afed74bd81cd38ad0b373ea6b6ec382975013", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.13.0_1710221343274_0.9347083098087152", "host": "s3://npm-registry-packages"}}, "4.13.1-1": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.13.1-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.13.1-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "935f9c73fc656231ee52ae835dd0e0823bddc7d6", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.13.1-1.tgz", "fileCount": 3, "integrity": "sha512-4ULGEXZVsDElKaEz3Tm53sGfnPR+8GHzTsKbafZU+Q4AZ6UETVq1SUC3qdDLeQq54OG5svnCbN4rTRAg0EcFbQ==", "signatures": [{"sig": "MEUCIA46Wiar4Xu5Mkyqa7D950eoirxMUu4J1M6wDL4xjxTJAiEAs1Z7Jj+gWWNAldYZ3OPqwEsMkUkRnzV/eNclfcL2WiQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3474919}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "84797d177bee161df233644292bc8f128b989cea", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.13.1-1_1711265983795_0.46858395840070033", "host": "s3://npm-registry-packages"}}, "4.13.1": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.13.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.13.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "d1b221daca9afca1885b91a311c6f4a04b0deeb5", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.13.1.tgz", "fileCount": 3, "integrity": "sha512-YY1Exxo2viZ/O2dMHuwQvimJ0SqvL+OAWQLLY6rvXavgQKjhQUzn7nc1Dd29gjB5Fqi00nrBWctJBOyfVMIVxw==", "signatures": [{"sig": "MEUCIH7ZdAAsTzNhSHOW7jBebSaz/QB8CGLJYLzPTdVaGjpdAiEAh+AN3oj9YATlkxNU1rZgTAdC0inSVUv8VVr3lJyFO+s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3474917}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "fffaedeaa1cf9c8f6efc93d53bb8a81738e0ce87", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.13.1_1711535286884_0.223077934742", "host": "s3://npm-registry-packages"}}, "4.13.2": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.13.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.13.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "851959c4c1c3c6647aba1f388198c8243aed6917", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.13.2.tgz", "fileCount": 3, "integrity": "sha512-7h7J2nokcdPePdKykd8wtc8QqqkqxIrUz7MHj6aNr8waBRU//NLDVnNjQnqQO6fqtjrtCdftpbTuOKAyrAQETQ==", "signatures": [{"sig": "MEYCIQCviILFUtG/pbc/IlHRkt2oVyP4IHQqgRmUoUTdk9dmbwIhAKCRgP00L4zngBGYsLtkJQQ2gVRfZNrtwRX/82tZ87BV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3474917}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "b379a592234416a2084918b0eea4c81865a1579f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.13.2_1711635247919_0.26871962871322386", "host": "s3://npm-registry-packages"}}, "4.14.0": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.14.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.14.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "0bb7ac3cd1c3292db1f39afdabfd03ccea3a3d34", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.14.0.tgz", "fileCount": 3, "integrity": "sha512-aGg7iToJjdklmxlUlJh/PaPNa4PmqHfyRMLunbL3eaMO0gp656+q1zOKkpJ/CVe9CryJv6tAN1HDoR8cNGzkag==", "signatures": [{"sig": "MEQCIErAPsgsNGdwoZ15oAsBkrwmf2U61EI1xSCx40AlU7RfAiBe/+T6/rzCzNp5Ljn6S1msqXrBjD10ZCoN0zG4RRc1Rw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3495397}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "5abe71bd5bae3423b4e2ee80207c871efde20253", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.14.0_1712121812134_0.4929740590630416", "host": "s3://npm-registry-packages"}}, "4.14.1": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.14.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.14.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "299eee74b7d87e116083ac5b1ce8dd9434668294", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.14.1.tgz", "fileCount": 3, "integrity": "sha512-wQGI+LY/Py20zdUPq+XCem7JcPOyzIJBm3dli+56DJsQOHbnXZFEwgmnC6el1TPAfC8lBT3m+z69RmLykNUbew==", "signatures": [{"sig": "MEYCIQCalnzQIEAyIFGjHYyEWWXpniV2kSEmlun2VFwmeeoCrgIhAOmbtoYTOGtuqgItpiXSwDJTu8lOu/SxmDnrZcXA6BuU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3465701}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "0b665c31833525c923c0fc20f43ebfca748c6670", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.14.1_1712475361444_0.5025194917547897", "host": "s3://npm-registry-packages"}}, "4.14.2": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.14.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.14.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "5d2d9dc96b436469dc74ef93de069b14fb12aace", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.14.2.tgz", "fileCount": 3, "integrity": "sha512-teAqzLT0yTYZa8ZP7zhFKEx4cotS8Tkk5XiqNMJhD4CpaWB1BHARE4Qy+RzwnXvSAYv+Q3jAqCVBS+PS+Yee8Q==", "signatures": [{"sig": "MEQCIGG8ahlZr5pGqWf1qH2YRur8etByaxkKiPrt65Qq0luUAiBmlgxeS1JvbMdq8EkL/zT1bcfBN3RRe7Nx6jYZY0gl6Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3502053}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "7275328b41b29605142bfdf55d68cb54e895a20c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.14.2_1712903042532_0.5373875892335964", "host": "s3://npm-registry-packages"}}, "4.14.3": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.14.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.14.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "5b2fb4d8cd44c05deef8a7b0e6deb9ccb8939d18", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.14.3.tgz", "fileCount": 3, "integrity": "sha512-/BypzV0H1y1HzgYpxqRaXGBRqfodgoBBCcsrujT6QRcakDQdfU+Lq9PENPh5jB4I44YWq+0C2eHsHya+nZY1sA==", "signatures": [{"sig": "MEUCIEueok+/j1TezXb1IsNAJlmyphve6NDlKzM3dHFvTZy3AiEA4B9VgNbvU/PEgZxASWPgAEO/5Xq9nz2Z1g4mExzo+6A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3465701}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "e64f3d8d0cdc561f00d3efe503e3081f81889679", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.14.3_1713165535357_0.24933290684730092", "host": "s3://npm-registry-packages"}}, "4.15.0": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.15.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.15.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "68bb584231dfc8e36bb7ad5317dfd1fd2563a6f7", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.15.0.tgz", "fileCount": 3, "integrity": "sha512-YA0hTwCunmKNeTOFWdJuKhdXse9jBqgo34FDo+9aS0spfCkp+wj0o1bCcOOTu+0P48O95GTfkLTAaVonwNuIdQ==", "signatures": [{"sig": "MEYCIQC6oL3Z7LpNkBvpUaghH7ss8TANjLwP7ljpdR/XxQegYgIhAL01zfTDsV7D0KSwET9fvcWsR6nJjVsNLJ8N5to4q5NV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3638757}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "e6e05cde31fc144228bb825c9d4ebba2f377075c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.15.0_1713591461688_0.6364663967299915", "host": "s3://npm-registry-packages"}}, "4.16.0": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.16.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.16.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "0db109f8e1a604da9c9a86deebd84d8d040d6671", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.16.0.tgz", "fileCount": 3, "integrity": "sha512-tkfxXt+7c3Ecgn7ln9NJPdBM+QKwQdmFFpgAP+FYhAuRS5y3tY8xeza82gFjbPpytkHmaQnVdMtuzbToCz2tuw==", "signatures": [{"sig": "MEQCIDaEHd2X/2kcXKXyor9ZFpONDPDhx2hrg1rIzUtKA/gnAiAqpWt2MVWRULO0I3oSeG1joGn99CUH5r6HnMhAWYapTw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3638757}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "38fe70780cb7e374b47da99e3a3dca6b2a2170d2", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.16.0_1713674575250_0.8551994309138058", "host": "s3://npm-registry-packages"}}, "4.16.1": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.16.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.16.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "5068a893ba292279adbe76fc487316724b15d811", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.16.1.tgz", "fileCount": 3, "integrity": "sha512-x0fvpHMuF7fK5r8oZxSi8VYXkrVmRgubXpO/wcf15Lk3xZ4Jvvh5oG+u7Su1776A7XzVKZhD2eRc4t7H50gL3w==", "signatures": [{"sig": "MEQCIErXIy26RoXJbo9V+Rluk1ZhwOsNmXvtWSWUnET539mrAiBu5Q18sfOUM6EwBDaZGfunwY2fn8ypvEDw0qWAiQirrA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3638757}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "5d8019b901e98cc8895751a23e5edfc9135b1a35", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.16.1_1713724230748_0.3411907658370137", "host": "s3://npm-registry-packages"}}, "4.16.2": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.16.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.16.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "98fb87589960075d39c44784e3a99f67138602f4", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.16.2.tgz", "fileCount": 3, "integrity": "sha512-3nIf+SJMs2ZzrCh+SKNqgLVV9hS/UY0UjT1YU8XQYFGLiUfmHYJ/5trOU1XSvmHjV5gTF/K3DjrWxtyzKKcAHA==", "signatures": [{"sig": "MEYCIQD1vnF7iWETIdV6a3t4zYDbMh37Ts1Gv9sLICqjtXZ5ZQIhAKw30W2QCsRIPWSXhIV3yHkmj7S+xW18BVpWnSAO9PPh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3638757}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "18839eb234f79adc44a591e355fd7b3243a4cd21", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.16.2_1713799188836_0.5051056452291172", "host": "s3://npm-registry-packages"}}, "4.16.3": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.16.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.16.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "335b89605dac0048176ded97cdeebbe1ae284245", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.16.3.tgz", "fileCount": 3, "integrity": "sha512-hutnZavtOx/G4uVdgoZz5279By9NVbgmxOmGGgnzUjZYuwp2+NzGq6KXQmHXBWz7W/vottXn38QmKYAdQLa/vQ==", "signatures": [{"sig": "MEUCIAGcsnhPXEjrQ4k9qexAKO4WgelVe9gFUKhukwGZWTJIAiEAh6sdf2vY/Ax4kbYqp4zzDVgm7rv8q7pXZWuUxyH+ifI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3638757}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "b9a62fd4cf28538d7c3b268eb25e709b45d44cce", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.16.3_1713849178113_0.2511001974257916", "host": "s3://npm-registry-packages"}}, "4.16.4": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.16.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.16.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "c09ad9a132ccb5a67c4f211d909323ab1294f95f", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.16.4.tgz", "fileCount": 3, "integrity": "sha512-YunpoOAyGLDseanENHmbFvQSfVL5BxW3k7hhy0eN4rb3gS/ct75dVD0EXOWIqFT/nE8XYW6LP6vz6ctKRi0k9A==", "signatures": [{"sig": "MEUCIQCMEajcga3msZBn4bQPfz4zGn0PufiMcW0aoyHy7dt4GgIgAWKjKsEVBtlhbz1rgVNKPuSghjLWrWoNSFiKIAFBPaw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3638757}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "1c404fa352b70007066e94ff4c1981f8046f8cef", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.16.4_1713878130763_0.4051550834340467", "host": "s3://npm-registry-packages"}}, "4.17.0": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.17.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.17.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "3b8ec9d6a7eccca80dd1f16fe444abe128a2ec08", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.17.0.tgz", "fileCount": 3, "integrity": "sha512-ve+D8t1prRSRnF2S3pyDtTXDlvW1Pngbz76tjgYFQW1jxVSysmQCZfPoDAo4WP+Ano8zeYp85LsArZBI12HfwQ==", "signatures": [{"sig": "MEUCIQDos9QFKGCY38Z3EogAAl5w6ACBnBbd/xpmCiLibU4WyQIgeT7f+dSQpcYBcggAJWipMVNNfIyd3/WoyH43ViKshH4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3632101}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "91352494fc722bcd5e8e922cd1497b34aec57a67", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.17.0_1714217416059_0.24486696089876103", "host": "s3://npm-registry-packages"}}, "4.17.1": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.17.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.17.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "e672de70ce490e5564fe75171373d1653b5694da", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.17.1.tgz", "fileCount": 3, "integrity": "sha512-0QbCkfk6cnnVKWqqlC0cUrrUMDMfu5ffvYMTUHf+qMN2uAb3MKP31LPcwiMXBNsvoFGs/kYdFOsuLmvppCopXA==", "signatures": [{"sig": "MEUCIBrsBtWqv0tY/IWgOBWUAYfgoN64iFrIMiqA+ac0Aoq7AiEA85ZoY3DXF4TaoiBTnrY0XjoHoja+/TSlnF2fP9GfHyY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3632101}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "dbf0a2e5d3c3eae09ac4d502646d0ecab63f40fd", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.17.1_1714366697311_0.024230496048456063", "host": "s3://npm-registry-packages"}}, "4.17.2": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.17.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.17.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "5a2d08b81e8064b34242d5cc9973ef8dd1e60503", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.17.2.tgz", "fileCount": 3, "integrity": "sha512-TGGO7v7qOq4CYmSBVEYpI1Y5xDuCEnbVC5Vth8mOsW0gDSzxNrVERPc790IGHsrT2dQSimgMr9Ub3Y1Jci5/8w==", "signatures": [{"sig": "MEQCIF4RFPZQaminJScoJclEesElsZ5G9EZym8z7+tFuo3NwAiAacZ3eD1xsinAqvgJARQkfrt3bHpK/rpNFDIbH+y6agw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3632101}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "5e955a1c2c5e080f80f20f650da9b44909d65d56", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.17.2_1714453276367_0.5429009667549585", "host": "s3://npm-registry-packages"}}, "4.18.0": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.18.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.18.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "5d694d345ce36b6ecf657349e03eb87297e68da4", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.18.0.tgz", "fileCount": 3, "integrity": "sha512-UOo5FdvOL0+eIVTgS4tIdbW+TtnBLWg1YBCcU2KWM7nuNwRz9bksDX1bekJJCpu25N1DVWaCwnT39dVQxzqS8g==", "signatures": [{"sig": "MEQCIADWEiNubfVw71i8HpBBY5ChK/1h+aW0aBEMtdlRYAQaAiBnordhk6iYJJpYwnJppMFt3ibjqHwgIrf3/t+yOadOdA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3601381}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "bb6f069ea3623b0297ef3895f2dcb98a2ca5ef58", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.18.0_1716354251134_0.31032507869645976", "host": "s3://npm-registry-packages"}}, "4.18.1": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.18.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.18.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "0cb240c147c0dfd0e3eaff4cc060a772d39e155c", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.18.1.tgz", "fileCount": 3, "integrity": "sha512-yjk2MAkQmoaPYCSu35RLJ62+dz358nE83VfTePJRp8CG7aMg25mEJYpXFiD+NcevhX8LxD5OP5tktPXnXN7GDw==", "signatures": [{"sig": "MEQCIEhk+E8c48JcW25+bT5aXhSRkcshcUZz4ge5CTZDa9UQAiAzPYnAAUxPKg+2MlfR0upMxzTh9XJX+uvMBJi6HoZEwg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3523557}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "21f9a4949358b60801c948cd4777d7a39d9e6de0", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.18.1_1720452338710_0.8933545302326529", "host": "s3://npm-registry-packages"}}, "4.19.0": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.19.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.19.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "baf9b65023ea2ecc5e6ec68f787a0fecfd8ee84c", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.19.0.tgz", "fileCount": 3, "integrity": "sha512-xNo5fV5ycvCCKqiZcpB65VMR11NJB+StnxHz20jdqRAktfdfzhgjTiJ2doTDQE/7dqGaV5I7ZGqKpgph6lCIag==", "signatures": [{"sig": "MEUCIQDcAh1qyXrE6WcVbrolAYYj3oYAbBGvirG5FZQWDXy7pgIgL3f0bQ2QgiWJF9meHRPNKeqKNlYycS0eD83iu5wROVM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3506661}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "28546b5821efcb72c2eb05f422d986524647a0e3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.19.0_1721454400329_0.3984303655613244", "host": "s3://npm-registry-packages"}}, "4.19.1": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.19.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.19.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "5f2c40d3f1b53ede80fb4e6964f840c0f8936832", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.19.1.tgz", "fileCount": 3, "integrity": "sha512-2b<PERSON>rL28PcK3YCqD9anGxDxamxdiJAxA+l7fWIwM5o8UqNy1t3d1NdAweO2XhA0KTDJ5aH1FsuiT5+7VhtHliXg==", "signatures": [{"sig": "MEYCIQCZ4Isz3N6FhowUZ1TFwLvyGWGv52NMeNggI78tLFl6TgIhAMFDMfRdlpbBeS7No1BAu1UxJzkcPmoAJlLvCXstLo0B", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3457509}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "8b967917c2923dc6a02ca1238261387aefa2cb2f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.19.1_1722056065155_0.6919602364234367", "host": "s3://npm-registry-packages"}}, "4.19.2": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.19.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.19.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "d67206c5f2e4b2832ce360bbbde194e96d16dc51", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.19.2.tgz", "fileCount": 3, "integrity": "sha512-DPi0ubYhSow/00YqmG1jWm3qt1F8aXziHc/UNy8bo9cpCacqhuWu+iSq/fp2SyEQK7iYTZ60fBU9cat3MXTjIQ==", "signatures": [{"sig": "MEQCIF+tmnLptsZZmfGKi89Ml1THUvattOjK1t/8OcePp7GHAiBEPfxaeMPkWr8aNKjDNJ8nLDRpY9a7dmJXcuW9rESc/g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3457509}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "39955e55dbc12ec379a21efcf8fc21e55ec6ce3a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.19.2_1722501199044_0.23579370466368155", "host": "s3://npm-registry-packages"}}, "4.20.0": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.20.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.20.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "601fffee719a1e8447f908aca97864eec23b2784", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.20.0.tgz", "fileCount": 3, "integrity": "sha512-aJ1EJSuTdGnM6qbVC4B5DSmozPTqIag9fSzXRNNo+humQLG89XpPgdt16Ia56ORD7s+H8Pmyx44uczDQ0yDzpg==", "signatures": [{"sig": "MEQCICIOiADIVArGjn3w7aHwykCx0u5/mjkHp2pqV4hFlZFbAiAhnKrUhOa50ucKSEH7BlQKirEkIISnQwULiml9ksZDYw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3456997}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "df12edfea6e9c1a71bda1a01bed1ab787b7514d5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.20.0_1722660556858_0.8719880050716484", "host": "s3://npm-registry-packages"}}, "4.21.0": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.21.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.21.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "20c09cf44dcb082140cc7f439dd679fe4bba3375", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.21.0.tgz", "fileCount": 3, "integrity": "sha512-2jsCDZwtQvRhejHLfZ1JY6w6kEuEtfF9nzYsZxzSlNVKDX+DpsDJ+Rbjkm74nvg2rdx0gwBS+IMdvwJuq3S9pQ==", "signatures": [{"sig": "MEUCIEVLXVwCLn5CAa3oj6pNgjAYlGj0NgH6DXZTnpbUfKmuAiEAkcUaansf5RPf3ONOTW7JRgtk/bfhDyjvZJmPo1wwjBM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3310053}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "c4bb050938778bcbe7b3b3ea3419f7fa70d60f5b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.21.0_1723960557302_0.7212699138456604", "host": "s3://npm-registry-packages"}}, "4.21.1": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.21.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.21.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "4d3ec02dbf280c20bfeac7e50cd5669b66f9108f", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.21.1.tgz", "fileCount": 3, "integrity": "sha512-xGiIH95H1zU7naUyTKEyOA/I0aexNMUdO9qRv0bLKN3qu25bBdrxZHqA3PTJ24YNN/GdMzG4xkDcd/GvjuhfLg==", "signatures": [{"sig": "MEUCIQDN6iz0H9Iw2Pd+7ie/DyhKREgKwsyqsMBqMrZaroGobgIgOkzzXD1+9M3NFfcORTLPO0Rc5K/Zguaw48/f8vpt66A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3309541}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "c33c6ceb7da712c3d14b67b81febf9303fbbd96c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.21.1_1724687679909_0.9670473230384931", "host": "s3://npm-registry-packages"}}, "4.21.2": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.21.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.21.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "e4291e3c1bc637083f87936c333cdbcad22af63b", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.21.2.tgz", "fileCount": 3, "integrity": "sha512-6UUxd0+SKomjdzuAcp+HAmxw1FlGBnl1v2yEPSabtx4lBfdXHDVsW7+lQkgz9cNFJGY3AWR7+V8P5BqkD9L9nA==", "signatures": [{"sig": "MEYCIQCjr5H8bwJupRThVqlk4stympFxhbqmm9y5U2lRPGW01AIhANt3oL+3fof6By4oBqEHwLsuaquCtGRF+fr0u+us0jby", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3295205}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "f83b3151e93253a45f5b8ccb9ccb2e04214bc490", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.21.2_1725001491060_0.7286213747299324", "host": "s3://npm-registry-packages"}}, "4.21.3": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.21.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.21.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "4115233aa1bd5a2060214f96d8511f6247093212", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.21.3.tgz", "fileCount": 3, "integrity": "sha512-fOvu7PCQjAj4eWDEuD8Xz5gpzFqXzGlxHZozHP4b9Jxv9APtdxL6STqztDzMLuRXEc4UpXGGhx029Xgm91QBeA==", "signatures": [{"sig": "MEUCIQD+e0u942YDQEXheUodL4cpnblkPAPk66AVRcKwtpHMLwIganW9YrB0GfdKx0rD1AZFTAOQHA66KYhqmX6DZ9wSj3k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3272677}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "9f5a735524a5c56ba61a8dc6989374917f5aceb1", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.21.3_1726124776580_0.7627849300510927", "host": "s3://npm-registry-packages"}}, "4.22.0": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.22.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.22.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "956948629f6b87de0bdf526b28d940221540bbb6", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.22.0.tgz", "fileCount": 3, "integrity": "sha512-YdicNOSJONVx/vuPkgPTyRoAPx3GbknBZRCOUkK84FJ/YTfs/F0vl/YsMscrB6Y177d+yDRcj+JWMPMCgshwrA==", "signatures": [{"sig": "MEQCICn4pKp8QrEdd7Dkn7KIyK2OQgi02v1r5IA2WC66KGKqAiBsOhBqXz5Tj+C855duJ+xDU9UCjZtAU8qLX1c2XJGMIQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3269093}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "5e7a3631a28a863ddb97a64189c3b76eec9983ca", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.22.0_1726721757009_0.2231389237394079", "host": "s3://npm-registry-packages"}}, "4.22.1": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.22.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.22.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "63ae061c8d645de7f3ba0cef856c242281e44d0d", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.22.1.tgz", "fileCount": 3, "integrity": "sha512-<PERSON><PERSON><PERSON>lcqjcgJSNMhrjMJpJ4T3zriTmiUd2COh1mJHwDShrhhMkpZ/j4M5e4GsvBFviaxtrJtufr0FnKfm2UfOSw==", "signatures": [{"sig": "MEQCIBstaNacH3nssPwJdgI3oYyySZeUN74KoHxFwi8F5lCXAiBJpwBJ4T+8p9pvMPUiqRVK7OmydWfvdcuVlBv32C+udw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3283941}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "76e962daca5b7352bf199c28fa0a10ad4745c5e7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.22.1_1726820537959_0.47743316050532614", "host": "s3://npm-registry-packages"}}, "4.22.2": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.22.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.22.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "c770006ccc780b2de7b2151fc7f37b49121a21c1", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.22.2.tgz", "fileCount": 3, "integrity": "sha512-Yy8So+SoRz8I3NS4Bjh91BICPOSVgdompTIPYTByUqU66AXSIOgmW3Lv1ke3NORPqxdF+RdrZET+8vYai6f4aA==", "signatures": [{"sig": "MEUCIQDzKy/W1APYSHj68XruRBvsP07ex5rIny8CCpP3LyiF2QIgGVgRRqdVlu/nwc84TRy2Ef/orwbsIu5aLq2BjM5i1EA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3283941}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "b86ffd776cfa906573d36c3f019316d02445d9ef", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.22.2_1726824850964_0.25369332924189214", "host": "s3://npm-registry-packages"}}, "4.22.3-0": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.22.3-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.22.3-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "171c39d2f0178d57cbf5a1764c252f82344fee5b", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.22.3-0.tgz", "fileCount": 3, "integrity": "sha512-klLGr4KCow6ATQOsAUFqcPa6QAEiwHFAnLVVSNAo8ZNmzyLZqa+/RnZfIhigZUZN8ttXDDtN61ieEUNkw727mQ==", "signatures": [{"sig": "MEUCIDamWmdaYeqaEO2mGxb5wkPbzw4vZLx+tDG3rhamHE9MAiEAy0M2GzEW6Zqgt3ds4dU7sACdxHxSanCaWpbnNsor7uA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3283943}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "9e04b4849db9134473b84e4b94aa353ae4fd8754", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.22.3-0_1726843705250_0.06292276592233348", "host": "s3://npm-registry-packages"}}, "4.22.3": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.22.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.22.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "16274b6238aa5afe66193f207fa857b0636c5c40", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.22.3.tgz", "fileCount": 3, "integrity": "sha512-4fEwOjO95h0RuNrAYKXIAd1ZWTKUNN+PZwVBHGGdekfeHldP7l2V/iqKPGinMKv5g+KJjcJe9BD6ooBpS/EUJQ==", "signatures": [{"sig": "MEYCIQDOV/UlUcuTJj8S5SIlu8fu5O6n+lFMfZyXLd4b3jZe+QIhAMPGIyM6q0gNjEl3at/RIaSn3Iuopl0DWEHK01wXGx2i", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3283941}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "e1cba8e84a0c01dd16580ba7a2536a988dfb4e18", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.22.3_1726895016321_0.7422957871059799", "host": "s3://npm-registry-packages"}}, "4.22.4": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.22.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.22.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "3dd5d53e900df2a40841882c02e56f866c04d202", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.22.4.tgz", "fileCount": 3, "integrity": "sha512-j8pPKp53/lq9lMXN57S8cFz0MynJk8OWNuUnXct/9KCpKU7DgU3bYMJhwWmcqC0UU29p8Lr0/7KEVcaM6bf47Q==", "signatures": [{"sig": "MEUCIQCbScSLGWB40Q4f4d7Yx0s4rySX+j7Nxox3BmRCT6mSigIgJ90KzjW8uFXEJFeELA9q51Qr6TsmwUIex5URe+3U0Sg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3283941}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "79c0aba353ca84c0e22c3cfe9eee433ba83f3670", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.22.4_1726899106144_0.019773024460403965", "host": "s3://npm-registry-packages"}}, "4.22.5": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.22.5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.22.5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "f2feb149235a5dc1deb5439758f8871255e5a161", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.22.5.tgz", "fileCount": 3, "integrity": "sha512-+lvL/4mQxSV8MukpkKyyvfwhH266COcWlXE/1qxwN08ajovta3459zrjLghYMgDerlzNwLAcFpvU+WWE5y6nAQ==", "signatures": [{"sig": "MEQCIH2UT0zHVkCXilk0vdV11a3vc7amZlH2YT1DmYmuBJI2AiABcIjQzdpum1BIj8jaK2pwE1LzSJGRZfKWPLHlGpyNSg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3269093}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "bc7780c322e134492f40a76bf64afe561670425c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.22.5_1727437724402_0.8975985559918636", "host": "s3://npm-registry-packages"}}, "4.23.0": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.23.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.23.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "54e3562ebd264ef5839f8091618310c40d43d8a9", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.23.0.tgz", "fileCount": 3, "integrity": "sha512-lqCK5GQC8fNo0+JvTSxcG7YB1UKYp8yrNLhsArlvPWN+16ovSZgoehlVHg6X0sSWPUkpjRBR5TuR12ZugowZ4g==", "signatures": [{"sig": "MEYCIQDvFA4fPqsmT2iy/ekddkCA5SKLoRRJWsBlrQYjpQNsdwIhAP3nVeoo1AQ2EZR5e35u251mVU20+2g99i4Oq5X0esQ6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3269093}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "ed98e0821e6ad064839f0af46ceca061adbe3f14", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.23.0_1727766643894_0.7785365183859807", "host": "s3://npm-registry-packages"}}, "4.24.0": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.24.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.24.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "0574d7e87b44ee8511d08cc7f914bcb802b70818", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.24.0.tgz", "fileCount": 3, "integrity": "sha512-fbMkAF7fufku0N2dE5TBXcNlg0pt0cJue4xBRE2Qc5Vqikxr4VCgKj/ht6SMdFcOacVA9rqF70APJ8RN/4vMJw==", "signatures": [{"sig": "MEUCIBCZRnNS6paMfbgHK24ItWb2nbruFyl6RmNdrnlPRheJAiEAn40UcMYOTE+VIfp+c+c0QS4vSetRDZEQoch5OWoCFjo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3278821}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "d3c000f4fd453e39a354299f0cfaa6831f56d7d8", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.24.0_1727861868101_0.5699468450665404", "host": "s3://npm-registry-packages"}}, "4.24.1": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.24.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.24.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "0b7f9f3ca4162a665595204ea09be7ae2377ba55", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.24.1.tgz", "fileCount": 3, "integrity": "sha512-b9IK2buRXwm7owl4Hd8fselCQ7/gr2WaErv0e/IPgRQuJfFS+O0cFJA4t13+FKAZeQh97iEyBG06g613IJLirQ==", "signatures": [{"sig": "MEUCIF5sc0+9HDcOvI7cgL5avCuN+Qk1WcCml0UzwITfJcfNAiEAtJXMRwdpyZm22bBvqjks2oSFEm0haffYwJfWmUzdP0w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3304933}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "88a54d892dacbb0efdbcade263a32d9df1a77b37", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.24.1_1730011412604_0.4729836021911802", "host": "s3://npm-registry-packages"}}, "4.24.2": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.24.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.24.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "0610af0fb8fec52be779d5b163bbbd6930150467", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.24.2.tgz", "fileCount": 3, "integrity": "sha512-2mLH46K1u3r6uwc95hU+OR9q/ggYMpnS7pSp83Ece1HUQgF9Nh/QwTK5rcgbFnV9j+08yBrU5sA/P0RK2MSBNA==", "signatures": [{"sig": "MEUCICcZ4mUo4CJrdCHAdniqMO6cdB2FsvYQCJ9wvwRNhc02AiEA3akcu/3wp4koQynVagDDElqgS0VKqLm2YH2ha7u1PBY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3304933}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "32d0e7dae85121ac0850ec28576a10a6302f84a9", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.24.2_1730043639938_0.7170791111060684", "host": "s3://npm-registry-packages"}}, "4.25.0-0": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.25.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.25.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "e3ec85f17eba6a66d1c451c5314feb6ff33a7e6d", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.25.0-0.tgz", "fileCount": 3, "integrity": "sha512-Jw1kDbybZqZNFREh/DvR1maLRLNyU5upq2X+fBGYeroRE91LiEJnZxgZ+7/VhWv3Swcomo+8lGXXRFOiaUazsQ==", "signatures": [{"sig": "MEUCIQD5NrCOUSh7r80BHx9XNcZsvJ9k4dAs5yv8ZpZEilNy+QIgMlW9By3cqD0dQNhyBkGAVW/6UNj0mnHHLs9/U3HUgGA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3304935}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "b7fcaba12e863db516f39de74c1eacfe5329a5c3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.25.0-0_1730182542112_0.9907846215484029", "host": "s3://npm-registry-packages"}}, "4.24.3": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.24.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.24.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "7687335781efe6bee14d6ed8eff9746a9f24c9cd", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.24.3.tgz", "fileCount": 3, "integrity": "sha512-HGZgRFFYrMrP3TJlq58nR1xy8zHKId25vhmm5S9jETEfDf6xybPxsavFTJaufe2zgOGYJBskGlj49CwtEuFhWQ==", "signatures": [{"sig": "MEUCIDHnFf5ui0GMZ57xDiwVOmgApsVV0VT2ju7+jm5L7fB4AiEAnwxf+WB5rdSRhdAtic6dAIOlI2/vSbqOWZvJ1E/Kfgs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3304933}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "69353a84d70294ecfcd5e1ab8e372e21e94c9f8e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.24.3_1730211280575_0.026708998690968322", "host": "s3://npm-registry-packages"}}, "4.24.4": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.24.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.24.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "e2a9d1fd56524103a6cc8a54404d9d3ebc73c454", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.24.4.tgz", "fileCount": 3, "integrity": "sha512-LTw1Dfd0mBIEqUVCxbvTE/LLo+9ZxVC9k99v1v4ahg9Aak6FpqOfNu5kRkeTAn0wphoC4JU7No1/rL+bBCEwhg==", "signatures": [{"sig": "MEYCIQDUwZqjW96TASLDCQyRAl6+RnA6l3OqtlRM0C8DzE+YpgIhAKgh4en3VK6kVcAX5WsFPIYheevfSawiI90kLKbN1LWY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3305445}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "cdf34ab5411aac6ac3f6cd21b10d2e58427e88ec", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.24.4_1730710059069_0.7244987033073405", "host": "s3://npm-registry-packages"}}, "4.25.0": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.25.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.25.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "93415e7e707e4b156d77c5950b983b58f4bc33f3", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.25.0.tgz", "fileCount": 3, "integrity": "sha512-/RqrIFtLB926frMhZD0a5oDa4eFIbyNEwLLloMTEjmqfwZWXywwVVOVmwTsuyhC9HKkVEZcOOi+KV4U9wmOdlg==", "signatures": [{"sig": "MEUCIBU9z9FQwDBbrJbxJslNhzZm7kUEHd7oux1C2hro3r2KAiEA9VGs1+E/9LveYbJq2cH04MXg2W48jJJ30CDFfjH1PUE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3308517}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "42e587e0e37bc0661aa39fe7ad6f1d7fd33f825c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.25.0_1731141473613_0.6208456362648196", "host": "s3://npm-registry-packages"}}, "4.26.0": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.26.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.26.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "a3ae3da434a4ba0785312e963ae4c1239470403a", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.26.0.tgz", "fileCount": 3, "integrity": "sha512-2x8MO1rm4PGEP0xWbubJW5RtbNLk3puzAMaLQd3B3JHVw4KcHlmXcO+Wewx9zCoo7EUFiMlu/aZbCJ7VjMzAag==", "signatures": [{"sig": "MEYCIQCde1iSAjiYP88XuOKFf1W9xT/theapf+GlsbqvGxqbqAIhANz3LsjvOmX8CWQbdy2FQphrxCz9hGGxv9IP4ggMLq90", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3308517}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "ae1d14b7855ff6568a6697d37271a5eb4d8e2d3e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.26.0_1731480329617_0.5454296369742828", "host": "s3://npm-registry-packages"}}, "4.27.0-0": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.27.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.27.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "31c2234c5f1c65894d802181efd561b450b1b7ff", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.27.0-0.tgz", "fileCount": 3, "integrity": "sha512-hjy09fKIsuDr06qogsUbrSiifE0g5i0YSdL4w/n1Nj7y3AghU6t1pxbVYEVeskgMeGizCualcybwEZnKaxasAA==", "signatures": [{"sig": "MEUCIBIASsjfqCObeiqoRr3C2ysUuo6zTl/Sp7YAE0XnbE9SAiEA2UroF0y5alJtje5zO++CbYxVKf9cRIu0DiDECTt+C7A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3308519}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "5e6074f07843bcbcf26b916c557fdfd81d2adece", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.27.0-0_1731481424258_0.08818131114330252", "host": "s3://npm-registry-packages"}}, "4.27.0-1": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.27.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.27.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "2f825b8b52ca9a1f0aa6c990d27022af206d1f84", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.27.0-1.tgz", "fileCount": 3, "integrity": "sha512-L32rEu9JPqry2Y+lMRvvfPFlSPYb7CnBOGzw8FyJ971fOjzbX7VJ3hq6va3i4APLkktxXzkYbUO74x04HD4YtQ==", "signatures": [{"sig": "MEUCIF70R4Z12OgYd37KzB7nUxPBbgAncqy30KMjo7ss8qBEAiEAiECTVElvLRg0ZRLsGajgTq+l9w6Gl9z0nlPTcXu/AIc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3308519}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "81f5021d7d7e2a488639dc036f2334995b3761fc", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.27.0-1_1731566021457_0.7339620390369188", "host": "s3://npm-registry-packages"}}, "4.27.0": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.27.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.27.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "52d1db041bbb3af2440fab2eab5f851b57023bf8", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.27.0.tgz", "fileCount": 3, "integrity": "sha512-dj2ZolfViR3chLWwSHID2mBzLLwYvXFldIplR6BSkdACXqAsrcmItKTff4h7enYB3Ugoh0v41WbxijE9HJb1Hw==", "signatures": [{"sig": "MEYCIQDtKRAp6DVb9KqfHHsSsUWc8sI+2HRWDXTvlR5Y7WemVgIhAO2m2OT9wt/EK2sNUOPCkZrQ08EAru5svVE62r/mWh93", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3311589}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "c035068dfebeb959a35a8acf3ff008a249e2af73", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.27.0_1731667265748_0.8507395400573865", "host": "s3://npm-registry-packages"}}, "4.27.1-0": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.27.1-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.27.1-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "8f64053a3fad76800bcd922448a9c0b55db50f13", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.27.1-0.tgz", "fileCount": 3, "integrity": "sha512-7wrVczl7Oj5MgVnpRcm6ke9tm0TOU8dFCVe4k0A/X1VIziyMLqIMUnAzK8si/z5myHxLC21ULhiHt12EBsMfxg==", "signatures": [{"sig": "MEYCIQDOYtpnzamPunU3bLrlXXNXEawEJczyFeu3db8ifxvzlAIhALNbpLYlDbrIruxeTT/6XWUDFa7HALP5vcSBI9IwGCLB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3311591}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "a80f6a94d720224a44331d5a50745e9887619703", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.27.1-0_1731677320662_0.42836378466750835", "host": "s3://npm-registry-packages"}}, "4.27.1-1": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.27.1-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.27.1-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "5e04a6b59d099123856f15fdfe21b1fbff36e219", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.27.1-1.tgz", "fileCount": 3, "integrity": "sha512-Qs1JtV5JslUFs/0wgmOJLgSAmTQhVwHEZWJhJaLeAunRXEbwCgDAbFLpdagXV5b99rC2YWNpNbJnAqqreo5deg==", "signatures": [{"sig": "MEUCICD2IC8n7IVrHgHP+seRsGJPpV16iqvEQMtljxBbd8Z0AiEAzAuu9Lc9EyTxWH3KDK4GhassiL5SPKltW4G6qZsdUOA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3311591}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "892ce0206dbf4fbf656b2f0563ef803c5e5a0016", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.27.1-1_1731685115109_0.633014149674932", "host": "s3://npm-registry-packages"}}, "4.27.1": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.27.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.27.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "ddde48f17f0ab5bf840ff46d941bd82cb5149d19", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.27.1.tgz", "fileCount": 3, "integrity": "sha512-zpKGR76smqVOCPeQPqmqXNhEXx+1WwHwozJWvWU6cYy5itRQyYC8TQQoWph+ikxJz5H81u6vwNB8cnYzvXV5Mw==", "signatures": [{"sig": "MEUCIB9rNMQz0mF1OeLYxmE+h5mHeMR27XVf5rMpFy3709ONAiEAilzzDQLX+Nubucg9VoEej00gvgq64oK1LGtxR/moa34=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3311589}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "aaf38b725dd142b1da4190a91de8b04c006fead5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.27.1_1731686893072_0.26568692864151067", "host": "s3://npm-registry-packages"}}, "4.27.2": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.27.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.27.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "dd46f94fb22ea3be0b79193f721b3510fb428a1d", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.27.2.tgz", "fileCount": 3, "integrity": "sha512-foJM5vv+z2KQmn7emYdDLyTbkoO5bkHZE1oth2tWbQNGW7mX32d46Hz6T0MqXdWS2vBZhaEtHqdy9WYwGfiliA==", "signatures": [{"sig": "MEQCIGVRfF9Agqr8hu0MSGvSW5hvq250j8IgzQFS0yxdKlidAiAwQZVR3sN6zj+5Kw8xOTMCuioOE4s3lXb77VGqEK63Wg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3311589}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "a503a4dd9982bf20fd38aeb171882a27828906ae", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.27.2_1731691236250_0.8855679990052954", "host": "s3://npm-registry-packages"}}, "4.27.3": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.27.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.27.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "3721f601f973059bfeeb572992cf0dfc94ab2970", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.27.3.tgz", "fileCount": 3, "integrity": "sha512-vliZLrDmYKyaUoMzEbMTg2JkerfBjn03KmAw9CykO0Zzkzoyd7o3iZNam/TpyWNjNT+Cz2iO3P9Smv2wgrR+Eg==", "signatures": [{"sig": "MEYCIQD5O2ob0OXCuvLOiq2gFabKl1UXxq4XjSwCLLoQ8ym4kgIhAMVcVzyqHig7+jFvJHxCt1+ZAlYoG14Ob5XuKJ/I0qUG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3310565}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "7c0b1f8810013b5a351a976df30a6a5da4fa164b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.27.3_1731948007841_0.16808493312789818", "host": "s3://npm-registry-packages"}}, "4.27.4": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.27.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.27.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "67d516613c9f2fe42e2d8b78e252d0003179d92c", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.27.4.tgz", "fileCount": 3, "integrity": "sha512-3j4jx1TppORdTAoBJRd+/wJRGCPC0ETWkXOecJ6PPZLj6SptXkrXcNqdj0oclbKML6FkQltdz7bBA3rUSirZug==", "signatures": [{"sig": "MEQCIFDxc3Xg07w5goFh3oFjSXauFSTVzue8+hQeTl4SErtNAiB9MnF/LXuMCwW/g0B+ht6Jv5rqy9YlbCptKUiNyaIX0w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3304421}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "e805b546405a4e6cfccd3fe73e9f4df770023824", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.27.4_1732345249768_0.870834083086623", "host": "s3://npm-registry-packages"}}, "4.28.0": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.28.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.28.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "cb313feef9ac6e3737067fdf34f42804ac65a6f2", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.28.0.tgz", "fileCount": 3, "integrity": "sha512-Bvno2/aZT6usSa7lRDL2+hMjVAGjuqaymF1ApZm31JXzniR/hvr14jpU+/z4X6Gt5BPlzosscyJZGUvguXIqeQ==", "signatures": [{"sig": "MEUCIBRjEYEG5HoWiRXaia6WetDpK8E+s26L+hCoX86QjI3HAiEAlEliJ2x979UZidtVWu+TWvmx/N96P3yuoZRSv1WEUgA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3291621}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "0595e433edec3608bfc0331d8f02912374e7f7f7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.28.0_1732972577634_0.7185604902584921", "host": "s3://npm-registry-packages"}}, "4.28.1": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.28.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.28.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "4dff5c4259ebe6c5b4a8f2c5bc3829b7a8447ff0", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.28.1.tgz", "fileCount": 3, "integrity": "sha512-ZvK2jBafvttJjoIdKm/Q/Bh7IJ1Ose9IBOwpOXcOvW3ikGTQGmKDgxTC6oCAzW6PynbkKP8+um1du81XJHZ0JA==", "signatures": [{"sig": "MEUCIQDZGjaD7t8AHwxPjx4Xh2rCxO7f+xoXKZ6CGeiz5B3ntAIgAOk0F+z9eD/qi04B78S8YXu6meFBODNQ51wJcz6MjnY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3282917}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "e60fb1c5d4e54ed5257495215eeda1bb43cf54ba", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.28.1_1733485529833_0.2691799350432025", "host": "s3://npm-registry-packages"}}, "4.29.0-0": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.29.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.29.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "feb3174cc16a30fbe17ec6249e06ae89970b67b5", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.29.0-0.tgz", "fileCount": 3, "integrity": "sha512-qkh4rahxoigDAqv7ZcYhQNTesmRTxZ9Ap1g0bItxG2hGWv+IUTcwc/jUrDkc5xvkAuCyCMv0zCBZlkRNSIW1uA==", "signatures": [{"sig": "MEUCIAVmM/MzC5BDUvxe5PYXmQSKZscwcfgKBJkUtVyazEDBAiEA7Wm2nRaSM25E3xJboPH++aC3RJ9JmKNIDTcqk5m0B50=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3300327}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "879d03d68890f365f880e30c69b58377b8743407", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.29.0-0_1734331228123_0.20749602118608435", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.0-1": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.29.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.29.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "ca3920e5a201fe72b858deb517c2cc9580cda81e", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.29.0-1.tgz", "fileCount": 3, "integrity": "sha512-ilo7eYUZTfghzUGliF91OYaQApv/EJ6+sN0s1vR0DroZjapf/iRMqWLsehCGCaQHxYUlVcWCF/xkv/9lxJO5Cg==", "signatures": [{"sig": "MEUCIQDLVwTGEnVJ+acrHpEFlKK3VtrFMX9eH8OgPSrdz5D9PgIgQx+3liglaF48hZHmuSxU9uouEcIkQrwpAwi/6BgcmW4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3300327}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "fa5064084196636acd98263f95ffea59f8362e32", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.29.0-1_1734590282262_0.5036336214227164", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.0-2": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.29.0-2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.29.0-2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "a98bf9a9f807ed1d7e618f191376b2af14f71264", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.29.0-2.tgz", "fileCount": 3, "integrity": "sha512-Qdo62whe3z6xmHIsj+6aj138D/rk5AB9KRU24QMevQycyy/te5r8Sya6m3K+Us0NPiF3m1NKKpPO5EGo2nLE6A==", "signatures": [{"sig": "MEUCIDJu1rkdohdFSdmYKYzKYDRO6GexqCLfdFHh9bZuHQ0ZAiEA3Dt0U42Cq1sA9tM+lAOs4ySzfbcN2KhZuSDtWlzS/yw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3300327}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "bbb7e7b1d4e208a923b0f18ceb8dd886838e1a01", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.29.0-2_1734677797018_0.08221804117959874", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.0": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.29.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.29.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "1a26eaed53130817e48efca7d1806150e16d2448", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.29.0.tgz", "fileCount": 3, "integrity": "sha512-GsFvcTZ7Yj9k94Qm0qgav7pxmQ7lQDR9NjoelRaxeV1UF6JSDfanR/2tHZ8hS7Ps4KPIVf5AElYPRPmN/Q0ZkQ==", "signatures": [{"sig": "MEYCIQDJ8t/7jWP79Hi2FNUViewL7aO7EysKx6G61bcYaIDtiQIhAMWJBDzbXx98U5TTfmQbPE0RcckXP139hVYT4DCnyQjW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3286501}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "dadd4882c4984d7875af799ad56e506784d50e1c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.29.0_1734719878104_0.40013799046306886", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.1": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.29.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.29.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "c82f04a09ba481e13857d6f2516e072aaa51b7f4", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.29.1.tgz", "fileCount": 3, "integrity": "sha512-+10CMg9vt1MoHj6x1pxyjPSMjHTIlqs8/tBztXvPAx24SKs9jwVnKqHJumlH/IzhaPUaj3T6T6wfZr8okdXaIg==", "signatures": [{"sig": "MEQCIGb3Kq14+7SzckFNMFqqNBshKML9WU6du/4nHm5+dph9AiBeBq394C9K3qmwNraKhmoN2nncNlVeLlpEpKwHlS8ztg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3286501}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "5d3777803404c67ce14c62b8b05d6e26e46856f5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.29.1_1734765396944_0.24623739001692013", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.0-0": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.30.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.30.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "1779912c9b171269195f4ee9e6e328d7814cf47e", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.30.0-0.tgz", "fileCount": 3, "integrity": "sha512-CyWOtqNJmteGxl5b14sPZrHh7XrlCbeF2cQrKx91LGDeVI6IC6k3YyelTqypQQZsLSoCjkzbN+jxiZz8+471gw==", "signatures": [{"sig": "MEUCIFj4FQt9HgIgK5uK76EBGCC4VN4TrAwiyndlUqjbYjRKAiEAqyqIHL1eIp7sNJQvz2LRoUrRtKRaax4UYLvlWIRDwDU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3286503}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "2339f1d8384a8999645823f83f9042a9fc7b3bf7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.30.0-0_1734765468029_0.5063479805853883", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.0-1": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.30.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.30.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "740acb805229621409d8157823644411522329b5", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.30.0-1.tgz", "fileCount": 3, "integrity": "sha512-2JlONfgWogkMZwtpYYql0Ur9odq+n8llDSxyD1HAOF/VEECweXYS6JSlIn7rQz4m9VmfG/lguUxQwRRZFV9k1Q==", "signatures": [{"sig": "MEQCIFMKygPKVn93DxngA8ynS9lafIJmDAReboVEUPUfb0XcAiALPCcWzSvqHSeBav8h2l9qevbNnNfJbOJpjjzo1t8yxA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3307495}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "41ab39a6e4a5181e9be21e816dd6f11c57e1c52a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.30.0-1_1735541571691_0.3843291184226527", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.2": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.29.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.29.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "dc89d92418ae2efa1d70e071c686cffbcf788147", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.29.2.tgz", "fileCount": 3, "integrity": "sha512-p6fTArexECPf6KnOHvJXRpAEq0ON1CBtzG/EY4zw08kCHk/kivBc5vUEtnCFNCHOpJZ2ne77fxwRLIKD4wuW2Q==", "signatures": [{"sig": "MEQCIBpqO57wE30F4WKyLZdJUnutHvdzBWjDYzU2S+i4in3zAiAUdB8wKHeg09ZZ4inv5LvnEo5Frwbxq//XEMxrDvvArA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3307493}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "f5c349e5bb4cb40b0cc1a1b2a3fb5de415946406", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.29.2_1736078897791_0.9935910934611598", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.0": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.30.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.30.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "7384b359bb45c0c3c76ba2c7aaec1d047305efcb", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.30.0.tgz", "fileCount": 3, "integrity": "sha512-DYvxS0M07PvgvavMIybCOBYheyrqlui6ZQBHJs6GqduVzHSZ06TPPvlfvnYstjODHQ8UUXFwt5YE+h0jFI8kwg==", "signatures": [{"sig": "MEUCIQD8wgwW7qU2En57SHF+WuVjxjIN2ObeQnhEIuko5RQFXQIgHXhauA8f0ZgIpga4e9XWeh4oBeycz1w9jj/kMq+sqe8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3307493}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "958d5ebabd49297e9a4b78ad34ac0a0132305dea", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.30.0_1736145430948_0.3617503335290553", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.1": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.30.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.30.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "73bf1885ff052b82fbb0f82f8671f73c36e9137c", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.30.1.tgz", "fileCount": 3, "integrity": "sha512-D6qjsXGcvhTjv0kI4fU8tUuBDF/Ueee4SVX79VfNDXZa64TfCW1Slkb6Z7O1p7vflqZjcmOVdZlqf8gvJxc6og==", "signatures": [{"sig": "MEYCIQCR3kDdmeyQHwhr9iIYeRZjN7+l2S63hETp1fuAOfLS8AIhAJpc7Y+7lT4zPjRmHTAs9XNNmbb25tduGSc+ApdJyJOg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3307493}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "94917087deb9103fbf605c68670ceb3e71a67bf7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.30.1_1736246186307_0.5558647558486804", "host": "s3://npm-registry-packages-npm-production"}}, "4.31.0-0": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.31.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.31.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "086b019f2b5be0b796b5cf678331586512b69d6c", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.31.0-0.tgz", "fileCount": 3, "integrity": "sha512-kWvrWPYhN3vjfQJjW1Y9j9TH3Gymq8+ITXzeOFgkPQisBU4hRNWYm5WIP99kyw14ZCNPwxY14m5DRMDVufbOsg==", "signatures": [{"sig": "MEUCIQDq+x0kXZTJ83d5rTI5nM6pLaGUuwWjpxivBRDh3JfJRAIgYV2JyH6Ivta7H/FPeo+k8wJaQViUleLGjDLl6ZhbpQM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3324903}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "8c80d5f657f0777d14bd75d446fee3fa4b7639fc", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.31.0-0_1736834295436_0.6909314195148644", "host": "s3://npm-registry-packages-npm-production"}}, "4.31.0": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.31.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.31.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "7a2d89a82cf0388d60304964217dd7beac6de645", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.31.0.tgz", "fileCount": 3, "integrity": "sha512-ul8rnCsUumNln5YWwz0ted2ZHFhzhRRnkpBZ+YRuHoRAlUji9KChpOUOndY7uykrPEPXVbHLlsdo6v5yXo/TXw==", "signatures": [{"sig": "MEUCIDGpXfEu5l4KJ9yItQJ0Mw6wi+ao1/ED5BGEisPTT/tuAiEAhxwjGHqURw0aCZdEdJmjeveSAj4yPO/fZVYUBtZWEOs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3325925}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "15c264d59e0768b7d283a7bb8ded0519d1b5199e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.31.0_1737291441593_0.47681158541538626", "host": "s3://npm-registry-packages-npm-production"}}, "4.32.0": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.32.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.32.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "1a40b4792c08094b6479c48c90fe7f4b10ec2f54", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.32.0.tgz", "fileCount": 3, "integrity": "sha512-5hqO5S3PTEO2E5VjCePxv40gIgyS2KvO7E7/vvC/NbIW4SIRamkMr1hqj+5Y67fbBWv/bQLB6KelBQmXlyCjWA==", "signatures": [{"sig": "MEQCIE65vkrzlFHLTqHSoQXj5Z6YpwLcQkzeqwfQUUorVW38AiAqRErVdQNB+q0xImwo+V9M2W1wbB+yZ5Oc2vPCb6qwEg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3342309}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "2538304efdc05ecb7c52e6376d5777565139f075", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.32.0_1737707288141_0.6272565157762431", "host": "s3://npm-registry-packages-npm-production"}}, "4.33.0-0": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.33.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.33.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "69422c20b2ac215dd1b0b9d44f98999b8dbcea4d", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.33.0-0.tgz", "fileCount": 3, "integrity": "sha512-5QdwXJx+2GtTTy4WmWVG0V6ukJ/GfnhTO3nNKpJkpA+01TPklOQBOu+KYSekd3odNn+uFdzHWcXz7UCHiEq0FQ==", "signatures": [{"sig": "MEQCICsYdJItGvudt4y8EhPZQaqCW2YMBOue37yqr+c+9DFBAiAPfjNQqG4BNzKu3c39A4+e4jj1VbZKGJvg6pvvwsgnhw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3342311}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "f854e1988542d09f9691923eddd80888e92240d3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.33.0-0_1738053041738_0.2409321298867786", "host": "s3://npm-registry-packages-npm-production"}}, "4.32.1": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.32.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.32.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "512db088df67afee8f07183cdf8c9eecd64f6ef8", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.32.1.tgz", "fileCount": 3, "integrity": "sha512-ar80GhdZb4DgmW3myIS9nRFYcpJRSME8iqWgzH2i44u+IdrzmiXVxeFnExQ5v4JYUSpg94bWjevMG8JHf1Da5Q==", "signatures": [{"sig": "MEUCIDUDzDqIEkm0+k90rhqXdlye+G1H2v4F9jgmuDUFLXkqAiEAiw2CUep23gPyyopGU/caAy66cNjheAuXXj+4C4q8+K0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3342309}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "abcf4febe11f3d313fae41ddca35fc60670b9ff8", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.32.1_1738053228886_0.6289882346398772", "host": "s3://npm-registry-packages-npm-production"}}, "4.33.0": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.33.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.33.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "cd29166ca36d73af8a9afba7471dac405dd7670c", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.33.0.tgz", "fileCount": 3, "integrity": "sha512-wqYKNnO1Sbfl2/Tn/4gLJb<PERSON><PERSON>+MhCQWF/ZTe/7zTqq3G8GBAtL746Oj3Eq3VwJD0Mt5Z9uZYCdvMxTV/GJ2+A==", "signatures": [{"sig": "MEUCIBJvhG7iW0WxEE1fYdMksifs32LMsDAyIER3XCCQM0eYAiEAt5fuedRwxn4xdMmOy8Mu5CkXhC0egOso7StMUUUtHLo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3320293}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "494483e8df7b5d04796b30e37f54d7e96fa91a97", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.33.0_1738393954319_0.9194960134261378", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.0": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.34.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.34.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "64c15d791d9b07ced7e89f735b2bd420a41c2e8e", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.34.0.tgz", "fileCount": 3, "integrity": "sha512-g2ASy1QwHP88y5KWvblUolJz9rN+i4ZOsYzkEwcNfaNooxNUXG+ON6F5xFo0NIItpHqxcdAyls05VXpBnludGw==", "signatures": [{"sig": "MEQCICB2LfUKXNKDUUoOVzDBSf+tac5SWbNPona94+3EMdoWAiAiGRszNH28KaWALsP+i58E9XUKd/IQFm9D0j6Vy3j6jg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3320293}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "979d62888dbe75f92e50fdd64246c737c52f5f1f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.34.0_1738399255187_0.5117102046003592", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.1": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.34.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.34.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "37cddc33f8cd961a615f5f854d2b1994c90fe547", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.34.1.tgz", "fileCount": 3, "integrity": "sha512-k3MVFD9Oq+laHkw2N2v7ILgoa9017ZMF/inTtHzyTVZjYs9cSH18sdyAf6spBAJIGwJ5UaC7et2ZH1WCdlhkMw==", "signatures": [{"sig": "MEQCIH04695o8I8totiBGlBMFeO+gGDK5TPl+TP8A2ejcWauAiB2YrRUm0aN64LqUAhr/XkQyeWhnyEasbkjR0g93lC6fw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3320293}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "0f20524ad9ecd166a900d43af93f05a3405d2a45", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.34.1_1738565925725_0.07429330369111997", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.2": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.34.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.34.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "87e27b5da4ae743567df2988e5975bb876bdaa32", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.34.2.tgz", "fileCount": 3, "integrity": "sha512-rUrqINax0TvrPBXrFKg0YbQx18NpPN3NNrgmaao9xRNbTwek7lOXObhx8tQy8gelmQ/gLaGy1WptpU2eKJZImg==", "signatures": [{"sig": "MEUCIQDkrtWqBOS9pC1z9E834ypKFy7TOPMbH5499dfzO9qbjwIgCzszRkGvK7DsDYILuC1nyK4dG/QAWU2epINTRS4BT34=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3320293}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "615efa045779fae70c4fd5fe64fdb08a039c0442", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.34.2_1738656637435_0.20760451545850556", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.3": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.34.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.34.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "2cd47d213ddd921bab1470a3e31312ee37aac08a", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.34.3.tgz", "fileCount": 3, "integrity": "sha512-CUypcYP31Q8O04myV6NKGzk9GVXslO5EJNfmARNSzLF2A+5rmZUlDJ4et6eoJaZgBT9wrC2p4JZH04Vkic8HdQ==", "signatures": [{"sig": "MEUCIFD7KKsppXxu1V0eYcFkunDfD+F3xCayWwrFvw7zS+a2AiEAxVwVHFmTnvCXA+umwNDGdRkRxw3rJG/MgVyEDL/MJ2g=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3320293}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "ac8b06a2b5406f694c38c416912cc2b18ba13355", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.34.3_1738747359385_0.898528826471469", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.4": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.34.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.34.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "21f0d5e44e4ac7d9b6510bef283faace629c6f7e", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.34.4.tgz", "fileCount": 3, "integrity": "sha512-p0scwGkR4kZ242xLPBuhSckrJ734frz6v9xZzD+kHVYRAkSUmdSLCIJRfql6H5//aF8Q10K+i7q8DiPfZp0b7A==", "signatures": [{"sig": "MEYCIQCEOrHG33t3OBN1D7ECIyJvjW7Qj+Mf8o7EtvLMqykoiAIhALjqLM3I41ZHNRREwG0JW4SD+WceF8Kp3u4JXgfIxJ4f", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3320293}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "19312a762c3cda56a0f6dc80a0887a4499db2257", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.34.4_1738791104699_0.09218458034728405", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.5": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.34.5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.34.5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "a8996c4726a28caa7c68105101ca4ec655ffd65e", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.34.5.tgz", "fileCount": 3, "integrity": "sha512-Mi8yVUlQOoeBpY72n75VLATptPGvj2lHa47rQdK9kZ4MoG5Ve86aVIU+PO3tBklTCBtILtdRfXS0EvIbXgmCAg==", "signatures": [{"sig": "MEYCIQChjYSHiy+4zS7TUvV1kpzGuOQcO1LGbUfo0l2CNZvbzQIhAMaBuPwkAlFFSDOnN5aGaSdHqF6tGj5r9jI/m/KcY7AX", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3311077}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "3426b026e95319048dd5b703f2a0330c1c924e52", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.34.5_1738918417972_0.7031881374615232", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.6": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.34.6", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.34.6", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "f3d03ce2d82723eb089188ea1494a719b09e1561", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.34.6.tgz", "fileCount": 3, "integrity": "sha512-0PVwmgzZ8+TZ9oGBmdZoQVXflbvuwzN/HRclujpl4N/q3i+y0lqLw8n1bXA8ru3sApDjlmONaNAuYr38y1Kr9w==", "signatures": [{"sig": "MEUCIQDX6W7ALqs/EmReoJHYbSenx5wfZWmmfT6SgPvwZd04wAIgQ0iB2DxmSuUgniOlMjwnGXGmZQjKytSOFJC8X9N8Df4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3308005}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "4b8745922d37d8325197d5a6613ffbf231163c7d", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.34.6_1738945961422_0.15482357679054615", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.7": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.34.7", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.34.7", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "6531d61e7141091eaab0461ee8e0380c10e4ca57", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.34.7.tgz", "fileCount": 3, "integrity": "sha512-4ZedScpxxIrVO7otcZ8kCX1mZArtH2Wfj3uFCxRJ9NO80gg1XV0U/b2f/MKaGwj2X3QopHfoWiDQ917FRpwY3w==", "signatures": [{"sig": "MEYCIQC4iNzutSHIeMgIJUgi6Im0z2tIgl5eYTnHFOz7ws6KAgIhALYUD8bfjeCil18Aw+C9m59r2pgLyO3VRq57CWlsJpM3", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3317221}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "f9c52f80074e33f5b0799e8ca215e3bfac7d2755", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.34.7_1739526872118_0.8188011801441573", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.8": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.34.8", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.34.8", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "4cdb2cfae69cdb7b1a3cc58778e820408075e928", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.34.8.tgz", "fileCount": 3, "integrity": "sha512-U0FaE5O1BCpZSeE6gBl3c5ObhePQSfk9vDRToMmTkbhCOgW4jqvtS5LGyQ76L1fH8sM0keRp4uDTsbjiUyjk0g==", "signatures": [{"sig": "MEYCIQD+EQ5e5a+EYT9K6s7kjRXRZkdj4LIK6Vyb06S0f+5SPQIhAPqoo2cpXk9F1tkp+uADi8L6Pa6RRywNb59ZJRY6+aOf", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3317221}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "8f667b7c15b176728449a4917cb29fe5ee3a1c0c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.34.8_1739773618200_0.9942891166418069", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.9": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.34.9", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.34.9", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "1973871850856ae72bc678aeb066ab952330e923", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.34.9.tgz", "fileCount": 3, "integrity": "sha512-AyleYRPU7+rgkMWbEh71fQlrzRfeP6SyMnRf9XX4fCdDPAJumdSBqYEcWPMzVQ4ScAl7E4oFfK0GUVn77xSwbw==", "signatures": [{"sig": "MEQCIBAgsBUemNqn7J3ONSnOsDG6mckF0V+nQ6FD4DJil9maAiB4S6wh535pvLRpLPPIDG0A/kVxtDaH+7WZq9PhpP1cTA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3553765}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "0ab9b9772e24dfe9ef08bfce3132e99a15b793f6", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.34.9_1740814391960_0.1817698656993596", "host": "s3://npm-registry-packages-npm-production"}}, "4.35.0": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.35.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.35.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "05f25dbc9981bee1ae6e713daab10397044a46ca", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.35.0.tgz", "fileCount": 3, "integrity": "sha512-PIQeY5XDkrOysbQblSW7v3l1MDZzkTEzAfTPkj5VAu3FW8fS4ynyLg2sINp0fp3SjZ8xkRYpLqoKcYqAkhU1dw==", "signatures": [{"sig": "MEQCIDlJodR+N9er0lwke/xO1h+v8HUoT43A6xHqLb2Q+v8sAiBi70UwLDI3O7NzTl5PGoVj4zrmZRVp+jvW+GHGdJYauA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3582949}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "70ef1cce7c740030cc2935b563d13950cc1511f5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.35.0_1741415123860_0.07781639763788695", "host": "s3://npm-registry-packages-npm-production"}}, "4.36.0": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.36.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.36.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "88b23fe29d28fa647030b36e912c1b5b50831b1d", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.36.0.tgz", "fileCount": 3, "integrity": "sha512-aRXd7tRZkWLqGbChgcMMDEHjOKudo1kChb1Jt1IfR8cY/KIpgNviLeJy5FUb9IpSuQj8dU2fAYNMPW/hLKOSTw==", "signatures": [{"sig": "MEUCIQCd3reHC3g0VwZ2hb3ul3PL8w0kDvdqKGhTawMYI98haAIgHUk7athQBI62VPd28F2Y8VjfYpcat8JNSK0ISiW/qOs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3569637}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "ab7bfa8fe9c25e41cc62058fa2dcde6b321fd51d", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.36.0_1742200580804_0.40644401573976885", "host": "s3://npm-registry-packages-npm-production"}}, "4.37.0": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.37.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.37.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "a54d7304c3bd45573d8bcd1270de89771f8195fe", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.37.0.tgz", "fileCount": 3, "integrity": "sha512-LWbXUBwn/bcLx2sSsqy7pK5o+Nr+VCoRoAohfJ5C/aBio9nfJmGQqHAhU6pwxV/RmyTk5AqdySma7uwWGlmeuA==", "signatures": [{"sig": "MEUCIQDIOZ7itBaSrvN+6bk/7xyqEHHBs+aZ11zDT7gPUV5svQIgYSDyovYqJLwy1ui/O+tbVb0cqomkIa2J/TWN/tWKp/M=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3578853}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "8b1c634d945dda9294cf579de68c4b223c618e7f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.37.0_1742741860058_0.6341696881611838", "host": "s3://npm-registry-packages-npm-production"}}, "4.38.0": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.38.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.38.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "38197da22c1af7b6b5d1cc7541757379193b8e83", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.38.0.tgz", "fileCount": 3, "integrity": "sha512-jjqy3uWlecfB98Psxb5cD6Fny9Fupv9LrDSPTQZUROqjvZmcCqNu4UMl7qqhlUUGpwiAkotj6GYu4SZdcr/nLw==", "signatures": [{"sig": "MEYCIQC+6Jb/zlxcbOkAHRvTHb9l+z0xqYb6OthWzVuaz4c1KwIhALGZWnACXXxUwUD1jFj6qmil1Hh/95cnLLQc6p8okZIA", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3579365}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "22b64bcc511dfc40ce463e3f662a928915908713", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.38.0_1743229786084_0.12205411587064452", "host": "s3://npm-registry-packages-npm-production"}}, "4.39.0": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.39.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.39.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "bfe0214e163f70c4fec1c8f7bb8ce266f4c05b7e", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.39.0.tgz", "fileCount": 3, "integrity": "sha512-yAkUOkIKZlK5dl7u6dg897doBgLXmUHhIINM2c+sND3DZwnrdQkkSiDh7N75Ll4mM4dxSkYfXqU9fW3lLkMFug==", "signatures": [{"sig": "MEYCIQCiE7NA14bzznYJaPIDfwcMN5Y/zsQjMqj1QRFs6R6r+AIhAOGe25wx0hc+7XvP0QurG4y423WvBhy6PI9erIy1TsyF", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3579365}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "5c001245779063abac3899aa9d25294ab003581b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.39.0_1743569410305_0.5441533213351697", "host": "s3://npm-registry-packages-npm-production"}}, "4.40.0": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.40.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.40.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "fd92d31a2931483c25677b9c6698106490cbbc76", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.40.0.tgz", "fileCount": 3, "integrity": "sha512-lpPE1cLfP5oPzVjKMx10pgBmKELQnFJXHgvtHCtuJWOv8MxqdEIMNtgHgBFf7Ea2/7EuVwa9fodWUfXAlXZLZQ==", "signatures": [{"sig": "MEYCIQD1o3oFSgCHKlBIJP0YNI30qN5JiTjERDQFeIFJY35wXQIhANrtLoEh4qal58W8RKDtKUsyj8uk9cSvZq74jJreMXhd", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3622885}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "1f2d579ccd4b39f223fed14ac7d031a6c848cd80", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.40.0_1744447212611_0.667483534305233", "host": "s3://npm-registry-packages-npm-production"}}, "4.40.1": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.40.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.40.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "8078b71fe0d5825dcbf83d52a7dc858b39da165c", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.40.1.tgz", "fileCount": 3, "integrity": "sha512-ECyOuDeH3C1I8jH2MK1RtBJW+YPMvSfT0a5NN0nHfQYnDSJ6tUiZH3gzwVP5/Kfh/+Tt7tpWVF9LXNTnhTJ3kA==", "signatures": [{"sig": "MEUCIQDkKURAZ7ock5DbqIwM/bhOSQkIq4Y12ceWXGaQ+KbBFQIgLysD6rnVbPvm/NqrxJc2LDvO2LABGaT1gr07786A+eQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3689445}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "1e6c40f49c428b7657fe3b9a2026f705acd39da1", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.40.1_1745814960938_0.28136740761845447", "host": "s3://npm-registry-packages-npm-production"}}, "4.40.2": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.40.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.40.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "8dae04d01a2cbd84d6297d99356674c6b993f0fc", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.40.2.tgz", "fileCount": 3, "integrity": "sha512-bwspbWB04XJpeElvsp+DCylKfF4trJDa2Y9Go8O6A7YLX2LIKGcNK/CYImJN6ZP4DcuOHB4Utl3iCbnR62DudA==", "signatures": [{"sig": "MEUCICfmlQHPLbhOdDwBScCnIGyIXmUkAa9lWuR+9LzE2YP2AiEAsRhQaWB475eufCuzCiHDZ3gc1Uy8GokAnC3rVUV08qw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3682789}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "02da7efedcf373f0f819b78e3acbe50de05d9a5b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.40.2_1746516452014_0.5232886746840439", "host": "s3://npm-registry-packages-npm-production"}}, "4.41.0": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.41.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.41.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "b0b595ad4720259bbb81600750d26a655cac06be", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.41.0.tgz", "fileCount": 3, "integrity": "sha512-h1J+Yzjo/X+0EAvR2kIXJDuTuyT7drc+t2ALY0nIcGPbTatNOf0VWdhEA2Z4AAjv6X1NJV7SYo5oCTYRJhSlVA==", "signatures": [{"sig": "MEYCIQCzs1A9ty5Wk7Qysufjn+6tsLECCMayIIgfAKbdhzUGWwIhAP7fxGErFjhxLOJIB7Z1MREPRCtZ6Tzh5AC6XWa2fskC", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3810789}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "0928185cd544907dab472754634ddf988452aae6", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.41.0_1747546445501_0.9118451399044711", "host": "s3://npm-registry-packages-npm-production"}}, "4.41.1": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.41.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.41.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "848f99b0d9936d92221bb6070baeff4db6947a30", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.41.1.tgz", "fileCount": 3, "integrity": "sha512-Wq2zpapRYLfi4aKxf2Xff0tN+7slj2d4R87WEzqw7ZLsVvO5zwYCIuEGSZYiK41+GlwUo1HiR+GdkLEJnCKTCw==", "signatures": [{"sig": "MEYCIQCvIpLNW2ueTbar/1sjdQ+W1vTqGZHj04rgDvbKkKsXyAIhAOo9cdbSY9cLXcqAdEDly3Tmdi8AQ2irr5JWxinDGrrW", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3941861}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "7c469dc4eb8e1cb6def9fdc04581fdfce9975da3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.41.1_1748067315803_0.5427036443537789", "host": "s3://npm-registry-packages-npm-production"}}, "4.41.2": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.41.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.41.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "7c7c5d4524213e233c74e554c430581e9a989a1f", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.41.2.tgz", "fileCount": 3, "integrity": "sha512-AQ5GvrrLYw6dfkfT/bgYg1NU54exCKDrO9JKOE87zy2mdf2CyI6Vayy41r8Vo+kNDpqacaVQppwvYl30YGJu/Q==", "signatures": [{"sig": "MEUCIQDleGeB73kBT3e8AEcUSNrIViqQ69FAP/de+D1LFjmmJAIgG1QxZ2zLsqfgICETjRq07X4XyFp3p8R1M/3pVVb1pq0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3936229}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "13b4669dbc21cb738551cd725d2a18c77b3cea11", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.41.2_1749210070810_0.36693014333424046", "host": "s3://npm-registry-packages-npm-production"}}, "4.42.0": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.42.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.42.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "516c6770ba15fe6aef369d217a9747492c01e8b7", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.42.0.tgz", "fileCount": 3, "integrity": "sha512-LpHiJRwkaVz/LqjHjK8LCi8osq7elmpwujwbXKNW88bM8eeGxavJIKKjkjpMHAh/2xfnrt1ZSnhTv41WYUHYmA==", "signatures": [{"sig": "MEUCIHWZvdq4vfmytOpgPSpVuQoMm3Yz2HdaPW1aiB24YUgqAiEA0EUJatcaug54KzWvDfjHf8BB6sgLMufARZQKFqWvQzk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3936229}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "f76339428586620ff3e4c32fce48f923e7be7b05", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.42.0_1749221330882_0.8076694071267647", "host": "s3://npm-registry-packages-npm-production"}}, "4.43.0": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.43.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-x64-msvc@4.43.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["x64"], "dist": {"shasum": "42a88207659e404e8ffa655cae763cbad94906ab", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.43.0.tgz", "fileCount": 3, "integrity": "sha512-SnGhLiE5rlK0ofq8kzuDkM0g7FN1s5VYY+YSMTibP7CqShxCQvqtNxTARS4xX4PFJfHjG0ZQYX9iGzI3FQh5Aw==", "signatures": [{"sig": "MEUCIG31kGvWoogFWv/eCuDTQ7scOenMIhrtD9JJsWeRbB04AiEA/yMWYf9S7PNlti0BKoJUxMqSFBhrXhZy2hJ9V8uDVRE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3936229}, "main": "./rollup.win32-x64-msvc.node", "gitHead": "72858cb1474b81c91902794ab7d28c79f34b8ca8", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-x64-msvc_4.43.0_1749619401254_0.7810159096957141", "host": "s3://npm-registry-packages-npm-production"}}, "4.44.0": {"name": "@rollup/rollup-win32-x64-msvc", "version": "4.44.0", "os": ["win32"], "cpu": ["x64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-x64-msvc.node", "_id": "@rollup/rollup-win32-x64-msvc@4.44.0", "gitHead": "fa4b2842c823f6a61f6b994a28b7fcb54419b6c6", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-Q2Mgwt+D8hd5FIPUuPDsvPR7Bguza6yTkJxspDGkZj7tBRn2y4KSWYuIXpftFSjBra76TbKerCV7rgFPQrn+wQ==", "shasum": "a36e79b6ccece1533f777a1bca1f89c13f0c5f62", "tarball": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.44.0.tgz", "fileCount": 3, "unpackedSize": 3919845, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCWRskocxZiao7vfI6wcqCo9jmU1oR55wLPLaYHeL+JlgIhAObBVeiJjaXvIf3E84R7CCmrWiW5ikZHejmOdB1WeRaF"}]}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>", "actor": {"name": "lukastaegert", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-win32-x64-msvc_4.44.0_1750314217660_0.4509604066497883"}, "_hasShrinkwrap": false}}, "time": {"created": "2023-07-31T19:17:44.958Z", "modified": "2025-06-19T06:23:38.116Z", "4.0.0-0": "2023-07-31T19:17:45.166Z", "4.0.0-1": "2023-08-01T04:49:11.165Z", "4.0.0-2": "2023-08-01T11:16:46.052Z", "4.0.0-3": "2023-08-04T08:17:11.761Z", "4.0.0-4": "2023-08-04T11:36:57.141Z", "4.0.0-5": "2023-08-20T06:57:04.159Z", "4.0.0-6": "2023-08-20T07:51:52.107Z", "4.0.0-7": "2023-08-20T10:33:54.140Z", "4.0.0-8": "2023-08-20T11:22:45.903Z", "4.0.0-9": "2023-08-20T14:29:31.512Z", "4.0.0-10": "2023-08-21T15:30:30.086Z", "4.0.0-11": "2023-08-23T10:16:14.522Z", "4.0.0-12": "2023-08-23T14:41:00.897Z", "4.0.0-13": "2023-08-24T15:48:50.401Z", "4.0.0-14": "2023-09-15T12:34:40.802Z", "4.0.0-15": "2023-09-15T13:07:07.776Z", "4.0.0-16": "2023-09-15T14:17:33.254Z", "4.0.0-17": "2023-09-15T14:59:23.183Z", "4.0.0-18": "2023-09-15T16:11:02.933Z", "4.0.0-19": "2023-09-15T18:51:13.359Z", "4.0.0-20": "2023-09-24T06:10:52.832Z", "4.0.0-21": "2023-09-24T17:22:39.850Z", "4.0.0-22": "2023-09-26T16:17:51.733Z", "4.0.0-23": "2023-09-26T20:14:42.500Z", "4.0.0-24": "2023-10-03T05:13:03.561Z", "4.0.0-25": "2023-10-05T14:13:32.737Z", "4.0.0": "2023-10-05T15:15:10.975Z", "4.0.1": "2023-10-06T12:37:01.446Z", "4.0.2": "2023-10-06T14:19:01.661Z", "4.1.0": "2023-10-14T05:52:31.866Z", "4.1.1": "2023-10-15T06:32:06.082Z", "4.1.3": "2023-10-15T17:48:44.223Z", "4.1.4": "2023-10-16T04:34:27.458Z", "4.1.5": "2023-10-28T09:23:50.513Z", "4.1.6": "2023-10-31T05:45:33.788Z", "4.2.0": "2023-10-31T08:11:00.610Z", "4.3.0": "2023-11-03T20:13:23.959Z", "4.3.1": "2023-11-11T07:58:10.660Z", "4.4.0": "2023-11-12T07:50:08.813Z", "4.4.1": "2023-11-14T05:25:59.415Z", "4.5.0": "2023-11-18T05:52:27.430Z", "4.5.1": "2023-11-21T20:13:25.477Z", "4.5.2": "2023-11-24T06:30:06.541Z", "4.6.0": "2023-11-26T13:39:29.203Z", "4.6.1": "2023-11-30T05:23:26.357Z", "4.7.0": "2023-12-08T07:58:20.119Z", "4.8.0": "2023-12-11T06:25:14.490Z", "4.9.0": "2023-12-13T09:24:35.352Z", "4.9.1": "2023-12-17T06:26:27.565Z", "4.9.2": "2023-12-30T06:23:42.998Z", "4.9.3": "2024-01-05T06:21:03.088Z", "4.9.4": "2024-01-06T06:39:16.890Z", "4.9.5": "2024-01-12T06:16:29.497Z", "4.9.6": "2024-01-21T05:52:35.096Z", "4.10.0": "2024-02-10T05:58:59.305Z", "4.11.0": "2024-02-15T06:10:04.351Z", "4.12.0": "2024-02-16T13:32:55.882Z", "4.12.1": "2024-03-06T06:03:58.974Z", "4.13.0": "2024-03-12T05:29:03.525Z", "4.13.1-1": "2024-03-24T07:39:44.008Z", "4.13.1": "2024-03-27T10:28:07.073Z", "4.13.2": "2024-03-28T14:14:08.186Z", "4.14.0": "2024-04-03T05:23:32.420Z", "4.14.1": "2024-04-07T07:36:01.693Z", "4.14.2": "2024-04-12T06:24:02.733Z", "4.14.3": "2024-04-15T07:18:55.584Z", "4.15.0": "2024-04-20T05:37:41.918Z", "4.16.0": "2024-04-21T04:42:55.501Z", "4.16.1": "2024-04-21T18:30:30.960Z", "4.16.2": "2024-04-22T15:19:49.070Z", "4.16.3": "2024-04-23T05:12:58.439Z", "4.16.4": "2024-04-23T13:15:31.015Z", "4.17.0": "2024-04-27T11:30:16.265Z", "4.17.1": "2024-04-29T04:58:17.626Z", "4.17.2": "2024-04-30T05:01:16.692Z", "4.18.0": "2024-05-22T05:04:11.520Z", "4.18.1": "2024-07-08T15:25:38.916Z", "4.19.0": "2024-07-20T05:46:40.601Z", "4.19.1": "2024-07-27T04:54:25.379Z", "4.19.2": "2024-08-01T08:33:19.383Z", "4.20.0": "2024-08-03T04:49:17.052Z", "4.21.0": "2024-08-18T05:55:57.522Z", "4.21.1": "2024-08-26T15:54:40.136Z", "4.21.2": "2024-08-30T07:04:51.341Z", "4.21.3": "2024-09-12T07:06:16.792Z", "4.22.0": "2024-09-19T04:55:57.181Z", "4.22.1": "2024-09-20T08:22:18.177Z", "4.22.2": "2024-09-20T09:34:11.212Z", "4.22.3-0": "2024-09-20T14:48:25.503Z", "4.22.3": "2024-09-21T05:03:36.582Z", "4.22.4": "2024-09-21T06:11:46.385Z", "4.22.5": "2024-09-27T11:48:44.630Z", "4.23.0": "2024-10-01T07:10:44.225Z", "4.24.0": "2024-10-02T09:37:48.335Z", "4.24.1": "2024-10-27T06:43:32.906Z", "4.24.2": "2024-10-27T15:40:40.227Z", "4.25.0-0": "2024-10-29T06:15:42.353Z", "4.24.3": "2024-10-29T14:14:40.761Z", "4.24.4": "2024-11-04T08:47:39.294Z", "4.25.0": "2024-11-09T08:37:53.876Z", "4.26.0": "2024-11-13T06:45:29.858Z", "4.27.0-0": "2024-11-13T07:03:44.562Z", "4.27.0-1": "2024-11-14T06:33:41.728Z", "4.27.0": "2024-11-15T10:41:06.011Z", "4.27.1-0": "2024-11-15T13:28:40.899Z", "4.27.1-1": "2024-11-15T15:38:35.282Z", "4.27.1": "2024-11-15T16:08:13.282Z", "4.27.2": "2024-11-15T17:20:37.004Z", "4.27.3": "2024-11-18T16:40:08.138Z", "4.27.4": "2024-11-23T07:00:49.950Z", "4.28.0": "2024-11-30T13:16:17.874Z", "4.28.1": "2024-12-06T11:45:30.048Z", "4.29.0-0": "2024-12-16T06:40:28.313Z", "4.29.0-1": "2024-12-19T06:38:02.484Z", "4.29.0-2": "2024-12-20T06:56:37.231Z", "4.29.0": "2024-12-20T18:37:58.292Z", "4.29.1": "2024-12-21T07:16:37.178Z", "4.30.0-0": "2024-12-21T07:17:48.333Z", "4.30.0-1": "2024-12-30T06:52:51.896Z", "4.29.2": "2025-01-05T12:08:18.109Z", "4.30.0": "2025-01-06T06:37:11.138Z", "4.30.1": "2025-01-07T10:36:26.530Z", "4.31.0-0": "2025-01-14T05:58:15.637Z", "4.31.0": "2025-01-19T12:57:21.879Z", "4.32.0": "2025-01-24T08:28:08.356Z", "4.33.0-0": "2025-01-28T08:30:41.968Z", "4.32.1": "2025-01-28T08:33:49.077Z", "4.33.0": "2025-02-01T07:12:34.583Z", "4.34.0": "2025-02-01T08:40:55.460Z", "4.34.1": "2025-02-03T06:58:45.933Z", "4.34.2": "2025-02-04T08:10:37.679Z", "4.34.3": "2025-02-05T09:22:39.602Z", "4.34.4": "2025-02-05T21:31:44.963Z", "4.34.5": "2025-02-07T08:53:38.303Z", "4.34.6": "2025-02-07T16:32:41.782Z", "4.34.7": "2025-02-14T09:54:32.373Z", "4.34.8": "2025-02-17T06:26:58.452Z", "4.34.9": "2025-03-01T07:33:12.219Z", "4.35.0": "2025-03-08T06:25:24.081Z", "4.36.0": "2025-03-17T08:36:21.121Z", "4.37.0": "2025-03-23T14:57:40.314Z", "4.38.0": "2025-03-29T06:29:46.327Z", "4.39.0": "2025-04-02T04:50:10.701Z", "4.40.0": "2025-04-12T08:40:12.859Z", "4.40.1": "2025-04-28T04:36:01.196Z", "4.40.2": "2025-05-06T07:27:32.235Z", "4.41.0": "2025-05-18T05:34:05.730Z", "4.41.1": "2025-05-24T06:15:16.043Z", "4.41.2": "2025-06-06T11:41:11.059Z", "4.42.0": "2025-06-06T14:48:51.105Z", "4.43.0": "2025-06-11T05:23:21.516Z", "4.44.0": "2025-06-19T06:23:37.870Z"}, "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "homepage": "https://rollupjs.org/", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "description": "Native bindings for Rollup", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "readme": "# `@rollup/rollup-win32-x64-msvc`\n\nThis is the **x86_64-pc-windows-msvc** binary for `rollup`\n", "readmeFilename": "README.md"}