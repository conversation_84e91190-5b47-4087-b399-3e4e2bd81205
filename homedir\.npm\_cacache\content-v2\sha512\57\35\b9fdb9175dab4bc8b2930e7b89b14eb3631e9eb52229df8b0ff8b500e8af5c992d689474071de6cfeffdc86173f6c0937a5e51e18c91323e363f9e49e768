{"_id": "@hapi/hoek", "_rev": "70-40ae27e600611f9c914e567ffdefb364", "name": "@hapi/hoek", "dist-tags": {"lts": "8.5.1", "latest": "11.0.7"}, "versions": {"6.2.0": {"name": "@hapi/hoek", "version": "6.2.0", "keywords": ["utilities"], "license": "BSD-3-<PERSON><PERSON>", "_id": "@hapi/hoek@6.2.0", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "marsup", "email": "<EMAIL>"}, {"name": "wyatt", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/hoek#readme", "bugs": {"url": "https://github.com/hapijs/hoek/issues"}, "dist": {"shasum": "7e36b3705aaf67cdc2680eb8e532e699187092c5", "tarball": "https://registry.npmjs.org/@hapi/hoek/-/hoek-6.2.0.tgz", "fileCount": 8, "integrity": "sha512-wrzOkl3WYlbIGhDLPf4dYHp2IACrqIbuteZPtdhUgjIxjwjPfavS1PBlmpg2cEss7bV9w/QdsY1cHj6GryDiXw==", "signatures": [{"sig": "MEUCIQDzMJsluF87GKmHGk9mE3Q8qvdGORNg/6LK40KH1ZLpVgIgURRV7+PkPcNREK8aGxpP5Y4bJNv0N6w5a0uMDAeRoEQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31015, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcmsTnCRA9TVsSAnZWagAANBkP/2kEuS8f93bD05c2yLkG\nRcylKm2UUXc9RmgfEGA66mLdD2VjC41gi87pcUXMXq2YnV8AAdWI63Ad/3mw\n97OXiZmfKRtMc3eex5pLnKM5zH2A0WU6ftetQ2emBni+F/JotqQHykoIofDp\nT1ZguEJ7MyVYF2vZGgod9NhOTfovD5OTyGsYKxJMX33oKte5vAvbIOfikEfB\n/HftqbQ02SicBjeMSDJe0azibutuVe+D5WSflQU8KCwdVq0o8mM9iu0qfdA7\ngILb5Mts1K5LZ5NWuYOKmG7heebUsqxHEp/WEwmgB6LQpgZwUvzqiEwe/kZO\n4X7TzIUP8ALLo9NC5FeTqVeLdq/pIhp+PUC5y93JRClobE+4cGiYB4qZ4H4e\n3hn95BJIl+OMGQqFE+bxxYEbuBdtgA/ZbjpebAqnLKs5FqSuXL635jc2BXi/\noJ2tfS1dmXEqhxO4Gc+txStN5eObiaHoVkxMea+4UvF2pmfQItT2xxUVueKA\n/Ncb5/WH/FykwtrXxJdVlZveUQN2ZJKSrNCyO18acaIp+3E3+XBF/5MgA1TU\n1vzQsjJhFAorE2eGljzhBJFV3vPZkL+qnGpPtw2P/CJlDw0Zq+TCdYc43WXc\njDjoFny6Fu13vfLFmLtXaZadSPtnjLz7nDquItY3Vi6jdi66eA5w+VeRjpCV\nUrZG\r\n=BmkK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "94d57bbd714471394a62a20e9b691b4e879e95d8", "scripts": {"test": "lab -a code -t 100 -L", "test-cov-html": "lab -a code -t 100 -L -r html -o coverage.html"}, "_npmUser": {"name": "hueniverse", "email": "<EMAIL>"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial).", "repository": {"url": "git://github.com/hapijs/hoek.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "General purpose node utilities", "directories": {}, "_nodeVersion": "10.15.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"lab": "18.x.x", "code": "5.x.x"}, "_npmOperationalInternal": {"tmp": "tmp/hoek_6.2.0_1553646822428_0.725197802083742", "host": "s3://npm-registry-packages"}}, "6.2.1": {"name": "@hapi/hoek", "version": "6.2.1", "keywords": ["utilities"], "license": "BSD-3-<PERSON><PERSON>", "_id": "@hapi/hoek@6.2.1", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/hoek#readme", "bugs": {"url": "https://github.com/hapijs/hoek/issues"}, "dist": {"shasum": "d3a66329159af879bfdf0b0cff2229c43c5a3451", "tarball": "https://registry.npmjs.org/@hapi/hoek/-/hoek-6.2.1.tgz", "fileCount": 8, "integrity": "sha512-+ryw4GU9pjr1uT6lBuErHJg3NYqzwJTvZ75nKuJijEzpd00Uqi6oiawTGDDf5Hl0zWmI7qHfOtaqB0kpQZJQzA==", "signatures": [{"sig": "MEYCIQC8ezwYCPil/8li4Pys2y6R5Qm4itQlxDE9DhN5oWcmlgIhAIwC9HDF8U86Ha91YjaM71tcOjzrrcxpdOj3NNTj74+n", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31039, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcnpvOCRA9TVsSAnZWagAAvjcP/AkPO5r+LeJ9L6sye9+M\nE92tVgSHEuyFoz0xC1xkOEvOzLmmtSingbj7fG65CwMV8UyIiPIFEzxvWhB5\nHef7rBowDL133Px44vNpGnBq9z7cM0gLKzPClrFYkzwKrc7uXE/Cajxj8Y9d\ncU1+2blDQPdsQGKsPD7szaIO/CthvxyyuhYAuMAqq15J8IHQgrxNujYDFi4m\n1LgcCvV5QDPV1sV4+YMh+v7yKKiGaSqXMRSoaLQCZKOdChKvOv8dhbbnBT9w\nML0m8DBfHfnjTBEzDWyJKcNEWJigj1SZv++Hg50AQceOh1DhmitvFOCbC/ep\nzj5VVcXqWR4PB1VqDHzivkQqY2o9TMxn0vmPkEa3U6ty8q2L5p8p0fkpjCoL\nw8gfuP5z3ZPSnuycqXINH2AdswGQFfkTZqy8gZb6fV75fL4HqUx5V/pfitUM\negjnZaiQXToCYCf8Xie0mdOTitfVdfaS925lh4z0omDxNupBof7xSFDbJOIH\nEjL4VJGcVzOpCa5rkse80eM27SdJr3tDLRh3zTK8Gbvv5OV+0HTKb8DGnwbE\nT9gEMxYOsZ3mEA6R/La6wcytweTrUT/dy39/ARkVHE4xr6dAqwHB/GgH+nKM\nDWFl57vwzJhUDzHdnS+VoI4zxPYRURnnDgzTvbgvraJ03XaoV7ldoaZgXrTF\nIV6z\r\n=D9vH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "02419083d7bd7b2ea323081b57fddcd9cb1d2422", "scripts": {"test": "lab -a @hapi/code -t 100 -L", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "_npmUser": {"name": "hueniverse", "email": "<EMAIL>"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial).", "repository": {"url": "git://github.com/hapijs/hoek.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "General purpose node utilities", "directories": {}, "_nodeVersion": "10.15.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"@hapi/lab": "18.x.x", "@hapi/code": "5.x.x"}, "_npmOperationalInternal": {"tmp": "tmp/hoek_6.2.1_1553898446291_0.033318095815722426", "host": "s3://npm-registry-packages"}}, "6.2.2": {"name": "@hapi/hoek", "version": "6.2.2", "keywords": ["utilities"], "license": "BSD-3-<PERSON><PERSON>", "_id": "@hapi/hoek@6.2.2", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/hoek#readme", "bugs": {"url": "https://github.com/hapijs/hoek/issues"}, "dist": {"shasum": "75eac1901861b48fc835976b4480b6522ed8f6e1", "tarball": "https://registry.npmjs.org/@hapi/hoek/-/hoek-6.2.2.tgz", "fileCount": 8, "integrity": "sha512-092QtwNnYHTWdsmonjzJhFwvRv+V4ZybM1LI/YlQ6GWQo3mkBZw7bWmmceTwk1sZaiHnuJVcgZL5E+ll97OTuw==", "signatures": [{"sig": "MEYCIQCPVAEl7w/9idFv9876/vjhtWxzwFA6AQpIRdWCOXGtmQIhAKZcivqUxG9r2H3isS1LKzhPrj+8dR1lOTs5uqD4OlFo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42382, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc3xVHCRA9TVsSAnZWagAA2tUP/1etth+OYFlfo8jN3a/W\nU68cZjyce7ZTpXTgTsG9zGSAwXDN5ku/ymErZvHeuyjoFLyEH/mgVqjEzEuM\nBYP8atG0BM42MbvDkxAZX3+5WW0PHuPRKEnhbdEQXnlwiXzOWV4uGLtrkeS2\n0Kkfi3Cd5HlYVihvtbtL23M8L3kIrU8iZ9lkqVyvxVloIpmkqH5sI90UdShs\nNba5ewlUPCgWCt+Jz3RpreHSPTk8g9x2HJbCEalmgaV6XlBVEAt3spumnXIz\nItbD7FSlyvsk1v5ibn1720waPz2EGC1a+PPQTKKsOgwIlaW9gY8mwn5g3U2B\nkQNObTIcWYkR0dnfSGDB6eP+UxxBPzGg/4q/mZZna1Ewkj667duvEvB9Hcgv\nfauaco1m0Ov962iLQXgjGC/WmKVQaHeZeGXfKyJzIULYmsvsjav5RB7RERbp\nZ+qCdVZZrMy3W/923vC3+hMC1d7A5BMM8u7zvJnNb7wmHS18dJjLBvH5IEZ7\nSKcImznRcrVRLZEnCWhk31Moum7HzhqnputUoKQBP/Y18ZLKRBrZJsXnOFJ3\nxV0sUYdPXKD0WyFnh8RyS6zkmnrIOfUqfndgeq5Fggs8ioMtjHKogX3nV0/O\nFxDaIlV7CkRfOWNeoVRp7Re13d2JraqU2edYM9RjMjTGH5OlXFF9K91b1ieY\nnJZ+\r\n=DwrX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "3c9abc2e1cba8524dc36fed0354b7f00744dedc9", "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "_npmUser": {"name": "hueniverse", "email": "<EMAIL>"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial).", "repository": {"url": "git://github.com/hapijs/hoek.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "General purpose node utilities", "directories": {}, "_nodeVersion": "10.15.3", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"@hapi/lab": "19.x.x", "@hapi/code": "5.x.x"}, "_npmOperationalInternal": {"tmp": "tmp/hoek_6.2.2_1558123847209_0.2752006580058588", "host": "s3://npm-registry-packages"}}, "6.2.3": {"name": "@hapi/hoek", "version": "6.2.3", "keywords": ["utilities"], "license": "BSD-3-<PERSON><PERSON>", "_id": "@hapi/hoek@6.2.3", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/hoek#readme", "bugs": {"url": "https://github.com/hapijs/hoek/issues"}, "dist": {"shasum": "0abff7ac75d0c5388d2829c464b2aff74d473721", "tarball": "https://registry.npmjs.org/@hapi/hoek/-/hoek-6.2.3.tgz", "fileCount": 8, "integrity": "sha512-CtV9cp35+6Sfh6OfB+AYBozNIorZ6npNJjfO8InIyh/iFQI7uBW9bIApYoYf6TWq9w9BArecw2DDJf7oK+VlRw==", "signatures": [{"sig": "MEQCIAK+JlfNoVe8l6mvYTz0nNppM+ywzTHENAX/ZeYSqbA9AiBpD+U+ACdyINf5aZ6JWCfPHxycARwoLg4SBvT7gTUSpg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43548, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc3xZVCRA9TVsSAnZWagAAsiMP/0QDxlltzw8WCaMoMvBA\nvPZ7f95rJFruQSY2eNteZu7K7y/dX+abZ9s/JxrsdTShmtErIWErFdjyISki\nViXGnxiQH43Xkhoxw+0hbl/Iy5d6fHLDQ8oQV1s0sGRIZQC8jt8HDPNyskJd\n+WuBlywxsgEnCD9hzQQDmog031gs+yeGpJF1CJYhe6gQM4/0Kdp3sMtHrdhO\nLXVD+SLV+XDapLsuP2delI5StISxVTNyA3JSwO14NpJur4gkTTzOtnN4BE+U\nTNi7FCNu5SU1zi2VhKAzXYBtwCFCR/od//6M27Zfkd7rjEDMG0HlsSzHsJLv\n60CWqA3+P/+aXLbn5DsT5fCM6wkbUhHw/v2ay57ZjD9ahd9b7i1wOL1r9v6E\nB1O9K+Q+b3GRisRCOJIHDhGdbK+mSsqpsm4BC1drkMutHqqmHRZL7dp92A8e\nREOR/KB06fK/ZBltfjQ2CuFf90wxXQZTNK4A7+AtgrD6CfuPjyimgtC5+zrC\nKwjSDqZHZYm6bva5rF1++BTZv0fNhWJx3IWkcdGIN4DN//Hi7eQ0Uu+7ycic\nvsu1VH2J4dn2mDM7uHQh/XZLp1aDH4oefEopIeSd6IPmUbdr/aBH4NmUZ8dF\n5rPRQ8SKD1zZ1LkbS7fDRipgshAXzCFuVk4ZmzhxS2xes3ZNLKPbT2IN+tVn\ns1Az\r\n=NyeZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "e31aa0cc14f7c1a7e6e3d8cd30f4b7af755e7b22", "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "_npmUser": {"name": "hueniverse", "email": "<EMAIL>"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial).", "repository": {"url": "git://github.com/hapijs/hoek.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "General purpose node utilities", "directories": {}, "_nodeVersion": "10.15.3", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"@hapi/lab": "19.x.x", "@hapi/code": "5.x.x"}, "_npmOperationalInternal": {"tmp": "tmp/hoek_6.2.3_1558124116350_0.5305697446287283", "host": "s3://npm-registry-packages"}}, "6.2.4": {"name": "@hapi/hoek", "version": "6.2.4", "keywords": ["utilities"], "license": "BSD-3-<PERSON><PERSON>", "_id": "@hapi/hoek@6.2.4", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/hoek#readme", "bugs": {"url": "https://github.com/hapijs/hoek/issues"}, "dist": {"shasum": "4b95fbaccbfba90185690890bdf1a2fbbda10595", "tarball": "https://registry.npmjs.org/@hapi/hoek/-/hoek-6.2.4.tgz", "fileCount": 9, "integrity": "sha512-HOJ20Kc93DkDVvjwHyHawPwPkX44sIrbXazAUDiUXaY2R9JwQGo2PhFfnQtdrsIe4igjG2fPgMra7NYw7qhy0A==", "signatures": [{"sig": "MEUCIAzSLkm6JdFyK9EsvbmQeVTmlvnwUn1jJmtHRRA+3nJMAiEA48DAVjnzPj/pTDUKJwJGSvkfW37ukhvpHl2qPEVSKZc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43083, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc6zLRCRA9TVsSAnZWagAAYZIQAJAutNKfgXYPAli2Yukq\nu6mLy9sziX4pLzsMXniU6tbICsSj6yQBFBd/KyAjfWiAiDzRHQYP1whlLxAz\nzvM2Y37W/YqGug2nlqlz2nCPIURIa215lIdmZA0aZDPqGWKpA+Fl5uoJ+Qbs\nxJMXQ/xwITx5+IFUqNuSiYA78lv4GI167n53quiZ4X8DDvhhwBA2JE2OXhlW\nXHtF6FjCwCyNtasacBLa61uUpW4nQOYTpTCiLUPAV4TNTDWTf/ezWMvA7EyO\nWVonXenMMKbzCUwzinKYbADqDy98GgOucfJluqnsS08k35kYGW/6dWy9Hyob\nWzR0VK4rjTzQ9biRUdY/vEH4rPyzrdfTjdXy7FihO+Io+6P532e0j9MDzVPS\nAVkWCnh9KQWJFHH+tQ8hBKDD7+5HBQIZBuLLrL+8PI/G2G9QgtZmxx/nO1qZ\nPa4oBJ3VA1gqMQrJTp83Fye6lTWLdQ7xrecKFPtSngwty3cKTTAqP6BtTQvu\n1acTaRKB2H9wMaCLUro6RBvZyzRv1cw9DcJBG+MEY8MJ0Ub1HKbHBd4g7xP5\nnlDVc8YFn5VZPoFLzTAnUnk6Vi+WBRIuPlbbzZPpLA9pcq9IpbX4dhkVBEln\n19rQHUN+5ELVskKHfCVYjdfeJ6TYyRYSPhyTjTMW4TOtaUMelfBrUw5lCyCM\n7Tq/\r\n=y9mh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "d79b77ddbed807b5c1544c74abc593d0b8483a49", "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "_npmUser": {"name": "hueniverse", "email": "<EMAIL>"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial).", "repository": {"url": "git://github.com/hapijs/hoek.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "General purpose node utilities", "directories": {}, "_nodeVersion": "10.15.3", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"@hapi/lab": "19.x.x", "@hapi/code": "5.x.x"}, "_npmOperationalInternal": {"tmp": "tmp/hoek_6.2.4_1558917841039_0.0543825094413708", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "@hapi/hoek", "version": "7.0.0", "keywords": ["utilities"], "license": "BSD-3-<PERSON><PERSON>", "_id": "@hapi/hoek@7.0.0", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/hoek#readme", "bugs": {"url": "https://github.com/hapijs/hoek/issues"}, "dist": {"shasum": "fdfcd5ea8d039886f950d14946dd12f6c59b4c04", "tarball": "https://registry.npmjs.org/@hapi/hoek/-/hoek-7.0.0.tgz", "fileCount": 9, "integrity": "sha512-tYjMLfZ0OPBr+IcMwObQJx3LISuixRTxKHkMfzR90J18LYxuPz6+tOb5SO994u+ncOu4hSULeOSgBwOCmh/Vjw==", "signatures": [{"sig": "MEQCIFHkg9oqwlKcy6bLxxYtPQ8wfWKfZenTJgK0bAWk6YpRAiB6srxONPyt9RyTRM8a8y8UxyFBdKKVNl26l1f0CzfwsQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43100, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc8Z7ACRA9TVsSAnZWagAAIzYP/2vTSozQPiFNv/ysVSHF\n4kJiSEfLK4GnFlHm3aRnwZQiHNxzhMXLaGHZa14/tzFDdR1i8yL/OFVs4Jz4\nC2ONHPxEf0TPk/8z0VPj7T5M7Fdn05ArsFt6v6/lK1/dexQSThKUo0wbNB4C\n9l4EG+B8+ynAvmATOrT87/2R9Qt088R/T7Tn6AU2giDSDYlHCdJSoNfh4Eo5\n1f+pZSkOGsYDwsJ/KYLxbPlZZcWa0MJD1c+bLjJpXoObhxugkiXNP4305p7P\nNokSBGQK89WNDsOoYGpXTbYC8rVlvJifVpqVer89S9r/aMcoV9YxuNURkZ8m\nbAPe6AoOzfn0PYgCra6rTU3Tw4oqqWwaFFds8T0tsiQkdjyOwseIgE0w/rLu\nDauqf+s2H8ycgeY9CkWmPpxG4ZWi/vASeZkSxlWNO3c3ucj2QjYBskKHcS75\nanaQcyu3fNoUlmX0CRNYkA2GSED91qbm76mPXTPqLSLAn9Zf8sgo1bb/ymtm\nLquSJES0ZAQzv+2nhH091Zxi/4o7qiZvPD5cR+UKY8Ikagk7vNFNFQHkV7rx\n63bKP3hRwkMvZXs21VVo8YKTCjVNxbb805GjDpFR7zDf801apQnsdXvss0Hz\n95Q+umCNL9mN2ONq/HOenCfzU+H6lg1C/Znhj8mjxGWSt4FmmRUQkK9IjPAC\ns623\r\n=RbFE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "8a3545eba5eec1366abcb83324271c01381e0497", "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "_npmUser": {"name": "hueniverse", "email": "<EMAIL>"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial).", "repository": {"url": "git://github.com/hapijs/hoek.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "General purpose node utilities", "directories": {}, "_nodeVersion": "10.15.3", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"@hapi/lab": "19.x.x", "@hapi/code": "5.x.x"}, "_npmOperationalInternal": {"tmp": "tmp/hoek_7.0.0_1559338687544_0.6590035173406634", "host": "s3://npm-registry-packages"}}, "7.1.0": {"name": "@hapi/hoek", "version": "7.1.0", "keywords": ["utilities"], "license": "BSD-3-<PERSON><PERSON>", "_id": "@hapi/hoek@7.1.0", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/hoek#readme", "bugs": {"url": "https://github.com/hapijs/hoek/issues"}, "dist": {"shasum": "0031ea68e5b925536b6c52b7e758686265755c67", "tarball": "https://registry.npmjs.org/@hapi/hoek/-/hoek-7.1.0.tgz", "fileCount": 9, "integrity": "sha512-jBTPzWrWQAizq7naLVwU+P2+TzVY3ZtPSX+F9gwW23ihwpihpYKvjN21zHKUjaePYS9ijlDF3oFVNbGfhbbk2w==", "signatures": [{"sig": "MEYCIQCtCE3rBMW9UAk6Edho/ro8hCJlDaf1HC06r2KPnEu9MQIhAMnUu/FcNT8+xitHgM4+jBR5Zp8QTTo4UXU2wXDEU32M", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43264, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc8rqKCRA9TVsSAnZWagAAS+YP/ifYO6qLJ1BIQQwLc6Lw\nQ8YlWV16wH1zNHHAQ99kvk+0g1nWY7BN1NyNiVL/6NaMu+POFnpY9sbRTs7c\n1H7Ga7+upYUas1YDJA5mXp+KbcsBMZyfvNAdOzhcVa62ldW4acYaCfst4bEx\n/FitxwC5dcTsJi/9qTM1r/ar5HC8mXaYwSwJRHOD0CfzU3bQWCv4jBrpqBqR\nDnGMxCkeeszB0V2aQATc0Vo/TsJ5JSQJHTXvEGp7+LNz8aaAS2xwf++SZswc\nDcEFUNk6ehp/af3TfsErWw0ie69LSjqukd1NJ4UcoNnwdWCsJYOWOn4RjYzB\nzPS6q52GrIzV7GrN1tBbQic5mzKETELwmUWPeNR2o6dihyRYdzXLW9sLdr4k\nnf01EIJlJMRVtM0KVjcZQ8u1pfJSA1IBT1OLFBLnDc8twkpHsw9bkw/xlRBw\n7+iV8W7gRYwvhZE8J5STVkKaw4zoaxGWtBNLutf4rK/oDhMu98HUwd0UyIbY\nUTY/Y3PGA+2YcgfRKzro4FpMMxHSoj+1j3cmMB33Uz5Ebfw1U8zMHz9JReVA\nRIdEivHrknPHUgf0bSGidjT985zMMXzfTqO2+9XNjCsPl+S728owgHcauPbU\nIppLtlnI9bd5id/aVzE/w+m+oPfIBJUpKhMrvHSpV8blHJWJXluA/CSbyLOU\nxsV7\r\n=PMQW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "a31c46dad33af6b16a5c86319395569f32df63aa", "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "_npmUser": {"name": "hueniverse", "email": "<EMAIL>"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial).", "repository": {"url": "git://github.com/hapijs/hoek.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "General purpose node utilities", "directories": {}, "_nodeVersion": "10.15.3", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"@hapi/lab": "19.x.x", "@hapi/code": "5.x.x"}, "_npmOperationalInternal": {"tmp": "tmp/hoek_7.1.0_1559411337843_0.23094497669423708", "host": "s3://npm-registry-packages"}}, "7.2.0": {"name": "@hapi/hoek", "version": "7.2.0", "keywords": ["utilities"], "license": "BSD-3-<PERSON><PERSON>", "_id": "@hapi/hoek@7.2.0", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}, {"name": "wyatt", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/hoek#readme", "bugs": {"url": "https://github.com/hapijs/hoek/issues"}, "dist": {"shasum": "e59001a9f4f0924ab30d51b3e98592f2c2114a3c", "tarball": "https://registry.npmjs.org/@hapi/hoek/-/hoek-7.2.0.tgz", "fileCount": 9, "integrity": "sha512-rRKo8M11Mvj/mq6gl3yyIYY87O7Zkb1jfFGG/G65j4VE4rm0DwA+6ppgfqhzzK+3sWXjPvbZ3DKCtRP8I4YHMw==", "signatures": [{"sig": "MEQCIGSbNNwRj0yffEEm7vMzkaatazoHIkuF5VkF7RwpdeEXAiBqWs8DoWeu3CkFdEWQT4XLL+MAf0b3NgsBYXhWnCdoVA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43874, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdDaVYCRA9TVsSAnZWagAArf0QAIJSSXLAMQ4OHWrpSn4y\nMC3h3LWtyjccnAh8h5Buwn50MCAvrCIb+Ny7IVoHdr/0/ow6uo1oPhkUHWGM\ndp0iWmKeDsXNA2b3N0KB+q5F33PLa7vX9cGTkRu6zxAnvCo2c6tdTwIKUmC9\nyIkfueTlOj48yb+qvkScO6aKY3kqUomkdWBInuN3f9sSEYqrnhBAG41VNvQ8\nbDL36aI+/6hfH/8ITNKkVwUE7nJ+PbX1sgGfl6vYxRx/24RMUc0tWLMFYYEE\naO61GP22LkOp3NJDRAZWuHpR2xd4/gCnP+a326ey+XLmoBozc9NhuyWg+AYQ\nIng5bdJZ71S1DpZCscc0DnMNmxCMhi6zzdAIK4S5n2r7Q1B5NXWRJTRutVGE\nZDT9czAkahzjSm5lg3kz3UVOIcPIaaDlCu3xMgzlygy3ne6bQPHPJkCCjPuV\nlnoN3xtJaeoFingsR/i0LUlMfIcNBLRNn/z4SGH0cUbU2tVo7CVjlw3FyBh8\nE2PcWueZubPFl7ONyLjvPZF6P3l/9nTWOeR+d1CK/aQdocvMKPHPZfCZsXxj\n66dxHkT1EkBWCLjcS6I9ibrnEdSELohPRRKERlcpeMZLBOz+E/L5O1aObjOg\n1/yBZw4x54MT2db2cigmvhHLsZZXSfc2gZoGDiUUF4jnCU+d8rAUTDSAcse3\nn3qS\r\n=RXY0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "5112e4adf312e719141f6288c5ea1c667dd22af3", "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "_npmUser": {"name": "hueniverse", "email": "<EMAIL>"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial).", "repository": {"url": "git://github.com/hapijs/hoek.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "General purpose node utilities", "directories": {}, "_nodeVersion": "12.2.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"@hapi/lab": "19.x.x", "@hapi/code": "5.x.x"}, "_npmOperationalInternal": {"tmp": "tmp/hoek_7.2.0_1561175384041_0.0015555320674274231", "host": "s3://npm-registry-packages"}}, "7.2.1": {"name": "@hapi/hoek", "version": "7.2.1", "keywords": ["utilities"], "license": "BSD-3-<PERSON><PERSON>", "_id": "@hapi/hoek@7.2.1", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}, {"name": "wyatt", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/hoek#readme", "bugs": {"url": "https://github.com/hapijs/hoek/issues"}, "dist": {"shasum": "14c4a1ae8795472c2d5e27ee61fd7bed1b8de226", "tarball": "https://registry.npmjs.org/@hapi/hoek/-/hoek-7.2.1.tgz", "fileCount": 9, "integrity": "sha512-X6YzLoU+VvZwUNe0VFJV/r4IiFHf61/6VItdnKjlay+YS/5qoczO3u/7wyTj2NtaOZHlFJBndNkfZ2Ag2XxCsg==", "signatures": [{"sig": "MEUCIE7UpwG9jn3h+enlwFqO4ezXM/kgFqYKUVfoWhXtHoI9AiEAiItuBo49dBHISJBGc7o3wktM2fiueT/Eii4yeS7WlXI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43786, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdDvukCRA9TVsSAnZWagAAlPMP/RvVOzaizs9wOwTcK0f6\nrZuo+xu49zORcTsmBghup98f4igu4oo58TC/07yfDOtKy4vvQ8sQbFp/GYhG\nC8RIqVdZheVIQ1qglx7VuEg9cxCXty5RluR6dZ4ZRS48UaHZ+3U0shYiww0i\nvtrZrM2IrdzvUzweQljcID+6CMJJFjWJQzNSAcvt01fWgvruwCV6woWFN6Aa\nrHlfqp9VKFimP5qKevWonpJAWR5yjvx9BQTTEs/9p0IxqF0lzk3Iu0prlyln\nWqGnL1EdbUn0pJY8Fk9xy4sFBWHX1HCIXq4U77hi84/5y+HXv8H+C4MnYmrt\nsT3WJTJck5U2lqn0H7S68DE0hk9UQWmW5w4PkdGMCCZoPaeGmARGJ0nsQFwz\ns6+mxUAi7/1AqKAqv6QsVhmOip0yiA7lTf1t1w82pbD9Cm5Z4q6jx/RNiG+9\nCSgiPTAPYb75hx4v6CASFaOw5tY/Y4Eu+cPtK041VFYGaAb8EcVhPFDvL21I\noh/1DIZsuozOoz+j01kwjiVyw1r0pZhHT+o+qC8hbFSUnVbl/qA3bY27Np2r\n8QNr4dj9lA+/K81G6/RKr5Z6GIb6DgiAOEKTmQWEeXLwqACgs5aAN9ikhLjv\n1QFH3SH/kUYMNEoEEpPtgvIN4zxhlfw48k0GgdUf/CZa6Y1urDWfn63gXKr1\nt3iY\r\n=MOCE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "352276b276e230c73fb292c1454adfd585e59da5", "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "_npmUser": {"name": "hueniverse", "email": "<EMAIL>"}, "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial).", "repository": {"url": "git://github.com/hapijs/hoek.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "General purpose node utilities", "directories": {}, "_nodeVersion": "12.2.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"@hapi/lab": "19.x.x", "@hapi/code": "5.x.x"}, "_npmOperationalInternal": {"tmp": "tmp/hoek_7.2.1_1561263011616_0.6059563680606224", "host": "s3://npm-registry-packages"}}, "8.0.0": {"name": "@hapi/hoek", "version": "8.0.0", "keywords": ["utilities"], "license": "BSD-3-<PERSON><PERSON>", "_id": "@hapi/hoek@8.0.0", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}, {"name": "wyatt", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/hoek#readme", "bugs": {"url": "https://github.com/hapijs/hoek/issues"}, "dist": {"shasum": "5427a529a3ed5f0e3879406fcab5676253ef4285", "tarball": "https://registry.npmjs.org/@hapi/hoek/-/hoek-8.0.0.tgz", "fileCount": 9, "integrity": "sha512-e+POliOm77S3COl6k9xFKyRmukuZ2ydiwpELg/pVmltNJWD0KSynWaEnRZsFJXtuRrEW/GvfhWwDPqL1gUIGFg==", "signatures": [{"sig": "MEUCIAxBDC52MbwyDUpyweI3M3IwxQ/GgrLZDvLJKYZGPuDLAiEAwlC7DZwMhDrneI1JoDFcGKWo5JYILDvk7CCvJOj2AKk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42947, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdEwk6CRA9TVsSAnZWagAA75UP/Ro+vAysOREXmFhus+fv\nga1ONmsh0JcKtyzM3WT0b1FKAi7M8b6X9hzM2TomxeF7kVa3O2I1Xk3Bgyb/\nwMB9zMy1BY6rmoC8QLe5gFDg4PxZuiv9o6KTljCwnkfMOR8k+oJINfQXkqa1\n0xiAUMO3PY6hWEeF84kVvxdKoYV98ziIWlFR5LtCG3uYeU42LhK0E5W56c11\n26zvnznWKRMYQYXvMVHs4JQ0I+p6KClL75Eq2YdKtZaZXOos4jmbbaVgJ4EM\nyJsi5PnomCZolszEqh2pR09pb3BSzzT6DHcnr4XkuIcQTIJryYIoDEYrec6x\nQTF2lhc6QYuRbNTmdZQxVnI+GIzKuXJT9QWNBzeDZqHjeffzKfk328wOpgYO\nr3N6aqYQJCOSuo2LUAq3982pi1Q/vrPlMIANWfI5AwLkWhOQLBrtm3zxgF9V\nVmtYTz8h5FWd0U+Ptzj8Z2x8noc6qta6pVSonR8GStkNCeaIZzilnXzpOvIp\n1jztw6kreygjiiF1/9a7yXpZC09fNich3zEETTS7yIGIAb1PZCKEwM+WJJ9j\nwGtjTinCdUAFZt6TQbH+BbPRetoqE0N+gOVkJIHgb1S0qAmLelI/doAx1gp8\nKlmdBFSjAoWiEYpqOt+Nt266OlB/fDdnnwEf5uiymBmGY8dVdF24zS6PuV2q\nhNlr\r\n=gS+F\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "b2178b72bb1e298d21eebf29bf9b9cb0c4344b4a", "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "_npmUser": {"name": "hueniverse", "email": "<EMAIL>"}, "deprecated": "This version has been deprecated and is no longer supported or maintained", "repository": {"url": "git://github.com/hapijs/hoek.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "General purpose node utilities", "directories": {}, "_nodeVersion": "10.15.3", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"@hapi/lab": "19.x.x", "@hapi/code": "5.x.x"}, "_npmOperationalInternal": {"tmp": "tmp/hoek_8.0.0_1561528633690_0.498586125725095", "host": "s3://npm-registry-packages"}}, "8.0.1": {"name": "@hapi/hoek", "version": "8.0.1", "keywords": ["utilities"], "license": "BSD-3-<PERSON><PERSON>", "_id": "@hapi/hoek@8.0.1", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}, {"name": "wyatt", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/hoek#readme", "bugs": {"url": "https://github.com/hapijs/hoek/issues"}, "dist": {"shasum": "9712fa2ad124ac64668ab06ba847b1eaf83a03fd", "tarball": "https://registry.npmjs.org/@hapi/hoek/-/hoek-8.0.1.tgz", "fileCount": 9, "integrity": "sha512-cctMYH5RLbElaUpZn3IJaUj9QNQD8iXDnl7xNY6KB1aFD2ciJrwpo3kvZowIT75uA+silJFDnSR2kGakALUymg==", "signatures": [{"sig": "MEQCIGUuFu9Bf8v6BH8SkXYxvu6aQxW0FIEecTWKMRfHyDtkAiBspNAbB7ByDGC9ueRR9GT2aoTSazWEHimIiEAPaBA7iw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42962, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdE6jICRA9TVsSAnZWagAA1fAP/iXottGzEj6bsBRuU77p\nwglq6ZywbsWymEq/69GLrxSqEXSrrk3d5vtvPxExUZQMO8sRDCfTTfCt3Kph\nVBtsBhvjrWlomUIYHoBKgP34K9UkZrKesYVQ6oNKlNs2UuyIGRui7Zlw5fsK\nQdt8dUDqzWPQsei42HLjBcIgYwDrKO2n+fNAYEAoCIkQx5AMEAOtvITB5UnR\n98cMpISlaE+L/J3Xn1sEg3/QHHkKG3bRkxKirdQGUFRENSS795OsvMY75udR\nsnJVSbeEByo2dRv04VxsJfP/ziKKOSGwyTHKR5WzHeRWnWNq1qEpNNo7gPYb\nTWHjraGbihHmsZNpfKXvNBGF01CT+4nahhcjpX2doksTW9Kg65C2qHhINPWY\nlFz5wlNqNzE35bhYs1/BI2wQfZ03mGYe4jAVxPn80b2VAZk7s9IOeaFvw5ii\n+9azVlj2IFkavU0X1D877cskFWVCryEnXPNt7xQjSHEocjOiIBjcHiZzBYL7\njBI6pKC6KeN0aa8YOJjiWRUU/fIyh4dRom6Ztj5wGITIpNfeKaWPNohcC55M\nQYR8RXCiOiprXso5+CQhSePT7K3Ks+RpTmnbEtrPc2Uv0Ki9acODR0oxXVLB\ne+FdrVcn8JW90rdR2n2cfqFtrAU03J59jMWuLtXpXmKrC21PCjMbgioW5BeA\nurgb\r\n=QUiz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "aecedb0de2347dd906440effadb36d97c1f48bb5", "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "_npmUser": {"name": "hueniverse", "email": "<EMAIL>"}, "deprecated": "This version has been deprecated and is no longer supported or maintained", "repository": {"url": "git://github.com/hapijs/hoek.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "General purpose node utilities", "directories": {}, "_nodeVersion": "10.15.3", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"@hapi/lab": "19.x.x", "@hapi/code": "5.x.x"}, "_npmOperationalInternal": {"tmp": "tmp/hoek_8.0.1_1561569479593_0.7676580054895454", "host": "s3://npm-registry-packages"}}, "8.0.2": {"name": "@hapi/hoek", "version": "8.0.2", "keywords": ["utilities"], "license": "BSD-3-<PERSON><PERSON>", "_id": "@hapi/hoek@8.0.2", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}, {"name": "wyatt", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/hoek#readme", "bugs": {"url": "https://github.com/hapijs/hoek/issues"}, "dist": {"shasum": "f63a5ff00e891a4e7aa98f11119f9515c6672032", "tarball": "https://registry.npmjs.org/@hapi/hoek/-/hoek-8.0.2.tgz", "fileCount": 9, "integrity": "sha512-O6o6mrV4P65vVccxymuruucb+GhP2zl9NLCG8OdoFRS8BEGw3vwpPp20wpAtpbQQxz1CEUtmxJGgWhjq1XA3qw==", "signatures": [{"sig": "MEYCIQDKRhHi6GxbYRCRKl/lIHaWSUJ4Gw+J88VwE2dumkLtRQIhAL16oNttRcAgkW1YA18bgebkKeXOxG/uj6XtaM1heROC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43058, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdGjqsCRA9TVsSAnZWagAAz6EP/2m7s95/D96yGzVNu6e/\n8RoUBNTpdaclGdbM5Y6vwd/Elqf0WNoqxx1yHcTUHXuOK1YKkNMcNazo3zdn\nEyMDqYLXvwI8pKcaaJ5YiI9IxKrvcMxDLR9OPuKOvQ4y776tkEZmILAdQHkE\n0HLyFEzc+/0a+DAGC++T9TEiWF5HqyMbtQyh7Ds94didTeZoO51wQi3iSpvY\npTJIcCHGl46I3Mf9pUPMcOReFVE7nRhkGrCX0Rm8LiM8hN5Yfip+VQfyFMT1\nye4Ktp28S5mCNh70LwqR4zBFeg5KEUyJ3TfLHiY6ZazYr5K7yif8CyhpzJT9\nzsuJVeRAUBW8aItfJlktcghgxQpeMOy3H61AWRPRdyeUOpMBc9OueR1LwHdR\nEPdMktOQ8hJL0t5sOkbKGSI6IfKmiXMHM5VUHAoero9rOHdzVyGky4veENK3\nnHPG9QVg7b7MLLXTE7qFRE6hSOjtPnpQbJNFhbDY6l/FGMTtqDns0QMoX99h\nEFSb8lkCSSqDqI87DonfUN0dZleckb9+1GGRODwHa/1DGMNWeiR2AYJvVQru\nGx4q0pHd4l1RrFM1xtR70Z6BxzkrW2BD8g3DdoEKNZ2qBeUtR6cmtcJxlCXU\nXV7ztU3LpbLT8+V/+HkfPWKBOTiJoWOldr8WjUezxAB2nnxGyEZ7p2HueijY\nAue5\r\n=0lk6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "17b3817ea682ba87220560f49f6af82230e4498a", "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "_npmUser": {"name": "hueniverse", "email": "<EMAIL>"}, "deprecated": "This version has been deprecated and is no longer supported or maintained", "repository": {"url": "git://github.com/hapijs/hoek.git", "type": "git"}, "_npmVersion": "6.9.2", "description": "General purpose node utilities", "directories": {}, "_nodeVersion": "10.15.3", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"@hapi/lab": "19.x.x", "@hapi/code": "5.x.x"}, "_npmOperationalInternal": {"tmp": "tmp/hoek_8.0.2_1562000042778_0.06660271430548681", "host": "s3://npm-registry-packages"}}, "8.1.0": {"name": "@hapi/hoek", "version": "8.1.0", "keywords": ["utilities"], "license": "BSD-3-<PERSON><PERSON>", "_id": "@hapi/hoek@8.1.0", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/hoek#readme", "bugs": {"url": "https://github.com/hapijs/hoek/issues"}, "dist": {"shasum": "8f7627b23ed9bf67088fc7f9669e48c63ad421bd", "tarball": "https://registry.npmjs.org/@hapi/hoek/-/hoek-8.1.0.tgz", "fileCount": 9, "integrity": "sha512-b1J4jxYnW+n6lC91V6Pqg9imP9BZq0HNCeM+3sbXg05rQsE9cGYrKFpZjyztVesGmNRE6R+QaEoWGATeIiUVjA==", "signatures": [{"sig": "MEUCIQDZiwylCl3lPDLdGXmsWjdVYUYYQOW1+fPUwO9qPpkmwAIgYrU7lVjOVCnz8/WtInmlLJK4E+TwkRUMJuiDIDRBMZg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43798, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdOJg4CRA9TVsSAnZWagAAbqcP/jLgoGOKnfvEIRpBWMmI\nPsXWMRu16OdP08I6ZdaLGWm2pVI6X2R2F6WxhOgrv1/VzI8cK375F4I5s9Q1\nJB0K9gFHFv3h60b1tQIYYVwT7z3qCZu8voLX25GUoYoe//zOuJ1NqQnzTbe+\n5Tilid27/yjYnS9B4gj7cxFYg1gCvuD+gaeu0tSEQ4/Pw8hyix8HVjs08uPs\nR66d81pJOQiHjTD4/C+PMiApCyiEw2ghQ20Qu0i/vGa/EcRbyWBLYfiRWsSp\nwPsU0OpgfhqGgKZ2iuuRjqF5zF/kzmQbqz2o7MTvWycyfC0MglcQR1Pwj2mM\nxsWdNl6fm/8zV/iQzR7iVq2tsHVr5R22BAauy0QjKdFr9r79bvIgKqwfN8BM\n9WQqzFk/GEhd9BxoomRSQ0GTYPgPgPoLX/PjNdIfi8iuU0E2jxeEQ1I39nhN\nTH/4Cavq/WHWhRNcp460MA4LJT8RqNl0Ir2h6pY/DM+8/kGMcW9OQBk+oy0V\ngCgP7zShrxoNnjWzQPsn36IUPMq5L+fEn7xPQfuMqeKB6KLgxn1OyjqshXUk\nx+Djb2ZQfbofSZcXv8p7egDBJSlcaoaLEZIBti+bwgiQOXYq74mFkVPBuKqd\nYFuU/0W2jc7lm1kMBWYxE9v4AoEumBaZ4zrYSvymxzoW7huShPKFoiilZ0ME\niUmf\r\n=pR8u\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "01e8ba57bdd5ef6d716535f179d2b6933c69b500", "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "_npmUser": {"name": "hueniverse", "email": "<EMAIL>"}, "deprecated": "This version has been deprecated and is no longer supported or maintained", "repository": {"url": "git://github.com/hapijs/hoek.git", "type": "git"}, "_npmVersion": "6.10.2", "description": "General purpose node utilities", "directories": {}, "_nodeVersion": "12.6.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"@hapi/lab": "19.x.x", "@hapi/code": "5.x.x"}, "_npmOperationalInternal": {"tmp": "tmp/hoek_8.1.0_1563990071435_0.4031972530258916", "host": "s3://npm-registry-packages"}}, "8.2.0": {"name": "@hapi/hoek", "version": "8.2.0", "keywords": ["utilities"], "license": "BSD-3-<PERSON><PERSON>", "_id": "@hapi/hoek@8.2.0", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/hoek#readme", "bugs": {"url": "https://github.com/hapijs/hoek/issues"}, "dist": {"shasum": "079a133be240ff866ad8532eaa46a691bdd91117", "tarball": "https://registry.npmjs.org/@hapi/hoek/-/hoek-8.2.0.tgz", "fileCount": 29, "integrity": "sha512-pR2ZgiP562aiaQvQ98WgfqfTrm+xG+7hwHRPEiYZ+7U1OHAAb4OVZJIalCP03bMqYSioQzflzVTVrybSwDBn1Q==", "signatures": [{"sig": "MEQCICblBfyCggM2ZloTCyK044LD6GSa5H722ou9Ea7YN2NbAiBd07fuOApFJcBFVNlvUz4jVbZzC+uh6YUgLvgBT9nIbw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45336, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdS0QKCRA9TVsSAnZWagAAq48P/3oHAqg/XFZ9V/mhO7EF\nWtM1pXyhuSOK5n3aKvvw4GEU3daEcW8oKH/2XSgwo+AkTOCRXEkG0iCj8NoZ\nxCMbGlR7cLa1G30pw8qeJ1bKICQ8txvQWNqxWOi7XSaoH27zmWtgSgBS0Hiz\ng2B4jvuJY1MAT/4QM4ol32sAvCqrv7H9O0FR36/BL+Fh9iAdFtIUrd15j+XD\nTlDIG45pPKej7kwC1EnUi6uTT+e1cu4LgPgg7f+JlYYOpVThQlQohiUKmm2x\nv/B/w11PvTV2Mw6ekg58jYyVK4bw4vSD/O9cafLjso1k6ulQnbuCmLHGiTpx\nYS5XU0h5jkeSxnQux4saVjToPs8barRxQYnbEPsClLzb6dAY12fpkvjaGwT7\n6RWAEHMnVLs4e+rEyYvZRh/zTd49LHQZdk+7RStPGLNEvJQVEKs3jTTE/cwX\nMDLueTVrHZ8Oo+H4GRKKEl6rCVOH/rqkO5ipEPTwstDh6KfToSVsbcjP1HuJ\nInHQ11Z3/9xb8UQjo5iUj1S4gEcY1ZjHhz10DPcJi6k841SuUJEzD4bERZRF\n13N5AGjRoraiBmbHQIvdyrxkxTFjwJZ1VEV3JuzWzaxMtX98hnHKXE/8POu9\niHPEayxbQ06O/xbU9xFmvmaw0MT87L0PNBwshkeVY6A0zr+j+wrOlklTxrfY\nltYY\r\n=5Z+4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "10c8a0a05339c34a6f31ef8109595bdfb2205c44", "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "_npmUser": {"name": "hueniverse", "email": "<EMAIL>"}, "deprecated": "This version has been deprecated and is no longer supported or maintained", "repository": {"url": "git://github.com/hapijs/hoek.git", "type": "git"}, "_npmVersion": "6.10.0", "description": "General purpose node utilities", "directories": {}, "_nodeVersion": "12.7.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"@hapi/lab": "20.x.x", "@hapi/code": "6.x.x"}, "_npmOperationalInternal": {"tmp": "tmp/hoek_8.2.0_1565213705549_0.7331555698975927", "host": "s3://npm-registry-packages"}}, "8.2.1": {"name": "@hapi/hoek", "version": "8.2.1", "keywords": ["utilities"], "license": "BSD-3-<PERSON><PERSON>", "_id": "@hapi/hoek@8.2.1", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/hoek#readme", "bugs": {"url": "https://github.com/hapijs/hoek/issues"}, "dist": {"shasum": "924af04cbb22e17359c620d2a9c946e63f58eb77", "tarball": "https://registry.npmjs.org/@hapi/hoek/-/hoek-8.2.1.tgz", "fileCount": 29, "integrity": "sha512-JPiBy+oSmsq3St7XlipfN5pNA6bDJ1kpa73PrK/zR29CVClDVqy04AanM/M/qx5bSF+I61DdCfAvRrujau+zRg==", "signatures": [{"sig": "MEUCIFSjHV2mPmfd8xLXq11YbvL/5t9wvPYfTwbDTGqwA2ICAiEAuUkugF3ZD5SW53yokLaEnVypfh2gDPLDnRmDMOh6vvA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45771, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdUcJ5CRA9TVsSAnZWagAAw7MQAIkOTMg+W/3OzWoSYFgk\nj5YxEmV39HfxC3xThDFxoHnE6PngIRaXbCgc6TGxmdMcokpKLBQ1b6y1MJMQ\nXJNfh0KquEDZzJ5JiTZsLHJWB69fpFNEy4eIqGYWu/FWjTYi5VO+J/ljRS/R\nxrzm5fQ6T75LYkEwnsVWEcZJqzIdjvz5vhB0XjCXAx0FlkGwcj08jXwcR6Gj\nWE9wJNI+C2cAwQKv6BhROi0UVr2IvWUmNrJrL7D9j4q6NdipBe5Xs19N7Q47\nw3vIQIEmoMxfySxsjhjoANHTUsQhMjexXRJk8YrhQLbfIlfN9AL8gMxUveud\nJuwbRqBuip8KrpAiLAAClaGhPQVDmDztT7Yv/uMN/2Kx833U1jS2W3dZ0KBn\nOdcwHA5B8PrAsxT6ToP6lWln/mP1O8pyHR1Ek7ADrpAkFX5C/1icu1pDbpDI\n/AWdonCtakD0u+NzzoqywJSatJ/aoohDueYrRxIikaATDfQ+RtTq91chuJtF\n84Ilsxtr/6yGLKQXRd3JnBXM9+IZlRp1mrXNuEesXAKx8M+EZTkHpn7QZKk4\ntF3P56ZuzzVHmsjdPDQgxt8ulyUUGPJeDzUzmYulVwzJhsCqE/j5jn+Zuppe\ng7BlHD9csQEEJbZarG4WespbPhOHg1REYkjOESDSBLcHOJkgqeo8p/LTeegS\n8TBw\r\n=qwSk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "dca090d1da8515b48c235afbba0ca187f4b3c476", "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "_npmUser": {"name": "hueniverse", "email": "<EMAIL>"}, "deprecated": "This version has been deprecated and is no longer supported or maintained", "repository": {"url": "git://github.com/hapijs/hoek.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "General purpose node utilities", "directories": {}, "_nodeVersion": "10.16.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"@hapi/lab": "20.x.x", "@hapi/code": "6.x.x"}, "_npmOperationalInternal": {"tmp": "tmp/hoek_8.2.1_1565639288927_0.2808628330856522", "host": "s3://npm-registry-packages"}}, "8.2.2": {"name": "@hapi/hoek", "version": "8.2.2", "keywords": ["utilities"], "license": "BSD-3-<PERSON><PERSON>", "_id": "@hapi/hoek@8.2.2", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/hoek#readme", "bugs": {"url": "https://github.com/hapijs/hoek/issues"}, "dist": {"shasum": "6eaa2e1ec3b50dfb8dccbe705dc289094652bc2d", "tarball": "https://registry.npmjs.org/@hapi/hoek/-/hoek-8.2.2.tgz", "fileCount": 29, "integrity": "sha512-18P3VwngjNEcmvPj1mmiHLPyUPjhPAxIyJKDj4PRIY0F5ac3P0Vd0hkASPyWXHK0rfY3P9N2FoxV8ZuYaRBZ1g==", "signatures": [{"sig": "MEYCIQCr38K8+iYu1GGQ9BVhr6YOxnpW15LxvmFC0JYQg+o8vAIhAJdaHrFuGTS8QjRwmik7C6Nfjk6MI545YWaNS5Kgf2m/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45865, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJda3TKCRA9TVsSAnZWagAAvnQQAKUekl6TSC3pqsWm0NYJ\nZTOeH0ZlAfY+kZKjT+sDIMpi+yVuY0LJR9tEQEZ18i6hp+i/55S36Gz6vJ5y\nUJWnxN9ghwVQ3RceTFmZwD9DUm7IsQAfiMLa33NEIs4UDi2lDgAzztsckKhs\nFJSMfb/GR8Ni2CPGI7tF+MhVPCzAzaLGUrNUyDP8drWBv3BQ84iK6T6nC6LP\n2jjyqBs2S8jCfm7wvYyg6zp4fGkRmtAgCTkP92beLFd8RktJoISHrRjSJa5w\nh8k+SpP/sWB29//RJEOgCvFpQgW+iNV4wMNdgtXnQZCC/110yhdV+Iaucy/5\npj+vwEBNPwKIpnEPVZzU7rs2A1PPrxprubNL261WmwSvn7ySptyH9sUv5NrC\n45J3fq93lYlDUcHwaWnijiYfFaArCjFWO0gE2UoKbzAO1psDymOCskRugnS6\ne2wbSDz920piGZNOH9eU3B6+H8rL4EouDQQfAGaQdA7Yzvkhp+HdeaTAvkkM\nIYimsuC/cx7+s088b8kXAkuYq64nFYDgR+psYWcY471NkCJ8v6lKoM3DvxQE\ncRBmd56gm52LULN0ik9ulFjUDc8qBNK3Qnr6GSfnuCfMqyQqksbsLk69vao/\nOGDZwhknDun4zBuSb1wtjmu16kOOz5jNevIp9Rsv4JJCzUC/veG2209AhbMC\n17+Z\r\n=Kzh3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "cc9dea4b591dffb7d3bfff8d23fd1406d89bbf07", "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "_npmUser": {"name": "hueniverse", "email": "<EMAIL>"}, "deprecated": "This version has been deprecated and is no longer supported or maintained", "repository": {"url": "git://github.com/hapijs/hoek.git", "type": "git"}, "_npmVersion": "6.11.0", "description": "General purpose node utilities", "directories": {}, "_nodeVersion": "12.8.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"@hapi/lab": "20.x.x", "@hapi/code": "6.x.x"}, "_npmOperationalInternal": {"tmp": "tmp/hoek_8.2.2_1567323338206_0.16369055329661641", "host": "s3://npm-registry-packages"}}, "8.2.3": {"name": "@hapi/hoek", "version": "8.2.3", "keywords": ["utilities"], "license": "BSD-3-<PERSON><PERSON>", "_id": "@hapi/hoek@8.2.3", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/hoek#readme", "bugs": {"url": "https://github.com/hapijs/hoek/issues"}, "dist": {"shasum": "7ffcb10d9609be5eeb96521b62941653da264b28", "tarball": "https://registry.npmjs.org/@hapi/hoek/-/hoek-8.2.3.tgz", "fileCount": 29, "integrity": "sha512-rvgNiuJU19K/YmVJ4nvDPstqs2C7NSA1+ApAFgTSVvfGahzlwRqu0S0dnyWEDhEvgO40E5phUyZi/MZ6vEUKcQ==", "signatures": [{"sig": "MEQCIA+2+5nb7cpyUXIuUg9YbSQMXLTjLtT/5G9w7gnc8qJ/AiAqtW1H5RvgeY5UM2xkkhg5TSRC6jTwcu+fI8U3CvGn1w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48010, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJddVMvCRA9TVsSAnZWagAAQRoQAIaotju0PxmvbgLus7I+\nz7m8szvkgT5Lb0EVAMkoy3rYerGgEZQdgZXrwVIMlddO6yMu97D4GM8obBLK\nQsPEOGX7QbSY2vz8spTNfbbzGGsW2l3ddMbwOYfPI5o0dDdu3DFvCFE15Y4R\nTbAMecBtb9pkO2x/nUOrZffOZVRHXr83s5lxJ1DQGxPul9O30hRPlF2Z9gpX\nnWBSOydEMWZjYNpn8unNFxoriKnlaMD0SHLQ6wDAuGD5ULk/DKS4az9sxryh\nW1IZkZZGZFPjwI15RdvUv4DEyxvmiaUvrObbpfxeC78/6/xOzCQOpSsn2QUj\n31UzjgsekJZUzxsmJnnB/Iu+vxWYZZpsjxn3uPdY9FLG+9XGoNbaiQqpssf7\n1S6QXR4ysfoOkTyjhgv1uK+MHiuWHoa8zrqHK5Pm+L1hcmC1QBFHtCQhOyGy\nlKAa+TC4KGP7jxdteCwoCVg9KJMVClwH6vtHA52f06jIQDsfdim44EUeo/0p\niMxd7Jo6YA+cp9jHLe4hK5yvhJ0QiqjWZnJf+MqDFZTnU7ebuuDbmKL7gCFh\nHWdtCyogQgKXf58XHRcIpuDoHPOU4fluXBR5MwEF0G+ZKkD/SNZ1NpLlrKvD\n3Bsp6MD2Lnq4pW5FIPo5js7IBIBnzWI+TYtaN+eL6PZEs7/rAkAj5hM2MuZp\nOwlU\r\n=lDdC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "bf43cc0b40e392986e62166d5500fcb522e95132", "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "_npmUser": {"name": "hueniverse", "email": "<EMAIL>"}, "deprecated": "This version has been deprecated and is no longer supported or maintained", "repository": {"url": "git://github.com/hapijs/hoek.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "General purpose node utilities", "directories": {}, "_nodeVersion": "12.9.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"@hapi/lab": "20.x.x", "@hapi/code": "6.x.x"}, "_npmOperationalInternal": {"tmp": "tmp/hoek_8.2.3_1567970094702_0.6491295746593368", "host": "s3://npm-registry-packages"}}, "8.2.4": {"name": "@hapi/hoek", "version": "8.2.4", "keywords": ["utilities"], "license": "BSD-3-<PERSON><PERSON>", "_id": "@hapi/hoek@8.2.4", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/hoek#readme", "bugs": {"url": "https://github.com/hapijs/hoek/issues"}, "dist": {"shasum": "684a14f4ca35d46f44abc87dfc696e5e4fe8a020", "tarball": "https://registry.npmjs.org/@hapi/hoek/-/hoek-8.2.4.tgz", "fileCount": 29, "integrity": "sha512-Ze5SDNt325yZvNO7s5C4fXDscjJ6dcqLFXJQ/M7dZRQCewuDj2iDUuBi6jLQt+APbW9RjjVEvLr35FXuOEqjow==", "signatures": [{"sig": "MEQCIBpBsrQfswX/bpoy2RCP2Opv1dNHiPrqmS7c7d3SRJ6pAiAFvvnDqE+66n7zPCjc3AfW3NaaZLrkGVCOwybQtFRVkA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47918, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdde7JCRA9TVsSAnZWagAA5/gP/RZ49Rdh2JbioqO1zztr\nBTbCyAlQyhFdMcpskazJE0OtPLUlfhx8ZQBnler55CUnEPBE/sj5pmOe9nT7\nJmmeQFwMp1HN/r+MC7lBJC16JWMjeHvQhsmdcdU4l8LeoXGBBl8+MT4UjA/v\nvWbQIv/VoRhA4iPmjnU7hfAt5NK7Kw4nZrjOjspAcNWrMacqojvmD/LCFQSM\nlswfADXJZA0so8HlmtFPCz9CPrmag6+8OzzCxDFYvVyyie0da1o1AEj9guUf\n2CYk5R86QN79mJCRb434Vy12ITBx6S8mpNmBlQQLrWXqOyEng7QcWs2bYtXj\nzzQoW8ybqvs8rIGG4bx6an18DPjsQa42/O0RC8qI4YUurHg0rmu3zhcrWDod\nxnZXxYUlodO9j75yYFg6RKyDJ0Vti42OPZsI+J5P20Bwdcow5yAxdCBBuGPT\nF2Gssx5btVNflCz6gtRM9bQp6vxq0edvmlmhkQDp8uFZj69RmxmqqB5r8RAG\nz8FKsnHkXc1OdN9K7NUGNkmLJGESmBNy8GkqIXlH3P/O967YHBgCSU4/3lLt\n/HMj1S1KRqmpO9Niu3qVFLyh7sOxQbyZnvBy/WkVK8LR5OaE0AiVFjYdVf8t\nNEw/yhkm0X21pyxYRH62CJ+HXkVKtQKWxp8gE1WqUOD7b2igiIGeOlJoXsuG\nmpzM\r\n=n19W\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "e8fa92d2d4422b5ab323154eda2c147790bfb3a3", "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "_npmUser": {"name": "hueniverse", "email": "<EMAIL>"}, "deprecated": "This version has been deprecated and is no longer supported or maintained", "repository": {"url": "git://github.com/hapijs/hoek.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "General purpose node utilities", "directories": {}, "_nodeVersion": "12.9.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"@hapi/lab": "20.x.x", "@hapi/code": "6.x.x"}, "_npmOperationalInternal": {"tmp": "tmp/hoek_8.2.4_1568009928763_0.7988300191291338", "host": "s3://npm-registry-packages"}}, "8.2.5": {"name": "@hapi/hoek", "version": "8.2.5", "keywords": ["utilities"], "license": "BSD-3-<PERSON><PERSON>", "_id": "@hapi/hoek@8.2.5", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/hoek#readme", "bugs": {"url": "https://github.com/hapijs/hoek/issues"}, "dist": {"shasum": "b307d3f1aced22e05bd6a2403c302eaebb577da3", "tarball": "https://registry.npmjs.org/@hapi/hoek/-/hoek-8.2.5.tgz", "fileCount": 29, "integrity": "sha512-rmGFzok1zR3xZKd5m3ihWdqafXFxvPHoQ/78+AG5URKbEbJiwBBfRgzbu+07W5f3+07JRshw6QqGbVmCp8ntig==", "signatures": [{"sig": "MEYCIQD7prEP8xs/khiCXxtHoGD8WT9CPXQPEuHWqLsWCfuoCQIhAJb/QdMvc9MBnfgKen/wfeZalpVHS2ZhyWz1f7Hq5nxK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49412, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdjwf/CRA9TVsSAnZWagAAxYEP/jooY3PnWWZF+OxI0Eq2\nvO5D/MQg/yaziAceOmUg37SwoAXZgx59G7OJjiTWhW6+rEMb6ecXpsbb5enS\n1ywarXg0/PmiV+Q5N3drA7i+Ti8b+F6UyqkN8j2eJCJIpqlFg3kCwEfYO82c\nJ2TSnAeoEyOfCBk57ssKGt0U9Yeu+hzK+aaFKoUBIkI7ccgKtZTQYlmRIrCC\nSC8uXMHnH4dEyfQndpM+JzE0/qlX6PVAbfcrveJKLDbIZCgEFCFzS5/cizPh\nyMrkCKptYUgcFhmScufWcTKL6ntL4S6RUjGCenMSmmWOxXbh3si6Cg/58y68\nNZVfZeKTD6AC35b389ATVfE4h021bpQYjHpZkcNTBjHEFKFOYk4G2tYtupsg\nNrab37YBV07HvFTJfjM6qp9+1hSOHoo4/zuGptElV7MPLkvubqyLIX9IO/27\nahQ0qbs56eyfJvMg99nyRU/e0g/3uo9SpBQWdqVVXXP8RYrYy3xQE2xyEhXz\nLN9mLjas3lFBBGhyA5mKtZuhsGYXEqSxsqk7K2HbiRwKynByntJw4xJSk1vp\nVYJkt6m9y70W3u/dJmqqEgPHSKaLzI9F5eqWzTelYeV21XUr5C1JxGVSzKxQ\nZKUTM23RLJdv6pSwMp4o67n4H+HbrBcYNqURTohff+Nz2EVbTbeyU3OiNzvk\nOzp0\r\n=PHir\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "474f2f4002b3497f518123c5566171f0ed92f69b", "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "_npmUser": {"name": "hueniverse", "email": "<EMAIL>"}, "deprecated": "This version has been deprecated and is no longer supported or maintained", "repository": {"url": "git://github.com/hapijs/hoek.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "General purpose node utilities", "directories": {}, "_nodeVersion": "10.16.3", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"@hapi/lab": "20.x.x", "@hapi/code": "6.x.x"}, "_npmOperationalInternal": {"tmp": "tmp/hoek_8.2.5_1569654783086_0.6738033842439268", "host": "s3://npm-registry-packages"}}, "8.3.0": {"name": "@hapi/hoek", "version": "8.3.0", "keywords": ["utilities"], "license": "BSD-3-<PERSON><PERSON>", "_id": "@hapi/hoek@8.3.0", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/hoek#readme", "bugs": {"url": "https://github.com/hapijs/hoek/issues"}, "dist": {"shasum": "2b9db1cd00f3891005c77b3a8d608b88a6d0aa4d", "tarball": "https://registry.npmjs.org/@hapi/hoek/-/hoek-8.3.0.tgz", "fileCount": 29, "integrity": "sha512-C0QL9bmgUXTSuf8nDeGrpMjtJG7tPUr8wG6/wxPbP62tGwCwQtdMSJYfESowmY4P3Hn593f+8OzNY5bckcu/LQ==", "signatures": [{"sig": "MEUCIQDDQiQIzOuuVjpY04hJ7pv0JfF8RY1XKXUp0DR9rdJ53AIgca9wF3daM67D/w/qBWzKE+pKK9fe1/0XBMbmBLk1Lb8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49538, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdmAwcCRA9TVsSAnZWagAAZ0wP/R1Xl1c7g2N1Nn6WCTSQ\nh2RoSWq92cJ2Fa0L6i6aNVe+CIfjyVZDeK+INeOvR9h4xRS49lM1d4G45g3e\napMAYPC68dVQAbiAEZ2+7ksy+huiXHo+AFNcrzPpD2rfDylJj0CeV+XH5P2O\n95fPBvFkTweIq6iidqokC0hfKCRvE4z5tkDnoggMfZaI1ys17Er073e3m1sz\nxPY449ZpQ3PNnI9BeuQ/D4taHHdU561kLAi5ohI5suxoTIiswwEZ/c1mK78D\n01KkP/kss/UtWJkSKEFLbJGBNFXfPOiZHtep1gaJmPjfs/meunPn147dr+Zn\nm6nwZ11Ld8yyDAgUPd/QAxylYZmb6Y+6DNWtQNs//Jv+hTCaUj46LFB8pk6w\ng8ONwkPbzRBPEWXuQl2v+5NHvdRCzNVn5peiWmFtaVZUCbbD29NhlQghyP3q\nUTNdt+3X8JfjFsxhetPL7+kkuGrbih7uK42ozx9LLUWAyHT1IiBuZVJAPc1V\nyJD6H+wYhhq92gTw1tW+cXpfHyifZ5JLj5PJSd7bXcw3hj0AsruOlh7NE63L\nv/acJPDwBjtrmtVUrTPsr5DMqSoFGNED/WF3e26gStdHSIqfEnWI1CTAG2v9\n/ZO1OWmnjDL2WC0IkohTcySf0N8MyD/7XyLv9Pc6HM1BuJDqjbUV41on4aQn\naq4I\r\n=9dye\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "ec88426e3ddc08ac9513c88734884380496f0051", "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "_npmUser": {"name": "hueniverse", "email": "<EMAIL>"}, "deprecated": "This version has been deprecated and is no longer supported or maintained", "repository": {"url": "git://github.com/hapijs/hoek.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "General purpose node utilities", "directories": {}, "_nodeVersion": "10.16.3", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"@hapi/lab": "20.x.x", "@hapi/code": "6.x.x"}, "_npmOperationalInternal": {"tmp": "tmp/hoek_8.3.0_1570245660046_0.3421197567949852", "host": "s3://npm-registry-packages"}}, "8.3.1": {"name": "@hapi/hoek", "version": "8.3.1", "keywords": ["utilities"], "license": "BSD-3-<PERSON><PERSON>", "_id": "@hapi/hoek@8.3.1", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/hoek#readme", "bugs": {"url": "https://github.com/hapijs/hoek/issues"}, "dist": {"shasum": "227d29efdb158e4a64590224add90c820f94d20e", "tarball": "https://registry.npmjs.org/@hapi/hoek/-/hoek-8.3.1.tgz", "fileCount": 29, "integrity": "sha512-75ocgnI7HG/I01iGA3/rs0y6PXydUA/kxhFZM0HoT8NLSTnt/J8Gq03iKl4a4B/2A3iMG0ctXtxr5Hg9SGr1gw==", "signatures": [{"sig": "MEUCIDNiLX967VYrMYJgyFOTM0u2JzTKCyzPVtbXouOGyCnhAiEAsBDvaGIhutlngit3QH/Np0+NDPZYN7WdNxwPKlg41TE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49655, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdpWbWCRA9TVsSAnZWagAAncMP+wRprBdjQpmjWT3ewoEa\n02XOW2FUdGoT4KHBHfLvR1oJ1YSaOtrn3FOVVX+4/U17f5hmIzTYsje9N3vG\n7f9lhr/VEUiNXZoQP/4+2WVZhBPok2AlsiplHSy+FlTS9IP7k+EpBR+NxWz5\nvqSizuEjJePgzgSzypB4xHO4D2afVe/UQPIn7Mhfh/NBYobTjuOOF+S8PTTN\nVtNRMGF+iIxp2cq+o90Ct9ozp2MEPTu8mBr0lM3JKhXKUPYTkmzbuliSU1RM\nEul73FrbYzxaky91UJRfpt8EoUH8W0qV5lsHWyxahf3asbwt99rZnOPqxe41\nKirzbTwe1ycupwXl8WDa7tPLHsTrQ38lGHQzw8kS0x6vje23I5mo7yw7ADAN\njYV6163tb6sYDO6JPIe5qfRs/XBvWYZdAJShdN+t0Norh29cS1lx5qTJwXKZ\nvgxZyzOUz2tGHQaBCVcW6BcSmZ6MIMZAXlhdZHCp7tY+vPnQhGUsbJFHGReZ\nfjhpPX1pKOK15l3iMdN4jpKI2CdMUd4IKS9HzaGQpfodOk7bM1cdW4zhWNM7\niGkyDCUBmSxJFMRN45PrJ+JoluR6f9O0NA2eEUYktwM94J+6BYym4gpAAlpV\na7PknV3YPzKSN9hkEDAzXS65bhPoTb+YSUHkn1WK80oZVSXx9NLkkED2cTjy\nEs/X\r\n=HZ8F\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "8fa5664b4cb0daaa242f384e415ffc3fc93a11e5", "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "_npmUser": {"name": "hueniverse", "email": "<EMAIL>"}, "deprecated": "This version has been deprecated and is no longer supported or maintained", "repository": {"url": "git://github.com/hapijs/hoek.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "General purpose node utilities", "directories": {}, "_nodeVersion": "10.16.3", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"@hapi/lab": "20.x.x", "@hapi/code": "6.x.x"}, "_npmOperationalInternal": {"tmp": "tmp/hoek_8.3.1_1571120853842_0.16922386078480955", "host": "s3://npm-registry-packages"}}, "8.3.2": {"name": "@hapi/hoek", "version": "8.3.2", "keywords": ["utilities"], "license": "BSD-3-<PERSON><PERSON>", "_id": "@hapi/hoek@8.3.2", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/hoek#readme", "bugs": {"url": "https://github.com/hapijs/hoek/issues"}, "dist": {"shasum": "91e7188edebc5d876f0b91a860f555ff06f0782b", "tarball": "https://registry.npmjs.org/@hapi/hoek/-/hoek-8.3.2.tgz", "fileCount": 29, "integrity": "sha512-NP5SG4bzix+EtSMtcudp8TvI0lB46mXNo8uFpTDw6tqxGx4z5yx+giIunEFA0Z7oUO4DuWrOJV9xqR2tJVEdyA==", "signatures": [{"sig": "MEYCIQDPIARGKq4LMK+/SrwlxjPLK4ViSfa+03+nlUuOGo/zlwIhAOlrekYF3QWuxvNw46N1vMGnGjThX+7xTKRNPFk2F7Ga", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49835, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdqNTtCRA9TVsSAnZWagAAGq0P/jggU1mgMfoJqywm7ai1\ne0BVMiutnqL8GwvBXM8GMIw30K+3q+Gv0TM/obEsMmQjk6UPSHyn5oAMNOnv\n3zmhE+S4liUht9KUzWPcqvDAhP25fYXWeX4UpShODvk2YEeL9vgEQzza5Fci\novHSkIX3Bfeww44TJA6VznnzuvO6GaX2Ybm1eV5diIWY/7GZyG7iocz/jLA/\nsq9AtQrDcin6kmAamgb0I2pcM0QT0MLylp6lN58aXvtjNRLPO6bWtna5hLtF\nye1B4QBFe3WJqEGBYKAxTMiCkSU+h5R1yh6daep9g13y6gBGXOkGgHFg4ohF\nGziJh2Iex3AhWtraNXN4LzXsIBZWL+nJqkVfS/nPRCUlaXwqm/noXcYJPh+Z\nz5rKPj/PWacBTFb3ZnRW8MV5JlfowLDu9e6FCKCKEwWFlYvoj8wxDIbIaS19\nUAyMJHHwxsaiAiYWa6c9Kz6aYKOvV80xbVGfXrKwCbpAYcXS2Ze8rR0QmSW3\nW3vlMKRDXeiSNSxAnnLVeUmV7NIvyIxm745ziqtr7YPdj4sxZtKTVCTFkIwd\n0bAM4++J+5ZpqW5hYLZ7RFcgkv2vKsNOjP6yIi9QyX+fNowxJp/YKaihlTbf\nWeyonMuPP30wNm1sAB0bwqkcWqOe36fJYfGzu5nV1+P6BNZQgpHkyBtZBntc\nuzFV\r\n=Utfr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "ea1741d7d3fcf6b1dd2bf9f5cfbcb36668f700d9", "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "_npmUser": {"name": "hueniverse", "email": "<EMAIL>"}, "deprecated": "This version has been deprecated and is no longer supported or maintained", "repository": {"url": "git://github.com/hapijs/hoek.git", "type": "git"}, "_npmVersion": "6.12.0", "description": "General purpose node utilities", "directories": {}, "_nodeVersion": "10.16.3", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"@hapi/lab": "20.x.x", "@hapi/code": "6.x.x"}, "_npmOperationalInternal": {"tmp": "tmp/hoek_8.3.2_1571345644255_0.41647386383603546", "host": "s3://npm-registry-packages"}}, "8.4.0": {"name": "@hapi/hoek", "version": "8.4.0", "keywords": ["utilities"], "license": "BSD-3-<PERSON><PERSON>", "_id": "@hapi/hoek@8.4.0", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/hoek#readme", "bugs": {"url": "https://github.com/hapijs/hoek/issues"}, "dist": {"shasum": "3f2153d9eea8942dfe217e1642a420f1fe4087f3", "tarball": "https://registry.npmjs.org/@hapi/hoek/-/hoek-8.4.0.tgz", "fileCount": 29, "integrity": "sha512-JL<PERSON>+vNrtZSQy1PiAAvtaPGiZhFQo+BLywJkD4EHG8vCzlW9w7Y9yfb2be1GFKnZKczLgzHBpgMOBUZs1qBNB5g==", "signatures": [{"sig": "MEUCIFZ4TNfCM1LbWhjoULa8eHHERdMekzrT68ExGist0BcxAiEA5laWl0CMM6WSaR8b5Wrw0ycOwRHeoPT5lqub41ciwz0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50168, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJduTDgCRA9TVsSAnZWagAAHngP/icF2vsge8iA++9qp74W\nsz9yT9c3KxHklPIuf5m9D031Yf6d5cNbLpaNVTiu3/KYFyGnLOaiRxjmDMsw\nlnCuCkxeaXuHI5x44mIxEOa+XZ7lpYh6QNcbLU4SwYV4OvhiXTg8A9cHosn3\nKxjpyMATLQa6qpzID/mJljKCOJ2Hkd4fr5dk4hcjJ3BjjU466GdY6922uhDm\nLYOPLv+2LT0iPpn1PdDXYUHqYUsoudocOReMSg3CCUQCEgwxWYBiOfwjsS/8\nFroaVCw4EMwynSadKcJBlSRpaMtRnJa/ULVei/i7UvvD/C6lbbcGaNuz/iVO\nmgyRu7NaDNWTef2V60eWHkTM0SIvBD5YzUdu7KoL431tYWdkKvHNEr9GRnTR\nR1X0LXxFmWxyYzxFhjdt8SmgKNKPeptdo9zagVOnhejGNKmKY4W0eNqYTLSk\nx4lZv2lTtkEg2eV50Z9W4k4GSn5NjB07aJDsHI795sM8dzKSm18txE23NLDw\nc8YK/zz9Zz2BteIlImtS6HAz/UxF0JrFIEVWRgegz7e36Zq3Q+pOv4A18Cws\nNOfy4YgX2IieTb+1UOjn/OaJI18Phk35FpCMY/hSOP3wPs+pRIuVZps7L+ll\nz2WwtPK3eZ6UNysZv8OcFxrO7odm3taAlX6+CwUJUdGsD7jbRfHjHVIBX2bd\nVHZR\r\n=tBIr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "5dcbb9c45de655996ca3c41caf80605a585d5806", "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "_npmUser": {"name": "hueniverse", "email": "<EMAIL>"}, "deprecated": "This version has been deprecated and is no longer supported or maintained", "repository": {"url": "git://github.com/hapijs/hoek.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "General purpose node utilities", "directories": {}, "_nodeVersion": "10.16.3", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"@hapi/lab": "20.x.x", "@hapi/code": "6.x.x"}, "_npmOperationalInternal": {"tmp": "tmp/hoek_8.4.0_1572417759761_0.6496752409648501", "host": "s3://npm-registry-packages"}}, "8.5.0": {"name": "@hapi/hoek", "version": "8.5.0", "keywords": ["utilities"], "license": "BSD-3-<PERSON><PERSON>", "_id": "@hapi/hoek@8.5.0", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/hoek#readme", "bugs": {"url": "https://github.com/hapijs/hoek/issues"}, "dist": {"shasum": "2f9ce301c8898e1c3248b0a8564696b24d1a9a5a", "tarball": "https://registry.npmjs.org/@hapi/hoek/-/hoek-8.5.0.tgz", "fileCount": 30, "integrity": "sha512-7XYT10CZfPsH7j9F1Jmg1+d0ezOux2oM2GfArAzLwWe4mE2Dr3hVjsAL6+TFY49RRJlCdJDMw3nJsLFroTc8Kw==", "signatures": [{"sig": "MEQCIBY/51XGWYcw8T8KZar7/8amkjkGwmuKKONSEFzG4ZNmAiBDSUwglBjp+21oMvtJVkFfN2sB9rQzcbCVzUYEtcl2hg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50591, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJduiy2CRA9TVsSAnZWagAAM68P/24wwgNyuJfiwIpgM87V\n6RaklLtRfyxt16wH/7AbeLBoyMTYfsDQX7FPyrQSyiB6GBkVONXrmCtal5Ux\nfzlgkMm0T0Eca2Zk3DqnPAEbPsvPjZLe459+iBrGeM0NFxUAMTqoU1BRmBKU\nY5Mxyfakm3+/qYHKpNLhudjolmsFgd5MOenWYFOs7x7paGbPi6XUzC+aMcfZ\ndE3fIpszXnGYzruJwofTU/CTfsAB4k6nkV1QQJC8TytFFTOl5chpR6VZBV1+\nTGNjLXFu8rA3cs1r449LnMNzwzXDZSjUYJQ9l7HEEcjRnkZD3wJ5D1JLdEvq\n/OAPpvUVbgzQ++1qNftcUfkXseo3l5jY5mA/Tg23GGqIYcaex1L3oKLx8CjK\nRhoYYkH7Z7FpGWGdJtwjOUQPxkY7eS0xG/+Su0wC4DNh5CPbdQEw2re/z9vP\n3u+WnAGSWqLe6mPOstqVwTiiNilzobJ6jFWedEq7A6f+JQceZf9aEl86JW+h\nHjXz8libIHXs27YE5799I660DStR5nR957hZsuRHmxjiRMo9UAW33dWa3Q45\nZ1NO4883sSvAdgjOiuh4kmh/VcOzqnc3LZcMxuSnZzjVjGl7+RlkaBfQlFGJ\nJ6i+4IUcW53jwZXFV23Qtp6ngiQ9LSeZi72wAA0Sn9jZ6rF6K8XH1lhyRcgt\ns+QM\r\n=JsTx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "4ae5f5355f55f600e0969cb61369b7c41b0f50e1", "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "_npmUser": {"name": "hueniverse", "email": "<EMAIL>"}, "deprecated": "This version has been deprecated and is no longer supported or maintained", "repository": {"url": "git://github.com/hapijs/hoek.git", "type": "git"}, "_npmVersion": "6.12.0", "description": "General purpose node utilities", "directories": {}, "_nodeVersion": "12.13.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"@hapi/lab": "20.x.x", "@hapi/code": "6.x.x"}, "_npmOperationalInternal": {"tmp": "tmp/hoek_8.5.0_1572482230277_0.3066712149783404", "host": "s3://npm-registry-packages"}}, "9.0.0": {"name": "@hapi/hoek", "version": "9.0.0", "keywords": ["utilities"], "license": "BSD-3-<PERSON><PERSON>", "_id": "@hapi/hoek@9.0.0", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/hoek#readme", "bugs": {"url": "https://github.com/hapijs/hoek/issues"}, "dist": {"shasum": "ba83436edfac1d1ffd0e94797d43419c20ad49b8", "tarball": "https://registry.npmjs.org/@hapi/hoek/-/hoek-9.0.0.tgz", "fileCount": 30, "integrity": "sha512-XxD4A5YMIH70ddjG7BJBUz7RWVQAwIP/36Eoyh0DsaWp92OAeXkrbtSEaYkynBPTsN9Uv2mZq9QWZYILl2Svrw==", "signatures": [{"sig": "MEUCIHxcjwIantnfNRkgjkqtoRvoOjp4Y/oFqrmHAlhESxOTAiEA4Dzhm8/aJcQYG2GBTCW00Nk4vx6KlWOdormgbS8QOiA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50354, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeD8kDCRA9TVsSAnZWagAAbFYP/1MouDEqVkYSO3CMmjfY\nNdIc0K2/r5qOhFcaAsEpTZ1BvDAu3ko5w2dBDJxwzz6+zQ6/ChWi+JDiwllt\nTzYqBHwuys7nbmM3kOM18ysqZNpyEeXqzxmASNoPRhdaGaqI/8BnC/ORj6dj\nQSppw/fdl6GTRT1W6zK+PDVM5/8dxaWtHwJzhh21iLxWr4TMytRRS+lXL/1/\njtS35nTo6Wi7z2jsWcvysCtYLDaxnmMuKc6lu7/cCI/Ykd1XephVWSiy2D7u\niXwDjBtdPs5hJYwa5S7RMJqM3iAtyqCEKvnaVqlNUJiPoPMHdByyGHtk7+KU\nVMO5NwOHc/cnE6nsDK4vA+wxjcshdRXPIyuDzOZNq6fhVE4ekPBbcg6/ZKYm\ne8zDpGS8K2wjtocaO62Pr4hpf+xjShu3jHdLrFoRex87vkkPuJPPnwZjJsl/\nBp3aopaVZUAKvu6dXmKZgJsoVdSz718TzTDHqDzczNC1iCA2rP8wfCjKxdii\nf7CZ585sm/jn26Mo7tbNucdiO86pJt9jmHG6Q7F36HoN8awDdmXVjjt9vKa9\nk/0AuWriI6KmtMuyY4tsk3s63j6wOQxsoB9KdlhyyNhSddtmOoAaWeDfKEjH\nVGcXLxC3Nww9y123PACvpqHaqh0iUw/nYXMi71oalBPauEk6+5JazPUiUZrm\nhZ3S\r\n=TfwZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "04450279698565e2a9ebffe8b1c349539daa7924", "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "_npmUser": {"name": "hueniverse", "email": "<EMAIL>"}, "deprecated": "This version has been deprecated and is no longer supported or maintained", "repository": {"url": "git://github.com/hapijs/hoek.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "General purpose node utilities", "directories": {}, "_nodeVersion": "10.16.3", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"@hapi/lab": "22.x.x", "@hapi/code": "8.x.x"}, "_npmOperationalInternal": {"tmp": "tmp/hoek_9.0.0_1578092803181_0.2928294920924026", "host": "s3://npm-registry-packages"}}, "9.0.1": {"name": "@hapi/hoek", "version": "9.0.1", "keywords": ["utilities"], "license": "BSD-3-<PERSON><PERSON>", "_id": "@hapi/hoek@9.0.1", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/hoek#readme", "bugs": {"url": "https://github.com/hapijs/hoek/issues"}, "dist": {"shasum": "280e6b51a3ae08f360e0fb5533116d331b1522d2", "tarball": "https://registry.npmjs.org/@hapi/hoek/-/hoek-9.0.1.tgz", "fileCount": 29, "integrity": "sha512-8L2MB67CB90iPDW/PE5rJPE7VFNfxtvHANuaw1GHNV8gAD7xuJPNjMzXxm0k4Y4KsCZYbHMS0g23ppkspt2qHA==", "signatures": [{"sig": "MEUCIQCfmB15qe1pd77uMv4tNrnHAKlbqSzNKNn1p8b/2ipVDwIgMq8BdGp77QsE6+dn7w5EkVDQgH9P8UUvKMKCXujuEVU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50088, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeFsTVCRA9TVsSAnZWagAAdVwP/idKEGWrGiBXJ3lFWjNy\n4fQJzxqrGitewJ8q0c6zbKx3FSTLXqHWx/lUaSsmc3SlWFFtHOnpgYVsMRMK\nxxQDYItl1/ck0yUu4iucio52MGbFv3DEnpUCWfUBcgstuDgqLizlr+F7VwpP\nH5Uq0L1aMzpRnoJQC7iQz1jg4pJ/tpRZrZKTUtie5JgCZsmehbOkJxoC1QdM\niZkEkewfvNIs5i+YyxjA8tMeOpdfS8R6kQ9fSnmOqP5GwS81I2C+K3yplgjq\nn5oUfMrKaSkaK4d/CzCZ5ksFRUX04xkfsWlnsMQrQybqvwUBmyPfCAVvdEZz\nl5C8YQiCk9x5O1hhAe/9q4be55g7KfHB9oxkoWw0tR4y7JbrOpLq5uE027Pa\nu9blPf1+abXQAPWIZWdceFrT0q78M9w/7pyEp60unlsXXMhfux0JZ4HbTVrl\nOayGTkxo7vNWbRk+Apf9LM6TEpPGV3z1mOWY4cI7NMEK0sHEf/jIPhgKeiDW\nKsi8uaMPdvKk6JGFRbMQ23v35fsExSXCKMpS7hA9roI8SVVoeABouv4298HN\nBc7t1AJ+V4TadUH6RKePBVQyiqhLCFA8tMZh0K50/JzlTaDXp44GoxQbJwnR\ntaf2mX0gEEMo97SyrchdidqN3nS5sLQt5PIoXpLOYX+5II+ZGIb/8ZFI+A3D\nm1Bg\r\n=S0vb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "34823defd8696a525c63511ebc54fb6f8698c438", "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "_npmUser": {"name": "hueniverse", "email": "<EMAIL>"}, "deprecated": "This version has been deprecated and is no longer supported or maintained", "repository": {"url": "git://github.com/hapijs/hoek.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "General purpose node utilities", "directories": {}, "_nodeVersion": "13.0.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"@hapi/lab": "22.x.x", "@hapi/code": "8.x.x"}, "_npmOperationalInternal": {"tmp": "tmp/hoek_9.0.1_1578550485142_0.5822344873015286", "host": "s3://npm-registry-packages"}}, "9.0.2": {"name": "@hapi/hoek", "version": "9.0.2", "keywords": ["utilities"], "license": "BSD-3-<PERSON><PERSON>", "_id": "@hapi/hoek@9.0.2", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/hoek#readme", "bugs": {"url": "https://github.com/hapijs/hoek/issues"}, "dist": {"shasum": "57597083f763eafbfdc902d16ec868aa787b24d2", "tarball": "https://registry.npmjs.org/@hapi/hoek/-/hoek-9.0.2.tgz", "fileCount": 29, "integrity": "sha512-LyibKv2QnD9BPI5g2L+g85yiIPv3ajYpENGFgy4u0xCLPhXWG1Zdx29neSB8sgX0/wz6k5TMjHzTwJ6+DaBYOA==", "signatures": [{"sig": "MEYCIQCEzTUO9dZ85iZpp7hMI+IcXhe5LDgrcbxsBn/uCEt4PgIhAKs7Shvt1yfYNEsHQh/B0OvWpnJXgY9OAu9lKGRaj0NV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50248, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeF3CQCRA9TVsSAnZWagAAoa0P/R+tUdZM9sKwPLRZULuf\ndrn8TbKVoboLPiaaGO3+x99p5juIbS0jKK0bcWI0tBtKmDx2PoaoKQHdjPEs\n0eDlTeojVXnmMPKIrj4vRFRgnrBytSl7yCmzDmPZsYojxhc+meqYvuUudMrv\nzt7BLptBeKNvdC+n11ixYQ92PijA2ueZ5MNtg1B3Vv/eaMlj8aXFOO/nC341\nWODC1ZeypHCCY4hbU1Np8ltvuZ2gyZD9xtatHXdstnBkg5a+MJyLX0bNzoQV\nMNsKcU4xQVWEPK7IO9R2Ke0Yfni4xXMPEG9K+Ru/emyl7ziwbWpZ6OWKKtnT\nkY7CGUWkPIhyUd1Bsx+Mkk7tBU6b57wKnnGYVGnYiAG8HkjSoa4O/UAeWnTK\nSOFqul67ei4viaRfTXvp9WpgG03P3u1Y3gum4CpHoZy3fNG56kU8Ty5Fko8B\ntFlP6P6LqUO7B/smYjsIL++Fi/dKLsMwYG8nRddN/Lz6wBVga3fbbzXRsOqX\n7zQ79fwNw2HRi/W8+17sWvzpBS+OEYXYAuVGY2tNBQEnHQ2sTC6SgoqJ/xNG\n4lb4ndSI9frnCWQl4Y7bru9kfvf5r7Mf8L2tiIOz9xiukiT26Mewc+476QkZ\n+V//5CvPEdebVTUU5eTyxsf3anzTkAJpwoTOKMAMy5qczVAuoI27I2gQUCET\nD1M6\r\n=OqiF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "f9e865a9e9855c4ca16e97de36e653266f79760a", "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "_npmUser": {"name": "hueniverse", "email": "<EMAIL>"}, "deprecated": "This version has been deprecated and is no longer supported or maintained", "repository": {"url": "git://github.com/hapijs/hoek.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "General purpose node utilities", "directories": {}, "_nodeVersion": "13.0.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"@hapi/lab": "22.x.x", "@hapi/code": "8.x.x"}, "_npmOperationalInternal": {"tmp": "tmp/hoek_9.0.2_1578594448362_0.966486989010084", "host": "s3://npm-registry-packages"}}, "9.0.3": {"name": "@hapi/hoek", "version": "9.0.3", "keywords": ["utilities"], "license": "BSD-3-<PERSON><PERSON>", "_id": "@hapi/hoek@9.0.3", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/hoek#readme", "bugs": {"url": "https://github.com/hapijs/hoek/issues"}, "dist": {"shasum": "e49e637d5de8faa4f0d313c2590b455d7c00afd7", "tarball": "https://registry.npmjs.org/@hapi/hoek/-/hoek-9.0.3.tgz", "fileCount": 29, "integrity": "sha512-jKtjLLDiH95b002sJVc5c74PE6KKYftuyVdVmsuYId5stTaWcRFqE+5ukZI4gDUKjGn8wv2C3zPn3/nyjEI7gg==", "signatures": [{"sig": "MEYCIQC2xPz71p75PRfM3lnARbaO6wXcnhuBvy6iMEaTVrOnrgIhAI3O7v4k4n9O9kMcKa6M//Jhy8gkA1voz+QWWswmvH6K", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50316, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJePlc0CRA9TVsSAnZWagAAVpgQAIItyq5rJg7oNciow00y\nvWDaLox8AfPGRxJYNR444BAxivQ7OS3nk+SxFG+U7w/QQbObdHd2atCn5m0R\nkqdCvnt1zYtnRnHr1VTPTYC/4iYuumWonOIRDX6iHs7jfsbT3VcsNImyFBMJ\nWFrUT5k2CwZ1bpMyUXfcCP5rMoTTzgKH7anJjxQbGgn6pzG0W/2hz3vkaC3g\nzl7yKmSRQLKeICw0E5tWa8sPr7456ZUZo7Y8UJV9HLL8euFBNFUZb0L7th0u\nIWWCmthpPL25DxdhVVGIDkCebYyxzj5VgU+8oQJzJy+UrMzSIOpTj74wKJng\nwpmf/58DhDbgIEu7ue10bTJBHRSauTAP4YMjI6CepFzKcyh/0l/9VY6Z93bO\nrWzDgVwmTTZb+iOfykqPJv3UFqzReTCmBv3qQzwcxSk8w2vhZ2WotHIk8x24\ngFi7HinPDPpn/tfA60rkECiFs8P2USUcSYcIT1E9qIikIljqMzeuU+jroTbE\nNun9q1WGRiOKrno4evRI9rNiEq8oRxPaQf7UTA10NPNheaCXpOzM+elKTrfm\nHGNVu9I1BsnYCvIA1OuUEcdlI+lTUQYvj8UFEh3azGPOC3+uH5ybqzxja1SR\nEnG/sLx4qI7dI0f9kN05Drf/ODiHDGBP4hrA7g+kGscqGS2Cf4dPXJowJeK8\njLIp\r\n=k+p+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "8bf7635550d35ff01966601c19078df8da9b96a6", "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "_npmUser": {"name": "hueniverse", "email": "<EMAIL>"}, "deprecated": "This version has been deprecated and is no longer supported or maintained", "repository": {"url": "git://github.com/hapijs/hoek.git", "type": "git"}, "_npmVersion": "6.13.7", "description": "General purpose node utilities", "directories": {}, "_nodeVersion": "13.7.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"@hapi/lab": "22.x.x", "@hapi/code": "8.x.x"}, "_npmOperationalInternal": {"tmp": "tmp/hoek_9.0.3_1581143859494_0.12997466275210567", "host": "s3://npm-registry-packages"}}, "8.5.1": {"name": "@hapi/hoek", "version": "8.5.1", "keywords": ["utilities"], "license": "BSD-3-<PERSON><PERSON>", "_id": "@hapi/hoek@8.5.1", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/hoek#readme", "bugs": {"url": "https://github.com/hapijs/hoek/issues"}, "dist": {"shasum": "fde96064ca446dec8c55a8c2f130957b070c6e06", "tarball": "https://registry.npmjs.org/@hapi/hoek/-/hoek-8.5.1.tgz", "fileCount": 30, "integrity": "sha512-yN7kbciD87WzLGc5539Tn0sApjyiGHAJgKvG9W8C7O+6c7qmoQMfVs0W4bX17eqz6C78QJqqFrtgdK5EWf6Qow==", "signatures": [{"sig": "MEYCIQDiabd526ORvEpQAlAynEdBV54vlQ+bSf7WjBmbtauUOgIhAJMah+BMyecG8JNNaiGWpqS+LV4tiTAfA0uoLWf2HI/c", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50659, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJePlkVCRA9TVsSAnZWagAAqCcQAIxHXg7AjH4MlihWcvqA\nkzQrpkCPGOSKmnqx0Fn3oCnJfs/g6dVNxs9xeAwfdQT1snyuB/5B3UY9GvBH\nU7IPS9dx+7P3EptnVWHdouapjVnu9WneEI4Qms1CNCvdmQU/Aky9V2vgJdcR\nRJVk8ntISzMUAxHzJxVTuIb79oCTr2/vGBKhLFRP/S8Ew0m/iZ3d1Fjc1Qe8\nOaiSfmxKoYnAVhPrsUoICVlk6qy40ghNeb1O9IEyPlCXMqevnpWrlZ9/EWdT\ncAAH58/ZAVghBOtikSgvuK7GtXMOhmaxG05wwDJnOgYTu5vqlWf/qtAqDHdE\nF5pdFPHUcQ2xHSLcH24RbsUDxL+EMvyJ2pjqaVKqBbA9CWdGJs2wYVtNtXQ2\n+GLEElSfQD30Gc8XlJVlBIFV0XxMMX1OYYuvLdFu9QwSi0vwzkhJaOSVGIHr\nCWyhnc+uS7VbV4D/v2wM2k3e1amCpBmrtTbSnpfn8/Hao0FZR3bRABuprcgX\nDFK0svlUuSWEDcCKFRpU9WekBz4mpAnI6wqiobaQOOeEkSqQTGBBGn4MpFMN\nq3DwbAaBgvFqg0G8XJkIuu9fkGNXvZwJFBVkjuYDR3lYEC34J+C41BHC9UmF\na4n6N9uAmIafBSmRcz2EK5ri/fRfGdas0oVd3jKiXrRLCM2QmUD9ugSOCsRu\naUHq\r\n=+0sI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "5bd73f642b0e21a92fc582ad2b489301753d1e01", "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "_npmUser": {"name": "hueniverse", "email": "<EMAIL>"}, "deprecated": "This version has been deprecated and is no longer supported or maintained", "repository": {"url": "git://github.com/hapijs/hoek.git", "type": "git"}, "_npmVersion": "6.13.7", "description": "General purpose node utilities", "directories": {}, "_nodeVersion": "13.7.0", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@hapi/lab": "20.x.x", "@hapi/code": "6.x.x"}, "_npmOperationalInternal": {"tmp": "tmp/hoek_8.5.1_1581144341020_0.3015261716255577", "host": "s3://npm-registry-packages"}}, "9.0.4": {"name": "@hapi/hoek", "version": "9.0.4", "keywords": ["utilities"], "license": "BSD-3-<PERSON><PERSON>", "_id": "@hapi/hoek@9.0.4", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/hoek#readme", "bugs": {"url": "https://github.com/hapijs/hoek/issues"}, "dist": {"shasum": "e80ad4e8e8d2adc6c77d985f698447e8628b6010", "tarball": "https://registry.npmjs.org/@hapi/hoek/-/hoek-9.0.4.tgz", "fileCount": 29, "integrity": "sha512-EwaJS7RjoXUZ2cXXKZZxZqieGtc7RbvQhUy8FwDoMQtxWVi14tFjeFCYPZAM1mBCpOpiBpyaZbb9NeHc7eGKgw==", "signatures": [{"sig": "MEUCIGT2T/zpn8gdaJPalDwE4Eg/xYu6ohZR1wtoVLNVz8rgAiEAiyaS+dDps8FZob4GBtPcMREkasLWeOcG5r41i6lRa28=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52426, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeacS+CRA9TVsSAnZWagAA4Z4P/32u6vXX9/crKmuvS1Xe\n05/lCH1LXkNHneCTUqXNchTvGBYENq4kM9j3d2BrQdtaDelICuaw9FSy7Fl1\nSUNDx7+ZHKknoX3EWcty1EXS/bJjSUd3lUXXFvzTpcQwGHVxE1+HGAlHwlWO\nW6uhEFWhD3x0M57f0/b1JngVbqNa+ht1d0jylxgNU24iZBOJoq51uGmkpdNM\nuiPPnwt1LCCiTz1+GbCxKbT4CmWkqQuCOgda9mSNK+dyH9VqkX5awJgzRdtE\nEO8RZHMLehRN+jUUsez6cDN2jTBKU/4kwzGJO/+SZyGujDnz8kAbCNWT+4sN\n7NmP/rWYERvO9/hm5Fs6rdAuD1tergEnLlA3FBQNo7Gy5igiofG4HN2sd6WP\n0WkoDtQz09E2Um3pA+z7usVLEqAlLXhzwzbZG+TR6Xj0enllgu19BsIYNF7G\nqNZaEKVo+JD41OtYDPa1y2ZgmE0wT3IanPI/6BS3hr4/h5y3KpDofetqDI/9\nppHR9mC50lykV0SpvxIgpTpHgqhs2oIZknuJYSfxT+OIC9d8zdJ0DGPX6i/y\ntrldH+q+YbaDOxJ7blkyA1eI1ARR5BeICUtgoeHXJYsStygSiqsxpoyncrgf\nD6MAASG8EIyxhFXjSmDXo30Qo22jzqbTuoQHRkEwYE51GPF/PeugMDdd0B8L\nCJia\r\n=2Giv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "148f23295c029c23361e6195b1db0b0f3dfc5251", "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "_npmUser": {"name": "hueniverse", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/hapijs/hoek.git", "type": "git"}, "_npmVersion": "6.14.1", "description": "General purpose node utilities", "directories": {}, "_nodeVersion": "13.7.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"@hapi/lab": "22.x.x", "@hapi/code": "8.x.x"}, "_npmOperationalInternal": {"tmp": "tmp/hoek_9.0.4_1583989949984_0.7276216005223972", "host": "s3://npm-registry-packages"}}, "9.1.0": {"name": "@hapi/hoek", "version": "9.1.0", "keywords": ["utilities"], "license": "BSD-3-<PERSON><PERSON>", "_id": "@hapi/hoek@9.1.0", "maintainers": [{"name": "c<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "devin<PERSON>y", "email": "<EMAIL>"}, {"name": "hueniverse", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "wyatt", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/hoek#readme", "bugs": {"url": "https://github.com/hapijs/hoek/issues"}, "dist": {"shasum": "6c9eafc78c1529248f8f4d92b0799a712b6052c6", "tarball": "https://registry.npmjs.org/@hapi/hoek/-/hoek-9.1.0.tgz", "fileCount": 29, "integrity": "sha512-i9YbZPN3QgfighY/1X1Pu118VUz2Fmmhd6b2n0/O8YVgGGfw0FbUYoA97k7FkpGJ+pLCFEDLUmAPPV4D1kpeFw==", "signatures": [{"sig": "MEUCIQDO12i6Eg7hrq1Lx91AnyPvQyM4XcN5kxPGFan1JHEVdQIgU8SXas+mWVSawSE1O673+zyNMZaPuyF1oF4LOtXCr10=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50787, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfTvbqCRA9TVsSAnZWagAA9OIQAJnAZirwZ6/4yHC0FNpc\nV5KJbuvk9htodXipPRNa8Ug12ISKSOgu2Zd/dgIBVNaN1U5S0ZYkozcGg7J0\nu4er+kNdqDm8Tpig5MKTlLSq/8H+oQoiE1N59RvSQsg3Gx3GuAhYL+3dlZjv\nzSo0uw7y9ubJSmPHZykB6ohpEn04SrnPGVvD3CmXuD7wTBsQwf+rS623Dxo8\nFi8d2Uf+D4NmEF84qT0LkUzvz/pxMh3iwhvEcxom0JhGiHuwCt9Tph7X4en6\nozqUpMbp/Mtp36cTcRUm/tb4RO8462NHjBhH3uw265S13UMoBMgcaFziCjQt\nq8TpLJ47Z/zWkrjcK8b1D5SC6sTRHK4drhLOuaZix1zd7SVkd05x04L9E5M5\ntvh2HZ86T6CnBTM9TShiCVsZFDivpyCAbU9e+QdBJp+tjViJ4dVnHQ2PK/gN\nHRFpJ+ID5rx6TEEpfftDGIAC4xPAZVpzVdDKT+PdlQCGsQpViYhqHO425tJ4\nQblQtjcNsnaRNSpRzqYgwLQjx0+bbMyvZiXNvSXNfoEZu1N6QhDQGVMmdXNt\nDK5P/Bp8CXaKIPVEyZ2QT8tqsC2CBV8yyai5+BIQn7OLW/xKizVAvKJnxP5R\nGkBBytnwl17dM6aWDGxCMgWRt/dB+R6Yd15KQ8b7UpiQMW9MpyR2lCVNKe7w\nTXaw\r\n=6eYH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "baf3bef37d3db7beefe1516917f006de7b2f7383", "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "_npmUser": {"name": "devin<PERSON>y", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/hapijs/hoek.git", "type": "git"}, "_npmVersion": "6.14.7", "description": "General purpose node utilities", "directories": {}, "_nodeVersion": "14.8.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"@hapi/lab": "^23.0.0", "@hapi/code": "8.x.x"}, "_npmOperationalInternal": {"tmp": "tmp/hoek_9.1.0_1599010538012_0.6669128609665878", "host": "s3://npm-registry-packages"}}, "9.1.1": {"name": "@hapi/hoek", "version": "9.1.1", "keywords": ["utilities"], "license": "BSD-3-<PERSON><PERSON>", "_id": "@hapi/hoek@9.1.1", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "c<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "devin<PERSON>y", "email": "<EMAIL>"}, {"name": "wyatt", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/hoek#readme", "bugs": {"url": "https://github.com/hapijs/hoek/issues"}, "dist": {"shasum": "9daf5745156fd84b8e9889a2dc721f0c58e894aa", "tarball": "https://registry.npmjs.org/@hapi/hoek/-/hoek-9.1.1.tgz", "fileCount": 29, "integrity": "sha512-CAEbWH7OIur6jEOzaai83jq3FmKmv4PmX1JYfs9IrYcGEVI/lyL1EXJGCj7eFVJ0bg5QR8LMxBlEtA+xKiLpFw==", "signatures": [{"sig": "MEYCIQCtSoBKPkqCqJ5ZNP3UwZg2q+sabxc1ZLJ9TuK83FWypwIhAON+/TJPqIEIXVLgEw62hXwVuA3DNjsp8ZdQD/C6tpTN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51016, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf6L7+CRA9TVsSAnZWagAA4X0P/2H9SYgY818jdbPxjpCE\nvEQFPqwQGpNJxvMztvNJKF8547OG7bxFMST91pwXhCNcLRMcqY0aLIcL3zmf\n3ibUf5MR9neep+9u0Vh4qkpdnB22HYLOlNGSW4LrU3VmAd/+m1rKDb6IThG4\ntRUbLNnNTD9Wa/CwLOwhV6UVoK/ljQl61DgO0rvFXptORYaogSnwCej1X6C2\nJbx6PHVeG37166moanV1TAmuZIBl4IcYrARvRQriUpZQy1Hbntmq66Axc1sH\nmiCFymfWkEOdMYQm7W5bbdRLWUjY0ZEG8/DzzGN4gHTtRWtUvpsIy4Ike+r6\n7xLdm501iWE1KDqDNCTCUz4nAcP6EXpFmtHCrWVyLCK29sdRK6ntZznDEw3e\n9PRCfoDoCAx+ujcaidK5d37sLpFHu38ZcOMIy5YuT/mY48nzRndQ3pgQ0wT3\nJJtrRhpxjFqev2O/gMzdQ8luVlqOdYshiVUYEGNq5tRib1wJ+PPK1y2L7ZG3\nymmSZDgIT2Y9rY84+pcjmz5XOx0nzM7X0zchy17g9zwM0mJAI8C2mLBcJDwg\nl8YVBtLVKkj/f1+bn03z97/57lAcHXsQ3hHPAx4PAKWsPiWxXqjFcYzmioBg\nJyAesiMX61OC2gzQYrtJwzzQwo/qsJY+HIIBrmH6v7XH0z5QqrSvQLjKJK3f\ndhnS\r\n=iKQp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "af58f45b126464611506abc25bae887bb10a2f10", "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "_npmUser": {"name": "c<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/hapijs/hoek.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "General purpose node utilities", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"@hapi/lab": "^24.0.0", "@hapi/code": "8.x.x", "typescript": "~4.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/hoek_9.1.1_1609088766378_0.7545829601617497", "host": "s3://npm-registry-packages"}}, "9.2.0": {"name": "@hapi/hoek", "version": "9.2.0", "keywords": ["utilities"], "license": "BSD-3-<PERSON><PERSON>", "_id": "@hapi/hoek@9.2.0", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "devin<PERSON>y", "email": "<EMAIL>"}, {"name": "wyatt", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/hoek#readme", "bugs": {"url": "https://github.com/hapijs/hoek/issues"}, "dist": {"shasum": "f3933a44e365864f4dad5db94158106d511e8131", "tarball": "https://registry.npmjs.org/@hapi/hoek/-/hoek-9.2.0.tgz", "fileCount": 29, "integrity": "sha512-sqKVVVOe5ivCaXDWivIJYVSaEgdQK9ul7a4Kity5Iw7u9+wBAPbX1RMSnLLmp7O4Vzj0WOWwMAJsTL00xwaNug==", "signatures": [{"sig": "MEUCIFRxoC4Bt53lvzsCxI8QNMj9+PetGyIz8D9QX9bEirFFAiEA4v3Dwu4qGZdPy6CyRYHoxb/oz6GEKhawxNP0Im0dxug=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51102, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJge07YCRA9TVsSAnZWagAAmx8P/jkK8qXPWK5fThI8Gu8C\nXmBDqzjJzj0aLQKJwRFp7Mvn5Mf83IERUG5rhetKPi/hvnPSrfabqBeBr3Wd\nbqq8PacI5BKvTIQ8ORV2DyXs2hAYGH6snHfw6Ex0OjoaKoLJy61TKiR+HWoF\nCRnzxtfg27/265aNbD2LKvblWhVdVw71UytjBvg/qIdaHSZyfcLEj10SZb2O\nf3/WlCgLa2rX21l1mJ/w6hgmUv65k1VHZc6wML6XychLMdmuUBnTK2EC7h7M\ny4PgA7xJea5X+TNFhdKloB5g3WZOrUNfuvRonXNuhkBejEe8E3ilW4FwnUhR\ndpK5Uc5ZNB71ASCxdx+NIo1ClGXlQhKAc5qZEtqYu19sH+7E89xpbfn09h3e\n02/FhoilyV8ykte6CP1J0DioepLvyCGGBeyPLkDmOH3lNmR580z7wFpYWIu/\nWQUVJFtSb05gzMDZpZe2MjdF3JBOP0pPdF9qwGSKAaMYVqIGxMEIPKgY9eVA\n9Qp30/DEe09OqGMfoMcPreWJdQ9jKFdVKbygKj2R4LSM0ohAzrZudt111nqA\n6piJQwN1FEb5EDPgiW3NQ1qdLrPM+Nt5miNWDQqScAIjzNdyzs8T8HWhV4AL\nHoIu1/o8srY5hGJ+tSLFpq5veSNyZhU0RkpDeU2M2pDfFv3boxKWKxN4o7ma\npx/y\r\n=spJ2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "7ff448eddb23795a4a60fe48806c5554a6ef7976", "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "_npmUser": {"name": "devin<PERSON>y", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/hapijs/hoek.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "General purpose node utilities", "directories": {}, "_nodeVersion": "12.16.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"@hapi/lab": "^24.0.0", "@hapi/code": "8.x.x", "typescript": "~4.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/hoek_9.2.0_1618693847698_0.7310554242117033", "host": "s3://npm-registry-packages"}}, "9.2.1": {"name": "@hapi/hoek", "version": "9.2.1", "keywords": ["utilities"], "license": "BSD-3-<PERSON><PERSON>", "_id": "@hapi/hoek@9.2.1", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "devin<PERSON>y", "email": "<EMAIL>"}, {"name": "wyatt", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/hoek#readme", "bugs": {"url": "https://github.com/hapijs/hoek/issues"}, "dist": {"shasum": "9551142a1980503752536b5050fd99f4a7f13b17", "tarball": "https://registry.npmjs.org/@hapi/hoek/-/hoek-9.2.1.tgz", "fileCount": 29, "integrity": "sha512-gfta+H8aziZsm8pZa0vj04KO6biEiisppNgA1kbJvFrrWu9Vm7eaUEy76DIxsuTaWvti5fkJVhllWc6ZTE+Mdw==", "signatures": [{"sig": "MEUCIBBwt4ILwjcfXDAiRQ8lmNzTGhmZ6Ekh4zS0+bWW7LkPAiEA3gBARGSwSaFkz2ax0MuZZZlgCjMs80L0UoKP+vz046w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51151, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2J13CRA9TVsSAnZWagAATq0P/3FmuzUw2/+7hoG6oLok\n+OFTIwWxnZmvj9DE4ULZ6JzSSYbSWH35hWljMYBr9yDV4jWiOSew6OmLAyXy\ncnkFw1tOfpLdunXsj1/+hei9ucnMfz1XAQCpGlNGu/anJwXD/5jSUA19W+jA\nxxpygRuPS6vz+6eJDuyfDAYF4QcDIM1F6C35oVPMDxZq5XhCivIB8tUBhXhK\nyky1GGUEou6ueIRsQTzPd31l5aL/yJ1eaKPo3L9OOW8Ies+3G+enviZRdG2v\nENX2ctIZYTCSIQcccm+efg8k09hUGDkYGiimv102wa9CzJgNs1qi6rarek2V\n82/eyrzWE2QQXrbxY8khkteguCq6ih4MJyMWUGIsmNHxn42i8lPnP2HDPezN\ngGLDrI9xxBrdZfz1VkSaTmWZIoWxB5+3+MLAuL/0a6r3JAfhDSMajFzc0R40\nW+VYRdBqkQW3/UsO8tBkuemPkkw6TH1cpBSIj3Dqs7gLuNRRp0njGeO5oHJH\neeuwEr6BCeozAAM/1tBKQQ0dBUUj7C27rvEsT4GD8z8TxSWn7UL161s9f+aU\nnmT6ujoq41MT/zuGakoPBTbHn7eBy1EUb1OlT1uTcCaxknxaaPNwInp4D18F\nnWbkY+9tM4znkX0Vh5wS1dJ3mu5CFeAtCxMea7ra05JWq8jEwSkPuY2r6qZw\nobLW\r\n=WcH2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "0d614977a23e30e344a34d4539952ccd1ea71306", "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "_npmUser": {"name": "devin<PERSON>y", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/hapijs/hoek.git", "type": "git"}, "_npmVersion": "7.24.0", "description": "General purpose node utilities", "directories": {}, "_nodeVersion": "16.10.0", "dependencies": {}, "eslintConfig": {"extends": ["plugin:@hapi/module"]}, "_hasShrinkwrap": false, "devDependencies": {"@hapi/lab": "^24.0.0", "@hapi/code": "8.x.x", "typescript": "~4.0.2", "@hapi/eslint-plugin": "*"}, "_npmOperationalInternal": {"tmp": "tmp/hoek_9.2.1_1632776274001_0.26652481377234216", "host": "s3://npm-registry-packages"}}, "9.3.0": {"name": "@hapi/hoek", "version": "9.3.0", "keywords": ["utilities"], "license": "BSD-3-<PERSON><PERSON>", "_id": "@hapi/hoek@9.3.0", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "devin<PERSON>y", "email": "<EMAIL>"}, {"name": "wyatt", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/hoek#readme", "bugs": {"url": "https://github.com/hapijs/hoek/issues"}, "dist": {"shasum": "8368869dcb735be2e7f5cb7647de78e167a251fb", "tarball": "https://registry.npmjs.org/@hapi/hoek/-/hoek-9.3.0.tgz", "fileCount": 29, "integrity": "sha512-/c6rf4UJlmHlC9b5BaNvzAcFv7HZ2QHaV0D4/HNlBdvFnvQq8RI4kYdhyPCl7Xj+oWvTWQ8ujhqS53LIgAe6KQ==", "signatures": [{"sig": "MEUCIFKKFv4drX7Ycn0R+3g+1L4xQPsHNffIejlYIFaJzi0KAiEA9v7QISbU90lbCCTtSezKr0Ju/3QREdrrwOCkeaxVm6I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51451, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJibsr/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmowEBAAl64AWPCGxXMqj8s4ff1oPOSE1tfbOHa5GXT1pUcI6MlMNurm\r\nrPW80QsyUPZngLykqnOHNnGv4bMYiTNdGVUkNJFtJEvZGz69phgYXOllVPt1\r\nqfuVbg2JZMpVdCea2GxZgHvfjolWQ2n06isU6MJ6eZyU0D59G+h4/iBdff1L\r\nn0FsplsGUxXecEj8fNE2w/7fgLXc2bZKpHAPnrqQ9pYT+JTn3CJPCid8V/Lx\r\nEFCBgHpWmTaCGNDcRmk45UaB0OOXOX/ebOIEvVocR6SgkvJ/BujE2tTV/Xe5\r\nyZMVdzfJ+JI009TT/R8OwIvKcbKYNiXyEOad6H7le2lZMo5Vw/oW0yiYNauB\r\n1StXWnPtIDaqVKUdKD2t8UGbgXyWfpxYhZqaN4Y5JXaZH4RdJTgz976ENocW\r\n7jCe2S3GirVKxQfNBWH8dPUd2gzpPkeg+qNylD7hAZZoWX5r7Weh6OPeqHwZ\r\nVdBpqGEWS3vYgTqTI8Cu10/Ek0J/WH6u7J1/shddE1r/NJ/FNCsykpgiUBMV\r\n4hR04raKwfsReEZh+7KHUiE21BZ3AEdO6wiEO5CSsOQYxQiWEm5035eGJCBe\r\nzfA8nrr7hXps3d0Oawv/lb7+23nDdMGpJRY9cLX13vpRBHuzTWdG+XXNkL4I\r\nRjdZWy5lAOcTXUTAVcsdvdAp9WyNeb57bDM=\r\n=UfJA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "24467c98a471a23c0b1901098948b6662e516173", "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "_npmUser": {"name": "devin<PERSON>y", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/hapijs/hoek.git", "type": "git"}, "_npmVersion": "7.24.0", "description": "General purpose node utilities", "directories": {}, "_nodeVersion": "16.10.0", "dependencies": {}, "eslintConfig": {"extends": ["plugin:@hapi/module"]}, "_hasShrinkwrap": false, "devDependencies": {"@hapi/lab": "^24.0.0", "@hapi/code": "8.x.x", "typescript": "~4.0.2", "@hapi/eslint-plugin": "*"}, "_npmOperationalInternal": {"tmp": "tmp/hoek_9.3.0_1651428095083_0.9815018111406344", "host": "s3://npm-registry-packages"}}, "10.0.0": {"name": "@hapi/hoek", "version": "10.0.0", "keywords": ["utilities"], "license": "BSD-3-<PERSON><PERSON>", "_id": "@hapi/hoek@10.0.0", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "devin<PERSON>y", "email": "<EMAIL>"}, {"name": "wyatt", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/hoek#readme", "bugs": {"url": "https://github.com/hapijs/hoek/issues"}, "dist": {"shasum": "b17be58febab7dbc60cde5f522da197ccde12713", "tarball": "https://registry.npmjs.org/@hapi/hoek/-/hoek-10.0.0.tgz", "fileCount": 29, "integrity": "sha512-CeNFz1JcLZ5xE8Vc9aau37cgHw9bxXqSDK/D55GF2GAOv0n0XjyyjSodHtKahB7A1tV3FlgCpijp3zkSITmBdA==", "signatures": [{"sig": "MEQCID2Hek83PBEtS1IwKagElUbJ5u6Z2PYAG3fiHlimWtIYAiBXQLUI0Xi6wuD4uE2QjZzFtV1mSBb80NLmxTxxYEM0Zg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51385, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJibtFtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoT8Q/5APWdkZNhfruD+RaFrS2BqvG5Y5k2V/CHntslu0iOC5Ekt/+1\r\nKRCb8ORLvKQZdS3ndrCvnldZkMRgqDmuhHo1Y3jCsCN+hlrorwyFATWs2JkV\r\nE1uDDym0cBOpjgKP1YJnoTbbHgFkLWbfN+C86/c1uDUKDmcgiUp9T4si6BkH\r\n9MTSVVls3YcDL6D0vpRm2B6AwTzuwOkDHnfTJH5njJph82fvL99w1oINdQHG\r\nQWCo2LuSe6US5cgt3mtsM4dMkXpRVAJuFKj2bwALbi7ghOTTkaH5zs0C3CD0\r\nlhQZXiVBPEp9Bnbxy4EV2DS7xoCIne/7OGkrQry8g89ROJyjtjKKb1IDcUNt\r\n4cBXOwdqlgTQDK1pfpf2liRTslt0JAOuNP2RC6fqF7SKKA0aqUvX8J0pMZEJ\r\nZ21Oit4nYgTXrVQm8oNHiFWwBsphrwIqWMNqb5lSG5ZGPzi4ZTQ99zlZTHj6\r\nWqSq0TED3n+c730navogjY7mf0RnVxVLI+rE+JaHA7Brsg3q1O/OTocrr4Vo\r\nRAve/drpyVkwDDgRxN4sH7wdRVMrFqsxQikBl/cCcsiELZL/JHwODXy7jGXL\r\nRizIOj2VHqCo48kVIYPd4/Zum8W0E79j0gu/0UhRdx9Bm6Do6q62jlTPW21n\r\n0xI8oX6uOBWOUv4z+N5fESiYxixoP1Eel1w=\r\n=esGq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "9e81474b0beef2bb7c0443b756c29e534b7b7dd7", "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "_npmUser": {"name": "devin<PERSON>y", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/hapijs/hoek.git", "type": "git"}, "_npmVersion": "7.24.0", "description": "General purpose node utilities", "directories": {}, "_nodeVersion": "16.10.0", "eslintConfig": {"extends": ["plugin:@hapi/module"]}, "_hasShrinkwrap": false, "devDependencies": {"@hapi/lab": "25.0.0-beta.1", "@hapi/code": "9.0.0-beta.0", "typescript": "~4.6.4", "@types/node": "^17.0.30", "@hapi/eslint-plugin": "*"}, "_npmOperationalInternal": {"tmp": "tmp/hoek_10.0.0_1651429740825_0.9425663117047334", "host": "s3://npm-registry-packages"}}, "10.0.1": {"name": "@hapi/hoek", "version": "10.0.1", "keywords": ["utilities"], "license": "BSD-3-<PERSON><PERSON>", "_id": "@hapi/hoek@10.0.1", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "devin<PERSON>y", "email": "<EMAIL>"}, {"name": "wyatt", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/hoek#readme", "bugs": {"url": "https://github.com/hapijs/hoek/issues"}, "dist": {"shasum": "ee9da297fabc557e1c040a0f44ee89c266ccc306", "tarball": "https://registry.npmjs.org/@hapi/hoek/-/hoek-10.0.1.tgz", "fileCount": 29, "integrity": "sha512-CvlW7jmOhWzuqOqiJQ3rQVLMcREh0eel4IBnxDx2FAcK8g7qoJRQK4L1CPBASoCY6y8e6zuCy3f2g+HWdkzcMw==", "signatures": [{"sig": "MEUCIQCW01g989TV9D8v7r/0KrrrzvKbytvXuj0JEAOoIEmoiwIgLIStDQwno1QOUUa7WCte9z1L0XUTUX6oEoLJ/kWds6E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51419, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi3D58ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrlDw//RSCdMe9QnzL7OipVTUZa2b0m57xO5X33sxG5fSxgHHv9S1qD\r\nGp7fqNJg1zexSrz1m4wDSHotbh3RAyuSxOhWIEViID4dhcGr5eoku/Su7i7q\r\nseUQWKMVMwxoUpHCPJNjDnNKUR/mPgNUlo3NUPZRwS95pvdA35II6ndafWtL\r\nMK8DajytoZSFW1gf0l0KQwK2C7JJUMhgqaaUTso7Hns7X8CPCUiPl34YEPiH\r\nYkArQy7+c8Rtjw5iQ/i4ktypOT5hkTsXHZUgoAhmKjfolnp4C+tD+dpimdDq\r\nKdhUCOwRlIK5y/hq/HkBYuDnpaNdRGxAR/fH9/Sh3Y8SlP38GN9VTKui9TWT\r\nCwzYr2AbWhr1S3XkcTMnCu3aUQSmo0X1imB98UwO7fDZQ7d23wXyhWg1tyrr\r\ns145ncAMU24U3H8TSeLUYDB3bz8EpT5Q0BKo8zSTiY4TAAxPWFUdjRfl5m9p\r\nmNesG+4H/bZzKCL4hv/yMeqsFQevIOh3IcRpp4zLXVnKVrEp6bKgquSsICSo\r\ntGWi8WnDaDw5cLNsAcONqtPDPE1oMdv39nXqfWcWDZKg8zpVZiviOpBkFY5F\r\n0WG5EuyvPSPNzNwZhOOpI1CMzfwtLR3ujepBzUSVff/KOdxjIA8DneJ7Xi4Z\r\nnIP+tBJtbM9ABS4hw/tZrIftuwUVn/nfr5U=\r\n=Qswn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "941a932b8be873bd119c6680c368116c52845e4a", "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "_npmUser": {"name": "hueniverse", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/hapijs/hoek.git", "type": "git"}, "_npmVersion": "8.13.2", "description": "General purpose node utilities", "directories": {}, "_nodeVersion": "16.14.2", "eslintConfig": {"extends": ["plugin:@hapi/module"]}, "_hasShrinkwrap": false, "devDependencies": {"@hapi/lab": "^25.0.1", "@hapi/code": "^9.0.0", "typescript": "~4.6.4", "@types/node": "^17.0.30", "@hapi/eslint-plugin": "*"}, "_npmOperationalInternal": {"tmp": "tmp/hoek_10.0.1_1658601083766_0.9163032497958008", "host": "s3://npm-registry-packages"}}, "11.0.0": {"name": "@hapi/hoek", "version": "11.0.0", "keywords": ["utilities"], "license": "BSD-3-<PERSON><PERSON>", "_id": "@hapi/hoek@11.0.0", "maintainers": [{"name": "marsup", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "devin<PERSON>y", "email": "<EMAIL>"}, {"name": "wyatt", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/hoek#readme", "bugs": {"url": "https://github.com/hapijs/hoek/issues"}, "dist": {"shasum": "9ece2a2a07347c412a8462a48a7b5bffcda3db3f", "tarball": "https://registry.npmjs.org/@hapi/hoek/-/hoek-11.0.0.tgz", "fileCount": 29, "integrity": "sha512-yoV74DrW2+mH/awIquCFn94puF1+1cjbcNJOhVBzvGE8eb0Ex5ySui7JURsUzjGe0FGQoUVrzzlxpdx0pPXYtA==", "signatures": [{"sig": "MEYCIQCwWWahOgE9963/EZJgtsgf/MGVFkR36YwGhUKHA7FUbQIhAJvKeyAOo2bEs8YoioMtnoM8whBq0gMeCQXv3ukjrGRX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51604, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjmDIpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpTzBAAkq04Asu4VmDvneOlfh4RBo3y6dZTZYIIV+D/Not2EK1GhVZ+\r\nFUhAeMmI8VZHlaKwFHlAKBLAALHfP33zyNaj3rUGSa6MhjoJiXTKwsAIug3C\r\nj0EYE6E/MPiqNr5jy24CoTZxLcJjnvNOx0iGwhNEdc37ikdPvc7lLFIjj6PE\r\njMniVZaP6QZ0MWUiz80nGY/bTzcraPBKMXb2i1od7qOcgK3OVTpywZqS8BON\r\nThx65lDiAuiRqdzMWLyGIHaubt0mj4cKQCrN8DBkcJFiSO0abivmN91Buw5J\r\nvjkoJyYJocIImY2yuoCie6aWK9bxCGBqxhL7LcuUrBbHOAZ+R9K9yqzUAgtJ\r\n2v2DG0HA/tZWBlebclic1Y5hSgkgUHP67OkGe/j4aUrZMo85hftIoDJ6I6tv\r\niEQ+/KlLxz81im7+UZ6XUUD1Ey8s3JW0PaOL6NPgD+J5R7/rFy8AA174jfst\r\n7AQX4tB1xxi4acft4KuV4BsP6sUit6sd/XjMdHlJ1zo1igu+pR55u/x8AgAb\r\nU+g75u859BnGmObV3h4e2NWeCYDkh6TJtRhpTkUo80W6WY55aARY7Aa52zy3\r\nvhpDzJt3xkGDQ70uVT7r8fP07wKTJVBHS5kvEK6un9433zgp1Ox2oI7ASOhE\r\nuixJOgpeJ5HLAf7fLRjrVcBHXx+LMnZSzCk=\r\n=9Qd7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "18f55e8f47e5f429b21c443ff497c5674848a8c1", "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "_npmUser": {"name": "marsup", "email": "<EMAIL>"}, "deprecated": "Release 11.0.0 was missing a critical part, which is now part of 11.0.1", "repository": {"url": "git://github.com/hapijs/hoek.git", "type": "git"}, "_npmVersion": "6.14.17", "description": "General purpose node utilities", "directories": {}, "_nodeVersion": "14.20.1", "eslintConfig": {"extends": ["plugin:@hapi/module"]}, "_hasShrinkwrap": false, "devDependencies": {"@hapi/lab": "^25.0.1", "@hapi/code": "^9.0.0", "typescript": "~4.6.4", "@types/node": "^17.0.30", "@hapi/eslint-plugin": "*"}, "_npmOperationalInternal": {"tmp": "tmp/hoek_11.0.0_1670918697617_0.6724442381222551", "host": "s3://npm-registry-packages"}}, "11.0.1": {"name": "@hapi/hoek", "version": "11.0.1", "keywords": ["utilities"], "license": "BSD-3-<PERSON><PERSON>", "_id": "@hapi/hoek@11.0.1", "maintainers": [{"name": "marsup", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "devin<PERSON>y", "email": "<EMAIL>"}, {"name": "wyatt", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/hoek#readme", "bugs": {"url": "https://github.com/hapijs/hoek/issues"}, "dist": {"shasum": "38c1c2b4f5bf294bf0a62f6f62c8375b19bc7a10", "tarball": "https://registry.npmjs.org/@hapi/hoek/-/hoek-11.0.1.tgz", "fileCount": 30, "integrity": "sha512-nP+56NOTkEDbJlrO9QKxbDwIrbpDDZgAKaV4lkC1St+5yRUlYHlryWm7mLtmglfL9kwE2XtmfZre+hpFw64PGw==", "signatures": [{"sig": "MEUCIQCZjcqEB+RAxj8UYBfE33+4KqHL1l0VE0QbjtZbZjs73wIgK/Y9rCRY/Nvx+lRXZoA9A8cGOjL/aqIEHBxHITldtQQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53798, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjmNPTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp1OhAAg/KPwE+GBoXxGnBm5tlGFN1dcKqJvtGc1Syf6pXYAubN27i6\r\n7tAXNJBW8418fvBfRkHa7/7esRVcbSt4Cx97N95l2pTZsjgYXaMBYvCQu4QV\r\nWF9A8MnfXMEh/RKZTXpQPzYSr0UbebCWIeatCfv7lrJGUR6/hTuXOD3F2q6m\r\nzU18DAWjBd/I9pYqIEnNry0ASbGp4uXYOv1JuKnMiQvINchGamrFP9pNKTW5\r\nFkQtZG2zrJIW4DDkPDTINA653Dgk4KP+wW1TUP1UiL3UFvm9IRIPt9pQHvTP\r\nX7S/kTSaGZK3eH/yNRvadbnSXNxeOiKRAaP0sh7gFdCMl1REJMgCtguBreSK\r\nJpP3/zfaWs1ve7PWQYZ27a4fpw5RPQ1sBbAgcvFouaXQRCSyqxMKiTTuEOGU\r\n6VQ7y2LPsL2StpAzgpLZ1OZd3mZiULNeRfTXfhiDD645uemOYIiW3+zo+y6x\r\ngG0Ow+jVcPsPxBHZ5DYyBbGL7H51FBb88d3X9dNBStkTgXSb/gXrKi1qDYt5\r\nuwj3vuYvDQ/7gZBexgyBdRyt59FLwJpGJXZ1DuBonN0BK/YKzHKHcNA4A2eq\r\nhtUDfv1J5McVBIxvyXE31/2MtBSAz+B7kSDPOli6EwxqxssAeLiJYyXlA6fy\r\naDzzXse1kgFEl/9YEUSFpTtK/AcebjhKkxQ=\r\n=TFuN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "exports": {".": {"import": "./lib/index.mjs", "require": "./lib/index.js"}, "./once": "./lib/once.js", "./wait": "./lib/wait.js", "./bench": "./lib/bench.js", "./block": "./lib/block.js", "./clone": "./lib/clone.js", "./merge": "./lib/merge.js", "./reach": "./lib/reach.js", "./assert": "./lib/assert.js", "./ignore": "./lib/ignore.js", "./contain": "./lib/contain.js", "./flatten": "./lib/flatten.js", "./deepEqual": "./lib/deepEqual.js", "./intersect": "./lib/intersect.js", "./isPromise": "./lib/isPromise.js", "./stringify": "./lib/stringify.js", "./escapeHtml": "./lib/escapeHtml.js", "./escapeJson": "./lib/escapeJson.js", "./assertError": "./lib/assertError.js", "./escapeRegex": "./lib/escapeRegex.js", "./reachTemplate": "./lib/reachTemplate.js", "./applyToDefaults": "./lib/applyToDefaults.js", "./escapeHeaderAttribute": "./lib/escapeHeaderAttribute.js"}, "gitHead": "e56d141c83f7626e0b2028dd28c6b7a77f1765d2", "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "_npmUser": {"name": "marsup", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/hapijs/hoek.git", "type": "git"}, "_npmVersion": "6.14.17", "description": "General purpose node utilities", "directories": {}, "_nodeVersion": "14.20.1", "eslintConfig": {"extends": ["plugin:@hapi/module"]}, "_hasShrinkwrap": false, "devDependencies": {"@hapi/lab": "^25.0.1", "@hapi/code": "^9.0.0", "typescript": "~4.6.4", "@types/node": "^17.0.30", "@hapi/eslint-plugin": "*"}, "_npmOperationalInternal": {"tmp": "tmp/hoek_11.0.1_1670960083260_0.21424673940625705", "host": "s3://npm-registry-packages"}}, "11.0.2": {"name": "@hapi/hoek", "version": "11.0.2", "keywords": ["utilities"], "license": "BSD-3-<PERSON><PERSON>", "_id": "@hapi/hoek@11.0.2", "maintainers": [{"name": "marsup", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "devin<PERSON>y", "email": "<EMAIL>"}, {"name": "wyatt", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/hoek#readme", "bugs": {"url": "https://github.com/hapijs/hoek/issues"}, "dist": {"shasum": "cb3ea547daac7de5c9cf1d960c3f35c34f065427", "tarball": "https://registry.npmjs.org/@hapi/hoek/-/hoek-11.0.2.tgz", "fileCount": 52, "integrity": "sha512-aKmlCO57XFZ26wso4rJsW4oTUnrgTFw2jh3io7CAtO9w4UltBNwRXvXIVzzyfkaaLRo3nluP/19msA8vDUUuKw==", "signatures": [{"sig": "MEUCIBi0WHjb9iomubmQNPHXBf/ywgwI53OKg5DpVFPABXrrAiEApSMz2MahvFb1Ste9AZKrYYAbfybcVpeNzD2XFt2QhFk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55050, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjme5kACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmra8Q//Z475nfqqfOb0bRSsu2tHxpRuf0ZFYgOcrQ+pDFeqcU8OFrfv\r\njGazKNhE0LE1LLHHD01xpzSc3HQTaHTTr90Juys6g0PtpZp+MgHrB+5lY5h6\r\nBlW6DiZK8uo25sYmjnHCDuD9Qsj3pUf/sOJvV2QdEOGoER3OtWKgsN2M5PMN\r\nTGJeI/Ttax+iV+WJSXFgU7xesF2i9vCOrfUgn5KYPkUlhhV7GxktG6KA9tjf\r\nzRHEtlxOHMC3Aw/Q48FX3HHw+ifYIPGkqPRcz7SDlIwkhtvJl/Gs0Oza2W8A\r\n30axfbMHplOWZ694HNbc087SGmFs+rd4dxSKCZqwM06I1wOo7jbOSMxqR/55\r\nDu1XhxtL/XdfR/5eykvzZvi3p51Q5AEr/xMzfbLG4c7XhoGCTTun3aImBxNa\r\nvJ0VUTgIxbTzDJkFRr/ds+kZrBVH0a+jN/pDIzcGeT9jobQjULybhNZ+UOZi\r\niKJNOJ2d0UQhvxh5vPw8By9gzJGpWgG86mH+M0RybU/kVVfOGooMJqCRJrTl\r\nK29i9U1gF3XPacZrpKXdgrRf3Mumiz16IASVao5p8bIIU3WLAHjdp3rOqLoo\r\n1hPBS/mfDyJYcHTErES5507hkxWdgWq5BECMOq0ofOtn0SHOTc8ORR/VU+Ly\r\nAULiQ2beSBJRTFbNe7tN2dy7Y+CwqRLD3Ps=\r\n=DYD8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "exports": {".": {"import": "./lib/index.mjs", "require": "./lib/index.js"}, "./once": "./lib/once.js", "./wait": "./lib/wait.js", "./bench": "./lib/bench.js", "./block": "./lib/block.js", "./clone": "./lib/clone.js", "./merge": "./lib/merge.js", "./reach": "./lib/reach.js", "./assert": "./lib/assert.js", "./ignore": "./lib/ignore.js", "./contain": "./lib/contain.js", "./flatten": "./lib/flatten.js", "./deepEqual": "./lib/deepEqual.js", "./intersect": "./lib/intersect.js", "./isPromise": "./lib/isPromise.js", "./stringify": "./lib/stringify.js", "./escapeHtml": "./lib/escapeHtml.js", "./escapeJson": "./lib/escapeJson.js", "./assertError": "./lib/assertError.js", "./escapeRegex": "./lib/escapeRegex.js", "./reachTemplate": "./lib/reachTemplate.js", "./applyToDefaults": "./lib/applyToDefaults.js", "./escapeHeaderAttribute": "./lib/escapeHeaderAttribute.js"}, "gitHead": "c3460d68027fbf5bb6c94c0f7899936459499e3c", "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "_npmUser": {"name": "marsup", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/hapijs/hoek.git", "type": "git"}, "_npmVersion": "6.14.17", "description": "General purpose node utilities", "directories": {}, "_nodeVersion": "14.20.1", "eslintConfig": {"extends": ["plugin:@hapi/module"]}, "_hasShrinkwrap": false, "devDependencies": {"@hapi/lab": "^25.0.1", "@hapi/code": "^9.0.0", "typescript": "~4.6.4", "@types/node": "^17.0.30", "@hapi/eslint-plugin": "*"}, "_npmOperationalInternal": {"tmp": "tmp/hoek_11.0.2_1671032419932_0.7663855876960193", "host": "s3://npm-registry-packages"}}, "11.0.3": {"name": "@hapi/hoek", "version": "11.0.3", "keywords": ["utilities"], "license": "BSD-3-<PERSON><PERSON>", "_id": "@hapi/hoek@11.0.3", "maintainers": [{"name": "c<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marsup", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "devin<PERSON>y", "email": "<EMAIL>"}, {"name": "wyatt", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/hoek#readme", "bugs": {"url": "https://github.com/hapijs/hoek/issues"}, "dist": {"shasum": "de6c7727ab48fa841e2458c924729c9db38043b6", "tarball": "https://registry.npmjs.org/@hapi/hoek/-/hoek-11.0.3.tgz", "fileCount": 52, "integrity": "sha512-xA4/pdKayKa60It+ItpVP5n/ep/jRtXAdpTS+QnYukgTsrMS7esGOJOTPio206vzebcnr/KYCwJSoJCweJQPtw==", "signatures": [{"sig": "MEQCIBxNi4k2NmA50cDcGwWCQofPeambtjeS3VsKBSnCZw7aAiBJLz6afwuo43LdARD5Nd9zRm7C86yxrsBrsRU0Y2w3yw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55115}, "main": "lib/index.js", "types": "lib/index.d.ts", "exports": {".": {"import": "./lib/index.mjs", "require": "./lib/index.js"}, "./once": "./lib/once.js", "./wait": "./lib/wait.js", "./bench": "./lib/bench.js", "./block": "./lib/block.js", "./clone": "./lib/clone.js", "./merge": "./lib/merge.js", "./reach": "./lib/reach.js", "./assert": "./lib/assert.js", "./ignore": "./lib/ignore.js", "./contain": "./lib/contain.js", "./flatten": "./lib/flatten.js", "./deepEqual": "./lib/deepEqual.js", "./intersect": "./lib/intersect.js", "./isPromise": "./lib/isPromise.js", "./stringify": "./lib/stringify.js", "./escapeHtml": "./lib/escapeHtml.js", "./escapeJson": "./lib/escapeJson.js", "./assertError": "./lib/assertError.js", "./escapeRegex": "./lib/escapeRegex.js", "./reachTemplate": "./lib/reachTemplate.js", "./applyToDefaults": "./lib/applyToDefaults.js", "./escapeHeaderAttribute": "./lib/escapeHeaderAttribute.js"}, "gitHead": "7c7afc24d40180b9fbd5b811639a5d0eeeae47c3", "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/hapijs/hoek.git", "type": "git"}, "_npmVersion": "10.2.1", "description": "General purpose node utilities", "directories": {}, "_nodeVersion": "18.18.1", "eslintConfig": {"extends": ["plugin:@hapi/module"]}, "_hasShrinkwrap": false, "devDependencies": {"@hapi/lab": "^25.0.1", "@hapi/code": "^9.0.0", "typescript": "~4.6.4", "@types/node": "^17.0.30", "@hapi/eslint-plugin": "*"}, "_npmOperationalInternal": {"tmp": "tmp/hoek_11.0.3_1701763672157_0.46753464073956486", "host": "s3://npm-registry-packages"}}, "11.0.4": {"name": "@hapi/hoek", "version": "11.0.4", "keywords": ["utilities"], "license": "BSD-3-<PERSON><PERSON>", "_id": "@hapi/hoek@11.0.4", "maintainers": [{"name": "c<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marsup", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "devin<PERSON>y", "email": "<EMAIL>"}, {"name": "wyatt", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/hoek#readme", "bugs": {"url": "https://github.com/hapijs/hoek/issues"}, "dist": {"shasum": "42a7f244fd3dd777792bfb74b8c6340ae9182f37", "tarball": "https://registry.npmjs.org/@hapi/hoek/-/hoek-11.0.4.tgz", "fileCount": 52, "integrity": "sha512-PnsP5d4q7289pS2T2EgGz147BFJ2Jpb4yrEdkpz2IhgEUzos1S7HTl7ezWh1yfYzYlj89KzLdCRkqsP6SIryeQ==", "signatures": [{"sig": "MEQCICZJTvp1urmzCifWjPXJ+Vh3TzQKj8ly+MBzRyKgpM2sAiBrjKZABmE71fOrhrkt7O72bmLFJt7dxbcXf1pUFLgpDQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55150}, "main": "lib/index.js", "types": "lib/index.d.ts", "exports": {".": {"types": "./lib/index.d.ts", "import": "./lib/index.mjs", "require": "./lib/index.js"}, "./once": "./lib/once.js", "./wait": "./lib/wait.js", "./bench": "./lib/bench.js", "./block": "./lib/block.js", "./clone": "./lib/clone.js", "./merge": "./lib/merge.js", "./reach": "./lib/reach.js", "./assert": "./lib/assert.js", "./ignore": "./lib/ignore.js", "./contain": "./lib/contain.js", "./flatten": "./lib/flatten.js", "./deepEqual": "./lib/deepEqual.js", "./intersect": "./lib/intersect.js", "./isPromise": "./lib/isPromise.js", "./stringify": "./lib/stringify.js", "./escapeHtml": "./lib/escapeHtml.js", "./escapeJson": "./lib/escapeJson.js", "./assertError": "./lib/assertError.js", "./escapeRegex": "./lib/escapeRegex.js", "./reachTemplate": "./lib/reachTemplate.js", "./applyToDefaults": "./lib/applyToDefaults.js", "./escapeHeaderAttribute": "./lib/escapeHeaderAttribute.js"}, "gitHead": "7309947c64ae444854a35a5202809f265437a708", "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/hapijs/hoek.git", "type": "git"}, "_npmVersion": "10.2.1", "description": "General purpose node utilities", "directories": {}, "_nodeVersion": "18.18.1", "eslintConfig": {"extends": ["plugin:@hapi/module"]}, "_hasShrinkwrap": false, "devDependencies": {"@hapi/lab": "^25.0.1", "@hapi/code": "^9.0.0", "typescript": "~4.6.4", "@types/node": "^17.0.30", "@hapi/eslint-plugin": "*"}, "_npmOperationalInternal": {"tmp": "tmp/hoek_11.0.4_1701773273095_0.4905943221505713", "host": "s3://npm-registry-packages"}}, "11.0.5": {"name": "@hapi/hoek", "version": "11.0.5", "keywords": ["utilities"], "license": "BSD-3-<PERSON><PERSON>", "_id": "@hapi/hoek@11.0.5", "maintainers": [{"name": "c<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marsup", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "devin<PERSON>y", "email": "<EMAIL>"}, {"name": "wyatt", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/hoek#readme", "bugs": {"url": "https://github.com/hapijs/hoek/issues"}, "dist": {"shasum": "2e927bf4fd4cfc06b12373c479be2aaddd2325b4", "tarball": "https://registry.npmjs.org/@hapi/hoek/-/hoek-11.0.5.tgz", "fileCount": 52, "integrity": "sha512-2G2uHO5I6sS7duo7l60rATT41tGfq3tsG6Ha07KV0PvLIM09E8oa48ozPp7GVa8gEL3UyeaioVNbqbQV7Hb9lg==", "signatures": [{"sig": "MEQCICt60h0bEk260ZaVI+MnTMkRiVbuLbzkmY5UMN7wVlvSAiBVbJliO1r0YxBEm3xNxu8VNAoB5vwbA4Ya6h0Cj7d6CA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55952}, "main": "lib/index.js", "types": "lib/index.d.ts", "exports": {".": {"types": "./lib/index.d.ts", "import": "./lib/index.mjs", "require": "./lib/index.js"}, "./once": "./lib/once.js", "./wait": "./lib/wait.js", "./bench": "./lib/bench.js", "./block": "./lib/block.js", "./clone": "./lib/clone.js", "./merge": "./lib/merge.js", "./reach": "./lib/reach.js", "./assert": "./lib/assert.js", "./ignore": "./lib/ignore.js", "./contain": "./lib/contain.js", "./flatten": "./lib/flatten.js", "./deepEqual": "./lib/deepEqual.js", "./intersect": "./lib/intersect.js", "./isPromise": "./lib/isPromise.js", "./stringify": "./lib/stringify.js", "./escapeHtml": "./lib/escapeHtml.js", "./escapeJson": "./lib/escapeJson.js", "./assertError": "./lib/assertError.js", "./escapeRegex": "./lib/escapeRegex.js", "./reachTemplate": "./lib/reachTemplate.js", "./applyToDefaults": "./lib/applyToDefaults.js", "./escapeHeaderAttribute": "./lib/escapeHeaderAttribute.js"}, "gitHead": "d9960cfa79bebf1744d5d192d8b0f9ebaaf57755", "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "_npmUser": {"name": "marsup", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/hapijs/hoek.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "General purpose node utilities", "directories": {}, "_nodeVersion": "22.10.0", "eslintConfig": {"extends": ["plugin:@hapi/module"]}, "_hasShrinkwrap": false, "devDependencies": {"@hapi/lab": "^25.0.1", "@hapi/code": "^9.0.0", "typescript": "~4.6.4", "@types/node": "^17.0.30", "@hapi/eslint-plugin": "^6.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/hoek_11.0.5_1729763786338_0.994699953042977", "host": "s3://npm-registry-packages"}}, "11.0.6": {"name": "@hapi/hoek", "version": "11.0.6", "keywords": ["utilities"], "license": "BSD-3-<PERSON><PERSON>", "_id": "@hapi/hoek@11.0.6", "maintainers": [{"name": "c<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marsup", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "devin<PERSON>y", "email": "<EMAIL>"}, {"name": "wyatt", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/hoek#readme", "bugs": {"url": "https://github.com/hapijs/hoek/issues"}, "dist": {"shasum": "b08e149fe588557eda2dbcf5ddca78c2eed089bb", "tarball": "https://registry.npmjs.org/@hapi/hoek/-/hoek-11.0.6.tgz", "fileCount": 52, "integrity": "sha512-mu8He+jghTDJ+la/uGBT4b1rqQdqFADZiXhzd98b3XW5nb/c+5woXx3FiNco2nm4wPJFHQVRGxYeWeSDPIYpYw==", "signatures": [{"sig": "MEYCIQDjPULNqnb4oEadQzD0z7BJHtsLwkgxRSAhiz7vAuF7yAIhAMV1VtYwxUQhcHvJigmPdEKIfBFivC21KeKtob8cveNF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55969}, "main": "lib/index.js", "types": "lib/index.d.ts", "exports": {".": {"types": "./lib/index.d.ts", "import": "./lib/index.mjs", "require": "./lib/index.js"}, "./once": "./lib/once.js", "./wait": "./lib/wait.js", "./bench": "./lib/bench.js", "./block": "./lib/block.js", "./clone": "./lib/clone.js", "./merge": "./lib/merge.js", "./reach": "./lib/reach.js", "./assert": "./lib/assert.js", "./ignore": "./lib/ignore.js", "./contain": "./lib/contain.js", "./flatten": "./lib/flatten.js", "./deepEqual": "./lib/deepEqual.js", "./intersect": "./lib/intersect.js", "./isPromise": "./lib/isPromise.js", "./stringify": "./lib/stringify.js", "./escapeHtml": "./lib/escapeHtml.js", "./escapeJson": "./lib/escapeJson.js", "./assertError": "./lib/assertError.js", "./escapeRegex": "./lib/escapeRegex.js", "./reachTemplate": "./lib/reachTemplate.js", "./applyToDefaults": "./lib/applyToDefaults.js", "./escapeHeaderAttribute": "./lib/escapeHeaderAttribute.js"}, "gitHead": "9b91eee216baef735d362d803d0f9e7460805ba0", "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "_npmUser": {"name": "marsup", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/hapijs/hoek.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "General purpose node utilities", "directories": {}, "_nodeVersion": "22.10.0", "eslintConfig": {"extends": ["plugin:@hapi/module"]}, "_hasShrinkwrap": false, "devDependencies": {"@hapi/lab": "^25.0.1", "@hapi/code": "^9.0.0", "typescript": "~4.6.4", "@types/node": "^17.0.30", "@hapi/eslint-plugin": "^6.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/hoek_11.0.6_1729807534095_0.20276746376058918", "host": "s3://npm-registry-packages"}}, "11.0.7": {"name": "@hapi/hoek", "description": "General purpose node utilities", "version": "11.0.7", "repository": {"type": "git", "url": "git://github.com/hapijs/hoek.git"}, "main": "lib/index.js", "types": "lib/index.d.ts", "exports": {".": {"import": "./lib/index.mjs", "require": "./lib/index.js", "types": "./lib/index.d.ts"}, "./applyToDefaults": "./lib/applyToDefaults.js", "./assert": "./lib/assert.js", "./assertError": "./lib/assertError.js", "./bench": "./lib/bench.js", "./block": "./lib/block.js", "./clone": "./lib/clone.js", "./contain": "./lib/contain.js", "./deepEqual": "./lib/deepEqual.js", "./escapeHeaderAttribute": "./lib/escapeHeaderAttribute.js", "./escapeHtml": "./lib/escapeHtml.js", "./escapeJson": "./lib/escapeJson.js", "./escapeRegex": "./lib/escapeRegex.js", "./flatten": "./lib/flatten.js", "./ignore": "./lib/ignore.js", "./intersect": "./lib/intersect.js", "./isPromise": "./lib/isPromise.js", "./merge": "./lib/merge.js", "./once": "./lib/once.js", "./reach": "./lib/reach.js", "./reachTemplate": "./lib/reachTemplate.js", "./stringify": "./lib/stringify.js", "./wait": "./lib/wait.js"}, "keywords": ["utilities"], "eslintConfig": {"extends": ["plugin:@hapi/module"]}, "devDependencies": {"@hapi/code": "^9.0.0", "@hapi/eslint-plugin": "^6.0.0", "@hapi/lab": "^25.0.1", "@types/node": "^17.0.30", "typescript": "~4.6.4"}, "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@hapi/hoek@11.0.7", "gitHead": "40e7940363febdb4d427d57c88eba96accdcc0e1", "bugs": {"url": "https://github.com/hapijs/hoek/issues"}, "homepage": "https://github.com/hapijs/hoek#readme", "_nodeVersion": "22.10.0", "_npmVersion": "10.9.0", "dist": {"integrity": "sha512-HV5undWkKzcB4RZUusqOpcgxOaq6VOAH7zhhIr2g3G8NF/MlFO75SjOr2NfuSx0Mh40+1FqCkagKLJRykUWoFQ==", "shasum": "56a920793e0a42d10e530da9a64cc0d3919c4002", "tarball": "https://registry.npmjs.org/@hapi/hoek/-/hoek-11.0.7.tgz", "fileCount": 52, "unpackedSize": 56136, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHZPff92RjLWN70OyrsY77aZtzMLBNYWPhpNh2IlGThNAiEA9YKGQjDbf9evT+l5+mLpS4WOYk85nz4qu0K6bLRhepE="}]}, "_npmUser": {"name": "marsup", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "c<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marsup", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "devin<PERSON>y", "email": "<EMAIL>"}, {"name": "wyatt", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/hoek_11.0.7_1731599672099_0.6156652436846677"}, "_hasShrinkwrap": false}}, "time": {"created": "2019-03-27T00:33:42.297Z", "modified": "2024-11-14T15:54:32.587Z", "6.2.0": "2019-03-27T00:33:42.563Z", "6.2.1": "2019-03-29T22:27:26.396Z", "6.2.2": "2019-05-17T20:10:47.356Z", "6.2.3": "2019-05-17T20:15:16.480Z", "6.2.4": "2019-05-27T00:44:01.199Z", "7.0.0": "2019-05-31T21:38:07.705Z", "7.1.0": "2019-06-01T17:48:58.001Z", "7.2.0": "2019-06-22T03:49:44.346Z", "7.2.1": "2019-06-23T04:10:11.794Z", "8.0.0": "2019-06-26T05:57:13.880Z", "8.0.1": "2019-06-26T17:17:59.712Z", "8.0.2": "2019-07-01T16:54:03.581Z", "8.1.0": "2019-07-24T17:41:11.598Z", "8.2.0": "2019-08-07T21:35:05.674Z", "8.2.1": "2019-08-12T19:48:09.087Z", "8.2.2": "2019-09-01T07:35:38.323Z", "8.2.3": "2019-09-08T19:14:54.845Z", "8.2.4": "2019-09-09T06:18:49.004Z", "8.2.5": "2019-09-28T07:13:03.264Z", "8.3.0": "2019-10-05T03:21:00.181Z", "8.3.1": "2019-10-15T06:27:33.972Z", "8.3.2": "2019-10-17T20:54:04.456Z", "8.4.0": "2019-10-30T06:42:39.896Z", "8.5.0": "2019-10-31T00:37:10.389Z", "9.0.0": "2020-01-03T23:06:43.327Z", "9.0.1": "2020-01-09T06:14:45.247Z", "9.0.2": "2020-01-09T18:27:28.494Z", "9.0.3": "2020-02-08T06:37:39.724Z", "8.5.1": "2020-02-08T06:45:41.173Z", "9.0.4": "2020-03-12T05:12:30.107Z", "9.1.0": "2020-09-02T01:35:38.140Z", "9.1.1": "2020-12-27T17:06:06.530Z", "9.2.0": "2021-04-17T21:10:47.837Z", "9.2.1": "2021-09-27T20:57:54.176Z", "9.3.0": "2022-05-01T18:01:35.233Z", "10.0.0": "2022-05-01T18:29:01.029Z", "10.0.1": "2022-07-23T18:31:23.980Z", "11.0.0": "2022-12-13T08:04:57.807Z", "11.0.1": "2022-12-13T19:34:43.458Z", "11.0.2": "2022-12-14T15:40:20.111Z", "11.0.3": "2023-12-05T08:07:52.430Z", "11.0.4": "2023-12-05T10:47:53.274Z", "11.0.5": "2024-10-24T09:56:26.560Z", "11.0.6": "2024-10-24T22:05:34.349Z", "11.0.7": "2024-11-14T15:54:32.283Z"}, "bugs": {"url": "https://github.com/hapijs/hoek/issues"}, "license": "BSD-3-<PERSON><PERSON>", "homepage": "https://github.com/hapijs/hoek#readme", "keywords": ["utilities"], "repository": {"type": "git", "url": "git://github.com/hapijs/hoek.git"}, "description": "General purpose node utilities", "maintainers": [{"name": "c<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marsup", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "devin<PERSON>y", "email": "<EMAIL>"}, {"name": "wyatt", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "<a href=\"https://hapi.dev\"><img src=\"https://raw.githubusercontent.com/hapijs/assets/master/images/family.png\" width=\"180px\" align=\"right\" /></a>\n\n# @hapi/hoek\n\n#### Utility methods for the hapi ecosystem.\n\n**hoek** is part of the **hapi** ecosystem and was designed to work seamlessly with the [hapi web framework](https://hapi.dev) and its other components (but works great on its own or with other frameworks). If you are using a different web framework and find this module useful, check out [hapi](https://hapi.dev) – they work even better together.\n\nThis module is not intended to solve every problem for everyone, but rather as a central place to store hapi-specific methods. If you're looking for a general purpose utility module, check out [lodash](https://github.com/lodash/lodash).\n\n### Visit the [hapi.dev](https://hapi.dev) Developer Portal for tutorials, documentation, and support\n\n## Useful resources\n\n- [Documentation and API](https://hapi.dev/family/hoek/)\n- [Version status](https://hapi.dev/resources/status/#hoek) (builds, dependencies, node versions, licenses, eol)\n- [Changelog](https://hapi.dev/family/hoek/changelog/)\n- [Project policies](https://hapi.dev/policies/)\n- [Free and commercial support options](https://hapi.dev/support/)\n", "readmeFilename": "README.md"}