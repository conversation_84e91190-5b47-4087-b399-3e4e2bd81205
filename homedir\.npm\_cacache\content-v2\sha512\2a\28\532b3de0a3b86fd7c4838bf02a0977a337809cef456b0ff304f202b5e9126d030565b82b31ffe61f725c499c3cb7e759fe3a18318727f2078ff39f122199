{"_id": "lightningcss-freebsd-x64", "_rev": "21-1e9eb48d7fd209b819896aea0f2de2c9", "name": "lightningcss-freebsd-x64", "dist-tags": {"latest": "1.30.1"}, "versions": {"1.21.6": {"name": "lightningcss-freebsd-x64", "version": "1.21.6", "license": "MPL-2.0", "_id": "lightningcss-freebsd-x64@1.21.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "c47664966e675cc1e97104b92c402b340bb26112", "tarball": "https://registry.npmjs.org/lightningcss-freebsd-x64/-/lightningcss-freebsd-x64-1.21.6.tgz", "fileCount": 4, "integrity": "sha512-L/fiLBo75v3CfvgaHqpuk7NY8hl7fJwoHFH6gwEUyurv88BjDNY6I4yHzyYSv6v7rsx34mQtSQY8K1XdnKWWBQ==", "signatures": [{"sig": "MEUCIFr5OfgFoMmU91wG0lQDOTcafkuPLinrbq+2m3oMMFLyAiEAqK9/ikoz87zz1tjJotJLpmkT2zOFO9q/hlbSxZOp+9c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9616297}, "main": "lightningcss.freebsd-x64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "f485eb51d13e677a674cb10e170c71138446b71d", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.17.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-freebsd-x64_1.21.6_1692511442402_0.13760586102267514", "host": "s3://npm-registry-packages"}}, "1.21.7": {"name": "lightningcss-freebsd-x64", "version": "1.21.7", "license": "MPL-2.0", "_id": "lightningcss-freebsd-x64@1.21.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "c9a622a2b3159198203c99ce7471b877cacf6a1c", "tarball": "https://registry.npmjs.org/lightningcss-freebsd-x64/-/lightningcss-freebsd-x64-1.21.7.tgz", "fileCount": 4, "integrity": "sha512-RMfNzJWXCSfPnL55fcLWEAadcY6QUFT0S8NceNKYzp1KiCZtkJIy6RQ5SaVxPzRqd3iMsahUf5sfnG8N1UQSNQ==", "signatures": [{"sig": "MEUCIQDcacUZGfqmjO+kUCfe3Zed/xJDuiwEn5JfTMHAh4+JywIgIYyJilNTpuO5OdxEn/zMw144Lhwpm4zcciqkExxVI5E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9616457}, "main": "lightningcss.freebsd-x64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "393013928888d47ec7684d52ed79f758d371bd7b", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.17.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-freebsd-x64_1.21.7_1692555041849_0.6957915178765031", "host": "s3://npm-registry-packages"}}, "1.21.8": {"name": "lightningcss-freebsd-x64", "version": "1.21.8", "license": "MPL-2.0", "_id": "lightningcss-freebsd-x64@1.21.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "d1b18c5a1b894e1332b23870afdbe23d07f22614", "tarball": "https://registry.npmjs.org/lightningcss-freebsd-x64/-/lightningcss-freebsd-x64-1.21.8.tgz", "fileCount": 4, "integrity": "sha512-CV6A/vTG2Ryd3YpChEgfWWv4TXCAETo9TcHSNx0IP0dnKcnDEiAko4PIKhCqZL11IGdN1ZLBCVPw+vw5ZYwzfA==", "signatures": [{"sig": "MEYCIQCqyHzVyjzsZRlHV/kJtydCkC45uj3LfycSb4ZAY2ijUgIhAKPMjQ117RHoVX3vWkl0vW6zHPs6ymfuMqDrt40gunj2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9616345}, "main": "lightningcss.freebsd-x64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "81cb0daee30dad924b14af7fbc739ddfe31d7f9e", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.17.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-freebsd-x64_1.21.8_1694407623870_0.15602319609717608", "host": "s3://npm-registry-packages"}}, "1.22.0": {"name": "lightningcss-freebsd-x64", "version": "1.22.0", "license": "MPL-2.0", "_id": "lightningcss-freebsd-x64@1.22.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "1ee7bcb68258b2cb1425bdc7ccb632233eae639c", "tarball": "https://registry.npmjs.org/lightningcss-freebsd-x64/-/lightningcss-freebsd-x64-1.22.0.tgz", "fileCount": 4, "integrity": "sha512-xaYL3xperGwD85rQioDb52ozF3NAJb+9wrge3jD9lxGffplu0Mn35rXMptB8Uc2N9Mw1i3Bvl7+z1evlqVl7ww==", "signatures": [{"sig": "MEUCIA+SgQFjCNeh2ZVsSJj4fH9VJorEbwA7ETaNvB2O5OqrAiEA3v+9V/MNPaBn8Vnvv30q46N7Lr6jzxsnk7mXyFW7vEE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9616193}, "main": "lightningcss.freebsd-x64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "7ff93ca5c69ba9df415e1e2319d275e2cec249d7", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.17.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-freebsd-x64_1.22.0_1694990934212_0.9457929139198933", "host": "s3://npm-registry-packages"}}, "1.22.1": {"name": "lightningcss-freebsd-x64", "version": "1.22.1", "license": "MPL-2.0", "_id": "lightningcss-freebsd-x64@1.22.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "dd1b19308e3b0f24b6f79da10fd3975e5e02ebda", "tarball": "https://registry.npmjs.org/lightningcss-freebsd-x64/-/lightningcss-freebsd-x64-1.22.1.tgz", "fileCount": 4, "integrity": "sha512-1FaBtcFrZqB2hkFbAxY//Pnp8koThvyB6AhjbdVqKD4/pu13Rl91fKt2N9qyeQPUt3xy7ORUvSO+dPk3J6EjXg==", "signatures": [{"sig": "MEUCIQCj0EaR//hkf++DReSP+7FnpyG2poj/++yUg5MUijju0AIgDeShyDYJKRLHwA3uMvhq3f4Wy/7TwdZx7G1sr44HffM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9635905}, "main": "lightningcss.freebsd-x64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "4994306f8f8d98a2b37433fced50efdacbbab901", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.18.2", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-freebsd-x64_1.22.1_1699395804975_0.33546286801057", "host": "s3://npm-registry-packages"}}, "1.23.0": {"name": "lightningcss-freebsd-x64", "version": "1.23.0", "license": "MPL-2.0", "_id": "lightningcss-freebsd-x64@1.23.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "d3f6faddc424f17ed046e8be9ca97868a5f804ed", "tarball": "https://registry.npmjs.org/lightningcss-freebsd-x64/-/lightningcss-freebsd-x64-1.23.0.tgz", "fileCount": 4, "integrity": "sha512-xhnhf0bWPuZxcqknvMDRFFo2TInrmQRWZGB0f6YoAsZX8Y+epfjHeeOIGCfAmgF0DgZxHwYc8mIR5tQU9/+ROA==", "signatures": [{"sig": "MEYCIQCAgSYC76pXRsSnFCSoiMUuG1OAs2zu8HXfzakzPGpnvwIhAJONT2kzHSixQykOGmg4pTJ9sfe6K9PD2DLncZx4bmRa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9683305}, "main": "lightningcss.freebsd-x64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "b47f496a86075adcb6719aee3a8867e749a880b9", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.19.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-freebsd-x64_1.23.0_1705276069689_0.013524329550782088", "host": "s3://npm-registry-packages"}}, "1.24.0": {"name": "lightningcss-freebsd-x64", "version": "1.24.0", "license": "MPL-2.0", "_id": "lightningcss-freebsd-x64@1.24.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "d8c94574f8e12b09cb8dcdd05333d9e2288cd906", "tarball": "https://registry.npmjs.org/lightningcss-freebsd-x64/-/lightningcss-freebsd-x64-1.24.0.tgz", "fileCount": 4, "integrity": "sha512-FJAYlek1wXuVTsncNU0C6YD41q126dXcIUm97KAccMn9C4s/JfLSqGWT2gIzAblavPFkyGG2gIADTWp3uWfN1g==", "signatures": [{"sig": "MEYCIQC6YgsC0aZbUjNznqkFwGeuF7z0LiFngIqttCylqxNqDwIhAOOL9QfnS6iTNhpHXHMa73eHJ882kF/TAkqQS9UBBmTf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9764729}, "main": "lightningcss.freebsd-x64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "30b9d79b3d277dfb90d90d78cd9182ac755ad6cf", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.19.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-freebsd-x64_1.24.0_1708648883666_0.7665817490229814", "host": "s3://npm-registry-packages"}}, "1.24.1": {"name": "lightningcss-freebsd-x64", "version": "1.24.1", "license": "MPL-2.0", "_id": "lightningcss-freebsd-x64@1.24.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "ff692c41ed0bbf37ab5a239db4c2fc04c11195e6", "tarball": "https://registry.npmjs.org/lightningcss-freebsd-x64/-/lightningcss-freebsd-x64-1.24.1.tgz", "fileCount": 4, "integrity": "sha512-z6<PERSON>berUUw5ALES6Ixn2shmjRRrM1cmEn1ZQPiM5IrZ6xHHL5a1lPin9pRv+w6eWfcrEo+qGG6R9XfJrpuY3e4g==", "signatures": [{"sig": "MEUCIQCANFCZ+IIJwRBA9WT9ueM9Atv6I2zNNwJyHNhFZV/y/gIgJ6eKQsrtEYpt7Ohz17itiy8q58jTNUR4P/rFCrv7V8U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9486001}, "main": "lightningcss.freebsd-x64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "baa1a2b7fa52eeb3827f8edcc2e14de33cd69ad0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.19.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-freebsd-x64_1.24.1_1710476572768_0.12739539043430437", "host": "s3://npm-registry-packages"}}, "1.25.0": {"name": "lightningcss-freebsd-x64", "version": "1.25.0", "license": "MPL-2.0", "_id": "lightningcss-freebsd-x64@1.25.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "ac464941153d73d75d23b5244dc673bc0d16031d", "tarball": "https://registry.npmjs.org/lightningcss-freebsd-x64/-/lightningcss-freebsd-x64-1.25.0.tgz", "fileCount": 4, "integrity": "sha512-f7v6QwrqCFtQOG1Y7iZ4P1/EAmMsyUyRBrYbSmDxihMzdsL7xyTM753H2138/oCpam+maw2RZrXe/NA1r/I5cQ==", "signatures": [{"sig": "MEUCIQCc85RWLTfeZuQVyfwOa4ZVOBAQSINa/iPkOkEncUgNXgIgHQaHw/0xb877WbN7QC5/IC9pLbVpFRft7JGzZXUlwAg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9652177}, "main": "lightningcss.freebsd-x64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "81d21b9f5201c6eb8365fbb4fa81cbd947c3474f", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.20.2", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-freebsd-x64_1.25.0_1715973751870_0.16362634528682185", "host": "s3://npm-registry-packages"}}, "1.25.1": {"name": "lightningcss-freebsd-x64", "version": "1.25.1", "license": "MPL-2.0", "_id": "lightningcss-freebsd-x64@1.25.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "bb37f25c2d136ff33b25dd08bee5e167afacc49c", "tarball": "https://registry.npmjs.org/lightningcss-freebsd-x64/-/lightningcss-freebsd-x64-1.25.1.tgz", "fileCount": 4, "integrity": "sha512-hXoy2s9A3KVNAIoKz+Fp6bNeY+h9c3tkcx1J3+pS48CqAt+5bI/R/YY4hxGL57fWAIquRjGKW50arltD6iRt/w==", "signatures": [{"sig": "MEUCICFRd1fgBHez0WKv4lDhqzAilSccKhX97hwNlHbercEFAiEA0MpZYRLZKimoSe/399huUfJnQaXUZ7wRImXar1Y57fo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9662601}, "main": "lightningcss.freebsd-x64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "fa6c015317e5cca29fa8a09b12d09ac53dcfbc77", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.20.2", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-freebsd-x64_1.25.1_1716617173644_0.36026325966629025", "host": "s3://npm-registry-packages"}}, "1.26.0": {"name": "lightningcss-freebsd-x64", "version": "1.26.0", "license": "MPL-2.0", "_id": "lightningcss-freebsd-x64@1.26.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "85d01defc5b2d9af3cba1d5d2f253d356c8cdc56", "tarball": "https://registry.npmjs.org/lightningcss-freebsd-x64/-/lightningcss-freebsd-x64-1.26.0.tgz", "fileCount": 4, "integrity": "sha512-C/io7POAxp6sZxFSVGezjajMlCKQ8KSwISLLGRq8xLQpQMokYrUoqYEwmIX8mLmF6C/CZPk0gFmRSzd8biWM0g==", "signatures": [{"sig": "MEUCIEU7SpZAFSCsxlvtsccrnIUbZdwFvDFo7Uxw21btLfNnAiEA8yFmsS3G6FnZKK9kFwiuE0IxYwmClbjh1CUX4x53Te4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9528673}, "main": "lightningcss.freebsd-x64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "0bcd896e81a8a2c5d847f626a37e2cffea79e2a0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.20.4", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-freebsd-x64_1.26.0_1722958362193_0.6014620596274729", "host": "s3://npm-registry-packages"}}, "1.27.0": {"name": "lightningcss-freebsd-x64", "version": "1.27.0", "license": "MPL-2.0", "_id": "lightningcss-freebsd-x64@1.27.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "a7c3c4d6ee18dffeb8fa69f14f8f9267f7dc0c34", "tarball": "https://registry.npmjs.org/lightningcss-freebsd-x64/-/lightningcss-freebsd-x64-1.27.0.tgz", "fileCount": 4, "integrity": "sha512-n1sEf85fePoU2aDN2PzYjoI8gbBqnmLGEhKq7q0DKLj0UTVmOTwDC7PtLcy/zFxzASTSBlVQYJUhwIStQMIpRA==", "signatures": [{"sig": "MEQCIEy7CKIKWgU/IlNBjSjFMl/5+6yRDC7hXK8MW5ohC17ZAiA1/E+xc+Sk5w/AoJa/nJLMC9rdnz5TD4gg+R0uJ+6pwA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9536617}, "main": "lightningcss.freebsd-x64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "eb49015cf887ae720b80a2856ccbdf61bf940ef1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.20.4", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-freebsd-x64_1.27.0_1726023636419_0.6998262811146849", "host": "s3://npm-registry-packages"}}, "1.28.0": {"name": "lightningcss-freebsd-x64", "version": "1.28.0", "license": "MPL-2.0", "_id": "lightningcss-freebsd-x64@1.28.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "d4923b62583549d43b8989a8fb025f97ecc094cb", "tarball": "https://registry.npmjs.org/lightningcss-freebsd-x64/-/lightningcss-freebsd-x64-1.28.0.tgz", "fileCount": 4, "integrity": "sha512-hgIfuFNsBJkAsK0RX6euB0MmVxsP70aAnJV/NsJSv1eYVUO/dAlutenLPFuZhBmfE60lassqDg32dZ/h3D8lAA==", "signatures": [{"sig": "MEYCIQCCZqqT3OQgQ2MITHiv+Ga8CDrAZaWv6eX7RbMLtZD3mQIhAN/H5CelNTsBIdLV06+Mgc/Dn43LuMBh+EbXZzijuUnx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9548310}, "main": "lightningcss.freebsd-x64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "8a67583105757e4a25378d65d243b87a345b2c2d", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"caniuse-lite": "^1.0.30001677", "lightningcss": "link:."}, "_nodeVersion": "18.20.4", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-freebsd-x64_1.28.0_1730668655177_0.5333542771817887", "host": "s3://npm-registry-packages"}}, "1.28.1": {"name": "lightningcss-freebsd-x64", "version": "1.28.1", "license": "MPL-2.0", "_id": "lightningcss-freebsd-x64@1.28.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "f8eb8b63845a88d32eed71a594cf224f6c7ea4fd", "tarball": "https://registry.npmjs.org/lightningcss-freebsd-x64/-/lightningcss-freebsd-x64-1.28.1.tgz", "fileCount": 4, "integrity": "sha512-b7sF89B31kYYijxVcFO7l5u6UNA862YstNu+3YbLl/IQKzveL4a5cwR5cdpG+OOhErg/c2u9WCmzZoX2I5GBvw==", "signatures": [{"sig": "MEYCIQDiEARJSyYEOdYnG1wjY73WxF+/fFB3pYfGoa9L9DxX5gIhAKDb3Tq5fGIpGBCKmVq0UETq8fafsrh6cFi0Qn4lPMpg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9549142}, "main": "lightningcss.freebsd-x64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "a3390fd4140ca87f5035595d22bc9357cf72177e", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"caniuse-lite": "^1.0.30001677", "lightningcss": "link:."}, "_nodeVersion": "18.20.4", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-freebsd-x64_1.28.1_1730674693647_0.289332836609955", "host": "s3://npm-registry-packages"}}, "1.28.2": {"name": "lightningcss-freebsd-x64", "version": "1.28.2", "license": "MPL-2.0", "_id": "lightningcss-freebsd-x64@1.28.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "804bc6652c6721e94a92e7bbb5e65165376cf108", "tarball": "https://registry.npmjs.org/lightningcss-freebsd-x64/-/lightningcss-freebsd-x64-1.28.2.tgz", "fileCount": 4, "integrity": "sha512-l2qrCT+x7crAY+lMIxtgvV10R8VurzHAoUZJaVFSlHrN8kRLTvEg9ObojIDIexqWJQvJcVVV3vfzsEynpiuvgA==", "signatures": [{"sig": "MEUCIQCaJw7Pc8CT77bDcRRmqQXM33WGeQ/ddyHUPLCUVyIAoAIgSziGLeHa4fxWj2ifR4Lw5GMXM34Hg4xcIDwBcVoOw+k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9553350}, "main": "lightningcss.freebsd-x64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "9b2e8bbe732d7c101272ddab03ac21b88bf55c4a", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"caniuse-lite": "^1.0.30001677", "lightningcss": "link:."}, "_nodeVersion": "18.20.5", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-freebsd-x64_1.28.2_1732512283388_0.9831378802040422", "host": "s3://npm-registry-packages"}}, "1.29.0": {"name": "lightningcss-freebsd-x64", "version": "1.29.0", "license": "MPL-2.0", "_id": "lightningcss-freebsd-x64@1.29.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "90b0e9885dab34d5790e7067da3476c8c849c340", "tarball": "https://registry.npmjs.org/lightningcss-freebsd-x64/-/lightningcss-freebsd-x64-1.29.0.tgz", "fileCount": 4, "integrity": "sha512-6LSOV2BsWh9bug15Ttb40vs9QAxSM6kof10K61nRp29sGuJDDH74g9FN6UGwAKu3/9OHlARn7vTdxi6Wubrm+Q==", "signatures": [{"sig": "MEUCIG1v3bdru+LVMSzvxb7kIjkMo3BLTFdt4qHmNWeFv88PAiEA9Wy7o6hSB15sylMVMjksskbPQ/Osrqu9HTTlGgtkFAI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9136022}, "main": "lightningcss.freebsd-x64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "222fa9f7ad8b4004afd7a30c2f99cab3c8621191", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"caniuse-lite": "^1.0.30001677", "lightningcss": "link:."}, "_nodeVersion": "20.18.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-freebsd-x64_1.29.0_1736401655118_0.3937935410989508", "host": "s3://npm-registry-packages-npm-production"}}, "1.29.1": {"name": "lightningcss-freebsd-x64", "version": "1.29.1", "license": "MPL-2.0", "_id": "lightningcss-freebsd-x64@1.29.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "4b3aec9620684a60c45266d50fd843869320f42f", "tarball": "https://registry.npmjs.org/lightningcss-freebsd-x64/-/lightningcss-freebsd-x64-1.29.1.tgz", "fileCount": 4, "integrity": "sha512-0SUW22fv/8kln2LnIdOCmSuXnxgxVC276W5KLTwoehiO0hxkacBxjHOL5EtHD8BAXg2BvuhsJPmVMasvby3LiQ==", "signatures": [{"sig": "MEYCIQCauRS9EewRwgDB01pTkpJTgzvSL445knhAEQV0a+k5jQIhAMo7YNIjGKy8gZ1TSoInMdqWWX80A9ke+/MZPZ8T8OI8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9137174}, "main": "lightningcss.freebsd-x64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "b10f9baf8878411bf2b09dfe8d64ba09ef7a4eac", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"caniuse-lite": "^1.0.30001677", "lightningcss": "link:."}, "_nodeVersion": "20.18.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-freebsd-x64_1.29.1_1736445750102_0.5018830080132177", "host": "s3://npm-registry-packages-npm-production"}}, "1.29.2": {"name": "lightningcss-freebsd-x64", "version": "1.29.2", "license": "MPL-2.0", "_id": "lightningcss-freebsd-x64@1.29.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "8a95f9ab73b2b2b0beefe1599fafa8b058938495", "tarball": "https://registry.npmjs.org/lightningcss-freebsd-x64/-/lightningcss-freebsd-x64-1.29.2.tgz", "fileCount": 4, "integrity": "sha512-wDk7M2tM78Ii8ek9YjnY8MjV5f5JN2qNVO+/0BAGZRvXKtQrBC4/cn4ssQIpKIPP44YXw6gFdpUF+Ps+RGsCwg==", "signatures": [{"sig": "MEUCIAqPj8BQtiNTox/KMewA5Jy4NPlHitpxvNJ6ceiAk+OSAiEAsQh6QKg/WtWMLXr/eeuCSPYrjdNJWDAeHh6J3OKirfw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9140481}, "main": "lightningcss.freebsd-x64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "f2303df29448a55c5f4f284f747eb53760769fe4", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "20.18.3", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-freebsd-x64_1.29.2_1741242109848_0.6567252115442008", "host": "s3://npm-registry-packages-npm-production"}}, "1.29.3": {"name": "lightningcss-freebsd-x64", "version": "1.29.3", "license": "MPL-2.0", "_id": "lightningcss-freebsd-x64@1.29.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "a777474c2747ec04ca99e66fe0a94d71f643e6e3", "tarball": "https://registry.npmjs.org/lightningcss-freebsd-x64/-/lightningcss-freebsd-x64-1.29.3.tgz", "fileCount": 4, "integrity": "sha512-VUWeVf+V1UM54jv9M4wen9vMlIAyT69Krl9XjI8SsRxz4tdNV/7QEPlW6JASev/pYdiynUCW0pwaFquDRYdxMw==", "signatures": [{"sig": "MEUCIQCx89qki1+qFTuniz8QtmO5mfW9MOORvQCn1mvy2A820QIgY97Nye3DdiTe/RByK4z8jt9gxJ8jX4DsVFsummsdKcM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9161505}, "main": "lightningcss.freebsd-x64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "80eb8617c5f8f5519ed85bd3eb6d8953f4d32493", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "20.18.3", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-freebsd-x64_1.29.3_1741974586492_0.835908709125732", "host": "s3://npm-registry-packages-npm-production"}}, "1.30.0": {"name": "lightningcss-freebsd-x64", "version": "1.30.0", "license": "MPL-2.0", "_id": "lightningcss-freebsd-x64@1.30.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "bd2da9045c294158902811ca60863bd6367ec464", "tarball": "https://registry.npmjs.org/lightningcss-freebsd-x64/-/lightningcss-freebsd-x64-1.30.0.tgz", "fileCount": 4, "integrity": "sha512-/sfAWALScgggjjk5ZlmGdpFELwGPIwzAdfcBJcT6UTgQoDHzQ4aP41XTq3N4LL01U9dsJp6uAvCvmHX7snqTdg==", "signatures": [{"sig": "MEUCIH48Q6rvHm56+noYymFdb0Mac2urLh4iaXPfiNx5r1hfAiEA29myM33jI5QG+ViOuTPFjso0fmu/tHIGoMgCEHAHfec=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9218449}, "main": "lightningcss.freebsd-x64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "474c67c046b4a54217166057296fa4ca28764858", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "20.19.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-freebsd-x64_1.30.0_1746945545343_0.8492405038283724", "host": "s3://npm-registry-packages-npm-production"}}, "1.30.1": {"name": "lightningcss-freebsd-x64", "version": "1.30.1", "license": "MPL-2.0", "description": "A CSS parser, transformer, and minifier written in Rust", "main": "lightningcss.freebsd-x64.node", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "git+https://github.com/parcel-bundler/lightningcss.git"}, "engines": {"node": ">= 12.0.0"}, "resolutions": {"lightningcss": "link:."}, "os": ["freebsd"], "cpu": ["x64"], "_id": "lightningcss-freebsd-x64@1.30.1", "gitHead": "f2dc67c4d3fe92f26693c02366db1e60cae0db27", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-kmW6UGCGg2PcyUE59K5r0kWfKPAVy4SltVeut+umLCFoJ53RdCUWxcRDzO1eTaxf/7Q2H7LTquFHPL5R+Gjyig==", "shasum": "a0e732031083ff9d625c5db021d09eb085af8be4", "tarball": "https://registry.npmjs.org/lightningcss-freebsd-x64/-/lightningcss-freebsd-x64-1.30.1.tgz", "fileCount": 4, "unpackedSize": 9218449, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIEsCm92Qd+5jOSN6rUC/jwZCX/JnhrOkJwiCFcsrsF+pAiEAn/S8jGPabmLD3OFERBnqPe/wd6P9sqrl6NFOegFdWXA="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/lightningcss-freebsd-x64_1.30.1_1747193915193_0.39883979241289036"}, "_hasShrinkwrap": false}}, "time": {"created": "2023-08-20T06:04:02.287Z", "modified": "2025-05-14T03:38:35.651Z", "1.21.6": "2023-08-20T06:04:02.680Z", "1.21.7": "2023-08-20T18:10:42.216Z", "1.21.8": "2023-09-11T04:47:04.161Z", "1.22.0": "2023-09-17T22:48:54.573Z", "1.22.1": "2023-11-07T22:23:25.339Z", "1.23.0": "2024-01-14T23:47:49.941Z", "1.24.0": "2024-02-23T00:41:24.010Z", "1.24.1": "2024-03-15T04:22:53.164Z", "1.25.0": "2024-05-17T19:22:32.098Z", "1.25.1": "2024-05-25T06:06:13.906Z", "1.26.0": "2024-08-06T15:32:42.587Z", "1.27.0": "2024-09-11T03:00:36.840Z", "1.28.0": "2024-11-03T21:17:35.519Z", "1.28.1": "2024-11-03T22:58:13.921Z", "1.28.2": "2024-11-25T05:24:43.693Z", "1.29.0": "2025-01-09T05:47:35.371Z", "1.29.1": "2025-01-09T18:02:30.302Z", "1.29.2": "2025-03-06T06:21:50.111Z", "1.29.3": "2025-03-14T17:49:46.730Z", "1.30.0": "2025-05-11T06:39:05.626Z", "1.30.1": "2025-05-14T03:38:35.485Z"}, "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "license": "MPL-2.0", "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "repository": {"type": "git", "url": "git+https://github.com/parcel-bundler/lightningcss.git"}, "description": "A CSS parser, transformer, and minifier written in Rust", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "This is the x86_64-unknown-freebsd build of lightningcss. See https://github.com/parcel-bundler/lightningcss for details.", "readmeFilename": "README.md"}