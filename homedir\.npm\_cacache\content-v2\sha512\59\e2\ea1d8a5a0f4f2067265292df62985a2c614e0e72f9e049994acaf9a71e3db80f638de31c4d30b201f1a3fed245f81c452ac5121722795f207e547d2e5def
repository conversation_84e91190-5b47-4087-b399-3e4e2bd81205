{"_id": "wildcard", "_rev": "28-37a06fcc51eee100c848d1b517b60dc6", "name": "wildcard", "description": "Wildcard matching tools", "dist-tags": {"latest": "2.0.1"}, "versions": {"0.0.1": {"name": "wildcard", "description": "Wildcard matching tools", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "tags": ["string", "wildcard"], "version": "0.0.1", "main": "./wildcard.js", "engines": {"node": ">= 0.4.x < 0.7.0"}, "dependencies": {}, "devDependencies": {"mocha": "*", "expect.js": "0.1.x"}, "repository": {"type": "git", "url": "git://github.com/Damon<PERSON>lman/wild.git"}, "bugs": {"url": "http://github.com/<PERSON><PERSON><PERSON>/wild/issues"}, "contributors": [], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "wildcard@0.0.1", "_engineSupported": true, "_npmVersion": "1.1.0-beta-10", "_nodeVersion": "v0.6.7", "_defaultsLoaded": true, "dist": {"shasum": "59bd7157156a039c318223b135e6f0d9c389b326", "tarball": "https://registry.npmjs.org/wildcard/-/wildcard-0.0.1.tgz", "integrity": "sha512-EmgdXd6xFM+vHMEEiB0+s5Z/NkLg5ooXzjHqj0hiEsdAELf27zhUnLjJdm4x5dVmXM/Jw2Qnl9c/jG+EgeigAw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDST1VDOpiwjH36tAEEZuBtlQXxXvNMId9Ch7zE97PJ6QIhAItRnNRcKcCHcvX8fGxlFHvnzFYcvQPYD3HcpjRN6QOn"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.1.2": {"name": "wildcard", "description": "Wildcard matching tools", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "tags": ["string", "wildcard"], "version": "0.1.2", "main": "dist/commonjs/wildcard", "engines": {"node": ">= 0.6.x < 0.9.0"}, "dependencies": {}, "devDependencies": {"mocha": "*", "expect.js": "0.1.x"}, "repository": {"type": "git", "url": "git://github.com/Damon<PERSON>lman/wildcard.git"}, "bugs": {"url": "http://github.com/<PERSON><PERSON><PERSON>/wildcard/issues"}, "scripts": {"test": "./node_modules/mocha/bin/mocha --reporter spec"}, "contributors": [], "_id": "wildcard@0.1.2", "dist": {"shasum": "704b7639baea4dd805532fa22fca84f6bf45e595", "tarball": "https://registry.npmjs.org/wildcard/-/wildcard-0.1.2.tgz", "integrity": "sha512-SvoRa/52rh67wYOLR39QyC5pLFdDkB8dowWUznY8hGk2U85PtmqgTF1mp37KGs/3tOJkikRtiSKEeaoTgmw5Bw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCvwlTrkB4hH/U7ZKi1LIum00/Zc3XvBSjKYbZ4FAALXwIgEL7NuCD7bibcC0JaYVHKXmM6EUBQSfroHvk+ppPcDig="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.1.3": {"name": "wildcard", "description": "Wildcard matching tools", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "tags": ["string", "wildcard"], "version": "0.1.3", "main": "wildcard", "engines": {"node": ">= 0.6.x < 0.9.0"}, "dependencies": {}, "devDependencies": {"mocha": "1.6.x", "expect.js": "0.1.x"}, "repository": {"type": "git", "url": "git://github.com/Damon<PERSON>lman/wildcard.git"}, "bugs": {"url": "http://github.com/<PERSON><PERSON><PERSON>/wildcard/issues"}, "scripts": {"test": "./node_modules/mocha/bin/mocha --reporter spec"}, "contributors": [], "_id": "wildcard@0.1.3", "dist": {"shasum": "581963b5c937bc1f0c624372079a04a3d6d9a35c", "tarball": "https://registry.npmjs.org/wildcard/-/wildcard-0.1.3.tgz", "integrity": "sha512-EGAKb/75SczLYbuG2RnZIggpDjSLfPykN96cXe0l0QGVPnZjAL/dHLX33Lr+aODMh6gEjzn7jfgdPlAGLor3oQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDILkbP/wnlMVNKwVDDHu0kyBD9x8mEZ3lYai82pCvAkAIgV/rEyqx2NPsOxq9wJsrZliQ7Di4QmkwIwCTFHgw+D80="}]}, "_npmVersion": "1.1.62", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.1.4": {"name": "wildcard", "description": "Wildcard matching tools", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "tags": ["string", "wildcard"], "version": "0.1.4", "dependencies": {}, "devDependencies": {"tape": "1.0.x"}, "testling": {"files": "test/*.js", "browsers": ["ie/9", "ie/10", "chrome/latest", "firefox/latest", "safari/latest", "opera/latest"]}, "repository": {"type": "git", "url": "git://github.com/Damon<PERSON>lman/wildcard.git"}, "bugs": {"url": "http://github.com/<PERSON><PERSON><PERSON>/wildcard/issues"}, "scripts": {"test": "./node_modules/.bin/tape test/*.js"}, "contributors": [], "_id": "wildcard@0.1.4", "dist": {"shasum": "f14bd497676e80098122cc3ce91b2401f18450df", "tarball": "https://registry.npmjs.org/wildcard/-/wildcard-0.1.4.tgz", "integrity": "sha512-HN6xUvGoAyCRPUTSPvDub7kTWj0vq9zGsecWdAtOH5xtRxQU7esRuRqrecDIRlPtSej1tDzJqPiFWedi1LhSpg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCAqaDK1njLaQcRJKQYXANJOHxal+taNaUOS1YyyI8tAwIhAMvinVFQWaboeVMWxGmvEJ8oR/Cl6l30aNwgAaNjFhWC"}]}, "_npmVersion": "1.1.62", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.1.5": {"name": "wildcard", "description": "Wildcard matching tools", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "keywords": ["string", "wildcard"], "version": "0.1.5", "dependencies": {}, "devDependencies": {"tape": "^2"}, "testling": {"files": "test/all.js", "browsers": {"ie": ["latest"], "ff": ["latest", "nightly"], "chrome": ["latest", "canary"], "opera": ["latest", "next"], "safari": ["latest"]}}, "repository": {"type": "git", "url": "git://github.com/Damon<PERSON>lman/wildcard.git"}, "bugs": {"url": "http://github.com/<PERSON><PERSON><PERSON>/wildcard/issues"}, "scripts": {"test": "$(npm bin)/tape test/all.js", "gendocs": "gendocs > README.md"}, "main": "index.js", "directories": {"test": "test"}, "license": "MIT", "gitHead": "2bfc84f73f5c06f6b991360d83fef0ff367c4634", "homepage": "https://github.com/<PERSON><PERSON><PERSON>/wildcard", "_id": "wildcard@0.1.5", "_shasum": "408513bc14f58cd3aacc7098a6820b2ef25b3dd0", "_from": ".", "_npmVersion": "1.4.13", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "408513bc14f58cd3aacc7098a6820b2ef25b3dd0", "tarball": "https://registry.npmjs.org/wildcard/-/wildcard-0.1.5.tgz", "integrity": "sha512-v+G9sqvXldof9hnzO1NUvVgnQShNKOOmU3M4nUv6QD058oZF6AfHUCz24zzAOFYtvunu+thbLCOXgspxARMZgg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDBy20WVoFLBeGEGZe0O+3N4tFK0GOA2kd95Fiam4uJagIgMWIjPn82YAth63P/tDw1jMmHYtiIBsQzLAt2sxdgO48="}]}}, "1.0.0": {"name": "wildcard", "description": "Wildcard matching tools", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "keywords": ["string", "wildcard"], "version": "1.0.0", "dependencies": {}, "devDependencies": {"tape": "^2"}, "testling": {"files": "test/all.js", "browsers": {"ie": ["latest"], "ff": ["latest", "nightly"], "chrome": ["latest", "canary"], "opera": ["latest", "next"], "safari": ["latest"]}}, "repository": {"type": "git", "url": "git://github.com/Damon<PERSON>lman/wildcard.git"}, "bugs": {"url": "http://github.com/<PERSON><PERSON><PERSON>/wildcard/issues"}, "scripts": {"test": "$(npm bin)/tape test/all.js", "gendocs": "gendocs > README.md"}, "main": "index.js", "directories": {"test": "test"}, "license": "MIT", "gitHead": "e5c5df16ca0fc991afc1317b601fb75f23ee5997", "homepage": "https://github.com/<PERSON><PERSON><PERSON>/wildcard", "_id": "wildcard@1.0.0", "_shasum": "5d39a502e17c0664ef9b2f2a02cc8213b12aab13", "_from": ".", "_npmVersion": "1.4.16", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "5d39a502e17c0664ef9b2f2a02cc8213b12aab13", "tarball": "https://registry.npmjs.org/wildcard/-/wildcard-1.0.0.tgz", "integrity": "sha512-ErioUxHw0Fs2TagejzKep3XyXMq9OQMv6QNDw+LUmwKjm6nHMBIYgHeW6KbAho8EQ+B6GcORevY1DJ+ykPyuFA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBSbEHL+Ee832xlPqVnCGrgNwkItbOn3ksT0FT6JYM5zAiBMwdvHgs75NALvVm/vyc4Q0hcYkCWokfxMqCRqCCZttw=="}]}}, "1.1.0": {"name": "wildcard", "description": "Wildcard matching tools", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "keywords": ["string", "wildcard"], "version": "1.1.0", "dependencies": {}, "devDependencies": {"tape": "^3.0.0"}, "testling": {"files": "test/all.js", "browsers": {"ie": ["latest"], "ff": ["latest", "nightly"], "chrome": ["latest", "canary"], "opera": ["latest", "next"], "safari": ["latest"]}}, "repository": {"type": "git", "url": "git://github.com/Damon<PERSON>lman/wildcard.git"}, "bugs": {"url": "http://github.com/<PERSON><PERSON><PERSON>/wildcard/issues"}, "scripts": {"test": "$(npm bin)/tape test/all.js", "gendocs": "gendocs > README.md"}, "main": "index.js", "directories": {"test": "test"}, "license": "MIT", "gitHead": "ffa0ecef1db3fba79544e635753291300d8f924d", "homepage": "https://github.com/<PERSON><PERSON><PERSON>/wildcard", "_id": "wildcard@1.1.0", "_shasum": "05cbabca1949373ab9ec87434d8b7209145b68cd", "_from": ".", "_npmVersion": "1.4.23", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "05cbabca1949373ab9ec87434d8b7209145b68cd", "tarball": "https://registry.npmjs.org/wildcard/-/wildcard-1.1.0.tgz", "integrity": "sha512-KSSYoSDesQ9Bon5BmIGqluMbjJJa3QASEFFBF404XE1a1V6XM9PvZmhH0GGySAUv4unVmv872cKIQQen+hAs4A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGsqaRqr5OPy76ca5OoIuwTGf2+P9V8qwI5UfZ8DVDihAiBvKAIpsgfhOqU99JzszuqgQPKO80jqA3vl76tIkf6+Pw=="}]}}, "1.1.1": {"name": "wildcard", "description": "Wildcard matching tools", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "keywords": ["string", "wildcard"], "version": "1.1.1", "dependencies": {}, "devDependencies": {"tape": "^3.0.0"}, "testling": {"files": "test/all.js", "browsers": {"ie": ["latest"], "ff": ["latest", "nightly"], "chrome": ["latest", "canary"], "opera": ["latest", "next"], "safari": ["latest"]}}, "repository": {"type": "git", "url": "git://github.com/Damon<PERSON>lman/wildcard.git"}, "bugs": {"url": "http://github.com/<PERSON><PERSON><PERSON>/wildcard/issues"}, "scripts": {"test": "$(npm bin)/tape test/all.js", "gendocs": "gendocs > README.md"}, "main": "index.js", "directories": {"test": "test"}, "license": "MIT", "gitHead": "818074d1430e42db5fc08c09428cc98d7abed7b1", "homepage": "https://github.com/<PERSON><PERSON><PERSON>/wildcard", "_id": "wildcard@1.1.1", "_shasum": "692bffc4d2cff00035c6b662c0f0baa6e72f6112", "_from": ".", "_npmVersion": "2.7.5", "_nodeVersion": "0.12.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "692bffc4d2cff00035c6b662c0f0baa6e72f6112", "tarball": "https://registry.npmjs.org/wildcard/-/wildcard-1.1.1.tgz", "integrity": "sha512-pAQGSkSDDwJ6eM34DB/qJPVCxOpjbc/txr1y4MNvs7Gmg4anlq6EHklmT0g2mIsbDLMltJYrilo3MNOby3LC8A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCyV4Nu9GSe9wdtUlSI8Y+SrE7UuLLXyasLOpOT4zrY6wIgO2NeBFs57lC31DVAwNR2sogTsMr824ap2tEJps7ZMgo="}]}}, "1.1.2": {"name": "wildcard", "description": "Wildcard matching tools", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "keywords": ["string", "wildcard"], "version": "1.1.2", "dependencies": {}, "devDependencies": {"tape": "^3.0.0"}, "testling": {"files": "test/all.js", "browsers": {"ie": ["latest"], "ff": ["latest", "nightly"], "chrome": ["latest", "canary"], "opera": ["latest", "next"], "safari": ["latest"]}}, "repository": {"type": "git", "url": "git://github.com/Damon<PERSON>lman/wildcard.git"}, "bugs": {"url": "http://github.com/<PERSON><PERSON><PERSON>/wildcard/issues"}, "scripts": {"test": "node test/all.js", "gendocs": "gendocs > README.md"}, "main": "index.js", "directories": {"test": "test"}, "license": "MIT", "gitHead": "d844aaa3ac09f35c79036178040c85b97ce74c54", "homepage": "https://github.com/<PERSON><PERSON><PERSON>/wildcard", "_id": "wildcard@1.1.2", "_shasum": "a7020453084d8cd2efe70ba9d3696263de1710a5", "_from": ".", "_npmVersion": "2.7.5", "_nodeVersion": "0.12.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "a7020453084d8cd2efe70ba9d3696263de1710a5", "tarball": "https://registry.npmjs.org/wildcard/-/wildcard-1.1.2.tgz", "integrity": "sha512-DXukZJxpHA8LuotRwL0pP1+rS6CS7FF2qStDDE1C7DDg2rLud2PXRMuEDYIPhgEezwnlHNL4c+N6MfMTjCGTng==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDAghKML7PO1qL+TFHRMlqHfeZzSSETKy9iA9AMolQ3kAIgDkEdwA+pkYXbjb20ykrbdh8tJ/vNActQ7de4u6bLMZ0="}]}}, "2.0.0": {"name": "wildcard", "description": "Wildcard matching tools", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "keywords": ["string", "wildcard"], "version": "2.0.0", "dependencies": {}, "devDependencies": {"tape": "^4.6.3"}, "testling": {"files": "test/all.js", "browsers": {"ie": ["latest"], "ff": ["latest", "nightly"], "chrome": ["latest", "canary"], "opera": ["latest", "next"], "safari": ["latest"]}}, "repository": {"type": "git", "url": "git://github.com/Damon<PERSON>lman/wildcard.git"}, "bugs": {"url": "http://github.com/<PERSON><PERSON><PERSON>/wildcard/issues"}, "scripts": {"test": "node test/all.js", "gendocs": "gendocs > README.md"}, "main": "index.js", "directories": {"test": "test"}, "license": "MIT", "gitHead": "becc50830de8a919b236dd53611dfaab24214946", "homepage": "https://github.com/<PERSON><PERSON><PERSON>/wildcard#readme", "_id": "wildcard@2.0.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-JcKqAHLPxcdb9KM49dufGXn2x3ssnfjbcaQdLlfZsL9rH9wgDQjUtDxbo8NE0F6SFvydeu1VhZe7hZuHsB2/pw==", "shasum": "a77d20e5200c6faaac979e4b3aadc7b3dd7f8fec", "tarball": "https://registry.npmjs.org/wildcard/-/wildcard-2.0.0.tgz", "fileCount": 13, "unpackedSize": 21670, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdEJzuCRA9TVsSAnZWagAAbksP/1GZq5W2mVHhvBY8p/ex\nFOHkBvE2OfQDGVc7qF9Haer6OGlyx2QzFhEE/OGLcV5ytVFQGRdiqysEAHTE\nz55YnIzUT/gA0ACKyWv5HqRBIFrPJJ5hJ235JEomEmenh95fuUP8SoR5mIFO\nW3o5kCWuy2WsCL7WwubewTsXbwoQwVcz1UDIgd5ZWtdQPtphOuOjk4rClIDg\nOB+9f3VJplPWvHZ/ArDiv0x8hrozORsyZmZPCFWGkNPsV+JWxfL9bg0Sr1eS\nUJ8mHXdc9r/xjjwBZfki+2Ul9byF/Osv6QrwO5PLK+ZteiOxNuo+NPihXTjz\nPMGkmqp6g3G17bYi3zBi1Po4FX++Vohu5s5331BePP/pyM0nH5teLNbrvJvw\nxQAz/dg86PX8V8yv2Kkn10hYjr8hF+P4GAJaT22pxgHgsTRjlb0cGjaBbWR1\nBI6y5YFj0egyeS6EAUb5x9SFn77+sMKmmAVZAufUhjsvJFcu0/+gTsdeUmTS\nuLDLpHNQOhVdHphEAEBRXq2A97/H3a1f1KpWQfZhbeSNZ1tqVy+33mXrD1YX\nF+dt27zh+JKfEcNlrmsxNT+bHIQrXcUlkSqVYLnVXhRaXZfj0WhIrQAXpHiW\nqJfdC7uYVLsw+NhT9Rlj8qdmfaB9kXnlJK4yZaEHIKgTAdHn4Zz9d2s1LE3w\nVVqG\r\n=ivsv\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCnYJsVMgoC6/UH/Wy0bwVjPyiUDnI6jxREk7sIu/yStAIhAJdm+NJLiWYm4Yj+qaXWSVX4SLj8HcRWpLihFUkUubI2"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/wildcard_2.0.0_1561369838168_0.7496851544388645"}, "_hasShrinkwrap": false}, "2.0.1": {"name": "wildcard", "description": "Wildcard matching tools", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "stability": "stable", "keywords": ["string", "wildcard"], "version": "2.0.1", "dependencies": {}, "devDependencies": {"embellish-readme": "^1.7.2", "tape": "^5.6.3"}, "repository": {"type": "git", "url": "git://github.com/Damon<PERSON>lman/wildcard.git"}, "bugs": {"url": "http://github.com/<PERSON><PERSON><PERSON>/wildcard/issues"}, "scripts": {"test": "node test/all.js", "gendocs": "embellish README.md"}, "main": "index.js", "directories": {"test": "test"}, "license": "MIT", "gitHead": "119827757597b9377d65b8b240f7ff7c6f25e348", "homepage": "https://github.com/<PERSON><PERSON><PERSON>/wildcard#readme", "_id": "wildcard@2.0.1", "_nodeVersion": "18.16.0", "_npmVersion": "9.6.5", "dist": {"integrity": "sha512-CC1bOL87PIWSBhDcTrdeLo6eGT7mCFtrg0uIJtqJUFyK+eJnzl8A1niH56uu7KMa5XFrtiV+AQuHO3n7DsHnLQ==", "shasum": "5ab10d02487198954836b6349f74fff961e10f67", "tarball": "https://registry.npmjs.org/wildcard/-/wildcard-2.0.1.tgz", "fileCount": 13, "unpackedSize": 13509, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICpBn65bzlTrKv8xChWv7NqkZ4bQvUW8stHb7aCHymBVAiALgfk7HU/OQZ3JBdbWAWaB1YdbFKLGsExX38ryXcqkgQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkRouUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq20g//anxHsuXsGWfK7JHs6I3xw6h0aq03ztTljyMb828Am0DwaleI\r\nGD/lXEnIuTOpLR23Jfzs4ZZtYbDeUvAkxSQPisY3Z5f5jteczThvhqQMvWiH\r\nT+nLifkr/Edr0fPuytwU/VsI8kx/Pnf5Py42nZQKKBSSTRYGuEq0DUe2wBs5\r\n4LD8mEEFWA0t7TA0sUWHXooSoIolN8j2HbKrIg643rmiuUkh4LpEQnLE4UdZ\r\nxoksKo9yER7beyvRVzK19NEIVqT1oMp3pLyO8yy25vBHPZbfl8EaMaM+fxxL\r\nFMa6fhAwwKRJYxOSk6RI3977bgUlRXKOsenrwSkYpTBAJzgYcCaeRoFDyzuE\r\n1xIeSuBpldxh1o7SIQdlTXq02Dfrv53aDqnF2C2h8FTdo7MHDi538P+g+Ey5\r\nZkQXlwyH59Gr28ywabECzEx9Hkcj8BVz3fT8MyJt2KqC1VXj79Bp3OqGHNYg\r\n0yEzCmQxba7EuBOYakxWYl72f1kxeQqqz5xPx09Xtem+8YcVQg1CT0Kwm0hz\r\nz7sELBqVrz6q8qw1nBmohMPIePlVvxeGG1G2E9l0UrM/Gt3ythYOK+s8lY5H\r\nCfl89aZoSMvhYU5SLvPhCAJNkRQuO5bMnl6c9yqhMmTteFmtoj7Wp4YfHmt9\r\nOLmmB1/51eUOVM8ttXHeTe/6PLCgKQopsyk=\r\n=Qogm\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/wildcard_2.0.1_1682344851786_0.6365284008570309"}, "_hasShrinkwrap": false}}, "readme": "# wildcard\n\nVery simple wildcard matching, which is designed to provide the same\nfunctionality that is found in the\n[eve](https://github.com/adobe-webplatform/eve) eventing library.\n\n[![NPM](https://nodei.co/npm/wildcard.png)](https://nodei.co/npm/wildcard/)\n\n[![stable](https://img.shields.io/badge/stability-stable-green.svg)](https://github.com/dominictarr/stability#stable)\n\n## Usage\n\nIt works with strings:\n\n```js\nvar wildcard = require('wildcard');\n\nconsole.log(wildcard('foo.*', 'foo.bar'));\n// --> true\n\nconsole.log(wildcard('foo.*', 'foo'));\n// --> true\n```\n\nArrays:\n\n```js\nvar wildcard = require('wildcard');\nvar testdata = [\n  'a.b.c',\n  'a.b',\n  'a',\n  'a.b.d'\n];\n\nconsole.log(wildcard('a.b.*', testdata));\n// --> ['a.b.c', 'a.b', 'a.b.d']\n```\n\nObjects (matching against keys):\n\n```js\nvar wildcard = require('wildcard');\nvar testdata = {\n  'a.b.c' : {},\n  'a.b'   : {},\n  'a'     : {},\n  'a.b.d' : {}\n};\n\nconsole.log(wildcard('a.*.c', testdata));\n// --> { 'a.b.c': {} }\n```\n\n## Alternative Implementations\n\n* <https://github.com/isaacs/node-glob>\n\nGreat for full file-based wildcard matching.\n\n* <https://github.com/sindresorhus/matcher>\n\nA well cared for and loved JS wildcard matcher.\n\n## License(s)\n\n### MIT\n\nCopyright (c) 2023 Damon Oehlman <&#x6d;&#x61;&#105;&#108;&#116;&#x6f;&#x3a;&#x64;&#x61;&#109;&#111;&#110;&#46;&#111;&#101;&#x68;&#108;&#x6d;&#97;&#x6e;&#x40;&#x67;&#x6d;&#x61;&#x69;&#x6c;&#x2e;&#x63;&#111;&#109;>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2023-04-24T14:00:52.088Z", "created": "2012-03-13T10:02:00.601Z", "0.0.1": "2012-03-13T10:02:05.426Z", "0.1.2": "2012-07-24T00:48:58.549Z", "0.1.3": "2012-10-29T22:40:53.915Z", "0.1.4": "2013-05-05T10:48:30.859Z", "0.1.5": "2014-06-08T09:11:05.338Z", "1.0.0": "2014-09-07T23:10:25.977Z", "1.1.0": "2014-10-02T07:00:19.301Z", "1.1.1": "2015-04-20T00:05:42.043Z", "1.1.2": "2015-04-21T23:36:50.006Z", "2.0.0": "2019-06-24T09:50:38.320Z", "2.0.1": "2023-04-24T14:00:51.975Z"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git://github.com/Damon<PERSON>lman/wildcard.git"}, "users": {"fgribreau": true, "paragi": true, "kehanshi": true, "gaboo": true, "sergeymakoveev": true, "serebro": true}, "homepage": "https://github.com/<PERSON><PERSON><PERSON>/wildcard#readme", "keywords": ["string", "wildcard"], "bugs": {"url": "http://github.com/<PERSON><PERSON><PERSON>/wildcard/issues"}, "license": "MIT", "readmeFilename": "README.md"}