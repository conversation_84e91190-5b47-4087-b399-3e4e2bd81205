{"_id": "lodash.isnumber", "_rev": "37-46f356e85abac4a576de1ed46fb80dea", "name": "lodash.isnumber", "description": "The lodash method `_.isNumber` exported as a module.", "dist-tags": {"latest": "3.0.3"}, "versions": {"2.0.0": {"name": "lodash.isnumber", "version": "2.0.0", "description": "The Lo-Dash function `_.isNumber` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["customize", "functional", "lodash", "performance", "speed", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "_id": "lodash.isnumber@2.0.0", "dist": {"shasum": "c91b4436135571ebba5aa491c3139428ad056a33", "tarball": "https://registry.npmjs.org/lodash.isnumber/-/lodash.isnumber-2.0.0.tgz", "integrity": "sha512-6fHYX34O29ZBdP82qE2jRaXznRhATSpGxPq+xxwIqEuPPAkR5191KI39tqFdJpdmlkXwe5VW2rU6V2jZmWxKGA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDfGsZnh+QZWtEGAPC6DTWEH0iJe18adVxV4kSKKPYHvwIhALt38MKn7ukRU1hvLDo81c9+gIop4rjm1NZHea5HTLGe"}]}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}}, "2.1.0": {"name": "lodash.isnumber", "version": "2.1.0", "description": "The Lo-Dash function `_.isNumber` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "performance", "speed", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "_id": "lodash.isnumber@2.1.0", "dist": {"shasum": "caa73582435721a1f22cfc22a6247886e6566b81", "tarball": "https://registry.npmjs.org/lodash.isnumber/-/lodash.isnumber-2.1.0.tgz", "integrity": "sha512-xlZstwUA/WPa2v/clHTv6Cbq63nnTBUMNRENvDRZdDNKRiej6slr+EX5ElJH+73Fb0WEILIy0asqSxMMlbD+oQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDStXVCrhKUmPPT1KY/VzT9FfH982BjlIHkz8uNT3y+4AIgCPRLef0TfEzR8w8aozvzCOXPgGieE10/ip1+u3u3jig="}]}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}]}, "2.2.0": {"name": "lodash.isnumber", "version": "2.2.0", "description": "The Lo-Dash function `_.isNumber` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "performance", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "_id": "lodash.isnumber@2.2.0", "dist": {"shasum": "b51d9d45c46921f0dc553c5c24e16a1190934df4", "tarball": "https://registry.npmjs.org/lodash.isnumber/-/lodash.isnumber-2.2.0.tgz", "integrity": "sha512-0FRlffw995J00SLbksn2HlAnBrc/A290vosr9rqQKTdDlemJcTIaeMWU1ooHB928U/piZJrcqXZaRjgGrsylJA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCK0cBK4IMi1vaMhTV38lAQKAiHeKYjpk3vnFjyy5SCKwIhAIQC+A1JIeCjL+EKPJwamgfUnoeKCMgV5Y6hjcppQj3z"}]}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}]}, "2.2.1": {"name": "lodash.isnumber", "version": "2.2.1", "description": "The Lo-Dash function `_.isNumber` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "_id": "lodash.isnumber@2.2.1", "dist": {"shasum": "4db02489f7f4f0d23cd8865c74d8d5b1a75cbfd6", "tarball": "https://registry.npmjs.org/lodash.isnumber/-/lodash.isnumber-2.2.1.tgz", "integrity": "sha512-h0ry1B1ZYl2x8HV3R8uOvjn+oEKPQZrGbGa5loDaWEhK6F5kp58FxACbEPSPRRhKWtwVqubdYYSbDNJD7fakwQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEkb3CQfGdixztDCZh6w1mOfHZ02RKWailXD05qjo1GiAiEAiH681tPjiy4QAOj7A4KWF2EDZPprKbPWiN6KZHNJ89s="}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}]}, "2.3.0": {"name": "lodash.isnumber", "version": "2.3.0", "description": "The Lo-Dash function `_.isNumber` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "_id": "lodash.isnumber@2.3.0", "dist": {"shasum": "eb1c9ec48a046f4054b4671d59a67bbe2f4da6e2", "tarball": "https://registry.npmjs.org/lodash.isnumber/-/lodash.isnumber-2.3.0.tgz", "integrity": "sha512-kBK+44JoHCR1gH4ZLI55hZulo407kBsbSFcAicbg/Az/0zhYSYFvcoR056CiN/Io28DZrkp5TCHlVM65Uh+a7w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCQeq2OQQP3qg/L5B4ww46iGLczq4zWrQ5s/FJWeBOWFgIgZH86Zdk22TQ+Fo63KHSdOQjGp/1LJV12IKofTTXV9vw="}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}]}, "2.4.0": {"name": "lodash.isnumber", "version": "2.4.0", "description": "The Lo-Dash function `_.isNumber` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "_id": "lodash.isnumber@2.4.0", "dist": {"shasum": "d492ab459bd84c1b663f6bcbdccea512f8b81faf", "tarball": "https://registry.npmjs.org/lodash.isnumber/-/lodash.isnumber-2.4.0.tgz", "integrity": "sha512-CCNBLvcCvdiM1K9pekMRwYHU2zn1B4LggyNuTPq2QixkjFUPY/zzpudDhXhucZ79NRPABmu+UcyvmyqFq5e9iQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD3saMAALBuAHSuG9DYfEqXuOiHGPJZq9jx77SvJTOWHAIgFROE10CEaq05G5eyDYPcvdA8frOIrGf9Zok1N14/L9M="}]}, "_from": ".", "_npmVersion": "1.3.14", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}]}, "2.4.1": {"name": "lodash.isnumber", "version": "2.4.1", "description": "The Lo-Dash function `_.isNumber` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "_id": "lodash.isnumber@2.4.1", "dist": {"shasum": "fa57e8890ed618d037da4b117af3f30f3fc2b260", "tarball": "https://registry.npmjs.org/lodash.isnumber/-/lodash.isnumber-2.4.1.tgz", "integrity": "sha512-TEFqBAwhKXCQ6SNWSw+W+VxZoaW2FL63Bh1UaejzudRxD563IGARdVOCte28I5zIsnnz/yX45ATCJuXv3xjuUA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC5sninX7FUGgEbYhcsI8m+SM5B1x3aLz3x8Vd5ygci9AIgGjpMPPUQRw0X7u47NXT6udpcr5w52BkLODIWLDk5XIQ="}]}, "_from": ".", "_npmVersion": "1.3.14", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}]}, "3.0.0": {"name": "lodash.isnumber", "version": "3.0.0", "description": "The modern build of lodash’s `_.isNumber` as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "https://github.com/lodash/lodash"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isnumber@3.0.0", "_shasum": "2bc60cbf4a11aa86ff2f1f1d42250d1cd2ec82b0", "_from": ".", "_npmVersion": "2.3.0", "_nodeVersion": "0.10.35", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "dist": {"shasum": "2bc60cbf4a11aa86ff2f1f1d42250d1cd2ec82b0", "tarball": "https://registry.npmjs.org/lodash.isnumber/-/lodash.isnumber-3.0.0.tgz", "integrity": "sha512-YUYBtz8KjuhzCDmfEcTqJA6OWEZnougpB1ijAaGsevYAannmAjsnvfNZ5AR5YZauXI0nzw4amISFxpLGQhCH9Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEG2jUy5VsrF3V2kEiv7yln9nShnayBQXZ09bWxxifY+AiADRTtua7DOPxUl4oq+diHE3M2v7rWTgzrAZEH8D0ofAw=="}]}}, "3.0.1": {"name": "lodash.isnumber", "version": "3.0.1", "description": "The modern build of lodash’s `_.isNumber` as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "https://github.com/lodash/lodash"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isnumber@3.0.1", "_shasum": "628a1f3f198e2ddcd2b7eb9163540b6776255985", "_from": ".", "_npmVersion": "2.7.3", "_nodeVersion": "0.12.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "dist": {"shasum": "628a1f3f198e2ddcd2b7eb9163540b6776255985", "tarball": "https://registry.npmjs.org/lodash.isnumber/-/lodash.isnumber-3.0.1.tgz", "integrity": "sha512-CPbcXi5uYF5EjI2DgfACYZm7HbgTEUcoa3y4P1p0Vilw237Lrd5PZfZiXqp3x3we1b4N4m2Di6gcIW/zJaW2iQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDMUJR8D+yhMNJM7JywJiaMAKWf16KZROgEcGHTrgm4wQIhAN5+O2eaKJq/4RXVYLEc9lju2yA23gnM8hhM9EcuFf64"}]}}, "3.0.2": {"name": "lodash.isnumber", "version": "3.0.2", "description": "The lodash method `_.isNumber` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util", "isnumber"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isnumber@3.0.2", "_shasum": "282a00f60b02142a8870cbf8b549a28a08d63e3b", "_from": ".", "_npmVersion": "2.14.15", "_nodeVersion": "5.4.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "282a00f60b02142a8870cbf8b549a28a08d63e3b", "tarball": "https://registry.npmjs.org/lodash.isnumber/-/lodash.isnumber-3.0.2.tgz", "integrity": "sha512-JZgo0TV8Cv3IYX3FrwHzuXH+aYba33abqXlCnYID35lAebLVT3kMmxVFb/x/joTEKGTipiQGz4LR9MIcgVhmcA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDYZ7sAop5oGJGu9AOFZOwkvnQhLk/6JQs19WoMInvplgIhAPOpVSRlGe8P+WHQnHEbcMXSujLgPnKEyY6LLXV5MEWT"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}]}, "3.0.3": {"name": "lodash.isnumber", "version": "3.0.3", "description": "The lodash method `_.isNumber` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "isnumber"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isnumber@3.0.3", "_shasum": "3ce76810c5928d03352301ac287317f11c0b1ffc", "_from": ".", "_npmVersion": "2.14.15", "_nodeVersion": "5.4.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "3ce76810c5928d03352301ac287317f11c0b1ffc", "tarball": "https://registry.npmjs.org/lodash.isnumber/-/lodash.isnumber-3.0.3.tgz", "integrity": "sha512-QYqzpfwO3/CWf3XP+Z+tkQsfaLL/EnUlXWVkIk5FUPc4sBdTehEqZONuyRt2P67PXAk+NXmTBcc97zw9t1FQrw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG6+l4NRd+fvZa48eVKCJZkETeh+GjmutK93BmhYcrehAiEA1T4UWhdcCOEN8KcH6vMRg/rxMNJwTARbg+YIf32VswQ="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-6-west.internal.npmjs.com", "tmp": "tmp/lodash.isnumber-3.0.3.tgz_1454484528642_0.7204555512871593"}}}, "readme": "# lodash.isnumber v3.0.3\n\nThe [lodash](https://lodash.com/) method `_.isNumber` exported as a [Node.js](https://nodejs.org/) module.\n\n## Installation\n\nUsing npm:\n```bash\n$ {sudo -H} npm i -g npm\n$ npm i --save lodash.isnumber\n```\n\nIn Node.js:\n```js\nvar isNumber = require('lodash.isnumber');\n```\n\nSee the [documentation](https://lodash.com/docs#isNumber) or [package source](https://github.com/lodash/lodash/blob/3.0.3-npm-packages/lodash.isnumber) for more details.\n", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "time": {"modified": "2022-06-19T13:36:07.958Z", "created": "2013-09-23T06:34:29.463Z", "2.0.0": "2013-09-23T07:38:02.142Z", "2.1.0": "2013-09-23T07:56:24.832Z", "2.2.0": "2013-09-29T22:09:50.053Z", "2.2.1": "2013-10-03T18:50:25.229Z", "2.3.0": "2013-11-11T16:47:46.064Z", "2.4.0": "2013-11-26T19:55:53.113Z", "2.4.1": "2013-12-03T17:14:35.262Z", "3.0.0": "2015-01-26T15:29:24.615Z", "3.0.1": "2015-03-25T23:35:54.870Z", "3.0.2": "2016-01-13T11:05:53.918Z", "3.0.3": "2016-02-03T07:28:49.514Z"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "homepage": "https://lodash.com/", "keywords": ["lodash-modularized", "isnumber"], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "license": "MIT", "readmeFilename": "README.md"}