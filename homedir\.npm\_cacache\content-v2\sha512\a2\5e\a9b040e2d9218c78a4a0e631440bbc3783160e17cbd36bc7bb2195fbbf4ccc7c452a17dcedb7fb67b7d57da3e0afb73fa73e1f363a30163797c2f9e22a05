{"_id": "methods", "_rev": "47-f69eaa18654f2476607e3d4d75d30472", "name": "methods", "dist-tags": {"latest": "1.1.2"}, "versions": {"0.0.1": {"name": "methods", "version": "0.0.1", "keywords": ["http", "methods"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "methods@0.0.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "277c90f8bef39709645a8371c51c3b6c648e068c", "tarball": "https://registry.npmjs.org/methods/-/methods-0.0.1.tgz", "integrity": "sha512-pB8oFfci/xcfUgM6DTxc7lbTKifPPgs3mZUOsEgaH+1TTWpmcmv3sHl+5sUHIj2X2W8aPYa2+nJealRHK+Lo6A==", "signatures": [{"sig": "MEUCIQCLp/RSMYPxRMcEnBJAysaJ22qn+0Y4GRjK/hM/dvyJggIgGj6jN5J6HqQD95VqlhlmwRhdVtIvMqVjZiqwC6RATZ8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "description": "HTTP methods that node supports", "directories": {}}, "0.1.0": {"name": "methods", "version": "0.1.0", "keywords": ["http", "methods"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "methods@0.1.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/visionmedia/node-methods/issues"}, "dist": {"shasum": "335d429eefd21b7bacf2e9c922a8d2bd14a30e4f", "tarball": "https://registry.npmjs.org/methods/-/methods-0.1.0.tgz", "integrity": "sha512-N4cn4CbDqu7Fp3AT4z3AsO19calgczhsmCGzXLCiUOrWg9sjb1B+yKFKOrnnPGKKvjyJBmw+k6b3adFN2LbuBw==", "signatures": [{"sig": "MEYCIQDLHHN7lRJSUYrmRMJH55DixlUflF1KhdNAUzi+SiJQNgIhAPS68LloAI3GP12sJOCUVC/xUsGjDFWbXApkyaFVvNEn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/visionmedia/node-methods.git", "type": "git"}, "_npmVersion": "1.3.8", "description": "HTTP methods that node supports", "directories": {}}, "1.0.0": {"name": "methods", "version": "1.0.0", "keywords": ["http", "methods"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "methods@1.0.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/node-methods", "bugs": {"url": "https://github.com/visionmedia/node-methods/issues"}, "dist": {"shasum": "9a73d86375dfcef26ef61ca3e4b8a2e2538a80e3", "tarball": "https://registry.npmjs.org/methods/-/methods-1.0.0.tgz", "integrity": "sha512-rr3+O8WLgLeqV0mAowDraG0fOaWNwZmZjm4B0d8lSyyqS5di8E7dm/Ec/a1FVkHPMyRqN2PQNSNEk9cd7zXePg==", "signatures": [{"sig": "MEQCIGgkjGgE5P9Ya3NjCfNZhTaQbCOwzsU29rZZ8xujAsbGAiBtGncX0gJ/XMg0rvx0SvBYmSDw0E8NTFw2AYrpJ7i7kQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "./node_modules/mocha/bin/mocha"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/visionmedia/node-methods.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "HTTP methods that node supports", "directories": {}, "devDependencies": {"mocha": "1.17.x"}}, "1.0.1": {"name": "methods", "version": "1.0.1", "keywords": ["http", "methods"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "methods@1.0.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/node-methods", "bugs": {"url": "https://github.com/visionmedia/node-methods/issues"}, "dist": {"shasum": "75bc91943dffd7da037cf3eeb0ed73a0037cd14b", "tarball": "https://registry.npmjs.org/methods/-/methods-1.0.1.tgz", "integrity": "sha512-2403MfnVypWSNIEpmQ26/ObZ5kSUx37E8NHRvriw0+I8Sne7k0HGuLGCk0OrCqURh4UIygD0cSsYq+Ll+kzNqA==", "signatures": [{"sig": "MEUCIQDWn5rqFPdgXfRmx3YR42N6C7dm/HB7sPDcsxcvv2LKqQIgX2zyG5fyjrtgUP2xO+jvOD3Wre73VLgvEHRTgYwVu3A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "./node_modules/mocha/bin/mocha"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/visionmedia/node-methods.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "HTTP methods that node supports", "directories": {}, "devDependencies": {"mocha": "1.17.x"}}, "1.1.0": {"name": "methods", "version": "1.1.0", "keywords": ["http", "methods"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "methods@1.1.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/visionmedia/node-methods", "bugs": {"url": "https://github.com/visionmedia/node-methods/issues"}, "dist": {"shasum": "5dca4ee12df52ff3b056145986a8f01cbc86436f", "tarball": "https://registry.npmjs.org/methods/-/methods-1.1.0.tgz", "integrity": "sha512-Th88HxNePtsAmz0WjEhVVyRGv9AQFLv4z6zOj4Dt15PjsKLWB8JXSmxzP+Q27139+AXao0AlCWvonFuJhu4GuA==", "signatures": [{"sig": "MEQCIH82sdnTr6MJMKOVUxhhEfV0Odx3hz3SxeyhHmS0pTrwAiBo+6woVKGCYx3fhwdHc/XxW/6GZvQQDLMtDxRr+tUrBg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "5dca4ee12df52ff3b056145986a8f01cbc86436f", "gitHead": "2a4dd325d18436c33356e6be19e32e08a7c877ab", "scripts": {"test": "./node_modules/mocha/bin/mocha"}, "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/visionmedia/node-methods.git", "type": "git"}, "_npmVersion": "1.4.16", "description": "HTTP methods that node supports", "directories": {}, "devDependencies": {"mocha": "1.17.x"}}, "1.1.1": {"name": "methods", "version": "1.1.1", "keywords": ["http", "methods"], "license": "MIT", "_id": "methods@1.1.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/methods", "bugs": {"url": "https://github.com/jshttp/methods/issues"}, "dist": {"shasum": "17ea6366066d00c58e375b8ec7dfd0453c89822a", "tarball": "https://registry.npmjs.org/methods/-/methods-1.1.1.tgz", "integrity": "sha512-jokk6GTgwlNnUGTSh6AjRRHwnQkDRC8tj39+zfzfZTDQURIOao9qiCO7o5r7fmog3I1MD6doJf71NmknZNoxdA==", "signatures": [{"sig": "MEUCIBJwmhkVHjTtODB7EYbSY3csuwFImGbAgzLxolfk4OcJAiEAlrDEKtkIbxAVs7RXaSpjXNSZK7Awe3649zoHaFsQqQc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "HISTORY.md", "LICENSE"], "_shasum": "17ea6366066d00c58e375b8ec7dfd0453c89822a", "browser": {"http": false}, "engines": {"node": ">= 0.6"}, "gitHead": "6293c6b27c5fb963acf67a347af80ad2ebd7247f", "scripts": {"test": "mocha --reporter spec", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/methods", "type": "git"}, "_npmVersion": "1.4.28", "description": "HTTP methods that node supports", "directories": {}, "devDependencies": {"mocha": "1", "istanbul": "0.3"}}, "1.1.2": {"name": "methods", "version": "1.1.2", "keywords": ["http", "methods"], "license": "MIT", "_id": "methods@1.1.2", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/methods", "bugs": {"url": "https://github.com/jshttp/methods/issues"}, "dist": {"shasum": "5529a4d67654134edcc5266656835b0f851afcee", "tarball": "https://registry.npmjs.org/methods/-/methods-1.1.2.tgz", "integrity": "sha512-iclAHeNqNm68zFtnZ0e+1L2yUIdvzNoauKU4WBA3VvH/vPFieF7qfRlwUZU+DA9P9bPXIS90ulxoUoCH23sV2w==", "signatures": [{"sig": "MEQCICMj+uSw7vKtablTbz7vOHvidI6YY+tj7rkzBiDezXSDAiBEV6yn3f0wlT6OQc6dZ9zDWBZjoJW5YblMA0x73RJyFQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "HISTORY.md", "LICENSE"], "_shasum": "5529a4d67654134edcc5266656835b0f851afcee", "browser": {"http": false}, "engines": {"node": ">= 0.6"}, "gitHead": "25d257d913f1b94bd2d73581521ff72c81469140", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/methods", "type": "git"}, "_npmVersion": "1.4.28", "description": "HTTP methods that node supports", "directories": {}, "devDependencies": {"mocha": "1.21.5", "istanbul": "0.4.1"}}}, "time": {"created": "2012-06-26T18:34:52.941Z", "modified": "2025-05-14T14:56:00.308Z", "0.0.1": "2012-06-26T18:35:02.616Z", "0.1.0": "2013-10-28T19:02:02.097Z", "1.0.0": "2014-05-08T19:17:32.955Z", "1.0.1": "2014-06-02T14:26:09.655Z", "1.1.0": "2014-07-06T02:45:33.027Z", "1.1.1": "2014-12-30T23:51:21.181Z", "1.1.2": "2016-01-18T02:53:56.364Z"}, "bugs": {"url": "https://github.com/jshttp/methods/issues"}, "license": "MIT", "homepage": "https://github.com/jshttp/methods", "keywords": ["http", "methods"], "repository": {"url": "https://github.com/jshttp/methods", "type": "git"}, "description": "HTTP methods that node supports", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "maintainers": [{"email": "<EMAIL>", "name": "ulisesgascon"}, {"email": "<EMAIL>", "name": "b<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "jonathan<PERSON><PERSON><PERSON>@gmail.com", "name": "j<PERSON><PERSON><PERSON>"}, {"email": "jonathan<PERSON><PERSON><PERSON>@gmail.com", "name": "jongleberry"}], "readme": "# Methods\n\n[![NPM Version][npm-image]][npm-url]\n[![NPM Downloads][downloads-image]][downloads-url]\n[![Node.js Version][node-version-image]][node-version-url]\n[![Build Status][travis-image]][travis-url]\n[![Test Coverage][coveralls-image]][coveralls-url]\n\nHTTP verbs that Node.js core's HTTP parser supports.\n\nThis module provides an export that is just like `http.METHODS` from Node.js core,\nwith the following differences:\n\n  * All method names are lower-cased.\n  * Contains a fallback list of methods for Node.js versions that do not have a\n    `http.METHODS` export (0.10 and lower).\n  * Provides the fallback list when using tools like `browserify` without pulling\n    in the `http` shim module.\n\n## Install\n\n```bash\n$ npm install methods\n```\n\n## API\n\n```js\nvar methods = require('methods')\n```\n\n### methods\n\nThis is an array of lower-cased method names that Node.js supports. If Node.js\nprovides the `http.METHODS` export, then this is the same array lower-cased,\notherwise it is a snapshot of the verbs from Node.js 0.10.\n\n## License\n\n[MIT](LICENSE)\n\n[npm-image]: https://img.shields.io/npm/v/methods.svg?style=flat\n[npm-url]: https://npmjs.org/package/methods\n[node-version-image]: https://img.shields.io/node/v/methods.svg?style=flat\n[node-version-url]: https://nodejs.org/en/download/\n[travis-image]: https://img.shields.io/travis/jshttp/methods.svg?style=flat\n[travis-url]: https://travis-ci.org/jshttp/methods\n[coveralls-image]: https://img.shields.io/coveralls/jshttp/methods.svg?style=flat\n[coveralls-url]: https://coveralls.io/r/jshttp/methods?branch=master\n[downloads-image]: https://img.shields.io/npm/dm/methods.svg?style=flat\n[downloads-url]: https://npmjs.org/package/methods\n", "readmeFilename": "README.md", "users": {"m42am": true, "daizch": true, "monjer": true, "pgilad": true, "semir2": true, "leonzhao": true, "moimikey": true, "nketchum": true, "mojaray2k": true, "junjiansyu": true, "simplyianm": true, "yangjiyuan": true, "wangnan0610": true, "nickeltobias": true, "tobiasnickel": true, "zhangyaochun": true}}