{"_id": "qs", "_rev": "420-a6b1231b6cfe2e6c8afed8f2b6bf72e7", "name": "qs", "dist-tags": {"v5.x-latest": "5.2.1", "v6.2-latest": "6.2.4", "v6.3-latest": "6.3.3", "v6.4-latest": "6.4.1", "v6.5-latest": "6.5.3", "v6.6-latest": "6.6.1", "v6.7-latest": "6.7.3", "v6.8-latest": "6.8.3", "v6.9-latest": "6.9.7", "latest": "6.14.0"}, "versions": {"0.0.1": {"name": "qs", "version": "0.0.1", "author": {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "qs@0.0.1", "dist": {"shasum": "ce6017433e83b67231af788c6eb00ba0dba3f964", "tarball": "https://registry.npmjs.org/qs/-/qs-0.0.1.tgz", "integrity": "sha512-ZA/YpSnCr9SUkM4MuuaSgFHCp0L2YCnnnwiMLqQAnEV8wgjCHQn/MgIiRMn4/8BaDKfl7p8HJVI9NJX81+JJfQ==", "signatures": [{"sig": "MEUCIAbFyxvdySX7cpsrSbriQkk7jWxcIUzEVNcJw1GpNiOPAiEA3bGHyJHyygOwoiwj+zVR2VhvA5jw+2FvpXZTNqCF8OY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "files": [""], "engines": {"node": "*"}, "modules": {"querystring.js": "lib/querystring.js"}, "repository": {}, "_npmVersion": "0.2.16", "description": "querystring parser", "directories": {"lib": "./lib"}, "_nodeVersion": "v0.3.7", "_defaultsLoaded": true, "_engineSupported": true}, "0.0.2": {"name": "qs", "version": "0.0.2", "author": {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "qs@0.0.2", "dist": {"shasum": "b4a180d16b43d7f592128e9f6b609e3f225a8d7a", "tarball": "https://registry.npmjs.org/qs/-/qs-0.0.2.tgz", "integrity": "sha512-/6YlAZZ3y2g5c9pVeVWF56ZNn95jcy3jDY9lL/KyjVea+MI8FEEyQuqsVPYMAe+qqpCBnlKhxFVPWfNwoTpBvg==", "signatures": [{"sig": "MEUCIHnhr84G6kGEyBOO097IkGtkS2WM2D37XJrrQDt1EQTpAiEAn4kE8pichU0WFQfGAjUT1WjI/kpras3rmak54hlHaio=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "files": [""], "engines": {"node": "*"}, "modules": {"querystring.js": "lib/querystring.js"}, "repository": {}, "_npmVersion": "0.2.16", "description": "querystring parser", "directories": {"lib": "./lib"}, "_nodeVersion": "v0.2.6", "_defaultsLoaded": true, "_engineSupported": true}, "0.0.3": {"name": "qs", "version": "0.0.3", "author": {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "qs@0.0.3", "dist": {"shasum": "e16a190316d4eca7555c6d361f1aa9f1ff4ce124", "tarball": "https://registry.npmjs.org/qs/-/qs-0.0.3.tgz", "integrity": "sha512-hHMmdXwzYU7GiESpOygHEwiXDZNJJ/Zkfmz8S0S126HFtoeYRC+ZslzwWJb63DgcncZRquvz6fIVcIkXoQTnpQ==", "signatures": [{"sig": "MEYCIQDuUeQ0X/kRgjfHdIpu+sEng0UwqpcNAq5w0sENQqpjKQIhAKW0LtVgINlT5Nkk0aaA6MYsXoc4BFkvvBxPgsVSYJIu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "files": [""], "engines": {"node": "*"}, "modules": {"querystring.js": "lib/querystring.js"}, "repository": {}, "_npmVersion": "0.2.16", "description": "querystring parser", "directories": {"lib": "./lib"}, "_nodeVersion": "v0.3.8", "_defaultsLoaded": true, "_engineSupported": true}, "0.0.4": {"name": "qs", "version": "0.0.4", "author": {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "qs@0.0.4", "dist": {"shasum": "5484dfa3cb8f58cca93ba1247d8a3015be0d9cfa", "tarball": "https://registry.npmjs.org/qs/-/qs-0.0.4.tgz", "integrity": "sha512-YWpPhjf+Oo2alb61r7D34QwFIqeKAM3M0/NuBlS/3RFwtMDu18Po/10NMQW66aGNjJaz78uU9GpRhNd8rEoB8A==", "signatures": [{"sig": "MEYCIQCW8bh5XR01zglqTO6xfyHyu0pfc3YLUaOdJjrBc5Mh3AIhAMmW9YEew+jtT671m5Nk5KyrO8CeEAM0ypKPLUse9rrE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "files": [""], "engines": {"node": "*"}, "modules": {"querystring.js": "lib/querystring.js"}, "repository": {}, "_npmVersion": "0.2.16", "description": "querystring parser", "directories": {"lib": "./lib"}, "_nodeVersion": "v0.3.8", "_defaultsLoaded": true, "_engineSupported": true}, "0.0.5": {"name": "qs", "version": "0.0.5", "author": {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "qs@0.0.5", "dist": {"shasum": "54e6adc62a8bfd1d837d81cd6bf1d68ff203aba5", "tarball": "https://registry.npmjs.org/qs/-/qs-0.0.5.tgz", "integrity": "sha512-Ir/r8QYnLeM7MVffvBLR8dCJJ0Wxzw2/qLbI/rkiqa6L8Q/+nIFHJmRnxY3uGTAvjwWVABB9V2Myf/iBiaWziA==", "signatures": [{"sig": "MEYCIQD1qavoTCFn8Fgtf9Hdp54EXelvLdxzarPXYE5jvBUAYAIhAPcLkpicgATBsfHhRvXHGF3ta1ZMoApExas/yzQiK9Vn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "files": [""], "engines": {"node": "*"}, "modules": {"querystring.js": "lib/querystring.js"}, "repository": {}, "_npmVersion": "0.2.16", "description": "querystring parser", "directories": {"lib": "./lib"}, "_nodeVersion": "v0.4.0", "_defaultsLoaded": true, "_engineSupported": true}, "0.0.6": {"name": "qs", "version": "0.0.6", "author": {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "qs@0.0.6", "dist": {"shasum": "481659b7e5bf6a5ea898010de5aed35eb469e124", "tarball": "https://registry.npmjs.org/qs/-/qs-0.0.6.tgz", "integrity": "sha512-1i8kQcg7L3IYwt9uLfTfAGucLE+wrp7hB+xEGbM0yFp0tbsykoXmSyi+AWn0qdglKMdPzIrzS6w5Ack0ZvkfqQ==", "signatures": [{"sig": "MEYCIQCsfobzYLEUkegwuolsg8KOD2QNRLPVlCZdG6dDIirVSwIhAICjcCWuhoMhnqrZBskkiaK2llQeKV96ZdKZgTIyVl8v", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "files": [""], "engines": {"node": "*"}, "modules": {"querystring.js": "lib/querystring.js"}, "repository": {}, "_npmVersion": "0.2.16", "description": "querystring parser", "directories": {"lib": "./lib"}, "_nodeVersion": "v0.2.6", "_defaultsLoaded": true, "_engineSupported": true}, "0.0.7": {"name": "qs", "version": "0.0.7", "author": {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "qs@0.0.7", "dist": {"shasum": "7a85d1c3cd17ad9ba94211cbb24b57f88f75ec40", "tarball": "https://registry.npmjs.org/qs/-/qs-0.0.7.tgz", "integrity": "sha512-UOP7s2JDFA6tUZkBOVsEuMOMwHfshh9PUZTnWrATtXzosohjDtgHA6BJ4gB6r9ZM50Ou8BPt9iNAYbRm1jASIw==", "signatures": [{"sig": "MEYCIQCUeRLrxFUyj2wCkUWOAM+fezbjpz5cDxQ0SPmI97a96QIhAPkRWOj4i7z/N0bd3lz2Jclw5FahFfqpCRTIia+H1dQB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "files": [""], "engines": {"node": "*"}, "repository": {"url": ""}, "_npmVersion": "0.3.15", "description": "querystring parser", "directories": {"lib": "./lib"}, "_nodeVersion": "v0.4.2", "_defaultsLoaded": true, "_engineSupported": true}, "0.1.0": {"name": "qs", "version": "0.1.0", "author": {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "qs@0.1.0", "dist": {"shasum": "9a0d2d70d01f63d3401ea4b050822601b462ee6b", "tarball": "https://registry.npmjs.org/qs/-/qs-0.1.0.tgz", "integrity": "sha512-ToZQFywckFeLEvpo5Ofy2AwXb1ywEUhZlRdN3OyZZN8262BkPPiX8dlp5YIfeV50PkrEkkXjGTN6VNLMChGUyg==", "signatures": [{"sig": "MEUCIAmLRtPzGk0ic1+xRM8cbR56U+9HSJIWMMu2P+ZoqqG4AiEA0qeyV5pn1f00PLHeuFYMQc3p7K2w+EcOXeQrnQ363os=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "files": [""], "engines": {"node": "*"}, "repository": {"url": ""}, "_npmVersion": "0.3.18", "description": "querystring parser", "directories": {"lib": "./lib"}, "_nodeVersion": "v0.4.5", "_defaultsLoaded": true, "_engineSupported": true}, "0.2.0": {"name": "qs", "version": "0.2.0", "author": {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "qs@0.2.0", "dist": {"shasum": "b39234e77a55f6276a9ca305210db02304792487", "tarball": "https://registry.npmjs.org/qs/-/qs-0.2.0.tgz", "integrity": "sha512-THeDmmiD3gM+/GIajyKAf33FLrWVFQlRIHU74cLxx/UsjQhj7hFRjAXfoD+c4yAOJD7M7MA3elLMSUyT77OVug==", "signatures": [{"sig": "MEYCIQDI9fq78d494vEOP8+N872cYpchbxOILl9zYvpbZ98KfQIhAIca1ma5/p/O9/7aij5hXzswSESjYloiQamUhWzTHog/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": "*"}, "scripts": {}, "repository": {"url": ""}, "_npmVersion": "1.0.14", "description": "querystring parser", "directories": {}, "_nodeVersion": "v0.4.8", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/qs/0.2.0/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.3.0": {"name": "qs", "version": "0.3.0", "author": {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "qs@0.3.0", "dist": {"shasum": "502ec1168b8f778921c31d0e4351759bb97cfb84", "tarball": "https://registry.npmjs.org/qs/-/qs-0.3.0.tgz", "integrity": "sha512-rqFWzA5c/lKwkEN3HTy8a9eCbMLDox4mr0ZgYms9+KkCvF/DlccgktysyLnmfzKDY42jCBAUmsRJWodrL7Nocw==", "signatures": [{"sig": "MEUCIQCzZYpSOQtzJZfO2A5gGKKzJbyxCw3suDQTbCGQ4H8XawIgRth8Q/ISgny3KLLQonNZwu6nJxOIJJOQIrQwnTMiSSI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": "*"}, "scripts": {}, "repository": {"url": ""}, "_npmVersion": "1.0.14", "description": "querystring parser", "directories": {}, "_nodeVersion": "v0.4.9", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/qs/0.3.0/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.3.1": {"name": "qs", "version": "0.3.1", "author": {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "qs@0.3.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "42871928506939d6b29d1b192f20e2a28b2c69f4", "tarball": "https://registry.npmjs.org/qs/-/qs-0.3.1.tgz", "integrity": "sha512-lPN8zwdQziuT9qpYR0qcOVudXc03FEBKyLItuDVZd3Bu2lrnnSON1KN8Y7FTKy8PvqVmsidtt8qgqVAF4NEDCg==", "signatures": [{"sig": "MEUCICTdbdU9WEQvx2nYJ+tZo9RngfLDJTo+d6LBijBEjWU9AiEAvFNWgzHsGN/vK90fm51DpWNzkN88M439JYxS9zORgMs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": "*"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/visionmedia/node-querystring.git", "type": "git"}, "_npmVersion": "1.0.102", "description": "querystring parser", "directories": {}, "_nodeVersion": "v0.4.12", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"should": "*", "expresso": "0.9.2"}, "_engineSupported": true}, "0.3.2": {"name": "qs", "version": "0.3.2", "author": {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "qs@0.3.2", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "099df444cab947bbadb2bfedda56d33519d6815c", "tarball": "https://registry.npmjs.org/qs/-/qs-0.3.2.tgz", "integrity": "sha512-lQN7JG5NsxXWk40OWkqXhm2sXLhvWdpVxpHpUCHfsp7Lap4i8dCof+Xnt46FVrN29Ye+6YyZHdUUhYXo6pi3wg==", "signatures": [{"sig": "MEUCIQD8/u63h12EbFJSjSliuCt0Cn6rYpNhYlOVSI5F9ie10QIgaPHBDV8McOx1eYODMZSIVTRDMs2o/+UGrjT6Atq11l4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": "*"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/visionmedia/node-querystring.git", "type": "git"}, "_npmVersion": "1.0.104", "description": "querystring parser", "directories": {}, "_nodeVersion": "v0.6.0", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"should": "*", "expresso": "0.9.2"}, "_engineSupported": true}, "0.4.0": {"name": "qs", "version": "0.4.0", "author": {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "qs@0.4.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "32343c3df5937fcd46e10bd0521b31a01b062705", "tarball": "https://registry.npmjs.org/qs/-/qs-0.4.0.tgz", "integrity": "sha512-Y4<PERSON><PERSON>ahNMUC5FpRVCA4+XNO5YKqQQW9UTSvHPBiu1RPh/aw6DgAeKb39l3Jd/asI6z5chdEdrzb7aZHGHGsTww==", "signatures": [{"sig": "MEYCIQDzPoZwHfCFWKGkFkHY8QTTCYui9OYI2cmEgWzCMEkJVwIhANrnMFJkT7TgVMV/1e7M+E5mo5SwyQbaXqvI0clMXyiA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": "*"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/visionmedia/node-querystring.git", "type": "git"}, "_npmVersion": "1.0.104", "description": "querystring parser", "directories": {}, "_nodeVersion": "v0.6.1", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"mocha": "*", "should": "*"}, "_engineSupported": true}, "0.4.1": {"name": "qs", "version": "0.4.1", "author": {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "qs@0.4.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "bea85a35a4f29c80864db6e3955725d2594417a2", "tarball": "https://registry.npmjs.org/qs/-/qs-0.4.1.tgz", "integrity": "sha512-l4bomba+u1nXEY2YPySnBjzxXkPgI+2BXaTsT01MdQvZoMBLzhZFUunDltPLYP446us5DbaJiYeeZTpG1h1dEw==", "signatures": [{"sig": "MEUCIHy0vv7EYI+2qp5QNqWEZ9hKe/QJVdUnhJFghZ2H+S9rAiEA1as+I556pQZrf8dTupPbAFzacwpRI5/cSOcm+mabFcQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": "*"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/visionmedia/node-querystring.git", "type": "git"}, "_npmVersion": "1.0.106", "description": "querystring parser", "directories": {}, "_nodeVersion": "v0.6.8", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"mocha": "*", "should": "*"}, "_engineSupported": true}, "0.4.2": {"name": "qs", "version": "0.4.2", "author": {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "qs@0.4.2", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "3cac4c861e371a8c9c4770ac23cda8de639b8e5f", "tarball": "https://registry.npmjs.org/qs/-/qs-0.4.2.tgz", "integrity": "sha512-VAtfWeUtlBOnGiWFok2vOIMmwumiLmpqXDcXQcAEIQmLYgDbjrkHrcFBfth+YVDeRsz7jX44dhJr7IBJR0t/FQ==", "signatures": [{"sig": "MEUCIH4AAGxcBNWpBASGeDIocssMU5tUGVLQuR4pUoaoA8NXAiEA/XkMTraFC/5wIxQd2zsEmcCpE7gqcXyIc3A+YM6vYm8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": "*"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/visionmedia/node-querystring.git", "type": "git"}, "_npmVersion": "1.0.106", "description": "querystring parser", "directories": {}, "_nodeVersion": "v0.6.6", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"mocha": "*", "should": "*"}, "_engineSupported": true}, "0.5.0": {"name": "qs", "version": "0.5.0", "keywords": ["query string", "parser", "component"], "author": {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "qs@0.5.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "fda53429faaa8a3a72f630941d4851144a24d34e", "tarball": "https://registry.npmjs.org/qs/-/qs-0.5.0.tgz", "integrity": "sha512-/ohfWFi5SHwH3Px8MdvGS+WPGCKiv5hjYAVCTgvGdDxEfCELLvrcDZV7mfTvrigWLGYi4R61z5mfM6rn8YGXKg==", "signatures": [{"sig": "MEUCIQCT53e4+X0XDLJfrAJem9cYjaJ1phHsoKJZggFvIbGvdgIgHrt01A1zSByXNs3eN7xMU8pS6Dg99PyplFaMfxmIEhg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": "*"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"querystring": "querystring.js"}}, "repository": {"url": "git://github.com/visionmedia/node-querystring.git", "type": "git"}, "_npmVersion": "1.1.0-3", "description": "querystring parser", "directories": {}, "_nodeVersion": "v0.6.12", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"mocha": "*", "expect.js": "*"}, "_engineSupported": true, "optionalDependencies": {}}, "0.5.1": {"name": "qs", "version": "0.5.1", "keywords": ["query string", "parser", "component"], "author": {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "qs@0.5.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "9f6bf5d9ac6c76384e95d36d15b48980e5e4add0", "tarball": "https://registry.npmjs.org/qs/-/qs-0.5.1.tgz", "integrity": "sha512-1NhhAEZMTI+2tQrOAGFlS1HFmKCcI9mvsysUbfqvvz6ObXwxCvPuAqzD+5LYBbEfjrdSOakWzaZx4wFPnND+xA==", "signatures": [{"sig": "MEUCIGXtLpPH2Gg681myJD1NxxFpk8w72ZW53A8iQrB/V0D/AiEA9LB6YOObhA+mNUyJs3FZ9mzZpOBerfN0QwQ7lK9DgRM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": "*"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"querystring": "querystring.js"}}, "repository": {"url": "git://github.com/visionmedia/node-querystring.git", "type": "git"}, "_npmVersion": "1.1.61", "description": "querystring parser", "directories": {}, "devDependencies": {"mocha": "*", "expect.js": "*"}}, "0.5.2": {"name": "qs", "version": "0.5.2", "keywords": ["query string", "parser", "component"], "author": {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "qs@0.5.2", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "e5734acb7009fb918e800fd5c60c2f5b94a7ff43", "tarball": "https://registry.npmjs.org/qs/-/qs-0.5.2.tgz", "integrity": "sha512-QYDRP8JGJ3fTiCieCglCfvN6BYBWd/qAm9F4KVAdSNE16Okoyzn/8KQxegJdZ78brV81jTLfK7/KO8YQ94fF6g==", "signatures": [{"sig": "MEUCIQCRKxnYqQE8OAIjEdkO9S9+u52qGIAgli8myqKx2FH67gIgYLQmetC80Jue5DHUUkdB29WLKLUFvSJZxHvxI3EwuhA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": "*"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"querystring": "querystring.js"}}, "repository": {"url": "git://github.com/visionmedia/node-querystring.git", "type": "git"}, "_npmVersion": "1.1.65", "description": "querystring parser", "directories": {}, "devDependencies": {"mocha": "*", "expect.js": "*"}}, "0.5.3": {"name": "qs", "version": "0.5.3", "keywords": ["query string", "parser", "component"], "author": {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "qs@0.5.3", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "1f6a85d62780de8c0b3b8c1090e39e1c316f306f", "tarball": "https://registry.npmjs.org/qs/-/qs-0.5.3.tgz", "integrity": "sha512-rL2nWFMRtz5AV/IAb4Ow+4VZcwrYLDHq6mpHHJxw0+E5irRg8DDQlzSm8bAHjDwwk+UsFOzyE91wL+hudYoBMA==", "signatures": [{"sig": "MEYCIQDBRVNstz2uhBlO9Gt/7MEkHOZB0LLV62SdUKDre9mvywIhAPai+TLBfFBSEGBqqxqz9zgHnomRy6fTVdpefevmsK82", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": "*"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"querystring": "querystring.js"}}, "repository": {"url": "git://github.com/visionmedia/node-querystring.git", "type": "git"}, "_npmVersion": "1.1.66", "description": "querystring parser", "directories": {}, "devDependencies": {"mocha": "*", "expect.js": "*"}}, "0.5.4": {"name": "qs", "version": "0.5.4", "keywords": ["query string", "parser", "component"], "author": {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "qs@0.5.4", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "6f0f27c3e2df2fb10733c002695d0f7e9450df10", "tarball": "https://registry.npmjs.org/qs/-/qs-0.5.4.tgz", "integrity": "sha512-g4yjtX5DAYwP7fLOmRS/AYPDeMOjzWTzXzna6BOf6HOnP8SeYLoa7NJ3BoDBImWIOnI52voVdZJbfSG7sdt0kg==", "signatures": [{"sig": "MEUCIF+JCxCg4sLni29BnwdaA9LdUKLQWadRTUQhLkeYhng2AiEAyNV2C9ihLg3JrcM1ovagzbIDM1mA7nvQdDrsXvqk0mA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "engines": {"node": "*"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"querystring": "querystring.js"}}, "repository": {"url": "git://github.com/visionmedia/node-querystring.git", "type": "git"}, "_npmVersion": "1.2.14", "description": "querystring parser", "directories": {}, "devDependencies": {"mocha": "*", "expect.js": "*"}}, "0.5.5": {"name": "qs", "version": "0.5.5", "keywords": ["query string", "parser", "component"], "author": {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "qs@0.5.5", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "b07f0d7ffe3efc6fc2fcde6c66a20775641423f3", "tarball": "https://registry.npmjs.org/qs/-/qs-0.5.5.tgz", "integrity": "sha512-TOoCbt0ntLVan+hp6Fkw4//relXUpWMDbNuxiO2KfDOgmO/+leG6RadwEpwlMiBb8YLfbzDJvBngoK8rQeK7SA==", "signatures": [{"sig": "MEYCIQD4f/2hJZEDmzemsz2vl2POnwnXxX1OW6bxYgfB1hdOJwIhAI0LbEph5z1dj/hU3BBB0sI0hfQeCKn2xH/G1d5mBhNy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "engines": {"node": "*"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"querystring": "querystring.js"}}, "repository": {"url": "git://github.com/visionmedia/node-querystring.git", "type": "git"}, "_npmVersion": "1.2.14", "description": "querystring parser", "directories": {}, "devDependencies": {"mocha": "*", "expect.js": "*"}}, "0.5.6": {"name": "qs", "version": "0.5.6", "keywords": ["query string", "parser", "component"], "author": {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "qs@0.5.6", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "31b1ad058567651c526921506b9a8793911a0384", "tarball": "https://registry.npmjs.org/qs/-/qs-0.5.6.tgz", "integrity": "sha512-KbOrQrP5Ye+0gmq+hwxoJwAFRwExACWqwxj1IDFFgqOw9Poxy3wwSbafd9ZqP6T6ykMfnxM573kt/a4i9ybatQ==", "signatures": [{"sig": "MEUCIAxOTbXBz8+fK38YIrwSD2S3QXkgpt7eVgc4Xs0H7yfXAiEAvXlSXalcPJoGnStRvYd1rqIqc+UGWG9UmJO0vjgCD+0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "engines": {"node": "*"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"querystring": "querystring.js"}}, "repository": {"url": "git://github.com/visionmedia/node-querystring.git", "type": "git"}, "_npmVersion": "1.2.14", "description": "querystring parser", "directories": {}, "devDependencies": {"mocha": "*", "expect.js": "*"}}, "0.6.0": {"name": "qs", "version": "0.6.0", "keywords": ["query string", "parser", "component"], "author": {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "qs@0.6.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "e3377a4fd406e45f5ba378fdd6b91c00a17fc0ce", "tarball": "https://registry.npmjs.org/qs/-/qs-0.6.0.tgz", "integrity": "sha512-gcquSb3Z/Gb9x5miiz6FFkHYDmicxbf1n3OzX+b6NWJ1gt+GABhC/eZFYgffJfRL/LqQ1vto1IbF9fBr8cI1Dg==", "signatures": [{"sig": "MEUCIAGiR3y7LJSAnkNVIDde43wQrzGrQgXiiTf2tUa2K5+XAiEAwKJ4ZU7VFAfp28AiJ9ZchG28ot+kvTOubGRNboSGHZQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"querystring": "querystring.js"}}, "repository": {"url": "git://github.com/visionmedia/node-querystring.git", "type": "git"}, "_npmVersion": "1.2.14", "description": "querystring parser", "directories": {}, "devDependencies": {"mocha": "*", "expect.js": "*"}}, "0.6.1": {"name": "qs", "version": "0.6.1", "keywords": ["query string", "parser", "component"], "author": {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "qs@0.6.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "37353130532742683ddf4aa1edc5661612a7cc7d", "tarball": "https://registry.npmjs.org/qs/-/qs-0.6.1.tgz", "integrity": "sha512-mLvzalseJzYGD6f2oWVmsgZLgMXYV89PJuCgnMPcNyxOza2Paogl8PZoiLqtydOdDtw73yUKuBW8uH996sAQ2Q==", "signatures": [{"sig": "MEUCIQCP26uuKul7wTjTrZ3rEhYVK4nxqJfw9rLtlbIK1dJUZQIgGv+jNUZzR6cwfJPSg1oYn0Q3Sd1ClMThIQprel/TUCc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"querystring": "querystring.js"}}, "repository": {"url": "git://github.com/visionmedia/node-querystring.git", "type": "git"}, "_npmVersion": "1.2.14", "description": "querystring parser", "directories": {}, "devDependencies": {"mocha": "*", "expect.js": "*"}}, "0.6.2": {"name": "qs", "version": "0.6.2", "keywords": ["query string", "parser", "component"], "author": {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "qs@0.6.2", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "905c808930742348ad765bcc7a7445c08e4655ef", "tarball": "https://registry.npmjs.org/qs/-/qs-0.6.2.tgz", "integrity": "sha512-OLHsXjse4xelM27cLoDRL/6XUY4vWkaVlLRxZgv2rbOGrfpX9ySy0VoC3TbJGpktN1Tl83uXVpBqkrfok4Ta3g==", "signatures": [{"sig": "MEYCIQDV9yGwZ1TG2hET5DeFHXOnceaPe7QBU2tL3yJ6Jd5EdAIhANnBGCnKVQFm7jH3B7vbTIXjUIexNaTE28qNjEj388o2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"querystring": "querystring.js"}}, "repository": {"url": "git://github.com/visionmedia/node-querystring.git", "type": "git"}, "_npmVersion": "1.2.14", "description": "querystring parser", "directories": {}, "devDependencies": {"mocha": "*", "expect.js": "*"}}, "0.6.3": {"name": "qs", "version": "0.6.3", "keywords": ["query string", "parser", "component"], "author": {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "qs@0.6.3", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "a2a5ae9baa5d720d0f8cd0ca6bfad31398e33e99", "tarball": "https://registry.npmjs.org/qs/-/qs-0.6.3.tgz", "integrity": "sha512-eI+Zk8WVdl+o5EO5vXcwNva+YfKFMmPLC2U6kzMLlDqC3+SJAIGaDnnbBf2a7RTUqGKjCBVpLQ7HZvfUu8YsVw==", "signatures": [{"sig": "MEQCIAV42hCi6Cq2bg24nupi5sUzeWAB+X+uD79dgbJeA7MuAiADzJ4GI+5ctdAuUYOPwasUw3hEwcKjjG0xfFnH428g9A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/visionmedia/node-querystring.git", "type": "git"}, "_npmVersion": "1.2.14", "description": "querystring parser", "directories": {}, "devDependencies": {"mocha": "*", "expect.js": "*"}}, "0.6.4": {"name": "qs", "version": "0.6.4", "keywords": ["query string", "parser", "component"], "author": {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "qs@0.6.4", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "8d17ddcfc864ace3f03477382126740ef25b234f", "tarball": "https://registry.npmjs.org/qs/-/qs-0.6.4.tgz", "integrity": "sha512-5kCEcYO5zYtA8CqVTyAmpXeTjjp2eNe/Qk6XZOPNtKL7RH5FeYllIIHTq1anBEBl9dPW/cnBwsC4sKLxWfegzg==", "signatures": [{"sig": "MEUCIGvZUBToJEr4iHh0ceNt0y1oTwTqu3BdAGoCtTM3ym5CAiEAyktmrjA2k/XwnxoKGSnQaD4dlXK00AFfJjRHtjEGIS8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/visionmedia/node-querystring.git", "type": "git"}, "_npmVersion": "1.2.14", "description": "querystring parser", "directories": {}, "devDependencies": {"mocha": "*", "expect.js": "*"}}, "0.6.5": {"name": "qs", "version": "0.6.5", "keywords": ["query string", "parser", "component"], "author": {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "qs@0.6.5", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "294b268e4b0d4250f6dde19b3b8b34935dff14ef", "tarball": "https://registry.npmjs.org/qs/-/qs-0.6.5.tgz", "integrity": "sha512-n7wA/f30O3SsOw2BVkGUDzjWMw7kXvQJWKtDdgfq5HJvDoad+Jbc6osN1AQ0Iain5plo9e7Cs5fE+xR+DVkPTw==", "signatures": [{"sig": "MEQCIHm5QDbePI5ElSaXjzjHRqLTjB1BeZeI1QhgaFR1lLgWAiA3O4I1OpMD5l1YmZApuNmFPn9VpNo7Q/vSFcrBqMRoiw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/visionmedia/node-querystring.git", "type": "git"}, "_npmVersion": "1.2.14", "description": "querystring parser", "directories": {}, "devDependencies": {"mocha": "*", "expect.js": "*"}}, "0.6.6": {"name": "qs", "version": "0.6.6", "keywords": ["query string", "parser", "component"], "author": {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "qs@0.6.6", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/visionmedia/node-querystring/issues"}, "dist": {"shasum": "6e015098ff51968b8a3c819001d5f2c89bc4b107", "tarball": "https://registry.npmjs.org/qs/-/qs-0.6.6.tgz", "integrity": "sha512-kN+yNdAf29Jgp+AYHUmC7X4QdJPR8czuMWLNLc0aRxkQ7tB3vJQEONKKT9ou/rW7EbqVec11srC9q9BiVbcnHA==", "signatures": [{"sig": "MEUCIA+LvknMTOBvIA1FBSk8AM0jP+QwX4SsKOSzJQxE1U6CAiEAp7j2kXY6dIjdCDC3JhOfY9R8elYiO4bFdKi14Tr5mgs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/visionmedia/node-querystring.git", "type": "git"}, "_npmVersion": "1.2.30", "description": "querystring parser", "directories": {}, "devDependencies": {"mocha": "*", "expect.js": "*"}}, "1.0.0": {"name": "qs", "version": "1.0.0", "keywords": ["querystring", "qs"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "qs@1.0.0", "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "hueniverse", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/qs", "bugs": {"url": "https://github.com/hapijs/qs/issues"}, "dist": {"shasum": "544f2e30a836ea6b5f28326efb6661244d73de1e", "tarball": "https://registry.npmjs.org/qs/-/qs-1.0.0.tgz", "integrity": "sha512-5C/V4Lf6D/i5s08CB9Wb7Nnfvx3J+eeBtyqlVVAuxAhhnSAn2Kmwaoi3niE4d/qjTVufuhSPROP1ihCFFDzwiA==", "signatures": [{"sig": "MEUCIHbPJlSrhLZ3JLe/kvjvs2NIg2CrXG1blmz6MqmQVWzYAiEAwn+LpPJ1y9hGhPbsMqzE6o4TTgf+vN4t4Mt9mgtqCyU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "544f2e30a836ea6b5f28326efb6661244d73de1e", "gitHead": "a66f9c78309b5e441d4840bf68d1a008486ae0db", "scripts": {"test": "make test-cov"}, "_npmUser": {"name": "nlf", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/hapijs/qs/raw/master/LICENSE", "type": "BSD"}], "repository": {"url": "https://github.com/hapijs/qs.git", "type": "git"}, "_npmVersion": "1.4.21", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "dependencies": {}, "devDependencies": {"lab": "3.x.x"}}, "1.0.1": {"name": "qs", "version": "1.0.1", "keywords": ["querystring", "qs"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "qs@1.0.1", "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "hueniverse", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/qs", "bugs": {"url": "https://github.com/hapijs/qs/issues"}, "dist": {"shasum": "cd2edc5257070bcccac68715e855c4524be71ef1", "tarball": "https://registry.npmjs.org/qs/-/qs-1.0.1.tgz", "integrity": "sha512-fE6K368s+X3P/9wCC0clsferKzpZR6Pihi22LycLIZ06/gpR6cdLOT/lOq8leiTKd+wbOBevSJvq03Aqi8fZ/Q==", "signatures": [{"sig": "MEUCIEzgp8eRnpWKDQd4znDwq/Zlr7gIpBWXqF2ZQ1IHpfheAiEA2fyJwC6M7mh/PxzxiglIB+UbhQ7s7J/+bigIkL77R4U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "cd2edc5257070bcccac68715e855c4524be71ef1", "gitHead": "dc3cfcc7ee4af9fd8f464b19c5587f8d1ef9d252", "scripts": {"test": "make test-cov"}, "_npmUser": {"name": "nlf", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/hapijs/qs/raw/master/LICENSE", "type": "BSD"}], "repository": {"url": "https://github.com/hapijs/qs.git", "type": "git"}, "_npmVersion": "1.4.21", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "dependencies": {}, "devDependencies": {"lab": "3.x.x"}}, "1.0.2": {"name": "qs", "version": "1.0.2", "keywords": ["querystring", "qs"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "qs@1.0.2", "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "hueniverse", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/qs", "bugs": {"url": "https://github.com/hapijs/qs/issues"}, "dist": {"shasum": "50a93e2b5af6691c31bcea5dae78ee6ea1903768", "tarball": "https://registry.npmjs.org/qs/-/qs-1.0.2.tgz", "integrity": "sha512-tHuOP9TN/1VmDM/ylApGK1QF3PSIP8I6bHDEfoKNQeViREQ/sfu1bAUrA1hoDun8p8Tpm7jcsz47g+3PiGoYdg==", "signatures": [{"sig": "MEQCIG7DTpdLHTbPNIAsuKf4eclYPobrp4WznXD1OSTBltsUAiBGVuG2nQ9J2XoXey6oGTIRDc1tQUir+gd1kQTwBAUn5g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "50a93e2b5af6691c31bcea5dae78ee6ea1903768", "gitHead": "b7af068a898c8456a99613e474c8765dea7f41dc", "scripts": {"test": "make test-cov"}, "_npmUser": {"name": "nlf", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/hapijs/qs/raw/master/LICENSE", "type": "BSD"}], "repository": {"url": "https://github.com/hapijs/qs.git", "type": "git"}, "_npmVersion": "1.4.21", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "dependencies": {}, "devDependencies": {"lab": "3.x.x"}}, "1.1.0": {"name": "qs", "version": "1.1.0", "keywords": ["querystring", "qs"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "qs@1.1.0", "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "hueniverse", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/qs", "bugs": {"url": "https://github.com/hapijs/qs/issues"}, "dist": {"shasum": "2845cd9df462b2db28a90370e142d492c5a45dde", "tarball": "https://registry.npmjs.org/qs/-/qs-1.1.0.tgz", "integrity": "sha512-75mvGtXJWXvALOd+7pVzHS5yIlGsG4Ab/z+ZMDE5ZkNNMjgg1xMQ4nTN7FV/QoSXKjGOtHJvFisbXo0V4vMY7g==", "signatures": [{"sig": "MEQCIFG9QDQhBgfUndDGi2it6dC3GmBbyNjJm1Lm+MvyqKRaAiBRNVfovfjUgJ2PcwAEbbIClpP0hnp39yRCuqMGBowfoQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "2845cd9df462b2db28a90370e142d492c5a45dde", "gitHead": "eda63da46d0a730f4eceb249104e0227a7e03f72", "scripts": {"test": "make test-cov"}, "_npmUser": {"name": "nlf", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/hapijs/qs/raw/master/LICENSE", "type": "BSD"}], "repository": {"url": "https://github.com/hapijs/qs.git", "type": "git"}, "_npmVersion": "1.4.21", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "dependencies": {}, "devDependencies": {"lab": "3.x.x"}}, "1.2.0": {"name": "qs", "version": "1.2.0", "keywords": ["querystring", "qs"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "qs@1.2.0", "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "hueniverse", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/qs", "bugs": {"url": "https://github.com/hapijs/qs/issues"}, "dist": {"shasum": "ed079be28682147e6fd9a34cc2b0c1e0ec6453ee", "tarball": "https://registry.npmjs.org/qs/-/qs-1.2.0.tgz", "integrity": "sha512-XUf0O7rlGjbH+n7uqyT+xn362fmoPe4ehtHL6VK1nbSgQ7CqG0ZZLr1nU2EyXlRq++YphPdQ/5scjIWNMSPnhg==", "signatures": [{"sig": "MEUCIHxJvpvz5nuZ2J65ime1Wqketw2wSMVRG94Zw0e0EWKgAiEAtijjp4Y+5eebPQnBdiT42dM+8e5QRoj6V0TNIwYfnpA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "ed079be28682147e6fd9a34cc2b0c1e0ec6453ee", "gitHead": "e1ff638989afe27a2c7f8ec7eecf9bab71a4fbf2", "scripts": {"test": "make test-cov"}, "_npmUser": {"name": "nlf", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/hapijs/qs/raw/master/LICENSE", "type": "BSD"}], "repository": {"url": "https://github.com/hapijs/qs.git", "type": "git"}, "_npmVersion": "1.4.21", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "dependencies": {}, "devDependencies": {"lab": "3.x.x"}}, "1.2.1": {"name": "qs", "version": "1.2.1", "keywords": ["querystring", "qs"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "qs@1.2.1", "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "hueniverse", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/qs", "bugs": {"url": "https://github.com/hapijs/qs/issues"}, "dist": {"shasum": "09d0872abc0d187910af0c793f31d0ce335c57f8", "tarball": "https://registry.npmjs.org/qs/-/qs-1.2.1.tgz", "integrity": "sha512-hh9GIFlCLr6AMX7/va37MJbaq9YJzGmc8eC5YW4k5HdE6SiIPBBTH+KI3E4paSOyVSrbYRE3A+gdKoa33c86Eg==", "signatures": [{"sig": "MEYCIQD1b8xTUeZRlKo0s80pe9RQeUCXmeQy1r8+4zpb2VbewgIhANFTHqb5hnySyufRRhwYqBvo98BXPvWJb/DuaxVaXmNi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "09d0872abc0d187910af0c793f31d0ce335c57f8", "gitHead": "96871b8ebe001a993454f8cbf79c2410dabd7eb9", "scripts": {"test": "make test-cov"}, "_npmUser": {"name": "nlf", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/hapijs/qs/raw/master/LICENSE", "type": "BSD"}], "repository": {"url": "https://github.com/hapijs/qs.git", "type": "git"}, "_npmVersion": "1.4.21", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "dependencies": {}, "devDependencies": {"lab": "3.x.x"}}, "1.2.2": {"name": "qs", "version": "1.2.2", "keywords": ["querystring", "qs"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "qs@1.2.2", "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "hueniverse", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/qs", "bugs": {"url": "https://github.com/hapijs/qs/issues"}, "dist": {"shasum": "19b57ff24dc2a99ce1f8bdf6afcda59f8ef61f88", "tarball": "https://registry.npmjs.org/qs/-/qs-1.2.2.tgz", "integrity": "sha512-xEqT+49YIt+BdwQthXKTOkp7atENe6JqrGGerxBPiER6BArOIiVJtpZZYpWOpq2IOkTPVnDM8CgYvppFoJNwyQ==", "signatures": [{"sig": "MEUCIQD8oyWwopctwWmZRqrsaHpiZw4R2fQw75UJVOEhVxsEQwIgclHMgdHhHQJK1lCGkT4UOcUXOs2FXGzBCzUEQnmddmE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "19b57ff24dc2a99ce1f8bdf6afcda59f8ef61f88", "gitHead": "bd9455fea88d1c51a80dbf57ef0f99b4e553177d", "scripts": {"test": "make test-cov"}, "_npmUser": {"name": "hueniverse", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/hapijs/qs/raw/master/LICENSE", "type": "BSD"}], "repository": {"url": "https://github.com/hapijs/qs.git", "type": "git"}, "_npmVersion": "1.4.21", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "dependencies": {}, "devDependencies": {"lab": "3.x.x"}}, "2.0.0": {"name": "qs", "version": "2.0.0", "keywords": ["querystring", "qs"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "qs@2.0.0", "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "hueniverse", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/qs", "bugs": {"url": "https://github.com/hapijs/qs/issues"}, "dist": {"shasum": "423344d2a1324a8eb721e27586d79518ff0787cf", "tarball": "https://registry.npmjs.org/qs/-/qs-2.0.0.tgz", "integrity": "sha512-eavUxwaEMWeJJ+/mMlS72ovPGFXeSjnJfKvCrC2cI7zkxNjm5N4514xqSvu5zRA0FoXck8sBj3uVc0iSgsShSg==", "signatures": [{"sig": "MEYCIQCUhECth3t/+TV9pmVrwbd0uOWTKIa3TFe6BFNDvSp9PwIhAP82oQ+LYvf9qqdetA2wXtE3W6357eUJXNVEC8Zn1f3l", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "423344d2a1324a8eb721e27586d79518ff0787cf", "gitHead": "124ea733898f68586b2eeae496d035834fa89c7c", "scripts": {"test": "make test-cov"}, "_npmUser": {"name": "nlf", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/hapijs/qs/raw/master/LICENSE", "type": "BSD"}], "repository": {"url": "https://github.com/hapijs/qs.git", "type": "git"}, "_npmVersion": "1.4.21", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "dependencies": {}, "devDependencies": {"lab": "3.x.x"}}, "2.1.0": {"name": "qs", "version": "2.1.0", "keywords": ["querystring", "qs"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "qs@2.1.0", "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "hueniverse", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/qs", "bugs": {"url": "https://github.com/hapijs/qs/issues"}, "dist": {"shasum": "959d7b2c89ecc3e2f8a4b2766b4fb00b25e36de5", "tarball": "https://registry.npmjs.org/qs/-/qs-2.1.0.tgz", "integrity": "sha512-MX5iyqdxJPE5T39xWSlkuOPOe2a/2nMftN3GN9mThjdT5fEWAGbnJt2F2DOCYbiLiIU9QTNB4aZ97okM8pPBEw==", "signatures": [{"sig": "MEUCIQDtnH24n7AtMEklNNReEx/sw5N1EJ0GUDI/yqH21t7XxAIgaNxxEqHOfhdP/soVY4+5lxKPXIVu8hkeYJ80D6fxNPM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "959d7b2c89ecc3e2f8a4b2766b4fb00b25e36de5", "gitHead": "07bb33ec67fe2fc2e34161a3e0d1d94e3d5bb810", "scripts": {"test": "make test-cov"}, "_npmUser": {"name": "nlf", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/hapijs/qs/raw/master/LICENSE", "type": "BSD"}], "repository": {"url": "https://github.com/hapijs/qs.git", "type": "git"}, "_npmVersion": "1.4.21", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "dependencies": {}, "devDependencies": {"lab": "3.x.x"}}, "2.2.0": {"name": "qs", "version": "2.2.0", "keywords": ["querystring", "qs"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "qs@2.2.0", "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "hueniverse", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/qs", "bugs": {"url": "https://github.com/hapijs/qs/issues"}, "dist": {"shasum": "1c18d6eaba0e09a38984f2473a94dd03811f5634", "tarball": "https://registry.npmjs.org/qs/-/qs-2.2.0.tgz", "integrity": "sha512-yioypzzNQGrhLrra8u2GoL9WdeqMPzffQo/rmB3VUr1QG5swpFMw87Z8kPGCaOdTP1GVLx+Xz+wTQ4MyjC1QHw==", "signatures": [{"sig": "MEUCIQDz7sXvr166BVFMBuma6yGpre7wVq3294m+A9+oOAyWvQIgc5eBgZv5KcMf1HWuZ3rHRYiGWQM5J8mL0mQafx2mxs0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "1c18d6eaba0e09a38984f2473a94dd03811f5634", "gitHead": "9bc7567539828d67b02a0c7d8a3204ed3d86da0e", "scripts": {"test": "make test-cov"}, "_npmUser": {"name": "nlf", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/hapijs/qs/raw/master/LICENSE", "type": "BSD"}], "repository": {"url": "https://github.com/hapijs/qs.git", "type": "git"}, "_npmVersion": "1.4.23", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "dependencies": {}, "devDependencies": {"lab": "3.x.x"}}, "2.2.1": {"name": "qs", "version": "2.2.1", "keywords": ["querystring", "qs"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "qs@2.2.1", "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "hueniverse", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/qs", "bugs": {"url": "https://github.com/hapijs/qs/issues"}, "dist": {"shasum": "2ccc1a02b69108091f1d82de88a316ad94df9cce", "tarball": "https://registry.npmjs.org/qs/-/qs-2.2.1.tgz", "integrity": "sha512-nhYpiBZ0iFAMwA0tB//tOO0jr5wZCicidFOjYWidGG8WsXiLQbC8xbAABIAJdFlE3XLYmeqrzCQM0PDjwIjJgg==", "signatures": [{"sig": "MEYCIQCcZAsjlVKGgNUkfK2bGqzRBJ8sL2E8k0lFh30AWWtKNQIhANVP7/kVosbZdOdKid3p5YPggviEu2PA0DpNI304BoIe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "2ccc1a02b69108091f1d82de88a316ad94df9cce", "gitHead": "32edf331cf876d907eb60b146c9251f8d46026b4", "scripts": {"test": "make test-cov"}, "_npmUser": {"name": "nlf", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/hapijs/qs/raw/master/LICENSE", "type": "BSD"}], "repository": {"url": "https://github.com/hapijs/qs.git", "type": "git"}, "_npmVersion": "1.4.23", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "dependencies": {}, "devDependencies": {"lab": "3.x.x"}}, "2.2.2": {"name": "qs", "version": "2.2.2", "keywords": ["querystring", "qs"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "qs@2.2.2", "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "hueniverse", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/qs", "bugs": {"url": "https://github.com/hapijs/qs/issues"}, "dist": {"shasum": "dfe783f1854b1ac2b3ade92775ad03e27e03218c", "tarball": "https://registry.npmjs.org/qs/-/qs-2.2.2.tgz", "integrity": "sha512-i0PpYJWWhn7DevbolgusRac1ijaEG/jrigNv+WHbTKYWtTqAc/D+6qwAdZ24kZ4ZH3V5hKiqy15ldAplk4V0TQ==", "signatures": [{"sig": "MEYCIQCZH082pnr9wLjkY92q8wv+foT6aFgzGgqnugwIXQkrqgIhANq753WduxX1lFo94K8IC40SMwJN6mie4Rti4NCm3LgU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "dfe783f1854b1ac2b3ade92775ad03e27e03218c", "gitHead": "062bedcc77df310ecb7be43e69fcd0df0c87b46c", "scripts": {"test": "make test-cov"}, "_npmUser": {"name": "nlf", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/hapijs/qs/raw/master/LICENSE", "type": "BSD"}], "repository": {"url": "https://github.com/hapijs/qs.git", "type": "git"}, "_npmVersion": "1.4.23", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "dependencies": {}, "devDependencies": {"lab": "3.x.x"}}, "2.2.3": {"name": "qs", "version": "2.2.3", "keywords": ["querystring", "qs"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "qs@2.2.3", "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "hueniverse", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/qs", "bugs": {"url": "https://github.com/hapijs/qs/issues"}, "dist": {"shasum": "6139c1f47960eff5655e56aab0ef9f6dd16d4eeb", "tarball": "https://registry.npmjs.org/qs/-/qs-2.2.3.tgz", "integrity": "sha512-fn1t1odvsfyCctjhKcMagUJ1LtfvS41///9GOaLBXiUHGsCWWEVFLVCGz9O0UnSn/JcXzCkynaQ22EEwoaDePQ==", "signatures": [{"sig": "MEUCIQD7FWYiu4PrqPFMpSwVx+XyGKrKDY3rsu9wWgY6HTlwnwIgXCbsDZLVqhoxGuvh263y303bIooEQ7Tp2zobn1KnTDU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "6139c1f47960eff5655e56aab0ef9f6dd16d4eeb", "gitHead": "904528124a6eb879ebc5197376e9613069414f67", "scripts": {"test": "make test-cov"}, "_npmUser": {"name": "nlf", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/hapijs/qs/raw/master/LICENSE", "type": "BSD"}], "repository": {"url": "https://github.com/hapijs/qs.git", "type": "git"}, "_npmVersion": "1.4.23", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "dependencies": {}, "devDependencies": {"lab": "4.x.x"}}, "2.2.4": {"name": "qs", "version": "2.2.4", "keywords": ["querystring", "qs"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "qs@2.2.4", "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "hueniverse", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/qs", "bugs": {"url": "https://github.com/hapijs/qs/issues"}, "dist": {"shasum": "2e9fbcd34b540e3421c924ecd01e90aa975319c8", "tarball": "https://registry.npmjs.org/qs/-/qs-2.2.4.tgz", "integrity": "sha512-ptau9CngYR/IimcThDkAs7LzlZhxo92RiMHtLbOq3R6u9iDkixdSysaAVaZpYByrXWWantEJ4fVPl0xR2McSCQ==", "signatures": [{"sig": "MEUCIFT4JFGMJFCEOK+3taTsOI32YqIJMXM44Ap9o2Cd3pbaAiEA1AW8r549hKGO2MUiKuRl6rdlomZQGxXOBCatqY6Sxnk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "2e9fbcd34b540e3421c924ecd01e90aa975319c8", "gitHead": "9775242fa57cbfa4db62e4b0aa4f82b23e2ce6af", "scripts": {"test": "make test-cov"}, "_npmUser": {"name": "nlf", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/hapijs/qs/raw/master/LICENSE", "type": "BSD"}], "repository": {"url": "https://github.com/hapijs/qs.git", "type": "git"}, "_npmVersion": "1.4.23", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "dependencies": {}, "devDependencies": {"lab": "4.x.x"}}, "2.2.5": {"name": "qs", "version": "2.2.5", "keywords": ["querystring", "qs"], "_id": "qs@2.2.5", "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "hueniverse", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/qs", "bugs": {"url": "https://github.com/hapijs/qs/issues"}, "dist": {"shasum": "1088abaf9dcc0ae5ae45b709e6c6b5888b23923c", "tarball": "https://registry.npmjs.org/qs/-/qs-2.2.5.tgz", "integrity": "sha512-z/IJSH2F7g7FbYokpSQqLcMAk0VXtR8boQS1EqLxeB7T5xcFK/RuGY0PZbPtF9r151X+a/ZaRauCOok4Xiyjmg==", "signatures": [{"sig": "MEQCICXtSvxDJ/Ymr28xBCCQnwUkXWDwchzjm93Zz3gFulZ1AiAQzIxiQ4FA6rgzoKXn07S3ZXk4t1AC+QSF/xewinavjA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "1088abaf9dcc0ae5ae45b709e6c6b5888b23923c", "gitHead": "211cbd9cd9ba6d16a5459c13073bc3a6b1dec018", "scripts": {"test": "make test-cov"}, "_npmUser": {"name": "nlf", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/hapijs/qs/raw/master/LICENSE", "type": "BSD"}], "repository": {"url": "https://github.com/hapijs/qs.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "dependencies": {}, "devDependencies": {"lab": "4.x.x"}}, "2.3.0": {"name": "qs", "version": "2.3.0", "keywords": ["querystring", "qs"], "_id": "qs@2.3.0", "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "hueniverse", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/qs", "bugs": {"url": "https://github.com/hapijs/qs/issues"}, "dist": {"shasum": "ea0c8161775cbb4f3673ad36571b5d5a3f3e72fb", "tarball": "https://registry.npmjs.org/qs/-/qs-2.3.0.tgz", "integrity": "sha512-UpJkm08AWL8e0SjpToEvCO1XHoEEh6USzRfxbcxf+nZvcmuwlJAFbImmAz2p3/Tyoi004KXg1fHBLwnyC3iovA==", "signatures": [{"sig": "MEQCICUSl+7oFugk7ygfy9CftOF09frt9UHM0Dc3OANgWt0XAiA4bJAw/SEMVULWdvISnXRV2oMnwD3hni1wuV5oZhJ+vg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "ea0c8161775cbb4f3673ad36571b5d5a3f3e72fb", "gitHead": "8ace6a435ed0639118ba127c711f2ae8f185314a", "scripts": {"test": "make test-cov"}, "_npmUser": {"name": "nlf", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/hapijs/qs/raw/master/LICENSE", "type": "BSD"}], "repository": {"url": "https://github.com/hapijs/qs.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "dependencies": {}, "devDependencies": {"lab": "4.x.x"}}, "2.3.1": {"name": "qs", "version": "2.3.1", "keywords": ["querystring", "qs"], "_id": "qs@2.3.1", "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "hueniverse", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/qs", "bugs": {"url": "https://github.com/hapijs/qs/issues"}, "dist": {"shasum": "24e448bc4ee726201995a3c0c768c3bf19494c1c", "tarball": "https://registry.npmjs.org/qs/-/qs-2.3.1.tgz", "integrity": "sha512-hAP601NswVYevOLlkK48Nyx6K5pQHRYXjsLNnXRMkZOqD2THi+nc8EKmdVht/edjaCBtzDqQlYTWs/nXayaySg==", "signatures": [{"sig": "MEUCIQC9eNngqSDtR+EoEgaw+7MOTT3kCFao7jmTNFiuoXzoRQIgRe+J5Vd3r739Kx6NBx+tS9XfIB0NUy0/0eNtnu/URDA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "24e448bc4ee726201995a3c0c768c3bf19494c1c", "gitHead": "a601ae18bc419399db7debc2765c96c84e83c237", "scripts": {"test": "make test-cov"}, "_npmUser": {"name": "nlf", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/hapijs/qs/raw/master/LICENSE", "type": "BSD"}], "repository": {"url": "https://github.com/hapijs/qs.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "dependencies": {}, "devDependencies": {"lab": "4.x.x"}}, "2.3.2": {"name": "qs", "version": "2.3.2", "keywords": ["querystring", "qs"], "_id": "qs@2.3.2", "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "hueniverse", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/qs", "bugs": {"url": "https://github.com/hapijs/qs/issues"}, "dist": {"shasum": "d45ec249e4b9b029af008829a101d5ff7e972790", "tarball": "https://registry.npmjs.org/qs/-/qs-2.3.2.tgz", "integrity": "sha512-EsFNiUi2ulgK7BCN0BCijcD1STkbs6YBnsUGr+Yr+3wG/bAZub7XL/BNf2RTyzwJhpA0F2ds6Vr2KVHhSx4hJA==", "signatures": [{"sig": "MEUCIQD/GVBIUNGIlzrp39EwtQQF6vrCCq9fFxG0T6DLgLimRwIgTsRi0LxTHHguPDIE/YfuijOuFpZUbLyPweQYrWaAYfU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "d45ec249e4b9b029af008829a101d5ff7e972790", "gitHead": "58097c12559b4c5857af99927273b3141dff8529", "scripts": {"test": "make test-cov"}, "_npmUser": {"name": "nlf", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/hapijs/qs/raw/master/LICENSE", "type": "BSD"}], "repository": {"url": "https://github.com/hapijs/qs.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "dependencies": {}, "devDependencies": {"lab": "4.x.x"}}, "2.3.3": {"name": "qs", "version": "2.3.3", "keywords": ["querystring", "qs"], "_id": "qs@2.3.3", "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "hueniverse", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/qs", "bugs": {"url": "https://github.com/hapijs/qs/issues"}, "dist": {"shasum": "e9e85adbe75da0bbe4c8e0476a086290f863b404", "tarball": "https://registry.npmjs.org/qs/-/qs-2.3.3.tgz", "integrity": "sha512-f5M0HQqZWkzU8GELTY8LyMrGkr3bPjKoFtTkwUEqJQbcljbeK8M7mliP9Ia2xoOI6oMerp+QPS7oYJtpGmWe/A==", "signatures": [{"sig": "MEUCIH7KftfzwbKnM4lW4uHSwWHGYE9mEdD1DvLRkXGq3QZrAiEA3DugnSB6NGn21stQJ0bGXEbBy8Zy1Tz1lJwOtgdB2HY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "e9e85adbe75da0bbe4c8e0476a086290f863b404", "gitHead": "9250c4cda5102fcf72441445816e6d311fc6813d", "scripts": {"test": "make test-cov"}, "_npmUser": {"name": "nlf", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/hapijs/qs/raw/master/LICENSE", "type": "BSD"}], "repository": {"url": "https://github.com/hapijs/qs.git", "type": "git"}, "_npmVersion": "2.1.6", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "_nodeVersion": "0.10.32", "dependencies": {}, "devDependencies": {"lab": "5.x.x", "code": "1.x.x"}}, "2.4.0": {"name": "qs", "version": "2.4.0", "keywords": ["querystring", "qs"], "_id": "qs@2.4.0", "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "hueniverse", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/qs", "bugs": {"url": "https://github.com/hapijs/qs/issues"}, "dist": {"shasum": "988dfa14e3ca66b54baa4b754e359f299486e212", "tarball": "https://registry.npmjs.org/qs/-/qs-2.4.0.tgz", "integrity": "sha512-zp+W0wCU3/x68eDnBk7vv+tLDNBykL0kwBWsYTMGhhKcdrksHml0FixBzmcgrepQUHtUwnyZLg5SMfAMcBEicQ==", "signatures": [{"sig": "MEUCIQCqWWAlpJFKxib9OKTir7Mel/rDUeG85osb0rjW/DnqTgIgeSWyBcF+75s/YZ/61cLNT+5r9xoSgEC5uI6iISdB1z4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "988dfa14e3ca66b54baa4b754e359f299486e212", "gitHead": "820be617369cadbb7b15701dacff3b4cc9a92b84", "scripts": {"test": "make test-cov"}, "_npmUser": {"name": "nlf", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/hapijs/qs/raw/master/LICENSE", "type": "BSD"}], "repository": {"url": "https://github.com/hapijs/qs.git", "type": "git"}, "_npmVersion": "2.6.1", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "_nodeVersion": "0.10.36", "dependencies": {}, "devDependencies": {"lab": "5.x.x", "code": "1.x.x"}}, "2.4.1": {"name": "qs", "version": "2.4.1", "keywords": ["querystring", "qs"], "_id": "qs@2.4.1", "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "hueniverse", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/qs", "bugs": {"url": "https://github.com/hapijs/qs/issues"}, "dist": {"shasum": "68cbaea971013426a80c1404fad6b1a6b1175245", "tarball": "https://registry.npmjs.org/qs/-/qs-2.4.1.tgz", "integrity": "sha512-Be4FpFGGi1EsFqPx6BEjDzTIWVtPlsIwDq0fq52YPFOcSlEX9Nk1ydgfIVjrTxZbeD6N/edkYEPYNwTDcwRn+g==", "signatures": [{"sig": "MEUCIQDGTQj+yCy00itH/c7Omm8NDOl0dVsTUZIt3dGo16KsTwIgE6WpIxrjsIRynIcsr5T74On8xEYLwrgUGQlPp3NMj7s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "68cbaea971013426a80c1404fad6b1a6b1175245", "gitHead": "58c6540418954867822c1af3e45fb4c26708b07e", "scripts": {"test": "make test-cov"}, "_npmUser": {"name": "nlf", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/hapijs/qs/raw/master/LICENSE", "type": "BSD"}], "repository": {"url": "https://github.com/hapijs/qs.git", "type": "git"}, "_npmVersion": "2.6.1", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "_nodeVersion": "0.10.36", "dependencies": {}, "devDependencies": {"lab": "5.x.x", "code": "1.x.x"}}, "2.4.2": {"name": "qs", "version": "2.4.2", "keywords": ["querystring", "qs"], "_id": "qs@2.4.2", "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "hueniverse", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/qs", "bugs": {"url": "https://github.com/hapijs/qs/issues"}, "dist": {"shasum": "f7ce788e5777df0b5010da7f7c4e73ba32470f5a", "tarball": "https://registry.npmjs.org/qs/-/qs-2.4.2.tgz", "integrity": "sha512-Ur2glV49dt6jknphzkWeLUNCy7pmwGxGaEJuuxVVBioSwQzT00cZPLEtRqr4cg/iO/6N+RbfB0lFD2EovyeEng==", "signatures": [{"sig": "MEQCIBjHve+f6uWJ6RCAJ11h59L5OhcgvQpRllHvMc5IzczwAiAhFYSU77n5phBfwSx0uRFAtZDLujQuP43Z8RxEYR/rXQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "f7ce788e5777df0b5010da7f7c4e73ba32470f5a", "gitHead": "cdd64a9d1385dbc3dde48da6de98b5993f1607bd", "scripts": {"test": "make test-cov"}, "_npmUser": {"name": "nlf", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/hapijs/qs/raw/master/LICENSE", "type": "BSD"}], "repository": {"url": "git+https://github.com/hapijs/qs.git", "type": "git"}, "_npmVersion": "2.9.0", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "_nodeVersion": "0.10.38", "dependencies": {}, "devDependencies": {"lab": "5.x.x", "code": "1.x.x"}}, "3.0.0": {"name": "qs", "version": "3.0.0", "keywords": ["querystring", "qs"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@3.0.0", "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "hueniverse", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/qs", "bugs": {"url": "https://github.com/hapijs/qs/issues"}, "dist": {"shasum": "bd3b4549424f0960bd6923724eeb29e4ffbc2af8", "tarball": "https://registry.npmjs.org/qs/-/qs-3.0.0.tgz", "integrity": "sha512-W+N2dfclAVHuV7XDXUcLv59WN/BFbSmH5s3O8zyFVZGni4CZRoRFGmTYp97B7orsP44HXd6YmCV8NFeUky2M3A==", "signatures": [{"sig": "MEUCIHmNz/y13NW4d+3MugpxH/6+uk6rzhKTjMdnHjPR8Mw5AiEAzDGJeQmRjimvyPeNlYtXKF7uHW9bukr6Xfq4EyJxbCY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "bd3b4549424f0960bd6923724eeb29e4ffbc2af8", "gitHead": "482674af2389db626f9ff67c7ae6a7b9ca7a4891", "scripts": {"dist": "browserify --standalone Qs index.js > dist/qs.js", "test": "make test-cov"}, "_npmUser": {"name": "nlf", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/hapijs/qs.git", "type": "git"}, "_npmVersion": "2.9.0", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "_nodeVersion": "0.12.2", "dependencies": {}, "devDependencies": {"lab": "5.x.x", "code": "1.x.x", "browserify": "^10.2.1"}}, "3.1.0": {"name": "qs", "version": "3.1.0", "keywords": ["querystring", "qs"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@3.1.0", "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "hueniverse", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/qs", "bugs": {"url": "https://github.com/hapijs/qs/issues"}, "dist": {"shasum": "d0e9ae745233a12dc43fb4f3055bba446261153c", "tarball": "https://registry.npmjs.org/qs/-/qs-3.1.0.tgz", "integrity": "sha512-nR5uYqNsm8CgBhfCpsYKz6iDhvKjf0xdFT4KanNlLP40COGwZEsjbLoDyL9VrTXyiICUGUbsiN3gBpLGZhSZWQ==", "signatures": [{"sig": "MEUCIQD9owcbWzQMH3fh/EA2/PnNlTes0BF94BY5hBW50Ip4CwIgP1fd1vQVymg+7cznx27oQz9iR0Cw38WAYbCRWs5tVtE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "d0e9ae745233a12dc43fb4f3055bba446261153c", "gitHead": "e53b1b242a55f886531954ebdd78b3b20efadaf0", "scripts": {"dist": "browserify --standalone Qs index.js > dist/qs.js", "test": "make test-cov"}, "_npmUser": {"name": "nlf", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/hapijs/qs.git", "type": "git"}, "_npmVersion": "2.9.0", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "_nodeVersion": "0.12.2", "dependencies": {}, "devDependencies": {"lab": "5.x.x", "code": "1.x.x", "browserify": "^10.2.1"}}, "4.0.0": {"name": "qs", "version": "4.0.0", "keywords": ["querystring", "qs"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@4.0.0", "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "hueniverse", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/qs", "bugs": {"url": "https://github.com/hapijs/qs/issues"}, "dist": {"shasum": "c31d9b74ec27df75e543a86c78728ed8d4623607", "tarball": "https://registry.npmjs.org/qs/-/qs-4.0.0.tgz", "integrity": "sha512-8MPmJ83uBOPsQj5tQCv4g04/nTiY+d17yl9o3Bw73vC6XlEm2POIRRlOgWJ8i74bkGLII670cDJJZkgiZ2sIkg==", "signatures": [{"sig": "MEQCID0KVnIumzM4oJcStd8Zxn8Ltx6Rx5XJdEiX54lozt72AiBPgY214d1h6ilOhBRtyBBMgXJJJiIbOUQO1Qoc/t98cA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "c31d9b74ec27df75e543a86c78728ed8d4623607", "gitHead": "e573dd08eae6cce30d2202704691a102dfa3782a", "scripts": {"dist": "browserify --standalone Qs lib/index.js > dist/qs.js", "test": "lab -a code -t 100 -L", "test-cov-html": "lab -a code -r html -o coverage.html"}, "_npmUser": {"name": "nlf", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/hapijs/qs.git", "type": "git"}, "_npmVersion": "2.12.0", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "_nodeVersion": "0.12.4", "dependencies": {}, "devDependencies": {"lab": "5.x.x", "code": "1.x.x", "browserify": "^10.2.1"}}, "5.0.0": {"name": "qs", "version": "5.0.0", "keywords": ["querystring", "qs"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@5.0.0", "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "hueniverse", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/qs", "bugs": {"url": "https://github.com/hapijs/qs/issues"}, "dist": {"shasum": "9e0dcfe32021927b3bca69ecbb9d7694fb9a97aa", "tarball": "https://registry.npmjs.org/qs/-/qs-5.0.0.tgz", "integrity": "sha512-ygvqueOBMDyLOAcyG8AkusOl0fuqVZUlqsb5yAMOYwN3QiYRb3LGnR1IzvBIFE/3ggtXxv41zwEF1su2eXAONg==", "signatures": [{"sig": "MEUCIQD4H41St+L/MGSNhaWIc25+eqPKGNGbijEPIYuo5L3gugIgMHXSINGeVptA/m+K/GYa5i9999vpTbpew47fU2o9bbU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "9e0dcfe32021927b3bca69ecbb9d7694fb9a97aa", "gitHead": "90ac66b2f90dc357134ea138be6cededa1578e5f", "scripts": {"dist": "browserify --standalone Qs lib/index.js > dist/qs.js", "test": "lab -a code -t 100 -L", "test-cov-html": "lab -a code -r html -o coverage.html"}, "_npmUser": {"name": "nlf", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/hapijs/qs.git", "type": "git"}, "_npmVersion": "2.14.0", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {}, "devDependencies": {"lab": "5.x.x", "code": "1.x.x", "browserify": "^10.2.1"}}, "5.1.0": {"name": "qs", "version": "5.1.0", "keywords": ["querystring", "qs"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@5.1.0", "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "hueniverse", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/qs", "bugs": {"url": "https://github.com/hapijs/qs/issues"}, "dist": {"shasum": "4d932e5c7ea411cca76a312d39a606200fd50cd9", "tarball": "https://registry.npmjs.org/qs/-/qs-5.1.0.tgz", "integrity": "sha512-SGDM48EwFLWnYYpNlOkEIRJb4wil5FKJxpR6NVfQjz6qJmX53ki7Xj1cLNEAkb70vUfJmdVLOwODyABgZyDMZw==", "signatures": [{"sig": "MEUCIDh7jqAnU56CT92tU/DczxQ595UcOB74b6CTxlHbQtCTAiEApUwtm/jt4A1VEJG+1jVngTv9COq5xOR8G4Fydo4oTKE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "4d932e5c7ea411cca76a312d39a606200fd50cd9", "engines": ">=0.10.40", "gitHead": "9e9759ec5be2dd99ce90961bbff47075cd5a8160", "scripts": {"dist": "browserify --standalone Qs lib/index.js > dist/qs.js", "test": "lab -a code -t 100 -L", "test-tap": "lab -a code -r tap -o tests.tap", "test-cov-html": "lab -a code -r html -o coverage.html"}, "_npmUser": {"name": "nlf", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/hapijs/qs.git", "type": "git"}, "_npmVersion": "2.14.1", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {}, "devDependencies": {"lab": "5.x.x", "code": "1.x.x", "browserify": "^10.2.1"}}, "5.2.0": {"name": "qs", "version": "5.2.0", "keywords": ["querystring", "qs"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@5.2.0", "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "hueniverse", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/qs", "bugs": {"url": "https://github.com/hapijs/qs/issues"}, "dist": {"shasum": "a9f31142af468cb72b25b30136ba2456834916be", "tarball": "https://registry.npmjs.org/qs/-/qs-5.2.0.tgz", "integrity": "sha512-VH4FeG98gs6AkHivaW2O14vsOPBL9E80Sj7fITunoDijiYQ1lsVwJYmm1CSL+oLyO2N5HPdo23GXAG64uKOAZQ==", "signatures": [{"sig": "MEYCIQDXcOL4JupVAsmxG/9xRtvuxvV+C69ZPIeEl5RwdQFWRAIhAPK33Ixzm4g+0QlhfKekMWCaYjq5NRbHGUjaHy/Auq5y", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "a9f31142af468cb72b25b30136ba2456834916be", "engines": ">=0.10.40", "gitHead": "a341cdf2fadba5ede1ce6c95c7051f6f31f37b81", "scripts": {"dist": "browserify --standalone Qs lib/index.js > dist/qs.js", "test": "lab -a code -t 100 -L", "test-tap": "lab -a code -r tap -o tests.tap", "test-cov-html": "lab -a code -r html -o coverage.html"}, "_npmUser": {"name": "nlf", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/hapijs/qs.git", "type": "git"}, "_npmVersion": "3.3.5", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "_nodeVersion": "0.10.40", "dependencies": {}, "devDependencies": {"lab": "5.x.x", "code": "1.x.x", "browserify": "^10.2.1"}}, "6.0.0": {"name": "qs", "version": "6.0.0", "keywords": ["querystring", "qs"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@6.0.0", "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "hueniverse", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/qs", "bugs": {"url": "https://github.com/hapijs/qs/issues"}, "dist": {"shasum": "de99c0c58c54eae49fff949510672338691110e8", "tarball": "https://registry.npmjs.org/qs/-/qs-6.0.0.tgz", "integrity": "sha512-NSkqkwuet8MUAYRb3x5mSbV9uu4gQY85/aksTPi1MHzw6HVBOqUaZEIEhAtRvxgjWsOx+8HvkHOj1NSrZIyi+Q==", "signatures": [{"sig": "MEUCIGfLWelL1FZJrO+4Zl7Mg+ROINjmU+TOspjgheHfkhENAiEAhCv94Av3TVJksQFYFnX56gF1oIwoRV0GF4KL8xxtXyI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "de99c0c58c54eae49fff949510672338691110e8", "engines": ">=4.0.0", "gitHead": "ed169696b6cebacf8e7f7c6b4ba8eeb5f2cb2694", "scripts": {"dist": "browserify --standalone Qs lib/index.js > dist/qs.js", "test": "lab -a code -t 100 -L", "test-tap": "lab -a code -r tap -o tests.tap", "test-cov-html": "lab -a code -r html -o coverage.html"}, "_npmUser": {"name": "hueniverse", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/hapijs/qs.git", "type": "git"}, "_npmVersion": "3.3.9", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "_nodeVersion": "4.2.1", "dependencies": {}, "devDependencies": {"lab": "7.x.x", "code": "2.x.x", "browserify": "^10.2.1"}}, "6.0.1": {"name": "qs", "version": "6.0.1", "keywords": ["querystring", "qs"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@6.0.1", "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "hueniverse", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/qs", "bugs": {"url": "https://github.com/hapijs/qs/issues"}, "dist": {"shasum": "ee8b7fcd64fcbe6e36c922bd2c464ee7c54766c3", "tarball": "https://registry.npmjs.org/qs/-/qs-6.0.1.tgz", "integrity": "sha512-LcJmRROyMPgjuOvVAOXZIv6yJhQoMtX6yUe8SlmKKw9H/XzZbknXTvPPpJQc0IOb2TbwWoHS/1u4kdFRf022sg==", "signatures": [{"sig": "MEUCIF2alelsOlsAjFdIoDL5IH+ImimGmfLMyZoGU9IC5O94AiEAq89FR66Kj+CXrM8qabuhousYcqi/TLS5NBS7yicnYqI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "ee8b7fcd64fcbe6e36c922bd2c464ee7c54766c3", "engines": {"node": ">=4.0.0"}, "gitHead": "bbb6ae1bbf8adb3057a52363715a20a25bd8a68f", "scripts": {"dist": "browserify --standalone Qs lib/index.js > dist/qs.js", "test": "lab -a code -t 100 -L", "test-tap": "lab -a code -r tap -o tests.tap", "test-cov-html": "lab -a code -r html -o coverage.html"}, "_npmUser": {"name": "nlf", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/hapijs/qs.git", "type": "git"}, "_npmVersion": "3.3.10", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "_nodeVersion": "4.2.2", "dependencies": {}, "devDependencies": {"lab": "7.x.x", "code": "2.x.x", "browserify": "^10.2.1"}}, "6.0.2": {"name": "qs", "version": "6.0.2", "keywords": ["querystring", "qs"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@6.0.2", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/qs", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "dist": {"shasum": "88c68d590e8ed56c76c79f352c17b982466abfcd", "tarball": "https://registry.npmjs.org/qs/-/qs-6.0.2.tgz", "integrity": "sha512-YiYenYVGHv0gA3XHl/pKjfdQsX1wWnCnGf6YBGx1Rtlqwac5XlAIYHd9d8Mf0awAf20bH93igRF0Mae8z641ew==", "signatures": [{"sig": "MEYCIQCCg7udr4TaO2AmQv8Z2vdqarH9TXM036IJ1BdfDTdFkAIhAJiO2+PGloxa5Teo2PtE6CsQlzFbtc4EbJfV6lR+TFPJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "88c68d590e8ed56c76c79f352c17b982466abfcd", "engines": {"node": ">=0.6"}, "gitHead": "47dfbd6740b3cc1593847825701c8aa136f636e3", "scripts": {"dist": "mkdirp dist && browserify --standalone Qs lib/index.js > dist/qs.js", "lint": "eslint lib/*.js text/*.js", "test": "parallelshell 'npm run readme' 'npm run lint' 'npm run coverage'", "readme": "evalmd README.md", "coverage": "covert test", "prepublish": "npm run dist", "tests-only": "node test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/qs.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "_nodeVersion": "5.4.1", "dependencies": {}, "devDependencies": {"tape": "^4.3.0", "covert": "^1.1.0", "eslint": "^1.10.3", "evalmd": "^0.0.16", "mkdirp": "^0.5.1", "browserify": "^12.0.1", "parallelshell": "^2.0.0", "@ljharb/eslint-config": "^1.6.1"}}, "6.1.0": {"name": "qs", "version": "6.1.0", "keywords": ["querystring", "qs"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@6.1.0", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/qs", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "dist": {"shasum": "ec1d1626b24278d99f0fdf4549e524e24eceeb26", "tarball": "https://registry.npmjs.org/qs/-/qs-6.1.0.tgz", "integrity": "sha512-tba7YmUjQg/DN5jDcqe/5AzaoM8KC+/be5/PBU1lxATKSqRAtYB0gK2m4opGb4UHNemmdKAkfy2cjS+TcyOChg==", "signatures": [{"sig": "MEUCIGq3YzpaSN4mmxdHulC+mBNSxhPz+V0ugjo6wtgSVgOYAiEA4xUmP8SW+QcquSWpwQL0ehtt+BffgV1ZRmQ3E521SJs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "ec1d1626b24278d99f0fdf4549e524e24eceeb26", "engines": {"node": ">=0.6"}, "gitHead": "5bd79545edb33d6a43398fec7df9ecef2da005ea", "scripts": {"dist": "mkdirp dist && browserify --standalone Qs lib/index.js > dist/qs.js", "lint": "eslint lib/*.js text/*.js", "test": "parallelshell 'npm run readme' 'npm run lint' 'npm run coverage'", "readme": "evalmd README.md", "coverage": "covert test", "prepublish": "npm run dist", "tests-only": "node test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/qs.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {}, "devDependencies": {"tape": "^4.3.0", "covert": "^1.1.0", "eslint": "^1.10.3", "evalmd": "^0.0.16", "mkdirp": "^0.5.1", "browserify": "^12.0.1", "parallelshell": "^2.0.0", "@ljharb/eslint-config": "^1.6.1"}, "_npmOperationalInternal": {"tmp": "tmp/qs-6.1.0.tgz_1454565583082_0.44599376199766994", "host": "packages-5-east.internal.npmjs.com"}}, "6.2.0": {"name": "qs", "version": "6.2.0", "keywords": ["querystring", "qs"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@6.2.0", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/qs", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "dist": {"shasum": "3b7848c03c2dece69a9522b0fae8c4126d745f3b", "tarball": "https://registry.npmjs.org/qs/-/qs-6.2.0.tgz", "integrity": "sha512-xJlDcRyZKEdvI99YhwNqGKmeiOCcfxk5E09Gof/E8xSpiMcqJ+BCwPZ3ykEYQdwDlmTpK6YlPB/kd8zfy9e7wg==", "signatures": [{"sig": "MEUCIQCfF46vU8FGoTVqZ/zvYGTzjx/3RUbiczEdS6il4zro+AIgAYx1bHbgtJaPyQ0cAZ16HS6NWM/22L5jKpGu61LLuWg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "3b7848c03c2dece69a9522b0fae8c4126d745f3b", "engines": {"node": ">=0.6"}, "gitHead": "d67d315b606c6bb809fedcbeebbbdb7f863852aa", "scripts": {"dist": "mkdirp dist && browserify --standalone Qs lib/index.js > dist/qs.js", "lint": "eslint lib/*.js text/*.js", "test": "npm run --silent coverage", "readme": "evalmd README.md", "pretest": "parallelshell 'npm run --silent readme' 'npm run --silent lint'", "coverage": "covert test", "prepublish": "npm run dist", "tests-only": "node test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/qs.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "_nodeVersion": "6.1.0", "dependencies": {}, "devDependencies": {"tape": "^4.5.1", "covert": "^1.1.0", "eslint": "^2.9.0", "evalmd": "^0.0.17", "mkdirp": "^0.5.1", "browserify": "^13.0.1", "iconv-lite": "^0.4.13", "parallelshell": "^2.0.0", "@ljharb/eslint-config": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/qs-6.2.0.tgz_1462749349998_0.03372702235355973", "host": "packages-12-west.internal.npmjs.com"}}, "5.2.1": {"name": "qs", "version": "5.2.1", "keywords": ["querystring", "qs"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@5.2.1", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}], "homepage": "https://github.com/hapijs/qs", "bugs": {"url": "https://github.com/hapijs/qs/issues"}, "dist": {"shasum": "801fee030e0b9450d6385adc48a4cc55b44aedfc", "tarball": "https://registry.npmjs.org/qs/-/qs-5.2.1.tgz", "integrity": "sha512-sh/hmLUTLEiYFhSbRvkM4zj6fMWnbqQt9wrppR2LJA/U/u4xS2eWN8LBE1xc79ExYZJBVZYSMBv/INC7wpE+fw==", "signatures": [{"sig": "MEUCIQDP9ZCg0PjKVneZdUZnPNqySUrLOYV5PErh/BcKOVwlcQIgMSUkKCY326TKOG2plAZJ6GLppmTVRw4QryZYcV+Andg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "801fee030e0b9450d6385adc48a4cc55b44aedfc", "engines": ">=0.10.40", "gitHead": "872da25efd167985c153898a06277ef34ce97a63", "scripts": {"dist": "browserify --standalone Qs lib/index.js > dist/qs.js", "test": "lab -a code -t 100 -L", "test-tap": "lab -a code -r tap -o tests.tap", "test-cov-html": "lab -a code -r html -o coverage.html"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/hapijs/qs.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "_nodeVersion": "6.3.0", "dependencies": {}, "devDependencies": {"lab": "5.x.x", "code": "1.x.x", "browserify": "^10.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/qs-5.2.1.tgz_1469043460945_0.553046926856041", "host": "packages-12-west.internal.npmjs.com"}}, "6.2.1": {"name": "qs", "version": "6.2.1", "keywords": ["querystring", "qs"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@6.2.1", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/qs", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "dist": {"shasum": "ce03c5ff0935bc1d9d69a9f14cbd18e568d67625", "tarball": "https://registry.npmjs.org/qs/-/qs-6.2.1.tgz", "integrity": "sha512-8yYCrxot1tgWocouWKNcewB8VdWuFe1Ab4GU38bg1gGNutR9bMMHaJgxDS5BGRVSLnyRrFhfJ8f04Er/kRmbLg==", "signatures": [{"sig": "MEUCIE9A/n37FZNZDJBRZCJDj2OSlfqKviXELhfY3xftYiEtAiEAiIFIqz4aJivB5HRaBeytsRVr7BbkGWoBIGWl7y166b0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "ce03c5ff0935bc1d9d69a9f14cbd18e568d67625", "engines": {"node": ">=0.6"}, "gitHead": "335f839142e6c2c69f5302c4940d92acb0e77561", "scripts": {"dist": "mkdirp dist && browserify --standalone Qs lib/index.js > dist/qs.js", "lint": "eslint lib/*.js text/*.js", "test": "npm run --silent coverage", "readme": "evalmd README.md", "pretest": "npm run --silent readme && npm run --silent lint", "coverage": "covert test", "prepublish": "npm run dist", "tests-only": "node test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/qs.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "_nodeVersion": "6.3.0", "dependencies": {}, "devDependencies": {"tape": "^4.6.0", "covert": "^1.1.0", "eslint": "^3.1.0", "evalmd": "^0.0.17", "mkdirp": "^0.5.1", "qs-iconv": "^1.0.3", "browserify": "^13.0.1", "iconv-lite": "^0.4.13", "parallelshell": "^2.0.0", "@ljharb/eslint-config": "^6.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/qs-6.2.1.tgz_1469044929716_0.06957711698487401", "host": "packages-12-west.internal.npmjs.com"}}, "6.3.0": {"name": "qs", "version": "6.3.0", "keywords": ["querystring", "qs"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@6.3.0", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/qs", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "dist": {"shasum": "f403b264f23bc01228c74131b407f18d5ea5d442", "tarball": "https://registry.npmjs.org/qs/-/qs-6.3.0.tgz", "integrity": "sha512-DR6wXKrnoGWWJvhiPWcvkVPxKd32GQyregSV6XpFG+GplAgmi/wVdJ06s1WP7UCbjiN/YHF0oo19CAUHN4q4BQ==", "signatures": [{"sig": "MEYCIQD8+bs6Cd+5P98tmCkWDa8N6eAFbfZR1hHalVVqansy2wIhANCND86MbxRwGY1YA91WmL3MKfXVa760EnRgbqrzSXET", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "f403b264f23bc01228c74131b407f18d5ea5d442", "engines": {"node": ">=0.6"}, "gitHead": "8aa9c26f90335b5483a4f456dea9acbada8a881c", "scripts": {"dist": "mkdirp dist && browserify --standalone Qs lib/index.js > dist/qs.js", "lint": "eslint lib/*.js test/*.js", "test": "npm run --silent coverage", "readme": "evalmd README.md", "pretest": "npm run --silent readme && npm run --silent lint", "coverage": "covert test", "prepublish": "safe-publish-latest && npm run dist", "tests-only": "node test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/qs.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "_nodeVersion": "6.6.0", "dependencies": {}, "devDependencies": {"tape": "^4.6.2", "covert": "^1.1.0", "eslint": "^3.8.0", "evalmd": "^0.0.17", "mkdirp": "^0.5.1", "qs-iconv": "^1.0.3", "browserify": "^13.1.0", "iconv-lite": "^0.4.13", "parallelshell": "^2.0.0", "safe-publish-latest": "^1.1.1", "@ljharb/eslint-config": "^8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/qs-6.3.0.tgz_1476663988179_0.7400497454218566", "host": "packages-16-east.internal.npmjs.com"}}, "6.3.1": {"name": "qs", "version": "6.3.1", "keywords": ["querystring", "qs"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@6.3.1", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/qs", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "dist": {"shasum": "918c0b3bcd36679772baf135b1acb4c1651ed79d", "tarball": "https://registry.npmjs.org/qs/-/qs-6.3.1.tgz", "integrity": "sha512-A4Hf32qAqlFeW0jPKgO8CiwK8KMBl9dv0I45752gbWG938IPI/pY7yK6hbBL5L0Y3nh5XeRWJ0MrGlK0VOFT7g==", "signatures": [{"sig": "MEQCICTcCKaBkKxqvhOKctnhSG6BoQChzNeL0XSCctSPamCAAiAJLfkBV/ThFtM4PEyy88Rpxlr5r5QW0bzZg+oy9DrF8w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "918c0b3bcd36679772baf135b1acb4c1651ed79d", "engines": {"node": ">=0.6"}, "gitHead": "153ce84948845330d90178cbad982fc7371df538", "scripts": {"dist": "mkdirp dist && browserify --standalone Qs lib/index.js > dist/qs.js", "lint": "eslint lib/*.js test/*.js", "test": "npm run --silent coverage", "readme": "evalmd README.md", "pretest": "npm run --silent readme && npm run --silent lint", "coverage": "covert test", "prepublish": "safe-publish-latest && npm run dist", "tests-only": "node test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/qs.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "_nodeVersion": "7.5.0", "dependencies": {}, "devDependencies": {"tape": "^4.6.3", "covert": "^1.1.0", "eslint": "^3.15.0", "evalmd": "^0.0.17", "mkdirp": "^0.5.1", "qs-iconv": "^1.0.4", "browserify": "^14.1.0", "iconv-lite": "^0.4.15", "parallelshell": "^2.0.0", "safe-publish-latest": "^1.1.1", "@ljharb/eslint-config": "^11.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/qs-6.3.1.tgz_1487220058786_0.35462796757929027", "host": "packages-12-west.internal.npmjs.com"}}, "6.1.1": {"name": "qs", "version": "6.1.1", "keywords": ["querystring", "qs"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@6.1.1", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/qs", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "dist": {"shasum": "9e671fca6df2881dde2e2922479f659752baad45", "tarball": "https://registry.npmjs.org/qs/-/qs-6.1.1.tgz", "integrity": "sha512-YxPtyopRY1w4wI7AhsB3E2ZGewQPE/eHMDjKlLjFP88I+Amky3MTN64dyt7Xn6HLa8CNuWsdPEjWcPArgvBLqQ==", "signatures": [{"sig": "MEYCIQDKRRFbU9OO/VbmUMicXO6oQJDIj7u/eTFoF67dk/cugAIhAKCM9NqF/+3ebH5gVkWFF8tACc9UCT3MahfZvrGBp4lA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "9e671fca6df2881dde2e2922479f659752baad45", "engines": {"node": ">=0.6"}, "gitHead": "fe86ca45597debd256450e8e7dba23ee61b4569b", "scripts": {"dist": "mkdirp dist && browserify --standalone Qs lib/index.js > dist/qs.js", "lint": "eslint lib/*.js text/*.js", "test": "parallelshell 'npm run readme' 'npm run lint' 'npm run coverage'", "readme": "evalmd README.md", "coverage": "covert test", "prepublish": "npm run dist", "tests-only": "node test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/qs.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "_nodeVersion": "7.5.0", "dependencies": {}, "devDependencies": {"tape": "^4.3.0", "covert": "^1.1.0", "eslint": "^1.10.3", "evalmd": "^0.0.16", "mkdirp": "^0.5.1", "browserify": "^12.0.1", "parallelshell": "^2.0.0", "@ljharb/eslint-config": "^1.6.1"}, "_npmOperationalInternal": {"tmp": "tmp/qs-6.1.1.tgz_1487227305904_0.8775855717249215", "host": "packages-12-west.internal.npmjs.com"}}, "6.0.3": {"name": "qs", "version": "6.0.3", "keywords": ["querystring", "qs"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@6.0.3", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/qs", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "dist": {"shasum": "95f870b23e70268fba18704e80667515905af06c", "tarball": "https://registry.npmjs.org/qs/-/qs-6.0.3.tgz", "integrity": "sha512-EHZJc+1HhJ49VmoJR1PPu5C7AIeJtc1ai89bz99AIZ1cHIYB4fsKjglCiXgZYlXau5vPVqMI398Ot5pZjEPLZg==", "signatures": [{"sig": "MEYCIQC5RIAhYrHK8eBVC5WLk2YVh5CQ/zF6zmNLbmbj+e7uuQIhAKL/TM+/HeRX+/UEKPCE7M+Nk2ER0oKHkTSB/DVm/mT3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "95f870b23e70268fba18704e80667515905af06c", "engines": {"node": ">=0.6"}, "gitHead": "be1c4211a6031cac3443ce66220f4954338bde3f", "scripts": {"dist": "mkdirp dist && browserify --standalone Qs lib/index.js > dist/qs.js", "lint": "eslint lib/*.js text/*.js", "test": "parallelshell 'npm run readme' 'npm run lint' 'npm run coverage'", "readme": "evalmd README.md", "coverage": "covert test", "prepublish": "npm run dist", "tests-only": "node test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/qs.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "_nodeVersion": "7.5.0", "dependencies": {}, "devDependencies": {"tape": "^4.3.0", "covert": "^1.1.0", "eslint": "^1.10.3", "evalmd": "^0.0.16", "mkdirp": "^0.5.1", "browserify": "^12.0.1", "parallelshell": "^2.0.0", "@ljharb/eslint-config": "^1.6.1"}, "_npmOperationalInternal": {"tmp": "tmp/qs-6.0.3.tgz_1487228751019_0.30726271285675466", "host": "packages-12-west.internal.npmjs.com"}}, "6.2.2": {"name": "qs", "version": "6.2.2", "keywords": ["querystring", "qs"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@6.2.2", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/qs", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "dist": {"shasum": "d506a5ad5b2cae1fd35c4f54ec182e267e3ef586", "tarball": "https://registry.npmjs.org/qs/-/qs-6.2.2.tgz", "integrity": "sha512-ZQ/RV/2Xt6cEDP9rPehKP0C2SIg4guXSjGFsKdVPzWSsvAVaS9q4e6gTirvagGOKFYovRuQNjsZJ19WPdOlqZw==", "signatures": [{"sig": "MEUCIEpyMyUCS67duiUJkCic9tl/8V04Bl+8JTIOt6sNlmZzAiEApWuSnCLpukcevqhCbnA8F4/Za8reOKXz3ZurbUL4ywI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "d506a5ad5b2cae1fd35c4f54ec182e267e3ef586", "engines": {"node": ">=0.6"}, "gitHead": "cafa12484f4e2f5318c0f108cf17b50233701427", "scripts": {"dist": "mkdirp dist && browserify --standalone Qs lib/index.js > dist/qs.js", "lint": "eslint lib/*.js text/*.js", "test": "npm run --silent coverage", "readme": "evalmd README.md", "pretest": "npm run --silent readme && npm run --silent lint", "coverage": "covert test", "prepublish": "npm run dist", "tests-only": "node test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/qs.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "_nodeVersion": "7.5.0", "dependencies": {}, "devDependencies": {"tape": "^4.6.0", "covert": "^1.1.0", "eslint": "^3.1.0", "evalmd": "^0.0.17", "mkdirp": "^0.5.1", "qs-iconv": "^1.0.3", "browserify": "^13.0.1", "iconv-lite": "^0.4.13", "parallelshell": "^2.0.0", "@ljharb/eslint-config": "^6.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/qs-6.2.2.tgz_1487231073987_0.7608081009238958", "host": "packages-18-east.internal.npmjs.com"}}, "6.4.0": {"name": "qs", "version": "6.4.0", "keywords": ["querystring", "qs"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@6.4.0", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/qs", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "dist": {"shasum": "13e26d28ad6b0ffaa91312cd3bf708ed351e7233", "tarball": "https://registry.npmjs.org/qs/-/qs-6.4.0.tgz", "integrity": "sha512-Qs6dfgR5OksK/PSxl1kGxiZgEQe8RqJMB9wZqVlKQfU+zzV+HY77pWJnoJENACKDQByWdpr8ZPIh1TBi4lpiSQ==", "signatures": [{"sig": "MEYCIQDsM07aGUH5sSe88aShOzeAiPsIvyhpunqjEx49DUa1OgIhAKz2CHXk7VUikQXtAB7RVlcCOtGv8P4/UeboxwsL6Ayn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "13e26d28ad6b0ffaa91312cd3bf708ed351e7233", "engines": {"node": ">=0.6"}, "gitHead": "c7f87b8d2eedd377f6ace065655201f51bee6334", "scripts": {"dist": "mkdirp dist && browserify --standalone Qs lib/index.js > dist/qs.js", "lint": "eslint lib/*.js test/*.js", "test": "npm run --silent coverage", "readme": "evalmd README.md", "pretest": "npm run --silent readme && npm run --silent lint", "coverage": "covert test", "prepublish": "safe-publish-latest && npm run dist", "tests-only": "node test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/qs.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "_nodeVersion": "7.7.1", "dependencies": {}, "devDependencies": {"tape": "^4.6.3", "covert": "^1.1.0", "eslint": "^3.17.0", "evalmd": "^0.0.17", "mkdirp": "^0.5.1", "qs-iconv": "^1.0.4", "browserify": "^14.1.0", "iconv-lite": "^0.4.15", "parallelshell": "^2.0.0", "safe-publish-latest": "^1.1.1", "@ljharb/eslint-config": "^11.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/qs-6.4.0.tgz_1488783808282_0.7979955193586648", "host": "packages-12-west.internal.npmjs.com"}}, "6.3.2": {"name": "qs", "version": "6.3.2", "keywords": ["querystring", "qs"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@6.3.2", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/qs", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "dist": {"shasum": "e75bd5f6e268122a2a0e0bda630b2550c166502c", "tarball": "https://registry.npmjs.org/qs/-/qs-6.3.2.tgz", "integrity": "sha512-H7Es34wXu+JjuD3QLwVgqtVU1nisFhwxYdYCYSFzWyP5/S4s8DtJ6rbVWdHVat/UPYiV36wxYmL7rPbLQJrX7g==", "signatures": [{"sig": "MEUCICjrutMIqmARhLxaoyHZr1jM0inIrqy9QKtq0cFJ8zeSAiEAi9e0jyCipu0/xR5VAhjKE248AEpWwqhT9QE0fo7jjCs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "e75bd5f6e268122a2a0e0bda630b2550c166502c", "engines": {"node": ">=0.6"}, "gitHead": "9ee56121311dac6b6014bfe56b3df0ebbf4ed048", "scripts": {"dist": "mkdirp dist && browserify --standalone Qs lib/index.js > dist/qs.js", "lint": "eslint lib/*.js test/*.js", "test": "npm run --silent coverage", "readme": "evalmd README.md", "pretest": "npm run --silent readme && npm run --silent lint", "coverage": "covert test", "prepublish": "safe-publish-latest && npm run dist", "tests-only": "node test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/qs.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "_nodeVersion": "7.7.1", "dependencies": {}, "devDependencies": {"tape": "^4.6.3", "covert": "^1.1.0", "eslint": "^3.17.0", "evalmd": "^0.0.17", "mkdirp": "^0.5.1", "qs-iconv": "^1.0.4", "browserify": "^14.1.0", "iconv-lite": "^0.4.15", "parallelshell": "^2.0.0", "safe-publish-latest": "^1.1.1", "@ljharb/eslint-config": "^11.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/qs-6.3.2.tgz_1488790933355_0.4183437137398869", "host": "packages-18-east.internal.npmjs.com"}}, "6.2.3": {"name": "qs", "version": "6.2.3", "keywords": ["querystring", "qs"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@6.2.3", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/qs", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "dist": {"shasum": "1cfcb25c10a9b2b483053ff39f5dfc9233908cfe", "tarball": "https://registry.npmjs.org/qs/-/qs-6.2.3.tgz", "integrity": "sha512-AY4g8t3LMboim0t6XWFdz6J5OuJ1ZNYu54SXihS/OMpgyCqYmcAJnWqkNSOjSjWmq3xxy+GF9uWQI2lI/7tKIA==", "signatures": [{"sig": "MEYCIQDNBVjWTuqdaib4Y1TWiAvpBy0oIQOS3gJqb/JTnOTGBQIhALsipnsbc25Hey1lJCSDBMpz4Xgs3Gp3PvHAE+I0My3l", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "1cfcb25c10a9b2b483053ff39f5dfc9233908cfe", "engines": {"node": ">=0.6"}, "gitHead": "1fb74cb66310c506e4b6bc04fa258a1759750222", "scripts": {"dist": "mkdirp dist && browserify --standalone Qs lib/index.js > dist/qs.js", "lint": "eslint lib/*.js text/*.js", "test": "npm run --silent coverage", "readme": "evalmd README.md", "pretest": "npm run --silent readme && npm run --silent lint", "coverage": "covert test", "prepublish": "npm run dist", "tests-only": "node test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/qs.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "_nodeVersion": "7.7.1", "dependencies": {}, "devDependencies": {"tape": "^4.6.0", "covert": "^1.1.0", "eslint": "^3.1.0", "evalmd": "^0.0.17", "mkdirp": "^0.5.1", "qs-iconv": "^1.0.3", "browserify": "^13.0.1", "iconv-lite": "^0.4.13", "parallelshell": "^2.0.0", "@ljharb/eslint-config": "^6.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/qs-6.2.3.tgz_1488819027828_0.679028207436204", "host": "packages-18-east.internal.npmjs.com"}}, "6.1.2": {"name": "qs", "version": "6.1.2", "keywords": ["querystring", "qs"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@6.1.2", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/qs", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "dist": {"shasum": "b59d8925d0c999ef6d63acf4ac5abb0adaa24b54", "tarball": "https://registry.npmjs.org/qs/-/qs-6.1.2.tgz", "integrity": "sha512-vkyEo9cSlcgr1xj5n14ykoPWKE36R8wkxK2fQkbGACZNv4zDGFw/juEwFFUs9/APU7DaTMRlRNTYISLPD0Z4Qw==", "signatures": [{"sig": "MEYCIQDWXZUxE+DOWuWzBaYwZ3zaVzrbJjGdJvuQX0+WC3xe0QIhAPrqEJ0z+tJGx7ejswXHpfucyB/ECRLqub1g2ipTcdF9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "b59d8925d0c999ef6d63acf4ac5abb0adaa24b54", "engines": {"node": ">=0.6"}, "gitHead": "68ca039dc2b23b9dabff79a34a906b56c17addaa", "scripts": {"dist": "mkdirp dist && browserify --standalone Qs lib/index.js > dist/qs.js", "lint": "eslint lib/*.js text/*.js", "test": "npm run coverage", "readme": "evalmd README.md", "pretest": "npm run lint && npm run readme", "coverage": "covert test", "prepublish": "npm run dist", "tests-only": "node test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/qs.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "_nodeVersion": "7.7.1", "dependencies": {}, "devDependencies": {"tape": "^4.3.0", "covert": "^1.1.0", "eslint": "^1.10.3", "evalmd": "^0.0.16", "mkdirp": "^0.5.1", "browserify": "^12.0.1", "parallelshell": "^2.0.0", "@ljharb/eslint-config": "^1.6.1"}, "_npmOperationalInternal": {"tmp": "tmp/qs-6.1.2.tgz_1488819050129_0.8727251156233251", "host": "packages-18-east.internal.npmjs.com"}}, "6.0.4": {"name": "qs", "version": "6.0.4", "keywords": ["querystring", "qs"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@6.0.4", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/qs", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "dist": {"shasum": "51019d84720c939b82737e84556a782338ecea7b", "tarball": "https://registry.npmjs.org/qs/-/qs-6.0.4.tgz", "integrity": "sha512-zTqfwnNxm6UUH0mxoY9TRHavimMmNp1YZwCIO3oQbEbuQWDXo/yhigCah9Q3KTerKxlVvK3RaTdJD7dHBDBptg==", "signatures": [{"sig": "MEQCIE6C9A7QVyFv7viWuimKmCB/j/uL8aami2ROtg8sbU95AiBWGgsJtKZackdJ2XA04lZ7XThMYPugGboQXZ8J/7WBow==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "51019d84720c939b82737e84556a782338ecea7b", "engines": {"node": ">=0.6"}, "gitHead": "10233c9f92a4e3537009fbfbd0baf6f3738c4551", "scripts": {"dist": "mkdirp dist && browserify --standalone Qs lib/index.js > dist/qs.js", "lint": "eslint lib/*.js text/*.js", "test": "npm run coverage", "readme": "evalmd README.md", "pretest": "npm run lint && npm run readme", "coverage": "covert test", "prepublish": "npm run dist", "tests-only": "node test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/qs.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "_nodeVersion": "7.7.1", "dependencies": {}, "devDependencies": {"tape": "^4.3.0", "covert": "^1.1.0", "eslint": "^1.10.3", "evalmd": "^0.0.16", "mkdirp": "^0.5.1", "browserify": "^12.0.1", "parallelshell": "^2.0.0", "@ljharb/eslint-config": "^1.6.1"}, "_npmOperationalInternal": {"tmp": "tmp/qs-6.0.4.tgz_1488819105955_0.938799966359511", "host": "packages-18-east.internal.npmjs.com"}}, "6.5.0": {"name": "qs", "version": "6.5.0", "keywords": ["querystring", "qs"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@6.5.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "hueniverse", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/qs", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "dist": {"shasum": "8d04954d364def3efc55b5a0793e1e2c8b1e6e49", "tarball": "https://registry.npmjs.org/qs/-/qs-6.5.0.tgz", "integrity": "sha512-fjVFjW9yhqMhVGwRExCXLhJKrLlkYSaxNWdyc9rmHlrVZbk35YHH312dFd7191uQeXkI3mKLZTIbSvIeFwFemg==", "signatures": [{"sig": "MEUCIQCQPVNSjQ74K2JVvPhGfVQ/Yhp0BZaI0Cy6Px88j4i+EgIgA1S9ZEA8P9JJNA1FU6m8OanlPJZUwK3T0Wpupz/+/p8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "engines": {"node": ">=0.6"}, "gitHead": "d66ac175bbf8afa44b41c2c85b04ae00bac7c916", "scripts": {"dist": "mkdirp dist && browserify --standalone Qs lib/index.js > dist/qs.js", "lint": "eslint lib/*.js test/*.js", "test": "npm run --silent coverage", "readme": "evalmd README.md", "prelint": "editorconfig-tools check * lib/* test/*", "pretest": "npm run --silent readme && npm run --silent lint", "coverage": "covert test", "prepublish": "safe-publish-latest && npm run dist", "tests-only": "node test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/qs.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "_nodeVersion": "8.1.2", "dependencies": {}, "devDependencies": {"tape": "^4.7.0", "covert": "^1.1.0", "eslint": "^3.19.0", "evalmd": "^0.0.17", "mkdirp": "^0.5.1", "qs-iconv": "^1.0.4", "browserify": "^14.4.0", "iconv-lite": "^0.4.18", "editorconfig-tools": "^0.1.1", "safe-publish-latest": "^1.1.1", "@ljharb/eslint-config": "^11.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/qs-6.5.0.tgz_1498633673933_0.663175018504262", "host": "s3://npm-registry-packages"}}, "6.5.1": {"name": "qs", "version": "6.5.1", "keywords": ["querystring", "qs"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@6.5.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "hueniverse", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/qs", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "dist": {"shasum": "349cdf6eef89ec45c12d7d5eb3fc0c870343a6d8", "tarball": "https://registry.npmjs.org/qs/-/qs-6.5.1.tgz", "integrity": "sha512-eRzhrN1WSINYCDCbrz796z37LOe3m5tmW7RQf6oBntukAG1nmovJvhnwHHRMAfeoItc1m2Hk02WER2aQ/iqs+A==", "signatures": [{"sig": "MEQCICj9IC3R31alRj7yz7sO7/17V4mszkDxn7UiVp0LsYT9AiAfud7+rcnne6mYRe8/UUikqLr878mXpPAWf0O8TdOUFw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "engines": {"node": ">=0.6"}, "gitHead": "0e838daa71f91fecda456441ac64e615f38bed8b", "scripts": {"dist": "mkdirp dist && browserify --standalone Qs lib/index.js > dist/qs.js", "lint": "eslint lib/*.js test/*.js", "test": "npm run --silent coverage", "readme": "evalmd README.md", "prelint": "editorconfig-tools check * lib/* test/*", "pretest": "npm run --silent readme && npm run --silent lint", "coverage": "covert test", "prepublish": "safe-publish-latest && npm run dist", "tests-only": "node test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/qs.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {}, "devDependencies": {"tape": "^4.8.0", "covert": "^1.1.0", "eslint": "^4.6.1", "evalmd": "^0.0.17", "mkdirp": "^0.5.1", "qs-iconv": "^1.0.4", "browserify": "^14.4.0", "iconv-lite": "^0.4.18", "editorconfig-tools": "^0.1.1", "safe-publish-latest": "^1.1.1", "@ljharb/eslint-config": "^12.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/qs-6.5.1.tgz_1504943698164_0.10575866606086493", "host": "s3://npm-registry-packages"}}, "6.5.2": {"name": "qs", "version": "6.5.2", "keywords": ["querystring", "qs"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@6.5.2", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/qs", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "dist": {"shasum": "cb3ae806e8740444584ef154ce8ee98d403f3e36", "tarball": "https://registry.npmjs.org/qs/-/qs-6.5.2.tgz", "fileCount": 18, "integrity": "sha512-N5ZAX4/LxJmF+7wN74pUD6qAh9/wnvdQcjq9TZjevvXzSUo7bfmw91saqMjzGS2xq91/odN2dW/WOl7qQHNDGA==", "signatures": [{"sig": "MEQCIA69UyoQ3ZswMYw2PvQIv8HOcpaQAMkbsfJTjQyyoZRJAiAB7cLfjjupQoMULn6gBztSx8C1J8Hb429XAqSVHkP+7w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 114127, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa6/hXCRA9TVsSAnZWagAAEJQP/0c3B2SWDmhz9zC9na++\nc6v1YkoIPJ+IhVaFhrpKvT3HeMsRbdQFTjGJ+7VijncHdxuieAGSAo5Tm9MZ\nnrskliXl61TelWC3/1GJ7hzggBCIudwf1Kl5P2lx+XLTBag+B3BnfxR+Gp/o\nF8f+CJ3I2KxkKZWcHYBTLLgE5dPi5i5KFnh9EAsaZRUiowCDl+CaKzj3u55M\nUfwKrUXLfZtgL3ALj5Nh/g1F+NW7m7RIDREVFPJ3MqeiNUXUnGU9KBmQuHMm\nWvOMuvN9vgPm40fp9TBZQlEazaR12QBqqvpgDtYBNqfUQaznoyZ8rN36W1oB\n5iV3Ve6B+BeIPNMaf92TvAnbvLcAEfy36VuOiH3IHDyEDAk706n/etInNeYS\nK/N359uzbRCQ7MQTJ5Z9TmlsD6/TFRfcBcGUrnPoL6HEEIxmAnJbKApy1sP4\nqb/GnrOQMuRJ6ty58aTEzotFvsxFw8kaeeexZ7Ftd7cKYdBRjutfiibJTH3v\n8feuHS0wDXRDvVd906qwYvC4LwGeqTJeRUHtFrd3pwCDqiXGyEXP60Yyir6L\nqZIYxzv8FRBspN//x5YJndpU06joflcCmm0bLfu+zLSevL4eciH+ciBQZQVY\nzWdcWZktjGXNt5I1s7xO31sgV7nwiXSPUQDS/SuaE860GWQ9pp0yL9M23S/q\ny88W\r\n=156t\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=0.6"}, "gitHead": "eaabd05558b657c75a137caf2eb030e8e856b82f", "scripts": {"dist": "mkdirp dist && browserify --standalone Qs lib/index.js > dist/qs.js", "lint": "eslint lib/*.js test/*.js", "test": "npm run --silent coverage", "readme": "evalmd README.md", "prelint": "editorconfig-tools check * lib/* test/*", "pretest": "npm run --silent readme && npm run --silent lint", "coverage": "covert test", "prepublish": "safe-publish-latest && npm run dist", "tests-only": "node test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/qs.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "_nodeVersion": "10.0.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.9.0", "covert": "^1.1.0", "eslint": "^4.19.1", "evalmd": "^0.0.17", "mkdirp": "^0.5.1", "qs-iconv": "^1.0.4", "browserify": "^16.2.0", "iconv-lite": "^0.4.21", "safer-buffer": "^2.1.2", "editorconfig-tools": "^0.1.1", "safe-publish-latest": "^1.1.1", "@ljharb/eslint-config": "^12.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/qs_6.5.2_1525413973776_0.4130968177833936", "host": "s3://npm-registry-packages"}}, "6.6.0": {"name": "qs", "version": "6.6.0", "keywords": ["querystring", "qs"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@6.6.0", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/qs", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "dist": {"shasum": "a99c0f69a8d26bf7ef012f871cdabb0aee4424c2", "tarball": "https://registry.npmjs.org/qs/-/qs-6.6.0.tgz", "fileCount": 18, "integrity": "sha512-KIJqT9jQJDQx5h5uAVPimw6yVg2SekOKu959OCtktD3FjzbpvaPr8i4zzg07DOMz+igA4W/aNM7OV8H37pFYfA==", "signatures": [{"sig": "MEYCIQCb9rMi9mVdSbO2499hrPbLTp9aUgNP5OkIJCSp99NVNAIhAOEmg3oVLf8DxcIFZQf60dKX2NzJCic5O5I48iZMJBrt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 134583, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb+kTaCRA9TVsSAnZWagAAoKgQAJ3yaFKHAZdEPsXsO36H\n3uSKGYfSd8+QqDXMYkOQIWfUtFQiscARQbWHctPfd07zdfAgYMx6Gx2N62qz\n9VJVFPe5USaoV7Y6uRFeOwsnh0qcEwOF5worq/rCNhIctKl10BBVAhQe84e4\natanjbg3l2WsIKv+4MjFs890dOB+gXNimoXYP4KzJvUTOplbIETbfFYcMI6k\nrR9Gu6zVriEqF5j/R1WWwAICFYK9+vF7QY1BVLhP/Xxu3vdI1e88j5BPx9Jf\nZ4b2v+S12Fqoe3iiDYmqTdwHrEAWUkDxJswWKKkP4JrH6D/rgA3HKVAQgs+y\nI6O+Fznr6zNPnwAowRw9y0FBIkBWgvHOAbbmG5tjkLDqB/7jmzMWveYU1hMI\nHCLoYtZ4oj/dUDGxWMQssDwdIQrNwWa+c7ZByKDisvzQwY70BI3whckTFYWj\n4onUR9nGCzsHPd0HYu8iVEU2kMil3xQLl5Zr/XPZsx4rLwtXD2SIIlg7YXVn\n3UzkGAKvsICOla+XoMUZgSbwHu8yoHvPGQOX/+8SvUIitbBbX3ADoWoTTclh\nB3KiH/0QbdXUMyAolVwA1sNhhb7mIXKAsg8y9NvyhtZ5B2ZIN1GMomqub68v\nQ+d+rVWtgRe13rJCQ0kx0SpzmaXd96PkoADsYibXM61hBnxBYKQSSydhVShD\nPWXC\r\n=NVkV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=0.6"}, "gitHead": "34af57edde61639054ea7b38fdfce050cffdab29", "scripts": {"dist": "mkdirp dist && browserify --standalone Qs lib/index.js > dist/qs.js", "lint": "eslint lib/*.js test/*.js", "test": "npm run --silent coverage", "readme": "evalmd README.md", "pretest": "npm run --silent readme && npm run --silent lint", "coverage": "covert test", "postlint": "editorconfig-tools check * lib/* test/*", "prepublish": "safe-publish-latest && npm run dist", "tests-only": "node test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/qs.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "_nodeVersion": "11.2.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.9.1", "covert": "^1.1.0", "eslint": "^5.9.0", "evalmd": "^0.0.17", "mkdirp": "^0.5.1", "qs-iconv": "^1.0.4", "browserify": "^16.2.3", "iconv-lite": "^0.4.24", "safer-buffer": "^2.1.2", "editorconfig-tools": "^0.1.1", "safe-publish-latest": "^1.1.2", "@ljharb/eslint-config": "^13.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/qs_6.6.0_1543128281582_0.7909573175660216", "host": "s3://npm-registry-packages"}}, "6.7.0": {"name": "qs", "version": "6.7.0", "keywords": ["querystring", "qs", "query", "url", "parse", "stringify"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@6.7.0", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/qs", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "dist": {"shasum": "41dc1a015e3d581f1621776be31afb2876a9b1bc", "tarball": "https://registry.npmjs.org/qs/-/qs-6.7.0.tgz", "fileCount": 18, "integrity": "sha512-VCdBRNFTX1fyE7Nb6FYoURo/SPe62QCaAyzJvUjwRaIsc+NePBEniHlvxFmmX56+HZphIGtV0XeCirBtpDrTyQ==", "signatures": [{"sig": "MEUCIC+PfFMzZGcgoP4xu8wJH3oHjGmkiwDDVVW60Mbg6Zr7AiEAlJlAvbk8Y05QTRY/oJmD9Owwrysu1dffn1doNpPfz0I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 142918, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJclUozCRA9TVsSAnZWagAAoroP/0PHjSUPNl7JIbIdKciJ\nBCTXasz49hv66gp1crSOmRPut07b3ao8gfYA4asdVauyJUteb9LtFqDOcvz9\n/VXV7XU63SnJIF2tsWV5sRvLgX4NgQzViijUH5XuE62potDawIa6M2i4zJTh\nWO5OSmizv/ODNCkoNw0tNiriB9sqQ1hXzcWg55geAWy4COB5E0+OvWKV9j/x\neAHQVUKBJTfNb+SsN8YqtDK9Dp+rGy0VCKn606FWu/l79sK4JvMYdpDpRliE\nX546lMOEzYZDDH+2Yq7n365PKV2pDzomRSoJnPWDimz6AbKDy0MIKpXQVmhi\n52MjXPgHhCntt2ETQToj6yA9Suy4DQ1nxtZtDdudMxr+4hqJShBhgMGTqpX6\nMS9wBP5CisDzpzHglcH5gRvDF4HLJU52/AXk2hECQsfVPTQx4R6gl/c0jCRQ\nIo7bBcFPaAsYQUjfo7PiOpMUZX5T1STBXAS7rN3tf02OBpnWD38xNu9t/6hI\n5HiurnlTVCUxajdGSitX0JZKDTI7bskIoiXTDMQxT3ppo1aptzP+9ZGiUZjz\nJRVD6LAC1PFC0cFXvlo0ZNbh0DxgiIHOCMpveS/8uh4KdZaWjnbhoMRTRgi+\novatKeu2pqLJqtZkh+016+dRR8QYDsFVDgRPc3qBOmMsR1CZ2E54euLpoFzf\nRfi3\r\n=ivNL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=0.6"}, "gitHead": "125e103b61f2bef245970f5a2a8dceffe5aab59a", "scripts": {"dist": "mkdirp dist && browserify --standalone Qs lib/index.js > dist/qs.js", "lint": "eslint lib/*.js test/*.js", "test": "npm run --silent coverage", "readme": "evalmd README.md", "pretest": "npm run --silent readme && npm run --silent lint", "coverage": "covert test", "postlint": "editorconfig-tools check * lib/* test/*", "prepublish": "safe-publish-latest && npm run dist", "tests-only": "node test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/qs.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.10.1", "covert": "^1.1.1", "eslint": "^5.15.3", "evalmd": "^0.0.17", "mkdirp": "^0.5.1", "for-each": "^0.3.3", "qs-iconv": "^1.0.4", "browserify": "^16.2.3", "iconv-lite": "^0.4.24", "safer-buffer": "^2.1.2", "object-inspect": "^1.6.0", "editorconfig-tools": "^0.1.1", "safe-publish-latest": "^1.1.2", "@ljharb/eslint-config": "^13.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/qs_6.7.0_1553287731018_0.8639544724083812", "host": "s3://npm-registry-packages"}}, "6.8.0": {"name": "qs", "version": "6.8.0", "keywords": ["querystring", "qs", "query", "url", "parse", "stringify"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@6.8.0", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/qs", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "dist": {"shasum": "87b763f0d37ca54200334cd57bb2ef8f68a1d081", "tarball": "https://registry.npmjs.org/qs/-/qs-6.8.0.tgz", "fileCount": 19, "integrity": "sha512-tPSkj8y92PfZVbinY1n84i1Qdx75lZjMQYx9WZhnkofyxzw2r7Ho39G3/aEvSUdebxpnnM4LZJCtvE/Aq3+s9w==", "signatures": [{"sig": "MEUCID/L21OV8n45fRKoel34ohZNxxq8EBSvR1L90kMc2RAgAiEA6BwHkeihtGXUvENlsVo02AvfDeZBWG1+FElBKB+GQHo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 148758, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdV2pQCRA9TVsSAnZWagAAvyQQAJDQK8zkD7AjVqr8BYy6\nc/fXvt1TxD4m+5T4vUMuGisTN4nEW86ugHrvuG7sfLCTCySQyNo6xdHVz6ea\nDLzH9PUCWM8R1OWmyXQ5kyyJItqVG9kkeTUer9s09hqEtWz5vuCGS9rq0J8F\nc8gjynqBpAS9UkoyMvP22fV+e/L2VXYXx7nndWpkm59DQsohreHsNDWr3F9A\nwAqmW7eVG57Ls66ukfvoWXPRA8jIVpRuysf6s8RpSOxrZiYUTmyWmOZHWTHk\nWi16XxTvEWwr0wvPhozMK4tOA95iMh2kneo5gPckFBpEKqHwKJJAfoq8jbcg\nH+/kxik3KKblHgO9wWpeSfZHyZFKjZec+W50FcNQfcfUd3UNU3ONRGZKRz5D\nqwB8La11JkMYGOAQYEOuOtCNRlCdfhqMlcBTVjsPx9yck+/afjTy5CUch5R4\nkuTdPEheAU2xldRz6QSYASY+w5i1zQXex6bF+JzA5zwtaYVtWlwIz8HraSZt\nMaOkNLw9rw/mlz2VwfEwoZJOoCzF+ceUUxrWo+vILmGbn0dXjQH/6P/w9KID\nCTNj0Y8Lg7XVjcxVWGhvmwpco+uCRl3bgzH9VwRgrK9WztIGnbyBrJ4FM2+d\nC7Dh+rXqi06LUAIU9+vwtiwRkB7KH2HpOW0WRnPUUXihHZFeITsMTANmlMGd\nRdqb\r\n=cmSp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=0.6"}, "gitHead": "57657fffc89c4dafb830ee78ec79892a17653eed", "scripts": {"dist": "mkdirp dist && browserify --standalone Qs lib/index.js > dist/qs.js", "lint": "eslint lib/*.js test/*.js", "test": "npm run --silent coverage", "readme": "evalmd README.md", "pretest": "npm run --silent readme && npm run --silent lint", "coverage": "covert test", "postlint": "eclint check * lib/* test/*", "prepublish": "safe-publish-latest && npm run dist", "tests-only": "node test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/qs.git", "type": "git"}, "_npmVersion": "6.10.2", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "_nodeVersion": "12.8.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.11.0", "covert": "^1.1.1", "eclint": "^2.8.1", "eslint": "^6.1.0", "evalmd": "^0.0.17", "mkdirp": "^0.5.1", "for-each": "^0.3.3", "qs-iconv": "^1.0.4", "browserify": "^16.5.0", "iconv-lite": "^0.4.24", "has-symbols": "^1.0.0", "safer-buffer": "^2.1.2", "object-inspect": "^1.6.0", "safe-publish-latest": "^1.1.3", "@ljharb/eslint-config": "^14.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/qs_6.8.0_1566009935569_0.19894011235292686", "host": "s3://npm-registry-packages"}}, "6.9.0": {"name": "qs", "version": "6.9.0", "keywords": ["querystring", "qs", "query", "url", "parse", "stringify"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@6.9.0", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/qs", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "dist": {"shasum": "d1297e2a049c53119cb49cca366adbbacc80b409", "tarball": "https://registry.npmjs.org/qs/-/qs-6.9.0.tgz", "fileCount": 19, "integrity": "sha512-27RP4UotQORTpmNQDX8BHPukOnBP3p1uUJY5UnDhaJB+rMt9iMsok724XL+UHU23bEFOHRMQ2ZhI99qOWUMGFA==", "signatures": [{"sig": "MEYCIQCJYiFYWNLeTKTGBeXik0zj9OpTpUwKZzY2xo9dGSoN9gIhAJqfNf5UR9QxwsRf92Qd3+kVCLb0AKkmaD45i2TAScuG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 152366, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdhqOLCRA9TVsSAnZWagAA+G8P/2ZsKpypA7CIMTYunSSg\noTo57Pbg5Dg5Wsb1x7gF3IfQmmUTgN9ixJOanhshuYdbd/pZ7bohHZnyoQ0U\nGnWOligjyvuQ5uK5cvmOFNh7mA8eeurkZmqNIaTWNru/7GHUoP2XYw8JcxRe\nKBK4kdcOCEzECgv1pCG71+9MzaX9G5S+l6jzSP0j3N06MG1wwbVIHYbN6RWo\nrmVTL2qdUT9XV+vK7mG5ygjIXF69DRY0jLPn9RRhCQITEQsVXabgkPSR+h26\nu5WkoM3thItPYA8/txhJfOxOyYgDgHNyiYCiWpVeNo+nl7M7iqDMUO9xplfC\noxdRa8ib5MjC5vmU2Bl6z7MFSHGgFEav+jEhm6TPQz7vFnAoYscxLzJGbPnD\nO0shqFPPZUNCl2M/RDX7vpMb/5QFWX2NJEmMP/hfMOJ+yT3atTd06E2tnn5i\nsywEKi2UJb61gOP6KJ4FwFhu5W/E4+W6kFgMv/L+6D31ycEK+E/G9hNOZNIJ\nbrXzI38eVdbMSpkO7Ss99m696Msphd5j9hy25wVzTu0g/8Krdd1vlkmQz358\nKnLUqlBkmhdbvGahT4tbW9ldKE4Fp/rEiByZDeY6hFgqjmfiAvTLx+AyW1/v\n02M+O9GtV2P9N1V9fpwC7IMw6UDrDHXZZQnwkcXCEyJM++/ooiu6YohFXIah\nzeO7\r\n=+2wp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=0.6"}, "gitHead": "670254b63fc7770894eed9a0f020bc0b72698ce3", "scripts": {"dist": "mkdirp dist && browserify --standalone Qs lib/index.js > dist/qs.js", "lint": "eslint lib/*.js test/*.js", "test": "npm run --silent coverage", "readme": "evalmd README.md", "pretest": "npm run --silent readme && npm run --silent lint", "coverage": "covert test", "postlint": "eclint check * lib/* test/*", "posttest": "npx aud", "prepublish": "safe-publish-latest && npm run dist", "tests-only": "node test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/qs.git", "type": "git"}, "_npmVersion": "6.10.3", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "_nodeVersion": "12.10.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.11.0", "covert": "^1.1.1", "eclint": "^2.8.1", "eslint": "^6.4.0", "evalmd": "^0.0.19", "mkdirp": "^0.5.1", "for-each": "^0.3.3", "qs-iconv": "^1.0.4", "browserify": "^16.5.0", "iconv-lite": "^0.4.24", "has-symbols": "^1.0.0", "safer-buffer": "^2.1.2", "object-inspect": "^1.6.0", "safe-publish-latest": "^1.1.3", "@ljharb/eslint-config": "^14.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/qs_6.9.0_1569104778997_0.9699639598156031", "host": "s3://npm-registry-packages"}}, "6.9.1": {"name": "qs", "version": "6.9.1", "keywords": ["querystring", "qs", "query", "url", "parse", "stringify"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@6.9.1", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/qs", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "dist": {"shasum": "20082c65cb78223635ab1a9eaca8875a29bf8ec9", "tarball": "https://registry.npmjs.org/qs/-/qs-6.9.1.tgz", "fileCount": 19, "integrity": "sha512-Cxm7/SS/y/Z3MHWSxXb8lIFqgqBowP5JMlTUFyJN88y0SGQhVmZnqFK/PeuMX9LzUyWsqqhNxIyg0jlzq946yA==", "signatures": [{"sig": "MEUCIQDkma1Hl8zRbi/UPc2glvkaZ99MsNO5yF+NyY0F10fRFQIgMev+r/L4MayHIxEUcTh2QnQRZ109OLEzePTAdeOGd2I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 154259, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdxQ9LCRA9TVsSAnZWagAAg0MP/2iGAM115NBWii9uxv1I\nMtgS6Q7RE2woj0nyK2MevzHyiBVv3AsytXhVxzAodz2Vv831O1wjTMR8g5l9\nvzx3YGTKghVUnt1ElBUCSh36LXgzahqMJV0BwS9mv/Bve4UUilxKOuKTndSy\nbCFXacaUJW/iPZFWMGo3NSOanHSedVESJR9uIfzIfnY70iQcoMnX3aEGiOwB\nGBA/U8oE+5DfNgZDjnAtEpqOjLdPRpV5Z5ug5gqnsPKqMgqW65p70hO+nFro\n9X0C58z24gLgig7llaRgvjIAP15607L/0FHn30G2p24RTAPlAHw/PfIw2lje\nQBf377T75OGlKgOEmy8wB76EUzAkk4mj6Y44IuNBO9NMeWuyKXyEczwhAIy0\nClMMNA2AIGoIBYmd3OOn0Wy7DGEr0VGLfEokwmzPLtu5E6QUkhlNa5EJQv9f\nyT3Q+vTxe/VsOMYFLyzJdXSo472mpSECau0W3Sre4ljMf6Zw386FCR7uWG03\nrUfzc6TDhkLWQBFYglbvOQ1AANdEmLXR9k0Gf+kRVm4cdlvV6EYeqrjqj203\nBqiJJ9MiDP5g3v4ol0dLN1HwgpkTH8TGsxlkdBb8mxVJiu+Xippp0T2zM1qK\nMwwICHod+pqlP0RSn4n4OVacWE78O3EgTTta+DD2musaBZFl5uQ4tKGXdfxV\nXm6H\r\n=JS/y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "7b368004723b8d11d4d237ff0479b9edcfb41449", "scripts": {"dist": "mkdirp dist && browserify --standalone Qs lib/index.js > dist/qs.js", "lint": "eslint lib/*.js test/*.js", "test": "npm run --silent coverage", "readme": "evalmd README.md", "pretest": "npm run --silent readme && npm run --silent lint", "coverage": "covert test", "postlint": "eclint check * lib/* test/*", "posttest": "npx aud", "prepublish": "safe-publish-latest && npm run dist", "tests-only": "node test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/qs.git", "type": "git"}, "_npmVersion": "6.12.1", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "_nodeVersion": "13.1.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.11.0", "covert": "^1.1.1", "eclint": "^2.8.1", "eslint": "^6.6.0", "evalmd": "^0.0.19", "mkdirp": "^0.5.1", "for-each": "^0.3.3", "qs-iconv": "^1.0.4", "browserify": "^16.5.0", "iconv-lite": "^0.4.24", "has-symbols": "^1.0.0", "safer-buffer": "^2.1.2", "object-inspect": "^1.6.0", "safe-publish-latest": "^1.1.3", "@ljharb/eslint-config": "^15.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/qs_6.9.1_1573195595196_0.762870404228227", "host": "s3://npm-registry-packages"}}, "6.9.2": {"name": "qs", "version": "6.9.2", "keywords": ["querystring", "qs", "query", "url", "parse", "stringify"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@6.9.2", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/qs", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "dist": {"shasum": "a27b695006544a04bf0e6c6a7e8120778926d5bd", "tarball": "https://registry.npmjs.org/qs/-/qs-6.9.2.tgz", "fileCount": 20, "integrity": "sha512-2eQ6zajpK7HwqrY1rRtGw5IZvjgtELXzJECaEDuzDFo2jjnIXpJSimzd4qflWZq6bLLi+Zgfj5eDrAzl/lptyg==", "signatures": [{"sig": "MEYCIQCABG1+BtMZ/76J6mskwBf5GNIUlW+L/fbem9JnDz8Z4AIhAIuNnslAp2Nu7nZCCa7zLqhokKYgHpeLMSJpbjlOZU8T", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 156197, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJed4a2CRA9TVsSAnZWagAAkdYQAJD//C2bHvQBOCI2jdDA\npeApeGwW1ZNW0Spp5jUZDDBE5Qje1BAu34vO/gwUF74YHDi/4ahNrbS/nr8R\nf+gjma8ixf++yFYAX/OO1G8Mik/vrqDtfH095uptBmiX0pMzTQkBYjYoCcW0\nyCP9MZUDq0yJ0o3u/t5iI4Xnnc9UEfi8P4H641zzRYNMpIzxUHwTgjIugevI\nX7LNTJABc7x+Ge75GSHhTCEyzAzFXJA7DJNQJDWyx0zb5wQQHl49ebtIdvhc\nHlJIH0xzFFiptAGBSdqeU3CRIBoqSU+0yPPZWtJZTE0J+D66N5flBVeKvQ7w\n4rI4elSmLXazBqMELaccr3ubQ7KTXTzfoNeUlCFu0joKOt7KBI8bNIHY2AtY\nfnB5UgdC7W5/cdphxJ9MdYafplU6/21yenAODpt6Z9Hzt2nZGFAjQnpyUPoQ\ng8owsezO2gJ4+egEmRlMaUIpop3Tnyll3Ga2vGMYIp4DdePvdsm5kvtZwPnK\nVZUx1YWJOP5m19tjgNWrNqk3ov8suRCnl4BRFG0LJbMde6/ebHI+m+RZeCG2\nG/zKEEK+a4YxGsHUq+UhetA6ZISDlZFwWqjvBuq3/OfctTeRaAtp+8CbbwKu\nyWEhfYNQ30m5Q4pKKmfM+YYotuW13uwOb75CGTlhn0nhHvRYLf/yHTKd3vOb\n80dy\r\n=2eTK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "ddc1ff9ca16a4b8963d7cf72d0a881732e54b8c9", "scripts": {"dist": "mkdirp dist && browserify --standalone Qs lib/index.js > dist/qs.js", "lint": "eslint lib/*.js test/*.js", "test": "npm run --silent coverage", "readme": "evalmd README.md", "pretest": "npm run --silent readme && npm run --silent lint", "coverage": "covert test", "postlint": "eclint check * lib/* test/*", "posttest": "npx aud", "prepublish": "safe-publish-latest && npm run dist", "tests-only": "node test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/qs.git", "type": "git"}, "_npmVersion": "6.13.7", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "greenkeeper": {"ignore": ["iconv-lite", "mkdirp"]}, "_nodeVersion": "13.11.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^5.0.0-next.5", "covert": "^1.1.1", "eclint": "^2.8.1", "eslint": "^6.8.0", "evalmd": "^0.0.19", "mkdirp": "^0.5.3", "for-each": "^0.3.3", "qs-iconv": "^1.0.4", "browserify": "^16.5.0", "iconv-lite": "^0.5.1", "has-symbols": "^1.0.1", "safer-buffer": "^2.1.2", "object-inspect": "^1.7.0", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^16.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/qs_6.9.2_1584891574201_0.7623260847733633", "host": "s3://npm-registry-packages"}}, "6.8.1": {"name": "qs", "version": "6.8.1", "keywords": ["querystring", "qs", "query", "url", "parse", "stringify"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@6.8.1", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/qs", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "dist": {"shasum": "65912fcd52e194951986<PERSON><PERSON>dd<PERSON>cbbacd9c23059", "tarball": "https://registry.npmjs.org/qs/-/qs-6.8.1.tgz", "fileCount": 20, "integrity": "sha512-9dsXTOm8lzTvKIK3qv6P+5RTyADROs1juUsJ4SsPG50o3I7eTqzLXt8FuneLMuD8pcfy+jO9Qrbd7kjINfyXVw==", "signatures": [{"sig": "MEUCIQCPmKTJukplxLRFHa9sOEKgOqrqY6w6RvjsCw033uE5PAIgBrJx4mVct9rcoYq62xf9Kct6sSWRr71eohX58MglgOI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 154171, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeeZD1CRA9TVsSAnZWagAAqZAQAJQ2czj3M2yy8g5rPFzD\nbZwx2y9fnhj9SzM5W3KPxmD3yOyJrKpi5tRxy/gxDfBsP0n2F3+ZTTFA2NBw\ngZDWRw6Yi4HOVyO//b5tyZ6YMzlyEpD+HsKVGiz2F844x143EEYRmYrZpTZ5\naWMwS5HgngC/FBFaZr8RhMQmIvSb63L1IO+EDZgdUxodn4B6X2wxvGXq0Cld\nJiT//gWbB/R8UWb051TF1T9hEozR1+9CcAff7Cq3tzw/eu7Gz/Y8DUhBfHPo\ns7UdxUhLzylqax+nM7E/JPh2XObqBQEO+FTLL+rMMWlbk237O+sKQpPO7IbL\nHLo3F9GScW1eHKusb3s/uQja8nrvroHKbVyYNtmia3UIHQpXfDkLj5IzzIDY\nZzNP9DpUPS5KCZIiuJud5o7JX0mTjH+/ImjLXb0uIwvkLqR2IxT/poeKRoYA\nhFPGbVLpGKsDLwD13zx5AhRlUaho7HoyHE2cGT24Sux4HVXQzsQPKLzA4VLp\nO6xmVWmzsRafGr3W5sMIcuG49KX2uOs3X1CRKdB7ldwbwpZLlSGW8xt43RFn\ntC1ixa/0x6GXxlAhLwlClsmG7ifUqRPAeZ+EXsoSfN4ulta+qHqn9iY3aUwL\nOGofo3N4XjPGD4et5RKiPdyRfzib7mFuiO6YDtn6OJRiOv/GVEoyopRDZREv\nzCU/\r\n=ILTl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "b57d12a37d20d13f34ce1abaf749fb5357976742", "scripts": {"dist": "mkdirp dist && browserify --standalone Qs lib/index.js > dist/qs.js", "lint": "eslint lib/*.js test/*.js", "test": "npm run --silent coverage", "readme": "evalmd README.md", "pretest": "npm run --silent readme && npm run --silent lint", "coverage": "covert test", "postlint": "eclint check * lib/* test/*", "posttest": "npx aud --production", "prepublish": "safe-publish-latest && npm run dist", "tests-only": "node test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/qs.git", "type": "git"}, "_npmVersion": "6.13.7", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "_nodeVersion": "13.11.0", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tape": "^5.0.0-next.5", "covert": "^1.1.1", "eclint": "^2.8.1", "eslint": "^6.8.0", "evalmd": "^0.0.19", "mkdirp": "^0.5.4", "for-each": "^0.3.3", "qs-iconv": "^1.0.4", "browserify": "^16.5.0", "iconv-lite": "^0.5.1", "has-symbols": "^1.0.1", "safer-buffer": "^2.1.2", "object-inspect": "^1.7.0", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^16.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/qs_6.8.1_1585025269126_0.43376637224066505", "host": "s3://npm-registry-packages"}}, "6.7.1": {"name": "qs", "version": "6.7.1", "keywords": ["querystring", "qs", "query", "url", "parse", "stringify"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@6.7.1", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/qs", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "dist": {"shasum": "856b51bab68c7228db9b26f8339b84bad935877d", "tarball": "https://registry.npmjs.org/qs/-/qs-6.7.1.tgz", "fileCount": 20, "integrity": "sha512-1wsZD0q8V7+olrPgA3LN04JXXJbvv2p8BAwIO06w+4ITT7xVOG0+5tAC18GrGPLvhr0tGN+jtoHs7mDa+yQVlw==", "signatures": [{"sig": "MEUCIGQzie1CYoZhsrKKww5U/wqvRgqMEBvfw6hBZWpljktCAiEA3Bdz6TfQlmHYZE9+Bf9SyvEyq6ERcPiepkMLTkrmJzk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 151905, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeeiZjCRA9TVsSAnZWagAAI6IP/iuZvjvvBtyvlZJ7iTk6\nhevuH7NbhujgMD5PfTGiJ6t8oh19stOzTWbitLD7OOtCHSV2kId7uORhIv/O\nIt41cumdro/O51R7JmJ65lg7U7tSnikamPxF1pawlXtvO9YcXTVgGMwiduIc\nOcssJol6d6oIn9I30WtrWQ6LOwvCnjzOw6Eow22J92pJhJ6MHRMfb1EAVsYB\naCoXu9Ff9LnFX8Np5KROT05jiWHAsdbPjua5bteaqVnBOMtnTFzNkr/NCr16\n9y9aYdaUChBz7Ekb0ElPS4VGTeLHsrYOXaVNAWlLG43ivFOowDQyVbeM7dt0\nQ4/FRY8mOl2ZO0d45pCk7XFvwbqAoLDx+OPvYrqcbagihA1uVj5R1nVv9XxE\n7lIda/nQGB5xFWdiUj+L+zA5CPVzyxSFKLYNfWmULwoF8MjBf0zBXjAJPW5e\nKel71ggLlGmPs+Lg2affE13nVvo9gNbQWFHTHvNrYlue5Mb88OABJhTmLJA0\nU0JR75uNd/voLh/BIIxU6TzgikKsbbf1Zlt3GrhX/gTCnwW5/QDlK1kA8OXP\n/bLRKS30cgyGV0FYmAPK7Q3uWWjZ7rZOSz2WbTJMuq+bzzYI3mlfIbM4LRy2\nW0TqbCGx2SaHM8/FGRq95wVT7jRQCHyno1vvI2qWxPkKiNN5BRvUORFYx3wJ\nGY4q\r\n=/MzM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "92f97f25e40bcc7cb3396ddc2ea813bcac0b4dac", "scripts": {"dist": "mkdirp dist && browserify --standalone Qs lib/index.js > dist/qs.js", "lint": "eslint lib/*.js test/*.js", "test": "npm run --silent coverage", "readme": "evalmd README.md", "pretest": "npm run --silent readme && npm run --silent lint", "coverage": "covert test", "postlint": "eclint check * lib/* test/*", "posttest": "npx aud --production", "prepublish": "safe-publish-latest && npm run dist", "tests-only": "node test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/qs.git", "type": "git"}, "_npmVersion": "6.13.7", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "_nodeVersion": "13.11.0", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tape": "^5.0.0-next.5", "covert": "^1.1.1", "eclint": "^2.8.1", "eslint": "^6.8.0", "evalmd": "^0.0.19", "mkdirp": "^0.5.4", "for-each": "^0.3.3", "qs-iconv": "^1.0.4", "browserify": "^16.5.0", "iconv-lite": "^0.5.1", "safer-buffer": "^2.1.2", "object-inspect": "^1.7.0", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^16.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/qs_6.7.1_1585063523259_0.4984773028048559", "host": "s3://npm-registry-packages"}}, "6.7.2": {"name": "qs", "version": "6.7.2", "keywords": ["querystring", "qs", "query", "url", "parse", "stringify"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@6.7.2", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/qs", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "dist": {"shasum": "3d55fb505cc82dbeb0f6125ddad0921639aa12ef", "tarball": "https://registry.npmjs.org/qs/-/qs-6.7.2.tgz", "fileCount": 20, "integrity": "sha512-pIxa8oGRlwdn9r+PfxMWAa27MPUvZV8+FU9m7VKkC9Ppe0Mi789ge1lDwqYiMFb3ZS7SBZMtII9dTs0UGqHvNA==", "signatures": [{"sig": "MEQCIBhyMTGv8eId2eH8+9OoTAaz90/EVPX77sVjXyW7Op/6AiBoIss+jjCrv61B95tcsEYd4lxp7XmHtKG5Ig4foZuwSA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 153474, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJee65DCRA9TVsSAnZWagAAowoP/01y9cbrRIq5qcpaz4F2\n2QbTK0gkTI1XdvoLBAv9nbrzlYJx5r8frVXWNNk9HlHst0ZjMewaWVYX094t\nz5Yga107k06Eo60eGnV1X6Wkp4C+Lcp1g1EOnIPjQnOiXUAfmAAV8zWoIoZy\nWqUYNd5ShIBT0FYxHd20k4SVTSF58ANVWBJHo/YqycSdZ5j1hrmkMYuukTAR\n98GbzEh3nbTrxCDDcoGASo2oMBht6NUwrBa5VXZTZb2qdr51zXpA5uHGZO70\nK96p6D+zTmpYLHJSJniBpIrr4c1nFLtDQk8xbBOYU9VITsY2aoW6TQi131cx\n/DcF56ygOmxvjwpUQ9E/DXL1r0OJOqpCHC1CliVg8UtqdoX8Uk9B2GyNiVBr\nG9JppBsTtda4DlfyHdt8ZN/0w6sT2VCyZKjKSaeQRjtAZjcQWdKicbEyhqhp\nLkEJgvJP8pqwCGnNOw0LmlX4xGHhnrE/9yY2DmUF2PE6QarUUI1+8riOIKVf\nagZW1aCBvA16FgitJ7EfAVn00ane3+h/1NH+CJ/j/ckhyCWlPtF0klQPVHFS\n9bUvVw/ZK4pUAqvhqDt7AiaOrqn9XQL29YdzH4XL21k4cXxtNJwBRtTfs7IL\n6z/AYuyrclvvs00Ggijn7ZIpdLmkeySGxWRciR+NvA8SPujNSEOksqh73lv8\nY7ml\r\n=FGRt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "bf93c5719c88e4c7ace17d747c62844988a65018", "scripts": {"dist": "mkdirp dist && browserify --standalone Qs lib/index.js > dist/qs.js", "lint": "eslint lib/*.js test/*.js", "test": "npm run --silent coverage", "readme": "evalmd README.md", "pretest": "npm run --silent readme && npm run --silent lint", "coverage": "covert test", "postlint": "eclint check * lib/* test/*", "posttest": "npx aud --production", "prepublish": "safe-publish-latest && npm run dist", "tests-only": "node test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/qs.git", "type": "git"}, "_npmVersion": "6.13.7", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "_nodeVersion": "13.11.0", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tape": "^5.0.0-next.5", "covert": "^1.1.1", "eclint": "^2.8.1", "eslint": "^6.8.0", "evalmd": "^0.0.19", "mkdirp": "^0.5.4", "for-each": "^0.3.3", "qs-iconv": "^1.0.4", "browserify": "^16.5.0", "iconv-lite": "^0.5.1", "safer-buffer": "^2.1.2", "object-inspect": "^1.7.0", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^16.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/qs_6.7.2_1585163843069_0.512144729683921", "host": "s3://npm-registry-packages"}}, "6.8.2": {"name": "qs", "version": "6.8.2", "keywords": ["querystring", "qs", "query", "url", "parse", "stringify"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@6.8.2", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/qs", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "dist": {"shasum": "c5453e62497c53a4b6b2817b4c724d882b74d7f5", "tarball": "https://registry.npmjs.org/qs/-/qs-6.8.2.tgz", "fileCount": 20, "integrity": "sha512-WhVrQcdQbsjtovkTn8ODmBJrgxV98Rq1XUCPC6GRwGG4GUm+gc2d+e4ENoXBvNeei1u+vTS7VJ9wlA7nwQE85Q==", "signatures": [{"sig": "MEUCIA9PMX7Uf8VS0AzDNvoCL6pLJn5qNh6vN2CXobI9WED2AiEAqtfgvSfMauFa2ah0QtqfOd1OBz3Z2V0aN27AIg7K6pw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 155740, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJee7VNCRA9TVsSAnZWagAAOH8P/14qAzLqS2PLaRxf90uJ\njZjM3srImLrRAovsHjVjVLEIyMdbsBXmyjzAyOJoeHSS5VSUEx9+429tiS9/\n5uxPCCoAPl0rhsPQ1zRxLXJ/iONd8Uhnbd/rOUWQGjVKawiS9h6tAEmlgf7g\nmWjTud430q6cm1ticMEUPVhQvrh40fZz8CLrdRk7oAyxlWfQQ6ny3otEo9cH\nTEyCn7NoE3Zknie49oHRH4lfWVnU3jsBjwUVDEw6jDsj+G3FKEUWyfh4jWbr\nBQhvROrourN3h2NY56CcsU3xNqd4Me/K2jD1KYLxf7pdyrIM5qvz4l/7TwNv\n2Upjsj+IHPbt0DF+d0CwPfvkrK0BjlRjtzKfiHRkf5nhUvuaJlDjybMLmEAz\nJv5cUuAR0/lfUsdC+37iIZ5AW4NMJNMXgMbrv53JPy+YAoBQrVSk/u7WFwmf\nVulnt/EVqo47kRwzDopekPZoF8iL/tlSnOVsyJxR8mzrvYIwr4/RrJgnwqoW\nDbVQexWyJRVVbHSUTeSQlPrExbf7jGnzc27FwkUJlhInAD426XH0tU2S/MSo\nwqdVn56+a0T/NmeBkz2bFIlSD7m9PMjrVwOj85RTnfsAc8BSXo1n5MHwepCg\nh4tfPBcJdPvOTGNP7MS5USCBhpSbkrFRwbkd7Z9JO0GKA1TGbbZlLl/5s/BA\n9Gf2\r\n=cTie\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "808b0b233e9408e0b5a7d7d19eb01093944240c5", "scripts": {"dist": "mkdirp dist && browserify --standalone Qs lib/index.js > dist/qs.js", "lint": "eslint lib/*.js test/*.js", "test": "npm run --silent coverage", "readme": "evalmd README.md", "pretest": "npm run --silent readme && npm run --silent lint", "coverage": "covert test", "postlint": "eclint check * lib/* test/*", "posttest": "npx aud --production", "prepublish": "safe-publish-latest && npm run dist", "tests-only": "node test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/qs.git", "type": "git"}, "_npmVersion": "6.13.7", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "_nodeVersion": "13.11.0", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tape": "^5.0.0-next.5", "covert": "^1.1.1", "eclint": "^2.8.1", "eslint": "^6.8.0", "evalmd": "^0.0.19", "mkdirp": "^0.5.4", "for-each": "^0.3.3", "qs-iconv": "^1.0.4", "browserify": "^16.5.0", "iconv-lite": "^0.5.1", "has-symbols": "^1.0.1", "safer-buffer": "^2.1.2", "object-inspect": "^1.7.0", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^16.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/qs_6.8.2_1585165645200_0.5305560390651123", "host": "s3://npm-registry-packages"}}, "6.9.3": {"name": "qs", "version": "6.9.3", "keywords": ["querystring", "qs", "query", "url", "parse", "stringify"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@6.9.3", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/qs", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "dist": {"shasum": "bfadcd296c2d549f1dffa560619132c977f5008e", "tarball": "https://registry.npmjs.org/qs/-/qs-6.9.3.tgz", "fileCount": 20, "integrity": "sha512-EbZYNarm6138UKKq46tdx08Yo/q9ZhFoAXAI1meAFd2GtbRDhbZY2WQSICskT0c5q99aFzLG1D4nvTk9tqfXIw==", "signatures": [{"sig": "MEQCIEBHZwBrMXo3WpY23io46Q7LBtcS3G1DDAI97thHHnDmAiAwSOCt+AsGuL/esoQpWRNXbGMfr2u+gUj8arHCWmTimQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 160076, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJee8CtCRA9TVsSAnZWagAAzgAP/j8WUxYBAFoHVbFRqK26\nMxv117JaEAH0GBBEdEgAtFKPdEVhLYzzTj+QBA4eZHqrB1uy9LdeIRzIhAr7\nVTReiBWHmQbs+kxRZuL5TBESsDfZzvF5XemV7O0kVar1DbsXM9IF4vW6+bCR\nLVXtJ9KehqkNO5aug05hC4jYZ3nSDNZpTNBYHIbHsKDNSqrsnlbChcI+3EwH\n9txs5lANhtXE5iHsnlWoVuY8A6cR885nIDimBdajLPFOTg1BnQDBF3gGEOeX\n/HYQLXy+2wSiR337mwlZw+W6Ru92uTFDg1HPgCN33/P1bpXg3l/sEWHh3MHa\n0Ngt2bDwt6pGHm716YV7p0aa8t82cTTyfFF1K4AcSeSOct0EOaj+kWMtSfqI\nacr6UFkl9gaPUal9MC+ZzU2961FUSeDkIHWLkDWHigEiM9l6y20lT302Cxj6\n4pE7qw9UKn6s1+BaBz045p1rOGgLnrNj5dIjlwpAWCzIw8q9q/hkauN7V9QN\n48gsVAWVCz8ml36L/KSuJJ6pVIjBDUywjelZKOCsmnUxS86qwtWy5QwzxAzM\nDQaZhxoJ53i2l37TexZOsVODlAdtj1lP9uEsbPVYcZXq29tzzDHr8nkHeWIq\n4KDsmmbSQaOTc1fWp2chvqRAC0mXgYrhWVVu4o7VwzDja+nvjIjR+h1mdnwS\nbzyM\r\n=edBT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "511e1c9e527679ca182b055895ce301ea82213ee", "scripts": {"dist": "mkdirp dist && browserify --standalone Qs lib/index.js > dist/qs.js", "lint": "eslint lib/*.js test/*.js", "test": "npm run --silent coverage", "readme": "evalmd README.md", "pretest": "npm run --silent readme && npm run --silent lint", "coverage": "covert test", "postlint": "eclint check * lib/* test/*", "posttest": "npx aud --production", "prepublish": "safe-publish-latest && npm run dist", "tests-only": "node test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/qs.git", "type": "git"}, "_npmVersion": "6.13.7", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "greenkeeper": {"ignore": ["iconv-lite", "mkdirp"]}, "_nodeVersion": "13.11.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^5.0.0-next.5", "covert": "^1.1.1", "eclint": "^2.8.1", "eslint": "^6.8.0", "evalmd": "^0.0.19", "mkdirp": "^0.5.4", "for-each": "^0.3.3", "qs-iconv": "^1.0.4", "browserify": "^16.5.0", "iconv-lite": "^0.5.1", "has-symbols": "^1.0.1", "safer-buffer": "^2.1.2", "object-inspect": "^1.7.0", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^16.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/qs_6.9.3_1585168556886_0.5838765005488886", "host": "s3://npm-registry-packages"}}, "6.9.4": {"name": "qs", "version": "6.9.4", "keywords": ["querystring", "qs", "query", "url", "parse", "stringify"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@6.9.4", "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/qs", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "dist": {"shasum": "9090b290d1f91728d3c22e54843ca44aea5ab687", "tarball": "https://registry.npmjs.org/qs/-/qs-6.9.4.tgz", "fileCount": 20, "integrity": "sha512-A1kFqHekCTM7cz0udomYUoYNWjBebHm/5wzU/XqrBRBNWectVH0QIiN+NEcZ0Dte5hvzHwbr8+XQmguPhJ6WdQ==", "signatures": [{"sig": "MEUCIB9k/5UWnHhnSGv33Z/luE3wpaBizXqCb0v//OQZ+HN3AiEAgij5e+XjLsc58utzJK8+m8EdKr95EB4OQW0P2BBMbaQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 160179, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJerz0zCRA9TVsSAnZWagAA+DkP/1nM5P1n2uJIBWozU5L3\nMGXafvAilkEQzB7WnXMkr/Kp+R/tow4LanUNf3why/IZoSfZrSb15tAXxBVO\niO/NWkAK7KWSsr3xYgDyjCwGtrRLJN9IIVrKdwIj4F6MWQSbhVP/ZoJgVBlE\nNXe4UOpYdla6mnEjwEW9yvTgV9461/ovogv7XSFWkG5wtf+debWZFUKnIjVk\n9JvP6ELLthwSd7lVWz/CiQ1i0wYcxARJEsxBXjtyfM+nWXBlfa0Kww3lKZUo\nqiITMou/759dIU1nev6z/VoVXu8KOtlje6BdWaroNbnOgKtNkLUCuftIjARJ\neuGz56IrCXegfeRbT1qMqk8GD/fgOcfojhsaOfH1yChw+Oo6KVCok6XLpJ/T\nw4SIntm0xcfHmwZNCd2NtH6edA2+Mso9qb3AmXkHTjpgWQR+zPypZzXExczs\ntkVzCL6205vPXkVnpT2FPjZ9kfV6kJxur1y3GPDEt2JDB0ChPeeBhzXT8ban\nGsesLmVhUEQ9baq0rBfh7X5GzNE5K7CT/1pMFb54El/+mc9GqIoBAWmq2rZJ\nMjbk0SU+KGBlaM1QzjVj94SY902byZ7VINNm50n3CwXqDsPJNLcfl8jSrNJW\nipGANtf+9m5YLhFLah7BdthCd5Qu2hoNyyl6c7AbQsTArmDvIO3wv/TZ1VzO\nfwuJ\r\n=y1jo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "8e014a7b1749ccec52104121950e7b0d251caa78", "scripts": {"dist": "mkdirp dist && browserify --standalone Qs lib/index.js > dist/qs.js", "lint": "eslint lib/*.js test/*.js", "test": "npm run --silent coverage", "readme": "evalmd README.md", "pretest": "npm run --silent readme && npm run --silent lint", "coverage": "covert test", "postlint": "eclint check * lib/* test/*", "posttest": "npx aud --production", "prepublish": "safe-publish-latest && npm run dist", "tests-only": "node test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/qs.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "greenkeeper": {"ignore": ["iconv-lite", "mkdirp"]}, "_nodeVersion": "14.1.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^5.0.0", "covert": "^1.1.1", "eclint": "^2.8.1", "eslint": "^6.8.0", "evalmd": "^0.0.19", "mkdirp": "^0.5.4", "for-each": "^0.3.3", "qs-iconv": "^1.0.4", "browserify": "^16.5.1", "iconv-lite": "^0.5.1", "has-symbols": "^1.0.1", "safer-buffer": "^2.1.2", "object-inspect": "^1.7.0", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^16.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/qs_6.9.4_1588542770614_0.8554103980636278", "host": "s3://npm-registry-packages"}}, "6.9.5": {"name": "qs", "version": "6.9.5", "keywords": ["querystring", "qs", "query", "url", "parse", "stringify"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@6.9.5", "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/qs", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "dist": {"shasum": "45859f731392198fbd3f91ea4290e7c4e25232fa", "tarball": "https://registry.npmjs.org/qs/-/qs-6.9.5.tgz", "fileCount": 17, "integrity": "sha512-T0SnbxGiMcB09qd3bFcPt8rufxPs7T7TjePk33r1WsJNt12/rWsK/ofKqRHQ0rY/iMGE0mVdkc6Yg9CuL/ty0Q==", "signatures": [{"sig": "MEQCIGLEnIgVENqyFqsEO9JJc8Wbyoi4gY5ZWsPWNt3mlhorAiAxUpQDg39qa9J4+5V1ROkrQpDzBZLd9x1PD1+GdFPM/A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 138396, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf/x//CRA9TVsSAnZWagAA7e0P/jUY6rNj3cJ/KbgFvcbu\nTWmaQdAtJWswMbLV/zmnbt6LKI0oNx/ufrsYJ1AaciXLX9oQFGQtMGsbxGLT\nzPQYNO21GAB71bVZTHvftTwJ903UbK+NhFdGHQ/X+CCwoUJoVoShCBhuJmcd\nOOl+C32qMl4H5Ffvc7pHpUkWPYxvNUR4gL3LcSTqCEZOu6m6ai2k1L1av9uO\nc1SsziRToLEzjxrWgUIkKc57KjBFXy07N9vw8xTtteBhHbO8aQVAqb+MqPB+\nGhPxL+el0JlmFcDk1pMJHGY7PAGbTTON+YA67Ede3j3Jgs31Dv18znkprPd+\nHiE849YoMvLw8LJVdmS9cbCNV60i4PUE4F1JHRCVuK2XhtoGHfnuH8NdTXwa\nHepEGhZZx8KlAA2ArmB86zlCdf98Hc8sCEQivR7CZ158+TSbLnbtr2tzurRy\nOpFuRWi31Gk5fXr84IzVK6epBKhhMaUnnjVubd2X/p6+45q183kKUG+2Y6Eo\nfaqeu+x00Fge4woETzNOMbIxo8LLfp5GwAykjLJSYV/s7TasIhMoaNMee/+d\nPcV01sMBtczeTmxxXyGe8QAuPmrPG6sMKlScbliqObVMxfFDHhxBZdWqYu7D\nPW3S+PXxMXxKuzPk9ecJ7k9SYLOzpAo2daZxqzGJpY34csR9MeZCjVGvCa8R\n9Zkw\r\n=3Rwm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "179fafc920123e60466a1729f9f2b43b2fd67212", "scripts": {"dist": "mkdirp dist && browserify --standalone Qs lib/index.js > dist/qs.js", "lint": "eslint lib/*.js test/*.js", "test": "npm run tests-only", "readme": "evalmd README.md", "pretest": "npm run --silent readme && npm run --silent lint", "postlint": "eclint check * lib/* test/*", "posttest": "aud --production", "prepublish": "safe-publish-latest && npm run dist", "tests-only": "nyc tape 'test/**/*.js'"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/qs.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "greenkeeper": {"ignore": ["iconv-lite", "mkdirp"]}, "_nodeVersion": "14.15.4", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"aud": "^1.1.3", "nyc": "^10.3.2", "tape": "^5.1.1", "eclint": "^2.8.1", "eslint": "^7.17.0", "evalmd": "^0.0.19", "mkdirp": "^0.5.5", "for-each": "^0.3.3", "qs-iconv": "^1.0.4", "browserify": "^16.5.2", "iconv-lite": "^0.5.1", "has-symbols": "^1.0.1", "safer-buffer": "^2.1.2", "object-inspect": "^1.9.0", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^17.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/qs_6.9.5_1610555390233_0.7725320302686958", "host": "s3://npm-registry-packages"}}, "6.9.6": {"name": "qs", "version": "6.9.6", "keywords": ["querystring", "qs", "query", "url", "parse", "stringify"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@6.9.6", "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/qs", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "dist": {"shasum": "26ed3c8243a431b2924aca84cc90471f35d5a0ee", "tarball": "https://registry.npmjs.org/qs/-/qs-6.9.6.tgz", "fileCount": 18, "integrity": "sha512-TIRk4aqYLNoJUbd+g2lEdz5kLWIuTMRagAXxl78Q0RiVjAOugHmeKNGdd3cwo/ktpf9aL9epCfFqWDEKysUlLQ==", "signatures": [{"sig": "MEQCIBfx2o9FI3sGfiApjJR8cELYQp6mWqPr68zDczDdwLl6AiBT+G9vrClb2a/DHmlyEMVyXalFjlD6y9Wx0xp9HLt/RQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164609, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf/85dCRA9TVsSAnZWagAAyw0QAJ6sJMP7p8ypPut03JtH\njh0RjokA1Gau10OUB7jmAb6aKd7YAsFWRrHuqr5Rj4iMj3mAd+bjeSOOn5+k\n9q6VxR/PkpDpPCabO1bKkGf4muLElJuKLdTNJ+1tRO46xDm9r8/JKeQlE1P3\nKQyVcP58Fj5OyLuyz9I+vWMF6mY1AwxYR97CZxry9S6dSPBATXD/ka50Veka\nnNw6FB9JGTLkjl1R7Iw4nql0gGpNqE4/ydRK36hluiWIj13x9VD3lJ49mdTr\n+xI3TI52ElMEG6Rh1NkfEwzf+lBOp4/INbu9vAEziaBtSFRw+XctTtXhWrYu\nSX9bJdr8MVewkuxerj3sTxWN/QMABNSdwXRGO8WrvDOh/hbuCIO42B2/4ogX\n7IMC/UOKwAJxQmodHWzRvdJvFXytQsuw+bowz7zVorfMyI0aXzbPOG8GxKx4\nZxTdIKzyLNSSRRZXw7vd/+xcByyd0PLD7ABxpuFPOJhqAzsHsyVca81vMNUb\n/COEO8PSXr+6MlrIer3lO8qYx5c8NBbwARKVRQcnI4uCWCNx/6Vm19sNWm4f\n2sXXpFHusTY/mDXmISVTv6eFoM5EVBH1mU2w/jhcra9YzJcuIZM95/BZ+brM\nysQ7OLdl1MtQTmfU1D8Mv9sfxbQ55h3hW6R/nXDiIeWsq8HYkYl0m3b/M07D\nsLBw\r\n=sS4C\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "b522d2e9993a47afd810ed9a19d35aadb6323988", "scripts": {"dist": "mkdirp dist && browserify --standalone Qs lib/index.js > dist/qs.js", "lint": "eslint lib/*.js test/*.js", "test": "npm run tests-only", "readme": "evalmd README.md", "pretest": "npm run --silent readme && npm run --silent lint", "postlint": "eclint check * lib/* test/*", "posttest": "aud --production", "prepublish": "safe-publish-latest && npm run dist", "tests-only": "nyc tape 'test/**/*.js'"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/qs.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "greenkeeper": {"ignore": ["iconv-lite", "mkdirp"]}, "_nodeVersion": "14.15.4", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"aud": "^1.1.3", "nyc": "^10.3.2", "tape": "^5.1.1", "eclint": "^2.8.1", "eslint": "^7.17.0", "evalmd": "^0.0.19", "mkdirp": "^0.5.5", "for-each": "^0.3.3", "qs-iconv": "^1.0.4", "browserify": "^16.5.2", "iconv-lite": "^0.5.1", "has-symbols": "^1.0.1", "safer-buffer": "^2.1.2", "object-inspect": "^1.9.0", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^17.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/qs_6.9.6_1610600028510_0.3565787797505109", "host": "s3://npm-registry-packages"}}, "6.10.0": {"name": "qs", "version": "6.10.0", "keywords": ["querystring", "qs", "query", "url", "parse", "stringify"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@6.10.0", "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/qs", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "dist": {"shasum": "8b6519121ab291c316a3e4d49cecf6d13d8c7fe5", "tarball": "https://registry.npmjs.org/qs/-/qs-6.10.0.tgz", "fileCount": 18, "integrity": "sha512-yj<PERSON>OWijC6L/kmPZZAsVBNY2zfHSIbpdpL977quseu56/8BZ2LoF5axK2bGhbzhVKt7V9xgWTtpyLbxwIoER0Q==", "signatures": [{"sig": "MEUCIDmB9qfhtiSiWuheCQzshwy/XEcWgu6ZhaIyU9ORHd8CAiEAgWVt5woavZSM97ooJR6lWEKA/XnCcx6wrrFSjdX3dNk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 203463, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgU6xmCRA9TVsSAnZWagAAjCEP/05hpdpxqGnV5DIx1pUj\nd4BEnebzi8kJZ8qAVjpKIG4UFUguZ2bglT1x9VDTGe8qi0JLfz34qo6vcbWl\njrlN/jSIGQaxaLyygdceTyacMxG0vcBhNPWaRQiROtTZyuPCxSxhNSuSoTNh\npmWagOCFVIEPVBQ/33PfM6uQ7+BFfvHXzx4Xh9QfLL9g0lPWMpYIOWoy8KLg\nf8Dz1FIpBEqwFAd0zqAjikOQUbSBHmVAseNk+bDoCwQyLSmUwUZ5QGPjv6Wg\n4aCjTInBptLueWQpF2Q67JJH4iq7jeA1NCfcr2Uvzg0OtdN/GHM+IurPZ0YC\nOV5/mXtYgzVJVgLudY4TNVTDsOMIBt815r11mRtJKLlcB8zH+gtpQvSTWD7g\nmUtxVZip5nKdyxjo3vYGr6cUJ8FYDkdOr7La5XJRUf6HoIk2WN5Q4LqGbpln\n7M2Rj5VSl8l5UFvH43P8eJ9ia+3ItMJ1JFDhHKMaUczl5m2104kA7EajLyGQ\n/ONj8Gwz08J3jORNPhCELpXgkJbLqIJZiIXJuR0w2oyH5GbZ3XLI+4M/xlsa\nhGEWLkQxnII/NOEyGMW+z7sa2JbDrq122SDEVdpzrX73F9HBvD3SxVORjIm6\nvTGIWL5c/YkXOzmiLUfSDwXamEEQxYnBOv//Zf6zGTYdjuCUykTxfs/fBHU1\njzml\r\n=Pj3s\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "7c1fcc53047ed2d7555910fbce9f72eed1e450b1", "scripts": {"dist": "mkdirp dist && browserify --standalone Qs lib/index.js > dist/qs.js", "lint": "eslint lib/*.js test/*.js", "test": "npm run tests-only", "readme": "evalmd README.md", "pretest": "npm run --silent readme && npm run --silent lint", "postlint": "eclint check * lib/* test/* !dist/*", "posttest": "aud --production", "prepublish": "safe-publish-latest && (not-in-publish || npm run dist)", "tests-only": "nyc tape 'test/**/*.js'"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/qs.git", "type": "git"}, "_npmVersion": "7.6.3", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "greenkeeper": {"ignore": ["iconv-lite", "mkdirp"]}, "_nodeVersion": "15.12.0", "dependencies": {"side-channel": "^1.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"aud": "^1.1.4", "nyc": "^10.3.2", "tape": "^5.2.2", "eclint": "^2.8.1", "eslint": "^7.22.0", "evalmd": "^0.0.19", "mkdirp": "^0.5.5", "for-each": "^0.3.3", "qs-iconv": "^1.0.4", "browserify": "^16.5.2", "iconv-lite": "^0.5.1", "in-publish": "^2.0.1", "has-symbols": "^1.0.2", "safer-buffer": "^2.1.2", "object-inspect": "^1.9.0", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^17.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/qs_6.10.0_1616096357773_0.0456838707513032", "host": "s3://npm-registry-packages"}}, "6.10.1": {"name": "qs", "version": "6.10.1", "keywords": ["querystring", "qs", "query", "url", "parse", "stringify"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@6.10.1", "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/qs", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "dist": {"shasum": "4931482fa8d647a5aab799c5271d2133b981fb6a", "tarball": "https://registry.npmjs.org/qs/-/qs-6.10.1.tgz", "fileCount": 18, "integrity": "sha512-M528Hph6wsSVOBiYUnGf+K/7w0hNshs/duGsNXPUCLH5XAqjEtiPGwNONLV0tBH8NoGb0mvD5JubnUTrujKDTg==", "signatures": [{"sig": "MEYCIQCmpuw03VdpHMc05zFzaB/viqI7BxQ0QNRNI/KtUbxGCQIhAPwO1Fi/YenDnUEA1+XepKqokBPvlwaqK12+7LdRKLB4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 204351, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWBTRCRA9TVsSAnZWagAA/04P/RZzBn2y0QZ9x7McOryS\nAT/shkBUfw/3wEPfWUGwe+aPHAOuuy8Kx2iJ62wcsLv7irZyrgD7wbgqKrq+\nKDtB50n7sAE4liyxfaUB5ZErsmh3exaNaH6cmmdn01g4fOhFjxzwpNRcPhZM\n3xLcWMsCJGUCGyOAxOouK0pdQOFd8q6gn3gKKZD7RIe5UsDoxWFKpGe0LLc8\nCiS67IeYS6tBxG1H4X+FKGi7a+xzJVlWXubshkpOtonv+VvrtFUHntFmRz2l\noB1yaA/hETLunu3GQEjutvKe/IKPTKwsR+/CCBTtdzk+MuoRZkuvZSFdodeZ\nAWYBmV7vKviXOoNKx+egVOiSY53Se2FziwhP/E/rRmBR5b7CB3VLki6ZG9eM\nvzjbmEhgD7n/UOqDFp7s3nf0LopC5LuZ5xpLLqfgXQakJx9JF69B9Z8LHeAd\nkO692CcQDMtyYDTn7tfTLg/W1HNbNAIONmLnvWBnQSl1Xfp7jJ0sEeyanSEZ\nZM6zEAw/T//KlCZaf5PLcC4wh6fGxds6asUHMMwnNbX5fkU0AC+Ps3bUnG8/\n/2auc7aCtYn8iSKbv2VDpyPnTgMMqX04/HCBLCfYMa+jlN3qb2tA2rZhAyn7\ndScCADE1pzso0k5dfBz8W4ihzFGFFvuWUgk4uG9/Ib28/mVBv7onssVrgBlk\neL0m\r\n=9KJK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "dd0f954e4c00b02915f4cdc3ee5174ebc351f1c8", "scripts": {"dist": "mkdirp dist && browserify --standalone Qs lib/index.js > dist/qs.js", "lint": "eslint lib/*.js test/*.js", "test": "npm run tests-only", "readme": "evalmd README.md", "pretest": "npm run --silent readme && npm run --silent lint", "postlint": "eclint check * lib/* test/* !dist/*", "posttest": "aud --production", "prepublish": "safe-publish-latest && (not-in-publish || npm run dist)", "tests-only": "nyc tape 'test/**/*.js'"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/qs.git", "type": "git"}, "_npmVersion": "7.6.3", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "greenkeeper": {"ignore": ["iconv-lite", "mkdirp"]}, "_nodeVersion": "15.12.0", "dependencies": {"side-channel": "^1.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"aud": "^1.1.4", "nyc": "^10.3.2", "tape": "^5.2.2", "eclint": "^2.8.1", "eslint": "^7.22.0", "evalmd": "^0.0.19", "mkdirp": "^0.5.5", "for-each": "^0.3.3", "qs-iconv": "^1.0.4", "browserify": "^16.5.2", "iconv-lite": "^0.5.1", "in-publish": "^2.0.1", "has-symbols": "^1.0.2", "safer-buffer": "^2.1.2", "object-inspect": "^1.9.0", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^17.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/qs_6.10.1_1616385232666_0.6098543358689268", "host": "s3://npm-registry-packages"}}, "6.10.2": {"name": "qs", "version": "6.10.2", "keywords": ["querystring", "qs", "query", "url", "parse", "stringify"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@6.10.2", "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/qs", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "dist": {"shasum": "c1431bea37fc5b24c5bdbafa20f16bdf2a4b9ffe", "tarball": "https://registry.npmjs.org/qs/-/qs-6.10.2.tgz", "fileCount": 18, "integrity": "sha512-mSIdjzqznWgfd4pMii7sHtaYF8rx8861hBO80SraY5GT0XQibWZWJSid0avzHGkDIZLImux2S5mXO0Hfct2QCw==", "signatures": [{"sig": "MEUCIDpEHdo3FU5Y36wrt6VeeIzO2pYhB5uWMDFBe4m7/ajIAiEAn7HsiCAlCU75a6VrPj0H9YJ/hcIX5S17fcr80Q7zSMg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 212118, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhraPvCRA9TVsSAnZWagAA/7oP/1zvgp6eVXvRWWjswks6\n0whFBZNdD0W82Jvj+k8GVj61NCMNMCMC0jJN207Hsk0+Zubo+D08GYtkzQFp\nH/Z30hqHQT4y6B5UfLwuVn91nqA4VIGNa2BN6IyWBkWNcla7zdSMI1An84Sc\nJeL5oApcTYh/f9/DdYA1d6k7gnEzwfZ2osnvSa++NErDMvwA4VoN6DIyYBt/\nosAF374UN21TdC5FgYT5+oE/H5dNxh0Mlp9DBoveMKIzoFLvDjvcMJ0mRCP4\nmQXNlGhQyBKdS9lrwPyAIP3Ew5LSTuAYu26qU4bw+7xN2DPGRFvntPoTgcOi\njDrLdv1XQ9dfdfoCeZ6Q/bMttCRl89n45ig9YPF/bC9gPxpbGQ45EYutmenN\niF02bt5iK/tEGmaTHz7TKFOXYg2+EOJ37nWhGtX71tNbJjbeUw2P852HoIA3\nmAZfoQjVKB6zWMMMDC19nbW6Rfuhmoqzdw6Lnq0noKpNLOPQuZR2zq/kID/b\n2R4NmopNbNMxyZSJfmmZAGeusCrHZR4qqJPqTgS0+/a+PZoXTRT1hJ5RQWvt\nlyobSMhcTWRzfWqDryhOaUk2COaTkQaNOKu6xzxNUrDDwKh/fH3J9iLJrbHL\nLXhI6TBxMiP7C8uy91CW1tezV831vqaLF4/pxTQm/zwtXtmTLBgfy9JqHAhW\nrqmj\r\n=crlK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "408ff95f1ab94ea73027bc8a7443afb62d41a72d", "scripts": {"dist": "mkdirp dist && browserify --standalone Qs lib/index.js > dist/qs.js", "lint": "eslint .", "test": "npm run tests-only", "readme": "evalmd README.md", "pretest": "npm run --silent readme && npm run --silent lint", "postlint": "eclint check * lib/* test/* !dist/*", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "prepublishOnly": "safe-publish-latest && npm run dist"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/qs.git", "type": "git"}, "_npmVersion": "8.1.4", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "greenkeeper": {"ignore": ["iconv-lite", "mkdirp"]}, "_nodeVersion": "17.2.0", "dependencies": {"side-channel": "^1.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"aud": "^1.1.5", "nyc": "^10.3.2", "tape": "^5.3.2", "eclint": "^2.8.1", "eslint": "^8.4.0", "evalmd": "^0.0.19", "mkdirp": "^0.5.5", "for-each": "^0.3.3", "qs-iconv": "^1.0.4", "browserify": "^16.5.2", "iconv-lite": "^0.5.1", "in-publish": "^2.0.1", "has-symbols": "^1.0.2", "safer-buffer": "^2.1.2", "object-inspect": "^1.11.0", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^20.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/qs_6.10.2_1638769647471_0.34729957789095955", "host": "s3://npm-registry-packages"}}, "6.2.4": {"name": "qs", "version": "6.2.4", "keywords": ["querystring", "qs"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@6.2.4", "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/qs", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "dist": {"shasum": "d90821bb8537cecc140e6c34f54ec76e54b39b22", "tarball": "https://registry.npmjs.org/qs/-/qs-6.2.4.tgz", "fileCount": 20, "integrity": "sha512-E57gmgKXqDda+qWTkUJgIwgJICK7zgMfqZZopTRKZ6mY9gzLlmJN9EpXNnDrTxXFlOM/a+I28kJkF/60rqgnYw==", "signatures": [{"sig": "MEYCIQClXncLF64oTkJulkkcw9bDc06J9xV3mEpBRa7XVYYgBQIhAKneByXOPfrgLAsKHUvWfhSVQWEejq/ajlWk8ot7CPvH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 97073, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh3RxPCRA9TVsSAnZWagAAUIIQAJjd/IK3YAoEqgl6z6Rs\nAwy+LT9VADqNYJec0AYGLDcU3pR+Zl3BMipu75g52B7IiRR2cFi5+qNYNyf2\nBeTjarY6upKTV0np4ESUl59MTPzIglQ0LuGTTX06Bd22eZaiWgR8BCRsJ0Ue\n7xKV2XJehi997HIu8Be9swLiAS8FtXSqjWPQy+99w9j3hksaVVnDtymDNxr7\no2r9y3mYIrsoUlOviiNr1KiNLdZf4btlhD/0Q27R04BQYkVa4Es+qYKlhz6E\nVuH31lMTYoki5D68gtb/bqL//g8Fnws1/W5DkuAswJTzDyhLlA5tPs1HL/mZ\nvQiufH0qSf+84WB5G8xzQBQ0Dm0rTEsg9JjRVgJJtAc3okQNJiBABfbCXWbQ\nO2qtVk8S8JDpflyoMKx34hD/kMKQP/bl/bGJPcQK0mzXIRTYiNyuiKd0i3BK\nTEyTJqPgxZPNFPrqlUPm2aMYWmbSXbo1F0P2WdOvff9MGbMlo0hMr0sOpUqI\nfodfikw3/xNb5E6KHdJkaM+uqyFsY30vBMZrkPQoZoCxh5nW/WguD4J8Ib9K\n7xGoICNPRQAcVcqrBrpW4k6okwh4rbc2vGeefHjauGkBrlnZ2efO/tSx4ddU\njOuxr4mM0y+1J/6/TZx6I4I/uNpcJWS5cwWw2Wj990WLaNedQCx7vy0Cd5d5\ndTP7\r\n=mtgI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=0.6"}, "gitHead": "90d9f2b45715b7b03da92113a7b8af236c01088d", "scripts": {"dist": "mkdirp dist && browserify --standalone Qs lib/index.js > dist/qs.js", "lint": "eslint --ext=js,mjs .", "test": "npm run --silent tests-only", "readme": "evalmd README.md", "pretest": "npm run --silent readme && npm run --silent lint", "postlint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "prepublishOnly": "safe-publish-latest && npm run dist"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/qs.git", "type": "git"}, "_npmVersion": "8.3.0", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "_nodeVersion": "17.3.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"aud": "^1.1.5", "nyc": "^10.3.2", "tape": "^5.4.0", "eclint": "^2.8.1", "eslint": "^8.6.0", "evalmd": "^0.0.17", "mkdirp": "^0.5.1", "qs-iconv": "^1.0.4", "browserify": "^16.5.2", "iconv-lite": "^0.4.24", "in-publish": "^2.0.1", "safer-buffer": "^2.1.2", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^20.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/qs_6.2.4_1641880655055_0.18330013316566696", "host": "s3://npm-registry-packages"}}, "6.3.3": {"name": "qs", "version": "6.3.3", "keywords": ["querystring", "qs"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@6.3.3", "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/qs", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "dist": {"shasum": "645c23bd6def4d552f258f0aadfeea6fd57f5a86", "tarball": "https://registry.npmjs.org/qs/-/qs-6.3.3.tgz", "fileCount": 20, "integrity": "sha512-f8CQ/sKJBr9vfNJBdGiPzTSPUufuWyvOFkCYJKN9voqPWuBuhdlSZM78dOHKigtZ0BwuktYGrRFW2DXXc/f2Fg==", "signatures": [{"sig": "MEUCIBPhllB5s1fKRTa6yAxUv9sVMs7gCOdGUKizxQkLA2EyAiEAnOYqvUv5hG37xM4nE9n5RlRe20B+ZJDXhEKqdBkJht4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 111131, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh3Ry0CRA9TVsSAnZWagAA6h0QAIlK+AZk+Rru+Mpu7SkD\nzfguZ53h5eKnQBdiZn9xhsc61TI2XvT2/v5TGaHNo+qdhY8nomTcMoSkvlpv\nxASo+twbC0V8s6jFEHdu6vhbzJJyRmGzfLYAS30CUUFQabTk9M363wFcpI8x\n0OeSsz+Ci7BvBsynFtgOKcLRTcg3+rx9oa0gHxHxbPccQv0nLPOqqe0GhRxw\n0qbbBVxSc65be4KHkKQBE6e9HTcfJMr+s/TIn+4IkR+stbPLP7l3bqqnZw3t\ncuMksS5TjiY53a+dm7FuJeb4Npu8JEfGORr4J5Qbl6bU7goHaok53MA7hZZo\nJt6QR55xhX5c7OaFFPGMiikpU9eIH4EU9zCPO0+8igC+sC1+WnpcFdTz8+qA\n2hsjma2+OVppgmFJuctMzTPa1lkjltOvb0pUFFnnKPtHyHkQzjl+BNAmTUL8\nx0GbVH7AkBlsFWvYngtXcOqjrg6SrylDb5ODjZMtd+zBCUMN0sFJMGu7ORJR\nodo3NCxrHfnnCJviDcV0gPTAaBYFH7+5SbeJ3OER4lCFGqNWHYlLo7V1I/bO\nkMFMQjjn7K0sq9vLTvReMMcM4Gfj8kgbT6yIkDAxef7i82xEqndyRJ3j00vh\nOYytST8vY34KrFl0Khwr7IVyD/Jv7O7I6m76p8s0Dyp+GoRSGtOqMjjmPLwE\n0Xyp\r\n=xNym\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=0.6"}, "gitHead": "ff235b4ca81f82728b745b71fbd4bad173535305", "scripts": {"dist": "mkdirp dist && browserify --standalone Qs lib/index.js > dist/qs.js", "lint": "eslint --ext=js,mjs .", "test": "npm run --silent tests-only", "readme": "evalmd README.md", "pretest": "npm run --silent readme && npm run --silent lint", "postlint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "prepublishOnly": "safe-publish-latest && npm run dist"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/qs.git", "type": "git"}, "_npmVersion": "8.3.0", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "_nodeVersion": "17.3.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"aud": "^1.1.5", "nyc": "^10.3.2", "tape": "^5.4.0", "eclint": "^2.8.1", "eslint": "^8.6.0", "evalmd": "^0.0.17", "mkdirp": "^0.5.1", "qs-iconv": "^1.0.4", "browserify": "^16.5.2", "iconv-lite": "^0.4.24", "in-publish": "^2.0.1", "safer-buffer": "^2.1.2", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^20.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/qs_6.3.3_1641880756518_0.8930809421780708", "host": "s3://npm-registry-packages"}}, "6.4.1": {"name": "qs", "version": "6.4.1", "keywords": ["querystring", "qs"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@6.4.1", "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/qs", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "dist": {"shasum": "2bad97710a5b661c366b378b1e3a44a592ff45e6", "tarball": "https://registry.npmjs.org/qs/-/qs-6.4.1.tgz", "fileCount": 20, "integrity": "sha512-LQy1Q1fcva/UsnP/6Iaa4lVeM49WiOitu2T4hZCyA/elLKu37L99qcBJk4VCCk+rdLvnMzfKyiN3SZTqdAZGSQ==", "signatures": [{"sig": "MEUCID+U9mxTM8fYSXvAP0LC1gYkdYl9KmqqyojHBoqVDjBuAiEAvPFMEQbL9Rx/7GaLU7wS+iEoQYUh5A320PUjsN/cCIU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 115464, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh3Ry6CRA9TVsSAnZWagAAU/0P/j3r+fhcBRg5tSM9UBbK\nkVRT4rSQZOMtA8Hk3hMctvSl0gEzcVh93AYi2tBuMUSe0NUa/lpS8V++ycxq\naDSL22UiCjMmWZKc1SQUuusNmDw2Tw3VDZ6feNmPPcy6H/Lb7xtnC5EfzR++\n2435TIbYN+z9ffa3pAWVWY4p7Vlnw7YYXjAQH1OZdjr/XqnH/LVNoNH/i+1r\nAumvJXflTDqOFYte8lzYdVtdQkcf+HTH7mjysl0NqNWVAZLkweAFuarJXaz5\nImtGs2F2G9jkOJyhjWjM0RAnH9EtgBkaGzkppjim5uu4utRrCDq9IiDxLTcU\nK2KX6u2hStvnsXxQJWartr2X5gFSea6WXylI11ivy9naS4eHzaBFM9Jr9ZVX\nAt6+Zv4lQlj9AcSHcfj3p69E61D51zfLpW2jZ62sqouWbkir1lLWUvhhQ7zB\njjZXmQOyaJ18gBY4e/3O2CDirAb+FuH8NiV7Vf+zdjN0RYUxFl2k9IwOcKmQ\n8FOfn6bR1L6cflfkjR5ksr9b78uNsqENK6bPH+1NSMmDnhAFV2GFHMy9CdcX\n9QH5zALVkOkRoCEciwnI/i6kVFOG0GDY+OBD8PDZlRzfRQ7G37mdH21CV/KF\nwgt/3lCJ+jSNhToDVi9cBMVgEDVsmHmQtyfukW7kpPlfsK4Ox4ty+Gt/Tu0+\nJra2\r\n=tZ+B\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=0.6"}, "gitHead": "486aa46547b4e878d6e87183de95dd26d46fb020", "scripts": {"dist": "mkdirp dist && browserify --standalone Qs lib/index.js > dist/qs.js", "lint": "eslint --ext=js,mjs .", "test": "npm run --silent tests-only", "readme": "evalmd README.md", "pretest": "npm run --silent readme && npm run --silent lint", "postlint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "prepublishOnly": "safe-publish-latest && npm run dist"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/qs.git", "type": "git"}, "_npmVersion": "8.3.0", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "_nodeVersion": "17.3.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"aud": "^1.1.5", "nyc": "^10.3.2", "tape": "^5.4.0", "eclint": "^2.8.1", "eslint": "^8.6.0", "evalmd": "^0.0.17", "mkdirp": "^0.5.1", "qs-iconv": "^1.0.4", "browserify": "^16.5.2", "iconv-lite": "^0.4.24", "in-publish": "^2.0.1", "safer-buffer": "^2.1.2", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^20.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/qs_6.4.1_1641880761698_0.30524380188952716", "host": "s3://npm-registry-packages"}}, "6.5.3": {"name": "qs", "version": "6.5.3", "keywords": ["querystring", "qs"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@6.5.3", "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/qs", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "dist": {"shasum": "3aeeffc91967ef6e35c0e488ef46fb296ab76aad", "tarball": "https://registry.npmjs.org/qs/-/qs-6.5.3.tgz", "fileCount": 20, "integrity": "sha512-qxXIEh4pCGfHICj1mAJQ2/2XVZkjCDTcEgfoSQxc/fYivUZxTkk7L3bDBJSoNrEzXI17oUO5Dp07ktqE5KzczA==", "signatures": [{"sig": "MEUCIQC/+fcCTyLPsPv3vmLlT2r7meeKBHQ+EMnyZaSuz4nUjgIgFkzvYa4xVIwZ5VRAvqHLVHcN2QUIXSOX2cc7IwZW4pw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 125650, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh3Ry/CRA9TVsSAnZWagAARJAP/31C2jFC0u7IMUBuJzEH\nVgP2pjpcyXVVyTfls7Ti+0M3EIhXfmtAbHfiqO6RZhfYHtsiEwd6TzHsCsAM\n2S4+Egi1bTTque41/LzcEjcH3RnXBgMqpO3qk5mzXBojEHTaQZtji0uykkA4\nq4y1CDb3WXbZXL642wBjEqUb6GSNf39rXq/0X3NaSr90YcTrnwys2etaSIvp\nevOZZCF0I/O5o9iRnim1X7IRifnQLY89XAXUHANnXvaiasugXivYyUw3P3Tq\nvMI38pbB5051SM3gGlTvifjJk6OrvgAac3XKmpxYopXHn4HD5iCMVOjBBr9v\nVUBgg2jdt9jVLNIz6oZpVFBiA2xcev1hKfCi2yL7OKIXiF8UlMHDwBD9m/zG\nQUEJy78/TYVL1PyVtf+7m/6N4v+JL8wrB9RWL5ZojC5lT8s0EhalXOL40G3j\nvV0pjzdnBzXQqzp8dS6WclknP2yS2viFhOFdQzE9PmcePa8HegKY1ZvBXbtL\nLvxj8QJww5xaGVtm3LHn8RmzJJmDi6oQp0RFeRhKD94FUWu5X3oTYFCGd9Yc\nKJ0J0Nx82not+Z2lnWjOpx2FVilLBsWj4I6kuKWK0l/14FdliyBXu2iUtgEv\nbmBm5ToTq+yjrcHvZ3sm68DB1vV/rBe1//AIagjXAJIdz3QlbJNw8NdK0ZOm\niyvD\r\n=Jprg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=0.6"}, "gitHead": "298bfa55d6db00ddea78dd0333509aadf9bb3077", "scripts": {"dist": "mkdirp dist && browserify --standalone Qs lib/index.js > dist/qs.js", "lint": "eslint --ext=js,mjs .", "test": "npm run --silent tests-only", "readme": "evalmd README.md", "pretest": "npm run --silent readme && npm run --silent lint", "postlint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "prepublishOnly": "safe-publish-latest && npm run dist"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/qs.git", "type": "git"}, "_npmVersion": "8.3.0", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "_nodeVersion": "17.3.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"aud": "^1.1.5", "nyc": "^10.3.2", "tape": "^5.4.0", "eclint": "^2.8.1", "eslint": "^8.6.0", "evalmd": "^0.0.17", "mkdirp": "^0.5.1", "qs-iconv": "^1.0.4", "browserify": "^16.5.2", "iconv-lite": "^0.4.24", "in-publish": "^2.0.1", "safer-buffer": "^2.1.2", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^20.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/qs_6.5.3_1641880767360_0.6826765969830277", "host": "s3://npm-registry-packages"}}, "6.6.1": {"name": "qs", "version": "6.6.1", "keywords": ["querystring", "qs"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@6.6.1", "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/qs", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "dist": {"shasum": "badc3705933629bed17bc709ba824ba0676708c5", "tarball": "https://registry.npmjs.org/qs/-/qs-6.6.1.tgz", "fileCount": 18, "integrity": "sha512-DxHK46yYwC06eXWu3FCqlADDkzHZUhkaMY2o4jFbPtN90TO/Y4jzyuOpCa1iZ4OvFEOCC4MoGzubQUdxpZzk2w==", "signatures": [{"sig": "MEUCIQCYtey4BsM/A1UTqrfo1wDbFgoXicVn09oJuLwlWpOQUwIgd7PRtFY2SVYEASWEc1B0tAJ9XgyL9co6gPozhH26kko=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 146433, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh3RzGCRA9TVsSAnZWagAA9nMP/ij57bkypPa00qAx8osB\nj5PT8eTJzcX3Gqs0DmJ5Ur6Wm6AMqlQWeEt9We76CpwqWf1Oj3liEKxip2vb\neupBl7p5K82sGZo6pn9OH0AtRrOdejUnG+Kh3uYRI2ssaE8DonT0F9rAxGnV\nNf3qCUqIhX8rxWBGZeeXUqAXwPTqejtU/Q8xcPKWpDQ+MqFbNsdVVJ5nIMRg\n8Lxts2UVqOyynpkim3nYgGWv4bA7CE9tA4MqnN4uFftd4ScLARedZ9+2HgVk\nS/Gj33rrs5ipg89kEPYK99LtiUN/ZEDru2i774JDiqDabzMRV6Qn3+NPlCt4\nssdXlmlvRmvZxdeSr20iGSk9rE7zNYjhTRoRNylCZZdc3wtKTqcIJbUZLJvd\nu0KXEKjrTjLMBUc7LtKnInx8+iK52j2vBOur1sLcOdQhmOn6gq5FJWk+hdmx\nJI2llLz9E/DqX69xhTj1/awQsVQu3BtidKFS8uvzMKWMpflpjZix/43Zuxx7\nfZfjHmOceAzRRrt4qcymirb7oHlA6rQdC/+FY1JW1KI/1J/rVpJ/SQUvHP/4\nszxLpogeszMeQUo+0RfUIrhgahDsZ0BSDOloUDc0kHz2qwX8P+9kZ0MtiYm9\nrLyICxUuKre/y9BhEf+4aTvbPcsgZkIYO6fbI0ZCDIy411pgMdrPvcldPqem\nC5D/\r\n=3jAG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=0.6"}, "gitHead": "4cc653c08c583c0b39e2eea0bf1cd2226ac5ec51", "scripts": {"dist": "mkdirp dist && browserify --standalone Qs lib/index.js > dist/qs.js", "lint": "eslint --ext=js,mjs .", "test": "npm run --silent tests-only", "readme": "evalmd README.md", "pretest": "npm run --silent readme && npm run --silent lint", "postlint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "prepublishOnly": "safe-publish-latest && npm run dist"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/qs.git", "type": "git"}, "_npmVersion": "8.3.0", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "_nodeVersion": "17.3.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"aud": "^1.1.5", "nyc": "^10.3.2", "tape": "^4.14.0", "eclint": "^2.8.1", "eslint": "^8.6.0", "evalmd": "^0.0.17", "mkdirp": "^0.5.1", "for-each": "^0.3.3", "qs-iconv": "^1.0.4", "browserify": "^16.5.2", "iconv-lite": "^0.4.24", "in-publish": "^2.0.1", "safer-buffer": "^2.1.2", "object-inspect": "^1.12.0", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^20.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/qs_6.6.1_1641880773898_0.27251653471782267", "host": "s3://npm-registry-packages"}}, "6.7.3": {"name": "qs", "version": "6.7.3", "keywords": ["querystring", "qs", "query", "url", "parse", "stringify"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@6.7.3", "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/qs", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "dist": {"shasum": "67634d715101aa950601f58dbef353b7e1696b95", "tarball": "https://registry.npmjs.org/qs/-/qs-6.7.3.tgz", "fileCount": 18, "integrity": "sha512-WBoQWf5L/UOLqUj8Mvr4Om7J+ZTCqPbYPHyeLNRS9t9Q3M3/o/9ctpWnlo8yyETPclx7FhH5LidjKKJa9kdSRQ==", "signatures": [{"sig": "MEUCIChmbYieZAGjScHauRxx90t+ky+AJxifseQ4LdD9sY/oAiEA8U4XwUKj5x1FesWgUW/bdb4eLywetBn+KEEL1y+DsNs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 159376, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh3RzLCRA9TVsSAnZWagAAIs0P/AvzIXL2ATDnw6dflk7q\natAKMqPu+csP2WLnHKAAZ4zJ7fQKTlO0c984d/0UI6U6qPGtn6ke5LS3zYzz\n6ttkfR9xRMqNjWBjY+bIzUpC1TkvknBnGPw7kvIiAnXsB+oycWLRyudoHNl+\nj9VYCIIJkyzes+VQ6lMG9JyPoGXBwit5kUS/wE1RRgGfQvGJVpCtMCju+X4I\n/pIXdvSqy3FiiG80blFDTSfMdZumv3UgwCCINTBO0WyGJwDDh9I/W+xJi0wk\nEFzSCujmA2x+7m2iRqsEermfERejBgvZkM6Na5YYrVEQzQjGpllVX0Z8fSqG\n9fNv5FrkM7qSe5K5L21dSVHsJCM2i4Fs1IdyVuKoz/HLQz9iK2MS6ZZBRC9a\n7d5ubp/QeK7nGJfZOfAoiwa6B6k/qre8Nd56Ioo6EmSFCqZnPAl/RnxcMbyx\nC3MJS3TaXC1QLOAsxjyUWsUKGwrsyJtsJH71P4NtLbL9hgmpAxTb+RmXiLzL\n8p8DObIS+3LE+YFh4dzmUCPA1HY8an5sxnwetETn5PwQkcXRp8MEMKAt2UnO\nfBKojoJiBpgWR2U5sKGtMdOgwr4H64Id1cBTw9vMq0c+68P361FBH229JQWf\nhYj6WGpOQtCqGNe8lSpyDroxbBM8gFiY3eDZdx3NcDWk7oNZGRuF61gacL3K\nxMCt\r\n=RX3L\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "834389afb51ac8cc03a22a0c76604c65776dc468", "scripts": {"dist": "mkdirp dist && browserify --standalone Qs lib/index.js > dist/qs.js", "lint": "eslint lib/*.js test/*.js", "test": "npm run --silent tests-only", "readme": "evalmd README.md", "pretest": "npm run --silent readme && npm run --silent lint", "postlint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "posttest": "npx aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "prepublishOnly": "safe-publish-latest && npm run dist"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/qs.git", "type": "git"}, "_npmVersion": "8.3.0", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "_nodeVersion": "17.3.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "^10.3.2", "tape": "^5.4.0", "eclint": "^2.8.1", "eslint": "^8.6.0", "evalmd": "^0.0.19", "mkdirp": "^0.5.4", "for-each": "^0.3.3", "qs-iconv": "^1.0.4", "browserify": "^16.5.2", "iconv-lite": "^0.5.1", "in-publish": "^2.0.1", "has-symbols": "^1.0.2", "safer-buffer": "^2.1.2", "object-inspect": "^1.12.0", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^20.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/qs_6.7.3_1641880779048_0.3030605328691325", "host": "s3://npm-registry-packages"}}, "6.8.3": {"name": "qs", "version": "6.8.3", "keywords": ["querystring", "qs", "query", "url", "parse", "stringify"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@6.8.3", "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/qs", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "dist": {"shasum": "c1909067d1f6c2f62f9bde1c44b44c72d2fc7a41", "tarball": "https://registry.npmjs.org/qs/-/qs-6.8.3.tgz", "fileCount": 18, "integrity": "sha512-lvnoy9TgoYNQLYY6Ba4ptroTR9ptMkMQuXLxH4oppEQtglfAop+ZaC0dtAgY75rarH73CPu1eq3XrtzI/zZYmA==", "signatures": [{"sig": "MEUCID86Lv2J4W9F7/nxZM1lhE0yZRqrRDNOF606cy3T4MVLAiEAmcns3SCV9H3FFXyDFQnR7YrBz/tuIN8mVVPAmQurHpo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 160581, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh3RzSCRA9TVsSAnZWagAAVkQP/3+yjAPuHkno/KvvPCRb\n27hlzu4XS21a/Qttu9YHRLGlRTfBwkRmikrohZGoY4j9O8F9ZwS4vzjuiEld\nJCPC2omQTe1Wg6tQvIuNIRDfuGvXCJqYrQVBEQUQfy8gKiSkm1Lm4Z9M/GoK\nd9WSH0ZRuUIA/HZffK6djgQ2VHzBm72GsNHnwPjdJM0cNVjSglzdFDxirtJH\n3jEIuDStj1SVQ9Fa7NsL2nScD6tzloy5Md9FQVNiD4M8Ij93J3JNPpYOiG6h\nFZ4GIHzUTk8yB41+KQ9n5kYM5SsTMHp4+nWxzgZZBCmXb8sDVdz3+3LGDkHo\nkaWXlKqxxEP8kGY1DJ5jXfI3rllED273u8P5NN3x0L98EWKdNu8KvpdepSDg\n75YxHpLeFLOjIFfSKXOJkDBv+iM2QBbj93B6ccr5mfC+HtfQOa99R+HdYx2Q\n2GXbYi3SJHcxT6N5fcuS/j4yiWsTWsKo8AO9LPjzmithO7Chz9ir75v8AtZd\nnM9aVLRK+qxd2VSUORh5CSlgV44OEUEJX4ofUxQHX7LHbcxKxOdlx5rJlMEs\nUYmx8NR4A/OHvzv6xdgy27w2mhU/7H2xzfNEYKXnLR/+Xn2YrtwyxZAVZwn0\nvaGUT1TDwErpJbyl10JVONgy0IEOiDR6oOlabGekEtlErdyWQusEnHM4Je8U\nrwNP\r\n=VbFx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "0db55386013a5d92503944ad42022fd8c112c983", "scripts": {"dist": "mkdirp dist && browserify --standalone Qs lib/index.js > dist/qs.js", "lint": "eslint .", "test": "npm run --silent coverage", "readme": "evalmd README.md", "pretest": "npm run --silent readme && npm run --silent lint", "coverage": "covert test", "postlint": "eclint check * lib/* test/* !dist/* '!coverage/**' '.nyc_output/**'", "posttest": "npx aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "node test", "prepublishOnly": "safe-publish-latest && npm run dist"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/qs.git", "type": "git"}, "_npmVersion": "8.3.0", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "_nodeVersion": "17.3.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tape": "^5.4.0", "covert": "^1.1.1", "eclint": "^2.8.1", "eslint": "^8.6.0", "evalmd": "^0.0.19", "mkdirp": "^0.5.4", "for-each": "^0.3.3", "qs-iconv": "^1.0.4", "browserify": "^16.5.2", "iconv-lite": "^0.5.1", "in-publish": "^2.0.1", "has-symbols": "^1.0.2", "safer-buffer": "^2.1.2", "object-inspect": "^1.12.0", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^20.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/qs_6.8.3_1641880786115_0.8679919915915015", "host": "s3://npm-registry-packages"}}, "6.9.7": {"name": "qs", "version": "6.9.7", "keywords": ["querystring", "qs", "query", "url", "parse", "stringify"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@6.9.7", "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/qs", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "dist": {"shasum": "4610846871485e1e048f44ae3b94033f0e675afe", "tarball": "https://registry.npmjs.org/qs/-/qs-6.9.7.tgz", "fileCount": 17, "integrity": "sha512-IhMFgUmuNpyRfxA90umL7ByLlgRXu6tIfKPpF5TmcfRLlLCckfP/g3IQmju6jjpu+Hh8rA+2p6A27ZSPOOHdKw==", "signatures": [{"sig": "MEUCIGvJUd+HOekbaDUZCtLznV2MBA58mNtzBY6W2c1vSJPMAiEAiKceZtJ/ec00WXH/exwHHNRb16vnRNA5DGMjD5SMIvY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 169869, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh3RzXCRA9TVsSAnZWagAAuMEP/3OtllpIwsYdO9cWR5rz\nxYR0HxYCaFyAFOMm04YUBnxpCQr473snA66qPj77ryfLNlG2/G3EC/HOynda\nVJZFGId3mlzVTMF9vY/dC0iLl/9NhD57gVK9tKcOXf09/Rpn+piR6ZQFNen4\nM1I4+ZxfjkvO8p4P74LNJ13rKvSj0bX8fVyStrmxGceEu8DhZduu0Y9S59KZ\nwQCKFwE8UueQDZP7I+fDv0HHu+i4jUeTqEgcRAK032ivj0jUJVeTjmafKmTa\n4LBChXZ5CEpeXzqw5H7uNwKZlkBjXwfuANJ5iVbIuLh73aOuLVvUzoNCSme+\n/+aiOuELjK0PKAKHo523h7p6K5CWfnTKTeMUuZnCZ/cmRnRVoRiv5qZPaz6x\nH6sMkO9J6Ofl+aE6YyB6hySv+e+VflrD/K1poLcrvlRwnkOZYC1cg5z7TmzW\nO5NTiAfsbjf0IUYol2EaQsnx6G1iuaaNU2GVZ0rFDTyMrFAT5CXy8jKsUjyS\nkbzZjtAk6TqTHcDSOV9Sgfqc20W1b74XssjWgN+YoL5iB3s7J5TfwNNM7X8c\n8/w5f4enGJ6RrtT4ZIQBgI1cio+q4VJR/UUFr7g059HKt/32Qv+ZVWVatjHL\nVOv9SDjGebCxI4z3gVSizuRhQZOzi/LwUFSZlmHyZM0Q2bspRuK7o1OXNfDj\nHdW8\r\n=XdBe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "4cd003291fe3b347884f797e548b58a12150a0e3", "scripts": {"dist": "mkdirp dist && browserify --standalone Qs lib/index.js > dist/qs.js", "lint": "eslint .", "test": "npm run tests-only", "readme": "evalmd README.md", "pretest": "npm run --silent readme && npm run --silent lint", "postlint": "eclint check * lib/* test/* !dist/*", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "prepublishOnly": "safe-publish-latest && npm run dist"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/qs.git", "type": "git"}, "_npmVersion": "8.3.0", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "greenkeeper": {"ignore": ["iconv-lite", "mkdirp"]}, "_nodeVersion": "17.3.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"aud": "^1.1.5", "nyc": "^10.3.2", "tape": "^5.4.0", "eclint": "^2.8.1", "eslint": "^8.6.0", "evalmd": "^0.0.19", "mkdirp": "^0.5.5", "for-each": "^0.3.3", "qs-iconv": "^1.0.4", "browserify": "^16.5.2", "iconv-lite": "^0.5.1", "in-publish": "^2.0.1", "has-symbols": "^1.0.2", "safer-buffer": "^2.1.2", "object-inspect": "^1.12.0", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^20.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/qs_6.9.7_1641880791666_0.026589856843736603", "host": "s3://npm-registry-packages"}}, "6.10.3": {"name": "qs", "version": "6.10.3", "keywords": ["querystring", "qs", "query", "url", "parse", "stringify"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@6.10.3", "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/qs", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "dist": {"shasum": "d6cde1b2ffca87b5aa57889816c5f81535e22e8e", "tarball": "https://registry.npmjs.org/qs/-/qs-6.10.3.tgz", "fileCount": 17, "integrity": "sha512-wr7M2E0OFRfIfJZjKGieI8lBKb7fRCH4Fv5KNPEs7gJ8jadvotdsS08PzOKR7opXhZ/Xkjtt3WF9g38drmyRqQ==", "signatures": [{"sig": "MEYCIQDPJ46DoXU1ydWyqKNmM1/Kl3DuvkdrtVNcgCcmnl3IOAIhAI3nrlggSaolvdZMmy0gmyuvGHtDPrInq4atOiCjCKAk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 216400, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh3Rz1CRA9TVsSAnZWagAA4RsP/A0gqkTNbA91pFmvx7DH\nhj+S6QXRMPzssJKVQGNwe8aiO6GlYo+hC7lKMEGVC9X8208+aeyMNgLU5+nK\n5a8jo1w5DAHv7M8R/tzkAEBcVDMji0MAGgjT+W0pUISeieZiQCAwvEbtLsJl\nFeVZ9+Abwt8q5J0vXiKLPTWBX0gYDgGLu6U++t46qdeGBh2VojmlD4VuN0a4\nYA3G4/awY6LOMng//lT7GW8Lc9Vg0dcuTk4nmvqC5BXg4eBfiVsOhgkmjEnl\n1O/V66p3qCK/HfR3mWKewIbulrC1lCEwo6c8jCaGU5YFIbVnljCGJ9SgNwB0\nvQAuoDyZ07rsv+TJ0Dk23T1TL4OgUPzlZqKYjFhlFk/wV3g+nZ5AYxLpEEV5\nO8138WRWsBnOAeECl1E/eOcgXGtNB9t1jphYfjHLVjscHwykwT7DCKRPvEBU\ndeaie1OOXWSInXcyIqZ51+cvNhBYGa381XLA6JuPBxJnBx19WZfmc63E5vzg\nYFpbOv9Hr0m1TpnKeB/mgP+vQO1A/L7yCiI0l2Yv8aizSwE2tZ7lMLEi/J1P\nguB98I0FM6D5eGU4gF/wv7ipar1yvofvQBjSElCyzNcsgeOukPDSZvA8eFN+\n2InSMy5ENl65cfbiPuQhYjbJqi9frYdlUS1e4Apnxy42Ym2qUMiHKZlLN6qt\nU4DI\r\n=APG3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "f92ddb56089ae2c74f5ca7b0447fef3a97e8c9bc", "scripts": {"dist": "mkdirp dist && browserify --standalone Qs lib/index.js > dist/qs.js", "lint": "eslint .", "test": "npm run tests-only", "readme": "evalmd README.md", "pretest": "npm run --silent readme && npm run --silent lint", "postlint": "eclint check * lib/* test/* !dist/*", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "prepublishOnly": "safe-publish-latest && npm run dist"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/qs.git", "type": "git"}, "_npmVersion": "8.3.0", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "greenkeeper": {"ignore": ["iconv-lite", "mkdirp"]}, "_nodeVersion": "17.3.1", "dependencies": {"side-channel": "^1.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"aud": "^1.1.5", "nyc": "^10.3.2", "tape": "^5.4.0", "eclint": "^2.8.1", "eslint": "^8.6.0", "evalmd": "^0.0.19", "mkdirp": "^0.5.5", "for-each": "^0.3.3", "qs-iconv": "^1.0.4", "browserify": "^16.5.2", "iconv-lite": "^0.5.1", "in-publish": "^2.0.1", "has-symbols": "^1.0.2", "safer-buffer": "^2.1.2", "object-inspect": "^1.12.0", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^20.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/qs_6.10.3_1641880821514_0.1327648895755973", "host": "s3://npm-registry-packages"}}, "6.10.4": {"name": "qs", "version": "6.10.4", "keywords": ["querystring", "qs", "query", "url", "parse", "stringify"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@6.10.4", "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/qs", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "dist": {"shasum": "6a3003755add91c0ec9eacdc5f878b034e73f9e7", "tarball": "https://registry.npmjs.org/qs/-/qs-6.10.4.tgz", "fileCount": 17, "integrity": "sha512-OQiU+C+Ds5qiH91qh/mg0w+8nwQuLjM4F4M/PbmhDOoYehPh+Fb0bDjtR1sOvy7YKxvj28Y/M0PhP5uVX0kB+g==", "signatures": [{"sig": "MEYCIQCEg5HljnV54vYV29J3iO91OriMUBODe+7K3Wgh+CfErQIhAOwTA7sV8j0vtmPzKsGUNFevQzZGb7II8iostxyRiMtD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 226413, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJinlBdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo7qxAAgCkWc9sy8IUn33bSi/57HsGgXBrvbymlYuDGieLqsZdXx+cL\r\nbvYC3Iwvm5oWF4QZdAQQ1Z1ELnLJpFGJ4Aai3QgEiwWO2YMHH0PO9XDbl8O9\r\nrEbNJaReLIr/5FsChDJut6pZQ17KgaLvvI/aiTKeq467dtcMSR53oUVwAV6i\r\nNhilsui6zwaWADSfc29MPZyaJYPTZ7S4v1Zw54SU8JyOoF5JmTb8IKJM8iid\r\n3UXTJjxHd1FsndmNsmoDq1Kv4sAx9K0hVxlEw9SKhB1b1x9xQtdjQJl6uqcd\r\no7+o6t5xCgAKd+HS580D3t5VdKYjMMVL7FVFDmxPluZP4T3H6AFA9MrlvFUv\r\nN49988+MsyIJch8ZOwgDOQrLjysNj8vUrQfnGgOmi6qOYR3FJwX7U/57O7lq\r\nXGGN4gyNetxGTvo5Em9Pbv5ND1OpoSRJi0qkyToiOrleJCja8/Q/OkjXguci\r\nl6znO79LRlOSH/i3T6viwCoeF8J5Os5nLlC1dwwMuIpfoc55wwqKUeCkywYo\r\nDacRzcyTEsbP+CQ6Yiu8J39+tQQx9jvuR7B9dX80OMlSsFeXYWoCqDYhbgmK\r\nvebdMUDJuCkazL0MSeN6DVk4CM50EksfsEX4M/lAkZGg/pQ3YfZRqbyHTw+T\r\nJxbKuDSH72iPauvupPFe/H5Hr/ldeKqqZiE=\r\n=j3Jf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "ba9703c0340dfdeb73cb4387d6ab32c37768aa5b", "scripts": {"dist": "mkdirp dist && browserify --standalone Qs lib/index.js > dist/qs.js", "lint": "eslint --ext=js,mjs .", "test": "npm run tests-only", "readme": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run --silent readme && npm run --silent lint", "postlint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git' | grep -v dist/)", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "prepublishOnly": "safe-publish-latest && npm run dist"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/qs.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "_nodeVersion": "18.3.0", "dependencies": {"side-channel": "^1.0.4"}, "publishConfig": {"ignore": ["!dist/*", "bower.json", "component.json", ".github/workflows"]}, "_hasShrinkwrap": false, "devDependencies": {"aud": "^2.0.0", "nyc": "^10.3.2", "tape": "^5.5.3", "eclint": "^2.8.1", "eslint": "=8.8.0", "evalmd": "^0.0.19", "mkdirp": "^0.5.5", "for-each": "^0.3.3", "qs-iconv": "^1.0.4", "npmignore": "^0.3.0", "browserify": "^16.5.2", "iconv-lite": "^0.5.1", "in-publish": "^2.0.1", "has-symbols": "^1.0.3", "safer-buffer": "^2.1.2", "object-inspect": "^1.12.2", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/qs_6.10.4_1654542429609_0.7992067402346508", "host": "s3://npm-registry-packages"}}, "6.10.5": {"name": "qs", "version": "6.10.5", "keywords": ["querystring", "qs", "query", "url", "parse", "stringify"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@6.10.5", "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/qs", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "dist": {"shasum": "974715920a80ff6a262264acd2c7e6c2a53282b4", "tarball": "https://registry.npmjs.org/qs/-/qs-6.10.5.tgz", "fileCount": 17, "integrity": "sha512-O5RlPh0VFtR78y79rgcgKK4wbAI0C5zGVLztOIdpWX6ep368q5Hv6XRxDvXuZ9q3C6v+e3n8UfZZJw7IIG27eQ==", "signatures": [{"sig": "MEUCIEIzy3DakoZdSFeM2LfwsGuVTVgJo/Xli3gZN4p8N6+MAiEAn/Z2hFPrrgTLjXE2Aq+paG5nbV+jRs/JTMGd5VQjHF0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 227634, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJinoUiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrsQw/9FB42odBdnnA49SAVJlysY3Wb2BtL7BtN7NxeVW+7Vuh+Ra1d\r\ndJgHqAKXBc6QW8il0gu9oQ7udoog18ghshoLfR4w68TNj44WTFmNTDc8zwsJ\r\nHNiPAZHZXntGPFKKDf5w7aPQeUa8zfu+k0zFzWBQAfzUdzFRCumgY3uIHyVd\r\n8o8ZqsROappuv7FHxh4YYqEIdj1eMQSi7mhqm+5/1/ElqeGTIH8eP9aqism6\r\nPD3QvzYZYZxohcj+8rb3nshQH6zNePyHndEwmM2Q0L5HRkusWiQfFwXmVv+Y\r\nplFvpuo6rgTaLNPrdRvr2rF5fT8WZN21Q4mwoEXiRKHTbGuNyiKGagkk2m62\r\nuLtJwvDQFhOZMzimUMZUMJKGWq+DZ8/Z32I5P58VKOvu2ccKY6n761aRz4vX\r\nmtrlCO3MllrmQcnAx9wOCgvHhyzyjj4N9atlTKeCbieFkOYIMRed+BDW4xiZ\r\n+iN7dYfRg+Ep0hrzizkI6zCkxp+LuhczSvV3YDx3iNlgYxURq8TGk9yOFT05\r\nS40rVwmVSP4s2cME/liqjF94x8dVROGTDP3OnXiaq/MmIDSqjqXvm6SM8JDm\r\n4EHSfCkDjhWkUW+13paP4QSYR4jR4IA+9h9QydYPdVIrZpsJ0oqR2a17cptf\r\nTYrHAfBV76sKDq/lE1yPCxrDs3EhhEZWeJI=\r\n=/YlC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "95bc0185e157d400da4f43f1fcf1c7f008fd847e", "scripts": {"dist": "mkdirp dist && browserify --standalone Qs lib/index.js > dist/qs.js", "lint": "eslint --ext=js,mjs .", "test": "npm run tests-only", "readme": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run --silent readme && npm run --silent lint", "postlint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git' | grep -v dist/)", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "prepublishOnly": "safe-publish-latest && npm run dist"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "deprecated": "when using stringify with arrayFormat comma, `[]` is appended on single-item arrays. Upgrade to v6.11.0 or downgrade to v6.10.4 to fix.", "repository": {"url": "git+https://github.com/ljharb/qs.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "_nodeVersion": "18.3.0", "dependencies": {"side-channel": "^1.0.4"}, "publishConfig": {"ignore": ["!dist/*", "bower.json", "component.json", ".github/workflows"]}, "_hasShrinkwrap": false, "devDependencies": {"aud": "^2.0.0", "nyc": "^10.3.2", "tape": "^5.5.3", "eclint": "^2.8.1", "eslint": "=8.8.0", "evalmd": "^0.0.19", "mkdirp": "^0.5.5", "for-each": "^0.3.3", "qs-iconv": "^1.0.4", "npmignore": "^0.3.0", "browserify": "^16.5.2", "iconv-lite": "^0.5.1", "in-publish": "^2.0.1", "has-symbols": "^1.0.3", "safer-buffer": "^2.1.2", "object-inspect": "^1.12.2", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/qs_6.10.5_1654555937890_0.7829314516679007", "host": "s3://npm-registry-packages"}}, "6.11.0": {"name": "qs", "version": "6.11.0", "keywords": ["querystring", "qs", "query", "url", "parse", "stringify"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@6.11.0", "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/qs", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "dist": {"shasum": "fd0d963446f7a65e1367e01abd85429453f0c37a", "tarball": "https://registry.npmjs.org/qs/-/qs-6.11.0.tgz", "fileCount": 17, "integrity": "sha512-MvjoMCJwEarSbUYk5O+nmoSzSutSsTwF85zcHPQ9OrlFoZOYIjaqBAJIqIXjptyD5vThxGq52Xu/MaJzRkIk4Q==", "signatures": [{"sig": "MEUCICdxpgh4uy5Z/t0PXOQnHrlz5UczCFALcCcn29YHBAlnAiEAia4Ywm2mqXFzcIiKvtvSMz+oILrw/OYvMbI9B+eiZng=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 229455, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiuTbyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr8OxAAoMdGZi/qfAM0Rg+Ac2eZm1tQEcJteQodGu2MGsF6C/GHuvVv\r\nap0CZyQrYkLa1K8cUV0svvpX9HehGhCY50cuNNvlIgd5lPqIXFJRJZxCt2Gn\r\nPkQye6E7KmDmqGobf/ddyLMAmEqMzGq60CZGE2FUFJXkc/UdYxQOXmhHwhuA\r\ni6SZ0xfPQkAnOX4mANkBI43L5qxZuNMABow6d2/e/5X5nUbtoyU8i9Mm+AjI\r\nRgxV+58m6VpISFaxuHjW7oOXv85JeSjYSBvhaALHy8vHf/Qt0+7CTbUreWPm\r\nY6/Yj8l09NaZ0JydqqGlNfo/goqpdB7c2TpWTwL1PRDoJUOLPHxhrexx79F7\r\nVDm24XNy6/YLl7SZi6YHkvHaMF962QQehZUvRjAHTPI/5OvhkmAuWL1BNcNi\r\nKugDcF+96p2o6eAarxO6OkWrwpinzJ/X2T80VuQ2d3gcQmuMtPbv1H1r4X12\r\nC8BvIKia+9CS5KrzIRygtroRT6z3jqqhPecSZ/wZZ1zXI9DReR9od0SSPEJ0\r\n+OrTNriytlsNtq3SIYM/+cFh0Y/3UyZHl2c7IxO1QeQpbfOLTzrs4sJfyHjE\r\nsZy+AhxT4aNXjKjItpVjUzAlbMIop8liNXpuV3CQSUoLft3vf4Mv4NuTR4Yc\r\nut0/Qaqf8HcEQt8mdFd8bVQvEPio5QuLVL0=\r\n=0BwP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "56763c12ec4fbf723333cbb32371cbd386c33cbb", "scripts": {"dist": "mkdirp dist && browserify --standalone Qs lib/index.js > dist/qs.js", "lint": "eslint --ext=js,mjs .", "test": "npm run tests-only", "readme": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run --silent readme && npm run --silent lint", "postlint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git' | grep -v dist/)", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "prepublishOnly": "safe-publish-latest && npm run dist"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/qs.git", "type": "git"}, "_npmVersion": "8.12.1", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "_nodeVersion": "18.4.0", "dependencies": {"side-channel": "^1.0.4"}, "publishConfig": {"ignore": ["!dist/*", "bower.json", "component.json", ".github/workflows"]}, "_hasShrinkwrap": false, "devDependencies": {"aud": "^2.0.0", "nyc": "^10.3.2", "tape": "^5.5.3", "eclint": "^2.8.1", "eslint": "=8.8.0", "evalmd": "^0.0.19", "mkdirp": "^0.5.5", "for-each": "^0.3.3", "qs-iconv": "^1.0.4", "npmignore": "^0.3.0", "browserify": "^16.5.2", "iconv-lite": "^0.5.1", "in-publish": "^2.0.1", "has-symbols": "^1.0.3", "safer-buffer": "^2.1.2", "object-inspect": "^1.12.2", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/qs_6.11.0_1656305393791_0.10459431341975911", "host": "s3://npm-registry-packages"}}, "6.11.1": {"name": "qs", "version": "6.11.1", "keywords": ["querystring", "qs", "query", "url", "parse", "stringify"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@6.11.1", "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/qs", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "dist": {"shasum": "6c29dff97f0c0060765911ba65cbc9764186109f", "tarball": "https://registry.npmjs.org/qs/-/qs-6.11.1.tgz", "fileCount": 17, "integrity": "sha512-0wsrzgTz/kAVIeuxSjnpGC56rzYtr6JT/2BwEvMaPhFIoYa1aGO8LbzuU1R0uUYQkLpWBTOj0l/CLAJB64J6nQ==", "signatures": [{"sig": "MEUCIFlgmdeGeAIJV9xcix5OZo3oY1dNkEumpegE+NOLyOg+AiEAvnnINt/n/15OWt0anjsniF0NCp3HI54ARIa8rb1QeBs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 234521, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkBmg/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqdiRAAjX9hNqKyCcJaIF7DdHTeKoDknXBRCYKhm/YWO1oDwBS/6bqG\r\nRTCdkDkin2F0N3TCgIPuQcFnZ+QlArf0VtnQNZgBDhJIOMy5B0RvHiVZWRV/\r\nrIoR7FNZfTvXM6xM5w7dfoNCgAP20Q2ySAHuD/ydGO8WE+wcay6J9rMN4xSA\r\nkKryCTDWVTntAd/tqtpwHkbHZpMzRC8/bsaFR3feoSrapYOGJKNzXaHKcSsp\r\nb3HHVJ1XjXobiiXnvNLQEJHwzzUWy3nwKEq4YVGY6tH9Y21BuBXaNM6x6c9K\r\nxCwTYbQ597eWNn/RZDC7+EfF2s+cf8touZHLFt3VtAzyh9rlyepOwNQih7Gy\r\nQPNQw27gBHIzF+ql3Ui0EjKGSDWLySCNxpJ342+uTjBwVt/A0nHryR5lJb6i\r\nZRQ8ApMCT27cHgEr/tUkaDrp3go5cSgxBiXJPqZ8VeC5QKcLJg9+3pSU5zs1\r\n94sPnNqgP7sjsxgvwPrzuQP9zldh3q8a2QeCL+yBFsDadvrbE1VNlHZQuBmW\r\n3Q4UgUif/OQLJhJRgZtwhJvzLuEVwOh2ws0MRDtxYoC0jTOGCoHlq2/RvG/L\r\nxwY189A7XYvx5d4mXRa4RbfUqI599BGJqgaavuwca0p5pYk9R/Tfi5vht/Al\r\no0x2gzpozFpFvB68JsdWTBxBNJPqmL/26IA=\r\n=Rjbe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "9dca37f15de317fe9ad0ced907cdf250ba310880", "scripts": {"dist": "mkdirp dist && browserify --standalone Qs lib/index.js > dist/qs.js", "lint": "eslint --ext=js,mjs .", "test": "npm run tests-only", "readme": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run --silent readme && npm run --silent lint", "postlint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git' | grep -v dist/)", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "prepublishOnly": "safe-publish-latest && npm run dist"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/qs.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "_nodeVersion": "19.7.0", "dependencies": {"side-channel": "^1.0.4"}, "publishConfig": {"ignore": ["!dist/*", "bower.json", "component.json", ".github/workflows"]}, "_hasShrinkwrap": false, "devDependencies": {"aud": "^2.0.2", "nyc": "^10.3.2", "tape": "^5.6.3", "eclint": "^2.8.1", "eslint": "=8.8.0", "evalmd": "^0.0.19", "mkdirp": "^0.5.5", "for-each": "^0.3.3", "qs-iconv": "^1.0.4", "npmignore": "^0.3.0", "browserify": "^16.5.2", "iconv-lite": "^0.5.1", "in-publish": "^2.0.1", "has-symbols": "^1.0.3", "safer-buffer": "^2.1.2", "object-inspect": "^1.12.3", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/qs_6.11.1_1678141502979_0.23201624458108672", "host": "s3://npm-registry-packages"}}, "6.11.2": {"name": "qs", "version": "6.11.2", "keywords": ["querystring", "qs", "query", "url", "parse", "stringify"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@6.11.2", "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/qs", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "dist": {"shasum": "64bea51f12c1f5da1bc01496f48ffcff7c69d7d9", "tarball": "https://registry.npmjs.org/qs/-/qs-6.11.2.tgz", "fileCount": 18, "integrity": "sha512-tDNIz22aBzCDxLtVH++VnTfzxlfeK5CbqohpSqpJgj1Wg/cQbStNAz3NuqCs5vV+pjBsK4x4pN9HlVh7rcYRiA==", "signatures": [{"sig": "MEUCIB+fMu4NOJq7nmFfNqhMD2qf8cmFsuMCwoggeSq9mZ7/AiEAkTtWfoOsoVOR5sxAI654c6P4/cb9/GZxSzo5aaCrjkE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 241132}, "main": "lib/index.js", "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "410bdd3c8ae7f5d7ae9b52648b8642b8adc5e1c0", "scripts": {"dist": "mkdirp dist && browserify --standalone Qs lib/index.js > dist/qs.js", "lint": "eslint --ext=js,mjs .", "test": "npm run tests-only", "readme": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run --silent readme && npm run --silent lint", "postlint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git' | grep -v dist/)", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "prepublishOnly": "safe-publish-latest && npm run dist"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/qs.git", "type": "git"}, "_npmVersion": "9.6.4", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "_nodeVersion": "20.1.0", "dependencies": {"side-channel": "^1.0.4"}, "publishConfig": {"ignore": ["!dist/*", "bower.json", "component.json", ".github/workflows"]}, "_hasShrinkwrap": false, "devDependencies": {"aud": "^2.0.2", "nyc": "^10.3.2", "tape": "^5.6.3", "eclint": "^2.8.1", "eslint": "=8.8.0", "evalmd": "^0.0.19", "mkdirp": "^0.5.5", "for-each": "^0.3.3", "qs-iconv": "^1.0.4", "npmignore": "^0.3.0", "browserify": "^16.5.2", "iconv-lite": "^0.5.1", "in-publish": "^2.0.1", "has-symbols": "^1.0.3", "safer-buffer": "^2.1.2", "mock-property": "^1.0.0", "object-inspect": "^1.12.3", "safe-publish-latest": "^2.0.0", "has-override-mistake": "^1.0.0", "@ljharb/eslint-config": "^21.0.1", "has-property-descriptors": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/qs_6.11.2_1684166911484_0.2375668376641622", "host": "s3://npm-registry-packages"}}, "6.12.0": {"name": "qs", "version": "6.12.0", "keywords": ["querystring", "qs", "query", "url", "parse", "stringify"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@6.12.0", "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/qs", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "dist": {"shasum": "edd40c3b823995946a8a0b1f208669c7a200db77", "tarball": "https://registry.npmjs.org/qs/-/qs-6.12.0.tgz", "fileCount": 18, "integrity": "sha512-trVZiI6RMOkO476zLGaBIzszOdFPnCCXHPG9kn0yuS1uz6xdVxPfZdB3vUig9pxPFDM9BRAgz/YUIVQ1/vuiUg==", "signatures": [{"sig": "MEYCIQCgj2uVDUIu2e6Vjdz9YIZamGp0HYy9szXE3IUckG/NaAIhAIxvDNn8r7pR7mmr7eql4zM/RSiwoFuzZ8oDPYFkCP/i", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 245120}, "main": "lib/index.js", "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "062334aa584809829822c077f5f9f41c1e253dab", "scripts": {"dist": "mkdirp dist && browserify --standalone Qs -g unassertify -g @browserify/envify -g [@browserify/uglifyify --mangle.keep_fnames --compress.keep_fnames --format.indent_level=1 --compress.arrows=false --compress.passes=4 --compress.typeofs=false] -p common-shakeify -p bundle-collapser/plugin lib/index.js > dist/qs.js", "lint": "eslint --ext=js,mjs .", "test": "npm run tests-only", "readme": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated && npm run dist", "pretest": "npm run --silent readme && npm run --silent lint", "postlint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git' | grep -v dist/)", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/qs.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "sideEffects": false, "_nodeVersion": "21.6.2", "dependencies": {"side-channel": "^1.0.6"}, "publishConfig": {"ignore": ["!dist/*", "bower.json", "component.json", ".github/workflows", "logos"]}, "_hasShrinkwrap": false, "devDependencies": {"aud": "^2.0.4", "nyc": "^10.3.2", "glob": "=10.3.7", "tape": "^5.7.5", "eclint": "^2.8.1", "eslint": "=8.8.0", "evalmd": "^0.0.19", "mkdirp": "^0.5.5", "for-each": "^0.3.3", "qs-iconv": "^1.0.4", "jackspeak": "=2.1.1", "npmignore": "^0.3.1", "browserify": "^16.5.2", "iconv-lite": "^0.5.1", "in-publish": "^2.0.1", "has-symbols": "^1.0.3", "module-deps": "^6.2.3", "unassertify": "^3.0.1", "safer-buffer": "^2.1.2", "mock-property": "^1.0.3", "object-inspect": "^1.13.1", "common-shakeify": "~1.0.0", "bundle-collapser": "^1.4.0", "es-value-fixtures": "^1.4.2", "@browserify/envify": "^6.0.0", "safe-publish-latest": "^2.0.0", "has-override-mistake": "^1.0.1", "@browserify/uglifyify": "^6.0.0", "@ljharb/eslint-config": "^21.1.0", "has-property-descriptors": "^1.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/qs_6.12.0_1709748827789_0.6296145314628603", "host": "s3://npm-registry-packages"}}, "6.12.1": {"name": "qs", "version": "6.12.1", "keywords": ["querystring", "qs", "query", "url", "parse", "stringify"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@6.12.1", "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/qs", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "dist": {"shasum": "39422111ca7cbdb70425541cba20c7d7b216599a", "tarball": "https://registry.npmjs.org/qs/-/qs-6.12.1.tgz", "fileCount": 18, "integrity": "sha512-zWmv4RSuB9r2mYQw3zxQuHWeU+42aKi1wWig/j4ele4ygELZ7PEO6MM7rim9oAQH2A5MWfsAVf/jPvTPgCbvUQ==", "signatures": [{"sig": "MEYCIQDNGMI4dtDJeL8Kvi03KpGT9bPl80ZX3m7ZeDguwhL4OgIhAI9jJVNVGbzp/K9zoRPuqc8xVlofZsdrRS0Q4S4a6XCx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 247047}, "main": "lib/index.js", "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "59d765f6fb350ae33002003a5e37b65fdf31776c", "scripts": {"dist": "mkdirp dist && browserify --standalone Qs -g unassertify -g @browserify/envify -g [@browserify/uglifyify --mangle.keep_fnames --compress.keep_fnames --format.indent_level=1 --compress.arrows=false --compress.passes=4 --compress.typeofs=false] -p common-shakeify -p bundle-collapser/plugin lib/index.js > dist/qs.js", "lint": "eslint --ext=js,mjs .", "test": "npm run tests-only", "readme": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated && npm run dist", "pretest": "npm run --silent readme && npm run --silent lint", "postlint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git' | grep -v dist/)", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/qs.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "sideEffects": false, "_nodeVersion": "21.7.2", "dependencies": {"side-channel": "^1.0.6"}, "publishConfig": {"ignore": ["!dist/*", "bower.json", "component.json", ".github/workflows", "logos", "tea.yaml"]}, "_hasShrinkwrap": false, "devDependencies": {"aud": "^2.0.4", "nyc": "^10.3.2", "glob": "=10.3.7", "tape": "^5.7.5", "eclint": "^2.8.1", "eslint": "=8.8.0", "evalmd": "^0.0.19", "mkdirp": "^0.5.5", "for-each": "^0.3.3", "qs-iconv": "^1.0.4", "jackspeak": "=2.1.1", "npmignore": "^0.3.1", "browserify": "^16.5.2", "iconv-lite": "^0.5.1", "in-publish": "^2.0.1", "has-symbols": "^1.0.3", "module-deps": "^6.2.3", "unassertify": "^3.0.1", "safer-buffer": "^2.1.2", "mock-property": "^1.0.3", "object-inspect": "^1.13.1", "common-shakeify": "~1.0.0", "bundle-collapser": "^1.4.0", "es-value-fixtures": "^1.4.2", "@browserify/envify": "^6.0.0", "safe-publish-latest": "^2.0.0", "has-override-mistake": "^1.0.1", "@browserify/uglifyify": "^6.0.0", "@ljharb/eslint-config": "^21.1.0", "has-property-descriptors": "^1.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/qs_6.12.1_1712960664945_0.12975970608601028", "host": "s3://npm-registry-packages"}}, "6.12.2": {"name": "qs", "version": "6.12.2", "keywords": ["querystring", "qs", "query", "url", "parse", "stringify"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@6.12.2", "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/qs", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "dist": {"shasum": "5443b587f3bf73ac68968de491e5b25bafe04478", "tarball": "https://registry.npmjs.org/qs/-/qs-6.12.2.tgz", "fileCount": 18, "integrity": "sha512-x+NLUpx9SYrcwXtX7ob1gnkSems4i/mGZX5SlYxwIau6RrUSODO89TR/XDGGpn5RPWSYIB+aSfuSlV5+CmbTBg==", "signatures": [{"sig": "MEYCIQDsnERlMEd2GVkIyfzDQ5xZnvW3IpltvWdcpvNrAV8LWAIhAKHlZYH2sEAOBp1zwXYyS3NsO7ZrVKfw/hH5un67px/4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 248143}, "main": "lib/index.js", "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "d0dff11f06be1b2588e62865f5e4aa91f2dabafb", "scripts": {"dist": "mkdirp dist && browserify --standalone Qs -g unassertify -g @browserify/envify -g [@browserify/uglifyify --mangle.keep_fnames --compress.keep_fnames --format.indent_level=1 --compress.arrows=false --compress.passes=4 --compress.typeofs=false] -p common-shakeify -p bundle-collapser/plugin lib/index.js > dist/qs.js", "lint": "eslint --ext=js,mjs .", "test": "npm run tests-only", "readme": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated && npm run dist", "pretest": "npm run --silent readme && npm run --silent lint", "postlint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git' | grep -v dist/)", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/qs.git", "type": "git"}, "_npmVersion": "10.8.1", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "sideEffects": false, "_nodeVersion": "22.3.0", "dependencies": {"side-channel": "^1.0.6"}, "publishConfig": {"ignore": ["!dist/*", "bower.json", "component.json", ".github/workflows", "logos", "tea.yaml"]}, "_hasShrinkwrap": false, "devDependencies": {"aud": "^2.0.4", "nyc": "^10.3.2", "glob": "=10.3.7", "tape": "^5.8.1", "eclint": "^2.8.1", "eslint": "=8.8.0", "evalmd": "^0.0.19", "mkdirp": "^0.5.5", "for-each": "^0.3.3", "qs-iconv": "^1.0.4", "jackspeak": "=2.1.1", "npmignore": "^0.3.1", "browserify": "^16.5.2", "iconv-lite": "^0.5.1", "in-publish": "^2.0.1", "has-symbols": "^1.0.3", "module-deps": "^6.2.3", "unassertify": "^3.0.1", "safer-buffer": "^2.1.2", "mock-property": "^1.0.3", "object-inspect": "^1.13.2", "common-shakeify": "~1.0.0", "bundle-collapser": "^1.4.0", "es-value-fixtures": "^1.4.2", "@browserify/envify": "^6.0.0", "safe-publish-latest": "^2.0.0", "has-override-mistake": "^1.0.1", "@browserify/uglifyify": "^6.0.0", "@ljharb/eslint-config": "^21.1.1", "has-property-descriptors": "^1.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/qs_6.12.2_1719857475086_0.37421592673617043", "host": "s3://npm-registry-packages"}}, "6.12.3": {"name": "qs", "version": "6.12.3", "keywords": ["querystring", "qs", "query", "url", "parse", "stringify"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@6.12.3", "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/qs", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "dist": {"shasum": "e43ce03c8521b9c7fd7f1f13e514e5ca37727754", "tarball": "https://registry.npmjs.org/qs/-/qs-6.12.3.tgz", "fileCount": 18, "integrity": "sha512-AWJm14H1vVaO/iNZ4/hO+HyaTehuy9nRqVdkTqlJt0HWvBiBIEXFmb4C0DGeYo3Xes9rrEW+TxHsaigCbN5ICQ==", "signatures": [{"sig": "MEUCIBT1bwi8NkInW0EJaM4O1I1gtS5gebc3skkQUi+/SfE6AiEAjvN8HJz8DBm6mCqADcJ1FeBRz8N369nHgyV8umGh7Rc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 248957}, "main": "lib/index.js", "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "f90cc35dd65c7099c35ae75d7a1a67aab85220e1", "scripts": {"dist": "mkdirp dist && browserify --standalone Qs -g unassertify -g @browserify/envify -g [@browserify/uglifyify --mangle.keep_fnames --compress.keep_fnames --format.indent_level=1 --compress.arrows=false --compress.passes=4 --compress.typeofs=false] -p common-shakeify -p bundle-collapser/plugin lib/index.js > dist/qs.js", "lint": "eslint --ext=js,mjs .", "test": "npm run tests-only", "readme": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated && npm run dist", "pretest": "npm run --silent readme && npm run --silent lint", "postlint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git' | grep -v dist/)", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/qs.git", "type": "git"}, "_npmVersion": "10.8.1", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "sideEffects": false, "_nodeVersion": "22.4.1", "dependencies": {"side-channel": "^1.0.6"}, "publishConfig": {"ignore": ["!dist/*", "bower.json", "component.json", ".github/workflows", "logos", "tea.yaml"]}, "_hasShrinkwrap": false, "devDependencies": {"aud": "^2.0.4", "nyc": "^10.3.2", "glob": "=10.3.7", "tape": "^5.8.1", "eclint": "^2.8.1", "eslint": "=8.8.0", "evalmd": "^0.0.19", "mkdirp": "^0.5.5", "for-each": "^0.3.3", "qs-iconv": "^1.0.4", "jackspeak": "=2.1.1", "npmignore": "^0.3.1", "browserify": "^16.5.2", "iconv-lite": "^0.5.1", "in-publish": "^2.0.1", "has-symbols": "^1.0.3", "module-deps": "^6.2.3", "unassertify": "^3.0.1", "safer-buffer": "^2.1.2", "mock-property": "^1.0.3", "object-inspect": "^1.13.2", "common-shakeify": "~1.0.0", "bundle-collapser": "^1.4.0", "es-value-fixtures": "^1.4.2", "@browserify/envify": "^6.0.0", "safe-publish-latest": "^2.0.0", "has-override-mistake": "^1.0.1", "@browserify/uglifyify": "^6.0.0", "@ljharb/eslint-config": "^21.1.1", "has-property-descriptors": "^1.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/qs_6.12.3_1720482885198_0.5920756378065619", "host": "s3://npm-registry-packages"}}, "6.13.0": {"name": "qs", "version": "6.13.0", "keywords": ["querystring", "qs", "query", "url", "parse", "stringify"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@6.13.0", "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/qs", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "dist": {"shasum": "6ca3bd58439f7e245655798997787b0d88a51906", "tarball": "https://registry.npmjs.org/qs/-/qs-6.13.0.tgz", "fileCount": 18, "integrity": "sha512-+38qI9SOr8tfZ4QmJNplMUxqjbe7LKvvZgWdExBOmd+egZTtjLB67Gu0HRX3u/XOq7UU2Nx6nsjvS16Z9uwfpg==", "signatures": [{"sig": "MEUCIHiLAq9nCpmkElo6vUqMnCpbwypYPxjxs6JCSq0ij+2wAiEAhINc+RxTbFpCJMfBPcUdf3PKyGl4OkJB6NezEXO2SsA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 253581}, "main": "lib/index.js", "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "5cf516c0dd557d85d5f18d4a916c96cd9cfc2305", "scripts": {"dist": "mkdirp dist && browserify --standalone Qs -g unassertify -g @browserify/envify -g [@browserify/uglifyify --mangle.keep_fnames --compress.keep_fnames --format.indent_level=1 --compress.arrows=false --compress.passes=4 --compress.typeofs=false] -p common-shakeify -p bundle-collapser/plugin lib/index.js > dist/qs.js", "lint": "eslint --ext=js,mjs .", "test": "npm run tests-only", "readme": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated && npm run dist", "pretest": "npm run --silent readme && npm run --silent lint", "postlint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git' | grep -v dist/)", "posttest": "npx npm@'>=10.2' audit --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/qs.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "sideEffects": false, "_nodeVersion": "22.5.1", "dependencies": {"side-channel": "^1.0.6"}, "publishConfig": {"ignore": ["!dist/*", "bower.json", "component.json", ".github/workflows", "logos", "tea.yaml"]}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^10.3.2", "glob": "=10.3.7", "tape": "^5.8.1", "eclint": "^2.8.1", "eslint": "=8.8.0", "evalmd": "^0.0.19", "mkdirp": "^0.5.5", "for-each": "^0.3.3", "qs-iconv": "^1.0.4", "jackspeak": "=2.1.1", "npmignore": "^0.3.1", "browserify": "^16.5.2", "iconv-lite": "^0.5.1", "in-publish": "^2.0.1", "has-symbols": "^1.0.3", "module-deps": "^6.2.3", "unassertify": "^3.0.1", "safer-buffer": "^2.1.2", "mock-property": "^1.0.3", "object-inspect": "^1.13.2", "common-shakeify": "~1.0.0", "bundle-collapser": "^1.4.0", "es-value-fixtures": "^1.4.2", "@browserify/envify": "^6.0.0", "safe-publish-latest": "^2.0.0", "has-override-mistake": "^1.0.1", "@browserify/uglifyify": "^6.0.0", "@ljharb/eslint-config": "^21.1.1", "has-property-descriptors": "^1.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/qs_6.13.0_1722532743365_0.07587892650543049", "host": "s3://npm-registry-packages"}}, "6.13.1": {"name": "qs", "version": "6.13.1", "keywords": ["querystring", "qs", "query", "url", "parse", "stringify"], "license": "BSD-3-<PERSON><PERSON>", "_id": "qs@6.13.1", "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/qs", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "dist": {"shasum": "3ce5fc72bd3a8171b85c99b93c65dd20b7d1b16e", "tarball": "https://registry.npmjs.org/qs/-/qs-6.13.1.tgz", "fileCount": 18, "integrity": "sha512-EJPeIn0CYrGu+hli1xilKAPXODtJ12T0sP63Ijx2/khC2JtuaN3JyNIpvmnkmaEtha9ocbG4A4cMcr+TvqvwQg==", "signatures": [{"sig": "MEUCIBDRO5pwnTw4F/PNiyIeIO4QqRJUmC6NT32klFMKuYZ2AiEAxZkv92LnfYGQWDuH2d5ZcFy5wmTRjJ8N264LeoN61ok=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 255728}, "main": "lib/index.js", "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "f1ee0376c1dfd06606520f3268ee0c5f3aaece65", "scripts": {"dist": "mkdirp dist && browserify --standalone Qs -g unassertify -g @browserify/envify -g [@browserify/uglifyify --mangle.keep_fnames --compress.keep_fnames --format.indent_level=1 --compress.arrows=false --compress.passes=4 --compress.typeofs=false] -p common-shakeify -p bundle-collapser/plugin lib/index.js > dist/qs.js", "lint": "eslint --ext=js,mjs .", "test": "npm run tests-only", "readme": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated && npm run dist", "pretest": "npm run --silent readme && npm run --silent lint", "postlint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git' | grep -v dist/)", "posttest": "npx npm@'>=10.2' audit --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/qs.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "directories": {}, "sideEffects": false, "_nodeVersion": "23.2.0", "dependencies": {"side-channel": "^1.0.6"}, "publishConfig": {"ignore": ["!dist/*", "bower.json", "component.json", ".github/workflows", "logos", "tea.yaml"]}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^10.3.2", "glob": "=10.3.7", "tape": "^5.9.0", "eclint": "^2.8.1", "eslint": "=8.8.0", "evalmd": "^0.0.19", "mkdirp": "^0.5.5", "for-each": "^0.3.3", "qs-iconv": "^1.0.4", "has-proto": "^1.0.3", "jackspeak": "=2.1.1", "npmignore": "^0.3.1", "browserify": "^16.5.2", "iconv-lite": "^0.5.1", "in-publish": "^2.0.1", "has-bigints": "^1.0.2", "has-symbols": "^1.0.3", "module-deps": "^6.2.3", "unassertify": "^3.0.1", "safer-buffer": "^2.1.2", "mock-property": "^1.1.0", "object-inspect": "^1.13.3", "common-shakeify": "~1.0.0", "bundle-collapser": "^1.4.0", "es-value-fixtures": "^1.5.0", "@browserify/envify": "^6.0.0", "safe-publish-latest": "^2.0.0", "has-override-mistake": "^1.0.1", "@browserify/uglifyify": "^6.0.0", "@ljharb/eslint-config": "^21.1.1", "has-property-descriptors": "^1.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/qs_6.13.1_1731868387780_0.15999772980361282", "host": "s3://npm-registry-packages"}}, "6.14.0": {"name": "qs", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "homepage": "https://github.com/ljharb/qs", "version": "6.14.0", "repository": {"type": "git", "url": "git+https://github.com/ljharb/qs.git"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "main": "lib/index.js", "sideEffects": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}], "keywords": ["querystring", "qs", "query", "url", "parse", "stringify"], "engines": {"node": ">=0.6"}, "dependencies": {"side-channel": "^1.1.0"}, "devDependencies": {"@browserify/envify": "^6.0.0", "@browserify/uglifyify": "^6.0.0", "@ljharb/eslint-config": "^21.1.1", "browserify": "^16.5.2", "bundle-collapser": "^1.4.0", "common-shakeify": "~1.0.0", "eclint": "^2.8.1", "es-value-fixtures": "^1.7.0", "eslint": "=8.8.0", "evalmd": "^0.0.19", "for-each": "^0.3.3", "glob": "=10.3.7", "has-bigints": "^1.1.0", "has-override-mistake": "^1.0.1", "has-property-descriptors": "^1.0.2", "has-proto": "^1.2.0", "has-symbols": "^1.1.0", "iconv-lite": "^0.5.1", "in-publish": "^2.0.1", "jackspeak": "=2.1.1", "mkdirp": "^0.5.5", "mock-property": "^1.1.0", "module-deps": "^6.2.3", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.3", "qs-iconv": "^1.0.4", "safe-publish-latest": "^2.0.0", "safer-buffer": "^2.1.2", "tape": "^5.9.0", "unassertify": "^3.0.1"}, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated && npm run dist", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "pretest": "npm run --silent readme && npm run --silent lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "posttest": "npx npm@'>=10.2' audit --production", "readme": "evalmd README.md", "postlint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git' | grep -v dist/)", "lint": "eslint --ext=js,mjs .", "dist": "mkdirp dist && browserify --standalone Qs -g unassertify -g @browserify/envify -g [@browserify/uglifyify --mangle.keep_fnames --compress.keep_fnames --format.indent_level=1 --compress.arrows=false --compress.passes=4 --compress.typeofs=false] -p common-shakeify -p bundle-collapser/plugin lib/index.js > dist/qs.js"}, "license": "BSD-3-<PERSON><PERSON>", "publishConfig": {"ignore": ["!dist/*", "bower.json", "component.json", ".github/workflows", "logos", "tea.yaml"]}, "_id": "qs@6.14.0", "gitHead": "993fe619f95db97ec5408ad9d78e2f0b75ae2a7a", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "_nodeVersion": "23.6.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-YWWTjgABSKcvs/nWBi9PycY/JiPJqOD4JA6o9Sej2AtvSGarXxKC3OQSk4pAarbdQlKAh5D4FCQkJNkW+GAn3w==", "shasum": "c63fa40680d2c5c941412a0e899c89af60c0a930", "tarball": "https://registry.npmjs.org/qs/-/qs-6.14.0.tgz", "fileCount": 18, "unpackedSize": 268238, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD1QJ1K7hNmkxfoY5oNC+tzgMzM/xccHFPKLoDnOHuCWQIgCRvEHLllF6cw716RSuEwa2ThEFGpKWUNb7ByPzsaI2Y="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/qs_6.14.0_1736877738397_0.059996334013611374"}, "_hasShrinkwrap": false}}, "time": {"created": "2011-02-04T04:40:23.617Z", "modified": "2025-01-14T18:02:18.864Z", "0.0.1": "2011-02-04T04:40:23.984Z", "0.0.2": "2011-02-07T16:45:02.442Z", "0.0.3": "2011-02-09T00:52:16.616Z", "0.0.4": "2011-02-09T20:45:35.890Z", "0.0.5": "2011-02-10T23:45:02.307Z", "0.0.6": "2011-02-14T22:27:23.241Z", "0.0.7": "2011-03-13T17:20:41.673Z", "0.1.0": "2011-04-13T18:07:22.394Z", "0.2.0": "2011-06-29T16:33:55.231Z", "0.3.0": "2011-07-19T19:07:48.886Z", "0.3.1": "2011-11-04T16:33:52.613Z", "0.3.2": "2011-11-09T03:42:13.569Z", "0.4.0": "2011-11-22T02:27:15.971Z", "0.4.1": "2012-01-26T13:40:54.775Z", "0.4.2": "2012-02-08T21:10:43.986Z", "0.5.0": "2012-05-04T21:42:09.623Z", "0.5.1": "2012-09-18T18:43:17.048Z", "0.5.2": "2012-11-14T19:56:00.323Z", "0.5.3": "2012-12-10T00:18:05.350Z", "0.5.4": "2013-03-15T23:33:06.012Z", "0.5.5": "2013-03-20T19:37:04.472Z", "0.5.6": "2013-04-09T17:16:57.783Z", "0.6.0": "2013-04-23T15:59:26.281Z", "0.6.1": "2013-04-26T20:11:41.989Z", "0.6.2": "2013-05-02T20:58:43.034Z", "0.6.3": "2013-05-03T20:05:29.458Z", "0.6.4": "2013-05-07T14:51:56.116Z", "0.6.5": "2013-05-13T15:40:34.969Z", "0.6.6": "2013-12-03T16:46:29.059Z", "1.0.0": "2014-08-04T22:27:58.076Z", "1.0.1": "2014-08-05T21:32:57.002Z", "1.0.2": "2014-08-06T00:17:36.599Z", "1.1.0": "2014-08-06T17:42:49.341Z", "1.2.0": "2014-08-07T14:01:10.992Z", "1.2.1": "2014-08-09T01:37:21.982Z", "1.2.2": "2014-08-14T20:14:29.160Z", "2.0.0": "2014-08-25T20:02:15.954Z", "2.1.0": "2014-08-25T22:43:41.944Z", "2.2.0": "2014-08-27T16:34:02.261Z", "2.2.1": "2014-08-28T20:36:15.270Z", "2.2.2": "2014-08-29T20:34:11.657Z", "2.2.3": "2014-09-05T18:30:40.705Z", "2.2.4": "2014-09-18T22:58:21.311Z", "2.2.5": "2014-10-22T21:38:19.356Z", "2.3.0": "2014-10-22T21:47:54.756Z", "2.3.1": "2014-10-23T16:26:22.009Z", "2.3.2": "2014-10-28T00:07:01.669Z", "2.3.3": "2014-11-14T00:54:19.016Z", "2.4.0": "2015-03-12T17:22:55.602Z", "2.4.1": "2015-03-13T23:38:45.229Z", "2.4.2": "2015-05-09T20:55:52.199Z", "3.0.0": "2015-05-22T19:34:21.873Z", "3.1.0": "2015-05-27T16:11:47.363Z", "4.0.0": "2015-07-02T18:33:51.252Z", "5.0.0": "2015-08-27T17:43:00.325Z", "5.1.0": "2015-09-11T17:10:21.549Z", "5.2.0": "2015-10-07T17:36:24.037Z", "6.0.0": "2015-11-03T03:02:36.639Z", "6.0.1": "2015-11-24T17:04:02.325Z", "6.0.2": "2016-01-17T22:58:43.204Z", "6.1.0": "2016-02-04T05:59:45.711Z", "6.2.0": "2016-05-08T23:15:52.801Z", "5.2.1": "2016-07-20T19:37:41.185Z", "6.2.1": "2016-07-20T20:02:09.943Z", "6.3.0": "2016-10-17T00:26:31.012Z", "6.3.1": "2017-02-16T04:40:59.014Z", "6.1.1": "2017-02-16T06:41:46.130Z", "6.0.3": "2017-02-16T07:05:51.249Z", "6.2.2": "2017-02-16T07:44:35.992Z", "6.4.0": "2017-03-06T07:03:28.551Z", "6.3.2": "2017-03-06T09:02:15.066Z", "6.2.3": "2017-03-06T16:50:31.546Z", "6.1.2": "2017-03-06T16:50:51.782Z", "6.0.4": "2017-03-06T16:51:48.044Z", "6.5.0": "2017-06-28T07:08:19.489Z", "6.5.1": "2017-09-09T07:54:59.287Z", "6.5.2": "2018-05-04T06:06:13.890Z", "6.6.0": "2018-11-25T06:44:41.677Z", "6.7.0": "2019-03-22T20:48:51.184Z", "6.8.0": "2019-08-17T02:45:35.763Z", "6.9.0": "2019-09-21T22:26:19.096Z", "6.9.1": "2019-11-08T06:46:35.355Z", "6.9.2": "2020-03-22T15:39:34.376Z", "6.8.1": "2020-03-24T04:47:49.232Z", "6.7.1": "2020-03-24T15:25:23.409Z", "6.7.2": "2020-03-25T19:17:23.223Z", "6.8.2": "2020-03-25T19:47:25.334Z", "6.9.3": "2020-03-25T20:35:57.060Z", "6.9.4": "2020-05-03T21:52:50.717Z", "6.9.5": "2021-01-13T16:29:50.613Z", "6.9.6": "2021-01-14T04:53:48.650Z", "6.10.0": "2021-03-18T19:39:17.956Z", "6.10.1": "2021-03-22T03:53:52.823Z", "6.10.2": "2021-12-06T05:47:27.638Z", "6.2.4": "2022-01-11T05:57:35.218Z", "6.3.3": "2022-01-11T05:59:16.690Z", "6.4.1": "2022-01-11T05:59:22.055Z", "6.5.3": "2022-01-11T05:59:27.508Z", "6.6.1": "2022-01-11T05:59:34.289Z", "6.7.3": "2022-01-11T05:59:39.213Z", "6.8.3": "2022-01-11T05:59:46.325Z", "6.9.7": "2022-01-11T05:59:51.851Z", "6.10.3": "2022-01-11T06:00:21.635Z", "6.10.4": "2022-06-06T19:07:09.851Z", "6.10.5": "2022-06-06T22:52:18.062Z", "6.11.0": "2022-06-27T04:49:53.976Z", "6.11.1": "2023-03-06T22:25:03.220Z", "6.11.2": "2023-05-15T16:08:31.665Z", "6.12.0": "2024-03-06T18:13:47.987Z", "6.12.1": "2024-04-12T22:24:25.107Z", "6.12.2": "2024-07-01T18:11:15.337Z", "6.12.3": "2024-07-08T23:54:45.404Z", "6.13.0": "2024-08-01T17:19:03.605Z", "6.13.1": "2024-11-17T18:33:07.980Z", "6.14.0": "2025-01-14T18:02:18.697Z"}, "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "license": "BSD-3-<PERSON><PERSON>", "homepage": "https://github.com/ljharb/qs", "keywords": ["querystring", "qs", "query", "url", "parse", "stringify"], "repository": {"type": "git", "url": "git+https://github.com/ljharb/qs.git"}, "description": "A querystring parser that supports nesting and arrays, with a depth limit", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}], "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "readme": "<p align=\"center\">\n    <img alt=\"qs\" src=\"./logos/banner_default.png\" width=\"800\" />\n</p>\n\n# qs <sup>[![Version Badge][npm-version-svg]][package-url]</sup>\n\n[![github actions][actions-image]][actions-url]\n[![coverage][codecov-image]][codecov-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n[![CII Best Practices](https://bestpractices.coreinfrastructure.org/projects/9058/badge)](https://bestpractices.coreinfrastructure.org/projects/9058)\n\n[![npm badge][npm-badge-png]][package-url]\n\nA querystring parsing and stringifying library with some added security.\n\nLead Maintainer: [<PERSON>](https://github.com/ljharb)\n\nThe **qs** module was originally created and maintained by [<PERSON><PERSON>](https://github.com/visionmedia/node-querystring).\n\n## Usage\n\n```javascript\nvar qs = require('qs');\nvar assert = require('assert');\n\nvar obj = qs.parse('a=c');\nassert.deepEqual(obj, { a: 'c' });\n\nvar str = qs.stringify(obj);\nassert.equal(str, 'a=c');\n```\n\n### Parsing Objects\n\n[](#preventEval)\n```javascript\nqs.parse(string, [options]);\n```\n\n**qs** allows you to create nested objects within your query strings, by surrounding the name of sub-keys with square brackets `[]`.\nFor example, the string `'foo[bar]=baz'` converts to:\n\n```javascript\nassert.deepEqual(qs.parse('foo[bar]=baz'), {\n    foo: {\n        bar: 'baz'\n    }\n});\n```\n\nWhen using the `plainObjects` option the parsed value is returned as a null object, created via `{ __proto__: null }` and as such you should be aware that prototype methods will not exist on it and a user may set those names to whatever value they like:\n\n```javascript\nvar nullObject = qs.parse('a[hasOwnProperty]=b', { plainObjects: true });\nassert.deepEqual(nullObject, { a: { hasOwnProperty: 'b' } });\n```\n\nBy default parameters that would overwrite properties on the object prototype are ignored, if you wish to keep the data from those fields either use `plainObjects` as mentioned above, or set `allowPrototypes` to `true` which will allow user input to overwrite those properties.\n*WARNING* It is generally a bad idea to enable this option as it can cause problems when attempting to use the properties that have been overwritten.\nAlways be careful with this option.\n\n```javascript\nvar protoObject = qs.parse('a[hasOwnProperty]=b', { allowPrototypes: true });\nassert.deepEqual(protoObject, { a: { hasOwnProperty: 'b' } });\n```\n\nURI encoded strings work too:\n\n```javascript\nassert.deepEqual(qs.parse('a%5Bb%5D=c'), {\n    a: { b: 'c' }\n});\n```\n\nYou can also nest your objects, like `'foo[bar][baz]=foobarbaz'`:\n\n```javascript\nassert.deepEqual(qs.parse('foo[bar][baz]=foobarbaz'), {\n    foo: {\n        bar: {\n            baz: 'foobarbaz'\n        }\n    }\n});\n```\n\nBy default, when nesting objects **qs** will only parse up to 5 children deep.\nThis means if you attempt to parse a string like `'a[b][c][d][e][f][g][h][i]=j'` your resulting object will be:\n\n```javascript\nvar expected = {\n    a: {\n        b: {\n            c: {\n                d: {\n                    e: {\n                        f: {\n                            '[g][h][i]': 'j'\n                        }\n                    }\n                }\n            }\n        }\n    }\n};\nvar string = 'a[b][c][d][e][f][g][h][i]=j';\nassert.deepEqual(qs.parse(string), expected);\n```\n\nThis depth can be overridden by passing a `depth` option to `qs.parse(string, [options])`:\n\n```javascript\nvar deep = qs.parse('a[b][c][d][e][f][g][h][i]=j', { depth: 1 });\nassert.deepEqual(deep, { a: { b: { '[c][d][e][f][g][h][i]': 'j' } } });\n```\n\nYou can configure **qs** to throw an error when parsing nested input beyond this depth using the `strictDepth` option (defaulted to false):\n\n```javascript\ntry {\n    qs.parse('a[b][c][d][e][f][g][h][i]=j', { depth: 1, strictDepth: true });\n} catch (err) {\n    assert(err instanceof RangeError);\n    assert.strictEqual(err.message, 'Input depth exceeded depth option of 1 and strictDepth is true');\n}\n```\n\nThe depth limit helps mitigate abuse when **qs** is used to parse user input, and it is recommended to keep it a reasonably small number. The strictDepth option adds a layer of protection by throwing an error when the limit is exceeded, allowing you to catch and handle such cases.\n\nFor similar reasons, by default **qs** will only parse up to 1000 parameters. This can be overridden by passing a `parameterLimit` option:\n\n```javascript\nvar limited = qs.parse('a=b&c=d', { parameterLimit: 1 });\nassert.deepEqual(limited, { a: 'b' });\n```\n\nIf you want an error to be thrown whenever the a limit is exceeded (eg, `parameterLimit`, `arrayLimit`), set the `throwOnLimitExceeded` option to `true`. This option will generate a descriptive error if the query string exceeds a configured limit.\n```javascript\ntry {\n    qs.parse('a=1&b=2&c=3&d=4', { parameterLimit: 3, throwOnLimitExceeded: true });\n} catch (err) {\n    assert(err instanceof Error);\n    assert.strictEqual(err.message, 'Parameter limit exceeded. Only 3 parameters allowed.');\n}\n```\n\nWhen `throwOnLimitExceeded` is set to `false` (default), **qs** will parse up to the specified `parameterLimit` and ignore the rest without throwing an error.\n\nTo bypass the leading question mark, use `ignoreQueryPrefix`:\n\n```javascript\nvar prefixed = qs.parse('?a=b&c=d', { ignoreQueryPrefix: true });\nassert.deepEqual(prefixed, { a: 'b', c: 'd' });\n```\n\nAn optional delimiter can also be passed:\n\n```javascript\nvar delimited = qs.parse('a=b;c=d', { delimiter: ';' });\nassert.deepEqual(delimited, { a: 'b', c: 'd' });\n```\n\nDelimiters can be a regular expression too:\n\n```javascript\nvar regexed = qs.parse('a=b;c=d,e=f', { delimiter: /[;,]/ });\nassert.deepEqual(regexed, { a: 'b', c: 'd', e: 'f' });\n```\n\nOption `allowDots` can be used to enable dot notation:\n\n```javascript\nvar withDots = qs.parse('a.b=c', { allowDots: true });\nassert.deepEqual(withDots, { a: { b: 'c' } });\n```\n\nOption `decodeDotInKeys` can be used to decode dots in keys\nNote: it implies `allowDots`, so `parse` will error if you set `decodeDotInKeys` to `true`, and `allowDots` to `false`.\n\n```javascript\nvar withDots = qs.parse('name%252Eobj.first=John&name%252Eobj.last=Doe', { decodeDotInKeys: true });\nassert.deepEqual(withDots, { 'name.obj': { first: 'John', last: 'Doe' }});\n```\n\nOption `allowEmptyArrays` can be used to allowing empty array values in object\n```javascript\nvar withEmptyArrays = qs.parse('foo[]&bar=baz', { allowEmptyArrays: true });\nassert.deepEqual(withEmptyArrays, { foo: [], bar: 'baz' });\n```\n\nOption `duplicates` can be used to change the behavior when duplicate keys are encountered\n```javascript\nassert.deepEqual(qs.parse('foo=bar&foo=baz'), { foo: ['bar', 'baz'] });\nassert.deepEqual(qs.parse('foo=bar&foo=baz', { duplicates: 'combine' }), { foo: ['bar', 'baz'] });\nassert.deepEqual(qs.parse('foo=bar&foo=baz', { duplicates: 'first' }), { foo: 'bar' });\nassert.deepEqual(qs.parse('foo=bar&foo=baz', { duplicates: 'last' }), { foo: 'baz' });\n```\n\nIf you have to deal with legacy browsers or services, there's also support for decoding percent-encoded octets as iso-8859-1:\n\n```javascript\nvar oldCharset = qs.parse('a=%A7', { charset: 'iso-8859-1' });\nassert.deepEqual(oldCharset, { a: '§' });\n```\n\nSome services add an initial `utf8=✓` value to forms so that old Internet Explorer versions are more likely to submit the form as utf-8.\nAdditionally, the server can check the value against wrong encodings of the checkmark character and detect that a query string or `application/x-www-form-urlencoded` body was *not* sent as utf-8, eg. if the form had an `accept-charset` parameter or the containing page had a different character set.\n\n**qs** supports this mechanism via the `charsetSentinel` option.\nIf specified, the `utf8` parameter will be omitted from the returned object.\nIt will be used to switch to `iso-8859-1`/`utf-8` mode depending on how the checkmark is encoded.\n\n**Important**: When you specify both the `charset` option and the `charsetSentinel` option, the `charset` will be overridden when the request contains a `utf8` parameter from which the actual charset can be deduced.\nIn that sense the `charset` will behave as the default charset rather than the authoritative charset.\n\n```javascript\nvar detectedAsUtf8 = qs.parse('utf8=%E2%9C%93&a=%C3%B8', {\n    charset: 'iso-8859-1',\n    charsetSentinel: true\n});\nassert.deepEqual(detectedAsUtf8, { a: 'ø' });\n\n// Browsers encode the checkmark as &#10003; when submitting as iso-8859-1:\nvar detectedAsIso8859_1 = qs.parse('utf8=%26%2310003%3B&a=%F8', {\n    charset: 'utf-8',\n    charsetSentinel: true\n});\nassert.deepEqual(detectedAsIso8859_1, { a: 'ø' });\n```\n\nIf you want to decode the `&#...;` syntax to the actual character, you can specify the `interpretNumericEntities` option as well:\n\n```javascript\nvar detectedAsIso8859_1 = qs.parse('a=%26%239786%3B', {\n    charset: 'iso-8859-1',\n    interpretNumericEntities: true\n});\nassert.deepEqual(detectedAsIso8859_1, { a: '☺' });\n```\n\nIt also works when the charset has been detected in `charsetSentinel` mode.\n\n### Parsing Arrays\n\n**qs** can also parse arrays using a similar `[]` notation:\n\n```javascript\nvar withArray = qs.parse('a[]=b&a[]=c');\nassert.deepEqual(withArray, { a: ['b', 'c'] });\n```\n\nYou may specify an index as well:\n\n```javascript\nvar withIndexes = qs.parse('a[1]=c&a[0]=b');\nassert.deepEqual(withIndexes, { a: ['b', 'c'] });\n```\n\nNote that the only difference between an index in an array and a key in an object is that the value between the brackets must be a number to create an array.\nWhen creating arrays with specific indices, **qs** will compact a sparse array to only the existing values preserving their order:\n\n```javascript\nvar noSparse = qs.parse('a[1]=b&a[15]=c');\nassert.deepEqual(noSparse, { a: ['b', 'c'] });\n```\n\nYou may also use `allowSparse` option to parse sparse arrays:\n\n```javascript\nvar sparseArray = qs.parse('a[1]=2&a[3]=5', { allowSparse: true });\nassert.deepEqual(sparseArray, { a: [, '2', , '5'] });\n```\n\nNote that an empty string is also a value, and will be preserved:\n\n```javascript\nvar withEmptyString = qs.parse('a[]=&a[]=b');\nassert.deepEqual(withEmptyString, { a: ['', 'b'] });\n\nvar withIndexedEmptyString = qs.parse('a[0]=b&a[1]=&a[2]=c');\nassert.deepEqual(withIndexedEmptyString, { a: ['b', '', 'c'] });\n```\n\n**qs** will also limit specifying indices in an array to a maximum index of `20`.\nAny array members with an index of greater than `20` will instead be converted to an object with the index as the key.\nThis is needed to handle cases when someone sent, for example, `a[999999999]` and it will take significant time to iterate over this huge array.\n\n```javascript\nvar withMaxIndex = qs.parse('a[100]=b');\nassert.deepEqual(withMaxIndex, { a: { '100': 'b' } });\n```\n\nThis limit can be overridden by passing an `arrayLimit` option:\n\n```javascript\nvar withArrayLimit = qs.parse('a[1]=b', { arrayLimit: 0 });\nassert.deepEqual(withArrayLimit, { a: { '1': 'b' } });\n```\n\nIf you want to throw an error whenever the array limit is exceeded, set the `throwOnLimitExceeded` option to `true`. This option will generate a descriptive error if the query string exceeds a configured limit.\n```javascript\ntry {\n    qs.parse('a[1]=b', { arrayLimit: 0, throwOnLimitExceeded: true });\n} catch (err) {\n    assert(err instanceof Error);\n    assert.strictEqual(err.message, 'Array limit exceeded. Only 0 elements allowed in an array.');\n}\n```\n\nWhen `throwOnLimitExceeded` is set to `false` (default), **qs** will parse up to the specified `arrayLimit` and if the limit is exceeded, the array will instead be converted to an object with the index as the key\n\nTo disable array parsing entirely, set `parseArrays` to `false`.\n\n```javascript\nvar noParsingArrays = qs.parse('a[]=b', { parseArrays: false });\nassert.deepEqual(noParsingArrays, { a: { '0': 'b' } });\n```\n\nIf you mix notations, **qs** will merge the two items into an object:\n\n```javascript\nvar mixedNotation = qs.parse('a[0]=b&a[b]=c');\nassert.deepEqual(mixedNotation, { a: { '0': 'b', b: 'c' } });\n```\n\nYou can also create arrays of objects:\n\n```javascript\nvar arraysOfObjects = qs.parse('a[][b]=c');\nassert.deepEqual(arraysOfObjects, { a: [{ b: 'c' }] });\n```\n\nSome people use comma to join array, **qs** can parse it:\n```javascript\nvar arraysOfObjects = qs.parse('a=b,c', { comma: true })\nassert.deepEqual(arraysOfObjects, { a: ['b', 'c'] })\n```\n(_this cannot convert nested objects, such as `a={b:1},{c:d}`_)\n\n### Parsing primitive/scalar values (numbers, booleans, null, etc)\n\nBy default, all values are parsed as strings.\nThis behavior will not change and is explained in [issue #91](https://github.com/ljharb/qs/issues/91).\n\n```javascript\nvar primitiveValues = qs.parse('a=15&b=true&c=null');\nassert.deepEqual(primitiveValues, { a: '15', b: 'true', c: 'null' });\n```\n\nIf you wish to auto-convert values which look like numbers, booleans, and other values into their primitive counterparts, you can use the [query-types Express JS middleware](https://github.com/xpepermint/query-types) which will auto-convert all request query parameters.\n\n### Stringifying\n\n[](#preventEval)\n```javascript\nqs.stringify(object, [options]);\n```\n\nWhen stringifying, **qs** by default URI encodes output. Objects are stringified as you would expect:\n\n```javascript\nassert.equal(qs.stringify({ a: 'b' }), 'a=b');\nassert.equal(qs.stringify({ a: { b: 'c' } }), 'a%5Bb%5D=c');\n```\n\nThis encoding can be disabled by setting the `encode` option to `false`:\n\n```javascript\nvar unencoded = qs.stringify({ a: { b: 'c' } }, { encode: false });\nassert.equal(unencoded, 'a[b]=c');\n```\n\nEncoding can be disabled for keys by setting the `encodeValuesOnly` option to `true`:\n```javascript\nvar encodedValues = qs.stringify(\n    { a: 'b', c: ['d', 'e=f'], f: [['g'], ['h']] },\n    { encodeValuesOnly: true }\n);\nassert.equal(encodedValues,'a=b&c[0]=d&c[1]=e%3Df&f[0][0]=g&f[1][0]=h');\n```\n\nThis encoding can also be replaced by a custom encoding method set as `encoder` option:\n\n```javascript\nvar encoded = qs.stringify({ a: { b: 'c' } }, { encoder: function (str) {\n    // Passed in values `a`, `b`, `c`\n    return // Return encoded string\n}})\n```\n\n_(Note: the `encoder` option does not apply if `encode` is `false`)_\n\nAnalogue to the `encoder` there is a `decoder` option for `parse` to override decoding of properties and values:\n\n```javascript\nvar decoded = qs.parse('x=z', { decoder: function (str) {\n    // Passed in values `x`, `z`\n    return // Return decoded string\n}})\n```\n\nYou can encode keys and values using different logic by using the type argument provided to the encoder:\n\n```javascript\nvar encoded = qs.stringify({ a: { b: 'c' } }, { encoder: function (str, defaultEncoder, charset, type) {\n    if (type === 'key') {\n        return // Encoded key\n    } else if (type === 'value') {\n        return // Encoded value\n    }\n}})\n```\n\nThe type argument is also provided to the decoder:\n\n```javascript\nvar decoded = qs.parse('x=z', { decoder: function (str, defaultDecoder, charset, type) {\n    if (type === 'key') {\n        return // Decoded key\n    } else if (type === 'value') {\n        return // Decoded value\n    }\n}})\n```\n\nExamples beyond this point will be shown as though the output is not URI encoded for clarity.\nPlease note that the return values in these cases *will* be URI encoded during real usage.\n\nWhen arrays are stringified, they follow the `arrayFormat` option, which defaults to `indices`:\n\n```javascript\nqs.stringify({ a: ['b', 'c', 'd'] });\n// 'a[0]=b&a[1]=c&a[2]=d'\n```\n\nYou may override this by setting the `indices` option to `false`, or to be more explicit, the `arrayFormat` option to `repeat`:\n\n```javascript\nqs.stringify({ a: ['b', 'c', 'd'] }, { indices: false });\n// 'a=b&a=c&a=d'\n```\n\nYou may use the `arrayFormat` option to specify the format of the output array:\n\n```javascript\nqs.stringify({ a: ['b', 'c'] }, { arrayFormat: 'indices' })\n// 'a[0]=b&a[1]=c'\nqs.stringify({ a: ['b', 'c'] }, { arrayFormat: 'brackets' })\n// 'a[]=b&a[]=c'\nqs.stringify({ a: ['b', 'c'] }, { arrayFormat: 'repeat' })\n// 'a=b&a=c'\nqs.stringify({ a: ['b', 'c'] }, { arrayFormat: 'comma' })\n// 'a=b,c'\n```\n\nNote: when using `arrayFormat` set to `'comma'`, you can also pass the `commaRoundTrip` option set to `true` or `false`, to append `[]` on single-item arrays, so that they can round trip through a parse.\n\nWhen objects are stringified, by default they use bracket notation:\n\n```javascript\nqs.stringify({ a: { b: { c: 'd', e: 'f' } } });\n// 'a[b][c]=d&a[b][e]=f'\n```\n\nYou may override this to use dot notation by setting the `allowDots` option to `true`:\n\n```javascript\nqs.stringify({ a: { b: { c: 'd', e: 'f' } } }, { allowDots: true });\n// 'a.b.c=d&a.b.e=f'\n```\n\nYou may encode the dot notation in the keys of object with option `encodeDotInKeys` by setting it to `true`:\nNote: it implies `allowDots`, so `stringify` will error if you set `decodeDotInKeys` to `true`, and `allowDots` to `false`.\nCaveat: when `encodeValuesOnly` is `true` as well as `encodeDotInKeys`, only dots in keys and nothing else will be encoded.\n```javascript\nqs.stringify({ \"name.obj\": { \"first\": \"John\", \"last\": \"Doe\" } }, { allowDots: true, encodeDotInKeys: true })\n// 'name%252Eobj.first=John&name%252Eobj.last=Doe'\n```\n\nYou may allow empty array values by setting the `allowEmptyArrays` option to `true`:\n```javascript\nqs.stringify({ foo: [], bar: 'baz' }, { allowEmptyArrays: true });\n// 'foo[]&bar=baz'\n```\n\nEmpty strings and null values will omit the value, but the equals sign (=) remains in place:\n\n```javascript\nassert.equal(qs.stringify({ a: '' }), 'a=');\n```\n\nKey with no values (such as an empty object or array) will return nothing:\n\n```javascript\nassert.equal(qs.stringify({ a: [] }), '');\nassert.equal(qs.stringify({ a: {} }), '');\nassert.equal(qs.stringify({ a: [{}] }), '');\nassert.equal(qs.stringify({ a: { b: []} }), '');\nassert.equal(qs.stringify({ a: { b: {}} }), '');\n```\n\nProperties that are set to `undefined` will be omitted entirely:\n\n```javascript\nassert.equal(qs.stringify({ a: null, b: undefined }), 'a=');\n```\n\nThe query string may optionally be prepended with a question mark:\n\n```javascript\nassert.equal(qs.stringify({ a: 'b', c: 'd' }, { addQueryPrefix: true }), '?a=b&c=d');\n```\n\nThe delimiter may be overridden with stringify as well:\n\n```javascript\nassert.equal(qs.stringify({ a: 'b', c: 'd' }, { delimiter: ';' }), 'a=b;c=d');\n```\n\nIf you only want to override the serialization of `Date` objects, you can provide a `serializeDate` option:\n\n```javascript\nvar date = new Date(7);\nassert.equal(qs.stringify({ a: date }), 'a=1970-01-01T00:00:00.007Z'.replace(/:/g, '%3A'));\nassert.equal(\n    qs.stringify({ a: date }, { serializeDate: function (d) { return d.getTime(); } }),\n    'a=7'\n);\n```\n\nYou may use the `sort` option to affect the order of parameter keys:\n\n```javascript\nfunction alphabeticalSort(a, b) {\n    return a.localeCompare(b);\n}\nassert.equal(qs.stringify({ a: 'c', z: 'y', b : 'f' }, { sort: alphabeticalSort }), 'a=c&b=f&z=y');\n```\n\nFinally, you can use the `filter` option to restrict which keys will be included in the stringified output.\nIf you pass a function, it will be called for each key to obtain the replacement value.\nOtherwise, if you pass an array, it will be used to select properties and array indices for stringification:\n\n```javascript\nfunction filterFunc(prefix, value) {\n    if (prefix == 'b') {\n        // Return an `undefined` value to omit a property.\n        return;\n    }\n    if (prefix == 'e[f]') {\n        return value.getTime();\n    }\n    if (prefix == 'e[g][0]') {\n        return value * 2;\n    }\n    return value;\n}\nqs.stringify({ a: 'b', c: 'd', e: { f: new Date(123), g: [2] } }, { filter: filterFunc });\n// 'a=b&c=d&e[f]=123&e[g][0]=4'\nqs.stringify({ a: 'b', c: 'd', e: 'f' }, { filter: ['a', 'e'] });\n// 'a=b&e=f'\nqs.stringify({ a: ['b', 'c', 'd'], e: 'f' }, { filter: ['a', 0, 2] });\n// 'a[0]=b&a[2]=d'\n```\n\nYou could also use `filter` to inject custom serialization for user defined types.\nConsider you're working with some api that expects query strings of the format for ranges:\n\n```\nhttps://domain.com/endpoint?range=30...70\n```\n\nFor which you model as:\n\n```javascript\nclass Range {\n    constructor(from, to) {\n        this.from = from;\n        this.to = to;\n    }\n}\n```\n\nYou could _inject_ a custom serializer to handle values of this type:\n\n```javascript\nqs.stringify(\n    {\n        range: new Range(30, 70),\n    },\n    {\n        filter: (prefix, value) => {\n            if (value instanceof Range) {\n                return `${value.from}...${value.to}`;\n            }\n            // serialize the usual way\n            return value;\n        },\n    }\n);\n// range=30...70\n```\n\n### Handling of `null` values\n\nBy default, `null` values are treated like empty strings:\n\n```javascript\nvar withNull = qs.stringify({ a: null, b: '' });\nassert.equal(withNull, 'a=&b=');\n```\n\nParsing does not distinguish between parameters with and without equal signs.\nBoth are converted to empty strings.\n\n```javascript\nvar equalsInsensitive = qs.parse('a&b=');\nassert.deepEqual(equalsInsensitive, { a: '', b: '' });\n```\n\nTo distinguish between `null` values and empty strings use the `strictNullHandling` flag. In the result string the `null`\nvalues have no `=` sign:\n\n```javascript\nvar strictNull = qs.stringify({ a: null, b: '' }, { strictNullHandling: true });\nassert.equal(strictNull, 'a&b=');\n```\n\nTo parse values without `=` back to `null` use the `strictNullHandling` flag:\n\n```javascript\nvar parsedStrictNull = qs.parse('a&b=', { strictNullHandling: true });\nassert.deepEqual(parsedStrictNull, { a: null, b: '' });\n```\n\nTo completely skip rendering keys with `null` values, use the `skipNulls` flag:\n\n```javascript\nvar nullsSkipped = qs.stringify({ a: 'b', c: null}, { skipNulls: true });\nassert.equal(nullsSkipped, 'a=b');\n```\n\nIf you're communicating with legacy systems, you can switch to `iso-8859-1` using the `charset` option:\n\n```javascript\nvar iso = qs.stringify({ æ: 'æ' }, { charset: 'iso-8859-1' });\nassert.equal(iso, '%E6=%E6');\n```\n\nCharacters that don't exist in `iso-8859-1` will be converted to numeric entities, similar to what browsers do:\n\n```javascript\nvar numeric = qs.stringify({ a: '☺' }, { charset: 'iso-8859-1' });\nassert.equal(numeric, 'a=%26%239786%3B');\n```\n\nYou can use the `charsetSentinel` option to announce the character by including an `utf8=✓` parameter with the proper encoding if the checkmark, similar to what Ruby on Rails and others do when submitting forms.\n\n```javascript\nvar sentinel = qs.stringify({ a: '☺' }, { charsetSentinel: true });\nassert.equal(sentinel, 'utf8=%E2%9C%93&a=%E2%98%BA');\n\nvar isoSentinel = qs.stringify({ a: 'æ' }, { charsetSentinel: true, charset: 'iso-8859-1' });\nassert.equal(isoSentinel, 'utf8=%26%2310003%3B&a=%E6');\n```\n\n### Dealing with special character sets\n\nBy default the encoding and decoding of characters is done in `utf-8`, and `iso-8859-1` support is also built in via the `charset` parameter.\n\nIf you wish to encode querystrings to a different character set (i.e.\n[Shift JIS](https://en.wikipedia.org/wiki/Shift_JIS)) you can use the\n[`qs-iconv`](https://github.com/martinheidegger/qs-iconv) library:\n\n```javascript\nvar encoder = require('qs-iconv/encoder')('shift_jis');\nvar shiftJISEncoded = qs.stringify({ a: 'こんにちは！' }, { encoder: encoder });\nassert.equal(shiftJISEncoded, 'a=%82%B1%82%F1%82%C9%82%BF%82%CD%81I');\n```\n\nThis also works for decoding of query strings:\n\n```javascript\nvar decoder = require('qs-iconv/decoder')('shift_jis');\nvar obj = qs.parse('a=%82%B1%82%F1%82%C9%82%BF%82%CD%81I', { decoder: decoder });\nassert.deepEqual(obj, { a: 'こんにちは！' });\n```\n\n### RFC 3986 and RFC 1738 space encoding\n\nRFC3986 used as default option and encodes ' ' to *%20* which is backward compatible.\nIn the same time, output can be stringified as per RFC1738 with ' ' equal to '+'.\n\n```\nassert.equal(qs.stringify({ a: 'b c' }), 'a=b%20c');\nassert.equal(qs.stringify({ a: 'b c' }, { format : 'RFC3986' }), 'a=b%20c');\nassert.equal(qs.stringify({ a: 'b c' }, { format : 'RFC1738' }), 'a=b+c');\n```\n\n## Security\n\nPlease email [@ljharb](https://github.com/ljharb) or see https://tidelift.com/security if you have a potential security vulnerability to report.\n\n## qs for enterprise\n\nAvailable as part of the Tidelift Subscription\n\nThe maintainers of qs and thousands of other packages are working with Tidelift to deliver commercial support and maintenance for the open source dependencies you use to build your applications.\nSave time, reduce risk, and improve code health, while paying the maintainers of the exact dependencies you use.\n[Learn more.](https://tidelift.com/subscription/pkg/npm-qs?utm_source=npm-qs&utm_medium=referral&utm_campaign=enterprise&utm_term=repo)\n\n[package-url]: https://npmjs.org/package/qs\n[npm-version-svg]: https://versionbadg.es/ljharb/qs.svg\n[deps-svg]: https://david-dm.org/ljharb/qs.svg\n[deps-url]: https://david-dm.org/ljharb/qs\n[dev-deps-svg]: https://david-dm.org/ljharb/qs/dev-status.svg\n[dev-deps-url]: https://david-dm.org/ljharb/qs#info=devDependencies\n[npm-badge-png]: https://nodei.co/npm/qs.png?downloads=true&stars=true\n[license-image]: https://img.shields.io/npm/l/qs.svg\n[license-url]: LICENSE\n[downloads-image]: https://img.shields.io/npm/dm/qs.svg\n[downloads-url]: https://npm-stat.com/charts.html?package=qs\n[codecov-image]: https://codecov.io/gh/ljharb/qs/branch/main/graphs/badge.svg\n[codecov-url]: https://app.codecov.io/gh/ljharb/qs/\n[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/ljharb/qs\n[actions-url]: https://github.com/ljharb/qs/actions\n\n## Acknowledgements\n\nqs logo by [NUMI](https://github.com/numi-hq/open-design):\n\n[<img src=\"https://raw.githubusercontent.com/numi-hq/open-design/main/assets/numi-lockup.png\" alt=\"NUMI Logo\" style=\"width: 200px;\"/>](https://numi.tech/?ref=qs)\n", "readmeFilename": "README.md", "users": {"291296283": true, "alu": true, "rsp": true, "vbv": true, "uojo": true, "akiva": true, "bhill": true, "chill": true, "etsit": true, "hdusy": true, "jalik": true, "m42am": true, "miloc": true, "mlowe": true, "panlw": true, "raojs": true, "sevit": true, "slurm": true, "stany": true, "ximex": true, "yikuo": true, "yvesm": true, "arikfr": true, "bbrown": true, "bhenav": true, "bojand": true, "bpatel": true, "buzuli": true, "cathay": true, "chimit": true, "dknell": true, "dubban": true, "eswat2": true, "gaafar": true, "gdbtek": true, "gibson": true, "h02e56": true, "h0ward": true, "hyteer": true, "iamwiz": true, "itesic": true, "jaeger": true, "jimnox": true, "js3692": true, "knoja4": true, "koslun": true, "likkli": true, "nanook": true, "nuwaio": true, "ryaned": true, "tiendq": true, "vcboom": true, "wisetc": true, "wu0792": true, "x4devs": true, "xmwx38": true, "yl2014": true, "zaggen": true, "zlobin": true, "zwwggg": true, "antanst": true, "asaupup": true, "belcour": true, "dnp1204": true, "fkamani": true, "gemini5": true, "itonyyo": true, "jcloutz": true, "kparkov": true, "newswim": true, "nichoth": true, "sahilsk": true, "sopepos": true, "tomchao": true, "xfloops": true, "xtx1130": true, "yanghcc": true, "Prestaul": true, "chaseshu": true, "cologler": true, "danielye": true, "dexteryy": true, "djviolin": true, "fabioper": true, "fang0408": true, "jmsherry": true, "kistoryg": true, "koulmomo": true, "meetravi": true, "mhaidarh": true, "nicodinh": true, "prestaul": true, "qbylucky": true, "rubychen": true, "vae-rain": true, "warp-lab": true, "wkaifang": true, "zuojiang": true, "abuelwafa": true, "alert1983": true, "allen_lyu": true, "asadm2706": true, "ddkothari": true, "devonning": true, "dillonace": true, "dylanh724": true, "fgribreau": true, "iceriver2": true, "illbullet": true, "jesusgoku": true, "justjavac": true, "kizzlebot": true, "largepuma": true, "mojaray2k": true, "nmccready": true, "nomemires": true, "redstrike": true, "rgraves90": true, "shakakira": true, "snowdream": true, "st.teneff": true, "trankhanh": true, "uptonking": true, "xiaokaike": true, "cestrensem": true, "hbzhangmao": true, "jrobinsonc": true, "kankungyip": true, "lightway82": true, "lius971125": true, "mahnunchik": true, "maxmaximov": true, "muzi131313": true, "myorkgitis": true, "pantyuhind": true, "princetoad": true, "qqqppp9998": true, "ruiquelhas": true, "sammok2003": true, "seangenabe": true, "shuoshubao": true, "simplyianm": true, "wangshijun": true, "xieranmaya": true, "xpepermint": true, "youxiachai": true, "amirmehmood": true, "anlijudavid": true, "bjoshuanoah": true, "brandonb927": true, "debearloper": true, "ericfish007": true, "fampinheiro": true, "fengmiaosen": true, "flumpus-dev": true, "galenandrew": true, "kodekracker": true, "mrmartineau": true, "oliverhuang": true, "poeticninja": true, "sadmansamee": true, "shangsinian": true, "wadehuang36": true, "wangnan0610": true, "aldo-sanchez": true, "brentonhouse": true, "columbennett": true, "fanchangyong": true, "mobeicaoyuan": true, "nickeltobias": true, "softdev-zeus": true, "superchenney": true, "tobiasnickel": true, "zhangyaochun": true, "zhenguo.zhao": true, "intuitivcloud": true, "josephdavisco": true, "justinanastos": true, "markthethomas": true, "program247365": true, "sakthiifnotec": true, "kamikadze4game": true, "karzanosman984": true, "santosharakere": true, "space_cat_lady": true, "arcticicestudio": true, "black-black-cat": true, "programmer.severson": true}}