{"name": "ecdsa-sig-formatter", "dist-tags": {"latest": "1.0.11"}, "versions": {"1.0.0": {"name": "ecdsa-sig-formatter", "version": "1.0.0", "dependencies": {"asn1.js": "^2.0.3", "base64-url": "^1.2.1"}, "devDependencies": {"chai": "^3.0.0", "coveralls": "^2.11.2", "istanbul": "^0.3.15", "mocha": "^2.2.5"}, "dist": {"shasum": "2925b91c568c1c13d1e55b6fb45917f753f7ca46", "tarball": "https://registry.npmjs.org/ecdsa-sig-formatter/-/ecdsa-sig-formatter-1.0.0.tgz", "integrity": "sha512-8oki6U/9WCO3Axr5Zcgy+JLrSI+s4vyBahJSYF9UghcZnJqB4bIBC0/yUFRN7XuR/1rcT3YWFKjweJAH1MOAjw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDDxTM5xP8mzD6/uFzS42nwBR4NttkXcSzOE1SBUVAFcAiBL74lC8s/Qe9I1lczcIr84h/QpEzHyMDOsJi48AIbfGA=="}]}}, "1.0.1": {"name": "ecdsa-sig-formatter", "version": "1.0.1", "dependencies": {"asn1.js": "^2.0.3", "base64-url": "^1.2.1"}, "devDependencies": {"chai": "^3.0.0", "coveralls": "^2.11.2", "istanbul": "^0.3.15", "mocha": "^2.2.5"}, "dist": {"shasum": "369b867c40080ba35921530df5a26dad3390d4f1", "tarball": "https://registry.npmjs.org/ecdsa-sig-formatter/-/ecdsa-sig-formatter-1.0.1.tgz", "integrity": "sha512-wsxwVpHRXqqxBcIBqlXncYrLDva8RbJ78GQBTAT0yhQp5+T4lDxqRjXk8VG2IJ4gz+eaWgx7BPs0RydVk/C9cA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHhjyxCJ3I6u7YOmPaHvFbgGxdbTho2Ln5ZGfe+kU8PRAiAj8g4glsfMG6Fa5MomiM0deGzzuufLehTxV9h+GpeOhQ=="}]}}, "1.0.2": {"name": "ecdsa-sig-formatter", "version": "1.0.2", "dependencies": {"asn1.js": "^2.0.3", "base64-url": "^1.2.1"}, "devDependencies": {"bench": "^0.3.5", "chai": "^3.0.0", "coveralls": "^2.11.2", "istanbul": "^0.3.15", "mocha": "^2.2.5"}, "dist": {"shasum": "2074b4bd06be5e7479c9f71e73358bc3deea4a9b", "tarball": "https://registry.npmjs.org/ecdsa-sig-formatter/-/ecdsa-sig-formatter-1.0.2.tgz", "integrity": "sha512-1GNuV5W8v1rU+5AxNrPTJxF8pkDk3U2a9kxVcVoer1jpF0ZjzjTcVw8zwbA78UJzObKWaswmwJvVZEglhboCKQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDxXj0KbBWT0BtVOFAAMxetqqHFR/k66Xbb0NkqMcBu0gIgKdha7SkL/Layj2zR7GfwsN/gEEGS9gg/HfwS/C/CiaE="}]}}, "1.0.5": {"name": "ecdsa-sig-formatter", "version": "1.0.5", "dependencies": {"base64-url": "^1.2.1"}, "devDependencies": {"bench": "^0.3.6", "chai": "^3.4.1", "coveralls": "^2.11.6", "elliptic": "^6.1.0", "eslint": "^1.10.3", "eslint-config-brightspace": "^0.1.0", "istanbul": "^0.4.2", "jwk-to-pem": "^1.2.4", "mocha": "^2.3.4"}, "dist": {"shasum": "0d0f32b638611f6b8f36ffd305a3e512ea5444e6", "tarball": "https://registry.npmjs.org/ecdsa-sig-formatter/-/ecdsa-sig-formatter-1.0.5.tgz", "integrity": "sha512-+iTWaj9CQHnvYQ7keATosCbg6I60hVQoT3SGAva2dHwGuWe9enc3Irl60Bb6Mm9+eEZ9xukm1UMrEY1dSHC03A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCOneZRXKfJSEsFHR1nCUdzZCFXOEc1qW70pNx913hkKgIgMQmmNLXLmJ79phW/X5gpWZfno6fPZjSxrv2PIiK6mn8="}]}}, "1.0.6": {"name": "ecdsa-sig-formatter", "version": "1.0.6", "dependencies": {"base64-url": "^1.2.1"}, "devDependencies": {"bench": "^0.3.6", "chai": "^3.5.0", "coveralls": "^2.11.9", "elliptic": "^6.3.1", "eslint": "^2.12.0", "eslint-config-brightspace": "^0.2.1", "istanbul": "^0.4.3", "jwk-to-pem": "^1.2.5", "mocha": "^2.5.3"}, "dist": {"shasum": "19968e66a2f4366210ef3f791e5454d760f7722d", "tarball": "https://registry.npmjs.org/ecdsa-sig-formatter/-/ecdsa-sig-formatter-1.0.6.tgz", "integrity": "sha512-Bft5z8c4ga3uS6X47orWnfAKCeeRVP1qYrvo/ndyMTZ4yZxVeV3owSNtcNCYDWvp2CMNyUAcdmtRE/22NDvqIA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG6uVpxwfHRcUprWgDaOf0HnzaoLpybTrMY9q5/icVUWAiEA6cUeTg850+0adAJ2R+x7NoOjcCknHivBbO5X1Iw7gBY="}]}}, "1.0.7": {"name": "ecdsa-sig-formatter", "version": "1.0.7", "dependencies": {"base64-url": "^1.2.1"}, "devDependencies": {"bench": "^0.3.6", "chai": "^3.5.0", "coveralls": "^2.11.9", "elliptic": "^6.3.1", "eslint": "^2.12.0", "eslint-config-brightspace": "^0.2.1", "istanbul": "^0.4.3", "jwk-to-pem": "^1.2.5", "mocha": "^2.5.3"}, "dist": {"shasum": "3137e976a1d6232517e2513e04e32f79bcbdf126", "tarball": "https://registry.npmjs.org/ecdsa-sig-formatter/-/ecdsa-sig-formatter-1.0.7.tgz", "integrity": "sha512-eQGBiG9EKukpMONQkopQv3Xv3JN4fSL+aupxz8o1B4+TSB+fvuFQcfU0u7SMIbLacpOe4c8uS9qnXd/jy3OqOA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCVE8O8XVt/FweesC5XmxKqx9wjbyx07SaDaUAdte68ygIgILGRVYyWG7YqCzg5OJ+0eVBwDpu26awmeEgVHE5FXKE="}]}}, "1.0.8": {"name": "ecdsa-sig-formatter", "version": "1.0.8", "dependencies": {"base64-url": "^1.2.1", "safe-buffer": "^5.0.1"}, "devDependencies": {"bench": "^0.3.6", "chai": "^3.5.0", "coveralls": "^2.11.9", "elliptic": "^6.3.1", "eslint": "^2.12.0", "eslint-config-brightspace": "^0.2.1", "istanbul": "^0.4.3", "jwk-to-pem": "^1.2.5", "mocha": "^2.5.3"}, "dist": {"shasum": "a9e3f5534e3755a56f8c982fb15a05a6364a1dab", "tarball": "https://registry.npmjs.org/ecdsa-sig-formatter/-/ecdsa-sig-formatter-1.0.8.tgz", "integrity": "sha512-ULDtFASgTauQ2zeSgA4DtdT9C1OVeoJNN1uuUjuAGSZw7z9UUcSGYcqPCfBP/dB+mtNv4WJpuB4AYqHryvXALQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGX0SSyxXZ4PHjxxY2jewmCUXCd6AzmyPAneCR8apU6KAiAqRCu0D123muXY4a6wQ3aRrOlDLAfyZxCxpzm+Oasveg=="}]}}, "1.0.9": {"name": "ecdsa-sig-formatter", "version": "1.0.9", "dependencies": {"base64url": "^2.0.0", "safe-buffer": "^5.0.1"}, "devDependencies": {"bench": "^0.3.6", "chai": "^3.5.0", "coveralls": "^2.11.9", "elliptic": "^6.3.1", "eslint": "^2.12.0", "eslint-config-brightspace": "^0.2.1", "istanbul": "^0.4.3", "jwk-to-pem": "^1.2.5", "mocha": "^2.5.3"}, "dist": {"shasum": "4bc926274ec3b5abb5016e7e1d60921ac262b2a1", "tarball": "https://registry.npmjs.org/ecdsa-sig-formatter/-/ecdsa-sig-formatter-1.0.9.tgz", "integrity": "sha512-J/mTMRuaEY85fUpQmLRXBg3tOk1zshbIIazSoUzdpjdD7hdiYKR/VpiL6JNWbuSjbSYdugmgBv8m/O1kK0CQRw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAwLNEN9wxCvdG7qfyU1uCb+D+a2zIUhiIM+s4p2ej/eAiBTSnzswi7qEEdEy4YXkJjuQVudzuzJVx4Dt6Vvp7CX+Q=="}]}}, "1.0.10": {"name": "ecdsa-sig-formatter", "version": "1.0.10", "dependencies": {"safe-buffer": "^5.0.1"}, "devDependencies": {"bench": "^0.3.6", "chai": "^3.5.0", "coveralls": "^2.11.9", "eslint": "^2.12.0", "eslint-config-brightspace": "^0.2.1", "istanbul": "^0.4.3", "jwk-to-pem": "^1.2.5", "mocha": "^2.5.3", "native-crypto": "^1.7.0"}, "dist": {"shasum": "1c595000f04a8897dfb85000892a0f4c33af86c3", "tarball": "https://registry.npmjs.org/ecdsa-sig-formatter/-/ecdsa-sig-formatter-1.0.10.tgz", "fileCount": 6, "unpackedSize": 19847, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+Z/rCRA9TVsSAnZWagAAzgQQAJOsuIKhrswNT/4bk34b\npKfVaKvj3OveIAUSeoINK9Q1X05xMlR6zu7jw3CL/HMw841L4/UV2BTVGp2y\noX0ES6nrToMTofGv6+0s2PuVYKymS86KKDizDJ5CUGMRTaB4/kTJQqAZDxXZ\nT2FPojKFznTetwB1Vz+hS1qOQfdUQ3QhbjTEj2/ExTSX/8U411+u+VmwT20m\nAiopghYiBs1SN5TcSmG+5a0lSxyh75IzGxkhss0hogk9RGqmp5bsD1j/7cYe\nZCBhadNS+Dyxkt4+h9KXHeCa3VzQub2ejaI1GHasHLQfu4A1W0wcP63HeOBZ\nAKYt1++aSlbefl5BTN712W+FSZuvbXI3uamxS4zR90MukzCr+PBiyo5tq0tm\nckQ00UCj/vxf9hLm9tN2EzjNAsQhXnODXnxzjvqHGb48yaQniR07fm5+z1V1\nrXPwaQLa7qIrE8o7Yl5YMLTGAMFX2f3+nY39rruFPY+/truORu8YmFORxgP6\n5baDe1l0wlvXd68REnNztIbwitksN2GSXWAAMQwCEHrWk3ixdrPi59CZLWqW\nMPKaEJzKaALAStrkz7eBCi3b+4P2lQvPoq/ol57lcUvs3wy4EKF0+aGdfU3S\nx/k9s0XZJvbqAKJOq05oH91pdZngSO//FPtfBOW1Ymkyj+SsdakNABp54CPz\n1cr5\r\n=rR+E\r\n-----END PGP SIGNATURE-----\r\n", "integrity": "sha512-0XqJH78hi7Cx141usy/GT+gSzwZs58W144EhBPCFWM5bQj5nDhC+CnGTAuJDIYkuXZei+m7/isuIBqhaf1XAEA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCk5UMB0RzTTjVow7BvneBS+h8u80wGanpGAu5YvHrJEwIhAJGiEIvmCpo7za0lZQJU5asNWaBKPGXTIhV+drqbP8nj"}]}}, "1.0.11": {"name": "ecdsa-sig-formatter", "version": "1.0.11", "dependencies": {"safe-buffer": "^5.0.1"}, "devDependencies": {"bench": "^0.3.6", "chai": "^3.5.0", "coveralls": "^2.11.9", "eslint": "^2.12.0", "eslint-config-brightspace": "^0.2.1", "istanbul": "^0.4.3", "jwk-to-pem": "^1.2.5", "mocha": "^2.5.3", "native-crypto": "^1.7.0"}, "dist": {"integrity": "sha512-nagl3RYrbNv6kQkeJIpt6NJZy8twLB/2vtz6yN9Z4vRKHN4/QZJIEbqohALSgwKdnksuY3k5Addp5lg8sVoVcQ==", "shasum": "ae0f0fa2d85045ef14a817daa3ce9acd0489e5bf", "tarball": "https://registry.npmjs.org/ecdsa-sig-formatter/-/ecdsa-sig-formatter-1.0.11.tgz", "fileCount": 7, "unpackedSize": 20588, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcS4BeCRA9TVsSAnZWagAAQHgQAJsWXbsQEgHH9CO915pO\nuosLqkOVmPVXT34pulC2fOjiiD3IAr9gCFrup/pfO26c2AInJh8BxCmfEm7z\nbS4QX8d4PUOHhoMDYoTOZnuRT5yiis0nynycjRF1ajexCtU+8jOovmQBo5dn\nfUK0odtULQg/bi84vinmGGK3s57lcD5PxHuzvR45io41wszJB90Pdv2vFMGf\nJUYDybFKHKZJbpbQ93VMvfTWDlFNnlRn4Bz/jrV4uGgDgkHJHlNARZSp/TLa\nwTyRzW98c2PQzMdZJZTnFz6b9hG2Cj0d0lIY1hxX+XQ422lEvaa6nQgKqVb6\nA0qkFRJoP/43/gjt0ZXVu0y09qdQCsnRzN2uxDyUK87QfZcuWPYxmiv1XEfX\n+aDtu1lKLgxT/1r01QleQtN2fw5FZSU57/Zw0KC9uUWDEvTcLMJDT447hvk2\n2cVt5XWOAtFwxAfdtYlGjq0qLSIM/vm8JwNjdaTfbDsLXMSwZrp/7egpZFX6\nsE/XJ4hxC0niiXP6QrrRFKynXm6ZWnzB49dAeDkzCiB4iJao8WKYptdV9F7W\n1dpuWrb28hHY4jaDI/7dumP/fvmCvsGLay9nRH5iE5ePU4JXyJp0AujvqV+c\noDM8h2/+zHkPYffosqmEZf2NxeB5rIAzFZrcBQHKYp7rKqCvztowBC6730Jb\n6eYG\r\n=3ns0\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCFPZcmrEORNGUiOCBVX+Jjwq3ESmM26Z5y6tP8DmGY3gIhANbjcgEPCX68J8RLDb5cvIioQ/ezZPxRw+VYxljOa80M"}]}}}, "modified": "2022-06-16T04:36:24.613Z"}