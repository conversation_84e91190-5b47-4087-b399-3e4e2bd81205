{"name": "@webassemblyjs/wasm-parser", "dist-tags": {"latest": "1.14.1"}, "versions": {"1.0.0-y.5": {"name": "@webassemblyjs/wasm-parser", "version": "1.0.0-y.5", "dependencies": {"webassemblyjs": "1.0.0-y.5", "@webassemblyjs/ast": "1.0.0-y.5", "@webassemblyjs/wasm-parser": "1.0.0-y.5"}, "dist": {"shasum": "bb728bd3a5d915d41a4ad74fba634d91c6bfc744", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.0.0-y.5.tgz", "fileCount": 15, "integrity": "sha512-w6akx78r9+ajPOgTUToz2NP+yKM3gT74e9cud1VzT8/Ol3neudMf+bAQH8fDXub0y/mJPi3hoQT7VHEYEZrM9w==", "signatures": [{"sig": "MEYCIQCQ6W/qdMSZwdrefqi510r8PJqBL/qs/Uh1VwPXStbedAIhAIDrB2JqexLdOOB8uipxvBUbBixNp5ClLlcrTWez1xg1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 105081}}, "1.0.0-y.6": {"name": "@webassemblyjs/wasm-parser", "version": "1.0.0-y.6", "dependencies": {"webassemblyjs": "1.0.0-y.6", "@webassemblyjs/ast": "1.0.0-y.6", "@webassemblyjs/wasm-parser": "1.0.0-y.6"}, "dist": {"shasum": "11a9bb1b7d51923c5e142ece79781735fb877330", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.0.0-y.6.tgz", "fileCount": 33, "integrity": "sha512-4wHIBLglcmwchYIXY1YgNwUBqAFdVpIQSYs8Yxn/Kb+/zkgShXOhpxe3KAbSZjCQOOnOvra/wHbr/IXjlTmBgQ==", "signatures": [{"sig": "MEYCIQCI8C9FkdJ46BjLrsKumMRAsUP13SrBO+Eb7e9f7u8e/gIhAITRhWblqqo9TNIbUTxKiah5IcpW1JzD8c89m5CNlk0b", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 113654}}, "1.0.0-y.7": {"name": "@webassemblyjs/wasm-parser", "version": "1.0.0-y.7", "dependencies": {"webassemblyjs": "1.0.0-y.7", "@webassemblyjs/ast": "1.0.0-y.7", "@webassemblyjs/wasm-parser": "1.0.0-y.7"}, "dist": {"shasum": "aafd74a65330d1b39f195717d987e1cfd9e648ba", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.0.0-y.7.tgz", "fileCount": 34, "integrity": "sha512-T5O/sWohAG49a1S67Z9aC9lF99h3jHN90GTlHS3kMzdwMopZasHkEtEQiybnGcr3XMTvLpjg3T7C2thgh8SFvg==", "signatures": [{"sig": "MEUCIQDfibBBZQ7V4dJZEUdH32V/r9D934Ch5o566BMUavyKSAIgMinWOj00ZJWs7edggmpW5j+lvBYnIu/0JRA4HHrXg2I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 113717}}, "1.0.0-y.8": {"name": "@webassemblyjs/wasm-parser", "version": "1.0.0-y.8", "dependencies": {"webassemblyjs": "1.0.0-y.8", "@webassemblyjs/ast": "1.0.0-y.8", "@webassemblyjs/wasm-parser": "1.0.0-y.8"}, "dist": {"shasum": "a01dddad37d5a344e43216ce47277eaa7a84bacd", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.0.0-y.8.tgz", "fileCount": 16, "integrity": "sha512-NZpiz+8I5k8uiTNK25reXdksxae+/L/tnE+57qSIciRBIKQ01ANLMACV+SUPwr8aUlSCHeLc62/ptXtxhTSWuw==", "signatures": [{"sig": "MEUCIQDjj5R3uS1H/hT3T0QtEqcrtUkRQC1PiFaA1XPDymdhBQIgNlJjGPvDZ1bMH4knHjhbC4V1hUOSswiGAj38Azlntck=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 105702}}, "1.0.0": {"name": "@webassemblyjs/wasm-parser", "version": "1.0.0", "dependencies": {"webassemblyjs": "1.0.0", "@webassemblyjs/ast": "1.0.0", "@webassemblyjs/wasm-parser": "1.0.0"}, "dist": {"shasum": "79e3e45f907a57c7a8bb74e746ee7bab7b77041a", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.0.0.tgz", "fileCount": 16, "integrity": "sha512-yr2TsY3+TMP4LvRr3WaAscnIAkoTbgCjLed5kpHDy6d0oiHDOuNc5dxat4hqLdtS50J1B8LAYL3Acej5L/G4lw==", "signatures": [{"sig": "MEUCIQCIE1OYlh7QNDBwKLUf4VMF++v0f8lEQI9fyuR42zXKtgIgBD3qgcsrnyamAxpbsnNh7hIzwfCEVWZsu0rMyUtkH4k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 107038}}, "1.1.0": {"name": "@webassemblyjs/wasm-parser", "version": "1.1.0", "dependencies": {"webassemblyjs": "1.1.0", "@webassemblyjs/ast": "1.1.0", "@webassemblyjs/wasm-parser": "1.1.0", "@webassemblyjs/helper-leb128": "1.1.0", "@webassemblyjs/helper-wasm-bytecode": "1.1.0"}, "dist": {"shasum": "5b2e4914e369e16856aed059c4f116b2abfc7e26", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.1.0.tgz", "fileCount": 12, "integrity": "sha512-Lo2rkLUJlFnTm1fDdaIrUEbhWkyikKtTye1ilBKp9wBcgs0ruH7WJiKsBtgUltQCpm6jerw473ZEl5FgMhmlUg==", "signatures": [{"sig": "MEQCIBj+FzL6YhG8YGIXnRuGpaW9sbtxBjwSk89/b4H7mbR3AiAb//mmU5tl4LVfSlpHImFEa2UskAZWnB1MD2ndOWwx/Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 80406}}, "1.1.1": {"name": "@webassemblyjs/wasm-parser", "version": "1.1.1", "dependencies": {"webassemblyjs": "1.1.1", "@webassemblyjs/ast": "1.1.1", "@webassemblyjs/wasm-parser": "1.1.1", "@webassemblyjs/helper-leb128": "1.1.1", "@webassemblyjs/helper-wasm-bytecode": "1.1.1"}, "dist": {"shasum": "4ca3e4ac96a55923c8728f7bfa6759d078228290", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.1.1.tgz", "fileCount": 12, "integrity": "sha512-nmB3R+Avx9gnBFRoNax+Dbvs5QuqoOqPoCVR8g3LXefhO9/G9w6aXMAMcNmj9xv/6V27VHkKmmQ0RhhOOWiZSQ==", "signatures": [{"sig": "MEUCID4G+1AmdPE4uDhIfLiND7efnBig4CYvcc8uy8dz012QAiEA3V/vnSuh0kRfktjVoxZv7iB4v7a5U/trvzjnMAiVrbk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 80602}}, "1.1.2-y.0": {"name": "@webassemblyjs/wasm-parser", "version": "1.1.2-y.0", "dependencies": {"webassemblyjs": "1.1.2-y.0", "@webassemblyjs/ast": "1.1.2-y.0", "@webassemblyjs/wasm-parser": "1.1.2-y.0", "@webassemblyjs/helper-leb128": "1.1.2-y.0", "@webassemblyjs/helper-wasm-bytecode": "1.1.2-y.0"}, "dist": {"shasum": "9c90f9e7ce1f54b922b8b76de5355f13e0d8c6f2", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.1.2-y.0.tgz", "fileCount": 12, "integrity": "sha512-ekakX8xdX7s87CohYuY5EVqqytusXgadEVm0iiY+OThiLVls6F/7Lq9q9Ar3oiZVXKcKczLkC4qYRfmDhnMO6Q==", "signatures": [{"sig": "MEUCIHftm7UGW+//FMgkc1ZMJOeJ7zjEXKn+h2N3DbP8VKP6AiEAgUXtCc03LSMZ/zJ1juYN9d4s9eU4LPXNcnyd21O9WU0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 80650}}, "1.1.2-y.1": {"name": "@webassemblyjs/wasm-parser", "version": "1.1.2-y.1", "dependencies": {"webassemblyjs": "1.1.2-y.1", "@webassemblyjs/ast": "1.1.2-y.1", "@webassemblyjs/wasm-parser": "1.1.2-y.1", "@webassemblyjs/helper-leb128": "1.1.2-y.1", "@webassemblyjs/helper-wasm-bytecode": "1.1.2-y.1"}, "dist": {"shasum": "954b077d81c3c4eac8b3bbfbad26ea0019a17b62", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.1.2-y.1.tgz", "fileCount": 12, "integrity": "sha512-U3gl27u6LrS7twhuvdV/pmAyBpR7UrE3zAVzLJyaFvC55uUIaA/auNFy1l7WedtTc9TWuh0vA5EUk0vFwueNDw==", "signatures": [{"sig": "MEYCIQCke8Xtc/hkwLpNfdPa3srjLu2K9+/y+/haT3jxkZviqAIhAO/TsJtSzbj9xOWcfDQhfjiuocqbykwRzonLTbDXcG1/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 80650}}, "1.1.2-y.2": {"name": "@webassemblyjs/wasm-parser", "version": "1.1.2-y.2", "dependencies": {"webassemblyjs": "1.1.2-y.2", "@webassemblyjs/ast": "1.1.2-y.2", "@webassemblyjs/wasm-parser": "1.1.2-y.2", "@webassemblyjs/helper-leb128": "1.1.2-y.2", "@webassemblyjs/helper-wasm-bytecode": "1.1.2-y.2"}, "dist": {"shasum": "53fd97f58a700f048b7bc575051e8ce1de7bd260", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.1.2-y.2.tgz", "fileCount": 12, "integrity": "sha512-0sBBSygI//0eOIb618r9FFvLBN8Y4I13MrXw+HHCvogQbOAjNNr/T11SQSTouYctfoJV1LZm0iFs6oWsVvRQVg==", "signatures": [{"sig": "MEYCIQDrrydcnOWybZLt13eBUlsJ5UN0ajH6QtXbJReOwaG0sQIhAMJiMTN1/Bhkb3nUYa7Ycg85LCseSPJ6IGzTMYb2M/cK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 80650}}, "1.1.2-y.3": {"name": "@webassemblyjs/wasm-parser", "version": "1.1.2-y.3", "dependencies": {"webassemblyjs": "1.1.2-y.3", "@webassemblyjs/ast": "1.1.2-y.3", "@webassemblyjs/wasm-parser": "1.1.2-y.3", "@webassemblyjs/helper-leb128": "1.1.2-y.3", "@webassemblyjs/helper-wasm-bytecode": "1.1.2-y.3"}, "dist": {"shasum": "6807b4ed4d6d7286e0cf6cae69b7b41c0318a4a5", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.1.2-y.3.tgz", "fileCount": 12, "integrity": "sha512-ZhWSVLDLtxnTmnoYZ98e8woxt72sYBOxIE41c7u7qhId408x53wM2LrQvFItQ3Mfg0HJSw3AxWGyxqlL+O15yQ==", "signatures": [{"sig": "MEQCICCCECmdQ+j8sBadZtbC2//bs6vfx8BVEqb+s9z8o9okAiAHT3dd5ebgWBSsFPIjPOmcvLtHGPddIv3zG2knsl7HbA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 80650}}, "1.1.2-y.4": {"name": "@webassemblyjs/wasm-parser", "version": "1.1.2-y.4", "dependencies": {"webassemblyjs": "1.1.2-y.4", "@webassemblyjs/ast": "1.1.2-y.4", "@webassemblyjs/wasm-parser": "1.1.2-y.4", "@webassemblyjs/helper-leb128": "1.1.2-y.4", "@webassemblyjs/helper-wasm-bytecode": "1.1.2-y.4"}, "dist": {"shasum": "f8ff7ff803b029e1e1af738b68efc6420f268773", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.1.2-y.4.tgz", "fileCount": 12, "integrity": "sha512-Wyv9OU1Ma/rsyib56MT3XI+0iMMAI6xMUo/F16bB1W2oWSBXrNJH9+RDTQHp0V15lr1A2oHkmeA6dQtBY2Iciw==", "signatures": [{"sig": "MEUCIQC/L7oZqdV2hlGBr1/HOE54oHyQU5ZR0KaexckXbl2bJQIgCXgc4ET0ZNNxLtUdFdomjTFxS/ksagBojtcUVaqB0/k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 80650}}, "1.1.2-y.5": {"name": "@webassemblyjs/wasm-parser", "version": "1.1.2-y.5", "dependencies": {"webassemblyjs": "1.1.2-y.5", "@webassemblyjs/ast": "1.1.2-y.5", "@webassemblyjs/wasm-parser": "1.1.2-y.5", "@webassemblyjs/helper-leb128": "1.1.2-y.5", "@webassemblyjs/helper-wasm-bytecode": "1.1.2-y.5"}, "dist": {"shasum": "163cd8dadfaf6e018bd0c26967166916c1daf259", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.1.2-y.5.tgz", "fileCount": 12, "integrity": "sha512-j7k5emgp8JBFIxZ7bDgFKbv6Q1yh9PZcU6HRAokodwNu6EDDpL9JsBoynmHZ2g8B1Q1IvAMfnHkX26SvKnZq0g==", "signatures": [{"sig": "MEQCIAJnEhuWnR0GhfAwYMLVrDn5MnR/7Xwkoindy0GBD2HoAiA+WQpjhNEdUPgxBQehxxhUV12189qES7vfc4ahoF/hHQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81702}}, "1.1.2-y.6": {"name": "@webassemblyjs/wasm-parser", "version": "1.1.2-y.6", "dependencies": {"webassemblyjs": "1.1.2-y.6", "@webassemblyjs/ast": "1.1.2-y.6", "@webassemblyjs/wasm-parser": "1.1.2-y.6", "@webassemblyjs/helper-leb128": "1.1.2-y.6", "@webassemblyjs/helper-wasm-bytecode": "1.1.2-y.6"}, "dist": {"shasum": "029ca09e016280cfcee7306989c32f8202a31914", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.1.2-y.6.tgz", "fileCount": 12, "integrity": "sha512-Qw0mT9184FD7ABU7NqIbewIAI8Yif9s6sX2NjQRLdD5zU10jxOK/Gmr0QWwJ0qEYyw08fzz9KIzNMyfqJzwO/A==", "signatures": [{"sig": "MEQCIE19awDvLIU9ESfjifFWjwxMDImcwaf5mbncKjJNaKkYAiBwAnR5oScLGyr4UUKWV8KNsByJqcqGR978eNcwLBoTOg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81702}}, "1.1.2-y.7": {"name": "@webassemblyjs/wasm-parser", "version": "1.1.2-y.7", "dependencies": {"webassemblyjs": "1.1.2-y.7", "@webassemblyjs/ast": "1.1.2-y.7", "@webassemblyjs/wasm-parser": "1.1.2-y.7", "@webassemblyjs/helper-leb128": "1.1.2-y.7", "@webassemblyjs/helper-wasm-bytecode": "1.1.2-y.7"}, "dist": {"shasum": "10d5f04ab2418a4ce12c93a2993ce89917fec0e7", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.1.2-y.7.tgz", "fileCount": 21, "integrity": "sha512-ZUty8JsrMMfgu0Svy57S/NnPmDieE80kuXTHDpgnCtbWpkBe5vAjKLn6Au1j6iuF3kLqs2aFuzWIf8/scjCA2w==", "signatures": [{"sig": "MEUCIQD7+lP7YeV+Etli/o4c3qxHeYzzDADb3QdtSqZBlTCu4QIgEb/lxyLgkNezQ3OcZW/aE5hbeqo3VtQTAFHRfrELlzk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 99023}}, "1.1.2-y.8": {"name": "@webassemblyjs/wasm-parser", "version": "1.1.2-y.8", "dependencies": {"webassemblyjs": "1.1.2-y.8", "@webassemblyjs/ast": "1.1.2-y.8", "@webassemblyjs/wasm-parser": "1.1.2-y.8", "@webassemblyjs/helper-leb128": "1.1.2-y.8", "@webassemblyjs/helper-wasm-bytecode": "1.1.2-y.8"}, "dist": {"shasum": "234b7cc2cd6f82eb8d88e788fe3abb3d9f63a73f", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.1.2-y.8.tgz", "fileCount": 12, "integrity": "sha512-mFwA5jHHTYdO9BEl4bMnRU6+YHQEdtKlnnk9V7Jco0sYx7pJqmoBKk3hYdgNJnKUtUdDC4aXflOaslbARxMqZw==", "signatures": [{"sig": "MEUCIGIsNFLSAjlrklzJ0wLB2yoVtji7STikOf1Qn2siaDNKAiEA7x8SjmD6GWdfwkAHa/xVVk1FmWTG7R4MZFati0EVCK0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81702}}, "1.1.2-y.9": {"name": "@webassemblyjs/wasm-parser", "version": "1.1.2-y.9", "dependencies": {"webassemblyjs": "1.1.2-y.9", "@webassemblyjs/ast": "1.1.2-y.9", "@webassemblyjs/wasm-parser": "1.1.2-y.9", "@webassemblyjs/helper-leb128": "1.1.2-y.9", "@webassemblyjs/helper-wasm-bytecode": "1.1.2-y.9"}, "dist": {"shasum": "f446d9523012780a90520c3b31155fca43ef1f46", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.1.2-y.9.tgz", "fileCount": 12, "integrity": "sha512-QWT1LfBUw06zUTt0FHx9ie+yk2pkmRFeNdcnM/qyCe7wkT62sytDDeWier4pKgj3/pvq2Kjmqe3ZFvNTLAnUYQ==", "signatures": [{"sig": "MEUCICTME9RZam6TMeIHyJomcd2v/gUvvPGVFj+IXGGEF9O0AiEAqBlDI3w8vjry1GQYpCKddfEHCiyndFTdFTClTiogffc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81702}}, "1.1.2-y.10": {"name": "@webassemblyjs/wasm-parser", "version": "1.1.2-y.10", "dependencies": {"webassemblyjs": "1.1.2-y.10", "@webassemblyjs/ast": "1.1.2-y.10", "@webassemblyjs/wasm-parser": "1.1.2-y.10", "@webassemblyjs/helper-leb128": "1.1.2-y.10", "@webassemblyjs/helper-wasm-bytecode": "1.1.2-y.10"}, "dist": {"shasum": "f1188834dd1ec17f0e6f1d4052cdf117d5344f29", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.1.2-y.10.tgz", "fileCount": 12, "integrity": "sha512-PLcIO9mVtRF9itgVZfe5f4JnVATvJkYCujviV9UIKLMwRagYAG3tFjv+MXE/umeUZyURxUyks74UGQ5xktolgA==", "signatures": [{"sig": "MEUCIQDdL4Y7Jp+nE7Mu8gCN7SIshLi+8VLDfN4CdDoHMk0oBQIgZ4Y9qKYZw86vEoLNX3GKYCReavR93FJrAFNZ7KOnfQ8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82200}}, "1.2.0": {"name": "@webassemblyjs/wasm-parser", "version": "1.2.0", "dependencies": {"webassemblyjs": "1.2.0", "@webassemblyjs/ast": "1.2.0", "@webassemblyjs/wasm-parser": "1.2.0", "@webassemblyjs/helper-leb128": "1.2.0", "@webassemblyjs/helper-wasm-bytecode": "1.2.0"}, "dist": {"shasum": "77dbac0956cebb7b723fc301cc5262455acaf4d3", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.2.0.tgz", "fileCount": 12, "integrity": "sha512-iNmZwuoNXbaW9NhiZZ2O+c1d7USa9sgIjqmYTyIxCxDrR0BUf85fSWsABOFPtJ+OEXTzLhSSAnhG2JGkE9HwXg==", "signatures": [{"sig": "MEYCIQC5rjsLh0kXdIPkNdGcB2akCEUC32FzmlYsdczyB513JwIhAKV5ZbPNfAutrBDn9AreVB6oIUmE+O53MoRMCqHs/We7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82170}}, "1.2.1": {"name": "@webassemblyjs/wasm-parser", "version": "1.2.1", "dependencies": {"webassemblyjs": "1.2.1", "@webassemblyjs/ast": "1.2.1", "@webassemblyjs/wasm-parser": "1.2.1", "@webassemblyjs/helper-leb128": "1.2.1", "@webassemblyjs/helper-wasm-bytecode": "1.2.1"}, "dist": {"shasum": "624cc7dc7a9dd3549dccec3ffa9753a9a0ff45b4", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.2.1.tgz", "fileCount": 12, "integrity": "sha512-R8bm1lV4vUNEs2/zkdRleViTjEF5fU4PX9ndcjDQcDG0mSX6Ycy6t1+WnE9f122ZvTt4aWa4HFh7zJl/BNFthA==", "signatures": [{"sig": "MEUCIQCbr9baQnsHu7DhesVT55WVae3wFt9sN5owqPWUcnG13gIgcy2Y890MWtsZaU7swWTGO9v3SIo2Ubrr0CsUpgCRV6s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82176}}, "1.2.2": {"name": "@webassemblyjs/wasm-parser", "version": "1.2.2", "dependencies": {"webassemblyjs": "1.2.2", "@webassemblyjs/ast": "1.2.2", "@webassemblyjs/wasm-parser": "1.2.2", "@webassemblyjs/helper-leb128": "1.2.2", "@webassemblyjs/helper-wasm-bytecode": "1.2.2"}, "dist": {"shasum": "9a81f3cc4f9710d5ee3e6ff0ebcca73346a58161", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.2.2.tgz", "fileCount": 12, "integrity": "sha512-N9ICM9ga7uOqPU8HEBOdh8KeiD/UQkHn7X8bro76T07ZPLJ+Ax/UHKpaF0UKSyOaBa+YiwWvHpqb7Z0W8aasVA==", "signatures": [{"sig": "MEUCIH+sfdyNEzred1XMNwLFduOhaR/l8BNJQ+Fl5KAvLJWdAiEA+91IBq23H59s8KtYa2XIICQ/MGwBigd+PpO90MKhjIc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88889}}, "1.2.3": {"name": "@webassemblyjs/wasm-parser", "version": "1.2.3", "dependencies": {"webassemblyjs": "1.2.3", "@webassemblyjs/ast": "1.2.3", "@webassemblyjs/leb128": "1.2.3", "@webassemblyjs/wasm-parser": "1.2.3", "@webassemblyjs/helper-wasm-bytecode": "1.2.3"}, "devDependencies": {"wabt": "^1.0.0", "@webassemblyjs/wast-parser": "1.2.3"}, "dist": {"shasum": "cad576195ba2576749f39c2d5051ffb17a6b8ff1", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.2.3.tgz", "fileCount": 12, "integrity": "sha512-epJfzxGKLfVR+F2yhHyqLxX7m7Kq8J33f84W79ytlzKpFJwkpEX3o5uBTDR/v22PTNvAiPkd0twimpYTcTh8cw==", "signatures": [{"sig": "MEYCIQC9bkT55A+B28B908m4cJjUFJfbjDN8h+RJGw6DGs0C6gIhANaSTNJ1cY2zY1YCJAW1qcdQpFT4E23tAtPscRV7NuWa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 92610}}, "1.2.4": {"name": "@webassemblyjs/wasm-parser", "version": "1.2.4", "dependencies": {"webassemblyjs": "1.2.4", "@webassemblyjs/ast": "1.2.4", "@webassemblyjs/leb128": "1.2.4", "@webassemblyjs/wasm-parser": "1.2.4", "@webassemblyjs/helper-wasm-bytecode": "1.2.4"}, "devDependencies": {"wabt": "^1.0.0", "@webassemblyjs/wast-parser": "1.2.4"}, "dist": {"shasum": "2cde613acf2db58f9fd97d8d8733dbdb15d31303", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.2.4.tgz", "fileCount": 12, "integrity": "sha512-fNaZDyGtM5qIkgDJvTcFrU7MPmLH3AY+8MsOXrmJ3UKsEDldW51S4bqp57WpaMJK8c3ACsbmVfA65/teL5z+ww==", "signatures": [{"sig": "MEUCIAFr6QIy8k2P6EOBg3pNAjVtKs56Dm/7rcHsPh5sdnNGAiEA9Owm93xL24qlRX5yHxZK8ZtCIe2twZbDhcx8RWWuRSI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 92610}}, "1.2.5": {"name": "@webassemblyjs/wasm-parser", "version": "1.2.5", "dependencies": {"webassemblyjs": "1.2.5", "@webassemblyjs/ast": "1.2.5", "@webassemblyjs/leb128": "1.2.5", "@webassemblyjs/wasm-parser": "1.2.5", "@webassemblyjs/helper-wasm-bytecode": "1.2.5"}, "devDependencies": {"wabt": "^1.0.0", "@webassemblyjs/wast-parser": "1.2.5"}, "dist": {"shasum": "94d78b6f3fdd601ec41927d831d0db4c5ecf03b0", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.2.5.tgz", "fileCount": 12, "integrity": "sha512-Py16sqUEf2EH0drgM3+MpomE+zsuhnIA/DfY2Pqcd0DxHVf/xnJlzCBO0+pqC3K8QUtUWtzaXyWjRFlMQaoUbg==", "signatures": [{"sig": "MEYCIQCgBP4uUDUcaC//uGr1O+S+2XbHFH2NjS/Gfr9OsJOsUgIhAOUX2fGeluY/wm3oFQsyzUsjgAHWZKwBAkmp0Sjv06g3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 92610}}, "1.2.6": {"name": "@webassemblyjs/wasm-parser", "version": "1.2.6", "dependencies": {"webassemblyjs": "1.2.6", "@webassemblyjs/ast": "1.2.6", "@webassemblyjs/leb128": "1.2.6", "@webassemblyjs/wasm-parser": "1.2.6", "@webassemblyjs/helper-wasm-bytecode": "1.2.6"}, "devDependencies": {"wabt": "^1.0.0", "@webassemblyjs/wast-parser": "1.2.6"}, "dist": {"shasum": "eecf45094055bceb0edb093624412bc4cb17b3df", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.2.6.tgz", "fileCount": 12, "integrity": "sha512-1ACdbuD+Xrq5ABffoYr2ZITKLrczZA5So611aIOZZWC0YygBPtUJtuOfa0e8NWpFIwylbJNTUouMNnfLzDi95A==", "signatures": [{"sig": "MEQCIE1y9oOwIdsTA+UscwVPWWRYXpS4l+kzSEjzJJlVjsD7AiBHtWj9EpAgPW5zSbd2mnmLuEtDQrMDhNyZeHLws9tyLQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 92610, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa1hCkCRA9TVsSAnZWagAABQcP/3yE7I4XbIvpl8c1Fx7E\nbsLlNJ2WyKWjOZ/tV8t8e+AX5zg6yOixLIfU2zUsfNiqjnQlulUMnCgfFbww\ngh70ippF6gUZ6Vt6M/HVI9T0BdoXsOR2IOxycJ/j3MnczkLic0OK0NXYhwC3\nLucYjYVHDqXtV+k3oH0iozRYVNnsJx6fMNAAjCooRcI0n3Ax1JEuArzsPIw8\n1Ud+k3XRikyLYaPQ0ChinEDUYCVZhWlqNkjiXeGJwsYIZ8wjJpQ+QSXu+LPE\nciaYYHSJkaMfEUjwq05GyNXLLfeqf/cWljthbqg0sULjjaH2jVbHosvoLkO/\nWnn12Li5OrtFv5PyQwR4y7eEIsuY6EK6/knGJbemE17w510Y+CNjXDKdDUds\npyNXo2EJI6I8a6WP59ncCq9LQTLfj0hEhZx/hRy78m+x7creubI6dfVbYgog\nFp0Z9b6jXHI1hAhkzWkrLhM0XJ4LCKmQ8hAPWutgzx/yhpJuZTZllInuQyGP\nkZN4zqdO5FKyV6Fc6IKH+1rhGlpyBWNQacaO41laSx02tr5IGMKt0Qb72pEN\nC3XijgJJnBUkQxSX3Axu9+OMFHPKE623Wm3VMXl51Ausd+p0fCfT8LWzfv6f\n7xkWwyGbY13aO2js7iTnQG5hafL0FqlXw0Yy3DunNdHuznHj62tqcm2bXHdr\nzboe\r\n=fJQS\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.2.7": {"name": "@webassemblyjs/wasm-parser", "version": "1.2.7", "dependencies": {"webassemblyjs": "1.2.7", "@webassemblyjs/ast": "1.2.7", "@webassemblyjs/leb128": "1.2.7", "@webassemblyjs/wasm-parser": "1.2.7", "@webassemblyjs/helper-wasm-bytecode": "1.2.7"}, "devDependencies": {"wabt": "^1.0.0", "@webassemblyjs/wast-parser": "1.2.7", "@webassemblyjs/helper-test-framework": "1.2.7"}, "dist": {"shasum": "cbdf0b2882004e07be071719c99f30447b2a4294", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.2.7.tgz", "fileCount": 12, "integrity": "sha512-Z1+3sHz2o76bfdhfgXvI5khPSZdJtSsyfjIiQ9QE26s3nlPh6DyIFx9s6c3gyKGvI8xCal6MzLJC5yDPYH/XFg==", "signatures": [{"sig": "MEUCIQDXAtD+epzyJv63U6qGgOhrYj7LH5QJI2AYDzPAX9ocsQIgJ2b+hVcHNk34XxSQi+MtCNdh5hg//ihxpQtmun44R8M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 97424, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa5yuLCRA9TVsSAnZWagAATAQP/21anrSM5JlBen/h6VT9\ndpwV0j/iMx/ZKfq0ksgZ3Drz/dmCnlBurhFt0TrWVpOXHRLCYB4XmpYKDrFw\nLJbCM/Fp8/ZZeGhl+XtHeVBa/lBGSz6Jr+8ppgvLKcXYB1kT1b/P6q7/rDS2\ncTrq38d9XzKyl9YeH0WczhDWHBrmBG7iOSib+F9I5+GjKSd61bHZu6ysztuR\nqrkSaCZL6Wgqbat3sha1ejn5UrVvCyJKDS+QovJ42ZROjNEFy9B0DSTFZ8S2\niBpYZrTFZOEwcuwHF6A8fAKLSABp6h/pqhOfq5M0zFaZ4motrWNenwAIJaP7\nUy9OoKIFO/vaoB+MFGxjSSGqDIJ6QGBYIUQJXlG4r6lO9xRerKsE+BEi2yRV\neV+3VulSwExRg0B3ay40k0wcADYhE2+rKRvaA1+qM35IWFmDWXWLjIMn1veX\nJgpSJ84UzocnaRSLYVt1aCPi67RQJl1UJz5Po2m17Zo5ElqDhiUkObjDg2fJ\n4HSr0L4PSKQAPdNXbSkvVRzHKwbUrGypjEZ3kvZTkMnCHgrdg4fzqMbcbUeJ\nuGGL6KZyh49poFkKYMdxFm2/1ilH5yUiUKzVuGAZiLmGt/lmCJiIiqJ9ePQl\n+cXgUNw0r0XI04rUp1xPeMQSCpskTW5JdUGwIETHXHHms+EhuDwsNPxDDb++\nb0Zr\r\n=Cwh+\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.2.8": {"name": "@webassemblyjs/wasm-parser", "version": "1.2.8", "dependencies": {"webassemblyjs": "1.2.8", "@webassemblyjs/ast": "1.2.8", "@webassemblyjs/leb128": "1.2.8", "@webassemblyjs/wasm-parser": "1.2.8", "@webassemblyjs/helper-wasm-bytecode": "1.2.8"}, "devDependencies": {"wabt": "^1.0.0", "@webassemblyjs/wast-parser": "1.2.8", "@webassemblyjs/helper-test-framework": "1.2.8"}, "dist": {"shasum": "ebc03e42c4c16c6885d7d2c58e9f88c82f668c17", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.2.8.tgz", "fileCount": 12, "integrity": "sha512-KgBmGhtMQHeYx1fB95lpkp9Z4ya9gRq4FEFJUq1LcKXeBfERmHKfWTsXW76eDiF6ljYKhlDVHxCMGNy/uWdalw==", "signatures": [{"sig": "MEQCIC9Nwkc63eXNnlvZg+YQul5rzC0k139qr6mNPiiLIT5xAiA2aLiF5TJFzoIbX6sJkbmQ9sUHrEN4sxKmEYX5VHsOdA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 101600, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa6fRlCRA9TVsSAnZWagAAT44P/0HG1Oos3R4MkN39l0zz\n6xfzNZJ4e9xDEyUgwqkyEKbg2u3WzeT8iwPcH5bRpuJlDoR2ZZTCts1jXIez\nupXAaQDHDUckzxFK/xkgBcaImFqskD3wHYEsxO+Y5mXQGEc+8Uz2vu6oNJki\nWHaCYUPAgEIcqAag3lAVzcvXYvwCAV4HCpVVpoQvaT2LC/HmAu+InXSTUDjs\n2SzLRev//0L7bumpccCoD0JcP3xRIIumH+Ha0qDW1Zfv27nIpvTRjQGhHsE3\nY+p48GsTb50+2b8eEDup5jb9OpWhR3me4UwpT5dPJD8jqC5BeFCvPjV1rTzJ\nCJM1mUPQ/VMtZkZcTuIJm8ozR4cxO3ueu1WsnwubvMyZ983E0YqR2wuHxMnF\nhmMfcdpyHK0m2ZjLCf9ZbvvcVkvAZRDfEcYy4Lq6tzso8adVsh8Yt2cPW7Sf\noNmy2D5pIP5mAwAXZGID5C717T80rUQDLQ+Ul2qfzpifuNRTOgV8k3zA/shr\noxnAe7yLYWWX+Ogq+pbZyijPU8scS4CtKTFAi/NgDNvrfyrsezP4TYVC/Ma2\nKjs4Rh4XbPvTDT47lG09WwDTYtx1I6hUA71trZyQe/yWE7cGnwitpLlUTWUU\nVKJkKJWP3vLjwhIsBCkJw+U/3g7xWfwrlyMa+NEPYygCPZJgx7a+uzc3i3ov\nULhc\r\n=Famm\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.3.0": {"name": "@webassemblyjs/wasm-parser", "version": "1.3.0", "dependencies": {"webassemblyjs": "1.3.0", "@webassemblyjs/ast": "1.3.0", "@webassemblyjs/leb128": "1.3.0", "@webassemblyjs/wasm-parser": "1.3.0", "@webassemblyjs/helper-wasm-bytecode": "1.3.0"}, "devDependencies": {"wabt": "^1.0.0", "@webassemblyjs/wast-parser": "1.3.0", "@webassemblyjs/helper-test-framework": "1.3.0"}, "dist": {"shasum": "66dd5ac632e0f938b1656bd46f01fe5f5f9488d0", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.3.0.tgz", "fileCount": 37, "integrity": "sha512-eQxCJa2Ks0lZc/JwG0A0uzvOmbjklsVKtKI+M1qp73xbOgpotHFBioeskk9J8AlpNwB5Ofeslw98O5k4Wi8scg==", "signatures": [{"sig": "MEUCIQDhmtEkTaW+jMIRrstS9IEBDtMiOcv8GIv8sIRrW6/SiwIgAl/AtKtrSAxCk04zUOulpKnCmBf8o/70FgXeRQFiNRs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108203, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa7GCcCRA9TVsSAnZWagAAzPgP/2igELH8ivi0BGquB8fp\nRXWtwfYlEz+gXCsKTkCAkSMz6gHiSkxBLnepA1o1pWSPav1wa+WXcoQTNZsR\nS9zpTIys56hGzIwA+1BmUqjQ+ZEjfo0w+fOEFwb1Jp5afVX4zqoDgdnSJQFC\nthySpdp5gYpAILaAKeyKC8QHjPEIzYrKC9bvxclXaRyrUtyGPX6W419668iF\nqGdJpkC7HAIEpWOk7gRSX9CIVkD3pm0RaW0TZ3CWXnpyA9IOlooqk108BoL2\nUboHiPkWHOxe9bTMCkhd0rklmZwjsvo8tTOpXEGxXNb/IXcWM0vY6RDTySwi\nZ7t+rxYx+z+P2gX18/Nzi7ljmTgiQOQ9Mn6FBCdUD86QWbIFv9+xlm2Qab8W\ntDYIYKnK4PFl9fW7hVnYfUTYM5MRqyco6byEo34JLSoeg2FsYBOc4xypGL7I\nDm9OJc8haD1EJEAwvN5X25CUDS4c4qWGAtJxQ9EJ61dWceOc+2OwW8bRqeHL\nmIrQG0bc4Y4R2rgYaSzzrgbq8bDhTumr4Aa/wnPHmx7QfkkUKMr21SU0xthJ\nZia757MPscCQDIEVtgse31ipxkkMewmA9f2+FXp86dWxkfXOmyC2ZHVXEZxL\nzxggfmGAth6ssq48AC2ZVcaBn7vFTknGUoJtGWKDsIuD153IEW4QuLEj5qf3\n8DDs\r\n=7daR\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.3.1": {"name": "@webassemblyjs/wasm-parser", "version": "1.3.1", "dependencies": {"webassemblyjs": "1.3.1", "@webassemblyjs/ast": "1.3.1", "@webassemblyjs/leb128": "1.3.1", "@webassemblyjs/wasm-parser": "1.3.1", "@webassemblyjs/helper-wasm-bytecode": "1.3.1"}, "devDependencies": {"wabt": "^1.0.0", "@webassemblyjs/wast-parser": "1.3.1", "@webassemblyjs/helper-test-framework": "1.3.1"}, "dist": {"shasum": "76727be6c313a9b775170ed38a126558eed7e8ef", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.3.1.tgz", "fileCount": 12, "integrity": "sha512-euRnqP/bky9DGbXf5Nqvtmc5soOM7xkvwI2H+LSP88NAFkcGMPEnrAP4UbsFAH+ESnS4kAW6wL5UyzjWBiiTbA==", "signatures": [{"sig": "MEUCIHJ9ZDQ9Bl15k86//5st2S39UcP0RQaqvrJjmHK7uwaqAiEArXqboyUVLKnu2IdwjdWx3eH+SndJ9S8a2+GKq3Xz1H4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 105076, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa8JZFCRA9TVsSAnZWagAAvY0P+QE6j5VP5fY4EgjdL6av\nLXf4bZe4ATYmHOrt/ajBCGeBJ7FikvyWgk1aGx9gFAXSk0yvzs3fQihtRPn2\n9xrY2ztejZYpXBIsgKnOzTnYpuX0rZHb+FfvMirjtIE9NMFsdHRfZyZXJVWr\n6m8NelLdWGaJpClIEshsicLnkL16oaujBcbrKIGRQaOLnwJcKhBl7blohvSI\n1miAv29pwjMwOwADeXnnW13l/IHjn7AAsi2pn6nCDAjeJkSB02QZmvvvIF0z\n1W2rX9pgzRK+0KSKwdnvVB3ftVjSdOGIPdAxWTImLuLS7fd/dESa9MbjSOon\nuQseYiLgy6qZGzvbTNv1HPrN/H8w+Z6NFP8DCGf54ngYFTLNI4dkv115b5OF\nMrTs5I9gN90YODtPZ6iPMElRRNJ1EJkwECqv3ZjgqwdTx3J1RO9I2e2XWnDf\nklZ/tC4JKUtTn/tivOZ8P8d5V7KaW/iX9+rNqwWVv4LiSvWtP2w9TcS4WrDY\n57LwYjTgNMTdsMsuwFttPJakUMSsMk35ZGz03VyMaeQZXYLJhj6U3JQws1nN\nzIg0XPNus/zC+ip7IgHi/f5WZ6TPHnUInP+Bi7v1IbyHTkpjaOK2oxWAUNfi\n5WauKNwK0G1Jr29X08/u3j6tZbEWTmv4CVucAlRhv/It+FqN7Cld0g4YKLmJ\nAedG\r\n=KIZp\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.3.2": {"name": "@webassemblyjs/wasm-parser", "version": "1.3.2", "dependencies": {"webassemblyjs": "1.3.2", "@webassemblyjs/ast": "1.3.2", "@webassemblyjs/leb128": "1.3.2", "@webassemblyjs/wasm-parser": "1.3.2", "@webassemblyjs/helper-wasm-bytecode": "1.3.2"}, "devDependencies": {"wabt": "^1.0.0", "@webassemblyjs/wast-parser": "1.3.2", "@webassemblyjs/helper-test-framework": "1.3.2"}, "dist": {"shasum": "351d368f4a0407afd8ac4454235bdeb2fb8a0913", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.3.2.tgz", "fileCount": 7, "integrity": "sha512-uEded+SDVlRL2UWOCc8WowLVcRhJQZlQHOJ3XJ7z5mm9RLNixybGXI15uaZF9RSrvi78B2+RyejnDe0xZti9ew==", "signatures": [{"sig": "MEUCIFg+KEhkFB6bP6KSC04jBpiKplFZD3U18/KEUIpwHHyTAiEA2gQpqxEWnqqRAovnnClNO2/fFjVkpMMSyV+7OsZyie8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53491, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa8fC4CRA9TVsSAnZWagAAG7wQAIohAWkBs1fAAuRWYXbx\nhB4k5RDeZ7tO7yRJdkNQAqZ+5WSjRWe/8DoRhcsermNzit7PPQ6chdlHuE12\n5IfscKMyRjFtX32ocU96VBlcIW91tpRw2upSZ59tT3DIvO8+WjYo1FeMrNsD\nEhPzSdoH7ERo3wJ/RJDLJkZRnALHK+HYNJYf2cHMIJKsicJPDJsrrREUb0J0\nWLsBggzlmEkHeXUamfpxKFzOBghjHehwP0061dFbpC6Cp+xbb1ICvQ6vslQ8\nJRv8pKBPCwhm21bomxN5M3rV3WpWFlbspIjNVs20HpgJaH7bofMiMGGbcQF0\nertM7KGa+twOEYzwyKyKJiKXVrmH44x/OX1wMdsdRnKXdK5Wb+6etslBaCSX\nWvL5zi4NHs3ynPX8284SefqR2pVnk5LbMyVvnxCC3d/sien0olHVV7oK9HNc\nqqGV9TPgQzohtzMTZiEkwrMBuil48Vld/L5AMKuTjeBtTeGVqSXavI7TLFgx\nN60uw5DuMKs8T1kDJVc7T/VYcfl8YNxYHTXxSZdPPh0mqhPsorQshIFnnA5o\nN7eKDx/uPjCa5jeJpDMv1ku87hYd5+RhiWEwxd/cj6YdRtJGOLGNJfmKbXcY\nW53LRBTXYm+C7Sg7BAfR6lHp/18pxLZ2RxqCpCtZrVod7JoRwuvqw9Un9WEl\nSRdk\r\n=u4eO\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.3.3": {"name": "@webassemblyjs/wasm-parser", "version": "1.3.3", "dependencies": {"webassemblyjs": "1.3.3", "@webassemblyjs/ast": "1.3.3", "@webassemblyjs/leb128": "1.3.3", "@webassemblyjs/wasm-parser": "1.3.3", "@webassemblyjs/helper-wasm-bytecode": "1.3.3"}, "devDependencies": {"wabt": "^1.0.0", "@webassemblyjs/wast-parser": "1.3.3", "@webassemblyjs/helper-test-framework": "1.3.3"}, "dist": {"shasum": "0e534ae7367022bfbc8309a2813654c165f95288", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.3.3.tgz", "fileCount": 7, "integrity": "sha512-6xASVyjRrYViFOHPKUvwtGFMS2+DbCwJkTaEd4Dc0s5V/7VMZiuF7RNMGn+crXMLgqJWjqmfSPkX/mrvjfLtPA==", "signatures": [{"sig": "MEUCIQCGoNPe/clXUSM6nQFkZKMxC0nHtKJz98IgVZuAWUgo0gIgCROa0v9VcM+zLB12n9n26PPUylZUUUR969x/5PTqdq8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53491, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa8qjsCRA9TVsSAnZWagAA8voP/j6FgkXyx60nv8j26N+R\nRFPuBUoEp/UZiWlOy4Z8PG7QVKANZuXCbS+/dKI/+Dr9CX9FEGu08gR6ifPJ\n4kLnBc9pboqJukzMvRzoaOV/pv8t/0o6clwSahYZJ/1285CylNAk21EgumMu\nTvjOhsCSzUEuZ/LLkbO2A/NgQVdLKcB302ZKJnYo6RC4wO6tWvX/013GIucE\nsSFDOZsKa1cMmcyFaa3qtDtyw4XKjWjyL9gtTjiAT6TuHkGyKKyp0u/rDFC7\nFeYcPvDuPUHVXP4ZhC/YwnsWzqA+lyr2kV1hy771FURJkO0KsXs3NqOXSs7X\nicqzJV/ehgdDgHKEuxpq8x8wAZOv7PO6BVMasaLN+tpy/pNEtCp/FydZGAC8\nAiwXl61Cmh/elSHaVk51jbpwosx4lCwspO7PD+wsnacXMg4Zxxgz7RVFDy01\nFZsFN1ypBn9rNKOW2q2+vniY+wWd1ln1+osArz2o1QS6U1Yl8fruZOLx/ajA\nm3VPKDXLZ2phZu5TDy+MOLm7o0LJKSD9dzC46vhcu1gLwlb/Y2ep6d78RF3r\nfHnXG3ByTtdxcfKKgwDihhMWL3mDOFZWRHKnnn+4hzSGx6Gf9qqgtQHrPduf\n/xRYsM5JgZ9hdJ9vJS0Vv5j4dRhk2/+IzEv1Vflm4pYjyictOiRncUlInnfl\nxpz2\r\n=S2YM\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.4.0": {"name": "@webassemblyjs/wasm-parser", "version": "1.4.0", "dependencies": {"webassemblyjs": "1.4.0", "@webassemblyjs/ast": "1.4.0", "@webassemblyjs/leb128": "1.4.0", "@webassemblyjs/wasm-parser": "1.4.0", "@webassemblyjs/helper-wasm-bytecode": "1.4.0"}, "devDependencies": {"wabt": "^1.0.0", "@webassemblyjs/wast-parser": "1.4.0", "@webassemblyjs/helper-test-framework": "1.4.0"}, "dist": {"shasum": "cf98ef396ad5553196a614c11a4b2ecfd13f1e82", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.4.0.tgz", "fileCount": 7, "integrity": "sha512-WuTU2j9Lb9Uwpcr7eLcOPO3BycEjW5UCpZCB6vw4DhWsI1NWergsMQXqEU/RYrAYoQSzQJ8R0lND3luR+MkDQA==", "signatures": [{"sig": "MEUCIQDb0DVa6tow+EQz/cR8/rlF14/G6BwUCJ1Eq6XhRV7oQAIgXzKk/adlhNXPaaToHcQ72h6u8/l27wzWu8Ml4Xcpmhc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54093, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa8wyYCRA9TVsSAnZWagAAT98P/0tVLd8QIoL60VHzczMQ\nNGGVowpkO4VEeSCwOioNdVBBDm78W1qmBTejyY9w4UJxDd9SRxeqhzPHyBDs\nMOurDsS0vWo8IasYBJ7rLa52BM7ph7vuigUKf5CiEUJ25y1gT70i4ICxFN3S\nLP1TdGX4m+N9xiEwb9cbl4+UQiazurKAHfdXVVrmFFvUaWyWxUxbSTrb1e4R\nnqsaaa+2JFjjHpm7WuPffh+u23zcJKqzLA2Lh6BDJQX4TmCdp9HPnklWlFpp\n8b4QepfO82svWl2qHhYMPb8Gc91GWqEueRea/mYT7zmRxBsBzBx2CvcjGwyW\nman7cZMq61P5AL/p9oKYcHtxvghKSW3Yju0Kfady3i4zQ20h48Po56sL2VzX\n5QNGQb+uCvqp1yhCp3rKZji8EgLNlXoc2dn/JUjpvnI6SSVqFgsDeqCw1NF3\nZysPgda7nX0MtCBbGXpn4BSz3LrdClC3K9JQ288U1m+NAXhh03kui1QpfFba\n99NiYmHe8iFQzPlPtJkV0bfgfqH041goKfDrU8F0aXNbeL9VVh9iktd0v0bp\n5pAEmWRBPxdpLEt/q/8z7EKkeaqve//58DsnvmBtbyt0GI2C1YdcUksS5f1t\n57xh6ns02lTMdTZw5SJaQvmykySJ/rqyvsRiQher0VBovhNVYk2yPwiAj7lm\n8Rqa\r\n=bOBj\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.4.1": {"name": "@webassemblyjs/wasm-parser", "version": "1.4.1", "dependencies": {"webassemblyjs": "1.4.1", "@webassemblyjs/ast": "1.4.1", "@webassemblyjs/leb128": "1.4.1", "@webassemblyjs/wasm-parser": "1.4.1", "@webassemblyjs/helper-wasm-bytecode": "1.4.1"}, "devDependencies": {"wabt": "^1.0.0", "@webassemblyjs/wast-parser": "1.4.1", "@webassemblyjs/helper-test-framework": "1.4.1"}, "dist": {"shasum": "363d326187d495024eacd63f8720576dded5b4c4", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.4.1.tgz", "fileCount": 7, "integrity": "sha512-du2umVV2NC/VFmZVH6cqkCHEN6yrJsf+ZiFAjspKGuWva/OgKVIgjv7Bz8C1j5iyftzDQxGWLi1/k3vL0+xB5w==", "signatures": [{"sig": "MEUCIHV4PplCY2vZMkwKSNdLH0pkCO8P8f9Bd//BvZODH/gBAiEAnug5AyG53voSynEt5YsMl3P7XRxfGmzaS+kKvnNaF38=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54093, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa9KE0CRA9TVsSAnZWagAASQwP/RsIhTKw4JvgzQmXemsX\ndkJbp0m06AAButQIUl770eRczdJMh8uFN6ZjTcw0OTLSXxFBGKOewLdkO20b\ni+npkiW1Jp0PYQ1jQb1FOdfYmDTLJCiBvc7KM60N1lAv/uHdCAXD38MMOvFK\neEi5mP0IztbLOrAkoH/ecQPPeYBrVDRV+u3oBZiT4XTBYAYw21pC53zhABcZ\nsdFahz3iZHinFlxR8sLXG1bXPJVTNbZbC+yLmLP7a01LGSRY0zdJEHXPKyWa\nlbqLw2nyVps5/Py8XTfoP50YA1uX5sguhXAQxBeaIcLs9k+Lu188dJ/xhKT0\nJY1Pj3HfozJBmeH6XKq9cfmHdkTStUE6GoeO7mnxZHwz9UxzSRBiswPuN+iZ\n53lxCjgxnBs2WShWMsSeTssFPRLbs2GKHswfxolf3MG2RAveEyNAPJ1Do7BZ\nVtNko9otoD753plM2MactviG+5LRG/CE6knCmfHhIhvY2Bqe/criKbSLDfwz\ne0yUIHTi4hvTwQBJwdpWH9nlW/c3beozsKHnvQeVLpG0SIK4EAVQYvLHobsJ\niMQCr7ts3yXu6WjAYyLE4SVk4dei1KSiF7RKSYkETYqGagHV8H6zcttHSKIE\nAdkkQDALlaTlqRlCwDHfwyQEXmvGViSKXxReFicOX9o41iJ0jR2eEqKe7RdJ\n31m/\r\n=T5im\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.4.2": {"name": "@webassemblyjs/wasm-parser", "version": "1.4.2", "dependencies": {"webassemblyjs": "1.4.2", "@webassemblyjs/ast": "1.4.2", "@webassemblyjs/leb128": "1.4.2", "@webassemblyjs/wasm-parser": "1.4.2", "@webassemblyjs/helper-wasm-bytecode": "1.4.2"}, "devDependencies": {"wabt": "^1.0.0", "@webassemblyjs/wast-parser": "1.4.2", "@webassemblyjs/helper-test-framework": "1.4.2"}, "dist": {"shasum": "3bf7e10cfe336db0ecdea0a5d7ed8a63b7a7754a", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.4.2.tgz", "fileCount": 7, "integrity": "sha512-LcPxb3fuvcuwtu9P5PEcf4STgeoyiDsHN59Kkzc2I9GiZQvYQUHG3R4WmcHN5WugfQQUiY+ylJR3JRXMna58oQ==", "signatures": [{"sig": "MEQCIFr231rhUHaBw2wkAgnimi8w7QjUAPFmXEdLPTXdWGpCAiAZvMx//vpn3rDJ5oUJL43MYc+k+Ddqksp9sdOZghwt3g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54260, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa9aT1CRA9TVsSAnZWagAARWsP/R/L6OTb6jZfuLdgxEIo\nLb5ZHt7g38UvwHDaYe9g+WALti37E4KYLTiWHIE10Kvyvz3SLH8RWFDLp3cP\nF3ajHCiIXEV8yjarTUJavC2SU2P77mOXLUGxsgSHSQT9KlDd9sT2hplK7u9r\nJO6MiLEAftg1z6Q7dhBl7ff7+9td1ilNpB8z4/W1WoXzxp6C2SFBVJGSEkM7\nFM+tyAUZsm3e7cEP15zP2ukzEQe91JzpuKOT2dP8tnIJ0+6MOMUxoEyW2Hps\nLNc9EQXFL5Dh/yUp0Suj/e2Hws33XO8zPg/nffQy0UTpB0QmMcXoFOoUwJFQ\ny2oMPpMn4oRBSfjNadFEmTY8iEajAnHIXp0IAW7jfenU+4ucGrfqRRqHJVoH\nYPb9Lz28KJJ1zbH7m/6tWMgQJ10xmGhO6RVKVIDkdDmJEvGZxYNhbljzf+hv\nL42y/18FtjDmbOnWC454lpcQN5A35TwNhRT2Bnm/rYXjrYcDnH5inhVDcuGV\nf4LCp9mgkwKd+PtHyW/fMb2HoQsPtsLlXqVhJwMoJKa4O0N/kkhw37oTbyR+\ns0xPplK1xHcxPCu2F2eFVZx6extZgsCSlaLLp64kjQrfC54dF/sIWy8rcBFC\nWrn2u7ajsLaiTIlmjOS4XLJaSBNIx7cF4DdRb4m0HnvduXRcrYPdzx4s7O8K\n4eiQ\r\n=lOzn\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.4.3": {"name": "@webassemblyjs/wasm-parser", "version": "1.4.3", "dependencies": {"webassemblyjs": "1.4.3", "@webassemblyjs/ast": "1.4.3", "@webassemblyjs/leb128": "1.4.3", "@webassemblyjs/wasm-parser": "1.4.3", "@webassemblyjs/helper-wasm-bytecode": "1.4.3"}, "devDependencies": {"wabt": "^1.0.0", "@webassemblyjs/wast-parser": "1.4.3", "@webassemblyjs/helper-test-framework": "1.4.3"}, "dist": {"shasum": "7ddd3e408f8542647ed612019cfb780830993698", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.4.3.tgz", "fileCount": 7, "integrity": "sha512-KXBjtlwA3BVukR/yWHC9GF+SCzBcgj0a7lm92kTOaa4cbjaTaa47bCjXw6cX4SGQpkncB9PU2hHGYVyyI7wFRg==", "signatures": [{"sig": "MEYCIQDHNQ0wssBRbDZ7bOsT6ZkC/wVI85+44sCyOVIiMfx8zwIhAJfZjg4iBpzYBpwKmqbcuv/xjlRqIRd+PMLNUsod+ty7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54260, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa9rIJCRA9TVsSAnZWagAA/lYP/inPVxM08kuUJaxD8OCi\nkwyhoKLkcf1/XhA94ZbtIF6M0UvXrvxWA82/j0NeV3hDGelTO3A4FyIsJ+Ev\niNoBZ0QHj+dAkVPOM2/SnCUFd2CYODvnrIdcvLZGXJSxTqvJgfpLIssU5s15\nIGc+MIscj8WSl/rJpTReC9PTLk8nggZ7/5fPC2weMEHjsZDfdMRrICsZPYz7\nQkffKXkqsWoHqnGYKXQJuFAqeKtB7bS380xfQtRdkA8wmjjLH/obD90sSU+g\nAIdfLw2X6TtKZ5+F80xZLT+t7EgEFRbVgdzgLunNuTMeOoaz8Am2XyarTysM\nFfyXddv2PtxkmTiVxJCMZeqWfhLEo0c1YsnoRfGVmUmLl7F+Eph9QYJrn6jG\nUyc959k3xfvI/t6AGSfrdk82NxNO7W5APUL/ex/Z+6Obc9dRo83NJvHSF9ea\nfuYUhzbHVak0ktFKivgYTa/Qg4opOpi/458lOU990O26wqLxf+LjRLl5ZBtf\nGGQ53LqoSIkHymYC7saRdIRNF8vJCqBrbb1/pOPPr+fTZ2ViyIazTfuD+ubb\niv5C585jZIdv40yii2oySjAVdkCk82AUmM4o8LIR2lCQDRN7WLhyN9SYshTv\n0J7Cb8V1ON2rx0pxhDm5t5W4zOocizsxKtg2KzGcwCm/0RfSTaXCwXXTQXvU\nYhV7\r\n=h3ul\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.0": {"name": "@webassemblyjs/wasm-parser", "version": "1.5.0", "dependencies": {"webassemblyjs": "1.5.0", "@webassemblyjs/ast": "1.5.0", "@webassemblyjs/leb128": "1.5.0", "@webassemblyjs/wasm-parser": "1.5.0", "@webassemblyjs/helper-wasm-bytecode": "1.5.0"}, "devDependencies": {"wabt": "^1.0.0", "@webassemblyjs/wast-parser": "1.5.0", "@webassemblyjs/helper-test-framework": "1.5.0"}, "dist": {"shasum": "d32b277ab8687de84d2a65f6866266796b9cdd27", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.5.0.tgz", "fileCount": 7, "integrity": "sha512-5PaY0DHK2Sjv0//huAwXJ8tOPk7dC9OkZEuZVMTEnjeNr33X7TJGo04sgL1Rq0p0FHje95c6FOtaShkCTopw7A==", "signatures": [{"sig": "MEUCIQCn26ilH08e0xwIuE/DYJ+yw7ihzI/sWHiDbOPAtvzhHAIgYyEL0Fa2YxpcMefr5dv+Wn1fR4jlv6Ygz1RUTZBN7jo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54458, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+b2WCRA9TVsSAnZWagAAa3YP/0/I390oXXLSykrAtkrE\n6+yt8ogE8wSAM7M2qElRKphYtCt8TrC/7IBnbbk3ImmIHnMd8JkmEzmw7HBB\nmXL61RV7kskJlXeDhoyTCusl64Dl5nf7ICOfm75Z5gZOOvhwF6NVuerjHZmJ\n+7FbZAKWbjIEtOGMCSwtpQGQkjsYFBVvExe/dVcf00Tqe0DVD4FL69J3FY4Z\ne+S8mHl+opsHq1KIVIYbhsmerUOXGdXfd4TUBkDe5wkjcg6CFyb3995QLvLa\nCtOEIazM0wyzXZbA6sVBrtfxNr9tfeZi+aag02QOeK/sSwJI5M2HMoVEyY4o\n6EN/cKmLgM3ryZvoNENa64I2W9T+3BcOhGXOGkEFtRQpMFUwv602Az7uZeA3\nKR1ts8uMyYEosUYRI6WkEZ7lG38zu1Fci+7wPAN1wx5+QUcaSDptQFRooOIf\nSvLgRySUkj7c/xCo2F+7R7IJ+ciNpefFT83PKbONrkw3QW/6kqBd+qnlAGhN\nf2OSLMbiOS0ITaAE3KKEgdCMZJqFHVdph/0NppcCfMTcgQr9oXSW1VOBZRFO\n2EqIjKSdgpzr4p06dVsrG0qWFefHEUV8og+hhkFJ6aJ0A5fzFhBPThIfAwkK\nk8OPdufn3kQeoAy86t1+9cBAiBjcsGd/Q9SZPTUeKBDMikUod0XpxH6qDyNS\ndJr9\r\n=C74w\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.1": {"name": "@webassemblyjs/wasm-parser", "version": "1.5.1", "dependencies": {"webassemblyjs": "1.5.1", "@webassemblyjs/ast": "1.5.1", "@webassemblyjs/leb128": "1.5.1", "@webassemblyjs/wasm-parser": "1.5.1", "@webassemblyjs/helper-wasm-bytecode": "1.5.1"}, "devDependencies": {"wabt": "^1.0.0", "@webassemblyjs/wast-parser": "1.5.1", "@webassemblyjs/helper-test-framework": "1.5.1"}, "dist": {"shasum": "ee8c5562fa6a1213dd535c4b59dd2d7760bbf0f3", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.5.1.tgz", "fileCount": 7, "integrity": "sha512-aYCwZWb74/gMuj1JGeIfgXjBf374eDeMw9ThicGOCsJrKtLmzS++Oj8w3/z6hyUICLW9G1MO/35GjTkMCkbiYA==", "signatures": [{"sig": "MEQCIHpzmOzSqd6LL9T2sYt3acWv/l3sL4U0c1+Rg31Isg8TAiB7OyE9E0vDQrpYwhsMTpGfjRtJw4Nmg9gsLB7qkRMDtA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55228, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa/BUACRA9TVsSAnZWagAAg8wQAKUT5CWNGNN0yCH2Lruz\nfzFtF0uE/1Of29tZKAEDk6qbEFr1+Y8/wc1Dsxuz5/A9zXsev1rMPSUjVBmv\nkN1pQrjzf4YMbop6656WXCEMv36cADHjOrLjATcm+oS49t3hWzZuJc6G/r26\ndHMYpPI476oUjsiaVn6F/vnfE/too287hpnuBYD6o1oYLlie5je/57rUVOR9\nRJaTeSyDHtsiXPYfF5f/hMGqH6m7SMj6nIEhfOFf1dnJtCE5qQaeFF4WRrqn\n3O0Lhb7LOs3T0wf2dRKWTn0XO7ryEkTsetX3XdaLjbo+yoXFVOpJrbW8FD33\nIjMEsfnO7CG/Sg4d35wWtSAilMUbh8/7mkcCOwTvBR3T6g6Hent4SGZ+N1xb\n9pNW7Tc1+Rmfgb2V7R/uzxJYbrm4qPYKabzSKk6zmyWbMub92mNgFpOd0kVr\nnQhR70RKww//iGwFPz0Z4O/Dwks9+LK/sa/51k/oiffxMXGosdKxKl2aq2Xb\nCN5e0rUJMyXVEHiaNxpcpBtLh6wUZIUXjnGgaCsA4a5OlaSF2Bx8eHloZnWN\n21IjySz48ZEPGKrwJzlJenSXRerDes64e9Zh5E01O3Zksz5iPI2lk8xNPDN+\nnXXmlVGzhi+10DV7DhM5nAMOJ4cHByq4LuXGfqN0EtQHHB5+c0LWbbyduzQP\npxYv\r\n=Ixes\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.2": {"name": "@webassemblyjs/wasm-parser", "version": "1.5.2", "dependencies": {"webassemblyjs": "1.5.2", "@webassemblyjs/ast": "1.5.2", "@webassemblyjs/leb128": "1.5.2", "@webassemblyjs/wasm-parser": "1.5.2", "@webassemblyjs/helper-wasm-bytecode": "1.5.2"}, "devDependencies": {"wabt": "^1.0.0", "@webassemblyjs/wast-parser": "1.5.2", "@webassemblyjs/helper-test-framework": "1.5.2"}, "dist": {"shasum": "30168712029e3d758bc02f97a1944a3889e5d99a", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.5.2.tgz", "fileCount": 7, "integrity": "sha512-4HDhcUyOu9JX8NDRDTFsEStA3cx18UWGqXQrwrwng0vaJPnZvigWowTqfAWpstYNqn9+7gd2cSSS+AoqPaI5cA==", "signatures": [{"sig": "MEUCIQCyFGJFJcaWXcxy+CLvProZ7xYMP1VnsOsfqEgk1bsQBwIgRzFfXIRRvlZwXY8ocP7MnfLLyUDStxyDR/IF7ltvqh4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55228, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa/XqYCRA9TVsSAnZWagAAly4P/iJveVsY4m21Z5IbXoZn\nJUIWZcJO5qUXQgWYmRjDZPZkSB+7yVT85bCHL5Te33zk8K3dnhCUxgJzeflg\nxDJma3VcATSbWG7j2JtMNsICwSprTYl70u7euXbJFxJqms01ocXYBTxoXj6r\nt5CYVb/qc9JZkdhGsAk8WK7jbmVdepJzZrvEVnxbhzGNGhzkwTn1CZGB4Oum\ntGgyG6f2afB9eSwlPqv67Ln3RAmVMggpR89P1nsmHgRVJInhZ34Yp/f2N3Ou\nkqdlFZhm4G9zguZwdZL91oH05bBcJhD5GUDcSNwAhJsKOnP00NQCB2OwhyBv\n06ZXPijyHPBxgqSymtcXJobACNrDo5lZBDMIPKFbpy2xMkYZuoLEddoFLEfc\n/A4HE8YR2qAfIhOYBgcclX938FspSiRl4dMtxVQ6Li6Ed5XAojJBklH83ckw\nTwoZdSZ4Jq1Mih60Rd47XlWzS+tbIycnhkjhvA9n4XOSYHMEL84/jeswYqYb\njHM7JS+8GkP//NMMA9WIgXEMBFj1rs7eWoPDxOSop/RVvFNQelaI0EVFiXuv\nu44rD2KGy5Qf0D+8UNNj8xD6Lc0P+NMlXYXrjMAHvuTMCR5XayNkur7vMQTo\nX3OQCRTAYACrnal4p67XjtSIhzoxkSvePXFvANTz4MUe3ILXbLgMI8xoR9BS\n8u/g\r\n=qGJ1\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.3": {"name": "@webassemblyjs/wasm-parser", "version": "1.5.3", "dependencies": {"webassemblyjs": "1.5.3", "@webassemblyjs/ast": "1.5.3", "@webassemblyjs/leb128": "1.5.3", "@webassemblyjs/wasm-parser": "1.5.3", "@webassemblyjs/helper-wasm-bytecode": "1.5.3"}, "devDependencies": {"wabt": "^1.0.0", "@webassemblyjs/wast-parser": "1.5.3", "@webassemblyjs/helper-test-framework": "1.5.3"}, "dist": {"shasum": "f7b4f6c3346f9ca9e325b578a780ee513558917b", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.5.3.tgz", "fileCount": 7, "integrity": "sha512-bO3oruS0utmaEs8hUFAQ/VgXuwCx3FukTGyRiqsCFCR70qxIY0mC17fqdR+tIk52t/b01LUY5t53mXticQ5gXQ==", "signatures": [{"sig": "MEUCIQC7ioZck3RYlzgIW32kax9DqcpzbN32pRxpKtC1WN8tvAIgOgiyFpsOZ9S1Ei0DgfQa0rYMCuw6iCplCJkAZZQbe2Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55373, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbAqivCRA9TVsSAnZWagAAA+4P/3dZKcMlmfO/FrSXnYa+\noH0eMQ0iQoRn6QLmqYSByFFQnu6DEq/ACWZgS+CHHdD2UqjFNcFASoFwlxFw\nsaTKTkbULzjGbVEa7gXKd59Sn53tCCBzbxCBP5eO6wG2fLYljK8S5eh0y3wt\n7/YasIgWUO+Chroqu4+XV6USjo9SxNqgPwBQ8qQf7dX/WxXHBMnRom8ciPTU\n9eT/b0eAgDtCkA72178K9TKnRztaBlBcRO3ycau4SwcFeVfkUp0E9GH75WYA\nD9JlbkKb9LVLerm5hz8Km7S1YcXRgZQUzFsDFTkawr1W3RoZldt/7WXo4DxS\nYWrs5mi0ySeHC8C7cQfkWd8bdWUX6HZn2LiDLcNaeSveHYF6X2Qk1MQpU/cs\nZYtyjDTGThLWU8tk6NV3kpwKyoeaIlfxLVznVyg/KUFOIpA46+hDng1/sOMR\nH8ulxTUe1nRIjlok2qt/YpJ+3O5LT/nt3aXP417Wmwd3J+ocDdX5ux2laELD\n2DeVlyT2lBbt/74gVLAPUPMYsb2Jdu8ukfHolo11taGb+C/xFnsEEWkgJ37D\noSldtUIo7eDLgpcfrxhpixWvYOfe694aFp0YzYCNQxsTrtLit7hdCEDC2zLZ\n7mpy2BRJ1716CkjxYkqKZTzs7WWYxy6ER7pRUx2UIjnbgYPkohYYA81tlbao\nz+wX\r\n=KYGB\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.4": {"name": "@webassemblyjs/wasm-parser", "version": "1.5.4", "dependencies": {"webassemblyjs": "1.5.4", "@webassemblyjs/ast": "1.5.4", "@webassemblyjs/leb128": "1.5.4", "@webassemblyjs/wasm-parser": "1.5.4", "@webassemblyjs/helper-wasm-bytecode": "1.5.4"}, "devDependencies": {"wabt": "^1.0.0", "@webassemblyjs/wast-parser": "1.5.4", "@webassemblyjs/helper-test-framework": "1.5.4"}, "dist": {"shasum": "9abaf1b2c521c0d01051cd0a65a1d74b3d78deaa", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.5.4.tgz", "fileCount": 7, "integrity": "sha512-MmCDoQm6l+48XAXWThTgkH62jIl0ouFQVWsIPZ63C9+2A0OzlTNtVsd3Jjlu2rAndNRP9sbx8QeWTKZn7Eh4Sw==", "signatures": [{"sig": "MEYCIQDt4fCKCJxV3Nbphj8l1E19SwVMLLWhWn8qSbTDzSlQJAIhAJplaFW86wMMYfdj6bvveID/6zsSuUlD2XVJTMMTKK94", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55044, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbAsIrCRA9TVsSAnZWagAACQkP/jdP04Smla7RocVFmUar\n6TZIa6hItN8xJQSdtrczOmmDjGZTYdSHAE3EkjjILFHyw44PK/xGElIKObS2\nWmgJwtDFoMU9keWnGfT+0H05Q4uvdh2wJSKvUzJiNi12zy+psvzZWEG5UYCP\ntIgO/GC3Mjq+6telVNF+vgOKZim8RtZEIYXN8zLrdV9zZc1e4U5EeCq0p+mj\nzdbI4N01SmnkLQc99CnMhjetwuktNNcRquzMI+R3K0wD6e19FHWOTqHtJXJm\nDmhzXeg6livLzBi6I93EwfOBBj2cP+h69TxDIejrBe4wDz6ytdBQkuO5dLSS\nv7T28kgjNMBNajZKSNrc546iX9ijuxyBwAPWfNwJ34kCXEPtiUnBs1N7VDNR\n4vpXTDBM2cgmszduBxFKnLeuPYwmBNREE8RsGDZgd5Ax3rkYKOSfXQR5posu\nuFCl7ZWywh5V2Ka6w4i0sabeSimy2QjiRnIgll5Y+ljuYDX1pefULaLyE8vo\naykg2L3nBfViniDjJKEJ//milbQi3kaZ0SNUwUv6jfYsc0dS0Ycl0mQJIpZX\nRRSZrnaFVZ6diwrUvlFppVtzhkiZufY3JcKLQIDF7PYIeTCg+wNeJaqkir9O\njGPTYI8ug4/TUBtvOLQ2IsVx1SpJuRRE+d8oxzIGCRGm2N1wnQaoefHjjP+N\nE5qj\r\n=80Ib\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.5": {"name": "@webassemblyjs/wasm-parser", "version": "1.5.5", "dependencies": {"webassemblyjs": "1.5.5", "@webassemblyjs/ast": "1.5.5", "@webassemblyjs/leb128": "1.5.5", "@webassemblyjs/wasm-parser": "1.5.5", "@webassemblyjs/helper-wasm-bytecode": "1.5.5"}, "devDependencies": {"wabt": "^1.0.0", "@webassemblyjs/wast-parser": "1.5.5", "@webassemblyjs/helper-test-framework": "1.5.5"}, "dist": {"shasum": "3a0318c0b2fd3ab621798f8a6f4cc767d757a897", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.5.5.tgz", "fileCount": 7, "integrity": "sha512-OaFxhe16WVDOK8jV9VhO3Zj2ulMrPYkKFl4X2gefsNaQkKkLt87YBuJWIPObrxmtsObu8IDA9NavrAnKmq9sjw==", "signatures": [{"sig": "MEUCIQDMv5KkHlf/CHyzaTFsALqn/3OGGmrmNn2ugBProYIIowIgHATDnPzUvqJnRXIOqhI58OLxLY+LYoZZaHV/JiEzw2E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55044, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBnGFCRA9TVsSAnZWagAAKRwP/iw83Q0tgEB57DVCcLeL\ncjHHj3oV6HxUSt0/jVg2olG8Cr3O8Es7aMUQVJcuE94MM+d3gqBjeztNL0+c\nvT8rPrZQgeLYv/4qJIFiH89N5BB9Fk5OBuv0NZj74fpM0AIvPLv6zaTXRu+N\nV+xbSW0+m0FTWg5pXYhE/YllZf6n0Lm+E5F0unYtHae5O4zrVlxCfcPGRVLf\nIiprkVoqZJnIiWHLLkiCaKkTAf8JpdLbUlEkct0/OTDzJRSx/pzswfWcI97S\nwErfGTxXXEX3MoQxa1YVAuoIm5A0JMvzHRoYHm/bGuAyOhOH+Bg9YJpXLZTA\nyyHDOKddhzRb4R5ZHEI3n1OtM6JIWius9SH9PNJREthEYnTpF2YsjOLVYJyl\nhfLPJuPFSPh7T92QE7m2j/PD4H3yD9WxMVfgtiwNx1Psym3c2YlkdWtsRLog\nGRgltikB46WzpZSHc1X4T/uZ9tXs2+qkzlX9BFHHtCJ/B2+W9HVG6XtrFKZ0\njK8fS1e3M65pqCZU0K40xRIr2BF1max+Gpi/AkRSekVR0RD6/PWFgY/1s/Ag\nVm2F7dZUg54G4z3MXoDhhjnj/RVyHpAg8ObfyIARiUHT4eiCUPc3Ojgb7ypA\nfxebI7vELJhgA89zp8rykXQlM6h2YYiNYjWrOCWtkoD2txHFyGFKbWiKPHHJ\npHqb\r\n=50ap\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.6": {"name": "@webassemblyjs/wasm-parser", "version": "1.5.6", "dependencies": {"@webassemblyjs/ast": "1.5.6", "@webassemblyjs/leb128": "1.5.6", "@webassemblyjs/wasm-parser": "1.5.6", "@webassemblyjs/helper-api-error": "1.5.6", "@webassemblyjs/helper-wasm-bytecode": "1.5.6"}, "devDependencies": {"wabt": "^1.0.0", "@webassemblyjs/wasm-gen": "1.5.4", "@webassemblyjs/wast-parser": "1.5.6", "@webassemblyjs/helper-buffer": "1.5.4", "@webassemblyjs/helper-wasm-bytecode": "1.5.6", "@webassemblyjs/helper-test-framework": "1.5.6"}, "dist": {"shasum": "a470c2f8e75bf43e277a8fa347c75eba34969582", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.5.6.tgz", "fileCount": 7, "integrity": "sha512-fRKkNNSLM9z+CQ4Ejrpv0BfpO/2pRqFfdkZvlCoiFbUu2UCXCQm/gc5xTo7FyaM0bssv1/MD+MPL5YYjQNxLIQ==", "signatures": [{"sig": "MEUCICwAK9VXyZJviEnmDMNE7LspqsrHlmRunfU5rFU5lA6ZAiEAhqSfi76GV06gBub7jkcLdUngJxG47GCRr1YpYS3Qd2g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56527, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBsqDCRA9TVsSAnZWagAATUcP/j46InegeJS97EsUv/Pr\nisankHjyL2SybsHl/SeYu8iuqm2qXPMR2alc4HkfNzL8G5p/WOqC2G9VS2j/\nRJLkfvGXSrBndOHtfg1+Hffwn1BtFWHT7gfOx7L1YJ22pvnZ7/sNwFqJ7P99\nf0qAB4PeKax0vQS+7lkR5+0EmaBX3XcWQ+lXeiN0Lk8vuZ014cqWtNdYRsHn\nQvg5S/phuWKGyqEE40FbIZ1Is6OZRYF7guYVaz2U/TU+puK7miL0YbhF4WQ0\nr5UZdzOtQ9Oz5gV1eTIBewJy7/R22kdlYIRGpHHWveg7oV4ObWR4tIWvVc7Z\nzQPBflwL/oVswmbi7s28MZsyB8ecMg/O4aVSRf3RICvzmlBsJdO3ZGn0813H\nm+4+F2NMELe6tktZSvh8sUdvfYdVdik7XNXLGlDtL2RaVcZGoHIwCHMX212U\n87tepxvhLQWMCt8hIwFXqdM/Oi/mXomJCnBRpv2KytrF0dqJwm8QHvTPNXg1\nTKBYqO/nZNVeEVru29qoR3xQlXCopHR7eROAHK4RyrTQ5/+lIznunVQy+fW4\nzqfhEP+bXGzOz+hlf6WHQregxRv/+hkXy+Ew318Tu6wKem8CYwaBjxL4E0s3\npTT145V6sK8qXJhc32B9mxS2mIteXgFCAD3zCV5AekMt1Pp1xtS2zsMJQxra\n1TrB\r\n=2xpJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.7": {"name": "@webassemblyjs/wasm-parser", "version": "1.5.7", "dependencies": {"@webassemblyjs/ast": "1.5.7", "@webassemblyjs/leb128": "1.5.7", "@webassemblyjs/wasm-parser": "1.5.7", "@webassemblyjs/helper-api-error": "1.5.7", "@webassemblyjs/helper-wasm-bytecode": "1.5.7"}, "devDependencies": {"wabt": "^1.0.0", "@webassemblyjs/wasm-gen": "1.5.4", "@webassemblyjs/wast-parser": "1.5.7", "@webassemblyjs/helper-buffer": "1.5.4", "@webassemblyjs/helper-wasm-bytecode": "1.5.7", "@webassemblyjs/helper-test-framework": "1.5.7"}, "dist": {"shasum": "2d3513e3437596ac19584f09abab5a5d482a0fb6", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.5.7.tgz", "fileCount": 7, "integrity": "sha512-4akl0238ELhMNkwuhEbtrHODFDdh7UqvVnDwrAI6spAHzPK5KqvKJiPWusxkfIFyBpZfQEEnSJEnEM/fDggUFQ==", "signatures": [{"sig": "MEUCIQD73MLh/+6WJeVyysCgXTheAOa3QNFevNLc04AGC8ldogIgJKOR6h7u+1Fl0Red0B+2ySg9O4P2iRNxeNeXlCFvcws=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56535, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCAePCRA9TVsSAnZWagAAE6cP/1RnXtBL4Yu1ASjGjE51\n18Pbbmh3Xb5UI5pxLj/+O8Gi3lZLZzpcHgnqiQMHyiC7d4F1+PCch+21eL1J\nu5tI1GT6UjahV2ZUVSv3loi/wcj8CQhs0yNI2yIv39NNBWUlqx9MVW818WGQ\nlVHJIUg6swXiJT6jGC9Sljrn7tAGh+JGlaj9CwlG56vXfhs88ODxEiJxJC9j\nR328Wfp2Jx+9Pyd5BHL7SzZoV2/UVSQUlI4bUNasSTOBFxrBzTeSHe3OXlgv\nJbKQaz3PwroCwIiMl0ni7GrvDQ0vSy69xZSgas9gpPkcKhVMi3wuwBitLstm\n0/4NO8fV9Lut4I3lUmfBhu7TMUgiuI2ySny400zIgpChpLF5HJm86mjQyuqh\nox1ZgKiShl4jbYKgPdnJnmPl87CheC1jNy0EZVVpfqGdxTWvYMzdzAWT2+OF\n1qPTJGnV37M8mAZ/7KqiclCnTEvcwwdVAFvoV/hd7CpaJ4d8zj5mtShre4PB\nbtTkOV60vBZ1wClZR3LBc+PBZ1wUjUbjMKwmJCMZo5NnJyzCu7WUSn7I0tjb\nJG0KvMm5vpK5APfDRrJAEyzWqYJ+cPTMxrHWh2g2tYz7GzKYTFHeW/RnKUy+\nXxqNV1DtaPIlrZjChZ4nvckXCj0/J/Xv0PuAdix53IORa4iRMORd0AkVQJ94\nR5hx\r\n=nJoJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.8": {"name": "@webassemblyjs/wasm-parser", "version": "1.5.8", "dependencies": {"@webassemblyjs/ast": "1.5.8", "@webassemblyjs/leb128": "1.5.8", "@webassemblyjs/wasm-parser": "1.5.8", "@webassemblyjs/helper-api-error": "1.5.8", "@webassemblyjs/helper-wasm-bytecode": "1.5.8"}, "devDependencies": {"wabt": "^1.0.0", "@webassemblyjs/wasm-gen": "1.5.4", "@webassemblyjs/wast-parser": "1.5.8", "@webassemblyjs/helper-buffer": "1.5.4", "@webassemblyjs/helper-wasm-bytecode": "1.5.8", "@webassemblyjs/helper-test-framework": "1.5.8"}, "dist": {"shasum": "a258a7fd15bd57597e4211d9068639807546555b", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.5.8.tgz", "fileCount": 7, "integrity": "sha512-DUJva0Xbhdy/CXJusWCDBWDb0Dm1zesL9Eys5XONqLwaRWE5XdAF6f3ru1q6echT+q9xijk/0PbSFpvjPKKLmw==", "signatures": [{"sig": "MEQCICnnxtwPiPRP24Kf0j/2mxatGOYZlUGmLuNCQZjyv4PpAiAHbLlOjvvB0Pz8Pj9OtmQM3Re10Za+d8IYgAUESmrAUw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57248, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbC/rNCRA9TVsSAnZWagAADRAP/3mvZ9fTeC+gm/kTQtcW\nd2BL0Kj8yW9xHMWAI1zNnQIbZavnBW1KeV6hsKtjoBJ4lcsD2PYhzvIjKqNC\nrxNpjAT4zK73cnKBO/QJP5ctgNJSCwPf/NW+If0fUlq6/m0dh0hrj+papuaq\n11w68mGaz7G6rxrCHajoEcfqd9kMUaTfCoXAoSNGT8j3dW3RTqpYIFup5lj+\neNbG4e2iLEaiYVpukV/NtgNYcR1gpDmz7CA1OKDjuAfgA3dQBMZLzd4Y6AsV\nHkTAdvWH4yDLT1oGceb4jhoZGHRzKXOoR8nmpkDsEYhn1fvGn170inWkEfV1\nFR1JNAY0df1pBS6QfeTbpEaNfPvSJv5NQZfsT0GTjEwcbWIqdwVWWlA6Vt++\nQfsvS0Pfef+FmBnWnnowibnmW6pbb1bMlJh0VRrbNOqU5x+vjvXXNCKg1UjU\nYEGNKrOXwvYFglJG7fGDmESWt6zB/w+3wUsKchKULXcbEXl02ngIk1gbQtbl\nIT8vPfPS06dRy2bdMr49tbCQarDvkUT+vh0c9mi0dg3drFRIlW+rpMqmjy0w\nrVPrPEKlyIBb4QFsWzrt4WGdDZMzi1WspkdpESsQdAeNnRokiNrYXzAQO+gr\n2Wzg9GzusMNAfD3uJIMOL0gb9Tfd9yvF5uYEFZQc/+c41Bz7Wp2Vazxyca5w\n6p6e\r\n=rS+Z\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.9": {"name": "@webassemblyjs/wasm-parser", "version": "1.5.9", "dependencies": {"@webassemblyjs/ast": "1.5.9", "@webassemblyjs/leb128": "1.5.9", "@webassemblyjs/ieee754": "1.5.9", "@webassemblyjs/wasm-parser": "1.5.9", "@webassemblyjs/helper-api-error": "1.5.9", "@webassemblyjs/helper-wasm-bytecode": "1.5.9"}, "devDependencies": {"wabt": "^1.0.0", "@webassemblyjs/wasm-gen": "1.5.4", "@webassemblyjs/wast-parser": "1.5.9", "@webassemblyjs/helper-buffer": "1.5.4", "@webassemblyjs/helper-wasm-bytecode": "1.5.9", "@webassemblyjs/helper-test-framework": "1.5.9"}, "dist": {"shasum": "ddab84da4957b64aafbc61e4ab706cc667082f32", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.5.9.tgz", "fileCount": 6, "integrity": "sha512-jBKBTKE4M/WYCSqLjRvK+/QD55E/HNcQjswbksof3GEXfkq0iMqYxoPfqR7uLAD9/jVf9HpBNW2FJOyfTTlYfw==", "signatures": [{"sig": "MEYCIQDvIdVl5arVWMkaTsYJE5Zae/NhcOewYw+SpP0DYxWniAIhAKS7PJrgQohX1+nXrnanvHzM97LimVhwM6jvnm+meA8f", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57288, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbDVIHCRA9TVsSAnZWagAAbi8P/iCY/deFMp52+ErE4skB\ndNetdAYCxOdcvKYfJWXqxdbExhYI1URrIYzboqL1tQZH7xbStjtw3u6mlGDr\n9acI8l11QpecLehFyhJC87txy3nt1WPvZx0Yq0wo1a2cRtVUuDFZ+ygYFWu1\nj1Q6Pl8tg8NY/vdrQGfhxLDGngqF9u6+wX/e/6qdx219G9n7z0LLSaycq2oU\nRY5ujVaPL2mhWpw0dzLIXlsEXA9kwV+qCRWU7yt9jW6ZcFqfKwidY2FknFMq\nFnxb4HKhHidjq8yJIXXlmBd+vLx7Wn7oPEWLB25KxJRFzf/jvbXyVDCTBx+0\ndlzLgKHUkhXNdmQtPwBd/WSDMWK1otkjjNcMOCHe865Mjo6NJ8Mfm0afTEna\nr/BR/mf3cXCNs/dvOkwoZ1c5VrdrZD8doHpLZmy3pDTOkVDfJK3BC2Fagi3z\nMIbaGl64vy0VGsuVGFnD+mU0X+UherMPqtL3yod+dzUPRrll9ldb0IQqoXm0\nWkuUiXSs6vRVyTH6Fmfqlncw9dwqiPFYZlueVxdkd0RwNNQ1VFn6Dl6SHRh8\nqs89cEx+gUhV3llmJuIWrn8mRFeGwbYWUjhzlxoMV3Wqm477O1KJwflJS0Qx\nwIfdX1+2XIq1ecxoFVIhHyvzRBr+IOseA4fht1GgY5siQU746UCvOgfqSk0J\nHeOq\r\n=hFq3\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.10": {"name": "@webassemblyjs/wasm-parser", "version": "1.5.10", "dependencies": {"@webassemblyjs/ast": "1.5.10", "@webassemblyjs/leb128": "1.5.10", "@webassemblyjs/ieee754": "1.5.10", "@webassemblyjs/wasm-parser": "1.5.10", "@webassemblyjs/helper-api-error": "1.5.10", "@webassemblyjs/helper-wasm-bytecode": "1.5.10"}, "devDependencies": {"wabt": "^1.0.0", "@webassemblyjs/utf8": "1.5.10", "@webassemblyjs/wasm-gen": "1.5.10", "@webassemblyjs/wast-parser": "1.5.10", "@webassemblyjs/helper-buffer": "1.5.10", "@webassemblyjs/helper-wasm-bytecode": "1.5.10", "@webassemblyjs/helper-test-framework": "1.5.10"}, "dist": {"shasum": "3e1017e49f833f46b840db7cf9d194d4f00037ff", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.5.10.tgz", "fileCount": 5, "integrity": "sha512-VWSi7NWmfEuMpZ0+CTnBzz8qhxw7R17CwmbcJ+QJ0wfqReWEgP/J5yI4mN/C/lEoYuroFUF+sTWoDQqzH4FNdQ==", "signatures": [{"sig": "MEYCIQDoZxI0tFx1ZjLWwco7ceMvSGVXJ7XbiQORWPEEGg2JDAIhAMaiy1id99ehjn1Bpp36ZJFvjtG2u8smAdNSxIhW/j4U", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55977, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbEUhUCRA9TVsSAnZWagAAXd8P/2PRQeUReP9YTu/KNgKW\naIm2k90RGsDEoGs9BzafjdhQgbHPIvlKFGuFOTngm6OivY4v1KOsUXsP6k4v\nw6z+01wjPdxu9MDucImShp4q8mn7TuhKiuuLmoq13pE/UpHGoOGBtuopg4j/\n6PK5ogdF18Bbckh6FqWpnLENEUTXh60bTceUd944xm6JqZf93IsadEA/im2/\n72xDAo0iKemkAtkdq5KFKGjeOHz0h96h6I90LyHqYZYQMuD+yOuIjGEmsflJ\nGPtq7lVFSExUGXnx5lkNEQKp2MZh3UOxZ9BDiAzWQXjt7WSv6gUs5FaG+3Cf\nKUZ8Bs13XlxCrO5Qr7Wb5ikwgJf8WODx5G+O7WMQaGe2EuxZcrKsc8T2vPpo\nibAPja5xOxBELn/f164MG3AuGgCmFChYQyFLERFmSFWUzKiBRsnpbi5l7uBU\nffGUboEt+js220LnmiyWQyfciiaPJ57yreMT2BxE3HBxzfXOteXNRfxOrWgS\nNNcR3+PAZ2wER401ma+de1xEAAh5DyN1gmbtgRw7ETeryQCZBv71mErJGU9m\nM8ILYrDVlKiNjjFEc4FN/R2xTSWcQwnweZWHhZhSGdOIIpO1xjF7XMcfN+YQ\nyKvTdHFWYYBHBg3+qE6hOxckSKo3nVYHmR7qGXE4xQIWweBtOPOA1ArFFnMa\ncJ7B\r\n=cUhl\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.11": {"name": "@webassemblyjs/wasm-parser", "version": "1.5.11", "dependencies": {"@webassemblyjs/ast": "1.5.11", "@webassemblyjs/leb128": "1.5.11", "@webassemblyjs/ieee754": "1.5.11", "@webassemblyjs/wasm-parser": "1.5.11", "@webassemblyjs/helper-api-error": "1.5.11", "@webassemblyjs/helper-wasm-bytecode": "1.5.11"}, "devDependencies": {"wabt": "^1.0.0", "@webassemblyjs/utf8": "1.5.11", "@webassemblyjs/wasm-gen": "1.5.11", "@webassemblyjs/wast-parser": "1.5.11", "@webassemblyjs/helper-buffer": "1.5.11", "@webassemblyjs/helper-wasm-bytecode": "1.5.11", "@webassemblyjs/helper-test-framework": "1.5.11"}, "dist": {"shasum": "63736d9426615a429c160b925e6066736d47348e", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.5.11.tgz", "fileCount": 5, "integrity": "sha512-8wsWSMIciOZzuOedt00rRaMKoM3jHS/uyRSgg+UqLw4fGSLjVnOXb0H2ksLNYSGhV/+mqOipHQugo25Q3aFPzQ==", "signatures": [{"sig": "MEUCIQC2dXegPJr8Ph/8DXvd1FqGeikF81BjYFMVO/UJt8HvgQIgKKz/tRtQCMHKx1dxf2ENdAI6R+93iBOPHqQ3ekpwoDE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56107, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbF6JDCRA9TVsSAnZWagAA9EoP/AvFoPyMvlefneVD9ZrE\n36CV424Wmxnx7kAOpj+HHs6EZ/miZ6W2XNcAcqIKvf+TYkQp071T2RCpAjkl\n4iqbXnkXk27vKMw5CKtjXjqBD2q1Y7f3WwmDc1PJGrqVGbjsJU60qEn7xwbm\nm8zDxRpeS7VnHXMyzM0UVlAfEJxXJ+DL8ell1IFvkOyrUbjVDh+Swhsiw9Gh\nQSxc3UGHR9wZUkOCX2dlP1qWFzSNbJ4d/YP9rMEBUpB1vUH82utJPLYmep7V\n3V0rNy9panFwSoYxuBJyoCLRGnPdrA6fEgPcsgcvLZ5txPeFHT2/Yy+rZfpg\nRDmuPxGN+zqMu6jeKD22Yff8I4yik8Q75Djc4rwIhyPnFOEKBKD1c20HpCox\n1MGBI9mfYcie3n1fPft54o1geM7TPhPeMlHfU/nlZIHCtNvynTd4deOD3aSa\nHhz/ayuH/NNJDiUzw1SIN8+Ya2DDBoja4sVLyM/LYl8O4knsHs6QLY7zq8xA\nbut1CEX8JV9I0E1apRgre3s7TFocTxK+rh4gRQO5IPQLxnuAA/BsTCxJxjMB\n+s0xTKPDYOleflUGIZZN4eviO3yCyH17htqHRBr0Da/6Ld9D/Ia0kQjpfzhk\nDl8lAM+qYrINMLYx7PK7HEG8R78fmhkzwkwWDWJIaegELnJhfcWWN6p3IFWW\nzvVn\r\n=u98a\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.12": {"name": "@webassemblyjs/wasm-parser", "version": "1.5.12", "dependencies": {"@webassemblyjs/ast": "1.5.12", "@webassemblyjs/utf8": "1.5.12", "@webassemblyjs/leb128": "1.5.12", "@webassemblyjs/ieee754": "1.5.12", "@webassemblyjs/helper-api-error": "1.5.12", "@webassemblyjs/helper-wasm-bytecode": "1.5.12"}, "devDependencies": {"wabt": "^1.0.0", "@webassemblyjs/wasm-gen": "1.5.12", "@webassemblyjs/wast-parser": "1.5.12", "@webassemblyjs/helper-buffer": "1.5.12", "@webassemblyjs/helper-wasm-bytecode": "1.5.12", "@webassemblyjs/helper-test-framework": "1.5.12"}, "dist": {"shasum": "7b10b4388ecf98bd7a22e702aa62ec2f46d0c75e", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.5.12.tgz", "fileCount": 5, "integrity": "sha512-xset3+1AtoFYEfMg30nzCGBnhKmTBzbIKvMyLhqJT06TvYV+kA884AOUpUvhSmP6XPF3G+HVZPm/PbCGxH4/VQ==", "signatures": [{"sig": "MEUCIADOsGQzxZFKesibGSJibQqPFLfnJ1LO5vVkEUSrllmdAiEAzJvlOd7WBQpjbSNLd6qrjCHUMIBIKR3ii3eaaOlk3Cw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56063, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbGPoUCRA9TVsSAnZWagAAl/EP/289R1DjFoKFfBju1dQ/\n/aXOHRwc3pc2P+Z7kWT1z/4V2Ul6zYmS5YkyiorQScqZLANtGqp9tQvI2T8N\nWF7Rtl5BxtCk2G3Df5J3NsquldYFUZWMwhluVrrPRt/wh/TGkN1ew69oN1gK\nluadcCPPKCLm2UTJlPHVRRuzAFY1OSH6ZeORoTlqHXx6Ia+wPqs0ncBjhfnC\nG6G20aBLr4sillOfbh4dEia+2aywWU+gaHP8n8AJ6re+o8aN0vamtBAGSQ6W\n7OwEf/aav8bKl44kg3CR9idKkCGMV4ef/vtpXIyb1WjZIqASPq9dVCnIkTuv\nr+02fpGSIQWwoVU9pj310NoO0epXWpReeVatnd/Xy2ELLyeLOes/wL9UhwwF\nHv/2zffjRWqM/Zwc7z24CLuw9y6oKNp0JhTnyL59qXpMv9wnOVr5g8eJhLU+\nTbbJRkfwrUCQNXVTi2Nx7pVWphnRdJQlgt7kjIWfxATk/km/K0UqCda0NG0e\nkKuuE1Q+z5+yJGfGPaBdH9+f4c9Lr1LC/lwf3fpn7JzR8BXJ1ojuAfMO+u+f\npmOlqKm9CDUuNLeqt1ekRV9a2TlqL2dBh+ZNVsb9M0wejORswLUHAtfaIU6g\nnftrhnI4z0SG56M+79r50gzn6ZvE2F7kt5FjoYYiNAYox7VcJLR7ur3CDkxh\nG6D6\r\n=Kp+j\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.13": {"name": "@webassemblyjs/wasm-parser", "version": "1.5.13", "dependencies": {"@webassemblyjs/ast": "1.5.13", "@webassemblyjs/utf8": "1.5.13", "@webassemblyjs/leb128": "1.5.13", "@webassemblyjs/ieee754": "1.5.13", "@webassemblyjs/helper-api-error": "1.5.13", "@webassemblyjs/helper-wasm-bytecode": "1.5.13"}, "devDependencies": {"wabt": "^1.0.0", "@webassemblyjs/wasm-gen": "1.5.13", "@webassemblyjs/wast-parser": "1.5.13", "@webassemblyjs/helper-buffer": "1.5.13", "@webassemblyjs/helper-wasm-bytecode": "1.5.13", "@webassemblyjs/helper-test-framework": "1.5.13"}, "dist": {"shasum": "6f46516c5bb23904fbdf58009233c2dd8a54c72f", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.5.13.tgz", "fileCount": 5, "integrity": "sha512-XnYoIcu2iqq8/LrtmdnN3T+bRjqYFjRHqWbqK3osD/0r/Fcv4d9ecRzjVtC29ENEuNTK4mQ9yyxCBCbK8S/cpg==", "signatures": [{"sig": "MEUCIDPwdbWpY+qGwe4fDbL3wyua+SjAm+2VjWI/sfbz9hBcAiEAgkwLARf2zxsFvTMaTNOiN1rHfmX7wF4oVQOj3PDM+XA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56234, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbN4lTCRA9TVsSAnZWagAA8c0P/ih4pCul5jSp3YHtesYI\nHZWr93pT3Q+IEJhe9Xcjvyu0YlcLit9TRpfMOJk+WcuK3veGNmApVmfVlID9\nLa7vdBgiRZfhm06uw+pSYyyz/WoBSZHEA5qMUXS4NZf6v7e/jBkCiTguhmsa\nieMJtqKdFvElD5CKuspmiOguSG27FOngJNukpK95OUvVE91mPCsfYWVulMLj\n+LSB6ZTY/WnP9uOxarg8PUfN3zE0BCbtN5qb/YGhT6zwrmZ8EeX31B51I6L1\nGtIkLmcvaBC+bmUYVJlmbHiTVmnJWGHd0EyXqykW1fLE7c1/ttTPUq2DSggs\ndGwJn+xrlseJZtJpU4Yco02840+SSI5d5OwaXiauksFgrASTtNiPtakLBlzU\nuKY/YsYlYT+q/SB0xVQ2t1NziqXilzqwrAQIA37FU4EUEEMqC8gL80mZQjwE\n7+O7NBUeIrmN8TMMNbjcy7ASXZNnE3Q5xiOa0De7Qtx8Jd4RVnDPiJLk/WJc\nJADcJYxH1n2iSAItfDen26XiFuKgA8lP9L8fDOUZL7HfVBTHL+xW4WtHULb6\nWBlbJU8uqLaOKfu7zYhszPRjQDa1I7NSybJ/D80ZvPU6SiwHiITeHnJ97qon\nWX+jUSpzNB3Mwu52zwY8yGfTqB2oczIYQpt/6Na3F0TEr9nv5Tt+O9Gv+4zE\n4fJ4\r\n=QEN7\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.6.0": {"name": "@webassemblyjs/wasm-parser", "version": "1.6.0", "dependencies": {"@webassemblyjs/ast": "1.6.0", "@webassemblyjs/utf8": "1.6.0", "@webassemblyjs/leb128": "1.6.0", "@webassemblyjs/ieee754": "1.6.0", "@webassemblyjs/helper-api-error": "1.6.0", "@webassemblyjs/helper-wasm-bytecode": "1.6.0"}, "devDependencies": {"wabt": "^1.0.0", "@webassemblyjs/wasm-gen": "1.6.0", "@webassemblyjs/wast-parser": "1.6.0", "@webassemblyjs/helper-buffer": "1.6.0", "@webassemblyjs/helper-wasm-bytecode": "1.6.0", "@webassemblyjs/helper-test-framework": "1.6.0"}, "dist": {"shasum": "1db1d6fb7f1057ab1b9dbebabf34ec33d5dd24c0", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.6.0.tgz", "fileCount": 5, "integrity": "sha512-lVevOmYhZ15SbhDWcdjQ3Ev1c6yMtO+TpgbH6sG7T+7svnXeKKq6MD60I7xCeNKSR/zyPTHcFEu8mxje6X30ZQ==", "signatures": [{"sig": "MEQCIFuWmtiN6OwjJbSjsMIZKuuvxPVVI9tta8Nzbv8GKsi5AiBr+NcRNk73WA8ogt0yN8g/5i55gUTMh4vVG39MMXOqXg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56222, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbTF/dCRA9TVsSAnZWagAA4CcP/0bH1GVvb8gE1nsBt8iS\nMi1/bybUUJmfGQtMemxmYzQdxpobUl2ZHT2NwR9n8emEABWBPaYn8+mAhBqw\nFNw7HBBm64KPlqvd0uFMYnatdLLURZeBvV1RrqRGZlh09In9m9G+HS+jSG4K\nIuvDoNVCZ4BJ1sZdqd6WRzOHDhrALa8JhW9cKuOcnEMRPo07wpTSf2p8CpAq\nh3vCD56mKyF9e1iRNZ5PLsmm14CocSislPygvrNM6/QlyKFHFS7hEH1AMn9S\njweEm5nPS14uQxQlZ6LnLU9Dw8yq60aZtllkWl7wGEYnB3UAKn2DsWQSTsSR\nVQpKDlBA5TVLC0SXhCYqGquJu0V8fi66Kc7Yf58LRKXjciRH/OyA03H6Co+u\nDvBSxqvIQMuDwSVw/ssTSAdtksmy27g1p4Sfxqhxbjs/rEUYrGYhqh9uwgX+\nLSLSwLjQHgcwHBTBrkV5QdblGF6qylv3TS7AtYOlb1FhqJSu4UIjX+yEqNHr\naJx8aO6On0qYxvD3f6kQPddjxLkBfJhxNQjjK1qIXDD0hDYIYKP9FSWcVWqr\nNK3ohmXOlCKEPWiKoqC4COkKGiccbtkweYPx1F8NPqrsUROKKROq9q5t2IgP\nsLxaLTRM0w0dcSBQSObkDkMeVTM5IVb8ayZREWp9ScljeUD8CvSh3B//PxP3\nO07B\r\n=PIy7\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.0-0": {"name": "@webassemblyjs/wasm-parser", "version": "1.7.0-0", "dependencies": {"@webassemblyjs/ast": "1.7.0-0", "@webassemblyjs/utf8": "1.7.0-0", "@webassemblyjs/leb128": "1.7.0-0", "@webassemblyjs/ieee754": "1.7.0-0", "@webassemblyjs/helper-api-error": "1.7.0-0", "@webassemblyjs/helper-wasm-bytecode": "1.7.0-0"}, "devDependencies": {"wabt": "^1.0.0", "@webassemblyjs/wasm-gen": "1.7.0-0", "@webassemblyjs/wast-parser": "1.7.0-0", "@webassemblyjs/helper-buffer": "1.7.0-0", "@webassemblyjs/helper-wasm-bytecode": "1.7.0-0", "@webassemblyjs/helper-test-framework": "1.7.0-0"}, "dist": {"shasum": "fc206c83aa9b281434262abf3bc9d3ae40c91bfb", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.7.0-0.tgz", "fileCount": 8, "integrity": "sha512-91JWOVUu1mldGlmC1eVkbRBBYp9Ek5uVAURHGl87tM7BAXXUiqT7e2BDr6IDUpWuR4qbpAn8DUo7xhoJ6P1QKg==", "signatures": [{"sig": "MEUCIFvnxUxU8t8cIqazA5LuKADj5j99q96fUKKxSj7GlFu+AiEA+CSGAAm4d3skO4pcb+cp2vQk7r7LMbz6yl1ED8hN4As=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 111195, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbTzlwCRA9TVsSAnZWagAAAdQP/RqVzTJ8i26QMG9/J7CX\nU39qlKqdJn/YpeKPr6PnothBUutdlzUTYY5gjYIgMXeOo4AdQtmoA3REQwjB\nUWN6EiJ3HKLyVjsCix2LV/eiiMIuG6ic++zCvMstcT7C5FxqhvWWBa2lw7Ja\n8DtGBygcJedvzyEdVpHDO2d57zLL/b62tiaks3qjQU/ExGwbxwrmZK9sig5c\ngXx+cDs+6FS7nOvG9Wrnz5fzozyKVBee0d3E/nJ9Rj5Eb/usOO0yc87GpdfH\nhSnVvtqQ9WIk9yr4XbYVUI3CdCkEUmqilSgb+3dJMiRyNQHuVSeRZtP48xO2\nt2Pa+s6uQ/QvmO9h0S1c1Ux2a4mXgdr87Kh2x8P8O/nb223igJxyBxFv54tW\nnnHfgtQS1YYf8taiH7m5esSp6nr7UaQS6woj1vbksbV1c+w0ixYiBUVlqjdM\nKOZhStl86seJ6F4LTs04J/BAPfHf614URlSd/uw+ybThGjSNBzlYpGCIsKAV\n8pHcp8WMq82is/IjAPOEdwJX5dzj3BQDTWPsrcP9a4Ey2s2V/XSIQKtNClYN\n9VO8SIpZB15QIZoC/JBxl3/8qeSHre9lrZUpZQ3f9eKhm88+Ia+ZD8H5b/Oj\nGcxEd8qqLT5vBax3BzEdxInHakYQCWXeK7Ti9YXx29SLQRi7CYCviP+m84RK\nvc5o\r\n=MBEg\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.1-0": {"name": "@webassemblyjs/wasm-parser", "version": "1.7.1-0", "dependencies": {"@webassemblyjs/ast": "1.7.1-0", "@webassemblyjs/utf8": "1.7.1-0", "@webassemblyjs/leb128": "1.7.1-0", "@webassemblyjs/ieee754": "1.7.1-0", "@webassemblyjs/helper-api-error": "1.7.1-0", "@webassemblyjs/helper-wasm-bytecode": "1.7.1-0"}, "devDependencies": {"wabt": "^1.0.0", "@webassemblyjs/wasm-gen": "1.7.1-0", "@webassemblyjs/wast-parser": "1.7.1-0", "@webassemblyjs/helper-buffer": "1.7.1-0", "@webassemblyjs/helper-wasm-bytecode": "1.7.1-0", "@webassemblyjs/helper-test-framework": "1.7.1-0"}, "dist": {"shasum": "af18b197d711a453cfdb00ad4840f3ee38aff130", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.7.1-0.tgz", "fileCount": 8, "integrity": "sha512-fXMdCw5kj/p5R/7PXn1vrPy9OWwM4ysPgFH0AoXse6g0DX7IV+Czzj4qMSs6Aa2Dn+h4eVb3/QPxnwzOsQlXIw==", "signatures": [{"sig": "MEYCIQDA/puSS4GExnd9EdMYvOHw059AdW49odtxACoIoXrS4gIhAMurtieMF2NByco8YMFq6cvwM3CcS3bEM57zP0QoRFul", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 111195, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbTzwVCRA9TVsSAnZWagAAqesQAJ1hvno3rmP3mS2cDNqb\n4BhAmD88D19896qFLRPolQsjZb+L7oI6Mb0gF3m1+CPxIiyNw7X6CGnaF2bl\npKkLtd50DKTbPYYVXcgW9vSotd8oZ+wofCDRnn66KyiASRTIgpu7+3v9bdO2\nUq849lsZsZzWmt0NAKCiatjRb9h3MSAxk1LUZfN/K79+vJhmQFNYt0c7Kk6X\nbOWAX9lj1h4YNQ6lmMiJIh9YGOqsKDPdS0z8TUZOhsNBzALSNS7iwZ7Y8ljb\nMr0WPCZ95i7LXJDXGYCiZdFWGqSVt3lk9TuQh2mKdKlEwr4ue++ENcwrT+JO\nBk5rYscvneKIcitCRWwV6L32aBaOZJGPglkPpJ81s/yS8OUNDct6/7jsuW9M\ntClORjBYfxf9Cl4Y6ltPzS7RtlOSFtofHrW/asQ0gpG6SvbH/4KObC5UGmna\n96wGe79oOk7D8c+oIHE9oQbeq1o5q7eL9aL/WFkVfrRJlL2oMJpXtUHO5Qzw\ncICdpz+6CoedMlnn5oE/FXubWW8Cd/k0uAc6T0dm4ZaevGwlVE/VApPoVYOp\nyuMwGqc9oZUsGuYx8Of82DiUgK/gFNLG4Mv4C1sl+RGmnWywNVJ6yqdzEGj6\nGuD3yMRGFm1NUdkcqJjsFuiob8TdZqpMPDZmT8ttqLKq3iiDuFFnwDn96iFb\ntvV3\r\n=pHSC\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.6.1": {"name": "@webassemblyjs/wasm-parser", "version": "1.6.1", "dependencies": {"@webassemblyjs/ast": "1.6.1", "@webassemblyjs/utf8": "1.6.1", "@webassemblyjs/leb128": "1.6.1", "@webassemblyjs/ieee754": "1.6.1", "@webassemblyjs/helper-api-error": "1.6.1", "@webassemblyjs/helper-wasm-bytecode": "1.6.1"}, "devDependencies": {"wabt": "^1.0.0", "@webassemblyjs/wasm-gen": "1.6.1", "@webassemblyjs/wast-parser": "1.6.1", "@webassemblyjs/helper-buffer": "1.6.1", "@webassemblyjs/helper-wasm-bytecode": "1.6.1", "@webassemblyjs/helper-test-framework": "1.6.1"}, "dist": {"shasum": "57523a41951e6e891d3d0ba351c9b5f76c2ed31f", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.6.1.tgz", "fileCount": 5, "integrity": "sha512-j8VuqobyK4rXKygCpSuYHgBXthDCBvtHig3geqDBjWSy4Fo0DNZXeoRsQAJXkAPHVb4IdcXXk7k+NKHQ82gF7A==", "signatures": [{"sig": "MEUCIDGWPOQP9pobAQF3umkReCLfSTiTJkl0SI+VqosLIYhvAiEAjClaDpVbNXvuyN/P7WVONhF7G6LGiR0Q4fZcmCn6Wig=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57139, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbT0XBCRA9TVsSAnZWagAA1roQAJNUbnrYdeHlhOlTesBX\n8OcJaxKf1uGvhcwWP132T6g45Tf1WKvkttgfUAqJZndjWJs5rvnmD/0azhJ9\nstFE8Uql3ytidwXw2wHvXuQckbyvAmpXfCLFpDH5SiIpq4sUKeTDsvAJX0oL\nQaDp8J3r/00cGCmAK0m2pxw1SzNtUm22Y3FBwlveQ29HqxNTjUu4qWFedUzi\nGGbSVJojcJwEknNgLdgScZjGEl6CLLlC0o044pE6BOMZqoJFZgQXi0BeasQl\n+TT8HmC91SZtm+KKR85ky09jzYuZqHa5Xm+deTGgGKLtO2e8YLXwHuqKy9IL\nw4wXECuWkuYl+kBndo8u1sgy/VVyiihMJOa48Ha7L27N5jFgOyGCcOXm5B+5\nLCo1xj0SHanANUYShES97i/TcF5bdGP+qcl1EZOsOnZJFEaMsxCI2ZRKH9Be\nBSzgn3f31NHiEp3r6wysvq4XKOw7YyqKnZhDAZ+DZfbfsYRKxae3cuzF8Cao\nAVaYgovuukArg2tcBCX0KA1nMYIt5yRGd1+PqOhoDlLbenzqZ6aC8COb0R6o\nseLg4fFXggHMvqUxn3YSAsE9wFac84aqLqGrO56x6loBA4v1OVHt6AJfZ3BU\nJsYsPXuEyJg8ozorIMGO6XypvJNooomVFP5goXnCi+tFdYGN/3l+X0eZe3BO\nf27t\r\n=gzZC\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.0-1": {"name": "@webassemblyjs/wasm-parser", "version": "1.7.0-1", "dependencies": {"@webassemblyjs/ast": "1.7.0-1", "@webassemblyjs/utf8": "1.7.0-1", "@webassemblyjs/leb128": "1.7.0-1", "@webassemblyjs/ieee754": "1.7.0-1", "@webassemblyjs/helper-api-error": "1.7.0-1", "@webassemblyjs/helper-wasm-bytecode": "1.7.0-1"}, "devDependencies": {"wabt": "^1.0.0", "@webassemblyjs/wasm-gen": "1.7.0-1", "@webassemblyjs/wast-parser": "1.7.0-1", "@webassemblyjs/helper-buffer": "1.7.0-1", "@webassemblyjs/helper-wasm-bytecode": "1.7.0-1", "@webassemblyjs/helper-test-framework": "1.7.0-1"}, "dist": {"shasum": "8a4b2099826fcfcf7bfead45a5a2c7af22ee8a02", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.7.0-1.tgz", "fileCount": 8, "integrity": "sha512-z4gS+23AEmi9TMF863tJRK5PboWypJgGHDQ1EKfjTwaTCwVN5930+ImfJsbZO/ZJ3hm9E5D3ieSHuoIlcaJVQA==", "signatures": [{"sig": "MEYCIQDq6+9TnEUmm8BHBI58q47+kF83r9w7rHja1sO4xFpj/AIhAJnffvSisPkklpMiQYZdOlsCqamegyNTT6IDQifEx3e8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 111195, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbT1YJCRA9TVsSAnZWagAAp9sP/j+G/5HonYMfMp9PLD6j\nvyDq96s/cX4wKNhxPxZR1SahOfYk8MarT2tT0jL8qtIkzarYKcOBzfWOAYNi\nZheT0AyKt08VAmCZEGQyGkAd2CJBQXDZjR64ecjmkTCsGJ2LSt10mGt/tTvP\n9VP1RyBat9yyFB1j8qvxHxxP9ca8nwHsP8NKAVHLNDUNkOcvEmahelwJwm1i\nG5xxGnjJbOBPj1gEOQwqf/kIW10wDaoe6Xmv1pzJpnpm4ajvr4+0l6t6WbmQ\nV5E9nvNZ3qfeyaKogRcGj9zxhXFrXbUv6DYHmL8kqZg1iPWEwCSwbZo/pbn6\ncWRNazYgWiP26XeoGKyMCLte/L5UpSG3UlFvdUCk5WPG26CqlCm5kMa1wtgv\nJ+/wg7u1ibTkGPGQ3O9KQru2VpPm8VF+WcIZPvmvv4OVPngzLsDUY3mfHNqK\nBB7QnjTrtIvZ3PqKzRaxLUpC6OJ8gPAJYrJlIrB3ONOTJnNeYziSp2/0Shn2\nBUiokGx5GkvQyUEHubMRS2QN78eks9nAxYvSCosbgEL7ZE4pNWX4v6GgYJ6R\nZ0V9rEamIW4fV5cy9UabDMx53XrwjEVQlOhgwD4u9OQ0R5+Y955qTv/misWA\nIg8RDIN0knwUzWWdarzUz6vsk7J77OiGTBbuLuO+VZMptGW8/z9c6UAxKpZw\nyaI5\r\n=afk5\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.0-2": {"name": "@webassemblyjs/wasm-parser", "version": "1.7.0-2", "dependencies": {"@webassemblyjs/ast": "1.7.0-2", "@webassemblyjs/utf8": "1.7.0-2", "@webassemblyjs/leb128": "1.7.0-2", "@webassemblyjs/ieee754": "1.7.0-2", "@webassemblyjs/helper-api-error": "1.7.0-2", "@webassemblyjs/helper-wasm-bytecode": "1.7.0-2"}, "devDependencies": {"wabt": "^1.0.0", "@webassemblyjs/wasm-gen": "1.7.0-2", "@webassemblyjs/wast-parser": "1.7.0-2", "@webassemblyjs/helper-buffer": "1.7.0-2", "@webassemblyjs/helper-wasm-bytecode": "1.7.0-2", "@webassemblyjs/helper-test-framework": "1.7.0-2"}, "dist": {"shasum": "d3457ddb2887addc1abc12511a37ccec34ebf699", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.7.0-2.tgz", "fileCount": 8, "integrity": "sha512-CWSWBYpnmexvVcJpQYYUDUl4z/X6rJoNcWSh87WgpwvV0kSrMNQ8MkHgvA1uDNnwPyVKEJUqLtN1Mt50ZyGCrA==", "signatures": [{"sig": "MEYCIQCgh7QUDtSAgwP3Q9kJSgbq/w9ZuKg9k1JXz9w5lBZY+wIhAIxFEn1csIaDkEp60S9tPY+p9WiQY95poVb4ff33CtJb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 111195, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbT1mtCRA9TVsSAnZWagAAHyMP/j4WK2N28eYuBUx/tprg\nJ18A9ewMNuWSx5N9CtHDAskJulSvgY1GU5cUoohDmgH4kUb0nBEpzaYCecsU\n5w3xDSBjzRAIYO68MkiWHdg9P09DF5e+MNkUpUZkharBmm4rcpLuOiDl4RoT\nOD8SLEUB7QPEFVCSryKiF1//5VcwzdNjsjg3rufJ6D98ESQE+ID4w1jQDMUw\n49NC45YNpCVH8+OU0DQgUy0Iz9F6g71ur8szvrZiMHgJwf2dQRNz6LJG3BZU\nKLBXtT/P2ju+lYQpCH53KROuOGW297zuLuNDELDSpZVlwGl0bOCnk3n7PADv\nrezK3GCWV5z55Fk6HGmdrI7GE9Xm0mFWnApkZUqx0oKyeRj0cDfbGUBBJKqe\nsxRuSK9aM9kxkYQ2Vab+dfD1fRAZ8v7trrFeMD9EKiT4/9oCkRILRQU8dKw6\n95t8MWNBA8EJk1HnXkzvUDYefTUYL1QlRnTF7JscJS5HgSXDuErtFjhGqp5e\nVFM2whvfQvqYfgu25sRVQwJCVBoQZrt5ySZc+X5W8IZTCU9JGAucHYT+6g6/\nDhkMOzIybEA7w9G94i0riauUg9naEbBR8NvMLqEk8MwHWxl4E/2uliqE9Mkd\nhqB7cGDb85Cer0RwRQFgGzIsSeiwNpdE0lDQWfz56Lc4nG+Bz73MmgV+WUvd\nzAou\r\n=RaWJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.0-3": {"name": "@webassemblyjs/wasm-parser", "version": "1.7.0-3", "dependencies": {"@webassemblyjs/ast": "1.7.0-3", "@webassemblyjs/utf8": "1.7.0-3", "@webassemblyjs/leb128": "1.7.0-3", "@webassemblyjs/ieee754": "1.7.0-3", "@webassemblyjs/helper-api-error": "1.7.0-3", "@webassemblyjs/helper-wasm-bytecode": "1.7.0-3"}, "devDependencies": {"wabt": "^1.0.0", "@webassemblyjs/wasm-gen": "1.7.0-3", "@webassemblyjs/wast-parser": "1.7.0-3", "@webassemblyjs/helper-buffer": "1.7.0-3", "@webassemblyjs/helper-wasm-bytecode": "1.7.0-3", "@webassemblyjs/helper-test-framework": "1.7.0-3"}, "dist": {"shasum": "1f50b53228c651a810f7bc73808fa51d951f22a2", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.7.0-3.tgz", "fileCount": 8, "integrity": "sha512-zKHcjuvPcogIVVpk25iK4cVxr17TsOuh4+nanEd9MaFNU3Fxr1HpgmnXSDj/5JxE1N0eg/IMPzsLGH9izugx3A==", "signatures": [{"sig": "MEQCIFnA6pi9Wij9F92am4m37ZdQieZUOrkmkPLrXWkNJivKAiA8mh+VTlb5L6I7/h22KWpwjLQkcpKlXEFUoS+ezXIkjA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 111403, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbT5FoCRA9TVsSAnZWagAArFMP/jywdEh14D1/IRq2UlDo\nBTKn1B0enoddlRiMK7eZqLqSH2r3i0Ul76EX+qFh9uyjJnalXLSfC/9fWXj1\nrQevNYZabE7SdXVn+4syxDW4dyStuXuQP7qrR2hA8e/Dt1is6IxlCRNMzX2C\nRcJ35gkpZ9psBLKlDRdBfSFIda8C+l8LIKCJWUlbRKAVV9FFi97FAeIA8g6d\n0VaRAu0+IS7PzWU1kOpe9z+kOPs0Ct/LrgJVC090DUaRIIK8FF87uVDx2G/U\nfq5w4+lrSTU8yI3t0GfVEvkNsVwGp/9unm7ht2+xMiZIQbEzvuEnauEugXRe\nWOGrvnP2Cgmf7zGyUIhKk+odvGTePvdEIs55Y1jPulGlHm0EaapuhaCrWVMS\nXsiDWQJK8gxC8t3b1gdwI9Y8Pv/gvpIi+80hwOJs3ixep4+pSaaYjbMbJSS+\nJkVHry4PDqNStFN+BEPPtmEXoDGiPfJw0SbtF2IDKGHPqwZ65akB75E5tZ+9\nLbr4weY03MkbBBjYNdDmUIFRUNQYtlsqNxJY+TlGB5RK1NeceCmhhmMhg+Xf\n6NgwbPQNG/3rtsau5B0NgfC/I5R6klYjVIRngj52s52r0bWvfhyShX+sPING\nU18wl8GkCFalJoyNrTljt39P/oYNLciFzk7W9V5fd+iXzUe9SHgZwfEo/zhM\nuZes\r\n=rPdl\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.0": {"name": "@webassemblyjs/wasm-parser", "version": "1.7.0", "dependencies": {"@webassemblyjs/ast": "1.7.0", "@webassemblyjs/utf8": "1.7.0", "@webassemblyjs/leb128": "1.7.0", "@webassemblyjs/ieee754": "1.7.0", "@webassemblyjs/helper-api-error": "1.7.0", "@webassemblyjs/helper-wasm-bytecode": "1.7.0"}, "devDependencies": {"wabt": "^1.0.0", "@webassemblyjs/wasm-gen": "1.7.0", "@webassemblyjs/wast-parser": "1.7.0", "@webassemblyjs/helper-buffer": "1.7.0", "@webassemblyjs/helper-wasm-bytecode": "1.7.0", "@webassemblyjs/helper-test-framework": "1.7.0"}, "dist": {"shasum": "7bbae700f072308e1ced202bd57a8c1279e34354", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.7.0.tgz", "fileCount": 8, "integrity": "sha512-8Fij+4qydyT7UC9Tixf6z7mD02+RgkWRs9v/nS4NZyqRqP8mbyq0ZJ5lNpM0p44zUjkYULvFPKeiFMtk5PW3MQ==", "signatures": [{"sig": "MEYCIQDT2dcwlAhzOgtw6DreqWMDanYZ5yx2BdJUCkmfMHYWeQIhAMT/mhRKLMOgNWg6abXaWkvXK9xnr2CGl8vMAtHvOGny", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 111457, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbULXQCRA9TVsSAnZWagAAet0P/RiUMudZVClrOb+8GnCQ\n5aLKRup0EWJKleoyCUKRViEXu0ZZizndkoBzwvJ2R0vEJKwHsglysT/Aemov\nsJw7NFnycqXNX2pI/vbTIdMmP0OsC9an1bfxMc1oPflbtf60Bjwvg1YXvzeu\nhOSwnpfYRfxpMB94VHnLfG5QPEM/uzfdk9SR1EOqFtVzXZ/XE3dR16dEuoRV\nUs6BKV5q4H+ONsMzMTs4UsJnS3AK8adgCjM9qAgCFA1BD1kvwuv2wTqrOOeU\nAR5n9EaNtEtYRVAfOGnb2Hzxa5ScgquU+QmL9WOgNrrXOHia6u4flGcfbZEv\nvM5EJE/3+qYAbw3KeDQkp9gD6JIZ9kHv6oTh0IsiJ2b5scdCxgaJEgUNZR+U\n0pSrAIWxveXC0fIZs0qsGMumwerN3+3it3o4gKR3F/EJhI8b/jFFXnyAg+AO\naOWXWnFsD8JuSm23ZEU7sN7oXH7Y63K+tQSNiAOJhT5VnbiIRCc5MfGo6wxG\nsOkgyXINL2tDVqyoVAoeU6LM3Yvgs3EMKvt/y4b+oFrRf7wzYVC97RsTMAPA\nhR8MWO73IzkFja5e8LwmV8IJWRiF1M4W4fkhweYvHJOTAOabBSKHYurjlk6K\nUPbb+S4rEFdBBqF90ZgJ4LXg0hAiVp39O7GA9z6MKdlcHNgAmvhV/CyINRXj\nkvGS\r\n=diGF\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.1": {"name": "@webassemblyjs/wasm-parser", "version": "1.7.1", "dependencies": {"@webassemblyjs/ast": "1.7.1", "@webassemblyjs/utf8": "1.7.1", "@webassemblyjs/leb128": "1.7.1", "@webassemblyjs/ieee754": "1.7.1", "@webassemblyjs/helper-api-error": "1.7.1", "@webassemblyjs/helper-wasm-bytecode": "1.7.1"}, "devDependencies": {"wabt": "^1.0.0", "@webassemblyjs/wasm-gen": "1.7.1", "@webassemblyjs/wast-parser": "1.7.1", "@webassemblyjs/helper-buffer": "1.7.1", "@webassemblyjs/helper-wasm-bytecode": "1.7.1", "@webassemblyjs/helper-test-framework": "1.7.1"}, "dist": {"shasum": "d26d7461b49179677a917168a4e5308935907601", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.7.1.tgz", "fileCount": 8, "integrity": "sha512-pNuLvs8REc0ajv8VGV7URsss03S2boqz/5PAzN3f+Nh4SQdxVqYW4AvMEIVcHKxnBzItg0J2Uxm3XNZgwoHNuA==", "signatures": [{"sig": "MEUCIQC0MeBWvLYOZjCxwPl/Hn+bDNEuXF9HwIzuya5goV+xygIgSxoahDONUhHWsHbSjYaBPltymTOQViOq+jZ7D0ZqB9Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 111820, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbUL0ACRA9TVsSAnZWagAADmwQAI1SBLjVenPL0JpAtrCH\ntzomxVv0y0Wj47bILaMwwJS7F92ZP18KLAJ6Ma75c0eoVP1OEeMNbg6OPP9K\nLmu7F0CTLHy5jfBewL1rqIToSNQwjxqBaPLbgtrET1MIPfO0G7YebWBpR7vJ\nUgD1mNEeuyl1h00yemotlHWaqGFsoo9+DxDjrqA+G7iDw0dPotKi0fMvwaMv\neZ5yy52KX7aWjyCWGkjglAEv8rUFg31IT5xoHTvW3+4/7k4lMdqh3gWQi1KY\nku7Ns/4ao3R7rSzPRtnYs+Sr5P8Da0V7yH4ZblrWOzXDxkJ0b4200/0ez3zx\nk7wyoIoAg18LhiQF7Tx3t79Q3nw0exzes/CUa/UBeFL+dwLZd11cLzhBfc2/\nSmwhC1UP7+th4IMTTanEBRWqg8NP3DHc0caHKn/yiCnju7enerjSA07OUEeE\nRfCqG1eFzDsStQO43HB4AypnOHqRB01Cvh1tOFLOj31h0fu2iT7TtO3+KGCj\nqUb+HRLVa+vncJXWX4iLezsg3GaybYj0Dz0fCgeU6JWEj3cEl6FHpdCJLswO\nHV9x28EgzCpWLx9FVfO6kn1uo9N6FC40esSGZ1DoS1NXIxZXaU60Bil1oEVr\njFOotkBwyVRO5IjGDr/vTJsnWO+fKYjoIgIItwXq19EMbn9R2QI4lH/1mvEk\nkvjR\r\n=tRyI\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.2-0": {"name": "@webassemblyjs/wasm-parser", "version": "1.7.2-0", "dependencies": {"@xtuc/buffer": "^5.2.0", "@webassemblyjs/ast": "1.7.2-0", "@webassemblyjs/utf8": "1.7.2-0", "@webassemblyjs/leb128": "1.7.2-0", "@webassemblyjs/ieee754": "1.7.2-0", "@webassemblyjs/helper-api-error": "1.7.2-0", "@webassemblyjs/helper-wasm-bytecode": "1.7.2-0"}, "devDependencies": {"wabt": "^1.0.0", "@webassemblyjs/wasm-gen": "1.7.2-0", "@webassemblyjs/wast-parser": "1.7.2-0", "@webassemblyjs/helper-buffer": "1.7.2-0", "@webassemblyjs/helper-wasm-bytecode": "1.7.2-0", "@webassemblyjs/helper-test-framework": "1.7.2-0"}, "dist": {"shasum": "89a7ae40ccdc467fb02382c65d95c15d989969f5", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.7.2-0.tgz", "fileCount": 8, "integrity": "sha512-zYgSK83uq1mTS6YJlYD5PnkGnVjNkcn/PsIaLSwjj5zXHjZgLkxKtDQL2FGJPfKdrq9v2Z4qyveBC0mX16jmbg==", "signatures": [{"sig": "MEQCIF834AWT+6+pHFxF5hOqhZefLhTNTErVofK29pJTpi05AiB4+x4fI4r+8LieqF1PNYYBxv+BuILt311fARr62kQm5w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 112013, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbUNP9CRA9TVsSAnZWagAAobMP/0fCC1UIJ/MbPPKTutnt\nZGRliw4C8psv+kBak/GGUMqskBtvGZIXM5SFIyxTbfCkSb5UUR2eL2e7uylg\n69948N/QI/jk4g1STGI9TaOBuUxhvkLH3q2wuXB5WCZ23jg0Gu8T2w7XL/55\nUTxxLItmjeDKw/USGrz3EMqLPqZ6YkmTIIJbU8Cvp15Wl/arzLf0dX277B4L\nDQV+m41ZG1sypr118dtxdf7kwI07ppri/Pbp9ZgvfCRlP9NP/fY/WBu9aMhd\niGmLnWcDkUGvM7OUXPyGI18TLNS7bav8LZmFp2fgnCr/fJYKGSxZMdlYzhuB\n9TalVKCp3FFQyZV4fF98KEambvXSa0/qL9octScNfCM2R86NkEFZXmnH2do6\nG9OLQeHbmjPKYxYPYEdVEqXcfVIw98kVyQv7kgRNzUcOkLOZnvL848oeLTCD\nFf9xtK1PKy2o7AxX4wAO0MlKIVmyYAI7z0WKeIVybQyuQvVEMNHX9O5118Ux\nTA/Idx5JzvycmVyXabvQK0d4ToZd/DgB0lKnVggcz55FSBswE0m54lyr4h1f\n473Q8ZejN+Ks1RDSWiuyFATuN4t/+NTj1FwU0ZQ2plXp/F0RFpaTC37sDRi0\nz1ej6gWzwevK+i2+AXS8cmJsCPldCu1EaehX3o2fcdPjLnv0cYymK+A8Jf2t\nq631\r\n=BxlE\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.2-1": {"name": "@webassemblyjs/wasm-parser", "version": "1.7.2-1", "dependencies": {"@xtuc/buffer": "^5.2.2", "@webassemblyjs/ast": "1.7.2-1", "@webassemblyjs/utf8": "1.7.2-1", "@webassemblyjs/leb128": "1.7.2-1", "@webassemblyjs/ieee754": "1.7.2-1", "@webassemblyjs/helper-api-error": "1.7.2-1", "@webassemblyjs/helper-wasm-bytecode": "1.7.2-1"}, "devDependencies": {"wabt": "^1.0.0", "@webassemblyjs/wasm-gen": "1.7.2-1", "@webassemblyjs/wast-parser": "1.7.2-1", "@webassemblyjs/helper-buffer": "1.7.2-1", "@webassemblyjs/helper-wasm-bytecode": "1.7.2-1", "@webassemblyjs/helper-test-framework": "1.7.2-1"}, "dist": {"shasum": "f13e32c17ddfbdb7b0da029d7105ff98bb90bbe5", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.7.2-1.tgz", "fileCount": 8, "integrity": "sha512-u1H7TthBXz0bDTjYrnaBJUPv8Z76zJunzCyTiWCQ1H0kRRJRxYtKF3ZiJLkNPWjH+TSaJM+EIfari1XRhgFVNg==", "signatures": [{"sig": "MEUCIQDLtO9re6TOapAqc1g5HNqjnDCBDWgGAuxQpTDVpNwWZQIgC5pVy8tmrRLVy1Gl/y3hi0Et57irrW8IwOzmgq58Z44=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 112013, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbUNd0CRA9TVsSAnZWagAAjW8QAISqvCCE77MTAmxsHByD\nN4tfGhjezPvi+FfIzqXlqvRH0VPiBPOBLsckRVKZl1annESOQ1mg/MdN3fco\nE8iPm3xqnTLSJcJJjR44UKUX7ffEBoYoA8Gx4a5X5kQCUkPmP6oz5x4HR17B\nfU2biVsP2lVy9p4ADUcwpSbhusLit7hstnge4iXzlQ/h2WiWOd4AByItJAqa\n8Z6lVaNTwybM9bpU7LnmyMU/+XR3cs3PmMQHESvDQo3nqVO6sYdXx/GnTzOw\nPY+2SGULyszaTgAzXa4fzfxizt7fm7f3z4xyFM3dX1Nq52pvfFOXFrms365M\niT4QgHZYQTWiu8FIDOtCzWnc/qcKUXRht7TPJd39MZ+mf7sXxXACzhbzFDU4\n/qvquZfgUS7y+FANjAO5yUxA3G7cFf6V7jRHu/z3zH0q5vzOikqv7BLQeWhw\nZnIUuFGI394qNfVRR9SlUWC/SB8zK5K0NXQOGiqgiz9nkHUY6u0u54Gns2aI\nLS3UXhGhLt3GQE9m8F0mWPHq1vDB/c8SBIBqwvhGLqZzAXJ2ehCf9xipbs2d\nMPnORowvVy5YWoQN6REeMbzLhprXkW+g4MT/3g0No7pT9O3y8Sh+9rpP9mKg\na3J/JusM+OFb/oyozPo+8gXjA2EAQur57g25I6bRCa/br69kqcOMe7WLMv9H\nJThL\r\n=I2KO\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.2": {"name": "@webassemblyjs/wasm-parser", "version": "1.7.2", "dependencies": {"@xtuc/buffer": "^5.2.2", "@webassemblyjs/ast": "1.7.2", "@webassemblyjs/utf8": "1.7.2", "@webassemblyjs/leb128": "1.7.2", "@webassemblyjs/ieee754": "1.7.2", "@webassemblyjs/helper-api-error": "1.7.2", "@webassemblyjs/helper-wasm-bytecode": "1.7.2"}, "devDependencies": {"wabt": "^1.0.0", "@webassemblyjs/wasm-gen": "1.7.2", "@webassemblyjs/wast-parser": "1.7.2", "@webassemblyjs/helper-buffer": "1.7.2", "@webassemblyjs/helper-wasm-bytecode": "1.7.2", "@webassemblyjs/helper-test-framework": "1.7.2"}, "dist": {"shasum": "f55a562834861fe89bb8a088e21854a9c58d1103", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.7.2.tgz", "fileCount": 8, "integrity": "sha512-PvDnmqA0OyIKnhRFJnB0S5ut4X7eo/ocQpLhCJMR7KbNyIfyC6ovrKfc/rsYQ5qyH9DiP686RAfhlzLfmXJGJA==", "signatures": [{"sig": "MEUCIQDHD0EEMPS/EDqchAn4WOfENmdI2XJl6DTvUQH0tmAbDAIgKZv4S9vm89dkNR37NNE3QZ1xIcSVJHQ7VFgJymbjEv4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 111989, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbUNmRCRA9TVsSAnZWagAAdM8QAItshSg8JpX00q4BNGI8\nPJzdRbGAqQCpYnM5Nnojn/dM3nJR+eAEk/6jiixJcn/vRhAOXyI7ACTdcPo1\nJIv1DkeNeaUd4CneBRlZrozoh1ALUxJEaNRnuNjaNogCqNKAzTV+bVXwYr8r\nsRQAtrHfZ+ezQFI8Ugsp447LdNn6w8UpQWetifu/aDxq81noK9e85mT9MXIG\nB8HRqdkZzR+FwiR6PK1Dyaegt+UIebnzDELHaEJPo8Wf/8YsiRbve9CNbBWM\njrmtaaQFkc+8QSXJ4196Hh+rgcVaw8n+/wuxRsgl0cknkGzpBI3rfdbnklYY\nRiPUFhjI4Td5Bcs3DK7lwDVY8COyx6p4qtEZSoKkNCen4+GVLX32T6dr4dcW\nExK2XH350hBu1kJs7uPxgZ3HuaXzk/SbjIeBiThKAbvtixhZFu0z+CfF1ryb\nLp6s5IXYqJfdgRRTOvFMq22hJB8ypGd2jpszvt6c6ufkuhZNaakxnWvUiMbc\nH5E7E2oI3NvLBvv+x3nzlu3vXeHeep+FKICLPrKtyXvq14E+EU3ellEv4+g2\nD6TtI+JM6h77bjNAt91Y7uWAfimPo6h4EDJBJ+tlSXZ2Kdy3bYhnoLoFOJuS\nkugSSVOx4LdY7iinzhfyineC5Eq53rHIx8MuZrt5689IuTbCiL0r3TzFk6E7\nN7w3\r\n=q2Cc\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.3": {"name": "@webassemblyjs/wasm-parser", "version": "1.7.3", "dependencies": {"@xtuc/buffer": "^5.2.2", "@webassemblyjs/ast": "1.7.3", "@webassemblyjs/utf8": "1.7.3", "@webassemblyjs/leb128": "1.7.3", "@webassemblyjs/ieee754": "1.7.3", "@webassemblyjs/helper-api-error": "1.7.3", "@webassemblyjs/helper-wasm-bytecode": "1.7.3"}, "devDependencies": {"wabt": "^1.0.0", "@webassemblyjs/wasm-gen": "1.7.3", "@webassemblyjs/wast-parser": "1.7.3", "@webassemblyjs/helper-buffer": "1.7.3", "@webassemblyjs/helper-wasm-bytecode": "1.7.3", "@webassemblyjs/helper-test-framework": "1.7.3"}, "dist": {"shasum": "dee9769b25dd1a73fcf2a4e6999af0b209d391f9", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.7.3.tgz", "fileCount": 9, "integrity": "sha512-8K/b7XqB62J8x9mCz5nJzcFoXAtpo5XQ2q0taUJWPtN8L+tUaQ7sqlC3MlHZ6vohKtw3HOYOnY0pkAJgHB6a4A==", "signatures": [{"sig": "MEUCIQCLarLH4Fg83S0SXFUmc6vOaPD2sAfRlaKWLcOHGjVSrgIgXLySFg7druSzKiRwruD1Z6t86qNteTiWP7Q+AMa4zcs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 114133, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbVX2TCRA9TVsSAnZWagAAyO4QAKONGVWpnv36FQSHc2K5\nZSGG7g0OmpLrY5BDfolBeMteeAUD3dohTl4e4DjGLaCWAPJpQozznb/rcBmB\nRps4T8J8zFLiPMqMURUadKCiKzikSM0R8h5+mG9A4v9eYeduBhWxvMoMgn5a\nB+OWf7HxhbLs0U3kPpz4Nq7bTQ3AsvNJjvzwC5L8xtvuoXPcGOy9J7XwXM7v\nYmQQokAOathH8V8zcI9SN91H4NliyYehp9o7GQywZXiSwIMnsO3FBzomQlVF\nL2wzEZBr2mF9zTm0RxIKmMWLo6jMmfy0J8ZoXFn/vqkTZtNzNpbmCEm10BRu\nMkFeeTM6HkITAu/S/aGL6EKHUOtyhQcQfO2hhV/u8ytO0awbo4m/MlQdIgkC\nxmr5L48DL8g14VtLlwoZnOB+W4DyhBBu/a6/Ueg1ObPQH2ls9LpUr+SijYkG\nuodDx2/ykYgDmuItz6lv/Aj8axGmDuszmZvzYp7pt1OGg4WOZSahOu+XWm2q\nmQU7Ewl9PosmFAuDYftr5M1g9UFd2ySRBjNDJxqT57h2XemzhtGpusmKCAL9\nyBzeWa0xfrgNlZv7irDAmS8IHrapkl3HA1+cZLLBSK4lLihGYiCTiA5SU9oj\n3PqlErvO9QUH46WB5cYTHFAC0RdRDO3PzmzIz91lbH5DSZRZwf1X4joMnmKv\nufxS\r\n=mK3x\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.4": {"name": "@webassemblyjs/wasm-parser", "version": "1.7.4", "dependencies": {"@xtuc/buffer": "^5.2.2", "@webassemblyjs/ast": "1.7.4", "@webassemblyjs/utf8": "1.7.4", "@webassemblyjs/leb128": "1.7.4", "@webassemblyjs/ieee754": "1.7.4", "@webassemblyjs/helper-api-error": "1.7.4", "@webassemblyjs/helper-wasm-bytecode": "1.7.4"}, "devDependencies": {"wabt": "^1.0.0", "@webassemblyjs/wasm-gen": "1.7.4", "@webassemblyjs/wast-parser": "1.7.4", "@webassemblyjs/helper-buffer": "1.7.4", "@webassemblyjs/helper-wasm-bytecode": "1.7.4", "@webassemblyjs/helper-test-framework": "1.7.4"}, "dist": {"shasum": "8a8f8ebd9594a9fbd3d69437bfd1a28d35c10a06", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.7.4.tgz", "fileCount": 9, "integrity": "sha512-RczSwtU0/pwywePhL80cF9iJ1vFU6MXHFnQo9nRD/nYCTbSMUvvhYxdDf/NwXezpfBjWGGJYBewECbZmQzVI3g==", "signatures": [{"sig": "MEUCIEFOw/8FQOz/x63gcsvAkblh3stOP54vydSCHf/PKlpPAiEAgosjiy37JtGGSIbIsMXzqbxKQfwRH6yaW3uOTxKmxyI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 114627, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbVs8JCRA9TVsSAnZWagAAKcIP/34kgF34xuuSjl6MQTI6\nToSKoKVqW+YE4kFl9hYISXpdLxCRPlSo1li5072DFXNAgieHNrRkVPUOYUWE\nK5o3DGBYpBvTEuHF5xgOZR11Z3NwI4EUVMarD5bUbBXj8EvFxctUu+UrRS19\njioy4Arh41NSxOXCxTlcwXeleq7jksgqvfxLUZd9pjq/xYQKfznJ/Bh+Jqfz\nVqb51QnvWgiU5JX2rNLcQWATAAud4zLpK/3+Nof2cULnyEF3Cvb3gK4QojZT\ndAIWBIFmgf7xf4Et7mFOzgvU5u5BxmvPH8V/vNkTESGOPCn6n833uGqGN62C\n/kwCVb8vN6I3tb6gTyORr973rCmoCiyOotBncd0dWgyjVxdUDlH8isuc3kYt\nMVxDwvEoh63FCKzmJn+iEivJ+WQTYabT6jjiSyrgzGGr3Q4mGDPIjjj0nd5i\nkal+CNyt1iwyL31n4BtYKhZSD5Y25Y9kTLaKWdW/WexBOppU1RqXcptP2RbS\n2+Zh0tMGsP1WXQXGE51E+lDRpHCc2DKEyoiXHBFPLzDQ38pGEBCl8xNzDwvo\nLU9aMYiqTkXQRUgkCz8Py7DIXfQoZnInhTEXbZqpiOGqto02ZwcAIKCR78tc\nFtLtmEiNzLrgJZTFsA+EStd3dH+YoioPFXWYQzDnyzpuJU38YFqMr6OTQdVM\n1I/3\r\n=QL9i\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.5": {"name": "@webassemblyjs/wasm-parser", "version": "1.7.5", "dependencies": {"@webassemblyjs/ast": "1.7.5", "@webassemblyjs/utf8": "1.7.5", "@webassemblyjs/leb128": "1.7.5", "@webassemblyjs/ieee754": "1.7.5", "@webassemblyjs/helper-api-error": "1.7.5", "@webassemblyjs/helper-wasm-bytecode": "1.7.5"}, "devDependencies": {"wabt": "^1.0.0", "@webassemblyjs/wasm-gen": "1.7.5", "@webassemblyjs/wast-parser": "1.7.5", "@webassemblyjs/helper-buffer": "1.7.5", "@webassemblyjs/helper-wasm-bytecode": "1.7.5", "@webassemblyjs/helper-test-framework": "1.7.5"}, "dist": {"shasum": "b3fa79b66c8d92e0f56f46587261c9f399878e4c", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.7.5.tgz", "fileCount": 9, "integrity": "sha512-kKWYL5VhnXoYbRy1QgyNIoocKT8KWPPzUDeF/RN6TZ0kPIGg8ECzaRcutopG56Pkg+UOcUuforpjm1y1m8TOtw==", "signatures": [{"sig": "MEUCIQCzWdsyZo7LnXB3EsNg+qmBByknMp8mpQV7iOiS9k5YIAIgQCrF+E2LrdVFgaWffqw6ZL3TmNnlnlCBqSuQAJHrObU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 114458, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbdZAoCRA9TVsSAnZWagAAtI8QAIqXnyW/JjIjNpt/yUXb\ndSVr3DHyUvS2e3gQcA/DhEqAkTFiG8r7O/Dh1VKH6rxxPMh+UucCYG/wOA/o\nqo3biOENKi5GSfYaNhSp9C/EG2hTr3EIG0e+Lh+1tkgCkR+jx182/qJqaxX4\nILj7VLUey0QxqF/F1Q6FhzdROUnSVQJiuLwGmwPnQ9zz1kuz3tsc+hWnWmV7\nNLoUTN33gfVaIR5u/NHuEfSoHHXpN332c0Oq4dTQEE+vvN8MTAnlfVr1rBm7\nbYlK2wrsirgtouXx9QTiUHNC+bYKIVGqXmFw3xFRA+Q52YN5cBxFRqsbbJAg\n+MmK87JzvW4HyvgvtZSlWaoo2MhRo3psgNwRWsSnDPFYsryEojpqr5CbWy8O\ntAsgZGO5m1SSW5YYxPY/2UX3wLGS1YNQxMZLg83MPfXlSqQN5IUGn0VHiahf\nQdCcwQgxMyL8rD/Xw++OcgggSUwp6nFufZEMSi6wBoCcqnF92X3D7ZVp0OqG\n+qVmKygIpKyE4ZpIKROxkkM53fWvjq/bHtqXFUcqpzs3yEl03b0a8wYzdHEb\n1k8lE8pv8VuDsUFZwUMFS5z3r+7Qcstn08JtgF9vBMSMG4Pem9RJKh/iyz2U\nJe2jY69/KajxF0iv2khKrjy2ClJefTEE3dukIoB9R1BOgdPiF34RO/DCFepB\nQOPO\r\n=HMfu\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.6": {"name": "@webassemblyjs/wasm-parser", "version": "1.7.6", "dependencies": {"@webassemblyjs/ast": "1.7.6", "@webassemblyjs/utf8": "1.7.6", "@webassemblyjs/leb128": "1.7.6", "@webassemblyjs/ieee754": "1.7.6", "@webassemblyjs/helper-api-error": "1.7.6", "@webassemblyjs/helper-wasm-bytecode": "1.7.6"}, "devDependencies": {"wabt": "^1.0.0", "@webassemblyjs/wasm-gen": "1.7.6", "@webassemblyjs/wast-parser": "1.7.6", "@webassemblyjs/helper-buffer": "1.7.6", "@webassemblyjs/helper-wasm-bytecode": "1.7.6", "@webassemblyjs/helper-test-framework": "1.7.6"}, "dist": {"shasum": "84eafeeff405ad6f4c4b5777d6a28ae54eed51fe", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.7.6.tgz", "fileCount": 8, "integrity": "sha512-t1T6TfwNY85pDA/HWPA8kB9xA4sp9ajlRg5W7EKikqrynTyFo+/qDzIpvdkOkOGjlS6d4n4SX59SPuIayR22Yg==", "signatures": [{"sig": "MEUCIG7qf+Fj7GSq0JG8W65T2ENiiFNpjYnU/GJTwg6o8UYFAiEA1485L5eKPUWqL2utsCjSwjZgtJ4WrbBd9IEA8fY1b+U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 113224, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbln0FCRA9TVsSAnZWagAAiv8P/RdJ5uagvbxoeWXMqgUh\naLlhbZ/qWcsCxaMmDpNUiDeT4W5mevgEAbYzZqWpfUj3uxPg9/nOV7ExMFAp\nTo50KFUpTbz2hJ6a4BmKHwD7d4oqNWTaa0RUGBP60vtrcPc6QkT9GnSPU0Jy\nithCQZXAMUwQC8ONuJ5th3dNOnVArLiCrGTIgR0YdUTfw2a+X9qVX09eflLN\nbbElrImOaj5pcsWPlAXqjRsY1YlFzlrA2cuyV07L8CKibFb6TpjibeEzm0+J\nS6KKakpkEpabiCMZpsLi1uCnoSh1vu0mU6rkaCoa8llega6A5MEwUrpxBbkW\nCs8IyjjL0yzktiBGznrasK5BivpFcaUhqMmbuuqpMDJ+Pj45i9/+aijUQjwL\nCg2ktQgnCUHqjeWF6oYov9w7MFfp0W35xs4g2TYOt3Taf38k8LvSY1QInDIj\nimg5aQ/nL4dSQqkbaGWPPUT3THh7Fa4Z2jsrUckMQd9tH6xB6FdoWJeFNWus\n1A9cuFjanvZFgweTSWmDpw10Hld+PiRaliBd1diC5W7PzUhGU7haEoEl/hb2\nkmtO6+QCmvl8urGUTVrE0NpcXz/n3yJa63A3bldvy+HWzvQh8QmF8cEHRpbx\nXOwA9s2jUuRaCnIMV0bRV9aLRCxI7ey9IVFdm1pJpzUb/fbQ5P16whOIyOv7\noaG/\r\n=u0Mu\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.7": {"name": "@webassemblyjs/wasm-parser", "version": "1.7.7", "dependencies": {"@webassemblyjs/ast": "1.7.7", "@webassemblyjs/utf8": "1.7.7", "@webassemblyjs/leb128": "1.7.7", "@webassemblyjs/ieee754": "1.7.7", "@webassemblyjs/helper-api-error": "1.7.7", "@webassemblyjs/helper-wasm-bytecode": "1.7.7"}, "devDependencies": {"wabt": "^1.0.0", "@webassemblyjs/wasm-gen": "1.7.7", "@webassemblyjs/wast-parser": "1.7.7", "@webassemblyjs/helper-buffer": "1.7.7", "@webassemblyjs/helper-wasm-bytecode": "1.7.7", "@webassemblyjs/helper-test-framework": "1.7.7"}, "dist": {"shasum": "04f34715d3138c1ee937ced679750223bec5b519", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.7.7.tgz", "fileCount": 8, "integrity": "sha512-zi/p9RRr1Kvn4p2WpHMPhG6FsZddm5XVzatXQeFOPxDrvqi4GbJ0IzudpgTFT9Au8bCM2vjZLJinhaTt64galw==", "signatures": [{"sig": "MEUCIBRx8uOz8ewv+6NVRzWa1VF6kj2cPkShVazJWsH5oA7iAiEA4WrYesNvIPrjXJm1bDPQsvk8o4xaaWtArygGLvkiRwY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 113224, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJboeETCRA9TVsSAnZWagAAnYIQAIrQn1o9H/YkBB3yiR5c\nj4Q4R8NSHTMYsW8LsTKjDzNADDxb4C4AMJICX+/99GROKGpUaUpqvCDU7+oM\noJtbV2HzWQikPYidlIQdN6hlw/ax34dPniKucmyKMOoJyRqjUXjEAoCT2z1+\nXLnnrWpRcKP6bPH/3DmEdyLJNQCLUIfTXyJbLRrk5U06rdVdzUQ4Zan4CtPc\n8mNeh+hwRxC0ufFTxpD28Vb98s1HzDeBI4Q69P20y+c3uj7BUCxXNXUWdJoQ\nMj0JdSwl2jYQkkKGXogtuwKMteSmhJd3L0o/t1MqSKjEnaaeWnfrYwZmFzfJ\n7UTNSRIB0yt/O/85/+82ll+JT6zJrICL2st0OMlGc+eKYWiKQEIV4m8hIzSl\nNWGnXnU17DTElymDK81qyoykJJi9sr+0V+M/RqQBwFgfHkX2SIRvmmJnUO92\nuTK/hM4WeBq5E+2YBRNGDE4pimuXqBsb72mg5gF6MwzNnKJ05clcEkvcFT9S\n/P9CJFkuynPaXr6SGCDCF4iWb1DatyXRRR+pAJsuvEnV5lOPrhLlgVdn93T8\n5kk8UXuEc3oxlAFsLk4p8uxT4tomGdifqQ7z+jk7P1EkUdNBToW/kqDBXe5B\nN0HFRBTEmthEDLz+gp9vgnRPrgA0IbgZmmHwzZtY9IUP2MdAL9yCUw8+6jvO\niZeU\r\n=HZEf\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.8": {"name": "@webassemblyjs/wasm-parser", "version": "1.7.8", "dependencies": {"@webassemblyjs/ast": "1.7.8", "@webassemblyjs/utf8": "1.7.8", "@webassemblyjs/leb128": "1.7.8", "@webassemblyjs/ieee754": "1.7.8", "@webassemblyjs/helper-api-error": "1.7.8", "@webassemblyjs/helper-wasm-bytecode": "1.7.8"}, "devDependencies": {"wabt": "1.0.0-nightly.20180421", "@webassemblyjs/wasm-gen": "1.7.8", "@webassemblyjs/wast-parser": "1.7.8", "@webassemblyjs/helper-buffer": "1.7.8", "@webassemblyjs/helper-test-framework": "1.7.8"}, "dist": {"shasum": "dac47c291fb6a3e63529aecd647592cd34afbf94", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.7.8.tgz", "fileCount": 9, "integrity": "sha512-rZ/zlhp9DHR/05zh1MbAjT2t624sjrPP/OkJCjXqzm7ynH+nIdNcn9Ixc+qzPMFXhIrk0rBoQ3to6sEIvHh9jQ==", "signatures": [{"sig": "MEUCIQCKUbpgxNgqim2mH1CDwhkeSb5t2KTFJyBOSC7dVZiVnQIgRG4XjPPLq/OZ7FEctyJ7CaLI+bnTUN1WtawcMZqUzb0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 114385, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbpJ/hCRA9TVsSAnZWagAAxNYP/0ibmJ77KJQY1IOpjP+4\nYdN5W5LdfBQcYtjW7G1TPIQol6hQ8jGZQ3uFCm6Gw/5Pi6Se5WxQfaglqreY\nGXeD3FeaOn/zDEgLB1nEaoE4V6qMLc+B6H7zqqyS3c5fNemgfp28jCuG5HZh\nmkn7ivvEH4ADQG6pHufNuwIoOaMWVHhVtFIQnSbikRu5C7BiHc/KG0krgboW\nAzJsxhIvBd+XEDtuN6Owh0hsd7jwy+PfncGH+dcniOSCQEmWE5zLz7UlvCBo\ntl3l4A4asXqgaF/Oo/LBDoeBsb6RVEFQcuF9iECTGwDliql1IkTlGpd3Hc3H\nWw5Z+44dS+AfmtI07hekT/bpIFiPo3+kWrqeeejpb814q2LZRRQBKz3x1dZM\n2tuSFwE7LXoZNEsgXlMjjuPJA8jH+imC7oNw9CBIxUaV4E+wlUZZY1Zqwasd\nVCSOxLCeJM5kO2rVRbvhZKfY6PRbNhoMj42ewAEVdYeG7+JvscPqPQTCDI+N\nGBsVSYKqsogmhWF2TABR0ACdbvFmEbDueub/46x2oje/Bni3/RUhC/RzE/q9\nxZnIN+Nmx6GAX0k6lhVzq8+tUy1OGIWFuNLLgN18CSvabanrLig2C4fXfiVF\nRayIBUJJV3//WpDvKKaiQ1Rgl+UuwnvdX61gr/aBcBrUYzFNanqfYbRU2/BE\nXh8S\r\n=qK2P\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.9": {"name": "@webassemblyjs/wasm-parser", "version": "1.7.9", "dependencies": {"@webassemblyjs/ast": "1.7.9", "@webassemblyjs/utf8": "1.7.9", "@webassemblyjs/leb128": "1.7.9", "@webassemblyjs/ieee754": "1.7.9", "@webassemblyjs/helper-api-error": "1.7.9", "@webassemblyjs/helper-wasm-bytecode": "1.7.9"}, "devDependencies": {"wabt": "1.0.0-nightly.20180421", "@webassemblyjs/wasm-gen": "1.7.9", "@webassemblyjs/wast-parser": "1.7.9", "@webassemblyjs/helper-buffer": "1.7.9", "@webassemblyjs/helper-test-framework": "1.7.9"}, "dist": {"shasum": "cfff58c2b8cd6f6d0e44d56ab158ea4bdc847c70", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.7.9.tgz", "fileCount": 9, "integrity": "sha512-b8iUbeRgqlln7T6VHvgHVLM8Ot6OqN158eqHIdC6hdlGtS7feC5vdn6klyHVI5mizMpZ4U3xgwurIlWOs91Z4A==", "signatures": [{"sig": "MEUCIArqL3xt88M3dq5jBa1mLYDt2igD+Q3aXJOSBKbaCgXvAiEAozjui6nb/wlA/fFSzgHfIlZiDiIIvaGsfBiVtAqsRYk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 114385, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbyLgiCRA9TVsSAnZWagAApfIQAIgrMkbpgoP0mh0a3eYH\nXp956/IaPbsrSiO4JUpQmmxcoNOlZVOolNpAIPmUbTpvz32R6jVGEThaYIZV\nhTgrXpkN5ao/si711i604A346txv+3gnd3+xtEkRA2sYQpSyln7el+tlp18m\nxMJARYY61NuvsBeaN6E7qjrCrCTiUBv4HWK/qDEhKOVqy53gDqutWgoQjJZr\nz6bZc7vo3mkehsI6ye8LRrbIKMQBC1oNxpV4r244pF48/NtW6ZEzX7kydOkZ\nc/lgtkkHw6fYI0zZZB06QeR5My/hzk9PXWn2HjZ+kEub0luGopNslcHoIUvl\njvoWJIm4Dgmha+9JgrWSnWu74YY3twugrW0X7/a00g/OxkugFYbr5Y0nLXLJ\n8b0AU7u601Bp+L66tu4WfTOgIJHyBmGrjO19vOyHv/xLYsnO5r50KtHg4JZp\nWyze/+Fa9UsFUARtm08sjn2CUXIFaj1585yrYEi7y0b60ALtwZ7YuZ/KjL1J\nBW7b/0Nbc+MKHKiF9iuZSe7XO6vfGU/VeDZb2NapQUVS+bcowwoBbOyQ38Ii\nHqjjFaTFVFtqUEERrl5BeZv6lxzuObHEUnGLboAhBZhMxR7PiAj40e+2ec48\noOOTdt4/llg2Cke71SmuDt+qfvS1TNtlk3tWTkcP0u4VlFx28afRKje33qL+\nYb+8\r\n=eadT\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.10": {"name": "@webassemblyjs/wasm-parser", "version": "1.7.10", "dependencies": {"@webassemblyjs/ast": "1.7.10", "@webassemblyjs/utf8": "1.7.10", "@webassemblyjs/leb128": "1.7.10", "@webassemblyjs/ieee754": "1.7.10", "@webassemblyjs/helper-api-error": "1.7.10", "@webassemblyjs/helper-wasm-bytecode": "1.7.10"}, "devDependencies": {"wabt": "1.0.0-nightly.20180421", "@webassemblyjs/wasm-gen": "1.7.10", "@webassemblyjs/wast-parser": "1.7.10", "@webassemblyjs/helper-buffer": "1.7.10", "@webassemblyjs/helper-wasm-bytecode": "1.7.7", "@webassemblyjs/helper-test-framework": "1.7.10"}, "dist": {"shasum": "0367be7bf8f09e3e6abc95f8e483b9206487ec65", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.7.10.tgz", "fileCount": 9, "integrity": "sha512-AEv8mkXVK63n/iDR3T693EzoGPnNAwKwT3iHmKJNBrrALAhhEjuPzo/lTE4U7LquEwyvg5nneSNdTdgrBaGJcA==", "signatures": [{"sig": "MEQCIEqe4t+8XZu1Z5kY3B8PAM16MiIglVRPs7e+cDSRv3zoAiBFQW6yJwWmsknME+vgH+bKlaXZdc6CsY/Zu7vJGUQVdA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 135088, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbzuveCRA9TVsSAnZWagAAXa0P/39CwFTrLHtoD4uu2cpF\nw0O4ZZejoxVJ7JMTJXymfUhghw+MfoGXFiDg2G48KmmrILdyX0/cuaD3/kVD\nqT3Rl7FWx0+lrZ+vIZQIu2KKRPiXPot4GSfiqQWNaNR33ZiB+3inWUVyMlhO\ng/v8fbhVLTVxECs8QmOWnvJo9p6MAu55vO/LrAqnd6ZGSJTg5RtMwvrje7tO\nYPIkb+gKNw+ebrlwnSO1wAWvUM9NEwspI+lxfVMFcCH024e7PiENFlPKi62r\ngLqrh2ae5Xo8QmMC2LtSG92NeZA7zx6WXqahtbOv2pEZMjjynkdLFjXCfCP7\nt04GZLbEBribc+/7XqVinlNazlMp8W0ZozKPoqJDhK+3Dbi/swPR4DKQBN+c\nU1fya5CoEsxZQs36Oap/UYpXma60n8JTamnW4CVTHwyJSiYd0S9gItfLzoa0\nt3cCVPqoBmj36CKbv4hH6h0osXEDzgxsWdtYZTVQrPcE/fCH4w+acE3rjUrx\nBUwaL4e8+5IQhQkiKpAiHUe2QJQAvuFrufQJNHPU6mC8JKrmw9h8uWsJZE25\nKGMrbcJAjcfd1kFQUCa44mKmWHq7Hwd50kd5vK10puar7vQ/NETsUSpTABbx\nffOQO6ZuoUOlDPxy2e2jnA6nRBEmA1NOZ7OHUdQC0ZjFv7qv6L1Bdshl0+Kp\nY3WI\r\n=hj/+\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.11": {"name": "@webassemblyjs/wasm-parser", "version": "1.7.11", "dependencies": {"@webassemblyjs/ast": "1.7.11", "@webassemblyjs/utf8": "1.7.11", "@webassemblyjs/leb128": "1.7.11", "@webassemblyjs/ieee754": "1.7.11", "@webassemblyjs/helper-api-error": "1.7.11", "@webassemblyjs/helper-wasm-bytecode": "1.7.11"}, "devDependencies": {"wabt": "1.0.0-nightly.20180421", "@webassemblyjs/wasm-gen": "1.7.11", "@webassemblyjs/wast-parser": "1.7.11", "@webassemblyjs/helper-buffer": "1.7.11", "@webassemblyjs/helper-test-framework": "1.7.11"}, "dist": {"shasum": "6e3d20fa6a3519f6b084ef9391ad58211efb0a1a", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.7.11.tgz", "fileCount": 10, "integrity": "sha512-6lmXRTrrZjYD8Ng8xRyvyXQJYUQKYSXhJqXOBLw24rdiXsHAOlvw5PhesjdcaMadU/pyPQOJ5dHreMjBxwnQKg==", "signatures": [{"sig": "MEUCIDsi9DjMNLzc884vlPP9vbNHne+DIptOAQW3M355OufWAiEAjTzAvDT2ZSzxUIivNzTIlQwiFscEnnk2pOkSByPUDDI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 136176, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb2JxyCRA9TVsSAnZWagAAx28P/1qNNNh2dscXM+RaZLL9\nnzjSQ668yAgOnZEOTDDZ2LjlO2QDmJnYniPAx5l8oH2810qVkm1nAzASYt9z\nQySYkAnGMAYXRLxHcKwp38TDhKu5KaBjd0VmGOQKkfWyrsVbUIYHUAHeWRz/\naMersX34b851arwFTW3ns1Sj60FGSWqB4BSWDUt4PkZmrxikchIYi5jlRxGc\nDt8QPPcwf/slUQGvZaJVI4in7T+7WEzoMuRr8HmQfeZvc8RAngkt6TTgkDYK\nJWgO/NelQpPnZHF+OXQ8pGelRb1EOxqWjhf4CUpu4Os8YdNWMjOgVzY/MrAL\nc/uMWWcVhh+6ZjHLaN/Adb2jVUWw3s9vzMqIoHbl8xHFb9NIp69zx3pXn5RY\n6zlyotJYrAaA2OGk/LGMaRZiPKrcN5o+2lU6NxlojEdrFZq99HiYhW/Np6At\nmkIQeBJb9/qNdfY4viSozKrjq2HPUuCVVLHnCQbVOfvYTnrCb955oLFfuO0s\n9ptaFBHGslhvDSsbwPPexFc2anHOn2PD9mEh27AxnAnULJf8oErIj74lpxdT\noPLlLaG1FyZW+DW5ko6jsWZv1VLW+BvZzlqgURQRTKQX95RRvHypVh20yBZE\nHTQpIPrdixxXiXsrsgMKI5PerIFzBNKpqXdWCt7fgM9AQCoTuFQ8JZakr7JG\nWDmN\r\n=MMKt\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.8.0": {"name": "@webassemblyjs/wasm-parser", "version": "1.8.0", "dependencies": {"@webassemblyjs/ast": "1.8.0", "@webassemblyjs/utf8": "1.8.0", "@webassemblyjs/leb128": "1.8.0", "@webassemblyjs/ieee754": "1.8.0", "@webassemblyjs/helper-api-error": "1.8.0", "@webassemblyjs/helper-wasm-bytecode": "1.8.0"}, "devDependencies": {"wabt": "1.0.0-nightly.20180421", "mamacro": "^0.0.3", "@webassemblyjs/wasm-gen": "1.8.0", "@webassemblyjs/wast-parser": "1.8.0", "@webassemblyjs/helper-buffer": "1.8.0", "@webassemblyjs/helper-test-framework": "1.8.0"}, "dist": {"shasum": "deed8d0f96d802e79b4d75930c0da170fa018a55", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.8.0.tgz", "fileCount": 10, "integrity": "sha512-SbRFN/Xu4wIRzV3CBb+JHoMDNqitszzGGEj0z+K+c01b4wb2xJ8LECy7zykbvlNc4IFXA7TemgvqaqG3WJnDmA==", "signatures": [{"sig": "MEYCIQD0GDuPl/NDSTCgNlUrfFqbVpqA+IKRvkVFfSFTWuRc1wIhAIlubZJVnpOX62sIVq3kUGVBWye5M2lwzE3/kJ7w7u+j", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 139939, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcD2Y7CRA9TVsSAnZWagAAtK0P/Ajx5lvQp+Z/ECCH2ZXO\n0F6NOKjg6lkUHttBdUPcyWSDa1djW1iQKHNz3rffS+KzKy+RtOxizZNJhsfL\nTCfxm3sRcOoDx3x0O0Ym0dTgkahH5l1J7QHzDQF1ShD68dISi/L77Acb0m9q\nxTEMfqS4WRoEP5KDlADYflfcfrP8VqFQWR0CyBZQJJR2Cbd7BvGOzeVJPo5T\noDejdSa1T7h9Tnybvfwx5zi6uHmGfDfFjufoMUXdB4uUeQ+k7qDgx5KAFHgK\nkqe6pOsRDaYiW01KsRl864y44AWoBchUIoLi/b6DgzDNuDTY2XtkTDaDba0d\ng40GKx5J7E60MC+mbtGk58rrZTpEdNsvbOMpCw48rDNSlulKubHRPlzmoIPK\naZ3lzgrx/8GBgxhk02Vg8Uf2oJqzCFKhsqeFwfKV9WCHMvg/wwLX2Ovil3DP\nOY1/m5Y/eEMOdr5VCtUSlQUF5FcTlXiAxySQDenAooremcC71PX6Y5rvYgE/\ni30Who7CZI6wsqmR0fRpentTdlNnwGRjFIctfyj2SuFBwgu+iCq+iDoeci08\nz0hDO4PH9AZPOXbQxYo9cHYWQUFwACLHef5pMhgzlku4zD7B2X4TyfqxQvgY\n3R2H1w+RXtyoRJ9sZvZhymKpykaulQtDKZQMA588QskllobmQwtCE965SLV2\nvCu7\r\n=N97B\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.8.1": {"name": "@webassemblyjs/wasm-parser", "version": "1.8.1", "dependencies": {"@webassemblyjs/ast": "1.8.1", "@webassemblyjs/utf8": "1.8.1", "@webassemblyjs/leb128": "1.8.1", "@webassemblyjs/ieee754": "1.8.1", "@webassemblyjs/helper-api-error": "1.8.1", "@webassemblyjs/helper-wasm-bytecode": "1.8.1"}, "devDependencies": {"wabt": "1.0.0-nightly.20180421", "mamacro": "^0.0.3", "@webassemblyjs/wasm-gen": "1.8.1", "@webassemblyjs/wast-parser": "1.8.1", "@webassemblyjs/helper-buffer": "1.8.1", "@webassemblyjs/helper-test-framework": "1.8.1"}, "dist": {"shasum": "c21e5486cbe57d026fc8753ce19ab1e919856859", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.8.1.tgz", "fileCount": 10, "integrity": "sha512-k63WJZdIjTQgZt+cn8rsIEvW0aNKttGip6ygTE/ZPXKZsMTk0G5xyw+MQxphbvt/GYbNu5DdxGN/7WGybO95TA==", "signatures": [{"sig": "MEUCIDYz0CIVKu6XkWS6xjfreLstK40rvnpmA4V+EPIApaC9AiEA3gpKsfNYw4Gpybu4sHvZ4bIkeNCwkl/93UnQfHegwIE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 140475, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcPZgUCRA9TVsSAnZWagAA9EoP/Rwa0NZIgOkBGNKu8/q1\nSy+LDWPzlh1hxcUlfVNMkXpV7w+s7Y7OBExRHsReQufnpAvoAgGLFjNaeZEZ\nPoH3QfUDYqDa4tljzcRzwEQ+PyVWxVu9POBZ4oGxfzee19jCgodyvV/bA0ny\n9BZLlkTGQ7kmhzthSi1U6NEaDHf3YDInGuv018+wWWduR64r1KmLkWmwQ5ep\neexZfAIqasniYVz817dZXUxThMgTDYW2LEGLtTtLZZI54UA4V7dICNOQy7JU\n6czifvbQSExkr+ByTZ3uc0by1NecskoJJVSYwAxWSBHYJd5PiEGWx1WN8NjF\nGSBs2U16ZxvisKYZCWgtdE3jvIYyCDxthZTyGE7fs1VSMluNmM+66jercTvu\neMKfGRwhRRY+tKJb62iPpkRhvuVprhotI6H410eD5DkM7HKmHd62AltihWFC\nkwLMg10WeFpptHGdeUxN472fJfhDyQtyej5tnZTlVfF/j0aztv25I6URjk6b\nQ3jwbzze/bUaFfmNLO/kJuwtMKKIvA1p24mMMM+DhMOUx4+HLVwxCJTLJ887\n4xowebQAjkkN0SZKlUdlv68jk/3EmBzzxzEA3i6GY8OxO7w1htwijLcrxdlE\nTPWuShFJp9Y4Gf/T3hflj1xKeuz1a/D80ApgiHwBhfloxiys1EhryyTk4faH\nGd+A\r\n=OWeE\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.8.2": {"name": "@webassemblyjs/wasm-parser", "version": "1.8.2", "dependencies": {"@webassemblyjs/ast": "1.8.2", "@webassemblyjs/utf8": "1.8.2", "@webassemblyjs/leb128": "1.8.2", "@webassemblyjs/ieee754": "1.8.2", "@webassemblyjs/helper-api-error": "1.8.2", "@webassemblyjs/helper-wasm-bytecode": "1.8.2"}, "devDependencies": {"wabt": "1.0.0-nightly.20180421", "mamacro": "^0.0.3", "@webassemblyjs/wasm-gen": "1.8.2", "@webassemblyjs/wast-parser": "1.8.2", "@webassemblyjs/helper-buffer": "1.8.2", "@webassemblyjs/helper-test-framework": "1.8.2"}, "dist": {"shasum": "7a9d587a3edab32fc800dc9498bdbdf90896bb79", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.8.2.tgz", "fileCount": 10, "integrity": "sha512-uc6nVjvUjZzHa8fSl0ko684puuw0ujfCYn19v5tTu0DQ7tXx9jlZXzYw0aW7fmROxyez7BcbJloYLmXg723vVQ==", "signatures": [{"sig": "MEUCIQDlgbrvM+j8Y1ih7AjDWtBNYinoSKG0KBnAxkA5qjif5wIgbh1xYLIChhi7Ad5QbVS8yFkpZdte6ZjY8El4h6jyhXI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 144271, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcZZWuCRA9TVsSAnZWagAAhGIP/i6AX5trZv3wEyIC7uup\n7S5t7+HXeFr0D37E5c60pFZG00pkaDAEM6dd/BwhNxfTcWN+iZY5xH0KDP41\nsWs19GbODozWu/gDer3sqSfyj9FJxQHE601bInGaQd4z9G06k7Y5ET5/uC7S\nMzB1mReCK65cFV1fbGr1O3Sju5tsSH8NPuN4l8E3mhLNkY+T/ZuFRKZiDMb8\nQt8rIuSn9Fbt8FUaQ2Q1ffNKyrodCuIpcF6OUWndyqaAjcp1wKzkqxWt4JRE\nswzbFNN4TWYljUkqbNPuLrxeCC5Wycj3+CT2c0TzQ1sePSYVD3z5kwgA/7GV\nI3kDn1XH6BWa7yZIEmNjpruUt5Ii6lMssXfKRcs2HWJ8JrcDo6jlou2HLS+p\nZPZO8+kU9xwApMM+xdVx5CRURhsITiLeJbRANBquuAp/cq1GwOCAaxn+jiLo\nZyh5LQ1ZC1/bakbuFChXqy8DXtiDZkQgIbhib5J3//GpJW3O87zrWLDbWEbn\n4JaT833G3X5bZSw72HuI9cO1lwGcIqba809O8bNawgYEOm8efvT51MmI52Ab\nwFYzdC0fA14oeenFj9h0e4ddQ/IRTkC8ApIjfDQiVIVhq2zAWKO6EAb5SNXm\na0hKG9y+VHkiLCZGypYpEcxl9z2wr1gvDOJM8N3TisNT7UQc47Rbdz26F50X\nZFiU\r\n=nIHt\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.8.3": {"name": "@webassemblyjs/wasm-parser", "version": "1.8.3", "dependencies": {"@webassemblyjs/ast": "1.8.3", "@webassemblyjs/utf8": "1.8.3", "@webassemblyjs/leb128": "1.8.3", "@webassemblyjs/ieee754": "1.8.3", "@webassemblyjs/helper-api-error": "1.8.3", "@webassemblyjs/helper-wasm-bytecode": "1.8.3"}, "devDependencies": {"wabt": "1.0.0-nightly.20180421", "mamacro": "^0.0.3", "@webassemblyjs/wasm-gen": "1.8.3", "@webassemblyjs/wast-parser": "1.8.3", "@webassemblyjs/helper-buffer": "1.8.3", "@webassemblyjs/helper-test-framework": "1.8.3"}, "dist": {"shasum": "d12ed19d1b8e8667a7bee040d2245aaaf215340b", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.8.3.tgz", "fileCount": 10, "integrity": "sha512-NBI3SNNtRoy4T/KBsRZCAWUzE9lI94RH2nneLwa1KKIrt/2zzcTavWg6oY05ArCbb/PZDk3OUi63CD1RYtN65w==", "signatures": [{"sig": "MEYCIQC151z4RXSgROYwiJ8/018q5CRY2V+WsdS6XYHn+u4z4AIhAIrbhHNRIAk9otc7Dudn299Bt/kIm1fRzlnaFn1B+8SN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 144271, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcam7ICRA9TVsSAnZWagAAL0AP/irFCLULm591YrdyezfK\nHg0/7pcz1CCX2uMtAJKXcDu8bZhOAnPHJTopCOPJevKAxdEoysa35oPjupwl\nrP+OW+fNu0kq3s/DCov7owGhVJObHwl4Z90hV6W4c3fIac1exD+Oxl7zCfEj\nAe/O/UqxQyYVAMDm/Se+W5X9lU6q7xwP6KwypR/kEDyvDYvpJNSCUvGxnB8c\n1lF01PD1zvO+FoIAGXkTt3MPdS79tbogtZSgIIuWRQziq5XfaEd9Pb2IfOJi\n47JfhH5foHUG8uEnA4OTFI8tC98/RCn2AAhlN3fGjhTTCgLpqvVchiB11r1y\nVPsEa2S/4/11zIKiKl6/4PTwTYmbB93JGsz+j0C6OiW0qAoYIe78Du1eJ6GA\nY50YsYxK3hIwLudn4jj1rimCfdpsXWLRAgEmYAEycYmNjGBhD2P6hA21qTyf\n43NQ86YSmSo5KetB9iZRKKFYswwNeYNcVvz0rV8ikzz9Esaq337HwSagymnh\ngJcbuxuRtY5qkH70K55WQDK2OeiJhuIFIBtK2oSLgIigwnpLeQ1MM1kjtEJ9\nJ0NA5tteS0qa7kjSwlPHc3+lPiFUapdn/puu+ZULPLdp1dez9KaOsuyTbsQK\nPQxNDME+mien6iMWo3oDiEwxqtIIJAkaERfSuCH3JP3tAc53M0AP9azGN4Qc\nzNiT\r\n=2CSV\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.8.4": {"name": "@webassemblyjs/wasm-parser", "version": "1.8.4", "dependencies": {"@webassemblyjs/ast": "1.8.4", "@webassemblyjs/utf8": "1.8.4", "@webassemblyjs/leb128": "1.8.4", "@webassemblyjs/ieee754": "1.8.4", "@webassemblyjs/helper-api-error": "1.8.4", "@webassemblyjs/helper-wasm-bytecode": "1.8.4"}, "devDependencies": {"wabt": "1.0.0-nightly.20180421", "mamacro": "^0.0.3", "@webassemblyjs/wasm-gen": "1.8.4", "@webassemblyjs/wast-parser": "1.8.4", "@webassemblyjs/helper-buffer": "1.8.4", "@webassemblyjs/helper-test-framework": "1.8.4"}, "dist": {"shasum": "0ce95d23e07baae13e3e2bb5e58ca1e7b932c32c", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.8.4.tgz", "fileCount": 10, "integrity": "sha512-cMOKi0j06W9nwU6+Yr4o63xeY892kcc2HURRiUJx1EJbGY0ETy/GVjaIXpIQaKqSaO86nAQC+6ZV7TYl398Ayw==", "signatures": [{"sig": "MEQCIDaoZsiXo6VRpsQrgSt9JTxH8SHmBkL1ItSCrHBN1nonAiB2CL0v8Iec4brlZw6GJFK6fuYUgJBoMjnvOE8u6fpG6w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 144271, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcbDyQCRA9TVsSAnZWagAAhk8QAJ9USJQ30VoZa+61DD0j\n2rwJDUwSyJ3tu9QHFKoVrnvZD0iZHlJVCMSJiw2kkpQRsaT7Gxyu/RiBUbg4\n7HciqMtDd74kqsOvaJTUfgOt9dgrPfbKWSFjfwZ399eifbQGkVOSq0QeD4+w\niIjPVuYnP8S4R92dMpqIYD8vvDWUihdiestLJ2x9ouIWK5Y/aVT4XbVwDfph\nK3GNkLmsvh80A89xOkCz0suDf1pKKnbpoOLxoOrTFqr9C1y3yjp5NKFfO9Qp\nmv5UbLRcDvG2yBUJILAnPgttPpJ7XiNeOelMm3VDYgRqhS1y5l21ZwX6RGDg\nUP+EfP31KHL0SU78Q8XipGBRwr0H1P15XJRjg85QJtdYI00pZQLjPYgV09y1\nb5eTC8N9/pJGMgVXsxOv5qqu8AsQGl2a46G18W5sr5aNZxF2Lss8ps6nEyyK\nMaACX83RG64fcTd3JfXqQJ+my/ndUMKausPW6GxSqoyf4E3gsvpjbgNTKrCb\n1Y0KHBMq09sUmCfKXkEZswWtWnvGwcjJ9VZEUItIZdx9oNmikB/T4Pf8YpNM\nf+cHzyOlTau1kvIvQOWmQurYDGqciYWIDcCpGWOZqfBqjC4lWmNg4Ot2REdw\nLOcIQThKge6a80W9y0n70biO5DA/BNf6tQ7CIPsq+f4RFTWmFyufhQaYw9Jv\nJXuZ\r\n=HgJ7\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.8.5": {"name": "@webassemblyjs/wasm-parser", "version": "1.8.5", "dependencies": {"@webassemblyjs/ast": "1.8.5", "@webassemblyjs/utf8": "1.8.5", "@webassemblyjs/leb128": "1.8.5", "@webassemblyjs/ieee754": "1.8.5", "@webassemblyjs/helper-api-error": "1.8.5", "@webassemblyjs/helper-wasm-bytecode": "1.8.5"}, "devDependencies": {"wabt": "1.0.0-nightly.20180421", "mamacro": "^0.0.3", "@webassemblyjs/wasm-gen": "1.8.5", "@webassemblyjs/wast-parser": "1.8.5", "@webassemblyjs/helper-buffer": "1.8.5", "@webassemblyjs/helper-test-framework": "1.8.5"}, "dist": {"shasum": "21576f0ec88b91427357b8536383668ef7c66b8d", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.8.5.tgz", "fileCount": 10, "integrity": "sha512-pi0SYE9T6tfcMkthwcgCpL0cM9nRYr6/6fjgDtL6q/ZqKHdMWvxitRi5JcZ7RI4SNJJYnYNaWy5UUrHQy998lw==", "signatures": [{"sig": "MEUCIQD0YK6HLmR+wwLn1O2JuMT/w0XI/uUUtImRWhL4mjO4LAIgT/xCOvpeRjn3Xay4WGIdaKYjkextggTyOjzQsvXw6/g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 144271, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJccnU/CRA9TVsSAnZWagAAe+gP/2szlduybxk7WRWVJqWi\nbzQ9+zmcEkTdIh9saHLtOJufDPw9SEXS/X6xedpGFuxHn4M8dax3GSGtdEO3\nsgUjNkg9CpZFuwmHfzf9AI/0DGMi7YN0Ar6ZqSc5gKrrS61njF7ShN76FZNh\noSyBsg8W/T5AeDMf50D/lhTrg4k64flo9wnUC5mD1ELgEXwFcfJN2Vbbh1/Z\n/4kd/pT9ooj68WXi6wYYv9oN5X7KRZrAN4APi2l/5EFtgJVUd6LWZkg2/9yJ\ndxvlZ9cQYpZwQduhpI8d+dQoHgTq78DzFywU4WbZXwyvUMMmKkg2jT+NzI0b\n2P0c6leGls9+TLWevSUdhhXKYi2TA2oJM4wGtTJsQD58PKGjjtExyrqWxOpD\ng92f8S83+QViUJqlp0jhSiSW4cnkXACIHyeTmrMAz1bQSFCPQ93xYmf/86b3\nF9roPxcaV2KQOR7Ybe8/EvSX7aTf1sT/F8sS+U7dgY1Eos9zvincI2SEeqaZ\n7X3ou7wpcLxMlVswkkwhClyDj+guAGZiYQAaOn4ok3TR8TIjuoSqn8c+3nTj\n9DtyxVgI/KZ61CMF173lbpdZXdenT/a0/+iM7w2u4By2iFeFyjScTpdnAooC\n2xqkIaVqElwEhUFTuzobHeWnu/QNEUE4//9C92KGVCesFVw2a+0oU4LZZGJM\nCp9j\r\n=sgM1\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.9.0": {"name": "@webassemblyjs/wasm-parser", "version": "1.9.0", "dependencies": {"@webassemblyjs/ast": "1.9.0", "@webassemblyjs/utf8": "1.9.0", "@webassemblyjs/leb128": "1.9.0", "@webassemblyjs/ieee754": "1.9.0", "@webassemblyjs/helper-api-error": "1.9.0", "@webassemblyjs/helper-wasm-bytecode": "1.9.0"}, "devDependencies": {"wabt": "1.0.12", "mamacro": "^0.0.7", "@webassemblyjs/wasm-gen": "1.9.0", "@webassemblyjs/wast-parser": "1.9.0", "@webassemblyjs/helper-buffer": "1.9.0", "@webassemblyjs/helper-wasm-bytecode": "1.7.7", "@webassemblyjs/helper-test-framework": "1.9.0"}, "dist": {"shasum": "9d48e44826df4a6598294aa6c87469d642fff65e", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.9.0.tgz", "fileCount": 9, "integrity": "sha512-9+wkMowR2AmdSWQzsPEjFU7njh8HTO5MqO8vjwEHuM+AMHioNqSBONRdr0NQQ3dVQrzp0s8lTcYqzUdb7YgELA==", "signatures": [{"sig": "MEQCIHt9OMX9xJ+JHwAqlzA1NAMzb6dBcWcHyU2e+4bzHy1bAiAfIdPxcDXZmvaZIdo9CcfZcR1RzfmFltwQSaHJqRA0cg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 122477, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeNgevCRA9TVsSAnZWagAAd+gP/2vxfJjJ6yUkHA12SVdu\nZ1NTLpfZvY2THmev0QLQ3a+vqvEcpkv/uOiioPNVAkULYg53RvgE7DiElJ2Q\nqBJbjq7ANgxMlIyjVDxg74kK6WXeKXpOV7RtpV4iQMal4R2Lj96c2zU/6Fxb\nmjDf6/1apbJ53bw0HlBljN01KaMt/WkTzemnPP1L+x5FuEAHXCXuK7F6oXcl\nulVJq6x5RxT4DULghBDvuQvbIgsgNFB5HkgDmphj9iO2yss+BaTJ5yTy7LXA\n13rBlu7XwBurLfCP1GDDaTLU/uczqDrMCHgfZwiGq6DkpVHU0JdGGM0qFF8/\nKRjlFpDnHWjFLkhc/O1OG+/iRqrcFGDiveTpLT5WR4P/J8HwU6EO84OR604d\nD1EvgX4HKlOt8XfQOc7MoXkYNjxvEZxl0eGcUsXZuI+PUVyLuOR+tQgWD8MH\nC+HRcJ8K7HiBLVhNg+M1aqCW8fX6JUDWUdsY3ScUoIAY56nRFElVc3CtmUX0\nF8JxN5g/KPk4OIVNB34o3afJOTBhgTeazp8nieCUsAt5l6h8BsWOwpoN7+bm\nQAh0ULbKc1MaWLAnm1q/hZjZ92rdTRTBrQsNbU0ehObuZYfrFqcw9E5v6de7\nxn+p77EiyF3NiGEea4JN2mI5K/dD1LOJ6lNjoxWTqAZAAcPWlFWL4htV2B0a\noE79\r\n=4CF9\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.9.1": {"name": "@webassemblyjs/wasm-parser", "version": "1.9.1", "dependencies": {"@webassemblyjs/ast": "1.9.1", "@webassemblyjs/utf8": "1.9.1", "@webassemblyjs/leb128": "1.9.1", "@webassemblyjs/ieee754": "1.9.1", "@webassemblyjs/helper-api-error": "1.9.1", "@webassemblyjs/helper-wasm-bytecode": "1.9.1"}, "devDependencies": {"wabt": "1.0.12", "mamacro": "^0.0.7", "@webassemblyjs/wasm-gen": "1.9.1", "@webassemblyjs/wast-parser": "1.9.1", "@webassemblyjs/helper-buffer": "1.9.1", "@webassemblyjs/helper-wasm-bytecode": "1.7.7", "@webassemblyjs/helper-test-framework": "1.9.1"}, "dist": {"shasum": "5e8352a246d3f605312c8e414f7990de55aaedfa", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.9.1.tgz", "fileCount": 9, "integrity": "sha512-ImM4N2T1MEIond0MyE3rXvStVxEmivQrDKf/ggfh5pP6EHu3lL/YTAoSrR7shrbKNPpeKpGesW1LIK/L4kqduw==", "signatures": [{"sig": "MEUCIQDKOg7H2jyX/L9z0WrrFvaEX0yyw2e5TK+su2+68mar/wIgawRpF/AV+P8wyQTX3QI9F+ax+ADg1JEdXmQTGf+7SUE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 122477, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfeNouCRA9TVsSAnZWagAAGrsP/icZ2gfsMWd9HKbWMo6W\nDd1DpHBjqb/rXW9S76oyOzt0CMLCxRI+f7mH1Uu+J5lFlnLGQvy20m6wIWgg\nLoqNCkQibnvj97RxFEFIFIpkhZ7F4ArXbI66ZVRkOlv5LNbE/Gs//+8vvAiM\nK3OnQ/9ffJCiWzZb7qlEuN4Cep+Q+UGoU32bN+HxbzE84Nmyy9/mypoQ0/v7\nUxbEKvMynqBBYH5QoJKD1pWt8HBjNW334J7HFCDLAdbSvwhTrHuRWsEnifhK\ny9oFNPV4aJqvhGORPLqgrQvL0T+0oWSYLa2nUShbtTOLVTX/nHV1/y0LWv7h\nXAJ/h1pHhFu+irT3I9wmyhJTaK2fS3BFP1jR8LD1edSghoFXMGdeI673V0KF\n+XUIkWV45zOQNr0g3s4tlPgTBwXqSMWN8cVLR5YqoHO1leAojnGScMJQ8Hgu\n9kJUVjucZVvKLk+JLvRFZAAsmfdJtxlxMBxOCR3E7Gx+kzOIzCAnxCv1hTMr\nDewT030j3FmnCbxc2d9KrNZHFM63KbTo8z6stbfb+0OVBjsQ4I6yJFas3jX9\nfKNM72n6BOomE2JvdelG5sLDhTsXxUPqDEylvXmJX17xoRAwWK9ap7cGMBN5\nz7TQ7L1ST8pD7238i4KxE0eFeFtfRkvHhmzPIRxiGe1uwjUXuuWtzSZwwGGi\n0cNo\r\n=hAPU\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.10.0": {"name": "@webassemblyjs/wasm-parser", "version": "1.10.0", "dependencies": {"@webassemblyjs/ast": "1.10.0", "@webassemblyjs/utf8": "1.10.0", "@webassemblyjs/leb128": "1.10.0", "@webassemblyjs/ieee754": "1.10.0", "@webassemblyjs/helper-api-error": "1.10.0", "@webassemblyjs/helper-wasm-bytecode": "1.10.0"}, "devDependencies": {"wabt": "1.0.12", "mamacro": "^0.0.7", "@webassemblyjs/wasm-gen": "1.10.0", "@webassemblyjs/wast-parser": "1.10.0", "@webassemblyjs/helper-buffer": "1.10.0", "@webassemblyjs/helper-wasm-bytecode": "1.7.7", "@webassemblyjs/helper-test-framework": "1.10.0"}, "dist": {"shasum": "a48b5fbeef3c986a0ca93624534b629382293c9f", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.10.0.tgz", "fileCount": 9, "integrity": "sha512-1wONqjNPh2ZKZKRr1AxvaPcqCfhAD7K/112lWra4FBpjr+ouWMFdW+AngEetl3aq9XUnyDmdGv9e1sPIzdiT/w==", "signatures": [{"sig": "MEUCIGyUNc3AnTfuieKThZS/7bQsTcwHPIyynnWC2I1Bc8MiAiEAhIPraEzcS1VMOvq7tzYjfQpdMWgpmqMfgxFfqI5+eoA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 122488, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf9zRYCRA9TVsSAnZWagAAQxoQAKMhtm/1FPhg48LGe+3e\nvODoz7g+r5OqUqps8BS6i/81108B6XsJv0Al8GQWgO2hyi5oX8Dq+zBwGuWj\n18KbtTrUI2c12pmYqeWncYBJ+zSLt/HKimS7tuzkFKQzhWmJQBChR8wQxzjO\nWp/fNLmtAdyqE4Vpk8LWUjL1eV4prVtYO2JvISY5W6OZGYLekYEJHgLiMGbh\nVT4RgOf5DiqKZ3Ip4RZdCAWAPoBr37OICOnHLUBOIeWOZlzklz4z9B72pocF\n6p8TvBb4MuI1EkNMUaG6GBzkrATG/2vz632pZi9McNv2MD09rxFvZ8HoqO+Q\nEdEIsidn8dudw5xmMK5vtfhDquuU9NGMcajLs8qAC8g+UWB4UhLNd6GygbZF\nzTOHPCqnJPvWtaA62826ctMcUg1AspcLxExq8pqQeMdvaWQdFEcbLCHDjser\nHhEvnzC+Icg2lU9t+UTuTPHJT1czTU3jRB0KaHTRYlrsFY0w9fy0FcWW3Xmj\nHJ0tbYmY8OKDEt+AIW9fiP+r9V0Tmh3F0EB3C3h03urXau5auFGCgmgbURvP\nybuk87GvdCvEwOImg9R+4V3i6UVmdrUVAq6Lqbd0IPvay/RGNUCV5WBoNTmo\nrqX5gpzeBcdPLkh+syc9JN1d6CQygFfkZwBAd/UNh77euBhQwf8c9oDlGKHC\niFGT\r\n=4KX1\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.10.1": {"name": "@webassemblyjs/wasm-parser", "version": "1.10.1", "dependencies": {"@webassemblyjs/ast": "1.10.1", "@webassemblyjs/utf8": "1.10.1", "@webassemblyjs/leb128": "1.10.1", "@webassemblyjs/ieee754": "1.10.1", "@webassemblyjs/helper-api-error": "1.10.1", "@webassemblyjs/helper-wasm-bytecode": "1.10.1"}, "devDependencies": {"wabt": "1.0.12", "mamacro": "^0.0.7", "@webassemblyjs/wasm-gen": "1.10.1", "@webassemblyjs/wast-parser": "1.10.1", "@webassemblyjs/helper-buffer": "1.10.1", "@webassemblyjs/helper-wasm-bytecode": "1.7.7", "@webassemblyjs/helper-test-framework": "1.10.1"}, "dist": {"shasum": "9323b80583a0b235f131f3d27635908586c84cf5", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.10.1.tgz", "fileCount": 9, "integrity": "sha512-ItEiV04oJp/Zu/d+b2YUfMf4BMyRciVsQ69M2jw6YB35jwSIG+KkUc+6hk+KlN513tjL8xYUzrw/ALO2pTB6HA==", "signatures": [{"sig": "MEYCIQDnw/hE65ZEravxvUii5eX4QircZ5JezJpNqEV4hRLdJQIhALecIYRp1+m7Gs14k7czh8BvVpQVL/gwtA/6jsqzQTAj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 122488, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf9zqnCRA9TVsSAnZWagAAg5YP/294HFBVAMnjNMjm+ryI\n7BM3Zdv6MAPO/nnyG7OaPuL+yCvpd22mu8Hn3lf5AHO/Gdlt9YPS7TZAoK5U\nKNi2V7sigkHzCnijra6bxgmsWxgIbLBPucCjOaBr2K0tU4gJ5+iJLJcftJXK\nt0cywBl0iArKvsi5QvFm7fuO2gFrM6MEbWJekUrsKtEx4g1XY2xMsZAyeHOV\nVkXcn6zo+UPdNhGJZPAA0oEBzf7jmK2PiG1+hnIY4H4TGUVeWI89QFQIBWYb\n/J5BxMpg43xoWN0jgr7nwItk3aPRYGbWHfSC69/ahtx48lu526DZmKlTsJqH\nHabqaHNaGKt3UJBdxhRRDu/r+4s7LEulD6txqo4s2xd2PcB9hitpDdIijz6m\neHPA6kbbb6qhX7FhtP+H+yOUEELOeZkXrhE8e1nbqaDXpIVtCLTDIiqy+M0f\n17CMheOtSOCJqXSyfa7gzCAaoSX8yl2cvOkkAg7SIF+hW5MKVPLnO9Rlo9Gg\n/AD9bC7YOC873O7brywzX9PHubxFRtiug4RlSuwag5quqV/uaxbrJeaLzObB\n4mZBgnMLxljzLV3uXZZKPl2wkkNITAirtaILPL48E+ABSm0A9gVdae/8DYzx\ndp3tZRj1GbJpQZOzxSAsKdNJTZI7Pk7ynfUdfLwhr4FA7X0A1yUJ/k5e4qE3\ny69J\r\n=1Y0e\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.11.0": {"name": "@webassemblyjs/wasm-parser", "version": "1.11.0", "dependencies": {"@webassemblyjs/ast": "1.11.0", "@webassemblyjs/utf8": "1.11.0", "@webassemblyjs/leb128": "1.11.0", "@webassemblyjs/ieee754": "1.11.0", "@webassemblyjs/helper-api-error": "1.11.0", "@webassemblyjs/helper-wasm-bytecode": "1.11.0"}, "devDependencies": {"wabt": "1.0.12", "mamacro": "^0.0.7", "@webassemblyjs/wasm-gen": "1.11.0", "@webassemblyjs/wast-parser": "1.11.0", "@webassemblyjs/helper-buffer": "1.11.0", "@webassemblyjs/helper-wasm-bytecode": "1.7.7", "@webassemblyjs/helper-test-framework": "1.11.0"}, "dist": {"shasum": "3e680b8830d5b13d1ec86cc42f38f3d4a7700754", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.11.0.tgz", "fileCount": 9, "integrity": "sha512-6L285Sgu9gphrcpDXINvm0M9BskznnzJTE7gYkjDbxET28shDqp27wpruyx3C2S/dvEwiigBwLA1cz7lNUi0kw==", "signatures": [{"sig": "MEYCIQD/mHDqwwSkF7eoYfmH/K9CTD2PNxvcdE3tXuHwQJWzNQIhALR/5S6WzPlF3qVeFztaiEBV0amv454Tr1xfRYUM8S1X", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 123458, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf907jCRA9TVsSAnZWagAAjMwP/iSUtP+0l6pm9M3fLskK\nChW2mnF0lo2ja4kIa9qEuZUT5eK4V8KKzwkOrC1EnPpwFIpfiwqoqBogECfq\nUYx9PFJLMZU0aSVVy52wyVUM84/gzDtRB08FvuWyGb7YEi39mocp7jxTbjMS\nS9yEceNPriPCiAiMJWQoK8gcSs0KaIwLViZVC9Th+agyeLwCxBQ2AOJSWJqt\nBmarhXpfTNTfjy3Md7jSA7tC+6YZK/x9VfrD27wYIXzITM6QrFvsMDxNnyKb\nmj4buEt2hLKJllhDdJjjW+YlzS/pnI3cFfeXvM6NFxwWFSaEyRCVWBwOKPim\ni5aAVpYgL6y6T7Qt3BaCP65gSimqehsoYjTCtJTYQi/XmsSNpXMo/mefomrS\nRFb5+C173/8iYRqcPXaYjyHgdPrC5Tg/Ah/FZUHPvd7mXr9vyRD2TBxevqf2\ncuQ1lsjJDvQAcWrgbrGVpMEvQcNRmfeAtO1vKJjp1VtlM2jqzV5bY7CzfASB\nVmhrw9LGdELreMoH7KFlVbF4n+7Qm8050Hy6nQpt4acdR2afWev4F4DYw5FN\nqP3w7okwsjRe0oIOsg9bRIYXnZoJr+F8kpZInLiQyYHOFXTYyQi3v/ZPEKxq\nQFbDo/xZKVr1WaBQQknq83ur4t6955iKZlnMFP2YUtp8dX1tojC7MFKa4AfZ\nhIxM\r\n=wKQn\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.11.1": {"name": "@webassemblyjs/wasm-parser", "version": "1.11.1", "dependencies": {"@webassemblyjs/ast": "1.11.1", "@webassemblyjs/utf8": "1.11.1", "@webassemblyjs/leb128": "1.11.1", "@webassemblyjs/ieee754": "1.11.1", "@webassemblyjs/helper-api-error": "1.11.1", "@webassemblyjs/helper-wasm-bytecode": "1.11.1"}, "devDependencies": {"wabt": "1.0.12", "mamacro": "^0.0.7", "@webassemblyjs/wasm-gen": "1.11.1", "@webassemblyjs/wast-parser": "1.11.1", "@webassemblyjs/helper-buffer": "1.11.1", "@webassemblyjs/helper-wasm-bytecode": "1.7.7", "@webassemblyjs/helper-test-framework": "1.11.1"}, "dist": {"shasum": "86ca734534f417e9bd3c67c7a1c75d8be41fb199", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.11.1.tgz", "fileCount": 9, "integrity": "sha512-rrBujw+dJu32gYB7/Lup6UhdkPx9S9SnobZzRVL7VcBH9Bt9bCBLEuX/YXOOtBsOZ4NQrRykKhffRWHvigQvOA==", "signatures": [{"sig": "MEUCIAS8RIiN/V8en/PHtKnciE6Hr7pxEXA86wcp7Pa1uVa9AiEA64qmp41Jo0y1Jv4BvVkmAk5++Jgd97aiz0UmqHm5vzY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 123546, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg4sE7CRA9TVsSAnZWagAAFVAP/R3lYAxflLW2GF+yFM8i\ngIgBXR8ClQWj8nJ0ayyORrO1qU1hls3VF064k5jBAV7bliWSiyj+2vQoeeiA\nE7eJBipUsSZ8zPiMArQD0NX1NDA5xSQfCVTaazvu9iPoiHYKcNaDUiEBmTxx\nlxjmLO8pa1ahOSnBUkmWXBE2Ed9XVReFlCA7i++wu8eVhwSPByndM7gyVuqA\nE1B01S0ERz2kbKB2Sl0rK/C5uxlW9/VnQBm0zh42NIQqru55a5r6M78ZeZgu\nd4o5PP4Cc0LR2PZFSWNRlUzs6bbO9MIYDDQ0VL+gzE36YNzMEIdAHU8zXYAg\nSa8awN8MybzeCDpDJ19fiog1XdEK+1Z+VDBjmANePIUeeI1A283quC8h0pD8\nz4F6aA1cmamUf4u1qyn4uNkkQcqHoCi0qblIi20Kwjq98GMWnMZLISgoE/g2\nSZwTffOE5M6YTvCck9miXhl0CFqzIQgd6COtcGcLnMoEJqSrq1we2cJiPvOy\nnqcwhuvStY84V7tB7rH6CJ9NOQZPcdQDrWVTZg8AM37bsE5oZKIL1O3khjCn\nJXZxUNaMkXACLzgk6B4wKkX0gOTGAwiJ6HjNz+NmlXmrJtkHJOig81IdjvY3\nGzYfkvcCmUpYXw+BrzbYzLQQo5uNUIWXMJKBCAaocAwtX+gEE9/5hPGtf+hx\nhn8U\r\n=E7u7\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.11.3": {"name": "@webassemblyjs/wasm-parser", "version": "1.11.3", "dependencies": {"@webassemblyjs/ast": "1.11.3", "@webassemblyjs/utf8": "1.11.3", "@webassemblyjs/leb128": "1.11.3", "@webassemblyjs/ieee754": "1.11.3", "@webassemblyjs/helper-api-error": "1.11.3", "@webassemblyjs/helper-wasm-bytecode": "1.11.3"}, "devDependencies": {"wabt": "1.0.12", "mamacro": "^0.0.7", "@webassemblyjs/wasm-gen": "1.11.3", "@webassemblyjs/wast-parser": "1.11.3", "@webassemblyjs/helper-buffer": "1.11.3", "@webassemblyjs/helper-wasm-bytecode": "1.7.7", "@webassemblyjs/helper-test-framework": "1.11.3"}, "dist": {"shasum": "1535db423fe1b4279332a4edc251e70003e6d7a9", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.11.3.tgz", "fileCount": 9, "integrity": "sha512-vbH9d6t3mjM8U/2XMpljXGT/MxFvwscO4Pjy6JczxssR8gMzdbRAAy5SAWrNrp1PvMqDcJz9aom58/9/8daJMg==", "signatures": [{"sig": "MEQCIBhjPp2H5zUAOuARK1NrEQxxD/ZI3l+wEW1XxbO7ofXAAiBGI7/rjt8K7S/E3Xj6poyqMaIgAFZ0iTlqVWs/rFDP6g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 128954, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiwDGYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp7AA//U5Fz9+djfrsjxsAsuKlaUbH4TClSQOuAka1JUNR9/mb6Hz47\r\nGDuqFSxEqT66e72I2vgQGY3B8F//estEECuNAPgF8gVtWf7fddL0BDW2e5PQ\r\ndAU1III4DLwCWXMNFW5BbUdchiSsNmX62+9G4btlcgF8lcpd/h7Ik+4oi44b\r\nFfxbBFSp4JHlU4ON4F9JZT+ceiEMGBkpEqM28JeWwUAB3hT6lN/itCvJqp4f\r\nJWx+o+7pDc7e8/4MlXSd7uhbGVftiplYUbHyv3Dene+X/mNRCE2ivGqJBYug\r\nGBg2///RQUA96P2AL8VqMBD5LG5O12tv9OJUgE8NlgIq91iZ8wmSnjjALilc\r\n2u2HI31UH2AcDGv1w/PzIUsCQ2WuPtz2XW8AwcrN6u7AgNzIl5TmXgRSzKmm\r\nGiMonVmL+N+JEgJ3vKFphvZFQxaEUmHqjCgMn4rAUtyx6TuCgjwhn2o2qUto\r\nUD9QczNr66mldSBh5+aS+qhnIHjdM3G7Pvi8X+2kUDX+3RxDHI/XvUpZa0bg\r\nEMiRe+w74QMCoMJ5bDviLYwo5CHrmrFclib3FFODnV3MjvETuqLtyx+drNcC\r\n1xhNIlb81DtY2mBU5HJSMXqHGyWbvoeB3oZOVdK7Shg76EnQZfh2465zzfYp\r\nEOlbUiGRzNXV6mFWY4YU9Ru97J3qKqww8/g=\r\n=7TI8\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.11.5": {"name": "@webassemblyjs/wasm-parser", "version": "1.11.5", "dependencies": {"@webassemblyjs/ast": "1.11.5", "@webassemblyjs/utf8": "1.11.5", "@webassemblyjs/leb128": "1.11.5", "@webassemblyjs/ieee754": "1.11.5", "@webassemblyjs/helper-api-error": "1.11.5", "@webassemblyjs/helper-wasm-bytecode": "1.11.5"}, "devDependencies": {"wabt": "1.0.12", "mamacro": "^0.0.7", "@webassemblyjs/wasm-gen": "1.11.5", "@webassemblyjs/wast-parser": "1.11.5", "@webassemblyjs/helper-buffer": "1.11.5", "@webassemblyjs/helper-wasm-bytecode": "1.7.7", "@webassemblyjs/helper-test-framework": "1.11.5"}, "dist": {"shasum": "7ba0697ca74c860ea13e3ba226b29617046982e2", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.11.5.tgz", "fileCount": 5, "integrity": "sha512-SVXUIwsLQlc8srSD7jejsfTU83g7pIGr2YYNb9oHdtldSxaOhvA5xwvIiWIfcX8PlSakgqMXsLpLfbbJ4cBYew==", "signatures": [{"sig": "MEUCIQCVveTZoniALDKo0/FIewEDqzF1Az8g7yTfZuKUnIV2DQIgcY/q3vFT151UHCygPkroMCCawiIDJh75WSo7dnwfGCk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67041, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkO6B7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpr6g/8CsvnFX3JeP/8MXU63iBhgONmL7RM8OzNVS+0PN8KtXo67rfz\r\nvxrWwnpA4drxXVUMbMuFiX+OeaDvHlCTddwjBxahCrw92fhau/HlAbA4Cz9v\r\nwdm3QN9HkRSIMAMVYHRZ2DKiuYl87Y87n2n+kB480AZjTT3/cHCMPNyW7W9U\r\nCv62objTBPFLVbN9Q/RibsoVVXDKzFt/HGv9ZONP4U3cz1OMNGMGJypbozCC\r\nyRMWAFP9HEXBt6viiuDJnQN2nCr37UohiBUE8FlSoHtpY2pqb+uQh9XVTELG\r\nYBgzZjMJ4LC8AdU0no/yCv3jKzaJT3+sI4WdeDy5NHWjzT6AXHR4ax8uZ4np\r\nEgY3amU+uXGT37LQOBFdOI/7egqCKd6xbSUbGHtjPRezpwZqdrfhWjvfDc3u\r\nIMiUb8kaUWb3oO362vyIv9f3M6oKvBjkSix6whep/nnuidnIsf2OThD/+fb+\r\nxHcnsYs/4aV7f5+WCCIx/VjdWD1u8DI6Y5lJesJX/7rVxpM1Gyt+xMMca9Jx\r\nla/T/QtLlgI5rXTJ1b/z5TGMV/0xCwgze2JRkQ7H1DlPGclnDL/V1W2ycwSr\r\nrCaYmQpJ8vy4zW+g3gTwMT7lDKhI8fiVNejcYcybptQu6IMqHZ82VURUPaR+\r\nL/ULnB4Fi0qDKQRgNPkOCT7pNMwfMWSJZtM=\r\n=FZ6f\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.11.6": {"name": "@webassemblyjs/wasm-parser", "version": "1.11.6", "dependencies": {"@webassemblyjs/ast": "1.11.6", "@webassemblyjs/utf8": "1.11.6", "@webassemblyjs/leb128": "1.11.6", "@webassemblyjs/ieee754": "1.11.6", "@webassemblyjs/helper-api-error": "1.11.6", "@webassemblyjs/helper-wasm-bytecode": "1.11.6"}, "devDependencies": {"wabt": "1.0.12", "mamacro": "^0.0.7", "@webassemblyjs/wasm-gen": "1.11.6", "@webassemblyjs/wast-parser": "1.11.6", "@webassemblyjs/helper-buffer": "1.11.6", "@webassemblyjs/helper-wasm-bytecode": "1.7.7", "@webassemblyjs/helper-test-framework": "1.11.6"}, "dist": {"shasum": "bb85378c527df824004812bbdb784eea539174a1", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.11.6.tgz", "fileCount": 5, "integrity": "sha512-6ZwPeGzMJM3Dqp3hCsLgESxBGtT/OeCvCZ4TA1JUPYgmhAx38tTPR9JaKy0S5H3evQpO/h2uWs2j6Yc/fjkpTQ==", "signatures": [{"sig": "MEUCIGHfCQloFOBJg3O6Fq/Sy16TfPPzF/kpcIoh7DwbIrw7AiEAqZHrhzkMzxUoCq2479lsLTiC++XOb1ZPBQIAN0IV+VE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67041}}, "1.12.1": {"name": "@webassemblyjs/wasm-parser", "version": "1.12.1", "dependencies": {"@webassemblyjs/ast": "1.12.1", "@webassemblyjs/utf8": "1.11.6", "@webassemblyjs/leb128": "1.11.6", "@webassemblyjs/ieee754": "1.11.6", "@webassemblyjs/helper-api-error": "1.11.6", "@webassemblyjs/helper-wasm-bytecode": "1.11.6"}, "devDependencies": {"wabt": "1.0.12", "mamacro": "^0.0.7", "@webassemblyjs/wasm-gen": "1.12.1", "@webassemblyjs/wast-parser": "1.12.1", "@webassemblyjs/helper-buffer": "1.12.1", "@webassemblyjs/helper-wasm-bytecode": "1.7.7", "@webassemblyjs/helper-test-framework": "1.12.1"}, "dist": {"shasum": "c47acb90e6f083391e3fa61d113650eea1e95937", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.12.1.tgz", "fileCount": 9, "integrity": "sha512-xikIi7c2FHXysxXe3COrVUPSheuBtpcfhbpFj4gmu7KRLYOzANztwUU0IbsqvMqzuNK2+glRGWCEqZo1WCLyAQ==", "signatures": [{"sig": "MEQCICiRy+7F1KfKi3BbdKrBTDtFnKoj8i7KgFjjFKbZHTnZAiApsKz1DY4ylw1DUBmcv8HRZiUZZMazefBVhSogHohLwA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 128990}}, "1.13.1": {"name": "@webassemblyjs/wasm-parser", "version": "1.13.1", "dependencies": {"@webassemblyjs/ast": "1.13.1", "@webassemblyjs/utf8": "1.12.1", "@webassemblyjs/leb128": "1.12.1", "@webassemblyjs/ieee754": "1.12.1", "@webassemblyjs/helper-api-error": "1.12.1", "@webassemblyjs/helper-wasm-bytecode": "1.12.1"}, "devDependencies": {"wabt": "1.0.12", "mamacro": "^0.0.7", "@webassemblyjs/wasm-gen": "1.13.1", "@webassemblyjs/wast-parser": "1.13.1", "@webassemblyjs/helper-buffer": "1.13.1", "@webassemblyjs/helper-wasm-bytecode": "1.7.7", "@webassemblyjs/helper-test-framework": "1.13.1"}, "dist": {"shasum": "42c20ec9a340865c3ba4fea8a19566afda90283e", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.13.1.tgz", "fileCount": 9, "integrity": "sha512-8SPOcbqSb7vXHG+B0PTsJrvT/HilwV3WkJgxw34lmhWvO+7qM9xBTd9u4dn1Lb86WHpKswT5XwF277uBTHFikg==", "signatures": [{"sig": "MEUCIHw9CslXKIWd6HIoQnurug81iLvUcRzF0iplxC9fUPGGAiEA6tGrD6QIE/T4NpEZMu/+wV4QZHvprd3D2zRBrL/V1UA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 133514}}, "1.13.2": {"name": "@webassemblyjs/wasm-parser", "version": "1.13.2", "dependencies": {"@webassemblyjs/ast": "1.13.2", "@webassemblyjs/utf8": "1.13.2", "@webassemblyjs/leb128": "1.13.2", "@webassemblyjs/ieee754": "1.13.2", "@webassemblyjs/helper-api-error": "1.13.2", "@webassemblyjs/helper-wasm-bytecode": "1.13.2"}, "devDependencies": {"wabt": "1.0.12", "mamacro": "^0.0.7", "@webassemblyjs/wasm-gen": "1.13.2", "@webassemblyjs/wast-parser": "1.13.2", "@webassemblyjs/helper-buffer": "1.13.2", "@webassemblyjs/helper-wasm-bytecode": "1.7.7", "@webassemblyjs/helper-test-framework": "1.13.2"}, "dist": {"shasum": "7d8d4a96018e4357a6af1e2b163b47f883c23efe", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.13.2.tgz", "fileCount": 9, "integrity": "sha512-Vh41LN7BGCt7tzuMVDfbkjKgc5x4LUD9tzbFqu41sfliljSKNXO/sSGuK8n6yr3Fn+j3WUjI8qV5Vcfoz2YpWQ==", "signatures": [{"sig": "MEQCIGbPe+ms/KJdVW0uyik4UjeFupCsrIdP6PVhQk/e25vLAiAWJM/RgP8H9p2WoeAx6QpHq8wPJ32ZhdrM9HuV8r33kw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 137454}}, "1.14.1": {"name": "@webassemblyjs/wasm-parser", "version": "1.14.1", "dependencies": {"@webassemblyjs/ast": "1.14.1", "@webassemblyjs/helper-api-error": "1.13.2", "@webassemblyjs/helper-wasm-bytecode": "1.13.2", "@webassemblyjs/ieee754": "1.13.2", "@webassemblyjs/leb128": "1.13.2", "@webassemblyjs/utf8": "1.13.2"}, "devDependencies": {"@webassemblyjs/helper-buffer": "1.14.1", "@webassemblyjs/helper-test-framework": "1.14.1", "@webassemblyjs/helper-wasm-bytecode": "1.13.2", "@webassemblyjs/wasm-gen": "1.14.1", "@webassemblyjs/wast-parser": "1.14.1", "mamacro": "^0.0.7", "wabt": "1.0.12"}, "dist": {"integrity": "sha512-JLBl+KZ0R5qB7mCnud/yyX08jWFw5MsoalJ1pQ4EdFlgj9VdXKGuENGsiCIjegI1W7p91rUlcB/LB5yRJKNTcQ==", "shasum": "b3e13f1893605ca78b52c68e54cf6a865f90b9fb", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.14.1.tgz", "fileCount": 9, "unpackedSize": 137455, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEqMGbgHH7gU9lWPTN1PIhggEkmylfj3MmLN0xkThKsVAiBW5y+A37fOJEEcE7B7GhwpvCy7k435uNAr0HQCZTQgiQ=="}]}}}, "modified": "2024-11-06T21:53:38.745Z"}