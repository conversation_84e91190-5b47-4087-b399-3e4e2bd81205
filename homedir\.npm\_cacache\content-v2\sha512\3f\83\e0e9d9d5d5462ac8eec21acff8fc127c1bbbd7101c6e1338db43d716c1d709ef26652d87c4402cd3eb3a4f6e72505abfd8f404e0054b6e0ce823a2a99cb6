{"_id": "nth-check", "_rev": "13-095741c8996f98df786b75d474491a46", "name": "nth-check", "description": "Parses and compiles CSS nth-checks to highly optimized functions.", "dist-tags": {"latest": "2.1.1"}, "versions": {"1.0.0": {"name": "nth-check", "version": "1.0.0", "description": "performant nth-check parser & compiler", "main": "index.js", "scripts": {"test": "node test"}, "repository": {"type": "git", "url": "https://github.com/fb55/nth-check"}, "keywords": ["nth-child", "nth", "css"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "bugs": {"url": "https://github.com/fb55/nth-check/issues"}, "homepage": "https://github.com/fb55/nth-check", "dependencies": {"boolbase": "~1.0.0"}, "_id": "nth-check@1.0.0", "dist": {"shasum": "02fc1277aa2bf8e6083be456104d6a646101a49d", "tarball": "https://registry.npmjs.org/nth-check/-/nth-check-1.0.0.tgz", "integrity": "sha512-BVmHliGk1RUWazUSRvLcFrHZGANkckB5xnDvUn3LDeZO3jp9QU77dgeUTtPbvQChg1Nf+tRiwcrQ9JZMXDZqiw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCs2MbWC0p0h4IKWykkDQWZd5x4hof0MWE+sE7MhX4fqQIhAOaJp2xmgG100NQ6UwYUC5i9Tw9iMFo+jcK/qD7S6J+S"}]}, "_from": ".", "_npmVersion": "1.4.2", "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "directories": {}}, "1.0.1": {"name": "nth-check", "version": "1.0.1", "description": "performant nth-check parser & compiler", "main": "index.js", "scripts": {"test": "node test"}, "repository": {"type": "git", "url": "https://github.com/fb55/nth-check"}, "keywords": ["nth-child", "nth", "css"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "bugs": {"url": "https://github.com/fb55/nth-check/issues"}, "homepage": "https://github.com/fb55/nth-check", "dependencies": {"boolbase": "~1.0.0"}, "gitHead": "257338e5bbd53228236abd4cc09539b66b27dd11", "_id": "nth-check@1.0.1", "_shasum": "9929acdf628fc2c41098deab82ac580cf149aae4", "_from": ".", "_npmVersion": "2.6.1", "_nodeVersion": "1.5.1", "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "dist": {"shasum": "9929acdf628fc2c41098deab82ac580cf149aae4", "tarball": "https://registry.npmjs.org/nth-check/-/nth-check-1.0.1.tgz", "integrity": "sha512-lTD1gl0OIJzVFadNdZ1Tc+Z1vqRlYr6syYgCvhtoOxl5T3c8mufKaJ0XEiaJ+HQSYbCaSH/NP5m5p+so/nTOOA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDk+0+pulkBJJSuI88AHJlWOYneGy5DqhMkeOeE6FInBAIgY7ZyJkv6bs+T6PpK52LCVIFST7rA11b/BUbqifh0O38="}]}, "directories": {}}, "1.0.2": {"name": "nth-check", "version": "1.0.2", "description": "performant nth-check parser & compiler", "main": "index.js", "scripts": {"test": "node test"}, "repository": {"type": "git", "url": "git+https://github.com/fb55/nth-check.git"}, "keywords": ["nth-child", "nth", "css"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "bugs": {"url": "https://github.com/fb55/nth-check/issues"}, "homepage": "https://github.com/fb55/nth-check", "dependencies": {"boolbase": "~1.0.0"}, "gitHead": "03a02587bbd126fafc3d2331ffef6ea5cb2f9b66", "_id": "nth-check@1.0.2", "_npmVersion": "6.4.1", "_nodeVersion": "10.11.0", "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-WeBOdju8SnzPN5vTUJYxYUxLeXpCaVP5i5e0LF8fg7WORF2Wd7wFX/pk0tYZk7s8T+J7VLy0Da6J1+wCT0AtHg==", "shasum": "b2bd295c37e3dd58a3bf0700376663ba4d9cf05c", "tarball": "https://registry.npmjs.org/nth-check/-/nth-check-1.0.2.tgz", "fileCount": 6, "unpackedSize": 5538, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbzPm3CRA9TVsSAnZWagAAhM4P/iS5h2NgtCybL1u9veDR\nECfbFBz60HsEQyqJtx3I2b4+cyQq5QnE71H2Hd4UsTmK/eQRaJO4LxWw5H1m\nX/k5+6Zn/UfsazAZQNKK4F+k2LJ90Fg6OuMJx6RTN18RLO7eqeecYKZHNxog\n8gR08VTytTCU6cK9kJvSv+kVOyYGZhd2LUo/uGonQcuUSvelc1rrHAbI0BrG\ncs1l71Oce33mTVkbnfZX6ffURLjoIp51OgBm+OqlXNEJnr99OG3GMOQAYJ3V\nv5wDLz0lDR6fxn5Q9humrBoIoFEsfIjQP3NzJpiFNCUo/27GrijzIO2H0mwf\n63nkfvNYlec6IQYCyZ3guQSNk6AMreZO97XZxbre6jIjUZD3yU8DxmX6WacH\nKEfOdDSL0P2zfszCgTexa4QLtvrSMpyMg1ZeKaOg68ZFIrtWnlAfQ3dCNNmb\nv9YT0xEpuJJ57Bi7iH9MSCLBYa+js11oxoIxivOw+fveNCna+6W6u9B916sc\ntN0Dvyrk3lS01I4AONu5zBStKtwo7/PYnuvdAz01GVaPEufZ93XSwGMdiTys\n1HNVu95I2c2B41Z4meKpIINF83r+16zu0i/+Vu9ocaPIcuxbl9GuBAf3NpRf\nK88HwaPBZAWPo035nohMs290iFliwlc300L7OsT5DbGIH4KVP4jivBQkbsGC\nbBzx\r\n=f9A8\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFQCFbOT7azALUZAt4MuARZEQnDTxvylIVSkAB//s9TIAiEAj6httRRoI+sgb6TiH444flv2vP72XuM+Ff4Yt9EkG9M="}]}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/nth-check_1.0.2_1540159926746_0.4517908661077017"}, "_hasShrinkwrap": false}, "2.0.0": {"name": "nth-check", "version": "2.0.0", "description": "Parses and compiles CSS nth-checks to highly optimized functions.", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "sideEffects": false, "funding": {"url": "https://github.com/fb55/nth-check?sponsor=1"}, "directories": {"lib": "lib/"}, "main": "lib/index.js", "types": "lib/index.d.ts", "scripts": {"test": "jest --coverage && npm run lint", "coverage": "cat coverage/lcov.info | coveralls", "lint": "npm run lint:es && npm run lint:prettier", "lint:es": "eslint .", "lint:prettier": "npm run prettier -- --check", "format": "npm run format:es && npm run format:prettier", "format:es": "npm run lint:es -- --fix", "format:prettier": "npm run prettier -- --write", "prettier": "prettier '**/*.{ts,md,json,yml}'", "build": "tsc", "prepare": "npm run build"}, "repository": {"type": "git", "url": "git+https://github.com/fb55/nth-check.git"}, "keywords": ["nth-child", "nth", "css"], "bugs": {"url": "https://github.com/fb55/nth-check/issues"}, "homepage": "https://github.com/fb55/nth-check", "dependencies": {"boolbase": "^1.0.0"}, "devDependencies": {"@types/jest": "^26.0.0", "@types/node": "^14.0.5", "@typescript-eslint/eslint-plugin": "^4.1.0", "@typescript-eslint/parser": "^4.1.0", "eslint": "^7.0.0", "eslint-config-prettier": "^6.0.0", "jest": "^26.0.1", "prettier": "^2.1.1", "ts-jest": "^26.0.0", "typescript": "^4.0.2"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "prettier": {"tabWidth": 4}, "gitHead": "3ba66fc22d68017baa3a6b97dd2475ab246d5a6f", "_id": "nth-check@2.0.0", "_nodeVersion": "15.3.0", "_npmVersion": "7.0.14", "dist": {"integrity": "sha512-i4sc/Kj8htBrAiH1viZ0TgU8Y5XqCaV/FziYK6TBczxmeKm3AEFWqqF3195yKudrarqy7Zu80Ra5dobFjn9X/Q==", "shasum": "1bb4f6dac70072fc313e8c9cd1417b5074c0a125", "tarball": "https://registry.npmjs.org/nth-check/-/nth-check-2.0.0.tgz", "fileCount": 12, "unpackedSize": 12860, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfxsagCRA9TVsSAnZWagAAZxwP+gOT6igLdoNVmR8Xy1La\nQppYTSrHMgROyj2Y0aSlp6kSQc5X7Mt0vYhJG3lXlOpnBFR1Js6aJPBM0uNZ\nwXr0Y2J5m0UHh0ndYjX9T9GB9DvWVaNwRPaKh1wS7PsPCRvVS72CwEvZWcyB\nlnLvfJm8z/lCQjKiWEAppWM+ynnQTpvonoqxxax7dg1bbmt/TZzHbxQlPx8K\nVv9FbtskkWgG9CoYpAFFXGMTDni/krWHdpavGNKFtUFbwjZbKvq4qF1luFcL\npTnu9xr4jarmyoxZeR2yNcZXZdyCdWCcfs1kMG0O1H87LlMrsCQEHFeRBgrT\nm6ov7qY5Ew+q+GbFitP5QOk+YlbrMK/uHzGP+fc9mwYyWaX/mB9rIoZhnl7y\nbyJEB6nE5H1+DKp6JwZG1JHOfltH8gKv0fA7ADNbkhJFBqGbq3rEWYeiICru\nI7lFD816eb50izgJvnHxzyranBKIHc675O+3tO0WazelymJjOuyPW0xfpn1b\nHXaW4SKe4DVoEZHfwAe+fRNS4ub68O/XFLMGOZPGO9RGfLLyvv6PQEnjfNeT\nqInx2l7u1NapIn43/cwhuapTHc+WncAMSGNWzKDiKn6YwCBEZDStNPwXVXDW\nvyHdacy5jDcKHddrOsBqYK0JEuDuEYl5okeqviUsyjOYFNGMElVoYLx5PI+X\nxzb4\r\n=bMtr\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAEHsqOOgqkgUcARCkDh1Z89yeJDmDZISnovuN7a9+lHAiEAjN0Wsza+PZ/GoMwFZ54CNITqzzoBjtfcVOQ3z57+PIc="}]}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/nth-check_2.0.0_1606862495923_0.2794616396314096"}, "_hasShrinkwrap": false}, "2.0.1": {"name": "nth-check", "version": "2.0.1", "description": "Parses and compiles CSS nth-checks to highly optimized functions.", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "sideEffects": false, "funding": {"url": "https://github.com/fb55/nth-check?sponsor=1"}, "directories": {"lib": "lib/"}, "main": "lib/index.js", "types": "lib/index.d.ts", "scripts": {"test": "npm run test:jest && npm run lint", "test:jest": "jest", "lint": "npm run lint:es && npm run lint:prettier", "lint:es": "eslint .", "lint:prettier": "npm run prettier -- --check", "format": "npm run format:es && npm run format:prettier", "format:es": "npm run lint:es -- --fix", "format:prettier": "npm run prettier -- --write", "prettier": "prettier '**/*.{ts,md,json,yml}'", "build": "tsc", "prepare": "npm run build"}, "repository": {"type": "git", "url": "git+https://github.com/fb55/nth-check.git"}, "keywords": ["nth-child", "nth", "css"], "bugs": {"url": "https://github.com/fb55/nth-check/issues"}, "homepage": "https://github.com/fb55/nth-check", "dependencies": {"boolbase": "^1.0.0"}, "devDependencies": {"@types/jest": "^27.0.1", "@types/node": "^16.9.1", "@typescript-eslint/eslint-plugin": "^4.31.1", "@typescript-eslint/parser": "^4.31.1", "eslint": "^7.32.0", "eslint-config-prettier": "^8.3.0", "jest": "^27.2.0", "prettier": "^2.4.1", "ts-jest": "^27.0.5", "typescript": "^4.4.3"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "prettier": {"tabWidth": 4}, "gitHead": "65e40b02b0437daf5d41760352433435ad2370a0", "_id": "nth-check@2.0.1", "_nodeVersion": "16.9.0", "_npmVersion": "7.21.1", "dist": {"integrity": "sha512-it1vE95zF6dTT9lBsYbxvqh0Soy4SPowchj0UBGj/V6cTPnXXtQOPUbhZ6CmGzAD/rW22LQK6E96pcdJXk4A4w==", "shasum": "2efe162f5c3da06a28959fbd3db75dbeea9f0fc2", "tarball": "https://registry.npmjs.org/nth-check/-/nth-check-2.0.1.tgz", "fileCount": 12, "unpackedSize": 13989, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQzHCCRA9TVsSAnZWagAAw3gP/RmzNF0ajgjZWagTJOEp\nnIDWB9jwwftNLOvCSjjuVQ2ZN0WA88WaYnbeXRUonNCg/4pXgHVHiBht2ezs\nJ1gNPAPWgW9YyEKcCUlEGFcnpSFepqz+tLbglNBRzbgoIQ64FeY8VyZDUIfu\nK4BMrVXZNDVb+NQ4KkXGVuttLbsiim4xyzWl5u+p3T6Hu0JPP9aat2yTqCzW\nJxEJdVu+wodVsd/o3Tt4wxATU8KAtvulMBs0xUEexRo2N0tttlwH82tTp10M\nJHj5/6aPi4rQJ83wamiF3YG2cPg1lUECsamdFuuM0XWPgEhrey90RnbmyZ9/\niEaH/Tt0brV2dQRca8yNmXDsvHBTTArrG1cChjP9V2gHAtRUF9+taiB8MUqL\nHA++Hus0FCHAUfA0DXWEqL6G7DE+OEkY1IaUF5G904xWlEHB0Wfq3WPazCmj\npkiI9L3nw467uA4sfz9XAyQE5d/l5UFeEl95cHzPUftp+3OdxJX1qs8Q9/0l\nNz6kj0HyQwTCyovbfSTDGA07R0lSVYjiwr1YbzTMpfN9AaW+pq/waL+YyzDX\nj00b+4E0vGkT9G1Zb8/eL7MoaPNWCoUQ5E7EKcUs31mblMII9d8XwWuxkDEr\nWFxfGUuaNZeMV38FJVzZpBNszyM/w5DFcF1LTbOsvEADIdR/no5uGT+zJaJw\nac9p\r\n=sHqD\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAswPUNUUd7YfP4NAyhKxxS5PSnxdzOvKVee6tOlefA1AiBixRTg+6pLmExiSHaw3+4QF5GM6R6PTXkD0525m+XY1Q=="}]}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/nth-check_2.0.1_1631793602091_0.035415616653284854"}, "_hasShrinkwrap": false}, "2.1.0": {"name": "nth-check", "version": "2.1.0", "description": "Parses and compiles CSS nth-checks to highly optimized functions.", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "sideEffects": false, "funding": {"url": "https://github.com/fb55/nth-check?sponsor=1"}, "directories": {"lib": "lib/"}, "main": "lib/index.js", "types": "lib/index.d.ts", "module": "lib/esm/index.js", "exports": {"require": "./lib/index.js", "import": "./lib/esm/index.js"}, "scripts": {"test": "npm run test:jest && npm run lint", "test:jest": "jest", "lint": "npm run lint:es && npm run lint:prettier", "lint:es": "eslint .", "lint:prettier": "npm run prettier -- --check", "format": "npm run format:es && npm run format:prettier", "format:es": "npm run lint:es -- --fix", "format:prettier": "npm run prettier -- --write", "prettier": "prettier '**/*.{ts,md,json,yml}'", "build": "npm run build:cjs && npm run build:esm", "build:cjs": "tsc --sourceRoot https://raw.githubusercontent.com/fb55/nth-check/$(git rev-parse HEAD)/src/", "build:esm": "npm run build:cjs -- --module esnext --target es2019 --outDir lib/esm && echo '{\"type\":\"module\"}' > lib/esm/package.json", "prepare": "npm run build"}, "repository": {"type": "git", "url": "git+https://github.com/fb55/nth-check.git"}, "keywords": ["nth-child", "nth", "css"], "bugs": {"url": "https://github.com/fb55/nth-check/issues"}, "homepage": "https://github.com/fb55/nth-check", "dependencies": {"boolbase": "^1.0.0"}, "devDependencies": {"@types/boolbase": "^1.0.1", "@types/jest": "^27.5.0", "@types/node": "^17.0.35", "@typescript-eslint/eslint-plugin": "^5.25.0", "@typescript-eslint/parser": "^5.25.0", "eslint": "^8.15.0", "eslint-config-prettier": "^8.5.0", "jest": "^27.5.1", "prettier": "^2.6.2", "ts-jest": "^27.1.4", "typescript": "^4.6.4"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "prettier": {"tabWidth": 4}, "gitHead": "432ebc605de10e2f26d7cc2a1fc3fcb048eea6e6", "_id": "nth-check@2.1.0", "_nodeVersion": "18.2.0", "_npmVersion": "8.9.0", "dist": {"integrity": "sha512-/sJnahgHzXT7OGgQeAPJ5WDcxs7teA2FU1wo6fEoYqjoHBU+AQi3Qz5hecKPlnOr1YjnSYwJTlBXADbEsaBMLg==", "shasum": "0cd91c5c08967b58b409fa76282e13851a33fa47", "tarball": "https://registry.npmjs.org/nth-check/-/nth-check-2.1.0.tgz", "fileCount": 28, "unpackedSize": 42230, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD3tgsTqG4+reBAKl1FyyYV5MuKJOVkwPJsHn8dRsD8BwIgbj3utRSIjKlOmdepfAMTDHg8bPtX5V9/3IFCCWvr/BE="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiirtXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqlSQ//YhIPIOjPa4JzMK84ZAAvTbIogArw7NdncBLozfngPME1ADTz\r\nIyjw8wHodQq+gVvuZ+vnspn2R9st0DOsyPBin//hZFzv1H+0mLG1xQRPFHAg\r\ncchgfT0KgDSL5Ia4Yg2/ZPJrjanc8jJT9GFu+8hYewbu+lMRWEca7OOwtWgn\r\nZdyIl+GgyP0ExGmv5nAfpkzBOzS3DcPp1fSKPgL/UGXy/YejFVdW2NnIMqNL\r\nQ4O/k3kK469Nagx8ytAUHs9g01ln4eUWlUfd4bO9vF0K9GdwMMbyVIPLFYxB\r\nEitr8uTouLlaupm4ydJ5icdJJgpRuSIpYl/vHzE+phrAZUPrvKsRuksMqx0m\r\nyeZepWSvt0ERs0s9d4G6Zr6SZl+UkjLwPw8Hhx4uF4dh+62kQD1j5Cz+aIos\r\njK/oZa/kRIjjlt8do8mCKzCJ+IjNSGu2wdc2DRcmWcKoxV7r8IA10wi3PuEf\r\nSXqEfavp3YPTkosr8M81fWTOb9d3Urtz6eHtAY8i1YmJUR3EN/dWrMpTN4Hj\r\nYxzxqRuyUCzqfOvijwp4wy/MjSWPbUG8OMRtXaw90SqE7KsObLRFbvKqXrrX\r\nfTC0vQsOTaEV1d/bDxcc2H+dm1z4zte16NiWSH7NfUhAKlkHYQh5FKdMRjNW\r\n9bfcQu8PFuVLXT8F2D69QHtHoASUe9oTP/A=\r\n=3wHB\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/nth-check_2.1.0_1653259095092_0.04771503905358787"}, "_hasShrinkwrap": false}, "2.1.1": {"name": "nth-check", "version": "2.1.1", "description": "Parses and compiles CSS nth-checks to highly optimized functions.", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "sideEffects": false, "funding": {"url": "https://github.com/fb55/nth-check?sponsor=1"}, "directories": {"lib": "lib/"}, "main": "lib/index.js", "types": "lib/index.d.ts", "module": "lib/esm/index.js", "exports": {"require": "./lib/index.js", "import": "./lib/esm/index.js"}, "scripts": {"test": "npm run test:jest && npm run lint", "test:jest": "jest", "lint": "npm run lint:es && npm run lint:prettier", "lint:es": "eslint .", "lint:prettier": "npm run prettier -- --check", "format": "npm run format:es && npm run format:prettier", "format:es": "npm run lint:es -- --fix", "format:prettier": "npm run prettier -- --write", "prettier": "prettier '**/*.{ts,md,json,yml}'", "build": "npm run build:cjs && npm run build:esm", "build:cjs": "tsc --sourceRoot https://raw.githubusercontent.com/fb55/nth-check/$(git rev-parse HEAD)/src/", "build:esm": "npm run build:cjs -- --module esnext --target es2019 --outDir lib/esm && echo '{\"type\":\"module\"}' > lib/esm/package.json", "prepare": "npm run build"}, "repository": {"type": "git", "url": "git+https://github.com/fb55/nth-check.git"}, "keywords": ["nth-child", "nth", "css"], "bugs": {"url": "https://github.com/fb55/nth-check/issues"}, "homepage": "https://github.com/fb55/nth-check", "dependencies": {"boolbase": "^1.0.0"}, "devDependencies": {"@types/boolbase": "^1.0.1", "@types/jest": "^27.5.0", "@types/node": "^17.0.35", "@typescript-eslint/eslint-plugin": "^5.25.0", "@typescript-eslint/parser": "^5.25.0", "eslint": "^8.15.0", "eslint-config-prettier": "^8.5.0", "jest": "^27.5.1", "prettier": "^2.6.2", "ts-jest": "^27.1.4", "typescript": "^4.6.4"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "moduleNameMapper": {"^(.*)\\.js$": "$1"}}, "prettier": {"tabWidth": 4}, "gitHead": "639fd2a4000b69f82350aad8c34cb43f77e483ba", "_id": "nth-check@2.1.1", "_nodeVersion": "18.2.0", "_npmVersion": "8.9.0", "dist": {"integrity": "sha512-lqjrjmaOoAnWfMmBPL+XNnynZh2+swxiX3WUE0s4yEHI6m+AwrK2UZOimIRl3X/4QctVqS8AiZjFqyOGrMXb/w==", "shasum": "c9eab428effce36cd6b92c924bdb000ef1f1ed1d", "tarball": "https://registry.npmjs.org/nth-check/-/nth-check-2.1.1.tgz", "fileCount": 28, "unpackedSize": 42555, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCgEG3CKXD4W+pySNOYyEw4yRPVxPBXovTGjuOqUek4PQIgCgLRFc4OkwerLuBhDSRKyUFu6qBmADW3hWazqk5CmUE="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJii1xhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoYbBAAlQPss1lUlBFVnOSCen5j5ivrsvXxYvdWD4rFBYKcW4dSxHtm\r\n8+bir9MNeaIT/uQJwlVPn6i43r+YlmDgxN/HyEAZTxnEOvutBXAmDfB8oyhl\r\nOTeX3maRCBhcuGb3HgO0ZRIlOxcMlW8em9YgpB6KSYWgoRN/q+hrpQxoIAPf\r\nzmjORT00onl+IzPVDRCNkLts78BT/4aPewbinW/eG0ec6mkfkz2MpZJM4t64\r\n80KtKtmVnxcMqt82ux6RTQ5gzxIVTycpqgDBXol8PK1/oaqHZIw41LPq4ocb\r\nxlmgeIelaJV5pMie4O2aevoW7pkJmH4tWFqhoIuGQYINQ77sSlWc/c9ercde\r\ndTlWd7M5x43fCfDYwBfKSDz6RHwWbaDv9+Bj/Gnx42J6mvx+tw5xAvr9FFY7\r\npKCJY6fWuptJ7n3XW+UhH/nb81MJhacyLFr89FEZquSBBcarKq3E+TnaI4Hy\r\nch9IBmnvslneilzwAvuErtTVhrt/mo8ZAlXzfUoNWOsaehsdjz5a6JJA3yGh\r\nlj+dZzeUpIEzvDg/wGcKfB6WecV5LBELdsAYYw1N1cLwzLcaT+4cPKXJtMza\r\nUuhq3doE+ZQfP8uere1iyZ4zrJf5BDzv0/kPvKRwVt/ogWy49YIrR20og6TC\r\nBuInyaslj2MFktfRZLjBKXOx3gcAaL5PGyc=\r\n=jrL1\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/nth-check_2.1.1_1653300320889_0.9414942401014395"}, "_hasShrinkwrap": false}}, "readme": "# nth-check [![Build Status](https://travis-ci.org/fb55/nth-check.svg)](https://travis-ci.org/fb55/nth-check)\n\nParses and compiles CSS nth-checks to highly optimized functions.\n\n### About\n\nThis module can be used to parse & compile nth-checks, as they are found in CSS 3's `nth-child()` and `nth-last-of-type()`. It can be used to check if a given index matches a given nth-rule, or to generate a sequence of indices matching a given nth-rule.\n\n`nth-check` focusses on speed, providing optimized functions for different kinds of nth-child formulas, while still following the [spec](http://www.w3.org/TR/css3-selectors/#nth-child-pseudo).\n\n### API\n\n```js\nimport nthCheck, { parse, compile } from \"nth-check\";\n```\n\n##### `nthCheck(formula)`\n\nParses and compiles a formula to a highly optimized function. Combination of `parse` and `compile`.\n\nIf the formula doesn't match any elements, it returns [`boolbase`](https://github.com/fb55/boolbase)'s `falseFunc`. Otherwise, a function accepting an _index_ is returned, which returns whether or not the passed _index_ matches the formula.\n\n**Note**: The nth-rule starts counting at `1`, the returned function at `0`.\n\n**Example:**\n\n```js\nconst check = nthCheck(\"2n+3\");\n\ncheck(0); // `false`\ncheck(1); // `false`\ncheck(2); // `true`\ncheck(3); // `false`\ncheck(4); // `true`\ncheck(5); // `false`\ncheck(6); // `true`\n```\n\n##### `parse(formula)`\n\nParses the expression, throws an `Error` if it fails. Otherwise, returns an array containing the integer step size and the integer offset of the nth rule.\n\n**Example:**\n\n```js\nparse(\"2n+3\"); // [2, 3]\n```\n\n##### `compile([a, b])`\n\nTakes an array with two elements (as returned by `.parse`) and returns a highly optimized function.\n\n**Example:**\n\n```js\nconst check = compile([2, 3]);\n\ncheck(0); // `false`\ncheck(1); // `false`\ncheck(2); // `true`\ncheck(3); // `false`\ncheck(4); // `true`\ncheck(5); // `false`\ncheck(6); // `true`\n```\n\n##### `generate([a, b])`\n\nReturns a function that produces a monotonously increasing sequence of indices.\n\nIf the sequence has an end, the returned function will return `null` after the last index in the sequence.\n\n**Example:** An always increasing sequence\n\n```js\nconst gen = nthCheck.generate([2, 3]);\n\ngen(); // `1`\ngen(); // `3`\ngen(); // `5`\ngen(); // `8`\ngen(); // `11`\n```\n\n**Example:** With an end value\n\n```js\nconst gen = nthCheck.generate([-2, 5]);\n\ngen(); // 0\ngen(); // 2\ngen(); // 4\ngen(); // null\n```\n\n##### `sequence(formula)`\n\nParses and compiles a formula to a generator that produces a sequence of indices. Combination of `parse` and `generate`.\n\n**Example:** An always increasing sequence\n\n```js\nconst gen = nthCheck.sequence(\"2n+3\");\n\ngen(); // `1`\ngen(); // `3`\ngen(); // `5`\ngen(); // `8`\ngen(); // `11`\n```\n\n**Example:** With an end value\n\n```js\nconst gen = nthCheck.sequence(\"-2n+5\");\n\ngen(); // 0\ngen(); // 2\ngen(); // 4\ngen(); // null\n```\n\n---\n\nLicense: BSD-2-Clause\n\n## Security contact information\n\nTo report a security vulnerability, please use the [Tidelift security contact](https://tidelift.com/security).\nTidelift will coordinate the fix and disclosure.\n\n## `nth-check` for enterprise\n\nAvailable as part of the Tidelift Subscription\n\nThe maintainers of `nth-check` and thousands of other packages are working with Tidelift to deliver commercial support and maintenance for the open source dependencies you use to build your applications. Save time, reduce risk, and improve code health, while paying the maintainers of the exact dependencies you use. [Learn more.](https://tidelift.com/subscription/pkg/npm-nth-check?utm_source=npm-nth-check&utm_medium=referral&utm_campaign=enterprise&utm_term=repo)\n", "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "time": {"modified": "2022-06-22T12:12:29.796Z", "created": "2014-02-15T15:06:22.622Z", "1.0.0": "2014-02-15T15:06:22.622Z", "1.0.1": "2015-03-12T12:59:56.470Z", "1.0.2": "2018-10-21T22:12:06.854Z", "2.0.0": "2020-12-01T22:41:36.046Z", "2.0.1": "2021-09-16T12:00:02.342Z", "2.1.0": "2022-05-22T22:38:15.274Z", "2.1.1": "2022-05-23T10:05:21.450Z"}, "readmeFilename": "README.md", "homepage": "https://github.com/fb55/nth-check", "keywords": ["nth-child", "nth", "css"], "repository": {"type": "git", "url": "git+https://github.com/fb55/nth-check.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/fb55/nth-check/issues"}, "license": "BSD-2-<PERSON><PERSON>", "users": {"mojaray2k": true}}